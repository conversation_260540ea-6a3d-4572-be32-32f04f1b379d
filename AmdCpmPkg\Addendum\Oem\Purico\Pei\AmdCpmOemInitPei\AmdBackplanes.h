/*****************************************************************************
 * Copyright (C) 2015-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * AMD CPM OEM API, and related functions.
 *
 * Contains the definitions for AMD backplanes.
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      CPM
 * @e sub-project:  OEM
 * @e \$Revision: 270275 $   @e \$Date: 2013-08-09 03:54:44 +0800 (Fri, 09 Aug 2013) $
 *
 */

#ifndef _AMD_BACKPLANES_H_
#define _AMD_BACKPLANES_H_


DXIO_PORT_DESCRIPTOR    PuricoUBMFullNVME[] = {
  { // UBM backplane HFC 0 - P0 <11-8>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 8, 11, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 8),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x10),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 1 - P0 <15-12>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 12, 15, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 12),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x10),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 2 - P0 <3-0>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 0, 3, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 0),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x10),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 3 - P0 <7-4>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 4, 7, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 4),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x10),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 4 - P1 <11-8>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 40, 43, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 40),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x10),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 5 - P1 <15-12>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 44, 47, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 44),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x10),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 6 - P1 <3-0>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 32, 35, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 32),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x10),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 7 - P1 <7-4>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 36, 39, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 36),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x10),
      PORT_PARAMS_END
    },
    ////
    { // UBM backplane HFC 8 - G1 <3-0>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 64, 67, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 64),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x18),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 9 - G1 <7-4>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 68, 71, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 68),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x18),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 10 - G1 <11-8>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 72, 75, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 72),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x18),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 11 - G1 <15-12>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 76, 79, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 76),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x18),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 12 - G0  <3-0>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 96, 99, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 96),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x18),
      PORT_PARAMS_END
    },
   { // UBM backplane HFC 13 - G0 <7-4>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 100, 103, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 100),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x18),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 14 - G0 <11-8>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 104, 107, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 104),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x18),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 15 - G0 <15-12>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 108, 111, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 108),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x18),
      PORT_PARAMS_END
    },
///
  { // UBM backplane HFC 16 - G2 <3-0>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 112, 115, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 112),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    },
   { // UBM backplane HFC 17 - G2 <7-4>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 116, 119, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 116),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    },
   { // UBM backplane HFC 18 - G2 <11-8>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 120, 123, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 120),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    },
   { // UBM backplane HFC 19 - G2 <15-12>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 124, 127, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 124),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    },
   { // UBM backplane HFC 20 - G3 <3-0>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 80, 83, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
       PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 80),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    },
   { // UBM backplane HFC 21 - G3 <7-4>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 84, 87, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
       PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 84),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    },
   { // UBM backplane HFC 22 - G3 <11-8>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 88, 91, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
       PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 88),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    },
   { // UBM backplane HFC 23 - G3 <15-12>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 92, 95, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
       PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 92),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    }
};

ADDIN_CARD_PORTS PuricoUBMFullNVMEEntry = {
    0xFF,
    24,
    &PuricoUBMFullNVME[0]
};

DXIO_PORT_DESCRIPTOR    PuricoUBMStandard[] = {
  { // UBM backplane HFC 24 - P0 <11-8>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 8, 11, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_START_LANE, 8),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x10),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 25 - P0 <15-8>
   0,
   DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 12, 15, 1),
   DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
   PORT_PARAMS_START
     PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
     PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
     PORT_PARAM (PP_START_LANE, 12),
     PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
     PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
     PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
     PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
     PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
     PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
     PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
     PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
     PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
     PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
     PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
     PORT_PARAM (PP_SLOT_NUM, 0x10),
     PORT_PARAMS_END
  },
  { // UBM backplane HFC 8 - G1 <3-0>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 64, 67, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 64),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 9 - G1 <7-4>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 68, 71, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 68),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 10 - G1 <11-8>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 72, 75, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 72),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 11 - G1 <15-12>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 76, 79, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 76),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 12 - G0  <3-0>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 96, 99, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 96),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 13 - G0 <7-4>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 100, 103, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 100),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 14 - G0 <11-8>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 104, 107, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 104),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 15 - G0 <15-12>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 108, 111, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 108),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 16 - G2 <3-0>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 112, 115, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 112),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 17 - G2 <7-4>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 116, 119, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 116),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 18 - G2 <11-8>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 120, 123, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 120),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 19 - G2 <15-12>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 124, 127, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 124),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 20 - G3 <3-0>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 80, 83, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
     PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 80),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 21 - G3 <7-4>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 84, 87, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
     PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 84),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 22 - G3 <11-8>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 88, 91, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
     PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 88),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 23 - G3 <15-12>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 92, 95, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
     PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 92),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  }
};

ADDIN_CARD_PORTS PuricoUBMStandardEntry = {
    0xFF,
    18,
    &PuricoUBMStandard[0]
};

DXIO_PORT_DESCRIPTOR    PuricoHbaStandard[] = {
  { // UBM backplane HFC 8 - G1 <3-0>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 64, 67, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 64),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 9 - G1 <7-4>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 68, 71, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 68),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 10 - G1 <11-8>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 72, 75, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 72),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 11 - G1 <15-12>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 76, 79, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 76),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 12 - G0  <3-0>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 96, 99, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 96),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 13 - G0 <7-4>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 100, 103, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 100),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 14 - G0 <11-8>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 104, 107, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 104),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 15 - G0 <15-12>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 108, 111, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 108),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 16 - G2 <3-0>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 112, 115, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 112),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 17 - G2 <7-4>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 116, 119, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 116),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 18 - G2 <11-8>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 120, 123, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 120),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 19 - G2 <15-12>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 124, 127, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 124),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 20 - G3 <3-0>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 80, 83, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
     PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 80),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 21 - G3 <7-4>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 84, 87, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
     PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 84),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 22 - G3 <11-8>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 88, 91, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
     PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 88),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 23 - G3 <15-12>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 92, 95, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
     PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 92),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  }
};

ADDIN_CARD_PORTS PuricoHbaStandardEntry = {
    0xFF,
    16,
    &PuricoHbaStandard[0]
};

DXIO_PORT_DESCRIPTOR    PuricoUBMFullSATA[] = {
  { // UBM backplane HFC 24 - P0 <11-8>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 8, 11, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_START_LANE, 8),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x10),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 25 - P0 <15-12>
   0,
   DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 12, 15, 1),
   DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
   PORT_PARAMS_START
     PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
     PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
     PORT_PARAM (PP_START_LANE, 12),
     PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
     PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
     PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
     PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
     PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
     PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
     PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
     PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
     PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
     PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
     PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
     PORT_PARAM (PP_SLOT_NUM, 0x10),
     PORT_PARAMS_END
  },
  { // UBM backplane HFC 26 - G3 <11-8>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 88, 91, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_START_LANE, 88),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 27 - G3 <15-12>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 92, 95, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_START_LANE, 92),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 28 - G3 <3-0>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 80, 83, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_START_LANE, 80),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 29 - G3 <7-4>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 84, 87, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_START_LANE, 84),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  }
};

ADDIN_CARD_PORTS PuricoUBMFullSATAEntry = {
    0xFF,
    6,
    &PuricoUBMFullSATA[0]
};

DXIO_PORT_DESCRIPTOR    PuricoSgpioFullSATA[] = {
  { // P0 - SATA ports
    0,
    DXIO_ENGINE_DATA_INITIALIZER (DxioSATAEngine, 8, 15, DxioHotplugDisabled, 1),
    DXIO_PORT_DATA_INITIALIZER_SATA (
      DxioPortEnabled                       // Port Present
    )
    PHY_PARAMS_START
    PHY_PARAM (GEN3_txX_eq_main, 0x0),
    PHY_PARAM (GEN3_txX_eq_post, 0xE),
    PHY_PARAM (GEN3_txX_eq_pre,  0x0),
    PHY_PARAM (GEN3_txX_pre_deemphasis,  1),
    PHY_PARAM (GEN3_txX_post_deemphasis, 1),
    PHY_PARAM (GEN2_txX_eq_main, 0x4),
    PHY_PARAM (GEN2_txX_eq_post, 0x0),
    PHY_PARAM (GEN2_txX_eq_pre,  0x0),
    PHY_PARAM (GEN2_txX_pre_deemphasis,  1),
    PHY_PARAM (GEN2_txX_post_deemphasis, 1),
    PHY_PARAM (GEN1_txX_eq_main, 0xA),
    PHY_PARAM (GEN1_txX_eq_post, 0x0),
    PHY_PARAM (GEN1_txX_eq_pre,  0x0),
    PHY_PARAM (GEN1_txX_pre_deemphasis,  1),
    PHY_PARAM (GEN1_txX_post_deemphasis, 1)
    PHY_PARAMS_END
  },
  { // G3 - SATA ports
    0,
    DXIO_ENGINE_DATA_INITIALIZER (DxioSATAEngine, 80, 87, DxioHotplugDisabled, 1),
    DXIO_PORT_DATA_INITIALIZER_SATA (
      DxioPortEnabled                       // Port Present
    )
    PHY_PARAMS_START
    PHY_PARAM (GEN3_txX_eq_main, 0x0),
    PHY_PARAM (GEN3_txX_eq_post, 0xE),
    PHY_PARAM (GEN3_txX_eq_pre,  0x0),
    PHY_PARAM (GEN3_txX_pre_deemphasis,  1),
    PHY_PARAM (GEN3_txX_post_deemphasis, 1),
    PHY_PARAM (GEN2_txX_eq_main, 0x4),
    PHY_PARAM (GEN2_txX_eq_post, 0x0),
    PHY_PARAM (GEN2_txX_eq_pre,  0x0),
    PHY_PARAM (GEN2_txX_pre_deemphasis,  1),
    PHY_PARAM (GEN2_txX_post_deemphasis, 1),
    PHY_PARAM (GEN1_txX_eq_main, 0xA),
    PHY_PARAM (GEN1_txX_eq_post, 0x0),
    PHY_PARAM (GEN1_txX_eq_pre,  0x0),
    PHY_PARAM (GEN1_txX_pre_deemphasis,  1),
    PHY_PARAM (GEN1_txX_post_deemphasis, 1)
    PHY_PARAMS_END
  },
  { // G3 - SATA ports
    0,
    DXIO_ENGINE_DATA_INITIALIZER (DxioSATAEngine, 88, 95, DxioHotplugDisabled, 1),
    DXIO_PORT_DATA_INITIALIZER_SATA (
      DxioPortEnabled                       // Port Present
    )
    PHY_PARAMS_START
    PHY_PARAM (GEN3_txX_eq_main, 0x0),
    PHY_PARAM (GEN3_txX_eq_post, 0xE),
    PHY_PARAM (GEN3_txX_eq_pre,  0x0),
    PHY_PARAM (GEN3_txX_pre_deemphasis,  1),
    PHY_PARAM (GEN3_txX_post_deemphasis, 1),
    PHY_PARAM (GEN2_txX_eq_main, 0x4),
    PHY_PARAM (GEN2_txX_eq_post, 0x0),
    PHY_PARAM (GEN2_txX_eq_pre,  0x0),
    PHY_PARAM (GEN2_txX_pre_deemphasis,  1),
    PHY_PARAM (GEN2_txX_post_deemphasis, 1),
    PHY_PARAM (GEN1_txX_eq_main, 0xA),
    PHY_PARAM (GEN1_txX_eq_post, 0x0),
    PHY_PARAM (GEN1_txX_eq_pre,  0x0),
    PHY_PARAM (GEN1_txX_pre_deemphasis,  1),
    PHY_PARAM (GEN1_txX_post_deemphasis, 1)
    PHY_PARAMS_END
  }
};

ADDIN_CARD_PORTS PuricoSgpioFullSATAEntry = {
    0xFF,
    3,
    &PuricoSgpioFullSATA[0]
};

DXIO_PORT_DESCRIPTOR    PuricoSgpioStandard[] = {
  { // P0 - SATA ports
    0,
    DXIO_ENGINE_DATA_INITIALIZER (DxioSATAEngine, 8, 15, DxioHotplugDisabled, 1),
    DXIO_PORT_DATA_INITIALIZER_SATA (
      DxioPortEnabled                       // Port Present
    )
    PHY_PARAMS_START
    PHY_PARAM (GEN3_txX_eq_main, 0x0),
    PHY_PARAM (GEN3_txX_eq_post, 0xE),
    PHY_PARAM (GEN3_txX_eq_pre,  0x0),
    PHY_PARAM (GEN3_txX_pre_deemphasis,  1),
    PHY_PARAM (GEN3_txX_post_deemphasis, 1),
    PHY_PARAM (GEN2_txX_eq_main, 0x4),
    PHY_PARAM (GEN2_txX_eq_post, 0x0),
    PHY_PARAM (GEN2_txX_eq_pre,  0x0),
    PHY_PARAM (GEN2_txX_pre_deemphasis,  1),
    PHY_PARAM (GEN2_txX_post_deemphasis, 1),
    PHY_PARAM (GEN1_txX_eq_main, 0xA),
    PHY_PARAM (GEN1_txX_eq_post, 0x0),
    PHY_PARAM (GEN1_txX_eq_pre,  0x0),
    PHY_PARAM (GEN1_txX_pre_deemphasis,  1),
    PHY_PARAM (GEN1_txX_post_deemphasis, 1)
    PHY_PARAMS_END
  },
  { // UBM backplane HFC 8 - G1 <3-0>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 64, 67, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 64),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 9 - G1 <7-4>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 68, 71, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 68),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 10 - G1 <11-8>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 72, 75, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 72),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 11 - G1 <15-12>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 76, 79, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 76),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 12 - G0  <3-0>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 96, 99, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 96),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 13 - G0 <7-4>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 100, 103, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 100),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 14 - G0 <11-8>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 104, 107, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 104),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 15 - G0 <15-12>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 108, 111, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 108),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x18),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 16 - G2 <3-0>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 112, 115, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 112),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 17 - G2 <7-4>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 116, 119, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 116),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 18 - G2 <11-8>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 120, 123, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 120),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 19 - G2 <15-12>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 124, 127, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 124),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 20 - G3 <3-0>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 80, 83, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
     PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 80),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 21 - G3 <7-4>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 84, 87, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
     PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 84),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 22 - G3 <11-8>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 88, 91, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
     PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 88),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  },
  { // UBM backplane HFC 23 - G3 <15-12>
    0,
    DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 92, 95, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
     PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
      PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
      PORT_PARAM (PP_NPEM_ENABLE, 0x1),
      PORT_PARAM (PP_START_LANE, 92),
      PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
      PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
      PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
      PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
      PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
      PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
      PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
      PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
      PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
      PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAM (PP_SLOT_NUM, 0x20),
    PORT_PARAMS_END
  }
};

ADDIN_CARD_PORTS PuricoSgpioStandardEntry = {
    0xFF,
    17,
    &PuricoSgpioStandard[0]
};

#endif

