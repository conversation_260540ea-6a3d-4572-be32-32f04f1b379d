#;*****************************************************************************
#;
#; Copyright (C) 2015-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[Defines]
  DEC_SPECIFICATION     = 0x00010005
  PACKAGE_NAME          = AgesaPkg
  PACKAGE_GUID          = DABFEFC6-5A79-444b-862A-1F6FE9F561FE
  PACKAGE_VERSION       = 0.1

[Includes]
  Include
  Addendum/Apcb/Inc

[LibraryClasses]
  OemAgesaCcxPlatformLib|Include/Library/OemAgesaCcxPlatformLib.h
  AmdHeapLib|Include/Library/AmdHeapLib.h
  OemPcieResetControlLib|AgesaPkg/Include/Library/OemPcieResetControlLib.h
  OemGpioResetControlLib|AgesaPkg/Include/Library/OemGpioResetControlLib.h
  OemClkReqControlLib|AgesaPkg/Include/Library/OemClkReqControlLib.h
  FabricResourceManagerLib|Include/Library/FabricResourceManagerLib.h
  AmlGenerationLib|Include/Library/AmlGenerationLib.h
  ApcbTokenWhiteListLib|Include/Library/ApcbTokenWhiteListLib.h # AMI PORTING - Added ApcbTokenWhiteListLib under Library Classes section

  #PSP Libs
  AmdPspCommonLib|Include/Library/AmdPspCommonLib.h
  AmdFtpmLib|Include/Library/AmdFtpmLib.h
  AmdPspApobLib|Include/Library/AmdPspApobLib.h
  ApcbCoreLib|Include/Library/ApcbCoreLib.h
  ApcbLib|Include/Library/ApcbLib.h
  ApcbLibV3Pei|Include/Library/ApcbLibV3Pei.h
  ApcbLibV3|Include/Library/ApcbLibV3.h
  ApobCommonServiceLib|Include/Library/ApobCommonServiceLib.h
  AmdStbLib|Include/Library/AmdStbLib.h

[Guids]
  gEfiAmdAgesaPkgTokenSpaceGuid          = { 0xd4d8435f, 0xfffb, 0x4acb, { 0xa0, 0x4d, 0xff, 0x0f, 0xad, 0x67, 0x7f, 0xe9 } }
  gAmdMemoryInfoHobGuid                  = { 0x1bce3d14, 0xa5fe, 0x4a0b, { 0x9a, 0x8d, 0x69, 0xca, 0x5d, 0x98, 0x38, 0xd3 } }
  gAmdHeapHobGuid                        = { 0xd97d161a, 0x16cd, 0x4ada, { 0xb9, 0xf6, 0xae, 0xc3, 0xf9, 0xfc, 0xcc, 0x2c } }
  gAmdCapsuleStatusHobGuid               = { 0x3a8e17f2, 0x7895, 0x4fb6, { 0xa8, 0x6c, 0xde, 0x82, 0xd3, 0x65, 0x63, 0x1e } }
  gAmdMemoryMbistHobGuid                 = { 0xfdd95c81, 0xea58, 0x4b51, { 0x99, 0x74, 0x82, 0xc1, 0x4a, 0x36, 0x55, 0xc7 } }
  gEfiTpmDeviceInstanceTpm20AmdFtpmGuid  = { 0x765be6b2, 0x02db, 0x40ea, { 0x94, 0xa8, 0xfa, 0xc0, 0x9f, 0x83, 0x3f, 0x08 } }
  gAmdPspMmioBaseHobGuid                 = { 0xfabe7090, 0x71cc, 0x4266, { 0x9b, 0x4f, 0x33, 0x0c, 0x0b, 0x32, 0x94, 0xf6 } } #Not published by all PSP versions.
  gAmdPerfLibGuid                        = { 0x00B84321, 0x0053, 0x4114, { 0x00, 0xD8, 0x87, 0xE1, 0x00, 0x62, 0x61, 0x78 } }

[Protocols]
  gAmdHeapHeaderProtocolGuid             = { 0xaf62673d, 0xdeda, 0x43ae, { 0x8f, 0x3, 0x9c, 0x2d, 0x89, 0xfd, 0x78, 0xda } }

  ## Error Log Protocols
  gAmdErrorLogServiceProtocolGuid        = {0x7ef3f75c, 0xae1f, 0x46fc, {0x87, 0x86, 0xe, 0xb4, 0xf2, 0x81, 0xb2, 0x3d}}
  gAmdErrorLogFullProtocolGuid           = {0x38c2bd90, 0x7bac, 0x47f2, {0xb7, 0x25, 0x4, 0x9, 0xad, 0x7b, 0x8d, 0xe8}}
  gAmdErrorLogAvailableProtocolGuid      = {0x8444f699, 0xdb97, 0x4f27, {0xb1, 0x1, 0x3b, 0x7b, 0x88, 0x9a, 0xb9, 0xd8}}
  gAmdCxlErrorLogProtocolGuid            = {0x8E125B30, 0x4CC4, 0xD7AA, {0x6E, 0xD0, 0x5A, 0x82, 0xDF, 0x6B, 0xFD, 0xE2}}
  #PSP Protocols
  gPspPlatformProtocolGuid               = {0xccf14a29, 0x37e0, 0x48ad, { 0x90, 0x5, 0x1f, 0x89, 0x62, 0x2f, 0xb7, 0x98 }}
  ## Fch Protocols
  gFchInitProtocolGuid                   = { 0xdb4a79ac, 0x5bbb, 0x4625, {0xa6, 0x9e, 0xfe, 0xbf, 0x9d, 0x6d, 0x95, 0xeb } }
  gFchInitDonePolicyProtocolGuid         = { 0xc63c0c73, 0xf612, 0x4c02, {0x84, 0xa3, 0xc6, 0x40, 0xad, 0xb, 0xa6, 0x22 } }
  gFchMultiFchInitProtocolGuid           = { 0xbb6afbf4, 0x1b0d, 0x483d, {0x96, 0x87, 0xee, 0x02, 0x3c, 0x1b, 0x54, 0x55 } }
  gFchControlServiceProtocolGuid         = { 0xae8fa023, 0xa0b6, 0x4ebc, {0xa3, 0xc7, 0x3d, 0xf3, 0xa6, 0xa8, 0x94, 0xe9} }
  gFchSmmGpiDispatch2ProtocolGuid        = { 0x7051ab6d, 0x9ec2, 0x42eb, { 0xa2, 0x13, 0xde, 0x48, 0x81, 0xf1, 0xf7, 0x87 } }
  gFchSmmIoTrapDispatch2ProtocolGuid     = { 0x91288fc4, 0xe64b, 0x4ef9, { 0xa4, 0x63, 0x66, 0x88, 0x0, 0x71, 0x7f, 0xca } }
  gFchSmmPeriodicalDispatch2ProtocolGuid = { 0x736102f1, 0x9584, 0x44e7, { 0x82, 0x8a, 0x43, 0x4b, 0x1e, 0x67, 0x5c, 0xc4 } }
  gFchSmmPwrBtnDispatch2ProtocolGuid     = { 0xa365240e, 0x56b0, 0x426d, { 0x83, 0xa, 0x30, 0x66, 0xc6, 0x81, 0xbe, 0x9a } }
  gFchSmmSwDispatch2ProtocolGuid         = { 0x881b4ab6, 0x17b0, 0x4bdf, { 0x88, 0xe2, 0xd4, 0x29, 0xda, 0x42, 0x5f, 0xfd } }
  gFchSmmSxDispatch2ProtocolGuid         = { 0x87e2a6cf, 0x91fb, 0x4581, { 0x90, 0xa9, 0x6f, 0x50, 0x5d, 0xdc, 0x1c, 0xb2 } }
  gFchSmmUsbDispatch2ProtocolGuid        = { 0xfbbb2ea9, 0xce0e, 0x4689, { 0xb3, 0xf0, 0xc6, 0xb8, 0xf0, 0x76, 0xbd, 0x20 } }
  gFchSmmMiscDispatchProtocolGuid        = { 0x13bd659b, 0xb4c6, 0x47da, {0x9b, 0x22, 0x11, 0x50, 0xd4, 0xf3, 0xb, 0xda } }
  gFchSmmApuRasDispatchProtocolGuid      = { 0xf871ee59, 0x29d2, 0x4b15, {0x9e, 0x67, 0xaf, 0x32, 0xcd, 0xc1, 0x41, 0x73 } }
  gFchSmmUsbDispatchProtocolGuid         = { 0x59053b0d, 0xeeb8, 0x4379, {0xb1, 0xc8, 0x14, 0x5f, 0x1b, 0xb, 0xe4, 0xb9 } }
  ## Nbio Protocols
  gAmdHotplugDescProtocolGuid            = {0xe8d7e476, 0xedab, 0x4a80, {0x91, 0x19, 0xea, 0x5b, 0xcc, 0xc4, 0xc1, 0x95}}
  gAmdNbioPcieAerProtocolGuid            = { 0xe48c773, 0x4445, 0x40d5, {0x9f, 0x11, 0x5f, 0x25, 0x6d, 0x19, 0xc1, 0x7b}}
  gAmdNbioIommuProtocolGuid              = { 0x30dc4b0e, 0xcd83, 0x4b85, { 0xbb, 0x11, 0x19, 0xa8, 0xa4, 0xbc, 0xe5, 0x62 } }
  gAmdNbioIommuDmarProtocolGuid          = { 0x6c422593, 0xd5c3, 0x426b, { 0xb8, 0x9, 0xca, 0x13, 0x5d, 0xaa, 0xc0, 0x91 } }
  gAmdNbioServicesProtocolGuid           = { 0xC4387568, 0x5AEF, 0x4E4D, { 0x86, 0x70, 0x48, 0x2F, 0x22, 0x1E, 0x7C, 0xD7 } }
  gAmdNbioCxlServicesProtocolGuid        = {0x125CCFCE, 0x34AF, 0x422C, {0xB3, 0xB3, 0x71, 0x23, 0xA3, 0x1E, 0xC8, 0x61}}
  gAmdPciResourceProtocolGuid            = {0x663d4897, 0xed94, 0x4f0f, {0x86, 0x64, 0xfe, 0xdc, 0x70, 0x30, 0x7f, 0x19}}
  ## CPM AND NBIO
  gAmdAerCpmNbioComProtocolGuid          = { 0x415447de, 0x6b48, 0x4a1d, { 0xa9, 0x77, 0xcd, 0x1d, 0x57, 0xd6, 0x4c, 0x79 } }

  #Universal
  gAmdSmbiosDxeInitCompleteProtocolGuid  = {0x607614f9, 0x3f1c, 0x45dd, {0x9c, 0x78, 0x1, 0x72, 0x3b, 0xc3, 0x85, 0xd9}}
  gAmdAcpiDxeInitCompleteProtocolGuid    = {0x2148aa15, 0x700c, 0x4f75, {0x87, 0xaf, 0x10, 0xa4, 0xdd, 0xbb, 0xb9, 0xe1}}
  gAmdCapsuleSmmHookProtocolGuid = { 0x4fc43bbe, 0x1433, 0x4951, { 0xac, 0x2d, 0xd, 0x1, 0xfe, 0xc0, 0xe, 0xb1 } }

  # Ccx
  gAmdMpServicesPreReqProtocolGuid       = {0x33f4458, 0x9c78, 0x42be, { 0x8e, 0x3d, 0x7b, 0xc6, 0x34, 0xb9, 0x5d, 0xe }}

  # DF
  gAmdFabricResourceManagerServicesProtocolGuid = {0xaf96f126, 0x64b6, 0x43dc, {0x9e, 0x6d, 0x76, 0x3f, 0xf1, 0x9b, 0xf3, 0x96}}
  gAmdFabricSocSpecificServicesProtocolGuid     = {0xc99f5067, 0xf8a1, 0x45c4, {0x9f, 0x95, 0x43, 0x35, 0x67, 0xe3, 0xef, 0xa7}}

  # RAS
  gAmdRasApeiProtocolGuid                = {0xe9dbcc60, 0x8f93, 0x47ed, {0x84, 0x78, 0x46, 0x78, 0xf1, 0x9f, 0x73, 0x4a}}
  gAmdRasSmmProtocolGuid                 = {0x4E41A9E3, 0x46AB, 0x1549, {0x06, 0x44, 0xB6, 0xA5, 0x55, 0x29, 0x89, 0x77}}
  gAmdRasApei2ProtocolGuid               = {0xD8ABD054, 0x468F, 0x503C, {0xD3, 0xA7, 0xE5, 0x80, 0xD5, 0x6B, 0x29, 0xB9}}
  gAmdRasSmm2ProtocolGuid                = {0x0CA49B20, 0x4EEF, 0xBFCB, {0x27, 0xE6, 0xCB, 0x90, 0x33, 0xEE, 0xD4, 0x29}}
  gAmdRasApeiGnProtocolGuid              = {0xC8ABD054, 0x468F, 0x503C, {0xD3, 0xA7, 0xE5, 0x80, 0xD5, 0x6B, 0x29, 0xB9}}
  gAmdRasSmmGnProtocolGuid               = {0x1CA49B20, 0x4EEF, 0xBFCB, {0x27, 0xE6, 0xCB, 0x90, 0x33, 0xEE, 0xD4, 0x29}}

  gAmdRasServiceDxeProtocolGuid          = {0x3C3CE2B9, 0x787C, 0x4898, {0xAC, 0x7C, 0x79, 0x1B, 0xA4, 0xF8, 0xF9, 0x5C}}
  gAmdRasServiceSmmProtocolGuid          = {0x39095317, 0xAECE, 0x4FC5, {0x9B, 0x2A, 0x79, 0x97, 0xD3, 0x45, 0x09, 0x48}}
  # AGESA Version String Protocol
  gAmdVersionStringProtocolGuid          = {0x144b3f95, 0x35, 0x4918, {0x97, 0xd2, 0xfa, 0xfd, 0xcd, 0x74, 0x74, 0x15 }}

  # SecureBio
  gAmdSecureBioXhciInfoProtocolGuid      = { 0xf59628ce, 0xa2a3, 0x4c96, { 0xab, 0x8, 0x92, 0x99, 0xb3, 0x18, 0x1e, 0xe5 }}

  # DPE
  gAmdPspDpeProtocolGuid                 = {0x89b4c32c, 0x9c90, 0x4ea5, { 0x81, 0x24, 0x8f, 0xaa, 0xc3, 0x33, 0x88, 0xc5}}

[Ppis]
  gAmdMemoryInfoHobPpiGuid               = { 0xba16e587, 0x1d66, 0x41b7, { 0x9b, 0x52, 0xca, 0x4f, 0x2c, 0xad, 0xd, 0xc8 } }

  ## Error Log Ppis
  gAmdErrorLogServicePpiGuid             = {0x7cdf73a2, 0x51a9, 0x4c0b, {0xaa, 0x68, 0x3c, 0x46, 0x91, 0x75, 0x76, 0xf9}}
  gAmdErrorLogFullPpiGuid                = {0xcbe31239, 0x532c, 0x4c27, {0x8f, 0x4e, 0x65, 0x7b, 0x3c, 0x39, 0xa5, 0x56}}
  gAmdErrorLogAvailablePpiGuid           = {0xb2b0fa81, 0x8b34, 0x4351, {0xa3, 0x9c, 0x8f, 0x5a, 0x88, 0x60, 0x19, 0x47}}

  # PCIe Complex Ppis
  gAmdNbioPcieComplexPpiGuid             = {0x324a4e15, 0x26ed, 0x4679, { 0xa9, 0xef, 0xba, 0x8a, 0x8f, 0xe7, 0x9a, 0xdb}}
  gAmdNbioPcieComplexFm15PpiGuid         = {0xdcd2770d, 0xede5, 0x41d0, { 0xa8, 0x84, 0xa8, 0x16, 0x8a, 0xcc, 0xa1, 0x5d}}
  gAmdNbioPcieGen1TrainingCompleteFm15PpiGuid = {0x3cc7c387, 0xee90, 0x4495, {0x98, 0x5, 0x6c, 0xf6, 0x38, 0x5d, 0xee, 0xd4}}
  gAmdPcieAuthenticationGuid             = {0x904CC483, 0xC1E9, 0x414A, { 0x83, 0x3D, 0x5E, 0x69, 0x22, 0xD3, 0xBB, 0x32}}

  gAmdFchInitPpiGuid                     = { 0x3fb7a27a, 0x33f3, 0x483d, {0xbc, 0x6f, 0x7a, 0x51, 0xbe, 0xf5, 0x3d, 0xa } }
  gAmdFchMultiFchInitPpiGuid             = { 0xa5640daf, 0xfb8b, 0x4265, {0xbf, 0xab, 0x9c, 0x77, 0xf4, 0xe9, 0xd6, 0x20} }

  ## SOC AGESA Group Identification Ppi
  gAmdSocAgesaGroupIdentificationPpiGuid = {0x970e2d2f, 0x52f7, 0x483d, {0x94, 0x5e, 0x30, 0xfd, 0x4b, 0x34, 0xb2, 0x17}}
  gAmdPcdInitReadyPpiGuid = {0x60db76a0, 0x5d55, 0x47bb, {0x97, 0xf, 0x34, 0xd8, 0xd0, 0xbe, 0x36, 0x9e}}

  # AGESA Version String Ppi
  gAmdVersionStringPpiGuid = {0x17632cbe, 0x6c80, 0x49b6, {0x82, 0x7, 0x12, 0xb5, 0x3d, 0x9b, 0x25, 0x70}}

  ## Universal
  gCapsuleUpdateDetectedPpiGuid = { 0x745dfc73, 0xc401, 0x4ced, { 0x8d, 0x3b, 0x1a, 0x82, 0xf3, 0xda, 0xdc, 0xf8 } }

  # PCIe Topology
  gAmdNbioPcieTopologyPpiGuid = { 0x778E7A72, 0x5F90, 0x41A9, { 0x54, 0x0E, 0xCB, 0xA4, 0x00, 0x87, 0x86, 0xB1 } }

  # DF
  gAmdFabricResourceManagerServicesPpiGuid = {0xf9060a2e, 0xf514, 0x4d70, {0x8a, 0x62, 0xea, 0x2a, 0xe0, 0x4f, 0x68, 0x52}}

  # PCI Bus Configured
  gAmdPciBusConfiguredPpiGuid            = { 0x99d31609, 0xdca3, 0x45c9, { 0x8e, 0x65, 0xed, 0xba, 0x3e, 0x58, 0xa0, 0x79 } }

  gS3RsmMemInfoPpiGuid         = {0xda81e8f1, 0x2914, 0x4467, {0x90, 0x51, 0x3c, 0x49, 0xac, 0x34, 0xe2, 0x4d}}
[PcdsFeatureFlag]

#----------------------------------------------------------------------------
#- Start documentation of user configurable controls
### Set Doxy_path: "PCD-Sys.h"
#----------------------------------------------------------------------------

[PcdsFixedAtBuild]

  ### @name General System PCD Controls

  ### @brief This token will display the following Event Log contents at the end of the UEFI boot up sequence. Customer can use
  ### it to display info per their need. Not all of the error log will be displayed, just the items with the listed topics.
  ### Note: the console output must be enabled for the platform. The items displayed are:
  ### @li display PMU training error detail info.
  ### @li ABL_MEM_PMU_TRAIN_ERROR
  ### @li ABL_MEM_AGESA_MEMORY_TEST_ERROR
  ### @li ABL_MEM_ERROR_MIXED_ECC_AND_NON_ECC_DIMM_IN_SYSTEM
  ### @li ABL_MEM_ERROR_LRDIMM_MIXMFG
  ### @li ABL_CCD_BIST_FAILURE
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li TRUE - This information will be printed to console.
  ### @li FALSE - This option is turned off.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAgesaPrintEventLogToConsole|FALSE|BOOLEAN|0x0002000E

  ### @brief Specify AGESA_TESTPOINT output IO port
  ### @details Specify AGESA_TESTPOINT output IO port for both test point and redirect IO print
  ### @li 0x80: Io Port Value
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdIdsDebugPort|0x80|UINT16|0x00020003

  ### @brief Enable EDK-II Protocols
  ### @details This item enables support for EDKII implementation of ACPI Protocols when set to TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCompliantEdkIIAcpiSdtProtocol|TRUE|BOOLEAN|0x00020006

  ### @brief assign non volatile storage block size.
  ### @details This assigns the flasg device block size.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAgesaFlashNvStorageBlockSize |0x1000|UINT32|0x00020007

  ### @brief assign non volatile storage base address
  ### @details This assigns the base address to map to flash deivce.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAgesaFlashAreaBaseAddress|0xFF000000|UINT32|0x00020017

#----------------------------------------------------------------------------
#- Start documentation of user configurable controls
### Set Doxy_path: "PCD-Sys-PPL.h"
#----------------------------------------------------------------------------

  # APCB 3.0

  ### @name APCB Purpose Priority Levels
  ### An APCB token may be saved in different instances or purpose levels and can
  ### have instances of the token at multiple purpose levels. These purpose levels
  ### provide a hierarchy of priority such that a token entry at one purpose level
  ### can be added to override the same token value set at a lower purpose level.
  ### The classic example is a priority system such that ADMIN -> DEBUGGING -> NORMAL,
  ### which means something occurring at a higher priority level would override
  ### another at a lower one. If the user set a token to TRUE at the debugging
  ### level and set the same one to FALSE at the normal level, the token readout
  ### would be TRUE. The intent is that a token can be temporarily changed for
  ### debug or evaluation; but should eventually be migrated to the 'Normal'
  ### purpose.
  ### <p>Some of the purposes (admin/debug/normal) can set their priority level
  ### via these PCD variables. By default, ADMIN -> DEBUG -> NORMAL
  ### (e.g. High -> Medium-> Low) , but the user can change it. For example if
  ### PcdAmdApcbPriorityLevelDebug is set to APCB_PRIORITY_HIGH (0x2) and PcdAmdApcbPriorityLevelAdmin
  ### set to APCB_PRIORITY_MEDIUM (0x3), the operation in DEBUGGING realm will be prioritized over the
  ### one in ADMIN (DEBUG -> ADMIN -> NORMAL). This can be done without actually
  ### changing the invocations in the executable code, but simply by changing the
  ### assignments of the PCDs.
  ### <p>Available Priority labels are (in highest to lowest priority):
  ###  1. HARD_FORCE - (Highest priority) Force this value to always be used.
  ###  2. ADMIN - ('high') Administrator's set of values.
  ###  3. DEBUG - ('medium') values used for temporary debugging.
  ###  4. EVENT_LOGGING - Instances used for Data Logging.
  ###  5. NORMAL - ('normal') Instances intended for production.
  ###  6. DEFAULT - (lowest priority)Instances for tokens that have no platform
  ###      specific setting.


  ### @brief The assigned priority level for the 'Admin' purpose. Permitted Choices: (Type: List)(Default: APCB_PRIORITY_HIGH)
  ### @li APCB_PRIORITY_HIGH - 0x2
  ### @li APCB_PRIORITY_MEDIUM - 0x3
  ### @li APCB_PRIORITY_LOW - 0x5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdApcbPriorityLevelAdmin|2|UINT8|0x000AB000

  ### @brief The assigned priority level for the 'Debug' purpose. Permitted Choices: (Type: List)(Default: APCB_PRIORITY_MEDIUM - 0x3)
  ### @li APCB_PRIORITY_HIGH - 0x2
  ### @li APCB_PRIORITY_MEDIUM - 0x3
  ### @li APCB_PRIORITY_LOW - 0x5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdApcbPriorityLevelDebug|3|UINT8|0x000AB001

  ### @brief The assigned priority level for the 'Normal' purpose. Permitted Choices: (Type: List)(Default: APCB_PRIORITY_LOW - 0x5)
  ### @li APCB_PRIORITY_HIGH - 0x2
  ### @li APCB_PRIORITY_MEDIUM - 0x3
  ### @li APCB_PRIORITY_LOW - 0x5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdApcbPriorityLevelNormal|5|UINT8|0x000AB002

#----------------------------------------------------------------------------
#- Start documentation of user configurable controls
### Set Doxy_path: "PCD-Sys.h"
#----------------------------------------------------------------------------

  ### @brief Enable updating Apcb Dimm Spd Shadow
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdApcbDimmSpdShadow|TRUE|BOOLEAN|0x000AB003

  ### @brief Enable/Disable to register APCB token id into white list table before SMM Lock.
  ### @details When this PCD is set to enable, if user did not add the related APCB token id and attribute information
  ###  into while list table before SMM lock, APCB SMM protocol (get/write: TokenBool/Token8/Token16/Token32)
  ###  will be denied access after SMM was locked. In the current design, after the APCB DXE/SMM protocol were installed
  ###  and before the SMM lock, the white list table is allowed to modify. The APCB DXE and SMM protocol function:
  ###  ApcbTokensRegisterPermission can be called to register APCB token into the white list table.
  ###  After SMM is locked, APCB SMM Protocol: access token functions will use the white list buffer table to verify
  ###  the APCB token id access is allowed or not before accessing the APCB data.
  ### @li TRUE:  Enabled
  ### @li FALSE: Disabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEnableApcbTokensRegisterPermission|FALSE|BOOLEAN|0x000AB004

  ### @brief Max number of APCB token id that are allocated for Apcb whitelist buffer
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdApcbTokensRegisterBufferMaxNum|1024|UINT32|0x000AB005

#----------------------------------------------------------------------------
#-  FCH Fixed PCDs
#-
### Set Doxy_path: "PCD-FCH.h"
#----------------------------------------------------------------------------

  ### @name General FCH Controls

  ### @brief Allows the host BIOS to set the base IO address used for the SMBus 0 controller. The SMBus1 controller base address
  ### is at a fixed offset from the base0 address. The size of this block is defined in the Processor Programmers Reference (PPR).
  ### @brief Permitted Choices: (Type: Value)(Default: 0x0B00)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchCfgSmbus0BaseAddress|0x0B00|UINT16|0x0002F001

  ### @brief This item sets the IO Base address for the SIO block of command registers. The size of this block depends on the SIO chosen.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x0E00)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchCfgSioPmeBaseAddress|0xE00|UINT16|0x0002F003

  ### @brief Allows the host BIOS to specify the IO address for the ACPI PM1 register blocks as defined by ACPI spec.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x0400)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchCfgAcpiPm1EvtBlkAddr|0x400|UINT16|0x0002F004

  ### @brief  Allows the host BIOS to specify the IO address for the ACPI PM1Cnt register blocks as defined by ACPI spec.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x0404)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchCfgAcpiPm1CntBlkAddr|0x404|UINT16|0x0002F005

  ### @brief Allows the host BIOS to specify the IO address for the ACPI PM Timer as defined by ACPI spec.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x0408)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchCfgAcpiPmTmrBlkAddr|0x408|UINT16|0x0002F006

  ### @brief Allows the host BIOS to specify the IO address for the ACPI CPU Control block as defined by ACPI spec.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x0410)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchCfgCpuControlBlkAddr|0x410|UINT16|0x0002F007

  ### @brief Allows the host BIOS to specify the IO address for the ACPI GPE0 register block as defined by ACPI spec.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x0420)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchCfgAcpiGpe0BlkAddr|0x420|UINT16|0x0002F008

  ### @brief This controls specifies the GPIO used by BIOS to trigger NVDIMM-N catastrophic save while cold/warm reset is
  ### happening (i.e. the GPIO used as the FSR_REQ signal).
  ### @brief Permitted Choices: (Type: value) (Default: 00)
  ### @li 00 - no GPIO is assigned.
  ### @li 01.. - This is the number of the GPIO that is assigned to the function. Example: value of 149(decimal) --> uses
  ### EGPIO149. Be careful of the decimal<->Hex conversions.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdNvdimmGpioForceSave|0x00000095|UINT32|0x0002F00A

  ### @brief This item defines the SMI command value sent by the host BIOS during the S3 resume sequence, to re-initialize the
  ### FCH registers. This must be issued before the platform driver restore function is started.
  ### @brief Permitted Choices: (Type: Value)(Default: 0xD3)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemBeforePciRestoreSwSmi|0xD3|UINT8|0x0002F010

  ### @brief This item defines the SMI command used by the host BIOS to signal the FCH driver that the platform driver has
  ### completed its restore function. This allows the FCH driver to perform some final FCH settings.
  ### @brief Permitted Choices: (Type: Value)(Default: 0xD4)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemAfterPciRestoreSwSmi|0xD4|UINT8|0x0002F011

  ### @brief Allows the host BIOS to set the SMI command value used by the OS to activate ACPI mode.
  ### @brief Permitted Choices: (Type: Value)(Default: 0xA0)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemEnableAcpiSwSmi|0xA0|UINT8|0x0002F012

  ### @brief Allows the host BIOS to set the SMI command value used by the OS to turn off ACPI mode.
  ### @brief Permitted Choices: (Type: Value)(Default: 0xA1)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemDisableAcpiSwSmi|0xA1|UINT8|0x0002F013

  ### @brief SMI command used for releasing the SPI controller lock mode. All devices on the SPI bus will be writable.
  ### @brief Permitted Choices: (Type: Value)(Default: 0xAA)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemSpiUnlockSwSmi|0xAA|UINT8|0x0002F014

  ### @brief SMI command for setting the lock mode in the SPI controller. This will effectively provide a write protection to the
  ### SPI Flash ROM; however, write access to secondary SPI devices will also be blocked.
  ### @brief Permitted Choices: (Type: Value)(Default: 0xAB)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemSpiLockSwSmi|0xAB|UINT8|0x0002F015

  ### @brief This item defines the SMI command value sent by the OS in the _WAK ACPI method during the S3 resume sequence when RomArmor 2 is enabled,
  ### It's required only when Boot from ROM is used and RomArmor 2 is enabled in the system.
  ### in the SMI handler, AGESA will notify PSP boot done, and PSP will block SPI MMIO read.
  ### @brief Permitted Choices: (Type: Value)(Default: 0xD5)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemAfterSystemWakeSwSmi|0xD5|UINT8|0x0002F018

#----------------------------------------------------------------------------
#-  FCH Fixed PCDs
#-
### Set Doxy_path: "PCD-FCH-IRQ.h"
#----------------------------------------------------------------------------

  ### @name IRQ Assignments
  ### IRQ assignments for the UART & I2C ports
  ### @{
  ### @brief  PcdFchUart1Irq [F17M30]  These controls allow the platform to specify which IRQs the devices should use. Between
  ### the UART and I2C ports, it is recommended that no two ports use the same IRQ. Control of which ports are enabled is handled
  ### by FchRTDeviceEnableMap. Permitted Choices: (Type: value)(Default: as below)
  ### @li Uart 0 - IRQ3
  ### @li Uart 1 - IRQE
  ### @li Uart 2 - IRQ5
  ### @li Uart 3 - IRQF
  ### @li Uart 4 - IRQD
  ### @li I2C 0 - IRQA
  ### @li I2C 1 - IRQB
  ### @li I2C 2 - IRQ4
  ### @li I2C 3 - IRQ6
  ### @li I2C 4 - IRQ16
  ### @li I2C 5 - IRQ17



  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchUart0Irq|0x03|UINT8|0x0002F01A
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchUart1Irq|0x0E|UINT8|0x0002F01B
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchUart2Irq|0x05|UINT8|0x0002F01C
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchUart3Irq|0x0F|UINT8|0x0002F01D
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchUart4Irq|0x10|UINT8|0x0002F01E
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI2c0Irq|0x0A|UINT8|0x0002F01F
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI2c1Irq|0x0B|UINT8|0x0002F020
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI2c2Irq|0x04|UINT8|0x0002F021
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI2c3Irq|0x06|UINT8|0x0002F022
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI2c4Irq|0x16|UINT8|0x0002F023
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI2c5Irq|0x17|UINT8|0x0002F024
  ### @} end IRQ_assignments

#----------------------------------------------------------------------------
#-  FCH Fixed PCDs
#-
### Set Doxy_path: "PCD-FCH.h"
#----------------------------------------------------------------------------

  ### @brief This items selects the action to be taken when the system requests a 'cold reset'.
  ### @brief Permitted Choices: (Type: Boolean)(Default: False)
  ### @li False - Assert Reset signals only. This will be quicker.
  ### @li True - place system in S5 state for 3 to 5 seconds. This causes power Good to de-assert. Return to operation will take longer.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchFullHardReset|FALSE|BOOLEAN|0x0002F02A
  ### @brief SMI command for enabling 'fencing' (security protection) on the FCH range of IO registers. This will effectively
  ### provide a SW SMI number to lock FCH register via PSP command when PcdFchOemSecureEnable is TRUE.
  ### @brief Permitted Choices: (Type: Value)(Default: 0xD2 )
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemSecureSwSmi|0xD2|UINT8|0x0002F02B

  ### @brief SMI command for setting PMFW stopping polling DIMM telemetry. This will effectively provide a SW SMI number for stopping PMFW polling DIMM telemetry when PcdAmdFchSpdHostCtrlRelease is FALSE.
  ### @brief Permitted Choices: (Type: Value)(Default: 0xBE )
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemCaptureSPDBusSmi|0xBE|UINT8|0x0002F032
  ### @brief SMI command for setting  PMFW resuming polling DIMM telemetry. This will effectively provide a SW SMI number for resuming PMFW polling DIMM telemetry when PcdAmdFchSpdHostCtrlRelease is FALSE.
  ### @brief Permitted Choices: (Type: Value)(Default: 0xBF )
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemReleaseSPDBusSmi|0xBF|UINT8|0x0002F033

  ### @cond  (RPL)
  ### @brief This value defines the 'OEM identifier' used when creating the PT SSDT ACPI table.
  ### @brief Permitted Choices: (Type: String, up to 8chars)(Default: "AMD PT")
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiPtSsdtTableHeaderOemTableId|"AmdTable"|VOID*|0x000AC008
  ### @endcond

  ### @cond (RS||MI3||BRH)
  ### @brief Turn off I3C master driver's debug message .
  ### @brief Permitted Choices: (Type: Boolean)(Default: False)
  ### @li False - do nothing.
  ### @li True -  To disable debug message of I3c master driver.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI3CDxeDebugOff|FALSE|BOOLEAN|0x0002F034
  ### @endcond
  ### @brief I3C Transfer time out (us)
  ### @details Use it in FchI3cLib.c for I3C transfer polling status time out in microsecond.
  ### The maximum of polling status time = PcdFchI3cTimeoutUs * PcdFchI3cTimeoutRetry
  ### @li 20~65535 = Range
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI3cTimeoutUs|20|UINT16|0x0002F036

  ### @brief I3C Transfer time out retry count
  ### @details Use it in FchI3cLib.c for I3C transfer polling status time out retry count.
  ### The maximum of polling status time = PcdFchI3cTimeoutUs * PcdFchI3cTimeoutRetry
  ### @li 01~32767 = Range
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI3cTimeoutRetry|2500|UINT16|0x0002F037
#----------------------------------------------------------------------------
#-  CCX Fixed PCDs
#-
### Set Doxy_path: "PCD-CCX.h"
#----------------------------------------------------------------------------
  ### @cond (AM4BR)
  ### @brief Number of IO APICs in the system
  ### @details This element provides the number of IO APICs that are used on the
  ### motherboard. This is used by the software to appropriately assign APIC IDs
  ### to the processors, leaving room for the motherboard devices.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNumberOfIoApics|3|UINT8|0x000CA000
  ### @endcond


#----------------------------------------------------------------------------
#-  DF Fixed PCDs
#-
### Set Doxy_path: "PCD-DF.h"
#----------------------------------------------------------------------------

  #Fabric

  ### @name General DF Controls

  ### @brief MMIO Reserved for Die0-RB0.
  ### @details This entry specifies an address in the below 4GB space that starts
  ### a reserved MMIO region. The space from this address to the 0xFEE0_0000
  ### address ('COMPAT' region) will be reserved for devices connected to Die0
  ### to use as MMIO space. This address should be at the end of, or above, the
  ### PCIe general allocation zone (Base + size).
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdBottomMmioReservedForPrimaryRb|0xFE000000|UINT32|0x000CC100

  ### @brief This item informs the SMBios generation code as to how many physical processor sockets exist in the system and
  ### therefore how many Type 4 SMBios records to produce.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x01)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNumberOfPhysicalSocket|1|UINT8|0x000CC103

  ### @brief MMIO for non-PCI
  ### @details Every Root Bridge has a non-PCI MMIO pool for items such as the PSP or IOMMU.
  ### This PCD defines the size of that pool to be allocated below the 4G boundary.
  ### The default is 16MB per Root Bridge.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMmioSizePerRbForNonPciDevice|0x1000000|UINT32|0x000CC104

  ### @brief MMIO for non-PCI above 4G
  ### @details Every Root Bridge has a non-PCI MMIO pool for items such as the PSP or IOMMU.
  ### This PCD defines the size of that pool to be allocated above the 4G boundary.
  ### The default is 514MB per Root Bridge.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAbove4GMmioSizePerRbForNonPciDevice|0x20200000|UINT32|0x000CC105

  ### @brief The percentage of Prefetchable MMIO
  ### @details PCI devices may have non-prefetchable and prefetchable memory requirement.
  ### This PCD defines the percentage of prefetchable MMIO of total non-prefetchable and prefetchable MMIO.
  ### The default value is 80 which means the percentage of prefetchable and non-prefetchable MMIO allocation are 80:20.
  ### @li 1~99 = Range
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMmioPercentageForPrefetchable|80|UINT8|0x000CC107

#----------------------------------------------------------------------------
#-  CCX Fixed PCDs
#-
### Set Doxy_path: "PCD-CCX-MTRR.h"
#----------------------------------------------------------------------------

  ### @name FixedMTRRs
  ### @{
  ### These are the FixedMTRR settings for all Application Processors (APs).
  ### The BootStrap processor (BSP) may be impacted because the
  ### MTRRs are shared between cores.
  ### A Value of 0xFFFFFFFFFFFFFFFF means that all APs must sync with the BSP.

  ### MTRR_FIX64k_00000
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFixedMtrr250|0xFFFFFFFFFFFFFFFF|UINT64|0x000CC110
  ### MTRR_FIX16k_80000
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFixedMtrr258|0xFFFFFFFFFFFFFFFF|UINT64|0x000CC111
  ###  MTRR_FIX16k_A0000
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFixedMtrr259|0xFFFFFFFFFFFFFFFF|UINT64|0x000CC112
  ###  MTRR_FIX4k_C0000
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFixedMtrr268|0xFFFFFFFFFFFFFFFF|UINT64|0x000CC113
  ###  MTRR_FIX4k_C8000
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFixedMtrr269|0xFFFFFFFFFFFFFFFF|UINT64|0x000CC114
  ### MTRR_FIX4k_D0000
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFixedMtrr26A|0xFFFFFFFFFFFFFFFF|UINT64|0x000CC115
  ###  MTRR_FIX4k_D8000
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFixedMtrr26B|0xFFFFFFFFFFFFFFFF|UINT64|0x000CC116
  ### MTRR_FIX4k_E0000
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFixedMtrr26C|0x1818181818181818|UINT64|0x000CC117
  ### MTRR_FIX4k_E8000
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFixedMtrr26D|0x1818181818181818|UINT64|0x000CC118
  ### MTRR_FIX4k_F0000
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFixedMtrr26E|0x1818181818181818|UINT64|0x000CC119
  ### MTRR_FIX4k_F8000
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFixedMtrr26F|0x1818181818181818|UINT64|0x000CC11A
  ### @} end FixedMTRRs

  ### @brief The base address of temporary page table for accessing PCIE MMIO base address above 4G in PEI phase.
  ### @brief Permitted Choices: 0x0 - 0xFFFFFFFF (Type: Value)(Default: 0)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPeiTempPageTableBaseAddress|0|UINT32|0x000CC11B

#----------------------------------------------------------------------------
#-  CCX ACPI Fixed PCDs
#-
### Set Doxy_path: "PCD-CCX-ACPI.h"
#----------------------------------------------------------------------------
  #ACPI
  #  Cpu SSDT

  ### @name CCX ACPI Controls

  ### @brief When creating the ACPI tables to describe the processor cores, the AGESA TM software will number the core
  ### sequentially from '00' to 'NN'. This leaves the first two characters of the ACPI name open for the customer to specify
  ### a desired prefix. These two elements set those two characters.
  ### @brief Permitted Choices:
  ### @li Scope name 0: (Type: Value/char)(Default: 'C'(0x43))
  ### @li Scope name 1: (Type: Value/char)(Default: '0'(zero)(0x30))
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuSsdtProcessorScopeName0|0x43|UINT8|0x000AC001

  ### @brief CPU SSDT scope name, 2nd character
  ### @see PcdAmdAcpiCpuSsdtProcessorScopeName0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuSsdtProcessorScopeName1|0x30|UINT8|0x000AC002

  ### @brief This element specifies whether the ACPI _PSS objects are defined in the system bus or processor scope.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li TRUE - The objects will be under the \_SB scope.
  ### @li FALSE - The objects will be under the \_PR scope
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuSsdtProcessorScopeInSb|FALSE|BOOLEAN|0x000AC003

  ### @brief Set the OEM ID field in ACPI table outputs to this string. The string must conform to the ACPI rules for the OEM ID field.
  ### @brief Permitted Choices: (Type: Value)(Default: "AMD")
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiTableHeaderOemId|"AMD"|VOID*|0x000AC004

  ### @brief Set the OEM TABLE ID field in ACPI table outputs to this string. The string must conform to the ACPI rules for the
  ### OEM TABLE ID field.
  ### @brief Permitted Choices: (Type: Value)(Default: "AmdTable")
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiTableHeaderOemTableId|"AmdTable"|VOID*|0x000AC005

  ### @brief This value defines the 'OEM identifier' used when creating the CPU SSDT ACPI table.
  ### @brief Permitted Choices: (Type: String, up to 8chars)(Default: "AMD CPU")
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuSsdtTableHeaderOemTableId|"AMD CPU"|VOID*|0x000AC006

  ### @brief Permitted Choices: (Type: String, up to 8chars)(Default: "AMD CDIT")
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCditTableHeaderOemTableId|"AMD CDIT"|VOID*|0x000AC009

  ### @brief This value defines the 'OEM identifier' used when creating the CRAT ACPI table.
  ### @brief Permitted Choices: (Type: String , up to 8chars)(Default: "AMD CRAT")
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCratTableHeaderOemTableId|"AMD CRAT"|VOID*|0x000AC00A

  ### @brief This value defines the 'OEM identifier' used when creating the SLIT ACPI table.
  ### @brief Permitted Choices: (Type: String up to 8chars)(Default: "AMD SLIT")
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiSlitTableHeaderOemTableId|"AMD SLIT"|VOID*|0x000AC00B

  ### @brief This value defines the 'OEM identifier' used when creating the SRAT ACPI table.
  ### @brief Permitted Choices: (Type: String up to 8chars)(Default: "AMD SRAT")
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiSratTableHeaderOemTableId|"AMD SRAT"|VOID*|0x000AC00C

  ### @brief ACPI  OemTableId for Mcst
  ### @details This value defines the 'OEM identifier' used when creating the
  ### MCST ACPI table.  This is a String type value of up to 8chars.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiMsctTableHeaderOemTableId|"AmdTable"|VOID*|0x000AC00E

  ### @brief ACPI  OemTableId for Pcct
  ### @details This value defines the 'OEM identifier' used when creating the
  ### PCCT ACPI table.  This is a String type value of up to 8chars.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiPcctTableHeaderOemTableId|"AmdTable"|VOID*|0x000AC00F

  ### @brief ACPI  OemTableId for Hmat
  ### @details This value defines the 'OEM identifier' used when creating the
  ### HMAT ACPI table.  This is a String type value of up to 8chars.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiHmatTableHeaderOemTableId|"AmdTable"|VOID*|0x000AC010

  ### @brief This value defines the 'OEM identifier' used when creating the CEDT ACPI table.
  ### @brief Permitted Choices: (Type: String, up to 8chars)(Default: "AMD CEDT")
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCedtTableHeaderOemTableId|"AMD CEDT"|VOID*|0x000AC011

  ### @brief ACPI  OemTableId for Aspt
  ### @details This value defines the 'OEM identifier' used when creating the
  ### ASPT ACPI table.  This is a String type value of up to 8chars.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiAsptTableHeaderOemTableId|"AmdTable"|VOID*|0x000AC012

  ### @brief ACPI  OemTableId for Bdat
  ### @details This value defines the 'OEM identifier' used when creating the
  ### BDAT ACPI table.  This is a String type value of up to 8chars.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiBdatTableHeaderOemTableId|"AmdTable"|VOID*|0x000AC013

#----------------------------------------------------------------------------
#-  System level Fixed PCDs
#-
### Set Doxy_path: "PCD-Sys.h"
#----------------------------------------------------------------------------

  #SMBIOS

  ### @brief Remove or Add AMD SMBIOS Table
  ### @details Remove or Add AMD SMBIOS Table Type: 4, 7, 11, 16, 17,
  ### 18, 19, 20, and 40
  ### @li TRUE:  Add AMD Smbios Table
  ### @li FALSE: Do not Add AMD Smbios Table
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdRemoveSmbios|FALSE|BOOLEAN|0x00001000

#----------------------------------------------------------------------------
#-  CCX SMBIOS Fixed PCDs
#-
### Set Doxy_path: "PCD-CCX-SMBIOS.h"
#----------------------------------------------------------------------------

  ### @name CCX SMBIOS Controls

  ### @brief When creating the SMBios table entry, use this as the label for the processor socket. This should match the
  ### silkscreen label on the motherboard.
  ### @brief Permitted Choices: (Type: Value)(Default: "Unknown")
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosSocketDesignationSocket0|"Unknown"|VOID*|0x00001001

  ### @brief When creating the SMBios table entry, use this as the value for the 'serial number' field for each processor.
  ### @brief Permitted Choices: (Type: Value)(Default: "Unknown")
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosSerialNumberSocket0|"Unknown"|VOID*|0x00001002


  ### @brief When creating the SMBios table entry, use this as the value for the 'Asset Tag' field for each processor.
  ### @brief Permitted Choices: (Type: Value)(Default: "Unknown")
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosAssetTagSocket0|"Unknown"|VOID*|0x00001003

  ### @brief When creating the SMBios table entry, use this as the value for the 'Part Number' field for each processor.
  ### @brief Permitted Choices: (Type: Value)(Default: "Unknown")
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosPartNumberSocket0|"Unknown"|VOID*|0x00001004

  ### @brief SMBios socket 1 Label.
  ### @details When creating the SMBios table entry, use this as the label for the
  ### processor socket. This should match the silkscreen label on the motherboard.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosSocketDesignationSocket1|"Unknown"|VOID*|0x00001005

  ### @brief SMBIOS socket 1 Serial Number.
  ### @details When creating the SMBios table entry, use this as the value for
  ### the 'serial number' field for the processor in socket 1.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosSerialNumberSocket1|"Unknown"|VOID*|0x00001006

  ### @brief SMBios socket 1 Asset Tag.
  ### @details When creating the SMBios table entry, use this as the value for
  ### the 'Asset Tag' field for the processor in socket 1.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosAssetTagSocket1|"Unknown"|VOID*|0x00001007

  ### @brief Socket 1 Part Number.
  ### @details When creating the SMBios table entry, use this as the value for
  ### the 'Part Number' field for the processor in socket 1.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosPartNumberSocket1|"Unknown"|VOID*|0x00001008

#----------------------------------------------------------------------------
#-  ACPI Fixed PCDs
#-
### Set Doxy_path: "PCD-ACPI.h"
#----------------------------------------------------------------------------

  ### @name General ACPI Controls

  ### @brief Select Long DIMM Serial Number Format (9-byte)
  ### @details
  ### @li TRUE:  Long DIMM Serial Number (9-byte)
  ### @li FALSE: Short DIMM Serial Number (4-byte)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosT17LongSerialNumber|TRUE|BOOLEAN|0x0000100A

  ### @brief PCD supporting maximum capacity for Type 16 table
  ### @details This PCD represents maximum memory capacity in KB
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosT16MaximumCapacity|0x04000000|UINT32|0x00001009   # 4G   - 0x00400000
                                                                                              # 8G   - 0x00800000
                                                                                              # 16G  - 0x01000000
                                                                                              # 32G  - 0x02000000
                                                                                              # 64G  - 0x04000000
                                                                                              # 128G - 0x08000000
                                                                                              # 256G - 0x10000000
                                                                                              # 512G - 0x20000000
  ### @brief PCD representing DIMM Vendor Info based of DIMM Vendor ID as per SPD data.
  ### @details This PCD represents DIMM Vendor Id based off SPD table.
  ### This will be utilized to extract the DIMM vendor name from
  ### PcdAmdSmbiosDimmVendorStr PCDs
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorWord0|0x2C00|UINT16|0x00001010
  ### @see PcdAmdSmbiosDimmVendorWord0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorWord1|0xAD00|UINT16|0x00001011
  ### @see PcdAmdSmbiosDimmVendorWord0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorWord2|0xC100|UINT16|0x00001012
  ### @see PcdAmdSmbiosDimmVendorWord0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorWord3|0xCE00|UINT16|0x00001013
  ### @see PcdAmdSmbiosDimmVendorWord0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorWord4|0x4F00|UINT16|0x00001014
  ### @see PcdAmdSmbiosDimmVendorWord0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorWord5|0x9801|UINT16|0x00001015
  ### @see PcdAmdSmbiosDimmVendorWord0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorWord6|0xFE02|UINT16|0x00001016
  ### @see PcdAmdSmbiosDimmVendorWord0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorWord7|0x0B03|UINT16|0x00001017
  ### @see PcdAmdSmbiosDimmVendorWord0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorWord8|0x2503|UINT16|0x00001018
  ### @see PcdAmdSmbiosDimmVendorWord0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorWord9|0x8303|UINT16|0x00001019
  ### @see PcdAmdSmbiosDimmVendorWord0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorWord10|0xCB04|UINT16|0x0000101A
  ### @see PcdAmdSmbiosDimmVendorWord0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorWord11|0xC106|UINT16|0x0000101B
  ### @see PcdAmdSmbiosDimmVendorWord0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorWord12|0x4304|UINT16|0x0000101C
  ### @see PcdAmdSmbiosDimmVendorWord0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorWord13|0x1603|UINT16|0x0000101D
  ### @see PcdAmdSmbiosDimmVendorWord0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorWord14|0x0000|UINT16|0x0000101E
  ### @see PcdAmdSmbiosDimmVendorWord0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorWord15|0x0000|UINT16|0x0000101F

  ### @details PCD for mapping DIMM Vendor ID to a string
  ### @see PcdAmdSmbiosDimmVendorWord0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorStr0|"Micron Technology"|VOID*|0x00001020
  ### @see PcdAmdSmbiosDimmVendorStr0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorStr1|"SK Hynix"|VOID*|0x00001021
  ### @see PcdAmdSmbiosDimmVendorStr0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorStr2|"Infineon (Siemens)"|VOID*|0x00001022
  ### @see PcdAmdSmbiosDimmVendorStr0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorStr3|"Samsung"|VOID*|0x00001023
  ### @see PcdAmdSmbiosDimmVendorStr0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorStr4|"Transcend Information"|VOID*|0x00001024
  ### @see PcdAmdSmbiosDimmVendorStr0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorStr5|"Kingston"|VOID*|0x00001025
  ### @see PcdAmdSmbiosDimmVendorStr0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorStr6|"Elpida"|VOID*|0x00001026
  ### @see PcdAmdSmbiosDimmVendorStr0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorStr7|"Nanya Technology"|VOID*|0x00001027
  ### @see PcdAmdSmbiosDimmVendorStr0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorStr8|"Kingmax Semiconductor"|VOID*|0x00001028
  ### @see PcdAmdSmbiosDimmVendorStr0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorStr9|"Buffalo (Formerly Melco)"|VOID*|0x00001029
  ### @see PcdAmdSmbiosDimmVendorStr0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorStr10|"A-DATA Technology"|VOID*|0x0000102A
  ### @see PcdAmdSmbiosDimmVendorStr0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorStr11|"ASint Technology"|VOID*|0x0000102B
  ### @see PcdAmdSmbiosDimmVendorStr0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorStr12|"Ramaxel Technology"|VOID*|0x0000102C
  ### @see PcdAmdSmbiosDimmVendorStr0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorStr13|"Netlist"|VOID*|0x0000102D
  ### @see PcdAmdSmbiosDimmVendorStr0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorStr14|"Unknown"|VOID*|0x0000102E
  ### @see PcdAmdSmbiosDimmVendorStr0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosDimmVendorStr15|"Unknown"|VOID*|0x0000102F

  ### @brief SMBios socket 2 Label.
  ### @details When creating the SMBios table entry, use this as the label for the
  ### processor socket. This should match the silkscreen label on the motherboard.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosSocketDesignationSocket2|"Unknown"|VOID*|0x00001030

  ### @brief SMBIOS socket 2 Serial Number.
  ### @details When creating the SMBios table entry, use this as the value for
  ### the 'serial number' field for the processor in socket 2.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosSerialNumberSocket2|"Unknown"|VOID*|0x00001031

  ### @brief SMBios socket 2 Asset Tag.
  ### @details When creating the SMBios table entry, use this as the value for
  ### the 'Asset Tag' field for the processor in socket 2.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosAssetTagSocket2|"Unknown"|VOID*|0x00001032

  ### @brief Socket 2 Part Number.
  ### @details When creating the SMBios table entry, use this as the value for
  ### the 'Part Number' field for the processor in socket 2.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosPartNumberSocket2|"Unknown"|VOID*|0x00001033

  ### @brief SMBios socket 3 Label.
  ### @details When creating the SMBios table entry, use this as the label for the
  ### processor socket. This should match the silkscreen label on the motherboard.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosSocketDesignationSocket3|"Unknown"|VOID*|0x00001034

  ### @brief SMBIOS socket 3 Serial Number.
  ### @details When creating the SMBios table entry, use this as the value for
  ### the 'serial number' field for the processor in socket 3.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosSerialNumberSocket3|"Unknown"|VOID*|0x00001035

  ### @brief SMBios socket 3 Asset Tag.
  ### @details When creating the SMBios table entry, use this as the value for
  ### the 'Asset Tag' field for the processor in socket 3.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosAssetTagSocket3|"Unknown"|VOID*|0x00001036

  ### @brief Socket 3 Part Number.
  ### @details When creating the SMBios table entry, use this as the value for
  ### the 'Part Number' field for the processor in socket 3.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosPartNumberSocket3|"Unknown"|VOID*|0x00001037
#----------------------------------------------------------------------------
#-  System level Fixed PCDs
#-
### Set Doxy_path: "PCD-Sys.h"
#----------------------------------------------------------------------------

  ### @cond NOT(BRH)
  ### FSP PCDs
  ### PcdAmdFsp will be used to differentiate FSP runtime environment from standard UEFI BIOS
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFsp|FALSE|BOOLEAN|0x0001040
  ### @endcond

#----------------------------------------------------------------------------
#    NBIO PCDs FIXED AT BUILD
### Set Doxy_path: "PCD-NBIO.h"
#----------------------------------------------------------------------------
  ### @cond (BIXBY)
  ### NBIO BIXBY SUPPORT
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdBixbySupport|FALSE|BOOLEAN|0x000AB010
  ### @endcond

  ### @cond (PHX||STX)
  ### @brief This PCD check the EndPoint vendor/device id and set the  EngineLcRecovery
  ### @brief typedef struct {
  ### @brief   UINT16    VendorId;
  ### @brief   UINT16    DeviceId;
  ### @brief   UINT8     (Bit 0 LC_RXRECOVER_EN Bit 7:1 LC_RXRECOVER_TIMEOUT us)
  ### @brief } ;
  ### @brief LcRecovery LcRecoveryListDeviceList[2] = {
  ### @brief   {0x0000, 0x0000, 0x00},
  ### @brief   {0xFFFF, 0xFFFF, 0x00000000} // TERMINATE
  ### @brief };
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEngineLcRecovery|{0x00}|VOID*|0x000AB011
  ### @endcond

  ### @cond (PHX||STX)
  ### @brief This PCD check the EndPoint vendor/device id and set the ExtendedSynch
  ### @brief typedef struct {
  ### @brief   UINT16    VendorId;
  ### @brief   UINT16    DeviceId;
  ### @brief } ;
  ### @brief LcRecovery ExtendedSynch [2] = {
  ### @brief   {0x0000, 0x0000},
  ### @brief   {0xFFFF, 0xFFFF} // TERMINATE
  ### @brief };
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdExtendedSynch |{0x0}|VOID*|0x000AB012
  ### @endcond

#----------------------------------------------------------------------------
#    PSP PCDs FIXED AT BUILD
### Set Doxy_path: "PCD-PSP.h"
#----------------------------------------------------------------------------

  ### @name General PSP Controls

  ### @cond !BRH
  ### @brief Time out value in microseconds of TPM_CC_CreatePrimary & TPM_CC_Create
  ### @details Time out value in microseconds of TPM_CC_CreatePrimary & TPM_CC_Create,
  ### and 0xFFFFFFFF means wait infinitely.
  ### @li non 0xFFFFFFFF: Time out value in microseconds
  ### @li 0xFFFFFFFF: Means wait infinitely
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFtpmTimeout1|0xFFFFFFFF|UINT64|0x95940006
  ### @endcond

  ### @cond !BRH
  ### @brief Time out value in microseconds for reset of TPM command
  ### @details Time out value in microseconds for reset of TPM command,
  ### and 0xFFFFFFFF means wait infinitely.
  ### @li non 0xFFFFFFFF: Time out value in microseconds
  ### @li 0xFFFFFFFF: Means wait infinitely
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFtpmTimeout2|0xFFFFFFFF|UINT64|0x95940007
  ### @endcond

  ### @brief Switch to control if PSP directory is using 16M address
  ### @details Switch to control if PSP directory is using 16M address, default value is TRUE.
  ### SBIOS must override this PCD to FALSE for flat 32M BIOS.
  ### @li TRUE:  Yes
  ### @li FALSE: No
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPspDirUsing16MAddress|TRUE|BOOLEAN|0x9594000B

  ### @brief Time out value in microseconds to wait for PSP mailbox ready
  ### @details Time out value in microseconds to wait for PSP mailbox ready,
  ### and 0xFFFFFFFF means wait infinitely.
  ### @li non 0xFFFFFFFF: Time out value in microseconds
  ### @li 0xFFFFFFFF: Means wait infinitely
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspMboxReadinessTimeout|0xFFFFFFFF|UINT32|0x95940050

  ### @brief Time out value in microseconds to wait for PSP command done
  ### @details Time out value in microseconds to wait for PSP command done,
  ### and 0xFFFFFFFF means wait infinitely.
  ### @li non 0xFFFFFFFF: Time out value in microseconds
  ### @li 0xFFFFFFFF: Means wait infinitely
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspCmdCompletenessTimeout|0xFFFFFFFF|UINT32|0x95940051

  ### @brief Switch to control if BIOS will send BOOT_DONE command to PSP at end of S3 resume
  ### @details Platform should enable this PCD unless any issue is found
  ###          (for example, if PSP tries to enforce FCH late fencing when it receives BOOT DONE command,
  ###           but FCH late fencing is not enabled in the Platform, the system may hang.
  ###           At this case, we can NOT enable this PCD, otherwise we should enable it)
  ### @li TRUE: BIOS will send BOOT_DONE command to PSP at end of S3 resume
  ### @li FALSE: BIOS will NOT send BOOT_DONE command to PSP at end of S3 resume
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspSendBootDoneCmdAtEndOfS3Resume|TRUE|BOOLEAN|0x95940052

  ### @cond !BRH
  ### @brief FFD image offset in BIOS image
  ### @details FFD (System Firmware Feature Description) image is a file requested by OEM/ODM,
  ###   generated by AMD SFFS web portal, and signed by OEM/ODM.
  ###   It's used to describe system firmware feature sets selected by OEM/ODM.
  ###   This file is stored in the common directory in the BIOS image,
  ###   Platform BIOS should specify the FFD image address at build time
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspFfdImageAddress|0xFFFFFFFF|UINT32|0x95940053
  ### @endcond

  ### @cond !BRH
  ### @brief Control if eSPI register 0x00:0F should be fenced off
  ### @details Control if eSPI register 0x00:0F should be fenced off
  ### @li TRUE: BIOS will send command to PSP to fence off eSPI register 0x00:0F
  ### @li FALSE: BIOS will NOT send command to PSP to fence off eSPI register 0x00:0F
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspLockeSPIEnable|FALSE|BOOLEAN|0x95940055
  ### @endcond

  ### @brief Control if Recovery flag should be ignored when sending BIOS command to PSP
  ### @details For BIOS with A/B layout, set it to TRUE,
  ###  BIOS will send command to PSP no matter Recovery flag is set or not.
  ###          For BIOS with legacy layout (no backup partition), set it to FALSE,
  ###  BIOS will send command to PSP only when Recovery flag is NOT set.
  ###  This is to avoid system hang at waiting for PSP response for BIOS with legacy recovery layout.
  ### @li TRUE: BIOS will send command to PSP no matter Recovery flag is set or not
  ### @li FALSE: BIOS will send command to PSP only when Recovery flag is NOT set
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSendPspCommandIgnoreRecoveryFlag|FALSE|BOOLEAN|0x95940056

  ### @cond !BRH
  ### @brief SPI Armor feature enabled or not
  ### @details "SPI Armor" is a required security feature of the AM5 product family
  ###  and must be present on all AM5 motherboard and systems.
  ###  The feature provides a 64K write-protected space in the SPI ROM image used for AMD reserved code and information.
  ###  This space must be write-protected at system board manufacture time and cannot be mutable.
  ### @li TRUE: "SPI Armor" feature is enabled
  ### @li FALSE: "SPI Armor" feature is disabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspSpiArmorEnabled|FALSE|BOOLEAN|0x95940057
  ### @endcond

  ### @cond !BRH
  ### @brief EC absent flag
  ### @details Indicate there is EC controller present on system or not
  ### @li TRUE: EC Absent
  ### @li FALSE: EC Present
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEcAbsent|FALSE|BOOLEAN|0x95940058
  ### @endcond

  ### @cond !BRH
  ### @brief Switch to turn on/off 64K Flash Erase Support when ROM Armor feature is enabled.
  ### @details When set to FALSE, use the default 4K Flash Erase Support and set to TRUE, turn on 64K Erase Support.
  ### @li TRUE:  Enable 64K Flash Erase Support
  ### @li FALSE: Disable 64K Flash Erase Support
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPsp64KFlashEraseSupportForRomArmor|FALSE|BOOLEAN|0x95940059
  ### @endcond

  ### @brief Switch to control if APOB uses SPI MMIO address directly
  ### @details If enabled, BIOS will read from APOB SPI MMIO address directly instead of reading from memory copy
  ### @li TRUE:  BIOS will read from APOB SPI MMIO address directly
  ### @li FALSE: BIOS will copy APOB from SPI MMIO address to memory first, and then read from memory
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspApobUseSpiMmioAddress|FALSE|BOOLEAN|0x9594005A

  ### @cond !BRH
  ### @brief Global System Interrupt Base of AMD NBIO IOAPIC
  ### @details In the APIC model, the number of interrupt inputs supported by each I/O APIC can vary.
  ### OSPM determines the mapping of the Global System Interrupts by determining how many interrupt
  ### inputs each I/O APIC supports and by determining the global system interrupt base for each
  ### I/O APIC as specified by the I/O APIC Structure.
  ### In this example, PCD need to be set as "System Interrupt Base", which is 0x18.
  ###   I/O APIC Structure
  ###   Type  0x01 (1)
  ###   Length  0x0C (12)
  ###   I/O APIC ID 0x21 (33)
  ###   Reserved  0x00 (0)
  ###   I/O APIC Address  0xFEC01000
  ###   System Interrupt Base 0x00000018 (24)
  ### More details, please check ACPI "APIC" Table.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHspNbioApicGsiBase|24|UINT8|0x95940060
  ### @endcond

  ### @brief Apcb Recovery Strategy
  ### @details Different Apcb Recovery Strategy when this PCD has been set to different Value
  ### @li 0x0: System will Reset in APCB Recovery Mode
  ### @li 0x1: System will use Apcb Backup date instead of corrupted Apcb workable data and
  ###          continue to boot in Apcb recovery mode. In this mode, APCB read and write service still works
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdApcbRecoveryStrategy|0x0|UINT8|0x95940061

  ### @cond !BRH
  ### @brief MP2 SRAM SMN Base Address Value for Debug Data Preserve feature
  ### @details Set MP2 SRAM SMN Base Address value for Debug Data Preserve feature
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMp2SramSmnBaseAddress|0x3F00000|UINT32|0x95940062
  ### @endcond

  ### @brief Apcb Recovery Discard Memory Context
  ### @details Switch to control if Discard Memory Context in next boot if no APCB change after Apcb Recovery
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdApcbRecoveryDiscardMemContext|TRUE|BOOLEAN|0x95940063

  ### RAM debug message PCDs
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdIdsDebugPrintRamDebugAddress|0x8A00000|UINT32|0x95940064
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdIdsDebugPrintRamDebugSize|0x1000000|UINT32|0x95940065
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdIdsDebugPrintRamDebugEnable|FALSE|BOOLEAN|0x95940066

  ### Flash Map PCDs
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTempRamBase                       |0x00100000|UINT32|0x95940067
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTempRamSize                       |0x00100000|UINT32|0x95940068
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdBootFvBase                        |0x09B00000|UINT32|0x95940069
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdBootFvSize                        |0x00300000|UINT32|0x9594006A
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFlashMmioBaseAddress              |0xFF000000|UINT32|0x9594006B
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFlashMmioSize                     |0x01000000|UINT32|0x9594006C

## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ##
## ##
## ##                D Y N A M I C   P C D s
## ##
## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ##

[PcdsDynamic]
#----------------------------------------------------------------------------
#    System level Dynamic PCDs
### Set Doxy_path: "PCD-Sys.h"
#----------------------------------------------------------------------------
  ### @cond (AM4BR)
  ### These following PCDs are for Family-15h only
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMemEccEnable|TRUE|BOOLEAN|0x00030001
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMemUmaEnable|TRUE|BOOLEAN|0x00030002
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMemDmiEnable|TRUE|BOOLEAN|0x00030003
  ### @endcond

  ### @brief control PSP to dump RDRAND, RDSEED related information
  ### @li FALSE - Disabled
  ### @li TRUE  - Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDumpRdInstructionInfo|FALSE|BOOLEAN|0x00030007

  ### @cond NOT(BRH)
  ### @brief SMN address for MP_POSTCODE_IP_0
  ### @details This Pcd value only active when PcdStbFilterMaskEnable set to TRUE.
  ### This address need to set specifically for each program.
  ### For combo program, this PCD need to update during RT by checking installed processor.
  ### @li 0x3E30210  RMB | MP::MP2MMU::MP2_POSTCODE_IP_0
  ### @li 0x3B30210  PHX | MP::MP1MMU::MP1_POSTCODE_IP_0
  ### @li 0x3B30210  RPL | MP::MP1MMU::MP1_POSTCODE_IP_0
  ### @li 0x3E30210  MDN | MP::MP2MMU::MP2_POSTCODE_IP_0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMpPostcodeIp0SmnAddress|0x3E30210|UINT32|0x95927208

  ### @brief SMN address for MP_POSTCODE_CONFIG
  ### @details This Pcd value only active when PcdStbFilterMaskEnable set to TRUE.
  ### This address need to set specifically for each program.
  ### For combo program, this PCD need to update during RT by checking installed processor
  ### If target system is using different address, the SMN address need be updated during RT
  ### @li 0x3E3020C  RMB | MP::MP2MMU::MP2_POSTCODE_CONFIG
  ### @li 0x3B3020C  PHX | MP::MP1MMU::MP1_POSTCODE_CONFIG
  ### @li 0x3B3020C  RPL | MP::MP1MMU::MP1_POSTCODE_CONFIG
  ### @li 0x3E3020C  MDN | MP::MP2MMU::MP2_POSTCODE_CONFIG
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMpPostcodeConfigSmnAddress|0x3E3020C|UINT32|0x9592720A

  ### @name Video BIOS load address for FSP PEI GOP driver
  ### @brief This PCD specifies 32-bit Video BIOS load address to be used for FSP PEI GOP driver. This PCD depends on PcdAmdFsp
  ### being set. Coreboot (not AGESA/FSP) will load VBIOS and pass a pointer to it in this PCD to FSP.
  ### @brief Permitted Choices: (Type: Value)(Default: 0)

  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPeiGopVbiosAddr|0x00|UINT32|0x000A6060
  ### @endcond

#----------------------------------------------------------------------------
#-  CCX Dynamic PCDs
### Set Doxy_path: "PCD-CCX.h"
#----------------------------------------------------------------------------

  ### @name General CCX Controls

  ### @brief This element specifies the C State operational mode. This can be used with processors which support deep sleep C
  ### states such as CC6.
  ### @brief Permitted Choices: (Type: List)(Default: 1)
  ### @li 0 - Disabled
  ### @li 1 - Use CState C6
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCStateMode|1|UINT8|0x000CC001

  ### @brief This item specifies a free block of 8 consecutive bytes of I/O ports that can be used to allow the CPU to enter
  ### C-States. This item should always be specified regardless of the C-State mode selected. The value of zero disables
  ### I/O C State transitions.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x0413)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCStateIoBaseAddress|0x413|UINT16|0x000CC002

  ### @cond (ZP||CZ)
  ### @brief Specifies a maximum power limit for the platform. This control is paired with PcdAmdAcpiCpuPerfPresentCap to control
  ### the value of the supported states reported in the ACPI PPC Table. The AGESA software will evaluate the defined P-States for
  ### the APU and trim off the top power states to achieve a P-State set where software P0 falls at or below the indicated power level.
  ### @brief  This value is the integer number, in milliwatts, to be used as the TDP limit.
  ### @brief Permitted Choices: (Type: Value)(Default: 0)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPowerCeiling|0x0|UINT32|0x000CC003
  ### @endcond

  ### @brief This item allows Core Performance Boost (CPB) to be forced to disabled, even if the hardware provides this feature.
  ### By default, CPB is enabled for processors that support it.
  ### @brief Permitted Choices: (Type: List)(Default: CpbModeAuto)
  ### @li CpbModeAuto - processors with CPB will have it enabled.
  ### @li CpbModeDisabled - force CPB to be disabled.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpbMode|0x1|UINT8|0x000CC004

  ### @brief This value provides for advanced performance tuning by controlling the hardware prefetcher setting. Use the
  ### recommended setting for best general performance, but this item can allow for optimizing specific workloads. The settings
  ### below are ordered from all enabled to all disabled and a specific disable setting implies all disable settings above it in
  ### the list as well. For example, disabling the L1 prefetcher implies that hardware prefetcher training on software prefetches
  ### is also disabled.
  ### @brief Permitted Choices: (Type: List)(Default: HARDWARE_PREFETCHER_AUTO)
  ### @li HARDWARE_PREFETCHER_AUTO - Use the recommended setting for the processor. In most cases, the recommended setting is enabled.
  ### @li DISABLE_HW_PREFETCHER_TRAINING_ON_SOFTWARE_PREFECT CHES - Use the recommended setting for the hardware prefetcher, but
  ### disable training on software prefetches.
  ### @li DISABLE_L1_PREFETCHER - Use the recommended settings for the hardware prefetcher, but disable L1 prefetching and above.
  ### @li DISABLE_L2_STRIDE_PREFETCHER - Use the recommended settings for the hardware prefetcher, but disable the L2 stride prefetcher and above.
  ### @li DISABLE_HARDWARE_PREFETCH - Disable hardware prefetching.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHardwarePrefetchMode|0|UINT8|0x000CC005

  ### @brief This value provides for advanced performance tuning by controlling the software prefetch instructions. Use the
  ### recommended setting for best general performance, but this item can allow for optimizing specific workloads.
  ### @brief Permitted Choices: (Type: List)(Default: SOFTWARE_PREFETCHES_AUTO)
  ### @li SOFTWARE_PREFETCHES_AUTO - Use the recommended setting for the processor. In most cases, the recommended setting is enabled.
  ### @li DISABLE_SOFTWARE_PREFETCHES - Disable software prefetches (convert software prefetch instructions to NOP).
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSoftwarePrefetchMode|0|UINT8|0x000CC006

  ### @brief This element selects whether P-States should be forced to be independent, as reported by the ACPI _PSD object.
  ### This setting can improve performance for an OS which supports this feature. Different processors may have different
  ### preferences for this feature.
  ### @brief Permitted Choices: (Type: List)(Default: 0)
  ### @li 0 - PSD is reported as dependent or independent per processor preference.
  ### @li 1 - PSD will report dependent P-State control.
  ### @li 2 - PSD will report independent P-State control.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAgesaPstatePolicy|0|UINT8|0x000CC007

  ### @cond !BRH
  ### @brief The ability to remove one or more cores from operation is supported in the silicon. It may be desirable to reduce
  ### the number of cores due to OS restrictions, or power reduction requirements of the system. This item allows the control
  ### of how many cores are running. This setting can only reduce the number of cores from those available in the processor.
  ### If the selection specifies more cores than are available in the processor, the setting is ignored.
  ### @brief Each core complex (a.k.a. Compute Unit) in the processor has two or more cores, each running 2 or more threads.
  ### The down-core option may be involved in two different ways:
  ### @brief In the option list, if the mode is not indicated, the default mode of operation is 'By_Core' reducing the number
  ### of Compute Units per complex first.
  ### @brief As new families of processors are introduced, the core architecture changes such that the option selection for this
  ### control changes to match the architecture. Please note the family-Model variations below.
  ### @brief Permitted Choices [F19MA0]: (Type: List)(Default: AUTO) The '(x+y)' notation indicates the number of cores to remain
  ### in each CCX. This is applied symmetrically to all sockets present. Available options are: AUTO, TWO (1 + 1), FOUR (2 + 2),
  ### SIX (3 + 3), EIGHT (4 + 4), TEN (5 + 5), TWELVE (6 + 6), FOURTEEN (7 + 7), ELEVEN (6 + 5).
  ### @brief Permitted Choices [F19M11]: (Type: List)(Default: AUTO) The '(x+y)' notation indicates the number of cores to remain
  ### in each CCX. This is applied symmetrically to all sockets present. Available options are: ONE (1 + 0), TWO (2 + 0),
  ### THREE (3 + 0), FOUR (4 + 0), FIVE (5 + 0), SIX (6 + 0), SEVEN (7 + 0).
  ### @brief Permitted Choices [F19M00]: (Type: List)(Default: AUTO) The '(x+y)' notation indicates the number of cores to remain
  ### in each CCX present. This is applied symmetrically to all sockets present.
  ### @brief Permitted Choices [F17M30]: (Type: List)(Default: AUTO) The '(x+y)' notation indicates the number of cores to remain
  ### in each CCX. This is applied symmetrically to all sockets present.
  ### @li AUTO: CCX_DOWN_CORE_AUTO - No down coring. (All cores are enabled).
  ### @li ONE (1 + 0): CCX_DOWN_CORE_1_0 - One core remain, 1 on CCX0
  ### @li TWO (2 + 0): CCX_DOWN_CORE_2_0 - Two cores remain, 2 on CCX0
  ### @li THREE (3 + 0): CCX_DOWN_CORE_3_0 - Three cores remain, 3 on CCX0
  ### @li FOUR (4 + 0): CCX_DOWN_CORE_4_0 - Four cores remain, 4 on CCX0
  ### @li FIVE (5 + 0): CCX_DOWN_CORE_5_0 - Five cores remain, 5 on CCX0
  ### @li SIX (6 + 0): CCX_DOWN_CORE_6_0 - Six cores remain, 6 on CCX0
  ### @li SEVEN (7 + 0): CCX_DOWN_CORE_7_0 - Seven cores remain, 7 on CCX0
  ### @li TWO (1 + 1): CCX_DOWN_CORE_1_1 - Two cores remain after down coring, one in each CCX.
  ### @li FOUR (2 + 2): CCX_DOWN_CORE_2_2 - Four cores remain, two in each CCX.
  ### @li SIX (3 + 3): CCX_DOWN_CORE_3_3 - Six cores remain, 3 in each CCX.
  ### @li EIGHT (4 + 4): CCX_DOWN_CORE_4_4 - Eight cores remain, 4 in each CCX.
  ### @li TEN (5 + 5): CCX_DOWN_CORE_5_5 - Ten cores remain, 5 in each CCX.
  ### @li TWELVE (6 + 6): CCX_DOWN_CORE_6_6 - Twelve cores remain, 6 in each CCX.
  ### @li FOURTEEN (7 + 7): CCX_DOWN_CORE_7_7 - Fourteen cores remain, 7 in each CCX.
  ### @li ELEVEN (6 + 5): CCX_DOWN_CORE_6_5 - Eleven cores remain, 6 in CCX0 and 5 in CCX1.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDownCoreMode|0|UINT8|0x000CC008
  ### @endcond

  ### @cond !BRH
  ### @brief This item selects the Symmetric Multi-Threading (SMT) mode of the Compute Units.
  ### @brief APCB_TOKEN_UID_CCX_SMT_CTRL should be set the same as this.
  ### @brief This token works only when SMT is supported by the SOC.
  ### @brief Permitted Choices: (Type: List)(Default: SMT_Auto)
  ### @li SMT_Disabled - the compute units are restricted to a single thread.
  ### @li SMT_Auto - the compute unit use all available threads.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmtMode|0x1|UINT8|0x000CC009
  ### @endcond

  ### @cond !BRH
  ### @brief This item is used to specify the number of CCDs that are desired to be enable in the system. Acceptable values are
  ### for this PCD are 0x01 through the max number available in the processor.
  ### @brief Permitted Values: (Type: Value)(Default: Auto)
  ### @li Auto(0x00) - The maximum CCDs provided by the processor will be enabled.
  ### @li N >= 1 - The exact number of CCDs to be enabled in the system. If N is < Max for the processor, the enabled CCDs will
  ### be the lowest order contiguous CCDs (#0..N).
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcdMode|0|UINT8|0x000CC00A
  ### @endcond

#----------------------------------------------------------------------------
#-  CCX ACPI Dynamic PCDs
### Set Doxy_path: "PCD-CCX-ACPI.h"
#----------------------------------------------------------------------------
  ### @brief The ACPI SSDT table entry can be included or skipped in the output generation phase. This item specifies the
  ### platform preference for the production of this table.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE - This table entry will be produced.
  ### @li FALSE - This table entry is skipped.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuSsdt|TRUE|BOOLEAN|0x000CC00B
  ### @brief The ACPI _PCT table entry can be included or skipped in the output generation phase. This item specifies the
  ### platform preference for the production of this table.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE - This table entry will be produced.
  ### @li FALSE - This table entry is skipped.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuSsdtPct|TRUE|BOOLEAN|0x000CC00C
  ### @brief The ACPI _PSS table entry can be included or skipped in the output generation phase. This item specifies the
  ### platform preference for the production of this table.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE - This table entry will be produced.
  ### @li FALSE - This table entry is skipped.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuSsdtPss|TRUE|BOOLEAN|0x000CC00D
  ### @brief The ACPI XPSS table entry can be included or skipped in the output generation phase. This item specifies the
  ### platform preference for the production of this table.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE - This table entry will be produced.
  ### @li FALSE - This table entry is skipped.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuSsdtXpss|TRUE|BOOLEAN|0x000CC00E
  ### @brief The ACPI _PSD table entry can be included or skipped in the output generation phase. This item specifies the
  ### platform preference for the production of this table.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE - This table entry will be produced.
  ### @li FALSE - This table entry is skipped.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuSsdtPsd|TRUE|BOOLEAN|0x000CC00F
  ### @brief The ACPI _PPC table entry can be included or skipped in the output generation phase. This item specifies the
  ### platform preference for the production of this table.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE - This table entry will be produced.
  ### @li FALSE - This table entry is skipped.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuSsdtPpc|TRUE|BOOLEAN|0x000CC010
  ### @brief The ACPI _CST table entry can optionally include describing the C1 state via the monitor/mwait mechanism in addition
  ### to the C2 state in the output generation phase. This item specifies the platform preference for the inclusion of this C1
  ### state which has been shown to increase processor performance in certain operating systems.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE -This table entry will be produced if the hardware supports the feature.
  ### @li FALSE - This table entry is skipped.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCstC1|TRUE|BOOLEAN|0x000CC011
  ### @brief The ACPI _CST table entry can optionally include describing the C3 state via the monitor/mwait mechanism in the
  ### output generation phase. This item specifies the platform preference for the inclusion of this C3 state.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li TRUE -This table entry will be produced if the hardware supports the feature.
  ### @li FALSE - This table entry is skipped.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCstC3|FALSE|BOOLEAN|0x000CC012

  ### @brief The ACPI CRAT table entry can be included or skipped in the output generation phase. This item specifies the
  ### platform preference for the production of this table.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li TRUE - This table entry will be produced.
  ### @li FALSE - This table entry is skipped.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCrat|FALSE|BOOLEAN|0x000CC013
  ### @brief The ACPI CDIT table entry can be included or skipped in the output generation phase. This item specifies the
  ### platform preference for the production of this table.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li TRUE - This table entry will be produced.
  ### @li FALSE - This table entry is skipped.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCdit|FALSE|BOOLEAN|0x000CC014
  ### @brief The ACPI WHEA table entry can be included or skipped in the output generation phase. This item specifies the
  ### platform preference for the production of this table.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE - This table entry will be produced.
  ### @li FALSE - This table entry is skipped.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiWhea|TRUE|BOOLEAN|0x000CC015
  ### @brief The ACPI SRAT table entry can be included or skipped in the output generation phase. This item specifies the
  ### platform preference for the production of this table.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE - This table entry will be produced.
  ### @li FALSE - This table entry is skipped.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiSrat|TRUE|BOOLEAN|0x000CC016
  ### @brief The ACPI SLIT table entry can be included or skipped in the output generation phase. This item specifies the
  ### platform preference for the production of this table.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE - This table entry will be produced.
  ### @li FALSE - This table entry is skipped.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiSlit|TRUE|BOOLEAN|0x000CC017
  ### @brief The DMI table entry can be included or skipped in the output generation phase. This item specifies the platform
  ### preference for the production of this table.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE - This table entry will be produced.
  ### @li FALSE - This table entry is skipped.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAgesaDmi|TRUE|BOOLEAN|0x000CC018
  ### @brief The ACPI HMAT table entry can be included or skipped in the output generation phase. This item specifies the platform
  ### preference for the production of this table.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE - This table entry will be produced.
  ### @li FALSE - This table entry is skipped.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiHmat|TRUE|BOOLEAN|0x000CC019
  ### @brief The ACPI BDAT table entry can be included or skipped in the output generation phase. This item specifies the platform preference for the production of this table.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE - This table entry will be produced.
  ### @li FALSE - This table entry is skipped.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiBdat|FALSE|BOOLEAN|0x000CC01E
  ### @brief The base address of BDAT table. A Value of 0xFFFFFFFFFFFFFFFF means that APCB_TOKEN_UID_BDAT_SUPPORT is disable.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdBdatAddress|0xFFFFFFFFFFFFFFFF|UINT64|0x000CC01F

  ## Dynamic PCD used to indicate whether or not the current config supports S3
  # @li TRUE - S3 can be enabled
  # @li FALSE - S3 support should be disabled
  ### @brief This PCD is used to signal the system BIOS whether or not the installed processor in its current configuration can
  ### support the ACPI S3 sleep state. The PCD is declared at build time in a .DEC file. The CCX driver analyzes the current
  ### configuration during PEI phase and will reset this PCD to =FALSE if S3 cannot be supported. If S3 can be supported then
  ### the PCD is left in its original state.
  ### @brief The system BIOS should add this PCD to its criteria to determine whether or not it should enable the S3 feature.
  ### That evaluation is expected to occur in the DXE phase (after the EFI_ACPI_TABLE_PROTOCOL has been published).
  ### @li FALSE - The system should not be allowed to enter the S3 sleep state. The system BIOS should not declare the \_S3
  ### object to the operating system. Either the platform decided to not support S3 (set =FALSE in .DEC file) or the CCX driver
  ### determined the present configuration is not able to support the feature.
  ### @li TRUE - S3 is supported in the current configuration. The platform set the PCD to =TRUE in the .DEC file and it was not
  ### modified by the CCX driver.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiS3Support|TRUE|BOOLEAN|0x000CC01A

  ### @cond !BRH
  ### @brief When memory context restore feature is enabled, PMU training can be skipped during Down-Core warm reset if DIMM configuration is not changed.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE - Skip PMU training for Down-Core warm reset if DIMM configuration is not changed.
  ### @li FALSE - Always do PMU training for Down-Core warm reset.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSkipPmuTrainingForDownCore|TRUE|BOOLEAN|0x000CC01B
  ### @endcond

  ### @cond (STX)
  ### @brief The ACPI _CST table entry can optionally include describing the C4 state via the monitor/mwait mechanism in the output generation phase. This item specifies the platform preference for the inclusion of this C4 state.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li TRUE -This table entry will be produced if the hardware supports the feature.
  ### @li FALSE - This table entry is skipped.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCstC4|FALSE|BOOLEAN|0x000CC01C
  ### @endcond

#----------------------------------------------------------------------------
#-  CCX Dynamic PCDs
### Set Doxy_path: "PCD-CCX.h"
#----------------------------------------------------------------------------
  ### @cond (BRH)
  ### @brief This control selects the Apic mode to be used. xAPIC mode supports upto 255 threads; x2 APIC mode supports much more than 255 threads. Note: in order to support x2 Apic mode, IOMMU must be enabled.
  ### @brief APCB_TOKEN_UID_APIC_MODE should be set the same as this.
  ### @brief Permitted Choices: (Type: Value)(Default: Auto)
  ### @li Auto (0xFF) - x2ApicMode will be chosen, unless IOMMU is disabled, in which case xApic must be used
  ### @li CompatibilityMode (0x00) - threads with APIC ID below 255 run in xAPIC with xAPIC ACPI structures and threads with APIC ID 255 and above run in x2 mode with x2 ACPI structures.
  ### @li xApicMode (0x01) - force legacy xApic mode. Note: If the system contains more than 255 active threads, then a sufficient number of threads will be disabled to allow xApic mode.
  ### @li x2ApicMode (0x02) - force x2Apic mode independent of thread count.
  ### @see PcdCfgIommuSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdApicMode|0xFF|UINT8|0x000CC01D
  ### @endcond

  ### @cond (BRH || RS)
  ### @brief Enable Requested Cpu minimum frequency
  ### @details This allows PcdAmdCpuReqMinFreq to specify the CPPC requested minimum performance frequency
  ### @li TRUE - Enabled
  ### @li FALSE - Disabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuReqMinFreqEn|FALSE|BOOLEAN|0x000CC020
  ### @endcond

  ### @cond (BRH || RS)
  ### @brief Requested Cpu minimum frequency
  ### @details This value specifies the CPPC requested minimum performance frequency to apply to all cores (MHz)
  ### @details This Pcd value only active when PcdAmdCpuReqMinFreqEn set to TRUE.
  ### @li upto 0xFFFF : CPPC requested minimum performance frequency in MHz. This will be limited/capped by the fused part minumum/maximum frequency.
  ### Limits should be within the part's operational range.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuReqMinFreq|0x0|UINT16|0x000CC021
  ### @endcond

  ### @brief This selects the state of the Fast Short Repeat Move String (FSRM) instruction capability in the processor. Generally, this should be left in its default state. See notes below and with the ERMS feature.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li TRUE -The FSRM capability will be reflected in CPUID_Fn00000007_EDX_x00[4]. Note: FSRM is a new feature in [F19M00] and some OS implementations have a requirement that ERMS also be enabled when FSRM is enabled.
  ### @li FALSE - This instruction capability is disabled.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdEnableFSRM|FALSE|BOOLEAN|0x000CC02B                   # Fast Short REP MOVSB (For Zen3, Zen4 and Zen5)

  ### @brief This selects the state of the Enhanced Repeat Move String (ERMS) instruction capability in the processor. Generally, this should be left in its default state. See notes below and with the FSRM feature.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li TRUE -The ERMS capability will be reflected in CPUID_Fn00000007_EBX_x00[9].
  ### @li FALSE - This instruction capability is disabled. Note: The FSRM=1 with ERMS=0 combination has been seen to cause a no-boot condition with some OS versions.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdEnableERMS|FALSE|BOOLEAN|0x000CC02C                   # Enhanced REP MOVSB/STOSB (For Zen3, Zen4, and Zen5)

  ### @brief This selects the state of the REP-MOV/STOS Streaming instruction capability in the processor. Generally, this should be left in its default state.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li FALSE - streaming not used by MOVSB/STOSB.
  ### @li TRUE - allows REP-MOVS/STOS to use non-caching streaming stores for large sizes.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdEnableRMSS|FALSE|BOOLEAN|0x000CC02F                   # REP-MOV/STOS Streaming (For Zen3, Zen4, and Zen5)

  ### @brief AVX512
  ### @details This allows users to enable or disable software/OS visibility to AVX512 capability.
  ### @brief Permitted Choices: (Type: Value)(Default: Auto)
  ### @li Auto (0xFF) - Default AVX512 settings.
  ### @li Disabled (0x00) - AVX512 Disabled Mode.
  ### @li Enabled (0x01) - AVX512 Enabled Mode.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxEnableAvx512|0xFF|UINT8|0x000CC033

  ### @brief ERMSB Caching Behavior
  ### @details This supports NV feature DisFstStrErmsb (Disable ERMSB Caching Behavior).
  ### @brief Permitted Choices: (Type: Value)(Default: Auto)
  ### @li Auto (0xFF) - Default ERMSB Caching Behavior settings.
  ### @li Disabled (0x01) - Enable Optimized caching for REPs.
  ### @li Enabled (0x00) - Disable Legacy caching behavior for REPs.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxDisFstStrErmsb|0xFF|UINT8|0x000CC036

  ### @cond (GN)
  ### @brief This setting can be used to reduce high failure rate of DE-HWA on some workloads. Generally, this should be left in its default state, as it can negatively impact performance.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li FALSE - has no effect
  ### @li TRUE - override MSR_C001_1021[60:59]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdICCfgDisIT01Byp|FALSE|BOOLEAN|0x000CC03B
  ### @endcond

  ### @cond (BRH)
  ### @brief AMD_ERMSB Reporting
  ### @details Report presence of AMD_ERMSB. By default, this is reported as true (Enable), the field can be set to false for analysis purposes as long as OS supports it.
  ### @brief Permitted Choices: (Type: UINT8)(Default: 0xFF)
  ### @li Auto (0xFF) - Default AMD_ERMSB Reporting settings.
  ### @li TRUE - Report presence of AMD_ERMSB.
  ### @li FALSE - Do not report presence of AMD_ERMSB.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxErmsbRepo|0xFF|UINT8|0x000CC03E
  ### @endcond

#----------------------------------------------------------------------------
#-  ACPI Dynamic PCDs
### Set Doxy_path: "PCD-CCX.h"
#----------------------------------------------------------------------------

  ### @brief Enable CPU WDT
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li FALSE - The CPU WDT is disabled.
  ### @li TRUE - The CPU WDT is running.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuWdtEn|TRUE|BOOLEAN|0x000CC200

  ### @brief CPU WDT Timout vaue
  ### <p> '0xFFFF' means Auto which will use silicon reset value
  ### <p> upper 8 bits - CpuWdtCountSel
  ### @li 0      - 4095
  ### @li 1      - 2047
  ### @li 2      - 1023
  ### @li 3      - 511
  ### @li 4      - 255
  ### @li 5      - 127
  ### @li 6      - 63
  ### @li 7      - 31
  ### @li 8      - 8191
  ### @li 9      - 16383
  ### @li Others - Reserved
  ### </p>
  ### <p>
  ### lower 8 bits - CpuWdtTimeBase
  ### @li 00     - 1.31ms
  ### @li 01     - 1.28us
  ### @li Others - Reserved
  ### </p>
  ### <p>
  ### @attention RESTRICTION: When both CPU WDT & DF WDT are enable, the CPU WDT timeout must be greater than or equal to the DF
  ### CCM WDT timeout limit
  ### </p>
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuWdtTimeout|0xFFFF|UINT16|0x000CC201

  ### @brief Enable L1 Stream HW Prefetcher
  ### @brief See the section above for discussion on prefetchers.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE - This prefetcher is running.
  ### @li FALSE - The prefetcher is disabled.
  ### @see PcdAmdHardwarePrefetchMode, PcdAmdSoftwarePrefetchMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL1StreamPrefetcher|TRUE|BOOLEAN|0x000CC202

  ### @brief Enable L2 Stream HW Prefetcher
  ### @see PcdAmdL1StreamPrefetcher
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL2StreamPrefetcher|TRUE|BOOLEAN|0x000CC203

#----------------------------------------------------------------------------
#-  CCX ACPI Dynamic PCDs
### Set Doxy_path: "PCD-CCX-ACPI.h"
#----------------------------------------------------------------------------

  ### @cond (ZP||CZ)
  ### @brief Specifies the value of the highest power P-state to be listed in the ACPI PPC Table. This control is paired with,
  ### and simultaneously evaluated with PcdAmdPowerCeiling. It is limited by the value of PcdAmdPowerCeiling and the lowest power
  ### enabled P-state on the system. This control allows the user to separately restrict the PPC entries below what would be set
  ### by the PcdAmdPowerCeiling value.
  ### @brief This value is the P-State number to be used as the highest performance P-State as reported in the ACPI PPC table.
  ### This is a software P-State number as originally defined by the APU (e.g. before the power ceiling algorithm is applied).
  ### @brief Permitted Choices: (Type: Value)(Default: 0xFF)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuPerfPresentCap|0xFF|UINT8|0x000CC300
  ### @endcond
  ### @brief Enable AGESA to generate ACPI MCST table.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li FALSE - The MCST table is not generated.
  ### @li TRUE - The MCST table is generated.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiMsct|TRUE|BOOLEAN|0x000CC301

#----------------------------------------------------------------------------
#-  CCX Dynamic PCDs
### Set Doxy_path: "PCD-CCX.h"
#----------------------------------------------------------------------------

  ### @brief Enable force Locks to only schedule non-speculative
  ### @cond (SSP)
  # for ZP-SP3 only
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdScnLockNEnable|FALSE|BOOLEAN|0x000CC400
  ### @endcond

#----------------------------------------------------------------------------
#-  CCX ACPI Dynamic PCDs
### Set Doxy_path: "PCD-CCX-ACPI.h"
#----------------------------------------------------------------------------

  ### @brief ACPI C-Sate C1 latency value.
  # default is 1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuCstC1Latency|0x00000001|UINT16|0x000CC500
  ### @brief ACPI C-Sate C2 latency value.
  # default is 18
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuCstC2Latency|0x00000012|UINT16|0x000CC501
  ### @brief ACPI C-Sate C3 latency value.
  # default is 350
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuCstC3Latency|0x0000015E|UINT16|0x000CC502
  ### @brief ACPI C-Sate C1 Low Power State (LPI) Min Residency value.
  ### @details Minimum Residency - time in microseconds after which a state becomes more energy efficient than any shallower state.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuLpiC1MinRes|0x00000000|UINT32|0x000CC503   # RV. 0
  ### @brief ACPI C-Sate C2 Low Power State (LPI) Min Residency value.
  ### @details Minimum Residency - time in microseconds after which a state becomes more energy efficient than any shallower state.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuLpiC2MinRes|0x0000003C|UINT32|0x000CC504   # RV. 60
  ### @brief ACPI C-Sate C3 Low Power State (LPI) Min Residency value.
  ### @details Minimum Residency - time in microseconds after which a state becomes more energy efficient than any shallower state.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuLpiC3MinRes|0x000003E8|UINT32|0x000CC505   # RV. 1000
  ### @brief ACPI C-Sate C1 Low Power State (LPI) Worst case wakeup latency value.
  ### @details Worst case time in microseconds from a wake interrupt being asserted to the return to a running state of the
  ### owning hierarchy node (processor or processor container).
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuLpiC1WorstCaseWakeupLatency|0x00000001|UINT32|0x000CC506 # RV. 1
  ### @brief ACPI C-Sate C2 Low Power State (LPI) Worst case wakeup latency value.
  ### @details Worst case time in microseconds from a wake interrupt being asserted to the return to a running state of the
  ### owning hierarchy node (processor or processor container).
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuLpiC2WorstCaseWakeupLatency|0x00000012|UINT32|0x000CC507 # RV. 18
  ### @brief ACPI C-Sate C3 Low Power State (LPI) Worst case wakeup latency value.
  ### @details Worst case time in microseconds from a wake interrupt being asserted to the return to a running state of the
  ### owning hierarchy node (processor or processor container).
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuLpiC3WorstCaseWakeupLatency|0x0000015E|UINT32|0x000CC508 # RV. 350
  ### @brief ACPI C3 control.
  ### @li 0 - Disable
  ### @li 1 - CST
  ### @li 2 - LPI with CST
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiC3Ctrl|0x00|UINT8|0x000CC509

  ### @brief Enable AGESA to generate ACPI PCCT table.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li FALSE - The PCCT table is not generated.
  ### @li TRUE - The PCCT table is generated.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiPcct|TRUE|BOOLEAN|0x000CC50A
  ### @brief Use a container to put CPU SSDT location.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li TRUE - Processor scope in _SB container. ex. _SB.PLTF ACPI 6 required.
  ### @li FALSE - Processor scope not in _SB container. Determined by PcdAmdAcpiCpuSsdtProcessorScopeInSb.
  ### @see PcdAmdAcpiCpuSsdtProcessorScopeInSb
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuSsdtProcessorContainerInSb|FALSE|BOOLEAN|0x000CC50B
  ### @brief Processor container name in _SB scope. Must be 4 bytes. ACPI 6 required.
  ### @brief Used if PcdAmdAcpiCpuSsdtProcessorContainerInSb is TRUE.
  ### @see PcdAmdAcpiCpuSsdtProcessorContainerInSb
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuSsdtProcessorContainerName|"PLTF"|VOID*|0x000CC50C
  ### @brief Parent state for LPI C2 state in processor container.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuSsdtProcessorLpiC2ParentState|0x00|UINT8|0x000CC50D
  ### @brief Parent state for LPI C3 state in processor container.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuSsdtProcessorLpiC3ParentState|0x01|UINT8|0x000CC50E

  ### @brief ACPI C-Sate C4 latency value.
  ### @cond (STX)
  # default is 0 (Disabled)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuCstC4Latency|0x00000000|UINT16|0x000CC50F
  ### @endcond
  ### @brief ACPI C-Sate C4 Low Power State (LPI) Min Residency value.
  ### @details Minimum Residency - time in microseconds after which a state becomes more energy efficient than any shallower state.
  ### @cond (STX)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuLpiC4MinRes|0x000003E8|UINT32|0x000CC510
  ### @endcond
  ### @brief ACPI C-Sate C4 Low Power State (LPI) Worst case wakeup latency value.
  ### @details Worst case time in microseconds from a wake interrupt being asserted to the return to a running state of the owning hierarchy node (processor or processor container).
  ### @cond (STX)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuLpiC4WorstCaseWakeupLatency|0x0000015E|UINT32|0x000CC511
  ### @endcond
  ### @brief Parent state for LPI C4 state in processor container.
  ### @cond (STX)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuSsdtProcessorLpiC4ParentState|0x01|UINT8|0x000CC512
  ### @endcond
#----------------------------------------------------------------------------
#-  CCX Dynamic PCDs
### Set Doxy_path: "PCD-CCX.h"
#----------------------------------------------------------------------------
  ### @brief CC6 Control
  ### @details This value controls whether AGESA will or disable CC6 support in the CPU Cores
  ### @li 0 - Disable CC6
  ### @li 1 - Enable CC6 at core side
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCc6Ctrl|1|UINT8|0x000CC600

  ### @brief This PCD enabes Platform First Error Handling. Please see the PPR and RAS references for more details.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxCfgPFEHEnable|FALSE|BOOLEAN|0x000CC601

  ### @brief This control selects the operating mode of the Nested Paging (SNP) Memory and the Reverse MaP table (RMP).
  ### @brief The reason to set PcdAmdSnpMemCover manually is for performance. If pages are not covered by the RMP, then those
  ### additional hardware checks on each page are not performed. So a user could just cover the amount of memory needed for a VM
  ### by the RMP and make the rest unprotected/not covered pages. The RMP is used to ensure a one-to-one mapping between system
  ### physical addresses and guest physical addresses.
  ### @brief Permitted Choices: (Type: Value)(Default: 0)
  ### @li 0 - Disabled.
  ### @li 1 - Enabled (ENTIRE system memory).
  ### @li 2 - Custom.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSnpMemCover|0|UINT8|0x000CC602

  ### @brief Specifies the SNP size of system memory (size in MB) to be covered by the RMP. This control applies only if
  ### PcdAmdSnpMemCover is set for 'Custom'.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x0000_0000)(force to 16MB)
  ### @li 0x10 .. 0x100000 - the size of memory, in MB, to be covered by the RMP. This number is described in hex.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSnpMemSize|0|UINT32|0x000CC603

  ### @brief Secure Memory Encryption Extension (SMEE). TSME basically controls DataScrambleEn in the UMC. This is a hardware
  ### feature. SMEE which is also know as 'Secure Memory Encryption Extension' enables the privileged software to select
  ### individual memory pages for runtime encryption. Pages marked encrypted are automatically decrypted by hardware when read
  ### and encrypted when written to DRAM. Both SMEE and HMKEE cannot be enabled together. For more information, see the Memory
  ### Encryption white papers and PPR description for MSR:SYS_CFG[23].
  ### @brief Permitted Choices: (Type: Boolean) (Default: FALSE)
  ### @li FALSE - the feature is disabled.
  ### @li TRUE - the SMEE feature is enabled.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmee|FALSE|BOOLEAN|0x000CC604

  ### @brief Enable L1 Stride Prefetcher
  ### @details This PCD enables L1 Stride Prefetcher
  ### @li 0 - Disabled
  ### @li 1 - Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL1StridePrefetcher|TRUE|BOOLEAN|0x000CC605

  ### @brief Enable L1 Region Prefetcher
  ### @details This PCD enables L1 Region Prefetcher
  ### @li 0 - Disabled
  ### @li 1 - Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL1RegionPrefetcher|TRUE|BOOLEAN|0x000CC606

  ### @brief Enable L2 Up/Down Prefetcher
  ### @details This PCD enables L2 Up/Down Prefetcher
  ### @li 0 - Disabled
  ### @li 1 - Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL2UpDownPrefetcher|TRUE|BOOLEAN|0x000CC607

  ### @brief Enable Statistical Correct Predictor (SCP)
  ### @details This PCD enables Statistical Correct Predictor (SCP). Enabling for branch heavy codes may reduce
  ### condidtional branch mispredicts
  ### @li 0 - Disabled
  ### @li 1 - Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdStatisticalCorrectPredictor|FALSE|BOOLEAN|0x000CC608

  ### @brief This control selects if the RMP Table is allocated at the End of Physical DRAM, or split in Memory across Sockets. This control applies only if PcdAmdSnpMemCover is set for 'Custom'.
  ### @brief Permitted Choices: (Type: Value)(Default: 0)
  ### @li 0 - Disabled
  ### @li 1 - Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSplitRmpTable|0|UINT8|0x000CC609

  ### @brief This is a default Segment Size (as power of 2) to be used to split memory ranges into segments, when Segmented RMP is enabled.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x24 -> 64GB)
  ### @li All expressed as Power of 2, MinSegmentSize (equivalent to 64GB) <= PcdAmdRmpSegSize <= MaxSegmentSize (equivalent to 4TB)
  ### @li Default 0x24 If not covering the entire range, optimal Segment Size determined at runtime, based on available total system memory
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdRmpSegSize|0x24|UINT8|0x000CC60A

  ### @brief  This is a setting to Enable / Disable Segmented RMP Feature
  ### @brief Permitted Choices: (Type: Boolean) (Default: FALSE)
  ### @li TRUE: Segmented RMP Enabled
  ### @li FALSE: Segmented RMP Disabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSegmentedRmp|FALSE|BOOLEAN|0x000CC60B

  ### @brief  This is a setting to Enable / Disable RMP Coverage for 64Bit MMIO above 4GB
  ### @brief Permitted Choices: (Type: Boolean) (Default: FALSE)
  ### @li TRUE: 64Bit MMIO Above 4GB is covered by RMP
  ### @li FALSE: 64Bit MMIO Above 4GB is not covered by RMP
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdRmpCover64BitMMIORanges|FALSE|BOOLEAN|0x000CC60C

  ### @brief Mask for Socket 0 RootBridges to cover 64Bit MMIO above 4GB
  ### @brief Permitted Choices: (Type: Value) (Default: 0x01)
  ### @li Each bit represents 1 RB 64Bit MMIO range
  ### @li Bit0=RB0, Bit1=RB1, Bit2=RB2.........Bit7=RB7
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdRmp64BitMmioS0RbMask|0x0001|UINT16|0x000CC60D

  ### @brief Mask for Socket 1 RootBridges to cover 64Bit MMIO above 4GB
  ### @brief Permitted Choices: (Type: Value) (Default: 0x00)
  ### @li Each bit represents 1 RB 64Bit MMIO range
  ### @li Bit0=RB0, Bit1=RB1, Bit2=RB2.........Bit7=RB7
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdRmp64BitMmioS1RbMask|0x0000|UINT16|0x000CC60E

  ### @brief New 64Bit MMIO Limit when Segmented RMP is enabled.
  ### @brief Permitted Choices: (Type: Value) (Default: 0x1FFFFFFFFFF)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdRmpEnMmioAbove4GLimit|0x1FFFFFFFFFF|UINT64|0x000CC60F

#----------------------------------------------------------------------------
#-  CCX SMBIOS Dynamic PCDs
### Set Doxy_path: "PCD-CCX-SMBIOS.h"
#----------------------------------------------------------------------------
  ### @brief PCD to set SmbiosTableType16->ExtendedMaximumCapacity
  ### @details PCD is set to 4TB or 8TB based on the No of Dimms per channel
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosT16ExtMaximumCapacity|0x0000040000000000|UINT64|0x000CC700

#----------------------------------------------------------------------------
#-  CCX Dynamic PCDs
### Set Doxy_path: "PCD-CCX.h"
#----------------------------------------------------------------------------

  ### @brief Enable HMKEE(Host Multi Key Encryption Enable)
  ### @details This PCD enables HMKEE, it is disabled by default. Both SMEE and HMKEE cannot be enabled together. For more
  ### information, see the Memory Encryption white papers and PPR description for MSR:SYS_CFG[26].
  ### @li 0 - Disabled
  ### @li 1 - Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHmkee|FALSE|BOOLEAN|0x000CC800

  ### @brief PCD to enable transparent error logging
  ### @details PCD is set to TRUE to enable logging or FALSE to disable logging
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdTransparentErrorLoggingEnable|FALSE|BOOLEAN|0x000CC801

  ### @brief The MONITOR, MWAIT, MONITORX, and MWAITX opcodes become invalid, when Enabled.
  ### @li 0 - Disabled
  ### @li 1 - Enabled
  ### @li 0xFF - Auto
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMonMwaitDis|0xFF|UINT8|0x000CC802

  ### @brief CPU Speculative Store Modes
  ### @li 0xFF - Auto
  ### @li 0 - Balanced: Store instructions may delay sending out their invalidations to remote cacheline
  ### copies when the cacheline is present but not in a writable state in the local cache.
  ### @li 1 - More Speculative: Store instructions will send out invalidations to remote cacheline copies as soon as possible.
  ### @li 2 - Less Speculative: Store instructions may delay sending out their invalidations to remote cacheline copies when the
  ### cacheline is not present in the local cache or not in a writable state in the local cache.
  ### @cond INT
  ### @li 3 - Aggressive
  ### @endcond
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuSpeculativeStoreMode|0xFF|UINT8|0x000CC803

  ### @brief PCD to control number of cycles a thread will be idle after PAUSE instruction.
  ### @brief Permitted Choices: (Type: List)(Default: CPU_PAUSE_DELAY_AUTO)
  ### @li CPU_PAUSE_DELAY_DISABLE   - PAUSE instruction will not stall its thread's dispatch.
  ### @li CPU_PAUSE_DELAY_16CYCLES  - 16 Cycles.
  ### @li CPU_PAUSE_DELAY_32CYCLES  - 32 Cycles.
  ### @li CPU_PAUSE_DELAY_64CYCLES  - 64 Cycles.
  ### @li CPU_PAUSE_DELAY_128CYCLES - 128 Cycles.
  ### @li CPU_PAUSE_DELAY_AUTO      - Use the recommended setting for the processor.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuPauseDelay|0xFF|UINT8|0x000CC804

  ### @brief Enable L1 Burst Prefetch Mode
  ### @details This PCD enables L1 Burst Prefetch Mode
  ### @li 0 - Disabled
  ### @li 1 - Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL1BurstPrefetch|TRUE|BOOLEAN|0x000CC805

  ### @brief PCD to control number of cycles dispatch stalled for a thread after dispatching PAUSE instruction.
  ### @brief Permitted Choices: (Type: List)(Default: CPU_PAUSECNTSEL_1_0_AUTO)
  ### @li CPU_PAUSECNTSEL_1_0_16CYCLES  - 16 Cycles.
  ### @li CPU_PAUSECNTSEL_1_0_32CYCLES  - 32 Cycles.
  ### @li CPU_PAUSECNTSEL_1_0_64CYCLES  - 64 Cycles.
  ### @li CPU_PAUSECNTSEL_1_0_128CYCLES - 128 Cycles.
  ### @li CPU_PAUSECNTSEL_1_0_AUTO      - Use the recommended setting for the processor.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuPauseCntSel_1_0|0xFF|UINT8|0x000CC806

  ### @brief Disable to use fixed L2 replacement/allocation policy
  ### @details This PCD enables Adaptive Allocation (AA)
  ### @li 0 - Enabled
  ### @li 1 - Disabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuAdaptiveAlloc|0xFF|UINT8|0x000CC807

#----------------------------------------------------------------------------
#-  CCX SMBIOS Dynamic PCDs
### Set Doxy_path: "PCD-CCX-SMBIOS.h"
#----------------------------------------------------------------------------

  ### @brief PCD to set SmbiosTableType4->MaxSpeed (SMBIOS Type4 Offset 14h)
  ### @details PCD used by platform to set Maximum processor speed (in MHz) supported by the system for this processor socket
  ### @li 0 - Unknown
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosT4CpuMaxSpeed|0|UINT16|0x000CC900

#----------------------------------------------------------------------------
#-  CCX Dynamic PCDs
### Set Doxy_path: "PCD-CCX.h"
#----------------------------------------------------------------------------

  ### @cond (MI3||PHX||RMB||RPL||RS||BRH||STXHL||KRK)
  ### @brief RedirectForReturnDis
  ### @details From a workaround for GCC/C000005 issue for XV Core on CZ A0, setting MSRC001_1029 Decode Configuration (DE_CFG) bit 14 [DecfgNoRdrctForReturns] to 1
  ### @brief Permitted Choices: (Type: Value)(Default: Auto)
  ### @li 0xFF - Auto
  ### @li 0    - Disabled
  ### @li 1    - Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdRedirectForReturnDis|0xFF|UINT8|0x000CCA07            # MSR_C001_1029[14]
  ### @endcond

  ### @cond (MI3||PHX||RMB||RPL||RS||BRH||STXHL||KRK||STP)
  ### @brief Streaming Stores Control
  ### @details Enables or disables the streaming stores functionality
  ### @brief Permitted Choices: (Type: Value)(Default: Auto)
  ### @li 0xFF - Auto
  ### @li 1    - Disabled
  ### @li 0    - Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdStreamingStoresCtrl|0xFF|UINT8|0x000CCA09             # MSR_C001_1020[28]
  ### @endcond

  ### @cond (MI3||PHX||RMB||RS||BRH||STXHL||KRK||STP)
  ### @brief Custom Pstate0
  ### @details WARNING - DAMAGE CAUSED BY USE OF YOUR AMD PROCESSOR OUTSIDE OF SPECIFICATION OR IN EXCESS OF FACTORY SETTINGS ARE NOT COVERED
  ### UNDER YOUR AMD PRODUCT WARRANTY AND MAY NOT BE COVERED BY YOUR SYSTEM MANUFACTURER'S WARRANTY.
  ### @details Operating your AMD processor outside of specification or in excess of factory settings, including but not limited to overclocking,
  ### may damage or shorten the life of your processor or other system components, create system instabilities
  ### (e.g., data loss and corrupted images) and in extreme cases may result in total system failure. AMD does not provide support or service for issues or damages
  ### related to use of an AMD processor outside of processor specifications or in excess of factory settings.
  ### @brief Permitted Choices: (Type: Value)(Default: Auto)
  ### @li Auto   (2) - Disabled
  ### @li Custom (1) - Customize this Pstate, applicable only if PcdOcDisable=FALSE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxP0Setting|0x2|UINT8|0x000CCA0A                     # 1- Custom; 2- Auto (For all)
  ### @endcond

  ### @cond (BRH)
  ### @brief Pstate0 FID
  ### @details Specifies the core frequency multiplier. COF = 5MHz * FID
  ### @brief Valid range for the value is: 0x10 .. 0xFF.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxP0Fid32|0xFFFFFFFF|UINT32|0x000CCA0E               # FID (Extend to 32bit)
  ### @endcond

  ### @cond (PHX||RMB||RS||BRH||STXHL||KRK||STP)
  ### @brief Pstate0 VID
  ### @details Specifies the core voltage.
  ### @brief Valid range for the value is: 0x0 .. 0x1FF.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxP0Vid32|0xFFFFFFFF|UINT32|0x000CCA0F               # VID (Extend to 32bit, SVI3 use 9bit VID)
  ### @endcond

  ### @cond (PHX||RMB||RS||BRH||STXHL||KRK||STP)
  ### @brief Pstate0 Freq (MHz)
  ### @details Specifies core frequency (MHz)
  ### @brief Valid range for the value is: 0x0 .. 0xFFFFFFFF.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxP0Freq|0xFFFFFFFF|UINT32|0x000CCA10                # Frequency (For VH, PCO AM4)
  ### @endcond

#----------------------------------------------------------------------------
#-  DF Dynamic PCDs
### Set Doxy_path: "PCD-DF.h"
#----------------------------------------------------------------------------

  ### @cond (BIXBY||RV||ZP||FF3||RN||BA||GN||RMB||RPL||SSP||PHX)
  ### @brief Enable Data Fabric PState support
  ### @details This value controls whether AGESA will enable P-State support.
  ### P-states refers to performance states for a given Socket/die.
  ### This PCD is used to make run time decisions on P-state enablement.
  ### @li TRUE  - AGESA will enable Pstate support.
  ### @li FALSE - AGESA will disable Pstate support.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricPstateSupport|TRUE|BOOLEAN|0x000DF000  # BR
  ### @endcond

  ### @cond (RV||ZP||FF3||RN||BA||GN||RMB||RPL||SSP||PHX)
  ### @brief DRAM ECC redirection is a data-protection feature. Redirection is a special ECC feature that enables the scrubber to
  ### immediately scrub any address in which a correctable error is discovered. The AMD recommended setting is to enable this
  ### feature. Changes to this setting must be based on individual platform testing.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE - This option is active
  ### @li FALSE - This option is turned off
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricEccScrubRedirection|TRUE|BOOLEAN|0x000DF001
  ### @endcond

  ### @cond (FF3||RN||BA||GN||RMB||RPL||SSP)
  ### @brief This item Enables/Disables the Poisoned ECC Scrub mode.
  ### @li TRUE - This option is active
  ### @li FALSE - This option is turned off
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricEccScrubPoisoned|TRUE|BOOLEAN|0x000DF002
  ### @endcond

  ### @cond (FF3||RN||BA||GN||RMB||RPL||SSP)
  ### @brief Limit the number of redirect scrubs in flight at any one time. Valid only when either
  ### PcdAmdFabricEccScrubRedirection or PcdAmdFabricEccScrubPoisoned is TRUE.
  ### @li 0 - Infinite
  ### @li 1 - Limit Redirect Scrubs to 2
  ### @li 2 - Limit Redirect Scrubs to 4
  ### @li 3 - Limit Redirect Scrubs to 8
  ### @li 0xFF - Auto
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricEccScrubRedirectionLimit|0xFF|UINT8|0x000DF003
  ### @endcond

  ### @cond (FF3||RN||BA||GN||RMB||RPL||SSP)
  ### @brief Number of hours to scrub the entire memory. AMD suggests ~24 hours. Please refer to the PPR/BKDG for a description
  ### of this feature.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricDramScrubTime|0xFF|UINT8|0x000DF004
  ### @endcond

  ### @cond (FF3||RN||BA||GN||RMB||RPL||RS||SSP||MI3||BRH)
  ### @brief Fabric Watch Dog timer configuration
  ### @details This value set the value for the Fabric Watchdog timer.
  ### @li 0 - 1.31ms
  ### @li 1 - 1.28us
  ### @li 2 - 10ns
  ### @li 3 - Disabled
  ### @li 0xFF - Auto
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricWdtCfg|0xFF|UINT8|0x000DF005
  ### @endcond

  ### @cond (FF3||RN||BA||GN||RMB||RPL||RS||SSP||MI3||BRH)
  ### @brief Fabric Watch Dog timer control selection
  ### @details This value sets the Watch Dog timer control selection value
  ### @li 0: 31
  ### @li 1: 127
  ### @li 2: 255
  ### @li 3: 511
  ### @li 4: 1023
  ### @li 5: 2047
  ### @li 6: 4095
  ### @li 7: 16383
  ### @li 0xFF - Auto
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricWdtCntSel|0xFF|UINT8|0x000DF006
  ### @endcond

  ### @cond (FF3||RN||BA||ZP||RMB||RPL)
  ### @brief For CPUs that use multiple die or use multiple sockets. The NUMA distribution of memory devices affects the
  ### performance. The SLIT table tells the OS the relative distance of memory from the processor core, to allow it to select
  ### cores for tasks that are closer to the available memory so as to maximize performance. The number of unique distance values
  ### declared within the ACPI SLIT table has been found to affect performance within certain operating system environments.
  ### This item specifies the platform preference for how the SLIT distance values are to be created.
  ### @li 0 - Honor the PPR definition and declare as many unique distance values as the hardware configuration warrants.
  ### @li 1 - Declare all domains as locally accessible (a distance value of 0xA for all system dies).
  ### @li 2 - Declare a maximum of 2 unique distance values.
  ### @li Die local accesses will have a distance of 0xA.
  ### @li All domains other than self are described by PcdAmdFabric2ndDegreeSlitDistance.
  ### @li 3 - Declare a maximum of 3 unique distance values.
  ### @li All accesses to the
  ### @li 0xFF - Declare a maximum of 3 unique distance values. Die local accesses will have a distance of 0xA. Socket local
  ### domains other than self are set to 0x10. All accesses to the other sockets are set to 0x20.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitDegree|0xFF|UINT8|0x000DF007
  ### @endcond

  ### @cond (FF3||RN||BA||ZP||RMB||RPL)
  ### @brief Specifies the SLIT distance to domains other than self if PcdAmdFabricSlitDegree is set to 2.
  ### @li 0x0A..0xFF - The SMP relative distance to use for all cores not on the same die.
  ### @li all other values - Reserved.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabric2ndDegreeSlitDistance|0x1C|UINT8|0x000DF008
  ### @endcond

  ### @cond (FF3||RN||BA||ZP||RMB||RPL)
  ### @brief Specifies the SLIT distance to 'socket local' domains other than self that are within the same socket, but not on
  ### the same die. This is used only when PcdAmdFabricSlitDegree is set to 3.
  ### @li 0x0A..0xFF - The SMP relative distance to use for all cores not on the same die, but within the same socket.
  ### @li all other values - Reserved.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabric3rdDegreeSlitLocalDistance|0x10|UINT8|0x000DF009
  ### @endcond

  ### @cond (FF3||RN||BA||ZP||RMB||RPL)
  ### @brief Specifies the SLIT distance to 'socket remote' domains that are in other sockets. This is used only when
  ### PcdAmdFabricSlitDegree is set to 3.
  ### @li 0x0A..0xFF - The SMP relative distance to use for all cores not on the same socket.
  ### @li all other values - Reserved.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabric3rdDegreeSlitRemoteDistance|0x20|UINT8|0x000DF00A
  ### @endcond

  ### @cond (FF3||RN||BA||ZP||RMB||RPL||RS||SSP||MI3||BRH)
  ### @brief Creates a layer of virtual domains on top of the physical domains in which each CCX is declared to be in its on domain.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li TRUE - Each CCX is declared to be in its own domain.
  ### @li FALSE - Use NPS settings for domain configuration.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricCcxAsNumaDomain|FALSE|BOOLEAN|0x000DF00B
  ### @endcond

  ### @cond (FF3||RN||BA||ZP||RMB||RPL||RS||MI3||BRH)
  ### @brief This determines how the SLIT distances are declared. If this is set to Auto=0xFF, then various SLIT distances are
  ### automatically determined. When this is set to Manual=0, then other PCDs (PcdAmdFabricSlitVirtualDistance,
  ### PcdAmdFabricSlitLocalDistance, PcdAmdFabricSlitRemoteDistance, PcdAmdFabricSlitSLinkLocalDistance,
  ### PcdAmdFabricSlitSLinkRemoteDistance, PcdAmdFabricSlitInterSLinkLocalDistance, PcdAmdFabricSlitInterSLinkRemoteDistance) are
  ### honored and user can manually configure the SLIT distances.
  ### @li 0xFF - (auto) Let firmware determine distances.
  ### @li 0 - Manual.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitDistancePcdCtrl|0xFF|UINT8|0x000DF00C
  ### @endcond

  ### @cond (FF3||RN||BA||ZP||RMB||RPL||RS||SSP||MI3||BRH)
  ### @brief If PcdAmdFabricSlitDistancePcdCtrl is zero, this value specifies the distance between two virtual domains
  ### (see L3 Cache as NUMA Domain) that belong to the same physical domain.
  ### @brief Valid range for the value is: 0x0A .. 0xFF.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitVirtualDistance|0x0B|UINT8|0x000DF00D
  ### @endcond

  ### @cond (FF3||RN||BA||ZP||RMB||RPL||RS||SSP||MI3||BRH)
  ### @brief If PcdAmdFabricSlitDistancePcdCtrl is zero, this value specifies the distance between two physical domains on the same socket.
  ### @brief Valid range for the value is: 0x0A .. 0xFF.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitLocalDistance|0x0C|UINT8|0x000DF00E
  ### @endcond

  ### @cond (FF3||RN||BA||ZP||RMB||RPL||RS||SSP||MI3||BRH)
  ### @brief If PcdAmdFabricSlitDistancePcdCtrl is zero, this value specifies the distance between two physical domains on different sockets.
  ### @brief Valid range for the value is: 0x0A .. 0xFF.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitRemoteDistance|0x20|UINT8|0x000DF00F
  ### @endcond

  ### @cond (FF3||RN||BA||ZP||RMB||RPL||RS||SSP||MI3||BRH)
  ### @brief If PcdAmdFabricSlitDistancePcdCtrl is non-zero, this boolean switches between setting a value of 2.8 or 3.2 is
  ### reported for domains on different sockets. The possible values are on two different sides of a threshold for certain operating systems.
  ### @li TRUE - use the 'far' 3.2 values.
  ### @li FALSE - use the 'near' 2.8 values.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitAutoRemoteFar|TRUE|BOOLEAN|0x000DF010
  ### @endcond

  ### @cond (FF3||RN||BA||ZP||RMB||RPL||SSP)
  ### @brief If PcdAmdFabricSlitDistancePcdCtrl is zero, this value specifies the distance between a conventional domain and an SLink connected domain, both on the same socket.
  ### @brief Valid range for the value is: 0x0A .. 0xFF.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitSLinkLocalDistance|0x32|UINT8|0x000DF011
  ### @endcond

  ### @cond (FF3||RN||BA||ZP||RMB||RPL||SSP)
  ### @brief If PcdAmdFabricSlitDistancePcdCtrl is zero, this value specifies the distance between a conventional domain and an SLink connected domain, each on different sockets.
  ### @brief Valid range for the value is: 0x0A .. 0xFF.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitSLinkRemoteDistance|0x3C|UINT8|0x000DF012
  ### @endcond

  ### @cond (FF3||RN||BA||ZP||RMB||RPL||SSP)
  ### @brief If PcdAmdFabricSlitDistancePcdCtrl is zero, this value specifies the distance between two SLink connected domains, both on the same socket.
  ### @brief Valid range for the value is: 0x0A .. 0xFF.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitInterSLinkLocalDistance|0xFF|UINT8|0x000DF013
  ### @endcond

  ### @cond (FF3||RN||BA||ZP||RMB||RPL||SSP)
  ### @brief If PcdAmdFabricSlitDistancePcdCtrl is zero, this value specifies the distance between two SLink connected domains, each on different sockets.
  ### @brief Valid range for the value is: 0x0A .. 0xFF.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitInterSLinkRemoteDistance|0xFF|UINT8|0x000DF014
  ### @endcond

  ### @cond (FF3||RN||BA||ZP||RMB||RPL||SSP||MI3||BRH)
  ### @brief if PcdAmdFabricCcxAsNumaDomain is TRUE, this specifies if a round-robin scheme to return a single NUMA node from the list of nodes associated with a given
  ### Quadrant is used. See
  ### @li TRUE - Use the Round-Robin scheme and report one node at a time.
  ### @li FALSE - Report all nodes at one time.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricRoundRobinNumaDomainForCcx|TRUE|BOOLEAN|0x000DF015
  ### @endcond

  ### @cond (RS||BRH)
  ### @brief Fabric SLIT CXL Local Distance
  ### @details if PcdAmdFabricSlitDistancePcdCtrl is zero, this value
  ### specifies the distance between a conventional domain and
  ### an CXL connected domain, both on the same socket
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitCxlLocalDistance|18|UINT8|0x000DF016
  ### @endcond

  ### @cond (RS||BRH)
  ### @brief  Fabric SLIT CXL Remote Distance
  ### @details if PcdAmdFabricSlitDistancePcdCtrl is zero, this value
  ### specifies the distance between a conventional domain an
  ### an CXL connected domain, each on different sockets
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitCxlRemoteDistance|28|UINT8|0x000DF17
  ### @endcond

  ### @cond (RMB || PHX || RPL)
  ### @brief Enable Mixed DIMM config extended NUMA domain
  ### @details Mixed DIMM config extended NUMA domain in SRAT that contain only non-interleaving memory region but no processor.
  ### @li TRUE  - Enable Mixed DIMM config extended NUMA domain in SRAT
  ### @li FALSE - Disnable Mixed DIMM config extended NUMA domain in SRAT
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricMixedDimmConfigNumaDomain|TRUE|BOOLEAN|0x000DF01A
  ### @endcond

  ### @cond (FF3||RN||BA||ZP||RMB||RPL||RS||SSP||MI3||BRH)
  ### @brief Specifies the top address to be used for MMIO space allocations. No MMIO space will be used above this limit. Some devices have limits on the address space they
  ### are able to access. For example a device with a 40-bit address limitation can not use MMIO space above 1TeraByte (1T). By setting this PCD to 0x10000000000 (1T), MMIO
  ### space would not be allocated above 1T. The default is to use all of the space above the end of physical memory.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMmioAbove4GLimit|0xFFFFFFFFFFFFFFFF|UINT64|0x0003FFC2
  ### @endcond

  ### @cond (FF3||RN||BA||ZP||RMB||RPL||RS||SSP||MI3||BRH)
  ### @brief This control is provided to force the MMIO allocation to use the evenly allocated method regardless of any calls made to the MMIO reallocation functions.
  ### @li TRUE - MMIO space will be allocated evenly over the present die.
  ### @li FALSE - User based re-distribution will be allowed.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricResourceDefaultMap|FALSE|BOOLEAN|0x0003FFC3
  ### @endcond

  ### @cond (FF3||RN||BA||ZP||RMB||RPL||SSP)
  ### @brief This control is part of the periodic scrub controls that are described in the PPR section "DRAM Scrub Rate Algorithm". The Periodic Directory Rinse (PDR)
  ### Algorithm uses this input to set the DramScrubLimitAddr.
  ### @li TRUE - Enable Periodic Directory Rinse using a DramScrubRate of 3.
  ### @li FALSE - DramScrubRate is a calculated number and will be less than 3.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricPeriodicDirRinse|TRUE|BOOLEAN|0x0003FFC4
  ### @endcond

  ### @cond (RS||MI3||BRH)
  ### @brief Fabric control SRAT/SLIT support
  ### @details Override to allow installation of SRAT,SLIT and MSCT protocols if NumaCount is 1 or 0
  ### @li TRUE  - Override to force installation of SRAT,SLIT and MSCT protocols if NumaCount is 1
  ### @li FALSE - Installation of SRAT,SLIT and MSCT protocols will be dependent on the actual NumaCount to be > 1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSratSlitInstallOverride|FALSE|BOOLEAN|0x0003FFC5
  ### @endcond

  ### @cond (RS||FF3||RN||BA||ZP||RMB||RPL||SSP||MI3||BRH)
  ### @brief Fabric Resource Default Size Pointer
  ### @details This points to a structure of FABRIC_RESOURCE_FOR_EACH_RB
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricResourceDefaultSizePtr|0|UINT64|0x00DF0000
  ### @endcond

  ### @cond (RPL||PHX||MI3||BRH||STX||STXHL||KRK||GNR||STP)
  ### @brief Enable/Disable Data Fabric Sync Flood on Fatal Error for debug purposes.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li FALSE - No Sync Flood occurs.
  ### @li TRUE - Fatal errors cause Sync Flood across fabric.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricImmSyncFloodOnFatalErrCtrl|TRUE|BOOLEAN|0x00DF0004
  ### @endcond

  ### @cond (BRH)
  ### @brief Attempt to remap DRAM out of the space just below the 1TB boundary.
  ### @li 0 - Memory 'hole' is not remapped
  ### @li 1 - Memory 'hole' is remapped
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabric1TbRemap|0x01|UINT8|0x00DF0007
  ### @endcond

#----------------------------------------------------------------------------
#-  FCH Dynamic PCDs
### Set Doxy_path: "PCD-FCH.h"
#----------------------------------------------------------------------------

  #FCH
  ### @brief Allows the host BIOS to set the IO address used for the ACPI SMI command port.
  ### @brief Permitted Choices: (Type: Value)(Default: 0xB0)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchCfgSmiCmdPortAddr|0xB0|UINT16|0x0003FFC0

  ### @brief Allows upstream Soc RAS event to trigger SMI.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchApuRasSmiSupport|FALSE|BOOLEAN|0x0003FFC1

#----------------------------------------------------------------------------
#-  FCH Dynamic PCDs
### Set Doxy_path: "PCD-FCH-I2C-BUS-HOLD.h"
#----------------------------------------------------------------------------

  ### @name I2C bus data hold times
  ### @{
  ### @brief These PCDs allow a platform to set the required SDA hold time, in units of ic_clk period, for the I2C controllers. Permitted Choices: (Type: Value)(Default: as below)
  ### @li bits[15:0] IC_SDA_TX_HOLD - Sets the required SDA hold time in units of ic_clk period, when I2C logic acts as a transmitter.
  ### @li bits[23:16] IC_SDA_RX_HOLD - Sets the required SDA hold time in units of ic_clk period, when I2C logic acts as a reciever.


  ### I2C-0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c0SdaHold|0x00000035|UINT32|0x000FC000
  ### I2C-1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c1SdaHold|0x00000035|UINT32|0x000FC001
  ### I2C-2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c2SdaHold|0x00000035|UINT32|0x000FC002
  ### I2C-3
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c3SdaHold|0x00000035|UINT32|0x000FC003
  ### I2C-4
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c4SdaHold|0x00000035|UINT32|0x000FC004
  ### I2C-5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c5SdaHold|0x00000035|UINT32|0x000FC005
  ### @} end of I2C hold times

### @name I2C bus data hold times
  ### @{
  ### @brief These PCDs allow a platform to set the required SDA hold time, in units of ic_clk period, for the I2C controllers. Permitted Choices: (Type: Value)(Default: as below)
  ### @li bits[15:0] IC_SDA_TX_HOLD - Sets the required SDA hold time in units of ic_clk period, when I2C logic acts as a transmitter. These PCD's shoud match with their corresponding APCB token:
  ### APCB_TOKEN_UID_FCH_I2Cx_SDA_TX_HOLD. (Default: 0x0035)
  ### @li bits[23:16] IC_SDA_RX_HOLD - Sets the required SDA hold time in units of ic_clk period, when I2C logic acts as a reciever. These PCD's shoud match with their corresponding APCB token:
  ### APCB_TOKEN_UID_FCH_I2Cx_SDA_RX_HOLD (Default: 0x00)

  ### I2C-0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c0SdaTxHold|0x0035|UINT16|0x000FC110
  ### I2C-1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c1SdaTxHold|0x0035|UINT16|0x000FC221
  ### I2C-2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c2SdaTxHold|0x0035|UINT16|0x000FC332
  ### I2C-3
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c3SdaTxHold|0x0035|UINT16|0x000FC443
  ### I2C-4
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c4SdaTxHold|0x0035|UINT16|0x000FC554
  ### I2C-5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c5SdaTxHold|0x0035|UINT16|0x000FC665

  ### I2C-0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c0SdaRxHold|0x00|UINT8|0x000FC082
  ### I2C-1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c1SdaRxHold|0x00|UINT8|0x000FC083
  ### I2C-2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c2SdaRxHold|0x00|UINT8|0x000FC084
  ### I2C-3
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c3SdaRxHold|0x00|UINT8|0x000FC085
  ### I2C-4
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c4SdaRxHold|0x00|UINT8|0x000FC086
  ### I2C-5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c5SdaRxHold|0x00|UINT8|0x000FC087
  ### @} end of I2C hold times

  ### @brief To override I2C data hold times
  ### @details This PCD should match with APCB_TOKEN_UID_FCH_IC_SDA_HOLD_OVERRIDE
  ### @li 0 - Disabled
  ### @li 1 - Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2cSdaHoldOverride|0x00|UINT8|0x000FC088

#----------------------------------------------------------------------------
#-  FCH Dynamic PCDs
### Set Doxy_path: "PCD-FCH-I3C-HOLD.h"
#----------------------------------------------------------------------------

  ### @name I3C bus data hold times in term of the core clock period of the transmit data (SDA) with respect to the SCL edge.
  ### @{
  ### @brief These PCDs allow a platform to set the required SDA hold time for the I3C controllers. Permitted Choices: (Type: Value)(Default: 0x0000_0002)
  ### @li 1 - 7

  ### I3C-0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI3c0SdaHold|0x2|UINT8|0x000FC009
  ### I3C-1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI3c1SdaHold|0x2|UINT8|0x000FC00A
  ### I3C-2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI3c2SdaHold|0x2|UINT8|0x000FC00B
  ### I3C-3
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI3c3SdaHold|0x2|UINT8|0x000FC00C
  ### @} end of I3C hold times

### @brief To override I3C SDA_TX_HOLD Time
  ### @details This PCD should match with APCB_TOKEN_UID_FCH_I3C_SDA_HOLD_OVERRIDE
  ### @li 0 - Disabled
  ### @li 1 - Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI3cSdaHoldOverride|0x00|UINT8|0x000FC089

### @brief I3C PP HCNT
  ### @details SCL push-pull High count for I3C transfers targeted to I3C devices.
  ### In ABL, a similar APCB token "APCB_TOKEN_UID_FCH_I3C_PP_HCNT" is used to control the
  ### I3C Push Pull High Count. Default value is 0x08.
  ### Please make sure to sync any changes to APCB_TOKEN_UID_FCH_I3C_PP_HCNT
  ### if there is any modification to this PCD.
  ### @li 0x08~0x0D = Range
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI3cPPHcnt|0x08|UINT8|0x000FC08A

#----------------------------------------------------------------------------
#-  FCH Dynamic PCDs
### Set Doxy_path: "PCD-FCH-I2C-I3C-CONTROLLER.h"
#----------------------------------------------------------------------------

  ### @name I3C/I2C Configuration Options.
  ### @{
  ### @brief These PCDs allows a platform to set the configuration of each IxC Controllers [0-5]. Permitted Choices: (Type: Value)(Default: 0x01)
  ### @details  The values used for these controls must match the values used for the APCB token:
  ### @details  PcdAmdFchI2cI3c0 should match APCB_TOKEN_UID_I2C_I3C_SMBUS_0
  ### @details  PcdAmdFchI2cI3c1 should match APCB_TOKEN_UID_I2C_I3C_SMBUS_1
  ### @details  PcdAmdFchI2cI3c2 should match APCB_TOKEN_UID_I2C_I3C_SMBUS_2
  ### @details  PcdAmdFchI2cI3c3 should match APCB_TOKEN_UID_I2C_I3C_SMBUS_3
  ### @details  PcdAmdFchI2c4 should match APCB_TOKEN_UID_I2C_I3C_SMBUS_4
  ### @details  PcdAmdFchI2c5 should match APCB_TOKEN_UID_I2C_I3C_SMBUS_5
  ### @li  0 - I3C Enabled
  ### @li  1 - I2C Enabled
  ### @li  3 - Both Disabled

  ### IxC-0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2cI3c0|0x01|UINT8|0x000FC090
  ### IxC-1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2cI3c1|0x01|UINT8|0x000FC091
  ### IxC-2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2cI3c2|0x01|UINT8|0x000FC092
  ### IxC-3
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2cI3c3|0x01|UINT8|0x000FC093
  ### IxC-4
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c4|0x01|UINT8|0x000FC094
  ### IxC-5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c5|0x01|UINT8|0x000FC095
  ### @} end of I3C/I2C Configuration Options


#----------------------------------------------------------------------------
#-  FCH Dynamic PCDs
### Set Doxy_path: "PCD-FCH-I3C.h"
#----------------------------------------------------------------------------

  ### @brief I3c controller support in Bios (Type: UINT8)(Default: 0x3)
  ### @li 0x0 - I3c not Supported in PEI and DXE phase.
  ### @li 0x1 - I3c Supported in PEI phase.
  ### @li 0x2 - I3c Supported in DXE phase.
  ### @li 0x3 - I3c Supported in PEI and DXE phase.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI3cControllerSupport|0x3|UINT8|0x000FC08F

#----------------------------------------------------------------------------
#-  FCH Dynamic PCDs
### Set Doxy_path: "PCD-FCH.h"
#----------------------------------------------------------------------------

  ### @brief FCH Alink RAS support
  ### @details  Enable FCH A-Link parity checking.
  ### @li TRUE  - Enable A-Link parity checking.
  ### @li FALSE - Disable A-Link parity checking.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchAlinkRasSupport|FALSE|BOOLEAN|0x000FC006

  ### @brief This control specifies the action taken when a Sync Flood is detected.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li False - System halt when Sync Flood occurs.
  ### @li True - System reset when Sync Flood occurs.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdResetCpuOnSyncFlood|TRUE|BOOLEAN|0x000FC007
  ### @brief This control delay time for system reset when a Sync Flood is detected.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li 5-255 - Delay minutes, rang 5 to 255 minutes. System reset will be delayed in minutes when Sync Flood occurs.
  ### @li 0-4   - Delaying system reset on sync flood is not enabled.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDelayResetCpuOnSyncFlood|0|UINT8|0x000FC03B

#----------------------------------------------------------------------------
#-  FCH Dynamic PCDs
### Set Doxy_path: "PCD-FCH-UART.h"
#----------------------------------------------------------------------------
  ### @name UART Legacy IO Assignments
  ### @{
  ### @brief These controls allow the platform to specify the legacy IO ranges that are to be used by the UART ports in the processor. Permitted Choices: (Type: value)(Default: Disabled)
  ### @li  0 - Disabled
  ### @li  1 - IO range 0x02E8 - 0x02EF
  ### @li  2 - IO range 0x02F8 - 0x02FF
  ### @li  3 - IO range 0x03E8 - 0x03EF
  ### @li  4 - IO range 0x03F8 - 0x03FF


  gEfiAmdAgesaPkgTokenSpaceGuid.FchUart0LegacyEnable|0x00|UINT8|0x000FC010
  gEfiAmdAgesaPkgTokenSpaceGuid.FchUart1LegacyEnable|0x00|UINT8|0x000FC011
  gEfiAmdAgesaPkgTokenSpaceGuid.FchUart2LegacyEnable|0x00|UINT8|0x000FC012
  gEfiAmdAgesaPkgTokenSpaceGuid.FchUart3LegacyEnable|0x00|UINT8|0x000FC013
  ### @} end UART Legacy IO assignments

  ### @name UART MittEnable
  ### @{
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUartMittEnable|FALSE|BOOLEAN|0x000FC014
  ### @} end UART MittEnable assignments
#----------------------------------------------------------------------------
#-  FCH Dynamic PCDs
### Set Doxy_path: "PCD-FCH.h"
#----------------------------------------------------------------------------

  ### @brief This item selects the action to take after a power failure has occurred.
  ### @brief Permitted Choices: (Type: List)(Default: Pwr_Restore)
  ### @li Pwr_Off - Always leave the unit off.
  ### @li Pwr_On - Always restart the unit.
  ### @li Pwr_Restore - Return the unit to the state prior to the power outage.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPwrFailShadow|0x00|UINT8|0x000FC018

  ### @brief This control enables the EMI control for Spread Spectrum feature for the clock source.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE - This option is active.
  ### @li FALSE - This option is turned off.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSpreadSpectrum|TRUE|BOOLEAN|0x000FC019

#----------------------------------------------------------------------------
#-  FCH Dynamic PCDs
### Set Doxy_path: "PCD-FCH-USB.h"
#----------------------------------------------------------------------------

  ### @name USB Controller Enables
  ### @{
  ### @brief This group of controls select which XHCI controllers are active in the system. Note that different processor models have controllers on socket 1, please see port
  ### mapping . Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @cond (BRH)
  ### Also need to sync value with APCB_TOKEN_UID_FCH_USB_0_ENABLE~APCB_TOKEN_UID_FCH_USB_3_ENABLE
  ### @endcond
  ### @li TRUE - The controller on is active.
  ### @li FALSE - This controller is turned off.


  ### USB 3.0 controller0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXhci0Enable|TRUE|BOOLEAN|0x000FC020
  ### USB 3.0 controller1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXhci1Enable|TRUE|BOOLEAN|0x000FC021
  ### USB3.0 controller0 on MCM-1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXhci2Enable|TRUE|BOOLEAN|0x000FC022
  ### USB3.0 controller1 on MCM-1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXhci3Enable|TRUE|BOOLEAN|0x000FC023
  ### @} end USB Controller Enables

#----------------------------------------------------------------------------
#-  FCH Dynamic PCDs
### Set Doxy_path: "PCD-FCH.h"
#----------------------------------------------------------------------------
  ### @cond !BRH
  ### @brief USB ECC SMI Contro. Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li FALSE - USB ecc SMI Disable.
  ### @li TRUE - USB ecc SMI Enable.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXhciECCDedErrRptEn|FALSE|BOOLEAN|0x000FC024
  ### @endcond

  ### @brief Allows the host BIOS to set the PCI Sub-System ID value reported by the SMBus controller. A value of zero will indicate that the normal hardware fused value should be used.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x00000000)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSmbusSsid|0x00000000|UINT32|0x000FC025

  ### @brief This item specifies the ISA bridge controller SSID. Some customers may wish to set a specific SSID so that their driver or application can invoke special features. A value of zero will indicate that the normal hardware fused value. should be used.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x00000000)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdIsaBridgeSsid|0x00000000|UINT32|0x000FC027

  ### @brief Select whether or not the FCH Sata controller is active.
  ### @brief APCB_TOKEN_UID_FCH_SATA_ENABLE need to be synchronized with PcdSataEnable
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE - This option is active.
  ### @li FALSE - This option is turned off.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataEnable|TRUE|BOOLEAN|0x000FC030

  ### @brief There is one FCH per socket for these models; so, this control is used to select which FCH SATA controllers are active. there are 4 controllers per socket.
  ### This control is a bit-mapped list, each bit represents a controller; 0=disabled, 1=active.
  ### @brief APCB_TOKEN_UID_FCH_SATA_0_ENABLE to APCB_TOKEN_UID_FCH_SATA_7_ENABLE need to be synchronized with PcdSataEnable2
  ### @brief Permitted Choices: (Type: Value)(Default: 0xFF)
  ### @li Bits 0-3: Socket 0, SATA controllers 0-3.
  ### @li Bits 4-7: Socket 1, SATA controllers 0-3.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataEnable2|0xFF|UINT8|0x000FC031

  ### @brief Selects the operational mode for the SATA controller
  ### @brief Should be synchronized with APCB_TOKEN_UID_NBIO_SATA_MODE if this ACPB token exists in your program.
  ### @brief Permitted Choices: (Type: List)(Default: AHCI_Mode)
  ### @li RAID mode Not available for [F19M00].
  ### @li 0x02: AHCI mode
  ### @li 0x05: AMD-AHCI mode (for Device ID of 0x7904)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataClass|0x02|UINT8|0x000FC032

  ### @brief Select whether or not the FCH Sata RAS feature is active.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li TRUE - This option is active.
  ### @li FALSE - This option is turned off.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataRasSupport|FALSE|BOOLEAN|0x000FC033

  ### @brief SATA AHCI Prefetch
  ### @details  Sata Disabled AHCI Prefetch Function.
  ### @li TRUE : Sata Disabled AHCI Prefetch Function is active.
  ### @li FALSE: Sata Disabled AHCI Prefetch Function is tuned off.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataAhciDisPrefetchFunction|TRUE|BOOLEAN|0x000FC034

#----------------------------------------------------------------------------
#-  FCH Dynamic PCDs
### Set Doxy_path: "PCD-FCH-SATA.h"
#----------------------------------------------------------------------------

  ### @name SATA Device Sleep
  ### @{
  ### @brief These controls Enable/Disable DevSlp for a port of the SATA controller. Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li FALSE - SATA DevSlp Port N is Disable.
  ### @li TRUE - SATA DevSlp Port N Enabled.

  ### SATA DevSlp Port 0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataDevSlpPort0|FALSE|BOOLEAN|0x000FC035
  ### SATA DevSlp Port 1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataDevSlpPort1|FALSE|BOOLEAN|0x000FC036

  ### @brief This control specifies the SATA controller port that is to be enabled for DevSlp. Up to two ports can be so designated. This control is dependent upon the corresponding PcdSataDevSlpPort0 control. Permitted Choices: (Type: List)(Default: 0)
  ### @li 0:3 - Indicates the port # of the controller supporting DevSlp0.
  ### @li 4:7 - Indicates the controller # supporting DevSlp0.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataDevSlpPort0Num|0x00|UINT8|0x000FC037
  ### @brief This control specifies the SATA controller port that is to be enabled for DevSlp. Up to two ports can be so designated. This control is dependent upon the corresponding PcdSataDevSlpPort1 control. Permitted Choices: (Type: List)(Default: 0)
  ### @li 0:3 - Indicates the port # of the controller supporting DevSlp1.
  ### @li 4:7 - Indicates the controller # supporting DevSlp1.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataDevSlpPort1Num|0x00|UINT8|0x000FC038

  ### @cond (BRH)
  ### @brief This control specifies the Device Sleep function of the SATA controller of the non-main socket. Permitted Choices: (Type: List)(Default: 0)
  ### @details Each socket can support max 4 SATA controllers and each SATA controller can support max 8 SATA ports. So each socket can support max 32 SATA ports, but only 2 SATA ports can have DevSlp feature. Based on motherboard design, set correct value to this PCD and AGESA would configure SATA registers to make DevSlp works.
  ### @li Bit[0]    : Socket1 DevSlp 0 Enable Bit (1 - enable; 0 - disable)
  ### @li Bit[3:1]  : Socket1 DevSlp 0 port number (Range from 0 to 7)
  ### @li Bit[7:4]  : Socket1 DevSlp 0 port controller (Range from 0 to 3)
  ### @li Bit[8]    : Socket1 DevSlp 1 Enable Bit (1 - enable; 0 - disable)
  ### @li Bit[11:9] : Socket1 DevSlp 1 port number (Range from 0 to 7)
  ### @li Bit[15:12]: Socket1 DevSlp 1 port controller (Range from 0 to 3)
  ### @li Bit[63:16]: Reserved
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataMultiDieDevSlp|0x00|UINT64|0x000FC03C
  ### @}  SATA Device Sleep
  ### @endcond

  ### @brief FCH-SATA Staggered Spin-up feature
  ### @details Select whether or not the FCH Sata Spin-up feature.
  ### @li  TRUE  - This option is active.
  ### @li  FALSE - This option is turned off.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataStaggeredSpinup|FALSE|BOOLEAN|0x000FC039

  ### @brief This control selects the SATA port hot plug capability. Note: 'ESP' is External SATA Port, also known as E-SATA in the SATA spec. 5.2.6 e-SATA. The hot-plug
  ### feature is described in SATA spec 5.2 Usage Models - hot plug support.
  ### @brief  The control is a 64-bit value divided into bit fields. Each bit represents one SATA port as follows:
  ### @brief For each bit field, permitted Choices: (Type: Value)(Default: 0x00000000_00000000)
  ### @li Bit 00-07: Socket0, SATA0 controller, Ports 0-7
  ### @li Bit 08-15: Socket0, SATA1 controller, Ports 0-7
  ### @li Bit 16-23: Socket0, SATA2 controller, Ports 0-7
  ### @li Bit 24-31: Socket0, SATA3 controller, Ports 0-7
  ### @li Bit 32-39: Socket1, SATA0 controller, Ports 0-7
  ### @li Bit 40-47: Socket1, SATA1 controller, Ports 0-7
  ### @li Bit 48-55: Socket1, SATA2 controller, Ports 0-7
  ### @li Bit 56-63: Socket1, SATA3 controller, Ports 0-7
  ### @li 0b - hot plug feature is disabled.
  ### @li 1b - hot plug feature is enabled for the port.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataMultiDiePortESP|0x00|UINT64|0x000FC03A


#----------------------------------------------------------------------------
#-  FCH Dynamic PCDs
### Set Doxy_path: "PCD-FCH.h"
#----------------------------------------------------------------------------

  ### @cond (ALL)
  ### @brief Serial IRQ
  ### @details Enable the serial IRQ function
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSerialIrqEnable|TRUE|BOOLEAN|0x000A6001
  ### @endcond

#----------------------------------------------------------------------------
#-  FCH Dynamic PCDs
### Set Doxy_path: "PCD-FCH-ESPI.h"
#----------------------------------------------------------------------------

  ### @cond (FF3||RN||BA||ZP||RMB||RPL||SSP)
  ### @name eSPI Enablement
  ### @{
  ### These controls specify board characteristics that affect the operating
  ###  conditions of the eSPI bus.

  ### @brief ESPI Enables
  ### @details Enable eSPI Controller.
  ### @li TRUE  - The controller is active.
  ### @li FALSE - The controller is turned off.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEspiEnable|FALSE|BOOLEAN|0x000FC008
  ### @brief This control specifies a board restriction on the top frequency the eSPI channel should use. Permitted Choices: (Type: Value)(Default: 0x0F)
  ### @li 0x00 - 16.7MHz is the top limit used on the board.
  ### @li 0x01 - 33MHz is the limit.
  ### @li 0x02 - 66MHz is the max the board can use.
  ### @li 0x0F - Auto. The FCH firmware will evaluate the host and device capability and choose the fastest common frequency.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEspiOperatingFreq|0x0F|UINT8|0x000A6003
  ### @brief This control specifies a board restriction on the transfer mode on the eSPI channel should use. Permitted Choices: (Type: Value)(Default: 0x0F)
  ### @li 0x00 - Single IO mode is the top limit used on the board.
  ### @li 0x01 - Dual IO mode is the limit.
  ### @li 0x02 - Quad mode is the max the board can use.
  ### @li 0x0F - Auto. The FCH firmware will evaluate the host and device capability and choose the fastest common mode.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEspiIoMode|0x0F|UINT8|0x000A6004

  ### @brief The eSPI controller is capable decoding up to 4 IO ranges and upto 4 MMIO ranges for the device(s) present on the eSPI bus. The values used to select these
  ### ranges is device dependent. The ranges are specified in a PCD structure that has an effective structure is as follows:
  ### @code
  ### struct  {
  ###  struct  {
  ###      UINT8       IoRangeEnable;
  ###      UINT16      IoRangeBase;
  ###      UINT8       IoRangeSize;
  ###    }eSPI_IO_Ranges[3];
  ###    struct  {
  ###      UINT8       MmioRangeEnable;
  ###      UINT32      MmioRangeBase;
  ###      UINT16      MmioRangeSize;
  ###    eSPI_MMIO_Ranges[3];
  ###} PcdEspiIoMmioDecode;
  ### @endcode
  ### @details IoRangeEnable
  ### @details MmioRangeEnable
  ### @li Indicates if the range is active.
  ### @li Permitted Choices: (Type: Boolean)(Default: False)
  ### @li False - range is not active.
  ### @li True - accesses to this range will be forwarded to the eSPI device.
  ### @details IoRangeBase
  ### @li This is the starting address on the IO bus for the decode range.
  ### @li Permitted Choices: (Type: Value (Default: 0x0000)
  ### @details IoRangeSize
  ### @li This is the size, in bytes, of the range. Decode range is from Base to (Base+Size).
  ### @li Permitted Choices: (Type: Value (Default: 0x0000)
  ### @details MmioRangeBase
  ### @li This is the starting address on the memory bus for the decode range.
  ### @li Permitted Choices: (Type: Value (Default: 0x00000000)
  ### @details MmioRangeSize
  ### @li This is the size, in bytes, of the range. Decode range is from Base to (Base+Size).
  ### @li Permitted Choices: (Type: Value (Default: 0x0000)

  ### @brief The PCD declaration is a byte packed array. For example:gEfiAmdAgesaPkgTokenSpaceGuid.PcdEspiIoMmioDecode|{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}|VOID*|0x000A6005
  ### @code
  ### PcdEspiIoMmioDecode =
  ###  {#eSPI decode ranges
  ###      # 1,  ENABLE     - ESPI IO Decode Range
  ###      # 2,3,BASE       - ESPI IO Decode Range base - support 4Ch/4Eh/4Fh 62h/66h 600h-6FFh
  ###      # 4,  SIZE       - ESPI IO Decode Range size - #bytes-1
  ###    { # IO decode ranges
  ###      {0x01, {0x4C, 0x03}, 0x00}, # Enabled, base=4C:4F, size=3
  ###      {0x01, {0x00, 0x06}, 0xFF}, # Enabled, base=0600:06FF, size=FF
  ###      {0x00, {0x00, 0x00}, 0x00}, # Disabled
  ###      {0x01, {0x62, 0x00}, 0x04}, # Enabled, base=62:66, size=4
  ###    }
  ###    {# MMIO decode ranges
  ###      # 1,ENABLE       - ESPI MMIO Decode Range
  ###      # 2,3,4,5,BASE   - ESPI MMIO Decode Range base
  ###      # 6,7,SIZE       - ESPI MMIO Decode Range size
  ###      { #ESPI MMIO decode ranges
  ###      {0x01, {0x04, 0x03, 0x02, 0x01}, {0x02, 0x01}, # Enabled, base=0x01020304, size=0x0102
  ###      {0x00, {0x00, 0x00, 0x00, 0x00}, {0x00, 0x00}, # Disabled
  ###      {0x00, {0x00, 0x00, 0x00, 0x00}, {0x00, 0x00}, # Disabled
  ###      {0x00, {0x00, 0x00, 0x00, 0x00}, {0x00, 0x00}  # Disabled
  ###    }
  ###  }
  ### @endcode
  ### @brief Formatting rules for the PCD files force the above definition. For illustration, an equivalent C structure style declaration is provided below.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEspiIoMmioDecode|{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}|VOID*|0x000A6005
  #   #ESPI IO decode ranges
  #   0x00,   0x00, 0x00,   0x00, \     #ESPI IO decode range 0
  #   0x00,   0x00, 0x00,   0x00, \     #ESPI IO decode range 1
  #   0x00,   0x00, 0x00,   0x00, \     #ESPI IO decode range 2
  #   0x00,   0x00, 0x00,   0x00, \     #ESPI IO decode range 3
  #   #ESPI MMIO decode range parameter
  #   0x00,   0x00, 0x00, 0x00, 0x00,   0x00, 0x00, \  #ESPI MMIO decode range 0
  #   0x00,   0x00, 0x00, 0x00, 0x00,   0x00, 0x00, \  #ESPI MMIO decode range 1
  #   0x00,   0x00, 0x00, 0x00, 0x00,   0x00, 0x00, \  #ESPI MMIO decode range 2
  #   0x00,   0x00, 0x00, 0x00, 0x00,   0x00, 0x00}    #ESPI MMIO decode range 3

  ### V2 uses the same structure but moves to 16 IO blocks and 5 MMIO regions
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEspiIoMmioDecodeV2|{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}|VOID*|0x000A600F
  #   #ESPI IO decode ranges
  #   0x00,   0x00, 0x00,   0x00, \     #ESPI IO decode range 0
  #   0x00,   0x00, 0x00,   0x00, \     #ESPI IO decode range 1
  #   0x00,   0x00, 0x00,   0x00, \     #ESPI IO decode range 2
  #   0x00,   0x00, 0x00,   0x00, \     #ESPI IO decode range 3
  #   0x00,   0x00, 0x00,   0x00, \     #ESPI IO decode range 4
  #   0x00,   0x00, 0x00,   0x00, \     #ESPI IO decode range 5
  #   0x00,   0x00, 0x00,   0x00, \     #ESPI IO decode range 6
  #   0x00,   0x00, 0x00,   0x00, \     #ESPI IO decode range 7
  #   0x00,   0x00, 0x00,   0x00, \     #ESPI IO decode range 8
  #   0x00,   0x00, 0x00,   0x00, \     #ESPI IO decode range 9
  #   0x00,   0x00, 0x00,   0x00, \     #ESPI IO decode range 10
  #   0x00,   0x00, 0x00,   0x00, \     #ESPI IO decode range 11
  #   0x00,   0x00, 0x00,   0x00, \     #ESPI IO decode range 12
  #   0x00,   0x00, 0x00,   0x00, \     #ESPI IO decode range 13
  #   0x00,   0x00, 0x00,   0x00, \     #ESPI IO decode range 14
  #   0x00,   0x00, 0x00,   0x00, \     #ESPI IO decode range 15
  #   #ESPI MMIO decode range parameter
  #   0x00,   0x00, 0x00, 0x00, 0x00,   0x00, 0x00, \  #ESPI MMIO decode range 0
  #   0x00,   0x00, 0x00, 0x00, 0x00,   0x00, 0x00, \  #ESPI MMIO decode range 1
  #   0x00,   0x00, 0x00, 0x00, 0x00,   0x00, 0x00, \  #ESPI MMIO decode range 2
  #   0x00,   0x00, 0x00, 0x00, 0x00,   0x00, 0x00, \  #ESPI MMIO decode range 3
  #   0x00,   0x00, 0x00, 0x00, 0x00,   0x00, 0x00}    #ESPI MMIO decode range 4
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEspiDevice0Enable|FALSE|BOOLEAN|0x000A6019                           # Enable Espi Device0
  ### @} end eSPI Enablement
  ### @endcond

  ### @cond (Huashan)
  ### @details Send command to SMU
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataActLWkaEnable|TRUE|BOOLEAN|0x000A6006
  ### @endcond

  ### @cond (Bixby)
  ### @details Send command to SMU
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdBixbySataActLWkaEnable|TRUE|BOOLEAN|0x000A6007
  ### @endcond

  ### @cond (Huashan||Keith||Shasta||Tacoma||Yuntai)
  ### @brief ESPI IRQ Polarity
  ### @details ESPI IRQ Polarity
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEspiIrqPolarity|0x00000000|UINT32|0x000A6008
  ### @endcond

  ### @cond (Huashan||Keith||Shasta||Tacoma||Yuntai)
  ### @brief ESPI IRQ Mask
  ### @details ESPI IRQ Mask
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEspiIrqMask|0x00FFFFFF|UINT32|0x000A6009
  ### @endcond

  ### @cond (Keith||Shang||Shasta||Tacoma||Yuntai)
  ### @brief ACDC Timer Enable
  ### @details Enalbe or disable Time and Alarm Device (ACPI000E) reported in ACPI.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcdcTimerEnable|FALSE|BOOLEAN|0x000A600A
  ### @endcond

  ### @cond (Huashan||Keith||Shasta||Tacoma||Yuntai)
  ### @brief ESPI channel
  ### @details Disable PD and enable PU for Alert/Datas PAD.
  ### @li 0x00 - channel 0 share with SPI
  ### @li 0x01 - channel 1 share with LPC
  ### @li 0x0F - Auto depend on PSP option
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEspiChannel|0x0F|UINT8|0x000A600B
  ### @endcond

  ### @cond (Keith||Shang||Shasta||Tacoma||Yuntai)
  ### @brief BP_X48M0 Clock Output Enable
  ### @details BP_X48M0 Clock Output Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdBpX48M0ClockEnable|TRUE|BOOLEAN|0x000A600C
  ### @endcond

  ### @cond (Keith||Shasta||Tacoma||Yuntai)
  ### @brief Turn off XTAL during S3/S5
  ### @details Turn off XTAL during S3/S5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTurnOffXtalS3S5|TRUE|BOOLEAN|0x000A600D
  ### @endcond

  ### @cond (Huashan||Keith||Shasta||Tacoma||Yuntai)
  ### @brief ESPI Alert Mode
  ### @details ESPI Alert Mode
  ### @li 0 - I/O[1] pin is used to signal the Alert event
  ### @li 1 - A dedicated Alert# pin is used to signal the Alert event
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEspiAlertMode|0x01|UINT8|0x000A600E
  ### @endcond

#----------------------------------------------------------------------------
#-  FCH Dynamic PCDs
### Set Doxy_path: "PCD-FCH-SATA.h"
#----------------------------------------------------------------------------

  ### @cond (RMB||RN||SSP)
  ### @brief Name of SATA root bridge in ASL.
  ### @details SATA DXE driver update the name with PCD value.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataAcpiTableRootBridgeName|"GP18"|VOID*|0x000A60010
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSata1AcpiTableRootBridgeName|"GP18"|VOID*|0x000A60017
  ### SATA0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSata0AcpiTableDeviceName|"SATA"|VOID*|0x000A60011
  ### SATA1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSata1AcpiTableDeviceName|"SAT1"|VOID*|0x000A60012
  ### @endcond

#----------------------------------------------------------------------------
#-  FCH Dynamic PCDs
### Set Doxy_path: "PCD-FCH.h"
#----------------------------------------------------------------------------

  ### @cond (Keith||Shasta||Tacoma||Yuntai)
  ### @brief ACDC timer Timezone and Daylight
  ### @details ACDC timer Timezone and Daylight field location in CMOS RAM
  ### @li 0xFF - Invalid
  ### @li 0x01 - Valid
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchAcdcTimerTimezoneDaylightValid|0xFF|UINT8|0x000A60013
  ### CMOS Offset of Low byte
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchAcdcTimerTimezoneCmosOffsetLow|0x34|UINT8|0x000A60014
  ### CMOS Offset of High byte
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchAcdcTimerTimezoneCmosOffsetHigh|0x35|UINT8|0x000A60015
  ### CMOS Offset of Day Light saving time
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchAcdcTimerDaylightCmosOffset|0x36|UINT8|0x000A60016
  ### @endcond

  ### @cond (Keith||Tacoma||Yuntai)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXtal48MPadPowerSaving|TRUE|BOOLEAN|0x000A6018                        # Enable 48M XTAL_PAD power saving
  ### @endcond

  ### @brief SCI bitmap for CPPC
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCppcSciBitMap|0|UINT32|0x000A6020
  ### @cond (RN||CZN||FF3||RMB||RPL||PHX)
  ### @brief Thermal control variables (Slow PPT Limit)
  ### @brief APU Only sPPT Limit in mW
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSlowPPTLimitApuOnly|0|UINT32|0x000A6021
  ### @endcond
  ### @cond (Huashan||Songshan||MI3)
  ### @brief Enable/Disable Boot Timer function in post time.
  ### @li False - Disable the boot timer.
  ### @li True - Boot Timer is used.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdBootTimerEnable|TRUE|BOOLEAN|0x000A6022
  ### @endcond
  ### @cond (Songshan||MI3)
  ### @details Indicates the reset type triggered by Boot Timer.
  ### @li 0x00 - Warm Reset.
  ### @li 0x01 - Cold Reset.
  ### @li Other - reserved.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdBootTimerResetType|0|UINT8|0x000A6023
  ### @brief Enables trapping of CF9 warm and cold resets. This control must be set to TRUE on systems that support NVDIMM-N.
  ### @li FALSE - No reset trap. NVDimm save is disabled.
  ### @li TRUE - Trap the resets to perform NVDIMM save.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdNvdimmEnable|FALSE|BOOLEAN|0x000A6024
  ### @endcond
  ### @cond (RMB||RPL||PHX)
  ### @brief Hid Control
  ### @details The Hid control is designed for the customers to change SpiReadMode/SpiSpeed/Spi_spd6/Spi_spd7
  ###  PCD structure that has an effective structure is as follows:
  ### @code
  ###  struct {
  ###      UINT8     SpiReadMode;
  ###      UINT8     SpiSpeed;
  ###      UINT8     Spi_spd6;
  ###      UINT8     Spi_spd7;
  ###  } HidControl[2];
  ### @endcode
  ### @li SpiReadMode - Default = 5: Quad-io <_1-4-4_>. Other supporting modes are 5: Dual-io <_1-2-2_> and 7: Fast read <_1-1-1_>
  ### @li SpiSpeed - Default is 1: 33.33MHz
  ### @li                       1: 33.33MHz
  ### @li                       2: 22.22MHz
  ### @li                       3: 16.66MHz
  ### @li                       5: 800KHz
  ### @li                       6: spi_spd6, defined in SPIx6C[5:0], default = 50MHz
  ### @li                       7: spi_spd7, defined in SPIx6C[13:8], default = 4MHz
  ### @li Spi_spd6 and Spi_spd7 - Default are 0x4: 50MHz, 200MHz/4 and 0x32: 4MHz, 200MHz/50
  ### Formatting rules for this PCD file forces a byte packed array definition.
  ### RPL only has one Hid controller.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchHidControl|{5, 1, 4, 32, 5, 1, 4, 32}|VOID*|0x000A6025
  ### @endcond

  ### @cond (PHX||STX||STXH||KRK)
  ### @brief Turn off XTAL during S0i3
  ### @details Turn off XTAL during S0i3
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTurnOffXtalS0i3|TRUE|BOOLEAN|0x000A6026
  ### @endcond

#----------------------------------------------------------------------------
#-  FCH Dynamic PCDs
### Set Doxy_path: "PCD-FCH-USB.h"
#----------------------------------------------------------------------------

  ### @cond (RMB||PHX)
  ### @name USB4 pre-CM Enables
  ### @{
  ### This group of controls select if enable pre-CM in the system.
  ### @li TRUE  - USB4 pre-CM is enabled.
  ### @li FALSE - USB4 pre-CM is disabled.
  ###
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdUsb4CmEnable|TRUE|BOOLEAN|0x00B40000  #TRUE:  Enable
                                                                            #FALSE: Disable
  ### @endcond
  ### @cond (RMB||PHX||STX)
  ### @name USB4 Router Enables
  ### @{
  ### This group of controls select which USB4 Router controllers are active
  ### in the system.
  ### @li TRUE  - The controller is active.
  ### @li FALSE - This controller is turned off.
  ###
  ### USB4 Router0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt0En|TRUE|BOOLEAN|0x000FC040
  ### USB4 Router1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt1En|TRUE|BOOLEAN|0x000FC050
  ### @}
  ### @endcond

  ### @cond (RMB||PHX||STX)
  ### @name USB4 Router Connected USB3 controller Enables
  ### @{
  ### This group of controls select which USB3 Controllers that connect
  ### to USB4 Router are active in the system. This control is a
  ### bit-mapped list, each bit representing a controller; 0=disabled, 1=active.
  ### @li BIT0   - USB3 Controller0 connect to USB4.
  ### @li BIT1-7 - USB3 controller1-7.
  ###
  ### USB4 Router0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt0XhciEn|0xFF|UINT8|0x000FC041
  ### USB4 Router1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt1XhciEn|0XFF|UINT8|0x000FC051
  ### @}
  ### @endcond

  ### @cond (RMB||PHX||STX)
  ### @name USB4 Router USB3 Tunnel Enables
  ### @{
  ### This group of controls select whether USB3 Tunnel fucntion
  ### is active in the USB4 Router.
  ### @li TRUE  - The USB3 tunnel is active.
  ### @li FALSE - The USB3 tunnel is turned off.
  ###
  ### USB4 Router0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt0Usb3TnlEn|TRUE|BOOLEAN|0x000FC042
  ### USB4 Router1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt1Usb3TnlEn|TRUE|BOOLEAN|0x000FC052
  ### @}
  ### @endcond

  ### @cond (RMB||PHX||STX)
  ### @name USB4 Router PCIe Tunnel Enables
  ### @{
  ### This group of controls select whether PCIe Tunnel fucntion
  ### is active in the USB4 Router.
  ### @li TRUE  - The PCIe tunnel is active.
  ### @li FALSE - The PCIe tunnel is turned off.
  ###
  ### USB4 Router0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt0PcieTnlEn|TRUE|BOOLEAN|0x000FC043
  ### USB4 Router1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt1PcieTnlEn|TRUE|BOOLEAN|0x000FC053
  ### @}
  ### @endcond

  ### @cond (RMB||PHX||STX)
  ### @name USB4 Router Display Port Tunnel Enables
  ### @{
  ### This group of controls select whether DP Tunnel fucntion
  ### is active in the USB4 Router.
  ### @li TRUE  - The DP tunnel is active.
  ### @li FALSE - The DP tunnel is turned off.
  ###
  ### USB4 Router0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt0DpTnlEn|TRUE|BOOLEAN|0x000FC044
  ### USB4 Router1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt1DpTnlEn|TRUE|BOOLEAN|0x000FC054
  ### @}
  ### @endcond

  ### @cond (RMB||PHX||STX)
  ### @name USB4 Router TBT3 Compatibility support Enables
  ### @{
  ### This group of controls select whether Thunderbolt3 Cimpatibility
  ### support function is active in the USB4 Router.
  ### @li TRUE  - The TBT3 support is active.
  ### @li FALSE - The TBT3 support is turned off.
  ###
  ### USB4 Router0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt0Tbt3En|TRUE|BOOLEAN|0x000FC045
  ### USB4 Router1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt1Tbt3En|TRUE|BOOLEAN|0x000FC055
  ### @}
  ### @endcond

  ### @cond (RMB||PHX||STX)
  ### @name USB4 Port RS-FEC Request
  ### @{
  ### This group of controls select whether RS-FEC Request
  ### support function is active in the USB4 Router.
  ### @li TRUE  - The RS-FEC Request is made.
  ### @li FALSE - The RS-FEC Request is not made.
  ### @li BIT0    RT0.
  ### @li BIT1    RT1.
  ###
  ### USB4 Gen 3 RS-FEC
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Gen2RsFec|0xFF|UINT8|0x000FC056
  ### USB4 Gen 2 RS-FEC
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Gen3RsFec|0xFF|UINT8|0x000FC057
  ### @}
  ### @endcond

  ### @cond (RMB||PHX||STX)
  ### @name USB4 Link Speed
  ### @{
  ###
  ### @li TRUE  - The RS-FEC Request is made.
  ### @li FALSE - The RS-FEC Request is not made.
  ### @li BIT0    RT0.
  ### @li BIT1    RT1.
  ###
  ### USB4 Link Speed
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Gen3Support|0xFF|UINT8|0x000FC058
  ### @}
  ### @endcond

#----------------------------------------------------------------------------
#-  FCH Fixed PCDs
#-
### Set Doxy_path: "PCD-FCH-I2C-BUS-HZ.h"
#----------------------------------------------------------------------------

  ### @name I2C Bus Frequency
  ### @{
  ### These PCDs allow a platform to set the bus frequency
  ### @li 0 - Standard Speed
  ### @li 1 - Fast Speed
  ### @li 2 - High Speed

  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c0BusFrequency|0x00000000|UINT32|0x000FC060
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c1BusFrequency|0x00000000|UINT32|0x000FC061
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c2BusFrequency|0x00000000|UINT32|0x000FC062
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c3BusFrequency|0x00000000|UINT32|0x000FC063
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c4BusFrequency|0x00000000|UINT32|0x000FC064
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c5BusFrequency|0x00000000|UINT32|0x000FC065
  ### @} end of I2C bus frequency

#----------------------------------------------------------------------------
#-  FCH Fixed PCDs
#-
### Set Doxy_path: "PCD-FCH.h"
#----------------------------------------------------------------------------

  ### @brief FCH release SPD Host Ctrl
  ### @details Release SPD Host Ctrl so that BMC can take ownership of I2C/I3C bus.
  ### @li TRUE  - Release SPD Host Ctrl.
  ### @li FALSE - Not release SPD Host Ctrl.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchSpdHostCtrlRelease|FALSE|BOOLEAN|0x000FC066

  ### @cond (RMB||RPL||PHX)
  ### @brief USB PD interrupt mode enablement
  ### @details These PCDs allow a platform to enable USB host controller interrupt mode.
  ### @li BIT0 - USB3HC0
  ### @li BIT1 - USB3HC1
  ### @li BIT2 - USB4RT0
  ### @li BIT3 - USB4RT1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPdInterruptModeEn|0x00|UINT8|0x000FC067
  ### @endcond

  ### @brief Send StartDimmTelemetryReading message to PMFW
  ### @details Send.StartDimmTelemetryReading message to PMFW at the end of POST
  ### @li TRUE  - Send message.
  ### @li FALSE - Not send message.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchDimmTelemetry|TRUE|BOOLEAN|0x000FC068

  ### @cond !BRH
  ### @brief I3C mode speed
  ### @details Transfer speed in I3C mode.
  ### In ABL, a similar APCB token "APCB_TOKEN_UID_FCH_I3C_TRANSFER_SPEED" is used to control the
  ### I3C transfer speed. Default speed is SDR2.
  ### @li 0 - SDR0 = 12.5 MHz.
  ### @li 2 - SDR2 = 6 MHz. (default)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI3cSpeed|0x02|UINT8|0x000FC069
  ### @endcond


  ### @cond (PHX||STX)
  ### @brief USB4 CLx enablement
  ### @{
  ### This group of controls enable or disable a host controller's CLx feature
  ### @li TRUE  - The CLx feature is enabled.
  ### @li FALSE - The CLx feature is disabled.
  ###
  ### USB4 Router0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt0ClxEnable|FALSE|BOOLEAN|0x000FC070
  ### USB4 Router1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt1ClxEnable|FALSE|BOOLEAN|0x000FC071
  ### @}
  ### @endcond

  ### @cond (PHX||STX)
  ### @brief USB4 P4.PG enablement
  ### @{
  ### This group of controls enable or disable a host controller's P4.PG feature
  ### @li TRUE  - The P4.PG feature is enabled.
  ### @li FALSE - The P4.PG feature is disabled.
  ###
  ### USB4 Router0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt0P4PgEnable|FALSE|BOOLEAN|0x000FC074
  ### USB4 Router1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt1P4PgEnable|FALSE|BOOLEAN|0x000FC075
  ### @}
  ### @endcond

  ### @cond (PHX||STX)
  ### @brief Supported Link Speeds
  ### @{
  ### This group of controls enable or disable a host controller's P4.PG feature
  ### @li 0 - Support Gen2 only
  ### @li 1 - Support Gen2/Gen3
  ### @li Other - Reserved
  ###
  ### USB4 Router0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt0LinkSpeed|0x1|UINT8|0x000FC076
  ### USB4 Router1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt1LinkSpeed|0x1|UINT8|0x000FC077
  ### @}
  ### @endcond

  ### @cond (PHX||STX)
  ### @brief USB4 USB3 Features
  ### @{
  ### This group of controls enable or disable a host controller's various USB3 features
  ### @li BIT0 - Debug Streaming Disable
  ### @li Other - Reserved
  ###
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4DebugSCEnable|FALSE|UINT16|0x000FC078
  ### @}
  ### @endcond

  ### @cond (STX)
  ### @brief Onboard Retimer
  ### @{
  ### This group of indications if there is an onboard retimer
  ### @li TRUE  - There are onboard retimers.
  ### @li FALSE - There are no onboard retimers.
  ###
  ### USB4 Router0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt0OnboardRetimer|TRUE|BOOLEAN|0x000FC079
  ### USB4 Router1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt1OnboardRetimer|TRUE|BOOLEAN|0x000FC07A
  ### @}
  ### @endcond

  ### @cond (STX)
  ### @brief D3Z8 ZPR Function Configuration
  ### @details These PCDs allow a platform to Enable/Disable ZPR function for a controller during Z3D8.
  ### @li BIT0 - USB3HC0
  ### @li BIT1 - USB3HC1
  ### @li BIT2 - USB3HC2 (Reserved)
  ### @li BIT3 - USB3HC3 (USBADP0)
  ### @li BIT4 - USB3HC4 (USBADP1)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdZ3D8ZprEnable|0x00|UINT8|0x000FC07B
  ### @endcond

  ### @cond (STX)
  ### @brief D0Z8 ZPR Function Configuration
  ### @details These PCDs allow a platform to Enable/Disable ZPR function for a controller during Z0D8.
  ### @li BIT0 - USB3HC0
  ### @li BIT1 - USB3HC1
  ### @li BIT2 - USB3HC2 (Reserved)
  ### @li BIT3 - USB3HC3 (USBADP0)
  ### @li BIT4 - USB3HC4 (USBADP1)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdZ0D8ZprEnable|0x00|UINT8|0x000FC07C
  ### @endcond

  ### @cond (STX)
  ### @brief USB4 ZPR Function Configuration
  ### @{
  ### This group of controls enable or disable a host controller's ZPR function
  ### @li TRUE  - The ZPR function is enabled.
  ### @li FALSE - The ZPR function is disabled.
  ###
  ### USB4 Router0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt0ZprEnable|FALSE|BOOLEAN|0x000FC07D
  ### USB4 Router1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt1ZprEnable|FALSE|BOOLEAN|0x000FC07E
  ### @}
  ### @endcond

  ### @cond (STX)
  ### @brief VDDZ Function - SNPS External Clock Gating Configuration
  ### @{
  ### These PCDs allow a platform to Enable/Disable VDDZ Function for SNPS external clock gating.
  ###
  ### USB4 Router0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt0VddzSnpsClkGatingEnable|FALSE|BOOLEAN|0x000FC0A0
  ### USB4 Router1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt1VddzSnpsClkGatingEnable|FALSE|BOOLEAN|0x000FC0A1
  ### @}
  ### @endcond

  ### @cond (STX)
  ### @brief VDDZ Function - SNPS U3 External Clock Gating Configuration
  ### @{
  ### These PCDs allow a platform to Enable/Disable VDDZ Function for SNPS U3 external clock gating.
  ###
  ### USB4 Router0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt0VddzSnpsU3ClkGatingEnable|FALSE|BOOLEAN|0x000FC0A2
  ### USB4 Router1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt1VddzSnpsU3ClkGatingEnable|FALSE|BOOLEAN|0x000FC0A3
  ### @}
  ### @endcond

  ### @cond (STX)
  ### @brief VDDZ Function - D0 Z-State Configuration
  ### @{
  ### These PCDs allow a platform to Enable/Disable VDDZ Function for D0 Z-State.
  ###
  ### USB4 Router0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt0VddzD0ZState|FALSE|BOOLEAN|0x000FC0A4
  ### USB4 Router1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt1VddzD0ZState|FALSE|BOOLEAN|0x000FC0A5
  ### @}
  ### @endcond

  ### @cond (STX)
  ### @brief XHCI Controller P4.PG
  ### @details These PCDs allow a platform to Enable/Disable P4.PG feature for a controller.
  ### @li BIT0 - USB3HC0
  ### @li BIT1 - USB3HC1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXhciP4PgEnable|0x00|UINT8|0x000FC08B
  ### @endcond

  ### @cond (PHX||STX)
  ### @brief USB4 - SuperSpeed Port of USB3 Adapter Host Controller Configuration
  ### @{
  ### This group of controls enable or disable the SuperSpeed port of USB3 Adapter host controller
  ### @li TRUE  - SS port is enabled.
  ### @li FALSE - SS port is disabled.
  ###
  ### USB4 Router0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt0SSPortDisable|0x00|UINT8|0x000FC08C
  ### USB4 Router1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4Rt1SSPortDisable|0x00|UINT8|0x000FC08D
  ### @}
  ### @endcond

  ### @brief IxC Telemetry ports Fencing Control
  ### @details This PCD controls the Fencing off for I2C/I3C ports which are involved in DDR Telemetry.
  ### Note: If IxC Telemetry ports Fencing Control option is disabled, there is a risk of collision happening between x86 accessing IxC and PMFW running DDR Telemetry which can cause undefined behavior.
  ### @li TRUE  - Fencing Enabled. This will allow x86 to send command to PSP to fence off the Ixc ports so that PMFW has the exclusive ownership to the ports.
  ### @li FALSE - Fencing Disabled.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchIxcTelemetryPortsFenceControl|TRUE|BOOLEAN|0x000FC08E

  ### NBIO PCDs DYNAMIC
#----------------------------------------------------------------------------
#-  NBIO Dynamic PCDs
### Set Doxy_path: "PCD-NBIO.h"
#----------------------------------------------------------------------------
  #NBIO
  ### @{
  ### @brief Summary
  ### <p> The NBIO Driver is responsible for the PCIe Root Complex configuration.
  ### @details Description <p> The NBIO Driver is comprised of several sub-components that are
  ### responsible for configuration of the northbridge IO related functions
  ### , including:
  ### - Distributed IO crossbar (DXIO)
  ### <p> The high speed IO ports in the NorthBridge are capable of driving one of
  ### several types of physical connections.
  ### Each NB port can be configured to drive:
  ### - PCIe Root Complex
  ### - SATA/SATA Express physical connection
  ### - XGBe, GBe physical connection
  ### <p>This component is responsible for configuring the DXIO subsystem,
  ### the physical connections for devices that share the PHY connections,
  ### and internal GMI or GOP (GMI Over PCIe ) connections. The layout and
  ### assignment of the ports and devices is imported from the platform driver
  ### via the Root Complex PPI.
  ### - NBIF bus interface to NBIO components
  ### <p>This component initializes the device registers for services used for internal communications.
  ### @}
#----------------------------------------------------------------------------
#-  NBIO Dynamic PCDs
### Set Doxy_path: "PCD-NBIO-SSID.h"
#----------------------------------------------------------------------------

  ### @name Platform SSIDs
  ### These are the PCIe Subsystem IDs for different platforms.
  ### @{

  ### @cond (BIXBY|| RV|| ZP|| BA|| FF3|| GN|| RMB|| RN|| RPL|| SSP|| PHX|| RS||BRH)
  ### @brief This 16-bit value defines the subsystem device ID assigned to PCIe
  ### @brief Permitted Choices: (Type: Value)(Default: 0x1453)
  ### @li 0x0 - Use the default power-up setting.
  ### @li !=0x0 - Value to set as the SSID.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPcieSubsystemDeviceID|0x1453|UINT16|0x00041500
  ### @endcond

  ### @cond (BIXBY|| RV|| ZP|| BA|| FF3|| GN|| RMB|| RN|| RPL|| SSP|| PHX|| RS||BRH)
  ### @brief This 16-bit value defines the subsystem vendor ID assigned to PCIe
  ### @brief Permitted Choices: (Type: Value)(Default: 0x1022)
  ### @li 0x0 - Use the default power-up setting.
  ### @li !=0x0 - Value to set as the SSID.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPcieSubsystemVendorID|0x1022|UINT16|0x00041501
  ### @endcond

  ### @cond (CZ)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCfgGnbPcieSSID|0x12341022|UINT32|0x00041502
  ### @endcond
  ### @cond (RV || RMB || RN || RPL || PHX)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCfgGnbIGPUSSID|0|UINT32|0x00041503
  ### @endcond

  ### @cond (RV)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCfgGnbHDAudioSSID|0|UINT32|0x00041504
  ### @endcond

  ### @cond (BIXBY||ZP||BA||FF3||GN||MR||RMB||RN||RPL||SSP||PHX||RS||BRH)
  ### @brief This 32-bit value defines the subsystem ID and subsystem vendor ID assigned to the NBIO controller.
  ### @brief Permitted Choices: (Type: Value)(Default: 0)
  ### @li 0x0 - Use the default power-up settings.
  ### @li !=0x0 - Upper 16 bits = subsystem ID. Lower 16 bits = subsystem vendor ID
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgNbioSsid|0|UINT32|0x00041505
  ### @endcond

  ### @cond (BIXBY||RV||ZP||BA||FF3||GN||MR||RMB||RN||RPL||SSP||PHX||RS||BRH)
  ### @brief This 32-bit value defines the subsystem ID and subsystem vendor ID assigned to the IOMMU controller.
  ### @brief Permitted Choices: (Type: Value)(Default: 0)
  ### @li 0x0 - Use the default power-up settings.
  ### @li !=0x0 - Upper 16 bits = subsystem ID. Lower 16 bits = subsystem vendor ID
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgIommuSsid|0|UINT32|0x00041506
  ### @endcond

  ### @cond (BIXBY||RV||ZP||BA||GN||MR||RMB||RN||RPL||SSP||PHX||RS||BRH)
  ### @brief This 32-bit value defines the subsystem ID and subsystem vendor ID assigned to the PSP CCP controller.
  ### @brief Permitted Choices: (Type: Value)(Default: 0)
  ### @li 0x0 - Use the default power-up settings.
  ### @li !=0x0 - Upper 16 bits = subsystem ID. Lower 16 bits = subsystem vendor ID
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPspccpSsid|0|UINT32|0x00041507
  ### @endcond

  ### @cond (BIXBY||ZP||BA||GN||SSP||RS)
  ### @brief This 32-bit value defines the subsystem ID and subsystem vendor ID assigned to the NTB CCP controller
  ### @brief Permitted Choices: (Type: Value)(Default: 0)
  ### @li 0x0 - Use the default power-up settings.
  ### @li !=0x0 - Upper 16 bits = subsystem ID. Lower 16 bits = subsystem vendor ID
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgNtbccpSsid|0|UINT32|0x00041508
  ### @endcond

  ### @cond (RV||ZP)
  ### @brief This 32-bit value defines the subsystem ID assigned to the internal XGBE controller.
  ### @li 0x0000 - Use the default power-up setting.
  ### @li !=0 - Value to set as the SSID.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgXgbeSsid|0|UINT32|0x00041509
  ### @endcond

  ### @cond (BIXBY||ZP||BA||GN||SSP||RS||BRH)
  ### @brief This 32-bit value defines the subsystem ID and subsystem vendor ID assigned to the NBIFs Func0.
  ### @brief Permitted Choices: (Type: Value)(Default: 0)
  ### @li 0x0 - Use the default power-up settings.
  ### @li !=0x0 - Upper 16 bits = subsystem ID. Lower 16 bits = subsystem vendor ID
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgNbifF0Ssid|0|UINT32|0x0004150A
  ### @endcond

  ### @cond (BIXBY||RV||ZP||RMB||RN||RPL||SSP||PHX)
  ### @brief This 32-bit value defines the subsystem ID assigned to the Root Controllers.
  ### @li 0x0000 - Use the default power-up setting.
  ### @li !=0 - Value to set as the SSID.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgNbifRCSsid|0|UINT32|0x0004150B
  ### @endcond

  ### @cond (ZP||BA||GN||SSP||RS)
  ### @brief This 32-bit value defines the subsystem ID and subsystem vendor ID assigned to the NTB controller
  ### @brief Permitted Choices: (Type: Value)(Default: 0)
  ### @li 0x0 - Use the default power-up setting.
  ### @li !=0x0 - Upper 16 bits = subsystem ID. Lower 16 bits = subsystem vendor ID
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgNtbSsid|0|UINT32|0x0004150C
  ### @endcond

  ### @cond (BA||GN||SSP)
  ### Subsystem ID for PTDMA
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPtDmaSsid|0|UINT32|0x0004150D
  ### @endcond

  ### @cond (BA||GN||SSP)
  ### Subsystem ID for Internal PCIe GPP Bridge 0 to bus B
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgIntPcieGppBdgSsid|0|UINT32|0x0004150E
  ### @endcond

  ### @cond (RMB||RN||RPL||PXH)
  ### Be able to assign the ACP SSID go through this PCD
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAcpSsid|0|UINT32|0x00041510
  ### @endcond

  ### @cond (RS||BRH)
  ### @brief Enable this setting to use 2 symbols per clock for devices at Gen 4 speed.
  ### @li FALSE   - Use default symbols per clock
  ### @li TRUE    - Use 2 symbols per clock
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEnable2SpcGen4|FALSE|BOOLEAN|0x00041511
  ### @endcond

  ### @cond (BRH)
  ### @brief Enable this setting to use 2 symbols per clock for devices at Gen 5 speed.
  ### @li FALSE   - Use default symbols per clock
  ### @li TRUE    - Use 2 symbols per clock
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEnable2SpcGen5|TRUE|BOOLEAN|0x00041512
  ### @endcond

  ### @cond (BRH)
  ### @brief Enable the DFE tap count function
  ### @li FALSE   - Disable this fuction
  ### @li TRUE    - Enable this function
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDfeTapEnable|FALSE|BOOLEAN|0x00041513
  ### @endcond

  ### @cond (BRH)
  ### @brief DFE tap count. Valid values are 0, 2 or 3.
  ### @li 0x0    - 0 Tap DFE
  ### @li 0x2    - 2 Tap DFE
  ### @li 0x3    - 3 Tap DFE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDfeTapCount|2|UINT8|0x00041514
  ### @endcond
  ### @} end of Platform SSIDs

#----------------------------------------------------------------------------
#-  NBIO Dynamic PCDs
### Set Doxy_path: "PCD-NBIO.h"
#----------------------------------------------------------------------------

  ### @name General NBIO Controls

  ### @cond (RMB)
  ### @brief Control SSC of PLL5
  ### @li 0   - Enable
  ### @li 1   - Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPll5SscDisable|0x0|UINT8|0x0004153F
  ### @endcond

  ### @cond (SSP||VMR||CPK||CGL||BRH)
  ### @brief Ioapic sb feature
  ### @li 1 - the system is in PIC mode
  ### @li 0 - the system is in APIC mode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgIoapicSbFeature|1|UINT8|0x0004151D
  ### @endcond

  ### @cond (SSP||VMR||CPK||CGL)
  ### @brief This value controls PCIE Multicast capability supports. A feature and associated mechanisms that enable a single Posted Request TLP sent by a source to be
  ### distributed to multiple targets.
  ### @li FALSE - ('normal' operation) communications are point to point.
  ### @li TRUE - Enable the multi-cast feature.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgMcCapEnable|FALSE|BOOLEAN|0x0004E001
  ### @endcond

  ### @cond (SSP||VMR||CPK||CGL||RS||BRH)
  ### @brief Receiver Error Report Enable
  ### @li TRUE - Enable receiver error report
  ### @li FALSE - Disable receiver error report
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgRcvErrEnable|FALSE|BOOLEAN|0x0004E002
  ### @endcond

  ### @cond (GN || RS || MI3 || BRH)
  ### @brief This value controls whether or not to show unused PCIE ports to the OS. By exposing the unused ports, it provides the ability to control the enable/disable of
  ### those ports and know the NUMA affinity so as to determine which port to use when a new device is added.
  ### @li 0: FALSE - disabled, hide unused PCIE port.
  ### @li 1: TRUE - enabled, expose all unused PCIE ports.
  ### @li 0xFF: Auto - Based on platform configuration.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCfgExposeUnusedPciePorts|0xFF|UINT8|0x0004E003
  ### @endcond

  ### @cond (MI200||ZP||FF3||RN||SSP||BA||GN||RMB||RPL||SSP||PHX||RS||MI3||BRH)
  ### @brief Enables Advanced Error Reporting Capability
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE - This option is active.
  ### @li FALSE - This option is turned off.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAEREnable|TRUE|BOOLEAN|0x0004F000
  ### @endcond

  ### @cond (RV|| PHX|| RMB|| RN|| RPL|| VN|| V10)
  ### @brief This value defines the enablement for Platform level power management. This feature manages power consumption between the APU and an AMD discrete GPU in the platform.
  ### @li TRUE - PeApm is enabled if a discrete GPU is found
  ### @li FALSE - PeApm will not be enabled.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPeApmEnable|0|UINT8|0x0004F001
  ### @endcond

  ### @cond (BIXBY|| RV|| ZP|| FF3|| RN|| BA|| GN|| RMB|| RPL|| SSP|| PHX|| RS ||MI3||BRH)
  ### @brief This value controls whether AGESA will enable Access Control Services (ACS). The PCIe RAS feature [F17M00][F17M30][F17M60], this recommendation will be enforced
  ### with a dependency that ACS cannot be enabled without AER also being enabled. On these platforms even though this PCD is set =TRUE, the code will decide to not enable
  ### ACS if PcdCfgAEREnable is not set =TRUE also.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE - AGESA will enable ACS.
  ### @li FALSE - ACS option is turned off.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgACSEnable|TRUE|BOOLEAN|0x0004F002
  ### @endcond

  ### @cond (V9||BA||GN||SSP||VMR||RS||Mi3||BRH)
  ### @brief This value is one of a group of controls provided to fine tune the Hot Plug feature operation. This switch defines the starting I2C address of the mux from the
  ### hot plug controller (SMU) to the PCA devices. Typically 0x70.
  ### @brief Permitted Choices: 0x0-0xFF (Type: Value)(Default: 0x70)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdHotplugI2cAddress|0x70|UINT8|0x0004F003
  ### @endcond

#----------------------------------------------------------------------------
#-  NBIO Dynamic PCDs
### Set Doxy_path: "PCD-NBIO-BMC.h"
#----------------------------------------------------------------------------

  ### @name BMC Configuration Items
  ### A BMC micro-controller is typically connected via a 'x1' PCIe link. A BMC attached to the CPU via a PCIe link
  ### acts like a standard PCI device and is configured in that manor.
  ### Using the standard PCI enumeration process, the BMC to CPU link will be connected 'late' in the POST time
  ### line. A special provision is available to have this specific link trained early, so as to establish the connection as
  ### soon as possible after power-on.These controls are used by the UEFI NBIO Driver. The values used for these controls must
  ### match the values used for the APCB token if/when the early initialization option is used.
  ### @{

  ### @cond (ZP||BA||GN||SSP||RS||MI3||BRH)
  ### @brief Early BMC Link Training
  ### @brief This value notifies AGESA if the early BMC link training feature is enabled in the AGESA boot loader. AGESA utilizes this notification to clear link training
  ### status before DXIO initialization.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE - Early link training is enabled at the location specified by PcdEarlyBmcLinkSocket and PcdEarlyBmcLinkDie
  ### @li FALSE - Early link training is turned off
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEarlyBmcLinkTraining|FALSE|BOOLEAN|0x0004F004
  ### @endcond

  ### @cond (ZP||BA||GN||SSP||RS||MI3||BRH)
  ### @brief This value identifies the socket number to which the BMC link is connected if early BMC training is enabled.
  ### @brief Permitted Choices: (Type: List)(Default: 0xFF)
  ### @li 0 - BMC is connected to socket 0
  ### @li 1 - BMC is connected to socket 1
  ### @li 0xFF - BMC early training is not supported
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEarlyBmcLinkSocket|0xFF|UINT8|0x0004F005
  ### @endcond

  ### @cond (ZP||BA||GN||SSP||RS||MI3||BRH)
  ### @brief This value identifies the die number to which the BMC link is connected if early BMC training is enabled.
  ### @brief Permitted Choices: (Type: List)(Default: 0xFF)
  ### @li 0 - BMC is connected to die 0 (lanes 0 - 31) of the socketed identified by PcdEarlyBmcLinkSocket
  ### @li 1 - BMC is connected to die 1 (lanes 32 - 63) of the socketed identified by PcdEarlyBmcLinkSocket
  ### @li 2 - BMC is connected to die 2 (lanes 64 - 95) of the socketed identified by PcdEarlyBmcLinkSocket
  ### @li 3 - BMC is connected to die 3 (lanes 96 - 127) of the socketed identified by PcdEarlyBmcLinkSocket
  ### @li 0xFF - BMC early training is not supported
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEarlyBmcLinkDie|0xFF|UINT8|0x0004F006
  ### @endcond

  ### @cond (ZP||BA||GN||SSP||RS||MI3||BRH)
  ### @brief This value identifies the lane number use for the BMC link trained in ABL. If the lane number is 128 or 129 then the link does not need to be disabled for
  ### reconfiguration. In this case the link will remain active. Note that any value below 128 will cause the BMC link to be reconfigured in PEI.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x0, No BMC link present)
  ### @li 0 - 127 - Link will be disabled and restored during PEI
  ### @li 128 - 129 - Link will not be disabled during PEI
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEarlyBmcLinkLaneNum|0|UINT8|0x0004F03E
  ### @endcond

  ### @}  end of BMC config items

#----------------------------------------------------------------------------
#-  NBIO Dynamic PCDs
### Set Doxy_path: "PCD-NBIO.h"
#----------------------------------------------------------------------------

  ### @cond (BIXBY||RV||ZP||BA||FF3||GN||MR||RMB||RN||RPL||SSP||PHX||RS||MI3||BRH)
  ### @brief This value controls whether AGESA will enable Alternative Routing-ID Interpretation (ARI) support for PCIe
  ### @brief For Single-Root I/O Virtualization (SR-IOV) support this PCD should be set to TRUE. SR-IOV is a specification that allows a physical PCIe
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li TRUE - AGESA will enable ARI in root ports and endpoints that support it. Note that this does not guarantee ARI will be active; the endpoint must also support ARI.
  ### @li FALSE - ARI support will remain disabled regardless of end-point support.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPcieAriSupport|TRUE|BOOLEAN|0x0004F007
  ### @endcond

  ### @cond (CZ||FF3||GN||RMB||RN||RPL||SSP||RV||ZP||PHX)
  ### @brief Select whether or not the NBIO High Definition (HD) audio Device is active.
  ### @li TRUE - This option is active
  ### @li FALSE - This option is turned off
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgHdAudioEnable|TRUE|BOOLEAN|0x0004F008
  ### @endcond

  ### @cond (V9||BA||GN||SSP||VMR||RS||MI3||BRH)
  ### @brief This value is one of a group of controls provided to fine tune the Hot Plug feature operation. This switch defines if slot numbering for hotplug is 0 based or 1 based.
  ### @li 0 - 0 based slot numbering.
  ### @li 1 - 1 based slot numbering.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdHotplugSlotIndex|0x0|UINT8|0x0004F009
  ### @endcond

  ### @cond (ZP)
  ### @brief This value selects the method of training for PCIe
  ### @li 0 - Enables single step training to the target operating speed
  ### @li 1 - Enables dual step training
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLinkTrainingType|0x0|UINT8|0x0004F00A
  ### @endcond

  ### @cond (ZP||BRH)
  ### @brief This value selects the idle operation as managed by the SMU. For APU this is the "CPUOFF" feature, while CPU refers to the feature as "PC6".
  ### @li 0xF - Auto
  ### @li 0   - Typical current idle
  ### @li 1   - Low current idle
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPowerSupplyIdleControl|0xF|UINT8|0x0004F00B
  ### @endcond

  ### @cond (RV||RMB||RN||RPL||PHX)
  ### @brief Enable/Disable Sensor Fusion Hub
  ### @details Enables or Disables the nBIF device for MP2
  ### @li TRUE  - Enable
  ### @li FALSE - Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgSensorFusionHubEnable|TRUE|BOOLEAN|0x0004F00C
  ### @endcond

  ### @cond (ZP)
  ### @brief Maximum GEN speed is Gen3
  ### @endcond

  ### @cond (ZP||STP||RS||MI3||BRH)
  ### @brief This value defines an upper limit for PCIe
  ### @li 0xFF - Auto
  ### @li 0    - Do not limit PCIe link speed
  ### @li 1    - Limit all PCIe links to Gen1
  ### @li 2    - Limit all PCIe links to Gen2
  ### @li 3    - Limit all PCIe links to Gen3
  ### @li 4    - Limit all PCIe links to Gen4
  ### @li 5    - Limit all PCIe links to Gen5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgForcePcieGenSpeed|0xFF|UINT8|0x0004F00D
  ### @endcond

  ### @cond (BRH)
  ### @brief This value targets but does not limit the GEN Speed for all PCIe Links
  ### @li 0xFF - Auto
  ### @li 0    - Target all PCIe links to Maximum speed
  ### @li 1    - Target all PCIe links to Gen1
  ### @li 2    - Target all PCIe links to Gen2
  ### @li 3    - Target all PCIe links to Gen3
  ### @li 4    - Target all PCIe links to Gen4
  ### @li 5    - Target all PCIe links to Gen5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTargetPcieGenSpeed|0xFF|UINT8|0x0004EFFF
  ### @endcond

  ### @cond (V10)
  ### @brief [F17M18] This value specifies the maximum slow PPT value that the APU alone is allowed to consume in a platform that has PeAPM enabled. The traditional Slow PPT
  ### value becomes the combined platform limit for the APU
  ### @brief This is a numeric value and must be in milliwatts. (Default: slow PPT value of the system configuration defined in the IRM.)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSetSlowPPTLimitApuOnly|0|UINT32|0x0004F00E
  ### @endcond

  ### @brief In certain platform configurations it is possible to improve the performance of an endpoint by enabling "Preferred I/O" mode. This PCD is intended for use by
  ### platform BIOS software that provides the user interface to select a particular add-in device and references the PCI bus/device/function address of the add-in card when
  ### requesting Preferred I/O mode for the device.
  ### @brief See also PcdAmdPreferredIOBus and PcdAmdEnhancedPreferredIOMode. This control is not used when PcdAmdPreferredIOBus is active, and is now deprecated.
  ### @brief Permitted Choices: (Type: UINT32)(Default: 0x00)
  ### @li 0x00 - disable this feature.
  ### @li not=0 - PCI address of target device. Bit packed field:
  ### @li [23:16] BusNum
  ### @li [15:8] DevNum
  ### @li [7:0] FunNum
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPreferredIODevice|0|UINT32|0x0004F00F

  ### @cond (V9)
  ### @brief This value is one of a group of controls provided to fine tune the Hot Plug feature operation. This control switch allows the firmware to ignore the physical presence (GPIO).
  ### @li FALSE - Will execute normal I2C sideband behavior (SMU reports physical presence to the PCIe
  ### @li TRUE - SMU does not report physical presence to the PCIe
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDisableSideband|0|BOOLEAN|0x0004F010
  ### @endcond

  ### @cond (V9)
  ### @brief This value is one of a group of controls provided to fine tune the Hot Plug feature operation. This switch allows a system to explore a limitation found in the PCIe
  ### @li FALSE - SMU FW will enforce valid device presence status during a L1 hot remove via LINK_RETRAIN.
  ### @li TRUE - SMU FW will not enforce valid device presence during L1 hot removal.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDisableL1wa|0|BOOLEAN|0x0004F011
  ### @endcond

  ### @cond (V9)
  ### @brief This value is one of a group of controls provided to fine tune the Hot Plug feature operation. This switch allows BridgeDis programming based on physical presence.
  ### @brief Note: If PcdAmdIrqSetBridgeDis=FALSE, then there is no control of BridgeDis.
  ### @li FALSE - BridgeDis based on physical presence.
  ### @li TRUE - BridgeDis not controlled by physical presence.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDisableBridgeDis|0|BOOLEAN|0x0004F012
  ### @endcond

  ### @cond (V9)
  ### @brief hot plug controller holds root port IRQ until DL_Active is set
  ### @details Note: new control PcdAmdRRCEn replacing PcdAmdDisableIrqPoll
  ### PcdAmdRRCEn
  ### This value is one of a group of controls provided to fine tune the Hot Plug feature operation.
  ### This switch determines if the ReceiverResetCycleEn bit is toggled during hot plug events.
  ### @li FALSE - ReceiverResetCycleEn remains unmodified during hot plug events..
  ### @li TRUE - ReceiverResetCycleEn is toggled during hot plug events.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDisableIrqPoll|0|BOOLEAN|0x0004F013
  ### @endcond

  ### @cond (V9)
  ### @brief This value is one of a group of controls provided to fine tune the Hot Plug feature operation. This switch allows BridgeDis to be based on PCIe
  ### @brief Note: When set to TRUE, PcdAmdDisableBridgeDis and PcdAmdRRCEn are forced to TRUE.
  ### @li FALSE - BridgeDis not based off of PCIe
  ### @li TRUE - BridgeDis based off of PCIe
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdIrqSetBridgeDis|0|BOOLEAN|0x0004F014
  ### @endcond

  ### @cond (BIXBY||BA||GN||RMB||RPL||SSP||PHX||RS||MI3)
  ### @brief This value determines if AGESA will enable the Data Link Feature capability on all PCIe ports Gen4 or higher.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li FALSE - Data Link Feature Capability is disabled.
  ### @li TRUE - Data Link Feature Capability is enabled.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDlfCapEn|TRUE|BOOLEAN|0x0004F015
  ### @endcond

  ### @cond (BIXBY||BA||GN||RMB||RPL||SSP||PHX||RS||MI3)
  ### @brief This value determines if AGESA will enable Data Link Feature Exchange on all PCIe ports Gen4 or higher. The PCIe specification explicitly provides this option
  ### so a root compile can disable the DL Feature Exchange transactions in the event a PCIe endpoint is present in a system that cannot respond properly to those packets.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li FALSE - Data Link Feature Exchange is disabled.
  ### @li TRUE - Data Link Feature Exchange is enabled.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDlfExEn|TRUE|BOOLEAN|0x0004F017
  ### @endcond

  ### @cond (BIXBY||BA||GN||RMB||SSP||PHX||RS||MI3||BRH)
  ### @brief This Boolean is the control for receiver margin enablement.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li FALSE - RX Margin Feature is disabled
  ### @li TRUE - RX Margin Feature is enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdRxMarginEnabled|TRUE|BOOLEAN|0x0004F016
  ### @endcond

  ### @cond (BA||FF3||GN||RMB||RN||RPL||SSP||PHX||RS||MI3||BRH)
  ### @brief This control will force all PCIe ports into compliance mode.
  ### @li FALSE - Allow platforms to configure the PCIe ports manually to be in compliance mode
  ### @li TRUE - All PCIe ports will be in compliance mode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLinkComplianceModeAllPorts|FALSE|BOOLEAN|0x0004F018
  ### @endcond

  ### @brief Set Slot power limit base on Link Width
  ### @details This setting determines if PCIe slot power limit is based on Link Width
  ### @li TRUE  - PCIe slot power limit is based on Link Width
  ### @li FALSE - PCIe slot power limit is not based on Link Width
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdWidthBasedSlotPowerLimit|FALSE|BOOLEAN|0x0004F019

  ### @cond (BA||GN||SSP)
  ### @brief When set, this value will be used as a global override for all ports. The PCDs are intended as a convenience for certain test cases when it can be useful to set
  ### all the ports at once. Setting this field, or the DXIO descriptors for 'Enable' may be useful when a re-timer is not present. This control is available only in the
  ### Family 17h products and has been deprecated in the Family 19h product line.
  ### @li 0: Disable - set all ports to disabled.
  ### @li 1: Enable - set all ports to enabled.
  ### @li 0xFF: Auto - Skip this override setting. The normal "per port" configuration for all ports in the DXIO descriptors config value that was populated by the customer
  ### will be the value used in AGESA.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieRxMarginPersistenceAllPort|0xFF|UINT8|0x0004F01A
  ### @endcond

  ### @cond (MI200||BA||GN||SSP||BRH)
  ### @brief When set, this value will be used as a global override for Link Active State Power Management for all ports. This PCD is intended as a convenience for certain
  ### test cases when it can be useful to set all the ports at once.
  ### @li 0: Disable - set all ports to disabled.
  ### @li 1: L0s - set all ports to use L0s. Note: L0 option not available on [F19M00].
  ### @li 2: L1 - set all ports to use L1s.
  ### @li 3: L0sL1 - set all ports to use L0sL1. Note: L0 option not available on [F19M00].
  ### @li 0xFF: Auto - Skip this override setting. The normal "per port" configuration for all ports in the DXIO descriptors config value that was populated by the customer
  ### will be the value used in AGESA.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLinkAspmAllPort|0xFF|UINT8|0x0004F01B
  ### @endcond

  ### @brief Enables the ARI forwarding feature from AGESA
  ### @details When enabled, this PCD will enable ARI forwarding in the root port of any endpooint that supports it.
  ### @li TRUE - Enable ARI forwarding if supported by the endpoint device.
  ### @li FALSE - Disable ARI forwarding for all devices.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieAriForwardingEnable|FALSE|BOOLEAN|0x0004F05F

  ### @cond (RS || BRH)
  ### @brief Non-PCIe Compliant Support
  ### @details Send command to disable Extended EIEOS, DLF, and Gen 5 Support on training failure for non-pcie compliant devices.
  ### @li TRUE - Send command to disable Extended EIEOS, DLF, and Gen 5 Support on training failure for non-pcie compliant devices
  ### @li FALSE - Do not send command to disable Extended EIEOS, DLF, and Gen 5 Support on training failure for non-pcie compliant devices
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieNonPcieCompliantTrainingFailureSupport|TRUE|BOOLEAN|0x0004F06C
  ### @endcond

  ### @cond (RS||MI3)
  ### @brief MPIO Post Message Support
  ### @details Send command to disable enable MPIO Post Message Support.
  ### @li TRUE - Allow for MPIO Post Message Support
  ### @li FALSE - Do not allow for MPIO Post Message Support
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMpioPostMessageSupport|FALSE|BOOLEAN|0x0004F06D
  ### @endcond

  ### @cond (GN)
  ### @brief Early Train Two Pcie Links
  ### @details Early training of a second PCIe link in addition to BMC, EX: NIC
  ### @li TRUE   - Enable
  ### @li FALSE  - Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgEarlyTrainTwoPcieLinks|FALSE|BOOLEAN|0x0004F01C
  ### @endcond

  ### @brief This value controls the maximum Read Request size which a PCIe root port will support. The power up default is 512 bytes but this PCD can be used to increase as
  ### high as 4096 if an endpoint is present that supports the larger size.
  ### @brief Permitted Choices: (Type: List)(Default: 0xFF)
  ### @li 0x0 - 128 Bytes
  ### @li 0x1 - 256 Bytes
  ### @li 0x2 - 512 Bytes
  ### @li 0x3 - 1024 Bytes
  ### @li 0x4 - 2048 Bytes
  ### @li 0x5 - 4096 Bytes
  ### @li 0xFF - Used hardware default value
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgMaxReadRequestSize|0xFF|UINT8|0x0004F01D

  ### @cond (GN||RS||BRH)
  ### @brief SMU Diagnostic Mode
  ### @details Ability to add specialized diagnostic commands support
  ### @li 0x0 - Disable
  ### @li 0x1 - Enable
  ### @li 0xFF - Hardware default value used
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgDiagnosticMode|0xFF|UINT8|0x0004F01E
  ### @endcond

  ### @cond (BA||GN||RS||MI3||BRH)
  ### @brief This value controls Host System Management Port (HSMP) interface to provide OS-level software with access to system management functions via a set of mailbox
  ### registers. Functionality of the HSMP interface is implemented by the power management firmware and documented in the PPR.
  ### @li 0x0 - Disable HSMP interface
  ### @li 0x1 - Enable HSMP interface support
  ### @li 0xFF - Hardware default value used
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgHSMPSupport|0x1|UINT8|0x0004F01F
  ### @endcond

  ### @cond (RX||V9||BA||SSP||VMR||RS||BRH)
  ### @brief This value is the same as PcdVrmCurrentLimit but this is the label used by the F17M00 and F17M08 code sets.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgTDC|0x00|UINT32|0x0004F020
  ### @endcond

  ### @cond (RX||V9||BA||SSP||VMR||RS)
  ### @brief This control name is 'overloaded' and the function has changed with new programs.
  ### @brief This value is the same as PcdVrmMaximumCurrentLimit but this is the label used by the F17M00 and F17M08 code sets.
  ### @brief This a numeric value expressed in Amperes. For example, a current limit of 40 amperes is represented as: decimal 40.
  ### @brief This control allows the user to set the desired EDC limit on the platform. This value must be less than or equal to PcdCfgPlatformEDC which must also be configured.
  ### @brief The valid value range is 0 - 300A. Please see the Power and Thermal Data Sheet for your part to see recommended values.
  ### @li For [F17M00][F17M08]
  ### @li For [F19M00]
  ### @li 0x00 - use default value
  ### @li 0xXX - Specifies the override value in Amperes. For example, a current limit of 280A amperes is represented as: 0x118 (decimal 280).
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgEDC|0x00|UINT32|0x0004F021
  ### @endcond

  ### @cond (RV||FF3||RMB||RN||RPL||PHX)
  ### @brief Nbio Audio Select
  ### @details Selects the ACP (Audio Co-Processor) Pin Configuration
  ### NOTE: For more information please refer to the Audio Co-Processor spec (doc# 56704, revision 1.01).
  ### @li 1 - HEDT/Workstation(SPrx package) ; HDA(3SDI).
  ### @li 2 - HEDT/Workstation(SPrx package) ; HDA(1SDI) + SW0(1MDATA).
  ### @li 3 - HEDT/Workstation(SPrx package) ; SW0(4MDATA)+ SW1(1MDATA).
  ### @li 4 - Mainstream Desktop (AM5 Package);HDA(3SDI) + PDM(2CH).
  ### @li 5 - Mainstream Desktop (AM5 Package);HDA(1SDI) + PDM(6CH).
  ### @li 6 - Mainstream Desktop (AM5 Package) ;HDA(1SDI) + SW0(1MDATA) + PDM(2CH).
  ### @li 7 - MainstreamDesktop (AM5 Package);SW0(4MDATA) + PDM(6CH).
  ### @li 8 - MainstreamDesktop (AM5 Package);SW0(4MDATA) +SW1(1MDATA)+ PDM(2CH) .
  ### @li 9 - mainstream notebook;3I2S + 1 REFCLK + 1 INTR.
  ### @li 10 - mainstream notebook;HDA(3SDI) + PDM(6CH) +I2S.
  ### @li 11 - mainstream notebook;HDA(3SDI) + PDM(8CH).
  ### @li 12 - mainstream notebook;HDA(1SDI) + SW(1MDATA) + PDM(6CH) + I2S.
  ### @li 13 - mainstream notebook;SW0(4MDATA) + SW1(1MDATA) +PDM(6CH) + I2S.
  ### @li 14 - mainstream notebook;SW0(4MDATA) + SW1(1MDATA) + PDM(8CH).
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioAudioSelect|0x1|UINT8|0x0004F022
  ### @endcond

  ### @cond (RS||V9||BA||GN||SSP||BRH)
  ### @brief This control only applies when DeterminismMode (above) is set to 'Manual'. This control sets the percentage amount of determinism desired by the user.
  ### @brief Permitted Choices: (Type: Value) (Default: Auto)
  ### @li 0x0 - Auto/Power - 'determinism' is set to low percentage allowing the power saving states of the processor to be used.
  ### @li 0x1 - Performance - 'determinism' is set to a high percentage so that response times can be predicted and consistent.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDeterminismControl|FALSE|BOOLEAN|0x0004F023
  ### @endcond

  ### @cond (RS||BA||GN||SSP||BRH)
  ### @brief This value can select the fused determinism or customized determinism. 'Determinism' is the trade-off between performance mode (deterministic behavior) and power
  ### saving (non-deterministic due to CStates/PStates).
  ### @brief Permitted Choices: (Type: Value) (Default: Auto)
  ### @li 0x0 - Auto - Use the fused Determinism. This selection favors the power saving mode.
  ### @li 0x1 - Manual - User can set customized Determinism. See 'DeterminismControl' below.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDeterminismMode|0|UINT8|0x00050003
  ### @endcond

  ### @cond (BA||GN||SSP||RS||BRH)
  ### @brief Boost Fmax
  ### @details This value specifies the boost Fmax frequency limit to apply to all cores (MHz)
  ### @li 0 - no limit. Use the fused values.
  ### @li upto 0xFFFFFFFF : CPU cores limit on frequency||in MHz. This will be capped by the fused part maximum frequency.
  ### Limits should be within the part's operational range.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdBoostFmax|0x0|UINT32|0x00050004
  ### @endcond

  ### @cond (RS||BRH)
  ### @brief SVI3 SVC Speed
  ### @details This value specifies SVI3 SVC speed (MHz)
  ### @li 0 - 50MHz
  ### @li 1 - 40Mhz
  ### @li 2 - 26Mhz
  ### @li 3 - 20Mhz
  ### @li 4 - 16Mhz
  ### @li 5 - 13Mhz
  ### @li 6 - 10Mhz
  ### @li 7 - 8Mhz
  ### @li 8 - 5Mhz
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSvi3SvcSpeed|3|UINT8|0x00050005
  ### @endcond

  ### @cond (RS||BRH)
  ### @brief Power Profile Selection
  ### @details DF Pstate selection in the profile policy is overridden by the pstate range BIOS option or the APB_DIS BIOS option if either one is selected. This value specifies Power Profile Selection
  ### @li 0 = High Performance Mode (DEFAULT)
  ### @li 1 = Efficiency Mode
  ### @li 2 = Maximum IO Performance Mode
  ### @li 3 = Balanced Memory Performance Mode
  ### @li 4 = Balanced Core Performance Mode
  ### @li 5 = Balanced Core Memory Performance Mode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPowerProfileSelect|0|UINT8|0x00050006
  ### @endcond

  ### @cond (RS||BRH)
  ### @brief xGMI force link width control.
  ### @li 0 - no action applied.
  ### @li 1 - force to the value in xGMIForceLinkWidth.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdxGMIForceLinkWidthEn|0x00|UINT8|0x00050012
  ### @endcond

  ### @cond (RS||BRH)
  ### @brief Forces the xGMI link width.
  ### @details This setting will override the values of xGMI Max link width (PcdxGMIMaxLinkWidth)
  ### and xGMI Min link width (PcdxGMIMinLinkWidth), and force the xGMI link width to the specified value.
  ### @li 0 - x4 link.
  ### @li 1 - x8 link.
  ### @li 2 - x16 link.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdxGMIForceLinkWidth|0x00|UINT8|0x00050013
  ### @endcond

  ### @cond (RS||BRH)
  ### @brief Allows configurations for the max xGMI link widths.
  ### @details Applies to all xGMI links in the system.
  ### This may be useful for debugging or for certain configurations that need a controlled bandwidth.
  ### @li 0 - (auto)Use default xGMI max supported link width.
  ### @li 1 - (manual) User can set custom xGMI max link width using PcdxGMIMaxLinkWidth.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdxGMIMaxLinkWidthEn|0x00|UINT8|0x00050014
  ### @endcond

  ### @cond (RS||BRH)
  ### @brief The value sets the max xGMI link width.
  ### @li 0 - x4.
  ### @li 1 - x8.
  ### @li 2 - x16.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdxGMIMaxLinkWidth|0x02|UINT8|0x00050015
  ### @endcond

  ### @cond (BRH)
  ### @brief The value sets the min xGMI link width.
  ### @li 0 - x4.
  ### @li 1 - x8.
  ### @li 2 - x16.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdxGMIMinLinkWidth|0x00|UINT8|0x00050016
  ### @endcond

  ### @cond (BRH)
  ### @brief xGMI Pstate Control
  ### @details This value enables the XGMI Range support.
  ### @li 0xFF - Auto
  ### @li 1 - Manual.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXgmiPstateControl|0xFF|UINT8|0x00050007
  ### @endcond

  ### @cond (BRH)
  ### @brief xGMI Pstate Selection
  ### @details This value specifies the minimum and maximum range for Xgmi pstate.
  ### @li 0 - High Speed
  ### @li 1 - Low Speed
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXgmiPstateSelection|0x0|UINT8|0x00050008
  ### @endcond

  ### @cond (BRH)
  ### @Enable port bifurcation support
  ### @details Enable or disable port bifurcation support
  ### @li TRUE  = Enabled
  ### @li FALSE = Disabled (DEFAULT)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverRideEnabled|FALSE|BOOLEAN|0x00050009
  ### @endcond

  ### @cond (BRH)
  ### @brief Port Bifurcation Configurations for Socket0 P0 Link
  ### @details Various supported port bifurcation configurations. (For 1P board: P0 Link)
  ### @li 0xf = Keep original setting (DEFAULT)
  ### @li 0x9 = Special case 1 (1x8, 2x4)
  ### @li 0xa = Special case 2 (1x8, 8x1)
  ### @li 0xb = All x8
  ### @li 0xc = All x4
  ### @li 0xd = All x2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS0P0|0xF|UINT8|0x0005000A
  ### @endcond

  ### @cond (BRH)
  ### @brief Port Bifurcation Configurations for Socket0 P1 Link
  ### @details Various supported port bifurcation configurations. (For 1P board: P1 Link)
  ### @li 0xf = Keep original setting (DEFAULT)
  ### @li 0x9 = Special case 1 (1x8, 2x4)
  ### @li 0xa = Special case 2 (1x8, 8x1)
  ### @li 0xb = All x8
  ### @li 0xc = All x4
  ### @li 0xd = All x2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS0P1|0xF|UINT8|0x0005000B
  ### @endcond

  ### @cond (BRH)
  ### @brief Port Bifurcation Configurations for Socket0 P2 Link
  ### @details Various supported port bifurcation configurations. (For 1P board: P2 Link)
  ### @li 0xf = Keep original setting (DEFAULT)
  ### @li 0x9 = Special case 1 (1x8, 2x4)
  ### @li 0xa = Special case 2 (1x8, 8x1)
  ### @li 0xb = All x8
  ### @li 0xc = All x4
  ### @li 0xd = All x2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS0P2|0xF|UINT8|0x0005000C
  ### @endcond

  ### @cond (BRH)
  ### @brief Port Bifurcation Configurations for Socket0 P3 Link
  ### @details Various supported port bifurcation configurations. (For 1P board: P3 Link)
  ### @li 0xf = Keep original setting (DEFAULT)
  ### @li 0x9 = Special case 1 (1x8, 2x4)
  ### @li 0xa = Special case 2 (1x8, 8x1)
  ### @li 0xb = All x8
  ### @li 0xc = All x4
  ### @li 0xd = All x2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS0P3|0xF|UINT8|0x0005000D
  ### @endcond

  ### @cond (BRH)
  ### @brief Port Bifurcation Configurations for Socket1 P0 Link
  ### @details Various supported port bifurcation configurations. (For 1P board: G0 Link)
  ### @li 0xf = Keep original setting (DEFAULT)
  ### @li 0x9 = Special case 1 (1x8, 2x4)
  ### @li 0xa = Special case 2 (1x8, 8x1)
  ### @li 0xb = All x8
  ### @li 0xc = All x4
  ### @li 0xd = All x2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS1P0|0xF|UINT8|0x0005000E
  ### @endcond

  ### @cond (BRH)
  ### @brief Port Bifurcation Configurations for Socket1 P1 Link
  ### @details Various supported port bifurcation configurations. (For 1P board: G1 Link)
  ### @li 0xf = Keep original setting (DEFAULT)
  ### @li 0x9 = Special case 1 (1x8, 2x4)
  ### @li 0xa = Special case 2 (1x8, 8x1)
  ### @li 0xb = All x8
  ### @li 0xc = All x4
  ### @li 0xd = All x2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS1P1|0xF|UINT8|0x0005000F
  ### @endcond

  ### @cond (BRH)
  ### @brief Port Bifurcation Configurations for Socket1 P2 Link
  ### @details Various supported port bifurcation configurations. (For 1P board: G2 Link)
  ### @li 0xf = Keep original setting (DEFAULT)
  ### @li 0x9 = Special case 1 (1x8, 2x4)
  ### @li 0xa = Special case 2 (1x8, 8x1)
  ### @li 0xb = All x8
  ### @li 0xc = All x4
  ### @li 0xd = All x2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS1P2|0xF|UINT8|0x00050010
  ### @endcond

  ### @cond (BRH)
  ### @brief Port Bifurcation Configurations for Socket1 P3 Link
  ### @details Various supported port bifurcation configurations. (For 1P board: G3 Link)
  ### @li 0xf = Keep original setting (DEFAULT)
  ### @li 0x9 = Special case 1 (1x8, 2x4)
  ### @li 0xa = Special case 2 (1x8, 8x1)
  ### @li 0xb = All x8
  ### @li 0xc = All x4
  ### @li 0xd = All x2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS1P3|0xF|UINT8|0x00050011
  ### @endcond

  ### @cond (RS||V9||BA||GN||SSP||VMR||BRH)
  ### @brief This value selects the maximum sustained power in Watts. 'Max' is the maximum value supported by the fusing of the part. NOTE: For more information please
  ### refer to the Infrastructure Roadmap spec.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x00)
  ### @li 0x00 - SMU will use the fused TDP value.
  ### @li 0x01..N - specifies the TDP value in Watts, up to the 'Max'.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdcTDP|0x00|UINT32|0x0004F024
  ### @endcond

  ### @cond (V9||BA||GN||SSP||VMR)
  ### @brief This value controls Dynamic Link Width Management (DLWM) feature. When the platform can support either an 8-lane or 16-lane xGMI operation, the dynamic
  ### adjustment feature can provide some power savings. This feature is described in the PPOG, section "xGMI - Connection Between Sockets".
  ### @brief Permitted Choices: (type: value)(Default: 0xFF)
  ### @li 0x0 - Force at fixed width as set by PcdxGMIForceLinkWidth.
  ### @li 0x1 - Enable dynamic link width adjustments.
  ### @li 0xFF - Used hardware default value - which is 'enabled'.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgDLWMSupport|0xFF|UINT8|0x0004F05A
  ### @endcond

  ### @cond (RN)
  ### @brief Audio Policy
  ### @details PDM Channel Mic Selection
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPdmMicSelection|0|UINT8|0x0004F025
  ### @endcond

  ### @cond (V9)
  ### @brief This value controls whether AGESA will disable the "Power State Indicate" control through SMU. NOTE: For more information please refer to the IRM spec.
  ### @li TRUE - AGESA will disable PSI.
  ### @li FALSE - PSI option is turned on.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPSIDisable|FALSE|BOOLEAN|0x0004F026
  ### @endcond

  ### @cond (RV||ZP||BA||FF3||GH||RMB||RN||RPL||SSP||CZ||PHX||RS||MI3||BRH)
  ### @brief Selects the state of the IOMMU support firmware.
  ### @brief APCB_TOKEN_UID_IOMMU should be set the same as this.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE - This option is active.
  ### @li FALSE - This option is turned off.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgIommuSupport|TRUE|BOOLEAN|0x0004F027
  ### @endcond

  ### @cond (PHX||RMB)
  ### @brief This control enables or disables IOMMU Prefetch during POST. Requires PcdCfgIommuSupport = TRUE.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li FALSE - IOMMU Prefetch is disabled.
  ### @li TRUE - IOMMU Prefetch is enabled.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgIommuPrefetchSupport|FALSE|BOOLEAN|0x0004F05C
  ### @endcond

  ### @cond (RV||ZP||BA||FF3||GH||RMB||RN||RPL||SSP||CZ||PHX||RS||MI3||BRH)
  ### @brief This control enables or disables DMAR mitigation during POST. Requires PcdCfgIommuSupport = TRUE.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li FALSE - DMAR mitigation is disabled.
  ### @li TRUE - DMAR mitigation is enabled.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdIvInfoDmaReMap|FALSE|BOOLEAN|0x0004F028
  ### @endcond

  ### @cond (RN||RS||BRH)
  ### @brief Enables or disables DMA remap support in IVRS IVinfo Field.
  ### @details Requires PcdCfgIommuSupport = TRUE
  ### @li TRUE - Enable DMA remap support in IVRS IVinfo Field
  ### @li FALSE - Disable DMA remap support in IVRS IVinfo Field
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDmaProtection|FALSE|BOOLEAN|0x0004F05B
  ### @endcond

  ### @cond (SHP)
  ### @brief Enables or disables mapping DRAM into HT hole
  ### @details Requires PcdCfgIommuSupport = TRUE
  ### @li TRUE - Enable mapping DRAM into HT hole
  ### @li FALSE - Disable mapping DRAM into HT hole
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMappingDramIntoHtHole|FALSE|BOOLEAN|0x0004F05E
  ### @endcond

  ### @cond (RS||V9||BA||GN||SSP||VMR||BRH)
  ### @brief This control establishes the operational mode for the SMU QOS. The purpose is to improve overall performance per watt. However, some benchmarks may score better
  ### when this feature is disabled.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE - This option is active
  ### @li FALSE - This option is turned off
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEfficiencyOptimizedMode|FALSE|BOOLEAN|0x0004F029
  ### @endcond

  ### @cond (RMB||RN||RPL||PHX)
  ### @details Enable/Disable Acp Controller behind nbif
  ### @li TRUE - Enable
  ### @li FALSE = Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcpController|TRUE|BOOLEAN|0x0004F02A
  ### @endcond

  ### @cond (CZ||PHX||V10||AR||CZN||FF3||RMB||RN||RPL)
  ### @brief This value may be set to override the system configuration value as specified in the IRM specification for your APU.
  ### @li 0 - Use the fused default System Configuration.
  ### @li 1 - XX Identifies a specific system configuration to override the fused defaults.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgSystemConfiguration|0|UINT8|0x0004F02B
  ### @endcond

  ### @cond (BIXY||RV||ZP||BA||FF3||GN||RMB||RN||RPL||SSP||PHX||RS||MI3||BRH)
  ### @brief This value enables reporting of EDB errors from the PCIe
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li FALSE - EDB errors are not reported by the hardware.
  ### @li TRUE - Bad EDB tokens are reported by the hardware.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioReportEdbErrors|FALSE|BOOLEAN|0x0004F02C
  ### @endcond

  ### @cond (BA||FF3||GN||RMB||RN||RPL||SSP||PHX||RS||BRH)
  ### @brief This configuration option enables a user to prevent the root port from entering loopback.
  ### @details For some use cases it is useful to prevent the root port from entering loopback mode.
  ### @li FALSE - AGESA will prevent the root port from entering loopback mode.
  ### @li TRUE - AGESA will allow the root port to enter loopback mode.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdLcLoopbackWaitForAllActiveLanes|TRUE|BOOLEAN|0x0004F03F
  ### @endcond

  ### @cond (ZP||VMR)
  ### @brief These values are set by the AGESA
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDxioMajorRevision|0|UINT32|0x0004F02D
  ### @endcond

  ### @cond (ZP||VMR)
  ### @brief Dxio Minor Revision
  ### @details These values are set by the AGESA(TM)) to report to the platform BIOS the revision of DXIO firmware
  ### that is present in the system. This value is Read-Only||populated AGESA for consumption by platform BIOS.
  ### Writes to this item will have no affect.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDxioMinorRevision|0|UINT32|0x0004F02E
  ### @endcond

  ### @cond (RV||FF3||RMB||RPL||PHX)
  ### @brief Az function enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAzaliaEnable|TRUE|BOOLEAN|0x0004F02F
  ### @endcond

  ### @cond (CZ||RV||FF3||RMB||RN||RPL||PHX)
  ### @brief This value defines the initial PCIe
  ### @li 0 - PSPP is Disabled.
  ### @li 1 - PSPP Performance Mode.
  ### @li 2 - PSPP Balanced Mode.
  ### @li 3 - PSPP Power Saving Mode.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPsppPolicy|0|UINT8|0x0004F030
  ### @endcond

  ### @cond (RS||BA||GN||BRH)
  ### @brief When long duration idleness is expected in a system, this control allows the system to transition into a DF C-state which can set the system into an even lower power state.
  ### @brief Permitted Choices: (Type: Value)(Default: 0xF)
  ### @li 0xF - Auto - Based on platform configuration.
  ### @li 0x1 - This option is active, saving power when the system is very idle.
  ### @li 0x0 - This option is turned off. Long periods of idleness are not expected so no power savings would be achieved.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDfCstateEnable|0xF|UINT8|0x00051000
  ### @endcond

  ### @cond (RS||BA||BRH)
  ### @brief DF P-state Freq Optimizer
  ### @li 0 - Do not disable the DF Pstate CCLK effective frequency optimizer
  ### @li 1 - Disable the DF Pstate CCLK effective frequency optimizer
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDFFODisable|0|UINT8|0x00051003
  ### @endcond

  ### @cond (BA)
  ### @brief Package Power Limit Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPackagePowerLimitEnable|0|UINT8|0x00051001
  ### @endcond

  ### @cond (RMB)
  ### @brief USB4_DPIA disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsb4DpiaDisable|0|UINT8|0x00051002
  ### @endcond

  ### @cond (RS||BA||GN||SSP||VMR||BRH)
  ### @brief This value selects the APB Disable value for the SMU.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x0)
  ### @li 0 - Clear ApbDis to SMU.
  ### @li 1 - Set ApbDis to SMU.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgApbDis|0|UINT8|0x00051004
  ### @endcond

  ### @cond (RS||BA||GN||SSP||VMR||BRH)
  ### @brief This value defines the target PState when ApbDis is set.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x0)
  ### @li 0-x - Specify a valid PState for the processor installed.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgFixedSocPstate|0|UINT8|0x00051005
  ### @endcond

  ### @cond (SSP)
  ### @details Latchup Voltage (in mV)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgLatchupVoltage|0|UINT32|0x00051008
  ### @endcond

  ### @cond (RMB||RPL||PHX||MDN)
  ### @brief Address of PCIe RP device lists for S0i3 Resume Prevent Nvme Link Training
  ### @brief Examples of the use:
  ### @code
  ### UINT32 DevFunc[] = {
  ###   {(UINT32)((<Device> << 3) | <Function>)}, // Device << 3 | Function, for each RP device
  ###   {0xFFFFFFFF}                              // End Sign
  ### };
  ### @endcode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgDxioNoTrainLinkDevFunc|0|UINT32|0x00051009
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief SRIOV capability Enablement
  ### @li FALSE - Disable SRIOV capability.
  ### @li TRUE - Enable SRIOV capability.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgSriovEnDev0F1|FALSE|BOOLEAN|0x00051010
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable ARI cap in EP function
  ### @li FALSE - Disable ARI capability in EP function.
  ### @li TRUE - Enable ARI capability in EP function.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAriEnDev0F1|FALSE|BOOLEAN|0x00051011
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable AER cap in EP function
  ### @li FALSE - Disable AER capability in EP function.
  ### @li TRUE - Enable AER capability in EP function.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAerEnDev0F1|FALSE|BOOLEAN|0x00051012
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable ACS cap in EP function.
  ### @li FALSE - Disable ACS capability in EP function.
  ### @li TRUE - Enable ACS capability in EP function.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAcsEnDev0F1|FALSE|BOOLEAN|0x00051013
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable ATS cap in EP function.
  ### @li FALSE - Disable ATS capability in EP function.
  ### @li TRUE - Enable ATS capability in EP function.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAtsEnDev0F1|FALSE|BOOLEAN|0x00051014
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable PASID cap in EP function.
  ### @li FALSE - Disable PASID capability in EP function.
  ### @li TRUE - Enable PASID capability in EP function.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPasidEnDev0F1|FALSE|BOOLEAN|0x00051015
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable PWR Budget cap in EP function.
  ### @li FALSE - Disable PWR Budget capability in EP function.
  ### @li TRUE - Enable PWR Budget capability in EP function.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPwrEnDev0F1|FALSE|BOOLEAN|0x00051016
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable Readiness Time Reporting
  ### @li FALSE - Disable Readiness Time Reporting.
  ### @li TRUE - Enable Readiness Time Reporting.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgRtrEnDev0F1|FALSE|BOOLEAN|0x00051017
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable Page Request cap in EP function.
  ### @li FALSE - Disable Page Request capability in EP function.
  ### @li TRUE - Enable Page Request capability in EP function.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPriEnDev0F1|FALSE|BOOLEAN|0x00051018
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable ATC enable for Pcie ATS Control
  ### @li FALSE - Disable ATC enable for Pcie ATS Control.
  ### @li TRUE - Enable ATC enable for Pcie ATS Control.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAtcEnable|FALSE|BOOLEAN|0x00051019
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable ACS in RCC DEV0 strap0
  ### @li FALSE - Disable ACS in RCC DEV0 strap0.
  ### @li TRUE - Enable ACS in RCC DEV0 strap0.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsEnRccDev0|FALSE|BOOLEAN|0x00051020
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable AER in RCC DEV0 Strap0
  ### @li FALSE - Disable AER in RCC DEV0 Strap0.
  ### @li TRUE - Enable AER in RCC DEV0 Strap0.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAerEnRccDev0|FALSE|BOOLEAN|0x00051021
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable Data Link Feature for RCC BIF Strap1
  ### @li FALSE - Disable Data Link Feature for RCC BIF Strap1.
  ### @li TRUE - Enable Data Link Feature for RCC BIF Strap1.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDlfEnStrap1|TRUE|BOOLEAN|0x00051022
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable PHY 16GT for RCC BIF Strap1
  ### @li FALSE - Disable PHY 16GT for RCC BIF Strap1.
  ### @li TRUE - Enable PHY 16GT for RCC BIF Strap1.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPhy16gtStrap1|TRUE|BOOLEAN|0x00051023
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable Margin Enabled for RCC BIF Strap1
  ### @li FALSE - Disable Margin Enabled for RCC BIF Strap1.
  ### @li TRUE - Enable Margin Enabled for RCC BIF Strap1.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMarginEnStrap1|TRUE|BOOLEAN|0x00051024
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable ACS Source Validation in ACS capability Strap5
  ### @li FALSE - Disable ACS Source Validation in ACS capability Strap5.
  ### @li TRUE - EnableACS Source Validation in ACS capability Strap5.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsSourceValStrap5|TRUE|BOOLEAN|0x00051025
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable Translation Blocking in ACS capability Strap 5
  ### @li FALSE - Disable Translation Blocking in ACS capability Strap 5.
  ### @li TRUE - Enable Translation Blocking in ACS capability Strap 5.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsTranslationalBlockingStrap5|TRUE|BOOLEAN|0x00051026
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable ACS P2P request redirect in ACS capability strap5
  ### @li FALSE - Disable ACS P2P request redirect in ACS capability strap5.
  ### @li TRUE - Enable ACS P2P request redirect in ACS capability strap5.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsP2pReqStrap5|TRUE|BOOLEAN|0x00051027
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable P2P completion redirect in ACS capability Strap 5
  ### @li FALSE - Disable P2P completion redirect in ACS capability Strap 5.
  ### @li TRUE - Enable P2P completion redirect in ACS capability Strap 5.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsP2pCompStrap5|TRUE|BOOLEAN|0x00051028
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable Upstream forwarding in ACS capability Strap5
  ### @li FALSE - Disable Upstream forwarding in ACS capability Strap5.
  ### @li TRUE - Enable Upstream forwarding in ACS capability Strap5.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsUpstreamFwdStrap5|TRUE|BOOLEAN|0x00051029
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable P2P Egress control capability Strap 5
  ### @li FALSE - Disable P2P Egress control capability Strap 5.
  ### @li TRUE - Enable P2P Egress control capability Strap 5.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsP2PEgressStrap5|FALSE|BOOLEAN|0x00051030
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable ACS direct translated P2P in Strap5
  ### @li FALSE - Disable ACS direct translated P2P in Strap5.
  ### @li TRUE - Enable ACS direct translated P2P in Strap5.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsDirectTranslatedStrap5|TRUE|BOOLEAN|0x00051031
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable SSID Enabled in Strap 5
  ### @li FALSE - Disable SSID Enabled in Strap 5.
  ### @li TRUE - Enable SSID Enabled in Strap 5.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsSsidEnStrap5|TRUE|BOOLEAN|0x00051032
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable PRI Enable for PCIE page request control
  ### @li FALSE - Disable PRI Enable for PCIE page request control.
  ### @li TRUE - Enable PRI Enable for PCIE page request control.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPriEnPageReq|TRUE|BOOLEAN|0x00051033
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable PRI reset for PCIE page request control
  ### @li FALSE - Disable PRI reset for PCIE page request control.
  ### @li TRUE - Enable PRI reset for PCIE page request control.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPriResetPageReq|FALSE|BOOLEAN|0x00051034
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable ACS Source Validation in PCIE ACS control
  ### @li FALSE - Disable ACS Source Validation in PCIE ACS control.
  ### @li TRUE - Enable ACS Source Validation in PCIE ACS control.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsSourceVal|TRUE|BOOLEAN|0x00051035
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable Translation Blocking in PCIE ACS control
  ### @li FALSE - Disable Translation Blocking in PCIE ACS control.
  ### @li TRUE - Enable Translation Blocking in PCIE ACS control.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsTranslationalBlocking|FALSE|BOOLEAN|0x00051036
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable ACS P2P request redirect in PCIE ACS control
  ### @li FALSE - Disable ACS P2P request redirect in PCIE ACS control.
  ### @li TRUE - Enable ACS P2P request redirect in PCIE ACS control.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsP2pReq|TRUE|BOOLEAN|0x00051037
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable P2P completion redirect in PCIE ACS control
  ### @li FALSE - Disable P2P completion redirect in PCIE ACS control.
  ### @li TRUE - Enable P2P completion redirect in PCIE ACS control.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsP2pComp|TRUE|BOOLEAN|0x00051038
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable ACS Upstream forwarding in PCIE ACS control
  ### @li FALSE - Disable ACS Upstream forwarding in PCIE ACS control.
  ### @li TRUE - Enable ACS Upstream forwarding in PCIE ACS control.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsUpstreamFwd|TRUE|BOOLEAN|0x00051039
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable P2P Egress control in PCIE ACS control
  ### @li FALSE - Disable P2P Egress control in PCIE ACS control.
  ### @li TRUE - Enable P2P Egress control in PCIE ACS control.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsP2PEgress|FALSE|BOOLEAN|0x00051040
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable End to End TLP prefix setting
  ### @li FALSE - Disable End to End TLP prefix setting.
  ### @li TRUE - Enable End to End TLP prefix setting.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdRccDev0E2EPrefix|FALSE|BOOLEAN|0x00051041
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enable Extended Format support.  It must be set for functions that support End-End TLP Prefixes.
  ### @li FALSE - Disable Extended Format support.
  ### @li TRUE - Enable Extended Format support.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdRccDev0ExtendedFmtSupported|FALSE|BOOLEAN|0x00051042
  ### @endcond

  ### @cond (MDN)
  ### @brief This pcd controls if send message to Dxio fw to enable TX_VBOOST_EN=0.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgDxioTxVboostEnSetting|0|UINT8|0x00051043
  ### @endcond

  # Some PCDs must be updated from BOOLEAN to UINT8 to support auto value
  # Creating Version 2 of those PCDs

  ### @cond (BRH)
  ### @brief This value determines if AGESA will enable the Data Link Feature capability on all PCIe ports Gen4 or higher.
  ### @brief Permitted Choices: (Type: uint8)(Default: Auto)
  ### @li 0x0 - Disable Data Link Feature
  ### @li 0x1 - Enable Data Link Feature
  ### @li 0xF - Auto: Default to platform configurations
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDlfCapEnV2|0xF|UINT8|0x00051044
  ### @endcond

  ### @cond (BRH)
  ### @brief This value determines if AGESA will enable Data Link Feature Exchange on all PCIe ports Gen4 or higher. The PCIe specification explicitly provides this option
  ### so a root compile can disable the DL Feature Exchange transactions in the event a PCIe endpoint is present in a system that cannot respond properly to those packets.
  ### @brief Permitted Choices: (Type: uint8)(Default: Auto)
  ### @li 0x0 - Disable Data Link Feature Exchange
  ### @li 0x1 - Enable Data Link Feature Exchange
  ### @li 0xF - Auto: Default to platform configurations
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDlfExEnV2|0xF|UINT8|0x00051045
  ### @endcond

  ### @brief This value defines the 'OEM identifier' used when creating the IVRS ACPI table.
  ### @brief Permitted Choices: (Type: String, up to 8chars)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiIvrsTableHeaderOemTableId|"AmdTable"|VOID*|0x000AC00D

  ### @cond (ZP||SSP||VMR)
  ### @brief This value defines the 'OEM identifier' used when creating the ALIB SSDT ACPI table.
  ### @brief Permitted Choices: (Type: String, up to 8chars)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiAlibSsdtTableHeaderOemTableId|"AmdTable"|VOID*|0x000AC007
  ### @endcond

  ### @cond (FF3||PHX||RMB||RPL||RV||ZP)
  ### @brief This value controls whether the fan control should be forced to a fixed PWM value.
  ### @li TRUE - The fan speed will be fixed at the PWM value provided by PcdForceFanPwm.
  ### @li FALSE - The fan speed will be controlled by the default fan table or the PCD override values.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdForceFanPwmEn|0x00|UINT8|0x0004F031
  ### @endcond

  ### @cond (FF3||PHX||RMB||RPL||RV||ZP)
  ### @brief This value controls the fixed PWM value when PcdForceFanPwmEn is set to true. A value between 0 and 100 (decimal) specifies the percentage of "on" time for the fan PWM.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdForceFanPwm|0x00|UINT8|0x0004F032
  ### @endcond

  ### @cond (FF3||RMB||RN||RPL||VMR)
  ### @brief Override Fan Table Control Options
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFanTableOverride|0x00|UINT8|0x0004F033
  ### @endcond

  ### @cond (FF3||PHX||RMB||RPL||RV||ZP||CZN||AR||VMR)
  ### @brief Override Fan Table Hysteresis when PcdFanTableOverride is enabled.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFanTableHysteresis|0x00|UINT8|0x0004F034
  ### @endcond

  ### @cond (FF3||PHX||RMB||RPL||RV||ZP||AR||CZN||VMR)
  ### @brief temperature threshold at which the speed of the fan will be increased
  ### @details This value indicates the temperature threshold at which the speed of the fan will be increased. The
  ### temperature is specified in degrees C. For each temperature threshold, there is also an associated PWM
  ### percentage value used to select the fan speed when the temperature exceeds the selected value. Hysteresis
  ### control is provided by another control.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFanTableTempLow|0x00|UINT8|0x0004F035
  ### @endcond

  ### @cond (FF3||PHX||RMB||RPL||RV||ZP||AR||CZN||VMR)
  ### @brief temperature threshold at which the speed of the fan will be increased
  ### @details This value indicates the temperature threshold at which the speed of the fan will be increased. The
  ### This value indicates the temperature threshold at which the speed of the fan will be increased. The
  ### temperature is specified in degrees C. For each temperature threshold, there is also an associated PWM
  ### percentage value used to select the fan speed when the temperature exceeds the selected value. Hysteresis
  ### control is provided by another control.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFanTableTempMed|0x00|UINT8|0x0004F036
  ### @endcond

  ### @cond (FF3||PHX||RMB||RPL||RV||ZP||AR||CZN||VMR)
  ### @brief temperature threshold at which the speed of the fan will be increased
  ### @details This value indicates the temperature threshold at which the speed of the fan will be increased. The
  ### This value indicates the temperature threshold at which the speed of the fan will be increased. The
  ### temperature is specified in degrees C. For each temperature threshold, there is also an associated PWM
  ### percentage value used to select the fan speed when the temperature exceeds the selected value. Hysteresis
  ### control is provided by another control.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFanTableTempHigh|0x00|UINT8|0x0004F037
  ### @endcond

  ### @cond (FF3||PHX||RMB||RPL||RV||ZP||AR||CZN||VMR)
  ### @brief temperature at which the fan speed will be set to 100%
  ### @details This value indicates the temperature at which the fan speed will be set to 100% on (full speed). The
  ### temperature is specified in degrees C.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFanTableTempCritical|0x00|UINT8|0x0004F038
  ### @endcond

  ### @cond (FF3||PHX||RMB||RPL||RV||ZP||AR||CZN||VMR)
  ### @brief PWM cycle ratio
  ### @details This value indicates the fan speed for the associated temperature level (PWM cycle ratio). The value is
  ### specified in a percentage of on time from 0 to 100.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFanTablePwmLow|0x00|UINT8|0x0004F039
  ### @endcond

  ### @cond (FF3||PHX||RMB||RPL||RV||ZP||AR||CZN||VMR)
  ### @brief PWM cycle ratio
  ### @details This value indicates the fan speed for the associated temperature level (PWM cycle ratio). The value is
  ### specified in a percentage of on time from 0 to 100.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFanTablePwmMed|0x00|UINT8|0x0004F03A
  ### @endcond

  ### @cond (FF3||PHX||RMB||RPL||RV||ZP||AR||CZN||VMR)
  ### @brief PWM cycle ratio
  ### @details This value indicates the fan speed for the associated temperature level (PWM cycle ratio). The value is
  ### specified in a percentage of on time from 0 to 100.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFanTablePwmHigh|0x00|UINT8|0x0004F03B
  ### @endcond

  ### @cond (FF3||PHX||RMB||RPL||RV||ZP||AR||CZN||VMR)
  ### @brief Pulse Width Modulation
  ### @details Selects the base frequency for the Pulse Width Modulation (PWM).
  ### @li 0 - 25kHz base frequency
  ### @li 1 - 100Hz base frequency
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFanTablePwmFreq|0x00|UINT8|0x0004F03C
  ### @endcond

  ### @cond (FF3||MDN||PHX||RMB||RPL||RV||STX||V10||V9||ZP||AR||CZN||VMR)
  ### @brief This value identifies the polarity of the fan control signal used for PWM fan control.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x0001)
  ### @li 0 - The fan control signal has a negative polarity (active low)
  ### @li 1 - The fan control signal has a positive polarity
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFanTablePolarity|0x00|UINT8|0x0004F03D
  ### @endcond

#----------------------------------------------------------------------------
#-  NBIO Dynamic PCDs
### Set Doxy_path: "PCD-NBIO.h"
#----------------------------------------------------------------------------

  #GFX v1_11

  ### @cond (RV||FF3||RMB||RN||RPL||PHX)
  ### @brief Dp Phy Override
  ### @li ENABLE_DVI_TUNINGSET    = 0x01
  ### @li ENABLE_HDMI_TUNINGSET   = 0x02
  ### @li ENABLE_HDMI6G_TUNINGSET = 0x04
  ### @li ENABLE_DP_TUNINGSET     = 0x08
  ### @li ENABLE_DP_HBR3_TUNINGSET= 0x10
  ### @li ENABLE_DP_HBR_TUNINGSET = 0x20
  ### @li ENABLE_HBR2_TUNINGSET   = 0x40
  ### @li ENABLE_EDP_TUNINGSET    = 0x80
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDpPhyOverride|0x00|UINT16|0x0004F040
  ### @endcond

  ### @cond (RV)
  ### @brief DVI max symclk in10khz
  ### @li 400000 = override DP1 and DP2 phy HDMI setting above 4Ghz
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDVI_max_symclk_in10khz|0x00|UINT32|0x0004F041
  ### @endcond

  ### @cond (RV)
  ### @brief DVI encoder mode
  ### @li 2 - DVI
  ### @li 3 - HDMI mode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDVI_encoder_mode|0x00|UINT8|0x0004F042
  ### @endcond

  ### @cond (RV)
  ### @brief DVI phy sel
  ### @li bit0= phya, bit1=phyb, ....bit5 = phyf
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDVI_phy_sel|0x00|UINT8|0x0004F043
  ### @endcond

  ### @cond (RV)
  ### @brief COMMON_MAR_DEEMPH_NOM DVI_margindeemph
  ### @li [7:0]tx_margin_nom [15:8]deemph_gen1_nom
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDVI_margindeemph|0x00|UINT16|0x0004F044
  ### @endcond

  ### @cond (RV)
  ### @brief COMMON_SELDEEMPH60 DVI_deemph_6db_4
  ### @li [31:24]deemph_6db_4
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDVI_deemph_6db_4|0x00|UINT8|0x0004F045
  ### @endcond

  ### @cond (RV)
  ### @brief DVI_boostadj
  ### @li [19:16]tx_boost_adj  [20]tx_boost_en  [23:22]tx_binary_ron_code_offset
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDVI_boostadj|0x00|UINT8|0x0004F046
  ### @endcond

  ### @cond (RV)
  ### @brief COMMON_ZCALCODE_CTRL[21].tx_driver_fifty_ohms
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDVI_tx_driver_fifty_ohms|0x00|UINT8|0x0004F047
  ### @endcond

  ### @cond (RV)
  ### @brief MARGIN_DEEMPH_LANE0.DEEMPH_SEL
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDVI_deemph_sel|0x00|UINT8|0x0004F048
  ### @endcond

  ### @cond (RV)
  ### @brief
  ### @li 400000 = override DP1 and DP2 phy HDMI setting above 4Ghz
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHDMI_max_symclk_in10khz|0x00|UINT32|0x0004F049
  ### @endcond

  ### @cond (RV)
  ### @brief HDMI_encoder_mode
  ### @li 2: DVI, 3: HDMI mode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHDMI_encoder_mode|0x00|UINT8|0x0004F04A
  ### @endcond

  ### @cond (RV)
  ### @brief PcdAmdHDMI_phy_sel
  ### @li # bit0= phya, bit1=phyb, ....bit5 = phyf
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHDMI_phy_sel|0x00|UINT8|0x0004F04B
  ### @endcond

  ### @cond (RV)
  ### @brief COMMON_MAR_DEEMPH_NOM[7:0]tx_margin_nom [15:8]deemph_gen1_nom
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHDMI_margindeemph|0x00|UINT16|0x0004F04C
  ### @endcond

  ### @cond (RV)
  ### @brief COMMON_SELDEEMPH60[31:24]deemph_6db_4
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHDMI_deemph_6db_4|0x00|UINT8|0x0004F04D
  ### @endcond

  ### @cond (RV)
  ### @brief # CMD_BUS_GLOBAL_FOR_TX_LANE0
  ### @li [19:16]tx_boost_adj  [20]tx_boost_en  [23:22]tx_binary_ron_code_offset
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHDMI_boostadj|0x00|UINT8|0x0004F04E
  ### @endcond

  ### @cond (RV)
  ### @brief COMMON_ZCALCODE_CTRL[21].tx_driver_fifty_ohms
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHDMI_tx_driver_fifty_ohms|0x00|UINT8|0x0004F04F
  ### @endcond

  ### @cond (RV)
  ### @brief MARGIN_DEEMPH_LANE0.DEEMPH_SEL
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHDMI_deemph_sel|0x00|UINT8|0x0004F050
  ### @endcond

  ### @cond (RV)
  ### @brief 400000 = override DP1 and DP2 phy HDMI setting above 4Ghz
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHDMI6G_max_symclk_in10khz|0x00|UINT32|0x0004F051
  ### @endcond

  ### @cond (RV)
  ### @brief PcdAmdHDMI6G_encoder_mode
  ### @li 2: DVI
  ### @li 3: HDMI mode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHDMI6G_encoder_mode|0x00|UINT8|0x0004F052
  ### @endcond

  ### @cond (RV)
  ### @brief PcdAmdHDMI6G_phy_sel
  ### @li bit0= phya, bit1=phyb, ....bit5 = phyf
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHDMI6G_phy_sel|0x00|UINT8|0x0004F053
  ### @endcond

  ### @cond (RV)
  ### @brief COMMON_MAR_DEEMPH_NOM
  ### @li [7:0]tx_margin_nom [15:8]deemph_gen1_nom
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHDMI6G_margindeemph|0x00|UINT16|0x0004F054
  ### @endcond

  ### @cond (RV)
  ### @brief COMMON_SELDEEMPH60[31:24]deemph_6db_4
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHDMI6G_deemph_6db_4|0x00|UINT8|0x0004F055
  ### @endcond

  ### @cond (RV)
  ### @brief CMD_BUS_GLOBAL_FOR_TX_LANE0
  ### @li [19:16]tx_boost_adj  [20]tx_boost_en  [23:22]tx_binary_ron_code_offset
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHDMI6G_boostadj|0x00|UINT8|0x0004F056
  ### @endcond

  ### @cond (RV)
  ### @brief COMMON_ZCALCODE_CTRL[21].tx_driver_fifty_ohms
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHDMI6G_tx_driver_fifty_ohms|0x00|UINT8|0x0004F057
  ### @endcond

  ### @cond (RV)
  ### @brief MARGIN_DEEMPH_LANE0.DEEMPH_SEL
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHDMI6G_deemph_sel|0x00|UINT8|0x0004F058
  ### @endcond

  ### @cond (RV||FF3||RN)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdBackLightPwmHz|200|UINT16|0x0004F059
  ### @endcond

  ### @cond (CZN||RMB||RPL||MDN||PHX)
  ### @brief IVRS exclusion range pointer
  ### @details This points to a structure of IOMMU_EXCLUSION_RANGE_DESCRIPTOR
  ### @code
  ###  struct {
  ###    IN       UINT32               Flags;
  ###    IN       IOMMU_REQUESTOR_ID   RequestorIdStart;
  ###    IN       IOMMU_REQUESTOR_ID   RequestorIdEnd;
  ###    IN       UINT64               RangeBaseAddress;
  ###    IN       UINT64               RangeLength;
  ###  } IOMMU_EXCLUSION_RANGE_DESCRIPTOR;
  ### @endcode
  ### @li                              Flags - Flags[31] - Terminate descriptor array.
  ### @li                                      Flags[30] - Ignore descriptor.
  ### @li                              RequestorIdStart - Requestor ID start
  ### @li                              RequestorIdEnd - Requestor ID end (use same as start for single ID)
  ### @li                              RangeBaseAddress - Phisical base address of exclusion range
  ### @li                              RangeLength - Length of exclusion range in bytes
  ### Add one or more IVMD lists, and the end should be the Terminate descriptor Flags[31]=1
  ### IOMMU_EXCLUSION_RANGE_DESCRIPTOR  IvmdList[] = {
  ###   {0, {0, 0, 0}, {0, 0, 0}, 0, 0},
  ###   {(UINT32)(1 << 31), 0, 0, 0, 0}  //TERMINATE_IVMD
  ### };
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdIvrsExclusionRangePtr|0|UINT64|0x0004F05D
  ### @endcond

  ### @cond (RV||RN)
  ### @name Power sequence numbers (in uint of 4ms)
  ### @{
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPwrOnDigonToDe|0x0|UINT8|0x0004F060
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPwrOnDeToVaryBl|0x0|UINT8|0x0004F061
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPwrDownVaryBloffToDe|0x0|UINT8|0x0004F062
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPwrDownDeToDigOff|0x0|UINT8|0x0004F063
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPwrOffDelay|0x0|UINT8|0x0004F064
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPwrOnVaryBlToBlon|0x0|UINT8|0x0004F065
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPwrDownBloffToVaryBlOff|0x0|UINT8|0x0004F066
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMinAllowedBlLevel|0x0|UINT8|0x0004F067
  ###@}
  ### @endcond

  ### @cond (RV)
  ### @name DP Phy Settings
  ### @{
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDpPhySel|0x00|UINT8|0x0004F068
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAtomDpPhyDpSetting|0x00|UINT32|0x0004F071
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDpHbr3PhySel|0x00|UINT8|0x0004F072
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAtomDpHbr3PhyDpSetting|0x00|UINT32|0x0004F075
  ###@}
  ### @endcond

  ### @cond (RV||RMB||RN||RPL||CZ||PHX)
  ### @brief Max Number of Audio Endpoints
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgMaxNumAudioEndpoints|0x4|UINT8|0x0004F076
  ### @endcond

  ### @cond (RV||FF3||RN)
  ### @name HBR2DisableX
  ### @{
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDpHBR2Disable0|FALSE|BOOLEAN|0x0004F077
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDpHBR2Disable1|FALSE|BOOLEAN|0x0004F078
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDpHBR2Disable2|FALSE|BOOLEAN|0x0004F079
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDpHBR2Disable3|FALSE|BOOLEAN|0x0004F07A
  ###@}
  ### @endcond

  ### @cond (RV||FF3||RN)
  ### @name HBR3DisableX
  ### @{
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDpHBR3Disable0|FALSE|BOOLEAN|0x0004F07B
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDpHBR3Disable1|FALSE|BOOLEAN|0x0004F07C
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDpHBR3Disable2|FALSE|BOOLEAN|0x0004F07D
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDpHBR3Disable3|FALSE|BOOLEAN|0x0004F07E
  ###@}
  ### @endcond

  ### @cond (RV||FF3||RN)
  ### @name HDMI2DisableX
  ### @{
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdHDMI2Disable0|FALSE|BOOLEAN|0x0004F07F
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdHDMI2Disable1|FALSE|BOOLEAN|0x0004F080
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdHDMI2Disable2|FALSE|BOOLEAN|0x0004F081
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdHDMI2Disable3|FALSE|BOOLEAN|0x0004F082
  ###@}
  ### @endcond

  ### @cond (RV)
  ### @name DP HBR Phy Settings
  ### @{
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDpHbrPhySel|0x00|UINT8|0x0004F083
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAtomDpHbrTuningSetting|0x00|UINT32|0x0004F086
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDpHbr2PhySel|0x00|UINT8|0x0004F087
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAtomDpHbr2TuningSetting|0x00|UINT32|0x0004F090
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdEDpPhySel|0x00|UINT8|0x0004F091
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAtomEDpTuningSetting|0x00|UINT32|0x0004F094
  ### @}
  ### @endcond

  ### @cond (RV||FF3||RMB||RN||RPL||PHX)
  ### @brief Enable PEI GOP feature
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPeiGopEnable|FALSE|BOOLEAN|0x0004F095
  ### @endcond

  ### @cond (RN||SSP)
  ### @brief Enable/Disable CCIX
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCcixEnable|FALSE|BOOLEAN|0x0004F096
  ### @endcond

  ### @cond (RMB||RN||RPL||PHX)
  ### @brief Disable all audio end points
  ### @li TRUE - Disable
  ### @li FALSE - Not disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgDisableAllNumAudioEndpoints|FALSE|BOOLEAN|0x0004F097
  ### @endcond

  ### @name HDMI ReTimer
  ###@{

  ### @cond (FF3||RN)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdHDMIRetimerCaps0|FALSE|BOOLEAN|0x0004F098
  ### @endcond

  ### @cond (FF3||RN)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdHDMIRetimerCaps1|FALSE|BOOLEAN|0x0004F099
  ### @endcond

  ### @cond (FF3||RN)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdHDMIRetimerCaps2|FALSE|BOOLEAN|0x0004F09A
  ### @endcond

  ### @cond (FF3||RN)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdHDMIRetimerCaps3|FALSE|BOOLEAN|0x0004F09B
  ### @endcond

  ### @cond (RN)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdHDMIRetimerCaps4|FALSE|BOOLEAN|0x0004F09C
  ### @endcond
  ###@}

  ### @cond (RN)
  ### @name Dp HBR Disable
  ### @{
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDpHBR2Disable4|FALSE|BOOLEAN|0x0004F09D
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDpHBR3Disable4|FALSE|BOOLEAN|0x0004F09E
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdHDMI2Disable4|FALSE|BOOLEAN|0x0004F09F
  ###@}
  ### @endcond

#----------------------------------------------------------------------------
#-  NBIO Dynamic PCDs
### Set Doxy_path: "PCD-NBIO-GNB.h"
#----------------------------------------------------------------------------

  ### @name GNB SMU 10 CBS debug options
  ### @cond (PHX||RS||V10||V9||BA||CZN||GN||RMB||RN||RPL||SSP||VMR||BRH)
  ###@{

  ### These values define overrides for the specified current limits in mA.
  ### @li 0 - Use fused default value.
  ### @li XX - Specifies the override value.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTelemetry_VddcrVddfull_Scale_Current|0x00|UINT32|0x0004F0A0
  ### This value defines overrides for the specified voltage offsets in mV.
  ### @li 0 - Use fused default value.
  ### @li XX - Specifies the override value.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTelemetry_VddcrVddOffset|0x00|UINT32|0x0004F0A1
  ### These values define overrides for the specified current limits in mA.
  ### @li 0 - Use fused default value.
  ### @li XX - Specifies the override value.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTelemetry_VddcrSocfull_Scale_Current|0x00|UINT32|0x0004F0A2
  ### This value defines overrides for the specified voltage offsets in mV.
  ### @li 0 - Use fused default value.
  ### @li XX - Specifies the override value.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTelemetry_VddcrSocOffset|0x00|UINT32|0x0004F0A3
  ###@}
  ### @endcond

#----------------------------------------------------------------------------
#-  NBIO Dynamic PCDs
### Set Doxy_path: "PCD-NBIO.h"
#----------------------------------------------------------------------------

  ### @cond (PHX||V10||CZN||RMB||RN||RPL)
  ### @name PPT Limit
  ### @{
  ### @brief Please see the reference for the Infrastructure Roadmap for more details. This value may be changed at run-time via the ALIB functions.
  ### @li 0 - Use fused values.
  ### @li XX - Specifies the override value.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFastPptLimit|0x00|UINT32|0x0004F0A4
  ### @brief Please see the reference for the Infrastructure Roadmap for more details. This value may be changed at run-time via the ALIB functions.
  ### @li 0 - Use fused values.
  ### @li XX - Specifies the override value.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSlowPptLimit|0x00|UINT32|0x0004F0A5
  ### @brief This value defines the time constant for "slow" PPT calculations.
  ### @li 0 - Use fused default values.
  ### @li XX - Specifies the override value.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSlowPptTimeConstant|0x00|UINT32|0x0004F0A6
  ###@}
  ### @endcond

  ### @cond (V10||CZN||RN)
  ### @brief These values define the low power thresholds for the voltage regulators.
  ### @li 0 - Use fused default value.
  ### @li XX - Specifies the override value.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdVrmLowPowerThreshold|0x00|UINT32|0x0004F0A7
  ### @endcond

  ### @cond (V10||CZN||RN)
  ### @brief These values define the low power thresholds for the voltage regulators.
  ### @li 0 - Use fused default value.
  ### @li XX - Specifies the override value.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdVrmSocLowPowerThreshold|0x00|UINT32|0x0004F0A8
  ### @endcond

  ### @cond (PHX||V10||CZN||RMB||RN||RPL)
  ### @brief This value defines the Thermal Control value.
  ### @li 0 - Use fused default value.
  ### @li XX - Specifies the override value.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgThermCtlValue|0x00|UINT32|0x0004F0A9
  ### @endcond

  ### @cond (PHX||V10||AR||CZN||FF3||RMB||RN||RPL)
  ### @name Current and Power Limit
  ### @{
  ### @brief This value indicates the maximum current that the voltage regulator is capable of providing to the VDD pins of the processor for This a numeric value expressed
  ### in milliamperes. For example, a current limit of 14.5 amperes is represented as: decimal 14,500.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdVrmMaximumCurrentLimit|0x00|UINT32|0x0004F0AA
  ### @brief This value indicates the maximum current that the voltage regulator is capable of providing to the SOC pins of the processor for This a numeric value expressed
  ### in milliamperes. For example, a current limit of 14.5 amperes is represented as: decimal 14,500.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdVrmSocMaximumCurrentLimit|0x00|UINT32|0x0004F0AB
  ### @brief This values specifies the sustained power limit that can be supported by the platform thermal solution and chassis thermal design. A value of zero means to use
  ### the fused value for the part. This limit is managed within the STAPM time constant. This a numeric value expressed in milliwatts. For example, a power limit of 14.25
  ### watts is represented as: decimal 14,250.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSustainedPowerLimit|0x00|UINT32|0x0004F0AC
  ### @brief This value specifies the Time Constant used by the power monitor. This is a rolling average window of time used by the monitor. A value of zero means to use the
  ### fused value for the part. Passing in 200 will indicate the heat up time constant is 200 seconds.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdStapmTimeConstant|0x00|UINT32|0x0004F0AD
  ### @brief This specifies the time to take to ramp the CCLK/GFXCLK clocks up to Fmax from Fmin when PROCHOT_L is deasserted. A value of zero means to use the default value
  ### (20ms). When PROCHOT_L is deasserted, a jump to boost could cause a large current in-rush. Some systems could experience a problem with their power supply. This control
  ### is provided to lengthen the ramp time and lessen the current in-rush. This a numeric value expressed in milliseconds. For example, a deassertion time of 32 milliseconds
  ### is represented as: decimal 32.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdProchotlDeassertionRampTime|0|UINT32|0x0004F0AE
  ###@}
  ### @endcond

  ### @cond (PHX||V10||AR||CZN||RMB||RN||RPL)
  ### @brief This value indicates the maximum current that the voltage regulators is capable of providing to the VDD of the processor on a
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdVrmCurrentLimit|0x00|UINT32|0x0004F0AF
  ### @endcond

  ### @cond (PHX||V10||AR||CZN||FF3||RMB||RN||RPL)
  ### @brief This value indicates the maximum current that the voltage regulators is capable of providing to the SOC pins of the processor on a
  ### @brief This a numeric value expressed in milliamperes. For example, a current limit of 12.6 amperes is represented as: decimal 12,600.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdVrmSocCurrentLimit|0x00|UINT32|0x0004F0B0
  ### @endcond

  ### @cond (V10)
  ### @brief This value defines the frequency associated with Vmin voltage. The value is in megahertz (MHz). If a value of 0 is specified then the fused value will be used.
  ### @brief Example: To select a maximum frequency of 3.6GHz, this PCD should be set to 3600.
  ### @li 0x00 - Use default fused value
  ### @li 0x01..N - Frequency limit in MHz.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdVminFrequency|0x00|UINT32|0x0004F0B1
  ### @endcond

  ### @cond (PHX||V10||AR||CZN||FF3||RMB||RN||RPL||SSP||VMR||RS||BRH)
  ### @brief This value defines the limit on the FCLK frequency.
  ### @li 0 - Use fused default value.
  ### @li XX - Specifies the override value.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFMaxFrequency|0x00|UINT32|0x0004F0B2
  ### @endcond

  ### @cond (RV)
  ### @name Phy Dynamic Control
  ### @{
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPhyA0DynamicControl|0|UINT8|0x0004F0B4
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPhyA1DynamicControl|0|UINT8|0x0004F0B5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPhyA2DynamicControl|0|UINT8|0x0004F0B6
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPhyA34DynamicControl|0|UINT8|0x0004F0B7
  ###@}
  ### @endcond

  ### @cond (V10)
  ### @name These values define overrides for the specified current limits in mA.
  ### @li 0 - Use fused default value.
  ### @li XX - Specifies the override value.
  ### @{

  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTelemetryVddcrVddfullScale2Current|0|UINT32|0x0004F0BC
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTelemetryVddcrVddfullScale3Current|0|UINT32|0x0004F0BD
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTelemetryVddcrVddfullScale4Current|0|UINT32|0x0004F0BE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTelemetryVddcrVddfullScale5Current|0|UINT32|0x0004F0BF
  ###@}
  ### @endcond

  ### @cond (RS||BA||GN||SSP||VMR||BRH)
  ### @brief This value defines the power limits for Package Power Tracking in mW as set by the BIOS. This value must be less than, or equal to, the platform max value.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x0)
  ### @li 0 - Use fused values.
  ### @li XX - Specifies the override value.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPPT|0|UINT32|0x0004F0C0
  ### @endcond

  ### @cond (BA||CZN||GN||RN||SSP||VMR||BRH)
  ### @details CBS debug options
  ### @li 0=Enable CC1 for debug purposes
  ### @li 1=Disable CC1 for debug purposes
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuCc1Dis|0|UINT8|0x0004F0C2
  ### @endcond

  ### @cond (V10)
  ### @details This Boolean selects the SB TSI Alert Comparator Mode.
  ### @li TRUE - Comparator mode enabled.
  ### @li FALSE - Comparator mode disabled.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSbTsiAlertComparatorModeEn|FALSE|BOOLEAN|0x0004F0C3
  ### @endcond

  ### @cond (RV||V10)
  ### @brief
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdGetRVFamilyOPN|0|UINT32|0x0004F0C4
  ### @endcond

  ### @cond (PHX||V10||CZN||RMB||RN||RPL)
  ### @brief GPU Fmax frequency
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdGFXFMaxFrequency|0|UINT32|0x0004F0C5
  ### @endcond

  ### @cond (V10)
  ### @brief
  ### @li 1 - bypass
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCoreDldoBypass|1|UINT8|0x0004F0C6
  ### @endcond

  ### @cond (RN)
  ### @brief
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdGetiGPUFamilyOPN|0|UINT32|0x0004F0C7
  ### @endcond

  ### @cond (SSP)
  ### @brief
  ### @li 0 - Use default CC6 filter setting
  ### @li 1 - Use more efficient CC6 filter setting
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCc6FilterSetting|0|UINT8|0x0004F0C8
  ### @endcond

  ### @cond (CZN||RN)
  ### @brief This control will specify that during Panel Self Refresh(PSR) time, the DCN clock will be paused. This will save power and help extend battery life.
  ### @li FALSE - no action. the DCN clock runs normally.
  ### @li TRUE - the DCN clock will be stopped during the PSR time periods.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdLowerDcnClkinPSRDisabled|0|UINT8|0x0004F0C9
  ### @endcond

  ### @cond (PHX||RMB||RPL)
  ### @name Telemetry calibration
  ### @{

  ### @brief Vddcr SR Saturation Current
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTelemetry_VddcrSrfull_Scale_Current|0x00|UINT32|0x0004F0CA
  ### @brief Vddcr SR Offset
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTelemetry_VddcrSrOffset|0x00|UINT32|0x0004F0CB
  ###@}
  ### @endcond

  ### @name PIC
  ### @{

  ### @cond (AR||CZN||FF3||RN)
  ### @brief Enable/Disable S0i2 feature
  ### @li 1:  Enable
  ### @li 0:  Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdS0i2Enable|0|UINT8|0x0004F0D0
  ### @endcond

  ### @cond (SSP||PHX||V10||AR||CZN||FF3||RMB||RN||RPL||VMR)
  ### @brief Enable/Disable S0i3 feature
  ### @li 1:  Enable
  ### @li 0:  Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdS0i3Enable|0|UINT8|0x0004F0D1
  ### @endcond

  ### @cond (V10)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMinS0i3SleepTimeMs|0|UINT32|0x0004F0D2
  ### @endcond

  ### @cond (V10)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSttMinLimit|0|UINT32|0x0004F0D3
  ### @endcond

  ### @cond (V10)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsbPortsToClearWceWde0|0|UINT8|0x0004F0D4
  ### @endcond

  ### @cond (V10)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdUsbPortsToClearWceWde1|0|UINT8|0x0004F0D5
  ### @endcond

  ### @cond (RV||RMB||RN||RPL||PHX)
  ### @brief Allows the host BIOS to set the PCI Sub-System ID value reported by the iGPU audio controller. This is nBIF Function 1 for Family 17h processors. A value of
  ### zero will indicate that the normal hardware fused value should be used.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCfgGnbIGPUAudioSSID|0|UINT32|0x0004F0D6
  ### @endcond

  ### @cond (RV)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCfgGnbSataSSID|0|UINT32|0x0004F0D7
  ### @endcond
  ###@}

  ### @cond (RPL||DR)
  ### @brief Enable/Disable Prochot feature
  ### @li 0:  Enable
  ### @li 1:  Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdProchotDisable|0|UINT8|0x0004F0D8
  ### @endcond

  ### @cond (RPL||DR)
  ### @brief Enable/Disable PCC feature
  ### @li 0:  Enable
  ### @li 1:  Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPccDisable|0|UINT8|0x0004F0D9
  ### @endcond

  ### @cond (STP||DR||RPL||STXKRK||STXHL||PHX||PHX2||GNR)
  ### @brief Enable/Disable VR_HOT throttle
  ### @li 1:  Enable
  ### @li 0:  Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdVRHOTEnable|0|UINT8|0x0004F0DA
  ### @endcond

#----------------------------------------------------------------------------
#-  NBIO Dynamic PCDs
### Set Doxy_path: "PCD-NBIO-PCIE.h"
#----------------------------------------------------------------------------
  ### @name Pcie Lane Equalization
  ###@{

  ### @cond (GN||RMB||RS||BRH)
  ### @brief Gen3 Downstream Tx Preset
  ### @details Downstream Tx Preset (Gen3)-This PCD value sets the Downstream Port's transmitter preset for initial operation at 8.0 GT/s Link Equalization for all ports.
  ### @li 0x0 - P0
  ### @li 0x1 - P1
  ### @li 0x2 - P2
  ### @li 0x3 - P3
  ### @li 0x4 - P4
  ### @li 0x5 - P5
  ### @li 0x6 - P6
  ### @li 0x7 - P7
  ### @li 0x8 - P8
  ### @li 0x9 - P9
  ### @li 0xA - P10
  ### @li 0xFF - Auto: Default to platform configurations
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieGen3LaneEqDsTxPreset|0xFF|UINT8|0x0004F0E0
  ### @endcond

  ### @cond (GN)
  ### @brief Gen3 Downstream Rx Preset Hint
  ### @details Downstream Rx Preset (Gen3)-This PCD value sets the Downstream Port's receiver preset for initial operation at 8.0 GT/s Link Equalization for all ports.
  ### @li 0x0 - P0
  ### @li 0x1 - P1
  ### @li 0x2 - P2
  ### @li 0x3 - P3
  ### @li 0x4 - P4
  ### @li 0x5 - P5
  ### @li 0x6 - P6
  ### @li 0x7 - P7
  ### @li 0xFF - Auto: Default to platform configurations
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieGen3LaneEqDsRxPresetHint|0xFF|UINT8|0x0004F0E1
  ### @endcond

  ### @cond (GN||RMB||RS||BRH)
  ### @brief Gen3 Upstream Tx Preset
  ### @details Upstream Tx Preset (Gen3)-This PCD value sets the transmitter preset value that the Upstream Port requests the other side to use for initial operation at 8.0 GT/s for all ports.
  ### @li 0x0 - P0
  ### @li 0x1 - P1
  ### @li 0x2 - P2
  ### @li 0x3 - P3
  ### @li 0x4 - P4
  ### @li 0x5 - P5
  ### @li 0x6 - P6
  ### @li 0x7 - P7
  ### @li 0x8 - P8
  ### @li 0x9 - P9
  ### @li 0xA - P10
  ### @li 0xFF - Auto: Default to platform configurations
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieGen3LaneEqUsTxPreset|0xFF|UINT8|0x0004F0E2
  ### @endcond

  ### @cond (GN)
  ### @brief Gen3 Upstream Rx Preset Hint
  ### @details Upstream Rx Preset (Gen3)- This PCD value sets the Receiver preset Upstream Port for initial operation at 8.0 GT/s for all ports.
  ### @li 0x0 - P0
  ### @li 0x1 - P1
  ### @li 0x2 - P2
  ### @li 0x3 - P3
  ### @li 0x4 - P4
  ### @li 0x5 - P5
  ### @li 0x6 - P6
  ### @li 0x7 - P7
  ### @li 0xFF - Auto: Default to platform configurations
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieGen3LaneEqUsRxPresetHint|0xFF|UINT8|0x0004F0E3
  ### @endcond

  ### @cond (GN)
  ### @brief  Disable Gen3 EQ Phase2/3 - Setting this PCD to TRUE bypasses 8.0 GT/s Link Equalization phase 2 and phase 3 for all ports. If these phases are skipped then the settings used in phase 1 will become the operational settings.
  ### @li 1 - Skip phases 2 and 3.
  ### @li 0 - perform full set of initialization phases. Phases are documented in the "Link Equalization Procedure for 8.0 GT/s and Higher Data Rates" section of the PCIE specification.
  ### @li 0xFF - Auto: Default to platform configurations
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieDisGen3EQPhase|0xFF|UINT8|0x0004F0E6
  ### @endcond

  ### @cond (GN)
  ### @brief  Bypass Gen3 EQ Phase - Controls if LTSSM enters the requesting Phase of 8.0 GT/s Link Equalization for all ports.
  ### @li 1 - Bypass Link Equalization requesting Phase.
  ### @li 0 - Perform Link Equalization requesting Phase.
  ### @li 0xFF - Auto: Default to platform configurations
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLinkBypassGen3EQ|0xFF|UINT8|0x0004F22E
  ### @endcond

  ### @cond (RS||STP||MI3||BRH)
  ### @brief Configuration for Gen3 Preset Mask for all ports
  ### @li 0x0 - Custom
  ### @li 0xFF - Auto: Default to platform configurations
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqPresetMask8GtConfig|0xFF|UINT8|0x0004F06E
  ### @endcond

  ### @cond (RS||STP||MI3||BRH)
  ### @brief Gen3 Preset Mask
  ### @details This 32-bit value specifies the default value for the Gen3 PCIE LC Preset Mask Control for all ports.
  ### Only applicable when PcdPcieLaneEqPresetMask8GtConfig is set to Custom, else setting defaults to platform configurations.
  ### @li 0x0 - 0x3FF: Bit mask that when any individual bit value is 1, the corresponding preset is included in the selection for evaluation, while a bit value of 0 excludes the corresponding preset from evaluation.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqPresetMask8Gt|0|UINT32|0x0004F06F
  ### @endcond

  ### @cond (BRH)
  ### @brief  Gen3 Force Preset
  ### @details Overwrites link partner EQ phase 2 preset requests for all ports.
  ### @li 0x0 - Preset 0
  ### @li 0x1 - Preset 1
  ### @li 0x2 - Preset 2
  ### @li 0x3 - Preset 3
  ### @li 0x4 - Preset 4
  ### @li 0x5 - Preset 5
  ### @li 0x6 - Preset 6
  ### @li 0x7 - Preset 7
  ### @li 0x8 - Preset 8
  ### @li 0x9 - Preset 9
  ### @li 0xFF - Auto: Don't force presets
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqForcePreset8Gt|0xFF|UINT8|0x0004F08D
  ### @endcond

  ### @cond (GN||RMB||RS||BRH)
  ### @brief Downstream Tx Preset (Gen4) - This PCD value sets the Downstream Port`s transmitter preset for initial operation at 16.0 GT/s for all ports. This is "Downstream Port 16.0 GT/s Transmitter Preset" field of the "16.0 GT/s Lane Equalization Control Register" documented in the PCIE specification.
  ### @li 0x0 - P0
  ### @li 0x1 - P1
  ### @li 0x2 - P2
  ### @li 0x3 - P3
  ### @li 0x4 - P4
  ### @li 0x5 - P5
  ### @li 0x6 - P6
  ### @li 0x7 - P7
  ### @li 0x8 - P8
  ### @li 0x9 - P9
  ### @li 0xA - P10
  ### @li 0xFF - Auto: Default to platform configurations
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieGen4LaneEqDsTxPreset|0xFF|UINT8|0x0004F0E4
  ### @endcond

  ### @cond (GN||RMB||RS||BRH)
  ### @brief Upstream Tx Preset (Gen4) This PCD value sets the transmitter preset value that the Downstream Port requests the other side to use for initial operation at 16.0 GT/s for all ports. This is the "Upstream Port 16.0 GT/s Transmitter Preset" field of the "16.0 GT/s Lane Equalization Control Register" documented in the PCIE specification.
  ### @li 0x0 - P0
  ### @li 0x1 - P1
  ### @li 0x2 - P2
  ### @li 0x3 - P3
  ### @li 0x4 - P4
  ### @li 0x5 - P5
  ### @li 0x6 - P6
  ### @li 0x7 - P7
  ### @li 0x8 - P8
  ### @li 0x9 - P9
  ### @li 0xA - P10
  ### @li 0xFF - Auto: Default to platform configurations
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieGen4LaneEqUsTxPreset|0xFF|UINT8|0x0004F0E5
  ### @endcond

  ### @cond (GN)
  ### @brief  Disable Gen4 EQ Phase2/3 - Setting this PCD to TRUE bypasses 16.0 GT/s Link Equalization phase 2 and phase 3 for all ports. If these phases are skipped then the settings used in phase 1 will become the operational settings.
  ### @li 1 - Skip phases 2 and 3.
  ### @li 0 - perform full set of initialization phases. Phases are documented in the "Link Equalization Procedure for 16.0 GT/s and Higher Data Rates" section of the PCIE specification.
  ### @li 0xFF - Auto: Default to platform configurations
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieDisGen4EQPhase|0xFF|UINT8|0x0004F0E7
  ### @endcond

  ### @cond (GN)
  ### @brief  Bypass Gen4 EQ Phase - Controls if LTSSM enters the requesting Phase of 16.0 GT/s Link Equalization for all ports.
  ### @li 1 - Bypass Link Equalization requesting Phase.
  ### @li 0 - Perform Link Equalization requesting Phase.
  ### @li 0xFF - Auto: Default to platform configurations
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLinkBypassGen4EQ|0xFF|UINT8|0x0004F22F
  ### @endcond

  ### @cond (GN||SSP)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLinkTxVettingGen4|0xFF|UINT8|0x0004F228
  ### @endcond

  ### @cond (GN||SSP)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLinkRxVettingGen4|0xFF|UINT8|0x0004F229
  ### @endcond

  ### @cond (RS||STP||MI3||BRH)
  ### @brief Configuration for Gen4 Preset Mask for all ports
  ### @li 0x0 - Custom
  ### @li 0xFF - Auto: Default to platform configurations
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqPresetMask16GtConfig|0xFF|UINT8|0x0004F084
  ### @endcond

  ### @cond (RS||STP||MI3||BRH)
  ### @brief Gen4 Preset Mask
  ### @details This 32-bit value specifies the default value for the Gen4 PCIE LC Preset Mask Control for all ports.
  ### Only applicable when PcdPcieLaneEqPresetMask16GtConfig is set to Custom, else setting defaults to platform configurations.
  ### @li 0x0 - 0x3FF: Bit mask that when any individual bit value is 1, the corresponding preset is included in the selection for evaluation, while a bit value of 0 excludes the corresponding preset from evaluation.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqPresetMask16Gt|0|UINT32|0x0004F085
  ### @endcond

  ### @cond (BRH)
  ### @brief  Gen4 Force Preset
  ### @details Overwrites link partner EQ phase 2 preset requests for all ports.
  ### @li 0x0 - Preset 0
  ### @li 0x1 - Preset 1
  ### @li 0x2 - Preset 2
  ### @li 0x3 - Preset 3
  ### @li 0x4 - Preset 4
  ### @li 0x5 - Preset 5
  ### @li 0x6 - Preset 6
  ### @li 0x7 - Preset 7
  ### @li 0x8 - Preset 8
  ### @li 0x9 - Preset 9
  ### @li 0xFF - Auto: Don't force presets
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqForcePreset16Gt|0xFF|UINT8|0x0004F08E
  ### @endcond

  ### @cond (RS||BRH)
  ### @brief Downstream Tx Preset (Gen5) - This PCD value sets the Downstream Port`s transmitter preset for initial operation at 32.0 GT/s for all ports. This is "Downstream Port 32.0 GT/s Transmitter Preset" field of the "32.0 GT/s Lane Equalization Control Register" documented in the PCIE specification.
  ### @li 0x0 - P0
  ### @li 0x1 - P1
  ### @li 0x2 - P2
  ### @li 0x3 - P3
  ### @li 0x4 - P4
  ### @li 0x5 - P5
  ### @li 0x6 - P6
  ### @li 0x7 - P7
  ### @li 0x8 - P8
  ### @li 0x9 - P9
  ### @li 0xA - P10
  ### @li 0xFF - Auto: Default to platform configurations
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieGen5LaneEqDsTxPreset|0xFF|UINT8|0x0004F0E8
  ### @endcond

  ### @cond (RS||BRH)
  ### @brief Upstream Tx Preset (Gen5) This PCD value sets the transmitter preset value that the Downstream Port requests the other side to use for initial operation at 32.0 GT/s for all ports. This is the "Upstream Port 32.0 GT/s Transmitter Preset" field of the "32.0 GT/s Lane Equalization Control Register" documented in the PCIE specification.
  ### @li 0x0 - P0
  ### @li 0x1 - P1
  ### @li 0x2 - P2
  ### @li 0x3 - P3
  ### @li 0x4 - P4
  ### @li 0x5 - P5
  ### @li 0x6 - P6
  ### @li 0x7 - P7
  ### @li 0x8 - P8
  ### @li 0x9 - P9
  ### @li 0xA - P10
  ### @li 0xFF - Auto: Default to platform configurations
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieGen5LaneEqUsTxPreset|0xFF|UINT8|0x0004F0E9
  ### @endcond

  ### @cond (RS||RPL||STP||BRH)
  ### @brief Advertise EQ To High Rate Support
  ### @details  Controls the ability to advertise Equalization Bypass to Highest Rate Support in TSxs sent prior to LinkUp=1.
  ### @li TRUE - Enable
  ### @li FALSE - Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAdvertiseEqToHighRateSupport|FALSE|BOOLEAN|0x000A650C
  ### @endcond

  ### @cond (RS||STP||MI3||BRH)
  ### @brief Configuration for Gen5 Preset Mask for all ports
  ### @li 0x0 - Custom
  ### @li 0xFF - Auto: Default to platform configurations
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqPresetMask32GtConfig|0xFF|UINT8|0x0004F08B
  ### @endcond

  ### @cond (RS||STP||MI3||BRH)
  ### @brief Gen5 Preset Mask
  ### @details This 32-bit value specifies the default value for the Gen5 PCIE LC Preset Mask Control for all ports.
  ### Only applicable when PcdPcieLaneEqPresetMask32GtConfig is set to Custom, else setting defaults to platform configurations.
  ### @li 0x0 - 0x3FF: Bit mask that when any individual bit value is 1, the corresponding preset is included in the selection for evaluation, while a bit value of 0 excludes the corresponding preset from evaluation.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqPresetMask32Gt|0|UINT32|0x0004F08C
  ### @endcond

  ### @cond (BRH)
  ### @brief  Gen5 Force Preset
  ### @details Overwrites link partner EQ phase 2 preset requests for all ports.
  ### @li 0x0 - Preset 0
  ### @li 0x1 - Preset 1
  ### @li 0x2 - Preset 2
  ### @li 0x3 - Preset 3
  ### @li 0x4 - Preset 4
  ### @li 0x5 - Preset 5
  ### @li 0x6 - Preset 6
  ### @li 0x7 - Preset 7
  ### @li 0x8 - Preset 8
  ### @li 0x9 - Preset 9
  ### @li 0xFF - Auto: Don't force presets
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqForcePreset32Gt|0xFF|UINT8|0x0004F08F
  ### @endcond
  ###@} end Pcie Lane Equalization

  ### @cond (RPL||PHX)
  ### @brief Enable or disable dGPU only mode.
  ### @li TRUE  - enable
  ### @li FALSE - disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgdGPUOnlyModeEnable|FALSE|BOOLEAN|0x0004F06A
  ### @endcond

  ### @cond (RPL||PHX)
  ### @brief combin training early and normal, enable/disable combin PCIe training.
  ### @li TRUE  - enable
  ### @li FALSE - disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgCombinTrainingEnable|FALSE|BOOLEAN|0x0004F06B
  ### @endcond

#----------------------------------------------------------------------------
#-  NBIO Dynamic PCDs
### Set Doxy_path: "PCD-NBIO.h"
#----------------------------------------------------------------------------

  ### @cond (BA||GN||SSP||VMR)
  ### @brief Specifies the bus number for the performance improvement "preferred bus". This control is dependent upon the state of the enabling switch: APCB_TOKEN_UID_PREFERRED_IO.
  ### @brief Note: For [F19M00] this control is superseded by the LclkDpmLevel control.
  ### @li 0x00 to 0xFF - Any valid PCI bus number, will enable the feature on the root complex that corresponds to that address.
  ### @li 0xFFFF - do not enable the feature
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPreferredIOBus|0xFFFF|UINT16|0x0004F0F0
  ### @endcond

  ### @cond (BA||GN||SSP||VMR)
  ### @brief A preferred I/O enhancement is added to optimize performance on the selected root bridge. The enhanced preferred I/O mode can only be enabled if
  ### PcdAmdPreferredIOBus is also enabled. The combination of these two controls establishes one of the 8 root bridges to be the one 'preferred' bus. This control is
  ### also dependent upon the state of the enabling switch: APCB_TOKEN_UID_PREFERRED_IO.
  ### @brief Note: For [F19M00] this control is superseded by the
  ### @li FALSE - disable the enhanced mode.
  ### @li TRUE - enable an enhanced mode which assures an LCLK value for best performance.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdEnhancedPreferredIOMode|FALSE|BOOLEAN|0x0004F0F2
  ### @endcond

  ### @cond (RS||BA||GN||SSP||VMR)
  ### @brief This item selects the level of Dynamic Power Management (DPM) for the NBIO root bridges. This control is a 32-bit bit-mapped value with 8 bit fields each
  ### representing one of the NBIO root bridges. The PreferredIOMode and Bus controls establish one preferred bus; this control can establish preferred status to many
  ### root bridges.
  ### @code
  ### // PcdAmdNbioLclkDpmLevel BitField:
  ###          //   - Socket0 (NBIO0[3:0], NBIO1[7:4], NBIO2[11:8] NBIO3[15:12])
  ###          //   - Socket1 (NBIO0[19:16], NBIO1[23:20], NBIO2[27:24] NBIO3[31:28])
  ### @endcode
  ### @brief Permitted Choices within each bit field: (Type: Value)(Default: Auto - 0x0F)
  ### @li Auto - 0x0F - No special handling, the DPM feature is left intact for the optimum balance between power saving and performance. Recommended for most situations.
  ### @cond (BA||GN||SSP||VMR)
  ### @li Max - 0x02 - set the LCLK to the high/low frequencies (593MHz). This disables the DPM feature and always runs the bridge at max performance. This may help
  ### workloads that tend to be quick bursts of data.
  ### @endcond
  ### @cond (RS||BRH)
  ### @li Max - 0x03 - set the LCLK to the high/low frequencies (highest freq). This disables the DPM feature and always runs the bridge at max performance. This may help
  ### workloads that tend to be quick bursts of data.
  ### @endcond
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioLclkDpmLevel|0xFFFFFFFF|UINT32|0x0004F0F1
  ### @endcond

  ### @cond (BA||GN||RS||MI3||BRH)
  ### @brief This control selects the operation of the SEV-SNP feature.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li FALSE - Leave the SEV-SNP disabled.
  ### @li TRUE - Program SEV-SNP configuration setting and enable the feature.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgSevSnpSupport|FALSE|BOOLEAN|0x0004F0F3
  ### @endcond

  ### @cond (BRH||SHP)
  ### @brief This control selects the operation of the SEV-TIO feature.
  ### @li FALSE - Disable SEV-TIO.
  ### @li TRUE - Enable SEV-TIO
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgSevTioSupport|FALSE|BOOLEAN|0x0004F0F4
  ### @endcond

  ### @cond (BRH||PHX||RPL||GNR||STP)
  ### @brief Enable/Disable PcieLoopBackMode
  ### @li TRUE - This option is active
  ### @li FALSE - This option is turned off
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPcieLoopbackMode|FALSE|BOOLEAN|0x0004F0F5
  ### @endcond

  ### @cond (RS||BRH)
  ### @brief This control safe recovery on BERExceedErr
  ### @li FALSE - Leave the safe recovery on BERExceedErr disabled.
  ### @li TRUE - Enable safe recovery on BERExceedErr.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdsafeRecoveryBER|FALSE|BOOLEAN|0x0004F0F6
  ### @endcond

  ### @cond (RS||BRH)
  ### @brief This monitors temp/time delta over a period, if there is a delta, it triggers re-calibration of impedance and DFE
  ### @li FALSE - Leave Periodic Callibration disabled.
  ### @li TRUE - Enable Periodic Calibration.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPeriodicCal|FALSE|BOOLEAN|0x0004F0F7
  ### @endcond

  ### @cond (BRH)
  ### @brief Limit hotplug devices to PCIe boot speed
  ### @li FALSE - Do not limit hotplug slots to Gen4 if system booted with only Gen4 devices, increases idle power
  ### @li TRUE - Limit hotplug slots to Gen4 if system booted with only Gen4 devices, which optimizes idle power
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdLimitHpDevicesToPcieBootSpeed|TRUE|BOOLEAN|0x0004F0F8
  ### @endcond

#----------------------------------------------------------------------------
#-  NBIO Dynamic PCDs
### Set Doxy_path: "PCD-NBIO-OV-CLK.h"
#----------------------------------------------------------------------------

  ### @name Overclocking Control
  ### @{

  ### @cond (PHX||RS||V10||V9||BA||CZN||GN||RMB||RN||RPL||SSP||VMR||BRH)
  ### @brief This value provides an option to disable overclocking capabilities.
  ### @brief Permitted Choices: (Type: Boolean)(Default: False)
  ### @li TRUE - Overclocking will not be permitted.
  ### @li FALSE - The system may use overclocking within the stated limits.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOcDisable|FALSE|BOOLEAN|0x0004F100
  ### @endcond

  ### @cond (PHX||RS||V10||V9||BA||CZN||GN||RMB||RN||RPL||SSP||VMR||BRH)
  ### @brief This value defines the voltage limit that can be requested for overclocking. The value is in millivolts. If a value of 0 is specified then the OPN-dependent
  ### default value will be used. Example: To select a maximum voltage of 1.5V, this item should be set to 1500.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x00)
  ### @li 0x00 - Default fused value for the part will be used.
  ### @li 0x01..N - Voltage limit in milli-volts.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOcVoltageMax|0x00|UINT16|0x0004F101
  ### @endcond

  ### @cond (PHX||RS||V10||V9||BA||CZN||GN||RMB||RN||RPL||SSP||VMR||BRH)
  ### @brief This value defines the frequency limit that can be requested for overclocking. The value is in megahertz (MHz). If a value of 0 is specified then the fused
  ### value will be used. Example:To select a maximum frequency of 3.6GHz, this PCD should be set to 3600.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x00)
  ### @li 0x00 - Default fused value for the part will be used.
  ### @li 0x01..N - Frequency limit in MHz.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOcFrequencyMax|0x00|UINT16|0x0004F102
  ### @endcond

  ### @cond (PHX||CZN||RMB||RN||RPL)
  ### @brief Specifies the forced GFXCLK frequency to the specified frequency [MHz].
  ### @li 0 - Does not override the GFXCLK frequency.
  ### @li Non-Zero - Forces the GFXCLK frequency to the specified clock.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdForceGfxclkFrequency|0x00|UINT16|0x0004F103
  ### @endcond

  ### @cond (PHX||AR||CZN||FF3||RMB||RN||RPL)
  ### @brief Specifies the GFXCLK frequency max override value [MHz].
  ### @li 0 - Does not override the GFXCLK frequency.
  ### @li Non-Zero - Forces the GFXCLK frequency to the specified clock.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdGfxclkFmaxOverride|0x00|UINT16|0x0004F104
  ### @endcond

  ### @cond (PHX||CZN||RMB||RN||RPL)
  ### @brief Specifies a forced voltage for the SOC [mV].
  ### @li 0 - Does not override the SOC voltage.
  ### @li Non-Zero - Forces the SOC voltage to the specified value in millivolts.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdForceVddcrSocVoltage|0x00|UINT16|0x0004F105
  ### @endcond

  ### @cond (PHX||CZN||RMB||RN||RPL)
  ### @brief Specifies a forced voltage for the CPU [mV].
  ### @li 0 - Does not override the CPU voltage.
  ### @li Non-Zero - Forces the CPU voltage to the specified value in millivolts.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdForceVddcrCpuVoltage|0x00|UINT16|0x0004F106
  ### @endcond

  ### @cond (PHX||RMB||RPL)
  ### @brief Disable Gfx Over Clocking
  ### @li TRUE - Disable.
  ### @li FALSE - Enable.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdGfxOcDisable|FALSE|BOOLEAN|0x0004F107
  ### @endcond
  ###@}

#----------------------------------------------------------------------------
#-  NBIO Dynamic PCDs
### Set Doxy_path: "PCD-NBIO.h"
#----------------------------------------------------------------------------

  ### @cond (RN||RS||BRH)
  ### @name Platform Limits
  ### @{
  ### @brief This value selects the maximum sustained power in Watts provided to the CPU. 'Max' is the maximum value supported by the fusing of the part. NOTE: For more
  ### information please refer to the IRM spec.
  ### @li 0x00 - SMU will use the fused TDP value.
  ### @li 0x01..N - specifies the TDP value in milli-Watts, up to the 'Max'.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPlatformTDP|0|UINT32|0x0004F110
  ### @brief This value defines the power limit for the
  ### @li 0 - Use fused values.
  ### @li XX - Specifies the override value.
  ### @li Fuse PPT Limit (Max PPT Limit): The fused limit of the part. Cannot be changed by the user.
  ### @li Platform PPT Limit (
  ### @li BIOS PPT Limit (
  ### @li HSMP/APML PPT Limit Override: User can use HSMP or APML to override the BIOS Limit. The HSMP/APML limit that is overriding the BIOS Limit must be less than the
  ### Plat and Fuse Limits.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPlatformPPT|0|UINT32|0x0004F111
  ### @brief This value is the same as PcdVrmCurrentLimit but this is the label used by the F17M30 code set.
  ### @li 0x00 - Use the fused default values.
  ### @li This a numeric value expressed in Amperes. For example, a current limit of 40 amperes is represented as: decimal 40.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPlatformTDC|0|UINT32|0x0004F112
  ### @brief This control name is 'overloaded' and the function has changed with new programs. This value is the same as PcdVrmMaximumCurrentLimit but this is the label
  ### used by the F17M30 code sets. This a numeric value expressed in Amperes. For example, a current limit of 40 amperes is represented as: decimal 40. Specifies the
  ### Maximum EDC limit that the platform can support per recommendation of the platform team based on electrical and thermal characterization of the platform. The valid
  ### value range is 0 - 300A.
  ### @li For [F17M30]
  ### @li For [F19M00]
  ### @li 0x00 - use default value
  ### @li 0xXX - Specifies the override value in Amperes. For example, a current limit of 300A amperes is represented as: 0x12c (decimal 300)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPlatformEDC|0|UINT32|0x0004F113
  ### @}
  ### @endcond

  ### @cond (CZN||RN||SSP||VMR)
  ### @brief This value specifies the 'Default socket TDP' which is the fused value for the part. This value is set by the
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDefaultsocketTDP|0x0|UINT32|0x0004F120
  ### @endcond

  ### @cond (CZN||RN||SSP||VMR)
  ### @brief This value specifies the 'Maximum ASIC hotspot operating temperature' which is the fused value for the part. This value is set by the
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDefaultTJMax|0x0|UINT32|0x0004F121
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdLN2ModeEnable|FALSE|BOOLEAN|0x0004F123
  ### @endcond

#----------------------------------------------------------------------------
#-  NBIO Dynamic PCDs
### Set Doxy_path: "PCD-NBIO-RAS.h"
#----------------------------------------------------------------------------

  ### @name RAS Feature Control
  ### @{

  ### @cond (RV||ZP||BA||GN||SSP||RS||MI3||BRH)
  ### @brief This value controls whether AGESA will enable NBIO poison data consumption. When enabled, poison consumption will enable SyncFlood and link disablement when
  ### an APML error occurs.
  ### @li TRUE - AGESA will enable poison consumption errors.
  ### @li FALSE - AGESA will not enable poison consumption errors.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioPoisonConsumption|FALSE|BOOLEAN|0x0004F201
  ### @endcond

  ### @cond (RV||ZP)
  ### @brief Describes if the controls for the RAS features for PCIe
  ### @li TRUE - NBIO RAS features are enabled.
  ### @li FALSE - This option is turned off
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioRASControl|FALSE|BOOLEAN|0x0004F202
  ### @endcond

  ### @brief When using Platform First Error Handling (PFEH) operation by the BIOS, this is the value used by the MCE sub-system to write to the software SMI command
  ### port to trigger a software SMI. The default is generally best, but it is allowed to be changed since this value could conflict with other handlers in the host system.
  ### @brief Permitted Choices: 0x0 - 0xFF (Type: Value)(Default: 0x80)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMceSwSmiData|0x80|UINT8|0x0004F203

  ### @cond (MI200||BA||GN||SSP||RS||MI3||BRH)
  ### @brief  NBIO corrected error thresholding counter control.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li TRUE - Enable the NBIO Corrected Error Thresholding counter.
  ### @li FALSE - The counter is turned off.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdNbioCorrectedErrThreshEn|FALSE|BOOLEAN|0x0004F206
  ### @endcond

  ### @cond (MI200||BA||GN||SSP||RS||MI3||BRH)
  ### @brief  The value for setting the NBIO corrected error thresholding counter.
  ### @brief Permitted Choices: 0x1-0xFFF (Type: Value)(Default: 0x0FF5)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdNbioCorrectedErrThreshCount|0x0FF5|UINT16|0x0004F207
  ### @endcond

  ### @cond (BA||GN||RV||SSP||ZP||RS||MI3)
  ### @brief  NBIO deferred error thresholding counter control.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li TRUE - Enable the NBIO Deferred Error Thresholding counter.
  ### @li FALSE - The counter is turned off.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdNbioDeferredErrThreshEn|FALSE|BOOLEAN|0x0004F208
  ### @endcond

  ### @cond (BA||GN||RV||SSP||ZP||RS||MI3)
  ### @brief  The value for setting the NBIO deferred error thresholding counter.
  ### @brief Permitted Choices: 0x1-0xFFF (Type: Value)(Default: 0x0FF5)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdNbioDeferredErrThreshCount|0x0FF5|UINT16|0x0004F209
  ### @endcond

  ### @cond (BIXBY||ZP||BA||GN||RMB||SSP||RS||MI3||BRH)
  ### @brief Specifies the state of the NBIO ECRC feature.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE - This option is active.
  ### @li FALSE - This option is turned off.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieEcrcEnablement|TRUE|BOOLEAN|0x0004F20A
  ### @endcond

  ### @cond (MI200||ZP||BA||FF3||GN||RMB||RPL||SSP||PHX||RS||MI3||BRH)
  ### @brief Specifies the severity of an ECRC error.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE - Errors are fatal.
  ### @li FALSE - Error are not fatal.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieEcrcSeverityFatal|TRUE|BOOLEAN|0x0004F20B
  ### @endcond

  ### @cond (MI200||BA||GN||SSP||RS||MI3||BRH)
  ### Sets the value for EGRESS POISON SEVERITY configuration
  ### Each bit is associated with a logical IOHC Egress port such as a PCIe RC.
  ### For each bit set to 1, the poison severity associated with that
  ### port is HIGH severity. For each bit set to 0 it is considered LOW severity.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEnableEgressPoisonSeverity|FALSE|BOOLEAN|0x0004F20F
  ### @endcond

  ### @cond (MI200||ZP||BA||GN||SSP||RS||MI3||BRH)
  ### @brief Theses two values set the value for EGRESS POISON SEVERITY configuration. Each bit is associated with a logical IOHC Egress port such as a PCIe
  ### @brief Permitted Choices: 0x0 - 0xFFFFFFFF (Type: Value)(Default: 0x4)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEgressPoisonSeverityLo|0x4|UINT32|0x0004F210
  ### @endcond

  ### @cond (MI200||ZP||BA||GN||SSP||RS||MI3||BRH)
  ### @brief Theses two values set the value for EGRESS POISON SEVERITY configuration. Each bit is associated with a logical IOHC Egress port such as a PCIe
  ### @brief Permitted Choices: 0x0 - 0xFFFFFFFF (Type: Value)(Default: 0x30011)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEgressPoisonSeverityHi|0x30011|UINT32|0x0004F211
  ### @endcond

  ### @cond (BA||GN||SSP||RS||BRH)
  ### @brief This value may be used to mask SyncFlood caused by NBIO RAS options. When set to TRUE SyncFlood from NBIO is masked off. When set to FALSE, NBIO is capable
  ### of generating SyncFlood.
  ### @li TRUE - Prevent RAS SyncFlood
  ### @li FALSE - Allow SyncFlood errors
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMaskNbioSyncFlood|TRUE|BOOLEAN|0x0004F212
  ### @endcond

  ### @cond (MI200||BA||GN||SSP||RS||MI3||BRH)
  ### @brief This value is used to enable SyncFlood reporting to APML. When set to TRUE SyncFlood will be reported to APML. When set to FALSE that reporting will be disabled.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li TRUE - Use APML reporting
  ### @li FALSE - Do not report to APML
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSyncFloodToApml|FALSE|BOOLEAN|0x0004F213
  ### @endcond

  ### @cond (MI200||BA||GN||SSP||RS||MI3||BRH)
  ### @brief Theses values set the enable mask for masking of errors logged in EGRESS_POISON_STATUS. For each bit set to 1, errors are masked off. For each bit set to 0,
  ### errors trigger response actions. See the PPR section on "Data Poisoning". Each value must be a 32-bit number.
  ### @brief  Permitted Choices: 0x0 - 0xFFFFFFFF (Type: UINT32 Value) (Default: 0xFFFCFFFF)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioEgressPoisonMaskHi|0xFFFCFFFF|UINT32|0x0004F214
  ### @endcond

  ### @cond (MI200||BA||GN||SSP||RS||MI3||BRH)
  ### @brief Theses values set the enable mask for masking of errors logged in EGRESS_POISON_STATUS. For each bit set to 1, errors are masked off. For each bit set to 0,
  ### errors trigger response actions. See the PPR section on "Data Poisoning". Each value must be a 32-bit number.
  ### @brief  Permitted Choices: 0x0 - 0xFFFFFFFF (Type: UINT32 Value) (Default: 0xFFFFFFFB)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioEgressPoisonMaskLo|0xFFFFFFFB|UINT32|0x0004F215
  ### @endcond

  ### @cond (BA||SSP||RS||MI3||BRH)
  ### @brief These set the enable mask for masking of uncorrectable parity errors on internal arrays. For each bit set to 1, a system fatal error event is triggered for
  ### UCP errors on arrays associated with that egress port. For each bit set to 0, errors are masked off. See the PPR section on "Data Poisoning". Each value must be a
  ### 32-bit number.Permitted Choices: (Type: UINT32 Value) (Default: Hi 0x00030000, Lo 0x00000004)
  ### @brief  Permitted Choices: 0x0 - 0xFFFFFFFF (Type: UINT32 Value) (Default: 0x00000004)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioRASUcpMaskLo|0x00000004|UINT32|0x0004F216
  ### @endcond

  ### @cond (BA||SSP||RS||MI3||BRH)
  ### @brief These set the enable mask for masking of uncorrectable parity errors on internal arrays. For each bit set to 1, a system fatal error event is triggered for
  ### UCP errors on arrays associated with that egress port. For each bit set to 0, errors are masked off. See the PPR section on "Data Poisoning". Each value must be a
  ### 32-bit number.Permitted Choices: (Type: UINT32 Value) (Default: Hi 0x00030000, Lo 0x00000004)
  ### @brief  Permitted Choices: 0x0 - 0xFFFFFFFF (Type: UINT32 Value) (Default: 0x00030000)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioRASUcpMaskHi|0x00030000|UINT32|0x0004F217
  ### @endcond

  ### @cond (MI200||BA||GN||SSP||RS||MI3||BRH)
  ### @brief This value specifies the timer interval of the SYSHUB Watchdog timer in ms.
  ### @brief  Permitted Choices: 0 - 5200 (Type: UINT32 Value) (Default: 2600)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSyshubWdtTimerInterval|2600|UINT32|0x0004F218
  ### @endcond

  ### @cond (MI3||BA||GN||SSP)
  ### @brief This value specifies whether SLINK read response errors are converted to an "Okay" response. See the PPR section on "Data Poisoning".
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li TRUE - read response errors are converted to "Okay" responses with data of all FFs.
  ### @li FALSE - read response errors are not converted
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSlinkConvertReadResponseErrorsToOkay|FALSE|BOOLEAN|0x0004F219
  ### @endcond

  ### @cond (MI3||BA||GN||SSP)
  ### @brief This value specifies whether SLINK write response errors are converted to an "Okay" response. See the PPR section on "Data Poisoning".
  ### @brief Permitted Choices: (Type: List)(Default: 0)
  ### @li 0, write response errors will be logged in the MCA.
  ### @li 1, write response errors will trigger an MCOMMIT error.
  ### @li 2, write response errors are converted to "Okay" responses.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSlinkWriteResponseErrorHandling|0|UINT8|0x0004F21A
  ### @endcond

  ### @cond (MI3||BA||GN||SSP)
  ### @brief This value specifies whether poison data propagated from SLINK will generate a deferred error. See the PPR section on "Data Poisoning".
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li TRUE - deferred errors are enabled.
  ### @li FALSE - errors are not generated.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdLogPoisonDataFromSLink|FALSE|BOOLEAN|0x0004F21B
  ### @endcond

  ### @cond (MI200||BA||GN||SSP||BA||FF3||GN||RMB||RPL||PHX||RS||MI3||BRH)
  ### @brief This value selects the method of reporting AER errors from PCI Express.
  ### @brief Permitted Choices: (Type: List)(Default: [F17M30]:0, [F19M00]:1)
  ### @li 0 - indicates that the hardware will report the error through PFEH-based Firmware First reporting.
  ### @li 1 - allows "OS First" handling of the errors through generation of a system control interrupt (SCI).
  ### @li 2 - provides for "Firmware First" handling of errors through generation of a system management interrupt (SMI).
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPcieAerReportMechanism|1|UINT8|0x0004F21D
  ### @endcond

  ### @cond (MI200||BA||GN||SSP||BA||FF3||GN||RMB||RPL||PHX||RS||MI3||BRH)
  ### @brief This value enables EDPC.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x0)
  ### @li 0 - EDPC is disabled.
  ### @li 1 - EDPC is enabled.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdEdpcEnable|0|UINT8|0x0004F21E
  ### @endcond

  ### @cond (MI200||SSP||BA||GN||RS||MI3||BRH)
  ### @brief This value is the master control for NBIO RAS support.
  ### @brief Permitted Choices: (Type: Value)(Default: [F17M30]:0x0, [F19M00]:0x01)
  ### @li 0 - Disabled
  ### @li 1 - MCA reporting
  ### @li 2 - Legacy Mode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioRASControlV2|0|UINT8|0x0004F220
  ### @endcond

  ### @cond (MI200||BA||FF3||GN||RMB||RPL||SSP||PHX||RS||MI3||BRH)
  ### @brief This control selects whether to generate Sync Flood on PCIE Fatal Error
  ### @li FALSE - No sync flood is generated.
  ### @li TRUE - Sync flood is generated on fatal errors.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPcieSyncFloodOnFatal|FALSE|BOOLEAN|0x0004F22B
  ### @endcond

  ### @cond (SSP || GN || RS || MI3 || BRH)
  ### @details This PCD is used for OEM to select a different ACS value from the 3 possible values below.
  ### @li 00b - Direct Request Access Enabled
  ### @li 01b - Request Blocking Enabled
  ### @li 10b - Request Redirect Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdRASAcsValue|1|UINT16|0x000500B8
  ### @endcond

  ### @cond (BIXBY||RV||ZP||BA||GN||RN||SSP||RS||MI3||BRH)
  ### @details Rx Margin persistence mode
  ### @li 1 - Enable
  ### @li 0 - Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgRxMarginPersistenceMode|1|UINT8|0x0004F22D
  ### @endcond

  ### @brief This value specifies a delay, in mS, to be inserted between PCI reset de-assertion and start of training. Some devices, particularly NVME drives, are
  ### problematic with longer delays. Other devices are problematic with short delays, such as a custom NIC found to require 500ms. This delay affects the training
  ### of all PCIe controllers, pausing the main flow until the timer completes. Generally the PCIe resets are connected to the PERST pin for one of the NBIOs
  ### in the chip, but the resets are controllable as GPIOs as well.
  ### Note this is a 32-bit value so delays can be quite large, as needed. When the value gets larger an internal loop timing variance will play a larger
  ### roll; so, the user should verify the actual time. The variance is positive (i.e. ranges from 1ms to ~1.25ms).
  ### @brief Permitted Choices: (Type: Value, milliseconds)(Default: 0x0)
  ### @li 0 - No delay inserted after the reset.
  ### @li XX - insert a delay of ~xx milliseconds.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAfterResetDelay|0|UINT32|0x0004F221

  ### @cond (PHX||V10||AR||CZN||FF3||RMB||RN||RN||RPL)
  ### S0i3 Reset Support
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdS0i3SetGenericPeRstEnable|FALSE|BOOLEAN|0x0004F222
  ### @endcond

  ### @cond (PHX||V10||AR||CZN||FF3||RMB||RN||RN||RPL)
  ### S0i3 Reset for GPIO0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdS0i3SetGenericPeRstGpio0|0|UINT32|0x0004F223
  ### @endcond

  ### @cond (PHX||V10||AR||CZN||FF3||RMB||RN||RN||RPL)
  ### S0i3 Reset for GPIO1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdS0i3SetGenericPeRstGpio1|0|UINT32|0x0004F224
  ### @endcond

  ### @cond (BIXBY||MI200||BA||GN||RMB||SSP||PHX||RS||MI3||BRH)
  ### @brief Control to support PCIe
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li TRUE - AGESA will enable 10-bit tag for devices that indicate support.
  ### @li FALSE - 10-bit tag support is disabled.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPcieTbtSupport|TRUE|BOOLEAN|0x0004F225
  ### @endcond

  ### @cond (RV||ZP||BA||GN||RN||SSP||RS||MI3)
  ### @brief Initial PCIe ESM Target Speed for all cards in the system
  ### @brief Permitted Choices: 16 - 25 (Type: Value)(Default: 16)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEsmTargetSpeed|16|UINT8|0x0004F226
  ### @endcond

  ### @cond (RV||ZP||BA||GN||RN||SSP||RS||MI3)
  ### @brief If set PCIe ESM sequence is attempted on all root ports with supported devices
  ### @li TRUE - Enable PCIe ESM sequence on all root ports with supported devices
  ### @li FALSE - Disable PCIe ESM sequence on all root ports with supported devices
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEsmEnableAllRootPorts|FALSE|BOOLEAN|0x0004F227
  ### @endcond

  ### @cond (FF3||RMB||RN||RPL||PHX)
  ### @brief Utilized to specifiy which controller PCIe lanes are mapped to.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdApuLaneRouteConfig|0x0|UINT8|0x0004F22C
  ### @endcond

  ### @cond (PHX||RMB||RN||RPL)
  ### @brief (in milliseconds)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSetS0i3PmeTurnOffDelay|0|UINT32|0x0004F230
  ### @endcond

  ### @cond (BA||GN||SSP)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPeer2PeerOrdering|FALSE|BOOLEAN|0x0004F231
  ### @endcond

  ### @cond (GN)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdxGMI18GACOFC|TRUE|BOOLEAN|0x0004F232
  ### @endcond

  ### @cond (BRH)
  ### @brief BERT Core Trace Dump Enable/Disable
  ### @li TRUE - Enable Core Trace Dump
  ### @li FALSE - Disable Core Trace dump
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCoreTraceDumpEnable|FALSE|BOOLEAN|0x0004F238
  ### @endcond

  ### @cond (MI3)
  ### HBM Corrected Error Counter Enable
  ### @li 0 - Disable
  ### @li 1 - NoLeakMode
  ### @li 2 - LeakMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHbmEccErrorCounterEnable|0x00|UINT8|0x0004F401

  ### @brief Enables/Disables the SMI interrupt generated when the DRAM Corrected Error Counter count exceeds the threshold value.
  ### @li TRUE - Enables SMIs when DRAM Corrected Error Counter count exceeds the threshold value
  ### @li FALSE -  Disables SMIs when DRAM Corrected Error Counter count exceeds the threshold value
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHbmEccErrorCounterIntEnable|FALSE|BOOLEAN|0x0004F402

  ### @brief Error Counter Leak Rate (0x00..0x1F)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHbmEccErrorCounterLeakRate|0x00|UINT8|0x0004F403

  ### @brief Error Counter Leak Threshold (0x0000..0xFFFF).
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHbmEccErrorCounterLeakThreshold|0x00|UINT16|0x0004F404
  ### @endcond
  ###@} end of RAS Feature Control

#----------------------------------------------------------------------------
#-  RAS Dynamic PCDs
### Set Doxy_path: "PCD-RAS.h"
#----------------------------------------------------------------------------

  ### @name RAS Controls
  ### @{

  ### @brief  This value controls the MCA error thresholding counter to all MCA banks.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li TRUE - Enable the MCA Error Thresholding counter.
  ### @li FALSE - The counter is turned off, MCE interrupts will be raised on every occurrence.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMcaErrThreshEn|TRUE|BOOLEAN|0x0004F204

  ### @brief  The value for setting the MCA Error Thresholding counter.
  ### @brief Permitted Choices: 0x1-0xFFF (Type: Value)(Default: 0x0FF5)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMcaErrThreshCount|0x0FF5|UINT16|0x0004F205

  ### @cond (MI200||BA||GN||SSP||RS||BRH)
  ### @brief This item enables PSP error injection support.
  ### @brief Permitted Choices: (Type: Boolean) (Default: FALSE)
  ### @li FALSE - the feature is disabled.
  ### @li TRUE - the SMEE feature is enabled.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspEinjSupport|FALSE|BOOLEAN|0x0004F20C
  ### @endcond

  ### @cond (MI3||RS||BRH)
  ### Specifies the state of the MCA FruText feature.
  ### @li TRUE - Enables MCA FruText functionality.
  ### @li FALSE - Disable Enables MCA FruText functionality.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMcaFruTextEnable|TRUE|BOOLEAN|0x0004F20D

    ### @brief  The value for setting the CMC Notification Type.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x05)
  ### @li 0 - POLLED type H/W error notification.
  ### @li 5 - CMCI type H/W error notification.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCmcNotificationType|0x05|UINT8|0x0004F20E

  ### The value to control the PMIC Error reporting mechanism.
  ### @li TRUE - This option is active.
  ### @li FALSE - This option is turned off. (Default)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPmicErrorReporting|FALSE|BOOLEAN|0x0004F237
  ### @endcond

  ### @cond (RS||BRH)
  ### DRAM Corrected Error Counter Enable
  ### @li 0 - Disable
  ### @li 1 - NoLeakMode
  ### @li 2 - LeakMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDdrEccErrorCounterEnable|0x00|UINT8|0x0004F233

  ### @brief Enables/Disables the SMI interrupt generated when the DRAM Corrected Error Counter count exceeds the threshold value.
  ### @li TRUE - Enables SMIs when DRAM Corrected Error Counter count exceeds the threshold value
  ### @li FALSE -  Disables SMIs when DRAM Corrected Error Counter count exceeds the threshold value
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDdrEccErrorCounterIntEnable|FALSE|BOOLEAN|0x0004F234

  ### @brief Error Counter Leak Rate (0x00..0x1F)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDdrEccErrorCounterLeakRate|0x00|UINT8|0x0004F235

  ### @brief Error Counter Start Count (0x0000..0xFFFF).
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDdrEccErrorCounterStartCount|0x00|UINT16|0x0004F236
  ### @endcond
  ###@} end of RAS Controls

#----------------------------------------------------------------------------
#-  NBIO Dynamic PCDs
### Set Doxy_path: "PCD-NBIO.h"
#----------------------------------------------------------------------------

  ### @name GNB SMU12
  ### These are controls passed to the SMU for Power Management purposes.
  ### @{

  ### @cond (PHX||AR||CZN||FF3||RMB||RN||RPL)
  ### @brief This control enables the STT feature in the Model 60h. All STT controls for Model 60h depend on this feature enable.
  ### @li TRUE - This option is active.
  ### @li FALSE - This option is turned off.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSttEnable|0|UINT8|0x0004F324
  ### @endcond

  ### @cond (PHX||AR||CZN||FF3||RMB||RN||RPL)
  ### @brief This value specifies the number of motherboard sensors that are being used to report skin temperature calculation. There are two modes when STT is enabled and
  ### @li 2 - UMA mode: The internal (iGPU) is being used so only 2 motherboard sensors are active; the remote and the APU (per the ThDG).
  ### @li 3 - APU+HS2 mode: A second heat source (HS2) is included in the STT calculations. The HS2 source is dependent upon the processor version and may be the dGPU,
  ### an SSD, 5G module or Prom Chip. All 3 motherboard sensors described by the ThDG are present and active.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSttPcbSensorCount|2|UINT8|0x0004F325
  ### @endcond

  ### @cond (PHX||AR||CZN||FF3||RMB||RN||RPL)
  ### @brief The System Temperature Tracking minimum value specifies the minimum sustained power that the APU power is allowed to reduce to in order to maintain hotspot
  ### temperatures within skin temperature limits. This is applicable only on platforms with system temperature tracking feature enabled.
  ### @brief STT_MinLimit is paired with PcdFastPptLimit (set via the
  ### @li 0 - Inactive, minimum power values are obtained from the Fuses.
  ### @li This a numeric value and must be in milliwatts.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSttMinPowerLimit|0|UINT16|0x0004F327
  ### @endcond

  ### @cond (PHX||AR||CZN||FF3||RMB||RN||RPL)
  ### @brief These values specify the characterized coefficients for the motherboard temperature sensors. There are two calculated values, three sensors and two
  ### coefficients for each sensor.
  ### @brief Characterized coefficients are derived from testing. Customers will have to run tests on their systems to determine these coefficients for their specific
  ### systems. The guideline to determine these coefficients is provided in the Thermal Design Guide (ThDG) for the Processor. Please see the appropriate TDG for the
  ### applicable infrastructure (e.g. FP6, AM4, FF3, etc.)
  ### @brief The Sensor coefficients are an encoded Q6.10 fractional number.
  ### @li Bit 15 - sign bit
  ### @li Bits 14:10 - integer portion
  ### @li bits 9:0 - fractional portion (is = value/1024)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSttM1|0x0469|UINT16|0x0004F329
  ### @endcond

  ### @cond (PHX||AR||CZN||FF3||RMB||RN||RPL)
  ### @brief STT M2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSttM2|0x00EE|UINT16|0x0004F32B
  ### @endcond

  ### @cond (PHX||AR||CZN||FF3||RMB||RN||RPL)
  ### @brief STT M3
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSttM3|0|UINT16|0x0004F32D
  ### @endcond

  ### @cond (PHX||AR||CZN||FF3||RMB||RN||RPL)
  ### @brief STT M4
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSttM4|0|UINT16|0x0004F32F
  ### @endcond

  ### @cond (PHX||AR||CZN||FF3||RMB||RN||RPL)
  ### @brief STT M5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSttM5|0|UINT16|0x0004F331
  ### @endcond

  ### @cond (PHX||AR||CZN||FF3||RMB||RN||RPL)
  ### @brief STT M6
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSttM6|0|UINT16|0x0004F333
  ### @endcond

  ### @cond (PHX||AR||CZN||FF3||RMB||RN||RPL)
  ### @brief These values specify the 'characterized intercept' (see ThDG for explanation) to calculate APU or HS2 dependent skin temperature value.
  ### @brief These values are encoded as Signed Q6.10 fractional number representing the associated Intercept value.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSttCApu|0xE9A0|UINT16|0x0004F335
  ### @endcond

  ### @cond (PHX||AR||FF3||RMB||RN||RPL)
  ### @brief STT C GPU
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSttCGpu|0|UINT16|0x0004F337
  ### @endcond

  ### @cond (CZN)
  ### @brief STT C HS2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSttCHS2|0|UINT16|0x0004F338
  ### @endcond

  ### @cond (PHX||AR||CZN||FF3||RMB||RN||RPL)
  ### @brief These values specify the 'characterized alpha-filter' value, between 0 and 1 for APU or HS2 dependent skin temperature
  ### @brief These values are encoded as Unsigned Q0.16 fractional number representing the associated alpha-filter value.
  ### @li Bits 15:0 - fractional portion (is = value/65536)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSttAlphaApu|0xFFFF|UINT16|0x0004F339
  ### @endcond

  ### @cond (PHX||AR||FF3||RMB||RN||RPL)
  ### @brief STT Alpha Gpu
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSttAlphaGpu|0|UINT16|0x0004F33B
  ### @endcond

  ### @cond (CZN)
  ### @brief STT Alpha HS2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSttAlphaHS2|0|UINT16|0x0004F33C
  ### @endcond

  ### @cond (PHX||AR||CZN||FF3||RMB||RN||RPL)
  ### @brief These values specify the 'skin temperature limit' for APU or HS2 dependent skin temperature.
  ### @brief These values are an unsigned integer representing the associated skin temperature limit. Refer to the Thermal Design Guide for more details.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSttSkinTemperatureLimitApu|0x3C00|UINT16|0x0004F33D
  ### @endcond

  ### @cond (PHX||AR||FF3||RMB||RN||RPL)
  ### @brief STT Skin Temperature Limit Gpu
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSttSkinTemperatureLimitGpu|0|UINT16|0x0004F33F
  ### @endcond

  ### @cond (CZN)
  ### @brief STT Skin Temperature Limit HS2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSttSkinTemperatureLimitHS2|0|UINT16|0x0004F340
  ### @endcond

  ### @cond (PHX||AR||CZN||FF3||RMB||RN||RPL)
  ### @brief STT Error Coeff
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSttErrorCoeff|0x0021|UINT16|0x0004F341
  ### @endcond

  ### @cond (PHX||AR||CZN||FF3||RMB||RN||RPL)
  ### @brief Cppc Constraints Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCppcConstraintsEnabled|0|UINT8|0x0004F342
  ### @endcond

  ### @cond (PHX||AR||CZN||FF3||RMB||RN||RPL)
  ### @brief STT Error Rate Coeff
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSttErrorRateCoeff|0x2666|UINT16|0x0004F343
  ### @endcond

  ### @cond (PHX||AR||CZN||FF3||RMB||RN||RPL)
  ### @brief Cppc Perf Limit Max Range
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCppcPerfLimitMaxRange|0|UINT8|0x0004F345
  ### @endcond

  ### @cond (PHX||AR||CZN||FF3||RMB||RN||RPL)
  ### @brief Cppc Perf Limit Min Range
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCppcPerfLimitMinRange|0|UINT8|0x0004F346
  ### @endcond

  ### @cond (PHX||AR||CZN||FF3||RMB||RN||RPL)
  ### @brief Cppc Epp Max Range
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCppcEppMaxRange|0|UINT8|0x0004F347
  ### @endcond

  ### @cond (PHX||AR||CZN||FF3||RMB||RN||RPL)
  ### @brief Cppc Epp Min Range
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCppcEppMinRange|0|UINT8|0x0004F348
  ### @endcond
  ###@} end of GNB SMU12

  ### @cond (PHX||AR||CZN||FF3||RMB||RN||RPL)
  ### @name Appended: STAPM
  ### @brief STAPM Boost
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdStapmBoost|0x00|UINT8|0x0004F349
  ### @endcond

  ### @cond (CZN||RN)
  ### @name Appended: PWM
  ### @{
  ### Fan PWM control
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdForcePwmCtl|0x00|UINT8|0x0004F34A

  ### Fan PWM percentage value [% 0-100]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdForcePwm|0x00|UINT8|0x0004F34B
  ###@}
  ### @endcond

  ### @cond (PHX||CZN||RMB||RN||RPL)
  ### @name A plus A
  ### @{
  ### @brief Thermal control variables (Sustained Power Limit)
  ### @brief Thermal control variables (Fast PPT Limit)
  ### @brief Thermal control variables (Slow PPT Limit)
  ### @brief Thermal control variables (Stapm Time Constant)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMsgSetSustainedPowerLimit|0|UINT32|0x0004F34C
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMsgSetFastPPTLimit|0|UINT32|0x0004F34D
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMsgSetSlowPPTLimit|0|UINT32|0x0004F34E
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMsgStapmTimeConstant|0|UINT32|0x0004F34F
  ###@}
  ### @endcond

  ### @cond (FF3||RN)
  ### @name GFX v1_12
  ### address of ATOM_DCN_DPPHY_DVIHDMI_TUNINGSET
  ### @{
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdTmdsTuningSetting|0x00|UINT32|0x0004F350
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHdmiCLK5TuningSetting|0x00|UINT32|0x0004F351
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHdmiCLK8TuningSetting|0x00|UINT32|0x0004F352
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHdmiCLK6TuningSetting|0x00|UINT32|0x0004F353
  ###@}
  ### @endcond

  ### @cond (FF3 || RN)
  ### @name address of ATOM_DCN_DPPHY_DP_TUNINGSET
  ### @{
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDcnDpHbrTuningSetting|0x00|UINT32|0x0004F354
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDcnDpHbr2TuningSetting|0x00|UINT32|0x0004F355
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDcnDpHbr3TuningSetting|0x00|UINT32|0x0004F356
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDcnEdpTuningSetting|0x00|UINT32|0x0004F357
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDcnDpPhyDpTuningSetting|0x00|UINT32|0x0004F358
  ###@}
  ### @endcond

  ### @cond (CZN||RN)
  ### @brief MPSVrm is used on this platform
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMPSVrm|0|UINT8|0x0004F35B
  ### @endcond

  ### @cond (RN)
  ### @name address of ATOM_DCN_DPPHY_TUNINGSET
  ### @details This is the address for GPU PHY tuning
  ###@{

  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdTxLpAttTuningSetting|0x00|UINT32|0x0004F35D
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdTxTermCtrlTuningSetting|0x00|UINT32|0x0004F35E
  ###@}
  ### @endcond

  ### @cond (CZN)
  ### @brief This PCD sets the vendor/device id of support PCIe device list which request SMU to save/restore its PMCSR(Power Management Control/Status Register)
  ### during S0i3 resume.
  ### @li 0x0000_0000 - No device specified.
  ### @li XX - Specifies the vendor and device ID of the device for which the SMU will save/restore the PMCSR.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdS0i3UnSafeShutdownApprovedListVidDid|0x00|UINT32|0x0004F35F
  ### @endcond

  ### @cond (RMB||RPL||PHX)
  ### @name Address of ATOM_DISPLAY_PHY_TUNING_INFO which provide PHY tuning information.
  ### @{
  ### @brief address of ATOM_DISPLAY_PHY_TUNING_INFO - COMMON_TABLE_HEADER
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDisplayPhyTuningSettingTableHeader|0x00|UINT32|0x0004F360
  ### @brief address of ATOM_DISPLAY_PHY_TUNING_INFO - Content
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDisplayPhyTuningSettingTableContent|0x00|UINT32|0x0004F361
  ### @}
  ### @endcond

  ### @cond (RS||MI3||BRH)
  ### @brief Configure CXL Protocol Error reporting mechanism
  ### @li 0 - Disabled
  ### @li 1 - Same as PcdAmdPcieAerReportMechanism
  ### @li 2 - PcdAmdPcieAerReportMechanism must be treated as set to FW-First
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlProtocolErrorReporting|0|UINT8|0x0004F362
  ### @endcond

  ### @cond (RS||MI3||BRH)
  ### @details Configure CXL Component Error reporting mechanism.
  ### @li 0 - OS First
  ### @li 1 - Firmware First
  ### @li 2 - Debug Firmware First
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlComponentErrorReporting|0|UINT8|0x0004F363
  ### @endcond

  ### @cond (BRH)
  ### @brief Configure CXL Informational EventLog Interrupt.
  ### @li FALSE - Configures the "Informational EventLog Interrupt" to: 00b = No Interrupt.
  ### @li TRUE - Configures the "Informational EventLog Interrupt" to: 10b = FW Interrupt (EFN VDM).
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCXlInformationalEventLogInterrupt|FALSE|BOOLEAN|0x0004F368
  ### @endcond

  ### @cond (BRH)
  ### @brief Configure CXL WarningEventLog Interrupt.
  ### @li TRUE - Configures the "Warning EventLog Interrupt" to: 10b = FW Interrupt (EFN VDM).
  ### @li FALSE - Configures the "Warning EventLog Interrupt" to: 00b = No Interrupt.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCXlWarningEventLogInterrupt|FALSE|BOOLEAN|0x0004F369
  ### @endcond

  ### @cond (BRH)
  ### @brief Enable/Disable Early CXL Link Training
  ### @li TRUE -  Early CXL Link Training is Enabled
  ### @li FALSE - Early CXL Link Training is Disabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCXlEarlyLinkTraining|0|UINT8|0x0004F36A
  ### @endcond

  ### @cond (BRH)
  ### @brief Enable CXL System physical Address
  ### @li 0 - Disabled
  ### @li 1 - Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlSpaEnable|0|UINT8|0x0004F36B
  ### @endcond

  ### @cond (PHX)
  ### @brief STAPM Feature Enable
  ### @li Enable - STAPM Feature Enable.
  ### @li Disable - STAPM Feature Disable.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEnableStapm|TRUE|BOOLEAN|0x0004F36C
  ### @endcond

  ### @cond (RS||BRH)
  ### @details Enable CDMA feature.
  ### @li 0 - Disable CDMA feature
  ### @li 1 - Enable CDMA feature
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricCdma|TRUE|BOOLEAN|0x0004F364
  ### @endcond

  ### @cond (BRH)
  ### @brief Enable CXL.mem Isolation
  ### @li FALSE - Disabled
  ### @li TRUE - Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlMemIsolationEnable|FALSE|BOOLEAN|0x0004F365
  ### @endcond

  ### @cond (BRH)
  ### @brief Enable CXL.mem Isolation Notification
  ### @li FALSE - Disabled
  ### @li TRUE - Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlMemIsolationFwNotification|FALSE|BOOLEAN|0x0004F367
  ### @endcond

  ### @cond (PHX||STX||STXH)
  ### @brief Indicates maximum index selectable for carveout options.
  ### @brief 0 means feature is disabled.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdUmaCarveoutIndexMax|1|UINT32|0x0004F366
  ### @endcond

  ### @name NBIO SMU13
  ###@{

  ### @cond (AR||FF3||RMB)
  ### @brief Enable fan control
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPublicFanControlEnable|FALSE|BOOLEAN|0x000A6100
  ### @endcond

  ### @cond (PHX||AR||FF3||RMB||RPL)
  ### @brief CoreCountControl feature
  ### @li 0 - Disable
  ### @li 1 - Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCoreCountControlEnable|0x0|UINT8|0x000A6101
  ### @endcond

  ### @cond (PHX||AR||FF3||RMB||RPL)
  ### @brief GPE Event ID [0-31] used by MP1 to trigger software SCI (needs to be edge configured)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSwSciGpeID|0xFF|UINT8|0x000A6102
  ### @endcond

  ### @cond (RMB)
  ### @brief Enable Z-State control
  ### @li FALSE - Disable
  ### @li TRUE - Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuZStateControlEnable|FALSE|BOOLEAN|0x000A6103
  ### @endcond

  ### @cond (PHX2)
  ### @brief Enable Z-States Mask control
  ### @li FALSE - Disable
  ### @li TRUE - Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdZstatesMaskEnable|TRUE|BOOLEAN|0x000A6105
  ### @endcond

  ### @cond (PHX2)
  ### @brief Enable Z-States Mask0 Timer control
  ### @li FALSE - Disable
  ### @li TRUE - Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdZstatesWake0Timer|TRUE|BOOLEAN|0x000A6106
  ### @endcond

  ### @cond (PHX2)
  ### @brief Enable Z-States Mask0 S2NS Timer control
  ### @li FALSE - Disable
  ### @li TRUE - Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdZstatesWake0S2nsTimer|TRUE|BOOLEAN|0x000A6107
  ### @endcond

  ### @cond (PHX2)
  ### @brief Enable Z-States Mask0 FCH control
  ### @li FALSE - Disable
  ### @li TRUE - Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdZstatesWake0Fch|TRUE|BOOLEAN|0x000A6108
  ### @endcond

  ### @cond (PHX2)
  ### @brief Enable Z-States Mask0 Acp
  ### @li FALSE - Disable
  ### @li TRUE - Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdZstatesWake0Acp|TRUE|BOOLEAN|0x000A6109
  ### @endcond

  ### @cond (PHX2)
  ### @brief Enable Z-States Mask0 Mp2
  ### @li FALSE - Disable
  ### @li TRUE - Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdZstatesWake0Mp2|TRUE|BOOLEAN|0x000A610A
  ### @endcond

  ### @cond (PHX2)
  ### @brief Enable Z-States Mask0 Dbreq
  ### @li FALSE - Disable
  ### @li TRUE - Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdZstatesWake0Dbreq|TRUE|BOOLEAN|0x000A610B
  ### @endcond

  ### @cond (PHX2)
  ### @brief Enable Z-States Mask0 DCN S
  ### @li FALSE - Disable
  ### @li TRUE - Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdZstatesWake0DcnS|TRUE|BOOLEAN|0x000A610C
  ### @endcond

  ### @cond (PHX2)
  ### @brief Enable Z-States Mask0 DCN NS
  ### @li FALSE - Disable
  ### @li TRUE - Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdZstatesWake0DcnNs|TRUE|BOOLEAN|0x000A610D
  ### @endcond

  ### @cond (PHX2)
  ### @brief Enable Z-States Mask0 DF
  ### @li FALSE - Disable
  ### @li TRUE - Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdZstatesWake0Df|TRUE|BOOLEAN|0x000A610E
  ### @endcond

  ### @cond (BA||GN)
  ### @brief For certain (rarely encountered) workloads, the EDC algorithm needs to handle L3 credits differently from the default behavior. This switch is provided
  ### for customer's use in that situation. Note that this field should only be changed from the default in special circumstances if advised by AMD. Please see reference
  ### @li 0 - mode 0
  ### @li 1 - mode 1
  ### @li 0xF - Auto
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdEdcModeSelect|0xF|UINT8|0x000A6200
  ### @endcond
  ### @cond (BA||GN)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCacWeightAdjust|0xF|UINT8|0x000A6201
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPerformanceTracing|FALSE|BOOLEAN|0x000A6202
  ### @endcond
  ###@} end of NBIO SMU13


  ### @cond (RMB)
  ### @brief Smu Gpio Config Table for s0i3 and Z9
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuGpioConfigTable|0x0|UINT32|0x000A6203
  ### @endcond

  ### @cond (RMB||RPL||PHX)
  ### @name GFX v2_2
  ### @{
  ### BitMap for Display Caps
  ### HBR2_DISABLE               =0x0001,    (BIT0)
  ### DP_FIXED_VS_EN             =0x0002,    (BIT1)
  ### HBR3_DISABLE               =0x0080,    (BIT7)
  ### USB_C_TYPE                 =0x100,     (BIT8)     // the DP connector is a USB-C type.
  ### HDMI20_DISABLE             =0x200,     (BIT9)     // HDMI2.0 6GBPS disable
  ###
  ### DP2                        =0x100000,  (BIT20)    // HDMI2.0 6GBPS disable
  ### UHBR10_EN                  =0x200000,  (BIT21)    // DP2.0 UHBR10 settings is supported by board
  ### UHBR13_5_EN                =0x400000,  (BIT22)    // DP2.0 UHBR13.5 settings is supported by board
  ### RECORD_UHBR20_EN           =0x800000,  (BIT23)    // DP2.0 UHBR20 settings is supported by board
  ### HDMI_FRL                   =0x2000000, (BIT25)    // HDMI FRL supported by ASIC/board.
  ### HDMI_FRL_8GbEn             =0x4000000, (BIT26)    // HDMI FRL 8Gb support
  ### HDMI_FRL_10GbEn            =0x8000000, (BIT27)    // HDMI FRL 10Gb support
  ### HDMI_FRL_12GbEn            =0x10000000,(BIT28)    // HDMI FRL 12Gb support
  ### USB4_C_TYPE                =0x20000000,(BIT23)    // the DP connector is a USB4-C type
  ###
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDisplayCapDdi0|0x0|UINT32|0x000A6300
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDisplayCapDdi1|0x0|UINT32|0x000A6301
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDisplayCapDdi2|0x0|UINT32|0x000A6302
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDisplayCapDdi3|0x0|UINT32|0x000A6303
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDisplayCapDdi4|0x0|UINT32|0x000A6304
  ###@}
  ### @endcond

  ### @cond (PHX)
  ### @brief En/Disable Tcon Instant-on-Logo
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSysInfoTconInstantOnLogoSupport|FALSE|BOOLEAN|0x000A6305
  ### @endcond

  ### @cond (RMB)
  ### @brief Vddcr Vdd Slew Rate
  ### @li 0   - 2.5 mV/us
  ### @li 1   - 10 mV/us
  ### @li 2   - 20 mV/us
  ### @li 3   - 40 mV/us
  ### @li 0xF - Not touch
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuVddcrVddSlewRateIndex|0xF|UINT8|0x000A6400
  ### @endcond

  ### @cond (RMB)
  ### @brief Vddcr Vdd Slew Rate Down Cntl
  ### @li 0   - Negative equal to Positive Slew Rate
  ### @li 1   - Negative equal to 1/4 Positive Slew Rat
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuVddcrVddSlewRateDownCntl|0x0|UINT8|0x000A6401
  ### @endcond

  ### @cond (RMB)
  ### @brief Slew Rate Override Control For VDDCR_SOC and VDDCR_SR Rails
  ### @li 0   - Not touch
  ### @li 1   - Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuVddcrSocSlewRateOverride|0x0|UINT8|0x000A6402
  ### @endcond

  ### @cond (RMB)
  ### @brief Slew Rate Index For VDDCR_SOC and VDDCR_SR Rails
  ### @li 0   - 2.5 mV/us
  ### @li 1   - 10 mV/us
  ### @li 2   - 20 mV/us
  ### @li 3   - 40 mV/us
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuVddcrSocSlewRateIndex|0x0|UINT8|0x000A6403
  ### @endcond

  ### @cond (RMB)
  ### @brief Slew Rate Down Cntl For VDDCR_SOC and VDDCR_SR Rails
  ### @li 0   - Negative equal to Positive Slew Rate
  ### @li 1   - Negative equal to 1/4 Positive Slew Rat
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuVddcrSocSlewRateDownCntl|0x0|UINT8|0x000A6404
  ### @endcond

  ### @cond (RMB)
  ### @brief Smu Override bit to PSI Decay Condition
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuPsiDecayConditionOverride|0x0|UINT8|0x000A6500
  ### @endcond

  ### @cond (RMB)
  ### @brief Smu PSI Decay Condition Vdd Rail
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuPsiDecayConditionVddRail|0x0|UINT8|0x000A6501
  ### @endcond

  ### @cond (RMB)
  ### @brief Smu PSI Decay Condition Soc Rail
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuPsiDecayConditionSocRail|0x0|UINT8|0x000A6502
  ### @endcond

  ### @cond (RMB)
  ### @brief Smu PSI Decay Condition Sr Rail
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuPsiDecayConditionSrRail|0x0|UINT8|0x000A6503
  ### @endcond

  ### @cond (RPL)
  ### @brief Vddcr VddLoadline Slope Override Enable
  ### @li 0 - Disable
  ### @li 1 - Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdVddcrVddLoadlineSlopeOverrideEn|0|UINT8|0x000A6504
  ### @endcond

  ### @cond (RPL)
  ### @brief Vddcr VddLoadline Slope
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdVddcrVddLoadlineSlope|0|UINT16|0x000A6505
  ### @endcond

  ### @cond (RPL)
  ### @brief Vddcr VddVoltage Offset
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdVddcrVddVoltageOffset|0|UINT16|0x000A6506
  ### @endcond

  ### @cond (RPL)
  ### @brief Vddcr VddVoltage Override
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdVddcrVddVoltageOverride|0|UINT16|0x000A6507
  ### @endcond

  ### @cond (RS||GN||BA ||MI3||BRH)
  ### @brief This control PCIe SRIS feature support
  ### @brief Permitted Choices: (Type: UINT8)(Default: 0xFF)
  ### @li 0x0: Disable
  ### @li 0x1: Enable
  ### @li 0xFF: Auto (Defaults to SRIS Enable)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSrisEnableMode|0xFF|UINT8|0x000A6508                      #///< 0xFF:Auto 0:Disable 1:Enable
  ### @endcond

  ### @cond (RS||GN||BA||MI3||BRH)
  ### @brief Controls SKP generation interval. Provides an override value for STRAP_SKIP_INTERVAL. This control depends on PcdSrisEnableMode enabled.
  ### @brief Permitted Choices: (Type: UINT8))(Default: 0x00)
  ### @li 0x0: 1506; 144; 6050; 640
  ### @li 0x1: 1538; 154; 6068; 656
  ### @li 0x2: 1358; 128; 6032; 624
  ### @li 0x3: 1180; 112; 5996; 608
  ### @li 0x5: 512;  64;  2048; 256
  ### @li 0x6: 128;  16;  512;  128
  ### @li 0x7: SKP transmission disabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSrisSkipInterval|0|UINT8|0x000A6509                       #///< Controls SRIS SKP generation interval
  ### @endcond

  ### @cond (RS||GN||BA||MI3||BRH)
  ### @brief Indicates if the Port supports both SRIS and software control of the SKP ordered set transmission scheduling rate for the indicated speed. This control
  ### depends on PcdSrisEnableMode enabled.
  ### @brief Permitted Choices: (Type: Value)(Default: 0)
  ### @li 0x0 - No Supported
  ### @li 0x1 - Supported at 2.5 GT/s
  ### @li 0x3 - Supported at 2.5 to 5.0 GT/s
  ### @li 0x7 - Supported at 2.5 to 8.0 GT/s
  ### @li 0xF - Supported at 2.5 to 16.0 GT/s
  ### @li 0x1F - Supported at 2.5 to 32.0 GT/s
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSrisLowerSkpOsGenSup|0|UINT8|0x000A650A                   #///< Controls LOWER_SKP_OS_GEN_SUPPORT
  ### @endcond

  ### @cond (RS||GN||BA||MI3||BRH)
  ### @brief Indicates if the Port supports both SRIS and receiving SKP ordered sets at the SRNS rate, while running in SRIS, for the indicated speed(s). This control
  ### depends on PcdSrisEnableMode enabled.
  ### @brief Permitted Choices: (Type: Value)(Default: 0)
  ### @li 0x0 - No Supported
  ### @li 0x1 - Supported at 2.5 GT/s
  ### @li 0x3 - Supported at 2.5 to 5.0 GT/s
  ### @li 0x7 - Supported at 2.5 to 8.0 GT/s
  ### @li 0xF - Supported at 2.5 to 16.0 GT/s
  ### @li 0x1F - Supported at 2.5 to 32.0 GT/s
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSrisLowerSkpOsRcvSup|0|UINT8|0x000A650B                   #///< Controls LOWER_SKP_OS_RCV_SUPPORT
  ### @endcond

  ### @cond (PHX)
  ### @brief Set this to indicate the Zstates Slew Rate must change
  ### @li 0   - Not touch
  ### @li 1   - Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuZ10SlewRateOverride|0x0|UINT8|0x000A650D
  ### @endcond

  ### @cond (PHX)
  ### @brief Z10 Slew Rate control - Up Slew Rate Vdd
  ### @brief 4 bits [3:0]: Up Slew Rate = [3:0] * 2.5 + 2.5 mV/us
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuZ10UpSlewRateVdd|0x0|UINT8|0x000A650E
  ### @endcond

  ### @cond (PHX)
  ### @brief Z10 Slew Rate control - Up Slew Rate Soc
  ### @brief 4 bits [3:0]: Up Slew Rate = [3:0] * 2.5 + 2.5 mV/us
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuZ10UpSlewRateSoc|0x0|UINT8|0x000A650F
  ### @endcond

  ### @cond (PHX)
  ### @brief Z10 Slew Rate control - Up Slew Rate Sr
  ### @brief 4 bits [3:0]: Up Slew Rate = [3:0] * 2.5 + 2.5 mV/us
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuZ10UpSlewRateSr|0x0|UINT8|0x000A6510
  ### @endcond

  ### @cond (PHX)
  ### @brief Z10 Slew Rate control - Down Slew Rate Vdd
  ### @li 0   - Negative equal to Positive Slew Rate
  ### @li 1   - Negative equal to 1/4 Positive Slew Rate
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuZ10DownSlewRateVdd|0x0|UINT8|0x000A6511
  ### @endcond

  ### @cond (PHX)
  ### @brief Z10 Slew Rate control - Down Slew Rate Soc
  ### @li 0   - Negative equal to Positive Slew Rate
  ### @li 1   - Negative equal to 1/4 Positive Slew Rate
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuZ10DownSlewRateSoc|0x0|UINT8|0x000A6512
  ### @endcond

  ### @cond (PHX)
  ### @brief Z10 Slew Rate control - Down Slew Rate Sr
  ### @li 0   - Negative equal to Positive Slew Rate
  ### @li 1   - Negative equal to 1/4 Positive Slew Rate
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuZ10DownSlewRateSr|0x0|UINT8|0x000A6513
  ### @endcond

  ### @cond (PHX||GNR)
  ### @brief address of Smu13BiosIfTable_t.PsmGuardband_EXTERNAL[5][4]; //5 frequency points * 4 temperature points
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCurveShaperPsmArray|0|UINT64|0x000A6514
  ### @endcond

  ### @cond (PHX)
  ### @brief Override setting of Smu13BiosIfTable_t for the SB-TSI Slave address
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuSbTsiSlaveAddrOverride|0|UINT8|0x000A6515
  ### @endcond

  ### @cond (PHX)
  ### @brief SB-TSI Slave Address Select: 0 = 98h, 1 = 9Ah, 2 = 9Ch, 3 = 9Eh, 4 = 90h, 5 = 92h, 6 = 94h, 7 = 96h
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuSbTsiSlaveAddrSelect|0|UINT8|0x000A6516
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief MPDMA-TF PASID Enable
  ### @details  Enable. If programmed when Enable bit is clear in the ATS Control register, controls if the function is permitted to send requests that contain
  ### a PASID value.
  ### @li TRUE - Enable
  ### @li FALSE - Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgDev0F1PasidEn|FALSE|BOOLEAN|0x000A6517
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief NBIF DEV0 Enable AtomicOp Routing support in Downstream Port
  ### @li FALSE - Disable AtomicOp Routing support
  ### @li TRUE - Enable AtomicOp Routing support
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAtomicRoutingEnStrap5|FALSE|BOOLEAN|0x000A6518
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief NBIF Endpoint device. Controls the ability of a function to initiate AtomicOp requests.
  ### @li TRUE - Enable
  ### @li FALSE - Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdNbifDev0F1AtomicRequestEn|FALSE|BOOLEAN|0x000A6519
  ### @endcond

  ### @cond (RPL)
  ### @brief Reorder CPPC Preferred Cores
  ### @li 0 - Frequency
  ### @li 1 - Cache
  ### @li 2 - Driver
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgReorderCPPCPreferredCores|0xFF|UINT8|0x000A651A
  ### @endcond

  ### @cond (PHX)
  ### @brief Control of SMU HSP CLK Deep Sleep support
  ### @li FALSE - Disable SMU HSP CLK Deep Sleep support
  ### @li TRUE - Enable SMU HSP CLK Deep Sleep support
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuHspClkDsEn|TRUE|BOOLEAN|0x000A651B
  ### @endcond

  ### @cond (PHX)
  ### @brief Control of SMU FCLK DPM support
  ### @li FALSE - Disable SMU FCLK DPM support
  ### @li TRUE - Enable SMU FCLK DPM support
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuFclkDpmEn|TRUE|BOOLEAN|0x000A651C
  ### @endcond

  ### @cond (PHX)
  ### @brief Control of SMU GFX DPM support
  ### @li FALSE - Disable SMU GFX DPM support
  ### @li TRUE - Enable SMU GFX DPM support
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuGfxDpmEn|TRUE|BOOLEAN|0x000A651D
  ### @endcond

  ### @cond (STX)
  ### @brief Control of Smart PC Boost and Throttle feature.
  ### @li FALSE - Disable SPBT support
  ### @li TRUE - Enable SPBT support
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuSpbtEn|FALSE|BOOLEAN|0x000A651F
  ### @endcond

  ### @cond (STX)
  ### @brief Specify the CCLK Dense Core Fmax override [MHz]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuCclkFmaxOverrideDns|0|UINT16|0x000A6520
  ### @endcond

  ### @cond (STX)
  ### @brief Specify the CCLK Dense Core Fmin override [MHz]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuCclkFminOverrideDns|0|UINT16|0x000A6521
  ### @endcond

  ### @cond (STX)
  ### @brief Specify ISP with MIPI PHY
  ### @li FALSE - Disable Mipi support
  ### @li TRUE - Enable Mipi support
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMipiEn|TRUE|BOOLEAN|0x000A6522
  ### @endcond

  ### @cond (PHX||STX)
  ### @brief This PCD sets the vendor/device id of PCIe device list which request adding latency on RP's T Power On Control
  ### @brief typedef struct {
  ### @brief   UINT16    VendorId;
  ### @brief   UINT16    DeviceId;
  ### @brief   UINT32    Delay;    // us
  ### @brief } T_POWER_ON_DELAY;
  ### @brief T_POWER_ON_DELAY TPowenOnDelayListDeviceList[2] = {
  ### @brief   {0x0000, 0x0000, 0x00000000},
  ### @brief   {0xFFFF, 0xFFFF, 0x00000000} // TERMINATE
  ### @brief };
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTPOnDelayListVidDid|{0x0}|VOID*|0x000A6523
  ### @endcond

  ### @cond (STX)
  ### @brief This PCD control if check LcRecoveryListDeviceList or not.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEngineLcRecoveryControl|FALSE|BOOLEAN|0x000A6524
  ### @endcond

#----------------------------------------------------------------------------
#-  PSP Dynamic PCDs
### Set Doxy_path: "PCD-PSP.h"
#----------------------------------------------------------------------------
  ### PSP PCDs DYNAMIC

  ### @brief This feature selects the APCB recovery support on x86 side. If or when an instance of a writable APCB is determined to be invalid, the PSP driver will
  ### attempt a 'recovery' by copying the recovery instance of the APCB (default values as indicated in the APCB descriptor files). Upon boot up, the ABL reads CMOS
  ### bytes 06/07 at index/data port of 0x72/0x73. If the CMOS flag reads anything else other than 0xA55A or 0x5555, the system boots in APCB recovery mode, in which
  ### ABL consumes the recovery instances of APCB. Otherwise it boots in normal/non-recovery mode.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @brief This feature is affected by the platform CMOS power design. Please see Platform CMOS power .
  ### @li TRUE - the AGESA PSP driver restores the APCB instances from the recovery instance, writes 0xA55A to the CMOS location and triggers a reset . The next time
  ### the system powers up, ABL runs in normal/non-recovery mode.
  ### @li FALSE - the AGESA PSP driver writes 0x5555 to the CMOS location without restoring the APCB instances or triggering a reset. In this mode the additional reset
  ### is avoided at the potential risk of the writeable APCB instance being left corrupted forever.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspApcbRecoveryEnable|TRUE|BOOLEAN|0x95940008

  ### @cond !BRH
  ### @brief Switch to turn off PspKvm feature
  ### @details Switch to turn off PspKvm feature.
  ### @li TRUE:  Enable
  ### @li FALSE: Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspKvmEnable|FALSE|BOOLEAN|0x9594000C
  ### @endcond

  ### @cond !BRH
  ### @brief Switch between Absolute/Simple protocol for PSP KVM
  ### @details Switch between Absolute/Simple protocol for PSP KVM.
  ### @li 0: Absolute Protocol
  ### @li 1: Sample Protocol
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspKvmMouseProtocol|0x0|UINT8|0x9594000E
  ### @endcond

  ### @brief Switch to control the Later_SPL_FUSE C2P command for Anti-Rollback feature
  ### @details When set to TRUE, C2P command (MboxBiosCmdLaterSplFuse) will be sent for anti-rollback fuse by PSP request.
  ### @li TRUE:  Enable
  ### @li FALSE: Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspAntiRollbackLateSplFuse|FALSE|BOOLEAN|0x95940018

  ### @brief Initial SPL value specified at factory for validation purpose
  ### @details Initial SPL value specified at factory for validation purpose, by default 0.
  ### @li 0: no initial SPL value specified, system will follow SPL value in the SPL table
  ### if Anti-Rollback switch(PcdAmdPspAntiRollbackLateSplFuse) is TRUE.
  ### @li non-zero value: if Anti-Rollback switch(PcdAmdPspAntiRollbackLateSplFuse) is TRUE
  ### and Anti-Rollback is not enforced in CPU, BIOS will set SPL fuse with this initial SPL value.
  ### In subsequent boots, the initial SPL value will be ignored
  ### and the SPL fuse in the CPU will be kept as it is unless initial SPL fuse is set back to 0.
  ### Typically the initial SPL value can be ranged from 1 to 63,
  ### and it should be equal to or less than the SPL value in the SPL table.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspAntiRollbackInitialSpl|0|UINT32|0x95940030

  ### @brief Switch to turn on/off PspUpdateFlash functionality
  ### @details Switch to turn on/off PspUpdateFlash functionality.
  ### @li TRUE:  Enable
  ### @li FALSE: Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspSpiUpdateEnable|TRUE|BOOLEAN|0x95940019

  ### @brief Switch to control whether DRTM is enabled.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li FALSE - DRTM is not enabled.
  ### @li TRUE - DRTM is enabled.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspDrtmVirtualDevice|FALSE|BOOLEAN|0x95940021

  ### @cond !BRH
  ### @brief Switch to control RPMC support to PSP NVRAM
  ### @details When set to TRUE, RPMC support to PSP NVRAM.
  ### @li TRUE:  RPMC Enabled
  ### @li FALSE: RPMC Disabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspNvramUsingRpmc|FALSE|BOOLEAN|0x95940022
  ### @endcond

  ### @cond !BRH
  ### @brief Switch to control TPM config synchronization
  ### @details Check if System TPM config synchronization feature is enabled or not.
  ### @li TRUE:  Enable
  ### @li FALSE: Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspTpmConfigSynchronizationEnabled|TRUE|BOOLEAN|0x95940023
  ### @endcond

  ### @brief System TPM config Value
  ### @details System TPM config Value, SBIOS needs to set the value in PEI phase.
  ### @li 0x0:  dTPM
  ### @li 0x1:  PSP fTPM
  ### @li 0x2:  HSP fTPM
  ### @li 0xFF: no TPM
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspSystemTpmConfig|0xFF|UINT8|0x95940024

  ### @cond !BRH
  ### @brief TPM SMx algorithm flag
  ### @details TPM SMx algorithm flag, SBIOS needs to set the value in PEI phase.
  ### @li 0: SM2, SM3 or SM4 crypto algorithms not supported
  ### @li 1: SM2, SM3 or SM4 crypto algorithms supported
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspSystemTpmAlgSmx|0|UINT8|0x95940025
  ### @endcond

  ### @brief Switch to control PSB Trigger Deferred Fuse
  ### @details Switch to control PSB Trigger Deferred Fuse execute or not.
  ### @li TRUE:  PSB Trigger Deferred Fuse Enabled
  ### @li FALSE: PSB Trigger Deferred Fuse Disabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspPsbDeferredFuse|FALSE|BOOLEAN|0x9594002B

  ### @cond !BRH
  ### @brief fTPM NV RAM(entry 0x4) is corrupted or not
  ### @details When set to TRUE, fTPM NV RAM is corrupted.
  ### @li TRUE:  fTPM NV RAM is corrupted
  ### @li FALSE: fTPM NV RAM is NOT corrupted
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFtpmNvCorrupted|FALSE|BOOLEAN|0x9594002C
  ### @endcond

  ### @cond !BRH
  ### @brief Switch to control if Intrusion Detection feature is enabled
  ### @details Check if Intrusion Detection feature is enabled
  ### @li TRUE:  Enable Intrusion Detection feature
  ### @li FALSE: Disable Intrusion Detection feature
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspIntrusionDetectionEnabled|FALSE|BOOLEAN|0x9594002E
  ### @endcond

  ### @cond !BRH
  ### @brief Switch to control if intrusion event should be logged when intrusion is detected
  ### @details Check if intrusion event should be logged when intrusion is detected
  ### @li TRUE:  system will log intrusion event when intrusion is detected
  ### @li FALSE: system will not log intrusion event when intrusion is detected
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspLogEventIfIntrusionDetected|FALSE|BOOLEAN|0x9594002F
  ### @endcond

  ### @cond !BRH
  ### @brief Switch to control if TPM data should be cleared when intrusion is detected
  ### @details Check if TPM data should be cleared when intrusion is detected
  ### @li TRUE:  system will clear TPM data when intrusion is detected
  ### @li FALSE: system will not clear TPM data when intrusion is detected
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspClearTpmIfIntrusionDetected|FALSE|BOOLEAN|0x95940031
  ### @endcond

  ### @cond !BRH
  ### @brief Switch to control if system will be powered off forcibly when intrusion is detected
  ### @details Check if system will be powered off forcibly when intrusion is detected
  ### @li TRUE:  system will be powered off forcibly when intrusion is detected
  ### @li FALSE: system will not be powered off forcibly when intrusion is detected
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspPowerOffSystemIfIntrusionDetected|FALSE|BOOLEAN|0x95940032
  ### @endcond

  ### @cond !BRH
  ### @brief Switch to control if Psp Spread Out is enabled
  ### @details if enabled, PSP will enable NV erase 50ms delay + NV write with 1K buffer and 15ms delay
  ###          when sending SMI to BIOS to do NV write/erase
  ### @li TRUE:  PSP will enable NV erase 50ms delay + NV write with 1K buffer and 15ms delay
  ### @li FALSE: PSP will NOT enable NV erase 50ms delay + NV write with 1K buffer and 15ms delay
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspSpreadOutForNvWriteErase|FALSE|BOOLEAN|0x95940033
  ### @endcond

  ### @cond !BRH
  ### @brief Switch to Enable Pluton Security Processor or not
  ### @details When set to TRUE, bios will publish MHSP Acpi Table during POST time
  ### @li TRUE:  Publish MHSP Acpi Table
  ### @li FALSE: Do not Publish MHSP Acpi Table
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPlutonSecurityProcessorEnable|TRUE|BOOLEAN|0x95940034
  ### @endcond

  ### @cond !BRH
  ### @brief Switch to control if GFX workstation feature is enabled
  ### @details When set to TRUE, BIOS will notify PSP to set workstation bit,
  ### graphics driver will read this bit to enable workstation feature.
  ### @li TRUE:  Enable GFX workstation feature
  ### @li FALSE: Disable GFX workstation feature
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspGfxWorkstationEnabled|FALSE|BOOLEAN|0x95940035
  ### @endcond

  ### @brief Switch to control if Platform Secure Boot(PSB) is enabled or disabled.
  ### @details Send a request to enable/disable Platform Secure Boot(PSB), default value: 0.
  ### @li 0 - Do nothing
  ### @li 1 - Send a request to PSP to permanently enable PSB
  ### WARNING, When set to TRUE, AutoPSB feature will fuse the PSB related fuse,
  ### when BIOS is correctly signed the part with PSB fused, can NOT boot any unsigned BIOS.
  ### @li 2 - Send a request to PSP to permanently disable PSB.
  ### Mark the processor for PSB lockout. The processor will ONLY boot with an
  ### unsigned BIOS and will never be able to load a signed BIOS.
  ### *WARNING* This setting will permanently set a processor fuse which cannot be undone.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspPsbSelection|0|UINT8|0x95940036

  ### @cond !BRH
  ### @brief A flag to identify if APCB Recovery has been finished or not, only valid
  ###  when APCB recovery flag has been set
  ### @li TRUE:  Apcb Recovery is finished, APCB workable copy has been recovered from backup copy
  ### @li FALSE: Apcb Recovery is not finished.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspApcbRecoveryisDone|FALSE|BOOLEAN|0x95940037
  ### @endcond

  ### @cond !BRH
  ### @brief Switch to control if BIOS will send FMTC Query and Activate Command to PSP
  ### @details
  ### @li TRUE:  BIOS will send FMTC Query and Activate Command to PSP
  ### @li FALSE: BIOS will not send FMTC Query and Activate Command to PSP
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspSendFmtcMboxCmd|FALSE|BOOLEAN|0x95940038
  ### @endcond

  ### @cond !BRH
  ### @brief Instance Id Value for FMTC binary
  ### @details
  ### @li 0-7:  Valid Instance Id for FMTC binary
  ### @li 0xFE: FMTC not supported
  ### @li 0xFF: FMTC not activated
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspFmtcInstanceId|0xFF|UINT8|0x95940039
  ### @endcond

#----------------------------------------------------------------------------
#    MPM PCDs DYNAMIC
### Set Doxy_path: "PCD-MPM.h"
#----------------------------------------------------------------------------

  ### @name General MPM Controls

  ### @brief Switch to turn off all MPM AGESA code path execution
  ### @details When set to FALSE, turn off all MPM AGESA code path execution.
  ### @li 0: AIM-T Disabled
  ### @li 1: AIM-T enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMpmEnable|0|UINT8|0x95927101

  ### @brief Switch to turn off Wired KVM
  ### @details Switch to turn off Wired KVM, Only take effect when PcdMpmEnable set to MPM_AIMT_ENABLED.
  ### @li TRUE:  Enable
  ### @li FALSE: Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMpmWiredKvmEnable|FALSE|BOOLEAN|0x95927100

  ### @brief Switch to turn off All WirelessManageability related functions
  ### @details Switch to turn off All WirelessManageability related functions: KVM, TCR, PLDM, ALERT,
  ### and only take effect when PcdMpmEnable set to MPM_AIMT_ENABLED.
  ### If "Wireless Manageability" is enabled in BIOS, both KVM and TCR functionality
  ### will be enabled by default, Otherwise both KVM and TCR functionality are disabled.
  ### @li TRUE:  Enable
  ### @li FALSE: Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMpmWirelessManageability|FALSE|BOOLEAN|0x95927102

  ### @brief Switch to turn off Wireless KVM
  ### @details when set to FALSE, it will turn off Wireless KVM,
  ### only take effect when PcdMpmEnable set to MPM_AIMT_ENABLED and PcdMpmWirelessManageability set to TRUE.
  ### @li TRUE:  Enable
  ### @li FALSE: Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMpmWirelessKvmEnable|FALSE|BOOLEAN|0x95927103

  ### @brief Switch between Absolute/Simple protocol for MPM KVM
  ### @details Switch between Absolute/Simple protocol for MPM KVM,
  ### and only valid if PcdMpmWiredKvmEnable or PcdMpmWirelessKvmEnable set to TRUE.
  ### @li 0x0:  Absolute
  ### @li 0x1:  Simple
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMpmKvmMouseProtocol|0x0|UINT8|0x95927104


  ### @brief Switch to turn off Wireless Text Console Redirect
  ### @details when set to FALSE, it will turn off Wireless Text Console Redirect,
  ### only take effect when PcdMpmEnable set to MPM_AIMT_ENABLED and PcdMpmWirelessManageability set to TRUE.
  ### @li TRUE:  Enable
  ### @li FALSE: Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMpmWirelessTcrEnable|FALSE|BOOLEAN|0x95927105


  ### @brief HorizontalResolution for MPM KVM
  ### @details The Horizontal Resolution Value for MPM KVM
  ### @li 1024: Default Resolution Value
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMpmKvmHorizontalResolution|1024|UINT16|0x95927107

  ### @brief VerticalResolution for MPM KVM
  ### @details The Vertical Resolution Value for MPM KVM
  ### @li 768: Default Resolution Value
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMpmKvmVerticalResolution|768|UINT16|0x95927108

  ### @brief Switch to sync MPM APCB and Pcds
  ### @details sync APCB and Pcds enable/disable
  ### @li FALSE: Disable APCB/PCD sync
  ### @li TRUE: Enable APCB/PCD sync
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMpmConfigSynchronizationEnabled|TRUE|BOOLEAN|0x9592710A

  ### @brief Indicate MPM wireless NIC device is supported or not
  ### @details Indicate MPM wireless NIC device is supported or not
  ### @li FALSE: Unsupport
  ### @li TRUE: Support
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMpmWirelessNicDetected|FALSE|BOOLEAN|0x9592710B

  ### @brief Indicate MPM wired NIC device is supported or not
  ### @details Indicate MPM wired NIC device is supported or not
  ### @li FALSE: Unsupport
  ### @li TRUE: Support
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMpmWiredNicDetected|FALSE|BOOLEAN|0x9592710C

  ### @brief AIM-T Enablement Information
  ### @details This consists of information with which the AIM-T solution is configured to function
  ### It consists of capability options and firmware versions, such as whether AIM-T is enabled at OEM factory or not,
  ### AIM-T version, options of AIM-T enabled by core BIOS, provisioned state, supported DASH specification version, and so forth.
  ### @li FALSE: Disable
  ### @li TRUE: Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMpmCreateSmBiosTypeA1|TRUE|BOOLEAN|0x9592710D

  ### @brief AIM-T Provisioning Information
  ### @details This consists of information with which the AIM-T solution is provided to the function
  ### These details are provided by Enterprise IT Administrators and consist of organization details,
  ### authentication & access details, network details, signing & encryption certificates, enabled DASH profiles, and so forth
  ### @li FALSE: Disable
  ### @li TRUE: Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMpmCreateSmBiosTypeA2|TRUE|BOOLEAN|0x9592710E

  ### @brief AIM-T System is provisioned or not
  ### @details This provision value can provide to ABL or EC
  ### @li FALSE: AIM-T is not provisioned
  ### @li TRUE: AIM-T is provisioned
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMpmProvisioned|FALSE|BOOLEAN|0x95927112

  ### @brief Enable or Disable AIM-T wireless Device detection
  ### @details: Below PCDs will be set to FALSE when wireless NIC is not detected:
  ### PcdMpmWirelessManageability, PcdMpmWirelessKvmEnable, PcdMpmWirelessTcrEnable
  ### @li FALSE: Detection is disabled
  ### @li TRUE: Detection is enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMpmWirelessDeviceDetection|TRUE|BOOLEAN|0x95927114

  ### @brief Force enable fake Wlan or not
  ### @details Force enable fake Wlan or not
  ### @li FALSE: Did not force enable fake Wlan
  ### @li TRUE: Force enable fake Wlan
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMpmForceEnableFakeWlan|FALSE|BOOLEAN|0x95927115

  ### @brief Skip BootOrder variables for MPM Boot Config
  ### @details Skip BootOrder variables for MPM Boot Config
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMpmSkipBootOrderVariable1|"UiAppWrapper"|VOID*|0x95927116

  ### @brief Skip BootOrder variables for MPM Boot Config
  ### @details Skip BootOrder variables for MPM Boot Config
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMpmSkipBootOrderVariable2|"ToBeDefined"|VOID*|0x95927117

  ### @brief ResolutionX for MPM KVM Mouse Simple protocol
  ### @details The ResolutionX Value for MPM KVM Mouse Simple protocol
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMpmKvmMouseModeSimpleResolutionX|10|UINT8|0x9592711A

  ### @brief ResolutionY for MPM KVM Mouse Simple protocol
  ### @details The ResolutionY Value for MPM KVM Mouse Simple protocol
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMpmKvmMouseModeSimpleResolutionY|10|UINT8|0x9592711B

  ### @brief Switch to control HSP UART
  ### @details HSP UART enable/disable
  ### @li FALSE: Disable HSP UART
  ### @li TRUE: Enable HSP UART
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdHspUartEnable|FALSE|BOOLEAN|0x95927109

  ###  HSP PCDs DYNAMIC

  ### @brief HSP UART Port
  ### @details HSP UART Port
  ### @li 0: HSP UART Port 0
  ### @li 1: HSP UART Port 1
  ### @li 2: HSP UART Port 2 (UART2)
  ### @li 3: HSP UART Port 3
  ### @li 4: HSP UART Port 4
  ### @li 5: HSP UART Port 5
  ### @li 6: HSP UART Port 6
  ### @li 7: HSP UART Port 7
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdHspUartPort|2|UINT8|0x95927110

  ### @brief Switch to control FIPS enablement
  ### @details FIPS enable/disable, FIPS is short for Federal Information Processing Standards
  ### @li FALSE: Disable FIPS
  ### @li TRUE: Enable FIPS
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdHspFipsEnable|FALSE|BOOLEAN|0x95927111

  ### @brief Switch to turn off Secure BIO
  ### @details when set to FALSE, it will turn off ISP Secure BIO,
  ### @li TRUE:  Enable
  ### @li FALSE: Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSecureBioEnable|FALSE|BOOLEAN|0x95927118

  ### @brief Select the SecureBio camera type
  ### @details Select which type of camera need be reported in ACPI SDEV table
  ### XHCI Camera and MIPI camera are mutually exclusive options, only can select one at onetime
  ### @li 0: disabled
  ### @li 1: XHCI Camera
  ### @li 2: MIPI Camera
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSecureBioCameraType|0x0|UINT8|0x95927119

  ###  Other Security PCDs DYNAMIC

  ### @brief Switch to control Secure Core Auto Enablement feature
  ### @details if set to disable, AGESA will delete EFI variable "BuiltAsSecuredCorePC" if detected
  ### If set to enable, AGESA will set EFI variable "BuiltAsSecuredCorePC" to non-zero value, which
  ### works a identifier to OS, the Secured-core Auto enablement is turn on.
  ### @li 0: Disable Secure Core Auto Enablement feature
  ### @li 1: Enable Secure Core Auto Enablement feature
  ### @li other value: ignore, do nothing, this is used in case OEM want to implement "BuiltAsSecuredCorePC" by
  ###     their own without any impaction from AGESA.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdScpcAutoEnablement|0xFF|UINT8|0x95927120


#----------------------------------------------------------------------------
#-  RAS Dynamic PCDs
### Set Doxy_path: "PCD-RAS.h"
#----------------------------------------------------------------------------

  ### @name RAS Controls
  ### @{

  ### @cond (BA||GN||SSP)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMemPostPackageRepair|TRUE|BOOLEAN|0x00030004
  ### @endcond

  ### @cond (RS||BRH)
  ### @brief Boot Time Post Package Repair Control
  ### @details  This control to indicate to firmware to enable Soft or Hard
  ### post-package repairs during ABL memory training.
  ### @li TRUE:  Enable
  ### @li FALSE: Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMemBootTimePostPackageRepair|TRUE|BOOLEAN|0x00030005

  ### @brief RunTime Post Package Repair Control
  ### @details  This control to indicate to firmware to enable Soft post-package repairs of
  ### corrected memory errors during OS Runtime.
  ### post-package repairs during ABL memory training.
  ### @li TRUE:  Enable
  ### @li FALSE: Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMemRuntimePostPackageRepair|FALSE|BOOLEAN|0x00030006

  ### @brief RunTime Post Package Repair Only Mode Control
  ### @details  This control to indicate to firmware to only enable Soft post-package repairs of
  ### corrected memory errors during OS Runtime.
  ### post-package repairs during ABL memory training were ignored.
  ### @li TRUE:  Enable
  ### @li FALSE: Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMemRuntimePostPackageRepairOnly|FALSE|BOOLEAN|0x00030008

  ### @brief DRAM Post Package Repair Configuration Initiator
  ### @details  This PCD indicate the Post Package Repair configuration setting whether it is
  ### In-Band or Out-Of-band Repair. This PCD value will also be communicated to PMFW.
  ### @li 0: In-Band(default)
  ### @li 1: Out Of Band
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMemPostPackageRepairConfigInitiator|0x00|UINT8|0x00030009
  ### @endcond

  ### @cond (MI3)
  ### @brief Specifies the GFX WDT timeout period value [0 - 3F].
  ### @li 0 - Timeout disabled.
  ### @li Non-Zero - Timeout interval value.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdGfxWdtTimerInterval|0x02|UINT8|0x0004F108
  ### @endcond
  ###@} end of RAS Controls

  ### @brief This PCD is for the customized SVI3 table memory address.
  ### @brief To customize SVI3 values, customer should create the SVI3 table and have the
  ### @brief PcdSvi3TableAddress point to the table.  PcdSvi3TableAddress = 0 means no customized table created.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSvi3TableAddress|0x0|UINT32|0x0004F109

## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ##
## ##
## ##                D Y N A M I C    E X T E R N    P C D s
## ##
## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ##

[PcdsDynamicEx]
#----------------------------------------------------------------------------
#-  NBIO Dynamic PCDs
### Set Doxy_path: "PCD-NBIO.h"
#----------------------------------------------------------------------------

  ### @cond (RV||FF3||RMB||RN||RPL||PHX)
  ### @brief PEI GOP HOB data memory size
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPeiGopConfigMemsize|0x00|UINT32|0x00050000

  ### @brief PEI GOP HOB data memory base value
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPeiGopVmFbOffset|0x00|UINT32|0x00050001
  ### @brief PEI GOP HOB data memory size location top
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPeiGopVmFbLocationTop|0x00|UINT32|0x00050002
  ### @endcond

  #Hotplug
  ### @cond (BRH)
  ### @brief Masked DPC Capability
  ### @li TRUE: Disable DPC Capability (Masked)
  ### @li FALSE: Enable DPC Capability  (Unmasked)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMaskDpcCapability|FALSE|BOOLEAN|0x000500A0

  ### @brief Selects hot plug mode.
  ### @brief Permitted Choices: (Type: Value)(Default: 0xFF/Auto)
  ### @li 0 - F17M00 Compatibility mode ([F17M30] only).
  ### @li 1 - OS-First.
  ### @li 3 - Firmware-First ([F17M30] Rev B0 Only).
  ### @li 3 - Firmware-First ([F19M00] Error Disconnect Recovery (EDR) Support)
  ### @li 3 - Firmware First/EDR if OS supports ([F19M11])
  ### @li 5 - System Firmware Intermediary ([F19M10])
  ### @li 6 - Firmware First but allow OS First ([F19M11])
  ### @li 0xFF (Auto) - for:
  ### @li   F17M30 Rev A0 = F17M00 Compatibility mode
  ### @li   F17M30 Rev B0 = OS-First
  ### @li   F19M00/F19M10 = OS-First
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugHandlingMode|0xFF|UINT8|0x000500A1

  ### @brief Selects hot plug presence detection mode.
  ### @brief Permitted Choices: (Type: Value)(Default: 0xFF/Auto)
  ### @li 0 - 'OR' - Presence detect value is the logical OR of in-band presence detect and side-band presence detect.
  ### @li 1 - 'AND' - Presence detect value is the logical AND of in-band presence detect and side-band presence detect.
  ### @li 2 - 'In-band only' - Presence detect value is in-band presence detect.
  ### @li 3 - 'Out of band only' - Presence detect value is the side-band presence detect.
  ### @li 0xFF (Auto) - use 'OR' mode.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPresenceDetectSelectMode|0xFF|UINT8|0x000500A2
  ### @endcond

  ### @cond (RS||BRH)
  ### @brief Hot plug port settling time in ms.
  ### @brief Permitted Choices: (Type: Value)(Default: 0xFF)
  ### @li 01~254 = Range
  ### @li 0xFF - default settling time setting for:
  ### @li   F19M10 = 0xFF - 100ms
  ### @li   F1AM00 = 0xFF - 100ms
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugSettleTime|0xFF|UINT8|0x000500D0
  ### @endcond

  ### @cond (BRH)
  ### @brief Hot plug port settling multiplier.
  ### @brief Permitted Choices: (Type: Value)(Default: 0xFF)
  ### @li 0   = Settle time * 1
  ### @li 1   = Settle time * 10
  ### @li 2   = Settle time * 20
  ### @li 3   = Settle time * 30
  ### @li 0xFF - default settling time multiplier setting for:
  ### @li   F1AM00 = 0xFF - 0 (x1 multiplier)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugSettleTimeMultiplier|0xFF|UINT8|0x000500D6
  ### @endcond

  ### @cond (BRH)
  ### @brief Hot plug port sync time retry count.
  ### @brief Permitted Choices: (Type: Value)(Default: 0xFF)
  ### @li 01~15 = Range
  ### @li 0xFF - default sync time setting for:
  ### @li   F1AM00 = 0xFF - 3 retries
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugDLPDSyncCount|0xFF|UINT8|0x000500D7
  ### @endcond

  ### @cond (BRH)
  ### @brief Hot plug disable BridgeDis control.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugDisBridgeDis|FALSE|BOOLEAN|0x000500D8
  ### @endcond

  ### @cond (BRH)
  ### @brief Expose SFI_OOB_PD_SUPPORTED field in SFI_CAP register.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdExposeSFIOOBSupport|FALSE|BOOLEAN|0x000500D9
  ### @endcond

  ### @cond (BRH)
  ### @brief Expose DRS_SUPPORTED field in LINK_CAP2 register.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdExposeSFIDRSSupport|FALSE|BOOLEAN|0x000500DD
  ### @endcond

  ### @cond (BRH)
  ### @brief Set INBAND_PD_DISABLE field in SLOT_CAP register.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDisableInbandPDSupport|FALSE|BOOLEAN|0x000500DC
  ### @endcond

  ### @cond (RS||BRH)
  ### @brief Includes presence detect state in hot plug settling time.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugPDSettle|FALSE|BOOLEAN|0x000500D1
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Enabled SFI features in non-SFI handling mode.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugForceSFIStrap|FALSE|BOOLEAN|0x000500D2
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief  Enable PCIe Hot-plug port to enter Polling. Compliance state
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAllowComplianceForHpPort|TRUE|BOOLEAN|0x000500D3
  ### @endcond

  ### @cond (BRH)
  ### @brief Enabled Hot-plug support.
  ### @brief Permitted Choices: (Type: Value)(Default: 0xF/Auto)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugSupport|0xF|UINT8|0x000500D4
  ### @endcond

  ### @cond (BRH)
  ### @brief Sets default MAX_PAYLOAD_SIZE in DEVICE_CNTL register of unpopulated EnterpriseSSD hot plug ports.
  ### @brief Permitted Choices: (Type: Value)(Default: 0xFF)
  ### @li 0 - 128 Bytes
  ### @li 1 - 256 Bytes
  ### @li 2 - 512 Bytes
  ### @li 3 - 1024 Bytes
  ### @li 4 - 2048 Bytes
  ### @li 5 - 4096 Bytes
  ### @li 0xFF - default hardware setting.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugNVMEDefaultMaxPayload|0xFF|UINT8|0x000500A3
  ### @endcond

  ### @cond (GN||SSP||RS||BRH)
  ### @brief This options prevents sideband presence updates. This may be required in some specific configurations.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li 0 - Normal Mode (per spec).
  ### @li 1 - Skip sideband presence updates.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugNVMESkipHPStatUpdate|0x0|UINT8|0x000500A6
  ### @endcond

  ### @cond (GN||VMR)
  ### @brief Controls Hotplug Port Reset mode
  ###       1:  Express Module
  ###       3:  Hotplug Platform First
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotplugPortReset|0|UINT8|0x000500A7
  ### @endcond

  ### @cond (RS||MI3)
  ### @brief Allows firmware first and SFI flows for synchronous hot plug
  ### @li 0:  Do not allow firmware first flows for synchronous hot plug
  ### @li 1:  Allow firmware first flows for synchronous hot plug
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotplugSynchronousFF|0|UINT8|0x000500BB
  ### @endcond

  ### @cond (RS||MI3)
  ### @brief Controls whether endpoint can be validated
  ### @li 0:  Do not validate endpoint
  ### @li 1:  Allow for endpoint validation
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotplugValidateEndpoint|0|UINT8|0x000500AA
  ### @endcond

  ### @cond (RS||MI3||BRH)
  ### @brief PCIe polling
  ### @details  Allows PCIe RP to enter Polling. Compliance state
  ### @li 0      - Enabled, Allow
  ### @li 1      - Disabled, Disallow
  ### @li 0x0F   - Auto - Use hardware default value
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAllowCompliance|0x0F|UINT16|0x000500AB
  ### @endcond

  ### @cond (RS|BRH)
  ### @brief Enable PCIe SFI Config via OOB
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPCIeSFIConfigviaOOBEn|FALSE|BOOLEAN|0x000500D5
  ### @endcond

  #CXL
  ### @cond (RS||MI3||BRH)
  ### @brief CXL port control
  ### @details Force enablement of CXL on all ports.
  ### @li FALSE: Allow platforms to enable CXL by port
  ### @li TRUE: Force enablement of CXL on all ports.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlOnAllPorts|TRUE|BOOLEAN|0x000500AC
  ### @endcond

  ### @cond (RS||MI3||BRH)
  ### @brief CXL power management
  ### @details Enable/Disable Active-state power management (ASPM)
  ### @li FALSE: Disabled
  ### @li TRUE: Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlAspm|TRUE|BOOLEAN|0x000500AD
  ### @endcond

  ### @cond (RS||MI3||BRH)
  ### @brief CXL.io
  ### @details Enable/Disable L1 power saving state
  ### @li FALSE: Disabled
  ### @li TRUE: Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlIoL1|TRUE|BOOLEAN|0x000500AE
  ### @endcond

  ### @cond (RS||MI3||BRH)
  ### @brief CXL.io
  ### @details Enable/Disable L2 power saving state
  ### @li FALSE: Disabled
  ### @li TRUE: Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlIoL2|TRUE|BOOLEAN|0x000500AF
  ### @endcond

  ### @cond (RS||MI3||BRH)
  ### @brief CXL.cache CXL.mem
  ### @details Enable/Disable L1 power saving state
  ### @li FALSE: Disabled
  ### @li TRUE: Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlCaMemL1|TRUE|BOOLEAN|0x000500B0
  ### @endcond

  ### @cond (RS||MI3||BRH)
  ### @brief CXL.cache CXL.mem
  ### @details Enable/Disable L2 power saving state
  ### @li FALSE: Disabled
  ### @li TRUE: Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlCaMemL2|TRUE|BOOLEAN|0x000500B1
  ### @endcond

  ### @cond (RS||MI3||BRH)
  ### @brief CXL.cache CXL.mem
  ### @details Enable/Disable Special Purpose Memory
  ### @li FALSE: Disabled
  ### @li TRUE: Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlSpm|FALSE|BOOLEAN|0x000500B3
  ### @endcond

  ### @cond (RS||MI3||BRH)
  ### @brief CXL.cache CXL.mem
  ### @details Enable/Disable DVSEC Lock
  ### @li FALSE: Disabled
  ### @li TRUE: Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlDvsecLock|FALSE|BOOLEAN|0x000500B4
  ### @endcond

  ### @cond (RS||BRH)
  ### @details This 8-bit value defines time out value of hot plug slot device link training.
  ### @li  0: Disable checking CXL present in hot plug slot.
  ### @li 10: Default 10 seconds.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCxlHotPlugSlotTimeOut|10|UINT8|0x000500B7
  ### @endcond

  ### @cond (BRH)
  ### @brief CXL.cache CXL.mem
  ### @details Enable/Disable CXL Speculative Reads
  ### @li FALSE: Disabled
  ### @li TRUE: Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCxlSpeculativeReads|TRUE|BOOLEAN|0x000500BC
  ### @endcond

  ### @cond (BRH)
  ### @brief CXL Switch Interleaving
  ### @details Enable/Disable Interleaving of CXL Type 3 devices behind a CXL switch.
  ### @li FALSE: Disabled
  ### @li TRUE: Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlSwitchInterleave|FALSE|BOOLEAN|0x000500BD
  ### @endcond

  ### @cond (BRH)
  ### @brief CXL.cache CXL.mem
  ### @details Enable/Disable HDM Decoder Lock On Commit
  ### @li FALSE: Disabled
  ### @li TRUE: Enabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlHdmDecoderLockOnCommit|FALSE|BOOLEAN|0x000500BE
  ### @endcond

  ### @cond (BRH)
  ### @brief Truncate CXL memory size.
  ### @details Limit the max amount of CXL memory size to 32gb, 64gb or 128gb. (Default: 'Auto')
  ### @li 0      - 32GB
  ### @li 1      - 64GB
  ### @li 2      - 128GB
  ### @li 0xFF   - Auto
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTruncateCxlMemory|0xFF|UINT8|0x000500BF
  ### @endcond

  ### @cond (BRH||RS)
  ### @brief Temp Gen5 Advertisement
  ### @details Enable/Disable Temp Gen5 Advertisement for Alternate Protocol
  ### @li TRUE   - Enable
  ### @li FALSE  - Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCxlTempGen5AdvertAltPtcl|FALSE|BOOLEAN|0x000500C9
  ### @endcond

  ### @cond (BRH)
  ### @brief Sync Header Bypass
  ### @details Enable/Disable Sync Header Bypass
  ### @li TRUE   - Enable
  ### @li FALSE  - Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSyncHeaderByPass|TRUE|BOOLEAN|0x000500CA
  ### @endcond


  ### @cond (BRH)
  ### @brief Sync Header Bypass Compatibility Mode
  ### @details Enable/Disable Sync Header Bypass Compatibility Mode
  ### @li TRUE   - Enable
  ### @li FALSE  - Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCxlSyncHeaderByPassCompMode|TRUE|BOOLEAN|0x000500CB
  ### @endcond

  #MCTP
  ### @cond (GN||SSP||RS||MI3||BRH)
  ### @brief Enables or disables MCTP support. The MCTP support applies special programming that manually routes all unroutable VDMs to the BMC.
  ### @li TRUE - MCTP support is enabled.
  ### @li FALSE - MCTP support is disabled.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMCTPEnable|FALSE|BOOLEAN|0x000500A4
  ### @endcond

  ### @cond (BRH)
  ### @brief AMD MCTP Mode
  ### @details AMD MCTP Modes when MCTP is enabled.
  ### @li 0 - MCTP Bridge - Enables MCTP Endpoint Support and manually routes all unroutable VDMs to the BMC via an internal MCTP Bridge.
  ### @li 1 - Legacy MCTP + MCTP Bridge - BIOS will enable MCTP Bridge when required based off the system configuration, otherwise BIOS will default to Legacy MCTP where MCTP endpoint support is disabled
  ### and the BMC will route MCTP packets directly to other MCTP endpoints through the processor root ports without an internal MCTP Bridge.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMCTPMode|1|UINT8|0x000500B5
  ### @endcond

  ### @cond (BRH)
  ### @brief AMD MCTP discovery notify message
  ### @details Send AMD MCTP discovery notifify message to indicate it's presence to BMC on the bus.
  ### @li 0 - FALSE - AMD MCTP doesn't send discovery notifiy message
  ### @li 1 - TRUE - AMD MCTP sends discovery notifiy message
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMCTPDiscoveryNotify|FALSE|BOOLEAN|0x000500DA
  ### @endcond

  ### @cond (GN||SSP||RS||MI3||BRH)
  ### @brief This 16-bit value specifies the PCI address of the MCTP master.
  ### @brief This has no functionality if PcdAmdMCTPEnable is FALSE.
  ### @brief This value is overwritten by the platform if PcdEarlyBmcLinkTraining is TRUE or if port parameter PP_BMC_LOCATION is configured
  ### @brief Permitted Values: MCTP Master ID = BMC bus/device/function (Bus << 8 | Device << 3 | Function)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMCTPMasterID|0|UINT16|0x000500A5
  ### @endcond

  ### @cond (GN||SSP||RS||BRH)
  ### @brief This 8-bit value specifies the Segment of the PCI address of the MCTP master.
  ### @brief This has no functionality if MCTPMultiSegEn is 0 or if PcdAmdMCTPEnable is FALSE.
  ### @brief This value is overwritten by the platform if PcdEarlyBmcLinkTraining is TRUE or if port parameter PP_BMC_LOCATION is configured
  ### @brief Permitted Values: 0 - 127
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMCTPMasterSeg|0|UINT8|0x000500B6
  ### @endcond

  ### @cond (GN)
  ### @brief This PCD sets the limit on the maximum VDDCR_CPU supply voltage that can be requested by the CPU. Valid range 1150mV - 1550mV.
  ### @brief Permitted Choices: (Type: Value(32b))(Default: 1550mV)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdVddCrCpuVidCap|0x60E|UINT32|0x000500A8
  ### @endcond

  ### @cond (PHX||STX||RMB||MDN)
  ### @brief USB/Display Combo PHY info - 0: Enable USB capability; 1:Disable USB capability.
  ### @details USBC0[0:3] -- Combo-PHY 0,USBC1[4:7] -- Combo-PHY 1,USBC2[8:11] -- Combo-PHY 2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdBitMapDisaplyOnlyController|0x00000000|UINT32|0x000500A9
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief PCIe Speed Control
  ### @details  PCIe Speed Control of PCIe Rate on Gen5-Capable devices.
  ### @li 0      - Enable PCIe speed controller
  ### @li 1      - Limit to GEN4
  ### @li 2      - Limit to GEN5
  ### @li 0xF    - Auto
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPcieSpeedControl|0xF|UINT8|0x000500B2
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Switch to control GMI Folding feature
  ### @details This Pcd is supported in Bergamo only.
  ### The value of this pcd is ignored in Genoa because GMI Folding is not supported on it.
  ### @li 0      - Disable GMI folding
  ### @li 1      - Enable GMI folding
  ### @li 0xF    - Auto, Use hardware default value
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdGmiFolding|0xF|UINT8|0x000500B9
  ### @endcond

  ### @cond (RS || BRH)
  ### @brief Collaborative Processor Performance Control (CPPC) is a new feature added to ACPI 6.1 that allows the OS to more closely control the core(s) performance.
  ### Further details for this feature can be found in ACPI specification and AMD CPPC overview . This control allows the platform to control the state of the feature.
  ### @brief Permitted Choices: (Type: Boolean)(Default: 'Auto')
  ### @li Auto (0xF) - Use the default specified by the product fusing.
  ### @li Disabled (0) - The feature is turned off.
  ### @li Enabled (1) - The feature is operational in the system.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgCPPCMode|0xF|UINT8|0x000500BA
  ### @endcond

  ### @cond (RS||BRH)
  ### @brief DfPstate Range Support
  ### @details DF Pstate selection is overridden by the APB_DIS BIOS option if it is selected. If enable this feature, the range value setting should follow the rule that MaxDfPstate <= MinDfPstate. Otherwise it will not work.
  ### @li 0 - Disables this feature.
  ### @li 1 - Enables this feature.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDfPstateRangeSupportEn|0|UINT8|0x000500C0
  ### @endcond

  ### @cond (RS||BRH)
  ### @brief The value for MaxDfPstate.
  ### @li 0 - DFP0
  ### @li 1 - DFP1
  ### @li 2 - DFP2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDfPstateRangeMax|0|UINT8|0x000500C1
  ### @endcond

  ### @cond (RS||BRH)
  ### @brief The value for MinDfPstate.
  ### @li 0 - DFP0
  ### @li 1 - DFP1
  ### @li 2 - DFP2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDfPstateRangeMin|0|UINT8|0x000500C2
  ### @endcond

  ### @cond (RS||BRH)
  ### @brief The value for PCIE Idle Power Settings. Modify PCIE Power Savings Features that can impact lightly loaded latency.
  ### @li 0xF - Optimize for Perf/Power
  ### @li 1 - Optimize for Latency
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieIdlePowerSetting|0xF|UINT8|0x000500C3
  ### @endcond

  ### @cond (BRH)
  ### @brief Enables or disables Data Object Exchange (DOE) capability.
  ### @li FALSE - Disable DOE capability
  ### @li TRUE - Enable DOE capability
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDataObjectExchange|TRUE|BOOLEAN|0x000500C4
  ### @endcond

  ### @cond (RS)
  ### @brief This value is to select GPF phase-2 timeout from 1 to 10 (0.5 to 5 sec in 0.5 sec granurality) with default 0 to auto.
  ### @brief Permitted Values: 0 - 10
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCxlGpfPhase2Timeout|0|UINT8|0x000500C5
  ### @endcond

  ### @cond (BRH)
  ### @brief Separate CPU power plane throttling.
  ### @li 0   - link throttling for CPU planes;
  ### @li 1   - Unlink throttling for CPU planes
  ### @li 0xF - Auto - Based on platform configuration.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdThrottlerMode|0xF|UINT8|0x000500C6
  ### @endcond

  ### @cond (BRH)
  ### @brief Multi Auto Speed Change On Last Rate
  ### @li FALSE   - Disable Multi Auto Speed Change On Last Rate
  ### @li TRUE    - Enable Multi Auto Speed Change On Last Rate
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdLcMultAutoSpdChgOnLastRateEnable|FALSE|BOOLEAN|0x000500C7
  ### @endcond

  ### @cond (BRH)
  ### @brief Multi Upstream Auto Speed Change
  ### @li 0   - Disable Multi Upstream Auto Speed Change
  ### @li 1   - Enable Multi Upstream Auto Speed Change
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAutoSpeedChangeEnable|0|UINT8|0x000500C8
  ### @endcond

  ### @cond (RS||BRH)
  ### @brief Controls the completion timeout value
  ### @li 1h      - 50 to 100 us (Range A only)
  ### @li 2h      - 1 to 10 ms (Range A only)
  ### @li 5h      - 16 to 55 ms (Range B only)
  ### @li 6h      - 65 to 210 ms (Range B only)
  ### @li 9h      - 260 to 900 ms (Range C only)
  ### @li Ah      - 1 to 3.5 s (Range C only)
  ### @li Dh      - 4 to 13 s (Range D only)
  ### @li Eh      - 17 to 64 s (Range D only)
  ### @li 0xFF   - Auto - Use hardware default value
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgDxioCplTimeout|0x0FF|UINT8|0x000500CC
 ### @endcond

## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ##
## ##
## ##                F I X E D  &  D Y N A M I C   P C D s
## ##
## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ## ##
                                                                                                        # Reserved [12:31]
[PcdsFixedAtBuild, PcdsDynamic]
#----------------------------------------------------------------------------
#-  System control PCDs
### Set Doxy_path: "PCD-Sys.h"
#----------------------------------------------------------------------------

  ###IDS PCDs
  ### @brief Switch for AGESA_TESTPOINT enable or not
  ### @details When Set this Pcd to enable, it will add AGESA_TESTPOINT with Post Code.
  ### @li TRUE:  Enable
  ### @li FALSE: Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAgesaTestPointEnable|TRUE|BOOLEAN|0x00020001

  ### @brief Switch for AGESA_TESTPOINT enable or not in SMM modules
  ### @details When Set this Pcd to enable, it will add AGESA_TESTPOINT with Post Code in SMM modules.
  ### @li TRUE:  Enable
  ### @li FALSE: Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAgesaTestPointEnableSmm|TRUE|BOOLEAN|0x00020018

  ### @brief Specify AGESA_TESTPOINT output data width
  ### @details Set the width of AGESA_TESTPOINT output data, This Pcd value only active
  ### when PcdAgesaTestPointEnable set to TRUE.
  ### @li 1: AccessWidth8
  ### @li 2: AccessWidth16
  ### @li 3: AccessWidth32
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAgesaTestPointWidth|3|UINT8|0x00020002

  ### @brief Switch for Debug Print function
  ### @details Switch for Debug Print function to enable or not.
  ### @li TRUE:  Enable IdsDebugPrint output
  ### @li FALSE: Disable IdsDebugPrint output
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdIdsDebugPrintEnable|FALSE|BOOLEAN|0x00020004

  ### @brief Specify the filter for IdsDebugPrint
  ### @details When the filter values are matched, the AmdIdsDebugPrint can start to print debug message.
  ### Refer Library/IdsLib.h for details, and the Master token is PcdAmdIdsDebugPrintEnable.
  ### @li 0x100401008A30042C: (GNB_TRACE | PCIE_MISC | NB_MISC | GFX_MISC  | CPU_TRACE | MEM_FLOW | MEM_STATUS | MEM_PMU | FCH_TRACE | MAIN_FLOW| TEST_POINT | PSP_TRACE)
  ### @li 0x100401008A300408: (GNB_TRACE | PCIE_MISC | NB_MISC | GFX_MISC  | CPU_TRACE | MEM_FLOW | FCH_TRACE  | MAIN_FLOW| TEST_POINT | PSP_TRACE)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdIdsDebugPrintFilter|0x100401030A300408|UINT64|0x00020005

  ### @brief Switch for Redirect IO layer of debug print
  ### @details Switch for Redirect IO layer of debug print, normally used on emulation platform.
  ### @li TRUE:  Enable
  ### @li FALSE: Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdIdsDebugPrintRedirectIOEnable|FALSE|BOOLEAN|0x00020008

  ### @brief Switch for Serial port support of AGESA debug print
  ### @details Switch for Serial port support of AGESA debug print, NOTE, AGESA will not init the serial port,
  ### serial port should be initialized before call AGESA debug print.
  ### @li TRUE:  Enable
  ### @li FALSE: Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdIdsDebugPrintSerialPortEnable|FALSE|BOOLEAN|0x00020009

  ### @brief Selection of UART and UART Legacy IO Serial port for AGESA debug print
  ### @details Selection of UART and UART Legacy IO Serial port for AGESA debug print and invisible in ACPI name space.
  ### @li Bit[0]:  Select UART0 for AGESA debug print
  ### @li Bit[1]:  Select UART1 for AGESA debug print
  ### @li Bit[2]:  Select UART2 for AGESA debug print
  ### @li Bit[3]:  Select UART3 for AGESA debug print
  ### @li Bit[4]:  Select UART4 for AGESA debug print
  ### @li Bit[8]:  Select UART0 Legacy IO for AGESA debug print
  ### @li Bit[9]:  Select UART1 Legacy IO for AGESA debug print
  ### @li Bit[10]: Select UART2 Legacy IO for AGESA debug print
  ### @li Bit[11]: Select UART3 Legacy IO for AGESA debug print
  ### @li Bit[15]: Set debug print serial port to invisible in ACPI name space at OS runtime
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdIdsDebugPrintSerialPortSelect|0x0000|UINT16|0x0002718A

  ### @brief The serial port used for debug output can be either 2-wire (Rx/Tx) or 4-wire (adding DSR/CTS). Waiting for CTS on a
  ### 2-wire port would cause a hang. This control is added to check the cable connection.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li FALSE - Presume a 2-wire port and do not wait for CTS.
  ### @li TRUE - Wait for both DSR and CTS to be set. DSR is set if a cable is connected. CTS is set if it is ok to transmit data.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdIdsDebugPrintSerialPortDetectCableConnection|FALSE|BOOLEAN|0x0002000A

  ### @brief Specify the IO port for serial out
  ### @details ESPI or LPC COM1: 0x3F8, COM2: 0x2F8, COM3: 0x3E8, COM4: 0x2E8,
  ### UART0: 0xFEDC9000, UART1: 0xFEDCA000, UART2: 0xFEDCE000, UART3: 0xFEDCF000, UART4: 0xFEDD1000.
  ### @li If it's IO port: it must < 0x10000
  ### @li If it's Memory: it must >= 0x10000
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdIdsDebugPrintSerialPort|0x3F8|UINT64|0x0002000B

  ### @brief Switch for IDS_HOOK
  ### @details Switch IDS HOOK supported or not. TRUE: IDS HOOK supported. FALSE: IDS HOOK unsupported.
  ### @li TRUE:  Enable
  ### @li FALSE: Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdIdsHookEnable|TRUE|BOOLEAN|0x0002000C

  ### @brief Switch for AGESA ASSERT
  ### @details Switch for AGESA ASSERT to enable or not.
  ### @li TRUE:  Enable AGESA ASSERT
  ### @li FALSE: Disable AGESA ASSERT
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAgesaAssertEnable|TRUE|BOOLEAN|0x0002000D

  ### @brief Switch for write AGESA testpoint to STB
  ### @details Switch for write AGESA testpoint to STB to enable or not.
  ### @li TRUE:  Enable
  ### @li FALSE: Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAgesaTestPointToStb|FALSE|BOOLEAN|0x0002000F

  ### @brief Initialize UART Non-legacy Mode
  ### @details Initialize UART Non-legacy Mode AOAC, IOMUX, Baudrate, Line Control and Fifo Control.
  ### @li TRUE:  Enable
  ### @li FALSE: Disable
  ### This PCD is redundant, it will be deleted in the future.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdIdsUartInitialize|FALSE|BOOLEAN|0x00020010

  ### @brief UART Clock setting
  ### @details SOC UART Clock setting.
  ### @li 48000000: Default Value
  ### @li  1843200: For Legacy IO Mode, and maximum baudrate is 115200.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdIdsUartClock|48000000|UINT32|0x00020011

  ### @brief UART BaudRate Value Setting
  ### @details UART BaudRate Value Setting. To receive data from UART with higher speed > 500K,
  ### you may use X_S_h_e_l_l instead of P_u_t_t_y.
  ### @li 3000000: 3M
  ### @li 1500000: 1.5M
  ### @li 1000000: 1M
  ### @li 750000:  750K
  ### @li 600000:  600K
  ### @li 500000:  500K
  ### @li 428571:  429K
  ### @li 375000:  375K
  ### @li 333333:  333K
  ### @li 115200:  115K - Maximum baudrate for Legacy IO Mode.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdIdsUartBaudRate|3000000|UINT32|0x00020012

  ### @brief UART LineControl setting
  ### @details SOC UART Line Control
  ### @li 0x03: Default Value
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdIdsUartLineControl|0x03|UINT8|0x00020013

  ### @brief UART FifoControl setting
  ### @details UART FifoControl setting
  ### @li 0x21: Default Value
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdIdsUartFifoControl|0x21|UINT8|0x00020014

  ### @brief Switch to enable UART hardware flow control
  ### @details If UART cannot support it, please set it to FALSE.
  ### Recommand to enable it if BaudRate > 500K, and you have to connect TXD, CTS and Ground pin at least.
  ### Usage: UART TXD <-> TTL cable RXD, UART CTS <-> TTL cable RTS and UART Ground <-> TTL cable Ground.
  ### @li TRUE:  Enable
  ### @li FALSE: Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdIdsDebugPrintSerialPortHardwareFlowControl|FALSE|BOOLEAN|0x00020015

  ### @brief Debug Print Emulation Auto Detect
  ### @details Auto detect emulation platform to instead of PcdAmdIdsDebugPrintRedirectIOEnable and
  ### PcdAmdIdsDebugPrintSerialPortEnable.
  ### @li TRUE:  Enable
  ### @li FALSE: Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdIdsDebugPrintEmulationAutoDetect|FALSE|BOOLEAN|0x00020016

  ### @brief Swtich for STB related features, including STB write function, STB register config,
  ### send STB verbosity to FWs.
  ### @details STB is short for Smart Trace buffer, is a data buffer used to log information about
  ### system execution for characterization and debug purposes using HDT. In our production software
  ### stack we have allocated this buffer to serve as a multi-source, cross-IP, postcode buffer.
  ### Postcodes embedded within multiple firmware code bases are coalesced into this common time
  ### accurate data structure. This creates an inter-IP sequence visibility that is extremely useful to
  ### root cause certain types of bugs. This feature is enabled and running during normal steady state operation.
  ### At any point should a system encounter a functional failure the trace can be collected without
  ### need for reproducing the failure while running additional instrumentation.
  ### @li TRUE:  Enable
  ### @li FALSE: Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdStbEnable|FALSE|BOOLEAN|0x95927206

  ###Global VerbosityControl for all firmware modules, e.g. BIOS, SMU, PSP
  ### 0x0:  STB disabled
  ### 0x1:  Low level verbosity
  ### 0x2:  Production level verbosity
  ### 0x3:  High level verbosity
  ### 0xF:  PerIPControl
  ### 0 = STB disabled
  ###     No prints
  ### 1 = Low level verbosity
  ###     Limit to ~10 prints in a typical cold boot
  ### 2 = Production level verbosity (default, requires oversight)
  ###     Limit to ~256 prints in a typical cold boot
  ### 3 = High level verbosity
  ###     >256 prints in a typical cold boot, could be thousands
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdGlobalStbVerbosityControl|0x2|UINT8|0x95927201

  ###VerbosityControl for SMU, only active when PcdGlobalStbVerbosityControl equal to PerIPControl
  ### 0x0:  STB disabled
  ### 0x1:  Low level verbosity
  ### 0x2:  Production level verbosity
  ### 0x3:  High level verbosity
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdStbSmuVerbosityControl|0x2|UINT8|0x95927202

  ###VerbosityControl for PSP, only active when PcdGlobalStbVerbosityControl equal to PerIPControl
  ### 0x0:  STB disabled
  ### 0x1:  Low level verbosity
  ### 0x2:  Production level verbosity
  ### 0x3:  High level verbosity
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdStbPspVerbosityControl|0x2|UINT8|0x95927203

  ###VerbosityControl for BIOS, only active when PcdGlobalStbVerbosityControl equal to PerIPControl
  ### 0x0:  STB disabled
  ### 0x1:  Low level verbosity
  ### 0x2:  Production level verbosity
  ### 0x3:  High level verbosity
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdStbBiosVerbosityControl|0x2|UINT8|0x95927204

  ### @cond NOT(BRH)
  ### @brief SMN address for STB
  ### @details This address need to set specifically for each program.
  ### For combo program, this PCD need to update during RT by checking installed processor
  ### @li 0x3E30600  RMB | MP::MP2MMU::MP2_PMI_0
  ### @li 0x3B30600  PHX | MP::MP1MMU::MP1_PMI_3
  ### @li 0x3B30600  RPL | MP::MP1MMU::MP1_PMI_3
  ### @li 0x3E30600  MDN | MP::MP2MMU::MP2_PMI_0
  ### @li 0x3B30600  STX | MP::MP1MMU::MP1_PMI_3
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdStbSmnAddress|0x3E30600|UINT32|0x95927205
  ### @endcond

  ###Swtich for STB FilterMask feature
  ###      TRUE:  Enable
  ###      FALSE: Disable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdStbFilterMaskEnable|FALSE|BOOLEAN|0x95927207

  ###Only active when PcdStbFilterMaskEnable set to TRUE
  ###Need to set to the PostCode prefix assigned to each IBV
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdStbIbvSourceId|0xB0|UINT8|0x95927209

  ###VerbosityControl for MPIO, only active when PcdGlobalStbVerbosityControl equal to PerIPControl
  ### 0x0:  STB disabled
  ### 0x1:  Low level verbosity
  ### 0x2:  Production level verbosity
  ### 0x3:  High level verbosity
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdStbMpioVerbosityControl|0x2|UINT8|0x9592720C

#----------------------------------------------------------------------------
#-  PSP Dynamic PCDs
### Set Doxy_path: "PCD-PSP.h"
#----------------------------------------------------------------------------
  ### PSP PCDs

  ### @brief Switch to control if S3/Capsule start
  ### @details Switch to control if S3/Capsule start from SMM or
  ### the address provided by PspPlatfromProtocol->RsmHandOffInfo->RsmEntryPoint.
  ### @li TRUE:  S3/Capsule start from BspSmmResumeVector.
  ### @li FALSE: S3/Capsule start from PspPlatfromProtocol->RsmHandOffInfo->RsmEntryPoint.
  ### NOTE, Processor will be set to 32bits protect mode with pagging disabled.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspS3WakeFromSmm|TRUE|BOOLEAN|0x95940F00

  ### @brief Rom Armor selection
  ### @details Rom Armor selection
  ### @li 0:  Rom Armor is disabled
  ### @li 1:  Rom Armor 1 is enabled (VMR/MTS/CPK)
  ### @li 2:  Rom Armor 2 is enabled (RN/CZN)
  ### @li 3:  Rom Armor 3 is enabled (CGL, RMB and later)
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspRomArmorSelection|0|UINT8|0x95940054

