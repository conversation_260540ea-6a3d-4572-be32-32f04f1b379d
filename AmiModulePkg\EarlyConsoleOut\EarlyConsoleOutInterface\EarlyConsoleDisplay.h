//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2023, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************

/** @file EarlyConsoleDisplay.h

**/

#ifndef _EARLY_CONSOLE_DISPLAY_LIB_H_
#define _EARLY_CONSOLE_DISPLAY_LIB_H_

#include <Uefi.h>
#include <Token.h>
#include <Library/UefiLib.h>
#include <Library/MemoryAllocationLib.h>
#include <Library/PcdLib.h>
#include <Library/HobLib.h>
#include <Library/DebugLib.h>
#include <Library/PrintLib.h>
#include <Library/BaseMemoryLib.h>
#include <Ppi/AmiSimpleTextOutPpi.h>
#include <Ppi/AmiGraphicsOutputPpi.h>
#include <Protocol/SimpleTextOut.h>
#include <Protocol/GraphicsOutput.h>
#include <Protocol/PciEnumerationComplete.h>
#include <AmiEarlyConsoleOutInterface.h>

#define MAXIMUM_SIMPLE_TEXTOUT_PPI  3
#define MAX_HOTKEY_COUNT 4

typedef struct {
    UINTN StartX;
    UINTN StartY;
    UINTN Width;
    UINTN Height;
} HOTKEY_LOCATION;

typedef struct {
    EARLY_CONSOLE_DISPLAY_FRAME_TYPE    FrameType;
    UINT8                               FrameNo;           // Position in display. starts with 0 (column 0 and Row 0)
    UINT8                               ColumnPercentage;  // Height Percentage
    UINT8                               RowPercentage;     // Width Percentage
} EARLY_GRAPHICS_FRAME_DEFINE;

typedef struct {
    EARLY_CONSOLE_DISPLAY_FRAME_TYPE    FrameType;
    BOOLEAN                             IsFrameInfoValid;
    UINT8                               FrameNo; 
    UINT32                              StartX;
    UINT32                              StartY;
    UINT32                              EndX;
    UINT32                              EndY;
    UINT32                              DeltaX;
    UINT32                              DeltaY;
    UINT32                              FrameWidth;
    UINT32                              FrameHeight;
    UINT32                              StartRow;
    UINT32                              EndRow;
    UINT32                              StartColumn;
    UINT32                              EndColumn;
    UINT32                              CurrentRow;
    UINT32                              CurrentColumn;
    UINT8                               CurrentPercentage;
    UINT32                              HorizontalResolution;
    UINT32                              VerticalResolution;
} AMI_EARLY_GRAPHICS_FRAME_INFO;

typedef union {
    UINT64                              Ptr;
    AMI_SIMPLE_TEXT_OUTPUT_PPI          *SimpleTextOutPpi;
    EFI_SIMPLE_TEXT_OUTPUT_PROTOCOL     *SimpleTextOutProtocol;
} SIMPLE_TEXT_OUT_UNION;

typedef union {
    UINT64                           Ptr;
    AMI_GRAPHICS_OUTPUT_PPI          *GraphicsOutputPpi;
    EFI_GRAPHICS_OUTPUT_PROTOCOL     *GraphicsOutputProtocol;
} GRAPHICS_OUTPUT_UNION;

typedef struct {
    AMI_PEI_SIMPLE_TEXT_CONSOLE_TYPE   SimpleTextOutConsoleType;
    SIMPLE_TEXT_OUT_UNION              SimpleTextOut;
    GRAPHICS_OUTPUT_UNION              GraphicsOutput;
    BOOLEAN                            IsDisplayFrameInfoValid;
    AMI_EARLY_GRAPHICS_FRAME_INFO      FrameInfo[EarlyConsoleDisplayFrameMax];
} DISPLAY_FRAME_INFO;

typedef struct {
    SIMPLE_TEXT_OUT_UNION      ConsSimpleTextOut;
    DISPLAY_FRAME_INFO         DisplayFrameInfo[MAXIMUM_SIMPLE_TEXTOUT_PPI];
} EARLY_CONSOLE_DISPLAY_FRAME_INFO_HOB;

typedef struct {
    CHAR16  InfoMsgs[INFO_MESSAGE_BUFFER_SIZE];
    CHAR16  DebugMsgs[DEBUG_MESSAGE_BUFFER_SIZE];
} AMI_EARLY_CONSOLE_STRING_HOB;

/**
    Returns the screen frame information from HOB data based on the 
    console type

    @param   ConsoleType - Serial/Text/GOP

    @retval  DISPLAY_FRAME_INFO
**/
DISPLAY_FRAME_INFO*
GetDisplayFrameInfo (
    AMI_PEI_SIMPLE_TEXT_CONSOLE_TYPE   ConsoleType
);

/**
    Displays BLT buffer in all EFI GOP instances
    
    @param BltBuffer
    @param BltOperation
    @param SourceX
    @param SourceY
    @param Width
    @param Height
    @param Delta
    
    @return EFI_STATUS
**/
VOID
DisplayBltInAllGops (
    EFI_GRAPHICS_OUTPUT_BLT_PIXEL           *BltBuffer,
    EFI_GRAPHICS_OUTPUT_BLT_OPERATION       BltOperation,
    UINTN                                   SourceX,
    UINTN                                   SourceY,
    UINTN                                   DestinationX,
    UINTN                                   DestinationY,
    UINTN                                   Width,
    UINTN                                   Height,
    UINTN                                   Delta
);

/**
    @param IsAttribute  - If this flag is TRUE then consider Attribute
                        - Otherwise FrameInfo and String are valid
    @param Attribute    - Attribute value to be set on the screen.
    @param FrameInfo    - Pointer to Frame information data
    @param String       - String to be displayed on screen
**/
VOID
OutputStringInAllGops (
    BOOLEAN                          IsAttribute,
    UINTN                            Attribute,
    AMI_EARLY_GRAPHICS_FRAME_INFO    *FrameInfo,
    CHAR16                           *String
);

/**
    Appends the input string to the HOB data

    @param   DisplayFrameType - Frame type where the string to be appended
    @param   String  - String to be appended to HOB data                  

    @retval  VOID
**/
VOID
UpdateStringInHob (
    EARLY_CONSOLE_DISPLAY_FRAME_TYPE    DisplayFrameType,
    CHAR16                              *String
);

/**
    Initialize display frames as per display device type.
    
    @param None
    
    @return AMI_EARLY_GRAPHICS_FRAME_INFO_HOB  Pointer to Frame info HOB
**/
EARLY_CONSOLE_DISPLAY_FRAME_INFO_HOB *
EarlyConsoleInitFrames ();

/**
    Creates number of frames based on Porting
 
    @param DisplayFrameInfo   Pointer to Display Frame Info
    
    @return EFI_STATUS
**/
EFI_STATUS
CreateFrames (
  IN DISPLAY_FRAME_INFO                 *DisplayFrameInfo
);

/**
   Function to set the foreground and background color of the console devices

   @param Attribute         Attributes to set

   @retval EFI_SUCCESS attribute was changed successfully
   @retval EFI_DEVICE_ERROR device had an error
   @retval EFI_UNSUPPORTED attribute is not supported
**/
EFI_STATUS
EarlyConsoleSetAttribute (
  IN UINTN                            Attribute
);

/**
    Function that writes a Unicode string to all the output devices

    @param DisplayFrameType Frame type to output the string
    @param String           Pointer to the string to output

    @retval EFI_SUCCESS function executed successfully
    @retval EFI_DEVICE_ERROR error occurred during output string
    @retval EFI_WARN_UNKNOWN_GLYPH some of characters were skipped during output
**/
EFI_STATUS
EarlyConsoleOutputString (
    IN EARLY_CONSOLE_DISPLAY_FRAME_TYPE DisplayFrameType,
    IN CHAR16                           *String
);

/**
    Choose frame by its type and display BLT buffer
    
    @param DisplayFrameType - Type of frame to display
    @param BltBuffer        - Points to the Buffer data
    @param BltOperation     - Type of Operation to be performed
    @param SourceX          - X-position of the buffer 
    @param SourceY          - Y-position of the buffer 
    @param Width            - Width of the data to be displayed
    @param Height           - Height of the data to be displayed
    @param AlignCenter      - If TRUE, displays BLT buffer at center of the frame
                            - If FALSE, displays BLT buffer at top left corner of the frame
    
    @return EFI_STATUS
**/
EFI_STATUS
EarlyConsoleBlt (
    IN  EARLY_CONSOLE_DISPLAY_FRAME_TYPE        DisplayFrameType,
    IN  EFI_GRAPHICS_OUTPUT_BLT_PIXEL           *BltBuffer,
    IN  EFI_GRAPHICS_OUTPUT_BLT_OPERATION       BltOperation,
    IN  UINTN                                   SourceX,
    IN  UINTN                                   SourceY,
    IN  UINTN                                   Width,
    IN  UINTN                                   Height,
    IN  BOOLEAN                                 AlignCenter
);

/**
    Displays Checkpoint in PostCodeFrame
    
    @param CheckPoint - Checkpoint to be displayed on screen
    
    @return EFI_STATUS
**/
EFI_STATUS
EarlyConsoleDisplayCheckPoint (
    IN UINT8  CheckPoint
);

/**
    Displays progress bar in ProgressBarFrame
    
    @param Completion - Progress bar completion number to be incremented
    
    @return EFI_STATUS
**/
EFI_STATUS
EarlyConsoleDisplayProgressBar (
    IN UINT8   Completion
);

/**
    Function to report that hotkey is detected

    @param String - Hotkey String

    @retval EFI_SUCCESS function executed successfully
**/
EFI_STATUS
EarlyConsoleReportHotkey (
    IN CHAR8       *String
);

EFI_STATUS
EarlyConsoleDisplayDxeInit ();
#endif
