#;*****************************************************************************
#;
#; Copyright (C) 2008-2025 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = CbsSetupSmmBRH
  FILE_GUID                      = 6362004D-B2A3-44E1-AF2F-E860E5BD572E
  MODULE_TYPE                    = DXE_SMM_DRIVER
  VERSION_STRING                 = 1.0
  PI_SPECIFICATION_VERSION       = 0x0001000A
  ENTRY_POINT                    = CbsSetupSmmInit

[Sources]
  CbsSetupSmmV2.c
  CbsSetupSmm.h
  ../Build/ResourceBRH/AmdCbsVariable.h

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  AmdCbsPkg/AmdCbsPkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec

[LibraryClasses]
  UefiBootServicesTableLib
  UefiDriverEntryPoint
  BaseMemoryLib
  DebugLib
  CbsUpdateApcbLib
  SmmServicesTableLib
  SmmMemLib
  AmdPspDxeSmmBufLib

[Pcd]
   gAmdCbsPkgTokenSpaceGuid.PcdCbsSmmCommunicationAddress
   gAmdCbsPkgTokenSpaceGuid.PcdCbsSmmCommunicationBufferSize

[Guids]
  gCbsSystemConfigurationGuid                   ## PRODUCES
  gCbsSmmCommHandleGuid

[Protocols]
  gCbsProtocolGuid
  gCbsSetupProtocolGuid                         ## CONSUMES
  gAmdApcbSmmServiceProtocolGuid                ## CONSUMES

[BuildOptions]
  MSFT:*_*_*_CC_FLAGS = /D CBS_XXX_RAW_ID=F1A_BRH_RAW_ID /D CBS_XXX_RAW_ID_1=F1A_BRHD_RAW_ID
  GCC:*_*_*_CC_FLAGS = -D CBS_XXX_RAW_ID=F1A_BRH_RAW_ID -D CBS_XXX_RAW_ID_1=F1A_BRHD_RAW_ID

[Depex]
  TRUE

