//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************


/** @file PeiVideoTextOut.c
    This PEIM initializes VGA device, produces PPI for Video Display 
    and creates HOB with the Video device information.

**/

#include <PeiVideoTextOut.h>

EFI_STATUS
ReinstallVideoTextOutPpiAfterMemoryDiscovered (
  IN EFI_PEI_SERVICES           **PeiServices,
  IN EFI_PEI_NOTIFY_DESCRIPTOR  *NotifyDescriptor,
  IN VOID                       *Ppi
  );


EFI_STATUS
VideoNotify (
  IN EFI_PEI_SERVICES           **PeiServices,
  IN EFI_PEI_NOTIFY_DESCRIPTOR  *NotifyDescriptor,
  IN VOID                       *Ppi
  );

EFI_PEI_NOTIFY_DESCRIPTOR  mEndOfPeiSignalPpiNotifyList[] = {
  {
    (EFI_PEI_PPI_DESCRIPTOR_NOTIFY_CALLBACK | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST),
    &gEfiEndOfPeiSignalPpiGuid,
    PeiVideoTextOutNotifyCallback
  }
};

EFI_PEI_NOTIFY_DESCRIPTOR gVideoNotifyList[] = {
  {
           (EFI_PEI_PPI_DESCRIPTOR_NOTIFY_CALLBACK | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST),
           &PEI_VIDEO_CALLBACK_GUID,
           VideoNotify
  }
};

EFI_PEI_NOTIFY_DESCRIPTOR gVideoTextOutMemoryInstalledNotifyList[] = {
                                    {
                                     (EFI_PEI_PPI_DESCRIPTOR_NOTIFY_CALLBACK | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST),
                                     &gEfiPeiMemoryDiscoveredPpiGuid,
                                     ReinstallVideoTextOutPpiAfterMemoryDiscovered
                                    }
                                     };

/**
  Report Max display depth and current cursor position as guid HOB 
  so that DxeVideoTextOut driver can use it.

  @param PeiServices       An indirect pointer to the EFI_PEI_SERVICES table published by the PEI Foundation
  @param NotifyDescriptor  Address of the notification descriptor data structure.
  @param Ppi               Address of the PPI that was installed.

  @retval EFI_SUCCESS      Successfully update the Display records.
**/
EFI_STATUS
EFIAPI
PeiVideoTextOutNotifyCallback (
  IN EFI_PEI_SERVICES           **PeiServices,
  IN EFI_PEI_NOTIFY_DESCRIPTOR  *NotifyDescriptor,
  IN VOID                       *Ppi
  )
{

    EFI_STATUS                          Status;
    AMI_SIMPLE_TEXT_OUTPUT_PPI          *SimpleTextOutPpi;
    AMI_VIDEO_TEXT_OUT_PRIVATE_DATA     *VideoTextOutPrivate;
    AMI_SIMPLE_TEXT_OUT_HOB             *SimpleTextOutHob;
    UINT16                              HobSize = sizeof(AMI_SIMPLE_TEXT_OUT_HOB);


    // Locate Pei Simple Text Out Ppi.
    Status = PeiServicesLocatePpi (
                                &gAmiSimpleTextOutPpiGuid,
                                PcdGet16 (AmiPcdVideoSimpleTextOutPpiInstance),
                                NULL,
                                (VOID **)&SimpleTextOutPpi);
    
    if (EFI_ERROR (Status)) {
        DEBUG ((DEBUG_ERROR, "%a: LocatePpi for gAmiSimpleTextOutPpiGuid Status: %r \n", __FUNCTION__, Status));
        return Status;
    }

    VideoTextOutPrivate = (AMI_VIDEO_TEXT_OUT_PRIVATE_DATA *)SimpleTextOutPpi;
            
    // Create HOB for the Video Data
    Status = PeiServicesCreateHob( 
                                    EFI_HOB_TYPE_GUID_EXTENSION,
                                    HobSize,
                                    (VOID**)&SimpleTextOutHob);
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR,"%a(): Failed to create HOB data Status:%r", __FUNCTION__, Status));
        return Status;
    }

    // Initialize HOB
    SimpleTextOutHob->Header.Name = gAmiSimpleTextOutHobGuid;
    SimpleTextOutHob->Mode        = VideoTextOutPrivate->Mode;
    SimpleTextOutHob->MaxRows     = VideoTextOutPrivate->MaxRows;
    SimpleTextOutHob->MaxColumns  = VideoTextOutPrivate->MaxColumns;
    SimpleTextOutHob->IsGraphics  = FALSE;
    
    return EFI_SUCCESS;
}

/**
  Reset the text output device hardware and optionally run diagnostics

  @param  This                 The PPI instance pointer.
  @param  ExtendedVerification Driver may perform more exhaustive verification
                               operation of the device during reset.

  @retval EFI_SUCCESS          The text output device was reset.
  @retval EFI_DEVICE_ERROR     The text output device is not functioning correctly and
                               could not be reset.

**/
EFI_STATUS  
PeiSimpleTextOutReset (
    IN AMI_SIMPLE_TEXT_OUTPUT_PPI   *This,
    IN BOOLEAN                      ExtendedVerification
)
{
    // Reset should:
    // - Clear the screen 
    // - set the cursor back to (0,0)
    
    This->SetAttribute(This, EFI_BACKGROUND_BLACK | EFI_WHITE);
    
    This->ClearScreen(This);
    
    This->SetCursorPosition(This, 0, 0);
    
    This->EnableCursor(This, PcdGetBool(PcdDefaultCursorState));
    
    return  EFI_SUCCESS;
}

/**
    Sets the current coordinates of the cursor position

    @param  This        The PPI instance pointer.
    @param  Column      The position to set the cursor to. Must be greater than or
                        equal to zero and less than the number of columns and rows
                        by QueryMode ().
    @param  Row         The position to set the cursor to. Must be greater than or
                        equal to zero and less than the number of columns and rows
                        by QueryMode ().

    @retval EFI_SUCCESS      The operation completed successfully.
    @retval EFI_DEVICE_ERROR The device had an error and could not complete the request.
    @retval EFI_UNSUPPORTED  The output device is not in a valid text mode, or the
                             cursor position is invalid for the current mode.

**/
EFI_STATUS
EFIAPI
PeiSimpleTextOutSetCursorPosition (
    IN AMI_SIMPLE_TEXT_OUTPUT_PPI   *This, 
    IN UINTN                        Column,
    IN UINTN                        Row
)
{
    BOOLEAN                         CursorVisible;
    
    if (Column >= MAX_COLS || Row >= MAX_ROWS) {
        DEBUG ((DEBUG_ERROR, "%a : Row : %x Column : %x out of boundary!!!\n", __FUNCTION__, Row, Column));
        return EFI_INVALID_PARAMETER;
    }
    
    //save cursor status and hide it if necessary
    CursorVisible = This->Mode->CursorVisible;
    if (CursorVisible)
        This->EnableCursor(This, FALSE);
    
    AmiVideoSetCursorPosition((UINT8)Column, (UINT8)Row);
    
    This->Mode->CursorColumn = (INT32)Column;
    This->Mode->CursorRow    = (INT32)Row;

    //restore cursor at new position
    if (CursorVisible) {
        This->EnableCursor(This, TRUE);
    }
    
    return EFI_SUCCESS;
}


/**
    Clears the output device(s) display to the currently selected background 
    color.
      
    @param   This   The PPI instance pointer.

    @retval  EFI_SUCCESS      The operation completed successfully.
    @retval  EFI_DEVICE_ERROR The device had an error and could not complete the request.
    @retval  EFI_UNSUPPORTED  The output device is not in a valid text mode.

**/
EFI_STATUS
EFIAPI
PeiSimpleTextOutClearScreen (
    IN AMI_SIMPLE_TEXT_OUTPUT_PPI    *This
)
{
    EFI_STATUS  Status;
    
    Status = AmiVideoClear();
    if (EFI_ERROR(Status)) {
        return Status;
    }
    
    //Set cursor position X=0, Y=0
    Status = PeiSimpleTextOutSetCursorPosition (This, 0, 0);
    
    if (EFI_ERROR(Status)) { //on first invocation this failed because MaxRows = MaxCols = 0
        This->Mode->CursorColumn = 0;
        This->Mode->CursorRow    = 0;
    }
    
    return Status;
}

/**
    Write a string to the output device.

    @param  This   The PPI instance pointer.
    @param  String The NULL-terminated string to be displayed on the output
                   device(s). All output devices must also support the Unicode
                   drawing character codes defined in this file.

    @retval EFI_SUCCESS             The string was output to the device.
    @retval EFI_DEVICE_ERROR        The device reported an error while attempting to output
                                    the text.
    @retval EFI_UNSUPPORTED         The output device's mode is not currently in a
                                    defined text mode.
**/
EFI_STATUS
EFIAPI
PeiSimpleTextOutPutString (
    IN AMI_SIMPLE_TEXT_OUTPUT_PPI   *This,
    IN CHAR16                       *String
)    
{
    EFI_STATUS                      Status;
    CHAR8                           Text[0x400];   //1KB

    UnicodeStrToAsciiStrS (String, Text, sizeof(Text));

    Status = AmiVideoPrint((CONST CHAR8 *)Text);
    if (EFI_ERROR(Status)) {
        return Status;
    }
    
    AmiVideoGetCursorPosition (
                    (UINT8 *)&This->Mode->CursorRow, 
                    (UINT8 *)&This->Mode->CursorColumn);
    
    return Status;
}

/**
  Verifies that all characters in a string can be output to the 
  target device.

  @param  This   The PPI instance pointer.
  @param  String The NULL-terminated string to be examined for the output
                 device(s).

  @retval EFI_SUCCESS      The device(s) are capable of rendering the output string.
  @retval EFI_UNSUPPORTED  Some of the characters in the string cannot be
                           rendered by one or more of the output devices mapped
                           by the EFI handle.

**/
EFI_STATUS  
PeiSimpleTextOutTestString (
    IN AMI_SIMPLE_TEXT_OUTPUT_PPI       *This,
    IN CHAR16                           *String
)
{
    return EFI_SUCCESS;
}

/**
    Returns information for an available text mode that the output device(s)
    supports.

    @param  This       The PPI instance pointer.
    @param  ModeNumber The mode number to return information on.
    @param  Columns    Returns the geometry of the text output device for the
                       requested ModeNumber.
    @param  Rows       Returns the geometry of the text output device for the
                       requested ModeNumber.
                                          
    @retval EFI_SUCCESS      The requested mode information was returned.
    @retval EFI_DEVICE_ERROR The device had an error and could not complete the request.
    @retval EFI_UNSUPPORTED  The mode number was not valid.

**/
EFI_STATUS
EFIAPI
PeiSimpleTextOutQueryMode (
    IN AMI_SIMPLE_TEXT_OUTPUT_PPI   *This,
    IN UINTN                        ModeNumber,
    IN OUT UINTN                    *Columns,
    IN OUT UINTN                    *Rows
)
{
    
    AMI_VIDEO_TEXT_OUT_PRIVATE_DATA     *VideoTextOutPrivate;
    
    if (ModeNumber >= (UINTN)(This->Mode->MaxMode)) {
        return EFI_UNSUPPORTED;
    }

    // if the mode is a valid mode, return the data from the array of
    //for the height and width
    
    VideoTextOutPrivate = (AMI_VIDEO_TEXT_OUT_PRIVATE_DATA *)This;
    
    *Columns = VideoTextOutPrivate->MaxColumns;
    *Rows    = VideoTextOutPrivate->MaxRows;
    
    return EFI_SUCCESS;
}

/**
  Sets the output device(s) to a specified mode.

  @param  This       The PPI instance pointer.
  @param  ModeNumber The mode number to set.

  @retval EFI_SUCCESS      The requested text mode was set.
  @retval EFI_DEVICE_ERROR The device had an error and could not complete the request.
  @retval EFI_UNSUPPORTED  The mode number was not valid.

**/
EFI_STATUS
EFIAPI
PeiSimpleTextOutSetMode (
    IN AMI_SIMPLE_TEXT_OUTPUT_PPI   *This,
    IN UINTN                        ModeNumber
)
{
    if (ModeNumber >= (UINTN)(This->Mode->MaxMode)) {
        return EFI_UNSUPPORTED;
    }

    // if the mode is a valid mode, return EFI_SUCCESS as we are
    // supporting only Mode 0.
    
    return EFI_SUCCESS;
}

/**
    Sets the background and foreground colors for the OutputString () and
    ClearScreen () functions.

    @param  This        The PPI instance pointer.
    @param  Attribute   Attribute to set.
  

    @retval EFI_SUCCESS       The attribute was set.
    @retval EFI_DEVICE_ERROR  The device had an error and could not complete the request.
    @retval EFI_UNSUPPORTED   The attribute requested is not defined.

**/
EFI_STATUS
EFIAPI
PeiSimpleTextOutSetAttribute (
    IN AMI_SIMPLE_TEXT_OUTPUT_PPI       *This, 
    IN UINTN                            Attribute
)
{
    UINT8               Blink;
    
    if ((Attribute | 0xFF) != 0xFF) {
        DEBUG ((DEBUG_ERROR, "%a : Attribute : %x not supported!!!\n", __FUNCTION__, Attribute));
        return EFI_UNSUPPORTED;
    }
    
    //    Bits 0..3 are the foreground color, and
    //    bits 4..6 are the background color. All other bits are undefined
    //    and must be zero.
    
    Blink                 = This->Mode->CursorVisible;
    This->Mode->Attribute = (INT32)Attribute;

    return AmiVideoSetColor((UINT8)Attribute | (Blink << 7));
}

/**
    Makes the cursor visible or invisible

    @param  This    The PPI instance pointer.
    @param  Enable  If TRUE, the cursor is set to be visible. If FALSE, the cursor is
                    set to be invisible.

    @retval EFI_SUCCESS      The operation completed successfully.
    @retval EFI_DEVICE_ERROR The device had an error and could not complete the
                             request, or the device does not support changing
                             the cursor mode.
    @retval EFI_UNSUPPORTED  The output device is not in a valid text mode.

**/
EFI_STATUS
EFIAPI
PeiSimpleTextOutEnableCursor (
    IN AMI_SIMPLE_TEXT_OUTPUT_PPI   *This,
    IN BOOLEAN                      Enable
)
{
    
    This->Mode->CursorVisible = Enable;

    return AmiVideoSetColor((UINT8)This->Mode->Attribute | (Enable << 7));
}

/**
    After memory is discovered, Re-install the Pei VideoSimpleTextOut Ppi with updated
    VideoSimpleTextOut.

    @param[in] PeiServices          Describes the list of possible PEI
                                    Services.

    @return EFI_STATUS  Status 

**/
EFI_STATUS
EFIAPI
ReinstallVideoTextOutPpiAfterMemoryDiscovered (
        IN EFI_PEI_SERVICES           **PeiServices,
        IN EFI_PEI_NOTIFY_DESCRIPTOR  *NotifyDescriptor,
        IN VOID                       *Ppi
)
{
    EFI_STATUS                          Status;
    EFI_PEI_PPI_DESCRIPTOR              *VideoSimpleTextOutPpiDesc;
    AMI_SIMPLE_TEXT_OUTPUT_PPI          *SimpleTextOutPpi;
    AMI_VIDEO_TEXT_OUT_PRIVATE_DATA     *VideoTextOutPrivate;

    // Locate Pei Simple Text Out Ppi.
    Status = (*PeiServices)->LocatePpi (
                                (CONST EFI_PEI_SERVICES **)PeiServices,
                                &gAmiSimpleTextOutPpiGuid,
                                PcdGet16 (AmiPcdVideoSimpleTextOutPpiInstance),
                                &VideoSimpleTextOutPpiDesc,
                                (VOID **)&SimpleTextOutPpi);
    if (EFI_ERROR (Status)) {
        DEBUG ((DEBUG_ERROR, "%a: LocatePpi for gAmiSimpleTextOutPpiGuid Status: %r \n", __FUNCTION__, Status));
        return Status;
    }
    
    if (SimpleTextOutPpi->ConsoleType != PeiSimpleTextOutConsoleTypeVideoText) {
        return Status;
    }

    VideoTextOutPrivate = (AMI_VIDEO_TEXT_OUT_PRIVATE_DATA *)SimpleTextOutPpi;
    SimpleTextOutPpi->Reset             = PeiSimpleTextOutReset;
    SimpleTextOutPpi->OutputString      = PeiSimpleTextOutPutString;
    SimpleTextOutPpi->TestString        = PeiSimpleTextOutTestString;
    SimpleTextOutPpi->QueryMode         = PeiSimpleTextOutQueryMode;
    SimpleTextOutPpi->SetMode           = PeiSimpleTextOutSetMode;
    SimpleTextOutPpi->SetAttribute      = PeiSimpleTextOutSetAttribute;
    SimpleTextOutPpi->ClearScreen       = PeiSimpleTextOutClearScreen;
    SimpleTextOutPpi->SetCursorPosition = PeiSimpleTextOutSetCursorPosition;
    SimpleTextOutPpi->EnableCursor      = PeiSimpleTextOutEnableCursor;
    SimpleTextOutPpi->Mode              = &VideoTextOutPrivate->Mode;
    
    // Re-install the AMI_SIMPLE_TEXT_OUT_PPI
    Status = (*PeiServices)->ReInstallPpi (
                                (CONST EFI_PEI_SERVICES **)PeiServices,
                                VideoSimpleTextOutPpiDesc,
                                VideoSimpleTextOutPpiDesc );
    
    DEBUG ((DEBUG_INFO, "ReInstallPpi for VideoSimpleTextOutPpiDesc Status: %r \n", Status ));

    //Register End of PEI callback to hand off cursor position to DXE driver.
    Status = PeiServicesNotifyPpi (&mEndOfPeiSignalPpiNotifyList[0]);
    
    return Status;
}

/**
    Entry point for PEI Video Driver

    @param FileHandle  - Pointer to image file handle
    @param PeiServices - Pointer to the PEI Core data Structure

    @retval EFI_STATUS
**/

/**
    VideoNotify for PEI Video Driver

    @param  **PeiServices    - pointer to the PEI services.
    @param  NotifyDescriptor - pointer to descriptor
    @param  Ppi -            - VideoNotify PPI that was installed.
    
    @retval EFI_STATUS
**/

EFI_STATUS 
VideoNotify (
  IN EFI_PEI_SERVICES                   **PeiServices,
  IN EFI_PEI_NOTIFY_DESCRIPTOR          *NotifyDescriptor,
  IN VOID                               *Ppi
)
{
    AMI_VIDEO_TEXT_OUT_PRIVATE_DATA         *VideoTextOutPrivate;
    EFI_PEI_PPI_DESCRIPTOR                  *VideoSimpleTextOutPpiDesc;
    AMI_SIMPLE_TEXT_OUTPUT_PPI              *SimpleTextOutPpi = NULL;
    UINTN                                   Instance = 0;
    EFI_STATUS                              Status;
    BOOLEAN                                 MemoryDiscovered = TRUE;
    
    
    Status = (*PeiServices)->LocatePpi(
                                 (const EFI_PEI_SERVICES**)PeiServices,
                                 &gEfiPeiMemoryDiscoveredPpiGuid,
                                 0,
                                 NULL,
                                 NULL); 
    
    if (Status == EFI_NOT_FOUND) {
        Status = (*PeiServices)->NotifyPpi(
                                     (const EFI_PEI_SERVICES**)PeiServices,
                                     &gVideoTextOutMemoryInstalledNotifyList[0] );

        if(EFI_ERROR(Status)) {
            DEBUG((DEBUG_ERROR,"%a(): NotifyPpi for Memory Discovered Status:%r", __FUNCTION__, Status));
        }
       MemoryDiscovered = FALSE;
    }
    
    // Call the Video Controller Initialization 
    Status = AmiVideoInit(
                    (EFI_PEI_SERVICES**)PeiServices, 
                    EARLY_CONSOLE_TEXT_MODE); 

    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR,"%a(): AmiVideoInit Status:%r", __FUNCTION__, Status));
        return Status;
    }    

    Status = (*PeiServices)->AllocatePool( 
                                     (const EFI_PEI_SERVICES**)PeiServices,
                                     sizeof(AMI_VIDEO_TEXT_OUT_PRIVATE_DATA),
                                     (VOID**)&VideoTextOutPrivate );
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR,"%a(): AllocatePool VideoTextOutPrivate Status:%r", __FUNCTION__, Status));
        return Status;
    }

    Status = (*PeiServices)->AllocatePool( 
                                     (const EFI_PEI_SERVICES**)PeiServices,
                                     sizeof(EFI_PEI_PPI_DESCRIPTOR),
                                     (VOID**)&VideoSimpleTextOutPpiDesc);
    if (EFI_ERROR(Status)) { 
        FreePool(VideoTextOutPrivate);
        DEBUG((DEBUG_ERROR,"%a(): AllocatePool VideoTextOutPpi Status:%r", __FUNCTION__, Status));
        return Status;
    }
 
    // Produce the TextOut PPI for the Video Device
    VideoSimpleTextOutPpiDesc->Flags = EFI_PEI_PPI_DESCRIPTOR_PPI | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST;
    VideoSimpleTextOutPpiDesc->Guid  = &gAmiSimpleTextOutPpiGuid;
    VideoSimpleTextOutPpiDesc->Ppi   = &VideoTextOutPrivate->AmiPeiSimpleTextOut;

    VideoTextOutPrivate->AmiPeiSimpleTextOut.Reset             = PeiSimpleTextOutReset;
    VideoTextOutPrivate->AmiPeiSimpleTextOut.OutputString      = PeiSimpleTextOutPutString;
    VideoTextOutPrivate->AmiPeiSimpleTextOut.TestString        = PeiSimpleTextOutTestString;
    VideoTextOutPrivate->AmiPeiSimpleTextOut.QueryMode         = PeiSimpleTextOutQueryMode;
    VideoTextOutPrivate->AmiPeiSimpleTextOut.SetMode           = PeiSimpleTextOutSetMode;
    VideoTextOutPrivate->AmiPeiSimpleTextOut.SetAttribute      = PeiSimpleTextOutSetAttribute;
    VideoTextOutPrivate->AmiPeiSimpleTextOut.ClearScreen       = PeiSimpleTextOutClearScreen;
    VideoTextOutPrivate->AmiPeiSimpleTextOut.SetCursorPosition = PeiSimpleTextOutSetCursorPosition;
    VideoTextOutPrivate->AmiPeiSimpleTextOut.EnableCursor      = PeiSimpleTextOutEnableCursor;
    VideoTextOutPrivate->AmiPeiSimpleTextOut.ConsoleType       = PeiSimpleTextOutConsoleTypeVideoText;
    VideoTextOutPrivate->AmiPeiSimpleTextOut.Mode              = &VideoTextOutPrivate->Mode;
    VideoTextOutPrivate->AmiPeiSimpleTextOut.Mode->MaxMode     = 1;
    VideoTextOutPrivate->AmiPeiSimpleTextOut.Mode->Mode        = 0;
    VideoTextOutPrivate->AmiPeiSimpleTextOut.Mode->CursorVisible = FALSE;

    VideoTextOutPrivate->MaxColumns = MAX_COLS;
    VideoTextOutPrivate->MaxRows    = MAX_ROWS;
    
    // Clear Screen and Install PPI.
    PeiSimpleTextOutSetAttribute (
                    &VideoTextOutPrivate->AmiPeiSimpleTextOut, 
                    EFI_BLACK);
    
    PeiSimpleTextOutClearScreen  (
                    &VideoTextOutPrivate->AmiPeiSimpleTextOut);
    
    PeiSimpleTextOutSetAttribute (
                    &VideoTextOutPrivate->AmiPeiSimpleTextOut, 
                    EFI_BACKGROUND_BLACK | EFI_WHITE);
    
    PeiSimpleTextOutEnableCursor(
                    &VideoTextOutPrivate->AmiPeiSimpleTextOut, 
                    PcdGetBool(PcdDefaultCursorState));
    
    Status = (*PeiServices)->InstallPpi(
                                    (const EFI_PEI_SERVICES**)PeiServices,
                                    VideoSimpleTextOutPpiDesc);
    if (EFI_ERROR(Status)) {
        FreePool(VideoTextOutPrivate);
        FreePool(VideoSimpleTextOutPpiDesc);
        DEBUG((DEBUG_ERROR,"%a(): Failed to Install VideoSimpleTextOutPpi!!! Status:%r", __FUNCTION__, Status));
        return Status;
    }

    do {
        // Locate the Video TextOut PPI Index.
        Status = (*PeiServices)->LocatePpi(
                                     (const EFI_PEI_SERVICES**)PeiServices,
                                     &gAmiSimpleTextOutPpiGuid,
                                     Instance,
                                     NULL,
                                     (VOID**)&SimpleTextOutPpi); 

        if (!EFI_ERROR(Status) && (VideoSimpleTextOutPpiDesc->Ppi == SimpleTextOutPpi)) {
           PcdSet16S (AmiPcdVideoSimpleTextOutPpiInstance, (UINT16)Instance);
           break;
        }
        Instance++;
        
    } while(!EFI_ERROR(Status));
    
    if( MemoryDiscovered == TRUE)  {
        //Register End of PEI callback to hand off cursor position to DXE driver.
        Status = PeiServicesNotifyPpi (&mEndOfPeiSignalPpiNotifyList[0]);
    }
    return Status;
}

/**
    Entry point for PEI Video Driver

    @param FileHandle  - Pointer to image file handle
    @param PeiServices - Pointer to the PEI Core data Structure

    @retval EFI_STATUS
**/

EFI_STATUS
EFIAPI
PeiVideoSimpleTextOutEntryPoint (
    IN  EFI_PEI_FILE_HANDLE             FileHandle,
    IN  CONST EFI_PEI_SERVICES          **PeiServices
)
{

    EFI_STATUS                              Status;
    
    Status = (*PeiServices)->NotifyPpi(
                                 (const EFI_PEI_SERVICES**)PeiServices,
                                 gVideoNotifyList );

   DEBUG((DEBUG_ERROR,"%a(): NotifyPpi Status:%r", __FUNCTION__, Status));
    
    return Status;
}
