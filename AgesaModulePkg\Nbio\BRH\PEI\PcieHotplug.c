/*
******************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*
*/
/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include "AmdNbioPei.h"
#include <Filecode.h>
#include <MpioLib.h>

#define FILECODE NBIO_BRH_PEI_PCIEHOTPLUG_FILECODE

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U  R E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U  N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                     L O C A L   D A T A   D E F I N I T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------*/
/**
 * Callback to init hotplug features on all hotplug ports
 *
 *
 *
 *
 * @param[in]       Engine          Pointer to engine config descriptor
 * @param[in, out]  Buffer          Not used
 * @param[in]       Pcie            Pointer to global PCIe configuration
 *
 */

VOID
STATIC
PcieHotplugInitCallback (
  IN       PCIe_ENGINE_CONFIG    *Engine,
  IN OUT   VOID                  *Buffer,
  IN       PCIe_PLATFORM_CONFIG  *Pcie
  )
{
  PCIe_WRAPPER_CONFIG   *Wrapper;
  GNB_HANDLE            *GnbHandle;
  UINT32                Value;

  CONST EFI_PEI_SERVICES            **PeiServices;
  AMD_PEI_SOC_LOGICAL_ID_PPI        *SocLogicalIdPpi;
  SOC_LOGICAL_ID                    LogicalId;
  PEI_AMD_NBIO_PCIE_COMPLEX_PPI     *NbioPcieComplexPpi;
  DXIO_COMPLEX_DESCRIPTOR           *DxioTopologyData;
  DXIO_PORT_DESCRIPTOR              *TopologyEntry;
  UINT8                             PortParamIndex;


  if (Engine->Type.Port.PortData.LinkHotplug != HotplugDisabled ) {
    IDS_HDT_CONSOLE (GNB_TRACE, "Found Hotplug Engine at:\n");
    IDS_HDT_CONSOLE (GNB_TRACE, "  Port.PortId = %d\n", Engine->Type.Port.PortId);
    IDS_HDT_CONSOLE (GNB_TRACE, "  Port.PcieBridgeId = %d\n", Engine->Type.Port.PcieBridgeId);
    IDS_HDT_CONSOLE (GNB_TRACE, "  Port.Address = %x\n", Engine->Type.Port.Address);
    IDS_HDT_CONSOLE (GNB_TRACE, "  Type = ");

    PeiServices = GetPeiServicesTablePointer();

    // Get PCIe topology from platform BIOS
    (*PeiServices)->LocatePpi (
                               PeiServices,
                               &gAmdNbioPcieComplexPpiGuid,
                               0,
                               NULL,
                               (VOID**)&NbioPcieComplexPpi);

    // Get Logical CPU ID info
    (*PeiServices)->LocatePpi (
                               PeiServices,
                               &gAmdSocLogicalIdPpiGuid,
                               0,
                               NULL,
                               (VOID **)&SocLogicalIdPpi
                               );
    SocLogicalIdPpi->GetLogicalIdOnCurrentCore (&LogicalId);

    Wrapper = PcieConfigGetParentWrapper (Engine);
    GnbHandle = (GNB_HANDLE *) PcieConfigGetParentSilicon (Engine);

    //devCfg:NB_PCIE_SLOT_CAP.HOTPLUG_CAPABLE = 1h;
    GnbLibPciRmw (
      (GnbHandle->Address.AddressValue |
       MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber, PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_SLOT_CAP_ADDRESS))),
      AccessWidth32,
      (UINT32) ~SLOT_CAP_HOTPLUG_CAPABLE_MASK,
      (UINT32) (1 << SLOT_CAP_HOTPLUG_CAPABLE_OFFSET),
      NULL
      );

    GnbLibPciRmw (
      (GnbHandle->Address.AddressValue |
      MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber, PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_SLOT_CAP2_ADDRESS))),
      AccessWidth32,
      (UINT32) ~(SLOT_CAP2_INBAND_PD_DISABLE_SUPPORTED_MASK),
      (UINT32) (1 << SLOT_CAP2_INBAND_PD_DISABLE_SUPPORTED_OFFSET),
      NULL
      );

    GnbLibPciRmw (
      (GnbHandle->Address.AddressValue |
       MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber, PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_SLOT_CNTL_ADDRESS))),
       AccessWidth16,
       (UINT32) ~(SLOT_CNTL_INBAND_PD_DISABLE_MASK),
       ((PcdGetBool(PcdAmdDisableInbandPDSupport) ? 1 : 0) << SLOT_CNTL_INBAND_PD_DISABLE_OFFSET),
       0
      );

    SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                   GnbHandle->Address.Address.Bus,
                   NBIO_SPACE (GnbHandle, SMN_IOMMU0IOAGRNBIO0_L1_MISC_CNTRL_1_ADDRESS),
                   (UINT32) ~(L1_MISC_CNTRL_1_REG_force_OrderStreamID_func_MASK),
                   1 << L1_MISC_CNTRL_1_REG_force_OrderStreamID_func_OFFSET,
                   0
                   );
    SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                   GnbHandle->Address.Address.Bus,
                   NBIO_SPACE (GnbHandle, SMN_IOMMU0PCIE0NBIO0_L1_MISC_CNTRL_1_ADDRESS),
                   (UINT32) ~(L1_MISC_CNTRL_1_REG_force_OrderStreamID_func_MASK),
                   1 << L1_MISC_CNTRL_1_REG_force_OrderStreamID_func_OFFSET,
                   0
                   );
    SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                   GnbHandle->Address.Address.Bus,
                   NBIO_SPACE (GnbHandle, SMN_IOMMU0PCIE1NBIO0_L1_MISC_CNTRL_1_ADDRESS),
                   (UINT32) ~(L1_MISC_CNTRL_1_REG_force_OrderStreamID_func_MASK),
                   1 << L1_MISC_CNTRL_1_REG_force_OrderStreamID_func_OFFSET,
                   0
                   );

    if ((Value = PcdGet8 (PcdAmdPresenceDetectSelectMode)) != 0xFF) {
      SmnPrivateRegRMW (GnbHandle,
                       WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_PRESENCE_DETECT_SELECT_ADDRESS),
                       (UINT32) ~(PCIE_PRESENCE_DETECT_SELECT_PRESENCE_DETECT_SELECT_MASK),
                       (Value & (PCIE_PRESENCE_DETECT_SELECT_PRESENCE_DETECT_SELECT_MASK >>
                       PCIE_PRESENCE_DETECT_SELECT_PRESENCE_DETECT_SELECT_OFFSET)) << PCIE_PRESENCE_DETECT_SELECT_PRESENCE_DETECT_SELECT_OFFSET,
                       0
                      );
    }

    if (PcdGet8 (PcdAmdHotPlugHandlingMode) != 5) {
        if (PcdGetBool(PcdAmdHotPlugPDSettle) && (Engine->Type.Port.PortData.LinkHotplug != DxioHotplugServerExpress)) {
            IDS_HDT_CONSOLE (GNB_TRACE, "Setting Blocking Bits.");
            SmnPrivateRegRMW (GnbHandle,
                       PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIEP_HPGI_ADDRESS),
                       (UINT32) ~((PCIEP_HPGI_REG_HPGI_ASSERT_TO_SMI_EN_MASK) |
                                  (PCIEP_HPGI_REG_HPGI_DEASSERT_TO_SMI_EN_MASK) |
                                  (PCIEP_HPGI_HPGI_BLOCK_PD_INT_MASK) |
                                  (PCIEP_HPGI_REG_HPGI_HOOK_MASK) |
                                  (PCIEP_HPGI_REG_HPGI_PRESENCE_DETECT_STATE_CHANGE_EN_MASK)),
                       ((0 << PCIEP_HPGI_REG_HPGI_ASSERT_TO_SMI_EN_OFFSET) |
                        (0 << PCIEP_HPGI_REG_HPGI_DEASSERT_TO_SMI_EN_OFFSET) |
                        (1 << PCIEP_HPGI_HPGI_BLOCK_PD_INT_OFFSET) |
                        (1 << PCIEP_HPGI_REG_HPGI_HOOK_OFFSET) |
                        (1 << PCIEP_HPGI_REG_HPGI_PRESENCE_DETECT_STATE_CHANGE_EN_OFFSET)),
                         0
                       );
            }
    } else {
          SmnPrivateRegRMW (GnbHandle,
                       PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIEP_HPGI_ADDRESS),
                       (UINT32) ~((PCIEP_HPGI_REG_HPGI_ASSERT_TO_SMI_EN_MASK) |
                                  (PCIEP_HPGI_REG_HPGI_DEASSERT_TO_SMI_EN_MASK) |
                                  (PCIEP_HPGI_HPGI_BLOCK_PD_INT_MASK) |
                                  (PCIEP_HPGI_REG_HPGI_HOOK_MASK) |
                                  (PCIEP_HPGI_REG_HPGI_PRESENCE_DETECT_STATE_CHANGE_EN_MASK)),
                       ((0 << PCIEP_HPGI_REG_HPGI_ASSERT_TO_SMI_EN_OFFSET) |
                        (0 << PCIEP_HPGI_REG_HPGI_DEASSERT_TO_SMI_EN_OFFSET) |
                        (1 << PCIEP_HPGI_HPGI_BLOCK_PD_INT_OFFSET) |
                        (0 << PCIEP_HPGI_REG_HPGI_HOOK_OFFSET) |
                        (0 << PCIEP_HPGI_REG_HPGI_PRESENCE_DETECT_STATE_CHANGE_EN_OFFSET)),
                         0
                       );
    }

    SmnPrivateRegRMW (GnbHandle,
                     PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_CNTL5_ADDRESS),
                     (UINT32) ~(PCIE_LC_CNTL5_LC_WAIT_IN_DETECT_MASK),
                     0 << PCIE_LC_CNTL5_LC_WAIT_IN_DETECT_OFFSET,
                     0
                    );

    SmnPrivateRegRMW (GnbHandle,
                     WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_RCB_CNTL_ADDRESS),
                     (UINT32) ~(PCIE_RCB_CNTL_RX_RCB_RC_CTO_TO_SC_IN_LINK_DOWN_EN_MASK |
                                PCIE_RCB_CNTL_RX_RCB_RC_CTO_IGNORE_ERR_IN_LINK_DOWN_EN_MASK),
                     (UINT32) (1 << PCIE_RCB_CNTL_RX_RCB_RC_CTO_TO_SC_IN_LINK_DOWN_EN_OFFSET) |
                             (1 << PCIE_RCB_CNTL_RX_RCB_RC_CTO_IGNORE_ERR_IN_LINK_DOWN_EN_OFFSET),
                     0
                    );

    // Firmware first mode / Firmware first but allow OS First mode
    if (PcdGet8 (PcdAmdHotPlugHandlingMode) == 3 || PcdGet8 (PcdAmdHotPlugHandlingMode) == 6) {
        // Enable SW SMI
        if (GnbHandle->RBIndex < 4) {
          SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                           GnbHandle->Address.Address.Bus,
                           NBIO_SPACE(GnbHandle, SMN_IOHUB0NBIO0_MISC_RAS_CONTROL_ADDRESS),
                           (UINT32)~((MISC_RAS_CONTROL_PCIe_SMI_En_MASK) |
                                     (MISC_RAS_CONTROL_SW_SMI_En_MASK)),
                           ((1 << MISC_RAS_CONTROL_PCIe_SMI_En_OFFSET) |
                            (1 << MISC_RAS_CONTROL_SW_SMI_En_OFFSET)),
                            0
                          );
        } else {
          SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                           GnbHandle->Address.Address.Bus,
                           NBIO_SPACE(GnbHandle, SMN_IOHUB1NBIO0_MISC_RAS_CONTROL_ADDRESS),
                           (UINT32)~((MISC_RAS_CONTROL_PCIe_SMI_En_MASK) |
                                     (MISC_RAS_CONTROL_SW_SMI_En_MASK)),
                           ((1 << MISC_RAS_CONTROL_PCIe_SMI_En_OFFSET) |
                            (1 << MISC_RAS_CONTROL_SW_SMI_En_OFFSET)),
                            0
                          );
        }

        // Configure eDPC
        SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                         GnbHandle->Address.Address.Bus,
                         PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_DPC_CNTL_ADDRESS),
                         (UINT32)~((PCIE_DPC_CNTL_DL_ACTIVE_ERR_COR_ENABLE_MASK << 16) |
                                   (PCIE_DPC_CNTL_DPC_TRIGGER_ENABLE_MASK << 16) |
                                   (PCIE_DPC_CNTL_DPC_ERR_COR_ENABLE_MASK << 16)),
                         ((0 << (PCIE_DPC_CNTL_DL_ACTIVE_ERR_COR_ENABLE_OFFSET + 16)) |
                          ((PcdGet8(PcdAmdEdpcEnable) == 1) ? (1 << (PCIE_DPC_CNTL_DPC_TRIGGER_ENABLE_OFFSET + 16)) : 0) |
                          (0 << (PCIE_DPC_CNTL_DPC_ERR_COR_ENABLE_OFFSET + 16))),
                          0
                        );

        SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                         GnbHandle->Address.Address.Bus,
                         PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_RP_PIO_MASK_ADDRESS),
                         (UINT32)~((PCIE_RP_PIO_STATUS_CFG_CTO_MASK) |
                                   (PCIE_RP_PIO_STATUS_CFG_CA_CPL_MASK) |
                                   (PCIE_RP_PIO_STATUS_CFG_UR_CPL_MASK) |
                                   (PCIE_RP_PIO_STATUS_MEM_CTO_MASK) |
                                   (PCIE_RP_PIO_STATUS_IO_CTO_MASK)),
                         ((1 << PCIE_RP_PIO_STATUS_CFG_CTO_OFFSET) |
                          (1 << PCIE_RP_PIO_STATUS_CFG_CA_CPL_OFFSET) |
                          (1 << PCIE_RP_PIO_STATUS_CFG_UR_CPL_OFFSET) |
                          (1 << PCIE_RP_PIO_STATUS_MEM_CTO_OFFSET) |
                          (1 << PCIE_RP_PIO_STATUS_IO_CTO_OFFSET)),
                          0
                        );

        SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                         GnbHandle->Address.Address.Bus,
                         PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_RP_PIO_SEVERITY_ADDRESS),
                         (UINT32)~(-1), (UINT32) (-1), 0
                        );
    } else if (PcdGet8 (PcdAmdHotPlugHandlingMode) == 5) {
      // Enable SW SMI
      if (GnbHandle->RBIndex < 4) {
        SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                         GnbHandle->Address.Address.Bus,
                         NBIO_SPACE(GnbHandle, SMN_IOHUB0NBIO0_MISC_RAS_CONTROL_ADDRESS),
                         (UINT32)~(MISC_RAS_CONTROL_SW_SMI_En_MASK),
                         (1 << MISC_RAS_CONTROL_SW_SMI_En_OFFSET),
                          0
                        );
      } else {
        SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                         GnbHandle->Address.Address.Bus,
                         NBIO_SPACE(GnbHandle, SMN_IOHUB1NBIO0_MISC_RAS_CONTROL_ADDRESS),
                         (UINT32)~(MISC_RAS_CONTROL_SW_SMI_En_MASK),
                         (1 << MISC_RAS_CONTROL_SW_SMI_En_OFFSET),
                          0
                        );
      }
      // SFI mode - configure any SFI settings required for all hot plug modes
      // (SLOT_CNTL: PRESENCE_DETECT_CHANGED_EN = 1)
          GnbLibPciRmw ((GnbHandle->Address.AddressValue |
                        MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber, PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_SLOT_CNTL_ADDRESS))),
                        AccessWidth16,
        (UINT32) ~(SLOT_CNTL_PRESENCE_DETECT_CHANGED_EN_MASK),
        (1 << SLOT_CNTL_PRESENCE_DETECT_CHANGED_EN_OFFSET),
         0
         );
      // (PCIERCCFG::SFI_CNTL: SFI_PD_STATE_MASK = 1)
      // (PCIERCCFG::SFI_CNTL: SFI_DLL_STATE_MASK = 1)
      // (PCIERCCFG::SFI_CNTL: SFI_OOB_PD_CHANGED_EN = 1)
      // (PCIERCCFG::SFI_CNTL: SFI_DLL_STATE_CHANGED_EN = 0)
      // (PCIERCCFG::SFI_CNTL: SFI_DRS_MASK = 1)
      // (PCIERCCFG::SFI_CNTL: SFI_DRS_SIGNALING_EN = 1)
          GnbLibPciRmw ((GnbHandle->Address.AddressValue |
                        MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber, PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_SFI_CNTL_ADDRESS))),
                        AccessWidth16,
        (UINT32) ~((SFI_CNTL_SFI_OOB_PD_CHANGED_EN_MASK) |
                   (SFI_CNTL_SFI_DLL_STATE_CHANGED_EN_MASK) |
                   (SFI_CNTL_SFI_DRS_SIGNALING_EN_MASK) |
                   (SFI_CNTL_SFI_HPS_SUPPRESS_MASK)),
             (0 << (SFI_CNTL_SFI_OOB_PD_CHANGED_EN_OFFSET)) |
             (1 << (SFI_CNTL_SFI_DLL_STATE_CHANGED_EN_OFFSET)) |
             (1 << (SFI_CNTL_SFI_DRS_SIGNALING_EN_OFFSET)) |
             (1 << (SFI_CNTL_SFI_HPS_SUPPRESS_OFFSET)),
              0
         );
    }

    if (GnbHandle->RBIndex < 4) {
      SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                       GnbHandle->Address.Address.Bus,
                       NBIO_SPACE(GnbHandle, SMN_IOHUB0NBIO0_MISC_RAS_CONTROL_ADDRESS),
                       (UINT32)~((MISC_RAS_CONTROL_PCIe_SMI_En_MASK)),
                       ((1 << MISC_RAS_CONTROL_PCIe_SMI_En_OFFSET)),
                        0
                      );
    } else {
      SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                       GnbHandle->Address.Address.Bus,
                       NBIO_SPACE(GnbHandle, SMN_IOHUB0NBIO1_MISC_RAS_CONTROL_ADDRESS),
                       (UINT32)~((MISC_RAS_CONTROL_PCIe_SMI_En_MASK)),
                       ((1 << MISC_RAS_CONTROL_PCIe_SMI_En_OFFSET)),
                        0
                      );
    }

    // Sets the bit so that port does not go into loopback mode
    SmnPrivateRegRMW (GnbHandle,
                      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_TRAINING_CNTL_ADDRESS),
                      (UINT32) ~(PCIE_LC_TRAINING_CNTL_LC_DISABLE_TRAINING_BIT_ARCH_MASK),
                      1 << PCIE_LC_TRAINING_CNTL_LC_DISABLE_TRAINING_BIT_ARCH_OFFSET,
                      0
                     );

    // Sets the bit so that port does not go into loopback mode
    SmnPrivateRegRMW (GnbHandle,
                      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIEP_STRAP_LC_ADDRESS),
                      (UINT32) ~(PCIEP_STRAP_LC_STRAP_COMPLIANCE_DIS_MASK),
                      1 << PCIEP_STRAP_LC_STRAP_COMPLIANCE_DIS_OFFSET,
                      0
                     );

    //
    // Type specific hotplug configuration
    //
    switch (Engine->Type.Port.PortData.LinkHotplug) {
    //
    // Basic Hotplug Configuration
    //
    case DxioHotplugBasic:
      IDS_HDT_CONSOLE (GNB_TRACE, "DxioHotplugBasic\n");
          GnbLibPciRmw ((GnbHandle->Address.AddressValue |
                        MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber, PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_SLOT_CNTL_ADDRESS))),
                        AccessWidth16,
                       (UINT32) ~(SLOT_CNTL_HOTPLUG_INTR_EN_MASK),
                       (1 << SLOT_CNTL_HOTPLUG_INTR_EN_OFFSET),
                       0
                      );

      // devCfg:PCIEIND_P: PCIEP PCIEP_PORT_CNTL.NATIVE_PME_EN = !legacyHotPlug;
      SmnPrivateRegRMW (GnbHandle,
                        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIEP_PORT_CNTL_ADDRESS),
                        (UINT32) ~(PCIEP_PORT_CNTL_NATIVE_PME_EN_MASK | PCIEP_PORT_CNTL_HOTPLUG_MSG_EN_MASK),
                        (0 << PCIEP_PORT_CNTL_NATIVE_PME_EN_OFFSET) | (1 << PCIEP_PORT_CNTL_HOTPLUG_MSG_EN_OFFSET),
                        0
                       );
      SmnRegisterReadS (GnbHandle->Address.Address.Segment,
                        GnbHandle->Address.Address.Bus,
                        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_LINK_CNTL_ADDRESS),
                        &Value
                       );
      if ((Value & (UINT32) (1 << (LINK_STATUS_DL_ACTIVE_OFFSET + 16))) == 0) {
        if (GnbHandle->RBIndex < 4) {
          SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                           GnbHandle->Address.Address.Bus,
                           IOHC_BRIDGE_SPACE (GnbHandle, Engine, SMN_PCIE0DEVINDCFG0_IOHUB0NBIO0_IOHC_Bridge_CNTL_ADDRESS),
                           (UINT32)~(IOHC_Bridge_CNTL_BridgeDis_MASK),
                           ((PcdGetBool(PcdAmdHotPlugDisBridgeDis) ? 0 : 1) << IOHC_Bridge_CNTL_BridgeDis_OFFSET),
                           0
                          );
          IDS_HDT_CONSOLE (GNB_TRACE, "No ep - BridgeDis: %08x\n", IOHC_BRIDGE_SPACE (GnbHandle, Engine, SMN_PCIE0DEVINDCFG0_IOHUB0NBIO0_IOHC_Bridge_CNTL_ADDRESS));
        } else {
          SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                           GnbHandle->Address.Address.Bus,
                           IOHC_BRIDGE_SPACE (GnbHandle, Engine, SMN_PCIE2DEVINDCFG0_IOHUB1NBIO0_IOHC_Bridge_CNTL_ADDRESS),
                           (UINT32)~(IOHC_Bridge_CNTL_BridgeDis_MASK),
                           ((PcdGetBool(PcdAmdHotPlugDisBridgeDis) ? 0 : 1) << IOHC_Bridge_CNTL_BridgeDis_OFFSET),
                           0
                          );
          IDS_HDT_CONSOLE (GNB_TRACE, "No ep - BridgeDis: %08x\n", IOHC_BRIDGE_SPACE (GnbHandle, Engine, SMN_PCIE2DEVINDCFG0_IOHUB1NBIO0_IOHC_Bridge_CNTL_ADDRESS));
        }

        Value = (UINT8) PcdGet8 (PcdAmdHotPlugNVMEDefaultMaxPayload);
        if (Value != 0xFF) {
          GnbLibPciRmw ((GnbHandle->Address.AddressValue |
                        MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber, PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_DEVICE_CNTL_ADDRESS))),
                        AccessWidth16,
                           (UINT32) ~(DEVICE_CNTL_MAX_PAYLOAD_SIZE_MASK),
                           (Value & 0x7) << DEVICE_CNTL_MAX_PAYLOAD_SIZE_OFFSET,
                           0
                          );
                        }
      }
      break;

    //
    // Enhanced Hotplug Configuration
    //
    case DxioHotplugEnhanced:
      IDS_HDT_CONSOLE (GNB_TRACE, "DxioHotplugEnhanced\n");
          GnbLibPciRmw ((GnbHandle->Address.AddressValue |
                        MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber, PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_SLOT_CNTL_ADDRESS))),
                        AccessWidth16,
          (UINT32) ~(SLOT_CNTL_HOTPLUG_INTR_EN_MASK |
                     SLOT_CNTL_PRESENCE_DETECT_CHANGED_EN_MASK |
                     SLOT_CNTL_DL_STATE_CHANGED_EN_MASK),
                     ((1 << SLOT_CNTL_HOTPLUG_INTR_EN_OFFSET) |
                      (1 << SLOT_CNTL_PRESENCE_DETECT_CHANGED_EN_OFFSET) |
                      (1 << SLOT_CNTL_DL_STATE_CHANGED_EN_OFFSET) |
                      (1 << (SLOT_STATUS_ATTN_BUTTON_PRESSED_OFFSET + 16)) |
                      (1 << (SLOT_STATUS_PWR_FAULT_DETECTED_OFFSET + 16)) |
                      (1 << (SLOT_STATUS_MRL_SENSOR_CHANGED_OFFSET + 16)) |
                      (1 << (SLOT_STATUS_PRESENCE_DETECT_CHANGED_OFFSET + 16)) |
                      (1 << (SLOT_STATUS_COMMAND_COMPLETED_OFFSET + 16)) |
                      (1 << (SLOT_STATUS_DL_STATE_CHANGED_OFFSET + 16))),
                       0
                     );

      // devCfg:PCIEIND_P: PCIEP PCIEP_PORT_CNTL.NATIVE_PME_EN = !legacyHotPlug;
      SmnPrivateRegRMW (GnbHandle,
                       PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIEP_PORT_CNTL_ADDRESS),
                       (UINT32) ~(PCIEP_PORT_CNTL_NATIVE_PME_EN_MASK | PCIEP_PORT_CNTL_HOTPLUG_MSG_EN_MASK),
                       (1 << PCIEP_PORT_CNTL_NATIVE_PME_EN_OFFSET) | (1 << PCIEP_PORT_CNTL_HOTPLUG_MSG_EN_OFFSET),
                       0
                      );

      SmnPrivateRegRMW (GnbHandle,
                       PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_CNTL3_ADDRESS),
                       (UINT32) ~(PCIE_LC_CNTL3_LC_ENHANCED_HOT_PLUG_EN_MASK |
                                  PCIE_LC_CNTL3_LC_RCVR_DET_EN_OVERRIDE_MASK),
                       ((1 << PCIE_LC_CNTL3_LC_ENHANCED_HOT_PLUG_EN_OFFSET) |
                        (0 << PCIE_LC_CNTL3_LC_RCVR_DET_EN_OVERRIDE_OFFSET)),
                       0
                      );

      GnbLibPciRead (GnbHandle->Address.AddressValue | MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber,
                     PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_SLOT_CNTL_ADDRESS)),
                      AccessWidth32,
                      &Value, NULL);
      IDS_HDT_CONSOLE (GNB_TRACE, "Value = 0x%x\n", Value);
      SmnPrivateRegRead (GnbHandle,
                        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_CNTL3_ADDRESS),
                         &Value);
      IDS_HDT_CONSOLE (GNB_TRACE, "Value = 0x%x\n", Value);

      SmnPrivateRegRMW (GnbHandle,
                       WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_CNTL_ADDRESS),
                       (UINT32) ~(PCIE_CNTL_LC_HOT_PLUG_DELAY_SEL_MASK),
                       0x5 << PCIE_CNTL_LC_HOT_PLUG_DELAY_SEL_OFFSET,
                       0
                      );

      SmnPrivateRegRMW (GnbHandle,
                       WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIE0NBIO0_CPM_CONTROL_ADDRESS),
                       (UINT32) ~(CPM_CONTROL_RCVR_DET_CLK_ENABLE_MASK),
                       0x1 << CPM_CONTROL_RCVR_DET_CLK_ENABLE_OFFSET,
                       0
                      );

      SmnRegisterReadS (GnbHandle->Address.Address.Segment,
                        GnbHandle->Address.Address.Bus,
                        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_LINK_CNTL_ADDRESS),
                        &Value
                       );
      if ((Value & (UINT32) (1 << (LINK_STATUS_DL_ACTIVE_OFFSET + 16))) == 0) {
        if (GnbHandle->RBIndex < 4) {
          SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                           GnbHandle->Address.Address.Bus,
                           IOHC_BRIDGE_SPACE (GnbHandle, Engine, SMN_PCIE0DEVINDCFG0_IOHUB0NBIO0_IOHC_Bridge_CNTL_ADDRESS),
                           (UINT32)~(IOHC_Bridge_CNTL_BridgeDis_MASK),
                           ((PcdGetBool(PcdAmdHotPlugDisBridgeDis) ? 0 : 1) << IOHC_Bridge_CNTL_BridgeDis_OFFSET),
                           0
                          );
          IDS_HDT_CONSOLE (GNB_TRACE, "No ep - BridgeDis: %08x\n", IOHC_BRIDGE_SPACE (GnbHandle, Engine, SMN_PCIE0DEVINDCFG0_IOHUB0NBIO0_IOHC_Bridge_CNTL_ADDRESS));
        } else {
          SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                           GnbHandle->Address.Address.Bus,
                           IOHC_BRIDGE_SPACE (GnbHandle, Engine, SMN_PCIE2DEVINDCFG0_IOHUB1NBIO0_IOHC_Bridge_CNTL_ADDRESS),
                           (UINT32)~(IOHC_Bridge_CNTL_BridgeDis_MASK),
                           ((PcdGetBool(PcdAmdHotPlugDisBridgeDis) ? 0 : 1) << IOHC_Bridge_CNTL_BridgeDis_OFFSET),
                           0
                          );
          IDS_HDT_CONSOLE (GNB_TRACE, "No ep - BridgeDis: %08x\n", IOHC_BRIDGE_SPACE (GnbHandle, Engine, SMN_PCIE2DEVINDCFG0_IOHUB1NBIO0_IOHC_Bridge_CNTL_ADDRESS));
        }
      } else {
        if (GnbHandle->RBIndex < 4) {
          SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                           GnbHandle->Address.Address.Bus,
                           IOHC_BRIDGE_SPACE (GnbHandle, Engine, SMN_PCIE0DEVINDCFG0_IOHUB0NBIO0_IOHC_Bridge_CNTL_ADDRESS),
                           (UINT32)~(IOHC_Bridge_CNTL_BridgeDis_MASK),
                           0,
                           0
                          );
          IDS_HDT_CONSOLE (GNB_TRACE, "Found ep - BridgeDis: %08x\n", IOHC_BRIDGE_SPACE (GnbHandle, Engine, SMN_PCIE0DEVINDCFG0_IOHUB0NBIO0_IOHC_Bridge_CNTL_ADDRESS));
        } else {
          SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                           GnbHandle->Address.Address.Bus,
                           IOHC_BRIDGE_SPACE (GnbHandle, Engine, SMN_PCIE2DEVINDCFG0_IOHUB1NBIO0_IOHC_Bridge_CNTL_ADDRESS),
                           (UINT32)~(IOHC_Bridge_CNTL_BridgeDis_MASK),
                           0,
                           0
                          );
          IDS_HDT_CONSOLE (GNB_TRACE, "Found ep - BridgeDis: %08x\n", IOHC_BRIDGE_SPACE (GnbHandle, Engine, SMN_PCIE2DEVINDCFG0_IOHUB1NBIO0_IOHC_Bridge_CNTL_ADDRESS));
        }
      }

      break;
    //
    // Inboard Hotplug Configuration
    // Inboard Hotplug is similar to Enhanced Hotplug, except that the device must be present at boot
    //
    case DxioHotplugInboard:
      IDS_HDT_CONSOLE (GNB_TRACE, "DxioHotplugInboard\n");
      if (Engine->InitStatus == INIT_STATUS_PCIE_TRAINING_SUCCESS) {
        // devCfg:PCIEIND_P: PCIEP PCIEP_PORT_CNTL.NATIVE_PME_EN = !legacyHotPlug;
        SmnPrivateRegRMW (GnbHandle,
                         PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIEP_PORT_CNTL_ADDRESS),
                         (UINT32) ~(PCIEP_PORT_CNTL_NATIVE_PME_EN_MASK),
                         1 << PCIEP_PORT_CNTL_NATIVE_PME_EN_OFFSET,
                         0
                        );

        //*NBIO_TODO : Some of these defines do not exist in BA PPR

        SmnPrivateRegRMW (GnbHandle,
                          PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_CNTL3_ADDRESS),
                          (UINT32) ~(PCIE_LC_CNTL3_LC_ENHANCED_HOT_PLUG_EN_MASK |
                                     PCIE_LC_CNTL3_LC_RCVR_DET_EN_OVERRIDE_MASK),
                          ((1 << PCIE_LC_CNTL3_LC_ENHANCED_HOT_PLUG_EN_OFFSET) |
                           (0 << PCIE_LC_CNTL3_LC_RCVR_DET_EN_OVERRIDE_OFFSET)),
                          0
                         );

        GnbLibPciRead (GnbHandle->Address.AddressValue | MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber,
                        PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_SLOT_CAP_ADDRESS)),
                        AccessWidth32,
                        &Value,                        NULL
                        );
        IDS_HDT_CONSOLE (GNB_TRACE, "Value = 0x%x\n", Value);
        SmnPrivateRegRead (GnbHandle,
                          PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_CNTL3_ADDRESS),
                          &Value
                         );
        IDS_HDT_CONSOLE (GNB_TRACE, "Value = 0x%x\n", Value);

        SmnPrivateRegRMW (GnbHandle,
                         WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_CNTL_ADDRESS),
                         (UINT32) ~(PCIE_CNTL_LC_HOT_PLUG_DELAY_SEL_MASK),
                         0x5 << PCIE_CNTL_LC_HOT_PLUG_DELAY_SEL_OFFSET,
                         0
                        );
        //*NBIO_TODO : Some of these defines do not exist in BA PPR
        /*
        SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                         GnbHandle->Address.Address.Bus,
                         WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIE0_IOHUB0NBIO0_CPM_CONTROL_ADDRESS),
                         (UINT32) ~(CPM_CONTROL_RCVR_DET_CLK_ENABLE_MASK),
                         0x1 << CPM_CONTROL_RCVR_DET_CLK_ENABLE_OFFSET,
                         0
                         );
        */
      }
      break;
    case DxioHotplugServerExpress:
      IDS_HDT_CONSOLE (GNB_TRACE, "DxioHotplugServerExpress\n");
      //Express Module
      if (PcdGet8 (PcdAmdHotplugPortReset) == 1) {
        GnbLibPciRmw (
          (GnbHandle->Address.AddressValue |
           MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber, PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_SLOT_CAP_ADDRESS))),
          AccessWidth32,
          (UINT32) ~(SLOT_CAP_ATTN_BUTTON_PRESENT_MASK |
                     SLOT_CAP_PWR_CONTROLLER_PRESENT_MASK |
                     SLOT_CAP_MRL_SENSOR_PRESENT_MASK |
                     SLOT_CAP_ATTN_INDICATOR_PRESENT_MASK |
                     SLOT_CAP_PWR_INDICATOR_PRESENT_MASK |
                     SLOT_CAP_HOTPLUG_SURPRISE_MASK |
                     SLOT_CAP_ELECTROMECH_INTERLOCK_PRESENT_MASK |
                     SLOT_CAP_NO_COMMAND_COMPLETED_SUPPORTED_MASK),
          (UINT32) ((0 << SLOT_CAP_ATTN_BUTTON_PRESENT_OFFSET) |
                    (1 << SLOT_CAP_PWR_CONTROLLER_PRESENT_OFFSET) |
                    (0 << SLOT_CAP_MRL_SENSOR_PRESENT_OFFSET) |
                    (1 << SLOT_CAP_ATTN_INDICATOR_PRESENT_OFFSET) |
                    (1 << SLOT_CAP_PWR_INDICATOR_PRESENT_OFFSET) |
                    (1 << SLOT_CAP_HOTPLUG_SURPRISE_OFFSET) |
                    (1 << SLOT_CAP_ELECTROMECH_INTERLOCK_PRESENT_OFFSET) |
                    (0 << SLOT_CAP_NO_COMMAND_COMPLETED_SUPPORTED_OFFSET)),
          NULL
          );
      } else {
        GnbLibPciRmw (
          (GnbHandle->Address.AddressValue |
           MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber, PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_SLOT_CAP_ADDRESS))),
          AccessWidth32,
          (UINT32) ~(SLOT_CAP_ATTN_BUTTON_PRESENT_MASK |
                     SLOT_CAP_PWR_CONTROLLER_PRESENT_MASK |
                     SLOT_CAP_MRL_SENSOR_PRESENT_MASK |
                     SLOT_CAP_ATTN_INDICATOR_PRESENT_MASK |
                     SLOT_CAP_PWR_INDICATOR_PRESENT_MASK |
                     SLOT_CAP_HOTPLUG_SURPRISE_MASK |
                     SLOT_CAP_ELECTROMECH_INTERLOCK_PRESENT_MASK |
                     SLOT_CAP_NO_COMMAND_COMPLETED_SUPPORTED_MASK),
          (UINT32) ((1 << SLOT_CAP_ATTN_BUTTON_PRESENT_OFFSET) |
                    (1 << SLOT_CAP_PWR_CONTROLLER_PRESENT_OFFSET) |
                    (0 << SLOT_CAP_MRL_SENSOR_PRESENT_OFFSET) |
                    (1 << SLOT_CAP_ATTN_INDICATOR_PRESENT_OFFSET) |
                    (1 << SLOT_CAP_PWR_INDICATOR_PRESENT_OFFSET) |
                    (0 << SLOT_CAP_HOTPLUG_SURPRISE_OFFSET) |
                    (1 << SLOT_CAP_ELECTROMECH_INTERLOCK_PRESENT_OFFSET) |
                    (0 << SLOT_CAP_NO_COMMAND_COMPLETED_SUPPORTED_OFFSET)),
          NULL
          );
      }

      //Set OR mode for DxioHotplugServerExpress
      SmnPrivateRegRMW (GnbHandle,
                       WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_PRESENCE_DETECT_SELECT_ADDRESS),
                       (UINT32) ~(PCIE_PRESENCE_DETECT_SELECT_PRESENCE_DETECT_SELECT_MASK),
                       (0 & (PCIE_PRESENCE_DETECT_SELECT_PRESENCE_DETECT_SELECT_MASK >>
                       PCIE_PRESENCE_DETECT_SELECT_PRESENCE_DETECT_SELECT_OFFSET)) << PCIE_PRESENCE_DETECT_SELECT_PRESENCE_DETECT_SELECT_OFFSET,
                       0
                      );

      SmnPrivateRegRMW (GnbHandle,
                       PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIEP_PORT_CNTL_ADDRESS),
                       (UINT32) ~(PCIEP_PORT_CNTL_PWR_FAULT_EN_MASK),
                       1 << PCIEP_PORT_CNTL_PWR_FAULT_EN_OFFSET,
                       0
                      );

      SmnPrivateRegRMW (GnbHandle,
                       WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIE0NBIO0_SWRST_CONTROL_6_ADDRESS),
                       (UINT32) ~(1 << (Engine->Type.Port.PortId)),
                       0,
                       0
                      );

      SmnRegisterReadS (GnbHandle->Address.Address.Segment,
                        GnbHandle->Address.Address.Bus,
                        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_LINK_CNTL_ADDRESS),
                        &Value
                       );
      if ((Value & (UINT32) (1 << (LINK_STATUS_DL_ACTIVE_OFFSET + 16))) == 0) {
        if (GnbHandle->RBIndex < 4) {
          SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                           GnbHandle->Address.Address.Bus,
                           IOHC_BRIDGE_SPACE (GnbHandle, Engine, SMN_PCIE0DEVINDCFG0_IOHUB0NBIO0_IOHC_Bridge_CNTL_ADDRESS),
                           (UINT32)~(IOHC_Bridge_CNTL_BridgeDis_MASK),
                           ((PcdGetBool(PcdAmdHotPlugDisBridgeDis) ? 0 : 1) << IOHC_Bridge_CNTL_BridgeDis_OFFSET),
                           0
                          );
          IDS_HDT_CONSOLE (GNB_TRACE, "No ep - BridgeDis: %08x\n", IOHC_BRIDGE_SPACE (GnbHandle, Engine, SMN_PCIE0DEVINDCFG0_IOHUB0NBIO0_IOHC_Bridge_CNTL_ADDRESS));
        } else {
          SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                           GnbHandle->Address.Address.Bus,
                           IOHC_BRIDGE_SPACE (GnbHandle, Engine, SMN_PCIE2DEVINDCFG0_IOHUB1NBIO0_IOHC_Bridge_CNTL_ADDRESS),
                           (UINT32)~(IOHC_Bridge_CNTL_BridgeDis_MASK),
                           ((PcdGetBool(PcdAmdHotPlugDisBridgeDis) ? 0 : 1) << IOHC_Bridge_CNTL_BridgeDis_OFFSET),
                           0
                          );
          IDS_HDT_CONSOLE (GNB_TRACE, "No ep - BridgeDis: %08x\n", IOHC_BRIDGE_SPACE (GnbHandle, Engine, SMN_PCIE2DEVINDCFG0_IOHUB1NBIO0_IOHC_Bridge_CNTL_ADDRESS));
        }
      }

      if (PcdGetBool (PcdAmdAllowComplianceForHpPort) == TRUE) {
        // Clear the bit so that port does go into loopback mode
        SmnPrivateRegRMW (GnbHandle,
                          PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIEP_STRAP_LC_ADDRESS),
                          (UINT32) ~(PCIEP_STRAP_LC_STRAP_COMPLIANCE_DIS_MASK),
                          0 << PCIEP_STRAP_LC_STRAP_COMPLIANCE_DIS_OFFSET,
                          0
                        );
      }
      break;

    case DxioHotplugUBM:
      IDS_HDT_CONSOLE (GNB_TRACE, "DxioHotplugUBM\n");
      NbioPcieComplexPpi->PcieGetComplex (NbioPcieComplexPpi, &DxioTopologyData);

      while (DxioTopologyData != NULL) {
        TopologyEntry = DxioTopologyData->PciePortList;
        while (TopologyEntry != NULL) {
          if ((Engine->EngineData.EngineType == DxioPcieEngine) &&
              (Engine->EngineData.StartLane >= TopologyEntry->EngineData.StartLane) &&
              (Engine->EngineData.EndLane <= TopologyEntry->EngineData.EndLane)) {
            PortParamIndex = 0;
            while (PortParamIndex < PCIE_PORT_PARAMETER_COUNT) {
              // Configure NPEM Capability value
              if (TopologyEntry->PortParams.PhyParam[PortParamIndex].ParamType == PP_NPEM_CAPABILITES) {
                SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                                 GnbHandle->Address.Address.Bus,
                                 PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_NPEM_CAP_ADDRESS),
                                 (UINT32) ~(0xFFFFFFFF),
                                 TopologyEntry->PortParams.PhyParam[PortParamIndex].ParamValue & 0x0FFF,
                                 0
                                );
              }

              // Configure overall NPEM Enable bit
              if (TopologyEntry->PortParams.PhyParam[PortParamIndex].ParamType == PP_NPEM_ENABLE) {
                SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                                 GnbHandle->Address.Address.Bus,
                                 PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_NPEM_CNTL_ADDRESS),
                                 (UINT32) ~(0xFFFFFFFF),
                                 TopologyEntry->PortParams.PhyParam[PortParamIndex].ParamValue & 0x0FFF,
                                 0
                               );
              }
              PortParamIndex++;
            }
          }
          TopologyEntry = PcieConfigGetNextDataDescriptor (TopologyEntry);
        }
        DxioTopologyData = PcieConfigGetNextDataDescriptor (DxioTopologyData);
      }
      // fallthrough

    case DxioHotplugOCP:
      GnbLibPciRead (
        (GnbHandle->Address.AddressValue |
         MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber, PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_SLOT_CAP_ADDRESS))),
        AccessWidth32,
        &Value,
        NULL
        );
      Value = (Value & SLOT_CAP_PHYSICAL_SLOT_NUM_MASK) >> SLOT_CAP_PHYSICAL_SLOT_NUM_OFFSET;
      SmnPrivateRegRMW (GnbHandle,
                       (PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIEP_HCNT_DESCRIPTOR_ADDRESS)),
                       (UINT32) ~(0xFFFFFFFF),
                       (UINT32) (0x80000000 | Value),
                       0
                       );
      // fallthrough
    case DxioHotplugServerEntSSD:
      IDS_HDT_CONSOLE (GNB_TRACE, "DxioHotplugServerEntSSD\n");
      //Enterprise SSD
      GnbLibPciRmw (
        (GnbHandle->Address.AddressValue |
         MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber, PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_SLOT_CAP_ADDRESS))),
        AccessWidth32,
        (UINT32) ~(SLOT_CAP_ATTN_BUTTON_PRESENT_MASK |
                   SLOT_CAP_PWR_CONTROLLER_PRESENT_MASK |
                   SLOT_CAP_MRL_SENSOR_PRESENT_MASK |
                   SLOT_CAP_ATTN_INDICATOR_PRESENT_MASK |
                   SLOT_CAP_PWR_INDICATOR_PRESENT_MASK |
                   SLOT_CAP_HOTPLUG_SURPRISE_MASK |
                   SLOT_CAP_ELECTROMECH_INTERLOCK_PRESENT_MASK |
                   SLOT_CAP_NO_COMMAND_COMPLETED_SUPPORTED_MASK),
        (UINT32) ((0 << SLOT_CAP_ATTN_BUTTON_PRESENT_OFFSET) |
                  (0 << SLOT_CAP_PWR_CONTROLLER_PRESENT_OFFSET) |
                  (0 << SLOT_CAP_MRL_SENSOR_PRESENT_OFFSET) |
                  (0 << SLOT_CAP_ATTN_INDICATOR_PRESENT_OFFSET) |
                  (0 << SLOT_CAP_PWR_INDICATOR_PRESENT_OFFSET) |
                  (1 << SLOT_CAP_HOTPLUG_SURPRISE_OFFSET) |
                  (0 << SLOT_CAP_ELECTROMECH_INTERLOCK_PRESENT_OFFSET) |
                  (1 << SLOT_CAP_NO_COMMAND_COMPLETED_SUPPORTED_OFFSET)),
        NULL
        );

      if (PcdGet8 (PcdAmdHotPlugHandlingMode) == 5) {
        GnbLibPciRmw (
          (GnbHandle->Address.AddressValue |
           MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber, PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_SLOT_CAP_ADDRESS))),
          AccessWidth32,
          (UINT32) ~SLOT_CAP_HOTPLUG_SURPRISE_MASK,
          (UINT32) (0 << SLOT_CAP_HOTPLUG_SURPRISE_OFFSET),
          NULL
          );
      }

      SmnPrivateRegRMW (GnbHandle,
                       WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIE0NBIO0_SWRST_CONTROL_6_ADDRESS),
                       (UINT32) ~(1 << (Engine->Type.Port.PortId)),
                       0,
                       0
                      );

      SmnPrivateRegRMW (GnbHandle,
                       PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIEP_PORT_CNTL_ADDRESS),
                       (UINT32) ~(PCIEP_PORT_CNTL_PWR_FAULT_EN_MASK),
                       1 << PCIEP_PORT_CNTL_PWR_FAULT_EN_OFFSET,
                       0
                      );

      SmnPrivateRegRMW (GnbHandle,
                       PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_ERR_CNTL_ADDRESS),
                       (UINT32) ~(PCIE_ERR_CNTL_PRIV_SURP_DIS_VEC_MASK),
                       0x20 << PCIE_ERR_CNTL_PRIV_SURP_DIS_VEC_OFFSET,
                       0
                      );

      SmnRegisterReadS (GnbHandle->Address.Address.Segment,
                        GnbHandle->Address.Address.Bus,
                        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_LINK_CNTL_ADDRESS),
                        &Value);
      if ((Value & ((UINT32) 1 << (LINK_STATUS_DL_ACTIVE_OFFSET + 16))) == 0) {
        if (PcdGet8 (PcdAmdHotPlugHandlingMode) != 5) {
          if (GnbHandle->RBIndex < 4) {
            SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                             GnbHandle->Address.Address.Bus,
                             IOHC_BRIDGE_SPACE (GnbHandle, Engine, SMN_PCIE0DEVINDCFG0_IOHUB0NBIO0_IOHC_Bridge_CNTL_ADDRESS),
                             (UINT32)~((IOHC_Bridge_CNTL_BridgeDis_MASK) |
                                       (IOHC_Bridge_CNTL_CfgDis_MASK) |
                                       (IOHC_Bridge_CNTL_BusMasterDis_MASK)),
                             ((PcdGetBool(PcdAmdHotPlugDisBridgeDis) ? 0 : 1) << IOHC_Bridge_CNTL_BridgeDis_OFFSET),
                             0
                            );
            IDS_HDT_CONSOLE (GNB_TRACE, "No ep - BridgeDis: %08x\n", IOHC_BRIDGE_SPACE (GnbHandle, Engine, SMN_PCIE0DEVINDCFG0_IOHUB0NBIO0_IOHC_Bridge_CNTL_ADDRESS));
          } else {
            SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                             GnbHandle->Address.Address.Bus,
                             IOHC_BRIDGE_SPACE (GnbHandle, Engine, SMN_PCIE2DEVINDCFG0_IOHUB1NBIO0_IOHC_Bridge_CNTL_ADDRESS),
                             (UINT32)~((IOHC_Bridge_CNTL_BridgeDis_MASK) |
                                       (IOHC_Bridge_CNTL_CfgDis_MASK) |
                                       (IOHC_Bridge_CNTL_BusMasterDis_MASK)),
                             ((PcdGetBool(PcdAmdHotPlugDisBridgeDis) ? 0 : 1) << IOHC_Bridge_CNTL_BridgeDis_OFFSET),
                             0
                            );
            IDS_HDT_CONSOLE (GNB_TRACE, "No ep - BridgeDis: %08x\n", IOHC_BRIDGE_SPACE (GnbHandle, Engine, SMN_PCIE2DEVINDCFG0_IOHUB1NBIO0_IOHC_Bridge_CNTL_ADDRESS));
          }
        }

        if (PcdGet8 (PcdAmdHotPlugHandlingMode) == 5) {
          //Check for empty slot and if set, (PCIERCCFG::SFI_CNTL: SFI_PD_STATE_MASK = 1), (PCIERCCFG::SFI_CNTL: SFI_DLL_STATE_MASK = 1), (PCIERCCFG::SFI_CNTL: SFI_DRS_MASK = 1)
          IDS_HDT_CONSOLE (GNB_TRACE, "No ep SFI Mode - Set SFI_PD_STATE_MASK, SFI_DLL_STATE_MASK, SFI_DRS_MASK: %08x\n",
                           PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_SFI_CNTL_ADDRESS));
          GnbLibPciRmw ((GnbHandle->Address.AddressValue |
                        MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber, PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_SFI_CNTL_ADDRESS))),
                        AccessWidth16,
                        (UINT32) ~((SFI_CNTL_SFI_PD_STATE_MASK_MASK) |
                                   (SFI_CNTL_SFI_DLL_STATE_MASK_MASK) |
                                   (SFI_CNTL_SFI_DRS_MASK_MASK)),
                             (1 << (SFI_CNTL_SFI_PD_STATE_MASK_OFFSET)) |
                             (1 << (SFI_CNTL_SFI_DLL_STATE_MASK_OFFSET)) |
                             (1 << (SFI_CNTL_SFI_DRS_MASK_OFFSET)),
                              0);
        }

        Value = (UINT8) PcdGet8 (PcdAmdHotPlugNVMEDefaultMaxPayload);
        if (Value != 0xFF) {
          GnbLibPciRmw ((GnbHandle->Address.AddressValue |
                        MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber, PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_DEVICE_CNTL_ADDRESS))),
                        AccessWidth16,
                           (UINT32) ~(DEVICE_CNTL_MAX_PAYLOAD_SIZE_MASK),
                           (Value & 0x7) << DEVICE_CNTL_MAX_PAYLOAD_SIZE_OFFSET,
                           0
                          );
        }
        // b) If slot is empty, block empty traffic (PCIERCCFG::SFI_CNTL: SFI_DPF_CONTROL = 1)
          GnbLibPciRmw ((GnbHandle->Address.AddressValue |
                        MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber, PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_SFI_CNTL_ADDRESS))),
                        AccessWidth16,
                         (UINT16) ~(SFI_CNTL_SFI_DPF_CONTROL_MASK),
                         (1 << (SFI_CNTL_SFI_DPF_CONTROL_OFFSET)),
                         0
                        );
      } else {
        if (PcdGet8 (PcdAmdHotPlugHandlingMode) == 5) {
          // a) If slot is populated, allow all traffic (PCIERCCFG::SFI_CNTL: SFI_DPF_CONTROL = 0)
          GnbLibPciRmw ((GnbHandle->Address.AddressValue |
                        MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber, PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_SFI_CNTL_ADDRESS))),
                        AccessWidth16,
                        (UINT16) ~(SFI_CNTL_SFI_DPF_CONTROL_MASK),
                        (0 << (SFI_CNTL_SFI_DPF_CONTROL_OFFSET)),
                        0
                        );
          //Clear out SFI_STATUS on BOOT
          GnbLibPciRmw ((GnbHandle->Address.AddressValue |
                        MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber, PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_SFI_STATUS_ADDRESS))),
                        AccessWidth32,
          (UINT32) ~((SFI_STATUS_SFI_DLL_STATE_CHANGED_MASK) |
                     (SFI_STATUS_SFI_OOB_PD_CHANGED_MASK)),
               (1 << (SFI_STATUS_SFI_DLL_STATE_CHANGED_OFFSET)) |
               (1 << (SFI_STATUS_SFI_OOB_PD_CHANGED_OFFSET)),
                0);
        } else {
          if (GnbHandle->RBIndex < 4) {
            SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                             GnbHandle->Address.Address.Bus,
                             IOHC_BRIDGE_SPACE (GnbHandle, Engine, SMN_PCIE0DEVINDCFG0_IOHUB0NBIO0_IOHC_Bridge_CNTL_ADDRESS),
                             (UINT32)~((IOHC_Bridge_CNTL_BridgeDis_MASK) |
                                       (IOHC_Bridge_CNTL_CfgDis_MASK) |
                                       (IOHC_Bridge_CNTL_BusMasterDis_MASK)),
                             0,
                             0
                            );
            IDS_HDT_CONSOLE (GNB_TRACE, "Ep found - BridgeDis: %08x\n", IOHC_BRIDGE_SPACE (GnbHandle, Engine, SMN_PCIE0DEVINDCFG0_IOHUB0NBIO0_IOHC_Bridge_CNTL_ADDRESS));
          } else {
            SmnRegisterRMWS (GnbHandle->Address.Address.Segment,
                             GnbHandle->Address.Address.Bus,
                             IOHC_BRIDGE_SPACE (GnbHandle, Engine, SMN_PCIE2DEVINDCFG0_IOHUB1NBIO0_IOHC_Bridge_CNTL_ADDRESS),
                             (UINT32)~((IOHC_Bridge_CNTL_BridgeDis_MASK) |
                                       (IOHC_Bridge_CNTL_CfgDis_MASK) |
                                       (IOHC_Bridge_CNTL_BusMasterDis_MASK)),
                             0,
                             0
                            );
            IDS_HDT_CONSOLE (GNB_TRACE, "Ep found - BridgeDis: %08x\n", IOHC_BRIDGE_SPACE (GnbHandle, Engine, SMN_PCIE2DEVINDCFG0_IOHUB1NBIO0_IOHC_Bridge_CNTL_ADDRESS));
          }
        }
      }

      break;

    default:
      IDS_HDT_CONSOLE (GNB_TRACE, "Invalid Hotplug Type\n");
      ASSERT (FALSE);
      break;
    }
  IDS_HDT_CONSOLE (GNB_TRACE, "%a Exit\n", __FUNCTION__);
  }
}


/*----------------------------------------------------------------------------------------*/
/**
 * Callback before hot plug initialization on all ports
 *
 *
 *
 *
 * @param[in]       Engine          Pointer to engine config descriptor
 * @param[in, out]  Buffer          Not used
 * @param[in]       Pcie            Pointer to global PCIe configuration
 *
 */

VOID
STATIC
PcieHotplugPreInitCallback (
  IN       PCIe_ENGINE_CONFIG    *Engine,
  IN OUT   VOID                  *Buffer,
  IN       PCIe_PLATFORM_CONFIG  *Pcie
  )
{
  PCIe_WRAPPER_CONFIG   *Wrapper;
  GNB_HANDLE            *GnbHandle;

  Wrapper = PcieConfigGetParentWrapper (Engine);
  GnbHandle = (GNB_HANDLE *) PcieConfigGetParentSilicon (Engine);

  // Clear incorrect HW default of HPGI_SMI_EN and HPGI_SCI_EN in PCIEP_HPGI_ADDRESS
  SmnPrivateRegRMW (
    GnbHandle,
    PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIEP_HPGI_PRIVATE_ADDRESS),
    (UINT32) ~(PCIEP_HPGI_PRIVATE_HPGI_SMI_EN_MASK | PCIEP_HPGI_PRIVATE_HPGI_SCI_EN_MASK),
    ((0 << PCIEP_HPGI_PRIVATE_HPGI_SMI_EN_OFFSET) | (0 << PCIEP_HPGI_PRIVATE_HPGI_SCI_EN_OFFSET)),
    0
    );
}


/*----------------------------------------------------------------------------------------*/
/**
 * Map engine to specific PCI device address
 *
 *
 * @param[in]  GnbHandle           Pointer to the Silicon Descriptor for this node
 * @param[in]  PortDevMap          Pointer to PortDevMap
 */

VOID
PcieConfigureHotplugPorts (
  IN       PCIe_PLATFORM_CONFIG  *Pcie
  )
{
  IDS_HDT_CONSOLE (GNB_TRACE, "%a Enter\n", __FUNCTION__);
  PcieConfigRunProcForAllEngines (
    DESCRIPTOR_ALLOCATED | DESCRIPTOR_PCIE_ENGINE,
    PcieHotplugPreInitCallback,
    NULL,
    Pcie
    );
  PcieConfigRunProcForAllEngines (
    DESCRIPTOR_ALLOCATED | DESCRIPTOR_PCIE_ENGINE,
    PcieHotplugInitCallback,
    NULL,
    Pcie
    );
}





