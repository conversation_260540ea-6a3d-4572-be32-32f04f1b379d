#***********************************************************************
#*                                                                     *
#*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
#*                                                                     *
#*      All rights reserved. Subject to AMI licensing agreement.       *
#*                                                                     *
#***********************************************************************

## @file
#  Cryptographic Library Instance for SMM driver.
#
#  Caution: This module requires additional review when modified.
#  This library will have external input - signature.
#  This external input must be validated carefully to avoid security issues such as
#  buffer overflow or integer overflow.
#
#  Note: SHA-384 Digest functions, SHA-512 Digest functions,
#  RSA external functions, PKCS#7 SignedData sign functions, Diffie-<PERSON>man functions, and
#  authenticode signature verification functions are not supported in this instance.
#
#  Copyright (c) 2010 - 2022, Intel Corporation. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = SmmCryptLib
  MODULE_UNI_FILE                = SmmCryptLib.uni
  FILE_GUID                      = 028080a3-8958-4a62-a1a8-0fa1da162007
  MODULE_TYPE                    = DXE_SMM_DRIVER
  VERSION_STRING                 = 1.0
  PI_SPECIFICATION_VERSION       = 0x0001000A
  LIBRARY_CLASS                  = BaseCryptLib|DXE_SMM_DRIVER SMM_CORE MM_STANDALONE

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 ARM AARCH64
#

[Sources]
  InternalCryptLib.h
  Hash/CryptMd5.c
  Hash/CryptSha1.c
  Hash/CryptSha256.c
  Hash/CryptSm3.c
  Hash/CryptSha512.c
  Hash/CryptSha3.c
  Hash/CryptXkcp.c
  Hash/CryptCShake256.c
  Hash/CryptParallelHash.c
  #APTIOV OVERRIDE - Included in CryptSha3.c
  Hash/CryptParallelHash.h
  #APTIOV OVERRIDE
  Hash/CryptDispatchApMm.c
  Hmac/CryptHmac.c
  Kdf/CryptHkdf.c
  Cipher/CryptAes.c
  Cipher/CryptAeadAesGcmNull.c
  Pk/CryptRsaBasic.c
  Pk/CryptRsaExt.c
  Pk/CryptPkcs1Oaep.c
  Pk/CryptPkcs5Pbkdf2.c
  Pk/CryptPkcs7Sign.c
  Pk/CryptPkcs7VerifyCommon.c
  Pk/CryptPkcs7VerifyBase.c
  Pk/CryptPkcs7VerifyEku.c
  Pk/CryptDhNull.c
  Pk/CryptX509.c
  Pk/CryptAuthenticodeNull.c
  Pk/CryptTsNull.c
  Pk/CryptRsaPss.c
  Pk/CryptRsaPssSignNull.c
  Pk/CryptEcNull.c
  Pem/CryptPem.c
  Bn/CryptBn.c

#  SysCall/CrtWrapper.c              #APTIOV OVERRIDE - CrtWrapperLib is reported as library instance, so that it can be overriden by other modules. 
  SysCall/ConstantTimeClock.c
  SysCall/BaseMemAllocation.c

[Sources.Ia32]
  Rand/CryptRandTsc.c

[Sources.X64]
  Rand/CryptRandTsc.c

#APTIOV OVERRIDE starts
[Sources.IPF]
  Rand/CryptRandItc.c
#APTIOV OVERRIDE ends

[Sources.ARM]
  Rand/CryptRand.c

[Sources.AARCH64]
  Rand/CryptRand.c

[Packages]
  MdePkg/MdePkg.dec
  CryptoPkg/CryptoPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  MemoryAllocationLib
  OpensslLib
  IntrinsicLib
  PrintLib
  CrtWrapperLib  #APTIOV OVERRIDE - CrtWrapperLib is reported as library instance, so that it can be overriden by other modules. 
  MmServicesTableLib
  SynchronizationLib

#
# Remove these [BuildOptions] after this library is cleaned up
#
[BuildOptions]
  #
  # suppress the following warnings so we do not break the build with warnings-as-errors:
  #

  XCODE:*_*_*_CC_FLAGS = -mmmx -msse -std=c99

  GCC:*_CLANGDWARF_*_CC_FLAGS = -std=c99 -Wno-error=incompatible-pointer-types
  GCC:*_CLANGPDB_*_CC_FLAGS = -std=c99 -Wno-error=incompatible-pointer-types
