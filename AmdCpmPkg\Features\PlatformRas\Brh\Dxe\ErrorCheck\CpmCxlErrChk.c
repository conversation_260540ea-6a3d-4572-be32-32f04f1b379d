/*****************************************************************************
 *
 * Copyright (C) 2021-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Library/BaseMemoryLib.h>
#include <Library/IoLib.h>
#include <Library/PciLib.h>
#include <Protocol/AmdRasServiceDxeProtocol.h>
#include "AmdPlatformRasBrhDxe.h"
#include "CpmPcieAer.h"
#include "CpmCxlErrChk.h"
#include <Library/NbioCommonLibDxe.h>
#include <Library/NbioHandleLib.h>
#include <Library/PcieConfigLib.h>
#include <Library/IdsLib.h>
#include <AmdPcieComplex.h>
#include <Library/NbioHandleLib.h>
#include <Library/MpioInitLib.h>
#include <MpioLib.h>
#include <Library/MemoryAllocationLib.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */

// CXL Subsystem Component Register Ranges in MEMBAR0
#define CXL_RCRB_MEMBAR0_IO_OFFSET      0x0
#define CXL_RCRB_MEMBAR0_CAMEM_OFFSET   0x1000

//DF::CnliCxlUncorrErrStat0
//A0 Stepping
//#define SMN_CNLI0_UNCORRECTABLE_ERROR_STATUS_ADDR                 0x1F100054

//C0 stepping
#define SMN_CNLI0_UNCORRECTABLE_ERROR_STATUS_ADDR                 0x1F100060

typedef enum  {
  DS_MEMBAR,
  US_MEMBAR,
} DS_US_INDEX;

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */
extern EFI_BOOT_SERVICES              *gBS;
extern AMD_RAS_SERVICE_DXE_PROTOCOL   *AmdRasServiceDxeProtocol;
extern GENERIC_PCIE_AER_ERR_ENTRY_V3  gGenPcieErrEntry;
extern GENERIC_CXL_ERR_ENTRY_V3       gGenCxlErrEntry;

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
EFI_STATUS
RcrbAppendList (
  IN OUT   RCEC_PROFILE  *RcecProfile,
  IN       UINT64        RcrbAddress,
  IN       UINT8         PortId
  );


EFI_STATUS
RciepAppendList (
  IN       PCI_ADDR           Device,
  IN       PCIE_DEVICE_TYPE   DeviceType,
  IN       RCEC_PROFILE       *RcecProfile
  );


SCAN_STATUS
STATIC
RciepScanCallback (
  IN       PCI_ADDR             Device,
  IN OUT   RAS_PCI_SCAN_DATA    *ScanData
  );


EFI_STATUS
ScanDpRcrb(
  IN OUT   RCEC_PROFILE  *RcecProfile,
  IN       UINT32        RcrbBaseAddrReg,
  IN       UINT32        SocketBasedBusNum
  );


EFI_STATUS
ScanRciep(
  IN       RCEC_PROFILE *RcecProfile
  );


EFI_STATUS
LogCxlProtocolError(
  IN       CXL_ERROR_LOG_DATA  *CxlErrorLogData
  );


EFI_STATUS
CxlSetEventIntrPolicy(
  IN       UINT32   RciepAddr,
  IN       UINT8    RcecId
  );


EFI_STATUS
CxlGetEventIntrPolicy(
  IN       UINT32   RciepAddr,
  IN       UINT8    RcecId
  );


EFI_STATUS
CxlDpErrReportEn(
  IN       RCEC_PROFILE            *RcecProfile,
  IN       PCIe_AER_CONFIG_TEMP    *PcieAerSetting
  );

EFI_STATUS
SetCxlEcrcFeature (
  IN      PCIe_ENGINE_CONFIG                *Engine
  );

UINT32
GetSmnCxlRcrbBase (
  IN  UINT8     Nbio,
  IN  UINT8     PcieCoreNumInNbio
  );

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                          T A B L E    D E F I N I T I O N S
 *----------------------------------------------------------------------------------------
 */


/*---------------------------------------------------------------------------------------*/



EFI_STATUS
RcrbAppendList (
  IN OUT   RCEC_PROFILE  *RcecProfile,
  IN       UINT64        RcrbAddress,
  IN       UINT8         PortId
)
{
  CXL_DP_ENTRY    *CxlDpEntry;
  EFI_STATUS      Status = EFI_SUCCESS;

  //
  // Allocate a new PCI entry
  //
  Status = gBS->AllocatePool (
                  EfiRuntimeServicesData,
                  sizeof (CXL_DP_ENTRY),
                  (VOID **)&CxlDpEntry
                  );
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, " Add Link failed, Out of Resource!!!\n");
    return Status;
  }

  CxlDpEntry->RcecPciAddr = RcecProfile->RcecPciAddr;
  CxlDpEntry->PortId = PortId;
  CxlDpEntry->RcrbAddr = RcrbAddress;
  CxlDpEntry->SecondaryBusNum = MmioRead8(RcrbAddress + PCICFG_SPACE_SECONDARY_BUS_OFFSET);
  CxlDpEntry->SubordinateBusNum = MmioRead8(RcrbAddress + PCICFG_SPACE_SUBORDINATE_BUS_OFFSET);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, " Add Port: %x, RCRB Address 0x%016lX, Offset 0x19: 0x%x, Offset 0x1A: 0x%x to the Link List\n",
          CxlDpEntry->PortId,
          CxlDpEntry->RcrbAddr,
          CxlDpEntry->SecondaryBusNum,
          CxlDpEntry->SubordinateBusNum
          );
  //
  // Add the newly allocated PCI entry to the Root Port pci link list
  //
//  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, " RcrbLinkList Address: 0x%08x\n", (UINTN)&RcecProfile->RcrbLinkList);
//  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, " Forward Link Address: 0x%08x, Backward Link Address: 0x%08x\n", RcecProfile->RcrbLinkList.ForwardLink, RcecProfile->RcrbLinkList.BackLink);

  InsertTailList (&RcecProfile->RcrbLinkList, &CxlDpEntry->ListEntry);
  RcecProfile->DpCnt++;

  return Status;
}


EFI_STATUS
RciepAppendList (
  IN       PCI_ADDR           Device,
  IN       PCIE_DEVICE_TYPE   DeviceType,
  IN       RCEC_PROFILE       *RcecProfile
)
{
  CXL_RCIEP_ENTRY* CxlRciepEntry;
  EFI_STATUS      Status = EFI_SUCCESS;

  //
  // Allocate a new PCI entry
  //
  Status = gBS->AllocatePool (
                  EfiRuntimeServicesData,
                  sizeof (CXL_RCIEP_ENTRY),
                  (VOID **)&CxlRciepEntry
                  );
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, " Add Link failed, Out of Resource!!!\n");
    return Status;
  }

  CxlRciepEntry->RcecPciAddr = RcecProfile->RcecPciAddr;
  CxlRciepEntry->DevAddr = Device.AddressValue;
  CxlRciepEntry->DevType = DeviceType;
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, " Add Device @0x%08x, Type: 0x%x to the Link List\n", CxlRciepEntry->DevAddr, CxlRciepEntry->DevType);
  //
  // Add the newly allocated PCI entry to the Root Port pci link list
  //
//  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, " PciLinkList Address: 0x%08x\n", (UINTN)&PciePortProfile->PciLinkList);
//  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, " Forward Link Address: 0x%08x, Backward Link Address: 0x%08x\n", PciePortProfile->PciLinkList.ForwardLink, PciePortProfile->PciLinkList.BackLink);

  InsertTailList (&RcecProfile->RciepLinkList, &CxlRciepEntry->ListEntry);
  RcecProfile->RciepCnt++;

  return Status;
}


SCAN_STATUS
STATIC
RciepScanCallback (
  IN       PCI_ADDR             Device,
  IN OUT   RAS_PCI_SCAN_DATA    *ScanData
)
{
  SCAN_STATUS             ScanStatus;
  PCIE_DEVICE_TYPE        DeviceType;

  ScanStatus = SCAN_SUCCESS;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  RciepScanCallback at Device = S:%d,B:%d,D:%d,F:%d\n",
    Device.Address.Segment,
    Device.Address.Bus,
    Device.Address.Device,
    Device.Address.Function
    );

  DeviceType = RasGetPcieDeviceType (Device);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  PCI-E device type = 0x%x\n", DeviceType);

  if (DeviceType == PcieNotPcieDevice) {
    return ScanStatus;
  }
  RciepAppendList(Device, DeviceType, (RCEC_PROFILE*)ScanData->Buffer);

  return ScanStatus;
}


EFI_STATUS
ScanDpRcrb(
  IN OUT   RCEC_PROFILE  *RcecProfile,
  IN       UINT32        RcrbBaseAddrReg,
  IN       UINT32        SocketBasedBusNum
)
{
  EFI_STATUS                  Status = EFI_SUCCESS;
  UINT64                      RcrbAddress;
  CXL_RCRB_BASE_ADDR_LO_REG   CxlRcrbBaseAddrLo;
  UINT32                      CxlRcrbBaseAddrHi;
  UINT32                      RegOffset;
  UINT8                       Port;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a Start\n", __FUNCTION__);

  InitializeListHead(&RcecProfile->RcrbLinkList);

  for (Port = 0; Port < MAX_PCIE_CORE0_PORT_SUPPORT; Port++) {
    RegOffset = (RcrbBaseAddrReg | (Port << 12));  //RegOffset = RcrbBaseAddrReg + (Port * sizeof(UINT64));

    CxlRcrbBaseAddrLo.Value = 0;
    //Get Lower 32 bits RCRB Address
    RasSmnRead (SocketBasedBusNum, RegOffset, &CxlRcrbBaseAddrLo.Value);

    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  SocketBasedBusNum: 0x%02X, RegOffset: 0x%08X, CxlRcrbBaseAddrLo.Value: 0x%08X\n",
      SocketBasedBusNum, RegOffset, CxlRcrbBaseAddrLo.Value);

    if (CxlRcrbBaseAddrLo.Field.Cxl_Rcrb_Enable) {
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Port: %d, RCRB Address Lo: 0x%08x\n", Port, CxlRcrbBaseAddrLo.Value);

      //Get Higher 32 bits RCRB Address
      RasSmnRead (SocketBasedBusNum, RegOffset + sizeof(UINT32), &CxlRcrbBaseAddrHi);

      RcrbAddress = (UINT64)CxlRcrbBaseAddrHi;
      RcrbAddress = (UINT64)((RcrbAddress << 32) + (CxlRcrbBaseAddrLo.Value & CXL_RCRB_BASE_ADDR_LO_MASK));

      RcrbAppendList(RcecProfile, RcrbAddress, Port);

      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Port: %d, RCRB Address: 0x%016lX\n", Port, RcrbAddress);
    }  //if (CxlRcrbBaseAddrLo.Field.Cxl_Rcrb_Enable) {
  }  //for (Port = 0; Port < MAX_PCIE_PORT_SUPPORT; Port++) {

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a End\n", __FUNCTION__);

  return Status;
}


EFI_STATUS
ScanRciep(
  IN       RCEC_PROFILE *RcecProfile
)
{
  PCI_ADDR  Rcec;
  RAS_PCI_SCAN_DATA  ScanData;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a Start\n", __FUNCTION__);

  InitializeListHead(&RcecProfile->RciepLinkList);
  Rcec.AddressValue = RcecProfile->RcecPciAddr;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Scan RCEC Address: 0x%08x\n", Rcec.AddressValue);
  ScanData.Buffer = RcecProfile;
  ScanData.RasScanCallback = RciepScanCallback;
  RasRcecScanSecondaryBus (Rcec, &ScanData);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a End\n", __FUNCTION__);

  return EFI_SUCCESS;
}


EFI_STATUS
CxlPortDetect ( VOID )
{
  EFI_STATUS      Status = EFI_SUCCESS;
  CXL_MAP         *CxlMap;
  UINT32          SocketBasedBusNum;
  UINT32          RegBase;
  UINT32          RcrbBaseAddrReg;
  UINT32          RegOffset;
  RCEC_CFG_REG    RcecCfgReg;
  PCI_ADDR        PciPortAddr;
  UINT8           Nbio;
  UINT8           Rb;
  UINT8           Iohc;
  UINT8           Pcie;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a Start\n", __FUNCTION__);

  //
  //  Allocate memory and Initialize a CXL Map data block
  //
  Status = gBS->AllocatePool (
                  EfiRuntimeServicesData,
                  sizeof (CXL_MAP),
                  (VOID **)&CxlMap
                  );
  if (EFI_ERROR (Status)) {
    return Status;
  }
  ZeroMem (CxlMap, sizeof (CXL_MAP));

  for (Rb = 0; Rb < mRbBusMap->RbCount; Rb++) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] CxlPortDetect - DfNode %d, Nbio %d, Iohc %d, Bus 0x%x, CxlControl: %d\n",
      mRbBusMap->RbBusEntry[Rb].SocketNumber,
      mRbBusMap->RbBusEntry[Rb].Nbio,
      mRbBusMap->RbBusEntry[Rb].Iohc,
      mRbBusMap->RbBusEntry[Rb].RbBusBase,
      mRbBusMap->RbBusEntry[Rb].CxlControl);

    if (!mRbBusMap->RbBusEntry[Rb].CxlControl) {
      continue;
    }

    // Get IOHC instance bus number
    SocketBasedBusNum = SMN_SEG_BUS (mRbBusMap->RbBusEntry[Rb].RbSeg, mRbBusMap->RbBusEntry[Rb].RbBusBase);

    RegBase = ((mRbBusMap->RbBusEntry[Rb].Iohc & BIT0) == 0) ? (NBIO0_IOHUB0_RCEC_BASE_ADDR + CXL_CFG_OFFSET) : (NBIO0_IOHUB1_RCEC_BASE_ADDR + CXL_CFG_OFFSET);
    RegOffset = RegBase + (IOHC_SMN_ADDR_OFFSET * (mRbBusMap->RbBusEntry[Rb].Iohc >> 1)) + (NBIO_SMN_ADDR_OFFSET * mRbBusMap->RbBusEntry[Rb].Nbio);

    // Get RCEC_CFG
    RasSmnRead (SocketBasedBusNum, RegOffset, &RcecCfgReg.Value);

    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]RCEC_CFG: 0x%08x\n", RcecCfgReg.Value);

    // RCEC PCI configuration space address
    PciPortAddr.AddressValue = 0;
    PciPortAddr.Address.Segment = mRbBusMap->RbBusEntry[Rb].RbSeg;
    PciPortAddr.Address.Bus = mRbBusMap->RbBusEntry[Rb].RbBusBase;
    PciPortAddr.Address.Device = 0;
    PciPortAddr.Address.Function = RcecCfgReg.Field.FuncNum;

    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]RCEC Seg:0x%x, Bus:0x%x, Device:0x%x, Function:0x%x\n",
            PciPortAddr.Address.Segment,
            PciPortAddr.Address.Bus,
            PciPortAddr.Address.Device,
            PciPortAddr.Address.Function);

    CxlMap->RcecProfile[CxlMap->RcecCount].RbIndex = mRbBusMap->RbBusEntry[Rb].RbIndex;
    CxlMap->RcecProfile[CxlMap->RcecCount].SocketNum = mRbBusMap->RbBusEntry[Rb].SocketNumber;

    /* Valid value for IOHC: 0~3 */
    CxlMap->RcecProfile[CxlMap->RcecCount].Iohc = mRbBusMap->RbBusEntry[Rb].Iohc;

    CxlMap->RcecProfile[CxlMap->RcecCount].RbSegNum = mRbBusMap->RbBusEntry[Rb].RbSeg;
    CxlMap->RcecProfile[CxlMap->RcecCount].RbBusNum = mRbBusMap->RbBusEntry[Rb].RbBusBase;
    CxlMap->RcecProfile[CxlMap->RcecCount].RcecId = Rb;

    CxlMap->RcecProfile[CxlMap->RcecCount].RcecPciAddr = PciPortAddr.AddressValue;

    // Scan enabled RCRB and add to the map
    Nbio = mRbBusMap->RbBusEntry[Rb].Nbio;
    Iohc = mRbBusMap->RbBusEntry[Rb].Iohc;
    Pcie = (((Iohc & BIT0) << 1) | (((Iohc & BIT1) >> 1)));
    RcrbBaseAddrReg = GetSmnCxlRcrbBase (Nbio, Pcie);
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]CXL_RCRB_BASE_ADDR_LO: 0x%08x\n", RcrbBaseAddrReg);
    if (RcrbBaseAddrReg != 0) {
      ScanDpRcrb(&CxlMap->RcecProfile[CxlMap->RcecCount], RcrbBaseAddrReg, SocketBasedBusNum);
    }

    // Scan CXL device and add to the map
    if (0 != CxlMap->RcecProfile[CxlMap->RcecCount].DpCnt) {
      ScanRciep(&CxlMap->RcecProfile[CxlMap->RcecCount]);
    }

    CxlMap->RcecCount++;
  }

  mPlatformApeiPrivate->CxlMap = CxlMap;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a End\n", __FUNCTION__);

  return Status;
}


EFI_STATUS
LogCxlProtocolError(
  IN       CXL_ERROR_LOG_DATA  *CxlErrorLogData
)
{
  EFI_STATUS                Status = EFI_SUCCESS;
  GENERIC_CXL_ERR_ENTRY_V3  *GenCxlErrEntry;
  ROOT_ERR_STS_REG          RootErrSts;
  UINT32                    ErrorSeverity;
  UINT32                    CxlErrLen = 0;
  EFI_HANDLE                *HandleBuffer;
  UINTN                     i, NumberOfHandles;
  AMD_CPM_RAS_OEM_PROTOCOL  *AmdRasOemProtocol;
  UINT8                     BertRecordNum;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a Start\n", __FUNCTION__);

  Status = gBS->AllocatePool (EfiBootServicesData, PRE_GENERIC_CXL_ERR_ENTRY_SIZE, (VOID **)&GenCxlErrEntry);
  if (EFI_ERROR (Status)) {
    return Status;
  }

  ZeroMem (GenCxlErrEntry, PRE_GENERIC_CXL_ERR_ENTRY_SIZE);

  Status = LogCxlProtocolErrorHelper (
              CxlErrorLogData,
              GenCxlErrEntry,
              &CxlErrLen
              );

  if (CxlErrorLogData->RcecAddress != 0) {  //RcecAddress is 0 in CPM CXL 2.0
    //CXL 1.1 RootErrSts on RCEC. Get RCEC Root Error Status Register
    RootErrSts.Value = RasPcieRootErrorStatus (CxlErrorLogData->RcecAddress);
  } else {
    //CXL 2.0 RootErrSts on CXL Root Port
    RootErrSts.Value = CxlErrorLogData->Cxl2p0RPPcieRootStatus;
  }
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Root Error Status: 0x%08x\n", RootErrSts.Value);

  //CXL device severity should align to the RCEC.
  ErrorSeverity = ERROR_NONE;
  if (RootErrSts.Field.ErrCorReceived) {
    ErrorSeverity = ERROR_SEVERITY_CORRECTED;
  }
  if (RootErrSts.Field.NonFatalErrMesgReceived) {
    ErrorSeverity = ERROR_RECOVERABLE;
  }
  if (RootErrSts.Field.FatalErrMesgReceived) {
    ErrorSeverity = ERROR_SEVERITY_FATAL;
  }

  GenCxlErrEntry->GenErrorDataEntry.ErrorSeverity = ErrorSeverity;

  // Add a new record to the BERT table
  if (!EFI_ERROR(Status)) {

    //Locate Ras Oem Protocol
    Status = gBS->LocateHandleBuffer(ByProtocol,
                &gAmdCpmRasOemProtocolGuid,
                NULL, &NumberOfHandles, &HandleBuffer);
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "LogCxlProtocolError LocateHandleBuffer gAmdCpmRasOemProtocolGuid Status = %r NumberOfHandles = %x HandleBuffer = %lx\n",Status ,NumberOfHandles, HandleBuffer);

    if(!EFI_ERROR (Status)) {
      for (i = 0; i < NumberOfHandles; i++) {
          // Get the protocol on this handle
          Status = gBS->HandleProtocol(HandleBuffer[i],
                      &gAmdCpmRasOemProtocolGuid,
                      (VOID **)&AmdRasOemProtocol);

          if(!EFI_ERROR (Status))
            AmdRasOemProtocol->OemErrorLogEventCxlProtocol (CxlErrorLogData, GenCxlErrEntry);
      }

      if (HandleBuffer != NULL) {
        gBS->FreePool(HandleBuffer);
      }
    }

    BertRecordNum = 1;
    Status = AmdRasServiceDxeProtocol->AddBootErrorRecordEx (
                                             BertRecordNum,                                          // IN UINT8 RecordNum
                                             (UINT8*)GenCxlErrEntry,                                 // IN UINT8* pErrRecord
                                             CxlErrLen,                                              // IN UINT nSize
                                             ERROR_TYPE_GENERIC,                                     // IN UINT8 ErrorType
                                             (UINT8)GenCxlErrEntry->GenErrorDataEntry.ErrorSeverity  // IN UINT8 SeverityType
                                            );
  } else {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] LogCxlProtocolErrorHelper failed: 0x%x\n", Status);
  }

  gBS->FreePool (GenCxlErrEntry);


  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a End\n", __FUNCTION__);

  return Status;
}

VOID
PrintBuffer(
  UINT8  *DataBuffer,
  UINT32 Length
)
{
  UINT32  index;
  UINT8   *BufferPtr;

  BufferPtr = DataBuffer;
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Data Buffer dump, Address: 0x%08x, Length: 0x%x\n", DataBuffer, Length);
  for (index = 0; index < Length; index++) {
    if ((index != 0) && ((index % 16) == 0)) {
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "\n");
    }
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "0x%02x ", *BufferPtr);
    BufferPtr++;
  }
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "\n");
}


EFI_STATUS
LogCxlComponentEvent(
  IN       CXL_ERROR_LOG_DATA  *CxlErrorLogData
)
{
  EFI_STATUS                        Status = EFI_SUCCESS;
  GENERIC_CXL_EVENT_ENTRY_V3        *GenCxlEventEntry;
  GET_EVENT_RECORD_OUTPUT_PAYLOAD   *GetEventRecordOutPutPayload;
  UINT32                            CxlEventLen = 0;
  CXL_DEVICE_EVENT_STATUS           CxlDeviceEventStatus;
  UINT64                            CxlMemDeviceRegAddrBase;
  UINT32                            MailboxRegOffset;
  UINT32                            MailboxRegLength;
  UINT32                            OutPlLength;
  MB_CMD_STATUS                     MbCmdStatus;
  UINT8                             EventLogType;
  BOOLEAN                           ClearEventRecord;
  UINT32                            InPlLength;
  CLEAR_EVENT_RECORD_INPUT_PAYLOAD  *ClrEventRecordInputPl;
  COMMON_EVENT_RECORD               *EventRecordPt;
  VOID                              *pt;
  EVENT_RECORD_FLAGS1               EventRecordFlag1;
  EVENT_RECORD_HEADER               *EventRecordHeader;
  UINTN                             Index = 0;
  EFI_HANDLE                        *HandleBuffer;
  UINTN                             i, NumberOfHandles;
  AMD_CPM_RAS_OEM_PROTOCOL          *AmdRasOemProtocol;
  UINT8                             BertRecordNum;
  UINT8                             MoreEventRecords;
  BOOLEAN                           ClearAllEvent;
  UINT16                            RecordIndex;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a Start\n", __FUNCTION__);

  CxlMemDeviceRegAddrBase = CxlErrorLogData->CxlMemDeviceRegAddr;

  // Check CXL component event
  Status = CxlGetMemoryDeviceStatus (CxlMemDeviceRegAddrBase, &CxlDeviceEventStatus);
  if (Status != EFI_SUCCESS) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] Component Event not support!!!\n");
    return Status;
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] Device Event Status: 0x%08x\n", CxlDeviceEventStatus);
  if ((CxlDeviceEventStatus.Value & EVENT_STATUS_MASK) == 0) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] No valid CXL component events found!!!\n");
    return EFI_NOT_FOUND;
  }
  // Get mailbox registers address offset and length
  MailboxRegOffset = CxlGetMailboxRegAddrByCmpErrRptMode (
                       mPlatformApeiPrivate->PlatRasPolicy.CxlComponentErrorReporting,
                       CxlMemDeviceRegAddrBase,
                       &MailboxRegLength
                     );
  if (MailboxRegOffset == 0) {
    //No Mailbox Register found
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] Unable to get the mailbox address of the CXL device!!!\n");
    return EFI_UNSUPPORTED;
  }

  for (EventLogType = 0; EventLogType < EVENT_STATUS_NUMBER_OF_EVENT_TYPES; EventLogType++) {
    if ((CxlDeviceEventStatus.Value & (1 << EventLogType)) == 0) {
      continue;
    }

    // Get Event Record
    ClearEventRecord = FALSE;
    ClearAllEvent = FALSE;
    do {
      MbCmdStatus = MbCmdGetEventRecord (CxlMemDeviceRegAddrBase + MailboxRegOffset,
                                         EventLogType,
                                         &OutPlLength,
                                         (VOID**)&GetEventRecordOutPutPayload
                                         );
      if ((MbCmdStatus != Mb_Cmd_Success) || (0 == OutPlLength)) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] CXL Mailbox command failed! EventLogType: 0x%x\n",
          EventLogType);
        break;
      }

      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
        "[RAS] OutPlLength: 0x%x, sizeof (GET_EVENT_RECORD_OUTPUT_PAYLOAD): 0x%x, EventRecordCount: 0x%04X\n",
        OutPlLength,
        sizeof (GET_EVENT_RECORD_OUTPUT_PAYLOAD),
        GetEventRecordOutPutPayload->EventRecordCount
      );

      //PrintBuffer((UINT8*)GetEventRecordOutPutPayload, OutPlLength);

      //Make sure all event record extract from the device.
      MoreEventRecords = GetEventRecordOutPutPayload->Flags.Field.MoreEventRecords;
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] MoreEventRecords: 0x%x\n",
        MoreEventRecords);

      if (GetEventRecordOutPutPayload->EventRecordCount == 0) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] EventRecordCount: 0, skip processing this OutPutPayload.\n");
        if (OutPlLength != 0) {
          FreePages (GetEventRecordOutPutPayload, EFI_SIZE_TO_PAGES (OutPlLength));
        }
        continue;
      }
      ClearEventRecord = TRUE;

      pt = (void *) &GetEventRecordOutPutPayload->EventRecords[0];
      EventRecordPt = pt;
      for (RecordIndex = 0; RecordIndex < GetEventRecordOutPutPayload->EventRecordCount; RecordIndex++) {
        CxlEventLen = (sizeof(GENERIC_CXL_EVENT_ENTRY_V3) + (sizeof (COMMON_EVENT_RECORD) - sizeof (EFI_GUID)));

        EventRecordHeader = (EVENT_RECORD_HEADER*)EventRecordPt;

        EventRecordFlag1.Value = EventRecordHeader->EventRecordFlags[0]; //Get first byte from Event Record Flag
        EventRecordHeader->EventRecordLength = sizeof (COMMON_EVENT_RECORD);

        EventRecordPt++; //prepare for the next loop

        Status = gBS->AllocatePool (EfiBootServicesData,
                                    CxlEventLen,
                                    (VOID **)&GenCxlEventEntry);
        if (EFI_ERROR (Status)) {
          // Check next event log type
          continue;
        }
        ZeroMem (GenCxlEventEntry, CxlEventLen);

        EventRecordHeader = (EVENT_RECORD_HEADER*)GetEventRecordOutPutPayload->EventRecords;
        Status = LogCxlEventRecordHelper (
                    CxlErrorLogData,
                    EventRecordHeader,
                    GenCxlEventEntry,
                    &CxlEventLen
                    );
        if(EFI_ERROR (Status)) {
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] CXL Event Logging failed. EventLogType: 0x%x\n", EventLogType);
          gBS->FreePool (GenCxlEventEntry);
          continue;
        }
        EventRecordFlag1.Value = EventRecordHeader->EventRecordFlags[0]; //Get first byte from Event Record Flag

        CxlDeviceEventLogSeverityConvertion (
                               EventRecordFlag1.Field.EventRecordSeverity,
                               &GenCxlEventEntry->GenErrorDataEntry.ErrorSeverity
                              );

        // Add a new record to the BERT table

        //Locate Ras Oem Protocol
        Status = gBS->LocateHandleBuffer(ByProtocol,
                    &gAmdCpmRasOemProtocolGuid,
                    NULL, &NumberOfHandles, &HandleBuffer);
        IDS_HDT_CONSOLE_RAS_TRACE(
          RAS_TRACE_INFO,
          "LogCxlComponentEvent LocateHandleBuffer gAmdCpmRasOemProtocolGuid Status = %r NumberOfHandles = %x HandleBuffer = %lx\n",
          Status ,NumberOfHandles, HandleBuffer
        );

        if(!EFI_ERROR (Status)) {
          for (i = 0; i < NumberOfHandles; i++) {
              // Get the protocol on this handle
              Status = gBS->HandleProtocol(HandleBuffer[i],
                          &gAmdCpmRasOemProtocolGuid,
                          (VOID **)&AmdRasOemProtocol);

              if(!EFI_ERROR (Status))
                AmdRasOemProtocol->OemErrorLogEventCxlComponent (CxlErrorLogData, GenCxlEventEntry);
          }

          if (HandleBuffer != NULL) {
            gBS->FreePool(HandleBuffer);
          }
        }
        BertRecordNum = 2;
        Status = AmdRasServiceDxeProtocol->AddBootErrorRecordEx (
                                                 BertRecordNum,                                             // IN UINT8 RecordNum
                                                 (UINT8*)GenCxlEventEntry,                                  // IN UINT8* pErrRecord
                                                 CxlEventLen,                                               // IN UINT nSize
                                                 ERROR_TYPE_GENERIC,                                        // IN UINT8 ErrorType
                                                 (UINT8)GenCxlEventEntry->GenErrorDataEntry.ErrorSeverity   // IN UINT8 SeverityType
                                                );
        if (EFI_ERROR (Status)) {
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] AddBootErrorRecordEntry failed: 0x%x\n", Status);
        }

        gBS->FreePool (GenCxlEventEntry);
      }

      // Clear current event record
      if (ClearEventRecord) {
        InPlLength = (sizeof (CLEAR_EVENT_RECORD_INPUT_PAYLOAD) - sizeof (UINT16)) +
                     (sizeof (UINT16) * GetEventRecordOutPutPayload->EventRecordCount); //For Clear Event Record Input payload
        //ClrEventRecordInputPl = AllocateRuntimeZeroPool (InPlLength);
        Status = gBS->AllocatePool (EfiBootServicesData, InPlLength, (VOID **)&ClrEventRecordInputPl);
        if (EFI_ERROR (Status)) {
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] Unable to allocate buffer for clear current event record input payload!!!\n");
        } else {
          ZeroMem (ClrEventRecordInputPl, InPlLength);
          pt = (void *) &GetEventRecordOutPutPayload->EventRecords[0];
          EventRecordPt = pt;
          for (Index = 0; Index < GetEventRecordOutPutPayload->EventRecordCount; Index++) {
            ClrEventRecordInputPl->EventRecordHandles[Index] = EventRecordPt->EventRecordHeader.EventRecordHandle;
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] EventRecordHandles[%x]=0x%x\n", Index, ClrEventRecordInputPl->EventRecordHandles[Index]);
            EventRecordPt++;
          }
          ClrEventRecordInputPl->EventLog = EventLogType;
          ClrEventRecordInputPl->ClearEventFlags = 0; //If 0, the device shall clear the event records specified in the Event Record Handles list.
          ClrEventRecordInputPl->NumberOfEventRecordHandles = (UINT8)GetEventRecordOutPutPayload->EventRecordCount; //The number of event records in the Event Records list.

          if (GetEventRecordOutPutPayload->Flags.Field.Overflow & 0x01) { //This bit shall be set by the device when errors occur that the device cannot log without overwriting an existing log event.
            //Clear All Events is only allowed when the Event Log has overflowed; otherwise, the device shall return Invalid Input.
            ClearAllEvent = TRUE;
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] ClearAllEvent flag is set\n");
          }
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ClrEventRecordInputPl Input Pauload dump, Address: 0x%08x, InPlLength: 0x%x\n", ClrEventRecordInputPl, InPlLength);
          PrintBuffer((UINT8*)ClrEventRecordInputPl, InPlLength);
          MbCmdClearEventRecord (CxlMemDeviceRegAddrBase + MailboxRegOffset,
                                 InPlLength,
                                 (UINT8*)ClrEventRecordInputPl
                                 );
          FreePool (ClrEventRecordInputPl);
        }
      }  // if (ClearEventRecord)

      if (OutPlLength != 0) {
        FreePages (GetEventRecordOutPutPayload, EFI_SIZE_TO_PAGES (OutPlLength));
      }
    } while (MoreEventRecords);

    //
    // Check if there are any remaining records of this EventLogType that cannot be retrieved - try clearing them.
    //
    if (ClearAllEvent) {
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
        "[RAS] Clear all remaining records of this EventLogType that cannot be retrieved\n"
      );

      // Clear all device Event record
      InPlLength = sizeof (CLEAR_EVENT_RECORD_INPUT_PAYLOAD); //For Clear Event Record Input payload
      Status = gBS->AllocatePool (EfiBootServicesData, InPlLength, (VOID **)&ClrEventRecordInputPl);
      if (EFI_ERROR (Status)) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] Unable to allocate buffer for clear all event record input payload!!!\n");
      } else {
        ZeroMem (ClrEventRecordInputPl, InPlLength);
        ClrEventRecordInputPl->EventLog = EventLogType;
        ClrEventRecordInputPl->ClearEventFlags = (UINT8)CLEAR_EVENT_FLAG_CLEAR_ALL_EVENT;
        ClrEventRecordInputPl->NumberOfEventRecordHandles = 0; //If Clear All Events is set, this shall be 0.
        ClrEventRecordInputPl->EventRecordHandles[0] = 0;        //Cleat all event command should not ask for handle. just in case.

        InPlLength = sizeof (CLEAR_EVENT_RECORD_INPUT_PAYLOAD);

        MbCmdClearEventRecord (CxlMemDeviceRegAddrBase + MailboxRegOffset,
                               InPlLength,
                               (UINT8*)&ClrEventRecordInputPl
                               );
        FreePool (ClrEventRecordInputPl);
      }
    }  // if (ClearAllEvent)
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a End\n", __FUNCTION__);
  return Status;
}


EFI_STATUS
LogDpCxlIoError (
  IN       UINT64   RcrbAddress
  )
{
  EFI_STATUS                    Status = EFI_SUCCESS;
  GENERIC_PCIE_AER_ERR_ENTRY_V3 *GenPcieAerErrEntry;
  PCIE_ERROR_SECTION            *PcieErrorSection;
  EFI_GUID                      PcieErrorSectGuid = PCIE_SECT_GUID;
  ROOT_ERR_STS_REG              RootErrSts;
  ERROR_SOURCE_ID_REG           ErrorSrcId;
  PCI_ADDR                      RcrbBdf;
  EFI_HANDLE                    *HandleBuffer;
  UINTN                         NumberOfHandles;
  AMD_CPM_RAS_OEM_PROTOCOL      *AmdRasOemProtocol;
  UINT32                        Index;
  UINT32                        ErrorSourceId = 0;
  UINT8                         SeverityType = 0;
  UINT16                        AerCapPtr;
  UINT8                         PcieCapPtr;
  UINT8                         BertRecordNum;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  %a - Entry\n", __FUNCTION__);

  AerCapPtr  = RasFindRcrbExtendedCapability(RcrbAddress, PCIE_EXT_AER_CAP_ID);
  PcieCapPtr = RasFindRcrbPciCapability (RcrbAddress, PCIE_CAP_ID);

  if ((AerCapPtr == 0) && (PcieCapPtr == 0)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  PCIE or AER capabilbity not found\n");
  }

  Status = gBS->AllocatePool (EfiBootServicesData, sizeof (GENERIC_PCIE_AER_ERR_ENTRY_V3), (VOID **)&GenPcieAerErrEntry);
  if (EFI_ERROR (Status)) {
    return Status;
  }

  gBS->CopyMem (GenPcieAerErrEntry, &gGenPcieErrEntry, sizeof (GENERIC_PCIE_AER_ERR_ENTRY_V3));

  //Update Error section GUID
  gBS->CopyMem (&GenPcieAerErrEntry->GenErrorDataEntry.SectionType[0], &PcieErrorSectGuid, sizeof (EFI_GUID));

  //Check Error Severity
  RootErrSts.Value = MmioRead32(RcrbAddress + AerCapPtr + PCIE_ROOT_STATUS_PTR);
  ErrorSrcId.Value = MmioRead32(RcrbAddress + AerCapPtr + PCIE_ERROR_SOURCE_IDENTIFICATION_PTR);
  SeverityType = ERROR_NONE;
  if (RootErrSts.Field.ErrCorReceived) {
    SeverityType = ERROR_SEVERITY_CORRECTED;
    ErrorSourceId = (ErrorSrcId.Field.CorSourceIdentification << 12);
  }
  if (RootErrSts.Field.NonFatalErrMesgReceived) {
    SeverityType = ERROR_RECOVERABLE;
    ErrorSourceId = (ErrorSrcId.Field.FatalSourceIdentification << 12);
  }
  if (RootErrSts.Field.FatalErrMesgReceived) {
    SeverityType = ERROR_SEVERITY_FATAL;
    ErrorSourceId = (ErrorSrcId.Field.FatalSourceIdentification << 12);
  }

  //Fill PCIe Error Data Section
  RcrbBdf.AddressValue = ErrorSourceId;
  PcieErrorSection = &GenPcieAerErrEntry->PcieAerErrorSection;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]RCRB Address : 0x%016lX @ B:0x%08x D:0x%08x F:0x%08x\n",
          RcrbAddress,
          RcrbBdf.Address.Bus,
          RcrbBdf.Address.Device,
          RcrbBdf.Address.Function
          );

  FillRcrbPcieErrorSection (RcrbAddress,
                            RcrbBdf,
                            (UINT32)SeverityType,
                            PcieErrorSection
                            );
  GenPcieAerErrEntry->GenErrorDataEntry.ValidationBits = FRU_STRING_VALID;
  AsciiStrCpyS ((CHAR8 *)GenPcieAerErrEntry->GenErrorDataEntry.FruText, FRU_TEXT_MAX_LENGTH, "PcieError (CXL DP)");

  GenPcieAerErrEntry->GenErrorDataEntry.ErrorSeverity = SeverityType;
  //Locate Ras Oem Protocol
  Status = gBS->LocateHandleBuffer(ByProtocol,
              &gAmdCpmRasOemProtocolGuid,
              NULL, &NumberOfHandles, &HandleBuffer);

  if(!EFI_ERROR(Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "PcieErrorLog LocateHandleBuffer gAmdCpmRasOemProtocolGuid Status = %r NumberOfHandles = %x HandleBuffer = %lx\n",Status ,NumberOfHandles, HandleBuffer);

    for (Index = 0; Index < NumberOfHandles; Index++) {
        // Get the protocol on this handle
        Status = gBS->HandleProtocol(HandleBuffer[Index],
                    &gAmdCpmRasOemProtocolGuid,
                    (VOID **)&AmdRasOemProtocol);

        if(!EFI_ERROR(Status))
          AmdRasOemProtocol->OemErrorLogEventPcie (GenPcieAerErrEntry);
    }

    if (HandleBuffer != NULL) {
      gBS->FreePool(HandleBuffer);
    }
  }

  // Add a new record to the BERT table
  BertRecordNum = 1;
  Status = AmdRasServiceDxeProtocol->AddBootErrorRecordEx (BertRecordNum,                              // IN UINT8 RecordNum
                                                                (UINT8*)GenPcieAerErrEntry,                 // IN UINT8* pErrRecord
                                                                sizeof (GENERIC_PCIE_AER_ERR_ENTRY_V3),     // IN UINT nSize
                                                                ERROR_TYPE_GENERIC,                         // IN UINT8 ErrorType - GENERIC error type
                                                                SeverityType                                // IN UINT8 SeverityType
                                                               );

  gBS->FreePool (GenPcieAerErrEntry);

  return Status;
}

/*
  CxlErrorLog
    The value returned is used only to indicate whether PCIe AER errors were found and logged.
*/
EFI_STATUS
CxlErrorLog (
  IN       PCI_ADDR             Device,
  IN OUT   RAS_ERR_LOG_DATA    *ErrLogData
  )
{
  EFI_STATUS           PcieErrorLogStatus;
  CXL_ERROR_LOG_DATA   *CxlErrorLogData;
  UINT16               ErrorEntryCnt;
  PCIE_ERR_ENTRY       PcieErrorEntry;
  CXL_AGENT_ADDRESS    CxlAgentAddress;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a Start\n", __FUNCTION__);

  CxlErrorLogData = (CXL_ERROR_LOG_DATA*)ErrLogData->Buffer;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL Error Log Type: 0x%x\n", CxlErrorLogData->ErrorLogType);

  //IO error
  PcieErrorLogStatus = EFI_NOT_FOUND;
  if (CxlErrorLogData->ErrorLogType & CXL_IO_ERROR) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL Error Log Type: IO Error\n");
    CxlAgentAddress.AgentAddress = CxlErrorLogData->CxlAgentAddress;
    if (CxlErrorLogData->CxlAgentType == CXL_AGENT_TYPE_DEVICE) {
      // RCiEP io error.
      PcieErrorEntry.DevAddr = (CxlCxlAgentAddrToPciAddr(&CxlAgentAddress)).AddressValue;
      PcieErrorEntry.EntryValid = TRUE;
      if (CxlErrorLogData->RcecAddress != 0) {  //RcecAddress is 0 in CPM CXL 2.0
        //CXL 1.1 RootErrSts on RCEC
        PcieErrorEntry.RootErrSts = RasPcieRootErrorStatus (CxlErrorLogData->RcecAddress);
      } else {
        //CXL 2.0 RootErrSts on CXL Root Port
        PcieErrorEntry.RootErrSts = CxlErrorLogData->Cxl2p0RPPcieRootStatus;
      }
      PcieErrorEntry.DevType = PCieDeviceRCiEP;

      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  CXL Device IO Error at Address: 0x%08X, Root Error Status: 0x%08X\n",
        PcieErrorEntry.DevAddr, PcieErrorEntry.RootErrSts
      );

      ErrorEntryCnt = 1;
      PcieErrorLogStatus = PcieErrorLog (ErrorEntryCnt, &PcieErrorEntry);
    } else if (CxlErrorLogData->CxlAgentType == CXL_AGENT_TYPE_ROOT_PORT) {
      // CXL 2.0 root port io error.
      PcieErrorEntry.DevAddr = (CxlCxlAgentAddrToPciAddr(&CxlAgentAddress)).AddressValue;
      PcieErrorEntry.EntryValid = TRUE;
      PcieErrorEntry.RootErrSts = CxlErrorLogData->Cxl2p0RPPcieRootStatus;
      PcieErrorEntry.DevType = PcieDeviceRootComplex;

      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  CXL 2.0 Root Port IO Error at Address: 0x%08X, Root Error Status: 0x%08X\n",
        PcieErrorEntry.DevAddr, PcieErrorEntry.RootErrSts
      );

      ErrorEntryCnt = 1;
      PcieErrorLogStatus = PcieErrorLog (ErrorEntryCnt, &PcieErrorEntry);
    } else {
      // CXL 1.1 RCRB IO error
      LogDpCxlIoError (CxlAgentAddress.AgentAddress);
    }
  }

  //cache and mem error
  if (CxlErrorLogData->ErrorLogType & CXL_CACHE_MEM_ERROR) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL Error Log Type: Protocol Error\n");
    LogCxlProtocolError(CxlErrorLogData);
  }

  //Compoenent Event
  if (CxlErrorLogData->ErrorLogType & CXL_COMPONENT_EVENT) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL Error Log Type: Component Error\n");
    LogCxlComponentEvent(CxlErrorLogData);
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a End, PcieErrorLog Status: %r\n", __FUNCTION__, PcieErrorLogStatus);

  return PcieErrorLogStatus;
}


EFI_STATUS
CxlErrorDection ( VOID )
{
  EFI_STATUS          Status = EFI_SUCCESS;
  UINT16              RcecIndex;
  RCEC_PROFILE        *RcecProfileInstance;
  RAS_ERR_LOG_DATA    CxlErrLogData;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a Start\n", __FUNCTION__);

  CxlErrLogData.Buffer = NULL;
  CxlErrLogData.RasLogCallback = CxlErrorLog;

  RcecProfileInstance = mPlatformApeiPrivate->CxlMap->RcecProfile;

  // Search RCEC for CXL errors
  for (RcecIndex = 0; RcecIndex < mPlatformApeiPrivate->CxlMap->RcecCount; RcecIndex++, RcecProfileInstance++) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  CXL Error search at Seg: 0x%x, Bus: 0x%x, RCEC Id: %0d, DP count: %d, Device count: %d\n",
      RcecProfileInstance->RbSegNum,
      RcecProfileInstance->RbBusNum,
      RcecProfileInstance->RcecId,
      RcecProfileInstance->DpCnt,
      RcecProfileInstance->RciepCnt
    );

    // Check RCRB root error status for valid error

    // Check CXL downstream port
    if (0 != RcecProfileInstance->DpCnt) {
      CxlDpErrStsCheck(RcecProfileInstance, &CxlErrLogData);
    }

    // Check CXL device
    if (0 != RcecProfileInstance->RciepCnt) {
      CxlDevErrStsCheck(RcecProfileInstance, &CxlErrLogData, 0, CXL_DEV_CHK_ALL_ERROR_TYPES);
    }
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a End\n", __FUNCTION__);
  return Status;
}


VOID
PrintEventIntrPolicy(
  IN       EVENT_INTR_PAYLOAD    *EventIntrOutputPayload
)
{
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Informational Event Log Interrupt: 0x%02X\n", EventIntrOutputPayload->InformationEventIntrSetting.Value);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Warning Event Log Interrupt:       0x%02X\n", EventIntrOutputPayload->WarningEventIntrSetting.Value);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Failure Event Log Interrupt:       0x%02X\n", EventIntrOutputPayload->FailureEventIntrSetting.Value);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Fatal Event Log Interrupt:         0x%02X\n", EventIntrOutputPayload->FatalEventIntrSetting.Value);
}

EFI_STATUS
CxlSetEventIntrPolicy(
  IN       UINT32   RciepAddr,
  IN       UINT8    RcecId
)
{
  EFI_STATUS            Status = EFI_SUCCESS;
  EVENT_INTR_PAYLOAD    SetEventIntrInputPayload;
  UINT64                CxlMemDeviceRegAddrBase;
  UINT32                MailboxRegOffset;
  UINT32                MailboxRegLength;
  FW_INTR_VECTOR        FwIntrVector;
  MB_CMD_STATUS         MbCmdStatus;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a Start\n", __FUNCTION__);

  CxlMemDeviceRegAddrBase = RasGetRciepRegisterBlockAddress (RciepAddr, CxlMemoryDeviceRegistersBlkId);
  if (0 == CxlMemDeviceRegAddrBase) {
    return EFI_UNSUPPORTED;
  }
  MailboxRegOffset = CxlGetMailboxRegAddrByCmpErrRptMode (
                       mPlatformApeiPrivate->PlatRasPolicy.CxlComponentErrorReporting,
                       CxlMemDeviceRegAddrBase,
                       &MailboxRegLength
                     );
  if (MailboxRegOffset == 0) {
    //No Mailbox Register found
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] Unable to get the mailbox address of the CXL device!!!\n");
    return EFI_UNSUPPORTED;
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL Memory Device Register Base: 0x%016lX, Offset: 0x%x\n",
          CxlMemDeviceRegAddrBase,
          MailboxRegOffset);

  FwIntrVector.Value = 0;
  FwIntrVector.Field.EventLogId = EVENT_RECORD_INFORMATIONAL_EVENT_LOG;
  FwIntrVector.Field.RcecId = RcecId & EVENT_INTERRUPT_MODE_MASK;
  SetEventIntrInputPayload.InformationEventIntrSetting.Value = 0;
  if (mPlatformApeiPrivate->PlatRasPolicy.CXlInformationalEventLogInterrupt) {
    SetEventIntrInputPayload.InformationEventIntrSetting.Field.InterruptMode = EVENT_INTERRUPT_MODE_FW_INTERRUPT;
  } else {
    SetEventIntrInputPayload.InformationEventIntrSetting.Field.InterruptMode = EVENT_INTERRUPT_MODE_NO_INTERRUPT;
  }
  SetEventIntrInputPayload.InformationEventIntrSetting.Field.FwIntrMsgNum = FwIntrVector.Value;

  FwIntrVector.Field.EventLogId = EVENT_RECORD_WARNING_EVENT_LOG;
  SetEventIntrInputPayload.WarningEventIntrSetting.Value = 0;
  if (mPlatformApeiPrivate->PlatRasPolicy.CXlWarningEventLogInterrupt) {
    SetEventIntrInputPayload.WarningEventIntrSetting.Field.InterruptMode = EVENT_INTERRUPT_MODE_FW_INTERRUPT;
  } else {
    SetEventIntrInputPayload.WarningEventIntrSetting.Field.InterruptMode = EVENT_INTERRUPT_MODE_NO_INTERRUPT;
  }
  SetEventIntrInputPayload.WarningEventIntrSetting.Field.FwIntrMsgNum = FwIntrVector.Value;

  FwIntrVector.Field.EventLogId = EVENT_RECORD_FAILURE_EVENT_LOG;
  SetEventIntrInputPayload.FailureEventIntrSetting.Value = 0;
  SetEventIntrInputPayload.FailureEventIntrSetting.Field.InterruptMode = EVENT_INTERRUPT_MODE_FW_INTERRUPT;
  SetEventIntrInputPayload.FailureEventIntrSetting.Field.FwIntrMsgNum = FwIntrVector.Value;

  FwIntrVector.Field.EventLogId = EVENT_RECORD_FATAL_EVENT_LOG;
  SetEventIntrInputPayload.FatalEventIntrSetting.Value = 0;
  SetEventIntrInputPayload.FatalEventIntrSetting.Field.InterruptMode = EVENT_INTERRUPT_MODE_FW_INTERRUPT;
  SetEventIntrInputPayload.FatalEventIntrSetting.Field.FwIntrMsgNum = FwIntrVector.Value;

  PrintEventIntrPolicy(&SetEventIntrInputPayload);

  MbCmdStatus = MbCmdSetEventIntrPolicy (CxlMemDeviceRegAddrBase + MailboxRegOffset,
                                         sizeof (EVENT_INTR_PAYLOAD),
                                         (UINT8*)&SetEventIntrInputPayload
                                         );
  if (MbCmdStatus != Mb_Cmd_Success) {
    Status = EFI_INVALID_PARAMETER;
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "SetEventIntrPolicy command failed: 0x%08x\n", MbCmdStatus);
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a End\n", __FUNCTION__);

  return Status;
}


EFI_STATUS
CxlGetEventIntrPolicy(
  IN       UINT32   RciepAddr,
  IN       UINT8    RcecId
)
{
  EFI_STATUS            Status = EFI_SUCCESS;
  UINT32                OutputLength;
  EVENT_INTR_PAYLOAD    *EventIntrOutputPayload;
  UINT64                CxlMemDeviceRegAddrBase;
  UINT32                MailboxRegOffset;
  UINT32                MailboxRegLength;
  MB_CMD_STATUS         MbCmdStatus;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a Start\n", __FUNCTION__);

  CxlMemDeviceRegAddrBase = RasGetRciepRegisterBlockAddress (RciepAddr, CxlMemoryDeviceRegistersBlkId);
  if (0 == CxlMemDeviceRegAddrBase) {
    return EFI_UNSUPPORTED;
  }
  MailboxRegOffset = CxlGetMailboxRegAddrByCmpErrRptMode (
                       mPlatformApeiPrivate->PlatRasPolicy.CxlComponentErrorReporting,
                       CxlMemDeviceRegAddrBase,
                       &MailboxRegLength
                     );
  if (MailboxRegOffset == 0) {
    //No Mailbox Register found
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] Unable to get the mailbox address of the CXL device!!!\n");
    return EFI_UNSUPPORTED;
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL Memory Device Register Base: 0x%016lX, Offset: 0x%x\n",
          CxlMemDeviceRegAddrBase,
          MailboxRegOffset);

  MbCmdStatus = MbCmdGetEventIntrPolicy (CxlMemDeviceRegAddrBase + MailboxRegOffset,
                                         &OutputLength,
                                         (UINT8**)&EventIntrOutputPayload
                                         );
  if (MbCmdStatus != Mb_Cmd_Success) {
    Status = EFI_INVALID_PARAMETER;
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "GetEventIntrPolicy command failed: 0x%08x\n", MbCmdStatus);
  }
  PrintEventIntrPolicy(EventIntrOutputPayload);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a End\n", __FUNCTION__);

  return Status;
}


EFI_STATUS
CxlDeviceErrReportEn(
  RCEC_PROFILE            *RcecProfile,
  PCIe_AER_CONFIG_TEMP    *PcieDevAerSetting
)
{
  EFI_STATUS          Status = EFI_SUCCESS;
  UINT8               Index;
  LIST_ENTRY          *Node;
  CXL_RCIEP_ENTRY     *CxlRciepEntry;
  PCI_ADDR            RciepPciAddr;
  BOOLEAN             MpioIgnoreCxlRasVdm;
  PCI_ADDR            Rcec;
  UINT32              MpioStatus;
  UINT32              MpioArg[6];

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] CxlDeviceErrReportEn - Entry\n");

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
    "[RAS]CxlComponentErrorReporting: 0x%02X, AllCxlDevsSupportSecMailbox: 0x%02X\n",
    mPlatformApeiPrivate->PlatRasPolicy.CxlComponentErrorReporting,
    mPlatformApeiPrivate->PlatRasPolicy.AllCxlDevsSupportSecMailbox
  );

  MpioIgnoreCxlRasVdm = FALSE;
  if ((mPlatformApeiPrivate->PlatRasPolicy.CxlComponentErrorReporting != DEBUG_FW_FIRST) &&
      !mPlatformApeiPrivate->PlatRasPolicy.AllCxlDevsSupportSecMailbox) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
      "FW_FIRST handling of CXL Component errors cannot be supported by the platform.\n"
    );
    MpioIgnoreCxlRasVdm = TRUE;
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]Rcrc: 0x%08X, Cxl RCiEP count : %d\n",
    RcecProfile->RcecPciAddr,
    RcecProfile->RciepCnt
  );
  if (RcecProfile->RciepCnt != 0) {
    if (MpioIgnoreCxlRasVdm) {
      Rcec.AddressValue = RcecProfile->RcecPciAddr;
      Rcec.Address.Function = 0;
      //Sends a message to MPIO FW to ignore CXL RAS VDMs
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Sends a message to MPIO FW to ignore CXL RAS VDMs\n");
      NbioMpioServiceCommonInitArguments (MpioArg);
      MpioArg[0] = 0; //0: Disable SMI generation, 1: Enable SMI generation
      MpioStatus = MpioServiceRequest (Rcec, BIOS_MPIO_MSG_CXL_ERROR_FW_FIRST, MpioArg, 0);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Response from MPIO: %x\n", MpioStatus);
    }

    //Search End Point device error
    for (Index = 0; Index < RcecProfile->RciepCnt; Index++) {
      if (Index == 0) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Get First Node, Head Forward Link Addr: 0x%08x\n",RcecProfile->RciepLinkList.ForwardLink);
        Node = GetFirstNode(&RcecProfile->RciepLinkList);
      } else {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Get Next Node, Current Node Address: 0x%08x\n",(UINTN)Node);
        Node = GetNextNode(&RcecProfile->RciepLinkList, Node);
      }
      CxlRciepEntry = (CXL_RCIEP_ENTRY*)Node;
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  RCiEP/RP Address: 0x%08x, DevType\n", CxlRciepEntry->DevAddr, CxlRciepEntry->DevType);

      RciepPciAddr.AddressValue = CxlRciepEntry->DevAddr;

      if (RciepPciAddr.AddressValue == 0) {
        continue;
      }

      PcieDevCntlEnableOnDevice (RciepPciAddr, PcieDevAerSetting);

      if (!MpioIgnoreCxlRasVdm) {
        //Setup CXL device event log interrupt.
        Status = CxlSetEventIntrPolicy(RciepPciAddr.AddressValue, RcecProfile->RcecId);
        if (Status != EFI_SUCCESS) {
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  CXL Memory Device error reoprt not enabled\n");
        } else {
          //Event Interrupt Ploicy setting readback
          Status = CxlGetEventIntrPolicy(RciepPciAddr.AddressValue, RcecProfile->RcecId);
        }
      }
    }
  }

  return Status;
}

EFI_STATUS
CxlErrorConfig ( VOID )
{
  EFI_STATUS            Status = EFI_SUCCESS;
  RCEC_PROFILE          *RcecProfileInstance;
  PCI_ADDR              RcecPciAddr;
  UINT16                RcecIndex;
  PCIe_AER_CONFIG_TEMP  PcieAerSetting;
  UINT16                PcieDevCtrlOr;
  UINT8                 PcieCapPtr;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a Start\n", __FUNCTION__);

  RcecProfileInstance = mPlatformApeiPrivate->CxlMap->RcecProfile;

  // Clear all CXL status registers here

  // Start configure RCEC/RCRB/RCiEP error report
  for (RcecIndex = 0; RcecIndex < mPlatformApeiPrivate->CxlMap->RcecCount; RcecIndex++, RcecProfileInstance++) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Enable CXL Error Report at Seg: 0x%x, Bus: 0x%x, RCEC Id: %0d, DP count: %d, Device count: %d\n",
      RcecProfileInstance->RbSegNum,
      RcecProfileInstance->RbBusNum,
      RcecProfileInstance->RcecId,
      RcecProfileInstance->DpCnt,
      RcecProfileInstance->RciepCnt
    );

    if ((0 == RcecProfileInstance->DpCnt) && (0 == RcecProfileInstance->RciepCnt)) {
      //Skip config if no CXL device found.
      continue;
    }
    RcecPciAddr.AddressValue = RcecProfileInstance->RcecPciAddr;
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "RCEC Address: 0x%08x\n", RcecPciAddr.AddressValue);

    if (mPlatformApeiPrivate->PlatRasPolicy.CxlProtocolErrorReporting == 2 ||
         mPlatformApeiPrivate->PlatRasPolicy.CxlProtocolErrorReporting == 1) {
      // RCEC AER enablment
      PcieAerSetting.AerEnable = 1;
      PcieAerSetting.PciSeg = (UINT8)RcecPciAddr.Address.Segment;
      PcieAerSetting.PciBus = (UINT8)RcecPciAddr.Address.Bus;
      PcieAerSetting.PciDev = (UINT8)RcecPciAddr.Address.Device;
      PcieAerSetting.PciFunc = (UINT8)RcecPciAddr.Address.Function;

      if (mPlatformApeiPrivate->PlatRasPolicy.CxlDpCieMask) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CxlDpCieMask enabled\n");
        PcieAerSetting.CorrectableMask =  mPlatformApeiPrivate->PlatRasPolicy.PcieRpCorrectedErrorMask | PCIE_CORR_INT_ERR_MASK;
      } else {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CxlDpCieMask disabled\n");
        PcieAerSetting.CorrectableMask =  mPlatformApeiPrivate->PlatRasPolicy.PcieRpCorrectedErrorMask & (~PCIE_CORR_INT_ERR_MASK);
      }
      PcieAerSetting.UncorrectableMask = mPlatformApeiPrivate->PlatRasPolicy.PcieRpUnCorrectedErrorMask;
      PcieAerSetting.UncorrectableMask |= (BIT14); //BIT14: Completion Timeout Mask
      PcieAerSetting.UncorrectableSeverity = mPlatformApeiPrivate->PlatRasPolicy.PcieRpUnCorrectedErrorSeverity;

      RasSetPcieAerFeature (&PcieAerSetting);

      // Get active port PCI-E cap pointer
      PcieCapPtr = RasFindPciCapability (RcecPciAddr.AddressValue, PCIE_CAP_ID);

      if (PcieCapPtr != 0) {
        //Enable active port dev_cntl and bridge control register.
        PcieDevCtrlOr = PCIE_DEV_CORR_ERR + PCIE_DEV_NON_FATAL_ERR + PCIE_DEV_FATAL_ERR;
        PciOr16(RcecPciAddr.AddressValue + PcieCapPtr + PCIE_DEVICE_CONTROL_PTR, PcieDevCtrlOr);
      }

      //Set dev_cntl and root command registers on DP
      CxlDpErrReportEn (RcecProfileInstance, &PcieAerSetting);
    }

    if ((mPlatformApeiPrivate->PlatRasPolicy.CxlComponentErrorReporting == DEBUG_FW_FIRST) ||
        (mPlatformApeiPrivate->PlatRasPolicy.AllCxlDevsSupportSecMailbox)) {
      PcieAerSetting.AerEnable = 1;
      PcieAerSetting.PciBus = (UINT8)RcecPciAddr.Address.Bus;
      PcieAerSetting.PciDev = (UINT8)RcecPciAddr.Address.Device;
      PcieAerSetting.PciFunc = (UINT8)RcecPciAddr.Address.Function;
      PcieAerSetting.CorrectableMask = mPlatformApeiPrivate->PlatRasPolicy.PcieDevCorrectedErrorMask;
      PcieAerSetting.UncorrectableMask = mPlatformApeiPrivate->PlatRasPolicy.PcieDevUnCorrectedErrorMask;
      PcieAerSetting.UncorrectableSeverity = mPlatformApeiPrivate->PlatRasPolicy.PcieDevUnCorrectedErrorSeverity;

      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Device Correctable Error Mask: 0x%08x\n", PcieAerSetting.CorrectableMask);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Device UnCorrectable Error Mask: 0x%08x\n", PcieAerSetting.UncorrectableMask);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Device UnCorrectable Error Severity: 0x%08x\n", PcieAerSetting.UncorrectableSeverity);


      // RCiEP Error Report enablement
      CxlDeviceErrReportEn(RcecProfileInstance, &PcieAerSetting);
    }
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a End\n", __FUNCTION__);

  return Status;
}

//CXL 1.1
VOID
STATIC
CxlAerPerPortConfig (
  IN      PCIe_ENGINE_CONFIG                *Engine,
  IN OUT  VOID                              *Buffer,
  IN      PCIe_WRAPPER_CONFIG               *Wrapper
  )
{
  CXL_CAPABILITY_HEADER                         *CapHeader;
  CXL_RAS_CAPABILITY_HEADER                     *RasCapHeader;
  CXL_1_1_RAS_CAPABILITY_STRUCTURE              *RasCapStruct;
  UINT64                                        DsUsMemBar[2];
  UINT8                                         i;
  UINT64                                        RcrbAddress;
  UINT16                                        DsRcrbAerCapPtr;
  PCIE_EXTCAP_AER_UNCORRECTABLE_ERROR_SEVERITY  DsRcrbAerUcErSevReg;

  if (!PcieLibIsEngineAllocated(Engine)) {
    return;
  }

  IDS_HDT_CONSOLE (GNB_TRACE, "%a CXL AER Engine StartLane:%d\n", __FUNCTION__, Engine->EngineData.StartLane);
  DsUsMemBar[DS_MEMBAR] = RasGetDpComponentRegisterAddress(Engine->Type.Cxl.DsRcrb);
  DsUsMemBar[US_MEMBAR] = RasGetDpComponentRegisterAddress((Engine->Type.Cxl.DsRcrb + 0x1000));

  IDS_HDT_CONSOLE (GNB_TRACE, "DsMemBar0: 0x%016lx, UsMemBar0: 0x%016lx\n", DsUsMemBar[DS_MEMBAR], DsUsMemBar[US_MEMBAR]);

  for (i = 0; i < (sizeof (DsUsMemBar) / sizeof (UINT64)); i++) {
    if (DsUsMemBar[i] == 0) {
      continue;
    }
    CapHeader = (CXL_CAPABILITY_HEADER *)((UINTN)(DsUsMemBar[i] + CXL_RCRB_MEMBAR0_CAMEM_OFFSET));
    RasCapHeader = (CXL_RAS_CAPABILITY_HEADER *)((UINTN)CapHeader + CXL_RAS_CAPABILITY_HEADER_OFFSET);
    IDS_HDT_CONSOLE (GNB_TRACE, "%a CapHeader Addr:0x%016lX \n", ((i == DS_MEMBAR)?"DsMemBar":"UsMemBar"), CapHeader);
    IDS_HDT_CONSOLE (GNB_TRACE, "RasCapHeader Addr:0x%016lX Id:0x%X Version:0x%X Pointer:0x%X\n",
      RasCapHeader, RasCapHeader->Bits.CxlCapabilityId, RasCapHeader->Bits.CxlCapabilityVersion, RasCapHeader->Bits.CxlRasCapabilityPointer);

    //Check CXL_RAS_Capability ID 0x2
    if (RasCapHeader->Bits.CxlCapabilityId != 0x2) {
      continue;
    }
    RasCapStruct = (CXL_1_1_RAS_CAPABILITY_STRUCTURE *)((UINTN)CapHeader + RasCapHeader->Bits.CxlRasCapabilityPointer);
    //Poison_Received_Mask
    RasCapStruct->UncorrectableErrorMask.Bits.PoisonReceivedMask = 1;

    IDS_HDT_CONSOLE (GNB_TRACE, "RasCapStruct Addr:0x%016lX\n", RasCapStruct);
    IDS_HDT_CONSOLE (GNB_TRACE, "    UncorrectableErrorStatus   : 0x%08X\n", RasCapStruct->UncorrectableErrorStatus.Uint32);
    IDS_HDT_CONSOLE (GNB_TRACE, "    UncorrectableErrorMask     : 0x%08X\n", RasCapStruct->UncorrectableErrorMask.Uint32);
    IDS_HDT_CONSOLE (GNB_TRACE, "    UncorrectableErrorSeverity : 0x%08X\n", RasCapStruct->UncorrectableErrorSeverity.Uint32);
    IDS_HDT_CONSOLE (GNB_TRACE, "    CorrectableErrorStatus     : 0x%08X\n", RasCapStruct->CorrectableErrorStatus.Uint32);
    IDS_HDT_CONSOLE (GNB_TRACE, "    CorrectableErrorMask       : 0x%08X\n", RasCapStruct->CorrectableErrorMask.Uint32);
    IDS_HDT_CONSOLE (GNB_TRACE, "    ErrorCapabilitiesAndControl: 0x%08X\n", RasCapStruct->ErrorCapabilitiesAndControl.Uint32);
  }

  //DsRcrb - Set UIE Severity as Fatal
  RcrbAddress = Engine->Type.Cxl.DsRcrb;
  if (RcrbAddress != 0) {
    DsRcrbAerCapPtr  = RasFindRcrbExtendedCapability(RcrbAddress, PCIE_EXT_AER_CAP_ID);
    if (DsRcrbAerCapPtr != 0) {
      DsRcrbAerUcErSevReg.Value = MmioRead32(RcrbAddress + DsRcrbAerCapPtr + PCIE_EXTCAP_AER_UNCORRECTABLE_ERROR_SEVERITY_OFFSET);
      DsRcrbAerUcErSevReg.Field.UncorrectableInternalErrorSeverity = 1;
      MmioWrite32 (RcrbAddress + DsRcrbAerCapPtr + PCIE_EXTCAP_AER_UNCORRECTABLE_ERROR_SEVERITY_OFFSET, DsRcrbAerUcErSevReg.Value);
    }
  }

  if (PcdGetBool(PcdPcieEcrcEnablement)) {
    SetCxlEcrcFeature(Engine);
  }
}

//CXL 2.0
//[MMIO]
VOID
Cxl2p0AerConfig (  //CxlAerConfig
  VOID
  )
{
  PCIE_PORT_PROFILE                     *PciePortProfileInstance;
  UINT16                                PciePortIndex;
  UINT64                                CxlComponentRegAddr;
  UINT64                                CxlCacheMemRegisterAddr;
  UINT16                                CxlRasCapPtr;
  CXL_1_1_RAS_CAPABILITY_STRUCTURE      CxlRasCapReg;

  PciePortProfileInstance = mPlatformApeiPrivate->AmdPciePortMap->PciPortNumber;

  //Search only the active CXL 2.0 root port and the devices behind it for errors
  for (PciePortIndex = 0; PciePortIndex < mPlatformApeiPrivate->AmdPciePortMap->PortCount; PciePortIndex++, PciePortProfileInstance++) {
    if (!PciePortProfileInstance->CxlPresent ||
        (PciePortProfileInstance->CxlVersion != 2)) {
      continue;
    }

    //
    //CXL 2.0 Root Port PCI Address
    //
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
      "[RAS] Cxl2p0AerConfig - Cxl 2.0 Root Port Address:  0x%08X\n",
      PciePortProfileInstance->RpPciAddr
    );

    CxlComponentRegAddr = RasGetCxl2p0RpRegisterBlockAddress (PciePortProfileInstance->RpPciAddr, ComponenetRegistersBlkId);
    if (CxlComponentRegAddr == 0 ) {
      continue;
    }

    CxlCacheMemRegisterAddr = CxlComponentRegAddr + CXL_CACHE_MEM_REGISTERS_OFFSET;
    CxlRasCapPtr = RasGetCxlCapabilityPointer (CxlCacheMemRegisterAddr, CxlRasCapabilityId);

    if (CxlRasCapPtr == 0) {
      continue;
    }

    //DF::CnliCxlUncorrErrStat0
    CxlRasCapReg.UncorrectableErrorStatus.Uint32 = MmioRead32(CxlCacheMemRegisterAddr + CxlRasCapPtr + CXL_UNCORRECTABLE_ERROR_STATUS_OFFSET);
    IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "    UncorrectableErrorStatus   : 0x%08X\n", CxlRasCapReg.UncorrectableErrorStatus.Uint32);

    //DF::CnliCxlUncorrErrMask0
    CxlRasCapReg.UncorrectableErrorMask.Uint32 = MmioRead32(CxlCacheMemRegisterAddr + CxlRasCapPtr + CXL_UNCORRECTABLE_ERROR_MASK_OFFSET);
      //Poison_Received_Mask
    CxlRasCapReg.UncorrectableErrorMask.Bits.PoisonReceivedMask = 1;
    MmioWrite32(CxlCacheMemRegisterAddr + CxlRasCapPtr + CXL_UNCORRECTABLE_ERROR_MASK_OFFSET, CxlRasCapReg.UncorrectableErrorMask.Uint32);
    CxlRasCapReg.UncorrectableErrorMask.Uint32 = MmioRead32(CxlCacheMemRegisterAddr + CxlRasCapPtr + CXL_UNCORRECTABLE_ERROR_MASK_OFFSET);
    IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "    UncorrectableErrorMask     : 0x%08X\n", CxlRasCapReg.UncorrectableErrorMask.Uint32);

    //DF::CnliCxlUncorrErrSeverity0
    CxlRasCapReg.UncorrectableErrorSeverity.Uint32 = MmioRead32(CxlCacheMemRegisterAddr + CxlRasCapPtr + CXL_UNCORRECTABLE_ERROR_SEVERITY_OFFSET);
    IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "    UncorrectableErrorSeverity : 0x%08X\n", CxlRasCapReg.UncorrectableErrorSeverity.Uint32);

    //DF::CnliCxlCorrErrStat0
    CxlRasCapReg.CorrectableErrorStatus.Uint32 = MmioRead32(CxlCacheMemRegisterAddr + CxlRasCapPtr + CXL_CORRECTABLE_ERROR_STATUS_OFFSET);
    IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "    CorrectableErrorStatus     : 0x%08X\n", CxlRasCapReg.CorrectableErrorStatus.Uint32);

    //DF::CnliCxlCorrErrMask0
    CxlRasCapReg.CorrectableErrorMask.Uint32 = MmioRead32(CxlCacheMemRegisterAddr + CxlRasCapPtr + CXL_CORRECTABLE_ERROR_MASK_OFFSET);
      //Mem_Poison_Received_Mask
    CxlRasCapReg.CorrectableErrorMask.Bits.MemoryPoisonReceivedMask = 1;
    MmioWrite32(CxlCacheMemRegisterAddr + CxlRasCapPtr + CXL_CORRECTABLE_ERROR_MASK_OFFSET, CxlRasCapReg.CorrectableErrorMask.Uint32);
    CxlRasCapReg.CorrectableErrorMask.Uint32 = MmioRead32(CxlCacheMemRegisterAddr + CxlRasCapPtr + CXL_CORRECTABLE_ERROR_MASK_OFFSET);
    IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "    CorrectableErrorMask     : 0x%08X\n", CxlRasCapReg.CorrectableErrorMask.Uint32);

    //DF::CnliCxlErrCapAndCtrl0
    CxlRasCapReg.ErrorCapabilitiesAndControl.Uint32= MmioRead32(CxlCacheMemRegisterAddr + CxlRasCapPtr + CXL_ERROR_CAPABILITY_AND_CONTROL_OFFSET);
    IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "    ErrorCapabilitiesAndControl: 0x%08X\n", CxlRasCapReg.ErrorCapabilitiesAndControl.Uint32);
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a End\n", __FUNCTION__);

  return;
}

//CXL 1.1
VOID
CxlAerConfig ( VOID )
{
  EFI_STATUS                          Status;
  GNB_HANDLE                          *GnbHandle;
  PCIe_PLATFORM_CONFIG                *Pcie;
  PCIe_WRAPPER_CONFIG                 *Wrapper;

  Status = PcieGetPcieDxe (&Pcie);
  if (EFI_ERROR(Status)) {
    return;
  }

  GnbHandle = NbioGetHandle (Pcie);
  while (GnbHandle != NULL) {
    Wrapper = PcieConfigGetChildWrapper (GnbHandle);
    while (Wrapper != NULL) {
      PcieConfigRunProcForAllEnginesInWrapper (DESCRIPTOR_CXL_ENGINE, CxlAerPerPortConfig, NULL, Wrapper);
      Wrapper = PcieLibGetNextDescriptor (Wrapper);
    }
    GnbHandle = GnbGetNextHandle (GnbHandle);
  }
}

EFI_STATUS
CxlDpErrReportEn(
  IN       RCEC_PROFILE            *RcecProfile,
  IN       PCIe_AER_CONFIG_TEMP    *PcieAerSetting
  )
{
  EFI_STATUS          Status = EFI_SUCCESS;
  UINT8               Index;
  LIST_ENTRY          *Node;
  CXL_DP_ENTRY        *CxlDpEntry;
  UINT64              RcrbAddress;
  UINT8               PcieCapPtr;
  UINT16              AerCapPtr;
  UINT16              PcieDevCtrlOr;
  UINT16              PciBrdgeCtrlOr;
  UINT32              PcieRootErrCmd;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] CxlDpErrReportEn - Cxl Down Stream port count : %d\n", RcecProfile->DpCnt);

  Node = NULL;
  if (RcecProfile->DpCnt != 0) {
    for (Index = 0; Index < RcecProfile->DpCnt; Index++) {
      if (Index == 0) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Get First Node, Head Forward Link Addr: 0x%08x\n",RcecProfile->RcrbLinkList.ForwardLink);
        Node = GetFirstNode(&RcecProfile->RcrbLinkList);
      } else {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Get Next Node, Current Node Address: 0x%08x\n",(UINTN)Node);
        Node = GetNextNode(&RcecProfile->RcrbLinkList, Node);
      }
      CxlDpEntry = (CXL_DP_ENTRY*)Node;
      RcrbAddress = CxlDpEntry->RcrbAddr;

      if (RcrbAddress == 0) {
        continue;
      }

      // Clear status registers before enable error report.
      RasRcrbPcieStsClr(RcrbAddress, mPlatformApeiPrivate->PlatRasPolicy.RasRetryCnt);

      PcieCapPtr = RasFindRcrbPciCapability(RcrbAddress, PCIE_CAP_ID);
      AerCapPtr  = RasFindRcrbExtendedCapability(RcrbAddress, PCIE_EXT_AER_CAP_ID);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  RCRB[%x] Address 0x%016lX, PcieCapPtr 0x%04x, AerCapPtr 0x%04x\n", Index, RcrbAddress, PcieCapPtr, AerCapPtr);


      PciBrdgeCtrlOr = PCI_BRIDGECTRL_SERR_EN;
      MmioOr16(RcrbAddress + PCI_BRIDGE_CONTROL_REG, PciBrdgeCtrlOr);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Set Bridge Control Register:  0x%04x\n", PciBrdgeCtrlOr);

      if (PcieCapPtr != 0) {
        PcieDevCtrlOr = PCIE_DEV_CORR_ERR + PCIE_DEV_NON_FATAL_ERR + PCIE_DEV_FATAL_ERR;
        MmioOr16(RcrbAddress + PcieCapPtr + PCIE_DEVICE_CONTROL_PTR, PcieDevCtrlOr);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Set Device Control Register: 0x%04x\n", PcieDevCtrlOr);
      }

      if (AerCapPtr != 0) {
        PcieRootErrCmd = PCIE_ROOT_CORR_ERR + PCIE_ROOT_NON_FATAL_ERR + PCIE_ROOT_FATAL_ERR;
        MmioOr32(RcrbAddress + AerCapPtr + PCIE_ROOT_ERR_CMD_PTR, PcieRootErrCmd);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Set Root Error Command Register: 0x%08x\n", PcieRootErrCmd);

        // Program Internal Error Mask in Correctable Error Mask Register (Offset 14h)
        MmioWrite32(RcrbAddress + AerCapPtr + PCIE_CORR_MASK_PTR, PcieAerSetting->CorrectableMask);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Correctable Error Mask Register = 0x%08x\n", MmioRead32(RcrbAddress + AerCapPtr + PCIE_CORR_MASK_PTR));

        // Program Internal Error Mask in UnCorrectable Error Mask Register (Offset 08h)
        MmioWrite32(RcrbAddress + AerCapPtr + PCIE_UNCORR_MASK_PTR, PcieAerSetting->UncorrectableMask & (~PCIE_UNCORR_INT_ERR_MASK));
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Uncorrectable Error Mask Register = 0x%08x\n", MmioRead32(RcrbAddress + AerCapPtr + PCIE_UNCORR_MASK_PTR));

        // Unmask Internal Error Severity in Uncorrectable Error Severity Register (Offset 0Ch)
        MmioWrite32(RcrbAddress + AerCapPtr + PCIE_UNCORR_SEVERITY_PTR, (PcieAerSetting->UncorrectableSeverity & (~PCIE_UNCORR_INT_ERR_SEVERITY)));
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Uncorrectable Error Severity Register = 0x%08x\n", MmioRead32(RcrbAddress + AerCapPtr + PCIE_UNCORR_SEVERITY_PTR));
      }
    }
  }

  return Status;
}

EFI_STATUS
SetCxlEcrcFeature (
  IN      PCIe_ENGINE_CONFIG                *Engine
  )
{
  UINT64                                            RcrbAddress;
  UINT32                                            RciepAddr;
  UINT16                                            DsRcrbAerCapPtr;
  PCIE_EXTCAP_AER_ADVANCED_ERROR_CAPABILITIES_CNTL  DsRcrbAerCapCntlReg;
  PCIE_EXTCAP_AER_UNCORRECTABLE_ERROR_SEVERITY      DsRcrbAerUcErSevReg;
  UINT16                                            AerCapPtr;
  PCIE_EXTCAP_AER_ADVANCED_ERROR_CAPABILITIES_CNTL  AerCapCntlReg;
  PCIE_EXTCAP_AER_UNCORRECTABLE_ERROR_SEVERITY      AerUcErSevReg;
  BOOLEAN                                           EcrcEnabled;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a Entry\n", __FUNCTION__);

  EcrcEnabled = FALSE;

  //RCRB
  RcrbAddress = Engine->Type.Cxl.DsRcrb;
  DsRcrbAerCapPtr = 0;
  if (RcrbAddress != 0) {
    DsRcrbAerCapPtr  = RasFindRcrbExtendedCapability(RcrbAddress, PCIE_EXT_AER_CAP_ID);
  }
  //RCiEP
  RciepAddr = Engine->Type.Cxl.Address.AddressValue;
  AerCapPtr = 0;
  if (RciepAddr != 0) {
    AerCapPtr = RasFindPcieExtendedCapability(RciepAddr, PCIE_EXT_AER_CAP_ID, 0xFFFF);
  }

  //RCRB
  if (DsRcrbAerCapPtr != 0) {
    DsRcrbAerCapCntlReg.Value = MmioRead32(RcrbAddress + DsRcrbAerCapPtr + PCIE_EXTCAP_AER_ADVANCED_ERROR_CAPABILITIES_CNTL_OFFSET);
    //Check for ECRC Capability support on downstream RCRB
    if (DsRcrbAerCapCntlReg.Field.EcrcGenerationCapable && DsRcrbAerCapCntlReg.Field.EcrcCheckCapable) {
      //RCiEP
      if (AerCapPtr != 0) {
        AerCapCntlReg.Value = PciSegmentRead32(RciepAddr + AerCapPtr + PCIE_EXTCAP_AER_ADVANCED_ERROR_CAPABILITIES_CNTL_OFFSET);
        //Check for ECRC Capability support on RCiEP
        if (AerCapCntlReg.Field.EcrcGenerationCapable && AerCapCntlReg.Field.EcrcCheckCapable) {
          AerCapCntlReg.Field.EcrcGenerationEnable = 1;
          AerCapCntlReg.Field.EcrcCheckEnable = 1;
          PciSegmentWrite32(RciepAddr + AerCapPtr + PCIE_EXTCAP_AER_ADVANCED_ERROR_CAPABILITIES_CNTL_OFFSET, AerCapCntlReg.Value);
          EcrcEnabled = TRUE;
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ECRC Enabled with Servity: %a\n", PcdGetBool(PcdPcieEcrcSeverityFatal) ? "Fatal" : "Non-Fatal");
          if (PcdGetBool(PcdPcieEcrcSeverityFatal)) {
            //Set ECRC Severity as Fatal
            AerUcErSevReg.Value = PciSegmentRead32(RciepAddr + AerCapPtr + PCIE_EXTCAP_AER_UNCORRECTABLE_ERROR_SEVERITY_OFFSET);
            AerUcErSevReg.Field.ECRCErrorSeverity = 1;
            PciSegmentWrite32(RciepAddr + AerCapPtr + PCIE_EXTCAP_AER_UNCORRECTABLE_ERROR_SEVERITY_OFFSET, AerUcErSevReg.Value);
          }
        }
      }

      if (EcrcEnabled) {
        DsRcrbAerCapCntlReg.Field.EcrcGenerationEnable = 1;
        DsRcrbAerCapCntlReg.Field.EcrcCheckEnable = 1;
        MmioWrite32(RcrbAddress + DsRcrbAerCapPtr + PCIE_EXTCAP_AER_ADVANCED_ERROR_CAPABILITIES_CNTL_OFFSET, DsRcrbAerCapCntlReg.Value);
        if (PcdGetBool(PcdPcieEcrcSeverityFatal)) {
          //Set ECRC Severity as Fatal
          DsRcrbAerUcErSevReg.Value = MmioRead32(RcrbAddress + DsRcrbAerCapPtr + PCIE_EXTCAP_AER_UNCORRECTABLE_ERROR_SEVERITY_OFFSET);
          DsRcrbAerUcErSevReg.Field.ECRCErrorSeverity = 1;
          MmioWrite32 (RcrbAddress + DsRcrbAerCapPtr + PCIE_EXTCAP_AER_UNCORRECTABLE_ERROR_SEVERITY_OFFSET, DsRcrbAerUcErSevReg.Value);
        }
      }
    }
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "DsRcrb Advanced Error Capabilities and Control Register@ 0x%08x = 0x%08x\n",
    (DsRcrbAerCapPtr != 0) ? (RcrbAddress + DsRcrbAerCapPtr + PCIE_EXTCAP_AER_ADVANCED_ERROR_CAPABILITIES_CNTL_OFFSET) : 0,
    (DsRcrbAerCapPtr != 0) ? (MmioRead32(RcrbAddress + DsRcrbAerCapPtr + PCIE_EXTCAP_AER_ADVANCED_ERROR_CAPABILITIES_CNTL_OFFSET)) : 0
    );
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "DsRcrb Uncorrectable Error Severity Register           @ 0x%08x = 0x%08x\n",
    (DsRcrbAerCapPtr != 0) ? (RcrbAddress + DsRcrbAerCapPtr + PCIE_EXTCAP_AER_UNCORRECTABLE_ERROR_SEVERITY_OFFSET) : 0,
    (DsRcrbAerCapPtr != 0) ? (MmioRead32(RcrbAddress + DsRcrbAerCapPtr + PCIE_EXTCAP_AER_UNCORRECTABLE_ERROR_SEVERITY_OFFSET)) : 0
    );
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "RCiEP  Advanced Error Capabilities and Control Register@ 0x%08x = 0x%08x\n",
    (AerCapPtr != 0) ? (RciepAddr + AerCapPtr + PCIE_EXTCAP_AER_ADVANCED_ERROR_CAPABILITIES_CNTL_OFFSET) : 0,
    (AerCapPtr != 0) ? (PciSegmentRead32(RciepAddr + AerCapPtr + PCIE_EXTCAP_AER_ADVANCED_ERROR_CAPABILITIES_CNTL_OFFSET)) : 0
    );
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "RCiEP  Uncorrectable Error Severity Register           @ 0x%08x = 0x%08x\n",
    (AerCapPtr != 0) ? (RciepAddr + AerCapPtr + PCIE_EXTCAP_AER_UNCORRECTABLE_ERROR_SEVERITY_OFFSET) : 0,
    (AerCapPtr != 0) ? (PciSegmentRead32(RciepAddr + AerCapPtr + PCIE_EXTCAP_AER_UNCORRECTABLE_ERROR_SEVERITY_OFFSET)) : 0
    );

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a Exit\n", __FUNCTION__);

  return EFI_SUCCESS;
}

UINT32
GetSmnCxlRcrbBase (
  IN  UINT8     Nbio,
  IN  UINT8     PcieCoreNumInNbio
  )
{
  UINT32          SmnCxlRcrbBase;  //PCIERCCFG::CXL_RCRB_BASE

  if (Nbio > MAX_NBIO_PER_DIE) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] %a failed. Nbio = %d, PcieCoreNumInNbio = %d\n", __FUNCTION__, Nbio, PcieCoreNumInNbio);
    return 0;
  }
  //Todo PCIe 5 on NBIO 0
  switch (PcieCoreNumInNbio) {
    case 0:
      SmnCxlRcrbBase = 0x1A300640UL;
      break;
    case 1:
      SmnCxlRcrbBase = 0x1A700640UL;
      break;
    case 2:
      SmnCxlRcrbBase = 0x1A800640UL;
      break;
    case 3:
      SmnCxlRcrbBase = 0x1A400640UL;
      break;
    default:
      //Should not be here
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] %a failed. PcieCoreNumInNbio = %d\n", __FUNCTION__, PcieCoreNumInNbio);
      return 0;
      break;
  }

  SmnCxlRcrbBase += (Nbio * NBIO_SMN_ADDR_OFFSET);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] %a SmnCxlRcrbBase = 0x%0lx @ Nbio: %d, PcieCore: %d\n", __FUNCTION__, SmnCxlRcrbBase, Nbio, PcieCoreNumInNbio);

  return SmnCxlRcrbBase;
}

EFI_STATUS
Cxl2p0ErrorDection ( VOID )
{
  PCIE_PORT_PROFILE   *PciePortProfileInstance;
  UINT16              PciePortIndex;
  UINT8               Index;
  LIST_ENTRY          *Node;
  PCIE_DEV_ENTRY      *PcieDevEntry;
  RAS_ERR_LOG_DATA    CxlErrLogData;
  CXL_ERROR_LOG_DATA  CxlErrorLogData;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a Start\n", __FUNCTION__);

  PciePortProfileInstance = mPlatformApeiPrivate->AmdPciePortMap->PciPortNumber;

  //Search only the active CXL 2.0 root port and the devices behind it for errors
  for (PciePortIndex = 0; PciePortIndex < mPlatformApeiPrivate->AmdPciePortMap->PortCount; PciePortIndex++, PciePortProfileInstance++) {
    if (!PciePortProfileInstance->CxlPresent ||
        (PciePortProfileInstance->CxlVersion != 2)) {
      continue;
    }

    //
    //CXL 2.0 Root Port
    //

    //  IO and protocol error detection: Cxl2p0RPErrStsCheck (), which is performed by PcieErrorScanDxe.

    if (PciePortProfileInstance->EndPointDevCnt == 0) {
      continue;
    }

    //
    //CXL devices behind CXL 2.0 Root Port
    //

    //  01. IO and protocol error detection: Cxl2p0DevErrStsCheck (,,,,CXL_DEV_CHK_IO_CACHE_MEM_ERROR_ONLY),
    //      which is performed by PcieErrorScanDxe.
    //  02. CXL Component Error detection is as follows:
    for (Index = 0; Index < PciePortProfileInstance->EndPointDevCnt; Index++) {
      if (Index == 0) {
        //IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
        //  "  Get First Node, Head Forward Link Addr: 0x%08x\n",PciePortProfileInstance->PciLinkList.ForwardLink);
        Node = GetFirstNode(&PciePortProfileInstance->PciLinkList);
      } else {
        //IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
        //"  Get Next Node, Current Node Address: 0x%08x\n",(UINTN)Node);
        Node = GetNextNode(&PciePortProfileInstance->PciLinkList, Node);
      }
      PcieDevEntry = (PCIE_DEV_ENTRY*)Node;

      CxlErrLogData.Buffer = &CxlErrorLogData;
      CxlErrLogData.RasLogCallback = CxlErrorLog;
      Cxl2p0DevErrStsCheck (PciePortProfileInstance, PcieDevEntry, &CxlErrLogData, 0, CXL_DEV_CHK_COMPONENT_ERROR_ONLY);
    }
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a End\n", __FUNCTION__);

  return EFI_SUCCESS;
}

EFI_STATUS
Cxl2p0RootPortDetect (
  UINTN                 RbBusEntryIndex,
  PCIE_PORT_PROFILE     *PciePortProfileInstance
  )
{
  PCI_ADDR                  PciPortAddr;
  UINT64                    PciSegAddr;
  UINT16                    PciePhy32gtEnhCapPtr;
  UINT32                    LinkStatus;
  UINT16                    CxlDvsecPtr;
  UINT16                    CxlPortStatus;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a - Start\n", __FUNCTION__);

  PciePortProfileInstance->CxlPresent = FALSE;
  PciePortProfileInstance->CxlVersion = 0;

  PciPortAddr.AddressValue = PciePortProfileInstance->RpPciAddr;

  PciSegAddr = PCI_SEGMENT_LIB_ADDRESS (PciPortAddr.Address.Segment,
                                        PciPortAddr.Address.Bus,
                                        PciPortAddr.Address.Device,
                                        PciPortAddr.Address.Function,
                                        PciPortAddr.Address.Register);

  LinkStatus = 0;
  PciePhy32gtEnhCapPtr = RasFindPcieExtendedCapability(PciPortAddr.AddressValue, PCIE_PHY_32GT_ENH_CAP_ID, 0xFFFF);
  if (PciePhy32gtEnhCapPtr != 0) {
    LinkStatus = PciSegmentRead32(PciSegAddr + PciePhy32gtEnhCapPtr + RECEIVED_MODIFIED_TS_DATA2_OFFSET);
  }
  if ((LinkStatus & (BIT25 | BIT24)) != (BIT25 | BIT24)) {  //[25:24] RECEIVED_ALTERNATE_PROTOCOL_NEGOTIATION_STATUS
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
      "  This is a CXL capable root port (0x%08X) but there are no CXL devices under this root port.(Status:0x%08X)\n",
      PciPortAddr.AddressValue,
      LinkStatus
    );
    return EFI_NOT_FOUND;
  }
  PciePortProfileInstance->CxlPresent = TRUE;

  CxlDvsecPtr = RasFindCxlDvsecCapability (PciPortAddr.AddressValue, PcieDvsecforFlexBusPort); //bugbug?
  CxlDvsecPtr = 0x57C;
  CxlPortStatus = 0;
  if (CxlDvsecPtr != 0) {
    CxlPortStatus = PciSegmentRead16 (PciSegAddr + CxlDvsecPtr + CXL_PORT_STATUS_OFFSET);
  }

  PciePortProfileInstance->CxlVersion = ((CxlPortStatus & BIT5 /*CXL2p0_ENABLED*/) == BIT5) ? 2 /*CXL 2.0*/ : 1/*CXL 1.1*/;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  This is a CXL capable root port (0x%08X), operating in CXL %a mode.\n",
    PciPortAddr.AddressValue, ((PciePortProfileInstance->CxlVersion == 2)? "2.0" : "1.1"));

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a - Exit\n", __FUNCTION__);

  return EFI_SUCCESS;
}

EFI_STATUS
Cxl2p0DeviceErrReportEn (VOID)  //CxlDeviceErrReportEn
{
  EFI_STATUS             Status;
  PCIE_PORT_PROFILE      *PciePortProfileInstance;
//  PCIe_AER_CONFIG_TEMP   PcieAerSetting;
  UINT16                 PciePortIndex;
  UINT8                  Index;
  LIST_ENTRY             *Node;
  PCIE_DEV_ENTRY         *PcieDevEntry;
  PCI_ADDR               RciepPciAddr;
  BOOLEAN                MpioIgnoreCxlRasVdm;
  PCI_ADDR               RpPciAddr;
  UINT32                 MpioStatus;
  UINT32                 MpioArg[6];

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] Cxl2p0DeviceErrReportEn - Entry\n");

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
    "[RAS]CxlComponentErrorReporting: 0x%02X, AllCxlDevsSupportSecMailbox: 0x%02X\n",
    mPlatformApeiPrivate->PlatRasPolicy.CxlComponentErrorReporting,
    mPlatformApeiPrivate->PlatRasPolicy.AllCxlDevsSupportSecMailbox
  );

  MpioIgnoreCxlRasVdm = FALSE;
  if ((mPlatformApeiPrivate->PlatRasPolicy.CxlComponentErrorReporting != DEBUG_FW_FIRST) &&
      !mPlatformApeiPrivate->PlatRasPolicy.AllCxlDevsSupportSecMailbox) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
      "FW_FIRST handling of CXL Component errors cannot be supported by the platform.\n"
    );
    MpioIgnoreCxlRasVdm = TRUE;
  }

  PciePortProfileInstance = mPlatformApeiPrivate->AmdPciePortMap->PciPortNumber;
  for (PciePortIndex = 0; PciePortIndex < mPlatformApeiPrivate->AmdPciePortMap->PortCount; PciePortIndex++, PciePortProfileInstance++) {
    if (!PciePortProfileInstance->CxlPresent ||
        (PciePortProfileInstance->CxlVersion != 2)) {
      continue;
    }

    //Check PCI devices under root port
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  RpPciAddr: 0x%08X, EndPointDevCnt : %d\n",
      PciePortProfileInstance->RpPciAddr,
      PciePortProfileInstance->EndPointDevCnt
    );
    if (PciePortProfileInstance->EndPointDevCnt == 0) {
      continue;
    }

    if (MpioIgnoreCxlRasVdm) {
      RpPciAddr.AddressValue = PciePortProfileInstance->RpPciAddr;
      RpPciAddr.Address.Function = 0;
      //Sends a message to MPIO FW to ignore CXL RAS VDMs
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Sends a message to MPIO FW to ignore CXL RAS VDMs\n");
      NbioMpioServiceCommonInitArguments (MpioArg);
      MpioArg[0] = 0; //0: Disable SMI generation, 1: Enable SMI generation
      MpioStatus = MpioServiceRequest (RpPciAddr, BIOS_MPIO_MSG_CXL_ERROR_FW_FIRST, MpioArg, 0);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Response from MPIO: %x\n", MpioStatus);
      continue;
    }

    //Search for CXL devices and set EventIntrPolicy on them
    for (Index = 0; Index <PciePortProfileInstance->EndPointDevCnt; Index++) {
      if (Index == 0) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Get First Node, Head Forward Link Addr: 0x%08x\n",PciePortProfileInstance->PciLinkList.ForwardLink);
        Node = GetFirstNode(&PciePortProfileInstance->PciLinkList);
      } else {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Get Next Node, Current Node Address: 0x%08x\n",(UINTN)Node);
        Node = GetNextNode(&PciePortProfileInstance->PciLinkList, Node);
      }
      PcieDevEntry = (PCIE_DEV_ENTRY*)Node;
      RciepPciAddr.AddressValue = PcieDevEntry->DevAddr;
      if (RasFindCxlDvsecCapability (RciepPciAddr.AddressValue, PcieDvsecforCxlDevice) == 0) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, " [PCI address: 0x%08X] is not a CXL device. (PCIe DVSEC for CXL Device not found)\n",
          RciepPciAddr.AddressValue);
        continue;
      }

      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, " [PCI address: 0x%08X] is a CXL device.\n",
        RciepPciAddr.AddressValue);

      //
      //CXL 2.0 RciEp AER is set by PCIe RAS handler
      //
      //PcieAerSetting.AerEnable = 1;
      //PcieAerSetting.PciBus = (UINT8)RciepPciAddr.Address.Bus;
      //PcieAerSetting.PciDev = (UINT8)RciepPciAddr.Address.Device;
      //PcieAerSetting.PciFunc = (UINT8)RciepPciAddr.Address.Function;
      //PcieAerSetting.CorrectableMask = mPlatformApeiPrivate->PlatRasPolicy.PcieDevCorrectedErrorMask;
      //PcieAerSetting.UncorrectableMask = mPlatformApeiPrivate->PlatRasPolicy.PcieDevUnCorrectedErrorMask;
      //PcieAerSetting.UncorrectableSeverity = mPlatformApeiPrivate->PlatRasPolicy.PcieDevUnCorrectedErrorSeverity;
      //IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Device Correctable Error Mask: 0x%08x\n", PcieAerSetting.CorrectableMask);
      //IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Device UnCorrectable Error Mask: 0x%08x\n", PcieAerSetting.UncorrectableMask);
      //IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Device UnCorrectable Error Severity: 0x%08x\n", PcieAerSetting.UncorrectableSeverity);
      //PcieDevCntlEnableOnDevice (RciepPciAddr, &PcieAerSetting);

      Status = CxlSetEventIntrPolicy(RciepPciAddr.AddressValue, PciePortProfileInstance->NbioDieNum); //NbioDieNum = mRbBusMap->RbBusEntry[Rb].RbIndex
      if (Status != EFI_SUCCESS) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  CXL Memory Device error reoprt not enabled\n");
      } else {
        //Event Interrupt Ploicy setting readback
        CxlGetEventIntrPolicy(RciepPciAddr.AddressValue, PciePortProfileInstance->NbioDieNum);
      }
    }
  }

  return EFI_SUCCESS;
}

BOOLEAN
AllCxlDevsSupSecMbx ()
{
  BOOLEAN             CxlDevSupPriAndSecMbx;
  RCEC_PROFILE        *RcecProfileInstance;
  UINTN               RcecIndex;
  UINT8               Index;
  LIST_ENTRY          *Node;
  CXL_RCIEP_ENTRY     *CxlRciepEntry;
  UINT64              CxlMemDeviceRegAddrBase;
  UINT32              MailboxRegLength;
  PCIE_PORT_PROFILE   *PciePortProfileInstance;
  UINT16              PciePortIndex;
  PCIE_DEV_ENTRY      *PcieDevEntry;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a Start\n", __FUNCTION__);

  CxlDevSupPriAndSecMbx = FALSE;

  //CXL 1.1
  RcecProfileInstance = mPlatformApeiPrivate->CxlMap->RcecProfile;
  for (RcecIndex = 0; RcecIndex < mPlatformApeiPrivate->CxlMap->RcecCount; RcecIndex++, RcecProfileInstance++) {
    if (RcecProfileInstance->RciepCnt == 0) {
      continue;
    }
    for (Index = 0; Index < RcecProfileInstance->RciepCnt; Index++) {
      if (Index == 0) {
        Node = GetFirstNode(&RcecProfileInstance->RciepLinkList);
      } else {
        Node = GetNextNode(&RcecProfileInstance->RciepLinkList, Node);
      }
      CxlRciepEntry = (CXL_RCIEP_ENTRY*)Node;
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  RCiEP/RP Address: 0x%08x, DevType\n", CxlRciepEntry->DevAddr, CxlRciepEntry->DevType);

      CxlMemDeviceRegAddrBase = RasGetRciepRegisterBlockAddress (CxlRciepEntry->DevAddr, CxlMemoryDeviceRegistersBlkId);
      if (CxlMemDeviceRegAddrBase == 0) {
        continue;
      }
      if (CxlGetPrimaryMailboxRegsitersAddress (CxlMemDeviceRegAddrBase, &MailboxRegLength) == 0) {
        continue;
      }
      if (CxlGetSecondaryMailboxRegsitersAddress (CxlMemDeviceRegAddrBase, &MailboxRegLength) == 0) {
        return FALSE; //At least one CXL device has a primary mailbox but no secondary mailbox, so return False.
      }

      CxlDevSupPriAndSecMbx = TRUE;
    }
  }

  //CXL 2.0
  //Searches only devices behind an active CXL 2.0 root port to check if a secondary mailbox exists.
  PciePortProfileInstance = mPlatformApeiPrivate->AmdPciePortMap->PciPortNumber;
  for (PciePortIndex = 0; PciePortIndex < mPlatformApeiPrivate->AmdPciePortMap->PortCount; PciePortIndex++, PciePortProfileInstance++) {
    if (!PciePortProfileInstance->CxlPresent ||
        (PciePortProfileInstance->CxlVersion != 2)) {
      continue;
    }

    if (PciePortProfileInstance->EndPointDevCnt == 0) {
      continue;
    }

    //
    //CXL devices behind CXL 2.0 Root Port
    //
    for (Index = 0; Index < PciePortProfileInstance->EndPointDevCnt; Index++) {
      if (Index == 0) {
        //IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
        //  "  Get First Node, Head Forward Link Addr: 0x%08x\n",PciePortProfileInstance->PciLinkList.ForwardLink);
        Node = GetFirstNode(&PciePortProfileInstance->PciLinkList);
      } else {
        //IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
        //"  Get Next Node, Current Node Address: 0x%08x\n",(UINTN)Node);
        Node = GetNextNode(&PciePortProfileInstance->PciLinkList, Node);
      }
      PcieDevEntry = (PCIE_DEV_ENTRY*)Node;

      CxlMemDeviceRegAddrBase = RasGetRciepRegisterBlockAddress (PcieDevEntry->DevAddr, CxlMemoryDeviceRegistersBlkId);
      if (CxlMemDeviceRegAddrBase == 0) {
        continue;
      }
      if (CxlGetPrimaryMailboxRegsitersAddress (CxlMemDeviceRegAddrBase, &MailboxRegLength) == 0) {
        continue;
      }
      if (CxlGetSecondaryMailboxRegsitersAddress (CxlMemDeviceRegAddrBase, &MailboxRegLength) == 0) {
        return FALSE; //At least one CXL device has a primary mailbox but no secondary mailbox, so return False.
      }

      CxlDevSupPriAndSecMbx = TRUE;
    }
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a End\n", __FUNCTION__);

  return CxlDevSupPriAndSecMbx;
}

VOID
CheckIfAllCxlDevsSupSecMbx ( VOID )
{
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] CheckIfAllCxlDevsSupSecMbx Entry\n");

  //
  // PcdAmdCxlComponentErrorReporting
  //
  // If set to ALLOW_OS_FIRST:
  //     * If all CXL.Mem devices support the Secondary mailbox, AGESA:
  //     * Configures CXL.Mem devices for FW_FIRST error handling using the Secondary mailbox.
  //     * Prepares to undo FW_FIRST error handling if the OS requests control of "CXL Memory Error Reporting Control" via the CXL _OSC.
  //     * Prepares to grant control of "CXL Memory Error Reporting Control" via the CXL _OSC.
  //
  // If set to FORCE_FW_FIRST:
  //     * If all CXL.Mem devices support the Secondary mailbox, AGESA:
  //     * Configures CXL.Mem devices for FW_FIRST error handling using the Secondary mailbox.
  // Else, AGESA:
  //     * Displays a "Warning" message to the effect of "One or more CXL.Mem devices do not support the
  //       Secondary Mailbox, hence FW_FIRST handling of CXL Component errors cannot be supported by the platform",
  //     * Prepares to deny control of "CXL Memory Error Reporting Control" via the CXL _OSC.
  //
  // If set to DEBUG_FW_FIRST:
  //     * Configures CXL.Mem devices for FW_FIRST error handling using the Secondary mailbox if available, otherwise using the Primary mailbox.
  //     * Prepares to deny control of "CXL Memory Error Reporting Control" via the CXL _OSC.

  if (AllCxlDevsSupSecMbx ()) {
    mPlatformApeiPrivate->PlatRasPolicy.AllCxlDevsSupportSecMailbox = TRUE;
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
      "All CXL.Mem devices support the Secondary Mailbox.\n"
    );
    mPlatformApeiPrivate->RasAcpiSmmData->CxlOscCtrl.Value = 0;  //0: Denies control to the OS via CXL _OSC
  } else {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
      "[Warning] One or more CXL.Mem devices do not support the Secondary Mailbox,\n"
    );
    if (mPlatformApeiPrivate->PlatRasPolicy.CxlComponentErrorReporting == DEBUG_FW_FIRST) {
      mPlatformApeiPrivate->RasAcpiSmmData->CxlOscCtrl.Value = 0;  //0: Denies control to the OS via CXL _OSC
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
        "[Warning] DEBUG_FW_FIRST mode, deny OS control request.\n"
      );
    } else {
      PcdSet8S (PcdAmdCxlComponentErrorReporting, ALLOW_OS_FIRST);
      mPlatformApeiPrivate->PlatRasPolicy.CxlComponentErrorReporting = ALLOW_OS_FIRST;
      mPlatformApeiPrivate->RasAcpiSmmData->CxlOscCtrl.Value = 1;  //1: Grants control to the OS via CXL _OSC
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
        "[Warning] Not DEBUG_FW_FIRST mode, hence FW_FIRST handling of CXL Component errors cannot be supported by the platform. \n \
          Set to ALLOW_OS_FIRST and grant OS control request.\n"
      );
    }
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] CheckIfAllCxlDevsSupSecMbx End\n");
}