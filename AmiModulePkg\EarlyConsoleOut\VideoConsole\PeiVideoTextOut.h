//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************


/** @file PeiVideoTextOut.h
    Definitions used by PeiVideoTextOut.

**/

#ifndef _PEI_VIDEO_TEXT_OUT_H__
#define _PEI_VIDEO_TEXT_OUT_H__

#include <Library/HobLib.h>
#include <Library/PrintLib.h>
#include <Library/DebugLib.h>
#include <Library/MemoryAllocationLib.h>
#include <Library/PeiServicesLib.h>
#include <Library/EarlyConsoleInterfaceCommonLib.h>
#include <Library/AmiVideoTextOutLib.h>
#include <Ppi/AmiSimpleTextOutPpi.h>
#include <Protocol/SimpleTextOut.h>
#include <AmiSimpleTextOutHob.h>
#include <Token.h>
#include <Library/PeiServicesLib.h>


typedef struct {
    AMI_SIMPLE_TEXT_OUTPUT_PPI          AmiPeiSimpleTextOut;
    EFI_SIMPLE_TEXT_OUTPUT_MODE         Mode;
    UINT32                              MaxRows;    ///< Max number of rows in current mode
    UINT32                              MaxColumns; ///< Max number of columns in current mode
} AMI_VIDEO_TEXT_OUT_PRIVATE_DATA;

/**
  Report Max display depth and current cursor position as guid HOB 
  so that DxeVideoTextOut driver can use it.

  @param PeiServices       An indirect pointer to the EFI_PEI_SERVICES table published by the PEI Foundation
  @param NotifyDescriptor  Address of the notification descriptor data structure.
  @param Ppi               Address of the PPI that was installed.

  @retval EFI_SUCCESS      Successfully update the Display records.
**/
EFI_STATUS
EFIAPI
PeiVideoTextOutNotifyCallback (
  IN EFI_PEI_SERVICES           **PeiServices,
  IN EFI_PEI_NOTIFY_DESCRIPTOR  *NotifyDescriptor,
  IN VOID                       *Ppi
);
#endif
