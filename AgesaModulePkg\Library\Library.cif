<component>
    name = "Library"
    category = ModulePart
    LocalRoot = "AgesaModulePkg\Library\"
    RefName = "AgesaModulePkg.Library"
[parts]
"AgesaModulePkg.Library.AcpiTableHelperLib"
"AgesaModulePkg.Library.AgesaConfigLib"
"AgesaModulePkg.Library.AmdAcpiAmlLib"
"AgesaModulePkg.Library.AmdBaseLib"
"AgesaModulePkg.Library.AmdCalloutLib"
"AgesaModulePkg.Library.AmdCapsuleLibDxe"
"AgesaModulePkg.Library.AmdCapsuleLibPei"
"AgesaModulePkg.Library.AmdCfgPcdBufLibDxe"
"AgesaModulePkg.Library.AmdCfgPcdBufLibNull"
"AgesaModulePkg.Library.AmdCfgPcdBufLibPei"
"AgesaModulePkg.Library.AmdDirectoryBaseLib"
"AgesaModulePkg.Library.AmdEmulationAutoDetectDxeLib"
"AgesaModulePkg.Library.AmdEmulationAutoDetectPeiLib"
"AgesaModulePkg.Library.AmdEmulationFlagLib"
"AgesaModulePkg.Library.AmdErrorLogLib"
"AgesaModulePkg.Library.AmdHeapDxeLib"
"AgesaModulePkg.Library.AmdHeapLibNull"
"AgesaModulePkg.Library.AmdHeapPeiLib"
"AgesaModulePkg.Library.AmdIdsDebugPrintLib"
"AgesaModulePkg.Library.AmdIdsExtLibNull"
"AgesaModulePkg.Library.AmdIdsHookLibDxe"
"AgesaModulePkg.Library.AmdIdsHookLibNull"
"AgesaModulePkg.Library.AmdIdsHookLibPei"
"AgesaModulePkg.Library.AmdMpmRegBaseLib"
"AgesaModulePkg.Library.AmdPostCodeEmuLib"
"AgesaModulePkg.Library.AmdPostCodeEmuLib2"
"AgesaModulePkg.Library.AmdPostCodeLib"
"AgesaModulePkg.Library.AmdPspApobLib"
"AgesaModulePkg.Library.AmdPspBarInitLibV2"
"AgesaModulePkg.Library.AmdPspBaseLibV2"
"AgesaModulePkg.Library.AmdPspCommonLibDxe"
"AgesaModulePkg.Library.AmdPspCommonLibPei"
"AgesaModulePkg.Library.AmdPspDxeSmmBufLib"
"AgesaModulePkg.Library.AmdPspFlashAccLibDxe"
"AgesaModulePkg.Library.AmdPspFlashAccLibNull"
"AgesaModulePkg.Library.AmdPspFlashUpdateLib"
"AgesaModulePkg.Library.AmdPspFwImageHeaderLib"
"AgesaModulePkg.Library.AmdPspHstiStateLib"
"AgesaModulePkg.Library.AmdPspMboxLibV2"
"AgesaModulePkg.Library.AmdPspMmioLib"
"AgesaModulePkg.Library.AmdPspMmioLibSmmIso"
"AgesaModulePkg.Library.AmdPspPsbFusingLib"
"AgesaModulePkg.Library.AmdPspRegBaseLib"
"AgesaModulePkg.Library.AmdPspRegMuxLibV2Dxe"
"AgesaModulePkg.Library.AmdPspRegMuxLibV2DxeRt"
"AgesaModulePkg.Library.AmdPspRegMuxLibV2Null"
"AgesaModulePkg.Library.AmdPspRomArmorLib"
"AgesaModulePkg.Library.AmdPspRomArmorLibNull"
"AgesaModulePkg.Library.AmdPspSfsLib"
"AgesaModulePkg.Library.AmdRtclib"
"AgesaModulePkg.Library.AmdS3SaveLib"
"AgesaModulePkg.Library.AmdSocBaseLib"
"AgesaModulePkg.Library.AmdStbLib"
"AgesaModulePkg.Library.AmdStbLibNull"
"AgesaModulePkg.Library.AmdTableLibV2"
"AgesaModulePkg.Library.ApcbCoreLib"
"AgesaModulePkg.Library.ApcbHmacChecksumLibV3"
"AgesaModulePkg.Library.ApcbLibV3"
"AgesaModulePkg.Library.ApcbLibV3Pei"
"AgesaModulePkg.Library.ApcbVariableLibV3"
"AgesaModulePkg.Library.ApobApcbLib"
"AgesaModulePkg.Library.ApobApcbUpdatesBrhLib"
"AgesaModulePkg.Library.ApobBrhLib"
"AgesaModulePkg.Library.ApobCommonServiceLibDxe"
"AgesaModulePkg.Library.ApobCommonServiceLibPei"
"AgesaModulePkg.Library.ApobDummyLib"
"AgesaModulePkg.Library.BaseCoreLogicalIdX86Lib"
"AgesaModulePkg.Library.BaseFabricTopologyBrhLib"
"AgesaModulePkg.Library.BaseSocketLogicalIdRsDieLib"
"AgesaModulePkg.Library.BaseSocLogicalIdXlatZen5DieLib"
"AgesaModulePkg.Library.BxbNbio"
"AgesaModulePkg.Library.CcxApicZen5Lib"
"AgesaModulePkg.Library.CcxBaseX86Lib"
"AgesaModulePkg.Library.CcxHaltLib"
"AgesaModulePkg.Library.CcxIdsCustomPstateNullLib"
"AgesaModulePkg.Library.CcxMicrocodePatchLib"
"AgesaModulePkg.Library.CcxMpServicesDxeLib"
"AgesaModulePkg.Library.CcxNonSmmResumeSecLib"
"AgesaModulePkg.Library.CcxPspLib"
"AgesaModulePkg.Library.CcxPstatesZen5Lib"
"AgesaModulePkg.Library.CcxResetTablesZen5Lib"
"AgesaModulePkg.Library.CcxRolesX86Lib"
"AgesaModulePkg.Library.CcxRolesZen5Lib"
"AgesaModulePkg.Library.CcxSetMcaZen5Lib"
"AgesaModulePkg.Library.CcxSetMmioCfgBaseLib"
"AgesaModulePkg.Library.CcxSmbiosZen5Lib"
"AgesaModulePkg.Library.CcxTscTimerLib"
"AgesaModulePkg.Library.CcxZen5BrhDxeLib"
"AgesaModulePkg.Library.CcxZen5BrhIdsHookLib"
"AgesaModulePkg.Library.CcxZen5SegRmpBrhLib"
"AgesaModulePkg.Library.DashAsfBaseLib"
"AgesaModulePkg.Library.DxeAmdPciHostBridgeLib"
"AgesaModulePkg.Library.DxeAmlGenerationLib"
"AgesaModulePkg.Library.DxeCcxBaseX86ServicesLib"
"AgesaModulePkg.Library.DxeCcxCcdReorderZen5Lib"
"AgesaModulePkg.Library.DxeCcxCoreTopologyServicesV3BrhLib"
"AgesaModulePkg.Library.DxeCcxCoreTopologyServicesV3OnV2Lib"
"AgesaModulePkg.Library.DxeCcxCppcLib"
"AgesaModulePkg.Library.DxeCcxSmmAccess2Lib"
"AgesaModulePkg.Library.DxeCoreTopologyV3Lib"
"AgesaModulePkg.Library.DxeFabricResourceManagerServicesLib"
"AgesaModulePkg.Library.DxeFabricResourceSizeForEachRbLib"
"AgesaModulePkg.Library.DxeFabricSocSpecificServicesBrhLib"
"AgesaModulePkg.Library.DxeFabricTopologyServices2Lib"
"AgesaModulePkg.Library.DxeSocLogicalIdServicesLib"
"AgesaModulePkg.Library.DxeSocZen5ServicesBrhLib"
"AgesaModulePkg.Library.FabricIdsHookBrhLib"
"AgesaModulePkg.Library.FabricRegisterAccDf4Lib"
"AgesaModulePkg.Library.FabricResourceManagerBrhLib"
"AgesaModulePkg.Library.FabricResourceManagerLibNull"
"AgesaModulePkg.Library.FabricResourceReportToGcdLib"
"AgesaModulePkg.Library.FabricResourceReportToGcdNullLib"
"AgesaModulePkg.Library.FabricRootBridgeOrderLib"
"AgesaModulePkg.Library.FabricWdtDf4Lib"
"AgesaModulePkg.Library.FchBaseLib"
"AgesaModulePkg.Library.FchBaseResetSystemLib"
"AgesaModulePkg.Library.FchDxeLegacyInterruptLib"
"AgesaModulePkg.Library.FchDxeLib"
"AgesaModulePkg.Library.FchDxeRuntimeResetSystemLib"
"AgesaModulePkg.Library.FchEspiCmdLib"
"AgesaModulePkg.Library.FchEspiLib"
"AgesaModulePkg.Library.FchI2cLib"
"AgesaModulePkg.Library.FchI3cLib"
"AgesaModulePkg.Library.FchIdsHookBrhLib"
"AgesaModulePkg.Library.FchIdsHookLib"
"AgesaModulePkg.Library.FchInitHookLib"
"AgesaModulePkg.Library.FchPeiLib"
"AgesaModulePkg.Library.FchSmmLib"
"AgesaModulePkg.Library.FchSocLib"
"AgesaModulePkg.Library.FchSpiAccessLib"
"AgesaModulePkg.Library.GnbCommonLib"
"AgesaModulePkg.Library.GnbCpuAccLib"
"AgesaModulePkg.Library.GnbHeapDxeLib"
"AgesaModulePkg.Library.GnbIoAccLib"
"AgesaModulePkg.Library.GnbLib"
"AgesaModulePkg.Library.GnbMemAccLib"
"AgesaModulePkg.Library.GnbPciLib"
"AgesaModulePkg.Library.GnbPciSegmentAccLib"
"AgesaModulePkg.Library.IdsDxeLib"
"AgesaModulePkg.Library.IdsHookLibTableNull"
"AgesaModulePkg.Library.IdsLibNull"
"AgesaModulePkg.Library.IdsMiscLib"
"AgesaModulePkg.Library.IdsNonUefiLib"
"AgesaModulePkg.Library.IdsPeiLib"
"AgesaModulePkg.Library.Mem"
"AgesaModulePkg.Library.MemRestoreLib"
"AgesaModulePkg.Library.MemSmbiosV2BrhD5Lib"
"AgesaModulePkg.Library.MemUmcAccessLib"
"AgesaModulePkg.Library.NbioClkReqControlLibNull"
"AgesaModulePkg.Library.NbioHandleLib"
"AgesaModulePkg.Library.NbioRegisterAccLib"
"AgesaModulePkg.Library.NbioServicesLib"
"AgesaModulePkg.Library.NbioSmuBrhLib"
"AgesaModulePkg.Library.NbioUtilLib"
"AgesaModulePkg.Library.PcieConfigLib"
"AgesaModulePkg.Library.PcieMiscCommLib"
"AgesaModulePkg.Library.PeiCcxCoreTopologyServicesV3BrhLib"
"AgesaModulePkg.Library.PeiCcxSmmAccessLib"
"AgesaModulePkg.Library.PeiCoreTopologyV3Lib"
"AgesaModulePkg.Library.PeiFabricResourceManagerServicesLib"
"AgesaModulePkg.Library.PeiFabricResourceSizeForEachRbLib"
"AgesaModulePkg.Library.PeiFabricSocSpecificServicesBrhLib"
"AgesaModulePkg.Library.PeiFabricTopologyServices2Lib"
"AgesaModulePkg.Library.PeiSocBistLogging2Lib"
"AgesaModulePkg.Library.PeiSocBistLogging3Lib"
"AgesaModulePkg.Library.PeiSocBistZen5CcdBrhLib"
"AgesaModulePkg.Library.PeiSocBistZen5CcdLib"
"AgesaModulePkg.Library.PeiSocLogicalIdServicesLib"
"AgesaModulePkg.Library.PeiSocZen5ServicesBrhLib"
"AgesaModulePkg.Library.PmMpDmaArsLib"
"AgesaModulePkg.Library.PresiliconControlBrhLib"
"AgesaModulePkg.Library.Ras"
"AgesaModulePkg.Library.SmmFabricTopologyServices2Lib"
"AgesaModulePkg.Library.SmnAccessLib"
"AgesaModulePkg.Library.SocCmnIdsHookBrhLib"
"AgesaModulePkg.Library.SocCoreInfo2AccessLib"
"AgesaModulePkg.Library.ApcbTokenWhiteListBrhLib"
"AgesaModulePkg.Library.ApcbTokenWhiteListNullLib"
<endComponent>
