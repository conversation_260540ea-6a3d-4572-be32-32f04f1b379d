TOKEN
    Name  = "Crypto_SUPPORT"
    Value  = "1"
    Help  = "Switch for Enabling Driver support in the project"
    TokenType = Boolean
    Master = Yes
    TargetMAK = Yes
    Token = "BaseCryptLibBin" "=" "0"
    Token = "APTIOV_TOOLS_VERSION" ">=" "37"
End

INFComponent
    Name  = "CryptoDxe"
    File  = "CryptoDxe.inf"
    Package  = "CryptoPkg"
    ModuleTypes  = "DXE_DRIVER"
    TOKEN = "BUILD_OPENSSL_WITH_SOCKET" "=" "0"
End

INFComponent
    Name  = "CryptoPei"
    File  = "CryptoPei.inf"
    Package  = "CryptoPkg"
    ModuleTypes  = "PEIM"
    TOKEN = "BUILD_OPENSSL_WITH_SOCKET" "=" "0"
    Token = "BUILD_EDKII_PEI_CRYPT_LIB" "=" "1"
End

INFComponent
    Name  = "CryptoSmm"
    File  = "CryptoSmm.inf"
    Package  = "CryptoPkg"
    ModuleTypes  = "DXE_SMM_DRIVER"
    Token = "SMM_SUPPORT" "=" "1"
    TOKEN = "BUILD_OPENSSL_WITH_SOCKET" "=" "0"
End

INFComponent
    Name  = "CryptoStandaloneMm"
    File  = "CryptoStandaloneMm.inf"
    Package  = "CryptoPkg"
    ModuleTypes  = "MM_STANDALONE"
    TOKEN = "BUILD_OPENSSL_WITH_SOCKET" "=" "0"
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Sha1.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoPei"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Sha256.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoPei"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Sha384.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoPei"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Sha512.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoPei"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.HmacMd5.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.HmacSha1.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.HmacSha256.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.HmacSha384.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Md4.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Md5.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Pkcs.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Dh.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Random.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Rsa.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Sha1.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Sha256.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Sha384.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Sha512.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.X509.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Tdes.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Aes.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Arc4.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Sm3.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Hkdf.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Tls.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.TlsSet.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.TlsGet.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.RsaPss.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.ParallelHash.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.AeadAesGcm.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Bn.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Ec.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoDxe"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.HmacMd5.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.HmacSha1.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.HmacSha256.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.HmacSha384.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Md4.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Md5.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Pkcs.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Dh.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Random.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Rsa.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Sha1.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Sha256.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Sha384.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Sha512.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.X509.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Tdes.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Aes.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Arc4.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Sm3.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Hkdf.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Tls.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.TlsSet.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.TlsGet.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.RsaPss.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.ParallelHash.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.AeadAesGcm.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Bn.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Ec.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoSmm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.HmacMd5.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.HmacSha1.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.HmacSha256.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.HmacSha384.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Md4.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Md5.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Pkcs.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Dh.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Random.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Rsa.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Sha1.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Sha256.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Sha384.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Sha512.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.X509.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Tdes.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Aes.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Arc4.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Sm3.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Hkdf.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Tls.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.TlsSet.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.TlsGet.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.RsaPss.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.ParallelHash.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.AeadAesGcm.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Bn.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

PcdMapping
    Name  = "PcdCryptoServiceFamilyEnable.Ec.Family"
    GuidSpace  = "gEfiCryptoPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT32"
    Value  = "0xFFFFFFFF"
    Override  = "CryptoPkg.CryptoStandaloneMm"
    Offset  = 00h
    Length  = 01h
    TargetDSC = Yes
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

LibraryMapping
    Class  = "TimerLib"
    Instance  = "MdePkg.BaseTimerLibNullTemplate"
    Override = "CryptoPkg.CryptoDxe"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

LibraryMapping
    Class  = "TimerLib"
    Instance  = "MdePkg.BaseTimerLibNullTemplate"
    Override = "CryptoPkg.CryptoSmm"
    Token = "SMM_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End

LibraryMapping
    Class  = "TimerLib"
    Instance  = "MdePkg.BaseTimerLibNullTemplate"
    Override = "CryptoPkg.CryptoStandaloneMm"
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    Token = "CREATE_CRYPTO_BIN" "=" "1"
End
