/*******************************************************************************
*
 * Copyright (C) 2023-2025 Advanced Micro Devices, Inc. All rights reserved.
 *
*******************************************************************************/

#include <MyPorting.h>
#include <MiscMemDefines.h>
#include <APCB.h>
#include "ApcbCustomizedBoardDefinitions.h"

//
// DDR5 training override for MsgBlock before training & PHY register after training
// Note: the .DimmPartNumber field must copy full 30 bytes of DIMM SPD part number.
// do not change blank character (0x20) to zero.

// "ChannelMask" in following table is POSITIVE logic, 1 == apply
//
// TRAINING_OVERRIDE_ENTRY_S TrainingOverride[] = {
//   {
//     Header = {
//       Length         = sizeof (TRAINING_OVERRIDE_ENTRY_HEADER_S),
//       DimmPartNumber = "HMCG94AHBRA277N               ",
//       MemClkMask     = 0x8,
//       ChannelMask    = 0xFFA,
//       OffsetData     = 0x0011,
//     },
//   },
//

APCB_TYPE_DATA_START_SIGNATURE();
APCB_V3_TYPE_HEADER       ApcbTypeHeader = {
  APCB_GROUP_MEMORY,                          // GroupId
  APCB_MEM_TYPE_DDR5_TRAINING_OVERRIDE,       // TypeId
  sizeof(ApcbTypeHeader),                     // SizeOfType, will be fixed up by tool
  0,                                          // InstanceId
  {
    APCB_TYPE_ATTR_CONTEXT_TYPE_STRUCT,
    APCB_TYPE_ATTR_CONTEXT_FORMAT_NATIVE_RAW,
    0,
    APCB_PRIORITY_TYPE_MASK_DEFAULT,          // Priority mask
    0,
    0,
    BLDCFG_APCB_DATA_BOARD_MASK               // Board specific APCB instance mask
  }
};  // SizeOfType will be fixed up by tool
/*
TRAINING_OVERRIDE_ENTRY_S TrainingOverride[] = {
  {
    .Header = {
      .Length         = sizeof (TRAINING_OVERRIDE_ENTRY_HEADER_S),
      .DimmPartNumber = "                              ",
//                       ----+----1----+----2----+----3
      .MemClkMask     = 0x8,
      .ChannelMask    = 0xFFA,
      .OffsetData     = 0x0011,
    },
  },
};
*/
APCB_TYPE_DATA_END_SIGNATURE();
APCB_DUMMY_MAIN_FUNC();

