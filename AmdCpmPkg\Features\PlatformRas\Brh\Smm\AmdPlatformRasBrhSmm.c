/*****************************************************************************
 *
 * Copyright (C) 2018-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */


/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Library/BaseMemoryLib.h>
#include <Library/BaseLib.h>
#include "AmdPlatformRasBrhSmm.h"
#include <Library/IdsLib.h>
#include <Library/SmmMemLib.h>


/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */
AMD_RAS_SERVICE_SMM_PROTOCOL    *mAmdRasServiceSmmProtocol;
PLATFORM_APEI_PRIVATE_BUFFER_V3 *mPlatformApeiData;
RB_BUS_MAP                      *mRbBusMap;
AMD_PSP_ARS_SERVICE_PROTOCOL    *mAmdPspArsServiceProtocol;
EFI_HANDLE                      AmdRasMaintenanceHandle;
BOOLEAN                         mDxePciActivePortMapIsInvalid = FALSE;
UINT8                           *mPspMboxSmmBuffer = NULL;
BOOLEAN                         *mPspMboxSmmFlagAddr = NULL;

RAS_MAINT_PCIEACTIVEPORTMAP_PROTOCOL RasMaintPcieActivePortMapProtocol = {
  RasRebuildByRootPort
};

AMD_ASYNC_HP_EDR_PROTOCOL AmdAsyncHpEdrProtocol = {
    NULL,
    NULL,
    PerformEdr,
    OsEdrEnabled
};

ROOTPORT_VS_NVMESLOT_DESC RootPortVsNvmeSlotMappingTable[TOTAL_NVME_SLOT];

#define MAX_NUM_EDR_PORTS (128)
PCI_ADDR OsEdrEnabledAddressTable[MAX_NUM_EDR_PORTS];

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

VOID
STATIC
PciAppendList (
  IN       PCI_ADDR          Device,
  IN       PCIE_DEVICE_TYPE  DeviceType,
  IN       PCIE_PORT_PROFILE *PciePortProfile
  );


EFI_STATUS
EFIAPI
RegisterCxlRasSmiCallback (
  IN CONST EFI_GUID     *Protocol,
  IN VOID               *Interface,
  IN EFI_HANDLE         Handle
  );


EFI_STATUS
UpdateOemDimmFruTextToMca(
  IN       OEM_MEMORY_MAP_TABLE *OemMemoryMapTable
  );

EFI_STATUS
PassOemMcaThresholdMapTableToAGESA(
  IN       OEM_MCA_THRESHOLD_MAP_TABLE *OemMcaThresholdMapTable
);

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */


/*---------------------------------------------------------------------------------------*/
/**
 * RasSmmRegistrationCenter
 *
 * RAS SMI handle registery function
 *
 *
 * @param[in/out]  None
 *
 */
EFI_STATUS
RasSmmRegistrationCenter (
  VOID
  )
{
  EFI_STATUS     Status = EFI_SUCCESS;
  VOID           *Registration;

  if (mPlatformApeiData->PlatRasPolicy.PFEHEnable) {
    Status = RasSmmRegisterMceSwSmi ();
    if (EFI_ERROR(Status)) {
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] RasSmmRegisterMceSwSmi failed \n");
    }
  }

  if (mPlatformApeiData->PlatRasPolicy.FchApuRasSmiSupport) {
    if (mPlatformApeiData->PlatRasPolicy.PcieAerReportMechanism == 2 || mPlatformApeiData->PlatRasPolicy.PcieAerReportMechanism == 3) { //FW First / FW First but allow OS First
      Status = RasSmmRegisterPcieLegacyRasSmi ();
    }
  }

  if (PcdGet8 (PcdAmdHotPlugHandlingMode) == 3 || PcdGet8 (PcdAmdHotPlugHandlingMode) == 5 || PcdGet8 (PcdAmdHotPlugHandlingMode) == 6) {
    Status = RasSmmRegisterEdrSwSmi ();
  }

  if (mPlatformApeiData->PlatRasPolicy.CpmCxlErrorReport) {
    Status = gSmst->SmmRegisterProtocolNotify (
                      &gEdkiiSmmReadyToBootProtocolGuid,
                      RegisterCxlRasSmiCallback,
                      &Registration
                      );
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] CXL RegisterProtocolNotify - RegisterCxlRasSmiCallback: %r\n", Status);
  }

  if (mPlatformApeiData->PlatRasPolicy.AmdMcaFruTextEnable) {
    if (mPlatformApeiData->OemMemoryMapTable != NULL) {
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]Update DIMM FRU Text to MCA\n");
      Status = UpdateOemDimmFruTextToMca (mPlatformApeiData->OemMemoryMapTable);
      if (EFI_ERROR(Status)) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] Failed to update DIMM FRU Text to MCA\n");
      }
    }
  }


  if (mPlatformApeiData->OemMcaThresholdMapTable != NULL) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]PASS OEM MCA THRESHOLD MAP TABLE to AGESA RAS\n");
    Status = PassOemMcaThresholdMapTableToAGESA (mPlatformApeiData->OemMcaThresholdMapTable);
    if (EFI_ERROR(Status)) {
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] Failed to PASS OEM MCA THRESHOLD MAP TABLE to AGESA RAS\n");
    }
  }
  return Status;
}

/*********************************************************************************
 * Name: AmdPlatformRasBrhSmmInit
 *
 * Description
 *   Entry point of the AMD PLATFORM BRH RAS SMM driver
 *   Register Ras Smm callbacks
 *
 * Input
 *   ImageHandle : EFI Image Handle for the DXE driver
 *   SystemTable : pointer to the EFI system table
 *
 * Output
 *   EFI_SUCCESS : Module initialized successfully
 *   EFI_ERROR   : Initialization failed (see error for more details)
 *
 *********************************************************************************/
EFI_STATUS
EFIAPI
AmdPlatformRasBrhSmmInit (
  IN       EFI_HANDLE         ImageHandle,
  IN       EFI_SYSTEM_TABLE   *SystemTable
  )

{
  EFI_STATUS                           Status;
  PSP_MBOX_SMMBUFFER_ADDRESS_PROTOCOL  *PspMboxSmmBufferAddressProtocol;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] BRH CPM RAS SMM driver entry\n");

  mAmdPspArsServiceProtocol = NULL;

  Status = gBS->LocateProtocol (
                  &gAmdPlatformApeiDataProtocolGuid,
                  NULL,
                  (VOID **)&mPlatformApeiData
                  );
  if (EFI_ERROR (Status)) {
    return Status;
  }
  mRbBusMap = mPlatformApeiData->RbBusMap;

  Status = gSmst->SmmLocateProtocol (
                    &gAmdRasServiceSmmProtocolGuid,
                    NULL,
                    (VOID **)&mAmdRasServiceSmmProtocol
                    );
  ASSERT_EFI_ERROR (Status);

  Status = gSmst->SmmLocateProtocol (
                    &gPspMboxSmmBufferAddressProtocolGuid,
                    NULL,
                    (VOID **)&PspMboxSmmBufferAddressProtocol
                    );
  if (EFI_ERROR(Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Locate gPspMboxSmmBufferAddressProtocolGuid: %r\n", Status);
    ASSERT_EFI_ERROR (Status);
  } else {
    mPspMboxSmmBuffer = PspMboxSmmBufferAddressProtocol->PspMboxSmmBuffer;
    mPspMboxSmmFlagAddr = PspMboxSmmBufferAddressProtocol->PspMboxSmmFlagAddr;
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "\tmPspMboxSmmFlagAddr@ 0x%x\n", mPspMboxSmmFlagAddr);
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "\tmPspMboxSmmBuffer@   0x%08x\n", mPspMboxSmmBuffer);
  }

  Status = RasSmmRegistrationCenter ();

  RasEnablePspEinj ();

  RasSciInit();

  Status = RasPcieMisc();
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] %a - RasPcieMisc: %r\n", __FUNCTION__, Status);

  Status = RuntimePprInit();
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] %a - RuntimePprInit: %r\n", __FUNCTION__, Status);

  return Status;
}

VOID
RasTriggerNMI (
  IN OUT   VOID
  )
{
  ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REGC0) &=  ~(UINT32)BIT20;
  ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REGC0) |= BIT21;
  ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG98) &= ~(UINT32)BIT25;
  ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG98) |= BIT25;
  ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REGC0) &=  ~(UINT32) (BIT20 + BIT21);
}

VOID
RasSciInit(
  IN OUT   VOID
  )
{
  ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG1C) &= ~(UINT32)BIT20;
  ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG18) |= BIT20;
}

VOID
RasTriggerSci (
  IN OUT   VOID
  )
{
  ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG1C) |= BIT20;
  ACPIMMIO32 (ACPI_MMIO_BASE + SMI_BASE + FCH_SMI_REG1C) &= ~(UINT32)BIT20;
}

VOID
RasResetErrBlk (
  IN OUT   EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE *ErrStatusBlk
  )
{
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] Error Block Reset!!\n");
  //OS already recorded the previous error and invalid the valid bits, reset buffer.
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] Error Block Buffer Size to clear: 0x%x\n", ErrStatusBlk->DataLength + sizeof (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE));
  ZeroMem (ErrStatusBlk, ErrStatusBlk->DataLength + sizeof (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE));

  //Reinit header
  ErrStatusBlk->ErrorSeverity = EFI_ACPI_6_3_ERROR_SEVERITY_NONE;
  ErrStatusBlk->RawDataOffset = sizeof (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE);
}

VOID
RasReinitErrBlkSts (
  IN OUT       EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE *ErrStatusBlk
  )
{
  if ((ErrStatusBlk->BlockStatus.CorrectableErrorValid | ErrStatusBlk->BlockStatus.UncorrectableErrorValid) == 0)
  {
    RasResetErrBlk(ErrStatusBlk);
  }
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] Error Block Data Entry Count: 0x%x, Length: 0x%0x\n", ErrStatusBlk->BlockStatus.ErrorDataEntryCount, ErrStatusBlk->DataLength);
}

VOID
MpRegisterAccess (
  IN       UINTN                ProcessorNumber,
  IN OUT   PLAT_RAS_MSR_ACCESS  *RasMsrAccess
  )
{
  if (ProcessorNumber > gSmst->NumberOfCpus) {
    return;
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "MSR Access @ Processor: %d, Address: 0x%08x, IsWrite (1:True): %d\n", ProcessorNumber, RasMsrAccess->RegisterAddress, RasMsrAccess->IsWrite);

  LibRasSmmRunFunc (MsrRegisterAccess, (VOID *)RasMsrAccess, ProcessorNumber);
}

VOID
UpdateGenErrStsBlkSeverity(
  EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE *ErrStatusBlk,
  UINT32        ErrorSeverity
)
{
    switch (ErrorSeverity)
    {
    case ERROR_RECOVERABLE:
        if (ErrStatusBlk->ErrorSeverity != ERROR_SEVERITY_FATAL) {
          ErrStatusBlk->ErrorSeverity = ERROR_RECOVERABLE;
        }
      break;
    case ERROR_SEVERITY_FATAL:
      // If error type Uncorrectable AND multiple uncorrectable errors
      ErrStatusBlk->ErrorSeverity = ERROR_SEVERITY_FATAL;
      break;
    case ERROR_SEVERITY_CORRECTED:
      if (ErrStatusBlk->ErrorSeverity > ERROR_SEVERITY_CORRECTED) {
        ErrStatusBlk->ErrorSeverity = ERROR_SEVERITY_CORRECTED;
      }
      break;
    }
}

EFI_STATUS
EFIAPI
BuildSmmPcieActivePortMapCallback (
  IN CONST EFI_GUID     *Protocol,
  IN VOID               *Interface,
  IN EFI_HANDLE         Handle
  )
{
  EFI_STATUS              Status;
  UINT16                  PciePortIndex;
  UINT32                  PciePortMapSize;
  PCIE_ACTIVE_PORT_MAP    *PciePortMap;
  PCIE_PORT_PROFILE       *PciePortProfileInstance;
  PCIE_ACTIVE_PORT_MAP    *TempPciePortMap;
  PCIE_PORT_PROFILE       *TempPciePortProfileInstance;
  LIST_ENTRY              *ListHead;
  PCI_ADDR                Device;
  PCIE_DEV_ENTRY          *PcieDevNode;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] BuildSmmPcieActivePortMapCallback Entry\n");
  mDxePciActivePortMapIsInvalid = TRUE;
  PciePortMap = mPlatformApeiData->AmdPciePortMap;
  PciePortMapSize = (sizeof (PCIE_ACTIVE_PORT_MAP)) + ((sizeof (PCIE_PORT_PROFILE)) * (PciePortMap->PortCount - 1));
  Status = gSmst->SmmAllocatePool (
                    EfiRuntimeServicesData,
                    PciePortMapSize,
                    (VOID **)&TempPciePortMap
                    );
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] %a - SmmAllocatePool: %r\n", __FUNCTION__, Status);
    Status = gSmst->SmmUninstallProtocolInterface (AmdRasMaintenanceHandle, &gRasMaintPcieActivePortMapProtocolGuid, &RasMaintPcieActivePortMapProtocol);
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] %a - Uninstsll RAS_MAINT_PCIEACTIVEPORTMAP_PROTOCOL: %r\n", __FUNCTION__, Status);
    return Status;
  }

  ZeroMem (TempPciePortMap, PciePortMapSize);
  CopyMem (TempPciePortMap, PciePortMap, PciePortMapSize);

  PciePortProfileInstance = PciePortMap->PciPortNumber;
  TempPciePortProfileInstance = TempPciePortMap->PciPortNumber;
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] ActiveRootPortCount: %d\n", TempPciePortMap->PortCount);
  for (PciePortIndex = 0; PciePortIndex < TempPciePortMap->PortCount; PciePortIndex++) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]=== %d. BuildSmmPcieActivePortMap (0x%08x) ===\n",
            PciePortIndex, TempPciePortProfileInstance[PciePortIndex].RpPciAddr);
    InitializeListHead(&TempPciePortProfileInstance[PciePortIndex].PciLinkList);
    TempPciePortProfileInstance[PciePortIndex].EndPointDevCnt = 0;

    ListHead = &PciePortProfileInstance[PciePortIndex].PciLinkList;
    if (ListHead != NULL) {
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Check existing map\n");
      while (!IsListEmpty (ListHead)) {
        PcieDevNode = BASE_CR (
                        ListHead->ForwardLink,
                        PCIE_DEV_ENTRY,
                        ListEntry
                        );
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  [DXE] PcieDevNode Ptr Address: 0x%08x\n", (UINTN)PcieDevNode);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  [DXE] PcieDevNode DevAddr: 0x%08x, DevType: 0x%x, BackLink: 0x%08x, ForwardLink: 0x%08x\n",
                PcieDevNode->DevAddr,
                PcieDevNode->DevType,
                PcieDevNode->ListEntry.BackLink,
                PcieDevNode->ListEntry.ForwardLink);

        Device.AddressValue = PcieDevNode->DevAddr;
        PciAppendList (Device, PcieDevNode->DevType, (TempPciePortProfileInstance+PciePortIndex));

        PciePortProfileInstance[PciePortIndex].EndPointDevCnt--;
        RemoveEntryList (ListHead->ForwardLink);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  [DXE] Remove PcieDevNode: 0x%08x from DXE PciePortMap\n", (UINTN)PcieDevNode);
      }
    }
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  [SMM] Pci Device count: %d\n", TempPciePortProfileInstance[PciePortIndex].EndPointDevCnt);
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  [SMM] Head List Forward Link Addr: 0x%08x, Back Link Addr: 0x%08x\n",
            TempPciePortProfileInstance[PciePortIndex].PciLinkList.ForwardLink,
            TempPciePortProfileInstance[PciePortIndex].PciLinkList.BackLink);
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]=== BuildSmmPcieActivePortMap Complete ===\n");
  }

  mPlatformApeiData->AmdPciePortMap = TempPciePortMap;
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  [DXE] PciePortMap structure@ 0x%08x has been processed\n", (UINTN)PciePortMap);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  [SMM] New PciePortMap structure in SMM@ 0x%08x.\n", (UINTN)mPlatformApeiData->AmdPciePortMap);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] BuildSmmPcieActivePortMapCallback Exit\n");

  return EFI_SUCCESS;
}

VOID
STATIC
PciAppendList (
  IN       PCI_ADDR          Device,
  IN       PCIE_DEVICE_TYPE  DeviceType,
  IN       PCIE_PORT_PROFILE *PciePortProfile
  )
{
  PCIE_DEV_ENTRY* PciDevEntry;
  EFI_STATUS      Status = EFI_SUCCESS;

  if (mDxePciActivePortMapIsInvalid) {
    //
    // Allocate a new PCI entry in SMRAM
    //
    Status = gSmst->SmmAllocatePool(
                      EfiRuntimeServicesData,
                      sizeof (PCIE_DEV_ENTRY),
                      (VOID **)&PciDevEntry
                      );
  } else {
    //
    // Allocate a new PCI entry
    //
    Status = gBS->AllocatePool (
                    EfiRuntimeServicesData,
                    sizeof (PCIE_DEV_ENTRY),
                    (VOID **)&PciDevEntry
                    );
  }
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, " Add Link failed: %r\n", Status);
    return;
  }

  PciDevEntry->DevAddr = Device.AddressValue;
  PciDevEntry->DevType = DeviceType;
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, " Add Device @0x%08x, Type: 0x%x to the Link List\n", PciDevEntry->DevAddr, PciDevEntry->DevType);
  //
  // Add the newly allocated PCI entry to the Root Port pci link list
  //
//  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, " PciLinkList Address: 0x%08x\n", (UINTN)&PciePortProfile->PciLinkList);
//  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, " Forward Link Address: 0x%08x, Backward Link Address: 0x%08x\n", PciePortProfile->PciLinkList.ForwardLink, PciePortProfile->PciLinkList.BackLink);

  InsertTailList (&PciePortProfile->PciLinkList, &PciDevEntry->ListEntry);
  PciePortProfile->EndPointDevCnt++;

  return;
}

SCAN_STATUS
STATIC
PcieDevScanCallback (
  IN       PCI_ADDR             Device,
  IN OUT   RAS_PCI_SCAN_DATA    *ScanData
  )
{
  SCAN_STATUS             ScanStatus;
  PCIE_DEVICE_TYPE        DeviceType;
  ScanStatus = SCAN_SUCCESS;
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  PcieDevScanCallback for Device = 0x%X:0x%X:0x%X:0x%X\n",
    Device.Address.Segment,
    Device.Address.Bus,
    Device.Address.Device,
    Device.Address.Function
    );
  ScanStatus = SCAN_SUCCESS;
  DeviceType = RasGetPcieDeviceType (Device);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  PCI-E device type = 0x%x\n", DeviceType);
  switch (DeviceType) {
  case  PcieDeviceRootComplex:
  case  PcieDeviceDownstreamPort:
  case  PcieDeviceUpstreamPort:
  case  PcieDevicePcieToPcix:
  case  PcieDeviceEndPoint:
  case  PcieDeviceLegacyEndPoint:
    PciAppendList (Device, DeviceType, (PCIE_PORT_PROFILE*)ScanData->Buffer);
    break;
  default:
    break;
  }
  return ScanStatus;
}

VOID
STATIC
PcieDevScan (
  IN       PCI_ADDR  DownstreamPort,
  IN       PCIE_PORT_PROFILE *PciePortProfile
  )
{
  RAS_PCI_SCAN_DATA  ScanData;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Scan PCI device\n");
  ScanData.Buffer = PciePortProfile;
  ScanData.RasScanCallback = PcieDevScanCallback;
  RasPciScanSecondaryBus (DownstreamPort, &ScanData);
}

EFI_STATUS
EFIAPI
RasRebuildByRootPort (
  IN  UINT32    RpPciAddr
  )
{
  EFI_STATUS           Status;
  UINT16               PciePortIndex;
  LIST_ENTRY           *ListHead;
  PCIE_DEV_ENTRY       *PcieDevNode;
  PCIE_PORT_PROFILE    *PciePortProfileInstance;
  PCI_ADDR             PciPortAddr;
  PCIe_AER_CONFIG_TEMP PcieAerSetting;
  PCIe_AER_CONFIG_TEMP PcieDevAerSetting;
  UINT8                OldEndPointDevCnt;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] RAS_MAINT_PCIEACTIVEPORTMAP_PROTOCOL.RebuildByRootPort Entry (RpPciAddr: 0x%08x)\n", RpPciAddr);

  // Search for specify PCI-E port to rebuild its device link-list
  PciePortProfileInstance = mPlatformApeiData->AmdPciePortMap->PciPortNumber;
  Status = EFI_NOT_FOUND;
  for (PciePortIndex = 0; (PciePortIndex < mPlatformApeiData->AmdPciePortMap->PortCount) && (EFI_ERROR(Status)); PciePortIndex++) {
    if (PciePortProfileInstance[PciePortIndex].RpPciAddr != RpPciAddr) {
      continue;
    }
    Status = EFI_SUCCESS;
  }
  if (EFI_ERROR(Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] Error: The specified Root Port cannot be found\n");
    return Status;
  }

  PciePortProfileInstance += (--PciePortIndex);
  OldEndPointDevCnt = PciePortProfileInstance->EndPointDevCnt;
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] PciePortProfileInstance->RpPciAddr: 0x%08x\n", PciePortProfileInstance->RpPciAddr);

  ListHead = &PciePortProfileInstance->PciLinkList;
  if (ListHead == NULL) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Input root port address not in the map, Intial List head\n");
    InitializeListHead(&PciePortProfileInstance->PciLinkList);
  } else {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Check existing map\n");
    while (!IsListEmpty (ListHead)) {
      PcieDevNode = BASE_CR (
                      ListHead->ForwardLink,
                      PCIE_DEV_ENTRY,
                      ListEntry
                      );
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  PciDevNode Ptr Address: 0x%08x\n", (UINTN)PcieDevNode);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  PciDevNode DevAddr: 0x%08x, DevType: 0x%x, BackLink: 0x%08x, ForwardLink: 0x%08x\n",
              PcieDevNode->DevAddr,
              PcieDevNode->DevType,
              PcieDevNode->ListEntry.BackLink,
              PcieDevNode->ListEntry.ForwardLink);

      RemoveEntryList (ListHead->ForwardLink);
      if (mDxePciActivePortMapIsInvalid) {
        Status = gSmst->SmmFreePool (PcieDevNode);
        if (EFI_ERROR(Status)) {
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] Error: Cannot free this node from the device list.\n");
          return Status;
        }
      }
      PciePortProfileInstance->EndPointDevCnt--;
    }
  }

  //Scan Device
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] Scan PCI Device for specified Root Port\n");
  PciePortProfileInstance->EndPointDevCnt = 0;
  PciPortAddr.AddressValue = PciePortProfileInstance->RpPciAddr;
  PcieDevScan(PciPortAddr, PciePortProfileInstance);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  RootPort Address: 0x%08x\n", PciePortProfileInstance->RpPciAddr);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Pci Device count: %d\n", PciePortProfileInstance->EndPointDevCnt);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Head List Forward Link Addr: 0x%08x, Back Link Addr: 0x%08x\n",
          PciePortProfileInstance->PciLinkList.ForwardLink,
          PciePortProfileInstance->PciLinkList.BackLink);

  // Runtime Behavior
  // PCI-E Aer firmware first setup
  if (PciePortProfileInstance->EndPointDevCnt >= OldEndPointDevCnt) {
    // Asynchronous Hot Add
    // Configures AER identically to the "Boot Time Configuration" for a populated PCIe slot.
    // This sets up AER for this slot. At this point, the firmware configuration for this slot matches the "Boot Time Configuration" for a populated slot.
    // Root port
    PcieAerSetting.AerEnable = 1;
    PcieAerSetting.PciSeg = (UINT8)PciPortAddr.Address.Segment;
    PcieAerSetting.PciBus = (UINT8)PciPortAddr.Address.Bus;
    PcieAerSetting.PciDev = (UINT8)PciPortAddr.Address.Device;
    PcieAerSetting.PciFunc = (UINT8)PciPortAddr.Address.Function;
    PcieAerSetting.CorrectableMask = mPlatformApeiData->PlatRasPolicy.PcieRpCorrectedErrorMask;
    PcieAerSetting.UncorrectableMask = mPlatformApeiData->PlatRasPolicy.PcieRpUnCorrectedErrorMask;

    // Align with the setting in PcieAerErrorConfig in the AmdPlatformRasBrhDxe driver. (i.e. Boot time setting)
    if (IsHotPlugRp (PciPortAddr)) {
      PcieAerSetting.UncorrectableMask |= (BIT5 + BIT14); //BIT5: Surprise Down Error Mask, BIT14: Completion Timeout Mask
      if ((mPlatformApeiData->OsHotPlugMechanism->HotPlugHandlingMode == 3) ||
          (mPlatformApeiData->OsHotPlugMechanism->HotPlugHandlingMode == 6)) {
        PcieAerSetting.UncorrectableMask &= ~(BIT14); //Do not mask BIT14: Completion Timeout Mask in Hotplug Firmware First mode and Hotplug Firmware First mode with EDR
      }
    }
    // If EDR is enabled on this port, keep SURPDN_ERR_MASK (BIT5) cleared
    if (OsEdrEnabled(PciPortAddr)) {
      PcieAerSetting.UncorrectableMask &= 0xFFFFFFDF;
    }
    PcieAerSetting.UncorrectableSeverity = mPlatformApeiData->PlatRasPolicy.PcieRpUnCorrectedErrorSeverity;
    RasSetPcieAerFeature (&PcieAerSetting);
    RasPcieStsClr (PciPortAddr, mPlatformApeiData->PlatRasPolicy.RasRetryCnt);

    // Device
    PcieDevAerSetting.AerEnable = 1;
    PcieDevAerSetting.PciBus = (UINT8)PciPortAddr.Address.Bus;
    PcieDevAerSetting.PciDev = (UINT8)PciPortAddr.Address.Device;
    PcieDevAerSetting.PciFunc = (UINT8)PciPortAddr.Address.Function;
    PcieDevAerSetting.CorrectableMask = mPlatformApeiData->PlatRasPolicy.PcieDevCorrectedErrorMask;
    PcieDevAerSetting.UncorrectableMask = mPlatformApeiData->PlatRasPolicy.PcieDevUnCorrectedErrorMask;
    PcieDevAerSetting.UncorrectableSeverity = mPlatformApeiData->PlatRasPolicy.PcieDevUnCorrectedErrorSeverity;

    EnDevErrReport(PciPortAddr, &PcieDevAerSetting);
  } else {
    // Asynchronous Hot Remove
    // Clear any error logged in the AER STATUS registers on the slot in question.
    // Root port
    RasPcieStsClr (PciPortAddr, mPlatformApeiData->PlatRasPolicy.RasRetryCnt);

    // Device
    // Disable AER on the slot in question.
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] RAS_MAINT_PCIEACTIVEPORTMAP_PROTOCOL.RebuildByRootPort Exit\n");
  return Status;
}


/*********************************************************************************
 * Name: PerformEdr
 *
 * Description
 *   This routine is used to handle non-Surprise Down error.
 *     The error is not a Surprise Down event (if PCIERCCFG::PCIE_UNCORR_ERR_STATUS[SURPDN_ERR_STATUS]=0x0) then treat this as a "true" error and attempt recovery.
 *     Notifies the OS via an EDR notification. the system then continues with the Error Disconnect Recover flows including the operating system _DSM query for which device triggered DPC,
 *     and the _OST message from the operating system on success/failure of device recovery.
 *
 * Input
 *   To-DO
 *
 * Output
 *   To-DO
 *
 *********************************************************************************/
EFI_STATUS
EFIAPI
PerformEdr (
  IN  UINT32    RpPciAddr
  )
{
  EFI_STATUS             Status;
  BOOLEAN                TrigNMI;
  BOOLEAN                TrigSCI;
  PCI_ADDR               RootPortPciAddr;
  UINT8                  Index;
  EDR_DSM_ACPI_SMM_DATA  *EdrDsmAcpiSmmData;
  UINT32                 RpDevFnc;

  TrigNMI = FALSE;
  TrigSCI = FALSE;
  RootPortPciAddr.AddressValue = RpPciAddr;

  EdrDsmAcpiSmmData = mPlatformApeiData->EdrDsmAcpiSmmData;
  if (!SmmIsBufferOutsideSmmValid ((UINTN) EdrDsmAcpiSmmData, sizeof(EDR_DSM_ACPI_SMM_DATA))) {
    DEBUG ((EFI_D_ERROR, "EdrDsmAcpiSmmData is in SMRAM or overlapped with SMRAM!\n"));
    return EFI_INVALID_PARAMETER;
  }
//  1. Check all error status registers (PCIERCCFG::PCIE_UNCORR_ERR_STATUS and PCIERCCFG::PCIE_CORR_ERR_STATUS in the root port,
//       and Uncorrectable Error Status and Correctable Error Status in all child devices) in devices below this root port.
//       Firmware must first check each child device to ensure it is present and has not been removed (i.e. heck for non-FF device/vendor ID).
//  2. Keep internal firmware state of any errors that are found, including the bus/device/function of the device on which they are found.
//  3. Clear the AER registers in root port and child device.
//  4. Issue ACPI Notify event with Notification Value of Error Disconnect Recover and Object as the root port on which DPC was triggered.
//        See the ACPI 6.3 spec, section 5.6.6 Device Object Notifications, and section 19.6.93 Notify, for details on how this event works.
  Status = PcieErrorScanHelper ((UINT8)(RootPortPciAddr.Address.Bus), RpPciAddr, &TrigNMI, &TrigSCI, TRUE);

  if (!TrigNMI && !TrigSCI) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] EDR - PerformEdr: ERROR! No PCIe errors could be found.\n");
  }

  if (TrigSCI) {
    EdrDsmAcpiSmmData->RpAslDevName = 0;
    for (Index = 0; Index < TOTAL_NVME_SLOT; Index++) {
      if (RootPortVsNvmeSlotMappingTable[Index].RpAddr == (RpPciAddr & 0xFFFFF000)) {
        EdrDsmAcpiSmmData->RpSeg = (UINT8)(RootPortPciAddr.Address.Segment);
        EdrDsmAcpiSmmData->RpBus = (UINT8)(RootPortPciAddr.Address.Bus);
        RpDevFnc = RootPortPciAddr.Address.Device;
        RpDevFnc = ((RpDevFnc << 16) | (RootPortPciAddr.Address.Function));
        EdrDsmAcpiSmmData->RpDevFnc = RpDevFnc;
        EdrDsmAcpiSmmData->RpAslDevName = RootPortVsNvmeSlotMappingTable[Index].NvmeSlot;
        break;
      }
    }

    if (EdrDsmAcpiSmmData->RpAslDevName != 0) {
      //Trigger SCI
      RasTriggerSci();
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
        "[RAS] EDR - PerformEdr: Trigger SCI with RootPort: 0x%08x. PcieRpAcpiName: 0x%08x\n",
        RpPciAddr, EdrDsmAcpiSmmData->RpAslDevName
      );
    } else {
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
      "[RAS] EDR - PerformEdr: ERROR! No NVME Slot number could be found, Skip to trigger SCI.\n");
    }
  }

  return Status;
}

/*********************************************************************************
 * Name: OsEdrEnabled
 *
 * Description
 *   This routine is used to determine if the OS has enabled EDR
 *
 * Input
 *   RpAddress - PCI_ADDR - PCI address of port
 *
 * Output
 *   VOID
 *
 * Returns
 *   TRUE - OS has enabled Error Disconnect Recovery on port
 *   FALSE - OS has disabled or does not support Error Disconnect Recover on port
 *
 *********************************************************************************/
BOOLEAN
EFIAPI
OsEdrEnabled (
  IN  PCI_ADDR RpAddress
  )
{
  UINT8       Index;
  PCI_ADDR    RpAddressNoReg;

  RpAddressNoReg = RpAddress;
  RpAddressNoReg.Address.Register = 0;

  for (Index = 0; Index < MAX_NUM_EDR_PORTS; Index++) {
    if (OsEdrEnabledAddressTable[Index].AddressValue == RpAddressNoReg.AddressValue) {
      // Port address is found in the list of EDR-enabled port addresses
      return TRUE;
    } else if (OsEdrEnabledAddressTable[Index].AddressValue == 0) {
      // End of populated list of EDR enabled port addresses, input port address was not found
      return FALSE;
    }
  }

  return FALSE;
}

/*********************************************************************************
 * Name: AddOsEdrEnabledPortToTable
 *
 * Description
 *   Add input port address to table of EDR-enabled ports
 *
 * Input
 *   RpAddress - PCI_ADDR - PCI address of EDR enabled port
 *
 * Output
 *   VOID
 *
 * Returns
 *   EFI_STATUS
 *
 *********************************************************************************/
EFI_STATUS
AddOsEdrEnabledPortToTable (
  IN  PCI_ADDR RpAddress
  )
{
  UINT8       Index;
  PCI_ADDR    RpAddressNoReg;
  EFI_STATUS  Status;

  RpAddressNoReg = RpAddress;
  RpAddressNoReg.Address.Register = 0;

  for (Index = 0; Index < MAX_NUM_EDR_PORTS; Index++) {
    if (OsEdrEnabledAddressTable[Index].AddressValue == RpAddressNoReg.AddressValue) {
      // Port address is already found in the list of EDR-enabled port addresses, exit
      return EFI_SUCCESS;
    } else if (OsEdrEnabledAddressTable[Index].AddressValue == 0) {
      // End of populated list of EDR enabled port addresses, add new address here
      OsEdrEnabledAddressTable[Index] = RpAddressNoReg;
      return EFI_SUCCESS;
    }
  }

  // Table is completely full of addresses, error
  Status = EFI_BUFFER_TOO_SMALL;
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] %a - OsEdrEnabledAddressTable allocated size too small!: %r\n", __FUNCTION__, Status);
  ASSERT_EFI_ERROR (Status);
  return Status;
}

EFI_STATUS
RasPcieMisc ()
{
  EFI_STATUS              Status;
  VOID                    *Registration;
  EFI_HANDLE              AmdAsyncHpEdrProtocolHandle;

  AmdRasMaintenanceHandle = NULL;
  ZeroMem (OsEdrEnabledAddressTable, sizeof(PCI_ADDR) * MAX_NUM_EDR_PORTS);
  Status = gSmst->SmmInstallProtocolInterface (
      &AmdRasMaintenanceHandle,
      &gRasMaintPcieActivePortMapProtocolGuid,
      EFI_NATIVE_INTERFACE,
      &RasMaintPcieActivePortMapProtocol
      );
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] %a - Install RAS_MAINT_PCIEACTIVEPORTMAP_PROTOCOL: %r\n", __FUNCTION__, Status);
  if (!EFI_ERROR (Status)) {
    Status = gSmst->SmmRegisterProtocolNotify (
                      &gEdkiiSmmReadyToBootProtocolGuid,
                      BuildSmmPcieActivePortMapCallback,
                      &Registration
                      );

    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] %a - Register BuildSmmPcieActivePortMapCallback: %r\n", __FUNCTION__, Status);
    if (EFI_ERROR (Status)) {
      Status = gSmst->SmmUninstallProtocolInterface (
          AmdRasMaintenanceHandle,
          &gRasMaintPcieActivePortMapProtocolGuid,
          &RasMaintPcieActivePortMapProtocol
          );
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] %a - Uninstsll RAS_MAINT_PCIEACTIVEPORTMAP_PROTOCOL: %r\n", __FUNCTION__, Status);
      return Status;
    }
  }

  AmdAsyncHpEdrProtocolHandle = NULL;
  AmdAsyncHpEdrProtocol.EdrDsmAcpiSmmData = mPlatformApeiData->EdrDsmAcpiSmmData;
  AmdAsyncHpEdrProtocol.EdrOstAcpiSmmData = mPlatformApeiData->EdrOstAcpiSmmData;
  Status = gSmst->SmmInstallProtocolInterface (
                    &AmdAsyncHpEdrProtocolHandle,
                    &gAmdAsyncHpEdrProtocolGuid,
                    EFI_NATIVE_INTERFACE,
                    &AmdAsyncHpEdrProtocol
                    );
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] %a - Install AMD_ASYNC_HP_EDR_PROTOCOL: %r\n", __FUNCTION__, Status);

  return Status;
}

EFI_STATUS
EFIAPI
RegisterCxlRasSmiCallback (
  IN CONST EFI_GUID     *Protocol,
  IN VOID               *Interface,
  IN EFI_HANDLE         Handle
  )
{
  // CXL RAS SMI handler
  return RasSmmRegisterCxlRasSmi();
}


EFI_STATUS
PassOemMcaThresholdMapTableToAGESA(
  IN       OEM_MCA_THRESHOLD_MAP_TABLE *OemMcaThresholdMapTable
)
{
  EFI_STATUS              Status = EFI_SUCCESS;
  AMD_MCA_THRESHOLD_TABLE AmdMcaThresholdTable;
  UINT32                  McaThresholdTableEntryTablesize;
  UINT32                  TblIndex;
  RAS_THRESHOLD_CONFIG      RasThresholdConfig;


  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, " [RAS] %a - Start\n", __FUNCTION__);
  McaThresholdTableEntryTablesize = (sizeof (AMD_MCA_THRESHOLD_ENTRY)) * OemMcaThresholdMapTable->TableEntryNum;
  Status = gBS->AllocatePool (EfiRuntimeServicesData,
                              McaThresholdTableEntryTablesize,
                              (VOID **)&AmdMcaThresholdTable.McaThresholdTableEntry);

  if (EFI_ERROR (Status)) {
    return Status;
  } else {
    //clear instances content
    gBS->SetMem (AmdMcaThresholdTable.McaThresholdTableEntry, McaThresholdTableEntryTablesize, 0);
  }


  for (TblIndex = 0; TblIndex < OemMcaThresholdMapTable->TableEntryNum; TblIndex++) {
    AmdMcaThresholdTable.McaThresholdTableEntry[TblIndex].HardwareID = OemMcaThresholdMapTable->McaThresholdTableEntry[TblIndex].HardwareID;
    AmdMcaThresholdTable.McaThresholdTableEntry[TblIndex].McaType = OemMcaThresholdMapTable->McaThresholdTableEntry[TblIndex].McaType;
    AmdMcaThresholdTable.McaThresholdTableEntry[TblIndex].Misc0ThresholdCount = OemMcaThresholdMapTable->McaThresholdTableEntry[TblIndex].Misc0ThresholdCount;
    AmdMcaThresholdTable.McaThresholdTableEntry[TblIndex].Misc1ThresholdCount = OemMcaThresholdMapTable->McaThresholdTableEntry[TblIndex].Misc1ThresholdCount;

    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "AmdMcaThresholdTable.McaThresholdTableEntry[%d].HardwareID: 0x%016lX,\
                                               AmdMcaThresholdTable.McaThresholdTableEntry[%d].McaType 0x%x, \
                                               AmdMcaThresholdTable.McaThresholdTableEntry[%d].Misc0ThresholdCount %d, \
                                               AmdMcaThresholdTable.McaThresholdTableEntry[%d].Misc1ThresholdCount : %d \n",
                                               TblIndex,
                                               AmdMcaThresholdTable.McaThresholdTableEntry[TblIndex].HardwareID,
                                               TblIndex,
                                               AmdMcaThresholdTable.McaThresholdTableEntry[TblIndex].McaType,
                                               TblIndex,
                                               AmdMcaThresholdTable.McaThresholdTableEntry[TblIndex].Misc0ThresholdCount,
                                               TblIndex,
                                               AmdMcaThresholdTable.McaThresholdTableEntry[TblIndex].Misc1ThresholdCount);
  }

  AmdMcaThresholdTable.TableEntryNum = OemMcaThresholdMapTable->TableEntryNum;
  AmdMcaThresholdTable.TableSize = McaThresholdTableEntryTablesize;

  RasThresholdConfig.ThresholdControl = mPlatformApeiData->PlatRasPolicy.McaErrThreshEn;
  RasThresholdConfig.ThresholdCount =  mPlatformApeiData->PlatRasPolicy.McaErrThreshCount;
  RasThresholdConfig.ThresholdIntType = 2;          // SMI

  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "OemMcaThresholdMapTotalSize 0x%x \n", OemMcaThresholdMapTable->TableSize);
  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "OemMcaThresholdMapTable->TableEntryNum 0x%x\n", OemMcaThresholdMapTable->TableEntryNum);

  mAmdRasServiceSmmProtocol->SetMcaThresholdOemTable(NULL, NULL, &RasThresholdConfig, FALSE,&AmdMcaThresholdTable);

  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "[RAS] %a - End\n", __FUNCTION__);
  return EFI_SUCCESS;
}
EFI_STATUS
UpdateOemDimmFruTextToMca(
  IN       OEM_MEMORY_MAP_TABLE *OemMemoryMapTable
)
{
  EFI_STATUS              Status = EFI_SUCCESS;
  CHAR8                   FruText[MAX_MCA_FRUTEXT_SIZE];
  AMD_DIMM_FRUTEXT_TABLE  AmdDimmFrutextTable;
  UINT32                  FruTextEntryTablesize;
  UINT32                  TblIndex;


  FruTextEntryTablesize = (sizeof (AMD_FRUTEXT_ENTRY)) * OemMemoryMapTable->TableEntryNum;
  Status = gBS->AllocatePool (EfiBootServicesData,
                              FruTextEntryTablesize,
                              (VOID **)&AmdDimmFrutextTable.FrutextEntry);
  if (EFI_ERROR (Status)) {
    return Status;
  } else {
    //clear instances content
    gBS->SetMem (AmdDimmFrutextTable.FrutextEntry, FruTextEntryTablesize, 0);
  }

  for (TblIndex = 0; TblIndex < OemMemoryMapTable->TableEntryNum; TblIndex++) {
    GetFullMemErrFruText(&FruText[0], &OemMemoryMapTable->MemoryMapTableEntry[TblIndex].FruText[0]);

    AmdDimmFrutextTable.FrutextEntry[TblIndex].Socket = OemMemoryMapTable->MemoryMapTableEntry[TblIndex].Node;
    AmdDimmFrutextTable.FrutextEntry[TblIndex].Die = 0;
    AmdDimmFrutextTable.FrutextEntry[TblIndex].Channel = OemMemoryMapTable->MemoryMapTableEntry[TblIndex].Card;
    AmdDimmFrutextTable.FrutextEntry[TblIndex].Module = OemMemoryMapTable->MemoryMapTableEntry[TblIndex].Module;

    gBS->CopyMem (&AmdDimmFrutextTable.FrutextEntry[TblIndex].AmdFrutextStr, &FruText , MAX_MCA_FRUTEXT_SIZE);
  }

  AmdDimmFrutextTable.TableEntryNum = OemMemoryMapTable->TableEntryNum;
  mAmdRasServiceSmmProtocol->UpdateDimmFruTextToMca(&AmdDimmFrutextTable);

  return EFI_SUCCESS;
}

