/*****************************************************************************
 *
 * Copyright (C) 2018-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Filecode.h>
#include "AGESA.h"
#include <Library/BaseMemoryLib.h>
#include <Library/BaseLib.h>
#include <Library/IoLib.h>
#include <Library/PcdLib.h>
#include <Library/CcxBaseX86Lib.h>
#include <Library/RasSocLib.h>
#include <Library/DfAddressTranslateLib.h>
#include <Library/CoreTopologyV3Lib.h>
#include <Protocol/MpService.h>
#include <Protocol/FabricTopologyServices2.h>
#include <Protocol/AmdCoreTopologyV3Protocol.h>
#include "AmdRasRegistersBrh.h"
#include "AmdRasBrhDxe.h"
#include <Library/AmdPspMboxLibV2.h>
#include <Library/AgesaConfigLib.h>
#include <ActOptions.h>
#include <Library/IdsLib.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE RAS_AMDRASBRHDXE_AMDRASBRHDXE_FILECODE

#define CCX_MAX_SOCKETS            2
#define CCX_MAX_DIES_PER_SOCKET    1   // Program dependent
#define MAX_CCX_PER_CCD            1   // Program dependent
#define CCX_MAX_CORES_PER_COMPLEX  16  // Todo Brh = 8 cores per CCX, Brhd = 16
#define CCX_MAX_THREADS_PER_CORE   2   // Fixed for Zen / Cerberus
#define CCX_NOT_PRESENT (0xFF)

#define MAX_CCDS_PER_IOD       16 // Program dependent. Todo Brh = 16 CCD per die, Brhd = 12

//Maximum number of cores in one Brh = 128. Brhd = 192
#define RAS_MAX_CORES (CCX_MAX_SOCKETS * CCX_MAX_DIES_PER_SOCKET * CCX_MAX_CORES_PER_COMPLEX * CCX_MAX_THREADS_PER_CORE * MAX_CCDS_PER_IOD * MAX_CCX_PER_CCD)


#define SATA0_MCA_SMN_ADDRESS       (0x03102000)
#define SATA1_MCA_SMN_ADDRESS       (0x03202000)
#define SATA2_MCA_SMN_ADDRESS       (0x03302000)
#define SATA3_MCA_SMN_ADDRESS       (0x03402000)
#define USB0_MCA_SMN_ADDRESS        (0x16DFE000)
#define USB1_MCA_SMN_ADDRESS        (0x16FFE000)

#define SERDES_AG3_PCSXGMI_MCA_SMN_ADDRESS        (0x12109200)
#define SERDES_BG0_PCSXGMI_MCA_SMN_ADDRESS        (0x11E09200)
#define SERDES_BG1_PCSXGMI_MCA_SMN_ADDRESS        (0x11F09200)
#define SERDES_BG2_PCSXGMI_MCA_SMN_ADDRESS        (0x12009200)
#define SERDES_BP1_PCSXGMI_MCA_SMN_ADDRESS        (0x11B09200)
#define SERDES_BP3_PCSXGMI_MCA_SMN_ADDRESS        (0x11D09200)

#define INSTANCE_ID_HI_SOCKET0         (0)
#define INSTANCE_ID_HI_SOCKET1         (1)

#define MAX_MCA_BANKS                  (64)

#define MCA_IPID(mcatype, instanceidhi, hardwareid, instanceid) \
                (UINT64) ( (((UINT64)mcatype) << 48) + (((UINT64)instanceidhi) << 44) + (((UINT64)hardwareid) << 32) + ((UINT64)instanceid) )

#define SMN_SEG_BUS(s,b)            (UINT32)((((UINT32)s & 0xFFFF) << 8) | ((UINT32)b & 0xFF))
/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */

extern  EFI_BOOT_SERVICES       *gBS;
ADDR_DATA                       *gAddrData;
AMD_RAS_POLICY                  *mAmdRasPolicy;
EFI_MP_SERVICES_PROTOCOL        *mMpServices=NULL;

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
EFI_STATUS
CollectDimmMap(VOID);

EFI_STATUS
CollectCpuMap(VOID);

EFI_STATUS
CollectMcaMap(VOID);

EFI_STATUS
AmdRasPolicyInit(VOID);

EFI_STATUS
SendSpecifiedPcdToPSP(VOID);

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */
typedef struct {
  MCA_INFO          BankMcaInfo[XMCA_MAX_BANK_COUNT];   ///< Place holder for MCA_INFO entries.
  UINT32            MaxMcaBankCount;                    /// MCA count of CPU
} CPU_MCA_INFO; 

typedef struct {
  CPU_MCA_INFO      CpuMcaInfo[1];                      ///< Place holder for MCA_INFO entries.
} PRE_MCA_BANK_MAP;

typedef struct {
  UINT32        ThreadId;     ///< MCA bank thread ID with SMT enabled.
  UINT32        BankNum;      ///< MCA bank number
  UINT64        McaIpid;      ///< MCA default IPID
} MCA_IP_INFO;


/// This is a default SATA/USB MCA bank information for Breithorn. Use only when firmware cannot
/// get valid MCA_IPID value from SATA or USB MCA bank.
/// This table should only use for MCA harvest support.
MCA_IP_INFO gMcaIpInfo[] = {
  {0, 26, MCA_IPID(SATA_MCA_TYPE, INSTANCE_ID_HI_SOCKET0, MCA_SATA_ID, SATA0_MCA_SMN_ADDRESS)},
  {2, 26, MCA_IPID(SATA_MCA_TYPE, INSTANCE_ID_HI_SOCKET0, MCA_SATA_ID, SATA1_MCA_SMN_ADDRESS)},
  {4, 26, MCA_IPID(SATA_MCA_TYPE, INSTANCE_ID_HI_SOCKET0, MCA_SATA_ID, SATA2_MCA_SMN_ADDRESS)},
  {6, 26, MCA_IPID(SATA_MCA_TYPE, INSTANCE_ID_HI_SOCKET0, MCA_SATA_ID, SATA3_MCA_SMN_ADDRESS)},
  {8, 26, MCA_IPID(USB_MCA_TYPE,  INSTANCE_ID_HI_SOCKET0, MCA_USB_ID,  USB0_MCA_SMN_ADDRESS)},
  {10,26, MCA_IPID(USB_MCA_TYPE,  INSTANCE_ID_HI_SOCKET0, MCA_USB_ID,  USB1_MCA_SMN_ADDRESS)},
  {0, 26, MCA_IPID(SATA_MCA_TYPE, INSTANCE_ID_HI_SOCKET1, MCA_SATA_ID, SATA0_MCA_SMN_ADDRESS)},
  {2, 26, MCA_IPID(SATA_MCA_TYPE, INSTANCE_ID_HI_SOCKET1, MCA_SATA_ID, SATA1_MCA_SMN_ADDRESS)},
  {4, 26, MCA_IPID(SATA_MCA_TYPE, INSTANCE_ID_HI_SOCKET1, MCA_SATA_ID, SATA2_MCA_SMN_ADDRESS)},
  {6, 26, MCA_IPID(SATA_MCA_TYPE, INSTANCE_ID_HI_SOCKET1, MCA_SATA_ID, SATA3_MCA_SMN_ADDRESS)},
  {8, 26, MCA_IPID(USB_MCA_TYPE,  INSTANCE_ID_HI_SOCKET1, MCA_USB_ID,  USB0_MCA_SMN_ADDRESS)},
  {10,26, MCA_IPID(USB_MCA_TYPE,  INSTANCE_ID_HI_SOCKET1, MCA_USB_ID,  USB1_MCA_SMN_ADDRESS)},
  {8, 30, MCA_IPID(PCS_XGMI_MCA_TYPE, INSTANCE_ID_HI_SOCKET0, MCA_PCS_XGMI_ID, SERDES_BG0_PCSXGMI_MCA_SMN_ADDRESS)},
  {10,30, MCA_IPID(PCS_XGMI_MCA_TYPE, INSTANCE_ID_HI_SOCKET0, MCA_PCS_XGMI_ID, SERDES_BG1_PCSXGMI_MCA_SMN_ADDRESS)},
  {4, 34, MCA_IPID(PCS_XGMI_MCA_TYPE, INSTANCE_ID_HI_SOCKET0, MCA_PCS_XGMI_ID, SERDES_BG2_PCSXGMI_MCA_SMN_ADDRESS)},
  {6, 34, MCA_IPID(PCS_XGMI_MCA_TYPE, INSTANCE_ID_HI_SOCKET0, MCA_PCS_XGMI_ID, SERDES_AG3_PCSXGMI_MCA_SMN_ADDRESS)},
  {8, 30, MCA_IPID(PCS_XGMI_MCA_TYPE, INSTANCE_ID_HI_SOCKET1, MCA_PCS_XGMI_ID, SERDES_BG0_PCSXGMI_MCA_SMN_ADDRESS)},
  {10,30, MCA_IPID(PCS_XGMI_MCA_TYPE, INSTANCE_ID_HI_SOCKET1, MCA_PCS_XGMI_ID, SERDES_BG1_PCSXGMI_MCA_SMN_ADDRESS)},
  {4, 34, MCA_IPID(PCS_XGMI_MCA_TYPE, INSTANCE_ID_HI_SOCKET1, MCA_PCS_XGMI_ID, SERDES_BG2_PCSXGMI_MCA_SMN_ADDRESS)},
  {6, 34, MCA_IPID(PCS_XGMI_MCA_TYPE, INSTANCE_ID_HI_SOCKET1, MCA_PCS_XGMI_ID, SERDES_AG3_PCSXGMI_MCA_SMN_ADDRESS)}
};

/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                          T A B L E    D E F I N I T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------*/

/*********************************************************************************
 * Name: AmdRasBrhDxeInit
 *
 * Description
 *   Entry point of the AMD RAS BRH DXE driver
 *   Perform the configuration init, resource reservation, early post init
 *   and install all the supported protocol
 *
 * Input
 *   ImageHandle : EFI Image Handle for the DXE driver
 *   SystemTable : pointer to the EFI system table
 *
 * Output
 *   EFI_SUCCESS : Module initialized successfully
 *   EFI_ERROR   : Initialization failed (see error for more details)
 *
 *********************************************************************************/
EFI_STATUS
EFIAPI
AmdRasBrhDxeInit (
  IN       EFI_HANDLE         ImageHandle,
  IN       EFI_SYSTEM_TABLE   *SystemTable
  )
{
  EFI_STATUS          Status = EFI_SUCCESS;
  EFI_HANDLE          Handle = NULL;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] BRH AGESA RAS DXE driver entry\n");

  SendSpecifiedPcdToPSP();

  AmdRasPolicyInit();

  CollectDimmMap();

  Status = CollectCpuMap();

  if (Status == EFI_SUCCESS) {
    CollectMcaMap();
  }

  Handle = ImageHandle;
  Status = gBS->InstallProtocolInterface (
                  &Handle,
                  &gAmdRasInitDataProtocolGuid,
                  EFI_NATIVE_INTERFACE,
                  mAmdRasPolicy
                  );
  if (EFI_ERROR (Status)) {
    return (Status);
  }

  return (Status);
}


EFI_STATUS
AmdRasPolicyInit(VOID)
{
  EFI_STATUS          Status = EFI_SUCCESS;

  //
  //  Allocate memory and Initialize for Data block
  //
  Status = gBS->AllocatePool (
                  EfiReservedMemoryType,
                  sizeof (AMD_RAS_POLICY),
                  (VOID **)&mAmdRasPolicy
                  );
  if (EFI_ERROR (Status)) {
    return Status;
  }
  ZeroMem (mAmdRasPolicy, sizeof (AMD_RAS_POLICY));

  mAmdRasPolicy->PFEHEnable =                PcdGetBool(PcdAmdCcxCfgPFEHEnable);
  mAmdRasPolicy->MceSwSmiData =              PcdGet8(PcdMceSwSmiData);
  mAmdRasPolicy->McaErrThreshEn =            PcdGetBool(PcdMcaErrThreshEn);
  mAmdRasPolicy->AmdMaskNbioSyncFlood =      PcdGetBool (PcdAmdMaskNbioSyncFlood);
  mAmdRasPolicy->AmdPcieAerReportMechanism = PcdGet8 (PcdAmdPcieAerReportMechanism);
  mAmdRasPolicy->AmdNbioRASControlV2 =       PcdGet8 (PcdAmdNbioRASControlV2);
  mAmdRasPolicy->AmdNbioPoisonConsumption =  PcdGetBool(PcdAmdNbioPoisonConsumption);

  mAmdRasPolicy->AmdMcaFruTextEnable = PcdGetBool(PcdAmdMcaFruTextEnable);
  SetAgesaCfg8 (ACT_CFG_UID_CmnCpuMcaFruTextEn, (UINT8)PcdGetBool(PcdAmdMcaFruTextEnable));

  if (PcdGet16 (PcdMcaErrThreshCount) == 0xFFFF)  {
    //Use default setting
    if (PcdGetBool(PcdAmdCcxCfgPFEHEnable)) {
      PcdSet16S (PcdMcaErrThreshCount, 0x0FFE);
    } else {
      PcdSet16S (PcdMcaErrThreshCount, 0x0FF5);
    }
  }
  mAmdRasPolicy->McaErrThreshCount = PcdGet16(PcdMcaErrThreshCount);
  mAmdRasPolicy->CmcNotificationType = PcdGet8(PcdCmcNotificationType);

  //AGESA Support for DRAM Corrected Error Counters
  mAmdRasPolicy->DdrEccErrorCounterEnable = PcdGet8(PcdAmdDdrEccErrorCounterEnable);
  SetAgesaCfg8 (ACT_CFG_UID_CmnMemCorrectedErrorCounterEnable, PcdGet8(PcdAmdDdrEccErrorCounterEnable));
  mAmdRasPolicy->DdrEccErrorCounterIntEnable = PcdGetBool(PcdAmdDdrEccErrorCounterIntEnable);
  SetAgesaCfg8 (ACT_CFG_UID_CmnMemCorrectedErrorCounterInterruptEnable, (UINT8)PcdGetBool(PcdAmdDdrEccErrorCounterIntEnable));
  mAmdRasPolicy->DdrEccErrorCounterLeakRate = PcdGet8(PcdAmdDdrEccErrorCounterLeakRate);
  mAmdRasPolicy->DdrEccErrorCounterStartCount = PcdGet16(PcdAmdDdrEccErrorCounterStartCount);
  //FCH software SMI command port
  mAmdRasPolicy->SwSmiCmdPortAddr = MmioRead16((ACPI_MMIO_BASE + PMIO_BASE + FCH_PMIOA_REG6A));
  //RAS Ids Debug Print Enable
  mAmdRasPolicy->RasDebugPrintEnabled = PcdGetBool(PcdAmdRasDebugPrintEnabled);
  //UMC ECS Error Interrupt
  mAmdRasPolicy->AmdMemEcsStatusInterrupt = PcdGetBool(PcdAmdMemEcsStatusInterrupt);

  return Status;
}

EFI_STATUS
CollectDimmMap(VOID)
{
  EFI_STATUS    Status = EFI_SUCCESS;
  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL *FabricTopology;
  UINTN     NumberOfInstalledProcessors;
  UINTN     NumberOfRootBridgesOnDie;
  UINTN     TotalNumberOfDie;
  UINT8     SegNumber;
  UINTN     BusNumberBase;
  UINTN     socketId;
  UINTN     dieId;
  UINTN     channelId;
  UINTN     channelIdInSystem;
  UINT32    SMUFuse;

  Status = gBS->AllocatePool (
      EfiReservedMemoryType,  // IN EFI_MEMORY_TYPE PoolType
      sizeof (ADDR_DATA),     // IN UINTN Size
      (VOID **)&gAddrData     // OUT VOID **Buffer
      );
  if (EFI_ERROR (Status)) {
    ASSERT_EFI_ERROR (Status);
    return Status;
  }
  ZeroMem (gAddrData, sizeof (ADDR_DATA));

  NumberOfInstalledProcessors = 0;
  TotalNumberOfDie = 0;
  NumberOfRootBridgesOnDie = 0;
  SegNumber = 0;
  BusNumberBase = 0;

  Status = gBS->LocateProtocol(&gAmdFabricTopologyServices2ProtocolGuid, NULL, (VOID **) &FabricTopology);
  FabricTopology->GetSystemInfo (FabricTopology, &NumberOfInstalledProcessors, NULL, NULL, NULL, NULL);

  for (socketId = 0; socketId < NumberOfInstalledProcessors; socketId++) {
    FabricTopology->GetProcessorInfo (FabricTopology, socketId, &TotalNumberOfDie, NULL);
    for (dieId = 0; dieId < TotalNumberOfDie; dieId++) {
      FabricTopology->GetRootBridgeInfo (FabricTopology, socketId, dieId, NumberOfRootBridgesOnDie, NULL, &BusNumberBase, NULL, NULL, NULL, NULL);

      BusNumberBase %= MAX_PCI_BUS_NUMBER_PER_SEGMENT;
      SegNumber = (UINT8)(BusNumberBase / MAX_PCI_BUS_NUMBER_PER_SEGMENT);
      //Get UMC Harvest FUSE
      SMUFuse = GetUmcHarvestFuse ((UINTN)SMN_SEG_BUS (SegNumber, BusNumberBase));

      for (channelId = 0; channelId < UMC_PER_DIE; channelId++) {
        if (0 == (SMUFuse & ((UINT32) 1 << channelId))) {
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "\nsocket %x die %x channel %x pci bus = %x\n", socketId, dieId, channelId, BusNumberBase);
          retrieve_regs (socketId, dieId, channelId, 0, BusNumberBase);
          channelIdInSystem = convert_to_addr_trans_index (socketId, dieId, channelId, 0);
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "\n channelIdInSystem = %x\n", channelIdInSystem);

        }
      }
    }
  }

  //Save Memory Address Data pointer to Amd Ras Policy
  mAmdRasPolicy->AddrData = gAddrData;

  return Status;
}

EFI_STATUS
CollectCpuMap(VOID)
{
  AMD_CORE_TOPOLOGY_SERVICES_V3_PROTOCOL  *CoreTopology;
  CPU_INFO                                *RasCpuMap;
  UINT32                                  Index;
  UINTN                                   SocketLoop;
  UINTN                                   DieLoop;
  UINTN                                   CcdLoop;
  UINTN                                   ComplexLoop;
  UINTN                                   CoreLoop;
  UINTN                                   ThreadLoop;
  UINTN                                   PhySocket;
  UINTN                                   PhyDie;
  UINTN                                   PhyCcd;
  UINTN                                   PhyComplex;
  UINTN                                   PhyCore;
  EFI_STATUS                              Status = EFI_SUCCESS;
  CORE_TOPOLOGY_ITERATION_RESULT          IterationResult;

  mAmdRasPolicy->RasCpuMap = NULL;
  CoreTopology = NULL;

  Status = gBS->LocateProtocol (&gAmdCoreTopologyServicesV3ProtocolGuid, NULL, (VOID **)&CoreTopology);
  if (EFI_ERROR (Status)) {
    return Status;
  }

  //
  //  Allocate memory and Initialize for Data block
  //
  Status = gBS->AllocatePool (
                  EfiReservedMemoryType,
                  sizeof (CPU_INFO) * RAS_MAX_CORES,
                  (VOID **)&RasCpuMap
                  );
  if (EFI_ERROR (Status)) {
    return Status;
  }
  ZeroMem (RasCpuMap, sizeof (CPU_INFO) * RAS_MAX_CORES);

  Index = 0;
  CORE_TOPOLOGY_V3_FOR_EACH_THREAD (CoreTopology, IterationResult, SocketLoop, DieLoop, CcdLoop, ComplexLoop, CoreLoop, ThreadLoop) {
    if (Index == RAS_MAX_CORES) {
      ASSERT (FALSE);
      break;
    }
    PhySocket = SocketLoop;
    PhyDie = DieLoop;
    PhyCcd = CcdLoop;
    PhyComplex = ComplexLoop;
    PhyCore = CoreLoop;
    if (CoreTopology->LogicalToPhysicalLocation (CoreTopology, &PhySocket, &PhyDie, &PhyCcd, &PhyComplex, &PhyCore) == EFI_SUCCESS) {
      RasCpuMap[Index].ProcessorNumber = Index;    //CPU Logic Number
      RasCpuMap[Index].SocketId = (UINT8)(PhySocket & 0xFF);
      RasCpuMap[Index].DieId = (UINT8)(PhyCcd & 0xFF);
      RasCpuMap[Index].CcxId = (UINT8)(PhyComplex & 0xFF);
      RasCpuMap[Index].CoreId = (UINT8)(PhyCore & 0xFF);
      RasCpuMap[Index].ThreadID = (UINT8)ThreadLoop;
      Index++;
    }
  }

  mAmdRasPolicy->TotalNumberOfProcessors = Index;

  //Update Ras CPU map pointer to AMD RAS Policy buffer.
  mAmdRasPolicy->RasCpuMap = RasCpuMap;

  return EFI_SUCCESS;
}


VOID
EFIAPI
CollectCoreMcaInfo (
  IN OUT   VOID *Buffer
)
{
  EFI_STATUS                Status;
  UINT32                    BankNum;
  MCA_IPID_MSR              McaIpid;
  CPU_MCA_INFO              *CpuMcaInfo;
  UINTN                     ProcNum;
  UINT32                    MaxMcaBankCount;

  if (mMpServices == NULL) {
    return;
  }

  Status = mMpServices->WhoAmI (mMpServices, &ProcNum);
  if (EFI_ERROR (Status)) {
    return;
  }

  CpuMcaInfo = Buffer;
  MaxMcaBankCount = (AsmReadMsr64 (MSR_MCG_CAP) & 0xFF);
  for (BankNum = 0; BankNum < MaxMcaBankCount; BankNum++) {
    McaIpid.Value = AsmReadMsr64 ((MCA_EXTENSION_BASE + ((BankNum * SMCA_REG_PER_BANK) | MCA_IPID_OFFSET)));

    (CpuMcaInfo + ProcNum)->BankMcaInfo[BankNum].McaIpid.Value = McaIpid.Value;
  }
  (CpuMcaInfo + ProcNum)->MaxMcaBankCount = MaxMcaBankCount;

  return;
}

EFI_STATUS
GetFirstThreadFromSocket1(
  IN     UINTN *FirstThreadId
)
{
  UINTN             ProcNum;
  UINT32            SocketId;
  UINTN             ThreadId;

  *FirstThreadId = 0;
  //Count valid records for reserve memory space
  for (ProcNum = 0; ProcNum < mAmdRasPolicy->TotalNumberOfProcessors; ProcNum++) {
    // Find the fisrt thread ID from socket 1
    ProcessorNumberToPhysicalThread(mAmdRasPolicy,
                                    ProcNum,
                                    &SocketId,
                                    &ThreadId);
    if ((*FirstThreadId == 0) && (SocketId == 1)) {
      *FirstThreadId = ProcNum;
      return EFI_SUCCESS;
    }
  }
  return EFI_SUCCESS;
}


BOOLEAN
GetMcaIpId(
  IN     UINTN ThreadId,
  IN     UINT32 BankNum,
     OUT UINT64 *McaIpidValue
)
{
  UINT32            McaIpInfoEntriesNum;
  UINT32            EntryIndex;

  McaIpInfoEntriesNum = (sizeof (gMcaIpInfo) / sizeof (gMcaIpInfo[0]));

  //Count valid records for reserve memory space
  for (EntryIndex = 0; EntryIndex < McaIpInfoEntriesNum; EntryIndex++) {
    if ((gMcaIpInfo[EntryIndex].ThreadId == ThreadId) &&
        (gMcaIpInfo[EntryIndex].BankNum == BankNum)) {
      *McaIpidValue = gMcaIpInfo[EntryIndex].McaIpid;
      //Found valid instance
      return TRUE;
    }
  }

  //IP not found
  return FALSE;
}

EFI_STATUS
CollectMcaMap(VOID)
{
  EFI_STATUS        Status = EFI_SUCCESS;
  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL  *FabricTopologyServices;
  MCA_BANK_MAP      *McaBankMap;
  PRE_MCA_BANK_MAP  *PreMcaBankMap;
  UINTN             NumberOfSockets;
  UINTN             NumberOfSystemDies;
  UINT32            Socket;
  UINTN             ProcNum;
  UINTN             MaxProcNum;
  UINT32            PreMcaBankMapLength;
  UINT32            McaBankMapLength;
  UINT32            BankNum;
  UINT32            RecordCount;
  UINT32            ThreadCount;
  UINTN             FirstThreadId;
  UINTN             ThreadIdPerSocket;
  UINTN             ProcessorsPerSocket;


  PreMcaBankMapLength = (UINT32)((sizeof (PRE_MCA_BANK_MAP)) * mAmdRasPolicy->TotalNumberOfProcessors);
  mAmdRasPolicy->McaBankMap = NULL;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Pre-map Length: 0x%x\n", PreMcaBankMapLength);

  Status = gBS->LocateProtocol (&gAmdFabricTopologyServices2ProtocolGuid,
                                NULL,
                                (VOID **)&FabricTopologyServices);
  if (EFI_ERROR (Status)) {
    return Status;
  }

  Status = FabricTopologyServices->GetSystemInfo (FabricTopologyServices,
                                                  &NumberOfSockets,
                                                  &NumberOfSystemDies,
                                                  NULL,
                                                  NULL,
                                                  NULL);
  if (EFI_ERROR (Status)) {
    return Status;
  }
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "NumberOfSockets: %d, NumberOfSystemDies: %d\n", NumberOfSockets, NumberOfSystemDies);

  Status = gBS->LocateProtocol (&gEfiMpServiceProtocolGuid, NULL, (VOID **)&mMpServices);
  if (EFI_ERROR (Status)) {
    return Status;    // Error detected while locating EFI_MP_SERVICES_PROTOCOL
  }

  Status = gBS->AllocatePool (
      EfiBootServicesData,          // IN EFI_MEMORY_TYPE PoolType
      PreMcaBankMapLength,          // IN UINTN Size
      (VOID **)&PreMcaBankMap       // OUT VOID **Buffer
      );
  if (EFI_ERROR (Status)) {
    return Status;    // Error detected while trying to locate pool
  }
  ZeroMem (PreMcaBankMap, PreMcaBankMapLength);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "PreMcaBankMap Address: 0x%08x\n", (UINTN)PreMcaBankMap);

  // Execute on running APs
  mMpServices->StartupAllAPs (
      mMpServices,
      CollectCoreMcaInfo,
      PcdGetBool (PcdAmdStartupAllAPsSingleThread),
      NULL,
      0,
      (VOID *) &PreMcaBankMap->CpuMcaInfo,
      NULL);

  // For BSP
  CollectCoreMcaInfo(&PreMcaBankMap->CpuMcaInfo);

  // Collect system processor config
  ThreadCount = CcxGetThreadsPerCore ();
  GetFirstThreadFromSocket1(&FirstThreadId);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ThreadCount Per Core: %d, Socket 1 First Thread number: %d\n", ThreadCount, FirstThreadId);

  RecordCount = 0;
  //Count valid records for reserve memory space
  for (ProcNum = 0; ProcNum < mAmdRasPolicy->TotalNumberOfProcessors; ProcNum++) {
    for (BankNum = 0; BankNum < PreMcaBankMap->CpuMcaInfo[ProcNum].MaxMcaBankCount; BankNum++) {
      if (PreMcaBankMap->CpuMcaInfo[ProcNum].BankMcaInfo[BankNum].McaIpid.Value != 0) {
        /*IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Processor: %d, Bank: %d, MCA_IPID: 0x%016lX\n",
               ProcNum,
               BankNum,
               PreMcaBankMap->CpuMcaInfo[ProcNum].BankMcaInfo[BankNum].McaIpid.Value);*/
        if ((PreMcaBankMap->CpuMcaInfo[ProcNum].BankMcaInfo[BankNum].McaIpid.Field.HardwareID == MCA_SATA_ID) ||
            (PreMcaBankMap->CpuMcaInfo[ProcNum].BankMcaInfo[BankNum].McaIpid.Field.HardwareID == MCA_USB_ID) ||
            (PreMcaBankMap->CpuMcaInfo[ProcNum].BankMcaInfo[BankNum].McaIpid.Field.HardwareID == MCA_UMC_ID) ||
            (PreMcaBankMap->CpuMcaInfo[ProcNum].BankMcaInfo[BankNum].McaIpid.Field.HardwareID == MCA_PCS_XGMI_ID)) {
          RecordCount++;
        }
      } else {
        //Add default MCA info here when no valid MCA_IPID information for SATA or USB bank.
        if ((ProcNum < FirstThreadId) && (0 != FirstThreadId)) {
          //Socket 0
          ThreadIdPerSocket = ProcNum;
        } else {
          //Socket 1
          ThreadIdPerSocket = ProcNum - FirstThreadId;
        }
        if (ThreadCount == 1) {
          //SMT disabled
          ThreadIdPerSocket = ThreadIdPerSocket << 1;
        }

        if (GetMcaIpId(ThreadIdPerSocket, BankNum, &PreMcaBankMap->CpuMcaInfo[ProcNum].BankMcaInfo[BankNum].McaIpid.Value))
        {
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Processor: %d, Bank: %d, Default MCA_IPID: 0x%016lX\n",
                 ProcNum,
                 BankNum,
                 PreMcaBankMap->CpuMcaInfo[ProcNum].BankMcaInfo[BankNum].McaIpid.Value);
          RecordCount++;
        }
      }
    }
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Valid Record counter: %d\n", RecordCount);
  if (0 == RecordCount) {
    //No Support IP banks found
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "No Support IP banks found: %d\n", RecordCount);
    gBS->FreePool(PreMcaBankMap);
    return EFI_SUCCESS;
  }

  McaBankMapLength = (sizeof (MCA_BANK_MAP)) + ((sizeof (MCA_INFO)) * RecordCount);

  Status = gBS->AllocatePool (
      EfiReservedMemoryType,        // IN EFI_MEMORY_TYPE PoolType
      McaBankMapLength,             // IN UINTN Size
      (VOID **)&McaBankMap          // OUT VOID **Buffer
      );
  if (EFI_ERROR (Status)) {
    gBS->FreePool(PreMcaBankMap);
    return Status;
  }
  ZeroMem (McaBankMap, McaBankMapLength);

  //Move data
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "MCA bank Map for Harvest:\n");
  for (ProcNum = 0; ProcNum < mAmdRasPolicy->TotalNumberOfProcessors; ProcNum++) {
    for (BankNum = 0; BankNum < PreMcaBankMap->CpuMcaInfo[ProcNum].MaxMcaBankCount; BankNum++) {
      if ((PreMcaBankMap->CpuMcaInfo[ProcNum].BankMcaInfo[BankNum].McaIpid.Field.HardwareID == MCA_SATA_ID) ||
          (PreMcaBankMap->CpuMcaInfo[ProcNum].BankMcaInfo[BankNum].McaIpid.Field.HardwareID == MCA_USB_ID) ||
          (PreMcaBankMap->CpuMcaInfo[ProcNum].BankMcaInfo[BankNum].McaIpid.Field.HardwareID == MCA_UMC_ID)) {

        McaBankMap->McaInfo[McaBankMap->InstanceCounter].McaIpid.Value = PreMcaBankMap->CpuMcaInfo[ProcNum].BankMcaInfo[BankNum].McaIpid.Value;
        ProcessorNumberToPhysicalThread(mAmdRasPolicy,
                                        ProcNum,
                                        &McaBankMap->McaInfo[McaBankMap->InstanceCounter].SocketId,
                                        &McaBankMap->McaInfo[McaBankMap->InstanceCounter].ThreadId);

        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Entry %d: Socket ID: %d, ThreadId: %d, MCA_IPID: 0x%016lX, MCA_IPID[HardwareID]: 0x%x\n",
               McaBankMap->InstanceCounter,
               McaBankMap->McaInfo[McaBankMap->InstanceCounter].SocketId,
               McaBankMap->McaInfo[McaBankMap->InstanceCounter].ThreadId,
               McaBankMap->McaInfo[McaBankMap->InstanceCounter].McaIpid.Value,
               McaBankMap->McaInfo[McaBankMap->InstanceCounter].McaIpid.Field.HardwareID);
        //Assert if McaBankMap->InstanceCounter exceed limit.
        ASSERT(McaBankMap->InstanceCounter < RecordCount);
        McaBankMap->InstanceCounter++;
      }
    }
  }

  //
  // Copy the XGMI Bank info in the sequence of Pcs1->Pcs3->Pcs4->Pc5->Pcs6->Pcs7.
  //
  ProcessorsPerSocket = mAmdRasPolicy->TotalNumberOfProcessors/NumberOfSockets;
  for (Socket = 0; Socket < NumberOfSockets; Socket++) {
    ProcNum = ProcessorsPerSocket * Socket;
    MaxProcNum = ProcessorsPerSocket * (Socket + 1);
    for (; ProcNum < MaxProcNum; ProcNum++) {
      for (BankNum = 0; BankNum < MAX_MCA_BANKS/2; BankNum++) {
        if (PreMcaBankMap->CpuMcaInfo[ProcNum].BankMcaInfo[BankNum].McaIpid.Field.HardwareID == MCA_PCS_XGMI_ID) {
          McaBankMap->McaInfo[McaBankMap->InstanceCounter].McaIpid.Value = PreMcaBankMap->CpuMcaInfo[ProcNum].BankMcaInfo[BankNum].McaIpid.Value;
          ProcessorNumberToPhysicalThread(mAmdRasPolicy,
                                          ProcNum,
                                          &McaBankMap->McaInfo[McaBankMap->InstanceCounter].SocketId,
                                          &McaBankMap->McaInfo[McaBankMap->InstanceCounter].ThreadId);

          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Entry %d: Socket ID: %d, ThreadId: %d, MCA_IPID: 0x%016lX, MCA_IPID[HardwareID]: 0x%x\n",
                McaBankMap->InstanceCounter,
                McaBankMap->McaInfo[McaBankMap->InstanceCounter].SocketId,
                McaBankMap->McaInfo[McaBankMap->InstanceCounter].ThreadId,
                McaBankMap->McaInfo[McaBankMap->InstanceCounter].McaIpid.Value,
                McaBankMap->McaInfo[McaBankMap->InstanceCounter].McaIpid.Field.HardwareID);
          //Assert if McaBankMap->InstanceCounter exceed limit.
          ASSERT(McaBankMap->InstanceCounter < RecordCount);
          McaBankMap->InstanceCounter++;
        }
      }
    }

    ProcNum = ProcessorsPerSocket * Socket;
    if (PreMcaBankMap->CpuMcaInfo[ProcNum].MaxMcaBankCount > MAX_MCA_BANKS/2) {
      for (; ProcNum < MaxProcNum; ProcNum++) {
        for (BankNum = MAX_MCA_BANKS/2; BankNum < PreMcaBankMap->CpuMcaInfo[ProcNum].MaxMcaBankCount; BankNum++) {
          if (PreMcaBankMap->CpuMcaInfo[ProcNum].BankMcaInfo[BankNum].McaIpid.Field.HardwareID == MCA_PCS_XGMI_ID) {

            McaBankMap->McaInfo[McaBankMap->InstanceCounter].McaIpid.Value = PreMcaBankMap->CpuMcaInfo[ProcNum].BankMcaInfo[BankNum].McaIpid.Value;
            ProcessorNumberToPhysicalThread(mAmdRasPolicy,
                                            ProcNum,
                                            &McaBankMap->McaInfo[McaBankMap->InstanceCounter].SocketId,
                                            &McaBankMap->McaInfo[McaBankMap->InstanceCounter].ThreadId);

            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Entry %d: Socket ID: %d, ThreadId: %d, MCA_IPID: 0x%016lX, MCA_IPID[HardwareID]: 0x%x\n",
                  McaBankMap->InstanceCounter,
                  McaBankMap->McaInfo[McaBankMap->InstanceCounter].SocketId,
                  McaBankMap->McaInfo[McaBankMap->InstanceCounter].ThreadId,
                  McaBankMap->McaInfo[McaBankMap->InstanceCounter].McaIpid.Value,
                  McaBankMap->McaInfo[McaBankMap->InstanceCounter].McaIpid.Field.HardwareID);
            //Assert if McaBankMap->InstanceCounter exceed limit.
            ASSERT(McaBankMap->InstanceCounter < RecordCount);
            McaBankMap->InstanceCounter++;
          }
        }
      }
    }
  }

  gBS->FreePool(PreMcaBankMap);

  //Update MCA bank map pointer to AMD RAS Policy buffer.
  mAmdRasPolicy->McaBankMap = McaBankMap;

  return (Status);
}


EFI_STATUS
SendSpecifiedPcdToPSP(VOID)
{
  PCD_VALUES  PcdValues;

  PcdValues.PcdSyncFloodToApml = PcdGetBool (PcdSyncFloodToApml);
  PcdValues.PcdResetCpuOnSyncFlood = PcdGetBool (PcdResetCpuOnSyncFlood);
  PcdValues.PcdDelayResetCpuOnSyncFlood = PcdGet8 (PcdDelayResetCpuOnSyncFlood);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] SendSpecifiedPcdToPSP: PcdSyncFloodToApml - 0x%02x, PcdResetCpuOnSyncFlood - 0x%02x PcdDelayResetCpuOnSyncFlood - 0x%02x\n",
         PcdGetBool (PcdSyncFloodToApml), PcdGetBool (PcdResetCpuOnSyncFlood), PcdGet8 (PcdDelayResetCpuOnSyncFlood));

  return PspMboxBiosSetPcdValues (&PcdValues);
}

