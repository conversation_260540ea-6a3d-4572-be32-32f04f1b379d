TOKEN
    Name  = "AmdCbsPkg_SUPPORT"
    Value  = "1"
    Help  = "Switch for Enabling AmdCbsPkg support in the project"
    TokenType = Boolean
    TargetH = Yes
    Master = Yes
End

PATH
    Name  = "AmdCbsPkg_DIR"
    Help  = "Path to Variables Module in Project"
End

MODULE
    Help  = "Includes AmdCbsPkg.mak to Project"
    File  = "AmdCbsPkg.mak"
End

TOKEN
    Name  = "CBSBUILD_SUPPORT"
    Value  = "1"
    Help  = "Switch to enable CBS Build in the project"
    TokenType = Boolean
    TargetMAK = Yes
End

TOKEN
    Name = "PEIM_AmdCbsPkg_CbsBasePei_CbsBasePeiBRH_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCbsPkg_CbsBasePei_CbsBasePeiBRH_INF"
    File = "CbsBasePei/CbsBasePeiBRH.inf"
    Package = "AmdCbsPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCbsPkg_CbsBasePei_CbsBasePeiBRH_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsFuncLibPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsFuncLibPei_INF"
    File = "Library/Family/0x1A/BRH/External/CbsFuncLibPei.inf"
    Package = "AmdCbsPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsFuncLibPei_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CbsPeiFuncLib"
    Instance = "AmdCbsPkg.AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsFuncLibPei_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsFuncLibPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AmdCbsPkg_Build_ResourceBRH_CbsSetAgesaPcdLibBRH_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCbsPkg_Build_ResourceBRH_CbsSetAgesaPcdLibBRH_INF"
    File = "Build/ResourceBRH/CbsSetAgesaPcdLibBRH.inf"
    Package = "AmdCbsPkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AmdCbsPkg_Build_ResourceBRH_CbsSetAgesaPcdLibBRH_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CbsSetAgesaPcdLib"
    Instance = "AmdCbsPkg.AmdCbsPkg_Build_ResourceBRH_CbsSetAgesaPcdLibBRH_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCbsPkg_Build_ResourceBRH_CbsSetAgesaPcdLibBRH_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCbsPkg_BctBaseSmm_BctBaseSmmBRH_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCbsPkg_BctBaseSmm_BctBaseSmmBRH_INF"
    File = "BctBaseSmm/BctBaseSmmBRH.inf"
    Package = "AmdCbsPkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AmdCbsPkg_BctBaseSmm_BctBaseSmmBRH_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsBctSmmLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsBctSmmLib_INF"
    File = "Library/Family/0x1A/BRH/External/CbsBctSmmLib.inf"
    Package = "AmdCbsPkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsBctSmmLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CbsBctSmmLib"
    Instance = "AmdCbsPkg.AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsBctSmmLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsBctSmmLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCbsPkg_CbsSetupDxe_CbsSetupDxeBRH_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCbsPkg_CbsSetupDxe_CbsSetupDxeBRH_INF"
    File = "CbsSetupDxe/CbsSetupDxeBRH.inf"
    Package = "AmdCbsPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCbsPkg_CbsSetupDxe_CbsSetupDxeBRH_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsFuncLibDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsFuncLibDxe_INF"
    File = "Library/Family/0x1A/BRH/External/CbsFuncLibDxe.inf"
    Package = "AmdCbsPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsFuncLibDxe_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CbsDxeFuncLib"
    Instance = "AmdCbsPkg.AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsFuncLibDxe_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsFuncLibDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsSetupLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsSetupLib_INF"
    File = "Library/Family/0x1A/BRH/External/CbsSetupLib.inf"
    Package = "AmdCbsPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsSetupLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CbsSetupLib"
    Instance = "AmdCbsPkg.AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsSetupLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsSetupLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCbsPkg_CbsBaseDxe_CbsBaseDxeBRH_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCbsPkg_CbsBaseDxe_CbsBaseDxeBRH_INF"
    File = "CbsBaseDxe/CbsBaseDxeBRH.inf"
    Package = "AmdCbsPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCbsPkg_CbsBaseDxe_CbsBaseDxeBRH_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCbsPkg_CbsSetupSmm_CbsSetupSmmBRH_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCbsPkg_CbsSetupSmm_CbsSetupSmmBRH_INF"
    File = "CbsSetupSmm/CbsSetupSmmBRH.inf"
    Package = "AmdCbsPkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AmdCbsPkg_CbsSetupSmm_CbsSetupSmmBRH_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsUpdateApcbLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsUpdateApcbLib_INF"
    File = "Library/Family/0x1A/BRH/External/CbsUpdateApcbLib.inf"
    Package = "AmdCbsPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsUpdateApcbLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CbsUpdateApcbLib"
    Instance = "AmdCbsPkg.AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsUpdateApcbLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsUpdateApcbLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsUpdateApcbLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsUpdateApcbLib_INF"
    File = "Library/Family/0x1A/BRH/External/CbsUpdateApcbLib.inf"
    Package = "AmdCbsPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_SMM_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsUpdateApcbLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CbsUpdateApcbLib"
    Instance = "AmdCbsPkg.AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsUpdateApcbLib_INF"
    ModuleTypes = "DXE_SMM_DRIVER DXE_RUNTIME_DRIVER"
    Token = "DXE_SMM_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsUpdateApcbLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsIdsLibDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsIdsLibDxe_INF"
    File = "Library/Family/0x1A/BRH/External/CbsIdsLibDxe.inf"
    Package = "AmdCbsPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsIdsLibDxe_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdIdsHookExtLib"
    Instance = "AmdCbsPkg.AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsIdsLibDxe_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsIdsLibDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AmdCbsPkg_Library_CbsSmmCommLib_CbsSmmCommLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCbsPkg_Library_CbsSmmCommLib_CbsSmmCommLib_INF"
    File = "Library/CbsSmmCommLib/CbsSmmCommLib.inf"
    Package = "AmdCbsPkg"
    ModuleTypes = "BASE"
    Token = "DXE_SMM_DRIVER_AmdCbsPkg_Library_CbsSmmCommLib_CbsSmmCommLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CbsSmmCommLib"
    Instance = "AmdCbsPkg.AmdCbsPkg_Library_CbsSmmCommLib_CbsSmmCommLib_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AmdCbsPkg_Library_CbsSmmCommLib_CbsSmmCommLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCbsPkg_Library_CbsSmmCommLib_CbsSmmCommLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCbsPkg_Library_CbsSmmCommLib_CbsSmmCommLib_INF"
    File = "Library/CbsSmmCommLib/CbsSmmCommLib.inf"
    Package = "AmdCbsPkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AmdCbsPkg_Library_CbsSmmCommLib_CbsSmmCommLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CbsSmmCommLib"
    Instance = "AmdCbsPkg.AmdCbsPkg_Library_CbsSmmCommLib_CbsSmmCommLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCbsPkg_Library_CbsSmmCommLib_CbsSmmCommLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsBctSmmLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsBctSmmLib_INF"
    File = "Library/Family/0x1A/BRH/External/CbsBctSmmLib.inf"
    Package = "AmdCbsPkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsBctSmmLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CbsBctSmmLib"
    Instance = "AmdCbsPkg.AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsBctSmmLib_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsBctSmmLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsIdsLibPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsIdsLibPei_INF"
    File = "Library/Family/0x1A/BRH/External/CbsIdsLibPei.inf"
    Package = "AmdCbsPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsIdsLibPei_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdIdsHookExtLib"
    Instance = "AmdCbsPkg.AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsIdsLibPei_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsIdsLibPei_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CbsPeiFuncLib"
    Instance  = "AmdCbsPkg.AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsFuncLibPei_INF"
    Override  = "AmdCbsPkg.AmdCbsPkg_CbsBasePei_CbsBasePeiBRH_INF"
End

LibraryMapping
    Class = "CbsSetAgesaPcdLib"
    Instance  = "AmdCbsPkg.AmdCbsPkg_Build_ResourceBRH_CbsSetAgesaPcdLibBRH_INF"
    Override  = "AmdCbsPkg.AmdCbsPkg_CbsBasePei_CbsBasePeiBRH_INF"
End

LibraryMapping
    Class = "CbsBctSmmLib"
    Instance  = "AmdCbsPkg.AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsBctSmmLib_INF"
    Override  = "AmdCbsPkg.AmdCbsPkg_BctBaseSmm_BctBaseSmmBRH_INF"
End

LibraryMapping
    Class = "CbsDxeFuncLib"
    Instance  = "AmdCbsPkg.AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsFuncLibDxe_INF"
    Override  = "AmdCbsPkg.AmdCbsPkg_CbsSetupDxe_CbsSetupDxeBRH_INF"
End

LibraryMapping
    Class = "CbsSetupLib"
    Instance  = "AmdCbsPkg.AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsSetupLib_INF"
    Override  = "AmdCbsPkg.AmdCbsPkg_CbsSetupDxe_CbsSetupDxeBRH_INF"
End

LibraryMapping
    Class = "CbsDxeFuncLib"
    Instance  = "AmdCbsPkg.AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsFuncLibDxe_INF"
    Override  = "AmdCbsPkg.AmdCbsPkg_CbsSetupDxe_CbsSetupDxeBRH_INF"
End

LibraryMapping
    Class = "CbsDxeFuncLib"
    Instance  = "AmdCbsPkg.AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsFuncLibDxe_INF"
    Override  = "AmdCbsPkg.AmdCbsPkg_CbsBaseDxe_CbsBaseDxeBRH_INF"
End

LibraryMapping
    Class = "CbsUpdateApcbLib"
    Instance  = "AmdCbsPkg.AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsUpdateApcbLib_INF"
    Override  = "AmdCbsPkg.AmdCbsPkg_CbsSetupSmm_CbsSetupSmmBRH_INF"
End

