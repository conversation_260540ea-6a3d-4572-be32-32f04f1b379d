#pragma message( "Compal Server Override Compiling-" __FILE__ )
//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************
 
/** @file AmiOemRasSmm.c
    ErrorLog helper driver   

**/

#include "Token.h"
//#include <AmiDxeLib.h>
#include <Library/RasIpmiLibBrh.h>
#include <Library/BaseMemoryLib.h>
#include "AmiOemRasSmm.h"
#include <Protocol/AmdCpmRasOemProtocol.h>
#include <Protocol/GenericElogProtocol.h>
#include <AmiRasCommon.h>
#include <Library/RasSmbiosLibBrh.h>
#include <Library/UefiBootServicesTableLib.h>   //COMPAL_CHANGE

//COMPAL_CHANGE >>> Add SKU_ID Protocol Support
#include <Protocol/SkuIdProtocol.h>
//COMPAL_CHANGE <<<

#if HardwareHealthManagement_SUPPORT
#include "Protocol/AmdRasSmm2Protocol.h"
#include <Protocol/SystemInventoryInfoProtocol.h>
#include <HardwareHealthManagement.h>
#include <Library/PrintLib.h>
#include <Include/AmiIpmiNetFnTransportDefinitions.h>
#include <Protocol/IPMITransportProtocol.h>
#include <Include/AmdRas.h>
#endif
/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */

UINT8                       ErrorLogData[FixedPcdGet8 (PcdErrorLogDataBufferSize)];
COMMON_ELOG_PRIVATE         CommonElogPrivate;
EFI_SM_ELOG_REDIR_PROTOCOL  *EfiRedirElogProtocol;
VOID                        *gRegistration;
PLATFORM_APEI_PRIVATE_BUFFER_V3 *mPlatformApeiData = NULL; //COMPAL_CHANGE

//COMPAL_CHANGE >>> Add SKU_ID Protocol Support
SKU_ID_PROTOCOL             *mSkuIdProtocol = NULL;
UINT8                       *mSkuIdData = NULL;
//COMPAL_CHANGE <<<

#if HardwareHealthManagement_SUPPORT 

AMD_RAS_SMM2_PROTOCOL        *AmdRasSmm2Protocol = NULL;

typedef enum {
  Unknown       = 0,
  PciSource ,
  CsiSource,
  MemSource,
  ProcSource,
  UncoreSource,
  IohQpiSource,
  IohCoreSource,
  IohVtdSource,
  IohItcSource,
  IohOtcSource,
  IohDmaSource,
  CoreSource,
  PcuSource,
  UpiSource,
  ChaSource
} ERROR_SOURCE; 

typedef struct {
  ERROR_SOURCE       ErrorType;
  UINT8                           ErrorSeverity;
} ERROR_HEADER;

typedef struct {
  BOOLEAN                   Valid;
  UINT32                    BankNum;
  UINT32                    ApicId;
  UINT32                    BankType;
  UINT32                    BankScope;
  UINT64                    McaStatus;
  UINT64                    McaAddress;
  UINT64                    McaMisc;
} MCA_BANK_INFO;

typedef struct {
  //
  // Header-like information
  //
  ERROR_HEADER    Header;
  // Standard fields
  UINT64                    ValidBits;
  UINT64                    ErrorStatus;
  UINT64                    PhysAddr;
  UINT64                    PhysAddrMask;
  UINT16                    Node;
  UINT16                    Card;
  UINT16                    Module;
  UINT16                    Bank;
  UINT16                    Device;
  UINT16                    Row;
  UINT16                    Col;
  UINT16                    BitPosition;
  UINT64                    RequesterId;
  UINT64                    ResponderId;
  UINT64                    TargetId;
  UINT8                     ErrorType;
  UINT8                     Extended;
  UINT16                    RankNumber;
  UINT16                    CardHandle;
  UINT16                    ModuleHandle;
  MCA_BANK_INFO             McaBankInfo;
  UINT8                     ErrorEvent;  // OEM Hook Support (ErrorEvent - 0 SDDC 1 ADDDC 2 RANK SPARING)
  UINT64                    RankAddress;
  UINT8                     OtherMcBankCount;  // Total valid MCBanks
  MCA_BANK_INFO             OtherMcBankInfo[MAX_NUM_MCA_BANKS];  // Total valid MC Banks data
} AMI_MEMORY_ERROR_SECTION;
#endif


AMD_CPM_RAS_OEM_PROTOCOL AmdCpmRasOemSmmProtocol = {
  OemErrorLogEventMemTest,
  OemErrorLogEventPmic,
  OemErrorLogEventCxlProtocol,
  OemErrorLogEventCxlComponent,
  OemErrorLogEventMem,
  OemErrorLogEventNbio,
  OemErrorLogEventPcie,
  OemErrorLogEventSMN,
  OemErrorLogEventMca,
  OemErrorLogEventProcessor,
  OemErrorLogEventRtPpr
};

#if HardwareHealthManagement_SUPPORT 
SYSTEM_INVENTORY_INFO_PROTOCOL  *gSmmSystemInventoryInfoProtocol = NULL;
EFI_IPMI_TRANSPORT              *gIpmiTransport;
VOID                        *gSmmSystemInventoryInfoProtocolRegistration;

VOID
SendIpmiCommandHealthStatusUpdate (
  IN        UINTN                       CmdSize, 
  IN        IPMI_OEM_CMD_HEALTH_REQUEST_DATA   *RequestData 
)
{
    EFI_STATUS                  Status;
    UINTN                       Index;
    UINT8                       IpmiOemCmdResponseDataSize;
    IPMI_OEM_CMD_RESPONSE_DATA       RedFishCmdRespData;
    IPMI_OEM_REDIS_REPLY_STRING           *RedisReply;

    IpmiOemCmdResponseDataSize = sizeof(IPMI_OEM_CMD_RESPONSE_DATA);

    if (gIpmiTransport== NULL){
        return;
    }
    Status = gIpmiTransport-> SendIpmiCommand (
                                gIpmiTransport,
                                HHM_IPMI_OEM_NETFN,
#if IPMI_SUPPORT
                                BMC_LUN,
#elif IPMI2_SUPPORT
                                AMI_BMC_LUN,
#endif
                                HHM_OEM_IPMI_DEV_HEALTH_REDFISH_COMMAND,
                                (UINT8*)RequestData,
                                (UINT8)CmdSize,
                                (UINT8 *)&RedFishCmdRespData,
                                &IpmiOemCmdResponseDataSize);
    DEBUG ((DEBUG_INFO, "HHM_OEM_IPMI_DEV_HEALTH_REDFISH_COMMAND Status :%r, \n", Status));
#if IPMI_SUPPORT
    DEBUG ((DEBUG_INFO, "!!! --- IpmiTransportPpi->CommandCompletionCode:0x%X --- !!!\n", gIpmiTransport->CommandCompletionCode));
#elif IPMI2_SUPPORT
    DEBUG ((DEBUG_INFO, "!!! --- IpmiTransportPpi->CommandCompletionCode:0x%X --- !!!\n", RedFishCmdRespData.CompletionCode));
#endif
    ASSERT_EFI_ERROR(Status);
    
    DEBUG ((DEBUG_INFO, "::Redfish Command Response Data::\n"));
    DEBUG ((DEBUG_INFO, "Response Type: %d; (1-String, 5-Status, 6-Error)\n", RedFishCmdRespData.Type));
    
    if(RedFishCmdRespData.Type == REDIS_REPLY_ERROR){
        DEBUG ((DEBUG_INFO, "!!! ERROR Responseeck String below\n"));
    }
    
    if((RedFishCmdRespData.Type == REDIS_REPLY_STATUS) || (RedFishCmdRespData.Type == REDIS_REPLY_ERROR)|| 
            (RedFishCmdRespData.Type == REDIS_REPLY_STRING)){
        RedisReply = (IPMI_OEM_REDIS_REPLY_STRING *)&(RedFishCmdRespData.Type);
        DEBUG ((DEBUG_INFO, "Response Len: %d\n",RedisReply->Len ));
        DEBUG ((DEBUG_INFO, "Response String: "));
        for(Index=0; (Index < RedisReply->Len) && (Index < 50) ; Index++  ){
            DEBUG ((DEBUG_INFO, "%c ", RedisReply->String[Index]));
        }
        DEBUG((DEBUG_INFO,"\n"));
    }

    return;
}

/**
This function parses AMI System Inventory Info SMM Protocol instance to find
the ID of the affected device and send command to BMC to update Live 
Health.

@param[in] DevId          Device ID Variable to identify Processor/Memory/PCIe

@param[in] DataPtr        Pointer to Error Record Structure of respective Device type

@retval VOID
**/

VOID
FindRedFishID (
IN    UINT8   DevId,
IN    VOID    *DataPtr,
IN    VOID    *AddrPtr
)
{
    EFI_STATUS                            Status ;
    UINTN                                 DevIndex;
    UINT8                                 Socket;
    UINT8                                 DdrChannel;
    UINT8                                 DieId;
    SYS_INV_DIMM_INFO                     *DIMMData;
    AMI_MEMORY_ERROR_SECTION              *MemInfo;
    NORMALIZED_ADDRESS                    *Address;
    CHAR8                                 *OemRedFishLiveStatus = NULL;
    UINTN                                 CmdSize=0;
    BOOLEAN                               DevceFound=FALSE;
    SYS_INV_PCI_INFO                      *PCIData;
    IPMI_OEM_CMD_HEALTH_REQUEST_DATA      RequestData;
    GENERIC_PCIE_AER_ERR_ENTRY_V3         *PcieInfo;
    
    DEBUG((DEBUG_INFO,"%a() Entry\n",__FUNCTION__));
    
    if (gSmmSystemInventoryInfoProtocol == NULL) {
        Status = gSmst->SmmLocateProtocol (
                          &gAmiSmmSystemInventoryInfoProtocolGuid,
                          NULL,
                          &gSmmSystemInventoryInfoProtocol);
        ASSERT_EFI_ERROR (Status);
        
        if (EFI_ERROR (Status)) {
            return ;
        }
    }

    if (!gSmmSystemInventoryInfoProtocol->InventoryDataReady) {
        return;
    }
    
    for (DevIndex = 0; DevIndex < gSmmSystemInventoryInfoProtocol->DevInfoCount ;DevIndex++) {
        DevceFound = FALSE;

        switch (gSmmSystemInventoryInfoProtocol->DevInfoList[DevIndex]->Dp.DeviceStatus.DeviceType) {

        case (SysInvDevDimm):
          if(DevId != (SysInvDevDimm)) {
              continue;
          }
          DIMMData = (SYS_INV_DIMM_INFO*)&gSmmSystemInventoryInfoProtocol->DevInfoList[DevIndex]->DisplayPtr.Dimm;
          DEBUG ((DEBUG_INFO, "DIMMData: Channel %d |MemoryController %d |Slot %d |Socket %d\n", DIMMData->MemoryLocation.Channel, DIMMData->MemoryLocation.MemoryController, DIMMData->MemoryLocation.Slot, DIMMData->MemoryLocation.Socket));
          
        MemInfo = (AMI_MEMORY_ERROR_SECTION *) DataPtr;
        Address = (NORMALIZED_ADDRESS *)AddrPtr;
        Socket = Address->normalizedSocketId;
        DieId = Address->normalizedDieId;
        DdrChannel = Address->normalizedChannelId;
        DEBUG ((DEBUG_INFO, "Address: Socket %d |DieId %d |DdrChannel %d \n", Address->normalizedSocketId, Address->normalizedDieId, Address->normalizedChannelId));
        
        if ((DIMMData->MemoryLocation.Socket == Socket) && (DIMMData->MemoryLocation.MemoryController== DdrChannel)) {
              Status = gSmst->SmmAllocatePool(EfiRuntimeServicesData,MAX_REDFISH_CMD_SIZE,&OemRedFishLiveStatus);
              //ASSERT_EFI_ERROR(Status);
              
              RequestData.Resource = DevId;
              
              ZeroMem (OemRedFishLiveStatus, MAX_REDFISH_CMD_SIZE);
              
            DEBUG ((DEBUG_INFO, "MemInfo->Header.ErrorSeverity = %d \n", MemInfo->Header.ErrorSeverity));
              // Device is Located. Need to find Severity.
              
            if ((MemInfo->Header.ErrorSeverity == EFI_ACPI_5_0_ERROR_SEVERITY_CORRECTABLE) ||
                      (MemInfo->Header.ErrorSeverity == EFI_ACPI_5_0_ERROR_SEVERITY_CORRECTED))  {
                  /// Memory CE Error: DIMMData->Id 
                  // Device Found: Redfish ID can be updated from here.
                  DEBUG((DEBUG_INFO, "Device Found: Memory CE with ID: %a\n",DIMMData->Id));
                  
                  AsciiSPrint(OemRedFishLiveStatus,MAX_REDFISH_CMD_SIZE,\
                          "%a",DIMMData->Id);
                  CmdSize = AsciiStrSize(OemRedFishLiveStatus);
                  DEBUG((DEBUG_INFO, "Memory Device (Warning)\n"));
                  DEBUG((DEBUG_INFO, "Redfish Cmd: %a\n", OemRedFishLiveStatus));
                  DevceFound = TRUE;
                  RequestData.Health = SysInvWarning;
              }
              else {
                  /// Memory UCE Error: DIMMData->Id 
                  // Device Found: Redfish ID can be updated from here.
                  DEBUG((DEBUG_INFO, "Device Found: Memory UCE with ID: %a\n",DIMMData->Id));
                  AsciiSPrint(OemRedFishLiveStatus,MAX_REDFISH_CMD_SIZE,\
                          "%a",DIMMData->Id);
                  CmdSize = AsciiStrSize(OemRedFishLiveStatus);
                  DEBUG((DEBUG_INFO, "Memory Device (Critical)\n"));
                  DEBUG((DEBUG_INFO, "RedFish Cmd: %a\n", OemRedFishLiveStatus));
                  DevceFound = TRUE;
                  RequestData.Health = SysInvCritical;
              }
          }
          break; // SysInvDevDimm Case break;
        case (SysInvDevPci):
        if(DevId != (SysInvDevPci)) {
            continue;
        }

        PCIData = (SYS_INV_PCI_INFO*)&gSmmSystemInventoryInfoProtocol->DevInfoList[DevIndex]->DisplayPtr.Pci;
        PcieInfo = (GENERIC_PCIE_AER_ERR_ENTRY_V3 *)DataPtr;

        Status = gSmst->SmmAllocatePool(EfiRuntimeServicesCode,MAX_REDFISH_CMD_SIZE,&OemRedFishLiveStatus);
        ZeroMem (OemRedFishLiveStatus, MAX_REDFISH_CMD_SIZE);

        if((PCIData->BusNumber == PcieInfo->PcieAerErrorSection.DeviceId.PrimaryBus) && 
              (PCIData->DevFun.Dev == PcieInfo->PcieAerErrorSection.DeviceId.Device) && (PCIData->DevFun.Fun == PcieInfo->PcieAerErrorSection.DeviceId.Function)){
            /// Device Entry Found. Checking Severity Now

            RequestData.Resource = DevId;

            DEBUG((DEBUG_INFO,"ErrorSeverity: %d\n", PcieInfo->GenErrorDataEntry.ErrorSeverity));
            
            if (PcieInfo->GenErrorDataEntry.ErrorSeverity == EFI_ACPI_6_3_ERROR_SEVERITY_FATAL) {
                  /// PCIe UCE Error.
                  DEBUG((DEBUG_INFO, "Device Found: PCIe UCE with ID: %a\n",PCIData->PcieFunctions.Id));
                  AsciiSPrint(
                          OemRedFishLiveStatus,
                          MAX_REDFISH_CMD_SIZE,
                          "%a",PCIData->PcieFunctions.Id);
                  CmdSize = AsciiStrSize(OemRedFishLiveStatus);
                  DEBUG((DEBUG_INFO, "PCIe Device (Critical)\n"));
                  DEBUG((DEBUG_INFO, "RedFish Cmd: %a\n", OemRedFishLiveStatus));
                  DevceFound = TRUE;
                  RequestData.Health = SysInvCritical;
            } 
            else if ((PcieInfo->GenErrorDataEntry.ErrorSeverity == EFI_ACPI_6_3_ERROR_SEVERITY_CORRECTED) || 
                    (PcieInfo->GenErrorDataEntry.ErrorSeverity == EFI_ACPI_6_3_ERROR_SEVERITY_CORRECTABLE)) {
                  /// PCIe CE Error.
                  DEBUG((DEBUG_INFO, "Device Found: PCIe CE with ID: %a\n",PCIData->PcieFunctions.Id));
                  AsciiSPrint(
                          OemRedFishLiveStatus,
                          MAX_REDFISH_CMD_SIZE,
                          "%a",PCIData->PcieFunctions.Id);
                  CmdSize = AsciiStrSize(OemRedFishLiveStatus);
                  DEBUG((DEBUG_INFO, "PCIe Device (Warning)\n"));
                  DEBUG((DEBUG_INFO, "RedFishCmd: %a\n", OemRedFishLiveStatus));
                  DevceFound = TRUE;
                  RequestData.Health = SysInvWarning;
            }
        }
        break; // SysInvDevPci Case break;
        
        default:
            /// Just Continue for Other Devices.
            continue;
            break;
          }
        if(DevceFound) {
            break;
        }
    }

    if(DevceFound == FALSE){
        DEBUG((DEBUG_INFO, "Device is not Found Flag: %d\n", DevceFound));
        DEBUG((DEBUG_INFO,"%a() Exiting - Device Not Found\n",__FUNCTION__));
        if (OemRedFishLiveStatus){
                Status = gSmst->SmmFreePool(OemRedFishLiveStatus);
        }
        return;
    }
    
    DEBUG((DEBUG_INFO, "DeviceFound FLAG: %d\n", DevceFound));
    RequestData.DbNumber = REDIS_DB_LIVE_STATUS;

    CopyMem(RequestData.InstanceId, OemRedFishLiveStatus, CmdSize);

    CmdSize += sizeof(RequestData.DbNumber) + sizeof(RequestData.Resource) + sizeof(RequestData.Health);

    SendIpmiCommandHealthStatusUpdate(CmdSize, &RequestData);

    if (OemRedFishLiveStatus){
        Status = gSmst->SmmFreePool(OemRedFishLiveStatus);
    }
    

    DEBUG((DEBUG_INFO,"%a() Exiting - Status: %r\n",__FUNCTION__, Status));
    return;
}

EFI_STATUS
SmmSystemInventoryInfoProtocolCallback (
  IN CONST EFI_GUID  *Protocol,
  IN VOID            *Interface,
  IN EFI_HANDLE      Handle )
{
    EFI_STATUS      Status;
    
    Status = gSmst->SmmLocateProtocol (
                         &gAmiSmmSystemInventoryInfoProtocolGuid,
                         NULL,
                         &gSmmSystemInventoryInfoProtocol
                         );
    ASSERT_EFI_ERROR (Status);
    if (EFI_ERROR (Status)) {
         return Status;
    }
       
    Status = gSmst->SmmLocateProtocol (
                        &gSmmIpmiTransport2ProtocolGuid, 
                        NULL, 
                        &gIpmiTransport);
    DEBUG((DEBUG_INFO, "SmmSystemInventoryInfoProtocolCallback: SmmLocateProtocol for SmmIpmiTransport - %r \n", Status));
    
    ASSERT_EFI_ERROR (Status);
    if (EFI_ERROR (Status)) {
          return Status;
    }
    
    return EFI_SUCCESS;
}
#endif

/**
    This function collects the EFI_SM_ELOG_REDIR_PROTOCOL instances.
**/
VOID
ConnectRedirElogProtocol (
  VOID
)
{
    EFI_STATUS      Status;
    EFI_HANDLE      *HandleBuffer;
    UINTN           HandleBufferSize;
    UINTN           HandleCount;
    UINTN           HandleIndex;
    UINTN           EntryIndex;
    UINTN           DataType;
    
    HandleBuffer = NULL;
    HandleBufferSize = 0;

    // Get all RedirElogProtocol handles.
    Status = gSmst->SmmLocateHandle (
                        ByProtocol,
                        &gEfiRedirElogProtocolGuid,
                        NULL,
                        &HandleBufferSize,
                        HandleBuffer 
                        );
    
    if (Status == EFI_BUFFER_TOO_SMALL) {
        // Allocate memory for Handle buffer
        Status = gSmst->SmmAllocatePool (
                            EfiRuntimeServicesData,
                            HandleBufferSize,
                            (VOID**)&HandleBuffer
                            );
        if (Status == EFI_SUCCESS) {
            Status = gSmst->SmmLocateHandle (
                                ByProtocol,
                                &gEfiRedirElogProtocolGuid,
                                NULL,
                                &HandleBufferSize,
                                HandleBuffer 
                                );
            if (Status == EFI_SUCCESS) {
                HandleCount = (HandleBufferSize/sizeof(EFI_HANDLE));

                // Store collected info
                ZeroMem (&CommonElogPrivate, sizeof(CommonElogPrivate));
                EntryIndex = 0;
                    
                for (HandleIndex = 0; HandleIndex < HandleCount; HandleIndex++) {
                    Status = gSmst->SmmHandleProtocol (
                                        (EFI_SM_ELOG_PROTOCOL*)(HandleBuffer[HandleIndex]),
                                        &gEfiRedirElogProtocolGuid,
                                        &CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol
                                         );
                    if ((Status != EFI_SUCCESS) || (CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol == NULL)) continue;
                    for (DataType = 0; DataType < EfiSmElogMax; DataType++) {
                        Status = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol->ActivateEventLog (
                                            CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol,    // This
                                            DataType,                                                     // DataType
                                            NULL,                                                         // *EnableElog
                                            &CommonElogPrivate.RedirectEntry[EntryIndex].Enabled          // *ElogStatus
                                            );
                        if (Status == EFI_NOT_FOUND) continue;
                        CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType = DataType;
                        EntryIndex++;
                        break;
                    }
                }
            }
        }
        if (HandleBuffer != NULL) {
            gSmst->SmmFreePool (HandleBuffer);
        }
    }
}

/**
    This  callback function collects the EFI_SM_ELOG_REDIR_PROTOCOL instances,
    if not done during driver init.
    @param  EFI_GUID   *Protocol  - Pointer to the protocol GUID
    @param  VOID       *Interface - Pointer to EFI_SM_ELOG_REDIR_PROTOCOL
    @param  EFI_HANDLE  Handle   -  EFI_HANDLE    
    @retval EFI_STATUS Return the EFI  Status
**/

EFI_STATUS
EFIAPI
RedirElogProtocolCallBack (
    IN CONST EFI_GUID  *Protocol,
    IN VOID            *Interface,
    IN EFI_HANDLE      Handle )
{
    EFI_STATUS      Status;
    
    Status = gSmst->SmmLocateProtocol (
                        &gEfiRedirElogProtocolGuid,
                        gRegistration,
                        &EfiRedirElogProtocol 
                        );
    
    if (EFI_ERROR (Status)) {
        return Status;
    }
    ConnectRedirElogProtocol();

#if HardwareHealthManagement_SUPPORT      

    Status = gSmst->SmmRegisterProtocolNotify (
                       &gAmiSmmSystemInventoryInfoProtocolGuid,
                       SmmSystemInventoryInfoProtocolCallback,
                       &gSmmSystemInventoryInfoProtocolRegistration );
    ASSERT_EFI_ERROR (Status);
    if (EFI_ERROR (Status)) {
        return Status;
    }
#endif    

    return EFI_SUCCESS;
}    

/**
 Entry point of the AMI OEM RS RAS SMM driver to
 install Ras OEM protocol for SMM.

 @param  ImageHandle - EFI Image Handle for the SMM driver
 @param  SystemTable - pointer to the EFI system table

 @retval   EFI_SUCCESS : Module initialized successfully
 @retval   EFI_ERROR   : Initialization failed (see error for more details)
**/
EFI_STATUS
AmiOemRasSmmInit (
  IN       EFI_HANDLE         ImageHandle,
  IN       EFI_SYSTEM_TABLE   *SystemTable
  )
{
    EFI_STATUS        Status = EFI_SUCCESS;
    EFI_HANDLE        Handle;

    DEBUG((EFI_D_ERROR, "[AmiRAS] RS OEM CPM RAS SMM driver entry\n"));

    Handle = NULL;
    Status = gSmst->SmmInstallProtocolInterface (
                      &Handle,
                      &gAmdCpmRasOemSmmProtocolGuid,
                      EFI_NATIVE_INTERFACE,
                      &AmdCpmRasOemSmmProtocol
                      );
    ASSERT_EFI_ERROR (Status);
    DEBUG((EFI_D_ERROR, "[AmiRAS] RS OEM CPM RAS SMM driver install AmdCpmRasOemSmmProtocol %r\n", Status));

//COMPAL_CHANGE >>>
    Status = gBS->LocateProtocol (
                &gAmdPlatformApeiDataProtocolGuid,
                NULL,
                (VOID **)&mPlatformApeiData
                );
    DEBUG((EFI_D_ERROR, "[AmiRAS] RS OEM CPM RAS SMM locate mPlatformApeiData %r\n", Status));

    // Locate SKU_ID Protocol
    Status = gBS->LocateProtocol (
                &gOemboardPkgSkuIdGuid,
                NULL,
                (VOID **)&mSkuIdProtocol
                );
    if (EFI_ERROR (Status)) {
        DEBUG((EFI_D_ERROR, "[AmiRAS] RS OEM CPM RAS SMM LocateProtocol for mSkuIdProtocol %r\n", Status));
        mSkuIdProtocol = NULL;
        mSkuIdData = NULL;
    } else {
        mSkuIdData = &(mSkuIdProtocol->SkuId);
        DEBUG((EFI_D_ERROR, "[AmiRAS] RS OEM CPM RAS SMM LocateProtocol for mSkuIdProtocol %r, SKU_ID = 0x%02X\n", Status, *mSkuIdData));
    }
//COMPAL_CHANGE <<<
    
    Status = gSmst->SmmLocateProtocol (
                        &gEfiRedirElogProtocolGuid,
                        gRegistration,
                        &EfiRedirElogProtocol 
                        );
    
    if (EFI_ERROR (Status)) {
        // Protocol not found, postpone connection to install time
        Status = gSmst->SmmRegisterProtocolNotify (
                         &gEfiRedirElogProtocolGuid,
                         RedirElogProtocolCallBack,
                         &gRegistration
                         );
    } else {
        ConnectRedirElogProtocol();
    }

#if HardwareHealthManagement_SUPPORT      
    Status = gSmst->SmmLocateProtocol (
                  &gSmmIpmiTransport2ProtocolGuid, 
                  NULL, 
                  &gIpmiTransport);
    DEBUG((DEBUG_INFO, "AmiOemRasSmmInit: SmmLocateProtocol for gIpmiTransport - %r \n", Status));

    ASSERT_EFI_ERROR (Status);
    if (EFI_ERROR (Status)) {
      return Status;
    }
    
    Status = gSmst->SmmRegisterProtocolNotify (
                       &gAmiSmmSystemInventoryInfoProtocolGuid,
                       SmmSystemInventoryInfoProtocolCallback,
                       &gSmmSystemInventoryInfoProtocolRegistration );
#endif    
              
    return Status;
}

EFI_STATUS
OemErrorLogEventMemTest (
  IN  UINT16                   Node,
  IN  UINT16                   Card,
  IN  UINT16                   Module,
  IN  GENERIC_MEM_ERR_ENTRY_V3 *MemTestErrEntry
)
{
    DEBUG((DEBUG_INFO, "[AmiRAS] SMM OemErrorLogEventMemTest entry\n"));
    // MemTest log would not be called in SMM
    return  EFI_SUCCESS;
}

EFI_STATUS
EFIAPI
OemErrorLogEventPmic (
  IN  UINT8                    Socket,
  IN  UINT8                    Channel,
  IN  UINT8                    Dimm,
  IN  GENERIC_PMIC_ERR_ENTRY_V3 *PlatformPmicErrEntry
)
{
    DEBUG((DEBUG_INFO, "[AmiRAS] OemErrorLogEventPmic entry \n"));
    return  EFI_SUCCESS;
}
EFI_STATUS
OemErrorLogEventCxlProtocol (
  IN  VOID        *CxlErrorLogData,
  IN  VOID        *GenCxlErrEntry
)
{
    EFI_STATUS                  Status;
    EFI_SM_ELOG_REDIR_PROTOCOL  *CurrRedirProtocol;
    UINT64                      RecordId;
    UINTN                       EventDataSize;
    UINTN                       EntryIndex;
    
    DEBUG((DEBUG_INFO, "[AmiRAS] SMM OemErrorLogEventCxlProtocol entry\n"));
    
    Status = CxlProtocolToIpmiBrh (CxlErrorLogData, (UINT8*)&ErrorLogData[0], GenCxlErrEntry);
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmIPMI) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                if (CurrRedirProtocol != NULL) {
                    EventDataSize = 0;
                    CurrRedirProtocol->SetEventLogData(
                                                    CurrRedirProtocol,   // This
                                                    &ErrorLogData[0],    // *ElogData
                                                    EfiElogSmIPMI,       // DataType
                                                    FALSE,               // AlertEvent
                                                    EventDataSize,       // DataSize
                                                    &RecordId            // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "SendElogEventCxlProtocol: SetEventLogData for Ipmi-%r\n", Status));
                }
                break;
            }
        }
    }    
#if ( defined(SMBIOS_RAS_ELOG) && (SMBIOS_RAS_ELOG != 0))
    Status = CxlProtocolToSmbiosBrh (CxlErrorLogData, (UINT8*)&ErrorLogData[0], GenCxlErrEntry);
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmSMBIOS) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                EventDataSize = 4;
                if (CurrRedirProtocol != NULL) {
                    Status = CurrRedirProtocol->SetEventLogData (
                                                    CurrRedirProtocol, // This
                                                    &ErrorLogData[0],  // *ElogData
                                                    EfiElogSmSMBIOS,   // DataType
                                                    FALSE,             // AlertEvent
                                                    EventDataSize,     // DataSize
                                                    &RecordId          // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "OemErrorLogEventCxlProtocol: SetEventLogData for Smbios-%r\n", Status));
                    break;
                }
            }
        }
    }
#endif
    return  EFI_SUCCESS;
}

EFI_STATUS
OemErrorLogEventCxlComponent (
  IN  VOID        *CxlErrorLogData,
  IN  VOID        *GenCxlErrEntry
)
{
    EFI_STATUS                  Status;
    EFI_SM_ELOG_REDIR_PROTOCOL  *CurrRedirProtocol;
    UINT64                      RecordId;
    UINTN                       EventDataSize;
    UINTN                       EntryIndex;
    
    DEBUG((DEBUG_INFO, "[AmiRAS] SMM OemErrorLogEventCxlComponent entry\n"));
    
    Status = CxlComponentToIpmiBrh (CxlErrorLogData, (UINT8*)&ErrorLogData[0], GenCxlErrEntry);
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmIPMI) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                if (CurrRedirProtocol != NULL) {
                    EventDataSize = 0;
                    CurrRedirProtocol->SetEventLogData(
                                                    CurrRedirProtocol,   // This
                                                    &ErrorLogData[0],    // *ElogData
                                                    EfiElogSmIPMI,       // DataType
                                                    FALSE,               // AlertEvent
                                                    EventDataSize,       // DataSize
                                                    &RecordId            // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "SendElogEventCxlComponent: SetEventLogData for Ipmi-%r\n", Status));
                }
                break;
            }
        }
    }    
#if ( defined(SMBIOS_RAS_ELOG) && (SMBIOS_RAS_ELOG != 0))
    Status = CxlComponentToSmbiosBrh (CxlErrorLogData, (UINT8*)&ErrorLogData[0], GenCxlErrEntry);
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmSMBIOS) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                EventDataSize = 4;
                if (CurrRedirProtocol != NULL) {
                    Status = CurrRedirProtocol->SetEventLogData (
                                                    CurrRedirProtocol, // This
                                                    &ErrorLogData[0],  // *ElogData
                                                    EfiElogSmSMBIOS,   // DataType
                                                    FALSE,             // AlertEvent
                                                    EventDataSize,     // DataSize
                                                    &RecordId          // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "OemErrorLogEventCxlComponent: SetEventLogData for Smbios-%r\n", Status));
                    break;
                }
            }
        }
    }
#endif
    return  EFI_SUCCESS;
}

EFI_STATUS
OemErrorLogEventMem (
  IN VOID         *RasMcaErrorInfo,
  IN UINT8        BankIndex,
  IN UINTN        ProcessorNumber,
  IN VOID         *GenericMemErrEntryBuffer
)
{
/*
    EFI_STATUS                  Status;
    EFI_SM_ELOG_REDIR_PROTOCOL  *CurrRedirProtocol;
    UINT64                      RecordId;
    UINTN                       EventDataSize;
    UINTN                       EntryIndex;
    
    DEBUG((DEBUG_INFO, "[AmiRAS] SMM OemErrorLogEventMem entry\n"));
    
    Status = MemToIpmiBrh (RasMcaErrorInfo, (UINT8*)&ErrorLogData[0], BankIndex, NULL, NULL, GenericMemErrEntryBuffer);
    DEBUG((DEBUG_INFO, "[AmiRAS] SMM MemToIpmiBrh Status=%r\n", Status));
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmIPMI) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                if (CurrRedirProtocol != NULL) {
                    EventDataSize = 0;
                    CurrRedirProtocol->SetEventLogData(
                                                    CurrRedirProtocol,   // This
                                                    &ErrorLogData[0],    // *ElogData
                                                    EfiElogSmIPMI,       // DataType
                                                    FALSE,               // AlertEvent
                                                    EventDataSize,       // DataSize
                                                    &RecordId            // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "SendElogEventMem: SetEventLogData for Ipmi-%r\n", Status));
                }
                break;
            }
        }
    }    
#if ( defined(SMBIOS_RAS_ELOG) && (SMBIOS_RAS_ELOG != 0))
    Status = MemToSmbiosBrh (RasMcaErrorInfo, (UINT8*)&ErrorLogData[0], BankIndex, ProcessorNumber, GenericMemErrEntryBuffer);
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmSMBIOS) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                EventDataSize = 4;
                if (CurrRedirProtocol != NULL) {
                    Status = CurrRedirProtocol->SetEventLogData (
                                                    CurrRedirProtocol, // This
                                                    &ErrorLogData[0],  // *ElogData
                                                    EfiElogSmSMBIOS,   // DataType
                                                    FALSE,             // AlertEvent
                                                    EventDataSize,     // DataSize
                                                    &RecordId          // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "OemErrorLogEventMem: SetEventLogData for Smbios-%r\n", Status));
                    break;
                }
            }
        }
    }
#endif
*/
    return  EFI_SUCCESS;
}

EFI_STATUS
OemErrorLogEventNbio (
  IN VOID       *RasNbioErrorInfo,
  IN VOID       *GenericNbioErrEntry
)
{
    EFI_STATUS                  Status;
    EFI_SM_ELOG_REDIR_PROTOCOL  *CurrRedirProtocol;
    UINT64                      RecordId;
    UINTN                       EventDataSize;
    UINTN                       EntryIndex; 
    
    DEBUG((DEBUG_INFO, "[RAS] SMM OemErrorLogEventNbio entry\n"));
    
    Status = NbioToIpmiBrh (RasNbioErrorInfo, (UINT8*)&ErrorLogData[0]);
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmIPMI) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                if (CurrRedirProtocol != NULL) {
                    EventDataSize = 0;
                    CurrRedirProtocol->SetEventLogData(
                                                    CurrRedirProtocol,   // This
                                                    &ErrorLogData[0],    // *ElogData
                                                    EfiElogSmIPMI,       // DataType
                                                    FALSE,               // AlertEvent
                                                    EventDataSize,       // DataSize
                                                    &RecordId            // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "SendElogEventNbio: SetEventLogData for Ipmi-%r\n", Status));
                }
                break;
            }
        }
    }    
#if ( defined(SMBIOS_RAS_ELOG) && (SMBIOS_RAS_ELOG != 0))
    Status = NbioToSmbiosBrh (RasNbioErrorInfo, (UINT8*)&ErrorLogData[0]);
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmSMBIOS) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                EventDataSize = 4;
                if (CurrRedirProtocol != NULL) {
                    Status = CurrRedirProtocol->SetEventLogData (
                                                    CurrRedirProtocol, // This
                                                    &ErrorLogData[0],  // *ElogData
                                                    EfiElogSmSMBIOS,   // DataType
                                                    FALSE,             // AlertEvent
                                                    EventDataSize,     // DataSize
                                                    &RecordId          // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "OemErrorLogEventNbio: SetEventLogData for Smbios-%r\n", Status));
                    break;
                }
            }
        }
    }
#endif
    return  EFI_SUCCESS;
}

EFI_STATUS
OemErrorLogEventPcie (
  IN VOID       *GenPcieAerErrEntry
)
{
    EFI_STATUS                  Status;
    EFI_SM_ELOG_REDIR_PROTOCOL  *CurrRedirProtocol;
    UINT64                      RecordId;
    UINTN                       EventDataSize;
    UINTN                       EntryIndex;
    
    DEBUG((DEBUG_INFO, "[AmiRAS] SMM OemErrorLogEventPcie entry\n"));
    
    Status = PcieToIpmiBrh (GenPcieAerErrEntry, (UINT8*)&ErrorLogData[0]);
    DEBUG((DEBUG_INFO, "[AmiRAS] SMM PcieToIpmiBrh Status=%r\n", Status));
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmIPMI) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                if (CurrRedirProtocol != NULL) {
                    EventDataSize = 0;
                    CurrRedirProtocol->SetEventLogData(
                                                    CurrRedirProtocol,   // This
                                                    &ErrorLogData[0],    // *ElogData
                                                    EfiElogSmIPMI,       // DataType
                                                    FALSE,               // AlertEvent
                                                    EventDataSize,       // DataSize
                                                    &RecordId            // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "SendElogEventPCIe: SetEventLogData for Ipmi-%r\n", Status));
                }
                break;
            }
        }
    }    
#if ( defined(SMBIOS_RAS_ELOG) && (SMBIOS_RAS_ELOG != 0))
    Status = PcieToSmbiosBrh (GenPcieAerErrEntry, (UINT8*)&ErrorLogData[0]);
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmSMBIOS) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                EventDataSize = 4;
                if (CurrRedirProtocol != NULL) {
                    Status = CurrRedirProtocol->SetEventLogData (
                                                    CurrRedirProtocol, // This
                                                    &ErrorLogData[0],  // *ElogData
                                                    EfiElogSmSMBIOS,   // DataType
                                                    FALSE,             // AlertEvent
                                                    EventDataSize,     // DataSize
                                                    &RecordId          // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "OemErrorLogEventPcie: SetEventLogData for Smbios-%r\n", Status));
                    break;
                }
            }
        }
    }
#endif

#if HardwareHealthManagement_SUPPORT      
    FindRedFishID(SysInvDevPci, (VOID *)GenPcieAerErrEntry, NULL);
#endif    

    return  EFI_SUCCESS;
}

EFI_STATUS
OemErrorLogEventSMN (
  UINT8                     NbioBusNum,
  UINT32                    PmBreakEvent,
  GENERIC_SMN_ERR_ENTRY_V3  *GenericSmnErrEntry
)
{
    EFI_STATUS                  Status;
    EFI_SM_ELOG_REDIR_PROTOCOL  *CurrRedirProtocol;
    UINT64                      RecordId;
    UINTN                       EventDataSize;
    UINTN                       EntryIndex;
    
    DEBUG((DEBUG_INFO, "[AmiRAS] SMM OemErrorLogEventSMN entry\n"));
    
    Status = SMNToIpmiBrh (GenericSmnErrEntry, (UINT8*)&ErrorLogData[0]);
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmIPMI) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                if (CurrRedirProtocol != NULL) {
                    EventDataSize = 0;
                    CurrRedirProtocol->SetEventLogData(
                                                    CurrRedirProtocol,   // This
                                                    &ErrorLogData[0],    // *ElogData
                                                    EfiElogSmIPMI,       // DataType
                                                    FALSE,               // AlertEvent
                                                    EventDataSize,       // DataSize
                                                    &RecordId            // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "SendElogEventSMN: SetEventLogData for Ipmi-%r\n", Status));
                }
                break;
            }
        }
    }    
#if ( defined(SMBIOS_RAS_ELOG) && (SMBIOS_RAS_ELOG != 0))
    Status = SMNToSmbiosBrh (GenericSmnErrEntry, (UINT8*)&ErrorLogData[0]);
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmSMBIOS) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                EventDataSize = 4;
                if (CurrRedirProtocol != NULL) {
                    Status = CurrRedirProtocol->SetEventLogData (
                                                    CurrRedirProtocol, // This
                                                    &ErrorLogData[0],  // *ElogData
                                                    EfiElogSmSMBIOS,   // DataType
                                                    FALSE,             // AlertEvent
                                                    EventDataSize,     // DataSize
                                                    &RecordId          // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "OemErrorLogEventSMN: SetEventLogData for Smbios-%r\n", Status));
                    break;
                }
            }
        }
    }
#endif
    return  EFI_SUCCESS;
}

EFI_STATUS
OemErrorLogEventMca (
  IN  VOID               *ErrorRecord,
  IN  DIMM_INFO          *DimmInfo,
  IN  NORMALIZED_ADDRESS *Address,
  IN  UINT8               BankIndex
)
{
    EFI_STATUS                  Status;
    EFI_SM_ELOG_REDIR_PROTOCOL  *CurrRedirProtocol;
    UINT64                      RecordId;
    UINTN                       EventDataSize;
    UINTN                       EntryIndex;
    
    DEBUG((DEBUG_INFO, "[AmiRAS] SMM OemErrorLogEventMca entry\n"));
    
    Status = McaToIpmiBrh (ErrorRecord, (UINT8*)&ErrorLogData[0], DimmInfo,Address,BankIndex);
    DEBUG((DEBUG_INFO, "[AmiRAS] SMM McaToIpmiBrh Status=%r\n", Status));
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmIPMI) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                if (CurrRedirProtocol != NULL) {
                    EventDataSize = 0;
                    CurrRedirProtocol->SetEventLogData(
                                                    CurrRedirProtocol,   // This
                                                    &ErrorLogData[0],    // *ElogData
                                                    EfiElogSmIPMI,       // DataType
                                                    FALSE,               // AlertEvent
                                                    EventDataSize,       // DataSize
                                                    &RecordId            // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "SendElogEventMca: SetEventLogData for Ipmi-%r\n", Status));
                }
                break;
            }
        }
    }
#if ( defined(SMBIOS_RAS_ELOG) && (SMBIOS_RAS_ELOG != 0))
    Status = McaToSmbiosBrh (ErrorRecord, (UINT8*)&ErrorLogData[0], BankIndex);
    DEBUG((DEBUG_INFO, "[AmiRAS] SMM McaToSmbiosBrh Status=%r\n", Status));
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmSMBIOS) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                EventDataSize = 4;
                if (CurrRedirProtocol != NULL) {
                    Status = CurrRedirProtocol->SetEventLogData (
                                                    CurrRedirProtocol, // This
                                                    &ErrorLogData[0],  // *ElogData
                                                    EfiElogSmSMBIOS,   // DataType
                                                    FALSE,             // AlertEvent
                                                    EventDataSize,     // DataSize
                                                    &RecordId          // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "OemErrorLogEventMca: SetEventLogData for Smbios-%r\n", Status));
                    break;
                }
            }
        }
    }
#endif    

#if HardwareHealthManagement_SUPPORT      
    FindRedFishID(SysInvDevDimm, (VOID *)ErrorRecord, Address);
#endif    

    return  EFI_SUCCESS;
}

EFI_STATUS
OemErrorLogEventProcessor (
  IN  VOID               *ErrorRecord,
  IN  UINT8              BankIndex,
  IN  UINTN              ProcessorNumber,
  IN  VOID               *GenericProcErrBuffer
)
{
    EFI_STATUS                  Status;
    EFI_SM_ELOG_REDIR_PROTOCOL  *CurrRedirProtocol;
    UINT64                      RecordId;
    UINTN                       EventDataSize;
    UINTN                       EntryIndex;
    
    DEBUG((DEBUG_INFO, "[AmiRAS] SMM OemErrorLogEventProcessor entry\n"));
    
    Status = ProcessorToIpmiBrh (ErrorRecord, (UINT8*)&ErrorLogData[0], BankIndex, ProcessorNumber,GenericProcErrBuffer);
    DEBUG((DEBUG_INFO, "[AmiRAS] SMM ProcessorToIpmi status=%r\n", Status));
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmIPMI) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                if (CurrRedirProtocol != NULL) {
                    EventDataSize = 0;
                    Status = CurrRedirProtocol->SetEventLogData (
                                                CurrRedirProtocol,   // This
                                                &ErrorLogData[0],    // *ElogData
                                                EfiElogSmIPMI,       // DataType
                                                FALSE,               // AlertEvent
                                                EventDataSize,       // DataSize
                                                &RecordId            // *RecordId
                                                );
                    DEBUG((EFI_D_ERROR, "OemErrorLogEventProcessor: SetEventLogData for Ipmi-%r\n", Status));
                    break;
                }
            }
        }
    }
#if ( defined(SMBIOS_RAS_ELOG) && (SMBIOS_RAS_ELOG != 0))
    Status = ProcessorToSmbiosBrh (ErrorRecord, (UINT8*)&ErrorLogData[0], BankIndex, ProcessorNumber,GenericProcErrBuffer);
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmSMBIOS) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                EventDataSize = 4;
                if (CurrRedirProtocol != NULL) {
                    Status = CurrRedirProtocol->SetEventLogData (
                                                    CurrRedirProtocol, // This
                                                    &ErrorLogData[0],  // *ElogData
                                                    EfiElogSmSMBIOS,   // DataType
                                                    FALSE,             // AlertEvent
                                                    EventDataSize,     // DataSize
                                                    &RecordId          // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "OemErrorLogEventProcessor: SetEventLogData for Smbios-%r\n", Status));
                    break;
                }
            }
        }
    }
#endif
    
    return  EFI_SUCCESS;
}

EFI_STATUS
EFIAPI
OemErrorLogEventRtPpr (
  IN  UINT32             RuntimePprStatus,
  IN  VOID               *ChkRtPprComplStructPtr,
  IN  BOOLEAN            *RepairResult,
  IN  UINT32             NumOfRtPptEntryServed
)
{
  CHK_RTPPR_COMPL_STRUCT  *ChkRtPprComplStruct;
  UINT8                   Index;

  DEBUG((DEBUG_INFO, "[AmiRAS] SMM OemErrorLogEventRtPpr entry\n"));
  ChkRtPprComplStruct = (CHK_RTPPR_COMPL_STRUCT *)ChkRtPprComplStructPtr;

  for (Index = 0; Index < NumOfRtPptEntryServed; Index++) {

    DEBUG((DEBUG_INFO, "[AmiRAS] SMM OemErrorLogEventRtPpr entry\n"));

    DEBUG((DEBUG_INFO, "[AmiRAS] RuntimePprStatus returned from PSP: 0x%08x\n", RuntimePprStatus));

    DEBUG((DEBUG_INFO, "[%a] [Entry: 0x%02X] ProcessorNumber: 0x%x, BankNumber: 0x%x\n",
      __FUNCTION__,  Index, ChkRtPprComplStruct[Index].CpuInfo.ProcessorNumber, ChkRtPprComplStruct[Index].BankNumber));

    DEBUG((DEBUG_INFO, "[%a] [Entry: 0x%02X] System Address: 0x%016lX. (Normalized Address: 0x%016lX)\n",
      __FUNCTION__, Index, ChkRtPprComplStruct[Index].SystemMemoryAddress, ChkRtPprComplStruct[Index].NormalizedAddrInfo.normalizedAddr));

    DEBUG((DEBUG_INFO, "[%a] Repair %a.\n", __FUNCTION__, (RepairResult[Index])? "Passed" : "Failed"));
  }
  return  EFI_SUCCESS;
}
