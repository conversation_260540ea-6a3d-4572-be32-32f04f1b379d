#***********************************************************************
#*                                                                     *
#*   Copyright (c) 1985-2025, American Megatrends International LLC.   *
#*                                                                     *
#*      All rights reserved. Subject to AMI licensing agreement.       *
#*                                                                     *
#***********************************************************************

## @file
#   This AMI PCI Express Lib
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = AmiNbPciAccessCspBaseLib
  FILE_GUID                      = 6E53E392-40FC-4539-9040-06469C50AFBA
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = PciAccessCspLib
  
#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
 NbPciCsp.c
 ProgramMmioRange.c
  
[Packages]
  MdePkg/MdePkg.dec
  AmiModulePkg/AmiModulePkg.dec
  AmiCompatibilityPkg/AmiCompatibilityPkg.dec
  AmiChipsetModulePkg/AmiChipsetModulePkg.dec #for nb.h
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec 
  AgesaModulePkg/AgesaModuleNbioPkg.dec

[Protocols]
  gAmiBoardInfo2ProtocolGuid                    # CONSUMED
  gAmdSocLogicalIdProtocolGuid                  # CONSUMED
  gAmdApcbDxeServiceProtocolGuid                # CONSUMED
  gAmdFabricTopologyServices2ProtocolGuid       # CONSUMED
  gAmdFabricResourceManagerServicesProtocolGuid # CONSUMED
  gAmdSocLogicalIdProtocolGuid                  # CONSUMED
  gEfiAcpiTableProtocolGuid                     # CONSUMED
  gAmdNbioCxlServicesProtocolGuid               # CONSUMED
  gAmdNbioServicesProtocolGuid                  # CONSUMED

[Guids]
  gDisableResourcesVgaListGuid
  gLastBootFailedGuid

[LibraryClasses]
  BaseLib
  UefiRuntimeServicesTableLib
  DxeServicesTableLib
  UefiBootServicesTableLib
  DebugLib
  UefiLib
  HobLib
  PciLib
  SmnAccessLib
  BaseFabricTopologyLib

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdPciExpressBaseAddress
  gAmiModulePkgTokenSpaceGuid.AmiPcdPciOutOfResourcesStatus
  gAmiChipsetModulePkgTokenSpaceGuid.PcdLegacyVgaBDF
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIommuMMIOAddressReservedEnable
  
