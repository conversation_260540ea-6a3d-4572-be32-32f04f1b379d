#***********************************************************************
#*                                                                     *
#*   Copyright (c) 1985-2025, American Megatrends International LLC.   *
#*                                                                     *
#*      All rights reserved. Subject to AMI licensing agreement.       *
#*                                                                     *
#***********************************************************************

## @file
#  Produces the EDK II Crypto Protocol using the library services from
#  BaseCryptLib and TlsLib.  PcdCryptoServiceFamilyEnable is used to enable the
#  subset of available services.
#
#  Copyright (C) Microsoft Corporation. All rights reserved.
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x0001001B
  PI_SPECIFICATION_VERSION       = 0x0001000A
  BASE_NAME                      = CryptoDxe
  MODULE_UNI_FILE                = Crypto.uni
  FILE_GUID                      = FEA01457-E381-4135-9475-C6AFD0076C61
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = CryptoDxeEntry

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 ARM AARCH64
#

[Sources]
  Crypto.c
  CryptoDxe.c

[Packages]
  MdePkg/MdePkg.dec
  CryptoPkg/CryptoPkg.dec

[LibraryClasses]
  UefiDriverEntryPoint
  UefiBootServicesTableLib
  DebugLib
  BaseCryptLib
  TlsLib

[BuildOptions]
  #MSFT:*_*_IA32_DLINK_FLAGS = /ALIGN:4096  // APTIOV OVERRIDE - 4K Alignment for DXE_DRIVER
  #MSFT:*_*_X64_DLINK_FLAGS  = /ALIGN:4096  // has already defined in build_rule.txt file.
  #GCC:*_*_AARCH64_DLINK_XIPFLAGS = -z common-page-size=0x1000  // TODO

[Protocols]
  gEdkiiCryptoProtocolGuid  ## PRODUCES

[Pcd]
  gEfiCryptoPkgTokenSpaceGuid.PcdCryptoServiceFamilyEnable  #CONSUMES

[Depex]
  TRUE
