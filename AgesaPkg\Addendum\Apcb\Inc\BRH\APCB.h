/*****************************************************************************
*
 * Copyright (C) 2008-2025 Advanced Micro Devices, Inc. All rights reserved.
 *
*******************************************************************************
*
*/

#ifndef _APCB_H_
#define _APCB_H_

//
// @APCB_START
//
#define APCB_HEADER_VERSION         0x0300    // APCB Version
#define APCB_HEADER_STRUCT_VERSION  0x0000
#define APCB_HEADER_DATA_VERSION    0x0000
#define ABL_RESERVED                0x0

#include "ApcbV3Arch.h"
#include "ApcbV3Priority.h"
#include "ApcbDataGroups.h"
#include "ApcbAblBreakpoint.h"

/*----------------------------------------------------------------------------
 *   Mixed (DEFINITIONS AND MACROS / TYPEDEFS, STRUCTURES, ENUMS)
 *
 *----------------------------------------------------------------------------
 */

/*-----------------------------------------------------------------------------
 *                         DEFINITIONS AND MACROS
 *
 *-----------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------
 *                         TYPEDEFS, STRUCTURES, ENUMS
 *
 *----------------------------------------------------------------------------
 */
/// ABL Serial Baud Rate
typedef enum {
  BAUD_RATE_2400 = 0,     ///< Baud rate 2400
  BAUD_RATE_3600 = 1,     ///< Baud rate 3600
  BAUD_RATE_4800 = 2,     ///< Baud rate 4800
  BAUD_RATE_7200 = 3,     ///< Baud rate 7200
  BAUD_RATE_9600 = 4,     ///< Baud rate 9600
  BAUD_RATE_19200 = 5,    ///< Baud rate 19200
  BAUD_RATE_38400 = 6,    ///< Baud rate 38400
  BAUD_RATE_57600 = 7,    ///< Baud rate 57600
  BAUD_RATE_115200 = 8    ///< Baud rate 115200
} ABL_SERIAL_BAUD_RATE;

/// PMU message control (HdtCtrl)
typedef enum {
  DETAILED_DBG_MSG = 0x05,     ///< Detailded debug message
  COARSE_DBG_MSG = 0x0A,       ///< Coarse debug message
  STAGE_COMPLETION = 0xC8      ///< Stage completion
} ABL_PMU_MESSAGE_CONTROL;

typedef struct _DIMM_HUB_INFO {
  UINT8  SocketId;            ///< Indicates the socket number
  UINT8  ChannelId;           ///< Indicates the channel number
  UINT8  DimmId;              ///< Indicates the channel number
  UINT8  DimmSmbusAdderess;   ///< SMBus address of this DRAM module
  UINT8  FchI2cI3cCtrlIndex;  ///< FCH I2C / I3C controller route to this DIMM slot
} DIMM_HUB_INFO;

//
// DIMM Configuration Lookup Criteria
//
#define PT_DONT_CARE    0xFF  ///< Do not care
#define NP              1     ///< Not Populated
#define V1_5            1     ///< 1.5 Volts
#define V1_35           2     ///< 1.35 Volts
#define V1_25           4     ///< 1.25 Volts
#define V1_2            1     ///< 1.2 Volts
#define V_TBD1          2     ///< Voltage to be determined 1
#define V_TBD2          4     ///< Voltage to be determined 2
#define V1_1            1     ///< 1.1 Volts
#define V0_6            2     ///< 0.6 Volts
//
// Rank
//
#define DIMM_NP         1     ///< DIMM Not Present
#define DIMM_SR         2     ///< Single Rank DIMM Present
#define DIMM_DR         4     ///< Dual Ranke DIMM Present
#define DIMM_QR         8     ///< Quad Rank DIMM Present
#define DIMM_ALL        0xFF  ///< Quad Rank DIMM Present
//
// Device Width
//
#define DEVWIDTH_4      0x01
#define DEVWIDTH_8      0x02
//#define DEVWIDTH_16     0x04
//#define DEVWIDTH_32     0x08
#define DEVWIDTH_ALL    0xFF

#define RCDMFGID_ALL    0xFFFF

#define RCDGEN_1        0     ///< SPD byte 242[3:0]
#define RCDGEN_2        1
#define RCDGEN_3        2
#define RCDGEN_ALL      0xFF

#define RAWCARDREV_ALL  0xFFFF

#define DIE_ALL         0xFFFF

#define DENSITY_8GBIT   0x01
#define DENSITY_12GBIT  0x02
#define DENSITY_16GBIT  0x04
#define DENSITY_24GBIT  0x08
#define DENSITY_32GBIT  0x10
#define DENSITY_48GBIT  0x20
#define DENSITY_64GBIT  0x40
#define DENSITY_ALL     0xFF

//
// MPstate
//
#define MP0             0x01
#define MP1             0x02
#define MP2             0x04
#define MP3             0x08
#define MP_ALL          0xFF
/// Timing
///
#define TMG_1N           1     ///< 1N Timing Mode
#define TMG_2N           0     ///< 2N Timing Mode
///
/// Defined values for DDR5 CK_ODT, CS_ODT, and CK_ODT
///
#define D5_ODT_OFF        0     ///< ODT Off
#define D5_ODT_480        1     ///< 480 Ohm
#define D5_ODT_240        2     ///< 240 Ohm
#define D5_ODT_120        3     ///< 120 Ohm
#define D5_ODT_80         4     ///< 80 Ohm
#define D5_ODT_60         5     ///< 60 Ohm
#define D5_ODT_40         7     ///< 40 Ohm
///
/// Defined values for RTT_NOM_WR,RTT_NOM_RD
///
#define D5_RTT_OFF        0     ///< RTT Off
#define D5_RTT_240        1     ///< 240 Ohm
#define D5_RTT_120        2     ///< 120 Ohm
#define D5_RTT_80         3     ///< 80 Ohm
#define D5_RTT_60         4     ///< 60 Ohm
#define D5_RTT_48         5     ///< 48 Ohm
#define D5_RTT_40         6     ///< 40 Ohm
#define D5_RTT_34         7     ///< 34 Ohm
///
/// Generic, un-encoded impedance values for various purposes. They can be
/// translated to the appropriate value for how it is consumed.
///
#define D5_IMP_OFF        0      ///< Impedance Off
#define D5_IMP_480        480    ///< 480 Ohms
#define D5_IMP_240        240    ///< 240 Ohms
#define D5_IMP_160        160    ///< 160 Ohms
#define D5_IMP_120        120    ///< 120 Ohms
#define D5_IMP_96         96     ///< 96 Ohms
#define D5_IMP_80         80     ///< 80 Ohms
#define D5_IMP_68         68     ///< 68 Ohms
#define D5_IMP_60         60     ///< 60 Ohms
#define D5_IMP_53         53     ///< 53 Ohms
#define D5_IMP_48         48     ///< 48 Ohms
#define D5_IMP_43         43     ///< 43 Ohms
#define D5_IMP_40         40     ///< 40 Ohms
#define D5_IMP_36         36     ///< 36 Ohms
#define D5_IMP_34         34     ///< 34 Ohms
#define D5_IMP_32         32     ///< 32 Ohms
#define D5_IMP_30         30     ///< 30 Ohms
#define D5_IMP_28         28     ///< 28 Ohms
#define D5_IMP_27         27     ///< 27 Ohms
#define D5_IMP_26         26     ///< 26 Ohms
#define D5_IMP_25         25     ///< 25 Ohms
#define D5_IMP_20         20     ///< 20 Ohms, RW0A/0C - QCK/QCA/QCS Signals Driver Characteristics
#define D5_IMP_14         14     ///< 14 Ohms, RW0A/0C - QCK/QCA/QCS Signals Driver Characteristics
#define D5_IMP_10         10     ///< 10 Ohms, RW0A/0C - QCK/QCA/QCS Signals Driver Characteristics
#define D5_IMP_NA         255    ///< RCW0A/0C use ABL default setting
#define D5_IMP_LIGHT      0      ///< RCW A/C
#define D5_IMP_MODERATE   1      ///< RCW A/C
#define D5_IMP_STRONG     2      ///< RCW A/C

#define D5_SLEW_MODERATE  0      ///< RW0E QCK, QCA and QCS Output Slew Rate
#define D5_SLEW_FAST      1      ///< RW0E QCK, QCA and QCS Output Slew Rate
#define D5_SLEW_SLOW      2      ///< RW0E QCK, QCA and QCS Output Slew Rate
#define D5_SLEW_NA        255    ///< Not available
#define D5_VREF_NA        255    ///< Not available

// MR11/12
#define D5_CACS_VREF_35P0  0x7D
#define D5_CACS_VREF_35P5  0x7C
#define D5_CACS_VREF_36P0  0x7B
#define D5_CACS_VREF_36P5  0x7A
#define D5_CACS_VREF_37P0  0x79
#define D5_CACS_VREF_37P5  0x78
#define D5_CACS_VREF_38P0  0x77
#define D5_CACS_VREF_38P5  0x76
#define D5_CACS_VREF_39P0  0x75
#define D5_CACS_VREF_39P5  0x74
#define D5_CACS_VREF_40P0  0x73
#define D5_CACS_VREF_40P5  0x72
#define D5_CACS_VREF_41P0  0x71
#define D5_CACS_VREF_41P5  0x70
#define D5_CACS_VREF_42P0  0x6F
#define D5_CACS_VREF_42P5  0x6E
#define D5_CACS_VREF_43P0  0x6D
#define D5_CACS_VREF_43P5  0x6C
#define D5_CACS_VREF_44P0  0x6B
#define D5_CACS_VREF_44P5  0x6A
#define D5_CACS_VREF_45P0  0x69
#define D5_CACS_VREF_45P5  0x68
#define D5_CACS_VREF_46P0  0x67
#define D5_CACS_VREF_46P5  0x66
#define D5_CACS_VREF_47P0  0x65
#define D5_CACS_VREF_47P5  0x64
#define D5_CACS_VREF_48P0  0x63
#define D5_CACS_VREF_48P5  0x62
#define D5_CACS_VREF_49P0  0x61
#define D5_CACS_VREF_49P5  0x60
#define D5_CACS_VREF_50P0  0x5F
#define D5_CACS_VREF_50P5  0x5E
#define D5_CACS_VREF_51P0  0x5D
#define D5_CACS_VREF_51P5  0x5C
#define D5_CACS_VREF_52P0  0x5B
#define D5_CACS_VREF_52P5  0x5A
#define D5_CACS_VREF_53P0  0x59
#define D5_CACS_VREF_53P5  0x58
#define D5_CACS_VREF_54P0  0x57
#define D5_CACS_VREF_54P5  0x56
#define D5_CACS_VREF_55P0  0x55
#define D5_CACS_VREF_55P5  0x54
#define D5_CACS_VREF_56P0  0x53
#define D5_CACS_VREF_56P5  0x52
#define D5_CACS_VREF_57P0  0x51
#define D5_CACS_VREF_57P5  0x50
#define D5_CACS_VREF_58P0  0x4F
#define D5_CACS_VREF_58P5  0x4E
#define D5_CACS_VREF_59P0  0x4D
#define D5_CACS_VREF_59P5  0x4C
#define D5_CACS_VREF_60P0  0x4B
#define D5_CACS_VREF_60P5  0x4A
#define D5_CACS_VREF_61P0  0x49
#define D5_CACS_VREF_61P5  0x48
#define D5_CACS_VREF_62P0  0x47
#define D5_CACS_VREF_62P5  0x46
#define D5_CACS_VREF_63P0  0x45
#define D5_CACS_VREF_63P5  0x44
#define D5_CACS_VREF_64P0  0x43
#define D5_CACS_VREF_64P5  0x42
#define D5_CACS_VREF_65P0  0x41
#define D5_CACS_VREF_65P5  0x40
#define D5_CACS_VREF_66P0  0x3F
#define D5_CACS_VREF_66P5  0x3E
#define D5_CACS_VREF_67P0  0x3D
#define D5_CACS_VREF_67P5  0x3C
#define D5_CACS_VREF_68P0  0x3B
#define D5_CACS_VREF_68P5  0x3A
#define D5_CACS_VREF_69P0  0x39
#define D5_CACS_VREF_69P5  0x38
#define D5_CACS_VREF_70P0  0x37
#define D5_CACS_VREF_70P5  0x36
#define D5_CACS_VREF_71P0  0x35
#define D5_CACS_VREF_71P5  0x34
#define D5_CACS_VREF_72P0  0x33
#define D5_CACS_VREF_72P5  0x32
#define D5_CACS_VREF_73P0  0x31
#define D5_CACS_VREF_73P5  0x30
#define D5_CACS_VREF_74P0  0x2F
#define D5_CACS_VREF_74P5  0x2E
#define D5_CACS_VREF_75P0  0x2D
#define D5_CACS_VREF_75P5  0x2C
#define D5_CACS_VREF_76P0  0x2B
#define D5_CACS_VREF_76P5  0x2A
#define D5_CACS_VREF_77P0  0x29
#define D5_CACS_VREF_77P5  0x28
#define D5_CACS_VREF_78P0  0x27
#define D5_CACS_VREF_78P5  0x26
#define D5_CACS_VREF_79P0  0x25
#define D5_CACS_VREF_79P5  0x24
#define D5_CACS_VREF_80P0  0x23
#define D5_CACS_VREF_80P5  0x22
#define D5_CACS_VREF_81P0  0x21
#define D5_CACS_VREF_81P5  0x20
#define D5_CACS_VREF_82P0  0x1F
#define D5_CACS_VREF_82P5  0x1E
#define D5_CACS_VREF_83P0  0x1D
#define D5_CACS_VREF_83P5  0x1C
#define D5_CACS_VREF_84P0  0x1B
#define D5_CACS_VREF_84P5  0x1A
#define D5_CACS_VREF_85P0  0x19
#define D5_CACS_VREF_85P5  0x18
#define D5_CACS_VREF_86P0  0x17
#define D5_CACS_VREF_86P5  0x16
#define D5_CACS_VREF_87P0  0x15
#define D5_CACS_VREF_87P5  0x14
#define D5_CACS_VREF_88P0  0x13
#define D5_CACS_VREF_88P5  0x12
#define D5_CACS_VREF_89P0  0x11
#define D5_CACS_VREF_89P5  0x10
#define D5_CACS_VREF_90P0  0x0F
#define D5_CACS_VREF_90P5  0x0E
#define D5_CACS_VREF_91P0  0x0D
#define D5_CACS_VREF_91P5  0x0C
#define D5_CACS_VREF_92P0  0x0B
#define D5_CACS_VREF_92P5  0x0A
#define D5_CACS_VREF_93P0  0x09
#define D5_CACS_VREF_93P5  0x08
#define D5_CACS_VREF_94P0  0x07
#define D5_CACS_VREF_94P5  0x06
#define D5_CACS_VREF_95P0  0x05
#define D5_CACS_VREF_95P5  0x04
#define D5_CACS_VREF_96P0  0x03
#define D5_CACS_VREF_96P5  0x02
#define D5_CACS_VREF_97P0  0x01
#define D5_CACS_VREF_97P5  0x00

///
/// DFE
///
#define DFE_DIS           0      ///< DFE Disabled
#define DFE_EN            1      ///< DFE Enabled

#define JEDEC_ID_SK_HYNIX_HYUNDAI  0xAD80
#define SKHYNIX_24GBIT             0xAD84  // '4' for 24 Gbit
#define SKHYNIX_DIMM_6400          0xAD86  // '6' for 6400 DIMM
#define JEDEC_ID_SAMSUNG           0xCE80
#define SAMSUNG_24GBIT             0xCE84  // '4' for 24 Gbit
#define SAMSUNG_DIMM_6400          0xCE86  // '6' for 6400 DIMM
#define JEDEC_ID_MICRON_TECHNOLOGY 0x2C80
#define MICRON_24GBIT              0x2C84  // '4' for 24 Gbit
#define MICRON_D_DIE               0x2C8D  // 'D' for D-die
#define MICRON_DIMM_6400           0x2C86  // '6' for 6400 DIMM
//
// The entry header structure
//
typedef struct _PSCFG_DDR5_BUS_ENTRY_HEADER_S {
  UINT32 Length;          ///< The size of this structure
  UINT32 MemClk;          ///< The target MemClk Frequency
  UINT8  DimmPerCh;       ///< The number of DIMM slot per chanel
  UINT8  Dimm0;           ///< The bitmap of rank type of DIMM0
  UINT8  Dimm1;           ///< The bitmap of rank type of DIMM1
  UINT8  DevWidth;        ///< The bitmap of SDRAM IO width
} PSCFG_DDR5_BUS_ENTRY_HEADER_S;

//
// The entry payload structure
// The entry data is listed at the following.
//
typedef struct _PSCFG_DDR5_BUS_ENTRY_PAYLOAD_S {
  UINT32 Length;          ///< The size of this structure
  UINT32 CaTimingMode;    ///< CaTimingMode
  UINT32 Dimm0_RttNomWr;  ///< RTT_NOM_WR
  UINT32 Dimm0_RttNomRd;  ///< RTT_NOM_RD
  UINT32 Dimm0_RttWr;     ///< RTT_WR
  UINT32 Dimm0_RttPark;   ///< RTT_PARK
  UINT32 Dimm0_DqsRttPark;///< DQS_RTT_PARK
  UINT32 Dimm1_RttNomWr;  ///< RTT_NOM_WR
  UINT32 Dimm1_RttNomRd;  ///< RTT_NOM_RD
  UINT32 Dimm1_RttWr;     ///< RTT_WR
  UINT32 Dimm1_RttPark;   ///< RTT_PARK
  UINT32 Dimm1_DqsRttPark;///< DQS_RTT_PARK
  UINT32 DramDrv;         ///< DRAM_DRV
  UINT32 CkOdtA;          ///< CK_ODT Group A
  UINT32 CsOdtA;          ///< CS_ODT Group A
  UINT32 CaOdtA;          ///< CA_ODT Group A
  UINT32 CkOdtB;          ///< CK_ODT Group B
  UINT32 CsOdtB;          ///< CS_ODT Group B
  UINT32 CaOdtB;          ///< CA_ODT Group B
  UINT32 POdt;            ///< PODT
  UINT32 DqDrv;           ///< DQ Drv
  UINT32 AlertPu;         ///< Alert Pullup
  UINT32 CaDrv;           ///< CA Drv
  UINT32 PhyVref;         ///< PHY Vref
  UINT32 DqVref;          ///< DQ Vref
  UINT32 CaVref;          ///< CA Vref
  UINT32 CsVref;          ///< CS Vref
  UINT32 DCaVref;         ///< DCA Vref
  UINT32 DCsVref;         ///< DCS Vref
  UINT32 RxDfe;           ///< RXDFE
  UINT32 TxDfe;           ///< TXDFE
} PSCFG_DDR5_BUS_ENTRY_PAYLOAD_S;

/// DDR5 Bus configuration entry for DRAMDOWN, SODIMM, or UDIMM
typedef struct _PSCFG_BUS_ENTRY_S {
  PSCFG_DDR5_BUS_ENTRY_HEADER_S   Header;
  PSCFG_DDR5_BUS_ENTRY_PAYLOAD_S  Payload;
} PSCFG_BUS_ENTRY_S;

typedef struct _DDR5_RAW_CARD_ENTRY_HEADER_S {
  UINT32 Length;        ///< The size of this structure
  UINT32 MemClk;        ///< The target MemClk Frequency
  UINT8  DimmType;      ///< The bitmap of rank type of DIMM
  UINT8  DevWidth;      ///< The bitmap of SDRAM IO width
  UINT32 RcdMfgId;      ///< RCD JEDEC ID
  UINT8  RcdGen;        ///< RCD generation
  UINT32 RawCardRev;    ///< Raw card revision
  UINT32 DieRev;        ///< DRAM die stepping/revision
  UINT8  Density;       ///< DRAM density
} DDR5_RAW_CARD_ENTRY_HEADER_S;

typedef struct _DDR5_RAW_CARD_ENTRY_PAYLOAD_S {
  UINT32 Length;      ///< The size of this structure
  UINT16 QCK_Dev0;    ///<
  UINT16 QCK_Dev1;    ///<
  UINT16 QCK_Dev2;    ///<
  UINT16 QCK_Dev3;    ///<
  UINT16 QCK_Dev4;    ///<
  UINT16 QCK_Dev5;    ///<
  UINT16 QCK_Dev6;    ///<
  UINT16 QCK_Dev7;    ///<
  UINT16 QCK_Dev8;    ///<
  UINT16 QCK_Dev9;    ///<
  UINT16 QCK_Drv;     ///<
  UINT16 QCK_Slew;    ///<

  UINT16 QCS_Dev0;    ///<
  UINT16 QCS_Dev1;    ///<
  UINT16 QCS_Dev2;    ///<
  UINT16 QCS_Dev3;    ///<
  UINT16 QCS_Dev4;    ///<
  UINT16 QCS_Dev5;    ///<
  UINT16 QCS_Dev6;    ///<
  UINT16 QCS_Dev7;    ///<
  UINT16 QCS_Dev8;    ///<
  UINT16 QCS_Dev9;    ///<
  UINT16 QCS_Drv;     ///<
  UINT16 QCS_Slew;    ///<
  UINT16 QCS_Vref;    ///<

  UINT16 QCA_Dev0;    ///<
  UINT16 QCA_Dev1;    ///<
  UINT16 QCA_Dev2;    ///<
  UINT16 QCA_Dev3;    ///<
  UINT16 QCA_Dev4;    ///<
  UINT16 QCA_Dev5;    ///<
  UINT16 QCA_Dev6;    ///<
  UINT16 QCA_Dev7;    ///<
  UINT16 QCA_Dev8;    ///<
  UINT16 QCA_Dev9;    ///<
  UINT16 QCA_Drv;     ///<
  UINT16 QCA_Slew;    ///<
  UINT16 QCA_Vref;    ///<
} DDR5_RAW_CARD_ENTRY_PAYLOAD_S;

/// DDR5 raw card override entry for RDIMM
typedef struct _RAW_CARD_ENTRY_S {
  DDR5_RAW_CARD_ENTRY_HEADER_S   Header;
  DDR5_RAW_CARD_ENTRY_PAYLOAD_S  Payload;
} RAW_CARD_ENTRY_S;

/// DDR5 training override to specific DIMM part number && running speed
typedef struct _TRAINING_OVERRIDE_ENTRY_HEADER_S {
  UINT32 Length;              ///< The size of this structure
  UINT8  DimmPartNumber[31];  ///< DIMM module part number, SPD byte 521-550
  UINT8  Reserved;            ///< Make dword alignment
  UINT32 MemClkMask:4;        ///< The target MemClk Frequency, bit mask, bit 0: DDR4800, bit 1: DDR5600, bit 2: DDR6000, bit 3: DDR6400
  UINT32 ChannelMask:12;      ///< The target channel, bit 4-15 for channel 0-11, 0: do not apply to this channel, 1: apply to this channel
  UINT32 OffsetData:16;       ///< Bit[19:16], ReadDqDelay,  adjust to MsgBlock->rxclkdly_shift, 2's complement data
                              ///< Bit[23:20], ReadDqVref,   adjust to UMC::PHY::VrefDAC0/1/2/3, 2's complement data
                              ///< Bit[27:24], WriteDqDelay, adjust to UMC::Phy::TxDqDlyTg0/1/2/3, 2's complement data
                              ///< Bit[31:28], WriteDqVref,  adjust to MsgBlock->MR10_ADJ, 2's complement data
} TRAINING_OVERRIDE_ENTRY_HEADER_S;

typedef struct _TRAINING_OVERRIDE_ENTRY_S {
  TRAINING_OVERRIDE_ENTRY_HEADER_S   Header;
} TRAINING_OVERRIDE_ENTRY_S;

/*----------------------------------------------------------------------------
 *                           FUNCTIONS PROTOTYPE
 *
 *----------------------------------------------------------------------------
 */

#endif
