TOKEN
	Name  = "TurinSystemInventoryInfo_SUPPORT"
	Value  = "1"
	Help  = "Main switch to enable TurinSystemInventoryInfo support in Project"
	TokenType = Boolean
	TargetEQU = Yes
	TargetMAK = Yes
	Master = Yes
	Token = "SystemInventory_SUPPORT" "=" "1"
End

TOKEN
   	Name  = "DEFAULT_UPDATE_ASSET_TAG"
    Value  = "0"
    Help  = "1: Update Asset Tag with default details; 0: Asset tag won't be updated"
    TokenType = Boolean
    TargetH = Yes
    TargetEQU = Yes
    TargetMAK = Yes
End

TOKEN
    Name  = "AMI_SYS_INV_MAX_SOCKET"
    Value  = "$(NSOCKETS)"
    Help  = "Max CPU Sockets."
    TokenType = Integer
    TargetH = Yes
End

TOKEN
    Name  = "DIMM_INVENTORY_SMBIOS_V_3_2"
    Value  = "1"
    Help  = "**PORTING NEEDED**; Enable this Token if SMBIOS Version 3.2 is available in Project."
    TokenType = Integer
    TargetH = Yes
End

INFComponent
    Name  = "TurinSystemInventoryInfo"
    File  = "TurinSystemInventoryInfo.inf"
    Package  = "AmiServerModulePkg"
    ModuleTypes  = "BASE"
End

LibraryMapping
    Class  = "SystemInventoryInfoPortingLib"
    Instance  = "AmiServerModulePkg.TurinSystemInventoryInfo"
    ModuleTypes  = "BASE"
    Override = "AmiServerModulePkg.SystemInventoryInfo"
End

LibraryMapping
    Class  = "SystemInventoryInfoPortingLib"
    Instance  = "AmiServerModulePkg.TurinSystemInventoryInfo"
    ModuleTypes  = "BASE"
    Override = "AmiTsePkg.AMITSE"
End

################## Board Porting for CPU Info  - Start ################################
# AMD Turin Chalupa CRB has maximum of 2 CPU Socket, 12 channel per CPU, 1 slot per channel
### and 12 memory controller per CPU, each channel has one memory controller ###

PcdMapping
    Name  = "PcdAmiSysBoardSocketCount"
    GuidSpace  = "gEfiAmiServerModulePkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    Value  = "$(NSOCKETS)"
    Offset  = 00h
    Length  = 00h
    Help  = "Maximum Socket count"
    TargetDSC = Yes
    Token = "TurinSystemInventoryInfo_SUPPORT" "=" "1"
End

PcdMapping
    Name  = "PcdAmiSysMaxChannelPerSocket"
    GuidSpace  = "gEfiAmiServerModulePkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    Value  = "0xC"
    Offset  = 00h
    Length  = 00h
    Help  = "Maximum channel count per socket"
    TargetDSC = Yes
    Token = "TurinSystemInventoryInfo_SUPPORT" "=" "1"
End

PcdMapping
    Name  = "PcdAmiSysMaxDimmPerChannel"
    GuidSpace  = "gEfiAmiServerModulePkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    Value  = "$(NUMBER_OF_DIMMS_PER_CHANNEL)"
    Offset  = 00h
    Length  = 00h
    Help  = "Maximum Dimm slot per channel"
    TargetDSC = Yes
    Token = "TurinSystemInventoryInfo_SUPPORT" "=" "1"
End

################## Board Porting for Storage Slots  - Start ################################
### USB Ports - Chalupa has a Total of 4 USB 3.0 Ports; On the Front Left Top port is disabled in default configuration

### USB 3.0 Ports
ELINK
    Name = '{ L"PciRoot(0x2)/Pci(0x7,0x1)/Pci(0x0,0x4)/USB(0x2,0x0)", "Front Left Bottom Port"},'
    Parent = "SysInventoryStorageDeviceAssetTag"
    Token = "PLATFORM_SELECT" "=" "1"
End

ELINK
    Name = '{ L"PciRoot(0x7)/Pci(0x7,0x1)/Pci(0x0,0x4)/USB(0x2,0x0)", "Front Right Bottom Port"},'
    Parent = "SysInventoryStorageDeviceAssetTag"
    Token = "PLATFORM_SELECT" "=" "1"
End

ELINK
    Name = '{ L"PciRoot(0x7)/Pci(0x7,0x1)/Pci(0x0,0x4)/USB(0x3,0x0)", "Front Right Top Port"},'
    Parent = "SysInventoryStorageDeviceAssetTag"
    Token = "PLATFORM_SELECT" "=" "1"
End

### NVMe Ports - Chalupa has a Total of 3 NVMe Ports

### M.2 Slot NVMe
ELINK
    Name = '{ L"PciRoot(0x5)/Pci(0x3,0x1)/Pci(0x0,0x0)/NVMe(0x1,D2-08-C0-50-72-A7-79-64)", "NVMe1"},'
    Parent = "SysInventoryStorageDeviceAssetTag"
    Token = "PLATFORM_SELECT" "=" "1"
End

ELINK
    Name = '{ L"PciRoot(0xD)/Pci(0x3,0x3)/Pci(0x0,0x0)/NVMe(0x1,D2-08-C0-50-72-A7-79-64)", "NVMe2"},'
    Parent = "SysInventoryStorageDeviceAssetTag"
    Token = "PLATFORM_SELECT" "=" "1"
End

ELINK
    Name = '{ L"PciRoot(0xD)/Pci(0x3,0x1)/Pci(0x0,0x0)/NVMe(0x1,D2-08-C0-50-72-A7-79-64)", "NVMe3"},'
    Parent = "SysInventoryStorageDeviceAssetTag"
    Token = "PLATFORM_SELECT" "=" "1"
End

################## Board Porting for Storage Slots  - END ###############################