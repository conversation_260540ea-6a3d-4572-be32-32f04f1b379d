<component>
    name = "BaseCryptLib"
    category = ModulePart
    LocalRoot = "CryptoPkg/Library/BaseCryptLib/"
    RefName = "CryptoPkg.BaseCryptLib"
[INF]
"CrtWrapperLib.inf"
"BaseCryptLib.inf"
"BaseCryptLibSocket.inf"
"PeiCryptLib.inf"
"RuntimeCryptLib.inf"
"SmmCryptLib.inf"
"SecCryptLib.inf"
[files]
"BaseCryptLib.sdl"
"BaseCryptLib.uni"
"PeiCryptLib.uni"
"RuntimeCryptLib.uni"
"SmmCryptLib.uni"
<endComponent>
