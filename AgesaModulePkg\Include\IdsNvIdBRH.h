/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */
//This file is auto generated, do not edit it manually

#ifndef _IDS_NV_ID_BRH_H_
#define _IDS_NV_ID_BRH_H_


#ifndef IDSNVID_CPU_SMT_CTRL
  #define IDSNVID_CPU_SMT_CTRL   (0xD000) //SMT Control
#endif

#ifndef IDSNVID_CMN_CPU_REQ_MIN_FREQ
  #define IDSNVID_CMN_CPU_REQ_MIN_FREQ   (0xD001) //Requested CPU min frequency
#endif

#ifndef IDSNVID_CMN_CPU_EN_REQ_MIN_FREQ
  #define IDSNVID_CMN_CPU_EN_REQ_MIN_FREQ   (0xD002) //Enable Requested CPU min frequency
#endif

#ifndef IDSNVID_CMN_CPU_RMSS
  #define IDSNVID_CMN_CPU_RMSS   (0xD003) //REP-MOV/STOS Streaming
#endif

#ifndef IDSNVID_CMN_CPU_GEN_W_A05
  #define IDSNVID_CMN_CPU_GEN_W_A05   (0xD004) //RedirectForReturnDis
#endif

#ifndef IDSNVID_CMN_CPU_PFEH
  #define IDSNVID_CMN_CPU_PFEH   (0xD005) //Platform First Error Handling
#endif

#ifndef IDSNVID_CMN_CPU_CPB
  #define IDSNVID_CMN_CPU_CPB   (0xD006) //Core Performance Boost
#endif

#ifndef IDSNVID_CMN_CPU_GLOBAL_CSTATE_CTRL
  #define IDSNVID_CMN_CPU_GLOBAL_CSTATE_CTRL   (0xD007) //Global C-state Control
#endif

#ifndef IDSNVID_CMN_GNB_POWER_SUPPLY_IDLE_CTRL
  #define IDSNVID_CMN_GNB_POWER_SUPPLY_IDLE_CTRL   (0xD008) //Power Supply Idle Control
#endif

#ifndef IDSNVID_CMN_CPU_STREAMING_STORES_CTRL
  #define IDSNVID_CMN_CPU_STREAMING_STORES_CTRL   (0xD009) //Streaming Stores Control
#endif

#ifndef IDSNVID_DBG_CPU_L_APIC_MODE
  #define IDSNVID_DBG_CPU_L_APIC_MODE   (0xD00A) //Local APIC Mode
#endif

#ifndef IDSNVID_CMN_CPU_CST_C1_CTRL
  #define IDSNVID_CMN_CPU_CST_C1_CTRL   (0xD00B) //ACPI _CST C1 Declaration
#endif

#ifndef IDSNVID_CMN_CPU_CST_C2_LATENCY
  #define IDSNVID_CMN_CPU_CST_C2_LATENCY   (0xD00C) //ACPI CST C2 Latency
#endif

#ifndef IDSNVID_CMN_CPU_MCA_ERR_THRESH_EN
  #define IDSNVID_CMN_CPU_MCA_ERR_THRESH_EN   (0xD00D) //MCA error thresh enable
#endif

#ifndef IDSNVID_CMN_CPU_MCA_ERR_THRESH_COUNT
  #define IDSNVID_CMN_CPU_MCA_ERR_THRESH_COUNT   (0xD00E) //MCA error thresh count
#endif

#ifndef IDSNVID_CMN_CPU_MCA_FRU_TEXT_EN
  #define IDSNVID_CMN_CPU_MCA_FRU_TEXT_EN   (0xD00F) //MCA FruText
#endif

#ifndef IDSNVID_CMN_CPU_SMU_PSP_DEBUG_MODE
  #define IDSNVID_CMN_CPU_SMU_PSP_DEBUG_MODE   (0xD010) //SMU and PSP Debug Mode
#endif

#ifndef IDSNVID_CMN_CPU_PPIN_CTRL
  #define IDSNVID_CMN_CPU_PPIN_CTRL   (0xD011) //PPIN Opt-in
#endif

#ifndef IDSNVID_CMN_CPU_SMEE
  #define IDSNVID_CMN_CPU_SMEE   (0xD012) //SMEE
#endif

#ifndef IDSNVID_PSP_SEV_CTRL
  #define IDSNVID_PSP_SEV_CTRL   (0xD013) //SEV Control
#endif

#ifndef IDSNVID_CMN_CPU_SEV_ASID_SPACE_LIMIT
  #define IDSNVID_CMN_CPU_SEV_ASID_SPACE_LIMIT   (0xD014) //SEV-ES ASID Space Limit
#endif

#ifndef IDSNVID_DBG_CPU_SNP_MEM_COVER
  #define IDSNVID_DBG_CPU_SNP_MEM_COVER   (0xD015) //SNP Memory (RMP Table) Coverage
#endif

#ifndef IDSNVID_DBG_CPU_SNP_MEM_SIZE_COVER
  #define IDSNVID_DBG_CPU_SNP_MEM_SIZE_COVER   (0xD016) //Amount of Memory to Cover
#endif

#ifndef IDSNVID_CMN_CPU64_BIT_MMIO_COVERAGE
  #define IDSNVID_CMN_CPU64_BIT_MMIO_COVERAGE   (0xD017) //RMP Coverage for 64Bit MMIO Ranges
#endif

#ifndef IDSNVID_CMN_CPU64_BIT_MMIO_RMP_S0_RB_MASK
  #define IDSNVID_CMN_CPU64_BIT_MMIO_RMP_S0_RB_MASK   (0xD018) //Socket0 RootBridge Mask for 64Bit MMIO RMP Coverage
#endif

#ifndef IDSNVID_CMN_CPU64_BIT_MMIO_RMP_S1_RB_MASK
  #define IDSNVID_CMN_CPU64_BIT_MMIO_RMP_S1_RB_MASK   (0xD019) //Socket1 RootBridge Mask for 64Bit MMIO RMP Coverage
#endif

#ifndef IDSNVID_DBG_CPU_SPLIT_RMP
  #define IDSNVID_DBG_CPU_SPLIT_RMP   (0xD01A) //Split RMP Table
#endif

#ifndef IDSNVID_DBG_CPU_SEGMENTED_RMP
  #define IDSNVID_DBG_CPU_SEGMENTED_RMP   (0xD01B) //Segmented RMP Table
#endif

#ifndef IDSNVID_DBG_CPU_RMP_SEGMENT_SIZE
  #define IDSNVID_DBG_CPU_RMP_SEGMENT_SIZE   (0xD01C) //RMP Segment Size
#endif

#ifndef IDSNVID_CMN_ACTION_ON_BIST_FAILURE
  #define IDSNVID_CMN_ACTION_ON_BIST_FAILURE   (0xD01D) //Action on BIST Failure
#endif

#ifndef IDSNVID_CMN_CPU_ERMS
  #define IDSNVID_CMN_CPU_ERMS   (0xD01E) //Enhanced REP MOVSB/STOSB (ERSM)
#endif

#ifndef IDSNVID_CMN_CPU_LOG_TRANSPARENT_ERRORS
  #define IDSNVID_CMN_CPU_LOG_TRANSPARENT_ERRORS   (0xD01F) //Log Transparent Errors
#endif

#ifndef IDSNVID_CMN_CPU_AVX512
  #define IDSNVID_CMN_CPU_AVX512   (0xD020) //AVX512
#endif

#ifndef IDSNVID_CMN_CPU_DIS_FST_STR_ERMSB
  #define IDSNVID_CMN_CPU_DIS_FST_STR_ERMSB   (0xD021) //ERMSB Caching Behavior
#endif

#ifndef IDSNVID_CMN_CPU_MON_MWAIT_DIS
  #define IDSNVID_CMN_CPU_MON_MWAIT_DIS   (0xD022) //MONITOR and MWAIT disable
#endif

#ifndef IDSNVID_CPU_SPECULATIVE_STORE_MODES
  #define IDSNVID_CPU_SPECULATIVE_STORE_MODES   (0xD023) //CPU Speculative Store Modes
#endif

#ifndef IDSNVID_CMN_CPU_FSRM
  #define IDSNVID_CMN_CPU_FSRM   (0xD024) //Fast Short REP MOVSB (FSRM)
#endif

#ifndef IDSNVID_CMN_CPU_PAUSE_CNT_SEL_1_0
  #define IDSNVID_CMN_CPU_PAUSE_CNT_SEL_1_0   (0xD025) //PauseCntSel_1_0
#endif

#ifndef IDSNVID_CMN_CPU_PF_REQ_THR_EN
  #define IDSNVID_CMN_CPU_PF_REQ_THR_EN   (0xD026) //Prefetch/Request Throttle
#endif

#ifndef IDSNVID_CMN_CMC_NOTIFICATION_TYPE
  #define IDSNVID_CMN_CMC_NOTIFICATION_TYPE   (0xD027) //CMC H/W Error Notification type
#endif

#ifndef IDSNVID_CMN_CPU_SCAN_DUMP_DBG_EN
  #define IDSNVID_CMN_CPU_SCAN_DUMP_DBG_EN   (0xD028) //Scan Dump Debug Enable
#endif

#ifndef IDSNVID_CMN_CPU_MCAX64_BANK_SUPPORT
  #define IDSNVID_CMN_CPU_MCAX64_BANK_SUPPORT   (0xD029) //MCAX 64 bank support
#endif

#ifndef IDSNVID_CMN_CPU_ADAPTIVE_ALLOC
  #define IDSNVID_CMN_CPU_ADAPTIVE_ALLOC   (0xD02A) //Adaptive Allocation (AA)
#endif

#ifndef IDSNVID_CPU_LATENCY_UNDER_LOAD
  #define IDSNVID_CPU_LATENCY_UNDER_LOAD   (0xD02B) //Latency Under Load (LUL)
#endif

#ifndef IDSNVID_CMN_CORE_TRACE_DUMP_EN
  #define IDSNVID_CMN_CORE_TRACE_DUMP_EN   (0xD02C) //Core Trace Dump Enable
#endif

#ifndef IDSNVID_CMN_CPU_F_P512
  #define IDSNVID_CMN_CPU_F_P512   (0xD02D) //FP512
#endif

#ifndef IDSNVID_CMN_CPU_AMD_ERMSB_REPO
  #define IDSNVID_CMN_CPU_AMD_ERMSB_REPO   (0xD02E) //AMD_ERMSB Reporting
#endif

#ifndef IDSNVID_CMN_CPU_OC_MODE
  #define IDSNVID_CMN_CPU_OC_MODE   (0xD02F) //OC Mode
#endif

#ifndef IDSNVID_CMN_CPU_DOWNCORE_MODE
  #define IDSNVID_CMN_CPU_DOWNCORE_MODE   (0xD030) //DownCore Mode
#endif

#ifndef IDSNVID_CPU_LEGAL_DISCLAIMER
  #define IDSNVID_CPU_LEGAL_DISCLAIMER   (0xD031) //Pstates Disclaimer
#endif

#ifndef IDSNVID_CPU_LEGAL_DISCLAIMER1
  #define IDSNVID_CPU_LEGAL_DISCLAIMER1   (0xD032) //Pstates Disclaimer 1
#endif

#ifndef IDSNVID_CPU_PST_CUSTOM_P0
  #define IDSNVID_CPU_PST_CUSTOM_P0   (0xD033) //Custom Pstate0
#endif

#ifndef IDSNVID_CPU_PST0_FREQ
  #define IDSNVID_CPU_PST0_FREQ   (0xD034) //Pstate0 Freq (MHz)
#endif

#ifndef IDSNVID_CPU_COF_P0
  #define IDSNVID_CPU_COF_P0   (0xD035) //Frequency (MHz)
#endif

#ifndef IDSNVID_CPU_VOLTAGE_P0
  #define IDSNVID_CPU_VOLTAGE_P0   (0xD036) //Voltage (uV)
#endif

#ifndef IDSNVID_CPU_PST0_FID
  #define IDSNVID_CPU_PST0_FID   (0xD037) //Pstate0 FID
#endif

#ifndef IDSNVID_CPU_PST0_VID
  #define IDSNVID_CPU_PST0_VID   (0xD038) //Pstate0 VID
#endif

#ifndef IDSNVID_CPU_PST_CUSTOM_P1
  #define IDSNVID_CPU_PST_CUSTOM_P1   (0xD039) //Custom Pstate1
#endif

#ifndef IDSNVID_CPU_COF_P1
  #define IDSNVID_CPU_COF_P1   (0xD03A) //Frequency (MHz)
#endif

#ifndef IDSNVID_CPU_VOLTAGE_P1
  #define IDSNVID_CPU_VOLTAGE_P1   (0xD03B) //Voltage (uV)
#endif

#ifndef IDSNVID_CPU_PST1_FID
  #define IDSNVID_CPU_PST1_FID   (0xD03C) //Pstate1 FID
#endif

#ifndef IDSNVID_CPU_PST1_VID
  #define IDSNVID_CPU_PST1_VID   (0xD03D) //Pstate1 VID
#endif

#ifndef IDSNVID_CPU_PST_CUSTOM_P2
  #define IDSNVID_CPU_PST_CUSTOM_P2   (0xD03E) //Custom Pstate2
#endif

#ifndef IDSNVID_CPU_COF_P2
  #define IDSNVID_CPU_COF_P2   (0xD03F) //Frequency (MHz)
#endif

#ifndef IDSNVID_CPU_VOLTAGE_P2
  #define IDSNVID_CPU_VOLTAGE_P2   (0xD040) //Voltage (uV)
#endif

#ifndef IDSNVID_CPU_PST2_FID
  #define IDSNVID_CPU_PST2_FID   (0xD041) //Pstate2 FID
#endif

#ifndef IDSNVID_CPU_PST2_VID
  #define IDSNVID_CPU_PST2_VID   (0xD042) //Pstate2 VID
#endif

#ifndef IDSNVID_CPU_PST_CUSTOM_P3
  #define IDSNVID_CPU_PST_CUSTOM_P3   (0xD043) //Custom Pstate3
#endif

#ifndef IDSNVID_CPU_COF_P3
  #define IDSNVID_CPU_COF_P3   (0xD044) //Frequency (MHz)
#endif

#ifndef IDSNVID_CPU_VOLTAGE_P3
  #define IDSNVID_CPU_VOLTAGE_P3   (0xD045) //Voltage (uV)
#endif

#ifndef IDSNVID_CPU_PST3_FID
  #define IDSNVID_CPU_PST3_FID   (0xD046) //Pstate3 FID
#endif

#ifndef IDSNVID_CPU_PST3_VID
  #define IDSNVID_CPU_PST3_VID   (0xD047) //Pstate3 VID
#endif

#ifndef IDSNVID_CPU_PST_CUSTOM_P4
  #define IDSNVID_CPU_PST_CUSTOM_P4   (0xD048) //Custom Pstate4
#endif

#ifndef IDSNVID_CPU_COF_P4
  #define IDSNVID_CPU_COF_P4   (0xD049) //Frequency (MHz)
#endif

#ifndef IDSNVID_CPU_VOLTAGE_P4
  #define IDSNVID_CPU_VOLTAGE_P4   (0xD04A) //Voltage (uV)
#endif

#ifndef IDSNVID_CPU_PST4_FID
  #define IDSNVID_CPU_PST4_FID   (0xD04B) //Pstate4 FID
#endif

#ifndef IDSNVID_CPU_PST4_VID
  #define IDSNVID_CPU_PST4_VID   (0xD04C) //Pstate4 VID
#endif

#ifndef IDSNVID_CPU_PST_CUSTOM_P5
  #define IDSNVID_CPU_PST_CUSTOM_P5   (0xD04D) //Custom Pstate5
#endif

#ifndef IDSNVID_CPU_COF_P5
  #define IDSNVID_CPU_COF_P5   (0xD04E) //Frequency (MHz)
#endif

#ifndef IDSNVID_CPU_VOLTAGE_P5
  #define IDSNVID_CPU_VOLTAGE_P5   (0xD04F) //Voltage (uV)
#endif

#ifndef IDSNVID_CPU_PST5_FID
  #define IDSNVID_CPU_PST5_FID   (0xD050) //Pstate5 FID
#endif

#ifndef IDSNVID_CPU_PST5_VID
  #define IDSNVID_CPU_PST5_VID   (0xD051) //Pstate5 VID
#endif

#ifndef IDSNVID_CPU_PST_CUSTOM_P6
  #define IDSNVID_CPU_PST_CUSTOM_P6   (0xD052) //Custom Pstate6
#endif

#ifndef IDSNVID_CPU_COF_P6
  #define IDSNVID_CPU_COF_P6   (0xD053) //Frequency (MHz)
#endif

#ifndef IDSNVID_CPU_VOLTAGE_P6
  #define IDSNVID_CPU_VOLTAGE_P6   (0xD054) //Voltage (uV)
#endif

#ifndef IDSNVID_CPU_PST6_FID
  #define IDSNVID_CPU_PST6_FID   (0xD055) //Pstate6 FID
#endif

#ifndef IDSNVID_CPU_PST6_VID
  #define IDSNVID_CPU_PST6_VID   (0xD056) //Pstate6 VID
#endif

#ifndef IDSNVID_CPU_PST_CUSTOM_P7
  #define IDSNVID_CPU_PST_CUSTOM_P7   (0xD057) //Custom Pstate7
#endif

#ifndef IDSNVID_CPU_COF_P7
  #define IDSNVID_CPU_COF_P7   (0xD058) //Frequency (MHz)
#endif

#ifndef IDSNVID_CPU_VOLTAGE_P7
  #define IDSNVID_CPU_VOLTAGE_P7   (0xD059) //Voltage (uV)
#endif

#ifndef IDSNVID_CPU_PST7_FID
  #define IDSNVID_CPU_PST7_FID   (0xD05A) //Pstate7 FID
#endif

#ifndef IDSNVID_CPU_PST7_VID
  #define IDSNVID_CPU_PST7_VID   (0xD05B) //Pstate7 VID
#endif

#ifndef IDSNVID_CMN_CPU_CCD0_DOWNCORE_BIT_MAP
  #define IDSNVID_CMN_CPU_CCD0_DOWNCORE_BIT_MAP   (0xD05C) //CCD 0 DownCore Bitmap
#endif

#ifndef IDSNVID_CMN_CPU_CCD1_DOWNCORE_BIT_MAP
  #define IDSNVID_CMN_CPU_CCD1_DOWNCORE_BIT_MAP   (0xD05D) //CCD 1 DownCore Bitmap
#endif

#ifndef IDSNVID_CMN_CPU_CCD2_DOWNCORE_BIT_MAP
  #define IDSNVID_CMN_CPU_CCD2_DOWNCORE_BIT_MAP   (0xD05E) //CCD 2 DownCore Bitmap
#endif

#ifndef IDSNVID_CMN_CPU_CCD3_DOWNCORE_BIT_MAP
  #define IDSNVID_CMN_CPU_CCD3_DOWNCORE_BIT_MAP   (0xD05F) //CCD 3 DownCore Bitmap
#endif

#ifndef IDSNVID_CMN_CPU_CCD4_DOWNCORE_BIT_MAP
  #define IDSNVID_CMN_CPU_CCD4_DOWNCORE_BIT_MAP   (0xD060) //CCD 4 DownCore Bitmap
#endif

#ifndef IDSNVID_CMN_CPU_CCD5_DOWNCORE_BIT_MAP
  #define IDSNVID_CMN_CPU_CCD5_DOWNCORE_BIT_MAP   (0xD061) //CCD 5 DownCore Bitmap
#endif

#ifndef IDSNVID_CMN_CPU_CCD6_DOWNCORE_BIT_MAP
  #define IDSNVID_CMN_CPU_CCD6_DOWNCORE_BIT_MAP   (0xD062) //CCD 6 DownCore Bitmap
#endif

#ifndef IDSNVID_CMN_CPU_CCD7_DOWNCORE_BIT_MAP
  #define IDSNVID_CMN_CPU_CCD7_DOWNCORE_BIT_MAP   (0xD063) //CCD 7 DownCore Bitmap
#endif

#ifndef IDSNVID_CMN_CPU_CCD8_DOWNCORE_BIT_MAP
  #define IDSNVID_CMN_CPU_CCD8_DOWNCORE_BIT_MAP   (0xD064) //CCD 8 DownCore Bitmap
#endif

#ifndef IDSNVID_CMN_CPU_CCD9_DOWNCORE_BIT_MAP
  #define IDSNVID_CMN_CPU_CCD9_DOWNCORE_BIT_MAP   (0xD065) //CCD 9 DownCore Bitmap
#endif

#ifndef IDSNVID_CMN_CPU_CCD10_DOWNCORE_BIT_MAP
  #define IDSNVID_CMN_CPU_CCD10_DOWNCORE_BIT_MAP   (0xD066) //CCD 10 DownCore Bitmap
#endif

#ifndef IDSNVID_CMN_CPU_CCD11_DOWNCORE_BIT_MAP
  #define IDSNVID_CMN_CPU_CCD11_DOWNCORE_BIT_MAP   (0xD067) //CCD 11 DownCore Bitmap
#endif

#ifndef IDSNVID_CMN_CPU_CCD12_DOWNCORE_BIT_MAP
  #define IDSNVID_CMN_CPU_CCD12_DOWNCORE_BIT_MAP   (0xD068) //CCD 12 DownCore Bitmap
#endif

#ifndef IDSNVID_CMN_CPU_CCD13_DOWNCORE_BIT_MAP
  #define IDSNVID_CMN_CPU_CCD13_DOWNCORE_BIT_MAP   (0xD069) //CCD 13 DownCore Bitmap
#endif

#ifndef IDSNVID_CMN_CPU_CCD14_DOWNCORE_BIT_MAP
  #define IDSNVID_CMN_CPU_CCD14_DOWNCORE_BIT_MAP   (0xD06A) //CCD 14 DownCore Bitmap
#endif

#ifndef IDSNVID_CMN_CPU_CCD15_DOWNCORE_BIT_MAP
  #define IDSNVID_CMN_CPU_CCD15_DOWNCORE_BIT_MAP   (0xD06B) //CCD 15 DownCore Bitmap
#endif

#ifndef IDSNVID_CPU_CCD_CTRL
  #define IDSNVID_CPU_CCD_CTRL   (0xD06C) //CCD Control
#endif

#ifndef IDSNVID_CPU_CORE_CTRL
  #define IDSNVID_CPU_CORE_CTRL   (0xD06D) //Core control
#endif

#ifndef IDSNVID_CMN_CPU_L1_STREAM_HW_PREFETCHER
  #define IDSNVID_CMN_CPU_L1_STREAM_HW_PREFETCHER   (0xD06E) //L1 Stream HW Prefetcher
#endif

#ifndef IDSNVID_CMN_CPU_L1_STRIDE_PREFETCHER
  #define IDSNVID_CMN_CPU_L1_STRIDE_PREFETCHER   (0xD06F) //L1 Stride Prefetcher
#endif

#ifndef IDSNVID_CMN_CPU_L1_REGION_PREFETCHER
  #define IDSNVID_CMN_CPU_L1_REGION_PREFETCHER   (0xD070) //L1 Region Prefetcher
#endif

#ifndef IDSNVID_CMN_CPU_L2_STREAM_HW_PREFETCHER
  #define IDSNVID_CMN_CPU_L2_STREAM_HW_PREFETCHER   (0xD071) //L2 Stream HW Prefetcher
#endif

#ifndef IDSNVID_CMN_CPU_L2_UP_DOWN_PREFETCHER
  #define IDSNVID_CMN_CPU_L2_UP_DOWN_PREFETCHER   (0xD072) //L2 Up/Down Prefetcher
#endif

#ifndef IDSNVID_CMN_CPU_L1_BURST_PREFETCH_MODE
  #define IDSNVID_CMN_CPU_L1_BURST_PREFETCH_MODE   (0xD073) //L1 Burst Prefetch Mode
#endif

#ifndef IDSNVID_DBG_CPU_GEN_CPU_WDT
  #define IDSNVID_DBG_CPU_GEN_CPU_WDT   (0xD074) //Core Watchdog Timer Enable
#endif

#ifndef IDSNVID_DBG_CPU_GEN_CPU_WDT_TIMEOUT
  #define IDSNVID_DBG_CPU_GEN_CPU_WDT_TIMEOUT   (0xD075) //Core Watchdog Timer Interval
#endif

#ifndef IDSNVID_DF_CMN_WDT_INTERVAL
  #define IDSNVID_DF_CMN_WDT_INTERVAL   (0xD076) //DF Watchdog Timer Interval
#endif

#ifndef IDSNVID_DF_CMN_EXT_IP_SYNC_FLOOD_PROP
  #define IDSNVID_DF_CMN_EXT_IP_SYNC_FLOOD_PROP   (0xD077) //Disable DF to external IP SyncFloodPropagation
#endif

#ifndef IDSNVID_DF_CMN_DIS_SYNC_FLOOD_PROP
  #define IDSNVID_DF_CMN_DIS_SYNC_FLOOD_PROP   (0xD078) //Sync Flood Propagation to DF Components
#endif

#ifndef IDSNVID_DF_CMN_FREEZE_QUEUE_ERROR
  #define IDSNVID_DF_CMN_FREEZE_QUEUE_ERROR   (0xD079) //Freeze DF module queues on error
#endif

#ifndef IDSNVID_DF_CMN_CC6_MEM_ENCRYPTION
  #define IDSNVID_DF_CMN_CC6_MEM_ENCRYPTION   (0xD07A) //CC6 memory region encryption
#endif

#ifndef IDSNVID_DF_CMN_CCD_BW_THROTTLE_LV
  #define IDSNVID_DF_CMN_CCD_BW_THROTTLE_LV   (0xD07B) //CCD B/W Balance Throttle Level
#endif

#ifndef IDSNVID_DF_DBG_NUM_PCI_SEGMENTS
  #define IDSNVID_DF_DBG_NUM_PCI_SEGMENTS   (0xD07C) //Number of PCI Segments
#endif

#ifndef IDSNVID_DF_CMN_CCM_THROT
  #define IDSNVID_DF_CMN_CCM_THROT   (0xD07D) //CCM Throttler
#endif

#ifndef IDSNVID_DF_CMN_FINE_THROT_HEAVY
  #define IDSNVID_DF_CMN_FINE_THROT_HEAVY   (0xD07E) //MemReqBandwidthControl[FineThrotHeavy]
#endif

#ifndef IDSNVID_DF_CMN_FINE_THROT_LIGHT
  #define IDSNVID_DF_CMN_FINE_THROT_LIGHT   (0xD07F) //MemReqBandwidthControl[FineThrotLight]
#endif

#ifndef IDSNVID_DF_CMN_CLEAN_VIC_FTI_CMD_BAL
  #define IDSNVID_DF_CMN_CLEAN_VIC_FTI_CMD_BAL   (0xD080) //Clean Victim FTI Cmd Balancing
#endif

#ifndef IDSNVID_DF_CMN_REQV_REQ_ND_IMB_THR
  #define IDSNVID_DF_CMN_REQV_REQ_ND_IMB_THR   (0xD081) //CCMConfig5[ReqvReqNDImbThr]
#endif

#ifndef IDSNVID_DF_CMN_CXL_STRONGLY_ORDERED_WRITES
  #define IDSNVID_DF_CMN_CXL_STRONGLY_ORDERED_WRITES   (0xD082) //CXL Strongly Ordered Writes
#endif

#ifndef IDSNVID_DF_CMN_ENHANCED_PART_WR
  #define IDSNVID_DF_CMN_ENHANCED_PART_WR   (0xD083) //Enhanced Partial Writes to Same Address
#endif

#ifndef IDSNVID_DF_CMN_DRAM_NPS
  #define IDSNVID_DF_CMN_DRAM_NPS   (0xD084) //NUMA nodes per socket
#endif

#ifndef IDSNVID_DF_CMN_MEM_INTLV
  #define IDSNVID_DF_CMN_MEM_INTLV   (0xD085) //Memory interleaving
#endif

#ifndef IDSNVID_DF_CMN_MIXED_INTERLEAVED_MODE
  #define IDSNVID_DF_CMN_MIXED_INTERLEAVED_MODE   (0xD086) //Mixed interleaved mode
#endif

#ifndef IDSNVID_DF_CMN_CXL_MEM_INTLV
  #define IDSNVID_DF_CMN_CXL_MEM_INTLV   (0xD087) //CXL Memory interleaving
#endif

#ifndef IDSNVID_DF_CNLI_SUBLINK_INTERLEAVING
  #define IDSNVID_DF_CNLI_SUBLINK_INTERLEAVING   (0xD088) //CXL Sublink interleaving
#endif

#ifndef IDSNVID_DF_CMN_DRAM_MAP_INVERSION
  #define IDSNVID_DF_CMN_DRAM_MAP_INVERSION   (0xD089) //DRAM map inversion
#endif

#ifndef IDSNVID_DF_CMN_CC6_ALLOCATION_SCHEME
  #define IDSNVID_DF_CMN_CC6_ALLOCATION_SCHEME   (0xD08A) //Location of private memory regions
#endif

#ifndef IDSNVID_DF_CMN_ACPI_SRAT_L3_NUMA
  #define IDSNVID_DF_CMN_ACPI_SRAT_L3_NUMA   (0xD08B) //ACPI SRAT L3 Cache As NUMA Domain
#endif

#ifndef IDSNVID_DF_CMN_ACPI_SLIT_DIST_CTRL
  #define IDSNVID_DF_CMN_ACPI_SLIT_DIST_CTRL   (0xD08C) //ACPI SLIT Distance Control
#endif

#ifndef IDSNVID_DF_CMN_ACPI_SLIT_REMOTE_FAR
  #define IDSNVID_DF_CMN_ACPI_SLIT_REMOTE_FAR   (0xD08D) //ACPI SLIT remote relative distance
#endif

#ifndef IDSNVID_DF_CMN_ACPI_SLIT_VIRTUAL_DIST
  #define IDSNVID_DF_CMN_ACPI_SLIT_VIRTUAL_DIST   (0xD08E) //ACPI SLIT virtual distance
#endif

#ifndef IDSNVID_DF_CMN_ACPI_SLIT_LCL_DIST
  #define IDSNVID_DF_CMN_ACPI_SLIT_LCL_DIST   (0xD08F) //ACPI SLIT same socket distance
#endif

#ifndef IDSNVID_DF_CMN_ACPI_SLIT_RMT_DIST
  #define IDSNVID_DF_CMN_ACPI_SLIT_RMT_DIST   (0xD090) //ACPI SLIT remote socket distance
#endif

#ifndef IDSNVID_DF_CMN_ACPI_SLIT_CXL_LCL
  #define IDSNVID_DF_CMN_ACPI_SLIT_CXL_LCL   (0xD091) //ACPI SLIT local CXL distance
#endif

#ifndef IDSNVID_DF_CMN_ACPI_SLIT_CXL_RMT
  #define IDSNVID_DF_CMN_ACPI_SLIT_CXL_RMT   (0xD092) //ACPI SLIT remote CXL distance
#endif

#ifndef IDSNVID_DF_CMN_GMI_ENCRYPTION
  #define IDSNVID_DF_CMN_GMI_ENCRYPTION   (0xD093) //GMI encryption control
#endif

#ifndef IDSNVID_DF_CMN_X_GMI_ENCRYPTION
  #define IDSNVID_DF_CMN_X_GMI_ENCRYPTION   (0xD094) //xGMI encryption control
#endif

#ifndef IDSNVID_DF_DBG_XGMI_LINK_CFG
  #define IDSNVID_DF_DBG_XGMI_LINK_CFG   (0xD095) //xGMI Link Configuration
#endif

#ifndef IDSNVID_DF_CMN4_LINK_MAX_XGMI_SPEED
  #define IDSNVID_DF_CMN4_LINK_MAX_XGMI_SPEED   (0xD096) //4-link xGMI max speed
#endif

#ifndef IDSNVID_DF_CMN3_LINK_MAX_XGMI_SPEED
  #define IDSNVID_DF_CMN3_LINK_MAX_XGMI_SPEED   (0xD097) //3-link xGMI max speed
#endif

#ifndef IDSNVID_DF_XGMI_CRC_SCALE
  #define IDSNVID_DF_XGMI_CRC_SCALE   (0xD098) //xGMI CRC Scale
#endif

#ifndef IDSNVID_DF_XGMI_CRC_THRESHOLD
  #define IDSNVID_DF_XGMI_CRC_THRESHOLD   (0xD099) //xGMI CRC Threshold
#endif

#ifndef IDSNVID_DF_XGMI_PRESET_CONTROL
  #define IDSNVID_DF_XGMI_PRESET_CONTROL   (0xD09A) //xGMI Preset Control
#endif

#ifndef IDSNVID_DF_XGMI_TRAINING_ERR_MASK
  #define IDSNVID_DF_XGMI_TRAINING_ERR_MASK   (0xD09B) //xGMI Training Err Mask
#endif

#ifndef IDSNVID_DF_XGMI_PRESET_P11
  #define IDSNVID_DF_XGMI_PRESET_P11   (0xD09C) //Preset P11 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_CMN1_P11
  #define IDSNVID_DF_XGMI_CMN1_P11   (0xD09D) //Preset P11 Cmn1
#endif

#ifndef IDSNVID_DF_XGMI_CN_P11
  #define IDSNVID_DF_XGMI_CN_P11   (0xD09E) //Preset P11 Cn
#endif

#ifndef IDSNVID_DF_XGMI_CNP1_P11
  #define IDSNVID_DF_XGMI_CNP1_P11   (0xD09F) //Preset P11 Cnp1
#endif

#ifndef IDSNVID_DF_XGMI_PRESET_P12
  #define IDSNVID_DF_XGMI_PRESET_P12   (0xD0A0) //Preset P12 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_CMN1_P12
  #define IDSNVID_DF_XGMI_CMN1_P12   (0xD0A1) //Preset P12 Cmn1
#endif

#ifndef IDSNVID_DF_XGMI_CN_P12
  #define IDSNVID_DF_XGMI_CN_P12   (0xD0A2) //Preset P12 Cn
#endif

#ifndef IDSNVID_DF_XGMI_CNP1_P12
  #define IDSNVID_DF_XGMI_CNP1_P12   (0xD0A3) //Preset P12 Cnp1
#endif

#ifndef IDSNVID_DF_XGMI_PRESET_P13
  #define IDSNVID_DF_XGMI_PRESET_P13   (0xD0A4) //Preset P13 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_CMN1_P13
  #define IDSNVID_DF_XGMI_CMN1_P13   (0xD0A5) //Preset P13 Cmn1
#endif

#ifndef IDSNVID_DF_XGMI_CN_P13
  #define IDSNVID_DF_XGMI_CN_P13   (0xD0A6) //Preset P13 Cn
#endif

#ifndef IDSNVID_DF_XGMI_CNP1_P13
  #define IDSNVID_DF_XGMI_CNP1_P13   (0xD0A7) //Preset P13 Cnp1
#endif

#ifndef IDSNVID_DF_XGMI_PRESET_P14
  #define IDSNVID_DF_XGMI_PRESET_P14   (0xD0A8) //Preset P14 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_CMN1_P14
  #define IDSNVID_DF_XGMI_CMN1_P14   (0xD0A9) //Preset P14 Cmn1
#endif

#ifndef IDSNVID_DF_XGMI_CN_P14
  #define IDSNVID_DF_XGMI_CN_P14   (0xD0AA) //Preset P14 Cn
#endif

#ifndef IDSNVID_DF_XGMI_CNP1_P14
  #define IDSNVID_DF_XGMI_CNP1_P14   (0xD0AB) //Preset P14 Cnp1
#endif

#ifndef IDSNVID_DF_XGMI_PRESET_P15
  #define IDSNVID_DF_XGMI_PRESET_P15   (0xD0AC) //Preset P15 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_CMN1_P15
  #define IDSNVID_DF_XGMI_CMN1_P15   (0xD0AD) //Preset P15 Cmn1
#endif

#ifndef IDSNVID_DF_XGMI_CN_P15
  #define IDSNVID_DF_XGMI_CN_P15   (0xD0AE) //Preset P15 Cn
#endif

#ifndef IDSNVID_DF_XGMI_CNP1_P15
  #define IDSNVID_DF_XGMI_CNP1_P15   (0xD0AF) //Preset P15 Cnp1
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S0_L0
  #define IDSNVID_DF_XGMI_INIT_PRESET_S0_L0   (0xD0B0) //Initial Preset Socket 0 Link 0 Pstate0/1/2/3 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S0_L0_P0
  #define IDSNVID_DF_XGMI_INIT_PRESET_S0_L0_P0   (0xD0B1) //Initial Preset Socket 0 Link 0 Pstate0
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S0_L0_P1
  #define IDSNVID_DF_XGMI_INIT_PRESET_S0_L0_P1   (0xD0B2) //Initial Preset Socket 0 Link 0 Pstate1
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S0_L0_P2
  #define IDSNVID_DF_XGMI_INIT_PRESET_S0_L0_P2   (0xD0B3) //Initial Preset Socket 0 Link 0 Pstate2
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S0_L0_P3
  #define IDSNVID_DF_XGMI_INIT_PRESET_S0_L0_P3   (0xD0B4) //Initial Preset Socket 0 Link 0 Pstate3
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S0_L1
  #define IDSNVID_DF_XGMI_INIT_PRESET_S0_L1   (0xD0B5) //Initial Preset Socket 0 Link 1 Pstate0/1/2/3 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S0_L1_P0
  #define IDSNVID_DF_XGMI_INIT_PRESET_S0_L1_P0   (0xD0B6) //Initial Preset Socket 0 Link 1 Pstate0
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S0_L1_P1
  #define IDSNVID_DF_XGMI_INIT_PRESET_S0_L1_P1   (0xD0B7) //Initial Preset Socket 0 Link 1 Pstate1
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S0_L1_P2
  #define IDSNVID_DF_XGMI_INIT_PRESET_S0_L1_P2   (0xD0B8) //Initial Preset Socket 0 Link 1 Pstate2
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S0_L1_P3
  #define IDSNVID_DF_XGMI_INIT_PRESET_S0_L1_P3   (0xD0B9) //Initial Preset Socket 0 Link 1 Pstate3
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S0_L2
  #define IDSNVID_DF_XGMI_INIT_PRESET_S0_L2   (0xD0BA) //Initial Preset Socket 0 Link 2 Pstate0/1/2/3 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S0_L2_P0
  #define IDSNVID_DF_XGMI_INIT_PRESET_S0_L2_P0   (0xD0BB) //Initial Preset Socket 0 Link 2 Pstate0
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S0_L2_P1
  #define IDSNVID_DF_XGMI_INIT_PRESET_S0_L2_P1   (0xD0BC) //Initial Preset Socket 0 Link 2 Pstate1
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S0_L2_P2
  #define IDSNVID_DF_XGMI_INIT_PRESET_S0_L2_P2   (0xD0BD) //Initial Preset Socket 0 Link 2 Pstate2
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S0_L2_P3
  #define IDSNVID_DF_XGMI_INIT_PRESET_S0_L2_P3   (0xD0BE) //Initial Preset Socket 0 Link 2 Pstate3
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S0_L3
  #define IDSNVID_DF_XGMI_INIT_PRESET_S0_L3   (0xD0BF) //Initial Preset Socket 0 Link 3 Pstate0/1/2/3 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S0_L3_P0
  #define IDSNVID_DF_XGMI_INIT_PRESET_S0_L3_P0   (0xD0C0) //Initial Preset Socket 0 Link 3 Pstate0
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S0_L3_P1
  #define IDSNVID_DF_XGMI_INIT_PRESET_S0_L3_P1   (0xD0C1) //Initial Preset Socket 0 Link 3 Pstate1
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S0_L3_P2
  #define IDSNVID_DF_XGMI_INIT_PRESET_S0_L3_P2   (0xD0C2) //Initial Preset Socket 0 Link 3 Pstate2
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S0_L3_P3
  #define IDSNVID_DF_XGMI_INIT_PRESET_S0_L3_P3   (0xD0C3) //Initial Preset Socket 0 Link 3 Pstate3
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S1_L0
  #define IDSNVID_DF_XGMI_INIT_PRESET_S1_L0   (0xD0C4) //Initial Preset Socket 1 Link 0 Pstate0/1/2/3 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S1_L0_P0
  #define IDSNVID_DF_XGMI_INIT_PRESET_S1_L0_P0   (0xD0C5) //Initial Preset Socket 1 Link 0 Pstate0
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S1_L0_P1
  #define IDSNVID_DF_XGMI_INIT_PRESET_S1_L0_P1   (0xD0C6) //Initial Preset Socket 1 Link 0 Pstate1
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S1_L0_P2
  #define IDSNVID_DF_XGMI_INIT_PRESET_S1_L0_P2   (0xD0C7) //Initial Preset Socket 1 Link 0 Pstate2
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S1_L0_P3
  #define IDSNVID_DF_XGMI_INIT_PRESET_S1_L0_P3   (0xD0C8) //Initial Preset Socket 1 Link 0 Pstate3
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S1_L1
  #define IDSNVID_DF_XGMI_INIT_PRESET_S1_L1   (0xD0C9) //Initial Preset Socket 1 Link 1 Pstate0/1/2/3 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S1_L1_P0
  #define IDSNVID_DF_XGMI_INIT_PRESET_S1_L1_P0   (0xD0CA) //Initial Preset Socket 1 Link 1 Pstate0
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S1_L1_P1
  #define IDSNVID_DF_XGMI_INIT_PRESET_S1_L1_P1   (0xD0CB) //Initial Preset Socket 1 Link 1 Pstate1
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S1_L1_P2
  #define IDSNVID_DF_XGMI_INIT_PRESET_S1_L1_P2   (0xD0CC) //Initial Preset Socket 1 Link 1 Pstate2
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S1_L1_P3
  #define IDSNVID_DF_XGMI_INIT_PRESET_S1_L1_P3   (0xD0CD) //Initial Preset Socket 1 Link 1 Pstate3
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S1_L2
  #define IDSNVID_DF_XGMI_INIT_PRESET_S1_L2   (0xD0CE) //Initial Preset Socket 1 Link 2 Pstate0/1/2/3 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S1_L2_P0
  #define IDSNVID_DF_XGMI_INIT_PRESET_S1_L2_P0   (0xD0CF) //Initial Preset Socket 1 Link 2 Pstate0
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S1_L2_P1
  #define IDSNVID_DF_XGMI_INIT_PRESET_S1_L2_P1   (0xD0D0) //Initial Preset Socket 1 Link 2 Pstate1
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S1_L2_P2
  #define IDSNVID_DF_XGMI_INIT_PRESET_S1_L2_P2   (0xD0D1) //Initial Preset Socket 1 Link 2 Pstate2
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S1_L2_P3
  #define IDSNVID_DF_XGMI_INIT_PRESET_S1_L2_P3   (0xD0D2) //Initial Preset Socket 1 Link 2 Pstate3
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S1_L3
  #define IDSNVID_DF_XGMI_INIT_PRESET_S1_L3   (0xD0D3) //Initial Preset Socket 1 Link 3 Pstate0/1/2/3 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S1_L3_P0
  #define IDSNVID_DF_XGMI_INIT_PRESET_S1_L3_P0   (0xD0D4) //Initial Preset Socket 1 Link 3 Pstate0
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S1_L3_P1
  #define IDSNVID_DF_XGMI_INIT_PRESET_S1_L3_P1   (0xD0D5) //Initial Preset Socket 1 Link 3 Pstate1
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S1_L3_P2
  #define IDSNVID_DF_XGMI_INIT_PRESET_S1_L3_P2   (0xD0D6) //Initial Preset Socket 1 Link 3 Pstate2
#endif

#ifndef IDSNVID_DF_XGMI_INIT_PRESET_S1_L3_P3
  #define IDSNVID_DF_XGMI_INIT_PRESET_S1_L3_P3   (0xD0D7) //Initial Preset Socket 1 Link 3 Pstate3
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S0_L0_P01
  #define IDSNVID_DF_XGMI_TXEQ_S0_L0_P01   (0xD0D8) //TXEQ Search Mask Socket 0 Link 0 Pstate0/1 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S0_L0_P23
  #define IDSNVID_DF_XGMI_TXEQ_S0_L0_P23   (0xD0D9) //TXEQ Search Mask Socket 0 Link 0 Pstate2/3 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S0_L0_P0
  #define IDSNVID_DF_XGMI_TXEQ_S0_L0_P0   (0xD0DA) //TXEQ Search Mask Socket 0 Link 0 Pstate0
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S0_L0_P1
  #define IDSNVID_DF_XGMI_TXEQ_S0_L0_P1   (0xD0DB) //TXEQ Search Mask Socket 0 Link 0 Pstate1
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S0_L0_P2
  #define IDSNVID_DF_XGMI_TXEQ_S0_L0_P2   (0xD0DC) //TXEQ Search Mask Socket 0 Link 0 Pstate2
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S0_L0_P3
  #define IDSNVID_DF_XGMI_TXEQ_S0_L0_P3   (0xD0DD) //TXEQ Search Mask Socket 0 Link 0 Pstate3
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S0_L1_P01
  #define IDSNVID_DF_XGMI_TXEQ_S0_L1_P01   (0xD0DE) //TXEQ Search Mask Socket 0 Link 1 Pstate0/1 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S0_L1_P23
  #define IDSNVID_DF_XGMI_TXEQ_S0_L1_P23   (0xD0DF) //TXEQ Search Mask Socket 0 Link 1 Pstate2/3 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S0_L1_P0
  #define IDSNVID_DF_XGMI_TXEQ_S0_L1_P0   (0xD0E0) //TXEQ Search Mask Socket 0 Link 1 Pstate0
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S0_L1_P1
  #define IDSNVID_DF_XGMI_TXEQ_S0_L1_P1   (0xD0E1) //TXEQ Search Mask Socket 0 Link 1 Pstate1
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S0_L1_P2
  #define IDSNVID_DF_XGMI_TXEQ_S0_L1_P2   (0xD0E2) //TXEQ Search Mask Socket 0 Link 1 Pstate2
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S0_L1_P3
  #define IDSNVID_DF_XGMI_TXEQ_S0_L1_P3   (0xD0E3) //TXEQ Search Mask Socket 0 Link 1 Pstate3
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S0_L2_P01
  #define IDSNVID_DF_XGMI_TXEQ_S0_L2_P01   (0xD0E4) //TXEQ Search Mask Socket 0 Link 2 Pstate0/1 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S0_L2_P23
  #define IDSNVID_DF_XGMI_TXEQ_S0_L2_P23   (0xD0E5) //TXEQ Search Mask Socket 0 Link 2 Pstate2/3 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S0_L2_P0
  #define IDSNVID_DF_XGMI_TXEQ_S0_L2_P0   (0xD0E6) //TXEQ Search Mask Socket 0 Link 2 Pstate0
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S0_L2_P1
  #define IDSNVID_DF_XGMI_TXEQ_S0_L2_P1   (0xD0E7) //TXEQ Search Mask Socket 0 Link 2 Pstate1
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S0_L2_P2
  #define IDSNVID_DF_XGMI_TXEQ_S0_L2_P2   (0xD0E8) //TXEQ Search Mask Socket 0 Link 2 Pstate2
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S0_L2_P3
  #define IDSNVID_DF_XGMI_TXEQ_S0_L2_P3   (0xD0E9) //TXEQ Search Mask Socket 0 Link 2 Pstate3
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S0_L3_P01
  #define IDSNVID_DF_XGMI_TXEQ_S0_L3_P01   (0xD0EA) //TXEQ Search Mask Socket 0 Link 3 Pstate0/1 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S0_L3_P23
  #define IDSNVID_DF_XGMI_TXEQ_S0_L3_P23   (0xD0EB) //TXEQ Search Mask Socket 0 Link 3 Pstate2/3 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S0_L3_P0
  #define IDSNVID_DF_XGMI_TXEQ_S0_L3_P0   (0xD0EC) //TXEQ Search Mask Socket 0 Link 3 Pstate0
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S0_L3_P1
  #define IDSNVID_DF_XGMI_TXEQ_S0_L3_P1   (0xD0ED) //TXEQ Search Mask Socket 0 Link 3 Pstate1
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S0_L3_P2
  #define IDSNVID_DF_XGMI_TXEQ_S0_L3_P2   (0xD0EE) //TXEQ Search Mask Socket 0 Link 3 Pstate2
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S0_L3_P3
  #define IDSNVID_DF_XGMI_TXEQ_S0_L3_P3   (0xD0EF) //TXEQ Search Mask Socket 0 Link 3 Pstate3
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S1_L0_P01
  #define IDSNVID_DF_XGMI_TXEQ_S1_L0_P01   (0xD0F0) //TXEQ Search Mask Socket 1 Link 0 Pstate0/1 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S1_L0_P23
  #define IDSNVID_DF_XGMI_TXEQ_S1_L0_P23   (0xD0F1) //TXEQ Search Mask Socket 1 Link 0 Pstate2/3 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S1_L0_P0
  #define IDSNVID_DF_XGMI_TXEQ_S1_L0_P0   (0xD0F2) //TXEQ Search Mask Socket 1 Link 0 Pstate0
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S1_L0_P1
  #define IDSNVID_DF_XGMI_TXEQ_S1_L0_P1   (0xD0F3) //TXEQ Search Mask Socket 1 Link 0 Pstate1
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S1_L0_P2
  #define IDSNVID_DF_XGMI_TXEQ_S1_L0_P2   (0xD0F4) //TXEQ Search Mask Socket 1 Link 0 Pstate2
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S1_L0_P3
  #define IDSNVID_DF_XGMI_TXEQ_S1_L0_P3   (0xD0F5) //TXEQ Search Mask Socket 1 Link 0 Pstate3
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S1_L1_P01
  #define IDSNVID_DF_XGMI_TXEQ_S1_L1_P01   (0xD0F6) //TXEQ Search Mask Socket 1 Link 1 Pstate0/1 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S1_L1_P23
  #define IDSNVID_DF_XGMI_TXEQ_S1_L1_P23   (0xD0F7) //TXEQ Search Mask Socket 1 Link 1 Pstate2/3 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S1_L1_P0
  #define IDSNVID_DF_XGMI_TXEQ_S1_L1_P0   (0xD0F8) //TXEQ Search Mask Socket 1 Link 1 Pstate0
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S1_L1_P1
  #define IDSNVID_DF_XGMI_TXEQ_S1_L1_P1   (0xD0F9) //TXEQ Search Mask Socket 1 Link 1 Pstate1
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S1_L1_P2
  #define IDSNVID_DF_XGMI_TXEQ_S1_L1_P2   (0xD0FA) //TXEQ Search Mask Socket 1 Link 1 Pstate2
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S1_L1_P3
  #define IDSNVID_DF_XGMI_TXEQ_S1_L1_P3   (0xD0FB) //TXEQ Search Mask Socket 1 Link 1 Pstate3
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S1_L2_P01
  #define IDSNVID_DF_XGMI_TXEQ_S1_L2_P01   (0xD0FC) //TXEQ Search Mask Socket 1 Link 2 Pstate0/1 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S1_L2_P23
  #define IDSNVID_DF_XGMI_TXEQ_S1_L2_P23   (0xD0FD) //TXEQ Search Mask Socket 1 Link 2 Pstate2/3 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S1_L2_P0
  #define IDSNVID_DF_XGMI_TXEQ_S1_L2_P0   (0xD0FE) //TXEQ Search Mask Socket 1 Link 2 Pstate0
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S1_L2_P1
  #define IDSNVID_DF_XGMI_TXEQ_S1_L2_P1   (0xD0FF) //TXEQ Search Mask Socket 1 Link 2 Pstate1
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S1_L2_P2
  #define IDSNVID_DF_XGMI_TXEQ_S1_L2_P2   (0xD100) //TXEQ Search Mask Socket 1 Link 2 Pstate2
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S1_L2_P3
  #define IDSNVID_DF_XGMI_TXEQ_S1_L2_P3   (0xD101) //TXEQ Search Mask Socket 1 Link 2 Pstate3
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S1_L3_P01
  #define IDSNVID_DF_XGMI_TXEQ_S1_L3_P01   (0xD102) //TXEQ Search Mask Socket 1 Link 3 Pstate0/1 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S1_L3_P23
  #define IDSNVID_DF_XGMI_TXEQ_S1_L3_P23   (0xD103) //TXEQ Search Mask Socket 1 Link 3 Pstate2/3 (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S1_L3_P0
  #define IDSNVID_DF_XGMI_TXEQ_S1_L3_P0   (0xD104) //TXEQ Search Mask Socket 1 Link 3 Pstate0
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S1_L3_P1
  #define IDSNVID_DF_XGMI_TXEQ_S1_L3_P1   (0xD105) //TXEQ Search Mask Socket 1 Link 3 Pstate1
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S1_L3_P2
  #define IDSNVID_DF_XGMI_TXEQ_S1_L3_P2   (0xD106) //TXEQ Search Mask Socket 1 Link 3 Pstate2
#endif

#ifndef IDSNVID_DF_XGMI_TXEQ_S1_L3_P3
  #define IDSNVID_DF_XGMI_TXEQ_S1_L3_P3   (0xD107) //TXEQ Search Mask Socket 1 Link 3 Pstate3
#endif

#ifndef IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_CONTROL
  #define IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_CONTROL   (0xD108) //xGMI AC/DC Coupled Link Control
#endif

#ifndef IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK
  #define IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK   (0xD109) //xGMI AC/DC Coupled Link (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK0
  #define IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK0   (0xD10A) //xGMI AC/DC Coupled Link Socket 0 Link 0
#endif

#ifndef IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK1
  #define IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK1   (0xD10B) //xGMI AC/DC Coupled Link Socket 0 Link 1
#endif

#ifndef IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK2
  #define IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK2   (0xD10C) //xGMI AC/DC Coupled Link Socket 0 Link 2
#endif

#ifndef IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK3
  #define IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK3   (0xD10D) //xGMI AC/DC Coupled Link Socket 0 Link 3
#endif

#ifndef IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK0
  #define IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK0   (0xD10E) //xGMI AC/DC Coupled Link Socket 1 Link 0
#endif

#ifndef IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK1
  #define IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK1   (0xD10F) //xGMI AC/DC Coupled Link Socket 1 Link 1
#endif

#ifndef IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK2
  #define IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK2   (0xD110) //xGMI AC/DC Coupled Link Socket 1 Link 2
#endif

#ifndef IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK3
  #define IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK3   (0xD111) //xGMI AC/DC Coupled Link Socket 1 Link 3
#endif

#ifndef IDSNVID_DF_XGMI_CHANNEL_TYPE_CONTROL
  #define IDSNVID_DF_XGMI_CHANNEL_TYPE_CONTROL   (0xD112) //xGMI Channel Type Control
#endif

#ifndef IDSNVID_DF_XGMI_CHANNEL_TYPE
  #define IDSNVID_DF_XGMI_CHANNEL_TYPE   (0xD113) //xGMI Channel Type (APCB)
#endif

#ifndef IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK0
  #define IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK0   (0xD114) //xGMI Channel Type Socket 0 Link 0
#endif

#ifndef IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK1
  #define IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK1   (0xD115) //xGMI Channel Type Socket 0 Link 1
#endif

#ifndef IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK2
  #define IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK2   (0xD116) //xGMI Channel Type Socket 0 Link 2
#endif

#ifndef IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK3
  #define IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK3   (0xD117) //xGMI Channel Type Socket 0 Link 3
#endif

#ifndef IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK0
  #define IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK0   (0xD118) //xGMI Channel Type Socket 1 Link 0
#endif

#ifndef IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK1
  #define IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK1   (0xD119) //xGMI Channel Type Socket 1 Link 1
#endif

#ifndef IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK2
  #define IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK2   (0xD11A) //xGMI Channel Type Socket 1 Link 2
#endif

#ifndef IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK3
  #define IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK3   (0xD11B) //xGMI Channel Type Socket 1 Link 3
#endif

#ifndef IDSNVID_DF_CDMA
  #define IDSNVID_DF_CDMA   (0xD11C) //SDCI
#endif

#ifndef IDSNVID_DF_DBG_DIS_RMT_STEER
  #define IDSNVID_DF_DBG_DIS_RMT_STEER   (0xD11D) //DisRmtSteer
#endif

#ifndef IDSNVID_DF_CMN_PF_ORGANIZATION
  #define IDSNVID_DF_CMN_PF_ORGANIZATION   (0xD11E) //Organization
#endif

#ifndef IDSNVID_CMN_DF_PDR_TUNING
  #define IDSNVID_CMN_DF_PDR_TUNING   (0xD11F) //Periodic Directory Rinse (PDR) Tuning
#endif

#ifndef IDSNVID_DF_CMN_MEM_INTLV_PAGE_SIZE
  #define IDSNVID_DF_CMN_MEM_INTLV_PAGE_SIZE   (0xD120) //Tracking Granularity
#endif

#ifndef IDSNVID_DF_CMN_PF_PDR_MODE
  #define IDSNVID_DF_CMN_PF_PDR_MODE   (0xD121) //PDR Mode
#endif

#ifndef IDSNVID_CMN_MEM_CS_INTERLEAVE_DDR
  #define IDSNVID_CMN_MEM_CS_INTERLEAVE_DDR   (0xD122) //Chipselect Interleaving
#endif

#ifndef IDSNVID_CMN_MEM_ADDRESS_HASH_BANK_DDR
  #define IDSNVID_CMN_MEM_ADDRESS_HASH_BANK_DDR   (0xD123) //Address Hash Bank
#endif

#ifndef IDSNVID_CMN_MEM_ADDRESS_HASH_CS_DDR
  #define IDSNVID_CMN_MEM_ADDRESS_HASH_CS_DDR   (0xD124) //Address Hash CS
#endif

#ifndef IDSNVID_CMN_MEM_ADDRESS_HASH_RM_DDR
  #define IDSNVID_CMN_MEM_ADDRESS_HASH_RM_DDR   (0xD125) //Address Hash Rm
#endif

#ifndef IDSNVID_CMN_MEM_ADDRESS_HASH_SUBCHANNEL_DDR
  #define IDSNVID_CMN_MEM_ADDRESS_HASH_SUBCHANNEL_DDR   (0xD126) //Address Hash Subchannel
#endif

#ifndef IDSNVID_CMN_MEM_CTRLLER_BANK_SWAP_MODE_DDR
  #define IDSNVID_CMN_MEM_CTRLLER_BANK_SWAP_MODE_DDR   (0xD127) //BankSwapMode
#endif

#ifndef IDSNVID_CMN_MEM_CONTEXT_RESTORE_DDR
  #define IDSNVID_CMN_MEM_CONTEXT_RESTORE_DDR   (0xD128) //Memory Context Restore
#endif

#ifndef IDSNVID_DRAM_SURVIVES_WARM_RESET
  #define IDSNVID_DRAM_SURVIVES_WARM_RESET   (0xD129) //DRAM Survives Warm Reset
#endif

#ifndef IDSNVID_CMN_MEM_CTRLLER_PWR_DN_EN_DDR
  #define IDSNVID_CMN_MEM_CTRLLER_PWR_DN_EN_DDR   (0xD12A) //Power Down Enable
#endif

#ifndef IDSNVID_CMN_MEM_SUB_URG_REF_LOWER_BOUND
  #define IDSNVID_CMN_MEM_SUB_URG_REF_LOWER_BOUND   (0xD12B) //Sub Urgent Refresh Lower Bound
#endif

#ifndef IDSNVID_CMN_MEM_URG_REF_LIMIT
  #define IDSNVID_CMN_MEM_URG_REF_LIMIT   (0xD12C) //Urgent Refresh Limit
#endif

#ifndef IDSNVID_CMN_MEM_DRAM_REFRESH_RATE
  #define IDSNVID_CMN_MEM_DRAM_REFRESH_RATE   (0xD12D) //DRAM Refresh Rate
#endif

#ifndef IDSNVID_CMN_MEM_SELF_REFRESH_EXIT_STAGGERING
  #define IDSNVID_CMN_MEM_SELF_REFRESH_EXIT_STAGGERING   (0xD12E) //Self-Refresh Exit Staggering
#endif

#ifndef IDSNVID_CMN_MEMT2X_REFRESH_TEMPERATURE_THRESHOLD
  #define IDSNVID_CMN_MEMT2X_REFRESH_TEMPERATURE_THRESHOLD   (0xD12F) //DRAM 2x Refresh Temperature Threshold
#endif

#ifndef IDSNVID_CMN_MEM_CHANNEL_DISABLE_FLOAT_POWER_GOOD_DDR
  #define IDSNVID_CMN_MEM_CHANNEL_DISABLE_FLOAT_POWER_GOOD_DDR   (0xD130) //Memory Channel Disable Float Power Good
#endif

#ifndef IDSNVID_CMN_MEM_CHANNEL_DISABLE_BITMASK_DDR
  #define IDSNVID_CMN_MEM_CHANNEL_DISABLE_BITMASK_DDR   (0xD131) //Memory Channel Disable Bitmask
#endif

#ifndef IDSNVID_CMN_MEM_SOCKET0_CHANNEL0_DDR
  #define IDSNVID_CMN_MEM_SOCKET0_CHANNEL0_DDR   (0xD132) //Socket 0 Channel 0
#endif

#ifndef IDSNVID_CMN_MEM_SOCKET0_CHANNEL1_DDR
  #define IDSNVID_CMN_MEM_SOCKET0_CHANNEL1_DDR   (0xD133) //Socket 0 Channel 1
#endif

#ifndef IDSNVID_CMN_MEM_SOCKET0_CHANNEL2_DDR
  #define IDSNVID_CMN_MEM_SOCKET0_CHANNEL2_DDR   (0xD134) //Socket 0 Channel 2
#endif

#ifndef IDSNVID_CMN_MEM_SOCKET0_CHANNEL3_DDR
  #define IDSNVID_CMN_MEM_SOCKET0_CHANNEL3_DDR   (0xD135) //Socket 0 Channel 3
#endif

#ifndef IDSNVID_CMN_MEM_SOCKET0_CHANNEL4_DDR
  #define IDSNVID_CMN_MEM_SOCKET0_CHANNEL4_DDR   (0xD136) //Socket 0 Channel 4
#endif

#ifndef IDSNVID_CMN_MEM_SOCKET0_CHANNEL5_DDR
  #define IDSNVID_CMN_MEM_SOCKET0_CHANNEL5_DDR   (0xD137) //Socket 0 Channel 5
#endif

#ifndef IDSNVID_CMN_MEM_SOCKET0_CHANNEL6_DDR
  #define IDSNVID_CMN_MEM_SOCKET0_CHANNEL6_DDR   (0xD138) //Socket 0 Channel 6
#endif

#ifndef IDSNVID_CMN_MEM_SOCKET0_CHANNEL7_DDR
  #define IDSNVID_CMN_MEM_SOCKET0_CHANNEL7_DDR   (0xD139) //Socket 0 Channel 7
#endif

#ifndef IDSNVID_CMN_MEM_SOCKET0_CHANNEL8_DDR
  #define IDSNVID_CMN_MEM_SOCKET0_CHANNEL8_DDR   (0xD13A) //Socket 0 Channel 8
#endif

#ifndef IDSNVID_CMN_MEM_SOCKET0_CHANNEL9_DDR
  #define IDSNVID_CMN_MEM_SOCKET0_CHANNEL9_DDR   (0xD13B) //Socket 0 Channel 9
#endif

#ifndef IDSNVID_CMN_MEM_SOCKET0_CHANNEL10_DDR
  #define IDSNVID_CMN_MEM_SOCKET0_CHANNEL10_DDR   (0xD13C) //Socket 0 Channel 10
#endif

#ifndef IDSNVID_CMN_MEM_SOCKET0_CHANNEL11_DDR
  #define IDSNVID_CMN_MEM_SOCKET0_CHANNEL11_DDR   (0xD13D) //Socket 0 Channel 11
#endif

#ifndef IDSNVID_CMN_MEM_SOCKET1_CHANNEL0_DDR
  #define IDSNVID_CMN_MEM_SOCKET1_CHANNEL0_DDR   (0xD13E) //Socket 1 Channel 0
#endif

#ifndef IDSNVID_CMN_MEM_SOCKET1_CHANNEL1_DDR
  #define IDSNVID_CMN_MEM_SOCKET1_CHANNEL1_DDR   (0xD13F) //Socket 1 Channel 1
#endif

#ifndef IDSNVID_CMN_MEM_SOCKET1_CHANNEL2_DDR
  #define IDSNVID_CMN_MEM_SOCKET1_CHANNEL2_DDR   (0xD140) //Socket 1 Channel 2
#endif

#ifndef IDSNVID_CMN_MEM_SOCKET1_CHANNEL3_DDR
  #define IDSNVID_CMN_MEM_SOCKET1_CHANNEL3_DDR   (0xD141) //Socket 1 Channel 3
#endif

#ifndef IDSNVID_CMN_MEM_SOCKET1_CHANNEL4_DDR
  #define IDSNVID_CMN_MEM_SOCKET1_CHANNEL4_DDR   (0xD142) //Socket 1 Channel 4
#endif

#ifndef IDSNVID_CMN_MEM_SOCKET1_CHANNEL5_DDR
  #define IDSNVID_CMN_MEM_SOCKET1_CHANNEL5_DDR   (0xD143) //Socket 1 Channel 5
#endif

#ifndef IDSNVID_CMN_MEM_SOCKET1_CHANNEL6_DDR
  #define IDSNVID_CMN_MEM_SOCKET1_CHANNEL6_DDR   (0xD144) //Socket 1 Channel 6
#endif

#ifndef IDSNVID_CMN_MEM_SOCKET1_CHANNEL7_DDR
  #define IDSNVID_CMN_MEM_SOCKET1_CHANNEL7_DDR   (0xD145) //Socket 1 Channel 7
#endif

#ifndef IDSNVID_CMN_MEM_SOCKET1_CHANNEL8_DDR
  #define IDSNVID_CMN_MEM_SOCKET1_CHANNEL8_DDR   (0xD146) //Socket 1 Channel 8
#endif

#ifndef IDSNVID_CMN_MEM_SOCKET1_CHANNEL9_DDR
  #define IDSNVID_CMN_MEM_SOCKET1_CHANNEL9_DDR   (0xD147) //Socket 1 Channel 9
#endif

#ifndef IDSNVID_CMN_MEM_SOCKET1_CHANNEL10_DDR
  #define IDSNVID_CMN_MEM_SOCKET1_CHANNEL10_DDR   (0xD148) //Socket 1 Channel 10
#endif

#ifndef IDSNVID_CMN_MEM_SOCKET1_CHANNEL11_DDR
  #define IDSNVID_CMN_MEM_SOCKET1_CHANNEL11_DDR   (0xD149) //Socket 1 Channel 11
#endif

#ifndef IDSNVID_CMN_MEM_REF_MANAGEMENT_DDR
  #define IDSNVID_CMN_MEM_REF_MANAGEMENT_DDR   (0xD14A) //Refresh Management
#endif

#ifndef IDSNVID_CMN_MEM_ARFM_DDR
  #define IDSNVID_CMN_MEM_ARFM_DDR   (0xD14B) //Adaptive Refresh Management
#endif

#ifndef IDSNVID_CMN_MEM_RAAIMT_DDR
  #define IDSNVID_CMN_MEM_RAAIMT_DDR   (0xD14C) //RAA Initial Management Threshold
#endif

#ifndef IDSNVID_CMN_MEM_RAAMMT_DDR
  #define IDSNVID_CMN_MEM_RAAMMT_DDR   (0xD14D) //RAA Maximum Management Threshold
#endif

#ifndef IDSNVID_CMN_MEM_RAA_REF_DEC_MULTIPLIER_DDR
  #define IDSNVID_CMN_MEM_RAA_REF_DEC_MULTIPLIER_DDR   (0xD14E) //RAA Refresh Decrement Multiplier
#endif

#ifndef IDSNVID_CMN_MEM_DRFM_DDR
  #define IDSNVID_CMN_MEM_DRFM_DDR   (0xD14F) //DRFM Enable
#endif

#ifndef IDSNVID_CMN_MEM_DRFM_BRC_DDR
  #define IDSNVID_CMN_MEM_DRFM_BRC_DDR   (0xD150) //Bounded Refresh Configuration
#endif

#ifndef IDSNVID_CMN_MEM_DRFM_HASH_DDR
  #define IDSNVID_CMN_MEM_DRFM_HASH_DDR   (0xD151) //DRFM Hash Enable
#endif

#ifndef IDSNVID_CMN_MEM_MBIST_EN_DDR
  #define IDSNVID_CMN_MEM_MBIST_EN_DDR   (0xD152) //MBIST Enable
#endif

#ifndef IDSNVID_CMN_MEM_MBIST_TESTMODE_DDR
  #define IDSNVID_CMN_MEM_MBIST_TESTMODE_DDR   (0xD153) //MBIST Test Mode
#endif

#ifndef IDSNVID_CMN_MEM_MBIST_AGGRESSORS_DDR
  #define IDSNVID_CMN_MEM_MBIST_AGGRESSORS_DDR   (0xD154) //MBIST Aggressors
#endif

#ifndef IDSNVID_CMN_MEM_HEALING_BIST_ENABLE_BIT_MASK_DDR
  #define IDSNVID_CMN_MEM_HEALING_BIST_ENABLE_BIT_MASK_DDR   (0xD155) //DDR Healing BIST
#endif

#ifndef IDSNVID_CMN_MEM_HEALING_BIST_EXECUTION_MODE
  #define IDSNVID_CMN_MEM_HEALING_BIST_EXECUTION_MODE   (0xD156) //DDR Healing BIST Execution Mode
#endif

#ifndef IDSNVID_CMN_MEM_HEALING_BIST_REPAIR_TYPE_DDR
  #define IDSNVID_CMN_MEM_HEALING_BIST_REPAIR_TYPE_DDR   (0xD157) //DDR Healing BIST Repair Type
#endif

#ifndef IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM_SELECT
  #define IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM_SELECT   (0xD158) //PMU Mem BIST Algorithm Select
#endif

#ifndef IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM_BIT_MASK_DDR
  #define IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM_BIT_MASK_DDR   (0xD159) //PMU Mem BIST Algorithm Bitmask
#endif

#ifndef IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM1
  #define IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM1   (0xD15A) //Algorithm #1
#endif

#ifndef IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM2
  #define IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM2   (0xD15B) //Algorithm #2
#endif

#ifndef IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM3
  #define IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM3   (0xD15C) //Algorithm #3
#endif

#ifndef IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM4
  #define IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM4   (0xD15D) //Algorithm #4
#endif

#ifndef IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM5
  #define IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM5   (0xD15E) //Algorithm #5
#endif

#ifndef IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM6
  #define IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM6   (0xD15F) //Algorithm #6
#endif

#ifndef IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM7
  #define IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM7   (0xD160) //Algorithm #7
#endif

#ifndef IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM8
  #define IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM8   (0xD161) //Algorithm #8
#endif

#ifndef IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM9
  #define IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM9   (0xD162) //Algorithm #9
#endif

#ifndef IDSNVID_CMN_MEM_MBIST_PATTERN_SELECT
  #define IDSNVID_CMN_MEM_MBIST_PATTERN_SELECT   (0xD163) //Pattern Select
#endif

#ifndef IDSNVID_CMN_MEM_MBIST_PATTERN_LENGTH
  #define IDSNVID_CMN_MEM_MBIST_PATTERN_LENGTH   (0xD164) //Pattern Length
#endif

#ifndef IDSNVID_CMN_MEM_MBIST_AGGRESSORS_CHNL
  #define IDSNVID_CMN_MEM_MBIST_AGGRESSORS_CHNL   (0xD165) //Aggressor Channel
#endif

#ifndef IDSNVID_CMN_MEM_MBIST_AGGR_STATIC_LANE_CTRL
  #define IDSNVID_CMN_MEM_MBIST_AGGR_STATIC_LANE_CTRL   (0xD166) //Aggressor Static Lane Control
#endif

#ifndef IDSNVID_CMN_MEM_MBIST_AGGR_STATIC_LANE_SEL_U32
  #define IDSNVID_CMN_MEM_MBIST_AGGR_STATIC_LANE_SEL_U32   (0xD167) //Aggressor Static Lane Select Upper 32 bits
#endif

#ifndef IDSNVID_CMN_MEM_MBIST_AGGR_STATIC_LANE_SEL_L32
  #define IDSNVID_CMN_MEM_MBIST_AGGR_STATIC_LANE_SEL_L32   (0xD168) //Aggressor Static Lane Select Lower 32 Bits
#endif

#ifndef IDSNVID_CMN_MEM_MBIST_AGGR_STATIC_LANE_SEL_ECC
  #define IDSNVID_CMN_MEM_MBIST_AGGR_STATIC_LANE_SEL_ECC   (0xD169) //Aggressor Static Lane Select ECC
#endif

#ifndef IDSNVID_CMN_MEM_MBIST_AGGR_STATIC_LANE_VAL
  #define IDSNVID_CMN_MEM_MBIST_AGGR_STATIC_LANE_VAL   (0xD16A) //Aggressor Static Lane Value
#endif

#ifndef IDSNVID_CMN_MEM_MBIST_TGT_STATIC_LANE_CTRL
  #define IDSNVID_CMN_MEM_MBIST_TGT_STATIC_LANE_CTRL   (0xD16B) //Target Static Lane Control
#endif

#ifndef IDSNVID_CMN_MEM_MBIST_TGT_STATIC_LANE_SEL_U32
  #define IDSNVID_CMN_MEM_MBIST_TGT_STATIC_LANE_SEL_U32   (0xD16C) //Target Static Lane Select Upper 32 bit
#endif

#ifndef IDSNVID_CMN_MEM_MBIST_TGT_STATIC_LANE_SEL_L32
  #define IDSNVID_CMN_MEM_MBIST_TGT_STATIC_LANE_SEL_L32   (0xD16D) //Target Static Lane Select Lower 32 Bits
#endif

#ifndef IDSNVID_CMN_MEM_MBIST_TGT_STATIC_LANE_SEL_ECC
  #define IDSNVID_CMN_MEM_MBIST_TGT_STATIC_LANE_SEL_ECC   (0xD16E) //Target Static Lane Select ECC
#endif

#ifndef IDSNVID_CMN_MEM_MBIST_TGT_STATIC_LANE_VAL
  #define IDSNVID_CMN_MEM_MBIST_TGT_STATIC_LANE_VAL   (0xD16F) //Target Static Lane Value
#endif

#ifndef IDSNVID_CMN_MEM_MBIST_READ_DATA_EYE_VOLTAGE_STEP
  #define IDSNVID_CMN_MEM_MBIST_READ_DATA_EYE_VOLTAGE_STEP   (0xD170) //Read Voltage Sweep Step Size
#endif

#ifndef IDSNVID_CMN_MEM_MBIST_READ_DATA_EYE_TIMING_STEP
  #define IDSNVID_CMN_MEM_MBIST_READ_DATA_EYE_TIMING_STEP   (0xD171) //Read Timing Sweep Step Size
#endif

#ifndef IDSNVID_CMN_MEM_MBIST_WRITE_DATA_EYE_VOLTAGE_STEP
  #define IDSNVID_CMN_MEM_MBIST_WRITE_DATA_EYE_VOLTAGE_STEP   (0xD172) //Write Voltage Sweep Step Size
#endif

#ifndef IDSNVID_CMN_MEM_MBIST_WRITE_DATA_EYE_TIMING_STEP
  #define IDSNVID_CMN_MEM_MBIST_WRITE_DATA_EYE_TIMING_STEP   (0xD173) //Write Timing Sweep Step Size
#endif

#ifndef IDSNVID_CMN_MEM_MBIST_DATAEYE_SILENT_EXECUTION
  #define IDSNVID_CMN_MEM_MBIST_DATAEYE_SILENT_EXECUTION   (0xD174) //Silent Execution
#endif

#ifndef IDSNVID_CMN_MEM_DATA_POISONING_DDR
  #define IDSNVID_CMN_MEM_DATA_POISONING_DDR   (0xD175) //Data Poisoning
#endif

#ifndef IDSNVID_CMN_MEM_BOOT_TIME_POST_PACKAGE_REPAIR
  #define IDSNVID_CMN_MEM_BOOT_TIME_POST_PACKAGE_REPAIR   (0xD176) //DRAM Boot Time Post Package Repair
#endif

#ifndef IDSNVID_CMN_MEM_RUNTIME_POST_PACKAGE_REPAIR
  #define IDSNVID_CMN_MEM_RUNTIME_POST_PACKAGE_REPAIR   (0xD177) //DRAM Runtime Post Package Repair
#endif

#ifndef IDSNVID_CMN_MEM_POST_PACKAGE_REPAIR_CONFIG_INITIATOR
  #define IDSNVID_CMN_MEM_POST_PACKAGE_REPAIR_CONFIG_INITIATOR   (0xD178) //DRAM Post Package Repair Config Initiator
#endif

#ifndef IDSNVID_CMN_MEM_RCD_PARITY_DDR
  #define IDSNVID_CMN_MEM_RCD_PARITY_DDR   (0xD179) //RCD Parity
#endif

#ifndef IDSNVID_CMN_MEM_MAX_RCD_PARITY_ERROR_REPLAY_DDR
  #define IDSNVID_CMN_MEM_MAX_RCD_PARITY_ERROR_REPLAY_DDR   (0xD17A) //Max RCD Parity Error Replay
#endif

#ifndef IDSNVID_CMN_MEM_WRITE_CRC_DDR
  #define IDSNVID_CMN_MEM_WRITE_CRC_DDR   (0xD17B) //Write CRC
#endif

#ifndef IDSNVID_CMN_MEM_MAX_WRITE_CRC_ERROR_REPLAY_DDR
  #define IDSNVID_CMN_MEM_MAX_WRITE_CRC_ERROR_REPLAY_DDR   (0xD17C) //Max Write CRC Error Replay
#endif

#ifndef IDSNVID_CMN_MEM_READ_CRC_DDR
  #define IDSNVID_CMN_MEM_READ_CRC_DDR   (0xD17D) //Read CRC
#endif

#ifndef IDSNVID_CMN_MEM_MAX_READ_CRC_ERROR_REPLAY_DDR
  #define IDSNVID_CMN_MEM_MAX_READ_CRC_ERROR_REPLAY_DDR   (0xD17E) //Max Read CRC Error Replay
#endif

#ifndef IDSNVID_CMN_MEM_DIS_MEM_ERR_INJ
  #define IDSNVID_CMN_MEM_DIS_MEM_ERR_INJ   (0xD17F) //Memory Error Injection
#endif

#ifndef IDSNVID_CMN_MEM_ECS_STATUS_INTERRUPT_DDR
  #define IDSNVID_CMN_MEM_ECS_STATUS_INTERRUPT_DDR   (0xD180) //EcsStatus Interrupt
#endif

#ifndef IDSNVID_CMN_MEM_CORRECTED_ERROR_COUNTER_ENABLE
  #define IDSNVID_CMN_MEM_CORRECTED_ERROR_COUNTER_ENABLE   (0xD181) //DRAM Corrected Error Counter Enable
#endif

#ifndef IDSNVID_CMN_MEM_CORRECTED_ERROR_COUNTER_INTERRUPT_ENABLE
  #define IDSNVID_CMN_MEM_CORRECTED_ERROR_COUNTER_INTERRUPT_ENABLE   (0xD182) //DRAM Corrected Error Counter Interrupt Enable
#endif

#ifndef IDSNVID_CMN_MEM_CORRECTED_ERROR_COUNTER_LEAK_RATE
  #define IDSNVID_CMN_MEM_CORRECTED_ERROR_COUNTER_LEAK_RATE   (0xD183) //DRAM Corrected Error Counter Leak Rate
#endif

#ifndef IDSNVID_CMN_MEM_CORRECTED_ERROR_COUNTER_START_COUNT
  #define IDSNVID_CMN_MEM_CORRECTED_ERROR_COUNTER_START_COUNT   (0xD184) //DRAM Corrected Error Counter Start Count
#endif

#ifndef IDSNVID_CMN_MEM_DRAM_ECC_SYMBOL_SIZE_DDR
  #define IDSNVID_CMN_MEM_DRAM_ECC_SYMBOL_SIZE_DDR   (0xD185) //DRAM ECC Symbol Size
#endif

#ifndef IDSNVID_CMN_MEM_DRAM_ECC_EN_DDR
  #define IDSNVID_CMN_MEM_DRAM_ECC_EN_DDR   (0xD186) //DRAM ECC Enable
#endif

#ifndef IDSNVID_CMN_MEM_DRAM_UECC_RETRY_DDR
  #define IDSNVID_CMN_MEM_DRAM_UECC_RETRY_DDR   (0xD187) //DRAM UECC Retry
#endif

#ifndef IDSNVID_CMN_MEM_MAX_DRAM_UECC_ERROR_REPLAY_DDR
  #define IDSNVID_CMN_MEM_MAX_DRAM_UECC_ERROR_REPLAY_DDR   (0xD188) //Max DRAM UECC Error Replay
#endif

#ifndef IDSNVID_CMN_MEM_DRAM_MEM_CLR_DDR
  #define IDSNVID_CMN_MEM_DRAM_MEM_CLR_DDR   (0xD189) //Memory Clear
#endif

#ifndef IDSNVID_CMN_MEM_ADDR_XOR_AFTER_ECC
  #define IDSNVID_CMN_MEM_ADDR_XOR_AFTER_ECC   (0xD18A) //Address XOR after ECC
#endif

#ifndef IDSNVID_DBG_MEM_CIPHER_TEXT_HIDING
  #define IDSNVID_DBG_MEM_CIPHER_TEXT_HIDING   (0xD18B) //CipherText Hiding Enable
#endif

#ifndef IDSNVID_CMN_MEM_DRAM_ECS_MODE_DDR
  #define IDSNVID_CMN_MEM_DRAM_ECS_MODE_DDR   (0xD18C) //DRAM ECS Mode
#endif

#ifndef IDSNVID_CMN_MEM_DRAM_REDIRECT_SCRUB_EN_DDR
  #define IDSNVID_CMN_MEM_DRAM_REDIRECT_SCRUB_EN_DDR   (0xD18D) //DRAM Redirect Scrubber Enable
#endif

#ifndef IDSNVID_CMN_MEM_DRAM_REDIRECT_SCRUB_LIMIT_DDR
  #define IDSNVID_CMN_MEM_DRAM_REDIRECT_SCRUB_LIMIT_DDR   (0xD18E) //DRAM Scrub Redirection Limit
#endif

#ifndef IDSNVID_CMN_MEM_DRAM_SCRUB_TIME
  #define IDSNVID_CMN_MEM_DRAM_SCRUB_TIME   (0xD18F) //DRAM Scrub Time
#endif

#ifndef IDSNVID_CMN_MEMT_EC_SINT_CTRL_DDR
  #define IDSNVID_CMN_MEMT_EC_SINT_CTRL_DDR   (0xD190) //tECSint Ctrl
#endif

#ifndef IDSNVID_CMN_MEMT_EC_SINT_DDR
  #define IDSNVID_CMN_MEMT_EC_SINT_DDR   (0xD191) //tECSint
#endif

#ifndef IDSNVID_CMN_MEM_DRAM_ETC_DDR
  #define IDSNVID_CMN_MEM_DRAM_ETC_DDR   (0xD192) //DRAM Error Threshold Count
#endif

#ifndef IDSNVID_CMN_MEM_DRAM_ECS_COUNT_MODE_DDR
  #define IDSNVID_CMN_MEM_DRAM_ECS_COUNT_MODE_DDR   (0xD193) //DRAM ECS Count Mode
#endif

#ifndef IDSNVID_CMN_MEM_DRAM_AUTO_ECS_SELF_REFRESH_DDR
  #define IDSNVID_CMN_MEM_DRAM_AUTO_ECS_SELF_REFRESH_DDR   (0xD194) //DRAM AutoEcs during Self Refresh
#endif

#ifndef IDSNVID_CMN_MEM_DRAM_ECS_WRITEBACK_SUPPRESSION_DDR
  #define IDSNVID_CMN_MEM_DRAM_ECS_WRITEBACK_SUPPRESSION_DDR   (0xD195) //DRAM ECS WriteBack Suppression
#endif

#ifndef IDSNVID_CMN_MEM_DRAM_X4_WRITEBACK_SUPPRESSION_DDR
  #define IDSNVID_CMN_MEM_DRAM_X4_WRITEBACK_SUPPRESSION_DDR   (0xD196) //DRAM X4 WriteBack Suppression
#endif

#ifndef IDSNVID_CMN_MEM_ODT_IMPED_PROC_DDR
  #define IDSNVID_CMN_MEM_ODT_IMPED_PROC_DDR   (0xD197) //Processor ODT Pull Up Impedance
#endif

#ifndef IDSNVID_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR
  #define IDSNVID_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR   (0xD198) //Processor ODT Pull Down Impedance
#endif

#ifndef IDSNVID_CMN_MEM_DRAM_DRV_STREN_DQ_DDR
  #define IDSNVID_CMN_MEM_DRAM_DRV_STREN_DQ_DDR   (0xD199) //Dram DQ drive strengths
#endif

#ifndef IDSNVID_CMN_MEM_RTT_NOM_WR_P0_DDR
  #define IDSNVID_CMN_MEM_RTT_NOM_WR_P0_DDR   (0xD19A) //RTT_NOM_WR P-State 0
#endif

#ifndef IDSNVID_CMN_MEM_RTT_NOM_RD_P0_DDR
  #define IDSNVID_CMN_MEM_RTT_NOM_RD_P0_DDR   (0xD19B) //RTT_NOM_RD P-State 0
#endif

#ifndef IDSNVID_CMN_MEM_RTT_WR_P0_DDR
  #define IDSNVID_CMN_MEM_RTT_WR_P0_DDR   (0xD19C) //RTT_WR P-State 0
#endif

#ifndef IDSNVID_CMN_MEM_RTT_PARK_P0_DDR
  #define IDSNVID_CMN_MEM_RTT_PARK_P0_DDR   (0xD19D) //RTT_PARK P-State 0
#endif

#ifndef IDSNVID_CMN_MEM_RTT_PARK_DQS_P0_DDR
  #define IDSNVID_CMN_MEM_RTT_PARK_DQS_P0_DDR   (0xD19E) //DQS_RTT_PARK P-State 0
#endif

#ifndef IDSNVID_CMN_MEM_RTT_NOM_WR_P1_DDR
  #define IDSNVID_CMN_MEM_RTT_NOM_WR_P1_DDR   (0xD19F) //RTT_NOM_WR P-State 1
#endif

#ifndef IDSNVID_CMN_MEM_RTT_NOM_RD_P1_DDR
  #define IDSNVID_CMN_MEM_RTT_NOM_RD_P1_DDR   (0xD1A0) //RTT_NOM_RD P-State 1
#endif

#ifndef IDSNVID_CMN_MEM_RTT_WR_P1_DDR
  #define IDSNVID_CMN_MEM_RTT_WR_P1_DDR   (0xD1A1) //RTT_WR P-State 1
#endif

#ifndef IDSNVID_CMN_MEM_RTT_PARK_P1_DDR
  #define IDSNVID_CMN_MEM_RTT_PARK_P1_DDR   (0xD1A2) //RTT_PARK P-State 1
#endif

#ifndef IDSNVID_CMN_MEM_RTT_PARK_DQS_P1_DDR
  #define IDSNVID_CMN_MEM_RTT_PARK_DQS_P1_DDR   (0xD1A3) //DQS_RTT_PARK P-State 1
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_LEGAL_DISCLAIMER
  #define IDSNVID_CMN_MEM_TIMING_LEGAL_DISCLAIMER   (0xD1A4) //DRAM Timing Configuration Legal Disclaimer
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_LEGAL_DISCLAIMER1
  #define IDSNVID_CMN_MEM_TIMING_LEGAL_DISCLAIMER1   (0xD1A5) //DRAM Timing Configuration Legal Disclaimer 1
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_SETTING_DDR
  #define IDSNVID_CMN_MEM_TIMING_SETTING_DDR   (0xD1A6) //Active Memory Timing Settings
#endif

#ifndef IDSNVID_CMN_MEM_TARGET_SPEED_DDR
  #define IDSNVID_CMN_MEM_TARGET_SPEED_DDR   (0xD1A7) //Memory Target Speed
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TCL_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TCL_CTRL_DDR   (0xD1A8) //Tcl Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TCL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TCL_DDR   (0xD1A9) //Tcl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRCD_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRCD_CTRL_DDR   (0xD1AA) //Trcd Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRCD_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRCD_DDR   (0xD1AB) //Trcd
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRP_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRP_CTRL_DDR   (0xD1AC) //Trp Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRP_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRP_DDR   (0xD1AD) //Trp
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRAS_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRAS_CTRL_DDR   (0xD1AE) //Tras Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRAS_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRAS_DDR   (0xD1AF) //Tras
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRC_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRC_CTRL_DDR   (0xD1B0) //Trc Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRC_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRC_DDR   (0xD1B1) //Trc
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TWR_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TWR_CTRL_DDR   (0xD1B2) //Twr Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TWR_DDR
  #define IDSNVID_CMN_MEM_TIMING_TWR_DDR   (0xD1B3) //Twr
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRFC1_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRFC1_CTRL_DDR   (0xD1B4) //Trfc1 Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRFC1_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRFC1_DDR   (0xD1B5) //Trfc1
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRFC2_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRFC2_CTRL_DDR   (0xD1B6) //Trfc2 Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRFC2_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRFC2_DDR   (0xD1B7) //Trfc2
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRFC_SB_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRFC_SB_CTRL_DDR   (0xD1B8) //TrfcSb Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRFC_SB_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRFC_SB_DDR   (0xD1B9) //TrfcSb
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TCWL_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TCWL_CTRL_DDR   (0xD1BA) //Tcwl Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TCWL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TCWL_DDR   (0xD1BB) //Tcwl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRTP_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRTP_CTRL_DDR   (0xD1BC) //Trtp Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRTP_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRTP_DDR   (0xD1BD) //Trtp
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRRD_L_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRRD_L_CTRL_DDR   (0xD1BE) //TrrdL Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRRD_L_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRRD_L_DDR   (0xD1BF) //TrrdL
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRRD_S_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRRD_S_CTRL_DDR   (0xD1C0) //TrrdS Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRRD_S_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRRD_S_DDR   (0xD1C1) //TrrdS
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TFAW_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TFAW_CTRL_DDR   (0xD1C2) //Tfaw Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TFAW_DDR
  #define IDSNVID_CMN_MEM_TIMING_TFAW_DDR   (0xD1C3) //Tfaw
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TWTR_L_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TWTR_L_CTRL_DDR   (0xD1C4) //TwtrL Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TWTR_L_DDR
  #define IDSNVID_CMN_MEM_TIMING_TWTR_L_DDR   (0xD1C5) //TwtrL
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TWTR_S_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TWTR_S_CTRL_DDR   (0xD1C6) //TwtrS Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TWTR_S_DDR
  #define IDSNVID_CMN_MEM_TIMING_TWTR_S_DDR   (0xD1C7) //TwtrS
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRDRD_SC_L_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRDRD_SC_L_CTRL_DDR   (0xD1C8) //TrdrdScL Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRDRD_SC_L_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRDRD_SC_L_DDR   (0xD1C9) //TrdrdScL
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRDRD_SC_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRDRD_SC_CTRL_DDR   (0xD1CA) //TrdrdSc Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRDRD_SC_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRDRD_SC_DDR   (0xD1CB) //TrdrdSc
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRDRD_SD_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRDRD_SD_CTRL_DDR   (0xD1CC) //TrdrdSd Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRDRD_SD_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRDRD_SD_DDR   (0xD1CD) //TrdrdSd
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRDRD_DD_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRDRD_DD_CTRL_DDR   (0xD1CE) //TrdrdDd Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRDRD_DD_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRDRD_DD_DDR   (0xD1CF) //TrdrdDd
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TWRWR_SC_L_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TWRWR_SC_L_CTRL_DDR   (0xD1D0) //TwrwrScL Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TWRWR_SC_L_DDR
  #define IDSNVID_CMN_MEM_TIMING_TWRWR_SC_L_DDR   (0xD1D1) //TwrwrScL
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TWRWR_SC_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TWRWR_SC_CTRL_DDR   (0xD1D2) //TwrwrSc Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TWRWR_SC_DDR
  #define IDSNVID_CMN_MEM_TIMING_TWRWR_SC_DDR   (0xD1D3) //TwrwrSc
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TWRWR_SD_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TWRWR_SD_CTRL_DDR   (0xD1D4) //TwrwrSd Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TWRWR_SD_DDR
  #define IDSNVID_CMN_MEM_TIMING_TWRWR_SD_DDR   (0xD1D5) //TwrwrSd
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TWRWR_DD_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TWRWR_DD_CTRL_DDR   (0xD1D6) //TwrwrDd Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TWRWR_DD_DDR
  #define IDSNVID_CMN_MEM_TIMING_TWRWR_DD_DDR   (0xD1D7) //TwrwrDd
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TWRRD_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TWRRD_CTRL_DDR   (0xD1D8) //Twrrd Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TWRRD_DDR
  #define IDSNVID_CMN_MEM_TIMING_TWRRD_DDR   (0xD1D9) //Twrrd
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRDWR_CTRL_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRDWR_CTRL_DDR   (0xD1DA) //Trdwr Ctrl
#endif

#ifndef IDSNVID_CMN_MEM_TIMING_TRDWR_DDR
  #define IDSNVID_CMN_MEM_TIMING_TRDWR_DDR   (0xD1DB) //Trdwr
#endif

#ifndef IDSNVID_CMN_MEM_DRAM_PDA_ENUM_ID_PROG_MODE_DDR
  #define IDSNVID_CMN_MEM_DRAM_PDA_ENUM_ID_PROG_MODE_DDR   (0xD1DC) //DRAM PDA Enumerate ID Programming Mode
#endif

#ifndef IDSNVID_CMN_MEM_WRITE_TRAINING_BURST_LENGTH
  #define IDSNVID_CMN_MEM_WRITE_TRAINING_BURST_LENGTH   (0xD1DD) //Write Training Burst Length
#endif

#ifndef IDSNVID_CMN_TRAINING_RETRY_COUNT
  #define IDSNVID_CMN_TRAINING_RETRY_COUNT   (0xD1DE) //Training Retry Count
#endif

#ifndef IDSNVID_CMN_MEM_PERIODIC_TRAINING_MODE_DDR
  #define IDSNVID_CMN_MEM_PERIODIC_TRAINING_MODE_DDR   (0xD1DF) //Periodic Training Mode
#endif

#ifndef IDSNVID_CMN_MEM_PERIODIC_INTERVAL_MODE
  #define IDSNVID_CMN_MEM_PERIODIC_INTERVAL_MODE   (0xD1E0) //Periodic Interval Mode
#endif

#ifndef IDSNVID_CMN_MEM_PERIODIC_INTERVAL
  #define IDSNVID_CMN_MEM_PERIODIC_INTERVAL   (0xD1E1) //Periodic Interval
#endif

#ifndef IDSNVID_CMN_MEM_TSME_ENABLE_DDR
  #define IDSNVID_CMN_MEM_TSME_ENABLE_DDR   (0xD1E2) //TSME
#endif

#ifndef IDSNVID_CMN_MEM_AES
  #define IDSNVID_CMN_MEM_AES   (0xD1E3) //AES
#endif

#ifndef IDSNVID_CMN_MEM_DATA_SCRAMBLE
  #define IDSNVID_CMN_MEM_DATA_SCRAMBLE   (0xD1E4) //Data Scramble
#endif

#ifndef IDSNVID_CMN_MEM_SME_MK_ENABLE
  #define IDSNVID_CMN_MEM_SME_MK_ENABLE   (0xD1E5) //SME-MK
#endif

#ifndef IDSNVID_CMN_PMIC_ERROR_REPORTING
  #define IDSNVID_CMN_PMIC_ERROR_REPORTING   (0xD1E6) //PMIC Error Reporting
#endif

#ifndef IDSNVID_CMN_MEM_CTRLLER_PMIC_OP_MODE
  #define IDSNVID_CMN_MEM_CTRLLER_PMIC_OP_MODE   (0xD1E7) //PMIC Operation Mode
#endif

#ifndef IDSNVID_CMN_MEM_CTRLLER_PMIC_FAULT_RECOVERY
  #define IDSNVID_CMN_MEM_CTRLLER_PMIC_FAULT_RECOVERY   (0xD1E8) //PMIC Fault Recovery
#endif

#ifndef IDSNVID_CMN_MEM_CTRLLER_PMIC_SWA_SWB_VDD_CORE
  #define IDSNVID_CMN_MEM_CTRLLER_PMIC_SWA_SWB_VDD_CORE   (0xD1E9) //PMIC SWA/SWB VDD Core
#endif

#ifndef IDSNVID_CMN_MEM_CTRLLER_PMIC_SWC_VDDIO
  #define IDSNVID_CMN_MEM_CTRLLER_PMIC_SWC_VDDIO   (0xD1EA) //PMIC SWC VDDIO
#endif

#ifndef IDSNVID_CMN_MEM_CTRLLER_PMIC_SWD_VPP
  #define IDSNVID_CMN_MEM_CTRLLER_PMIC_SWD_VPP   (0xD1EB) //PMIC SWD VPP
#endif

#ifndef IDSNVID_CMN_MEM_CTRLLER_PMIC_STAGGER_DELAY
  #define IDSNVID_CMN_MEM_CTRLLER_PMIC_STAGGER_DELAY   (0xD1EC) //PMIC Stagger Delay
#endif

#ifndef IDSNVID_CMN_MEM_CTRLLER_MAX_PMIC_POWER_ON
  #define IDSNVID_CMN_MEM_CTRLLER_MAX_PMIC_POWER_ON   (0xD1ED) //Max PMIC Power On
#endif

#ifndef IDSNVID_CMN_MEM_ODTS_CMD_THROTTLE_CYCLE_CTL_DDR
  #define IDSNVID_CMN_MEM_ODTS_CMD_THROTTLE_CYCLE_CTL_DDR   (0xD1EE) //ODTS Thermal Throttle Control
#endif

#ifndef IDSNVID_CMN_MEM_ODTS_CMD_THROTTLE_THRESHOLD_DDR
  #define IDSNVID_CMN_MEM_ODTS_CMD_THROTTLE_THRESHOLD_DDR   (0xD1EF) //ODTS Thermal Throttle Threshold
#endif

#ifndef IDSNVID_CMN_TSOD_THERMAL_THROTTLE_CONTROL_DDR
  #define IDSNVID_CMN_TSOD_THERMAL_THROTTLE_CONTROL_DDR   (0xD1F0) //TSOD Thermal Throttle Control
#endif

#ifndef IDSNVID_CMN_TSOD_THERMAL_THROTTLE_START_TEMP_DDR
  #define IDSNVID_CMN_TSOD_THERMAL_THROTTLE_START_TEMP_DDR   (0xD1F1) //TSOD Thermal Throttle Start Temperature
#endif

#ifndef IDSNVID_CMN_TSOD_THERMAL_THROTTLE_HYSTERESIS_DDR
  #define IDSNVID_CMN_TSOD_THERMAL_THROTTLE_HYSTERESIS_DDR   (0xD1F2) //TSOD Thermal Throttle Hysteresis
#endif

#ifndef IDSNVID_CMN_TSOD_CMD_THROTTLE_PERCENTAGE0_DDR
  #define IDSNVID_CMN_TSOD_CMD_THROTTLE_PERCENTAGE0_DDR   (0xD1F3) //TSOD Command Throttle Percentage (Threshold)
#endif

#ifndef IDSNVID_CMN_TSOD_CMD_THROTTLE_PERCENTAGE5_DDR
  #define IDSNVID_CMN_TSOD_CMD_THROTTLE_PERCENTAGE5_DDR   (0xD1F4) //TSOD Command Throttle Percentage (Threshold+5C)
#endif

#ifndef IDSNVID_CMN_TSOD_CMD_THROTTLE_PERCENTAGE10_DDR
  #define IDSNVID_CMN_TSOD_CMD_THROTTLE_PERCENTAGE10_DDR   (0xD1F5) //TSOD Command Throttle Percentage (Threshold+10C)
#endif

#ifndef IDSNVID_CMN_GNB_PCIE_LOOP_BACK_MODE
  #define IDSNVID_CMN_GNB_PCIE_LOOP_BACK_MODE   (0xD1F6) //PCIe loopback Mode
#endif

#ifndef IDSNVID_ENABLE2_SPC_GEN4
  #define IDSNVID_ENABLE2_SPC_GEN4   (0xD1F7) //Enable 2 SPC (Gen 4)
#endif

#ifndef IDSNVID_ENABLE2_SPC_GEN5
  #define IDSNVID_ENABLE2_SPC_GEN5   (0xD1F8) //Enable 2 SPC (Gen 5)
#endif

#ifndef IDSNVID_GNB_SAFE_RECOVERY_UPON_ABER_EXCEEDED_ERROR
  #define IDSNVID_GNB_SAFE_RECOVERY_UPON_ABER_EXCEEDED_ERROR   (0xD1F9) //Safe recovery upon a BERExceeded Error
#endif

#ifndef IDSNVID_GNB_PERIODIC_CALIBRATION
  #define IDSNVID_GNB_PERIODIC_CALIBRATION   (0xD1FA) //Periodic Calibration
#endif

#ifndef IDSNVID_CMN_TDP_CTL
  #define IDSNVID_CMN_TDP_CTL   (0xD1FB) //TDP Control
#endif

#ifndef IDSNVID_CMN_TDP_LIMIT
  #define IDSNVID_CMN_TDP_LIMIT   (0xD1FC) //TDP
#endif

#ifndef IDSNVID_CMN_PPT_CTL
  #define IDSNVID_CMN_PPT_CTL   (0xD1FD) //PPT Control
#endif

#ifndef IDSNVID_CMN_PPT_LIMIT
  #define IDSNVID_CMN_PPT_LIMIT   (0xD1FE) //PPT
#endif

#ifndef IDSNVID_CMN_DETERMINISM_CTL
  #define IDSNVID_CMN_DETERMINISM_CTL   (0xD1FF) //Determinism Control
#endif

#ifndef IDSNVID_CMN_DETERMINISM_ENABLE
  #define IDSNVID_CMN_DETERMINISM_ENABLE   (0xD200) //Determinism Enable
#endif

#ifndef IDSNVID_CMNX_GMI_LINK_WIDTH_CTL
  #define IDSNVID_CMNX_GMI_LINK_WIDTH_CTL   (0xD201) //xGMI Link Width Control
#endif

#ifndef IDSNVID_CMNX_GMI_FORCE_LINK_WIDTH_CTL
  #define IDSNVID_CMNX_GMI_FORCE_LINK_WIDTH_CTL   (0xD202) //xGMI Force Link Width Control
#endif

#ifndef IDSNVID_CMNX_GMI_FORCE_LINK_WIDTH
  #define IDSNVID_CMNX_GMI_FORCE_LINK_WIDTH   (0xD203) //xGMI Force Link Width
#endif

#ifndef IDSNVID_CMNX_GMI_MAX_LINK_WIDTH_CTL
  #define IDSNVID_CMNX_GMI_MAX_LINK_WIDTH_CTL   (0xD204) //xGMI Max Link Width Range Control
#endif

#ifndef IDSNVID_CMNX_GMI_MAX_LINK_WIDTH
  #define IDSNVID_CMNX_GMI_MAX_LINK_WIDTH   (0xD205) //xGMI Max Link Width
#endif

#ifndef IDSNVID_CMNX_GMI_MIN_LINK_WIDTH
  #define IDSNVID_CMNX_GMI_MIN_LINK_WIDTH   (0xD206) //xGMI Min Link Width
#endif

#ifndef IDSNVID_CMN_APBDIS
  #define IDSNVID_CMN_APBDIS   (0xD207) //APBDIS
#endif

#ifndef IDSNVID_CMN_APBDIS_DF_PSTATE
  #define IDSNVID_CMN_APBDIS_DF_PSTATE   (0xD208) //DfPstate
#endif

#ifndef IDSNVID_CMN_EFFICIENCY_MODE_EN
  #define IDSNVID_CMN_EFFICIENCY_MODE_EN   (0xD209) //Power Profile Selection
#endif

#ifndef IDSNVID_CMN_XGMI_PSTATE_CONTROL
  #define IDSNVID_CMN_XGMI_PSTATE_CONTROL   (0xD20A) //xGMI Pstate Control
#endif

#ifndef IDSNVID_CMN_XGMI_PSTATE_SELECTION
  #define IDSNVID_CMN_XGMI_PSTATE_SELECTION   (0xD20B) //xGMI Pstate Selection
#endif

#ifndef IDSNVID_CMN_BOOST_FMAX_EN
  #define IDSNVID_CMN_BOOST_FMAX_EN   (0xD20C) //BoostFmaxEn
#endif

#ifndef IDSNVID_CMN_BOOST_FMAX
  #define IDSNVID_CMN_BOOST_FMAX   (0xD20D) //BoostFmax
#endif

#ifndef IDSNVID_CMN_GNB_SMU_DFFO
  #define IDSNVID_CMN_GNB_SMU_DFFO   (0xD20E) //DF PState Frequency Optimizer
#endif

#ifndef IDSNVID_CMN_GNB_SMU_DF_CSTATES
  #define IDSNVID_CMN_GNB_SMU_DF_CSTATES   (0xD20F) //DF Cstates
#endif

#ifndef IDSNVID_CMN_GNB_SMU_CPPC
  #define IDSNVID_CMN_GNB_SMU_CPPC   (0xD210) //CPPC
#endif

#ifndef IDSNVID_CMN_GNB_SMU_HSMP_SUPPORT
  #define IDSNVID_CMN_GNB_SMU_HSMP_SUPPORT   (0xD211) //HSMP Support
#endif

#ifndef IDSNVID_CMN_SVI3_SVC_SPEED_CTL
  #define IDSNVID_CMN_SVI3_SVC_SPEED_CTL   (0xD212) //SVI3 SVC Speed Control
#endif

#ifndef IDSNVID_CMN_SVI3_SVC_SPEED
  #define IDSNVID_CMN_SVI3_SVC_SPEED   (0xD213) //SVI3 SVC Speed
#endif

#ifndef IDSNVID_CMN_X3D_STACK_OVERRIDE
  #define IDSNVID_CMN_X3D_STACK_OVERRIDE   (0xD214) //3D V-Cache
#endif

#ifndef IDSNVID_CMN_L3_BIST
  #define IDSNVID_CMN_L3_BIST   (0xD215) //L3 BIST
#endif

#ifndef IDSNVID_CMN_GNB_DIAG_MODE
  #define IDSNVID_CMN_GNB_DIAG_MODE   (0xD216) //Diagnostic Mode
#endif

#ifndef IDSNVID_CMN_GNB_SMU_GMI_FOLDING
  #define IDSNVID_CMN_GNB_SMU_GMI_FOLDING   (0xD217) //GMI Folding
#endif

#ifndef IDSNVID_CMN_THROTTLER_MODE
  #define IDSNVID_CMN_THROTTLER_MODE   (0xD218) //Separate CPU power plane throttling
#endif

#ifndef IDSNVID_CMN_DF_PSTATE_RANGE_CTL
  #define IDSNVID_CMN_DF_PSTATE_RANGE_CTL   (0xD219) //DfPstate Range Control
#endif

#ifndef IDSNVID_CMN_DF_PSTATE_MAX
  #define IDSNVID_CMN_DF_PSTATE_MAX   (0xD21A) //DfPstate Max Index
#endif

#ifndef IDSNVID_CMN_DF_PSTATE_MIN
  #define IDSNVID_CMN_DF_PSTATE_MIN   (0xD21B) //DfPstate Min Index
#endif

#ifndef IDSNVID_CMN_RAS_CONTROL
  #define IDSNVID_CMN_RAS_CONTROL   (0xD21C) //NBIO RAS Control
#endif

#ifndef IDSNVID_CMN_NBIO_SYNC_FLOOD_GEN
  #define IDSNVID_CMN_NBIO_SYNC_FLOOD_GEN   (0xD21D) //NBIO SyncFlood Generation
#endif

#ifndef IDSNVID_PCD_SYNC_FLOOD_TO_APML
  #define IDSNVID_PCD_SYNC_FLOOD_TO_APML   (0xD21E) //NBIO SyncFlood Reporting
#endif

#ifndef IDSNVID_CMN_GNB_AMD_PCIE_AER_REPORT_MECHANISM
  #define IDSNVID_CMN_GNB_AMD_PCIE_AER_REPORT_MECHANISM   (0xD21F) //PCIe Aer Reporting Mechanism
#endif

#ifndef IDSNVID_EDPC_CONTROL
  #define IDSNVID_EDPC_CONTROL   (0xD220) //Edpc Control
#endif

#ifndef IDSNVID_ACS_RAS_VALUE
  #define IDSNVID_ACS_RAS_VALUE   (0xD221) //ACS RAS Request Value
#endif

#ifndef IDSNVID_CMN_POISON_CONSUMPTION
  #define IDSNVID_CMN_POISON_CONSUMPTION   (0xD222) //NBIO Poison Consumption
#endif

#ifndef IDSNVID_CMN_GNB_RAS_SYNCFLOOD_PCIE_FATAL_ERROR
  #define IDSNVID_CMN_GNB_RAS_SYNCFLOOD_PCIE_FATAL_ERROR   (0xD223) //Sync Flood on PCIe Fatal Error
#endif

#ifndef IDSNVID_CMN_RAS_NUMERICAL_COMMON_OPTIONS
  #define IDSNVID_CMN_RAS_NUMERICAL_COMMON_OPTIONS   (0xD224) //NBIO RAS Numerical Common Options
#endif

#ifndef IDSNVID_PCD_EGRESS_POISON_SEVERITY_HI
  #define IDSNVID_PCD_EGRESS_POISON_SEVERITY_HI   (0xD225) //Egress Poison Severity High
#endif

#ifndef IDSNVID_PCD_EGRESS_POISON_SEVERITY_LO
  #define IDSNVID_PCD_EGRESS_POISON_SEVERITY_LO   (0xD226) //Egress Poison Severity Low
#endif

#ifndef IDSNVID_PCD_AMD_NBIO_EGRESS_POISON_MASK_HI
  #define IDSNVID_PCD_AMD_NBIO_EGRESS_POISON_MASK_HI   (0xD227) //Egress Poison Mask High
#endif

#ifndef IDSNVID_PCD_AMD_NBIO_EGRESS_POISON_MASK_LO
  #define IDSNVID_PCD_AMD_NBIO_EGRESS_POISON_MASK_LO   (0xD228) //Egress Poison Mask Low
#endif

#ifndef IDSNVID_PCD_AMD_NBIO_RAS_UCP_MASK_HI
  #define IDSNVID_PCD_AMD_NBIO_RAS_UCP_MASK_HI   (0xD229) //Uncorrected Converted to Poison Enable Mask High
#endif

#ifndef IDSNVID_PCD_AMD_NBIO_RAS_UCP_MASK_LO
  #define IDSNVID_PCD_AMD_NBIO_RAS_UCP_MASK_LO   (0xD22A) //Uncorrected Converted to Poison Enable Mask Low
#endif

#ifndef IDSNVID_PCD_SYSHUB_WDT_TIMER_INTERVAL
  #define IDSNVID_PCD_SYSHUB_WDT_TIMER_INTERVAL   (0xD22B) //System Hub Watchdog Timer
#endif

#ifndef IDSNVID_CMN_GNB_DATA_OBJECT_EXCHANGE
  #define IDSNVID_CMN_GNB_DATA_OBJECT_EXCHANGE   (0xD22C) //Data Object Exchange
#endif

#ifndef IDSNVID_CMN_GNB_RTM_MARGINING_SUPPORT
  #define IDSNVID_CMN_GNB_RTM_MARGINING_SUPPORT   (0xD22D) //RTM Margining Support
#endif

#ifndef IDSNVID_CMN_NBIO_FORCE_SPEED_LAST_ADVERTISED
  #define IDSNVID_CMN_NBIO_FORCE_SPEED_LAST_ADVERTISED   (0xD22E) //Multi Auto Speed Change On Last Rate
#endif

#ifndef IDSNVID_CMN_LC_MULT_UPSTREAM_AUTO
  #define IDSNVID_CMN_LC_MULT_UPSTREAM_AUTO   (0xD22F) //Multi Upstream Auto Speed Change
#endif

#ifndef IDSNVID_STRAP_COMPLIANCE_DIS
  #define IDSNVID_STRAP_COMPLIANCE_DIS   (0xD230) //Allow Compliance
#endif

#ifndef IDSNVID_CMN_NBIO_PCIE_ADVERTISE_EQ_TO_HIGH_RATE_SUPPORT
  #define IDSNVID_CMN_NBIO_PCIE_ADVERTISE_EQ_TO_HIGH_RATE_SUPPORT   (0xD231) //EQ Bypass To Highest Rate
#endif

#ifndef IDSNVID_CMN_GNB_DATA_LINK_FEATURE_CAP
  #define IDSNVID_CMN_GNB_DATA_LINK_FEATURE_CAP   (0xD232) //Data Link Feature Cap
#endif

#ifndef IDSNVID_CMN_GNB_DATA_LINK_FEATURE_EXCHANGE
  #define IDSNVID_CMN_GNB_DATA_LINK_FEATURE_EXCHANGE   (0xD233) //Data Link Feature Exchange
#endif

#ifndef IDSNVID_CMN_GNB_SRIS
  #define IDSNVID_CMN_GNB_SRIS   (0xD234) //SRIS
#endif

#ifndef IDSNVID_DBG_GNB_DBG_ACS_ENABLE
  #define IDSNVID_DBG_GNB_DBG_ACS_ENABLE   (0xD235) //ACS Enable
#endif

#ifndef IDSNVID_GNB_CMN_PCIE_TBT_SUPPORT
  #define IDSNVID_GNB_CMN_PCIE_TBT_SUPPORT   (0xD236) //PCIe Ten Bit Tag Support
#endif

#ifndef IDSNVID_GNB_CMN_PCIE_ARI_ENUMERATION
  #define IDSNVID_GNB_CMN_PCIE_ARI_ENUMERATION   (0xD237) //PCIe ARI Enumeration
#endif

#ifndef IDSNVID_CMN_GNB_PCIE_ARI_SUPPORT
  #define IDSNVID_CMN_GNB_PCIE_ARI_SUPPORT   (0xD238) //PCIe ARI Support
#endif

#ifndef IDSNVID_PRESENCE_DETECT_SELECTMODE
  #define IDSNVID_PRESENCE_DETECT_SELECTMODE   (0xD239) //Presence Detect Select mode
#endif

#ifndef IDSNVID_HOT_PLUG_HANDLING_MODE
  #define IDSNVID_HOT_PLUG_HANDLING_MODE   (0xD23A) //Hot Plug Handling mode
#endif

#ifndef IDSNVID_HOT_PLUG_PD_SETTLE
  #define IDSNVID_HOT_PLUG_PD_SETTLE   (0xD23B) //Presence Detect State Settle Time
#endif

#ifndef IDSNVID_HOT_PLUG_SETTLE_TIME
  #define IDSNVID_HOT_PLUG_SETTLE_TIME   (0xD23C) //Hot Plug Port Settle Time
#endif

#ifndef IDSNVID_HOTPLUG_SUPPORT
  #define IDSNVID_HOTPLUG_SUPPORT   (0xD23D) //Hotplug Support
#endif

#ifndef IDSNVID_CMN_EARLY_LINK_SPEED
  #define IDSNVID_CMN_EARLY_LINK_SPEED   (0xD23E) //Early Link Speed
#endif

#ifndef IDSNVID_DBG_GNB_DBG_AERCAP_ENABLE
  #define IDSNVID_DBG_GNB_DBG_AERCAP_ENABLE   (0xD23F) //Enable AER Cap
#endif

#ifndef IDSNVID_CMN_PCIE_CAP_LINK_SPEED
  #define IDSNVID_CMN_PCIE_CAP_LINK_SPEED   (0xD240) //PCIE Link Speed Capability
#endif

#ifndef IDSNVID_CMN_PCIE_TARGET_LINK_SPEED
  #define IDSNVID_CMN_PCIE_TARGET_LINK_SPEED   (0xD241) //PCIE Target Link Speed
#endif

#ifndef IDSNVID_CMN_ALL_PORTS_ASPM
  #define IDSNVID_CMN_ALL_PORTS_ASPM   (0xD242) //ASPM Control
#endif

#ifndef IDSNVID_CMN_NBIO_MCTP_EN
  #define IDSNVID_CMN_NBIO_MCTP_EN   (0xD243) //MCTP Enable
#endif

#ifndef IDSNVID_CMN_NBIO_MCTP_MODE
  #define IDSNVID_CMN_NBIO_MCTP_MODE   (0xD244) //MCTP Mode
#endif

#ifndef IDSNVID_CMN_NBIO_MCTP_DISCOVERY_NOTIFY_MESSAGE
  #define IDSNVID_CMN_NBIO_MCTP_DISCOVERY_NOTIFY_MESSAGE   (0xD245) //MCTP discovery notify message
#endif

#ifndef IDSNVID_CMN_NBIO_PCIE_NON_PCIE_COMPLIANT_SUPPORT
  #define IDSNVID_CMN_NBIO_PCIE_NON_PCIE_COMPLIANT_SUPPORT   (0xD246) //Non-PCIe Compliant Support
#endif

#ifndef IDSNVID_CMN_LIMIT_HP_DEVICES_TO_PCIE_BOOT_SPEED
  #define IDSNVID_CMN_LIMIT_HP_DEVICES_TO_PCIE_BOOT_SPEED   (0xD247) //Limit hotplug devices to PCIe boot speed
#endif

#ifndef IDSNVID_CMN_PC_IE_SFI_CONFIGVIA_OOB_EN
  #define IDSNVID_CMN_PC_IE_SFI_CONFIGVIA_OOB_EN   (0xD248) //Enable PCIe SFI Config via OOB
#endif

#ifndef IDSNVID_CMN_NBIO_PCIE_IDLE_POWER_SETTING
  #define IDSNVID_CMN_NBIO_PCIE_IDLE_POWER_SETTING   (0xD249) //PCIE Idle Power Setting
#endif

#ifndef IDSNVID_CFG_ACS_EN_RCC_DEV0
  #define IDSNVID_CFG_ACS_EN_RCC_DEV0   (0xD24A) //ACS Rcc_Dev0
#endif

#ifndef IDSNVID_CFG_AER_EN_RCC_DEV0
  #define IDSNVID_CFG_AER_EN_RCC_DEV0   (0xD24B) //AER Rcc_Dev0
#endif

#ifndef IDSNVID_CFG_DLF_EN_STRAP1
  #define IDSNVID_CFG_DLF_EN_STRAP1   (0xD24C) //DlfEnableStrap1
#endif

#ifndef IDSNVID_CFG_PHY16GT_STRAP1
  #define IDSNVID_CFG_PHY16GT_STRAP1   (0xD24D) //Phy16GTStrap1
#endif

#ifndef IDSNVID_CFG_MARGIN_EN_STRAP1
  #define IDSNVID_CFG_MARGIN_EN_STRAP1   (0xD24E) //MarginEnStrap1
#endif

#ifndef IDSNVID_CFG_ACS_SOURCE_VAL_STRAP5
  #define IDSNVID_CFG_ACS_SOURCE_VAL_STRAP5   (0xD24F) //SourceValStrap5
#endif

#ifndef IDSNVID_CFG_ACS_TRANSLATIONAL_BLOCKING_STRAP5
  #define IDSNVID_CFG_ACS_TRANSLATIONAL_BLOCKING_STRAP5   (0xD250) //TranslationalBlockingStrap5
#endif

#ifndef IDSNVID_CFG_ACS_P2P_REQ
  #define IDSNVID_CFG_ACS_P2P_REQ   (0xD251) //P2pReq ACS Control
#endif

#ifndef IDSNVID_CFG_ACS_P2P_COMP_STRAP5
  #define IDSNVID_CFG_ACS_P2P_COMP_STRAP5   (0xD252) //P2pCompStrap5
#endif

#ifndef IDSNVID_CFG_ACS_UPSTREAM_FWD_STRAP5
  #define IDSNVID_CFG_ACS_UPSTREAM_FWD_STRAP5   (0xD253) //UpstreamFwdStrap5
#endif

#ifndef IDSNVID_CFG_ACS_P2_P_EGRESS_STRAP5
  #define IDSNVID_CFG_ACS_P2_P_EGRESS_STRAP5   (0xD254) //P2PEgressStrap5
#endif

#ifndef IDSNVID_CFG_ACS_DIRECT_TRANSLATED_STRAP5
  #define IDSNVID_CFG_ACS_DIRECT_TRANSLATED_STRAP5   (0xD255) //DirectTranslatedStrap5
#endif

#ifndef IDSNVID_CFG_ACS_SSID_EN_STRAP5
  #define IDSNVID_CFG_ACS_SSID_EN_STRAP5   (0xD256) //SsidEnStrap5
#endif

#ifndef IDSNVID_CFG_PRI_EN_PAGE_REQ
  #define IDSNVID_CFG_PRI_EN_PAGE_REQ   (0xD257) //PriEnPageReq
#endif

#ifndef IDSNVID_CFG_PRI_RESET_PAGE_REQ
  #define IDSNVID_CFG_PRI_RESET_PAGE_REQ   (0xD258) //PriResetPageReq
#endif

#ifndef IDSNVID_CFG_ACS_SOURCE_VAL
  #define IDSNVID_CFG_ACS_SOURCE_VAL   (0xD259) //SourceVal ACS cntl
#endif

#ifndef IDSNVID_CFG_ACS_TRANSLATIONAL_BLOCKING
  #define IDSNVID_CFG_ACS_TRANSLATIONAL_BLOCKING   (0xD25A) //TranslationalBlocking ACS Control
#endif

#ifndef IDSNVID_CFG_ACS_P2P_COMP
  #define IDSNVID_CFG_ACS_P2P_COMP   (0xD25B) //P2pComp ACS Control
#endif

#ifndef IDSNVID_CFG_ACS_UPSTREAM_FWD
  #define IDSNVID_CFG_ACS_UPSTREAM_FWD   (0xD25C) //UpstreamFwd ACS Control
#endif

#ifndef IDSNVID_CFG_ACS_P2_P_EGRESS
  #define IDSNVID_CFG_ACS_P2_P_EGRESS   (0xD25D) //P2PEgress ACS Control
#endif

#ifndef IDSNVID_CFG_ACS_P2P_REQ_STRAP5
  #define IDSNVID_CFG_ACS_P2P_REQ_STRAP5   (0xD25E) //P2pReqStrap5
#endif

#ifndef IDSNVID_CFG_E2_E_PREFIX
  #define IDSNVID_CFG_E2_E_PREFIX   (0xD25F) //E2E_PREFIX
#endif

#ifndef IDSNVID_CFG_EXTENDED_FMT_SUPPORTED
  #define IDSNVID_CFG_EXTENDED_FMT_SUPPORTED   (0xD260) //EXTENDED_FMT
#endif

#ifndef IDSNVID_CMN_NBIO_ATOMIC_ROUTING_STRAP5
  #define IDSNVID_CMN_NBIO_ATOMIC_ROUTING_STRAP5   (0xD261) //AtomicRoutingStrap5
#endif

#ifndef IDSNVID_SEV_SNP_SUPPORT
  #define IDSNVID_SEV_SNP_SUPPORT   (0xD262) //SEV-SNP Support
#endif

#ifndef IDSNVID_SEV_TIO_SUPPORT
  #define IDSNVID_SEV_TIO_SUPPORT   (0xD263) //SEV-TIO Support
#endif

#ifndef IDSNVID_CMN_DRTM_MEMORY_RESERVATION
  #define IDSNVID_CMN_DRTM_MEMORY_RESERVATION   (0xD264) //DRTM Memory Reservation
#endif

#ifndef IDSNVID_CMN_DRTM_SUPPORT
  #define IDSNVID_CMN_DRTM_SUPPORT   (0xD265) //DRTM Virtual Device Support
#endif

#ifndef IDSNVID_CMN_DMA_PROTECTION
  #define IDSNVID_CMN_DMA_PROTECTION   (0xD266) //DMA Protection
#endif

#ifndef IDSNVID_CMN_GNB_NB_IOMMU
  #define IDSNVID_CMN_GNB_NB_IOMMU   (0xD267) //IOMMU
#endif

#ifndef IDSNVID_CMN_DMAR_SUPPORT
  #define IDSNVID_CMN_DMAR_SUPPORT   (0xD268) //DMAr Support
#endif

#ifndef IDSNVID_CMN_ENABLE_PORT_BIFURCATION
  #define IDSNVID_CMN_ENABLE_PORT_BIFURCATION   (0xD269) //Enable Port Bifurcation
#endif

#ifndef IDSNVID_CMN_S0_P0_OVERRIDE
  #define IDSNVID_CMN_S0_P0_OVERRIDE   (0xD26A) //Socket 0 P0 Override
#endif

#ifndef IDSNVID_CMN_S0_P1_OVERRIDE
  #define IDSNVID_CMN_S0_P1_OVERRIDE   (0xD26B) //Socket 0 P1 Override
#endif

#ifndef IDSNVID_CMN_S0_P2_OVERRIDE
  #define IDSNVID_CMN_S0_P2_OVERRIDE   (0xD26C) //Socket 0 P2 Override
#endif

#ifndef IDSNVID_CMN_S0_P3_OVERRIDE
  #define IDSNVID_CMN_S0_P3_OVERRIDE   (0xD26D) //Socket 0 P3 Override
#endif

#ifndef IDSNVID_CMN_S1_P0_OVERRIDE
  #define IDSNVID_CMN_S1_P0_OVERRIDE   (0xD26E) //Socket 1 P0 Override
#endif

#ifndef IDSNVID_CMN_S1_P1_OVERRIDE
  #define IDSNVID_CMN_S1_P1_OVERRIDE   (0xD26F) //Socket 1 P1 Override
#endif

#ifndef IDSNVID_CMN_S1_P2_OVERRIDE
  #define IDSNVID_CMN_S1_P2_OVERRIDE   (0xD270) //Socket 1 P2 Override
#endif

#ifndef IDSNVID_CMN_S1_P3_OVERRIDE
  #define IDSNVID_CMN_S1_P3_OVERRIDE   (0xD271) //Socket 1 P3 Override
#endif

#ifndef IDSNVID_CMN_P0_OVERRIDE
  #define IDSNVID_CMN_P0_OVERRIDE   (0xD272) //P0 Override
#endif

#ifndef IDSNVID_CMN_P1_OVERRIDE
  #define IDSNVID_CMN_P1_OVERRIDE   (0xD273) //P1 Override
#endif

#ifndef IDSNVID_CMN_P2_OVERRIDE
  #define IDSNVID_CMN_P2_OVERRIDE   (0xD274) //P2 Override
#endif

#ifndef IDSNVID_CMN_P3_OVERRIDE
  #define IDSNVID_CMN_P3_OVERRIDE   (0xD275) //P3 Override
#endif

#ifndef IDSNVID_CMN_G0_OVERRIDE
  #define IDSNVID_CMN_G0_OVERRIDE   (0xD276) //G0 Override
#endif

#ifndef IDSNVID_CMN_G1_OVERRIDE
  #define IDSNVID_CMN_G1_OVERRIDE   (0xD277) //G1 Override
#endif

#ifndef IDSNVID_CMN_G2_OVERRIDE
  #define IDSNVID_CMN_G2_OVERRIDE   (0xD278) //G2 Override
#endif

#ifndef IDSNVID_CMN_G3_OVERRIDE
  #define IDSNVID_CMN_G3_OVERRIDE   (0xD279) //G3 Override
#endif

#ifndef IDSNVID_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN3
  #define IDSNVID_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN3   (0xD27A) //Preset Search Mask Configuration (Gen3)
#endif

#ifndef IDSNVID_CMN_NBIO_PCIE_SEARCH_MASK_GEN3
  #define IDSNVID_CMN_NBIO_PCIE_SEARCH_MASK_GEN3   (0xD27B) //Preset Search Mask (Gen3)
#endif

#ifndef IDSNVID_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN4
  #define IDSNVID_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN4   (0xD27C) //Preset Search Mask Configuration (Gen4)
#endif

#ifndef IDSNVID_CMN_NBIO_PCIE_SEARCH_MASK_GEN4
  #define IDSNVID_CMN_NBIO_PCIE_SEARCH_MASK_GEN4   (0xD27D) //Preset Search Mask (Gen4)
#endif

#ifndef IDSNVID_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN5
  #define IDSNVID_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN5   (0xD27E) //Preset Search Mask Configuration (Gen5)
#endif

#ifndef IDSNVID_CMN_NBIO_PCIE_SEARCH_MASK_GEN5
  #define IDSNVID_CMN_NBIO_PCIE_SEARCH_MASK_GEN5   (0xD27F) //Preset Search Mask (Gen5)
#endif

#ifndef IDSNVID_CMN_FCH_I3_C0_CONFIG
  #define IDSNVID_CMN_FCH_I3_C0_CONFIG   (0xD280) //I3C/I2C 0 Enable
#endif

#ifndef IDSNVID_CMN_FCH_I3_C1_CONFIG
  #define IDSNVID_CMN_FCH_I3_C1_CONFIG   (0xD281) //I3C/I2C 1 Enable
#endif

#ifndef IDSNVID_CMN_FCH_I3_C2_CONFIG
  #define IDSNVID_CMN_FCH_I3_C2_CONFIG   (0xD282) //I3C/I2C 2 Enable
#endif

#ifndef IDSNVID_CMN_FCH_I3_C3_CONFIG
  #define IDSNVID_CMN_FCH_I3_C3_CONFIG   (0xD283) //I3C/I2C 3 Enable
#endif

#ifndef IDSNVID_CMN_FCH_I2_C4_CONFIG
  #define IDSNVID_CMN_FCH_I2_C4_CONFIG   (0xD284) //I2C 4 Enable
#endif

#ifndef IDSNVID_CMN_FCH_I2_C5_CONFIG
  #define IDSNVID_CMN_FCH_I2_C5_CONFIG   (0xD285) //I2C 5 Enable
#endif

#ifndef IDSNVID_CMN_FCH_RELEASE_SPD_HOST_CONTROL
  #define IDSNVID_CMN_FCH_RELEASE_SPD_HOST_CONTROL   (0xD286) //Release SPD Host Control
#endif

#ifndef IDSNVID_CMN_FCH_PMFW_DDR5_TELEMETRY
  #define IDSNVID_CMN_FCH_PMFW_DDR5_TELEMETRY   (0xD287) //PMFW Poll DDR5 Telemetry
#endif

#ifndef IDSNVID_CMN_FCH_IXC_TELEMETRY_PORTS_FENCE
  #define IDSNVID_CMN_FCH_IXC_TELEMETRY_PORTS_FENCE   (0xD288) //Ixc Telemetry Ports Fence Control
#endif

#ifndef IDSNVID_CMN_FCH_I2C_SDA_HOLD_OVERRIDE
  #define IDSNVID_CMN_FCH_I2C_SDA_HOLD_OVERRIDE   (0xD289) //I2C SDA Hold Override
#endif

#ifndef IDSNVID_CMN_FCH_APML_SBTSI_SLV_MODE
  #define IDSNVID_CMN_FCH_APML_SBTSI_SLV_MODE   (0xD28A) //APML SB-TSI Mode
#endif

#ifndef IDSNVID_CMN_FCH_I3C_MODE_SPEED
  #define IDSNVID_CMN_FCH_I3C_MODE_SPEED   (0xD28B) //I3C Mode Speed
#endif

#ifndef IDSNVID_CMN_FCH_I3C_PP_HCNT_VALUE
  #define IDSNVID_CMN_FCH_I3C_PP_HCNT_VALUE   (0xD28C) //I3C Push Pull HCNT Value
#endif

#ifndef IDSNVID_CMN_FCH_I3C_SDA_HOLD_VALUE
  #define IDSNVID_CMN_FCH_I3C_SDA_HOLD_VALUE   (0xD28D) //I3C SDA Hold Value
#endif

#ifndef IDSNVID_CMN_FCH_I3C_SDA_HOLD_OVERRIDE
  #define IDSNVID_CMN_FCH_I3C_SDA_HOLD_OVERRIDE   (0xD28E) //I3C SDA Hold Override
#endif

#ifndef IDSNVID_CMN_FCH_I2C0_SDA_TX_HOLD_VALUE
  #define IDSNVID_CMN_FCH_I2C0_SDA_TX_HOLD_VALUE   (0xD28F) //I2C 0 SDA TX HOLD VALUE
#endif

#ifndef IDSNVID_CMN_FCH_I2C1_SDA_TX_HOLD_VALUE
  #define IDSNVID_CMN_FCH_I2C1_SDA_TX_HOLD_VALUE   (0xD290) //I2C 1 SDA TX HOLD VALUE
#endif

#ifndef IDSNVID_CMN_FCH_I2C2_SDA_TX_HOLD_VALUE
  #define IDSNVID_CMN_FCH_I2C2_SDA_TX_HOLD_VALUE   (0xD291) //I2C 2 SDA TX HOLD VALUE
#endif

#ifndef IDSNVID_CMN_FCH_I2C3_SDA_TX_HOLD_VALUE
  #define IDSNVID_CMN_FCH_I2C3_SDA_TX_HOLD_VALUE   (0xD292) //I2C 3 SDA TX HOLD VALUE
#endif

#ifndef IDSNVID_CMN_FCH_I2C4_SDA_TX_HOLD_VALUE
  #define IDSNVID_CMN_FCH_I2C4_SDA_TX_HOLD_VALUE   (0xD293) //I2C 4 SDA TX HOLD VALUE
#endif

#ifndef IDSNVID_CMN_FCH_I2C5_SDA_TX_HOLD_VALUE
  #define IDSNVID_CMN_FCH_I2C5_SDA_TX_HOLD_VALUE   (0xD294) //I2C 5 SDA TX HOLD VALUE
#endif

#ifndef IDSNVID_CMN_FCH_I2C0_SDA_RX_HOLD_VALUE
  #define IDSNVID_CMN_FCH_I2C0_SDA_RX_HOLD_VALUE   (0xD295) //I2C 0 SDA RX HOLD VALUE
#endif

#ifndef IDSNVID_CMN_FCH_I2C1_SDA_RX_HOLD_VALUE
  #define IDSNVID_CMN_FCH_I2C1_SDA_RX_HOLD_VALUE   (0xD296) //I2C 1 SDA RX HOLD VALUE
#endif

#ifndef IDSNVID_CMN_FCH_I2C2_SDA_RX_HOLD_VALUE
  #define IDSNVID_CMN_FCH_I2C2_SDA_RX_HOLD_VALUE   (0xD297) //I2C 2 SDA RX HOLD VALUE
#endif

#ifndef IDSNVID_CMN_FCH_I2C3_SDA_RX_HOLD_VALUE
  #define IDSNVID_CMN_FCH_I2C3_SDA_RX_HOLD_VALUE   (0xD298) //I2C 3 SDA RX HOLD VALUE
#endif

#ifndef IDSNVID_CMN_FCH_I2C4_SDA_RX_HOLD_VALUE
  #define IDSNVID_CMN_FCH_I2C4_SDA_RX_HOLD_VALUE   (0xD299) //I2C 4 SDA RX HOLD VALUE
#endif

#ifndef IDSNVID_CMN_FCH_I2C5_SDA_RX_HOLD_VALUE
  #define IDSNVID_CMN_FCH_I2C5_SDA_RX_HOLD_VALUE   (0xD29A) //I2C 5 SDA RX HOLD VALUE
#endif

#ifndef IDSNVID_CMN_FCH_I3C0_SDA_HOLD_VALUE
  #define IDSNVID_CMN_FCH_I3C0_SDA_HOLD_VALUE   (0xD29B) //I3C 0 SDA HOLD VALUE
#endif

#ifndef IDSNVID_CMN_FCH_I3C1_SDA_HOLD_VALUE
  #define IDSNVID_CMN_FCH_I3C1_SDA_HOLD_VALUE   (0xD29C) //I3C 1 SDA HOLD VALUE
#endif

#ifndef IDSNVID_CMN_FCH_I3C2_SDA_HOLD_VALUE
  #define IDSNVID_CMN_FCH_I3C2_SDA_HOLD_VALUE   (0xD29D) //I3C 2 SDA HOLD VALUE
#endif

#ifndef IDSNVID_CMN_FCH_I3C3_SDA_HOLD_VALUE
  #define IDSNVID_CMN_FCH_I3C3_SDA_HOLD_VALUE   (0xD29E) //I3C 3 SDA HOLD VALUE
#endif

#ifndef IDSNVID_CMN_FCH_SATA_ENABLE
  #define IDSNVID_CMN_FCH_SATA_ENABLE   (0xD29F) //SATA Enable
#endif

#ifndef IDSNVID_CMN_FCH_SATA_CLASS
  #define IDSNVID_CMN_FCH_SATA_CLASS   (0xD2A0) //SATA Mode
#endif

#ifndef IDSNVID_CMN_FCH_SATA_RAS_SUPPORT
  #define IDSNVID_CMN_FCH_SATA_RAS_SUPPORT   (0xD2A1) //SATA RAS Support
#endif

#ifndef IDSNVID_CMN_FCH_SATA_STAGGERED_SPINUP
  #define IDSNVID_CMN_FCH_SATA_STAGGERED_SPINUP   (0xD2A2) //SATA Staggered Spin-up
#endif

#ifndef IDSNVID_CMN_FCH_SATA_AHCI_DIS_PREFETCH_FUNCTION
  #define IDSNVID_CMN_FCH_SATA_AHCI_DIS_PREFETCH_FUNCTION   (0xD2A3) //SATA Disabled AHCI Prefetch Function
#endif

#ifndef IDSNVID_DBG_FCH_SATA0_ENABLE
  #define IDSNVID_DBG_FCH_SATA0_ENABLE   (0xD2A4) //Sata0 Enable
#endif

#ifndef IDSNVID_DBG_FCH_SATA1_ENABLE
  #define IDSNVID_DBG_FCH_SATA1_ENABLE   (0xD2A5) //Sata1 Enable
#endif

#ifndef IDSNVID_DBG_FCH_SATA2_ENABLE
  #define IDSNVID_DBG_FCH_SATA2_ENABLE   (0xD2A6) //Sata2 Enable
#endif

#ifndef IDSNVID_DBG_FCH_SATA3_ENABLE
  #define IDSNVID_DBG_FCH_SATA3_ENABLE   (0xD2A7) //Sata3 Enable
#endif

#ifndef IDSNVID_DBG_FCH_SATA4_ENABLE
  #define IDSNVID_DBG_FCH_SATA4_ENABLE   (0xD2A8) //Sata4 (Socket1) Enable
#endif

#ifndef IDSNVID_DBG_FCH_SATA5_ENABLE
  #define IDSNVID_DBG_FCH_SATA5_ENABLE   (0xD2A9) //Sata5 (Socket1) Enable
#endif

#ifndef IDSNVID_DBG_FCH_SATA6_ENABLE
  #define IDSNVID_DBG_FCH_SATA6_ENABLE   (0xD2AA) //Sata6 (Socket1) Enable
#endif

#ifndef IDSNVID_DBG_FCH_SATA7_ENABLE
  #define IDSNVID_DBG_FCH_SATA7_ENABLE   (0xD2AB) //Sata7 (Socket1) Enable
#endif

#ifndef IDSNVID_DBG_FCH_SATAE_SATA_PORT0
  #define IDSNVID_DBG_FCH_SATAE_SATA_PORT0   (0xD2AC) //Sata0 eSATA Port0
#endif

#ifndef IDSNVID_DBG_FCH_SATAE_SATA_PORT1
  #define IDSNVID_DBG_FCH_SATAE_SATA_PORT1   (0xD2AD) //Sata0 eSATA Port1
#endif

#ifndef IDSNVID_DBG_FCH_SATAE_SATA_PORT2
  #define IDSNVID_DBG_FCH_SATAE_SATA_PORT2   (0xD2AE) //Sata0 eSATA Port2
#endif

#ifndef IDSNVID_DBG_FCH_SATAE_SATA_PORT3
  #define IDSNVID_DBG_FCH_SATAE_SATA_PORT3   (0xD2AF) //Sata0 eSATA Port3
#endif

#ifndef IDSNVID_DBG_FCH_SATAE_SATA_PORT4
  #define IDSNVID_DBG_FCH_SATAE_SATA_PORT4   (0xD2B0) //Sata0 eSATA Port4
#endif

#ifndef IDSNVID_DBG_FCH_SATAE_SATA_PORT5
  #define IDSNVID_DBG_FCH_SATAE_SATA_PORT5   (0xD2B1) //Sata0 eSATA Port5
#endif

#ifndef IDSNVID_DBG_FCH_SATAE_SATA_PORT6
  #define IDSNVID_DBG_FCH_SATAE_SATA_PORT6   (0xD2B2) //Sata0 eSATA Port6
#endif

#ifndef IDSNVID_DBG_FCH_SATAE_SATA_PORT7
  #define IDSNVID_DBG_FCH_SATAE_SATA_PORT7   (0xD2B3) //Sata0 eSATA Port7
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT0
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT0   (0xD2B4) //Sata1 eSATA Port0
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT1
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT1   (0xD2B5) //Sata1 eSATA Port1
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT2
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT2   (0xD2B6) //Sata1 eSATA Port2
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT3
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT3   (0xD2B7) //Sata1 eSATA Port3
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT4
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT4   (0xD2B8) //Sata1 eSATA Port4
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT5
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT5   (0xD2B9) //Sata1 eSATA Port5
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT6
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT6   (0xD2BA) //Sata1 eSATA Port6
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT7
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT7   (0xD2BB) //Sata1 eSATA Port7
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT0
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT0   (0xD2BC) //Sata2 eSATA Port0
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT1
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT1   (0xD2BD) //Sata2 eSATA Port1
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT2
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT2   (0xD2BE) //Sata2 eSATA Port2
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT3
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT3   (0xD2BF) //Sata2 eSATA Port3
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT4
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT4   (0xD2C0) //Sata2 eSATA Port4
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT5
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT5   (0xD2C1) //Sata2 eSATA Port5
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT6
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT6   (0xD2C2) //Sata2 eSATA Port6
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT7
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT7   (0xD2C3) //Sata2 eSATA Port7
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT0
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT0   (0xD2C4) //Sata3 eSATA Port0
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT1
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT1   (0xD2C5) //Sata3 eSATA Port1
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT2
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT2   (0xD2C6) //Sata3 eSATA Port2
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT3
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT3   (0xD2C7) //Sata3 eSATA Port3
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT4
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT4   (0xD2C8) //Sata3 eSATA Port4
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT5
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT5   (0xD2C9) //Sata3 eSATA Port5
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT6
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT6   (0xD2CA) //Sata3 eSATA Port6
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT7
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT7   (0xD2CB) //Sata3 eSATA Port7
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT0
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT0   (0xD2CC) //Sata4 eSATA Port0
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT1
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT1   (0xD2CD) //Sata4 eSATA Port1
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT2
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT2   (0xD2CE) //Sata4 eSATA Port2
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT3
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT3   (0xD2CF) //Sata4 eSATA Port3
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT4
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT4   (0xD2D0) //Sata4 eSATA Port4
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT5
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT5   (0xD2D1) //Sata4 eSATA Port5
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT6
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT6   (0xD2D2) //Sata4 eSATA Port6
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT7
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT7   (0xD2D3) //Sata4 eSATA Port7
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT0
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT0   (0xD2D4) //Sata5 eSATA Port0
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT1
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT1   (0xD2D5) //Sata5 eSATA Port1
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT2
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT2   (0xD2D6) //Sata5 eSATA Port2
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT3
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT3   (0xD2D7) //Sata5 eSATA Port3
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT4
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT4   (0xD2D8) //Sata5 eSATA Port4
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT5
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT5   (0xD2D9) //Sata5 eSATA Port5
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT6
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT6   (0xD2DA) //Sata5 eSATA Port6
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT7
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT7   (0xD2DB) //Sata5 eSATA Port7
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT0
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT0   (0xD2DC) //Sata6 eSATA Port0
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT1
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT1   (0xD2DD) //Sata6 eSATA Port1
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT2
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT2   (0xD2DE) //Sata6 eSATA Port2
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT3
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT3   (0xD2DF) //Sata6 eSATA Port3
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT4
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT4   (0xD2E0) //Sata6 eSATA Port4
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT5
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT5   (0xD2E1) //Sata6 eSATA Port5
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT6
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT6   (0xD2E2) //Sata6 eSATA Port6
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT7
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT7   (0xD2E3) //Sata6 eSATA Port7
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT0
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT0   (0xD2E4) //Sata7 eSATA Port0
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT1
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT1   (0xD2E5) //Sata7 eSATA Port1
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT2
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT2   (0xD2E6) //Sata7 eSATA Port2
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT3
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT3   (0xD2E7) //Sata7 eSATA Port3
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT4
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT4   (0xD2E8) //Sata7 eSATA Port4
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT5
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT5   (0xD2E9) //Sata7 eSATA Port5
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT6
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT6   (0xD2EA) //Sata7 eSATA Port6
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT7
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT7   (0xD2EB) //Sata7 eSATA Port7
#endif

#ifndef IDSNVID_DBG_FCH_SATA_AGGRESIVE_DEV_SLP_P0
  #define IDSNVID_DBG_FCH_SATA_AGGRESIVE_DEV_SLP_P0   (0xD2EC) //Socket0 DevSlp0 Enable
#endif

#ifndef IDSNVID_DBG_FCH_SATA_DEV_SLP_CONTROLLER0_NUM
  #define IDSNVID_DBG_FCH_SATA_DEV_SLP_CONTROLLER0_NUM   (0xD2ED) //Socket0 DevSlp0 Controller Number
#endif

#ifndef IDSNVID_DBG_FCH_SATA_DEV_SLP_PORT0_NUM
  #define IDSNVID_DBG_FCH_SATA_DEV_SLP_PORT0_NUM   (0xD2EE) //Socket0 DevSlp0 Port Number
#endif

#ifndef IDSNVID_DBG_FCH_SATA_AGGRESIVE_DEV_SLP_P1
  #define IDSNVID_DBG_FCH_SATA_AGGRESIVE_DEV_SLP_P1   (0xD2EF) //Socket0 DevSlp1 Enable
#endif

#ifndef IDSNVID_DBG_FCH_SATA_DEV_SLP_CONTROLLER1_NUM
  #define IDSNVID_DBG_FCH_SATA_DEV_SLP_CONTROLLER1_NUM   (0xD2F0) //Socket0 DevSlp1 Controller Number
#endif

#ifndef IDSNVID_DBG_FCH_SATA_DEV_SLP_PORT1_NUM
  #define IDSNVID_DBG_FCH_SATA_DEV_SLP_PORT1_NUM   (0xD2F1) //Socket0 DevSlp1 Port Number
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP0
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP0   (0xD2F2) //Socket1 DevSlp0 Enable
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP_CONTROLLER0_NUM
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP_CONTROLLER0_NUM   (0xD2F3) //Socket1 DevSlp0 Controller Number
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP0_NUM
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP0_NUM   (0xD2F4) //Socket1 DevSlp0 Port Number
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP1
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP1   (0xD2F5) //Socket1 DevSlp1 Enable
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP_CONTROLLER1_NUM
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP_CONTROLLER1_NUM   (0xD2F6) //Socket1 DevSlp1 Controller Number
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP1_NUM
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP1_NUM   (0xD2F7) //Socket1 DevSlp1 Port Number
#endif

#ifndef IDSNVID_DBG_FCH_SATA_SGPIO0
  #define IDSNVID_DBG_FCH_SATA_SGPIO0   (0xD2F8) //Sata0 SGPIO
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE1_SGPIO0
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE1_SGPIO0   (0xD2F9) //Sata1 SGPIO
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE2_SGPIO0
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE2_SGPIO0   (0xD2FA) //Sata2 SGPIO
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE3_SGPIO0
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE3_SGPIO0   (0xD2FB) //Sata3 SGPIO
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE4_SGPIO0
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE4_SGPIO0   (0xD2FC) //Sata4 SGPIO
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE5_SGPIO0
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE5_SGPIO0   (0xD2FD) //Sata5 SGPIO
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE6_SGPIO0
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE6_SGPIO0   (0xD2FE) //Sata6 SGPIO
#endif

#ifndef IDSNVID_DBG_FCH_SATA_MCM_DIE7_SGPIO0
  #define IDSNVID_DBG_FCH_SATA_MCM_DIE7_SGPIO0   (0xD2FF) //Sata7 SGPIO
#endif

#ifndef IDSNVID_CMN_FCH_USB_XHC_I0_ENABLE
  #define IDSNVID_CMN_FCH_USB_XHC_I0_ENABLE   (0xD300) //XHCI Controller0 enable
#endif

#ifndef IDSNVID_CMN_FCH_USB_XHC_I1_ENABLE
  #define IDSNVID_CMN_FCH_USB_XHC_I1_ENABLE   (0xD301) //XHCI Controller1 enable
#endif

#ifndef IDSNVID_CMN_FCH_USB_XHC_I2_ENABLE
  #define IDSNVID_CMN_FCH_USB_XHC_I2_ENABLE   (0xD302) //XHCI2 enable (Socket1)
#endif

#ifndef IDSNVID_CMN_FCH_USB_XHC_I3_ENABLE
  #define IDSNVID_CMN_FCH_USB_XHC_I3_ENABLE   (0xD303) //XHCI3 enable (Socket1)
#endif

#ifndef IDSNVID_CMN_FCH_SYSTEM_PWR_FAIL_SHADOW
  #define IDSNVID_CMN_FCH_SYSTEM_PWR_FAIL_SHADOW   (0xD304) //Ac Loss Control
#endif

#ifndef IDSNVID_CMN_FCH_PWR_FAIL_SHADOW_ABL_ENABLED
  #define IDSNVID_CMN_FCH_PWR_FAIL_SHADOW_ABL_ENABLED   (0xD305) //Set Fch Power failed Shadow in ABL
#endif

#ifndef IDSNVID_CMN_FCH_UART0_CONFIG
  #define IDSNVID_CMN_FCH_UART0_CONFIG   (0xD306) //Uart 0 Enable
#endif

#ifndef IDSNVID_CMN_FCH_UART0_LEGACY_CONFIG
  #define IDSNVID_CMN_FCH_UART0_LEGACY_CONFIG   (0xD307) //Uart 0 Legacy Options
#endif

#ifndef IDSNVID_CMN_FCH_UART1_CONFIG
  #define IDSNVID_CMN_FCH_UART1_CONFIG   (0xD308) //Uart 1 Enable
#endif

#ifndef IDSNVID_CMN_FCH_UART1_LEGACY_CONFIG
  #define IDSNVID_CMN_FCH_UART1_LEGACY_CONFIG   (0xD309) //Uart 1 Legacy Options
#endif

#ifndef IDSNVID_CMN_FCH_UART2_CONFIG
  #define IDSNVID_CMN_FCH_UART2_CONFIG   (0xD30A) //Uart 2 Enable
#endif

#ifndef IDSNVID_CMN_FCH_UART2_LEGACY_CONFIG
  #define IDSNVID_CMN_FCH_UART2_LEGACY_CONFIG   (0xD30B) //Uart 2 Legacy Options
#endif

#ifndef IDSNVID_CMN_FCH_ALINK_RAS_SUPPORT
  #define IDSNVID_CMN_FCH_ALINK_RAS_SUPPORT   (0xD30C) //ALink RAS Support
#endif

#ifndef IDSNVID_DBG_FCH_SYNCFLOOD_ENABLE
  #define IDSNVID_DBG_FCH_SYNCFLOOD_ENABLE   (0xD30D) //Reset After Sync-Flood
#endif

#ifndef IDSNVID_DBG_FCH_DELAY_SYNCFLOOD
  #define IDSNVID_DBG_FCH_DELAY_SYNCFLOOD   (0xD30E) //Delay Reset After Sync-Flood
#endif

#ifndef IDSNVID_DBG_FCH_SYSTEM_SPREAD_SPECTRUM
  #define IDSNVID_DBG_FCH_SYSTEM_SPREAD_SPECTRUM   (0xD30F) //FCH Spread Spectrum
#endif

#ifndef IDSNVID_CMN_BOOT_TIMER_ENABLE
  #define IDSNVID_CMN_BOOT_TIMER_ENABLE   (0xD310) //Boot Timer Enable
#endif

#ifndef IDSNVID_CMN_S_P3_NTB_P0_P0
  #define IDSNVID_CMN_S_P3_NTB_P0_P0   (0xD311) //Socket-0 P0 NTB Enable
#endif

#ifndef IDSNVID_CMN_S_P3_NTB_START_LANE_P0_P0
  #define IDSNVID_CMN_S_P3_NTB_START_LANE_P0_P0   (0xD312) //Socket-0 P0 Start Lane
#endif

#ifndef IDSNVID_CMN_S_P3_NTB_END_LANE_P0_P0
  #define IDSNVID_CMN_S_P3_NTB_END_LANE_P0_P0   (0xD313) //Socket-0 P0 End Lane
#endif

#ifndef IDSNVID_CMN_S_P3_NTB_LINK_SPEED_P0_P0
  #define IDSNVID_CMN_S_P3_NTB_LINK_SPEED_P0_P0   (0xD314) //Socket-0 P0 Link Speed
#endif

#ifndef IDSNVID_CMN_S_P3_NTB_MODE_P0_P0
  #define IDSNVID_CMN_S_P3_NTB_MODE_P0_P0   (0xD315) //Socket-0 P0 NTB Mode
#endif

#ifndef IDSNVID_CMN_S_P3_NTB_P0_P2
  #define IDSNVID_CMN_S_P3_NTB_P0_P2   (0xD316) //Socket-0 P2 NTB Enable
#endif

#ifndef IDSNVID_CMN_S_P3_NTB_START_LANE_P0_P2
  #define IDSNVID_CMN_S_P3_NTB_START_LANE_P0_P2   (0xD317) //Socket-0 P2 Start Lane
#endif

#ifndef IDSNVID_CMN_S_P3_NTB_END_LANE_P0_P2
  #define IDSNVID_CMN_S_P3_NTB_END_LANE_P0_P2   (0xD318) //Socket-0 P2 End Lane
#endif

#ifndef IDSNVID_CMN_S_P3_NTB_LINK_SPEED_P0_P2
  #define IDSNVID_CMN_S_P3_NTB_LINK_SPEED_P0_P2   (0xD319) //Socket-0 P2 Link Speed
#endif

#ifndef IDSNVID_CMN_S_P3_NTB_MODE_P0_P2
  #define IDSNVID_CMN_S_P3_NTB_MODE_P0_P2   (0xD31A) //Socket-0 P2 NTB Mode
#endif

#ifndef IDSNVID_CMN_SOC_ABL_CON_OUT
  #define IDSNVID_CMN_SOC_ABL_CON_OUT   (0xD31B) //ABL Console Out Control
#endif

#ifndef IDSNVID_CMN_SOC_ABL_CON_OUT_SERIAL_PORT
  #define IDSNVID_CMN_SOC_ABL_CON_OUT_SERIAL_PORT   (0xD31C) //ABL Console Out Serial Port
#endif

#ifndef IDSNVID_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_IO
  #define IDSNVID_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_IO   (0xD31D) //ABL Console Out Serial Port IO
#endif

#ifndef IDSNVID_CMN_SOC_ABL_SERIAL_PORT_IO_CUSTOM_ENABLED
  #define IDSNVID_CMN_SOC_ABL_SERIAL_PORT_IO_CUSTOM_ENABLED   (0xD31E) //ABL Serial port IO customized enabled
#endif

#ifndef IDSNVID_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_IO_CUSTOM
  #define IDSNVID_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_IO_CUSTOM   (0xD31F) //ABL Console out Serial Port IO Customized
#endif

#ifndef IDSNVID_CMN_SOC_ABL_CON_OUT_BASIC
  #define IDSNVID_CMN_SOC_ABL_CON_OUT_BASIC   (0xD320) //ABL Basic Console Out Control
#endif

#ifndef IDSNVID_CMN_SOC_ABL_PMU_MSG_CTRL
  #define IDSNVID_CMN_SOC_ABL_PMU_MSG_CTRL   (0xD321) //ABL PMU message Control
#endif

#ifndef IDSNVID_CMN_SOC_ABL_MEM_POP_MSG_CTRL
  #define IDSNVID_CMN_SOC_ABL_MEM_POP_MSG_CTRL   (0xD322) //ABL Memory Population message Control
#endif

#ifndef IDSNVID_CMN_PRINT_SOCKET1_PMU_MSG_BLOCK
  #define IDSNVID_CMN_PRINT_SOCKET1_PMU_MSG_BLOCK   (0xD323) //Print Socket 1 PMU MsgBlock
#endif

#ifndef IDSNVID_CMN_PRINT_SOCKET1_TRAINING_LOG
  #define IDSNVID_CMN_PRINT_SOCKET1_TRAINING_LOG   (0xD324) //Print Socket 1 PMU Training Log
#endif

#ifndef IDSNVID_DF_CMN_PSP_ERR_INJ
  #define IDSNVID_DF_CMN_PSP_ERR_INJ   (0xD325) //PSP error injection support
#endif

#ifndef IDSNVID_NUMBER_OF_SOCKETS
  #define IDSNVID_NUMBER_OF_SOCKETS   (0xD326) //Number of Sockets
#endif

#ifndef IDSNVID_CMN_SEC_I2C_VOLT_MODE
  #define IDSNVID_CMN_SEC_I2C_VOLT_MODE   (0xD327) //SEC_I2C Voltage Mode
#endif

#ifndef IDSNVID_CMN_SOC_FAR_ENFORCED
  #define IDSNVID_CMN_SOC_FAR_ENFORCED   (0xD328) //FAR enforcement state
#endif

#ifndef IDSNVID_CMN_SOC_SPL_FUSE
  #define IDSNVID_CMN_SOC_SPL_FUSE   (0xD329) //SPL value in the CPU fuse
#endif

#ifndef IDSNVID_CMN_SOC_SPL_VALUE_IN_TBL
  #define IDSNVID_CMN_SOC_SPL_VALUE_IN_TBL   (0xD32A) //SPL value in the SPL table
#endif

#ifndef IDSNVID_CMN_SOC_FAR_SWITCH
  #define IDSNVID_CMN_SOC_FAR_SWITCH   (0xD32B) //FAR Switch
#endif

#ifndef IDSNVID_CMN_CXL_CONTROL
  #define IDSNVID_CMN_CXL_CONTROL   (0xD32C) //CXL Control
#endif

#ifndef IDSNVID_CMN_CXL_SDP_REQ_SYS_ADDR
  #define IDSNVID_CMN_CXL_SDP_REQ_SYS_ADDR   (0xD32D) //CXL Physical Addressing
#endif

#ifndef IDSNVID_CMN_CXL_SPM
  #define IDSNVID_CMN_CXL_SPM   (0xD32E) //CXL Memory Attribute
#endif

#ifndef IDSNVID_CMN_CXL_ENCRYPTION
  #define IDSNVID_CMN_CXL_ENCRYPTION   (0xD32F) //CXL Encryption
#endif

#ifndef IDSNVID_CMN_CXL_DVSEC_LOCK
  #define IDSNVID_CMN_CXL_DVSEC_LOCK   (0xD330) //CXL DVSEC Lock
#endif

#ifndef IDSNVID_CMN_CXL_HDM_DECODER_LOCK_ON_COMMIT
  #define IDSNVID_CMN_CXL_HDM_DECODER_LOCK_ON_COMMIT   (0xD331) //CXL HDM Decoder Lock On Commit
#endif

#ifndef IDSNVID_CMN_CXL_TEMP_GEN5_ADVERTISEMENT
  #define IDSNVID_CMN_CXL_TEMP_GEN5_ADVERTISEMENT   (0xD332) //Temp Gen5 Advertisement
#endif

#ifndef IDSNVID_CMN_SYNC_HEADER_BY_PASS
  #define IDSNVID_CMN_SYNC_HEADER_BY_PASS   (0xD333) //Sync Header Bypass
#endif

#ifndef IDSNVID_CXL_SYNC_HEADER_BYPASS_COMP_MODE
  #define IDSNVID_CXL_SYNC_HEADER_BYPASS_COMP_MODE   (0xD334) //Sync Header Bypass Compatibility Mode
#endif

#ifndef IDSNVID_CMN_CXL_MEM_ONLINE_OFFLINE
  #define IDSNVID_CMN_CXL_MEM_ONLINE_OFFLINE   (0xD335) //CXL Memory Online/Offline
#endif

#ifndef IDSNVID_DBG_CXL_OVERIDE_CXL_MEMORY_SIZE
  #define IDSNVID_DBG_CXL_OVERIDE_CXL_MEMORY_SIZE   (0xD336) //Override CXL Memory Size
#endif

#ifndef IDSNVID_CMN_CXL_PROTOCOL_ERROR_REPORTING
  #define IDSNVID_CMN_CXL_PROTOCOL_ERROR_REPORTING   (0xD337) //CXL Protocol Error Reporting
#endif

#ifndef IDSNVID_CMN_CXL_COMPONENT_ERROR_REPORTING
  #define IDSNVID_CMN_CXL_COMPONENT_ERROR_REPORTING   (0xD338) //CXL Component Error Reporting
#endif

#ifndef IDSNVID_CMN_CXL_MEM_ISOLATION_ENABLE
  #define IDSNVID_CMN_CXL_MEM_ISOLATION_ENABLE   (0xD339) //CXL Root Port Isolation
#endif

#ifndef IDSNVID_CMN_CXL_MEM_ISOLATION_FW_NOTIFICATION
  #define IDSNVID_CMN_CXL_MEM_ISOLATION_FW_NOTIFICATION   (0xD33A) //CXL Root Port Isolation FW Notification
#endif

#endif //_IDS_NV_ID_BRH_H_
