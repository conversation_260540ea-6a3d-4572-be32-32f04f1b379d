#;*****************************************************************************
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;*****************************************************************************


[LibraryClasses.common.SEC]
  CcxNonSmmResumeSecLib|AgesaModulePkg/Library/CcxNonSmmResumeSecLib/CcxNonSmmResumeSecLib.inf
  AmdEmulationAutoDetectLib|AgesaModulePkg/Library/AmdEmulationAutoDetectPeiLib/AmdEmulationAutoDetectPeiLib.inf

[LibraryClasses.Common.PEIM]
  AmdEmulationAutoDetectLib|AgesaModulePkg/Library/AmdEmulationAutoDetectPeiLib/AmdEmulationAutoDetectPeiLib.inf
  AmdS3SaveLib|AgesaModulePkg/Library/AmdS3SaveLib/WOS3Save/AmdWOS3SaveLib.inf
  AmdHeapLib|AgesaModulePkg/Library/AmdHeapPeiLib/AmdHeapPeiLib.inf
  FabricResourceSizeForEachRbLib|AgesaModulePkg/Library/PeiFabricResourceSizeForEachRbLib/PeiFabricResourceSizeForEachRbLib.inf
  FabricResourceReportToGcdLib|AgesaModulePkg/Library/FabricResourceReportToGcdNullLib/FabricResourceReportToGcdNullLib.inf
  CcxSetMmioCfgBaseLib|AgesaModulePkg/Library/CcxSetMmioCfgBaseLib/CcxSetMmioCfgBaseLib.inf
  IdsLib|AgesaModulePkg/Library/IdsPeiLib/IdsPeiLib.inf
  FchPeiLibV9|AgesaModulePkg/Library/FchPeiLib/FchPeiLib.inf
  FchKunlunPeiLib|AgesaModulePkg/Fch/Kunlun/FchKunlunCore/FchKunlunLibPei.inf
  FchInitHookLibPei|AgesaModulePkg/Library/FchInitHookLib/FchInitHookLibPei.inf
  AmdPspCommonLib|AgesaModulePkg/Library/AmdPspCommonLibPei/AmdPspCommonLibPei.inf
  ApobCommonServiceLib|AgesaModulePkg/Library/ApobCommonServiceLibPei/ApobCommonServiceLibPei.inf
  PeiSocLogicalIdServicesLib|AgesaModulePkg/Library/PeiSocLogicalIdServicesLib/PeiSocLogicalIdServicesLib.inf
  AmdTableHookLib|AgesaModulePkg/Library/AmdTableLibV2/Pei/AmdTableHookPeiLibV2.inf
  AmdCapsuleLib|AgesaModulePkg/Library/AmdCapsuleLibPei/AmdCapsuleLibPei.inf
  PeiSocBistLogging3Lib|AgesaModulePkg/Library/PeiSocBistLogging3Lib/PeiSocBistLogging3Lib.inf
  PcieComplexDefaultsLib|AgesaModulePkg/Nbio/Library/PcieComplexDefaultsLib/PcieComplexDefaultsLib.inf
  NbioCommonPeiLib|AgesaModulePkg/Nbio/Library/CommonPei/NbioCommonPeiLib.inf
  CcxPeiSmmAccessLib|AgesaModulePkg/Library/PeiCcxSmmAccessLib/PeiCcxSmmAccessLib.inf
  AmdPspRegMuxLibV2|AgesaModulePkg/Library/AmdPspRegMuxLibV2Null/AmdPspRegMuxLibV2.inf
  AmdPspBarInitLibV2|AgesaModulePkg/Library/AmdPspBarInitLibV2/AmdPspBarInitLibV2.inf
  AmdCfgPcdBufLib|AgesaModulePkg/Library/AmdCfgPcdBufLibPei/AmdCfgPcdBufLibPei.inf
  PeiCoreTopologyServicesV3Lib|AgesaModulePkg/Library/PeiCcxCoreTopologyServicesV3OnV2Lib/PeiCcxCoreTopologyServicesV3OnV2Lib.inf
  RasIdsLib|AgesaModulePkg/Library/Ras/RasIdsPeiLib/RasIdsPeiLib.inf
  CoreTopologyV3Lib|AgesaModulePkg/Library/PeiCoreTopologyV3Lib/PeiCoreTopologyV3Lib.inf
  SocCoreInfo2AccessLib|AgesaModulePkg/Library/SocCoreInfo2AccessLib/SocCoreInfo2AccessLib.inf

  ## MEM Libs
  AmdMemBaseLib|AgesaModulePkg/Library/Mem/BaseLib/AmdMemBaseLib.inf

  ## IDS LIB
  AmdIdsHookLib|AgesaModulePkg/Library/AmdIdsHookLibPei/AmdIdsHookLib.inf
  CcxZen5IdsHookLibPei|AgesaModulePkg/Library/CcxZen5BrhIdsHookLib/Pei/CcxZen5BrhIdsHookLibPei.inf
  FabricIdsHookBrhLibPei|AgesaModulePkg/Library/FabricIdsHookBrhLib/Pei/FabricIdsHookBrhLibPei.inf
  NbioIdsHookBrhLibPei|AgesaModulePkg/Nbio/BRH/Library/NbioIdsHookBrhLib/Pei/NbioIdsHookBrhLibPei.inf
  FchIdsHookLib|AgesaModulePkg/Library/FchIdsHookBrhLib/Pei/FchIdsHookBrhLibPei.inf

  ## Emulation Switch lib
  AmdEmulationFlagLib|AgesaModulePkg/Library/AmdEmulationFlagLib/AmdEmulationFlagPeiLib.inf

  ## APCB
  ApcbLibV3Pei|AgesaModulePkg/Library/ApcbLibV3Pei/ApcbLibV3Pei.inf

[LibraryClasses.Common.DXE_DRIVER]
  AmdS3SaveLib|AgesaModulePkg/Library/AmdS3SaveLib/S3Save/AmdS3SaveLib.inf
  IdsLib|AgesaModulePkg/Library/IdsDxeLib/IdsDxeLib.inf
  FabricResourceSizeForEachRbLib|AgesaModulePkg/Library/DxeFabricResourceSizeForEachRbLib/DxeFabricResourceSizeForEachRbLib.inf
  AmdHeapLib|AgesaModulePkg/Library/AmdHeapDxeLib/AmdHeapDxeLib.inf
  GnbHeapLib|AgesaModulePkg/Library/GnbHeapDxeLib/GnbHeapDxeLib.inf
  AmdPspCommonLib|AgesaModulePkg/Library/AmdPspCommonLibDxe/AmdPspCommonLibDxe.inf
  ApobCommonServiceLib|AgesaModulePkg/Library/ApobCommonServiceLibDxe/ApobCommonServiceLibDxe.inf
  AmdPspFlashAccLib|AgesaModulePkg/Library/AmdPspFlashAccLibDxe/AmdPspFlashAccLibDxe.inf
  AmdPspFlashUpdateLib|AgesaModulePkg/Library/AmdPspFlashUpdateLib/AmdPspFlashUpdateLib.inf
  AmdPspRomArmorLib|AgesaModulePkg/Library/AmdPspRomArmorLibNull/AmdPspRomArmorLibNull.inf
  DxeSocLogicalIdServicesLib|AgesaModulePkg/Library/DxeSocLogicalIdServicesLib/DxeSocLogicalIdServicesLib.inf
  AmdTableHookLib|AgesaModulePkg/Library/AmdTableLibV2/Dxe/AmdTableHookDxeLibV2.inf
  AmdCapsuleLib|AgesaModulePkg/Library/AmdCapsuleLibDxe/AmdCapsuleLibDxe.inf
  CcxMpServicesLib|AgesaModulePkg/Library/CcxMpServicesDxeLib/CcxMpServicesDxeLib.inf
  CcxSmmAccess2Lib|AgesaModulePkg/Library/DxeCcxSmmAccess2Lib/DxeCcxSmmAccess2Lib.inf
  AmdIOMMUDmarLib|AgesaModulePkg/Nbio/Library/IommuDmarLib/DXE/AmdIOMMUDmarLib.inf
  CcxCppcLib|AgesaModulePkg/Library/DxeCcxCppcLib/DxeCcxCppcLib.inf
  AmdPspRegMuxLibV2|AgesaModulePkg/Library/AmdPspRegMuxLibV2Dxe/AmdPspRegMuxLibV2.inf
  AmdCfgPcdBufLib|AgesaModulePkg/Library/AmdCfgPcdBufLibDxe/AmdCfgPcdBufLibDxe.inf
  AmlGenerationLib|AgesaModulePkg/Library/DxeAmlGenerationLib/AmlGenerationLib.inf
  AcpiTableHelperLib|AgesaModulePkg/Library/AcpiTableHelperLib/AcpiTableHelperLib.inf
  DxeCoreTopologyServicesV3Lib|AgesaModulePkg/Library/DxeCcxCoreTopologyServicesV3OnV2Lib/DxeCcxCoreTopologyServicesV3OnV2Lib.inf
  RasIdsLib|AgesaModulePkg/Library/Ras/RasIdsDxeLib/RasIdsDxeLib.inf
  CoreTopologyV3Lib|AgesaModulePkg/Library/DxeCoreTopologyV3Lib/DxeCoreTopologyV3Lib.inf
  CcxCcdReorderLib|AgesaModulePkg/Library/DxeCcxCcdReorderZen5Lib/DxeCcxCcdReorderZen5Lib.inf
  SocCoreInfo2AccessLib|AgesaModulePkg/Library/SocCoreInfo2AccessLib/SocCoreInfo2AccessLib.inf

  ## MEM Lib
  ApcbLibV3|AgesaModulePkg/Library/ApcbLibV3/ApcbLibV3.inf
  ApcbVariableLibV3|AgesaModulePkg/Library/ApcbVariableLibV3/ApcbVariableLibV3.inf
  ApcbChecksumLibV3|AgesaModulePkg/Library/ApcbHmacChecksumLibV3/ApcbHmacChecksumLibV3.inf
  AmdPspDxeSmmBufLib|AgesaModulePkg/Library/AmdPspDxeSmmBufLib/AmdPspDxeSmmBufLib.inf
  ApcbTokenWhiteListLib|AgesaModulePkg/Library/ApcbTokenWhiteListBrhLib/ApcbTokenWhiteListBrhLib.inf

  ## IDS LIB
  AmdIdsHookLib|AgesaModulePkg/Library/AmdIdsHookLibDxe/AmdIdsHookLib.inf
  CcxZen5IdsHookLibDxe|AgesaModulePkg/Library/CcxZen5BrhIdsHookLib/Dxe/CcxZen5BrhIdsHookLibDxe.inf
  FabricIdsHookBrhLibDxe|AgesaModulePkg/Library/FabricIdsHookBrhLib/Dxe/FabricIdsHookBrhLibDxe.inf
  NbioIdsHookBrhLibDxe|AgesaModulePkg/Nbio/BRH/Library/NbioIdsHookBrhLib/Dxe/NbioIdsHookBrhLibDxe.inf
  FchIdsHookLib|AgesaModulePkg/Library/FchIdsHookBrhLib/Dxe/FchIdsHookBrhLibDxe.inf

  ## Emulation Switch lib
  AmdEmulationFlagLib|AgesaModulePkg/Library/AmdEmulationFlagLib/AmdEmulationFlagDxeSmmLib.inf

  ## RAS Lib
  RasAcpiLib|AgesaModulePkg/Library/Ras/RasAcpi63Lib/RasAcpi63Lib.inf
  DfAddressTranslateLib|AgesaModulePkg/Library/Ras/Brh/DfAddressTranslateBrhLib/DfAddressTranslateBrhLib.inf

[LibraryClasses.Common.DXE_SMM_DRIVER]
  AmdHeapLib|AgesaModulePkg/Library/AmdHeapDxeLib/AmdHeapDxeLib.inf
  AmdS3SaveLib|AgesaModulePkg/Library/AmdS3SaveLib/S3Save/AmdS3SaveLib.inf
  FabricResourceSizeForEachRbLib|AgesaModulePkg/Library/DxeFabricResourceSizeForEachRbLib/DxeFabricResourceSizeForEachRbLib.inf
#@todo add specifiic SMM Lib instance, DXE Lib is depend on gBS service exisitance
  IdsLib|AgesaModulePkg/Library/IdsNonUefiLib/IdsNonUefiLib.inf
  AmdIdsHookLib|AgesaModulePkg/Library/AmdIdsHookLibDxe/AmdIdsHookLib.inf
  AmdPspCommonLib|AgesaModulePkg/Library/AmdPspCommonLibDxe/AmdPspCommonLibDxe.inf
!if $(SMM_ISOLATION_LEVEL30_SUPPORT) == TRUE
  AmdPspMmioLib|AgesaModulePkg/Library/AmdPspMmioLibSmmIso/AmdPspMmioLib.inf
!else
  AmdPspMmioLib|AgesaModulePkg/Library/AmdPspMmioLib/AmdPspMmioLib.inf
!endif
  AmdPspRegMuxLibV2|AgesaModulePkg/Library/AmdPspRegMuxLibV2Dxe/AmdPspRegMuxLibV2.inf
  ApobCommonServiceLib|AgesaModulePkg/Library/ApobCommonServiceLibDxe/ApobCommonServiceLibDxe.inf
  AmdPspFlashUpdateLib|AgesaModulePkg/Library/AmdPspFlashUpdateLib/AmdPspFlashUpdateLib.inf
  AmdPspRomArmorLib|AgesaModulePkg/Library/AmdPspRomArmorLib/AmdPspRomArmorLib.inf
  CcxZen5IdsHookLibSmm|AgesaModulePkg/Library/CcxZen5BrhIdsHookLib/Smm/CcxZen5BrhIdsHookLibSmm.inf
  AmdCapsuleLib|AgesaModulePkg/Library/AmdCapsuleLibDxe/AmdCapsuleLibDxe.inf
  AmdTableHookLib|AgesaModulePkg/Library/AmdTableLibV2/Dxe/AmdTableHookDxeLibV2.inf
  ApcbLibV3|AgesaModulePkg/Library/ApcbLibV3/ApcbLibV3.inf
  ApcbVariableLibV3|AgesaModulePkg/Library/ApcbVariableLibV3/ApcbVariableLibV3.inf
  ApcbChecksumLibV3|AgesaModulePkg/Library/ApcbHmacChecksumLibV3/ApcbHmacChecksumLibV3.inf
  RasSmmLib|AgesaModulePkg/Library/Ras/Brh/RasBrhSmmLib/RasBrhSmmLib.inf
  DfAddressTranslateLib|AgesaModulePkg/Library/Ras/Brh/DfAddressTranslateBrhLib/DfAddressTranslateBrhLib.inf
  AmdCfgPcdBufLib|AgesaModulePkg/Library/AmdCfgPcdBufLibDxe/AmdCfgPcdBufLibDxe.inf
  AmdPspDxeSmmBufLib|AgesaModulePkg/Library/AmdPspDxeSmmBufLib/AmdPspDxeSmmBufLib.inf
  RasIdsLib|AgesaModulePkg/Library/Ras/RasIdsSmmLib/Brh/RasIdsSmmLib.inf
  ApcbTokenWhiteListLib|AgesaModulePkg/Library/ApcbTokenWhiteListBrhLib/ApcbTokenWhiteListBrhLib.inf

  ## PM_MPDMA ARS Lib
  PmMpDmaArsLib|AgesaModulePkg/Library/PmMpDmaArsLib/Brh/PmMpDmaBrhArsLib.inf

  ## Emulation Switch lib
  AmdEmulationFlagLib|AgesaModulePkg/Library/AmdEmulationFlagLib/AmdEmulationFlagDxeSmmLib.inf

[LibraryClasses.COMMON.DXE_RUNTIME_DRIVER]
  AmdHeapLib|AgesaModulePkg/Library/AmdHeapDxeLib/AmdHeapDxeLib.inf
  AmdPspRegMuxLibV2|AgesaModulePkg/Library/AmdPspRegMuxLibV2DxeRt/AmdPspRegMuxLibV2.inf
  AmdStbLib|AgesaModulePkg/Library/AmdStbLibNull/AmdStbLibNull.inf
  AmdCfgPcdBufLib|AgesaModulePkg/Library/AmdCfgPcdBufLibDxe/AmdCfgPcdBufLibDxe.inf

  ## FCH DXE Runtime ResetSystem lib
  ResetSystemLib|AgesaModulePkg/Library/FchDxeRuntimeResetSystemLib/KunLun/FchDxeRuntimeResetSystemLib.inf

[LibraryClasses.common.PEI_CORE]
  AmdEmulationAutoDetectLib|AgesaModulePkg/Library/AmdEmulationAutoDetectPeiLib/AmdEmulationAutoDetectPeiLib.inf
  AmdHeapLib|AgesaModulePkg/Library/AmdHeapPeiLib/AmdHeapPeiLib.inf
  AmdTableHookLib|AgesaModulePkg/Library/AmdTableLibV2/Pei/AmdTableHookPeiLibV2.inf
  AmdCfgPcdBufLib|AgesaModulePkg/Library/AmdCfgPcdBufLibPei/AmdCfgPcdBufLibPei.inf

[LibraryClasses]
  #
  # Agesa specific common libraries
  #
  AmdBaseLib|AgesaModulePkg/Library/AmdBaseLib/AmdBaseLibNoIntrinsic.inf
  FabricResourceSizeForEachRbLib|AgesaModulePkg/Library/DxeFabricResourceSizeForEachRbLib/DxeFabricResourceSizeForEachRbLib.inf
  FabricResourceReportToGcdLib|AgesaModulePkg/Library/FabricResourceReportToGcdLib/FabricResourceReportToGcdLib.inf
  AmdEmulationAutoDetectLib|AgesaModulePkg/Library/AmdEmulationAutoDetectDxeLib/AmdEmulationAutoDetectDxeLib.inf
  AmdIdsDebugPrintLib|AgesaModulePkg/Library/AmdIdsDebugPrintLib/AmdIdsDebugPrintLib.inf
  AmdIdsHookLib|AgesaModulePkg/Library/AmdIdsHookLibNull/AmdIdsHookLibNull.inf
  AmdIdsHookExtLib|AgesaModulePkg/Library/AmdIdsExtLibNull/AmdIdsHookExtLibNull.inf
  IdsLib|AgesaModulePkg/Library/IdsNonUefiLib/IdsNonUefiLib.inf
  IdsMiscLib|AgesaModulePkg/Library/IdsMiscLib/IdsMiscLib.inf
  AmdHeapLib|AgesaModulePkg/Library/AmdHeapLibNull/AmdHeapLibNull.inf
  AmdCfgPcdBufLib|AgesaModulePkg/Library/AmdCfgPcdBufLibNull/AmdCfgPcdBufLibNull.inf
  AmdPostCodeLib|AgesaModulePkg/Library/AmdPostCodeLib/AmdPostCodeLib.inf
  AmdSocBaseLib|AgesaModulePkg/Library/AmdSocBaseLib/AmdSocBaseLib.inf
  AmdErrorLogLib|AgesaModulePkg/Library/AmdErrorLogLib/AmdErrorLogLib.inf
  AmdTableLib|AgesaModulePkg/Library/AmdTableLibV2/AmdTableLibV2.inf
  SocCmnIdsHookBrhLibPei|AgesaModulePkg/Library/SocCmnIdsHookBrhLib/Pei/SocCmnIdsHookBrhLibPei.inf
  SocCmnIdsHookBrhLibDxe|AgesaModulePkg/Library/SocCmnIdsHookBrhLib/Dxe/SocCmnIdsHookBrhLibDxe.inf
  BaseCoreLogicalIdLib|AgesaModulePkg/Library/BaseCoreLogicalIdX86Lib/BaseCoreLogicalIdX86Lib.inf
  AmdS3SaveLib|AgesaModulePkg/Library/AmdS3SaveLib/WOS3Save/AmdWOS3SaveLib.inf
  AmdStbLib|AgesaModulePkg/Library/AmdStbLib/AmdStbLib.inf
  PresiliconControlLib|AgesaModulePkg/Library/PresiliconControlBrhLib/PresiliconControlBrhLib.inf
  AgesaConfigLib|AgesaModulePkg/Library/AgesaConfigLib/AgesaConfigLib.inf
  BaseSocLogicalIdXlatLib|AgesaModulePkg/Library/BaseSocLogicalIdXlatZen5DieLib/BaseSocLogicalIdXlatZen5DieLib.inf
  ApobApcbLib|AgesaModulePkg/Library/ApobApcbLib/ApobApcbLib.inf

  ## PSP Libs
  AmdPspBaseLibV2|AgesaModulePkg/Library/AmdPspBaseLibV2/AmdPspBaseLibV2.inf
  AmdPspMboxLibV2|AgesaModulePkg/Library/AmdPspMboxLibV2/AmdPspMboxLibV2.inf
  AmdPspRegMuxLibV2|AgesaModulePkg/Library/AmdPspRegMuxLibV2Null/AmdPspRegMuxLibV2.inf
  AmdPspApobLib|AgesaModulePkg/Library/AmdPspApobLib/AmdPspApobLib.inf
  AmdPspFlashAccLib|AgesaModulePkg/Library/AmdPspFlashAccLibNull/AmdPspFlashAccLibNull.inf
  ApobBrhLib|AgesaModulePkg/Library/ApobBrhLib/ApobBrhLib.inf
  ApobDummyLib|AgesaModulePkg/Library/ApobDummyLib/ApobDummyLib.inf
  AmdPspPsbFusingLib|AgesaModulePkg/Library/AmdPspPsbFusingLib/AmdPspPsbFusingLib.inf
  AmdPspHstiStateLib|AgesaModulePkg/Library/AmdPspHstiStateLib/AmdPspHstiStateLib.inf
  AmdPspMmioLib|AgesaModulePkg/Library/AmdPspMmioLib/AmdPspMmioLib.inf
  AmdDirectoryBaseLib|AgesaModulePkg/Library/AmdDirectoryBaseLib/AmdDirectoryBaseLib.inf
  AmdPspFwImageHeaderLib|AgesaModulePkg/Library/AmdPspFwImageHeaderLib/AmdPspFwImageHeaderLib.inf
  ApcbCoreLib|AgesaModulePkg/Library/ApcbCoreLib/ApcbCoreLib.inf
  AmdPspRegBaseLib|AgesaModulePkg/Library/AmdPspRegBaseLib/AmdPspRegBaseLib.inf
  AmdMpmRegBaseLib|AgesaModulePkg/Library/AmdMpmRegBaseLib/AmdMpmRegBaseLib.inf
  AmdPspSfsLib|AgesaModulePkg/Library/AmdPspSfsLib/AmdPspSfsLib.inf

  ## CCX Lib
  CcxBaseX86Lib|AgesaModulePkg/Library/CcxBaseX86Lib/CcxBaseX86Lib.inf
  CcxPspLib|AgesaModulePkg/Library/CcxPspLib/CcxPspLib.inf
  CcxHaltLib|AgesaModulePkg/Library/CcxHaltLib/CcxHaltLib.inf
  CcxMicrocodePatchLib|AgesaModulePkg/Library/CcxMicrocodePatchLib/CcxMicrocodePatchLib.inf
  CcxRolesLib|AgesaModulePkg/Library/CcxRolesX86Lib/CcxRolesX86Lib.inf
  CcxPstatesLib|AgesaModulePkg/Library/CcxPstatesZen5Lib/CcxPstatesZen5Lib.inf
  DxeCcxBaseX86ServicesLib|AgesaModulePkg/Library/DxeCcxBaseX86ServicesLib/DxeCcxBaseX86ServicesLib.inf
  CcxApicZen5Lib|AgesaModulePkg/Library/CcxApicZen5Lib/CcxApicZen5Lib.inf
  CcxZen5DxeLib|AgesaModulePkg/Library/CcxZen5BrhDxeLib/CcxZen5BrhDxeLib.inf
  CcxZen5SegRmpDxeLib|AgesaModulePkg/Library/CcxZen5SegRmpBrhLib/CcxZen5SegRmpBrhDxeLib.inf

  ## DF Lib
  BaseFabricTopologyLib|AgesaModulePkg/Library/BaseFabricTopologyBrhLib/BaseFabricTopologyBrhLib.inf

  PeiFabricTopologyServices2Lib|AgesaModulePkg/Library/PeiFabricTopologyServices2Lib/PeiFabricTopologyServices2Lib.inf
  DxeFabricTopologyServices2Lib|AgesaModulePkg/Library/DxeFabricTopologyServices2Lib/DxeFabricTopologyServices2Lib.inf
  SmmFabricTopologyServices2Lib|AgesaModulePkg/Library/SmmFabricTopologyServices2Lib/SmmFabricTopologyServices2Lib.inf
  FabricRegisterAccLib|AgesaModulePkg/Library/FabricRegisterAccDf4Lib/FabricRegisterAccDf4Lib.inf

  PeiFabricResourceManagerServicesLib|AgesaModulePkg/Library/PeiFabricResourceManagerServicesLib/PeiFabricResourceManagerServicesLib.inf
  DxeFabricResourceManagerServicesLib|AgesaModulePkg/Library/DxeFabricResourceManagerServicesLib/DxeFabricResourceManagerServicesLib.inf

  ## MEM Lib
  AmdMemBaseLib|AgesaModulePkg/Library/Mem/BaseLib/AmdMemBaseLib.inf
  MemRestoreLib|AgesaModulePkg/Library/MemRestoreLib/MemRestoreLib.inf

  ## Gnb Lib
  GnbCommonLib|AgesaModulePkg/Library/GnbCommonLib/GnbCommonLib.inf
  GnbMemAccLib|AgesaModulePkg/Library/GnbMemAccLib/GnbMemAccLib.inf
  GnbIoAccLib|AgesaModulePkg/Library/GnbIoAccLib/GnbIoAccLib.inf
  GnbPciAccLib|AgesaModulePkg/Library/GnbPciSegmentAccLib/GnbPciSegmentAccLib.inf
  GnbCpuAccLib|AgesaModulePkg/Library/GnbCpuAccLib/GnbCpuAccLib.inf
  GnbPciLib|AgesaModulePkg/Library/GnbPciLib/GnbPciLib.inf
  GnbLib|AgesaModulePkg/Library/GnbLib/GnbLib.inf
  NbioHandleLib|AgesaModulePkg/Library/NbioHandleLib/NbioHandleLib.inf
  NbioIommuIvrsLib|AgesaModulePkg/Nbio/Library/IvrsLibV3/IvrsLibV3.inf
  IvrsDeviceInfoLib|AgesaModulePkg/Nbio/Library/IvrsDeviceDfltLib/IvrsDeviceDfltLib.inf
  PcieConfigLib|AgesaModulePkg/Library/PcieConfigLib/PcieConfigLib.inf
  NbioRegisterAccLib|AgesaModulePkg/Library/NbioRegisterAccLib/NbioRegisterAcc.inf
  NbioSmuBrhLib|AgesaModulePkg/Library/NbioSmuBrhLib/NbioSmuBrhLib.inf
  SmnTableLib|AgesaModulePkg/Nbio/Library/SmnTable/SmnTableLib.inf

  MpioInitLib|AgesaModulePkg/Nbio/Library/MpioInitLib/MpioInitLib.inf
  PcieMiscCommLib|AgesaModulePkg/Library/PcieMiscCommLib/PcieMiscCommLib.inf
  SmnAccessLib|AgesaModulePkg/Library/SmnAccessLib/SmnAccessLib.inf
  NbioUtilLib|AgesaModulePkg/Library/NbioUtilLib/NbioUtilLib.inf
  BxbInitLibV1|AgesaModulePkg/Library/BxbNbio/BxbNullLib/BxbNullLib.inf
  OemClkReqControlLib|AgesaModulePkg/Library/NbioClkReqControlLibNull/NbioClkReqControlLibNull.inf
  NbioServicesLibDxe|AgesaModulePkg/Library/NbioServicesLib/Dxe/NbioServicesLibDxe.inf
  NbioCommonDxeLib|AgesaModulePkg/Nbio/Library/CommonDxe/NbioCommonDxeLib.inf
  MpioLib|AgesaModulePkg/Nbio/Library/MpioLibV2/MpioLib.inf
  GetPcieResourcesLib|AgesaModulePkg/Nbio/Library/GetPcieResourcesLib/GetPcieResourcesLib.inf
  CxlConfigLib|AgesaModulePkg/Nbio/Library/CxlConfigLib/CxlConfigLib.inf
  CxlCdatLib|AgesaModulePkg/Nbio/Library/CxlCdatLib/CxlCdatLib.inf
  CxlCedtLib|AgesaModulePkg/Nbio/Library/CxlCedtLib/CxlCedtLib.inf
  CxlMboxLib|AgesaModulePkg/Nbio/Library/CxlMboxLib/CxlMboxLib.inf

  ## Fch Lib
  FchBaseLib|AgesaModulePkg/Library/FchBaseLib/FchBaseLib.inf
  BaseResetSystemLib|AgesaModulePkg/Library/FchBaseResetSystemLib/FchBaseResetSystemLib.inf
  FchDxeLibV9|AgesaModulePkg/Library/FchDxeLib/FchDxeLib.inf
  FchSmmLibV9|AgesaModulePkg/Library/FchSmmLib/FchSmmLib.inf
  FchKunlunDxeLib|AgesaModulePkg/Fch/Kunlun/FchKunlunCore/FchKunlunLibDxe.inf
  FchInitHookLibDxe|AgesaModulePkg/Library/FchInitHookLib/FchInitHookLibDxe.inf
  FchIdsHookLib|AgesaModulePkg/Library/FchIdsHookLib/FchIdsHookLib.inf
  RtcLib|AgesaModulePkg/Library/AmdRtclib/AmdRtcLib.inf
  LegacyInterruptLib|AgesaModulePkg/Library/FchDxeLegacyInterruptLib/FchDxeLegacyInterruptLib.inf
  FchEspiLib|AgesaModulePkg/Library/FchEspiLib/FchEspiLib.inf
  FchEspiCmdLib|AgesaModulePkg/Library/FchEspiCmdLib/FchEspiCmdLib.inf

  ## FCH SPI ACCESS lib
  FchSpiAccessLib|AgesaModulePkg/Library/FchSpiAccessLib/FchSpiAccessRom2Lib.inf

#  FchSpiAccessLib|AgesaModulePkg/Library/FchSpiAccessLib/FchSpiAccessRom2V2Lib.inf                                     <ALL>
  ## FCH I2C Lib
  FchI2cLib|AgesaModulePkg/Library/FchI2cLib/FchI2cLib.inf

  ## FCH I3C Lib
  FchI3cLib|AgesaModulePkg/Library/FchI3cLib/FchI3cLib.inf

  ## FCH Soc Lib
  FchSocLib|AgesaModulePkg/Library/FchSocLib/Breithorn/FchBreithornLib.inf

  ## Ras Lib
  RasSocLib|AgesaModulePkg/Library/Ras/Brh/RasBrhSocLib/RasBrhSocLib.inf

  ## ACPI lib
  AmdAcpiAmlLib|AgesaModulePkg/Library/AmdAcpiAmlLib/AmdAcpiAmlLib.inf

[Components.IA32]
  AgesaModulePkg/Debug/AmdIdsDebugPrintPei/AmdIdsDebugPrintPei.inf
  AgesaModulePkg/Fch/Common/I2cPei/I2cMasterPei.inf

  ##PSP Drivers
  AgesaModulePkg/Psp/AmdPspPeiV2Brh/AmdPspPeiV2.inf {
    <LibraryClasses>
    FabricResourceManagerLib|AgesaModulePkg/Library/FabricResourceManagerBrhLib/FabricResourceManager3Lib.inf
  }
  AgesaModulePkg/Psp/AmdPspDtpmPei/AmdPspDtpmPei.inf
  AgesaModulePkg/Psp/AmdPspPsbDisablePei/AmdPspPsbDisablePei.inf
  AgesaModulePkg/Psp/ApcbDrv/ApcbV3Pei/ApcbV3Pei.inf
  AgesaModulePkg/Psp/ApobDrv/ApobBrhPei/ApobBrhPei.inf

  AgesaModulePkg/Ccx/Zen5/Pei/AmdCcxZen5Pei.inf {
    <LibraryClasses>
    CcxResetTablesLib|AgesaModulePkg/Library/CcxResetTablesZen5Lib/CcxResetTablesZen5Lib.inf
    IdsLib|AgesaModulePkg/Library/IdsNonUefiLib/IdsNonUefiLib.inf
    CcxRolesLib|AgesaModulePkg/Library/CcxRolesZen5Lib/CcxRolesZen5Lib.inf
    CcxPstatesLib|AgesaModulePkg/Library/CcxPstatesZen5Lib/CcxPstatesZen5Lib.inf
    CcxSetMcaLib|AgesaModulePkg/Library/CcxSetMcaZen5Lib/CcxSetMcaZen5Lib.inf
    FabricWdtLib|AgesaModulePkg/Library/FabricWdtDf4Lib/FabricWdtDf4Lib.inf
  }

  AgesaModulePkg/Fabric/BRH/FabricBrhPei/AmdFabricBrhPei.inf {
    <LibraryClasses>
    IdsLib|AgesaModulePkg/Library/IdsNonUefiLib/IdsNonUefiLib.inf
    BaseFabricTopologyLib|AgesaModulePkg/Library/BaseFabricTopologyBrhLib/BaseFabricTopologyBrhLib.inf
    FabricResourceManagerLib|AgesaModulePkg/Library/FabricResourceManagerBrhLib/FabricResourceManager3Lib.inf
  }
  AgesaModulePkg/Nbio/BRH/PEI/NbioPeiBrh.inf {
    <LibraryClasses>
    FabricResourceManagerLib|AgesaModulePkg/Library/FabricResourceManagerBrhLib/FabricResourceManager3Lib.inf
  }

  AgesaModulePkg/Fch/Kunlun/FchKunlunPei/FchPei.inf
  AgesaModulePkg/Fch/Kunlun/FchKunLunSmbusPei/Smbus.inf
  AgesaModulePkg/Fch/Common/I3cPei/I3cMasterPei.inf
  AgesaModulePkg/Fch/Common/FchEspiCmdPei/FchEspiCmdPei.inf
  AgesaModulePkg/Fch/Kunlun/FchKunlunMultiFchPei/FchMultiFchPei.inf  {
    <LibraryClasses>
    FabricResourceManagerLib|AgesaModulePkg/Library/FabricResourceManagerBrhLib/FabricResourceManager3Lib.inf
  }

  AgesaModulePkg/Mem/AmdMemBrhSp5Pei/AmdMemBrhSp5Pei.inf
  AgesaModulePkg/Soc/AmdSocSp5BrhPei/AmdSocSp5BrhPei.inf {
    <LibraryClasses>
    FabricResourceInitLib|AgesaModulePkg/Library/FabricResourceManagerBrhLib/FabricResourceInit3Lib.inf
    BaseFabricTopologyLib|AgesaModulePkg/Library/BaseFabricTopologyBrhLib/BaseFabricTopologyBrhLib.inf
    BaseSocketLogicalIdLib|AgesaModulePkg/Library/BaseSocketLogicalIdRsDieLib/BaseSocketLogicalIdRsDieLib.inf
    BaseSocLogicalIdXlatLib|AgesaModulePkg/Library/BaseSocLogicalIdXlatZen5DieLib/BaseSocLogicalIdXlatZen5DieLib.inf
    PeiSocBistLib|AgesaModulePkg/Library/PeiSocBistZen5CcdBrhLib/PeiSocBistZen5CcdBrhLib.inf
    PeiFabricSocSpecificServicesLib|AgesaModulePkg/Library/PeiFabricSocSpecificServicesBrhLib/PeiFabricSocSpecificServicesBrhLib.inf
    PeiSocZen5ServicesLib|AgesaModulePkg/Library/PeiSocZen5ServicesBrhLib/PeiSocZen5ServicesBrhLib.inf
    PeiCoreTopologyServicesV3Lib|AgesaModulePkg/Library/PeiCcxCoreTopologyServicesV3BrhLib/PeiCcxCoreTopologyServicesV3BrhLib.inf
    ApobApcbUpdatesLib|AgesaModulePkg/Library/ApobApcbUpdatesBrhLib/ApobApcbUpdatesBrhLib.inf
    FabricRootBridgeOrderLib|AgesaModulePkg/Library/FabricRootBridgeOrderLib/FabricRootBridgeOrderLib.inf
  }

  AgesaModulePkg/Mem/AmdMemChanXLatPei/MemChanXLatPei.inf
  AgesaModulePkg/Mem/AmdMemSmbiosV2BrhPei/MemSmbiosV2Pei.inf {
    <LibraryClasses>
    AmdMemSmbiosV2Lib|AgesaModulePkg/Library/MemSmbiosV2BrhD5Lib/MemSmbiosV2Lib.inf
  }
  AgesaModulePkg/Mem/AmdMemRestorePei/MemRestorePei.inf
  AgesaModulePkg/Mem/AmdMbistBrhPei/AmdMbistBrhPei.inf
  AgesaModulePkg/ErrorLog/AmdErrorLogPei/AmdErrorLogPei.inf
  AgesaModulePkg/Mem/AmdMemoryHobInfoPeimBrh/AmdMemoryHobInfoPeimBrh.inf
  AgesaModulePkg/Universal/Version/AmdVersionPei/AmdVersionPei.inf

[Components.X64]
  AgesaModulePkg/Debug/AmdIdsDebugPrintDxe/AmdIdsDebugPrintDxe.inf
  AgesaModulePkg/Mem/AmdMemBrhSp5Dxe/AmdMemBrhSp5Dxe.inf
  AgesaModulePkg/Mem/AmdMemSmbiosV2Dxe/AmdMemSmbiosV2Dxe.inf
  AgesaModulePkg/Mem/AmdMemRestoreDxe/MemRestoreDxe.inf
  AgesaModulePkg/Mem/AmdMemPprSmmDriver/AmdMemPprSmmDriver.inf
  AgesaModulePkg/Mem/AmdMemChanXLatDxe/MemChanXLatDxe.inf
  AgesaModulePkg/Psp/ApcbDrv/ApcbV3Dxe/ApcbV3Dxe.inf
  AgesaModulePkg/Psp/ApcbDrv/ApcbV3Smm/ApcbV3Smm.inf

  AgesaModulePkg/Psp/AmdPspDxeV2Brh/AmdPspDxeV2.inf
  AgesaModulePkg/Psp/AmdPspDxeV2Brh/AmdDrtmAsl.inf
  AgesaModulePkg/Psp/AmdPspP2CmboxV2Mcm/AmdPspP2CmboxV2.inf
  AgesaModulePkg/Psp/AmdPspP2CmboxV2Mcm/AmdPspP2CmboxV2SmmBuffer.inf

  AgesaModulePkg/Psp/AmdPspSmmV2Mcm/AmdPspSmmV2.inf

!ifndef $(AMD_HSTI_SUPPORT_DISABLED)
  AgesaModulePkg/Psp/AmdHstiV2/AmdHstiV2.inf
!endif

  AgesaModulePkg/Psp/ApobDrv/ApobBrhDxe/ApobBrhDxe.inf
  AgesaModulePkg/Psp/AmdPspAspt/AmdPspAspt.inf

  AgesaModulePkg/Ccx/Zen5/Dxe/AmdCcxZen5Dxe.inf {
    <LibraryClasses>
    IdsLib|AgesaModulePkg/Library/IdsNonUefiLib/IdsNonUefiLib.inf
    CcxResetTablesLib|AgesaModulePkg/Library/CcxResetTablesZen5Lib/CcxResetTablesZen5Lib.inf
    CcxSetMcaLib|AgesaModulePkg/Library/CcxSetMcaZen5Lib/CcxSetMcaZen5Lib.inf
    FabricWdtLib|AgesaModulePkg/Library/FabricWdtDf4Lib/FabricWdtDf4Lib.inf
    CcxSmbiosLib|AgesaModulePkg/Library/CcxSmbiosZen5Lib/CcxSmbiosZen5Lib.inf
    CcxRolesLib|AgesaModulePkg/Library/CcxRolesZen5Lib/CcxRolesZen5Lib.inf
    CcxPstatesLib|AgesaModulePkg/Library/CcxPstatesZen5Lib/CcxPstatesZen5Lib.inf
  }
  AgesaModulePkg/Ccx/Zen5/Smm/AmdCcxZen5Smm.inf {
    <LibraryClasses>
    CcxSetMcaLib|AgesaModulePkg/Library/CcxSetMcaZen5Lib/CcxSetMcaZen5Lib.inf
  }
  AgesaModulePkg/Fabric/BRH/FabricBrhDxe/AmdFabricBrhDxe.inf {
    <LibraryClasses>
    IdsLib|AgesaModulePkg/Library/IdsNonUefiLib/IdsNonUefiLib.inf
    FabricResourceManagerLib|AgesaModulePkg/Library/FabricResourceManagerBrhLib/FabricResourceManager3Lib.inf
    BaseFabricTopologyLib|AgesaModulePkg/Library/BaseFabricTopologyBrhLib/BaseFabricTopologyBrhLib.inf
  }

  AgesaModulePkg/Fabric/BRH/FabricBrhSmm/AmdFabricBrhSmm.inf {
    <LibraryClasses>
    AmdS3SaveLib|AgesaModulePkg/Library/AmdS3SaveLib/WOS3Save/AmdWOS3SaveLib.inf
  }
  AgesaModulePkg/Soc/AmdSocSp5BrhDxe/AmdSocSp5BrhDxe.inf {
    <LibraryClasses>
    AmdS3SaveLib|AgesaModulePkg/Library/AmdS3SaveLib/WOS3Save/AmdWOS3SaveLib.inf
    BaseSocketLogicalIdLib|AgesaModulePkg/Library/BaseSocketLogicalIdRsDieLib/BaseSocketLogicalIdRsDieLib.inf
    BaseSocLogicalIdXlatLib|AgesaModulePkg/Library/BaseSocLogicalIdXlatZen5DieLib/BaseSocLogicalIdXlatZen5DieLib.inf
    BaseFabricTopologyLib|AgesaModulePkg/Library/BaseFabricTopologyBrhLib/BaseFabricTopologyBrhLib.inf
    DxeSocZen5ServicesLib|AgesaModulePkg/Library/DxeSocZen5ServicesBrhLib/DxeSocZen5ServicesBrhLib.inf
    DxeCoreTopologyServicesV3Lib|AgesaModulePkg/Library/DxeCcxCoreTopologyServicesV3BrhLib/DxeCcxCoreTopologyServicesV3BrhLib.inf
    DxeFabricSocSpecificServicesLib|AgesaModulePkg/Library/DxeFabricSocSpecificServicesBrhLib/DxeFabricSocSpecificServicesBrhLib.inf
    AmdIdsHookExtLib|AgesaModulePkg/Library/AmdIdsExtLibNull/AmdIdsHookExtLibNull.inf
  }

  AgesaModulePkg/ErrorLog/AmdErrorLogDxe/AmdErrorLogDxe.inf
  AgesaModulePkg/ErrorLog/AmdErrorLogDisplayBrhDxe/AmdErrorLogDisplayBrhDxe.inf
  AgesaModulePkg/ErrorLog/AmdCxlErrorLog/AmdCxlErrorLogDxe.inf

  ## Gnb Dxe Drivers
  AgesaModulePkg/Nbio/Common/CxlManagerDxe/CxlManagerDxe.inf  {
  <LibraryClasses>
    FabricResourceManagerLib|AgesaModulePkg/Library/FabricResourceManagerBrhLib/FabricResourceManager3Lib.inf
  }
  AgesaModulePkg/Nbio/BRH/DXE/NbioDxeBrh.inf {
  <LibraryClasses>
    NbioIommuIvrsLib|AgesaModulePkg/Nbio/Library/IvrsLibV3/IvrsLibV3.inf
    FabricResourceManagerLib|AgesaModulePkg/Library/FabricResourceManagerBrhLib/FabricResourceManager3Lib.inf
    CollectNbifPortInfoLib|AgesaModulePkg/Nbio/BRH/Library/CollectNbifPortInfoLib/CollectNbifPortInfoLib.inf
  }

  ## Fch Dxe Drivers
  AgesaModulePkg/Fch/Kunlun/FchKunlunDxe/FchDxe.inf {
    <LibraryClasses>
    FabricResourceManagerLib|AgesaModulePkg/Library/FabricResourceManagerBrhLib/FabricResourceManager3Lib.inf
  }
  AgesaModulePkg/Fch/Kunlun/FchKunlunSmm/FchSmm.inf {
    <LibraryClasses>
    FabricResourceManagerLib|AgesaModulePkg/Library/FabricResourceManagerBrhLib/FabricResourceManager3Lib.inf
  }
  AgesaModulePkg/Fch/Kunlun/FchKunlunSmbusDxe/SmbusLight.inf
  AgesaModulePkg/Fch/Common/I2cDxe/I2cMasterDxe.inf
  AgesaModulePkg/Fch/Common/I2cSmm/I2cMasterSmm.inf
  AgesaModulePkg/Fch/Common/I3cDxe/I3cMasterDxe.inf
  AgesaModulePkg/Fch/Common/FchEspiCmdDxe/FchEspiCmdDxe.inf
  AgesaModulePkg/Fch/Common/FchEspiCmdSmm/FchEspiCmdSmm.inf
!ifndef $(AMD_RESET_DXE_DRIVER_SUPPORT_DISABLED)
  AgesaModulePkg/Fch/Kunlun/FchKunlunCf9ResetDxe/Cf9Reset.inf
!endif
  AgesaModulePkg/Fch/Kunlun/FchKunlunSmmControlDxe/SmmControl.inf
  AgesaModulePkg/Fch/Kunlun/FchKunlunSmmDispatcher/FchSmmDiagDispatcher.inf
  AgesaModulePkg/Fch/Kunlun/FchKunlunSmmDispatcher/FchSmmDispatcher.inf {
    <LibraryClasses>
    FabricResourceManagerLib|AgesaModulePkg/Library/FabricResourceManagerBrhLib/FabricResourceManager3Lib.inf
  }
  AgesaModulePkg/Fch/Kunlun/FchKunlunMultiFchDxe/FchMultiFchDxe.inf {
    <LibraryClasses>
    FabricResourceManagerLib|AgesaModulePkg/Library/FabricResourceManagerBrhLib/FabricResourceManager3Lib.inf
  }
  AgesaModulePkg/Fch/Kunlun/FchKunlunMultiFchSmm/FchMultiFchSmm.inf {
    <LibraryClasses>
    FabricResourceManagerLib|AgesaModulePkg/Library/FabricResourceManagerBrhLib/FabricResourceManager3Lib.inf
  }

  # Universal
  AgesaModulePkg/Universal/Smbios/AmdSmbiosDxe.inf
  AgesaModulePkg/Universal/Acpi/AmdAcpiDxe.inf
  AgesaModulePkg/Universal/Acpi/AmdAcpiHmatService.inf
  AgesaModulePkg/Universal/AmdSmmCommunication/AmdSmmCommunication.inf
  AgesaModulePkg/Universal/Version/AmdVersionDxe/AmdVersionDxe.inf

  ## Ras Dxe Drivers
  AgesaModulePkg/Ras/Brh/AmdRasBrhServiceDxe/AmdRasBrhServiceDxe.inf
  AgesaModulePkg/Ras/Brh/AmdRasBrhDxe/AmdRasBrhDxe.inf
  AgesaModulePkg/Ras/Brh/AmdRasBrhServiceSmm/AmdRasBrhServiceSmm.inf

  #ACT Dxe Driver
  AgesaModulePkg/Universal/ActDxe/ActDxe.inf

[PcdsFixedAtBuild]
  # AMD AGESA PI Name & Version string
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdPackageString|"AGESA!V9\0TurinPI-SP5 *******"

  # For RAS Post Package Repair function
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdMemMaxChannelPerDieV2|12

  # For Memory Context Restore
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspApobUseSpiMmioAddress|TRUE

[PcdsDynamicDefault]
  # Gnb Gbs Override
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXhciForceGen1|0xFF
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMPIOAncDataSupport|TRUE
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMpdmaAcpiIvrsSupport|TRUE
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdIvrsRelativeAddrNamesSupport|TRUE

  # CCX and Fabric Default
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdEnableFSRM|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdEnableERMS|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdEnableRMSS|TRUE
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdEnableSvmAVIC|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuCstC2Latency|0x64
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricCdma|FALSE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuPauseCntSel_1_0|0xFF
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuReqMinFreq|0

  #ACPI Override
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSratSlitInstallOverride|TRUE

  # For NBIO
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSyncFloodToApml|TRUE
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMaster7bitSteeringTag|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEnable2SpcGen4|TRUE

  # For SDXI
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgSriovEnDev0F1|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAriEnDev0F1|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAerEnDev0F1|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAcsEnDev0F1|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAtsEnDev0F1|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPasidEnDev0F1|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgDev0F1PasidEn|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgRtrEnDev0F1|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPriEnDev0F1|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPwrEnDev0F1|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAtcEnable|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdNbifDev0F1AtomicRequestEn|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsEnRccDev0|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsP2pReq|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsSourceVal|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdRccDev0E2EPrefix|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdRccDev0ExtendedFmtSupported|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAtomicRoutingEnStrap5|TRUE

  # For APCB HMAC
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdApcbUseHmacChecksum|TRUE
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdLockApcbDxeAfterSmmLock|TRUE

[PcdsDynamicExDefault]
  #For NBIO
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDfPstateRangeMin|2
