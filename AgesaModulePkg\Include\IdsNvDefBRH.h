/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */
//This file is auto generated, do not edit it manually

#ifndef _IDS_NV_DEF_BRH_H_
#define _IDS_NV_DEF_BRH_H_

///SMT Control
///Can be used to disable symmetric multithreading. To re-enable SMT, a POWER CYCLE is needed after selecting the "Enable" option. Select "Auto" based on BIOS PCD (PcdAmdSmtMode) default setting. WARNING - S3 is NOT SUPPORTED on systems where SMT is disabled.
typedef enum {
  IDSOPT_CPU_SMT_CTRL_DISABLE = 0,///<Disable
  IDSOPT_CPU_SMT_CTRL_ENABLE = 1,///<Enable
  IDSOPT_CPU_SMT_CTRL_AUTO = 0xF,///<Auto
} IDSOPT_CPU_SMT_CTRL;

///Requested CPU min frequency
///Minimum requested CPU frequency in Mhz
#define IDSOPT_CMN_CPU_REQ_MIN_FREQ_MIN 1200 ///< Min of Requested CPU min frequency
#define IDSOPT_CMN_CPU_REQ_MIN_FREQ_MAX 0xFFFF ///< Max of Requested CPU min frequency

///Enable Requested CPU min frequency
///This allows for minimum requested CPU frequency to be used.
typedef enum {
  IDSOPT_CMN_CPU_EN_REQ_MIN_FREQ_DISABLE = 0,///<Disable
  IDSOPT_CMN_CPU_EN_REQ_MIN_FREQ_ENABLE = 1,///<Enable
} IDSOPT_CMN_CPU_EN_REQ_MIN_FREQ;

///REP-MOV/STOS Streaming
///Allow REP-MOVS/STOS to use non-caching streaming stores for large sizes
typedef enum {
  IDSOPT_CMN_CPU_RMSS_DISABLED = 0,///<Disabled
  IDSOPT_CMN_CPU_RMSS_ENABLED = 1,///<Enabled
} IDSOPT_CMN_CPU_RMSS;

///RedirectForReturnDis
///From a workaround for GCC/C000005 issue for XV Core on CZ A0, setting MSRC001_1029 Decode Configuration (DE_CFG) bit 14 [DecfgNoRdrctForReturns] to 1
typedef enum {
  IDSOPT_CMN_CPU_GEN_W_A05_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_CPU_GEN_W_A05_1 = 1,///<1
  IDSOPT_CMN_CPU_GEN_W_A05_0 = 0,///<0
} IDSOPT_CMN_CPU_GEN_W_A05;

///Platform First Error Handling
///Enable/disable PFEH, cloak individual banks, and mask deferred error interrupts from each bank.
typedef enum {
  IDSOPT_CMN_CPU_PFEH_ENABLED = 1,///<Enabled
  IDSOPT_CMN_CPU_PFEH_DISABLED = 0,///<Disabled
  IDSOPT_CMN_CPU_PFEH_AUTO = 3,///<Auto
} IDSOPT_CMN_CPU_PFEH;

///Core Performance Boost
///Disable CPB
typedef enum {
  IDSOPT_CMN_CPU_CPB_DISABLED = 0,///<Disabled
  IDSOPT_CMN_CPU_CPB_AUTO = 1,///<Auto
} IDSOPT_CMN_CPU_CPB;

///Global C-state Control
///Controls IO based C-state generation and DF C-states.
typedef enum {
  IDSOPT_CMN_CPU_GLOBAL_CSTATE_CTRL_DISABLED = 0,///<Disabled
  IDSOPT_CMN_CPU_GLOBAL_CSTATE_CTRL_ENABLED = 1,///<Enabled
  IDSOPT_CMN_CPU_GLOBAL_CSTATE_CTRL_AUTO = 3,///<Auto
} IDSOPT_CMN_CPU_GLOBAL_CSTATE_CTRL;

///Power Supply Idle Control
///Power Supply Idle Control.
typedef enum {
  IDSOPT_CMN_GNB_POWER_SUPPLY_IDLE_CTRL_LOWCURRENTIDLE = 1,///<Low Current Idle
  IDSOPT_CMN_GNB_POWER_SUPPLY_IDLE_CTRL_TYPICALCURRENTIDLE = 0,///<Typical Current Idle
  IDSOPT_CMN_GNB_POWER_SUPPLY_IDLE_CTRL_AUTO = 0xf,///<Auto
} IDSOPT_CMN_GNB_POWER_SUPPLY_IDLE_CTRL;

///Streaming Stores Control
///Enables or disables the streaming stores functionality
typedef enum {
  IDSOPT_CMN_CPU_STREAMING_STORES_CTRL_DISABLED = 1,///<Disabled
  IDSOPT_CMN_CPU_STREAMING_STORES_CTRL_ENABLED = 0,///<Enabled
  IDSOPT_CMN_CPU_STREAMING_STORES_CTRL_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_CPU_STREAMING_STORES_CTRL;

///Local APIC Mode
///Select local APIC operation modes
typedef enum {
  IDSOPT_DBG_CPU_L_APIC_MODE_XAPIC = 1,///<xAPIC
  IDSOPT_DBG_CPU_L_APIC_MODE_X2APIC = 2,///<x2APIC
  IDSOPT_DBG_CPU_L_APIC_MODE_AUTO = 0xFF,///<Auto
} IDSOPT_DBG_CPU_L_APIC_MODE;

///ACPI _CST C1 Declaration
///Determines whether or not to declare the C1 state to the OS.
typedef enum {
  IDSOPT_CMN_CPU_CST_C1_CTRL_DISABLED = 0,///<Disabled
  IDSOPT_CMN_CPU_CST_C1_CTRL_ENABLED = 1,///<Enabled
  IDSOPT_CMN_CPU_CST_C1_CTRL_AUTO = 3,///<Auto
} IDSOPT_CMN_CPU_CST_C1_CTRL;

///ACPI CST C2 Latency
///Enter in microseconds (decimal value).\nLarger C2 latency values will reduce the number of C2 transitions and reduce C2 residency. Fewer transitions can help when performance is sensitive to the latency of C2 entry and exit. Higher residency can improve performance by allowing higher frequency boost and reduce idle core power. With Linux kernel 6.0 or later, the C2 transition cost is significantly reduced. The best value will be dependent on kernel version, use case, and workload.
#define IDSOPT_CMN_CPU_CST_C2_LATENCY_MIN 18 ///< Min of ACPI CST C2 Latency
#define IDSOPT_CMN_CPU_CST_C2_LATENCY_MAX 1000 ///< Max of ACPI CST C2 Latency

///MCA error thresh enable
///Enable MCA error thresholding.
typedef enum {
  IDSOPT_CMN_CPU_MCA_ERR_THRESH_EN_FALSE = 0,///<False
  IDSOPT_CMN_CPU_MCA_ERR_THRESH_EN_TRUE = 1,///<True
  IDSOPT_CMN_CPU_MCA_ERR_THRESH_EN_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_CPU_MCA_ERR_THRESH_EN;

///MCA error thresh count
///Effective error threshold count = 0xFFF (4095) - <this value> (e.g. the default value of 0xFF5 (4085) results in a threshold of 0xA (10)).
#define IDSOPT_CMN_CPU_MCA_ERR_THRESH_COUNT_MIN 0x0 ///< Min of MCA error thresh count
#define IDSOPT_CMN_CPU_MCA_ERR_THRESH_COUNT_MAX 0xFFE ///< Max of MCA error thresh count

///MCA FruText
///Enable MCA FruText
typedef enum {
  IDSOPT_CMN_CPU_MCA_FRU_TEXT_EN_FALSE = 0,///<False
  IDSOPT_CMN_CPU_MCA_FRU_TEXT_EN_TRUE = 1,///<True
} IDSOPT_CMN_CPU_MCA_FRU_TEXT_EN;

///SMU and PSP Debug Mode
///When this option is enabled, uncorrected errors detected by the PSP FW or SMU FW that should cause a cold reset, will hang and not reset the system
typedef enum {
  IDSOPT_CMN_CPU_SMU_PSP_DEBUG_MODE_DISABLED = 0,///<Disabled
  IDSOPT_CMN_CPU_SMU_PSP_DEBUG_MODE_ENABLED = 1,///<Enabled
  IDSOPT_CMN_CPU_SMU_PSP_DEBUG_MODE_AUTO = 3,///<Auto
} IDSOPT_CMN_CPU_SMU_PSP_DEBUG_MODE;

///PPIN Opt-in
///Turn on PPIN feature
typedef enum {
  IDSOPT_CMN_CPU_PPIN_CTRL_DISABLED = 0,///<Disabled
  IDSOPT_CMN_CPU_PPIN_CTRL_ENABLED = 1,///<Enabled
  IDSOPT_CMN_CPU_PPIN_CTRL_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_CPU_PPIN_CTRL;

///SMEE
///Control secure memory encryption enable\nEnabling both SMEE and SME-MK is not supported. Results in #GP
typedef enum {
  IDSOPT_CMN_CPU_SMEE_DISABLE = 0,///<Disable
  IDSOPT_CMN_CPU_SMEE_ENABLE = 1,///<Enable
  IDSOPT_CMN_CPU_SMEE_AUTO = 3,///<Auto
} IDSOPT_CMN_CPU_SMEE;

///SEV Control
///Can be used to disable SEV. To re-enable SEV, a POWER CYCLE is needed after selecting the "Enable" option.
typedef enum {
  IDSOPT_PSP_SEV_CTRL_ENABLE = 0,///<Enable
  IDSOPT_PSP_SEV_CTRL_DISABLE = 1,///<Disable
} IDSOPT_PSP_SEV_CTRL;

///SEV-ES ASID Space Limit
///SEV-ES and SNP guests must use ASIDs in the range 1 through (this value -1). SEV guests must use ASIDs in the range of this value through 1007. To have all ASIDs support SEV-ES or SNP guests, set this value to 1007. The default is 1: all SEV guests and no SEV-ES or SNP guests.
#define IDSOPT_CMN_CPU_SEV_ASID_SPACE_LIMIT_MIN 1 ///< Min of SEV-ES ASID Space Limit
#define IDSOPT_CMN_CPU_SEV_ASID_SPACE_LIMIT_MAX 1007 ///< Max of SEV-ES ASID Space Limit

///SNP Memory (RMP Table) Coverage
///Enabled = ENTIRE system memory is covered.
typedef enum {
  IDSOPT_DBG_CPU_SNP_MEM_COVER_DISABLED = 0,///<Disabled
  IDSOPT_DBG_CPU_SNP_MEM_COVER_ENABLED = 1,///<Enabled
  IDSOPT_DBG_CPU_SNP_MEM_COVER_CUSTOM = 2,///<Custom
  IDSOPT_DBG_CPU_SNP_MEM_COVER_AUTO = 0xFF,///<Auto
} IDSOPT_DBG_CPU_SNP_MEM_COVER;

///Amount of Memory to Cover
///Specify MB of System Memory to be covered in Hex.
#define IDSOPT_DBG_CPU_SNP_MEM_SIZE_COVER_MIN 0x10 ///< Min of Amount of Memory to Cover
#define IDSOPT_DBG_CPU_SNP_MEM_SIZE_COVER_MAX 0x100000 ///< Max of Amount of Memory to Cover

///RMP Coverage for 64Bit MMIO Ranges
///Control RMP Coverage for 64Bit MMIO above 4GB. Enable = 64Bit MMIO Above 4GB is covered by RMP. Disable = 64Bit MMIO Above 4GB is not covered by RMP.
typedef enum {
  IDSOPT_CMN_CPU64_BIT_MMIO_COVERAGE_DISABLED = 0,///<Disabled
  IDSOPT_CMN_CPU64_BIT_MMIO_COVERAGE_ENABLED = 1,///<Enabled
  IDSOPT_CMN_CPU64_BIT_MMIO_COVERAGE_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_CPU64_BIT_MMIO_COVERAGE;

///Socket0 RootBridge Mask for 64Bit MMIO RMP Coverage
///Mask for S0 RootBridges covering 64Bit MMIO Ranges. Bit0=RB0,Bit1=RB1,Bit2=RB2...Bit7=RB7
#define IDSOPT_CMN_CPU64_BIT_MMIO_RMP_S0_RB_MASK_MIN 0x01 ///< Min of Socket0 RootBridge Mask for 64Bit MMIO RMP Coverage
#define IDSOPT_CMN_CPU64_BIT_MMIO_RMP_S0_RB_MASK_MAX 0xFF ///< Max of Socket0 RootBridge Mask for 64Bit MMIO RMP Coverage

///Socket1 RootBridge Mask for 64Bit MMIO RMP Coverage
///Mask for S1 RootBridges covering 64Bit MMIO Ranges. Bit0=RB0,Bit1=RB1,Bit2=RB2...Bit7=RB7
#define IDSOPT_CMN_CPU64_BIT_MMIO_RMP_S1_RB_MASK_MIN 0x00 ///< Min of Socket1 RootBridge Mask for 64Bit MMIO RMP Coverage
#define IDSOPT_CMN_CPU64_BIT_MMIO_RMP_S1_RB_MASK_MAX 0xFF ///< Max of Socket1 RootBridge Mask for 64Bit MMIO RMP Coverage

///Split RMP Table
///Control RMP Table Allocation. Enabled=Split RMP Table across sockets. Disabled=Allocate RMP Table at the end of memory.
typedef enum {
  IDSOPT_DBG_CPU_SPLIT_RMP_DISABLED = 0,///<Disabled
  IDSOPT_DBG_CPU_SPLIT_RMP_ENABLED = 1,///<Enabled
  IDSOPT_DBG_CPU_SPLIT_RMP_AUTO = 0xFF,///<Auto
} IDSOPT_DBG_CPU_SPLIT_RMP;

///Segmented RMP Table
///Control RMP_CFG[0] "Segmented RMP" feature.  Enable = segmented RMP table. Disable = legacy/old single non-segmented RMP table.
typedef enum {
  IDSOPT_DBG_CPU_SEGMENTED_RMP_DISABLED = 0,///<Disabled
  IDSOPT_DBG_CPU_SEGMENTED_RMP_ENABLED = 1,///<Enabled
  IDSOPT_DBG_CPU_SEGMENTED_RMP_AUTO = 0xFF,///<Auto
} IDSOPT_DBG_CPU_SEGMENTED_RMP;

///RMP Segment Size
///Define RMP_CFG[13:8] RmpSegSize (as power of 2) used to split memory ranges into segments, when Segmented RMP is enabled.
typedef enum {
  IDSOPT_DBG_CPU_RMP_SEGMENT_SIZE_64GB = 0x24,///<64GB
  IDSOPT_DBG_CPU_RMP_SEGMENT_SIZE_128GB = 0x25,///<128GB
  IDSOPT_DBG_CPU_RMP_SEGMENT_SIZE_256GB = 0x26,///<256GB
  IDSOPT_DBG_CPU_RMP_SEGMENT_SIZE_512GB = 0x27,///<512GB
  IDSOPT_DBG_CPU_RMP_SEGMENT_SIZE_1024GB = 0x28,///<1024GB
  IDSOPT_DBG_CPU_RMP_SEGMENT_SIZE_2048GB = 0x29,///<2048GB
  IDSOPT_DBG_CPU_RMP_SEGMENT_SIZE_4096GB = 0x2A,///<4096GB
  IDSOPT_DBG_CPU_RMP_SEGMENT_SIZE_AUTO = 0xFF,///<Auto
} IDSOPT_DBG_CPU_RMP_SEGMENT_SIZE;

///Action on BIST Failure
///Action to take when a CCD BIST failure is detected.
typedef enum {
  IDSOPT_CMN_ACTION_ON_BIST_FAILURE_DONOTHING = 0,///<Do nothing
  IDSOPT_CMN_ACTION_ON_BIST_FAILURE_DOWNCCD = 1,///<Down-CCD
  IDSOPT_CMN_ACTION_ON_BIST_FAILURE_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_ACTION_ON_BIST_FAILURE;

///Enhanced REP MOVSB/STOSB (ERSM)
///Default is 1, can be set to zero for analysis purposes as long as OS supports it.
typedef enum {
  IDSOPT_CMN_CPU_ERMS_DISABLED = 0,///<Disabled
  IDSOPT_CMN_CPU_ERMS_ENABLED = 1,///<Enabled
  IDSOPT_CMN_CPU_ERMS_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_CPU_ERMS;

///Log Transparent Errors
///Log transparent errors in MCA in addition to debug registers.
typedef enum {
  IDSOPT_CMN_CPU_LOG_TRANSPARENT_ERRORS_AUTO = 3,///<Auto
  IDSOPT_CMN_CPU_LOG_TRANSPARENT_ERRORS_DISABLED = 0,///<Disabled
  IDSOPT_CMN_CPU_LOG_TRANSPARENT_ERRORS_ENABLED = 1,///<Enabled
} IDSOPT_CMN_CPU_LOG_TRANSPARENT_ERRORS;

///AVX512
///Enable/Disable AVX512.
typedef enum {
  IDSOPT_CMN_CPU_AVX512_DISABLED = 0,///<Disabled
  IDSOPT_CMN_CPU_AVX512_ENABLED = 1,///<Enabled
  IDSOPT_CMN_CPU_AVX512_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_CPU_AVX512;

///ERMSB Caching Behavior
///Enable: Optimized caching for REPs.\nDisable: Legacy caching behavior for REPs.
typedef enum {
  IDSOPT_CMN_CPU_DIS_FST_STR_ERMSB_DISABLED = 1,///<Disabled
  IDSOPT_CMN_CPU_DIS_FST_STR_ERMSB_ENABLED = 0,///<Enabled
  IDSOPT_CMN_CPU_DIS_FST_STR_ERMSB_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_CPU_DIS_FST_STR_ERMSB;

///MONITOR and MWAIT disable
///The MONITOR, MWAIT, MONITORX, and MWAITX opcodes become invalid, when Enabled.
typedef enum {
  IDSOPT_CMN_CPU_MON_MWAIT_DIS_ENABLED = 1,///<Enabled
  IDSOPT_CMN_CPU_MON_MWAIT_DIS_DISABLED = 0,///<Disabled
  IDSOPT_CMN_CPU_MON_MWAIT_DIS_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_CPU_MON_MWAIT_DIS;

///CPU Speculative Store Modes
///[Warning] If CPU Debug Options/CPU Aggressive Store Speculation is set to "Yes", it'll override the setting of this option and enforce the CPU Speculative Store Mode to be Aggressive.\n\nBalanced:\nStore instructions may delay sending out their invalidations to remote cacheline copies when the cacheline is present but not in a writable state in the local cache.\n\nMore Speculative:\nStore instructions will send out invalidations to remote cacheline copies as soon as possible.\n\nLess Speculative:\nStore instructions may delay sending out their invalidations to remote cacheline copies when the cacheline is not present in the local cache or not in a writable state in the local cache.
typedef enum {
  IDSOPT_CPU_SPECULATIVE_STORE_MODES_AUTO = 0xFF,///<Auto
  IDSOPT_CPU_SPECULATIVE_STORE_MODES_BALANCED = 0,///<Balanced
  IDSOPT_CPU_SPECULATIVE_STORE_MODES_MORESPECULATIVE = 1,///<More Speculative
  IDSOPT_CPU_SPECULATIVE_STORE_MODES_LESSSPECULATIVE = 2,///<Less Speculative
} IDSOPT_CPU_SPECULATIVE_STORE_MODES;

///Fast Short REP MOVSB (FSRM)
///Default is 1, can be set to zero for analysis purposes as long as OS supports it.
typedef enum {
  IDSOPT_CMN_CPU_FSRM_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_CPU_FSRM_ENABLED = 1,///<Enabled
  IDSOPT_CMN_CPU_FSRM_DISABLED = 0,///<Disabled
} IDSOPT_CMN_CPU_FSRM;

///PauseCntSel_1_0
///Number of cycles dispatch is stalled for a thread after dispatching PAUSE instruction. POR is 64 cycles.
typedef enum {
  IDSOPT_CMN_CPU_PAUSE_CNT_SEL_1_0_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_CPU_PAUSE_CNT_SEL_1_0_16CYCLES = 0,///<16 cycles
  IDSOPT_CMN_CPU_PAUSE_CNT_SEL_1_0_32CYCLES = 1,///<32 cycles
  IDSOPT_CMN_CPU_PAUSE_CNT_SEL_1_0_64CYCLES = 2,///<64 cycles
  IDSOPT_CMN_CPU_PAUSE_CNT_SEL_1_0_128CYCLES = 3,///<128 cycles
} IDSOPT_CMN_CPU_PAUSE_CNT_SEL_1_0;

///Prefetch/Request Throttle
///Enables XI logic which calculates average latency, updates throttle level, and sends throttle level messages to L2
typedef enum {
  IDSOPT_CMN_CPU_PF_REQ_THR_EN_DISABLE = 0,///<Disable
  IDSOPT_CMN_CPU_PF_REQ_THR_EN_ENABLE = 1,///<Enable
  IDSOPT_CMN_CPU_PF_REQ_THR_EN_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_CPU_PF_REQ_THR_EN;

///CMC H/W Error Notification type
typedef enum {
  IDSOPT_CMN_CMC_NOTIFICATION_TYPE_POLLED = 0,///<POLLED
  IDSOPT_CMN_CMC_NOTIFICATION_TYPE_CMCI = 5,///<CMCI
} IDSOPT_CMN_CMC_NOTIFICATION_TYPE;

///Scan Dump Debug Enable
///This option operates like below settings (when enabled) to avoid the reset caused by Syncflood, etc\nAPCB_TOKEN_UID_PSP_ENABLE_DEBUG_MODE = 1 (Enabled)\nAPCB_TOKEN_UID_DF_EXT_IP_SYNC_FLOOD_PROP = 1 (Sync flood disabled)\nPcdResetCpuOnSyncFlood = 0 (Disable)
typedef enum {
  IDSOPT_CMN_CPU_SCAN_DUMP_DBG_EN_DISABLE = 0,///<Disable
  IDSOPT_CMN_CPU_SCAN_DUMP_DBG_EN_ENABLE = 1,///<Enable
} IDSOPT_CMN_CPU_SCAN_DUMP_DBG_EN;

///MCAX 64 bank support
///Enable 64 MCA banks per thread mapping.
typedef enum {
  IDSOPT_CMN_CPU_MCAX64_BANK_SUPPORT_DISABLED = 0,///<Disabled
  IDSOPT_CMN_CPU_MCAX64_BANK_SUPPORT_ENABLED = 1,///<Enabled
  IDSOPT_CMN_CPU_MCAX64_BANK_SUPPORT_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_CPU_MCAX64_BANK_SUPPORT;

///Adaptive Allocation (AA)
///Disable to use fixed L2 replacement/allocation policy
typedef enum {
  IDSOPT_CMN_CPU_ADAPTIVE_ALLOC_ENABLED = 0,///<Enabled
  IDSOPT_CMN_CPU_ADAPTIVE_ALLOC_DISABLED = 1,///<Disabled
  IDSOPT_CMN_CPU_ADAPTIVE_ALLOC_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_CPU_ADAPTIVE_ALLOC;

///Latency Under Load (LUL)
///Enabling may improve latency in heavy BW scenarios.  May slightly reduce peak CCD BW.
typedef enum {
  IDSOPT_CPU_LATENCY_UNDER_LOAD_AUTO = 0xFF,///<Auto
  IDSOPT_CPU_LATENCY_UNDER_LOAD_ENABLED = 0,///<Enabled
  IDSOPT_CPU_LATENCY_UNDER_LOAD_DISABLED = 1,///<Disabled
} IDSOPT_CPU_LATENCY_UNDER_LOAD;

///Core Trace Dump Enable
///Enable/Disable Core Trace Dump Feature.
typedef enum {
  IDSOPT_CMN_CORE_TRACE_DUMP_EN_DISABLE = 0,///<Disable
  IDSOPT_CMN_CORE_TRACE_DUMP_EN_ENABLE = 1,///<Enable
} IDSOPT_CMN_CORE_TRACE_DUMP_EN;

///FP512
///Indicates support for downgrading FP512 datapath to FP256. Enable = 512bit datapath, Disable=256bit datapath.
typedef enum {
  IDSOPT_CMN_CPU_F_P512_DISABLED = 1,///<Disabled
  IDSOPT_CMN_CPU_F_P512_ENABLED = 0,///<Enabled
  IDSOPT_CMN_CPU_F_P512_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_CPU_F_P512;

///AMD_ERMSB Reporting
///Report presence of AMD_ERMSB via CPUID. By default, this is reported as true (Enable), the field can be set to false for analysis purposes as long as OS supports it.
typedef enum {
  IDSOPT_CMN_CPU_AMD_ERMSB_REPO_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_CPU_AMD_ERMSB_REPO_DISABLE = 0,///<Disable
  IDSOPT_CMN_CPU_AMD_ERMSB_REPO_ENABLE = 1,///<Enable
} IDSOPT_CMN_CPU_AMD_ERMSB_REPO;

///OC Mode
///Select overclock operation modes
typedef enum {
  IDSOPT_CMN_CPU_OC_MODE_NORMALOPERATION = 0,///<Normal Operation
  IDSOPT_CMN_CPU_OC_MODE_CUSTOMIZED = 5,///<Customized
} IDSOPT_CMN_CPU_OC_MODE;

///DownCore Mode
///Select the DownCore Mode
typedef enum {
  IDSOPT_CMN_CPU_DOWNCORE_MODE_ENABLEMENTOPTION = 0,///<Enablement Option
  IDSOPT_CMN_CPU_DOWNCORE_MODE_BITMAP = 1,///<Bitmap
} IDSOPT_CMN_CPU_DOWNCORE_MODE;

///Pstates Disclaimer
///Legal Disclaimer
///Pstates Disclaimer 1
///Legal Disclaimer
///Custom Pstate0
///Disable - disable this Pstate\nCustom - customize this Pstate, applicable only if PcdOcDisable=FALSE\n\nWARNING - DAMAGE CAUSED BY USE OF YOUR AMD PROCESSOR OUTSIDE OF SPECIFICATION OR IN EXCESS OF FACTORY SETTINGS ARE NOT COVERED UNDER YOUR AMD PRODUCT WARRANTY AND MAY NOT BE COVERED BY YOUR SYSTEM MANUFACTURER'S WARRANTY.\nOperating your AMD processor outside of specification or in excess of factory settings, including but not limited to overclocking, may damage or shorten the life of your processor or other system components, create system instabilities (e.g., data loss and corrupted images) and in extreme cases may result in total system failure. AMD does not provide support or service for issues or damages related to use of an AMD processor outside of processor specifications or in excess of factory settings.\n
typedef enum {
  IDSOPT_CPU_PST_CUSTOM_P0_CUSTOM = 1,///<Custom
  IDSOPT_CPU_PST_CUSTOM_P0_AUTO = 2,///<Auto
} IDSOPT_CPU_PST_CUSTOM_P0;

///Pstate0 Freq (MHz)
///Specifies core frequency (MHz)
#define IDSOPT_CPU_PST0_FREQ_MIN 0 ///< Min of Pstate0 Freq (MHz)
#define IDSOPT_CPU_PST0_FREQ_MAX 0xFFFFFFFF ///< Max of Pstate0 Freq (MHz)

///Frequency (MHz)
///Current core frequency in MHz
#define IDSOPT_CPU_COF_P0_MIN 0 ///< Min of Frequency (MHz)
#define IDSOPT_CPU_COF_P0_MAX 0xffffffff ///< Max of Frequency (MHz)

///Voltage (uV)
///Voltage in uV (1V = 1000 * 1000 uV)
#define IDSOPT_CPU_VOLTAGE_P0_MIN 0 ///< Min of Voltage (uV)
#define IDSOPT_CPU_VOLTAGE_P0_MAX 0xffffffff ///< Max of Voltage (uV)

///Pstate0 FID
///Specifies the core frequency multiplier. COF = 5MHz * FID
#define IDSOPT_CPU_PST0_FID_MIN 0x10 ///< Min of Pstate0 FID
#define IDSOPT_CPU_PST0_FID_MAX 0x7ff ///< Max of Pstate0 FID

///Pstate0 VID
///Specifies the core voltage.
#define IDSOPT_CPU_PST0_VID_MIN 0 ///< Min of Pstate0 VID
#define IDSOPT_CPU_PST0_VID_MAX 0x1ff ///< Max of Pstate0 VID

///Custom Pstate1
///Disabled - disable this Pstate\nCustom - customize this Pstate, applicable only if PcdOcDisable=FALSE\n\nWARNING - DAMAGE CAUSED BY USE OF YOUR AMD PROCESSOR OUTSIDE OF SPECIFICATION OR IN EXCESS OF FACTORY SETTINGS ARE NOT COVERED UNDER YOUR AMD PRODUCT WARRANTY AND MAY NOT BE COVERED BY YOUR SYSTEM MANUFACTURER'S WARRANTY.\nOperating your AMD processor outside of specification or in excess of factory settings, including but not limited to overclocking, may damage or shorten the life of your processor or other system components, create system instabilities (e.g., data loss and corrupted images) and in extreme cases may result in total system failure. AMD does not provide support or service for issues or damages related to use of an AMD processor outside of processor specifications or in excess of factory settings.\n
typedef enum {
  IDSOPT_CPU_PST_CUSTOM_P1_DISABLED = 0,///<Disabled
  IDSOPT_CPU_PST_CUSTOM_P1_CUSTOM = 1,///<Custom
  IDSOPT_CPU_PST_CUSTOM_P1_AUTO = 2,///<Auto
} IDSOPT_CPU_PST_CUSTOM_P1;

///Frequency (MHz)
#define IDSOPT_CPU_COF_P1_MIN 0 ///< Min of Frequency (MHz)
#define IDSOPT_CPU_COF_P1_MAX 0xffffffff ///< Max of Frequency (MHz)

///Voltage (uV)
#define IDSOPT_CPU_VOLTAGE_P1_MIN 0 ///< Min of Voltage (uV)
#define IDSOPT_CPU_VOLTAGE_P1_MAX 0xffffffff ///< Max of Voltage (uV)

///Pstate1 FID
///Specifies the core frequency multiplier. COF = 5MHz * FID
#define IDSOPT_CPU_PST1_FID_MIN 0x10 ///< Min of Pstate1 FID
#define IDSOPT_CPU_PST1_FID_MAX 0xff ///< Max of Pstate1 FID

///Pstate1 VID
///Specifies the core voltage.
#define IDSOPT_CPU_PST1_VID_MIN 0 ///< Min of Pstate1 VID
#define IDSOPT_CPU_PST1_VID_MAX 0xff ///< Max of Pstate1 VID

///Custom Pstate2
///Disabled - disable this Pstate\nCustom - customize this Pstate, applicable only if PcdOcDisable=FALSE\n\nWARNING - DAMAGE CAUSED BY USE OF YOUR AMD PROCESSOR OUTSIDE OF SPECIFICATION OR IN EXCESS OF FACTORY SETTINGS ARE NOT COVERED UNDER YOUR AMD PRODUCT WARRANTY AND MAY NOT BE COVERED BY YOUR SYSTEM MANUFACTURER'S WARRANTY.\nOperating your AMD processor outside of specification or in excess of factory settings, including but not limited to overclocking, may damage or shorten the life of your processor or other system components, create system instabilities (e.g., data loss and corrupted images) and in extreme cases may result in total system failure. AMD does not provide support or service for issues or damages related to use of an AMD processor outside of processor specifications or in excess of factory settings.\n
typedef enum {
  IDSOPT_CPU_PST_CUSTOM_P2_DISABLED = 0,///<Disabled
  IDSOPT_CPU_PST_CUSTOM_P2_CUSTOM = 1,///<Custom
  IDSOPT_CPU_PST_CUSTOM_P2_AUTO = 2,///<Auto
} IDSOPT_CPU_PST_CUSTOM_P2;

///Frequency (MHz)
#define IDSOPT_CPU_COF_P2_MIN 0 ///< Min of Frequency (MHz)
#define IDSOPT_CPU_COF_P2_MAX 0xffffffff ///< Max of Frequency (MHz)

///Voltage (uV)
#define IDSOPT_CPU_VOLTAGE_P2_MIN 0 ///< Min of Voltage (uV)
#define IDSOPT_CPU_VOLTAGE_P2_MAX 0xffffffff ///< Max of Voltage (uV)

///Pstate2 FID
///Specifies the core frequency multiplier. COF = 5MHz * FID
#define IDSOPT_CPU_PST2_FID_MIN 0x10 ///< Min of Pstate2 FID
#define IDSOPT_CPU_PST2_FID_MAX 0xff ///< Max of Pstate2 FID

///Pstate2 VID
///Specifies the core voltage.
#define IDSOPT_CPU_PST2_VID_MIN 0 ///< Min of Pstate2 VID
#define IDSOPT_CPU_PST2_VID_MAX 0xff ///< Max of Pstate2 VID

///Custom Pstate3
///Disabled - disable this Pstate\nCustom - customize this Pstate, applicable only if PcdOcDisable=FALSE\n\nWARNING - DAMAGE CAUSED BY USE OF YOUR AMD PROCESSOR OUTSIDE OF SPECIFICATION OR IN EXCESS OF FACTORY SETTINGS ARE NOT COVERED UNDER YOUR AMD PRODUCT WARRANTY AND MAY NOT BE COVERED BY YOUR SYSTEM MANUFACTURER'S WARRANTY.\nOperating your AMD processor outside of specification or in excess of factory settings, including but not limited to overclocking, may damage or shorten the life of your processor or other system components, create system instabilities (e.g., data loss and corrupted images) and in extreme cases may result in total system failure. AMD does not provide support or service for issues or damages related to use of an AMD processor outside of processor specifications or in excess of factory settings.\n
typedef enum {
  IDSOPT_CPU_PST_CUSTOM_P3_DISABLED = 0,///<Disabled
  IDSOPT_CPU_PST_CUSTOM_P3_CUSTOM = 1,///<Custom
  IDSOPT_CPU_PST_CUSTOM_P3_AUTO = 2,///<Auto
} IDSOPT_CPU_PST_CUSTOM_P3;

///Frequency (MHz)
#define IDSOPT_CPU_COF_P3_MIN 0 ///< Min of Frequency (MHz)
#define IDSOPT_CPU_COF_P3_MAX 0xffffffff ///< Max of Frequency (MHz)

///Voltage (uV)
#define IDSOPT_CPU_VOLTAGE_P3_MIN 0 ///< Min of Voltage (uV)
#define IDSOPT_CPU_VOLTAGE_P3_MAX 0xffffffff ///< Max of Voltage (uV)

///Pstate3 FID
///Specifies the core frequency multiplier. COF = 5MHz * FID
#define IDSOPT_CPU_PST3_FID_MIN 0x10 ///< Min of Pstate3 FID
#define IDSOPT_CPU_PST3_FID_MAX 0xff ///< Max of Pstate3 FID

///Pstate3 VID
///Specifies the core voltage.
#define IDSOPT_CPU_PST3_VID_MIN 0 ///< Min of Pstate3 VID
#define IDSOPT_CPU_PST3_VID_MAX 0xff ///< Max of Pstate3 VID

///Custom Pstate4
///Disabled - disable this Pstate\nCustom - customize this Pstate, applicable only if PcdOcDisable=FALSE\n\nWARNING - DAMAGE CAUSED BY USE OF YOUR AMD PROCESSOR OUTSIDE OF SPECIFICATION OR IN EXCESS OF FACTORY SETTINGS ARE NOT COVERED UNDER YOUR AMD PRODUCT WARRANTY AND MAY NOT BE COVERED BY YOUR SYSTEM MANUFACTURER'S WARRANTY.\nOperating your AMD processor outside of specification or in excess of factory settings, including but not limited to overclocking, may damage or shorten the life of your processor or other system components, create system instabilities (e.g., data loss and corrupted images) and in extreme cases may result in total system failure. AMD does not provide support or service for issues or damages related to use of an AMD processor outside of processor specifications or in excess of factory settings.\n
typedef enum {
  IDSOPT_CPU_PST_CUSTOM_P4_DISABLED = 0,///<Disabled
  IDSOPT_CPU_PST_CUSTOM_P4_CUSTOM = 1,///<Custom
  IDSOPT_CPU_PST_CUSTOM_P4_AUTO = 2,///<Auto
} IDSOPT_CPU_PST_CUSTOM_P4;

///Frequency (MHz)
#define IDSOPT_CPU_COF_P4_MIN 0 ///< Min of Frequency (MHz)
#define IDSOPT_CPU_COF_P4_MAX 0xffffffff ///< Max of Frequency (MHz)

///Voltage (uV)
#define IDSOPT_CPU_VOLTAGE_P4_MIN 0 ///< Min of Voltage (uV)
#define IDSOPT_CPU_VOLTAGE_P4_MAX 0xffffffff ///< Max of Voltage (uV)

///Pstate4 FID
///Specifies the core frequency multiplier. COF = 5MHz * FID
#define IDSOPT_CPU_PST4_FID_MIN 0x10 ///< Min of Pstate4 FID
#define IDSOPT_CPU_PST4_FID_MAX 0xff ///< Max of Pstate4 FID

///Pstate4 VID
///Specifies the core voltage.
#define IDSOPT_CPU_PST4_VID_MIN 0 ///< Min of Pstate4 VID
#define IDSOPT_CPU_PST4_VID_MAX 0xff ///< Max of Pstate4 VID

///Custom Pstate5
///Disabled - disable this Pstate\nCustom - customize this Pstate, applicable only if PcdOcDisable=FALSE\n\nWARNING - DAMAGE CAUSED BY USE OF YOUR AMD PROCESSOR OUTSIDE OF SPECIFICATION OR IN EXCESS OF FACTORY SETTINGS ARE NOT COVERED UNDER YOUR AMD PRODUCT WARRANTY AND MAY NOT BE COVERED BY YOUR SYSTEM MANUFACTURER'S WARRANTY.\nOperating your AMD processor outside of specification or in excess of factory settings, including but not limited to overclocking, may damage or shorten the life of your processor or other system components, create system instabilities (e.g., data loss and corrupted images) and in extreme cases may result in total system failure. AMD does not provide support or service for issues or damages related to use of an AMD processor outside of processor specifications or in excess of factory settings.\n
typedef enum {
  IDSOPT_CPU_PST_CUSTOM_P5_DISABLED = 0,///<Disabled
  IDSOPT_CPU_PST_CUSTOM_P5_CUSTOM = 1,///<Custom
  IDSOPT_CPU_PST_CUSTOM_P5_AUTO = 2,///<Auto
} IDSOPT_CPU_PST_CUSTOM_P5;

///Frequency (MHz)
#define IDSOPT_CPU_COF_P5_MIN 0 ///< Min of Frequency (MHz)
#define IDSOPT_CPU_COF_P5_MAX 0xffffffff ///< Max of Frequency (MHz)

///Voltage (uV)
#define IDSOPT_CPU_VOLTAGE_P5_MIN 0 ///< Min of Voltage (uV)
#define IDSOPT_CPU_VOLTAGE_P5_MAX 0xffffffff ///< Max of Voltage (uV)

///Pstate5 FID
///Specifies the core frequency multiplier. COF = 5MHz * FID
#define IDSOPT_CPU_PST5_FID_MIN 0x10 ///< Min of Pstate5 FID
#define IDSOPT_CPU_PST5_FID_MAX 0xff ///< Max of Pstate5 FID

///Pstate5 VID
///Specifies the core voltage.
#define IDSOPT_CPU_PST5_VID_MIN 0 ///< Min of Pstate5 VID
#define IDSOPT_CPU_PST5_VID_MAX 0xff ///< Max of Pstate5 VID

///Custom Pstate6
///Disabled - disable this Pstate\nCustom - customize this Pstate, applicable only if PcdOcDisable=FALSE\n\nWARNING - DAMAGE CAUSED BY USE OF YOUR AMD PROCESSOR OUTSIDE OF SPECIFICATION OR IN EXCESS OF FACTORY SETTINGS ARE NOT COVERED UNDER YOUR AMD PRODUCT WARRANTY AND MAY NOT BE COVERED BY YOUR SYSTEM MANUFACTURER'S WARRANTY.\nOperating your AMD processor outside of specification or in excess of factory settings, including but not limited to overclocking, may damage or shorten the life of your processor or other system components, create system instabilities (e.g., data loss and corrupted images) and in extreme cases may result in total system failure. AMD does not provide support or service for issues or damages related to use of an AMD processor outside of processor specifications or in excess of factory settings.\n
typedef enum {
  IDSOPT_CPU_PST_CUSTOM_P6_DISABLED = 0,///<Disabled
  IDSOPT_CPU_PST_CUSTOM_P6_CUSTOM = 1,///<Custom
  IDSOPT_CPU_PST_CUSTOM_P6_AUTO = 2,///<Auto
} IDSOPT_CPU_PST_CUSTOM_P6;

///Frequency (MHz)
#define IDSOPT_CPU_COF_P6_MIN 0 ///< Min of Frequency (MHz)
#define IDSOPT_CPU_COF_P6_MAX 0xffffffff ///< Max of Frequency (MHz)

///Voltage (uV)
#define IDSOPT_CPU_VOLTAGE_P6_MIN 0 ///< Min of Voltage (uV)
#define IDSOPT_CPU_VOLTAGE_P6_MAX 0xffffffff ///< Max of Voltage (uV)

///Pstate6 FID
///Specifies the core frequency multiplier. COF = 5MHz * FID
#define IDSOPT_CPU_PST6_FID_MIN 0x10 ///< Min of Pstate6 FID
#define IDSOPT_CPU_PST6_FID_MAX 0xff ///< Max of Pstate6 FID

///Pstate6 VID
///Specifies the core voltage.
#define IDSOPT_CPU_PST6_VID_MIN 0 ///< Min of Pstate6 VID
#define IDSOPT_CPU_PST6_VID_MAX 0xff ///< Max of Pstate6 VID

///Custom Pstate7
///Disabled - disable this Pstate\nCustom - customize this Pstate, applicable only if PcdOcDisable=FALSE\n\nWARNING - DAMAGE CAUSED BY USE OF YOUR AMD PROCESSOR OUTSIDE OF SPECIFICATION OR IN EXCESS OF FACTORY SETTINGS ARE NOT COVERED UNDER YOUR AMD PRODUCT WARRANTY AND MAY NOT BE COVERED BY YOUR SYSTEM MANUFACTURER'S WARRANTY.\nOperating your AMD processor outside of specification or in excess of factory settings, including but not limited to overclocking, may damage or shorten the life of your processor or other system components, create system instabilities (e.g., data loss and corrupted images) and in extreme cases may result in total system failure. AMD does not provide support or service for issues or damages related to use of an AMD processor outside of processor specifications or in excess of factory settings.\n
typedef enum {
  IDSOPT_CPU_PST_CUSTOM_P7_DISABLED = 0,///<Disabled
  IDSOPT_CPU_PST_CUSTOM_P7_CUSTOM = 1,///<Custom
  IDSOPT_CPU_PST_CUSTOM_P7_AUTO = 2,///<Auto
} IDSOPT_CPU_PST_CUSTOM_P7;

///Frequency (MHz)
#define IDSOPT_CPU_COF_P7_MIN 0 ///< Min of Frequency (MHz)
#define IDSOPT_CPU_COF_P7_MAX 0xffffffff ///< Max of Frequency (MHz)

///Voltage (uV)
#define IDSOPT_CPU_VOLTAGE_P7_MIN 0 ///< Min of Voltage (uV)
#define IDSOPT_CPU_VOLTAGE_P7_MAX 0xffffffff ///< Max of Voltage (uV)

///Pstate7 FID
///Specifies the core frequency multiplier. COF = 5MHz * FID
#define IDSOPT_CPU_PST7_FID_MIN 0x10 ///< Min of Pstate7 FID
#define IDSOPT_CPU_PST7_FID_MAX 0xff ///< Max of Pstate7 FID

///Pstate7 VID
///Specifies the core voltage.
#define IDSOPT_CPU_PST7_VID_MIN 0 ///< Min of Pstate7 VID
#define IDSOPT_CPU_PST7_VID_MAX 0xff ///< Max of Pstate7 VID

///CCD 0 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD0_DOWNCORE_BIT_MAP_MIN 0 ///< Min of CCD 0 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD0_DOWNCORE_BIT_MAP_MAX 0xffffffff ///< Max of CCD 0 DownCore Bitmap

///CCD 1 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD1_DOWNCORE_BIT_MAP_MIN 0 ///< Min of CCD 1 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD1_DOWNCORE_BIT_MAP_MAX 0xffffffff ///< Max of CCD 1 DownCore Bitmap

///CCD 2 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD2_DOWNCORE_BIT_MAP_MIN 0 ///< Min of CCD 2 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD2_DOWNCORE_BIT_MAP_MAX 0xffffffff ///< Max of CCD 2 DownCore Bitmap

///CCD 3 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD3_DOWNCORE_BIT_MAP_MIN 0 ///< Min of CCD 3 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD3_DOWNCORE_BIT_MAP_MAX 0xffffffff ///< Max of CCD 3 DownCore Bitmap

///CCD 4 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD4_DOWNCORE_BIT_MAP_MIN 0 ///< Min of CCD 4 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD4_DOWNCORE_BIT_MAP_MAX 0xffffffff ///< Max of CCD 4 DownCore Bitmap

///CCD 5 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD5_DOWNCORE_BIT_MAP_MIN 0 ///< Min of CCD 5 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD5_DOWNCORE_BIT_MAP_MAX 0xffffffff ///< Max of CCD 5 DownCore Bitmap

///CCD 6 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD6_DOWNCORE_BIT_MAP_MIN 0 ///< Min of CCD 6 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD6_DOWNCORE_BIT_MAP_MAX 0xffffffff ///< Max of CCD 6 DownCore Bitmap

///CCD 7 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD7_DOWNCORE_BIT_MAP_MIN 0 ///< Min of CCD 7 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD7_DOWNCORE_BIT_MAP_MAX 0xffffffff ///< Max of CCD 7 DownCore Bitmap

///CCD 8 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD8_DOWNCORE_BIT_MAP_MIN 0 ///< Min of CCD 8 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD8_DOWNCORE_BIT_MAP_MAX 0xffffffff ///< Max of CCD 8 DownCore Bitmap

///CCD 9 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD9_DOWNCORE_BIT_MAP_MIN 0 ///< Min of CCD 9 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD9_DOWNCORE_BIT_MAP_MAX 0xffffffff ///< Max of CCD 9 DownCore Bitmap

///CCD 10 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD10_DOWNCORE_BIT_MAP_MIN 0 ///< Min of CCD 10 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD10_DOWNCORE_BIT_MAP_MAX 0xffffffff ///< Max of CCD 10 DownCore Bitmap

///CCD 11 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD11_DOWNCORE_BIT_MAP_MIN 0 ///< Min of CCD 11 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD11_DOWNCORE_BIT_MAP_MAX 0xffffffff ///< Max of CCD 11 DownCore Bitmap

///CCD 12 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD12_DOWNCORE_BIT_MAP_MIN 0 ///< Min of CCD 12 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD12_DOWNCORE_BIT_MAP_MAX 0xffffffff ///< Max of CCD 12 DownCore Bitmap

///CCD 13 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD13_DOWNCORE_BIT_MAP_MIN 0 ///< Min of CCD 13 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD13_DOWNCORE_BIT_MAP_MAX 0xffffffff ///< Max of CCD 13 DownCore Bitmap

///CCD 14 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD14_DOWNCORE_BIT_MAP_MIN 0 ///< Min of CCD 14 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD14_DOWNCORE_BIT_MAP_MAX 0xffffffff ///< Max of CCD 14 DownCore Bitmap

///CCD 15 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD15_DOWNCORE_BIT_MAP_MIN 0 ///< Min of CCD 15 DownCore Bitmap
#define IDSOPT_CMN_CPU_CCD15_DOWNCORE_BIT_MAP_MAX 0xffffffff ///< Max of CCD 15 DownCore Bitmap

///CCD Control
///Sets the number of active CCDs.  Once this option has been used to remove any CCDs, a POWER CYCLE is required in order for future selections to take effect.
typedef enum {
  IDSOPT_CPU_CCD_CTRL_AUTO = 0,///<Auto
  IDSOPT_CPU_CCD_CTRL_2CCDS = 2,///<2 CCDs
  IDSOPT_CPU_CCD_CTRL_4CCDS = 4,///<4 CCDs
  IDSOPT_CPU_CCD_CTRL_6CCDS = 6,///<6 CCDs
  IDSOPT_CPU_CCD_CTRL_8CCDS = 8,///<8 CCDs
  IDSOPT_CPU_CCD_CTRL_10CCDS = 10,///<10 CCDs
  IDSOPT_CPU_CCD_CTRL_12CCDS = 12,///<12 CCDs
  IDSOPT_CPU_CCD_CTRL_14CCDS = 14,///<14 CCDs
} IDSOPT_CPU_CCD_CTRL;

///Core control
///Sets the number of cores to be used. Once this option has been used to remove any cores, a POWER CYCLE is required in order for future selections to take effect.
typedef enum {
  IDSOPT_CPU_CORE_CTRL_AUTO = 0,///<Auto
  IDSOPT_CPU_CORE_CTRL_ONE10 = 1,///<ONE (1 + 0)
  IDSOPT_CPU_CORE_CTRL_TWO20 = 3,///<TWO (2 + 0)
  IDSOPT_CPU_CORE_CTRL_THREE30 = 4,///<THREE (3 + 0)
  IDSOPT_CPU_CORE_CTRL_FOUR40 = 6,///<FOUR (4 + 0)
  IDSOPT_CPU_CORE_CTRL_FIVE50 = 8,///<FIVE (5 + 0)
  IDSOPT_CPU_CORE_CTRL_SIX60 = 9,///<SIX (6 + 0)
  IDSOPT_CPU_CORE_CTRL_SEVEN70 = 10,///<SEVEN (7 + 0)
  IDSOPT_CPU_CORE_CTRL_EIGHT80 = 16,///<EIGHT (8 + 0)
  IDSOPT_CPU_CORE_CTRL_NINE90 = 17,///<NINE (9 + 0)
  IDSOPT_CPU_CORE_CTRL_TEN100 = 18,///<TEN (10 + 0)
  IDSOPT_CPU_CORE_CTRL_ELEVEN110 = 19,///<ELEVEN (11 + 0)
  IDSOPT_CPU_CORE_CTRL_TWELVE120 = 20,///<TWELVE (12 + 0)
  IDSOPT_CPU_CORE_CTRL_THIRTEEN130 = 21,///<THIRTEEN (13 + 0)
  IDSOPT_CPU_CORE_CTRL_FOURTEEN140 = 22,///<FOURTEEN (14 + 0)
  IDSOPT_CPU_CORE_CTRL_FIFTEEN150 = 23,///<FIFTEEN (15 + 0)
} IDSOPT_CPU_CORE_CTRL;

///L1 Stream HW Prefetcher
///Option to Enable | Disable L1 Stream HW Prefetcher
typedef enum {
  IDSOPT_CMN_CPU_L1_STREAM_HW_PREFETCHER_DISABLE = 0,///<Disable
  IDSOPT_CMN_CPU_L1_STREAM_HW_PREFETCHER_ENABLE = 1,///<Enable
  IDSOPT_CMN_CPU_L1_STREAM_HW_PREFETCHER_AUTO = 3,///<Auto
} IDSOPT_CMN_CPU_L1_STREAM_HW_PREFETCHER;

///L1 Stride Prefetcher
///Uses memory access history of individual instructions to fetch additional lines when each access is a constant distance from the previous.
typedef enum {
  IDSOPT_CMN_CPU_L1_STRIDE_PREFETCHER_DISABLE = 0,///<Disable
  IDSOPT_CMN_CPU_L1_STRIDE_PREFETCHER_ENABLE = 1,///<Enable
  IDSOPT_CMN_CPU_L1_STRIDE_PREFETCHER_AUTO = 3,///<Auto
} IDSOPT_CMN_CPU_L1_STRIDE_PREFETCHER;

///L1 Region Prefetcher
///Uses memory access history to fetch additional lines when the data access for a given instruction tends to be followed by other data accesses.
typedef enum {
  IDSOPT_CMN_CPU_L1_REGION_PREFETCHER_DISABLE = 0,///<Disable
  IDSOPT_CMN_CPU_L1_REGION_PREFETCHER_ENABLE = 1,///<Enable
  IDSOPT_CMN_CPU_L1_REGION_PREFETCHER_AUTO = 3,///<Auto
} IDSOPT_CMN_CPU_L1_REGION_PREFETCHER;

///L2 Stream HW Prefetcher
///Option to Enable | Disable L2 Stream HW Prefetcher
typedef enum {
  IDSOPT_CMN_CPU_L2_STREAM_HW_PREFETCHER_DISABLE = 0,///<Disable
  IDSOPT_CMN_CPU_L2_STREAM_HW_PREFETCHER_ENABLE = 1,///<Enable
  IDSOPT_CMN_CPU_L2_STREAM_HW_PREFETCHER_AUTO = 3,///<Auto
} IDSOPT_CMN_CPU_L2_STREAM_HW_PREFETCHER;

///L2 Up/Down Prefetcher
///Uses memory access history to determine whether to fetch the next or previous line for all memory accesses.
typedef enum {
  IDSOPT_CMN_CPU_L2_UP_DOWN_PREFETCHER_DISABLE = 0,///<Disable
  IDSOPT_CMN_CPU_L2_UP_DOWN_PREFETCHER_ENABLE = 1,///<Enable
  IDSOPT_CMN_CPU_L2_UP_DOWN_PREFETCHER_AUTO = 3,///<Auto
} IDSOPT_CMN_CPU_L2_UP_DOWN_PREFETCHER;

///L1 Burst Prefetch Mode
///Option to Enable | Disable L1 Burst Prefetch Mode
typedef enum {
  IDSOPT_CMN_CPU_L1_BURST_PREFETCH_MODE_DISABLE = 0,///<Disable
  IDSOPT_CMN_CPU_L1_BURST_PREFETCH_MODE_ENABLE = 1,///<Enable
  IDSOPT_CMN_CPU_L1_BURST_PREFETCH_MODE_AUTO = 3,///<Auto
} IDSOPT_CMN_CPU_L1_BURST_PREFETCH_MODE;

///Core Watchdog Timer Enable
///Enable or disable CPU Watchdog Timer
typedef enum {
  IDSOPT_DBG_CPU_GEN_CPU_WDT_DISABLED = 0,///<Disabled
  IDSOPT_DBG_CPU_GEN_CPU_WDT_ENABLED = 1,///<Enabled
  IDSOPT_DBG_CPU_GEN_CPU_WDT_AUTO = 3,///<Auto
} IDSOPT_DBG_CPU_GEN_CPU_WDT;

///Core Watchdog Timer Interval
///Select CPU Watchdog Timer interval
typedef enum {
  IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_2681S = 0x100,///<2.681s
  IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_1340S = 0x200,///<1.340s
  IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_66941MS = 0x300,///<669.41ms
  IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_33405MS = 0x400,///<334.05ms
  IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_16637MS = 0x500,///<166.37ms
  IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_8253MS = 0x600,///<82.53ms
  IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_4061MS = 0x700,///<40.61ms
  IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_20970MS = 0x901,///<20.970ms
  IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_10484MS = 0x801,///<10.484ms
  IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_5241MS = 0x001,///<5.241ms
  IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_2620MS = 0x101,///<2.620ms
  IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_1309MS = 0x201,///<1.309ms
  IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_65408US = 0x301,///<654.08us
  IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_3264US = 0x401,///<326.4us
  IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_16256US = 0x501,///<162.56us
  IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_8064US = 0x601,///<80.64us
  IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_3968US = 0x701,///<39.68us
  IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_AUTO = 0xFFFF,///<Auto
} IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT;

///DF Watchdog Timer Interval
///Configure the Data Fabric watchdog timer interval.
typedef enum {
  IDSOPT_DF_CMN_WDT_INTERVAL_AUTO = 0xFF,///<Auto
  IDSOPT_DF_CMN_WDT_INTERVAL_41MS = 0,///<41 ms
  IDSOPT_DF_CMN_WDT_INTERVAL_166MS = 1,///<166 ms
  IDSOPT_DF_CMN_WDT_INTERVAL_334MS = 2,///<334 ms
  IDSOPT_DF_CMN_WDT_INTERVAL_669MS = 3,///<669 ms
  IDSOPT_DF_CMN_WDT_INTERVAL_134SECONDS = 4,///<1.34 seconds
  IDSOPT_DF_CMN_WDT_INTERVAL_268SECONDS = 5,///<2.68 seconds
  IDSOPT_DF_CMN_WDT_INTERVAL_536SECONDS = 6,///<5.36 seconds
} IDSOPT_DF_CMN_WDT_INTERVAL;

///Disable DF to external IP SyncFloodPropagation
///Disable SyncFlood to UMC & downstream slaves.
typedef enum {
  IDSOPT_DF_CMN_EXT_IP_SYNC_FLOOD_PROP_SYNCFLOODDISABLED = 1,///<Sync flood disabled
  IDSOPT_DF_CMN_EXT_IP_SYNC_FLOOD_PROP_SYNCFLOODENABLED = 0,///<Sync flood enabled
  IDSOPT_DF_CMN_EXT_IP_SYNC_FLOOD_PROP_AUTO = 0xFF,///<Auto
} IDSOPT_DF_CMN_EXT_IP_SYNC_FLOOD_PROP;

///Sync Flood Propagation to DF Components
///Control DF::PIEConfig[DisSyncFloodProp]
typedef enum {
  IDSOPT_DF_CMN_DIS_SYNC_FLOOD_PROP_SYNCFLOODDISABLED = 1,///<Sync flood disabled
  IDSOPT_DF_CMN_DIS_SYNC_FLOOD_PROP_SYNCFLOODENABLED = 0,///<Sync flood enabled
  IDSOPT_DF_CMN_DIS_SYNC_FLOOD_PROP_AUTO = 0xFF,///<Auto
} IDSOPT_DF_CMN_DIS_SYNC_FLOOD_PROP;

///Freeze DF module queues on error
///Controls DF::DfGlobalCtrl[DisImmSyncFloodOnFatalError]\nDisabling this option sets DF::DfGlobalCtrl[DisImmSyncFloodOnFatalError]
typedef enum {
  IDSOPT_DF_CMN_FREEZE_QUEUE_ERROR_DISABLED = 0,///<Disabled
  IDSOPT_DF_CMN_FREEZE_QUEUE_ERROR_ENABLED = 1,///<Enabled
  IDSOPT_DF_CMN_FREEZE_QUEUE_ERROR_AUTO = 3,///<Auto
} IDSOPT_DF_CMN_FREEZE_QUEUE_ERROR;

///CC6 memory region encryption
///Control whether or not the CC6 save/restore memory is encrypted
typedef enum {
  IDSOPT_DF_CMN_CC6_MEM_ENCRYPTION_DISABLED = 0,///<Disabled
  IDSOPT_DF_CMN_CC6_MEM_ENCRYPTION_ENABLED = 1,///<Enabled
  IDSOPT_DF_CMN_CC6_MEM_ENCRYPTION_AUTO = 3,///<Auto
} IDSOPT_DF_CMN_CC6_MEM_ENCRYPTION;

///CCD B/W Balance Throttle Level
///Enables throttling of memory traffic per CCD. Increased throttling can reduce imbalance across CCDs (expected to be rare).
typedef enum {
  IDSOPT_DF_CMN_CCD_BW_THROTTLE_LV_AUTO = 0xFF,///<Auto
  IDSOPT_DF_CMN_CCD_BW_THROTTLE_LV_LEVEL0 = 0,///<Level 0
  IDSOPT_DF_CMN_CCD_BW_THROTTLE_LV_LEVEL1 = 1,///<Level 1
  IDSOPT_DF_CMN_CCD_BW_THROTTLE_LV_LEVEL2 = 2,///<Level 2
  IDSOPT_DF_CMN_CCD_BW_THROTTLE_LV_LEVEL3 = 3,///<Level 3
  IDSOPT_DF_CMN_CCD_BW_THROTTLE_LV_LEVEL4 = 4,///<Level 4
} IDSOPT_DF_CMN_CCD_BW_THROTTLE_LV;

///Number of PCI Segments
typedef enum {
  IDSOPT_DF_DBG_NUM_PCI_SEGMENTS_1SEGMENT = 0x10000000,///<1 Segment
  IDSOPT_DF_DBG_NUM_PCI_SEGMENTS_2SEGMENTS = 0x20000000,///<2 Segments
  IDSOPT_DF_DBG_NUM_PCI_SEGMENTS_4SEGMENTS = 0x40000000,///<4 Segments
  IDSOPT_DF_DBG_NUM_PCI_SEGMENTS_AUTO = 0xFFFFFFFF,///<Auto
} IDSOPT_DF_DBG_NUM_PCI_SEGMENTS;

///CCM Throttler
///Limit peak CCM throughput
typedef enum {
  IDSOPT_DF_CMN_CCM_THROT_AUTO = 0xFF,///<Auto
  IDSOPT_DF_CMN_CCM_THROT_ENABLED = 1,///<Enabled
  IDSOPT_DF_CMN_CCM_THROT_DISABLED = 0,///<Disabled
} IDSOPT_DF_CMN_CCM_THROT;

///MemReqBandwidthControl[FineThrotHeavy]
///If CCM Throttler enabled , limit level.\nValue 0 to 31 where 31+1 = 100% BW\n
#define IDSOPT_DF_CMN_FINE_THROT_HEAVY_MIN 0 ///< Min of MemReqBandwidthControl[FineThrotHeavy]
#define IDSOPT_DF_CMN_FINE_THROT_HEAVY_MAX 0x1F ///< Max of MemReqBandwidthControl[FineThrotHeavy]

///MemReqBandwidthControl[FineThrotLight]
///If CCM Throttler enabled , limit level.\nValue 0 to 31 where 31+1 = 100% BW
#define IDSOPT_DF_CMN_FINE_THROT_LIGHT_MIN 0 ///< Min of MemReqBandwidthControl[FineThrotLight]
#define IDSOPT_DF_CMN_FINE_THROT_LIGHT_MAX 0x1F ///< Max of MemReqBandwidthControl[FineThrotLight]

///Clean Victim FTI Cmd Balancing
///Control Clean Victim FTI Cmd Balancing feature
typedef enum {
  IDSOPT_DF_CMN_CLEAN_VIC_FTI_CMD_BAL_DISABLED = 0,///<Disabled
  IDSOPT_DF_CMN_CLEAN_VIC_FTI_CMD_BAL_ENABLED = 1,///<Enabled
  IDSOPT_DF_CMN_CLEAN_VIC_FTI_CMD_BAL_AUTO = 0xFF,///<Auto
} IDSOPT_DF_CMN_CLEAN_VIC_FTI_CMD_BAL;

///CCMConfig5[ReqvReqNDImbThr]
///When Clean Victim FTI Cmd Balancing is Enabled, in the presence of pending WrSized requests and sufficient credits, begin sending pending primary channel VicBlkClns to the secondary (ND) req channel based on the indicated threshold.
typedef enum {
  IDSOPT_DF_CMN_REQV_REQ_ND_IMB_THR_AUTO = 0xFF,///<Auto
  IDSOPT_DF_CMN_REQV_REQ_ND_IMB_THR_1H = 1,///<1h
  IDSOPT_DF_CMN_REQV_REQ_ND_IMB_THR_2H = 2,///<2h
  IDSOPT_DF_CMN_REQV_REQ_ND_IMB_THR_3H = 3,///<3h
  IDSOPT_DF_CMN_REQV_REQ_ND_IMB_THR_4H = 4,///<4h
  IDSOPT_DF_CMN_REQV_REQ_ND_IMB_THR_5H = 5,///<5h
  IDSOPT_DF_CMN_REQV_REQ_ND_IMB_THR_6H = 6,///<6h
  IDSOPT_DF_CMN_REQV_REQ_ND_IMB_THR_7H = 7,///<7h
} IDSOPT_DF_CMN_REQV_REQ_ND_IMB_THR;

///CXL Strongly Ordered Writes
///Determines how the host treats Strongly ordered Writes (ItoMWr and MemWr) from CXL.cache devices. When disabled is chosen Strongly ordered Writes are downgraded to Weakly ordered Writes within the host. When one at a time is chosen the host throttles processing Strongly ordered Writes to a one at a time cadence.
typedef enum {
  IDSOPT_DF_CMN_CXL_STRONGLY_ORDERED_WRITES_DISABLED = 0,///<Disabled
  IDSOPT_DF_CMN_CXL_STRONGLY_ORDERED_WRITES_ONEATATIME = 1,///<One at a time
} IDSOPT_DF_CMN_CXL_STRONGLY_ORDERED_WRITES;

///Enhanced Partial Writes to Same Address
///Controls enhanced writes to the same address for requests less than 64B.
typedef enum {
  IDSOPT_DF_CMN_ENHANCED_PART_WR_DISABLED = 0,///<Disabled
  IDSOPT_DF_CMN_ENHANCED_PART_WR_ENABLED = 1,///<Enabled
  IDSOPT_DF_CMN_ENHANCED_PART_WR_AUTO = 0xFF,///<Auto
} IDSOPT_DF_CMN_ENHANCED_PART_WR;

///NUMA nodes per socket
///Specifies the number of desired NUMA nodes per socket.  Zero will attempt to interleave the two sockets together.
typedef enum {
  IDSOPT_DF_CMN_DRAM_NPS_NPS0 = 0,///<NPS0
  IDSOPT_DF_CMN_DRAM_NPS_NPS1 = 1,///<NPS1
  IDSOPT_DF_CMN_DRAM_NPS_NPS2 = 2,///<NPS2
  IDSOPT_DF_CMN_DRAM_NPS_NPS4 = 3,///<NPS4
  IDSOPT_DF_CMN_DRAM_NPS_AUTO = 7,///<Auto
} IDSOPT_DF_CMN_DRAM_NPS;

///Memory interleaving
///Allows for disabling memory interleaving.  Note that NUMA nodes per socket will be honored regardless of this setting.
typedef enum {
  IDSOPT_DF_CMN_MEM_INTLV_DISABLED = 0,///<Disabled
  IDSOPT_DF_CMN_MEM_INTLV_ENABLED = 1,///<Enabled
  IDSOPT_DF_CMN_MEM_INTLV_AUTO = 7,///<Auto
} IDSOPT_DF_CMN_MEM_INTLV;

///Mixed interleaved mode
///Allows for interleaving UMC and CXL together.
typedef enum {
  IDSOPT_DF_CMN_MIXED_INTERLEAVED_MODE_DISABLED = 0,///<Disabled
  IDSOPT_DF_CMN_MIXED_INTERLEAVED_MODE_ENABLED = 1,///<Enabled
  IDSOPT_DF_CMN_MIXED_INTERLEAVED_MODE_AUTO = 0xFF,///<Auto
} IDSOPT_DF_CMN_MIXED_INTERLEAVED_MODE;

///CXL Memory interleaving
///Allows for enabling/disabling CXL memory devices interleaving.\nOption inactive when 'CXL Memory Online/Offline' is enabled.
typedef enum {
  IDSOPT_DF_CMN_CXL_MEM_INTLV_DISABLED = 0,///<Disabled
  IDSOPT_DF_CMN_CXL_MEM_INTLV_ENABLED = 1,///<Enabled
  IDSOPT_DF_CMN_CXL_MEM_INTLV_AUTO = 0xFF,///<Auto
} IDSOPT_DF_CMN_CXL_MEM_INTLV;

///CXL Sublink interleaving
///Enable or disable CXL sublink interleaving.\nOption inactive when 'CXL Memory Online/Offline' is enabled.
typedef enum {
  IDSOPT_DF_CNLI_SUBLINK_INTERLEAVING_ENABLE = 1,///<Enable
  IDSOPT_DF_CNLI_SUBLINK_INTERLEAVING_DISABLE = 0,///<Disable
  IDSOPT_DF_CNLI_SUBLINK_INTERLEAVING_AUTO = 0xFF,///<Auto
} IDSOPT_DF_CNLI_SUBLINK_INTERLEAVING;

///DRAM map inversion
///Inverting the map will cause the highest memory channels to get assigned the lowest addresses in the system.
typedef enum {
  IDSOPT_DF_CMN_DRAM_MAP_INVERSION_DISABLED = 0,///<Disabled
  IDSOPT_DF_CMN_DRAM_MAP_INVERSION_ENABLED = 1,///<Enabled
  IDSOPT_DF_CMN_DRAM_MAP_INVERSION_AUTO = 3,///<Auto
} IDSOPT_DF_CMN_DRAM_MAP_INVERSION;

///Location of private memory regions
///Controls whether or not the private memory regions (PSP, SMU and CC6) are at the top of DRAM, at the top of 1st DRAM pair or distributed. Note that distributed requires memory on all dies. Note that it will always be at the top of DRAM if some dies do not have memory regardless of this option's setting. Also, Consolidation to 1st DRAM pair is only valid in the non-interleaved case.
typedef enum {
  IDSOPT_DF_CMN_CC6_ALLOCATION_SCHEME_DISTRIBUTED = 0,///<Distributed
  IDSOPT_DF_CMN_CC6_ALLOCATION_SCHEME_CONSOLIDATED = 1,///<Consolidated
  IDSOPT_DF_CMN_CC6_ALLOCATION_SCHEME_CONSOLIDATEDTO1STDRAMPAIR = 2,///<Consolidated to 1st DRAM pair
  IDSOPT_DF_CMN_CC6_ALLOCATION_SCHEME_AUTO = 0xFF,///<Auto
} IDSOPT_DF_CMN_CC6_ALLOCATION_SCHEME;

///ACPI SRAT L3 Cache As NUMA Domain
///Enabled: Each CCX in the system will be declared as a separate NUMA domain.\nDisabled: Memory Addressing \ NUMA nodes per socket will be declared.
typedef enum {
  IDSOPT_DF_CMN_ACPI_SRAT_L3_NUMA_DISABLED = 0,///<Disabled
  IDSOPT_DF_CMN_ACPI_SRAT_L3_NUMA_ENABLED = 1,///<Enabled
  IDSOPT_DF_CMN_ACPI_SRAT_L3_NUMA_AUTO = 255,///<Auto
} IDSOPT_DF_CMN_ACPI_SRAT_L3_NUMA;

///ACPI SLIT Distance Control
///Determines how the SLIT distances are declared.
typedef enum {
  IDSOPT_DF_CMN_ACPI_SLIT_DIST_CTRL_MANUAL = 0,///<Manual
  IDSOPT_DF_CMN_ACPI_SLIT_DIST_CTRL_AUTO = 0xFF,///<Auto
} IDSOPT_DF_CMN_ACPI_SLIT_DIST_CTRL;

///ACPI SLIT remote relative distance
///Set the remote socket distance for 2P systems as near (2.8) or far (3.2).
typedef enum {
  IDSOPT_DF_CMN_ACPI_SLIT_REMOTE_FAR_NEAR = 0,///<Near
  IDSOPT_DF_CMN_ACPI_SLIT_REMOTE_FAR_FAR = 1,///<Far
  IDSOPT_DF_CMN_ACPI_SLIT_REMOTE_FAR_AUTO = 255,///<Auto
} IDSOPT_DF_CMN_ACPI_SLIT_REMOTE_FAR;

///ACPI SLIT virtual distance
///Specify the distance between two virtual domains (see L3 Cache as NUMA Domain) in the same physical domain.
#define IDSOPT_DF_CMN_ACPI_SLIT_VIRTUAL_DIST_MIN 10 ///< Min of ACPI SLIT virtual distance
#define IDSOPT_DF_CMN_ACPI_SLIT_VIRTUAL_DIST_MAX 255 ///< Max of ACPI SLIT virtual distance

///ACPI SLIT same socket distance
///Specify the distance to other physical domains within the same socket.
#define IDSOPT_DF_CMN_ACPI_SLIT_LCL_DIST_MIN 10 ///< Min of ACPI SLIT same socket distance
#define IDSOPT_DF_CMN_ACPI_SLIT_LCL_DIST_MAX 255 ///< Max of ACPI SLIT same socket distance

///ACPI SLIT remote socket distance
///Specify the distance to domains on the remote socket.
#define IDSOPT_DF_CMN_ACPI_SLIT_RMT_DIST_MIN 10 ///< Min of ACPI SLIT remote socket distance
#define IDSOPT_DF_CMN_ACPI_SLIT_RMT_DIST_MAX 255 ///< Max of ACPI SLIT remote socket distance

///ACPI SLIT local CXL distance
///Specify the distance to a CXL domain on the same socket.
#define IDSOPT_DF_CMN_ACPI_SLIT_CXL_LCL_MIN 10 ///< Min of ACPI SLIT local CXL distance
#define IDSOPT_DF_CMN_ACPI_SLIT_CXL_LCL_MAX 255 ///< Max of ACPI SLIT local CXL distance

///ACPI SLIT remote CXL distance
///Specify the distance to a CXL domain on the other socket.
#define IDSOPT_DF_CMN_ACPI_SLIT_CXL_RMT_MIN 10 ///< Min of ACPI SLIT remote CXL distance
#define IDSOPT_DF_CMN_ACPI_SLIT_CXL_RMT_MAX 255 ///< Max of ACPI SLIT remote CXL distance

///GMI encryption control
///Control GMI link encryption. Program GMI key to enabling encryption.
typedef enum {
  IDSOPT_DF_CMN_GMI_ENCRYPTION_DISABLED = 0,///<Disabled
  IDSOPT_DF_CMN_GMI_ENCRYPTION_ENABLED = 1,///<Enabled
  IDSOPT_DF_CMN_GMI_ENCRYPTION_AUTO = 3,///<Auto
} IDSOPT_DF_CMN_GMI_ENCRYPTION;

///xGMI encryption control
///Control xGMI link encryption.  Program xGMI key to enabling encryption.
typedef enum {
  IDSOPT_DF_CMN_X_GMI_ENCRYPTION_DISABLED = 0,///<Disabled
  IDSOPT_DF_CMN_X_GMI_ENCRYPTION_ENABLED = 1,///<Enabled
  IDSOPT_DF_CMN_X_GMI_ENCRYPTION_AUTO = 3,///<Auto
} IDSOPT_DF_CMN_X_GMI_ENCRYPTION;

///xGMI Link Configuration
///Configures the number of xGMI2 links used on a multi-socket system.
typedef enum {
  IDSOPT_DF_DBG_XGMI_LINK_CFG_AUTO = 3,///<Auto
  IDSOPT_DF_DBG_XGMI_LINK_CFG_3XGMILINKS = 1,///<3 xGMI Links
  IDSOPT_DF_DBG_XGMI_LINK_CFG_4XGMILINKS = 2,///<4 xGMI Links
  IDSOPT_DF_DBG_XGMI_LINK_CFG_2XGMILINKS2PCILINKS = 4,///<2 xGMI Links + 2 PCI Links
} IDSOPT_DF_DBG_XGMI_LINK_CFG;

///4-link xGMI max speed
///Specifies the max frequency used for XGMI PState in a 4-link topology.
typedef enum {
  IDSOPT_DF_CMN4_LINK_MAX_XGMI_SPEED_20GBPS = 14,///<20Gbps
  IDSOPT_DF_CMN4_LINK_MAX_XGMI_SPEED_25GBPS = 19,///<25Gbps
  IDSOPT_DF_CMN4_LINK_MAX_XGMI_SPEED_32GBPS = 26,///<32Gbps
  IDSOPT_DF_CMN4_LINK_MAX_XGMI_SPEED_AUTO = 0xFF,///<Auto
} IDSOPT_DF_CMN4_LINK_MAX_XGMI_SPEED;

///3-link xGMI max speed
///Specifies the max frequency used for XGMI PState in a 3-link topology.
typedef enum {
  IDSOPT_DF_CMN3_LINK_MAX_XGMI_SPEED_20GBPS = 14,///<20Gbps
  IDSOPT_DF_CMN3_LINK_MAX_XGMI_SPEED_25GBPS = 19,///<25Gbps
  IDSOPT_DF_CMN3_LINK_MAX_XGMI_SPEED_32GBPS = 26,///<32Gbps
  IDSOPT_DF_CMN3_LINK_MAX_XGMI_SPEED_AUTO = 0xFF,///<Auto
} IDSOPT_DF_CMN3_LINK_MAX_XGMI_SPEED;

///xGMI CRC Scale
///Configure leaky bucket scale for xGMI and WAFL CRC errors. Every scale milliseconds an error will leak from the CRC counter.
#define IDSOPT_DF_XGMI_CRC_SCALE_MIN 0 ///< Min of xGMI CRC Scale
#define IDSOPT_DF_XGMI_CRC_SCALE_MAX 21 ///< Max of xGMI CRC Scale

///xGMI CRC Threshold
///Configure leaky bucket threshold for xGMI and WAFL CRC errors. If link CRC counter exceeds this threshold, an error will be logged.
#define IDSOPT_DF_XGMI_CRC_THRESHOLD_MIN 0 ///< Min of xGMI CRC Threshold
#define IDSOPT_DF_XGMI_CRC_THRESHOLD_MAX 255 ///< Max of xGMI CRC Threshold

///xGMI Preset Control
///Enable/Disable XGMI Preset Control options
typedef enum {
  IDSOPT_DF_XGMI_PRESET_CONTROL_DISABLED = 0,///<Disabled
  IDSOPT_DF_XGMI_PRESET_CONTROL_ENABLED = 1,///<Enabled
  IDSOPT_DF_XGMI_PRESET_CONTROL_AUTO = 0xFF,///<Auto
} IDSOPT_DF_XGMI_PRESET_CONTROL;

///xGMI Training Err Mask
///Enable/Disable XGMI training err mask
typedef enum {
  IDSOPT_DF_XGMI_TRAINING_ERR_MASK_DISABLE = 0,///<Disable
  IDSOPT_DF_XGMI_TRAINING_ERR_MASK_ENABLE = 1,///<Enable
  IDSOPT_DF_XGMI_TRAINING_ERR_MASK_AUTO = 0xff,///<Auto
} IDSOPT_DF_XGMI_TRAINING_ERR_MASK;

///Preset P11 (APCB)
///An APCB token that combined "Preset P11 Cmn1", "Preset P11 Cn" and "Preset P11 Cnp1". The APCB token was set in BIOS and used in ABL. Control xGMI preset settings.
#define IDSOPT_DF_XGMI_PRESET_P11_MIN 0 ///< Min of Preset P11 (APCB)
#define IDSOPT_DF_XGMI_PRESET_P11_MAX 0xFFFFFF ///< Max of Preset P11 (APCB)

///Preset P11 Cmn1
///Enable/Disable Preset P11 Cmn1 option on xGMI Global Preset List
#define IDSOPT_DF_XGMI_CMN1_P11_MIN 0 ///< Min of Preset P11 Cmn1
#define IDSOPT_DF_XGMI_CMN1_P11_MAX 0xFF ///< Max of Preset P11 Cmn1

///Preset P11 Cn
///Enable/Disable Preset P11 Cn option on xGMI Global Preset List
#define IDSOPT_DF_XGMI_CN_P11_MIN 0 ///< Min of Preset P11 Cn
#define IDSOPT_DF_XGMI_CN_P11_MAX 0xFF ///< Max of Preset P11 Cn

///Preset P11 Cnp1
///Enable/Disable Preset P11 Cnp1 option on xGMI Global Preset List
#define IDSOPT_DF_XGMI_CNP1_P11_MIN 0 ///< Min of Preset P11 Cnp1
#define IDSOPT_DF_XGMI_CNP1_P11_MAX 0xFF ///< Max of Preset P11 Cnp1

///Preset P12 (APCB)
///An APCB token that combined "Preset P12 Cmn1", "Preset P12 Cn" and "Preset P12 Cnp1". The APCB token was set in BIOS and used in ABL. Control xGMI preset settings.
#define IDSOPT_DF_XGMI_PRESET_P12_MIN 0 ///< Min of Preset P12 (APCB)
#define IDSOPT_DF_XGMI_PRESET_P12_MAX 0xFFFFFF ///< Max of Preset P12 (APCB)

///Preset P12 Cmn1
///Enable/Disable Preset P12 Cmn1 option on xGMI Global Preset List
#define IDSOPT_DF_XGMI_CMN1_P12_MIN 0 ///< Min of Preset P12 Cmn1
#define IDSOPT_DF_XGMI_CMN1_P12_MAX 0xFF ///< Max of Preset P12 Cmn1

///Preset P12 Cn
///Enable/Disable Preset P12 Cn option on xGMI Global Preset List
#define IDSOPT_DF_XGMI_CN_P12_MIN 0 ///< Min of Preset P12 Cn
#define IDSOPT_DF_XGMI_CN_P12_MAX 0xFF ///< Max of Preset P12 Cn

///Preset P12 Cnp1
///Enable/Disable Preset P12 Cnp1 option on xGMI Global Preset List
#define IDSOPT_DF_XGMI_CNP1_P12_MIN 0 ///< Min of Preset P12 Cnp1
#define IDSOPT_DF_XGMI_CNP1_P12_MAX 0xFF ///< Max of Preset P12 Cnp1

///Preset P13 (APCB)
///An APCB token that combined "Preset P13 Cmn1", "Preset P13 Cn" and "Preset P13 Cnp1". The APCB token was set in BIOS and used in ABL. Control xGMI preset settings.
#define IDSOPT_DF_XGMI_PRESET_P13_MIN 0 ///< Min of Preset P13 (APCB)
#define IDSOPT_DF_XGMI_PRESET_P13_MAX 0xFFFFFF ///< Max of Preset P13 (APCB)

///Preset P13 Cmn1
///Enable/Disable Preset P13 Cmn1 option on xGMI Global Preset List
#define IDSOPT_DF_XGMI_CMN1_P13_MIN 0 ///< Min of Preset P13 Cmn1
#define IDSOPT_DF_XGMI_CMN1_P13_MAX 0xFF ///< Max of Preset P13 Cmn1

///Preset P13 Cn
///Enable/Disable Preset P13 Cn option on xGMI Global Preset List
#define IDSOPT_DF_XGMI_CN_P13_MIN 0 ///< Min of Preset P13 Cn
#define IDSOPT_DF_XGMI_CN_P13_MAX 0xFF ///< Max of Preset P13 Cn

///Preset P13 Cnp1
///Enable/Disable Preset P13 Cnp1 option on xGMI Global Preset List
#define IDSOPT_DF_XGMI_CNP1_P13_MIN 0 ///< Min of Preset P13 Cnp1
#define IDSOPT_DF_XGMI_CNP1_P13_MAX 0xFF ///< Max of Preset P13 Cnp1

///Preset P14 (APCB)
///An APCB token that combined "Preset P14 Cmn1", "Preset P14 Cn" and "Preset P14 Cnp1". The APCB token was set in BIOS and used in ABL. Control xGMI preset settings.
#define IDSOPT_DF_XGMI_PRESET_P14_MIN 0 ///< Min of Preset P14 (APCB)
#define IDSOPT_DF_XGMI_PRESET_P14_MAX 0xFFFFFF ///< Max of Preset P14 (APCB)

///Preset P14 Cmn1
///Enable/Disable Preset P14 Cmn1 option on xGMI Global Preset List
#define IDSOPT_DF_XGMI_CMN1_P14_MIN 0 ///< Min of Preset P14 Cmn1
#define IDSOPT_DF_XGMI_CMN1_P14_MAX 0xFF ///< Max of Preset P14 Cmn1

///Preset P14 Cn
///Enable/Disable Preset P14 Cn option on xGMI Global Preset List
#define IDSOPT_DF_XGMI_CN_P14_MIN 0 ///< Min of Preset P14 Cn
#define IDSOPT_DF_XGMI_CN_P14_MAX 0xFF ///< Max of Preset P14 Cn

///Preset P14 Cnp1
///Enable/Disable Preset P14 Cnp1 option on xGMI Global Preset List
#define IDSOPT_DF_XGMI_CNP1_P14_MIN 0 ///< Min of Preset P14 Cnp1
#define IDSOPT_DF_XGMI_CNP1_P14_MAX 0xFF ///< Max of Preset P14 Cnp1

///Preset P15 (APCB)
///An APCB token that combined "Preset P15 Cmn1", "Preset P15 Cn" and "Preset P15 Cnp1". The APCB token was set in BIOS and used in ABL. Control xGMI preset settings.
#define IDSOPT_DF_XGMI_PRESET_P15_MIN 0 ///< Min of Preset P15 (APCB)
#define IDSOPT_DF_XGMI_PRESET_P15_MAX 0xFFFFFF ///< Max of Preset P15 (APCB)

///Preset P15 Cmn1
///Enable/Disable Preset P15 Cmn1 option on xGMI Global Preset List
#define IDSOPT_DF_XGMI_CMN1_P15_MIN 0 ///< Min of Preset P15 Cmn1
#define IDSOPT_DF_XGMI_CMN1_P15_MAX 0xFF ///< Max of Preset P15 Cmn1

///Preset P15 Cn
///Enable/Disable Preset P15 Cn option on xGMI Global Preset List
#define IDSOPT_DF_XGMI_CN_P15_MIN 0 ///< Min of Preset P15 Cn
#define IDSOPT_DF_XGMI_CN_P15_MAX 0xFF ///< Max of Preset P15 Cn

///Preset P15 Cnp1
///Enable/Disable Preset P15 Cnp1 option on xGMI Global Preset List
#define IDSOPT_DF_XGMI_CNP1_P15_MIN 0 ///< Min of Preset P15 Cnp1
#define IDSOPT_DF_XGMI_CNP1_P15_MAX 0xFF ///< Max of Preset P15 Cnp1

///Initial Preset Socket 0 Link 0 Pstate0/1/2/3 (APCB)
///An APCB token that combined "Initial Preset Socket 0 Link 0 Pstate0", "Initial Preset Socket 0 Link 0 Pstate1", "Initial Preset Socket 0 Link 0 Pstate2" and "Initial Preset Socket 0 Link 0 Pstate3". The APCB token was set in BIOS and used in ABL. Control xGMI initial preset.
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L0_MIN 0 ///< Min of Initial Preset Socket 0 Link 0 Pstate0/1/2/3 (APCB)
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L0_MAX 0xFFFF ///< Max of Initial Preset Socket 0 Link 0 Pstate0/1/2/3 (APCB)

///Initial Preset Socket 0 Link 0 Pstate0
///Enable/Disable Initial Preset Socket 0 Link 0 Pstate0
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L0_P0_MIN 0 ///< Min of Initial Preset Socket 0 Link 0 Pstate0
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L0_P0_MAX 0xF ///< Max of Initial Preset Socket 0 Link 0 Pstate0

///Initial Preset Socket 0 Link 0 Pstate1
///Enable/Disable Initial Preset Socket 0 Link 0 Pstate1
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L0_P1_MIN 0 ///< Min of Initial Preset Socket 0 Link 0 Pstate1
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L0_P1_MAX 0xF ///< Max of Initial Preset Socket 0 Link 0 Pstate1

///Initial Preset Socket 0 Link 0 Pstate2
///Enable/Disable Initial Preset Socket 0 Link 0 Pstate2
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L0_P2_MIN 0 ///< Min of Initial Preset Socket 0 Link 0 Pstate2
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L0_P2_MAX 0xF ///< Max of Initial Preset Socket 0 Link 0 Pstate2

///Initial Preset Socket 0 Link 0 Pstate3
///Enable/Disable Initial Preset Socket 0 Link 0 Pstate3
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L0_P3_MIN 0 ///< Min of Initial Preset Socket 0 Link 0 Pstate3
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L0_P3_MAX 0xF ///< Max of Initial Preset Socket 0 Link 0 Pstate3

///Initial Preset Socket 0 Link 1 Pstate0/1/2/3 (APCB)
///An APCB token that combined "Initial Preset Socket 0 Link 1 Pstate0", "Initial Preset Socket 0 Link 1 Pstate1", "Initial Preset Socket 0 Link 1 Pstate2" and "Initial Preset Socket 0 Link 1 Pstate3". The APCB token was set in BIOS and used in ABL. Control xGMI initial preset.
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L1_MIN 0 ///< Min of Initial Preset Socket 0 Link 1 Pstate0/1/2/3 (APCB)
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L1_MAX 0xFFFF ///< Max of Initial Preset Socket 0 Link 1 Pstate0/1/2/3 (APCB)

///Initial Preset Socket 0 Link 1 Pstate0
///Enable/Disable Initial Preset Socket 0 Link 1 Pstate0
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L1_P0_MIN 0 ///< Min of Initial Preset Socket 0 Link 1 Pstate0
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L1_P0_MAX 0xF ///< Max of Initial Preset Socket 0 Link 1 Pstate0

///Initial Preset Socket 0 Link 1 Pstate1
///Enable/Disable Initial Preset Socket 0 Link 1 Pstate1
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L1_P1_MIN 0 ///< Min of Initial Preset Socket 0 Link 1 Pstate1
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L1_P1_MAX 0xF ///< Max of Initial Preset Socket 0 Link 1 Pstate1

///Initial Preset Socket 0 Link 1 Pstate2
///Enable/Disable Initial Preset Socket 0 Link 1 Pstate2
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L1_P2_MIN 0 ///< Min of Initial Preset Socket 0 Link 1 Pstate2
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L1_P2_MAX 0xF ///< Max of Initial Preset Socket 0 Link 1 Pstate2

///Initial Preset Socket 0 Link 1 Pstate3
///Enable/Disable Initial Preset Socket 0 Link 1 Pstate3
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L1_P3_MIN 0 ///< Min of Initial Preset Socket 0 Link 1 Pstate3
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L1_P3_MAX 0xF ///< Max of Initial Preset Socket 0 Link 1 Pstate3

///Initial Preset Socket 0 Link 2 Pstate0/1/2/3 (APCB)
///An APCB token that combined "Initial Preset Socket 0 Link 2 Pstate0", "Initial Preset Socket 0 Link 2 Pstate1", "Initial Preset Socket 0 Link 2 Pstate2" and "Initial Preset Socket 0 Link 2 Pstate3". The APCB token was set in BIOS and used in ABL. Control xGMI initial preset.
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L2_MIN 0 ///< Min of Initial Preset Socket 0 Link 2 Pstate0/1/2/3 (APCB)
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L2_MAX 0xFFFF ///< Max of Initial Preset Socket 0 Link 2 Pstate0/1/2/3 (APCB)

///Initial Preset Socket 0 Link 2 Pstate0
///Enable/Disable Initial Preset Socket 0 Link 2 Pstate0
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L2_P0_MIN 0 ///< Min of Initial Preset Socket 0 Link 2 Pstate0
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L2_P0_MAX 0xF ///< Max of Initial Preset Socket 0 Link 2 Pstate0

///Initial Preset Socket 0 Link 2 Pstate1
///Enable/Disable Initial Preset Socket 0 Link 2 Pstate1
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L2_P1_MIN 0 ///< Min of Initial Preset Socket 0 Link 2 Pstate1
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L2_P1_MAX 0xF ///< Max of Initial Preset Socket 0 Link 2 Pstate1

///Initial Preset Socket 0 Link 2 Pstate2
///Enable/Disable Initial Preset Socket 0 Link 2 Pstate2
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L2_P2_MIN 0 ///< Min of Initial Preset Socket 0 Link 2 Pstate2
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L2_P2_MAX 0xF ///< Max of Initial Preset Socket 0 Link 2 Pstate2

///Initial Preset Socket 0 Link 2 Pstate3
///Enable/Disable Initial Preset Socket 0 Link 2 Pstate3
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L2_P3_MIN 0 ///< Min of Initial Preset Socket 0 Link 2 Pstate3
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L2_P3_MAX 0xF ///< Max of Initial Preset Socket 0 Link 2 Pstate3

///Initial Preset Socket 0 Link 3 Pstate0/1/2/3 (APCB)
///An APCB token that combined "Initial Preset Socket 0 Link 3 Pstate0", "Initial Preset Socket 0 Link 3 Pstate1", "Initial Preset Socket 0 Link 3 Pstate2" and "Initial Preset Socket 0 Link 3 Pstate3". The APCB token was set in BIOS and used in ABL. Control xGMI initial preset.
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L3_MIN 0 ///< Min of Initial Preset Socket 0 Link 3 Pstate0/1/2/3 (APCB)
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L3_MAX 0xFFFF ///< Max of Initial Preset Socket 0 Link 3 Pstate0/1/2/3 (APCB)

///Initial Preset Socket 0 Link 3 Pstate0
///Enable/Disable Initial Preset Socket 0 Link 3 Pstate0
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L3_P0_MIN 0 ///< Min of Initial Preset Socket 0 Link 3 Pstate0
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L3_P0_MAX 0xF ///< Max of Initial Preset Socket 0 Link 3 Pstate0

///Initial Preset Socket 0 Link 3 Pstate1
///Enable/Disable Initial Preset Socket 0 Link 3 Pstate1
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L3_P1_MIN 0 ///< Min of Initial Preset Socket 0 Link 3 Pstate1
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L3_P1_MAX 0xF ///< Max of Initial Preset Socket 0 Link 3 Pstate1

///Initial Preset Socket 0 Link 3 Pstate2
///Enable/Disable Initial Preset Socket 0 Link 3 Pstate2
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L3_P2_MIN 0 ///< Min of Initial Preset Socket 0 Link 3 Pstate2
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L3_P2_MAX 0xF ///< Max of Initial Preset Socket 0 Link 3 Pstate2

///Initial Preset Socket 0 Link 3 Pstate3
///Enable/Disable Initial Preset Socket 0 Link 3 Pstate3
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L3_P3_MIN 0 ///< Min of Initial Preset Socket 0 Link 3 Pstate3
#define IDSOPT_DF_XGMI_INIT_PRESET_S0_L3_P3_MAX 0xF ///< Max of Initial Preset Socket 0 Link 3 Pstate3

///Initial Preset Socket 1 Link 0 Pstate0/1/2/3 (APCB)
///An APCB token that combined "Initial Preset Socket 1 Link 0 Pstate0", "Initial Preset Socket 1 Link 0 Pstate1", "Initial Preset Socket 1 Link 0 Pstate2" and "Initial Preset Socket 1 Link 0 Pstate3". The APCB token was set in BIOS and used in ABL. Control xGMI initial preset.
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L0_MIN 0 ///< Min of Initial Preset Socket 1 Link 0 Pstate0/1/2/3 (APCB)
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L0_MAX 0xFFFF ///< Max of Initial Preset Socket 1 Link 0 Pstate0/1/2/3 (APCB)

///Initial Preset Socket 1 Link 0 Pstate0
///Enable/Disable Initial Preset Socket 1 Link 0 Pstate0
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L0_P0_MIN 0 ///< Min of Initial Preset Socket 1 Link 0 Pstate0
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L0_P0_MAX 0xF ///< Max of Initial Preset Socket 1 Link 0 Pstate0

///Initial Preset Socket 1 Link 0 Pstate1
///Enable/Disable Initial Preset Socket 1 Link 0 Pstate1
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L0_P1_MIN 0 ///< Min of Initial Preset Socket 1 Link 0 Pstate1
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L0_P1_MAX 0xF ///< Max of Initial Preset Socket 1 Link 0 Pstate1

///Initial Preset Socket 1 Link 0 Pstate2
///Enable/Disable Initial Preset Socket 1 Link 0 Pstate2
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L0_P2_MIN 0 ///< Min of Initial Preset Socket 1 Link 0 Pstate2
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L0_P2_MAX 0xF ///< Max of Initial Preset Socket 1 Link 0 Pstate2

///Initial Preset Socket 1 Link 0 Pstate3
///Enable/Disable Initial Preset Socket 1 Link 0 Pstate3
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L0_P3_MIN 0 ///< Min of Initial Preset Socket 1 Link 0 Pstate3
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L0_P3_MAX 0xF ///< Max of Initial Preset Socket 1 Link 0 Pstate3

///Initial Preset Socket 1 Link 1 Pstate0/1/2/3 (APCB)
///An APCB token that combined "Initial Preset Socket 1 Link 1 Pstate0", "Initial Preset Socket 1 Link 1 Pstate1", "Initial Preset Socket 1 Link 1 Pstate2" and "Initial Preset Socket 1 Link 1 Pstate3". The APCB token was set in BIOS and used in ABL. Control xGMI initial preset.
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L1_MIN 0 ///< Min of Initial Preset Socket 1 Link 1 Pstate0/1/2/3 (APCB)
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L1_MAX 0xFFFF ///< Max of Initial Preset Socket 1 Link 1 Pstate0/1/2/3 (APCB)

///Initial Preset Socket 1 Link 1 Pstate0
///Enable/Disable Initial Preset Socket 1 Link 1 Pstate0
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L1_P0_MIN 0 ///< Min of Initial Preset Socket 1 Link 1 Pstate0
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L1_P0_MAX 0xF ///< Max of Initial Preset Socket 1 Link 1 Pstate0

///Initial Preset Socket 1 Link 1 Pstate1
///Enable/Disable Initial Preset Socket 1 Link 1 Pstate1
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L1_P1_MIN 0 ///< Min of Initial Preset Socket 1 Link 1 Pstate1
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L1_P1_MAX 0xF ///< Max of Initial Preset Socket 1 Link 1 Pstate1

///Initial Preset Socket 1 Link 1 Pstate2
///Enable/Disable Initial Preset Socket 1 Link 1 Pstate2
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L1_P2_MIN 0 ///< Min of Initial Preset Socket 1 Link 1 Pstate2
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L1_P2_MAX 0xF ///< Max of Initial Preset Socket 1 Link 1 Pstate2

///Initial Preset Socket 1 Link 1 Pstate3
///Enable/Disable Initial Preset Socket 1 Link 1 Pstate3
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L1_P3_MIN 0 ///< Min of Initial Preset Socket 1 Link 1 Pstate3
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L1_P3_MAX 0xF ///< Max of Initial Preset Socket 1 Link 1 Pstate3

///Initial Preset Socket 1 Link 2 Pstate0/1/2/3 (APCB)
///An APCB token that combined "Initial Preset Socket 1 Link 2 Pstate0", "Initial Preset Socket 1 Link 2 Pstate1", "Initial Preset Socket 1 Link 2 Pstate2" and "Initial Preset Socket 1 Link 2 Pstate3". The APCB token was set in BIOS and used in ABL. Control xGMI initial preset.
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L2_MIN 0 ///< Min of Initial Preset Socket 1 Link 2 Pstate0/1/2/3 (APCB)
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L2_MAX 0xFFFF ///< Max of Initial Preset Socket 1 Link 2 Pstate0/1/2/3 (APCB)

///Initial Preset Socket 1 Link 2 Pstate0
///Enable/Disable Initial Preset Socket 1 Link 2 Pstate0
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L2_P0_MIN 0 ///< Min of Initial Preset Socket 1 Link 2 Pstate0
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L2_P0_MAX 0xF ///< Max of Initial Preset Socket 1 Link 2 Pstate0

///Initial Preset Socket 1 Link 2 Pstate1
///Enable/Disable Initial Preset Socket 1 Link 2 Pstate1
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L2_P1_MIN 0 ///< Min of Initial Preset Socket 1 Link 2 Pstate1
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L2_P1_MAX 0xF ///< Max of Initial Preset Socket 1 Link 2 Pstate1

///Initial Preset Socket 1 Link 2 Pstate2
///Enable/Disable Initial Preset Socket 1 Link 2 Pstate2
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L2_P2_MIN 0 ///< Min of Initial Preset Socket 1 Link 2 Pstate2
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L2_P2_MAX 0xF ///< Max of Initial Preset Socket 1 Link 2 Pstate2

///Initial Preset Socket 1 Link 2 Pstate3
///Enable/Disable Initial Preset Socket 1 Link 2 Pstate3
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L2_P3_MIN 0 ///< Min of Initial Preset Socket 1 Link 2 Pstate3
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L2_P3_MAX 0xF ///< Max of Initial Preset Socket 1 Link 2 Pstate3

///Initial Preset Socket 1 Link 3 Pstate0/1/2/3 (APCB)
///An APCB token that combined "Initial Preset Socket 1 Link 3 Pstate0", "Initial Preset Socket 1 Link 3 Pstate1", "Initial Preset Socket 1 Link 3 Pstate2" and "Initial Preset Socket 1 Link 3 Pstate3". The APCB token was set in BIOS and used in ABL. Control xGMI initial preset.
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L3_MIN 0 ///< Min of Initial Preset Socket 1 Link 3 Pstate0/1/2/3 (APCB)
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L3_MAX 0xFFFF ///< Max of Initial Preset Socket 1 Link 3 Pstate0/1/2/3 (APCB)

///Initial Preset Socket 1 Link 3 Pstate0
///Enable/Disable Initial Preset Socket 1 Link 3 Pstate0
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L3_P0_MIN 0 ///< Min of Initial Preset Socket 1 Link 3 Pstate0
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L3_P0_MAX 0xF ///< Max of Initial Preset Socket 1 Link 3 Pstate0

///Initial Preset Socket 1 Link 3 Pstate1
///Enable/Disable Initial Preset Socket 1 Link 3 Pstate1
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L3_P1_MIN 0 ///< Min of Initial Preset Socket 1 Link 3 Pstate1
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L3_P1_MAX 0xF ///< Max of Initial Preset Socket 1 Link 3 Pstate1

///Initial Preset Socket 1 Link 3 Pstate2
///Enable/Disable Initial Preset Socket 1 Link 3 Pstate2
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L3_P2_MIN 0 ///< Min of Initial Preset Socket 1 Link 3 Pstate2
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L3_P2_MAX 0xF ///< Max of Initial Preset Socket 1 Link 3 Pstate2

///Initial Preset Socket 1 Link 3 Pstate3
///Enable/Disable Initial Preset Socket 1 Link 3 Pstate3
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L3_P3_MIN 0 ///< Min of Initial Preset Socket 1 Link 3 Pstate3
#define IDSOPT_DF_XGMI_INIT_PRESET_S1_L3_P3_MAX 0xF ///< Max of Initial Preset Socket 1 Link 3 Pstate3

///TXEQ Search Mask Socket 0 Link 0 Pstate0/1 (APCB)
///An APCB token that combined "TXEQ Search Mask Socket 0 Link 0 Pstate0" and "TXEQ Search Mask Socket 0 Link 0 Pstate1". The APCB token was set in BIOS and used in ABL. Control xGMI Pstate search mask.
#define IDSOPT_DF_XGMI_TXEQ_S0_L0_P01_MIN 0 ///< Min of TXEQ Search Mask Socket 0 Link 0 Pstate0/1 (APCB)
#define IDSOPT_DF_XGMI_TXEQ_S0_L0_P01_MAX 0xFFFFFFFF ///< Max of TXEQ Search Mask Socket 0 Link 0 Pstate0/1 (APCB)

///TXEQ Search Mask Socket 0 Link 0 Pstate2/3 (APCB)
///An APCB token that combined "TXEQ Search Mask Socket 0 Link 0 Pstate2" and "TXEQ Search Mask Socket 0 Link 0 Pstate3". The APCB token was set in BIOS and used in ABL. Control xGMI Pstate search mask.
#define IDSOPT_DF_XGMI_TXEQ_S0_L0_P23_MIN 0 ///< Min of TXEQ Search Mask Socket 0 Link 0 Pstate2/3 (APCB)
#define IDSOPT_DF_XGMI_TXEQ_S0_L0_P23_MAX 0xFFFFFFFF ///< Max of TXEQ Search Mask Socket 0 Link 0 Pstate2/3 (APCB)

///TXEQ Search Mask Socket 0 Link 0 Pstate0
///Enable/Disable TXEQ Search Mask Socket 0 Link 0 Pstate0
#define IDSOPT_DF_XGMI_TXEQ_S0_L0_P0_MIN 0 ///< Min of TXEQ Search Mask Socket 0 Link 0 Pstate0
#define IDSOPT_DF_XGMI_TXEQ_S0_L0_P0_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 0 Link 0 Pstate0

///TXEQ Search Mask Socket 0 Link 0 Pstate1
///Enable/Disable TXEQ Search Mask Socket 0 Link 0 Pstate1
#define IDSOPT_DF_XGMI_TXEQ_S0_L0_P1_MIN 0 ///< Min of TXEQ Search Mask Socket 0 Link 0 Pstate1
#define IDSOPT_DF_XGMI_TXEQ_S0_L0_P1_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 0 Link 0 Pstate1

///TXEQ Search Mask Socket 0 Link 0 Pstate2
///Enable/Disable TXEQ Search Mask Socket 0 Link 0 Pstate2
#define IDSOPT_DF_XGMI_TXEQ_S0_L0_P2_MIN 0 ///< Min of TXEQ Search Mask Socket 0 Link 0 Pstate2
#define IDSOPT_DF_XGMI_TXEQ_S0_L0_P2_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 0 Link 0 Pstate2

///TXEQ Search Mask Socket 0 Link 0 Pstate3
///Enable/Disable TXEQ Search Mask Socket 0 Link 0 Pstate3
#define IDSOPT_DF_XGMI_TXEQ_S0_L0_P3_MIN 0 ///< Min of TXEQ Search Mask Socket 0 Link 0 Pstate3
#define IDSOPT_DF_XGMI_TXEQ_S0_L0_P3_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 0 Link 0 Pstate3

///TXEQ Search Mask Socket 0 Link 1 Pstate0/1 (APCB)
///An APCB token that combined "TXEQ Search Mask Socket 0 Link 1 Pstate0" and "TXEQ Search Mask Socket 0 Link 1 Pstate1". The APCB token was set in BIOS and used in ABL. Control xGMI Pstate search mask.
#define IDSOPT_DF_XGMI_TXEQ_S0_L1_P01_MIN 0 ///< Min of TXEQ Search Mask Socket 0 Link 1 Pstate0/1 (APCB)
#define IDSOPT_DF_XGMI_TXEQ_S0_L1_P01_MAX 0xFFFFFFFF ///< Max of TXEQ Search Mask Socket 0 Link 1 Pstate0/1 (APCB)

///TXEQ Search Mask Socket 0 Link 1 Pstate2/3 (APCB)
///An APCB token that combined "TXEQ Search Mask Socket 0 Link 1 Pstate2" and "TXEQ Search Mask Socket 0 Link 1 Pstate3". The APCB token was set in BIOS and used in ABL. Control xGMI Pstate search mask.
#define IDSOPT_DF_XGMI_TXEQ_S0_L1_P23_MIN 0 ///< Min of TXEQ Search Mask Socket 0 Link 1 Pstate2/3 (APCB)
#define IDSOPT_DF_XGMI_TXEQ_S0_L1_P23_MAX 0xFFFFFFFF ///< Max of TXEQ Search Mask Socket 0 Link 1 Pstate2/3 (APCB)

///TXEQ Search Mask Socket 0 Link 1 Pstate0
///Enable/Disable TXEQ Search Mask Socket 0 Link 1 Pstate0
#define IDSOPT_DF_XGMI_TXEQ_S0_L1_P0_MIN 0 ///< Min of TXEQ Search Mask Socket 0 Link 1 Pstate0
#define IDSOPT_DF_XGMI_TXEQ_S0_L1_P0_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 0 Link 1 Pstate0

///TXEQ Search Mask Socket 0 Link 1 Pstate1
///Enable/Disable TXEQ Search Mask Socket 0 Link 1 Pstate1
#define IDSOPT_DF_XGMI_TXEQ_S0_L1_P1_MIN 0 ///< Min of TXEQ Search Mask Socket 0 Link 1 Pstate1
#define IDSOPT_DF_XGMI_TXEQ_S0_L1_P1_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 0 Link 1 Pstate1

///TXEQ Search Mask Socket 0 Link 1 Pstate2
///Enable/Disable TXEQ Search Mask Socket 0 Link 1 Pstate2
#define IDSOPT_DF_XGMI_TXEQ_S0_L1_P2_MIN 0 ///< Min of TXEQ Search Mask Socket 0 Link 1 Pstate2
#define IDSOPT_DF_XGMI_TXEQ_S0_L1_P2_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 0 Link 1 Pstate2

///TXEQ Search Mask Socket 0 Link 1 Pstate3
///Enable/Disable TXEQ Search Mask Socket 0 Link 1 Pstate3
#define IDSOPT_DF_XGMI_TXEQ_S0_L1_P3_MIN 0 ///< Min of TXEQ Search Mask Socket 0 Link 1 Pstate3
#define IDSOPT_DF_XGMI_TXEQ_S0_L1_P3_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 0 Link 1 Pstate3

///TXEQ Search Mask Socket 0 Link 2 Pstate0/1 (APCB)
///An APCB token that combined "TXEQ Search Mask Socket 0 Link 2 Pstate0" and "TXEQ Search Mask Socket 0 Link 2 Pstate1". The APCB token was set in BIOS and used in ABL. Control xGMI Pstate search mask.
#define IDSOPT_DF_XGMI_TXEQ_S0_L2_P01_MIN 0 ///< Min of TXEQ Search Mask Socket 0 Link 2 Pstate0/1 (APCB)
#define IDSOPT_DF_XGMI_TXEQ_S0_L2_P01_MAX 0xFFFFFFFF ///< Max of TXEQ Search Mask Socket 0 Link 2 Pstate0/1 (APCB)

///TXEQ Search Mask Socket 0 Link 2 Pstate2/3 (APCB)
///An APCB token that combined "TXEQ Search Mask Socket 0 Link 2 Pstate2" and "TXEQ Search Mask Socket 0 Link 2 Pstate3". The APCB token was set in BIOS and used in ABL. Control xGMI Pstate search mask.
#define IDSOPT_DF_XGMI_TXEQ_S0_L2_P23_MIN 0 ///< Min of TXEQ Search Mask Socket 0 Link 2 Pstate2/3 (APCB)
#define IDSOPT_DF_XGMI_TXEQ_S0_L2_P23_MAX 0xFFFFFFFF ///< Max of TXEQ Search Mask Socket 0 Link 2 Pstate2/3 (APCB)

///TXEQ Search Mask Socket 0 Link 2 Pstate0
///Enable/Disable TXEQ Search Mask Socket 0 Link 2 Pstate0
#define IDSOPT_DF_XGMI_TXEQ_S0_L2_P0_MIN 0 ///< Min of TXEQ Search Mask Socket 0 Link 2 Pstate0
#define IDSOPT_DF_XGMI_TXEQ_S0_L2_P0_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 0 Link 2 Pstate0

///TXEQ Search Mask Socket 0 Link 2 Pstate1
///Enable/Disable TXEQ Search Mask Socket 0 Link 2 Pstate1
#define IDSOPT_DF_XGMI_TXEQ_S0_L2_P1_MIN 0 ///< Min of TXEQ Search Mask Socket 0 Link 2 Pstate1
#define IDSOPT_DF_XGMI_TXEQ_S0_L2_P1_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 0 Link 2 Pstate1

///TXEQ Search Mask Socket 0 Link 2 Pstate2
///Enable/Disable TXEQ Search Mask Socket 0 Link 2 Pstate2
#define IDSOPT_DF_XGMI_TXEQ_S0_L2_P2_MIN 0 ///< Min of TXEQ Search Mask Socket 0 Link 2 Pstate2
#define IDSOPT_DF_XGMI_TXEQ_S0_L2_P2_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 0 Link 2 Pstate2

///TXEQ Search Mask Socket 0 Link 2 Pstate3
///Enable/Disable TXEQ Search Mask Socket 0 Link 2 Pstate3
#define IDSOPT_DF_XGMI_TXEQ_S0_L2_P3_MIN 0 ///< Min of TXEQ Search Mask Socket 0 Link 2 Pstate3
#define IDSOPT_DF_XGMI_TXEQ_S0_L2_P3_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 0 Link 2 Pstate3

///TXEQ Search Mask Socket 0 Link 3 Pstate0/1 (APCB)
///An APCB token that combined "TXEQ Search Mask Socket 0 Link 3 Pstate0" and "TXEQ Search Mask Socket 0 Link 3 Pstate1". The APCB token was set in BIOS and used in ABL. Control xGMI Pstate search mask.
#define IDSOPT_DF_XGMI_TXEQ_S0_L3_P01_MIN 0 ///< Min of TXEQ Search Mask Socket 0 Link 3 Pstate0/1 (APCB)
#define IDSOPT_DF_XGMI_TXEQ_S0_L3_P01_MAX 0xFFFFFFFF ///< Max of TXEQ Search Mask Socket 0 Link 3 Pstate0/1 (APCB)

///TXEQ Search Mask Socket 0 Link 3 Pstate2/3 (APCB)
///An APCB token that combined "TXEQ Search Mask Socket 0 Link 3 Pstate2" and "TXEQ Search Mask Socket 0 Link 3 Pstate3". The APCB token was set in BIOS and used in ABL. Control xGMI Pstate search mask.
#define IDSOPT_DF_XGMI_TXEQ_S0_L3_P23_MIN 0 ///< Min of TXEQ Search Mask Socket 0 Link 3 Pstate2/3 (APCB)
#define IDSOPT_DF_XGMI_TXEQ_S0_L3_P23_MAX 0xFFFFFFFF ///< Max of TXEQ Search Mask Socket 0 Link 3 Pstate2/3 (APCB)

///TXEQ Search Mask Socket 0 Link 3 Pstate0
///Enable/Disable TXEQ Search Mask Socket 0 Link 3 Pstate0
#define IDSOPT_DF_XGMI_TXEQ_S0_L3_P0_MIN 0 ///< Min of TXEQ Search Mask Socket 0 Link 3 Pstate0
#define IDSOPT_DF_XGMI_TXEQ_S0_L3_P0_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 0 Link 3 Pstate0

///TXEQ Search Mask Socket 0 Link 3 Pstate1
///Enable/Disable TXEQ Search Mask Socket 0 Link 3 Pstate1
#define IDSOPT_DF_XGMI_TXEQ_S0_L3_P1_MIN 0 ///< Min of TXEQ Search Mask Socket 0 Link 3 Pstate1
#define IDSOPT_DF_XGMI_TXEQ_S0_L3_P1_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 0 Link 3 Pstate1

///TXEQ Search Mask Socket 0 Link 3 Pstate2
///Enable/Disable TXEQ Search Mask Socket 0 Link 3 Pstate2
#define IDSOPT_DF_XGMI_TXEQ_S0_L3_P2_MIN 0 ///< Min of TXEQ Search Mask Socket 0 Link 3 Pstate2
#define IDSOPT_DF_XGMI_TXEQ_S0_L3_P2_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 0 Link 3 Pstate2

///TXEQ Search Mask Socket 0 Link 3 Pstate3
///Enable/Disable TXEQ Search Mask Socket 0 Link 3 Pstate3
#define IDSOPT_DF_XGMI_TXEQ_S0_L3_P3_MIN 0 ///< Min of TXEQ Search Mask Socket 0 Link 3 Pstate3
#define IDSOPT_DF_XGMI_TXEQ_S0_L3_P3_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 0 Link 3 Pstate3

///TXEQ Search Mask Socket 1 Link 0 Pstate0/1 (APCB)
///An APCB token that combined "TXEQ Search Mask Socket 1 Link 0 Pstate0" and "TXEQ Search Mask Socket 1 Link 0 Pstate1". The APCB token was set in BIOS and used in ABL. Control xGMI Pstate search mask.
#define IDSOPT_DF_XGMI_TXEQ_S1_L0_P01_MIN 0 ///< Min of TXEQ Search Mask Socket 1 Link 0 Pstate0/1 (APCB)
#define IDSOPT_DF_XGMI_TXEQ_S1_L0_P01_MAX 0xFFFFFFFF ///< Max of TXEQ Search Mask Socket 1 Link 0 Pstate0/1 (APCB)

///TXEQ Search Mask Socket 1 Link 0 Pstate2/3 (APCB)
///An APCB token that combined "TXEQ Search Mask Socket 1 Link 0 Pstate2" and "TXEQ Search Mask Socket 1 Link 0 Pstate3". The APCB token was set in BIOS and used in ABL. Control xGMI Pstate search mask.
#define IDSOPT_DF_XGMI_TXEQ_S1_L0_P23_MIN 0 ///< Min of TXEQ Search Mask Socket 1 Link 0 Pstate2/3 (APCB)
#define IDSOPT_DF_XGMI_TXEQ_S1_L0_P23_MAX 0xFFFFFFFF ///< Max of TXEQ Search Mask Socket 1 Link 0 Pstate2/3 (APCB)

///TXEQ Search Mask Socket 1 Link 0 Pstate0
///Enable/Disable TXEQ Search Mask Socket 1 Link 0 Pstate0
#define IDSOPT_DF_XGMI_TXEQ_S1_L0_P0_MIN 0 ///< Min of TXEQ Search Mask Socket 1 Link 0 Pstate0
#define IDSOPT_DF_XGMI_TXEQ_S1_L0_P0_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 1 Link 0 Pstate0

///TXEQ Search Mask Socket 1 Link 0 Pstate1
///Enable/Disable TXEQ Search Mask Socket 1 Link 0 Pstate1
#define IDSOPT_DF_XGMI_TXEQ_S1_L0_P1_MIN 0 ///< Min of TXEQ Search Mask Socket 1 Link 0 Pstate1
#define IDSOPT_DF_XGMI_TXEQ_S1_L0_P1_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 1 Link 0 Pstate1

///TXEQ Search Mask Socket 1 Link 0 Pstate2
///Enable/Disable TXEQ Search Mask Socket 1 Link 0 Pstate2
#define IDSOPT_DF_XGMI_TXEQ_S1_L0_P2_MIN 0 ///< Min of TXEQ Search Mask Socket 1 Link 0 Pstate2
#define IDSOPT_DF_XGMI_TXEQ_S1_L0_P2_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 1 Link 0 Pstate2

///TXEQ Search Mask Socket 1 Link 0 Pstate3
///Enable/Disable TXEQ Search Mask Socket 1 Link 0 Pstate3
#define IDSOPT_DF_XGMI_TXEQ_S1_L0_P3_MIN 0 ///< Min of TXEQ Search Mask Socket 1 Link 0 Pstate3
#define IDSOPT_DF_XGMI_TXEQ_S1_L0_P3_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 1 Link 0 Pstate3

///TXEQ Search Mask Socket 1 Link 1 Pstate0/1 (APCB)
///An APCB token that combined "TXEQ Search Mask Socket 1 Link 1 Pstate0" and "TXEQ Search Mask Socket 1 Link 1 Pstate1". The APCB token was set in BIOS and used in ABL. Control xGMI Pstate search mask.
#define IDSOPT_DF_XGMI_TXEQ_S1_L1_P01_MIN 0 ///< Min of TXEQ Search Mask Socket 1 Link 1 Pstate0/1 (APCB)
#define IDSOPT_DF_XGMI_TXEQ_S1_L1_P01_MAX 0xFFFFFFFF ///< Max of TXEQ Search Mask Socket 1 Link 1 Pstate0/1 (APCB)

///TXEQ Search Mask Socket 1 Link 1 Pstate2/3 (APCB)
///An APCB token that combined "TXEQ Search Mask Socket 1 Link 1 Pstate2" and "TXEQ Search Mask Socket 1 Link 1 Pstate3". The APCB token was set in BIOS and used in ABL. Control xGMI Pstate search mask.
#define IDSOPT_DF_XGMI_TXEQ_S1_L1_P23_MIN 0 ///< Min of TXEQ Search Mask Socket 1 Link 1 Pstate2/3 (APCB)
#define IDSOPT_DF_XGMI_TXEQ_S1_L1_P23_MAX 0xFFFFFFFF ///< Max of TXEQ Search Mask Socket 1 Link 1 Pstate2/3 (APCB)

///TXEQ Search Mask Socket 1 Link 1 Pstate0
///Enable/Disable TXEQ Search Mask Socket 1 Link 1 Pstate0
#define IDSOPT_DF_XGMI_TXEQ_S1_L1_P0_MIN 0 ///< Min of TXEQ Search Mask Socket 1 Link 1 Pstate0
#define IDSOPT_DF_XGMI_TXEQ_S1_L1_P0_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 1 Link 1 Pstate0

///TXEQ Search Mask Socket 1 Link 1 Pstate1
///Enable/Disable TXEQ Search Mask Socket 1 Link 1 Pstate1
#define IDSOPT_DF_XGMI_TXEQ_S1_L1_P1_MIN 0 ///< Min of TXEQ Search Mask Socket 1 Link 1 Pstate1
#define IDSOPT_DF_XGMI_TXEQ_S1_L1_P1_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 1 Link 1 Pstate1

///TXEQ Search Mask Socket 1 Link 1 Pstate2
///Enable/Disable TXEQ Search Mask Socket 1 Link 1 Pstate2
#define IDSOPT_DF_XGMI_TXEQ_S1_L1_P2_MIN 0 ///< Min of TXEQ Search Mask Socket 1 Link 1 Pstate2
#define IDSOPT_DF_XGMI_TXEQ_S1_L1_P2_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 1 Link 1 Pstate2

///TXEQ Search Mask Socket 1 Link 1 Pstate3
///Enable/Disable TXEQ Search Mask Socket 1 Link 1 Pstate3
#define IDSOPT_DF_XGMI_TXEQ_S1_L1_P3_MIN 0 ///< Min of TXEQ Search Mask Socket 1 Link 1 Pstate3
#define IDSOPT_DF_XGMI_TXEQ_S1_L1_P3_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 1 Link 1 Pstate3

///TXEQ Search Mask Socket 1 Link 2 Pstate0/1 (APCB)
///An APCB token that combined "TXEQ Search Mask Socket 1 Link 2 Pstate0" and "TXEQ Search Mask Socket 1 Link 2 Pstate1". The APCB token was set in BIOS and used in ABL. Control xGMI Pstate search mask.
#define IDSOPT_DF_XGMI_TXEQ_S1_L2_P01_MIN 0 ///< Min of TXEQ Search Mask Socket 1 Link 2 Pstate0/1 (APCB)
#define IDSOPT_DF_XGMI_TXEQ_S1_L2_P01_MAX 0xFFFFFFFF ///< Max of TXEQ Search Mask Socket 1 Link 2 Pstate0/1 (APCB)

///TXEQ Search Mask Socket 1 Link 2 Pstate2/3 (APCB)
///An APCB token that combined "TXEQ Search Mask Socket 1 Link 2 Pstate2" and "TXEQ Search Mask Socket 1 Link 2 Pstate3". The APCB token was set in BIOS and used in ABL. Control xGMI Pstate search mask.
#define IDSOPT_DF_XGMI_TXEQ_S1_L2_P23_MIN 0 ///< Min of TXEQ Search Mask Socket 1 Link 2 Pstate2/3 (APCB)
#define IDSOPT_DF_XGMI_TXEQ_S1_L2_P23_MAX 0xFFFFFFFF ///< Max of TXEQ Search Mask Socket 1 Link 2 Pstate2/3 (APCB)

///TXEQ Search Mask Socket 1 Link 2 Pstate0
///Enable/Disable TXEQ Search Mask Socket 1 Link 2 Pstate0
#define IDSOPT_DF_XGMI_TXEQ_S1_L2_P0_MIN 0 ///< Min of TXEQ Search Mask Socket 1 Link 2 Pstate0
#define IDSOPT_DF_XGMI_TXEQ_S1_L2_P0_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 1 Link 2 Pstate0

///TXEQ Search Mask Socket 1 Link 2 Pstate1
///Enable/Disable TXEQ Search Mask Socket 1 Link 2 Pstate1
#define IDSOPT_DF_XGMI_TXEQ_S1_L2_P1_MIN 0 ///< Min of TXEQ Search Mask Socket 1 Link 2 Pstate1
#define IDSOPT_DF_XGMI_TXEQ_S1_L2_P1_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 1 Link 2 Pstate1

///TXEQ Search Mask Socket 1 Link 2 Pstate2
///Enable/Disable TXEQ Search Mask Socket 1 Link 2 Pstate2
#define IDSOPT_DF_XGMI_TXEQ_S1_L2_P2_MIN 0 ///< Min of TXEQ Search Mask Socket 1 Link 2 Pstate2
#define IDSOPT_DF_XGMI_TXEQ_S1_L2_P2_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 1 Link 2 Pstate2

///TXEQ Search Mask Socket 1 Link 2 Pstate3
///Enable/Disable TXEQ Search Mask Socket 1 Link 2 Pstate3
#define IDSOPT_DF_XGMI_TXEQ_S1_L2_P3_MIN 0 ///< Min of TXEQ Search Mask Socket 1 Link 2 Pstate3
#define IDSOPT_DF_XGMI_TXEQ_S1_L2_P3_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 1 Link 2 Pstate3

///TXEQ Search Mask Socket 1 Link 3 Pstate0/1 (APCB)
///An APCB token that combined "TXEQ Search Mask Socket 1 Link 3 Pstate0" and "TXEQ Search Mask Socket 1 Link 3 Pstate1". The APCB token was set in BIOS and used in ABL. Control xGMI Pstate search mask.
#define IDSOPT_DF_XGMI_TXEQ_S1_L3_P01_MIN 0 ///< Min of TXEQ Search Mask Socket 1 Link 3 Pstate0/1 (APCB)
#define IDSOPT_DF_XGMI_TXEQ_S1_L3_P01_MAX 0xFFFFFFFF ///< Max of TXEQ Search Mask Socket 1 Link 3 Pstate0/1 (APCB)

///TXEQ Search Mask Socket 1 Link 3 Pstate2/3 (APCB)
///An APCB token that combined "TXEQ Search Mask Socket 1 Link 3 Pstate2" and "TXEQ Search Mask Socket 1 Link 3 Pstate3". The APCB token was set in BIOS and used in ABL. Control xGMI Pstate search mask.
#define IDSOPT_DF_XGMI_TXEQ_S1_L3_P23_MIN 0 ///< Min of TXEQ Search Mask Socket 1 Link 3 Pstate2/3 (APCB)
#define IDSOPT_DF_XGMI_TXEQ_S1_L3_P23_MAX 0xFFFFFFFF ///< Max of TXEQ Search Mask Socket 1 Link 3 Pstate2/3 (APCB)

///TXEQ Search Mask Socket 1 Link 3 Pstate0
///Enable/Disable TXEQ Search Mask Socket 1 Link 3 Pstate0
#define IDSOPT_DF_XGMI_TXEQ_S1_L3_P0_MIN 0 ///< Min of TXEQ Search Mask Socket 1 Link 3 Pstate0
#define IDSOPT_DF_XGMI_TXEQ_S1_L3_P0_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 1 Link 3 Pstate0

///TXEQ Search Mask Socket 1 Link 3 Pstate1
///Enable/Disable TXEQ Search Mask Socket 1 Link 3 Pstate1
#define IDSOPT_DF_XGMI_TXEQ_S1_L3_P1_MIN 0 ///< Min of TXEQ Search Mask Socket 1 Link 3 Pstate1
#define IDSOPT_DF_XGMI_TXEQ_S1_L3_P1_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 1 Link 3 Pstate1

///TXEQ Search Mask Socket 1 Link 3 Pstate2
///Enable/Disable TXEQ Search Mask Socket 1 Link 3 Pstate2
#define IDSOPT_DF_XGMI_TXEQ_S1_L3_P2_MIN 0 ///< Min of TXEQ Search Mask Socket 1 Link 3 Pstate2
#define IDSOPT_DF_XGMI_TXEQ_S1_L3_P2_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 1 Link 3 Pstate2

///TXEQ Search Mask Socket 1 Link 3 Pstate3
///Enable/Disable TXEQ Search Mask Socket 1 Link 3 Pstate3
#define IDSOPT_DF_XGMI_TXEQ_S1_L3_P3_MIN 0 ///< Min of TXEQ Search Mask Socket 1 Link 3 Pstate3
#define IDSOPT_DF_XGMI_TXEQ_S1_L3_P3_MAX 0xFFFF ///< Max of TXEQ Search Mask Socket 1 Link 3 Pstate3

///xGMI AC/DC Coupled Link Control
///Control XGMI AC/DC Coupled Link. Valid value: 0: AC coupled 1: DC coupled
typedef enum {
  IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_CONTROL_MANUAL = 0,///<Manual
  IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_CONTROL_AUTO = 0xFF,///<Auto
} IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_CONTROL;

///xGMI AC/DC Coupled Link (APCB)
///An APCB token that combined "xGMI AC/DC Coupled Link Socket [1:0] Link [3:0] ". The APCB token was set in BIOS and used in ABL. Control xGMI AC/DC Coupled Link.
#define IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_MIN 0 ///< Min of xGMI AC/DC Coupled Link (APCB)
#define IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_MAX 0xFF ///< Max of xGMI AC/DC Coupled Link (APCB)

///xGMI AC/DC Coupled Link Socket 0 Link 0
///Control xGMI AC/DC Coupled Link Socket 0 Link 0. Valid value: 0: AC coupled 1: DC coupled
typedef enum {
  IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK0_ACCOUPLED = 0,///<AC Coupled
  IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK0_DCCOUPLED = 1,///<DC Coupled
} IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK0;

///xGMI AC/DC Coupled Link Socket 0 Link 1
///Control xGMI AC/DC Coupled Link Socket 0 Link 1. Valid value: 0: AC coupled 1: DC coupled
typedef enum {
  IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK1_ACCOUPLED = 0,///<AC Coupled
  IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK1_DCCOUPLED = 1,///<DC Coupled
} IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK1;

///xGMI AC/DC Coupled Link Socket 0 Link 2
///Control xGMI AC/DC Coupled Link Socket 0 Link 2. Valid value: 0: AC coupled 1: DC coupled
typedef enum {
  IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK2_ACCOUPLED = 0,///<AC Coupled
  IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK2_DCCOUPLED = 1,///<DC Coupled
} IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK2;

///xGMI AC/DC Coupled Link Socket 0 Link 3
///Control xGMI AC/DC Coupled Link Socket 0 Link 3. Valid value: 0: AC coupled 1: DC coupled
typedef enum {
  IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK3_ACCOUPLED = 0,///<AC Coupled
  IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK3_DCCOUPLED = 1,///<DC Coupled
} IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK3;

///xGMI AC/DC Coupled Link Socket 1 Link 0
///Control xGMI AC/DC Coupled Link Socket 1 Link 0. Valid value: 0: AC coupled 1: DC coupled
typedef enum {
  IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK0_ACCOUPLED = 0,///<AC Coupled
  IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK0_DCCOUPLED = 1,///<DC Coupled
} IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK0;

///xGMI AC/DC Coupled Link Socket 1 Link 1
///Control xGMI AC/DC Coupled Link Socket 1 Link 1. Valid value: 0: AC coupled 1: DC coupled
typedef enum {
  IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK1_ACCOUPLED = 0,///<AC Coupled
  IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK1_DCCOUPLED = 1,///<DC Coupled
} IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK1;

///xGMI AC/DC Coupled Link Socket 1 Link 2
///Control xGMI AC/DC Coupled Link Socket 1 Link 2. Valid value: 0: AC coupled 1: DC coupled
typedef enum {
  IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK2_ACCOUPLED = 0,///<AC Coupled
  IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK2_DCCOUPLED = 1,///<DC Coupled
} IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK2;

///xGMI AC/DC Coupled Link Socket 1 Link 3
///Control xGMI AC/DC Coupled Link Socket 1 Link 3. Valid value: 0: AC coupled 1: DC coupled
typedef enum {
  IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK3_ACCOUPLED = 0,///<AC Coupled
  IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK3_DCCOUPLED = 1,///<DC Coupled
} IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK3;

///xGMI Channel Type Control
///Control xGMI Channel Type. Valid channel type: 0: Disabled 1: Long Reach
typedef enum {
  IDSOPT_DF_XGMI_CHANNEL_TYPE_CONTROL_MANUAL = 0,///<Manual
  IDSOPT_DF_XGMI_CHANNEL_TYPE_CONTROL_AUTO = 0xFF,///<Auto
} IDSOPT_DF_XGMI_CHANNEL_TYPE_CONTROL;

///xGMI Channel Type (APCB)
///An APCB token that combined "xGMI Channel Type Socket [1:0] Link [3:0]". The APCB token was set in BIOS and used in ABL. Control xGMI Channel Type.
#define IDSOPT_DF_XGMI_CHANNEL_TYPE_MIN 0 ///< Min of xGMI Channel Type (APCB)
#define IDSOPT_DF_XGMI_CHANNEL_TYPE_MAX 0xFFFFFFFF ///< Max of xGMI Channel Type (APCB)

///xGMI Channel Type Socket 0 Link 0
///Control xGMI Channel Type Socket 0 Link 0. Valid channel type: 0: Disabled 1: Long Reach
typedef enum {
  IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK0_DISABLED = 0,///<Disabled
  IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK0_LONGREACH = 1,///<Long Reach
} IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK0;

///xGMI Channel Type Socket 0 Link 1
///Control xGMI Channel Type Socket 0 Link 1. Valid channel type: 0: Disabled 1: Long Reach
typedef enum {
  IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK1_DISABLED = 0,///<Disabled
  IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK1_LONGREACH = 1,///<Long Reach
} IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK1;

///xGMI Channel Type Socket 0 Link 2
///Control xGMI Channel Type Socket 0 Link 2. Valid channel type: 0: Disabled 1: Long Reach
typedef enum {
  IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK2_DISABLED = 0,///<Disabled
  IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK2_LONGREACH = 1,///<Long Reach
} IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK2;

///xGMI Channel Type Socket 0 Link 3
///Control xGMI Channel Type Socket 0 Link 3. Valid channel type: 0: Disabled 1: Long Reach
typedef enum {
  IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK3_DISABLED = 0,///<Disabled
  IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK3_LONGREACH = 1,///<Long Reach
} IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK3;

///xGMI Channel Type Socket 1 Link 0
///Control xGMI Channel Type Socket 1 Link 0. Valid channel type: 0: Disabled 1: Long Reach
typedef enum {
  IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK0_DISABLED = 0,///<Disabled
  IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK0_LONGREACH = 1,///<Long Reach
} IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK0;

///xGMI Channel Type Socket 1 Link 1
///Control xGMI Channel Type Socket 1 Link 1. Valid channel type: 0: Disabled 1: Long Reach
typedef enum {
  IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK1_DISABLED = 0,///<Disabled
  IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK1_LONGREACH = 1,///<Long Reach
} IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK1;

///xGMI Channel Type Socket 1 Link 2
///Control xGMI Channel Type Socket 1 Link 2. Valid channel type: 0: Disabled 1: Long Reach
typedef enum {
  IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK2_DISABLED = 0,///<Disabled
  IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK2_LONGREACH = 1,///<Long Reach
} IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK2;

///xGMI Channel Type Socket 1 Link 3
///Control xGMI Channel Type Socket 1 Link 3. Valid channel type: 0: Disabled 1: Long Reach
typedef enum {
  IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK3_DISABLED = 0,///<Disabled
  IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK3_LONGREACH = 1,///<Long Reach
} IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK3;

///SDCI
///Enable or Disable Smart Data Cache Injection feature
typedef enum {
  IDSOPT_DF_CDMA_DISABLED = 0,///<Disabled
  IDSOPT_DF_CDMA_ENABLED = 1,///<Enabled
  IDSOPT_DF_CDMA_AUTO = 0xFF,///<Auto
} IDSOPT_DF_CDMA;

///DisRmtSteer
///When set, this bit disables the sending of steering probes to remote socket
typedef enum {
  IDSOPT_DF_DBG_DIS_RMT_STEER_DISABLED = 0,///<Disabled
  IDSOPT_DF_DBG_DIS_RMT_STEER_ENABLED = 1,///<Enabled
  IDSOPT_DF_DBG_DIS_RMT_STEER_AUTO = 0xFF,///<Auto
} IDSOPT_DF_DBG_DIS_RMT_STEER;

///Organization
///Specifies whether multiple memory/CXL channels will share probe filter storage. For memory sizes of 16TB or larger, this feature is ignored as it is auto-selected to \'shared\'\n
typedef enum {
  IDSOPT_DF_CMN_PF_ORGANIZATION_AUTO = 0xFF,///<Auto
  IDSOPT_DF_CMN_PF_ORGANIZATION_DEDICATED = 0,///<Dedicated
  IDSOPT_DF_CMN_PF_ORGANIZATION_SHARED = 2,///<Shared
} IDSOPT_DF_CMN_PF_ORGANIZATION;

///Periodic Directory Rinse (PDR) Tuning
///Controls PDR settings that may impact performance by workload and/or processor.\nPeriodic (RefClock Based Floss Only): Rate based Directory Rinse.\nBlended (Cache Load Based Floss with Background RefClock Based Floss): Demand based Directory Rinse.
typedef enum {
  IDSOPT_CMN_DF_PDR_TUNING_PERIODIC = 4,///<Periodic
  IDSOPT_CMN_DF_PDR_TUNING_BLENDED = 5,///<Blended
  IDSOPT_CMN_DF_PDR_TUNING_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_DF_PDR_TUNING;

///Tracking Granularity
///Finer-Grain tracking enables higher memory efficiency and is expected to be the preferred performance option. Coarser-grain tracking may improve memory performance for a narrow set of workloads.
typedef enum {
  IDSOPT_DF_CMN_MEM_INTLV_PAGE_SIZE_FINERGRAIN = 0,///<Finer-Grain
  IDSOPT_DF_CMN_MEM_INTLV_PAGE_SIZE_COARSERGRAIN = 1,///<Coarser-Grain
  IDSOPT_DF_CMN_MEM_INTLV_PAGE_SIZE_AUTO = 0xFF,///<Auto
} IDSOPT_DF_CMN_MEM_INTLV_PAGE_SIZE;

///PDR Mode
///Selective: [Recommended setting] Rinsing applied only to the minimum set of memory addresses.\nAll: Rinsing applied to all memory addresses.
typedef enum {
  IDSOPT_DF_CMN_PF_PDR_MODE_AUTO = 0xFF,///<Auto
  IDSOPT_DF_CMN_PF_PDR_MODE_SELECTIVE = 0x1,///<Selective
  IDSOPT_DF_CMN_PF_PDR_MODE_ALL = 0x0,///<All
} IDSOPT_DF_CMN_PF_PDR_MODE;

///Chipselect Interleaving
///Interleave memory blocks across the DRAM chip selects for node 0.
typedef enum {
  IDSOPT_CMN_MEM_CS_INTERLEAVE_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_CS_INTERLEAVE_DDR_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_MEM_CS_INTERLEAVE_DDR;

///Address Hash Bank
///Enable or disable bank address hashing
typedef enum {
  IDSOPT_CMN_MEM_ADDRESS_HASH_BANK_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_ADDRESS_HASH_BANK_DDR_ENABLED = 1,///<Enabled
  IDSOPT_CMN_MEM_ADDRESS_HASH_BANK_DDR_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_MEM_ADDRESS_HASH_BANK_DDR;

///Address Hash CS
///Enable or disable CS address hashing
typedef enum {
  IDSOPT_CMN_MEM_ADDRESS_HASH_CS_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_ADDRESS_HASH_CS_DDR_ENABLED = 1,///<Enabled
  IDSOPT_CMN_MEM_ADDRESS_HASH_CS_DDR_DISABLED = 0,///<Disabled
} IDSOPT_CMN_MEM_ADDRESS_HASH_CS_DDR;

///Address Hash Rm
///Enable or disable RM address hashing
typedef enum {
  IDSOPT_CMN_MEM_ADDRESS_HASH_RM_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_ADDRESS_HASH_RM_DDR_ENABLED = 1,///<Enabled
  IDSOPT_CMN_MEM_ADDRESS_HASH_RM_DDR_DISABLED = 0,///<Disabled
} IDSOPT_CMN_MEM_ADDRESS_HASH_RM_DDR;

///Address Hash Subchannel
///Enable or disable sub-channel address hashing
typedef enum {
  IDSOPT_CMN_MEM_ADDRESS_HASH_SUBCHANNEL_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_ADDRESS_HASH_SUBCHANNEL_DDR_ENABLED = 1,///<Enabled
  IDSOPT_CMN_MEM_ADDRESS_HASH_SUBCHANNEL_DDR_DISABLED = 0,///<Disabled
} IDSOPT_CMN_MEM_ADDRESS_HASH_SUBCHANNEL_DDR;

///BankSwapMode
///BankSwapMode value: 0=Disabled, 1=SwapCPU
typedef enum {
  IDSOPT_CMN_MEM_CTRLLER_BANK_SWAP_MODE_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_CTRLLER_BANK_SWAP_MODE_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_CTRLLER_BANK_SWAP_MODE_DDR_SWAPCPU = 1,///<Swap CPU
} IDSOPT_CMN_MEM_CTRLLER_BANK_SWAP_MODE_DDR;

///Memory Context Restore
///Configure the memory context restore mode. When enabled, DRAM re-retraining is avoided when possible and the POST latency is minimized.
typedef enum {
  IDSOPT_CMN_MEM_CONTEXT_RESTORE_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_CONTEXT_RESTORE_DDR_ENABLED = 1,///<Enabled
  IDSOPT_CMN_MEM_CONTEXT_RESTORE_DDR_DISABLED = 0,///<Disabled
} IDSOPT_CMN_MEM_CONTEXT_RESTORE_DDR;

///DRAM Survives Warm Reset
///1 - Enabled (default); 0 - Disabled\nIf enabled - Upon warm reset DRAM content is preserved, Training values are saved & retrieved.
typedef enum {
  IDSOPT_DRAM_SURVIVES_WARM_RESET_DISABLED = 0,///<Disabled
  IDSOPT_DRAM_SURVIVES_WARM_RESET_ENABLED = 1,///<Enabled
} IDSOPT_DRAM_SURVIVES_WARM_RESET;

///Power Down Enable
///Enable or disable DDR power down mode
typedef enum {
  IDSOPT_CMN_MEM_CTRLLER_PWR_DN_EN_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_CTRLLER_PWR_DN_EN_DDR_ENABLED = 1,///<Enabled
  IDSOPT_CMN_MEM_CTRLLER_PWR_DN_EN_DDR_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_MEM_CTRLLER_PWR_DN_EN_DDR;

///Sub Urgent Refresh Lower Bound
///Specifies the stored refresh limit required to enter sub-urgent refresh mode\nConstraint: SubUrgRefLowerBound <= UrgRefLimit\nValid value: 6 ~ 1
#define IDSOPT_CMN_MEM_SUB_URG_REF_LOWER_BOUND_MIN 1 ///< Min of Sub Urgent Refresh Lower Bound
#define IDSOPT_CMN_MEM_SUB_URG_REF_LOWER_BOUND_MAX 6 ///< Max of Sub Urgent Refresh Lower Bound

///Urgent Refresh Limit
///Specifies the stored refresh limit required to enter urgent refresh mode\nConstraint: SubUrgRefLowerBound <= UrgRefLimit\nValid value: 6 ~ 1
#define IDSOPT_CMN_MEM_URG_REF_LIMIT_MIN 1 ///< Min of Urgent Refresh Limit
#define IDSOPT_CMN_MEM_URG_REF_LIMIT_MAX 6 ///< Max of Urgent Refresh Limit

///DRAM Refresh Rate
///DRAM refresh rate: 1.95us or 3.9us (default)
typedef enum {
  IDSOPT_CMN_MEM_DRAM_REFRESH_RATE_39USEC = 0,///<3.9 usec
  IDSOPT_CMN_MEM_DRAM_REFRESH_RATE_195USEC = 1,///<1.95 usec
} IDSOPT_CMN_MEM_DRAM_REFRESH_RATE;

///Self-Refresh Exit Staggering
///Tcksrx += (Trfc/n * (UMC_Number % 3))\n\nSelectable by CBS Option:\nDisable Staggering\nn = 1 <= Stagger Channels by ~270 ns\nn = 2\nn = 3\nn = 4\n. . .\nn = 9 <= Stagger Channels By ~30 ns (Default)\n
typedef enum {
  IDSOPT_CMN_MEM_SELF_REFRESH_EXIT_STAGGERING_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_SELF_REFRESH_EXIT_STAGGERING_N1 = 1,///<n = 1
  IDSOPT_CMN_MEM_SELF_REFRESH_EXIT_STAGGERING_N2 = 2,///<n = 2
  IDSOPT_CMN_MEM_SELF_REFRESH_EXIT_STAGGERING_N3 = 3,///<n = 3
  IDSOPT_CMN_MEM_SELF_REFRESH_EXIT_STAGGERING_N4 = 4,///<n = 4
  IDSOPT_CMN_MEM_SELF_REFRESH_EXIT_STAGGERING_N5 = 5,///<n = 5
  IDSOPT_CMN_MEM_SELF_REFRESH_EXIT_STAGGERING_N6 = 6,///<n = 6
  IDSOPT_CMN_MEM_SELF_REFRESH_EXIT_STAGGERING_N7 = 7,///<n = 7
  IDSOPT_CMN_MEM_SELF_REFRESH_EXIT_STAGGERING_N8 = 8,///<n = 8
  IDSOPT_CMN_MEM_SELF_REFRESH_EXIT_STAGGERING_N9 = 9,///<n = 9
} IDSOPT_CMN_MEM_SELF_REFRESH_EXIT_STAGGERING;

///DRAM 2x Refresh Temperature Threshold
///Determine the DDR temperature threshold to activate 2x REF rate per the DRAM MR readout.
typedef enum {
  IDSOPT_CMN_MEMT2X_REFRESH_TEMPERATURE_THRESHOLD_8590 = 2,///<85' - 90'
  IDSOPT_CMN_MEMT2X_REFRESH_TEMPERATURE_THRESHOLD_9095 = 3,///<90' - 95'
  IDSOPT_CMN_MEMT2X_REFRESH_TEMPERATURE_THRESHOLD_95100 = 4,///<95' - 100'
  IDSOPT_CMN_MEMT2X_REFRESH_TEMPERATURE_THRESHOLD_100 = 5,///<> 100'
} IDSOPT_CMN_MEMT2X_REFRESH_TEMPERATURE_THRESHOLD;

///Memory Channel Disable Float Power Good
///Float Power Good when channel is disabled by BIOS setup options.
typedef enum {
  IDSOPT_CMN_MEM_CHANNEL_DISABLE_FLOAT_POWER_GOOD_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_CHANNEL_DISABLE_FLOAT_POWER_GOOD_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_CHANNEL_DISABLE_FLOAT_POWER_GOOD_DDR;

///Memory Channel Disable Bitmask
///Bit[0:11] Disable Socket 0 Channel [0:11]\nBit[12:15] Reserved\nBit[16:27] Disable Socket 1 Channel [0:11]\nBit[28:31] Reserved
#define IDSOPT_CMN_MEM_CHANNEL_DISABLE_BITMASK_DDR_MIN 0 ///< Min of Memory Channel Disable Bitmask
#define IDSOPT_CMN_MEM_CHANNEL_DISABLE_BITMASK_DDR_MAX 0xFFFFFFFF ///< Max of Memory Channel Disable Bitmask

///Socket 0 Channel 0
///SPD reading will be skipped when channel is disabled.
typedef enum {
  IDSOPT_CMN_MEM_SOCKET0_CHANNEL0_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_SOCKET0_CHANNEL0_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_SOCKET0_CHANNEL0_DDR;

///Socket 0 Channel 1
///SPD reading will be skipped when channel is disabled.
typedef enum {
  IDSOPT_CMN_MEM_SOCKET0_CHANNEL1_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_SOCKET0_CHANNEL1_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_SOCKET0_CHANNEL1_DDR;

///Socket 0 Channel 2
///SPD reading will be skipped when channel is disabled.
typedef enum {
  IDSOPT_CMN_MEM_SOCKET0_CHANNEL2_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_SOCKET0_CHANNEL2_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_SOCKET0_CHANNEL2_DDR;

///Socket 0 Channel 3
///SPD reading will be skipped when channel is disabled.
typedef enum {
  IDSOPT_CMN_MEM_SOCKET0_CHANNEL3_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_SOCKET0_CHANNEL3_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_SOCKET0_CHANNEL3_DDR;

///Socket 0 Channel 4
///SPD reading will be skipped when channel is disabled.
typedef enum {
  IDSOPT_CMN_MEM_SOCKET0_CHANNEL4_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_SOCKET0_CHANNEL4_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_SOCKET0_CHANNEL4_DDR;

///Socket 0 Channel 5
///SPD reading will be skipped when channel is disabled.
typedef enum {
  IDSOPT_CMN_MEM_SOCKET0_CHANNEL5_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_SOCKET0_CHANNEL5_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_SOCKET0_CHANNEL5_DDR;

///Socket 0 Channel 6
///SPD reading will be skipped when channel is disabled.
typedef enum {
  IDSOPT_CMN_MEM_SOCKET0_CHANNEL6_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_SOCKET0_CHANNEL6_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_SOCKET0_CHANNEL6_DDR;

///Socket 0 Channel 7
///SPD reading will be skipped when channel is disabled.
typedef enum {
  IDSOPT_CMN_MEM_SOCKET0_CHANNEL7_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_SOCKET0_CHANNEL7_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_SOCKET0_CHANNEL7_DDR;

///Socket 0 Channel 8
///SPD reading will be skipped when channel is disabled.
typedef enum {
  IDSOPT_CMN_MEM_SOCKET0_CHANNEL8_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_SOCKET0_CHANNEL8_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_SOCKET0_CHANNEL8_DDR;

///Socket 0 Channel 9
///SPD reading will be skipped when channel is disabled.
typedef enum {
  IDSOPT_CMN_MEM_SOCKET0_CHANNEL9_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_SOCKET0_CHANNEL9_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_SOCKET0_CHANNEL9_DDR;

///Socket 0 Channel 10
///SPD reading will be skipped when channel is disabled.
typedef enum {
  IDSOPT_CMN_MEM_SOCKET0_CHANNEL10_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_SOCKET0_CHANNEL10_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_SOCKET0_CHANNEL10_DDR;

///Socket 0 Channel 11
///SPD reading will be skipped when channel is disabled.
typedef enum {
  IDSOPT_CMN_MEM_SOCKET0_CHANNEL11_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_SOCKET0_CHANNEL11_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_SOCKET0_CHANNEL11_DDR;

///Socket 1 Channel 0
///SPD reading will be skipped when channel is disabled.
typedef enum {
  IDSOPT_CMN_MEM_SOCKET1_CHANNEL0_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_SOCKET1_CHANNEL0_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_SOCKET1_CHANNEL0_DDR;

///Socket 1 Channel 1
///SPD reading will be skipped when channel is disabled.
typedef enum {
  IDSOPT_CMN_MEM_SOCKET1_CHANNEL1_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_SOCKET1_CHANNEL1_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_SOCKET1_CHANNEL1_DDR;

///Socket 1 Channel 2
///SPD reading will be skipped when channel is disabled.
typedef enum {
  IDSOPT_CMN_MEM_SOCKET1_CHANNEL2_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_SOCKET1_CHANNEL2_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_SOCKET1_CHANNEL2_DDR;

///Socket 1 Channel 3
///SPD reading will be skipped when channel is disabled.
typedef enum {
  IDSOPT_CMN_MEM_SOCKET1_CHANNEL3_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_SOCKET1_CHANNEL3_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_SOCKET1_CHANNEL3_DDR;

///Socket 1 Channel 4
///SPD reading will be skipped when channel is disabled.
typedef enum {
  IDSOPT_CMN_MEM_SOCKET1_CHANNEL4_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_SOCKET1_CHANNEL4_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_SOCKET1_CHANNEL4_DDR;

///Socket 1 Channel 5
///SPD reading will be skipped when channel is disabled.
typedef enum {
  IDSOPT_CMN_MEM_SOCKET1_CHANNEL5_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_SOCKET1_CHANNEL5_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_SOCKET1_CHANNEL5_DDR;

///Socket 1 Channel 6
///SPD reading will be skipped when channel is disabled.
typedef enum {
  IDSOPT_CMN_MEM_SOCKET1_CHANNEL6_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_SOCKET1_CHANNEL6_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_SOCKET1_CHANNEL6_DDR;

///Socket 1 Channel 7
///SPD reading will be skipped when channel is disabled.
typedef enum {
  IDSOPT_CMN_MEM_SOCKET1_CHANNEL7_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_SOCKET1_CHANNEL7_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_SOCKET1_CHANNEL7_DDR;

///Socket 1 Channel 8
///SPD reading will be skipped when channel is disabled.
typedef enum {
  IDSOPT_CMN_MEM_SOCKET1_CHANNEL8_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_SOCKET1_CHANNEL8_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_SOCKET1_CHANNEL8_DDR;

///Socket 1 Channel 9
///SPD reading will be skipped when channel is disabled.
typedef enum {
  IDSOPT_CMN_MEM_SOCKET1_CHANNEL9_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_SOCKET1_CHANNEL9_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_SOCKET1_CHANNEL9_DDR;

///Socket 1 Channel 10
///SPD reading will be skipped when channel is disabled.
typedef enum {
  IDSOPT_CMN_MEM_SOCKET1_CHANNEL10_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_SOCKET1_CHANNEL10_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_SOCKET1_CHANNEL10_DDR;

///Socket 1 Channel 11
///SPD reading will be skipped when channel is disabled.
typedef enum {
  IDSOPT_CMN_MEM_SOCKET1_CHANNEL11_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_SOCKET1_CHANNEL11_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_SOCKET1_CHANNEL11_DDR;

///Refresh Management
///Auto\nDisable: Disable RFM for all Ranks.\nEnable: Enable RFM for Ranks which support RFM.\nForce Enable: Enable RFM for all Ranks regardless of support.\nSelecting 'Force Enable' will cause REFpb/REFsb to be disabled if all ranks do not support RFM
typedef enum {
  IDSOPT_CMN_MEM_REF_MANAGEMENT_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_REF_MANAGEMENT_DDR_DISABLE = 0,///<Disable
  IDSOPT_CMN_MEM_REF_MANAGEMENT_DDR_ENABLE = 1,///<Enable
  IDSOPT_CMN_MEM_REF_MANAGEMENT_DDR_FORCEENABLE = 2,///<Force Enable
} IDSOPT_CMN_MEM_REF_MANAGEMENT_DDR;

///Adaptive Refresh Management
///Apply the Settings for RAAIMT, RAAMMT, and RefDecrement which are associated with the selected level.
typedef enum {
  IDSOPT_CMN_MEM_ARFM_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_ARFM_DDR_DISABLE = 0,///<Disable
  IDSOPT_CMN_MEM_ARFM_DDR_ARFMLEVELA = 1,///<ARFM Level A
  IDSOPT_CMN_MEM_ARFM_DDR_ARFMLEVELB = 2,///<ARFM Level B
  IDSOPT_CMN_MEM_ARFM_DDR_ARFMLEVELC = 3,///<ARFM Level C
} IDSOPT_CMN_MEM_ARFM_DDR;

///RAA Initial Management Threshold
///Override Rolling Accumulated ACT Initial Management Threshold\nAuto: BIOS will choose the lowest supported value from SPD.\nChoices from list are for Normal Refresh Mode. In Fine Granularity Mode, the value will be divided by 2
typedef enum {
  IDSOPT_CMN_MEM_RAAIMT_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_RAAIMT_DDR_32 = 32,///<32
  IDSOPT_CMN_MEM_RAAIMT_DDR_40 = 40,///<40
  IDSOPT_CMN_MEM_RAAIMT_DDR_48 = 48,///<48
  IDSOPT_CMN_MEM_RAAIMT_DDR_56 = 56,///<56
  IDSOPT_CMN_MEM_RAAIMT_DDR_64 = 64,///<64
  IDSOPT_CMN_MEM_RAAIMT_DDR_72 = 72,///<72
  IDSOPT_CMN_MEM_RAAIMT_DDR_80 = 80,///<80
} IDSOPT_CMN_MEM_RAAIMT_DDR;

///RAA Maximum Management Threshold
///Override Rolling Accumulated ACT Maximum Management Threshold\nAuto: BIOS will choose the lowest supported value from SPD.\nChoices from list are for Normal Refresh Mode. Logic handles adjustment for Fine Granularity Mode
typedef enum {
  IDSOPT_CMN_MEM_RAAMMT_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_RAAMMT_DDR_3X = 2,///<3X
  IDSOPT_CMN_MEM_RAAMMT_DDR_4X = 3,///<4X
  IDSOPT_CMN_MEM_RAAMMT_DDR_5X = 4,///<5X
  IDSOPT_CMN_MEM_RAAMMT_DDR_6X = 5,///<6X
} IDSOPT_CMN_MEM_RAAMMT_DDR;

///RAA Refresh Decrement Multiplier
///Override RAA Refresh Decrement Multiplier\nAuto: BIOS will choose the lowest supported value from SPD
typedef enum {
  IDSOPT_CMN_MEM_RAA_REF_DEC_MULTIPLIER_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_RAA_REF_DEC_MULTIPLIER_DDR_05 = 0,///<0.5
  IDSOPT_CMN_MEM_RAA_REF_DEC_MULTIPLIER_DDR_1 = 1,///<1
} IDSOPT_CMN_MEM_RAA_REF_DEC_MULTIPLIER_DDR;

///DRFM Enable
///Enable DRFM for any ranks that support it. (Enable/Disable)
typedef enum {
  IDSOPT_CMN_MEM_DRFM_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_DRFM_DDR_DISABLE = 0,///<Disable
  IDSOPT_CMN_MEM_DRFM_DDR_ENABLE = 1,///<Enable
} IDSOPT_CMN_MEM_DRFM_DDR;

///Bounded Refresh Configuration
///Set Bounded Refresh Configuration Level. The selected level will be used for each rank that supports it. For ranks that only support a BRC of 2, then 2 will be used regardless of this selection.
typedef enum {
  IDSOPT_CMN_MEM_DRFM_BRC_DDR_BRC2 = 2,///<BRC2
  IDSOPT_CMN_MEM_DRFM_BRC_DDR_BRC3 = 3,///<BRC3
  IDSOPT_CMN_MEM_DRFM_BRC_DDR_BRC4 = 4,///<BRC4
} IDSOPT_CMN_MEM_DRFM_BRC_DDR;

///DRFM Hash Enable
///Enable DRFM Hashing for all channels
typedef enum {
  IDSOPT_CMN_MEM_DRFM_HASH_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_DRFM_HASH_DDR_DISABLE = 0,///<Disable
  IDSOPT_CMN_MEM_DRFM_HASH_DDR_ENABLE = 1,///<Enable
} IDSOPT_CMN_MEM_DRFM_HASH_DDR;

///MBIST Enable
///Enable or disable Memory MBIST
typedef enum {
  IDSOPT_CMN_MEM_MBIST_EN_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_MBIST_EN_DDR_ENABLED = 1,///<Enabled
  IDSOPT_CMN_MEM_MBIST_EN_DDR_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_MEM_MBIST_EN_DDR;

///MBIST Test Mode
///Select MBIST Test Mode -Interface Mode (Tests Single and Multiple CS transactions and Basic Connectivity) or Data Eye Mode (Measures Voltage vs. Timing)
typedef enum {
  IDSOPT_CMN_MEM_MBIST_TESTMODE_DDR_INTERFACEMODE = 0,///<Interface Mode
  IDSOPT_CMN_MEM_MBIST_TESTMODE_DDR_DATAEYEMODE = 1,///<Data Eye Mode
  IDSOPT_CMN_MEM_MBIST_TESTMODE_DDR_BOTH = 2,///<Both
  IDSOPT_CMN_MEM_MBIST_TESTMODE_DDR_AUTO = 0xFF,///<AUTO
} IDSOPT_CMN_MEM_MBIST_TESTMODE_DDR;

///MBIST Aggressors
///Enable or disable MBIST Aggressor test
typedef enum {
  IDSOPT_CMN_MEM_MBIST_AGGRESSORS_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_MBIST_AGGRESSORS_DDR_ENABLED = 1,///<Enabled
  IDSOPT_CMN_MEM_MBIST_AGGRESSORS_DDR_AUTO = 0xff,///<Auto
} IDSOPT_CMN_MEM_MBIST_AGGRESSORS_DDR;

///DDR Healing BIST
///This item enables a full memory test. Please note that this is a memory content test and is separate and distinct from the MBIST test of Interface and Data Eye.\nPMU Mem BIST: this uses PMU firmware to test memory on all channels simultaneously. Failing memory will be repaired using soft or hard PPR depending on the PPR configuration.\nSelf-Healing Mem BIST: this runs the JEDEC DRAM self healing, if the device and DIMM support the feature. The DRAM will do a hard repair for failing memory.\nPMU and Self-Healing Mem BIST: this option runs the PMU Mem BIST then the Self-Healing Mem BIST tests sequentially.
typedef enum {
  IDSOPT_CMN_MEM_HEALING_BIST_ENABLE_BIT_MASK_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_HEALING_BIST_ENABLE_BIT_MASK_DDR_PMUMEMBIST = 1,///<PMU Mem BIST
  IDSOPT_CMN_MEM_HEALING_BIST_ENABLE_BIT_MASK_DDR_SELFHEALINGMEMBIST = 2,///<Self-Healing Mem BIST
  IDSOPT_CMN_MEM_HEALING_BIST_ENABLE_BIT_MASK_DDR_PMUANDSELFHEALINGMEMBIST = 3,///<PMU and Self-Healing Mem BIST
} IDSOPT_CMN_MEM_HEALING_BIST_ENABLE_BIT_MASK_DDR;

///DDR Healing BIST Execution Mode
///[One Time]: DDR Healing BIST will only be executed one time.\n[Every Boot]: DDR Healing BIST will be executed on every boot.
typedef enum {
  IDSOPT_CMN_MEM_HEALING_BIST_EXECUTION_MODE_ONETIME = 0,///<One Time
  IDSOPT_CMN_MEM_HEALING_BIST_EXECUTION_MODE_EVERYBOOT = 1,///<Every Boot
} IDSOPT_CMN_MEM_HEALING_BIST_EXECUTION_MODE;

///DDR Healing BIST Repair Type
///For DRAM errors found in the BIOS memory BIST select the repair type, soft, hard or test only and do not attempt to repair.
typedef enum {
  IDSOPT_CMN_MEM_HEALING_BIST_REPAIR_TYPE_DDR_SOFTREPAIR = 0,///<Soft Repair
  IDSOPT_CMN_MEM_HEALING_BIST_REPAIR_TYPE_DDR_HARDREPAIR = 1,///<Hard Repair
  IDSOPT_CMN_MEM_HEALING_BIST_REPAIR_TYPE_DDR_NOREPAIRSTESTONLY = 2,///<No Repairs - Test only
} IDSOPT_CMN_MEM_HEALING_BIST_REPAIR_TYPE_DDR;

///PMU Mem BIST Algorithm Select
///PMU Mem BIST algorithms that will be performed can be selected by user or vendor
typedef enum {
  IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM_SELECT_BYUSER = 0,///<By User
  IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM_SELECT_BYVENDOR = 1,///<By Vendor
  IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM_SELECT_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM_SELECT;

///PMU Mem BIST Algorithm Bitmask
///Bitmask of the algorithms that will be used for PMU Mem BIST.\nBIT0:Algorithm #1\nBIT1:Algorithm #2\nBIT2:Algorithm #3\nBIT3:Algorithm #4\nBIT4:Algorithm #5\nBIT5:Algorithm #6\nBIT6:Algorithm #7\nBIT7:Algorithm #8\nBIT8:Algorithm #9
#define IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM_BIT_MASK_DDR_MIN 0 ///< Min of PMU Mem BIST Algorithm Bitmask
#define IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM_BIT_MASK_DDR_MAX 0x1FF ///< Max of PMU Mem BIST Algorithm Bitmask

///Algorithm #1
///Algorithm #1: Write-Pause-Read
typedef enum {
  IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM1_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM1_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM1;

///Algorithm #2
///Algorithm #2: Write-Pause-Read, short tWR
typedef enum {
  IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM2_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM2_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM2;

///Algorithm #3
///Algorithm #3: Row Victim Aggressor
typedef enum {
  IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM3_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM3_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM3;

///Algorithm #4
///Algorithm #4: Write-Pause-Read, Closed Page
typedef enum {
  IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM4_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM4_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM4;

///Algorithm #5
///Algorithm #5: Closed Page Stress Test
typedef enum {
  IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM5_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM5_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM5;

///Algorithm #6
///Algorithm #6: Double Row Victim Aggressor
typedef enum {
  IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM6_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM6_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM6;

///Algorithm #7
///Algorithm #7: Open page Checkerboard RMW
typedef enum {
  IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM7_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM7_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM7;

///Algorithm #8
///Algorithm #8: Closed page tWR Stress
typedef enum {
  IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM8_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM8_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM8;

///Algorithm #9
///Algorithm #9: Open Page Static Refresh Test
typedef enum {
  IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM9_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM9_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM9;

///Pattern Select
///MBIST Data Eye Pattern Type. 0 - PRBS (default), 1 - SSO, 2 - Both
typedef enum {
  IDSOPT_CMN_MEM_MBIST_PATTERN_SELECT_PRBS = 0,///<PRBS
  IDSOPT_CMN_MEM_MBIST_PATTERN_SELECT_SSO = 1,///<SSO
  IDSOPT_CMN_MEM_MBIST_PATTERN_SELECT_BOTH = 2,///<Both
} IDSOPT_CMN_MEM_MBIST_PATTERN_SELECT;

///Pattern Length
///This token determine the pattern length, available options: 3...C (input hex number, not decimal)
#define IDSOPT_CMN_MEM_MBIST_PATTERN_LENGTH_MIN 3 ///< Min of Pattern Length
#define IDSOPT_CMN_MEM_MBIST_PATTERN_LENGTH_MAX 12 ///< Max of Pattern Length

///Aggressor Channel
///One Sub-Channel enables the non-target subchannel on the target channel to be an aggressor. Half Channels enables all non-target channels on one half of the processor to be aggressors. All Channels enables all non-target channels to be aggressors.
typedef enum {
  IDSOPT_CMN_MEM_MBIST_AGGRESSORS_CHNL_ONESUBCHANNEL = 0,///<One Sub-Channel
  IDSOPT_CMN_MEM_MBIST_AGGRESSORS_CHNL_HALFCHANNELS = 1,///<Half Channels
  IDSOPT_CMN_MEM_MBIST_AGGRESSORS_CHNL_ALLCHANNELS = 2,///<All Channels
} IDSOPT_CMN_MEM_MBIST_AGGRESSORS_CHNL;

///Aggressor Static Lane Control
///This option, if enabled, will control the Aggressor Static Lane Controls.\n
typedef enum {
  IDSOPT_CMN_MEM_MBIST_AGGR_STATIC_LANE_CTRL_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_MBIST_AGGR_STATIC_LANE_CTRL_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_MBIST_AGGR_STATIC_LANE_CTRL;

///Aggressor Static Lane Select Upper 32 bits
///Static Lane Select for Upper 32 bits. The bit mask represents the bits to be read
#define IDSOPT_CMN_MEM_MBIST_AGGR_STATIC_LANE_SEL_U32_MIN 0 ///< Min of Aggressor Static Lane Select Upper 32 bits
#define IDSOPT_CMN_MEM_MBIST_AGGR_STATIC_LANE_SEL_U32_MAX 0xFFFFFFFF ///< Max of Aggressor Static Lane Select Upper 32 bits

///Aggressor Static Lane Select Lower 32 Bits
///Static Lane Select for Lower 32 bits. The bit mask represents the bits to be read
#define IDSOPT_CMN_MEM_MBIST_AGGR_STATIC_LANE_SEL_L32_MIN 0 ///< Min of Aggressor Static Lane Select Lower 32 Bits
#define IDSOPT_CMN_MEM_MBIST_AGGR_STATIC_LANE_SEL_L32_MAX 0xFFFFFFFF ///< Max of Aggressor Static Lane Select Lower 32 Bits

///Aggressor Static Lane Select ECC
///Static Lane Select for ECC Lanes. The bit mask represents the bits to be read
#define IDSOPT_CMN_MEM_MBIST_AGGR_STATIC_LANE_SEL_ECC_MIN 0 ///< Min of Aggressor Static Lane Select ECC
#define IDSOPT_CMN_MEM_MBIST_AGGR_STATIC_LANE_SEL_ECC_MAX 0xa ///< Max of Aggressor Static Lane Select ECC

///Aggressor Static Lane Value
///TBD
#define IDSOPT_CMN_MEM_MBIST_AGGR_STATIC_LANE_VAL_MIN 0 ///< Min of Aggressor Static Lane Value
#define IDSOPT_CMN_MEM_MBIST_AGGR_STATIC_LANE_VAL_MAX 0xa ///< Max of Aggressor Static Lane Value

///Target Static Lane Control
///Enable Mbist Target Static Lane Control
typedef enum {
  IDSOPT_CMN_MEM_MBIST_TGT_STATIC_LANE_CTRL_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_MBIST_TGT_STATIC_LANE_CTRL_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_MBIST_TGT_STATIC_LANE_CTRL;

///Target Static Lane Select Upper 32 bit
///Static Lane Select for Upper 32 bit. The bit mask represents the bits to be read
#define IDSOPT_CMN_MEM_MBIST_TGT_STATIC_LANE_SEL_U32_MIN 0 ///< Min of Target Static Lane Select Upper 32 bit
#define IDSOPT_CMN_MEM_MBIST_TGT_STATIC_LANE_SEL_U32_MAX 0xFFFFFFFF ///< Max of Target Static Lane Select Upper 32 bit

///Target Static Lane Select Lower 32 Bits
///Static Lane Select for Lower 32 bit. The bit mask represents the bits to be read
#define IDSOPT_CMN_MEM_MBIST_TGT_STATIC_LANE_SEL_L32_MIN 0 ///< Min of Target Static Lane Select Lower 32 Bits
#define IDSOPT_CMN_MEM_MBIST_TGT_STATIC_LANE_SEL_L32_MAX 0xa ///< Max of Target Static Lane Select Lower 32 Bits

///Target Static Lane Select ECC
///TBD
#define IDSOPT_CMN_MEM_MBIST_TGT_STATIC_LANE_SEL_ECC_MIN 0 ///< Min of Target Static Lane Select ECC
#define IDSOPT_CMN_MEM_MBIST_TGT_STATIC_LANE_SEL_ECC_MAX 0xa ///< Max of Target Static Lane Select ECC

///Target Static Lane Value
///Value for Mbsit target static lane. Enable 'Target Static Lane Control' option to enter the value
#define IDSOPT_CMN_MEM_MBIST_TGT_STATIC_LANE_VAL_MIN 0 ///< Min of Target Static Lane Value
#define IDSOPT_CMN_MEM_MBIST_TGT_STATIC_LANE_VAL_MAX 0xa ///< Max of Target Static Lane Value

///Read Voltage Sweep Step Size
///This option determines the step size for Read Data Eye voltage sweep, Supported options are 1,2 and 4
typedef enum {
  IDSOPT_CMN_MEM_MBIST_READ_DATA_EYE_VOLTAGE_STEP_1 = 1,///<1
  IDSOPT_CMN_MEM_MBIST_READ_DATA_EYE_VOLTAGE_STEP_2 = 2,///<2
  IDSOPT_CMN_MEM_MBIST_READ_DATA_EYE_VOLTAGE_STEP_4 = 4,///<4
} IDSOPT_CMN_MEM_MBIST_READ_DATA_EYE_VOLTAGE_STEP;

///Read Timing Sweep Step Size
///This options supports step size for Read Data Eye. Supported options are 1, 2 and 4
typedef enum {
  IDSOPT_CMN_MEM_MBIST_READ_DATA_EYE_TIMING_STEP_1 = 1,///<1
  IDSOPT_CMN_MEM_MBIST_READ_DATA_EYE_TIMING_STEP_2 = 2,///<2
  IDSOPT_CMN_MEM_MBIST_READ_DATA_EYE_TIMING_STEP_4 = 4,///<4
} IDSOPT_CMN_MEM_MBIST_READ_DATA_EYE_TIMING_STEP;

///Write Voltage Sweep Step Size
///This option determines the step size for write Data Eye voltage sweep, Supported options are 1,2 and 4
typedef enum {
  IDSOPT_CMN_MEM_MBIST_WRITE_DATA_EYE_VOLTAGE_STEP_1 = 1,///<1
  IDSOPT_CMN_MEM_MBIST_WRITE_DATA_EYE_VOLTAGE_STEP_2 = 2,///<2
  IDSOPT_CMN_MEM_MBIST_WRITE_DATA_EYE_VOLTAGE_STEP_4 = 4,///<4
} IDSOPT_CMN_MEM_MBIST_WRITE_DATA_EYE_VOLTAGE_STEP;

///Write Timing Sweep Step Size
///This options supports step size for write Data Eye. Supported options are 1, 2 and 4
typedef enum {
  IDSOPT_CMN_MEM_MBIST_WRITE_DATA_EYE_TIMING_STEP_1 = 1,///<1
  IDSOPT_CMN_MEM_MBIST_WRITE_DATA_EYE_TIMING_STEP_2 = 2,///<2
  IDSOPT_CMN_MEM_MBIST_WRITE_DATA_EYE_TIMING_STEP_4 = 4,///<4
} IDSOPT_CMN_MEM_MBIST_WRITE_DATA_EYE_TIMING_STEP;

///Silent Execution
///Execute MBIST Data Eye silently without ABL log output\nDisabled - MBIST Enable will not be overridden\nEnabled - Execute MBIST Data Eye silently without ABL log output
typedef enum {
  IDSOPT_CMN_MEM_MBIST_DATAEYE_SILENT_EXECUTION_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_MBIST_DATAEYE_SILENT_EXECUTION_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_MBIST_DATAEYE_SILENT_EXECUTION;

///Data Poisoning
/// Enable poison data creation on uncorrectable DDR DRAM ECC errors and poison propagation to CPU cores and caches. Requires ECC memory. When FALSE, a fatal error event will occur on DDR ECC errors\nsets UMC_CH::EccCtrl[UcFatalEn] when MC_CH::EccCtrl[WrEccEn] is set.
typedef enum {
  IDSOPT_CMN_MEM_DATA_POISONING_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_DATA_POISONING_DDR_ENABLED = 1,///<Enabled
  IDSOPT_CMN_MEM_DATA_POISONING_DDR_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_MEM_DATA_POISONING_DDR;

///DRAM Boot Time Post Package Repair
///Enable or Disable DRAM Boot Time Post Package Repair.\n
typedef enum {
  IDSOPT_CMN_MEM_BOOT_TIME_POST_PACKAGE_REPAIR_ENABLE = 1,///<Enable
  IDSOPT_CMN_MEM_BOOT_TIME_POST_PACKAGE_REPAIR_DISABLE = 0,///<Disable
} IDSOPT_CMN_MEM_BOOT_TIME_POST_PACKAGE_REPAIR;

///DRAM Runtime Post Package Repair
///Enable or Disable DRAM Run Time Post Package Repair.\n
typedef enum {
  IDSOPT_CMN_MEM_RUNTIME_POST_PACKAGE_REPAIR_ENABLE = 1,///<Enable
  IDSOPT_CMN_MEM_RUNTIME_POST_PACKAGE_REPAIR_DISABLE = 0,///<Disable
} IDSOPT_CMN_MEM_RUNTIME_POST_PACKAGE_REPAIR;

///DRAM Post Package Repair Config Initiator
///This option will indicate the Post Package Repair configuration setting whether it is In-Band or Out-Of-Band Repair.
typedef enum {
  IDSOPT_CMN_MEM_POST_PACKAGE_REPAIR_CONFIG_INITIATOR_INBAND = 0,///<In-Band
  IDSOPT_CMN_MEM_POST_PACKAGE_REPAIR_CONFIG_INITIATOR_OUTOFBAND = 1,///<Out Of Band
} IDSOPT_CMN_MEM_POST_PACKAGE_REPAIR_CONFIG_INITIATOR;

///RCD Parity
///Enable RCD command and address parity.
typedef enum {
  IDSOPT_CMN_MEM_RCD_PARITY_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_RCD_PARITY_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_RCD_PARITY_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_RCD_PARITY_DDR;

///Max RCD Parity Error Replay
///Program to UMC::RecCtrl[MaxParRply], valid value:1 - 3F hex, default 8,
#define IDSOPT_CMN_MEM_MAX_RCD_PARITY_ERROR_REPLAY_DDR_MIN 1 ///< Min of Max RCD Parity Error Replay
#define IDSOPT_CMN_MEM_MAX_RCD_PARITY_ERROR_REPLAY_DDR_MAX 0x3f ///< Max of Max RCD Parity Error Replay

///Write CRC
///Enable write CRC on DDR5 DRAM
typedef enum {
  IDSOPT_CMN_MEM_WRITE_CRC_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_WRITE_CRC_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_WRITE_CRC_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_WRITE_CRC_DDR;

///Max Write CRC Error Replay
///Program to UMC::RecCtrl [MaxCrcRply], valid value:1 - 3F hex, default 8
#define IDSOPT_CMN_MEM_MAX_WRITE_CRC_ERROR_REPLAY_DDR_MIN 1 ///< Min of Max Write CRC Error Replay
#define IDSOPT_CMN_MEM_MAX_WRITE_CRC_ERROR_REPLAY_DDR_MAX 0x3f ///< Max of Max Write CRC Error Replay

///Read CRC
///Program to RecCtrl.RecEn [3]
typedef enum {
  IDSOPT_CMN_MEM_READ_CRC_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_READ_CRC_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_READ_CRC_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_READ_CRC_DDR;

///Max Read CRC Error Replay
///Program to UMC::RecCtrl2 [MaxRdCrcRply], valid value:1 - 3F hex, default 8
#define IDSOPT_CMN_MEM_MAX_READ_CRC_ERROR_REPLAY_DDR_MIN 1 ///< Min of Max Read CRC Error Replay
#define IDSOPT_CMN_MEM_MAX_READ_CRC_ERROR_REPLAY_DDR_MAX 0x3F ///< Max of Max Read CRC Error Replay

///Memory Error Injection
///0=Enable. 1=Disable. Specifies UMC error injection configuration\nwrites are disabled.\nTrue: UMC::CH::MiscCfg[DisErrInj]=1
typedef enum {
  IDSOPT_CMN_MEM_DIS_MEM_ERR_INJ_FALSE = 0,///<False
  IDSOPT_CMN_MEM_DIS_MEM_ERR_INJ_TRUE = 1,///<True
  IDSOPT_CMN_MEM_DIS_MEM_ERR_INJ_AUTO = 0xff,///<Auto
} IDSOPT_CMN_MEM_DIS_MEM_ERR_INJ;

///EcsStatus Interrupt
///True=Enable. False=Disable.\nEnable interrupts from the EcsStatus Array, in lieu of ECS logging via MCA. Valid only when PcdAmdCcxCfgPFEHEnable is set to TRUE.
typedef enum {
  IDSOPT_CMN_MEM_ECS_STATUS_INTERRUPT_DDR_FALSE = 0,///<False
  IDSOPT_CMN_MEM_ECS_STATUS_INTERRUPT_DDR_TRUE = 1,///<True
} IDSOPT_CMN_MEM_ECS_STATUS_INTERRUPT_DDR;

///DRAM Corrected Error Counter Enable
///Configure DRAM Corrected Error Counter function.\nOnly meaningful when PcdAmdCcxCfgPFEHEnable\nis TRUE.
typedef enum {
  IDSOPT_CMN_MEM_CORRECTED_ERROR_COUNTER_ENABLE_DISABLE = 0,///<Disable
  IDSOPT_CMN_MEM_CORRECTED_ERROR_COUNTER_ENABLE_NOLEAKMODE = 1,///<NoLeakMode
  IDSOPT_CMN_MEM_CORRECTED_ERROR_COUNTER_ENABLE_LEAKMODE = 2,///<LeakMode
} IDSOPT_CMN_MEM_CORRECTED_ERROR_COUNTER_ENABLE;

///DRAM Corrected Error Counter Interrupt Enable
///Enable SMI when DRAM Corrected Error Counter count exceeds the threshold value.
typedef enum {
  IDSOPT_CMN_MEM_CORRECTED_ERROR_COUNTER_INTERRUPT_ENABLE_FALSE = 0,///<FALSE
  IDSOPT_CMN_MEM_CORRECTED_ERROR_COUNTER_INTERRUPT_ENABLE_TRUE = 1,///<TRUE
} IDSOPT_CMN_MEM_CORRECTED_ERROR_COUNTER_INTERRUPT_ENABLE;

///DRAM Corrected Error Counter Leak Rate
///Program Rate value for DRAM Corrected Error Counter function. Only meaningful when PcdAmdDdrEccErrorCounterEnable is set to\nLeakMode (Value :0x00-0x1F).
#define IDSOPT_CMN_MEM_CORRECTED_ERROR_COUNTER_LEAK_RATE_MIN 0x00 ///< Min of DRAM Corrected Error Counter Leak Rate
#define IDSOPT_CMN_MEM_CORRECTED_ERROR_COUNTER_LEAK_RATE_MAX 0x1F ///< Max of DRAM Corrected Error Counter Leak Rate

///DRAM Corrected Error Counter Start Count
///Program starting count value for DRAM Corrected Error Counter function. Only meaningful when PcdAmdDdrEccErrorCounterEnable is not Disable (0x00 - 0xFFFF).
#define IDSOPT_CMN_MEM_CORRECTED_ERROR_COUNTER_START_COUNT_MIN 0x00 ///< Min of DRAM Corrected Error Counter Start Count
#define IDSOPT_CMN_MEM_CORRECTED_ERROR_COUNTER_START_COUNT_MAX 0xFFFF ///< Max of DRAM Corrected Error Counter Start Count

///DRAM ECC Symbol Size
///DRAM ECC Symbol Size (x4/x16) - UMC_CH::EccCtrl[EccSymbolSize16, EccSymbolSize]
typedef enum {
  IDSOPT_CMN_MEM_DRAM_ECC_SYMBOL_SIZE_DDR_X4 = 0,///<x4
  IDSOPT_CMN_MEM_DRAM_ECC_SYMBOL_SIZE_DDR_X16 = 2,///<x16
  IDSOPT_CMN_MEM_DRAM_ECC_SYMBOL_SIZE_DDR_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_MEM_DRAM_ECC_SYMBOL_SIZE_DDR;

///DRAM ECC Enable
///Use this option to enable / disable DRAM ECC. Auto will set ECC to enable.
typedef enum {
  IDSOPT_CMN_MEM_DRAM_ECC_EN_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_DRAM_ECC_EN_DDR_ENABLED = 1,///<Enabled
  IDSOPT_CMN_MEM_DRAM_ECC_EN_DDR_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_MEM_DRAM_ECC_EN_DDR;

///DRAM UECC Retry
///DRAM UECC Retry. Program to UMC::RecCtrl.RecEn [2]
typedef enum {
  IDSOPT_CMN_MEM_DRAM_UECC_RETRY_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_DRAM_UECC_RETRY_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_DRAM_UECC_RETRY_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_DRAM_UECC_RETRY_DDR;

///Max DRAM UECC Error Replay
///Program to UMC::RecCtrl2 [MaxEccRply], valid value:1 - 3F hex, default 8
#define IDSOPT_CMN_MEM_MAX_DRAM_UECC_ERROR_REPLAY_DDR_MIN 1 ///< Min of Max DRAM UECC Error Replay
#define IDSOPT_CMN_MEM_MAX_DRAM_UECC_ERROR_REPLAY_DDR_MAX 0x3F ///< Max of Max DRAM UECC Error Replay

///Memory Clear
///Clear/Zero out Dram range [DramScrubBaseAddr: DramScrubLimitAddr].When this option is disabled, Memory is not cleared after training. ECC Dimms have memory clear enabled always. Non-ECC Dimms can choose to disable/enable using this option. \nDefault = Memclear enabled
typedef enum {
  IDSOPT_CMN_MEM_DRAM_MEM_CLR_DDR_ENABLED = 1,///<Enabled
  IDSOPT_CMN_MEM_DRAM_MEM_CLR_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_DRAM_MEM_CLR_DDR_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_MEM_DRAM_MEM_CLR_DDR;

///Address XOR after ECC
///In order to provide data integrity when data is returned from the wrong address, UMC will hash the data after ECC with the normalized address
typedef enum {
  IDSOPT_CMN_MEM_ADDR_XOR_AFTER_ECC_ENABLED = 1,///<Enabled
  IDSOPT_CMN_MEM_ADDR_XOR_AFTER_ECC_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_ADDR_XOR_AFTER_ECC_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_MEM_ADDR_XOR_AFTER_ECC;

///CipherText Hiding Enable
///Enable or disable ciphertext hiding
typedef enum {
  IDSOPT_DBG_MEM_CIPHER_TEXT_HIDING_DISABLE = 0,///<Disable
  IDSOPT_DBG_MEM_CIPHER_TEXT_HIDING_ENABLE = 1,///<Enable
} IDSOPT_DBG_MEM_CIPHER_TEXT_HIDING;

///DRAM ECS Mode
///0 = AutoECS Mode, 1 = ManualECS mode\nDisable ECS mode disables only the controller and does not disable it on the DRAM
typedef enum {
  IDSOPT_CMN_MEM_DRAM_ECS_MODE_DDR_AUTOECS = 0,///<AutoECS
  IDSOPT_CMN_MEM_DRAM_ECS_MODE_DDR_MANUALECS = 1,///<ManualECS
  IDSOPT_CMN_MEM_DRAM_ECS_MODE_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_DRAM_ECS_MODE_DDR_DISABLEECS = 2,///<DisableECS
} IDSOPT_CMN_MEM_DRAM_ECS_MODE_DDR;

///DRAM Redirect Scrubber Enable
///Enable/Disable Dram Redirect Scrubber and poison scrubber
typedef enum {
  IDSOPT_CMN_MEM_DRAM_REDIRECT_SCRUB_EN_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_DRAM_REDIRECT_SCRUB_EN_DDR_ENABLED = 1,///<Enabled
  IDSOPT_CMN_MEM_DRAM_REDIRECT_SCRUB_EN_DDR_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_MEM_DRAM_REDIRECT_SCRUB_EN_DDR;

///DRAM Scrub Redirection Limit
///Dram ECC Scrub Redirection Limit: 0=8 scrubs, 1=4 scrubs, 2=2 scrubs, 3=1 scrub
typedef enum {
  IDSOPT_CMN_MEM_DRAM_REDIRECT_SCRUB_LIMIT_DDR_8SCRUBS = 0,///<8 Scrubs
  IDSOPT_CMN_MEM_DRAM_REDIRECT_SCRUB_LIMIT_DDR_4SCRUBS = 1,///<4 Scrubs
  IDSOPT_CMN_MEM_DRAM_REDIRECT_SCRUB_LIMIT_DDR_2SCRUBS = 2,///<2 Scrubs
  IDSOPT_CMN_MEM_DRAM_REDIRECT_SCRUB_LIMIT_DDR_1SCRUB = 3,///<1 Scrub
  IDSOPT_CMN_MEM_DRAM_REDIRECT_SCRUB_LIMIT_DDR_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_MEM_DRAM_REDIRECT_SCRUB_LIMIT_DDR;

///DRAM Scrub Time
///Provide a value that is the number of hours to scrub memory.
typedef enum {
  IDSOPT_CMN_MEM_DRAM_SCRUB_TIME_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_DRAM_SCRUB_TIME_1HOUR = 1,///<1 hour
  IDSOPT_CMN_MEM_DRAM_SCRUB_TIME_4HOURS = 4,///<4 hours
  IDSOPT_CMN_MEM_DRAM_SCRUB_TIME_6HOURS = 6,///<6 hours
  IDSOPT_CMN_MEM_DRAM_SCRUB_TIME_8HOURS = 8,///<8 hours
  IDSOPT_CMN_MEM_DRAM_SCRUB_TIME_12HOURS = 12,///<12 hours
  IDSOPT_CMN_MEM_DRAM_SCRUB_TIME_16HOURS = 16,///<16 hours
  IDSOPT_CMN_MEM_DRAM_SCRUB_TIME_24HOURS = 24,///<24 hours
  IDSOPT_CMN_MEM_DRAM_SCRUB_TIME_48HOURS = 48,///<48 hours
} IDSOPT_CMN_MEM_DRAM_SCRUB_TIME;

///tECSint Ctrl
///Full ECS interval manual input
typedef enum {
  IDSOPT_CMN_MEMT_EC_SINT_CTRL_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEMT_EC_SINT_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEMT_EC_SINT_CTRL_DDR;

///tECSint
///Full ECS interval in minute, range 25 - 1440 minutes
#define IDSOPT_CMN_MEMT_EC_SINT_DDR_MIN 25 ///< Min of tECSint
#define IDSOPT_CMN_MEMT_EC_SINT_DDR_MAX 1440 ///< Max of tECSint

///DRAM Error Threshold Count
///List of Values:\n0 = ETC_4, \n1 = ETC_16, \n2 = ETC_64, \n3 = ETC_256 (default - Auto),\n4 = ETC_1024,\n5 = ETC_4096
typedef enum {
  IDSOPT_CMN_MEM_DRAM_ETC_DDR_ETC_4 = 0,///<ETC_4
  IDSOPT_CMN_MEM_DRAM_ETC_DDR_ETC_16 = 1,///<ETC_16
  IDSOPT_CMN_MEM_DRAM_ETC_DDR_ETC_64 = 2,///<ETC_64
  IDSOPT_CMN_MEM_DRAM_ETC_DDR_ETC_256 = 3,///<ETC_256
  IDSOPT_CMN_MEM_DRAM_ETC_DDR_ETC_1024 = 4,///<ETC_1024
  IDSOPT_CMN_MEM_DRAM_ETC_DDR_ETC_4096 = 5,///<ETC_4096
  IDSOPT_CMN_MEM_DRAM_ETC_DDR_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_MEM_DRAM_ETC_DDR;

///DRAM ECS Count Mode
///0: RowCount Mode\n1: CodeWord Mode\n0xFF: Auto - ABL decides default as CodeWord Mode
typedef enum {
  IDSOPT_CMN_MEM_DRAM_ECS_COUNT_MODE_DDR_ROWCOUNTMODE = 0,///<Row Count Mode
  IDSOPT_CMN_MEM_DRAM_ECS_COUNT_MODE_DDR_CODEWORDCOUNTMODE = 1,///<Code Word Count Mode
  IDSOPT_CMN_MEM_DRAM_ECS_COUNT_MODE_DDR_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_MEM_DRAM_ECS_COUNT_MODE_DDR;

///DRAM AutoEcs during Self Refresh
///0: AutoEcs Disabled\n1: AutoEcs Enabled\n0xFF: Auto - ABL choose AutoEcs Disabled
typedef enum {
  IDSOPT_CMN_MEM_DRAM_AUTO_ECS_SELF_REFRESH_DDR_AUTOECSDISABLED = 0,///<AutoEcs Disabled
  IDSOPT_CMN_MEM_DRAM_AUTO_ECS_SELF_REFRESH_DDR_AUTOECSENABLED = 1,///<AutoEcs Enabled
  IDSOPT_CMN_MEM_DRAM_AUTO_ECS_SELF_REFRESH_DDR_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_MEM_DRAM_AUTO_ECS_SELF_REFRESH_DDR;

///DRAM ECS WriteBack Suppression
///To enable/Disable ECS Error Correction Writeback suppression\n0: ECS Writeback Suppression Disabled\n1: ECS Writeback Suppression Enabled\n0xFF: Auto - ABL chooses Writeback Suppression to be Enabled by default
typedef enum {
  IDSOPT_CMN_MEM_DRAM_ECS_WRITEBACK_SUPPRESSION_DDR_DISABLE = 0,///<Disable
  IDSOPT_CMN_MEM_DRAM_ECS_WRITEBACK_SUPPRESSION_DDR_ENABLE = 1,///<Enable
  IDSOPT_CMN_MEM_DRAM_ECS_WRITEBACK_SUPPRESSION_DDR_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_MEM_DRAM_ECS_WRITEBACK_SUPPRESSION_DDR;

///DRAM X4 WriteBack Suppression
///To enable/Disable X4 device Error Correction Writeback suppression\n0: X4 Writeback Suppression Disabled\n1: X4 Writeback Suppression Enabled\n0xFF: Auto
typedef enum {
  IDSOPT_CMN_MEM_DRAM_X4_WRITEBACK_SUPPRESSION_DDR_DISABLE = 0,///<Disable
  IDSOPT_CMN_MEM_DRAM_X4_WRITEBACK_SUPPRESSION_DDR_ENABLE = 1,///<Enable
  IDSOPT_CMN_MEM_DRAM_X4_WRITEBACK_SUPPRESSION_DDR_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_MEM_DRAM_X4_WRITEBACK_SUPPRESSION_DDR;

///Processor ODT Pull Up Impedance
///Select the ODT impedance for all DBYTE IOs
typedef enum {
  IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_HIGHIMPEDANCE = 0,///<High Impedance
  IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_480OHM = 1,///<480 ohm
  IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_240OHM = 2,///<240 ohm
  IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_160OHM = 3,///<160 ohm
  IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_120OHM = 4,///<120 ohm
  IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_96OHM = 5,///<96 ohm
  IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_80OHM = 6,///<80 ohm
  IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_686OHM = 7,///<68.6 ohm
  IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_60OHM = 0xC,///<60 ohm
  IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_533OHM = 0xD,///<53.3 ohm
  IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_48OHM = 0xE,///<48 ohm
  IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_436OHM = 0xF,///<43.6 ohm
  IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_40OHM = 0x1C,///<40 ohm
  IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_369OHM = 0x1D,///<36.9 ohm
  IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_343OHM = 0x1E,///<34.3 ohm
  IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_32OHM = 0x1F,///<32 ohm
  IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_30OHM = 0x3C,///<30 ohm
  IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_282OHM = 0x3D,///<28.2 ohm
  IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_267OHM = 0x3E,///<26.7 ohm
  IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_253OHM = 0x3F,///<25.3 ohm
} IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR;

///Processor ODT Pull Down Impedance
///Select the ODT pull down impedance for all DBYTE IOs
typedef enum {
  IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_HIGHIMPEDANCE = 0,///<High Impedance
  IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_480OHM = 1,///<480 ohm
  IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_240OHM = 2,///<240 ohm
  IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_160OHM = 3,///<160 ohm
  IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_120OHM = 4,///<120 ohm
  IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_96OHM = 5,///<96 ohm
  IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_80OHM = 6,///<80 ohm
  IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_686OHM = 7,///<68.6 ohm
  IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_60OHM = 0xC,///<60 ohm
  IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_533OHM = 0xD,///<53.3 ohm
  IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_48OHM = 0xE,///<48 ohm
  IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_436OHM = 0xF,///<43.6 ohm
  IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_40OHM = 0x1C,///<40 ohm
  IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_369OHM = 0x1D,///<36.9 ohm
  IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_343OHM = 0x1E,///<34.3 ohm
  IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_32OHM = 0x1F,///<32 ohm
  IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_30OHM = 0x3C,///<30 ohm
  IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_282OHM = 0x3D,///<28.2 ohm
  IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_267OHM = 0x3E,///<26.7 ohm
  IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_253OHM = 0x3F,///<25.3 ohm
} IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR;

///Dram DQ drive strengths
///Selects the Dram Pull-up and Pull-Down Output Driver Impedance for all DQ IOs
typedef enum {
  IDSOPT_CMN_MEM_DRAM_DRV_STREN_DQ_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_DRAM_DRV_STREN_DQ_DDR_48OHM = 2,///<48 ohm
  IDSOPT_CMN_MEM_DRAM_DRV_STREN_DQ_DDR_40OHM = 1,///<40 ohm
  IDSOPT_CMN_MEM_DRAM_DRV_STREN_DQ_DDR_34OHM = 0,///<34 ohm
} IDSOPT_CMN_MEM_DRAM_DRV_STREN_DQ_DDR;

///RTT_NOM_WR P-State 0
///Select the DRAMs On-die Termination impedance for RTT_NOM_WR  P-State 0
typedef enum {
  IDSOPT_CMN_MEM_RTT_NOM_WR_P0_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_RTT_NOM_WR_P0_DDR_RTT_OFF = 0x0,///<RTT_OFF
  IDSOPT_CMN_MEM_RTT_NOM_WR_P0_DDR_RZQ240 = 0x1,///<RZQ (240)
  IDSOPT_CMN_MEM_RTT_NOM_WR_P0_DDR_RZQ2120 = 0x2,///<RZQ/2 (120)
  IDSOPT_CMN_MEM_RTT_NOM_WR_P0_DDR_RZQ380 = 0x3,///<RZQ/3 (80)
  IDSOPT_CMN_MEM_RTT_NOM_WR_P0_DDR_RZQ460 = 0x4,///<RZQ/4 (60)
  IDSOPT_CMN_MEM_RTT_NOM_WR_P0_DDR_RZQ548 = 0x5,///<RZQ/5 (48)
  IDSOPT_CMN_MEM_RTT_NOM_WR_P0_DDR_RZQ640 = 0x6,///<RZQ/6 (40)
  IDSOPT_CMN_MEM_RTT_NOM_WR_P0_DDR_RZQ734 = 0x7,///<RZQ/7 (34)
} IDSOPT_CMN_MEM_RTT_NOM_WR_P0_DDR;

///RTT_NOM_RD P-State 0
///Select the DRAMs On-die Termination impedance for RTT_NOM_RD P-State 0
typedef enum {
  IDSOPT_CMN_MEM_RTT_NOM_RD_P0_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_RTT_NOM_RD_P0_DDR_RTT_OFF = 0x0,///<RTT_OFF
  IDSOPT_CMN_MEM_RTT_NOM_RD_P0_DDR_RZQ240 = 0x1,///<RZQ (240)
  IDSOPT_CMN_MEM_RTT_NOM_RD_P0_DDR_RZQ2120 = 0x2,///<RZQ/2 (120)
  IDSOPT_CMN_MEM_RTT_NOM_RD_P0_DDR_RZQ380 = 0x3,///<RZQ/3 (80)
  IDSOPT_CMN_MEM_RTT_NOM_RD_P0_DDR_RZQ460 = 0x4,///<RZQ/4 (60)
  IDSOPT_CMN_MEM_RTT_NOM_RD_P0_DDR_RZQ548 = 0x5,///<RZQ/5 (48)
  IDSOPT_CMN_MEM_RTT_NOM_RD_P0_DDR_RZQ640 = 0x6,///<RZQ/6 (40)
  IDSOPT_CMN_MEM_RTT_NOM_RD_P0_DDR_RZQ734 = 0x7,///<RZQ/7 (34)
} IDSOPT_CMN_MEM_RTT_NOM_RD_P0_DDR;

///RTT_WR P-State 0
///Select the DRAMs On-die Termination impedance for RTT_WR P-State 0
typedef enum {
  IDSOPT_CMN_MEM_RTT_WR_P0_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_RTT_WR_P0_DDR_RTT_OFF = 0x0,///<RTT_OFF
  IDSOPT_CMN_MEM_RTT_WR_P0_DDR_RZQ240 = 0x1,///<RZQ (240)
  IDSOPT_CMN_MEM_RTT_WR_P0_DDR_RZQ2120 = 0x2,///<RZQ/2 (120)
  IDSOPT_CMN_MEM_RTT_WR_P0_DDR_RZQ380 = 0x3,///<RZQ/3 (80)
  IDSOPT_CMN_MEM_RTT_WR_P0_DDR_RZQ460 = 0x4,///<RZQ/4 (60)
  IDSOPT_CMN_MEM_RTT_WR_P0_DDR_RZQ548 = 0x5,///<RZQ/5 (48)
  IDSOPT_CMN_MEM_RTT_WR_P0_DDR_RZQ640 = 0x6,///<RZQ/6 (40)
  IDSOPT_CMN_MEM_RTT_WR_P0_DDR_RZQ734 = 0x7,///<RZQ/7 (34)
} IDSOPT_CMN_MEM_RTT_WR_P0_DDR;

///RTT_PARK P-State 0
///Select the DRAMs On-die Termination impedance for RTT_PARK P-State 0
typedef enum {
  IDSOPT_CMN_MEM_RTT_PARK_P0_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_RTT_PARK_P0_DDR_RTT_OFF = 0x0,///<RTT_OFF
  IDSOPT_CMN_MEM_RTT_PARK_P0_DDR_RZQ240 = 0x1,///<RZQ (240)
  IDSOPT_CMN_MEM_RTT_PARK_P0_DDR_RZQ2120 = 0x2,///<RZQ/2 (120)
  IDSOPT_CMN_MEM_RTT_PARK_P0_DDR_RZQ380 = 0x3,///<RZQ/3 (80)
  IDSOPT_CMN_MEM_RTT_PARK_P0_DDR_RZQ460 = 0x4,///<RZQ/4 (60)
  IDSOPT_CMN_MEM_RTT_PARK_P0_DDR_RZQ548 = 0x5,///<RZQ/5 (48)
  IDSOPT_CMN_MEM_RTT_PARK_P0_DDR_RZQ640 = 0x6,///<RZQ/6 (40)
  IDSOPT_CMN_MEM_RTT_PARK_P0_DDR_RZQ734 = 0x7,///<RZQ/7 (34)
} IDSOPT_CMN_MEM_RTT_PARK_P0_DDR;

///DQS_RTT_PARK P-State 0
///Select the DRAMs On-die Termination impedance for DQS_RTT_PARK P-State 0
typedef enum {
  IDSOPT_CMN_MEM_RTT_PARK_DQS_P0_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_RTT_PARK_DQS_P0_DDR_RTT_OFF = 0x0,///<RTT_OFF
  IDSOPT_CMN_MEM_RTT_PARK_DQS_P0_DDR_RZQ240 = 0x1,///<RZQ (240)
  IDSOPT_CMN_MEM_RTT_PARK_DQS_P0_DDR_RZQ2120 = 0x2,///<RZQ/2 (120)
  IDSOPT_CMN_MEM_RTT_PARK_DQS_P0_DDR_RZQ380 = 0x3,///<RZQ/3 (80)
  IDSOPT_CMN_MEM_RTT_PARK_DQS_P0_DDR_RZQ460 = 0x4,///<RZQ/4 (60)
  IDSOPT_CMN_MEM_RTT_PARK_DQS_P0_DDR_RZQ548 = 0x5,///<RZQ/5 (48)
  IDSOPT_CMN_MEM_RTT_PARK_DQS_P0_DDR_RZQ640 = 0x6,///<RZQ/6 (40)
  IDSOPT_CMN_MEM_RTT_PARK_DQS_P0_DDR_RZQ734 = 0x7,///<RZQ/7 (34)
} IDSOPT_CMN_MEM_RTT_PARK_DQS_P0_DDR;

///RTT_NOM_WR P-State 1
///Select the DRAMs On-die Termination impedance for RTT_NOM_WR  P-State 1
typedef enum {
  IDSOPT_CMN_MEM_RTT_NOM_WR_P1_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_RTT_NOM_WR_P1_DDR_RTT_OFF = 0x0,///<RTT_OFF
  IDSOPT_CMN_MEM_RTT_NOM_WR_P1_DDR_RZQ240 = 0x1,///<RZQ (240)
  IDSOPT_CMN_MEM_RTT_NOM_WR_P1_DDR_RZQ2120 = 0x2,///<RZQ/2 (120)
  IDSOPT_CMN_MEM_RTT_NOM_WR_P1_DDR_RZQ380 = 0x3,///<RZQ/3 (80)
  IDSOPT_CMN_MEM_RTT_NOM_WR_P1_DDR_RZQ460 = 0x4,///<RZQ/4 (60)
  IDSOPT_CMN_MEM_RTT_NOM_WR_P1_DDR_RZQ548 = 0x5,///<RZQ/5 (48)
  IDSOPT_CMN_MEM_RTT_NOM_WR_P1_DDR_RZQ640 = 0x6,///<RZQ/6 (40)
  IDSOPT_CMN_MEM_RTT_NOM_WR_P1_DDR_RZQ734 = 0x7,///<RZQ/7 (34)
} IDSOPT_CMN_MEM_RTT_NOM_WR_P1_DDR;

///RTT_NOM_RD P-State 1
///Select the DRAMs On-die Termination impedance for RTT_NOM_RD P-State 1
typedef enum {
  IDSOPT_CMN_MEM_RTT_NOM_RD_P1_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_RTT_NOM_RD_P1_DDR_RTT_OFF = 0x0,///<RTT_OFF
  IDSOPT_CMN_MEM_RTT_NOM_RD_P1_DDR_RZQ240 = 0x1,///<RZQ (240)
  IDSOPT_CMN_MEM_RTT_NOM_RD_P1_DDR_RZQ2120 = 0x2,///<RZQ/2 (120)
  IDSOPT_CMN_MEM_RTT_NOM_RD_P1_DDR_RZQ380 = 0x3,///<RZQ/3 (80)
  IDSOPT_CMN_MEM_RTT_NOM_RD_P1_DDR_RZQ460 = 0x4,///<RZQ/4 (60)
  IDSOPT_CMN_MEM_RTT_NOM_RD_P1_DDR_RZQ548 = 0x5,///<RZQ/5 (48)
  IDSOPT_CMN_MEM_RTT_NOM_RD_P1_DDR_RZQ640 = 0x6,///<RZQ/6 (40)
  IDSOPT_CMN_MEM_RTT_NOM_RD_P1_DDR_RZQ734 = 0x7,///<RZQ/7 (34)
} IDSOPT_CMN_MEM_RTT_NOM_RD_P1_DDR;

///RTT_WR P-State 1
///Select the DRAMs On-die Termination impedance for RTT_WR P-State 1
typedef enum {
  IDSOPT_CMN_MEM_RTT_WR_P1_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_RTT_WR_P1_DDR_RTT_OFF = 0x0,///<RTT_OFF
  IDSOPT_CMN_MEM_RTT_WR_P1_DDR_RZQ240 = 0x1,///<RZQ (240)
  IDSOPT_CMN_MEM_RTT_WR_P1_DDR_RZQ2120 = 0x2,///<RZQ/2 (120)
  IDSOPT_CMN_MEM_RTT_WR_P1_DDR_RZQ380 = 0x3,///<RZQ/3 (80)
  IDSOPT_CMN_MEM_RTT_WR_P1_DDR_RZQ460 = 0x4,///<RZQ/4 (60)
  IDSOPT_CMN_MEM_RTT_WR_P1_DDR_RZQ548 = 0x5,///<RZQ/5 (48)
  IDSOPT_CMN_MEM_RTT_WR_P1_DDR_RZQ640 = 0x6,///<RZQ/6 (40)
  IDSOPT_CMN_MEM_RTT_WR_P1_DDR_RZQ734 = 0x7,///<RZQ/7 (34)
} IDSOPT_CMN_MEM_RTT_WR_P1_DDR;

///RTT_PARK P-State 1
///Select the DRAMs On-die Termination impedance for RTT_PARK P-State 1
typedef enum {
  IDSOPT_CMN_MEM_RTT_PARK_P1_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_RTT_PARK_P1_DDR_RTT_OFF = 0x0,///<RTT_OFF
  IDSOPT_CMN_MEM_RTT_PARK_P1_DDR_RZQ240 = 0x1,///<RZQ (240)
  IDSOPT_CMN_MEM_RTT_PARK_P1_DDR_RZQ2120 = 0x2,///<RZQ/2 (120)
  IDSOPT_CMN_MEM_RTT_PARK_P1_DDR_RZQ380 = 0x3,///<RZQ/3 (80)
  IDSOPT_CMN_MEM_RTT_PARK_P1_DDR_RZQ460 = 0x4,///<RZQ/4 (60)
  IDSOPT_CMN_MEM_RTT_PARK_P1_DDR_RZQ548 = 0x5,///<RZQ/5 (48)
  IDSOPT_CMN_MEM_RTT_PARK_P1_DDR_RZQ640 = 0x6,///<RZQ/6 (40)
  IDSOPT_CMN_MEM_RTT_PARK_P1_DDR_RZQ734 = 0x7,///<RZQ/7 (34)
} IDSOPT_CMN_MEM_RTT_PARK_P1_DDR;

///DQS_RTT_PARK P-State 1
///Select the DRAMs On-die Termination impedance for DQS_RTT_PARK P-State 1
typedef enum {
  IDSOPT_CMN_MEM_RTT_PARK_DQS_P1_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_RTT_PARK_DQS_P1_DDR_RTT_OFF = 0x0,///<RTT_OFF
  IDSOPT_CMN_MEM_RTT_PARK_DQS_P1_DDR_RZQ240 = 0x1,///<RZQ (240)
  IDSOPT_CMN_MEM_RTT_PARK_DQS_P1_DDR_RZQ2120 = 0x2,///<RZQ/2 (120)
  IDSOPT_CMN_MEM_RTT_PARK_DQS_P1_DDR_RZQ380 = 0x3,///<RZQ/3 (80)
  IDSOPT_CMN_MEM_RTT_PARK_DQS_P1_DDR_RZQ460 = 0x4,///<RZQ/4 (60)
  IDSOPT_CMN_MEM_RTT_PARK_DQS_P1_DDR_RZQ548 = 0x5,///<RZQ/5 (48)
  IDSOPT_CMN_MEM_RTT_PARK_DQS_P1_DDR_RZQ640 = 0x6,///<RZQ/6 (40)
  IDSOPT_CMN_MEM_RTT_PARK_DQS_P1_DDR_RZQ734 = 0x7,///<RZQ/7 (34)
} IDSOPT_CMN_MEM_RTT_PARK_DQS_P1_DDR;

///DRAM Timing Configuration Legal Disclaimer
///DRAM Timing Configuration Legal Disclaimer 1
///Active Memory Timing Settings
///Active Memory Timing Settings
typedef enum {
  IDSOPT_CMN_MEM_TIMING_SETTING_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_TIMING_SETTING_DDR_ENABLED = 1,///<Enabled
} IDSOPT_CMN_MEM_TIMING_SETTING_DDR;

///Memory Target Speed
///Specifies the memory target speed in MT/s.
typedef enum {
  IDSOPT_CMN_MEM_TARGET_SPEED_DDR_AUTO = 0xFFFF,///<Auto
  IDSOPT_CMN_MEM_TARGET_SPEED_DDR_DDR3600 = 3600,///<DDR3600
  IDSOPT_CMN_MEM_TARGET_SPEED_DDR_DDR4000 = 4000,///<DDR4000
  IDSOPT_CMN_MEM_TARGET_SPEED_DDR_DDR4400 = 4400,///<DDR4400
  IDSOPT_CMN_MEM_TARGET_SPEED_DDR_DDR4800 = 4800,///<DDR4800
  IDSOPT_CMN_MEM_TARGET_SPEED_DDR_DDR5200 = 5200,///<DDR5200
  IDSOPT_CMN_MEM_TARGET_SPEED_DDR_DDR5600 = 5600,///<DDR5600
  IDSOPT_CMN_MEM_TARGET_SPEED_DDR_DDR6000 = 6000,///<DDR6000
  IDSOPT_CMN_MEM_TARGET_SPEED_DDR_DDR6400 = 6400,///<DDR6400
} IDSOPT_CMN_MEM_TARGET_SPEED_DDR;

///Tcl Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TCL_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TCL_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TCL_CTRL_DDR;

///Tcl
///Specifies the CAS Latency. Valid values: 0x9 ~ 0x32. The value is in hex.
#define IDSOPT_CMN_MEM_TIMING_TCL_DDR_MIN 0x9 ///< Min of Tcl
#define IDSOPT_CMN_MEM_TIMING_TCL_DDR_MAX 0x32 ///< Max of Tcl

///Trcd Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TRCD_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TRCD_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TRCD_CTRL_DDR;

///Trcd
///Specifies the RAS# Active to CAS# Read Delay Time. Valid values: 0x8 ~ 0x3F. The value is in hex.
#define IDSOPT_CMN_MEM_TIMING_TRCD_DDR_MIN 0x8 ///< Min of Trcd
#define IDSOPT_CMN_MEM_TIMING_TRCD_DDR_MAX 0x3F ///< Max of Trcd

///Trp Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TRP_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TRP_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TRP_CTRL_DDR;

///Trp
///Specifies Row Precharge Delay Time. Valid values: 0x8 ~ 0x3F. The value is in hex.
#define IDSOPT_CMN_MEM_TIMING_TRP_DDR_MIN 0x8 ///< Min of Trp
#define IDSOPT_CMN_MEM_TIMING_TRP_DDR_MAX 0x3F ///< Max of Trp

///Tras Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TRAS_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TRAS_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TRAS_CTRL_DDR;

///Tras
///Specifies the minimum time in memory clock cycles from an activate\ncommand to a precharge command, both to the same bank.\nValid values: 0x20 ~ 0x75.
#define IDSOPT_CMN_MEM_TIMING_TRAS_DDR_MIN 0x20 ///< Min of Tras
#define IDSOPT_CMN_MEM_TIMING_TRAS_DDR_MAX 0x75 ///< Max of Tras

///Trc Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TRC_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TRC_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TRC_CTRL_DDR;

///Trc
///Specifies Active to Active/Refresh Delay Time. Valid values 87h-1Dh.
#define IDSOPT_CMN_MEM_TIMING_TRC_DDR_MIN 0x1D ///< Min of Trc
#define IDSOPT_CMN_MEM_TIMING_TRC_DDR_MAX 0x87 ///< Max of Trc

///Twr Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TWR_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TWR_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TWR_CTRL_DDR;

///Twr
///Specifies the Minimum Write Recovery Time. Valid values: 0xA ~ 0x64. The value is in hex
#define IDSOPT_CMN_MEM_TIMING_TWR_DDR_MIN 0xA ///< Min of Twr
#define IDSOPT_CMN_MEM_TIMING_TWR_DDR_MAX 0x64 ///< Max of Twr

///Trfc1 Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TRFC1_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TRFC1_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TRFC1_CTRL_DDR;

///Trfc1
///Specifies the Refresh Recovery Delay Time (tRFC1). Valid values 3DEh-3Ch
#define IDSOPT_CMN_MEM_TIMING_TRFC1_DDR_MIN 0x3C ///< Min of Trfc1
#define IDSOPT_CMN_MEM_TIMING_TRFC1_DDR_MAX 0x3DE ///< Max of Trfc1

///Trfc2 Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TRFC2_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TRFC2_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TRFC2_CTRL_DDR;

///Trfc2
///Specifies the Refresh Recovery Delay Time (tRFC2).  Valid values 3DEh-3Ch
#define IDSOPT_CMN_MEM_TIMING_TRFC2_DDR_MIN 0x3C ///< Min of Trfc2
#define IDSOPT_CMN_MEM_TIMING_TRFC2_DDR_MAX 0x3DE ///< Max of Trfc2

///TrfcSb Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TRFC_SB_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TRFC_SB_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TRFC_SB_CTRL_DDR;

///TrfcSb
///Specifies the Refresh Recovery Delay Time (tRFCSb). Valid values 0x32 ~ 0x7FF. The value is in hex.
#define IDSOPT_CMN_MEM_TIMING_TRFC_SB_DDR_MIN 0x32 ///< Min of TrfcSb
#define IDSOPT_CMN_MEM_TIMING_TRFC_SB_DDR_MAX 0x7FF ///< Max of TrfcSb

///Tcwl Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TCWL_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TCWL_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TCWL_CTRL_DDR;

///Tcwl
///Specifies the CAS Write Latency. Valid Values: 0x9 ~ 0x16
#define IDSOPT_CMN_MEM_TIMING_TCWL_DDR_MIN 0x9 ///< Min of Tcwl
#define IDSOPT_CMN_MEM_TIMING_TCWL_DDR_MAX 0x16 ///< Max of Tcwl

///Trtp Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TRTP_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TRTP_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TRTP_CTRL_DDR;

///Trtp
///Specifies the Read CAS# to Precharge Delay Time. Valid values: 0x5 ~ 0x0E.
#define IDSOPT_CMN_MEM_TIMING_TRTP_DDR_MIN 0x05 ///< Min of Trtp
#define IDSOPT_CMN_MEM_TIMING_TRTP_DDR_MAX 0x0E ///< Max of Trtp

///TrrdL Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TRRD_L_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TRRD_L_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TRRD_L_CTRL_DDR;

///TrrdL
///Specifies the Activate to Activate Delay Time, same bank group(tRRD_L). Valid values: 0x4 ~ 0x0C. The value is in hex.
#define IDSOPT_CMN_MEM_TIMING_TRRD_L_DDR_MIN 0x04 ///< Min of TrrdL
#define IDSOPT_CMN_MEM_TIMING_TRRD_L_DDR_MAX 0x0C ///< Max of TrrdL

///TrrdS Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TRRD_S_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TRRD_S_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TRRD_S_CTRL_DDR;

///TrrdS
///Specifies the Activate to Activate Delay Time, different bank group(tRRD_S). Valid values: 0x04 ~ 0x0C. The value is in hex.
#define IDSOPT_CMN_MEM_TIMING_TRRD_S_DDR_MIN 0x04 ///< Min of TrrdS
#define IDSOPT_CMN_MEM_TIMING_TRRD_S_DDR_MAX 0x0C ///< Max of TrrdS

///Tfaw Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TFAW_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TFAW_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TFAW_CTRL_DDR;

///Tfaw
///Specifies the Four Activate Window Time. Valid values 6h ~ 36h.
#define IDSOPT_CMN_MEM_TIMING_TFAW_DDR_MIN 0x6 ///< Min of Tfaw
#define IDSOPT_CMN_MEM_TIMING_TFAW_DDR_MAX 0x36 ///< Max of Tfaw

///TwtrL Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TWTR_L_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TWTR_L_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TWTR_L_CTRL_DDR;

///TwtrL
///Specifies the Minimum Write to Read Time, same bank group. Valid values: 0x2 ~ 0xE
#define IDSOPT_CMN_MEM_TIMING_TWTR_L_DDR_MIN 0x02 ///< Min of TwtrL
#define IDSOPT_CMN_MEM_TIMING_TWTR_L_DDR_MAX 0x0E ///< Max of TwtrL

///TwtrS Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TWTR_S_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TWTR_S_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TWTR_S_CTRL_DDR;

///TwtrS
///Specifies the Minimum Write to Read Time, different bank group. Valid values: 0x02 ~ 0x0E
#define IDSOPT_CMN_MEM_TIMING_TWTR_S_DDR_MIN 0x02 ///< Min of TwtrS
#define IDSOPT_CMN_MEM_TIMING_TWTR_S_DDR_MAX 0x0E ///< Max of TwtrS

///TrdrdScL Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TRDRD_SC_L_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TRDRD_SC_L_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TRDRD_SC_L_CTRL_DDR;

///TrdrdScL
///Specifies the CAS to CAS Delay Time, same bank group. Valid values 0x1 ~ 0xF
#define IDSOPT_CMN_MEM_TIMING_TRDRD_SC_L_DDR_MIN 0x1 ///< Min of TrdrdScL
#define IDSOPT_CMN_MEM_TIMING_TRDRD_SC_L_DDR_MAX 0xF ///< Max of TrdrdScL

///TrdrdSc Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TRDRD_SC_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TRDRD_SC_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TRDRD_SC_CTRL_DDR;

///TrdrdSc
///Specifies the Read to Read turnaround timing in the same chipselect.\nValid values: 0x1 ~ 0xF
#define IDSOPT_CMN_MEM_TIMING_TRDRD_SC_DDR_MIN 0x1 ///< Min of TrdrdSc
#define IDSOPT_CMN_MEM_TIMING_TRDRD_SC_DDR_MAX 0xF ///< Max of TrdrdSc

///TrdrdSd Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TRDRD_SD_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TRDRD_SD_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TRDRD_SD_CTRL_DDR;

///TrdrdSd
///Specifies the Read to Read turnaround timing in the same DIMM.\nValid values: 0x1 ~ 0xF.
#define IDSOPT_CMN_MEM_TIMING_TRDRD_SD_DDR_MIN 0x1 ///< Min of TrdrdSd
#define IDSOPT_CMN_MEM_TIMING_TRDRD_SD_DDR_MAX 0xF ///< Max of TrdrdSd

///TrdrdDd Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TRDRD_DD_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TRDRD_DD_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TRDRD_DD_CTRL_DDR;

///TrdrdDd
///Specifies the Read to Read turnaround timing in a different DIMM.\nValid Values: 0x1 ~ 0xF
#define IDSOPT_CMN_MEM_TIMING_TRDRD_DD_DDR_MIN 0x1 ///< Min of TrdrdDd
#define IDSOPT_CMN_MEM_TIMING_TRDRD_DD_DDR_MAX 0xF ///< Max of TrdrdDd

///TwrwrScL Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TWRWR_SC_L_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TWRWR_SC_L_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TWRWR_SC_L_CTRL_DDR;

///TwrwrScL
///Specifies the CAS to CAS Delay Time, same bank group. Valid values 3Fh-1h
#define IDSOPT_CMN_MEM_TIMING_TWRWR_SC_L_DDR_MIN 0x1 ///< Min of TwrwrScL
#define IDSOPT_CMN_MEM_TIMING_TWRWR_SC_L_DDR_MAX 0x3f ///< Max of TwrwrScL

///TwrwrSc Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TWRWR_SC_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TWRWR_SC_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TWRWR_SC_CTRL_DDR;

///TwrwrSc
///Specifies the Write to Write turnaround timing in the same chipselect. Valid values: 0x1 ~ 0xF
#define IDSOPT_CMN_MEM_TIMING_TWRWR_SC_DDR_MIN 0x1 ///< Min of TwrwrSc
#define IDSOPT_CMN_MEM_TIMING_TWRWR_SC_DDR_MAX 0xF ///< Max of TwrwrSc

///TwrwrSd Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TWRWR_SD_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TWRWR_SD_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TWRWR_SD_CTRL_DDR;

///TwrwrSd
///Specifies the Write to Write turnaround timing in the same DIMM. Valid values: 0x1 ~ 0xF.
#define IDSOPT_CMN_MEM_TIMING_TWRWR_SD_DDR_MIN 0x1 ///< Min of TwrwrSd
#define IDSOPT_CMN_MEM_TIMING_TWRWR_SD_DDR_MAX 0xF ///< Max of TwrwrSd

///TwrwrDd Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TWRWR_DD_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TWRWR_DD_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TWRWR_DD_CTRL_DDR;

///TwrwrDd
///Specifies the Write to Write turnaround timing in a different DIMM. Valid values: 0x1 ~ 0xF
#define IDSOPT_CMN_MEM_TIMING_TWRWR_DD_DDR_MIN 0x1 ///< Min of TwrwrDd
#define IDSOPT_CMN_MEM_TIMING_TWRWR_DD_DDR_MAX 0xF ///< Max of TwrwrDd

///Twrrd Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TWRRD_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TWRRD_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TWRRD_CTRL_DDR;

///Twrrd
///Specifies the Write to Read turnaround timing. Valid values: 0x1 ~ 0xF. The value is in hex.
#define IDSOPT_CMN_MEM_TIMING_TWRRD_DDR_MIN 0x1 ///< Min of Twrrd
#define IDSOPT_CMN_MEM_TIMING_TWRRD_DDR_MAX 0xF ///< Max of Twrrd

///Trdwr Ctrl
///Auto: Follow default setting, Manual: Manually specify
typedef enum {
  IDSOPT_CMN_MEM_TIMING_TRDWR_CTRL_DDR_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_TIMING_TRDWR_CTRL_DDR_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_TIMING_TRDWR_CTRL_DDR;

///Trdwr
///Specifies the Read to Write Turnaround Timing. Valid value: 0x1 ~ 0x1F. The value is in hex.
#define IDSOPT_CMN_MEM_TIMING_TRDWR_DDR_MIN 0x1 ///< Min of Trdwr
#define IDSOPT_CMN_MEM_TIMING_TRDWR_DDR_MAX 0xF ///< Max of Trdwr

///DRAM PDA Enumerate ID Programming Mode
///Specify PDA enumeration mode\nAuto : default\n0 : Continuous DQS toggling PDA enumeration mode (default)\n1 : Legacy PDA enumeration mode
typedef enum {
  IDSOPT_CMN_MEM_DRAM_PDA_ENUM_ID_PROG_MODE_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_DRAM_PDA_ENUM_ID_PROG_MODE_DDR_TOGGLINGPDAENUMERATIONMODE = 0,///<Toggling PDA enumeration mode
  IDSOPT_CMN_MEM_DRAM_PDA_ENUM_ID_PROG_MODE_DDR_LEGACYPDAENUMERATIONMODE = 1,///<Legacy PDA enumeration mode
} IDSOPT_CMN_MEM_DRAM_PDA_ENUM_ID_PROG_MODE_DDR;

///Write Training Burst Length
///Extended sequence for write training, control\nPMU MsgBlock->AdvTrainOpt[6:5]
typedef enum {
  IDSOPT_CMN_MEM_WRITE_TRAINING_BURST_LENGTH_4X = 2,///<4x
  IDSOPT_CMN_MEM_WRITE_TRAINING_BURST_LENGTH_8X = 3,///<8x
} IDSOPT_CMN_MEM_WRITE_TRAINING_BURST_LENGTH;

///Training Retry Count
///Retry on PMU training failure, retry to all channels, including passed and failed channel.
typedef enum {
  IDSOPT_CMN_TRAINING_RETRY_COUNT_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_TRAINING_RETRY_COUNT_DISABLED = 0,///<Disabled
  IDSOPT_CMN_TRAINING_RETRY_COUNT_1 = 1,///<1
  IDSOPT_CMN_TRAINING_RETRY_COUNT_2 = 2,///<2
  IDSOPT_CMN_TRAINING_RETRY_COUNT_3 = 3,///<3
  IDSOPT_CMN_TRAINING_RETRY_COUNT_4 = 4,///<4
  IDSOPT_CMN_TRAINING_RETRY_COUNT_5 = 5,///<5
  IDSOPT_CMN_TRAINING_RETRY_COUNT_6 = 6,///<6
  IDSOPT_CMN_TRAINING_RETRY_COUNT_7 = 7,///<7
  IDSOPT_CMN_TRAINING_RETRY_COUNT_8 = 8,///<8
  IDSOPT_CMN_TRAINING_RETRY_COUNT_9 = 9,///<9
  IDSOPT_CMN_TRAINING_RETRY_COUNT_10 = 10,///<10
} IDSOPT_CMN_TRAINING_RETRY_COUNT;

///Periodic Training Mode
///Specify the PPT Control
typedef enum {
  IDSOPT_CMN_MEM_PERIODIC_TRAINING_MODE_DDR_DISABLED = 0,///<Disabled
  IDSOPT_CMN_MEM_PERIODIC_TRAINING_MODE_DDR_LEGACY = 1,///<Legacy
} IDSOPT_CMN_MEM_PERIODIC_TRAINING_MODE_DDR;

///Periodic Interval Mode
///0: Auto. The Periodic Interval value is decided by BIOS automatically.\n1: Manual. It is specified by the CBS option Periodic Interval.
typedef enum {
  IDSOPT_CMN_MEM_PERIODIC_INTERVAL_MODE_AUTO = 0,///<Auto
  IDSOPT_CMN_MEM_PERIODIC_INTERVAL_MODE_MANUAL = 1,///<Manual
} IDSOPT_CMN_MEM_PERIODIC_INTERVAL_MODE;

///Periodic Interval
///Periodic Interval value in milli-second, in decimal.\nRange 100 ~ 4095 ms.
#define IDSOPT_CMN_MEM_PERIODIC_INTERVAL_MIN 100 ///< Min of Periodic Interval
#define IDSOPT_CMN_MEM_PERIODIC_INTERVAL_MAX 4095 ///< Max of Periodic Interval

///TSME
///Transparent SME
typedef enum {
  IDSOPT_CMN_MEM_TSME_ENABLE_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_TSME_ENABLE_DDR_ENABLED = 1,///<Enabled
  IDSOPT_CMN_MEM_TSME_ENABLE_DDR_DISABLED = 0,///<Disabled
} IDSOPT_CMN_MEM_TSME_ENABLE_DDR;

///AES
///AES mode: AES-128 or AES-256 (default)
typedef enum {
  IDSOPT_CMN_MEM_AES_AES128 = 0,///<AES-128
  IDSOPT_CMN_MEM_AES_AES256 = 1,///<AES-256
} IDSOPT_CMN_MEM_AES;

///Data Scramble
///Data scrambling: DataScrambleEn
typedef enum {
  IDSOPT_CMN_MEM_DATA_SCRAMBLE_ENABLED = 1,///<Enabled
  IDSOPT_CMN_MEM_DATA_SCRAMBLE_DISABLED = 0,///<Disabled
} IDSOPT_CMN_MEM_DATA_SCRAMBLE;

///SME-MK
///SME-MK encryption mode.\nEnabling both SMEE and SME-MK is not supported. Results in #GP.
typedef enum {
  IDSOPT_CMN_MEM_SME_MK_ENABLE_ENABLED = 1,///<Enabled
  IDSOPT_CMN_MEM_SME_MK_ENABLE_DISABLED = 0,///<Disabled
} IDSOPT_CMN_MEM_SME_MK_ENABLE;

///PMIC Error Reporting
///Enables support for PMIC Error Reporting.
typedef enum {
  IDSOPT_CMN_PMIC_ERROR_REPORTING_FALSE = 0,///<False
  IDSOPT_CMN_PMIC_ERROR_REPORTING_TRUE = 1,///<True
  IDSOPT_CMN_PMIC_ERROR_REPORTING_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_PMIC_ERROR_REPORTING;

///PMIC Operation Mode
///1 - Programmable Mode Operation (default); 0 - Secure Mode Operation\nProgrammable mode allows certain registers to be programmed after VR enable else they will be in secure mode
typedef enum {
  IDSOPT_CMN_MEM_CTRLLER_PMIC_OP_MODE_SECUREMODE = 0,///<Secure Mode
  IDSOPT_CMN_MEM_CTRLLER_PMIC_OP_MODE_PROGRAMMABLEMODE = 1,///<Programmable Mode
} IDSOPT_CMN_MEM_CTRLLER_PMIC_OP_MODE;

///PMIC Fault Recovery
///0 - Always; 1 - Never (default); 2 - Once\nAlways - PMIC will ignore previous boot errors. No channel disabled\nNever - PMIC disables the channel with errors from previous boot.\nOnce - PMIC will ignore the previous boot errors once. More than once channel will be disabled
typedef enum {
  IDSOPT_CMN_MEM_CTRLLER_PMIC_FAULT_RECOVERY_ALWAYS = 0,///<Always
  IDSOPT_CMN_MEM_CTRLLER_PMIC_FAULT_RECOVERY_NEVER = 1,///<Never
  IDSOPT_CMN_MEM_CTRLLER_PMIC_FAULT_RECOVERY_ONCE = 2,///<Once
} IDSOPT_CMN_MEM_CTRLLER_PMIC_FAULT_RECOVERY;

///PMIC SWA/SWB VDD Core
///Range is from 1000mV to 1200mV; default of 1100mV\nApply to PMIC register 0x21, 0x23.
#define IDSOPT_CMN_MEM_CTRLLER_PMIC_SWA_SWB_VDD_CORE_MIN 1000 ///< Min of PMIC SWA/SWB VDD Core
#define IDSOPT_CMN_MEM_CTRLLER_PMIC_SWA_SWB_VDD_CORE_MAX 1200 ///< Max of PMIC SWA/SWB VDD Core

///PMIC SWC VDDIO
///Range is from 1000mV to 1200mV; default of 1100mV.\nApply to PMIC register 0x25.
#define IDSOPT_CMN_MEM_CTRLLER_PMIC_SWC_VDDIO_MIN 1000 ///< Min of PMIC SWC VDDIO
#define IDSOPT_CMN_MEM_CTRLLER_PMIC_SWC_VDDIO_MAX 1200 ///< Max of PMIC SWC VDDIO

///PMIC SWD VPP
///Range is from 1500mV to 2135mV; default of 1800mV.\nApply to PMIC register 0x27.
#define IDSOPT_CMN_MEM_CTRLLER_PMIC_SWD_VPP_MIN 1500 ///< Min of PMIC SWD VPP
#define IDSOPT_CMN_MEM_CTRLLER_PMIC_SWD_VPP_MAX 2135 ///< Max of PMIC SWD VPP

///PMIC Stagger Delay
///Amount of time to wait between powering on each DIMMs in milliseconds
#define IDSOPT_CMN_MEM_CTRLLER_PMIC_STAGGER_DELAY_MIN 0 ///< Min of PMIC Stagger Delay
#define IDSOPT_CMN_MEM_CTRLLER_PMIC_STAGGER_DELAY_MAX 0xFF ///< Max of PMIC Stagger Delay

///Max PMIC Power On
///Maximum number of DIMMs that can power on at the same time.
#define IDSOPT_CMN_MEM_CTRLLER_MAX_PMIC_POWER_ON_MIN 1 ///< Min of Max PMIC Power On
#define IDSOPT_CMN_MEM_CTRLLER_MAX_PMIC_POWER_ON_MAX 0xFF ///< Max of Max PMIC Power On

///ODTS Thermal Throttle Control
///ODTS Thermal Throttle control is a HW function that is always enabled
typedef enum {
  IDSOPT_CMN_MEM_ODTS_CMD_THROTTLE_CYCLE_CTL_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_ODTS_CMD_THROTTLE_CYCLE_CTL_DDR_ENABLED = 1,///<Enabled
  IDSOPT_CMN_MEM_ODTS_CMD_THROTTLE_CYCLE_CTL_DDR_DISABLED = 0,///<Disabled
} IDSOPT_CMN_MEM_ODTS_CMD_THROTTLE_CYCLE_CTL_DDR;

///ODTS Thermal Throttle Threshold
///Dram MR4 Temperature status value to start ODTS Command Thermal Throttling
typedef enum {
  IDSOPT_CMN_MEM_ODTS_CMD_THROTTLE_THRESHOLD_DDR_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_MEM_ODTS_CMD_THROTTLE_THRESHOLD_DDR_85C = 3,///< > 85' C
  IDSOPT_CMN_MEM_ODTS_CMD_THROTTLE_THRESHOLD_DDR_90C = 4,///< > 90' C
  IDSOPT_CMN_MEM_ODTS_CMD_THROTTLE_THRESHOLD_DDR_95C = 5,///< > 95' C
} IDSOPT_CMN_MEM_ODTS_CMD_THROTTLE_THRESHOLD_DDR;

///TSOD Thermal Throttle Control
///TSOD = Thermal Sensor On DIMM\nEnables SoC (PM firmware) based thermal management of DDR5 memory, based on thermal sensor located on DIMM
typedef enum {
  IDSOPT_CMN_TSOD_THERMAL_THROTTLE_CONTROL_DDR_ENABLED = 1,///<Enabled
  IDSOPT_CMN_TSOD_THERMAL_THROTTLE_CONTROL_DDR_DISABLED = 0,///<Disabled
} IDSOPT_CMN_TSOD_THERMAL_THROTTLE_CONTROL_DDR;

///TSOD Thermal Throttle Start Temperature
///Sets an integer temperature threshold at which TSOD (Thermal Sensor on DIMM) based memory throttling begins.  Applies to all installed and enabled DIMMs
#define IDSOPT_CMN_TSOD_THERMAL_THROTTLE_START_TEMP_DDR_MIN 40 ///< Min of TSOD Thermal Throttle Start Temperature
#define IDSOPT_CMN_TSOD_THERMAL_THROTTLE_START_TEMP_DDR_MAX 100 ///< Max of TSOD Thermal Throttle Start Temperature

///TSOD Thermal Throttle Hysteresis
///Sets an integer number of degrees the reported TSOD(Thermal Sensor On DIMM) temperature must drop below the "TSOD Thermal Throttle Start Temperature" before memory throttling is removed.  Applies to all installed and enabled DIMMs
#define IDSOPT_CMN_TSOD_THERMAL_THROTTLE_HYSTERESIS_DDR_MIN 1 ///< Min of TSOD Thermal Throttle Hysteresis
#define IDSOPT_CMN_TSOD_THERMAL_THROTTLE_HYSTERESIS_DDR_MAX 50 ///< Max of TSOD Thermal Throttle Hysteresis

///TSOD Command Throttle Percentage (Threshold)
///Sets an integer value for the DDR throttling applied when the "TSOD Thermal Throttle Start Temperature" is exceeded.
#define IDSOPT_CMN_TSOD_CMD_THROTTLE_PERCENTAGE0_DDR_MIN 0 ///< Min of TSOD Command Throttle Percentage (Threshold)
#define IDSOPT_CMN_TSOD_CMD_THROTTLE_PERCENTAGE0_DDR_MAX 80 ///< Max of TSOD Command Throttle Percentage (Threshold)

///TSOD Command Throttle Percentage (Threshold+5C)
///Sets an integer value for the DDR throttling applied when the "TSOD Thermal Throttle Start Temperature" is exceeded by more than 5C.
#define IDSOPT_CMN_TSOD_CMD_THROTTLE_PERCENTAGE5_DDR_MIN 0 ///< Min of TSOD Command Throttle Percentage (Threshold+5C)
#define IDSOPT_CMN_TSOD_CMD_THROTTLE_PERCENTAGE5_DDR_MAX 80 ///< Max of TSOD Command Throttle Percentage (Threshold+5C)

///TSOD Command Throttle Percentage (Threshold+10C)
///Sets an integer value for the DDR throttling applied when the "TSOD Thermal Throttle Start Temperature" is exceeded by more than 10C.
#define IDSOPT_CMN_TSOD_CMD_THROTTLE_PERCENTAGE10_DDR_MIN 0 ///< Min of TSOD Command Throttle Percentage (Threshold+10C)
#define IDSOPT_CMN_TSOD_CMD_THROTTLE_PERCENTAGE10_DDR_MAX 80 ///< Max of TSOD Command Throttle Percentage (Threshold+10C)

///PCIe loopback Mode
///Enable/Disable PcieLoopBackMode
typedef enum {
  IDSOPT_CMN_GNB_PCIE_LOOP_BACK_MODE_AUTO = 0xF,///<Auto
  IDSOPT_CMN_GNB_PCIE_LOOP_BACK_MODE_DISABLED = 0,///<Disabled
  IDSOPT_CMN_GNB_PCIE_LOOP_BACK_MODE_ENABLED = 1,///<Enabled
} IDSOPT_CMN_GNB_PCIE_LOOP_BACK_MODE;

///Enable 2 SPC (Gen 4)
///Enable this setting to use 2 symbols per clock for devices at Gen 4 speed.
typedef enum {
  IDSOPT_ENABLE2_SPC_GEN4_ENABLE = 1,///<Enable
  IDSOPT_ENABLE2_SPC_GEN4_DISABLE = 0,///<Disable
  IDSOPT_ENABLE2_SPC_GEN4_AUTO = 0xf,///<Auto
} IDSOPT_ENABLE2_SPC_GEN4;

///Enable 2 SPC (Gen 5)
///Enable this setting to use 2 symbols per clock for devices at Gen 5 speed.
typedef enum {
  IDSOPT_ENABLE2_SPC_GEN5_ENABLE = 1,///<Enable
  IDSOPT_ENABLE2_SPC_GEN5_DISABLE = 0,///<Disable
  IDSOPT_ENABLE2_SPC_GEN5_AUTO = 0xf,///<Auto
} IDSOPT_ENABLE2_SPC_GEN5;

///Safe recovery upon a BERExceeded Error
typedef enum {
  IDSOPT_GNB_SAFE_RECOVERY_UPON_ABER_EXCEEDED_ERROR_AUTO = 0xf,///<Auto
  IDSOPT_GNB_SAFE_RECOVERY_UPON_ABER_EXCEEDED_ERROR_ENABLE = 1,///<Enable
  IDSOPT_GNB_SAFE_RECOVERY_UPON_ABER_EXCEEDED_ERROR_DISABLE = 0,///<Disable
} IDSOPT_GNB_SAFE_RECOVERY_UPON_ABER_EXCEEDED_ERROR;

///Periodic Calibration
typedef enum {
  IDSOPT_GNB_PERIODIC_CALIBRATION_AUTO = 0xf,///<Auto
  IDSOPT_GNB_PERIODIC_CALIBRATION_ENABLE = 1,///<Enable
  IDSOPT_GNB_PERIODIC_CALIBRATION_DISABLE = 0,///<Disable
} IDSOPT_GNB_PERIODIC_CALIBRATION;

///TDP Control
///Auto = Use the fused TDP\nManual = User can set customized TDP
typedef enum {
  IDSOPT_CMN_TDP_CTL_MANUAL = 1,///<Manual
  IDSOPT_CMN_TDP_CTL_AUTO = 0,///<Auto
} IDSOPT_CMN_TDP_CTL;

///TDP
///TDP [W] (in decimal)
#define IDSOPT_CMN_TDP_LIMIT_MIN 0 ///< Min of TDP
#define IDSOPT_CMN_TDP_LIMIT_MAX 0xffffffff ///< Max of TDP

///PPT Control
///Auto = Use the fused PPT\nManual = User can set customized PPT
typedef enum {
  IDSOPT_CMN_PPT_CTL_MANUAL = 1,///<Manual
  IDSOPT_CMN_PPT_CTL_AUTO = 0,///<Auto
} IDSOPT_CMN_PPT_CTL;

///PPT
///PPT [W] (in decimal)
#define IDSOPT_CMN_PPT_LIMIT_MIN 0 ///< Min of PPT
#define IDSOPT_CMN_PPT_LIMIT_MAX 0xffffffff ///< Max of PPT

///Determinism Control
///Auto = Use default performance determinism settings\nManual = User can set custom performance determinism settings
typedef enum {
  IDSOPT_CMN_DETERMINISM_CTL_MANUAL = 1,///<Manual
  IDSOPT_CMN_DETERMINISM_CTL_AUTO = 0,///<Auto
} IDSOPT_CMN_DETERMINISM_CTL;

///Determinism Enable
///[0 = Power; 1 = Performance]
typedef enum {
  IDSOPT_CMN_DETERMINISM_ENABLE_POWER = 0,///<Power
  IDSOPT_CMN_DETERMINISM_ENABLE_PERFORMANCE = 1,///<Performance
} IDSOPT_CMN_DETERMINISM_ENABLE;

///xGMI Link Width Control
///Auto = Use default xGMI link width controller settings\nManual = User can set custom xGMI link width controller settings
typedef enum {
  IDSOPT_CMNX_GMI_LINK_WIDTH_CTL_MANUAL = 1,///<Manual
  IDSOPT_CMNX_GMI_LINK_WIDTH_CTL_AUTO = 0,///<Auto
} IDSOPT_CMNX_GMI_LINK_WIDTH_CTL;

///xGMI Force Link Width Control
///Unforce = Do not force the xGMI to a fixed width\nForce = Force the xGMI link to the user specified width
typedef enum {
  IDSOPT_CMNX_GMI_FORCE_LINK_WIDTH_CTL_AUTO = 0xF,///<Auto
  IDSOPT_CMNX_GMI_FORCE_LINK_WIDTH_CTL_FORCE = 1,///<Force
  IDSOPT_CMNX_GMI_FORCE_LINK_WIDTH_CTL_UNFORCE = 0,///<Unforce
} IDSOPT_CMNX_GMI_FORCE_LINK_WIDTH_CTL;

///xGMI Force Link Width
///0 = Force xGMI link width to x4 \n1 = Force xGMI link width to x8 \n2 = Force xGMI link width to x16
typedef enum {
  IDSOPT_CMNX_GMI_FORCE_LINK_WIDTH_AUTO = 0xF,///<Auto
  IDSOPT_CMNX_GMI_FORCE_LINK_WIDTH_2 = 2,///<2
  IDSOPT_CMNX_GMI_FORCE_LINK_WIDTH_1 = 1,///<1
  IDSOPT_CMNX_GMI_FORCE_LINK_WIDTH_0 = 0,///<0
} IDSOPT_CMNX_GMI_FORCE_LINK_WIDTH;

///xGMI Max Link Width Range Control
///Auto = Use default xGMI max supported link width\nManual = User can set custom xGMI max link width
typedef enum {
  IDSOPT_CMNX_GMI_MAX_LINK_WIDTH_CTL_MANUAL = 1,///<Manual
  IDSOPT_CMNX_GMI_MAX_LINK_WIDTH_CTL_AUTO = 0,///<Auto
} IDSOPT_CMNX_GMI_MAX_LINK_WIDTH_CTL;

///xGMI Max Link Width
///0 = Set max xGMI link width to x4\n1 = Set max xGMI link width to x8\n2 = Set max xGMI link width to x16
typedef enum {
  IDSOPT_CMNX_GMI_MAX_LINK_WIDTH_AUTO = 0xF,///<Auto
  IDSOPT_CMNX_GMI_MAX_LINK_WIDTH_2 = 2,///<2
  IDSOPT_CMNX_GMI_MAX_LINK_WIDTH_1 = 1,///<1
  IDSOPT_CMNX_GMI_MAX_LINK_WIDTH_0 = 0,///<0
} IDSOPT_CMNX_GMI_MAX_LINK_WIDTH;

///xGMI Min Link Width
///0 = Set min xGMI link width to x4\n1 = Set min xGMI link width to x8\n2 = Set min xGMI link width to x16
typedef enum {
  IDSOPT_CMNX_GMI_MIN_LINK_WIDTH_AUTO = 0xF,///<Auto
  IDSOPT_CMNX_GMI_MIN_LINK_WIDTH_2 = 2,///<2
  IDSOPT_CMNX_GMI_MIN_LINK_WIDTH_1 = 1,///<1
  IDSOPT_CMNX_GMI_MIN_LINK_WIDTH_0 = 0,///<0
} IDSOPT_CMNX_GMI_MIN_LINK_WIDTH;

///APBDIS
///0 = not APBDIS (mission mode)\n1 = APBDIS
typedef enum {
  IDSOPT_CMN_APBDIS_0 = 0,///<0
  IDSOPT_CMN_APBDIS_1 = 1,///<1
  IDSOPT_CMN_APBDIS_AUTO = 0xf,///<Auto
} IDSOPT_CMN_APBDIS;

///DfPstate
///DfPstate index to set when APBDIS=1 [0-2]
#define IDSOPT_CMN_APBDIS_DF_PSTATE_MIN 0 ///< Min of DfPstate
#define IDSOPT_CMN_APBDIS_DF_PSTATE_MAX 2 ///< Max of DfPstate

///Power Profile Selection
///[0 = High Performance Mode; 1 = Efficiency Mode; 2 = Maximum IO Performance Mode; 3 = Balanced Memory Performance Mode; 4 = Balanced Core Performance Mode; 5 = Balanced Core Memory Performance Mode; 0xFF = Auto]
typedef enum {
  IDSOPT_CMN_EFFICIENCY_MODE_EN_HIGHPERFORMANCEMODE = 0,///<High Performance Mode
  IDSOPT_CMN_EFFICIENCY_MODE_EN_EFFICIENCYMODE = 1,///< Efficiency Mode
  IDSOPT_CMN_EFFICIENCY_MODE_EN_MAXIMUMIOPERFORMANCEMODE = 2,///<Maximum IO Performance Mode
  IDSOPT_CMN_EFFICIENCY_MODE_EN_BALANCEDMEMORYPERFORMANCEMODE = 3,///<Balanced Memory Performance Mode
  IDSOPT_CMN_EFFICIENCY_MODE_EN_BALANCEDCOREPERFORMANCEMODE = 4,///<Balanced Core Performance Mode
  IDSOPT_CMN_EFFICIENCY_MODE_EN_BALANCEDCOREMEMORYPERFORMANCEMODE = 5,///<Balanced Core Memory Performance Mode
  IDSOPT_CMN_EFFICIENCY_MODE_EN_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_EFFICIENCY_MODE_EN;

///xGMI Pstate Control
/// If manual, set XgmiPstateRangeSupportEn=1, else 0
typedef enum {
  IDSOPT_CMN_XGMI_PSTATE_CONTROL_AUTO = 0xf,///<Auto
  IDSOPT_CMN_XGMI_PSTATE_CONTROL_MANUAL = 1,///<Manual
} IDSOPT_CMN_XGMI_PSTATE_CONTROL;

///xGMI Pstate Selection
///XgmiPstateRangeMax and XgmiPstateRangeMin to 0 for High speed and 1 for Low speed
typedef enum {
  IDSOPT_CMN_XGMI_PSTATE_SELECTION_HIGHSPEED = 0,///<High Speed
  IDSOPT_CMN_XGMI_PSTATE_SELECTION_LOWSPEED = 1,///<Low Speed
} IDSOPT_CMN_XGMI_PSTATE_SELECTION;

///BoostFmaxEn
///Auto = Use the default Fmax\nManual = User can set the boost Fmax
typedef enum {
  IDSOPT_CMN_BOOST_FMAX_EN_MANUAL = 1,///<Manual
  IDSOPT_CMN_BOOST_FMAX_EN_AUTO = 0,///<Auto
} IDSOPT_CMN_BOOST_FMAX_EN;

///BoostFmax
///Specify the boost Fmax frequency limit to apply to all cores (MHz in decimal)
#define IDSOPT_CMN_BOOST_FMAX_MIN 0 ///< Min of BoostFmax
#define IDSOPT_CMN_BOOST_FMAX_MAX 0xFFFF ///< Max of BoostFmax

///DF PState Frequency Optimizer
///Disabled - means disable the DFPstate CCLK effective frequency optimizer\nEnabled - means enable the DFPstate CCLK effective frequency optimizer
typedef enum {
  IDSOPT_CMN_GNB_SMU_DFFO_AUTO = 0xF,///<Auto
  IDSOPT_CMN_GNB_SMU_DFFO_ENABLED = 0,///<Enabled
  IDSOPT_CMN_GNB_SMU_DFFO_DISABLED = 1,///<Disabled
} IDSOPT_CMN_GNB_SMU_DFFO;

///DF Cstates
///Enable = Enable the feature : Disable = Disable the feature
typedef enum {
  IDSOPT_CMN_GNB_SMU_DF_CSTATES_DISABLED = 0,///<Disabled
  IDSOPT_CMN_GNB_SMU_DF_CSTATES_ENABLED = 1,///<Enabled
  IDSOPT_CMN_GNB_SMU_DF_CSTATES_AUTO = 0xf,///<Auto
} IDSOPT_CMN_GNB_SMU_DF_CSTATES;

///CPPC
///Enable = Enable the feature : Disable = Disable the feature
typedef enum {
  IDSOPT_CMN_GNB_SMU_CPPC_DISABLED = 0,///<Disabled
  IDSOPT_CMN_GNB_SMU_CPPC_ENABLED = 1,///<Enabled
  IDSOPT_CMN_GNB_SMU_CPPC_AUTO = 0xf,///<Auto
} IDSOPT_CMN_GNB_SMU_CPPC;

///HSMP Support
///Select HSMP support enable or disable
typedef enum {
  IDSOPT_CMN_GNB_SMU_HSMP_SUPPORT_DISABLED = 0,///<Disabled
  IDSOPT_CMN_GNB_SMU_HSMP_SUPPORT_ENABLED = 1,///<Enabled
  IDSOPT_CMN_GNB_SMU_HSMP_SUPPORT_AUTO = 0xF,///<Auto
} IDSOPT_CMN_GNB_SMU_HSMP_SUPPORT;

///SVI3 SVC Speed Control
///Enables for programming of the SVI3 SVC speed. Auto = Use default SVI3 speed control \nManual = User can set custom SVI3 speed control settings
typedef enum {
  IDSOPT_CMN_SVI3_SVC_SPEED_CTL_AUTO = 0,///<Auto
  IDSOPT_CMN_SVI3_SVC_SPEED_CTL_MANUAL = 1,///<Manual
} IDSOPT_CMN_SVI3_SVC_SPEED_CTL;

///SVI3 SVC Speed
///0=50.00MHz\n1=40.00MHz\n2=26.67MHz\n3=20.00MHz\n4=16.00MHz\n5=13.33MHz\n6=10.00MHz\n7=8.00MHz\n8=5.00MHz
typedef enum {
  IDSOPT_CMN_SVI3_SVC_SPEED_2000MHZ = 3,///<20.00MHz
  IDSOPT_CMN_SVI3_SVC_SPEED_1333MHZ = 5,///<13.33MHz
  IDSOPT_CMN_SVI3_SVC_SPEED_500MHZ = 8,///<5.00MHz
} IDSOPT_CMN_SVI3_SVC_SPEED;

///3D V-Cache
///Override of X3D technology
typedef enum {
  IDSOPT_CMN_X3D_STACK_OVERRIDE_AUTO = 0xF,///<Auto
  IDSOPT_CMN_X3D_STACK_OVERRIDE_DISABLE = 0,///<Disable
  IDSOPT_CMN_X3D_STACK_OVERRIDE_1STACK = 1,///<1 stack
} IDSOPT_CMN_X3D_STACK_OVERRIDE;

///L3 BIST
///Enable or Disable L3 BIST
typedef enum {
  IDSOPT_CMN_L3_BIST_DISABLE = 0,///<Disable
  IDSOPT_CMN_L3_BIST_ENABLE = 1,///<Enable
  IDSOPT_CMN_L3_BIST_AUTO = 0xF,///<Auto
} IDSOPT_CMN_L3_BIST;

///Diagnostic Mode
///Select Diag mode enable or disable
typedef enum {
  IDSOPT_CMN_GNB_DIAG_MODE_DISABLED = 0,///<Disabled
  IDSOPT_CMN_GNB_DIAG_MODE_ENABLED = 1,///<Enabled
  IDSOPT_CMN_GNB_DIAG_MODE_AUTO = 0xF,///<Auto
} IDSOPT_CMN_GNB_DIAG_MODE;

///GMI Folding
///Enable = Enable the feature : Disable = Disable the feature
typedef enum {
  IDSOPT_CMN_GNB_SMU_GMI_FOLDING_DISABLED = 0,///<Disabled
  IDSOPT_CMN_GNB_SMU_GMI_FOLDING_ENABLED = 1,///<Enabled
  IDSOPT_CMN_GNB_SMU_GMI_FOLDING_AUTO = 0xf,///<Auto
} IDSOPT_CMN_GNB_SMU_GMI_FOLDING;

///Separate CPU power plane throttling
///Disable=link throttling for CPU planes; Enable=unlink throttling for CPU planes
typedef enum {
  IDSOPT_CMN_THROTTLER_MODE_ENABLE = 1,///<Enable
  IDSOPT_CMN_THROTTLER_MODE_DISABLE = 0,///<Disable
  IDSOPT_CMN_THROTTLER_MODE_AUTO = 0xf,///<Auto
} IDSOPT_CMN_THROTTLER_MODE;

///DfPstate Range Control
///Disable - Disable DF Pstate Range Control\nEnable - Enable DF Pstate Range Control\n\nDF Pstate selection is overridden by the APBDIS BIOS option if it is selected. If this feature is enabled, the range value setting should follow the rule that the DF Pstate Max Index must be less than or equal to the DF Pstate Min Index. Otherwise, DF Pstate Range selections will not work.
typedef enum {
  IDSOPT_CMN_DF_PSTATE_RANGE_CTL_DISABLE = 0,///<Disable
  IDSOPT_CMN_DF_PSTATE_RANGE_CTL_ENABLE = 1,///<Enable
  IDSOPT_CMN_DF_PSTATE_RANGE_CTL_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_DF_PSTATE_RANGE_CTL;

///DfPstate Max Index
///DfPstate index [0-2]\n0 - DFP0\n1 - DFP1\n2 - DFP2
typedef enum {
  IDSOPT_CMN_DF_PSTATE_MAX_DFP0 = 0,///<DFP0
  IDSOPT_CMN_DF_PSTATE_MAX_DFP1 = 1,///<DFP1
  IDSOPT_CMN_DF_PSTATE_MAX_DFP2 = 2,///<DFP2
} IDSOPT_CMN_DF_PSTATE_MAX;

///DfPstate Min Index
///DfPstate index [0-2]\n0 - DFP0\n1 - DFP1\n2 - DFP2
typedef enum {
  IDSOPT_CMN_DF_PSTATE_MIN_DFP0 = 0,///<DFP0
  IDSOPT_CMN_DF_PSTATE_MIN_DFP1 = 1,///<DFP1
  IDSOPT_CMN_DF_PSTATE_MIN_DFP2 = 2,///<DFP2
} IDSOPT_CMN_DF_PSTATE_MIN;

///NBIO RAS Control
///(0) Disabled, (1) MCA
typedef enum {
  IDSOPT_CMN_RAS_CONTROL_DISABLED = 0,///<Disabled
  IDSOPT_CMN_RAS_CONTROL_MCA = 1,///<MCA
  IDSOPT_CMN_RAS_CONTROL_AUTO = 0xF,///<Auto
} IDSOPT_CMN_RAS_CONTROL;

///NBIO SyncFlood Generation
///This value may be used to mask SyncFlood caused by NBIO RAS options.  When set to TRUE SyncFlood from NBIO is masked.  When set to FALSE NBIO is capable of generating SyncFlood.
typedef enum {
  IDSOPT_CMN_NBIO_SYNC_FLOOD_GEN_ENABLED = 1,///<Enabled
  IDSOPT_CMN_NBIO_SYNC_FLOOD_GEN_DISABLED = 0,///<Disabled
  IDSOPT_CMN_NBIO_SYNC_FLOOD_GEN_AUTO = 0xf,///<Auto
} IDSOPT_CMN_NBIO_SYNC_FLOOD_GEN;

///NBIO SyncFlood Reporting
///This value may be used to enable SyncFlood reporting to APML.  When set to TRUE SyncFlood will be reported to APML.  When set to FALSE that reporting well be disabled
typedef enum {
  IDSOPT_PCD_SYNC_FLOOD_TO_APML_ENABLED = 1,///<Enabled
  IDSOPT_PCD_SYNC_FLOOD_TO_APML_DISABLED = 0,///<Disabled
  IDSOPT_PCD_SYNC_FLOOD_TO_APML_AUTO = 0xF,///<Auto
} IDSOPT_PCD_SYNC_FLOOD_TO_APML;

///PCIe Aer Reporting Mechanism
///This value selects the method of reporting AER errors from PCI Express.  A value of 1 allows OS First handling of the errors through generation of a system control interrupt (SCI).  A value of 2 provides for Firmware First handling of errors through generation of a system management interrupt (SMI).
typedef enum {
  IDSOPT_CMN_GNB_AMD_PCIE_AER_REPORT_MECHANISM_FIRMWAREFIRST = 2,///<Firmware First
  IDSOPT_CMN_GNB_AMD_PCIE_AER_REPORT_MECHANISM_FIRMWAREFIRSTBUTALLOWOSFIRST = 3,///<Firmware First but allow OS First
  IDSOPT_CMN_GNB_AMD_PCIE_AER_REPORT_MECHANISM_OSFIRST = 1,///<OS First
  IDSOPT_CMN_GNB_AMD_PCIE_AER_REPORT_MECHANISM_AUTO = 0x0F,///<Auto
} IDSOPT_CMN_GNB_AMD_PCIE_AER_REPORT_MECHANISM;

///Edpc Control
///(0) Disabled; (1) Enabled; (3) Auto
typedef enum {
  IDSOPT_EDPC_CONTROL_DISABLED = 0,///<Disabled
  IDSOPT_EDPC_CONTROL_ENABLED = 1,///<Enabled
  IDSOPT_EDPC_CONTROL_AUTO = 3,///<Auto
} IDSOPT_EDPC_CONTROL;

///ACS RAS Request Value
typedef enum {
  IDSOPT_ACS_RAS_VALUE_DIRECTREQUESTACCESSENABLED = 0,///<Direct Request Access Enabled
  IDSOPT_ACS_RAS_VALUE_REQUESTBLOCKINGENABLED = 1,///<Request Blocking Enabled
  IDSOPT_ACS_RAS_VALUE_REQUESTREDIRECTENABLED = 2,///<Request Redirect Enabled
  IDSOPT_ACS_RAS_VALUE_AUTO = 0xF,///<Auto
} IDSOPT_ACS_RAS_VALUE;

///NBIO Poison Consumption
///NBIO Poison Consumption
typedef enum {
  IDSOPT_CMN_POISON_CONSUMPTION_AUTO = 0xF,///<Auto
  IDSOPT_CMN_POISON_CONSUMPTION_DISABLED = 0,///<Disabled
  IDSOPT_CMN_POISON_CONSUMPTION_ENABLED = 1,///<Enabled
} IDSOPT_CMN_POISON_CONSUMPTION;

///Sync Flood on PCIe Fatal Error
///When "Sync Flood on PCIe Fatal Error" is True, PcdAmdPcieSyncFloodOnFatal should be set to True.\n\nWhen "Sync Flood on PCIe Fatal Error" is False, PcdAmdPcieSyncFloodOnFatal should be set to False.\n\nWhen "Sync Flood on PCIe Fatal Error" is Auto, PcdAmdPcieSyncFloodOnFatal should retain its AGESA default.
typedef enum {
  IDSOPT_CMN_GNB_RAS_SYNCFLOOD_PCIE_FATAL_ERROR_AUTO = 0xF,///<Auto
  IDSOPT_CMN_GNB_RAS_SYNCFLOOD_PCIE_FATAL_ERROR_TRUE = 1,///<True
  IDSOPT_CMN_GNB_RAS_SYNCFLOOD_PCIE_FATAL_ERROR_FALSE = 0,///<False
} IDSOPT_CMN_GNB_RAS_SYNCFLOOD_PCIE_FATAL_ERROR;

///NBIO RAS Numerical Common Options
///All the numerical RAS CBS options override the PCD values automatically.\nDisable: Keep original PCD values\nManual: Display custom numerical RAS CBS options
typedef enum {
  IDSOPT_CMN_RAS_NUMERICAL_COMMON_OPTIONS_DISABLE = 0,///<Disable
  IDSOPT_CMN_RAS_NUMERICAL_COMMON_OPTIONS_MANUAL = 1,///<Manual
} IDSOPT_CMN_RAS_NUMERICAL_COMMON_OPTIONS;

///Egress Poison Severity High
///Each bit set to 1 enables HIGH severity on the associated IOHC egress port. A bit of 0 indicates LOW severity.
#define IDSOPT_PCD_EGRESS_POISON_SEVERITY_HI_MIN 0 ///< Min of Egress Poison Severity High
#define IDSOPT_PCD_EGRESS_POISON_SEVERITY_HI_MAX 0xffffffff ///< Max of Egress Poison Severity High

///Egress Poison Severity Low
///Each bit set to 1 enables HIGH severity on the associated IOHC egress port. A bit of 0 indicates LOW severity.
#define IDSOPT_PCD_EGRESS_POISON_SEVERITY_LO_MIN 0 ///< Min of Egress Poison Severity Low
#define IDSOPT_PCD_EGRESS_POISON_SEVERITY_LO_MAX 0xffffffff ///< Max of Egress Poison Severity Low

///Egress Poison Mask High
///These set the enable mask for masking of errors logged in EGRESS_POISON_STATUS. For each bit set to 1, errors are masked.  For each bit set to 0, errors trigger response actions.
#define IDSOPT_PCD_AMD_NBIO_EGRESS_POISON_MASK_HI_MIN 0 ///< Min of Egress Poison Mask High
#define IDSOPT_PCD_AMD_NBIO_EGRESS_POISON_MASK_HI_MAX 0xffffffff ///< Max of Egress Poison Mask High

///Egress Poison Mask Low
///These set the enable mask for masking of errors logged in EGRESS_POISON_STATUS. For each bit set to 1, errors are masked.  For each bit set to 0, errors trigger response actions.
#define IDSOPT_PCD_AMD_NBIO_EGRESS_POISON_MASK_LO_MIN 0 ///< Min of Egress Poison Mask Low
#define IDSOPT_PCD_AMD_NBIO_EGRESS_POISON_MASK_LO_MAX 0xffffffff ///< Max of Egress Poison Mask Low

///Uncorrected Converted to Poison Enable Mask High
///These set the enable mask for masking of uncorrectable parity errors on internal arrays.  For each bit set to 1, a system fatal error event is triggered for UCP errors on arrays associated with that egress port.  For each bit set to 0, errors are masked.
#define IDSOPT_PCD_AMD_NBIO_RAS_UCP_MASK_HI_MIN 0 ///< Min of Uncorrected Converted to Poison Enable Mask High
#define IDSOPT_PCD_AMD_NBIO_RAS_UCP_MASK_HI_MAX 0xffffffff ///< Max of Uncorrected Converted to Poison Enable Mask High

///Uncorrected Converted to Poison Enable Mask Low
///These set the enable mask for masking of uncorrectable parity errors on internal arrays.  For each bit set to 1, a system fatal error event is triggered for UCP errors on arrays associated with that egress port.  For each bit set to 0, errors are masked.
#define IDSOPT_PCD_AMD_NBIO_RAS_UCP_MASK_LO_MIN 0 ///< Min of Uncorrected Converted to Poison Enable Mask Low
#define IDSOPT_PCD_AMD_NBIO_RAS_UCP_MASK_LO_MAX 0xffffffff ///< Max of Uncorrected Converted to Poison Enable Mask Low

///System Hub Watchdog Timer
///This value specifies the timer interval of the SYSHUB Watchdog timer in miliseconds.\nMaximium value is 5200ms
#define IDSOPT_PCD_SYSHUB_WDT_TIMER_INTERVAL_MIN 0 ///< Min of System Hub Watchdog Timer
#define IDSOPT_PCD_SYSHUB_WDT_TIMER_INTERVAL_MAX 5200 ///< Max of System Hub Watchdog Timer

///Data Object Exchange
///Data Object Exchange (DOE)
typedef enum {
  IDSOPT_CMN_GNB_DATA_OBJECT_EXCHANGE_DISABLED = 0,///<Disabled
  IDSOPT_CMN_GNB_DATA_OBJECT_EXCHANGE_ENABLED = 1,///<Enabled
  IDSOPT_CMN_GNB_DATA_OBJECT_EXCHANGE_AUTO = 0xf,///<Auto
} IDSOPT_CMN_GNB_DATA_OBJECT_EXCHANGE;

///RTM Margining Support
///RTM Margining Support
typedef enum {
  IDSOPT_CMN_GNB_RTM_MARGINING_SUPPORT_DISABLE = 0,///<Disable
  IDSOPT_CMN_GNB_RTM_MARGINING_SUPPORT_ENABLE = 1,///<Enable
  IDSOPT_CMN_GNB_RTM_MARGINING_SUPPORT_AUTO = 0xF,///<Auto
} IDSOPT_CMN_GNB_RTM_MARGINING_SUPPORT;

///Multi Auto Speed Change On Last Rate
///Force PCIe link training speed to last advertised for all ports.\nDisabled=Use highest data rate ever advertised.\nEnabled=Use last data rate advertised.
typedef enum {
  IDSOPT_CMN_NBIO_FORCE_SPEED_LAST_ADVERTISED_DISABLE = 0,///<Disable
  IDSOPT_CMN_NBIO_FORCE_SPEED_LAST_ADVERTISED_ENABLE = 1,///<Enable
  IDSOPT_CMN_NBIO_FORCE_SPEED_LAST_ADVERTISED_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_NBIO_FORCE_SPEED_LAST_ADVERTISED;

///Multi Upstream Auto Speed Change
///Defines the setting of this feature for all PCIe devices.  "Auto" uses the DXIO default setting of 0 for Gen1 and 1 for Gen2/3
typedef enum {
  IDSOPT_CMN_LC_MULT_UPSTREAM_AUTO_DISABLED = 0,///<Disabled
  IDSOPT_CMN_LC_MULT_UPSTREAM_AUTO_ENABLED = 1,///<Enabled
  IDSOPT_CMN_LC_MULT_UPSTREAM_AUTO_AUTO = 0xF,///<Auto
} IDSOPT_CMN_LC_MULT_UPSTREAM_AUTO;

///Allow Compliance
///When enabled, allows the PCIe RP to enter Polling.Compliance state
typedef enum {
  IDSOPT_STRAP_COMPLIANCE_DIS_AUTO = 0xf,///<Auto
  IDSOPT_STRAP_COMPLIANCE_DIS_DISABLE = 1,///<Disable
  IDSOPT_STRAP_COMPLIANCE_DIS_ENABLE = 0,///<Enable
} IDSOPT_STRAP_COMPLIANCE_DIS;

///EQ Bypass To Highest Rate
///Controls the ability to advertise Equalization Bypass to Highest Rate Support in TSxs sent prior to LinkUp=1
typedef enum {
  IDSOPT_CMN_NBIO_PCIE_ADVERTISE_EQ_TO_HIGH_RATE_SUPPORT_DISABLE = 0,///<Disable
  IDSOPT_CMN_NBIO_PCIE_ADVERTISE_EQ_TO_HIGH_RATE_SUPPORT_ENABLE = 1,///<Enable
  IDSOPT_CMN_NBIO_PCIE_ADVERTISE_EQ_TO_HIGH_RATE_SUPPORT_AUTO = 0xF,///<Auto
} IDSOPT_CMN_NBIO_PCIE_ADVERTISE_EQ_TO_HIGH_RATE_SUPPORT;

///Data Link Feature Cap
///Data Link Feature Capability
typedef enum {
  IDSOPT_CMN_GNB_DATA_LINK_FEATURE_CAP_ENABLED = 1,///<Enabled
  IDSOPT_CMN_GNB_DATA_LINK_FEATURE_CAP_DISABLED = 0,///<Disabled
  IDSOPT_CMN_GNB_DATA_LINK_FEATURE_CAP_AUTO = 0xF,///<Auto
} IDSOPT_CMN_GNB_DATA_LINK_FEATURE_CAP;

///Data Link Feature Exchange
///Data Link Feature Exchange
typedef enum {
  IDSOPT_CMN_GNB_DATA_LINK_FEATURE_EXCHANGE_ENABLED = 1,///<Enabled
  IDSOPT_CMN_GNB_DATA_LINK_FEATURE_EXCHANGE_DISABLED = 0,///<Disabled
  IDSOPT_CMN_GNB_DATA_LINK_FEATURE_EXCHANGE_AUTO = 0xF,///<Auto
} IDSOPT_CMN_GNB_DATA_LINK_FEATURE_EXCHANGE;

///SRIS
///SRIS
typedef enum {
  IDSOPT_CMN_GNB_SRIS_AUTO = 0xF,///<Auto
  IDSOPT_CMN_GNB_SRIS_DISABLE = 0,///<Disable
  IDSOPT_CMN_GNB_SRIS_ENABLE = 1,///<Enable
} IDSOPT_CMN_GNB_SRIS;

///ACS Enable
///AER must be enabled for ACS enable to work
typedef enum {
  IDSOPT_DBG_GNB_DBG_ACS_ENABLE_ENABLE = 1,///<Enable
  IDSOPT_DBG_GNB_DBG_ACS_ENABLE_DISABLED = 0,///<Disabled
  IDSOPT_DBG_GNB_DBG_ACS_ENABLE_AUTO = 0xF,///<Auto
} IDSOPT_DBG_GNB_DBG_ACS_ENABLE;

///PCIe Ten Bit Tag Support
///Enables PCIe ten bit tags for supported devices.\nAuto = Disabled
typedef enum {
  IDSOPT_GNB_CMN_PCIE_TBT_SUPPORT_DISABLE = 0,///<Disable
  IDSOPT_GNB_CMN_PCIE_TBT_SUPPORT_ENABLE = 1,///<Enable
  IDSOPT_GNB_CMN_PCIE_TBT_SUPPORT_AUTO = 0xf,///<Auto
} IDSOPT_GNB_CMN_PCIE_TBT_SUPPORT;

///PCIe ARI Enumeration
///ARI Forwarding Enable for each downstream port
typedef enum {
  IDSOPT_GNB_CMN_PCIE_ARI_ENUMERATION_DISABLE = 0,///<Disable
  IDSOPT_GNB_CMN_PCIE_ARI_ENUMERATION_ENABLE = 1,///<Enable
  IDSOPT_GNB_CMN_PCIE_ARI_ENUMERATION_AUTO = 0xf,///<Auto
} IDSOPT_GNB_CMN_PCIE_ARI_ENUMERATION;

///PCIe ARI Support
///Enables Alternative Routing-ID Interpretation
typedef enum {
  IDSOPT_CMN_GNB_PCIE_ARI_SUPPORT_DISABLE = 0,///<Disable
  IDSOPT_CMN_GNB_PCIE_ARI_SUPPORT_ENABLE = 1,///<Enable
  IDSOPT_CMN_GNB_PCIE_ARI_SUPPORT_AUTO = 0xf,///<Auto
} IDSOPT_CMN_GNB_PCIE_ARI_SUPPORT;

///Presence Detect Select mode
///Control the Presence Detect Select mode
typedef enum {
  IDSOPT_PRESENCE_DETECT_SELECTMODE_OR = 0,///<OR
  IDSOPT_PRESENCE_DETECT_SELECTMODE_AND = 1,///<AND
  IDSOPT_PRESENCE_DETECT_SELECTMODE_AUTO = 0xF,///<Auto
  IDSOPT_PRESENCE_DETECT_SELECTMODE_INBANDONLY = 2,///<In-Band Only
  IDSOPT_PRESENCE_DETECT_SELECTMODE_OUTOFBANDONLY = 3,///<Out-Of-Band Only
} IDSOPT_PRESENCE_DETECT_SELECTMODE;

///Hot Plug Handling mode
///Control the Hot Plug Handling mode
typedef enum {
  IDSOPT_HOT_PLUG_HANDLING_MODE_OSFIRST = 1,///<OS First
  IDSOPT_HOT_PLUG_HANDLING_MODE_FIRMWAREFIRSTEDRIFOSSUPPORTS = 3,///<Firmware First/EDR if OS supports
  IDSOPT_HOT_PLUG_HANDLING_MODE_FIRMWAREFIRSTBUTALLOWOSFIRST = 6,///<Firmware First but allow OS First
  IDSOPT_HOT_PLUG_HANDLING_MODE_SYSTEMFIRMWAREINTERMEDIARY = 5,///<System Firmware Intermediary
  IDSOPT_HOT_PLUG_HANDLING_MODE_AUTO = 0xF,///<Auto
} IDSOPT_HOT_PLUG_HANDLING_MODE;

///Presence Detect State Settle Time
///Presence Detect State Settle Time Enable/Disable
typedef enum {
  IDSOPT_HOT_PLUG_PD_SETTLE_AUTO = 0,///<Auto
  IDSOPT_HOT_PLUG_PD_SETTLE_TRUE = 1,///<TRUE
  IDSOPT_HOT_PLUG_PD_SETTLE_FALSE = 0,///<FALSE
} IDSOPT_HOT_PLUG_PD_SETTLE;

///Hot Plug Port Settle Time
///Hex values. Value is between 0x1 to 0xFF (1ms to 255ms) but valid value are 0x1 to 0xFE.\n0xFF: Force settle time to 0ms.
#define IDSOPT_HOT_PLUG_SETTLE_TIME_MIN 1 ///< Min of Hot Plug Port Settle Time
#define IDSOPT_HOT_PLUG_SETTLE_TIME_MAX 255 ///< Max of Hot Plug Port Settle Time

///Hotplug Support
///Allows disabling hot plug functionality.\nAuto = Normal functionality\nDisabled = Hot plug functionality disabled
typedef enum {
  IDSOPT_HOTPLUG_SUPPORT_AUTO = 0xF,///<Auto
  IDSOPT_HOTPLUG_SUPPORT_DISABLED = 0,///<Disabled
} IDSOPT_HOTPLUG_SUPPORT;

///Early Link Speed
///Set Early Link Speed
typedef enum {
  IDSOPT_CMN_EARLY_LINK_SPEED_MAX = 0,///<Max
  IDSOPT_CMN_EARLY_LINK_SPEED_GEN1 = 1,///<Gen1
  IDSOPT_CMN_EARLY_LINK_SPEED_GEN2 = 2,///<Gen2
  IDSOPT_CMN_EARLY_LINK_SPEED_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_EARLY_LINK_SPEED;

///Enable AER Cap
///Enables Advanced Error Reporting Capability
typedef enum {
  IDSOPT_DBG_GNB_DBG_AERCAP_ENABLE_ENABLE = 1,///<Enable
  IDSOPT_DBG_GNB_DBG_AERCAP_ENABLE_DISABLED = 0,///<Disabled
  IDSOPT_DBG_GNB_DBG_AERCAP_ENABLE_AUTO = 0xF,///<Auto
} IDSOPT_DBG_GNB_DBG_AERCAP_ENABLE;

///PCIE Link Speed Capability
///Set all PCIe port speed capability
typedef enum {
  IDSOPT_CMN_PCIE_CAP_LINK_SPEED_MAXIMUMSPEED = 0,///<Maximum speed
  IDSOPT_CMN_PCIE_CAP_LINK_SPEED_GEN1 = 1,///<GEN1
  IDSOPT_CMN_PCIE_CAP_LINK_SPEED_GEN2 = 2,///<GEN2
  IDSOPT_CMN_PCIE_CAP_LINK_SPEED_GEN3 = 3,///<GEN3
  IDSOPT_CMN_PCIE_CAP_LINK_SPEED_GEN4 = 4,///<GEN4
  IDSOPT_CMN_PCIE_CAP_LINK_SPEED_GEN5 = 5,///<GEN5
  IDSOPT_CMN_PCIE_CAP_LINK_SPEED_AUTO = 0xf,///<Auto
} IDSOPT_CMN_PCIE_CAP_LINK_SPEED;

///PCIE Target Link Speed
///Set PCIe speed on all ports
typedef enum {
  IDSOPT_CMN_PCIE_TARGET_LINK_SPEED_MAXIMUMSPEED = 0,///<Maximum speed
  IDSOPT_CMN_PCIE_TARGET_LINK_SPEED_GEN1 = 1,///<GEN1
  IDSOPT_CMN_PCIE_TARGET_LINK_SPEED_GEN2 = 2,///<GEN2
  IDSOPT_CMN_PCIE_TARGET_LINK_SPEED_GEN3 = 3,///<GEN3
  IDSOPT_CMN_PCIE_TARGET_LINK_SPEED_GEN4 = 4,///<GEN4
  IDSOPT_CMN_PCIE_TARGET_LINK_SPEED_GEN5 = 5,///<GEN5
  IDSOPT_CMN_PCIE_TARGET_LINK_SPEED_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_PCIE_TARGET_LINK_SPEED;

///ASPM Control
typedef enum {
  IDSOPT_CMN_ALL_PORTS_ASPM_DISABLE = 0,///<Disable
  IDSOPT_CMN_ALL_PORTS_ASPM_L1 = 2,///<L1
  IDSOPT_CMN_ALL_PORTS_ASPM_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_ALL_PORTS_ASPM;

///MCTP Enable
///Enable/Disable MCTP
typedef enum {
  IDSOPT_CMN_NBIO_MCTP_EN_DISABLE = 0,///<Disable
  IDSOPT_CMN_NBIO_MCTP_EN_ENABLE = 1,///<Enable
  IDSOPT_CMN_NBIO_MCTP_EN_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_NBIO_MCTP_EN;

///MCTP Mode
///Option to configure how MCTP packets are routed.\n\n\nMCTP Bridge - Enables MCTP Endpoint Support and manually routes all unrouteable VDMs to the BMC via an internal MCTP Bridge.\n\nLegacy MCTP + MCTP Bridge - BIOS will enable MCTP Bridge when required based off the system configuration, otherwise BIOS will default to Legacy MCTP where MCTP endpoint support is disabled and the BMC will route MCTP packets directly to other MCTP endpoints through the processor root ports without an internal MCTP Bridge.
typedef enum {
  IDSOPT_CMN_NBIO_MCTP_MODE_MCTPBRIDGE = 0,///<MCTP Bridge
  IDSOPT_CMN_NBIO_MCTP_MODE_LEGACYMCTPMCTPBRIDGE = 1,///<Legacy MCTP + MCTP Bridge
  IDSOPT_CMN_NBIO_MCTP_MODE_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_NBIO_MCTP_MODE;

///MCTP discovery notify message
///Send AMD MCTP discovery notifify message to indicate it's presence to BMC on the bus
typedef enum {
  IDSOPT_CMN_NBIO_MCTP_DISCOVERY_NOTIFY_MESSAGE_DISABLE = 0,///<Disable
  IDSOPT_CMN_NBIO_MCTP_DISCOVERY_NOTIFY_MESSAGE_ENABLE = 1,///<Enable
  IDSOPT_CMN_NBIO_MCTP_DISCOVERY_NOTIFY_MESSAGE_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_NBIO_MCTP_DISCOVERY_NOTIFY_MESSAGE;

///Non-PCIe Compliant Support
///Enable this setting to send command to disable Extended EIEOS, DLF, and Gen 5 Support on training failure for non-pcie compliant devices.
typedef enum {
  IDSOPT_CMN_NBIO_PCIE_NON_PCIE_COMPLIANT_SUPPORT_DISABLE = 0,///<Disable
  IDSOPT_CMN_NBIO_PCIE_NON_PCIE_COMPLIANT_SUPPORT_ENABLE = 1,///<Enable
  IDSOPT_CMN_NBIO_PCIE_NON_PCIE_COMPLIANT_SUPPORT_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_NBIO_PCIE_NON_PCIE_COMPLIANT_SUPPORT;

///Limit hotplug devices to PCIe boot speed
///Enabled: Limit hotplug slots to Gen4 if system booted with only Gen4 devices, which optimizes idle power\nDisabled: Do not limit hotplug slots to Gen4 if system booted with only Gen4 devices, increases idle power
typedef enum {
  IDSOPT_CMN_LIMIT_HP_DEVICES_TO_PCIE_BOOT_SPEED_AUTO = 0xf,///<Auto
  IDSOPT_CMN_LIMIT_HP_DEVICES_TO_PCIE_BOOT_SPEED_ENABLE = 1,///<Enable
  IDSOPT_CMN_LIMIT_HP_DEVICES_TO_PCIE_BOOT_SPEED_DISABLE = 0,///<Disable
} IDSOPT_CMN_LIMIT_HP_DEVICES_TO_PCIE_BOOT_SPEED;

///Enable PCIe SFI Config via OOB
///Enable PCIe SFI Config via OOB support
typedef enum {
  IDSOPT_CMN_PC_IE_SFI_CONFIGVIA_OOB_EN_TRUE = 1,///<True
  IDSOPT_CMN_PC_IE_SFI_CONFIGVIA_OOB_EN_FALSE = 0,///<False
} IDSOPT_CMN_PC_IE_SFI_CONFIGVIA_OOB_EN;

///PCIE Idle Power Setting
///Modify PCIE Power Savings Features that Can Impact Lightly Loaded Latency
typedef enum {
  IDSOPT_CMN_NBIO_PCIE_IDLE_POWER_SETTING_OPTIMIZEFORLATENCY = 1,///<Optimize for Latency
  IDSOPT_CMN_NBIO_PCIE_IDLE_POWER_SETTING_OPTIMIZEFORPERFPOWER = 0xF,///<Optimize for Perf/Power
} IDSOPT_CMN_NBIO_PCIE_IDLE_POWER_SETTING;

///ACS Rcc_Dev0
///Enable ACS enable for RCC_DEV0 ,STRAP_ACS_EN_DN_DEV0
typedef enum {
  IDSOPT_CFG_ACS_EN_RCC_DEV0_AUTO = 0xF,///<Auto
  IDSOPT_CFG_ACS_EN_RCC_DEV0_DISABLE = 0,///<Disable
  IDSOPT_CFG_ACS_EN_RCC_DEV0_ENABLE = 1,///<Enable
} IDSOPT_CFG_ACS_EN_RCC_DEV0;

///AER Rcc_Dev0
///Enable AER enable for RCC_DEV0 ,STRAP_AER_EN_DN_DEV0
typedef enum {
  IDSOPT_CFG_AER_EN_RCC_DEV0_AUTO = 0xF,///<Auto
  IDSOPT_CFG_AER_EN_RCC_DEV0_DISABLE = 0,///<Disable
  IDSOPT_CFG_AER_EN_RCC_DEV0_ENABLE = 1,///<Enable
} IDSOPT_CFG_AER_EN_RCC_DEV0;

///DlfEnableStrap1
///RCC_BIF_STRAP1,STRAP_DLF_EN=1
typedef enum {
  IDSOPT_CFG_DLF_EN_STRAP1_AUTO = 0xF,///<Auto
  IDSOPT_CFG_DLF_EN_STRAP1_DISABLE = 0,///<Disable
  IDSOPT_CFG_DLF_EN_STRAP1_ENABLE = 1,///<Enable
} IDSOPT_CFG_DLF_EN_STRAP1;

///Phy16GTStrap1
///RCC_BIF_STRAP1 ,STRAP_PHY_16GT_EN=1
typedef enum {
  IDSOPT_CFG_PHY16GT_STRAP1_AUTO = 0xF,///<Auto
  IDSOPT_CFG_PHY16GT_STRAP1_DISABLE = 0,///<Disable
  IDSOPT_CFG_PHY16GT_STRAP1_ENABLE = 1,///<Enable
} IDSOPT_CFG_PHY16GT_STRAP1;

///MarginEnStrap1
///RCC_BIF_STRAP1 ,STRAP_MARGIN_EN=1
typedef enum {
  IDSOPT_CFG_MARGIN_EN_STRAP1_AUTO = 0xF,///<Auto
  IDSOPT_CFG_MARGIN_EN_STRAP1_DISABLE = 0,///<Disable
  IDSOPT_CFG_MARGIN_EN_STRAP1_ENABLE = 1,///<Enable
} IDSOPT_CFG_MARGIN_EN_STRAP1;

///SourceValStrap5
///RCC_DEV0_PORT_STRAP5 = 0xAF80_0000,Source Validation bit \n[23]
typedef enum {
  IDSOPT_CFG_ACS_SOURCE_VAL_STRAP5_AUTO = 0xF,///<Auto
  IDSOPT_CFG_ACS_SOURCE_VAL_STRAP5_DISABLE = 0,///<Disable
  IDSOPT_CFG_ACS_SOURCE_VAL_STRAP5_ENABLE = 1,///<Enable
} IDSOPT_CFG_ACS_SOURCE_VAL_STRAP5;

///TranslationalBlockingStrap5
///RCC_DEV0_PORT_STRAP5 = 0xAF80_0000,Translational Blocking bit [24]
typedef enum {
  IDSOPT_CFG_ACS_TRANSLATIONAL_BLOCKING_STRAP5_AUTO = 0xF,///<Auto
  IDSOPT_CFG_ACS_TRANSLATIONAL_BLOCKING_STRAP5_DISABLE = 0,///<Disable
  IDSOPT_CFG_ACS_TRANSLATIONAL_BLOCKING_STRAP5_ENABLE = 1,///<Enable
} IDSOPT_CFG_ACS_TRANSLATIONAL_BLOCKING_STRAP5;

///P2pReq ACS Control
///PCIE_ACS_CNTL_dev0 0x001D,P2P page request bit[2]
typedef enum {
  IDSOPT_CFG_ACS_P2P_REQ_AUTO = 0xF,///<Auto
  IDSOPT_CFG_ACS_P2P_REQ_DISABLE = 0,///<Disable
  IDSOPT_CFG_ACS_P2P_REQ_ENABLE = 1,///<Enable
} IDSOPT_CFG_ACS_P2P_REQ;

///P2pCompStrap5
///RCC_DEV0_PORT_STRAP5 = 0xAF80_0000,P2P Completion bit[26]
typedef enum {
  IDSOPT_CFG_ACS_P2P_COMP_STRAP5_AUTO = 0xF,///<Auto
  IDSOPT_CFG_ACS_P2P_COMP_STRAP5_DISABLE = 0,///<Disable
  IDSOPT_CFG_ACS_P2P_COMP_STRAP5_ENABLE = 1,///<Enable
} IDSOPT_CFG_ACS_P2P_COMP_STRAP5;

///UpstreamFwdStrap5
///RCC_DEV0_PORT_STRAP5 = 0xAF80_0000,Upstream Forwarding bit[27]
typedef enum {
  IDSOPT_CFG_ACS_UPSTREAM_FWD_STRAP5_AUTO = 0xF,///<Auto
  IDSOPT_CFG_ACS_UPSTREAM_FWD_STRAP5_DISABLE = 0,///<Disable
  IDSOPT_CFG_ACS_UPSTREAM_FWD_STRAP5_ENABLE = 1,///<Enable
} IDSOPT_CFG_ACS_UPSTREAM_FWD_STRAP5;

///P2PEgressStrap5
///RCC_DEV0_PORT_STRAP5 = 0xAF80_0000,P2P Egress bit[28]
typedef enum {
  IDSOPT_CFG_ACS_P2_P_EGRESS_STRAP5_AUTO = 0xF,///<Auto
  IDSOPT_CFG_ACS_P2_P_EGRESS_STRAP5_DISABLE = 0,///<Disable
  IDSOPT_CFG_ACS_P2_P_EGRESS_STRAP5_ENABLE = 1,///<Enable
} IDSOPT_CFG_ACS_P2_P_EGRESS_STRAP5;

///DirectTranslatedStrap5
///RCC_DEV0_PORT_STRAP5 = 0xAF80_0000,ACS Direct Translated bit [29]
typedef enum {
  IDSOPT_CFG_ACS_DIRECT_TRANSLATED_STRAP5_AUTO = 0xF,///<Auto
  IDSOPT_CFG_ACS_DIRECT_TRANSLATED_STRAP5_DISABLE = 0,///<Disable
  IDSOPT_CFG_ACS_DIRECT_TRANSLATED_STRAP5_ENABLE = 1,///<Enable
} IDSOPT_CFG_ACS_DIRECT_TRANSLATED_STRAP5;

///SsidEnStrap5
///RCC_DEV0_PORT_STRAP5 = 0xAF80_0000,ACS SSID Enable bit [31]
typedef enum {
  IDSOPT_CFG_ACS_SSID_EN_STRAP5_AUTO = 0xF,///<Auto
  IDSOPT_CFG_ACS_SSID_EN_STRAP5_DISABLE = 0,///<Disable
  IDSOPT_CFG_ACS_SSID_EN_STRAP5_ENABLE = 1,///<Enable
} IDSOPT_CFG_ACS_SSID_EN_STRAP5;

///PriEnPageReq
///PCIE_PAGE_REQ_CNTL (for all nbio instances) set to 0x0001,PriEn bit set
typedef enum {
  IDSOPT_CFG_PRI_EN_PAGE_REQ_AUTO = 0xF,///<Auto
  IDSOPT_CFG_PRI_EN_PAGE_REQ_DISABLE = 0,///<Disable
  IDSOPT_CFG_PRI_EN_PAGE_REQ_ENABLE = 1,///<Enable
} IDSOPT_CFG_PRI_EN_PAGE_REQ;

///PriResetPageReq
///PCIE_PAGE_REQ_CNTL (for all nbio instances) set to 0x0001,Pri Reset bit clear
typedef enum {
  IDSOPT_CFG_PRI_RESET_PAGE_REQ_AUTO = 0xF,///<Auto
  IDSOPT_CFG_PRI_RESET_PAGE_REQ_DISABLE = 0,///<Disable
  IDSOPT_CFG_PRI_RESET_PAGE_REQ_ENABLE = 1,///<Enable
} IDSOPT_CFG_PRI_RESET_PAGE_REQ;

///SourceVal ACS cntl
///PCIE_ACS_CNTL_dev0 = 0x001D, Source Validation bit [0] is set
typedef enum {
  IDSOPT_CFG_ACS_SOURCE_VAL_AUTO = 0xF,///<Auto
  IDSOPT_CFG_ACS_SOURCE_VAL_DISABLE = 0,///<Disable
  IDSOPT_CFG_ACS_SOURCE_VAL_ENABLE = 1,///<Enable
} IDSOPT_CFG_ACS_SOURCE_VAL;

///TranslationalBlocking ACS Control
///PCIE_ACS_CNTL_dev0 = 0x001D, Translational blocking bit[1]
typedef enum {
  IDSOPT_CFG_ACS_TRANSLATIONAL_BLOCKING_AUTO = 0xF,///<Auto
  IDSOPT_CFG_ACS_TRANSLATIONAL_BLOCKING_DISABLE = 0,///<Disable
  IDSOPT_CFG_ACS_TRANSLATIONAL_BLOCKING_ENABLE = 1,///<Enable
} IDSOPT_CFG_ACS_TRANSLATIONAL_BLOCKING;

///P2pComp ACS Control
///PCIE_ACS_CNTL_dev0=0x001D,P2P Completion bit[3]
typedef enum {
  IDSOPT_CFG_ACS_P2P_COMP_AUTO = 0xF,///<Auto
  IDSOPT_CFG_ACS_P2P_COMP_DISABLE = 0,///<Disable
  IDSOPT_CFG_ACS_P2P_COMP_ENABLE = 1,///<Enable
} IDSOPT_CFG_ACS_P2P_COMP;

///UpstreamFwd ACS Control
///PCIE_ACS_CNTL_dev0 = 0x001D,Upstream Forwarding bit[4]
typedef enum {
  IDSOPT_CFG_ACS_UPSTREAM_FWD_AUTO = 0xF,///<Auto
  IDSOPT_CFG_ACS_UPSTREAM_FWD_DISABLE = 0,///<Disable
  IDSOPT_CFG_ACS_UPSTREAM_FWD_ENABLE = 1,///<Enable
} IDSOPT_CFG_ACS_UPSTREAM_FWD;

///P2PEgress ACS Control
///PCIE_ACS_CNTL_dev0 = 0x001D,P2P Egress bit[5]
typedef enum {
  IDSOPT_CFG_ACS_P2_P_EGRESS_AUTO = 0xF,///<Auto
  IDSOPT_CFG_ACS_P2_P_EGRESS_DISABLE = 0,///<Disable
  IDSOPT_CFG_ACS_P2_P_EGRESS_ENABLE = 1,///<Enable
} IDSOPT_CFG_ACS_P2_P_EGRESS;

///P2pReqStrap5
///RCC_DEV0_PORT_STRAP5 = 0xAF80_0000,P2P request strap bit
typedef enum {
  IDSOPT_CFG_ACS_P2P_REQ_STRAP5_AUTO = 0xF,///<Auto
  IDSOPT_CFG_ACS_P2P_REQ_STRAP5_DISABLE = 0,///<Disable
  IDSOPT_CFG_ACS_P2P_REQ_STRAP5_ENABLE = 1,///<Enable
} IDSOPT_CFG_ACS_P2P_REQ_STRAP5;

///E2E_PREFIX
///RCC_DEV0_PORT_STRAP2 has bit  STRAP_E2E_PREFIX_EN_DEV0
typedef enum {
  IDSOPT_CFG_E2_E_PREFIX_AUTO = 0xF,///<Auto
  IDSOPT_CFG_E2_E_PREFIX_DISABLE = 0,///<Disable
  IDSOPT_CFG_E2_E_PREFIX_ENABLE = 1,///<Enable
} IDSOPT_CFG_E2_E_PREFIX;

///EXTENDED_FMT
///RCC_DEV0_PORT_STRAP2 has bit STRAP_EXTENDED_FMT_SUPPORTED_DEV0
typedef enum {
  IDSOPT_CFG_EXTENDED_FMT_SUPPORTED_AUTO = 0xF,///<Auto
  IDSOPT_CFG_EXTENDED_FMT_SUPPORTED_DISABLE = 0,///<Disable
  IDSOPT_CFG_EXTENDED_FMT_SUPPORTED_ENABLE = 1,///<Enable
} IDSOPT_CFG_EXTENDED_FMT_SUPPORTED;

///AtomicRoutingStrap5
///NBIF DEV0 Enable AtomicOp Routing support in Downstream Port.
typedef enum {
  IDSOPT_CMN_NBIO_ATOMIC_ROUTING_STRAP5_DISABLE = 0,///<Disable
  IDSOPT_CMN_NBIO_ATOMIC_ROUTING_STRAP5_ENABLE = 1,///<Enable
  IDSOPT_CMN_NBIO_ATOMIC_ROUTING_STRAP5_AUTO = 0xF,///<Auto
} IDSOPT_CMN_NBIO_ATOMIC_ROUTING_STRAP5;

///SEV-SNP Support
///Enables support for Secure Encrypted Virtualization and Secure Nested Paging
typedef enum {
  IDSOPT_SEV_SNP_SUPPORT_DISABLE = 0,///<Disable
  IDSOPT_SEV_SNP_SUPPORT_ENABLE = 1,///<Enable
  IDSOPT_SEV_SNP_SUPPORT_AUTO = 0xf,///<Auto
} IDSOPT_SEV_SNP_SUPPORT;

///SEV-TIO Support
///Support Secure IO with TDSIP enabled devices
typedef enum {
  IDSOPT_SEV_TIO_SUPPORT_DISABLED = 0,///<Disabled
  IDSOPT_SEV_TIO_SUPPORT_ENABLED = 1,///<Enabled
  IDSOPT_SEV_TIO_SUPPORT_AUTO = 0xFF,///<Auto
} IDSOPT_SEV_TIO_SUPPORT;

///DRTM Memory Reservation
///Reserve 128MB memory below Bottom IO for DRTM. It is required to be enabled for Secured-Core Server function.
typedef enum {
  IDSOPT_CMN_DRTM_MEMORY_RESERVATION_DISABLED = 0,///<Disabled
  IDSOPT_CMN_DRTM_MEMORY_RESERVATION_ENABLED = 1,///<Enabled
  IDSOPT_CMN_DRTM_MEMORY_RESERVATION_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_DRTM_MEMORY_RESERVATION;

///DRTM Virtual Device Support
///Enable DRTM ACPI virtual device.
typedef enum {
  IDSOPT_CMN_DRTM_SUPPORT_DISABLED = 0,///<Disabled
  IDSOPT_CMN_DRTM_SUPPORT_ENABLED = 1,///<Enabled
  IDSOPT_CMN_DRTM_SUPPORT_AUTO = 0xF,///<Auto
} IDSOPT_CMN_DRTM_SUPPORT;

///DMA Protection
///Enable DMA remap support in IVRS IVinfo Field.
typedef enum {
  IDSOPT_CMN_DMA_PROTECTION_AUTO = 0xF,///<Auto
  IDSOPT_CMN_DMA_PROTECTION_ENABLED = 1,///<Enabled
  IDSOPT_CMN_DMA_PROTECTION_DISABLED = 0,///<Disabled
} IDSOPT_CMN_DMA_PROTECTION;

///IOMMU
///Enable/Disable IOMMU
typedef enum {
  IDSOPT_CMN_GNB_NB_IOMMU_DISABLED = 0,///<Disabled
  IDSOPT_CMN_GNB_NB_IOMMU_ENABLED = 1,///<Enabled
  IDSOPT_CMN_GNB_NB_IOMMU_AUTO = 0xf,///<Auto
} IDSOPT_CMN_GNB_NB_IOMMU;

///DMAr Support
///Enable DMAr system protection during POST.
typedef enum {
  IDSOPT_CMN_DMAR_SUPPORT_DISABLED = 0,///<Disabled
  IDSOPT_CMN_DMAR_SUPPORT_ENABLED = 1,///<Enabled
  IDSOPT_CMN_DMAR_SUPPORT_AUTO = 0xF,///<Auto
} IDSOPT_CMN_DMAR_SUPPORT;

///Enable Port Bifurcation
///Change the configuration of each PCIe link individually.\nBy default, each link is configured 1 port of 16 lanes. (x16)
typedef enum {
  IDSOPT_CMN_ENABLE_PORT_BIFURCATION_AUTO = 0xf,///<Auto
  IDSOPT_CMN_ENABLE_PORT_BIFURCATION_ENABLE = 1,///<Enable
  IDSOPT_CMN_ENABLE_PORT_BIFURCATION_DISABLE = 0,///<Disable
} IDSOPT_CMN_ENABLE_PORT_BIFURCATION;

///Socket 0 P0 Override
///Select configuration for Socket-0 PCIe link P0\nBy default is 1 port of 16 lanes. (x16)
typedef enum {
  IDSOPT_CMN_S0_P0_OVERRIDE_AUTO = 0xf,///<Auto
  IDSOPT_CMN_S0_P0_OVERRIDE_1PORTOFX82PORTSOFX4 = 0x9,///<1 port of x8 + 2 ports of x4
  IDSOPT_CMN_S0_P0_OVERRIDE_1PORTOFX88PORTSOFX1 = 0xA,///<1 port of x8 + 8 ports of x1
  IDSOPT_CMN_S0_P0_OVERRIDE_2PORTSOFX8 = 0xB,///<2 ports of x8
  IDSOPT_CMN_S0_P0_OVERRIDE_4PORTSOFX4 = 0xC,///<4 ports of x4
  IDSOPT_CMN_S0_P0_OVERRIDE_8PORTSOFX2 = 0xD,///<8 ports of x2
} IDSOPT_CMN_S0_P0_OVERRIDE;

///Socket 0 P1 Override
///Select configuration for Socket-0 PCIe link P1\nBy default is 1 port of 16 lanes. (x16)
typedef enum {
  IDSOPT_CMN_S0_P1_OVERRIDE_AUTO = 0xf,///<Auto
  IDSOPT_CMN_S0_P1_OVERRIDE_1PORTOFX82PORTSOFX4 = 0x9,///<1 port of x8 + 2 ports of x4
  IDSOPT_CMN_S0_P1_OVERRIDE_1PORTOFX88PORTSOFX1 = 0xA,///<1 port of x8 + 8 ports of x1
  IDSOPT_CMN_S0_P1_OVERRIDE_2PORTSOFX8 = 0xB,///<2 ports of x8
  IDSOPT_CMN_S0_P1_OVERRIDE_4PORTSOFX4 = 0xC,///<4 ports of x4
  IDSOPT_CMN_S0_P1_OVERRIDE_8PORTSOFX2 = 0xD,///<8 ports of x2
} IDSOPT_CMN_S0_P1_OVERRIDE;

///Socket 0 P2 Override
///Select configuration for Socket-0 PCIe link P2\nBy default is 1 port of 16 lanes. (x16)
typedef enum {
  IDSOPT_CMN_S0_P2_OVERRIDE_AUTO = 0xf,///<Auto
  IDSOPT_CMN_S0_P2_OVERRIDE_1PORTOFX82PORTSOFX4 = 0x9,///<1 port of x8 + 2 ports of x4
  IDSOPT_CMN_S0_P2_OVERRIDE_1PORTOFX88PORTSOFX1 = 0xA,///<1 port of x8 + 8 ports of x1
  IDSOPT_CMN_S0_P2_OVERRIDE_2PORTSOFX8 = 0xB,///<2 ports of x8
  IDSOPT_CMN_S0_P2_OVERRIDE_4PORTSOFX4 = 0xC,///<4 ports of x4
  IDSOPT_CMN_S0_P2_OVERRIDE_8PORTSOFX2 = 0xD,///<8 ports of x2
} IDSOPT_CMN_S0_P2_OVERRIDE;

///Socket 0 P3 Override
///Select configuration for Socket-0 PCIe link P3\nBy default is 1 port of 16 lanes. (x16)
typedef enum {
  IDSOPT_CMN_S0_P3_OVERRIDE_AUTO = 0xf,///<Auto
  IDSOPT_CMN_S0_P3_OVERRIDE_1PORTOFX82PORTSOFX4 = 0x9,///<1 port of x8 + 2 ports of x4
  IDSOPT_CMN_S0_P3_OVERRIDE_1PORTOFX88PORTSOFX1 = 0xA,///<1 port of x8 + 8 ports of x1
  IDSOPT_CMN_S0_P3_OVERRIDE_2PORTSOFX8 = 0xB,///<2 ports of x8
  IDSOPT_CMN_S0_P3_OVERRIDE_4PORTSOFX4 = 0xC,///<4 ports of x4
  IDSOPT_CMN_S0_P3_OVERRIDE_8PORTSOFX2 = 0xD,///<8 ports of x2
} IDSOPT_CMN_S0_P3_OVERRIDE;

///Socket 1 P0 Override
///Select configuration for Socket-1 PCIe link P0\nBy default is 1 port of 16 lanes. (x16)
typedef enum {
  IDSOPT_CMN_S1_P0_OVERRIDE_AUTO = 0xf,///<Auto
  IDSOPT_CMN_S1_P0_OVERRIDE_1PORTOFX82PORTSOFX4 = 0x9,///<1 port of x8 + 2 ports of x4
  IDSOPT_CMN_S1_P0_OVERRIDE_1PORTOFX88PORTSOFX1 = 0xA,///<1 port of x8 + 8 ports of x1
  IDSOPT_CMN_S1_P0_OVERRIDE_2PORTSOFX8 = 0xB,///<2 ports of x8
  IDSOPT_CMN_S1_P0_OVERRIDE_4PORTSOFX4 = 0xC,///<4 ports of x4
  IDSOPT_CMN_S1_P0_OVERRIDE_8PORTSOFX2 = 0xD,///<8 ports of x2
} IDSOPT_CMN_S1_P0_OVERRIDE;

///Socket 1 P1 Override
///Select configuration for Socket-1 PCIe link P1\nBy default is 1 port of 16 lanes. (x16)
typedef enum {
  IDSOPT_CMN_S1_P1_OVERRIDE_AUTO = 0xf,///<Auto
  IDSOPT_CMN_S1_P1_OVERRIDE_1PORTOFX82PORTSOFX4 = 0x9,///<1 port of x8 + 2 ports of x4
  IDSOPT_CMN_S1_P1_OVERRIDE_1PORTOFX88PORTSOFX1 = 0xA,///<1 port of x8 + 8 ports of x1
  IDSOPT_CMN_S1_P1_OVERRIDE_2PORTSOFX8 = 0xB,///<2 ports of x8
  IDSOPT_CMN_S1_P1_OVERRIDE_4PORTSOFX4 = 0xC,///<4 ports of x4
  IDSOPT_CMN_S1_P1_OVERRIDE_8PORTSOFX2 = 0xD,///<8 ports of x2
} IDSOPT_CMN_S1_P1_OVERRIDE;

///Socket 1 P2 Override
///Select configuration for Socket-1 PCIe link P2\nBy default is 1 port of 16 lanes. (x16)
typedef enum {
  IDSOPT_CMN_S1_P2_OVERRIDE_AUTO = 0xf,///<Auto
  IDSOPT_CMN_S1_P2_OVERRIDE_1PORTOFX82PORTSOFX4 = 0x9,///<1 port of x8 + 2 ports of x4
  IDSOPT_CMN_S1_P2_OVERRIDE_1PORTOFX88PORTSOFX1 = 0xA,///<1 port of x8 + 8 ports of x1
  IDSOPT_CMN_S1_P2_OVERRIDE_2PORTSOFX8 = 0xB,///<2 ports of x8
  IDSOPT_CMN_S1_P2_OVERRIDE_4PORTSOFX4 = 0xC,///<4 ports of x4
  IDSOPT_CMN_S1_P2_OVERRIDE_8PORTSOFX2 = 0xD,///<8 ports of x2
} IDSOPT_CMN_S1_P2_OVERRIDE;

///Socket 1 P3 Override
///Select configuration for Socket-1 PCIe link P3\nBy default is 1 port of 16 lanes. (x16)
typedef enum {
  IDSOPT_CMN_S1_P3_OVERRIDE_AUTO = 0xf,///<Auto
  IDSOPT_CMN_S1_P3_OVERRIDE_1PORTOFX82PORTSOFX4 = 0x9,///<1 port of x8 + 2 ports of x4
  IDSOPT_CMN_S1_P3_OVERRIDE_1PORTOFX88PORTSOFX1 = 0xA,///<1 port of x8 + 8 ports of x1
  IDSOPT_CMN_S1_P3_OVERRIDE_2PORTSOFX8 = 0xB,///<2 ports of x8
  IDSOPT_CMN_S1_P3_OVERRIDE_4PORTSOFX4 = 0xC,///<4 ports of x4
  IDSOPT_CMN_S1_P3_OVERRIDE_8PORTSOFX2 = 0xD,///<8 ports of x2
} IDSOPT_CMN_S1_P3_OVERRIDE;

///P0 Override
///Select configuration for PCIe link P0\nBy default is 1 port of 16 lanes. (x16)
typedef enum {
  IDSOPT_CMN_P0_OVERRIDE_AUTO = 0xf,///<Auto
  IDSOPT_CMN_P0_OVERRIDE_1PORTOFX82PORTSOFX4 = 0x9,///<1 port of x8 + 2 ports of x4
  IDSOPT_CMN_P0_OVERRIDE_1PORTOFX88PORTSOFX1 = 0xA,///<1 port of x8 + 8 ports of x1
  IDSOPT_CMN_P0_OVERRIDE_2PORTSOFX8 = 0xB,///<2 ports of x8
  IDSOPT_CMN_P0_OVERRIDE_4PORTSOFX4 = 0xC,///<4 ports of x4
  IDSOPT_CMN_P0_OVERRIDE_8PORTSOFX2 = 0xD,///<8 ports of x2
} IDSOPT_CMN_P0_OVERRIDE;

///P1 Override
///Select configuration for PCIe link P1\nBy default is 1 port of 16 lanes. (x16)
typedef enum {
  IDSOPT_CMN_P1_OVERRIDE_AUTO = 0xf,///<Auto
  IDSOPT_CMN_P1_OVERRIDE_1PORTOFX82PORTSOFX4 = 0x9,///<1 port of x8 + 2 ports of x4
  IDSOPT_CMN_P1_OVERRIDE_1PORTOFX88PORTSOFX1 = 0xA,///<1 port of x8 + 8 ports of x1
  IDSOPT_CMN_P1_OVERRIDE_2PORTSOFX8 = 0xB,///<2 ports of x8
  IDSOPT_CMN_P1_OVERRIDE_4PORTSOFX4 = 0xC,///<4 ports of x4
  IDSOPT_CMN_P1_OVERRIDE_8PORTSOFX2 = 0xD,///<8 ports of x2
} IDSOPT_CMN_P1_OVERRIDE;

///P2 Override
///Select configuration for PCIe link P2\nBy default is 1 port of 16 lanes. (x16)
typedef enum {
  IDSOPT_CMN_P2_OVERRIDE_AUTO = 0xf,///<Auto
  IDSOPT_CMN_P2_OVERRIDE_1PORTOFX82PORTSOFX4 = 0x9,///<1 port of x8 + 2 ports of x4
  IDSOPT_CMN_P2_OVERRIDE_1PORTOFX88PORTSOFX1 = 0xA,///<1 port of x8 + 8 ports of x1
  IDSOPT_CMN_P2_OVERRIDE_2PORTSOFX8 = 0xB,///<2 ports of x8
  IDSOPT_CMN_P2_OVERRIDE_4PORTSOFX4 = 0xC,///<4 ports of x4
  IDSOPT_CMN_P2_OVERRIDE_8PORTSOFX2 = 0xD,///<8 ports of x2
} IDSOPT_CMN_P2_OVERRIDE;

///P3 Override
///Select configuration for PCIe link P3\nBy default is 1 port of 16 lanes. (x16)
typedef enum {
  IDSOPT_CMN_P3_OVERRIDE_AUTO = 0xf,///<Auto
  IDSOPT_CMN_P3_OVERRIDE_1PORTOFX82PORTSOFX4 = 0x9,///<1 port of x8 + 2 ports of x4
  IDSOPT_CMN_P3_OVERRIDE_1PORTOFX88PORTSOFX1 = 0xA,///<1 port of x8 + 8 ports of x1
  IDSOPT_CMN_P3_OVERRIDE_2PORTSOFX8 = 0xB,///<2 ports of x8
  IDSOPT_CMN_P3_OVERRIDE_4PORTSOFX4 = 0xC,///<4 ports of x4
  IDSOPT_CMN_P3_OVERRIDE_8PORTSOFX2 = 0xD,///<8 ports of x2
} IDSOPT_CMN_P3_OVERRIDE;

///G0 Override
///Select configuration for PCIe link G0\nBy default is 1 port of 16 lanes. (x16)
typedef enum {
  IDSOPT_CMN_G0_OVERRIDE_AUTO = 0xf,///<Auto
  IDSOPT_CMN_G0_OVERRIDE_1PORTOFX82PORTSOFX4 = 0x9,///<1 port of x8 + 2 ports of x4
  IDSOPT_CMN_G0_OVERRIDE_1PORTOFX88PORTSOFX1 = 0xA,///<1 port of x8 + 8 ports of x1
  IDSOPT_CMN_G0_OVERRIDE_2PORTSOFX8 = 0xB,///<2 ports of x8
  IDSOPT_CMN_G0_OVERRIDE_4PORTSOFX4 = 0xC,///<4 ports of x4
  IDSOPT_CMN_G0_OVERRIDE_8PORTSOFX2 = 0xD,///<8 ports of x2
} IDSOPT_CMN_G0_OVERRIDE;

///G1 Override
///Select configuration for PCIe link G1\nBy default is 1 port of 16 lanes. (x16)
typedef enum {
  IDSOPT_CMN_G1_OVERRIDE_AUTO = 0xf,///<Auto
  IDSOPT_CMN_G1_OVERRIDE_1PORTOFX82PORTSOFX4 = 0x9,///<1 port of x8 + 2 ports of x4
  IDSOPT_CMN_G1_OVERRIDE_1PORTOFX88PORTSOFX1 = 0xA,///<1 port of x8 + 8 ports of x1
  IDSOPT_CMN_G1_OVERRIDE_2PORTSOFX8 = 0xB,///<2 ports of x8
  IDSOPT_CMN_G1_OVERRIDE_4PORTSOFX4 = 0xC,///<4 ports of x4
  IDSOPT_CMN_G1_OVERRIDE_8PORTSOFX2 = 0xD,///<8 ports of x2
} IDSOPT_CMN_G1_OVERRIDE;

///G2 Override
///Select configuration for PCIe link G2\nBy default is 1 port of 16 lanes. (x16)
typedef enum {
  IDSOPT_CMN_G2_OVERRIDE_AUTO = 0xf,///<Auto
  IDSOPT_CMN_G2_OVERRIDE_1PORTOFX82PORTSOFX4 = 0x9,///<1 port of x8 + 2 ports of x4
  IDSOPT_CMN_G2_OVERRIDE_1PORTOFX88PORTSOFX1 = 0xA,///<1 port of x8 + 8 ports of x1
  IDSOPT_CMN_G2_OVERRIDE_2PORTSOFX8 = 0xB,///<2 ports of x8
  IDSOPT_CMN_G2_OVERRIDE_4PORTSOFX4 = 0xC,///<4 ports of x4
  IDSOPT_CMN_G2_OVERRIDE_8PORTSOFX2 = 0xD,///<8 ports of x2
} IDSOPT_CMN_G2_OVERRIDE;

///G3 Override
///Select configuration for PCIe link G3\nBy default is 1 port of 16 lanes. (x16)
typedef enum {
  IDSOPT_CMN_G3_OVERRIDE_AUTO = 0xf,///<Auto
  IDSOPT_CMN_G3_OVERRIDE_1PORTOFX82PORTSOFX4 = 0x9,///<1 port of x8 + 2 ports of x4
  IDSOPT_CMN_G3_OVERRIDE_1PORTOFX88PORTSOFX1 = 0xA,///<1 port of x8 + 8 ports of x1
  IDSOPT_CMN_G3_OVERRIDE_2PORTSOFX8 = 0xB,///<2 ports of x8
  IDSOPT_CMN_G3_OVERRIDE_4PORTSOFX4 = 0xC,///<4 ports of x4
  IDSOPT_CMN_G3_OVERRIDE_8PORTSOFX2 = 0xD,///<8 ports of x2
} IDSOPT_CMN_G3_OVERRIDE;

///Preset Search Mask Configuration (Gen3)
///Configuration for Gen3 Preset Mask.  Select Custom to modify Gen3 Preset Search Mask.  Auto will default to platform configurations.
typedef enum {
  IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN3_CUSTOM = 0,///<Custom
  IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN3_AUTO = 0xff,///<Auto
} IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN3;

///Preset Search Mask (Gen3)
///Bit mask that when any individual bit value is 1, the corresponding Gen3 preset is included in the selection for evaluation, while a bit value of 0 excludes the corresponding Gen3 preset from evaluation.
#define IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_GEN3_MIN 0 ///< Min of Preset Search Mask (Gen3)
#define IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_GEN3_MAX 0x3ff ///< Max of Preset Search Mask (Gen3)

///Preset Search Mask Configuration (Gen4)
///Configuration for Gen4 Preset Mask.  Select Custom to modify Gen4 Preset Search Mask.  Auto will default to platform configurations.
typedef enum {
  IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN4_CUSTOM = 0,///<Custom
  IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN4_AUTO = 0xff,///<Auto
} IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN4;

///Preset Search Mask (Gen4)
///Bit mask that when any individual bit value is 1, the corresponding Gen4 preset is included in the selection for evaluation, while a bit value of 0 excludes the corresponding Gen4 preset from evaluation.
#define IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_GEN4_MIN 0 ///< Min of Preset Search Mask (Gen4)
#define IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_GEN4_MAX 0x3ff ///< Max of Preset Search Mask (Gen4)

///Preset Search Mask Configuration (Gen5)
///Configuration for Gen5 Preset Mask.  Select Custom to modify Gen5 Preset Search Mask.  Auto will default to platform configurations.
typedef enum {
  IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN5_CUSTOM = 0,///<Custom
  IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN5_AUTO = 0xff,///<Auto
} IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN5;

///Preset Search Mask (Gen5)
///Bit mask that when any individual bit value is 1, the corresponding Gen5 preset is included in the selection for evaluation, while a bit value of 0 excludes the corresponding Gen5 preset from evaluation.
#define IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_GEN5_MIN 0 ///< Min of Preset Search Mask (Gen5)
#define IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_GEN5_MAX 0x3ff ///< Max of Preset Search Mask (Gen5)

///I3C/I2C 0 Enable
///Enable or disable Inter-Integrated Circuit Control 0
typedef enum {
  IDSOPT_CMN_FCH_I3_C0_CONFIG_BOTHDISABLED = 3,///<Both Disabled
  IDSOPT_CMN_FCH_I3_C0_CONFIG_I3CENABLED = 0,///<I3C Enabled
  IDSOPT_CMN_FCH_I3_C0_CONFIG_I2CENABLED = 1,///<I2C Enabled
  IDSOPT_CMN_FCH_I3_C0_CONFIG_AUTO = 0xf,///<AUTO
} IDSOPT_CMN_FCH_I3_C0_CONFIG;

///I3C/I2C 1 Enable
///Enable or disable Inter-Integrated Circuit Control 1
typedef enum {
  IDSOPT_CMN_FCH_I3_C1_CONFIG_BOTHDISABLED = 3,///<Both Disabled
  IDSOPT_CMN_FCH_I3_C1_CONFIG_I3CENABLED = 0,///<I3C Enabled
  IDSOPT_CMN_FCH_I3_C1_CONFIG_I2CENABLED = 1,///<I2C Enabled
  IDSOPT_CMN_FCH_I3_C1_CONFIG_AUTO = 0xf,///<AUTO
} IDSOPT_CMN_FCH_I3_C1_CONFIG;

///I3C/I2C 2 Enable
///Enable or disable Inter-Integrated Circuit Control 2
typedef enum {
  IDSOPT_CMN_FCH_I3_C2_CONFIG_BOTHDISABLED = 3,///<Both Disabled
  IDSOPT_CMN_FCH_I3_C2_CONFIG_I3CENABLED = 0,///<I3C Enabled
  IDSOPT_CMN_FCH_I3_C2_CONFIG_I2CENABLED = 1,///<I2C Enabled
  IDSOPT_CMN_FCH_I3_C2_CONFIG_AUTO = 0xf,///<AUTO
} IDSOPT_CMN_FCH_I3_C2_CONFIG;

///I3C/I2C 3 Enable
///Enable or disable Inter-Integrated Circuit Control 3
typedef enum {
  IDSOPT_CMN_FCH_I3_C3_CONFIG_BOTHDISABLED = 3,///<Both Disabled
  IDSOPT_CMN_FCH_I3_C3_CONFIG_I3CENABLED = 0,///<I3C Enabled
  IDSOPT_CMN_FCH_I3_C3_CONFIG_I2CENABLED = 1,///<I2C Enabled
  IDSOPT_CMN_FCH_I3_C3_CONFIG_AUTO = 0xf,///<AUTO
} IDSOPT_CMN_FCH_I3_C3_CONFIG;

///I2C 4 Enable
///Enable or disable Inter-Integrated Circuit Controller 4
typedef enum {
  IDSOPT_CMN_FCH_I2_C4_CONFIG_DISABLED = 3,///<Disabled
  IDSOPT_CMN_FCH_I2_C4_CONFIG_ENABLED = 1,///<Enabled
  IDSOPT_CMN_FCH_I2_C4_CONFIG_AUTO = 0xf,///<AUTO
} IDSOPT_CMN_FCH_I2_C4_CONFIG;

///I2C 5 Enable
///Enable or disable  Inter-Integrated Circuit Controller 5
typedef enum {
  IDSOPT_CMN_FCH_I2_C5_CONFIG_DISABLED = 3,///<Disabled
  IDSOPT_CMN_FCH_I2_C5_CONFIG_ENABLED = 1,///<Enabled
  IDSOPT_CMN_FCH_I2_C5_CONFIG_AUTO = 0xf,///<AUTO
} IDSOPT_CMN_FCH_I2_C5_CONFIG;

///Release SPD Host Control
///Release SPD Host Control, so that BMC can take over the ownership of I2C/I3C bus
typedef enum {
  IDSOPT_CMN_FCH_RELEASE_SPD_HOST_CONTROL_DISABLED = 0,///<Disabled
  IDSOPT_CMN_FCH_RELEASE_SPD_HOST_CONTROL_ENABLED = 1,///<Enabled
} IDSOPT_CMN_FCH_RELEASE_SPD_HOST_CONTROL;

///PMFW Poll DDR5 Telemetry
///Send message to PMFW for polling DDR5 telemetry at the end of POST.
typedef enum {
  IDSOPT_CMN_FCH_PMFW_DDR5_TELEMETRY_DISABLED = 0,///<Disabled
  IDSOPT_CMN_FCH_PMFW_DDR5_TELEMETRY_ENABLED = 1,///<Enabled
} IDSOPT_CMN_FCH_PMFW_DDR5_TELEMETRY;

///Ixc Telemetry Ports Fence Control
///Controls the Fencing off for I2C/I3C ports which are involved in DDR Telemetry. If this option is enabled, then the associated Ixc ports registers will be put in the secure region. \nNote: If this option is disabled, there is a risk of collision happening between x86 accessing IxC and PMFW running DDR Telemetry which can cause undefined behavior.
typedef enum {
  IDSOPT_CMN_FCH_IXC_TELEMETRY_PORTS_FENCE_DISABLED = 0,///<Disabled
  IDSOPT_CMN_FCH_IXC_TELEMETRY_PORTS_FENCE_ENABLED = 1,///<Enabled
} IDSOPT_CMN_FCH_IXC_TELEMETRY_PORTS_FENCE;

///I2C SDA Hold Override
///Override I2C SDA_TX_HOLD and SDA_RX_HOLD
typedef enum {
  IDSOPT_CMN_FCH_I2C_SDA_HOLD_OVERRIDE_DISABLED = 0,///<Disabled
  IDSOPT_CMN_FCH_I2C_SDA_HOLD_OVERRIDE_ENABLED = 1,///<Enabled
  IDSOPT_CMN_FCH_I2C_SDA_HOLD_OVERRIDE_AUTO = 0xf,///<Auto
} IDSOPT_CMN_FCH_I2C_SDA_HOLD_OVERRIDE;

///APML SB-TSI Mode
///Select APML SB-TSI over I3C or I2C. In I3C mode, the slave controller can support both I3C and I2C(Adaptive mode).
typedef enum {
  IDSOPT_CMN_FCH_APML_SBTSI_SLV_MODE_I3C = 0,///<I3C
  IDSOPT_CMN_FCH_APML_SBTSI_SLV_MODE_I2C = 1,///<I2C
} IDSOPT_CMN_FCH_APML_SBTSI_SLV_MODE;

///I3C Mode Speed
///I3C Transfer Speed
typedef enum {
  IDSOPT_CMN_FCH_I3C_MODE_SPEED_SDR26MHZ = 0x2,///<SDR2 (6 MHz)
  IDSOPT_CMN_FCH_I3C_MODE_SPEED_AUTO = 0xf,///<Auto
} IDSOPT_CMN_FCH_I3C_MODE_SPEED;

///I3C Push Pull HCNT Value
///SCL push-pull High count for I3C transfers targeted to I3C devices.
#define IDSOPT_CMN_FCH_I3C_PP_HCNT_VALUE_MIN 0x08 ///< Min of I3C Push Pull HCNT Value
#define IDSOPT_CMN_FCH_I3C_PP_HCNT_VALUE_MAX 0x0D ///< Max of I3C Push Pull HCNT Value

///I3C SDA Hold Value
///I3C SDA Hold Value
#define IDSOPT_CMN_FCH_I3C_SDA_HOLD_VALUE_MIN 0x1 ///< Min of I3C SDA Hold Value
#define IDSOPT_CMN_FCH_I3C_SDA_HOLD_VALUE_MAX 0x7 ///< Max of I3C SDA Hold Value

///I3C SDA Hold Override
///Override I3C SDA HOLD VALUE
typedef enum {
  IDSOPT_CMN_FCH_I3C_SDA_HOLD_OVERRIDE_DISABLED = 0,///<Disabled
  IDSOPT_CMN_FCH_I3C_SDA_HOLD_OVERRIDE_ENABLED = 1,///<Enabled
  IDSOPT_CMN_FCH_I3C_SDA_HOLD_OVERRIDE_AUTO = 0xf,///<Auto
} IDSOPT_CMN_FCH_I3C_SDA_HOLD_OVERRIDE;

///I2C 0 SDA TX HOLD VALUE
///I2C0 IC SDA TX HOLD value
#define IDSOPT_CMN_FCH_I2C0_SDA_TX_HOLD_VALUE_MIN 0x1 ///< Min of I2C 0 SDA TX HOLD VALUE
#define IDSOPT_CMN_FCH_I2C0_SDA_TX_HOLD_VALUE_MAX 0xFFFF ///< Max of I2C 0 SDA TX HOLD VALUE

///I2C 1 SDA TX HOLD VALUE
///I2C1 IC SDA TX HOLD value
#define IDSOPT_CMN_FCH_I2C1_SDA_TX_HOLD_VALUE_MIN 0x1 ///< Min of I2C 1 SDA TX HOLD VALUE
#define IDSOPT_CMN_FCH_I2C1_SDA_TX_HOLD_VALUE_MAX 0xFFFF ///< Max of I2C 1 SDA TX HOLD VALUE

///I2C 2 SDA TX HOLD VALUE
///I2C2 IC SDA TX HOLD value
#define IDSOPT_CMN_FCH_I2C2_SDA_TX_HOLD_VALUE_MIN 0x1 ///< Min of I2C 2 SDA TX HOLD VALUE
#define IDSOPT_CMN_FCH_I2C2_SDA_TX_HOLD_VALUE_MAX 0xFFFF ///< Max of I2C 2 SDA TX HOLD VALUE

///I2C 3 SDA TX HOLD VALUE
///I2C3 IC SDA TX HOLD value
#define IDSOPT_CMN_FCH_I2C3_SDA_TX_HOLD_VALUE_MIN 0x1 ///< Min of I2C 3 SDA TX HOLD VALUE
#define IDSOPT_CMN_FCH_I2C3_SDA_TX_HOLD_VALUE_MAX 0xFFFF ///< Max of I2C 3 SDA TX HOLD VALUE

///I2C 4 SDA TX HOLD VALUE
///I2C4 IC SDA TX HOLD value
#define IDSOPT_CMN_FCH_I2C4_SDA_TX_HOLD_VALUE_MIN 0x1 ///< Min of I2C 4 SDA TX HOLD VALUE
#define IDSOPT_CMN_FCH_I2C4_SDA_TX_HOLD_VALUE_MAX 0xFFFF ///< Max of I2C 4 SDA TX HOLD VALUE

///I2C 5 SDA TX HOLD VALUE
///I2C5 IC SDA TX HOLD value
#define IDSOPT_CMN_FCH_I2C5_SDA_TX_HOLD_VALUE_MIN 0x1 ///< Min of I2C 5 SDA TX HOLD VALUE
#define IDSOPT_CMN_FCH_I2C5_SDA_TX_HOLD_VALUE_MAX 0xFFFF ///< Max of I2C 5 SDA TX HOLD VALUE

///I2C 0 SDA RX HOLD VALUE
///I2C0 IC SDA RX HOLD value
#define IDSOPT_CMN_FCH_I2C0_SDA_RX_HOLD_VALUE_MIN 0x00 ///< Min of I2C 0 SDA RX HOLD VALUE
#define IDSOPT_CMN_FCH_I2C0_SDA_RX_HOLD_VALUE_MAX 0xFF ///< Max of I2C 0 SDA RX HOLD VALUE

///I2C 1 SDA RX HOLD VALUE
///I2C1 IC SDA RX HOLD value
#define IDSOPT_CMN_FCH_I2C1_SDA_RX_HOLD_VALUE_MIN 0x00 ///< Min of I2C 1 SDA RX HOLD VALUE
#define IDSOPT_CMN_FCH_I2C1_SDA_RX_HOLD_VALUE_MAX 0xFF ///< Max of I2C 1 SDA RX HOLD VALUE

///I2C 2 SDA RX HOLD VALUE
///I2C2 IC SDA RX HOLD value
#define IDSOPT_CMN_FCH_I2C2_SDA_RX_HOLD_VALUE_MIN 0x00 ///< Min of I2C 2 SDA RX HOLD VALUE
#define IDSOPT_CMN_FCH_I2C2_SDA_RX_HOLD_VALUE_MAX 0xFF ///< Max of I2C 2 SDA RX HOLD VALUE

///I2C 3 SDA RX HOLD VALUE
///I2C3 IC SDA RX HOLD value
#define IDSOPT_CMN_FCH_I2C3_SDA_RX_HOLD_VALUE_MIN 0x00 ///< Min of I2C 3 SDA RX HOLD VALUE
#define IDSOPT_CMN_FCH_I2C3_SDA_RX_HOLD_VALUE_MAX 0xFF ///< Max of I2C 3 SDA RX HOLD VALUE

///I2C 4 SDA RX HOLD VALUE
///I2C4 IC SDA RX HOLD value
#define IDSOPT_CMN_FCH_I2C4_SDA_RX_HOLD_VALUE_MIN 0x00 ///< Min of I2C 4 SDA RX HOLD VALUE
#define IDSOPT_CMN_FCH_I2C4_SDA_RX_HOLD_VALUE_MAX 0xFF ///< Max of I2C 4 SDA RX HOLD VALUE

///I2C 5 SDA RX HOLD VALUE
///I2C5 IC SDA RX HOLD value
#define IDSOPT_CMN_FCH_I2C5_SDA_RX_HOLD_VALUE_MIN 0x00 ///< Min of I2C 5 SDA RX HOLD VALUE
#define IDSOPT_CMN_FCH_I2C5_SDA_RX_HOLD_VALUE_MAX 0xFF ///< Max of I2C 5 SDA RX HOLD VALUE

///I3C 0 SDA HOLD VALUE
///I3C 0 SDA Hold Value
#define IDSOPT_CMN_FCH_I3C0_SDA_HOLD_VALUE_MIN 0x1 ///< Min of I3C 0 SDA HOLD VALUE
#define IDSOPT_CMN_FCH_I3C0_SDA_HOLD_VALUE_MAX 0x7 ///< Max of I3C 0 SDA HOLD VALUE

///I3C 1 SDA HOLD VALUE
///I3C 1 SDA Hold Value
#define IDSOPT_CMN_FCH_I3C1_SDA_HOLD_VALUE_MIN 0x1 ///< Min of I3C 1 SDA HOLD VALUE
#define IDSOPT_CMN_FCH_I3C1_SDA_HOLD_VALUE_MAX 0x7 ///< Max of I3C 1 SDA HOLD VALUE

///I3C 2 SDA HOLD VALUE
///I3C 2 SDA Hold Value
#define IDSOPT_CMN_FCH_I3C2_SDA_HOLD_VALUE_MIN 0x1 ///< Min of I3C 2 SDA HOLD VALUE
#define IDSOPT_CMN_FCH_I3C2_SDA_HOLD_VALUE_MAX 0x7 ///< Max of I3C 2 SDA HOLD VALUE

///I3C 3 SDA HOLD VALUE
///I3C 3 SDA Hold Value
#define IDSOPT_CMN_FCH_I3C3_SDA_HOLD_VALUE_MIN 0x1 ///< Min of I3C 3 SDA HOLD VALUE
#define IDSOPT_CMN_FCH_I3C3_SDA_HOLD_VALUE_MAX 0x7 ///< Max of I3C 3 SDA HOLD VALUE

///SATA Enable
///Disable or enable OnChip SATA controller
typedef enum {
  IDSOPT_CMN_FCH_SATA_ENABLE_DISABLED = 0,///<Disabled
  IDSOPT_CMN_FCH_SATA_ENABLE_ENABLED = 1,///<Enabled
  IDSOPT_CMN_FCH_SATA_ENABLE_AUTO = 0xf,///<Auto
} IDSOPT_CMN_FCH_SATA_ENABLE;

///SATA Mode
///Select OnChip SATA Type
typedef enum {
  IDSOPT_CMN_FCH_SATA_CLASS_AHCI = 2,///<AHCI
  IDSOPT_CMN_FCH_SATA_CLASS_AHCIASID0X7904 = 5,///<AHCI as ID 0x7904
  IDSOPT_CMN_FCH_SATA_CLASS_AUTO = 0xf,///<Auto
} IDSOPT_CMN_FCH_SATA_CLASS;

///SATA RAS Support
///Disable or enable Sata RAS Support
typedef enum {
  IDSOPT_CMN_FCH_SATA_RAS_SUPPORT_DISABLED = 0,///<Disabled
  IDSOPT_CMN_FCH_SATA_RAS_SUPPORT_ENABLED = 1,///<Enabled
  IDSOPT_CMN_FCH_SATA_RAS_SUPPORT_AUTO = 0xf,///<Auto
} IDSOPT_CMN_FCH_SATA_RAS_SUPPORT;

///SATA Staggered Spin-up
///Enable or disable SATA staggered spin-up.
typedef enum {
  IDSOPT_CMN_FCH_SATA_STAGGERED_SPINUP_DISABLED = 0,///<Disabled
  IDSOPT_CMN_FCH_SATA_STAGGERED_SPINUP_ENABLED = 1,///<Enabled
  IDSOPT_CMN_FCH_SATA_STAGGERED_SPINUP_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_FCH_SATA_STAGGERED_SPINUP;

///SATA Disabled AHCI Prefetch Function
///Disable or enable Sata Disabled AHCI Prefetch Function
typedef enum {
  IDSOPT_CMN_FCH_SATA_AHCI_DIS_PREFETCH_FUNCTION_DISABLED = 0,///<Disabled
  IDSOPT_CMN_FCH_SATA_AHCI_DIS_PREFETCH_FUNCTION_ENABLED = 1,///<Enabled
  IDSOPT_CMN_FCH_SATA_AHCI_DIS_PREFETCH_FUNCTION_AUTO = 0xf,///<Auto
} IDSOPT_CMN_FCH_SATA_AHCI_DIS_PREFETCH_FUNCTION;

///Sata0 Enable
///Enable or Disable Sata0.\nEach IOD has 4 Sata Controllers.
typedef enum {
  IDSOPT_DBG_FCH_SATA0_ENABLE_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA0_ENABLE_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA0_ENABLE_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA0_ENABLE;

///Sata1 Enable
///Enable or Disable Sata1.\nEach IOD has 4 Sata Controllers.
typedef enum {
  IDSOPT_DBG_FCH_SATA1_ENABLE_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA1_ENABLE_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA1_ENABLE_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA1_ENABLE;

///Sata2 Enable
///Enable or Disable Sata2.\nEach IOD has 4 Sata Controllers.
typedef enum {
  IDSOPT_DBG_FCH_SATA2_ENABLE_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA2_ENABLE_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA2_ENABLE_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA2_ENABLE;

///Sata3 Enable
///Enable or Disable Sata3.\nEach IOD has 4 Sata Controllers.
typedef enum {
  IDSOPT_DBG_FCH_SATA3_ENABLE_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA3_ENABLE_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA3_ENABLE_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA3_ENABLE;

///Sata4 (Socket1) Enable
///Enable or Disable Sata4 on Socket 1 (IOD1)..\nEach IOD has 4 Sata Controllers.
typedef enum {
  IDSOPT_DBG_FCH_SATA4_ENABLE_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA4_ENABLE_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA4_ENABLE_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA4_ENABLE;

///Sata5 (Socket1) Enable
///Enable or Disable Sata5 on Socket 1 (IOD1)..\nEach IOD has 4 Sata Controllers.
typedef enum {
  IDSOPT_DBG_FCH_SATA5_ENABLE_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA5_ENABLE_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA5_ENABLE_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA5_ENABLE;

///Sata6 (Socket1) Enable
///Enable or Disable Sata6 on Socket 1 (IOD1)..\nEach IOD has 4 Sata Controllers.
typedef enum {
  IDSOPT_DBG_FCH_SATA6_ENABLE_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA6_ENABLE_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA6_ENABLE_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA6_ENABLE;

///Sata7 (Socket1) Enable
///Enable or Disable Sata7 on Socket 1 (IOD1)..\nEach IOD has 4 Sata Controllers.
typedef enum {
  IDSOPT_DBG_FCH_SATA7_ENABLE_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA7_ENABLE_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA7_ENABLE_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA7_ENABLE;

///Sata0 eSATA Port0
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATAE_SATA_PORT0_ISATA = 0,///<iSATA
  IDSOPT_DBG_FCH_SATAE_SATA_PORT0_ESATA = 1,///<eSATA
  IDSOPT_DBG_FCH_SATAE_SATA_PORT0_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATAE_SATA_PORT0;

///Sata0 eSATA Port1
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATAE_SATA_PORT1_ISATA = 0,///<iSATA
  IDSOPT_DBG_FCH_SATAE_SATA_PORT1_ESATA = 1,///<eSATA
  IDSOPT_DBG_FCH_SATAE_SATA_PORT1_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATAE_SATA_PORT1;

///Sata0 eSATA Port2
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATAE_SATA_PORT2_ISATA = 0,///<iSATA
  IDSOPT_DBG_FCH_SATAE_SATA_PORT2_ESATA = 1,///<eSATA
  IDSOPT_DBG_FCH_SATAE_SATA_PORT2_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATAE_SATA_PORT2;

///Sata0 eSATA Port3
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATAE_SATA_PORT3_ISATA = 0,///<iSATA
  IDSOPT_DBG_FCH_SATAE_SATA_PORT3_ESATA = 1,///<eSATA
  IDSOPT_DBG_FCH_SATAE_SATA_PORT3_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATAE_SATA_PORT3;

///Sata0 eSATA Port4
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATAE_SATA_PORT4_ISATA = 0,///<iSATA
  IDSOPT_DBG_FCH_SATAE_SATA_PORT4_ESATA = 1,///<eSATA
  IDSOPT_DBG_FCH_SATAE_SATA_PORT4_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATAE_SATA_PORT4;

///Sata0 eSATA Port5
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATAE_SATA_PORT5_ISATA = 0,///<iSATA
  IDSOPT_DBG_FCH_SATAE_SATA_PORT5_ESATA = 1,///<eSATA
  IDSOPT_DBG_FCH_SATAE_SATA_PORT5_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATAE_SATA_PORT5;

///Sata0 eSATA Port6
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATAE_SATA_PORT6_ISATA = 0,///<iSATA
  IDSOPT_DBG_FCH_SATAE_SATA_PORT6_ESATA = 1,///<eSATA
  IDSOPT_DBG_FCH_SATAE_SATA_PORT6_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATAE_SATA_PORT6;

///Sata0 eSATA Port7
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATAE_SATA_PORT7_ISATA = 0,///<iSATA
  IDSOPT_DBG_FCH_SATAE_SATA_PORT7_ESATA = 1,///<eSATA
  IDSOPT_DBG_FCH_SATAE_SATA_PORT7_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATAE_SATA_PORT7;

///Sata1 eSATA Port0
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT0_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT0_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT0_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT0;

///Sata1 eSATA Port1
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT1_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT1_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT1_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT1;

///Sata1 eSATA Port2
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT2_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT2_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT2_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT2;

///Sata1 eSATA Port3
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT3_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT3_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT3_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT3;

///Sata1 eSATA Port4
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT4_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT4_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT4_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT4;

///Sata1 eSATA Port5
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT5_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT5_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT5_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT5;

///Sata1 eSATA Port6
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT6_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT6_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT6_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT6;

///Sata1 eSATA Port7
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT7_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT7_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT7_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT7;

///Sata2 eSATA Port0
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT0_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT0_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT0_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT0;

///Sata2 eSATA Port1
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT1_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT1_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT1_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT1;

///Sata2 eSATA Port2
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT2_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT2_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT2_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT2;

///Sata2 eSATA Port3
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT3_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT3_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT3_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT3;

///Sata2 eSATA Port4
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT4_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT4_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT4_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT4;

///Sata2 eSATA Port5
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT5_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT5_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT5_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT5;

///Sata2 eSATA Port6
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT6_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT6_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT6_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT6;

///Sata2 eSATA Port7
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT7_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT7_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT7_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT7;

///Sata3 eSATA Port0
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT0_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT0_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT0_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT0;

///Sata3 eSATA Port1
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT1_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT1_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT1_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT1;

///Sata3 eSATA Port2
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT2_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT2_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT2_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT2;

///Sata3 eSATA Port3
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT3_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT3_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT3_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT3;

///Sata3 eSATA Port4
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT4_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT4_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT4_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT4;

///Sata3 eSATA Port5
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT5_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT5_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT5_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT5;

///Sata3 eSATA Port6
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT6_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT6_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT6_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT6;

///Sata3 eSATA Port7
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT7_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT7_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT7_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT7;

///Sata4 eSATA Port0
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT0_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT0_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT0_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT0;

///Sata4 eSATA Port1
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT1_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT1_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT1_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT1;

///Sata4 eSATA Port2
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT2_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT2_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT2_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT2;

///Sata4 eSATA Port3
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT3_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT3_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT3_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT3;

///Sata4 eSATA Port4
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT4_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT4_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT4_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT4;

///Sata4 eSATA Port5
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT5_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT5_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT5_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT5;

///Sata4 eSATA Port6
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT6_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT6_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT6_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT6;

///Sata4 eSATA Port7
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT7_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT7_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT7_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT7;

///Sata5 eSATA Port0
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT0_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT0_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT0_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT0;

///Sata5 eSATA Port1
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT1_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT1_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT1_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT1;

///Sata5 eSATA Port2
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT2_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT2_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT2_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT2;

///Sata5 eSATA Port3
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT3_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT3_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT3_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT3;

///Sata5 eSATA Port4
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT4_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT4_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT4_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT4;

///Sata5 eSATA Port5
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT5_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT5_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT5_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT5;

///Sata5 eSATA Port6
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT6_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT6_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT6_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT6;

///Sata5 eSATA Port7
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT7_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT7_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT7_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT7;

///Sata6 eSATA Port0
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT0_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT0_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT0_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT0;

///Sata6 eSATA Port1
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT1_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT1_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT1_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT1;

///Sata6 eSATA Port2
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT2_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT2_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT2_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT2;

///Sata6 eSATA Port3
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT3_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT3_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT3_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT3;

///Sata6 eSATA Port4
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT4_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT4_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT4_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT4;

///Sata6 eSATA Port5
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT5_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT5_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT5_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT5;

///Sata6 eSATA Port6
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT6_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT6_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT6_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT6;

///Sata6 eSATA Port7
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT7_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT7_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT7_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT7;

///Sata7 eSATA Port0
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT0_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT0_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT0_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT0;

///Sata7 eSATA Port1
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT1_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT1_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT1_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT1;

///Sata7 eSATA Port2
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT2_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT2_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT2_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT2;

///Sata7 eSATA Port3
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT3_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT3_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT3_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT3;

///Sata7 eSATA Port4
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT4_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT4_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT4_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT4;

///Sata7 eSATA Port5
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT5_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT5_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT5_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT5;

///Sata7 eSATA Port6
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT6_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT6_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT6_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT6;

///Sata7 eSATA Port7
///External SATA Port support
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT7_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT7_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT7_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT7;

///Socket0 DevSlp0 Enable
///Enable socket 0 DevSlp0. In SOC two DEVSLP pads are assigned.\nAggressive Device Sleep enables the HBA to assert the DEVSLP signal as soon as there are no commands outstanding to the device and the port specific Device Sleep idle timer has expired.\n
typedef enum {
  IDSOPT_DBG_FCH_SATA_AGGRESIVE_DEV_SLP_P0_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_AGGRESIVE_DEV_SLP_P0_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_AGGRESIVE_DEV_SLP_P0_AUTO = 0xF,///<Auto
} IDSOPT_DBG_FCH_SATA_AGGRESIVE_DEV_SLP_P0;

///Socket0 DevSlp0 Controller Number
///SATA controller number to be supported with DEVSLP0. Range from 0 to 3.
#define IDSOPT_DBG_FCH_SATA_DEV_SLP_CONTROLLER0_NUM_MIN 0 ///< Min of Socket0 DevSlp0 Controller Number
#define IDSOPT_DBG_FCH_SATA_DEV_SLP_CONTROLLER0_NUM_MAX 3 ///< Max of Socket0 DevSlp0 Controller Number

///Socket0 DevSlp0 Port Number
///SATA port number to be supported with DEVSLP0. Range from 0 to 7.
#define IDSOPT_DBG_FCH_SATA_DEV_SLP_PORT0_NUM_MIN 0 ///< Min of Socket0 DevSlp0 Port Number
#define IDSOPT_DBG_FCH_SATA_DEV_SLP_PORT0_NUM_MAX 7 ///< Max of Socket0 DevSlp0 Port Number

///Socket0 DevSlp1 Enable
///Enable Socket 0 DevSlp1. In SOC two DEVSLP pads are assigned.\nAggressive Device Sleep enables the HBA to assert the DEVSLP signal as soon as there are no commands outstanding to the device and the port specific Device Sleep idle timer has expired.
typedef enum {
  IDSOPT_DBG_FCH_SATA_AGGRESIVE_DEV_SLP_P1_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_AGGRESIVE_DEV_SLP_P1_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_AGGRESIVE_DEV_SLP_P1_AUTO = 0xF,///<Auto
} IDSOPT_DBG_FCH_SATA_AGGRESIVE_DEV_SLP_P1;

///Socket0 DevSlp1 Controller Number
///SATA controller number to be supported with DEVSLP1. Range from 0 to 3.
#define IDSOPT_DBG_FCH_SATA_DEV_SLP_CONTROLLER1_NUM_MIN 0 ///< Min of Socket0 DevSlp1 Controller Number
#define IDSOPT_DBG_FCH_SATA_DEV_SLP_CONTROLLER1_NUM_MAX 3 ///< Max of Socket0 DevSlp1 Controller Number

///Socket0 DevSlp1 Port Number
///SATA port number to be supported with DEVSLP1. Range from 0 to 7.
#define IDSOPT_DBG_FCH_SATA_DEV_SLP_PORT1_NUM_MIN 0 ///< Min of Socket0 DevSlp1 Port Number
#define IDSOPT_DBG_FCH_SATA_DEV_SLP_PORT1_NUM_MAX 7 ///< Max of Socket0 DevSlp1 Port Number

///Socket1 DevSlp0 Enable
///Enable socket 1 DevSlp0. In SOC two DEVSLP pads are assigned.\nAggressive Device Sleep enables the HBA to assert the DEVSLP signal as soon as there are no commands outstanding to the device and the port specific Device Sleep idle timer has expired.\n
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP0_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP0_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP0_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP0;

///Socket1 DevSlp0 Controller Number
///SATA controller number to be supported with DEVSLP0. Range from 4 to 7.
#define IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP_CONTROLLER0_NUM_MIN 4 ///< Min of Socket1 DevSlp0 Controller Number
#define IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP_CONTROLLER0_NUM_MAX 7 ///< Max of Socket1 DevSlp0 Controller Number

///Socket1 DevSlp0 Port Number
///SATA port number to be supported with DEVSLP0. Range from 0 to 7.
#define IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP0_NUM_MIN 0 ///< Min of Socket1 DevSlp0 Port Number
#define IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP0_NUM_MAX 7 ///< Max of Socket1 DevSlp0 Port Number

///Socket1 DevSlp1 Enable
///Enable socket 1 DevSlp0. In SOC two DEVSLP pads are assigned.\nAggressive Device Sleep enables the HBA to assert the DEVSLP signal as soon as there are no commands outstanding to the device and the port specific Device Sleep idle timer has expired.\n
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP1_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP1_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP1_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP1;

///Socket1 DevSlp1 Controller Number
///SATA controller number to be supported with DEVSLP0. Range from 4 to 7.
#define IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP_CONTROLLER1_NUM_MIN 4 ///< Min of Socket1 DevSlp1 Controller Number
#define IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP_CONTROLLER1_NUM_MAX 7 ///< Max of Socket1 DevSlp1 Controller Number

///Socket1 DevSlp1 Port Number
///SATA port number to be supported with DEVSLP0. Range from 0 to 7.
#define IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP1_NUM_MIN 0 ///< Min of Socket1 DevSlp1 Port Number
#define IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP1_NUM_MAX 7 ///< Max of Socket1 DevSlp1 Port Number

///Sata0 SGPIO
///Enable or Disable SataSgpio on Sata0
typedef enum {
  IDSOPT_DBG_FCH_SATA_SGPIO0_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_SGPIO0_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_SGPIO0_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_SGPIO0;

///Sata1 SGPIO
///Enable or Disable SataSgpio on Sata1
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_SGPIO0_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_SGPIO0_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE1_SGPIO0_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE1_SGPIO0;

///Sata2 SGPIO
///Enable or Disable SataSgpio on Sata2
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_SGPIO0_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_SGPIO0_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE2_SGPIO0_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE2_SGPIO0;

///Sata3 SGPIO
///Enable or Disable SataSgpio on Sata3
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_SGPIO0_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_SGPIO0_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE3_SGPIO0_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE3_SGPIO0;

///Sata4 SGPIO
///Enable or Disable SataSgpio on Sata4 (Socket1)
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_SGPIO0_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_SGPIO0_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE4_SGPIO0_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE4_SGPIO0;

///Sata5 SGPIO
///Enable or Disable SataSgpio on Sata5 (Socket1)
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_SGPIO0_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_SGPIO0_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE5_SGPIO0_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE5_SGPIO0;

///Sata6 SGPIO
///Enable or Disable SataSgpio on Sata6 (Socket1)
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_SGPIO0_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_SGPIO0_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE6_SGPIO0_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE6_SGPIO0;

///Sata7 SGPIO
///Enable or Disable SataSgpio on Sata7 (Socket1)
typedef enum {
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_SGPIO0_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_SGPIO0_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SATA_MCM_DIE7_SGPIO0_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SATA_MCM_DIE7_SGPIO0;

///XHCI Controller0 enable
///Enable or disable USB3 controller.
typedef enum {
  IDSOPT_CMN_FCH_USB_XHC_I0_ENABLE_ENABLED = 1,///<Enabled
  IDSOPT_CMN_FCH_USB_XHC_I0_ENABLE_DISABLED = 0,///<Disabled
  IDSOPT_CMN_FCH_USB_XHC_I0_ENABLE_AUTO = 0xf,///<Auto
} IDSOPT_CMN_FCH_USB_XHC_I0_ENABLE;

///XHCI Controller1 enable
///Enable or disable USB3 controller.
typedef enum {
  IDSOPT_CMN_FCH_USB_XHC_I1_ENABLE_ENABLED = 1,///<Enabled
  IDSOPT_CMN_FCH_USB_XHC_I1_ENABLE_DISABLED = 0,///<Disabled
  IDSOPT_CMN_FCH_USB_XHC_I1_ENABLE_AUTO = 0xf,///<Auto
} IDSOPT_CMN_FCH_USB_XHC_I1_ENABLE;

///XHCI2 enable (Socket1)
///Enable or disable USB3 controller.
typedef enum {
  IDSOPT_CMN_FCH_USB_XHC_I2_ENABLE_ENABLED = 1,///<Enabled
  IDSOPT_CMN_FCH_USB_XHC_I2_ENABLE_DISABLED = 0,///<Disabled
  IDSOPT_CMN_FCH_USB_XHC_I2_ENABLE_AUTO = 0xf,///<Auto
} IDSOPT_CMN_FCH_USB_XHC_I2_ENABLE;

///XHCI3 enable (Socket1)
///Enable or disable USB3 controller.
typedef enum {
  IDSOPT_CMN_FCH_USB_XHC_I3_ENABLE_ENABLED = 1,///<Enabled
  IDSOPT_CMN_FCH_USB_XHC_I3_ENABLE_DISABLED = 0,///<Disabled
  IDSOPT_CMN_FCH_USB_XHC_I3_ENABLE_AUTO = 0xf,///<Auto
} IDSOPT_CMN_FCH_USB_XHC_I3_ENABLE;

///Ac Loss Control
///Select Ac Loss Control Method
typedef enum {
  IDSOPT_CMN_FCH_SYSTEM_PWR_FAIL_SHADOW_ALWAYSOFF = 0,///<Always Off
  IDSOPT_CMN_FCH_SYSTEM_PWR_FAIL_SHADOW_ALWAYSON = 1,///<Always On
  IDSOPT_CMN_FCH_SYSTEM_PWR_FAIL_SHADOW_RESERVED = 2,///<Reserved
  IDSOPT_CMN_FCH_SYSTEM_PWR_FAIL_SHADOW_PREVIOUS = 3,///<Previous
  IDSOPT_CMN_FCH_SYSTEM_PWR_FAIL_SHADOW_AUTO = 0xf,///<Auto
} IDSOPT_CMN_FCH_SYSTEM_PWR_FAIL_SHADOW;

///Set Fch Power failed Shadow in ABL
///Enable/Disable set FCH power failed shadow by Ac Loss control policy in ABL
typedef enum {
  IDSOPT_CMN_FCH_PWR_FAIL_SHADOW_ABL_ENABLED_ENABLED = 1,///<Enabled
  IDSOPT_CMN_FCH_PWR_FAIL_SHADOW_ABL_ENABLED_DISABLED = 0,///<Disabled
  IDSOPT_CMN_FCH_PWR_FAIL_SHADOW_ABL_ENABLED_AUTO = 0xF,///<Auto
} IDSOPT_CMN_FCH_PWR_FAIL_SHADOW_ABL_ENABLED;

///Uart 0 Enable
///Enable or disable Uart0.\nUart 0 has no HW flow control if Uart 2 is enabled
typedef enum {
  IDSOPT_CMN_FCH_UART0_CONFIG_DISABLED = 0,///<Disabled
  IDSOPT_CMN_FCH_UART0_CONFIG_ENABLED = 1,///<Enabled
  IDSOPT_CMN_FCH_UART0_CONFIG_AUTO = 0xf,///<AUTO
} IDSOPT_CMN_FCH_UART0_CONFIG;

///Uart 0 Legacy Options
///Assign Uart0 to receive one of the I/O addresses in 0x3F8-0x3FF, 0x3E8-0x3EF, 0x2F8-0x2FF and 0x2E8-0x2EF.
typedef enum {
  IDSOPT_CMN_FCH_UART0_LEGACY_CONFIG_DISABLED = 0,///<Disabled
  IDSOPT_CMN_FCH_UART0_LEGACY_CONFIG_0X2E8 = 1,///<0x2E8
  IDSOPT_CMN_FCH_UART0_LEGACY_CONFIG_0X2F8 = 2,///<0x2F8
  IDSOPT_CMN_FCH_UART0_LEGACY_CONFIG_0X3E8 = 3,///<0x3E8
  IDSOPT_CMN_FCH_UART0_LEGACY_CONFIG_0X3F8 = 4,///<0x3F8
  IDSOPT_CMN_FCH_UART0_LEGACY_CONFIG_AUTO = 0xf,///<AUTO
} IDSOPT_CMN_FCH_UART0_LEGACY_CONFIG;

///Uart 1 Enable
///Enable or disable Uart1.
typedef enum {
  IDSOPT_CMN_FCH_UART1_CONFIG_DISABLED = 0,///<Disabled
  IDSOPT_CMN_FCH_UART1_CONFIG_ENABLED = 1,///<Enabled
  IDSOPT_CMN_FCH_UART1_CONFIG_AUTO = 0xf,///<AUTO
} IDSOPT_CMN_FCH_UART1_CONFIG;

///Uart 1 Legacy Options
///Assign Uart1 to receive one of the I/O addresses in 0x3F8-0x3FF, 0x3E8-0x3EF, 0x2F8-0x2FF and 0x2E8-0x2EF.
typedef enum {
  IDSOPT_CMN_FCH_UART1_LEGACY_CONFIG_DISABLED = 0,///<Disabled
  IDSOPT_CMN_FCH_UART1_LEGACY_CONFIG_0X2E8 = 1,///<0x2E8
  IDSOPT_CMN_FCH_UART1_LEGACY_CONFIG_0X2F8 = 2,///<0x2F8
  IDSOPT_CMN_FCH_UART1_LEGACY_CONFIG_0X3E8 = 3,///<0x3E8
  IDSOPT_CMN_FCH_UART1_LEGACY_CONFIG_0X3F8 = 4,///<0x3F8
  IDSOPT_CMN_FCH_UART1_LEGACY_CONFIG_AUTO = 0xf,///<AUTO
} IDSOPT_CMN_FCH_UART1_LEGACY_CONFIG;

///Uart 2 Enable
///Enable or disable Uart2.\nIf Uart 2 is enable, Uart0 has no HW flow control.
typedef enum {
  IDSOPT_CMN_FCH_UART2_CONFIG_DISABLED = 0,///<Disabled
  IDSOPT_CMN_FCH_UART2_CONFIG_ENABLED = 1,///<Enabled
  IDSOPT_CMN_FCH_UART2_CONFIG_AUTO = 0xf,///<AUTO
} IDSOPT_CMN_FCH_UART2_CONFIG;

///Uart 2 Legacy Options
///Assign Uart2 to receive one of the I/O addresses in 0x3F8-0x3FF, 0x3E8-0x3EF, 0x2F8-0x2FF and 0x2E8-0x2EF.
typedef enum {
  IDSOPT_CMN_FCH_UART2_LEGACY_CONFIG_DISABLED = 0,///<Disabled
  IDSOPT_CMN_FCH_UART2_LEGACY_CONFIG_0X2E8 = 1,///<0x2E8
  IDSOPT_CMN_FCH_UART2_LEGACY_CONFIG_0X2F8 = 2,///<0x2F8
  IDSOPT_CMN_FCH_UART2_LEGACY_CONFIG_0X3E8 = 3,///<0x3E8
  IDSOPT_CMN_FCH_UART2_LEGACY_CONFIG_0X3F8 = 4,///<0x3F8
  IDSOPT_CMN_FCH_UART2_LEGACY_CONFIG_AUTO = 0xf,///<AUTO
} IDSOPT_CMN_FCH_UART2_LEGACY_CONFIG;

///ALink RAS Support
///Enable FCH A-Link parity error
typedef enum {
  IDSOPT_CMN_FCH_ALINK_RAS_SUPPORT_DISABLED = 0,///<Disabled
  IDSOPT_CMN_FCH_ALINK_RAS_SUPPORT_ENABLED = 1,///<Enabled
  IDSOPT_CMN_FCH_ALINK_RAS_SUPPORT_AUTO = 0xf,///<Auto
} IDSOPT_CMN_FCH_ALINK_RAS_SUPPORT;

///Reset After Sync-Flood
///Enable AB to forward downstream sync-flood message to system\ncontroller.
typedef enum {
  IDSOPT_DBG_FCH_SYNCFLOOD_ENABLE_ENABLE = 1,///<Enable
  IDSOPT_DBG_FCH_SYNCFLOOD_ENABLE_DISABLE = 0,///<Disable
  IDSOPT_DBG_FCH_SYNCFLOOD_ENABLE_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SYNCFLOOD_ENABLE;

///Delay Reset After Sync-Flood
///This control delay time for system reset when a Sync Flood is detected.\ndelayed minutes  rang from 5  to 255 minutes \n 0 ~ 4 : disabled.
#define IDSOPT_DBG_FCH_DELAY_SYNCFLOOD_MIN 0 ///< Min of Delay Reset After Sync-Flood
#define IDSOPT_DBG_FCH_DELAY_SYNCFLOOD_MAX 255 ///< Max of Delay Reset After Sync-Flood

///FCH Spread Spectrum
///Select whether or not Enable the Spread Spectrum Feature
typedef enum {
  IDSOPT_DBG_FCH_SYSTEM_SPREAD_SPECTRUM_DISABLED = 0,///<Disabled
  IDSOPT_DBG_FCH_SYSTEM_SPREAD_SPECTRUM_ENABLED = 1,///<Enabled
  IDSOPT_DBG_FCH_SYSTEM_SPREAD_SPECTRUM_AUTO = 0xf,///<Auto
} IDSOPT_DBG_FCH_SYSTEM_SPREAD_SPECTRUM;

///Boot Timer Enable
///Boot Timer enable.\nEnable : force PMx44 bit 27 = 1\nDisable : force PMx44 bit 27 = 0\nAuto:PMx44 bit 27 = PcdBootTimerEnable
typedef enum {
  IDSOPT_CMN_BOOT_TIMER_ENABLE_DISABLED = 0,///<Disabled
  IDSOPT_CMN_BOOT_TIMER_ENABLE_ENABLED = 1,///<Enabled
  IDSOPT_CMN_BOOT_TIMER_ENABLE_AUTO = 2,///<Auto
} IDSOPT_CMN_BOOT_TIMER_ENABLE;

///Socket-0 P0 NTB Enable
///Enable NTB on Socket-0 P0 Link
typedef enum {
  IDSOPT_CMN_S_P3_NTB_P0_P0_AUTO = 0xF,///<Auto
  IDSOPT_CMN_S_P3_NTB_P0_P0_ENABLE = 1,///<Enable
  IDSOPT_CMN_S_P3_NTB_P0_P0_DISABLE = 0,///<Disable
} IDSOPT_CMN_S_P3_NTB_P0_P0;

///Socket-0 P0 Start Lane
///NTB Start Lane on Socket-0 P0 Link
#define IDSOPT_CMN_S_P3_NTB_START_LANE_P0_P0_MIN 0 ///< Min of Socket-0 P0 Start Lane
#define IDSOPT_CMN_S_P3_NTB_START_LANE_P0_P0_MAX 15 ///< Max of Socket-0 P0 Start Lane

///Socket-0 P0 End Lane
///NTB End Lane on Socket-0 P0 Link
#define IDSOPT_CMN_S_P3_NTB_END_LANE_P0_P0_MIN 0 ///< Min of Socket-0 P0 End Lane
#define IDSOPT_CMN_S_P3_NTB_END_LANE_P0_P0_MAX 15 ///< Max of Socket-0 P0 End Lane

///Socket-0 P0 Link Speed
///Link Speed for Socket-0 P0 Link
typedef enum {
  IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P0_AUTO = 0xf,///<Auto
  IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P0_GEN1 = 1,///<Gen 1
  IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P0_GEN2 = 2,///<Gen 2
  IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P0_GEN3 = 3,///<Gen 3
  IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P0_GEN4 = 4,///<Gen 4
  IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P0_GEN5 = 5,///<Gen 5
} IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P0;

///Socket-0 P0 NTB Mode
///NTB Mode for Socket-0 P0 Link
typedef enum {
  IDSOPT_CMN_S_P3_NTB_MODE_P0_P0_AUTO = 0xf,///<Auto
  IDSOPT_CMN_S_P3_NTB_MODE_P0_P0_NTBDISABLED = 0,///<NTB Disabled
  IDSOPT_CMN_S_P3_NTB_MODE_P0_P0_NTBPRIMARY = 1,///<NTB Primary
  IDSOPT_CMN_S_P3_NTB_MODE_P0_P0_NTBSECONDARY = 2,///<NTB Secondary
} IDSOPT_CMN_S_P3_NTB_MODE_P0_P0;

///Socket-0 P2 NTB Enable
///Enable NTB on Socket-0 P2 Link
typedef enum {
  IDSOPT_CMN_S_P3_NTB_P0_P2_AUTO = 0xF,///<Auto
  IDSOPT_CMN_S_P3_NTB_P0_P2_ENABLE = 1,///<Enable
  IDSOPT_CMN_S_P3_NTB_P0_P2_DISABLE = 0,///<Disable
} IDSOPT_CMN_S_P3_NTB_P0_P2;

///Socket-0 P2 Start Lane
///NTB Start Lane on Socket-0 P2 Link
#define IDSOPT_CMN_S_P3_NTB_START_LANE_P0_P2_MIN 48 ///< Min of Socket-0 P2 Start Lane
#define IDSOPT_CMN_S_P3_NTB_START_LANE_P0_P2_MAX 63 ///< Max of Socket-0 P2 Start Lane

///Socket-0 P2 End Lane
///NTB End Lane on Socket-0 P2 Link
#define IDSOPT_CMN_S_P3_NTB_END_LANE_P0_P2_MIN 48 ///< Min of Socket-0 P2 End Lane
#define IDSOPT_CMN_S_P3_NTB_END_LANE_P0_P2_MAX 63 ///< Max of Socket-0 P2 End Lane

///Socket-0 P2 Link Speed
///Link Speed for Socket-0 P2 Link
typedef enum {
  IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P2_AUTO = 0xf,///<Auto
  IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P2_GEN1 = 1,///<Gen 1
  IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P2_GEN2 = 2,///<Gen 2
  IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P2_GEN3 = 3,///<Gen 3
  IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P2_GEN4 = 4,///<Gen 4
  IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P2_GEN5 = 5,///<Gen 5
} IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P2;

///Socket-0 P2 NTB Mode
///NTB Mode for Socket-0 P2 Link
typedef enum {
  IDSOPT_CMN_S_P3_NTB_MODE_P0_P2_AUTO = 0xf,///<Auto
  IDSOPT_CMN_S_P3_NTB_MODE_P0_P2_NTBDISABLED = 0,///<NTB Disabled
  IDSOPT_CMN_S_P3_NTB_MODE_P0_P2_NTBPRIMARY = 1,///<NTB Primary
  IDSOPT_CMN_S_P3_NTB_MODE_P0_P2_NTBSECONDARY = 2,///<NTB Secondary
} IDSOPT_CMN_S_P3_NTB_MODE_P0_P2;

///ABL Console Out Control
///Enable : Enable ConsoleOut Function for ABL\nDisable : Disable ConsoleOut Function for ABL\nAuto : Keep default behavior
typedef enum {
  IDSOPT_CMN_SOC_ABL_CON_OUT_DISABLE = 0,///<Disable
  IDSOPT_CMN_SOC_ABL_CON_OUT_ENABLE = 1,///<Enable
  IDSOPT_CMN_SOC_ABL_CON_OUT_AUTO = 2,///<AUTO
} IDSOPT_CMN_SOC_ABL_CON_OUT;

///ABL Console Out Serial Port
///eSPI UART : Enabled serial port through eSPI UART\nSOC UART0 : Enabled serial port through SOC UART0\nSOC UART1 : Enabled serial port through SOC UART1\nAuto : Keep default behavior
typedef enum {
  IDSOPT_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_ESPIUART = 0,///<eSPI UART
  IDSOPT_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_SOCUART0 = 1,///<SOC UART0
  IDSOPT_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_SOCUART1 = 2,///<SOC UART1
  IDSOPT_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_SOC_ABL_CON_OUT_SERIAL_PORT;

///ABL Console Out Serial Port IO
///Select Legacy Uart (SIO or eSPI) IO base\n0x3F8:  Set  IO base to 0x3F8\n0x2F8:  Set  IO base to 0x2F8\n0x3E8:  Set  IO base to 0x3E8\n0x2E8:  Set  IO base to 0x2E8\nAuto : Keep default behavior\n  Please make sure the selected eSPI IO base and length has been filled in APCB_FCH_TYPE_ESPI_INIT or APCB_FCH_TYPE_ESPI1_INIT EspiInitConfiguration table.\n  CRB only fills 0x3F8, 0x2F8 in it by default.
typedef enum {
  IDSOPT_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_IO_0X3F8 = 0,///<0x3F8
  IDSOPT_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_IO_0X2F8 = 1,///<0x2F8
  IDSOPT_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_IO_0X3E8 = 2,///<0x3E8
  IDSOPT_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_IO_0X2E8 = 3,///<0x2E8
  IDSOPT_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_IO_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_IO;

///ABL Serial port IO customized enabled
///Enabled : can input  io based for  ABL console out Serial  Port IO by ABL Console out Serial Port IO Customized.
typedef enum {
  IDSOPT_CMN_SOC_ABL_SERIAL_PORT_IO_CUSTOM_ENABLED_DISABLED = 0,///<Disabled
  IDSOPT_CMN_SOC_ABL_SERIAL_PORT_IO_CUSTOM_ENABLED_ENABLED = 1,///<Enabled
} IDSOPT_CMN_SOC_ABL_SERIAL_PORT_IO_CUSTOM_ENABLED;

///ABL Console out Serial Port IO Customized
///0:disabled. no supported ( default value ).\nplease input your eSPI UART io based in non zero value.\n\nAlso please make sure the selected eSPI IO base and length has been filled in APCB_FCH_TYPE_ESPI_INIT or APCB_FCH_TYPE_ESPI1_INIT EspiInitConfiguration table.\n
#define IDSOPT_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_IO_CUSTOM_MIN 0 ///< Min of ABL Console out Serial Port IO Customized
#define IDSOPT_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_IO_CUSTOM_MAX 0xFFFF ///< Max of ABL Console out Serial Port IO Customized

///ABL Basic Console Out Control
///Enable : Enable Basic ConsoleOut Function for ABL\nDisable : Disable Basic ConsoleOut Function for ABL\nAuto : Keep default behavior
typedef enum {
  IDSOPT_CMN_SOC_ABL_CON_OUT_BASIC_DISABLE = 0,///<Disable
  IDSOPT_CMN_SOC_ABL_CON_OUT_BASIC_ENABLE = 1,///<Enable
  IDSOPT_CMN_SOC_ABL_CON_OUT_BASIC_AUTO = 0xFF,///<AUTO
} IDSOPT_CMN_SOC_ABL_CON_OUT_BASIC;

///ABL PMU message Control
///To control the total number of PMU debug messages.\nSeveral major controls are listed below:\n1. Detailed debug messages (e.g. Eye delays)\n2. Coarse debug messages (e.g. rank information)\n3. Stage completion
typedef enum {
  IDSOPT_CMN_SOC_ABL_PMU_MSG_CTRL_DETAILEDDEBUGMESSAGE = 0x04,///<Detailed debug message
  IDSOPT_CMN_SOC_ABL_PMU_MSG_CTRL_COARSEDEBUGMESSAGE = 0x0A,///<Coarse debug message
  IDSOPT_CMN_SOC_ABL_PMU_MSG_CTRL_STAGECOMPLETION = 0xC8,///<Stage completion
  IDSOPT_CMN_SOC_ABL_PMU_MSG_CTRL_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_SOC_ABL_PMU_MSG_CTRL;

///ABL Memory Population message Control
///Non-Recommended configurations may be functional but may not be validated by AMD. Select\n"warning message": To show warning messages if Memory channel configuration does NOT follow SP5 Memory Population Guidelines.\n"Fatal error": To show the messages and halt the system.
typedef enum {
  IDSOPT_CMN_SOC_ABL_MEM_POP_MSG_CTRL_WARNINGMESSAGE = 0,///<Warning message
  IDSOPT_CMN_SOC_ABL_MEM_POP_MSG_CTRL_FATALERROR = 1,///<Fatal error
} IDSOPT_CMN_SOC_ABL_MEM_POP_MSG_CTRL;

///Print Socket 1 PMU MsgBlock
///Print Socket 1 PMU MsgBlock contents after training.
typedef enum {
  IDSOPT_CMN_PRINT_SOCKET1_PMU_MSG_BLOCK_DISABLED = 0,///<Disabled
  IDSOPT_CMN_PRINT_SOCKET1_PMU_MSG_BLOCK_ENABLED = 1,///<Enabled
  IDSOPT_CMN_PRINT_SOCKET1_PMU_MSG_BLOCK_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_PRINT_SOCKET1_PMU_MSG_BLOCK;

///Print Socket 1 PMU Training Log
///Print Socket 1 PMU training log while 'ABL PMU message Control' == Detailed debug message.
typedef enum {
  IDSOPT_CMN_PRINT_SOCKET1_TRAINING_LOG_DISABLED = 0,///<Disabled
  IDSOPT_CMN_PRINT_SOCKET1_TRAINING_LOG_ENABLED = 1,///<Enabled
  IDSOPT_CMN_PRINT_SOCKET1_TRAINING_LOG_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_PRINT_SOCKET1_TRAINING_LOG;

///PSP error injection support
///Enable EINJ support
typedef enum {
  IDSOPT_DF_CMN_PSP_ERR_INJ_FALSE = 0,///<False
  IDSOPT_DF_CMN_PSP_ERR_INJ_TRUE = 1,///<True
} IDSOPT_DF_CMN_PSP_ERR_INJ;

///Number of Sockets
///Invisible option to know the number of sockets, it's updated by code
#define IDSOPT_NUMBER_OF_SOCKETS_MIN 0 ///< Min of Number of Sockets
#define IDSOPT_NUMBER_OF_SOCKETS_MAX 2 ///< Max of Number of Sockets

///SEC_I2C Voltage Mode
///This option allows to select SEC I2C voltage.\nValid options:\n- Auto (Leave it to silicon initial value)\n- 1.8V\n- 1.1V
typedef enum {
  IDSOPT_CMN_SEC_I2C_VOLT_MODE_AUTO = 0xFF,///<Auto
  IDSOPT_CMN_SEC_I2C_VOLT_MODE_18V = 0x12,///<1.8 V
  IDSOPT_CMN_SEC_I2C_VOLT_MODE_11V = 0xB,///<1.1 V
} IDSOPT_CMN_SEC_I2C_VOLT_MODE;

///FAR enforcement state
///Enabled = FAR is permanently enforced in the CPU, the system can only boot from BIOS with update to date firmware stack as defined by SPL table. \nDisabled  = FAR is NOT enforced in the CPU
typedef enum {
  IDSOPT_CMN_SOC_FAR_ENFORCED_DISABLED = 0,///<Disabled
  IDSOPT_CMN_SOC_FAR_ENFORCED_ENABLED = 1,///<Enabled
} IDSOPT_CMN_SOC_FAR_ENFORCED;

///SPL value in the CPU fuse
///The current SPL value in the CPU fuse which is converted from fuse bitmask
#define IDSOPT_CMN_SOC_SPL_FUSE_MIN 0 ///< Min of SPL value in the CPU fuse
#define IDSOPT_CMN_SOC_SPL_FUSE_MAX 0xff ///< Max of SPL value in the CPU fuse

///SPL value in the SPL table
///if Initial SPL value is set to 0, the SPL fuse in the CPU will be upgraded to the SPL value in the SPL table at next boot.
#define IDSOPT_CMN_SOC_SPL_VALUE_IN_TBL_MIN 0 ///< Min of SPL value in the SPL table
#define IDSOPT_CMN_SOC_SPL_VALUE_IN_TBL_MAX 0xff ///< Max of SPL value in the SPL table

///FAR Switch
///[Enabled] : BIOS will update SPL fuse to SPL value in the SPL table.\n\n[Disabled] : BIOS will not set SPL fuse.
typedef enum {
  IDSOPT_CMN_SOC_FAR_SWITCH_DISABLED = 0,///<Disabled
  IDSOPT_CMN_SOC_FAR_SWITCH_ENABLED = 1,///<Enabled
  IDSOPT_CMN_SOC_FAR_SWITCH_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_SOC_FAR_SWITCH;

///CXL Control
///Force enablement of CXL on all ports.\nDisabled: Allow platforms to enable CXL by port\nEnabled: Force enablement of CXL on all ports.\n
typedef enum {
  IDSOPT_CMN_CXL_CONTROL_AUTO = 0xf,///<Auto
  IDSOPT_CMN_CXL_CONTROL_ENABLED = 1,///<Enabled
  IDSOPT_CMN_CXL_CONTROL_DISABLED = 0,///<Disabled
} IDSOPT_CMN_CXL_CONTROL;

///CXL Physical Addressing
///Control SDP request system address. Normalized address: CS sends normalized address to SDP; System address: CS sends system address to SDP.
typedef enum {
  IDSOPT_CMN_CXL_SDP_REQ_SYS_ADDR_NORMALIZEDADDRESS = 0,///<Normalized address
  IDSOPT_CMN_CXL_SDP_REQ_SYS_ADDR_SYSTEMADDRESS = 1,///<System address
  IDSOPT_CMN_CXL_SDP_REQ_SYS_ADDR_AUTO = 0xFF,///<Auto
} IDSOPT_CMN_CXL_SDP_REQ_SYS_ADDR;

///CXL Memory Attribute
///Sets CXL memory as Special Purpose Memory
typedef enum {
  IDSOPT_CMN_CXL_SPM_AUTO = 0xf,///<Auto
  IDSOPT_CMN_CXL_SPM_ENABLED = 1,///<Enabled
  IDSOPT_CMN_CXL_SPM_DISABLED = 0,///<Disabled
} IDSOPT_CMN_CXL_SPM;

///CXL Encryption
///CXL Encryption
typedef enum {
  IDSOPT_CMN_CXL_ENCRYPTION_ENABLED = 1,///<Enabled
  IDSOPT_CMN_CXL_ENCRYPTION_DISABLED = 0,///<Disabled
} IDSOPT_CMN_CXL_ENCRYPTION;

///CXL DVSEC Lock
///Locks the CXL DVSEC
typedef enum {
  IDSOPT_CMN_CXL_DVSEC_LOCK_AUTO = 0xf,///<Auto
  IDSOPT_CMN_CXL_DVSEC_LOCK_ENABLED = 1,///<Enabled
  IDSOPT_CMN_CXL_DVSEC_LOCK_DISABLED = 0,///<Disabled
} IDSOPT_CMN_CXL_DVSEC_LOCK;

///CXL HDM Decoder Lock On Commit
///The CXL HDM Decoder will become read only when the decoder becomes active.
typedef enum {
  IDSOPT_CMN_CXL_HDM_DECODER_LOCK_ON_COMMIT_AUTO = 0xf,///<Auto
  IDSOPT_CMN_CXL_HDM_DECODER_LOCK_ON_COMMIT_ENABLED = 1,///<Enabled
  IDSOPT_CMN_CXL_HDM_DECODER_LOCK_ON_COMMIT_DISABLED = 0,///<Disabled
} IDSOPT_CMN_CXL_HDM_DECODER_LOCK_ON_COMMIT;

///Temp Gen5 Advertisement
///Temp Gen5 Advertisement for Alternate Protocol
typedef enum {
  IDSOPT_CMN_CXL_TEMP_GEN5_ADVERTISEMENT_DISABLE = 0,///<Disable
  IDSOPT_CMN_CXL_TEMP_GEN5_ADVERTISEMENT_ENABLE = 1,///<Enable
  IDSOPT_CMN_CXL_TEMP_GEN5_ADVERTISEMENT_AUTO = 0xF,///<Auto
} IDSOPT_CMN_CXL_TEMP_GEN5_ADVERTISEMENT;

///Sync Header Bypass
///Enable/Disable Sync Header Bypass
typedef enum {
  IDSOPT_CMN_SYNC_HEADER_BY_PASS_AUTO = 0xf,///<Auto
  IDSOPT_CMN_SYNC_HEADER_BY_PASS_ENABLED = 1,///<Enabled
  IDSOPT_CMN_SYNC_HEADER_BY_PASS_DISABLED = 0,///<Disabled
} IDSOPT_CMN_SYNC_HEADER_BY_PASS;

///Sync Header Bypass Compatibility Mode
///Enable/Disable Sync Header Bypass Compatibility Mode
typedef enum {
  IDSOPT_CXL_SYNC_HEADER_BYPASS_COMP_MODE_AUTO = 0xF,///<Auto
  IDSOPT_CXL_SYNC_HEADER_BYPASS_COMP_MODE_ENABLED = 1,///<Enabled
  IDSOPT_CXL_SYNC_HEADER_BYPASS_COMP_MODE_DISABLED = 0,///<Disabled
} IDSOPT_CXL_SYNC_HEADER_BYPASS_COMP_MODE;

///CXL Memory Online/Offline
///All 4 Plink slots support memory online/offline\nOnly slot4 of Amber supports hot plug\nCXL memory interleaving automatically disabled globally when this CBS is enabled
typedef enum {
  IDSOPT_CMN_CXL_MEM_ONLINE_OFFLINE_DISABLED = 0,///<Disabled
  IDSOPT_CMN_CXL_MEM_ONLINE_OFFLINE_ENABLED = 1,///<Enabled
} IDSOPT_CMN_CXL_MEM_ONLINE_OFFLINE;

///Override CXL Memory Size
typedef enum {
  IDSOPT_DBG_CXL_OVERIDE_CXL_MEMORY_SIZE_32GB = 0,///<32GB
  IDSOPT_DBG_CXL_OVERIDE_CXL_MEMORY_SIZE_64GB = 1,///<64GB
  IDSOPT_DBG_CXL_OVERIDE_CXL_MEMORY_SIZE_128GB = 2,///<128GB
  IDSOPT_DBG_CXL_OVERIDE_CXL_MEMORY_SIZE_AUTO = 0xFF,///<Auto
} IDSOPT_DBG_CXL_OVERIDE_CXL_MEMORY_SIZE;

///CXL Protocol Error Reporting
///Configure CXL Protocol Error reporting mechanism
typedef enum {
  IDSOPT_CMN_CXL_PROTOCOL_ERROR_REPORTING_DISABLED = 0,///<Disabled
  IDSOPT_CMN_CXL_PROTOCOL_ERROR_REPORTING_SAMEASPCIEAER = 1,///<SameAsPcieAer
  IDSOPT_CMN_CXL_PROTOCOL_ERROR_REPORTING_FORCEAERFWFIRSTIFCXLPRESENT = 2,///<ForceAerFwFirstIfCxlPresent
} IDSOPT_CMN_CXL_PROTOCOL_ERROR_REPORTING;

///CXL Component Error Reporting
///Configure CXL Component Error reporting mechanism
typedef enum {
  IDSOPT_CMN_CXL_COMPONENT_ERROR_REPORTING_ALLOWOSFIRST = 0,///<Allow OS-First
  IDSOPT_CMN_CXL_COMPONENT_ERROR_REPORTING_FORCEFWFIRST = 1,///<Force FW-First
  IDSOPT_CMN_CXL_COMPONENT_ERROR_REPORTING_DEBUGFWFIRST = 2,///<Debug FW-First
} IDSOPT_CMN_CXL_COMPONENT_ERROR_REPORTING;

///CXL Root Port Isolation
///Enable/Disable CXL.mem Root Port Isolation
typedef enum {
  IDSOPT_CMN_CXL_MEM_ISOLATION_ENABLE_AUTO = 0xf,///<Auto
  IDSOPT_CMN_CXL_MEM_ISOLATION_ENABLE_ENABLED = 1,///<Enabled
  IDSOPT_CMN_CXL_MEM_ISOLATION_ENABLE_DISABLED = 0,///<Disabled
} IDSOPT_CMN_CXL_MEM_ISOLATION_ENABLE;

///CXL Root Port Isolation FW Notification
///Enable/Disable CXL.mem Root Port Isolation Notification
typedef enum {
  IDSOPT_CMN_CXL_MEM_ISOLATION_FW_NOTIFICATION_AUTO = 0xF,///<Auto
  IDSOPT_CMN_CXL_MEM_ISOLATION_FW_NOTIFICATION_ENABLED = 1,///<Enabled
  IDSOPT_CMN_CXL_MEM_ISOLATION_FW_NOTIFICATION_DISABLED = 0,///<Disabled
} IDSOPT_CMN_CXL_MEM_ISOLATION_FW_NOTIFICATION;


#endif //_IDS_NV_DEF_BRH_H_
