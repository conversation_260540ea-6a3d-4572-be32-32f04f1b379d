TOKEN
    Name  = "BaseCryptLib_SUPPORT"
    Value  = "1"
    Help  = "Switch for Enabling BaseCryptLib support in the project"
    TokenType = Boolean
    Master = Yes
    Token = "BaseCryptLibBin" "=" "0"
End

INFComponent
    Name  = "BaseCryptLib"
    File  = "BaseCryptLibSocket.inf"
    Package  = "CryptoPkg"
    PreProcess = Yes
    Arch  = "IA32 X64 IPF ARM"
    ModuleTypes  = "DXE_DRIVER"
    TOKEN = "BUILD_OPENSSL_WITH_SOCKET" "=" "1"
End

LibraryMapping
    Class  = "BaseCryptLib"
    Instance  = "CryptoPkg.BaseCryptLib"
    Arch  = "IA32 X64 IPF ARM AARCH64"
    ModuleTypes  = "DXE_DRIVER DXE_CORE UEFI_APPLICATION UEFI_DRIVER"
    TOKEN = "BUILD_OPENSSL_WITH_SOCKET" "=" "1"
End

INFComponent
    Name  = "BaseHashApiLib"
    File  = "..\BaseHashApiLib\BaseHashApiLib.inf"
    Package  = "CryptoPkg"
    Arch  = "IA32 X64"
    ModuleTypes  = "BASE"
End

LibraryMapping
    Class  = "BaseHashApiLib"
    Instance  = "CryptoPkg.BaseHashApiLib"
    Arch  = "IA32 X64"
    ModuleTypes  = "BASE"
End

INFComponent
    Name  = "BaseCryptLib"
    File  = "BaseCryptLib.inf"
    Package  = "CryptoPkg"
    Arch  = "IA32 X64 IPF ARM"
    ModuleTypes  = "DXE_DRIVER"
    TOKEN = "BUILD_OPENSSL_WITH_SOCKET" "=" "0" 
End

LibraryMapping
    Class  = "BaseCryptLib"
    Instance  = "CryptoPkg.BaseCryptLib"
    Arch  = "IA32 X64 IPF ARM AARCH64"
    ModuleTypes  = "DXE_DRIVER DXE_CORE UEFI_APPLICATION UEFI_DRIVER"
    TOKEN = "BUILD_OPENSSL_WITH_SOCKET" "=" "0"
End

INFComponent
    Name  = "PeiCryptLib"
    File  = "PeiCryptLib.inf"
    Package  = "CryptoPkg"
    Arch  = "IA32"
    ModuleTypes  = "PEIM"
End

INFComponent
    Name  = "RuntimeCryptLib"
    File  = "RuntimeCryptLib.inf"
    Package  = "CryptoPkg"
    Arch  = "IA32 X64 IPF ARM"
    ModuleTypes  = "DXE_RUNTIME_DRIVER"
End

INFComponent
    Name  = "SmmCryptLib"
    File  = "SmmCryptLib.inf"
    Package  = "CryptoPkg"
    Arch  = "IA32 X64 IPF ARM AARCH64"
    ModuleTypes  = "DXE_SMM_DRIVER MM_STANDALONE"
End

INFComponent
    Name  = "SecCryptLib"
    File  = "SecCryptLib.inf"
    Package  = "CryptoPkg"
    Arch  = "IA32 X64"
    ModuleTypes  = "SEC"
End

INFComponent
    Name  = "BaseCrtWrapperLib"
    File  = "CrtWrapperLib.inf"
    Package  = "CryptoPkg"
    Arch  = "IA32 X64 IPF AARCH64"
    ModuleTypes  = "BASE"
End

LibraryMapping
    Class  = "IntrinsicLib"
    Instance  = "AmiModulePkg.AmiMsftIntrinsicsLib"
    Arch  = "IA32 X64 IPF AARCH64"
End

# Added support for ARM Architecture build
LibraryMapping
    Class  = "BaseCryptLib"
    Instance  = "CryptoPkg.PeiCryptLib"
    Arch  = "IA32 X64 ARM AARCH64"
    ModuleTypes  = "PEIM PEI_CORE"
    Token = "BUILD_EDKII_PEI_CRYPT_LIB" "=" "1"
End

LibraryMapping
    Class  = "BaseCryptLib"
    Instance  = "CryptoPkg.RuntimeCryptLib"
    Arch  = "IA32 X64 IPF ARM"
    ModuleTypes  = "DXE_RUNTIME_DRIVER"
End

LibraryMapping
    Class  = "BaseCryptLib"
    Instance  = "CryptoPkg.SmmCryptLib"
    Arch  = "IA32 X64 IPF ARM AARCH64"
    ModuleTypes  = "DXE_SMM_DRIVER SMM_CORE MM_STANDALONE"
End

LibraryMapping
    Class  = "BaseCryptLib"
    Instance  = "CryptoPkg.SecCryptLib"
    Arch  = "IA32 X64"
    ModuleTypes  = "SEC"
End

LibraryMapping
    Class  = "CrtWrapperLib"
    Instance  = "CryptoPkg.BaseCrtWrapperLib"
    Arch  = "IA32 X64 IPF AARCH64"
End
