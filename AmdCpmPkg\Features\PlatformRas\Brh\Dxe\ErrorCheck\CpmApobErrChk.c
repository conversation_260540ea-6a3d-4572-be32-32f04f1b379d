/*****************************************************************************
 *
 * Copyright (C) 2018-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Library/BaseMemoryLib.h>
#include <Library/DebugLib.h>
#include <Protocol/AmdErrorLogServiceProtocol.h>
#include <Protocol/AmdRasServiceDxeProtocol.h>
#include "AmdPlatformRasBrhDxe.h"
#include "CpmApobErrChk.h"
#include <Library/IdsLib.h>
#include <AmdRas.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
extern EFI_BOOT_SERVICES                *gBS;
extern AMD_RAS_SERVICE_DXE_PROTOCOL     *AmdRasServiceDxeProtocol;
extern GENERIC_MEM_ERR_ENTRY_V3         gGenMemTestErrEntry;
extern PROC_GENERIC_ERR_ENTRY_V3        gProcGenErrEntry;
extern GENERIC_PMIC_ERR_ENTRY_V3        gGenPmicErrEntry;

/*----------------------------------------------------------------------------------------
 *                          T A B L E    D E F I N I T I O N S
 *----------------------------------------------------------------------------------------
 */

/*---------------------------------------------------------------------------------------*/
EFI_STATUS
LogPmicErr (
  IN       UINT32   Severity,
  IN       UINT8    Socket,
  IN       UINT8    Channel,
  IN       UINT8    Dimm,
  IN       UINT8    ChStatus,
  IN       UINT32   PmicErrLog,
  IN       UINT32   PmicRTErrLogA,
  IN       UINT32   PmicRTErrLogB
)
{
  EFI_STATUS                Status = EFI_SUCCESS;
  GENERIC_PMIC_ERR_ENTRY_V3  *PlatformPmicErrEntry;
  EFI_GUID                  PmicErrorSectGuid = PLATFORM_PMIC_SECT_GUID;
  UINTN                     i, NumberOfHandles;
  EFI_HANDLE                *HandleBuffer;
  AMD_CPM_RAS_OEM_PROTOCOL  *AmdRasOemProtocol;
  UINT8                     BertRecordNum;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  PMIC error at socket: %x, channel: %x, dimm: %x\n", Socket, Channel, Dimm);

  Status = gBS->AllocatePool (EfiBootServicesData, sizeof (GENERIC_PMIC_ERR_ENTRY_V3), (VOID **)&PlatformPmicErrEntry);
  gBS->CopyMem (PlatformPmicErrEntry, &gGenPmicErrEntry, sizeof (GENERIC_PMIC_ERR_ENTRY_V3));

  //Update Error section GUID
  gBS->CopyMem (&PlatformPmicErrEntry->GenErrorDataEntry.SectionType[0], &PmicErrorSectGuid, sizeof (EFI_GUID));

  PlatformPmicErrEntry->GenErrorDataEntry.ErrorSeverity = Severity;                    //The Error Severity.
  PlatformPmicErrEntry->PmicErrorSection.ValidBits.Value = PMIC_VALID_BIT_MAP;
  PlatformPmicErrEntry->PmicErrorSection.DimmAddress.Field.SocketId = Socket;          //Socket = Socket Id
  PlatformPmicErrEntry->PmicErrorSection.DimmAddress.Field.ChannelId = Channel;        //Channel = Channel Id
  PlatformPmicErrEntry->PmicErrorSection.DimmAddress.Field.DimmId = Dimm;              //Dimm (0,1)
  PlatformPmicErrEntry->PmicErrorSection.DimmStatus = ChStatus;                        //DIMM Status (0:Channel Disabled/ 1:Channel Enabled)
  PlatformPmicErrEntry->PmicErrorSection.ErrLog.Value = PmicErrLog;                    //0x406B_DATA_B (PMIC Registers 0x4..0x6)
  PlatformPmicErrEntry->PmicErrorSection.PeriodicStatus.Value = 0;
  PlatformPmicErrEntry->PmicErrorSection.RealtimeStatus.Value = (UINT64)PmicRTErrLogB; //0x406E_DATA_B (PMIC Registers 0x8..0xB)
  PlatformPmicErrEntry->PmicErrorSection.RealtimeStatus.Field.PMIC_REG_33 = (UINT8) (PmicRTErrLogA >> 24) & 0x1C; //PMIC_REG_33[4:2]

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "PMIC BERT ErrorSeverity=%x BitMap=%x Socket=%x Channel=%x Dimm=%x ChStatus=%x PmicErrLog=%x PeriodicStatus=%x PmicRTErrLogB=%x PMIC_REG_33=%x\n",
  (UINT8)PlatformPmicErrEntry->GenErrorDataEntry.ErrorSeverity, // = ERROR_SEVERITY_FATAL;  // 0x01;
  (UINT32)PlatformPmicErrEntry->PmicErrorSection.ValidBits.Value, // = PMIC_VALID_BIT_MAP;
  (UINT8)PlatformPmicErrEntry->PmicErrorSection.DimmAddress.Field.SocketId, // = Socket;           //Socket = Socket Id
  (UINT8)PlatformPmicErrEntry->PmicErrorSection.DimmAddress.Field.ChannelId, // = Channel;         //Channel = Channel Id
  (UINT8)PlatformPmicErrEntry->PmicErrorSection.DimmAddress.Field.DimmId, // = Dimm;               //Dimm (0,1)
  (UINT8)PlatformPmicErrEntry->PmicErrorSection.DimmStatus, // = ChStatus;                         //DIMM Status (0:Channel Disabled/ 1:Channel Enabled)
  (UINT32)PlatformPmicErrEntry->PmicErrorSection.ErrLog.Value, // = PmicErrLog;                    //0x406B_DATA_B (PMIC Registers 0x4..0x6)
  (UINT8)PlatformPmicErrEntry->PmicErrorSection.PeriodicStatus.Value, // = 0;
  (UINT32)PlatformPmicErrEntry->PmicErrorSection.RealtimeStatus.Value, // = (UINT64)PmicRTErrLogB; //0x406E_DATA_B (PMIC Registers 0x8..0xB)
  (UINT8)PlatformPmicErrEntry->PmicErrorSection.RealtimeStatus.Field.PMIC_REG_33 // = (UINT8) (PmicRTErrLogA >> 24) & 0x1C; //PMIC_REG_33[4:2]
  );

  UpdateMemErrFruText ((CHAR8 *)PlatformPmicErrEntry->GenErrorDataEntry.FruText,
                       (UINT16)PlatformPmicErrEntry->PmicErrorSection.DimmAddress.Field.SocketId,
                       (UINT16)PlatformPmicErrEntry->PmicErrorSection.DimmAddress.Field.ChannelId,
                       (UINT16)PlatformPmicErrEntry->PmicErrorSection.DimmAddress.Field.DimmId,
                       mPlatformApeiPrivate->OemMemoryMapTable
                       );

  //Locate Ras Oem Protocol
  Status = gBS->LocateHandleBuffer(ByProtocol,
              &gAmdCpmRasOemProtocolGuid,
              NULL, &NumberOfHandles, &HandleBuffer);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "LogPmicErr LocateHandleBuffer gAmdCpmRasOemProtocolGuid Status = %r NumberOfHandles = %x HandleBuffer = %lx\n",Status ,NumberOfHandles, HandleBuffer);

  if(!EFI_ERROR (Status)) {
    for (i = 0; i < NumberOfHandles; i++) {
        // Get the protocol on this handle
        Status = gBS->HandleProtocol(HandleBuffer[i],
                    &gAmdCpmRasOemProtocolGuid,
                    (VOID **)&AmdRasOemProtocol);
        if(EFI_ERROR(Status)) {
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "LogPmicErr HandleProtocol Status = %r\n",Status);
        }
        if(!EFI_ERROR (Status))
          AmdRasOemProtocol->OemErrorLogEventPmic (Socket, Channel, Dimm, (VOID*)PlatformPmicErrEntry);
    }

    if (HandleBuffer != NULL) {
      gBS->FreePool(HandleBuffer);
    }
  }

  // Add a new record to the BERT table
  BertRecordNum = 1;
  Status = AmdRasServiceDxeProtocol->AddBootErrorRecordEx (BertRecordNum,                       // IN UINT8 RecordNum
                                                                (UINT8*)PlatformPmicErrEntry,        // IN UINT8* pErrRecord
                                                                sizeof (GENERIC_PMIC_ERR_ENTRY_V3),  // IN UINT nSize
                                                                ERROR_TYPE_GENERIC,                  // IN UINT8 ErrorType - GENERIC error type
                                                                (UINT8)Severity                      // severity of the PMIC reported error
                                                               );
  if (EFI_ERROR (Status)) {
    ASSERT_EFI_ERROR (Status);
    gBS->FreePool (PlatformPmicErrEntry);
    return EFI_SUCCESS;
  }

  gBS->FreePool (PlatformPmicErrEntry);
  return Status;
}

EFI_STATUS
LogMemTestErr (
  IN       UINT16   Node,
  IN       UINT16   Card,
  IN       UINT16   Module
)
{
  EFI_STATUS                Status = EFI_SUCCESS;
  GENERIC_MEM_ERR_ENTRY_V3  *MemTestErrEntry;
  EFI_GUID                  MemErrorSectGuid = PLATFORM_MEMORY_SECT_GUID;
  UINTN                     i, NumberOfHandles;
  EFI_HANDLE                *HandleBuffer;
  AMD_CPM_RAS_OEM_PROTOCOL  *AmdRasOemProtocol;
  UINT8                     BertRecordNum;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Bad memory at node: %x, channel: %x, module: %x\n", Node, Card, Module);

  Status = gBS->AllocatePool (EfiBootServicesData, sizeof (GENERIC_MEM_ERR_ENTRY_V3), (VOID **)&MemTestErrEntry);
  gBS->CopyMem (MemTestErrEntry, &gGenMemTestErrEntry, sizeof (GENERIC_MEM_ERR_ENTRY_V3));

  //Update Error section GUID
  gBS->CopyMem (&MemTestErrEntry->GenErrorDataEntry.SectionType[0], &MemErrorSectGuid, sizeof (EFI_GUID));

 // Module will be sent as 0xFF for ABL_MEM_PMU_TRAIN_EVER_FAILED - 0x401B
 // AGESA STATUS will be AGESA_ALERT
  if (Module == 0xFF) {
    MemTestErrEntry->GenErrorDataEntry.ErrorSeverity = ERROR_SEVERITY_CORRECTED;  // 0x02;
    MemTestErrEntry->MemErrorSection.ValidBits.Value = MEM_PARITY_VALID_BIT_MAP;  // DIMM VALID IS NOT SET
  }
  else {
    MemTestErrEntry->GenErrorDataEntry.ErrorSeverity = ERROR_SEVERITY_FATAL;  // 0x01;
    MemTestErrEntry->MemErrorSection.ValidBits.Value = MEM_TEST_VALID_BIT_MAP;
  }
  //Card = Socket Id
  MemTestErrEntry->MemErrorSection.Node = Node;
  //Card = Channel Id
  MemTestErrEntry->MemErrorSection.Card = Card;
  //Module = (ChipSelect & 0x03) >> 1, ChipSelect 0,1 = Module 0, ChipSelect 2,3 = Module 1

  if (Module != 0xFF) {
    MemTestErrEntry->MemErrorSection.Module = Module;
    UpdateMemErrFruText ((CHAR8 *)MemTestErrEntry->GenErrorDataEntry.FruText,
                       MemTestErrEntry->MemErrorSection.Node,
                       MemTestErrEntry->MemErrorSection.Card,
                       MemTestErrEntry->MemErrorSection.Module,
                       mPlatformApeiPrivate->OemMemoryMapTable
                       );
  }

  MemTestErrEntry->MemErrorSection.ErrStatus = ((1 << 18) | (4 << 8));   // Error Detected on Data Transaction | ERR_MEM (0x40400)
  MemTestErrEntry->MemErrorSection.MemErrType = PHYSICAL_MEM_MAPOUT;

  //Locate Ras Oem Protocol
  Status = gBS->LocateHandleBuffer(ByProtocol,
              &gAmdCpmRasOemProtocolGuid,
              NULL, &NumberOfHandles, &HandleBuffer);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "LogMemTestErr LocateHandleBuffer gAmdCpmRasOemProtocolGuid Status = %r NumberOfHandles = %x HandleBuffer = %lx\n",Status ,NumberOfHandles, HandleBuffer);

  if(!EFI_ERROR (Status)) {
    for (i = 0; i < NumberOfHandles; i++) {
        // Get the protocol on this handle
        Status = gBS->HandleProtocol(HandleBuffer[i],
                    &gAmdCpmRasOemProtocolGuid,
                    (VOID **)&AmdRasOemProtocol);

        if(!EFI_ERROR (Status))
          AmdRasOemProtocol->OemErrorLogEventMemTest (Node, Card, Module, (VOID*)MemTestErrEntry);
    }

    if (HandleBuffer != NULL) {
      gBS->FreePool(HandleBuffer);
    }
  }

  // Add a new record to the BERT table
  BertRecordNum = 1;
  Status = AmdRasServiceDxeProtocol->AddBootErrorRecordEx (BertRecordNum,                       // IN UINT8 RecordNum
                                                                (UINT8*)MemTestErrEntry,             // IN UINT8* pErrRecord
                                                                sizeof (GENERIC_MEM_ERR_ENTRY_V3),   // IN UINT nSize
                                                                ERROR_TYPE_GENERIC,                  // IN UINT8 ErrorType - GENERIC error type
                                                                (Module!=0xFF) ? ERROR_SEVERITY_FATAL:ERROR_SEVERITY_CORRECTED
                                                               );
  if (EFI_ERROR (Status)) {
    ASSERT_EFI_ERROR (Status);
    gBS->FreePool (MemTestErrEntry);
    return EFI_SUCCESS;
  }
  gBS->FreePool (MemTestErrEntry);
  return Status;
}

EFI_STATUS
CheckMemTestErr ( VOID )
{
  EFI_STATUS                            Status = EFI_SUCCESS;
  DXE_AMD_ERROR_LOG_SERVICES_PROTOCOL   *ErrorLogServices;
  ERROR_LOG_DATA_STRUCT                 *ErrorLogDataPtr;
  MEMORY_TEST_ERROR_STRUC               MemoryTestError;
  MEMORY_PMU_ERROR_STRUC                MemoryPmuTrainingError;
  MEMORY_PMU_TRAIN_EVER_FAIL_ERROR_STRUC MemoryPmuTrainingEverFailedError;
  UINT32                                i;
  UINT16                                ChannelIndex;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]Check ABL memory test error\n");
  Status = gBS->LocateProtocol(&gAmdErrorLogServiceProtocolGuid, NULL, (VOID **)&ErrorLogServices);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Locate gAmdErrorLogServiceProtocolGuid failed\n");
    return Status;
  }

  Status = gBS->AllocatePool (EfiBootServicesData, sizeof (ERROR_LOG_DATA_STRUCT), (VOID **)&ErrorLogDataPtr);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Allocate buffer failed\n");
    return Status;
  }
  ZeroMem (ErrorLogDataPtr, sizeof (ERROR_LOG_DATA_STRUCT));

  ErrorLogServices->AmdAquireErrorLogWithFlagDxe (ErrorLogServices, ErrorLogDataPtr, FALSE);
  for (i = 0; i < ErrorLogDataPtr->Count; i++) {
    //Log error to BERT
    if (ABL_MEM_PMU_TRAIN_ERROR == ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo) { //0x4001
      MemoryPmuTrainingError.Value = ErrorLogDataPtr->ErrorLog_Param[i].DataParam1;
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Memory PMU Training error found at EventLog[%x]=%x\n", i, MemoryPmuTrainingError.Value);
      if (MemoryPmuTrainingError.Field.Dimm0) {
        LogMemTestErr ((UINT16)MemoryPmuTrainingError.Field.Node,
                       (UINT16)MemoryPmuTrainingError.Field.Channel,
                       0
                      );
      }
      if (MemoryPmuTrainingError.Field.Dimm1) {
        LogMemTestErr ((UINT16)MemoryPmuTrainingError.Field.Node,
                       (UINT16)MemoryPmuTrainingError.Field.Channel,
                       1
                      );
      }
    }

    //Log error to BERT
    if (ABL_MEM_PMU_TRAIN_EVER_FAILED == ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo) { //0x401B
      MemoryPmuTrainingEverFailedError.Value = ErrorLogDataPtr->ErrorLog_Param[i].DataParam1;
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Memory PMU Training Ever failed found at EventLog[%x]=%x\n", i, MemoryPmuTrainingEverFailedError.Value);
      if (MemoryPmuTrainingEverFailedError.Field.ChannelRetryMask) {
        // Find the bit set position to identify the channel retried.
        // Bit 27:16 - Channel retry mask, bit 16=channel 0, bit 17=channel 1, etc, 1: channel retried
        for (ChannelIndex = 0 ; ChannelIndex < 12; ChannelIndex++) {
          if (MemoryPmuTrainingEverFailedError.Field.ChannelRetryMask & (1 << ChannelIndex)) {
             IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Channel Retried =%x\n", ChannelIndex);
             LogMemTestErr ((UINT16)MemoryPmuTrainingEverFailedError.Field.Node,
                       ChannelIndex,
                       0xFF  // DIMM is not valid for this error.
                      );
          } // if (MemoryPmuTrainingEverFailedError.Field.ChannelRetryMask & (1 << Index))
        } // for loop
      } // if (MemoryPmuTrainingEverFailedError.Field.ChannelRetryMask)
    } // If (0x401b)

    if (ABL_MEM_AGESA_MEMORY_TEST_ERROR == ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo) { //0x4003
      MemoryTestError.Value = ErrorLogDataPtr->ErrorLog_Param[i].DataParam1;
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Memory test error found at EventLog[%x]=%x\n", i, MemoryTestError.Value);
      if (MemoryTestError.Field.Dimm0) {
        LogMemTestErr ((UINT16)MemoryTestError.Field.Node,
                       (UINT16)MemoryTestError.Field.Channel,
                       0
                      );
      }
      if (MemoryTestError.Field.Dimm1) {
        LogMemTestErr ((UINT16)MemoryTestError.Field.Node,
                       (UINT16)MemoryTestError.Field.Channel,
                       1
                      );
      }
    }
  }
  return Status;
}


EFI_STATUS
AmdErrorLogDetection ( VOID )
{
  EFI_STATUS                            Status;
  DXE_AMD_ERROR_LOG_SERVICES_PROTOCOL   *ErrorLogServices;
  ERROR_LOG_DATA_STRUCT                 ErrorLogDataPtr;
  UINT32                                Index;
  UINT8                                 LoopA;
  UINT8                                 LoopB;
  UINT8                                 Step;
  PROC_GENERIC_ERR_ENTRY_V3             *ProcGenErrEntry;
  EFI_GUID                              ProcGenErrorSectGuid = PROCESSOR_GENERIC_SECT_GUID;
  UINT32                                ProcGenErrEntrySize;
  RAS_CPUID_FN                          RasCpuidFn = {0};
  CHAR8                                 *ProcBrandStr;
  UINT8                                 BertRecordNum;
  EFI_HANDLE                            *HandleBuffer;
  UINTN                                 i, NumberOfHandles;
  AMD_CPM_RAS_OEM_PROTOCOL              *AmdRasOemProtocol;

  Status = gBS->LocateProtocol(&gAmdErrorLogServiceProtocolGuid, NULL, (VOID **)&ErrorLogServices);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] %a - Locate gAmdErrorLogServiceProtocolGuid: %r\n", __FUNCTION__, Status);
    return Status;
  }

  Status = ErrorLogServices->AmdAquireErrorLogWithFlagDxe (ErrorLogServices, &ErrorLogDataPtr, FALSE);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] %a - AquireErrorLogWithFlagDxe: %r\n", __FUNCTION__, Status);
    return Status;
  }

  for (Index = 0; Index < ErrorLogDataPtr.Count; Index++) {
    switch (ErrorLogDataPtr.ErrorLog_Param[Index].ErrorInfo) {
    case (ABL_CCD_BIST_FAILURE):  //0x4065
      ProcGenErrEntrySize = sizeof (PROC_GENERIC_ERR_ENTRY_V3);
      Status = gBS->AllocatePool (EfiBootServicesData, ProcGenErrEntrySize, (VOID **)&ProcGenErrEntry);
      ZeroMem (ProcGenErrEntry, ProcGenErrEntrySize);

      //Update generic data info
      gBS->CopyMem (ProcGenErrEntry, &gProcGenErrEntry, ProcGenErrEntrySize);

      //Update Error section GUID
      gBS->CopyMem (&ProcGenErrEntry->GenErrorDataEntry.SectionType[0], &ProcGenErrorSectGuid, sizeof (EFI_GUID));

      ProcGenErrEntry->ProcGenErrorSection.ValidFields =
        (GENERIC_ERROR_PROC_TYPE_VALID | GENERIC_ERROR_PROC_ISA_VALID | GENERIC_ERROR_PROC_ERROR_TYPE_VALID | \
         GENERIC_ERROR_PROC_OPERATION_VALID | GENERIC_ERROR_PROC_VERSION_VALID  | GENERIC_ERROR_PROC_BRAND_VALID | \
         GENERIC_ERROR_PROC_TARGET_ADDR_VALID);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] %a - ValidFields: 0x%0lx\n", __FUNCTION__, ProcGenErrEntry->ProcGenErrorSection.ValidFields);

      ProcGenErrEntry->ProcGenErrorSection.Type = GENERIC_ERROR_PROC_TYPE_IA32_X64;
      ProcGenErrEntry->ProcGenErrorSection.Isa = GENERIC_ERROR_PROC_ISA_X64;
      ProcGenErrEntry->ProcGenErrorSection.ErrorType = GENERIC_ERROR_PROC_ERROR_TYPE_UNKNOWN;
      ProcGenErrEntry->ProcGenErrorSection.Operation = GENERIC_ERROR_PROC_OPERATION_GENERIC;

      //Get CPUID Information from BSP
      RasCpuidFn.FunctionId = 0x00000001;  //Register In EAX
      GetCpuId (&RasCpuidFn);
      ProcGenErrEntry->ProcGenErrorSection.VersionInfo = RasCpuidFn.EAX_Reg;

      //Get CPUID Brand String from BSP
      ProcBrandStr = ProcGenErrEntry->ProcGenErrorSection.BrandString;
      for (LoopA = 0; LoopA < 3; LoopA++) {
        RasCpuidFn.FunctionId = 0x80000002 + LoopA;  //Register In EAX: 0x80000002, 0x80000003, 0x80000004
        GetCpuId (&RasCpuidFn);
        for (LoopB = 0; LoopB < 4; LoopB++) {
          Step = (LoopB * sizeof (UINT32));
          gBS->CopyMem (ProcBrandStr + (LoopA * sizeof (UINT32) * 4) + Step, ((CHAR8 *)&(RasCpuidFn.EAX_Reg) + Step), sizeof (UINT32));
        }
      }
      ProcBrandStr[127] = '\0';
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] %a - ProcBrandStr: [%a]\n", __FUNCTION__, ProcGenErrEntry->ProcGenErrorSection.BrandString);

      //CCD BIST Error Record(32 bits) => Bit[31:24] Socket, Bit[23:16] Die, Bit[15:0] CcdBistMap
      ProcGenErrEntry->ProcGenErrorSection.TargetAddr = ErrorLogDataPtr.ErrorLog_Param[Index].DataParam1;
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] %a - CCD BIST Error Record(32 bits) => Bit[31:24] Socket, Bit[23:16] Die, Bit[15:0] CcdBistMap = 0x%08x \n",
              __FUNCTION__, ProcGenErrEntry->ProcGenErrorSection.TargetAddr);

      ProcGenErrEntry->GenErrorDataEntry.ErrorDataLength = sizeof (PROC_GENERIC_ERR_SEC);
      ProcGenErrEntry->GenErrorDataEntry.ErrorSeverity = ERROR_SEVERITY_FATAL;

      // OEM Hooks for CCD BIST Errors
      //Locate Ras Oem Protocol
      Status = gBS->LocateHandleBuffer(ByProtocol,
                  &gAmdCpmRasOemProtocolGuid,
                  NULL, &NumberOfHandles, &HandleBuffer);

      if(!EFI_ERROR(Status)) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "AmdErrorLogDetection LocateHandleBuffer gAmdCpmRasOemProtocolGuid Status = %r \
                                                   NumberOfHandles = %x HandleBuffer = %lx\n",Status ,NumberOfHandles, HandleBuffer);

        for (i = 0; i < NumberOfHandles; i++) {
            // Get the protocol on this handle
            Status = gBS->HandleProtocol(HandleBuffer[i],
                        &gAmdCpmRasOemProtocolGuid,
                        (VOID **)&AmdRasOemProtocol);

            if(!EFI_ERROR(Status))
              AmdRasOemProtocol->OemErrorLogEventCCDBIST (ProcGenErrEntry); // ProcGenErrEntry->ProcGenErrorSection.TargetAddr has CCD BIST Error information.
        }

        if (HandleBuffer != NULL) {
          gBS->FreePool(HandleBuffer);
        }
      }
      
      // Add a new record to the BERT table
      BertRecordNum = 1;
      Status = AmdRasServiceDxeProtocol->AddBootErrorRecordEx (BertRecordNum,                   // IN UNT8 RecordNum
                                                                    (UINT8*)ProcGenErrEntry,         // IN UINT8* pErrRecord
                                                                    ProcGenErrEntrySize,             // IN UINT nSize
                                                                    ERROR_TYPE_GENERIC,              // IN UINT8 ErrorType - GENERIC error type
                                                                    ERROR_SEVERITY_FATAL             // IN UINT8 SeverityType - NON-CORRECTABLE
                                                                   );
      if (EFI_ERROR(Status)) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "Failed to add a new record to the BERT Table\n");
      }

      gBS->FreePool (ProcGenErrEntry);

      break;
    default:
      break;
    }
  }

  return EFI_SUCCESS;
}

EFI_STATUS
CheckPmicErr ( VOID )
{
  EFI_STATUS                            Status = EFI_SUCCESS;
  DXE_AMD_ERROR_LOG_SERVICES_PROTOCOL   *ErrorLogServices;
  ERROR_LOG_DATA_STRUCT                 *ErrorLogDataPtr;
  PMIC_ERROR_0x406B_STRUCT              *PmicErrorData0x406BPtr;
  PMIC_ERROR_0x406E_STRUCT              *PmicErrorData0x406EPtr;
  PARSED_PMIC_ERROR_DATA_STRUCT         *ParsedPmicErrorData;
  UINT8                                 i = 0, j = 0, index = 0, PmicErrorCount = 0;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]Check ABL PMIC error\n");
  Status = gBS->LocateProtocol(&gAmdErrorLogServiceProtocolGuid, NULL, (VOID **)&ErrorLogServices);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Locate gAmdErrorLogServiceProtocolGuid failed\n");
    return Status;
  }

  Status = gBS->AllocatePool (EfiBootServicesData, sizeof (PMIC_ERROR_0x406B_STRUCT), (VOID **)&PmicErrorData0x406BPtr);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Allocate PMIC_ERROR_0x406B_STRUCT buffer failed\n");
    return Status;
  }
  ZeroMem (PmicErrorData0x406BPtr, sizeof (PMIC_ERROR_0x406B_STRUCT));

  Status = gBS->AllocatePool (EfiBootServicesData, sizeof (PMIC_ERROR_0x406E_STRUCT), (VOID **)&PmicErrorData0x406EPtr);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Allocate PMIC_ERROR_0x406E_STRUCT buffer failed\n");
    return Status;
  }
  ZeroMem (PmicErrorData0x406EPtr, sizeof (PMIC_ERROR_0x406E_STRUCT));

  Status = gBS->AllocatePool (EfiBootServicesData, sizeof (PARSED_PMIC_ERROR_DATA_STRUCT), (VOID **)&ParsedPmicErrorData);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Allocate PARSED_PMIC_ERROR_DATA_STRUCT buffer failed\n");
    return Status;
  }
  ZeroMem (ParsedPmicErrorData, sizeof (PARSED_PMIC_ERROR_DATA_STRUCT));

  Status = gBS->AllocatePool (EfiBootServicesData, sizeof (ERROR_LOG_DATA_STRUCT), (VOID **)&ErrorLogDataPtr);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Allocate buffer failed\n");
    return Status;
  }
  ZeroMem (ErrorLogDataPtr, sizeof (ERROR_LOG_DATA_STRUCT));

  ErrorLogServices->AmdAquireErrorLogWithFlagDxe (ErrorLogServices, ErrorLogDataPtr, FALSE);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "ErrorLogDataPtr->Count=%x \n", ErrorLogDataPtr->Count);
  for (i = 0; i < ErrorLogDataPtr->Count; i++) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "ErrorLogDataPtr->ErrorLog_Param[%x].ErrorInfo=%x ErrorClass[%x]=0x%x\n",
    i,
    ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo,
    i,
    ErrorLogDataPtr->ErrorLog_Param[i].ErrorClass);
  }

  ParsedPmicErrorData->Count = 0;
  PmicErrorData0x406BPtr->Count = 0;
  index = 0;
  for (i = 0; i < ErrorLogDataPtr->Count; i++) {
    if ((ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo == ABL_MEM_ERROR_PMIC_ERROR) || (ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo == ABL_MEM_ERROR_PMIC_REAL_TIME_ERROR)) {
      PmicErrorCount++;
    }

    if ((ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo == ABL_MEM_ERROR_PMIC_ERROR )) { //0x406B
      if (AGESA_FATAL == ErrorLogDataPtr->ErrorLog_Param[i].ErrorClass){
        PmicErrorData0x406BPtr->Severity[index] = ERROR_SEVERITY_FATAL;
      } else {
        PmicErrorData0x406BPtr->Severity[index] = ERROR_RECOVERABLE;
      }
      PmicErrorData0x406BPtr->PmicErrorDataA[index].Value = ErrorLogDataPtr->ErrorLog_Param[i].DataParam1; // 0x406B_DATA_A (Socket/Channel/DIMM)
      PmicErrorData0x406BPtr->PmicErrorDataB[index].Value = ErrorLogDataPtr->ErrorLog_Param[i].DataParam2; // 0x406B_DATA_B (PMIC Registers 0x4..0x6)
      PmicErrorData0x406BPtr->Count++;
      //Debug message for 0x406B
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "Pmic 0x406B count=%x ErrorClass[%x]=0x%x PmicErrorDataA.Value[%x]=0x%x PmicErrorDataB.Value[%x]=0x%x\n",
      PmicErrorData0x406BPtr->Count,
      index,
      PmicErrorData0x406BPtr->Severity[index],
      index,
      PmicErrorData0x406BPtr->PmicErrorDataA[index].Value,
      index,
      PmicErrorData0x406BPtr->PmicErrorDataB[index].Value);

      index++;
    }
  }
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "Total Pmic count (406B+406E)=%x, PmicErrorData0x406BPtr->Count=%x\n", PmicErrorCount, PmicErrorData0x406BPtr->Count);

  index = 0; //reset index
  for (i = 0; i < ErrorLogDataPtr->Count; i++) {
    if ((ErrorLogDataPtr->ErrorLog_Param[i].ErrorInfo == ABL_MEM_ERROR_PMIC_REAL_TIME_ERROR )) { //0x406E
      PmicErrorData0x406EPtr->PmicRealTimeErrorDataA[i].Value = ErrorLogDataPtr->ErrorLog_Param[i].DataParam1; // 0x406E_DATA_A (Socket/Channel/DIMM/PMIC_REG_33)
      PmicErrorData0x406EPtr->PmicRealTimeErrorDataB[i].Value = ErrorLogDataPtr->ErrorLog_Param[i].DataParam2; // 0x406E_DATA_B (PMIC Registers 0x4..0x6)
      if (AGESA_FATAL == ErrorLogDataPtr->ErrorLog_Param[i].ErrorClass){  //Convert AGESA_STATUS to The severity of this error
        PmicErrorData0x406EPtr->Severity[i] = ERROR_SEVERITY_FATAL;
      } else {
        PmicErrorData0x406EPtr->Severity[i] = ERROR_RECOVERABLE;
      }

      //Debug message for 0x406E
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "PmicErrorData0x406E from APCB PmicRealTimeErrorDataA.Value[%x]=0x%x(DataParam1=%x) PmicRealTimeErrorDataB.Value[%x]=0x%x(DataParam2=%x)\n",
      i,
      PmicErrorData0x406EPtr->PmicRealTimeErrorDataA[i].Value,
      ErrorLogDataPtr->ErrorLog_Param[i].DataParam1,
      i,
      PmicErrorData0x406EPtr->PmicRealTimeErrorDataB[i].Value,
      ErrorLogDataPtr->ErrorLog_Param[i].DataParam2
      );

      if (PmicErrorData0x406BPtr->Count > 0) {
        for (j = 0; j < PmicErrorData0x406BPtr->Count; j++) {
          if ((PmicErrorData0x406EPtr->PmicRealTimeErrorDataA[i].Field.Socket == PmicErrorData0x406BPtr->PmicErrorDataA[j].Field.Socket) && \
              (PmicErrorData0x406EPtr->PmicRealTimeErrorDataA[i].Field.Channel == PmicErrorData0x406BPtr->PmicErrorDataA[j].Field.Channel) && \
              (PmicErrorData0x406EPtr->PmicRealTimeErrorDataA[i].Field.Dimm == PmicErrorData0x406BPtr->PmicErrorDataA[j].Field.Dimm)){

              //parse data for PmicRealTimeErrorData0x406E and PmicErrorData0x406B
              ParsedPmicErrorData->Severity[index] = PmicErrorData0x406BPtr->Severity[j];
              ParsedPmicErrorData->Socket[index] = (UINT8)PmicErrorData0x406EPtr->PmicRealTimeErrorDataA[i].Field.Socket;
              ParsedPmicErrorData->Channel[index] = (UINT8)PmicErrorData0x406EPtr->PmicRealTimeErrorDataA[i].Field.Channel;
              ParsedPmicErrorData->Dimm[index] = (UINT8)PmicErrorData0x406EPtr->PmicRealTimeErrorDataA[i].Field.Dimm;
              ParsedPmicErrorData->ChStatus[index] = (UINT8)PmicErrorData0x406EPtr->PmicRealTimeErrorDataA[i].Field.ChStatus;
              ParsedPmicErrorData->PmicErrorDataA[index].Value = PmicErrorData0x406BPtr->PmicErrorDataA[j].Value;
              ParsedPmicErrorData->PmicErrorDataB[index].Value = PmicErrorData0x406BPtr->PmicErrorDataB[j].Value;
              ParsedPmicErrorData->PmicRealTimeErrorDataA[index].Value = PmicErrorData0x406EPtr->PmicRealTimeErrorDataA[i].Value;
              ParsedPmicErrorData->PmicRealTimeErrorDataB[index].Value = PmicErrorData0x406EPtr->PmicRealTimeErrorDataB[i].Value;

              // mark the matched PmicErrorData array.
              PmicErrorData0x406BPtr->PmicErrorDataA[j].Value = 0xFFFFFFFF;
              PmicErrorData0x406BPtr->PmicErrorDataB[j].Value = 0xFFFFFFFF;
              index++;
            } else {
              //parsed data for PmicRealTimeErrorData0x406E only
              ParsedPmicErrorData->Severity[index] = PmicErrorData0x406EPtr->Severity[i];
              ParsedPmicErrorData->Socket[index] = (UINT8)PmicErrorData0x406EPtr->PmicRealTimeErrorDataA[i].Field.Socket;
              ParsedPmicErrorData->Channel[index] = (UINT8)PmicErrorData0x406EPtr->PmicRealTimeErrorDataA[i].Field.Channel;
              ParsedPmicErrorData->Dimm[index] = (UINT8)PmicErrorData0x406EPtr->PmicRealTimeErrorDataA[i].Field.Dimm;
              ParsedPmicErrorData->ChStatus[index] = (UINT8)PmicErrorData0x406EPtr->PmicRealTimeErrorDataA[i].Field.ChStatus;
              ParsedPmicErrorData->PmicErrorDataA[index].Value = 0;
              ParsedPmicErrorData->PmicErrorDataB[index].Value = 0;
              ParsedPmicErrorData->PmicRealTimeErrorDataA[index].Value = PmicErrorData0x406EPtr->PmicRealTimeErrorDataA[i].Value;
              ParsedPmicErrorData->PmicRealTimeErrorDataB[index].Value = PmicErrorData0x406EPtr->PmicRealTimeErrorDataB[i].Value;
              index++;
          }
        }
      }else { //if (PmicErrorData0x406BPtr->Count == 0)
        //parsed data for PmicRealTimeErrorData0x406E only
        ParsedPmicErrorData->Severity[index] = PmicErrorData0x406EPtr->Severity[i];
        ParsedPmicErrorData->Socket[index] = (UINT8)PmicErrorData0x406EPtr->PmicRealTimeErrorDataA[i].Field.Socket;
        ParsedPmicErrorData->Channel[index] = (UINT8)PmicErrorData0x406EPtr->PmicRealTimeErrorDataA[i].Field.Channel;
        ParsedPmicErrorData->Dimm[index] = (UINT8)PmicErrorData0x406EPtr->PmicRealTimeErrorDataA[i].Field.Dimm;
        ParsedPmicErrorData->ChStatus[index] = (UINT8)PmicErrorData0x406EPtr->PmicRealTimeErrorDataA[i].Field.ChStatus;
        ParsedPmicErrorData->PmicErrorDataA[index].Value = 0;
        ParsedPmicErrorData->PmicErrorDataB[index].Value = 0;
        ParsedPmicErrorData->PmicRealTimeErrorDataA[index].Value = PmicErrorData0x406EPtr->PmicRealTimeErrorDataA[i].Value;
        ParsedPmicErrorData->PmicRealTimeErrorDataB[index].Value = PmicErrorData0x406EPtr->PmicRealTimeErrorDataB[i].Value;
        index++;
      }
    } // if(ABL_MEM_ERROR_PMIC_REAL_TIME_ERROR)
  }

  ParsedPmicErrorData->Count = index;
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "Total ParsedPmicErrorData->Count (include 0x406E)=%x The next start index=0x%x \n", ParsedPmicErrorData->Count, index);

  //Store the rest PmicErrorData0x406B array to ParsedPmicErrorData.
  for (i = 0; i < PmicErrorData0x406BPtr->Count; i++) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "PmicErrorData0x406BPtr->Severity[%x]=%x 0x406BPtr->PmicErrorDataA[%x]=%x 0x406BPtr->PmicErrorDataB[%x]=%x,\n",
    i,
    PmicErrorData0x406BPtr->Severity[i],
    i,
    PmicErrorData0x406BPtr->PmicErrorDataA[i].Value,
    i,
    PmicErrorData0x406BPtr->PmicErrorDataB[i].Value);

    if ((PmicErrorData0x406BPtr->PmicErrorDataA[i].Value != 0xFFFFFFFF) && (PmicErrorData0x406BPtr->PmicErrorDataB[i].Value != 0xFFFFFFFF)) {
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "Store the rest PmicErrorData0x406B array to ParsedPmicErrorData array.\n");
      ParsedPmicErrorData->Severity[index] = PmicErrorData0x406BPtr->Severity[i];
      ParsedPmicErrorData->Socket[index] = (UINT8)PmicErrorData0x406BPtr->PmicErrorDataA[i].Field.Socket;
      ParsedPmicErrorData->Channel[index] = (UINT8)PmicErrorData0x406BPtr->PmicErrorDataA[i].Field.Channel;
      ParsedPmicErrorData->Dimm[index] = (UINT8)PmicErrorData0x406BPtr->PmicErrorDataA[i].Field.Dimm;
      ParsedPmicErrorData->ChStatus[index] = (UINT8)PmicErrorData0x406BPtr->PmicErrorDataA[i].Field.ChStatus;
      ParsedPmicErrorData->PmicErrorDataA[index].Value = PmicErrorData0x406BPtr->PmicErrorDataA[i].Value;
      ParsedPmicErrorData->PmicErrorDataB[index].Value = PmicErrorData0x406BPtr->PmicErrorDataB[i].Value;
      ParsedPmicErrorData->PmicRealTimeErrorDataA[index].Value = 0;
      ParsedPmicErrorData->PmicRealTimeErrorDataB[index].Value = 0;

      //Debug message......
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "Severity[%x]=%x Socket=0x%x Channel=0x%x Dimm=0x%x ChStatus=0x%x PmicErrorDataA=0x%x PmicErrorDataB=0x%x PmicRealTimeErrorDataA=0x%x PmicRealTimeErrorDataB=0x%x\n",
      index,
      ParsedPmicErrorData->Severity[index],
      (UINT8)ParsedPmicErrorData->Socket[index],
      (UINT8)ParsedPmicErrorData->Channel[index],
      (UINT8)ParsedPmicErrorData->Dimm[index],
      (UINT8)ParsedPmicErrorData->ChStatus[index],
      (UINT32)ParsedPmicErrorData->PmicErrorDataA[index].Value,
      (UINT32)ParsedPmicErrorData->PmicErrorDataB[index].Value,
      (UINT32)ParsedPmicErrorData->PmicRealTimeErrorDataA[index].Value,
      (UINT32)ParsedPmicErrorData->PmicRealTimeErrorDataB[index].Value
      );

      ParsedPmicErrorData->Count++; //increase the error count.
      index++;
    }
  }
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "Total ParsedPmicErrorData->Count (0x406E+0x406B)=%x The final Parsed_indx=0x%x \n", ParsedPmicErrorData->Count, index);

  //Log error to BERT
  for (i = 0; i < ParsedPmicErrorData->Count; i++) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "ParsedPmicErrorData->Count=0x%x index=0x%x Severity=%x Socket=0x%x Channel=0x%x Dimm=0x%x ChStatus=0x%x \
    PmicErrorDataB=0x%x PmicRealTimeErrorDataA=0x%x PmicRealTimeErrorDataB=0x%x\n",
    ParsedPmicErrorData->Count,
    i,
    ParsedPmicErrorData->Severity[i],
    (UINT8)ParsedPmicErrorData->Socket[i],
    (UINT8)ParsedPmicErrorData->Channel[i],
    (UINT8)ParsedPmicErrorData->Dimm[i],
    (UINT8)ParsedPmicErrorData->ChStatus[i],
    (UINT32)ParsedPmicErrorData->PmicErrorDataB[i].Value,
    (UINT32)ParsedPmicErrorData->PmicRealTimeErrorDataA[i].Value,
    (UINT32)ParsedPmicErrorData->PmicRealTimeErrorDataB[i].Value);

    if (mPlatformApeiPrivate->PlatRasPolicy.PcdAmdPmicBertWAReg0x5) {
      if ((ParsedPmicErrorData->PmicErrorDataB[i].Field.PMIC_REG_05 & 0x07) == 0x07){
        ParsedPmicErrorData->PmicErrorDataB[i].Field.PMIC_REG_05 &= 0xF8;
      }
    }
    LogPmicErr (ParsedPmicErrorData->Severity[i],
                (UINT8)ParsedPmicErrorData->Socket[i],
                (UINT8)ParsedPmicErrorData->Channel[i],
                (UINT8)ParsedPmicErrorData->Dimm[i],
                (UINT8)ParsedPmicErrorData->ChStatus[i],
                (UINT32)ParsedPmicErrorData->PmicErrorDataB[i].Value,
                (UINT32)ParsedPmicErrorData->PmicRealTimeErrorDataA[i].Value,
                (UINT32)ParsedPmicErrorData->PmicRealTimeErrorDataB[i].Value
                );
  }

  return Status;
}
