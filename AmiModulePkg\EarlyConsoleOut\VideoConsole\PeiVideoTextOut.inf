#***********************************************************************
#*                                                                     *
#*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
#*                                                                     *
#*      All rights reserved. Subject to AMI licensing agreement.       *
#*                                                                     *
#***********************************************************************


## @file PeiVideoTextOut.inf
#  This PEIM produces PPI for Video display and creates the HOB for the 
#  Video Controller to Pass the data from PEI to DXE. 
##

[defines]
  INF_VERSION       = 0x00010005
  BASE_NAME         = PeiVideoTextOut
  FILE_GUID         = AE2020DF-C175-4344-B755-BBA47744F8B1
  MODULE_TYPE       = PEIM
  VERSION_STRING    = 1.0
  ENTRY_POINT       = PeiVideoSimpleTextOutEntryPoint

[Sources]
  PeiVideoTextOut.c
  PeiVideoTextOut.h

[Packages]  
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  AmiModulePkg/AmiModulePkg.dec
  AmiModulePkg/EarlyConsoleOut/AmiEarlyConsoleOutPkg.dec 
  $(PEI_VIDEO_CALLBACK_PACKAGE_DEC)
  
[LibraryClasses]
  PeimEntryPoint
  DebugLib
  HobLib
  PrintLib
  VideoTextOutLib
  PeiVideoInitLib

[Ppis]
  gAmiSimpleTextOutPpiGuid
  gEfiEndOfPeiSignalPpiGuid
  $(PEI_VIDEO_CALLBACK_GUID)
  
[Guids]
  gAmiSimpleTextOutHobGuid
[Pcd]
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdVideoSimpleTextOutPpiInstance
  gAmiModulePkgTokenSpaceGuid.PcdDefaultCursorState
  
[Depex]
  TRUE
