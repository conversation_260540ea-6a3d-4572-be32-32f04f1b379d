//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2025, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************
 
/** @file RasSmbiosLibBrh.c
    Translate Error Log entry from AMD MCA and NBIO format to Smbios type 15

**/

#include <AmiDxeLib.h>
#include <Protocol/SmBiosElogSupport.h>
#include <Library/RasCommonLib.h>
#include <Library/RasLibBrh.h>
#include <AmdRas.h>
#include <Library/CpmRasLib.h>
#include <Library/RasIpmiLibBrh.h>
#include <Library/RasSmbiosLibBrh.h>
#include <AmdRasRegistersBrh.h>

/**
    Translate/convert, if possible, error entry from Processor to Smbios format

    @param *McaErrorRecord - pointer to MCA error structure
    @param *SmbiosErrorBuffer - buffer with smbios error entry, only data
    @param BankIndex - MCA bank index
    @param ProcessorNumber - logical CPU number
    @param GenericProcErrEntryBuffer - pointer to GenericErrorEntryBuffer

    @retval EFI_SUCCESS - Smbios error log entry created
    @retval EFI_INVALID_PARAMETER - input param contains null pointer
    @retval EFI_NOT_FOUND - translation cannot be done
**/

EFI_STATUS
ProcessorToSmbiosBrh (
    IN     RAS_MCA_ERROR_INFO_V2    *McaErrorRecord,
    IN OUT UINT8                    *SmbiosErrorBuffer,
    IN  UINT8                       BankIndex,
    IN  UINTN                       ProcessorNumber,
    IN  GENERIC_PROC_ERR_ENTRY_V3   *GenericProcErrEntryBuffer
    )
{      
    if (EFI_ERROR (InitErrorLogBuffer((VOID *)McaErrorRecord, (VOID *)SmbiosErrorBuffer))) {
        return  EFI_INVALID_PARAMETER; 
    }
    
    // check for the branch from UMC_WRITEDATAPOISONERR
    if(McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_UMC_ID){
        // 
        SmbiosErrorBuffer[0] = EFI_EVENT_LOG_TYPE_CPU_FAILURE; // CPU
        SmbiosErrorBuffer[1] = 0x06; // TEMP Smbios handle = 0x0006
        SmbiosErrorBuffer[2] = 0x00;
        return EFI_SUCCESS;
    }else{
        return McaToSmbiosBrh(McaErrorRecord, SmbiosErrorBuffer, BankIndex);
    }
}

/**
    Translate/convert, if possible, error entry from MCA to Smbios format

    @param RAS_MCA_ERROR_INFO_V2    *McaErrorRecord - pointer to MCA error structure
    @param UINT8              *SmbiosErrorBuffer - buffer with smbios error entry, only data
    @param UINT8                BankIndex - MCA bank index

    @retval EFI_SUCCESS - Smbios error log entry created
    @retval EFI_INVALID_PARAMETER - input param contains null pointer
    @retval EFI_NOT_FOUND - translation cannot be done
**/

EFI_STATUS
McaToSmbiosBrh (
    IN     RAS_MCA_ERROR_INFO_V2  *McaErrorRecord,
    IN OUT UINT8                  *SmbiosErrorBuffer,
    IN     UINT8                  BankIndex )
{
    EFI_STATUS  Status = EFI_NOT_FOUND;
    UINT8       ExErrCode;
    UINT8       UMCID;
    
    DEBUG((DEBUG_INFO, "[AmiRAS] SMM McaToSmbiosBrh entry\n"));
    
    if (EFI_ERROR (InitErrorLogBuffer((VOID *)McaErrorRecord, (VOID *)SmbiosErrorBuffer))) {
        return  EFI_INVALID_PARAMETER; 
    }

    if((UINT8)McaErrorRecord->McaBankCount < (BankIndex + 1)){
        return  EFI_INVALID_PARAMETER;
    }

    if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_UMC_ID) {
        if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == UMC_MCA_TYPE) && \
                        (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val) && \
                        (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.ErrorCodeExt == UMC_DCQSRAMECCERR)\
                        ){
            //Use IPID Instance ID check the UMC channel number.
            UMCID = McaInstanceIdSearch(McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr);

            SmbiosErrorBuffer[0] = GPNV_OEM_TYPE; 
            SmbiosErrorBuffer[1] = OEM_TYPE_UMC;
            SmbiosErrorBuffer[2] = (McaErrorRecord->CpuInfo.SocketId << 4) | UMCID;
      
            Status = EFI_SUCCESS;
        }
        else
        {
            DEBUG((DEBUG_INFO, "[AmiRAS] SMM Calling MemToSmbiosBrh....\n"));
            return MemToSmbiosBrh(McaErrorRecord, SmbiosErrorBuffer, BankIndex, McaErrorRecord->CpuInfo.ProcessorNumber, NULL); 
        }
        
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_DATA_FABRIC_ID) && \
          (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == PIE_MCA_TYPE)) {
        
        ExErrCode = ((UINT8)McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.ErrorCodeExt & 0xF);
        SmbiosErrorBuffer[3] = ExErrCode;
        if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)){
            // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
            // ---------------------------- start --------------------------------------------------
            SmbiosErrorBuffer[0] = GPNV_OEM_TYPE; 
            SmbiosErrorBuffer[1] = OEM_TYPE_PIE << 4;;
            SmbiosErrorBuffer[2] = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
            if(McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.SyndV == 1){
                // Correctable / Uncorrectable error
                if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaSyndMsr.Field.ErrorInformation & 0x3) == 0x01) {
                    // Correctable
                    SmbiosErrorBuffer[1] |= 0x1;
                } else {
                    // Uncorrectable
                    SmbiosErrorBuffer[1] |= 0x2;
                }
            } else { // Syndrome not valid
                if(McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 1){
                    // Uncorrectable
                    SmbiosErrorBuffer[1] |= 0x2;
                }else{
                    // Correctable
                    SmbiosErrorBuffer[1] |= 0x1;
                }
            }
            // ---------------------------- end ----------------------------------------------------
            Status = EFI_SUCCESS;
        }
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_NBIO_ID) && \
            (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == NBIO_MCA_TYPE) && \
            (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)&& \
            (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.ErrorCodeExt != NBIO_PCIE_SIDEBAND)) {
            // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list    
            SmbiosErrorBuffer[0] = EFI_EVENT_LOG_TYPE_PCI_PARITY_ERROR;
            SmbiosErrorBuffer[1] = OEM_TYPE_NBIO << 4;;
            SmbiosErrorBuffer[2] = 0x00;
            Status = EFI_SUCCESS;
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_SMU_ID) && \
            (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == SMU_MCA_TYPE) && \
            (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
            // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
            SmbiosErrorBuffer[0] = EFI_EVENT_LOG_TYPE_CPU_FAILURE;
            SmbiosErrorBuffer[1] = OEM_TYPE_SMU << 4;;
            SmbiosErrorBuffer[2] = 0x00;
            Status = EFI_SUCCESS;
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_SMU_ID) && \
            (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == MP5_MCA_TYPE) && \
            (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
            // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
            SmbiosErrorBuffer[0] = EFI_EVENT_LOG_TYPE_CPU_FAILURE;
            SmbiosErrorBuffer[1] = OEM_TYPE_MP5 << 4;;
            SmbiosErrorBuffer[2] = 0x00;
            Status = EFI_SUCCESS;
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_SMU_ID) && \
            (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == MPDMA_MCA_TYPE) && \
            (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
            // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
            SmbiosErrorBuffer[0] = EFI_EVENT_LOG_TYPE_CPU_FAILURE;
            SmbiosErrorBuffer[1] = OEM_TYPE_MPDMA << 4;;
            SmbiosErrorBuffer[2] = 0x00;
            Status = EFI_SUCCESS;
    } else if (((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_PCS_GMI_ID) || \
            (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_KPX_GMI_ID)) && \
            (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
            // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
            SmbiosErrorBuffer[0] = GPNV_OEM_TYPE;
            SmbiosErrorBuffer[1] = OEM_TYPE_GMI << 4;;
            SmbiosErrorBuffer[2] = 0x00;
            Status = EFI_SUCCESS;
    } else if (((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_PCS_XGMI_ID) || \
            (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_KPX_SERDES_ID))   && \
            (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
            // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
            SmbiosErrorBuffer[0] = GPNV_OEM_TYPE;
            SmbiosErrorBuffer[1] = OEM_TYPE_XGMI << 4;;
            SmbiosErrorBuffer[2] = 0x00;
            Status = EFI_SUCCESS;        
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_SATA_ID) && \
            (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == SATA_MCA_TYPE) && \
            (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
            // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
            SmbiosErrorBuffer[0] = GPNV_OEM_TYPE;
            SmbiosErrorBuffer[1] = OEM_TYPE_SATA << 4;;
            SmbiosErrorBuffer[2] = 0x00;
            Status = EFI_SUCCESS;
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_USB_ID) && \
            (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == USB_MCA_TYPE) && \
            (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
            // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
            SmbiosErrorBuffer[0] = GPNV_OEM_TYPE;
            SmbiosErrorBuffer[1] = OEM_TYPE_USB << 4;;
            SmbiosErrorBuffer[2] = 0x00;
            Status = EFI_SUCCESS;
    }else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_PSP_ID) && \
            (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == PSP_MCA_TYPE) && \
            (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
            // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
            SmbiosErrorBuffer[0] = GPNV_OEM_TYPE;
            SmbiosErrorBuffer[2] = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
            
            if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.ErrorCodeExt == 0x3F){
                SmbiosErrorBuffer[1] = OEM_TYPE_WAFL;
            }
            else if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.ErrorCodeExt <= 0x17){
                SmbiosErrorBuffer[1] = OEM_TYPE_PSP;
            }
            else {
                //
                // Other error types
                // 
                return EFI_NOT_FOUND;
            }

            Status = EFI_SUCCESS;
    }
    return  Status;
}

/**
    Translate/convert, if possible, error entry from memory MCA to Smbios format

    @param RAS_MCA_ERROR_INFO_V2    *McaErrorRecord - pointer to MCA error structure
    @param UINT8              *SmbiosErrorBuffer - buffer with smbios error entry, only data
    @param UINT8                BankIndex - MCA bank index
    @param UINTN    ProcessorNumber - logical CPU number
    @param GENERIC_MEM_ERR_ENTRY_V3 *GenericMemErrEntryBuffer - pointer to generic Memory Error entry

    @retval EFI_SUCCESS - Smbios error log entry created
    @retval EFI_INVALID_PARAMETER - input param contains null pointer
    @retval EFI_NOT_FOUND - translation cannot be done
**/
EFI_STATUS
MemToSmbiosBrh (
    IN     RAS_MCA_ERROR_INFO_V2    *McaErrorRecord,
    IN OUT UINT8                    *SmbiosErrorBuffer,
    IN     UINT8                    BankIndex,
    IN     UINTN                    ProcessorNumber,
    IN     GENERIC_MEM_ERR_ENTRY_V3 *GenericMemErrEntryBuffer
    )
{
    EFI_STATUS  Status = EFI_NOT_FOUND;
    UINTN       EccType = 0;
    UINT8       Symbol;
    UINT16      Syndrome;
    UINT8       DramErrorType;
    
    if (EFI_ERROR (InitErrorLogBuffer((VOID *)McaErrorRecord, (VOID *)SmbiosErrorBuffer))) {
        return  EFI_INVALID_PARAMETER; 
    }

    DramErrorType = (UINT8)McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.ErrorCodeExt;
    if (DramErrorType == DramEccErr ) {
        //
        // Check if this is a Corrected Error
        //
        if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaSyndMsr.Field.ErrorPriority == MCA_ERROR_CORRECTED) {
            //
            // Check if syndrome valid
            //
            if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaSyndMsr.Field.Length != 0){
                Symbol = (UINT8)((McaErrorRecord->McaBankErrorInfo[BankIndex].McaSyndMsr.Field.ErrorInformation >> 8)& 0x3F - 1);
                Syndrome = (UINT16)(McaErrorRecord->McaBankErrorInfo[BankIndex].McaSyndMsr.Field.Syndrome);
                    if (IsMultiBitErrorBrh(Symbol, Syndrome) == TRUE) {
                    EccType = 2;
                } else {
                    EccType = 1;
                }
            }
        }
        if (EccType == 1) {
            SmbiosErrorBuffer[0] = EFI_EVENT_LOG_TYPE_SINGLE_BIT_ECC_MEMORY_ERROR;
        } else if (EccType == 2) {
            SmbiosErrorBuffer[0] = EFI_EVENT_LOG_TYPE_MULTI_BIT_ECC_MEMORY_ERROR;
        } else {
            SmbiosErrorBuffer[0] = EFI_EVENT_LOG_TYPE_PARITY_MEMORY_ERROR;
        }
        
        SmbiosErrorBuffer[1] = MEM_TYPE_ECC << 4;
        SmbiosErrorBuffer[2] = 0x00;
        Status = EFI_SUCCESS;
        
    } else if (DramErrorType == AddressCommandParityErr) {
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        SmbiosErrorBuffer[0] = GPNV_OEM_TYPE; // OEM type
        SmbiosErrorBuffer[1] = MEM_TYPE_PARITY << 4;
        SmbiosErrorBuffer[2] = 0x01;
        Status = EFI_SUCCESS;
        
    } else if (DramErrorType == WriteDataCrcErr) {
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        SmbiosErrorBuffer[0] = GPNV_OEM_TYPE; // OEM type
        SmbiosErrorBuffer[1] = MEM_TYPE_CRC << 4;
        SmbiosErrorBuffer[2] = 0x01;
        Status = EFI_SUCCESS;
        
    } else {
        //
        // Other error types
        // 
        Status = EFI_NOT_FOUND;
    }
    
    return  Status;
}

/**
    Translate/convert, if possible, error entry from NBIO to Smbios format

    @param RAS_NBIO_ERROR_INFO *NbioErrorRecord - pointer to NBIO error structure
    @param UINT8              *SmbiosErrorBuffer - buffer with smbios error entry, only data

    @retval EFI_SUCCESS - Smbios error log entry created
    @retval EFI_INVALID_PARAMETER - input param contains null pointer
    @retval EFI_NOT_FOUND - translation cannot be done
**/

EFI_STATUS
NbioToSmbiosBrh(
    IN     RAS_NBIO_ERROR_INFO  *NbioErrorRecord,
    IN OUT UINT8                *SmbiosErrorBuffer )
{
    if (EFI_ERROR (InitErrorLogBuffer((VOID *)NbioErrorRecord, (VOID *)SmbiosErrorBuffer))) {
        return  EFI_INVALID_PARAMETER; 
    }

    SmbiosErrorBuffer[0] = EFI_EVENT_LOG_TYPE_PCI_PARITY_ERROR;
    SmbiosErrorBuffer[1] = OEM_TYPE_NBIO << 4;;
    SmbiosErrorBuffer[2] = 0x00;

    return EFI_SUCCESS;
}
#if 0
/**
    Translate/convert, if possible, error entry from NBIF to Smbios format

    @param RAS_NBIO_ERROR_INFO *NbioErrorRecord - pointer to NBIF error structure
    @param UINT8              *SmbiosErrorBuffer - buffer with smbios error entry, only data

    @retval EFI_SUCCESS - Smbios error log entry created
    @retval EFI_INVALID_PARAMETER - input param contains null pointer
    @retval EFI_NOT_FOUND - translation cannot be done
**/

EFI_STATUS
NbifToSmbiosBrh (
    IN     RAS_NBIO_ERROR_INFO  *NbioErrorRecord,
    IN OUT UINT8                *SmbiosErrorBuffer )
{
    if (EFI_ERROR (InitErrorLogBuffer((VOID *)NbioErrorRecord, (VOID *)SmbiosErrorBuffer))) {
        return  EFI_INVALID_PARAMETER; 
    }

    SmbiosErrorBuffer[0] = EFI_EVENT_LOG_TYPE_PCI_PARITY_ERROR;
    SmbiosErrorBuffer[1] = OEM_TYPE_NBIF << 4;;
    SmbiosErrorBuffer[2] = (UINT8)(NbioErrorRecord->NbioGlobalStatusHi >> 18);

    return EFI_SUCCESS;
}
#endif

/**
    Translate/convert, if possible, error entry from NBIO to Smbios format

    @param VOID     *PcieErrorEntry - pointer to GENERIC_PCIE_AER_ERR_ENTRY_V3 structure
    @param UINT8    *SmbiosErrorBuffer - buffer with smbios error entry, only data

    @retval EFI_SUCCESS - Smbios error log entry created
    @retval EFI_INVALID_PARAMETER - input param contains null pointer
    @retval EFI_NOT_FOUND - translation cannot be done
**/

EFI_STATUS
PcieToSmbiosBrh (
  	IN     VOID     *PcieErrorEntry,
    IN OUT UINT8    *SmbiosErrorBuffer )
{
    GENERIC_PCIE_AER_ERR_ENTRY_V3   *GenPcie;

    if (EFI_ERROR (InitErrorLogBuffer((VOID *)PcieErrorEntry, (VOID *)SmbiosErrorBuffer))) {
        return  EFI_INVALID_PARAMETER; 
    }
    
    GenPcie = (GENERIC_PCIE_AER_ERR_ENTRY_V3*)PcieErrorEntry;

    if (GenPcie->GenErrorDataEntry.ErrorSeverity == ERROR_RECOVERABLE ||
            GenPcie->GenErrorDataEntry.ErrorSeverity == ERROR_SEVERITY_FATAL){
        SmbiosErrorBuffer[0] = EFI_EVENT_LOG_TYPE_PCI_SYSTEM_ERROR;
    } else {
        SmbiosErrorBuffer[0] = EFI_EVENT_LOG_TYPE_PCI_PARITY_ERROR;
    }
    SmbiosErrorBuffer[1] = 0x06; // TEMP Smbios handle = 0x0006
    SmbiosErrorBuffer[2] = 0x00;

    return EFI_SUCCESS;
}


/**
    Translate/convert, if possible, error entry from Mem to SMBIOS format

    @param GENERIC_MEM_ERR_ENTRY_V3 *GenMemErroEntry - pointer to GENERIC_MEM_ERR_ENTRY_V2 structure
    @param UINT8              *ErrorBuffer - buffer with smbios error entry, only data

    @retval EFI_SUCCESS - SMBIOS error log entry created
    @retval EFI_INVALID_PARAMETER - input param contains null pointer
    @retval EFI_NOT_FOUND - translation cannot be done
**/

EFI_STATUS
MemTestToSmbiosBrh (
  IN     GENERIC_MEM_ERR_ENTRY_V3  *GenMemErroEntry,
  IN OUT UINT8                     *SmbiosErrorBuffer,
  IN     UINT16                    Node,
  IN     UINT16                    Card,
  IN     UINT16                    Module
  )
{
    if (EFI_ERROR (InitErrorLogBuffer((VOID *)GenMemErroEntry, (VOID *)SmbiosErrorBuffer))) {
        return  EFI_INVALID_PARAMETER; 
    }
   
   // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
   // ---------------------------- start --------------------------------------------------
   SmbiosErrorBuffer[0] = GPNV_OEM_TYPE;
   SmbiosErrorBuffer[1] = 0xA0; 
   SmbiosErrorBuffer[2] = 0x00; 
   // ---------------------------- end ----------------------------------------------------
   
   return EFI_SUCCESS;
}
#if 0
/**
    Translate/convert, if possible, error entry from FCH to SMBIOS format

    @param GenFchErrEntry - pointer to GENERIC_FCH_ALINK_ERR_ENTRY_V2 structure
    @param ErrorBuffer - buffer with SMBIOS error entry, only data

    @retval EFI_SUCCESS - SMBIOS error log entry created
    @retval EFI_INVALID_PARAMETER - input param contains null pointer
    @retval EFI_NOT_FOUND - translation cannot be done
**/

EFI_STATUS
FCHToSmbiosBrh (
    IN  GENERIC_FCH_ALINK_ERR_ENTRY_V2  *GenFchErrEntry,
    IN OUT  UINT8                       *SmbiosErrorBuffer )
{ 
    if (EFI_ERROR (InitErrorLogBuffer((VOID *)GenFchErrEntry, (VOID *)SmbiosErrorBuffer))) {
        return  EFI_INVALID_PARAMETER; 
    }
 
    // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
    // ---------------------------- start --------------------------------------------------
    SmbiosErrorBuffer[0] = GPNV_OEM_TYPE;
    SmbiosErrorBuffer[1] = (UINT8)(OEM_TYPE_FCH << 4);
    SmbiosErrorBuffer[2] = (UINT8)GenFchErrEntry->FchALinkErrorSection.ErrorSource;
    // ---------------------------- end ----------------------------------------------------
    return EFI_SUCCESS;
}

/**
    Translate/convert, if possible, error entry from SATA to SMBIOS format

    @param GenSataErrEntry - pointer to GENERIC_SATA_ERR_ENTRY_V2 structure
    @param ErrorBuffer - buffer with SMBIOS error entry, only data

    @retval EFI_SUCCESS - SMBIOS error log entry created
    @retval EFI_INVALID_PARAMETER - input param contains null pointer
    @retval EFI_NOT_FOUND - translation cannot be done
**/

EFI_STATUS
SATAToSmbiosBrh (
    IN  GENERIC_SATA_ERR_ENTRY_V2  *GenSataErrEntry,
    IN OUT  UINT8                  *SmbiosErrorBuffer )
{
    if (EFI_ERROR (InitErrorLogBuffer((VOID *)GenSataErrEntry, (VOID *)SmbiosErrorBuffer))) {
        return  EFI_INVALID_PARAMETER; 
    }

    // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
    // ---------------------------- start --------------------------------------------------
    SmbiosErrorBuffer[0] = GPNV_OEM_TYPE;
    SmbiosErrorBuffer[1] = (UINT8)(OEM_TYPE_SATA << 4);
    SmbiosErrorBuffer[2] = (GenSataErrEntry->SataErrorSection.BusId << 8) | (GenSataErrEntry->SataErrorSection.Port);
    // ---------------------------- end ----------------------------------------------------
    return EFI_SUCCESS;
}

/**
    Translate/convert, if possible, error entry from USB to SMBIOS format

    @param *GenSataErrEntry - pointer to GENERIC_USB_ERR_ENTRY_V2 structure
    @param  ErrorBuffer - buffer with SMBIOS error entry, only data

    @retval EFI_SUCCESS - SMBIOS error log entry created
    @retval EFI_INVALID_PARAMETER - input param contains null pointer
    @retval EFI_NOT_FOUND - translation cannot be done

**/

EFI_STATUS
USBToSmbiosBrh (
  IN  GENERIC_USB_ERR_ENTRY_V2  *GenUsbErrEntry,
  IN OUT  UINT8                 *SmbiosErrorBuffer )
{
    if (EFI_ERROR (InitErrorLogBuffer((VOID *)GenUsbErrEntry, (VOID *)SmbiosErrorBuffer))) {
        return  EFI_INVALID_PARAMETER; 
    }
    
    // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
    // ---------------------------- start --------------------------------------------------
    SmbiosErrorBuffer[0] = GPNV_OEM_TYPE;
    SmbiosErrorBuffer[1] = (UINT8)(OEM_TYPE_USB << 4);
    SmbiosErrorBuffer[2] = GenUsbErrEntry->UsbErrorSection.ErrorSource;
    // ---------------------------- end ----------------------------------------------------
    return EFI_SUCCESS;
}
#endif
EFI_STATUS
SMNToSmbiosBrh (
    IN  GENERIC_SMN_ERR_ENTRY_V3  *GenSmnErrEntry,
    IN OUT  UINT8                 *SmbiosErrorBuffer )
{
    if (EFI_ERROR (InitErrorLogBuffer((VOID *)GenSmnErrEntry, (VOID *)SmbiosErrorBuffer))) {
        return  EFI_INVALID_PARAMETER; 
    }

    // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
    // ---------------------------- start --------------------------------------------------
    SmbiosErrorBuffer[0] = GPNV_OEM_TYPE;
    SmbiosErrorBuffer[1] = (UINT8)(OEM_TYPE_SMN << 4);
    SmbiosErrorBuffer[2] = (UINT8)GenSmnErrEntry->SmnErrorSection.ValidationBits.Field.ErrorSource;
    // ---------------------------- end ----------------------------------------------------
    return EFI_SUCCESS;
}

/**
    Translate/convert, if possible, error entry from CXL Error Log to SMBIOS format

    @param GenSmnECxlErrorLogData - Pointer to CXL_ERROR_LOG_DATA structure 
    @param ErrorBuffer -  buffer with SMBIOS error entry, only data
    @param GenCxlErrEntry - Pointer to GENERIC_CXL_ERR_ENTRY_V3 structure
   
    @retval EFI_SUCCESS - SMBIOS error log entry created
    @retval EFI_INVALID_PARAMETER - input param contains null pointer
    @retval EFI_NOT_FOUND - translation cannot be done

**/
EFI_STATUS
CxlProtocolToSmbiosBrh (
    IN     CXL_ERROR_LOG_DATA       *GenSmnECxlErrorLogData,
    IN OUT UINT8                    *ErrorBuffer,
    IN     GENERIC_CXL_ERR_ENTRY_V3 *GenCxlErrEntry
    )
{
    if (EFI_ERROR (InitErrorLogBuffer((VOID *)GenSmnECxlErrorLogData, (VOID *)ErrorBuffer))) {
        return  EFI_INVALID_PARAMETER; 
    }
       
    ErrorBuffer[0] = OEM_TYPE_CXL_PROTOCOL;
    
    // Store the Error Type in MSB of ErrorBuffer[1]
    ErrorBuffer[1] |= SEL_CXL_SENSOR_PROTOCOL_ERROR << 4;
    
    //Store Error Severity in LSB of ErrorBuffer[1]
    if (GenCxlErrEntry->GenErrorDataEntry.ErrorSeverity == ERROR_RECOVERABLE || GenCxlErrEntry->GenErrorDataEntry.ErrorSeverity == ERROR_SEVERITY_FATAL) {
        ErrorBuffer[1] |= SEL_CXL_SENSOR_UNCORRECTABLE_ERROR;
    } else if (GenCxlErrEntry->GenErrorDataEntry.ErrorSeverity == ERROR_SEVERITY_CORRECTED) {
        ErrorBuffer[1] |= SEL_CXL_SENSOR_CORRECTABLE_ERROR;
    } else {
        ErrorBuffer[1] |= SEL_CXL_SENSOR_CORRECTABLE_ERROR ;
    }
    
    //Device Type
    ErrorBuffer[1] |= (UINT8)(GenCxlErrEntry->CxlErrorSection.CxlAgentType << 1);
        
    //Store Cache/Mem error
    ErrorBuffer[1] |= (UINT8)(CXL_ELOG_CACHE_MEM << 2);
    
    //Store Bus, device and function number
    ErrorBuffer[2] = (UINT8)GenCxlErrEntry->CxlErrorSection.CxlAgentAddress.Device.BusNum ;
    ErrorBuffer[3]= (UINT8)(GenCxlErrEntry->CxlErrorSection.CxlAgentAddress.Device.DeviceNum << 3 | 
                             GenCxlErrEntry->CxlErrorSection.CxlAgentAddress.Device.FunctionNum);
        
    return EFI_SUCCESS;   
}

/**
    Translate/convert, if possible, error entry from CXL Error Log to SMBIOS format

    @param GenSmnECxlErrorLogData - Pointer to CXL_ERROR_LOG_DATA structure 
    @param ErrorBuffer -  buffer with SMBIOS error entry, only data
    @param GenCxlErrEntry - Pointer to GENERIC_CXL_ERR_ENTRY_V3 structure

    @retval EFI_SUCCESS - SMBIOS error log entry created
    @retval EFI_INVALID_PARAMETER - input param contains null pointer
    @retval EFI_NOT_FOUND - translation cannot be done

**/
EFI_STATUS
CxlComponentToSmbiosBrh (
    IN     CXL_ERROR_LOG_DATA       *GenSmnECxlErrorLogData,
    IN OUT UINT8                    *ErrorBuffer,
    IN     GENERIC_CXL_ERR_ENTRY_V3 *GenCxlErrEntry
    )
{
    if (EFI_ERROR (InitErrorLogBuffer((VOID *)GenSmnECxlErrorLogData, (VOID *)ErrorBuffer))) {
        return  EFI_INVALID_PARAMETER; 
    }
           
    ErrorBuffer[0] = OEM_TYPE_CXL_COMPONENT;
        
    // Store the Error Type in MSB of ErrorBuffer[1]
    ErrorBuffer[1] |= SEL_CXL_SENSOR_COMPONENT_ERROR << 4;
        
    //Store Error Severity in LSB of ErrorBuffer[1]
    if (GenCxlErrEntry->GenErrorDataEntry.ErrorSeverity == ERROR_RECOVERABLE || GenCxlErrEntry->GenErrorDataEntry.ErrorSeverity == ERROR_SEVERITY_FATAL) {
        ErrorBuffer[1] |= SEL_CXL_SENSOR_UNCORRECTABLE_ERROR;
    } else if (GenCxlErrEntry->GenErrorDataEntry.ErrorSeverity == ERROR_SEVERITY_CORRECTED) {
        ErrorBuffer[1] |= SEL_CXL_SENSOR_CORRECTABLE_ERROR;
    } else {
        ErrorBuffer[1] |= SEL_CXL_SENSOR_CORRECTABLE_ERROR ;
    }
        
    //Device Type
    ErrorBuffer[1] |= (UINT8)(GenCxlErrEntry->CxlErrorSection.CxlAgentType << 1);
            
    //Store Component error
    ErrorBuffer[1] |= (UINT8)(CXL_ELOG_COMPONENT_EVENT << 2);
        
    //Store Bus, device and function number
    ErrorBuffer[2] = (UINT8)GenCxlErrEntry->CxlErrorSection.CxlAgentAddress.Device.BusNum ;
    ErrorBuffer[3]= (UINT8)(GenCxlErrEntry->CxlErrorSection.CxlAgentAddress.Device.DeviceNum << 3 | 
                                 GenCxlErrEntry->CxlErrorSection.CxlAgentAddress.Device.FunctionNum);
            
    return EFI_SUCCESS;
}

EFI_STATUS
PmicToSmbiosBrh (
    IN  UINT8                     Socket,
    IN  UINT8                     Channel,
    IN  UINT8                     Dimm,
    IN OUT  UINT8                 *SmbiosErrorBuffer, 
    IN  GENERIC_PMIC_ERR_ENTRY_V3 *PlatformPmicErrEntry)
{
    if (EFI_ERROR (InitErrorLogBuffer((VOID *)PlatformPmicErrEntry, (VOID *)SmbiosErrorBuffer))) {
        return  EFI_INVALID_PARAMETER; 
    }

    // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
    // ---------------------------- start --------------------------------------------------
    SmbiosErrorBuffer[0] = GPNV_OEM_TYPE;
    SmbiosErrorBuffer[1] = (UINT8)OEM_TYPE_PMIC;
    // SmbiosErrorBuffer[2]: BIT5 - Socket, BIT4 - DIMM, BIT[3..0] - Channel
    SmbiosErrorBuffer[2] |= (UINT8)Channel;
    if(Socket == 1){
        SmbiosErrorBuffer[2] |= BIT5; 
    }
    if(Dimm == 1){
        SmbiosErrorBuffer[2] |= BIT4; 
    }
    // ---------------------------- end ----------------------------------------------------
    return EFI_SUCCESS;
}
