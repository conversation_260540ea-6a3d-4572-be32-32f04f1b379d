#pragma message( "Compal Server Override Compiling-" __FILE__ )
//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2025, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************
 
/** @file AmiOemRasDxe.c
    ErrorLog helper driver   

**/

#include "Token.h"
//#include <AmiDxeLib.h>
#include <Library/DebugLib.h>
#include <Library/UefiLib.h>
#include <Library/BaseMemoryLib.h>
#include "AmiOemRasDxe.h"
#include <Protocol/GenericElogProtocol.h>
#include <AmiRasCommon.h>
#include <Library/RasIpmiLibBrh.h>
#include <Library/RasSmbiosLibBrh.h>

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */

UINT8                       ErrorLogData[FixedPcdGet8 (PcdErrorLogDataBufferSize)];
COMMON_ELOG_PRIVATE         CommonElogPrivate;
EFI_SM_ELOG_REDIR_PROTOCOL  *EfiRedirElogProtocol;
VOID                        *gRegistration;
PLATFORM_APEI_PRIVATE_BUFFER_V3 *mPlatformApeiData = NULL; //COMPAL_CHANGE

AMD_CPM_RAS_OEM_PROTOCOL AmdCpmRasOemProtocol = {
  OemErrorLogEventMemTest,
  OemErrorLogEventPmic,
  OemErrorLogEventCxlProtocol,
  OemErrorLogEventCxlComponent,
  OemErrorLogEventMem,
  OemErrorLogEventNbio,
  OemErrorLogEventPcie,
  OemErrorLogEventSMN,
  OemErrorLogEventMca,
  OemErrorLogEventProcessor,
  OemErrorLogEventRtPpr,
  OemErrorLogEventCCDBIST
};

extern  EFI_BOOT_SERVICES       *gBS;


EFI_STATUS
OemErrorLogEventMemTest (
  IN  UINT16                   Node,
  IN  UINT16                   Card,
  IN  UINT16                   Module,
  IN  GENERIC_MEM_ERR_ENTRY_V3 *MemTestErrEntry
)
{
    EFI_STATUS                  Status;
    EFI_SM_ELOG_REDIR_PROTOCOL  *CurrRedirProtocol;
    UINT64                      RecordId;
    UINTN                       EventDataSize;
    UINTN                       EntryIndex;
    
    DEBUG((DEBUG_INFO, "[AmiRAS]DXE OemErrorLogEventMemTest entry\n"));
    
    Status = MemTestToIpmiBrh (MemTestErrEntry, (UINT8*)&ErrorLogData[0], Node, Card, Module);
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmIPMI) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                if (CurrRedirProtocol != NULL) {
                    EventDataSize = 0;
                    CurrRedirProtocol->SetEventLogData(
                                                    CurrRedirProtocol,   // This
                                                    &ErrorLogData[0],    // *ElogData
                                                    EfiElogSmIPMI,       // DataType
                                                    FALSE,               // AlertEvent
                                                    EventDataSize,       // DataSize
                                                    &RecordId            // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "SendElogEventNBIO: SetEventLogData for Ipmi-%r\n", Status));
                }
                break;
            }
        }
    }    
#if ( defined(SMBIOS_RAS_ELOG) && (SMBIOS_RAS_ELOG != 0))
    Status = MemTestToSmbiosBrh (MemTestErrEntry, (UINT8*)&ErrorLogData[0], Node, Card, Module);
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmSMBIOS) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                EventDataSize = 4;
                if (CurrRedirProtocol != NULL) {
                    Status = CurrRedirProtocol->SetEventLogData (
                                                    CurrRedirProtocol, // This
                                                    &ErrorLogData[0],  // *ElogData
                                                    EfiElogSmSMBIOS,   // DataType
                                                    FALSE,             // AlertEvent
                                                    EventDataSize,     // DataSize
                                                    &RecordId          // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "OemErrorLogEventPcie: SetEventLogData for Smbios-%r\n", Status));
                    break;
                }
            }
        }
    }
#endif    
    return  EFI_SUCCESS;
}

EFI_STATUS
EFIAPI
OemErrorLogEventPmic (
  IN  UINT8                    Socket,
  IN  UINT8                    Channel,
  IN  UINT8                    Dimm,
  IN  GENERIC_PMIC_ERR_ENTRY_V3 *PlatformPmicErrEntry
)
{
    EFI_STATUS                  Status;
    EFI_SM_ELOG_REDIR_PROTOCOL  *CurrRedirProtocol;
    UINT64                      RecordId;
    UINTN                       EventDataSize;
    UINTN                       EntryIndex;
        
    DEBUG((DEBUG_INFO, "[AmiRAS]DXE OemErrorLogEventPmic entry\n"));
    
    Status = PmicToIpmiBrh (Socket, Channel, Dimm, (UINT8*)&ErrorLogData[0], PlatformPmicErrEntry);
    DEBUG((DEBUG_INFO, "[AmiRAS]DXE PmicToIpmiBrh Status=%r\n", Status));
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmIPMI) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                if (CurrRedirProtocol != NULL) {
                    EventDataSize = 0;
                    CurrRedirProtocol->SetEventLogData(
                                                    CurrRedirProtocol,   // This
                                                    &ErrorLogData[0],    // *ElogData
                                                    EfiElogSmIPMI,       // DataType
                                                    FALSE,               // AlertEvent
                                                    EventDataSize,       // DataSize
                                                    &RecordId            // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "SendElogEventPmic: SetEventLogData for Ipmi-%r\n", Status));
                }
                break;
            }
        }
    }    
#if ( defined(SMBIOS_RAS_ELOG) && (SMBIOS_RAS_ELOG != 0))
    Status = PmicToSmbiosBrh (Socket, Channel, Dimm, (UINT8*)&ErrorLogData[0], PlatformPmicErrEntry);
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmSMBIOS) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                EventDataSize = 4;
                if (CurrRedirProtocol != NULL) {
                    Status = CurrRedirProtocol->SetEventLogData (
                                                    CurrRedirProtocol, // This
                                                    &ErrorLogData[0],  // *ElogData
                                                    EfiElogSmSMBIOS,   // DataType
                                                    FALSE,             // AlertEvent
                                                    EventDataSize,     // DataSize
                                                    &RecordId          // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "OemErrorLogEventPmic: SetEventLogData for Smbios-%r\n", Status));
                    break;
                }
            }
        }
    }
#endif    
    return  EFI_SUCCESS;
}

EFI_STATUS
OemErrorLogEventCxlProtocol (
  IN  VOID        *CxlErrorLogData,
  IN  VOID        *GenCxlErrEntry
)
{
    EFI_STATUS                  Status;
    EFI_SM_ELOG_REDIR_PROTOCOL  *CurrRedirProtocol;
    UINT64                      RecordId;
    UINTN                       EventDataSize;
    UINTN                       EntryIndex;
        
    DEBUG((DEBUG_INFO, "[AmiRAS]DXE OemErrorLogEventCxlProtocol entry\n"));
    
    Status = CxlProtocolToIpmiBrh (CxlErrorLogData, (UINT8*)&ErrorLogData[0], GenCxlErrEntry);
    DEBUG((DEBUG_INFO, "[AmiRAS]DXE CxlProtocolToIpmiBrh Status=%r\n", Status));
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmIPMI) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                if (CurrRedirProtocol != NULL) {
                    EventDataSize = 0;
                    CurrRedirProtocol->SetEventLogData(
                                                    CurrRedirProtocol,   // This
                                                    &ErrorLogData[0],    // *ElogData
                                                    EfiElogSmIPMI,       // DataType
                                                    FALSE,               // AlertEvent
                                                    EventDataSize,       // DataSize
                                                    &RecordId            // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "SendElogEventCxlProtocol: SetEventLogData for Ipmi-%r\n", Status));
                }
                break;
            }
        }
    }    
#if ( defined(SMBIOS_RAS_ELOG) && (SMBIOS_RAS_ELOG != 0))
    Status = CxlProtocolToSmbiosBrh (CxlErrorLogData, (UINT8*)&ErrorLogData[0], GenCxlErrEntry);
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmSMBIOS) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                EventDataSize = 4;
                if (CurrRedirProtocol != NULL) {
                    Status = CurrRedirProtocol->SetEventLogData (
                                                    CurrRedirProtocol, // This
                                                    &ErrorLogData[0],  // *ElogData
                                                    EfiElogSmSMBIOS,   // DataType
                                                    FALSE,             // AlertEvent
                                                    EventDataSize,     // DataSize
                                                    &RecordId          // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "OemErrorLogEventCxlProtocol: SetEventLogData for Smbios-%r\n", Status));
                    break;
                }
            }
        }
    }
#endif
    return  EFI_SUCCESS;
}

EFI_STATUS
OemErrorLogEventCxlComponent (
  IN  VOID        *CxlErrorLogData,
  IN  VOID        *GenCxlErrEntry
)
{
    EFI_STATUS                  Status;
    EFI_SM_ELOG_REDIR_PROTOCOL  *CurrRedirProtocol;
    UINT64                      RecordId;
    UINTN                       EventDataSize;
    UINTN                       EntryIndex;
    
    DEBUG((DEBUG_INFO, "[AmiRAS]DXE OemErrorLogEventCxlComponent entry\n"));
    
    Status = CxlComponentToIpmiBrh (CxlErrorLogData, (UINT8*)&ErrorLogData[0], GenCxlErrEntry);
    DEBUG((DEBUG_INFO, "[AmiRAS]DXE CxlComponentToIpmiBrh Status=%r\n", Status));
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmIPMI) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                if (CurrRedirProtocol != NULL) {
                    EventDataSize = 0;
                    CurrRedirProtocol->SetEventLogData(
                                                    CurrRedirProtocol,   // This
                                                    &ErrorLogData[0],    // *ElogData
                                                    EfiElogSmIPMI,       // DataType
                                                    FALSE,               // AlertEvent
                                                    EventDataSize,       // DataSize
                                                    &RecordId            // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "SendElogEventCxlComponent: SetEventLogData for Ipmi-%r\n", Status));
                }
                break;
            }
        }
    }    
#if ( defined(SMBIOS_RAS_ELOG) && (SMBIOS_RAS_ELOG != 0))
    Status = CxlComponentToSmbiosBrh (CxlErrorLogData, (UINT8*)&ErrorLogData[0], GenCxlErrEntry);
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmSMBIOS) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                EventDataSize = 4;
                if (CurrRedirProtocol != NULL) {
                    Status = CurrRedirProtocol->SetEventLogData (
                                                    CurrRedirProtocol, // This
                                                    &ErrorLogData[0],  // *ElogData
                                                    EfiElogSmSMBIOS,   // DataType
                                                    FALSE,             // AlertEvent
                                                    EventDataSize,     // DataSize
                                                    &RecordId          // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "OemErrorLogEventCxlComponent: SetEventLogData for Smbios-%r\n", Status));
                    break;
                }
            }
        }
    }
#endif
    return  EFI_SUCCESS;
}

EFI_STATUS
OemErrorLogEventMem (
  IN VOID         *RasMcaErrorInfo,
  IN UINT8        BankIndex,
  IN UINTN        ProcessorNumber,
  IN VOID         *GenericMemErrEntryBuffer
)
{
    EFI_STATUS                  Status;
    EFI_SM_ELOG_REDIR_PROTOCOL  *CurrRedirProtocol;
    UINT64                      RecordId;
    UINTN                       EventDataSize;
    UINTN                       EntryIndex;
    
    DEBUG((DEBUG_INFO, "[AmiRAS]DXE OemErrorLogEventMem entry\n"));
    
    Status = MemToIpmiBrh (RasMcaErrorInfo, (UINT8*)&ErrorLogData[0], BankIndex, NULL, NULL, GenericMemErrEntryBuffer);
    DEBUG((DEBUG_INFO, "[AmiRAS] DXE MemToIpmiBrh Status=%r\n", Status));
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmIPMI) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                if (CurrRedirProtocol != NULL) {
                    EventDataSize = 0;
                    CurrRedirProtocol->SetEventLogData(
                                                    CurrRedirProtocol,   // This
                                                    &ErrorLogData[0],    // *ElogData
                                                    EfiElogSmIPMI,       // DataType
                                                    FALSE,               // AlertEvent
                                                    EventDataSize,       // DataSize
                                                    &RecordId            // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "SendElogEventMem: SetEventLogData for Ipmi-%r\n", Status));
                }
                break;
            }
        }
    }    
#if ( defined(SMBIOS_RAS_ELOG) && (SMBIOS_RAS_ELOG != 0))
    Status = MemToSmbiosBrh (RasMcaErrorInfo, (UINT8*)&ErrorLogData[0], BankIndex, ProcessorNumber, GenericMemErrEntryBuffer);
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmSMBIOS) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                EventDataSize = 4;
                if (CurrRedirProtocol != NULL) {
                    Status = CurrRedirProtocol->SetEventLogData (
                                                    CurrRedirProtocol, // This
                                                    &ErrorLogData[0],  // *ElogData
                                                    EfiElogSmSMBIOS,   // DataType
                                                    FALSE,             // AlertEvent
                                                    EventDataSize,     // DataSize
                                                    &RecordId          // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "OemErrorLogEventMem: SetEventLogData for Smbios-%r\n", Status));
                    break;
                }
            }
        }
    }
#endif
    return  EFI_SUCCESS;
}

EFI_STATUS
OemErrorLogEventNbio (
  IN VOID       *RasNbioErrorInfo,
  IN VOID       *GenericNbioErrEntry
)
{
    EFI_STATUS                  Status;
    EFI_SM_ELOG_REDIR_PROTOCOL  *CurrRedirProtocol;
    UINT64                      RecordId;
    UINTN                       EventDataSize;
    UINTN                       EntryIndex; 
    
  
    DEBUG((DEBUG_INFO, "[AmiRAS]DXE OemErrorLogEventNbio entry\n"));
    
    Status = NbioToIpmiBrh (RasNbioErrorInfo, (UINT8*)&ErrorLogData[0]);
    DEBUG((DEBUG_INFO, "[AmiRAS]DXE NbioToIpmiBrh Status=%r\n", Status));
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmIPMI) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                if (CurrRedirProtocol != NULL) {
                    EventDataSize = 0;
                    CurrRedirProtocol->SetEventLogData(
                                                    CurrRedirProtocol,   // This
                                                    &ErrorLogData[0],    // *ElogData
                                                    EfiElogSmIPMI,       // DataType
                                                    FALSE,               // AlertEvent
                                                    EventDataSize,       // DataSize
                                                    &RecordId            // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "SendElogEventNBIO: SetEventLogData for Ipmi-%r\n", Status));
                }
                break;
            }
        }
    }    
#if ( defined(SMBIOS_RAS_ELOG) && (SMBIOS_RAS_ELOG != 0))
    Status = NbioToSmbiosBrh (RasNbioErrorInfo, (UINT8*)&ErrorLogData[0]);
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmSMBIOS) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                EventDataSize = 4;
                if (CurrRedirProtocol != NULL) {
                    Status = CurrRedirProtocol->SetEventLogData (
                                                    CurrRedirProtocol, // This
                                                    &ErrorLogData[0],  // *ElogData
                                                    EfiElogSmSMBIOS,   // DataType
                                                    FALSE,             // AlertEvent
                                                    EventDataSize,     // DataSize
                                                    &RecordId          // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "OemErrorLogEventNbio: SetEventLogData for Smbios-%r\n", Status));
                    break;
                }
            }
        }
    }
#endif
    return  EFI_SUCCESS;
}

EFI_STATUS
OemErrorLogEventPcie (
  IN VOID       *GenPcieAerErrEntry
)
{
    EFI_STATUS                  Status;
    EFI_SM_ELOG_REDIR_PROTOCOL  *CurrRedirProtocol;
    UINT64                      RecordId;
    UINTN                       EventDataSize;
    UINTN                       EntryIndex;
    
    DEBUG((DEBUG_INFO, "[AmiRAS]DXE OemErrorLogEventPcie entry\n"));
    
    Status = PcieToIpmiBrh (GenPcieAerErrEntry, (UINT8*)&ErrorLogData[0]);
    DEBUG((DEBUG_INFO, "[AmiRAS]DXE PcieToIpmiBrh Status=%r\n", Status));
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmIPMI) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                if (CurrRedirProtocol != NULL) {
                    EventDataSize = 0;
                    CurrRedirProtocol->SetEventLogData(
                                                    CurrRedirProtocol,   // This
                                                    &ErrorLogData[0],    // *ElogData
                                                    EfiElogSmIPMI,       // DataType
                                                    FALSE,               // AlertEvent
                                                    EventDataSize,       // DataSize
                                                    &RecordId            // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "SendElogEventPCIe: SetEventLogData for Ipmi-%r\n", Status));
                }
                break;
            }
        }
    }    
#if ( defined(SMBIOS_RAS_ELOG) && (SMBIOS_RAS_ELOG != 0))
    Status = PcieToSmbiosBrh (GenPcieAerErrEntry, (UINT8*)&ErrorLogData[0]);
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmSMBIOS) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                EventDataSize = 4;
                if (CurrRedirProtocol != NULL) {
                    Status = CurrRedirProtocol->SetEventLogData (
                                                    CurrRedirProtocol, // This
                                                    &ErrorLogData[0],  // *ElogData
                                                    EfiElogSmSMBIOS,   // DataType
                                                    FALSE,             // AlertEvent
                                                    EventDataSize,     // DataSize
                                                    &RecordId          // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "OemErrorLogEventPcie: SetEventLogData for Smbios-%r\n", Status));
                    break;
                }
            }
        }
    }
#endif    
    return  EFI_SUCCESS;
}

EFI_STATUS
OemErrorLogEventSMN (
  UINT8                     NbioBusNum,
  UINT32                    PmBreakEvent,
  GENERIC_SMN_ERR_ENTRY_V3  *GenericSmnErrEntry
)
{
    EFI_STATUS                  Status;
    EFI_SM_ELOG_REDIR_PROTOCOL  *CurrRedirProtocol;
    UINT64                      RecordId;
    UINTN                       EventDataSize;
    UINTN                       EntryIndex;
    
    DEBUG((DEBUG_INFO, "[AmiRAS]DXE OemErrorLogEventSMN entry\n"));
    
    Status = SMNToIpmiBrh (GenericSmnErrEntry, (UINT8*)&ErrorLogData[0]);
    DEBUG((DEBUG_INFO, "[AmiRAS]DXE SMNToIpmiBrh Status=%r\n", Status));
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmIPMI) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                if (CurrRedirProtocol != NULL) {
                    EventDataSize = 0;
                    CurrRedirProtocol->SetEventLogData(
                                                    CurrRedirProtocol,   // This
                                                    &ErrorLogData[0],    // *ElogData
                                                    EfiElogSmIPMI,       // DataType
                                                    FALSE,               // AlertEvent
                                                    EventDataSize,       // DataSize
                                                    &RecordId            // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "SendElogEventSMN: SetEventLogData for Ipmi-%r\n", Status));
                }
                break;
            }
        }
    }    
#if ( defined(SMBIOS_RAS_ELOG) && (SMBIOS_RAS_ELOG != 0))
    Status = SMNToSmbiosBrh (GenericSmnErrEntry, (UINT8*)&ErrorLogData[0]);
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmSMBIOS) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                EventDataSize = 4;
                if (CurrRedirProtocol != NULL) {
                    Status = CurrRedirProtocol->SetEventLogData (
                                                    CurrRedirProtocol, // This
                                                    &ErrorLogData[0],  // *ElogData
                                                    EfiElogSmSMBIOS,   // DataType
                                                    FALSE,             // AlertEvent
                                                    EventDataSize,     // DataSize
                                                    &RecordId          // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "OemErrorLogEventSMN: SetEventLogData for Smbios-%r\n", Status));
                    break;
                }
            }
        }
    }
#endif  
    return  EFI_SUCCESS;
}

EFI_STATUS
OemErrorLogEventMca (
  IN  VOID               *ErrorRecord,
  IN  DIMM_INFO          *DimmInfo,
  IN  NORMALIZED_ADDRESS *Address,
  IN  UINT8               BankIndex
)
{
    EFI_STATUS                  Status;
    EFI_SM_ELOG_REDIR_PROTOCOL  *CurrRedirProtocol;
    UINT64                      RecordId;
    UINTN                       EventDataSize;
    UINTN                       EntryIndex;
    
    DEBUG((DEBUG_INFO, "[AmiRAS]DXE OemErrorLogEventMca entry\n"));
    
    Status = McaToIpmiBrh (ErrorRecord, (UINT8*)&ErrorLogData[0], DimmInfo,Address,BankIndex);
    DEBUG((DEBUG_INFO, "[AmiRAS]DXE McaToIpmiBrh Status=%r\n", Status));
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmIPMI) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                if (CurrRedirProtocol != NULL) {
                    EventDataSize = 0;
                    CurrRedirProtocol->SetEventLogData(
                                                    CurrRedirProtocol,   // This
                                                    &ErrorLogData[0],    // *ElogData
                                                    EfiElogSmIPMI,       // DataType
                                                    FALSE,               // AlertEvent
                                                    EventDataSize,       // DataSize
                                                    &RecordId            // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "SendElogEventMca: SetEventLogData for Ipmi-%r\n", Status));
                }
                break;
            }
        }
    }
#if ( defined(SMBIOS_RAS_ELOG) && (SMBIOS_RAS_ELOG != 0))
    Status = McaToSmbiosBrh (ErrorRecord, (UINT8*)&ErrorLogData[0], BankIndex);
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmSMBIOS) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                EventDataSize = 4;
                if (CurrRedirProtocol != NULL) {
                    Status = CurrRedirProtocol->SetEventLogData (
                                                    CurrRedirProtocol, // This
                                                    &ErrorLogData[0],  // *ElogData
                                                    EfiElogSmSMBIOS,   // DataType
                                                    FALSE,             // AlertEvent
                                                    EventDataSize,     // DataSize
                                                    &RecordId          // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "OemErrorLogEventProcessor: SetEventLogData for Smbios-%r\n", Status));
                    break;
                }
            }
        }
    }
#endif
    return  EFI_SUCCESS;
}

EFI_STATUS
OemErrorLogEventProcessor (
  IN  VOID                      *ErrorRecord,
  IN  UINT8                     BankIndex,
  IN  UINTN                     ProcessorNumber,
  IN  GENERIC_PROC_ERR_ENTRY_V3 *GenericProcErrBuffer
)
{
    EFI_STATUS                  Status;
    EFI_SM_ELOG_REDIR_PROTOCOL  *CurrRedirProtocol;
    UINT64                      RecordId;
    UINTN                       EventDataSize;
    UINTN                       EntryIndex;
    
    DEBUG((DEBUG_INFO, "[AmiRAS]DXE OemErrorLogEventProcessor entry\n"));
    
    Status = ProcessorToIpmiBrh (ErrorRecord, (UINT8*)&ErrorLogData[0], BankIndex, ProcessorNumber,GenericProcErrBuffer);
    DEBUG((DEBUG_INFO, "[AmiRAS] DXE ProcessorToIpmi status=%r\n", Status));
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmIPMI) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                if (CurrRedirProtocol != NULL) {
                    EventDataSize = 0;
                    Status = CurrRedirProtocol->SetEventLogData (
                                                CurrRedirProtocol,   // This
                                                &ErrorLogData[0],    // *ElogData
                                                EfiElogSmIPMI,       // DataType
                                                FALSE,               // AlertEvent
                                                EventDataSize,       // DataSize
                                                &RecordId            // *RecordId
                                                );
                    DEBUG((EFI_D_ERROR, "OemErrorLogEventProcessor: SetEventLogData for Ipmi-%r\n", Status));
                    break;
                }
            }
        }
    }
#if ( defined(SMBIOS_RAS_ELOG) && (SMBIOS_RAS_ELOG != 0))
    Status = ProcessorToSmbiosBrh (ErrorRecord, (UINT8*)&ErrorLogData[0], BankIndex, ProcessorNumber,GenericProcErrBuffer);
    if (Status == EFI_SUCCESS) {
        for (EntryIndex=0; EntryIndex < EfiSmElogMax; EntryIndex++) {
            if (CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType == EfiElogSmSMBIOS) {
                CurrRedirProtocol = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol;
                EventDataSize = 4;
                if (CurrRedirProtocol != NULL) {
                    Status = CurrRedirProtocol->SetEventLogData (
                                                    CurrRedirProtocol, // This
                                                    &ErrorLogData[0],  // *ElogData
                                                    EfiElogSmSMBIOS,   // DataType
                                                    FALSE,             // AlertEvent
                                                    EventDataSize,     // DataSize
                                                    &RecordId          // *RecordId
                                                    );
                    //DEBUG((EFI_D_ERROR, "OemErrorLogEventProcessor: SetEventLogData for Smbios-%r\n", Status));
                    break;
                }
            }
        }
    }
#endif
    return  EFI_SUCCESS;
}

EFI_STATUS
EFIAPI
OemErrorLogEventRtPpr (
  IN  UINT32             RuntimePprStatus,
  IN  VOID               *ChkRtpprComplStruct,
  IN  BOOLEAN            *RepairResult,
  IN  UINT32             NumOfRtPptEntryServed
)
{
    DEBUG((DEBUG_INFO, "[AmiRAS]DXE OemErrorLogEventRtPpr entry\n"));
    return  EFI_SUCCESS;
}

EFI_STATUS
EFIAPI
OemErrorLogEventCCDBIST (
  IN VOID       *ProcGenErrEntry
)
{
    DEBUG((DEBUG_INFO, "[AmiRAS] OemErrorLogEventCCDBIST entry\n"));
    // ProcGenErrEntry->ProcGenErrorSection.TargetAddr has CCD BIST Error information.
    return  EFI_SUCCESS;
}

VOID
CollectRedirProtocolInstances (
  VOID )
{
    EFI_STATUS      Status;
    EFI_HANDLE      *HandleBuffer = NULL;
    UINTN           HandleBufferSize = 0;
    UINTN           HandleCount;
    UINTN           HandleIndex;
    UINTN           EntryIndex;
    UINTN           DataType;
    
    // Get all RedirElogProtocol handles.
    Status = gBS->LocateHandle (
                    ByProtocol,
                    &gEfiRedirElogProtocolGuid,
                    NULL,
                    &HandleBufferSize,
                    HandleBuffer );
    
    if(Status == EFI_BUFFER_TOO_SMALL){
        // Allocate memory for Handle buffer
        Status = gBS->AllocatePool(
                                    EfiRuntimeServicesData,
                                    HandleBufferSize,
                                    (VOID**)&HandleBuffer);
        if (Status == EFI_SUCCESS) {
            Status = gBS->LocateHandle (
                            ByProtocol,
                            &gEfiRedirElogProtocolGuid,
                            NULL,
                            &HandleBufferSize,
                            HandleBuffer );
            if(Status == EFI_SUCCESS){
                HandleCount = (HandleBufferSize/sizeof(EFI_HANDLE));
                DEBUG ((EFI_D_ERROR, "[AmiRAS]CollectRedir: Number of RedirElogProtocol handles-%d\n", HandleCount));

                // Store collected info
                ZeroMem(&CommonElogPrivate, sizeof(CommonElogPrivate));
                EntryIndex = 0;
                    
                for(HandleIndex = 0; HandleIndex < HandleCount; HandleIndex++){
                    Status = gBS->HandleProtocol (
                                            (EFI_SM_ELOG_PROTOCOL*)(HandleBuffer[HandleIndex]),
                                            &gEfiRedirElogProtocolGuid,
                                            &CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol
                                            );
                    if((Status != EFI_SUCCESS)||(CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol == NULL)) continue;
                    for(DataType = 0; DataType < EfiSmElogMax; DataType++){
                        Status = CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol->ActivateEventLog(
                                            CommonElogPrivate.RedirectEntry[EntryIndex].RedirProtocol,    // This
                                            DataType,                                                     // DataType
                                            NULL,                                                         // *EnableElog
                                            &CommonElogPrivate.RedirectEntry[EntryIndex].Enabled          // *ElogStatus
                                            );
                        if(Status == EFI_NOT_FOUND) continue;
                        CommonElogPrivate.RedirectEntry[EntryIndex].ProtocolType = DataType;
                        EntryIndex++;
                        break;
                    }
                }
            }
        }
        if(HandleBuffer != NULL){
            gBS->FreePool(HandleBuffer);
        }
    }
}

/**
    This  callback function collects the EFI_SM_ELOG_REDIR_PROTOCOL instances.
    @param  EFI_EVENT   *Event  - Pointer to EFI_EVENT
    @param  VOID       *Context - Pointer to Context
    
    @retval None
**/

VOID
EFIAPI
RedirElogProtocolCallBack (
    IN EFI_EVENT  Event,
    IN VOID       *Context )
{

    DEBUG ((EFI_D_ERROR, "[AmiRAS]RedirElogProtocolCallBack: ==ENTER==\n"));
    CollectRedirProtocolInstances();
}    

/**
    Entry point of the AMI OEM RS RAS DXE driver to
    install dummy Ras OEM protocol for DXE.
 
    @param  ImageHandle : EFI Image Handle for the DXE driver
    @param  SystemTable : pointer to the EFI system table
 
    EFI_SUCCESS : Module initialized successfully
    EFI_ERROR   : Initialization failed (see error for more details)
**/
EFI_STATUS
EFIAPI
AmiOemRasDxeInit (
  IN       EFI_HANDLE         ImageHandle,
  IN       EFI_SYSTEM_TABLE   *SystemTable
  )
{

  EFI_STATUS    Status;
  EFI_HANDLE    Handle;

  DEBUG((DEBUG_INFO, "[AmiRAS] RS OEM RAS DXE driver entry\n"));
//COMPAL_CHANGE >>>
  Status = gBS->LocateProtocol (
            &gAmdPlatformApeiDataProtocolGuid,
            NULL,
            (VOID **)&mPlatformApeiData
            );
  DEBUG((EFI_D_ERROR, "[AmiRAS] RS OEM CPM RAS DXE locate mPlatformApeiData %r\n", Status));
//COMPAL_CHANGE <<<  
  Handle = NULL;
  Status = gBS->InstallProtocolInterface (
                &Handle,
                &gAmdCpmRasOemProtocolGuid,
                EFI_NATIVE_INTERFACE,
                &AmdCpmRasOemProtocol
                );

  DEBUG ((DEBUG_INFO, "[AmiRAS]Install AmdCpmRasOemProtocol: %r\n", Status));
  
  EfiCreateProtocolNotifyEvent (
                  &gEfiRedirElogProtocolGuid,
                  TPL_CALLBACK,
                  RedirElogProtocolCallBack,
                  NULL,
                  &gRegistration );

  return (Status);
}
