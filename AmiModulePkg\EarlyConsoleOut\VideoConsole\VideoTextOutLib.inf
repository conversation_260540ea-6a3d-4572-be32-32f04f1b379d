#***********************************************************************
#*                                                                     *
#*   Copyright (c) 1985-2025, American Megatrends International LLC.   *
#*                                                                     *
#*      All rights reserved. Subject to AMI licensing agreement.       *
#*                                                                     *
#***********************************************************************
## @file VideoTextOutLib.inf
#  Lib Module that provide the interface to access to Video Controller
##

[Defines]
  INF_VERSION       = 0x00010005
  VERSION_STRING    = 1.0
  BASE_NAME         = VideoTextOutLib
  LIBRARY_CLASS     = VideoTextOutLib
  MODULE_TYPE       = BASE
  
[Sources]
  VideoTextOutLib.c
  
[Packages]
  MdePkg/MdePkg.dec
  AmiModulePkg/AmiModulePkg.dec
  AmiModulePkg/EarlyConsoleOut/AmiEarlyConsoleOutPkg.dec
 
[LibraryClasses]
  DebugLib
  HobLib
  BaseMemoryLib
  BaseLib
 
[Guids]
  gAmiTextModeFrameBufferHobGuid 
  gAmiVideoPrivateDataHobGuid
