#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
#This file is auto generated, do not edit it manually


[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = CbsSetAgesaPcdLib
  FILE_GUID                      = CFBC986E-808D-42A7-B7B7-55D13A626802
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = CbsSetAgesaPcdLib

[sources.common]
  CbsSetAgesaPcdLibBRH.c
  AmdCbsVariable.h

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  AmdCbsPkg/AmdCbsPkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleCcxPkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/AgesaModuleMemPkg.dec
  AgesaModulePkg/AgesaModuleNbioPkg.dec
  AgesaModulePkg/AgesaModulePspPkg.dec
  AgesaModulePkg/AgesaModuleRasPkg.dec
  

[LibraryClasses]
  PcdLib
  DebugLib

[DEPEX]
  TRUE

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuReqMinFreq
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuReqMinFreqEn
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdEnableRMSS
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdRedirectForReturnDis
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxCfgPFEHEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpbMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCStateMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPowerSupplyIdleControl
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdStreamingStoresCtrl
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCstC1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuCstC2Latency
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMcaErrThreshEn
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMcaErrThreshCount
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMcaFruTextEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmee
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSnpMemCover
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSnpMemSize
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdRmpCover64BitMMIORanges
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdRmp64BitMmioS0RbMask
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdRmp64BitMmioS1RbMask
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSplitRmpTable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSegmentedRmp
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdRmpSegSize
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdEnableERMS
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdTransparentErrorLoggingEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxEnableAvx512
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxDisFstStrErmsb
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMonMwaitDis
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuSpeculativeStoreMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdEnableFSRM
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuPauseCntSel_1_0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCmcNotificationType
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuAdaptiveAlloc
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCoreTraceDumpEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxErmsbRepo
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxP0Setting
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxP0Freq
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxP0Fid32
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxP0Vid32
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL1StreamPrefetcher
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL1StridePrefetcher
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL1RegionPrefetcher
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL2StreamPrefetcher
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL2UpDownPrefetcher
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL1BurstPrefetch
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuWdtEn
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuWdtTimeout
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricWdtCntSel
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricImmSyncFloodOnFatalErrCtrl
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCXlEarlyLinkTraining
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricCcxAsNumaDomain
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitDistancePcdCtrl
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitAutoRemoteFar
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitVirtualDistance
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitLocalDistance
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitRemoteDistance
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitCxlLocalDistance
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricSlitCxlRemoteDistance
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricCdma
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMemBootTimePostPackageRepair
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMemRuntimePostPackageRepair
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMemPostPackageRepairConfigInitiator
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdMemEcsStatusInterrupt
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDdrEccErrorCounterEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDdrEccErrorCounterIntEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDdrEccErrorCounterLeakRate
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDdrEccErrorCounterStartCount
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHmkee
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPmicErrorReporting
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPcieLoopbackMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEnable2SpcGen4
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEnable2SpcGen5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdsafeRecoveryBER
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPeriodicCal
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdcTDP
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPPT
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDeterminismMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDeterminismControl
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdxGMIForceLinkWidthEn
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdxGMIForceLinkWidth
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdxGMIMaxLinkWidthEn
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdxGMIMaxLinkWidth
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdxGMIMinLinkWidth
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgApbDis
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgFixedSocPstate
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPowerProfileSelect
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXgmiPstateControl
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXgmiPstateSelection
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdBoostFmax
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDFFODisable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDfCstateEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgCPPCMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgHSMPSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSvi3SvcSpeed
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgDiagnosticMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdGmiFolding
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdThrottlerMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDfPstateRangeSupportEn
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDfPstateRangeMax
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDfPstateRangeMin
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioRASControlV2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMaskNbioSyncFlood
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSyncFloodToApml
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPcieAerReportMechanism
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdEdpcEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdRASAcsValue
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioPoisonConsumption
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPcieSyncFloodOnFatal
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEgressPoisonSeverityHi
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEgressPoisonSeverityLo
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioEgressPoisonMaskHi
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioEgressPoisonMaskLo
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioRASUcpMaskHi
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioRASUcpMaskLo
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSyshubWdtTimerInterval
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDataObjectExchange
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgRxMarginPersistenceMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdLcMultAutoSpdChgOnLastRateEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAutoSpeedChangeEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAllowCompliance
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAdvertiseEqToHighRateSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDlfCapEnV2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDlfExEnV2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSrisEnableMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgACSEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPcieTbtSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieAriForwardingEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPcieAriSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPresenceDetectSelectMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugHandlingMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugPDSettle
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugSettleTime
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAEREnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgForcePcieGenSpeed
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTargetPcieGenSpeed
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLinkAspmAllPort
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMCTPEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMCTPMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMCTPDiscoveryNotify
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieNonPcieCompliantTrainingFailureSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdLimitHpDevicesToPcieBootSpeed
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPCIeSFIConfigviaOOBEn
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieIdlePowerSetting
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsEnRccDev0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAerEnRccDev0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDlfEnStrap1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPhy16gtStrap1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMarginEnStrap1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsSourceValStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsTranslationalBlockingStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsP2pReq
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsP2pCompStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsUpstreamFwdStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsP2PEgressStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsDirectTranslatedStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsSsidEnStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPriEnPageReq
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPriResetPageReq
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsSourceVal
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsTranslationalBlocking
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsP2pComp
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsUpstreamFwd
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsP2PEgress
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsP2pReqStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdRccDev0E2EPrefix
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdRccDev0ExtendedFmtSupported
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAtomicRoutingEnStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgSevSnpSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgSevTioSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspDrtmVirtualDevice
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDmaProtection
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgIommuSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdIvInfoDmaReMap
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverRideEnabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS0P0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS0P1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS0P2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS0P3
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS1P0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS1P1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS1P2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS1P3
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS0P0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS0P1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS0P2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS0P3
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS1P0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS1P1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS1P2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS1P3
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqPresetMask8GtConfig
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqPresetMask8Gt
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqPresetMask16GtConfig
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqPresetMask16Gt
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqPresetMask32GtConfig
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqPresetMask32Gt
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2cI3c0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2cI3c1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2cI3c2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2cI3c3
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c4
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchSpdHostCtrlRelease
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchDimmTelemetry
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchIxcTelemetryPortsFenceControl
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2cSdaHoldOverride
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI3cSpeed
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI3cPPHcnt
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI3cSdaHoldOverride
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c0SdaTxHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c1SdaTxHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c2SdaTxHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c3SdaTxHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c4SdaTxHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c5SdaTxHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c0SdaRxHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c1SdaRxHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c2SdaRxHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c3SdaRxHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c4SdaRxHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI2c5SdaRxHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI3c0SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI3c1SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI3c2SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI3c3SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataClass
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataRasSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataStaggeredSpinup
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataAhciDisPrefetchFunction
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataDevSlpPort0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataDevSlpPort1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXhci0Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXhci1Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXhci2Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXhci3Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPwrFailShadow
  gEfiAmdAgesaPkgTokenSpaceGuid.FchUart0LegacyEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.FchUart1LegacyEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.FchUart2LegacyEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchAlinkRasSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdResetCpuOnSyncFlood
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDelayResetCpuOnSyncFlood
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSpreadSpectrum
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdBootTimerEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBP0P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBStartLaneP0P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBEndLaneP0P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBLinkSpeedP0P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBModeP0P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBP0P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBStartLaneP0P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBEndLaneP0P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBLinkSpeedP0P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBModeP0P2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspEinjSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspAntiRollbackLateSplFuse
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlOnAllPorts
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlSpaEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlSpm
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlDvsecLock
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlHdmDecoderLockOnCommit
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCxlTempGen5AdvertAltPtcl
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSyncHeaderByPass
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCxlSyncHeaderByPassCompMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTruncateCxlMemory
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlProtocolErrorReporting
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlComponentErrorReporting
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlMemIsolationEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlMemIsolationFwNotification
