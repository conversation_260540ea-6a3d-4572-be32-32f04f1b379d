TOKEN
    Name  = "AgesaModulePkg_SUPPORT"
    Value  = "1"
    Help  = "Switch for Enabling AgesaModulePkg support in the project"
    TokenType = Boolean
    TargetH = Yes
    Master = Yes
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Debug_AmdIdsDebugPrintPei_AmdIdsDebugPrintPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Debug_AmdIdsDebugPrintPei_AmdIdsDebugPrintPei_INF"
    File = "Debug/AmdIdsDebugPrintPei/AmdIdsDebugPrintPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Debug_AmdIdsDebugPrintPei_AmdIdsDebugPrintPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Fch_Common_I2cPei_I2cMasterPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Fch_Common_I2cPei_I2cMasterPei_INF"
    File = "Fch/Common/I2cPei/I2cMasterPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Fch_Common_I2cPei_I2cMasterPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Psp_AmdPspPeiV2Brh_AmdPspPeiV2_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Psp_AmdPspPeiV2Brh_AmdPspPeiV2_INF"
    File = "Psp/AmdPspPeiV2Brh/AmdPspPeiV2.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Psp_AmdPspPeiV2Brh_AmdPspPeiV2_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceManager3Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceManager3Lib_INF"
    File = "Library/FabricResourceManagerBrhLib/FabricResourceManager3Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceManager3Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FabricResourceManagerLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceManager3Lib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceManager3Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Psp_AmdPspDtpmPei_AmdPspDtpmPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Psp_AmdPspDtpmPei_AmdPspDtpmPei_INF"
    File = "Psp/AmdPspDtpmPei/AmdPspDtpmPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Psp_AmdPspDtpmPei_AmdPspDtpmPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Psp_AmdPspPsbDisablePei_AmdPspPsbDisablePei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Psp_AmdPspPsbDisablePei_AmdPspPsbDisablePei_INF"
    File = "Psp/AmdPspPsbDisablePei/AmdPspPsbDisablePei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Psp_AmdPspPsbDisablePei_AmdPspPsbDisablePei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Psp_ApcbDrv_ApcbV3Pei_ApcbV3Pei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Psp_ApcbDrv_ApcbV3Pei_ApcbV3Pei_INF"
    File = "Psp/ApcbDrv/ApcbV3Pei/ApcbV3Pei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Psp_ApcbDrv_ApcbV3Pei_ApcbV3Pei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Psp_ApobDrv_ApobBrhPei_ApobBrhPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Psp_ApobDrv_ApobBrhPei_ApobBrhPei_INF"
    File = "Psp/ApobDrv/ApobBrhPei/ApobBrhPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Psp_ApobDrv_ApobBrhPei_ApobBrhPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Ccx_Zen5_Pei_AmdCcxZen5Pei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Ccx_Zen5_Pei_AmdCcxZen5Pei_INF"
    File = "Ccx/Zen5/Pei/AmdCcxZen5Pei.inf"
    Package = "AgesaModulePkg"
    Arch = "IA32 X64"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Ccx_Zen5_Pei_AmdCcxZen5Pei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_CcxResetTablesZen5Lib_CcxResetTablesZen5Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_CcxResetTablesZen5Lib_CcxResetTablesZen5Lib_INF"
    File = "Library/CcxResetTablesZen5Lib/CcxResetTablesZen5Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_CcxResetTablesZen5Lib_CcxResetTablesZen5Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxResetTablesLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_CcxResetTablesZen5Lib_CcxResetTablesZen5Lib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_CcxResetTablesZen5Lib_CcxResetTablesZen5Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_INF"
    File = "Library/IdsNonUefiLib/IdsNonUefiLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "IdsLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_CcxRolesZen5Lib_CcxRolesZen5Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_CcxRolesZen5Lib_CcxRolesZen5Lib_INF"
    File = "Library/CcxRolesZen5Lib/CcxRolesZen5Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_CcxRolesZen5Lib_CcxRolesZen5Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxRolesLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_CcxRolesZen5Lib_CcxRolesZen5Lib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_CcxRolesZen5Lib_CcxRolesZen5Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_CcxPstatesZen5Lib_CcxPstatesZen5Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_CcxPstatesZen5Lib_CcxPstatesZen5Lib_INF"
    File = "Library/CcxPstatesZen5Lib/CcxPstatesZen5Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_CcxPstatesZen5Lib_CcxPstatesZen5Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxPstatesLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_CcxPstatesZen5Lib_CcxPstatesZen5Lib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_CcxPstatesZen5Lib_CcxPstatesZen5Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_CcxSetMcaZen5Lib_CcxSetMcaZen5Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_CcxSetMcaZen5Lib_CcxSetMcaZen5Lib_INF"
    File = "Library/CcxSetMcaZen5Lib/CcxSetMcaZen5Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_CcxSetMcaZen5Lib_CcxSetMcaZen5Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxSetMcaLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_CcxSetMcaZen5Lib_CcxSetMcaZen5Lib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_CcxSetMcaZen5Lib_CcxSetMcaZen5Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_FabricWdtDf4Lib_FabricWdtDf4Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FabricWdtDf4Lib_FabricWdtDf4Lib_INF"
    File = "Library/FabricWdtDf4Lib/FabricWdtDf4Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_FabricWdtDf4Lib_FabricWdtDf4Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FabricWdtLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FabricWdtDf4Lib_FabricWdtDf4Lib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_FabricWdtDf4Lib_FabricWdtDf4Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Fabric_BRH_FabricBrhPei_AmdFabricBrhPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Fabric_BRH_FabricBrhPei_AmdFabricBrhPei_INF"
    File = "Fabric/BRH/FabricBrhPei/AmdFabricBrhPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Fabric_BRH_FabricBrhPei_AmdFabricBrhPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_BaseFabricTopologyBrhLib_BaseFabricTopologyBrhLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_BaseFabricTopologyBrhLib_BaseFabricTopologyBrhLib_INF"
    File = "Library/BaseFabricTopologyBrhLib/BaseFabricTopologyBrhLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_BaseFabricTopologyBrhLib_BaseFabricTopologyBrhLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "BaseFabricTopologyLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_BaseFabricTopologyBrhLib_BaseFabricTopologyBrhLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_BaseFabricTopologyBrhLib_BaseFabricTopologyBrhLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Nbio_BRH_PEI_NbioPeiBrh_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Nbio_BRH_PEI_NbioPeiBrh_INF"
    File = "Nbio/BRH/PEI/NbioPeiBrh.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Nbio_BRH_PEI_NbioPeiBrh_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Fch_Kunlun_FchKunlunPei_FchPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Fch_Kunlun_FchKunlunPei_FchPei_INF"
    File = "Fch/Kunlun/FchKunlunPei/FchPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Fch_Kunlun_FchKunlunPei_FchPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Fch_Kunlun_FchKunLunSmbusPei_Smbus_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Fch_Kunlun_FchKunLunSmbusPei_Smbus_INF"
    File = "Fch/Kunlun/FchKunLunSmbusPei/Smbus.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Fch_Kunlun_FchKunLunSmbusPei_Smbus_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Fch_Common_I3cPei_I3cMasterPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Fch_Common_I3cPei_I3cMasterPei_INF"
    File = "Fch/Common/I3cPei/I3cMasterPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Fch_Common_I3cPei_I3cMasterPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Fch_Common_FchEspiCmdPei_FchEspiCmdPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Fch_Common_FchEspiCmdPei_FchEspiCmdPei_INF"
    File = "Fch/Common/FchEspiCmdPei/FchEspiCmdPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Fch_Common_FchEspiCmdPei_FchEspiCmdPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Fch_Kunlun_FchKunlunMultiFchPei_FchMultiFchPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Fch_Kunlun_FchKunlunMultiFchPei_FchMultiFchPei_INF"
    File = "Fch/Kunlun/FchKunlunMultiFchPei/FchMultiFchPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Fch_Kunlun_FchKunlunMultiFchPei_FchMultiFchPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Mem_AmdMemBrhSp5Pei_AmdMemBrhSp5Pei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Mem_AmdMemBrhSp5Pei_AmdMemBrhSp5Pei_INF"
    File = "Mem/AmdMemBrhSp5Pei/AmdMemBrhSp5Pei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Mem_AmdMemBrhSp5Pei_AmdMemBrhSp5Pei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Soc_AmdSocSp5BrhPei_AmdSocSp5BrhPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Soc_AmdSocSp5BrhPei_AmdSocSp5BrhPei_INF"
    File = "Soc/AmdSocSp5BrhPei/AmdSocSp5BrhPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Soc_AmdSocSp5BrhPei_AmdSocSp5BrhPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceInit3Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceInit3Lib_INF"
    File = "Library/FabricResourceManagerBrhLib/FabricResourceInit3Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceInit3Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FabricResourceInitLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceInit3Lib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceInit3Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_BaseSocketLogicalIdRsDieLib_BaseSocketLogicalIdRsDieLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_BaseSocketLogicalIdRsDieLib_BaseSocketLogicalIdRsDieLib_INF"
    File = "Library/BaseSocketLogicalIdRsDieLib/BaseSocketLogicalIdRsDieLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_BaseSocketLogicalIdRsDieLib_BaseSocketLogicalIdRsDieLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "BaseSocketLogicalIdLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_BaseSocketLogicalIdRsDieLib_BaseSocketLogicalIdRsDieLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_BaseSocketLogicalIdRsDieLib_BaseSocketLogicalIdRsDieLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_BaseSocLogicalIdXlatZen5DieLib_BaseSocLogicalIdXlatZen5DieLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_BaseSocLogicalIdXlatZen5DieLib_BaseSocLogicalIdXlatZen5DieLib_INF"
    File = "Library/BaseSocLogicalIdXlatZen5DieLib/BaseSocLogicalIdXlatZen5DieLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_BaseSocLogicalIdXlatZen5DieLib_BaseSocLogicalIdXlatZen5DieLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "BaseSocLogicalIdXlatLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_BaseSocLogicalIdXlatZen5DieLib_BaseSocLogicalIdXlatZen5DieLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_BaseSocLogicalIdXlatZen5DieLib_BaseSocLogicalIdXlatZen5DieLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_PeiSocBistZen5CcdBrhLib_PeiSocBistZen5CcdBrhLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_PeiSocBistZen5CcdBrhLib_PeiSocBistZen5CcdBrhLib_INF"
    File = "Library/PeiSocBistZen5CcdBrhLib/PeiSocBistZen5CcdBrhLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_PeiSocBistZen5CcdBrhLib_PeiSocBistZen5CcdBrhLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "PeiSocBistLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_PeiSocBistZen5CcdBrhLib_PeiSocBistZen5CcdBrhLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_PeiSocBistZen5CcdBrhLib_PeiSocBistZen5CcdBrhLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_PeiFabricSocSpecificServicesBrhLib_PeiFabricSocSpecificServicesBrhLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_PeiFabricSocSpecificServicesBrhLib_PeiFabricSocSpecificServicesBrhLib_INF"
    File = "Library/PeiFabricSocSpecificServicesBrhLib/PeiFabricSocSpecificServicesBrhLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_PeiFabricSocSpecificServicesBrhLib_PeiFabricSocSpecificServicesBrhLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "PeiFabricSocSpecificServicesLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_PeiFabricSocSpecificServicesBrhLib_PeiFabricSocSpecificServicesBrhLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_PeiFabricSocSpecificServicesBrhLib_PeiFabricSocSpecificServicesBrhLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_PeiSocZen5ServicesBrhLib_PeiSocZen5ServicesBrhLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_PeiSocZen5ServicesBrhLib_PeiSocZen5ServicesBrhLib_INF"
    File = "Library/PeiSocZen5ServicesBrhLib/PeiSocZen5ServicesBrhLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_PeiSocZen5ServicesBrhLib_PeiSocZen5ServicesBrhLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "PeiSocZen5ServicesLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_PeiSocZen5ServicesBrhLib_PeiSocZen5ServicesBrhLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_PeiSocZen5ServicesBrhLib_PeiSocZen5ServicesBrhLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_PeiCcxCoreTopologyServicesV3BrhLib_PeiCcxCoreTopologyServicesV3BrhLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_PeiCcxCoreTopologyServicesV3BrhLib_PeiCcxCoreTopologyServicesV3BrhLib_INF"
    File = "Library/PeiCcxCoreTopologyServicesV3BrhLib/PeiCcxCoreTopologyServicesV3BrhLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_PeiCcxCoreTopologyServicesV3BrhLib_PeiCcxCoreTopologyServicesV3BrhLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "PeiCoreTopologyServicesV3Lib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_PeiCcxCoreTopologyServicesV3BrhLib_PeiCcxCoreTopologyServicesV3BrhLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_PeiCcxCoreTopologyServicesV3BrhLib_PeiCcxCoreTopologyServicesV3BrhLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_ApobApcbUpdatesBrhLib_ApobApcbUpdatesBrhLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_ApobApcbUpdatesBrhLib_ApobApcbUpdatesBrhLib_INF"
    File = "Library/ApobApcbUpdatesBrhLib/ApobApcbUpdatesBrhLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_ApobApcbUpdatesBrhLib_ApobApcbUpdatesBrhLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "ApobApcbUpdatesLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_ApobApcbUpdatesBrhLib_ApobApcbUpdatesBrhLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_ApobApcbUpdatesBrhLib_ApobApcbUpdatesBrhLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_FabricRootBridgeOrderLib_FabricRootBridgeOrderLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FabricRootBridgeOrderLib_FabricRootBridgeOrderLib_INF"
    File = "Library/FabricRootBridgeOrderLib/FabricRootBridgeOrderLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_FabricRootBridgeOrderLib_FabricRootBridgeOrderLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FabricRootBridgeOrderLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FabricRootBridgeOrderLib_FabricRootBridgeOrderLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_FabricRootBridgeOrderLib_FabricRootBridgeOrderLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Mem_AmdMemChanXLatPei_MemChanXLatPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Mem_AmdMemChanXLatPei_MemChanXLatPei_INF"
    File = "Mem/AmdMemChanXLatPei/MemChanXLatPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Mem_AmdMemChanXLatPei_MemChanXLatPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Mem_AmdMemSmbiosV2BrhPei_MemSmbiosV2Pei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Mem_AmdMemSmbiosV2BrhPei_MemSmbiosV2Pei_INF"
    File = "Mem/AmdMemSmbiosV2BrhPei/MemSmbiosV2Pei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Mem_AmdMemSmbiosV2BrhPei_MemSmbiosV2Pei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_MemSmbiosV2BrhD5Lib_MemSmbiosV2Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_MemSmbiosV2BrhD5Lib_MemSmbiosV2Lib_INF"
    File = "Library/MemSmbiosV2BrhD5Lib/MemSmbiosV2Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_MemSmbiosV2BrhD5Lib_MemSmbiosV2Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdMemSmbiosV2Lib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_MemSmbiosV2BrhD5Lib_MemSmbiosV2Lib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_MemSmbiosV2BrhD5Lib_MemSmbiosV2Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Mem_AmdMemRestorePei_MemRestorePei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Mem_AmdMemRestorePei_MemRestorePei_INF"
    File = "Mem/AmdMemRestorePei/MemRestorePei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Mem_AmdMemRestorePei_MemRestorePei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Mem_AmdMbistBrhPei_AmdMbistBrhPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Mem_AmdMbistBrhPei_AmdMbistBrhPei_INF"
    File = "Mem/AmdMbistBrhPei/AmdMbistBrhPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Mem_AmdMbistBrhPei_AmdMbistBrhPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_ErrorLog_AmdErrorLogPei_AmdErrorLogPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_ErrorLog_AmdErrorLogPei_AmdErrorLogPei_INF"
    File = "ErrorLog/AmdErrorLogPei/AmdErrorLogPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_ErrorLog_AmdErrorLogPei_AmdErrorLogPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Mem_AmdMemoryHobInfoPeimBrh_AmdMemoryHobInfoPeimBrh_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Mem_AmdMemoryHobInfoPeimBrh_AmdMemoryHobInfoPeimBrh_INF"
    File = "Mem/AmdMemoryHobInfoPeimBrh/AmdMemoryHobInfoPeimBrh.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Mem_AmdMemoryHobInfoPeimBrh_AmdMemoryHobInfoPeimBrh_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Universal_Version_AmdVersionPei_AmdVersionPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Universal_Version_AmdVersionPei_AmdVersionPei_INF"
    File = "Universal/Version/AmdVersionPei/AmdVersionPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Universal_Version_AmdVersionPei_AmdVersionPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Debug_AmdIdsDebugPrintDxe_AmdIdsDebugPrintDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Debug_AmdIdsDebugPrintDxe_AmdIdsDebugPrintDxe_INF"
    File = "Debug/AmdIdsDebugPrintDxe/AmdIdsDebugPrintDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Debug_AmdIdsDebugPrintDxe_AmdIdsDebugPrintDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Mem_AmdMemBrhSp5Dxe_AmdMemBrhSp5Dxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Mem_AmdMemBrhSp5Dxe_AmdMemBrhSp5Dxe_INF"
    File = "Mem/AmdMemBrhSp5Dxe/AmdMemBrhSp5Dxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Mem_AmdMemBrhSp5Dxe_AmdMemBrhSp5Dxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Mem_AmdMemSmbiosV2Dxe_AmdMemSmbiosV2Dxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Mem_AmdMemSmbiosV2Dxe_AmdMemSmbiosV2Dxe_INF"
    File = "Mem/AmdMemSmbiosV2Dxe/AmdMemSmbiosV2Dxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Mem_AmdMemSmbiosV2Dxe_AmdMemSmbiosV2Dxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Mem_AmdMemRestoreDxe_MemRestoreDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Mem_AmdMemRestoreDxe_MemRestoreDxe_INF"
    File = "Mem/AmdMemRestoreDxe/MemRestoreDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Mem_AmdMemRestoreDxe_MemRestoreDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Mem_AmdMemPprSmmDriver_AmdMemPprSmmDriver_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Mem_AmdMemPprSmmDriver_AmdMemPprSmmDriver_INF"
    File = "Mem/AmdMemPprSmmDriver/AmdMemPprSmmDriver.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Mem_AmdMemPprSmmDriver_AmdMemPprSmmDriver_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Mem_AmdMemChanXLatDxe_MemChanXLatDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Mem_AmdMemChanXLatDxe_MemChanXLatDxe_INF"
    File = "Mem/AmdMemChanXLatDxe/MemChanXLatDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Mem_AmdMemChanXLatDxe_MemChanXLatDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Psp_ApcbDrv_ApcbV3Dxe_ApcbV3Dxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Psp_ApcbDrv_ApcbV3Dxe_ApcbV3Dxe_INF"
    File = "Psp/ApcbDrv/ApcbV3Dxe/ApcbV3Dxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Psp_ApcbDrv_ApcbV3Dxe_ApcbV3Dxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Psp_ApcbDrv_ApcbV3Smm_ApcbV3Smm_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Psp_ApcbDrv_ApcbV3Smm_ApcbV3Smm_INF"
    File = "Psp/ApcbDrv/ApcbV3Smm/ApcbV3Smm.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Psp_ApcbDrv_ApcbV3Smm_ApcbV3Smm_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Psp_AmdPspDxeV2Brh_AmdPspDxeV2_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Psp_AmdPspDxeV2Brh_AmdPspDxeV2_INF"
    File = "Psp/AmdPspDxeV2Brh/AmdPspDxeV2.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Psp_AmdPspDxeV2Brh_AmdPspDxeV2_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Psp_AmdPspDxeV2Brh_AmdDrtmAsl_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Psp_AmdPspDxeV2Brh_AmdDrtmAsl_INF"
    File = "Psp/AmdPspDxeV2Brh/AmdDrtmAsl.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "USER_DEFINED"
    Token = "DXE_DRIVER_AgesaModulePkg_Psp_AmdPspDxeV2Brh_AmdDrtmAsl_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Psp_AmdPspP2CmboxV2Mcm_AmdPspP2CmboxV2_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Psp_AmdPspP2CmboxV2Mcm_AmdPspP2CmboxV2_INF"
    File = "Psp/AmdPspP2CmboxV2Mcm/AmdPspP2CmboxV2.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Psp_AmdPspP2CmboxV2Mcm_AmdPspP2CmboxV2_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Psp_AmdPspP2CmboxV2Mcm_AmdPspP2CmboxV2SmmBuffer_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Psp_AmdPspP2CmboxV2Mcm_AmdPspP2CmboxV2SmmBuffer_INF"
    File = "Psp/AmdPspP2CmboxV2Mcm/AmdPspP2CmboxV2SmmBuffer.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Psp_AmdPspP2CmboxV2Mcm_AmdPspP2CmboxV2SmmBuffer_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Psp_AmdPspSmmV2Mcm_AmdPspSmmV2_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Psp_AmdPspSmmV2Mcm_AmdPspSmmV2_INF"
    File = "Psp/AmdPspSmmV2Mcm/AmdPspSmmV2.inf"
    Package = "AgesaModulePkg"
    Arch = "X64"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Psp_AmdPspSmmV2Mcm_AmdPspSmmV2_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Psp_AmdHstiV2_AmdHstiV2_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Psp_AmdHstiV2_AmdHstiV2_INF"
    File = "Psp/AmdHstiV2/AmdHstiV2.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Psp_AmdHstiV2_AmdHstiV2_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Psp_ApobDrv_ApobBrhDxe_ApobBrhDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Psp_ApobDrv_ApobBrhDxe_ApobBrhDxe_INF"
    File = "Psp/ApobDrv/ApobBrhDxe/ApobBrhDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Psp_ApobDrv_ApobBrhDxe_ApobBrhDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Psp_AmdPspAspt_AmdPspAspt_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Psp_AmdPspAspt_AmdPspAspt_INF"
    File = "Psp/AmdPspAspt/AmdPspAspt.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Psp_AmdPspAspt_AmdPspAspt_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Ccx_Zen5_Dxe_AmdCcxZen5Dxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Ccx_Zen5_Dxe_AmdCcxZen5Dxe_INF"
    File = "Ccx/Zen5/Dxe/AmdCcxZen5Dxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Ccx_Zen5_Dxe_AmdCcxZen5Dxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_INF"
    File = "Library/IdsNonUefiLib/IdsNonUefiLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "IdsLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_CcxResetTablesZen5Lib_CcxResetTablesZen5Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_CcxResetTablesZen5Lib_CcxResetTablesZen5Lib_INF"
    File = "Library/CcxResetTablesZen5Lib/CcxResetTablesZen5Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_CcxResetTablesZen5Lib_CcxResetTablesZen5Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxResetTablesLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_CcxResetTablesZen5Lib_CcxResetTablesZen5Lib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_CcxResetTablesZen5Lib_CcxResetTablesZen5Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_CcxSetMcaZen5Lib_CcxSetMcaZen5Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_CcxSetMcaZen5Lib_CcxSetMcaZen5Lib_INF"
    File = "Library/CcxSetMcaZen5Lib/CcxSetMcaZen5Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_CcxSetMcaZen5Lib_CcxSetMcaZen5Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxSetMcaLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_CcxSetMcaZen5Lib_CcxSetMcaZen5Lib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_CcxSetMcaZen5Lib_CcxSetMcaZen5Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_FabricWdtDf4Lib_FabricWdtDf4Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FabricWdtDf4Lib_FabricWdtDf4Lib_INF"
    File = "Library/FabricWdtDf4Lib/FabricWdtDf4Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_FabricWdtDf4Lib_FabricWdtDf4Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FabricWdtLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FabricWdtDf4Lib_FabricWdtDf4Lib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_FabricWdtDf4Lib_FabricWdtDf4Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_CcxSmbiosZen5Lib_CcxSmbiosZen5Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_CcxSmbiosZen5Lib_CcxSmbiosZen5Lib_INF"
    File = "Library/CcxSmbiosZen5Lib/CcxSmbiosZen5Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_CcxSmbiosZen5Lib_CcxSmbiosZen5Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxSmbiosLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_CcxSmbiosZen5Lib_CcxSmbiosZen5Lib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_CcxSmbiosZen5Lib_CcxSmbiosZen5Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_CcxRolesZen5Lib_CcxRolesZen5Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_CcxRolesZen5Lib_CcxRolesZen5Lib_INF"
    File = "Library/CcxRolesZen5Lib/CcxRolesZen5Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_CcxRolesZen5Lib_CcxRolesZen5Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxRolesLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_CcxRolesZen5Lib_CcxRolesZen5Lib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_CcxRolesZen5Lib_CcxRolesZen5Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_CcxPstatesZen5Lib_CcxPstatesZen5Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_CcxPstatesZen5Lib_CcxPstatesZen5Lib_INF"
    File = "Library/CcxPstatesZen5Lib/CcxPstatesZen5Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_CcxPstatesZen5Lib_CcxPstatesZen5Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxPstatesLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_CcxPstatesZen5Lib_CcxPstatesZen5Lib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_CcxPstatesZen5Lib_CcxPstatesZen5Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Ccx_Zen5_Smm_AmdCcxZen5Smm_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Ccx_Zen5_Smm_AmdCcxZen5Smm_INF"
    File = "Ccx/Zen5/Smm/AmdCcxZen5Smm.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Ccx_Zen5_Smm_AmdCcxZen5Smm_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Fabric_BRH_FabricBrhDxe_AmdFabricBrhDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Fabric_BRH_FabricBrhDxe_AmdFabricBrhDxe_INF"
    File = "Fabric/BRH/FabricBrhDxe/AmdFabricBrhDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Fabric_BRH_FabricBrhDxe_AmdFabricBrhDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceManager3Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceManager3Lib_INF"
    File = "Library/FabricResourceManagerBrhLib/FabricResourceManager3Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceManager3Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FabricResourceManagerLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceManager3Lib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceManager3Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_BaseFabricTopologyBrhLib_BaseFabricTopologyBrhLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_BaseFabricTopologyBrhLib_BaseFabricTopologyBrhLib_INF"
    File = "Library/BaseFabricTopologyBrhLib/BaseFabricTopologyBrhLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_BaseFabricTopologyBrhLib_BaseFabricTopologyBrhLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "BaseFabricTopologyLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_BaseFabricTopologyBrhLib_BaseFabricTopologyBrhLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_BaseFabricTopologyBrhLib_BaseFabricTopologyBrhLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Fabric_BRH_FabricBrhSmm_AmdFabricBrhSmm_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Fabric_BRH_FabricBrhSmm_AmdFabricBrhSmm_INF"
    File = "Fabric/BRH/FabricBrhSmm/AmdFabricBrhSmm.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Fabric_BRH_FabricBrhSmm_AmdFabricBrhSmm_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_AmdS3SaveLib_WOS3Save_AmdWOS3SaveLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdS3SaveLib_WOS3Save_AmdWOS3SaveLib_INF"
    File = "Library/AmdS3SaveLib/WOS3Save/AmdWOS3SaveLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdS3SaveLib_WOS3Save_AmdWOS3SaveLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdS3SaveLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdS3SaveLib_WOS3Save_AmdWOS3SaveLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdS3SaveLib_WOS3Save_AmdWOS3SaveLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Soc_AmdSocSp5BrhDxe_AmdSocSp5BrhDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Soc_AmdSocSp5BrhDxe_AmdSocSp5BrhDxe_INF"
    File = "Soc/AmdSocSp5BrhDxe/AmdSocSp5BrhDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Soc_AmdSocSp5BrhDxe_AmdSocSp5BrhDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_BaseSocketLogicalIdRsDieLib_BaseSocketLogicalIdRsDieLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_BaseSocketLogicalIdRsDieLib_BaseSocketLogicalIdRsDieLib_INF"
    File = "Library/BaseSocketLogicalIdRsDieLib/BaseSocketLogicalIdRsDieLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_BaseSocketLogicalIdRsDieLib_BaseSocketLogicalIdRsDieLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "BaseSocketLogicalIdLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_BaseSocketLogicalIdRsDieLib_BaseSocketLogicalIdRsDieLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_BaseSocketLogicalIdRsDieLib_BaseSocketLogicalIdRsDieLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_BaseSocLogicalIdXlatZen5DieLib_BaseSocLogicalIdXlatZen5DieLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_BaseSocLogicalIdXlatZen5DieLib_BaseSocLogicalIdXlatZen5DieLib_INF"
    File = "Library/BaseSocLogicalIdXlatZen5DieLib/BaseSocLogicalIdXlatZen5DieLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_BaseSocLogicalIdXlatZen5DieLib_BaseSocLogicalIdXlatZen5DieLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "BaseSocLogicalIdXlatLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_BaseSocLogicalIdXlatZen5DieLib_BaseSocLogicalIdXlatZen5DieLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_BaseSocLogicalIdXlatZen5DieLib_BaseSocLogicalIdXlatZen5DieLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_DxeSocZen5ServicesBrhLib_DxeSocZen5ServicesBrhLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_DxeSocZen5ServicesBrhLib_DxeSocZen5ServicesBrhLib_INF"
    File = "Library/DxeSocZen5ServicesBrhLib/DxeSocZen5ServicesBrhLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_DxeSocZen5ServicesBrhLib_DxeSocZen5ServicesBrhLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "DxeSocZen5ServicesLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_DxeSocZen5ServicesBrhLib_DxeSocZen5ServicesBrhLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_DxeSocZen5ServicesBrhLib_DxeSocZen5ServicesBrhLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_DxeCcxCoreTopologyServicesV3BrhLib_DxeCcxCoreTopologyServicesV3BrhLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_DxeCcxCoreTopologyServicesV3BrhLib_DxeCcxCoreTopologyServicesV3BrhLib_INF"
    File = "Library/DxeCcxCoreTopologyServicesV3BrhLib/DxeCcxCoreTopologyServicesV3BrhLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_DxeCcxCoreTopologyServicesV3BrhLib_DxeCcxCoreTopologyServicesV3BrhLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "DxeCoreTopologyServicesV3Lib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_DxeCcxCoreTopologyServicesV3BrhLib_DxeCcxCoreTopologyServicesV3BrhLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_DxeCcxCoreTopologyServicesV3BrhLib_DxeCcxCoreTopologyServicesV3BrhLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_DxeFabricSocSpecificServicesBrhLib_DxeFabricSocSpecificServicesBrhLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_DxeFabricSocSpecificServicesBrhLib_DxeFabricSocSpecificServicesBrhLib_INF"
    File = "Library/DxeFabricSocSpecificServicesBrhLib/DxeFabricSocSpecificServicesBrhLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_DxeFabricSocSpecificServicesBrhLib_DxeFabricSocSpecificServicesBrhLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "DxeFabricSocSpecificServicesLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_DxeFabricSocSpecificServicesBrhLib_DxeFabricSocSpecificServicesBrhLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_DxeFabricSocSpecificServicesBrhLib_DxeFabricSocSpecificServicesBrhLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_AmdIdsExtLibNull_AmdIdsHookExtLibNull_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdIdsExtLibNull_AmdIdsHookExtLibNull_INF"
    File = "Library/AmdIdsExtLibNull/AmdIdsHookExtLibNull.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdIdsExtLibNull_AmdIdsHookExtLibNull_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdIdsHookExtLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdIdsExtLibNull_AmdIdsHookExtLibNull_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdIdsExtLibNull_AmdIdsHookExtLibNull_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_ErrorLog_AmdErrorLogDxe_AmdErrorLogDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_ErrorLog_AmdErrorLogDxe_AmdErrorLogDxe_INF"
    File = "ErrorLog/AmdErrorLogDxe/AmdErrorLogDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_ErrorLog_AmdErrorLogDxe_AmdErrorLogDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_ErrorLog_AmdErrorLogDisplayBrhDxe_AmdErrorLogDisplayBrhDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_ErrorLog_AmdErrorLogDisplayBrhDxe_AmdErrorLogDisplayBrhDxe_INF"
    File = "ErrorLog/AmdErrorLogDisplayBrhDxe/AmdErrorLogDisplayBrhDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_ErrorLog_AmdErrorLogDisplayBrhDxe_AmdErrorLogDisplayBrhDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_ErrorLog_AmdCxlErrorLog_AmdCxlErrorLogDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_ErrorLog_AmdCxlErrorLog_AmdCxlErrorLogDxe_INF"
    File = "ErrorLog/AmdCxlErrorLog/AmdCxlErrorLogDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_ErrorLog_AmdCxlErrorLog_AmdCxlErrorLogDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Nbio_Common_CxlManagerDxe_CxlManagerDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Nbio_Common_CxlManagerDxe_CxlManagerDxe_INF"
    File = "Nbio/Common/CxlManagerDxe/CxlManagerDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Nbio_Common_CxlManagerDxe_CxlManagerDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Nbio_BRH_DXE_NbioDxeBrh_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Nbio_BRH_DXE_NbioDxeBrh_INF"
    File = "Nbio/BRH/DXE/NbioDxeBrh.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Nbio_BRH_DXE_NbioDxeBrh_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Nbio_Library_IvrsLibV3_IvrsLibV3_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Nbio_Library_IvrsLibV3_IvrsLibV3_INF"
    File = "Nbio/Library/IvrsLibV3/IvrsLibV3.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Nbio_Library_IvrsLibV3_IvrsLibV3_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "NbioIommuIvrsLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Nbio_Library_IvrsLibV3_IvrsLibV3_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Nbio_Library_IvrsLibV3_IvrsLibV3_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Nbio_BRH_Library_CollectNbifPortInfoLib_CollectNbifPortInfoLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Nbio_BRH_Library_CollectNbifPortInfoLib_CollectNbifPortInfoLib_INF"
    File = "Nbio/BRH/Library/CollectNbifPortInfoLib/CollectNbifPortInfoLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Nbio_BRH_Library_CollectNbifPortInfoLib_CollectNbifPortInfoLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CollectNbifPortInfoLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Nbio_BRH_Library_CollectNbifPortInfoLib_CollectNbifPortInfoLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Nbio_BRH_Library_CollectNbifPortInfoLib_CollectNbifPortInfoLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunDxe_FchDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Fch_Kunlun_FchKunlunDxe_FchDxe_INF"
    File = "Fch/Kunlun/FchKunlunDxe/FchDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunDxe_FchDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunSmm_FchSmm_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Fch_Kunlun_FchKunlunSmm_FchSmm_INF"
    File = "Fch/Kunlun/FchKunlunSmm/FchSmm.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunSmm_FchSmm_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunSmbusDxe_SmbusLight_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Fch_Kunlun_FchKunlunSmbusDxe_SmbusLight_INF"
    File = "Fch/Kunlun/FchKunlunSmbusDxe/SmbusLight.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunSmbusDxe_SmbusLight_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Fch_Common_I2cDxe_I2cMasterDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Fch_Common_I2cDxe_I2cMasterDxe_INF"
    File = "Fch/Common/I2cDxe/I2cMasterDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Fch_Common_I2cDxe_I2cMasterDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Fch_Common_I2cSmm_I2cMasterSmm_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Fch_Common_I2cSmm_I2cMasterSmm_INF"
    File = "Fch/Common/I2cSmm/I2cMasterSmm.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Fch_Common_I2cSmm_I2cMasterSmm_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Fch_Common_I3cDxe_I3cMasterDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Fch_Common_I3cDxe_I3cMasterDxe_INF"
    File = "Fch/Common/I3cDxe/I3cMasterDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Fch_Common_I3cDxe_I3cMasterDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Fch_Common_FchEspiCmdDxe_FchEspiCmdDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Fch_Common_FchEspiCmdDxe_FchEspiCmdDxe_INF"
    File = "Fch/Common/FchEspiCmdDxe/FchEspiCmdDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_RUNTIME_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Fch_Common_FchEspiCmdDxe_FchEspiCmdDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Fch_Common_FchEspiCmdSmm_FchEspiCmdSmm_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Fch_Common_FchEspiCmdSmm_FchEspiCmdSmm_INF"
    File = "Fch/Common/FchEspiCmdSmm/FchEspiCmdSmm.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Fch_Common_FchEspiCmdSmm_FchEspiCmdSmm_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunCf9ResetDxe_Cf9Reset_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Fch_Kunlun_FchKunlunCf9ResetDxe_Cf9Reset_INF"
    File = "Fch/Kunlun/FchKunlunCf9ResetDxe/Cf9Reset.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_RUNTIME_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunCf9ResetDxe_Cf9Reset_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunSmmControlDxe_SmmControl_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Fch_Kunlun_FchKunlunSmmControlDxe_SmmControl_INF"
    File = "Fch/Kunlun/FchKunlunSmmControlDxe/SmmControl.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_RUNTIME_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunSmmControlDxe_SmmControl_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunSmmDispatcher_FchSmmDiagDispatcher_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Fch_Kunlun_FchKunlunSmmDispatcher_FchSmmDiagDispatcher_INF"
    File = "Fch/Kunlun/FchKunlunSmmDispatcher/FchSmmDiagDispatcher.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunSmmDispatcher_FchSmmDiagDispatcher_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunSmmDispatcher_FchSmmDispatcher_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Fch_Kunlun_FchKunlunSmmDispatcher_FchSmmDispatcher_INF"
    File = "Fch/Kunlun/FchKunlunSmmDispatcher/FchSmmDispatcher.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunSmmDispatcher_FchSmmDispatcher_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunMultiFchDxe_FchMultiFchDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Fch_Kunlun_FchKunlunMultiFchDxe_FchMultiFchDxe_INF"
    File = "Fch/Kunlun/FchKunlunMultiFchDxe/FchMultiFchDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunMultiFchDxe_FchMultiFchDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunMultiFchSmm_FchMultiFchSmm_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Fch_Kunlun_FchKunlunMultiFchSmm_FchMultiFchSmm_INF"
    File = "Fch/Kunlun/FchKunlunMultiFchSmm/FchMultiFchSmm.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunMultiFchSmm_FchMultiFchSmm_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Universal_Smbios_AmdSmbiosDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Universal_Smbios_AmdSmbiosDxe_INF"
    File = "Universal/Smbios/AmdSmbiosDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Universal_Smbios_AmdSmbiosDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Universal_Acpi_AmdAcpiDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Universal_Acpi_AmdAcpiDxe_INF"
    File = "Universal/Acpi/AmdAcpiDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Universal_Acpi_AmdAcpiDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Universal_Acpi_AmdAcpiHmatService_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Universal_Acpi_AmdAcpiHmatService_INF"
    File = "Universal/Acpi/AmdAcpiHmatService.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Universal_Acpi_AmdAcpiHmatService_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Universal_AmdSmmCommunication_AmdSmmCommunication_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Universal_AmdSmmCommunication_AmdSmmCommunication_INF"
    File = "Universal/AmdSmmCommunication/AmdSmmCommunication.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Universal_AmdSmmCommunication_AmdSmmCommunication_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Universal_Version_AmdVersionDxe_AmdVersionDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Universal_Version_AmdVersionDxe_AmdVersionDxe_INF"
    File = "Universal/Version/AmdVersionDxe/AmdVersionDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Universal_Version_AmdVersionDxe_AmdVersionDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Ras_Brh_AmdRasBrhServiceDxe_AmdRasBrhServiceDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Ras_Brh_AmdRasBrhServiceDxe_AmdRasBrhServiceDxe_INF"
    File = "Ras/Brh/AmdRasBrhServiceDxe/AmdRasBrhServiceDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Ras_Brh_AmdRasBrhServiceDxe_AmdRasBrhServiceDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Ras_Brh_AmdRasBrhDxe_AmdRasBrhDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Ras_Brh_AmdRasBrhDxe_AmdRasBrhDxe_INF"
    File = "Ras/Brh/AmdRasBrhDxe/AmdRasBrhDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Ras_Brh_AmdRasBrhDxe_AmdRasBrhDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Ras_Brh_AmdRasBrhServiceSmm_AmdRasBrhServiceSmm_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Ras_Brh_AmdRasBrhServiceSmm_AmdRasBrhServiceSmm_INF"
    File = "Ras/Brh/AmdRasBrhServiceSmm/AmdRasBrhServiceSmm.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Ras_Brh_AmdRasBrhServiceSmm_AmdRasBrhServiceSmm_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Universal_ActDxe_ActDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Universal_ActDxe_ActDxe_INF"
    File = "Universal/ActDxe/ActDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Universal_ActDxe_ActDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdPostCodeLib_AmdPostCodeLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPostCodeLib_AmdPostCodeLib_INF"
    File = "Library/AmdPostCodeLib/AmdPostCodeLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdPostCodeLib_AmdPostCodeLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPostCodeLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPostCodeLib_AmdPostCodeLib_INF"
    Token = "_AgesaModulePkg_Library_AmdPostCodeLib_AmdPostCodeLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdSocBaseLib_AmdSocBaseLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdSocBaseLib_AmdSocBaseLib_INF"
    File = "Library/AmdSocBaseLib/AmdSocBaseLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdSocBaseLib_AmdSocBaseLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdSocBaseLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdSocBaseLib_AmdSocBaseLib_INF"
    Token = "_AgesaModulePkg_Library_AmdSocBaseLib_AmdSocBaseLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_AmdPspFlashUpdateLib_AmdPspFlashUpdateLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspFlashUpdateLib_AmdPspFlashUpdateLib_INF"
    File = "Library/AmdPspFlashUpdateLib/AmdPspFlashUpdateLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdPspFlashUpdateLib_AmdPspFlashUpdateLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspFlashUpdateLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspFlashUpdateLib_AmdPspFlashUpdateLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdPspFlashUpdateLib_AmdPspFlashUpdateLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AgesaModulePkg_Library_DxeFabricResourceSizeForEachRbLib_DxeFabricResourceSizeForEachRbLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_DxeFabricResourceSizeForEachRbLib_DxeFabricResourceSizeForEachRbLib_INF"
    File = "Library/DxeFabricResourceSizeForEachRbLib/DxeFabricResourceSizeForEachRbLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_DxeFabricResourceSizeForEachRbLib_DxeFabricResourceSizeForEachRbLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FabricResourceSizeForEachRbLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_DxeFabricResourceSizeForEachRbLib_DxeFabricResourceSizeForEachRbLib_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_DxeFabricResourceSizeForEachRbLib_DxeFabricResourceSizeForEachRbLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_AmdS3SaveLib_WOS3Save_AmdWOS3SaveLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdS3SaveLib_WOS3Save_AmdWOS3SaveLib_INF"
    File = "Library/AmdS3SaveLib/WOS3Save/AmdWOS3SaveLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_AmdS3SaveLib_WOS3Save_AmdWOS3SaveLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdS3SaveLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdS3SaveLib_WOS3Save_AmdWOS3SaveLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_AmdS3SaveLib_WOS3Save_AmdWOS3SaveLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_NbioClkReqControlLibNull_NbioClkReqControlLibNull_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_NbioClkReqControlLibNull_NbioClkReqControlLibNull_INF"
    File = "Library/NbioClkReqControlLibNull/NbioClkReqControlLibNull.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_NbioClkReqControlLibNull_NbioClkReqControlLibNull_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "OemClkReqControlLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_NbioClkReqControlLibNull_NbioClkReqControlLibNull_INF"
    Token = "_AgesaModulePkg_Library_NbioClkReqControlLibNull_NbioClkReqControlLibNull_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdErrorLogLib_AmdErrorLogLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdErrorLogLib_AmdErrorLogLib_INF"
    File = "Library/AmdErrorLogLib/AmdErrorLogLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdErrorLogLib_AmdErrorLogLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdErrorLogLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdErrorLogLib_AmdErrorLogLib_INF"
    Token = "_AgesaModulePkg_Library_AmdErrorLogLib_AmdErrorLogLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_CcxBaseX86Lib_CcxBaseX86Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_CcxBaseX86Lib_CcxBaseX86Lib_INF"
    File = "Library/CcxBaseX86Lib/CcxBaseX86Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_CcxBaseX86Lib_CcxBaseX86Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxBaseX86Lib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_CcxBaseX86Lib_CcxBaseX86Lib_INF"
    Token = "_AgesaModulePkg_Library_CcxBaseX86Lib_CcxBaseX86Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdTableLibV2_AmdTableLibV2_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdTableLibV2_AmdTableLibV2_INF"
    File = "Library/AmdTableLibV2/AmdTableLibV2.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdTableLibV2_AmdTableLibV2_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdTableLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdTableLibV2_AmdTableLibV2_INF"
    Token = "_AgesaModulePkg_Library_AmdTableLibV2_AmdTableLibV2_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_CcxPstatesZen5Lib_CcxPstatesZen5Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_CcxPstatesZen5Lib_CcxPstatesZen5Lib_INF"
    File = "Library/CcxPstatesZen5Lib/CcxPstatesZen5Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_CcxPstatesZen5Lib_CcxPstatesZen5Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxPstatesLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_CcxPstatesZen5Lib_CcxPstatesZen5Lib_INF"
    Token = "_AgesaModulePkg_Library_CcxPstatesZen5Lib_CcxPstatesZen5Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEI_CORE_AgesaModulePkg_Library_AmdTableLibV2_Pei_AmdTableHookPeiLibV2_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdTableLibV2_Pei_AmdTableHookPeiLibV2_INF"
    File = "Library/AmdTableLibV2/Pei/AmdTableHookPeiLibV2.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEI_CORE_AgesaModulePkg_Library_AmdTableLibV2_Pei_AmdTableHookPeiLibV2_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdTableHookLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdTableLibV2_Pei_AmdTableHookPeiLibV2_INF"
    ModuleTypes = "PEI_CORE"
    Token = "PEI_CORE_AgesaModulePkg_Library_AmdTableLibV2_Pei_AmdTableHookPeiLibV2_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_GnbCpuAccLib_GnbCpuAccLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_GnbCpuAccLib_GnbCpuAccLib_INF"
    File = "Library/GnbCpuAccLib/GnbCpuAccLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_GnbCpuAccLib_GnbCpuAccLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "GnbCpuAccLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_GnbCpuAccLib_GnbCpuAccLib_INF"
    Token = "_AgesaModulePkg_Library_GnbCpuAccLib_GnbCpuAccLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdMpmRegBaseLib_AmdMpmRegBaseLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdMpmRegBaseLib_AmdMpmRegBaseLib_INF"
    File = "Library/AmdMpmRegBaseLib/AmdMpmRegBaseLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdMpmRegBaseLib_AmdMpmRegBaseLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdMpmRegBaseLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdMpmRegBaseLib_AmdMpmRegBaseLib_INF"
    Token = "_AgesaModulePkg_Library_AmdMpmRegBaseLib_AmdMpmRegBaseLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_SmmFabricTopologyServices2Lib_SmmFabricTopologyServices2Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_SmmFabricTopologyServices2Lib_SmmFabricTopologyServices2Lib_INF"
    File = "Library/SmmFabricTopologyServices2Lib/SmmFabricTopologyServices2Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "_AgesaModulePkg_Library_SmmFabricTopologyServices2Lib_SmmFabricTopologyServices2Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "SmmFabricTopologyServices2Lib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_SmmFabricTopologyServices2Lib_SmmFabricTopologyServices2Lib_INF"
    Token = "_AgesaModulePkg_Library_SmmFabricTopologyServices2Lib_SmmFabricTopologyServices2Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdDirectoryBaseLib_AmdDirectoryBaseLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdDirectoryBaseLib_AmdDirectoryBaseLib_INF"
    File = "Library/AmdDirectoryBaseLib/AmdDirectoryBaseLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdDirectoryBaseLib_AmdDirectoryBaseLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdDirectoryBaseLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdDirectoryBaseLib_AmdDirectoryBaseLib_INF"
    Token = "_AgesaModulePkg_Library_AmdDirectoryBaseLib_AmdDirectoryBaseLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_AmdCfgPcdBufLibDxe_AmdCfgPcdBufLibDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdCfgPcdBufLibDxe_AmdCfgPcdBufLibDxe_INF"
    File = "Library/AmdCfgPcdBufLibDxe/AmdCfgPcdBufLibDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdCfgPcdBufLibDxe_AmdCfgPcdBufLibDxe_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdCfgPcdBufLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdCfgPcdBufLibDxe_AmdCfgPcdBufLibDxe_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdCfgPcdBufLibDxe_AmdCfgPcdBufLibDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_FchI2cLib_FchI2cLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FchI2cLib_FchI2cLib_INF"
    File = "Library/FchI2cLib/FchI2cLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_FchI2cLib_FchI2cLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FchI2cLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FchI2cLib_FchI2cLib_INF"
    Token = "_AgesaModulePkg_Library_FchI2cLib_FchI2cLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_RUNTIME_DRIVER_AgesaModulePkg_Library_AmdCfgPcdBufLibDxe_AmdCfgPcdBufLibDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdCfgPcdBufLibDxe_AmdCfgPcdBufLibDxe_INF"
    File = "Library/AmdCfgPcdBufLibDxe/AmdCfgPcdBufLibDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_RUNTIME_DRIVER_AgesaModulePkg_Library_AmdCfgPcdBufLibDxe_AmdCfgPcdBufLibDxe_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdCfgPcdBufLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdCfgPcdBufLibDxe_AmdCfgPcdBufLibDxe_INF"
    ModuleTypes = "DXE_RUNTIME_DRIVER"
    Token = "DXE_RUNTIME_DRIVER_AgesaModulePkg_Library_AmdCfgPcdBufLibDxe_AmdCfgPcdBufLibDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_RUNTIME_DRIVER_AgesaModulePkg_Library_AmdStbLibNull_AmdStbLibNull_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdStbLibNull_AmdStbLibNull_INF"
    File = "Library/AmdStbLibNull/AmdStbLibNull.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_RUNTIME_DRIVER_AgesaModulePkg_Library_AmdStbLibNull_AmdStbLibNull_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdStbLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdStbLibNull_AmdStbLibNull_INF"
    ModuleTypes = "DXE_RUNTIME_DRIVER"
    Token = "DXE_RUNTIME_DRIVER_AgesaModulePkg_Library_AmdStbLibNull_AmdStbLibNull_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdIdsHookLibNull_AmdIdsHookLibNull_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdIdsHookLibNull_AmdIdsHookLibNull_INF"
    File = "Library/AmdIdsHookLibNull/AmdIdsHookLibNull.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdIdsHookLibNull_AmdIdsHookLibNull_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdIdsHookLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdIdsHookLibNull_AmdIdsHookLibNull_INF"
    Token = "_AgesaModulePkg_Library_AmdIdsHookLibNull_AmdIdsHookLibNull_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AgesaModulePkg_Library_ApcbVariableLibV3_ApcbVariableLibV3_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_ApcbVariableLibV3_ApcbVariableLibV3_INF"
    File = "Library/ApcbVariableLibV3/ApcbVariableLibV3.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_ApcbVariableLibV3_ApcbVariableLibV3_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "ApcbVariableLibV3"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_ApcbVariableLibV3_ApcbVariableLibV3_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_ApcbVariableLibV3_ApcbVariableLibV3_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdPspFwImageHeaderLib_AmdPspFwImageHeaderLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspFwImageHeaderLib_AmdPspFwImageHeaderLib_INF"
    File = "Library/AmdPspFwImageHeaderLib/AmdPspFwImageHeaderLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdPspFwImageHeaderLib_AmdPspFwImageHeaderLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspFwImageHeaderLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspFwImageHeaderLib_AmdPspFwImageHeaderLib_INF"
    Token = "_AgesaModulePkg_Library_AmdPspFwImageHeaderLib_AmdPspFwImageHeaderLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_FchEspiLib_FchEspiLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FchEspiLib_FchEspiLib_INF"
    File = "Library/FchEspiLib/FchEspiLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_FchEspiLib_FchEspiLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FchEspiLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FchEspiLib_FchEspiLib_INF"
    Token = "_AgesaModulePkg_Library_FchEspiLib_FchEspiLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdBaseLib_AmdBaseLibNoIntrinsic_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdBaseLib_AmdBaseLibNoIntrinsic_INF"
    File = "Library/AmdBaseLib/AmdBaseLibNoIntrinsic.inf"
    Package = "AgesaModulePkg"
    Arch = "IA32 X64"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdBaseLib_AmdBaseLibNoIntrinsic_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdBaseLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdBaseLib_AmdBaseLibNoIntrinsic_INF"
    Token = "_AgesaModulePkg_Library_AmdBaseLib_AmdBaseLibNoIntrinsic_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Nbio_Library_CxlCdatLib_CxlCdatLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Nbio_Library_CxlCdatLib_CxlCdatLib_INF"
    File = "Nbio/Library/CxlCdatLib/CxlCdatLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Nbio_Library_CxlCdatLib_CxlCdatLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CxlCdatLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Nbio_Library_CxlCdatLib_CxlCdatLib_INF"
    Token = "_AgesaModulePkg_Nbio_Library_CxlCdatLib_CxlCdatLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_GnbPciSegmentAccLib_GnbPciSegmentAccLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_GnbPciSegmentAccLib_GnbPciSegmentAccLib_INF"
    File = "Library/GnbPciSegmentAccLib/GnbPciSegmentAccLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_GnbPciSegmentAccLib_GnbPciSegmentAccLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "GnbPciAccLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_GnbPciSegmentAccLib_GnbPciSegmentAccLib_INF"
    Token = "_AgesaModulePkg_Library_GnbPciSegmentAccLib_GnbPciSegmentAccLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_AmdPspRegMuxLibV2Null_AmdPspRegMuxLibV2_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspRegMuxLibV2Null_AmdPspRegMuxLibV2_INF"
    File = "Library/AmdPspRegMuxLibV2Null/AmdPspRegMuxLibV2.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_AmdPspRegMuxLibV2Null_AmdPspRegMuxLibV2_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspRegMuxLibV2"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspRegMuxLibV2Null_AmdPspRegMuxLibV2_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_AmdPspRegMuxLibV2Null_AmdPspRegMuxLibV2_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Nbio_Library_MpioLibV2_MpioLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Nbio_Library_MpioLibV2_MpioLib_INF"
    File = "Nbio/Library/MpioLibV2/MpioLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Nbio_Library_MpioLibV2_MpioLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "MpioLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Nbio_Library_MpioLibV2_MpioLib_INF"
    Token = "_AgesaModulePkg_Nbio_Library_MpioLibV2_MpioLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_AmdCapsuleLibPei_AmdCapsuleLibPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdCapsuleLibPei_AmdCapsuleLibPei_INF"
    File = "Library/AmdCapsuleLibPei/AmdCapsuleLibPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_AmdCapsuleLibPei_AmdCapsuleLibPei_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdCapsuleLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdCapsuleLibPei_AmdCapsuleLibPei_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_AmdCapsuleLibPei_AmdCapsuleLibPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_FchSmmLib_FchSmmLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FchSmmLib_FchSmmLib_INF"
    File = "Library/FchSmmLib/FchSmmLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_FchSmmLib_FchSmmLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FchSmmLibV9"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FchSmmLib_FchSmmLib_INF"
    Token = "_AgesaModulePkg_Library_FchSmmLib_FchSmmLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_PcieConfigLib_PcieConfigLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_PcieConfigLib_PcieConfigLib_INF"
    File = "Library/PcieConfigLib/PcieConfigLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_PcieConfigLib_PcieConfigLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "PcieConfigLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_PcieConfigLib_PcieConfigLib_INF"
    Token = "_AgesaModulePkg_Library_PcieConfigLib_PcieConfigLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_BaseSocLogicalIdXlatZen5DieLib_BaseSocLogicalIdXlatZen5DieLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_BaseSocLogicalIdXlatZen5DieLib_BaseSocLogicalIdXlatZen5DieLib_INF"
    File = "Library/BaseSocLogicalIdXlatZen5DieLib/BaseSocLogicalIdXlatZen5DieLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_BaseSocLogicalIdXlatZen5DieLib_BaseSocLogicalIdXlatZen5DieLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "BaseSocLogicalIdXlatLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_BaseSocLogicalIdXlatZen5DieLib_BaseSocLogicalIdXlatZen5DieLib_INF"
    Token = "_AgesaModulePkg_Library_BaseSocLogicalIdXlatZen5DieLib_BaseSocLogicalIdXlatZen5DieLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEI_CORE_AgesaModulePkg_Library_AmdHeapPeiLib_AmdHeapPeiLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdHeapPeiLib_AmdHeapPeiLib_INF"
    File = "Library/AmdHeapPeiLib/AmdHeapPeiLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEI_CORE_AgesaModulePkg_Library_AmdHeapPeiLib_AmdHeapPeiLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdHeapLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdHeapPeiLib_AmdHeapPeiLib_INF"
    ModuleTypes = "PEI_CORE"
    Token = "PEI_CORE_AgesaModulePkg_Library_AmdHeapPeiLib_AmdHeapPeiLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_GnbLib_GnbLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_GnbLib_GnbLib_INF"
    File = "Library/GnbLib/GnbLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_GnbLib_GnbLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "GnbLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_GnbLib_GnbLib_INF"
    Token = "_AgesaModulePkg_Library_GnbLib_GnbLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdPspRegMuxLibV2Null_AmdPspRegMuxLibV2_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspRegMuxLibV2Null_AmdPspRegMuxLibV2_INF"
    File = "Library/AmdPspRegMuxLibV2Null/AmdPspRegMuxLibV2.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdPspRegMuxLibV2Null_AmdPspRegMuxLibV2_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspRegMuxLibV2"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspRegMuxLibV2Null_AmdPspRegMuxLibV2_INF"
    Token = "_AgesaModulePkg_Library_AmdPspRegMuxLibV2Null_AmdPspRegMuxLibV2_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdIdsExtLibNull_AmdIdsHookExtLibNull_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdIdsExtLibNull_AmdIdsHookExtLibNull_INF"
    File = "Library/AmdIdsExtLibNull/AmdIdsHookExtLibNull.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdIdsExtLibNull_AmdIdsHookExtLibNull_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdIdsHookExtLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdIdsExtLibNull_AmdIdsHookExtLibNull_INF"
    Token = "_AgesaModulePkg_Library_AmdIdsExtLibNull_AmdIdsHookExtLibNull_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_RUNTIME_DRIVER_AgesaModulePkg_Library_AmdHeapDxeLib_AmdHeapDxeLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdHeapDxeLib_AmdHeapDxeLib_INF"
    File = "Library/AmdHeapDxeLib/AmdHeapDxeLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_RUNTIME_DRIVER_AgesaModulePkg_Library_AmdHeapDxeLib_AmdHeapDxeLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdHeapLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdHeapDxeLib_AmdHeapDxeLib_INF"
    ModuleTypes = "DXE_RUNTIME_DRIVER"
    Token = "DXE_RUNTIME_DRIVER_AgesaModulePkg_Library_AmdHeapDxeLib_AmdHeapDxeLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_DxeFabricTopologyServices2Lib_DxeFabricTopologyServices2Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_DxeFabricTopologyServices2Lib_DxeFabricTopologyServices2Lib_INF"
    File = "Library/DxeFabricTopologyServices2Lib/DxeFabricTopologyServices2Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "_AgesaModulePkg_Library_DxeFabricTopologyServices2Lib_DxeFabricTopologyServices2Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "DxeFabricTopologyServices2Lib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_DxeFabricTopologyServices2Lib_DxeFabricTopologyServices2Lib_INF"
    Token = "_AgesaModulePkg_Library_DxeFabricTopologyServices2Lib_DxeFabricTopologyServices2Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdPspPsbFusingLib_AmdPspPsbFusingLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspPsbFusingLib_AmdPspPsbFusingLib_INF"
    File = "Library/AmdPspPsbFusingLib/AmdPspPsbFusingLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdPspPsbFusingLib_AmdPspPsbFusingLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspPsbFusingLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspPsbFusingLib_AmdPspPsbFusingLib_INF"
    Token = "_AgesaModulePkg_Library_AmdPspPsbFusingLib_AmdPspPsbFusingLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_PeiSocBistLogging3Lib_PeiSocBistLogging3Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_PeiSocBistLogging3Lib_PeiSocBistLogging3Lib_INF"
    File = "Library/PeiSocBistLogging3Lib/PeiSocBistLogging3Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_PeiSocBistLogging3Lib_PeiSocBistLogging3Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "PeiSocBistLogging3Lib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_PeiSocBistLogging3Lib_PeiSocBistLogging3Lib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_PeiSocBistLogging3Lib_PeiSocBistLogging3Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_DxeCcxCoreTopologyServicesV3OnV2Lib_DxeCcxCoreTopologyServicesV3OnV2Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_DxeCcxCoreTopologyServicesV3OnV2Lib_DxeCcxCoreTopologyServicesV3OnV2Lib_INF"
    File = "Library/DxeCcxCoreTopologyServicesV3OnV2Lib/DxeCcxCoreTopologyServicesV3OnV2Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_DxeCcxCoreTopologyServicesV3OnV2Lib_DxeCcxCoreTopologyServicesV3OnV2Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "DxeCoreTopologyServicesV3Lib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_DxeCcxCoreTopologyServicesV3OnV2Lib_DxeCcxCoreTopologyServicesV3OnV2Lib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_DxeCcxCoreTopologyServicesV3OnV2Lib_DxeCcxCoreTopologyServicesV3OnV2Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "SEC_AgesaModulePkg_Library_AmdEmulationAutoDetectPeiLib_AmdEmulationAutoDetectPeiLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdEmulationAutoDetectPeiLib_AmdEmulationAutoDetectPeiLib_INF"
    File = "Library/AmdEmulationAutoDetectPeiLib/AmdEmulationAutoDetectPeiLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "SEC_AgesaModulePkg_Library_AmdEmulationAutoDetectPeiLib_AmdEmulationAutoDetectPeiLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdEmulationAutoDetectLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdEmulationAutoDetectPeiLib_AmdEmulationAutoDetectPeiLib_INF"
    ModuleTypes = "SEC"
    Token = "SEC_AgesaModulePkg_Library_AmdEmulationAutoDetectPeiLib_AmdEmulationAutoDetectPeiLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_AmdS3SaveLib_S3Save_AmdS3SaveLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdS3SaveLib_S3Save_AmdS3SaveLib_INF"
    File = "Library/AmdS3SaveLib/S3Save/AmdS3SaveLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdS3SaveLib_S3Save_AmdS3SaveLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdS3SaveLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdS3SaveLib_S3Save_AmdS3SaveLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdS3SaveLib_S3Save_AmdS3SaveLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_ApcbHmacChecksumLibV3_ApcbHmacChecksumLibV3_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_ApcbHmacChecksumLibV3_ApcbHmacChecksumLibV3_INF"
    File = "Library/ApcbHmacChecksumLibV3/ApcbHmacChecksumLibV3.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_ApcbHmacChecksumLibV3_ApcbHmacChecksumLibV3_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "ApcbChecksumLibV3"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_ApcbHmacChecksumLibV3_ApcbHmacChecksumLibV3_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_ApcbHmacChecksumLibV3_ApcbHmacChecksumLibV3_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspCommonLibDxe_AmdPspCommonLibDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspCommonLibDxe_AmdPspCommonLibDxe_INF"
    File = "Library/AmdPspCommonLibDxe/AmdPspCommonLibDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspCommonLibDxe_AmdPspCommonLibDxe_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspCommonLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspCommonLibDxe_AmdPspCommonLibDxe_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspCommonLibDxe_AmdPspCommonLibDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_Ras_Brh_DfAddressTranslateBrhLib_DfAddressTranslateBrhLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_Ras_Brh_DfAddressTranslateBrhLib_DfAddressTranslateBrhLib_INF"
    File = "Library/Ras/Brh/DfAddressTranslateBrhLib/DfAddressTranslateBrhLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_Ras_Brh_DfAddressTranslateBrhLib_DfAddressTranslateBrhLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "DfAddressTranslateLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_Ras_Brh_DfAddressTranslateBrhLib_DfAddressTranslateBrhLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_Ras_Brh_DfAddressTranslateBrhLib_DfAddressTranslateBrhLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_FabricRegisterAccDf4Lib_FabricRegisterAccDf4Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FabricRegisterAccDf4Lib_FabricRegisterAccDf4Lib_INF"
    File = "Library/FabricRegisterAccDf4Lib/FabricRegisterAccDf4Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_FabricRegisterAccDf4Lib_FabricRegisterAccDf4Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FabricRegisterAccLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FabricRegisterAccDf4Lib_FabricRegisterAccDf4Lib_INF"
    Token = "_AgesaModulePkg_Library_FabricRegisterAccDf4Lib_FabricRegisterAccDf4Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_FchDxeLib_FchDxeLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FchDxeLib_FchDxeLib_INF"
    File = "Library/FchDxeLib/FchDxeLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "_AgesaModulePkg_Library_FchDxeLib_FchDxeLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FchDxeLibV9"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FchDxeLib_FchDxeLib_INF"
    Token = "_AgesaModulePkg_Library_FchDxeLib_FchDxeLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_AmdIdsHookLibPei_AmdIdsHookLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdIdsHookLibPei_AmdIdsHookLib_INF"
    File = "Library/AmdIdsHookLibPei/AmdIdsHookLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_AmdIdsHookLibPei_AmdIdsHookLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdIdsHookLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdIdsHookLibPei_AmdIdsHookLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_AmdIdsHookLibPei_AmdIdsHookLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_AmdEmulationFlagLib_AmdEmulationFlagPeiLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdEmulationFlagLib_AmdEmulationFlagPeiLib_INF"
    File = "Library/AmdEmulationFlagLib/AmdEmulationFlagPeiLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_AmdEmulationFlagLib_AmdEmulationFlagPeiLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdEmulationFlagLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdEmulationFlagLib_AmdEmulationFlagPeiLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_AmdEmulationFlagLib_AmdEmulationFlagPeiLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_Ras_RasIdsPeiLib_RasIdsPeiLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_Ras_RasIdsPeiLib_RasIdsPeiLib_INF"
    File = "Library/Ras/RasIdsPeiLib/RasIdsPeiLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_Ras_RasIdsPeiLib_RasIdsPeiLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "RasIdsLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_Ras_RasIdsPeiLib_RasIdsPeiLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_Ras_RasIdsPeiLib_RasIdsPeiLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdAcpiAmlLib_AmdAcpiAmlLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdAcpiAmlLib_AmdAcpiAmlLib_INF"
    File = "Library/AmdAcpiAmlLib/AmdAcpiAmlLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdAcpiAmlLib_AmdAcpiAmlLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdAcpiAmlLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdAcpiAmlLib_AmdAcpiAmlLib_INF"
    Token = "_AgesaModulePkg_Library_AmdAcpiAmlLib_AmdAcpiAmlLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Nbio_Library_CommonPei_NbioCommonPeiLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Nbio_Library_CommonPei_NbioCommonPeiLib_INF"
    File = "Nbio/Library/CommonPei/NbioCommonPeiLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Nbio_Library_CommonPei_NbioCommonPeiLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "NbioCommonPeiLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Nbio_Library_CommonPei_NbioCommonPeiLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Nbio_Library_CommonPei_NbioCommonPeiLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdEmulationFlagLib_AmdEmulationFlagDxeSmmLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdEmulationFlagLib_AmdEmulationFlagDxeSmmLib_INF"
    File = "Library/AmdEmulationFlagLib/AmdEmulationFlagDxeSmmLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdEmulationFlagLib_AmdEmulationFlagDxeSmmLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdEmulationFlagLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdEmulationFlagLib_AmdEmulationFlagDxeSmmLib_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdEmulationFlagLib_AmdEmulationFlagDxeSmmLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_DxeCcxSmmAccess2Lib_DxeCcxSmmAccess2Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_DxeCcxSmmAccess2Lib_DxeCcxSmmAccess2Lib_INF"
    File = "Library/DxeCcxSmmAccess2Lib/DxeCcxSmmAccess2Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_DxeCcxSmmAccess2Lib_DxeCcxSmmAccess2Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxSmmAccess2Lib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_DxeCcxSmmAccess2Lib_DxeCcxSmmAccess2Lib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_DxeCcxSmmAccess2Lib_DxeCcxSmmAccess2Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdPspHstiStateLib_AmdPspHstiStateLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspHstiStateLib_AmdPspHstiStateLib_INF"
    File = "Library/AmdPspHstiStateLib/AmdPspHstiStateLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdPspHstiStateLib_AmdPspHstiStateLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspHstiStateLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspHstiStateLib_AmdPspHstiStateLib_INF"
    Token = "_AgesaModulePkg_Library_AmdPspHstiStateLib_AmdPspHstiStateLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdPspApobLib_AmdPspApobLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspApobLib_AmdPspApobLib_INF"
    File = "Library/AmdPspApobLib/AmdPspApobLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdPspApobLib_AmdPspApobLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspApobLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspApobLib_AmdPspApobLib_INF"
    Token = "_AgesaModulePkg_Library_AmdPspApobLib_AmdPspApobLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Nbio_Library_IvrsDeviceDfltLib_IvrsDeviceDfltLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Nbio_Library_IvrsDeviceDfltLib_IvrsDeviceDfltLib_INF"
    File = "Nbio/Library/IvrsDeviceDfltLib/IvrsDeviceDfltLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Nbio_Library_IvrsDeviceDfltLib_IvrsDeviceDfltLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "IvrsDeviceInfoLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Nbio_Library_IvrsDeviceDfltLib_IvrsDeviceDfltLib_INF"
    Token = "_AgesaModulePkg_Nbio_Library_IvrsDeviceDfltLib_IvrsDeviceDfltLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_AmdIdsHookLibDxe_AmdIdsHookLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdIdsHookLibDxe_AmdIdsHookLib_INF"
    File = "Library/AmdIdsHookLibDxe/AmdIdsHookLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdIdsHookLibDxe_AmdIdsHookLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdIdsHookLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdIdsHookLibDxe_AmdIdsHookLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdIdsHookLibDxe_AmdIdsHookLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_BaseFabricTopologyBrhLib_BaseFabricTopologyBrhLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_BaseFabricTopologyBrhLib_BaseFabricTopologyBrhLib_INF"
    File = "Library/BaseFabricTopologyBrhLib/BaseFabricTopologyBrhLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_BaseFabricTopologyBrhLib_BaseFabricTopologyBrhLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "BaseFabricTopologyLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_BaseFabricTopologyBrhLib_BaseFabricTopologyBrhLib_INF"
    Token = "_AgesaModulePkg_Library_BaseFabricTopologyBrhLib_BaseFabricTopologyBrhLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_FchI3cLib_FchI3cLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FchI3cLib_FchI3cLib_INF"
    File = "Library/FchI3cLib/FchI3cLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_FchI3cLib_FchI3cLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FchI3cLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FchI3cLib_FchI3cLib_INF"
    Token = "_AgesaModulePkg_Library_FchI3cLib_FchI3cLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_PeiCcxSmmAccessLib_PeiCcxSmmAccessLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_PeiCcxSmmAccessLib_PeiCcxSmmAccessLib_INF"
    File = "Library/PeiCcxSmmAccessLib/PeiCcxSmmAccessLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_PeiCcxSmmAccessLib_PeiCcxSmmAccessLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxPeiSmmAccessLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_PeiCcxSmmAccessLib_PeiCcxSmmAccessLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_PeiCcxSmmAccessLib_PeiCcxSmmAccessLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdStbLib_AmdStbLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdStbLib_AmdStbLib_INF"
    File = "Library/AmdStbLib/AmdStbLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdStbLib_AmdStbLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdStbLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdStbLib_AmdStbLib_INF"
    Token = "_AgesaModulePkg_Library_AmdStbLib_AmdStbLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_CcxMpServicesDxeLib_CcxMpServicesDxeLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_CcxMpServicesDxeLib_CcxMpServicesDxeLib_INF"
    File = "Library/CcxMpServicesDxeLib/CcxMpServicesDxeLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_CcxMpServicesDxeLib_CcxMpServicesDxeLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxMpServicesLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_CcxMpServicesDxeLib_CcxMpServicesDxeLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_CcxMpServicesDxeLib_CcxMpServicesDxeLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_AmdPspCommonLibDxe_AmdPspCommonLibDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspCommonLibDxe_AmdPspCommonLibDxe_INF"
    File = "Library/AmdPspCommonLibDxe/AmdPspCommonLibDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdPspCommonLibDxe_AmdPspCommonLibDxe_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspCommonLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspCommonLibDxe_AmdPspCommonLibDxe_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdPspCommonLibDxe_AmdPspCommonLibDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_CcxPspLib_CcxPspLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_CcxPspLib_CcxPspLib_INF"
    File = "Library/CcxPspLib/CcxPspLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_CcxPspLib_CcxPspLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxPspLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_CcxPspLib_CcxPspLib_INF"
    Token = "_AgesaModulePkg_Library_CcxPspLib_CcxPspLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdS3SaveLib_WOS3Save_AmdWOS3SaveLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdS3SaveLib_WOS3Save_AmdWOS3SaveLib_INF"
    File = "Library/AmdS3SaveLib/WOS3Save/AmdWOS3SaveLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdS3SaveLib_WOS3Save_AmdWOS3SaveLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdS3SaveLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdS3SaveLib_WOS3Save_AmdWOS3SaveLib_INF"
    Token = "_AgesaModulePkg_Library_AmdS3SaveLib_WOS3Save_AmdWOS3SaveLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_AmdTableLibV2_Pei_AmdTableHookPeiLibV2_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdTableLibV2_Pei_AmdTableHookPeiLibV2_INF"
    File = "Library/AmdTableLibV2/Pei/AmdTableHookPeiLibV2.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_AmdTableLibV2_Pei_AmdTableHookPeiLibV2_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdTableHookLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdTableLibV2_Pei_AmdTableHookPeiLibV2_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_AmdTableLibV2_Pei_AmdTableHookPeiLibV2_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_SocCmnIdsHookBrhLib_Pei_SocCmnIdsHookBrhLibPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_SocCmnIdsHookBrhLib_Pei_SocCmnIdsHookBrhLibPei_INF"
    File = "Library/SocCmnIdsHookBrhLib/Pei/SocCmnIdsHookBrhLibPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_SocCmnIdsHookBrhLib_Pei_SocCmnIdsHookBrhLibPei_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "SocCmnIdsHookBrhLibPei"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_SocCmnIdsHookBrhLib_Pei_SocCmnIdsHookBrhLibPei_INF"
    Token = "_AgesaModulePkg_Library_SocCmnIdsHookBrhLib_Pei_SocCmnIdsHookBrhLibPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_Ras_RasIdsDxeLib_RasIdsDxeLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_Ras_RasIdsDxeLib_RasIdsDxeLib_INF"
    File = "Library/Ras/RasIdsDxeLib/RasIdsDxeLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_Ras_RasIdsDxeLib_RasIdsDxeLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "RasIdsLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_Ras_RasIdsDxeLib_RasIdsDxeLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_Ras_RasIdsDxeLib_RasIdsDxeLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_ApcbLibV3_ApcbLibV3_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_ApcbLibV3_ApcbLibV3_INF"
    File = "Library/ApcbLibV3/ApcbLibV3.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_ApcbLibV3_ApcbLibV3_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "ApcbLibV3"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_ApcbLibV3_ApcbLibV3_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_ApcbLibV3_ApcbLibV3_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspRomArmorLib_AmdPspRomArmorLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspRomArmorLib_AmdPspRomArmorLib_INF"
    File = "Library/AmdPspRomArmorLib/AmdPspRomArmorLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspRomArmorLib_AmdPspRomArmorLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspRomArmorLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspRomArmorLib_AmdPspRomArmorLib_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspRomArmorLib_AmdPspRomArmorLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_FabricResourceReportToGcdLib_FabricResourceReportToGcdLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FabricResourceReportToGcdLib_FabricResourceReportToGcdLib_INF"
    File = "Library/FabricResourceReportToGcdLib/FabricResourceReportToGcdLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_FabricResourceReportToGcdLib_FabricResourceReportToGcdLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FabricResourceReportToGcdLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FabricResourceReportToGcdLib_FabricResourceReportToGcdLib_INF"
    Token = "_AgesaModulePkg_Library_FabricResourceReportToGcdLib_FabricResourceReportToGcdLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_DxeSocLogicalIdServicesLib_DxeSocLogicalIdServicesLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_DxeSocLogicalIdServicesLib_DxeSocLogicalIdServicesLib_INF"
    File = "Library/DxeSocLogicalIdServicesLib/DxeSocLogicalIdServicesLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_DxeSocLogicalIdServicesLib_DxeSocLogicalIdServicesLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "DxeSocLogicalIdServicesLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_DxeSocLogicalIdServicesLib_DxeSocLogicalIdServicesLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_DxeSocLogicalIdServicesLib_DxeSocLogicalIdServicesLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_SocCmnIdsHookBrhLib_Dxe_SocCmnIdsHookBrhLibDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_SocCmnIdsHookBrhLib_Dxe_SocCmnIdsHookBrhLibDxe_INF"
    File = "Library/SocCmnIdsHookBrhLib/Dxe/SocCmnIdsHookBrhLibDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_SocCmnIdsHookBrhLib_Dxe_SocCmnIdsHookBrhLibDxe_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "SocCmnIdsHookBrhLibDxe"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_SocCmnIdsHookBrhLib_Dxe_SocCmnIdsHookBrhLibDxe_INF"
    Token = "_AgesaModulePkg_Library_SocCmnIdsHookBrhLib_Dxe_SocCmnIdsHookBrhLibDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_AmdPspDxeSmmBufLib_AmdPspDxeSmmBufLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspDxeSmmBufLib_AmdPspDxeSmmBufLib_INF"
    File = "Library/AmdPspDxeSmmBufLib/AmdPspDxeSmmBufLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdPspDxeSmmBufLib_AmdPspDxeSmmBufLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspDxeSmmBufLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspDxeSmmBufLib_AmdPspDxeSmmBufLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdPspDxeSmmBufLib_AmdPspDxeSmmBufLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Nbio_Library_CxlCedtLib_CxlCedtLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Nbio_Library_CxlCedtLib_CxlCedtLib_INF"
    File = "Nbio/Library/CxlCedtLib/CxlCedtLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Nbio_Library_CxlCedtLib_CxlCedtLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CxlCedtLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Nbio_Library_CxlCedtLib_CxlCedtLib_INF"
    Token = "_AgesaModulePkg_Nbio_Library_CxlCedtLib_CxlCedtLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Nbio_Library_MpioInitLib_MpioInitLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Nbio_Library_MpioInitLib_MpioInitLib_INF"
    File = "Nbio/Library/MpioInitLib/MpioInitLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Nbio_Library_MpioInitLib_MpioInitLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "MpioInitLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Nbio_Library_MpioInitLib_MpioInitLib_INF"
    Token = "_AgesaModulePkg_Nbio_Library_MpioInitLib_MpioInitLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_IdsDxeLib_IdsDxeLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_IdsDxeLib_IdsDxeLib_INF"
    File = "Library/IdsDxeLib/IdsDxeLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_IdsDxeLib_IdsDxeLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "IdsLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_IdsDxeLib_IdsDxeLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_IdsDxeLib_IdsDxeLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_AmdEmulationAutoDetectPeiLib_AmdEmulationAutoDetectPeiLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdEmulationAutoDetectPeiLib_AmdEmulationAutoDetectPeiLib_INF"
    File = "Library/AmdEmulationAutoDetectPeiLib/AmdEmulationAutoDetectPeiLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_AmdEmulationAutoDetectPeiLib_AmdEmulationAutoDetectPeiLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdEmulationAutoDetectLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdEmulationAutoDetectPeiLib_AmdEmulationAutoDetectPeiLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_AmdEmulationAutoDetectPeiLib_AmdEmulationAutoDetectPeiLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_AmdPspCommonLibPei_AmdPspCommonLibPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspCommonLibPei_AmdPspCommonLibPei_INF"
    File = "Library/AmdPspCommonLibPei/AmdPspCommonLibPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_AmdPspCommonLibPei_AmdPspCommonLibPei_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspCommonLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspCommonLibPei_AmdPspCommonLibPei_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_AmdPspCommonLibPei_AmdPspCommonLibPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_AmdHeapPeiLib_AmdHeapPeiLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdHeapPeiLib_AmdHeapPeiLib_INF"
    File = "Library/AmdHeapPeiLib/AmdHeapPeiLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_AmdHeapPeiLib_AmdHeapPeiLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdHeapLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdHeapPeiLib_AmdHeapPeiLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_AmdHeapPeiLib_AmdHeapPeiLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_DxeFabricResourceManagerServicesLib_DxeFabricResourceManagerServicesLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_DxeFabricResourceManagerServicesLib_DxeFabricResourceManagerServicesLib_INF"
    File = "Library/DxeFabricResourceManagerServicesLib/DxeFabricResourceManagerServicesLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "_AgesaModulePkg_Library_DxeFabricResourceManagerServicesLib_DxeFabricResourceManagerServicesLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "DxeFabricResourceManagerServicesLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_DxeFabricResourceManagerServicesLib_DxeFabricResourceManagerServicesLib_INF"
    Token = "_AgesaModulePkg_Library_DxeFabricResourceManagerServicesLib_DxeFabricResourceManagerServicesLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdPspMmioLib_AmdPspMmioLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspMmioLib_AmdPspMmioLib_INF"
    File = "Library/AmdPspMmioLib/AmdPspMmioLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdPspMmioLib_AmdPspMmioLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspMmioLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspMmioLib_AmdPspMmioLib_INF"
    Token = "_AgesaModulePkg_Library_AmdPspMmioLib_AmdPspMmioLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_NbioRegisterAccLib_NbioRegisterAcc_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_NbioRegisterAccLib_NbioRegisterAcc_INF"
    File = "Library/NbioRegisterAccLib/NbioRegisterAcc.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_NbioRegisterAccLib_NbioRegisterAcc_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "NbioRegisterAccLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_NbioRegisterAccLib_NbioRegisterAcc_INF"
    Token = "_AgesaModulePkg_Library_NbioRegisterAccLib_NbioRegisterAcc_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdPspMboxLibV2_AmdPspMboxLibV2_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspMboxLibV2_AmdPspMboxLibV2_INF"
    File = "Library/AmdPspMboxLibV2/AmdPspMboxLibV2.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdPspMboxLibV2_AmdPspMboxLibV2_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspMboxLibV2"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspMboxLibV2_AmdPspMboxLibV2_INF"
    Token = "_AgesaModulePkg_Library_AmdPspMboxLibV2_AmdPspMboxLibV2_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Nbio_Library_CxlConfigLib_CxlConfigLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Nbio_Library_CxlConfigLib_CxlConfigLib_INF"
    File = "Nbio/Library/CxlConfigLib/CxlConfigLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Nbio_Library_CxlConfigLib_CxlConfigLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CxlConfigLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Nbio_Library_CxlConfigLib_CxlConfigLib_INF"
    Token = "_AgesaModulePkg_Nbio_Library_CxlConfigLib_CxlConfigLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdEmulationAutoDetectDxeLib_AmdEmulationAutoDetectDxeLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdEmulationAutoDetectDxeLib_AmdEmulationAutoDetectDxeLib_INF"
    File = "Library/AmdEmulationAutoDetectDxeLib/AmdEmulationAutoDetectDxeLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdEmulationAutoDetectDxeLib_AmdEmulationAutoDetectDxeLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdEmulationAutoDetectLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdEmulationAutoDetectDxeLib_AmdEmulationAutoDetectDxeLib_INF"
    Token = "_AgesaModulePkg_Library_AmdEmulationAutoDetectDxeLib_AmdEmulationAutoDetectDxeLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_CcxZen5BrhIdsHookLib_Dxe_CcxZen5BrhIdsHookLibDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_CcxZen5BrhIdsHookLib_Dxe_CcxZen5BrhIdsHookLibDxe_INF"
    File = "Library/CcxZen5BrhIdsHookLib/Dxe/CcxZen5BrhIdsHookLibDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_CcxZen5BrhIdsHookLib_Dxe_CcxZen5BrhIdsHookLibDxe_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxZen5IdsHookLibDxe"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_CcxZen5BrhIdsHookLib_Dxe_CcxZen5BrhIdsHookLibDxe_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_CcxZen5BrhIdsHookLib_Dxe_CcxZen5BrhIdsHookLibDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_AmdPspRomArmorLibNull_AmdPspRomArmorLibNull_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspRomArmorLibNull_AmdPspRomArmorLibNull_INF"
    File = "Library/AmdPspRomArmorLibNull/AmdPspRomArmorLibNull.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdPspRomArmorLibNull_AmdPspRomArmorLibNull_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspRomArmorLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspRomArmorLibNull_AmdPspRomArmorLibNull_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdPspRomArmorLibNull_AmdPspRomArmorLibNull_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AgesaModulePkg_Library_Ras_Brh_DfAddressTranslateBrhLib_DfAddressTranslateBrhLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_Ras_Brh_DfAddressTranslateBrhLib_DfAddressTranslateBrhLib_INF"
    File = "Library/Ras/Brh/DfAddressTranslateBrhLib/DfAddressTranslateBrhLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_Ras_Brh_DfAddressTranslateBrhLib_DfAddressTranslateBrhLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "DfAddressTranslateLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_Ras_Brh_DfAddressTranslateBrhLib_DfAddressTranslateBrhLib_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_Ras_Brh_DfAddressTranslateBrhLib_DfAddressTranslateBrhLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_AmdCapsuleLibDxe_AmdCapsuleLibDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdCapsuleLibDxe_AmdCapsuleLibDxe_INF"
    File = "Library/AmdCapsuleLibDxe/AmdCapsuleLibDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdCapsuleLibDxe_AmdCapsuleLibDxe_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdCapsuleLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdCapsuleLibDxe_AmdCapsuleLibDxe_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdCapsuleLibDxe_AmdCapsuleLibDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_AmdEmulationFlagLib_AmdEmulationFlagDxeSmmLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdEmulationFlagLib_AmdEmulationFlagDxeSmmLib_INF"
    File = "Library/AmdEmulationFlagLib/AmdEmulationFlagDxeSmmLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdEmulationFlagLib_AmdEmulationFlagDxeSmmLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdEmulationFlagLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdEmulationFlagLib_AmdEmulationFlagDxeSmmLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdEmulationFlagLib_AmdEmulationFlagDxeSmmLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AgesaModulePkg_Library_ApcbHmacChecksumLibV3_ApcbHmacChecksumLibV3_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_ApcbHmacChecksumLibV3_ApcbHmacChecksumLibV3_INF"
    File = "Library/ApcbHmacChecksumLibV3/ApcbHmacChecksumLibV3.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_ApcbHmacChecksumLibV3_ApcbHmacChecksumLibV3_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "ApcbChecksumLibV3"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_ApcbHmacChecksumLibV3_ApcbHmacChecksumLibV3_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_ApcbHmacChecksumLibV3_ApcbHmacChecksumLibV3_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_CcxMicrocodePatchLib_CcxMicrocodePatchLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_CcxMicrocodePatchLib_CcxMicrocodePatchLib_INF"
    File = "Library/CcxMicrocodePatchLib/CcxMicrocodePatchLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_CcxMicrocodePatchLib_CcxMicrocodePatchLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxMicrocodePatchLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_CcxMicrocodePatchLib_CcxMicrocodePatchLib_INF"
    Token = "_AgesaModulePkg_Library_CcxMicrocodePatchLib_CcxMicrocodePatchLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_CcxApicZen5Lib_CcxApicZen5Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_CcxApicZen5Lib_CcxApicZen5Lib_INF"
    File = "Library/CcxApicZen5Lib/CcxApicZen5Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_CcxApicZen5Lib_CcxApicZen5Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxApicZen5Lib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_CcxApicZen5Lib_CcxApicZen5Lib_INF"
    Token = "_AgesaModulePkg_Library_CcxApicZen5Lib_CcxApicZen5Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_PeiFabricResourceManagerServicesLib_PeiFabricResourceManagerServicesLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_PeiFabricResourceManagerServicesLib_PeiFabricResourceManagerServicesLib_INF"
    File = "Library/PeiFabricResourceManagerServicesLib/PeiFabricResourceManagerServicesLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "_AgesaModulePkg_Library_PeiFabricResourceManagerServicesLib_PeiFabricResourceManagerServicesLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "PeiFabricResourceManagerServicesLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_PeiFabricResourceManagerServicesLib_PeiFabricResourceManagerServicesLib_INF"
    Token = "_AgesaModulePkg_Library_PeiFabricResourceManagerServicesLib_PeiFabricResourceManagerServicesLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_NbioUtilLib_NbioUtilLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_NbioUtilLib_NbioUtilLib_INF"
    File = "Library/NbioUtilLib/NbioUtilLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_NbioUtilLib_NbioUtilLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "NbioUtilLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_NbioUtilLib_NbioUtilLib_INF"
    Token = "_AgesaModulePkg_Library_NbioUtilLib_NbioUtilLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AgesaModulePkg_Library_PmMpDmaArsLib_Brh_PmMpDmaBrhArsLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_PmMpDmaArsLib_Brh_PmMpDmaBrhArsLib_INF"
    File = "Library/PmMpDmaArsLib/Brh/PmMpDmaBrhArsLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_PmMpDmaArsLib_Brh_PmMpDmaBrhArsLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "PmMpDmaArsLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_PmMpDmaArsLib_Brh_PmMpDmaBrhArsLib_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_PmMpDmaArsLib_Brh_PmMpDmaBrhArsLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_GnbMemAccLib_GnbMemAccLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_GnbMemAccLib_GnbMemAccLib_INF"
    File = "Library/GnbMemAccLib/GnbMemAccLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_GnbMemAccLib_GnbMemAccLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "GnbMemAccLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_GnbMemAccLib_GnbMemAccLib_INF"
    Token = "_AgesaModulePkg_Library_GnbMemAccLib_GnbMemAccLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspRegMuxLibV2Dxe_AmdPspRegMuxLibV2_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspRegMuxLibV2Dxe_AmdPspRegMuxLibV2_INF"
    File = "Library/AmdPspRegMuxLibV2Dxe/AmdPspRegMuxLibV2.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspRegMuxLibV2Dxe_AmdPspRegMuxLibV2_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspRegMuxLibV2"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspRegMuxLibV2Dxe_AmdPspRegMuxLibV2_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspRegMuxLibV2Dxe_AmdPspRegMuxLibV2_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AgesaModulePkg_Library_ApcbLibV3_ApcbLibV3_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_ApcbLibV3_ApcbLibV3_INF"
    File = "Library/ApcbLibV3/ApcbLibV3.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_ApcbLibV3_ApcbLibV3_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "ApcbLibV3"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_ApcbLibV3_ApcbLibV3_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_ApcbLibV3_ApcbLibV3_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_AmdPspRegMuxLibV2Dxe_AmdPspRegMuxLibV2_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspRegMuxLibV2Dxe_AmdPspRegMuxLibV2_INF"
    File = "Library/AmdPspRegMuxLibV2Dxe/AmdPspRegMuxLibV2.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdPspRegMuxLibV2Dxe_AmdPspRegMuxLibV2_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspRegMuxLibV2"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspRegMuxLibV2Dxe_AmdPspRegMuxLibV2_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdPspRegMuxLibV2Dxe_AmdPspRegMuxLibV2_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AgesaModulePkg_Library_Ras_RasIdsSmmLib_Brh_RasIdsSmmLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_Ras_RasIdsSmmLib_Brh_RasIdsSmmLib_INF"
    File = "Library/Ras/RasIdsSmmLib/Brh/RasIdsSmmLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_Ras_RasIdsSmmLib_Brh_RasIdsSmmLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "RasIdsLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_Ras_RasIdsSmmLib_Brh_RasIdsSmmLib_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_Ras_RasIdsSmmLib_Brh_RasIdsSmmLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AgesaModulePkg_Library_ApobCommonServiceLibDxe_ApobCommonServiceLibDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_ApobCommonServiceLibDxe_ApobCommonServiceLibDxe_INF"
    File = "Library/ApobCommonServiceLibDxe/ApobCommonServiceLibDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_ApobCommonServiceLibDxe_ApobCommonServiceLibDxe_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "ApobCommonServiceLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_ApobCommonServiceLibDxe_ApobCommonServiceLibDxe_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_ApobCommonServiceLibDxe_ApobCommonServiceLibDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdIdsHookLibDxe_AmdIdsHookLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdIdsHookLibDxe_AmdIdsHookLib_INF"
    File = "Library/AmdIdsHookLibDxe/AmdIdsHookLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdIdsHookLibDxe_AmdIdsHookLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdIdsHookLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdIdsHookLibDxe_AmdIdsHookLib_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdIdsHookLibDxe_AmdIdsHookLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEI_CORE_AgesaModulePkg_Library_AmdEmulationAutoDetectPeiLib_AmdEmulationAutoDetectPeiLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdEmulationAutoDetectPeiLib_AmdEmulationAutoDetectPeiLib_INF"
    File = "Library/AmdEmulationAutoDetectPeiLib/AmdEmulationAutoDetectPeiLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEI_CORE_AgesaModulePkg_Library_AmdEmulationAutoDetectPeiLib_AmdEmulationAutoDetectPeiLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdEmulationAutoDetectLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdEmulationAutoDetectPeiLib_AmdEmulationAutoDetectPeiLib_INF"
    ModuleTypes = "PEI_CORE"
    Token = "PEI_CORE_AgesaModulePkg_Library_AmdEmulationAutoDetectPeiLib_AmdEmulationAutoDetectPeiLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_CcxRolesX86Lib_CcxRolesX86Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_CcxRolesX86Lib_CcxRolesX86Lib_INF"
    File = "Library/CcxRolesX86Lib/CcxRolesX86Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_CcxRolesX86Lib_CcxRolesX86Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxRolesLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_CcxRolesX86Lib_CcxRolesX86Lib_INF"
    Token = "_AgesaModulePkg_Library_CcxRolesX86Lib_CcxRolesX86Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_DxeCcxBaseX86ServicesLib_DxeCcxBaseX86ServicesLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_DxeCcxBaseX86ServicesLib_DxeCcxBaseX86ServicesLib_INF"
    File = "Library/DxeCcxBaseX86ServicesLib/DxeCcxBaseX86ServicesLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "_AgesaModulePkg_Library_DxeCcxBaseX86ServicesLib_DxeCcxBaseX86ServicesLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "DxeCcxBaseX86ServicesLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_DxeCcxBaseX86ServicesLib_DxeCcxBaseX86ServicesLib_INF"
    Token = "_AgesaModulePkg_Library_DxeCcxBaseX86ServicesLib_DxeCcxBaseX86ServicesLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_AmdPspBarInitLibV2_AmdPspBarInitLibV2_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspBarInitLibV2_AmdPspBarInitLibV2_INF"
    File = "Library/AmdPspBarInitLibV2/AmdPspBarInitLibV2.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_AmdPspBarInitLibV2_AmdPspBarInitLibV2_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspBarInitLibV2"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspBarInitLibV2_AmdPspBarInitLibV2_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_AmdPspBarInitLibV2_AmdPspBarInitLibV2_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdPspBaseLibV2_AmdPspBaseLibV2_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspBaseLibV2_AmdPspBaseLibV2_INF"
    File = "Library/AmdPspBaseLibV2/AmdPspBaseLibV2.inf"
    Package = "AgesaModulePkg"
    Arch = "IA32 X64"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdPspBaseLibV2_AmdPspBaseLibV2_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspBaseLibV2"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspBaseLibV2_AmdPspBaseLibV2_INF"
    Token = "_AgesaModulePkg_Library_AmdPspBaseLibV2_AmdPspBaseLibV2_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_DxeFabricResourceSizeForEachRbLib_DxeFabricResourceSizeForEachRbLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_DxeFabricResourceSizeForEachRbLib_DxeFabricResourceSizeForEachRbLib_INF"
    File = "Library/DxeFabricResourceSizeForEachRbLib/DxeFabricResourceSizeForEachRbLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_DxeFabricResourceSizeForEachRbLib_DxeFabricResourceSizeForEachRbLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FabricResourceSizeForEachRbLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_DxeFabricResourceSizeForEachRbLib_DxeFabricResourceSizeForEachRbLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_DxeFabricResourceSizeForEachRbLib_DxeFabricResourceSizeForEachRbLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdCfgPcdBufLibNull_AmdCfgPcdBufLibNull_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdCfgPcdBufLibNull_AmdCfgPcdBufLibNull_INF"
    File = "Library/AmdCfgPcdBufLibNull/AmdCfgPcdBufLibNull.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdCfgPcdBufLibNull_AmdCfgPcdBufLibNull_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdCfgPcdBufLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdCfgPcdBufLibNull_AmdCfgPcdBufLibNull_INF"
    Token = "_AgesaModulePkg_Library_AmdCfgPcdBufLibNull_AmdCfgPcdBufLibNull_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Nbio_Library_IommuDmarLib_DXE_AmdIOMMUDmarLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Nbio_Library_IommuDmarLib_DXE_AmdIOMMUDmarLib_INF"
    File = "Nbio/Library/IommuDmarLib/DXE/AmdIOMMUDmarLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Nbio_Library_IommuDmarLib_DXE_AmdIOMMUDmarLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdIOMMUDmarLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Nbio_Library_IommuDmarLib_DXE_AmdIOMMUDmarLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Nbio_Library_IommuDmarLib_DXE_AmdIOMMUDmarLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_PresiliconControlBrhLib_PresiliconControlBrhLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_PresiliconControlBrhLib_PresiliconControlBrhLib_INF"
    File = "Library/PresiliconControlBrhLib/PresiliconControlBrhLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_PresiliconControlBrhLib_PresiliconControlBrhLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "PresiliconControlLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_PresiliconControlBrhLib_PresiliconControlBrhLib_INF"
    Token = "_AgesaModulePkg_Library_PresiliconControlBrhLib_PresiliconControlBrhLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_AmdCfgPcdBufLibPei_AmdCfgPcdBufLibPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdCfgPcdBufLibPei_AmdCfgPcdBufLibPei_INF"
    File = "Library/AmdCfgPcdBufLibPei/AmdCfgPcdBufLibPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_AmdCfgPcdBufLibPei_AmdCfgPcdBufLibPei_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdCfgPcdBufLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdCfgPcdBufLibPei_AmdCfgPcdBufLibPei_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_AmdCfgPcdBufLibPei_AmdCfgPcdBufLibPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_NbioServicesLib_Dxe_NbioServicesLibDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_NbioServicesLib_Dxe_NbioServicesLibDxe_INF"
    File = "Library/NbioServicesLib/Dxe/NbioServicesLibDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "_AgesaModulePkg_Library_NbioServicesLib_Dxe_NbioServicesLibDxe_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "NbioServicesLibDxe"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_NbioServicesLib_Dxe_NbioServicesLibDxe_INF"
    Token = "_AgesaModulePkg_Library_NbioServicesLib_Dxe_NbioServicesLibDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_CcxZen5BrhDxeLib_CcxZen5BrhDxeLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_CcxZen5BrhDxeLib_CcxZen5BrhDxeLib_INF"
    File = "Library/CcxZen5BrhDxeLib/CcxZen5BrhDxeLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "_AgesaModulePkg_Library_CcxZen5BrhDxeLib_CcxZen5BrhDxeLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxZen5DxeLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_CcxZen5BrhDxeLib_CcxZen5BrhDxeLib_INF"
    Token = "_AgesaModulePkg_Library_CcxZen5BrhDxeLib_CcxZen5BrhDxeLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Fch_Kunlun_FchKunlunCore_FchKunlunLibDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Fch_Kunlun_FchKunlunCore_FchKunlunLibDxe_INF"
    File = "Fch/Kunlun/FchKunlunCore/FchKunlunLibDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Fch_Kunlun_FchKunlunCore_FchKunlunLibDxe_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FchKunlunDxeLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Fch_Kunlun_FchKunlunCore_FchKunlunLibDxe_INF"
    Token = "_AgesaModulePkg_Fch_Kunlun_FchKunlunCore_FchKunlunLibDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_DxeCcxCppcLib_DxeCcxCppcLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_DxeCcxCppcLib_DxeCcxCppcLib_INF"
    File = "Library/DxeCcxCppcLib/DxeCcxCppcLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_DxeCcxCppcLib_DxeCcxCppcLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxCppcLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_DxeCcxCppcLib_DxeCcxCppcLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_DxeCcxCppcLib_DxeCcxCppcLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_DxeCcxCcdReorderZen5Lib_DxeCcxCcdReorderZen5Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_DxeCcxCcdReorderZen5Lib_DxeCcxCcdReorderZen5Lib_INF"
    File = "Library/DxeCcxCcdReorderZen5Lib/DxeCcxCcdReorderZen5Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_DxeCcxCcdReorderZen5Lib_DxeCcxCcdReorderZen5Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxCcdReorderLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_DxeCcxCcdReorderZen5Lib_DxeCcxCcdReorderZen5Lib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_DxeCcxCcdReorderZen5Lib_DxeCcxCcdReorderZen5Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AgesaConfigLib_AgesaConfigLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AgesaConfigLib_AgesaConfigLib_INF"
    File = "Library/AgesaConfigLib/AgesaConfigLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AgesaConfigLib_AgesaConfigLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AgesaConfigLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AgesaConfigLib_AgesaConfigLib_INF"
    Token = "_AgesaModulePkg_Library_AgesaConfigLib_AgesaConfigLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_IdsPeiLib_IdsPeiLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_IdsPeiLib_IdsPeiLib_INF"
    File = "Library/IdsPeiLib/IdsPeiLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_IdsPeiLib_IdsPeiLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "IdsLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_IdsPeiLib_IdsPeiLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_IdsPeiLib_IdsPeiLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspFlashUpdateLib_AmdPspFlashUpdateLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspFlashUpdateLib_AmdPspFlashUpdateLib_INF"
    File = "Library/AmdPspFlashUpdateLib/AmdPspFlashUpdateLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspFlashUpdateLib_AmdPspFlashUpdateLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspFlashUpdateLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspFlashUpdateLib_AmdPspFlashUpdateLib_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspFlashUpdateLib_AmdPspFlashUpdateLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_Mem_BaseLib_AmdMemBaseLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_Mem_BaseLib_AmdMemBaseLib_INF"
    File = "Library/Mem/BaseLib/AmdMemBaseLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_Mem_BaseLib_AmdMemBaseLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdMemBaseLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_Mem_BaseLib_AmdMemBaseLib_INF"
    Token = "_AgesaModulePkg_Library_Mem_BaseLib_AmdMemBaseLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_DxeCoreTopologyV3Lib_DxeCoreTopologyV3Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_DxeCoreTopologyV3Lib_DxeCoreTopologyV3Lib_INF"
    File = "Library/DxeCoreTopologyV3Lib/DxeCoreTopologyV3Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_DxeCoreTopologyV3Lib_DxeCoreTopologyV3Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CoreTopologyV3Lib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_DxeCoreTopologyV3Lib_DxeCoreTopologyV3Lib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_DxeCoreTopologyV3Lib_DxeCoreTopologyV3Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_IdsMiscLib_IdsMiscLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_IdsMiscLib_IdsMiscLib_INF"
    File = "Library/IdsMiscLib/IdsMiscLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_IdsMiscLib_IdsMiscLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "IdsMiscLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_IdsMiscLib_IdsMiscLib_INF"
    Token = "_AgesaModulePkg_Library_IdsMiscLib_IdsMiscLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_FabricIdsHookBrhLib_Pei_FabricIdsHookBrhLibPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FabricIdsHookBrhLib_Pei_FabricIdsHookBrhLibPei_INF"
    File = "Library/FabricIdsHookBrhLib/Pei/FabricIdsHookBrhLibPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_FabricIdsHookBrhLib_Pei_FabricIdsHookBrhLibPei_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FabricIdsHookBrhLibPei"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FabricIdsHookBrhLib_Pei_FabricIdsHookBrhLibPei_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_FabricIdsHookBrhLib_Pei_FabricIdsHookBrhLibPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_RUNTIME_DRIVER_AgesaModulePkg_Library_AmdPspRegMuxLibV2DxeRt_AmdPspRegMuxLibV2_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspRegMuxLibV2DxeRt_AmdPspRegMuxLibV2_INF"
    File = "Library/AmdPspRegMuxLibV2DxeRt/AmdPspRegMuxLibV2.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_RUNTIME_DRIVER"
    Token = "DXE_RUNTIME_DRIVER_AgesaModulePkg_Library_AmdPspRegMuxLibV2DxeRt_AmdPspRegMuxLibV2_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspRegMuxLibV2"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspRegMuxLibV2DxeRt_AmdPspRegMuxLibV2_INF"
    ModuleTypes = "DXE_RUNTIME_DRIVER"
    Token = "DXE_RUNTIME_DRIVER_AgesaModulePkg_Library_AmdPspRegMuxLibV2DxeRt_AmdPspRegMuxLibV2_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_INF"
    File = "Library/IdsNonUefiLib/IdsNonUefiLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "IdsLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_FchEspiCmdLib_FchEspiCmdLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FchEspiCmdLib_FchEspiCmdLib_INF"
    File = "Library/FchEspiCmdLib/FchEspiCmdLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_FchEspiCmdLib_FchEspiCmdLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FchEspiCmdLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FchEspiCmdLib_FchEspiCmdLib_INF"
    Token = "_AgesaModulePkg_Library_FchEspiCmdLib_FchEspiCmdLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_PcieMiscCommLib_PcieMiscCommLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_PcieMiscCommLib_PcieMiscCommLib_INF"
    File = "Library/PcieMiscCommLib/PcieMiscCommLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_PcieMiscCommLib_PcieMiscCommLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "PcieMiscCommLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_PcieMiscCommLib_PcieMiscCommLib_INF"
    Token = "_AgesaModulePkg_Library_PcieMiscCommLib_PcieMiscCommLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_GnbHeapDxeLib_GnbHeapDxeLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_GnbHeapDxeLib_GnbHeapDxeLib_INF"
    File = "Library/GnbHeapDxeLib/GnbHeapDxeLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_GnbHeapDxeLib_GnbHeapDxeLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "GnbHeapLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_GnbHeapDxeLib_GnbHeapDxeLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_GnbHeapDxeLib_GnbHeapDxeLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_FchSocLib_Breithorn_FchBreithornLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FchSocLib_Breithorn_FchBreithornLib_INF"
    File = "Library/FchSocLib/Breithorn/FchBreithornLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_FchSocLib_Breithorn_FchBreithornLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FchSocLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FchSocLib_Breithorn_FchBreithornLib_INF"
    Token = "_AgesaModulePkg_Library_FchSocLib_Breithorn_FchBreithornLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Nbio_Library_CommonDxe_NbioCommonDxeLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Nbio_Library_CommonDxe_NbioCommonDxeLib_INF"
    File = "Nbio/Library/CommonDxe/NbioCommonDxeLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Nbio_Library_CommonDxe_NbioCommonDxeLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "NbioCommonDxeLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Nbio_Library_CommonDxe_NbioCommonDxeLib_INF"
    Token = "_AgesaModulePkg_Nbio_Library_CommonDxe_NbioCommonDxeLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_AcpiTableHelperLib_AcpiTableHelperLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AcpiTableHelperLib_AcpiTableHelperLib_INF"
    File = "Library/AcpiTableHelperLib/AcpiTableHelperLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AcpiTableHelperLib_AcpiTableHelperLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AcpiTableHelperLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AcpiTableHelperLib_AcpiTableHelperLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AcpiTableHelperLib_AcpiTableHelperLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_ApcbTokenWhiteListNullLib_ApcbTokenWhiteListNullLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_ApcbTokenWhiteListNullLib_ApcbTokenWhiteListNullLib_INF"
    File = "Library/ApcbTokenWhiteListNullLib/ApcbTokenWhiteListNullLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_ApcbTokenWhiteListNullLib_ApcbTokenWhiteListNullLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "ApcbTokenWhiteListLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_ApcbTokenWhiteListNullLib_ApcbTokenWhiteListNullLib_INF"
    ModuleTypes = "DXE_DRIVER DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_ApcbTokenWhiteListNullLib_ApcbTokenWhiteListNullLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_ApcbTokenWhiteListLib_ApcbTokenWhiteListLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_ApcbTokenWhiteListLib_ApcbTokenWhiteListLib_INF"
    File = "Library/ApcbTokenWhiteListBrhLib/ApcbTokenWhiteListBrhLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_ApcbTokenWhiteListLib_ApcbTokenWhiteListLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "ApcbTokenWhiteListLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_ApcbTokenWhiteListLib_ApcbTokenWhiteListLib_INF"
    ModuleTypes = "DXE_DRIVER DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_ApcbTokenWhiteListLib_ApcbTokenWhiteListLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_CcxHaltLib_CcxHaltLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_CcxHaltLib_CcxHaltLib_INF"
    File = "Library/CcxHaltLib/CcxHaltLib.inf"
    Package = "AgesaModulePkg"
    Arch = "IA32 X64"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_CcxHaltLib_CcxHaltLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxHaltLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_CcxHaltLib_CcxHaltLib_INF"
    Token = "_AgesaModulePkg_Library_CcxHaltLib_CcxHaltLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_SmnAccessLib_SmnAccessLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_SmnAccessLib_SmnAccessLib_INF"
    File = "Library/SmnAccessLib/SmnAccessLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_SmnAccessLib_SmnAccessLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "SmnAccessLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_SmnAccessLib_SmnAccessLib_INF"
    Token = "_AgesaModulePkg_Library_SmnAccessLib_SmnAccessLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_FchIdsHookBrhLib_Dxe_FchIdsHookBrhLibDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FchIdsHookBrhLib_Dxe_FchIdsHookBrhLibDxe_INF"
    File = "Library/FchIdsHookBrhLib/Dxe/FchIdsHookBrhLibDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_FchIdsHookBrhLib_Dxe_FchIdsHookBrhLibDxe_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FchIdsHookLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FchIdsHookBrhLib_Dxe_FchIdsHookBrhLibDxe_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_FchIdsHookBrhLib_Dxe_FchIdsHookBrhLibDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_Mem_BaseLib_AmdMemBaseLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_Mem_BaseLib_AmdMemBaseLib_INF"
    File = "Library/Mem/BaseLib/AmdMemBaseLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_Mem_BaseLib_AmdMemBaseLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdMemBaseLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_Mem_BaseLib_AmdMemBaseLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_Mem_BaseLib_AmdMemBaseLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_RUNTIME_DRIVER_AgesaModulePkg_Library_FchDxeRuntimeResetSystemLib_KunLun_FchDxeRuntimeResetSystemLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FchDxeRuntimeResetSystemLib_KunLun_FchDxeRuntimeResetSystemLib_INF"
    File = "Library/FchDxeRuntimeResetSystemLib/KunLun/FchDxeRuntimeResetSystemLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_RUNTIME_DRIVER"
    Token = "DXE_RUNTIME_DRIVER_AgesaModulePkg_Library_FchDxeRuntimeResetSystemLib_KunLun_FchDxeRuntimeResetSystemLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "ResetSystemLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FchDxeRuntimeResetSystemLib_KunLun_FchDxeRuntimeResetSystemLib_INF"
    ModuleTypes = "DXE_RUNTIME_DRIVER"
    Token = "DXE_RUNTIME_DRIVER_AgesaModulePkg_Library_FchDxeRuntimeResetSystemLib_KunLun_FchDxeRuntimeResetSystemLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_FchInitHookLib_FchInitHookLibPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FchInitHookLib_FchInitHookLibPei_INF"
    File = "Library/FchInitHookLib/FchInitHookLibPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_FchInitHookLib_FchInitHookLibPei_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FchInitHookLibPei"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FchInitHookLib_FchInitHookLibPei_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_FchInitHookLib_FchInitHookLibPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdHeapLibNull_AmdHeapLibNull_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdHeapLibNull_AmdHeapLibNull_INF"
    File = "Library/AmdHeapLibNull/AmdHeapLibNull.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdHeapLibNull_AmdHeapLibNull_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdHeapLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdHeapLibNull_AmdHeapLibNull_INF"
    Token = "_AgesaModulePkg_Library_AmdHeapLibNull_AmdHeapLibNull_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_ApcbLibV3Pei_ApcbLibV3Pei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_ApcbLibV3Pei_ApcbLibV3Pei_INF"
    File = "Library/ApcbLibV3Pei/ApcbLibV3Pei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_ApcbLibV3Pei_ApcbLibV3Pei_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "ApcbLibV3Pei"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_ApcbLibV3Pei_ApcbLibV3Pei_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_ApcbLibV3Pei_ApcbLibV3Pei_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_ApcbCoreLib_ApcbCoreLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_ApcbCoreLib_ApcbCoreLib_INF"
    File = "Library/ApcbCoreLib/ApcbCoreLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_ApcbCoreLib_ApcbCoreLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "ApcbCoreLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_ApcbCoreLib_ApcbCoreLib_INF"
    Token = "_AgesaModulePkg_Library_ApcbCoreLib_ApcbCoreLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Nbio_BRH_Library_NbioIdsHookBrhLib_Pei_NbioIdsHookBrhLibPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Nbio_BRH_Library_NbioIdsHookBrhLib_Pei_NbioIdsHookBrhLibPei_INF"
    File = "Nbio/BRH/Library/NbioIdsHookBrhLib/Pei/NbioIdsHookBrhLibPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Nbio_BRH_Library_NbioIdsHookBrhLib_Pei_NbioIdsHookBrhLibPei_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "NbioIdsHookBrhLibPei"
    Instance = "AgesaModulePkg.AgesaModulePkg_Nbio_BRH_Library_NbioIdsHookBrhLib_Pei_NbioIdsHookBrhLibPei_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Nbio_BRH_Library_NbioIdsHookBrhLib_Pei_NbioIdsHookBrhLibPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_Ras_RasAcpi63Lib_RasAcpi63Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_Ras_RasAcpi63Lib_RasAcpi63Lib_INF"
    File = "Library/Ras/RasAcpi63Lib/RasAcpi63Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_Ras_RasAcpi63Lib_RasAcpi63Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "RasAcpiLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_Ras_RasAcpi63Lib_RasAcpi63Lib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_Ras_RasAcpi63Lib_RasAcpi63Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_DxeFabricResourceSizeForEachRbLib_DxeFabricResourceSizeForEachRbLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_DxeFabricResourceSizeForEachRbLib_DxeFabricResourceSizeForEachRbLib_INF"
    File = "Library/DxeFabricResourceSizeForEachRbLib/DxeFabricResourceSizeForEachRbLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_DxeFabricResourceSizeForEachRbLib_DxeFabricResourceSizeForEachRbLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FabricResourceSizeForEachRbLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_DxeFabricResourceSizeForEachRbLib_DxeFabricResourceSizeForEachRbLib_INF"
    Token = "_AgesaModulePkg_Library_DxeFabricResourceSizeForEachRbLib_DxeFabricResourceSizeForEachRbLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AgesaModulePkg_Library_Ras_Brh_RasBrhSmmLib_RasBrhSmmLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_Ras_Brh_RasBrhSmmLib_RasBrhSmmLib_INF"
    File = "Library/Ras/Brh/RasBrhSmmLib/RasBrhSmmLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_Ras_Brh_RasBrhSmmLib_RasBrhSmmLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "RasSmmLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_Ras_Brh_RasBrhSmmLib_RasBrhSmmLib_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_Ras_Brh_RasBrhSmmLib_RasBrhSmmLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_ApobCommonServiceLibPei_ApobCommonServiceLibPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_ApobCommonServiceLibPei_ApobCommonServiceLibPei_INF"
    File = "Library/ApobCommonServiceLibPei/ApobCommonServiceLibPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_ApobCommonServiceLibPei_ApobCommonServiceLibPei_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "ApobCommonServiceLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_ApobCommonServiceLibPei_ApobCommonServiceLibPei_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_ApobCommonServiceLibPei_ApobCommonServiceLibPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_FchBaseResetSystemLib_FchBaseResetSystemLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FchBaseResetSystemLib_FchBaseResetSystemLib_INF"
    File = "Library/FchBaseResetSystemLib/FchBaseResetSystemLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_FchBaseResetSystemLib_FchBaseResetSystemLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "BaseResetSystemLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FchBaseResetSystemLib_FchBaseResetSystemLib_INF"
    Token = "_AgesaModulePkg_Library_FchBaseResetSystemLib_FchBaseResetSystemLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_NbioSmuBrhLib_NbioSmuBrhLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_NbioSmuBrhLib_NbioSmuBrhLib_INF"
    File = "Library/NbioSmuBrhLib/NbioSmuBrhLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_NbioSmuBrhLib_NbioSmuBrhLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "NbioSmuBrhLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_NbioSmuBrhLib_NbioSmuBrhLib_INF"
    Token = "_AgesaModulePkg_Library_NbioSmuBrhLib_NbioSmuBrhLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Fch_Kunlun_FchKunlunCore_FchKunlunLibPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Fch_Kunlun_FchKunlunCore_FchKunlunLibPei_INF"
    File = "Fch/Kunlun/FchKunlunCore/FchKunlunLibPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Fch_Kunlun_FchKunlunCore_FchKunlunLibPei_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FchKunlunPeiLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Fch_Kunlun_FchKunlunCore_FchKunlunLibPei_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Fch_Kunlun_FchKunlunCore_FchKunlunLibPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_AmdHeapDxeLib_AmdHeapDxeLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdHeapDxeLib_AmdHeapDxeLib_INF"
    File = "Library/AmdHeapDxeLib/AmdHeapDxeLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdHeapDxeLib_AmdHeapDxeLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdHeapLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdHeapDxeLib_AmdHeapDxeLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdHeapDxeLib_AmdHeapDxeLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdTableLibV2_Dxe_AmdTableHookDxeLibV2_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdTableLibV2_Dxe_AmdTableHookDxeLibV2_INF"
    File = "Library/AmdTableLibV2/Dxe/AmdTableHookDxeLibV2.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdTableLibV2_Dxe_AmdTableHookDxeLibV2_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdTableHookLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdTableLibV2_Dxe_AmdTableHookDxeLibV2_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdTableLibV2_Dxe_AmdTableHookDxeLibV2_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_SocCoreInfo2AccessLib_SocCoreInfo2AccessLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_SocCoreInfo2AccessLib_SocCoreInfo2AccessLib_INF"
    File = "Library/SocCoreInfo2AccessLib/SocCoreInfo2AccessLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_SocCoreInfo2AccessLib_SocCoreInfo2AccessLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "SocCoreInfo2AccessLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_SocCoreInfo2AccessLib_SocCoreInfo2AccessLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_SocCoreInfo2AccessLib_SocCoreInfo2AccessLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Nbio_Library_GetPcieResourcesLib_GetPcieResourcesLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Nbio_Library_GetPcieResourcesLib_GetPcieResourcesLib_INF"
    File = "Nbio/Library/GetPcieResourcesLib/GetPcieResourcesLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Nbio_Library_GetPcieResourcesLib_GetPcieResourcesLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "GetPcieResourcesLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Nbio_Library_GetPcieResourcesLib_GetPcieResourcesLib_INF"
    Token = "_AgesaModulePkg_Nbio_Library_GetPcieResourcesLib_GetPcieResourcesLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEI_CORE_AgesaModulePkg_Library_AmdCfgPcdBufLibPei_AmdCfgPcdBufLibPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdCfgPcdBufLibPei_AmdCfgPcdBufLibPei_INF"
    File = "Library/AmdCfgPcdBufLibPei/AmdCfgPcdBufLibPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEI_CORE_AgesaModulePkg_Library_AmdCfgPcdBufLibPei_AmdCfgPcdBufLibPei_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdCfgPcdBufLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdCfgPcdBufLibPei_AmdCfgPcdBufLibPei_INF"
    ModuleTypes = "PEI_CORE"
    Token = "PEI_CORE_AgesaModulePkg_Library_AmdCfgPcdBufLibPei_AmdCfgPcdBufLibPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_FabricIdsHookBrhLib_Dxe_FabricIdsHookBrhLibDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FabricIdsHookBrhLib_Dxe_FabricIdsHookBrhLibDxe_INF"
    File = "Library/FabricIdsHookBrhLib/Dxe/FabricIdsHookBrhLibDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_FabricIdsHookBrhLib_Dxe_FabricIdsHookBrhLibDxe_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FabricIdsHookBrhLibDxe"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FabricIdsHookBrhLib_Dxe_FabricIdsHookBrhLibDxe_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_FabricIdsHookBrhLib_Dxe_FabricIdsHookBrhLibDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_PeiSocLogicalIdServicesLib_PeiSocLogicalIdServicesLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_PeiSocLogicalIdServicesLib_PeiSocLogicalIdServicesLib_INF"
    File = "Library/PeiSocLogicalIdServicesLib/PeiSocLogicalIdServicesLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_PeiSocLogicalIdServicesLib_PeiSocLogicalIdServicesLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "PeiSocLogicalIdServicesLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_PeiSocLogicalIdServicesLib_PeiSocLogicalIdServicesLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_PeiSocLogicalIdServicesLib_PeiSocLogicalIdServicesLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Nbio_BRH_Library_NbioIdsHookBrhLib_Dxe_NbioIdsHookBrhLibDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Nbio_BRH_Library_NbioIdsHookBrhLib_Dxe_NbioIdsHookBrhLibDxe_INF"
    File = "Nbio/BRH/Library/NbioIdsHookBrhLib/Dxe/NbioIdsHookBrhLibDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Nbio_BRH_Library_NbioIdsHookBrhLib_Dxe_NbioIdsHookBrhLibDxe_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "NbioIdsHookBrhLibDxe"
    Instance = "AgesaModulePkg.AgesaModulePkg_Nbio_BRH_Library_NbioIdsHookBrhLib_Dxe_NbioIdsHookBrhLibDxe_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Nbio_BRH_Library_NbioIdsHookBrhLib_Dxe_NbioIdsHookBrhLibDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_GnbCommonLib_GnbCommonLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_GnbCommonLib_GnbCommonLib_INF"
    File = "Library/GnbCommonLib/GnbCommonLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_GnbCommonLib_GnbCommonLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "GnbCommonLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_GnbCommonLib_GnbCommonLib_INF"
    Token = "_AgesaModulePkg_Library_GnbCommonLib_GnbCommonLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_ApobCommonServiceLibDxe_ApobCommonServiceLibDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_ApobCommonServiceLibDxe_ApobCommonServiceLibDxe_INF"
    File = "Library/ApobCommonServiceLibDxe/ApobCommonServiceLibDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_ApobCommonServiceLibDxe_ApobCommonServiceLibDxe_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "ApobCommonServiceLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_ApobCommonServiceLibDxe_ApobCommonServiceLibDxe_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_ApobCommonServiceLibDxe_ApobCommonServiceLibDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AgesaModulePkg_Library_CcxZen5BrhIdsHookLib_Smm_CcxZen5BrhIdsHookLibSmm_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_CcxZen5BrhIdsHookLib_Smm_CcxZen5BrhIdsHookLibSmm_INF"
    File = "Library/CcxZen5BrhIdsHookLib/Smm/CcxZen5BrhIdsHookLibSmm.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_CcxZen5BrhIdsHookLib_Smm_CcxZen5BrhIdsHookLibSmm_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxZen5IdsHookLibSmm"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_CcxZen5BrhIdsHookLib_Smm_CcxZen5BrhIdsHookLibSmm_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_CcxZen5BrhIdsHookLib_Smm_CcxZen5BrhIdsHookLibSmm_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_MemRestoreLib_MemRestoreLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_MemRestoreLib_MemRestoreLib_INF"
    File = "Library/MemRestoreLib/MemRestoreLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_MemRestoreLib_MemRestoreLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "MemRestoreLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_MemRestoreLib_MemRestoreLib_INF"
    Token = "_AgesaModulePkg_Library_MemRestoreLib_MemRestoreLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_ApobApcbLib_ApobApcbLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_ApobApcbLib_ApobApcbLib_INF"
    File = "Library/ApobApcbLib/ApobApcbLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_ApobApcbLib_ApobApcbLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "ApobApcbLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_ApobApcbLib_ApobApcbLib_INF"
    Token = "_AgesaModulePkg_Library_ApobApcbLib_ApobApcbLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_ApcbVariableLibV3_ApcbVariableLibV3_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_ApcbVariableLibV3_ApcbVariableLibV3_INF"
    File = "Library/ApcbVariableLibV3/ApcbVariableLibV3.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_ApcbVariableLibV3_ApcbVariableLibV3_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "ApcbVariableLibV3"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_ApcbVariableLibV3_ApcbVariableLibV3_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_ApcbVariableLibV3_ApcbVariableLibV3_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_PeiFabricResourceSizeForEachRbLib_PeiFabricResourceSizeForEachRbLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_PeiFabricResourceSizeForEachRbLib_PeiFabricResourceSizeForEachRbLib_INF"
    File = "Library/PeiFabricResourceSizeForEachRbLib/PeiFabricResourceSizeForEachRbLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_PeiFabricResourceSizeForEachRbLib_PeiFabricResourceSizeForEachRbLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FabricResourceSizeForEachRbLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_PeiFabricResourceSizeForEachRbLib_PeiFabricResourceSizeForEachRbLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_PeiFabricResourceSizeForEachRbLib_PeiFabricResourceSizeForEachRbLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_NbioHandleLib_NbioHandleLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_NbioHandleLib_NbioHandleLib_INF"
    File = "Library/NbioHandleLib/NbioHandleLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_NbioHandleLib_NbioHandleLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "NbioHandleLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_NbioHandleLib_NbioHandleLib_INF"
    Token = "_AgesaModulePkg_Library_NbioHandleLib_NbioHandleLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdS3SaveLib_S3Save_AmdS3SaveLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdS3SaveLib_S3Save_AmdS3SaveLib_INF"
    File = "Library/AmdS3SaveLib/S3Save/AmdS3SaveLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdS3SaveLib_S3Save_AmdS3SaveLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdS3SaveLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdS3SaveLib_S3Save_AmdS3SaveLib_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdS3SaveLib_S3Save_AmdS3SaveLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Nbio_Library_IvrsLibV3_IvrsLibV3_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Nbio_Library_IvrsLibV3_IvrsLibV3_INF"
    File = "Nbio/Library/IvrsLibV3/IvrsLibV3.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Nbio_Library_IvrsLibV3_IvrsLibV3_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "NbioIommuIvrsLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Nbio_Library_IvrsLibV3_IvrsLibV3_INF"
    Token = "_AgesaModulePkg_Nbio_Library_IvrsLibV3_IvrsLibV3_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_GnbPciLib_GnbPciLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_GnbPciLib_GnbPciLib_INF"
    File = "Library/GnbPciLib/GnbPciLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_GnbPciLib_GnbPciLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "GnbPciLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_GnbPciLib_GnbPciLib_INF"
    Token = "_AgesaModulePkg_Library_GnbPciLib_GnbPciLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_FchIdsHookBrhLib_Pei_FchIdsHookBrhLibPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FchIdsHookBrhLib_Pei_FchIdsHookBrhLibPei_INF"
    File = "Library/FchIdsHookBrhLib/Pei/FchIdsHookBrhLibPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_FchIdsHookBrhLib_Pei_FchIdsHookBrhLibPei_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FchIdsHookLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FchIdsHookBrhLib_Pei_FchIdsHookBrhLibPei_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_FchIdsHookBrhLib_Pei_FchIdsHookBrhLibPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_Ras_Brh_RasBrhSocLib_RasBrhSocLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_Ras_Brh_RasBrhSocLib_RasBrhSocLib_INF"
    File = "Library/Ras/Brh/RasBrhSocLib/RasBrhSocLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_Ras_Brh_RasBrhSocLib_RasBrhSocLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "RasSocLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_Ras_Brh_RasBrhSocLib_RasBrhSocLib_INF"
    Token = "_AgesaModulePkg_Library_Ras_Brh_RasBrhSocLib_RasBrhSocLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_FchSpiAccessLib_FchSpiAccessRom2Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FchSpiAccessLib_FchSpiAccessRom2Lib_INF"
    File = "Library/FchSpiAccessLib/FchSpiAccessRom2Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_FchSpiAccessLib_FchSpiAccessRom2Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FchSpiAccessLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FchSpiAccessLib_FchSpiAccessRom2Lib_INF"
    Token = "_AgesaModulePkg_Library_FchSpiAccessLib_FchSpiAccessRom2Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdPspFlashAccLibNull_AmdPspFlashAccLibNull_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspFlashAccLibNull_AmdPspFlashAccLibNull_INF"
    File = "Library/AmdPspFlashAccLibNull/AmdPspFlashAccLibNull.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdPspFlashAccLibNull_AmdPspFlashAccLibNull_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspFlashAccLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspFlashAccLibNull_AmdPspFlashAccLibNull_INF"
    Token = "_AgesaModulePkg_Library_AmdPspFlashAccLibNull_AmdPspFlashAccLibNull_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_GnbIoAccLib_GnbIoAccLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_GnbIoAccLib_GnbIoAccLib_INF"
    File = "Library/GnbIoAccLib/GnbIoAccLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_GnbIoAccLib_GnbIoAccLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "GnbIoAccLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_GnbIoAccLib_GnbIoAccLib_INF"
    Token = "_AgesaModulePkg_Library_GnbIoAccLib_GnbIoAccLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_CcxSetMmioCfgBaseLib_CcxSetMmioCfgBaseLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_CcxSetMmioCfgBaseLib_CcxSetMmioCfgBaseLib_INF"
    File = "Library/CcxSetMmioCfgBaseLib/CcxSetMmioCfgBaseLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_CcxSetMmioCfgBaseLib_CcxSetMmioCfgBaseLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxSetMmioCfgBaseLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_CcxSetMmioCfgBaseLib_CcxSetMmioCfgBaseLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_CcxSetMmioCfgBaseLib_CcxSetMmioCfgBaseLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_BaseCoreLogicalIdX86Lib_BaseCoreLogicalIdX86Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_BaseCoreLogicalIdX86Lib_BaseCoreLogicalIdX86Lib_INF"
    File = "Library/BaseCoreLogicalIdX86Lib/BaseCoreLogicalIdX86Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_BaseCoreLogicalIdX86Lib_BaseCoreLogicalIdX86Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "BaseCoreLogicalIdLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_BaseCoreLogicalIdX86Lib_BaseCoreLogicalIdX86Lib_INF"
    Token = "_AgesaModulePkg_Library_BaseCoreLogicalIdX86Lib_BaseCoreLogicalIdX86Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdPspSfsLib_AmdPspSfsLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspSfsLib_AmdPspSfsLib_INF"
    File = "Library/AmdPspSfsLib/AmdPspSfsLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdPspSfsLib_AmdPspSfsLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspSfsLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspSfsLib_AmdPspSfsLib_INF"
    Token = "_AgesaModulePkg_Library_AmdPspSfsLib_AmdPspSfsLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdPspRegBaseLib_AmdPspRegBaseLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspRegBaseLib_AmdPspRegBaseLib_INF"
    File = "Library/AmdPspRegBaseLib/AmdPspRegBaseLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdPspRegBaseLib_AmdPspRegBaseLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspRegBaseLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspRegBaseLib_AmdPspRegBaseLib_INF"
    Token = "_AgesaModulePkg_Library_AmdPspRegBaseLib_AmdPspRegBaseLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdCfgPcdBufLibDxe_AmdCfgPcdBufLibDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdCfgPcdBufLibDxe_AmdCfgPcdBufLibDxe_INF"
    File = "Library/AmdCfgPcdBufLibDxe/AmdCfgPcdBufLibDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdCfgPcdBufLibDxe_AmdCfgPcdBufLibDxe_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdCfgPcdBufLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdCfgPcdBufLibDxe_AmdCfgPcdBufLibDxe_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdCfgPcdBufLibDxe_AmdCfgPcdBufLibDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_FchIdsHookLib_FchIdsHookLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FchIdsHookLib_FchIdsHookLib_INF"
    File = "Library/FchIdsHookLib/FchIdsHookLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_FchIdsHookLib_FchIdsHookLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FchIdsHookLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FchIdsHookLib_FchIdsHookLib_INF"
    Token = "_AgesaModulePkg_Library_FchIdsHookLib_FchIdsHookLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Nbio_Library_PcieComplexDefaultsLib_PcieComplexDefaultsLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Nbio_Library_PcieComplexDefaultsLib_PcieComplexDefaultsLib_INF"
    File = "Nbio/Library/PcieComplexDefaultsLib/PcieComplexDefaultsLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Nbio_Library_PcieComplexDefaultsLib_PcieComplexDefaultsLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "PcieComplexDefaultsLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Nbio_Library_PcieComplexDefaultsLib_PcieComplexDefaultsLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Nbio_Library_PcieComplexDefaultsLib_PcieComplexDefaultsLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_FchPeiLib_FchPeiLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FchPeiLib_FchPeiLib_INF"
    File = "Library/FchPeiLib/FchPeiLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_FchPeiLib_FchPeiLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FchPeiLibV9"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FchPeiLib_FchPeiLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_FchPeiLib_FchPeiLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_FchInitHookLib_FchInitHookLibDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FchInitHookLib_FchInitHookLibDxe_INF"
    File = "Library/FchInitHookLib/FchInitHookLibDxe.inf"
    Package = "AgesaModulePkg"
    Arch = "X64"
    ModuleTypes = "DXE_DRIVER"
    Token = "_AgesaModulePkg_Library_FchInitHookLib_FchInitHookLibDxe_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FchInitHookLibDxe"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FchInitHookLib_FchInitHookLibDxe_INF"
    Token = "_AgesaModulePkg_Library_FchInitHookLib_FchInitHookLibDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspMmioLibSmmIso_AmdPspMmioLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspMmioLibSmmIso_AmdPspMmioLib_INF"
    File = "Library/AmdPspMmioLibSmmIso/AmdPspMmioLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspMmioLibSmmIso_AmdPspMmioLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspMmioLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspMmioLibSmmIso_AmdPspMmioLib_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspMmioLibSmmIso_AmdPspMmioLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspMmioLib_AmdPspMmioLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspMmioLib_AmdPspMmioLib_INF"
    File = "Library/AmdPspMmioLib/AmdPspMmioLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspMmioLib_AmdPspMmioLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspMmioLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspMmioLib_AmdPspMmioLib_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspMmioLib_AmdPspMmioLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_PeiFabricTopologyServices2Lib_PeiFabricTopologyServices2Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_PeiFabricTopologyServices2Lib_PeiFabricTopologyServices2Lib_INF"
    File = "Library/PeiFabricTopologyServices2Lib/PeiFabricTopologyServices2Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "_AgesaModulePkg_Library_PeiFabricTopologyServices2Lib_PeiFabricTopologyServices2Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "PeiFabricTopologyServices2Lib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_PeiFabricTopologyServices2Lib_PeiFabricTopologyServices2Lib_INF"
    Token = "_AgesaModulePkg_Library_PeiFabricTopologyServices2Lib_PeiFabricTopologyServices2Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_FchDxeLegacyInterruptLib_FchDxeLegacyInterruptLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FchDxeLegacyInterruptLib_FchDxeLegacyInterruptLib_INF"
    File = "Library/FchDxeLegacyInterruptLib/FchDxeLegacyInterruptLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "_AgesaModulePkg_Library_FchDxeLegacyInterruptLib_FchDxeLegacyInterruptLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "LegacyInterruptLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FchDxeLegacyInterruptLib_FchDxeLegacyInterruptLib_INF"
    Token = "_AgesaModulePkg_Library_FchDxeLegacyInterruptLib_FchDxeLegacyInterruptLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_CcxZen5SegRmpBrhLib_CcxZen5SegRmpBrhDxeLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_CcxZen5SegRmpBrhLib_CcxZen5SegRmpBrhDxeLib_INF"
    File = "Library/CcxZen5SegRmpBrhLib/CcxZen5SegRmpBrhDxeLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "_AgesaModulePkg_Library_CcxZen5SegRmpBrhLib_CcxZen5SegRmpBrhDxeLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxZen5SegRmpDxeLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_CcxZen5SegRmpBrhLib_CcxZen5SegRmpBrhDxeLib_INF"
    Token = "_AgesaModulePkg_Library_CcxZen5SegRmpBrhLib_CcxZen5SegRmpBrhDxeLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_INF"
    File = "Library/IdsNonUefiLib/IdsNonUefiLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "IdsLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_INF"
    Token = "_AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_ApobBrhLib_ApobBrhLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_ApobBrhLib_ApobBrhLib_INF"
    File = "Library/ApobBrhLib/ApobBrhLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_ApobBrhLib_ApobBrhLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "ApobBrhLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_ApobBrhLib_ApobBrhLib_INF"
    Token = "_AgesaModulePkg_Library_ApobBrhLib_ApobBrhLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdIdsDebugPrintLib_AmdIdsDebugPrintLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdIdsDebugPrintLib_AmdIdsDebugPrintLib_INF"
    File = "Library/AmdIdsDebugPrintLib/AmdIdsDebugPrintLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdIdsDebugPrintLib_AmdIdsDebugPrintLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdIdsDebugPrintLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdIdsDebugPrintLib_AmdIdsDebugPrintLib_INF"
    Token = "_AgesaModulePkg_Library_AmdIdsDebugPrintLib_AmdIdsDebugPrintLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_SocCoreInfo2AccessLib_SocCoreInfo2AccessLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_SocCoreInfo2AccessLib_SocCoreInfo2AccessLib_INF"
    File = "Library/SocCoreInfo2AccessLib/SocCoreInfo2AccessLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_SocCoreInfo2AccessLib_SocCoreInfo2AccessLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "SocCoreInfo2AccessLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_SocCoreInfo2AccessLib_SocCoreInfo2AccessLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_SocCoreInfo2AccessLib_SocCoreInfo2AccessLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_AmdRtclib_AmdRtcLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdRtclib_AmdRtcLib_INF"
    File = "Library/AmdRtclib/AmdRtcLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_AmdRtclib_AmdRtcLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "RtcLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdRtclib_AmdRtcLib_INF"
    Token = "_AgesaModulePkg_Library_AmdRtclib_AmdRtcLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_AmdPspFlashAccLibDxe_AmdPspFlashAccLibDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspFlashAccLibDxe_AmdPspFlashAccLibDxe_INF"
    File = "Library/AmdPspFlashAccLibDxe/AmdPspFlashAccLibDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdPspFlashAccLibDxe_AmdPspFlashAccLibDxe_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspFlashAccLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspFlashAccLibDxe_AmdPspFlashAccLibDxe_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdPspFlashAccLibDxe_AmdPspFlashAccLibDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdCapsuleLibDxe_AmdCapsuleLibDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdCapsuleLibDxe_AmdCapsuleLibDxe_INF"
    File = "Library/AmdCapsuleLibDxe/AmdCapsuleLibDxe.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdCapsuleLibDxe_AmdCapsuleLibDxe_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdCapsuleLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdCapsuleLibDxe_AmdCapsuleLibDxe_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdCapsuleLibDxe_AmdCapsuleLibDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_FabricResourceReportToGcdNullLib_FabricResourceReportToGcdNullLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FabricResourceReportToGcdNullLib_FabricResourceReportToGcdNullLib_INF"
    File = "Library/FabricResourceReportToGcdNullLib/FabricResourceReportToGcdNullLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_FabricResourceReportToGcdNullLib_FabricResourceReportToGcdNullLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FabricResourceReportToGcdLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FabricResourceReportToGcdNullLib_FabricResourceReportToGcdNullLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_FabricResourceReportToGcdNullLib_FabricResourceReportToGcdNullLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_AmdTableLibV2_Dxe_AmdTableHookDxeLibV2_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdTableLibV2_Dxe_AmdTableHookDxeLibV2_INF"
    File = "Library/AmdTableLibV2/Dxe/AmdTableHookDxeLibV2.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdTableLibV2_Dxe_AmdTableHookDxeLibV2_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdTableHookLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdTableLibV2_Dxe_AmdTableHookDxeLibV2_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_AmdTableLibV2_Dxe_AmdTableHookDxeLibV2_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AgesaModulePkg_Library_DxeAmlGenerationLib_AmlGenerationLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_DxeAmlGenerationLib_AmlGenerationLib_INF"
    File = "Library/DxeAmlGenerationLib/AmlGenerationLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_DxeAmlGenerationLib_AmlGenerationLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmlGenerationLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_DxeAmlGenerationLib_AmlGenerationLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AgesaModulePkg_Library_DxeAmlGenerationLib_AmlGenerationLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdHeapDxeLib_AmdHeapDxeLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdHeapDxeLib_AmdHeapDxeLib_INF"
    File = "Library/AmdHeapDxeLib/AmdHeapDxeLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdHeapDxeLib_AmdHeapDxeLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdHeapLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdHeapDxeLib_AmdHeapDxeLib_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdHeapDxeLib_AmdHeapDxeLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "SEC_AgesaModulePkg_Library_CcxNonSmmResumeSecLib_CcxNonSmmResumeSecLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_CcxNonSmmResumeSecLib_CcxNonSmmResumeSecLib_INF"
    File = "Library/CcxNonSmmResumeSecLib/CcxNonSmmResumeSecLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "SEC_AgesaModulePkg_Library_CcxNonSmmResumeSecLib_CcxNonSmmResumeSecLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxNonSmmResumeSecLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_CcxNonSmmResumeSecLib_CcxNonSmmResumeSecLib_INF"
    ModuleTypes = "SEC"
    Token = "SEC_AgesaModulePkg_Library_CcxNonSmmResumeSecLib_CcxNonSmmResumeSecLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Nbio_Library_SmnTable_SmnTableLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Nbio_Library_SmnTable_SmnTableLib_INF"
    File = "Nbio/Library/SmnTable/SmnTableLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Nbio_Library_SmnTable_SmnTableLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "SmnTableLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Nbio_Library_SmnTable_SmnTableLib_INF"
    Token = "_AgesaModulePkg_Nbio_Library_SmnTable_SmnTableLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Nbio_Library_CxlMboxLib_CxlMboxLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Nbio_Library_CxlMboxLib_CxlMboxLib_INF"
    File = "Nbio/Library/CxlMboxLib/CxlMboxLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Nbio_Library_CxlMboxLib_CxlMboxLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CxlMboxLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Nbio_Library_CxlMboxLib_CxlMboxLib_INF"
    Token = "_AgesaModulePkg_Nbio_Library_CxlMboxLib_CxlMboxLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_FchBaseLib_FchBaseLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_FchBaseLib_FchBaseLib_INF"
    File = "Library/FchBaseLib/FchBaseLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_FchBaseLib_FchBaseLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FchBaseLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_FchBaseLib_FchBaseLib_INF"
    Token = "_AgesaModulePkg_Library_FchBaseLib_FchBaseLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_CcxZen5BrhIdsHookLib_Pei_CcxZen5BrhIdsHookLibPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_CcxZen5BrhIdsHookLib_Pei_CcxZen5BrhIdsHookLibPei_INF"
    File = "Library/CcxZen5BrhIdsHookLib/Pei/CcxZen5BrhIdsHookLibPei.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "PEIM_AgesaModulePkg_Library_CcxZen5BrhIdsHookLib_Pei_CcxZen5BrhIdsHookLibPei_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CcxZen5IdsHookLibPei"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_CcxZen5BrhIdsHookLib_Pei_CcxZen5BrhIdsHookLibPei_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_CcxZen5BrhIdsHookLib_Pei_CcxZen5BrhIdsHookLibPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspDxeSmmBufLib_AmdPspDxeSmmBufLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_AmdPspDxeSmmBufLib_AmdPspDxeSmmBufLib_INF"
    File = "Library/AmdPspDxeSmmBufLib/AmdPspDxeSmmBufLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspDxeSmmBufLib_AmdPspDxeSmmBufLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPspDxeSmmBufLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_AmdPspDxeSmmBufLib_AmdPspDxeSmmBufLib_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspDxeSmmBufLib_AmdPspDxeSmmBufLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_BxbNbio_BxbNullLib_BxbNullLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_BxbNbio_BxbNullLib_BxbNullLib_INF"
    File = "Library/BxbNbio/BxbNullLib/BxbNullLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_BxbNbio_BxbNullLib_BxbNullLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "BxbInitLibV1"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_BxbNbio_BxbNullLib_BxbNullLib_INF"
    Token = "_AgesaModulePkg_Library_BxbNbio_BxbNullLib_BxbNullLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AgesaModulePkg_Library_PeiCoreTopologyV3Lib_PeiCoreTopologyV3Lib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_PeiCoreTopologyV3Lib_PeiCoreTopologyV3Lib_INF"
    File = "Library/PeiCoreTopologyV3Lib/PeiCoreTopologyV3Lib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_PeiCoreTopologyV3Lib_PeiCoreTopologyV3Lib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CoreTopologyV3Lib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_PeiCoreTopologyV3Lib_PeiCoreTopologyV3Lib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AgesaModulePkg_Library_PeiCoreTopologyV3Lib_PeiCoreTopologyV3Lib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AgesaModulePkg_Library_ApobDummyLib_ApobDummyLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AgesaModulePkg_Library_ApobDummyLib_ApobDummyLib_INF"
    File = "Library/ApobDummyLib/ApobDummyLib.inf"
    Package = "AgesaModulePkg"
    ModuleTypes = "BASE"
    Token = "_AgesaModulePkg_Library_ApobDummyLib_ApobDummyLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "ApobDummyLib"
    Instance = "AgesaModulePkg.AgesaModulePkg_Library_ApobDummyLib_ApobDummyLib_INF"
    Token = "_AgesaModulePkg_Library_ApobDummyLib_ApobDummyLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FabricResourceManagerLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceManager3Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Psp_AmdPspPeiV2Brh_AmdPspPeiV2_INF"
End

LibraryMapping
    Class = "CcxResetTablesLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_CcxResetTablesZen5Lib_CcxResetTablesZen5Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Ccx_Zen5_Pei_AmdCcxZen5Pei_INF"
End

LibraryMapping
    Class = "IdsLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Ccx_Zen5_Pei_AmdCcxZen5Pei_INF"
End

LibraryMapping
    Class = "CcxRolesLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_CcxRolesZen5Lib_CcxRolesZen5Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Ccx_Zen5_Pei_AmdCcxZen5Pei_INF"
End

LibraryMapping
    Class = "CcxPstatesLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_CcxPstatesZen5Lib_CcxPstatesZen5Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Ccx_Zen5_Pei_AmdCcxZen5Pei_INF"
End

LibraryMapping
    Class = "CcxSetMcaLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_CcxSetMcaZen5Lib_CcxSetMcaZen5Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Ccx_Zen5_Pei_AmdCcxZen5Pei_INF"
End

LibraryMapping
    Class = "FabricWdtLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_FabricWdtDf4Lib_FabricWdtDf4Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Ccx_Zen5_Pei_AmdCcxZen5Pei_INF"
End

LibraryMapping
    Class = "IdsLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Fabric_BRH_FabricBrhPei_AmdFabricBrhPei_INF"
End

LibraryMapping
    Class = "BaseFabricTopologyLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_BaseFabricTopologyBrhLib_BaseFabricTopologyBrhLib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Fabric_BRH_FabricBrhPei_AmdFabricBrhPei_INF"
End

LibraryMapping
    Class = "FabricResourceManagerLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceManager3Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Fabric_BRH_FabricBrhPei_AmdFabricBrhPei_INF"
End

LibraryMapping
    Class = "FabricResourceManagerLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceManager3Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Nbio_BRH_PEI_NbioPeiBrh_INF"
End

LibraryMapping
    Class = "FabricResourceManagerLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceManager3Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Fch_Kunlun_FchKunlunMultiFchPei_FchMultiFchPei_INF"
End

LibraryMapping
    Class = "FabricResourceInitLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceInit3Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Soc_AmdSocSp5BrhPei_AmdSocSp5BrhPei_INF"
End

LibraryMapping
    Class = "BaseFabricTopologyLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_BaseFabricTopologyBrhLib_BaseFabricTopologyBrhLib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Soc_AmdSocSp5BrhPei_AmdSocSp5BrhPei_INF"
End

LibraryMapping
    Class = "BaseSocketLogicalIdLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_BaseSocketLogicalIdRsDieLib_BaseSocketLogicalIdRsDieLib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Soc_AmdSocSp5BrhPei_AmdSocSp5BrhPei_INF"
End

LibraryMapping
    Class = "BaseSocLogicalIdXlatLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_BaseSocLogicalIdXlatZen5DieLib_BaseSocLogicalIdXlatZen5DieLib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Soc_AmdSocSp5BrhPei_AmdSocSp5BrhPei_INF"
End

LibraryMapping
    Class = "PeiSocBistLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_PeiSocBistZen5CcdBrhLib_PeiSocBistZen5CcdBrhLib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Soc_AmdSocSp5BrhPei_AmdSocSp5BrhPei_INF"
End

LibraryMapping
    Class = "PeiFabricSocSpecificServicesLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_PeiFabricSocSpecificServicesBrhLib_PeiFabricSocSpecificServicesBrhLib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Soc_AmdSocSp5BrhPei_AmdSocSp5BrhPei_INF"
End

LibraryMapping
    Class = "PeiSocZen5ServicesLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_PeiSocZen5ServicesBrhLib_PeiSocZen5ServicesBrhLib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Soc_AmdSocSp5BrhPei_AmdSocSp5BrhPei_INF"
End

LibraryMapping
    Class = "PeiCoreTopologyServicesV3Lib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_PeiCcxCoreTopologyServicesV3BrhLib_PeiCcxCoreTopologyServicesV3BrhLib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Soc_AmdSocSp5BrhPei_AmdSocSp5BrhPei_INF"
End

LibraryMapping
    Class = "ApobApcbUpdatesLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_ApobApcbUpdatesBrhLib_ApobApcbUpdatesBrhLib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Soc_AmdSocSp5BrhPei_AmdSocSp5BrhPei_INF"
End

LibraryMapping
    Class = "FabricRootBridgeOrderLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_FabricRootBridgeOrderLib_FabricRootBridgeOrderLib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Soc_AmdSocSp5BrhPei_AmdSocSp5BrhPei_INF"
End

LibraryMapping
    Class = "AmdMemSmbiosV2Lib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_MemSmbiosV2BrhD5Lib_MemSmbiosV2Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Mem_AmdMemSmbiosV2BrhPei_MemSmbiosV2Pei_INF"
End

LibraryMapping
    Class = "IdsLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Ccx_Zen5_Dxe_AmdCcxZen5Dxe_INF"
End

LibraryMapping
    Class = "CcxResetTablesLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_CcxResetTablesZen5Lib_CcxResetTablesZen5Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Ccx_Zen5_Dxe_AmdCcxZen5Dxe_INF"
End

LibraryMapping
    Class = "CcxSetMcaLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_CcxSetMcaZen5Lib_CcxSetMcaZen5Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Ccx_Zen5_Dxe_AmdCcxZen5Dxe_INF"
End

LibraryMapping
    Class = "FabricWdtLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_FabricWdtDf4Lib_FabricWdtDf4Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Ccx_Zen5_Dxe_AmdCcxZen5Dxe_INF"
End

LibraryMapping
    Class = "CcxSmbiosLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_CcxSmbiosZen5Lib_CcxSmbiosZen5Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Ccx_Zen5_Dxe_AmdCcxZen5Dxe_INF"
End

LibraryMapping
    Class = "CcxRolesLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_CcxRolesZen5Lib_CcxRolesZen5Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Ccx_Zen5_Dxe_AmdCcxZen5Dxe_INF"
End

LibraryMapping
    Class = "CcxPstatesLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_CcxPstatesZen5Lib_CcxPstatesZen5Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Ccx_Zen5_Dxe_AmdCcxZen5Dxe_INF"
End

LibraryMapping
    Class = "CcxSetMcaLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_CcxSetMcaZen5Lib_CcxSetMcaZen5Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Ccx_Zen5_Smm_AmdCcxZen5Smm_INF"
End

LibraryMapping
    Class = "IdsLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Fabric_BRH_FabricBrhDxe_AmdFabricBrhDxe_INF"
End

LibraryMapping
    Class = "FabricResourceManagerLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceManager3Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Fabric_BRH_FabricBrhDxe_AmdFabricBrhDxe_INF"
End

LibraryMapping
    Class = "BaseFabricTopologyLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_BaseFabricTopologyBrhLib_BaseFabricTopologyBrhLib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Fabric_BRH_FabricBrhDxe_AmdFabricBrhDxe_INF"
End

LibraryMapping
    Class = "AmdS3SaveLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_AmdS3SaveLib_WOS3Save_AmdWOS3SaveLib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Fabric_BRH_FabricBrhSmm_AmdFabricBrhSmm_INF"
End

LibraryMapping
    Class = "AmdS3SaveLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_AmdS3SaveLib_WOS3Save_AmdWOS3SaveLib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Soc_AmdSocSp5BrhDxe_AmdSocSp5BrhDxe_INF"
End

LibraryMapping
    Class = "BaseSocketLogicalIdLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_BaseSocketLogicalIdRsDieLib_BaseSocketLogicalIdRsDieLib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Soc_AmdSocSp5BrhDxe_AmdSocSp5BrhDxe_INF"
End

LibraryMapping
    Class = "BaseSocLogicalIdXlatLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_BaseSocLogicalIdXlatZen5DieLib_BaseSocLogicalIdXlatZen5DieLib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Soc_AmdSocSp5BrhDxe_AmdSocSp5BrhDxe_INF"
End

LibraryMapping
    Class = "BaseFabricTopologyLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_BaseFabricTopologyBrhLib_BaseFabricTopologyBrhLib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Soc_AmdSocSp5BrhDxe_AmdSocSp5BrhDxe_INF"
End

LibraryMapping
    Class = "DxeSocZen5ServicesLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_DxeSocZen5ServicesBrhLib_DxeSocZen5ServicesBrhLib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Soc_AmdSocSp5BrhDxe_AmdSocSp5BrhDxe_INF"
End

LibraryMapping
    Class = "DxeCoreTopologyServicesV3Lib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_DxeCcxCoreTopologyServicesV3BrhLib_DxeCcxCoreTopologyServicesV3BrhLib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Soc_AmdSocSp5BrhDxe_AmdSocSp5BrhDxe_INF"
End

LibraryMapping
    Class = "DxeFabricSocSpecificServicesLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_DxeFabricSocSpecificServicesBrhLib_DxeFabricSocSpecificServicesBrhLib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Soc_AmdSocSp5BrhDxe_AmdSocSp5BrhDxe_INF"
End

LibraryMapping
    Class = "AmdIdsHookExtLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_AmdIdsExtLibNull_AmdIdsHookExtLibNull_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Soc_AmdSocSp5BrhDxe_AmdSocSp5BrhDxe_INF"
End

LibraryMapping
    Class = "FabricResourceManagerLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceManager3Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Nbio_Common_CxlManagerDxe_CxlManagerDxe_INF"
End

LibraryMapping
    Class = "NbioIommuIvrsLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Nbio_Library_IvrsLibV3_IvrsLibV3_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Nbio_BRH_DXE_NbioDxeBrh_INF"
End

LibraryMapping
    Class = "FabricResourceManagerLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceManager3Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Nbio_BRH_DXE_NbioDxeBrh_INF"
End

LibraryMapping
    Class = "CollectNbifPortInfoLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Nbio_BRH_Library_CollectNbifPortInfoLib_CollectNbifPortInfoLib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Nbio_BRH_DXE_NbioDxeBrh_INF"
End

LibraryMapping
    Class = "FabricResourceManagerLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceManager3Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Fch_Kunlun_FchKunlunDxe_FchDxe_INF"
End

LibraryMapping
    Class = "FabricResourceManagerLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceManager3Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Fch_Kunlun_FchKunlunSmm_FchSmm_INF"
End

LibraryMapping
    Class = "FabricResourceManagerLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceManager3Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Fch_Kunlun_FchKunlunSmmDispatcher_FchSmmDispatcher_INF"
End

LibraryMapping
    Class = "FabricResourceManagerLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceManager3Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Fch_Kunlun_FchKunlunMultiFchDxe_FchMultiFchDxe_INF"
End

LibraryMapping
    Class = "FabricResourceManagerLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceManager3Lib_INF"
    Override  = "AgesaModulePkg.AgesaModulePkg_Fch_Kunlun_FchKunlunMultiFchSmm_FchMultiFchSmm_INF"
End

PcdMapping
    Name  = "PcdAmdPackageString"
    GuidSpace  = "gEfiAmdAgesaModulePkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "Pointer"
    Value  = '"AGESA!V9\0TurinPI-SP5 *******"'
    Offset  = 00h
    Length  = 01eh
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdAmdMemMaxChannelPerDieV2"
    GuidSpace  = "gEfiAmdAgesaModulePkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "UINT8"
    Value  = "12"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdAmdPspApobUseSpiMmioAddress"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdXhciForceGen1"
    GuidSpace  = "gEfiAmdAgesaModulePkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "UINT8"
    Value  = "0xFF"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdMPIOAncDataSupport"
    GuidSpace  = "gEfiAmdAgesaModulePkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdMpdmaAcpiIvrsSupport"
    GuidSpace  = "gEfiAmdAgesaModulePkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdIvrsRelativeAddrNamesSupport"
    GuidSpace  = "gEfiAmdAgesaModulePkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdAmdEnableFSRM"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdAmdEnableERMS"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdAmdEnableRMSS"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdAmdEnableSvmAVIC"
    GuidSpace  = "gEfiAmdAgesaModulePkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdAmdAcpiCpuCstC2Latency"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "UINT16"
    Value  = "0x64"
    Offset  = 00h
    Length  = 002h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdAmdFabricCdma"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "FALSE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdAmdCpuPauseCntSel_1_0"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "UINT8"
    Value  = "0xFF"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdAmdCpuReqMinFreq"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "UINT16"
    Value  = "0"
    Offset  = 00h
    Length  = 002h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdAmdFabricSratSlitInstallOverride"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdSyncFloodToApml"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdMaster7bitSteeringTag"
    GuidSpace  = "gEfiAmdAgesaModulePkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdEnable2SpcGen4"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdCfgSriovEnDev0F1"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdCfgAriEnDev0F1"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdCfgAerEnDev0F1"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdCfgAcsEnDev0F1"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdCfgAtsEnDev0F1"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdCfgPasidEnDev0F1"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdCfgDev0F1PasidEn"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdCfgRtrEnDev0F1"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdCfgPriEnDev0F1"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdCfgPwrEnDev0F1"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdAtcEnable"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdNbifDev0F1AtomicRequestEn"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdAcsEnRccDev0"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdAcsP2pReq"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdAcsSourceVal"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdRccDev0E2EPrefix"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdRccDev0ExtendedFmtSupported"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdAtomicRoutingEnStrap5"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdApcbUseHmacChecksum"
    GuidSpace  = "gEfiAmdAgesaModulePkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdLockApcbDxeAfterSmmLock"
    GuidSpace  = "gEfiAmdAgesaModulePkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType  = "BOOLEAN"
    Value  = "TRUE"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdDfPstateRangeMin"
    GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicExDefault"
    DataType  = "UINT8"
    Value  = "2"
    Offset  = 00h
    Length  = 001h
    TargetDSC = Yes
End

