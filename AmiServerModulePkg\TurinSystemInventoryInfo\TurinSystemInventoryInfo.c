//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************



/** @file TurinSystemInventoryInfo.c
    Turin System Inventory Info Porting Library
  SystemInventoryInfoPortingLib: Function Implementations of different 
  Hooks available through ELINKs for updating different categories of 
  Platform Inventory Data in System Inventory Info Protocol.
**/
#include <TurinSystemInventoryInfo.h>
#include <Include/Library/AmiSysInvStringLib.h>
#include <Include/CpuRegisters.h>
#include <Addendum/Apcb/Inc/Common/MyPorting.h>
#include <Addendum/Apcb/Inc/BRH/APOB.h>
#include <Include/AmdSmBios.h>
#include <Include/Protocol/AmiSmbios.h>
#include <Library/UefiCpuLib.h>

///
/// ELINKs Functions Implementations.
///

/**

  ELINK that updates System Inventory Info Protocol Data.
  Gets ME operational and Recovery Version and Updates gSystemInventoryInfoProtocol global data with ME info
  @param VOID   SystemInventoryInfoProtocol  - Pointer to System Inventory Info Protocol Structure.

  @retval VOID    Updated gSystemInventoryInfoProtocol global data with ME info

**/

VOID
OemUpdateSystemInventoryInfoData (
  IN  SYSTEM_INVENTORY_INFO_PROTOCOL      *SystemInventoryInfoProtocol
  )
{
    // This Function can be implemented to update any Data in System Inventory Info 
    // Protocol. 
 
    return;
}


/**

  ELINK that can update CPU Inventory Data in System Inventory Info Protocol.

    @param [in][out] SystemInventoryProtocol        Pointer to System Inventory Protocol Instance
    @param [in][out] DeviceEntry                    Pointer to CPU Device Data

  @retval VOID    

**/

VOID
OemUpdateCpuInventoryData (
  IN OUT  SYSTEM_INVENTORY_INFO_PROTOCOL      *SystemInventoryInfoProtocol,
  IN  DEV_ENTRY                               *DeviceEntry
  )
{
    // This Function can be implemented to UPDATE CPU Inventory data
    // in System Inventory Info Protocol.
    
    /// Note: This Function will be called for Every Processor Socket 
    /// in Platform. Processor of Interest can be differentiated
    /// with ONE of the below Key Identifiers.
    
    /// Key Identifiers:
    /// 1. SocketIndex, 
    /// 2. AssetTag.

    /// Key Identifiers is a suggestive List. OEMs are open to choose their OWN Identifiers otherwise.
    
    SYS_INV_CPU_INFO                *DisplayCpu = NULL;
    UINT8                           CoreIndex = 0;
//    UINT64                          ResolvedCoresMask;
//    UINT64                          FusedCoresMask;
    CHAR8                           StringBuffer[0xFF];
    UINT32                          EffectiveFamily = 0;
    
    DEBUG((DEBUG_INFO, "%a() Entry \n", __FUNCTION__));

    DisplayCpu = &DeviceEntry->DisplayPtr.Cpu;
		    
    EffectiveFamily = GetCpuFamilyModel();
    DEBUG((DEBUG_INFO, "FamilyId - %d\n",EffectiveFamily));

    EffectiveFamily = ((EffectiveFamily & 0x00FF00000) >> 20) + ((EffectiveFamily & 0x0F00) >> 8);
    DEBUG((DEBUG_INFO, "ProcessorFamily - %d\n",EffectiveFamily));

    AsciiSPrint(
        StringBuffer,
        PcdGet32 (PcdAmiSysInvMaxStringLength) ,
        "0x%x",
        EffectiveFamily );
    
    DEBUG((DEBUG_INFO, "ProcessorFamilyString - %a\n",StringBuffer)); 

    if(DisplayCpu->ProcessorId.ValidFlags1.ProcessorIdVF1Param.EffectiveFamilyValid == 0) {
    SysInvAddString (
        SystemInventoryInfoProtocol,
        &DisplayCpu->StringHdr,
        StringBuffer,
        &DisplayCpu->ProcessorId.EffectiveFamilyStrIndex);

    DisplayCpu->ProcessorId.ValidFlags1.ProcessorIdVF1Param.EffectiveFamilyValid = TRUE;

    } else {
    SysInvUpdateString (
        SystemInventoryInfoProtocol,
        &DisplayCpu->StringHdr,
        StringBuffer,
        DisplayCpu->ProcessorId.EffectiveFamilyStrIndex);
    }
//    ResolvedCoresMask = PcuGetResolvedCores (DisplayCpu->SocketIndex, 0);
//    FusedCoresMask = PcuGetAvailableCores (DisplayCpu->SocketIndex, 0);

	for(CoreIndex = 0; CoreIndex < DisplayCpu->TotalCores; CoreIndex++) {
//        DisplayCpu->CoresData[CoreIndex].State = ((ResolvedCoresMask >> CoreIndex) & (UINT64)0x1) ? SysInvDevEnabled : SysInvDevDisabled;
        if (DisplayCpu->CoresData[CoreIndex].State == SysInvDevEnabled) {
            DisplayCpu->CoresData[CoreIndex].MaxFrequency = DisplayCpu->MaxSpeedMHz;
            DisplayCpu->CoresData[CoreIndex].CurrentFrequency = DisplayCpu->CurrentFrequency;
        }
        else {
//            DisplayCpu->CoresData[CoreIndex].State = (((ResolvedCoresMask >> CoreIndex) & (UINT64)0x1)| ((FusedCoresMask >> CoreIndex) & (UINT64)0x1)) ? SysInvDevDisabled : SysInvDevAbsent;
            DisplayCpu->CoresData[CoreIndex].MaxFrequency = 0;
            DisplayCpu->CoresData[CoreIndex].CurrentFrequency = 0;
        }
        DisplayCpu->CoresData[CoreIndex].ValidFlags1.CoreDataVF1Param.StateValid = 1;
        DisplayCpu->CoresData[CoreIndex].ValidFlags1.CoreDataVF1Param.MaxFrequencyValid = 1;
        DisplayCpu->CoresData[CoreIndex].ValidFlags1.CoreDataVF1Param.CurrentFrequencyValid = 1;
            
        DisplayCpu->CoresData[CoreIndex].ThreadsData[0].State = DisplayCpu->CoresData[CoreIndex].State;
        DisplayCpu->CoresData[CoreIndex].ThreadsData[0].MaxFrequency = DisplayCpu->CoresData[CoreIndex].MaxFrequency;
        DisplayCpu->CoresData[CoreIndex].ThreadsData[0].CurrentFrequency = DisplayCpu->CoresData[CoreIndex].CurrentFrequency;
        if (DisplayCpu->HyperThreadingEnabled) {
            DisplayCpu->CoresData[CoreIndex].ThreadsData[1].State = DisplayCpu->CoresData[CoreIndex].State;
            DisplayCpu->CoresData[CoreIndex].ThreadsData[1].MaxFrequency = DisplayCpu->CoresData[CoreIndex].MaxFrequency;
            DisplayCpu->CoresData[CoreIndex].ThreadsData[1].CurrentFrequency = DisplayCpu->CoresData[CoreIndex].CurrentFrequency;
        }
        else {
            DisplayCpu->CoresData[CoreIndex].ThreadsData[1].State = SysInvDevDisabled;
            DisplayCpu->CoresData[CoreIndex].ThreadsData[1].MaxFrequency = 0;
            DisplayCpu->CoresData[CoreIndex].ThreadsData[1].CurrentFrequency = 0;
        }
        DisplayCpu->CoresData[CoreIndex].ThreadsData[0].ValidFlags1.ThreadDataVF1Param.StateValid = 1;
        DisplayCpu->CoresData[CoreIndex].ThreadsData[0].ValidFlags1.ThreadDataVF1Param.MaxFrequencyValid = 1;
        DisplayCpu->CoresData[CoreIndex].ThreadsData[0].ValidFlags1.ThreadDataVF1Param.CurrentFrequencyValid = 1;
        DisplayCpu->CoresData[CoreIndex].ThreadsData[1].ValidFlags1.ThreadDataVF1Param.StateValid = 1;
        DisplayCpu->CoresData[CoreIndex].ThreadsData[1].ValidFlags1.ThreadDataVF1Param.MaxFrequencyValid = 1;
        DisplayCpu->CoresData[CoreIndex].ThreadsData[1].ValidFlags1.ThreadDataVF1Param.CurrentFrequencyValid = 1;
    }
}


/**

  ELINK that can update DIMM Inventory Data in System Inventory Info Protocol.

    @param [in][out] SystemInventoryProtocol        Pointer to System Inventory Protocol Instance
    @param [in][out] DeviceEntry                    Pointer to CPU Device Data

  @retval VOID    

**/

VOID
OemUpdateDimmInventoryData (
  IN OUT  SYSTEM_INVENTORY_INFO_PROTOCOL      *SystemInventoryInfoProtocol,
  IN  DEV_ENTRY                 *DeviceEntry
  )
{
    // This Function can be implemented to UPDATE DIMM Inventory data
    // in System Inventory Info Protocol.
    
    /// Note: This Function will be called for Every DIMM Slot 
    /// in Platform. DIMM of Interest can be differentiated
    /// with ONE of the below Key Identifiers.
    
    /// Key Identifiers:
    /// 1. DIMM Location (Socket Number, IMC Number, Channel Number, DIMM Number).
    
    /// Key Identifiers is a suggestive List. OEMs are open to choose their OWN Identifiers otherwise.

    EFI_STATUS                      Status = EFI_SUCCESS;
    EFI_SMBIOS_HANDLE               SmbiosHandle;
    EFI_SMBIOS_PROTOCOL             *gSmbiosProtocol;
    EFI_SMBIOS_TYPE                 Type;
    AMD_SMBIOS_TABLE_TYPE17         *Type17Structure = NULL;

    UINT8                           Socket, Channel;

    DEBUG((DEBUG_ERROR, "%a() - ENTRY \n", __FUNCTION__));

    Socket = DeviceEntry->DisplayPtr.Dimm.MemoryLocation.Socket;
    Channel = DeviceEntry->DisplayPtr.Dimm.MemoryLocation.Channel;

    Status = gBS->LocateProtocol (&gEfiSmbiosProtocolGuid, NULL, (VOID **) &gSmbiosProtocol);
    if (EFI_ERROR (Status)) {
        return;
    }
    
    SmbiosHandle = AMD_SMBIOS_HANDLE_PI_RESERVED;
    Type = EFI_SMBIOS_TYPE_MEMORY_DEVICE;

    while (TRUE) {

        Status = gSmbiosProtocol->GetNext (gSmbiosProtocol, &SmbiosHandle, &Type, (EFI_SMBIOS_TABLE_HEADER **)&Type17Structure, NULL);
        if (EFI_ERROR(Status)) {
            break;
        }

    if (Type17Structure->Size != 0) {
        if (DeviceEntry->Dp.DeviceStatus.DeviceState == SysInvDevEnabled) {

            DeviceEntry->DisplayPtr.Dimm.MemoryMedia[0] = SysInvMemoryMediaDRAM;
            DeviceEntry->DisplayPtr.Dimm.MemoryMediaValidArrayElementsCount = 1;
    
            switch (Channel) {
            // DIMMA
            case 0: 
                DeviceEntry->DisplayPtr.Dimm.MemoryLocation.MemoryController = 3; //UMC3
                break;
            // DIMMB
            case 1:
                DeviceEntry->DisplayPtr.Dimm.MemoryLocation.MemoryController = 4; //UMC4
                break;
            // DIMMC
            case 2:
                DeviceEntry->DisplayPtr.Dimm.MemoryLocation.MemoryController = 0; //UMC0
                break;
            // DIMMD
            case 3:
                DeviceEntry->DisplayPtr.Dimm.MemoryLocation.MemoryController = 5; //UMC5
                break;
            // DIMME
            case 4:
                DeviceEntry->DisplayPtr.Dimm.MemoryLocation.MemoryController = 1; //UMC1
                break;
            // DIMMF
            case 5:
                DeviceEntry->DisplayPtr.Dimm.MemoryLocation.MemoryController = 2; //UMC2
                break;
            // DIMMG
            case 6:
                DeviceEntry->DisplayPtr.Dimm.MemoryLocation.MemoryController = 9; //UMC9
                break;
            // DIMMH
            case 7: 
                DeviceEntry->DisplayPtr.Dimm.MemoryLocation.MemoryController = 10; //UMC10
                break;
            // DIMMI
            case 8:
                DeviceEntry->DisplayPtr.Dimm.MemoryLocation.MemoryController = 6; //UMC6
                break;
            // DIMMJ
            case 9:
                DeviceEntry->DisplayPtr.Dimm.MemoryLocation.MemoryController = 11; //UMC11
                break;
            // DIMMK
            case 10:
                DeviceEntry->DisplayPtr.Dimm.MemoryLocation.MemoryController = 7; //UMC7
                break;
            // DIMML
            case 11:
                DeviceEntry->DisplayPtr.Dimm.MemoryLocation.MemoryController = 8; //UMC8
                break;
            default:
                break;
            }

            DeviceEntry->DisplayPtr.Dimm.MemoryLocation.ValidFlags1.MemLocVF1Param.MemoryControllerValid = TRUE;

            DeviceEntry->DisplayPtr.Dimm.NonVolatileSizeMiB = 0;
            DeviceEntry->DisplayPtr.Dimm.Flags1.DimmInfoVF1param.NonVolatileSizeMiBValid = TRUE;
    
            DeviceEntry->DisplayPtr.Dimm.MemoryType = MemoryTechnologyDram;
            DeviceEntry->DisplayPtr.Dimm.Flags1.DimmInfoVF1param.MemoryTypeValid = TRUE;

            DeviceEntry->DisplayPtr.Dimm.OperatingMemoryModes[0] = SysInvOperatingMemoryModesVolatile;
            DeviceEntry->DisplayPtr.Dimm.OperatingMemoryModesValidArrayElementsCount = 1;

            DeviceEntry->DisplayPtr.Dimm.VolatileSizeMiB = DeviceEntry->DisplayPtr.Dimm.CapacityMiB;
            DeviceEntry->DisplayPtr.Dimm.Flags1.DimmInfoVF1param.VolatileSizeMiBValid = TRUE;
        
            DeviceEntry->DisplayPtr.Dimm.CacheSizeMiB = 0;
            DeviceEntry->DisplayPtr.Dimm.Flags1.DimmInfoVF1param.CacheSizeMiBValid = TRUE;
        
            DeviceEntry->DisplayPtr.Dimm.PersistentRegionNumberLimit = 0;
            DeviceEntry->DisplayPtr.Dimm.Flags1.DimmInfoVF1param.PersistentRegionNumberLimitValid = TRUE;
    
            DeviceEntry->DisplayPtr.Dimm.PersistentRegionSizeLimitMiB = 0;
            DeviceEntry->DisplayPtr.Dimm.Flags1.DimmInfoVF1param.PersistentRegionSizeLimitMiBValid = TRUE;
    
            DeviceEntry->DisplayPtr.Dimm.PersistentRegionSizeMaxMiB = 0;
            DeviceEntry->DisplayPtr.Dimm.Flags1.DimmInfoVF1param.PersistentRegionSizeMaxMiBValid = TRUE;
    
            DeviceEntry->DisplayPtr.Dimm.VolatileRegionNumberLimit = 1;
            DeviceEntry->DisplayPtr.Dimm.Flags1.DimmInfoVF1param.VolatileRegionNumberLimitValid = TRUE;
    
            DeviceEntry->DisplayPtr.Dimm.VolatileRegionSizeLimitMiB = DeviceEntry->DisplayPtr.Dimm.VolatileSizeMiB;
            DeviceEntry->DisplayPtr.Dimm.Flags1.DimmInfoVF1param.VolatileRegionSizeLimitMiBValid = TRUE;
    
            DeviceEntry->DisplayPtr.Dimm.VolatileRegionSizeMaxMiB = DeviceEntry->DisplayPtr.Dimm.VolatileSizeMiB;
            DeviceEntry->DisplayPtr.Dimm.Flags1.DimmInfoVF1param.VolatileRegionSizeMaxMiBValid = TRUE;

    		} 
		}
	}
    return;
}

/**

  ELINK that can update Memory Domain Inventory Data in System Inventory Info Protocol.

  @param VOID   SystemInventoryInfoProtocol  - Pointer to System Inventory Info Protocol Data Structure.

  @retval VOID    

**/

VOID
OemUpdateMemoryDomainInventoryData (
  IN     SYSTEM_INVENTORY_INFO_PROTOCOL                 *SystemInventoryInfoProtocol
  )
{
    //
    // Initialize System Memory (DDR5) Domains..
    //
    InitializeSystemMemoryDomains (SystemInventoryInfoProtocol);

    //
    // Initialize Non-Volatile Memory Domains ..
    //
    //InitializeNonVolatileMemoryDomains (SystemInventoryInfoProtocol);
    
    return;
}


/**

  ELINK that updates System Inventory Info Protocol Data.

  @param VOID   SystemInventoryProtocol  - Pointer to System Inventory Info Protocol Structure.

  @retval VOID    

**/

VOID
OemUpdateMediaControllerInventoryData (
  IN     SYSTEM_INVENTORY_INFO_PROTOCOL                 *SystemInventoryProtocol
  ) 
{
    // This Function can be implemented to update Media controller Data in
    // System Inventory Info Protocol. 
    
    
    
    return;
}

/**

  ELINK that can update Virtual Network Inventory Data in System Inventory Info Protocol.

  @param VOID   DeviceEntry  - Pointer to Virtual Network Device Inventory Data Structure.

  @retval VOID    

**/

VOID
OemUpdateVirtualNetworkDeviceInventoryData (
  IN  DEV_ENTRY                 *DeviceEntry
  )
{
    // This Function can be implemented to UPDATE Virtual Network Devices Inventory data
    // in System Inventory Info Protocol.
    
    /// Note: This Function will be called for Every Virtual Network Device 
    /// in Platform. Virtual Network Device of Interest can be differentiated
    /// with ONE of the below Key Identifiers.
    
    /// Key Identifiers:
    /// 1. Vendor ID / Device ID.
    /// 1. PCI Location (Segment#, Bus#, Dev#, Function#).
    /// 2. UEFI Device Path.
    
    /// Key Identifiers is a suggestive List. OEMs are open to choose their OWN Identifiers otherwise.
    
    return;
}

/**

  ELINK that can update Network Device Inventory Data in System Inventory Info Protocol.

  @param VOID   DeviceEntry  - Pointer to Network Device Inventory Data Structure.

  @retval VOID    

**/

VOID
OemUpdateNetworkDeviceInventoryData (
  IN  DEV_ENTRY                 *DeviceEntry
  )
{
    // This Function can be implemented to UPDATE Network Device Inventory data
    // in System Inventory Info Protocol.
    
    /// Note: This Function will be called for Every Network Device 
    /// in Platform. Network Device of Interest can be differentiated
    /// with ONE of the below Key Identifiers.
    
    /// Key Identifiers:
    /// 1. Vendor ID / Device ID of Network Device.
    /// 1. PCI Location of Network Device (Segment#, Bus#, Dev#, Function#).
    /// 2. UEFI Device Path.
    
    /// Key Identifiers is a suggestive List. OEMs are open to choose their OWN Identifiers otherwise.
    
    return;
}

/**

  ELINK that can update Chassis PCIe Slot Inventory Data in System Inventory Info Protocol.

  @param VOID   DeviceEntry  - Pointer to Chassis PCIe Slot Inventory Data Structure.

  @retval VOID    

**/

VOID
OemUpdateChassisPcieSlotInventoryData (
  IN  DEV_ENTRY                 *DeviceEntry
  )
{
    // This Function can be implemented to UPDATE Chassis PCIe Slot Inventory data
    // in System Inventory Info Protocol.
    
    /// Note: This Function will be called for Every Chassis PCIe Slot 
    /// in Platform. PCIe Slot of Interest can be differentiated
    /// with ONE of the below Key Identifiers.
    
    /// Key Identifiers:
    /// 1. Slot ID of PCIe Slot of Platform.
    /// 2. UEFI Device Path.
    
    /// Key Identifiers is a suggestive List. OEMs are open to choose their OWN Identifiers otherwise.
    
    return;
}

/**

  ELINK that can update PCIe Slot Device Inventory Data in System Inventory Info Protocol.

  @param VOID   DeviceEntry  - Pointer to PCIe Slot Device Inventory Data Structure.

  @retval VOID    

**/

VOID
OemUpdatePcieSlotInventoryData (
  IN  DEV_ENTRY                 *DeviceEntry
  )
{
    // This Function can be implemented to UPDATE PCIe Slot Devices Inventory data
    // in System Inventory Info Protocol.
    
    /// Note: This Function will be called for Every PCIe Slot  
    /// in Platform. PCIe Slot Device of Interest can be differentiated
    /// with ONE of the below Key Identifiers.
    
    /// Key Identifiers:
    /// 1. Slot ID of PCIe Slot of Platform.
    /// 2. Vendor ID / Device ID of PCIe Device.
    /// 3. PCI Location of PCIe Device (Segment#, Bus#, Dev#, Function#).
    /// 4. UEFI Device Path.
    /// 5. ASL Name of Device declared through SDL.
    
    /// Key Identifiers is a suggestive List. OEMs are open to choose their OWN Identifiers otherwise.
    
    return;
}

/**

  ELINK that can update PCIe On-Board Device Inventory Data in System Inventory Info Protocol.

  @param VOID   DeviceEntry  - Pointer to PCIe On-Board Device Inventory Data Structure.

  @retval VOID    

**/

VOID
OemUpdateOnBoardPcieInventoryData (
  IN  DEV_ENTRY                 *DeviceEntry
  )
{
    // This Function can be implemented to UPDATE On-Board PCIe Devices Inventory data
    // in System Inventory Info Protocol.
    
    /// Note: This Function will be called for Every On Board PCIe Device
    /// in Platform. PCIe On-Board Device of Interest can be differentiated
    /// with ONE of the below Key Identifiers.
    
    /// Key Identifiers:
    /// 1. Vendor ID / Device ID of Device.
    /// 2. PCI Location of Device (Segment#, Bus#, Dev#, Function#).
    /// 3. UEFI Device Path.
    /// 4. ASL Name of Device declared through SDL.
    
    /// Key Identifiers is a suggestive List. OEMs are open to choose their OWN Identifiers otherwise.
    
    return;
}

/**

  ELINK that can update ATA Storage Controller Inventory Data in System Inventory Info Protocol.

  @param VOID   DeviceEntry  - Pointer to ATA Storage Controller Inventory Data Structure.

  @retval VOID    

**/

VOID
OemUpdateAtaStorageControllerInventoryData (
  IN  DEV_ENTRY                 *DeviceEntry
  )
{
    // This Function can be implemented to UPDATE Ata Storage Controller Inventory data
    // in System Inventory Info Protocol.
    
    /// Note: This Function will be called for Every ATA Storage Controller
    /// in Platform. ATA Storage Controller of Interest can be differentiated
    /// with ONE of the below Key Identifiers.
    
    /// Key Identifiers:
    /// 1. Vendor ID / Device ID of Device.
    /// 2. PCI Location of Device (Segment#, Bus#, Dev#, Function#).
    /// 3. UEFI Device Path.
    /// 4. ASL Name of Device declared through SDL.
    
    /// Key Identifiers is a suggestive List. OEMs are open to choose their OWN Identifiers otherwise.
    
    return;
}

/**

  ELINK that can update ATA Storage Device Inventory Data in System Inventory Info Protocol.

  @param VOID   DeviceEntry  - Pointer to ATA Storage Device Inventory Data Structure.

  @retval VOID    

**/

VOID
OemUpdateAtaStorageDeviceInventoryData (
  IN  DEV_ENTRY                 *DeviceEntry
  )
{
    // This Function can be implemented to UPDATE Ata Storage Device Inventory data
    // in System Inventory Info Protocol.
    
    /// Note: This Function will be called for Every ATA Storage Device/Port
    /// in Platform. ATA Storage Device/Port of Interest can be differentiated
    /// with ONE of the below Key Identifiers.
    
    /// Key Identifiers:
    /// 1. UEFI Device Path.
    
    /// Key Identifiers is a suggestive List. OEMs are open to choose their OWN Identifiers otherwise.
    
    return;
}

/**

  ELINK that can update NVMe Storage Controller Inventory Data in System Inventory Info Protocol.

  @param VOID   DeviceEntry  - Pointer to NVMe Storage Controller Inventory Data Structure.

  @retval VOID    

**/

VOID
OemUpdateNvmeStorageControllerInventoryData (
  IN  DEV_ENTRY                 *DeviceEntry
  )
{
    // This Function can be implemented to UPDATE NVMe Storage Controller Inventory data
    // in System Inventory Info Protocol.
    
    /// Note: This Function will be called for Every NVMe Storage Controller
    /// in Platform. NVMe Storage Controller of Interest can be differentiated
    /// with ONE of the below Key Identifiers.
    
    /// Key Identifiers:
    /// 1. Vendor ID / Device ID of Device.
    /// 2. PCI Location of Device (Segment#, Bus#, Dev#, Function#).
    /// 3. UEFI Device Path.
    /// 4. ASL Name of Device declared through SDL.
    
    /// Key Identifiers is a suggestive List. OEMs are open to choose their OWN Identifiers otherwise.
    
    return;
}

/**

  ELINK that can update NVMe Storage Device Inventory Data in System Inventory Info Protocol.

  @param VOID   DeviceEntry  - Pointer to NVMe Storage Device Inventory Data Structure.

  @retval VOID    

**/

VOID
OemUpdateNvmeStorageDeviceInventoryData (
  IN  DEV_ENTRY                 *DeviceEntry
  )
{
    // This Function can be implemented to UPDATE NVMe Storage Device Inventory data
    // in System Inventory Info Protocol.
    
    /// Note: This Function will be called for Every NVMe Storage Device
    /// in Platform. NVMe Storage Device of Interest can be differentiated
    /// with ONE of the below Key Identifiers.
    
    /// Key Identifiers:
    /// 1. UEFI Device Path.
    
    /// Key Identifiers is a suggestive List. OEMs are open to choose their OWN Identifiers otherwise.
    
    return;
}

/**

  ELINK that can update USB Storage Controller Inventory Data in System Inventory Info Protocol.

  @param VOID   DeviceEntry  - Pointer to USB Storage Controller Inventory Data Structure.

  @retval VOID    

**/

VOID
OemUpdateUsbStorageControllerInventoryData (
  IN  DEV_ENTRY                 *DeviceEntry
  )
{
    // This Function can be implemented to UPDATE USB Storage Controller Inventory data
    // in System Inventory Info Protocol.
    
    /// Note: This Function will be called for Every USB Storage Controller
    /// in Platform. USB Storage Controller of Interest can be differentiated
    /// with ONE of the below Key Identifiers.
    
    /// Key Identifiers:
    /// 1. Vendor ID / Device ID of Device.
    /// 2. PCI Location of Device (Segment#, Bus#, Dev#, Function#).
    /// 3. UEFI Device Path.
    /// 4. ASL Name of Device declared through SDL.
    
    /// Key Identifiers is a suggestive List. OEMs are open to choose their OWN Identifiers otherwise.
    
    return;
}

/**

  ELINK that can update USB Storage Device Inventory Data in System Inventory Info Protocol.

  @param VOID   DeviceEntry  - Pointer to USB Storage Device Inventory Data Structure.

  @retval VOID    

**/

VOID
OemUpdateUsbStorageDeviceInventoryData (
  IN  DEV_ENTRY                 *DeviceEntry
  )
{
    // This Function can be implemented to UPDATE USB Storage Device Inventory data
    // in System Inventory Info Protocol.
    
    /// Note: This Function will be called for Every USB Storage Device
    /// in Platform. USB Storage Device of Interest can be differentiated
    /// with ONE of the below Key Identifiers.
    
    /// Key Identifiers:
    /// 1. UEFI Device Path.
    
    /// Key Identifiers is a suggestive List. OEMs are open to choose their OWN Identifiers otherwise.
    
    return;
}

VOID
OemUpdateScsiStorageControllerInventoryData (
  IN  DEV_ENTRY                 *DeviceEntry
  )
{
    // This Function can be implemented to UPDATE SCSI Storage Controller Inventory data
    // in System Inventory Info Protocol.
    
    /// Note: This Function will be called for Every SCSI Storage Controller
    /// in Platform. SCSI Storage Controller of Interest can be differentiated
    /// with ONE of the below Key Identifiers.
    
    /// Key Identifiers:
    /// 1. Vendor ID / Device ID of Device.
    /// 2. PCI Location of Device (Segment#, Bus#, Dev#, Function#).
    /// 3. UEFI Device Path.
    /// 4. ASL Name of Device declared through SDL.
    
    /// Key Identifiers is a suggestive List. OEMs are open to choose their OWN Identifiers otherwise.
    
    return;
}

/**

  ELINK that can update SCSI Storage Device Inventory Data in System Inventory Info Protocol.

  @param VOID   DeviceEntry  - Pointer to SCSI Storage Device Inventory Data Structure.

  @retval VOID    

**/

VOID
OemUpdateScsiStorageDeviceInventoryData (
  IN  DEV_ENTRY                 *DeviceEntry
  )
{
    // This Function can be implemented to UPDATE SCSI Storage Device Inventory data
    // in System Inventory Info Protocol.
    
    /// Note: This Function will be called for Every SCSI Storage Device
    /// in Platform. SCSI Storage Device of Interest can be differentiated
    /// with ONE of the below Key Identifiers.
    
    /// Key Identifiers:
    /// 1. UEFI Device Path.
    
    /// Key Identifiers is a suggestive List. OEMs are open to choose their OWN Identifiers otherwise.
    
    return;
}

/**

  ELINK that can update Storage Volume Inventory Data in System Inventory Info Protocol.

  @param VOID   DeviceEntry  - Pointer to Storage Volume Inventory Data Structure.

  @retval VOID    

**/

VOID
OemUpdateStorageVolumeInventoryData (
  IN  DEV_ENTRY                 *DeviceEntry
  )
{
    // This Function can be implemented to UPDATE Storage Volume Inventory data
    // in System Inventory Info Protocol.
    
    /// Note: This Function will be called for Every Storage Volume 
    /// in Platform. Storage Volume of Interest can be differentiated
    /// with ONE of the below Key Identifiers.
    
    /// Key Identifiers:
    /// 1. UEFI Device Path.
    
    /// Key Identifiers is a suggestive List. OEMs are open to choose their OWN Identifiers otherwise.
    
    return;
}

/**

  ELINK that can update Storage Unit Inventory Data in System Inventory Info Protocol.

  @param VOID   DeviceEntry  - Pointer to Storage Volume Inventory Data Structure.

  @retval VOID    

**/

VOID
OemUpdateStorageUnitInventoryData (
  IN  DEV_ENTRY                 *DeviceEntry
  )
{
    // This Function can be implemented to UPDATE Storage Unit Inventory data
    // in System Inventory Info Protocol.
    
    /// Note: This Function will be called for Every Storage Unit 
    /// in Platform. Storage Unit of Interest can be differentiated
    /// with ONE of the below Key Identifiers.
    
    /// Key Identifiers:
    /// 1. UEFI Device Path.
    
    /// Key Identifiers is a suggestive List. OEMs are open to choose their OWN Identifiers otherwise.
    
    return;
}

VOID
OemUpdatePortDeviceInventoryData (
  IN  DEV_ENTRY                               *DeviceEntry
  )
{
    // This Function can be implemented to UPDATE Port Inventory data
    // in System Inventory Info Protocol.
    
    /// Note: This Function will be called for Every Port
    /// in Platform. Port of Interest can be differentiated
    /// with ONE of the below Key Identifiers.
    
    /// Key Identifiers:
    /// 1. UEFI Device Path.
    
    /// Key Identifiers is a suggestive List. OEMs are open to choose their OWN Identifiers otherwise.
    
    return;
}

VOID
OemUpdateUsbControllerInventoryData (
  IN  DEV_ENTRY                               *DeviceEntry
  )
{
    // This Function can be implemented to UPDATE Usb controller Inventory data
    // in System Inventory Info Protocol.
    
    /// Note: This Function will be called for Every Usb controller 
    /// in Platform. Usb controller of Interest can be differentiated
    /// with ONE of the below Key Identifiers.
    
    /// Key Identifiers:
    /// 1. UEFI Device Path.
    
    /// Key Identifiers is a suggestive List. OEMs are open to choose their OWN Identifiers otherwise.
    
    return;
}

VOID
OemUpdateEthernetInterfaceInventoryData (
  IN  DEV_ENTRY                               *DeviceEntry
  )
{
    // This Function can be implemented to UPDATE Network Device Inventory data
    // in System Inventory Info Protocol.
    
    /// Note: This Function will be called for Every Network Device 
    /// in Platform. Network Device of Interest can be differentiated
    /// with ONE of the below Key Identifiers.
    
    /// Key Identifiers:
    /// 1. Vendor ID / Device ID of Network Device.
    /// 1. PCI Location of Network Device (Segment#, Bus#, Dev#, Function#).
    /// 2. UEFI Device Path.
    
    /// Key Identifiers is a suggestive List. OEMs are open to choose their OWN Identifiers otherwise.
    
    return;
}

VOID
OemUpdateNetworkDevFunInventoryData (
  IN  DEV_ENTRY                               *DeviceEntry
  )
{
    // This Function can be implemented to UPDATE Network Device Inventory data
    // in System Inventory Info Protocol.
    
    /// Note: This Function will be called for Every Network Device 
    /// in Platform. Network Device Function of Interest can be differentiated
    /// with ONE of the below Key Identifiers.
    
    /// Key Identifiers:
    /// 1. Vendor ID / Device ID of Network Device.
    /// 1. PCI Location of Network Device (Segment#, Bus#, Dev#, Function#).
    /// 2. UEFI Device Path.
    
    /// Key Identifiers is a suggestive List. OEMs are open to choose their OWN Identifiers otherwise.
    
    return;
}

VOID
OemUpdateGraphicsControllerInventoryData (
  )
{
    // This Function can be implemented to UPDATE Graphics controller Inventory data
    // in System Inventory Info Protocol.
    
    /// Note: This Function will be called for Every Graphics controller 
    /// in Platform. Graphics controller of Interest can be differentiated
    /// with ONE of the below Key Identifiers.
    
    /// Key Identifiers:
    /// 1. UEFI Device Path.
    
    /// Key Identifiers is a suggestive List. OEMs are open to choose their OWN Identifiers otherwise.
    
    return;
}

VOID
OemUpdatePrerequisiteData (
  )
{
    // This Function can be implemented to update any Data in System Inventory Info 
    // Protocol. 

    return;
}
