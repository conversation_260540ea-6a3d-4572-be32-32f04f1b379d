TOKEN
	Name  = RimmTools_SUPPORT
	Value  = 1
	TokenType = Boolean
	Master = Yes
	Help  = "Main switch to enable RimmTools support in Project"
End

TOKEN
	Name  = "XMLRIMM"
	Value  = "$(RimmTools_DIR)$(PATH_SLASH)XmlTcgRim.exe "
	TokenType = Expression
	TargetMAK = Yes
End

TOKEN
	Name  = "XMLRIMM"
	Value  = "$(RimmTools_DIR)$(PATH_SLASH)XmlTcgRim.exe -@"
	TokenType = Expression
	TargetMAK = Yes
	Token = "SILENT" "=" "1"
End

TOKEN
	Name  = "XMLSIG"
	Value  = "$(RimmTools_DIR)$(PATH_SLASH)XmlSig.exe"
	TokenType = Expression
	TargetMAK = Yes
End

TOKEN
	Name  = "XMLSIG"
	Value  = "$(RimmTools_DIR)$(PATH_SLASH)XmlSig.exe -@ "
	TokenType = Expression
	TargetMAK = Yes
	Token = "SILENT" "=" "1"
End

TOKEN
	Name  = "XMLCRYPTCON"
	Value  = "$(RimmTools_DIR)$(PATH_SLASH)CryptoCon.exe "
	TokenType = Expression
	TargetMAK = Yes
	Help = "Add switch -@ for Silent operation"
End

TOKEN
	Name  = "XMLCRYPTCON"
	Value  = "$(RimmTools_DIR)$(PATH_SLASH)CryptoCon.exe -@"
	TokenType = Expression
	TargetMAK = Yes
	Help = "Add switch -@ for Silent operation"
	Token = "SILENT" "=" "1"
End

PATH
	Name  = "RimmTools_DIR"
End
