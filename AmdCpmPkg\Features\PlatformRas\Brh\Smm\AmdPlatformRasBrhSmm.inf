#;*****************************************************************************
#;
#; Copyright (C) 2018-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = AmdPlatformRasBrhSmm
  FILE_GUID                      = 085A786C-5CFF-4762-81CF-D7CBFECA77D4
  MODULE_TYPE                    = DXE_SMM_DRIVER
  PI_SPECIFICATION_VERSION       = 0x0001000A
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = AmdPlatformRasBrhSmmInit


[Sources]
  AmdPlatformRasBrhSmm.c
  AmdPlatformRasBrhSmm.h
  MceSwSmi.c
  EinjSwSmi.c
  PciRasSmi.c
  PciLegacyRasSmi.c
  EdrSwSmi.c
  RasSmiErrHdlrHelper.c
  CxlRasSmi.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleCcxPkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/AgesaModuleRasPkg.dec
  AgesaPkg/AgesaPkg.dec
  AmdCpmPkg/AmdCpmPkg.dec
  AgesaModulePkg/Fch/Kunlun/FchKunlun.dec
  AmdCbsPkg/AmdCbsPkg.dec
  MdeModulePkg/MdeModulePkg.dec
  AgesaModulePkg/AgesaModulePspPkg.dec
  AgesaModulePkg/AgesaModuleNbioPkg.dec
  AgesaModulePkg/AgesaModuleMemPkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  IoLib
  UefiDriverEntryPoint
  SmmServicesTableLib
  DebugLib
  UefiRuntimeServicesTableLib
  UefiBootServicesTableLib
  MemoryAllocationLib
  BaseFabricTopologyLib
  SmnAccessLib
  FabricRegisterAccLib
  CpmRasPciLib
  CpmRasAcpiLib
  AmdPspMboxLibV2
  NbioSmuBrhLib
  NbioHandleLib
  CpmRasProcLib
  CpmRasMemLib
  CpmRasCxlLib
  RasSmmLib
  AgesaConfigLib
  MpioLib
  PciSegmentLib
  RasIdsLib
  TimerLib
  PcdLib
  MemRestoreLib
  SmmMemLib

[Guids]
  gCbsSystemConfigurationGuid               #CONSUMED

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugHandlingMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspEinjSupport
  gAmdCpmPkgTokenSpaceGuid.PcdCpmRasEinjMode
  gAmdCpmPkgTokenSpaceGuid.PcdCpmOscSwSmiId

[FixedPcd]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdMemMaxSocketSupportedV2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdMemMaxDiePerSocketV2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdMemMaxChannelPerDieV2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdMemMaxDimmPerChannelV2

[Protocols]
  gFchSmmSwDispatch2ProtocolGuid            #CONSUMED
  gEfiSmmBase2ProtocolGuid                  #CONSUMED
  gEfiSmmCpuProtocolGuid                    #CONSUMED
  gAmdRasServiceSmmProtocolGuid             #CONSUMED
  gAmdPlatformApeiDataProtocolGuid          #CONSUMED
  gFchSmmApuRasDispatchProtocolGuid         #CONSUMED
  gEfiSmmVariableProtocolGuid               #CONSUMED
  gAmdApcbSmmServiceProtocolGuid            #CONSUMED
  gFchSmmPeriodicalDispatch2ProtocolGuid    #CONSUMED
  gFchSmmMiscDispatchProtocolGuid           #CONSUMED
  gPspMboxSmmBufferAddressProtocolGuid      #CONSUMED
  gAmdPspArsServiceProtocolGuid             #CONSUMED
  gRasMaintPcieActivePortMapProtocolGuid    #PRODUCED
  gEdkiiSmmReadyToBootProtocolGuid          #CONSUMED
  gAmdAsyncHpEdrProtocolGuid                #PRODUCED
  gAmdCpmRasOemSmmProtocolGuid              #CONSUMED
  gAmdPostPackageRepairInfoProtocolGuid     #CONSUMED
  gEfiSmmEndOfDxeProtocolGuid               #CONSUMED
  gAmdFabricTopologyServices2SmmProtocolGuid #CONSUMED
  gAmdPspRuntimePprServiceProtocolGuid      #CONSUMED

[Depex]
  gEfiSmmBase2ProtocolGuid AND
  gFchSmmSwDispatch2ProtocolGuid AND
  gFchSmmApuRasDispatchProtocolGuid AND
  gEfiSmmCpuProtocolGuid AND
  gAmdRasServiceSmmProtocolGuid AND
  gAmdPlatformApeiDataProtocolGuid AND
  gPspMboxSmmBufferAddressProtocolGuid AND
  gAmdRasBrhDepexProtocolGuid AND
  gPspFlashAccSmmCommReadyProtocolGuid

