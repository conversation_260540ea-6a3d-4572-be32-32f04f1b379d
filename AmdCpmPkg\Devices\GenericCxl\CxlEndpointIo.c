/** @file

Cxl Endpoint I/O Handler.

**/
/******************************************************************************
 * Copyright (C) 2022-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 ***************************************************************************/

#include "CxlEndpointDriver.h"
#include "CxlEndpointCci.h"
#include "CxlQosTelemetry.h"
#include "CxlEndpointCsl.h"
#include <AMD.h>
#include <Library/BaseLib.h>
#include <Library/TimerLib.h>
#include <Protocol/AmdCxlServicesProtocol.h>


#define CXL_DEVICE_STATUS_FATAL                 0x01
#define CXL_DEVICE_STATUS_FW_HALT               0x02
#define CXL_DEVICE_STATUS_COLD_RESET_NEEDED     0x20
#define CXL_DEVICE_STATUS_WARM_RESET_NEEDED     0x40
#define CXL_DEVICE_STATUS_HOT_RESET_NEEDED      0x60
#define CXL_DEVICE_STATUS_CXL_RESET_NEEDED      0x80
#define CXL_MEDIA_STATUS_NOT_READY              0x00
#define CXL_MEDIA_STATUS_READY                  0x04
#define CXL_MEDIA_STATUS_ERROR                  0x08
#define CXL_MEDIA_STATUS_DISABLED               0x0C
#define CXL_MB_INTERFACE_READY                  0x10
#define CXL_DEVICE_STATUS_MEDIA_STATUS_MASK     0x0C
#define CXL_DEVICE_STATUS_RESET_NEEDED_MASK     0xE0
#define ABL_MIN_BASE_ADDRESS_ALIGNMENT          SIZE_256MB
#define CXL_HDM_DECODER_CAPABILITY_OFFSET       0x0
#define CXL_HDM_DECODER_GLOBAL_CONTROL_OFFSET   0x4
#define CXL_HDM_DECODER_BASE_LOW_OFFSET         0x10
#define CXL_HDM_DECODER_BASE_HIGH_OFFSET        0x14
#define CXL_HDM_DECODER_SIZE_LOW_OFFSET         0x18
#define CXL_HDM_DECODER_SIZE_HIGH_OFFSET        0x1C
#define CXL_HDM_DECODER_CONTROL_OFFSET          0x20
#define CXL_HDM_DECODER_DPA_SKIP_LOW_OFFSET     0x24
#define CXL_HDM_DECODER_DPA_SKIP_HIGH_OFFSET    0x28
#define CXL_HDM_DECODER_TARGET_LIST_LOW_OFFSET  0x24
#define CXL_HDM_DECODER_TARGET_LIST_HIGH_OFFSET 0x28
// Decoder 0 registers 0x10-0x28
// Decoder 1 registers 0x30-0x4F
// Decoder n registers (0x20*n + 0x10):(0x20*n + 0x2F)
#define MAX_HDM_DECODERS_PER_ENDPOINT           10

// Encoded NUmber of Interleave Ways (ENIW)
#define INTERLEAVE_1_WAY                        0
#define INTERLEAVE_2_WAY                        1
#define INTERLEAVE_4_WAY                        2
#define INTERLEAVE_8_WAY                        3
#define INTERLEAVE_16_WAY                       4
#define INTERLEAVE_3_WAY                        8
#define INTERLEAVE_6_WAY                        9
#define INTERLEAVE_12_WAY                       10

// Encoded Interleave Granularity (IG)
#define INTERLEAVE_GRANULARITY_256B             0
#define INTERLEAVE_GRANULARITY_512B             1
#define INTERLEAVE_GRANULARITY_1KB              2
#define INTERLEAVE_GRANULARITY_2KB              3
#define INTERLEAVE_GRANULARITY_4KB              4
#define INTERLEAVE_GRANULARITY_8KB              5
#define INTERLEAVE_GRANULARITY_16KB             6

// CXL memory types
typedef enum {
  CxlConventionalMemory,
  CxlPersistentMemory,
  CxlCache,
  CxlDualMode
} CXL_MEM_TYPE;

typedef struct {
  UINT8 DecoderCount;
  UINT8 TargetCount;
  UINT8 Interleave11to8;
  UINT8 Interleave14to12;
  UINT8 PoisonOnDecode;
} AMD_CXL_HDM_DECODER_CAP;

typedef struct {
  UINT32 MemBaseLow;
  UINT32 MemBaseHigh;
  UINT32 MemSizeLow;
  UINT32 MemSizeHigh;
} AMD_CXL_HDM_DECODER;

typedef union {
  struct {
    UINT32 InterleaveGranularity:4;
    UINT32 InterleaveWays:4;
    UINT32 LockOnCommit:1;
    UINT32 Commit:1;
    UINT32 Committed:1;
    UINT32 ErrorNotCommited:1;
    UINT32 TargetRangeType:1;
    UINT32 BI:1;
    UINT32 UIO:1;
    UINT32 Reserved:1;
    UINT32 UpstreamInterleaveGranularity:4;
    UINT32 UpstreamInterleaveWays:4;
    UINT32 InterleaveSetPosition:4;
    UINT32 Reserved2:4;
  } Field;
  UINT32 Value;
} AMD_CXL_HDM_DECODER_CTRL_REG;

typedef union {
  struct {
    UINT8 TargetPortId0;
    UINT8 TargetPortId1;
    UINT8 TargetPortId2;
    UINT8 TargetPortId3;
  } Field;
  UINT32 Value;
} AMD_CXL_HDM_DECODER_TARGET_LIST_REG;


GLOBAL_REMOVE_IF_UNREFERENCED CONST CHAR8 *mComponentIdName[] = {
  "Unsupported",    /* 0x00 */
  "Transport",      /* 0x01 */
  "Protocol",       /* 0x02 */
  "Common",         /* 0x03 */
  "Port",           /* 0x04 */
  "Link",           /* 0x05 */
  "HomeAgent",      /* 0x06 */
  "Unsupported",    /* 0x07 */
  "RequestAgent",   /* 0x08 */
  "Unsupported",    /* 0x09 */
  "SlaveAgent"      /* 0x0A */
};

GLOBAL_REMOVE_IF_UNREFERENCED CONST CHAR8 *mEfiMemoryTypeName[] = {
  "EfiReservedMemoryType",
  "EfiLoaderCode",
  "EfiLoaderData",
  "EfiBootServicesCode",
  "EfiBootServicesData",
  "EfiRuntimeServicesCode",
  "EfiRuntimeServicesData",
  "EfiConventionalMemory",
  "EfiUnusableMemory",
  "EfiACPIReclaimMemory",
  "EfiACPIMemoryNVS",
  "EfiMemoryMappedIO",
  "EfiMemoryMappedIOPortSpace",
  "EfiPalCode",
  "EfiPersistentMemory",
  "EfiMaxMemoryType"
};


/*----------------------------------------------------------------------------------------*/
/**
 * @brief Checks if the endpoint EFI memory type is supported.
 *
 * @param[in] EfiMemType          The endpoint memory type.
 *
 * @return TRUE                   Memory Type is supported.
 * @return FALSE                  Memory Type is not supported.
 */
STATIC
BOOLEAN
CxlEndpointSupportedEfiMemType (
  IN  UINT32  EfiMemType
  )
{
  return ((EfiMemType == EfiConventionalMemory) |
          (EfiMemType == EfiPersistentMemory) |
          (EfiMemType == CxlCache));
}


/**
 * @brief Reads the Memory Device Status Register.
 *
 * @param [in] Endpoint          Pointer to endpoint device structure.
 */
STATIC
VOID
CxlEndpointCheckDeviceStatus (
  IN  CXL_ENDPOINT  *Endpoint
)
{
  EFI_STATUS            Status;
  UINT64                BarStart = 0;
  UINT32                MemoryDeviceStatusOffset;
  UINT16                Timeout;
  UINT8                 DeviceStatus;
  BOOLEAN               PollMediaStatus;
  volatile int          *RegPtr;
  ERROR_LOG_PARAMS      CxlErrorLog;
  AMD_CXL_ERROR_LOG_PROTOCOL *AmdCxlErrorLog = NULL;

  DEBUG ((DEBUG_INFO, "%a - ENTRY\n", __FUNCTION__));

  //Endpoint->CxlCci.MbInterfaceReady = FALSE;

  Status = gBS->LocateProtocol (&gAmdCxlErrorLogProtocolGuid, 
                                NULL, 
                                (VOID **)&AmdCxlErrorLog
                                );
  if (EFI_ERROR (Status)) {
    DEBUG((DEBUG_ERROR, "%a: Failed to locate AmdCxlErrorLogProtocol! Status = %r\n", __FUNCTION__, Status));
  }

  Status = CxlEndpointFindRegisterBlock (
    Endpoint,
    CXL_MEMORY_DEVICE_REGISTERS_RBI,
    &BarStart
    );

  if (EFI_ERROR (Status)) {
    goto ON_EXIT;
  }

  Status = CxlEndpointFindDeviceCapability (
    CXL_MEMORY_DEVICE_STATUS_REGS_CAP_ID,
    BarStart,
    &MemoryDeviceStatusOffset
    );

  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_INFO, "CXL Memory Device Status Registers not found\n"));
    goto ON_EXIT;
  }

  RegPtr = (int *) (BarStart + MemoryDeviceStatusOffset);
  DeviceStatus = (UINT8) *RegPtr;

  if (DeviceStatus & CXL_DEVICE_STATUS_FATAL) {
    DEBUG ((DEBUG_ERROR, "CXL Memory Device Status: Device Fatal!\n"));
    if (AmdCxlErrorLog != NULL) {
      ZeroMem (&CxlErrorLog, sizeof (ERROR_LOG_PARAMS));
      CxlErrorLog.ErrorClass = AMD_ALERT;
      CxlErrorLog.ErrorInfo = CXL_ERROR_LOG_ID | CXL_ENDPOINT_INIT_CONFIG | CXL_MEMORY_DEVICE_ERROR;
      CxlErrorLog.DataParam1 = Endpoint->PciLocation.AsBits.Segment;
      CxlErrorLog.DataParam2 = Endpoint->PciLocation.AsBits.Bus;
      AmdCxlErrorLog->CxlAddErrorLog (AmdCxlErrorLog, &CxlErrorLog);
    }
    goto ON_EXIT;
  }

  if ((DeviceStatus & CXL_DEVICE_STATUS_FW_HALT) == CXL_DEVICE_STATUS_FW_HALT) {
    DEBUG ((DEBUG_ERROR, "CXL Memory Device Status: FW Halt!\n"));
    goto ON_EXIT;
  }

  if ((DeviceStatus & CXL_MB_INTERFACE_READY) == CXL_MB_INTERFACE_READY) {
    DEBUG ((DEBUG_INFO, "CXL Mailbox Interfaces READY\n")); // Poll for MB ready?
    Endpoint->CxlCci.MbInterfaceReady = TRUE;
  } else {
    DEBUG ((DEBUG_INFO, "CXL Mailbox Interfaces NOT Ready\n"));
    if (AmdCxlErrorLog != NULL) {
      ZeroMem (&CxlErrorLog, sizeof (ERROR_LOG_PARAMS));
      CxlErrorLog.ErrorClass = AMD_ALERT;
      CxlErrorLog.ErrorInfo = CXL_ERROR_LOG_ID | CXL_MAILBOX_ERROR | CXL_MAILBOX_INTERFACE_NOT_READY;
      CxlErrorLog.DataParam1 = Endpoint->PciLocation.AsBits.Segment;
      CxlErrorLog.DataParam2 = Endpoint->PciLocation.AsBits.Bus;
      AmdCxlErrorLog->CxlAddErrorLog (AmdCxlErrorLog, &CxlErrorLog);
    }
  }

  PollMediaStatus = FALSE;
  switch (DeviceStatus & CXL_DEVICE_STATUS_MEDIA_STATUS_MASK) {
    case CXL_MEDIA_STATUS_NOT_READY: PollMediaStatus = TRUE; break;
    case CXL_MEDIA_STATUS_READY: DEBUG ((DEBUG_INFO, "Media Status: READY\n")); break;
    case CXL_MEDIA_STATUS_ERROR: DEBUG ((DEBUG_ERROR, "Media Status: ERROR\n")); break;
    case CXL_MEDIA_STATUS_DISABLED: DEBUG ((DEBUG_ERROR, "Media Status: DISABLED\n"));break;
    default: break;
  }
  if (((DeviceStatus & CXL_DEVICE_STATUS_MEDIA_STATUS_MASK) == CXL_MEDIA_STATUS_ERROR) ||
      ((DeviceStatus & CXL_DEVICE_STATUS_MEDIA_STATUS_MASK) == CXL_MEDIA_STATUS_DISABLED)) {
    if (AmdCxlErrorLog != NULL) { 
      ZeroMem (&CxlErrorLog, sizeof (ERROR_LOG_PARAMS));
      CxlErrorLog.ErrorClass = AMD_ALERT;
      CxlErrorLog.ErrorInfo = CXL_ERROR_LOG_ID | CXL_ENDPOINT_INIT_CONFIG | CXL_MEMORY_MEDIA_ERROR;
      CxlErrorLog.DataParam1 = Endpoint->PciLocation.AsBits.Segment;
      CxlErrorLog.DataParam2 = Endpoint->PciLocation.AsBits.Bus;
      AmdCxlErrorLog->CxlAddErrorLog (AmdCxlErrorLog, &CxlErrorLog);
    }
  }
  if (PollMediaStatus) {
    // Poll the media status bits for Media Status Ready 01b
    Timeout = 300;  // Maximum 5 mins delay
    DEBUG ((DEBUG_INFO, "Polling Media Status for Media Ready...\n"));

    do {
      DeviceStatus = (UINT8) *RegPtr;
      if ((DeviceStatus & CXL_MEDIA_STATUS_READY) == CXL_MEDIA_STATUS_READY) {
        DEBUG ((DEBUG_INFO, "\tMedia is READY\n"));
        goto ON_EXIT;
      }
      if ((Timeout % 60) == 0) {
        DEBUG ((DEBUG_INFO, "\n"));
      }
      DEBUG ((DEBUG_INFO, "*"));
      MicroSecondDelay (1000000);
    } while (--Timeout);
  }

  // Media Status may not be ready because a reset is needed
  switch (DeviceStatus & CXL_DEVICE_STATUS_RESET_NEEDED_MASK) {
    case CXL_DEVICE_STATUS_COLD_RESET_NEEDED:
      DEBUG ((DEBUG_ERROR, "CXL Memory Device Status: Cold Reset Needed!\n"));
    break;
    case CXL_DEVICE_STATUS_WARM_RESET_NEEDED:
      DEBUG ((DEBUG_ERROR, "CXL Memory Device Status: Warm Reset Needed!\n"));
    break;
    case CXL_DEVICE_STATUS_HOT_RESET_NEEDED:
      DEBUG ((DEBUG_ERROR, "CXL Memory Device Status: Hot Reset Needed!\n"));
    break;
    case CXL_DEVICE_STATUS_CXL_RESET_NEEDED:
      DEBUG ((DEBUG_ERROR, "CXL Memory Device Status: CXL Reset Needed!\n"));
    break;
    default: break;
  }

ON_EXIT:
  DEBUG((DEBUG_INFO, "%a - EXIT (Status = %r)\n", __FUNCTION__, Status));
  return;
}


/**
 * @brief Adjusts the memory pool size for alignment if needed
 *
 * @param[in] Size
 * @param[in] Alignment
 * @return UINT64 memory pool size
 */
STATIC
UINT64
CxlEndpointAdjustMemPoolSize (
  IN  UINT64  Size,
  IN  UINT64  Alignment
  )
{
  UINT64  AndMask;

  AndMask = Alignment - 1;
  if ((Size & AndMask) != 0ULL) {
    Size = (Size & ~AndMask) + Alignment;
  }
  return Size;
}


/**
 * @brief Enable memory pools at CxlEndpointEnable().
 *
 * @param[in] Endpoint          Endpoint device structure.
 *
 * @return EFI_STATUS
 */
EFI_STATUS
CxlEndpointEnableMemoryPools (
  IN  CXL_ENDPOINT  *Endpoint
  )
{
  AMD_CXL_MEMORY_POOL   *HostMemPool;
  AMD_CXL_MEMORY_POOL   *ApobMemPool;
  UINTN                 *ApobMemPoolCount;
  UINTN                 MemPoolMappedCount;
  EFI_STATUS            Status;
  UINT16                DvsecOffset;
  UINT16                DvsecLength;
  CXL_DVSEC_CAPABILITY  Dvsec;
  UINT8                 MemHwInitMode;
  UINT8                 HdmCount;
  UINTN                 Index;
  UINTN                 Index2;
  UINT32                ClassCode;
  ERROR_LOG_PARAMS      CxlErrorLog;
  AMD_CXL_ERROR_LOG_PROTOCOL *AmdCxlErrorLog = NULL;

  DEBUG ((DEBUG_INFO, "%a - ENTRY\n", __FUNCTION__));

  Status = EFI_SUCCESS;
  MemPoolMappedCount = 0;

  if (Endpoint->IsDcd) {
    goto ON_EXIT;
  }

  Status = gBS->LocateProtocol (&gAmdCxlErrorLogProtocolGuid, 
                                NULL, 
                                (VOID **)&AmdCxlErrorLog
                                );
  if (EFI_ERROR (Status)) {
    DEBUG((DEBUG_ERROR, "%a: Failed to locate AmdCxlErrorLogProtocol! Status = %r\n", __FUNCTION__, Status));
  }

  HostMemPool = &Endpoint->HostMemPool;
  ApobMemPool = &Endpoint->ApobMemPool[0];
  ApobMemPoolCount = &Endpoint->ApobMemPoolCount;

  // Configure Base Address entry for memory pools
  Status = CxlPcieGetCxlDvsec(Endpoint, &DvsecOffset, &DvsecLength, &Dvsec);
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "%a - Error: CxlPcieGetCxlDvsec() Failed! Status = %r\n", __FUNCTION__, Status));
    if (AmdCxlErrorLog != NULL) {
      ZeroMem (&CxlErrorLog, sizeof (ERROR_LOG_PARAMS));
      CxlErrorLog.ErrorClass = AMD_ERROR;
      CxlErrorLog.ErrorInfo = CXL_ERROR_LOG_ID | CXL_ENDPOINT_INIT_CONFIG | CXL_DEVSEC_CAPABILITY_NOT_FOUND;
      CxlErrorLog.DataParam1 = Endpoint->PciLocation.AsBits.Segment;
      CxlErrorLog.DataParam2 = Endpoint->PciLocation.AsBits.Bus;
      AmdCxlErrorLog->CxlAddErrorLog (AmdCxlErrorLog, &CxlErrorLog);
    }
    goto ON_EXIT;
  }

  MemHwInitMode = (Dvsec.CxlCapability & 0x8) >> 3;
  HdmCount = (Dvsec.CxlCapability & 0x30) >> 4;
  DEBUG ((DEBUG_INFO, "Dvsec.CxlCapability = 0x%x, MemHwInitMode = 0x%x, HdmCount = %d\n",
          Dvsec.CxlCapability, MemHwInitMode, HdmCount));

  if (HdmCount != 0) {
    // Get PCI Class Code
    Status = Endpoint->StartPciIo->Pci.Read (
      Endpoint->StartPciIo,
      EfiPciIoWidthUint32,
      PCI_REVISION_ID_OFFSET,
      sizeof (ClassCode) / sizeof (UINT32),
      (VOID *)&ClassCode
      );
    if (EFI_ERROR (Status)) {
      DEBUG ((DEBUG_ERROR, "Error: PciIo->Pci.Read(PCI Class Code) failed! (Status = %r).\n", Status));
      return Status;
    }
    // If the base class is not a memory controller (5) and there is memory, set the memory type to CXL dual mode
    // NOTE: PCI_CLASS_SCC (7) is also checked because a CXL Type 3 device on a frequently used development system advertises base class 7.
    if ((((ClassCode & 0xFF000000) >> 24) != PCI_CLASS_MEMORY_CONTROLLER) &&
        (((ClassCode & 0xFF000000) >> 24) != PCI_CLASS_SCC)) {
      if (Endpoint->MemPool[0].Size != 0) {
        HostMemPool->EfiType = CxlDualMode;
        DEBUG ((DEBUG_INFO, "CXL Memory Pool type set to CXL Dual Mode\n"));
      }
      if (Endpoint->MemPool[1].Size != 0) {
        HostMemPool->EfiType = CxlDualMode;
      }
    }

    // Enable Host memory pool with Cxl Manager
    Status = gCxlMgrProtocol->EnableMemoryPool (
      gCxlMgrProtocol,
      Endpoint->PciLocation,
      HostMemPool,
      ApobMemPool,
      ApobMemPoolCount
      );
    if (EFI_ERROR (Status)) {
      DEBUG ((DEBUG_ERROR, "%a - Error: CxlMgr EnableMemoryPool() Failed! Status = %r\n", __FUNCTION__, Status));
      goto ON_EXIT;
    }

    // If this is a switch endpoint, adjust the memory pool base address
    if ( Endpoint->IsSwitchEndpoint) {
      // Get the switch this endpoint belongs to
      for (Index = 0; Index < gCxlSwitchCount; Index++) {
        for (Index2 = 0; Index2 < gCxlSwitchList[Index].EndpointTotal; Index2++) {
          if (gCxlSwitchList[Index].CxlEndpoints[Index2].PciLocation.AsUint32 == Endpoint->PciLocation.AsUint32) {
            if (gCxlSwitchList[Index].EndpointCount > 0 && Index2 != 0) {
              HostMemPool->Base += CxlEndpointAdjustMemPoolSize (
                                    gCxlSwitchList[Index].CxlEndpoints[Index2-1].HostMemPool.Size,
                                    gCxlSwitchList[Index].CxlEndpoints[Index2-1].HostMemPool.Alignment
                                    );
              HostMemPool->Size += gCxlSwitchList[Index].CxlEndpoints[Index2-1].HostMemPool.Size;
            }
          }
        }
      }
    }

    DEBUG ((DEBUG_INFO, " HostMemPoolBase = 0x%lX\n", Endpoint->HostMemPool.Base));
    DEBUG ((DEBUG_INFO, " HostMemPoolSize = 0x%lX\n", Endpoint->HostMemPool.Size));

    if (((Dvsec.Range1SizeLo & 0x00000001) != 0) && ((Dvsec.Range1SizeLo & 0x00000002) != 0) && (HdmCount != 0)) {
      if ((Dvsec.Range1SizeHi != 0) || ((Dvsec.Range1SizeLo & 0xF0000000) != 0)) {
        // Update Endpoint memory pool definition
        Endpoint->MemPool[0].Base = Endpoint->HostMemPool.Base;
        Endpoint->HostMemPool.Base += Endpoint->MemPool[0].Size;
        Dvsec.Range1BaseHi = Endpoint->MemPool[0].Base >> 32;
        Dvsec.Range1BaseLo = Endpoint->MemPool[0].Base &0xFFFFFFFF;
        Dvsec.CxlControl |= 1 << 2; // Mem_Enable
        Status = Endpoint->StartPciIo->Pci.Write (
          Endpoint->StartPciIo,
          EfiPciIoWidthUint32,
          DvsecOffset + FLEX1_BASE_HI_OFFSET,
          2,
          (VOID *)&Dvsec.Range1BaseHi
          );

        MemPoolMappedCount++;
        DEBUG ((DEBUG_INFO, " CXL Memory Pool 1 BASE 0x%lX SIZE 0x%lX\n",
                Endpoint->MemPool[0].Base, Endpoint->MemPool[0].Size));
      }
    }
    if (((Dvsec.Range2SizeLo & 0x00000001) != 0) && ((Dvsec.Range2SizeLo & 0x00000002) != 0) && (HdmCount == 2)) {
      if ((Dvsec.Range2SizeHi != 0) || ((Dvsec.Range2SizeLo & 0xF0000000) != 0)) {
        // Update Endpoint memory pool definition
        Endpoint->MemPool[1].Base = Endpoint->MemPool[0].Size;
        Dvsec.Range2BaseHi = Endpoint->MemPool[1].Base >> 32;
        Dvsec.Range2BaseLo = Endpoint->MemPool[1].Base &0xFFFFFFFF;
        Dvsec.CxlControl |= 1 << 2; // Mem_Enable
        Status = Endpoint->StartPciIo->Pci.Write (
          Endpoint->StartPciIo,
          EfiPciIoWidthUint32,
          DvsecOffset + FLEX2_BASE_HI_OFFSET,
          2,
          (VOID *)&Dvsec.Range2BaseHi
          );

        MemPoolMappedCount++;
        DEBUG ((DEBUG_INFO, " CXL Memory Pool 2 OFFSET 0x%lX SIZE 0x%lX\n ",
                Endpoint->MemPool[1].Base, Endpoint->MemPool[1].Size));
      }
    }
  }
  if ((Dvsec.CxlControl & (1 << 2)) != 0) {
    Status = Endpoint->StartPciIo->Pci.Write (
      Endpoint->StartPciIo,
      EfiPciIoWidthUint32,
      DvsecOffset + DVSEC_CXL_CTL_OFFSET,
      1,
      (VOID *) &Dvsec.CxlControl
      );
  }

  // If no memory was mapped, check for Type 1 (cache only)
  if((Dvsec.CxlCapability & 0x0001) == 0x0001) {
    DEBUG ((DEBUG_INFO, "Enabling CXL cache\n"));
    Dvsec.CxlControl |= 1UL << 0;
    Status = Endpoint->StartPciIo->Pci.Write (
      Endpoint->StartPciIo,
      EfiPciIoWidthUint32,
      DvsecOffset + DVSEC_CXL_CTL_OFFSET,
      1,
      (VOID *) &Dvsec.CxlControl
    );
  }

  if (Endpoint->HostMemPool.Flags & AMD_CXL_DVSEC_LOCK) {
    DEBUG ((DEBUG_INFO, " Locking CXL DVSEC\n"));
    Dvsec.CxlLock |= 1;
    Status = Endpoint->StartPciIo->Pci.Write (
      Endpoint->StartPciIo,
      EfiPciIoWidthUint16,
      DvsecOffset + DVSEC_CXL_LOCK_OFFSET,
      1,
      (VOID *) &Dvsec.CxlLock
      );
  }

ON_EXIT:
  if (MemPoolMappedCount == 0) {
    // no memory is active
    Status = EFI_NOT_FOUND;
  }
  DEBUG ((DEBUG_INFO, "%a - EXIT (Status = %r)\n", __FUNCTION__, Status));
  return Status;
}


/**
 * @brief This routine is called from the CxlEndpointEnable function to configure the CXL Endpoint.
 *
 * @param[in] Endpoint           CXL Endpoint instance pointer.
 *
 * @retval EFI_SUCCESS           This driver is added to this device.
 * @retval other                 Some error occurs when binding this driver to this device.
 */
EFI_STATUS
EFIAPI
CxlEndpointConfigure (
  IN  CXL_ENDPOINT  *Endpoint
  )
{
  PCI_ADDR                         EndpointBDF;
  EFI_STATUS                       Status;
  AMD_NBIO_CXL_SERVICES_PROTOCOL   *CxlNbioProtocol = NULL;

  DEBUG ((DEBUG_INFO, "%a - ENTRY\n", __FUNCTION__));
  Status = EFI_SUCCESS;

  // Locate CxlNbio protocol
  Status = gBS->LocateProtocol (
    &gAmdNbioCxlServicesProtocolGuid,
    NULL,
    (VOID **)&CxlNbioProtocol
    );
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Error: gBS->LocateProtocol (gAmdNbioCxlServicesProtocolGuid) failed! \n"));
    goto ON_EXIT;
  }

  if (Endpoint->IsSwitchEndpoint) {
    goto ON_EXIT;
  }

  // Configure Root port for this endpoint device
  EndpointBDF.Address.Segment = Endpoint->PciLocation.AsBits.Segment;
  EndpointBDF.Address.Bus = Endpoint->PciLocation.AsBits.Bus;
  EndpointBDF.Address.Device = Endpoint->PciLocation.AsBits.Device;
  EndpointBDF.Address.Function = Endpoint->PciLocation.AsBits.Function;
  EndpointBDF.Address.Register = 0;
  DEBUG ((DEBUG_INFO, "EndpointBDF (PCI_ADDR):\n"));
  DEBUG ((DEBUG_INFO, " Segment = %d, Bus = 0x%02X, Device = 0x%02X, Function = 0x%X\n",
    EndpointBDF.Address.Segment, EndpointBDF.Address.Bus,
    EndpointBDF.Address.Device, EndpointBDF.Address.Function));

  Status = CxlNbioProtocol->CxlConfigureRootPort (
    CxlNbioProtocol,
    EndpointBDF
    );

ON_EXIT:

  DEBUG ((DEBUG_INFO, "%a - EXIT (Status = %r)\n", __FUNCTION__, Status));
  return Status;
}

/**
 * Register memory pool for Endpoint at CxlEndpointGetRootBridgeInfo().
 *
 * @param[in] Endpoint                CXL Endpoint instance pointer.
 *
 * @retval EFI_SUCCESS                Operation was successful.
 * @retval other                      Some error occurs.
 */
STATIC
EFI_STATUS
CxlEndpointRegisterMemPools (
  IN  CXL_ENDPOINT  *Endpoint
  )
{
  AMD_CXL_MEMORY_POOL  *HostMemPool;
  AMD_CXL_MEMORY_POOL  *MemPool;
  UINTN                MemPoolToRegister;
  UINTN                Index;
  EFI_STATUS           Status;

  DEBUG ((DEBUG_INFO, "%a - ENTRY\n", __FUNCTION__));
  Status = EFI_UNSUPPORTED;
  // Combine memory pools from Endpoint into a single one for the Host
  HostMemPool = &Endpoint->HostMemPool;
  HostMemPool->Base = 0ULL;
  HostMemPool->Size = 0ULL;
  HostMemPool->Flags = 0ULL;
  MemPoolToRegister = Endpoint->MemPoolCount;

  for (Index = 0; Index < Endpoint->MemPoolCount && MemPoolToRegister != 0; ++Index) {
    MemPool = &Endpoint->MemPool[Index];
    DEBUG ((DEBUG_INFO, " CXL Memory Pool %d: Base=0x%lX Size=0x%lX\n", Index+1, MemPool->Base, MemPool->Size));

    if (HostMemPool->Size == 0ULL) {
      // Clone first supported memory pool
      DEBUG ((DEBUG_INFO, " Cloning first memory pool\n"));

      if (CxlEndpointSupportedEfiMemType (MemPool->EfiType)) {
        HostMemPool->Size = MemPool->Size;
        HostMemPool->Alignment = MemPool->Alignment;
        HostMemPool->EfiType = MemPool->EfiType;
        --MemPoolToRegister;
      }
    }
    else if (MemPool->EfiType == HostMemPool->EfiType) {
      // Set Base as offset relative to the first pool
      MemPool->Base = CxlEndpointAdjustMemPoolSize (HostMemPool->Size, HostMemPool->Alignment);
      HostMemPool->Size += MemPool->Size;
      DEBUG ((DEBUG_INFO, "  Added CXL Memory Pool %d: Base=0x%lX Size=0x%lX\n", Index+1, MemPool->Base, MemPool->Size));
      --MemPoolToRegister;
    }
  }

  if (MemPoolToRegister == 0) {
      Status = EFI_SUCCESS;
  }

  DEBUG ((DEBUG_INFO, "HOST Memory Pool Base=0x%lX Size=0x%lX Alignment=0x%lX Type=0x%x\n",
          HostMemPool->Base, HostMemPool->Size, HostMemPool->Alignment, HostMemPool->EfiType));

  return Status;
}


/**
 * @brief This routine is called from CxlEndpointEnable(), once the gCxlMgrProtocol is found.
 *
 * @param[in] Endpoint               CXL Endpoint instance pointer.
 *
 * @retval EFI_SUCCESS               Operation was successful.
 * @retval other                     Some error occurs.
 */
EFI_STATUS
EFIAPI
CxlEndpointGetHdmRanges (
  IN  CXL_ENDPOINT  *Endpoint
  )
{
  EFI_STATUS           Status;
  UINT16               DvsecOffset;
  UINT16               DvsecLength;
  CXL_DVSEC_CAPABILITY Dvsec;
  UINT32               PciAddr;
  DC_REGION_CONFIG     DcRegions[MAX_CXL_DC_REGIONS];
  UINT8                DcRegionsCount;
  UINT8                DcRegionsIndex;

  DEBUG ((DEBUG_INFO, "%a - ENTRY\n", __FUNCTION__));

  PciAddr = MAKE_SBDFO (Endpoint->PciLocation.AsBits.Segment,
                        Endpoint->PciLocation.AsBits.Bus,
                        Endpoint->PciLocation.AsBits.Device,
                        Endpoint->PciLocation.AsBits.Function,
                        0
                       );

  Status = gCxlMgrProtocol->GetDcRegions (gCxlMgrProtocol,
                                          PciAddr,
                                          &DcRegionsCount,
                                          DcRegions
                                         );
  if (!EFI_ERROR (Status)) {
    Endpoint->IsDcd = TRUE;
    for (DcRegionsIndex = 0; DcRegionsIndex < DcRegionsCount; DcRegionsIndex++) {
      Endpoint->MemPool[DcRegionsIndex].Base = DcRegions[DcRegionsIndex].RegionBase;
      Endpoint->MemPool[DcRegionsIndex].Size = DcRegions[DcRegionsIndex].RegionLen;
      Endpoint->MemPool[DcRegionsIndex].Alignment = ABL_MIN_BASE_ADDRESS_ALIGNMENT;
      Endpoint->MemPool[DcRegionsIndex].EfiType = EfiReservedMemoryType;
      Endpoint->MemPoolCount++;
    }
  } else {
    Endpoint->IsDcd = FALSE;
    Status = CxlPcieGetCxlDvsec(Endpoint, &DvsecOffset, &DvsecLength, &Dvsec);
    if (EFI_ERROR (Status)) {
      DEBUG ((DEBUG_ERROR, "%a - Error: CxlPcieGetCxlDvsec() Failed! Status = %r\n", __FUNCTION__, Status));
      goto ON_EXIT;
    }

    DEBUG ((DEBUG_INFO, "Found Memory Pool Info for Cxl Endpoint:\n"));
    DEBUG ((DEBUG_INFO, " Segment = %d, Bus = 0x%02X, Device = 0x%02X, Function = 0x%X\n",
      Endpoint->PciLocation.AsBits.Segment, Endpoint->PciLocation.AsBits.Bus,
      Endpoint->PciLocation.AsBits.Device, Endpoint->PciLocation.AsBits.Function));
    DEBUG ((DEBUG_INFO, " CXL DVSEC CAPABILITY 0x%x\n", Dvsec.CxlCapability));

    if ((Dvsec.Range1SizeLo & 0x00000001) != 0) {
      if ((Dvsec.Range1SizeHi != 0) || ((Dvsec.Range1SizeLo & 0xF0000000) != 0)) {
        // Update Endpoint memory pool definition
        Endpoint->MemPoolCount++;
        Endpoint->MemPool[0].Size = (((UINT64) Dvsec.Range1SizeHi) << 32) + (Dvsec.Range1SizeLo & 0xF0000000);
        /*
          NOTE: The Range Size Low Media Type setting is deprecated for CXL 2.0.
                The memory characteristics are communicated via CDAT.
                FlexBusRangeSizeLo will have bits 4:2 set to 010.
                For now, setting the MemPool.EfiType to EfiConventionalMemory here will not affect CXL 2.0 memory enablement.
        */
        if ((Dvsec.Range1SizeLo & 0x00000004) == 0x00000004) {
          Endpoint->MemPool[0].EfiType = EfiPersistentMemory;
        } else {
          Endpoint->MemPool[0].EfiType = EfiConventionalMemory;
        }
        Endpoint->MemPool[0].Alignment = ABL_MIN_BASE_ADDRESS_ALIGNMENT;
        DEBUG ((DEBUG_INFO, " CXL Memory Pool 1 size 0x%lX\n", Endpoint->MemPool[0].Size));
        Endpoint->DesiredInterleave = (UINT8)((Dvsec.Range1SizeLo & 0x00001F00) >> 8);
      }
    }

    if ((Dvsec.Range2SizeLo & 0x00000001) != 0) {
      if ((Dvsec.Range2SizeHi != 0) || ((Dvsec.Range2SizeLo & 0xF0000000) != 0)) {
        // Update Endpoint memory pool definition
        Endpoint->MemPoolCount++;
        Endpoint->MemPool[1].Size = (((UINT64) Dvsec.Range2SizeHi) << 32) + (Dvsec.Range2SizeLo & 0xF0000000);
        if ((Dvsec.Range2SizeLo & 0x00000004) == 0x00000004) {
          Endpoint->MemPool[1].EfiType = EfiPersistentMemory;
        } else {
          Endpoint->MemPool[1].EfiType = EfiConventionalMemory;
        }
        Endpoint->MemPool[1].Alignment = ABL_MIN_BASE_ADDRESS_ALIGNMENT;
        DEBUG ((DEBUG_INFO, " CXL Memory Pool 2 size 0x%lX\n", Endpoint->MemPool[1].Size));
        if ((UINT8)((Dvsec.Range2SizeLo & 0x00001F00) >> 8) > Endpoint->DesiredInterleave) {
          Endpoint->DesiredInterleave = (UINT8)((Dvsec.Range2SizeLo & 0x00001F00) >> 8);
        }
      }
    }

    // Check for Type 1
    if((Dvsec.CxlCapability & 0x0005) == 0x0001) {
      Endpoint->MemPoolCount++;
      Endpoint->MemPool[0].Size = 0UL;
      Endpoint->MemPool[0].EfiType = CxlCache;
      Endpoint->MemPool[0].Alignment = ABL_MIN_BASE_ADDRESS_ALIGNMENT;
    }

    // Register MemPool(s) on this EndPoint
    if (EFI_ERROR (CxlEndpointRegisterMemPools (Endpoint))) {
      DEBUG ((DEBUG_ERROR, "ERROR: CxlEndpointRegisterMemPoolsOnEndpoint() failed!\n"));
      goto ON_EXIT;
    }
  }

ON_EXIT:
  DEBUG ((DEBUG_INFO, "%a - EXIT (Status = %r)\n", __FUNCTION__, Status));
  return Status;
}


/**
 * @brief Dump CXL Endpoint data.
 *
 * @param[in] Endpoint       CXL Endpoint instance.
 *
 * @retval None
 */
VOID
DumpCxlEndpoint (
  IN CXL_ENDPOINT *Endpoint
  )
{
  DEBUG ((DEBUG_INFO, "<------------ Dump CXL Endpoint Start ------------>\n"));
  DEBUG ((DEBUG_INFO, " EP_Index[%d]:\n", gCxlEndpointCount));
  DEBUG ((DEBUG_INFO, " PciSegment         = 0x%x\n", Endpoint->PciLocation.AsBits.Segment));
  DEBUG ((DEBUG_INFO, " PciBus             = 0x%x\n", Endpoint->PciLocation.AsBits.Bus));
  DEBUG ((DEBUG_INFO, " PciDevice          = 0x%x\n", Endpoint->PciLocation.AsBits.Device));
  DEBUG ((DEBUG_INFO, " PciFunction        = 0x%x\n", Endpoint->PciLocation.AsBits.Function));
  DEBUG ((DEBUG_INFO, " VendorId           = 0x%04x\n", Endpoint->VendorId));
  DEBUG ((DEBUG_INFO, " DeviceId           = 0x%04x\n", Endpoint->DeviceId));
  DEBUG ((DEBUG_INFO, " MemPoolCount       = %d\n", Endpoint->MemPoolCount));
  DEBUG ((DEBUG_INFO, " MemPool Flags      = %d\n", Endpoint->MemPool->Flags));
  DEBUG ((DEBUG_INFO, " HostMemPool Flags  = %d\n", Endpoint->HostMemPool.Flags));
  DEBUG ((DEBUG_INFO, " HostMemPoolBase    = 0x%lX\n", Endpoint->HostMemPool.Base));
  DEBUG ((DEBUG_INFO, " HostMemPoolSize    = 0x%lX\n", Endpoint->HostMemPool.Size));
  DEBUG ((DEBUG_INFO, " MemPool0Base       = 0x%lX\n", Endpoint->MemPool[0].Base));
  DEBUG ((DEBUG_INFO, " MemPool0Size       = 0x%lX\n", Endpoint->MemPool[0].Size));
  DEBUG ((DEBUG_INFO, " MemPool1Base       = 0x%lX\n", Endpoint->MemPool[1].Base));
  DEBUG ((DEBUG_INFO, " MemPool1Size       = 0x%lX\n", Endpoint->MemPool[1].Size));
  DEBUG ((DEBUG_INFO, " Mailbox Ready      = 0x%d\n", Endpoint->CxlCci.MbInterfaceReady));
  DEBUG ((DEBUG_INFO, " QoSSldEPCSupported = 0x%d\n", Endpoint->CxlQosCapabilities.SldEgressPortCongestionSupported));
  DEBUG ((DEBUG_INFO, " QoSSldTTRSupported = 0x%d\n", Endpoint->CxlQosCapabilities.SldTemporaryTpReductionSupported));
  DEBUG ((DEBUG_INFO, " QoSMldEPCSupported = 0x%d\n", Endpoint->CxlQosCapabilities.MldEgressPortCongestionSupported));
  DEBUG ((DEBUG_INFO, " QoSMldTTRSupported = 0x%d\n", Endpoint->CxlQosCapabilities.MldTemporaryTpReductionSupported));
  DEBUG ((DEBUG_INFO, " IsSwitchEndpoint   = %d\n", Endpoint->IsSwitchEndpoint));
  DEBUG ((DEBUG_INFO, " DesiredInterleave  = %d\n", Endpoint->DesiredInterleave));
  DEBUG ((DEBUG_INFO, "<------------- Dump CXL Endpoint End ------------->\n"));
}

/**
 * @brief Dump CXL Switch data.
 *
 * @param[in] Switch       CXL Switch instance.
 *
 * @retval None
 */
VOID
DumpCxlSwitch (
  IN CXL_SWITCH *Switch
  )
{
  UINTN Index;
  UINTN Index2;

  DEBUG ((DEBUG_INFO, "<------------ Dump CXL Switch Start ------------>\n"));
  DEBUG ((DEBUG_INFO, " PciSegment                     = 0x%x\n", Switch->PciLocation.AsBits.Segment));
  DEBUG ((DEBUG_INFO, " PciBus                         = 0x%x\n", Switch->PciLocation.AsBits.Bus));
  DEBUG ((DEBUG_INFO, " PciDevice                      = 0x%x\n", Switch->PciLocation.AsBits.Device));
  DEBUG ((DEBUG_INFO, " PciFunction                    = 0x%x\n", Switch->PciLocation.AsBits.Function));
  DEBUG ((DEBUG_INFO, " VendorId                       = 0x%04x\n", Switch->VendorId));
  DEBUG ((DEBUG_INFO, " DeviceId                       = 0x%04x\n", Switch->DeviceId));
  DEBUG ((DEBUG_INFO, " Number of endpoints enumerated = %d\n", Switch->EndpointTotal));
  DEBUG ((DEBUG_INFO, " Number of endpoints processed  = %d\n", Switch->EndpointCount));
  DEBUG ((DEBUG_INFO, " Number of interleave sets      = %d\n", Switch->InterleaveSetCount));
  for (Index = 0; Index < Switch->InterleaveSetCount; Index++) {
    DEBUG ((DEBUG_INFO, "<----- Interleave Set [%d] Start ----->\n", Index));
    for (Index2 = 0; Index2 < Switch->InterleaveSet[Index].MemPoolCount; Index2++) {
      DEBUG ((DEBUG_INFO, " Interleave Base                = 0x%lX\n", Switch->InterleaveSet[Index].MemPool[Index2].Base));
      DEBUG ((DEBUG_INFO, " Interleave Size                = 0x%lX\n", Switch->InterleaveSet[Index].MemPool[Index2].Size));
      DEBUG ((DEBUG_INFO, " Interleave Ways                = %d\n", Switch->InterleaveSet[Index].InterleaveWays));
      DEBUG ((DEBUG_INFO, " Interleave Granularity         = %d\n", Switch->InterleaveSet[Index].InterleaveGranularity));
    }
    DEBUG ((DEBUG_INFO, "<------ Interleave Set [%d] End ------>\n", Index));
  }
  DEBUG ((DEBUG_INFO, "<------------- Dump CXL Switch End ------------->\n"));
}


/**
 * @brief This routine is called from the Support() function of Driver Binding protocol
 *        to check supported CXL device.
 *
 * @param[in] PciIo         PciIo instance.
 *
 * @retval TRUE             Supported CXL device.
 * @retval FALSE            Not CXL device or unsupported CXL device.
 */
BOOLEAN
IsSupportedCxlDevice (
  IN   EFI_PCI_IO_PROTOCOL  *PciIo
)
{
  EFI_STATUS           Status;
  UINTN                PciSegment;
  UINTN                PciBus;
  UINTN                PciDevice;
  UINTN                PciFunction;
  AMD_PCI_LOCATION     EndpointLocation;

  // Get PCI Location
  Status = PciIo->GetLocation (
    PciIo,
    &PciSegment,
    &PciBus,
    &PciDevice,
    &PciFunction
    );
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "%a: Failed to get CXL location from PciIo\n", __FUNCTION__));
    return FALSE;
  }

  EndpointLocation.AsUint32 = 0;
  EndpointLocation.AsBits.Segment = (UINT32) PciSegment;
  EndpointLocation.AsBits.Bus = (UINT32) PciBus;

  if (gCxlMgrProtocol == NULL) {
    return FALSE;
  }
  Status = gCxlMgrProtocol->IsCxlDevice (
    gCxlMgrProtocol,
    EndpointLocation
    );
  if (EFI_ERROR (Status)) {
    return FALSE;
  } else {
    DEBUG ((DEBUG_VERBOSE, "%a: CXL Endpoint: Segment = %d, Bus = 0x%02X, Device = 0x%02X, Function = 0x%X\n",
              __FUNCTION__, PciSegment, PciBus, PciDevice, PciFunction));
    return TRUE;
  }
}


/**
 * @brief Program CXL Extensions DVSEC For Ports (DVSEC ID 3)
 *
 * @param[in] PciIo             PciIo instance.
 * @param[in] CxlExtDvsec       CXL Extensions DVSEC data structure.
 *
 * @retval EFI_SUCCESS          CXL device is enabled.
 * @retval other errors         CXL device fails to enable.
 */
EFI_STATUS
CxlProgramExtensionsDvsecForPorts (
  IN EFI_PCI_IO_PROTOCOL  *PciIo,
  IN CXL_EXTENSIONS_DVSEC *CxlExtDvsec
)
{
  EFI_STATUS           Status;
  UINT16               CxlDvsecOffset;
  UINT16               CxlDvsecLength;
  CXL_EXTENSIONS_DVSEC CxlExtensionsDvsec;

  DEBUG ((DEBUG_INFO, "%a - ENTRY\n", __FUNCTION__));

  // Probe CXL DVSEC Capability
  Status = CxlPcieLocateCxlDvsecCapability (
    PciIo,
    CXL_EXTENSIONS_DVSEC_ID,
    &CxlDvsecOffset,
    &CxlDvsecLength
    );
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "%a - Error: CxlPcieLocateCxlDvsecCapability() Failed! Status = %r\n", __FUNCTION__, Status));
    return Status;
  }

  // Read CXL Extensions DVSEC
  Status = PciIo->Pci.Read (
    PciIo,
    EfiPciIoWidthUint32,
    CxlDvsecOffset,
    sizeof (CXL_EXTENSIONS_DVSEC) / sizeof (UINT32),
    (VOID *)&CxlExtensionsDvsec
    );
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "%a - Error: Read CXL Extensions DVSEC failed! Status = %r\n", __FUNCTION__, Status));
    return Status;
  }

  CxlExtensionsDvsec.AltBusBase = CxlExtDvsec->AltBusBase;
  CxlExtensionsDvsec.AltBusLimit = CxlExtDvsec->AltBusLimit;
  CxlExtensionsDvsec.AltPrefetchMemBase = CxlExtDvsec->AltPrefetchMemBase;
  CxlExtensionsDvsec.AltPrefetchMemBaseHigh = CxlExtDvsec->AltPrefetchMemBaseHigh;
  CxlExtensionsDvsec.AltPrefetchMemLimit = CxlExtDvsec->AltPrefetchMemLimit;
  CxlExtensionsDvsec.AltPrefetchMemLimitHigh = CxlExtDvsec->AltPrefetchMemLimitHigh;
  CxlExtensionsDvsec.PortControlExtensions |= 0x0004; // set Alt Memory and ID Space Enable bit

  // Write CXL Extensions DVSEC
  Status = PciIo->Pci.Write (
    PciIo,
    EfiPciIoWidthUint32,
    CxlDvsecOffset,
    sizeof (CXL_EXTENSIONS_DVSEC) / sizeof (UINT32),
    (VOID *)&CxlExtensionsDvsec
    );
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "%a - Error: Write CXL Extensions DVSEC failed! Status = %r\n", __FUNCTION__, Status));
    return Status;
  }

  // Read CXL Extensions DVSEC
  Status = PciIo->Pci.Read (
    PciIo,
    EfiPciIoWidthUint32,
    CxlDvsecOffset,
    sizeof (CXL_EXTENSIONS_DVSEC) / sizeof (UINT32),
    (VOID *)&CxlExtensionsDvsec
    );
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "%a - Error: Read CXL Extensions DVSEC failed! Status = %r\n", __FUNCTION__, Status));
    return Status;
  }

  DEBUG((DEBUG_INFO, "CxlExtensionsDvsec.AltBusBase              = 0x%02x\n", CxlExtensionsDvsec.AltBusBase));
  DEBUG((DEBUG_INFO, "CxlExtensionsDvsec.AltBusLimit             = 0x%02x\n", CxlExtensionsDvsec.AltBusLimit));
  DEBUG((DEBUG_INFO, "CxlExtensionsDvsec.AltPrefetchMemBase      = 0x%04x\n", CxlExtensionsDvsec.AltPrefetchMemBase));
  DEBUG((DEBUG_INFO, "CxlExtensionsDvsec.AltPrefetchMemBaseHigh  = 0x%08x\n", CxlExtensionsDvsec.AltPrefetchMemBaseHigh));
  DEBUG((DEBUG_INFO, "CxlExtensionsDvsec.AltPrefetchMemLimit     = 0x%04x\n", CxlExtensionsDvsec.AltPrefetchMemLimit));
  DEBUG((DEBUG_INFO, "CxlExtensionsDvsec.AltPrefetchMemLimitHigh = 0x%08x\n", CxlExtensionsDvsec.AltPrefetchMemLimitHigh));

  DEBUG((DEBUG_INFO, "%a - EXIT (Status = %r)\n", __FUNCTION__, Status));
  return Status;
}


/**
 * @brief Gets the CXL HDM Decoder Capability
 *
 * @param Endpoint
 * @param CxlHdmDecoderCap
 * @param CxlDecoderOffset
 * @return EFI_STATUS
 */
EFI_STATUS
CxlEndpointGetDecoderCapability (
  IN  CXL_ENDPOINT            *Endpoint,
  OUT AMD_CXL_HDM_DECODER_CAP *CxlHdmDecoderCap,
  OUT UINT64                  *CxlDecoderOffset
)
{
  EFI_STATUS                          Status;
  UINT64                              BarStart;
  UINT32                              CxlHdmDecoderCapabilityOffset;
  volatile int                        *RegPtr;
  ERROR_LOG_PARAMS                    CxlErrorLog;
  AMD_CXL_ERROR_LOG_PROTOCOL          *AmdCxlErrorLog = NULL;

  DEBUG ((DEBUG_INFO, "%a - ENTRY\n", __FUNCTION__));

  Status = gBS->LocateProtocol (&gAmdCxlErrorLogProtocolGuid, 
                                NULL, 
                                (VOID **)&AmdCxlErrorLog
                                );
  if (EFI_ERROR (Status)) {
    DEBUG((DEBUG_ERROR, "%a: Failed to locate AmdCxlErrorLogProtocol! Status = %r\n", __FUNCTION__, Status));
  }


  // HDM Decoder Capability structure is located in the Component Registers
  Status = CxlEndpointFindRegisterBlock (
    Endpoint,
    CXL_COMPONENT_REGISTERS_RBI,
    &BarStart
    );

  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_INFO, "CXL Component Register block not found\n"));
    if (AmdCxlErrorLog != NULL) {
      ZeroMem (&CxlErrorLog, sizeof (ERROR_LOG_PARAMS));
      CxlErrorLog.ErrorClass = AMD_ERROR;
      CxlErrorLog.ErrorInfo = CXL_ERROR_LOG_ID | CXL_ENDPOINT_INIT_CONFIG | CXL_COMPONENT_REGISTER_NOT_FOUND;
      CxlErrorLog.DataParam1 = Endpoint->PciLocation.AsBits.Segment;
      CxlErrorLog.DataParam2 = Endpoint->PciLocation.AsBits.Bus;
      AmdCxlErrorLog->CxlAddErrorLog (AmdCxlErrorLog, &CxlErrorLog);
    }
    goto ON_EXIT;
  }

  Status = CxlEndpointFindCxlCapability (
    CXL_HDM_DECODER_CAPABILITY_ID,
    BarStart + CXL_MEM_CACHE_COMPONENT_REGS_OFFSET,
    &CxlHdmDecoderCapabilityOffset
    );

  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_INFO, "CXL HDM Decoder Capability not found\n"));
    goto ON_EXIT;
  }

  *CxlDecoderOffset = BarStart + CXL_MEM_CACHE_COMPONENT_REGS_OFFSET + CxlHdmDecoderCapabilityOffset;
  RegPtr = (volatile int *) (*CxlDecoderOffset);
  DEBUG ((DEBUG_INFO, "CXL HDM Decoder Capability register = 0x%08x\n", (UINT32) *RegPtr));
  CxlHdmDecoderCap->DecoderCount = (UINT8) (*RegPtr & 0x0000000F);
  CxlHdmDecoderCap->TargetCount = (UINT8) ((*RegPtr & 0x000000F0) >> 4);
  CxlHdmDecoderCap->Interleave11to8 = (UINT8) ((*RegPtr & 0x00000100) >> 8);
  CxlHdmDecoderCap->Interleave14to12 = (UINT8) ((*RegPtr & 0x00000200) >> 9);
  CxlHdmDecoderCap->PoisonOnDecode = (UINT8) ((*RegPtr & 0x00000400) >> 10);

  switch (CxlHdmDecoderCap->DecoderCount) {
    case 0x00: CxlHdmDecoderCap->DecoderCount = 1; break;
    case 0x01:
    case 0x02:
    case 0x03:
    case 0x04:
    case 0x05:
    case 0x06:
    case 0x07:
    case 0x08: CxlHdmDecoderCap->DecoderCount *= 2; break;
    case 0x09: CxlHdmDecoderCap->DecoderCount = 20; break;
    case 0x0A: CxlHdmDecoderCap->DecoderCount = 24; break;
    case 0x0B: CxlHdmDecoderCap->DecoderCount = 28; break;
    case 0x0C: CxlHdmDecoderCap->DecoderCount = 32; break;
    default: CxlHdmDecoderCap->DecoderCount = 0; break;
  }

ON_EXIT:
  DEBUG((DEBUG_INFO, "%a - EXIT (Status = %r)\n", __FUNCTION__, Status));
  return Status;
}


/**
 * @brief Programs the device HDM Decoder(s).
 *
 * @param[in] Endpoint          Endpoint device structure.
 *
 * @return EFI_STATUS
 */
EFI_STATUS
CxlEndpointDecoderProgramming (
  IN CXL_ENDPOINT *Endpoint
)
{
  EFI_STATUS                          Status;
  UINTN                               Index;
  UINT64                              CxlDecoderOffset;
  UINT32                              CxlHdmDecoderGlobalCtrlReg;
  AMD_CXL_HDM_DECODER_CAP             CxlHdmDecoderCap;
  AMD_CXL_HDM_DECODER                 CxlHdmDecoders[MAX_HDM_DECODERS_PER_ENDPOINT];
  AMD_CXL_HDM_DECODER_CTRL_REG        CxlHdmDecoderCtrlReg;
  BOOLEAN                             CommitLogic;
  BOOLEAN                             PreviousCommitted;
  volatile int                        *RegPtr;
  ERROR_LOG_PARAMS                    CxlErrorLog;
  AMD_CXL_ERROR_LOG_PROTOCOL          *AmdCxlErrorLog = NULL;
  UINTN                               MemPoolCountToUse;
  AMD_CXL_MEMORY_POOL                 *MemPoolToUse;

  DEBUG ((DEBUG_INFO, "%a - ENTRY\n", __FUNCTION__));

  Status = gBS->LocateProtocol (&gAmdCxlErrorLogProtocolGuid, 
                                NULL, 
                                (VOID **)&AmdCxlErrorLog
                                );
  if (EFI_ERROR (Status)) {
    DEBUG((DEBUG_ERROR, "%a: Failed to locate AmdCxlErrorLogProtocol! Status = %r\n", __FUNCTION__, Status));
  }

  Status = CxlEndpointGetDecoderCapability (Endpoint, &CxlHdmDecoderCap, &CxlDecoderOffset);

  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_INFO, "CXL HDM Decoder Capability not found\n"));
    if (AmdCxlErrorLog != NULL) {
      ZeroMem (&CxlErrorLog, sizeof (ERROR_LOG_PARAMS));
      CxlErrorLog.ErrorClass = AMD_ERROR;
      CxlErrorLog.ErrorInfo = CXL_ERROR_LOG_ID | CXL_ENDPOINT_INIT_CONFIG | CXL_HDM_DECODER_CAPABILITY_NOT_FOUND;
      CxlErrorLog.DataParam1 = Endpoint->PciLocation.AsBits.Segment;
      CxlErrorLog.DataParam2 = Endpoint->PciLocation.AsBits.Bus;
      AmdCxlErrorLog->CxlAddErrorLog (AmdCxlErrorLog, &CxlErrorLog);
    }
    goto ON_EXIT;
  }

  //
  // Program the decoders
  //
  DEBUG ((DEBUG_INFO, "HDM Decoder count = %d, Endpoint HDM Range count = %d\n",
          CxlHdmDecoderCap.DecoderCount, Endpoint->MemPoolCount));

  if (CxlHdmDecoderCap.DecoderCount != 0) {
    ZeroMem (&CxlHdmDecoders, sizeof(AMD_CXL_HDM_DECODER) * MAX_HDM_DECODERS_PER_ENDPOINT);
    PreviousCommitted = FALSE;

    if (Endpoint->ApobMemPoolCount > 1) {
      MemPoolCountToUse = Endpoint->ApobMemPoolCount;
      MemPoolToUse = &Endpoint->ApobMemPool[0];
    } else {
      MemPoolCountToUse = Endpoint->MemPoolCount;
      MemPoolToUse = &Endpoint->MemPool[0];
    }

    for (Index = 0; (Index < MemPoolCountToUse) && (Index < CxlHdmDecoderCap.DecoderCount); Index++) {
      CxlHdmDecoders[Index].MemBaseLow = (UINT32) (MemPoolToUse[Index].Base & 0xF0000000);
      CxlHdmDecoders[Index].MemBaseHigh = (UINT32) ((MemPoolToUse[Index].Base & 0xFFFFFFFF00000000UL) >> 32);
      CxlHdmDecoders[Index].MemSizeLow = (UINT32) (MemPoolToUse[Index].Size & 0xF0000000);
      CxlHdmDecoders[Index].MemSizeHigh = (UINT32) ((MemPoolToUse[Index].Size & 0xFFFFFFFF00000000UL) >> 32);

      RegPtr = (volatile int *) (CxlDecoderOffset + (0x20*Index + CXL_HDM_DECODER_BASE_LOW_OFFSET));
      *RegPtr = (int) CxlHdmDecoders[Index].MemBaseLow;

      RegPtr = (volatile int *) (CxlDecoderOffset + (0x20*Index + CXL_HDM_DECODER_BASE_HIGH_OFFSET));
      *RegPtr = (int) CxlHdmDecoders[Index].MemBaseHigh;

      RegPtr = (volatile int *) (CxlDecoderOffset + (0x20*Index + CXL_HDM_DECODER_SIZE_LOW_OFFSET));
      *RegPtr = (int) CxlHdmDecoders[Index].MemSizeLow;

      RegPtr = (volatile int *) (CxlDecoderOffset + (0x20*Index + CXL_HDM_DECODER_SIZE_HIGH_OFFSET));
      *RegPtr = (int) CxlHdmDecoders[Index].MemSizeHigh;

      // Read the HDM Decoder Control register
      RegPtr = (volatile int *) (CxlDecoderOffset + (0x20*Index + CXL_HDM_DECODER_CONTROL_OFFSET));
      CxlHdmDecoderCtrlReg.Value = (UINT32) (*RegPtr);
      DEBUG ((DEBUG_INFO, "Read HDM Decoder Controler Register: Value 0x%x\n", CxlHdmDecoderCtrlReg.Value));

      // Configure interleaving
      if (Endpoint->IsInterleaved) {
        switch (Endpoint->InterleaveWays) {
          case 2:  CxlHdmDecoderCtrlReg.Field.InterleaveWays = INTERLEAVE_2_WAY; break;
          case 3:  CxlHdmDecoderCtrlReg.Field.InterleaveWays = INTERLEAVE_3_WAY; break;
          case 4:  CxlHdmDecoderCtrlReg.Field.InterleaveWays = INTERLEAVE_4_WAY; break;
          case 6:  CxlHdmDecoderCtrlReg.Field.InterleaveWays = INTERLEAVE_6_WAY; break;
          case 8:  CxlHdmDecoderCtrlReg.Field.InterleaveWays = INTERLEAVE_8_WAY; break;
          case 12: CxlHdmDecoderCtrlReg.Field.InterleaveWays = INTERLEAVE_12_WAY; break;
          case 16: CxlHdmDecoderCtrlReg.Field.InterleaveWays = INTERLEAVE_16_WAY; break;
          default: CxlHdmDecoderCtrlReg.Field.InterleaveWays = INTERLEAVE_1_WAY; break;
        }
      }
      CxlHdmDecoderCtrlReg.Field.InterleaveGranularity = 0;  // only support 256bytes IG
      DEBUG ((DEBUG_INFO, "HDM Decoder Controler Register: InterleaveGranularity 0x%x\n", CxlHdmDecoderCtrlReg.Field.InterleaveGranularity));

      //
      // Set Lock On Commit
      //
      if (Endpoint->HostMemPool.Flags & AMD_CXL_LOCK_ON_COMMIT) {
        CommitLogic = TRUE;
        if (Index > 0) {
          // NOTE: Decoder[m+1] is the current decoder (Index) in the following logic checks taken from the CXL 3.0 spec
          // Decoder[m+1].Base >= (Decoder[m].Base+Decoder[m].Size)
          if (MemPoolToUse[Index].Base < (MemPoolToUse[Index-1].Base + MemPoolToUse[Index-1].Size)) {
            CommitLogic = FALSE;
            DEBUG ((DEBUG_INFO, "Warning: Decoder[m+1].Base >= (Decoder[m].Base+Decoder[m].Size) check failed!\n"));
          }
          // Decoder[m+1].Base <= Decoder[m+1].Base+Decoder[m+1].Size (no wraparound)
          if (MemPoolToUse[Index].Base > (MemPoolToUse[Index].Base + MemPoolToUse[Index].Size)) {
            CommitLogic = FALSE;
            DEBUG ((DEBUG_INFO, "Warning: Decoder[m+1].Base <= Decoder[m+1].Base+Decoder[m+1].Size (no wraparound) check failed!\n"));
          }
          // If Decoder[m+1].IW>=8, Decoder[m+1].Size is a multiple of 3
          if (Endpoint->InterleaveWays >= 8) {
            if ((MemPoolToUse[Index].Size % 3) != 0) {
              CommitLogic = FALSE;
              DEBUG ((DEBUG_INFO, "Warning: Decoder[m+1].IW>=8, Decoder[m+1].Size is a multiple of 3 check failed!\n"));
            }
          }
          // Decoder[m].Committed=1
          if (!PreviousCommitted) {
            CommitLogic = FALSE;
            DEBUG ((DEBUG_INFO, "Warning: Previous HDM Decoder committed check failed!\n"));
          }
        }

        if (CommitLogic) {
          CxlHdmDecoderCtrlReg.Field.LockOnCommit = 1;
        } else {
          DEBUG ((DEBUG_INFO, "Error: Lock on Commit not set!\n"));
        }
      }

      // Commit the decoder
      CxlHdmDecoderCtrlReg.Field.Commit = 1;
      *RegPtr = (int) CxlHdmDecoderCtrlReg.Value;

      // Decoder logic shall set either Committed or Error Not Committed flag within 10 ms of a write to the Commit bit.
      MicroSecondDelay (10000); // 10ms delay
      DEBUG ((DEBUG_INFO, "CXL HDM Decoder Control register     = 0x%08x\n", (UINT32) *RegPtr));

      // Confirm commit
      PreviousCommitted = TRUE;
      CxlHdmDecoderCtrlReg.Value = (UINT32) (*RegPtr);
      if ((CxlHdmDecoderCtrlReg.Field.Commit != 1) ||
          (CxlHdmDecoderCtrlReg.Field.Committed != 1) ||
          (CxlHdmDecoderCtrlReg.Field.ErrorNotCommited == 1)) {
        DEBUG ((DEBUG_INFO, "Error: CXL HDM Decoder was NOT committed!\n"));
        if (AmdCxlErrorLog != NULL) {
          ZeroMem (&CxlErrorLog, sizeof (ERROR_LOG_PARAMS));
          CxlErrorLog.ErrorClass = AMD_ERROR;
          CxlErrorLog.ErrorInfo = CXL_ERROR_LOG_ID | CXL_ENDPOINT_INIT_CONFIG | CXL_HDM_DECODER_COMMIT_FAILED;
          CxlErrorLog.DataParam1 = Endpoint->PciLocation.AsBits.Segment;
          CxlErrorLog.DataParam2 = Endpoint->PciLocation.AsBits.Bus;
          AmdCxlErrorLog->CxlAddErrorLog (AmdCxlErrorLog, &CxlErrorLog);
        }
        PreviousCommitted = FALSE;
      }
    }

    //
    // Enable HDM Decoder(s)
    //
    RegPtr = (volatile int *) (CxlDecoderOffset + CXL_HDM_DECODER_GLOBAL_CONTROL_OFFSET);
    CxlHdmDecoderGlobalCtrlReg = (UINT32) (*RegPtr);
    CxlHdmDecoderGlobalCtrlReg |= 1 << 1;
    *RegPtr = (int) CxlHdmDecoderGlobalCtrlReg;
  }

ON_EXIT:
  DEBUG((DEBUG_INFO, "%a - EXIT (Status = %r)\n", __FUNCTION__, Status));
  return Status;
}


/**
 * @brief Programs the HDM Decoders on a CXL switch.
 *
 * @param[in] CxlSwitch          CXL switch device structure.
 *
 * @return EFI_STATUS
 */
EFI_STATUS
CxlSwitchDecoderProgramming (
  IN CXL_SWITCH *CxlSwitch
)
{
  EFI_STATUS                          Status;
  CXL_ENDPOINT                        *SwitchEndpoint;
  UINTN                               Index;
  UINTN                               Index2;
  UINT64                              CxlDecoderOffset;
  UINT32                              CxlHdmDecoderGlobalCtrlReg;
  UINT64                              InterleaveSetSize;
  UINT64                              InterleaveSetBase;
  AMD_CXL_HDM_DECODER_CAP             CxlHdmDecoderCap;
  AMD_CXL_HDM_DECODER                 CxlHdmDecoders[MAX_HDM_DECODERS_PER_ENDPOINT];
  AMD_CXL_HDM_DECODER_CTRL_REG        CxlHdmDecoderCtrlReg;
  AMD_CXL_HDM_DECODER_TARGET_LIST_REG CxlHdmDecoderTargetListLowReg;
  AMD_CXL_HDM_DECODER_TARGET_LIST_REG CxlHdmDecoderTargetListHighReg;
  volatile int                        *RegPtr;
  ERROR_LOG_PARAMS                    CxlErrorLog;
  AMD_CXL_ERROR_LOG_PROTOCOL          *AmdCxlErrorLog = NULL;

  DEBUG ((DEBUG_INFO, "%a - ENTRY\n", __FUNCTION__));

  Status = EFI_NOT_FOUND;
  SwitchEndpoint = NULL;

  Status = gBS->LocateProtocol (&gAmdCxlErrorLogProtocolGuid, 
                                NULL, 
                                (VOID **)&AmdCxlErrorLog
                                );
  if (EFI_ERROR (Status)) {
    DEBUG((DEBUG_ERROR, "%a: Failed to locate AmdCxlErrorLogProtocol! Status = %r\n", __FUNCTION__, Status));
  }

  for (Index = 0; Index < gCxlEndpointCount; Index++) {
    if (gCxlEndpointList[Index].IsSwitch) {
      if (gCxlEndpointList[Index].PciLocation.AsUint32 == CxlSwitch->PciLocation.AsUint32) {
        SwitchEndpoint = &gCxlEndpointList[Index];
        break;
      }
    }
  }

  if (SwitchEndpoint == NULL) {
    DEBUG ((DEBUG_INFO, "CXL switch endpoint not found\n"));
    if (AmdCxlErrorLog != NULL) {
      ZeroMem (&CxlErrorLog, sizeof (ERROR_LOG_PARAMS));
      CxlErrorLog.ErrorClass = AMD_ERROR;
      CxlErrorLog.ErrorInfo = CXL_ERROR_LOG_ID | CXL_SWITCH_ERROR | CXL_SWITCH_ENDPOINT_NOT_FOUND;
      CxlErrorLog.DataParam1 = CxlSwitch->PciLocation.AsBits.Segment;
      CxlErrorLog.DataParam2 = CxlSwitch->PciLocation.AsBits.Bus;
      AmdCxlErrorLog->CxlAddErrorLog (AmdCxlErrorLog, &CxlErrorLog);
    }
    goto ON_EXIT;
  }

  Status = CxlEndpointGetDecoderCapability (SwitchEndpoint, &CxlHdmDecoderCap, &CxlDecoderOffset);

  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_INFO, "CXL HDM Decoder Capability not found\n"));
    if (AmdCxlErrorLog != NULL) {
      ZeroMem (&CxlErrorLog, sizeof (ERROR_LOG_PARAMS));
      CxlErrorLog.ErrorClass = AMD_ERROR;
      CxlErrorLog.ErrorInfo = CXL_ERROR_LOG_ID | CXL_SWITCH_ERROR | CXL_SWITCH_HDM_DECODER_CAPABILITY_NOT_FOUND;
      CxlErrorLog.DataParam1 = SwitchEndpoint->PciLocation.AsBits.Segment;
      CxlErrorLog.DataParam2 = SwitchEndpoint->PciLocation.AsBits.Bus;
      AmdCxlErrorLog->CxlAddErrorLog (AmdCxlErrorLog, &CxlErrorLog);
    }
    goto ON_EXIT;
  }

  //
  // Program the decoders
  //
  DEBUG ((DEBUG_INFO, "HDM Decoder count = 0x%02x, Switch HDM decoder count = 0x%02x\n",
          CxlHdmDecoderCap.DecoderCount, CxlSwitch->InterleaveSetCount));

  if (CxlHdmDecoderCap.DecoderCount != 0) {
    ZeroMem (&CxlHdmDecoders, sizeof(AMD_CXL_HDM_DECODER) * MAX_HDM_DECODERS_PER_ENDPOINT);

    for (Index = 0; (Index < CxlSwitch->InterleaveSetCount) && (Index < CxlHdmDecoderCap.DecoderCount); Index++) {
      InterleaveSetSize = 0;
      InterleaveSetBase = 0;

      for (Index2 = 0; Index2 < CxlSwitch->InterleaveSet[Index].MemPoolCount; Index2++) {
        InterleaveSetSize += CxlSwitch->InterleaveSet[Index].MemPool[Index2].Size;
      }

      CxlHdmDecoders[Index].MemBaseLow = (UINT32) (InterleaveSetBase & 0xF0000000);
      CxlHdmDecoders[Index].MemBaseHigh = (UINT32) ((InterleaveSetBase & 0xFFFFFFFF00000000UL) >> 32);
      CxlHdmDecoders[Index].MemSizeLow = (UINT32) (InterleaveSetSize & 0xF0000000);
      CxlHdmDecoders[Index].MemSizeHigh = (UINT32) ((InterleaveSetSize & 0xFFFFFFFF00000000UL) >> 32);

      RegPtr = (volatile int *) (CxlDecoderOffset + (0x20*Index + CXL_HDM_DECODER_BASE_LOW_OFFSET));
      *RegPtr = (int) CxlHdmDecoders[Index].MemBaseLow;

      RegPtr = (volatile int *) (CxlDecoderOffset + (0x20*Index + CXL_HDM_DECODER_BASE_HIGH_OFFSET));
      *RegPtr = (int) CxlHdmDecoders[Index].MemBaseHigh;

      RegPtr = (volatile int *) (CxlDecoderOffset + (0x20*Index + CXL_HDM_DECODER_SIZE_LOW_OFFSET));
      *RegPtr = (int) CxlHdmDecoders[Index].MemSizeLow;

      RegPtr = (volatile int *) (CxlDecoderOffset + (0x20*Index + CXL_HDM_DECODER_SIZE_HIGH_OFFSET));
      *RegPtr = (int) CxlHdmDecoders[Index].MemSizeHigh;

      // Commit the decoder programming
      RegPtr = (volatile int *) (CxlDecoderOffset + (0x20*Index + CXL_HDM_DECODER_CONTROL_OFFSET));
      CxlHdmDecoderCtrlReg.Value = (UINT32) (*RegPtr);

      switch (CxlSwitch->InterleaveSet[Index].InterleaveWays) {
        case 2:  CxlHdmDecoderCtrlReg.Field.InterleaveWays = INTERLEAVE_2_WAY; break;   
        case 3:  CxlHdmDecoderCtrlReg.Field.InterleaveWays = INTERLEAVE_3_WAY; break;
        case 4:  CxlHdmDecoderCtrlReg.Field.InterleaveWays = INTERLEAVE_4_WAY; break;
        case 6:  CxlHdmDecoderCtrlReg.Field.InterleaveWays = INTERLEAVE_6_WAY; break;
        case 8:  CxlHdmDecoderCtrlReg.Field.InterleaveWays = INTERLEAVE_8_WAY; break;
        case 12: CxlHdmDecoderCtrlReg.Field.InterleaveWays = INTERLEAVE_12_WAY; break;
        case 16: CxlHdmDecoderCtrlReg.Field.InterleaveWays = INTERLEAVE_16_WAY; break;
        default: CxlHdmDecoderCtrlReg.Field.InterleaveWays = INTERLEAVE_1_WAY; break;
      }

      CxlHdmDecoderCtrlReg.Field.InterleaveWays = CxlSwitch->InterleaveSet[Index].InterleaveWays;
      CxlHdmDecoderCtrlReg.Field.InterleaveGranularity = CxlSwitch->InterleaveSet[Index].InterleaveGranularity;
      RegPtr = (volatile int *) (CxlDecoderOffset + (0x20*Index + CXL_HDM_DECODER_TARGET_LIST_LOW_OFFSET));
      CxlHdmDecoderTargetListLowReg.Value = (UINT32) (*RegPtr);
      RegPtr = (volatile int *) (CxlDecoderOffset + (0x20*Index + CXL_HDM_DECODER_TARGET_LIST_HIGH_OFFSET));
      CxlHdmDecoderTargetListHighReg.Value = (UINT32) (*RegPtr);
      CxlHdmDecoderTargetListLowReg.Field.TargetPortId0 = CxlSwitch->InterleaveSet[Index].TargetList[0];
      CxlHdmDecoderTargetListLowReg.Field.TargetPortId1 = CxlSwitch->InterleaveSet[Index].TargetList[1];
      CxlHdmDecoderTargetListLowReg.Field.TargetPortId2 = CxlSwitch->InterleaveSet[Index].TargetList[2];
      CxlHdmDecoderTargetListLowReg.Field.TargetPortId3 = CxlSwitch->InterleaveSet[Index].TargetList[3];
      CxlHdmDecoderTargetListHighReg.Field.TargetPortId0 = CxlSwitch->InterleaveSet[Index].TargetList[4];
      CxlHdmDecoderTargetListHighReg.Field.TargetPortId1 = CxlSwitch->InterleaveSet[Index].TargetList[5];
      CxlHdmDecoderTargetListHighReg.Field.TargetPortId2 = CxlSwitch->InterleaveSet[Index].TargetList[6];
      CxlHdmDecoderTargetListHighReg.Field.TargetPortId3 = CxlSwitch->InterleaveSet[Index].TargetList[7];
      RegPtr = (volatile int *) (CxlDecoderOffset + (0x20 + CXL_HDM_DECODER_TARGET_LIST_LOW_OFFSET));
      *RegPtr = (int) CxlHdmDecoderTargetListLowReg.Value;
      RegPtr = (volatile int *) (CxlDecoderOffset + (0x20 + CXL_HDM_DECODER_TARGET_LIST_HIGH_OFFSET));
      *RegPtr = (int) CxlHdmDecoderTargetListHighReg.Value;

      CxlHdmDecoderCtrlReg.Field.Commit = 1;
      *RegPtr = (int) CxlHdmDecoderCtrlReg.Value;

      // Confirm commit
      CxlHdmDecoderCtrlReg.Value = (UINT32) (*RegPtr);
      if ((CxlHdmDecoderCtrlReg.Field.Commit != 1) ||
          (CxlHdmDecoderCtrlReg.Field.Committed != 1) ||
          (CxlHdmDecoderCtrlReg.Field.ErrorNotCommited == 1)) {
        DEBUG ((DEBUG_INFO, "Error: CXL HDM Decoder was NOT committed!\n"));
        if (AmdCxlErrorLog != NULL) {
          ZeroMem (&CxlErrorLog, sizeof (ERROR_LOG_PARAMS));
          CxlErrorLog.ErrorClass = AMD_ERROR;
          CxlErrorLog.ErrorInfo = CXL_ERROR_LOG_ID | CXL_SWITCH_ERROR | CXL_SWITCH_HDM_DECODER_COMMIT_FAILED;
          CxlErrorLog.DataParam1 = SwitchEndpoint->PciLocation.AsBits.Segment;
          CxlErrorLog.DataParam2 = SwitchEndpoint->PciLocation.AsBits.Bus;
          AmdCxlErrorLog->CxlAddErrorLog (AmdCxlErrorLog, &CxlErrorLog);
        }
      }
    }

    //
    // Enable HDM Decoder
    //
    RegPtr = (volatile int *) (CxlDecoderOffset + (0x20*Index + CXL_HDM_DECODER_GLOBAL_CONTROL_OFFSET));
    CxlHdmDecoderGlobalCtrlReg = (UINT32) (*RegPtr);
    CxlHdmDecoderGlobalCtrlReg |= 1 << 1;
    *RegPtr = (int) CxlHdmDecoderGlobalCtrlReg;
  }

ON_EXIT:
  DEBUG((DEBUG_INFO, "%a - EXIT (Status = %r)\n", __FUNCTION__, Status));
  return Status;
}


/**
 * @brief CXL endpoint driver support for a CXL switch.
 *
 * @param[in] Endpoint Endpoint device structure.
 * @return VOID
 */
STATIC
VOID
CxlSwitchSupport (
  IN CXL_ENDPOINT *Endpoint
)
{
  EFI_STATUS                    Status;
  AMD_CXL_SWITCH_EP_INFO_STRUCT SwitchEndpoints[MAX_CXL_ENDPOINTS_PER_SWITCH];
  BOOLEAN                       IsSwitch;
  UINTN                         SwitchEndpointCount;
  BOOLEAN                       IsSwitchInterleaved;
  UINTN                         Index;

  ZeroMem (&SwitchEndpoints, sizeof(PCI_ADDR) * MAX_CXL_ENDPOINTS_PER_SWITCH);

  // Check if the endpoint is a switch or is attached to a switch
  Status = gCxlMgrProtocol->IsCxlSwitch (gCxlMgrProtocol,
                                         Endpoint->PciLocation,
                                         &IsSwitch,
                                         &SwitchEndpointCount,
                                         &SwitchEndpoints[0],
                                         &IsSwitchInterleaved
                                        );

  if (!EFI_ERROR (Status)) {
    if (IsSwitch) {
      Endpoint->IsSwitch = TRUE; // The CXL device is a CXL switch
      Endpoint->IsInterleaved = IsSwitchInterleaved;
      gCxlSwitchList[gCxlSwitchCount].EndpointTotal = SwitchEndpointCount;
      gCxlSwitchList[gCxlSwitchCount].DeviceId = Endpoint->DeviceId;
      gCxlSwitchList[gCxlSwitchCount].VendorId = Endpoint->VendorId;
      gCxlSwitchList[gCxlSwitchCount].PciLocation = Endpoint->PciLocation;
      gCxlSwitchList[gCxlSwitchCount].StartPciIo = Endpoint->StartPciIo;
      gCxlSwitchList[gCxlSwitchCount].EndpointCount = 0;
      for (Index = 0; Index < SwitchEndpointCount; Index++) {
        gCxlSwitchList[gCxlSwitchCount].CxlEndpoints[Index].PciLocation.AsBits.Bus = SwitchEndpoints[Index].PciAddress.Address.Bus;
        gCxlSwitchList[gCxlSwitchCount].CxlEndpoints[Index].PciLocation.AsBits.Device = SwitchEndpoints[Index].PciAddress.Address.Device;
        gCxlSwitchList[gCxlSwitchCount].CxlEndpoints[Index].PciLocation.AsBits.Function = SwitchEndpoints[Index].PciAddress.Address.Function;
        gCxlSwitchList[gCxlSwitchCount].CxlEndpoints[Index].PciLocation.AsBits.Segment = SwitchEndpoints[Index].PciAddress.Address.Segment;
        gCxlSwitchList[gCxlSwitchCount].CxlEndpoints[Index].RootBridgeLocation.AsBits.Bus = SwitchEndpoints[Index].RootPortAddress.Address.Bus;
        gCxlSwitchList[gCxlSwitchCount].CxlEndpoints[Index].RootBridgeLocation.AsBits.Device = SwitchEndpoints[Index].RootPortAddress.Address.Device;
        gCxlSwitchList[gCxlSwitchCount].CxlEndpoints[Index].RootBridgeLocation.AsBits.Function = SwitchEndpoints[Index].RootPortAddress.Address.Function;
        gCxlSwitchList[gCxlSwitchCount].CxlEndpoints[Index].RootBridgeLocation.AsBits.Segment = SwitchEndpoints[Index].RootPortAddress.Address.Segment;
      }
      gCxlSwitchCount++;
    } else {
      Endpoint->IsSwitch = FALSE;           // The CXL device is not a switch
      Endpoint->IsSwitchEndpoint = TRUE;    // The CXL device is downstream of a switch
    }
  }
}


/**
 * @brief Programs the switch downstream port CXL Extensions DVSEC (ID 3)
 *
 * @param[in] CxlEndpoint   CXL_ENDPOINT data
 * @return EFI_STATUS
 */
EFI_STATUS
CxlSwitchDsExtDvsecProgramming (
  IN CXL_ENDPOINT *CxlEndpoint
)
{
  EFI_STATUS            Status;
  UINT32                Value32;
  UINT64                MemBase;
  UINT64                MemSize;
  UINT64                MemLimit;
  UINT8                 BusBase;
  UINT8                 BusLimit;
  CXL_EXTENSIONS_DVSEC  CxlExtensionsDvsec;

  DEBUG ((DEBUG_INFO, "%a - ENTRY\n", __FUNCTION__));

  MemSize = 0;
  MemBase = 0xFFFFFFFFFFFFFFFFULL;
  BusBase = 0;
  BusLimit = 0;

  MemBase = CxlEndpoint->MemPool[0].Base;
  MemSize = CxlEndpoint->MemPool[0].Size + CxlEndpoint->MemPool[1].Size;

  Status = CxlEndpoint->RootBridgePciIo->Pci.Read (
    CxlEndpoint->RootBridgePciIo,
    EfiPciIoWidthUint32,
    PCI_BRIDGE_PRIMARY_BUS_REGISTER_OFFSET,
    sizeof (Value32) / sizeof (UINT32),
    (VOID *)&Value32
    );
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "%a - Error: PCI IO read failed! Status = %r\n", __FUNCTION__, Status));
    return Status;
  }

  BusBase = (UINT8)((Value32 & 0x0000ff00) >> 8);
  BusLimit = (UINT8)((Value32 & 0x00ff0000) >> 16);
  MemLimit = ((MemBase + MemSize) - 1);

  CxlExtensionsDvsec.AltBusBase = BusBase;
  CxlExtensionsDvsec.AltBusLimit = BusLimit;
  CxlExtensionsDvsec.AltPrefetchMemBase = (UINT16) ((MemBase & 0x00000000FFF00000ULL) >> 16);
  CxlExtensionsDvsec.AltPrefetchMemBaseHigh = (UINT32) ((MemBase & 0xFFFFFFFF00000000ULL) >> 32);
  CxlExtensionsDvsec.AltPrefetchMemLimit = (UINT16) ((MemLimit & 0x00000000FFF00000ULL) >> 16);
  CxlExtensionsDvsec.AltPrefetchMemLimitHigh = (UINT32) ((MemLimit & 0xFFFFFFFF00000000ULL) >> 32);

  Status = CxlProgramExtensionsDvsecForPorts (CxlEndpoint->RootBridgePciIo, &CxlExtensionsDvsec);

  DEBUG((DEBUG_INFO, "%a - EXIT (Status = %r)\n", __FUNCTION__, Status));
  return Status;
}


/**
 * @brief Programs the switch upstream port CXL Extensions DVSEC (ID 3)
 *
 * @param[in] CxlSwitch   CXL_SWITCH data
 * @return EFI_STATUS
 */
EFI_STATUS
CxlSwitchUsExtDvsecProgramming (
  IN  CXL_SWITCH  *CxlSwitch
)
{
  EFI_STATUS            Status;
  UINTN                 Index;
  UINTN                 Index2;
  UINT32                Value32;
  UINT64                MemBase;
  UINT64                MemSize;
  UINT64                MemLimit;
  UINT8                 BusBase;
  UINT8                 BusLimit;
  CXL_EXTENSIONS_DVSEC  CxlExtensionsDvsec;

  DEBUG ((DEBUG_INFO, "%a - ENTRY\n", __FUNCTION__));

  MemSize = 0;
  MemBase = 0xFFFFFFFFFFFFFFFFULL;
  BusBase = 0;
  BusLimit = 0;

  for (Index = 0; Index < CxlSwitch->InterleaveSetCount; Index++) {
    for (Index2 = 0; Index2 < CxlSwitch->InterleaveSet[Index].MemPoolCount; Index2++) {
      MemSize += CxlSwitch->InterleaveSet[Index].MemPool[Index2].Size;
      if (CxlSwitch->InterleaveSet[Index].MemPool[Index2].Base < MemBase) {
        MemBase = CxlSwitch->InterleaveSet[Index].MemPool[Index2].Base;
      }
    }
  }

  Status = CxlSwitch->StartPciIo->Pci.Read (
    CxlSwitch->StartPciIo,
    EfiPciIoWidthUint32,
    PCI_BRIDGE_PRIMARY_BUS_REGISTER_OFFSET,
    sizeof (Value32) / sizeof (UINT32),
    (VOID *)&Value32
    );
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "%a - Error: PCI IO read failed! Status = %r\n", __FUNCTION__, Status));
    return Status;
  }

  BusBase = (UINT8)((Value32 & 0x0000ff00) >> 8);
  BusLimit = (UINT8)((Value32 & 0x00ff0000) >> 16);
  MemLimit = ((MemBase + MemSize) - 1);

  CxlExtensionsDvsec.AltBusBase = BusBase;
  CxlExtensionsDvsec.AltBusLimit = BusLimit;
  CxlExtensionsDvsec.AltPrefetchMemBase = (UINT16) ((MemBase & 0x00000000FFF00000ULL) >> 16);
  CxlExtensionsDvsec.AltPrefetchMemBaseHigh = (UINT32) ((MemBase & 0xFFFFFFFF00000000ULL) >> 32);
  CxlExtensionsDvsec.AltPrefetchMemLimit = (UINT16) ((MemLimit & 0x00000000FFF00000ULL) >> 16);
  CxlExtensionsDvsec.AltPrefetchMemLimitHigh = (UINT32) ((MemLimit & 0xFFFFFFFF00000000ULL) >> 32);

  Status = CxlProgramExtensionsDvsecForPorts (CxlSwitch->StartPciIo, &CxlExtensionsDvsec);

  DEBUG((DEBUG_INFO, "%a - EXIT (Status = %r)\n", __FUNCTION__, Status));
  return Status;
}


/**
 * @brief Programs and enables a CXL switch.
 *
 * @param CxlSwitch
 * @return EFI_STATUS
 */
EFI_STATUS
CxlSwitchEnable (
  IN  CXL_SWITCH  *CxlSwitch
)
{
  EFI_STATUS    Status;
  CXL_ENDPOINT  *SwitchEndpoint;
  UINTN         Index;
  UINTN         Index2;
  UINTN         Index3;
  BOOLEAN       FoundInterleaveSet;
  BOOLEAN       MemPoolMatch;
  UINT64        InterleaveSetSize;

  DEBUG ((DEBUG_INFO, "%a - ENTRY\n", __FUNCTION__));
  Status = EFI_NOT_FOUND;
  SwitchEndpoint = NULL;

  for (Index = 0; Index < gCxlEndpointCount; Index++) {
    if (gCxlEndpointList[Index].IsSwitch) {
      if (gCxlEndpointList[Index].PciLocation.AsUint32 == CxlSwitch->PciLocation.AsUint32) {
        SwitchEndpoint = &gCxlEndpointList[Index];
        break;
      }
    }
  }

  if (SwitchEndpoint != NULL) {
    SwitchEndpoint->MemPoolCount = 0;
    CxlSwitch->InterleaveSetCount = 0;
    ZeroMem (&CxlSwitch->InterleaveSet, sizeof(CXL_SWITCH_INTERLEAVE_SET) * MAX_CXL_ENDPOINTS_PER_SWITCH);

    for (Index = 0; Index < CxlSwitch->EndpointTotal; Index++) {
      FoundInterleaveSet = FALSE;
      if (SwitchEndpoint->IsInterleaved) {
        /*
          Interleave set conditions:
            1. Equal number of memory ranges
            2. Ranges have equal memory sizes and the size is greater than 0
            3. Same memory type
            4. Interleave ways is equal to 1, 2, 3, 4, 6, 8, 12, 16
        */
        for (Index2 = 0; Index2 < CxlSwitch->InterleaveSetCount; Index2++) {
          MemPoolMatch = TRUE;
          if (CxlSwitch->CxlEndpoints[Index].MemPoolCount == CxlSwitch->InterleaveSet[Index2].MemPoolCount) {
            for (Index3 = 0; Index3 < CxlSwitch->CxlEndpoints[Index].MemPoolCount; Index3++) {
              if(CxlSwitch->CxlEndpoints[Index].MemPool[Index3].Size > 0) {
                if ((CxlSwitch->InterleaveSet[Index2].MemPool[Index3].Size != CxlSwitch->CxlEndpoints[Index].MemPool[Index3].Size) ||
                    (CxlSwitch->InterleaveSet[Index2].MemPool[Index3].EfiType != CxlSwitch->CxlEndpoints[Index].MemPool[Index3].EfiType)) {
                  MemPoolMatch = FALSE;
                  break;
                }
              }
            }
            if (MemPoolMatch) {
              CxlSwitch->CxlEndpoints[Index].IsInterleaved = TRUE;
              CxlSwitch->InterleaveSet[Index2].TargetList[CxlSwitch->InterleaveSet[Index2].InterleaveWays] = CxlSwitch->CxlEndpoints[Index].PortId;
              CxlSwitch->InterleaveSet[Index2].InterleaveWays++;
              if (CxlSwitch->CxlEndpoints[Index].DesiredInterleave > CxlSwitch->InterleaveSet[Index2].InterleaveGranularity) {
                CxlSwitch->InterleaveSet[Index2].InterleaveGranularity = CxlSwitch->CxlEndpoints[Index].DesiredInterleave;
              }
              FoundInterleaveSet = TRUE;
            }
          }
        }
      }

      if (!FoundInterleaveSet) {
        CopyMem (&CxlSwitch->InterleaveSet[CxlSwitch->InterleaveSetCount].MemPool,
                 &CxlSwitch->CxlEndpoints[Index].MemPool,
                 sizeof(AMD_CXL_MEMORY_POOL)*MAX_CXL_MEM_POOLS_PER_ENDPOINT);
        CxlSwitch->InterleaveSet[CxlSwitch->InterleaveSetCount].MemPoolCount = CxlSwitch->CxlEndpoints[Index].MemPoolCount;
        CxlSwitch->InterleaveSet[CxlSwitch->InterleaveSetCount].InterleaveWays = 1;
        CxlSwitch->InterleaveSet[CxlSwitch->InterleaveSetCount].InterleaveGranularity = CxlSwitch->CxlEndpoints[Index].DesiredInterleave;
        CxlSwitch->InterleaveSet[CxlSwitch->InterleaveSetCount].TargetList[0] = CxlSwitch->CxlEndpoints[Index].PortId;
        CxlSwitch->InterleaveSetCount++;
      }
    }

    SwitchEndpoint->MemPoolCount = CxlSwitch->InterleaveSetCount;

    for (Index = 0; Index < SwitchEndpoint->MemPoolCount; Index++) {
      InterleaveSetSize = 0;
      for (Index2 = 0; Index2 < CxlSwitch->InterleaveSet[Index].MemPoolCount; Index2++) {
        InterleaveSetSize += CxlSwitch->InterleaveSet[Index].MemPool[Index2].Size;
      }
      InterleaveSetSize *= CxlSwitch->InterleaveSet[Index].InterleaveWays;
      SwitchEndpoint->MemPool[Index].Size = InterleaveSetSize;
    }

    for (Index = 0; Index < CxlSwitch->EndpointTotal; Index++) {
      CxlEndpointDecoderProgramming (&CxlSwitch->CxlEndpoints[Index]);
    }

    Status = CxlSwitchDecoderProgramming (CxlSwitch);
    if (Status == EFI_NOT_FOUND) {
      // Program DVSEC 3 in the upstream port
      Status = CxlSwitchUsExtDvsecProgramming (CxlSwitch);
      if (Status == EFI_SUCCESS) {
        // Program DVSEC 3 in the downstream ports
        for (Index = 0; Index < CxlSwitch->EndpointTotal; Index++) {
          Status = CxlSwitchDsExtDvsecProgramming (&CxlSwitch->CxlEndpoints[Index]);
          if (EFI_ERROR (Status)) {
            DEBUG ((DEBUG_ERROR, "Error: Switch downstream port 0x%08x DVSEC 3 programming failed! Status = %r\n",
                    CxlSwitch->CxlEndpoints[Index].RootBridgeLocation.AsUint32, Status));
          }
        }
      }
    }
  }

  DumpCxlSwitch (CxlSwitch);
  DEBUG((DEBUG_INFO, "%a - EXIT (Status = %r)\n", __FUNCTION__, Status));
  return Status;
}

/**
 * @brief This routine is called from the Start() function of Driver Binding protocol
 *        to enable CXL device.
 *
 * @param[in] PciIo             PciIo instance.
 *
 * @retval EFI_SUCCESS          CXL device is enabled.
 * @retval other errors         CXL device fails to enable.
 */
EFI_STATUS
CxlEndpointEnable (
  IN   EFI_PCI_IO_PROTOCOL  *PciIo
  )
{
  CXL_ENDPOINT        *Endpoint;
  EFI_STATUS          Status;
  UINTN               PciSegment;
  UINTN               PciBus;
  UINTN               PciDevice;
  UINTN               PciFunction;
  UINTN               Index;
  UINTN               Index2;
  AMD_CXL_CSL         ComponentStateDumpLog;
  EFI_PCI_IO_PROTOCOL *TempPciIo;
  ERROR_LOG_PARAMS    CxlErrorLog;
  AMD_CXL_ERROR_LOG_PROTOCOL *AmdCxlErrorLog = NULL;

  DEBUG ((DEBUG_INFO, "%a - ENTRY\n", __FUNCTION__));

  // Get PCI Location
  Status = PciIo->GetLocation (
    PciIo,
    &PciSegment,
    &PciBus,
    &PciDevice,
    &PciFunction
    );
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "%a: Error: PciIo->GetLocation() failed (Status = %r).\n", __FUNCTION__, Status));
    return Status;
  }

  Status = gBS->LocateProtocol (&gAmdCxlErrorLogProtocolGuid, 
                                NULL, 
                                (VOID **)&AmdCxlErrorLog
                                );
  if (EFI_ERROR (Status)) {
    DEBUG((DEBUG_ERROR, "%a: Failed to locate AmdCxlErrorLogProtocol! Status = %r\n", __FUNCTION__, Status));
  }

  // If the PCI location is a switch downstream port, save the PCI IO pointer to the switch data structure
  // We need it later to program the downstream port DVSEC 3 with the endpoint data
  for (Index = 0; Index < gCxlSwitchCount; Index++) {
    for (Index2 = 0; Index2 < gCxlSwitchList[Index].EndpointTotal; Index2++) {
      if (gCxlSwitchList[Index].CxlEndpoints[Index2].RootBridgeLocation.AsBits.Bus == (UINT32) PciBus &&
          gCxlSwitchList[Index].CxlEndpoints[Index2].RootBridgeLocation.AsBits.Segment == (UINT32) PciSegment) {
        gCxlSwitchList[Index].CxlEndpoints[Index2].RootBridgePciIo = PciIo;
        return EFI_SUCCESS;
      }
    }
  }

  Endpoint = &gCxlEndpointList[gCxlEndpointCount];
  Endpoint->StartPciIo = PciIo;
  Endpoint->PciLocation.AsBits.Segment = (UINT32) PciSegment;
  Endpoint->PciLocation.AsBits.Bus = (UINT32) PciBus;
  Endpoint->PciLocation.AsBits.Device = (UINT32) PciDevice;
  Endpoint->PciLocation.AsBits.Function = (UINT32) PciFunction;
  Endpoint->IsInterleaved = FALSE;
  Endpoint->DesiredInterleave = 0;
  Endpoint->InterleaveWays = 0;

  Status = CxlPcieGetEndpointId (PciIo, Endpoint);
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "%a: Error: CxlPcieGetEndpointId failed.\n", __FUNCTION__));
    return Status;
  }

  CxlSwitchSupport (Endpoint);

  Status = CxlEndpointConfigure (Endpoint);
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "%a: Error: CxlEndpointConfigure failed.\n", __FUNCTION__));
    return Status;
  }

  if (!Endpoint->IsSwitch)
  {
    Status = CxlEndpointGetHdmRanges (Endpoint);
    if (EFI_ERROR (Status)) {
      DEBUG ((DEBUG_ERROR, "%a: Error: CxlEndpointGetHdmRanges failed.\n", __FUNCTION__));
      if (AmdCxlErrorLog != NULL) {
        ZeroMem (&CxlErrorLog, sizeof (ERROR_LOG_PARAMS));
        CxlErrorLog.ErrorClass = AMD_ERROR;
        CxlErrorLog.ErrorInfo = CXL_ERROR_LOG_ID | CXL_ENDPOINT_INIT_CONFIG | CXL_GET_HDM_DECODER_RANGE_ERROR;
        CxlErrorLog.DataParam1 = Endpoint->PciLocation.AsBits.Segment;
        CxlErrorLog.DataParam2 = Endpoint->PciLocation.AsBits.Bus;
        AmdCxlErrorLog->CxlAddErrorLog (AmdCxlErrorLog, &CxlErrorLog);
      }
      return Status;
    }

    // Enable CXL Devices
    // NOTE: A CXL Device is represented by PrimaryPort-enabled Endpoint
    Status = CxlEndpointEnableMemoryPools (Endpoint);
    if (EFI_ERROR (Status)) {
      DEBUG ((DEBUG_ERROR, "%a: Error: CxlEndpointEnableMemoryPools failed.\n", __FUNCTION__));
      if (AmdCxlErrorLog != NULL) {
        ZeroMem (&CxlErrorLog, sizeof (ERROR_LOG_PARAMS));
        CxlErrorLog.ErrorClass = AMD_ERROR;
        CxlErrorLog.ErrorInfo = CXL_ERROR_LOG_ID | CXL_ENDPOINT_INIT_CONFIG | CXL_MEMORY_ENABLED_ERROR;
        CxlErrorLog.DataParam1 = Endpoint->PciLocation.AsBits.Segment;
        CxlErrorLog.DataParam2 = Endpoint->PciLocation.AsBits.Bus;
        AmdCxlErrorLog->CxlAddErrorLog (AmdCxlErrorLog, &CxlErrorLog);
      }
    } else {
      CxlEndpointCheckDeviceStatus (Endpoint);
    }

    if (!Endpoint->IsSwitchEndpoint) {
      CxlEndpointDecoderProgramming (Endpoint);
    }
    CxlCciInit (Endpoint);
    CxlQosTelemetryInit (Endpoint);
    GetComponentStateDumpLog (Endpoint, &ComponentStateDumpLog);
    DumpCxlEndpoint (Endpoint);
  }

  // If this endpoint is attached to a switch, update the switch data structure with the endpoint data
  if (Endpoint->IsSwitchEndpoint) {
    for (Index = 0; Index < gCxlSwitchCount; Index++) {
      for (Index2 = 0; Index2 < gCxlSwitchList[Index].EndpointTotal; Index2++) {
        if (gCxlSwitchList[Index].CxlEndpoints[Index2].PciLocation.AsUint32 == Endpoint->PciLocation.AsUint32) {
          gCxlSwitchList[Index].EndpointCount++;
          TempPciIo = gCxlSwitchList[Index].CxlEndpoints[Index2].RootBridgePciIo;                 // Preserve the endpoint's PCI IO pointer
          CopyMem (&gCxlSwitchList[Index].CxlEndpoints[Index2], Endpoint, sizeof (CXL_ENDPOINT)); // Copy the endpoint
          gCxlSwitchList[Index].CxlEndpoints[Index2].RootBridgePciIo = TempPciIo;                 // Restore the endpoint's PCI IO pointer
          // If all endpoints downstream of the switch have been found, enable the switch
          if (gCxlSwitchList[Index].EndpointCount == gCxlSwitchList[Index].EndpointTotal) {
            CxlSwitchEnable (&gCxlSwitchList[Index]);
            Index = gCxlSwitchCount;
            break;
          }
        }
      }
    }
  }

  // Increment CXL endpoint count
  ++gCxlEndpointCount;

  return Status;
}
