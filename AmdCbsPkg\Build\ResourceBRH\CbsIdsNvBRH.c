/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */
//This file is auto generated, do not edit it manually

#include "AmdCbsVariable.h"
#include "IdsNvIdBRH.h"
#include "IdsNvTable.h"

UINT32 GetIdsNvRecordsSize ();

#pragma pack(1)
typedef struct {
  IDS_NV_ID     Id;
  UINT16        Offset;
} IDS_NV_RECORD;
#pragma pack()

#ifndef OFFSET_OF
  #define OFFSET_OF(type, member) ((UINT8) &(((type*)0)->member))
#endif

STATIC IDS_NV_RECORD mIdsNv8[] = {
  //SMT Control
  {IDSNVID_CPU_SMT_CTRL, OFFSET_OF(CBS_CONFIG,CbsCpuSmtCtrl)},
  //Enable Requested CPU min frequency
  {IDSNVID_CMN_CPU_EN_REQ_MIN_FREQ, OFFSET_OF(CBS_CONFIG,CbsCmnCpuEnReqMinFreq)},
  //REP-MOV/STOS Streaming
  {IDSNVID_CMN_CPU_RMSS, OFFSET_OF(CBS_CONFIG,CbsCmnCpuRMSS)},
  //RedirectForReturnDis
  {IDSNVID_CMN_CPU_GEN_W_A05, OFFSET_OF(CBS_CONFIG,CbsCmnCpuGenWA05)},
  //Platform First Error Handling
  {IDSNVID_CMN_CPU_PFEH, OFFSET_OF(CBS_CONFIG,CbsCmnCpuPfeh)},
  //Core Performance Boost
  {IDSNVID_CMN_CPU_CPB, OFFSET_OF(CBS_CONFIG,CbsCmnCpuCpb)},
  //Global C-state Control
  {IDSNVID_CMN_CPU_GLOBAL_CSTATE_CTRL, OFFSET_OF(CBS_CONFIG,CbsCmnCpuGlobalCstateCtrl)},
  //Power Supply Idle Control
  {IDSNVID_CMN_GNB_POWER_SUPPLY_IDLE_CTRL, OFFSET_OF(CBS_CONFIG,CbsCmnGnbPowerSupplyIdleCtrl)},
  //Streaming Stores Control
  {IDSNVID_CMN_CPU_STREAMING_STORES_CTRL, OFFSET_OF(CBS_CONFIG,CbsCmnCpuStreamingStoresCtrl)},
  //Local APIC Mode
  {IDSNVID_DBG_CPU_L_APIC_MODE, OFFSET_OF(CBS_CONFIG,CbsDbgCpuLApicMode)},
  //ACPI _CST C1 Declaration
  {IDSNVID_CMN_CPU_CST_C1_CTRL, OFFSET_OF(CBS_CONFIG,CbsCmnCpuCstC1Ctrl)},
  //MCA error thresh enable
  {IDSNVID_CMN_CPU_MCA_ERR_THRESH_EN, OFFSET_OF(CBS_CONFIG,CbsCmnCpuMcaErrThreshEn)},
  //MCA FruText
  {IDSNVID_CMN_CPU_MCA_FRU_TEXT_EN, OFFSET_OF(CBS_CONFIG,CbsCmnCpuMcaFruTextEn)},
  //SMU and PSP Debug Mode
  {IDSNVID_CMN_CPU_SMU_PSP_DEBUG_MODE, OFFSET_OF(CBS_CONFIG,CbsCmnCpuSmuPspDebugMode)},
  //PPIN Opt-in
  {IDSNVID_CMN_CPU_PPIN_CTRL, OFFSET_OF(CBS_CONFIG,CbsCmnCpuPpinCtrl)},
  //SMEE
  {IDSNVID_CMN_CPU_SMEE, OFFSET_OF(CBS_CONFIG,CbsCmnCpuSmee)},
  //SEV Control
  {IDSNVID_PSP_SEV_CTRL, OFFSET_OF(CBS_CONFIG,CbsPspSevCtrl)},
  //SNP Memory (RMP Table) Coverage
  {IDSNVID_DBG_CPU_SNP_MEM_COVER, OFFSET_OF(CBS_CONFIG,CbsDbgCpuSnpMemCover)},
  //RMP Coverage for 64Bit MMIO Ranges
  {IDSNVID_CMN_CPU64_BIT_MMIO_COVERAGE, OFFSET_OF(CBS_CONFIG,CbsCmnCpu64BitMMIOCoverage)},
  //Split RMP Table
  {IDSNVID_DBG_CPU_SPLIT_RMP, OFFSET_OF(CBS_CONFIG,CbsDbgCpuSplitRMP)},
  //Segmented RMP Table
  {IDSNVID_DBG_CPU_SEGMENTED_RMP, OFFSET_OF(CBS_CONFIG,CbsDbgCpuSegmentedRMP)},
  //RMP Segment Size
  {IDSNVID_DBG_CPU_RMP_SEGMENT_SIZE, OFFSET_OF(CBS_CONFIG,CbsDbgCpuRmpSegmentSize)},
  //Action on BIST Failure
  {IDSNVID_CMN_ACTION_ON_BIST_FAILURE, OFFSET_OF(CBS_CONFIG,CbsCmnActionOnBistFailure)},
  //Enhanced REP MOVSB/STOSB (ERSM)
  {IDSNVID_CMN_CPU_ERMS, OFFSET_OF(CBS_CONFIG,CbsCmnCpuERMS)},
  //Log Transparent Errors
  {IDSNVID_CMN_CPU_LOG_TRANSPARENT_ERRORS, OFFSET_OF(CBS_CONFIG,CbsCmnCpuLogTransparentErrors)},
  //AVX512
  {IDSNVID_CMN_CPU_AVX512, OFFSET_OF(CBS_CONFIG,CbsCmnCpuAvx512)},
  //ERMSB Caching Behavior
  {IDSNVID_CMN_CPU_DIS_FST_STR_ERMSB, OFFSET_OF(CBS_CONFIG,CbsCmnCpuDisFstStrErmsb)},
  //MONITOR and MWAIT disable
  {IDSNVID_CMN_CPU_MON_MWAIT_DIS, OFFSET_OF(CBS_CONFIG,CbsCmnCpuMonMwaitDis)},
  //CPU Speculative Store Modes
  {IDSNVID_CPU_SPECULATIVE_STORE_MODES, OFFSET_OF(CBS_CONFIG,CbsCpuSpeculativeStoreModes)},
  //Fast Short REP MOVSB (FSRM)
  {IDSNVID_CMN_CPU_FSRM, OFFSET_OF(CBS_CONFIG,CbsCmnCpuFSRM)},
  //PauseCntSel_1_0
  {IDSNVID_CMN_CPU_PAUSE_CNT_SEL_1_0, OFFSET_OF(CBS_CONFIG,CbsCmnCpuPauseCntSel_1_0)},
  //Prefetch/Request Throttle
  {IDSNVID_CMN_CPU_PF_REQ_THR_EN, OFFSET_OF(CBS_CONFIG,CbsCmnCpuPfReqThrEn)},
  //CMC H/W Error Notification type
  {IDSNVID_CMN_CMC_NOTIFICATION_TYPE, OFFSET_OF(CBS_CONFIG,CbsCmnCmcNotificationType)},
  //Scan Dump Debug Enable
  {IDSNVID_CMN_CPU_SCAN_DUMP_DBG_EN, OFFSET_OF(CBS_CONFIG,CbsCmnCpuScanDumpDbgEn)},
  //MCAX 64 bank support
  {IDSNVID_CMN_CPU_MCAX64_BANK_SUPPORT, OFFSET_OF(CBS_CONFIG,CbsCmnCpuMcax64BankSupport)},
  //Adaptive Allocation (AA)
  {IDSNVID_CMN_CPU_ADAPTIVE_ALLOC, OFFSET_OF(CBS_CONFIG,CbsCmnCpuAdaptiveAlloc)},
  //Latency Under Load (LUL)
  {IDSNVID_CPU_LATENCY_UNDER_LOAD, OFFSET_OF(CBS_CONFIG,CbsCpuLatencyUnderLoad)},
  //Core Trace Dump Enable
  {IDSNVID_CMN_CORE_TRACE_DUMP_EN, OFFSET_OF(CBS_CONFIG,CbsCmnCoreTraceDumpEn)},
  //FP512
  {IDSNVID_CMN_CPU_F_P512, OFFSET_OF(CBS_CONFIG,CbsCmnCpuFP512)},
  //AMD_ERMSB Reporting
  {IDSNVID_CMN_CPU_AMD_ERMSB_REPO, OFFSET_OF(CBS_CONFIG,CbsCmnCpuAmdErmsbRepo)},
  //OC Mode
  {IDSNVID_CMN_CPU_OC_MODE, OFFSET_OF(CBS_CONFIG,CbsCmnCpuOcMode)},
  //DownCore Mode
  {IDSNVID_CMN_CPU_DOWNCORE_MODE, OFFSET_OF(CBS_CONFIG,CbsCmnCpuDowncoreMode)},
  //Pstates Disclaimer
  {IDSNVID_CPU_LEGAL_DISCLAIMER, OFFSET_OF(CBS_CONFIG,CbsCpuLegalDisclaimer)},
  //Pstates Disclaimer 1
  {IDSNVID_CPU_LEGAL_DISCLAIMER1, OFFSET_OF(CBS_CONFIG,CbsCpuLegalDisclaimer1)},
  //Custom Pstate0
  {IDSNVID_CPU_PST_CUSTOM_P0, OFFSET_OF(CBS_CONFIG,CbsCpuPstCustomP0)},
  //Custom Pstate1
  {IDSNVID_CPU_PST_CUSTOM_P1, OFFSET_OF(CBS_CONFIG,CbsCpuPstCustomP1)},
  //Custom Pstate2
  {IDSNVID_CPU_PST_CUSTOM_P2, OFFSET_OF(CBS_CONFIG,CbsCpuPstCustomP2)},
  //Custom Pstate3
  {IDSNVID_CPU_PST_CUSTOM_P3, OFFSET_OF(CBS_CONFIG,CbsCpuPstCustomP3)},
  //Custom Pstate4
  {IDSNVID_CPU_PST_CUSTOM_P4, OFFSET_OF(CBS_CONFIG,CbsCpuPstCustomP4)},
  //Custom Pstate5
  {IDSNVID_CPU_PST_CUSTOM_P5, OFFSET_OF(CBS_CONFIG,CbsCpuPstCustomP5)},
  //Custom Pstate6
  {IDSNVID_CPU_PST_CUSTOM_P6, OFFSET_OF(CBS_CONFIG,CbsCpuPstCustomP6)},
  //Custom Pstate7
  {IDSNVID_CPU_PST_CUSTOM_P7, OFFSET_OF(CBS_CONFIG,CbsCpuPstCustomP7)},
  //CCD Control
  {IDSNVID_CPU_CCD_CTRL, OFFSET_OF(CBS_CONFIG,CbsCpuCcdCtrl)},
  //Core control
  {IDSNVID_CPU_CORE_CTRL, OFFSET_OF(CBS_CONFIG,CbsCpuCoreCtrl)},
  //L1 Stream HW Prefetcher
  {IDSNVID_CMN_CPU_L1_STREAM_HW_PREFETCHER, OFFSET_OF(CBS_CONFIG,CbsCmnCpuL1StreamHwPrefetcher)},
  //L1 Stride Prefetcher
  {IDSNVID_CMN_CPU_L1_STRIDE_PREFETCHER, OFFSET_OF(CBS_CONFIG,CbsCmnCpuL1StridePrefetcher)},
  //L1 Region Prefetcher
  {IDSNVID_CMN_CPU_L1_REGION_PREFETCHER, OFFSET_OF(CBS_CONFIG,CbsCmnCpuL1RegionPrefetcher)},
  //L2 Stream HW Prefetcher
  {IDSNVID_CMN_CPU_L2_STREAM_HW_PREFETCHER, OFFSET_OF(CBS_CONFIG,CbsCmnCpuL2StreamHwPrefetcher)},
  //L2 Up/Down Prefetcher
  {IDSNVID_CMN_CPU_L2_UP_DOWN_PREFETCHER, OFFSET_OF(CBS_CONFIG,CbsCmnCpuL2UpDownPrefetcher)},
  //L1 Burst Prefetch Mode
  {IDSNVID_CMN_CPU_L1_BURST_PREFETCH_MODE, OFFSET_OF(CBS_CONFIG,CbsCmnCpuL1BurstPrefetchMode)},
  //Core Watchdog Timer Enable
  {IDSNVID_DBG_CPU_GEN_CPU_WDT, OFFSET_OF(CBS_CONFIG,CbsDbgCpuGenCpuWdt)},
  //DF Watchdog Timer Interval
  {IDSNVID_DF_CMN_WDT_INTERVAL, OFFSET_OF(CBS_CONFIG,CbsDfCmnWdtInterval)},
  //Disable DF to external IP SyncFloodPropagation
  {IDSNVID_DF_CMN_EXT_IP_SYNC_FLOOD_PROP, OFFSET_OF(CBS_CONFIG,CbsDfCmnExtIpSyncFloodProp)},
  //Sync Flood Propagation to DF Components
  {IDSNVID_DF_CMN_DIS_SYNC_FLOOD_PROP, OFFSET_OF(CBS_CONFIG,CbsDfCmnDisSyncFloodProp)},
  //Freeze DF module queues on error
  {IDSNVID_DF_CMN_FREEZE_QUEUE_ERROR, OFFSET_OF(CBS_CONFIG,CbsDfCmnFreezeQueueError)},
  //CC6 memory region encryption
  {IDSNVID_DF_CMN_CC6_MEM_ENCRYPTION, OFFSET_OF(CBS_CONFIG,CbsDfCmnCc6MemEncryption)},
  //CCD B/W Balance Throttle Level
  {IDSNVID_DF_CMN_CCD_BW_THROTTLE_LV, OFFSET_OF(CBS_CONFIG,CbsDfCmnCcdBwThrottleLv)},
  //CCM Throttler
  {IDSNVID_DF_CMN_CCM_THROT, OFFSET_OF(CBS_CONFIG,CbsDfCmnCcmThrot)},
  //MemReqBandwidthControl[FineThrotHeavy]
  {IDSNVID_DF_CMN_FINE_THROT_HEAVY, OFFSET_OF(CBS_CONFIG,CbsDfCmnFineThrotHeavy)},
  //MemReqBandwidthControl[FineThrotLight]
  {IDSNVID_DF_CMN_FINE_THROT_LIGHT, OFFSET_OF(CBS_CONFIG,CbsDfCmnFineThrotLight)},
  //Clean Victim FTI Cmd Balancing
  {IDSNVID_DF_CMN_CLEAN_VIC_FTI_CMD_BAL, OFFSET_OF(CBS_CONFIG,CbsDfCmnCleanVicFtiCmdBal)},
  //CCMConfig5[ReqvReqNDImbThr]
  {IDSNVID_DF_CMN_REQV_REQ_ND_IMB_THR, OFFSET_OF(CBS_CONFIG,CbsDfCmnReqvReqNDImbThr)},
  //CXL Strongly Ordered Writes
  {IDSNVID_DF_CMN_CXL_STRONGLY_ORDERED_WRITES, OFFSET_OF(CBS_CONFIG,CbsDfCmnCxlStronglyOrderedWrites)},
  //Enhanced Partial Writes to Same Address
  {IDSNVID_DF_CMN_ENHANCED_PART_WR, OFFSET_OF(CBS_CONFIG,CbsDfCmnEnhancedPartWr)},
  //NUMA nodes per socket
  {IDSNVID_DF_CMN_DRAM_NPS, OFFSET_OF(CBS_CONFIG,CbsDfCmnDramNps)},
  //Memory interleaving
  {IDSNVID_DF_CMN_MEM_INTLV, OFFSET_OF(CBS_CONFIG,CbsDfCmnMemIntlv)},
  //Mixed interleaved mode
  {IDSNVID_DF_CMN_MIXED_INTERLEAVED_MODE, OFFSET_OF(CBS_CONFIG,CbsDfCmnMixedInterleavedMode)},
  //CXL Memory interleaving
  {IDSNVID_DF_CMN_CXL_MEM_INTLV, OFFSET_OF(CBS_CONFIG,CbsDfCmnCxlMemIntlv)},
  //CXL Sublink interleaving
  {IDSNVID_DF_CNLI_SUBLINK_INTERLEAVING, OFFSET_OF(CBS_CONFIG,CbsDfCnliSublinkInterleaving)},
  //DRAM map inversion
  {IDSNVID_DF_CMN_DRAM_MAP_INVERSION, OFFSET_OF(CBS_CONFIG,CbsDfCmnDramMapInversion)},
  //Location of private memory regions
  {IDSNVID_DF_CMN_CC6_ALLOCATION_SCHEME, OFFSET_OF(CBS_CONFIG,CbsDfCmnCc6AllocationScheme)},
  //ACPI SRAT L3 Cache As NUMA Domain
  {IDSNVID_DF_CMN_ACPI_SRAT_L3_NUMA, OFFSET_OF(CBS_CONFIG,CbsDfCmnAcpiSratL3Numa)},
  //ACPI SLIT Distance Control
  {IDSNVID_DF_CMN_ACPI_SLIT_DIST_CTRL, OFFSET_OF(CBS_CONFIG,CbsDfCmnAcpiSlitDistCtrl)},
  //ACPI SLIT remote relative distance
  {IDSNVID_DF_CMN_ACPI_SLIT_REMOTE_FAR, OFFSET_OF(CBS_CONFIG,CbsDfCmnAcpiSlitRemoteFar)},
  //ACPI SLIT virtual distance
  {IDSNVID_DF_CMN_ACPI_SLIT_VIRTUAL_DIST, OFFSET_OF(CBS_CONFIG,CbsDfCmnAcpiSlitVirtualDist)},
  //ACPI SLIT same socket distance
  {IDSNVID_DF_CMN_ACPI_SLIT_LCL_DIST, OFFSET_OF(CBS_CONFIG,CbsDfCmnAcpiSlitLclDist)},
  //ACPI SLIT remote socket distance
  {IDSNVID_DF_CMN_ACPI_SLIT_RMT_DIST, OFFSET_OF(CBS_CONFIG,CbsDfCmnAcpiSlitRmtDist)},
  //ACPI SLIT local CXL distance
  {IDSNVID_DF_CMN_ACPI_SLIT_CXL_LCL, OFFSET_OF(CBS_CONFIG,CbsDfCmnAcpiSlitCxlLcl)},
  //ACPI SLIT remote CXL distance
  {IDSNVID_DF_CMN_ACPI_SLIT_CXL_RMT, OFFSET_OF(CBS_CONFIG,CbsDfCmnAcpiSlitCxlRmt)},
  //GMI encryption control
  {IDSNVID_DF_CMN_GMI_ENCRYPTION, OFFSET_OF(CBS_CONFIG,CbsDfCmnGmiEncryption)},
  //xGMI encryption control
  {IDSNVID_DF_CMN_X_GMI_ENCRYPTION, OFFSET_OF(CBS_CONFIG,CbsDfCmnXGmiEncryption)},
  //xGMI Link Configuration
  {IDSNVID_DF_DBG_XGMI_LINK_CFG, OFFSET_OF(CBS_CONFIG,CbsDfDbgXgmiLinkCfg)},
  //4-link xGMI max speed
  {IDSNVID_DF_CMN4_LINK_MAX_XGMI_SPEED, OFFSET_OF(CBS_CONFIG,CbsDfCmn4LinkMaxXgmiSpeed)},
  //3-link xGMI max speed
  {IDSNVID_DF_CMN3_LINK_MAX_XGMI_SPEED, OFFSET_OF(CBS_CONFIG,CbsDfCmn3LinkMaxXgmiSpeed)},
  //xGMI CRC Scale
  {IDSNVID_DF_XGMI_CRC_SCALE, OFFSET_OF(CBS_CONFIG,CbsDfXgmiCrcScale)},
  //xGMI CRC Threshold
  {IDSNVID_DF_XGMI_CRC_THRESHOLD, OFFSET_OF(CBS_CONFIG,CbsDfXgmiCrcThreshold)},
  //xGMI Preset Control
  {IDSNVID_DF_XGMI_PRESET_CONTROL, OFFSET_OF(CBS_CONFIG,CbsDfXgmiPresetControl)},
  //xGMI Training Err Mask
  {IDSNVID_DF_XGMI_TRAINING_ERR_MASK, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTrainingErrMask)},
  //xGMI AC/DC Coupled Link Control
  {IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_CONTROL, OFFSET_OF(CBS_CONFIG,CbsDfXgmiAcDcCoupledLinkControl)},
  //xGMI AC/DC Coupled Link (APCB)
  {IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK, OFFSET_OF(CBS_CONFIG,CbsDfXgmiAcDcCoupledLink)},
  //xGMI AC/DC Coupled Link Socket 0 Link 0
  {IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK0, OFFSET_OF(CBS_CONFIG,CbsDfXgmiAcDcCoupledLinkSocket0Link0)},
  //xGMI AC/DC Coupled Link Socket 0 Link 1
  {IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK1, OFFSET_OF(CBS_CONFIG,CbsDfXgmiAcDcCoupledLinkSocket0Link1)},
  //xGMI AC/DC Coupled Link Socket 0 Link 2
  {IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK2, OFFSET_OF(CBS_CONFIG,CbsDfXgmiAcDcCoupledLinkSocket0Link2)},
  //xGMI AC/DC Coupled Link Socket 0 Link 3
  {IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK3, OFFSET_OF(CBS_CONFIG,CbsDfXgmiAcDcCoupledLinkSocket0Link3)},
  //xGMI AC/DC Coupled Link Socket 1 Link 0
  {IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK0, OFFSET_OF(CBS_CONFIG,CbsDfXgmiAcDcCoupledLinkSocket1Link0)},
  //xGMI AC/DC Coupled Link Socket 1 Link 1
  {IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK1, OFFSET_OF(CBS_CONFIG,CbsDfXgmiAcDcCoupledLinkSocket1Link1)},
  //xGMI AC/DC Coupled Link Socket 1 Link 2
  {IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK2, OFFSET_OF(CBS_CONFIG,CbsDfXgmiAcDcCoupledLinkSocket1Link2)},
  //xGMI AC/DC Coupled Link Socket 1 Link 3
  {IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK3, OFFSET_OF(CBS_CONFIG,CbsDfXgmiAcDcCoupledLinkSocket1Link3)},
  //xGMI Channel Type Control
  {IDSNVID_DF_XGMI_CHANNEL_TYPE_CONTROL, OFFSET_OF(CBS_CONFIG,CbsDfXgmiChannelTypeControl)},
  //SDCI
  {IDSNVID_DF_CDMA, OFFSET_OF(CBS_CONFIG,CbsDfCdma)},
  //DisRmtSteer
  {IDSNVID_DF_DBG_DIS_RMT_STEER, OFFSET_OF(CBS_CONFIG,CbsDfDbgDisRmtSteer)},
  //Organization
  {IDSNVID_DF_CMN_PF_ORGANIZATION, OFFSET_OF(CBS_CONFIG,CbsDfCmnPfOrganization)},
  //Periodic Directory Rinse (PDR) Tuning
  {IDSNVID_CMN_DF_PDR_TUNING, OFFSET_OF(CBS_CONFIG,CbsCmnDfPdrTuning)},
  //Tracking Granularity
  {IDSNVID_DF_CMN_MEM_INTLV_PAGE_SIZE, OFFSET_OF(CBS_CONFIG,CbsDfCmnMemIntlvPageSize)},
  //PDR Mode
  {IDSNVID_DF_CMN_PF_PDR_MODE, OFFSET_OF(CBS_CONFIG,CbsDfCmnPfPdrMode)},
  //Chipselect Interleaving
  {IDSNVID_CMN_MEM_CS_INTERLEAVE_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemCsInterleaveDdr)},
  //Address Hash Bank
  {IDSNVID_CMN_MEM_ADDRESS_HASH_BANK_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemAddressHashBankDdr)},
  //Address Hash CS
  {IDSNVID_CMN_MEM_ADDRESS_HASH_CS_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemAddressHashCsDdr)},
  //Address Hash Rm
  {IDSNVID_CMN_MEM_ADDRESS_HASH_RM_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemAddressHashRmDdr)},
  //Address Hash Subchannel
  {IDSNVID_CMN_MEM_ADDRESS_HASH_SUBCHANNEL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemAddressHashSubchannelDdr)},
  //BankSwapMode
  {IDSNVID_CMN_MEM_CTRLLER_BANK_SWAP_MODE_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemCtrllerBankSwapModeDdr)},
  //Memory Context Restore
  {IDSNVID_CMN_MEM_CONTEXT_RESTORE_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemContextRestoreDdr)},
  //DRAM Survives Warm Reset
  {IDSNVID_DRAM_SURVIVES_WARM_RESET, OFFSET_OF(CBS_CONFIG,CbsDramSurvivesWarmReset)},
  //Power Down Enable
  {IDSNVID_CMN_MEM_CTRLLER_PWR_DN_EN_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemCtrllerPwrDnEnDdr)},
  //Sub Urgent Refresh Lower Bound
  {IDSNVID_CMN_MEM_SUB_URG_REF_LOWER_BOUND, OFFSET_OF(CBS_CONFIG,CbsCmnMemSubUrgRefLowerBound)},
  //Urgent Refresh Limit
  {IDSNVID_CMN_MEM_URG_REF_LIMIT, OFFSET_OF(CBS_CONFIG,CbsCmnMemUrgRefLimit)},
  //DRAM Refresh Rate
  {IDSNVID_CMN_MEM_DRAM_REFRESH_RATE, OFFSET_OF(CBS_CONFIG,CbsCmnMemDramRefreshRate)},
  //Self-Refresh Exit Staggering
  {IDSNVID_CMN_MEM_SELF_REFRESH_EXIT_STAGGERING, OFFSET_OF(CBS_CONFIG,CbsCmnMemSelfRefreshExitStaggering)},
  //DRAM 2x Refresh Temperature Threshold
  {IDSNVID_CMN_MEMT2X_REFRESH_TEMPERATURE_THRESHOLD, OFFSET_OF(CBS_CONFIG,CbsCmnMemt2xRefreshTemperatureThreshold)},
  //Memory Channel Disable Float Power Good
  {IDSNVID_CMN_MEM_CHANNEL_DISABLE_FLOAT_POWER_GOOD_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemChannelDisableFloatPowerGoodDdr)},
  //Socket 0 Channel 0
  {IDSNVID_CMN_MEM_SOCKET0_CHANNEL0_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemSocket0Channel0Ddr)},
  //Socket 0 Channel 1
  {IDSNVID_CMN_MEM_SOCKET0_CHANNEL1_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemSocket0Channel1Ddr)},
  //Socket 0 Channel 2
  {IDSNVID_CMN_MEM_SOCKET0_CHANNEL2_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemSocket0Channel2Ddr)},
  //Socket 0 Channel 3
  {IDSNVID_CMN_MEM_SOCKET0_CHANNEL3_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemSocket0Channel3Ddr)},
  //Socket 0 Channel 4
  {IDSNVID_CMN_MEM_SOCKET0_CHANNEL4_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemSocket0Channel4Ddr)},
  //Socket 0 Channel 5
  {IDSNVID_CMN_MEM_SOCKET0_CHANNEL5_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemSocket0Channel5Ddr)},
  //Socket 0 Channel 6
  {IDSNVID_CMN_MEM_SOCKET0_CHANNEL6_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemSocket0Channel6Ddr)},
  //Socket 0 Channel 7
  {IDSNVID_CMN_MEM_SOCKET0_CHANNEL7_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemSocket0Channel7Ddr)},
  //Socket 0 Channel 8
  {IDSNVID_CMN_MEM_SOCKET0_CHANNEL8_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemSocket0Channel8Ddr)},
  //Socket 0 Channel 9
  {IDSNVID_CMN_MEM_SOCKET0_CHANNEL9_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemSocket0Channel9Ddr)},
  //Socket 0 Channel 10
  {IDSNVID_CMN_MEM_SOCKET0_CHANNEL10_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemSocket0Channel10Ddr)},
  //Socket 0 Channel 11
  {IDSNVID_CMN_MEM_SOCKET0_CHANNEL11_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemSocket0Channel11Ddr)},
  //Socket 1 Channel 0
  {IDSNVID_CMN_MEM_SOCKET1_CHANNEL0_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemSocket1Channel0Ddr)},
  //Socket 1 Channel 1
  {IDSNVID_CMN_MEM_SOCKET1_CHANNEL1_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemSocket1Channel1Ddr)},
  //Socket 1 Channel 2
  {IDSNVID_CMN_MEM_SOCKET1_CHANNEL2_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemSocket1Channel2Ddr)},
  //Socket 1 Channel 3
  {IDSNVID_CMN_MEM_SOCKET1_CHANNEL3_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemSocket1Channel3Ddr)},
  //Socket 1 Channel 4
  {IDSNVID_CMN_MEM_SOCKET1_CHANNEL4_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemSocket1Channel4Ddr)},
  //Socket 1 Channel 5
  {IDSNVID_CMN_MEM_SOCKET1_CHANNEL5_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemSocket1Channel5Ddr)},
  //Socket 1 Channel 6
  {IDSNVID_CMN_MEM_SOCKET1_CHANNEL6_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemSocket1Channel6Ddr)},
  //Socket 1 Channel 7
  {IDSNVID_CMN_MEM_SOCKET1_CHANNEL7_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemSocket1Channel7Ddr)},
  //Socket 1 Channel 8
  {IDSNVID_CMN_MEM_SOCKET1_CHANNEL8_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemSocket1Channel8Ddr)},
  //Socket 1 Channel 9
  {IDSNVID_CMN_MEM_SOCKET1_CHANNEL9_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemSocket1Channel9Ddr)},
  //Socket 1 Channel 10
  {IDSNVID_CMN_MEM_SOCKET1_CHANNEL10_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemSocket1Channel10Ddr)},
  //Socket 1 Channel 11
  {IDSNVID_CMN_MEM_SOCKET1_CHANNEL11_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemSocket1Channel11Ddr)},
  //Refresh Management
  {IDSNVID_CMN_MEM_REF_MANAGEMENT_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemRefManagementDdr)},
  //Adaptive Refresh Management
  {IDSNVID_CMN_MEM_ARFM_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemArfmDdr)},
  //RAA Initial Management Threshold
  {IDSNVID_CMN_MEM_RAAIMT_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemRAAIMTDdr)},
  //RAA Maximum Management Threshold
  {IDSNVID_CMN_MEM_RAAMMT_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemRAAMMTDdr)},
  //RAA Refresh Decrement Multiplier
  {IDSNVID_CMN_MEM_RAA_REF_DEC_MULTIPLIER_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemRAARefDecMultiplierDdr)},
  //DRFM Enable
  {IDSNVID_CMN_MEM_DRFM_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemDrfmDdr)},
  //Bounded Refresh Configuration
  {IDSNVID_CMN_MEM_DRFM_BRC_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemDrfmBrcDdr)},
  //DRFM Hash Enable
  {IDSNVID_CMN_MEM_DRFM_HASH_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemDrfmHashDdr)},
  //MBIST Enable
  {IDSNVID_CMN_MEM_MBIST_EN_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemMbistEnDdr)},
  //MBIST Test Mode
  {IDSNVID_CMN_MEM_MBIST_TESTMODE_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemMbistTestmodeDdr)},
  //MBIST Aggressors
  {IDSNVID_CMN_MEM_MBIST_AGGRESSORS_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemMbistAggressorsDdr)},
  //DDR Healing BIST
  {IDSNVID_CMN_MEM_HEALING_BIST_ENABLE_BIT_MASK_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemHealingBistEnableBitMaskDdr)},
  //DDR Healing BIST Execution Mode
  {IDSNVID_CMN_MEM_HEALING_BIST_EXECUTION_MODE, OFFSET_OF(CBS_CONFIG,CbsCmnMemHealingBistExecutionMode)},
  //DDR Healing BIST Repair Type
  {IDSNVID_CMN_MEM_HEALING_BIST_REPAIR_TYPE_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemHealingBistRepairTypeDdr)},
  //PMU Mem BIST Algorithm Select
  {IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM_SELECT, OFFSET_OF(CBS_CONFIG,CbsCmnMemPmuBistAlgorithmSelect)},
  //Algorithm #1
  {IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM1, OFFSET_OF(CBS_CONFIG,CbsCmnMemPmuBistAlgorithm1)},
  //Algorithm #2
  {IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM2, OFFSET_OF(CBS_CONFIG,CbsCmnMemPmuBistAlgorithm2)},
  //Algorithm #3
  {IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM3, OFFSET_OF(CBS_CONFIG,CbsCmnMemPmuBistAlgorithm3)},
  //Algorithm #4
  {IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM4, OFFSET_OF(CBS_CONFIG,CbsCmnMemPmuBistAlgorithm4)},
  //Algorithm #5
  {IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM5, OFFSET_OF(CBS_CONFIG,CbsCmnMemPmuBistAlgorithm5)},
  //Algorithm #6
  {IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM6, OFFSET_OF(CBS_CONFIG,CbsCmnMemPmuBistAlgorithm6)},
  //Algorithm #7
  {IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM7, OFFSET_OF(CBS_CONFIG,CbsCmnMemPmuBistAlgorithm7)},
  //Algorithm #8
  {IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM8, OFFSET_OF(CBS_CONFIG,CbsCmnMemPmuBistAlgorithm8)},
  //Algorithm #9
  {IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM9, OFFSET_OF(CBS_CONFIG,CbsCmnMemPmuBistAlgorithm9)},
  //Pattern Select
  {IDSNVID_CMN_MEM_MBIST_PATTERN_SELECT, OFFSET_OF(CBS_CONFIG,CbsCmnMemMbistPatternSelect)},
  //Pattern Length
  {IDSNVID_CMN_MEM_MBIST_PATTERN_LENGTH, OFFSET_OF(CBS_CONFIG,CbsCmnMemMbistPatternLength)},
  //Aggressor Channel
  {IDSNVID_CMN_MEM_MBIST_AGGRESSORS_CHNL, OFFSET_OF(CBS_CONFIG,CbsCmnMemMbistAggressorsChnl)},
  //Aggressor Static Lane Control
  {IDSNVID_CMN_MEM_MBIST_AGGR_STATIC_LANE_CTRL, OFFSET_OF(CBS_CONFIG,CbsCmnMemMbistAggrStaticLaneCtrl)},
  //Aggressor Static Lane Select ECC
  {IDSNVID_CMN_MEM_MBIST_AGGR_STATIC_LANE_SEL_ECC, OFFSET_OF(CBS_CONFIG,CbsCmnMemMbistAggrStaticLaneSelEcc)},
  //Aggressor Static Lane Value
  {IDSNVID_CMN_MEM_MBIST_AGGR_STATIC_LANE_VAL, OFFSET_OF(CBS_CONFIG,CbsCmnMemMbistAggrStaticLaneVal)},
  //Target Static Lane Control
  {IDSNVID_CMN_MEM_MBIST_TGT_STATIC_LANE_CTRL, OFFSET_OF(CBS_CONFIG,CbsCmnMemMbistTgtStaticLaneCtrl)},
  //Target Static Lane Select ECC
  {IDSNVID_CMN_MEM_MBIST_TGT_STATIC_LANE_SEL_ECC, OFFSET_OF(CBS_CONFIG,CbsCmnMemMbistTgtStaticLaneSelEcc)},
  //Target Static Lane Value
  {IDSNVID_CMN_MEM_MBIST_TGT_STATIC_LANE_VAL, OFFSET_OF(CBS_CONFIG,CbsCmnMemMbistTgtStaticLaneVal)},
  //Read Voltage Sweep Step Size
  {IDSNVID_CMN_MEM_MBIST_READ_DATA_EYE_VOLTAGE_STEP, OFFSET_OF(CBS_CONFIG,CbsCmnMemMbistReadDataEyeVoltageStep)},
  //Read Timing Sweep Step Size
  {IDSNVID_CMN_MEM_MBIST_READ_DATA_EYE_TIMING_STEP, OFFSET_OF(CBS_CONFIG,CbsCmnMemMbistReadDataEyeTimingStep)},
  //Write Voltage Sweep Step Size
  {IDSNVID_CMN_MEM_MBIST_WRITE_DATA_EYE_VOLTAGE_STEP, OFFSET_OF(CBS_CONFIG,CbsCmnMemMbistWriteDataEyeVoltageStep)},
  //Write Timing Sweep Step Size
  {IDSNVID_CMN_MEM_MBIST_WRITE_DATA_EYE_TIMING_STEP, OFFSET_OF(CBS_CONFIG,CbsCmnMemMbistWriteDataEyeTimingStep)},
  //Silent Execution
  {IDSNVID_CMN_MEM_MBIST_DATAEYE_SILENT_EXECUTION, OFFSET_OF(CBS_CONFIG,CbsCmnMemMbistDataeyeSilentExecution)},
  //Data Poisoning
  {IDSNVID_CMN_MEM_DATA_POISONING_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemDataPoisoningDdr)},
  //DRAM Boot Time Post Package Repair
  {IDSNVID_CMN_MEM_BOOT_TIME_POST_PACKAGE_REPAIR, OFFSET_OF(CBS_CONFIG,CbsCmnMemBootTimePostPackageRepair)},
  //DRAM Runtime Post Package Repair
  {IDSNVID_CMN_MEM_RUNTIME_POST_PACKAGE_REPAIR, OFFSET_OF(CBS_CONFIG,CbsCmnMemRuntimePostPackageRepair)},
  //DRAM Post Package Repair Config Initiator
  {IDSNVID_CMN_MEM_POST_PACKAGE_REPAIR_CONFIG_INITIATOR, OFFSET_OF(CBS_CONFIG,CbsCmnMemPostPackageRepairConfigInitiator)},
  //RCD Parity
  {IDSNVID_CMN_MEM_RCD_PARITY_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemRcdParityDdr)},
  //Max RCD Parity Error Replay
  {IDSNVID_CMN_MEM_MAX_RCD_PARITY_ERROR_REPLAY_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemMaxRcdParityErrorReplayDdr)},
  //Write CRC
  {IDSNVID_CMN_MEM_WRITE_CRC_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemWriteCrcDdr)},
  //Max Write CRC Error Replay
  {IDSNVID_CMN_MEM_MAX_WRITE_CRC_ERROR_REPLAY_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemMaxWriteCrcErrorReplayDdr)},
  //Read CRC
  {IDSNVID_CMN_MEM_READ_CRC_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemReadCrcDdr)},
  //Max Read CRC Error Replay
  {IDSNVID_CMN_MEM_MAX_READ_CRC_ERROR_REPLAY_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemMaxReadCrcErrorReplayDdr)},
  //Memory Error Injection
  {IDSNVID_CMN_MEM_DIS_MEM_ERR_INJ, OFFSET_OF(CBS_CONFIG,CbsCmnMemDisMemErrInj)},
  //EcsStatus Interrupt
  {IDSNVID_CMN_MEM_ECS_STATUS_INTERRUPT_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemEcsStatusInterruptDdr)},
  //DRAM Corrected Error Counter Enable
  {IDSNVID_CMN_MEM_CORRECTED_ERROR_COUNTER_ENABLE, OFFSET_OF(CBS_CONFIG,CbsCmnMemCorrectedErrorCounterEnable)},
  //DRAM Corrected Error Counter Interrupt Enable
  {IDSNVID_CMN_MEM_CORRECTED_ERROR_COUNTER_INTERRUPT_ENABLE, OFFSET_OF(CBS_CONFIG,CbsCmnMemCorrectedErrorCounterInterruptEnable)},
  //DRAM Corrected Error Counter Leak Rate
  {IDSNVID_CMN_MEM_CORRECTED_ERROR_COUNTER_LEAK_RATE, OFFSET_OF(CBS_CONFIG,CbsCmnMemCorrectedErrorCounterLeakRate)},
  //DRAM ECC Symbol Size
  {IDSNVID_CMN_MEM_DRAM_ECC_SYMBOL_SIZE_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemDramEccSymbolSizeDdr)},
  //DRAM ECC Enable
  {IDSNVID_CMN_MEM_DRAM_ECC_EN_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemDramEccEnDdr)},
  //DRAM UECC Retry
  {IDSNVID_CMN_MEM_DRAM_UECC_RETRY_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemDramUeccRetryDdr)},
  //Max DRAM UECC Error Replay
  {IDSNVID_CMN_MEM_MAX_DRAM_UECC_ERROR_REPLAY_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemMaxDramUeccErrorReplayDdr)},
  //Memory Clear
  {IDSNVID_CMN_MEM_DRAM_MEM_CLR_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemDramMemClrDdr)},
  //Address XOR after ECC
  {IDSNVID_CMN_MEM_ADDR_XOR_AFTER_ECC, OFFSET_OF(CBS_CONFIG,CbsCmnMemAddrXorAfterEcc)},
  //CipherText Hiding Enable
  {IDSNVID_DBG_MEM_CIPHER_TEXT_HIDING, OFFSET_OF(CBS_CONFIG,CbsDbgMemCipherTextHiding)},
  //DRAM ECS Mode
  {IDSNVID_CMN_MEM_DRAM_ECS_MODE_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemDramEcsModeDdr)},
  //DRAM Redirect Scrubber Enable
  {IDSNVID_CMN_MEM_DRAM_REDIRECT_SCRUB_EN_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemDramRedirectScrubEnDdr)},
  //DRAM Scrub Redirection Limit
  {IDSNVID_CMN_MEM_DRAM_REDIRECT_SCRUB_LIMIT_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemDramRedirectScrubLimitDdr)},
  //DRAM Scrub Time
  {IDSNVID_CMN_MEM_DRAM_SCRUB_TIME, OFFSET_OF(CBS_CONFIG,CbsCmnMemDramScrubTime)},
  //tECSint Ctrl
  {IDSNVID_CMN_MEMT_EC_SINT_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemtECSintCtrlDdr)},
  //DRAM Error Threshold Count
  {IDSNVID_CMN_MEM_DRAM_ETC_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemDramEtcDdr)},
  //DRAM ECS Count Mode
  {IDSNVID_CMN_MEM_DRAM_ECS_COUNT_MODE_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemDramEcsCountModeDdr)},
  //DRAM AutoEcs during Self Refresh
  {IDSNVID_CMN_MEM_DRAM_AUTO_ECS_SELF_REFRESH_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemDramAutoEcsSelfRefreshDdr)},
  //DRAM ECS WriteBack Suppression
  {IDSNVID_CMN_MEM_DRAM_ECS_WRITEBACK_SUPPRESSION_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemDramEcsWritebackSuppressionDdr)},
  //DRAM X4 WriteBack Suppression
  {IDSNVID_CMN_MEM_DRAM_X4_WRITEBACK_SUPPRESSION_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemDramX4WritebackSuppressionDdr)},
  //Processor ODT Pull Up Impedance
  {IDSNVID_CMN_MEM_ODT_IMPED_PROC_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemOdtImpedProcDdr)},
  //Processor ODT Pull Down Impedance
  {IDSNVID_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemOdtPullDownImpedProcDdr)},
  //Dram DQ drive strengths
  {IDSNVID_CMN_MEM_DRAM_DRV_STREN_DQ_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemDramDrvStrenDqDdr)},
  //RTT_NOM_WR P-State 0
  {IDSNVID_CMN_MEM_RTT_NOM_WR_P0_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemRttNomWrP0Ddr)},
  //RTT_NOM_RD P-State 0
  {IDSNVID_CMN_MEM_RTT_NOM_RD_P0_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemRttNomRdP0Ddr)},
  //RTT_WR P-State 0
  {IDSNVID_CMN_MEM_RTT_WR_P0_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemRttWrP0Ddr)},
  //RTT_PARK P-State 0
  {IDSNVID_CMN_MEM_RTT_PARK_P0_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemRttParkP0Ddr)},
  //DQS_RTT_PARK P-State 0
  {IDSNVID_CMN_MEM_RTT_PARK_DQS_P0_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemRttParkDqsP0Ddr)},
  //RTT_NOM_WR P-State 1
  {IDSNVID_CMN_MEM_RTT_NOM_WR_P1_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemRttNomWrP1Ddr)},
  //RTT_NOM_RD P-State 1
  {IDSNVID_CMN_MEM_RTT_NOM_RD_P1_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemRttNomRdP1Ddr)},
  //RTT_WR P-State 1
  {IDSNVID_CMN_MEM_RTT_WR_P1_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemRttWrP1Ddr)},
  //RTT_PARK P-State 1
  {IDSNVID_CMN_MEM_RTT_PARK_P1_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemRttParkP1Ddr)},
  //DQS_RTT_PARK P-State 1
  {IDSNVID_CMN_MEM_RTT_PARK_DQS_P1_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemRttParkDqsP1Ddr)},
  //DRAM Timing Configuration Legal Disclaimer
  {IDSNVID_CMN_MEM_TIMING_LEGAL_DISCLAIMER, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingLegalDisclaimer)},
  //DRAM Timing Configuration Legal Disclaimer 1
  {IDSNVID_CMN_MEM_TIMING_LEGAL_DISCLAIMER1, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingLegalDisclaimer1)},
  //Active Memory Timing Settings
  {IDSNVID_CMN_MEM_TIMING_SETTING_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingSettingDdr)},
  //Tcl Ctrl
  {IDSNVID_CMN_MEM_TIMING_TCL_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTclCtrlDdr)},
  //Trcd Ctrl
  {IDSNVID_CMN_MEM_TIMING_TRCD_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrcdCtrlDdr)},
  //Trp Ctrl
  {IDSNVID_CMN_MEM_TIMING_TRP_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrpCtrlDdr)},
  //Tras Ctrl
  {IDSNVID_CMN_MEM_TIMING_TRAS_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrasCtrlDdr)},
  //Trc Ctrl
  {IDSNVID_CMN_MEM_TIMING_TRC_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrcCtrlDdr)},
  //Twr Ctrl
  {IDSNVID_CMN_MEM_TIMING_TWR_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTwrCtrlDdr)},
  //Trfc1 Ctrl
  {IDSNVID_CMN_MEM_TIMING_TRFC1_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrfc1CtrlDdr)},
  //Trfc2 Ctrl
  {IDSNVID_CMN_MEM_TIMING_TRFC2_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrfc2CtrlDdr)},
  //TrfcSb Ctrl
  {IDSNVID_CMN_MEM_TIMING_TRFC_SB_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrfcSbCtrlDdr)},
  //Tcwl Ctrl
  {IDSNVID_CMN_MEM_TIMING_TCWL_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTcwlCtrlDdr)},
  //Trtp Ctrl
  {IDSNVID_CMN_MEM_TIMING_TRTP_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrtpCtrlDdr)},
  //TrrdL Ctrl
  {IDSNVID_CMN_MEM_TIMING_TRRD_L_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrrdLCtrlDdr)},
  //TrrdS Ctrl
  {IDSNVID_CMN_MEM_TIMING_TRRD_S_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrrdSCtrlDdr)},
  //Tfaw Ctrl
  {IDSNVID_CMN_MEM_TIMING_TFAW_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTfawCtrlDdr)},
  //TwtrL Ctrl
  {IDSNVID_CMN_MEM_TIMING_TWTR_L_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTwtrLCtrlDdr)},
  //TwtrS Ctrl
  {IDSNVID_CMN_MEM_TIMING_TWTR_S_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTwtrSCtrlDdr)},
  //TrdrdScL Ctrl
  {IDSNVID_CMN_MEM_TIMING_TRDRD_SC_L_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrdrdScLCtrlDdr)},
  //TrdrdSc Ctrl
  {IDSNVID_CMN_MEM_TIMING_TRDRD_SC_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrdrdScCtrlDdr)},
  //TrdrdSd Ctrl
  {IDSNVID_CMN_MEM_TIMING_TRDRD_SD_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrdrdSdCtrlDdr)},
  //TrdrdDd Ctrl
  {IDSNVID_CMN_MEM_TIMING_TRDRD_DD_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrdrdDdCtrlDdr)},
  //TwrwrScL Ctrl
  {IDSNVID_CMN_MEM_TIMING_TWRWR_SC_L_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTwrwrScLCtrlDdr)},
  //TwrwrSc Ctrl
  {IDSNVID_CMN_MEM_TIMING_TWRWR_SC_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTwrwrScCtrlDdr)},
  //TwrwrSd Ctrl
  {IDSNVID_CMN_MEM_TIMING_TWRWR_SD_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTwrwrSdCtrlDdr)},
  //TwrwrDd Ctrl
  {IDSNVID_CMN_MEM_TIMING_TWRWR_DD_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTwrwrDdCtrlDdr)},
  //Twrrd Ctrl
  {IDSNVID_CMN_MEM_TIMING_TWRRD_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTwrrdCtrlDdr)},
  //Trdwr Ctrl
  {IDSNVID_CMN_MEM_TIMING_TRDWR_CTRL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrdwrCtrlDdr)},
  //DRAM PDA Enumerate ID Programming Mode
  {IDSNVID_CMN_MEM_DRAM_PDA_ENUM_ID_PROG_MODE_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemDramPdaEnumIdProgModeDdr)},
  //Write Training Burst Length
  {IDSNVID_CMN_MEM_WRITE_TRAINING_BURST_LENGTH, OFFSET_OF(CBS_CONFIG,CbsCmnMemWriteTrainingBurstLength)},
  //Training Retry Count
  {IDSNVID_CMN_TRAINING_RETRY_COUNT, OFFSET_OF(CBS_CONFIG,CbsCmnTrainingRetryCount)},
  //Periodic Training Mode
  {IDSNVID_CMN_MEM_PERIODIC_TRAINING_MODE_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemPeriodicTrainingModeDdr)},
  //Periodic Interval Mode
  {IDSNVID_CMN_MEM_PERIODIC_INTERVAL_MODE, OFFSET_OF(CBS_CONFIG,CbsCmnMemPeriodicIntervalMode)},
  //TSME
  {IDSNVID_CMN_MEM_TSME_ENABLE_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTsmeEnableDdr)},
  //AES
  {IDSNVID_CMN_MEM_AES, OFFSET_OF(CBS_CONFIG,CbsCmnMemAes)},
  //Data Scramble
  {IDSNVID_CMN_MEM_DATA_SCRAMBLE, OFFSET_OF(CBS_CONFIG,CbsCmnMemDataScramble)},
  //SME-MK
  {IDSNVID_CMN_MEM_SME_MK_ENABLE, OFFSET_OF(CBS_CONFIG,CbsCmnMemSmeMkEnable)},
  //PMIC Error Reporting
  {IDSNVID_CMN_PMIC_ERROR_REPORTING, OFFSET_OF(CBS_CONFIG,CbsCmnPmicErrorReporting)},
  //PMIC Operation Mode
  {IDSNVID_CMN_MEM_CTRLLER_PMIC_OP_MODE, OFFSET_OF(CBS_CONFIG,CbsCmnMemCtrllerPmicOpMode)},
  //PMIC Fault Recovery
  {IDSNVID_CMN_MEM_CTRLLER_PMIC_FAULT_RECOVERY, OFFSET_OF(CBS_CONFIG,CbsCmnMemCtrllerPmicFaultRecovery)},
  //PMIC Stagger Delay
  {IDSNVID_CMN_MEM_CTRLLER_PMIC_STAGGER_DELAY, OFFSET_OF(CBS_CONFIG,CbsCmnMemCtrllerPmicStaggerDelay)},
  //Max PMIC Power On
  {IDSNVID_CMN_MEM_CTRLLER_MAX_PMIC_POWER_ON, OFFSET_OF(CBS_CONFIG,CbsCmnMemCtrllerMaxPmicPowerOn)},
  //ODTS Thermal Throttle Control
  {IDSNVID_CMN_MEM_ODTS_CMD_THROTTLE_CYCLE_CTL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemOdtsCmdThrottleCycleCtlDdr)},
  //ODTS Thermal Throttle Threshold
  {IDSNVID_CMN_MEM_ODTS_CMD_THROTTLE_THRESHOLD_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemOdtsCmdThrottleThresholdDdr)},
  //TSOD Thermal Throttle Control
  {IDSNVID_CMN_TSOD_THERMAL_THROTTLE_CONTROL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnTsodThermalThrottleControlDdr)},
  //TSOD Thermal Throttle Start Temperature
  {IDSNVID_CMN_TSOD_THERMAL_THROTTLE_START_TEMP_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnTsodThermalThrottleStartTempDdr)},
  //TSOD Thermal Throttle Hysteresis
  {IDSNVID_CMN_TSOD_THERMAL_THROTTLE_HYSTERESIS_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnTsodThermalThrottleHysteresisDdr)},
  //TSOD Command Throttle Percentage (Threshold)
  {IDSNVID_CMN_TSOD_CMD_THROTTLE_PERCENTAGE0_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnTsodCmdThrottlePercentage0Ddr)},
  //TSOD Command Throttle Percentage (Threshold+5C)
  {IDSNVID_CMN_TSOD_CMD_THROTTLE_PERCENTAGE5_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnTsodCmdThrottlePercentage5Ddr)},
  //TSOD Command Throttle Percentage (Threshold+10C)
  {IDSNVID_CMN_TSOD_CMD_THROTTLE_PERCENTAGE10_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnTsodCmdThrottlePercentage10Ddr)},
  //PCIe loopback Mode
  {IDSNVID_CMN_GNB_PCIE_LOOP_BACK_MODE, OFFSET_OF(CBS_CONFIG,CbsCmnGnbPcieLoopBackMode)},
  //Enable 2 SPC (Gen 4)
  {IDSNVID_ENABLE2_SPC_GEN4, OFFSET_OF(CBS_CONFIG,CbsEnable2SpcGen4)},
  //Enable 2 SPC (Gen 5)
  {IDSNVID_ENABLE2_SPC_GEN5, OFFSET_OF(CBS_CONFIG,CbsEnable2SpcGen5)},
  //Safe recovery upon a BERExceeded Error
  {IDSNVID_GNB_SAFE_RECOVERY_UPON_ABER_EXCEEDED_ERROR, OFFSET_OF(CBS_CONFIG,CbsGnbSafeRecoveryUponABERExceededError)},
  //Periodic Calibration
  {IDSNVID_GNB_PERIODIC_CALIBRATION, OFFSET_OF(CBS_CONFIG,CbsGnbPeriodicCalibration)},
  //TDP Control
  {IDSNVID_CMN_TDP_CTL, OFFSET_OF(CBS_CONFIG,CbsCmnTDPCtl)},
  //PPT Control
  {IDSNVID_CMN_PPT_CTL, OFFSET_OF(CBS_CONFIG,CbsCmnPPTCtl)},
  //Determinism Control
  {IDSNVID_CMN_DETERMINISM_CTL, OFFSET_OF(CBS_CONFIG,CbsCmnDeterminismCtl)},
  //Determinism Enable
  {IDSNVID_CMN_DETERMINISM_ENABLE, OFFSET_OF(CBS_CONFIG,CbsCmnDeterminismEnable)},
  //xGMI Link Width Control
  {IDSNVID_CMNX_GMI_LINK_WIDTH_CTL, OFFSET_OF(CBS_CONFIG,CbsCmnxGmiLinkWidthCtl)},
  //xGMI Force Link Width Control
  {IDSNVID_CMNX_GMI_FORCE_LINK_WIDTH_CTL, OFFSET_OF(CBS_CONFIG,CbsCmnxGmiForceLinkWidthCtl)},
  //xGMI Force Link Width
  {IDSNVID_CMNX_GMI_FORCE_LINK_WIDTH, OFFSET_OF(CBS_CONFIG,CbsCmnxGmiForceLinkWidth)},
  //xGMI Max Link Width Range Control
  {IDSNVID_CMNX_GMI_MAX_LINK_WIDTH_CTL, OFFSET_OF(CBS_CONFIG,CbsCmnxGmiMaxLinkWidthCtl)},
  //xGMI Max Link Width
  {IDSNVID_CMNX_GMI_MAX_LINK_WIDTH, OFFSET_OF(CBS_CONFIG,CbsCmnxGmiMaxLinkWidth)},
  //xGMI Min Link Width
  {IDSNVID_CMNX_GMI_MIN_LINK_WIDTH, OFFSET_OF(CBS_CONFIG,CbsCmnxGmiMinLinkWidth)},
  //APBDIS
  {IDSNVID_CMN_APBDIS, OFFSET_OF(CBS_CONFIG,CbsCmnApbdis)},
  //DfPstate
  {IDSNVID_CMN_APBDIS_DF_PSTATE, OFFSET_OF(CBS_CONFIG,CbsCmnApbdisDfPstate)},
  //Power Profile Selection
  {IDSNVID_CMN_EFFICIENCY_MODE_EN, OFFSET_OF(CBS_CONFIG,CbsCmnEfficiencyModeEn)},
  //xGMI Pstate Control
  {IDSNVID_CMN_XGMI_PSTATE_CONTROL, OFFSET_OF(CBS_CONFIG,CbsCmnXgmiPstateControl)},
  //xGMI Pstate Selection
  {IDSNVID_CMN_XGMI_PSTATE_SELECTION, OFFSET_OF(CBS_CONFIG,CbsCmnXgmiPstateSelection)},
  //BoostFmaxEn
  {IDSNVID_CMN_BOOST_FMAX_EN, OFFSET_OF(CBS_CONFIG,CbsCmnBoostFmaxEn)},
  //DF PState Frequency Optimizer
  {IDSNVID_CMN_GNB_SMU_DFFO, OFFSET_OF(CBS_CONFIG,CbsCmnGnbSMUDffo)},
  //DF Cstates
  {IDSNVID_CMN_GNB_SMU_DF_CSTATES, OFFSET_OF(CBS_CONFIG,CbsCmnGnbSmuDfCstates)},
  //CPPC
  {IDSNVID_CMN_GNB_SMU_CPPC, OFFSET_OF(CBS_CONFIG,CbsCmnGnbSmuCppc)},
  //HSMP Support
  {IDSNVID_CMN_GNB_SMU_HSMP_SUPPORT, OFFSET_OF(CBS_CONFIG,CbsCmnGnbSMUHsmpSupport)},
  //SVI3 SVC Speed Control
  {IDSNVID_CMN_SVI3_SVC_SPEED_CTL, OFFSET_OF(CBS_CONFIG,CbsCmnSvi3SvcSpeedCtl)},
  //SVI3 SVC Speed
  {IDSNVID_CMN_SVI3_SVC_SPEED, OFFSET_OF(CBS_CONFIG,CbsCmnSvi3SvcSpeed)},
  //3D V-Cache
  {IDSNVID_CMN_X3D_STACK_OVERRIDE, OFFSET_OF(CBS_CONFIG,CbsCmnX3dStackOverride)},
  //L3 BIST
  {IDSNVID_CMN_L3_BIST, OFFSET_OF(CBS_CONFIG,CbsCmnL3Bist)},
  //Diagnostic Mode
  {IDSNVID_CMN_GNB_DIAG_MODE, OFFSET_OF(CBS_CONFIG,CbsCmnGnbDiagMode)},
  //GMI Folding
  {IDSNVID_CMN_GNB_SMU_GMI_FOLDING, OFFSET_OF(CBS_CONFIG,CbsCmnGnbSmuGmiFolding)},
  //Separate CPU power plane throttling
  {IDSNVID_CMN_THROTTLER_MODE, OFFSET_OF(CBS_CONFIG,CbsCmnThrottlerMode)},
  //DfPstate Range Control
  {IDSNVID_CMN_DF_PSTATE_RANGE_CTL, OFFSET_OF(CBS_CONFIG,CbsCmnDFPstateRangeCtl)},
  //DfPstate Max Index
  {IDSNVID_CMN_DF_PSTATE_MAX, OFFSET_OF(CBS_CONFIG,CbsCmnDfPstateMax)},
  //DfPstate Min Index
  {IDSNVID_CMN_DF_PSTATE_MIN, OFFSET_OF(CBS_CONFIG,CbsCmnDfPstateMin)},
  //NBIO RAS Control
  {IDSNVID_CMN_RAS_CONTROL, OFFSET_OF(CBS_CONFIG,CbsCmnRASControl)},
  //NBIO SyncFlood Generation
  {IDSNVID_CMN_NBIO_SYNC_FLOOD_GEN, OFFSET_OF(CBS_CONFIG,CbsCmnNBIOSyncFloodGen)},
  //NBIO SyncFlood Reporting
  {IDSNVID_PCD_SYNC_FLOOD_TO_APML, OFFSET_OF(CBS_CONFIG,PcdSyncFloodToApml)},
  //PCIe Aer Reporting Mechanism
  {IDSNVID_CMN_GNB_AMD_PCIE_AER_REPORT_MECHANISM, OFFSET_OF(CBS_CONFIG,CmnGnbAmdPcieAerReportMechanism)},
  //Edpc Control
  {IDSNVID_EDPC_CONTROL, OFFSET_OF(CBS_CONFIG,EdpcControl)},
  //NBIO Poison Consumption
  {IDSNVID_CMN_POISON_CONSUMPTION, OFFSET_OF(CBS_CONFIG,CbsCmnPoisonConsumption)},
  //Sync Flood on PCIe Fatal Error
  {IDSNVID_CMN_GNB_RAS_SYNCFLOOD_PCIE_FATAL_ERROR, OFFSET_OF(CBS_CONFIG,CbsCmnGnbRasSyncfloodPcieFatalError)},
  //NBIO RAS Numerical Common Options
  {IDSNVID_CMN_RAS_NUMERICAL_COMMON_OPTIONS, OFFSET_OF(CBS_CONFIG,CbsCmnRASNumericalCommonOptions)},
  //Data Object Exchange
  {IDSNVID_CMN_GNB_DATA_OBJECT_EXCHANGE, OFFSET_OF(CBS_CONFIG,CbsCmnGnbDataObjectExchange)},
  //RTM Margining Support
  {IDSNVID_CMN_GNB_RTM_MARGINING_SUPPORT, OFFSET_OF(CBS_CONFIG,CbsCmnGnbRtmMarginingSupport)},
  //Multi Auto Speed Change On Last Rate
  {IDSNVID_CMN_NBIO_FORCE_SPEED_LAST_ADVERTISED, OFFSET_OF(CBS_CONFIG,CbsCmnNbioForceSpeedLastAdvertised)},
  //Multi Upstream Auto Speed Change
  {IDSNVID_CMN_LC_MULT_UPSTREAM_AUTO, OFFSET_OF(CBS_CONFIG,CbsCmnLcMultUpstreamAuto)},
  //EQ Bypass To Highest Rate
  {IDSNVID_CMN_NBIO_PCIE_ADVERTISE_EQ_TO_HIGH_RATE_SUPPORT, OFFSET_OF(CBS_CONFIG,CbsCmnNbioPcieAdvertiseEqToHighRateSupport)},
  //Data Link Feature Cap
  {IDSNVID_CMN_GNB_DATA_LINK_FEATURE_CAP, OFFSET_OF(CBS_CONFIG,CbsCmnGnbDataLinkFeatureCap)},
  //Data Link Feature Exchange
  {IDSNVID_CMN_GNB_DATA_LINK_FEATURE_EXCHANGE, OFFSET_OF(CBS_CONFIG,CbsCmnGnbDataLinkFeatureExchange)},
  //SRIS
  {IDSNVID_CMN_GNB_SRIS, OFFSET_OF(CBS_CONFIG,CbsCmnGnbSris)},
  //ACS Enable
  {IDSNVID_DBG_GNB_DBG_ACS_ENABLE, OFFSET_OF(CBS_CONFIG,CbsDbgGnbDbgACSEnable)},
  //PCIe Ten Bit Tag Support
  {IDSNVID_GNB_CMN_PCIE_TBT_SUPPORT, OFFSET_OF(CBS_CONFIG,CbsGnbCmnPcieTbtSupport)},
  //PCIe ARI Enumeration
  {IDSNVID_GNB_CMN_PCIE_ARI_ENUMERATION, OFFSET_OF(CBS_CONFIG,CbsGnbCmnPcieAriEnumeration)},
  //PCIe ARI Support
  {IDSNVID_CMN_GNB_PCIE_ARI_SUPPORT, OFFSET_OF(CBS_CONFIG,CmnGnbPcieAriSupport)},
  //Presence Detect Select mode
  {IDSNVID_PRESENCE_DETECT_SELECTMODE, OFFSET_OF(CBS_CONFIG,CbsPresenceDetectSelectmode)},
  //Hot Plug Handling mode
  {IDSNVID_HOT_PLUG_HANDLING_MODE, OFFSET_OF(CBS_CONFIG,CbsHotPlugHandlingMode)},
  //Presence Detect State Settle Time
  {IDSNVID_HOT_PLUG_PD_SETTLE, OFFSET_OF(CBS_CONFIG,CbsHotPlugPDSettle)},
  //Hot Plug Port Settle Time
  {IDSNVID_HOT_PLUG_SETTLE_TIME, OFFSET_OF(CBS_CONFIG,CbsHotPlugSettleTime)},
  //Hotplug Support
  {IDSNVID_HOTPLUG_SUPPORT, OFFSET_OF(CBS_CONFIG,CbsHotplugSupport)},
  //Early Link Speed
  {IDSNVID_CMN_EARLY_LINK_SPEED, OFFSET_OF(CBS_CONFIG,CbsCmnEarlyLinkSpeed)},
  //Enable AER Cap
  {IDSNVID_DBG_GNB_DBG_AERCAP_ENABLE, OFFSET_OF(CBS_CONFIG,CbsDbgGnbDbgAERCAPEnable)},
  //PCIE Link Speed Capability
  {IDSNVID_CMN_PCIE_CAP_LINK_SPEED, OFFSET_OF(CBS_CONFIG,CbsCmnPcieCAPLinkSpeed)},
  //PCIE Target Link Speed
  {IDSNVID_CMN_PCIE_TARGET_LINK_SPEED, OFFSET_OF(CBS_CONFIG,CbsCmnPcieTargetLinkSpeed)},
  //ASPM Control
  {IDSNVID_CMN_ALL_PORTS_ASPM, OFFSET_OF(CBS_CONFIG,CbsCmnAllPortsASPM)},
  //MCTP Enable
  {IDSNVID_CMN_NBIO_MCTP_EN, OFFSET_OF(CBS_CONFIG,CbsCmnNbioMctpEn)},
  //MCTP Mode
  {IDSNVID_CMN_NBIO_MCTP_MODE, OFFSET_OF(CBS_CONFIG,CbsCmnNbioMctpMode)},
  //MCTP discovery notify message
  {IDSNVID_CMN_NBIO_MCTP_DISCOVERY_NOTIFY_MESSAGE, OFFSET_OF(CBS_CONFIG,CbsCmnNbioMctpDiscoveryNotifyMessage)},
  //Non-PCIe Compliant Support
  {IDSNVID_CMN_NBIO_PCIE_NON_PCIE_COMPLIANT_SUPPORT, OFFSET_OF(CBS_CONFIG,CbsCmnNbioPcieNonPcieCompliantSupport)},
  //Limit hotplug devices to PCIe boot speed
  {IDSNVID_CMN_LIMIT_HP_DEVICES_TO_PCIE_BOOT_SPEED, OFFSET_OF(CBS_CONFIG,CbsCmnLimitHpDevicesToPcieBootSpeed)},
  //Enable PCIe SFI Config via OOB
  {IDSNVID_CMN_PC_IE_SFI_CONFIGVIA_OOB_EN, OFFSET_OF(CBS_CONFIG,CbsCmnPCIeSFIConfigviaOOBEn)},
  //PCIE Idle Power Setting
  {IDSNVID_CMN_NBIO_PCIE_IDLE_POWER_SETTING, OFFSET_OF(CBS_CONFIG,CbsCmnNbioPcieIdlePowerSetting)},
  //ACS Rcc_Dev0
  {IDSNVID_CFG_ACS_EN_RCC_DEV0, OFFSET_OF(CBS_CONFIG,CbsCfgAcsEnRccDev0)},
  //AER Rcc_Dev0
  {IDSNVID_CFG_AER_EN_RCC_DEV0, OFFSET_OF(CBS_CONFIG,CbsCfgAerEnRccDev0)},
  //DlfEnableStrap1
  {IDSNVID_CFG_DLF_EN_STRAP1, OFFSET_OF(CBS_CONFIG,CbsCfgDlfEnStrap1)},
  //Phy16GTStrap1
  {IDSNVID_CFG_PHY16GT_STRAP1, OFFSET_OF(CBS_CONFIG,CbsCfgPhy16gtStrap1)},
  //MarginEnStrap1
  {IDSNVID_CFG_MARGIN_EN_STRAP1, OFFSET_OF(CBS_CONFIG,CbsCfgMarginEnStrap1)},
  //SourceValStrap5
  {IDSNVID_CFG_ACS_SOURCE_VAL_STRAP5, OFFSET_OF(CBS_CONFIG,CbsCfgAcsSourceValStrap5)},
  //TranslationalBlockingStrap5
  {IDSNVID_CFG_ACS_TRANSLATIONAL_BLOCKING_STRAP5, OFFSET_OF(CBS_CONFIG,CbsCfgAcsTranslationalBlockingStrap5)},
  //P2pReq ACS Control
  {IDSNVID_CFG_ACS_P2P_REQ, OFFSET_OF(CBS_CONFIG,CbsCfgAcsP2pReq)},
  //P2pCompStrap5
  {IDSNVID_CFG_ACS_P2P_COMP_STRAP5, OFFSET_OF(CBS_CONFIG,CbsCfgAcsP2pCompStrap5)},
  //UpstreamFwdStrap5
  {IDSNVID_CFG_ACS_UPSTREAM_FWD_STRAP5, OFFSET_OF(CBS_CONFIG,CbsCfgAcsUpstreamFwdStrap5)},
  //P2PEgressStrap5
  {IDSNVID_CFG_ACS_P2_P_EGRESS_STRAP5, OFFSET_OF(CBS_CONFIG,CbsCfgAcsP2PEgressStrap5)},
  //DirectTranslatedStrap5
  {IDSNVID_CFG_ACS_DIRECT_TRANSLATED_STRAP5, OFFSET_OF(CBS_CONFIG,CbsCfgAcsDirectTranslatedStrap5)},
  //SsidEnStrap5
  {IDSNVID_CFG_ACS_SSID_EN_STRAP5, OFFSET_OF(CBS_CONFIG,CbsCfgAcsSsidEnStrap5)},
  //PriEnPageReq
  {IDSNVID_CFG_PRI_EN_PAGE_REQ, OFFSET_OF(CBS_CONFIG,CbsCfgPriEnPageReq)},
  //PriResetPageReq
  {IDSNVID_CFG_PRI_RESET_PAGE_REQ, OFFSET_OF(CBS_CONFIG,CbsCfgPriResetPageReq)},
  //SourceVal ACS cntl
  {IDSNVID_CFG_ACS_SOURCE_VAL, OFFSET_OF(CBS_CONFIG,CbsCfgAcsSourceVal)},
  //TranslationalBlocking ACS Control
  {IDSNVID_CFG_ACS_TRANSLATIONAL_BLOCKING, OFFSET_OF(CBS_CONFIG,CbsCfgAcsTranslationalBlocking)},
  //P2pComp ACS Control
  {IDSNVID_CFG_ACS_P2P_COMP, OFFSET_OF(CBS_CONFIG,CbsCfgAcsP2pComp)},
  //UpstreamFwd ACS Control
  {IDSNVID_CFG_ACS_UPSTREAM_FWD, OFFSET_OF(CBS_CONFIG,CbsCfgAcsUpstreamFwd)},
  //P2PEgress ACS Control
  {IDSNVID_CFG_ACS_P2_P_EGRESS, OFFSET_OF(CBS_CONFIG,CbsCfgAcsP2PEgress)},
  //P2pReqStrap5
  {IDSNVID_CFG_ACS_P2P_REQ_STRAP5, OFFSET_OF(CBS_CONFIG,CbsCfgAcsP2pReqStrap5)},
  //E2E_PREFIX
  {IDSNVID_CFG_E2_E_PREFIX, OFFSET_OF(CBS_CONFIG,CbsCfgE2EPrefix)},
  //EXTENDED_FMT
  {IDSNVID_CFG_EXTENDED_FMT_SUPPORTED, OFFSET_OF(CBS_CONFIG,CbsCfgExtendedFmtSupported)},
  //AtomicRoutingStrap5
  {IDSNVID_CMN_NBIO_ATOMIC_ROUTING_STRAP5, OFFSET_OF(CBS_CONFIG,CbsCmnNbioAtomicRoutingStrap5)},
  //SEV-SNP Support
  {IDSNVID_SEV_SNP_SUPPORT, OFFSET_OF(CBS_CONFIG,CbsSevSnpSupport)},
  //SEV-TIO Support
  {IDSNVID_SEV_TIO_SUPPORT, OFFSET_OF(CBS_CONFIG,CbsSevTioSupport)},
  //DRTM Memory Reservation
  {IDSNVID_CMN_DRTM_MEMORY_RESERVATION, OFFSET_OF(CBS_CONFIG,CbsCmnDrtmMemoryReservation)},
  //DRTM Virtual Device Support
  {IDSNVID_CMN_DRTM_SUPPORT, OFFSET_OF(CBS_CONFIG,CbsCmnDrtmSupport)},
  //DMA Protection
  {IDSNVID_CMN_DMA_PROTECTION, OFFSET_OF(CBS_CONFIG,CbsCmnDmaProtection)},
  //IOMMU
  {IDSNVID_CMN_GNB_NB_IOMMU, OFFSET_OF(CBS_CONFIG,CbsCmnGnbNbIOMMU)},
  //DMAr Support
  {IDSNVID_CMN_DMAR_SUPPORT, OFFSET_OF(CBS_CONFIG,CbsCmnDmarSupport)},
  //Enable Port Bifurcation
  {IDSNVID_CMN_ENABLE_PORT_BIFURCATION, OFFSET_OF(CBS_CONFIG,CbsCmnEnablePortBifurcation)},
  //Socket 0 P0 Override
  {IDSNVID_CMN_S0_P0_OVERRIDE, OFFSET_OF(CBS_CONFIG,CbsCmnS0P0Override)},
  //Socket 0 P1 Override
  {IDSNVID_CMN_S0_P1_OVERRIDE, OFFSET_OF(CBS_CONFIG,CbsCmnS0P1Override)},
  //Socket 0 P2 Override
  {IDSNVID_CMN_S0_P2_OVERRIDE, OFFSET_OF(CBS_CONFIG,CbsCmnS0P2Override)},
  //Socket 0 P3 Override
  {IDSNVID_CMN_S0_P3_OVERRIDE, OFFSET_OF(CBS_CONFIG,CbsCmnS0P3Override)},
  //Socket 1 P0 Override
  {IDSNVID_CMN_S1_P0_OVERRIDE, OFFSET_OF(CBS_CONFIG,CbsCmnS1P0Override)},
  //Socket 1 P1 Override
  {IDSNVID_CMN_S1_P1_OVERRIDE, OFFSET_OF(CBS_CONFIG,CbsCmnS1P1Override)},
  //Socket 1 P2 Override
  {IDSNVID_CMN_S1_P2_OVERRIDE, OFFSET_OF(CBS_CONFIG,CbsCmnS1P2Override)},
  //Socket 1 P3 Override
  {IDSNVID_CMN_S1_P3_OVERRIDE, OFFSET_OF(CBS_CONFIG,CbsCmnS1P3Override)},
  //P0 Override
  {IDSNVID_CMN_P0_OVERRIDE, OFFSET_OF(CBS_CONFIG,CbsCmnP0Override)},
  //P1 Override
  {IDSNVID_CMN_P1_OVERRIDE, OFFSET_OF(CBS_CONFIG,CbsCmnP1Override)},
  //P2 Override
  {IDSNVID_CMN_P2_OVERRIDE, OFFSET_OF(CBS_CONFIG,CbsCmnP2Override)},
  //P3 Override
  {IDSNVID_CMN_P3_OVERRIDE, OFFSET_OF(CBS_CONFIG,CbsCmnP3Override)},
  //G0 Override
  {IDSNVID_CMN_G0_OVERRIDE, OFFSET_OF(CBS_CONFIG,CbsCmnG0Override)},
  //G1 Override
  {IDSNVID_CMN_G1_OVERRIDE, OFFSET_OF(CBS_CONFIG,CbsCmnG1Override)},
  //G2 Override
  {IDSNVID_CMN_G2_OVERRIDE, OFFSET_OF(CBS_CONFIG,CbsCmnG2Override)},
  //G3 Override
  {IDSNVID_CMN_G3_OVERRIDE, OFFSET_OF(CBS_CONFIG,CbsCmnG3Override)},
  //Preset Search Mask Configuration (Gen3)
  {IDSNVID_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN3, OFFSET_OF(CBS_CONFIG,CbsCmnNbioPcieSearchMaskConfigGen3)},
  //Preset Search Mask Configuration (Gen4)
  {IDSNVID_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN4, OFFSET_OF(CBS_CONFIG,CbsCmnNbioPcieSearchMaskConfigGen4)},
  //Preset Search Mask Configuration (Gen5)
  {IDSNVID_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN5, OFFSET_OF(CBS_CONFIG,CbsCmnNbioPcieSearchMaskConfigGen5)},
  //I3C/I2C 0 Enable
  {IDSNVID_CMN_FCH_I3_C0_CONFIG, OFFSET_OF(CBS_CONFIG,CbsCmnFchI3C0Config)},
  //I3C/I2C 1 Enable
  {IDSNVID_CMN_FCH_I3_C1_CONFIG, OFFSET_OF(CBS_CONFIG,CbsCmnFchI3C1Config)},
  //I3C/I2C 2 Enable
  {IDSNVID_CMN_FCH_I3_C2_CONFIG, OFFSET_OF(CBS_CONFIG,CbsCmnFchI3C2Config)},
  //I3C/I2C 3 Enable
  {IDSNVID_CMN_FCH_I3_C3_CONFIG, OFFSET_OF(CBS_CONFIG,CbsCmnFchI3C3Config)},
  //I2C 4 Enable
  {IDSNVID_CMN_FCH_I2_C4_CONFIG, OFFSET_OF(CBS_CONFIG,CbsCmnFchI2C4Config)},
  //I2C 5 Enable
  {IDSNVID_CMN_FCH_I2_C5_CONFIG, OFFSET_OF(CBS_CONFIG,CbsCmnFchI2C5Config)},
  //Release SPD Host Control
  {IDSNVID_CMN_FCH_RELEASE_SPD_HOST_CONTROL, OFFSET_OF(CBS_CONFIG,CbsCmnFchReleaseSpdHostControl)},
  //PMFW Poll DDR5 Telemetry
  {IDSNVID_CMN_FCH_PMFW_DDR5_TELEMETRY, OFFSET_OF(CBS_CONFIG,CbsCmnFchPMFWDdr5Telemetry)},
  //Ixc Telemetry Ports Fence Control
  {IDSNVID_CMN_FCH_IXC_TELEMETRY_PORTS_FENCE, OFFSET_OF(CBS_CONFIG,CbsCmnFchIxcTelemetryPortsFence)},
  //I2C SDA Hold Override
  {IDSNVID_CMN_FCH_I2C_SDA_HOLD_OVERRIDE, OFFSET_OF(CBS_CONFIG,CbsCmnFchI2cSdaHoldOverride)},
  //APML SB-TSI Mode
  {IDSNVID_CMN_FCH_APML_SBTSI_SLV_MODE, OFFSET_OF(CBS_CONFIG,CbsCmnFchApmlSbtsiSlvMode)},
  //I3C Mode Speed
  {IDSNVID_CMN_FCH_I3C_MODE_SPEED, OFFSET_OF(CBS_CONFIG,CbsCmnFchI3cModeSpeed)},
  //I3C Push Pull HCNT Value
  {IDSNVID_CMN_FCH_I3C_PP_HCNT_VALUE, OFFSET_OF(CBS_CONFIG,CbsCmnFchI3cPpHcntValue)},
  //I3C SDA Hold Value
  {IDSNVID_CMN_FCH_I3C_SDA_HOLD_VALUE, OFFSET_OF(CBS_CONFIG,CbsCmnFchI3cSdaHoldValue)},
  //I3C SDA Hold Override
  {IDSNVID_CMN_FCH_I3C_SDA_HOLD_OVERRIDE, OFFSET_OF(CBS_CONFIG,CbsCmnFchI3cSdaHoldOverride)},
  //I2C 0 SDA RX HOLD VALUE
  {IDSNVID_CMN_FCH_I2C0_SDA_RX_HOLD_VALUE, OFFSET_OF(CBS_CONFIG,CbsCmnFchI2c0SdaRxHoldValue)},
  //I2C 1 SDA RX HOLD VALUE
  {IDSNVID_CMN_FCH_I2C1_SDA_RX_HOLD_VALUE, OFFSET_OF(CBS_CONFIG,CbsCmnFchI2c1SdaRxHoldValue)},
  //I2C 2 SDA RX HOLD VALUE
  {IDSNVID_CMN_FCH_I2C2_SDA_RX_HOLD_VALUE, OFFSET_OF(CBS_CONFIG,CbsCmnFchI2c2SdaRxHoldValue)},
  //I2C 3 SDA RX HOLD VALUE
  {IDSNVID_CMN_FCH_I2C3_SDA_RX_HOLD_VALUE, OFFSET_OF(CBS_CONFIG,CbsCmnFchI2c3SdaRxHoldValue)},
  //I2C 4 SDA RX HOLD VALUE
  {IDSNVID_CMN_FCH_I2C4_SDA_RX_HOLD_VALUE, OFFSET_OF(CBS_CONFIG,CbsCmnFchI2c4SdaRxHoldValue)},
  //I2C 5 SDA RX HOLD VALUE
  {IDSNVID_CMN_FCH_I2C5_SDA_RX_HOLD_VALUE, OFFSET_OF(CBS_CONFIG,CbsCmnFchI2c5SdaRxHoldValue)},
  //I3C 0 SDA HOLD VALUE
  {IDSNVID_CMN_FCH_I3C0_SDA_HOLD_VALUE, OFFSET_OF(CBS_CONFIG,CbsCmnFchI3c0SdaHoldValue)},
  //I3C 1 SDA HOLD VALUE
  {IDSNVID_CMN_FCH_I3C1_SDA_HOLD_VALUE, OFFSET_OF(CBS_CONFIG,CbsCmnFchI3c1SdaHoldValue)},
  //I3C 2 SDA HOLD VALUE
  {IDSNVID_CMN_FCH_I3C2_SDA_HOLD_VALUE, OFFSET_OF(CBS_CONFIG,CbsCmnFchI3c2SdaHoldValue)},
  //I3C 3 SDA HOLD VALUE
  {IDSNVID_CMN_FCH_I3C3_SDA_HOLD_VALUE, OFFSET_OF(CBS_CONFIG,CbsCmnFchI3c3SdaHoldValue)},
  //SATA Enable
  {IDSNVID_CMN_FCH_SATA_ENABLE, OFFSET_OF(CBS_CONFIG,CbsCmnFchSataEnable)},
  //SATA Mode
  {IDSNVID_CMN_FCH_SATA_CLASS, OFFSET_OF(CBS_CONFIG,CbsCmnFchSataClass)},
  //SATA RAS Support
  {IDSNVID_CMN_FCH_SATA_RAS_SUPPORT, OFFSET_OF(CBS_CONFIG,CbsCmnFchSataRasSupport)},
  //SATA Staggered Spin-up
  {IDSNVID_CMN_FCH_SATA_STAGGERED_SPINUP, OFFSET_OF(CBS_CONFIG,CbsCmnFchSataStaggeredSpinup)},
  //SATA Disabled AHCI Prefetch Function
  {IDSNVID_CMN_FCH_SATA_AHCI_DIS_PREFETCH_FUNCTION, OFFSET_OF(CBS_CONFIG,CbsCmnFchSataAhciDisPrefetchFunction)},
  //Sata0 Enable
  {IDSNVID_DBG_FCH_SATA0_ENABLE, OFFSET_OF(CBS_CONFIG,CbsDbgFchSata0Enable)},
  //Sata1 Enable
  {IDSNVID_DBG_FCH_SATA1_ENABLE, OFFSET_OF(CBS_CONFIG,CbsDbgFchSata1Enable)},
  //Sata2 Enable
  {IDSNVID_DBG_FCH_SATA2_ENABLE, OFFSET_OF(CBS_CONFIG,CbsDbgFchSata2Enable)},
  //Sata3 Enable
  {IDSNVID_DBG_FCH_SATA3_ENABLE, OFFSET_OF(CBS_CONFIG,CbsDbgFchSata3Enable)},
  //Sata4 (Socket1) Enable
  {IDSNVID_DBG_FCH_SATA4_ENABLE, OFFSET_OF(CBS_CONFIG,CbsDbgFchSata4Enable)},
  //Sata5 (Socket1) Enable
  {IDSNVID_DBG_FCH_SATA5_ENABLE, OFFSET_OF(CBS_CONFIG,CbsDbgFchSata5Enable)},
  //Sata6 (Socket1) Enable
  {IDSNVID_DBG_FCH_SATA6_ENABLE, OFFSET_OF(CBS_CONFIG,CbsDbgFchSata6Enable)},
  //Sata7 (Socket1) Enable
  {IDSNVID_DBG_FCH_SATA7_ENABLE, OFFSET_OF(CBS_CONFIG,CbsDbgFchSata7Enable)},
  //Sata0 eSATA Port0
  {IDSNVID_DBG_FCH_SATAE_SATA_PORT0, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataeSATAPort0)},
  //Sata0 eSATA Port1
  {IDSNVID_DBG_FCH_SATAE_SATA_PORT1, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataeSATAPort1)},
  //Sata0 eSATA Port2
  {IDSNVID_DBG_FCH_SATAE_SATA_PORT2, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataeSATAPort2)},
  //Sata0 eSATA Port3
  {IDSNVID_DBG_FCH_SATAE_SATA_PORT3, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataeSATAPort3)},
  //Sata0 eSATA Port4
  {IDSNVID_DBG_FCH_SATAE_SATA_PORT4, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataeSATAPort4)},
  //Sata0 eSATA Port5
  {IDSNVID_DBG_FCH_SATAE_SATA_PORT5, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataeSATAPort5)},
  //Sata0 eSATA Port6
  {IDSNVID_DBG_FCH_SATAE_SATA_PORT6, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataeSATAPort6)},
  //Sata0 eSATA Port7
  {IDSNVID_DBG_FCH_SATAE_SATA_PORT7, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataeSATAPort7)},
  //Sata1 eSATA Port0
  {IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT0, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie1EsataPort0)},
  //Sata1 eSATA Port1
  {IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT1, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie1EsataPort1)},
  //Sata1 eSATA Port2
  {IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT2, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie1EsataPort2)},
  //Sata1 eSATA Port3
  {IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT3, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie1EsataPort3)},
  //Sata1 eSATA Port4
  {IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT4, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie1EsataPort4)},
  //Sata1 eSATA Port5
  {IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT5, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie1EsataPort5)},
  //Sata1 eSATA Port6
  {IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT6, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie1EsataPort6)},
  //Sata1 eSATA Port7
  {IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT7, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie1EsataPort7)},
  //Sata2 eSATA Port0
  {IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT0, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie2EsataPort0)},
  //Sata2 eSATA Port1
  {IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT1, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie2EsataPort1)},
  //Sata2 eSATA Port2
  {IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT2, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie2EsataPort2)},
  //Sata2 eSATA Port3
  {IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT3, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie2EsataPort3)},
  //Sata2 eSATA Port4
  {IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT4, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie2EsataPort4)},
  //Sata2 eSATA Port5
  {IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT5, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie2EsataPort5)},
  //Sata2 eSATA Port6
  {IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT6, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie2EsataPort6)},
  //Sata2 eSATA Port7
  {IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT7, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie2EsataPort7)},
  //Sata3 eSATA Port0
  {IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT0, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie3EsataPort0)},
  //Sata3 eSATA Port1
  {IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT1, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie3EsataPort1)},
  //Sata3 eSATA Port2
  {IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT2, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie3EsataPort2)},
  //Sata3 eSATA Port3
  {IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT3, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie3EsataPort3)},
  //Sata3 eSATA Port4
  {IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT4, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie3EsataPort4)},
  //Sata3 eSATA Port5
  {IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT5, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie3EsataPort5)},
  //Sata3 eSATA Port6
  {IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT6, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie3EsataPort6)},
  //Sata3 eSATA Port7
  {IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT7, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie3EsataPort7)},
  //Sata4 eSATA Port0
  {IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT0, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie4EsataPort0)},
  //Sata4 eSATA Port1
  {IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT1, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie4EsataPort1)},
  //Sata4 eSATA Port2
  {IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT2, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie4EsataPort2)},
  //Sata4 eSATA Port3
  {IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT3, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie4EsataPort3)},
  //Sata4 eSATA Port4
  {IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT4, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie4EsataPort4)},
  //Sata4 eSATA Port5
  {IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT5, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie4EsataPort5)},
  //Sata4 eSATA Port6
  {IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT6, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie4EsataPort6)},
  //Sata4 eSATA Port7
  {IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT7, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie4EsataPort7)},
  //Sata5 eSATA Port0
  {IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT0, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie5EsataPort0)},
  //Sata5 eSATA Port1
  {IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT1, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie5EsataPort1)},
  //Sata5 eSATA Port2
  {IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT2, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie5EsataPort2)},
  //Sata5 eSATA Port3
  {IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT3, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie5EsataPort3)},
  //Sata5 eSATA Port4
  {IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT4, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie5EsataPort4)},
  //Sata5 eSATA Port5
  {IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT5, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie5EsataPort5)},
  //Sata5 eSATA Port6
  {IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT6, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie5EsataPort6)},
  //Sata5 eSATA Port7
  {IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT7, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie5EsataPort7)},
  //Sata6 eSATA Port0
  {IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT0, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie6EsataPort0)},
  //Sata6 eSATA Port1
  {IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT1, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie6EsataPort1)},
  //Sata6 eSATA Port2
  {IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT2, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie6EsataPort2)},
  //Sata6 eSATA Port3
  {IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT3, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie6EsataPort3)},
  //Sata6 eSATA Port4
  {IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT4, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie6EsataPort4)},
  //Sata6 eSATA Port5
  {IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT5, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie6EsataPort5)},
  //Sata6 eSATA Port6
  {IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT6, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie6EsataPort6)},
  //Sata6 eSATA Port7
  {IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT7, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie6EsataPort7)},
  //Sata7 eSATA Port0
  {IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT0, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie7EsataPort0)},
  //Sata7 eSATA Port1
  {IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT1, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie7EsataPort1)},
  //Sata7 eSATA Port2
  {IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT2, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie7EsataPort2)},
  //Sata7 eSATA Port3
  {IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT3, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie7EsataPort3)},
  //Sata7 eSATA Port4
  {IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT4, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie7EsataPort4)},
  //Sata7 eSATA Port5
  {IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT5, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie7EsataPort5)},
  //Sata7 eSATA Port6
  {IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT6, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie7EsataPort6)},
  //Sata7 eSATA Port7
  {IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT7, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie7EsataPort7)},
  //Socket0 DevSlp0 Enable
  {IDSNVID_DBG_FCH_SATA_AGGRESIVE_DEV_SLP_P0, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataAggresiveDevSlpP0)},
  //Socket0 DevSlp0 Controller Number
  {IDSNVID_DBG_FCH_SATA_DEV_SLP_CONTROLLER0_NUM, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataDevSlpController0Num)},
  //Socket0 DevSlp0 Port Number
  {IDSNVID_DBG_FCH_SATA_DEV_SLP_PORT0_NUM, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataDevSlpPort0Num)},
  //Socket0 DevSlp1 Enable
  {IDSNVID_DBG_FCH_SATA_AGGRESIVE_DEV_SLP_P1, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataAggresiveDevSlpP1)},
  //Socket0 DevSlp1 Controller Number
  {IDSNVID_DBG_FCH_SATA_DEV_SLP_CONTROLLER1_NUM, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataDevSlpController1Num)},
  //Socket0 DevSlp1 Port Number
  {IDSNVID_DBG_FCH_SATA_DEV_SLP_PORT1_NUM, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataDevSlpPort1Num)},
  //Socket1 DevSlp0 Enable
  {IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP0, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie4DevSlp0)},
  //Socket1 DevSlp0 Controller Number
  {IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP_CONTROLLER0_NUM, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie4DevSlpController0Num)},
  //Socket1 DevSlp0 Port Number
  {IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP0_NUM, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie4DevSlp0Num)},
  //Socket1 DevSlp1 Enable
  {IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP1, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie4DevSlp1)},
  //Socket1 DevSlp1 Controller Number
  {IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP_CONTROLLER1_NUM, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie4DevSlpController1Num)},
  //Socket1 DevSlp1 Port Number
  {IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP1_NUM, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie4DevSlp1Num)},
  //Sata0 SGPIO
  {IDSNVID_DBG_FCH_SATA_SGPIO0, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataSgpio0)},
  //Sata1 SGPIO
  {IDSNVID_DBG_FCH_SATA_MCM_DIE1_SGPIO0, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie1Sgpio0)},
  //Sata2 SGPIO
  {IDSNVID_DBG_FCH_SATA_MCM_DIE2_SGPIO0, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie2Sgpio0)},
  //Sata3 SGPIO
  {IDSNVID_DBG_FCH_SATA_MCM_DIE3_SGPIO0, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie3Sgpio0)},
  //Sata4 SGPIO
  {IDSNVID_DBG_FCH_SATA_MCM_DIE4_SGPIO0, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie4Sgpio0)},
  //Sata5 SGPIO
  {IDSNVID_DBG_FCH_SATA_MCM_DIE5_SGPIO0, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie5Sgpio0)},
  //Sata6 SGPIO
  {IDSNVID_DBG_FCH_SATA_MCM_DIE6_SGPIO0, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie6Sgpio0)},
  //Sata7 SGPIO
  {IDSNVID_DBG_FCH_SATA_MCM_DIE7_SGPIO0, OFFSET_OF(CBS_CONFIG,CbsDbgFchSataMcmDie7Sgpio0)},
  //XHCI Controller0 enable
  {IDSNVID_CMN_FCH_USB_XHC_I0_ENABLE, OFFSET_OF(CBS_CONFIG,CbsCmnFchUsbXHCI0Enable)},
  //XHCI Controller1 enable
  {IDSNVID_CMN_FCH_USB_XHC_I1_ENABLE, OFFSET_OF(CBS_CONFIG,CbsCmnFchUsbXHCI1Enable)},
  //XHCI2 enable (Socket1)
  {IDSNVID_CMN_FCH_USB_XHC_I2_ENABLE, OFFSET_OF(CBS_CONFIG,CbsCmnFchUsbXHCI2Enable)},
  //XHCI3 enable (Socket1)
  {IDSNVID_CMN_FCH_USB_XHC_I3_ENABLE, OFFSET_OF(CBS_CONFIG,CbsCmnFchUsbXHCI3Enable)},
  //Ac Loss Control
  {IDSNVID_CMN_FCH_SYSTEM_PWR_FAIL_SHADOW, OFFSET_OF(CBS_CONFIG,CbsCmnFchSystemPwrFailShadow)},
  //Set Fch Power failed Shadow in ABL
  {IDSNVID_CMN_FCH_PWR_FAIL_SHADOW_ABL_ENABLED, OFFSET_OF(CBS_CONFIG,CbsCmnFchPwrFailShadowABLEnabled)},
  //Uart 0 Enable
  {IDSNVID_CMN_FCH_UART0_CONFIG, OFFSET_OF(CBS_CONFIG,CbsCmnFchUart0Config)},
  //Uart 0 Legacy Options
  {IDSNVID_CMN_FCH_UART0_LEGACY_CONFIG, OFFSET_OF(CBS_CONFIG,CbsCmnFchUart0LegacyConfig)},
  //Uart 1 Enable
  {IDSNVID_CMN_FCH_UART1_CONFIG, OFFSET_OF(CBS_CONFIG,CbsCmnFchUart1Config)},
  //Uart 1 Legacy Options
  {IDSNVID_CMN_FCH_UART1_LEGACY_CONFIG, OFFSET_OF(CBS_CONFIG,CbsCmnFchUart1LegacyConfig)},
  //Uart 2 Enable
  {IDSNVID_CMN_FCH_UART2_CONFIG, OFFSET_OF(CBS_CONFIG,CbsCmnFchUart2Config)},
  //Uart 2 Legacy Options
  {IDSNVID_CMN_FCH_UART2_LEGACY_CONFIG, OFFSET_OF(CBS_CONFIG,CbsCmnFchUart2LegacyConfig)},
  //ALink RAS Support
  {IDSNVID_CMN_FCH_ALINK_RAS_SUPPORT, OFFSET_OF(CBS_CONFIG,CbsCmnFchAlinkRasSupport)},
  //Reset After Sync-Flood
  {IDSNVID_DBG_FCH_SYNCFLOOD_ENABLE, OFFSET_OF(CBS_CONFIG,CbsDbgFchSyncfloodEnable)},
  //Delay Reset After Sync-Flood
  {IDSNVID_DBG_FCH_DELAY_SYNCFLOOD, OFFSET_OF(CBS_CONFIG,CbsDbgFchDelaySyncflood)},
  //FCH Spread Spectrum
  {IDSNVID_DBG_FCH_SYSTEM_SPREAD_SPECTRUM, OFFSET_OF(CBS_CONFIG,CbsDbgFchSystemSpreadSpectrum)},
  //Boot Timer Enable
  {IDSNVID_CMN_BOOT_TIMER_ENABLE, OFFSET_OF(CBS_CONFIG,CbsCmnBootTimerEnable)},
  //Socket-0 P0 NTB Enable
  {IDSNVID_CMN_S_P3_NTB_P0_P0, OFFSET_OF(CBS_CONFIG,CbsCmnSP3NtbP0P0)},
  //Socket-0 P0 Start Lane
  {IDSNVID_CMN_S_P3_NTB_START_LANE_P0_P0, OFFSET_OF(CBS_CONFIG,CbsCmnSP3NtbStartLaneP0P0)},
  //Socket-0 P0 End Lane
  {IDSNVID_CMN_S_P3_NTB_END_LANE_P0_P0, OFFSET_OF(CBS_CONFIG,CbsCmnSP3NtbEndLaneP0P0)},
  //Socket-0 P0 Link Speed
  {IDSNVID_CMN_S_P3_NTB_LINK_SPEED_P0_P0, OFFSET_OF(CBS_CONFIG,CbsCmnSP3NtbLinkSpeedP0P0)},
  //Socket-0 P0 NTB Mode
  {IDSNVID_CMN_S_P3_NTB_MODE_P0_P0, OFFSET_OF(CBS_CONFIG,CbsCmnSP3NtbModeP0P0)},
  //Socket-0 P2 NTB Enable
  {IDSNVID_CMN_S_P3_NTB_P0_P2, OFFSET_OF(CBS_CONFIG,CbsCmnSP3NtbP0P2)},
  //Socket-0 P2 Start Lane
  {IDSNVID_CMN_S_P3_NTB_START_LANE_P0_P2, OFFSET_OF(CBS_CONFIG,CbsCmnSP3NtbStartLaneP0P2)},
  //Socket-0 P2 End Lane
  {IDSNVID_CMN_S_P3_NTB_END_LANE_P0_P2, OFFSET_OF(CBS_CONFIG,CbsCmnSP3NtbEndLaneP0P2)},
  //Socket-0 P2 Link Speed
  {IDSNVID_CMN_S_P3_NTB_LINK_SPEED_P0_P2, OFFSET_OF(CBS_CONFIG,CbsCmnSP3NtbLinkSpeedP0P2)},
  //Socket-0 P2 NTB Mode
  {IDSNVID_CMN_S_P3_NTB_MODE_P0_P2, OFFSET_OF(CBS_CONFIG,CbsCmnSP3NtbModeP0P2)},
  //ABL Console Out Control
  {IDSNVID_CMN_SOC_ABL_CON_OUT, OFFSET_OF(CBS_CONFIG,CbsCmnSocAblConOut)},
  //ABL Console Out Serial Port
  {IDSNVID_CMN_SOC_ABL_CON_OUT_SERIAL_PORT, OFFSET_OF(CBS_CONFIG,CbsCmnSocAblConOutSerialPort)},
  //ABL Console Out Serial Port IO
  {IDSNVID_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_IO, OFFSET_OF(CBS_CONFIG,CbsCmnSocAblConOutSerialPortIO)},
  //ABL Serial port IO customized enabled
  {IDSNVID_CMN_SOC_ABL_SERIAL_PORT_IO_CUSTOM_ENABLED, OFFSET_OF(CBS_CONFIG,CbsCmnSocAblSerialPortIOCustomEnabled)},
  //ABL Basic Console Out Control
  {IDSNVID_CMN_SOC_ABL_CON_OUT_BASIC, OFFSET_OF(CBS_CONFIG,CbsCmnSocAblConOutBasic)},
  //ABL PMU message Control
  {IDSNVID_CMN_SOC_ABL_PMU_MSG_CTRL, OFFSET_OF(CBS_CONFIG,CbsCmnSocAblPmuMsgCtrl)},
  //ABL Memory Population message Control
  {IDSNVID_CMN_SOC_ABL_MEM_POP_MSG_CTRL, OFFSET_OF(CBS_CONFIG,CbsCmnSocAblMemPopMsgCtrl)},
  //Print Socket 1 PMU MsgBlock
  {IDSNVID_CMN_PRINT_SOCKET1_PMU_MSG_BLOCK, OFFSET_OF(CBS_CONFIG,CbsCmnPrintSocket1PmuMsgBlock)},
  //Print Socket 1 PMU Training Log
  {IDSNVID_CMN_PRINT_SOCKET1_TRAINING_LOG, OFFSET_OF(CBS_CONFIG,CbsCmnPrintSocket1TrainingLog)},
  //PSP error injection support
  {IDSNVID_DF_CMN_PSP_ERR_INJ, OFFSET_OF(CBS_CONFIG,CbsDfCmnPspErrInj)},
  //Number of Sockets
  {IDSNVID_NUMBER_OF_SOCKETS, OFFSET_OF(CBS_CONFIG,CbsNumberOfSockets)},
  //SEC_I2C Voltage Mode
  {IDSNVID_CMN_SEC_I2C_VOLT_MODE, OFFSET_OF(CBS_CONFIG,CbsCmnSecI2cVoltMode)},
  //FAR enforcement state
  {IDSNVID_CMN_SOC_FAR_ENFORCED, OFFSET_OF(CBS_CONFIG,CbsCmnSocFarEnforced)},
  //FAR Switch
  {IDSNVID_CMN_SOC_FAR_SWITCH, OFFSET_OF(CBS_CONFIG,CbsCmnSocFarSwitch)},
  //CXL Control
  {IDSNVID_CMN_CXL_CONTROL, OFFSET_OF(CBS_CONFIG,CbsCmnCxlControl)},
  //CXL Physical Addressing
  {IDSNVID_CMN_CXL_SDP_REQ_SYS_ADDR, OFFSET_OF(CBS_CONFIG,CbsCmnCxlSdpReqSysAddr)},
  //CXL Memory Attribute
  {IDSNVID_CMN_CXL_SPM, OFFSET_OF(CBS_CONFIG,CbsCmnCxlSpm)},
  //CXL Encryption
  {IDSNVID_CMN_CXL_ENCRYPTION, OFFSET_OF(CBS_CONFIG,CbsCmnCxlEncryption)},
  //CXL DVSEC Lock
  {IDSNVID_CMN_CXL_DVSEC_LOCK, OFFSET_OF(CBS_CONFIG,CbsCmnCxlDvsecLock)},
  //CXL HDM Decoder Lock On Commit
  {IDSNVID_CMN_CXL_HDM_DECODER_LOCK_ON_COMMIT, OFFSET_OF(CBS_CONFIG,CbsCmnCxlHdmDecoderLockOnCommit)},
  //Temp Gen5 Advertisement
  {IDSNVID_CMN_CXL_TEMP_GEN5_ADVERTISEMENT, OFFSET_OF(CBS_CONFIG,CbsCmnCxlTempGen5Advertisement)},
  //Sync Header Bypass
  {IDSNVID_CMN_SYNC_HEADER_BY_PASS, OFFSET_OF(CBS_CONFIG,CbsCmnSyncHeaderByPass)},
  //Sync Header Bypass Compatibility Mode
  {IDSNVID_CXL_SYNC_HEADER_BYPASS_COMP_MODE, OFFSET_OF(CBS_CONFIG,CbsCxlSyncHeaderBypassCompMode)},
  //CXL Memory Online/Offline
  {IDSNVID_CMN_CXL_MEM_ONLINE_OFFLINE, OFFSET_OF(CBS_CONFIG,CbsCmnCxlMemOnlineOffline)},
  //Override CXL Memory Size
  {IDSNVID_DBG_CXL_OVERIDE_CXL_MEMORY_SIZE, OFFSET_OF(CBS_CONFIG,CbsDbgCxlOverideCxlMemorySize)},
  //CXL Protocol Error Reporting
  {IDSNVID_CMN_CXL_PROTOCOL_ERROR_REPORTING, OFFSET_OF(CBS_CONFIG,CbsCmnCxlProtocolErrorReporting)},
  //CXL Component Error Reporting
  {IDSNVID_CMN_CXL_COMPONENT_ERROR_REPORTING, OFFSET_OF(CBS_CONFIG,CbsCmnCxlComponentErrorReporting)},
  //CXL Root Port Isolation
  {IDSNVID_CMN_CXL_MEM_ISOLATION_ENABLE, OFFSET_OF(CBS_CONFIG,CbsCmnCxlMemIsolationEnable)},
  //CXL Root Port Isolation FW Notification
  {IDSNVID_CMN_CXL_MEM_ISOLATION_FW_NOTIFICATION, OFFSET_OF(CBS_CONFIG,CbsCmnCxlMemIsolationFwNotification)},
  //End
  {0xFFFF, 0}};

STATIC IDS_NV_RECORD mIdsNv16[] = {
  //Requested CPU min frequency
  {IDSNVID_CMN_CPU_REQ_MIN_FREQ, OFFSET_OF(CBS_CONFIG,CbsCmnCpuReqMinFreq)},
  //ACPI CST C2 Latency
  {IDSNVID_CMN_CPU_CST_C2_LATENCY, OFFSET_OF(CBS_CONFIG,CbsCmnCpuCstC2Latency)},
  //MCA error thresh count
  {IDSNVID_CMN_CPU_MCA_ERR_THRESH_COUNT, OFFSET_OF(CBS_CONFIG,CbsCmnCpuMcaErrThreshCount)},
  //Socket0 RootBridge Mask for 64Bit MMIO RMP Coverage
  {IDSNVID_CMN_CPU64_BIT_MMIO_RMP_S0_RB_MASK, OFFSET_OF(CBS_CONFIG,CbsCmnCpu64BitMMIORmpS0RBMask)},
  //Socket1 RootBridge Mask for 64Bit MMIO RMP Coverage
  {IDSNVID_CMN_CPU64_BIT_MMIO_RMP_S1_RB_MASK, OFFSET_OF(CBS_CONFIG,CbsCmnCpu64BitMMIORmpS1RBMask)},
  //Core Watchdog Timer Interval
  {IDSNVID_DBG_CPU_GEN_CPU_WDT_TIMEOUT, OFFSET_OF(CBS_CONFIG,CbsDbgCpuGenCpuWdtTimeout)},
  //Initial Preset Socket 0 Link 0 Pstate0/1/2/3 (APCB)
  {IDSNVID_DF_XGMI_INIT_PRESET_S0_L0, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS0L0)},
  //Initial Preset Socket 0 Link 0 Pstate0
  {IDSNVID_DF_XGMI_INIT_PRESET_S0_L0_P0, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS0L0P0)},
  //Initial Preset Socket 0 Link 0 Pstate1
  {IDSNVID_DF_XGMI_INIT_PRESET_S0_L0_P1, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS0L0P1)},
  //Initial Preset Socket 0 Link 0 Pstate2
  {IDSNVID_DF_XGMI_INIT_PRESET_S0_L0_P2, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS0L0P2)},
  //Initial Preset Socket 0 Link 0 Pstate3
  {IDSNVID_DF_XGMI_INIT_PRESET_S0_L0_P3, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS0L0P3)},
  //Initial Preset Socket 0 Link 1 Pstate0/1/2/3 (APCB)
  {IDSNVID_DF_XGMI_INIT_PRESET_S0_L1, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS0L1)},
  //Initial Preset Socket 0 Link 1 Pstate0
  {IDSNVID_DF_XGMI_INIT_PRESET_S0_L1_P0, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS0L1P0)},
  //Initial Preset Socket 0 Link 1 Pstate1
  {IDSNVID_DF_XGMI_INIT_PRESET_S0_L1_P1, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS0L1P1)},
  //Initial Preset Socket 0 Link 1 Pstate2
  {IDSNVID_DF_XGMI_INIT_PRESET_S0_L1_P2, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS0L1P2)},
  //Initial Preset Socket 0 Link 1 Pstate3
  {IDSNVID_DF_XGMI_INIT_PRESET_S0_L1_P3, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS0L1P3)},
  //Initial Preset Socket 0 Link 2 Pstate0/1/2/3 (APCB)
  {IDSNVID_DF_XGMI_INIT_PRESET_S0_L2, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS0L2)},
  //Initial Preset Socket 0 Link 2 Pstate0
  {IDSNVID_DF_XGMI_INIT_PRESET_S0_L2_P0, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS0L2P0)},
  //Initial Preset Socket 0 Link 2 Pstate1
  {IDSNVID_DF_XGMI_INIT_PRESET_S0_L2_P1, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS0L2P1)},
  //Initial Preset Socket 0 Link 2 Pstate2
  {IDSNVID_DF_XGMI_INIT_PRESET_S0_L2_P2, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS0L2P2)},
  //Initial Preset Socket 0 Link 2 Pstate3
  {IDSNVID_DF_XGMI_INIT_PRESET_S0_L2_P3, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS0L2P3)},
  //Initial Preset Socket 0 Link 3 Pstate0/1/2/3 (APCB)
  {IDSNVID_DF_XGMI_INIT_PRESET_S0_L3, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS0L3)},
  //Initial Preset Socket 0 Link 3 Pstate0
  {IDSNVID_DF_XGMI_INIT_PRESET_S0_L3_P0, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS0L3P0)},
  //Initial Preset Socket 0 Link 3 Pstate1
  {IDSNVID_DF_XGMI_INIT_PRESET_S0_L3_P1, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS0L3P1)},
  //Initial Preset Socket 0 Link 3 Pstate2
  {IDSNVID_DF_XGMI_INIT_PRESET_S0_L3_P2, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS0L3P2)},
  //Initial Preset Socket 0 Link 3 Pstate3
  {IDSNVID_DF_XGMI_INIT_PRESET_S0_L3_P3, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS0L3P3)},
  //Initial Preset Socket 1 Link 0 Pstate0/1/2/3 (APCB)
  {IDSNVID_DF_XGMI_INIT_PRESET_S1_L0, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS1L0)},
  //Initial Preset Socket 1 Link 0 Pstate0
  {IDSNVID_DF_XGMI_INIT_PRESET_S1_L0_P0, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS1L0P0)},
  //Initial Preset Socket 1 Link 0 Pstate1
  {IDSNVID_DF_XGMI_INIT_PRESET_S1_L0_P1, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS1L0P1)},
  //Initial Preset Socket 1 Link 0 Pstate2
  {IDSNVID_DF_XGMI_INIT_PRESET_S1_L0_P2, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS1L0P2)},
  //Initial Preset Socket 1 Link 0 Pstate3
  {IDSNVID_DF_XGMI_INIT_PRESET_S1_L0_P3, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS1L0P3)},
  //Initial Preset Socket 1 Link 1 Pstate0/1/2/3 (APCB)
  {IDSNVID_DF_XGMI_INIT_PRESET_S1_L1, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS1L1)},
  //Initial Preset Socket 1 Link 1 Pstate0
  {IDSNVID_DF_XGMI_INIT_PRESET_S1_L1_P0, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS1L1P0)},
  //Initial Preset Socket 1 Link 1 Pstate1
  {IDSNVID_DF_XGMI_INIT_PRESET_S1_L1_P1, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS1L1P1)},
  //Initial Preset Socket 1 Link 1 Pstate2
  {IDSNVID_DF_XGMI_INIT_PRESET_S1_L1_P2, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS1L1P2)},
  //Initial Preset Socket 1 Link 1 Pstate3
  {IDSNVID_DF_XGMI_INIT_PRESET_S1_L1_P3, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS1L1P3)},
  //Initial Preset Socket 1 Link 2 Pstate0/1/2/3 (APCB)
  {IDSNVID_DF_XGMI_INIT_PRESET_S1_L2, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS1L2)},
  //Initial Preset Socket 1 Link 2 Pstate0
  {IDSNVID_DF_XGMI_INIT_PRESET_S1_L2_P0, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS1L2P0)},
  //Initial Preset Socket 1 Link 2 Pstate1
  {IDSNVID_DF_XGMI_INIT_PRESET_S1_L2_P1, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS1L2P1)},
  //Initial Preset Socket 1 Link 2 Pstate2
  {IDSNVID_DF_XGMI_INIT_PRESET_S1_L2_P2, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS1L2P2)},
  //Initial Preset Socket 1 Link 2 Pstate3
  {IDSNVID_DF_XGMI_INIT_PRESET_S1_L2_P3, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS1L2P3)},
  //Initial Preset Socket 1 Link 3 Pstate0/1/2/3 (APCB)
  {IDSNVID_DF_XGMI_INIT_PRESET_S1_L3, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS1L3)},
  //Initial Preset Socket 1 Link 3 Pstate0
  {IDSNVID_DF_XGMI_INIT_PRESET_S1_L3_P0, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS1L3P0)},
  //Initial Preset Socket 1 Link 3 Pstate1
  {IDSNVID_DF_XGMI_INIT_PRESET_S1_L3_P1, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS1L3P1)},
  //Initial Preset Socket 1 Link 3 Pstate2
  {IDSNVID_DF_XGMI_INIT_PRESET_S1_L3_P2, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS1L3P2)},
  //Initial Preset Socket 1 Link 3 Pstate3
  {IDSNVID_DF_XGMI_INIT_PRESET_S1_L3_P3, OFFSET_OF(CBS_CONFIG,CbsDfXgmiInitPresetS1L3P3)},
  //PMU Mem BIST Algorithm Bitmask
  {IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM_BIT_MASK_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemPmuBistAlgorithmBitMaskDdr)},
  //DRAM Corrected Error Counter Start Count
  {IDSNVID_CMN_MEM_CORRECTED_ERROR_COUNTER_START_COUNT, OFFSET_OF(CBS_CONFIG,CbsCmnMemCorrectedErrorCounterStartCount)},
  //tECSint
  {IDSNVID_CMN_MEMT_EC_SINT_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemtECSintDdr)},
  //Memory Target Speed
  {IDSNVID_CMN_MEM_TARGET_SPEED_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTargetSpeedDdr)},
  //Tcl
  {IDSNVID_CMN_MEM_TIMING_TCL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTclDdr)},
  //Trcd
  {IDSNVID_CMN_MEM_TIMING_TRCD_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrcdDdr)},
  //Trp
  {IDSNVID_CMN_MEM_TIMING_TRP_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrpDdr)},
  //Tras
  {IDSNVID_CMN_MEM_TIMING_TRAS_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrasDdr)},
  //Trc
  {IDSNVID_CMN_MEM_TIMING_TRC_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrcDdr)},
  //Twr
  {IDSNVID_CMN_MEM_TIMING_TWR_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTwrDdr)},
  //Trfc1
  {IDSNVID_CMN_MEM_TIMING_TRFC1_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrfc1Ddr)},
  //Trfc2
  {IDSNVID_CMN_MEM_TIMING_TRFC2_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrfc2Ddr)},
  //TrfcSb
  {IDSNVID_CMN_MEM_TIMING_TRFC_SB_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrfcSbDdr)},
  //Tcwl
  {IDSNVID_CMN_MEM_TIMING_TCWL_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTcwlDdr)},
  //Trtp
  {IDSNVID_CMN_MEM_TIMING_TRTP_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrtpDdr)},
  //TrrdL
  {IDSNVID_CMN_MEM_TIMING_TRRD_L_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrrdLDdr)},
  //TrrdS
  {IDSNVID_CMN_MEM_TIMING_TRRD_S_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrrdSDdr)},
  //Tfaw
  {IDSNVID_CMN_MEM_TIMING_TFAW_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTfawDdr)},
  //TwtrL
  {IDSNVID_CMN_MEM_TIMING_TWTR_L_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTwtrLDdr)},
  //TwtrS
  {IDSNVID_CMN_MEM_TIMING_TWTR_S_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTwtrSDdr)},
  //TrdrdScL
  {IDSNVID_CMN_MEM_TIMING_TRDRD_SC_L_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrdrdScLDdr)},
  //TrdrdSc
  {IDSNVID_CMN_MEM_TIMING_TRDRD_SC_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrdrdScDdr)},
  //TrdrdSd
  {IDSNVID_CMN_MEM_TIMING_TRDRD_SD_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrdrdSdDdr)},
  //TrdrdDd
  {IDSNVID_CMN_MEM_TIMING_TRDRD_DD_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrdrdDdDdr)},
  //TwrwrScL
  {IDSNVID_CMN_MEM_TIMING_TWRWR_SC_L_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTwrwrScLDdr)},
  //TwrwrSc
  {IDSNVID_CMN_MEM_TIMING_TWRWR_SC_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTwrwrScDdr)},
  //TwrwrSd
  {IDSNVID_CMN_MEM_TIMING_TWRWR_SD_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTwrwrSdDdr)},
  //TwrwrDd
  {IDSNVID_CMN_MEM_TIMING_TWRWR_DD_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTwrwrDdDdr)},
  //Twrrd
  {IDSNVID_CMN_MEM_TIMING_TWRRD_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTwrrdDdr)},
  //Trdwr
  {IDSNVID_CMN_MEM_TIMING_TRDWR_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemTimingTrdwrDdr)},
  //Periodic Interval
  {IDSNVID_CMN_MEM_PERIODIC_INTERVAL, OFFSET_OF(CBS_CONFIG,CbsCmnMemPeriodicInterval)},
  //PMIC SWA/SWB VDD Core
  {IDSNVID_CMN_MEM_CTRLLER_PMIC_SWA_SWB_VDD_CORE, OFFSET_OF(CBS_CONFIG,CbsCmnMemCtrllerPmicSwaSwbVddCore)},
  //PMIC SWC VDDIO
  {IDSNVID_CMN_MEM_CTRLLER_PMIC_SWC_VDDIO, OFFSET_OF(CBS_CONFIG,CbsCmnMemCtrllerPmicSwcVddio)},
  //PMIC SWD VPP
  {IDSNVID_CMN_MEM_CTRLLER_PMIC_SWD_VPP, OFFSET_OF(CBS_CONFIG,CbsCmnMemCtrllerPmicSwdVpp)},
  //ACS RAS Request Value
  {IDSNVID_ACS_RAS_VALUE, OFFSET_OF(CBS_CONFIG,AcsRasValue)},
  //Allow Compliance
  {IDSNVID_STRAP_COMPLIANCE_DIS, OFFSET_OF(CBS_CONFIG,STRAP_COMPLIANCE_DIS)},
  //Preset Search Mask (Gen3)
  {IDSNVID_CMN_NBIO_PCIE_SEARCH_MASK_GEN3, OFFSET_OF(CBS_CONFIG,CbsCmnNbioPcieSearchMaskGen3)},
  //Preset Search Mask (Gen4)
  {IDSNVID_CMN_NBIO_PCIE_SEARCH_MASK_GEN4, OFFSET_OF(CBS_CONFIG,CbsCmnNbioPcieSearchMaskGen4)},
  //Preset Search Mask (Gen5)
  {IDSNVID_CMN_NBIO_PCIE_SEARCH_MASK_GEN5, OFFSET_OF(CBS_CONFIG,CbsCmnNbioPcieSearchMaskGen5)},
  //I2C 0 SDA TX HOLD VALUE
  {IDSNVID_CMN_FCH_I2C0_SDA_TX_HOLD_VALUE, OFFSET_OF(CBS_CONFIG,CbsCmnFchI2c0SdaTxHoldValue)},
  //I2C 1 SDA TX HOLD VALUE
  {IDSNVID_CMN_FCH_I2C1_SDA_TX_HOLD_VALUE, OFFSET_OF(CBS_CONFIG,CbsCmnFchI2c1SdaTxHoldValue)},
  //I2C 2 SDA TX HOLD VALUE
  {IDSNVID_CMN_FCH_I2C2_SDA_TX_HOLD_VALUE, OFFSET_OF(CBS_CONFIG,CbsCmnFchI2c2SdaTxHoldValue)},
  //I2C 3 SDA TX HOLD VALUE
  {IDSNVID_CMN_FCH_I2C3_SDA_TX_HOLD_VALUE, OFFSET_OF(CBS_CONFIG,CbsCmnFchI2c3SdaTxHoldValue)},
  //I2C 4 SDA TX HOLD VALUE
  {IDSNVID_CMN_FCH_I2C4_SDA_TX_HOLD_VALUE, OFFSET_OF(CBS_CONFIG,CbsCmnFchI2c4SdaTxHoldValue)},
  //I2C 5 SDA TX HOLD VALUE
  {IDSNVID_CMN_FCH_I2C5_SDA_TX_HOLD_VALUE, OFFSET_OF(CBS_CONFIG,CbsCmnFchI2c5SdaTxHoldValue)},
  //ABL Console out Serial Port IO Customized
  {IDSNVID_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_IO_CUSTOM, OFFSET_OF(CBS_CONFIG,CbsCmnSocAblConOutSerialPortIOCustom)},
  //End
  {0xFFFF, 0}};

STATIC IDS_NV_RECORD mIdsNv32[] = {
  //SEV-ES ASID Space Limit
  {IDSNVID_CMN_CPU_SEV_ASID_SPACE_LIMIT, OFFSET_OF(CBS_CONFIG,CbsCmnCpuSevAsidSpaceLimit)},
  //Amount of Memory to Cover
  {IDSNVID_DBG_CPU_SNP_MEM_SIZE_COVER, OFFSET_OF(CBS_CONFIG,CbsDbgCpuSnpMemSizeCover)},
  //Pstate0 Freq (MHz)
  {IDSNVID_CPU_PST0_FREQ, OFFSET_OF(CBS_CONFIG,CbsCpuPst0Freq)},
  //Frequency (MHz)
  {IDSNVID_CPU_COF_P0, OFFSET_OF(CBS_CONFIG,CbsCpuCofP0)},
  //Voltage (uV)
  {IDSNVID_CPU_VOLTAGE_P0, OFFSET_OF(CBS_CONFIG,CbsCpuVoltageP0)},
  //Pstate0 FID
  {IDSNVID_CPU_PST0_FID, OFFSET_OF(CBS_CONFIG,CbsCpuPst0Fid)},
  //Pstate0 VID
  {IDSNVID_CPU_PST0_VID, OFFSET_OF(CBS_CONFIG,CbsCpuPst0Vid)},
  //Frequency (MHz)
  {IDSNVID_CPU_COF_P1, OFFSET_OF(CBS_CONFIG,CbsCpuCofP1)},
  //Voltage (uV)
  {IDSNVID_CPU_VOLTAGE_P1, OFFSET_OF(CBS_CONFIG,CbsCpuVoltageP1)},
  //Pstate1 FID
  {IDSNVID_CPU_PST1_FID, OFFSET_OF(CBS_CONFIG,CbsCpuPst1Fid)},
  //Pstate1 VID
  {IDSNVID_CPU_PST1_VID, OFFSET_OF(CBS_CONFIG,CbsCpuPst1Vid)},
  //Frequency (MHz)
  {IDSNVID_CPU_COF_P2, OFFSET_OF(CBS_CONFIG,CbsCpuCofP2)},
  //Voltage (uV)
  {IDSNVID_CPU_VOLTAGE_P2, OFFSET_OF(CBS_CONFIG,CbsCpuVoltageP2)},
  //Pstate2 FID
  {IDSNVID_CPU_PST2_FID, OFFSET_OF(CBS_CONFIG,CbsCpuPst2Fid)},
  //Pstate2 VID
  {IDSNVID_CPU_PST2_VID, OFFSET_OF(CBS_CONFIG,CbsCpuPst2Vid)},
  //Frequency (MHz)
  {IDSNVID_CPU_COF_P3, OFFSET_OF(CBS_CONFIG,CbsCpuCofP3)},
  //Voltage (uV)
  {IDSNVID_CPU_VOLTAGE_P3, OFFSET_OF(CBS_CONFIG,CbsCpuVoltageP3)},
  //Pstate3 FID
  {IDSNVID_CPU_PST3_FID, OFFSET_OF(CBS_CONFIG,CbsCpuPst3Fid)},
  //Pstate3 VID
  {IDSNVID_CPU_PST3_VID, OFFSET_OF(CBS_CONFIG,CbsCpuPst3Vid)},
  //Frequency (MHz)
  {IDSNVID_CPU_COF_P4, OFFSET_OF(CBS_CONFIG,CbsCpuCofP4)},
  //Voltage (uV)
  {IDSNVID_CPU_VOLTAGE_P4, OFFSET_OF(CBS_CONFIG,CbsCpuVoltageP4)},
  //Pstate4 FID
  {IDSNVID_CPU_PST4_FID, OFFSET_OF(CBS_CONFIG,CbsCpuPst4Fid)},
  //Pstate4 VID
  {IDSNVID_CPU_PST4_VID, OFFSET_OF(CBS_CONFIG,CbsCpuPst4Vid)},
  //Frequency (MHz)
  {IDSNVID_CPU_COF_P5, OFFSET_OF(CBS_CONFIG,CbsCpuCofP5)},
  //Voltage (uV)
  {IDSNVID_CPU_VOLTAGE_P5, OFFSET_OF(CBS_CONFIG,CbsCpuVoltageP5)},
  //Pstate5 FID
  {IDSNVID_CPU_PST5_FID, OFFSET_OF(CBS_CONFIG,CbsCpuPst5Fid)},
  //Pstate5 VID
  {IDSNVID_CPU_PST5_VID, OFFSET_OF(CBS_CONFIG,CbsCpuPst5Vid)},
  //Frequency (MHz)
  {IDSNVID_CPU_COF_P6, OFFSET_OF(CBS_CONFIG,CbsCpuCofP6)},
  //Voltage (uV)
  {IDSNVID_CPU_VOLTAGE_P6, OFFSET_OF(CBS_CONFIG,CbsCpuVoltageP6)},
  //Pstate6 FID
  {IDSNVID_CPU_PST6_FID, OFFSET_OF(CBS_CONFIG,CbsCpuPst6Fid)},
  //Pstate6 VID
  {IDSNVID_CPU_PST6_VID, OFFSET_OF(CBS_CONFIG,CbsCpuPst6Vid)},
  //Frequency (MHz)
  {IDSNVID_CPU_COF_P7, OFFSET_OF(CBS_CONFIG,CbsCpuCofP7)},
  //Voltage (uV)
  {IDSNVID_CPU_VOLTAGE_P7, OFFSET_OF(CBS_CONFIG,CbsCpuVoltageP7)},
  //Pstate7 FID
  {IDSNVID_CPU_PST7_FID, OFFSET_OF(CBS_CONFIG,CbsCpuPst7Fid)},
  //Pstate7 VID
  {IDSNVID_CPU_PST7_VID, OFFSET_OF(CBS_CONFIG,CbsCpuPst7Vid)},
  //CCD 0 DownCore Bitmap
  {IDSNVID_CMN_CPU_CCD0_DOWNCORE_BIT_MAP, OFFSET_OF(CBS_CONFIG,CbsCmnCpuCcd0DowncoreBitMap)},
  //CCD 1 DownCore Bitmap
  {IDSNVID_CMN_CPU_CCD1_DOWNCORE_BIT_MAP, OFFSET_OF(CBS_CONFIG,CbsCmnCpuCcd1DowncoreBitMap)},
  //CCD 2 DownCore Bitmap
  {IDSNVID_CMN_CPU_CCD2_DOWNCORE_BIT_MAP, OFFSET_OF(CBS_CONFIG,CbsCmnCpuCcd2DowncoreBitMap)},
  //CCD 3 DownCore Bitmap
  {IDSNVID_CMN_CPU_CCD3_DOWNCORE_BIT_MAP, OFFSET_OF(CBS_CONFIG,CbsCmnCpuCcd3DowncoreBitMap)},
  //CCD 4 DownCore Bitmap
  {IDSNVID_CMN_CPU_CCD4_DOWNCORE_BIT_MAP, OFFSET_OF(CBS_CONFIG,CbsCmnCpuCcd4DowncoreBitMap)},
  //CCD 5 DownCore Bitmap
  {IDSNVID_CMN_CPU_CCD5_DOWNCORE_BIT_MAP, OFFSET_OF(CBS_CONFIG,CbsCmnCpuCcd5DowncoreBitMap)},
  //CCD 6 DownCore Bitmap
  {IDSNVID_CMN_CPU_CCD6_DOWNCORE_BIT_MAP, OFFSET_OF(CBS_CONFIG,CbsCmnCpuCcd6DowncoreBitMap)},
  //CCD 7 DownCore Bitmap
  {IDSNVID_CMN_CPU_CCD7_DOWNCORE_BIT_MAP, OFFSET_OF(CBS_CONFIG,CbsCmnCpuCcd7DowncoreBitMap)},
  //CCD 8 DownCore Bitmap
  {IDSNVID_CMN_CPU_CCD8_DOWNCORE_BIT_MAP, OFFSET_OF(CBS_CONFIG,CbsCmnCpuCcd8DowncoreBitMap)},
  //CCD 9 DownCore Bitmap
  {IDSNVID_CMN_CPU_CCD9_DOWNCORE_BIT_MAP, OFFSET_OF(CBS_CONFIG,CbsCmnCpuCcd9DowncoreBitMap)},
  //CCD 10 DownCore Bitmap
  {IDSNVID_CMN_CPU_CCD10_DOWNCORE_BIT_MAP, OFFSET_OF(CBS_CONFIG,CbsCmnCpuCcd10DowncoreBitMap)},
  //CCD 11 DownCore Bitmap
  {IDSNVID_CMN_CPU_CCD11_DOWNCORE_BIT_MAP, OFFSET_OF(CBS_CONFIG,CbsCmnCpuCcd11DowncoreBitMap)},
  //CCD 12 DownCore Bitmap
  {IDSNVID_CMN_CPU_CCD12_DOWNCORE_BIT_MAP, OFFSET_OF(CBS_CONFIG,CbsCmnCpuCcd12DowncoreBitMap)},
  //CCD 13 DownCore Bitmap
  {IDSNVID_CMN_CPU_CCD13_DOWNCORE_BIT_MAP, OFFSET_OF(CBS_CONFIG,CbsCmnCpuCcd13DowncoreBitMap)},
  //CCD 14 DownCore Bitmap
  {IDSNVID_CMN_CPU_CCD14_DOWNCORE_BIT_MAP, OFFSET_OF(CBS_CONFIG,CbsCmnCpuCcd14DowncoreBitMap)},
  //CCD 15 DownCore Bitmap
  {IDSNVID_CMN_CPU_CCD15_DOWNCORE_BIT_MAP, OFFSET_OF(CBS_CONFIG,CbsCmnCpuCcd15DowncoreBitMap)},
  //Number of PCI Segments
  {IDSNVID_DF_DBG_NUM_PCI_SEGMENTS, OFFSET_OF(CBS_CONFIG,CbsDfDbgNumPciSegments)},
  //Preset P11 (APCB)
  {IDSNVID_DF_XGMI_PRESET_P11, OFFSET_OF(CBS_CONFIG,CbsDfXgmiPresetP11)},
  //Preset P11 Cmn1
  {IDSNVID_DF_XGMI_CMN1_P11, OFFSET_OF(CBS_CONFIG,CbsDfXgmiCmn1P11)},
  //Preset P11 Cn
  {IDSNVID_DF_XGMI_CN_P11, OFFSET_OF(CBS_CONFIG,CbsDfXgmiCnP11)},
  //Preset P11 Cnp1
  {IDSNVID_DF_XGMI_CNP1_P11, OFFSET_OF(CBS_CONFIG,CbsDfXgmiCnp1P11)},
  //Preset P12 (APCB)
  {IDSNVID_DF_XGMI_PRESET_P12, OFFSET_OF(CBS_CONFIG,CbsDfXgmiPresetP12)},
  //Preset P12 Cmn1
  {IDSNVID_DF_XGMI_CMN1_P12, OFFSET_OF(CBS_CONFIG,CbsDfXgmiCmn1P12)},
  //Preset P12 Cn
  {IDSNVID_DF_XGMI_CN_P12, OFFSET_OF(CBS_CONFIG,CbsDfXgmiCnP12)},
  //Preset P12 Cnp1
  {IDSNVID_DF_XGMI_CNP1_P12, OFFSET_OF(CBS_CONFIG,CbsDfXgmiCnp1P12)},
  //Preset P13 (APCB)
  {IDSNVID_DF_XGMI_PRESET_P13, OFFSET_OF(CBS_CONFIG,CbsDfXgmiPresetP13)},
  //Preset P13 Cmn1
  {IDSNVID_DF_XGMI_CMN1_P13, OFFSET_OF(CBS_CONFIG,CbsDfXgmiCmn1P13)},
  //Preset P13 Cn
  {IDSNVID_DF_XGMI_CN_P13, OFFSET_OF(CBS_CONFIG,CbsDfXgmiCnP13)},
  //Preset P13 Cnp1
  {IDSNVID_DF_XGMI_CNP1_P13, OFFSET_OF(CBS_CONFIG,CbsDfXgmiCnp1P13)},
  //Preset P14 (APCB)
  {IDSNVID_DF_XGMI_PRESET_P14, OFFSET_OF(CBS_CONFIG,CbsDfXgmiPresetP14)},
  //Preset P14 Cmn1
  {IDSNVID_DF_XGMI_CMN1_P14, OFFSET_OF(CBS_CONFIG,CbsDfXgmiCmn1P14)},
  //Preset P14 Cn
  {IDSNVID_DF_XGMI_CN_P14, OFFSET_OF(CBS_CONFIG,CbsDfXgmiCnP14)},
  //Preset P14 Cnp1
  {IDSNVID_DF_XGMI_CNP1_P14, OFFSET_OF(CBS_CONFIG,CbsDfXgmiCnp1P14)},
  //Preset P15 (APCB)
  {IDSNVID_DF_XGMI_PRESET_P15, OFFSET_OF(CBS_CONFIG,CbsDfXgmiPresetP15)},
  //Preset P15 Cmn1
  {IDSNVID_DF_XGMI_CMN1_P15, OFFSET_OF(CBS_CONFIG,CbsDfXgmiCmn1P15)},
  //Preset P15 Cn
  {IDSNVID_DF_XGMI_CN_P15, OFFSET_OF(CBS_CONFIG,CbsDfXgmiCnP15)},
  //Preset P15 Cnp1
  {IDSNVID_DF_XGMI_CNP1_P15, OFFSET_OF(CBS_CONFIG,CbsDfXgmiCnp1P15)},
  //TXEQ Search Mask Socket 0 Link 0 Pstate0/1 (APCB)
  {IDSNVID_DF_XGMI_TXEQ_S0_L0_P01, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS0L0P01)},
  //TXEQ Search Mask Socket 0 Link 0 Pstate2/3 (APCB)
  {IDSNVID_DF_XGMI_TXEQ_S0_L0_P23, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS0L0P23)},
  //TXEQ Search Mask Socket 0 Link 0 Pstate0
  {IDSNVID_DF_XGMI_TXEQ_S0_L0_P0, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS0L0P0)},
  //TXEQ Search Mask Socket 0 Link 0 Pstate1
  {IDSNVID_DF_XGMI_TXEQ_S0_L0_P1, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS0L0P1)},
  //TXEQ Search Mask Socket 0 Link 0 Pstate2
  {IDSNVID_DF_XGMI_TXEQ_S0_L0_P2, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS0L0P2)},
  //TXEQ Search Mask Socket 0 Link 0 Pstate3
  {IDSNVID_DF_XGMI_TXEQ_S0_L0_P3, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS0L0P3)},
  //TXEQ Search Mask Socket 0 Link 1 Pstate0/1 (APCB)
  {IDSNVID_DF_XGMI_TXEQ_S0_L1_P01, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS0L1P01)},
  //TXEQ Search Mask Socket 0 Link 1 Pstate2/3 (APCB)
  {IDSNVID_DF_XGMI_TXEQ_S0_L1_P23, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS0L1P23)},
  //TXEQ Search Mask Socket 0 Link 1 Pstate0
  {IDSNVID_DF_XGMI_TXEQ_S0_L1_P0, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS0L1P0)},
  //TXEQ Search Mask Socket 0 Link 1 Pstate1
  {IDSNVID_DF_XGMI_TXEQ_S0_L1_P1, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS0L1P1)},
  //TXEQ Search Mask Socket 0 Link 1 Pstate2
  {IDSNVID_DF_XGMI_TXEQ_S0_L1_P2, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS0L1P2)},
  //TXEQ Search Mask Socket 0 Link 1 Pstate3
  {IDSNVID_DF_XGMI_TXEQ_S0_L1_P3, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS0L1P3)},
  //TXEQ Search Mask Socket 0 Link 2 Pstate0/1 (APCB)
  {IDSNVID_DF_XGMI_TXEQ_S0_L2_P01, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS0L2P01)},
  //TXEQ Search Mask Socket 0 Link 2 Pstate2/3 (APCB)
  {IDSNVID_DF_XGMI_TXEQ_S0_L2_P23, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS0L2P23)},
  //TXEQ Search Mask Socket 0 Link 2 Pstate0
  {IDSNVID_DF_XGMI_TXEQ_S0_L2_P0, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS0L2P0)},
  //TXEQ Search Mask Socket 0 Link 2 Pstate1
  {IDSNVID_DF_XGMI_TXEQ_S0_L2_P1, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS0L2P1)},
  //TXEQ Search Mask Socket 0 Link 2 Pstate2
  {IDSNVID_DF_XGMI_TXEQ_S0_L2_P2, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS0L2P2)},
  //TXEQ Search Mask Socket 0 Link 2 Pstate3
  {IDSNVID_DF_XGMI_TXEQ_S0_L2_P3, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS0L2P3)},
  //TXEQ Search Mask Socket 0 Link 3 Pstate0/1 (APCB)
  {IDSNVID_DF_XGMI_TXEQ_S0_L3_P01, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS0L3P01)},
  //TXEQ Search Mask Socket 0 Link 3 Pstate2/3 (APCB)
  {IDSNVID_DF_XGMI_TXEQ_S0_L3_P23, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS0L3P23)},
  //TXEQ Search Mask Socket 0 Link 3 Pstate0
  {IDSNVID_DF_XGMI_TXEQ_S0_L3_P0, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS0L3P0)},
  //TXEQ Search Mask Socket 0 Link 3 Pstate1
  {IDSNVID_DF_XGMI_TXEQ_S0_L3_P1, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS0L3P1)},
  //TXEQ Search Mask Socket 0 Link 3 Pstate2
  {IDSNVID_DF_XGMI_TXEQ_S0_L3_P2, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS0L3P2)},
  //TXEQ Search Mask Socket 0 Link 3 Pstate3
  {IDSNVID_DF_XGMI_TXEQ_S0_L3_P3, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS0L3P3)},
  //TXEQ Search Mask Socket 1 Link 0 Pstate0/1 (APCB)
  {IDSNVID_DF_XGMI_TXEQ_S1_L0_P01, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS1L0P01)},
  //TXEQ Search Mask Socket 1 Link 0 Pstate2/3 (APCB)
  {IDSNVID_DF_XGMI_TXEQ_S1_L0_P23, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS1L0P23)},
  //TXEQ Search Mask Socket 1 Link 0 Pstate0
  {IDSNVID_DF_XGMI_TXEQ_S1_L0_P0, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS1L0P0)},
  //TXEQ Search Mask Socket 1 Link 0 Pstate1
  {IDSNVID_DF_XGMI_TXEQ_S1_L0_P1, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS1L0P1)},
  //TXEQ Search Mask Socket 1 Link 0 Pstate2
  {IDSNVID_DF_XGMI_TXEQ_S1_L0_P2, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS1L0P2)},
  //TXEQ Search Mask Socket 1 Link 0 Pstate3
  {IDSNVID_DF_XGMI_TXEQ_S1_L0_P3, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS1L0P3)},
  //TXEQ Search Mask Socket 1 Link 1 Pstate0/1 (APCB)
  {IDSNVID_DF_XGMI_TXEQ_S1_L1_P01, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS1L1P01)},
  //TXEQ Search Mask Socket 1 Link 1 Pstate2/3 (APCB)
  {IDSNVID_DF_XGMI_TXEQ_S1_L1_P23, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS1L1P23)},
  //TXEQ Search Mask Socket 1 Link 1 Pstate0
  {IDSNVID_DF_XGMI_TXEQ_S1_L1_P0, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS1L1P0)},
  //TXEQ Search Mask Socket 1 Link 1 Pstate1
  {IDSNVID_DF_XGMI_TXEQ_S1_L1_P1, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS1L1P1)},
  //TXEQ Search Mask Socket 1 Link 1 Pstate2
  {IDSNVID_DF_XGMI_TXEQ_S1_L1_P2, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS1L1P2)},
  //TXEQ Search Mask Socket 1 Link 1 Pstate3
  {IDSNVID_DF_XGMI_TXEQ_S1_L1_P3, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS1L1P3)},
  //TXEQ Search Mask Socket 1 Link 2 Pstate0/1 (APCB)
  {IDSNVID_DF_XGMI_TXEQ_S1_L2_P01, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS1L2P01)},
  //TXEQ Search Mask Socket 1 Link 2 Pstate2/3 (APCB)
  {IDSNVID_DF_XGMI_TXEQ_S1_L2_P23, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS1L2P23)},
  //TXEQ Search Mask Socket 1 Link 2 Pstate0
  {IDSNVID_DF_XGMI_TXEQ_S1_L2_P0, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS1L2P0)},
  //TXEQ Search Mask Socket 1 Link 2 Pstate1
  {IDSNVID_DF_XGMI_TXEQ_S1_L2_P1, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS1L2P1)},
  //TXEQ Search Mask Socket 1 Link 2 Pstate2
  {IDSNVID_DF_XGMI_TXEQ_S1_L2_P2, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS1L2P2)},
  //TXEQ Search Mask Socket 1 Link 2 Pstate3
  {IDSNVID_DF_XGMI_TXEQ_S1_L2_P3, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS1L2P3)},
  //TXEQ Search Mask Socket 1 Link 3 Pstate0/1 (APCB)
  {IDSNVID_DF_XGMI_TXEQ_S1_L3_P01, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS1L3P01)},
  //TXEQ Search Mask Socket 1 Link 3 Pstate2/3 (APCB)
  {IDSNVID_DF_XGMI_TXEQ_S1_L3_P23, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS1L3P23)},
  //TXEQ Search Mask Socket 1 Link 3 Pstate0
  {IDSNVID_DF_XGMI_TXEQ_S1_L3_P0, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS1L3P0)},
  //TXEQ Search Mask Socket 1 Link 3 Pstate1
  {IDSNVID_DF_XGMI_TXEQ_S1_L3_P1, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS1L3P1)},
  //TXEQ Search Mask Socket 1 Link 3 Pstate2
  {IDSNVID_DF_XGMI_TXEQ_S1_L3_P2, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS1L3P2)},
  //TXEQ Search Mask Socket 1 Link 3 Pstate3
  {IDSNVID_DF_XGMI_TXEQ_S1_L3_P3, OFFSET_OF(CBS_CONFIG,CbsDfXgmiTxeqS1L3P3)},
  //xGMI Channel Type (APCB)
  {IDSNVID_DF_XGMI_CHANNEL_TYPE, OFFSET_OF(CBS_CONFIG,CbsDfXgmiChannelType)},
  //xGMI Channel Type Socket 0 Link 0
  {IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK0, OFFSET_OF(CBS_CONFIG,CbsDfXgmiChannelTypeSocket0Link0)},
  //xGMI Channel Type Socket 0 Link 1
  {IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK1, OFFSET_OF(CBS_CONFIG,CbsDfXgmiChannelTypeSocket0Link1)},
  //xGMI Channel Type Socket 0 Link 2
  {IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK2, OFFSET_OF(CBS_CONFIG,CbsDfXgmiChannelTypeSocket0Link2)},
  //xGMI Channel Type Socket 0 Link 3
  {IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK3, OFFSET_OF(CBS_CONFIG,CbsDfXgmiChannelTypeSocket0Link3)},
  //xGMI Channel Type Socket 1 Link 0
  {IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK0, OFFSET_OF(CBS_CONFIG,CbsDfXgmiChannelTypeSocket1Link0)},
  //xGMI Channel Type Socket 1 Link 1
  {IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK1, OFFSET_OF(CBS_CONFIG,CbsDfXgmiChannelTypeSocket1Link1)},
  //xGMI Channel Type Socket 1 Link 2
  {IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK2, OFFSET_OF(CBS_CONFIG,CbsDfXgmiChannelTypeSocket1Link2)},
  //xGMI Channel Type Socket 1 Link 3
  {IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK3, OFFSET_OF(CBS_CONFIG,CbsDfXgmiChannelTypeSocket1Link3)},
  //Memory Channel Disable Bitmask
  {IDSNVID_CMN_MEM_CHANNEL_DISABLE_BITMASK_DDR, OFFSET_OF(CBS_CONFIG,CbsCmnMemChannelDisableBitmaskDdr)},
  //Aggressor Static Lane Select Upper 32 bits
  {IDSNVID_CMN_MEM_MBIST_AGGR_STATIC_LANE_SEL_U32, OFFSET_OF(CBS_CONFIG,CbsCmnMemMbistAggrStaticLaneSelU32)},
  //Aggressor Static Lane Select Lower 32 Bits
  {IDSNVID_CMN_MEM_MBIST_AGGR_STATIC_LANE_SEL_L32, OFFSET_OF(CBS_CONFIG,CbsCmnMemMbistAggrStaticLaneSelL32)},
  //Target Static Lane Select Upper 32 bit
  {IDSNVID_CMN_MEM_MBIST_TGT_STATIC_LANE_SEL_U32, OFFSET_OF(CBS_CONFIG,CbsCmnMemMbistTgtStaticLaneSelU32)},
  //Target Static Lane Select Lower 32 Bits
  {IDSNVID_CMN_MEM_MBIST_TGT_STATIC_LANE_SEL_L32, OFFSET_OF(CBS_CONFIG,CbsCmnMemMbistTgtStaticLaneSelL32)},
  //TDP
  {IDSNVID_CMN_TDP_LIMIT, OFFSET_OF(CBS_CONFIG,CbsCmnTDPLimit)},
  //PPT
  {IDSNVID_CMN_PPT_LIMIT, OFFSET_OF(CBS_CONFIG,CbsCmnPPTLimit)},
  //BoostFmax
  {IDSNVID_CMN_BOOST_FMAX, OFFSET_OF(CBS_CONFIG,CbsCmnBoostFmax)},
  //Egress Poison Severity High
  {IDSNVID_PCD_EGRESS_POISON_SEVERITY_HI, OFFSET_OF(CBS_CONFIG,PcdEgressPoisonSeverityHi)},
  //Egress Poison Severity Low
  {IDSNVID_PCD_EGRESS_POISON_SEVERITY_LO, OFFSET_OF(CBS_CONFIG,PcdEgressPoisonSeverityLo)},
  //Egress Poison Mask High
  {IDSNVID_PCD_AMD_NBIO_EGRESS_POISON_MASK_HI, OFFSET_OF(CBS_CONFIG,PcdAmdNbioEgressPoisonMaskHi)},
  //Egress Poison Mask Low
  {IDSNVID_PCD_AMD_NBIO_EGRESS_POISON_MASK_LO, OFFSET_OF(CBS_CONFIG,PcdAmdNbioEgressPoisonMaskLo)},
  //Uncorrected Converted to Poison Enable Mask High
  {IDSNVID_PCD_AMD_NBIO_RAS_UCP_MASK_HI, OFFSET_OF(CBS_CONFIG,PcdAmdNbioRASUcpMaskHi)},
  //Uncorrected Converted to Poison Enable Mask Low
  {IDSNVID_PCD_AMD_NBIO_RAS_UCP_MASK_LO, OFFSET_OF(CBS_CONFIG,PcdAmdNbioRASUcpMaskLo)},
  //System Hub Watchdog Timer
  {IDSNVID_PCD_SYSHUB_WDT_TIMER_INTERVAL, OFFSET_OF(CBS_CONFIG,PcdSyshubWdtTimerInterval)},
  //SPL value in the CPU fuse
  {IDSNVID_CMN_SOC_SPL_FUSE, OFFSET_OF(CBS_CONFIG,CbsCmnSocSplFuse)},
  //SPL value in the SPL table
  {IDSNVID_CMN_SOC_SPL_VALUE_IN_TBL, OFFSET_OF(CBS_CONFIG,CbsCmnSocSplValueInTbl)},
  //End
  {0xFFFF, 0}};

STATIC IDS_NV_RECORD mIdsNv64[] = {
  //End
  {0xFFFF, 0}};


BOOLEAN
PrepareIdsNvTable (
  IN       VOID *CbsVariable,
  IN OUT   VOID *IdsNvTable,
  IN OUT   UINT32 *IdsNvTableSize
  )
{
  IDS_NV_TABLE_HEADER *IdsNvTblHdr;
  UINT8               *IdsNvRecord;
  UINTN               Setup_Config;
  IDS_NV_RECORD       *IdsNv;

  //Check if IdsNvTableSize size satisfied
  if ((*IdsNvTableSize) <  sizeof (IDS_NV_TABLE_HEADER) + GetIdsNvRecordsSize () ) {
    *IdsNvTableSize = sizeof (IDS_NV_TABLE_HEADER) + GetIdsNvRecordsSize ();
    return FALSE;
  }

  Setup_Config = (UINTN) CbsVariable;

  //Fill the IDS_NV_TABLE_HEADER
  IdsNvTblHdr = IdsNvTable;
  IdsNvTblHdr->Signature = IDS_NV_TABLE_SIGNATURE; //$INV
  IdsNvTblHdr->Revision = IDS_NV_TABLE_REV_1; //0x00000001ul

  //Fill the IDS_NV_RECORD
  IdsNvRecord = &(((IDS_NV_TABLE *)IdsNvTable)->NvRecords);

  IdsNv = mIdsNv8;
  while (IdsNv->Id != 0xFFFF) {
    ((IDS_NV_RECORD_U8 *) IdsNvRecord)->Id = IdsNv->Id;
    ((IDS_NV_RECORD_U8 *) IdsNvRecord)->Attrib.size = IDS_NV_ATTRIB_SIZE_BYTE;
    ((IDS_NV_RECORD_U8 *) IdsNvRecord)->Value = *(UINT8*)(Setup_Config+IdsNv->Offset);
    IdsNvRecord += sizeof (IDS_NV_RECORD_U8);
    IdsNv++;
  }

  IdsNv = mIdsNv16;
  while (IdsNv->Id != 0xFFFF) {
    ((IDS_NV_RECORD_U16 *) IdsNvRecord)->Id = IdsNv->Id;
    ((IDS_NV_RECORD_U16 *) IdsNvRecord)->Attrib.size = IDS_NV_ATTRIB_SIZE_WORD;
    ((IDS_NV_RECORD_U16 *) IdsNvRecord)->Value = *(UINT16*)(Setup_Config+IdsNv->Offset);
    IdsNvRecord += sizeof (IDS_NV_RECORD_U16);
    IdsNv++;
  }

  IdsNv = mIdsNv32;
  while (IdsNv->Id != 0xFFFF) {
    ((IDS_NV_RECORD_U32 *) IdsNvRecord)->Id = IdsNv->Id;
    ((IDS_NV_RECORD_U32 *) IdsNvRecord)->Attrib.size = IDS_NV_ATTRIB_SIZE_DWORD;
    ((IDS_NV_RECORD_U32 *) IdsNvRecord)->Value = *(UINT32*)(Setup_Config+IdsNv->Offset);
    IdsNvRecord += sizeof (IDS_NV_RECORD_U32);
    IdsNv++;
  }

  IdsNv = mIdsNv64;
  while (IdsNv->Id != 0xFFFF) {
    ((IDS_NV_RECORD_U64 *) IdsNvRecord)->Id = IdsNv->Id;
    ((IDS_NV_RECORD_U64 *) IdsNvRecord)->Attrib.size = IDS_NV_ATTRIB_SIZE_QWORD;
    ((IDS_NV_RECORD_U64 *) IdsNvRecord)->Value = *(UINT64*)(Setup_Config+IdsNv->Offset);
    IdsNvRecord += sizeof (IDS_NV_RECORD_U64);
    IdsNv++;
  }

  //Fill the end of IDS_NV_RECORD
  ((IDS_NV_RECORD_CMN *) IdsNvRecord)->Id = IDS_NV_ID_END;

  return TRUE;
}

#define IDS_NV_RECORDS_SIZE (3849)

UINT32
GetIdsNvRecordsSize (
  )
{
  return IDS_NV_RECORDS_SIZE;
}