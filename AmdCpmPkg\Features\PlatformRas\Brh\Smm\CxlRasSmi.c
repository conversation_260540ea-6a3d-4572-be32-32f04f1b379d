/*****************************************************************************
 *
 * Copyright (C) 2021-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */


/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Library/BaseMemoryLib.h>
#include <Library/IoLib.h>
#include "AmdPlatformRasBrhSmm.h"
#include <AmdCpmSmm.h>
#include <Protocol/AmdCpmRasOemProtocol.h>
#include <Library/MemoryAllocationLib.h>
#include <Library/IdsLib.h>
#include <Library/NbioHandleLib.h>
#include <Library/MpioInitLib.h>
#include <MpioLib.h>
#include <Library/SmmMemLib.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */
BOOLEAN               gTrigNMI;
BOOLEAN               gTrigSCI;

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
EFI_STATUS
LogCxlComponentEvent(
  IN       CXL_ERROR_LOG_DATA  *CxlErrorLogData
  );

EFI_STATUS
LogCxlProtocolError(
  IN       CXL_ERROR_LOG_DATA  *CxlErrorLogData
  );

EFI_STATUS
CxlErrorScan (
  IN       UINT8  IndexOfRbBusMap
  );

EFI_STATUS
CxlComponentErrorScan (
  IN       UINT8 Seg,
  IN       UINT8 Bus,
  IN       UINT8 MpioIntrPayload
  );

VOID
SmmPrintEventIntrPolicy (
  IN       EVENT_INTR_PAYLOAD    *EventIntrOutputPayload
  );

EFI_STATUS
SmmCxlGetEventIntrPolicy (
  IN       UINT32   RciepAddr,
  IN       UINT8    RcecId
  );

EFI_STATUS
SmmCxlSetEventIntrPolicyToMsi (
  IN       UINT32   RciepAddr,
  IN       UINT8    RcecId
  );

EFI_STATUS
SmmCxlDeviceErrReportOff (
  RCEC_PROFILE            *RcecProfile,
  PCIe_AER_CONFIG_TEMP    *PcieDevAerSetting
  );

EFI_STATUS
EFIAPI
AmdCxlOscCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SW_REGISTER_CONTEXT *DispatchContext,
  IN OUT   FCH_SMM_SW_CONTEXT                *SwContext,
  IN OUT   UINTN                             *SizeOfSwContext
  );

UINT32
GetPciepCxlIsoStatusSmnAddr (
  IN  UINT8     Iom,
  IN  UINT8     PcieCoreNumInNbio
  );

EFI_STATUS
CxlMemErrorIsolation (
  IN       UINT8  IndexOfRbBusMap
  );

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                          T A B L E    D E F I N I T I O N S
 *----------------------------------------------------------------------------------------
 */



EFI_STATUS
EFIAPI
AmdCxlRasSmiCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       FCH_SMM_APURAS_REGISTER_CONTEXT   *RegisterContext
  )
{
  EFI_STATUS            Status;
  UINT8                 IndexOfRbBusMap;
  UINT8                 Nbio;
  UINT8                 Iohc;
  UINT8                 Seg;
  UINT8                 Bus;
  UINT32                BaseAddr;
  UINT32                RasGlobalStatusLoAddr;
  UINT32                RasGlobalStatusHiAddr;
  UINT32                NbioGlobalStatusLo;
  UINT32                NbioGlobalStatusHi;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL SMI error handler entry. Bus: 0x%x\n", RegisterContext->Bus);

  Status = GetIndexByIomFromRbBusMap (
             mRbBusMap,
             RegisterContext->Socket,
             RegisterContext->NbioNumber,
             &IndexOfRbBusMap
           );
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_ERROR,
    "AmdCxlRasSmiCallback - Unable to get information about Socket:0x%02X/IOM: 0x%02X\n",
    RegisterContext->Socket,
    RegisterContext->NbioNumber);
    return Status;
  }

  Nbio = mRbBusMap->RbBusEntry[IndexOfRbBusMap].Nbio;
  Iohc = mRbBusMap->RbBusEntry[IndexOfRbBusMap].Iohc;
  Seg  = mRbBusMap->RbBusEntry[IndexOfRbBusMap].RbSeg;
  Bus  = mRbBusMap->RbBusEntry[IndexOfRbBusMap].RbBusBase;

  BaseAddr = ((Iohc & BIT0) == 0) ? NBIO0_IOHUB0_RAS_GLOBAL_STATUS_LO : NBIO0_IOHUB1_RAS_GLOBAL_STATUS_LO;
  RasGlobalStatusLoAddr = BaseAddr + (IOHC_SMN_ADDR_OFFSET * (Iohc >> 1)) + (NBIO_SMN_ADDR_OFFSET * Nbio);
  //Check NBIO Global Error Status
  RasSmnRead (SMN_SEG_BUS (Seg, Bus),
              RasGlobalStatusLoAddr,
              &NbioGlobalStatusLo);
  BaseAddr = ((Iohc & BIT0) == 0) ? NBIO0_IOHUB0_RAS_GLOBAL_STATUS_HI : NBIO0_IOHUB1_RAS_GLOBAL_STATUS_HI;
  RasGlobalStatusHiAddr = BaseAddr + (IOHC_SMN_ADDR_OFFSET * (Iohc >> 1)) + (NBIO_SMN_ADDR_OFFSET * Nbio);
  RasSmnRead (SMN_SEG_BUS (Seg, Bus),
              RasGlobalStatusHiAddr,
              &NbioGlobalStatusHi);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "CXL Error SMI - NBIO Global Status Lo : 0x%08x\n", NbioGlobalStatusLo);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "CXL Error SMI - NBIO Global Status Hi : 0x%08x\n", NbioGlobalStatusHi);

  //Is Component Error?
  //  Moved to PcieLegacyRasSmiCallback

  if ((mPlatformApeiData->PlatRasPolicy.CxlProtocolErrorReporting == 2 ||
        mPlatformApeiData->PlatRasPolicy.CxlProtocolErrorReporting == 1) &&
        (mPlatformApeiData->PlatRasPolicy.PcieAerReportMechanism == 2 ||
        mPlatformApeiData->PlatRasPolicy.PcieAerReportMechanism == 3)) {
    if ((NbioGlobalStatusHi & RAS_GLOBAL_STATUS_HI_MASK) != 0) {

      //CXL 1.1 - Check CXL.io/CXL.Cache/CXL.mem error
      Status = CxlErrorScan (IndexOfRbBusMap);
    }
  }

  return Status;
}

EFI_STATUS
RasSmmRegisterCxlRasSmi (
  VOID
)
{
//CXL 1.1
  EFI_STATUS                        Status;
  FCH_SMM_APURAS_DISPATCH_PROTOCOL  *AmdApuRasDispatch;
  FCH_SMM_APURAS_REGISTER_CONTEXT   ApuRasRegisterContext;
  EFI_HANDLE                        ApuRasHandle = NULL;
  CXL_MAP                           *CxlMap;
  UINTN                             RcecInx;
  FCH_SMM_SW_DISPATCH2_PROTOCOL     *AmdSwDispatch;
  FCH_SMM_SW_REGISTER_CONTEXT       SwRegisterContext;
  EFI_HANDLE                        SwHandle;

  //
  //  Locate SMM APU RAS dispatch protocol
  //
  Status = gSmst->SmmLocateProtocol (
                  &gFchSmmApuRasDispatchProtocolGuid,
                  NULL,
                  (VOID **)&AmdApuRasDispatch
                  );
  ASSERT_EFI_ERROR (Status);
  if (EFI_ERROR(Status)) {
    return Status;
  }

  CxlMap = mPlatformApeiData->CxlMap;
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS][%a] CxlMap->RcecCount: 0x%04x\n", __FUNCTION__, CxlMap->RcecCount);

  //Register Error handler per Root Complex.
  for (RcecInx = 0; RcecInx < CxlMap->RcecCount; RcecInx++) {
    if ((CxlMap->RcecProfile[RcecInx].DpCnt == 0) &&
        (CxlMap->RcecProfile[RcecInx].RciepCnt == 0)) {
      continue;
    }

    //NBIO Error handle registry.
    ApuRasRegisterContext.Socket = CxlMap->RcecProfile[RcecInx].SocketNum;
    ApuRasRegisterContext.Die = 0;
    ApuRasRegisterContext.Bus = CxlMap->RcecProfile[RcecInx].RbBusNum;
    ApuRasRegisterContext.NbioNumber = CxlMap->RcecProfile[RcecInx].RbIndex;
    ApuRasRegisterContext.Order = 0x80;
    Status = AmdApuRasDispatch->Register (
                              AmdApuRasDispatch,
                              AmdCxlRasSmiCallback,
                              &ApuRasRegisterContext,
                              &ApuRasHandle
                              );
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] Install Cxl Error SMI callback handler : Socket %d, Iohc%d, Bus 0x%x, Rcec 0x%x, Status %r\n",
            ApuRasRegisterContext.Socket,
            ApuRasRegisterContext.NbioNumber,
            ApuRasRegisterContext.Bus,
            CxlMap->RcecProfile[RcecInx].RcecId,
            Status
            );
    if (EFI_ERROR (Status)) {
      return Status;
    }
  }

  //
  //  Locate SMM SW dispatch protocol
  //
  Status = gSmst->SmmLocateProtocol (
                  &gFchSmmSwDispatch2ProtocolGuid,
                  NULL,
                  (VOID **)&AmdSwDispatch
                  );
  if (EFI_ERROR(Status)) {
    return Status;
  }

  //CxlOscSwSmiCmd = 0x8A;
  SwRegisterContext.AmdSwValue  = mPlatformApeiData->RasAcpiSmmData->CxlOscSwSmiCmd;
  SwRegisterContext.Order  = 0x80;
  Status = AmdSwDispatch->Register (
                            AmdSwDispatch,
                            AmdCxlOscCallback,
                            &SwRegisterContext,
                            &SwHandle
                            );
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] Install CXL OSC Software SMI callback handler: %r\n", Status);
  if (EFI_ERROR (Status)) {
    return Status;
  }

  return EFI_SUCCESS;
}


EFI_STATUS
LogDpCxlIoError (
  IN       UINT64 RcrbAddress
)
{
  EFI_STATUS                                  Status = EFI_SUCCESS;
  EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE *PcieErrStatusBlk;
  EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE *TempPcieErrStatusBlk;
  GENERIC_PCIE_AER_ERR_ENTRY_V3               *GenPcieAerErrEntry;
  PCIE_ERROR_SECTION                          *PcieErrorSection;
  EFI_GUID                                    PcieErrSectionType = PCIE_SECT_GUID;
  ERROR_SOURCE_ID_REG                         ErrorSrcId;
  PCI_ADDR                                    RcrbBdf;
  UINT32                                      ErrorSourceId;
  UINT32                                      ErrorSeverity;
  ROOT_ERR_STS_REG                            RootErrSts;
  UINT16                                      AerCapPtr;
  UINT8                                       PcieCapPtr;
  UINTN                                       TempCommBufferSize;
  UINT8                                       TempCommBuffer[MAX_ERROR_BLOCK_SIZE];

  AerCapPtr  = RasFindRcrbExtendedCapability(RcrbAddress, PCIE_EXT_AER_CAP_ID);
  PcieCapPtr = RasFindRcrbPciCapability (RcrbAddress, PCIE_CAP_ID);
  RootErrSts.Value = 0;
  ErrorSrcId.Value = 0;
  if ((AerCapPtr == 0) && (PcieCapPtr == 0)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  PCIE or AER capabilbity not found for DP RCRB\n");
  } else {
    RootErrSts.Value = MmioRead32(RcrbAddress + AerCapPtr + PCIE_ROOT_STATUS_PTR);
    ErrorSrcId.Value = MmioRead32(RcrbAddress + AerCapPtr + PCIE_ERROR_SOURCE_IDENTIFICATION_PTR);
  }

  //end-point device severity will aligned to the root port.
  ErrorSeverity = ERROR_NONE;
  if (RootErrSts.Field.ErrCorReceived) {
    ErrorSeverity = ERROR_SEVERITY_CORRECTED;
    ErrorSourceId = (ErrorSrcId.Field.CorSourceIdentification << 12);
  }
  if (RootErrSts.Field.NonFatalErrMesgReceived) {
    ErrorSeverity = ERROR_RECOVERABLE;
    ErrorSourceId = (ErrorSrcId.Field.FatalSourceIdentification << 12);
  }
  if (RootErrSts.Field.FatalErrMesgReceived) {
    ErrorSeverity = ERROR_SEVERITY_FATAL;
    ErrorSourceId = (ErrorSrcId.Field.FatalSourceIdentification << 12);
  }

  switch (ErrorSeverity) {
  case ERROR_SEVERITY_FATAL:
    //Find uncorrectable error block
    PcieErrStatusBlk = mPlatformApeiData->AmdPcieAerUnErrBlk;
    break;
  case ERROR_RECOVERABLE:
  case ERROR_SEVERITY_CORRECTED:
    PcieErrStatusBlk = mPlatformApeiData->AmdPcieAerErrBlk;
    break;
  default:
    return EFI_UNSUPPORTED;
  }

  TempCommBufferSize = sizeof (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE) + PcieErrStatusBlk->DataLength + sizeof (GENERIC_PCIE_AER_ERR_ENTRY_V3);
  if (TempCommBufferSize > MAX_ERROR_BLOCK_SIZE) {
    return EFI_OUT_OF_RESOURCES;
  }

  if (!SmmIsBufferOutsideSmmValid ((UINTN) PcieErrStatusBlk, TempCommBufferSize)) {
    DEBUG ((EFI_D_ERROR, "PcieErrStatusBlk is in SMRAM or overlapped with SMRAM!\n"));
    return EFI_INVALID_PARAMETER;
  }

  ZeroMem ((VOID *) TempCommBuffer, MAX_ERROR_BLOCK_SIZE);
  CopyMem ((VOID *) TempCommBuffer, (VOID *) PcieErrStatusBlk, TempCommBufferSize);
  TempPcieErrStatusBlk = (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE *) TempCommBuffer;

  RasReinitErrBlkSts(TempPcieErrStatusBlk);

  //Set BlockStatus
  if (TempPcieErrStatusBlk->BlockStatus.UncorrectableErrorValid) {
    TempPcieErrStatusBlk->BlockStatus.MultipleUncorrectableErrors = 1;
  } else {
    TempPcieErrStatusBlk->BlockStatus.UncorrectableErrorValid = 1;
  }

  GenPcieAerErrEntry = (GENERIC_PCIE_AER_ERR_ENTRY_V3 *) ((UINTN ) TempPcieErrStatusBlk + sizeof (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE) + TempPcieErrStatusBlk->DataLength);
  CopyGuid ((EFI_GUID*)&GenPcieAerErrEntry->GenErrorDataEntry.SectionType, &PcieErrSectionType);
  GenPcieAerErrEntry->GenErrorDataEntry.Revision = GENERIC_ERROR_REVISION;
  GenPcieAerErrEntry->GenErrorDataEntry.Flags = 0x01;
  GenPcieAerErrEntry->GenErrorDataEntry.ErrorDataLength = sizeof (PCIE_ERROR_SECTION);

  GenPcieAerErrEntry->GenErrorDataEntry.ValidationBits = FRU_STRING_VALID;
  AsciiStrCpyS ((CHAR8 *)GenPcieAerErrEntry->GenErrorDataEntry.FruText, FRU_TEXT_MAX_LENGTH, "PcieError (CXL DP)");

  GenPcieAerErrEntry->GenErrorDataEntry.ErrorSeverity = ErrorSeverity;

  //Fill PCIe Error Data Section
  RcrbBdf.AddressValue = ErrorSourceId;
  PcieErrorSection = &GenPcieAerErrEntry->PcieAerErrorSection;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]RCRB Address : 0x%016lX @ S:0x%x B:0x%x D:0x%x F:0x%x\n",
          RcrbAddress,
          RcrbBdf.Address.Segment,
          RcrbBdf.Address.Bus,
          RcrbBdf.Address.Device,
          RcrbBdf.Address.Function
          );

  FillRcrbPcieErrorSection (RcrbAddress,
                            RcrbBdf,
                            ErrorSeverity,
                            PcieErrorSection
                            );

  TempPcieErrStatusBlk->BlockStatus.ErrorDataEntryCount++;
  TempPcieErrStatusBlk->DataLength += sizeof (GENERIC_PCIE_AER_ERR_ENTRY_V3);

  UpdateGenErrStsBlkSeverity(TempPcieErrStatusBlk, ErrorSeverity);

  switch (ErrorSeverity) {
  case ERROR_SEVERITY_FATAL:
    if (mPlatformApeiData->PlatRasPolicy.PcieUnCorrGhesNotifyType) {
      gTrigNMI = TRUE;
    }
    break;
  case ERROR_SEVERITY_CORRECTED:
    if (mPlatformApeiData->PlatRasPolicy.PcieGhesNotifyType) {
      gTrigSCI = TRUE;
    }
    break;
  }

  CopyMem ((VOID *) PcieErrStatusBlk, (VOID *) TempCommBuffer, TempCommBufferSize);

  return Status;
}


EFI_STATUS
LogCxlProtocolError(
  IN       CXL_ERROR_LOG_DATA  *CxlErrorLogData
)
{
  EFI_STATUS                                  Status = EFI_SUCCESS;
  EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE *CxlErrStatusBlk;
  EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE *TempCxlErrStatusBlk;
  GENERIC_CXL_ERR_ENTRY_V3                    *GenCxlErrEntry;
  ROOT_ERR_STS_REG                            RootErrSts;
  UINT32                                      ErrorSeverity;
  UINT32                                      CxlErrLen = 0;
  UINTN                                       HandleBufferSize = 0;
  UINTN                                       Index = 0;
  UINT8                                       FreeFlag = 0;
  EFI_HANDLE                                  *HandleBuffer = NULL;
  AMD_CPM_RAS_OEM_PROTOCOL                    *AmdRasOemProtocol = NULL;
  UINTN                                       TempCommBufferSize;
  UINT8                                       TempCommBuffer[MAX_ERROR_BLOCK_SIZE];

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a Start\n", __FUNCTION__);
  if (CxlErrorLogData->RcecAddress != 0) {  //RcecAddress is 0 in CXL 2.0
    //CXL 1.1 RootErrSts on RCEC. Get RCEC Root Error Status Register
    RootErrSts.Value = RasPcieRootErrorStatus (CxlErrorLogData->RcecAddress);
  } else {
    //CXL 2.0 RootErrSts on CXL Root Port
    RootErrSts.Value = CxlErrorLogData->Cxl2p0RPPcieRootStatus;
  }
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  RCEC 0x%08x, AER Root Error Status: 0x%08x\n", CxlErrorLogData->RcecAddress, RootErrSts.Value);

  //CXL device severity should align to the RCEC.
  ErrorSeverity = ERROR_NONE;
  if (RootErrSts.Field.ErrCorReceived) {
    ErrorSeverity = ERROR_SEVERITY_CORRECTED;
  }
  if (RootErrSts.Field.NonFatalErrMesgReceived) {
    ErrorSeverity = ERROR_RECOVERABLE;
  }
  if (RootErrSts.Field.FatalErrMesgReceived) {
    ErrorSeverity = ERROR_SEVERITY_FATAL;
  }

  switch (ErrorSeverity) {
  case ERROR_SEVERITY_FATAL:
    //Find uncorrectable error block
    CxlErrStatusBlk = mPlatformApeiData->AmdCxlUnCorrErrBlk;

    break;
  case ERROR_RECOVERABLE:
  case ERROR_SEVERITY_CORRECTED:
    //Find correctable error block
    CxlErrStatusBlk = mPlatformApeiData->AmdCxlCorrErrBlk;

    break;
  default:
    return EFI_UNSUPPORTED;
  }

  switch (CxlErrorLogData->CxlAgentType) {
  case CXL_AGENT_TYPE_DEVICE:
    CxlErrLen = sizeof (GENERIC_CXL_ERR_ENTRY_V3) + sizeof (PCIE_CXL_DEVICE_DVSEC) + sizeof (CXL_1_1_RAS_CAPABILITY_STRUCTURE);
    break;
  case CXL_AGENT_TYPE_DOWNSTREAM:
    CxlErrLen = sizeof (GENERIC_CXL_ERR_ENTRY_V3) + sizeof (FLEX_BUS_PORT_DVSEC) + sizeof (CXL_1_1_RAS_CAPABILITY_STRUCTURE);
    break;
  case CXL_AGENT_TYPE_ROOT_PORT:
    CxlErrLen = sizeof (GENERIC_CXL_ERR_ENTRY_V3) + sizeof (FLEX_BUS_PORT_DVSEC) + sizeof (CXL_1_1_RAS_CAPABILITY_STRUCTURE);
    break;
  default:
    CxlErrLen = sizeof(GENERIC_CXL_ERR_ENTRY_V3);
    break;
  }

  TempCommBufferSize = sizeof (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE) + CxlErrStatusBlk->DataLength + CxlErrLen;
  if (TempCommBufferSize > MAX_ERROR_BLOCK_SIZE) {
    //Clear CXL RAS capability registers
    if (CxlErrorLogData->CxlComponentRegAddr != 0) {
      CxlRasCapStsClear (CxlErrorLogData->CxlComponentRegAddr, mPlatformApeiData->PlatRasPolicy.RasRetryCnt);
    }
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] Exceeds MAX_ERROR_BLOCK_SIZE Error.\n");
    return EFI_OUT_OF_RESOURCES;
  }

  if (!SmmIsBufferOutsideSmmValid ((UINTN) CxlErrStatusBlk, TempCommBufferSize)) {
    DEBUG ((EFI_D_ERROR, "CxlErrStatusBlk is in SMRAM or overlapped with SMRAM!\n"));
    return EFI_INVALID_PARAMETER;
  }

  ZeroMem ((VOID *) TempCommBuffer, MAX_ERROR_BLOCK_SIZE);
  CopyMem ((VOID *) TempCommBuffer, (VOID *) CxlErrStatusBlk, TempCommBufferSize);
  TempCxlErrStatusBlk = (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE *) TempCommBuffer;

  RasReinitErrBlkSts(TempCxlErrStatusBlk);

  //Set BlockStatus
  if (TempCxlErrStatusBlk->BlockStatus.UncorrectableErrorValid) {
    TempCxlErrStatusBlk->BlockStatus.MultipleUncorrectableErrors = 1;
  } else {
    TempCxlErrStatusBlk->BlockStatus.UncorrectableErrorValid = 1;
  }


  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]Log Agent Address : 0x%016lu @ Block Address: 0x%08x\n", CxlErrorLogData->CxlAgentAddress, (UINTN)CxlErrStatusBlk);

  GenCxlErrEntry = (GENERIC_CXL_ERR_ENTRY_V3*) ((UINTN) TempCxlErrStatusBlk + TempCxlErrStatusBlk->DataLength + sizeof (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE));

  Status = LogCxlProtocolErrorHelper (
              CxlErrorLogData,
              GenCxlErrEntry,
              &CxlErrLen
              );

  GenCxlErrEntry->GenErrorDataEntry.ErrorSeverity = ErrorSeverity;

  TempCxlErrStatusBlk->BlockStatus.ErrorDataEntryCount++;
  TempCxlErrStatusBlk->DataLength += CxlErrLen;

  UpdateGenErrStsBlkSeverity(TempCxlErrStatusBlk, ErrorSeverity);
  //Locate Ras Oem Protocol
  Status = gSmst->SmmLocateHandle (
                              ByProtocol,
                              &gAmdCpmRasOemSmmProtocolGuid,
                              NULL,
                              &HandleBufferSize,
                              HandleBuffer);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "LogCxlProtocolError SmmLocateHandle gAmdCpmRasOemSmmProtocolGuid Status =%r\n", Status);

  if (EFI_ERROR(Status)) {
      if (Status == EFI_BUFFER_TOO_SMALL) {
          HandleBuffer = AllocateRuntimePool (HandleBufferSize);
          if (HandleBuffer != NULL) {
            Status = gSmst->SmmLocateHandle (
                        ByProtocol,
                        &gAmdCpmRasOemSmmProtocolGuid,
                        NULL,
                        &HandleBufferSize,
                        HandleBuffer);
            if (!EFI_ERROR(Status))
              FreeFlag = 1;
          }
      }
  }

  if (!EFI_ERROR(Status)) {
    if (HandleBuffer != NULL) {
      for (Index = 0; Index < (HandleBufferSize / sizeof(EFI_HANDLE)); Index++) {
          Status = gSmst->SmmHandleProtocol (
                                    HandleBuffer[Index],
                                    &gAmdCpmRasOemSmmProtocolGuid,
                                    (VOID **)&AmdRasOemProtocol);
        if(!EFI_ERROR(Status)){
          AmdRasOemProtocol->OemErrorLogEventCxlProtocol (CxlErrorLogData, GenCxlErrEntry);
        }
      }
    }
  }

  if ((FreeFlag == 1) && (HandleBuffer != NULL)){
    Status = gSmst->SmmFreePool (HandleBuffer);
    ASSERT_EFI_ERROR (Status);
  }

  switch (ErrorSeverity) {
  case ERROR_SEVERITY_FATAL:
    if (mPlatformApeiData->PlatRasPolicy.PcieUnCorrGhesNotifyType) {
      gTrigNMI = TRUE;
    }
    break;
  case ERROR_RECOVERABLE:
  case ERROR_SEVERITY_CORRECTED:
    if (mPlatformApeiData->PlatRasPolicy.PcieGhesNotifyType) {
      gTrigSCI = TRUE;
    }
    break;
  }

  CopyMem ((VOID *) CxlErrStatusBlk, (VOID *) TempCommBuffer, TempCommBufferSize);

  return Status;
}


VOID
PrintBuffer(
  UINT8  *DataBuffer,
  UINT32 Length
)
{
  UINT32  index;
  UINT8   *BufferPtr;

  BufferPtr = DataBuffer;
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Data Buffer dump, Address: 0x%08x, Length: 0x%x\n", DataBuffer, Length);
  for (index = 0; index < Length; index++) {
    if ((index != 0) && ((index % 16) == 0)) {
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "\n");
    }
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "0x%02x ", *BufferPtr);
    BufferPtr++;
  }
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "\n");
}


EFI_STATUS
LogCxlComponentEvent(
  IN       CXL_ERROR_LOG_DATA  *CxlErrorLogData
)
{
  EFI_STATUS                        Status = EFI_SUCCESS;
  EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE *CxlEventStatusBlk;
  EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE *TempCxlEventStatusBlk;
  GENERIC_CXL_EVENT_ENTRY_V3        *GenCxlEventEntry;
  GET_EVENT_RECORD_OUTPUT_PAYLOAD   *GetEventRecordOutPutPayload;
  UINT32                            CxlEventLen = 0;
  CXL_DEVICE_EVENT_STATUS           CxlDeviceEventStatus;
  UINT64                            CxlMemDeviceRegAddrBase;
  UINT32                            MailboxRegOffset;
  UINT32                            MailboxRegLength;
  UINT32                            OutPlLength;
  MB_CMD_STATUS                     MbCmdStatus;
  UINT32                            ErrorSeverity;
  UINT8                             EventLogStatusType;
  UINT8                             EventLogType;
  BOOLEAN                           ClearEventRecord;
  BOOLEAN                           EventRecordFound;
  UINT32                            InPlLength;
  CLEAR_EVENT_RECORD_INPUT_PAYLOAD  *ClrEventRecordInputPl;
  COMMON_EVENT_RECORD               *EventRecordPt;
  VOID                              *pt;
  EVENT_RECORD_FLAGS1               EventRecordFlag1;
  EVENT_RECORD_HEADER               *EventRecordHeader;
  FW_INTR_VECTOR                    FwIntrVector;
  UINTN                             HandleBufferSize = 0;
  UINTN                             Index = 0;
  UINT8                             FreeFlag = 0;
  EFI_HANDLE                        *HandleBuffer = NULL;
  AMD_CPM_RAS_OEM_PROTOCOL          *AmdRasOemProtocol = NULL;
  UINT16                            RecordIndex;
  BOOLEAN                           ClearAllEvent;
  UINT8                             MoreEventRecords;
  UINTN                             TempCommBufferSize;
  UINT8                             TempCommBuffer[MAX_ERROR_BLOCK_SIZE];

  CxlMemDeviceRegAddrBase = CxlErrorLogData->CxlMemDeviceRegAddr;

  // Check CXL component event
  Status = CxlGetMemoryDeviceStatus (CxlMemDeviceRegAddrBase, &CxlDeviceEventStatus);
  if (Status != EFI_SUCCESS) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] Component Event not support!!!\n");
    return Status;
  }

  EventRecordFound = FALSE;
  FwIntrVector.Value = CxlErrorLogData->MpioIntrPayload;
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] CXL Device Status: 0x%08x, Event Log ID: 0x%x, CXL RbIndex/RCEC: 0x%x\n",
         CxlDeviceEventStatus,
         FwIntrVector.Field.EventLogId,
         FwIntrVector.Field.RcecId);

  if ((CxlDeviceEventStatus.Value & EVENT_STATUS_MASK) == 0) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] No valid CXL component events found!!!\n");
    return EFI_NOT_FOUND;
  }

  // Get mailbox registers address offset and length
  MailboxRegOffset = CxlGetMailboxRegAddrByCmpErrRptMode (
                       mPlatformApeiData->PlatRasPolicy.CxlComponentErrorReporting,
                       CxlMemDeviceRegAddrBase,
                       &MailboxRegLength
                     );
  if (MailboxRegOffset == 0) {
    //No Mailbox Register found
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] Unable to get the mailbox address of the CXL device!!!\n");
    return EFI_UNSUPPORTED;
  }

  for (EventLogStatusType = 0; EventLogStatusType < EVENT_STATUS_NUMBER_OF_EVENT_TYPES; EventLogStatusType++) {
    EventLogType = EventLogStatusType;
    // Check status and interrupt vector from MPIO.
    if ((CxlDeviceEventStatus.Value & (1 << EventLogStatusType)) == 0) {
      continue;
    }

    // Get Event Record
    ClearEventRecord = FALSE;
    ClearAllEvent = FALSE;
    do {
      MbCmdStatus = MbCmdGetEventRecord (CxlMemDeviceRegAddrBase + MailboxRegOffset,
                                         EventLogType,
                                         &OutPlLength,
                                         (VOID**)&GetEventRecordOutPutPayload
                                         );
      if ((MbCmdStatus != Mb_Cmd_Success) || (0 == OutPlLength)) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] CXL Mailbox command failed! EventLogType: 0x%x\n",
          EventLogType);
        break;
      }

      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
        "[RAS] OutPlLength: 0x%x, sizeof (GET_EVENT_RECORD_OUTPUT_PAYLOAD): 0x%x, EventRecordCount: 0x%04X\n",
        OutPlLength,
        sizeof (GET_EVENT_RECORD_OUTPUT_PAYLOAD),
        GetEventRecordOutPutPayload->EventRecordCount
      );

      //PrintBuffer((UINT8*)GetEventRecordOutPutPayload, OutPlLength);

      //Make sure all event record extract from the device.
      MoreEventRecords = GetEventRecordOutPutPayload->Flags.Field.MoreEventRecords;
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] MoreEventRecords: 0x%x\n",
        MoreEventRecords);

      if (GetEventRecordOutPutPayload->EventRecordCount == 0) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] EventRecordCount: 0, skip processing this OutPutPayload.\n");
        if (OutPlLength != 0) {
          FreePages (GetEventRecordOutPutPayload, EFI_SIZE_TO_PAGES (OutPlLength));
        }
        continue;
      }
      ClearEventRecord = TRUE;

      pt = (void *) &GetEventRecordOutPutPayload->EventRecords[0];
      EventRecordPt = pt;
      for (RecordIndex = 0; RecordIndex < GetEventRecordOutPutPayload->EventRecordCount; RecordIndex++) {
        CxlEventLen = (sizeof(GENERIC_CXL_EVENT_ENTRY_V3) + (sizeof (COMMON_EVENT_RECORD) - sizeof (EFI_GUID)));

        EventRecordHeader = (EVENT_RECORD_HEADER*)EventRecordPt;

        EventRecordFlag1.Value = EventRecordHeader->EventRecordFlags[0]; //Get first byte from Event Record Flag

        EventRecordPt++; //prepare for the next loop

        CxlDeviceEventLogSeverityConvertion (
                               EventRecordFlag1.Field.EventRecordSeverity,
                               &ErrorSeverity
                              );

        switch (ErrorSeverity) {
        case ERROR_SEVERITY_FATAL:
          //Find uncorrectable error block
          CxlEventStatusBlk = mPlatformApeiData->AmdCxlEventUnCorrBlk;

          break;
        case ERROR_NONE:
        case ERROR_RECOVERABLE:
        case ERROR_SEVERITY_CORRECTED:
          //Find correctable error block
          CxlEventStatusBlk = mPlatformApeiData->AmdCxlEventCorrBlk;

          break;
        default:
          continue;
        }

        TempCommBufferSize = sizeof (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE) + CxlEventStatusBlk->DataLength + CxlEventLen;
        if (TempCommBufferSize > MAX_ERROR_BLOCK_SIZE) {
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "      Event Log size overflow!!!\n");
          continue;
        }

        if (!SmmIsBufferOutsideSmmValid ((UINTN) CxlEventStatusBlk, TempCommBufferSize)) {
          DEBUG ((EFI_D_ERROR, "CxlEventStatusBlk is in SMRAM or overlapped with SMRAM!\n"));
          return EFI_INVALID_PARAMETER;
        }

        ZeroMem ((VOID *) TempCommBuffer, MAX_ERROR_BLOCK_SIZE);
        CopyMem ((VOID *) TempCommBuffer, (VOID *) CxlEventStatusBlk, TempCommBufferSize);
        TempCxlEventStatusBlk = (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE *) TempCommBuffer;


        if (!EventRecordFound) {
          //This is the first loop, reinitializing the ERROR BLOCK buffer.
          RasReinitErrBlkSts(TempCxlEventStatusBlk);
        }

        //Set BlockStatus
        if (TempCxlEventStatusBlk->BlockStatus.UncorrectableErrorValid) {
          TempCxlEventStatusBlk->BlockStatus.MultipleUncorrectableErrors = 1;
        } else {
          TempCxlEventStatusBlk->BlockStatus.UncorrectableErrorValid = 1;
        }

        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS]Log Agent Address : 0x%lu @ Block Address: 0x%08x\n",
          CxlErrorLogData->CxlAgentAddress,
          (UINTN)CxlEventStatusBlk);

        GenCxlEventEntry = (GENERIC_CXL_EVENT_ENTRY_V3*) ((UINTN) TempCxlEventStatusBlk + TempCxlEventStatusBlk->DataLength + sizeof (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE));

        Status = LogCxlEventRecordHelper (
                    CxlErrorLogData,
                    EventRecordHeader,
                    GenCxlEventEntry,
                    &CxlEventLen
                    );
        if(EFI_ERROR (Status)) {
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] CXL Event Logging failed. EventLogType: 0x%x\n", EventLogType);
          continue;
        }
        GenCxlEventEntry->GenErrorDataEntry.ErrorSeverity = ErrorSeverity;

        TempCxlEventStatusBlk->BlockStatus.ErrorDataEntryCount++;
        TempCxlEventStatusBlk->DataLength += CxlEventLen;

        UpdateGenErrStsBlkSeverity(TempCxlEventStatusBlk, ErrorSeverity);

        //Locate Ras Oem Protocol
        FreeFlag = 0;
        HandleBufferSize = 0;
        HandleBuffer = NULL;
        Status = gSmst->SmmLocateHandle (
                                    ByProtocol,
                                    &gAmdCpmRasOemSmmProtocolGuid,
                                    NULL,
                                    &HandleBufferSize,
                                    HandleBuffer);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "LogCxlComponentEvent SmmLocateHandle gAmdCpmRasOemSmmProtocolGuid Status =%r\n", Status);

        if (EFI_ERROR(Status)) {
            if (Status == EFI_BUFFER_TOO_SMALL) {
                HandleBuffer = AllocateRuntimePool (HandleBufferSize);
                if (HandleBuffer != NULL) {
                  Status = gSmst->SmmLocateHandle (
                              ByProtocol,
                              &gAmdCpmRasOemSmmProtocolGuid,
                              NULL,
                              &HandleBufferSize,
                              HandleBuffer);
                  if (!EFI_ERROR(Status))
                    FreeFlag = 1;
                }
            }
        }

        if (!EFI_ERROR(Status)) {
          if (HandleBuffer != NULL) {
            for (Index = 0; Index < (HandleBufferSize / sizeof(EFI_HANDLE)); Index++) {
                Status = gSmst->SmmHandleProtocol (
                                          HandleBuffer[Index],
                                          &gAmdCpmRasOemSmmProtocolGuid,
                                          (VOID **)&AmdRasOemProtocol);
              if(!EFI_ERROR(Status)){
                AmdRasOemProtocol->OemErrorLogEventCxlComponent (CxlErrorLogData, GenCxlEventEntry);
              }
            }
          }
        }
        if ((FreeFlag == 1) && (HandleBuffer != NULL)){
          Status = gSmst->SmmFreePool (HandleBuffer);
          ASSERT_EFI_ERROR (Status);
        }

        switch (ErrorSeverity) {
        case ERROR_SEVERITY_FATAL:
          if (mPlatformApeiData->PlatRasPolicy.PcieUnCorrGhesNotifyType) {
            gTrigNMI = TRUE;
          }
          break;
        case ERROR_NONE:
        case ERROR_RECOVERABLE:
        case ERROR_SEVERITY_CORRECTED:
          if (mPlatformApeiData->PlatRasPolicy.PcieGhesNotifyType) {
            gTrigSCI = TRUE;
          }
          break;
        }

        CopyMem ((VOID *) CxlEventStatusBlk, (VOID *) TempCommBuffer, TempCommBufferSize);

        EventRecordFound = TRUE;
      }

      // Clear current event record
      if (ClearEventRecord) {
        InPlLength = (sizeof (CLEAR_EVENT_RECORD_INPUT_PAYLOAD) - sizeof (UINT16)) +
                     (sizeof (UINT16) * GetEventRecordOutPutPayload->EventRecordCount); //For Clear Event Record Input payload
        ClrEventRecordInputPl = AllocateRuntimeZeroPool (InPlLength);
        if (ClrEventRecordInputPl == NULL) {
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] Unable to allocate buffer for clear current event record input payload!!!\n");
        } else {
          pt = (void *) &GetEventRecordOutPutPayload->EventRecords[0];
          EventRecordPt = pt;
          for (Index = 0; Index < GetEventRecordOutPutPayload->EventRecordCount; Index++) {
            ClrEventRecordInputPl->EventRecordHandles[Index] = EventRecordPt->EventRecordHeader.EventRecordHandle;
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] EventRecordHandles[%x]=0x%x\n", Index, ClrEventRecordInputPl->EventRecordHandles[Index]);
            EventRecordPt++;
          }
          ClrEventRecordInputPl->EventLog = EventLogType;
          ClrEventRecordInputPl->ClearEventFlags = 0; //If 0, the device shall clear the event records specified in the Event Record Handles list.
          ClrEventRecordInputPl->NumberOfEventRecordHandles = (UINT8)GetEventRecordOutPutPayload->EventRecordCount; //The number of event records in the Event Records list.

          if (GetEventRecordOutPutPayload->Flags.Field.Overflow & 0x01) { //This bit shall be set by the device when errors occur that the device cannot log without overwriting an existing log event.
            //Clear All Events is only allowed when the Event Log has overflowed; otherwise, the device shall return Invalid Input.
            ClearAllEvent = TRUE;
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] ClearAllEvent flag is set\n");
          }
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ClrEventRecordInputPl Input Pauload dump, Address: 0x%08x, InPlLength: 0x%x\n", ClrEventRecordInputPl, InPlLength);
          PrintBuffer((UINT8*)ClrEventRecordInputPl, InPlLength);
          MbCmdClearEventRecord (CxlMemDeviceRegAddrBase + MailboxRegOffset,
                                 InPlLength,
                                 (UINT8*)ClrEventRecordInputPl
                                 );
          FreePool (ClrEventRecordInputPl);
        }
      }  // if (ClearEventRecord)

      if (OutPlLength != 0) {
        FreePages (GetEventRecordOutPutPayload, EFI_SIZE_TO_PAGES (OutPlLength));
      }
    } while (MoreEventRecords);

    //
    // Check if there are any remaining records of this EventLogType that cannot be retrieved
    //
    if (ClearAllEvent) {
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
        "[RAS] Clear all remaining records of this EventLogType that cannot be retrieved\n"
      );

      // Clear all device Event record
      InPlLength = sizeof (CLEAR_EVENT_RECORD_INPUT_PAYLOAD); //For Clear Event Record Input payload
      ClrEventRecordInputPl = AllocateRuntimeZeroPool (InPlLength);
      if (ClrEventRecordInputPl == NULL) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] Unable to allocate buffer for clear all event record input payload!!!\n");
      } else {
        ClrEventRecordInputPl->EventLog = EventLogType;
        ClrEventRecordInputPl->ClearEventFlags = (UINT8)CLEAR_EVENT_FLAG_CLEAR_ALL_EVENT;
        ClrEventRecordInputPl->NumberOfEventRecordHandles = 0; //If Clear All Events is set, this shall be 0.
        ClrEventRecordInputPl->EventRecordHandles[0] = 0;        //Cleat all event command should not ask for handle. just in case.
        MbCmdClearEventRecord (CxlMemDeviceRegAddrBase + MailboxRegOffset,
                               InPlLength,
                               (UINT8*)&ClrEventRecordInputPl
                               );
        FreePool (ClrEventRecordInputPl);
      }
    }  // if (ClearAllEvent)
  }

  return Status;
}

/*
  CxlErrorLog
    The value returned is used only to indicate whether PCIe AER errors were found and logged.
*/
EFI_STATUS
CxlErrorLog (
  IN       PCI_ADDR             Device,
  IN OUT   RAS_ERR_LOG_DATA    *ErrLogData
  )
{
  EFI_STATUS          PciErrorLogStatus;
  CXL_ERROR_LOG_DATA  *CxlErrorLogData;
  PCIE_ERR_ENTRY      PcieErrorEntry;
  CXL_AGENT_ADDRESS   CxlAgentAddress;
  BOOLEAN             IsEdrDpcError;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a Start\n", __FUNCTION__);

  CxlErrorLogData = (CXL_ERROR_LOG_DATA*)ErrLogData->Buffer;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL Error Log Type: 0x%x\n", CxlErrorLogData->ErrorLogType);

  //IO error
  PciErrorLogStatus = EFI_NOT_FOUND;
  if (CxlErrorLogData->ErrorLogType & CXL_IO_ERROR) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL Error Log Type: IO Error\n");
    CxlAgentAddress.AgentAddress = CxlErrorLogData->CxlAgentAddress;
    if (CxlErrorLogData->CxlAgentType == CXL_AGENT_TYPE_DEVICE) {
      // RCiEP io error.
      PcieErrorEntry.DevAddr = (CxlCxlAgentAddrToPciAddr(&CxlAgentAddress)).AddressValue;
      PcieErrorEntry.EntryValid = TRUE;
      if (CxlErrorLogData->RcecAddress != 0) {  //RcecAddress is 0 in CXL 2.0
        //CXL 1.1 RootErrSts on RCEC
        PcieErrorEntry.RootErrSts = RasPcieRootErrorStatus (CxlErrorLogData->RcecAddress);
      } else {
        //CXL 2.0 RootErrSts on CXL Root Port
        PcieErrorEntry.RootErrSts = CxlErrorLogData->Cxl2p0RPPcieRootStatus;
      }
      PcieErrorEntry.DevType = PCieDeviceRCiEP;

      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  CXL Device IO Error at Address: 0x%08X, Root Error Status: 0x%08X\n",
        PcieErrorEntry.DevAddr, PcieErrorEntry.RootErrSts);

      IsEdrDpcError = FALSE;
      PciErrorLogStatus = PcieErrorLog (&PcieErrorEntry, &gTrigNMI, &gTrigSCI, &IsEdrDpcError);
    } else if (CxlErrorLogData->CxlAgentType == CXL_AGENT_TYPE_ROOT_PORT) {
      // CXL 2.0 root port io error.
      PcieErrorEntry.DevAddr = (CxlCxlAgentAddrToPciAddr(&CxlAgentAddress)).AddressValue;
      PcieErrorEntry.EntryValid = TRUE;
      PcieErrorEntry.RootErrSts = CxlErrorLogData->Cxl2p0RPPcieRootStatus;
      PcieErrorEntry.DevType = PcieDeviceRootComplex;

      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  CXL 2.0 Root Port IO Error at Address: 0x%08X, Root Error Status: 0x%08X\n",
        PcieErrorEntry.DevAddr, PcieErrorEntry.RootErrSts);

      IsEdrDpcError = FALSE;
      PciErrorLogStatus = PcieErrorLog (&PcieErrorEntry, &gTrigNMI, &gTrigSCI, &IsEdrDpcError);
    } else {
      //RCRB AER error report
      LogDpCxlIoError (CxlAgentAddress.AgentAddress);
    }
  }

  //cache and mem error
  if (CxlErrorLogData->ErrorLogType & CXL_CACHE_MEM_ERROR) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL Error Log Type: Protocol Error\n");
    LogCxlProtocolError(CxlErrorLogData);
  }

  //Compoenent Event
  if (CxlErrorLogData->ErrorLogType & CXL_COMPONENT_EVENT) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL Error Log Type: Component Error\n");
    LogCxlComponentEvent(CxlErrorLogData);
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a Exit. PciErrorLog Status: %r\n", __FUNCTION__, PciErrorLogStatus);

  return PciErrorLogStatus;
}


EFI_STATUS
CxlErrorScan (
  IN       UINT8  IndexOfRbBusMap
)
{
  EFI_STATUS Status = EFI_SUCCESS;
  UINT8               Seg;
  UINT8               Bus;
  UINT16              RcecIndex;
  RCEC_PROFILE        *RcecProfileInstance;
  RAS_ERR_LOG_DATA    CxlErrLogData;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a Start\n", __FUNCTION__);

  Seg  = mRbBusMap->RbBusEntry[IndexOfRbBusMap].RbSeg;
  Bus  = mRbBusMap->RbBusEntry[IndexOfRbBusMap].RbBusBase;

  CxlErrLogData.Buffer = NULL;
  CxlErrLogData.RasLogCallback = CxlErrorLog;

  RcecProfileInstance = mPlatformApeiData->CxlMap->RcecProfile;

  gTrigNMI = FALSE;
  gTrigSCI = FALSE;

  // Search RCEC for CXL errors
  for (RcecIndex = 0; RcecIndex < mPlatformApeiData->CxlMap->RcecCount; RcecIndex++, RcecProfileInstance++) {
    if ((RcecProfileInstance->RbSegNum != Seg) ||
        (RcecProfileInstance->RbBusNum != Bus)) {
      continue;
    }

    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR,
      "  CXL Error search at Seg: 0x%x, Bus: 0x%x, RCEC Id: %0d, DP count: %d, Device count: %d\n",
      RcecProfileInstance->RbSegNum,
      RcecProfileInstance->RbBusNum,
      RcecProfileInstance->RcecId,
      RcecProfileInstance->DpCnt,
      RcecProfileInstance->RciepCnt
    );

    // Check CXL downstream port
    CxlDpErrStsCheck(RcecProfileInstance, &CxlErrLogData);

    // Check CXL device
    CxlDevErrStsCheck(RcecProfileInstance,
                      &CxlErrLogData,
                      CXL_MPIO_INTR_PL_INVALID,
                      CXL_DEV_CHK_IO_CACHE_MEM_ERROR_ONLY
                      );

    //Clear RCEC AER status register
  }

  if (gTrigNMI) {
    //Trigger NMI
    RasTriggerNMI();
  }
  if (gTrigSCI) {
    //Trigger SCI
    RasTriggerSci();
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a End\n", __FUNCTION__);

  return Status;
}


EFI_STATUS
CxlComponentErrorScan (
  IN       UINT8 Seg,
  IN       UINT8 Bus,
  IN       UINT8 MpioIntrPayload
)
{
  EFI_STATUS Status = EFI_SUCCESS;
  UINT16              RcecIndex;
  RCEC_PROFILE        *RcecProfileInstance;
  RAS_ERR_LOG_DATA    CxlErrLogData;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a Start\n", __FUNCTION__);

  CxlErrLogData.Buffer = NULL;
  CxlErrLogData.RasLogCallback = CxlErrorLog;

  RcecProfileInstance = mPlatformApeiData->CxlMap->RcecProfile;

  // Search RCEC for CXL errors
  for (RcecIndex = 0; RcecIndex < mPlatformApeiData->CxlMap->RcecCount; RcecIndex++, RcecProfileInstance++) {
    if ((RcecProfileInstance->RbSegNum != Seg) ||
        (RcecProfileInstance->RbBusNum != Bus)) {
      continue;
    }

    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  CXL Error search at Seg: 0x%x, Bus: 0x%x, RCEC Id: %0d, DP count: %d, Device count: %d\n",
      RcecProfileInstance->RbSegNum,
      RcecProfileInstance->RbBusNum,
      RcecProfileInstance->RcecId,
      RcecProfileInstance->DpCnt,
      RcecProfileInstance->RciepCnt
    );

    // Check CXL device
    CxlDevErrStsCheck(RcecProfileInstance,
                      &CxlErrLogData,
                      MpioIntrPayload,
                      CXL_DEV_CHK_COMPONENT_ERROR_ONLY
                      );
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a End\n", __FUNCTION__);

  return Status;
}

VOID
SmmPrintEventIntrPolicy(
  IN       EVENT_INTR_PAYLOAD    *EventIntrOutputPayload
)
{
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Informational Event Log Interrupt: 0x%x\n", EventIntrOutputPayload->InformationEventIntrSetting.Value);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Warning Event Log Interrupt: 0x%x\n", EventIntrOutputPayload->WarningEventIntrSetting.Value);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Failure Event Log Interrupt: 0x%x\n", EventIntrOutputPayload->FailureEventIntrSetting.Value);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Fatal Event Log Interrupt: 0x%x\n", EventIntrOutputPayload->FatalEventIntrSetting.Value);
}

EFI_STATUS
SmmCxlGetEventIntrPolicy(
  IN       UINT32   RciepAddr,
  IN       UINT8    RcecId
  )
{
  EFI_STATUS            Status = EFI_SUCCESS;
  UINT32                OutputLength;
  EVENT_INTR_PAYLOAD    *EventIntrOutputPayload;
  UINT64                CxlMemDeviceRegAddrBase;
  UINT32                MailboxRegOffset;
  UINT32                MailboxRegLength;
  MB_CMD_STATUS         MbCmdStatus;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a Start\n", __FUNCTION__);

  CxlMemDeviceRegAddrBase = RasGetRciepRegisterBlockAddress (RciepAddr, CxlMemoryDeviceRegistersBlkId);
  if (0 == CxlMemDeviceRegAddrBase) {
    return EFI_UNSUPPORTED;
  }

  MailboxRegOffset = CxlGetMailboxRegAddrByCmpErrRptMode (
                       mPlatformApeiData->PlatRasPolicy.CxlComponentErrorReporting,
                       CxlMemDeviceRegAddrBase,
                       &MailboxRegLength
                     );
  if (MailboxRegOffset == 0) {
    //No Mailbox Register found
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] Unable to get the mailbox address of the CXL device!!!\n");
    return EFI_UNSUPPORTED;
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL Memory Device Register Base: 0x%016lX, Offset: 0x%x\n",
          CxlMemDeviceRegAddrBase,
          MailboxRegOffset);

  MbCmdStatus = MbCmdGetEventIntrPolicy (CxlMemDeviceRegAddrBase + MailboxRegOffset,
                                         &OutputLength,
                                         (UINT8**)&EventIntrOutputPayload
                                         );
  if (MbCmdStatus != Mb_Cmd_Success) {
    Status = EFI_INVALID_PARAMETER;
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "GetEventIntrPolicy command failed: 0x%08x\n", MbCmdStatus);
  }
  SmmPrintEventIntrPolicy(EventIntrOutputPayload);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a End\n", __FUNCTION__);

  return Status;
}

EFI_STATUS
SmmCxlSetEventIntrPolicyToMsi(
  IN       UINT32   RciepAddr,
  IN       UINT8    RcecId
  )
{
  EFI_STATUS            Status = EFI_SUCCESS;
  EVENT_INTR_PAYLOAD    SetEventIntrInputPayload;
  UINT64                CxlMemDeviceRegAddrBase;
  UINT32                MailboxRegOffset;
  UINT32                MailboxRegLength;
  MB_CMD_STATUS         MbCmdStatus;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a Start\n", __FUNCTION__);

  CxlMemDeviceRegAddrBase = RasGetRciepRegisterBlockAddress (RciepAddr, CxlMemoryDeviceRegistersBlkId);
  if (0 == CxlMemDeviceRegAddrBase) {
    return EFI_UNSUPPORTED;
  }

  MailboxRegOffset = CxlGetMailboxRegAddrByCmpErrRptMode (
                       mPlatformApeiData->PlatRasPolicy.CxlComponentErrorReporting,
                       CxlMemDeviceRegAddrBase,
                       &MailboxRegLength
                     );
  if (MailboxRegOffset == 0) {
    //No Mailbox Register found
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] Unable to get the mailbox address of the CXL device!!!\n");
    return EFI_UNSUPPORTED;
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL Memory Device Register Base: 0x%016lX, Offset: 0x%x\n",
          CxlMemDeviceRegAddrBase,
          MailboxRegOffset);

  SetEventIntrInputPayload.InformationEventIntrSetting.Value = 0;
  SetEventIntrInputPayload.InformationEventIntrSetting.Field.InterruptMode = EVENT_INTERRUPT_MODE_MSI_MSI_X;

  SetEventIntrInputPayload.WarningEventIntrSetting.Value = 0;
  SetEventIntrInputPayload.WarningEventIntrSetting.Field.InterruptMode = EVENT_INTERRUPT_MODE_MSI_MSI_X;

  SetEventIntrInputPayload.FailureEventIntrSetting.Value = 0;
  SetEventIntrInputPayload.FailureEventIntrSetting.Field.InterruptMode = EVENT_INTERRUPT_MODE_MSI_MSI_X;

  SetEventIntrInputPayload.FatalEventIntrSetting.Value = 0;
  SetEventIntrInputPayload.FatalEventIntrSetting.Field.InterruptMode = EVENT_INTERRUPT_MODE_MSI_MSI_X;

  SmmPrintEventIntrPolicy(&SetEventIntrInputPayload);

  MbCmdStatus = MbCmdSetEventIntrPolicy (CxlMemDeviceRegAddrBase + MailboxRegOffset,
                                         sizeof (EVENT_INTR_PAYLOAD),
                                         (UINT8*)&SetEventIntrInputPayload
                                         );
  if (MbCmdStatus != Mb_Cmd_Success) {
    Status = EFI_INVALID_PARAMETER;
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "SetEventIntrPolicy command failed: 0x%08x\n", MbCmdStatus);
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a End\n", __FUNCTION__);

  return Status;
}

EFI_STATUS
SmmCxlDeviceErrReportOff (
  RCEC_PROFILE            *RcecProfile,
  PCIe_AER_CONFIG_TEMP    *PcieDevAerSetting
  )
{
  EFI_STATUS          Status = EFI_SUCCESS;
  UINT8               Index;
  LIST_ENTRY          *Node;
  CXL_RCIEP_ENTRY     *CxlRciepEntry;
  PCI_ADDR            RciepPciAddr;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a Start\n", __FUNCTION__);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]Cxl RCiEP count : %d\n", RcecProfile->RciepCnt);
  if (RcecProfile->RciepCnt != 0) {
    //Search End Point device error
    for (Index = 0; Index < RcecProfile->RciepCnt; Index++) {
      if (Index == 0) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Get First Node, Head Forward Link Addr: 0x%08x\n",RcecProfile->RciepLinkList.ForwardLink);
        Node = GetFirstNode(&RcecProfile->RciepLinkList);
      } else {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Get Next Node, Current Node Address: 0x%08x\n",(UINTN)Node);
        Node = GetNextNode(&RcecProfile->RciepLinkList, Node);
      }
      CxlRciepEntry = (CXL_RCIEP_ENTRY*)Node;
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  RCiEP/RP Address: 0x%08x, DevType\n", CxlRciepEntry->DevAddr, CxlRciepEntry->DevType);

      RciepPciAddr.AddressValue = CxlRciepEntry->DevAddr;
      //PcieDevCntlEnableOnDevice (RciepPciAddr, PcieDevAerSetting);

      //Setup CXL device event log interrupt.
      Status = SmmCxlSetEventIntrPolicyToMsi(RciepPciAddr.AddressValue, RcecProfile->RcecId);
      if (Status != EFI_SUCCESS) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Disabling CXL Memory Device error reoprt failed\n");
      } else {
        //Event Interrupt Ploicy setting readback
        Status = SmmCxlGetEventIntrPolicy(RciepPciAddr.AddressValue, RcecProfile->RcecId);
      }
    }
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a End\n", __FUNCTION__);

  return Status;
}

EFI_STATUS
EFIAPI
AmdCxlOscCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SW_REGISTER_CONTEXT *DispatchContext,
  IN OUT   FCH_SMM_SW_CONTEXT                *SwContext,
  IN OUT   UINTN                             *SizeOfSwContext
  )
{
  EFI_STATUS            Status = EFI_SUCCESS;
  RCEC_PROFILE          *RcecProfileInstance;
  PCI_ADDR              RcecPciAddr;
  UINT16                RcecIndex;
  PCIe_AER_CONFIG_TEMP  PcieAerSetting;
  PCI_ADDR              RpPciAddr;
  UINT32                MpioStatus;
  UINT32                MpioArg[6];
  PCIE_PORT_PROFILE     *PciePortProfileInstance;
  UINT16                PciePortIndex;
  UINT8                 Index;
  LIST_ENTRY            *Node;
  PCIE_DEV_ENTRY        *PcieDevEntry;
  PCI_ADDR              RciepPciAddr;
  BOOLEAN               FwGrantControlToOs;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a Start\n", __FUNCTION__);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL _OSC Support: 0x%08X, Control: 0x%08X\n",
    mPlatformApeiData->RasAcpiSmmData->CxlOscSupp.Value,
    mPlatformApeiData->RasAcpiSmmData->CxlOscCtrl.Value
  );

  //
  //CXL memory error reporting
  //
  if (mPlatformApeiData->RasAcpiSmmData->CxlOscCtrl.Field.CxlMemoryErrorReportingControl == 1) {
    //The OS wants to take control of this feature.

    FwGrantControlToOs = FALSE;
    if ((mPlatformApeiData->RasAcpiSmmData->CxlOscSupp.Field.RcdRchPortRegisterAccessSupported == 0) &&
        (mPlatformApeiData->RasAcpiSmmData->CxlOscSupp.Field.CxlVhRegisterAccessSupported == 0)) {
    //Case 1: The OS does not support CXL 1.1 and CXL2.0 Port Register Access
    //  => denies OS control of CXL memory error reporting via CXL _OSC
      FwGrantControlToOs = FALSE;
    } else {
      if ((mPlatformApeiData->PlatRasPolicy.CxlComponentErrorReporting == DEBUG_FW_FIRST) ||
          ((mPlatformApeiData->PlatRasPolicy.CxlComponentErrorReporting == FORCE_FW_FIRST) &&
           (mPlatformApeiData->PlatRasPolicy.AllCxlDevsSupportSecMailbox))) {
    //Case 2: The CXL Memory Error Reporting is set to DEBUG_FW_FIRST mode
    //        or FORCE_FW_FIRST with AllCxlDevsSupportSecMailbox = TRUE.
    //  => denies OS control of CXL memory error reporting via CXL _OSC
        FwGrantControlToOs = FALSE;
      } else {
    //Case 3: The CXL Memory Error Reporting is set to ALLOW_OS_FIRST
    //        or FORCE_FW_FIRST with AllCxlDevsSupportSecMailbox = FALSE.
    //  => grants control to the OS via CXL _OSC.
        FwGrantControlToOs = TRUE;
      }
    }
    if (FwGrantControlToOs) {
      //
      //Release control of CXL memory error reporting to the OS - Undo FW_FIRST error handling
      //
      //CXL 1.1
      RcecProfileInstance = mPlatformApeiData->CxlMap->RcecProfile;
      for (RcecIndex = 0; RcecIndex < mPlatformApeiData->CxlMap->RcecCount; RcecIndex++, RcecProfileInstance++) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Enable CXL Error Report at Seg: 0x%x, Bus: 0x%x, RCEC Id: %0d, DP count: %d, Device count: %d\n",
          RcecProfileInstance->RbSegNum,
          RcecProfileInstance->RbBusNum,
          RcecProfileInstance->RcecId,
          RcecProfileInstance->DpCnt,
          RcecProfileInstance->RciepCnt
        );

        if ((0 == RcecProfileInstance->DpCnt) && (0 == RcecProfileInstance->RciepCnt)) {
          //Skip config if no CXL device found.
          continue;
        }
        RcecPciAddr.AddressValue = RcecProfileInstance->RcecPciAddr;
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "RCEC Address: 0x%08x\n", RcecPciAddr.AddressValue);

        //Step 1 of 2:
        //Sends a message to MPIO FW to ignore CXL RAS VDMs
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Sends a message to MPIO FW to ignore CXL RAS VDMs\n");
        NbioMpioServiceCommonInitArguments (MpioArg);
        MpioArg[0] = 0; //0: Disable SMI generation, 1: Enable SMI generation
        MpioStatus = MpioServiceRequest (RcecPciAddr, BIOS_MPIO_MSG_CXL_ERROR_FW_FIRST, MpioArg, 0);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Response from MPIO: %x\n", MpioStatus);

        //Step 2 of 2:
        //Re-configures all CXL endpoints to:
        //  Disable Component error reporting.
        //  Set Failure EventLog Interrupt Policy: 00b = No Interrupts.
        //  Set Fatal EventLog Interrupt Policy: 00b = No Interrupts.
        //  Set Informational EventLog Interrupt Policy: 00b = No Interrupts.
        //  Set Warning EventLog Interrupt Policy: 00b = No Interrupts.
        //
        PcieAerSetting.AerEnable = 1;
        PcieAerSetting.PciSeg = (UINT8)RcecPciAddr.Address.Segment;
        PcieAerSetting.PciBus = (UINT8)RcecPciAddr.Address.Bus;
        PcieAerSetting.PciDev = (UINT8)RcecPciAddr.Address.Device;
        PcieAerSetting.PciFunc = (UINT8)RcecPciAddr.Address.Function;
        PcieAerSetting.CorrectableMask = mPlatformApeiData->PlatRasPolicy.PcieDevCorrectedErrorMask;
        PcieAerSetting.UncorrectableMask = mPlatformApeiData->PlatRasPolicy.PcieDevUnCorrectedErrorMask;
        PcieAerSetting.UncorrectableSeverity = mPlatformApeiData->PlatRasPolicy.PcieDevUnCorrectedErrorSeverity;

        //IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Device Correctable Error Mask: 0x%08x\n", PcieAerSetting.CorrectableMask);
        //IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Device UnCorrectable Error Mask: 0x%08x\n", PcieAerSetting.UncorrectableMask);
        //IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Device UnCorrectable Error Severity: 0x%08x\n", PcieAerSetting.UncorrectableSeverity);

        // Disable RCiEP error reporting
        SmmCxlDeviceErrReportOff(RcecProfileInstance, &PcieAerSetting);
      }

      //CXL 2.0
      PciePortProfileInstance = mPlatformApeiData->AmdPciePortMap->PciPortNumber;
      for (PciePortIndex = 0; PciePortIndex < mPlatformApeiData->AmdPciePortMap->PortCount; PciePortIndex++, PciePortProfileInstance++) {
        if (!PciePortProfileInstance->CxlPresent ||
            (PciePortProfileInstance->CxlVersion != 2)) {
          continue;
        }

        //Check PCI devices under root port
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  EndPointDevCnt : %d\n", PciePortProfileInstance->EndPointDevCnt);
        if (PciePortProfileInstance->EndPointDevCnt == 0) {
          continue;
        }

        RpPciAddr.AddressValue = PciePortProfileInstance->RpPciAddr;
        RpPciAddr.Address.Function = 0;
        //Sends a message to MPIO FW to ignore CXL RAS VDMs
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Sends a message to MPIO FW to ignore CXL RAS VDMs\n");
        NbioMpioServiceCommonInitArguments (MpioArg);
        MpioArg[0] = 0; //0: Disable SMI generation, 1: Enable SMI generation
        MpioStatus = MpioServiceRequest (RpPciAddr, BIOS_MPIO_MSG_CXL_ERROR_FW_FIRST, MpioArg, 0);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Response from MPIO: %x\n", MpioStatus);

        //Search for CXL devices and set EventIntrPolicy on them
        for (Index = 0; Index <PciePortProfileInstance->EndPointDevCnt; Index++) {
          if (Index == 0) {
            Node = GetFirstNode(&PciePortProfileInstance->PciLinkList);
          } else {
            Node = GetNextNode(&PciePortProfileInstance->PciLinkList, Node);
          }
          PcieDevEntry = (PCIE_DEV_ENTRY*)Node;
          RciepPciAddr.AddressValue = PcieDevEntry->DevAddr;
          if (RasFindCxlDvsecCapability (RciepPciAddr.AddressValue, PcieDvsecforCxlDevice) == 0) {
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, " [PCI address: 0x%08X] is not a CXL device. (PCIe DVSEC for CXL Device not found)\n",
              RciepPciAddr.AddressValue);
            continue;
          }

          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, " [PCI address: 0x%08X] is a CXL device.\n",
            RciepPciAddr.AddressValue);

          //Setup CXL device event log interrupt.
          Status = SmmCxlSetEventIntrPolicyToMsi(RciepPciAddr.AddressValue, PciePortProfileInstance->NbioDieNum);//NbioDieNum = mRbBusMap->RbBusEntry[Rb].RbIndex
          if (Status != EFI_SUCCESS) {
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Disabling CXL Memory Device error reoprt failed\n");
          } else {
            //Event Interrupt Ploicy setting readback
            Status = SmmCxlGetEventIntrPolicy(RciepPciAddr.AddressValue, PciePortProfileInstance->NbioDieNum);
          }
        }
      }
    }
    mPlatformApeiData->RasAcpiSmmData->CxlOscCtrl.Field.CxlMemoryErrorReportingControl = FwGrantControlToOs;
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL Memory Error Reporting Control = 0x%X\n",
      FwGrantControlToOs);
  }

  //
  //CXL Timeout and Isolation control
  //
  if (mPlatformApeiData->RasAcpiSmmData->CxlOscCtrl.Field.CxlMemIsolationEnableControl == 1) {
    //The OS wants to take control of this feature.

    FwGrantControlToOs = FALSE;
    if ((mPlatformApeiData->RasAcpiSmmData->CxlOscSupp.Field.CxlTimeoutAndIsolationSupported == 1) &&
        (mPlatformApeiData->PlatRasPolicy.AmdCxlMemIsolationEnable == 0)) {
      //CPM grants control
      FwGrantControlToOs = TRUE;
    } else {
      //CPM denies control
      FwGrantControlToOs = FALSE;
    }
    mPlatformApeiData->RasAcpiSmmData->CxlOscCtrl.Field.CxlMemIsolationEnableControl = FwGrantControlToOs;
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL.mem Isolation Enable Control = 0x%X\n",
      FwGrantControlToOs);
  }

  //
  //CXL Timeout and Isolation Notification control
  //
  if (mPlatformApeiData->RasAcpiSmmData->CxlOscCtrl.Field.CxlIsolationNotificationControl == 1) {
    //The OS wants to take control of this feature.

    FwGrantControlToOs = FALSE;
    if ((mPlatformApeiData->RasAcpiSmmData->CxlOscSupp.Field.CxlTimeoutAndIsolationSupported == 1) &&
        (mPlatformApeiData->PlatRasPolicy.AmdCxlMemIsolationFwNotification == 0)) {
      //CPM grants control
      FwGrantControlToOs = TRUE;
    } else {
      //CPM denies control
      FwGrantControlToOs = FALSE;
    }
    mPlatformApeiData->RasAcpiSmmData->CxlOscCtrl.Field.CxlIsolationNotificationControl = FwGrantControlToOs;
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
      "CXL Isolation Notification Control = 0x%X\n",
      FwGrantControlToOs);
  }

  //
  // Clear unsupported feature bits
  //
  mPlatformApeiData->RasAcpiSmmData->CxlOscCtrl.Field.CxlMemIsolationRecoveryControl = 0;   //not supported by the SoC
  mPlatformApeiData->RasAcpiSmmData->CxlOscCtrl.Field.CxlCacheIsolationEnableControl = 0;   //not supported by the SoC
  mPlatformApeiData->RasAcpiSmmData->CxlOscCtrl.Field.CxlCacheIsolationRecoveryControl = 0; //not supported by the SoC

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a End\n", __FUNCTION__);

  return Status;
}

EFI_STATUS
Cxl_1p1_2p0_CmpErrScan (
  IN       UINT8  IndexOfRbBusMap
  )
{
  UINT32                BaseAddr;
  UINT8                 Nbio;
  UINT8                 Iohc;
  UINT8                 Seg;
  UINT8                 Bus;
  UINT32                IohcSwSmiCntlAddress;
  MPIO_SW_SMI_CNTL      MpioSwSmiCntl;
  PCIE_PORT_PROFILE     *PciePortProfileInstance;
  UINT16                PciePortIndex;
  UINT8                 Index;
  LIST_ENTRY            *Node;
  PCIE_DEV_ENTRY        *PcieDevEntry;
  RAS_ERR_LOG_DATA      CxlErrLogData;
  CXL_ERROR_LOG_DATA    CxlErrorLogData;
  PCI_ADDR              RpAddress;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] Cxl_1p1_2p0_CmpErrScan - Entry\n");

  if (!mPlatformApeiData->PlatRasPolicy.CpmCxlErrorReport) {  //FALSE: Disable , TRUE: Enable
    return EFI_UNSUPPORTED;
  }
  if (mPlatformApeiData->RasAcpiSmmData->CxlOscCtrl.Value == 1) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] OS is controlling CXL memory error reporting.\n");
    return EFI_UNSUPPORTED;
  }

  Nbio = mRbBusMap->RbBusEntry[IndexOfRbBusMap].Nbio;
  Iohc = mRbBusMap->RbBusEntry[IndexOfRbBusMap].Iohc;
  Seg  = mRbBusMap->RbBusEntry[IndexOfRbBusMap].RbSeg;
  Bus  = mRbBusMap->RbBusEntry[IndexOfRbBusMap].RbBusBase;

  //Is Component Error?
  //Read NBIO SW SMI status
  BaseAddr = ((Iohc & BIT0) == 0) ? NBIO0_IOHUB0_IOHC_SW_SMI_CNTL : NBIO0_IOHUB1_IOHC_SW_SMI_CNTL;
  IohcSwSmiCntlAddress = BaseAddr + (IOHC_SMN_ADDR_OFFSET * (Iohc >> 1)) + (NBIO_SMN_ADDR_OFFSET * Nbio);
  RasSmnRead(SMN_SEG_BUS (Seg, Bus), IohcSwSmiCntlAddress, &MpioSwSmiCntl.Value);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] IOHC_SW_SMI_CNTL VALUE: %x\n", MpioSwSmiCntl.Value);
  if (MpioSwSmiCntl.Field.CxlEvent != 1) {
    return EFI_NOT_FOUND;
  }

 //SMI from MPIO for device components error report

 //CXL 1.1
 CxlComponentErrorScan (Seg, Bus, (UINT8)MpioSwSmiCntl.Field.Payload);

 //CXL 2.0
 //Search the device behind the specified CXL 2.0 root port for CXL component errors.
  PciePortProfileInstance = mPlatformApeiData->AmdPciePortMap->PciPortNumber;
  for (PciePortIndex = 0; PciePortIndex < mPlatformApeiData->AmdPciePortMap->PortCount; PciePortIndex++, PciePortProfileInstance++) {
    RpAddress.AddressValue = PciePortProfileInstance->RpPciAddr;
    if ((RpAddress.Address.Segment != Seg) || (RpAddress.Address.Bus != Bus)) {
      //find next
      continue;
    }
    if (!PciePortProfileInstance->CxlPresent ||
        (PciePortProfileInstance->CxlVersion != 2)) {
      continue;
    }
    if (PciePortProfileInstance->EndPointDevCnt == 0) {
      continue;
    }

    //CXL Component Error detection
    for (Index = 0; Index < PciePortProfileInstance->EndPointDevCnt; Index++) {
      if (Index == 0) {
        //IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
        //  "  Get First Node, Head Forward Link Addr: 0x%08x\n",PciePortProfileInstance->PciLinkList.ForwardLink);
        Node = GetFirstNode(&PciePortProfileInstance->PciLinkList);
      } else {
        //IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
        //"  Get Next Node, Current Node Address: 0x%08x\n",(UINTN)Node);
        Node = GetNextNode(&PciePortProfileInstance->PciLinkList, Node);
      }
      PcieDevEntry = (PCIE_DEV_ENTRY*)Node;

      CxlErrLogData.Buffer = &CxlErrorLogData;
      CxlErrLogData.RasLogCallback = CxlErrorLog;
      Cxl2p0DevErrStsCheck (
        PciePortProfileInstance,
        PcieDevEntry,
        &CxlErrLogData,
        (UINT8)MpioSwSmiCntl.Field.Payload,
        CXL_DEV_CHK_COMPONENT_ERROR_ONLY
      );
    }
  }

  //Clear NBIO SW SMI status
  RasSmnWrite(SMN_SEG_BUS (Seg, Bus), IohcSwSmiCntlAddress, &MpioSwSmiCntl.Value);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] Cxl_1p1_2p0_CmpErrScan - Exit\n");

  return EFI_SUCCESS;
}

UINT32
GetPciepCxlIsoStatusSmnAddr (
  IN  UINT8     Iom,
  IN  UINT8     PcieCoreNumInNbio
  )
{
  UINT8           Nbio;
  UINT32          PciepCxlIsoStatusSmnAddr;

  if ((Iom > 7) || (PcieCoreNumInNbio > 5) || (PcieCoreNumInNbio == 4)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR,
      "[RAS - CXL] %a failed. Iom = %d, PcieCoreNumInNbio = %d\n",
      __FUNCTION__,
      Iom,
      PcieCoreNumInNbio
    );

    return 0;
  }

  Nbio = (Iom >> 2) & BIT0;  //Nbio = Iom[BIT2]

  //PCIEPORT::PCIEP_CXL_ISO_STATUS
  switch (PcieCoreNumInNbio) {
    case 0:
      PciepCxlIsoStatusSmnAddr = 0x1A340268UL;
      break;
    case 1:
      PciepCxlIsoStatusSmnAddr = 0x1A440268UL;
      break;
    case 2:
      PciepCxlIsoStatusSmnAddr = 0x1A740268UL;
      break;
    case 3:
      PciepCxlIsoStatusSmnAddr = 0x1A840268UL;
      break;
    case 5: /*PCIe 5. Bonus Lanes*/
      PciepCxlIsoStatusSmnAddr = 0x1AB40268UL;
      break;
    default:
      //Should not be here
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR,
        "[RAS - CXL] %a failed. PcieCoreNumInNbio = %d\n",
        __FUNCTION__,
        PcieCoreNumInNbio
      );
      return 0;
      break;
  }

  PciepCxlIsoStatusSmnAddr += (Nbio * NBIO_SMN_ADDR_OFFSET);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
    "[RAS - CXL] %a PciepCxlIsoStatusSmnAddr = 0x%0lx @ Iom: %d, PcieCoreNumInNbio: %d\n",
    __FUNCTION__,
    PciepCxlIsoStatusSmnAddr,
    Iom,
    PcieCoreNumInNbio
  );

  return PciepCxlIsoStatusSmnAddr;
}

//CXL.mem Error Isolation
EFI_STATUS
CxlMemErrorIsolation (
  IN       UINT8  IndexOfRbBusMap
  )
{
  UINT8                   Soc;
  UINT8                   Iom;
  UINT8                   Nbio;
  UINT8                   Iohc;
  UINT8                   Pcie;
  UINT32                  PciepCxlIsoStatusSmnAddr;
  UINT32                  PciepCxlIsoStatus;
  UINT8                   MaxPciePortCnt;
  UINT8                   PortIndex;
  UINT8                   Seg;
  UINT8                   Bus;
  UINT8                   CxlAcpiNameIdx;

  //Each IOHC has only one PCIe Gen5 9x16 controller.
  //Except NBIO0 IOHC2, it has two PCIe controller: one PCIe Gen5 9x16 controller and one PCIe Gen4 bonus lanes controller
  //IOM 0 => NBIO 0, IOHC 0, PCIE 0
  //IOM 1 => NBIO 0, IOHC 3, PCIE 3
  //IOM 2 => NBIO 0, IOHC 2, PCIE 1, PCIE 5
  //IOM 3 => MBIO 0, IOHC 1, PCIE 2
  //IOM 4 => NBIO 1, IOHC 0, PCIE 0
  //IOM 5 => NBIO 1, IOHC 3, PCIE 3
  //IOM 6 => NBIO 1, IOHC 2, PCIE 1
  //IOM 7 => MBIO 1, IOHC 1, PCIE 2
  Soc  = mRbBusMap->RbBusEntry[IndexOfRbBusMap].SocketNumber;
  Iom  = mRbBusMap->RbBusEntry[IndexOfRbBusMap].RbIndex;
  Nbio = mRbBusMap->RbBusEntry[IndexOfRbBusMap].Nbio;
  Iohc = mRbBusMap->RbBusEntry[IndexOfRbBusMap].Iohc;
  Seg  = mRbBusMap->RbBusEntry[IndexOfRbBusMap].RbSeg;
  Bus  = mRbBusMap->RbBusEntry[IndexOfRbBusMap].RbBusBase;
  Pcie = (((Iohc & BIT0) << 1) | (((Iohc & BIT1) >> 1)));

  PciepCxlIsoStatusSmnAddr = GetPciepCxlIsoStatusSmnAddr(Iom, Pcie);

  MaxPciePortCnt = (Pcie == 5) ? 8 : 9;
  for (PortIndex = 0; PortIndex < MaxPciePortCnt; PortIndex++) {
    RasSmnRead (
      SMN_SEG_BUS (Seg, Bus),
      (PciepCxlIsoStatusSmnAddr | (PortIndex << 12)),
      &PciepCxlIsoStatus
    );

    if ((PciepCxlIsoStatus & BIT1 /*CXL_ISO_ERRCOR_STAT*/) != 0) {
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
        "[RAS - CXL] %a  - CaMemOutIsolationErrCor was asserted for this port: Addr = 0x%08X: Data = 0x%08X\n",
        __FUNCTION__,
        PciepCxlIsoStatusSmnAddr,
        PciepCxlIsoStatus
      );
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
        "[RAS - CXL] => IOM: 0x%02X, NBIO: 0x%02X, IOHC: 0x%02X, PCIe: 0x%02X, Port: 0x%02X\n",
        Iom, Nbio, Iohc, Pcie, PortIndex
      );

      if ((mPlatformApeiData->RasAcpiSmmData->CxlOscSupp.Field.CxlTimeoutAndIsolationSupported == 1) &&
          (mPlatformApeiData->PlatRasPolicy.AmdCxlMemIsolationFwNotification == 1)) {
        //
        // Notify the OS of the CXL device that has the error.
        //
// CXL Timeout and Isolation Notification Sample code - Start (platform-specific code)
        CxlAcpiNameIdx = 0;
        if (!BRH_CXL_CAPABLE_RB(Iom) || (PortIndex >= 4 /*Up to 4 CXL ports per P-link*/)) {
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR,
            "[RAS - CXL] ERROR! Not a P-Link or incorrect Port number. IOM = 0x%02X. Port: 0x%02X\n",
            Iom, PortIndex
          );
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR,
            "The CXL device with the error could not be located, so ACPI Notify was aborted.\n");
        } else {
          CxlAcpiNameIdx = BRH_IOS_TO_RBINDEX(Iom);
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
            "[RAS - CXL] CXL device is located at Soc: 0x%02X, P-Link: 0x%02X, NBIO RbIndex: 0x%02X, Port: 0x%02X\n",
            Soc, BRH_IOS_TO_CNLI(Iom), BRH_IOS_TO_RBINDEX(Iom), PortIndex
          );
          if (Soc == 0 && CxlAcpiNameIdx == 2) {
            mPlatformApeiData->RasAcpiSmmData->CxlAslDevName =
              SIGNATURE_32 ('P', 'C', 'I', '0');
          } else {
            mPlatformApeiData->RasAcpiSmmData->CxlAslDevName =
              SIGNATURE_32 ('S', ('0' + Soc), 'D', (CxlAcpiNameIdx + ((CxlAcpiNameIdx <= 9) ? 0x30 : 0x37)));
          }
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
            "[RAS - CXL] %a - Notify CXL device (ACPI ASCII name in Hex: 0x%08X)@ Bus: 0x%02X.\n",
            __FUNCTION__,
            mPlatformApeiData->RasAcpiSmmData->CxlAslDevName,
            Bus
          );
          RasTriggerSci();
        }
// CXL Timeout and Isolation Notification Sample code - End
      }

      //
      // Adding OEM Hook here
      //

    }
  }

  return EFI_SUCCESS;
}