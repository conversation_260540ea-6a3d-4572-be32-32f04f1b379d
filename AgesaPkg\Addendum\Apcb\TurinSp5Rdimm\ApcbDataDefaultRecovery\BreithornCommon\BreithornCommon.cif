<component>
    name = "BreithornCommon"
    category = ModulePart
    LocalRoot = "AgesaPkg\Addendum\Apcb\TurinSp5Rdimm\ApcbDataDefaultRecovery\BreithornCommon\"
    RefName = "AgesaPkg.Addendum.Apcb.TurinSp5Rdimm.ApcbDataDefaultRecovery.BreithornCommon"
[files]
"ApcbCustomizedBoardDefinitions.h"
"ApcbData_BRH_GID_0x1000_Type_PriorityCustomization.c"
"ApcbData_BRH_GID_0x1704_Type_BoardIdGettingMethod.c"
"ApcbData_BRH_GID_0x1704_Type_ConsoleOutControl.c"
"ApcbData_BRH_GID_0x1704_Type_DdrTrainingOverride.c"
"ApcbData_BRH_GID_0x1704_Type_DimmHubInfo.c"
"ApcbData_BRH_GID_0x1704_Type_DimmInfoSpd.c"
"ApcbData_BRH_GID_0x1704_Type_ErrorOutControl.c"
"ApcbData_BRH_GID_0x1704_Type_ExtVoltageControl.c"
"ApcbData_BRH_GID_0x1704_Type_MemDfeSearchScheme.c"
"ApcbData_BRH_GID_0x1704_Type_PmuBistVendorAlgorithm.c"
"ApcbData_BRH_GID_0x1704_Type_PsBusCfgRDIMMDdr5.c"
"ApcbData_BRH_GID_0x1704_Type_PsBusCfgRDIMMDdr5_Hynix.c"
"ApcbData_BRH_GID_0x1704_Type_PsBusCfgRDIMMDdr5_Hynix_24Gb.c"
"ApcbData_BRH_GID_0x1704_Type_PsBusCfgRDIMMDdr5_Hynix_DIMM_6400.c"
"ApcbData_BRH_GID_0x1704_Type_PsBusCfgRDIMMDdr5_Micron.c"
"ApcbData_BRH_GID_0x1704_Type_PsBusCfgRDIMMDdr5_Micron_24Gb.c"
"ApcbData_BRH_GID_0x1704_Type_PsBusCfgRDIMMDdr5_Micron_DIMM_6400.c"
"ApcbData_BRH_GID_0x1704_Type_PsBusCfgRDIMMDdr5_Samsung.c"
"ApcbData_BRH_GID_0x1704_Type_PsBusCfgRDIMMDdr5_Samsung_24Gb.c"
"ApcbData_BRH_GID_0x1704_Type_PsBusCfgRDIMMDdr5_Samsung_DIMM_6400.c"
"ApcbData_BRH_GID_0x1704_Type_PsDramCaPinMapping.c"
"ApcbData_BRH_GID_0x1704_Type_PsDramDqPinMapping.c"
"ApcbData_BRH_GID_0x1704_Type_PsMaxFreq3DSRDIMMDdr5.c"
"ApcbData_BRH_GID_0x1704_Type_PsMaxFreqRDIMMDdr5.c"
"ApcbData_BRH_GID_0x1704_Type_PsMaxFreqRDIMMDdr5_C1.c"
"ApcbData_BRH_GID_0x1704_Type_PsoOverride.c"
"ApcbData_BRH_GID_0x1704_Type_RawCardCfgRDIMMDdr5_Hynix.c"
"ApcbData_BRH_GID_0x1704_Type_RawCardCfgRDIMMDdr5_Micron.c"
"ApcbData_BRH_GID_0x1704_Type_RawCardCfgRDIMMDdr5_Samsung.c"
"ApcbData_BRH_GID_0x1704_Type_SpdInfo.c"
"ApcbData_BRH_GID_0x1705_Type_PcieConfig.c"
"ApcbData_BRH_GID_0x1706_Type_EspiInitConfiguration.c"
"ApcbData_BRH_GID_0x1706_Type_EspiSioInitConfiguration.c"
"ApcbData_BRH_GID_0x3000_Type_Token1Byte.c"
"ApcbData_BRH_GID_0x3000_Type_Token2Bytes.c"
"ApcbData_BRH_GID_0x3000_Type_Token4Bytes.c"
"ApcbData_BRH_GID_0x3000_Type_TokenBoolean.c"
"ApcbData_FP9_GID_0x1704_Type_DimmInfoDdr5.c"
"SetApcbDataFileList.bat"
<endComponent>
