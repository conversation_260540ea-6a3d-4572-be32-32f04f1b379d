//#pragma message( "Compal Server Override Compiling-" __FILE__ )
//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2025, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************

#ifndef _RAS_COMMON_LIB__H_
#define _RAS_COMMON_LIB__H_

#define MCA_ERROR_CORRECTED      0x02

//From IPMI Spec. Sensor type codes (Table 42-3)
#define SEL_SENSOR_TYPE_MEMORY                           (0x0C)
#define SEL_MEMORY_SENSOR_OFFSET_CORRECTABLE_ERROR       (0x00)
#define SEL_MEMORY_SENSOR_OFFSET_UNCORRECTABLE_ERROR     (0x01)
#define SEL_MEMORY_SENSOR_OFFSET_PARITY_ERROR            (0x02)
#define SEL_MEMORY_SENSOR_OFFSET_DEVICE_DISABLED         (0x04)

#define SEL_SENSOR_TYPE_CRITICAL_INTERRUPT               (0x13)
#define SEL_CRITICAL_INTERRUPT_SENSOR_OFFSET_PCI_PERR    (0x04)
#define SEL_CRITICAL_INTERRUPT_SENSOR_OFFSET_PCI_SERR    (0x05)

//COMPAL_CHANGE >>>
#define SEL_SENSOR_TYPE_OEM                              (0x07)
#define SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR          (0x0C)
#define SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR        (0x0B)
//COMPAL_CHANGE <<<

#define DUMMY_SENSOR_NUMBER                              (0x00)
#define SEL_OEM_CODE_IN_BYTE2AND3                        (0xA0)

#define GPNV_OEM_TYPE                                    (0x90)

#define EFI_SOFTWARE_ID_SELECT 0x01
#define SMI_HANDLER_SOFTWARE_ID 0x10
#define EFI_GENERATOR_ID(SOFTWARE_ID) ( (SOFTWARE_ID << 1) | (EFI_SOFTWARE_ID_SELECT) )

#define SEL_SENSOR_TYPE_CXL                              (0xC1)
#define SEL_SENSOR_TYPE_CXL_2_0                          (0xC2)
#define SEL_CXL_SENSOR_PROTOCOL_ERROR                    (0x01)
#define SEL_CXL_SENSOR_COMPONENT_ERROR                   (0x02)

#define SEL_CXL_SENSOR_CORRECTABLE_ERROR                 (0)
#define SEL_CXL_SENSOR_UNCORRECTABLE_ERROR               (1)

typedef enum _MCA_STATUS_UMC_EXTERR_TYPE {
  DramEccErr = 0,
  WriteDataPoisonErr,
  SdpParityErr,
  ApbErr,
  AddressCommandParityErr,
  WriteDataCrcErr,
  EcsRowErr = 8
} MCA_STATUS_UMC_EXTERR_TYPE;

typedef enum _RAS_ELOG_MEM_TYPE {
  MEM_TYPE_ECC  ,
  MEM_TYPE_PARITY,
  MEM_TYPE_CRC,
  MEM_TYPE_DATA_POISON,
  MEM_TYPE_ECSROW
} RAS_ELOG_MEM_TYPE;

//OEM Note
//Use the following type to detect the events logged under OEM type
// In case of SMBIOS,   ErrorBuffer[1] has OEM type in MSB and Severity in LSB 
// In case of IPMI SEL, OEMEvData1 has OEM type in MSB and Severity in LSB 
typedef enum _RAS_ELOG_OEM_TYPE {
  OEM_TYPE_PIE = 0x3, //
  OEM_TYPE_NBIO,
  OEM_TYPE_NBIF,
  OEM_TYPE_SMN,
  OEM_TYPE_SATA,
  OEM_TYPE_LS = 0x80, //MCA types: Bit 7 = 1
  OEM_TYPE_IF,
  OEM_TYPE_L2,
  OEM_TYPE_DE,
  OEM_TYPE_EX,
  OEM_TYPE_FP,
  OEM_TYPE_L3,
  OEM_TYPE_CS,   
  OEM_TYPE_UMC,
  OEM_TYPE_PB,
  OEM_TYPE_PSP,
  OEM_TYPE_SMU,
  OEM_TYPE_MP5,
  OEM_TYPE_PCIE,
  OEM_TYPE_CPU_GEN, // 0x8E
  OEM_TYPE_GMI,     // 0x8F
  OEM_TYPE_XGMI,    // 0x90
  OEM_TYPE_SHUB = 0x91, 
  OEM_TYPE_USB,
  OEM_TYPE_WAFL,
  OEM_TYPE_MPDMA,
  OEM_TYPE_PMIC,
  OEM_TYPE_CXL_PROTOCOL,
  OEM_TYPE_CXL_COMPONENT
}RAS_ELOG_OEM_TYPE;

//CXL Error Log Type
typedef enum _RAS_ELOG_CXL_TYPE {
  CXL_ELOG_IO = 0,
  CXL_ELOG_CACHE_MEM,
  CXL_ELOG_COMPONENT_EVENT
} RAS_ELOG_CXL_TYPE;

#endif
