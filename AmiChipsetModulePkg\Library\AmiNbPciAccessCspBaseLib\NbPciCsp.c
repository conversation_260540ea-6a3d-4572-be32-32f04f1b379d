//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************

/** @file NbPciCsp.c
    This file contains generic NB code that is common between
    various components such as NB PEI, DXE etc

**/

#include <Token.h>

/****** DO NOT WRITE ABOVE THIS LINE *******/

#include <PciHostBridge.h>
#include <Nb.h>

#include <Library/DxeServicesTableLib.h>
#include <Library/UefiBootServicesTableLib.h>
#include <Library/UefiRuntimeServicesTableLib.h>
#include <Protocol/AcpiTable.h>
#if (!defined(BACKWARD_COMPATIBLE_MODE))
#include "Protocol/AcpiSupport.h"
#endif

#include <Library/ProgramMmioRange.h>
#include <Library/DebugLib.h>

#include <Library/SmnAccessLib.h>
#include <Protocol/AmiBoardInfo2.h>
#include <Protocol/FabricTopologyServices2.h>
#include <Protocol/FabricResourceManagerServicesProtocol.h>
#include <Protocol/SocLogicalIdProtocol.h>
#include <AMD.h>
#include <Protocol/AmdCxlServicesProtocol.h>
#include <Protocol/AmdNbioServices.h>

#if ACPI_SUPPORT
#include "AcpiOemElinks.h"
#endif

typedef struct _IO_RES_RB {
    UINT64  Base;
    UINT64  Size;
    UINT64  PhyRbNo;
    UINT64  ReqSize;
    BOOLEAN Processed;
}IO_RES_RB;

typedef struct {
    BOOLEAN                 OutOfResources;
    UINTN                   BusBase;
    UINTN                   BusLength;
    UINT8                   NewBusBase;
    UINT16                  NewBusLength;
    UINT64                  BusRequired;
    UINTN                   PhysicalRbNumber;
    UINTN                   SocketNumber;
} BUS_RB_RESOURCE;

typedef struct _BUS_RESOURCE {
    UINTN                   RbsUnaccountedFor;
    BUS_RB_RESOURCE         RbBusRes[MAX_ROOT_BRIDGE_COUNT];
} BUS_RESOURCE;

#pragma    pack(push, 1)

typedef struct _CONFIGURATION_SPACE_BASE_ADDR_ALLOCATION_STRUCTURE {
    UINT64            BaseAddr; //Base address of 256MB extended config space
    UINT16            PciSeg;   //Segment # of PCI Bus
    UINT8             StartBus; //Start bus number of PCI segment
    UINT8             EndBus;   //End bus number of PCI segment
    UINT32            Reserved; //Reserved DWORD
} CONFIGURATION_SPACE_BASE_ADDR_ALLOCATION_STRUCTURE;

typedef struct _MCFG_20_MSEG {
    ACPI_HDR                                             Header;     //0..35
    UINT64                                               Reserved;   //Bits 1-31 are reserved in ACPI 2.0
    CONFIGURATION_SPACE_BASE_ADDR_ALLOCATION_STRUCTURE   BaseAddrAllocation[];
} MCFG_20_MSEG;
#pragma    pack(pop)

UINTN AlignFromGra(UINTN g);// Aptio V Server override : Resolve build error from AlignFromGra
//--------------------------------------------------------------------------

//Global Vars

VOID                        *mAcpiReg;
UINTN                       mMcfgTblKey = 0;
EFI_EVENT                   mAcpiEvent;

FABRIC_RESOURCE_FOR_EACH_RB *gFabricResource = NULL;
IO_RES_RB                   gIoMap[MAX_SOCKETS_SUPPORTED][MAX_RBS_PER_SOCKET];
UINTN                       mNumSoCPresent = MAX_SOCKETS_SUPPORTED;
UINTN                       mRbsPerSocket;
BOOLEAN                     gIoMapInitialized = FALSE;
BOOLEAN                     mIoReallocationReq = FALSE;

STATIC BOOLEAN              ResourceOverlap = FALSE;

BUS_RESOURCE               gBusResources;
BOOLEAN                    gBusOorHit = FALSE;

static  EFI_GUID gAmiPciOutOfResVarGuid = AMI_PCI_OUT_OF_RESOURCES_GUID;
//--------------------------------------------------------------------------

//WMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWM
//PORTING!!!//PORTING!!!//PORTING!!!//PORTING!!!//PORTING!!!//PORTING!!!//PORTING!!!
//PORTING!!!//PORTING!!!//PORTING!!!//PORTING!!!//PORTING!!!//PORTING!!!//PORTING!!!
//PORTING!!!//PORTING!!!//PORTING!!!//PORTING!!!//PORTING!!!//PORTING!!!//PORTING!!!
//PORTING!!!//PORTING!!!//PORTING!!!//PORTING!!!//PORTING!!!//PORTING!!!//PORTING!!!
//PORTING!!!//PORTING!!!//PORTING!!!//PORTING!!!//PORTING!!!//PORTING!!!//PORTING!!!
//WMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWMWM
#if BoardPciRes_SUPPORT
extern
EFI_STATUS
FirstBootSetVgaList (
    VOID
    );

extern
EFI_STATUS
IncrementVgaList (
    BOOLEAN  NewVga
    );

extern
EFI_STATUS
GetLastBootFailed (
    VOID
    );

extern
EFI_STATUS
SetLastBootFailed (
    UINTN    NumRootBridges,
    BOOLEAN  RbsFailed,
    BOOLEAN         DisableNextBoot,
    UINT8           Type
    );
#endif

/**
    Csp Function which will return PCI Segment Number for
    Chipsets which capable of decoding multiple PCI segments
    Parameters UINTN  HostNumber, UINTN RootNumber are ZERO based

    @param HostBridgeNumber
    @param RootBridgeNumber

    @retval VOID

    @note
  THIS FUNCTION MUST BE PORTED FOR MULTI-HOST SYSTEMS

  HOST bridge handle supports:
    - ResourceAllocation Protocol (REQUIRED);
    - RootHotplugControllerInitialization Protocol (OPTIONAL);
    - PciPlatform Protocol (OPTIONAL).
  ROOT bridge handle supports:
    -PciRootBridgeIo Protocol (REQUIRED).
**/

UINTN
HbCspGetPciSegment (
    IN UINTN HostBridgeNumber,
    IN UINTN RootBridgeNumber )
{
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//

//Usualy even server chipsets decodes only one PCI segment
//but if  chipsets has more than one SEGMENT we have to specify
//which HOST/ROOT(s) pare will have SEG=0; SEG=1 and so on...

    return 0; // this is for i945 chipset

//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
}


/**
    CSP Function invoked in PCI Host Bridge Protocol when NotifyPhase function is called
    with phase EfiPciHostBridgeBeginEnumeration

    @param ResAllocProtocol
    @param RbIoProtocolBuffer
    @param RbCount

    @retval EFI_STATUS return EFI status

    @note
  THIS FUNCTION MUST BE PORTED FOR MULTI-HOST SYSTEMS

  HOST bridge handle supports:
    - ResourceAllocation Protocol (REQUIRED);
    - RootHotplugControllerInitialization Protocol (OPTIONAL);
    - PciPlatform Protocol (OPTIONAL).
  ROOT bridge handle supports:
    -PciRootBridgeIo Protocol (REQUIRED).
**/

EFI_STATUS
HbNotifyCspBeforeEnumeration (
    IN EFI_PCI_HOST_BRIDGE_RESOURCE_ALLOCATION_PROTOCOL    *ResAllocProtocol,
    IN EFI_PCI_ROOT_BRIDGE_IO_PROTOCOL                     **RbIoProtocolBuffer,
    IN UINTN                                               RbCount )
{
    EFI_STATUS  Status=EFI_SUCCESS;
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//Any Additional Variables goes here


//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
    return Status;
}

/**
    CSP Function invoked in PCI Host Bridge Protocol when NotifyPhase function is called
    with phase EfiPciHostBridgeEndEnumeration

    @param ResAllocProtocol
    @param RbIoProtocolBuffer
    @param RbCount

    @retval EFI_STATUS return EFI status

    @note
  THIS FUNCTION MUST BE PORTED FOR MULTI-HOST SYSTEMS

  HOST bridge handle supports:
    - ResourceAllocation Protocol (REQUIRED);
    - RootHotplugControllerInitialization Protocol (OPTIONAL);
    - PciPlatform Protocol (OPTIONAL).
  ROOT bridge handle supports:
    -PciRootBridgeIo Protocol (REQUIRED).
**/

EFI_STATUS
HbNotifyCspEndEnumeration (
    IN EFI_PCI_HOST_BRIDGE_RESOURCE_ALLOCATION_PROTOCOL    *ResAllocProtocol,
    IN EFI_PCI_ROOT_BRIDGE_IO_PROTOCOL                     **RbIoProtocolBuffer,
    IN UINTN                                               RbCount )
{
    EFI_STATUS  Status=EFI_SUCCESS;
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//Any Additional Variables goes here

//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
    return Status;
}

/**
    This function will be invoked in PCI Host Bridge Driver
    before converting all Non Existant MMIO into PCI MMIO.
    In order to allow CSP code do aome none standard conversion.

    @param ImageHandle - this image Handle
    @param ControllerHandle - Controller(RB) Handle (Optional).

    @retval EFI_STATUS
            EFI_UNSUPPORTED - means use default MMIO convertion.
            EFI_SUCCESS - CSP code has been converted MMIO itself.
            ANYTHING else - ERROR.

    @note  Porting required if needed.
**/

EFI_STATUS
HbCspConvertMemoryMapIo (
    IN EFI_HANDLE  ImageHandle,
    IN EFI_HANDLE  ControllerHandle )
{
    EFI_STATUS  Status = EFI_UNSUPPORTED;

    // Any Additional Variables goes here
 //---------------------------------------

    return Status;
}

EFI_STATUS
HbCspConvertMemoryMapMmio (
    IN EFI_HANDLE  ImageHandle,
    IN EFI_HANDLE  ControllerHandle )
{
    EFI_STATUS  Status = EFI_UNSUPPORTED;

    // Any Additional Variables goes here
 //---------------------------------------

    return Status;
}

/**
    CSP Function invoked in PCI Host Bridge Protocol when NotifyPhase function is called
    with phase EfiPciHostBridgeBeginBusAllocation

    @param ResAllocProtocol
    @param RbIoProtocolBuffer
    @param RbCount

    @retval EFI_STATUS return EFI status

    @note
  THIS FUNCTION MUST BE PORTED FOR MULTI-HOST SYSTEMS

  HOST bridge handle supports:
    - ResourceAllocation Protocol (REQUIRED);
    - RootHotplugControllerInitialization Protocol (OPTIONAL);
    - PciPlatform Protocol (OPTIONAL).
  ROOT bridge handle supports:
    -PciRootBridgeIo Protocol (REQUIRED).

**/

EFI_STATUS
HbNotifyCspBeginBusAllocation (
    IN EFI_PCI_HOST_BRIDGE_RESOURCE_ALLOCATION_PROTOCOL  *ResAllocProtocol,
    IN EFI_PCI_ROOT_BRIDGE_IO_PROTOCOL                   **RbIoProtocolBuffer,
    IN UINTN                                             RbCount )
{
    EFI_STATUS  Status=EFI_SUCCESS;
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//Any Additional Variables goes here

//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
    return Status;
}

/**
    CSP Function invoked in PCI Host Bridge Protocol when NotifyPhase function is called
    with phase EfiPciHostBridgeEndBusAllocation

    @param ResAllocProtocol
    @param RbIoProtocolBuffer
    @param RbCount

    @retval EFI_STATUS return EFI status

    @note
  THIS FUNCTION MUST BE PORTED FOR MULTI-HOST SYSTEMS

  HOST bridge handle supports:
    - ResourceAllocation Protocol (REQUIRED);
    - RootHotplugControllerInitialization Protocol (OPTIONAL);
    - PciPlatform Protocol (OPTIONAL).
  ROOT bridge handle supports:
    -PciRootBridgeIo Protocol (REQUIRED).

**/

EFI_STATUS
HbNotifyCspEndBusAllocation (
    IN EFI_PCI_HOST_BRIDGE_RESOURCE_ALLOCATION_PROTOCOL    *ResAllocProtocol,
    IN EFI_PCI_ROOT_BRIDGE_IO_PROTOCOL                     **RbIoProtocolBuffer,
    IN UINTN                                               RbCount )
{
    EFI_STATUS  Status=EFI_SUCCESS;
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//Any Additional Variables goes here


//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
    return Status;
}


/**
    CSP Function invoked in PCI Host Bridge Protocol when NotifyPhase function is called
    with phase EfiPciHostBridgeBeginResourceAllocation


    @param ResAllocProtocol
    @param RbIoProtocolBuffer
    @param RbCount


    @retval EFI_STATUS return EFI status

    @note
  THIS FUNCTION MUST BE PORTED FOR MULTI-HOST SYSTEMS

  HOST bridge handle supports:
    - ResourceAllocation Protocol (REQUIRED);
    - RootHotplugControllerInitialization Protocol (OPTIONAL);
    - PciPlatform Protocol (OPTIONAL).
  ROOT bridge handle supports:
    -PciRootBridgeIo Protocol (REQUIRED).

**/

EFI_STATUS HbNotifyCspBeginResourceAllocation(
    IN EFI_PCI_HOST_BRIDGE_RESOURCE_ALLOCATION_PROTOCOL  *ResAllocProtocol,
    IN EFI_PCI_ROOT_BRIDGE_IO_PROTOCOL                   **RbIoProtocolBuffer,
    IN UINTN                                             RbCount )
{
    EFI_STATUS  Status=EFI_SUCCESS;
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//Any Additional Variables goes here

//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
    return Status;
}

/**
    CSP Function invoked in PCI Host Bridge Protocol when NotifyPhase function is called
    with phase EfiPciHostBridgeAllocateResources

    @param ResAllocProtocol
    @param RbIoProtocolBuffer
    @param RbCount

    @retval EFI_STATUS return EFI status

    @note
  THIS FUNCTION MUST BE PORTED FOR MULTI-HOST SYSTEMS

  HOST bridge handle supports:
    - ResourceAllocation Protocol (REQUIRED);
    - RootHotplugControllerInitialization Protocol (OPTIONAL);
    - PciPlatform Protocol (OPTIONAL).
  ROOT bridge handle supports:
    -PciRootBridgeIo Protocol (REQUIRED).
**/

EFI_STATUS
HbNotifyCspAllocateResources (
    IN EFI_PCI_HOST_BRIDGE_RESOURCE_ALLOCATION_PROTOCOL  *ResAllocProtocol,
    IN EFI_PCI_ROOT_BRIDGE_IO_PROTOCOL                   **RbIoProtocolBuffer,
    IN UINTN                                             RbCount )
{
    EFI_STATUS  Status=EFI_SUCCESS;
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//Any Additional Variables goes here

//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
    return Status;
}


/**
    CSP Function invoked in PCI Host Bridge Protocol when NotifyPhase function is called
    with phase EfiPciHostBridgeSetResources

    @param ResAllocProtocol
    @param RbIoProtocolBuffer
    @param RbCount

    @retval EFI_STATUS return EFI status

    @note
  THIS FUNCTION MUST BE PORTED FOR MULTI-HOST SYSTEMS

  HOST bridge handle supports:
    - ResourceAllocation Protocol (REQUIRED);
    - RootHotplugControllerInitialization Protocol (OPTIONAL);
    - PciPlatform Protocol (OPTIONAL).
  ROOT bridge handle supports:
    -PciRootBridgeIo Protocol (REQUIRED).
**/

EFI_STATUS
HbNotifyCspSetResources (
    IN EFI_PCI_HOST_BRIDGE_RESOURCE_ALLOCATION_PROTOCOL  *ResAllocProtocol,
    IN EFI_PCI_ROOT_BRIDGE_IO_PROTOCOL                   **RbIoProtocolBuffer,
    IN UINTN                                             RbCount )
{
    EFI_STATUS  Status=EFI_SUCCESS;
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//Any Additional Variables goes here

//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
    return Status;
}

/**
    CSP Function invoked in PCI Host Bridge Protocol when NotifyPhase function is called
    with phase EfiPciHostBridgeEndResourceAllocation

    @param ResAllocProtocol
    @param RbIoProtocolBuffer
    @param RbCount

    @retval EFI_STATUS return EFI status

    @note
  THIS FUNCTION MUST BE PORTED FOR MULTI-HOST SYSTEMS

  HOST bridge handle supports:
    - ResourceAllocation Protocol (REQUIRED);
    - RootHotplugControllerInitialization Protocol (OPTIONAL);
    - PciPlatform Protocol (OPTIONAL).
  ROOT bridge handle supports:
    -PciRootBridgeIo Protocol (REQUIRED).
**/

EFI_STATUS
HbNotifyCspEndResourceAllocation (
    IN EFI_PCI_HOST_BRIDGE_RESOURCE_ALLOCATION_PROTOCOL  *ResAllocProtocol,
    IN EFI_PCI_ROOT_BRIDGE_IO_PROTOCOL                   **RbIoProtocolBuffer,
    IN UINTN                                             RbCount )
{
    EFI_STATUS  Status=EFI_SUCCESS;
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//Any Additional Variables goes here

//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
    return Status;
}


/**
    CSP Function invoked in PCI Host Bridge Protocol StartBusEnumeration function
    It must prepare initial Bus ACPI Resource

    @param HostBrgData
    @param RootBrgData
    @param RootBrgIndex

    @retval EFI_STATUS return EFI status

    @note
  THIS FUNCTION MUST BE PORTED FOR MULTI-HOST SYSTEMS

  HOST bridge handle supports:
    - ResourceAllocation Protocol (REQUIRED);
    - RootHotplugControllerInitialization Protocol (OPTIONAL);
    - PciPlatform Protocol (OPTIONAL).
  ROOT bridge handle supports:
    -PciRootBridgeIo Protocol (REQUIRED).
**/

EFI_STATUS
HbCspStartBusEnumeration (
    IN PCI_HOST_BRG_DATA  *HostBrgData,
    IN PCI_ROOT_BRG_DATA  *RootBrgData,
    IN UINTN              RootBrgIndex )
{
    EFI_STATUS  Status=EFI_SUCCESS;

//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//Any Additional Variables goes here

//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
    return Status;
}


/**
    CSP Function invoked in PCI Host Bridge Protocol SubmitBusNumbers function

    @param HostBrgData
    @param RootBrgData
    @param RootBrgIndex

    @retval EFI_STATUS return EFI status

    @note
  THIS FUNCTION MUST BE PORTED FOR MULTI-HOST SYSTEMS

  HOST bridge handle supports:
    - ResourceAllocation Protocol (REQUIRED);
    - RootHotplugControllerInitialization Protocol (OPTIONAL);
    - PciPlatform Protocol (OPTIONAL).
  ROOT bridge handle supports:
    -PciRootBridgeIo Protocol (REQUIRED).
**/

EFI_STATUS
HbCspSetBusNnumbers (
    IN PCI_HOST_BRG_DATA  *HostBrgData,
    IN PCI_ROOT_BRG_DATA  *RootBrgData,
    IN UINTN              RootBrgIndex )
{
    EFI_STATUS  Status=EFI_SUCCESS;
    UINTN       NumberOfSockets;
    UINTN       SocketLoop;
    UINTN       NumberOfSegment = FabricTopologyGetNumberOfPciSegments();
    UINTN       SegmentLoop = 0;
    UINTN       NumberOfDies;
    UINTN       DieLoop;
    UINTN       NumberOfRootBridges;
    UINTN       RbLoop;
    UINTN       SystemFabricID = 0;
    UINTN       BusNumberBase, BusNumberLimit;
    UINTN       PhysicalRbNumber;
    UINTN       TotalNumberDie;
    UINTN       TotalNumberRootBridges;
    UINTN       ResIndex;
    BOOLEAN     BusOorStatus = PcdGetBool(AmiPcdPciOutOfResourcesStatus);
    AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL *FabricTopology;
    AMD_NBIO_CXL_SERVICES_PROTOCOL         *AmdNbioCxlServicesProtocol = NULL;
    FABRIC_RESOURCE_MANAGER_PROTOCOL       *AmdFabricResourceManager;
    FABRIC_ADDR_SPACE_SIZE                 SpaceStatus;
    UINTN                                  TotalBussesRequired;
    UINTN                                  ExtraBusses;
    UINTN                                  RemainderBusses;
    UINTN       SegmentRootBridges = 0;
    UINTN       SegmentRbStart = 0;
    UINTN       SegmentRbEnd = 0;

    // Locate FabricTopologyServicesProtocol
    Status = pBS->LocateProtocol (&gAmdFabricTopologyServices2ProtocolGuid, NULL, (VOID **) &FabricTopology);
    if(EFI_ERROR (Status)) return Status;

    // Number of sockets & rb's
    Status = FabricTopology->GetSystemInfo (FabricTopology, &NumberOfSockets, &TotalNumberDie, &TotalNumberRootBridges, NULL, NULL);
    if(EFI_ERROR (Status)) return Status;

    if(RootBrgIndex == 0) gBusResources.RbsUnaccountedFor = TotalNumberRootBridges - 1;

    // Setup NumaPciMap
    for (SocketLoop = 0; SocketLoop < NumberOfSockets; SocketLoop++) { //Loop sockets
        Status = FabricTopology->GetProcessorInfo (FabricTopology, SocketLoop, &NumberOfDies, NULL);
        if(EFI_ERROR (Status)) return Status;

        for (DieLoop = 0; DieLoop < NumberOfDies; DieLoop++) { //Loop dies
            Status = FabricTopology->GetDieInfo(FabricTopology, SocketLoop, DieLoop, &NumberOfRootBridges, NULL, NULL);
            if(EFI_ERROR (Status)) return Status;
            for(RbLoop = 0; RbLoop < NumberOfRootBridges; RbLoop++){ //Loop Rb's
                FabricTopology->GetRootBridgeInfo(
                                FabricTopology,
                                SocketLoop,
                                DieLoop,
                                RbLoop,             // Index
                                &SystemFabricID,    // *SystemFabricID
                                &BusNumberBase,     // *BusNumberBase
                                &BusNumberLimit,    // *BusNumberLimit
                                &PhysicalRbNumber,  // *PhysicalRootBridgeNumber
                                NULL,               // *HasFchDevice
                                NULL                // *HasSystemMgmtUnit
                                );

                if(BusNumberBase == RootBrgData->RbSdlData->Bus + RootBrgData->RbSdlData->PciSegment * 0x100){
                    gBusResources.RbBusRes[RootBrgIndex].BusBase = BusNumberBase;
                    gBusResources.RbBusRes[RootBrgIndex].BusLength = BusNumberLimit - BusNumberBase + 1;
                    gBusResources.RbBusRes[RootBrgIndex].PhysicalRbNumber = PhysicalRbNumber;
                    gBusResources.RbBusRes[RootBrgIndex].SocketNumber = SocketLoop;
                    break;
                }
            }
        }
    }

    for(ResIndex=0; ResIndex<RootBrgData->ResCount; ResIndex++){
        if(RootBrgData->RbRes[ResIndex]->Type == ASLRV_SPC_TYPE_BUS){
            gBusResources.RbBusRes[RootBrgIndex].BusRequired = RootBrgData->RbRes[ResIndex]->_LEN;
            gBusResources.RbBusRes[RootBrgIndex].OutOfResources = BusOorStatus;
            break;
        }
    }

    if(!gBusOorHit) gBusOorHit = BusOorStatus;

    if(gBusResources.RbsUnaccountedFor) {
        gBusResources.RbsUnaccountedFor--;

        if(gBusResources.RbBusRes[RootBrgIndex].OutOfResources){
            return EFI_OUT_OF_RESOURCES;
        } else {
            return EFI_SUCCESS;
        }
    }


    if(!gBusOorHit){
        return EFI_SUCCESS;
    }

    //Need to reallocate busses
    DEBUG((DEBUG_VERBOSE,"\n*********** PciRB: Bus OutOfResource Condition Occurred ***********\n\n"));

    Status = pBS->LocateProtocol (
                          &gAmdFabricResourceManagerServicesProtocolGuid,
                          NULL,
                          &AmdFabricResourceManager
                        );
    if (EFI_ERROR (Status)) {
        DEBUG ((DEBUG_ERROR, "Locate ResourceManagerServices - %r\n", Status));
        return Status;
    }

    if (gFabricResource == NULL) {
        Status = pBS->AllocatePool (
                      EfiBootServicesData,
                      sizeof (FABRIC_RESOURCE_FOR_EACH_RB),
                      (VOID *) &gFabricResource
                    );
        if (EFI_ERROR (Status)) {
            return Status;
        }
        pBS->SetMem (gFabricResource, sizeof (FABRIC_RESOURCE_FOR_EACH_RB), 0);

        Status = AmdFabricResourceManager->FabricGetAvailableResource (
                                          AmdFabricResourceManager,
                                          gFabricResource
                                         );
        if (EFI_ERROR (Status)) {
            return Status;
        }
        Status = pBS->LocateProtocol (&gAmdNbioCxlServicesProtocolGuid, NULL, &AmdNbioCxlServicesProtocol);
        if (!EFI_ERROR (Status)) {
            AmdNbioCxlServicesProtocol->GetCxlAvailableResources (AmdNbioCxlServicesProtocol, gFabricResource);
        }
    }

    for (SegmentLoop = 0; SegmentLoop < NumberOfSegment; SegmentLoop++) {
        SegmentRootBridges = TotalNumberRootBridges / NumberOfSegment;
        SegmentRbStart = SegmentRootBridges * SegmentLoop;
        SegmentRbEnd = SegmentRbStart + SegmentRootBridges;
    TotalBussesRequired = 0;
        for(RbLoop = SegmentRbStart; RbLoop < SegmentRbEnd; RbLoop++){
        TotalBussesRequired += gBusResources.RbBusRes[RbLoop].BusRequired;
    }

    if(TotalBussesRequired > 0x100){
        ERROR_CODE(DXE_PCI_BUS_OUT_OF_RESOURCES,EFI_ERROR_MAJOR);
        ASSERT_EFI_ERROR(EFI_OUT_OF_RESOURCES);
        EFI_DEADLOOP();
    }

    ExtraBusses = 0x100 - TotalBussesRequired;
        RemainderBusses = ExtraBusses % SegmentRootBridges;
    ExtraBusses -= RemainderBusses;

        for(RbLoop = SegmentRbStart; RbLoop < SegmentRbEnd; RbLoop++){
        SocketLoop = gBusResources.RbBusRes[RbLoop].SocketNumber;
        PhysicalRbNumber = gBusResources.RbBusRes[RbLoop].PhysicalRbNumber;

        gFabricResource->PciBusNumber[SocketLoop][PhysicalRbNumber] = \
                   (UINT16) ((ExtraBusses/SegmentRootBridges) + gBusResources.RbBusRes[RbLoop].BusRequired);

        if(RemainderBusses){
            gFabricResource->PciBusNumber[SocketLoop][PhysicalRbNumber]++;
            RemainderBusses--;
            }
        }
    }

    Status = AmdFabricResourceManager->FabricReallocateResourceForEachRb (
                        AmdFabricResourceManager,
                        gFabricResource,
                        &SpaceStatus
                       );

    if(!EFI_ERROR(Status)) gRT->ResetSystem(EfiResetWarm, EFI_SUCCESS, 0, NULL);

//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
    return Status;
}

/**
    CSP Function invoked in PCI Host Bridge Protocol SubmitResources function

    @param HostBrgData
    @param RootBrgData
    @param RootBrgIndex

    @retval EFI_STATUS return EFI status

    @note
  THIS FUNCTION MUST BE PORTED FOR MULTI-HOST SYSTEMS

  HOST bridge handle supports:
    - ResourceAllocation Protocol (REQUIRED);
    - RootHotplugControllerInitialization Protocol (OPTIONAL);
    - PciPlatform Protocol (OPTIONAL).
  ROOT bridge handle supports:
    -PciRootBridgeIo Protocol (REQUIRED).
**/

EFI_STATUS
HbCspSubmitResources (
    IN PCI_HOST_BRG_DATA  *HostBrgData,
    IN PCI_ROOT_BRG_DATA  *RootBrgData,
    IN UINTN              RootBrgIndex )
{
    EFI_STATUS  Status=EFI_SUCCESS;
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//Any Additional Variables goes here

//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
    return Status;
}

/**
**/
EFI_STATUS
GetRootbridgeLocation (
    IN     PCI_ROOT_BRG_DATA  *RootBrgData,
    IN     UINTN              RootBrgIndex,
    IN OUT UINTN              *SocketNo,
    IN OUT UINTN              *RbNo,
    IN OUT UINTN              *PhyRbNo
	)
{
    EFI_STATUS        Status;
    UINTN             i;
    UINTN             j;
    UINTN             k;
    UINTN             RbInSocket;
    UINTN             PhysicalRootBridge;
    UINTN             Sockets;
    UINTN             Dies;
    UINTN             RBs;
    UINTN             FabricId;
    UINTN             BusBase;
    UINTN             BusLimit;

    AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL   *AmdFabricTopologyServices;
    AMD_NBIO_CXL_SERVICES_PROTOCOL           *AmdNbioCxlServicesProtocol = NULL;

    DEBUG ((DEBUG_INFO, "NbPciCsp::GetRootbridgeLocation\n"));
    DEBUG ((DEBUG_INFO, "  RB #%x at Bus %x\n", RootBrgIndex, RootBrgData->RbSdlData->Bus));

    Status = pBS->LocateProtocol (
                    &gAmdFabricTopologyServices2ProtocolGuid,
                    NULL,
                    &AmdFabricTopologyServices
                  );

    if (EFI_ERROR (Status)) {
      DEBUG ((DEBUG_ERROR, "  Fabric Topology Protocol not installed\n"));
      return Status;
    }

    Status = AmdFabricTopologyServices->GetSystemInfo (
                                          AmdFabricTopologyServices,
                                          &Sockets,
                                          &Dies,
                                          &RBs,
                                          NULL,
                                          NULL
                                        );
    if (EFI_ERROR (Status)) {
      DEBUG ((DEBUG_ERROR, "AmdFabricTopologyServices : GetSystemInfo Failed - %r\n", Status));
      return Status;
    }
    Status = pBS->LocateProtocol (&gAmdNbioCxlServicesProtocolGuid, NULL, &AmdNbioCxlServicesProtocol);
    if (!EFI_ERROR (Status)) {
        UINT8 SocketId = 0, RBIdx = 0;
        Status = AmdNbioCxlServicesProtocol->GetCxlPortRBLocation (AmdNbioCxlServicesProtocol, (UINT8)RootBrgData->RbSdlData->PciSegment ,RootBrgData->RbSdlData->Bus, &SocketId, &RBIdx);
        if (!EFI_ERROR (Status)) {
            *SocketNo = SocketId;
            *RbNo     = RBIdx;
            DEBUG ((DEBUG_INFO, " CXL RB %X matched to [SKT #%X] at Seg 0x%X Bus 0x%X\n", *RbNo, *SocketNo, RootBrgData->RbSdlData->PciSegment, RootBrgData->RbSdlData->Bus));
            // CXL not relate resource setting need for IOMMU, APIC, IO, MMIO resource. Return unsupport.
            return EFI_SUCCESS;
        }
    }

    for (i = 0; i < Sockets; i++) {
      RbInSocket = 0;
      Status = AmdFabricTopologyServices->GetProcessorInfo (
                                            AmdFabricTopologyServices,
                                            i,
                                            &Dies,
                                            NULL
                                          );
      if (EFI_ERROR (Status)) {
        DEBUG ((DEBUG_ERROR, "AmdFabricTopologyServices : GetProcessorInfo Failed - %r\n", Status));
        return Status;
      }
      for (j = 0; j < Dies; j++) {
        Status = AmdFabricTopologyServices->GetDieInfo (
                                              AmdFabricTopologyServices,
                                              i,
                                              j,
                                              &RBs,
                                              NULL,
                                              NULL
                                            );
        if (EFI_ERROR (Status)) {
            DEBUG ((DEBUG_ERROR, "AmdFabricTopologyServices : GetDieInfo Failed - %r\n", Status));
            return Status;
        }
        for (k = 0; k < RBs; k++) {
          Status = AmdFabricTopologyServices->GetRootBridgeInfo (
                                                AmdFabricTopologyServices,
                                                i,
                                                j,
                                                k,
                                                &FabricId,
                                                &BusBase,
                                                &BusLimit,
                                                &PhysicalRootBridge,
                                                NULL,
                                                NULL
                                              );
          if (EFI_ERROR (Status)) {
            DEBUG ((DEBUG_ERROR, "AmdFabricTopologyServices : GetRootBridgeInfo Failed - %r\n", Status));
            return Status;
          }
          if ((RootBrgData->RbSdlData->Bus == BusBase % 0x100) && (RootBrgData->RbSdlData->PciSegment == BusBase / 0x100)) {
            DEBUG ((DEBUG_INFO, "  RB %X matched to [SKT #%X:Die #%X:PhRB #%X] at Bus 0x%X - 0x%X\n",
                    RootBrgIndex,
                    i,
                    j,
                    PhysicalRootBridge,
                    BusBase,
                    BusLimit
                  ));

            *SocketNo = i;
            *RbNo     = RbInSocket;
            *PhyRbNo  = PhysicalRootBridge;
            return EFI_SUCCESS;
          }
          RbInSocket++;
        }
      }
    }

    return EFI_NOT_FOUND;
}

EFI_STATUS
AllocateNonPciResource (
  IN      PCI_ROOT_BRG_DATA   *RootBrgData,
  IN      UINTN               SocketNo,
  IN      UINTN               RbNo,
  IN OUT  UINT64              *BaseAddress,
  IN      UINT64              Length,
  IN      UINT64              BitMask )
{
    EFI_STATUS        Status;
    DXE_SERVICES      *Dxe;
    UINTN             Alignment;
    UINT8             BusNo;

    FABRIC_TARGET                     MmioTarget;
    FABRIC_MMIO_ATTRIBUTE             MmioAttr;
    FABRIC_RESOURCE_MANAGER_PROTOCOL  *AmdFabricResourceManager;

    DEBUG ((DEBUG_INFO, "NbPciCsp::AllocateNonPciResource Entry\n"));

    //
    // Get the DXE services table
    //
    Dxe = gDS;

    Status = pBS->LocateProtocol (
                    &gAmdFabricResourceManagerServicesProtocolGuid,
                    NULL,
                    &AmdFabricResourceManager
                  );
    if (EFI_ERROR (Status)) {
      return Status;
    }

    BusNo = RootBrgData->RbSdlData->Bus;

    MmioTarget.PciSegNum  = (UINT16)RootBrgData->RbSdlData->PciSegment;
    MmioTarget.PciBusNum  = (UINT16)BusNo;
    MmioTarget.SocketNum  = (UINT16)SocketNo;
    MmioTarget.RbNum      = (UINT16)RbNo;
    MmioTarget.TgtType    = TARGET_RB;

    MmioAttr.MmioType     = NON_PCI_DEVICE_BELOW_4G;
    MmioAttr.NonPosted    = 0;
    MmioAttr.ReadEnable   = 1;
    MmioAttr.WriteEnable  = 1;

    Status = AmdFabricResourceManager->FabricAllocateMmio (
                                        AmdFabricResourceManager,
                                        BaseAddress,
                                        &Length,
                                        BitMask,
                                        MmioTarget,
                                        &MmioAttr
                                      );
    if(EFI_ERROR(Status)) {
      DEBUG ((DEBUG_ERROR,"  Fabric Resource Allocation Failed\n"));
      return Status;
    }

    switch (BitMask) {
      case ALIGN_1M:
        Alignment = 20;
        break;
      case ALIGN_512K:
        Alignment = 19;
        break;
      case ALIGN_64K:
      default:
        Alignment = 16;
        break;
    }

    Status = Dxe->AllocateMemorySpace(
                    EfiGcdAllocateAddress,
                    EfiGcdMemoryTypeMemoryMappedIo,
                    Alignment,
                    Length,
                    BaseAddress,
                    RootBrgData->ImageHandle,
                    RootBrgData->RbHandle
                  );
    if (EFI_ERROR(Status)) {
      DEBUG ((DEBUG_ERROR,"  DXE::Memory Allocation Failed - %r\n", Status));
      return Status;
    }

    Status = Dxe->SetMemorySpaceAttributes(
                    *BaseAddress,
                    Length,
                    EFI_MEMORY_UC|EFI_MEMORY_RUNTIME
                  );
    if (EFI_ERROR(Status)) {
      DEBUG ((DEBUG_ERROR,"  DXE::Setting Memory Attribute Failed - %r\n", Status));
    }

    DEBUG ((DEBUG_INFO, "NbPciCsp::AllocateNonPciResource Exit\n"));

    return Status;
}

EFI_STATUS
SupportedAmdSoc()
{
    EFI_STATUS                    Status;
    AMD_SOC_LOGICAL_ID_PROTOCOL   *SocLogicalId;
    SOC_LOGICAL_ID                LogicalId;

    Status = gBS->LocateProtocol (
                    &gAmdSocLogicalIdProtocolGuid,
                    NULL,
                    (VOID **) &SocLogicalId
                  );
    if (EFI_ERROR(Status)) {
        DEBUG ((DEBUG_ERROR, "AmdSocLogicalIdProtocol not found!\n"));
        return Status;
    }
    Status = SocLogicalId->GetLogicalIdOnCurrentCore (
                            SocLogicalId,
                            &LogicalId
                           );
    if (EFI_ERROR(Status)) {
        DEBUG ((DEBUG_ERROR, "GetLogicalId - %r\n", Status));
        return Status;
    }
    
    if (LogicalId.Family & (AMD_FAMILY_1A_BRH | AMD_FAMILY_1A_BRHD)) {    
        return EFI_SUCCESS;
    } else {
        return EFI_UNSUPPORTED;
    }
}

EFI_STATUS
AllocateForIommu (
    IN  PCI_ROOT_BRG_DATA   *RootBrgData,
    IN  UINTN               SocketNo,
    IN  UINTN               RbNo )
{
    EFI_STATUS        Status;
    UINT64            BaseAddress = 0;
    UINT32            BaseAddReg;
    UINT32            Value;



    DEBUG ((DEBUG_INFO, "NbPciCsp::AllocateForIommu Entry\n"));

    Status = AllocateNonPciResource (
              RootBrgData,
              SocketNo,
              RbNo,
              &BaseAddress,
              (UINT64)NBIO_IOMMU_LENGTH,
              (UINT64)ALIGN_512K
             );

    if (EFI_ERROR(Status)) {
        DEBUG ((DEBUG_ERROR, "NbPciCsp::AllocateForIommu AllocateNonPciResource - %r\n", Status));
    }
    
    if (BaseAddress == 0) {
        ASSERT(FALSE);
        return EFI_NOT_FOUND;
    }

    DEBUG ((DEBUG_INFO, "  IOMMU BAR = 0x%lX\n", BaseAddress));

    // Program base address
    // IOMMU_CAP_BASE_LO
    if (EFI_ERROR(SupportedAmdSoc())) {
      DEBUG ((DEBUG_ERROR, "NbPciCsp - AllocateForIommu: Unsupported SoC!\n"));
      ASSERT (FALSE);
      return EFI_UNSUPPORTED;
    } else {
      switch(RbNo)
      {
      case 0:
          BaseAddReg = (UINT32) 0x13F00044;
          break;
      case 2:
          BaseAddReg = (UINT32) 0x14000044;
          break;
      case 4:
          BaseAddReg = (UINT32) 0x14100044;
          break;
      case 6:
          BaseAddReg = (UINT32) 0x14200044;
          break;

      default:
          break;
      }
    }
    Value = BaseAddress & 0xFFF80000;   // [31:19]
    SmnRegisterWriteS (RootBrgData->RbSdlData->PciSegment, RootBrgData->RbSdlData->Bus, BaseAddReg, &Value, 0);
    // IOMMU_CAP_BASE_HI
    BaseAddReg += 4;
    Value = (UINT32) (BaseAddress >> 32);
    SmnRegisterWriteS (RootBrgData->RbSdlData->PciSegment, RootBrgData->RbSdlData->Bus, BaseAddReg, &Value, 0);

    DEBUG ((DEBUG_INFO, "PciCsp::AllocateForIommu Exit\n"));

    return EFI_SUCCESS;
}

EFI_STATUS
AllocateForNbIoApic (
    IN  PCI_ROOT_BRG_DATA   *RootBrgData,
    IN  UINTN               RootBrgIndex,
    IN  UINTN               SocketNo,
    IN  UINTN               RbNo,
    IN  UINTN               PhyRbNo
)
{
    EFI_STATUS        Status;
    UINT64            BaseAddress = 0;
    UINT32            BaseAddReg;
    UINT32            ApicIdReg;
    UINT32            IoApicFeaturesReg;
    UINT32            Value;
    UINT32            i;
    UINT32            Count;
    UINT32            IoApicId;
    UINT32            Target;
    UINTN             Dies;
    UINTN             RBs;
    AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL *AmdFabricTopologyServices;
    AMD_NBIO_SERVICES_PROTOCOL             *AmdNbioServices = NULL;
    UINTN             NbioRbId = 0;

    AMD_SOC_LOGICAL_ID_PROTOCOL   *SocLogicalId;
    SOC_LOGICAL_ID                LogicalId;

    AMI_BOARD_INFO2_PROTOCOL      *AmiBoardInfo2Protocol = NULL;
    AMI_SDL_IO_APIC_INFO    temp[17];

    DEBUG ((DEBUG_INFO, "NbPciCsp::AllocateForNbIoApic Entry\n"));

    Status = AllocateNonPciResource (
              RootBrgData,
              SocketNo,
              RbNo,
              &BaseAddress,
              (UINT64)NBIO_IOAPIC_LENGTH,
              (UINT64)ALIGN_64K
             );
    if (EFI_ERROR(Status)) {
        DEBUG ((DEBUG_ERROR, "NbPciCsp::AllocateForNbIoApic AllocateNonPciResource- %r\n", Status));
    }

    if (BaseAddress == 0) {
        ASSERT(FALSE);
        return EFI_NOT_FOUND;
    }

    //IoApicId = (UINT32) (RBC0_IOAPIC_ID + RootBrgIndex);
    IoApicId = (UINT32) (RBC0_IOAPIC_ID + RbNo + (SocketNo * 8));
    Status = gBS->LocateProtocol(
                    &gAmiBoardInfo2ProtocolGuid,
                    NULL,
                    &AmiBoardInfo2Protocol
                  );
    if(EFI_ERROR(Status)) {
      DEBUG ((DEBUG_ERROR, "  Get AmiBoardInfo2Protocol failed\n"));
      return Status;
    }

    Count = AmiBoardInfo2Protocol->ApicBrdData->IoApicCount;
    // Initiate all IO APIC base address as invalid entry

    if (RootBrgIndex == 0) {

        // re-order entries
        // Update Correct APICID one by one to AMI_SDL_IO_APIC_INFO
        for(i = 0; i < Count; i++) {
            Target = AmiBoardInfo2Protocol->ApicBrdData->IoApicInfo[i].ApicIdBuild;
            if(Target == SB_IOAPIC_ID){
                Target = Count - 1; //for SB, put at end
            }else{
                Target = Target - RBC0_IOAPIC_ID;    //NBIO IOAPICs
                //if(Target > (Count - 2)) Target = Target - 4; //WA for 2P A0
            }

            temp[Target].ApicAddress = AmiBoardInfo2Protocol->ApicBrdData->IoApicInfo[i].ApicAddress;
            temp[Target].ApicIdBuild = AmiBoardInfo2Protocol->ApicBrdData->IoApicInfo[i].ApicIdBuild;
            temp[Target].ApicIdRun = AmiBoardInfo2Protocol->ApicBrdData->IoApicInfo[i].ApicIdRun;
            temp[Target].IntVectorBase = AmiBoardInfo2Protocol->ApicBrdData->IoApicInfo[i].IntVectorBase;
            temp[Target].Reserved = AmiBoardInfo2Protocol->ApicBrdData->IoApicInfo[i].Reserved;
        }

        // overwrite build time
        for(i = 0; i < Count; i++) {
            AmiBoardInfo2Protocol->ApicBrdData->IoApicInfo[i].ApicAddress = temp[i].ApicAddress;
            AmiBoardInfo2Protocol->ApicBrdData->IoApicInfo[i].ApicIdBuild = temp[i].ApicIdBuild;
            AmiBoardInfo2Protocol->ApicBrdData->IoApicInfo[i].ApicIdRun = temp[i].ApicIdRun;
            AmiBoardInfo2Protocol->ApicBrdData->IoApicInfo[i].IntVectorBase = temp[i].IntVectorBase;
            AmiBoardInfo2Protocol->ApicBrdData->IoApicInfo[i].Reserved = temp[i].Reserved;
        }

        for(i = 0; i < Count-1; i++) {  //don't overwrite SB apic address
            AmiBoardInfo2Protocol->ApicBrdData->IoApicInfo[i].ApicAddress = 0xDEADBEEF;
        }
    }

    // Update Correct APICID one by one to AMI_SDL_IO_APIC_INFO
    for(i = 0; i < Count; i++) {
        // Find the IOAPIC for this RB
        if (AmiBoardInfo2Protocol->ApicBrdData->IoApicInfo[i].ApicIdBuild == IoApicId) {
            AmiBoardInfo2Protocol->ApicBrdData->IoApicInfo[i].ApicAddress = BaseAddress;
            DEBUG ((DEBUG_INFO, "  IOAPIC ID = 0x%x, BAR = 0x%x\n",
                    IoApicId,
                    BaseAddress
                  ));
            break;
        }
    }

    if (EFI_ERROR(SupportedAmdSoc())) {
        DEBUG ((DEBUG_ERROR, "NbPciCsp - AllocateForNbIoApic: Unsupported SoC!\n"));
        ASSERT (FALSE);
        return EFI_UNSUPPORTED;
    }
    Status = pBS->LocateProtocol (
                    &gAmdFabricTopologyServices2ProtocolGuid,
                    NULL,
                    &AmdFabricTopologyServices
                  );

    if (EFI_ERROR (Status)) {
      DEBUG ((DEBUG_ERROR, "  Fabric Topology Protocol not installed\n"));
      return Status;
    }

    Status = AmdFabricTopologyServices->GetSystemInfo (
                                          AmdFabricTopologyServices,
                                          &mNumSoCPresent,
                                          &Dies,
                                          &RBs,
                                          NULL,
                                          NULL
                                          );

    if(Status != EFI_SUCCESS) return Status;


    Status = pBS->LocateProtocol (&gAmdNbioServicesProtocolGuid, NULL, &AmdNbioServices);
    if(EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR, "  Get AmdNbioServicesProtocol Failed - Status = %r\n", Status));
        return Status;
    }

    Status = AmdNbioServices->GetRootBridgeIndex (AmdNbioServices, SocketNo, &RbNo, &NbioRbId);
    if(EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR, "  GetRootBridgeIndex Failed - Status = %r\n", Status));
        return Status;
    }

    Status = gBS->LocateProtocol (
                    &gAmdSocLogicalIdProtocolGuid,
                    NULL,
                    (VOID **) &SocLogicalId
                  );

    Status = SocLogicalId->GetLogicalIdOnCurrentCore (
                            SocLogicalId,
                            &LogicalId
                           );

    // Program base address
    // IOAPIC_BASE_ADDR_LO
      switch(NbioRbId)
      {
      case 0:
          BaseAddReg = (UINT32) 0x13B102F0;
          ApicIdReg = (UINT32) 0x2801000;
          break;
      case 1:
          BaseAddReg = (UINT32) 0x13C102F0;
          ApicIdReg = (UINT32) 0x2901000;
          break;
      case 2:
          BaseAddReg = (UINT32) 0x13D102F0;
          ApicIdReg = (UINT32) 0x2A01000;
          break;
      case 3:
          BaseAddReg = (UINT32) 0x13E102F0;
          ApicIdReg = (UINT32) 0x2B01000;
          break;

      case 4:
          BaseAddReg = (UINT32) 0x1D4102F0;
          ApicIdReg = (UINT32) 0x1D001000;
          break;
      case 5:
          BaseAddReg = (UINT32) 0x1D5102F0;
          ApicIdReg = (UINT32) 0x1D101000;
          break;
      case 6:
          BaseAddReg = (UINT32) 0x1D6102F0;
          ApicIdReg = (UINT32) 0x1D201000;
          break;
      case 7:
          BaseAddReg = (UINT32) 0x1D7102F0;
          ApicIdReg = (UINT32) 0x1D301000;
          break;

      default:
          break;
      }

    IoApicFeaturesReg = (UINT32) (0x14300000 + (RbNo << 20));

    Value = (UINT32) (BaseAddress & 0xFFFFFF00);    // [31:8]
    Value |= 1;                                     // IOAPIC_MMIO_EN
    SmnRegisterWriteS (RootBrgData->RbSdlData->PciSegment, RootBrgData->RbSdlData->Bus, BaseAddReg, &Value, 0);
    // IOAPIC_BASE_ADDR_HI
    BaseAddReg += 4;
    Value = (UINT32) ((BaseAddress >> 32) & 0xFFFF);
    SmnRegisterWriteS (RootBrgData->RbSdlData->PciSegment, RootBrgData->RbSdlData->Bus, BaseAddReg, &Value, 0);

    // Program APIC ID
    Value = IoApicId << 24;
    SmnRegisterWriteS (RootBrgData->RbSdlData->PciSegment, RootBrgData->RbSdlData->Bus, ApicIdReg, &Value, 0);

    // Program IOAPIC FEATURES_ENABLED
    SmnRegisterReadS (RootBrgData->RbSdlData->PciSegment, RootBrgData->RbSdlData->Bus, IoApicFeaturesReg, &Value);
    if((RbNo == 0x2) && (SocketNo == 0)){
        Value |= 0x10;  // PCI0
    } else {
        Value |= 0x30;
    }
    SmnRegisterWriteS (RootBrgData->RbSdlData->PciSegment, RootBrgData->RbSdlData->Bus, IoApicFeaturesReg, &Value, 0);
    
    DEBUG ((DEBUG_INFO, "PciCsp::AllocateForNbIoApic Exit\n"));

    return EFI_SUCCESS;
}

/**
  Adjust existing ditribution of IO space per Root bridge

  Attempt to increase existing allocation of IO space in particular Root Bridge.
  If success, IO resource updated in gFabricResource.

  @param  SocketNo  Socket number
  @param  RbNo      Root Bridge physical number
  @param  RequiredLength Length of IO space requested by PCI driver

  @retval EFI_SUCCESS            Successfully redistributed IO
  @retval EFI_OUT_OF_RESOURCES   There is no enough resources for IO request
**/

EFI_STATUS
AdjustIo (
    IN UINTN  SocketNo,
    IN UINTN  RbNo,
    IN UINTN  RequiredLength )
{
    EFI_STATUS  Status = EFI_SUCCESS;
    UINT64      ProcessedSpace;
    UINT64      AllocatedSpace;
    UINT64      NeededSpace;
    UINTN       socket;
    UINTN       rb, PhyRb;
    UINT64      NextBase;
    
    // Find current requested RB in map
    for(socket = 0; socket < MAX_SOCKETS_SUPPORTED; socket++){
        for(rb = 0; rb < MAX_RBS_PER_SOCKET; rb++){
            if((gIoMap[socket][rb].PhyRbNo == RbNo) && (SocketNo == socket)) {
                gIoMap[SocketNo][rb].Processed = TRUE;
                gIoMap[SocketNo][rb].ReqSize = RequiredLength;
                // check if request fit in existing resource
                if(RequiredLength > gIoMap[SocketNo][rb].Size){
                    // resources not enough for current RB, activate reallocation path
                    gIoMap[SocketNo][rb].Size = RequiredLength;
                    mIoReallocationReq = TRUE;
                }
                break;
            }
        }
    }

    if(mIoReallocationReq){
        // Reallocation path
    
        // Calculate allocated space, and already processed space,
        // DF always assign 0x1000 starting from 0x0000 without reflecting it 
        // in gFabricResource to Primary RB
        AllocatedSpace = 0x1000;
        ProcessedSpace = 0x1000;
        for(socket = 0; socket < MAX_SOCKETS_SUPPORTED; socket++){
            for(rb = 0; rb < MAX_RBS_PER_SOCKET; rb++){
                //if(gIoMap[socket][rb].Processed) ProcessedSpace += gIoMap[socket][rb].Size;
                if(gIoMap[socket][rb].Processed) ProcessedSpace += gIoMap[socket][rb].ReqSize;
                //AllocatedSpace += gIoMap[socket][rb].Size;
                AllocatedSpace += gIoMap[socket][rb].ReqSize;
            }
        }
    
        if(ProcessedSpace > 0x10000){
#if BoardPciRes_SUPPORT                   
            Status = GetLastBootFailed();
            if(gLastBootFailedVar.DisableNextBoot == TRUE){
                //If we already disabled bridges try new VGA device to see if it passed allocation
                IncrementVgaList(TRUE);
                DEBUG((DEBUG_INFO, "PciOOR: Allocation failed. Attempting to reboot with new VGA\n"));
            } else {
                //If this is our first boot then try first VGA device
                IncrementVgaList(FALSE);
                Status = FirstBootSetVgaList();
                DEBUG((DEBUG_INFO, "PciOOR: Allocation failed. Rebooting and enabling first VGA device\n"));
            }
            SetLastBootFailed(MAX_ROOT_BRIDGE_COUNT, TRUE, TRUE, ASLRV_SPC_TYPE_IO);
        
            gRT->ResetSystem(EfiResetWarm, EFI_SUCCESS, 0, NULL);
#endif                    
            return EFI_OUT_OF_RESOURCES;
        }
    
        // Space available, just need to shrink non-processed RB's
        if(AllocatedSpace > 0x10000) {
            NeededSpace = AllocatedSpace - 0x10000;
            for(socket = 0; socket < MAX_SOCKETS_SUPPORTED; socket++){
                for(rb = 0; rb < MAX_RBS_PER_SOCKET; rb++){
                    if (!gIoMap[socket][rb].Processed){
                        if(gIoMap[socket][rb].ReqSize > NeededSpace) {
                            gIoMap[socket][rb].ReqSize -= NeededSpace;
                            NeededSpace = 0;
                        } else {
                            NeededSpace -= gIoMap[socket][rb].ReqSize;
                            gIoMap[socket][rb].ReqSize = 0;
                        }
                    }
                }
            }
        }
    
        // Set base values
        NextBase = 0x1000;
        for(socket = 0; socket < MAX_SOCKETS_SUPPORTED; socket++){
            for(rb = 0; rb < MAX_RBS_PER_SOCKET; rb++){
                gIoMap[socket][rb].Base = NextBase;
                NextBase = gIoMap[socket][rb].Base + gIoMap[socket][rb].ReqSize;
            }
        }
    
        // update gFabricResource
        for(socket = 0; socket < MAX_SOCKETS_SUPPORTED; socket++){
            for(rb = 0; rb < MAX_RBS_PER_SOCKET; rb++){
                PhyRb = gIoMap[socket][rb].PhyRbNo;
                gFabricResource->IO[socket][PhyRb].Base = gIoMap[socket][rb].Base;
                gFabricResource->IO[socket][PhyRb].Size = gIoMap[socket][rb].ReqSize;
            }
        }
    } // End Reallocation path

    return Status;
}

/**
  Allocate IO space per Root bridge IO resource

  @param  RootBrgData pointer to PCI_ROOT_BRG_DATA
  @param  SocketNo    socket number
  @param  RbNo        Root Bridge physical number

  @retval EFI_SUCCESS Successfully allocated IO
  @retval EFI_ERROR   There is no enough resources for IO request or other error
**/

EFI_STATUS
AllocateIoResource (
    IN  PCI_ROOT_BRG_DATA   *RootBrgData,
    IN  UINTN               SocketNo,
    IN  UINTN               RbNo )
{
    EFI_STATUS      Status;
    UINTN           i;
    UINT64          Base;
    UINT64          Size;
    UINT64          Address;
    DXE_SERVICES    *dxe;
    ASLR_QWORD_ASD  *res;
    UINTN           Dies;
    UINTN           RBs;
    UINTN           socket;
    UINTN           rb;
    FABRIC_ADDR_SPACE_SIZE            SpaceStatus;
    FABRIC_RESOURCE_MANAGER_PROTOCOL  *AmdFabricResourceManager;
    AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL   *AmdFabricTopologyServices;
    
    DEBUG ((DEBUG_INFO, "NbPciCsp::AllocateIoResource Entry\n"));
    Status = LibGetDxeSvcTbl(&dxe);
    if(EFI_ERROR(Status)) {
        return Status;
    }

    if(!gIoMapInitialized){
        UINTN     j;
        BOOLEAN   swapped;
        IO_RES_RB TempRes;
        // Get current IO distribution into temporary structure
        for(socket = 0; socket < MAX_SOCKETS_SUPPORTED; socket++){
            for(rb = 0; rb < MAX_RBS_PER_SOCKET; rb++){
                gIoMap[socket][rb].Base = gFabricResource->IO[socket][rb].Base;
                gIoMap[socket][rb].Size = gFabricResource->IO[socket][rb].Size;
                gIoMap[socket][rb].PhyRbNo = rb;
                gIoMap[socket][rb].ReqSize = 0;
                gIoMap[socket][rb].Processed = FALSE;
            }
        }

        Status = gBS->LocateProtocol (
                            &gAmdFabricTopologyServices2ProtocolGuid,
                            NULL,
                            &AmdFabricTopologyServices
                          );
        if(Status != EFI_SUCCESS) return Status;

        Status = AmdFabricTopologyServices->GetSystemInfo (
                                                      AmdFabricTopologyServices,
                                                      &mNumSoCPresent,
                                                      &Dies,
                                                      &RBs,
                                                      NULL,
                                                      NULL
                                                    );

        if(Status != EFI_SUCCESS) return Status;
        if(mNumSoCPresent == 2){
            mRbsPerSocket = RBs/2;
        }else{
            mRbsPerSocket = RBs;
        }
        // for relocation need to sort entries
        for(socket = 0; socket < mNumSoCPresent; socket++){
            for( i =0; i < mRbsPerSocket -1; i++){
                swapped = FALSE;
                for(j = 0; j < mRbsPerSocket - i - 1; j++){
                    if(gIoMap[socket][j].Base > gIoMap[socket][j + 1].Base){
                        // swap
                        TempRes.Base = gIoMap[socket][j + 1].Base;
                        TempRes.Size = gIoMap[socket][j + 1].Size;
                        TempRes.PhyRbNo = gIoMap[socket][j + 1].PhyRbNo;
                        
                        gIoMap[socket][j + 1].Base = gIoMap[socket][j].Base;
                        gIoMap[socket][j + 1].Size = gIoMap[socket][j].Size;
                        gIoMap[socket][j + 1].PhyRbNo = gIoMap[socket][j].PhyRbNo;
                        
                        gIoMap[socket][j].Base = TempRes.Base;
                        gIoMap[socket][j].Size = TempRes.Size;
                        gIoMap[socket][j].PhyRbNo = TempRes.PhyRbNo;
                        
                        swapped = TRUE;
                    }
                    // if no elements swapped - sorting done or not needed, break
                    if(swapped == FALSE)  break;
                }
            }
        }
        gIoMapInitialized = TRUE;
    }

    for (i = 0; i < RootBrgData->ResCount; i++) {
        res=RootBrgData->RbRes[i];
        if(res->Type == ASLRV_SPC_TYPE_IO) {
            Status = AdjustIo(SocketNo, RbNo, res->_LEN);
            if(Status == EFI_SUCCESS){
                // update Address with new value
                Address = gFabricResource->IO[SocketNo][RbNo].Base + gFabricResource->IO[SocketNo][RbNo].Size;
            } else {
                return Status;
            }
            RootBrgData->AcpiRbRes[raIo].Gra = res->_MAX;

            Status = dxe->AllocateIoSpace(
                        EfiGcdAllocateMaxAddressSearchTopDown,
                        EfiGcdIoTypeIo,
                        AlignFromGra(0xFFF),
                        res->_LEN,
                        &Address,
                        RootBrgData->ImageHandle,
                        RootBrgData->RbHandle
                        );
            if(EFI_ERROR(Status) && (Status != EFI_NOT_FOUND)) {
                DEBUG ((DEBUG_ERROR, "  IO Allocation Failed: _LEN=%X _GRA=%X\n",
                        res->_LEN, res->_MAX));
                return Status;
            } else {
                // NOT_FOUND could be in case when bigger than requested region allocated in previous RB
                DEBUG ((DEBUG_INFO, "  IO Allocation Base = 0x%X, Size = 0x%X\n",
                        Address, res->_LEN));
                res->_MIN = Address;
                Status = EFI_SUCCESS;
            }
        }
    }

    Base = gFabricResource->IO[SocketNo][RbNo].Base;
    Size = gFabricResource->IO[SocketNo][RbNo].Size;

    // Fill in ACPI DATA
    RootBrgData->AcpiRbRes[raIo].Min = Base;
    RootBrgData->AcpiRbRes[raIo].Max = Base + Size - 1;
    RootBrgData->AcpiRbRes[raIo].Len = Size;
    RootBrgData->AcpiRbRes[raIo].AddrUsed = Base + Size;
    RootBrgData->AcpiRbRes[raIo].AllocType = EfiGcdAllocateAddress;
    
    DEBUG ((DEBUG_INFO, "  ACPI_IO Base = 0x%x, Length =0x%x\n", Base, Size));
    if((SocketNo == (mNumSoCPresent - 1))&&(RbNo == 0)){ // Last RB
        if(mIoReallocationReq){
            // Program chipset by calling FabricResoureManager
            Status = gBS->LocateProtocol (
                                  &gAmdFabricResourceManagerServicesProtocolGuid,
                                  NULL,
                                  &AmdFabricResourceManager
                                );
            if (EFI_ERROR (Status)) {
                return EFI_OUT_OF_RESOURCES;
            }
        
            Status = AmdFabricResourceManager->FabricReallocateResourceForEachRb (
                                                    AmdFabricResourceManager,
                                                    gFabricResource,
                                                    &SpaceStatus
                                                   );
        }
     }// Last RB

    DEBUG ((DEBUG_INFO, "NbPciCsp::AllocateIoResource Exit\n"));
    return  Status;
}

EFI_STATUS
AllocateMmio64Resource (
    IN  PCI_ROOT_BRG_DATA   *RootBrgData,
    IN  UINTN               SocketNo,
    IN  UINTN               RbNo )
{
    EFI_STATUS        Status;
    UINTN             i;
    UINT64            Base;
    UINT64            Size;
    UINT64            Address;
    DXE_SERVICES      *dxe;
    ASLR_QWORD_ASD    *res;
  
    DEBUG ((DEBUG_INFO, "NbPciCsp::AllocateMmio64Resource Entry\n"));
  
    Status = LibGetDxeSvcTbl(&dxe);
    if(EFI_ERROR(Status)) {
        return Status;
    }

    if (gFabricResource->NonPrefetchableMmioSizeAbove4G[SocketNo][RbNo].Size != 0) {
        Base = gFabricResource->NonPrefetchableMmioSizeAbove4G[SocketNo][RbNo].Base;

        if (gFabricResource->PrefetchableMmioSizeAbove4G[SocketNo][RbNo].Size != 0 &&
            Base > gFabricResource->PrefetchableMmioSizeAbove4G[SocketNo][RbNo].Base) {
            Base = gFabricResource->PrefetchableMmioSizeAbove4G[SocketNo][RbNo].Base;
        }
    } else {  // NonPrefetchableMmioSizeAbove4G == 0
        Base = gFabricResource->PrefetchableMmioSizeAbove4G[SocketNo][RbNo].Base;
    }

    Size = gFabricResource->NonPrefetchableMmioSizeAbove4G[SocketNo][RbNo].Size;
    Size += gFabricResource->PrefetchableMmioSizeAbove4G[SocketNo][RbNo].Size;
    Address = Base + Size;

    for (i = 0; i < RootBrgData->ResCount; i++) {
        res = RootBrgData->RbRes[i];
        if(res->Type == ASLRV_SPC_TYPE_MEM && res->_GRA == 64) {
            if (res->_LEN == 0) {
                continue;
            }

            RootBrgData->AcpiRbRes[raMmio64].Gra = res->_MAX;

            Status = dxe->AllocateMemorySpace (
                          EfiGcdAllocateMaxAddressSearchTopDown,
                          EfiGcdMemoryTypeMemoryMappedIo,
                          AlignFromGra((UINTN)res->_MAX),
                          res->_LEN,
                          &Address,
                          RootBrgData->ImageHandle,
                          RootBrgData->RbHandle
                        );
            if(EFI_ERROR(Status)) {
                DEBUG ((DEBUG_ERROR, " DXE::MMIO64 Allocation Failed: Length: %lX\n", res->_LEN));
                return Status;
            }
            DEBUG ((DEBUG_INFO, "  DXE::MMIO64 Base = 0x%lX, Length = %lX\n", Address, res->_LEN));

            Status = dxe->SetMemorySpaceAttributes(Address, res->_LEN, EFI_MEMORY_UC);
            if(EFI_ERROR(Status)) {
                return Status;
            }
            res->_MIN = Address;
        }
    }

    // Check if over the allocation to this RB
    if (Address < Base) {
        ResourceOverlap = TRUE;
        gFabricResource->PrefetchableMmioSizeAbove4G[SocketNo][RbNo].Base = Address;
        gFabricResource->PrefetchableMmioSizeAbove4G[SocketNo][RbNo].Size = Base + Size - Address;
        gFabricResource->NonPrefetchableMmioSizeAbove4G[SocketNo][RbNo].Size = 0;
    }

    // Fill in ACPI DATA
    RootBrgData->AcpiRbRes[raMmio64].Min = Base;
    RootBrgData->AcpiRbRes[raMmio64].Max = Base + Size - 1;
    RootBrgData->AcpiRbRes[raMmio64].Len = Size;
    RootBrgData->AcpiRbRes[raMmio64].AddrUsed = Base + Size;
    RootBrgData->AcpiRbRes[raMmio64].AllocType = EfiGcdAllocateAddress;

    DEBUG ((DEBUG_INFO, "  ACPI_MMIO64 Base = 0x%lX, Length =0x%lX\n", Base, Size));
    DEBUG ((DEBUG_INFO, "NbPciCsp::AllocateMmio64Resource Exit\n"));

    return  Status;
}

/**
    This function is invoked in PCI Host Bridge Driver when time
    to ask GCD for resources. You can overwrite a default
    algorithm used to allocate resources for the Root Bridge.

    @param HostBrgData Pointer to Host Bridge private structure data.
    @param RootBrgData Pointer to Root Bridge private structure data.
    @param RootBrgIndex Root Bridge index (0 Based).

    @retval EFI_STATUS

    @note  Porting required if needed.
**/

EFI_STATUS
HbCspAllocateResources (
  IN  PCI_HOST_BRG_DATA  *HostBrgData,
  IN  PCI_ROOT_BRG_DATA  *RootBrgData,
  IN  UINTN              RootBrgIndex )
{
    EFI_STATUS  Status = EFI_UNSUPPORTED;
#if BoardPciRes_SUPPORT
    AMI_OUT_OF_RES_VAR      PciBusOorVar;
    UINTN       VariableSize;
    UINTN       i;
#endif
    
 //PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
 //PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
 //Any Additional Variables goes here
    UINTN       SocketNo;
    UINTN       RbNo;
    UINTN       PhyRbNo;

    FABRIC_RESOURCE_MANAGER_PROTOCOL  *AmdFabricResourceManager;
    AMD_NBIO_CXL_SERVICES_PROTOCOL    *AmdNbioCxlServicesProtocol = NULL;

    DEBUG ((DEBUG_INFO, "\n*****HbCspAllocateResources for RB#%X START********\n", RootBrgIndex));

    //
    // Get the index of current root bridge in the DF topology
    //
    Status = GetRootbridgeLocation (RootBrgData, RootBrgIndex, &SocketNo, &RbNo, &PhyRbNo);
    if (EFI_ERROR (Status)) {
        DEBUG ((DEBUG_ERROR, " Failed to find RB in DF Topology\n"));
        return Status;
    }
    
#if BoardPciRes_SUPPORT
    Status = GetLastBootFailed();
    
    if (EFI_ERROR(Status)){
        //Variable has not been created set all entries to false
        SetLastBootFailed(HostBrgData->RootBridgeCount, FALSE, FALSE, 0);
    } else {
        //check if last boot failed
		// prevent gLastBootFailedVar overrun
		if(RootBrgIndex < MAX_ROOT_BRIDGE_COUNT){
            if (gLastBootFailedVar.FailedRb[RootBrgIndex] == TRUE){
                
                //Set this Rb to not failed since we are repairing
                gLastBootFailedVar.FailedRb[RootBrgIndex] = FALSE;
                VariableSize = sizeof(LAST_BOOT_FAILED_VAR);
                Status = gRT->SetVariable (L"LastBootFailed",
                        &gLastBootFailedGuid,
                        EFI_VARIABLE_NON_VOLATILE |
                        EFI_VARIABLE_BOOTSERVICE_ACCESS,
                        VariableSize,
                        &gLastBootFailedVar );
                if (EFI_ERROR (Status)) {
                    DEBUG ((DEBUG_ERROR, "HbCspAllocateResources: SetVariable - LastBootFailed Failed - r\n", Status));
                }
                gBS->SetMem(&PciBusOorVar, sizeof(AMI_OUT_OF_RES_VAR), 0);
                PciBusOorVar.Resource.Type = gLastBootFailedVar.FailType;
                if(gLastBootFailedVar.FailType == ASLRV_SPC_TYPE_IO){
                    PciBusOorVar.Resource._GRA = 16;
                } else {
                    PciBusOorVar.Resource._GRA = 32;
                }
                PciBusOorVar.Count = 1;
                for(i = 0; i < HostBrgData->RootBridgeCount; i++){
                    if (gLastBootFailedVar.FailedRb[i] == TRUE) PciBusOorVar.Count++;
                }
                Status = gRT->SetVariable (L"AmiOutOfRes",
                             &gAmiPciOutOfResVarGuid,
                             EFI_VARIABLE_BOOTSERVICE_ACCESS,
                             sizeof(AMI_OUT_OF_RES_VAR),
                             &PciBusOorVar );
                if (EFI_ERROR (Status)) {
                    DEBUG ((DEBUG_ERROR, "HbCspAllocateResources: SetVariable - AmiOutOfRes Failed - r\n", Status));
                }            
                return EFI_OUT_OF_RESOURCES;
            }
        }
    }
#endif //end OOR support

    //
    // Get the pre-allocated resources
    //
    if (gFabricResource == NULL) {
        Status = gBS->AllocatePool (
                      EfiBootServicesData,
                      sizeof (FABRIC_RESOURCE_FOR_EACH_RB),
                      (VOID *) &gFabricResource
                    );

        gBS->SetMem (gFabricResource, sizeof (FABRIC_RESOURCE_FOR_EACH_RB), 0);
        if (EFI_ERROR (Status)) {
            return Status;
        }

        Status = gBS->LocateProtocol (
                      &gAmdFabricResourceManagerServicesProtocolGuid,
                      NULL,
                      &AmdFabricResourceManager
                    );
        if (EFI_ERROR (Status)) {
            return Status;
        }

        DEBUG ((DEBUG_INFO, "\n*****Get Fabric Available Resources********\n", RootBrgIndex));
        Status = AmdFabricResourceManager->FabricGetAvailableResource (
                                          AmdFabricResourceManager,
                                          gFabricResource
                                         );
        if (EFI_ERROR (Status)) {
            ASSERT_EFI_ERROR(Status);
            return Status;
        }
        DEBUG ((DEBUG_INFO, "\n*****Locate CXL Services Protocol********\n", RootBrgIndex));

        Status = gBS->LocateProtocol (
                      &gAmdNbioCxlServicesProtocolGuid, 
                      NULL, 
                      &AmdNbioCxlServicesProtocol
                    );
        if (!EFI_ERROR (Status)) {
            DEBUG ((DEBUG_INFO, "\n*****Get CXL Available Resources********\n", RootBrgIndex));
            Status = AmdNbioCxlServicesProtocol->GetCxlAvailableResources (
                                                AmdNbioCxlServicesProtocol, 
                                                gFabricResource
                                              );
            if (EFI_ERROR (Status)) {
                DEBUG ((DEBUG_INFO, " Non-Fatal CXL resources not available\n"));
            }
        } else {
           DEBUG ((DEBUG_INFO, "\n*****Could not find CXL Services********\n", RootBrgIndex));
        }
    }
    
    if (FALSE == ((RootBrgIndex > 15) || ((SocketNo == 0) && ( RootBrgIndex > 7)))) {
        //
        // Allocate resource for IOMMU
        //
        if (!PcdGetBool(PcdCfgIommuMMIOAddressReservedEnable)) {
            Status = AllocateForIommu (RootBrgData, SocketNo, RbNo);
            if (EFI_ERROR (Status)) {
                DEBUG ((DEBUG_ERROR, " NbPciCsp::AllocateForIommu failed\n"));
                return Status;
            }
        }

        //
        // Allocate resource for NBIO APIC
        //
        Status = AllocateForNbIoApic (RootBrgData, RootBrgIndex, SocketNo, RbNo, PhyRbNo);
        if (EFI_ERROR (Status)) {
            DEBUG ((DEBUG_ERROR, " NbPciCsp::AllocateForNbIoApic failed\n"));
            return Status;
        }
        //
        // Allocate IO resource
        //
        Status = AllocateIoResource (RootBrgData, SocketNo, RbNo);
        if (EFI_ERROR (Status)) {
            DEBUG ((DEBUG_ERROR, " NbPciCsp::AllocateIoResource failed\n"));
            return Status;
        }
    }

    //
    // Allocate MMIO64 resource
    //
    Status = AllocateMmio64Resource (RootBrgData, SocketNo, RbNo);
    if (EFI_ERROR (Status)) {
        DEBUG ((DEBUG_ERROR, " NbPciCsp::AllocateIoResource failed\n"));
        return Status;
    }

    //
    // Allocate MMIO32 resource
    //
    Status = HandleMMIO32(HostBrgData, RootBrgData, RootBrgIndex);
    if(EFI_ERROR(Status)) {
        if (Status != EFI_NOT_READY) {
            return Status;
        }
    }

 //PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
 //PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
    DEBUG ((DEBUG_INFO, "\n*****HbCspAllocateResources for RB#%X END********\n", RootBrgIndex));

    return EFI_SUCCESS;
}

/**
    This function adjusts the MMIO overlap.

    @param HostBrgData
    @param RootBrgData
    @param RootBrgIndex

    @retval EFI_STATUS return EFI status

    @note
      None
**/

EFI_STATUS
HbCspAdjustMemoryMmioOverlap (
    IN PCI_HOST_BRG_DATA  *HostBrgData,
    IN PCI_ROOT_BRG_DATA  *RootBrgData,
    IN UINTN              RootBrgIndex )
{
    return EFI_SUCCESS;
}

/**
    This function will be invoked when Pci Host Bridge driver runs
    out of resources.

    @param HostBrgData Pointer to Host Bridge private structure data.
    @param RootBrgData
    @param RootBrgNumber

    @retval EFI_STATUS return EFI status

    @note  Porting required if needed.
**/

EFI_STATUS
HbCspGetProposedResources (
    IN PCI_HOST_BRG_DATA  *HostBrgData,
    IN PCI_ROOT_BRG_DATA  *RootBrgData,
    IN UINTN              RootBrgNumber )
{
    EFI_STATUS  Status = EFI_SUCCESS;

    // Any Additional Variables goes here

    return Status;
}

/**
    This function adjusts the MMIO overlap.

    @param HostBrgData
    @param RootBrgData
    @param RootBrgNumber
    @param PciAddress
    @param Phase

    @retval EFI_STATUS return EFI status

    @note
      None
**/

EFI_STATUS
HbCspPreprocessController (
    IN PCI_HOST_BRG_DATA                             *HostBrgData,
    IN PCI_ROOT_BRG_DATA                             *RootBrgData,
    IN UINTN                                         RootBrgNumber,
    IN EFI_PCI_CONFIGURATION_ADDRESS                 PciAddress,
    IN EFI_PCI_CONTROLLER_RESOURCE_ALLOCATION_PHASE  Phase )
{
    EFI_STATUS  Status=EFI_SUCCESS;
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//Any Additional Variables goes here

//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
    return Status;
}

/**
    This function create NB ACPI table.

    @param Event
    @param Context

    @retval VOID

    @note
      None
**/

VOID
CreateNbAcpiTables (
    IN EFI_EVENT  Event,
    IN VOID       *Context )
{
#if ACPI_SUPPORT
    EFI_STATUS                  Status = 0;
    MCFG_20_MSEG                *mcfg;
    EFI_ACPI_TABLE_PROTOCOL     *AcpiTableProtocol = NULL;
    UINT64                      PcieBaseAddr;
    UINT16                      i;
    UINT32                      Segments;

    //--------------------------------
    //it must be only one instance of such protocol
    Status = gBS->LocateProtocol(&gEfiAcpiTableProtocolGuid, NULL, &AcpiTableProtocol);
    DEBUG ((DEBUG_INFO, "PciHostCSHooks: LocateProtocol(ACPISupport)=%r\n", Status));
    if (EFI_ERROR(Status))  return;

    Segments = (UINT32) LShiftU64 (1, (UINTN) (RShiftU64 (AsmReadMsr64 (0xC0010058), 2) & 0xF));
    Segments = ((Segments < 0x100) ? 1 : (Segments / 0x100));
    mcfg = MallocZ(sizeof(MCFG_20_MSEG) + sizeof (CONFIGURATION_SPACE_BASE_ADDR_ALLOCATION_STRUCTURE) * Segments);
    ASSERT(mcfg);
    if (!mcfg)  return;

    //Fill Table header;
    mcfg->Header.Signature = MCFG_SIG;
    mcfg->Header.Length = sizeof(MCFG_20_MSEG) + sizeof (CONFIGURATION_SPACE_BASE_ADDR_ALLOCATION_STRUCTURE) * Segments;
    mcfg->Header.Revision = 1;
    mcfg->Header.Checksum = 0;
//    mcfg->Header.OemId[0]='A';      //"A M I "
//    mcfg->Header.OemId[1]=0x20;
//    mcfg->Header.OemId[2]='M';
//    mcfg->Header.OemId[3]=0x20;
//    mcfg->Header.OemId[4]='I';
//    mcfg->Header.OemId[5]=0x20;
//    mcfg->Header.OemTblId[0]='A';     //"OEMMCFG "
//    mcfg->Header.OemTblId[1]='M';
//    mcfg->Header.OemTblId[2]='D';
//    mcfg->Header.OemTblId[3]='O';
//    mcfg->Header.OemTblId[4]='N';
//    mcfg->Header.OemTblId[5]='T';
//    mcfg->Header.OemTblId[6]='P';
//    mcfg->Header.OemTblId[7]='I';

#ifdef  NB_MCFG_OEM_ID
    // user definition.
    gBS->CopyMem((VOID*)mcfg->Header.OemId, \
        CONVERT_TO_STRING(NB_MCFG_OEM_ID), \
        sizeof(mcfg->Header.OemId) \
    );
#else
  // Use the definition of ACPI.sdl.
  #ifdef T_ACPI_OEM_ID
    gBS->CopyMem((VOID*)mcfg->Header.OemId, \
        CONVERT_TO_STRING(T_ACPI_OEM_ID), \
        sizeof(mcfg->Header.OemId) \
    );
  #endif
#endif

#ifdef NB_MCFG_OEM_TBL_ID
    // user definition.
    gBS->CopyMem((VOID*)mcfg->Header.OemTblId, \
        CONVERT_TO_STRING(NB_MCFG_OEM_TBL_ID), \
        sizeof(mcfg->Header.OemTblId) \
    );
#else
  // Use the definition of ACPI.sdl.
  #ifdef T_ACPI_OEM_TBL_ID
    gBS->CopyMem((VOID*)mcfg->Header.OemTblId,
#ifdef ACPI_OEM_TBL_ID_MAK
        ACPI_OEM_TBL_ID_MAK,
#else
        CONVERT_TO_STRING(T_ACPI_OEM_TBL_ID),
#endif
        sizeof(mcfg->Header.OemTblId)
    );
  #endif
#endif

    mcfg->Header.OemRev = ACPI_OEM_REV;
    mcfg->Header.CreatorId = CREATOR_ID_MS;     // "MSFT" 4D 53 46 54
    mcfg->Header.CreatorRev = CREATOR_REV_MS;   // 0x00010013;

    //fill MCFG Fields
    PcieBaseAddr = PcdGet64 (PcdPciExpressBaseAddress);
    for (i = 0; i < Segments; i++) {
        mcfg->BaseAddrAllocation[i].BaseAddr = PcieBaseAddr +  MultU64x32((UINT64)i, 0x10000000);  //Base address of 256MB extended config space
        mcfg->BaseAddrAllocation[i].PciSeg = i;                                //Segment # of PCI Bus
        mcfg->BaseAddrAllocation[i].StartBus = 0;                              //Start bus number of PCI segment
#if  (PCIEX_LENGTH <= 0x10000000)
        mcfg->BaseAddrAllocation[i].EndBus = (PCIEX_LENGTH >> 20) - 1;    //End bus number of PCI segment
#else //(PCIEX_LENGTH <= 0x10000000)
        mcfg->BaseAddrAllocation[i].EndBus = 0xFF;
#endif //(PCIEX_LENGTH <= 0x10000000)
    }
    //Add table
    Status = AcpiTableProtocol->InstallAcpiTable(AcpiTableProtocol, mcfg, mcfg->Header.Length, &mMcfgTblKey);
    DEBUG ((DEBUG_INFO, "PciHostCSHooks: ACPISupport->InstallAcpiTable(MCFG) = %r\n", Status));
    ASSERT_EFI_ERROR(Status);

    //free memory used for table image
    gBS->FreePool(mcfg);
    //Kill the Event
    gBS->CloseEvent(Event);
#endif

    return;
}

/**
    BASIC Chipset initialization function called after Initialization of generic part of the
    Host and Root Bridges. All Handles for PCIHostBrg and PciRootBrg has been created and
    Protocol Interfaces installed
    Need to update this function from Aptio core Version 4.6.2 onwards

    @param HostBrg0

    @retval EFI_STATUS return EFI status

    @note
      None
**/

EFI_STATUS
HbCspBasicChipsetInit (
    IN PCI_HOST_BRG_DATA  *HostBrg0 )
{
    EFI_STATUS              Status;
    DXE_SERVICES            *dxe;

    Status = LibGetDxeSvcTbl(&dxe);
    ASSERT_EFI_ERROR(Status);
    if (EFI_ERROR(Status)) return Status;

    //Now for New Chipset which has PCI Express support we have to build
    //MCFG Table to inform OS about PCIE Ext cfg space mapping
    Status = RegisterProtocolCallback(&gEfiAcpiTableProtocolGuid, CreateNbAcpiTables, NULL,
                                    &mAcpiEvent, &mAcpiReg);
    ASSERT_EFI_ERROR(Status);

    //If this protocol has been installed we can use it rigth on the way
    CreateNbAcpiTables(mAcpiEvent, NULL);

    return EFI_SUCCESS;
}

/**
    Chipset Specific function to Map Internal Device address
    residing ABOVE 4G to the BELOW 4G address space for DMA.
    MUST BE IMPLEMENTED if CHIPSET supports address space
    decoding ABOVE 4G.

    @param Root Bridge private structure
    @param Operation to provide Mapping for
    @param HostAddress of the Device
    @param Number of Byte in Mapped Buffer.
    @param Mapped Device Address.
    @param Mapping Info Structure this function must allocate and fill.

    @retval EFI_SUCCESS Successful.
    @retval UINTN Number of Byte in Mapped Buffer.
    @retval EFI_PHYSICAL_ADDRESS Mapped Device Address.
    @retval PCI_ROOT_BRIDGE_MAPPING Mapping Info Structure this function must allocate and fill.
**/

EFI_STATUS
RbCspIoPciMap (
    IN PCI_ROOT_BRG_DATA                            *RbData,
    IN EFI_PCI_ROOT_BRIDGE_IO_PROTOCOL_OPERATION    Operation,
    IN EFI_PHYSICAL_ADDRESS                         HostAddress,
    IN OUT UINTN                                    *NumberOfBytes,
    OUT EFI_PHYSICAL_ADDRESS                        *DeviceAddress,
    OUT VOID                                        **Mapping )
{
    EFI_STATUS  Status=EFI_SUCCESS;
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//Porting is required for chipsets that supports Decoding of the PCI Address Space ABOVE 4G.

//Any Additional Variables goes here

//LakePort DOES NOT support decoding of the PCI resources ABOVE 4G.
//So return EFI_UNSUPPORTED;
    *Mapping=NULL;
    Status=EFI_UNSUPPORTED;

//for Chipsets which DOES support decoding of the PCI resources ABOVE 4G.
//here must be something like that.
/*
    PCI_ROOT_BRIDGE_MAPPING *mapping;
//------------------------------
        //Common buffer operations can not be remapped because in such
        // operations the same buffer will be accessed by CPU and PCI hardware
        if(Operation==EfiPciOperationBusMasterCommonBuffer || Operation==EfiPciOperationBusMasterCommonBuffer64)
            return EFI_UNSUPPORTED;

        mapping =Malloc(sizeof(PCI_ROOT_BRIDGE_MAPPING));
        if(mapping==NULL) return EFI_OUT_OF_RESOURCES;

        mapping->Signature  = EFI_PCI_RB_MAPPING_SIGNATURE;
        mapping->Resrved    = 0;
        mapping->Operation  = Operation;
        mapping->NumPages   = EFI_SIZE_TO_PAGES(*NumberOfBytes);
        mapping->HostAddr   = HostAddress;
        mapping->DeviceAddr = 0x00000000ffffffff;

        Status = gBS->AllocatePages(AllocateMaxAddress,EfiBootServicesData,mapping->NumPages,&mapping->DeviceAddr);
        if (EFI_ERROR(Status))
        {
            gBS->FreePool(mapping);
            return Status;
        }
        *Mapping=(VOID*)mapping;

        //Here must be a way to copy context of HostDevice buffer to the Mapped one.
        //This code given as example only you might need to do some chipset programming to
        //access PCI Address Space Above 4G

        if(Operation==EfiPciOperationBusMasterRead||Operation==EfiPciOperationBusMasterRead64)
            gBS->CopyMem( (VOID*)(UINTN)mapping->DeviceAddr,
                        (VOID*)(UINTN)mapping->HostAddr,
                        mapping->NumBytes);

        *DeviceAddress = mapping->DeviceAddr;
*/
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
    return Status;
}

/**
    Chipset Specific function to Unmap previousely Mapped
    buffer for DMA.
    MUST BE IMPLEMENTED if CHIPSET supports address space
    decoding ABOVE 4G.

    @param Root Bridge private structure
    @param Mapping Info Structure this function must free.

    @retval EFI_SUCCESS Successful
    @retval EFI_INVALID_PARAMETER Failure
**/

EFI_STATUS
RbCspIoPciUnmap (
    IN PCI_ROOT_BRG_DATA         *RbData,
    OUT PCI_ROOT_BRIDGE_MAPPING  *Mapping )
{
    EFI_STATUS  Status=EFI_SUCCESS;
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//

//Any Additional Variables goes here

//LakePort DOES NOT support decoding of the PCI resources ABOVE 4G.
//and for corresponded Mapping call we will return NULL as Mapping
//So return EFI_UNSUPPORTED if we get back something different;
    if(Mapping!=NULL) return EFI_INVALID_PARAMETER;
    //for all other conditions we would return EFI_UNSUPPORTED.
    Status=EFI_UNSUPPORTED;

//for Chipsets which DOES support decoding of the PCI resources ABOVE 4G.
//And provides corresponded mapping for the host address
//here must be something like that.
/*
    if(Mapping->Signature!=EFI_PCI_RB_MAPPING_SIGNATURE)Status=EFI_INVALID_PARAMERTER;

    if(!EFI_ERROR(Status)){

        if (Mapping->Operation == EfiPciOperationBusMasterWrite || Mapping->Operation == EfiPciOperationBusMasterWrite64)

        //Here must be a way to copy context of the Unmapped buffer to HostDevice.
        //This code given as example only you might need to do some chipset programming to
        //access PCI Address Space Above 4G
        gBS->CopyMem((VOID*)(UINTN)Mapping->HostAddr,(VOID*)(UINTN)Mapping->DeviceAddr,Mapping->NumBytes);

        gBS->FreePages(Mapping->DeviceAddr, Mapping->NumPages);
        gBS->FreePool(Mapping);
    }
*/
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
//PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING////PORTING//
    return Status;
}


/**
    Chipset Specific function to do PCI RB Attributes releated
    programming.

    @param Root Bridge private structure
    @param Attributes
    @param ResourceBase
    @param ResourceLength

    @retval EFI_SUCCESS Successful.
    @retval UINT64 ResourceBase OPTIONAL,
    @retval UINT64 ResourceLength OPTIONAL
**/

EFI_STATUS
RbCspIoPciAttributes (
    IN PCI_ROOT_BRG_DATA  *RbData,
    IN     UINT64         Attributes,
    IN OUT UINT64         *ResourceBase OPTIONAL,
    IN OUT UINT64         *ResourceLength OPTIONAL )
{
    EFI_STATUS  Status = EFI_SUCCESS;
    return Status;
}

/**
    Read Pci Registers into buffer.
    Csp Function which actualy access PCI Config Space
    Chipsets that capable of having PCI EXPRESS Ext Cfg Space transactions
    Must Implement this access routine here

    @param RbData Root Bridge private structure
    @param Width Memory Width
    @param Address PCI Address
    @param Count Number of width reads/writes.
    @param Buffer Buffer where read/written.
    @param Write Set if write.

    @retval EFI_SUCCESS Successful read.
    @retval VOID Buffer where read/written.

    @note
    None
**/

EFI_STATUS
RootBridgeIoPciRW (
    IN PCI_ROOT_BRG_DATA                      *RbData,
    IN EFI_PCI_ROOT_BRIDGE_IO_PROTOCOL_WIDTH  Width,
    IN UINT64                                 Address,
    IN UINTN                                  Count,
    IN OUT VOID                               *Buffer,
    IN BOOLEAN                                Write )
{
    EFI_TPL     OldTpl;
    UINT8       IncrementValue      = 1 << (Width & 3); //1st 2 bits currently define width. Other bits define type.
    BOOLEAN     IsPCIe = FALSE;
    UINT64      PcieBaseAddr = PcdGet64 (PcdPciExpressBaseAddress);
    UINTN  PCIeAddress = PcieBaseAddr +
        LShiftU64 (RbData->RbIoProtocol.SegmentNumber, 28) +
        (((EFI_PCI_CONFIGURATION_ADDRESS*) &Address)->Bus      << 20 )  +
        (((EFI_PCI_CONFIGURATION_ADDRESS*) &Address)->Device   << 15 )  +
        (((EFI_PCI_CONFIGURATION_ADDRESS*) &Address)->Function << 12 )  +
        ((EFI_PCI_CONFIGURATION_ADDRESS*) &Address)->ExtendedRegister;
    UINT32  PciAddress = BIT31 +
        (((EFI_PCI_CONFIGURATION_ADDRESS*) &Address)->Bus      << 16 )  +
        (((EFI_PCI_CONFIGURATION_ADDRESS*) &Address)->Device   << 11 )  +
        (((EFI_PCI_CONFIGURATION_ADDRESS*) &Address)->Function << 8  )  +
        (((EFI_PCI_CONFIGURATION_ADDRESS*) &Address)->Register       );
    UINT8   PciDataByte = PciAddress & 3;
    UINT32  PciAligned;
    UINT16  PciDataReg;

    if (((EFI_PCI_CONFIGURATION_ADDRESS*) &Address)->ExtendedRegister == 0 && ((EFI_PCI_CONFIGURATION_ADDRESS*) &Address)->Register != 0) {
        PCIeAddress += ((EFI_PCI_CONFIGURATION_ADDRESS*) &Address)->Register;
    }
    if ((PcieBaseAddr) &&
        (((EFI_PCI_CONFIGURATION_ADDRESS*) &Address)->ExtendedRegister) )
            IsPCIe = TRUE;
    if (RbData->RbIoProtocol.SegmentNumber > 0) {
        IsPCIe = TRUE;
    }

    // To read 64bit values, reduce Increment Value (by half) and
    // double the count value (by twice)
    if (IncrementValue > 4)
    {
        IncrementValue >>= 1;
        Count <<= 1;
    }

    if (Width >= EfiPciWidthMaximum || IncrementValue > 4) return EFI_INVALID_PARAMETER;

    while (Count--)
    {
        if (!IsPCIe)
        {
            PciAligned = PciAddress & ~3;           //Dword aligned.
            PciDataReg = 0xCFC + PciDataByte;       //0xcfc + {0,1,2,3} = reg + {0,1,2,3}
            OldTpl = gBS->RaiseTPL(TPL_HIGH_LEVEL);
            IoWrite32(0xCF8, PciAligned);
        }
        if (Write)
        {
            switch(IncrementValue)
            {
            case 1:
                if (IsPCIe) *(UINT8*)PCIeAddress = *(UINT8*)Buffer;
                else        IoWrite8(PciDataReg, *(UINT8*)Buffer);
                break;
            case 2:
                if (IsPCIe) *(UINT16*)PCIeAddress = *(UINT16*)Buffer;
                else        IoWrite16(PciDataReg, *(UINT16*)Buffer);
                break;
            default:
                if (IsPCIe) *(UINT32*)PCIeAddress = *(UINT32*)Buffer;
                else        IoWrite32(PciDataReg, *(UINT32*)Buffer);
                break;
            }
        }
        else
        {
            switch(IncrementValue)
            {
            case 1:
                *(UINT8*)Buffer = (IsPCIe) ? *(UINT8*)PCIeAddress : IoRead8(PciDataReg);
                break;
            case 2:
                *(UINT16*)Buffer = (IsPCIe) ? *(UINT16*)PCIeAddress : IoRead16(PciDataReg);
                break;
            default:
                *(UINT32*)Buffer = (IsPCIe) ? *(UINT32*)PCIeAddress : IoRead32(PciDataReg);
                break;
            }
        }

        if (!IsPCIe) gBS->RestoreTPL(OldTpl);

        if (Width <= EfiPciWidthFifoUint64)
        {
             Buffer = ((UINT8 *)Buffer + IncrementValue);
            //Buffer is increased for only EfiPciWidthUintxx and EfiPciWidthFifoUintxx
        }

        // Only increment the PCI address if Width is not a FIFO.
        if ((Width & 4) == 0)
        {
            if (IsPCIe) PCIeAddress += IncrementValue;
            else
            {
                PciAddress += IncrementValue;
                PciDataByte = PciAddress & 3;
            }
        }
    }
    return EFI_SUCCESS;
}

/**
    Converts C passed into Alignment format 
        
    @param g AlignFromGra Value to convert

    @retval UINTN
        Converted Alignment value.
**/

UINTN
AlignFromGra (
    UINTN g )
{
    UINTN a=0;
//------------
    while(g&1){
        a++;
        g=g>>1;
    }
    return a;   
}

