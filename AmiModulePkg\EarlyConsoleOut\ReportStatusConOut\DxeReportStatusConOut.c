//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************


/** @file  DxeReportStatusConOut.c
 Defines Status code function to display Progress and Error code to the Conout device.
**/

#include <DxeReportStatusConOut.h>

EFI_RSC_HANDLER_PROTOCOL    *gRscHandlerProtocol = NULL;

AMI_EARLY_CONSOLE_OUT_PROTOCOL    *gAmiEarlyConsoleOutProtocol = NULL;
/**
    UnRegister Handler to stop displaying checkpoint after SetupEnter and Readytoboot event.
        
    @param Event Signalled Event
    @param Context calling context

    @retval VOID

**/
VOID 
StopDxeReportStatus (
    IN EFI_EVENT    Event,
    IN VOID         *Context
)
{
    // This event will gets control during SetupEnter and ReadyToBoot
    // If booted to setup first and then booting to OS/Shell, 
    // gRscHandlerProtocol->Unregister() will return error during ReadyToBoot event
    // as it is already unregisted during SetupEnter
    if (gRscHandlerProtocol) {
        gRscHandlerProtocol->Unregister (DxeReportStatusCode);
    }
    gBS->CloseEvent(Event);
} 

/**
    callback function to register the DxeReportStatusCode handler 
    to display checkpoint.

    @param Event      Event for this callback.
    @param Context    Pointer to the context

    @retval None

**/
VOID 
EFIAPI
SimpleTextOutProtocolCallback (
    IN EFI_EVENT            Event, 
    IN VOID                 *Context
)
{
    EFI_STATUS              Status;
    EFI_EVENT               ReadyToBoot;

    // Locate the Report Status Code Handler Protocol
    Status = gBS->LocateProtocol (
                            &gEfiRscHandlerProtocolGuid,
                            NULL,
                            (VOID **)&gRscHandlerProtocol);
    if (!EFI_ERROR(Status)) {
        Status = gBS->LocateProtocol (
                              &gAmiEarlyConsoleOutProtocolGuid,
                              NULL,
                              (VOID**)&gAmiEarlyConsoleOutProtocol);  
       
        if (EFI_ERROR(Status)) {
            return;
        }
		
        // Register a handler for Status Code
        Status = gRscHandlerProtocol->Register (
                                        DxeReportStatusCode, 
                                        TPL_HIGH_LEVEL);
        if (!EFI_ERROR (Status)) {            
            EfiCreateEventReadyToBootEx (
                       TPL_CALLBACK,
                       StopDxeReportStatus,
                       NULL,
                       &ReadyToBoot);
            
            EfiNamedEventListen (
                          &gAmiTseSetupEnterGuid,
                          TPL_CALLBACK,
                          StopDxeReportStatus,
                          NULL,
                          NULL);
        }
    }

    if (Event != NULL) {
        gBS->CloseEvent (Event);
    }
    return;
}

/**
    Display status code check point and description string.

    @param Value    - EFI status code Value
    @param CodeType - EFI status code type
    @param Instance - The enumeration of a hardware or software entity 
                      within the system. A system may contain multiple entities that
                      match a class/subclass pairing. The instance differentiates
                      between them. An instance of 0 indicates that instance
                      information is unavailable, not meaningful, or not relevant.
                      Valid instance numbers start with 1.
    @param CallerId - This optional parameter may be used to identify the caller.
                      This parameter allows the status code driver to apply different
                      rules to different callers.
    @param Data - This optional parameter may be used to pass additional data.

    @return EFI_STATUS
    @retval EFI_SUCCESS Status code displayed successfully.

**/
EFI_STATUS
EFIAPI
DxeReportStatusCode (
  IN EFI_STATUS_CODE_TYPE     CodeType,
  IN EFI_STATUS_CODE_VALUE    Value,
  IN UINT32                   Instance,
  IN EFI_GUID                 *CallerId,
  IN EFI_STATUS_CODE_DATA     *ErrorData OPTIONAL 
  )
{

    UINTN                            CheckPointCode = 0;
    CHAR8                            *ProgressCodeString = NULL;
    CHAR8                            *ErrorCodeString = NULL;
    CHAR8                            *PossibleCauseString = NULL;
    CHAR8                            *PossibleSolutionString = NULL;
    CHAR16                           CheckPointString[5];
    CHAR16                           ErrorString[13];
    CHAR16                           TextString[160];
    EFI_STATUS                       Status;
   
    // Check if the type is Progress Code
    if ((CodeType & EFI_STATUS_CODE_TYPE_MASK) == EFI_PROGRESS_CODE) {
        
        // Get the CheckPoint value for the EFI Progress Code
        CheckPointCode = GetAmiProgressCodeCheckPoint (Value);
        if (CheckPointCode == 0) {
            // If no CheckPoint found return it
            return EFI_SUCCESS;
        }
        
        // Get the Progress code String for the 
        ProgressCodeString = GetAmiProgressCodeString (Value);
        if (ProgressCodeString == NULL) {
            // If no Progress code string is found return it
            return EFI_SUCCESS;
        }

        // Set the Progress Code display Attribute
        gAmiEarlyConsoleOutProtocol->SetAttribute (EFI_TEXT_ATTR(PROGRESS_CODE_FOREGROUND, PROGRESS_CODE_BACKGROUND));
        
        UnicodeSPrintAsciiFormat (CheckPointString, sizeof(CheckPointString), "0x%X", CheckPointCode);
        
        // Add the Progress Code string along with CheckPoint
        UnicodeSPrintAsciiFormat (TextString, sizeof(TextString), " %s : %a \n \r", CheckPointString, ProgressCodeString);
        DEBUG((DEBUG_INFO, "%a() - %s\n", __FUNCTION__, TextString));

        // Write String to TextOut Device
        gAmiEarlyConsoleOutProtocol->OutputString (
                            EarlyConsoleDisplayFrameDebug,
                            TextString);
        
        // Set the Original Attribute 
        gAmiEarlyConsoleOutProtocol->SetAttribute (EFI_TEXT_ATTR(POST_MSG_FOREGROUND, POST_MSG_BACKGROUND));
    } else if ((CodeType & EFI_STATUS_CODE_TYPE_MASK) == EFI_ERROR_CODE) {

       
        // Get the Check Point value for the EFI Error Code
        CheckPointCode = GetAmiErrorCodeCheckPoint (Value);
        if (CheckPointCode == 0) {
            // If no CheckPoint found return it
            return EFI_SUCCESS;
        }
        
        // Get the Error Code String for the 
        Status = GetAmiErrorCodeString (
                                    Value, 
                                    &ErrorCodeString,
                                    &PossibleCauseString,
                                    &PossibleSolutionString );
        if (EFI_ERROR(Status) || (ErrorCodeString == NULL)) {
            return EFI_SUCCESS;
        }
        UnicodeSPrintAsciiFormat (CheckPointString, sizeof(CheckPointString), "0x%X", CheckPointCode);

        // Set the Attribute based on the Error Type
        if ((CodeType & EFI_STATUS_CODE_SEVERITY_MASK) == EFI_ERROR_MINOR) {
            gAmiEarlyConsoleOutProtocol->SetAttribute (EFI_TEXT_ATTR(MINOR_ERROR_FOREGROUND, MINOR_ERROR_BACKGROUND));
        } else { 
            gAmiEarlyConsoleOutProtocol->SetAttribute (EFI_TEXT_ATTR(MAJOR_ERROR_FOREGROUND, MAJOR_ERROR_BACKGROUND));
        }

        // Add the Error String along with CheckPoint Value
        UnicodeSPrintAsciiFormat (TextString, sizeof (TextString), " %s : %a \n \r", CheckPointString, ErrorCodeString);
        
        //Write the String to Text Out Device
        gAmiEarlyConsoleOutProtocol->OutputString (
                            EarlyConsoleDisplayFrameDebug,
                            TextString);
        
        if (ErrorData != NULL) { 
            // Display the Error Code Data
            UnicodeSPrintAsciiFormat (ErrorString, sizeof(ErrorString),  "%x %x %x %x ", 
                    ((*(UINT32*)(ErrorData + 1)) >> 24) & 0xFF, ((*(UINT32*)(ErrorData + 1)) >> 16) & 0xFF, 
                    ((*(UINT32*)(ErrorData + 1)) >> 8) & 0xFF, (*(UINT32*)(ErrorData + 1)) & 0xFF);
        
            gAmiEarlyConsoleOutProtocol->OutputString (
                                EarlyConsoleDisplayFrameDebug,
                                ErrorString);
        }
        
        //Display the Possible Cause String if available
        if (!EFI_ERROR(Status) && (PossibleCauseString != NULL)) {
            UnicodeSPrintAsciiFormat (TextString, sizeof(TextString), "%a \n \r", PossibleCauseString);
            gAmiEarlyConsoleOutProtocol->OutputString (
                                EarlyConsoleDisplayFrameDebug,
                                TextString);
        }
        
        //Display the Possible Solution String if available
        if (!EFI_ERROR(Status) && (PossibleSolutionString != NULL)) {
            UnicodeSPrintAsciiFormat (TextString, sizeof(TextString), "%a \n \r", PossibleSolutionString);
            gAmiEarlyConsoleOutProtocol->OutputString (
                                EarlyConsoleDisplayFrameDebug,
                                TextString);
        }
        
        // Set the Original Attribute 
        gAmiEarlyConsoleOutProtocol->SetAttribute (EFI_TEXT_ATTR(POST_MSG_FOREGROUND, POST_MSG_BACKGROUND));
    }

    return EFI_SUCCESS;
}

/**
    This function Registers the callback function for the ReportStatusCode 

    @param ImageHandle Handle for the image of this driver
    @param SystemTable Pointer to the EFI System Table

    @retval EFI_SUCCESS Handler Registered Successfully 
    @retval EFI_NOT_FOUND Conout Protocol not found 

**/

EFI_STATUS
EFIAPI
InitializeDxeReportStatus (
  IN EFI_HANDLE         ImageHandle,
  IN EFI_SYSTEM_TABLE   *SystemTable 
)
{
    EFI_STATUS      Status;
    VOID            *Interface;
    
    Status = gBS->LocateProtocol (
                        &gEfiRscHandlerProtocolGuid,
                        NULL,
                        (VOID **)&Interface);    
    
    if (!EFI_ERROR(Status)) {
        SimpleTextOutProtocolCallback (NULL, NULL);
    } else {
        EfiNamedEventListen(
                      &gEfiRscHandlerProtocolGuid,
                      TPL_CALLBACK,
                      SimpleTextOutProtocolCallback,
                      NULL,
                      NULL );
    }
    
    return EFI_SUCCESS;
}
