#***********************************************************************
#*                                                                     *
#*   Copyright (c) 1985-2025, American Megatrends International LLC.   *
#*                                                                     *
#*      All rights reserved. Subject to AMI licensing agreement.       *
#*                                                                     *
#***********************************************************************

[Defines]
  INF_VERSION     = 0x00010005
  VERSION_STRING  = 1.0
  BASE_NAME       = EarlyConsoleOutInterfacePei
  MODULE_TYPE     = PEIM
  FILE_GUID       = C50842D6-B284-4F3D-904B-E2DC83E00AF8
  ENTRY_POINT     = EarlyConsoleOutInterfacePeiEntryPoint

[Sources]
  EarlyConsoleOutInterfacePei.c
  EarlyConsoleDisplayPei.c
  EarlyConsoleDisplay.h
  SmLogo.h

[LibraryClasses]
  PeimEntryPoint
  DebugLib
  PeiServicesLib
  BaseMemoryLib
  HobLib
  BaseLib
  PeiLogoFontLib
  PrintLib
  BaseLib
  ProgressErrorCodeLib
  $(VIDEO_TEXT_OUT_LIB)

[Ppis]
  gAmiEarlyConsoleOutPpiGuid
  gAmiGraphicsOutPutPpiGuid
  gAmiSimpleTextOutPpiGuid
  gEfiPeiMemoryDiscoveredPpiGuid
 
[Guids]
  gAmiEarlyConsoleDisplayFrameInfoHobGuid
  gAmiEarlyConsoleStringHobGuid
  
[Pcd]
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdSimpleTextOutMaxPpiSupported
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdDrawDefaultLogoInPei
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdProgressBarForegroundColor
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdProgressBarBackgroundColor
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdProgressBarBorderColor
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdDisplayBackgroundImageInPei
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdCallFromEarlyConsoleOut
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdDynamicBackgroundStringsSupport

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  AmiModulePkg/AmiModulePkg.dec
  AmiModulePkg/EarlyConsoleOut/AmiEarlyConsoleOutPkg.dec

[Depex]
  TRUE
