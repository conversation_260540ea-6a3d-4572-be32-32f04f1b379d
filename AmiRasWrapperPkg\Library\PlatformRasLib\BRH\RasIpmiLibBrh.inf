#pragma message( "Compal Server Override Compiling-" __FILE__ )
#***********************************************************************
#*                                                                     *
#*   Copyright (c) 1985-2023, American Megatrends International LLC.   *
#*                                                                     *
#*      All rights reserved. Subject to AMI licensing agreement.       *
#*                                                                     *
#***********************************************************************

#
#
#
[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = RasIpmiLibBrh
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = RasIpmiLibBrh

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#
#

[Sources]
  RasIpmiLibBrh.c

[Packages]
  MdePkg/MdePkg.dec
  AmiModulePkg/AmiModulePkg.dec
  AgesaPkg/AgesaPkg.dec
  AmdCpmPkg/AmdCpmPkg.dec
  AmiCompatibilityPkg/AmiCompatibilityPkg.dec
  AmiIpmi2Pkg/AmiIpmi2Pkg.dec
  AmiRasWrapperPkg/AmiRasWrapperPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec 
  AmiChipsetPkg/AmiChipsetPkg.dec  #COMPAL_CHANGE
  OemboardPkg/OemboardPkg.dec      #COMPAL_CHANGE

[LibraryClasses]
  BaseLib
#  CpmRasLib
  PciLib                 #COMPAL_CHANGE
  CpmRasPciLib           #COMPAL_CHANGE
  BaseFabricTopologyLib  #COMPAL_CHANGE

[Protocols]
  gEfiPciIoProtocolGuid  #COMPAL_CHANGE
  gOemboardPkgSkuIdGuid  #COMPAL_CHANGE
