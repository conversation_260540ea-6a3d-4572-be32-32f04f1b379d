/*****************************************************************************
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * Platform PCIe Complex Driver
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  AmdNbioSmuV9Pei
 * @e \$Revision: 312065 $   @e \$Date: 2015-01-30 04:23:05 -0600 (Fri, 30 Jan 2015) $
 *
 */
#include <PiPei.h>
#include <AmdPcieComplex.h>
#include <AmdServerHotplugRs.h>
#include <Protocol/NbioHotplugDesc.h>
#include <Library/BaseLib.h>
#include <Library/DebugLib.h>
#include <Library/UefiLib.h>
//#include <Library/HobLib.h>
//#include <Library/UefiDriverEntryPoint.h>
#include <Library/UefiBootServicesTableLib.h>

//COMPAL_CHANGE >>> Add SKU_ID Protocol Support
#include <Protocol/SkuIdProtocol.h>
#include <OemBoardInfo.h>
#include <OemBoardInfoLib.h>
//COMPAL_CHANGE <<<

/*----------------------------------------------------------------------------------------
 *                         E X T E R N   D E F I N I T I O N S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U  R E S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U  N C T I O N S
 *----------------------------------------------------------------------------------------
 */

HOTPLUG_DESCRIPTOR    mHotplugDescriptorAmberRevA[] = {
  {
    0,
    HOTPLUG_ENGINE_DATA_INITIALIZER (31, 16, 1, 4)
    PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModule, 0, 0, 1, 1, 0, 0, 0)
    PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                       0,     // GPIO Byte Mapping
                                       3,     // GPIO Device Address
                                       1,     // Device Type 1 = 9535
                                       1,     // Bus Segment (0x70, bus 1, p-links)
                                       0)     // Function Mask
    PCIE_HOTPLUG_INITIALIZER_NO_RESET ()
  },
  {
    DESCRIPTOR_TERMINATE_LIST,
    HOTPLUG_ENGINE_DATA_INITIALIZER (95, 80, 1, 8)
    PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModule, 0, 0, 1, 1, 0, 0, 0)
    PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                       0,     // GPIO Byte Mapping
                                       3,     // GPIO Device Address
                                       1,     // Device Type 1 = 9535
                                       0,     // Bus Segment (0x70, bus 1, g-links)
                                       0)     // Function Mask
    PCIE_HOTPLUG_INITIALIZER_NO_RESET ()
  },
};

STATIC NBIO_HOTPLUG_DESC_PROTOCOL mHotplugDescriptorAmberRevAProtocol = {
  AMD_NBIO_HOTPLUG_DESC_VERSION,  ///< revision
  mHotplugDescriptorAmberRevA
};

HOTPLUG_DESCRIPTOR    mHotplugDescriptorAmberRevB[] = {
  {
    DESCRIPTOR_TERMINATE_LIST,
    HOTPLUG_ENGINE_DATA_INITIALIZER (47, 32, 1, 4)
    PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModule, 0, 0, 1, 1, 0, 0, 0)
    PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                       0,     // GPIO Byte Mapping
                                       3,     // GPIO Device Address
                                       1,     // Device Type 1 = 9535
                                       1,     // Bus Segment (0x70, bus 1, p-links)
                                       0)     // Function Mask
    PCIE_HOTPLUG_INITIALIZER_NO_RESET ()
  },
};

STATIC NBIO_HOTPLUG_DESC_PROTOCOL mHotplugDescriptorAmberRevBProtocol = {
  AMD_NBIO_HOTPLUG_DESC_VERSION,  ///< revision
  mHotplugDescriptorAmberRevB
};

  HOTPLUG_DESCRIPTOR    HotplugDescriptorAmberRetimer[] = {
  {
    0,
    HOTPLUG_ENGINE_DATA_INITIALIZER (47, 32, 1, 4)
    PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModuleB, 0, 0, 1, 1, 0, 0, 0)
    PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                       0,     // GPIO Byte Mapping
                                       3,     // GPIO Device Address
                                       1,     // Device Type 1 = 9535
                                       1,     // Bus Segment (0x70, bus 1, p-links)
                                       0)     // Function Mask
    PCIE_HOTPLUG_INITIALIZER_NO_RESET ()
  },
    {
    DESCRIPTOR_TERMINATE_LIST,
    HOTPLUG_ENGINE_DATA_INITIALIZER (95, 80, 1, 8)
    PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModule, 0, 0, 1, 1, 0, 0, 0)
    PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                       0,     // GPIO Byte Mapping
                                       3,     // GPIO Device Address
                                       1,     // Device Type 1 = 9535
                                       0,     // Bus Segment (0x70, bus 1, g-links)
                                       0)     // Function Mask
    PCIE_HOTPLUG_INITIALIZER_NO_RESET ()
  },
};

STATIC NBIO_HOTPLUG_DESC_PROTOCOL mHotplugDescriptorAmberRetimerProtocol = {
  AMD_NBIO_HOTPLUG_DESC_VERSION,  ///< revision
  HotplugDescriptorAmberRetimer
};
HOTPLUG_DESCRIPTOR    HotplugDescriptorSSDPLinks[] = {  // LegacyNVMEEntSSDEntryPlinks
  {  //P0 - Lanes 0-15 - Drives 0-3
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (0,      // Start Lane
                                       3,      // End Lane
                                       1,       // Socket Number
                                       8)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugEnterpriseSsd,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                         0,     // GPIO Byte Mapping
                                         0,     // GPIO Device Address
                                         1,     // Device Type 1 = 9535
                                         4,     // Bus Segment (No 9545 Present)
                                         0)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (4,       // Start Lane
                                       7,       // End Lane
                                       1,       // Socket Number
                                       9)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugEnterpriseSsd,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                         0,     // GPIO Byte Mapping
                                         0,    // GPIO Device Address
                                         1,     // Device Type 1 = 9535
                                         4,     // Bus Segment (No 9545 Present)
                                         0)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (8,       // Start Lane
                                       11,      // End Lane
                                       1,       // Socket Number
                                       10)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugEnterpriseSsd,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                         1,     // GPIO Byte Mapping
                                         0,    // GPIO Device Address
                                         1,     // Device Type 1 = 9535
                                         4,     // Bus Segment (No 9545 Present)
                                         0)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (12,      // Start Lane
                                       15,      // End Lane
                                       1,       // Socket Number
                                       11)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugEnterpriseSsd,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                         1,     // GPIO Byte Mapping
                                         0,    // GPIO Device Address
                                         1,     // Device Type 1 = 9535
                                         4,     // Bus Segment (No 9545 Present)
                                         0)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {  //P1 - Lanes 32-47 - Drives 4-7
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (32,       // Start Lane
                                       35,       // End Lane
                                       1,       // Socket Number
                                       12)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugEnterpriseSsd,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                         0,     // GPIO Byte Mapping
                                         1,     // GPIO Device Address
                                         1,     // Device Type 1 = 9535
                                         5,     // Bus Segment (No 9545 Present)
                                         0)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (36,       // Start Lane
                                       39,       // End Lane
                                       1,       // Socket Number
                                       13)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugEnterpriseSsd,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                         0,     // GPIO Byte Mapping
                                         1,    // GPIO Device Address
                                         1,     // Device Type 1 = 9535
                                         5,     // Bus Segment (No 9545 Present)
                                         0)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (40,       // Start Lane
                                       43,      // End Lane
                                       1,       // Socket Number
                                       14)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugEnterpriseSsd,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                         1,     // GPIO Byte Mapping
                                         1,    // GPIO Device Address
                                         1,     // Device Type 1 = 9535
                                         5,     // Bus Segment (No 9545 Present)
                                         0)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (44,      // Start Lane
                                       47,      // End Lane
                                       1,       // Socket Number
                                       15)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugEnterpriseSsd,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                         1,     // GPIO Byte Mapping
                                         1,    // GPIO Device Address
                                         1,     // Device Type 1 = 9535
                                         5,     // Bus Segment (No 9545 Present)
                                         0)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {  //P2 - Lanes 48-63 - Drives 8-11
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (48,       // Start Lane
                                       51,       // End Lane
                                       1,       // Socket Number
                                       16)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugEnterpriseSsd,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                         0,     // GPIO Byte Mapping
                                         2,     // GPIO Device Address
                                         1,     // Device Type 1 = 9535
                                         6,     // Bus Segment (No 9545 Present)
                                         0)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (52,       // Start Lane
                                       55,       // End Lane
                                       1,       // Socket Number
                                       17)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugEnterpriseSsd,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                         0,     // GPIO Byte Mapping
                                         2,    // GPIO Device Address
                                         1,     // Device Type 1 = 9535
                                         6,     // Bus Segment (No 9545 Present)
                                         0)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (56,       // Start Lane
                                       59,      // End Lane
                                       1,       // Socket Number
                                       18)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugEnterpriseSsd,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                         1,     // GPIO Byte Mapping
                                         2,    // GPIO Device Address
                                         1,     // Device Type 1 = 9535
                                         6,     // Bus Segment (No 9545 Present)
                                         0)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (60,      // Start Lane
                                       63,      // End Lane
                                       1,       // Socket Number
                                       19)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugEnterpriseSsd,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                         1,     // GPIO Byte Mapping
                                         2,    // GPIO Device Address
                                         1,     // Device Type 1 = 9535
                                         6,     // Bus Segment (No 9545 Present)
                                         0)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {  // P3 - Lanes 16-31 - Drives 12-15
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (16,       // Start Lane
                                       19,       // End Lane
                                       1,       // Socket Number
                                       20)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugEnterpriseSsd,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                         0,     // GPIO Byte Mapping
                                         3,     // GPIO Device Address
                                         1,     // Device Type 1 = 9535
                                         8,     // Bus Segment (No 9545 Present)
                                         0)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (20,       // Start Lane
                                       23,       // End Lane
                                       1,       // Socket Number
                                       21)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugEnterpriseSsd,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                         0,     // GPIO Byte Mapping
                                         3,    // GPIO Device Address
                                         1,     // Device Type 1 = 9535
                                         8,     // Bus Segment (No 9545 Present)
                                         0)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (24,       // Start Lane
                                       27,      // End Lane
                                       1,       // Socket Number
                                       22)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugEnterpriseSsd,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                         1,     // GPIO Byte Mapping
                                         3,     // GPIO Device Address
                                         1,     // Device Type 1 = 9535
                                         8,     // Bus Segment (No 9545 Present)
                                         0)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      DESCRIPTOR_TERMINATE_LIST,
      HOTPLUG_ENGINE_DATA_INITIALIZER (28,      // Start Lane
                                       31,      // End Lane
                                       1,       // Socket Number
                                       23)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugEnterpriseSsd,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                         1,     // GPIO Byte Mapping
                                         3,     // GPIO Device Address
                                         1,     // Device Type 1 = 9535
                                         8,     // Bus Segment (No 9545 Present)
                                         0)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
};

STATIC NBIO_HOTPLUG_DESC_PROTOCOL mHotplugDescriptorSSDPLinksProtocol = {
  AMD_NBIO_HOTPLUG_DESC_VERSION,  ///< revision
  HotplugDescriptorSSDPLinks
};

HOTPLUG_DESCRIPTOR    HotplugDescriptorModeA[] = {

  {  //P0 - Lanes 0-15 - Drives 0-3
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (0,      // Start Lane
                                       3,      // End Lane
                                       1,       // Socket Number
                                       8)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModule,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                         0,     // GPIO Byte Mapping
                                         0,     // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                         9,     // Bus Segment (No 9545 Present)
                                         0xE)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (4,       // Start Lane
                                       7,       // End Lane
                                       1,       // Socket Number
                                       9)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModule,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                         1,     // GPIO Byte Mapping
                                         0,    // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                         9,     // Bus Segment (No 9545 Present)
                                         0xE)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (8,       // Start Lane
                                       11,      // End Lane
                                       1,       // Socket Number
                                       10)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModule,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                         2,     // GPIO Byte Mapping
                                         0,    // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                         9,     // Bus Segment (No 9545 Present)
                                         0xE)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (12,      // Start Lane
                                       15,      // End Lane
                                       1,       // Socket Number
                                       11)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModule,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                         3,     // GPIO Byte Mapping
                                         0,    // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                         9,     // Bus Segment (No 9545 Present)
                                         0xE)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {  //P1 - Lanes 32-47 - Drives 4-7
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (32,       // Start Lane
                                       35,       // End Lane
                                       1,       // Socket Number
                                       12)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModule,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                         0,     // GPIO Byte Mapping
                                         1,     // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                        10,     // Bus Segment (No 9545 Present)
                                         0xE)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (36,       // Start Lane
                                       39,       // End Lane
                                       1,       // Socket Number
                                       13)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModule,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                         1,     // GPIO Byte Mapping
                                         1,    // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                        10,     // Bus Segment (No 9545 Present)
                                         0xE)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (40,       // Start Lane
                                       43,      // End Lane
                                       1,       // Socket Number
                                       14)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModule,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                         2,     // GPIO Byte Mapping
                                         1,    // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                        10,     // Bus Segment (No 9545 Present)
                                         0xE)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (44,      // Start Lane
                                       47,      // End Lane
                                       1,       // Socket Number
                                       15)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModule,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                         3,     // GPIO Byte Mapping
                                         1,    // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                         10,     // Bus Segment (No 9545 Present)
                                         0xE)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {  //P2 - Lanes 48-63 - Drives 8-11
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (48,       // Start Lane
                                       51,       // End Lane
                                       1,       // Socket Number
                                       16)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModule,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                         0,     // GPIO Byte Mapping
                                         2,     // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                        11,     // Bus Segment (No 9545 Present)
                                         0xE)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (52,       // Start Lane
                                       55,       // End Lane
                                       1,       // Socket Number
                                       17)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModule,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                         1,     // GPIO Byte Mapping
                                         2,    // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                        11,     // Bus Segment (No 9545 Present)
                                         0xE)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (56,       // Start Lane
                                       59,      // End Lane
                                       1,       // Socket Number
                                       18)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModule,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                         2,     // GPIO Byte Mapping
                                         2,    // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                        11,     // Bus Segment (No 9545 Present)
                                        0xE)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (60,      // Start Lane
                                       63,      // End Lane
                                       1,       // Socket Number
                                       19)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModule,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                         3,     // GPIO Byte Mapping
                                         2,    // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                        11,     // Bus Segment (No 9545 Present)
                                         0xE)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {  // P3 - Lanes 16-31 - Drives 12-15
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (16,       // Start Lane
                                       19,       // End Lane
                                       1,       // Socket Number
                                       20)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModule,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                         0,     // GPIO Byte Mapping
                                         3,     // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                        12,     // Bus Segment (No 9545 Present)
                                         0xE)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (20,       // Start Lane
                                       23,       // End Lane
                                       1,       // Socket Number
                                       21)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModule,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                         1,     // GPIO Byte Mapping
                                         3,    // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                        12,     // Bus Segment (No 9545 Present)
                                         0xE)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (24,       // Start Lane
                                       27,      // End Lane
                                       1,       // Socket Number
                                       22)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModule,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                         2,     // GPIO Byte Mapping
                                         3,     // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                        12,     // Bus Segment (No 9545 Present)
                                         0xE)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      DESCRIPTOR_TERMINATE_LIST,
      HOTPLUG_ENGINE_DATA_INITIALIZER (28,      // Start Lane
                                       31,      // End Lane
                                       1,       // Socket Number
                                       23)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModule,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                         3,     // GPIO Byte Mapping
                                         3,     // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                        12,     // Bus Segment (No 9545 Present)
                                         0xE)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
};

STATIC NBIO_HOTPLUG_DESC_PROTOCOL mHotplugDescriptorModeAProtocol = {
  AMD_NBIO_HOTPLUG_DESC_VERSION,  ///< revision
  HotplugDescriptorModeA
};

HOTPLUG_DESCRIPTOR    HotplugDescriptorModeB[] = {
  {  //P0 - Lanes 0-15 - Drives 0-3 ModeB
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (0,      // Start Lane
                                       3,      // End Lane
                                       1,       // Socket Number
                                       8)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModuleB,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                         0,     // GPIO Byte Mapping
                                         0,     // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                         13,     // Bus Segment (No 9545 Present)
                                         0xEF)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (4,       // Start Lane
                                       7,       // End Lane
                                       1,       // Socket Number
                                       9)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModuleB,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                         1,     // GPIO Byte Mapping
                                         0,    // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                         13,     // Bus Segment (No 9545 Present)
                                         0xEF)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (8,       // Start Lane
                                       11,      // End Lane
                                       1,       // Socket Number
                                       10)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModuleB,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                         2,     // GPIO Byte Mapping
                                         0,    // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                         13,     // Bus Segment (No 9545 Present)
                                         0xEF)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (12,      // Start Lane
                                       15,      // End Lane
                                       1,       // Socket Number
                                       11)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModuleB,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                         3,     // GPIO Byte Mapping
                                         0,    // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                         13,     // Bus Segment (No 9545 Present)
                                         0xEF)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {  //P1 - Lanes 32-47 - Drives 4-7
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (32,       // Start Lane
                                       35,       // End Lane
                                       1,       // Socket Number
                                       12)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModuleB,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                         0,     // GPIO Byte Mapping
                                         1,     // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                        14,     // Bus Segment (No 9545 Present)
                                         0xEF)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (36,       // Start Lane
                                       39,       // End Lane
                                       1,        // Socket Number
                                       13)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModuleB,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                         1,     // GPIO Byte Mapping
                                         1,    // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                        14,     // Bus Segment (No 9545 Present)
                                         0xEF)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (40,      // Start Lane
                                       43,      // End Lane
                                       1,       // Socket Number
                                       14)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModuleB,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                         2,     // GPIO Byte Mapping
                                         1,    // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                        14,     // Bus Segment (No 9545 Present)
                                         0xEF)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (44,      // Start Lane
                                       47,      // End Lane
                                       1,       // Socket Number
                                       15)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModuleB,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                         3,     // GPIO Byte Mapping
                                         1,    // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                         14,     // Bus Segment (No 9545 Present)
                                         0xEF)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {  //P2 - Lanes 48-63 - Drives 8-11
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (48,       // Start Lane
                                       51,       // End Lane
                                       1,       // Socket Number
                                       16)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModuleB,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                         0,     // GPIO Byte Mapping
                                         2,     // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                        15,     // Bus Segment (No 9545 Present)
                                         0xEF)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (52,       // Start Lane
                                       55,       // End Lane
                                       1,       // Socket Number
                                       17)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModuleB,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                         1,     // GPIO Byte Mapping
                                         2,    // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                        15,     // Bus Segment (No 9545 Present)
                                         0xEF)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (56,       // Start Lane
                                       59,      // End Lane
                                       1,       // Socket Number
                                       18)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModuleB,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                         2,     // GPIO Byte Mapping
                                         2,    // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                        15,     // Bus Segment (No 9545 Present)
                                         0xEF)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (60,      // Start Lane
                                       63,      // End Lane
                                       1,       // Socket Number
                                       19)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModuleB,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                         3,     // GPIO Byte Mapping
                                         2,    // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                        15,     // Bus Segment (No 9545 Present)
                                         0xEF)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {  // P3 - Lanes 16-31 - Drives 12-15
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (16,       // Start Lane
                                       19,       // End Lane
                                       1,       // Socket Number
                                       20)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModuleB,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                         0,     // GPIO Byte Mapping
                                         3,     // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                        16,     // Bus Segment (No 9545 Present)
                                         0xEF)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (20,       // Start Lane
                                       23,       // End Lane
                                       1,       // Socket Number
                                       21)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModuleB,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                         1,     // GPIO Byte Mapping
                                         3,    // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                        16,     // Bus Segment (No 9545 Present)
                                         0xEF)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (24,       // Start Lane
                                       27,      // End Lane
                                       1,       // Socket Number
                                       22)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModuleB,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                         2,     // GPIO Byte Mapping
                                         3,     // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                        16,     // Bus Segment (No 9545 Present)
                                         0xEF)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      DESCRIPTOR_TERMINATE_LIST,
      HOTPLUG_ENGINE_DATA_INITIALIZER (28,      // Start Lane
                                       31,      // End Lane
                                       1,       // Socket Number
                                       23)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugExpressModuleB,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                         3,     // GPIO Byte Mapping
                                         3,     // GPIO Device Address
                                         2,     // Device Type 2 = 9506
                                        16,     // Bus Segment (No 9545 Present)
                                         0xEF)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
};

STATIC NBIO_HOTPLUG_DESC_PROTOCOL mHotplugDescriptorModeBProtocol = {
  AMD_NBIO_HOTPLUG_DESC_VERSION,  ///< revision
  HotplugDescriptorModeB
};

HOTPLUG_DESCRIPTOR    HotplugDescriptorSimPresenceLinks[] = {  // LegacyNVMESimPresenceEntrylinks
  {  // Socket 1 P1 - Lanes 32-44 - NVMe x4
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (44,       // Start Lane
                                       47,       // End Lane
                                       1,       // Socket Number
                                       55)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugPresenceDetect,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (7,     // Gpio Bit Select
                                         0,     // GPIO Byte Mapping
                                         0,    // GPIO Device Address
                                         1,     // Device Type 1 = 9535
                                         7,     // Bus Segment (No 9545 Present)
                                         0)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (40,       // Start Lane
                                       43,       // End Lane
                                       1,       // Socket Number
                                       54)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugPresenceDetect,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (6,     // Gpio Bit Select
                                         0,     // GPIO Byte Mapping
                                         0,    // GPIO Device Address
                                         1,     // Device Type 1 = 9535
                                         7,     // Bus Segment (No 9545 Present)
                                         0)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (36,      // Start Lane
                                       39,     // End Lane
                                       1,       // Socket Number
                                       53)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugPresenceDetect,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (5,     // Gpio Bit Select
                                         0,     // GPIO Byte Mapping
                                         0,    // GPIO Device Address
                                         1,     // Device Type 1 = 9535
                                         7,     // Bus Segment (No 9545 Present)
                                         0)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (32,      // Start Lane
                                       35,      // End Lane
                                       1,       // Socket Number
                                       52)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugPresenceDetect,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (4,     // Gpio Bit Select
                                         0,     // GPIO Byte Mapping
                                         0,    // GPIO Device Address
                                         1,     // Device Type 1 = 9535
                                         7,     // Bus Segment (No 9545 Present)
                                         0)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {  // Socket 0 P1 - Lanes 32-44 - NVMe x4
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (44,       // Start Lane
                                       47,       // End Lane
                                       0,       // Socket Number
                                       43)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugPresenceDetect,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (3,     // Gpio Bit Select
                                         0,     // GPIO Byte Mapping
                                         0,     // GPIO Device Address
                                         1,     // Device Type 1 = 9535
                                         7,     // Bus Segment (No 9545 Present)
                                         0)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (40,       // Start Lane
                                       43,       // End Lane
                                       0,       // Socket Number
                                       42)       // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugPresenceDetect,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (2,     // Gpio Bit Select
                                         0,     // GPIO Byte Mapping
                                         0,    // GPIO Device Address
                                         1,     // Device Type 1 = 9535
                                         7,     // Bus Segment (No 9545 Present)
                                         0)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      0,
      HOTPLUG_ENGINE_DATA_INITIALIZER (36,       // Start Lane
                                       39,      // End Lane
                                       0,       // Socket Number
                                       41)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugPresenceDetect,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (1,     // Gpio Bit Select
                                         0,     // GPIO Byte Mapping
                                         0,    // GPIO Device Address
                                         1,     // Device Type 1 = 9535
                                         7,     // Bus Segment (No 9545 Present)
                                         0)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
  {
      DESCRIPTOR_TERMINATE_LIST,
      HOTPLUG_ENGINE_DATA_INITIALIZER (32,      // Start Lane
                                       35,      // End Lane
                                       0,       // Socket Number
                                       40)      // Slot number
      PCIE_HOTPLUG_INITIALIZER_MAPPING (HotplugPresenceDetect,   //Hotplug Type
                                        0,      // 0 = No Gpio Descriptor
                                        0,      // 0 = No Reset Descriptor
                                        1,      // 1 = Port Active - this is a valid descriptor
                                        0,      // 1 = Master/Slave APU
                                        0,      // 0 = Die number this slot is connected to
                                        0,      // Alternate Slot number
                                        0)      // Primary/secondary for SSD only
      PCIE_HOTPLUG_INITIALIZER_FUNCTION (0,     // Gpio Bit Select
                                         0,     // GPIO Byte Mapping
                                         0,    // GPIO Device Address
                                         1,     // Device Type 1 = 9535
                                         7,     // Bus Segment (No 9545 Present)
                                         0)     // Function Mask
      PCIE_HOTPLUG_INITIALIZER_NO_RESET()
  },
};

STATIC NBIO_HOTPLUG_DESC_PROTOCOL mHotplugDescriptorSimPresenceLinksProtocol = {
  AMD_NBIO_HOTPLUG_DESC_VERSION,  ///< revision
  HotplugDescriptorSimPresenceLinks
};

EFI_STATUS
EFIAPI
HotplugDescEntry (
  IN       EFI_HANDLE         ImageHandle,
  IN       EFI_SYSTEM_TABLE   *SystemTable
  )
{
  EFI_HANDLE                       Handle;
  EFI_STATUS                       Status;
  //COMPAL_CHANGE >>> Add SKU_ID Protocol Support
  SKU_ID_PROTOCOL                  *SkuIdProtocol;
  EFI_HANDLE                       SkuIdHandle;
  //COMPAL_CHANGE <<<

  Handle = NULL;
  Status = EFI_SUCCESS;

  //COMPAL_CHANGE >>>
  GET_HARDWARE_SKU_RESPONSE         HwSku;

  UINT8                             SKU_ID = 0;

  Status = GetHwSku(&HwSku);
  SKU_ID = HwSku.SkuId;

  // Allocate memory for SKU_ID Protocol
  Status = gBS->AllocatePool (
                  EfiBootServicesData,
                  sizeof (SKU_ID_PROTOCOL),
                  (VOID **)&SkuIdProtocol
                  );
  if (EFI_ERROR (Status)) {
    DEBUG ((EFI_D_ERROR, "Failed to allocate memory for SKU_ID Protocol: %r\n", Status));
    return Status;
  }

  // Initialize SKU_ID Protocol data
  SkuIdProtocol->SkuId = SKU_ID;

  // Install SKU_ID Protocol
  SkuIdHandle = NULL;
  Status = gBS->InstallProtocolInterface (
                  &SkuIdHandle,
                  &gOemboardPkgSkuIdGuid,
                  EFI_NATIVE_INTERFACE,
                  SkuIdProtocol
                  );
  if (EFI_ERROR (Status)) {
    DEBUG ((EFI_D_ERROR, "Failed to install SKU_ID Protocol: %r\n", Status));
    gBS->FreePool (SkuIdProtocol);
    return Status;
  }

  DEBUG ((EFI_D_INFO, "SKU_ID Protocol installed successfully. SKU_ID = 0x%02X\n", SKU_ID));

#if 1
  if (1){
      DEBUG ((EFI_D_ERROR, "DXE: Found Found legacy NVME Backplane in Simple Presence mode\n"));
      Status = gBS->InstallProtocolInterface (
                  &Handle,
                  &gAmdHotplugDescProtocolGuid,
                  EFI_NATIVE_INTERFACE,
                  &mHotplugDescriptorSimPresenceLinksProtocol
                  );
   }
#endif   
  //COMPAL_CHANGE <<<
  
return Status;
}

