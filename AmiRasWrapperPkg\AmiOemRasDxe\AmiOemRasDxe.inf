#pragma message( "Compal Server Override Compiling-" __FILE__ )
#***********************************************************************
#*                                                                     *
#*   Copyright (c) 1985-2023, American Megatrends International LLC.   *
#*                                                                     *
#*      All rights reserved. Subject to AMI licensing agreement.       *
#*                                                                     *
#***********************************************************************

[defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = AmiOemRasDxe
  FILE_GUID                      = 8456F6DB-043B-405E-B18D-684E69B05DE7
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = AmiOemRasDxeInit

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaModuleRasPkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec
  AgesaPkg/AgesaPkg.dec
  AmdCpmPkg/AmdCpmPkg.dec
  AmiGpnvErrorLoggingPkg/AmiGpnvErrorLoggingPkg.dec
  AmiRasWrapperPkg/AmiRasWrapperPkg.dec
  AmiIpmi2Pkg/AmiIpmi2Pkg.dec
  AmiModulePkg/AmiModulePkg.dec
  OemboardPkg/OemboardPkg.dec      #COMPAL_CHANGE

[Sources]
  AmiOemRasDxe.c
  AmiOemRasDxe.h

[LibraryClasses]
  UefiDriverEntryPoint
  UefiLib
  CpmRasLib
  RasCommonLibBrh
  RasIpmiLibBrh
  $(AMIOEMRAS_LIB_CLASSES_LIST)

[Guids]

[Pcd]

[FixedPcd]
  gWheaTokenSpaceGuid.PcdErrorLogDataBufferSize    # CONSUMES

[Protocols]
  gEfiRedirElogProtocolGuid         #CONSUMED
  gAmdRasServiceDxeProtocolGuid     #CONSUME
  gAmdCpmRasOemProtocolGuid         #PRODUCED
  gAmdPlatformApeiDataProtocolGuid  #COMPAL_CHANGE
  gOemboardPkgSkuIdGuid             #COMPAL_CHANGE

[Depex]
  AFTER gAmdPlatformRasBrhDxeFileGuid