#;*****************************************************************************
#;
#; Copyright (C) 2016-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[Defines]
  DEC_SPECIFICATION     = 0x00010005
  PACKAGE_NAME          = AgesaNbioPkg
  PACKAGE_GUID          = 1486f0fe-39ee-4856-a39f-222ff2404f26
  PACKAGE_VERSION       = 0.1

[Includes]
  Include

[LibraryClasses]
  GnbLib|Include/Library/GnbLib.h


[Guids]
  gGnbNbioBaseZPHobInfoGuid                 = {0xce3e2c94, 0x82e2, 0x4429, {0xba, 0x69, 0xcc, 0xf6, 0xb9, 0xba, 0x1,  0x4c}}
  gGnbPcieHobInfoGuid                       = {0x3eb1d90,  0xce14, 0x40d8, {0xa6, 0xba, 0x10, 0x3a, 0x8d, 0x7b, 0xd3, 0x2d}}
  gGnbIntegratedSysInfoHobGuid              = {0x0664BCCF, 0x0A93, 0x407C, {0xA5, 0x8B, 0x2A, 0x56, 0xE4, 0x88, 0x6A, 0x24}}
  gGnbIntegratedSysInfoV2HobGuid            = {0x6013bf85, 0xe185, 0x4b68, {0xbb, 0x6c, 0xcd, 0xbe, 0xed, 0x47, 0x83, 0x0f}}
  gGnbIntegratedSysInfoV3HobGuid            = {0x858cb1d4, 0x3d27, 0x4b7a, {0xb1, 0xdb, 0x97, 0xb6, 0xc3, 0xc1, 0x5b, 0x8c}}
  gGnbIntegratedSysInfoV4HobGuid            = {0x89d85219, 0x2ca1, 0x42de, {0x99, 0x32, 0x74, 0x88, 0xdf, 0x4f, 0x5f, 0x83}}
  gGnbPeiGopHobGuid                         = {0xa90f3dfd, 0xdb05, 0x4e73, {0x86, 0x75, 0xd,  0xd1, 0xf,  0x66, 0x9e, 0xf }}
  gGnbN6DisplayPhyTuningSetHobGuid          = {0xae79c42d, 0x0b10, 0x4ef4, {0x87, 0x9c, 0xf0, 0xd0, 0x34, 0x51, 0x8c, 0xa3}}
  ## GUID used to store Uma CarveOut Default into an EFI Variable
  # Include/Guid/GnbUmaCarveOutDefaultVariable.h
  gAmdUmaCarveOutDefaultVarGuid          = { 0x0E5CE58D, 0xE59B, 0x4F93, {0x90, 0x4A, 0x6E, 0xF2, 0xB9, 0x7A, 0x41, 0xD7} }

  gAmdCfgdGPUOnlyModeDefaultVarGuid         = {0x62c1ed4b, 0x15ec, 0x4daf, {0x9b, 0xbb, 0xd0, 0xa9, 0x7, 0x54, 0x17, 0x61 }}
  gAmdCfgIGPUDisVarGuid                     = {0x8b60513, 0x8255, 0x4c95, {0x82, 0x2b, 0xf8, 0xce, 0x88, 0xfa, 0x32, 0xfa }}

[Protocols]
  gAmdCoreTopologyServicesProtocolGuid      = {0xecf77b54, 0x9b7b, 0x4fcb, {0x9a, 0x3f, 0x2,  0x28, 0x82, 0xa1, 0x36, 0x7a}}
  gAmdCoreTopologyServicesV2ProtocolGuid    = {0x8e169307, 0xa77c, 0x4785, {0xa3, 0x7f, 0xc5, 0x24, 0x84, 0x56, 0xa3, 0x3f}}
  gAmdGnbMidDxeInitCompleteProtocolGuid     = {0x21b0f8f8, 0x2d53, 0x460f, {0x96, 0x21, 0x53, 0x2e, 0x13, 0xb7, 0x37, 0x60}}
  gAmdGnbLateDxeInitCompleteProtocolGuid    = {0xd64dfa32, 0x7ee4, 0x4a80, {0xab, 0xd3, 0xba, 0xc6, 0x3,  0xa,  0x61, 0xc6}}
  gAmdGnbMidLateDxeInitCompleteProtocolGuid = {0x41dce477, 0xe8f5, 0x4ad4, {0xb4, 0x5e, 0x61, 0x12, 0xb8, 0xe2, 0x8a, 0xd1}}
  gAmdNbioSmuServicesProtocolGuid           = {0x264272b3, 0x81d1, 0x453a, {0x85, 0x3,  0x89, 0x89, 0xf4, 0x38, 0x2e, 0x68}}
  gAmdNbioMpioServicesProtocolGuid          = {0x2cec9c8e, 0xbc8d, 0x425b, {0x98, 0xca, 0x77, 0x8c, 0xde, 0x94, 0x1f, 0xb7}}
  gAmdIGpuAcpiCratServicesProtocolGuid      = {0xfb75e5a6, 0x4d55, 0x4a4d, {0x95, 0xe9, 0xa4, 0xb3, 0x74, 0x0d, 0xa9, 0xaf}}
  gAmdNbioCppcServicesProtocolGuid          = {0x0EA8FF7D, 0x6E9E, 0x41D6, {0x86, 0x7B, 0xDC, 0x57, 0x81, 0x4F, 0x60, 0xAE}}
  gAmdNbioCcixServicesProtocolGuid          = {0x1C1E0D7F, 0xEDA3, 0x4063, {0x97, 0x50, 0x18, 0x2D, 0x1D, 0x0A, 0xC1, 0x94}}
  gAmdNbioCoreRankingTableServicesProtocolGuid = {0x80FFC040, 0x61D1, 0x4095, {0x80, 0x2C, 0x70, 0x25, 0xEC, 0x8C, 0x73, 0xF3}}

  ## Gnb Protocols
  gAmdNbioPcieServicesProtocolGuid          = {0x756db75c, 0xbb9d, 0x4289, {0x81, 0x3a, 0xdf, 0x21, 0x5,  0xc4, 0xf8, 0xe}}
  gAmdNbioDebugServicesProtocolGuid         = {0x636a5dc3, 0xe94a, 0x4f10, {0x85, 0x7f, 0x5e, 0xff, 0xe6, 0x5b, 0xf8, 0xb6}}
  gAmdNbioALibServicesProtocolGuid          = {0x329955de, 0xbc26, 0x46a9, {0x97, 0x9b, 0x32, 0x65, 0x1f, 0x7f, 0x24, 0x24}}

  #NBIO
  gAmdNbioBaseCZDepexProtocolGuid           = {0x1db226e8, 0x152e, 0x48ab, {0xaf, 0x79, 0x8d, 0xa5, 0xbb, 0xf3, 0x7b, 0x5d}}
  gAmdNbioBaseZPDxeDepexProtocolGuid        = {0xa7f7e6ef, 0x4f51, 0x1ed7, {0xba, 0xd3, 0xa5, 0x8e, 0xbc, 0xe2, 0x29, 0x1f}}
  gAmdNbioBaseRVDxeDepexProtocolGuid        = {0x596d93e1, 0x2437, 0x49f2, {0x89, 0xc4, 0x60, 0x3b, 0x52, 0x41, 0x94, 0x3e}}
  gAmdNbioBaseRNDxeDepexProtocolGuid        = {0xb0f836fc, 0x99f0, 0x4e63, {0xa8, 0x01, 0x3e, 0x74, 0x5b, 0x39, 0xf2, 0x61}}
  gAmdNbioBaseRMBDxeDepexProtocolGuid       = {0xb60bbd1f, 0xb0c9, 0x474a, {0xa9, 0xbf, 0x67, 0xec, 0xec, 0xa5, 0x82, 0x94}}
  gAmdNbioBaseRPLDxeDepexProtocolGuid       = {0x49e0132b, 0x9ddf, 0x45f7, {0xbc, 0x38, 0xa6, 0xaa, 0x79, 0x65, 0x8f, 0xec}}
  gAmdNbioBaseSSPDxeDepexProtocolGuid       = {0x17100CDE, 0xF316, 0x4E3E, {0xC2, 0xCF, 0x8E, 0x32, 0x6C, 0xDC, 0xC2, 0x91}}
  gAmdNbioBaseMDNDxeDepexProtocolGuid       = {0x8c02d3de, 0x730a, 0x4734, {0xac, 0xff, 0x37, 0x04, 0xbb, 0x71, 0x2c, 0x82}}
  gAmdNbioSmuV8DepexProtocolGuid            = {0xaf1f4b98, 0xdf0a, 0x43c8, {0x9b, 0xb,  0xa1, 0x51, 0xf6, 0x51, 0xa8, 0x4}}
  gAmdNbioSmuV9DxeDepexProtocol             = {0xd8bd23c9, 0x43dc, 0xc124, {0x9a, 0x5c, 0x7f, 0xa9, 0x60, 0x77, 0x01, 0x70}}
  gAmdNbioSmuV10DxeDepexProtocol            = {0x4523b4db, 0x5d76, 0x48b9, {0x9d, 0xdc, 0x84, 0xa9, 0x51, 0xc9, 0xa2, 0x7a}}
  gAmdNbioSmuV11DxeDepexProtocol            = {0x611C535E, 0x8856, 0x4CCA, {0x53, 0xF5, 0xC2, 0xBB, 0xA9, 0xB2, 0xEA, 0xB2}}
  gAmdNbioSmuV11DxeBADepexProtocol          = {0x5f64017b, 0xeae5, 0x4254, {0x99, 0x1d, 0x40, 0x18, 0x1, 0x5e, 0x14, 0xa3}}
  gAmdNbioSmuV11DxeGNDepexProtocol          = {0x3B257F26, 0x4B7D, 0x4075, {0xBE, 0x4A, 0x26, 0x25, 0x9E, 0x08, 0x81, 0x6D}}
  gAmdNbioSmuV11DxeVMRDepexProtocol         = {0x3ED6916B, 0x80EA, 0x4B4F, {0xB2, 0xA5, 0x1B, 0x2D, 0x6A, 0xC3, 0x1B, 0x2C}}
  gAmdNbioSmuV12DxeDepexProtocol            = {0xFD45EBCF, 0x7FE7, 0x448E, {0x90, 0x8D, 0xAE, 0x1C, 0x0B, 0x2C, 0x3A, 0x1C}}
  gAmdNbioSmuV12CZNDxeDepexProtocol         = {0xac62db06, 0xee6b, 0x4f08, {0x9e, 0x87, 0x6F, 0xA8, 0xA0, 0x7E, 0x7A, 0x97}}
  gAmdNbioSmuV13DxeDepexProtocol            = {0xfbf9009, 0xcf29, 0x4378, {0x99, 0xa7, 0x58, 0x23, 0x31, 0xb3, 0xa2, 0xf7}}
  gAmdNbioSmuV13DxeRPLDepexProtocol         = {0xb94c01b9, 0x619b, 0x4b05, {0xa3, 0xe7, 0xd4, 0x24, 0x49, 0xcd, 0x8, 0xb2}}
  gAmdNbioPcieZPDxeDepexProtocolGuid        = {0x695aa59e, 0x499d, 0xf523, {0xc4, 0xe1, 0x0d, 0x8d, 0xb0, 0xb8, 0x6e, 0x49}}
  gAmdNbioPcieRVDxeDepexProtocolGuid        = {0x3178b580, 0x9fc5, 0x48f1, {0x85, 0xa8, 0x4a, 0xef, 0x92, 0xa1, 0x8f, 0x66}}
  gAmdNbioPcieRNDxeDepexProtocolGuid        = {0x5eb2d483, 0x45c0, 0x4c94, {0xa0, 0x24, 0xb9, 0x5c, 0xec, 0x29, 0xd3, 0xcd}}
  gAmdNbioPcieRMBDxeDepexProtocolGuid       = {0x9249b762, 0x5e5f, 0x463f, {0x82, 0x96, 0x4f, 0x9f, 0x6d, 0x0, 0xe5, 0xbc}}
  gAmdNbioPcieRPLDxeDepexProtocolGuid       = {0x14c89698, 0x22b0, 0x4aea, {0x99, 0xe6, 0xed, 0xbe, 0xda, 0x10, 0xa6, 0x47}}
  gAmdNbioPcieSSPDxeDepexProtocolGuid       = {0x13E01531, 0xC130, 0x41CB, {0xA8, 0x0E, 0xC2, 0x7C, 0xDE, 0xF3, 0x97, 0x8C}}
  gAmdNbioPcieMDNDxeDepexProtocolGuid       = {0x01b20318, 0xa011, 0x478a, {0xbf, 0xda, 0xf9, 0xc3, 0x19, 0xb9, 0xa7, 0xd6}}
  gAmdNbioIOMMUZPDxeDepexProtocolGuid       = {0x716fc6ab, 0x4fde, 0x8d13, {0xa8, 0xfa, 0x0e, 0xa1, 0xb4, 0xf2, 0x1f, 0x6d}}
  gAmdNbioIOMMURVDxeDepexProtocolGuid       = {0x5bfce3f4, 0xa980, 0x43a3, {0x88, 0x3d, 0x4c, 0x1a, 0x1d, 0x84, 0xca, 0xc8}}
  gAmdNbioIOMMURNDxeDepexProtocolGuid       = {0x17b75484, 0xed78, 0x4521, {0xaa, 0xd7, 0x90, 0xe5, 0x79, 0xae, 0xec, 0xe1}}
  gAmdNbioIOMMURMBDxeDepexProtocolGuid      = {0x6248d6d3, 0xde15, 0x49fc, {0x9a, 0x34, 0x10, 0x89, 0x95, 0x4f, 0x59, 0x51}}
  gAmdNbioIOMMURPLDxeDepexProtocolGuid      = {0x86a2396b, 0xc513, 0x4c32, {0x9b, 0xa4, 0x41, 0x45, 0x2f, 0x6a, 0xe0, 0x4}}
  gAmdNbioIOMMUSSPDxeDepexProtocolGuid      = {0xBA41F071, 0xA105, 0x47D0, {0xC3, 0x2A, 0x34, 0x48, 0x37, 0xA8, 0x8D, 0x9D}}
  gAmdNbioIOMMUMDNDxeDepexProtocolGuid      = {0x75c67fe3, 0x9948, 0x4d2a, {0x95, 0x62, 0x9d, 0x81, 0x86, 0x10, 0xa2, 0xa4}}
  gAmdNbioBaseFF3DxeDepexProtocolGuid        = {0x093584f8, 0x2844, 0x4e74, {0x98, 0x20, 0x34, 0x21, 0x45, 0xa1, 0x2d, 0x82}}
  gAmdNbioSmuV13DxeDepexProtocol            = {0x52f59e3c, 0x8e4e, 0x4d30, {0xac, 0xda, 0xbd, 0xe4, 0xb9, 0x27, 0x31, 0x6f}}
  gAmdNbioPcieFF3DxeDepexProtocolGuid        = {0x931a932d, 0xa581, 0x4ab2, {0x9b, 0x37, 0x08, 0x65, 0xb6, 0x0f, 0x11, 0x47}}
  gAmdNbioIOMMUFF3DxeDepexProtocolGuid       = {0x61c6fb11, 0x9fa2, 0x4d2b, {0x88, 0x37, 0x92, 0x78, 0xa8, 0x98, 0xc8, 0x1a}}
  gAmdNbioALIBVNDxeDepexProtocolGuid        = {0x4932dfdf, 0x82f7, 0x498e, {0x89, 0x51, 0x16, 0x10, 0x35, 0x9c, 0x38, 0x84}}

  gAmdNbioALIBSSPDxeDepexProtocolGuid       = {0x284e0764, 0x5382, 0x4fb4, {0xb4, 0x92, 0x4c, 0x6f, 0x4c, 0x0a, 0x7c, 0xd2}}
  gAmdNbioALIBZPDxeDepexProtocolGuid        = {0x6588818e, 0x5eda, 0x49d0, {0x80, 0xca, 0xfc, 0xeb, 0x7c, 0x70, 0x61, 0x71}}
  gAmdNbioALIBRVDxeDepexProtocolGuid        = {0x47006b41, 0x7270, 0x433b, {0x98, 0xcf, 0x98, 0x88, 0x6d, 0x0c, 0xb6, 0xbb}}
  gAmdNbioALIBRNDxeDepexProtocolGuid        = {0xF191AF38, 0x2631, 0x4A30, {0xB8, 0xF4, 0x49, 0x8F, 0xD1, 0x78, 0xAD, 0x03}}
  gAmdNbioALIBRMBDxeDepexProtocolGuid       = {0x733f1e9b, 0x614c, 0x43e9, {0x88, 0xae, 0x4c, 0x8e, 0xfc, 0x68, 0x84, 0xb8}}
  gAmdNbioALIBRPLDxeDepexProtocolGuid       = {0xef9167df, 0xf506, 0x481e, {0xb5, 0x7c, 0xdd, 0xe6, 0xe, 0xe9, 0x79, 0x91}}
  gAmdNbioALIBVMRDxeDepexProtocolGuid       = {0xc30d01fe, 0x21d,  0x40fd, {0xab, 0x6d, 0xfb, 0xb5, 0x8b, 0xac, 0xbe, 0xdb}}
  gAmdNbioALIBMDNDxeDepexProtocolGuid       = {0xb37721de, 0x5994, 0x4254, {0x8e, 0xcf, 0x5f, 0x65, 0x45, 0x31, 0x00, 0xe8}}
  gAmdNbioBaseBADxeDepexProtocolGuid        = {0x3e4a1d9a, 0x97ba, 0x4a22, {0xb2, 0x47, 0x93, 0x65, 0x24, 0xcd, 0x17, 0xca}}
  gAmdNbioPcieBADxeDepexProtocolGuid        = {0xc151769b, 0xaf42, 0x4c1e, {0x9a, 0x35, 0x8a, 0x1d, 0xe9, 0xd1, 0x42, 0xa2}}
  gAmdNbioIOMMUBADxeDepexProtocolGuid       = {0xc0418587, 0xd5af, 0x498c, {0xb5, 0x4b, 0xcb, 0xff, 0x61, 0x89, 0xd, 0x75}}

  gAmdNbioBaseGNDxeDepexProtocolGuid        = {0x919D695A, 0x1CCA, 0x47E0, {0xBE, 0x9D, 0x05, 0x15, 0x6D, 0x45, 0xAE, 0x81}}
  gAmdNbioPcieGNDxeDepexProtocolGuid        = {0x4367F99F, 0xAD99, 0x4514, {0x9B, 0xFE, 0xD2, 0xC7, 0xD2, 0xA7, 0x21, 0x5F}}
  gAmdNbioIOMMUGNDxeDepexProtocolGuid       = {0x521B45B5, 0xBBC3, 0x4AD9, {0xA4, 0x5C, 0x92, 0xD7, 0x1F, 0xB5, 0x40, 0x55}}

  gAmdSmmCommunicationDepexProtocolGuid     = {0x84cb08f0, 0x4769, 0x4b65, {0x9c, 0x23, 0xe2, 0x89, 0x5b, 0xd6, 0xbf, 0x1e}}

  gAmdNbioRSDxeDepexProtocolGuid            = {0XEA88A83F, 0X8BDC, 0X4985, {0X8B, 0XA8, 0X76, 0X58, 0X71, 0X78, 0X19, 0X85}}
  gAmdNbioPHXDxeDepexProtocolGuid           = {0x8c97cde2, 0x7082, 0x466c, {0x98, 0x16, 0x17, 0x67, 0xb9, 0x01, 0x4a, 0xf2}}
  gAmdNbioMI3DxeDepexProtocolGuid           = {0xD43C6A05, 0x300B, 0x43A0, {0x98, 0x9A, 0xD1, 0x16, 0x9F, 0x01, 0xF4, 0x94}}
  gAmdNbioSTXKRKDxeDepexProtocolGuid        = {0x309df6cb, 0x7045, 0x4af6, {0xbd, 0x21, 0xee, 0x7d, 0xd6, 0x6d, 0x9e, 0x80}}
  gAmdNbioSTPDxeDepexProtocolGuid           = {0x118fd68d, 0xe46f, 0x4e9b, {0x9b, 0x9f, 0x4f, 0x64, 0x29, 0x65, 0xcc, 0xfd}}
  gAmdNbioBRHDxeDepexProtocolGuid           = {0xC234FFBF, 0xAFBF, 0x44D7, {0x91, 0x0E, 0xF3, 0xCD, 0x5E, 0x4F, 0x30, 0x19}}
  gAmdNbioSTXHDxeDepexProtocolGuid         = {0x0c4126c7, 0xd77c, 0x40fe, {0x8c, 0x42, 0xf7, 0xf5, 0x7a, 0xce, 0xb1, 0xa3}}
  gAmdNbioKRKDxeDepexProtocolGuid           = {0xc02d0d9d, 0xf6af, 0x4e9d, {0xac, 0x1c, 0x08, 0xda, 0x6b, 0x48, 0xa7, 0x64}}

  ## NBIO Services Protocols
  gAmdNbioSmuInitCompleteProtocolGuid       = {0xc7d74155, 0xa8d7, 0x4462, {0xa7, 0xe8, 0x53, 0xa3, 0x74, 0x77, 0x39, 0xef}}


[Ppis]
  gAmdNbioPcieTrainingStartPpiGuid         = {0xa99f2e33, 0x7f34, 0x4b5c, {0x83, 0xc1, 0x43, 0x92, 0x2f, 0x5b, 0x89, 0x58}}
  gAmdNbioPcieDeAssertResetPpiGuid         = {0x48d47948, 0x88a7, 0x4e06, {0xbf, 0x2b, 0x50, 0xb2, 0x57, 0x72, 0x97, 0x0e}}
  gAmdNbioPcieCxlInitStartPpiGuid          = {0x6cb2ea24, 0xfa2c, 0x4c3c, {0xa7, 0xb9, 0x98, 0x28, 0x8d, 0xfd, 0xc4, 0x90}}
  gAmdNbioPcieTrainingDonePpiGuid          = {0x72166411, 0x442c, 0x4aab, {0xa2, 0x60, 0x57, 0xd5, 0x84, 0xd3, 0x21, 0x39}}
  gAmdCoreTopologyServicesPpiGuid          = {0x8c61c925, 0x224e, 0x42d7, {0x89, 0x78, 0x81, 0x2a, 0xe5, 0x61, 0x2d, 0x23}}
  gAmdCoreTopologyServicesV2PpiGuid        = {0xa3e9c86f, 0xd620, 0x432a, {0x9a, 0x8a, 0x0c, 0x7e, 0xc7, 0x81, 0x76, 0xce}}
  gAmdNbioSmuServicesPpiGuid               = {0xea335e48, 0x7275, 0x4d2b, {0x82, 0x76, 0x55, 0xba, 0x55, 0x31, 0xd7, 0xd7}}
  gAmdNbioEarlyLinkPpiGuid                 = {0x2ab93c90, 0xd69a, 0x4b57, {0x98, 0x49, 0x98, 0xd5, 0xcc, 0x4f, 0x88, 0xde}}
  gAmdNbioBixbyEarlyLinkPpiGuid            = {0xe2b7f58e, 0xbfd8, 0x472f, {0x86, 0x91, 0x89, 0x37, 0x7f, 0x90, 0x99, 0xa1}}

  #NBIO
  gAmdNbioRVEarlyPhaseDepexPpiGuid       = {0xb397ad2b, 0x5153, 0x4950, {0xad, 0xa2, 0xb2, 0x8b, 0x0a, 0x07, 0xe1, 0x42}}
  gAmdNbioBaseCZDepexPpiGuid             = {0x456e435f, 0xca3,  0x430c, {0xaa, 0x78, 0xa,  0x3b, 0x66, 0x7c, 0xb,  0x9b}}
  gAmdNbioBaseZPPeiDepexPpiGuid          = {0x25117e9f, 0x46b8, 0xaebc, {0x3c, 0x29, 0xa2, 0x85, 0x31, 0xbe, 0x45, 0xc1}}
  gAmdNbioBaseRVPeiDepexPpiGuid          = {0x150f6f3,  0x58e8, 0x407b, {0xb0, 0x66, 0xdf, 0x54, 0x34, 0x2d, 0x50, 0x6b}}
  gAmdNbioBaseRNPeiDepexPpiGuid          = {0xc03f9d1d, 0xb6ec, 0x4493, {0x92, 0xa0, 0x1d, 0x7d, 0x90, 0x4e, 0xb8, 0x03}}
  gAmdNbioBaseRMBPeiDepexPpiGuid         = {0x2236bcce, 0x6570, 0x4f80, {0x8c, 0x6b, 0x56, 0xe6, 0xa7, 0x87, 0xdc, 0xcf}}
  gAmdNbioBaseRPLPeiDepexPpiGuid         = {0x130d9af6, 0xa264, 0x404b, {0xb7, 0x83, 0xbc, 0x9e, 0x1a, 0xfe, 0x73, 0x1d}}
  gAmdNbioBaseSSPPeiDepexPpiGuid         = {0xCE99DE68, 0x0F45, 0x4E31, {0x16, 0x17, 0x7D, 0xF4, 0x1F, 0xAF, 0xBD, 0xAF}}
  gAmdNbioBaseMDNPeiDepexPpiGuid         = {0xdec25b26, 0x7994, 0x4594, {0xaa, 0x04, 0xbd, 0xba, 0xb3, 0x8b, 0xd0, 0x98}}
  gAmdNbioSmuV8DepexPpiGuid              = {0x7ffe0bbc, 0xf7b4, 0x4e01, {0x98, 0xc5, 0xa8, 0xdc, 0x23, 0x2c, 0x6,  0xf8}}
  gAmdNbioSmuV9PeiDepexPpiGuid           = {0x9781c88a, 0x4046, 0x8286, {0xe2, 0x04, 0x6a, 0xb9, 0x8d, 0x38, 0x9a, 0x24}}
  gAmdNbioSmuV10PeiDepexPpiGuid          = {0xc3902c7a, 0x424a, 0x435d, {0x9a, 0x18, 0x7b, 0x9b, 0xa3, 0xeb, 0xd5, 0x96}}
  gAmdNbioSmuV11PeiDepexPpiGuid          = {0xD7257734, 0x772A, 0x422C, {0x7F, 0xFE, 0xD6, 0x75, 0x3C, 0x35, 0x02, 0xA1}}
  gAmdNbioSmuV11PeiBADepexPpiGuid        = {0xb957cf30, 0xbad7, 0x4613, {0xba, 0x1d, 0x76, 0x9b, 0xdb, 0xfc, 0xb8, 0xf0}}
  gAmdNbioSmuV11PeiGNDepexPpiGuid        = {0x4E304C0A, 0xD8FF, 0x4F6B, {0xA4, 0xA3, 0xAB, 0xA0, 0x2D, 0x6A, 0xBD, 0x74}}
  gAmdNbioSmuV11PeiVMRDepexPpiGuid       = {0xF9BFEF18, 0x2F6C, 0x41CA, {0x80, 0x82, 0x55, 0x6D, 0x9B, 0xDD, 0x5E, 0x87}}
  gAmdNbioSmuV12PeiDepexPpiGuid          = {0x335746E1, 0xB36A, 0x4DD7, {0x8D, 0x13, 0x57, 0xBE, 0x7B, 0x05, 0x25, 0x82}}
  gAmdNbioSmuV12CZNPeiDepexPpiGuid       = {0x283AC69A, 0x882C, 0x48F4, {0xB5, 0xE3, 0xA6, 0x2F, 0x95, 0x92, 0x73, 0x95}}
  gAmdNbioSmuV13PeiDepexPpiGuid          = {0x527b8a33, 0x9557, 0x44a2, {0x8e, 0x55, 0x22, 0x4e, 0x44, 0x59, 0xd0, 0xf}}
  gAmdNbioSmuV13PeiRPLDepexPpiGuid       = {0x941b16f7, 0x282b, 0x4221, {0x8e, 0x58, 0x2, 0x72, 0xa, 0xc6, 0x9d, 0x83}}
  gAmdNbioPcieZPPeiDepexPpiGuid          = {0x4e96fa1f, 0x475d, 0xd21e, {0x70, 0x1a, 0xfb, 0x85, 0x7d, 0xd2, 0x82, 0xb4}}
  gAmdNbioPcieRVPeiDepexPpiGuid          = {0x5eb022b2, 0x939f, 0x469f, {0x8d, 0x7c, 0xfa, 0x39, 0xc0, 0x89, 0xf0, 0x2 }}
  gAmdNbioPcieSSPPeiDepexPpiGuid         = {0x628FF19D, 0x8423, 0x45C7, {0x4B, 0x95, 0xEA, 0xAC, 0x8E, 0xD9, 0x69, 0xBA}}
  gAmdNbioPcieRNPeiDepexPpiGuid          = {0x33d6330a, 0x08bb, 0x4951, {0xb0, 0x75, 0x6a, 0xaa, 0x37, 0x9b, 0xf5, 0xab}}
  gAmdNbioPcieRMBPeiDepexPpiGuid         = {0xb0786c34, 0x196, 0x4ca3, {0xaa, 0x42, 0xc8, 0x29, 0x2e, 0x24, 0x28, 0x11}}
  gAmdNbioPcieRPLPeiDepexPpiGuid         = {0xa23247ee, 0x4e66, 0x471a, {0xa6, 0xe3, 0xed, 0x7f, 0x24, 0x55, 0x3, 0xf4}}
  gAmdNbioPcieMDNPeiDepexPpiGuid         = {0xfa842041, 0x85c5, 0x4fd8, {0x8b, 0x78, 0x50, 0x55, 0xb5, 0xae, 0x39, 0x39}}

  gAmdNbioIOMMUZPPeiDepexPpiGuid         = {0xf4ae2bec, 0xe8f1, 0x4715, {0x9b, 0x5,  0x61, 0x31, 0x14, 0xd6, 0x33, 0xe7}}
  gAmdNbioIOMMUSSPPeiDepexPpiGuid        = {0xF701DC2D, 0x3BB0, 0x45D6, {0x6B, 0xC2, 0xE7, 0xBD, 0xF5, 0x69, 0xD1, 0xA0}}
  gAmdNbioIOMMURNPeiDepexPpiGuid         = {0xd3681448, 0xce2a, 0x4b0c, {0x8b, 0x2c, 0x3a, 0xbc, 0x5b, 0x75, 0x54, 0xd2}}
  gAmdNbioIOMMURMBPeiDepexPpiGuid        = {0x30ba6df7, 0x3da6, 0x4325, {0x83, 0xbc, 0xa4, 0x1a, 0x5, 0xf, 0xd6, 0xea}}
  gAmdNbioIOMMURPLPeiDepexPpiGuid        = {0xfda3243, 0x86ec, 0x46bb, {0xa5, 0xec, 0x9b, 0xce, 0xad, 0x2e, 0xf, 0xed}}
  gAmdNbioIOMMUMDNPeiDepexPpiGuid        = {0xe466226d, 0xac15, 0x4faa, {0xa3, 0xbf, 0x8c, 0xdd, 0x44, 0x86, 0xe8, 0x51}}

  gAmdNbioPcieDpcStatusPpiGuid           = {0x7e70bbc8, 0x9e8d, 0x4bcb, {0x8a, 0x3f, 0xaf, 0x0c, 0xde, 0x67, 0x9d, 0x92}}
  gAmdNbioRNEarlyPhaseDepexPpiGuid       = {0xb05e6b60, 0x323f, 0x4bcf, {0x9a, 0x2b, 0x5d, 0xbf, 0xb0, 0x73, 0x01, 0xef}}
  gAmdNbioRMBEarlyPhaseDepexPpiGuid      = {0x86143e9, 0x8189, 0x4d57, {0xa6, 0x4, 0x8b, 0x56, 0x5e, 0x83, 0x42, 0x1b}}
  gAmdNbioRPLEarlyPhaseDepexPpiGuid      = {0xbd97402e, 0xf781, 0x46b4, {0x93, 0x74, 0x87, 0xe5, 0x74, 0x64, 0x10, 0xe1}}
  gAmdNbioMDNEarlyPhaseDepexPpiGuid      = {0x6d204ff0, 0x5c3c, 0x41d0, {0xb2, 0x53, 0x19, 0x6e, 0x3b, 0x17, 0x51, 0xd6}}

  gAmdNbioBaseBAPeiDepexPpiGuid          = {0x92e07e3f, 0xe010, 0x4225, {0xa6, 0x72, 0x56, 0xcf, 0x1b, 0x76, 0xd4, 0x32}}
  gAmdNbioPcieBAPeiDepexPpiGuid          = {0xbcae8637, 0x20d5, 0x4c64, {0x9e, 0x4c, 0x20, 0x8b, 0x5c, 0x2, 0x36, 0x2f}}
  gAmdNbioIOMMUBAPeiDepexPpiGuid         = {0xa96cb755, 0x143a, 0x45be, {0x9c, 0xba, 0x6d, 0xa4, 0x30, 0x1c, 0x46, 0x9e}}

  gAmdNbioBaseGNPeiDepexPpiGuid          = {0x50AA07B7, 0xA663, 0x4F1D, {0xA6, 0xEF, 0x8F, 0x51, 0x26, 0xD4, 0xE5, 0xE1}}
  gAmdNbioPcieGNPeiDepexPpiGuid          = {0xB871954F, 0xB9CC, 0x4150, {0xAD, 0x67, 0x76, 0x99, 0x0B, 0x46, 0x58, 0x68}}
  gAmdNbioIOMMUGNPeiDepexPpiGuid         = {0x639A2969, 0x115C, 0x4C04, {0xB8, 0x76, 0xF6, 0x65, 0xC9, 0xA7, 0xE0, 0x77}}

  gAmdNbioBaseFF3PeiDepexPpiGuid          = {0x8006b258, 0x7258, 0x4d90, {0x9b, 0xa8, 0xe4, 0xef, 0x72, 0x84, 0x7d, 0x0e}}
  gAmdNbioPcieFF3PeiDepexPpiGuid          = {0x919f2488, 0xb5aa, 0x408d, {0x90, 0x4e, 0xab, 0x17, 0x5a, 0xfb, 0x73, 0x07}}
  gAmdNbioIOMMUFF3PeiDepexPpiGuid         = {0xd1f7120e, 0x7853, 0x44b7, {0xa2, 0xb7, 0xc1, 0x84, 0x25, 0x6f, 0xdc, 0x36}}
  gAmdNbioFF3EarlyPhaseDepexPpiGuid       = {0x04081781, 0x5665, 0x4173, {0x96, 0x6a, 0x05, 0xae, 0xdb, 0xa3, 0x6d, 0x88}}

  gAmdNbioRSPeiDepexPpiGuid               = {0X8EDE3B75, 0XEAF5, 0X4755, {0X97, 0X33, 0X7B, 0X49, 0X65, 0X8C, 0X95, 0X8B}}
  gAmdNbioPHXPeiDepexPpiGuid              = {0xf2caed22, 0xa17f, 0x48ed, {0xa0, 0x5b, 0x50, 0x75, 0xf0, 0xe3, 0x27, 0xd5}}
  gAmdNbioMI3PeiDepexPpiGuid              = {0xBF3B7224, 0x7F1D, 0x4B32, {0x92, 0x28, 0xD5, 0x69, 0xEA, 0x68, 0x75, 0x10}}
  gAmdNbioSTXKRKPeiDepexPpiGuid           = {0x95781e47, 0xfe5f, 0x4c96, {0x90, 0xe7, 0xbf, 0x4f, 0xcb, 0xe6, 0x07, 0xd8}}
  gAmdNbioSTPPeiDepexPpiGuid              = {0x85c757de, 0x0b73, 0x4d60, {0xa7, 0x5a, 0x20, 0xc9, 0xb3, 0x2f, 0x6f, 0x50}}
  gAmdNbioBRHPeiDepexPpiGuid              = {0Xca331070, 0X01e6, 0X4755, {0X61, 0Xbf, 0X3b, 0Xdf, 0Xa9, 0Xea, 0X9a, 0X2b}}
  gAmdNbioSTXHPeiDepexPpiGuid            = {0x39a0c2fd, 0x47c8, 0x42a7, {0xb4, 0x9b, 0x4f, 0x1d, 0x01, 0x1e, 0x97, 0xe1}}
  gAmdNbioKRKPeiDepexPpiGuid              = {0x61133460, 0xf55b, 0x4e3d, {0x9a, 0xaf, 0xfd, 0x3d, 0x04, 0xc0, 0xf7, 0x80}}

  ## Gnb Ppis
  gAmdNbioSmuServicesPpiGuid             = {0xea335e48, 0x7275, 0x4d2b, {0x82, 0x76, 0x55, 0xba, 0x55, 0x31, 0xd7, 0xd7}}
  gAmdNbioPcieServicesPpiGuid            = {0x5af9cd51, 0x36ee, 0x4bab, {0xb4, 0x14, 0xaa, 0xa0, 0x54, 0x1c, 0x5b, 0x38}}
#  gAmdNbioDebugServicesPpiGuid          = {0xea89bf6,  0xded1, 0x4705, {0xa6, 0xde, 0x37, 0x91, 0xd2, 0x71, 0x96, 0x49}}
  gAmdNbioBaseServicesRnPpiGuid          = {0x92d11f11, 0xd4b0, 0x4dc1, {0x9a, 0x41, 0x17, 0xce, 0x5d, 0x51, 0xa2, 0x79}}
  gAmdNbioBaseServicesRmbPpiGuid         = {0x137f64a7, 0x3b41, 0x4d32, {0x9c, 0xc9, 0xe4, 0x5e, 0xbc, 0xf2, 0x12, 0x33}}
  gAmdNbioBaseServicesRplPpiGuid         = {0xf78b20c2, 0x4f25, 0x42c6, {0x95, 0x5c, 0x5e, 0x9c, 0xaf, 0xd2, 0xb4, 0x71}}
  gAmdNbioBaseServicesZpPpiGuid          = {0x556e2ead, 0xb0a2, 0x43d9, {0xa8, 0xf2, 0xf3, 0x36, 0x85, 0xaa, 0x21, 0xba}}
  gAmdNbioBaseServicesSspPpiGuid         = {0x4F12525F, 0xA771, 0x4381, {0x31, 0xBE, 0xA2, 0xC3, 0x1D, 0x67, 0x0B, 0x8C}}
  gAmdNbioBaseServicesGnPpiGuid          = {0x7E5366B3, 0x5EDD, 0x4A91, {0xB7, 0x87, 0x7E, 0xFF, 0x32, 0x8C, 0xA0, 0x5C}}
  gAmdNbioBaseServicesBaPpiGuid          = {0xe5719ef0, 0xcd22, 0x4b63, {0x89, 0xc4, 0x90, 0x74, 0x83, 0x5e, 0xa9, 0xe7}}
  gAmdNbioBaseServicesPhxPpiGuid         = {0xd37f008f, 0x6dad, 0x409b, {0xaa, 0x99, 0xf3, 0x17, 0xc1, 0x05, 0x5c, 0xae}}
  gAmdNbioBaseServicesStxKrkPpiGuid      = {0x3eb2d232, 0xd622, 0x4557, {0x8e, 0xbf, 0xb6, 0xdf, 0x26, 0x3a, 0xf5, 0x4d}}
  gAmdNbioBaseServicesStxhPpiGuid        = {0x285c3f46, 0x63fe, 0x4fcd, {0x8b, 0xa1, 0x83, 0x2e, 0x02, 0x72, 0xd2, 0xe2}}
  gAmdNbioBaseEarlyBeforeCpuPpiGuid      = {0xc568822a, 0xc89a, 0x4282, {0x8a, 0xa2, 0x18, 0x52, 0xde, 0x0,  0x95, 0x2e}}
  gAmdNbioGfxPpiGuid                     = {0xed92898e, 0xb59a, 0x4647, {0x88, 0xcc, 0xdb, 0xeb, 0xa3, 0xb4, 0xa6, 0xcd}}
  gAmdPciEnumerationCompletePpiGuid      = {0xe37b5bdf, 0xa62d, 0x45b9, {0xab, 0x63, 0x78, 0x87, 0x41, 0xf6, 0x3f, 0x1f}}
  gAmdNbioIommuFeaturePpiGuid            = {0xdc68ab1b, 0x4a97, 0x4954, {0x7a, 0xa9, 0x57, 0x3f, 0x3f, 0x71, 0x50, 0x06}}

[PcdsFeatureFlag]

[PcdsFixedAtBuild]
  #Gnb Common Build Options
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgScsSupport|FALSE|BOOLEAN|0x00021000
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgUmaSteering|1|UINT8|0x00021001
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGmcPowerGating|1|UINT8|0x00021002
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgGmcClockGating|FALSE|BOOLEAN|0x00021003
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgOrbDynWakeEnable|FALSE|BOOLEAN|0x00021004
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgOrbClockGatingEnable|FALSE|BOOLEAN|0x00021005
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIocLclkClockGatingEnable|FALSE|BOOLEAN|0x00021006
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgBapmSupport|FALSE|BOOLEAN|0x00021007
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDcTdpEnable|FALSE|BOOLEAN|0x00021008
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgGnbNumDisplayStreamPipes|1|UINT8|0x00021009
  #Gnb CZ Build Options
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgChubClockGating|FALSE|BOOLEAN|0x00021010
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgAcpClockGating|FALSE|BOOLEAN|0x00021011
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgAxgDisable|FALSE|BOOLEAN|0x00021012
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgPcieHwInitPwerGating|1|UINT8|0x00021013
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgAriSupport|FALSE|BOOLEAN|0x00021014
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgSpgClockGatingEnable|FALSE|BOOLEAN|0x00021015
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgAcgAzClockGatingEnable|FALSE|BOOLEAN|0x00021016
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgOrbTxMemPowerGating|1|UINT8|0x00021017
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgOrbRxMemPowerGating|1|UINT8|0x00021018
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgParityErrorConfiguration|FALSE|BOOLEAN|0x00021019
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgSpgMemPowerGatingEnable|1|UINT8|0x0002101A
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgAcgAzMemPowerGatingEnable|1|UINT8|0x0002101B
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgBifMemPowerGatingEnable|1|UINT8|0x0002101C
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgSDMAMemPowerGatingEnable|FALSE|BOOLEAN|0x0002101D
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgPcieTxpwrInOff|1|UINT8|0x0002101E
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgPcieRxpwrInOff|1|UINT8|0x0002101F
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgSmuDeterminismAmbient|1|UINT32|0x00021020
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgPSIEnable|FALSE|BOOLEAN|0x00021021

  #Gnb Build time Options for Common System Options.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdIgfxModeAsPcieEp|TRUE|BOOLEAN|0x00021030                   #///< Itegrated Gfx mode Pcie EP or Legacy
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgGnbLinkReceiverDetectionPooling|60000|UINT32|0x00021031 #///< Receiver pooling detection time in us.                               (60 * 1000)
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgGnbLinkL0Pooling|60000|UINT32|0x00021032                #///< Pooling for link to get to L0 in us                                  (60 * 1000)
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgGnbLinkGpioResetAssertionTime|2000|UINT32|0x00021033    #///< Gpio reset assertion time in us                                      (2 * 1000)
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgGnbLinkResetToTrainingTime|2000|UINT32|0x00021034       #///< Time duration between deassert GPIO reset and release training in us (2 * 1000)
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgGnbTrainingAlgorithm|0|UINT8|0x00021035                 #///< distribution of training across interface calls                      (PcieTrainingStandard)
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgPciePowerGatingFlags|0|UINT8|0x00021036                 #///< Pcie Power gating flags
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcieAspmBlackListEnable|TRUE|BOOLEAN|0x00021037               #///< Control Pcie Aspm Black List
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGnbLoadRealFuseTable|TRUE|BOOLEAN|0x0002103A               #///< Support for fuse table loading
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIommuMMIOAddressReservedEnable|FALSE|BOOLEAN|0x00021100 #///< 1: Enable Iommu MMIO reserved from GNB driver. 0:Disable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIoApicMMIOAddressReservedEnable|FALSE|BOOLEAN|0x00021101 #///< 1: Enable Ioapic MMIO reserved from GNB driver. 0:Disable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIoApicIdPreDefineEn|FALSE|BOOLEAN|0x00021102            #///< TRUE: Enable assign IOAPIC ID at PEI phase
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIoApicIdBase|0xF1|UINT8|0x00021103                      #///< Base NBIO IOAPIC ID. ID assigned start from this value
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIohcNonPCIBarInitSmu|TRUE|BOOLEAN|0x00021200            #///< Configure non pci device bar for SMU
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIohcNonPCIBarInitDbg|FALSE|BOOLEAN|0x00021201           #///< Configure non pci device bar for DBG
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIohcNonPCIBarInitFastReg|FALSE|BOOLEAN|0x00021202       #///< Configure non pci device bar for FastReg
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIohcNonPCIBarInitFastRegCtl|FALSE|BOOLEAN|0x00021203    #///< Configure non pci device bar for FastRegCtl
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIohcNonPCIBarInitIommuVf|FALSE|BOOLEAN|0x00021204       #///< Configure non pci device bar for Iommu VF
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIohcNonPCIBarInitIommuVfCtl|FALSE|BOOLEAN|0x00021205    #///< Configure non pci device bar for Iommu VF Control

[PcdsDynamic]
  # Gnb UserOptions
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgGnb3dStereoPinIndex|0|UINT8|0x00041001
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgGpuFrequencyLimit|0|UINT32|0x00041002
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgLvdsSpreadSpectrum|0|UINT16|0x00041003
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgLvdsSpreadSpectrumRate|0|UINT16|0x00041004
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgLvdsPowerOnSeqDigonToDe|0|UINT8|0x00041005
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgLvdsPowerOnSeqDeToVaryBl|0|UINT8|0x00041006
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgLvdsPowerOnSeqDeToDigon|0|UINT8|0x00041007
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgLvdsPowerOnSeqVaryBlToDe|0|UINT8|0x00041008
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgLvdsPowerOnSeqOnToOffDelay|0|UINT8|0x00041009
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgLvdsPowerOnSeqVaryBlToBlon|0|UINT8|0x0004100A
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgLvdsPowerOnSeqBlonToVaryBl|0|UINT8|0x0004100B
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgLvdsMaxPixelClockFreq|0|UINT16|0x0004100C
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgLcdBitDepthControlValue|0|UINT32|0x0004100D
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgLvds24bbpPanelMode|0|UINT8|0x0004100E
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgLvdsMiscControlValue|0|UINT8|0x0004100F
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgPcieRefClkSpreadSpectrum|375|UINT16|0x00041010
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgGnbRemoteDisplaySupport|FALSE|BOOLEAN|0x00041011
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgLvdsMiscVoltAdjustment|0|UINT8|0x00041012
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDisplayMiscControlValue|0|UINT8|0x00041013
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDpFixedVoltSwingType|0|UINT8|0x00041014
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgEDPv14VSMode|0|UINT8|0x00041015
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgExtHDMIReDrvSlvAddr|0|UINT8|0x00041016
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgExtHDMIReDrvRegNum|0|UINT8|0x00041017
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgExtHDMIRegSetting|0|UINT64|0x00041018
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgGnbIoapicAddress|0|UINT64|0x00041019
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgMaxNumAudioEndpoints|0xFF|UINT8|0x0004101A
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgiGpuVgaMode|0|UINT8|0x0004101B
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgPcieAcsCapability|0|UINT8|0x0004101C
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgGnbIoapicId|0|UINT8|0x0004101D
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgFchIoapicId|0|UINT8|0x0004101E
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgPCIeAEREnable|FALSE|BOOLEAN|0x00041020
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgAerCtoLogCapable|FALSE|BOOLEAN|0x00041067

  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdIommuL1ClockGatingEnable|TRUE|BOOLEAN|0x00041021
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdIommuL2ClockGatingEnable|TRUE|BOOLEAN|0x00041022
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIommuL1MemPowerGating|FALSE|BOOLEAN|0x00041023
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIommuL2MemPowerGating|FALSE|BOOLEAN|0x00041024
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdIOHCClkGatingSupport|TRUE|BOOLEAN|0x00041025
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgSstunlClkGating|TRUE|BOOLEAN|0x00041026
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgSataPhyTuning|0|UINT8|0x00041027
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBClockGatingEnable|FALSE|BOOLEAN|0x00041028
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgHotplugMode|0|UINT8|0x00041029
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIOMMUDynamicPgEnable|TRUE|BOOLEAN|0x0004102A
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIOMMUL2MemoryPGEnable|TRUE|BOOLEAN|0x0004102B
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgPCIePHYPowerGating|FALSE|BOOLEAN|0x0004102C
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgPCIeLTREnable|TRUE|BOOLEAN|0x0004102D
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdIOHCPgEnable|TRUE|BOOLEAN|0x0004102E
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgHotplugPollInterval|50|UINT32|0x0004102F
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPciePhyClkCntrlEnable|TRUE|BOOLEAN|0x00041030
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNbioCTOtoSC|FALSE|BOOLEAN|0x00041031
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNbifPgClkGating|TRUE|BOOLEAN|0x00041032
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNbifMgcgClkGating|TRUE|BOOLEAN|0x00041033
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgSyshubMgcgClkGating|TRUE|BOOLEAN|0x00041034
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDynamicLanesPowerState|0|UINT8|0x00041035
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgAutoSpeedChangeEnable|0|UINT8|0x00041036
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdTPHCompleterEnable|1|UINT8|0x00041037
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdWidthBasedSpc|TRUE|BOOLEAN|0x00041038
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIOMMUL1MemoryPGEnable|TRUE|BOOLEAN|0x00041039
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgPCIeClkReqMode|0|UINT8|0x0004103A   #///< 0x0 - Enhanced, Controlled by a (counter-based) instance of AMD FCH.
                                                                                #///< 0x1 - Legacy, Virtual-wire based signal provided to FCH
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdIOAGRPgEnable|TRUE|BOOLEAN|0x0004103B
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdNakWorkaroundEnable|FALSE|BOOLEAN|0x0004103C
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNbioHdtBreakPoint|0|UINT32|0x0004103D  #///< 0 - Disabled
                                                                                   #///< BIT0 - After Pcie Reset
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGen5CfgPrecodeRequestEnable|0xFF|UINT8|0x0004103E

  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgHotplugUMBSupport|TRUE|BOOLEAN|0x0004103F
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgHotplugForceGlinksPowerEn|FALSE|BOOLEAN|0x00041068
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgHotplugForcePlinksPowerEn|FALSE|BOOLEAN|0x00041069

  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTDMAEnable|FALSE|BOOLEAN|0x00041044
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIommuSocket0Nbio0Enable|TRUE|BOOLEAN|0x00041045
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIommuSocket0Nbio1Enable|TRUE|BOOLEAN|0x00041046
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIommuSocket0Nbio2Enable|TRUE|BOOLEAN|0x00041047
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIommuSocket0Nbio3Enable|TRUE|BOOLEAN|0x00041048
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIommuSocket1Nbio0Enable|TRUE|BOOLEAN|0x00041049
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIommuSocket1Nbio1Enable|TRUE|BOOLEAN|0x0004104a
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIommuSocket1Nbio2Enable|TRUE|BOOLEAN|0x0004104b
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIommuSocket1Nbio3Enable|TRUE|BOOLEAN|0x0004104c
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIommuAvicSupport|FALSE|BOOLEAN|0x0004104d
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdNbioAcpPowerOn|FALSE|BOOLEAN|0x0004104E
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNbifDev0Epf5En|TRUE|BOOLEAN|0x0004104F
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNbifDev1Epf1En|TRUE|BOOLEAN|0x00041050
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNbifDev2Epf2En|TRUE|BOOLEAN|0x00041051
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgTbtCompleterEn|TRUE|BOOLEAN|0x00041052
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgTbtRequesterEn|TRUE|BOOLEAN|0x00041053
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdEnableDMArOnS3Path|TRUE|BOOLEAN|0x00041056
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNbifDev0Epf7En|TRUE|BOOLEAN|0x00041057
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdDisplayFixVoltageSwing|0|UINT8|0x00041058
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdSteeringTag|0x0|UINT16|0x00041059
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdForceSteering|0x0|UINT8|0x0004105A
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdSteeringValue|0x0|UINT8|0x0004105B
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdForceWrPh|0x0|UINT8|0x0004105C
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdForceWrSteering|0x0|UINT8|0x0004105D
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdWrStTagMode|0x0|UINT8|0x0004105E
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdForceWrStEntry|0x0|UINT8|0x0004105F
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdTphReqrCntl|0x0|UINT8|0x00041060
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioSataGen1Settings|0x0|UINT32|0x00041061
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioSataGen2Settings|0x0|UINT32|0x00041062
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioSataGen3Settings|0x0|UINT32|0x00041063
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgPCIeCntlLcPresetMaskGen4|0x0|UINT32|0x00041064
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgPCIeCntlLcPresetMaskGen3|0x0|UINT32|0x00041065
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgUseFastTxClk|FALSE|BOOLEAN|0x00041066
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgPCIeLegacyClkPmInL1|0xF|UINT8|0x00041070
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgPCIeLcCntl2LcAllowPdwnInL23|0xF|UINT8|0x00041071
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgPCIeTPowerOnValue|0x0|UINT8|0x00041072
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIOMMUL1IoagrDynamicPgEn|TRUE|BOOLEAN|0x00041073
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIOMMUL1Pcie0DynamicPgEn|TRUE|BOOLEAN|0x00041074
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIOMMUL1Pcie2DynamicPgEn|TRUE|BOOLEAN|0x00041075
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIOMMUL2ImuDynamicPgEn|TRUE|BOOLEAN|0x00041076
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIOMMUL1muIoagrMemoryPGEn|FALSE|BOOLEAN|0x00041077
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIOMMUL1muPcie0MemoryPGEn|FALSE|BOOLEAN|0x00041078
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIOMMUL1muPcie2MemoryPGEn|FALSE|BOOLEAN|0x00041079
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIOMMUL2ImuMemoryPGEn|TRUE|BOOLEAN|0x0004107A
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdIohcClkGatingControl|0|BOOLEAN|0x0004107B  #///< When this PCD is set to manual,it can control individual clock gating settings
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIOMMUMultiContextSupport|FALSE|BOOLEAN|0x0004107C

  # GFX HDMI re-timer/re-driver
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDp0RetimerRedriverTable|0|UINT32|0x00041040
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDp1RetimerRedriverTable|0|UINT32|0x00041041
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDp2RetimerRedriverTable|0|UINT32|0x00041042
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDp3RetimerRedriverTable|0|UINT32|0x00041043
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgSysInfoGpuCapsDdsSupport|FALSE|BOOLEAN|0x00041054
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgSysInfoGpuCapsBr3SdrSupport|FALSE|BOOLEAN|0x00041055

  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgAbmSupport|0|UINT8|0x00041101
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDynamicRefreshRate|0|UINT8|0x00041102
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgLcdBackLightControl|200|UINT16|0x00041103
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgAcpPowerGating|FALSE|BOOLEAN|0x00041104
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgGnbAzI2sBusSelect|1|UINT32|0x00041105     #PcdCfgGnbAzI2sBusSelect
                                                                                      # 00h            ; < I2sBus
                                                                                      # 01h            ; < Azalia
                                                                                      # 02h            ; < Not valid value, used to verify input
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgGnbAzI2sBusPinConfig|0|UINT32|0x00041106  #AZ I2SBUS pin configuration
                                                                                      # 00h            ; < 4Tx4Rx and Bluetooth
                                                                                      # 01h            ; < 2Tx4Rx and Bluetooth
                                                                                      # 02h            ; < 6Tx4Rx and Bluetooth
                                                                                      # 03h            ; < Not valid value, used to verify input
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgAzaliaSsid|0|UINT32|0x00041107
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgAzaliaCodecVerbTable|0|UINT32|0x00041108
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdBridgeIdRemappingTable|{0xFF}|VOID*|0x00041109


  # Specify the default values for the VRM controlling the VDD plane.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgPkgPwrLimitAC|0|UINT32|0x0004110B
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgPkgPwrLimitDC|0|UINT32|0x0004110C
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgBatteryBoostTune|0|UINT32|0x0004110D
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgThermCtlLimit|0|UINT32|0x0004110E


  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgBTCEnable|TRUE|BOOLEAN|0x00041111
  ### @brief Selects to enable the NTB feature. This is the base PCD which needs to be enabled before using any of the other NTB related PCDs
  ### @brief Permitted Choices: (Type: Boolean)(Default: Disabled)
  ### @li False - Disable the NTB feature.
  ### @li TRUE - Enable NTB. This activates the following controls.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBEnable|FALSE|BOOLEAN|0x00041112
  ### @brief Socket and Die combination where to enable NTB for this link endpoint. The value of this PCD should be set to the Socket and Die pair on which NTB needs to be enabled.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x00)
  ### @li 0 - Socket0 Die0.
  ### @li 1 - Socket0 Die1.
  ### @li 2 - Socket0 Die2.
  ### @li 3 - Socket0 Die3.
  ### @li 4 - Socket1 Die0.
  ### @li 5 - Socket1 Die1.
  ### @li 6 - Socket1 Die2.
  ### @li 7 - Socket1 Die3.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBLocation|0|UINT8|0x00041113
  ### @brief PCIe Core on which to enable NTB for this end of the link. The value of this PCD should be set to the PCIe Core of the particular Socket and Die combination on which NTB needs to be enabled.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x00)
  ### @brief Values other that those listed above may result in undefined operations.
  ### @li 0 - PCIe Core 0.
  ### @li 16 - PCIe Core 1.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBPcieCoreSel|0|UINT8|0x00041114
  ### @brief Select NTB Mode. User can select whether they want to enable NTB as primary or secondary.  Permitted Choices: (Type: Value)(Default: 0x00)
  ### @li 0 - Disabled.
  ### @li 1 - NTB is primary.
  ### @li 2 - NTB is secondary.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBMode|0|UINT8|0x00041115
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBLinkSpeed|0|UINT8|0x00041116
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgPcieAspmBlackListEnableEnable|TRUE|BOOLEAN|0x00041117
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgMaxPayloadEnable|TRUE|BOOLEAN|0x00041118
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdIvrsRelativeAddrNamesSupport|FALSE|BOOLEAN|0x00041119      #///< Support for relative address names
  ### @brief This value controls whether AGESA will publish the IVRS table for IOMMUs.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li TRUE - AGESA will publish the IVRS if IOMMUs are enabled.
  ### @li FALSE - No IVRS table will be published.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdIvrsControl|TRUE|BOOLEAN|0x0004111B
  ### @brief  Enable NTB on Socket-0 P0 Link. By default NTB is disabled. In case the users want to enable NTB on P0 link, they should set this PCD to True. This is the base PCD which needs to be enabled before using any of the other NTB related PCDs for link P0.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li FALSE - NTB is disabled.
  ### @li TRUE - NTB is enabled for S0-P0.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBP0P0|FALSE|BOOLEAN|0x0004111C
  ### @brief NTB Start Lane on Socket-0 P0 Link. NTB can be enabled on the highest width lanes (x4, x8, x16) on P0. This PCD allows the user to select the first lane of P0 on which NTB will be enabled.
  ### @brief Permitted Choices: (Type: Value)(Default: 00)
  ### @li 0 - x16 lane for NTB on P0..
  ### @li 1..15 - start lane for the link. See width below. Keep in mind the alignment and size requirements of the lane as described in the PPR.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBStartLaneP0P0|0|UINT8|0x0004111D
  ### @brief NTB End Lane on Socket-0 P0 Link. This PCD allows the user to select the last lane of P0 on which NTB will be enabled.
  ### @brief Permitted Choices: (Type: Value)(Default: 15)
  ### @li 0..14 - Ending lane for the link. Keep in mind the alignment and size requirements for the lane as described in PPR.
  ### @li 15 - selects x16 lane for NTB on P0.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBEndLaneP0P0|15|UINT8|0x0004111E
  ### @brief Link Speed for Socket-0 P0 Link. NTB can work at PCIe Gen1, Gen2, Gen3 and Gen4 speeds. The user can select the desired speed using this PCD.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x03)
  ### @li 1 - Gen1.
  ### @li 2 - Gen2.
  ### @li 3 - Gen3.
  ### @li 4 - Gen4.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBLinkSpeedP0P0|3|UINT8|0x0004111F
  ### @brief Select NTB Mode. User can select whether they want to enable NTB as primary or secondary.  Permitted Choices: (Type: Value)(Default: 0x00)
  ### @li 0 - Disabled.
  ### @li 1 - NTB is primary.
  ### @li 2 - NTB is secondary.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBModeP0P0|0|UINT8|0x00041120
  ### @brief  Enable NTB on Socket-0 P1 Link. By default NTB is disabled. In case the users want to enable NTB on P1 link, they should set this PCD to True. This is the base PCD which needs to be enabled before using any of the other NTB related PCDs for link P1.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li FALSE - NTB is disabled.
  ### @li TRUE - NTB is enabled for S0-P1.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBP0P1|FALSE|BOOLEAN|0x00041121
  ### @brief NTB Start Lane on Socket-0 P1 Link. NTB can be enabled on the highest width lanes (x4, x8, x16) on P1. This PCD allows the user to select the first lane of P1 on which NTB will be enabled.
  ### @brief Permitted Choices: (Type: Value)(Default: 32)
  ### @li 32 - x16 lane for NTB on P1.
  ### @li 33..47 - start lane for the link. See width below. Keep in mind the alignment and size requirements of the lane as described in the PPR.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBStartLaneP0P1|32|UINT8|0x00041122
  ### @brief NTB End Lane on Socket-0 P1 Link. This PCD allows the user to select the last lane of P1 on which NTB will be enabled.
  ### @brief Permitted Choices: (Type: Value)(Default: 15)
  ### @li 32..46 - Ending lane for the link. Keep in mind the alignment and size requirements for the lane as described in PPR.
  ### @li 47 - selects x16 lane for NTB on P1.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBEndLaneP0P1|47|UINT8|0x00041123
  ### @brief Link Speed for Socket-0 P1 Link. NTB can work at PCIe Gen1, Gen2, Gen3 and Gen4 speeds. The user can select the desired speed using this PCD.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x03)
  ### @li 1 - Gen1.
  ### @li 2 - Gen2.
  ### @li 3 - Gen3.
  ### @li 4 - Gen4.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBLinkSpeedP0P1|3|UINT8|0x00041124
  ### @brief Select NTB Mode. User can select whether they want to enable NTB as primary or secondary.  Permitted Choices: (Type: Value)(Default: 0x00)
  ### @li 0 - Disabled.
  ### @li 1 - NTB is primary.
  ### @li 2 - NTB is secondary.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBModeP0P1|0|UINT8|0x00041125
  ### @brief  Enable NTB on Socket-0 P2 Link. By default NTB is disabled. In case the users want to enable NTB on P2 link, they should set this PCD to True. This is the base PCD which needs to be enabled before using any of the other NTB related PCDs for link P2.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li FALSE - NTB is disabled.
  ### @li TRUE - NTB is enabled for S0-P2.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBP0P2|FALSE|BOOLEAN|0x00041126
  ### @brief NTB Start Lane on Socket-0 P2 Link. NTB can be enabled on the highest width lanes (x4, x8, x16) on P2. This PCD allows the user to select the first lane of P2 on which NTB will be enabled.
  ### @brief Permitted Choices: (Type: Value)(Default: 80)
  ### @li 80 - x16 lane for NTB on P2.
  ### @li 81..95 - start lane for the link. See width below. Keep in mind the alignment and size requirements of the lane as described in the PPR.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBStartLaneP0P2|80|UINT8|0x00041127
  ### @brief NTB End Lane on Socket-0 P2 Link. This PCD allows the user to select the last lane of P2 on which NTB will be enabled.
  ### @brief Permitted Choices: (Type: Value)(Default: 95)
  ### @li 80..94 - Ending lane for the link. Keep in mind the alignment and size requirements for the lane as described in PPR.
  ### @li 95 - selects x16 lane for NTB on P2.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBEndLaneP0P2|95|UINT8|0x00041128
  ### @brief Link Speed for Socket-0 P2 Link. NTB can work at PCIe Gen1, Gen2, Gen3 and Gen4 speeds. The user can select the desired speed using this PCD.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x03)
  ### @li 1 - Gen1.
  ### @li 2 - Gen2.
  ### @li 3 - Gen3.
  ### @li 4 - Gen4.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBLinkSpeedP0P2|3|UINT8|0x00041129
  ### @brief Select NTB Mode. User can select whether they want to enable NTB as primary or secondary.  Permitted Choices: (Type: Value)(Default: 0x00)
  ### @li 0 - Disabled.
  ### @li 1 - NTB is primary.
  ### @li 2 - NTB is secondary.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBModeP0P2|0|UINT8|0x0004112A
  ### @brief  Enable NTB on Socket-0 P3 Link. By default NTB is disabled. In case the users want to enable NTB on P3 link, they should set this PCD to True. This is the base PCD which needs to be enabled before using any of the other NTB related PCDs for link P3.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li FALSE - NTB is disabled.
  ### @li TRUE - NTB is enabled for S0-P3.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBP0P3|FALSE|BOOLEAN|0x0004112B
  ### @brief NTB Start Lane on Socket-0 P3 Link. NTB can be enabled on the highest width lanes (x4, x8, x16) on P3. This PCD allows the user to select the first lane of P3 on which NTB will be enabled.
  ### @brief Permitted Choices: (Type: Value)(Default: 112)
  ### @li 112 - x16 lane for NTB on P3.
  ### @li 113..127 - start lane for the link. See width below. Keep in mind the alignment and size requirements of the lane as described in the PPR.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBStartLaneP0P3|112|UINT8|0x0004112C
  ### @brief NTB End Lane on Socket-0 P3 Link. This PCD allows the user to select the last lane of P3 on which NTB will be enabled.
  ### @brief Permitted Choices: (Type: Value)(Default: 95)
  ### @li 112..126 - Ending lane for the link. Keep in mind the alignment and size requirements for the lane as described in PPR.
  ### @li 127 - selects x16 lane for NTB on P3.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBEndLaneP0P3|127|UINT8|0x0004112D
  ### @brief Link Speed for Socket-0 P3 Link. NTB can work at PCIe Gen1, Gen2, Gen3 and Gen4 speeds. The user can select the desired speed using this PCD.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x03)
  ### @li 1 - Gen1.
  ### @li 2 - Gen2.
  ### @li 3 - Gen3.
  ### @li 4 - Gen4.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBLinkSpeedP0P3|3|UINT8|0x0004112E
  ### @brief Select NTB Mode. User can select whether they want to enable NTB as primary or secondary.  Permitted Choices: (Type: Value)(Default: 0x00)
  ### @li 0 - Disabled.
  ### @li 1 - NTB is primary.
  ### @li 2 - NTB is secondary.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBModeP0P3|0|UINT8|0x0004112F
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBP0G0|FALSE|BOOLEAN|0x00041130
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBStartLaneP0G0|16|UINT8|0x00041131
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBEndLaneP0G0|31|UINT8|0x00041132
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBLinkSpeedP0G0|3|UINT8|0x00041133
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBModeP0G0|0|UINT8|0x00041134
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBP0G2|FALSE|BOOLEAN|0x00041135
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBStartLaneP0G2|96|UINT8|0x00041136
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBEndLaneP0G2|111|UINT8|0x00041137
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBLinkSpeedP0G2|3|UINT8|0x00041138
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBModeP0G2|0|UINT8|0x00041139
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBP1P0|FALSE|BOOLEAN|0x0004113A
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBStartLaneP1P0|0|UINT8|0x0004113B
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBEndLaneP1P0|15|UINT8|0x0004113C
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBLinkSpeedP1P0|3|UINT8|0x0004113D
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBModeP1P0|0|UINT8|0x0004113E
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBP1P2|FALSE|BOOLEAN|0x0004113F
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBStartLaneP1P2|80|UINT8|0x00041140
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBEndLaneP1P2|95|UINT8|0x00041141
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBLinkSpeedP1P2|3|UINT8|0x00041142
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBModeP1P2|0|UINT8|0x00041143
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBP1G0|FALSE|BOOLEAN|0x00041144
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBStartLaneP1G0|16|UINT8|0x00041145
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBEndLaneP1G0|31|UINT8|0x00041146
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBLinkSpeedP1G0|3|UINT8|0x00041147
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBModeP1G0|0|UINT8|0x00041148
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBP1G2|FALSE|BOOLEAN|0x00041149
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBStartLaneP1G2|96|UINT8|0x0004114A
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBEndLaneP1G2|111|UINT8|0x0004114B
  ### @brief Select Link Speed for the NTB. NTB can work at PCIe Gen1, Gen2, Gen3 and Gen4 speeds. The user can select the desired speed using this PCD.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x00)
  ### @li 0 - Max Speed.
  ### @li 1 - Gen1.
  ### @li 2 - Gen2.
  ### @li 3 - Gen3.
  ### @li 4 - Gen4.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBLinkSpeedP1G2|3|UINT8|0x0004114C
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBModeP1G2|0|UINT8|0x0004114D
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar23SizeP0P0|1|UINT32|0x0004114E
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar45SizeP0P0|1|UINT32|0x0004114F
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar23SizeP0P2|1|UINT32|0x00041150
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar45SizeP0P2|1|UINT32|0x00041151
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar23SizeP0G0|1|UINT32|0x00041152
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar45SizeP0G0|1|UINT32|0x00041153
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar23SizeP0G2|1|UINT32|0x00041154
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar45SizeP0G2|1|UINT32|0x00041155
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar23SizeP1P0|1|UINT32|0x00041156
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar45SizeP1P0|1|UINT32|0x00041157
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar23SizeP1P2|1|UINT32|0x00041158
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar45SizeP1P2|1|UINT32|0x00041159
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar23SizeP1G0|1|UINT32|0x0004115A
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar45SizeP1G0|1|UINT32|0x0004115B
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar23SizeP1G2|1|UINT32|0x0004115C
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar45SizeP1G2|1|UINT32|0x0004115D

  #Gnb SMU BLDCFG
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSmuFeatureControlDefinesExt|0x0|UINT32|0x00041200
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSmuFeatureControlDefines|0x0|UINT32|0x00041201   # SmuFeatureControlDefines == 0    ///< default SMU features are enabled in SMU drvier
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgSmuMessageEnable|FALSE|BOOLEAN|0x00041202
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSmuAllocateDramBufferSize|0x00|UINT32|0x00041203
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCakeDataLatDis|FALSE|BOOLEAN|0x00041207
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgTemperatureLimit|0x4D|UINT8|0x00041208
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgFrequencyAdjust|0x64|UINT16|0x00041209
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgHTFmaxFrequenecy|0x0|UINT16|0x0004120A
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgHTFmaxTemperature|0x0|UINT16|0x0004120B
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIgpuContorl|0x1|UINT8|0x0004120C    #0: Disable , Others: Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgSkipPspMessage|0x1|UINT8|0x0004120D #1: Skip
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgMTFmaxFrequenecy|0x0|UINT16|0x0004120E
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgMTFmaxTemperature|0x0|UINT16|0x0004120F
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgAgmLogDramSize|0x4|UINT16|0x00041210    # number of page, 1 page = 4096 Bytes
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSmuFeatureControlDefines64|0x0|UINT32|0x00041211
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCclkFminOverride|0x0|UINT16|0x00041215
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGfxclkFminOverride|0x0|UINT16|0x00041217
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMsgSetStbDramAddr|FALSE|BOOLEAN|0x00041218
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdUseFusedFmax|FALSE|BOOLEAN|0x00041219
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdDisplayIdleOptimizationEn|FALSE|BOOLEAN|0x0004121A
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSmuTdpFuseValue|0x0|UINT16|0x0004121B
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdGfxDpmNpu|TRUE|BOOLEAN|0x0004121C
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSupressAmdGfxDpmNpu|TRUE|BOOLEAN|0x0004121D
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIgpuMemBarMode|0x0|UINT8|0x0004121E    #0: Auto , 1: Legacy 2:Large 3:Resizeable

  # Gnb SMU 9 CBS debug options
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdDldoPsmMargin|0x00|UINT8|0x00041230
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCoreStretchThreshEn|0x00|UINT8|0x00041231
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCoreStretchThresh|0x00|UINT8|0x00041232
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdL3StretchThreshEn|0x00|UINT8|0x00041233
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdL3StretchThresh|0x00|UINT8|0x00041234
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdDldoBypass|0x1|UINT8|0x00041235
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXiSeparationEn|0x00|UINT8|0x00041236
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXiSeparationHigh|0x00|UINT8|0x00041237
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXiSeparationLow|0x00|UINT8|0x00041238
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAvfsCoeffTableOverride|0x00|UINT8|0x00041239
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdForceVddcrCpuVidEn|0x00|UINT8|0x0004123A
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdForceVddcrCpuVid|0x00|UINT8|0x0004123B
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdVddcrCpuVoltageMargin|0x00|UINT32|0x0004123C
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdFcwSlewFracL3FidTotalSteps|0x00|UINT16|0x0004123D
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdFcwSlewFracL3FidTotalStepsEn|0x00|UINT8|0x0004123E
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdForceCclkFrequencyEn|0x00|UINT8|0x0004123F
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdForceCclkFrequency|0x00|UINT32|0x00041240
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdUseTelemetryData|0x00|UINT8|0x00041241
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdWaitVidCompDis|0x00|UINT8|0x00041242
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdVoltageSlewRate|0x00|UINT8|0x00041243

  # Gen3/Gen4 Initial RX settings
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGen3InitRxParam1|0x00|UINT32|0x00041245
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGen3InitRxParam2|0x00|UINT32|0x00041246
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGen4InitRxParam1|0x00|UINT32|0x00041247
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGen4InitRxParam2|0x00|UINT32|0x00041248

  # Nbio DBGU options
  ### @brief When ExactMatch is true, the algorithm that maps engines to PCIe ports will only map an engine to a port if the size of the engine matches the size of the port exactly. When exactMatch is false, the algorithm that maps engines to PCIe ports will allow an engine to be mapped to a port that is the same size or larger than the engine.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li FALSE - use any engine where port will fit.
  ### @li TRUE - Match port and engine sizes.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPCIeExactMatchEnable|FALSE|BOOLEAN|0x00041249
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdNbioMemHoleSize|0x00|UINT32|0x0004124A
  # DXIO Phy Programming
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdDxioPhyValid|0x01|UINT8|0x0004124B
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdDxioPhyProgramming|0x01|UINT8|0x0004124C

  ### @brief Controls whether or not the three PCIe training timing overrides below are leveraged by DXIO firmware. Note that all of the timers below have default values that are expected to work in any typical training scenario, but the timers are available for debug purposes or to address specific platform issues in special circumstances.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)Affected controls:
  ### @li PcdPCIELinkResetToTrainingTime
  ### @li PcdPCIELinkReceiverDetectionPolling
  ### @li PcdPCIELinkL0Polling
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPcieDxioTimingControlEnable|FALSE|BOOLEAN|0x0004124D
  ### @brief Time (in microseconds) from release of PERST# to the start of the LTSSM (training state machine). The default value of the internal timer controlled by this PCD is 1 microsecond. This control is enabled by PcdPcieDxioTimingControlEnable.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x00)
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPCIELinkResetToTrainingTime|0|UINT32|0x0004124E
  ### @brief Controls the timeout (in microseconds) from the beginning of PCIe training, i.e. when the AMD LTSSM begins, until receiver detection is successful or times out. Internally this timer defaults to 500 ms. This control is enabled by PcdPcieDxioTimingControlEnable.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x00)
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPCIELinkReceiverDetectionPolling|0|UINT32|0x0004124F
  ### @brief Timeout (in microseconds) for the link to be in L0 state after receiver detection is complete. Internally the timer controlled by this PCD defaults to 500 mS. This control is enabled by PcdPcieDxioTimingControlEnable.
  ### @brief Permitted Choices: (Type: Value)(Default: 0x00)
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPCIELinkL0Polling|0|UINT32|0x00041250
  ### @brief Enables usage of 7-bit Steering Tag in SDP ReqAddr for writes with TPH
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMaster7bitSteeringTag|FALSE|BOOLEAN|0x00041610

  # Gnb SMU 9 CBS debug options [AVFS]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCorePsfFreq|0x00|UINT32|0x00041251
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCorePsfTemp|0x00|UINT32|0x00041252
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCorePsfSidd|0x00|UINT32|0x00041253
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCorePsfCac|0x00|UINT32|0x00041254
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCorePsfNumActiveCores|0x00|UINT32|0x00041255
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCorePsfOtherDie|0x00|UINT32|0x00041256
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCorePsfSigma|0x00|UINT32|0x00041257
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdL3PsfFreq|0x00|UINT32|0x00041258
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdL3PsfTemp|0x00|UINT32|0x00041259
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdL3PsfSidd|0x00|UINT32|0x0004125A
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdL3PsfCac|0x00|UINT32|0x0004125B
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdL3PsfNumActiveCores|0x00|UINT32|0x0004125C
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdL3PsfOtherDie|0x00|UINT32|0x0004125D
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdL3PsfSigma|0x00|UINT32|0x0004125E

  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSocDcBtcEnable|TRUE|BOOLEAN|0x0004125F
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAcBtc|0x00|UINT8|0x00041264
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdDcBtc|0x01|UINT8|0x00041265
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdFllBtcEnable|TRUE|BOOLEAN|0x00041266

  ### @brief This option controls whether or not SMU FW performs XGMI Re-adaptation during BIOS POST. The phy used for xGMI links needs to adjust analog parameters to work at its optimum point. It is called 'adaptation'.
  ### @brief Once early in the boot process, DXIO FW adapts xGMI links; but as time passes, temperature/humidity changes the early boot analog parameters are not good enough. This causes bit error, and if not cured, eventually results in an MCE once certain a threshold is passed.
  ### @brief DXIO FW has a mechanism to overcome this, called "re-adaptation": every 1ms, SMU calls a DXIO FW routine, checks for bit errors on the xGMI links. If it finds enough errors, it tries to repeat the adaption sequence. The xGMI link is brought down (for up to 400us); the re-adaptation is performed then the link is set back to normal. This task is defined as high priority in SMU scheduler and may take up to 2ms.
  ### @brief This re-adaptation process may cause some system instability in very specific cases (due to the time the link is not available). No issues have been observed and it is highly recommended to keep the feature ON. However, if in certain cases, if this patch is found to destabilize the machine, the user may turn it OFF as a workaround. As turning the feature OFF may compromise the xGMI link quality/performance and cause MCE, users are recommended to report the case back to AMD and do not keep the feature turned OFF as a fix.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li FALSE - Only the initial adaptation process is used.
  ### @li TRUE - The adaptation process will be repeated as needed to avoid extra MCA errors.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdXgmiReadaptation|FALSE|BOOLEAN|0x00041267
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGppAtomicOps|0x01|UINT8|0x00041268
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGfxAtomicOps|0x01|UINT8|0x00041269
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdUSBMsiXCapEnable|FALSE|BOOLEAN|0x00041270


  #GNB SMU10
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdVddOffVidCtrl|FALSE|BOOLEAN|0x00041271
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdVddOffVid|0x00|UINT32|0x00041272
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdUlvVidCtrl|FALSE|BOOLEAN|0x00041273
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdUlvVidOffset|0x00|UINT32|0x00041274

  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdVddcrSocVoltageMargin|0x00|UINT32|0x00041275

  ### @brief This control configures the data path width when the PCIe Gen1 speed. For Family 17h Models 00h see erratum #1080; customers are advised to read the erratum but not to change the default unless they are pretty sure they are having a PCIe
  ### @brief Permitted Choices: (Type: Boolean)(Default: False)
  ### @li FALSE - 1 symbol per Clock Data Path.
  ### @li TRUE - 2 symbols per Clock Data Path.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdNbioSpcMode2P5GT|FALSE|BOOLEAN|0x00041276
  ### @brief This control configures the data path width when the PCIe Gen2 speed. For Family 17h Models 00h see erratum #1080; customers are advised to read the erratum but not to change the default unless they are pretty sure they are having a PCIe
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li FALSE - 1 symbol per Clock Data Path.
  ### @li TRUE - 2 symbols per Clock Data Path.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdNbioSpcMode5GT|FALSE|BOOLEAN|0x00041277

  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdEnableSmuPostCode|FALSE|BOOLEAN|0x00041278
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdRichtekVrmPsi0Workaround|FALSE|BOOLEAN|0x00041279
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdDcBtcVid|0x86|UINT8|0x0004127A
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdDcBtcErrorOfsetVoltageMargin|0x00|UINT8|0x0004127B
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdEnableSmuPostCodeLevel|0|UINT32|0x0004127C
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPcoBixbySupport|FALSE|BOOLEAN|0x0004127D
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdHTPsmMargin|0|UINT32|0x0004127E
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdRnBixbySupport|FALSE|BOOLEAN|0x0004127F

  #Managed OC Mode
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMocPPTLimit|0x00|UINT32|0x00041280
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMocTDCLimit|0x00|UINT32|0x00041281
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMocEDCLimit|0x00|UINT32|0x00041282
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMocPBOLimitScalar|0x00|UINT32|0x00041283
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMocTjMax|0x00|UINT32|0x00041284
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMocSocTDCLimit|0x00|UINT32|0x00041285
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMocSocEDCLimit|0x00|UINT32|0x00041286
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMocPBOEnable|FALSE|BOOLEAN|0x00041287

  #CPO BTC
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCpoBtc|TRUE|BOOLEAN|0x00041288

  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMocGfxClockFrequency|0x00|UINT16|0x00041290
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMocGfxCoreVid|0x00|UINT8|0x00041291

  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdLowPowerPromontory|0x00|UINT8|0x00041292
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSoftMaxCclk|0x00|UINT16|0x00041293
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSoftMinCclk|0x00|UINT16|0x00041294
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgCoreDldoPsmArray|0|UINT64|0x00041296
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgCcdFrequencyArray|0|UINT64|0x00041297

  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdOcGFXMinVID|0x0|UINT8|0x00041298
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdOcGFXFreqMax|0x00|UINT16|0x00041299

  #PT
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTPortID|0xFF|UINT32|0x00041295
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgEarlyLink|FALSE|BOOLEAN|0x0004129A

  #Misc
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSocketPkgType|0x1|UINT32|0x000412A0  # DxIO - AM4:0 , FP5:1

  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdDmaDsXgbeEnable|TRUE|BOOLEAN|0x000412A1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdDmaDsUsb1Enable|TRUE|BOOLEAN|0x000412A2

  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdOpnSpare|0x0|UINT32|0x000412A3
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdThermalTripProchotEnable|FALSE|BOOLEAN|0x000412A4
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXgbeDisable|0x1|UINT8|0x000412A5

  #PMM
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdShubclkDpmControl|TRUE|BOOLEAN|0x00041300   ##RV2 only
  ### @brief This enables an optional mode during PCIe
  ### @brief Permitted Choices: (Type: Value)(Default: 0x0000)
  ### @li 0 - Disables this feature.
  ### @li 1 - Enables this feature.
  ### @li >1 - reserved.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgLcSchedRxEqEvalInt|0|UINT8|0x00041301
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNonPCIBarInitIommuVf|TRUE|BOOLEAN|0x00041303            #///< Configure non pci device bar for Iommu VF
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNonPCIBarInitIommuVfCntl|TRUE|BOOLEAN|0x00041304        #///< Configure non pci device bar for Iommu VF Control
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNonPCIBarInitIommuVfEnBit|TRUE|BOOLEAN|0x00041305       #///< Hit the enable bit on any allocated VF Bars for IOMMU

  ### @brief PCIe Link Equalization Mode. AMD PCIe links operating at Gen3 speed always perform equalization at the hardware level when training from Gen1 to Gen3. The equalization process involves the root port and the connected end point exchanging information with one another at the PHY level in order to obtain the best possible signal quality. The defa ult equalization mode will always be "Exhaustive" unless the user chooses to change it here.
  ### @brief Permitted Choices: (Type: Value)(Default: Exhaustive)
  ### @li DxioEqPresetSearchDirectional. This mode is supported by the hardware but AMD does not recommend using this mode.
  ### @li DxioEqPresetSearchExhaustive. The PHY performs a best fit algorithm using a predetermined range of coefficients for tuning its transmit and receive components. This is the recommended mode.
  ### @li DxioEqPresetSearchPreset. This equalization mode allows the user to directly control the coefficients used by the PHY for its tuning algorithm.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPcieEqSearchMode|1|UINT8|0x00041306
  ### @brief This control depends on PcdPcieEqSearchMode above. This control value will only be used when PcdPcieEqSearchMode == DxioEqPresetSearchPreset. In that circumstance the value of this control will be used as the preset constant for all PCIe ports. These values will take precedence over any value passed in the API structure DXIO_PORT_DATA_INITIALIZER_PCIE.mEqPreset
  ### @brief Permitted Choices: (Type: Value)(Default: 0xFF)
  ### @li 0..9 - valid preset constants.
  ### @li 0x0A .. 0xFE - reserved.
  ### @li 0xFF - per port assignment. AGESA will use the customer defined value passed in the DXIO API data structure.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPcieEqSearchPreset|0xFF|UINT8|0x00041307
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCc6FilterEnable|0x1|UINT8|0x00041308
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSkipDXIOKPNPLaneReset|FALSE|BOOLEAN|0x00041309
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdBixbyLinkFound|FALSE|BOOLEAN|0x0004130A
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPcieMemoryPowerDeepSleep|TRUE|BOOLEAN|0x0004130B
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPcieMemoryPowerShutDown|TRUE|BOOLEAN|0x0004130C
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdBxbSlotPowerLimit|0|UINT8|0x0004130D
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdNbifPgHysteresis|0|UINT8|0x0004130E
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdNbifMgcgHysteresis|0|UINT8|0x0004130F
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSyshubMgcgHysteresis|0|UINT8|0x00041310
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdNbioGlobalCgOverride|1|UINT8|0x00041311                   #///< Set 0 to disable all NBIO Clock Gating
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSrisAutoDetectMode|0xFF|UINT8|0x00041315                  #///< Controls SRIS Autodetect mode
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSrisAutodetectFactor|0x0|UINT8|0x0004131B                 #///< Controls SRIS Autodetect factor
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdRcbWrongAttrDis|FALSE|BOOLEAN|0x00041316                  #///< TRUE: Disable Receiver Completion Buffer checks received completions for incorrect attributes
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdNbioSramEccParityErrWA|TRUE|BOOLEAN|0x00041317            #///< Controls the SRAM ECC Parity Error workaround

  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgPcieLegacyEpWa|FALSE|BOOLEAN|0x00041318

  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSyshubPg|TRUE|BOOLEAN|0x0004131C
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSyshubPgHysteresis|0|UINT8|0x0004131D
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAthubClockGating|TRUE|BOOLEAN|0x0004131E
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMmhubSramLightSleep|1|UINT8|0x0004131F
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSrisSkpIntervalSel|0x1|UINT8|0x00041320
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSrisCfgType|0x0|UINT8|0x00041321                         #///< SRIS mode BIT0:SRIS (common) BIT1:SRIS (debug) BIT2:SRIS (PBS) BIT3:SRIS AUTODETECT (PBS)
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPcieLclkDeepSleepEnable|TRUE|BOOLEAN|0x00041322
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPcieLclkDeepSleepMode|0xF|UINT8|0x00041323
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgSyshubMgcgHspClkGating|TRUE|BOOLEAN|0x00041324
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSyshubMgcgHspClkHysteresis|0|UINT8|0x00041325
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgSyshubGdcMgcgClkGating|TRUE|BOOLEAN|0x00041326
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSyshubGdcMgcgHysteresis|0|UINT8|0x00041327
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdUsbSysHubSelect|0xFF|UINT32|0x00041328
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIommuL2AtsCntlEn|FALSE|BOOLEAN|0x00041329              #///< IOMMUL2 SHDWL2AB PCIE ATS control enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdNbifLclkDeepSleep|TRUE|BOOLEAN|0x0004132A                 #Mi3 Specific option
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdNbifSocclkDeepSleep|TRUE|BOOLEAN|0x0004132B               #Mi3 Specific option
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdNbifSriovEnable|FALSE|BOOLEAN|0x0004132C
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdNbifVfDoorBellAperSize|1|UINT8|0x0004132D
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdNbifVfMemAperSize|0|UINT8|0x0004132E
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdNbifVfRegAperSize|2|UINT8|0x0004132F

  #GNB SMU10 part 2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdVddcrVddVoltageMargin|0|UINT32|0x00041400
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdR3VddcrVddVoltageMargin|0|UINT32|0x00041401
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdR5VddcrVddVoltageMargin|0|UINT32|0x00041402
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdR7VddcrVddVoltageMargin|0|UINT32|0x00041403

  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdminSocVidOffset|0|UINT8|0x00041404
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAclkDpm0Freq400MHz|0|UINT8|0x00041405   # 1:400Mhz , 0:200Mhz
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCpuBoostClockOverride|0|UINT8|0x00041407
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGpuBoostClockOverride|0|UINT8|0x00041408
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdFanControlEnable|TRUE|BOOLEAN|0x0004140B

  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNbioCTOIgnoreError|TRUE|BOOLEAN|0x0004140C
  gEfiAmdAgesaModulePkgTokenSpaceGuid.Pcditemp|0|UINT8|0x0004140D  # Change i-Temp situation, 1:enable  0:default
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdWLANS0i3SaveRestore|0|UINT32|0x0004140E  # //[31:24]Offset,[23:16]PcieBus,[15:8]PcieDevice,[7:0]PcieFunction
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdLpcRstS0i3|0|UINT8|0x0004140F
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSparseControlDisable|0|UINT8|0x00041410  # RV2 serial

  #SMU 11
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdFddBtcEnable|FALSE|BOOLEAN|0x00041555
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAddiFeatCfgSupport|TRUE|BOOLEAN|0x00041556
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAddiFeatDsmEnabled|TRUE|BOOLEAN|0x00041557
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAddiFeatDsmDetectorEnabled|FALSE|BOOLEAN|0x00041558
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAddiFeatMaxDidDeltaOverride|TRUE|BOOLEAN|0x00041559
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAddiFeatCstateBoostThresholdOverride|0|UINT16|0x0004155A
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAddiFeatAllCoresFmaxOverride|0|UINT16|0x0004155B
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAddiFeatCcaEnabled|1|UINT8|0x0004155C
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAddiFeatL3CreditThresholdCeil|0|UINT32|0x0004155D
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSPI4ByteModeParams|0|UINT32|0x0004155E  # //[31:24]Reserved,[23:16]SpiCmd[1],[15:8]SpiCmd[0],[7:0]NumSpiCmd
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdDisableCcaThrottler|0|UINT8|0x0004155F  # //[0 = CCA enabled; 1 = CCA disabled]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdDFLODisable|FALSE|BOOLEAN|0x00041561 #DF P-state Latency Optimizer
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdLclkDpmFeature|0xFF|UINT8|0x00041562    # 0 = LclkDpm disabled, 1 = LclkDpm enabled
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdLclkDpmEnhancedWA|TRUE|BOOLEAN|0x00041563

  #SMU 12
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPowerSensorsRoutingSelect|0|UINT8|0x00041420  # 0:SFH, 1:WALLE
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdDxioCompatibleV1Lib|FALSE|BOOLEAN|0x00041421  # TRUE: DXIO V1 Lib , FALSE: DXIO V2 Lib
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdNtbPowerGateEnable|TRUE|BOOLEAN|0x00041422                #///< Controls enablement of NTB power gating
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdTempCurveFltrEn|0|UINT8|0x00041423
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdIsDAP|0xFF|UINT8|0x00041424
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSmuGfxPsmMarginControl|FALSE|BOOLEAN|0x00041425  # TRUE: set GfxPsmMargin
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSmuGfxPsmMarginValue|0x0|UINT16|0x00041426

  # SMU CBS Debug Options
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdVddcrCpuAgingBtcEnable|0xFF|UINT8|0x00041427
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdVddioDcBtcEnable|0xFF|UINT8|0x00041428

  # CXL Debug Options
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdReportErrorsToRcec|TRUE|BOOLEAN|0x00041429
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSyncHeaderByPass|TRUE|BOOLEAN|0x0004142A
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlIoArbWeights|0|UINT8|0x0004142B
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlCaMemArbWeights|0|UINT8|0x0004142C
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCnliTokenAdvertisement|0|UINT8|0x0004142D
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlCorrectableErrorLogging|TRUE|BOOLEAN|0x0004142E
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlUnCorrectableErrorLogging|TRUE|BOOLEAN|0x0004142F
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlMemEnabled|TRUE|BOOLEAN|0x00041430
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlCacheEnabled|FALSE|BOOLEAN|0x00041431
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlMemValidation|FALSE|BOOLEAN|0x00041432
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlTempGen5AdvertAltPtcl|FALSE|BOOLEAN|0x00041433
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlCamemRxOptimization|FALSE|BOOLEAN|0x00041434
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlTxOptimizeDirectOutEn|FALSE|BOOLEAN|0x00041435

  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlQosNormalLimit|4|UINT16|0x00041436         #Lower threshold value to return to normal mode
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlQosHighLimit|10|UINT16|0x00041437          #Upper threshold value to enter slow/biased mode
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlQosTimerLimit|0|UINT16|0x00041438          #Timer limit
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlQosSchedGap|0|UINT16|0x00041439            #Number of SCLK of forced gap between requests when in slow/biased mode
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlQosVariableGap|FALSE|BOOLEAN|0x0004143A    #0=Fix Scheduling GAP. 1=Variable Sheduling GAP.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlQosRdspIncMode|FALSE|BOOLEAN|0x0004143B    #0=Increment once per RdRsp. 1=Increment once per data beat
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlQosTimerDecNum|0|UINT8|0x0004143C          #Decrement value when timer expires
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlQosRdspIncNum|0|UINT8|0x0004143D           #Increment value on receiving RdRsp
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlQosWrrspIncNum|0|UINT8|0x0004143E          #Increment value on receiving WrRsp

  #CXL Endpoint QOS related options
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdEgressPortCongestion|0x0|UINT8|0x0004143F          #Enable/Disable Egress Port Congestion
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdTemporaryThroughputReduction|0x0|UINT8|0x00041440  #Enable/Disable Temporary Throughput Reduction
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdEgressModeratePercentage|10|UINT8|0x00041441       #Threshold in percent for Egress Port moderate Congestion.Valid range is 1-100, default 10
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdEgressSeverePercentage|25|UINT8|0x00041442         #Threshold in percent for Egress Port severe Congestion.Valid range is 1-100, default 25
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdBackpressureSampleInterval|8|UINT8|0x00041443      #Valid range is 0-15 (ns). Default is 8ns. Value of 0 disables the mechanism
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlSyncHeaderByPassCompMode|TRUE|BOOLEAN|0x00041444  #CXL Sync Header Bypass Compatibility Mode

  # CXL
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlCdatSupport|TRUE|BOOLEAN|0x00041450
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdChbcrWorkAround|TRUE|BOOLEAN|0x00041451

  # PCie
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPcieSramWA|FALSE|BOOLEAN|0x00041498
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPcie23DynPowerGating|TRUE|BOOLEAN|0x00041499
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdDxioSingleResetCallout|FALSE|BOOLEAN|0x00041500
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGppTxClkDynPortGating|TRUE|BOOLEAN|0x00041501
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGppTxClkDynTrPortGating|TRUE|BOOLEAN|0x00041502
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGppTxClkRxpClkEnPortGating|TRUE|BOOLEAN|0x00041503
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGfxTxClkDynPortGating|TRUE|BOOLEAN|0x00041504
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGfxTxClkDynTrPortGating|TRUE|BOOLEAN|0x00041505
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGfxTxClkRxpClkEnPortGating|TRUE|BOOLEAN|0x00041506
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGfxPcieDynPortGating|TRUE|BOOLEAN|0x00041507
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGppPcieDynPortGating|TRUE|BOOLEAN|0x00041508
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGfxPcieSdpControl|TRUE|BOOLEAN|0x00041509
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGppPcieSdpControl|TRUE|BOOLEAN|0x0004150A
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioClockGating|TRUE|BOOLEAN|0x0004150B
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioStaticPowerGating|TRUE|BOOLEAN|0x0004150C
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioRefClkShutDown|TRUE|BOOLEAN|0x0004150D
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgPcieCVTestWA|0|UINT8|0x0004150E
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioWA|0|UINT8|0x0004150F
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioSPCWA|0|UINT8|0x00041510
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioFCCreditWADisable|0|UINT8|0x00041511       # 1:Disable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdDxioSaveRestoreModes|0xFF|UINT8|0x00041512       # 0:FULL,  1:SELECT, 2:HYBRID, 3:UNKNOWN
  ### @brief This value determines if AGESA will enable Alternative Routing-ID Interpretation (ARI) Forwarding for each downstream port. The PCIe specification recommends that ARI Forwarding only be enabled by BIOS when an OS is present that supports ARI. This option allows for manual enable or disable of the feature by the platform owner.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li TRUE - ARI Forwarding is enabled.
  ### @li FALSE - ARI Forwarding is disabled.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPcieAriForwardingEnable|FALSE|BOOLEAN|0x00041513
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSurpriseDownFeature|TRUE|BOOLEAN|0x00041514
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioPmaPowerGating|TRUE|BOOLEAN|0x00041515
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioPmaClockGating|TRUE|BOOLEAN|0x00041516
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdTargetLinkSpeed|0xFF|UINT8|0x00041517
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdTWFilterDis|TRUE|BOOLEAN|0x00041518
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioTXClockGating|TRUE|BOOLEAN|0x00041519
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdLcMultAutoSpdChgOnLastRateEnable|FALSE|BOOLEAN|0x0004151A
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioClockGatingSettings|0x4F4F|UINT16|0x0004151B    # DxioClockGating Mask [13:8], DxioClockGating Value [5:0]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgSLTBiosFlag|0x0|UINT8|0x0004151C    # 0 = non-SLT BIOS, 1 = SLT BIOS
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioAllowCompPass|TRUE|BOOLEAN|0x0004151D
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioAllowPointerSlipInterval|TRUE|BOOLEAN|0x0004151E
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioAllowPointerSlipIntervalRange|0x50|UINT8|0x0004151F
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioFrequencyVetting|FALSE|BOOLEAN|0x00041525
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioTxFIFOMode|1|UINT8|0x00041522          # 0: improved latency, 1: latency behaviour
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioTxFIFORdPtrOffset|0xEC|UINT8|0x0004152F
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioEnableCorrectableSRAMECCReporing|FALSE|BOOLEAN|0x00041527
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioPhySeqModeIqOffset|0x00|UINT8|0x00041528
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdObffControl|TRUE|BOOLEAN|0x00041529
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSbrBrokenLaneAvoidanceSup|TRUE|BOOLEAN|0x0004152A
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAutoFullMarginSup|TRUE|BOOLEAN|0x0004152B
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdLinkDisableAtPowerOffDelay|0x0|UINT8|0x0004152C    # 0: Default (2ms), 0x1 to 0xF: 10ms to 160ms
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCtrlUnusedTileClkGating|FALSE|BOOLEAN|0x0004153f
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPcieIdeCapSup|FALSE|BOOLEAN|0x00041570
  #S0i3
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioPCIeRSTGenericReset|FALSE|BOOLEAN|0x00041520
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioGPIONVMEReset|FALSE|BOOLEAN|0x00041521

  #S0i3 GPIO Reset
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioPCIeGPIOResetEP1|0|UINT32|0x00041523
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioPCIeGPIOResetEP2|0|UINT32|0x00041524
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioPCIeGPIOResetEP3|0|UINT32|0x00041526

  #SMU 13
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGfxDcBtc|0x00|UINT8|0x00041530
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPcieOBFF|0x00|UINT8|0x00041531
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdEnablePowerGateMmHub|TRUE|BOOLEAN|0x00041532
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSetHardMinI2sMclk|0x00|UINT32|0x00041533
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdI2SCtl|0xF|UINT8|0x00041534  #0 - Disable 1- Enabled ,0xF- Auto
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdEnableHspClkDs|TRUE|BOOLEAN|0x00041535
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdEnableNbifOBFF|FALSE|BOOLEAN|0x00041536
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdEnableNbifDmaOBFF|FALSE|BOOLEAN|0x00041537
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdApplyNrepFlopSel|0xF|UINT8|0x00041538
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdP3TLimitCtrl|0xF|UINT8|0x00041539
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdP3TLimit|0x00|UINT32|0x0004153A
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdOcGFXMinVIDSVI3|0x0|UINT16|0x0004153B
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgLclkFrequencyRange|0x0|UINT32|0x0004153C  # [15:0] LCLK MaxFrequency, [31:16] LCLK MinFrequency
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdVddMiscVoltage|0x0|UINT16|0x0004153D         # Voltage in mV (used in AOD menu)
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PerCoreDldoPsmMarginCtrl|0x0|UINT32|0x0004153E

  #SMU
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdReservedSmuMemoryLo|0|UINT32|0x00041540
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdReservedSmuMemoryHi|0|UINT32|0x00041541

  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPcieCoreMarginIgnoreCSkip|0x1|UINT8|0x00041550
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgFPThrottler|0x03|UINT8|0x00041551         # 0xFF:Auto
                                                                                      # CCD0 is for bit0, CCD1 is for bit1
                                                                                      # 1 - disable FP throttler
                                                                                      # 0 - enable FP throttle
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdSetEdcExcursionReportEn|FALSE|BOOLEAN|0x00041552
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdSetEdcExcursionReporting|0|UINT32|0x00041553    # Format as SetEdcExcursionReporting struct
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdSmuDsmClkCtrl|FALSE|BOOLEAN|0x00041554    # SMU DSM Clock Control

  #MI200
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgMi200MaxPayloadSize|0xF|UINT8|0x00041542
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgMi200PcieTbtSupport|TRUE|BOOLEAN|0x00041543                            # PCD for PCIe Ten Bit Tag Support
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMi200PcieLinkAspmAllPort|0xFF|UINT8|0x00041544                  # Pcie LinkAspm 0:Disable 1:L0s 2:L1 3:L0sL1 0xFF:Auto
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMi200TargetLinkSpeedAllPort|0xFF|UINT8|0x00041545
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMi200AtomicOps|TRUE|BOOLEAN|0x00041546
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMi200EsmEnableAllRootPorts|0xFF|UINT8|0x00041547
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMi200PcieEcrcEnablement|FALSE|BOOLEAN|0x00041549
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMi200PwrClockGating|TRUE|BOOLEAN|0x0004154A

  #MPIO
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMPIOAncDataSupport|FALSE|BOOLEAN|0x00041600      # Updated by Program if supporting MPIO Anc data format or not
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMpdmaAcpiIvrsSupport|FALSE|BOOLEAN|0x00041601    # Create MPDMA IVRS ACPI table
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdGnbNbifDdrInitEn|0xFF|UINT8|0x00041602        # NBIF DDR initial sequence enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdEnableFrameBufferTrap|1|UINT8|0x00041603         # Enable Frame buffer Trap
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdActiveSltMode|FALSE|BOOLEAN|0x0004152e           # Informs MPIO if SLT Mode is enabled
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgMpioDynamicPwrGating|FALSE|BOOLEAN|0x00041604
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMpioNopDefferedCmdEnable|1|UINT32|0x00041605     # enable deferred NOP functionality: 0 - Disable 1 - Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMpioClockGatingEn|FALSE|BOOLEAN|0x00041606       # MPIO Clock Gating: FALSE - Disable TRUE - Enable

  #X3D
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgSwitcharooAlwaysON|0|UINT8|0x00041700         # 0 - Switcharoo Normal Operation 1 - Force Switcharoo ON Always
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgLatchupX3DDIS|0|UINT8|0x00041701              # 0 - Read LATCHUP_VID_X3D Fuses  1 - Do NOT read LATCHUP_VID_X3D Fuses

  #GMI3 PS3
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdSmuGmi3Ps3Pc6|0|UINT8|0x00041702              # 0 - enable GMI3 PS3 in PC6; 1 - disable GMI3 PS3 in PC6

  #Xgbe
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXgbeMdio0|FALSE|BOOLEAN|0x0003F710
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXgbeMdio1|FALSE|BOOLEAN|0x0003F711
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXgbeSfp|FALSE|BOOLEAN|0x0003F712
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXgbePort0ConfigEn|FALSE|BOOLEAN|0x0003F713
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXgbePort1ConfigEn|FALSE|BOOLEAN|0x0003F714
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXgbePort2ConfigEn|FALSE|BOOLEAN|0x0003F715
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXgbePort3ConfigEn|FALSE|BOOLEAN|0x0003F716
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXgbePort4ConfigEn|FALSE|BOOLEAN|0x0003F717
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXgbePort5ConfigEn|FALSE|BOOLEAN|0x0003F718
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXgbePort6ConfigEn|FALSE|BOOLEAN|0x0003F719
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXgbePort7ConfigEn|FALSE|BOOLEAN|0x0003F71A
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXgbePort0Table|{0x0}|VOID*|0x0003F71B
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXgbePort1Table|{0x0}|VOID*|0x0003F71C
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXgbePort2Table|{0x0}|VOID*|0x0003F71D
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXgbePort3Table|{0x0}|VOID*|0x0003F71E
  #{0x01, PortConfig, Speed, Type, MdioId, MdioResetType, ResetGpio, ResetI2C, SfpI2c, TX_FAULT, RS, Mod_ABS, Rx_LOS, SfpGpioMask, TwiAddress, TwiBus, RedriverEn, 0x00, 0x00, 0x00, RedriverModel, RedriverInterface, RedriverAddress, RedriverLane, GpioPad, MdioPad, I2cPac, 0x00}
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXgbePort0MAC|0x00|UINT64|0x0003F720
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXgbePort1MAC|0x00|UINT64|0x0003F721
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXgbePort2MAC|0x00|UINT64|0x0003F722
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXgbePort3MAC|0x00|UINT64|0x0003F723

  # DMAR
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdRestricAllDeviceMemoryRangePtr|0|UINT64|0x0003F800
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdRestricOneDeviceMemoryRangePtr|0|UINT64|0x0003F801

  # Custom Topoly
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCustomTableEnable|FALSE|BOOLEAN|0x0004F800
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCustomTableEnableLowerP0|TRUE|BOOLEAN|0x0004F801
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCustomTableEnableUpperP0|TRUE|BOOLEAN|0x0004F802
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCustomTableEnableLowerP1|TRUE|BOOLEAN|0x0004F803
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCustomTableEnableUpperP1|TRUE|BOOLEAN|0x0004F804
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCustomTableEnableLowerP2|TRUE|BOOLEAN|0x0004F805
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCustomTableEnableUpperP2|TRUE|BOOLEAN|0x0004F806
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCustomTableEnableLowerP3|TRUE|BOOLEAN|0x0004F807
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCustomTableEnableUpperP3|TRUE|BOOLEAN|0x0004F808
