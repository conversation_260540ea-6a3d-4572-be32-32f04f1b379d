//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************


/** @file EarlyConsoleInterfaceCommonLib.h
    Early Console interface Function Prototypes.

*/

#ifndef _EARLY_CONSOLE_INTERFACE_COMMON_LIB_H_
#define _EARLY_CONSOLE_INTERFACE_COMMON_LIB_H_

#include <EarlyConsoleInclude.h>
#include <Ppi/AmiSimpleTextOutPpi.h>

/**
    Initializes video Parameter with Platform details. 

    @param   DevInfo    Pointer to Video Parameter 

    @retval  EFI_NOT_FOUND
**/
EFI_STATUS 
PlatformVideoParameterInit(
    IN OUT VIDEO_PARAMETERS               *DevInfo
);

/**
    Initializes chipset specific video controller initialization.

    @param   PeiServices    Pointer to PeiServices table 

    @retval  EFI_STATUS
**/
EFI_STATUS 
PlatformVgaEnable (
    IN EFI_PEI_SERVICES                 **PeiServices,
    IN UINT16                           bus
);

/**
    Disables legacy VGA decode down to the video controller
 
 **/

VOID
PlatformDisableVgaDecode (
    VOID 
);

/**
    Initializes video controller with VGA standard init. 

    @param   PeiServices                    : Pointer to PeiServices 
    @param   EarlyConsoleVideoInitMode      : Video mode to initialize

    @retval  EFI_NOT_FOUND
**/
EFI_STATUS 
AmiVideoInit(
    IN  EFI_PEI_SERVICES                **PeiServices,
    IN  UINT32                          EarlyConsoleVideoInitMode
);

/**
    Install the Callback to Reset the Video Controller Programming done in the PEI Phase. 

    @param   None 

    @retval  EFI_NOT_FOUND
**/

EFI_STATUS
AmiVideoControllerReset (
    VOID 
);

/**
    Find the Vbt file for specified Video controller

    @param   GraphicsPolicyPtr    Pointer to Graphics Policy Pointer
                                  Callee responsibility to allocate and copy policy Data

    @retval  Void
**/
EFI_STATUS 
PlatformGetVbtFile (
    IN VOID                             **GraphicsPolicyPtr
);

/**
    Outputs CPU information to early text out.

    @param  **PeiServices - pointer to the PEI services.

    @retval VOID
**/

EFI_STATUS
PlatformInitializeCpuData (
    IN EFI_PEI_SERVICES               **PeiServices
);

/**
    Gets the DIMM's data and output to serial/video

    @param  **PeiServices - pointer to the PEI services.

    @retval EFI_SUCCESS
*/

EFI_STATUS
PlatformInitializeDimmData (
    IN EFI_PEI_SERVICES               **PeiServices
);

/**
    Clear Secondary and subordinate bus numbers in bridges

    @param   parameters    Pointer to VIDEO_PARAMETERS 

    @retval  Void
**/
VOID 
PlatformClearBusNumbers (
    IN VIDEO_PARAMETERS                     *parameters
);

/**
    Outputs Platform System data to early video.

    @param  None

    @retval  VOID
**/
EFI_STATUS
PlatformInitializeSystemData ();

#endif  // #ifndef _EARLY_CONSOLE_INTERFACE_COMMON_LIB_H_

