#;*****************************************************************************
#;
#; Copyright (C) 2016-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[Defines]
  DEC_SPECIFICATION     = 0x00010005
  PACKAGE_NAME          = AgesaCcxPkg
  PACKAGE_GUID          = df325429-029e-40f0-82db-0e69be7f6560
  PACKAGE_VERSION       = 0.1

[Includes]


[LibraryClasses]
  CcxBaseX86Lib|Include/Library/CcxBaseX86Lib.h
  CcxBistLib|Include/Library/CcxBistLib.h
  CcxPspLib|Include/Library/CcxPspLib.h
  CcxHaltLib|Include/Library/CcxHaltLib.h
  CcxMicrocodePatchLib|Include/Library/CcxMicrocodePatchLib.h
  CcxRolesLib|Include/Library/CcxRolesLib.h
  CcxDownCoreLib|Include/Library/CcxDownCoreLib.h
  CcxResetTablesLib|Include/Library/CcxResetTablesLib.h
  CcxPstatesLib|Include/Library/CcxPstatesLib.h
  CcxSmbiosLib|Include/Library/CcxSmbiosLib.h
  CcxBtcLib|Include/Library/CcxBtcLib.h
  CcxStallLib|Include/Library/CcxStallLib.h
  CcxSetMcaLib|Include/Library/CcxSetMcaLib.h
  CcxMpServicesLib|Include/Library/CcxMpServicesLib.h
  CcxIdsCustomPstateLib|Include/Library/CcxIdsCustomPstatesLib.h
  CcxSmmAccess2Lib|Include/Library/CcxSmmAccess2Lib.h
  CcxPeiSmmAccesLib|Include/Library/CcxPeiSmmAccessLib.h
  CcxSetMmioCfgBaseLib|Include/Library/CcxSetMmioCfgBaseLib.h
  CcxCppcLib|Include/Library/CcxCppcLib.h


[Guids]
  gAmdDownCoreStatusGuid                   = {0x29749bad, 0x401b, 0x4f6d, {0xb1, 0x24, 0xce, 0xce, 0x8c, 0x59, 0xc, 0x48}}
  gAmdTscFrequencyGuid                     = {0x614facf7, 0x1ac6, 0x4b15, {0xa8, 0x8c, 0x2c, 0x1a, 0x70, 0xd7, 0x77, 0xec}}
  gAmdCcxIdsHookGuid                       = {0x40cf03d5, 0x2743, 0x452c, {0xa6, 0xbe, 0xb3, 0x58, 0xa7, 0x94, 0x00, 0xed}}
  gAmdCcxIommuFeatureDataGuid              = {0x55C08824, 0x3018, 0x4613, {0xB3, 0xEB, 0x83, 0x62, 0x69, 0x41, 0x79, 0x55}}

[Protocols]
  gAmdCcxDxeInitCompleteProtocolGuid       = {0xaddf9be3, 0x1eb8, 0x4472, {0x95, 0xd1, 0xf8, 0xcd, 0xde, 0x58, 0xe5, 0xbb}}
  gAmdCcxSmbiosServicesProtocolGuid        = {0x2575123e, 0x2878, 0x4a72, {0xb9, 0xd5, 0xa, 0xa3, 0x48, 0x53, 0x91, 0x66}}
  gAmdCcxAcpiCratServicesProtocolGuid      = {0x6e773b37, 0xf799, 0x4355, {0x9f, 0xe8, 0x60, 0xe, 0x7, 0x34, 0xe5, 0xf}}
  gAmdCcxAcpiSratServicesProtocolGuid      = {0x9b6cdfc1, 0xe996, 0x4f52, {0x8e, 0x63, 0xe3, 0xff, 0xe5, 0x72, 0x45, 0xc}}
  gAmdAcpiCpuSsdtServicesProtocolGuid      = {0x42de07da, 0x21d4, 0x4132, {0x9d, 0xbf, 0x4e, 0xfd, 0xd7, 0x8b, 0x7b, 0x86}}
  gAmdCcxOcCompleteProtocolGuid            = {0x1fbe6f3d, 0x6dbb, 0x47df, {0x82, 0xb8, 0x22, 0x5, 0x6, 0x99, 0xe4, 0xb7}}
  gAmdCcxBaseServicesProtocolGuid          = {0xf6f234ea, 0xc633, 0x41fc, {0xae, 0x1f, 0xd8, 0x89, 0x52, 0x1b, 0x57, 0x6f}}
  gAmdCcxAcpiPcctServicesProtocolGuid      = {0x468202ff, 0x7784, 0x4744, {0xa8, 0x13, 0x3f, 0x69, 0x9f, 0xdd, 0x50, 0x4}}
  gAmdCcxAcpiCppcServicesProtocolGuid      = {0x5b333a0a, 0xe571, 0x4b0f, {0x93, 0x24, 0x77, 0xd1, 0x6a, 0x74, 0xc2, 0xa5}}
  gAmdCoreTopologyServicesV3ProtocolGuid   = {0xd20482f8, 0x7ecf, 0x4a40, {0x8c, 0x11, 0xd3, 0x26, 0xd8, 0x15, 0x38, 0x06}}

[Ppis]
  gAmdCcxPeiInitCompletePpiGuid            = {0xa1192fdd, 0xbf51, 0x49cb, {0x97, 0x67, 0x87, 0x81, 0xdf, 0x7a, 0xbb, 0x8c}}
  gAmdCoreTopologyServicesV3PpiGuid        = {0xc1a20e90, 0x48d2, 0x46a0, {0x91, 0xc9, 0x12, 0xcf, 0x95, 0xa1, 0xcf, 0x6d}}
  gAmdCcxDownBinResetPpiGuid               = {0xb0aae673, 0x4729, 0x4bf8, {0xb3, 0x03, 0xc4, 0x8d, 0x8d, 0x79, 0xe6, 0xbe}}

[PcdsFeatureFlag]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCcxStallEnable|TRUE|BOOLEAN|0x000CC0E1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdSvmControl|FALSE|BOOLEAN|0x000CC0E2   # Enable SVM MSRs control code

[PcdsFixedAtBuild]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCcxS3SaveSmi|0xEE|UINT8|0x000CC0A1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdAcpiCpuLpiC1MinResCZN|0x00000002|UINT32|0x000CC0A3   # CZN. 2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdAcpiCpuLpiC2MinResCZN|0x00000024|UINT32|0x000CC0A4   # CZN. 36
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdAcpiCpuLpiC3MinResCZN|0x000002BC|UINT32|0x000CC0A5   # CZN. 700
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdAcpiCpuLpiC1WorstCaseWakeupLatencyCZN|0x00000001|UINT32|0x000CC0A6 # CZN. 1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdAcpiCpuLpiC2WorstCaseWakeupLatencyCZN|0x00000012|UINT32|0x000CC0A7 # CZN. 18
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdAcpiCpuLpiC3WorstCaseWakeupLatencyCZN|0x0000015E|UINT32|0x000CC0A8 # CZN. 350

  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdAcpiCpuLpiC1MinResRN|0x00000002|UINT32|0x000CC0A9   # RN. 2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdAcpiCpuLpiC2MinResRN|0x00000024|UINT32|0x000CC0AA   # RN. 36
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdAcpiCpuLpiC3MinResRN|0x000002BC|UINT32|0x000CC0AB   # RN. 700
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdAcpiCpuLpiC1WorstCaseWakeupLatencyRN|0x00000001|UINT32|0x000CC0AC # RN. 1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdAcpiCpuLpiC2WorstCaseWakeupLatencyRN|0x00000012|UINT32|0x000CC0AD # RN. 18
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdAcpiCpuLpiC3WorstCaseWakeupLatencyRN|0x0000015E|UINT32|0x000CC0AE # RN. 350

  ### @brief PlatformInterruptFlags value of PCCT type 4 for CoreRankingTable. See ACPI specification for detail.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCoreRankingTableFeedbackInterruptFlags|0|UINT8|0x000CC0AF
  ### @brief PlatformInterrupt value of PCCT type 4 for CoreRankingTable. See ACPI specification for detail. Value 0 means interrupt is not used.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCoreRankingTableFeedbackInterrupt|0|UINT32|0x000CC0B0

[PcdsDynamic]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCcxCfgApmEnable|TRUE|BOOLEAN|0x000CC001
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCcxCfgCFOHEnable|TRUE|BOOLEAN|0x000CC002
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdAcpiPstateObjEnable|TRUE|BOOLEAN|0x000CC004
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCpuWdtSeverity|0xFF|UINT8|0x000CC005                  # For VH, Control MSRC001_0074[CpuWdTmrCfgSeverity], 0xFF means leave it at reset value
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdOpcacheCtrl|0xFF|UINT8|0x000CC008                     # MSR_C001_1021[5]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCcxP0Fid|0xF|UINT8|0x000CC00B                         # FID (For all)
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCcxP0Did|0x8|UINT8|0x000CC00C                         # DID (For all)
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCcxP0Vid|0xFF|UINT8|0x000CC00D                        # VID
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCcxPxAutoSetting|0x2|UINT8|0x000CC011                 # GN, BA, RS, Brh Pstate default 2- Auto
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCcxPxAutoFreq|0xFFFFFFFF|UINT32|0x000CC012            # GN, BA, RS, Brh Default Frequency
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCcxPxAutoVid|0xFFFFFFFF|UINT32|0x000CC013             # GN, BA, RS, Brh Default VID
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdSmmLock|TRUE|BOOLEAN|0x000CC014                       # Set MSR_C001_0015[0][SmmLock]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdVddcrCpu0Vid|0xFFFFFFFF|UINT32|0x000CC040             # VDDCR_CPU0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdVddcrCpu1Vid|0xFFFFFFFF|UINT32|0x000CC041             # VDDCR_CPU1

#----------------------------------------------------------------------------
#-  CCX PCDs
#-
### Set Doxy_path: "PCD-CCX.h"
#----------------------------------------------------------------------------

  ### @brief The core disable bitmap for physical CCD 0. Set bit[x] = 1 could down logical core x. For test purpose only, NOT FOR PRODUCTION!!!
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCoreDisCcd0|0|UINT16|0x000CC015
  ### @brief The core disable bitmap for physical CCD 1. Set bit[x] = 1 could down logical core x. For test purpose only, NOT FOR PRODUCTION!!!
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCoreDisCcd1|0|UINT16|0x000CC016
  ### @brief The core disable bitmap for physical CCD 2. Set bit[x] = 1 could down logical core x. For test purpose only, NOT FOR PRODUCTION!!!
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCoreDisCcd2|0|UINT16|0x000CC017
  ### @brief The core disable bitmap for physical CCD 3. Set bit[x] = 1 could down logical core x. For test purpose only, NOT FOR PRODUCTION!!!
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCoreDisCcd3|0|UINT16|0x000CC018
  ### @brief The core disable bitmap for physical CCD 4. Set bit[x] = 1 could down logical core x. For test purpose only, NOT FOR PRODUCTION!!!
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCoreDisCcd4|0|UINT16|0x000CC019
  ### @brief The core disable bitmap for physical CCD 5. Set bit[x] = 1 could down logical core x. For test purpose only, NOT FOR PRODUCTION!!!
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCoreDisCcd5|0|UINT16|0x000CC01A
  ### @brief The core disable bitmap for physical CCD 6. Set bit[x] = 1 could down logical core x. For test purpose only, NOT FOR PRODUCTION!!!
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCoreDisCcd6|0|UINT16|0x000CC01B
  ### @brief The core disable bitmap for physical CCD 7. Set bit[x] = 1 could down logical core x. For test purpose only, NOT FOR PRODUCTION!!!
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCoreDisCcd7|0|UINT16|0x000CC01C
  ### @brief The core disable bitmap for physical CCD 8. Set bit[x] = 1 could down logical core x. For test purpose only, NOT FOR PRODUCTION!!!
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCoreDisCcd8|0|UINT16|0x000CC01D
  ### @brief The core disable bitmap for physical CCD 9. Set bit[x] = 1 could down logical core x. For test purpose only, NOT FOR PRODUCTION!!!
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCoreDisCcd9|0|UINT16|0x000CC01E
  ### @brief The core disable bitmap for physical CCD 10. Set bit[x] = 1 could down logical core x. For test purpose only, NOT FOR PRODUCTION!!!
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCoreDisCcd10|0|UINT16|0x000CC01F
  ### @brief The core disable bitmap for physical CCD 11. Set bit[x] = 1 could down logical core x. For test purpose only, NOT FOR PRODUCTION!!!
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCoreDisCcd11|0|UINT16|0x000CC020

#----------------------------------------------------------------------------
#-  CCX PCDs
#-
### Set Doxy_path: "PCD-CCX-Dummy.h"
#----------------------------------------------------------------------------
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdGameMode|FALSE|BOOLEAN|0x000CC021                     # CP. Game mode.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdDownCoreSmtWarmResetStall|0x20000|UINT32|0x000CC022   # PCO Microseconds to delay before warm reset request after downcore and SMT disable control
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdIbrsEn|FALSE|BOOLEAN|0x000CC023                       # MTS. Enable Indirect Branch Prediction Speculation
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCcxEnabledFeatures|0|UINT32|0x000CC024                # Capabilities about current running processor
                                                                                                  #  Bit 0: LPI capable (For RN, CZN)
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdSvmLock|TRUE|BOOLEAN|0x000CC025                       # Set MSR_C001_0114[3]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdSvmEnable|TRUE|BOOLEAN|0x000CC026                     # Clear MSR_C001_0114[4]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdBranchSampling|FALSE|BOOLEAN|0x000CC027               # For MSR_C001_10DC[31] of GN Bx
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdVmplEnable|TRUE|BOOLEAN|0x000CC028                    # GN. Enable VMPL
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCcxSingleBitErrLogging|FALSE|BOOLEAN|0x000CC029       # GN. Single bit transparent error logging
  ### @brief This control adjusts the performance of the speculative operations and changes the behavior of memory write operations. This may have performance benefits for a few very specialized workloads. The user is cautioned to use this with care.
  ### @brief Permitted Choices: (Type: Boolean)(Default: TRUE)
  ### @li FALSE - Use this setting for workloads that have multiple threads that are expected to update memory locations in close proximity temporally and spatially.
  ### @li TRUE - This setting gives the best performance for most workloads.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdEnSpecStFill|TRUE|BOOLEAN|0x000CC02A                  # For MSR_LS_CFG3[26][EnSpecStFill] (for GN)

  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdEnableSvmAVIC|FALSE|BOOLEAN|0x000CC02D                # BRH. GN. Enable SVM AVIC
  ### @brief Work around for errata 1285, this control will disable a portion of the speculative read process to avoid the condition noted in the errata. This may cause a small performance impact.
  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li FALSE - no workaround is applied.
  ### @li TRUE - the errata condition is avoided by disabling a portion of the speculative Instruction Based Sampling process.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdIbsHardwareEn|FALSE|BOOLEAN|0x000CC02E                # For core Cerberus B0, set MSR_C001_1020[54]

  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdPsdGrouping|0xFF|UINT8|0x000CC030                     # GN. Switch the grouping of Pstate Dependencty (_PSD)

  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdNewDownCoreSequence|FALSE|BOOLEAN|0x000CC031          # 0 - Downcore in x86 AGESA; 1 - Downcore in ABL

  # RS. BRH.
  ### Enable SVM x2AVIC.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdEnableSvmX2AVIC|TRUE|BOOLEAN|0x000CC032

  ### @brief Permitted Choices: (Type: Boolean)(Default: FALSE)
  ### @li 0 - Disable small hammer configuration
  ### @li 1 - Enable small hammer configuration
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdSmallHammerConfiguration|0xFF|UINT8|0x000CC034        # Small Hammer Configuration (For Zen4)

  ### Enable FP512 downgrade.
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCcxEnableFp512Downgrade|0xFF|UINT8|0x000CC035

  ### Load uCode Patch
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCcxLoadUcodePatch|TRUE|BOOLEAN|0x000CC037

  # BRH.
  ### ERMSB Intermediate Threshold
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCcxErmsbIntermThld|0|UINT8|0x000CC038

  # BRH.
  ### CPU Aggressive Store Speculation
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCpuAggrStoreSpec|0|UINT8|0x000CC039

