#pragma message( "Compal Server Override Compiling-" __FILE__ )
#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = CbsSetupLibInstance
  FILE_GUID                      = D78452D7-ED4F-40C6-9A52-B0417FE84D60
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = CbsSetupLib | DXE_DRIVER
  UEFI_HII_RESOURCE_SECTION      = TRUE

[Sources]
  CbsSetupLib/CbsSetupLib.c
  CbsSetupLib/CbsCustomCorePstates.c
  CbsSetupLib/CbsCustomCorePstates.h
  CbsFuncLib/CbsBaseLib.c
  ../../../../../Build/ResourceBRH/AmdCbsDefault.c
  ../../../../../Build/ResourceBRH/AmdCbsForm.vfr
  ../../../../../Build/ResourceBRH/AmdCbsStrings.uni
  ../../../../../Build/ResourceBRH/AmdCbsFormID.h
  ../../../../../Build/ResourceBRH/AmdCbsVariable.h
  ../../../../../../OemboardPkg/Setup/RedfishSetup/XCompal_AMD.uni  #COMPAL_CHANGE

[LibraryClasses]
  AmdPspBaseLibV2
  BaseLib
  NbioHandleLib
  AmdPspApobLib
  BaseFabricTopologyLib
  CbsSmmCommLib
  HiiLib
  PrintLib
  UefiLib
  AmdPspMboxLibV2
  CbsUpdateApcbLib
  MemRestoreLib
  HobLib

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaFamily1AModulePkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec
  AgesaModulePkg/AgesaModuleNbioPkg.dec
  AmdCbsPkg/AmdCbsPkg.dec
  AgesaModulePkg/AgesaModulePspPkg.dec
  AgesaModulePkg/AgesaModuleCcxPkg.dec

[Guids]
  gAmdCbsFormsetIDGuid
  gEfiIfrTianoGuid
  gAmdCcxIommuFeatureDataGuid

[Protocols]
  gAmdNbioSmuServicesProtocolGuid         #CONSUME
  gAmdApcbDxeServiceProtocolGuid          #CONSUME
  gAmdNbioServicesProtocolGuid
  gAmdNbioPcieServicesProtocolGuid
  gAmdSocZen5ServicesProtocolGuid
  gAmdFabricTopologyServices2ProtocolGuid

[Pcd]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSmuFeatureControlDefines
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdChipsetIdentifiedId
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdLockApcbDxeAfterSmmLock

[FixedPcd]
  gAmdCbsPkgTokenSpaceGuid.PcdAmdCbsVariableAttribute
