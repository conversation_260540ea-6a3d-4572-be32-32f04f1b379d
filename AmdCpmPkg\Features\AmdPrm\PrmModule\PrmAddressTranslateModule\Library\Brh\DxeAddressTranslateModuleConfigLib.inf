#;*****************************************************************************
#;
#; Copyright (C) 2018-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

## @file
#  PRM Configuration Library Instance
#
#  The PRM configuration library instance is responsible for initializing and setting the corresponding
#  PRM module's configuration in the boot environment.
#
#  Copyright (c) Microsoft Corporation
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION           = 0x00010005
  BASE_NAME             = DxeAddressTranslateModuleConfigLib
  FILE_GUID             = ********-FAFA-413A-AF0D-D9A5B2B4914F
  MODULE_TYPE           = BASE
  VERSION_STRING        = 1.0
  LIBRARY_CLASS         = DxeAddressTranslateModuleConfigLib|DXE_DRIVER DXE_RUNTIME_DRIVER

[Sources]
  DxeAddressTranslateModuleConfigLib.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  AgesaModulePkg/AgesaModuleRasPkg.dec
  AgesaPkg/AgesaPkg.dec
# AMI PORTING
#  edk2/PrmPkg/PrmPkg.dec
  PrmPkg/PrmPkg.dec
  AmdCpmPkg/AmdCpmPkg.dec

[Protocols]
  gPrmConfigProtocolGuid
  gAmdRasInitDataProtocolGuid  #CONSUMED
  gAmdPlatformApeiDataProtocolGuid
  gAmdPciResourceProtocolGuid


[LibraryClasses]
  IoLib
  BaseLib
  BaseMemoryLib
  DebugLib
  MemoryAllocationLib
  UefiBootServicesTableLib
  UefiDriverEntryPoint
  PcdLib

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdPciExpressBaseAddress  ## CONSUMES
  gEfiMdePkgTokenSpaceGuid.PcdPciExpressBaseSize  ## CONSUMES

[Depex]
  gAmdRasInitDataProtocolGuid AND
  gAmdPlatformApeiDataProtocolGuid