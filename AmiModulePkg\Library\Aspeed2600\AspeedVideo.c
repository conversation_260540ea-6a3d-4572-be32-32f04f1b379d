//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************

/** @file AspeedVideo.c
    Aspeed related functions for initializing early video output.  Based on
    C sample code provided by Aspeed for initializing AST video device,
    ported to UEFI by AMI.
**/

#include <AspeedVideo.h>

EFI_PEI_PPI_DESCRIPTOR gPeiGraphicsPpiDescriptor = {
    EFI_PEI_PPI_DESCRIPTOR_PPI | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST,
    &gEfiPeiGraphicsPpiGuid,
    NULL
};

VBIOS_STDTABLE_STRUCT StdTable[] = {
    /* MD_2_3_400 */
    {
        0x67,
        {0x00,0x03,0x00,0x02},
        {0x5f,0x4f,0x50,0x82,0x52,0x9e,0xbf,0x1f,
         0x00,0x4f,0x20,0x00,0x00,0x00,0x00,0x00,
         0x9e,0x80,0x8f,0x28,0x1f,0x96,0xb9,0xa3,
         0xff},
        {0x00,0x01,0x02,0x03,0x04,0x05,0x14,0x07,
         0x38,0x39,0x3a,0x3b,0x3c,0x3d,0x3e,0x3f,
         0x0c,0x00,0x0f,0x08},
        {0x00,0x00,0x00,0x00,0x00,0x10,0x0e,0x00,
         0xff}
    },
    /* Mode12/ExtEGATable */
    {
        0xe3,
        {0x01,0x0f,0x00,0x06},
        {0x5f,0x4f,0x50,0x82,0x55,0x81,0x0b,0x3e,
         0x00,0x40,0x00,0x00,0x00,0x00,0x00,0x00,
         0xe9,0x8b,0xdf,0x28,0x00,0xe7,0x04,0xe3,
         0xff},
        {0x00,0x01,0x02,0x03,0x04,0x05,0x14,0x07,
         0x38,0x39,0x3a,0x3b,0x3c,0x3d,0x3e,0x3f,
         0x01,0x00,0x0f,0x00},
        {0x00,0x00,0x00,0x00,0x00,0x00,0x05,0x0f,
         0xff}
    },
    /* ExtVGATable */
    {
        0x2f,
        {0x01,0x0f,0x00,0x0e},
        {0x5f,0x4f,0x50,0x82,0x54,0x80,0x0b,0x3e,
         0x00,0x40,0x00,0x00,0x00,0x00,0x00,0x00,
         0xea,0x8c,0xdf,0x28,0x40,0xe7,0x04,0xa3,
         0xff},
        {0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,
         0x08,0x09,0x0a,0x0b,0x0c,0x0d,0x0e,0x0f,
         0x01,0x00,0x00,0x00},
        {0x00,0x00,0x00,0x00,0x00,0x40,0x05,0x0f,
         0xff}
    },
    /* ExtHiCTable */
    {
        0x2f,
        {0x01,0x0f,0x00,0x0e},
        {0x5f,0x4f,0x50,0x82,0x54,0x80,0x0b,0x3e,
         0x00,0x40,0x00,0x00,0x00,0x00,0x00,0x00,
         0xea,0x8c,0xdf,0x28,0x40,0xe7,0x04,0xa3,
         0xff},
        {0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,
         0x08,0x09,0x0a,0x0b,0x0c,0x0d,0x0e,0x0f,
         0x01,0x00,0x00,0x00},
        {0x00,0x00,0x00,0x00,0x00,0x00,0x05,0x0f,
         0xff}
    },
    /* ExtTrueCTable */
    {
        0x2f,
        {0x01,0x0f,0x00,0x0e},
        {0x5f,0x4f,0x50,0x82,0x54,0x80,0x0b,0x3e,
         0x00,0x40,0x00,0x00,0x00,0x00,0x00,0x00,
         0xea,0x8c,0xdf,0x28,0x40,0xe7,0x04,0xa3,
         0xff},
        {0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,
         0x08,0x09,0x0a,0x0b,0x0c,0x0d,0x0e,0x0f,
         0x01,0x00,0x00,0x00},
        {0x00,0x00,0x00,0x00,0x00,0x00,0x05,0x0f,
         0xff}
    },
};

VBIOS_ENHTABLE_STRUCT  Res640x480Table[] = {
    { 800, 640, 8, 96, 525, 480, 2, 2, VCLK25_175,  /* 60Hz */
      (SyncNN | HBorderFlag | VBorderFlag | Charx8Dot), 60, 1, 0x2E },
    { 832, 640, 16, 40, 520, 480, 1, 3, VCLK31_5,   /* 72Hz */
      (SyncNN | HBorderFlag | VBorderFlag | Charx8Dot), 72, 2, 0x2E  },
    { 840, 640, 16, 64, 500, 480, 1, 3, VCLK31_5,   /* 75Hz */
      (SyncNN | Charx8Dot) , 75, 3, 0x2E },
    { 832, 640, 56, 56, 509, 480, 1, 3, VCLK36,     /* 85Hz */
      (SyncNN | Charx8Dot) , 85, 4, 0x2E },
    { 832, 640, 56, 56, 509, 480, 1, 3, VCLK36,     /* end */
      (SyncNN | Charx8Dot) , 0xFF, 4, 0x2E },
};

VBIOS_ENHTABLE_STRUCT  Res800x600Table[] = {
#if 0    
    {1024, 800, 24, 72, 625, 600, 1, 2, VCLK36,     /* 56Hz */
      (SyncPP | Charx8Dot), 56, 1, 0x30 },
#endif      
    {1056, 800, 40, 128, 628, 600, 1, 4, VCLK40,    /* 60Hz */
      (SyncPP | Charx8Dot), 60, 2, 0x30 },
    {1040, 800, 56, 120, 666, 600, 37, 6, VCLK50,   /* 72Hz */
      (SyncPP | Charx8Dot), 72, 3, 0x30 },
    {1056, 800, 16, 80, 625, 600, 1, 3, VCLK49_5,   /* 75Hz */
      (SyncPP | Charx8Dot), 75, 4, 0x30 },
    {1048, 800, 32, 64, 631, 600, 1, 3, VCLK56_25,  /* 85Hz */
      (SyncPP | Charx8Dot), 85, 5, 0x30 },
    {1048, 800, 32, 64, 631, 600, 1, 3, VCLK56_25,  /* end */
      (SyncPP | Charx8Dot), 0xFF, 5, 0x30 },
};

VBIOS_ENHTABLE_STRUCT  Res1024x768Table[] = {
    {1344, 1024, 24, 136, 806, 768, 3, 6, VCLK65,   /* 60Hz */
      (SyncNN | Charx8Dot), 60, 1, 0x31 },
    {1328, 1024, 24, 136, 806, 768, 3, 6, VCLK75,   /* 70Hz */
      (SyncNN | Charx8Dot), 70, 2, 0x31 },
    {1312, 1024, 16, 96, 800, 768, 1, 3, VCLK78_75, /* 75Hz */
      (SyncPP | Charx8Dot), 75, 3, 0x31 },
    {1376, 1024, 48, 96, 808, 768, 1, 3, VCLK94_5,  /* 85Hz */
      (SyncPP | Charx8Dot), 85, 4, 0x31 },
    {1376, 1024, 48, 96, 808, 768, 1, 3, VCLK94_5,  /* end */
      (SyncPP | Charx8Dot), 0xFF, 4, 0x31 },
};

VBIOS_ENHTABLE_STRUCT  Res1280x1024Table[] = {
    {1688, 1280, 48, 112, 1066, 1024, 1, 3, VCLK108,    /* 60Hz */
      (SyncPP | Charx8Dot), 60, 1, 0x32 },
    {1688, 1280, 16, 144, 1066, 1024, 1, 3, VCLK135,    /* 75Hz */
      (SyncPP | Charx8Dot), 75, 2, 0x32 },
    {1728, 1280, 64, 160, 1072, 1024, 1, 3, VCLK157_5,  /* 85Hz */
      (SyncPP | Charx8Dot), 85, 3, 0x32 },
    {1728, 1280, 64, 160, 1072, 1024, 1, 3, VCLK157_5,  /* end */
      (SyncPP | Charx8Dot), 0xFF, 3, 0x32 },
};

VBIOS_ENHTABLE_STRUCT  Res1600x1200Table[] = {
    {2160, 1600, 64, 192, 1250, 1200, 1, 3, VCLK162,    /* 60Hz */
      (SyncPP | Charx8Dot), 60, 1, 0x33 },
    {2160, 1600, 64, 192, 1250, 1200, 1, 3, VCLK162,    /* end */
      (SyncPP | Charx8Dot), 0xFF, 1, 0x33 },
};

VBIOS_ENHTABLE_STRUCT  Res1920x1080Table[] = {
    {2200, 1920, 88, 44, 1125, 1080, 4, 5, VCLK148_5,   /* 60Hz */ 
      (SyncPP | Charx8Dot), 60, 1, 0x38 }, 
    {2200, 1920, 88, 44, 1125, 1080, 4, 5, VCLK148_5,   /* end */ 
      (SyncPP | Charx8Dot), 0xFF, 1, 0x38 },         
};

VBIOS_DAC_INFO DAC_TEXT[] = {
 { 0x00, 0x00, 0x00 },  { 0x00, 0x00, 0x2a },  { 0x00, 0x2a, 0x00 },  { 0x00, 0x2a, 0x2a }, 
 { 0x2a, 0x00, 0x00 },  { 0x2a, 0x00, 0x2a },  { 0x2a, 0x2a, 0x00 },  { 0x2a, 0x2a, 0x2a }, 
 { 0x00, 0x00, 0x15 },  { 0x00, 0x00, 0x3f },  { 0x00, 0x2a, 0x15 },  { 0x00, 0x2a, 0x3f }, 
 { 0x2a, 0x00, 0x15 },  { 0x2a, 0x00, 0x3f },  { 0x2a, 0x2a, 0x15 },  { 0x2a, 0x2a, 0x3f }, 
 { 0x00, 0x15, 0x00 },  { 0x00, 0x15, 0x2a },  { 0x00, 0x3f, 0x00 },  { 0x00, 0x3f, 0x2a }, 
 { 0x2a, 0x15, 0x00 },  { 0x2a, 0x15, 0x2a },  { 0x2a, 0x3f, 0x00 },  { 0x2a, 0x3f, 0x2a }, 
 { 0x00, 0x15, 0x15 },  { 0x00, 0x15, 0x3f },  { 0x00, 0x3f, 0x15 },  { 0x00, 0x3f, 0x3f }, 
 { 0x2a, 0x15, 0x15 },  { 0x2a, 0x15, 0x3f },  { 0x2a, 0x3f, 0x15 },  { 0x2a, 0x3f, 0x3f }, 
 { 0x15, 0x00, 0x00 },  { 0x15, 0x00, 0x2a },  { 0x15, 0x2a, 0x00 },  { 0x15, 0x2a, 0x2a }, 
 { 0x3f, 0x00, 0x00 },  { 0x3f, 0x00, 0x2a },  { 0x3f, 0x2a, 0x00 },  { 0x3f, 0x2a, 0x2a }, 
 { 0x15, 0x00, 0x15 },  { 0x15, 0x00, 0x3f },  { 0x15, 0x2a, 0x15 },  { 0x15, 0x2a, 0x3f }, 
 { 0x3f, 0x00, 0x15 },  { 0x3f, 0x00, 0x3f },  { 0x3f, 0x2a, 0x15 },  { 0x3f, 0x2a, 0x3f }, 
 { 0x15, 0x15, 0x00 },  { 0x15, 0x15, 0x2a },  { 0x15, 0x3f, 0x00 },  { 0x15, 0x3f, 0x2a }, 
 { 0x3f, 0x15, 0x00 },  { 0x3f, 0x15, 0x2a },  { 0x3f, 0x3f, 0x00 },  { 0x3f, 0x3f, 0x2a }, 
 { 0x15, 0x15, 0x15 },  { 0x15, 0x15, 0x3f },  { 0x15, 0x3f, 0x15 },  { 0x15, 0x3f, 0x3f }, 
 { 0x3f, 0x15, 0x15 },  { 0x3f, 0x15, 0x3f },  { 0x3f, 0x3f, 0x15 },  { 0x3f, 0x3f, 0x3f }, 
};

VBIOS_DAC_INFO DAC_EGA[] = {
 { 0x00, 0x00, 0x00 },  { 0x00, 0x00, 0x2a },  { 0x00, 0x2a, 0x00 },  { 0x00, 0x2a, 0x2a }, 
 { 0x2a, 0x00, 0x00 },  { 0x2a, 0x00, 0x2a },  { 0x2a, 0x2a, 0x00 },  { 0x2a, 0x2a, 0x2a }, 
 { 0x00, 0x00, 0x15 },  { 0x00, 0x00, 0x3f },  { 0x00, 0x2a, 0x15 },  { 0x00, 0x2a, 0x3f }, 
 { 0x2a, 0x00, 0x15 },  { 0x2a, 0x00, 0x3f },  { 0x2a, 0x2a, 0x15 },  { 0x2a, 0x2a, 0x3f }, 
 { 0x00, 0x15, 0x00 },  { 0x00, 0x15, 0x2a },  { 0x00, 0x3f, 0x00 },  { 0x00, 0x3f, 0x2a }, 
 { 0x2a, 0x15, 0x00 },  { 0x2a, 0x15, 0x2a },  { 0x2a, 0x3f, 0x00 },  { 0x2a, 0x3f, 0x2a }, 
 { 0x00, 0x15, 0x15 },  { 0x00, 0x15, 0x3f },  { 0x00, 0x3f, 0x15 },  { 0x00, 0x3f, 0x3f }, 
 { 0x2a, 0x15, 0x15 },  { 0x2a, 0x15, 0x3f },  { 0x2a, 0x3f, 0x15 },  { 0x2a, 0x3f, 0x3f }, 
 { 0x15, 0x00, 0x00 },  { 0x15, 0x00, 0x2a },  { 0x15, 0x2a, 0x00 },  { 0x15, 0x2a, 0x2a }, 
 { 0x3f, 0x00, 0x00 },  { 0x3f, 0x00, 0x2a },  { 0x3f, 0x2a, 0x00 },  { 0x3f, 0x2a, 0x2a }, 
 { 0x15, 0x00, 0x15 },  { 0x15, 0x00, 0x3f },  { 0x15, 0x2a, 0x15 },  { 0x15, 0x2a, 0x3f }, 
 { 0x3f, 0x00, 0x15 },  { 0x3f, 0x00, 0x3f },  { 0x3f, 0x2a, 0x15 },  { 0x3f, 0x2a, 0x3f }, 
 { 0x15, 0x15, 0x00 },  { 0x15, 0x15, 0x2a },  { 0x15, 0x3f, 0x00 },  { 0x15, 0x3f, 0x2a }, 
 { 0x3f, 0x15, 0x00 },  { 0x3f, 0x15, 0x2a },  { 0x3f, 0x3f, 0x00 },  { 0x3f, 0x3f, 0x2a }, 
 { 0x15, 0x15, 0x15 },  { 0x15, 0x15, 0x3f },  { 0x15, 0x3f, 0x15 },  { 0x15, 0x3f, 0x3f }, 
 { 0x3f, 0x15, 0x15 },  { 0x3f, 0x15, 0x3f },  { 0x3f, 0x3f, 0x15 },  { 0x3f, 0x3f, 0x3f }, 
};

VBIOS_DAC_INFO DAC_VGA[] = {
 { 0x00, 0x00, 0x00 },  { 0x00, 0x00, 0x2a },  { 0x00, 0x2a, 0x00 },  { 0x00, 0x2a, 0x2a }, 
 { 0x2a, 0x00, 0x00 },  { 0x2a, 0x00, 0x2a },  { 0x2a, 0x15, 0x00 },  { 0x2a, 0x2a, 0x2a }, 
 { 0x15, 0x15, 0x15 },  { 0x15, 0x15, 0x3f },  { 0x15, 0x3f, 0x15 },  { 0x15, 0x3f, 0x3f }, 
 { 0x3f, 0x15, 0x15 },  { 0x3f, 0x15, 0x3f },  { 0x3f, 0x3f, 0x15 },  { 0x3f, 0x3f, 0x3f }, 
 { 0x00, 0x00, 0x00 },  { 0x05, 0x05, 0x05 },  { 0x08, 0x08, 0x08 },  { 0x0b, 0x0b, 0x0b }, 
 { 0x0e, 0x0e, 0x0e },  { 0x11, 0x11, 0x11 },  { 0x14, 0x14, 0x14 },  { 0x18, 0x18, 0x18 }, 
 { 0x1c, 0x1c, 0x1c },  { 0x20, 0x20, 0x20 },  { 0x24, 0x24, 0x24 },  { 0x28, 0x28, 0x28 }, 
 { 0x2d, 0x2d, 0x2d },  { 0x32, 0x32, 0x32 },  { 0x38, 0x38, 0x38 },  { 0x3f, 0x3f, 0x3f }, 
 { 0x00, 0x00, 0x3f },  { 0x10, 0x00, 0x3f },  { 0x1f, 0x00, 0x3f },  { 0x2f, 0x00, 0x3f }, 
 { 0x3f, 0x00, 0x3f },  { 0x3f, 0x00, 0x2f },  { 0x3f, 0x00, 0x1f },  { 0x3f, 0x00, 0x10 }, 
 { 0x3f, 0x00, 0x00 },  { 0x3f, 0x10, 0x00 },  { 0x3f, 0x1f, 0x00 },  { 0x3f, 0x2f, 0x00 }, 
 { 0x3f, 0x3f, 0x00 },  { 0x2f, 0x3f, 0x00 },  { 0x1f, 0x3f, 0x00 },  { 0x10, 0x3f, 0x00 }, 
 { 0x00, 0x3f, 0x00 },  { 0x00, 0x3f, 0x10 },  { 0x00, 0x3f, 0x1f },  { 0x00, 0x3f, 0x2f }, 
 { 0x00, 0x3f, 0x3f },  { 0x00, 0x2f, 0x3f },  { 0x00, 0x1f, 0x3f },  { 0x00, 0x10, 0x3f }, 
 { 0x1f, 0x1f, 0x3f },  { 0x27, 0x1f, 0x3f },  { 0x2f, 0x1f, 0x3f },  { 0x37, 0x1f, 0x3f }, 
 { 0x3f, 0x1f, 0x3f },  { 0x3f, 0x1f, 0x37 },  { 0x3f, 0x1f, 0x2f },  { 0x3f, 0x1f, 0x27 }, 
 { 0x3f, 0x1f, 0x1f },  { 0x3f, 0x27, 0x1f },  { 0x3f, 0x2f, 0x1f },  { 0x3f, 0x37, 0x1f }, 
 { 0x3f, 0x3f, 0x1f },  { 0x37, 0x3f, 0x1f },  { 0x2f, 0x3f, 0x1f },  { 0x27, 0x3f, 0x1f }, 
 { 0x1f, 0x3f, 0x1f },  { 0x1f, 0x3f, 0x27 },  { 0x1f, 0x3f, 0x2f },  { 0x1f, 0x3f, 0x37 }, 
 { 0x1f, 0x3f, 0x3f },  { 0x1f, 0x37, 0x3f },  { 0x1f, 0x2f, 0x3f },  { 0x1f, 0x27, 0x3f }, 
 { 0x2d, 0x2d, 0x3f },  { 0x31, 0x2d, 0x3f },  { 0x36, 0x2d, 0x3f },  { 0x3a, 0x2d, 0x3f }, 
 { 0x3f, 0x2d, 0x3f },  { 0x3f, 0x2d, 0x3a },  { 0x3f, 0x2d, 0x36 },  { 0x3f, 0x2d, 0x31 }, 
 { 0x3f, 0x2d, 0x2d },  { 0x3f, 0x31, 0x2d },  { 0x3f, 0x36, 0x2d },  { 0x3f, 0x3a, 0x2d }, 
 { 0x3f, 0x3f, 0x2d },  { 0x3a, 0x3f, 0x2d },  { 0x36, 0x3f, 0x2d },  { 0x31, 0x3f, 0x2d }, 
 { 0x2d, 0x3f, 0x2d },  { 0x2d, 0x3f, 0x31 },  { 0x2d, 0x3f, 0x36 },  { 0x2d, 0x3f, 0x3a }, 
 { 0x2d, 0x3f, 0x3f },  { 0x2d, 0x3a, 0x3f },  { 0x2d, 0x36, 0x3f },  { 0x2d, 0x31, 0x3f }, 
 { 0x00, 0x00, 0x1c },  { 0x07, 0x00, 0x1c },  { 0x0e, 0x00, 0x1c },  { 0x15, 0x00, 0x1c }, 
 { 0x1c, 0x00, 0x1c },  { 0x1c, 0x00, 0x15 },  { 0x1c, 0x00, 0x0e },  { 0x1c, 0x00, 0x07 }, 
 { 0x1c, 0x00, 0x00 },  { 0x1c, 0x07, 0x00 },  { 0x1c, 0x0e, 0x00 },  { 0x1c, 0x15, 0x00 }, 
 { 0x1c, 0x1c, 0x00 },  { 0x15, 0x1c, 0x00 },  { 0x0e, 0x1c, 0x00 },  { 0x07, 0x1c, 0x00 }, 
 { 0x00, 0x1c, 0x00 },  { 0x00, 0x1c, 0x07 },  { 0x00, 0x1c, 0x0e },  { 0x00, 0x1c, 0x15 }, 
 { 0x00, 0x1c, 0x1c },  { 0x00, 0x15, 0x1c },  { 0x00, 0x0e, 0x1c },  { 0x00, 0x07, 0x1c }, 
 { 0x0e, 0x0e, 0x1c },  { 0x11, 0x0e, 0x1c },  { 0x15, 0x0e, 0x1c },  { 0x18, 0x0e, 0x1c }, 
 { 0x1c, 0x0e, 0x1c },  { 0x1c, 0x0e, 0x18 },  { 0x1c, 0x0e, 0x15 },  { 0x1c, 0x0e, 0x11 }, 
 { 0x1c, 0x0e, 0x0e },  { 0x1c, 0x11, 0x0e },  { 0x1c, 0x15, 0x0e },  { 0x1c, 0x18, 0x0e }, 
 { 0x1c, 0x1c, 0x0e },  { 0x18, 0x1c, 0x0e },  { 0x15, 0x1c, 0x0e },  { 0x11, 0x1c, 0x0e }, 
 { 0x0e, 0x1c, 0x0e },  { 0x0e, 0x1c, 0x11 },  { 0x0e, 0x1c, 0x15 },  { 0x0e, 0x1c, 0x18 }, 
 { 0x0e, 0x1c, 0x1c },  { 0x0e, 0x18, 0x1c },  { 0x0e, 0x15, 0x1c },  { 0x0e, 0x11, 0x1c }, 
 { 0x14, 0x14, 0x1c },  { 0x16, 0x14, 0x1c },  { 0x18, 0x14, 0x1c },  { 0x1a, 0x14, 0x1c }, 
 { 0x1c, 0x14, 0x1c },  { 0x1c, 0x14, 0x1a },  { 0x1c, 0x14, 0x18 },  { 0x1c, 0x14, 0x16 }, 
 { 0x1c, 0x14, 0x14 },  { 0x1c, 0x16, 0x14 },  { 0x1c, 0x18, 0x14 },  { 0x1c, 0x1a, 0x14 }, 
 { 0x1c, 0x1c, 0x14 },  { 0x1a, 0x1c, 0x14 },  { 0x18, 0x1c, 0x14 },  { 0x16, 0x1c, 0x14 }, 
 { 0x14, 0x1c, 0x14 },  { 0x14, 0x1c, 0x16 },  { 0x14, 0x1c, 0x18 },  { 0x14, 0x1c, 0x1a }, 
 { 0x14, 0x1c, 0x1c },  { 0x14, 0x1a, 0x1c },  { 0x14, 0x18, 0x1c },  { 0x14, 0x16, 0x1c }, 
 { 0x00, 0x00, 0x10 },  { 0x04, 0x00, 0x10 },  { 0x08, 0x00, 0x10 },  { 0x0c, 0x00, 0x10 }, 
 { 0x10, 0x00, 0x10 },  { 0x10, 0x00, 0x0c },  { 0x10, 0x00, 0x08 },  { 0x10, 0x00, 0x04 }, 
 { 0x10, 0x00, 0x00 },  { 0x10, 0x04, 0x00 },  { 0x10, 0x08, 0x00 },  { 0x10, 0x0c, 0x00 }, 
 { 0x10, 0x10, 0x00 },  { 0x0c, 0x10, 0x00 },  { 0x08, 0x10, 0x00 },  { 0x04, 0x10, 0x00 }, 
 { 0x00, 0x10, 0x00 },  { 0x00, 0x10, 0x04 },  { 0x00, 0x10, 0x08 },  { 0x00, 0x10, 0x0c }, 
 { 0x00, 0x10, 0x10 },  { 0x00, 0x0c, 0x10 },  { 0x00, 0x08, 0x10 },  { 0x00, 0x04, 0x10 }, 
 { 0x08, 0x08, 0x10 },  { 0x0a, 0x08, 0x10 },  { 0x0c, 0x08, 0x10 },  { 0x0e, 0x08, 0x10 }, 
 { 0x10, 0x08, 0x10 },  { 0x10, 0x08, 0x0e },  { 0x10, 0x08, 0x0c },  { 0x10, 0x08, 0x0a }, 
 { 0x10, 0x08, 0x08 },  { 0x10, 0x0a, 0x08 },  { 0x10, 0x0c, 0x08 },  { 0x10, 0x0e, 0x08 }, 
 { 0x10, 0x10, 0x08 },  { 0x0e, 0x10, 0x08 },  { 0x0c, 0x10, 0x08 },  { 0x0a, 0x10, 0x08 }, 
 { 0x08, 0x10, 0x08 },  { 0x08, 0x10, 0x0a },  { 0x08, 0x10, 0x0c },  { 0x08, 0x10, 0x0e }, 
 { 0x08, 0x10, 0x10 },  { 0x08, 0x0e, 0x10 },  { 0x08, 0x0c, 0x10 },  { 0x08, 0x0a, 0x10 }, 
 { 0x0b, 0x0b, 0x10 },  { 0x0c, 0x0b, 0x10 },  { 0x0d, 0x0b, 0x10 },  { 0x0f, 0x0b, 0x10 }, 
 { 0x10, 0x0b, 0x10 },  { 0x10, 0x0b, 0x0f },  { 0x10, 0x0b, 0x0d },  { 0x10, 0x0b, 0x0c }, 
 { 0x10, 0x0b, 0x0b },  { 0x10, 0x0c, 0x0b },  { 0x10, 0x0d, 0x0b },  { 0x10, 0x0f, 0x0b }, 
 { 0x10, 0x10, 0x0b },  { 0x0f, 0x10, 0x0b },  { 0x0d, 0x10, 0x0b },  { 0x0c, 0x10, 0x0b }, 
 { 0x0b, 0x10, 0x0b },  { 0x0b, 0x10, 0x0c },  { 0x0b, 0x10, 0x0d },  { 0x0b, 0x10, 0x0f }, 
 { 0x0b, 0x10, 0x10 },  { 0x0b, 0x0f, 0x10 },  { 0x0b, 0x0d, 0x10 },  { 0x0b, 0x0c, 0x10 }, 
 { 0x00, 0x00, 0x00 },  { 0x00, 0x00, 0x00 },  { 0x00, 0x00, 0x00 },  { 0x00, 0x00, 0x00 }, 
 { 0x00, 0x00, 0x00 },  { 0x00, 0x00, 0x00 },  { 0x00, 0x00, 0x00 },  { 0x00, 0x00, 0x00 }, 
};

UINT8 FONT8X16[][16] = {
{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 000
{0x00,0x00,0x7e,0x81,0xa5,0x81,0x81,0xbd,0x99,0x81,0x81,0x7e,0x00,0x00,0x00,0x00},         // 001
{0x00,0x00,0x7e,0xff,0xdb,0xff,0xff,0xc3,0xe7,0xff,0xff,0x7e,0x00,0x00,0x00,0x00},         // 002
{0x00,0x00,0x00,0x00,0x6c,0xfe,0xfe,0xfe,0xfe,0x7c,0x38,0x10,0x00,0x00,0x00,0x00},         // 003
{0x00,0x00,0x00,0x00,0x10,0x38,0x7c,0xfe,0x7c,0x38,0x10,0x00,0x00,0x00,0x00,0x00},         // 004
{0x00,0x00,0x00,0x18,0x3c,0x3c,0xe7,0xe7,0xe7,0x18,0x18,0x3c,0x00,0x00,0x00,0x00},         // 005
{0x00,0x00,0x00,0x18,0x3c,0x7e,0xff,0xff,0x7e,0x18,0x18,0x3c,0x00,0x00,0x00,0x00},         // 006
{0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x3c,0x3c,0x18,0x00,0x00,0x00,0x00,0x00,0x00},         // 007
{0xff,0xff,0xff,0xff,0xff,0xff,0xe7,0xc3,0xc3,0xe7,0xff,0xff,0xff,0xff,0xff,0xff},         // 008
{0x00,0x00,0x00,0x00,0x00,0x3c,0x66,0x42,0x42,0x66,0x3c,0x00,0x00,0x00,0x00,0x00},         // 009
{0xff,0xff,0xff,0xff,0xff,0xc3,0x99,0xbd,0xbd,0x99,0xc3,0xff,0xff,0xff,0xff,0xff},         // 00A
{0x00,0x00,0x1e,0x0e,0x1a,0x32,0x78,0xcc,0xcc,0xcc,0xcc,0x78,0x00,0x00,0x00,0x00},         // 00B
{0x00,0x00,0x3c,0x66,0x66,0x66,0x66,0x3c,0x18,0x7e,0x18,0x18,0x00,0x00,0x00,0x00},         // 00C
{0x00,0x00,0x3f,0x33,0x3f,0x30,0x30,0x30,0x30,0x70,0xf0,0xe0,0x00,0x00,0x00,0x00},         // 00D
{0x00,0x00,0x7f,0x63,0x7f,0x63,0x63,0x63,0x63,0x67,0xe7,0xe6,0xc0,0x00,0x00,0x00},         // 00E
{0x00,0x00,0x00,0x18,0x18,0xdb,0x3c,0xe7,0x3c,0xdb,0x18,0x18,0x00,0x00,0x00,0x00},         // 00F
{0x00,0x80,0xc0,0xe0,0xf0,0xf8,0xfe,0xf8,0xf0,0xe0,0xc0,0x80,0x00,0x00,0x00,0x00},         // 010
{0x00,0x02,0x06,0x0e,0x1e,0x3e,0xfe,0x3e,0x1e,0x0e,0x06,0x02,0x00,0x00,0x00,0x00},         // 011
{0x00,0x00,0x18,0x3c,0x7e,0x18,0x18,0x18,0x7e,0x3c,0x18,0x00,0x00,0x00,0x00,0x00},         // 012
{0x00,0x00,0x66,0x66,0x66,0x66,0x66,0x66,0x66,0x00,0x66,0x66,0x00,0x00,0x00,0x00},         // 013
{0x00,0x00,0x7f,0xdb,0xdb,0xdb,0x7b,0x1b,0x1b,0x1b,0x1b,0x1b,0x00,0x00,0x00,0x00},         // 014
{0x00,0x7c,0xc6,0x60,0x38,0x6c,0xc6,0xc6,0x6c,0x38,0x0c,0xc6,0x7c,0x00,0x00,0x00},         // 015
{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfe,0xfe,0xfe,0xfe,0x00,0x00,0x00,0x00},         // 016
{0x00,0x00,0x18,0x3c,0x7e,0x18,0x18,0x18,0x7e,0x3c,0x18,0x7e,0x00,0x00,0x00,0x00},         // 017
{0x00,0x00,0x18,0x3c,0x7e,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x00,0x00,0x00,0x00},         // 018
{0x00,0x00,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x7e,0x3c,0x18,0x00,0x00,0x00,0x00},         // 019
{0x00,0x00,0x00,0x00,0x00,0x18,0x0c,0xfe,0x0c,0x18,0x00,0x00,0x00,0x00,0x00,0x00},         // 01A
{0x00,0x00,0x00,0x00,0x00,0x30,0x60,0xfe,0x60,0x30,0x00,0x00,0x00,0x00,0x00,0x00},         // 01B
{0x00,0x00,0x00,0x00,0x00,0x00,0xc0,0xc0,0xc0,0xfe,0x00,0x00,0x00,0x00,0x00,0x00},         // 01C
{0x00,0x00,0x00,0x00,0x00,0x28,0x6c,0xfe,0x6c,0x28,0x00,0x00,0x00,0x00,0x00,0x00},         // 01D
{0x00,0x00,0x00,0x00,0x10,0x38,0x38,0x7c,0x7c,0xfe,0xfe,0x00,0x00,0x00,0x00,0x00},         // 01E
{0x00,0x00,0x00,0x00,0xfe,0xfe,0x7c,0x7c,0x38,0x38,0x10,0x00,0x00,0x00,0x00,0x00},         // 01F
{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 020
{0x00,0x00,0x18,0x3c,0x3c,0x3c,0x18,0x18,0x18,0x00,0x18,0x18,0x00,0x00,0x00,0x00},         // 021
{0x00,0x66,0x66,0x66,0x24,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 022
{0x00,0x00,0x00,0x6c,0x6c,0xfe,0x6c,0x6c,0x6c,0xfe,0x6c,0x6c,0x00,0x00,0x00,0x00},         // 023
{0x18,0x18,0x7c,0xc6,0xc2,0xc0,0x7c,0x06,0x06,0x86,0xc6,0x7c,0x18,0x18,0x00,0x00},         // 024
{0x00,0x00,0x00,0x00,0xc2,0xc6,0x0c,0x18,0x30,0x60,0xc6,0x86,0x00,0x00,0x00,0x00},         // 025
{0x00,0x00,0x38,0x6c,0x6c,0x38,0x76,0xdc,0xcc,0xcc,0xcc,0x76,0x00,0x00,0x00,0x00},         // 026
{0x00,0x30,0x30,0x30,0x60,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 027
{0x00,0x00,0x0c,0x18,0x30,0x30,0x30,0x30,0x30,0x30,0x18,0x0c,0x00,0x00,0x00,0x00},         // 028
{0x00,0x00,0x30,0x18,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x18,0x30,0x00,0x00,0x00,0x00},         // 029
{0x00,0x00,0x00,0x00,0x00,0x66,0x3c,0xff,0x3c,0x66,0x00,0x00,0x00,0x00,0x00,0x00},         // 02A
{0x00,0x00,0x00,0x00,0x00,0x18,0x18,0x7e,0x18,0x18,0x00,0x00,0x00,0x00,0x00,0x00},         // 02B
{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x18,0x18,0x30,0x00,0x00,0x00},         // 02C
{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7e,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 02D
{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x18,0x00,0x00,0x00,0x00},         // 02E
{0x00,0x00,0x00,0x00,0x02,0x06,0x0c,0x18,0x30,0x60,0xc0,0x80,0x00,0x00,0x00,0x00},         // 02F
{0x00,0x00,0x7c,0xc6,0xc6,0xce,0xde,0xf6,0xe6,0xc6,0xc6,0x7c,0x00,0x00,0x00,0x00},         // 030
{0x00,0x00,0x18,0x38,0x78,0x18,0x18,0x18,0x18,0x18,0x18,0x7e,0x00,0x00,0x00,0x00},         // 031
{0x00,0x00,0x7c,0xc6,0x06,0x0c,0x18,0x30,0x60,0xc0,0xc6,0xfe,0x00,0x00,0x00,0x00},         // 032
{0x00,0x00,0x7c,0xc6,0x06,0x06,0x3c,0x06,0x06,0x06,0xc6,0x7c,0x00,0x00,0x00,0x00},         // 033
{0x00,0x00,0x0c,0x1c,0x3c,0x6c,0xcc,0xfe,0x0c,0x0c,0x0c,0x1e,0x00,0x00,0x00,0x00},         // 034
{0x00,0x00,0xfe,0xc0,0xc0,0xc0,0xfc,0x06,0x06,0x06,0xc6,0x7c,0x00,0x00,0x00,0x00},         // 035
{0x00,0x00,0x38,0x60,0xc0,0xc0,0xfc,0xc6,0xc6,0xc6,0xc6,0x7c,0x00,0x00,0x00,0x00},         // 036
{0x00,0x00,0xfe,0xc6,0x06,0x06,0x0c,0x18,0x30,0x30,0x30,0x30,0x00,0x00,0x00,0x00},         // 037
{0x00,0x00,0x7c,0xc6,0xc6,0xc6,0x7c,0xc6,0xc6,0xc6,0xc6,0x7c,0x00,0x00,0x00,0x00},         // 038
{0x00,0x00,0x7c,0xc6,0xc6,0xc6,0x7e,0x06,0x06,0x06,0x0c,0x78,0x00,0x00,0x00,0x00},         // 039
{0x00,0x00,0x00,0x00,0x18,0x18,0x00,0x00,0x00,0x18,0x18,0x00,0x00,0x00,0x00,0x00},         // 03A
{0x00,0x00,0x00,0x00,0x18,0x18,0x00,0x00,0x00,0x18,0x18,0x30,0x00,0x00,0x00,0x00},         // 03B
{0x00,0x00,0x00,0x06,0x0c,0x18,0x30,0x60,0x30,0x18,0x0c,0x06,0x00,0x00,0x00,0x00},         // 03C
{0x00,0x00,0x00,0x00,0x00,0x7e,0x00,0x00,0x7e,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 03D
{0x00,0x00,0x00,0x60,0x30,0x18,0x0c,0x06,0x0c,0x18,0x30,0x60,0x00,0x00,0x00,0x00},         // 03E
{0x00,0x00,0x7c,0xc6,0xc6,0x0c,0x18,0x18,0x18,0x00,0x18,0x18,0x00,0x00,0x00,0x00},         // 03F
{0x00,0x00,0x7c,0xc6,0xc6,0xc6,0xde,0xde,0xde,0xdc,0xc0,0x7c,0x00,0x00,0x00,0x00},         // 040
{0x00,0x00,0x10,0x38,0x6c,0xc6,0xc6,0xfe,0xc6,0xc6,0xc6,0xc6,0x00,0x00,0x00,0x00},         // 041
{0x00,0x00,0xfc,0x66,0x66,0x66,0x7c,0x66,0x66,0x66,0x66,0xfc,0x00,0x00,0x00,0x00},         // 042
{0x00,0x00,0x3c,0x66,0xc2,0xc0,0xc0,0xc0,0xc0,0xc2,0x66,0x3c,0x00,0x00,0x00,0x00},         // 043
{0x00,0x00,0xf8,0x6c,0x66,0x66,0x66,0x66,0x66,0x66,0x6c,0xf8,0x00,0x00,0x00,0x00},         // 044
{0x00,0x00,0xfe,0x66,0x62,0x68,0x78,0x68,0x60,0x62,0x66,0xfe,0x00,0x00,0x00,0x00},         // 045
{0x00,0x00,0xfe,0x66,0x62,0x68,0x78,0x68,0x60,0x60,0x60,0xf0,0x00,0x00,0x00,0x00},         // 046
{0x00,0x00,0x3c,0x66,0xc2,0xc0,0xc0,0xde,0xc6,0xc6,0x66,0x3a,0x00,0x00,0x00,0x00},         // 047
{0x00,0x00,0xc6,0xc6,0xc6,0xc6,0xfe,0xc6,0xc6,0xc6,0xc6,0xc6,0x00,0x00,0x00,0x00},         // 048
{0x00,0x00,0x3c,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x3c,0x00,0x00,0x00,0x00},         // 049
{0x00,0x00,0x1e,0x0c,0x0c,0x0c,0x0c,0x0c,0xcc,0xcc,0xcc,0x78,0x00,0x00,0x00,0x00},         // 04A
{0x00,0x00,0xe6,0x66,0x66,0x6c,0x78,0x78,0x6c,0x66,0x66,0xe6,0x00,0x00,0x00,0x00},         // 04B
{0x00,0x00,0xf0,0x60,0x60,0x60,0x60,0x60,0x60,0x62,0x66,0xfe,0x00,0x00,0x00,0x00},         // 04C
{0x00,0x00,0xc6,0xee,0xfe,0xfe,0xd6,0xc6,0xc6,0xc6,0xc6,0xc6,0x00,0x00,0x00,0x00},         // 04D
{0x00,0x00,0xc6,0xe6,0xf6,0xfe,0xde,0xce,0xc6,0xc6,0xc6,0xc6,0x00,0x00,0x00,0x00},         // 04E
{0x00,0x00,0x7c,0xc6,0xc6,0xc6,0xc6,0xc6,0xc6,0xc6,0xc6,0x7c,0x00,0x00,0x00,0x00},         // 04F
{0x00,0x00,0xfc,0x66,0x66,0x66,0x7c,0x60,0x60,0x60,0x60,0xf0,0x00,0x00,0x00,0x00},         // 050
{0x00,0x00,0x7c,0xc6,0xc6,0xc6,0xc6,0xc6,0xc6,0xd6,0xde,0x7c,0x0c,0x0e,0x00,0x00},         // 051
{0x00,0x00,0xfc,0x66,0x66,0x66,0x7c,0x6c,0x66,0x66,0x66,0xe6,0x00,0x00,0x00,0x00},         // 052
{0x00,0x00,0x7c,0xc6,0xc6,0x60,0x38,0x0c,0x06,0xc6,0xc6,0x7c,0x00,0x00,0x00,0x00},         // 053
{0x00,0x00,0x7e,0x7e,0x5a,0x18,0x18,0x18,0x18,0x18,0x18,0x3c,0x00,0x00,0x00,0x00},         // 054
{0x00,0x00,0xc6,0xc6,0xc6,0xc6,0xc6,0xc6,0xc6,0xc6,0xc6,0x7c,0x00,0x00,0x00,0x00},         // 055
{0x00,0x00,0xc6,0xc6,0xc6,0xc6,0xc6,0xc6,0xc6,0x6c,0x38,0x10,0x00,0x00,0x00,0x00},         // 056
{0x00,0x00,0xc6,0xc6,0xc6,0xc6,0xd6,0xd6,0xd6,0xfe,0xee,0x6c,0x00,0x00,0x00,0x00},         // 057
{0x00,0x00,0xc6,0xc6,0x6c,0x7c,0x38,0x38,0x7c,0x6c,0xc6,0xc6,0x00,0x00,0x00,0x00},         // 058
{0x00,0x00,0x66,0x66,0x66,0x66,0x3c,0x18,0x18,0x18,0x18,0x3c,0x00,0x00,0x00,0x00},         // 059
{0x00,0x00,0xfe,0xc6,0x86,0x0c,0x18,0x30,0x60,0xc2,0xc6,0xfe,0x00,0x00,0x00,0x00},         // 05A
{0x00,0x00,0x3c,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x3c,0x00,0x00,0x00,0x00},         // 05B
{0x00,0x00,0x00,0x80,0xc0,0xe0,0x70,0x38,0x1c,0x0e,0x06,0x02,0x00,0x00,0x00,0x00},         // 05C
{0x00,0x00,0x3c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x3c,0x00,0x00,0x00,0x00},         // 05D
{0x10,0x38,0x6c,0xc6,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 05E
{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xff,0x00,0x00},         // 05F
{0x30,0x30,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 060
{0x00,0x00,0x00,0x00,0x00,0x78,0x0c,0x7c,0xcc,0xcc,0xcc,0x76,0x00,0x00,0x00,0x00},         // 061
{0x00,0x00,0xe0,0x60,0x60,0x78,0x6c,0x66,0x66,0x66,0x66,0x7c,0x00,0x00,0x00,0x00},         // 062
{0x00,0x00,0x00,0x00,0x00,0x7c,0xc6,0xc0,0xc0,0xc0,0xc6,0x7c,0x00,0x00,0x00,0x00},         // 063
{0x00,0x00,0x1c,0x0c,0x0c,0x3c,0x6c,0xcc,0xcc,0xcc,0xcc,0x76,0x00,0x00,0x00,0x00},         // 064
{0x00,0x00,0x00,0x00,0x00,0x7c,0xc6,0xfe,0xc0,0xc0,0xc6,0x7c,0x00,0x00,0x00,0x00},         // 065
{0x00,0x00,0x38,0x6c,0x64,0x60,0xf0,0x60,0x60,0x60,0x60,0xf0,0x00,0x00,0x00,0x00},         // 066
{0x00,0x00,0x00,0x00,0x00,0x76,0xcc,0xcc,0xcc,0xcc,0xcc,0x7c,0x0c,0xcc,0x78,0x00},         // 067
{0x00,0x00,0xe0,0x60,0x60,0x6c,0x76,0x66,0x66,0x66,0x66,0xe6,0x00,0x00,0x00,0x00},         // 068
{0x00,0x00,0x18,0x18,0x00,0x38,0x18,0x18,0x18,0x18,0x18,0x3c,0x00,0x00,0x00,0x00},         // 069
{0x00,0x00,0x06,0x06,0x00,0x0e,0x06,0x06,0x06,0x06,0x06,0x06,0x66,0x66,0x3c,0x00},         // 06A
{0x00,0x00,0xe0,0x60,0x60,0x66,0x6c,0x78,0x78,0x6c,0x66,0xe6,0x00,0x00,0x00,0x00},         // 06B
{0x00,0x00,0x38,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x3c,0x00,0x00,0x00,0x00},         // 06C
{0x00,0x00,0x00,0x00,0x00,0xec,0xfe,0xd6,0xd6,0xd6,0xd6,0xc6,0x00,0x00,0x00,0x00},         // 06D
{0x00,0x00,0x00,0x00,0x00,0xdc,0x66,0x66,0x66,0x66,0x66,0x66,0x00,0x00,0x00,0x00},         // 06E
{0x00,0x00,0x00,0x00,0x00,0x7c,0xc6,0xc6,0xc6,0xc6,0xc6,0x7c,0x00,0x00,0x00,0x00},         // 06F
{0x00,0x00,0x00,0x00,0x00,0xdc,0x66,0x66,0x66,0x66,0x66,0x7c,0x60,0x60,0xf0,0x00},         // 070
{0x00,0x00,0x00,0x00,0x00,0x76,0xcc,0xcc,0xcc,0xcc,0xcc,0x7c,0x0c,0x0c,0x1e,0x00},         // 071
{0x00,0x00,0x00,0x00,0x00,0xdc,0x76,0x66,0x60,0x60,0x60,0xf0,0x00,0x00,0x00,0x00},         // 072
{0x00,0x00,0x00,0x00,0x00,0x7c,0xc6,0x60,0x38,0x0c,0xc6,0x7c,0x00,0x00,0x00,0x00},         // 073
{0x00,0x00,0x10,0x30,0x30,0xfc,0x30,0x30,0x30,0x30,0x36,0x1c,0x00,0x00,0x00,0x00},         // 074
{0x00,0x00,0x00,0x00,0x00,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0x76,0x00,0x00,0x00,0x00},         // 075
{0x00,0x00,0x00,0x00,0x00,0x66,0x66,0x66,0x66,0x66,0x3c,0x18,0x00,0x00,0x00,0x00},         // 076
{0x00,0x00,0x00,0x00,0x00,0xc6,0xc6,0xd6,0xd6,0xd6,0xfe,0x6c,0x00,0x00,0x00,0x00},         // 077
{0x00,0x00,0x00,0x00,0x00,0xc6,0x6c,0x38,0x38,0x38,0x6c,0xc6,0x00,0x00,0x00,0x00},         // 078
{0x00,0x00,0x00,0x00,0x00,0xc6,0xc6,0xc6,0xc6,0xc6,0xc6,0x7e,0x06,0x0c,0xf8,0x00},         // 079
{0x00,0x00,0x00,0x00,0x00,0xfe,0xcc,0x18,0x30,0x60,0xc6,0xfe,0x00,0x00,0x00,0x00},         // 07A
{0x00,0x00,0x0e,0x18,0x18,0x18,0x70,0x18,0x18,0x18,0x18,0x0e,0x00,0x00,0x00,0x00},         // 07B
{0x00,0x00,0x18,0x18,0x18,0x18,0x00,0x18,0x18,0x18,0x18,0x18,0x00,0x00,0x00,0x00},         // 07C
{0x00,0x00,0x70,0x18,0x18,0x18,0x0e,0x18,0x18,0x18,0x18,0x70,0x00,0x00,0x00,0x00},         // 07D
{0x00,0x00,0x76,0xdc,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 07E
{0x00,0x00,0x00,0x00,0x10,0x38,0x6c,0xc6,0xc6,0xc6,0xfe,0x00,0x00,0x00,0x00,0x00},         // 07F
{0x00,0x00,0x3c,0x66,0xc2,0xc0,0xc0,0xc0,0xc2,0x66,0x3c,0x0c,0x06,0x7c,0x00,0x00},         // 080
{0x00,0x00,0xcc,0x00,0x00,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0x76,0x00,0x00,0x00,0x00},         // 081
{0x00,0x0c,0x18,0x30,0x00,0x7c,0xc6,0xfe,0xc0,0xc0,0xc6,0x7c,0x00,0x00,0x00,0x00},         // 082
{0x00,0x10,0x38,0x6c,0x00,0x78,0x0c,0x7c,0xcc,0xcc,0xcc,0x76,0x00,0x00,0x00,0x00},         // 083
{0x00,0x00,0xcc,0x00,0x00,0x78,0x0c,0x7c,0xcc,0xcc,0xcc,0x76,0x00,0x00,0x00,0x00},         // 084
{0x00,0x60,0x30,0x18,0x00,0x78,0x0c,0x7c,0xcc,0xcc,0xcc,0x76,0x00,0x00,0x00,0x00},         // 085
{0x00,0x38,0x6c,0x38,0x00,0x78,0x0c,0x7c,0xcc,0xcc,0xcc,0x76,0x00,0x00,0x00,0x00},         // 086
{0x00,0x00,0x00,0x00,0x3c,0x66,0x60,0x60,0x66,0x3c,0x0c,0x06,0x3c,0x00,0x00,0x00},         // 087
{0x00,0x10,0x38,0x6c,0x00,0x7c,0xc6,0xfe,0xc0,0xc0,0xc6,0x7c,0x00,0x00,0x00,0x00},         // 088
{0x00,0x00,0xc6,0x00,0x00,0x7c,0xc6,0xfe,0xc0,0xc0,0xc6,0x7c,0x00,0x00,0x00,0x00},         // 089
{0x00,0x60,0x30,0x18,0x00,0x7c,0xc6,0xfe,0xc0,0xc0,0xc6,0x7c,0x00,0x00,0x00,0x00},         // 08A
{0x00,0x00,0x66,0x00,0x00,0x38,0x18,0x18,0x18,0x18,0x18,0x3c,0x00,0x00,0x00,0x00},         // 08B
{0x00,0x18,0x3c,0x66,0x00,0x38,0x18,0x18,0x18,0x18,0x18,0x3c,0x00,0x00,0x00,0x00},         // 08C
{0x00,0x60,0x30,0x18,0x00,0x38,0x18,0x18,0x18,0x18,0x18,0x3c,0x00,0x00,0x00,0x00},         // 08D
{0x00,0xc6,0x00,0x10,0x38,0x6c,0xc6,0xc6,0xfe,0xc6,0xc6,0xc6,0x00,0x00,0x00,0x00},         // 08E
{0x38,0x6c,0x38,0x00,0x38,0x6c,0xc6,0xc6,0xfe,0xc6,0xc6,0xc6,0x00,0x00,0x00,0x00},         // 08F
{0x18,0x30,0x60,0x00,0xfe,0x66,0x60,0x7c,0x60,0x60,0x66,0xfe,0x00,0x00,0x00,0x00},         // 090
{0x00,0x00,0x00,0x00,0x6c,0xfe,0xb2,0x32,0x7e,0xd8,0xd8,0x6e,0x00,0x00,0x00,0x00},         // 091
{0x00,0x00,0x3e,0x6c,0xcc,0xcc,0xfe,0xcc,0xcc,0xcc,0xcc,0xce,0x00,0x00,0x00,0x00},         // 092
{0x00,0x10,0x38,0x6c,0x00,0x7c,0xc6,0xc6,0xc6,0xc6,0xc6,0x7c,0x00,0x00,0x00,0x00},         // 093
{0x00,0x00,0xc6,0x00,0x00,0x7c,0xc6,0xc6,0xc6,0xc6,0xc6,0x7c,0x00,0x00,0x00,0x00},         // 094
{0x00,0x60,0x30,0x18,0x00,0x7c,0xc6,0xc6,0xc6,0xc6,0xc6,0x7c,0x00,0x00,0x00,0x00},         // 095
{0x00,0x30,0x78,0xcc,0x00,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0x76,0x00,0x00,0x00,0x00},         // 096
{0x00,0x60,0x30,0x18,0x00,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0x76,0x00,0x00,0x00,0x00},         // 097
{0x00,0x00,0xc6,0x00,0x00,0xc6,0xc6,0xc6,0xc6,0xc6,0xc6,0x7e,0x06,0x0c,0x78,0x00},         // 098
{0x00,0xc6,0x00,0x7c,0xc6,0xc6,0xc6,0xc6,0xc6,0xc6,0xc6,0x7c,0x00,0x00,0x00,0x00},         // 099
{0x00,0xc6,0x00,0xc6,0xc6,0xc6,0xc6,0xc6,0xc6,0xc6,0xc6,0x7c,0x00,0x00,0x00,0x00},         // 09A
{0x00,0x18,0x18,0x3c,0x66,0x60,0x60,0x60,0x66,0x3c,0x18,0x18,0x00,0x00,0x00,0x00},         // 09B
{0x00,0x38,0x6c,0x64,0x60,0xf0,0x60,0x60,0x60,0x60,0xe6,0xfc,0x00,0x00,0x00,0x00},         // 09C
{0x00,0x00,0x66,0x66,0x3c,0x18,0x7e,0x18,0x7e,0x18,0x18,0x18,0x00,0x00,0x00,0x00},         // 09D
{0x00,0xf8,0xcc,0xcc,0xf8,0xc4,0xcc,0xde,0xcc,0xcc,0xcc,0xc6,0x00,0x00,0x00,0x00},         // 09E
{0x00,0x0e,0x1b,0x18,0x18,0x18,0x7e,0x18,0x18,0x18,0x18,0x18,0xd8,0x70,0x00,0x00},         // 09F
{0x00,0x18,0x30,0x60,0x00,0x78,0x0c,0x7c,0xcc,0xcc,0xcc,0x76,0x00,0x00,0x00,0x00},         // 0A0
{0x00,0x0c,0x18,0x30,0x00,0x38,0x18,0x18,0x18,0x18,0x18,0x3c,0x00,0x00,0x00,0x00},         // 0A1
{0x00,0x18,0x30,0x60,0x00,0x7c,0xc6,0xc6,0xc6,0xc6,0xc6,0x7c,0x00,0x00,0x00,0x00},         // 0A2
{0x00,0x18,0x30,0x60,0x00,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0x76,0x00,0x00,0x00,0x00},         // 0A3
{0x00,0x00,0x76,0xdc,0x00,0xdc,0x66,0x66,0x66,0x66,0x66,0x66,0x00,0x00,0x00,0x00},         // 0A4
{0x76,0xdc,0x00,0xc6,0xe6,0xf6,0xfe,0xde,0xce,0xc6,0xc6,0xc6,0x00,0x00,0x00,0x00},         // 0A5
{0x00,0x3c,0x6c,0x6c,0x3e,0x00,0x7e,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 0A6
{0x00,0x38,0x6c,0x6c,0x38,0x00,0x7c,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 0A7
{0x00,0x00,0x30,0x30,0x00,0x30,0x30,0x60,0xc0,0xc6,0xc6,0x7c,0x00,0x00,0x00,0x00},         // 0A8
{0x00,0x00,0x00,0x00,0x00,0x00,0xfe,0xc0,0xc0,0xc0,0xc0,0x00,0x00,0x00,0x00,0x00},         // 0A9
{0x00,0x00,0x00,0x00,0x00,0x00,0xfe,0x06,0x06,0x06,0x06,0x00,0x00,0x00,0x00,0x00},         // 0AA
{0x00,0xc0,0xc0,0xc2,0xc6,0xcc,0x18,0x30,0x60,0xdc,0x86,0x0c,0x18,0x3e,0x00,0x00},         // 0AB
{0x00,0xc0,0xc0,0xc2,0xc6,0xcc,0x18,0x30,0x66,0xce,0x9e,0x3e,0x06,0x06,0x00,0x00},         // 0AC
{0x00,0x00,0x18,0x18,0x00,0x18,0x18,0x18,0x3c,0x3c,0x3c,0x18,0x00,0x00,0x00,0x00},         // 0AD
{0x00,0x00,0x00,0x00,0x00,0x36,0x6c,0xd8,0x6c,0x36,0x00,0x00,0x00,0x00,0x00,0x00},         // 0AE
{0x00,0x00,0x00,0x00,0x00,0xd8,0x6c,0x36,0x6c,0xd8,0x00,0x00,0x00,0x00,0x00,0x00},         // 0AF
{0x11,0x44,0x11,0x44,0x11,0x44,0x11,0x44,0x11,0x44,0x11,0x44,0x11,0x44,0x11,0x44},         // 0B0
{0x55,0xaa,0x55,0xaa,0x55,0xaa,0x55,0xaa,0x55,0xaa,0x55,0xaa,0x55,0xaa,0x55,0xaa},         // 0B1
{0xdd,0x77,0xdd,0x77,0xdd,0x77,0xdd,0x77,0xdd,0x77,0xdd,0x77,0xdd,0x77,0xdd,0x77},         // 0B2
{0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18},         // 0B3
{0x18,0x18,0x18,0x18,0x18,0x18,0x18,0xf8,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18},         // 0B4
{0x18,0x18,0x18,0x18,0x18,0xf8,0x18,0xf8,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18},         // 0B5
{0x36,0x36,0x36,0x36,0x36,0x36,0x36,0xf6,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36},         // 0B6
{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfe,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36},         // 0B7
{0x00,0x00,0x00,0x00,0x00,0xf8,0x18,0xf8,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18},         // 0B8
{0x36,0x36,0x36,0x36,0x36,0xf6,0x06,0xf6,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36},         // 0B9
{0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36},         // 0BA
{0x00,0x00,0x00,0x00,0x00,0xfe,0x06,0xf6,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36},         // 0BB
{0x36,0x36,0x36,0x36,0x36,0xf6,0x06,0xfe,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 0BC
{0x36,0x36,0x36,0x36,0x36,0x36,0x36,0xfe,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 0BD
{0x18,0x18,0x18,0x18,0x18,0xf8,0x18,0xf8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 0BE
{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xf8,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18},         // 0BF
{0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x1f,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 0C0
{0x18,0x18,0x18,0x18,0x18,0x18,0x18,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 0C1
{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xff,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18},         // 0C2
{0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x1f,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18},         // 0C3
{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 0C4
{0x18,0x18,0x18,0x18,0x18,0x18,0x18,0xff,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18},         // 0C5
{0x18,0x18,0x18,0x18,0x18,0x1f,0x18,0x1f,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18},         // 0C6
{0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x37,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36},         // 0C7
{0x36,0x36,0x36,0x36,0x36,0x37,0x30,0x3f,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 0C8
{0x00,0x00,0x00,0x00,0x00,0x3f,0x30,0x37,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36},         // 0C9
{0x36,0x36,0x36,0x36,0x36,0xf7,0x00,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 0CA
{0x00,0x00,0x00,0x00,0x00,0xff,0x00,0xf7,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36},         // 0CB
{0x36,0x36,0x36,0x36,0x36,0x37,0x30,0x37,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36},         // 0CC
{0x00,0x00,0x00,0x00,0x00,0xff,0x00,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 0CD
{0x36,0x36,0x36,0x36,0x36,0xf7,0x00,0xf7,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36},         // 0CE
{0x18,0x18,0x18,0x18,0x18,0xff,0x00,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 0CF
{0x36,0x36,0x36,0x36,0x36,0x36,0x36,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 0D0
{0x00,0x00,0x00,0x00,0x00,0xff,0x00,0xff,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18},         // 0D1
{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xff,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36},         // 0D2
{0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x3f,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 0D3
{0x18,0x18,0x18,0x18,0x18,0x1f,0x18,0x1f,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 0D4
{0x00,0x00,0x00,0x00,0x00,0x1f,0x18,0x1f,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18},         // 0D5
{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3f,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36},         // 0D6
{0x36,0x36,0x36,0x36,0x36,0x36,0x36,0xff,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36},         // 0D7
{0x18,0x18,0x18,0x18,0x18,0xff,0x18,0xff,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18},         // 0D8
{0x18,0x18,0x18,0x18,0x18,0x18,0x18,0xf8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 0D9
{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1f,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18},         // 0DA
{0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff},         // 0DB
{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff},         // 0DC
{0xf0,0xf0,0xf0,0xf0,0xf0,0xf0,0xf0,0xf0,0xf0,0xf0,0xf0,0xf0,0xf0,0xf0,0xf0,0xf0},         // 0DD
{0x0f,0x0f,0x0f,0x0f,0x0f,0x0f,0x0f,0x0f,0x0f,0x0f,0x0f,0x0f,0x0f,0x0f,0x0f,0x0f},         // 0DE
{0xff,0xff,0xff,0xff,0xff,0xff,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 0DF
{0x00,0x00,0x00,0x00,0x00,0x76,0xdc,0xd8,0xd8,0xd8,0xdc,0x76,0x00,0x00,0x00,0x00},         // 0E0
{0x00,0x00,0x78,0xcc,0xcc,0xcc,0xd8,0xcc,0xc6,0xc6,0xc6,0xcc,0x00,0x00,0x00,0x00},         // 0E1
{0x00,0x00,0xfe,0xc6,0xc6,0xc0,0xc0,0xc0,0xc0,0xc0,0xc0,0xc0,0x00,0x00,0x00,0x00},         // 0E2
{0x00,0x00,0x00,0x00,0xfe,0x6c,0x6c,0x6c,0x6c,0x6c,0x6c,0x6c,0x00,0x00,0x00,0x00},         // 0E3
{0x00,0x00,0x00,0xfe,0xc6,0x60,0x30,0x18,0x30,0x60,0xc6,0xfe,0x00,0x00,0x00,0x00},         // 0E4
{0x00,0x00,0x00,0x00,0x00,0x7e,0xd8,0xd8,0xd8,0xd8,0xd8,0x70,0x00,0x00,0x00,0x00},         // 0E5
{0x00,0x00,0x00,0x00,0x66,0x66,0x66,0x66,0x66,0x7c,0x60,0x60,0xc0,0x00,0x00,0x00},         // 0E6
{0x00,0x00,0x00,0x00,0x76,0xdc,0x18,0x18,0x18,0x18,0x18,0x18,0x00,0x00,0x00,0x00},         // 0E7
{0x00,0x00,0x00,0x7e,0x18,0x3c,0x66,0x66,0x66,0x3c,0x18,0x7e,0x00,0x00,0x00,0x00},         // 0E8
{0x00,0x00,0x00,0x38,0x6c,0xc6,0xc6,0xfe,0xc6,0xc6,0x6c,0x38,0x00,0x00,0x00,0x00},         // 0E9
{0x00,0x00,0x38,0x6c,0xc6,0xc6,0xc6,0x6c,0x6c,0x6c,0x6c,0xee,0x00,0x00,0x00,0x00},         // 0EA
{0x00,0x00,0x1e,0x30,0x18,0x0c,0x3e,0x66,0x66,0x66,0x66,0x3c,0x00,0x00,0x00,0x00},         // 0EB
{0x00,0x00,0x00,0x00,0x00,0x7e,0xdb,0xdb,0xdb,0x7e,0x00,0x00,0x00,0x00,0x00,0x00},         // 0EC
{0x00,0x00,0x00,0x03,0x06,0x7e,0xdb,0xdb,0xf3,0x7e,0x60,0xc0,0x00,0x00,0x00,0x00},         // 0ED
{0x00,0x00,0x1c,0x30,0x60,0x60,0x7c,0x60,0x60,0x60,0x30,0x1c,0x00,0x00,0x00,0x00},         // 0EE
{0x00,0x00,0x00,0x7c,0xc6,0xc6,0xc6,0xc6,0xc6,0xc6,0xc6,0xc6,0x00,0x00,0x00,0x00},         // 0EF
{0x00,0x00,0x00,0x00,0xfe,0x00,0x00,0xfe,0x00,0x00,0xfe,0x00,0x00,0x00,0x00,0x00},         // 0F0
{0x00,0x00,0x00,0x00,0x18,0x18,0x7e,0x18,0x18,0x00,0x00,0xff,0x00,0x00,0x00,0x00},         // 0F1
{0x00,0x00,0x00,0x30,0x18,0x0c,0x06,0x0c,0x18,0x30,0x00,0x7e,0x00,0x00,0x00,0x00},         // 0F2
{0x00,0x00,0x00,0x0c,0x18,0x30,0x60,0x30,0x18,0x0c,0x00,0x7e,0x00,0x00,0x00,0x00},         // 0F3
{0x00,0x00,0x0e,0x1b,0x1b,0x1b,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18},         // 0F4
{0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0xd8,0xd8,0xd8,0x70,0x00,0x00,0x00,0x00},         // 0F5
{0x00,0x00,0x00,0x00,0x18,0x18,0x00,0x7e,0x00,0x18,0x18,0x00,0x00,0x00,0x00,0x00},         // 0F6
{0x00,0x00,0x00,0x00,0x00,0x76,0xdc,0x00,0x76,0xdc,0x00,0x00,0x00,0x00,0x00,0x00},         // 0F7
{0x00,0x38,0x6c,0x6c,0x38,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 0F8
{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 0F9
{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 0FA
{0x00,0x0f,0x0c,0x0c,0x0c,0x0c,0x0c,0xec,0x6c,0x6c,0x3c,0x1c,0x00,0x00,0x00,0x00},         // 0FB
{0x00,0xd8,0x6c,0x6c,0x6c,0x6c,0x6c,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 0FC
{0x00,0x70,0xd8,0x30,0x60,0xc8,0xf8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 0FD
{0x00,0x00,0x00,0x00,0x7c,0x7c,0x7c,0x7c,0x7c,0x7c,0x7c,0x00,0x00,0x00,0x00,0x00},         // 0FE
{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},         // 0FF       
};

ast_ExtRegInfo astExtReg[] = {
    {0xA0, 0x0F},
    {0xA1, 0x04},
    {0xA2, 0x1C},
    {0xB6, 0x04},
    {0xB7, 0x00},
    {0xFF, 0xFF},
};

ast_ExtRegInfo astExtReg_AST2300[] = {
    {0xA0, 0x0F},
    {0xA1, 0x04},
    {0xA2, 0x1F},
    {0xB6, 0x24},
    {0xB7, 0x00},
    {0xFF, 0xFF},
};

UINT32 ddr3_1600_timing_table[REGTBL_NUM] = {
0x64604D38,                  // 0x010
0x29690599,                  // 0x014
0x00000300,                  // 0x018
0x00000000,                  // 0x020
0x00000000,                  // 0x024
0x02181E70,                  // 0x02C
0x00000040,                  // 0x030
0x00000024,                  // 0x214
0x02001300,                  // 0x2E0
0x0E0000A0,                  // 0x2E4
0x000E001B,                  // 0x2E8
0x35B8C105,                  // 0x2EC
0x08090408,                  // 0x2F0
0x9B000800,                  // 0x2F4
0x0E400A00,                  // 0x2F8
0x9971452F,                  // tRFC
0x000071C1};                 // PLL

UINT32 ddr4_1600_timing_table[REGTBL_NUM] = {
0x63604E37,                  // 0x010
0xE97AFA99,                  // 0x014
0x00019000,                  // 0x018
0x08000000,                  // 0x020
0x00000400,                  // 0x024
0x00000410,                  // 0x02C
0x00000101,                  // 0x030
0x00000024,                  // 0x214
0x03002900,                  // 0x2E0
0x0E0000A0,                  // 0x2E4
0x000E001C,                  // 0x2E8
0x35B8C106,                  // 0x2EC
0x08080607,                  // 0x2F0
0x9B000900,                  // 0x2F4
0x0E400A00,                  // 0x2F8
0x99714545,                  // tRFC
0x000071C1};                 // PLL

//AST2500 VCLK Table without SCU0130 Setting
VBIOS_DCLK_INFO DCLKTable_AST2500 [] = {
    {0x2C, 0xE7, 0x03},                         //00: VCLK25_175   
    {0x95, 0x62, 0x03},                         //01: VCLK28_322   
    {0x67, 0x63, 0x01},                         //02: VCLK31_5     
    {0x76, 0x63, 0x01},                         //03: VCLK36       
    {0xEE, 0x67, 0x01},                         //04: VCLK40       
    {0x82, 0x62, 0x01},                         //05: VCLK49_5             
    {0xC6, 0x64, 0x01},                         //06: VCLK50            
    {0x94, 0x62, 0x01},                         //07: VCLK56_25         
    {0x80, 0x64, 0x00},                         //08: VCLK65           
    {0x7B, 0x63, 0x00},                         //09: VCLK75               
    {0x67, 0x62, 0x00},                         //0A: VCLK78_75    
    {0x7C, 0x62, 0x00},                         //0B: VCLK94_5          
    {0x8E, 0x62, 0x00},                         //0C: VCLK108           
    {0x85, 0x24, 0x00},                         //0D: VCLK135           
    {0x67, 0x22, 0x00},                         //0E: VCLK157_5         
    {0x6A, 0x22, 0x00},                         //0F: VCLK162 
    {0x4d, 0x4c, 0x80},                         //10: VCLK154  
    {0x68, 0x6f, 0x80},                         //11: VCLK83.5                    
    {0x28, 0x49, 0x80},                         //12: VCLK106.5                   
    {0x37, 0x49, 0x80},                         //13: VCLK146.25                  
    {0x1f, 0x45, 0x80},                         //14: VCLK148.5  
    {0x47, 0x6c, 0x80},                         //15: VCLK71                                             
    {0x25, 0x65, 0x80},                         //16: VCLK88.75                                          
    {0x58, 0x01, 0x42},                         //17: VCLK119
    {0x32, 0x67, 0x80},                         //18: VCLK85_5 
    {0x6a, 0x6d, 0x80},                         //19: VCLK97_75
    {0x44, 0x20, 0x43},                         //1A: VCLK118_25
    {0x35, 0x68, 0x80},                         //1B: VCLK79_5 
};

VBIOS_DCLK_INFO DCLKTable [] = {
    {0x2C, 0xE7, 0x03},                         //00: VCLK25_175   
    {0x95, 0x62, 0x03},                         //01: VCLK28_322   
    {0x67, 0x63, 0x01},                         //02: VCLK31_5     
    {0x76, 0x63, 0x01},                         //03: VCLK36       
    {0xEE, 0x67, 0x01},                         //04: VCLK40       
    {0x82, 0x62, 0x01},                         //05: VCLK49_5             
    {0xC6, 0x64, 0x01},                         //06: VCLK50            
    {0x94, 0x62, 0x01},                         //07: VCLK56_25         
    {0x80, 0x64, 0x00},                         //08: VCLK65           
    {0x7B, 0x63, 0x00},                         //09: VCLK75               
    {0x67, 0x62, 0x00},                         //0A: VCLK78_75    
    {0x7C, 0x62, 0x00},                         //0B: VCLK94_5          
    {0x8E, 0x62, 0x00},                         //0C: VCLK108           
    {0x85, 0x24, 0x00},                         //0D: VCLK135           
    {0x67, 0x22, 0x00},                         //0E: VCLK157_5         
    {0x6A, 0x22, 0x00},                         //0F: VCLK162 
    {0x4d, 0x4c, 0x80},                         //10: VCLK154  
    {0x68, 0x6f, 0x80},                         //11: VCLK83.5                    
    {0x28, 0x49, 0x80},                         //12: VCLK106.5                   
    {0x37, 0x49, 0x80},                         //13: VCLK146.25                  
    {0x1f, 0x45, 0x80},                         //14: VCLK148.5  
    {0x47, 0x6c, 0x80},                         //15: VCLK71                                             
    {0x25, 0x65, 0x80},                         //16: VCLK88.75                                          
    {0x77, 0x58, 0x80},                         //17: VCLK119
    {0x32, 0x67, 0x80},                         //18: VCLK85_5 
    {0x6a, 0x6d, 0x80},                         //19: VCLK97_75
    {0x3b, 0x2c, 0x81},                         //1A: VCLK118_25  
    {0x35, 0x68, 0x80},                         //1B: VCLK79_5 
};


/**
    This function sets an I/O port with the provided data input.

    @param UINT16 Port - I/O port to be set
    @param UINT8  Data - Data to be set to I/O port

    @retval VOID
**/

VOID
SetReg (
    UINT16  Port,
    UINT8   Data )
{
   IoWrite8 ((UINTN)Port, Data);  
}

/**
    This function gets the data from the provided I/O port.

    @param UINT16 Port - I/O port to be read and returned.

    @retval UINT8 Data from I/O Port.
**/

UINT8
GetReg (
    UINT16  Port )
{
    UINT8   Data8;
    
    Data8 = IoRead8((UINTN)Port);
    
    return Data8;
}

/**
    This function reads 8-bits from the specified PCI device's register.

    @param UINT32 CfgBase - MMIO PCI configuration base.
    @param UINT64 Segment - Segment number of the PCI device.
    @param UINT8  Bus - Bus number of the PCI device.
    @param UINT8  Dev - Device number of the PCI device.
    @param UINT8  Func - Function number of the PCI device.
    @param UINT16 Reg - Register/offset to be read on the PCI device.

    @retval UINT8 Data from PCI device.
**/

UINT8
AvPciSegmentRead8 (
    UINT32  CfgBase,
    UINT64  Segment,
    UINT8   Bus,
    UINT8   Dev,
    UINT8   Func,
    UINT16  Reg )
{
    return PciSegmentRead8((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, Bus, Dev, Func, Reg));
}

/**
    This function reads 16-bits from the specified PCI device's register.

    @param UINT32 CfgBase - MMIO PCI configuration base.
    @param UINT64 Segment - Segment number of the PCI device.
    @param UINT8  Bus - Bus number of the PCI device.
    @param UINT8  Dev - Device number of the PCI device.
    @param UINT8  Func - Function number of the PCI device.
    @param UINT16 Reg - Register/offset to be read on the PCI device.

    @retval UINT16 Data from PCI device.
**/

UINT16
AvPciSegmentRead16 (
    UINT32  CfgBase,
    UINT64  Segment,
    UINT8   Bus,
    UINT8   Dev,
    UINT8   Func,
    UINT16  Reg )
{
    return PciSegmentRead16((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, Bus, Dev, Func, Reg));
}

/**
    This function reads 32-bits from the specified PCI device's register.

    @param UINT32 CfgBase - MMIO PCI configuration base.
    @param UINT64 Segment - Segment number of the PCI device.
    @param UINT8 Bus - Bus number of the PCI device.
    @param UINT8  Dev - Device number of the PCI device.
    @param UINT8  Func - Function number of the PCI device.
    @param UINT16 Reg - Register/offset to be read on the PCI device.

    @retval UINT32 Data from PCI device.
**/

UINT32
AvPciSegmentRead32 (
    UINT32  CfgBase,
    UINT64  Segment,
    UINT8   Bus,
    UINT8   Dev,
    UINT8   Func,
    UINT16  Reg )
{
    return PciSegmentRead32((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, Bus, Dev, Func, Reg));
}

/**
    This function sets the provided Index register and Data registers of an 
    Indexed I/O Port.  It assumes that the Data port is exactly one byte higher
    than the Index port.

    @param UINT16 Port - Index I/O port.
    @param UINT8  Index - Index value to be written to the Index I/O port.
    @param UINT8  Data - Data to be written to the Data I/O port.

    @retval VOID
**/

VOID
SetIndexReg (
    UINT16  Port,
    UINT8   Index,
    UINT8   Data )
{
    SetReg(Port++, Index);
    SetReg(Port, Data);
}

/**
    This function sets the provided Index register, then reads the Data register
    and writes back the And/Or value along with the original data.  It assumes
    that the Data port is exactly one byte higher than the Index port.

    @param UINT16 Port - Index I/O port.
    @param UINT8  Index - Index value to be written to the Index I/O port.
    @param UINT8  And - AND mask to be written to the Data I/O port.
    @param UINT8  Or - OR mask to be written to the Data I/O port.

    @retval VOID
**/

VOID
ReadSetIndexReg (
    UINT16  Port,
    UINT8   Index,
    UINT8   And,
    UINT8   Or )
{
    UINT8 Temp;
    SetReg(Port++, Index);
    Temp = GetReg(Port);
    SetReg(Port, (Temp & And) | Or);
}

/**
    This function reads and returns the Data from an Index I/O port after
    applying the provided AND mask.  It assumes that the Data port is
    exactly one byte higher than the Index port.

    @param UINT16 Port - Index I/O port.
    @param UINT8  Index - Index value to be written to the Index I/O port.
    @param UINT8  Mask - AND mask to be applied to the Data.

    @retval UINT8 Data I/O port value with applied AND Mask.
**/

UINT8
GetIndexReg (
    UINT16  Port,
    UINT8   Index,
    UINT8   Mask )
{
    UINT8 Value;
    SetReg(Port++, Index);
    Value = GetReg(Port);
    return Value & Mask;
}

/**
    This function reads and returns the Data from an Index I/O port.  
    It assumes that the Data port is exactly one byte higher than the Index port.

    @param UINT16 Port - Index I/O port.
    @param UINT8  Index - Index value to be written to the Index I/O port.

    @retval UINT8 Data I/O port value
**/
UINT8 GetIndexReg2 (
    UINT16 Port,
    UINT8 Index
)
{
    UINT8    Value;
    SetReg(Port++, Index);
    Value = GetReg(Port);
    return Value;
  
}


UINT32
MIndwm (
    UINT8   *MmioBase,
    UINT32  R ) 
{
    volatile UINT32 *Mmio32Addr;
    
    Mmio32Addr = (UINT32*) (MmioBase + 0xF004);
    *Mmio32Addr = R & 0xFFFF0000;
    Mmio32Addr = (UINT32*) (MmioBase + 0xF000);
    *Mmio32Addr = 0x1;
    
    Mmio32Addr = (UINT32*) (MmioBase + 0x10000 + (R & 0x0000FFFF));
    return (*Mmio32Addr);
}

VOID
MOutdwm (
    UINT8  *MmioBase,
    UINT32  R,
    UINT32  V )
{
    volatile UINT32 *Mmio32Addr;
    Mmio32Addr = (UINT32*) (MmioBase + 0xF004);
     *Mmio32Addr = R & 0xFFFF0000;
    Mmio32Addr = (UINT32*) (MmioBase + 0xF000);
    *Mmio32Addr = 0x1;

    Mmio32Addr = (UINT32*) (MmioBase + 0x10000 + (R & 0xFFFF));
    *Mmio32Addr = V;
}

/**
    This function delays for the provided number of microseconds.

    @param EFI_PEI_SERVICES **PeiServices - Double pointer to PEI Services.
    @param UINT32 Val - Number of microseconds to delay.

    @retval VOID
**/

VOID
uDelay (
    EFI_PEI_SERVICES  **PeiServices,
    UINT32            Val )
{
    EFI_STATUS Status;
    EFI_PEI_STALL_PPI *StallPpi;
    
    Status = PeiServicesLocatePpi (
        &gEfiPeiStallPpiGuid, 
        0,
        NULL, 
        (VOID **)&StallPpi
    );
    ASSERT_EFI_ERROR(Status);
    if (!EFI_ERROR(Status)) {
        StallPpi->Stall (
            (CONST EFI_PEI_SERVICES**)PeiServices,
            StallPpi,
            Val
        );
    }
}

/**
    This function turns off the display through the VGA Sequencer Clocking
    Mode register by setting the Screen Disable bit.

    @param UINT16 IoBase - IO Base address of the video card.

    @retval VOID
**/

VOID
DisplayOff (
    UINT16  IoBase )
{
    ReadSetIndexReg(IoBase + SEQ_PORT,0x01, 0xDF, 0x20);    /* screen off */
}

/**
    This function turns on the display through the VGA Sequencer Clocking
    Mode register by clearing the Screen Disable bit.

    @param UINT16 IoBase - IO Base address of the video card.

    @retval VOID
**/

VOID
DisplayOn (
    UINT16  IoBase )
{   
    ReadSetIndexReg(IoBase + SEQ_PORT,0x01, 0xDF, 0x00);    /* screen on */
}

/**
    This function enables the display by writing 1 to the Video Subsystem
    Enable register.

    @param UINT16 IoBase - IO Base address of the video card.

    @retval VOID
**/

VOID
EnableVga (
    UINT16  IoBase )
{
    UINT8 Data;

    Data = GetReg(IoBase + VGA_ENABLE);
    if (!(Data & 0x01)) {
        SetReg(IoBase + VGA_ENABLE, 0x1);
        SetReg(IoBase + MISC_PORT_WRITE, 0xE3);
    }
}

/**
    This function  resets indices 0xAC -> 0xB0 of the CRT Controller.

    @param UINT16 IoBase - IO Base address of the video card.

    @retval VOID
**/

VOID
ResetReg (
    UINT16  IoBase )
{
    UINT8 Index;
    
    for (Index=0xAC; Index<=0xB0; Index++) {
        SetIndexReg(IoBase + CRTC_PORT, Index, 0x00);
    }

    ReadSetIndexReg(IoBase + CRTC_PORT, 0xBB, 0x0F, 0x00);
}

/**
    This function programs the standard VGA registers provided through the
    pStdTableEntry table in pVgaModeInfo.

    @param UINT16 IoBase - IO Base address of the video card.
    @param PVBIOS_MODE_INFO pVgaModeInfo - Pointer to the VgaModeInfo to be programmed.

    @retval VOID
**/

VOID
SetStdReg (
    UINT16            IoBase,
    PVBIOS_MODE_INFO  pVgaModeInfo )
{
    PVBIOS_STDTABLE_STRUCT pStdModePtr;
    UINT32 Index;
    UINT8 Reg;

    pStdModePtr = pVgaModeInfo->pStdTableEntry;

    /* Set Misc */
    Reg = pStdModePtr->Misc;
    SetReg(IoBase + MISC_PORT_WRITE, Reg);

    /* Set Seq */
    SetIndexReg(IoBase + SEQ_PORT, 0x00, 0x03);
    for (Index=0; Index<4; Index++) {
        Reg = pStdModePtr->Seq[Index];
        if (!Index) 
            Reg |= 0x20;    /* display off */
        SetIndexReg(IoBase + SEQ_PORT,(UINT8) (Index+1), Reg);
    }

    /* Set CRTC */
    ReadSetIndexReg(IoBase + CRTC_PORT, 0x11, 0x7F, 0x00);
    for (Index=0; Index<25; Index++) {
        Reg = pStdModePtr->Crtc[Index];
        SetIndexReg(IoBase + CRTC_PORT,(UINT8) Index, Reg);
    }

    /* Set AR */
    Reg = GetReg(IoBase + INPUT_STATUS1_READ);
    for (Index=0; Index<20; Index++) {
        Reg = pStdModePtr->Ar[Index];
        SetReg(IoBase + AR_PORT_WRITE, (UINT8) Index);
        SetReg(IoBase + AR_PORT_WRITE, Reg);
    }

    SetReg(IoBase + AR_PORT_WRITE, 0x14);
    SetReg(IoBase + AR_PORT_WRITE, 0x00);

    Reg = GetReg(IoBase + INPUT_STATUS1_READ);
    SetReg(IoBase + AR_PORT_WRITE, 0x20);    /* set POS */

    /* Set GR */
    for (Index=0; Index<9; Index++) {
        Reg = pStdModePtr->Gr[Index];        
        SetIndexReg(IoBase + GR_PORT, (UINT8)Index, Reg);
    }
}

/**
    This function programs the CRT Controller registers provided through the
    pStdTableEntry table in pVgaModeInfo.

    @param UINT16 IoBase - IO Base address of the video card.
    @param PVBIOS_MODE_INFO pVgaModeInfo - Pointer to the VgaModeInfo to be programmed.

    @retval VOID
**/

VOID
SetCrtcReg (
    UINT16            IoBase,
    PVBIOS_MODE_INFO  pVgaModeInfo )
{
    PVBIOS_ENHTABLE_STRUCT pEnhModePtr;
    UINT32 HBorder = 0;
    UINT32 VBorder = 0;
    UINT16 Temp;    
    UINT8 Reg05 = 0;
    UINT8 Reg07 = 0;
    UINT8 Reg09 = 0;
    UINT8 RegAC = 0;
    UINT8 RegAD = 0;
    UINT8 RegAE = 0;

    pEnhModePtr = pVgaModeInfo->pEnhTableEntry;
    if (pEnhModePtr->Flags & HBorderFlag) 
        HBorder = 8;
    if (pEnhModePtr->Flags & VBorderFlag) 
        VBorder = 8;
    
    /* unlock CRTC */
    ReadSetIndexReg(IoBase + CRTC_PORT,0x11, 0x7F, 0x00);
    
    /* Horizontal Timing Programming */
    Temp = (UINT16)((pEnhModePtr->Ht >> 3) - 5);
    if (Temp & 0x100) RegAC |= 0x01;            /* HT D[8] */
    ReadSetIndexReg(IoBase + CRTC_PORT,0x00, 0x00, (UINT8) Temp);
    Temp = (UINT16)((pEnhModePtr->Hde >> 3) - 1);
    if (Temp & 0x100) RegAC |= 0x04;            /* HDE D[8] */
    ReadSetIndexReg(IoBase + CRTC_PORT,0x01, 0x00, (UINT8) Temp);
    Temp = (UINT16)(((pEnhModePtr->Hde + HBorder) >> 3) - 1);
    if (Temp & 0x100) RegAC |= 0x10;            /* HBS D[8] */
    ReadSetIndexReg(IoBase + CRTC_PORT,0x02, 0x00, (UINT8) Temp);
    Temp = (((pEnhModePtr->Ht - HBorder) >> 3) - 1) & 0x7F;
    if (Temp & 0x20) Reg05 |= 0x80;            /* HBE D[5] */
    if (Temp & 0x40) RegAD |= 0x01;            /* HBE D[6] */    
    ReadSetIndexReg(IoBase + CRTC_PORT,0x03, 0xE0, (UINT8) (Temp & 0x1F));
    Temp = (UINT16)((pEnhModePtr->Hde + pEnhModePtr->Hfp + HBorder - 1) >> 3 );
    if (Temp & 0x100) RegAC |= 0x40;            /* HRS D[5] */    
    ReadSetIndexReg(IoBase + CRTC_PORT,0x04, 0x00, (UINT8) (Temp)); 
    Temp = ((pEnhModePtr->Hde + pEnhModePtr->Hfp + pEnhModePtr->Hsync + HBorder - 1) >> 3 ) & 0x3F;
    if (Temp & 0x20) RegAD |= 0x04;            /* HRE D[5] */    
    ReadSetIndexReg(IoBase + CRTC_PORT,0x05, 0x60, (UINT8) ((Temp & 0x1F) | Reg05));
 
    ReadSetIndexReg(IoBase + CRTC_PORT,0xAC, 0x00, (UINT8) RegAC);
    ReadSetIndexReg(IoBase + CRTC_PORT,0xAD, 0x00, (UINT8) RegAD); 
    
    /* Vetical Timing Programming */
    Temp = (UINT16)((pEnhModePtr->Vt) - 2);
    if (Temp & 0x100) Reg07 |= 0x01;            /* VT D[8] */
    if (Temp & 0x200) Reg07 |= 0x20;            
    if (Temp & 0x400) RegAE |= 0x01;            /* VT D[10] */    
    ReadSetIndexReg(IoBase + CRTC_PORT,0x06, 0x00, (UINT8) Temp);  
    Temp = (UINT16)(pEnhModePtr->Vde + pEnhModePtr->Vfp + VBorder - 1);
    if (Temp & 0x100) Reg07 |= 0x04;            /* VRS D[8] */
    if (Temp & 0x200) Reg07 |= 0x80;            /* VRS D[9] */
    if (Temp & 0x400) RegAE |= 0x08;            /* VRS D[10] */    
    ReadSetIndexReg(IoBase + CRTC_PORT,0x10, 0x00, (UINT8) Temp); 
    Temp = (pEnhModePtr->Vde + pEnhModePtr->Vfp + pEnhModePtr->Vsync + VBorder - 1) & 0x3F;
    if (Temp & 0x10) RegAE |= 0x20;            /* VRE D[4] */
    if (Temp & 0x20) RegAE |= 0x40;            /* VRE D[5] */
    ReadSetIndexReg(IoBase + CRTC_PORT,0x11, 0x70, (UINT8) (Temp & 0x0F)); 
    Temp = (UINT16)(pEnhModePtr->Vde - 1);
    if (Temp & 0x100) Reg07 |= 0x02;            /* VDE D[8] */
    if (Temp & 0x200) Reg07 |= 0x40;            /* VDE D[9] */
    if (Temp & 0x400) RegAE |= 0x02;            /* VDE D[10] */  
    ReadSetIndexReg(IoBase + CRTC_PORT,0x12, 0x00, (UINT8) Temp); 
    Temp = (UINT16)(pEnhModePtr->Vde + VBorder - 1);
    if (Temp & 0x100) Reg07 |= 0x08;            /* VBS D[8] */
    if (Temp & 0x200) Reg09 |= 0x20;            /* VBS D[9] */
    if (Temp & 0x400) RegAE |= 0x04;            /* VBS D[10] */  
    ReadSetIndexReg(IoBase + CRTC_PORT,0x15, 0x00, (UINT8) Temp);     
    Temp = (UINT16)(pEnhModePtr->Vt - VBorder - 1);
    if (Temp & 0x100) RegAE |= 0x10;            /* VBE D[8] */
    ReadSetIndexReg(IoBase + CRTC_PORT,0x16, 0x00, (UINT8) Temp);

    ReadSetIndexReg(IoBase + CRTC_PORT,0x07, 0x00, (UINT8) Reg07);
    ReadSetIndexReg(IoBase + CRTC_PORT,0x09, 0xDF, (UINT8) Reg09);
    ReadSetIndexReg(IoBase + CRTC_PORT,0xAE, 0x00, (UINT8) (RegAE | 0x80));    /* disable line compare */
    
    /* lock CRTC */
    ReadSetIndexReg(CRTC_PORT,0x11, 0x7F, 0x80);                                    
                       
}

/**
    This function programs the Offset register in the CRT controller based on
    the provided data in pVgaModeInfo.

    @param UINT16 IoBase - IO Base address of the video card.
    @param PVBIOS_MODE_INFO pVgaModeInfo - Pointer to the VgaModeInfo to be programmed.

    @retval VOID
**/

VOID
SetOffsetReg (
    UINT16            IoBase,
    PVBIOS_MODE_INFO  pVgaModeInfo )
{
    UINT16 Offset;

    Offset = (UINT16)((pVgaModeInfo->Xres * ((pVgaModeInfo->Bpp + 7)/8)) >> 3);    /* Unit: char */

    SetIndexReg(IoBase + CRTC_PORT, 0x13, (UINT8) (Offset & 0xFF));
    SetIndexReg(IoBase + CRTC_PORT, 0xB0, (UINT8) ((Offset >> 8) & 0x3F));
}

/**
    This function programs the DCLK based on the provided data in pVgaModeInfo.

    @param UINT16 IoBase - IO Base address of the video card.
    @param UINT8 Revision - Revision number of the Aspeed device.
    @param PVBIOS_MODE_INFO pVgaModeInfo - Pointer to the VgaModeInfo to be programmed.

    @retval VOID
**/

VOID
SetDclkReg (
    UINT16            IoBase,
    UINT8             Revision,
    PVBIOS_MODE_INFO  pVgaModeInfo )
{
    PVBIOS_ENHTABLE_STRUCT pEnhModePtr;
    PVBIOS_DCLK_INFO pDclkPtr;

    /* Formal Setting */
    pEnhModePtr = pVgaModeInfo->pEnhTableEntry;
    if (Revision == 0x40)    //ast2500a0
        pDclkPtr = &DCLKTable_AST2500[pEnhModePtr->DclkIndex];    
    else
        pDclkPtr = &DCLKTable[pEnhModePtr->DclkIndex];

    ReadSetIndexReg(IoBase + CRTC_PORT,0xC0, 0x00,  pDclkPtr->Param1); 
    ReadSetIndexReg(IoBase + CRTC_PORT,0xC1, 0x00,  pDclkPtr->Param2);
    if (Revision == 0x40)    //ast2500a0
        ReadSetIndexReg(IoBase + CRTC_PORT,0xBB, 0x0F,  (pDclkPtr->Param3 & 0xF0));
    else                
        ReadSetIndexReg(IoBase + CRTC_PORT,0xBB, 0x0F,  ((pDclkPtr->Param3 & 0xC0) | ((pDclkPtr->Param3 & 0x03) << 4)) );
}

/**
    This function sets the active bank to the provided value.

    @param UINT16 IoBase - IO Base address of the video card.
    @param UINT32 Bank - Bank to be set active.

    @retval VOID
**/

VOID
SetBank (
    UINT16  IoBase,
    UINT32  Bank )
{
    UINT8 Data;

    /* modify reg */
    Data  = (UINT8)((Bank >> 8) & 0xF);
    Data |= (Data << 4);
    SetIndexReg(IoBase + CRTC_PORT, 0xA5, Data);

    Data  = (UINT8)((Bank >> 4) & 0xF);
    Data |= (Data << 4);
    SetReg(IoBase + BANK1, Data);

    Data  = (UINT8)((Bank) & 0xF);
    Data |= (Data << 4);
    SetReg(IoBase + BANK0, Data);

    /* modify private data */
//    DevInfo->WriteBank = DevInfo->ReadBank = bank;
}

/**
    This function clears the frame buffer starting at 0xA0000.

    @param UINT16 IoBase - IO Base address of the video card.
    @param PVBIOS_MODE_INFO pVgaModeInfo - Pointer to the VgaModeInfo to be programmed.
    @param EFI_PEI_SERVICES **PeiServices - Double pointer to PEI services.

    @retval VOID
**/

VOID
ClearBuffer (
    UINT16            IoBase,
    PVBIOS_MODE_INFO  pVgaModeInfo,
    EFI_PEI_SERVICES  **PeiServices )
{
    UINT32 Size;
    UINT32 Bank;
    UINT32 Index;
    
    Size = pVgaModeInfo->Xres * ((pVgaModeInfo->Bpp+7)/8) * pVgaModeInfo->Yres;
    Bank = (Size-1) >> 16;
    for (Index=0; Index<=Bank; Index++) {
        SetBank(IoBase, Index);
        (*PeiServices)->SetMem((UINT8*)0xA0000, 0x10000, 0);
    }
}    

/**
    This function programs the external registers of the CRT Controller based
    on the provided pVgaModeInfo data.

    @param UINT16 IoBase - IO Base address of the video card.
    @param PVBIOS_MODE_INFO pVgaModeInfo - Pointer to the VgaModeInfo to be programmed.
    @param UINT8 Revision - Aspeed device's revision number.

    @retval VOID
**/

VOID
SetExtReg (
    UINT16            IoBase,
    PVBIOS_MODE_INFO  pVgaModeInfo,
    UINT32            OutputOption,
    UINT8             Revision )
{
    UINT32 ModeId;
    UINT32 ColorIndex = 0;
    UINT32 RefreshRateIndex = 0;
    UINT8 RegA0 = 0;
    UINT8 RegA3 = 0;
    UINT8 RegA8 = 0;

    /* Mode Type Setting */    
    switch (pVgaModeInfo->Bpp) {
    case 1:
        ColorIndex = 0;

        RegA0 = 0x00;
        RegA3 = 0x00;
        RegA8 = 0x00;
        break;

    case 8:
        ColorIndex = VgaModeIndex-1;

        RegA0 = 0x70;
        RegA3 = 0x01;
        RegA8 = 0x00;
        break;

    case 15:
        ColorIndex = HicModeIndex - 1;
        RegA0 = 0x70;
        RegA3 = 0x04;
        RegA8 = 0x00;
        break;

    case 16:
        ColorIndex = HicModeIndex;

        RegA0 = 0x70; 
        RegA3 = 0x04;
        RegA8 = 0x00;
        break;

    case 32:
        ColorIndex = TruecModeIndex;

        RegA0 = 0x70; 
        RegA3 = 0x08;
        RegA8 = 0x00;
        break;
    }
    if (OutputOption & Init_DVO)
        RegA3 |= 0x80;
    ReadSetIndexReg(IoBase + CRTC_PORT, 0xA0, 0x8F, (UINT8) RegA0);
    ReadSetIndexReg(IoBase + CRTC_PORT, 0xA3, 0x00, (UINT8) RegA3);
    ReadSetIndexReg(IoBase + CRTC_PORT, 0xA8, 0xFD, (UINT8) RegA8);

    /* Set Threshold */
    if (Revision >= 0x20) {
        SetIndexReg(IoBase + CRTC_PORT, 0xA7, 0x78);
        SetIndexReg(IoBase + CRTC_PORT, 0xA6, 0x60);
    }    
    else {
        SetIndexReg(IoBase + CRTC_PORT, 0xA7, 0x3F);
        SetIndexReg(IoBase + CRTC_PORT, 0xA6, 0x2F);
    }        

    /* Write mode info to scratch */
    if (pVgaModeInfo->Bpp > 1)
    {
        RefreshRateIndex = pVgaModeInfo->pEnhTableEntry->RefreshRateIndex;
        ModeId = pVgaModeInfo->pEnhTableEntry->ModeID;

        SetIndexReg(IoBase + CRTC_PORT, 0x8C, (UINT8) ((ColorIndex & 0x0F) << 4));
        SetIndexReg(IoBase + CRTC_PORT, 0x8D, (UINT8) (RefreshRateIndex & 0xFF));
        SetIndexReg(IoBase + CRTC_PORT, 0x8E, (UINT8) (ModeId & 0xFF));
    }
 
    /* reset bank */
    SetBank(IoBase, 0);
}

/**
    This function syncs the VGA register based on the pVgaModeInfo data.

    @param UINT16 IoBase - IO Base address of the video card.
    @param PVBIOS_MODE_INFO pVgaModeInfo - Pointer to the VgaModeInfo to be programmed.

    @retval VOID
**/
VOID
SetSyncReg (
    UINT16              IoBase, 
    PVBIOS_MODE_INFO    pVgaModeInfo
)
{
    PVBIOS_ENHTABLE_STRUCT pEnhModePtr = pVgaModeInfo->pEnhTableEntry;
    UINT8 Reg;

    Reg = (UINT8) (pEnhModePtr->Flags & SyncNN) | 0x2F;
    SetReg(IoBase + MISC_PORT_WRITE, Reg);
}

/**
    This function programs the VGA DAC registers with the appropriate palette
    based on the color bit depth in pVgaModeInfo.

    @param UINT16 IoBase - IO Base address of the video card.
    @param PVBIOS_MODE_INFO pVgaModeInfo - Pointer to the VgaModeInfo to be programmed.

    @retval UINT16 - 0 if successful, 1 if unsupported.
**/

UINT16
SetDacReg (
    UINT16            IoBase,
    PVBIOS_MODE_INFO  pVgaModeInfo )
{
    PVBIOS_DAC_INFO pDacPtr;
    UINT32 Index;
    UINT32 DacNumber;
    UINT8 Dacr;
    UINT8 Dacg;
    UINT8 Dacb;

    switch (pVgaModeInfo->Bpp) {
        case 1: /* text */
            DacNumber = DAC_NUM_TEXT;
            pDacPtr = (PVBIOS_DAC_INFO) &DAC_TEXT[0];
            break;
        case 8:
            DacNumber = DAC_NUM_VGA;
            pDacPtr = (PVBIOS_DAC_INFO) &DAC_VGA[0];
            break;
        default:
            return 1;
    }

    for (Index=0; Index<DacNumber; Index++) {
        Dacr = pDacPtr->Dacr;
        Dacg = pDacPtr->Dacg;
        Dacb = pDacPtr->Dacb;

        VGA_LOAD_PALETTE_INDEX (IoBase, Index, Dacr, Dacg, Dacb);
        
        pDacPtr++;
    }

    return 0;
}

/**
    This function opens the character map.

    @param UINT16 IoBase - IO Base address of the video card.

    @retval VOID
**/

VOID
OpenCharMap (
    UINT16  IoBase )
{
    SetIndexReg(IoBase + SEQ_PORT, 0x02, 0x04);
    SetIndexReg(IoBase + SEQ_PORT, 0x04, 0x07);
    SetIndexReg(IoBase + GR_PORT, 0x04, 0x02);
    SetIndexReg(IoBase + GR_PORT, 0x05, 0x00);
    SetIndexReg(IoBase + GR_PORT, 0x06, 0x04);
}

/**
    This function closes the character map.

    @param UINT16 IoBase - IO Base address of the video card.

    @retval VOID
**/

VOID
CloseCharMap (
    UINT16  IoBase )
{
    SetIndexReg(IoBase + SEQ_PORT, 0x02, 0x03);
    SetIndexReg(IoBase + SEQ_PORT, 0x04, 0x03);
    SetIndexReg(IoBase + GR_PORT, 0x04, 0x00);
    SetIndexReg(IoBase + GR_PORT, 0x05, 0x10);
    SetIndexReg(IoBase + GR_PORT, 0x06, 0x0E);
}

/**
    This function loads the text font in FONT8X16[].

    @param UINT16 IoBase - IO Base address of the video card.

    @retval VOID
**/

VOID
LoadTextFont (
    UINT16  IoBase )
{
    UINT8 *WritePtr;
    UINT8 *FontPtr;
    UINT32 Index;
    UINT32 Index2;

    OpenCharMap(IoBase);

    // Load font
    WritePtr = (UINT8*) 0xA0000;
    for (Index=0; Index<0x100; Index++) {
        FontPtr = (UINT8*) &FONT8X16[Index];
        for (Index2=0; Index2<0x10; Index2++) {
            *(UINT8 *)(WritePtr + Index2) = *(UINT8 *)(FontPtr++);
        }
        WritePtr += 0x20;
    }

    CloseCharMap(IoBase);
}

/**
    This function clears the text buffer.

    @param EFI_PEI_SERVICES **PeiServices - Double pointer to PEI services.
    @param PVBIOS_MODE_INFO pVgaModeInfo - Pointer to the VgaModeInfo to be programmed.

    @retval VOID
**/

VOID
ClearTextBuffer (
    EFI_PEI_SERVICES  **PeiServices,
    PVBIOS_MODE_INFO  pVgaModeInfo )
{
    UINT8 *WritePtr = (UINT8*)(0xB0000 + 0x8000);
    UINT32 Size;

    Size = 4000;
    (*PeiServices)->SetMem(WritePtr, Size, 0);
}    

/**
    This function programs the VGA DAC registers with the appropriate palette
    based on the color bit depth in pVgaModeInfo.

    @param UINT32 MmioBase32 - IO Base address of the video card.
    @param EFI_PEI_SERVICES **PeiServices - Double pointer to PEI services.

    @retval VOID.
**/

VOID
SetMpll (
    UINT32            MmioBase32,
    EFI_PEI_SERVICES  **PeiServices )
{
    UINT8 *MmioBase = (UINT8*)(UINTN)MmioBase32;
    UINT32 Addr;
    UINT32 Data;
    UINT32 Param;

    // Reset MMC
    MOutdwm(MmioBase, 0x1E6E0000,0xFC600309);
    MOutdwm(MmioBase, 0x1E6E0034,0x00020080);
    for(Addr = 0x1e6e0004; Addr < 0x1e6e0090; Addr += 4) {
        MOutdwm(MmioBase, Addr, 0x0);
    }

    MOutdwm(MmioBase, 0x1E6E0034,0x00020000);

    MOutdwm(MmioBase, 0x1E6E2000, 0x1688A8A8);
    Data = MIndwm(MmioBase, 0x1E6E2070) & 0x00800000;
    if(Data) {  // CLKIN = 25MHz
        Param = 0x930023E0;
        MOutdwm(MmioBase, 0x1E6E2160, 0x00011320); // add at V1.1    
    }
    else {      // CLKIN = 24MHz
        Param = 0x93002400;
    }

    MOutdwm(MmioBase, 0x1E6E2020, Param);
    uDelay(PeiServices, 100); // delay 3 ms
}

VOID
DoDdrPhyInit (
    UINT32            MmioBase32,
    EFI_PEI_SERVICES  **PeiServices )
{
    UINT8 *MmioBase = (UINT8*)(UINTN)MmioBase32;
    UINT32 Data;
    UINT32 Pass;
    UINT32 TimeCnt;

    Pass = 0;
    MOutdwm(MmioBase, 0x1E6E0060,0x00000005);
    while(!Pass) {
        for(TimeCnt = 0; TimeCnt < TIMEOUT; TimeCnt++) {
            Data = MIndwm(MmioBase, 0x1E6E0060) & 0x1;
            if(!Data){
                break;
            }
        }
        if(TimeCnt != TIMEOUT) {
            Data = MIndwm(MmioBase, 0x1E6E0300) & 0x000A0000;
            if(!Data) {
                Pass = 1;
            }
        }
        if(!Pass){
            MOutdwm(MmioBase, 0x1E6E0060,0x00000000);
            uDelay(PeiServices, 10); // delay 10 us
            MOutdwm(MmioBase, 0x1E6E0060,0x00000005);
        }
    } // while(!Pass)

    MOutdwm(MmioBase, 0x1E6E0060,0x00000006);
}

UINT32
MmcTestBurstAst2500 (
    UINT32  MmioBase32,
    UINT32  DataGen )
{
    UINT8 *MmioBase = (UINT8*)(UINTN)MmioBase32;
    UINT32 Data;
    UINT32 TimeCnt;

    MOutdwm(MmioBase, 0x1E6E0070, 0x00000000);
    MOutdwm(MmioBase, 0x1E6E0070, 0x000000C1 | (DataGen << 3));
    TimeCnt = 0;
    do{
        Data = MIndwm(MmioBase, 0x1E6E0070) & 0x3000;
        if(Data & 0x2000) {
            return(0);
        }
        if(++TimeCnt > TIMEOUT) {
            // Timeout!
            MOutdwm(MmioBase, 0x1E6E0070, 0x00000000);
            return(0);
        }
    } while(!Data);

    MOutdwm(MmioBase, 0x1E6E0070, 0x00000000);
    return(1);
}

UINT32
MmcTestSingleAst2500 (
    UINT32  MmioBase32,
    UINT32  DataGen )
{
    UINT8 *MmioBase = (UINT8*)(UINTN)MmioBase32;
    UINT32 Data;
    UINT32 TimeCnt;

    MOutdwm(MmioBase, 0x1E6E0070, 0x00000000);
    MOutdwm(MmioBase, 0x1E6E0070, 0x00000085 | (DataGen << 3));
    TimeCnt = 0;
    do {
        Data = MIndwm(MmioBase, 0x1E6E0070) & 0x3000;
        if(Data & 0x2000) {
            return(0);
        }
        if(++TimeCnt > TIMEOUT){
            // Timeout!
            MOutdwm(MmioBase, 0x1E6E0070, 0x00000000);
            return(0);
        }
    } while(!Data);

    MOutdwm(MmioBase, 0x1E6E0070, 0x00000000);
    return(1);
}

UINT32
CbrTestAst2500 (
    UINT32  MmioBase32 )
{
    UINT8 *MmioBase = (UINT8*)(UINTN)MmioBase32;

    MOutdwm(MmioBase, 0x1E6E0074, 0x0000FFFF);
    MOutdwm(MmioBase, 0x1E6E007C, 0xFF00FF00);
    if(!MmcTestBurstAst2500(MmioBase32, 0))
        return(0);
    if(!MmcTestSingleAst2500(MmioBase32, 0))
        return(0);
    return(1);
}

VOID
CheckDramSize (
    UINT32  MmioBase32,
    UINT32  tRfc )
{
    UINT8 *MmioBase = (UINT8*)(UINTN)MmioBase32;
    UINT32 Reg_04;
    UINT32 Reg_14;

    Reg_04 = MIndwm(MmioBase, 0x1E6E0004) & 0xfffffffc;
    Reg_14 = MIndwm(MmioBase, 0x1E6E0014) & 0xffffff00;

    MOutdwm(MmioBase, 0xA0100000, 0x41424344);
    MOutdwm(MmioBase, 0x90100000, 0x35363738);
    MOutdwm(MmioBase, 0x88100000, 0x292A2B2C);
    MOutdwm(MmioBase, 0x80100000, 0x1D1E1F10);

    // Check 8Gbit
    if(MIndwm(MmioBase, 0xA0100000) == 0x41424344) {
        Reg_04 |= 0x03;
        Reg_14 |= (tRfc >> 24) & 0xFF;
    } 
    // Check 4Gbit
    else if(MIndwm(MmioBase, 0x90100000) == 0x35363738) {
        Reg_04 |= 0x02;
        Reg_14 |= (tRfc >> 16) & 0xFF;
    }
    // Check 2Gbit
    else if(MIndwm(MmioBase, 0x88100000) == 0x292A2B2C) {
        Reg_04 |= 0x01;
        Reg_14 |= (tRfc >> 8) & 0xFF;
    }
    // 1Gbit
    else {
        Reg_14 |= tRfc & 0xFF;
    }

    MOutdwm(MmioBase, 0x1E6E0004, Reg_04);
    MOutdwm(MmioBase, 0x1E6E0014, Reg_14);
}

VOID
EnableCache (
    UINT32  MmioBase32 )
{
    UINT8 *MmioBase = (UINT8*)(UINTN)MmioBase32;
    UINT32 Reg_04;
    UINT32 Data;

    Reg_04 = MIndwm(MmioBase, 0x1E6E0004);
    MOutdwm(MmioBase, 0x1E6E0004, Reg_04 | 0x1000);

    do {
        Data = MIndwm(MmioBase, 0x1E6E0004);
    } while(!(Data & 0x80000));
    MOutdwm(MmioBase, 0x1E6E0004, Reg_04 | 0x400);
}

VOID
DdrInitCommon (
    UINT32  MmioBase32 )
{
    UINT8 *MmioBase = (UINT8*)(UINTN)MmioBase32;

    MOutdwm(MmioBase, 0x1E6E0034,0x00020080);
    MOutdwm(MmioBase, 0x1E6E0008,0x2003000F);
    MOutdwm(MmioBase, 0x1E6E0038,0x00000FFF);
    MOutdwm(MmioBase, 0x1E6E0040,0x88448844);
    MOutdwm(MmioBase, 0x1E6E0044,0x24422288);
    MOutdwm(MmioBase, 0x1E6E0048,0x22222222);
    MOutdwm(MmioBase, 0x1E6E004C,0x22222222);
    MOutdwm(MmioBase, 0x1E6E0050,0x80000000);
    MOutdwm(MmioBase, 0x1E6E0208,0x00000000);
    MOutdwm(MmioBase, 0x1E6E0218,0x00000000);
    MOutdwm(MmioBase, 0x1E6E0220,0x00000000);
    MOutdwm(MmioBase, 0x1E6E0228,0x00000000);
    MOutdwm(MmioBase, 0x1E6E0230,0x00000000);
    MOutdwm(MmioBase, 0x1E6E02A8,0x00000000);
    MOutdwm(MmioBase, 0x1E6E02B0,0x00000000);
    MOutdwm(MmioBase, 0x1E6E0240,0x86000000);
    MOutdwm(MmioBase, 0x1E6E0244,0x00008600);
    MOutdwm(MmioBase, 0x1E6E0248,0x80000000);
    MOutdwm(MmioBase, 0x1E6E024C,0x80808080);
}

VOID
Ddr4InitAst2500 (
    UINT32            MmioBase32,
    UINT32            *DdrTable,
    EFI_PEI_SERVICES  **PeiServices )
{
    UINT8 *MmioBase = (UINT8*)(UINTN)MmioBase32;
    UINT32 Data;
    UINT32 Data2;
    UINT32 Pass;
    UINT32 RetryCnt;
    UINT32 Ddr_Vref;
    UINT32 Phy_Vref;
    UINT32 Min_Ddr_Vref = 0;
    UINT32 Min_Phy_Vref = 0;
    UINT32 Max_Ddr_Vref = 0;
    UINT32 Max_Phy_Vref = 0;

    MOutdwm(MmioBase, 0x1E6E0004,0x00000313);
    MOutdwm(MmioBase, 0x1E6E0010,DdrTable[REGIDX_010]);
    MOutdwm(MmioBase, 0x1E6E0014,DdrTable[REGIDX_014]);
    MOutdwm(MmioBase, 0x1E6E0018,DdrTable[REGIDX_018]);
    MOutdwm(MmioBase, 0x1E6E0020,DdrTable[REGIDX_020]);          // MODEREG4/6
    MOutdwm(MmioBase, 0x1E6E0024,DdrTable[REGIDX_024]);          // MODEREG5
    MOutdwm(MmioBase, 0x1E6E002C,DdrTable[REGIDX_02C] | 0x100);  // MODEREG0/2
    MOutdwm(MmioBase, 0x1E6E0030,DdrTable[REGIDX_030]);          // MODEREG1/3

    // DDR PHY Setting
    MOutdwm(MmioBase, 0x1E6E0200,0x42492AAE);
    MOutdwm(MmioBase, 0x1E6E0204,0x09002800);
    MOutdwm(MmioBase, 0x1E6E020C,0x55E00B0B);
    MOutdwm(MmioBase, 0x1E6E0210,0x20000000);
    MOutdwm(MmioBase, 0x1E6E0214,DdrTable[REGIDX_214]);
    MOutdwm(MmioBase, 0x1E6E02E0,DdrTable[REGIDX_2E0]);
    MOutdwm(MmioBase, 0x1E6E02E4,DdrTable[REGIDX_2E4]);
    MOutdwm(MmioBase, 0x1E6E02E8,DdrTable[REGIDX_2E8]);
    MOutdwm(MmioBase, 0x1E6E02EC,DdrTable[REGIDX_2EC]);
    MOutdwm(MmioBase, 0x1E6E02F0,DdrTable[REGIDX_2F0]);
    MOutdwm(MmioBase, 0x1E6E02F4,DdrTable[REGIDX_2F4]);
    MOutdwm(MmioBase, 0x1E6E02F8,DdrTable[REGIDX_2F8]);
    MOutdwm(MmioBase, 0x1E6E0290,0x00100008);
    MOutdwm(MmioBase, 0x1E6E02C4,0x3C183C3C);
    MOutdwm(MmioBase, 0x1E6E02C8,0x00631E0E);

    // Controller Setting
    MOutdwm(MmioBase, 0x1E6E0034,0x0001A991);

    // Train PHY Vref first
    Pass = 0;                                                   // add at V1.1
    for(RetryCnt = 0; RetryCnt < 4 && Pass == 0; RetryCnt++) {  // add at V1.1
        Max_Phy_Vref = 0x0;
        Pass = 0;
        MOutdwm(MmioBase, 0x1E6E02C0,0x00001C06);
        for(Phy_Vref = 0x40; Phy_Vref < 0x80; Phy_Vref++) {
            MOutdwm(MmioBase, 0x1E6E000C,0x00000000);
            MOutdwm(MmioBase, 0x1E6E0060,0x00000000);
            MOutdwm(MmioBase, 0x1E6E02CC,Phy_Vref | (Phy_Vref << 8));
            // Fire DFI Init
            DoDdrPhyInit(MmioBase32, PeiServices);
            MOutdwm(MmioBase, 0x1E6E000C,0x00005C01);
            if(CbrTestAst2500(MmioBase32)) {
                Pass++;
                Data = MIndwm(MmioBase, 0x1E6E03D0);
                Data2 = Data >> 8;
                Data  = Data & 0xff;
                if(Data > Data2) {
                    Data = Data2;
                }

                if(Max_Phy_Vref < Data) {
                    Max_Phy_Vref = Data;
                    Min_Phy_Vref = Phy_Vref;
                }
            }
            else if(Pass > 0) {
                break;
            }
        }
    }  // add at V1.1
    // Set Target PHY Vref
    MOutdwm(MmioBase, 0x1E6E02CC,Min_Phy_Vref | (Min_Phy_Vref << 8));

    // Train DDR Vref next
    Pass = 0;                                                   // add at V1.1
    for(RetryCnt = 0; RetryCnt < 4 && Pass == 0; RetryCnt++) {  // add at V1.1
        Min_Ddr_Vref = 0xFF;
        Max_Ddr_Vref = 0x0;
        Pass = 0;
        for(Ddr_Vref = 0x00; Ddr_Vref < 0x40; Ddr_Vref++) {
            MOutdwm(MmioBase, 0x1E6E000C,0x00000000);
            MOutdwm(MmioBase, 0x1E6E0060,0x00000000);
            MOutdwm(MmioBase, 0x1E6E02C0,0x00000006 | (Ddr_Vref << 8));
            // Fire DFI Init
            DoDdrPhyInit(MmioBase32, PeiServices);
            MOutdwm(MmioBase, 0x1E6E000C,0x00005C01);
            if(CbrTestAst2500(MmioBase32)) {
                Pass++;
                if(Min_Ddr_Vref > Ddr_Vref) {
                    Min_Ddr_Vref = Ddr_Vref;
                }
                if(Max_Ddr_Vref < Ddr_Vref){
                    Max_Ddr_Vref = Ddr_Vref;
                }
            }
            else if(Pass != 0) {
                break;
            }
        }
    }  // add at V1.1
    MOutdwm(MmioBase, 0x1E6E000C,0x00000000);
    MOutdwm(MmioBase, 0x1E6E0060,0x00000000);
    Ddr_Vref = (Min_Ddr_Vref + Max_Ddr_Vref + 1) >> 1;
    MOutdwm(MmioBase, 0x1E6E02C0,0x00000006 | (Ddr_Vref << 8));
    //printf("Target DDR Vref = %x\n",ddr_vref);

    // Wait for DdrPhyInit done
    DoDdrPhyInit(MmioBase32, PeiServices);

    MOutdwm(MmioBase, 0x1E6E0120,DdrTable[REGIDX_PLL]);
    MOutdwm(MmioBase, 0x1E6E000C,0x42AA5C81);
    MOutdwm(MmioBase, 0x1E6E0034,0x0001AF93);

    CheckDramSize(MmioBase32, DdrTable[REGIDX_RFC]);
    EnableCache(MmioBase32);
    MOutdwm(MmioBase, 0x1E6E001C,0x00000008);
    MOutdwm(MmioBase, 0x1E6E0038,0xFFFFFF00);
}

VOID Ddr3InitAst2500(UINT32 MmioBase32, UINT32 *DdrTable, EFI_PEI_SERVICES **PeiServices)
{
    UINT8 *MmioBase = (UINT8*)(UINTN)MmioBase32;

    MOutdwm(MmioBase, 0x1E6E0004,0x00000303);
    MOutdwm(MmioBase, 0x1E6E0010,DdrTable[REGIDX_010]);
    MOutdwm(MmioBase, 0x1E6E0014,DdrTable[REGIDX_014]);
    MOutdwm(MmioBase, 0x1E6E0018,DdrTable[REGIDX_018]);
    MOutdwm(MmioBase, 0x1E6E0020,DdrTable[REGIDX_020]);          // MODEREG4/6
    MOutdwm(MmioBase, 0x1E6E0024,DdrTable[REGIDX_024]);          // MODEREG5
    MOutdwm(MmioBase, 0x1E6E002C,DdrTable[REGIDX_02C] | 0x100);  // MODEREG0/2
    MOutdwm(MmioBase, 0x1E6E0030,DdrTable[REGIDX_030]);          // MODEREG1/3

    // DDR PHY Setting
    MOutdwm(MmioBase, 0x1E6E0200,0x02492AAE);
    MOutdwm(MmioBase, 0x1E6E0204,0x00001001);
    MOutdwm(MmioBase, 0x1E6E020C,0x55E00B0B);
    MOutdwm(MmioBase, 0x1E6E0210,0x20000000);
    MOutdwm(MmioBase, 0x1E6E0214,DdrTable[REGIDX_214]);
    MOutdwm(MmioBase, 0x1E6E02E0,DdrTable[REGIDX_2E0]);
    MOutdwm(MmioBase, 0x1E6E02E4,DdrTable[REGIDX_2E4]);
    MOutdwm(MmioBase, 0x1E6E02E8,DdrTable[REGIDX_2E8]);
    MOutdwm(MmioBase, 0x1E6E02EC,DdrTable[REGIDX_2EC]);
    MOutdwm(MmioBase, 0x1E6E02F0,DdrTable[REGIDX_2F0]);
    MOutdwm(MmioBase, 0x1E6E02F4,DdrTable[REGIDX_2F4]);
    MOutdwm(MmioBase, 0x1E6E02F8,DdrTable[REGIDX_2F8]);
    MOutdwm(MmioBase, 0x1E6E0290,0x00100008);
    MOutdwm(MmioBase, 0x1E6E02C0,0x00000006);

    // Controller Setting
    MOutdwm(MmioBase, 0x1E6E0034,0x00020091);

    // Wait for DdrPhyInit done
    DoDdrPhyInit(MmioBase32, PeiServices);

    MOutdwm(MmioBase, 0x1E6E0120,DdrTable[REGIDX_PLL]);
    MOutdwm(MmioBase, 0x1E6E000C,0x42AA5C81);
    MOutdwm(MmioBase, 0x1E6E0034,0x0001AF93);

    CheckDramSize(MmioBase32, DdrTable[REGIDX_RFC]);
    EnableCache(MmioBase32);
    MOutdwm(MmioBase, 0x1E6E001C,0x00000008);
    MOutdwm(MmioBase, 0x1E6E0038,0xFFFFFF00);
}

UINT32
DdrTestAst2500 (
    UINT32  MmioBase32 )
{
    UINT8 *MmioBase = (UINT8*)(UINTN)MmioBase32;

    MOutdwm(MmioBase, 0x1E6E0074, 0x0000FFFF);
    MOutdwm(MmioBase, 0x1E6E007C, 0xFF00FF00);
    if(!MmcTestBurstAst2500(MmioBase32, 0)) 
        return(0);
    if(!MmcTestBurstAst2500(MmioBase32, 1)) 
        return(0);
    if(!MmcTestBurstAst2500(MmioBase32, 2)) 
        return(0);
    if(!MmcTestBurstAst2500(MmioBase32, 3)) 
        return(0);
    if(!MmcTestSingleAst2500(MmioBase32, 0)) 
        return(0);
    return(1);
}

UINT32
DramInitAst2500 (
    UINT32            MmioBase32,
    EFI_PEI_SERVICES  **PeiServices )
{
    UINT32 Data;
    UINT8 *MmioBase = (UINT8*)(UINTN)MmioBase32;

    /************************************************************************************************/
    /* Below patch should be executed at the first time before any access to AST25xx                */
    // Clear bus lock condition
    MOutdwm(MmioBase, 0x1e600000,0xAEED1A03);
    MOutdwm(MmioBase, 0x1e600084,0x00010000);
    MOutdwm(MmioBase, 0x1e600088,0x00000000);
    MOutdwm(MmioBase, 0x1e6e2000,0x1688A8A8);
    /************************************************************************************************/

START_INIT:
    SetMpll(MmioBase32, PeiServices);
    DdrInitCommon(MmioBase32);
    Data = MIndwm(MmioBase, 0x1E6E2070);
    if(Data & 0x01000000) {
        Ddr4InitAst2500(MmioBase32, ddr4_1600_timing_table, PeiServices);
    }
    else {
        Ddr3InitAst2500(MmioBase32, ddr3_1600_timing_table, PeiServices);
    }
    if(!DdrTestAst2500(MmioBase32)) {
        goto START_INIT;
    }
    MOutdwm(MmioBase, 0x1E6E2040, MIndwm(MmioBase, 0x1E6E2040) | 0x41);
    // Patch code
    Data = MIndwm(MmioBase, 0x1E6E200C) & 0xF9FFFFFF;
    MOutdwm(MmioBase, 0x1E6E200C, Data | 0x10000000);
    return(1);
}

VOID
InitAst2500DramReg (
    UINT32            MmioBase32,
    UINT16            IoBase,
    EFI_PEI_SERVICES  **PeiServices )
{
    UINT8 Reg;
    UINT32 Temp;
    UINT8 *MmioBase = (UINT8*)(UINTN)MmioBase32;

    Reg = GetIndexReg(IoBase + CRTC_PORT, 0xD0, 0xFF);
    // VGA Only
    if ((Reg & 0x80) == 0) {
        MOutdwm(MmioBase, 0x1e6e2000, 0x1688a8a8);
        do {
            Temp = MIndwm(MmioBase, 0x1e6e2000);
        } while (Temp != 0x01);

        MOutdwm(MmioBase, 0x1e6e0000, 0xfc600309);
        do {
            Temp = MIndwm(MmioBase, 0x1e6e0000);
        } while (Temp != 0x01);

        /* Slow down CPU/AHB CLK in VGA only mode */
        Temp = MIndwm(MmioBase, 0x1e6e2008);
        Temp |= 0x73;
        MOutdwm(MmioBase, 0x1e6e2008, Temp);

        DramInitAst2500(MmioBase32, PeiServices);

        Temp  = MIndwm(MmioBase, 0x1E6E2040);
        MOutdwm(MmioBase, 0x1E6E2040, 0xC0);
    }
}

VOID
InitAST2600DramReg(
    UINT16              IoBase
)
{
    UINT16 Reg;
    DEBUG((DEBUG_ERROR, "InitAST2600DramReg entry\n"));
    /* wait ready */
    do {
        Reg = GetIndexReg(IoBase + CRTC_PORT, 0xD0, 0xFF);
    } while ((Reg & 0xC0) == 0);

}

// ASTDP Support functions starts
//
// Export Procedures
//
BOOLEAN
ASTDP_Check(
    EFI_PEI_SERVICES **PeiServices,
    UINT16      IoBase,
    UINT32      Delay
)
{
    uDelay(PeiServices, Delay);
    
    // Check DP support
    if(GetIndexReg(IoBase + CRTC_PORT, 0xD1, 0x0E) == 0x0E)
        return TRUE;
    else
        return FALSE;
        
} //CheckASTDP

BOOLEAN
ASTDP_WaitReady(
    EFI_PEI_SERVICES **PeiServices,
    UINT16      IoBase,
    UINT32      Delay
)
{
    if(ASTDP_Check(PeiServices, IoBase, Delay))
    {
        // Check DP status
        if(GetIndexReg(IoBase + CRTC_PORT, 0xD1, 0x20) == 0x20)
            return TRUE;
        else
            return FALSE;
    }
    else
    {
        return FALSE;
    }
}

VOID
ASTDP_PowerOnOff(
    UINT16      IoBase,
    UINT8       Mode
)
{
    UINT8 bE3 = 0;
    
    // Check DP status
    if(GetIndexReg(IoBase + CRTC_PORT, 0xD1, 0x2E) != 0x2E)
        return;
    
    // Read and Turn off DP PHY sleep
    bE3 = (GetIndexReg2(IoBase + CRTC_PORT, 0xe3) & 0x0F);

    // Turn on DP PHY sleep
    if(!Mode)
        bE3 |= 0x10;
    
    // DP Power on / off
    SetIndexReg (IoBase + CRTC_PORT, 0xe3, bE3);
}

VOID
ASTDP_SetOnOff(
    EFI_PEI_SERVICES    **PeiServices,
    UINT16      IoBase,
    UINT8       Mode
    )
{
    // Check DP status
    if(GetIndexReg(IoBase + CRTC_PORT, 0xD1, 0x2E) != 0x2E)
        return;

    SetIndexReg(IoBase + CRTC_PORT, 0xe3, Mode);

    // If DP plug in and link successful then check video on / off status
    if((GetIndexReg(IoBase + CRTC_PORT, 0xDC, 0x01))&&
       (GetIndexReg(IoBase + CRTC_PORT, 0xDF, 0x01)))
    {
        while(GetIndexReg(IoBase + CRTC_PORT, 0xDF, 0x10)!=(Mode << 4))
        {
            uDelay(PeiServices, 1000); //1ms
        };
    }
}

VOID
ASTDP_SetOutput(
    VBIOS_MODE_INFO *pVGAModeInfo,
    UINT16      IoBase
)
{
    UINT32 RefreshRateIndex;   
    UINT8 ModeIdx;
    
    // Check DP status
    if(GetIndexReg(IoBase + CRTC_PORT, 0xD1, 0x2E) != 0x2E)
        return;

    RefreshRateIndex = pVGAModeInfo->pEnhTableEntry->RefreshRateIndex - 1;
    
    switch (pVGAModeInfo->Xres)
    {       
        case 640:
            ModeIdx = (0x00 + (UINT8) RefreshRateIndex);
            break;
        case 720:
            ModeIdx = 0x45;
            break;
        case 800:
            ModeIdx = (0x04 + (UINT8) RefreshRateIndex);
            break;
        case 1024:
            ModeIdx = (0x09 + (UINT8) RefreshRateIndex);
            break;
        case 1280:
            ModeIdx = (0x0D + (UINT8) RefreshRateIndex);
            break;
        case 1600:
            ModeIdx = 0x10;
            break;
        case 1920:
            ModeIdx = 0x15;
            break;
        default:
            return;
    }
    
    // Set DP timing
    SetIndexReg(IoBase + CRTC_PORT, 0xe0, 0x20);
    SetIndexReg(IoBase + CRTC_PORT, 0xe1, 0x00);
    SetIndexReg(IoBase + CRTC_PORT, 0xe2, ModeIdx);
    
}

VOID
ASTDP_WaitVSync(
    UINT16      IoBase
)
{
    while (!(GetReg(IoBase + INPUT_STATUS1_READ)  & 0x8));  
    while ((GetReg(IoBase + INPUT_STATUS1_READ)  & 0x8));     
    while (!(GetReg(IoBase + INPUT_STATUS1_READ)  & 0x8));     
}
// ASTDP Support functions Ends

EFI_STATUS
EFIAPI
AspeedGraphicsPpiInit (
    IN VOID  *GraphicsPolicyPtr
)
{
    // VGA initialization already done. So, return success
    return EFI_SUCCESS;
}

EFI_STATUS
EFIAPI
AspeedGraphicsPpiGetMode (
    IN OUT EFI_GRAPHICS_OUTPUT_PROTOCOL_MODE  *Mode
)
{
    ASPEED_PEI_GRAPHICS_DATA  *GraphicsData;
    
    if (Mode == NULL) {
        return EFI_INVALID_PARAMETER;
    }
    
    GraphicsData = (ASPEED_PEI_GRAPHICS_DATA *)gPeiGraphicsPpiDescriptor.Ppi;

    Mode->MaxMode = GraphicsData->Mode.MaxMode;
    Mode->Mode = GraphicsData->Mode.Mode;
    Mode->FrameBufferBase = GraphicsData->Mode.FrameBufferBase;
    Mode->FrameBufferSize = GraphicsData->Mode.FrameBufferSize;
    
    if (Mode->Info != NULL) {
        Mode->Info->Version = GraphicsData->Info.Version;
        Mode->Info->HorizontalResolution = GraphicsData->Info.HorizontalResolution;
        Mode->Info->VerticalResolution = GraphicsData->Info.VerticalResolution;
        Mode->Info->PixelFormat = GraphicsData->Info.PixelFormat;
        Mode->Info->PixelInformation = GraphicsData->Info.PixelInformation;
        Mode->Info->PixelsPerScanLine = GraphicsData->Info.PixelsPerScanLine;
        
        Mode->SizeOfInfo = GraphicsData->Mode.SizeOfInfo;
    } else {
        Mode->SizeOfInfo = 0;
    }
    
    return EFI_SUCCESS;
}

EFI_STATUS
InstallPeiGraphicsPpi (
    IN OUT VIDEO_PARAMETERS             *Parameters,
    IN     VBIOS_MODE_INFO              *VgaModeInfo,
    IN     UINT8                        vBus 
)
{
    EFI_STATUS                          Status;
    ASPEED_PEI_GRAPHICS_DATA            *GraphicsData;
    EFI_PEI_GRAPHICS_INFO_HOB           *GraphicsInfoHob;
    EFI_PEI_GRAPHICS_DEVICE_INFO_HOB    *DeviceInfoHob;
    UINT8                               vDev;
    UINT8                               vFunc;
    UINT64                              Segment;
    UINT32                              PciBase;
    DEV_PATH                            *DevicePath = (DEV_PATH*)(UINTN)Parameters->DevPath;
    
    GraphicsData = AllocateZeroPool (sizeof (ASPEED_PEI_GRAPHICS_DATA));
    if (GraphicsData == NULL) {
        return EFI_OUT_OF_RESOURCES;
    }
    
    GraphicsData->Mode.MaxMode         = 1;
    GraphicsData->Mode.Mode            = 0;
    GraphicsData->Mode.Info            = &GraphicsData->Info;
    GraphicsData->Mode.SizeOfInfo      = sizeof(EFI_GRAPHICS_OUTPUT_MODE_INFORMATION);
    GraphicsData->Mode.FrameBufferBase = Parameters->MemBase;
    
    switch (GetIndexReg(Parameters->IoBase + CRTC_PORT, 0xAA, 0xFF) & 0x03) {
        case 0x03:
            GraphicsData->Mode.FrameBufferSize = 0x04000000;
            break;
        case 0x02:
            GraphicsData->Mode.FrameBufferSize = 0x02000000;
            break;
        case 0x01:
            GraphicsData->Mode.FrameBufferSize = 0x01000000;
            break;
        default:
            GraphicsData->Mode.FrameBufferSize = 0x00800000;
            break;
    }
    
    GraphicsData->Info.Version              = 0;
    GraphicsData->Info.HorizontalResolution = VgaModeInfo->Xres;
    GraphicsData->Info.VerticalResolution   = VgaModeInfo->Yres;
    GraphicsData->Info.PixelFormat          = PixelBlueGreenRedReserved8BitPerColor;
    GraphicsData->Info.PixelsPerScanLine    = VgaModeInfo->Xres;
    
    DEBUG((DEBUG_INFO, "%a\n", __FUNCTION__));
    DEBUG((DEBUG_INFO, "MaxMode         : %x\n", GraphicsData->Mode.MaxMode));
    DEBUG((DEBUG_INFO, "Mode            : %x\n", GraphicsData->Mode.Mode));
    DEBUG((DEBUG_INFO, "SizeOfInfo      : %x\n", GraphicsData->Mode.SizeOfInfo));
    DEBUG((DEBUG_INFO, "FrameBufferBase : %x\n", GraphicsData->Mode.FrameBufferBase));
    DEBUG((DEBUG_INFO, "FrameBufferSize : %x\n", GraphicsData->Mode.FrameBufferSize));
    
    DEBUG((DEBUG_INFO, "Info.Version              : %x\n", GraphicsData->Info.Version));
    DEBUG((DEBUG_INFO, "Info.HorizontalResolution : %x\n", GraphicsData->Info.HorizontalResolution));
    DEBUG((DEBUG_INFO, "Info.VerticalResolution   : %x\n", GraphicsData->Info.VerticalResolution));
    DEBUG((DEBUG_INFO, "Info.PixelFormat          : %x\n", GraphicsData->Info.PixelFormat));
    DEBUG((DEBUG_INFO, "Info.PixelsPerScanLine    : %x\n", GraphicsData->Info.PixelsPerScanLine));
    
    GraphicsData->GraphicsPpi.GraphicsPpiInit    = AspeedGraphicsPpiInit;
    GraphicsData->GraphicsPpi.GraphicsPpiGetMode = AspeedGraphicsPpiGetMode;
    gPeiGraphicsPpiDescriptor.Ppi                = &GraphicsData->GraphicsPpi;
    
    //Install EFI_PEI_GRAPHICS_PPI
    Status = PeiServicesInstallPpi (&gPeiGraphicsPpiDescriptor);
    if (EFI_ERROR(Status)) {
        return Status;
    }
    
    //Build EFI_PEI_GRAPHICS_INFO_HOB hob
    GraphicsInfoHob = BuildGuidHob (&gEfiGraphicsInfoHobGuid, sizeof (EFI_PEI_GRAPHICS_INFO_HOB));
    
    if (GraphicsInfoHob == NULL) {
        return EFI_OUT_OF_RESOURCES;
    }
    
    GraphicsInfoHob->FrameBufferBase = GraphicsData->Mode.FrameBufferBase;
    GraphicsInfoHob->FrameBufferSize = (UINT32)GraphicsData->Mode.FrameBufferSize;
    CopyMem (
        &GraphicsInfoHob->GraphicsMode, 
        &GraphicsData->Info, 
        sizeof (EFI_GRAPHICS_OUTPUT_MODE_INFORMATION));
    
    //Build EFI_PEI_GRAPHICS_DEVICE_INFO_HOB Hob
    DeviceInfoHob = BuildGuidHob (&gEfiGraphicsDeviceInfoHobGuid, sizeof(EFI_PEI_GRAPHICS_DEVICE_INFO_HOB));
    if (DeviceInfoHob == NULL) {
        return EFI_OUT_OF_RESOURCES;
    }
    
    PciBase = Parameters->PciExpressCfgBase;
    vDev = DevicePath[Parameters->DevPathEntries-1].Dev;
    vFunc = DevicePath[Parameters->DevPathEntries-1].Func;
    Segment = DevicePath[Parameters->DevPathEntries-1].Segment;
    
    DeviceInfoHob->VendorId = AvPciSegmentRead16 (PciBase, Segment, vBus, vDev, vFunc, PCI_VENDOR_ID_OFFSET);
    DeviceInfoHob->DeviceId = AvPciSegmentRead16 (PciBase, Segment, vBus, vDev, vFunc, PCI_DEVICE_ID_OFFSET);
    DeviceInfoHob->SubsystemVendorId = AvPciSegmentRead16 (PciBase, Segment, vBus, vDev, vFunc, PCI_SVID_OFFSET);
    DeviceInfoHob->SubsystemId = AvPciSegmentRead16 (PciBase, Segment, vBus, vDev, vFunc, PCI_SID_OFFSET);
    DeviceInfoHob->RevisionId = AvPciSegmentRead8  (PciBase, Segment, vBus, vDev, vFunc, PCI_REVISION_ID_OFFSET);
    DeviceInfoHob->BarIndex = 0;
    
    DEBUG((DEBUG_INFO, "VendorId   : 0x%x\n", DeviceInfoHob->VendorId));
    DEBUG((DEBUG_INFO, "DeviceId   : 0x%x\n", DeviceInfoHob->DeviceId));
    DEBUG((DEBUG_INFO, "SVID       : 0x%x\n", DeviceInfoHob->SubsystemVendorId));
    DEBUG((DEBUG_INFO, "SID        : 0x%x\n", DeviceInfoHob->SubsystemId));
    DEBUG((DEBUG_INFO, "RevisionId : 0x%x\n", DeviceInfoHob->RevisionId));
    
    return EFI_SUCCESS;
}

VOID
CreateVideoParameterHob (
    VIDEO_PARAMETERS  *Parameters
)
{
    VIDEO_PARAMETERS    *Hob;
    EFI_GUID            HobGuid = AMI_VIDEO_PARAMETERS_DATA_HOB_GUID;

    Hob = BuildGuidDataHob (
                   &HobGuid,
                   Parameters,
                   sizeof (VIDEO_PARAMETERS));    
    if (Hob == NULL) {
        return;
    }
    
    // Clear the pointers in HOB
    Hob->DevPath = 0;
    Hob->FontMap = 0;
    Hob->PeiServices = 0;
}

/**
    Initializes Aspeed video controller with VGA standard init. 

    @param   Parameters - VIDEO_PARAMETERS structure specifying parameters for video initialization
             vBus - Bus Number of the video controller 

    @retval  EFI_SUCCESS if everything worked, or one of the video error
             codes if something did not work or bad input was provided.
**/

EFI_STATUS
AspeedVgaInit (
    VIDEO_PARAMETERS  *Parameters,
    UINT8             vBus 
)
{
    UINT8 vDev;
    UINT8 vFunc;
    UINT64 Segment;
    UINT32 PciBase;
    UINT8 Revision;
    UINT32 Index;
    ast_ExtRegInfo *ExtReg;
    //UINT32 OrgMemBase;
    VBIOS_MODE_INFO VgaModeInfo;
    UINT32 InitOption = Init_MEM;
    EFI_STATUS  Status = EFI_SUCCESS;
    EFI_PEI_SERVICES **PeiServices = NULL;
    DEV_PATH         *DevicePath = NULL;
    
#if Ast2600_EarlyVideo_DP_Port_Support
    InitOption |= Init_ASTDP;
#endif
    
    if (Parameters == NULL) {
        return EFI_INVALID_PARAMETER;
    }
    
    PeiServices = (EFI_PEI_SERVICES**)(UINTN)Parameters->PeiServices;
    DevicePath = (DEV_PATH*)(UINTN)Parameters->DevPath;
    
    if (Parameters->Mode >= EARLY_CONSOLE_MAX_MODE) {
        return EFI_INVALID_PARAMETER;
    }
  
    vDev = DevicePath[Parameters->DevPathEntries-1].Dev;
    vFunc = DevicePath[Parameters->DevPathEntries-1].Func;
    PciBase = Parameters->PciExpressCfgBase;
    Segment = DevicePath[Parameters->DevPathEntries-1].Segment;
  
    // Open Key
    EnableVga(Parameters->IoBase);
    SetIndexReg(Parameters->IoBase + CRTC_PORT, 0x80, 0xA8);
    for (Index = 0x81; Index < 0x9E; Index++) {
        SetIndexReg(Parameters->IoBase + CRTC_PORT, (UINT8)Index, 0x00);
    }
    
    //OrgMemBase = Parameters.MemBase;
    //Parameters.MemBase = AvPciRead32(PciBase, vBus, vDev, vFunc, 0x14);
    //Parameters.MemBase = Parameters.MemBase & 0xFFFFFFF0;

    ExtReg = astExtReg;
    Revision = AvPciSegmentRead8(PciBase, Segment, vBus, vDev, vFunc, 0x8);

    if (Revision >= 0x20) {
        ExtReg = astExtReg_AST2300;
    }
    do {
        SetIndexReg(Parameters->IoBase + CRTC_PORT, ExtReg->Index, ExtReg->Data);
        ExtReg++;
    } while (ExtReg->Index != 0xFF);
      
    if (Revision == 0x40) {
        SetIndexReg(Parameters->IoBase + CRTC_PORT, 0xbc, 0x40);
        SetIndexReg(Parameters->IoBase + CRTC_PORT, 0xbd, 0x38);
        SetIndexReg(Parameters->IoBase + CRTC_PORT, 0xbe, 0x3a);
        SetIndexReg(Parameters->IoBase + CRTC_PORT, 0xbf, 0x38);
        SetIndexReg(Parameters->IoBase + CRTC_PORT, 0xcf, 0x70);
        SetIndexReg(Parameters->IoBase + CRTC_PORT, 0xb5, 0xa8);
        SetIndexReg(Parameters->IoBase + CRTC_PORT, 0xbb, 0x43);
    }

    if (InitOption & Init_MEM)
    {
        /* Dram Init 8 */
        if (Revision >= 0x50){
            // InitOption &= ~Init_MEM;        // for AST2600
            DEBUG((DEBUG_ERROR, "Revision >= 0x50, init ast2600\n"));
            InitAST2600DramReg(Parameters->IoBase);
        }else if (Revision >= 0x40)
            // InitAST2500DRAMReg(DevInfo); // Only support AST2600
            return EFI_INVALID_PARAMETER;
        else if (Revision >= 0x20)
            return EFI_INVALID_PARAMETER;
            //InitAST2300DRAMReg(DevInfo); // Only support AST2600
        else
            return EFI_INVALID_PARAMETER;
            //InitDRAMReg(DevInfo);// Only support AST2600
    }
    
#if Ast2600_EarlyVideo_DP_Port_Support
    /* AST DP Init */
    if (InitOption & Init_ASTDP)
    {
        if (ASTDP_Check(PeiServices, Parameters->IoBase, 100)) {
            if(!ASTDP_WaitReady(PeiServices,Parameters->IoBase, 100)) {
                    DEBUG((DEBUG_ERROR, "Failed to wait ASTDP ready!\n"));
                    InitOption &= ~Init_ASTDP;
            }
        } else {
            DEBUG((DEBUG_ERROR, "Failed to find ASTDP!\n"));
            InitOption &= ~Init_ASTDP;
        }
    }
#endif
    
    
    // Set Mode
/* AMI Possible Porting Required */
    if (Parameters->Mode == EARLY_CONSOLE_TEXT_MODE) {
        // Set to Text Mode 800x600 by default.  Supports 640x480, 800x600, 1024x768, 1280x1024, 1600x1200. 
        VgaModeInfo.Xres = 800;
        VgaModeInfo.Yres = 600;
        VgaModeInfo.Bpp  = 1; // Early Console Out only supports text mode.  Do not
                              // change this unless you know what you are doing!
    /* AMI Possible Porting Required */
    /* AMI DP Text mode porting */
#if Ast2600_EarlyVideo_DP_Port_Support
        VgaModeInfo.Xres = 720;
        VgaModeInfo.Yres = 400;
        VgaModeInfo.Bpp  = 1; // Early Console Out only supports text mode.  Do not
                              // change this unless you know what you are doing!
#endif
    /* AMI DP Text mode porting */
    } else {
        
        switch (Parameters->DisplayResolution) {
            case EARLY_CONSOLE_GRAPHICS_RES_640x480:
                VgaModeInfo.Xres = 640;
                VgaModeInfo.Yres = 480;
                VgaModeInfo.Bpp = 32;
                break;
            case EARLY_CONSOLE_GRAPHICS_RES_800x600:
            case EARLY_CONSOLE_GRAPHICS_RES_MAX:          //Forcing 800x600 as Maximum possible resolution supported if platform configured with Max resolution.
                VgaModeInfo.Xres = 800;
                VgaModeInfo.Yres = 600;
                VgaModeInfo.Bpp = 32;
                break;
            case EARLY_CONSOLE_GRAPHICS_RES_1024x768:
                VgaModeInfo.Xres = 1024;
                VgaModeInfo.Yres = 768;
                VgaModeInfo.Bpp = 32;
                break;
            case EARLY_CONSOLE_GRAPHICS_RES_1280x1024:
                VgaModeInfo.Xres = 1280;
                VgaModeInfo.Yres = 1024;
                VgaModeInfo.Bpp = 32;
                break;
            case EARLY_CONSOLE_GRAPHICS_RES_1600x1200:
                VgaModeInfo.Xres = 1600;
                VgaModeInfo.Yres = 1200;
                VgaModeInfo.Bpp = 32;
                break;
            default: 
                return EFI_INVALID_PARAMETER;
        }
    
    }
    
    switch(VgaModeInfo.Bpp) {
        case 1:
            VgaModeInfo.pStdTableEntry = (PVBIOS_STDTABLE_STRUCT) &StdTable[TextModeIndex];
            break;
        case 8:
            VgaModeInfo.pStdTableEntry = (PVBIOS_STDTABLE_STRUCT) &StdTable[VgaModeIndex];
            break;
        case 16:
            VgaModeInfo.pStdTableEntry = (PVBIOS_STDTABLE_STRUCT) &StdTable[HicModeIndex];
            break;
        case 24:
        case 32:
            VgaModeInfo.pStdTableEntry = (PVBIOS_STDTABLE_STRUCT) &StdTable[TruecModeIndex];
            break;
        default:
            return EFI_INVALID_PARAMETER;
    }
    
    switch (VgaModeInfo.Xres) {
        case 640:
        case 720:
            VgaModeInfo.pEnhTableEntry = (PVBIOS_ENHTABLE_STRUCT) Res640x480Table;
            break;
        case 800:
            VgaModeInfo.pEnhTableEntry = (PVBIOS_ENHTABLE_STRUCT) Res800x600Table;
            break;
        case 1024:
            VgaModeInfo.pEnhTableEntry = (PVBIOS_ENHTABLE_STRUCT) Res1024x768Table;
            break;
        case 1280:
            VgaModeInfo.pEnhTableEntry = (PVBIOS_ENHTABLE_STRUCT) Res1280x1024Table;
            break;
        case 1600:
            VgaModeInfo.pEnhTableEntry = (PVBIOS_ENHTABLE_STRUCT) Res1600x1200Table;
            break;
        case 1920:
            VgaModeInfo.pEnhTableEntry = (PVBIOS_ENHTABLE_STRUCT) Res1920x1080Table;
            break;
        default:
            return EFI_INVALID_PARAMETER;
    }

    DisplayOff(Parameters->IoBase);
    
#if Ast2600_EarlyVideo_DP_Port_Support     
    if (InitOption & Init_ASTDP)
    {
        ASTDP_SetOnOff(PeiServices,Parameters->IoBase, 0);   
        ASTDP_PowerOnOff(Parameters->IoBase, 0);
    }
#endif  
    
    switch (VgaModeInfo.Bpp) {
        case 1:
            ResetReg(Parameters->IoBase);
            SetStdReg(Parameters->IoBase, &VgaModeInfo);
            SetExtReg(Parameters->IoBase, &VgaModeInfo, 0, Revision);
            SetDacReg(Parameters->IoBase, &VgaModeInfo);
            LoadTextFont(Parameters->IoBase);
            ClearTextBuffer(PeiServices, &VgaModeInfo); 
            break;
        default:
            ResetReg(Parameters->IoBase);
            SetStdReg(Parameters->IoBase, &VgaModeInfo);
            SetCrtcReg(Parameters->IoBase, &VgaModeInfo);
            SetOffsetReg(Parameters->IoBase, &VgaModeInfo);
            SetDclkReg(Parameters->IoBase, Revision, &VgaModeInfo);
            SetExtReg(Parameters->IoBase, &VgaModeInfo, 0, Revision);
            SetSyncReg(Parameters->IoBase, &VgaModeInfo);
            SetDacReg(Parameters->IoBase, &VgaModeInfo);
            ClearBuffer(Parameters->IoBase, &VgaModeInfo, PeiServices);
            break;
    }
#if Ast2600_EarlyVideo_DP_Port_Support
    if (InitOption & Init_ASTDP) {
        ASTDP_SetOutput(&VgaModeInfo,Parameters->IoBase);
    }
#endif
    
    DisplayOn(Parameters->IoBase);
    
#if Ast2600_EarlyVideo_DP_Port_Support    
    if (InitOption & Init_ASTDP)
    {
        ASTDP_PowerOnOff(Parameters->IoBase, 1);
        ASTDP_WaitVSync(Parameters->IoBase);
        ASTDP_SetOnOff(PeiServices, Parameters->IoBase, 1);   
    }
#endif  
    
    // Produce PeiGraphics PPI only when EarlyGraphics is enabled
    if (Parameters->Mode == EARLY_CONSOLE_GRAPHICS_MODE) {
        
        
        Status = InstallPeiGraphicsPpi (
                                   Parameters,
                                   &VgaModeInfo,
                                   vBus);
    }
    
    CreateVideoParameterHob (Parameters);
    
    return Status;
}
