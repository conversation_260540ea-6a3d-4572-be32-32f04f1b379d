/*
*****************************************************************************
*
 * Copyright (C) 2018-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*******************************************************************************
*/

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Library/DebugLib.h>
#include <Library/PciLib.h>
#include <Library/BaseLib.h>
#include <Library/PciSegmentLib.h>
#include <Library/FabricRegisterAccLib.h>
#include "AmdRasRegistersBrh.h"
#include <Library/AmdBaseLib.h>
#include <Library/DfAddressTranslateLib.h>
#include <Library/BaseFabricTopologyLib.h>
#include <Filecode.h>
#include <Library/IdsLib.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE LIBRARY_RAS_RS_DFADDRESSTRANSLATERSLIB_DFADDRESSTRANSLATERSLIB_FILECODE

#define DF__DRAMMEGABASE_REGADDR_PRE_DF4      ((0<<12)|(0x190))
#define DF__DRAMMEGALIMIT_REGADDR_PRE_DF4     ((0<<12)|(0x194))
#define DF__DRAMMEGACONTROL_REGADDR_PRE_DF4   ((0<<12)|(0x198))

// Defines for register bit positions in DF
#define DF__SOCKET_ID_SHIFT_BITPOS_LO_DF2       28
#define DF__SOCKET_ID_SHIFT_BITPOS_HI_DF2       31
#define DF__SOCKET_ID_MASK_BITPOS_LO_DF2        16
#define DF__SOCKET_ID_MASK_BITPOS_HI_DF2        23
#define DF__DIE_ID_SHIFT_BITPOS_LO_DF2          24
#define DF__DIE_ID_SHIFT_BITPOS_HI_DF2          27
#define DF__DIE_ID_MASK_BITPOS_LO_DF2            8
#define DF__DIE_ID_MASK_BITPOS_HI_DF2           15
#define DF__SOCKET_ID_SHIFT_BITPOS_LO_DF3        8
#define DF__SOCKET_ID_SHIFT_BITPOS_HI_DF3       11 // not all bits are present, but they are reserved in DF3 and used in DF4
#define DF__SOCKET_ID_MASK_BITPOS_LO_DF3        24
#define DF__SOCKET_ID_MASK_BITPOS_HI_DF3        26
#define DF__DIE_ID_MASK_BITPOS_LO_DF4            0
#define DF__DIE_ID_MASK_BITPOS_HI_DF4           15
#define DF__SOCKET_ID_MASK_BITPOS_LO_DF4        16
#define DF__SOCKET_ID_MASK_BITPOS_HI_DF4        31
// Note: no DF__DIE_ID_SHIFT_BITPOS_LO_DF3 and DF__DIE_ID_SHIFT_BITPOS_HI_DF3.
// Use the DF__NODE_ID_SHIFT_BITPOS_ to determine the LSB of NodeID.
#define DF__DIE_ID_MASK_BITPOS_LO_DF3           16
#define DF__DIE_ID_MASK_BITPOS_HI_DF3           18
#define DF__NODE_ID_SHIFT_BITPOS_LO_DF3          0
#define DF__NODE_ID_SHIFT_BITPOS_HI_DF3          3
#define DF__NODE_ID_MASK_BITPOS_LO_DF3          16
#define DF__NODE_ID_MASK_BITPOS_HI_DF3          31 // not all bits are present, but they are reserved in DF3 and used in DF4
#define DF__COMPONENT_ID_MASK_BITPOS_LO_DF3      0
#define DF__COMPONENT_ID_MASK_BITPOS_HI_DF3     15 // not all bits are present, but they are reserved in DF3 and used in DF4
#define DF__BLOCK_INSTANCE_COUNT_BITPOS_LO       0
#define DF__BLOCK_INSTANCE_COUNT_BITPOS_HI       9
#define DF__MINOR_REVISION_BITPOS_LO            16
#define DF__MINOR_REVISION_BITPOS_HI            23
#define DF__MAJOR_REVISION_BITPOS_LO            24
#define DF__MAJOR_REVISION_BITPOS_HI            27
#define DF__INSTANCE_TYPE_BITPOS_LO              0
#define DF__INSTANCE_TYPE_BITPOS_HI              3
#define DF__BLOCK_FABRICID_BITPOS_LO             8
#define DF__BLOCK_FABRICID_BITPOS_HI            19 // not all bits are present, but they are reserved in DF3 and used in DF4
#define DF__INSTANCE_SUBTYPE_BITPOS_LO          24
#define DF__INSTANCE_SUBTYPE_BITPOS_HI          26
#define DF__HI_ADDR_OFFSET_EN_BITPOS             0
#define DF__HI_ADDR_OFFSET_BITPOS_LO_DF2        20
#define DF__HI_ADDR_OFFSET_BITPOS_HI_DF2        31
#define DF__HI_ADDR_OFFSET_BITPOS_LO_DF3        12
#define DF__HI_ADDR_OFFSET_BITPOS_HI_DF3        31
#define DF__HI_ADDR_OFFSET_BITPOS_LO_DF4         1
#define DF__HI_ADDR_OFFSET_BITPOS_HI_DF4        31 // Include reserved bits - used in DF4.5
#define DF__ADDR_RANGE_VALID_BITPOS              0
#define DF__DRAM_BASE_ADDR_BITPOS_LO            12
#define DF__DRAM_BASE_ADDR_BITPOS_HI            31
#define DF__DRAM_LIMIT_ADDR_BITPOS_LO           12
#define DF__DRAM_LIMIT_ADDR_BITPOS_HI           31
#define DF__DRAM_BASE_ADDR_BITPOS_LO_DF4         0
#define DF__DRAM_BASE_ADDR_BITPOS_HI_DF4        27
#define DF__DRAM_LIMIT_ADDR_BITPOS_LO_DF4        0
#define DF__DRAM_LIMIT_ADDR_BITPOS_HI_DF4       27
#define DF__LEGACY_MMIO_HOLE_EN_BITPOS           1
#define DF__DRAM_HOLE_BASE_ADDR_BITPOS_LO       24
#define DF__DRAM_HOLE_BASE_ADDR_BITPOS_HI       31
#define DF__DRAM_HOLE_VALID_BITPOS               0
#define DF__INTLV_ADDR_SEL_BITPOS_LO_DF2         8
#define DF__INTLV_ADDR_SEL_BITPOS_HI_DF2        10
#define DF__INTLV_ADDR_SEL_BITPOS_LO_DF3         9
#define DF__INTLV_ADDR_SEL_BITPOS_HI_DF3        11
#define DF__INTLV_ADDR_SEL_BITPOS_LO_DF4         0
#define DF__INTLV_ADDR_SEL_BITPOS_HI_DF4         2
#define DF__INTLV_NUM_CHAN_BITPOS_LO_DF2         4
#define DF__INTLV_NUM_CHAN_BITPOS_HI_DF2         7
#define DF__INTLV_NUM_CHAN_BITPOS_LO_DF3         2
#define DF__INTLV_NUM_CHAN_BITPOS_HI_DF3         5
#define DF__INTLV_NUM_CHAN_BITPOS_LO_DF4         4
#define DF__INTLV_NUM_CHAN_BITPOS_HI_DF4         8
#define DF__INTLV_NUM_CHAN_BITPOS_HI_DF4POINT5   9
#define DF__INTLV_NUM_CHAN_BITPOS_HI_DF3POINT5   6
#define DF__INTLV_NUM_DIES_BITPOS_LO_DF2        10
#define DF__INTLV_NUM_DIES_BITPOS_HI_DF2        11
#define DF__INTLV_NUM_DIES_BITPOS_LO_DF3         6
#define DF__INTLV_NUM_DIES_BITPOS_HI_DF3         7
#define DF__INTLV_NUM_DIES_BITPOS_DF3POINT5      7
#define DF__INTLV_NUM_DIES_BITPOS_LO_DF4        12
#define DF__INTLV_NUM_DIES_BITPOS_HI_DF4        13
#define DF__INTLV_NUM_SOCKETS_BITPOS             8
#define DF__INTLV_NUM_SOCKETS_BITPOS_DF4        18
#define DF__DSTFABRICID_BITPOS_LO_DF2            0
#define DF__DSTFABRICID_BITPOS_HI_DF2            7
#define DF__DSTFABRICID_BITPOS_LO_DF3            0
#define DF__DSTFABRICID_BITPOS_HI_DF3            9
#define DF__DSTFABRICID_BITPOS_HI_DF3POINT5     11
#define DF__DSTFABRICID_BITPOS_LO_DF4           16
#define DF__DSTFABRICID_BITPOS_HI_DF4           27
#define DF__HASH_INTLV_CTL_64K_BITPOS_DF3       20
//#define DF__HASH_INTLV_CTL_2M_BITPOS_DF3        21
//#define DF__HASH_INTLV_CTL_1G_BITPOS_DF3        22
#define DF__HASH_INTLV_CTL_64K_BITPOS_DF4        8
#define DF__LOG2_ADDR_SPACE_BITPOS_LO            0
#define DF__LOG2_ADDR_SPACE_BITPOS_HI            5
#define DF__LOG2_ADDR_SPACE_BITPOS_LO_DF4       24
#define DF__LOG2_ADDR_SPACE_BITPOS_HI_DF4       29
#define DF__REMAP_EN_BITPOS_DF4                  4
#define DF__REMAP_SEL_BITPOS_LO_DF4              5
#define DF__REMAP_SEL_BITPOS_HI_DF4              7
#define DF__REMAP_SEL_BITPOS_LO_DF4POINT5        5
#define DF__REMAP_SEL_BITPOS_HI_DF4POINT5        6
#define DF__MI300_LSB_ADDR_BIT_IN_DRAM_OFFSET    20

#define DF__CXL_ADDR_FORMAT_BITPOS_LO           28
#define DF__CXL_ADDR_FORMAT_BITPOS_HI           31
#define DF__CXL_BASE_ADDR_BITPOS_LO_FMT0         2
#define DF__CXL_BASE_ADDR_BITPOS_HI_FMT0        25
#define DF__CXL_BASE_ADDR_BITPOS_LO_FMT1         0
#define DF__CXL_BASE_ADDR_BITPOS_HI_FMT1        23
#define DF__CXL_LIMIT_ADDR_BITPOS_LO             0
#define DF__CXL_LIMIT_ADDR_BITPOS_HI            23
#define DF__CXL_INT_LV_ADDR_SEL_BITPOS           0
#define DF__CXL_NPA_EN_BITPOS_FMT0               1
#define DF__CXL_NPA_EN_BITPOS_FMT1               9
#define DF__CXL_NPA_BASE_EN_BITPOS              10
#define DF__CXL_INT_LV_LINK_EN_BITPOS_LO         4
#define DF__CXL_INT_LV_LINK_EN_BITPOS_HI         7
#define DF__CXL_ADDR_RANGE_VALID_BITPOS_FMT0     0
#define DF__CXL_ADDR_RANGE_VALID_BITPOS_FMT1     8

//#define DF__CXL_BASE_ADDR_BITPOS_LO              2
//#define DF__CXL_BASE_ADDR_BITPOS_HI             25

#define DF__LSB_ADDR_BIT_IN_DRAM_MAPS           28
#define DF__LSB_ADDR_BIT_IN_DRAM_OFFSET         28
#define DF__LSB_ADDR_BIT_IN_CXL_MAPS            28

// Number of address map registers in a single map
//   In DF4, we include the remap register as part of the map
//     BASE+LIMIT+CTL+INTLV+REMAPHI+REMAPLO
//   In DF3, we include the NP2_CHANNEL_CONFIG and the remap
//   as part of the map
//     BASE+LIMIT+{zeros}+{zeros}+NP2+REMAPLO
#define ADDR_MAP_ARRAYSIZE                       8
#define ADDR_MAP_ARRAY_BASE_OFFSET               0
#define ADDR_MAP_ARRAY_LIMIT_OFFSET              1
#define ADDR_MAP_ARRAY_CTL_OFFSET                2
#define ADDR_MAP_ARRAY_INTLV_OFFSET              3
#define ADDR_MAP_ARRAY_NP2_OFFSET                4 // DF3 only
#define ADDR_MAP_ARRAY_REMAPHI_OFFSET            4 // DF4 only
#define ADDR_MAP_ARRAY_REMAPLO_OFFSET            5

#define DF__NUM_DRAM_MAPS_AVAILABLE_PRE_DF4     16
#define DF__NUM_DRAM_MAPS_AVAILABLE_DF4         32
#define DF__NUM_DRAM_MAPS_AVAILABLE_DF4POINT5   64
#define DF__NUM_CXL_MAPS_AVAILABLE               4
// P0 can be subdivided into four x4.
// P0 is the "link" and then the four x4's are the "ports"
#define DF__NUM_CXL_SUBPORTS_PER_LINK            4
#define DF__CCM_INSTANCE_TYPE_VALUE              0
#define DF__GCM_INSTANCE_TYPE_VALUE              1
#define DF__CS_INSTANCE_TYPE_VALUE               4
#define DF__CNLI_INSTANCE_TYPE_VALUE            13
#define DF__CSCMP_INSTANCE_SUBTYPE_VALUE         2

// SOC-specific values
#define STONES_DF_DEVICE_AND_VENDOR_ID           0x14AD1022
#define TURIN_DF_DEVICE_AND_VENDOR_ID            0x12C01022
#define MI300_DEVICE_VENDOR_ID                   0x15281022
#define STONES_CSCMP0_INSTANCE_ID               12
#define STONES_IOS0_INSTANCE_ID                 36
#define STONES_CNLI0_INSTANCE_ID                53
#define TURIN_CSCMP0_INSTANCE_ID                12
#define TURIN_IOS0_INSTANCE_ID                  40
#define TURIN_CNLI0_INSTANCE_ID                 59

#define DF__COHERENTSLAVEMODECTRLA1_REGADDR   ((2<<12)|(0x074))
#define DF__CXLBASE0_REGADDR_DF4              ((7<<12)|(0xD80))

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */
extern  ADDR_DATA               *gAddrData;

INT32 savedComponentIdMask = -1;
INT32 savedNodeIdShift = -1;
INT32 savedNodeIdMask = -1;
INT32 savedSocketIdShift = -1;
INT32 savedSocketIdMask = -1;
INT32 savedDieIdShift = -1;
INT32 savedDieIdMask = -1;

UINT8 DRAMTYPE = 7;
UINT8 DRAMTYPE_1 = 7;
UINTN LOC = 0;
UINTN LOC_1 = 0;
UINTN LOC_2 = 0;
UINT8 TOTAL_NUM_UMCCH_PER_UMC_ADDR_TRANS = 1;
UINT8 three_way_cs;
UINT8 two_p_one;

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */
typedef struct
{
  UINT32   dfVersion;
  BOOLEAN  isDGpu;
  BOOLEAN  isHeterogeneous;
} DfType;

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
// Function prototypes of private internal functions
UINT32 getBits(UINT32 lowBit, UINT32 highBit, UINT32 data);
UINT64 getBits64(UINT32 lowBit, UINT32 highBit, UINT64 data);
UINT64 getBit64(UINT32 bit, UINT64 data);
UINT32 getBit(UINT32 bit, UINT32 data);
UINT64 removeBits64(UINT32 lowBit, UINT32 highBit, UINT64 data);
UINT64 expandBits64(UINT32 bitNumber, UINT32 numBits, UINT64 data);
UINT32 getDfReg(DfType *dfType, UINT32 instance, UINT32 nodeId, UINT32 regAddr);
UINT32 getDfRegSystemFabricIdMask0(DfType *dfType);
UINT32 getDfRegSystemFabricIdMask1(DfType *dfType);
UINT32 getDfRegSystemFabricIdMask2(DfType *dfType);
UINT32 getComponentIdMask(DfType *dfType);
UINT32 getNodeIdShift(DfType *dfType);
UINT32 getNodeIdMask(DfType *dfType);
UINT32 getSocketIdShift(DfType *dfType);
UINT32 getSocketIdMask(DfType *dfType);
UINT32 getDieIdShift(DfType *dfType);
UINT32 getDieIdMask(DfType *dfType);
UINT32 getNumAddressMaps(DfType *dfType);
UINT32 getNumCsMaps(DfType *dfType);
UINT32 getDfRegFabricBlkInstanceCnt(DfType *dfType, UINT32 nodeId);
UINT32 getDfRegFabricBlkInstInfo0(DfType *dfType, UINT32 instanceId, UINT32 nodeId);
UINT32 getDfRegFabricBlkInstInfo3(DfType *dfType, UINT32 instanceId, UINT32 nodeId);
UINT32 getIntlvHashCtlBits(DfType *dfType, UINT32 *dramAddressMapRegs);
VOID determineDfType(DfType *dfType);
UINT32 getDfRegDramOffset(DfType *dfType, UINT32 instanceId, UINT32 nodeId, UINT32 regNum);
UINT64 extractDramOffset(DfType *dfType, UINT32 dramOffsetReg);
VOID getCxlAddressMap(DfType *dfType, INT32 instanceId, UINT32 nodeId, UINT32 mapNumber, UINT32 *cxlAddressMapRegs);
VOID getDramAddressMap(DfType *dfType, INT32 instanceId, UINT32 nodeId, UINT32 mapNumber, UINT32 *dramAddressMapRegs);
UINT32 getDfRegDramHoleCtrl(DfType *dfType);
UINT64 getDramHoleBase(DfType *dfType);
UINT32 decodeDramIntLvAddrBit(DfType *dfType, UINT32 *dramAddressMapRegs);
INT32 getNumChannelFromDramIntLvMode(INT32 intLvMode);
UINT32 decodeDramIntLvNumChan(DfType *dfType, UINT32 *dramAddressMapRegs);
UINT32 extractDramIntLvNumDies(DfType *dfType, UINT32 *dramAddressMapRegs);
UINT32 decodeDramIntLvNumDies(DfType *dfType, UINT32 *dramAddressMapRegs);
UINT32 extractDramIntLvNumSkts(DfType *dfType, UINT32 *dramAddressMapRegs);
UINT32 extractDstFabricId(DfType *dfType, UINT32 *dramAddressMapRegs);
UINT64 extractDramBaseAddr(DfType *dfType, UINT32 *dramAddressMapRegs);
UINT64 extractDramLimitAddr(DfType *dfType, UINT32 *dramAddressMapRegs);
UINT32 extractDramAddrRangeValid(DfType *dfType, UINT32 *dramAddressMapRegs);
UINT32 extractLgcyMmioHoleEn(DfType *dfType, UINT32 *dramAddressMapRegs);
UINT32 extractLog2Addr64KSpace(DfType *dfType, UINT32 *dramAddressMapRegs);
UINT64 extractCxlBaseAddr(DfType *dfType, UINT32 *cxlAddressMapRegs);
UINT64 extractCxlLimitAddr(DfType *dfType, UINT32 *cxlAddressMapRegs);
UINT32 extractCxlAddrRangeValid(DfType *dfType, UINT32 *cxlAddressMapRegs);
UINT32 extractCxlNpaEn(DfType *dfType, UINT32 *cxlAddressMapRegs);
UINT32 extractCxlNpaBaseEn(DfType *dfType, UINT32 *cxlAddressMapRegs);
UINT32 extractCxlIntLvLinkEn(DfType *dfType, UINT32 *cxlAddressMapRegs);
UINT32 decodeCxlNumSubChannelInterleaveBits(DfType *dfType, UINT32 *cxlAddressMapRegs);
UINT32 decodeCxlIntLvAddrSel(DfType *dfType, UINT32 *cxlAddressMapRegs);
UINT32 convertPhysicalCsFabricIdToLogicalCsFabricId(DfType *dfType, UINT32 csFabricId, UINT32 *dramAddressMapRegs);
UINT32 convertLogicalFabricIdToPhysicalFabricId(DfType *dfType, UINT32 logicalDstFabricId, UINT32 *dramAddressMapRegs);
UINT32 getCsLogicalComponentIdFromAddr(DfType *dfType, UINT32 *dramAddressMapRegs, UINT64 addr);
UINT64 normalizeMod3(DfType *dfType, UINT32 *dramAddressMapRegs, UINT64 addr);
UINT64 normalizeDf4NP2(DfType *dfType, UINT32 *dramAddressMapRegs, UINT64 addr);
UINT64 normalizeAddr(DfType *dfType, UINT32 *dramAddressMapRegs, UINT64 addr);
UINT64 deNormalizeAddrMod3(DfType *dfType, UINT32 logicalCsFabricId, UINT32 *dramAddressMapRegs, UINT64 normAddr);
UINT64 deNormalizeAddrDf4Np2(DfType *dfType, UINT32 logicalCsFabricId, UINT32 *dramAddressMapRegs, UINT64 normAddr);
UINT64 deNormalizeAddr(DfType *dfType, UINT32 csFabricId, UINT32 *dramAddressMapRegs, UINT64 normAddr);
UINT64 deNormHashAddr(DfType *dfType, UINT32 *dramAddressMapRegs, UINT64 deNormAddr);
BOOLEAN isSystemDiscreteGpu(DfType *dfType, UINT32 nodeId);
UINT32 findModeratorInstanceId(DfType *dfType, UINT32 nodeId);
UINT32 findMapRegBySysAddr(DfType *dfType, UINT32 nodeId, UINT64 sysAddr);
UINT32 findMapRegByDstFabricId(DfType *dfType, UINT32 nodeId, UINT32 dstFabricId);

/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------*/
UINT8
get_dramtype()
{
  return DRAMTYPE_1;
}
/**
 *
 *
 *
 */
UINT64
pow_ras(
  UINTN   input,
  UINTN   exp
  )
{
  UINT64  value;
  UINTN   i;

  value = 1;

  if (0 == exp) {
  } else {
    for (i = 0; i < exp; i++) {
      value *= input;
    }
  }
  return value;
}

/*----------------------------------------------------------------------------------------*/
/**
 *
 *
 */
UINT32
log2 (
  UINT32 input
  )
{
  switch (input)
  {
    case 1:
      return 0;
    case 2:
      return 1;
    case 4:
      return 2;
    case 8:
      return 3;
    case 16:
      return 4;
    case 32:
      return 5;
    case 64:
      return 6;
    case 128:
      return 7;
    case 256:
      return 8;
    case 512:
      return 9;
    case 1024:
      return 10;
    default:
      ASSERT (FALSE);
      return 0;
  }
}

/*------------------------------------------------------------------
 Function: getBits
 Purpose: A helper function to get a bit range from a UINT32
 Inputs:
   low bit number
   high bit number
   the data
 Outputs:
   the requested bits, right justified
 *------------------------------------------------------------------*/
UINT32
getBits (
  UINT32 lowBit,
  UINT32 highBit,
  UINT32 data
  )
{
  UINT32 mask;
  ASSERT (highBit < 32);
  ASSERT (lowBit < 32);
  ASSERT (lowBit <= highBit);
  mask = (1 << (highBit - lowBit + 1)) - 1;
  return ((data >> lowBit) & mask);
}

/*------------------------------------------------------------------
 Function: getBits64
 Purpose: A helper function to get a bit range from a UINT64
 Inputs:
   low bit number
   high bit number
   the data
 Outputs:
   the requested bits, right justified
 *------------------------------------------------------------------*/
UINT64
getBits64 (
  UINT32 lowBit,
  UINT32 highBit,
  UINT64 data
  )
{
  UINT64    mask;
  ASSERT (highBit < 64);
  ASSERT (lowBit < 64);
  ASSERT (lowBit <= highBit);
  mask = (((UINT64)1) << (highBit - lowBit + ((UINT64)1))) - ((UINT64)1);
  return ((data >> lowBit) & mask);
}

/*------------------------------------------------------------------
 Function: getBit64
 Purpose: A helper function to get a specific bit from a UINT64
 Inputs:
   bit number
   the data
 Outputs:
   the requested bits, right justified
 *------------------------------------------------------------------*/
UINT64
getBit64 (
  UINT32 bit,
  UINT64 data
  )
{
  ASSERT (bit < 64);
  return ((data >> bit) & 0x1);
}

/*------------------------------------------------------------------
 Function: getBit
 Purpose: A helper function to get a specific bit from a UINT32
 Inputs:
   bit number
   the data
 Outputs:
   the requested bit, right justified
 *------------------------------------------------------------------*/
UINT32
getBit (
  UINT32 bit,
  UINT32 data
  )
{
  ASSERT (bit < 32);
  return ((data >> bit) & 0x1);
}

/*------------------------------------------------------------------
 Function: removeBits64
 Purpose: A helper function to remove bits from a 64-bit value
 Inputs:
   low bit number
   high bit number
   the data
 Outputs:
   data with bits [high:low] removed (shifted out)
   e.g. AAAAAAXXXBBBB with bits 6:4 removed is AAAAAABBBB
 *------------------------------------------------------------------*/
UINT64 removeBits64 (
  UINT32 lowBit,
  UINT32 highBit,
  UINT64 data
  )
{
  UINT64 temp1, temp2;
  ASSERT (highBit < 64);
  ASSERT (lowBit < 64);
  ASSERT (lowBit <= highBit);
  if (lowBit == 0)
  {
    data = data >> (highBit + 1);
    return (data);
  }
  temp1 = getBits64 (0, lowBit - 1, data);
  temp2 = getBits64 (highBit + 1, 63, data);
  temp2 = temp2 << lowBit;
  data = temp1 | temp2;
  return (data);
}

/*------------------------------------------------------------------
 Function: expandBits64
 Purpose: A helper function to expand bits within 64-bit value
 Inputs:
   bit number to start at
   number of bits to push out
   the data
 Outputs:
   data with bits [63:n] << number of Bits
   ORd with bits [n:0]
   e.g. AAAAAABBBB with 3 bits added at bit location 4 is AAAAAA000BBBB
 *------------------------------------------------------------------*/
UINT64 expandBits64 (
  UINT32 bitNumber,
  UINT32 numBits,
  UINT64 data
  )
{
  UINT64 temp1, temp2;
  ASSERT (bitNumber < 64);
  ASSERT (numBits < 64);
  if (bitNumber == 0)
  {
    data = data << numBits;
    return (data);
  }
  temp1 = getBits64 (0, bitNumber - 1, data);
  temp2 = getBits64 (bitNumber, 63, data);
  temp2 = temp2 << (bitNumber + numBits);
  data = temp1 | temp2;
  return (data);
}

/*------------------------------------------------------------------
 Function: getDfReg
 Purpose: Get a DF register
 Inputs:
   DF type
   An instanceID (-1 for a broadcast read),
   A nodeID (for the PCIe bus/device),
   A register address:
     function in bits 14:12
     offset in bits 11:0
     NOTE: This is DF4 compatible, DF3 must shift the function over 2 bits
 Outputs: The requested register
 *------------------------------------------------------------------*/
UINT32
getDfReg (
  DfType *dfType,
  UINT32 instanceId,
  UINT32 nodeId,
  UINT32 regAddr
  )
{
  UINT32                               RegisterValue;
  PCI_ADDR                             PciAddr;
  FABRIC_IND_CFG_ACCESS_ADDR_REGISTER  FICAA3;
  UINT32                               Ficaa3Save;

  PciAddr.AddressValue = 0;
  PciAddr.Address.Device = nodeId + 0x18;   // 0x18 = PCIE device number of socket 0 die 0

  FICAA3.Value = 0;   // Variable initiate
  if (instanceId != FABRIC_REG_ACC_BC) {
    FICAA3.Field.CfgRegInstAccEn = 1;
  }

  PciAddr.Address.Function = FICAA3_FUNC;
  PciAddr.Address.Register = FICAA3_REG;

  //Save FICAA3 register value
  Ficaa3Save = PciRead32 (PciAddr.AddressValue);

  FICAA3.Field.IndCfgAccRegNum = ((UINT32) (regAddr & 0xfff)) >> 2;
  FICAA3.Field.IndCfgAccFuncNum = ((UINT32)(regAddr >> 12 )) & 7;
  FICAA3.Field.CfgRegInstID = (UINT32) instanceId;
  PciWrite32 (PciAddr.AddressValue, FICAA3.Value);

  PciAddr.Address.Function = FICAD3_LO_FUNC;
  PciAddr.Address.Register = FICAD3_LO_REG;
  RegisterValue = PciRead32 (PciAddr.AddressValue);

  //Restore FICAA3 register value
  PciAddr.Address.Function = FICAA3_FUNC;
  PciAddr.Address.Register = FICAA3_REG;
  PciWrite32 (PciAddr.AddressValue, Ficaa3Save);

  return RegisterValue;
}

/*------------------------------------------------------------------
 Function: getDfRegSystemFabricIdMask0
 Purpose: Get the DF::SystemFabricIdMask register
 Inputs: The DF type
 Outputs: The requested register from node 0 (all nodes will be the same)
 *------------------------------------------------------------------*/
UINT32
getDfRegSystemFabricIdMask0 (
  DfType *dfType
  )
{
  UINT32 addr = 0;

  switch (dfType->dfVersion)
  {
    case DF_TYPE_DF2:
    case DF_TYPE_DF3:
      addr = DF__SYSFABIDMASK0_REGADDR_DF2_AND_DF3;
      break;
    case DF_TYPE_DF3POINT5:
      addr = DF__SYSFABIDMASK0_REGADDR_DF3POINT5;
      break;
    case DF_TYPE_DF4:
    case DF_TYPE_DF4POINT5:
      addr = DF__SYSFABIDMASK0_REGADDR_DF4;
      break;
    default:
      ASSERT (FALSE);
  }
  return (getDfReg (dfType, BROADCAST_ACCESS, 0, addr));
}

/*------------------------------------------------------------------
 Function: getDfRegSystemFabricIdMask1
 Purpose: Get the DF::SystemFabricIdMask1register
 Inputs: The DF type
 Outputs: The requested register from node 0 (all nodes will be the same)
 *------------------------------------------------------------------*/
UINT32
getDfRegSystemFabricIdMask1(
  DfType *dfType
  )
{
  UINT32 addr = 0;

  switch (dfType->dfVersion)
  {
    case DF_TYPE_DF2:
      ASSERT (FALSE);
      break;
    case DF_TYPE_DF3:
      addr = DF__SYSFABIDMASK1_REGADDR_DF3;
      break;
    case DF_TYPE_DF3POINT5:
      addr = DF__SYSFABIDMASK1_REGADDR_DF3POINT5;
      break;
    case DF_TYPE_DF4:
    case DF_TYPE_DF4POINT5:
      addr = DF__SYSFABIDMASK1_REGADDR_DF4;
      break;
    default:
      ASSERT (FALSE);
      break;
  }
  return (getDfReg (dfType, BROADCAST_ACCESS, 0, addr));
}

/*------------------------------------------------------------------
 Function: getDfRegSystemFabricIdMask2
 Purpose: Get the DF::SystemFabricIdMask2register
 Inputs: The DF type
 Outputs: The requested register from node 0 (all nodes will be the same)
 *------------------------------------------------------------------*/
UINT32
getDfRegSystemFabricIdMask2 (
  DfType *dfType
  )
{
  UINT32 addr = 0;

  switch (dfType->dfVersion)
  {
    case DF_TYPE_DF2:
    case DF_TYPE_DF3:
      ASSERT (FALSE);
      break;
    case DF_TYPE_DF3POINT5:
      addr = DF__SYSFABIDMASK2_REGADDR_DF3POINT5;
      break;
    case DF_TYPE_DF4:
    case DF_TYPE_DF4POINT5:
      addr = DF__SYSFABIDMASK2_REGADDR_DF4;
      break;
    default:
      ASSERT (FALSE);
  }
  return (getDfReg (dfType, BROADCAST_ACCESS, 0, addr));
}

/*------------------------------------------------------------------
 Function: getComponentIdMask
 Purpose: Get the mask of the component ID **within a FabricID**
          Example, if bits 5:0 of a FabricID is componentID, this will return 0x3F
 Inputs: DF type
 Outputs: A mask to apply to the FabricID to get just the component ID bits
 *------------------------------------------------------------------*/
UINT32
getComponentIdMask (
  DfType *dfType
  )
{
  if (savedComponentIdMask < 0)
  {
    switch (dfType->dfVersion)
    {
      case DF_TYPE_DF2:
        savedComponentIdMask = ((~(getSocketIdMask (dfType) | getDieIdMask (dfType))) & 0xFF);
        break;
      case DF_TYPE_DF3:
      case DF_TYPE_DF3POINT5:
      case DF_TYPE_DF4:
      case DF_TYPE_DF4POINT5:
        // No need to special case the Stones componentIDMask to be 0xFF since the "missing" dieID bit
        // will always be zero so it does not matter if it is part of the component mask or not.
        savedComponentIdMask = getBits (DF__COMPONENT_ID_MASK_BITPOS_LO_DF3, DF__COMPONENT_ID_MASK_BITPOS_HI_DF3,
                               getDfRegSystemFabricIdMask0 (dfType));
        break;
      default:
        ASSERT (FALSE);
    }
  }
  return (savedComponentIdMask);
}

/*------------------------------------------------------------------
 Function: getNodeIdShift
 Purpose: Get the shift (LSB) of the node ID **within a FabricID**
          Example, if bits 7:6 of a FabricID is nodeID, this will return 0xC0
 Inputs: DF type
 Outputs: A mask to apply to the FabricID to get just the node ID bits
   Note: Within DF registers, these fields are based off of the LSB
         of the NodeID (varies per variant). This function will normalize
         these to FabricID[0]
 *------------------------------------------------------------------*/
UINT32
getNodeIdShift (
  DfType *dfType
  )
{
  UINT32 deviceAndVendorId;
  if (savedNodeIdShift < 0)
  {
    switch (dfType->dfVersion)
    {
      case DF_TYPE_DF2:
        // not directly available from registers, but it is the same as the die ID shift
        savedNodeIdShift = getDieIdShift (dfType);
        break;
      case DF_TYPE_DF3:
      case DF_TYPE_DF3POINT5:
      case DF_TYPE_DF4:
      case DF_TYPE_DF4POINT5:
        savedNodeIdShift = getBits (DF__NODE_ID_SHIFT_BITPOS_LO_DF3, DF__NODE_ID_SHIFT_BITPOS_HI_DF3,
                           getDfRegSystemFabricIdMask1 (dfType));
        // Check for Stones special case
        if (savedNodeIdShift == 7)
        {
          deviceAndVendorId = getDfReg (dfType, BROADCAST_ACCESS, 0, DF__DEVICEID_REGADDR);
          if (deviceAndVendorId == STONES_DF_DEVICE_AND_VENDOR_ID)
          {
            savedNodeIdShift = 8; // Effective nodeID shift is 8 in Stones due to missing dieID bit
          }
        }
        break;
      default:
        ASSERT (FALSE);
    }
  }
  return (savedNodeIdShift);
}

/*------------------------------------------------------------------
 Function: getNodeIdMask
 Purpose: Get the mask of the node ID **within a FabricID**
          Example, if bits 7:6 of a FabricID is nodeID, this will return 0xC0
 Inputs: DF type
 Outputs: A mask to apply to the FabricID to get just the node ID bits
   Note: Within DF registers, these fields are based off of the LSB
         of the NodeID (varies per variant). This function will normalize
         these to FabricID[0]
 *------------------------------------------------------------------*/
UINT32
getNodeIdMask (
  DfType *dfType
  )
{
  UINT32 deviceAndVendorId;
  if (savedNodeIdMask < 0)
  {
    switch (dfType->dfVersion)
    {
      case DF_TYPE_DF2:
        // not directly available from registers, but it is the same as the die ID shift
        savedNodeIdMask = getSocketIdMask (dfType) | getDieIdMask (dfType);
        break;
      case DF_TYPE_DF3:
      case DF_TYPE_DF3POINT5:
      case DF_TYPE_DF4:
      case DF_TYPE_DF4POINT5:
        savedNodeIdMask = getBits (DF__NODE_ID_MASK_BITPOS_LO_DF3, DF__NODE_ID_MASK_BITPOS_HI_DF3,
                          getDfRegSystemFabricIdMask0 (dfType));
        // Check for Stones special case
        if (savedNodeIdMask == 0xF80)
        {
          deviceAndVendorId = getDfReg (dfType, BROADCAST_ACCESS, 0, DF__DEVICEID_REGADDR);
          if (deviceAndVendorId == STONES_DF_DEVICE_AND_VENDOR_ID)
          {
            savedNodeIdMask = 0x100; // Effective nodeID mask is 0x100 in Stones due to missing dieID bit
          }
        }
        break;
      default:
        ASSERT (FALSE);
    }
  }
  return (savedNodeIdMask);
}

/*------------------------------------------------------------------
 Function: getSocketIdShift
 Purpose: Get the shift (LSB) of the socket ID **within a FabricID**
          Example, if bits 7:6 of a FabricID is socketID, this will return 6
 Inputs: DF type
 Outputs: The LSB of socket ID
   Note: Within DF registers, these fields are based off of the LSB
         of the NodeID (varies per variant). This function will normalize
         these to FabricID[0]
 *------------------------------------------------------------------*/
UINT32
getSocketIdShift (
  DfType *dfType
  )
{
  UINT32 deviceAndVendorId = 0;
  if (savedSocketIdShift < 0)
  {
    switch (dfType->dfVersion)
    {
      case DF_TYPE_DF2:
        savedSocketIdShift = getBits (DF__SOCKET_ID_SHIFT_BITPOS_LO_DF2, DF__SOCKET_ID_SHIFT_BITPOS_HI_DF2,
                             getDfRegSystemFabricIdMask0 (dfType));
        break;
      case DF_TYPE_DF3:
      case DF_TYPE_DF3POINT5:
      case DF_TYPE_DF4:
      case DF_TYPE_DF4POINT5:
        savedSocketIdShift = getBits (DF__SOCKET_ID_SHIFT_BITPOS_LO_DF3, DF__SOCKET_ID_SHIFT_BITPOS_HI_DF3,
                             getDfRegSystemFabricIdMask1 (dfType));
        // Check for Stones special case
        if (savedSocketIdShift == 1)
        {
          deviceAndVendorId = getDfReg (dfType, BROADCAST_ACCESS, 0, DF__DEVICEID_REGADDR);
          if (deviceAndVendorId == STONES_DF_DEVICE_AND_VENDOR_ID)
          {
            savedSocketIdShift = 0; // getNodeIdShift will already take into account the missing dieID bit.
          }
        }
        savedSocketIdShift += getNodeIdShift (dfType);
        break;
      default:
        ASSERT (FALSE);
    }
  }
  return (savedSocketIdShift);
}

/*------------------------------------------------------------------
 Function: getSocketIdMask
 Purpose: Get the mask of the socket ID **within a FabricID**
          Example, if bits 7:6 of a FabricID is socketID, this will return 0xC0
 Inputs: DF type
 Outputs: A mask to apply to the FabricID to get just the socket ID bits
   Note: Within DF registers, these fields are based off of the LSB
         of the NodeID (varies per variant). This function will normalize
         these to FabricID[0]
 *------------------------------------------------------------------*/
UINT32
getSocketIdMask (
  DfType *dfType
  )
{
  UINT32 deviceAndVendorId;
  if (savedSocketIdMask < 0)
  {
    switch (dfType->dfVersion)
    {
      case DF_TYPE_DF2:
        savedSocketIdMask = getBits (DF__SOCKET_ID_MASK_BITPOS_LO_DF2, DF__SOCKET_ID_MASK_BITPOS_HI_DF2,
                            getDfRegSystemFabricIdMask0 (dfType));
        break;
      case DF_TYPE_DF3:
        savedSocketIdMask = getBits (DF__SOCKET_ID_MASK_BITPOS_LO_DF3, DF__SOCKET_ID_MASK_BITPOS_HI_DF3,
                            getDfRegSystemFabricIdMask1 (dfType));
        savedSocketIdMask = savedSocketIdMask << getNodeIdShift (dfType);
        break;
      case DF_TYPE_DF3POINT5:
      case DF_TYPE_DF4:
      case DF_TYPE_DF4POINT5:
        savedSocketIdMask = getBits (DF__SOCKET_ID_MASK_BITPOS_LO_DF4, DF__SOCKET_ID_MASK_BITPOS_HI_DF4,
                            getDfRegSystemFabricIdMask2 (dfType));
        // Check for Stones special case
        if (savedSocketIdMask == 0x1E)
        {
          deviceAndVendorId = getDfReg (dfType, BROADCAST_ACCESS, 0, DF__DEVICEID_REGADDR);
          if (deviceAndVendorId == STONES_DF_DEVICE_AND_VENDOR_ID)
          {
            savedSocketIdMask = 1; // SocketID Mask is just 1 due to the missing dieID bit.
          }
        }
        savedSocketIdMask = savedSocketIdMask << getNodeIdShift (dfType);
        break;
      default:
        ASSERT (FALSE);
    }
  }
  return (savedSocketIdMask);
}

/*------------------------------------------------------------------
 Function: getDieIdShift
 Purpose: Get the shift (LSB) of the die ID **within a FabricID**
          Example, if bits 7:6 of a FabricID is dieID, this will return 0xC0
 Inputs: DF type
 Outputs: A mask to apply to the FabricID to get just the die ID bits
   Note: Within DF registers, these fields are based off of the LSB
         of the NodeID (varies per variant). This function will normalize
         these to FabricID[0]
 *------------------------------------------------------------------*/
UINT32
getDieIdShift (
  DfType *dfType
  )
{
  if (savedDieIdShift < 0)
  {
    switch (dfType->dfVersion)
    {
      case DF_TYPE_DF2:
        savedDieIdShift = getBits (DF__DIE_ID_SHIFT_BITPOS_LO_DF2, DF__DIE_ID_SHIFT_BITPOS_HI_DF2,
                          getDfRegSystemFabricIdMask0 (dfType));
        break;
      case DF_TYPE_DF3:
      case DF_TYPE_DF3POINT5:
      case DF_TYPE_DF4:
      case DF_TYPE_DF4POINT5:
        // not directly available from registers, but it is the same as the node ID shift
        savedDieIdShift = getNodeIdShift (dfType);
        break;
      default:
        ASSERT (FALSE);
    }
  }
  return (savedDieIdShift);
}

/*------------------------------------------------------------------
 Function: getDieIdMask
 Purpose: Get the mask of the die ID **within a FabricID**
          Example, if bits 7:6 of a FabricID is dieID, this will return 0xC0
 Inputs: DF type
 Outputs: A mask to apply to the FabricID to get just the die ID bits
   Note: Within DF registers, these fields are based off of the LSB
         of the NodeID (varies per variant). This function will normalize
         these to FabricID[0]
 *------------------------------------------------------------------*/
UINT32
getDieIdMask (
  DfType *dfType
  )
{
  UINT32 deviceAndVendorId;
  if (savedDieIdMask < 0)
  {
    switch (dfType->dfVersion)
    {
      case DF_TYPE_DF2:
        savedDieIdMask = getBits (DF__DIE_ID_MASK_BITPOS_LO_DF2, DF__DIE_ID_MASK_BITPOS_HI_DF2,
                         getDfRegSystemFabricIdMask0 (dfType));
        break;
      case DF_TYPE_DF3:
        savedDieIdMask = getBits (DF__DIE_ID_MASK_BITPOS_LO_DF3, DF__DIE_ID_MASK_BITPOS_HI_DF3,
                         getDfRegSystemFabricIdMask1 (dfType));
        savedDieIdMask = savedDieIdMask << getNodeIdShift (dfType);
        break;
      case DF_TYPE_DF3POINT5:
      case DF_TYPE_DF4:
      case DF_TYPE_DF4POINT5:
        savedDieIdMask = getBits (DF__DIE_ID_MASK_BITPOS_LO_DF4, DF__DIE_ID_MASK_BITPOS_HI_DF4,
                         getDfRegSystemFabricIdMask2 (dfType));
        // Check for Stones special case
        if (savedDieIdMask == 1)
        {
          deviceAndVendorId = getDfReg (dfType, BROADCAST_ACCESS, 0, DF__DEVICEID_REGADDR);
          if (deviceAndVendorId == STONES_DF_DEVICE_AND_VENDOR_ID)
          {
            savedDieIdMask = 0; // SocketID Mask is just 1 due to the missing dieID bit.
          }
        }
        savedDieIdMask = savedDieIdMask << getNodeIdShift (dfType);
        break;
      default:
        ASSERT (FALSE);
    }
  }
  return (savedDieIdMask);
}

/*------------------------------------------------------------------
 Function: getNumAddressMaps
 Purpose: Returns the number of address maps in the hardware address space
 Inputs: DF type
 Outputs: The number of available address maps
          These do not have to be "instantiated" on a particular SOC
          As they can be reserved and will always return zero.
 *------------------------------------------------------------------*/
UINT32
getNumAddressMaps (
  DfType *dfType
  )
{
  switch (dfType->dfVersion)
  {
    case DF_TYPE_DF2:
    case DF_TYPE_DF3:
      return (DF__NUM_DRAM_MAPS_AVAILABLE_PRE_DF4);
    case DF_TYPE_DF3POINT5:
      // Include an extra one for the "mega" map
      return (DF__NUM_DRAM_MAPS_AVAILABLE_PRE_DF4 + 1);
    case DF_TYPE_DF4:
      return (DF__NUM_DRAM_MAPS_AVAILABLE_DF4);
    case DF_TYPE_DF4POINT5:
      if (dfType->isHeterogeneous || dfType->isDGpu)
      {
        return (44);
      }
      return (DF__NUM_DRAM_MAPS_AVAILABLE_DF4POINT5);
    default:
      ASSERT (0);
  }
  return (0);
}
/*------------------------------------------------------------------
 Function: getNumCsMaps
 Purpose: Returns the number of CS address maps in the hardware address space
 Inputs: DF type
 Outputs: The number of available address maps
          These do not have to be "instantiated" on a particular SOC
          As they can be reserved and will always return zero.
 *------------------------------------------------------------------*/
UINT32 getNumCsMaps (
  DfType *dfType
  )
{
  switch (dfType->dfVersion)
  {
    case DF_TYPE_DF2:
    case DF_TYPE_DF3:
    case DF_TYPE_DF3POINT5:
      return (2);
    case DF_TYPE_DF4:
    case DF_TYPE_DF4POINT5:
      if (dfType->isHeterogeneous || dfType->isDGpu)
      {
        return (3);
      }
      return (4);
    default:
      ASSERT (0);
  }
  return (0);
}
/*------------------------------------------------------------------
 Function: getDfRegFabricBlkInstanceCnt
 Purpose: Get the DF::FabricBlockInstanceCount register
 Inputs: DF type and a nodeID (for the PCIe bus/device)
 Outputs: The requested register
 *------------------------------------------------------------------*/
UINT32
getDfRegFabricBlkInstanceCnt (
  DfType *dfType,
  UINT32 nodeId
  )
{

  return (getDfReg (dfType, BROADCAST_ACCESS, nodeId, DF__FABBLKINSTCNT_REGADDR));
}

/*------------------------------------------------------------------
 Function: getDfRegFabricBlkInstInfo0
 Purpose: Get the DF::FabricBlockInstanceInformation0 register
 Inputs: DF type, an instanceID (CS) and a nodeID (for the PCIe bus/device)
 Outputs: The requested register
 *------------------------------------------------------------------*/
UINT32
getDfRegFabricBlkInstInfo0 (
  DfType *dfType,
  UINT32 instanceId,
  UINT32 nodeId
  )
{

  return (getDfReg (dfType, instanceId, nodeId, DF__FABBLKINFO0_REGADDR));
}

/*------------------------------------------------------------------
 Function: getDfRegFabricBlkInstInfo3
 Purpose: Get the DF::FabricBlockInstanceInformation3 register
 Inputs: DF type, an instanceID (CS) and a nodeID (for the PCIe bus/device)
 Outputs: The requested register
 *------------------------------------------------------------------*/
UINT32
getDfRegFabricBlkInstInfo3 (
  DfType *dfType,
  UINT32 instanceId,
  UINT32 nodeId
  )
{

  return (getDfReg (dfType, instanceId, nodeId, DF__FABBLKINFO3_REGADDR));
}

/*------------------------------------------------------------------
 Function: getIntlvHashCtlBits
 Purpose: Get the three bits to control interleave hashing, right justified
 Inputs: DF type and the address map
 Outputs:
   bit0: HASH_INTLV_CTL_64K
   bit1: HASH_INTLV_CTL_2M
   bit2: HASH_INTLV_CTL_1G
 *------------------------------------------------------------------*/
UINT32
getIntlvHashCtlBits (
  DfType *dfType,
  UINT32 *dramAddressMapRegs
  )
{
  UINT32 dfGlobalCtrlReg;

  // MI200 is an odd exception to this, as it has DramIntlvLocHashCtl0 and DramIntlvLocHashCtl1 registers
  // with individual enable bits for each one. But MI200 still has DfGlobalCtrl enabling them (for the CS),
  // and the only supported case is one where this is the same for all registers. So we do not have to
  // check DramIntlvLocHashCtl0/DramIntlvLocHashCtl1 at all.
  if (dfType->dfVersion < DF_TYPE_DF4)
  {
    dfGlobalCtrlReg = getDfReg (dfType, BROADCAST_ACCESS, 0, DF__DFGLOBALCTRL_REGADDR);
    dfGlobalCtrlReg = (dfGlobalCtrlReg >> DF__HASH_INTLV_CTL_64K_BITPOS_DF3);
    dfGlobalCtrlReg &= 7;
  } else if (dfType->dfVersion == DF_TYPE_DF4)
  {
    dfGlobalCtrlReg = (dramAddressMapRegs[ADDR_MAP_ARRAY_CTL_OFFSET] >> DF__HASH_INTLV_CTL_64K_BITPOS_DF4);
    dfGlobalCtrlReg &= 7;
  } else
  {
    dfGlobalCtrlReg = (dramAddressMapRegs[ADDR_MAP_ARRAY_CTL_OFFSET] >> DF__HASH_INTLV_CTL_64K_BITPOS_DF4);
    // HashIntLvCtl4K is put in bit 3
    dfGlobalCtrlReg |= ((dramAddressMapRegs[ADDR_MAP_ARRAY_CTL_OFFSET] >> 7) & 1) << 3;
    // HashIntLvCtl1T is put in bit 4
    dfGlobalCtrlReg |= ((dramAddressMapRegs[ADDR_MAP_ARRAY_CTL_OFFSET] >> 15) & 1) << 4;
    dfGlobalCtrlReg &= 31;
  }
  return (dfGlobalCtrlReg);
}

/*------------------------------------------------------------------
 Function: determineDfType
 Purpose: Determine if the DF type is DF2 or DF3, DF3.5 (Trento/MI200), or DF4
 Inputs: Pointer to structure
 Outputs: Structure is filled out
 Algorithm:
  1) Read F0x40 (FabricBlockInstanceCount). Bits 27:24 specify the MajorRevision,
     but this was added in DF4. Prior to DF4, this field is always zero.
  2) If bits F0x40[27:24] != 0, then MajorRevision indicates DF4 (done)
     2a) Then examine MinorRevision
     2b) If MinorRevision < 5, then DF4
     2c) Else DF4.5
  3) To determine pre-DF4...
  4) Read F1x150. SystemFabricIdMask0 was moved in Shoreline components (Trento and MI200)
     F1x150[15:0] is the ComponentIdMask and at least some LSB bits must be non-zero.
     Prior to Trento/MI200, F1x150 was reserved.
  5) If F1x150[7:0] != 0, then this is "DF3.5" (done)
  6) To determine DF3 vs DF2...
  7) Read F1x208. In DF3, this is the ComponentIdMask and that must be non-zero.
     In DF2, F1x208[7:0] was reserved.
  8) If F1x208[7:0] is non-zero, this is DF3 done)
  9) Else it is DF2 (done).
  We can do all this on node 0, since there will always be a node 0 and all
  nodes will be the same.

  Note that all of the accesses here must be done as "broadcast accesses" because
  even the format of the indirect access registers is variant specific.
 *------------------------------------------------------------------*/
VOID
determineDfType (
  DfType *dfType
  )
{
  UINT32 rev;
  UINT32 data;
  UINT32 i;
  UINT32 numDFInstances;
  UINT32 regData;
  BOOLEAN ccmFound;
  data = getDfRegFabricBlkInstanceCnt (dfType, 0);

  //Initialize dfType struct
  dfType->dfVersion = DF_TYPE_UNKNOWN;
  dfType->isDGpu = FALSE;
  dfType->isHeterogeneous = FALSE;
  rev = getBits (DF__MAJOR_REVISION_BITPOS_LO, DF__MAJOR_REVISION_BITPOS_HI, data);
  if (rev != 0)
  {
    ASSERT (rev == 4);
    rev = getBits (DF__MINOR_REVISION_BITPOS_LO, DF__MINOR_REVISION_BITPOS_HI, data);
    if (rev < 5)
    {
      dfType->dfVersion = DF_TYPE_DF4;
    } else
    {
      dfType->dfVersion = DF_TYPE_DF4POINT5;
    }
  } else  if ((getDfReg (dfType, BROADCAST_ACCESS, 0, DF__SYSFABIDMASK0_REGADDR_DF3POINT5) & 0xFF) != 0)
  {
    dfType->dfVersion = DF_TYPE_DF3POINT5;
  } else if ((getDfReg (dfType, BROADCAST_ACCESS, 0, DF__SYSFABIDMASK0_REGADDR_DF2_AND_DF3) & 0xFF) != 0)
  {
    dfType->dfVersion = DF_TYPE_DF3;
  } else
  {
    dfType->dfVersion = DF_TYPE_DF2;
  }
  // Determine if the system is a DGPU by looking for CCMs.
  numDFInstances = getBits (DF__BLOCK_INSTANCE_COUNT_BITPOS_LO, DF__BLOCK_INSTANCE_COUNT_BITPOS_HI, getDfRegFabricBlkInstanceCnt (dfType, 0));
  ccmFound = FALSE;
  for (i = 0; i < numDFInstances; i++)
  {
    regData = getDfRegFabricBlkInstInfo0 (dfType, i, 0);
    // Skip gated blocks (detected because at least one bit must be non-zero in non-gated blocks)
    if (regData == 0)
    {
      continue;
    }
    if (getBits (DF__INSTANCE_TYPE_BITPOS_LO, DF__INSTANCE_TYPE_BITPOS_HI, regData) == DF__CCM_INSTANCE_TYPE_VALUE)
    {
      ccmFound = TRUE;
      break;
    }
  }
  dfType->isDGpu = !ccmFound;
  // Heterogeneous is:
  // 1) DF3.5 (MI200/Bergamo) and not dGPU - covers the Shoreline system
  // 2) MI300 - detected by device/vendor ID.
  //    Note that we set this regardless of the MI300 mode.
  if ((dfType->dfVersion == DF_TYPE_DF3POINT5) && !dfType->isDGpu)
  {
    dfType->isHeterogeneous = TRUE;
  }
  if (dfType->dfVersion == DF_TYPE_DF4POINT5)
  {
    regData = getDfReg (dfType, BROADCAST_ACCESS, 0, DF__DEVICEID_REGADDR);
    if (regData == MI300_DEVICE_VENDOR_ID)
    {
      dfType->isHeterogeneous = TRUE;
    }
  }

  return;
}
/*------------------------------------------------------------------
 Function: getDfRegDramOffset
 Purpose: Get the DF::DramOffset[n] register
 Inputs: The DF type, an instanceID (CS), a nodeID (for the PCIe bus/device)
   and a register number
 Outputs: The requested register
 *------------------------------------------------------------------*/
UINT32
getDfRegDramOffset (
  DfType *dfType,
  UINT32 instanceId,
  UINT32 nodeId,
  UINT32 regNum
  )
{
  UINT32 addr = 0;

  // There is no actual DramOffset_n0. This is treated as if it is zero for code simplicity
  // (which is what the HW does as well).
  if (regNum == 0)
  {
    return (0);
  }
  ASSERT (regNum < getNumCsMaps (dfType));

  if (dfType->dfVersion < DF_TYPE_DF4)
  {
    addr = DF__DRAMOFFSET0_REGADDR_PRE_DF4;
  } else
  {
    addr = DF__DRAMOFFSET0_REGADDR_DF4;
  }
  addr += (4 * regNum);
  return (getDfReg (dfType, instanceId, nodeId, addr));
}

/*------------------------------------------------------------------
 Function: extractDramOffset
 Purpose: Decodes the DRAM offset register into a normalized address
 Inputs: The DF type, and the register
   and a register number
 Outputs: The requested register
 *------------------------------------------------------------------*/
UINT64
extractDramOffset (
  DfType *dfType,
  UINT32 dramOffsetReg
  )
{
  UINT64 hiAddrOffset = 0;
  UINT32 shift = 0;

  if (dfType->dfVersion == DF_TYPE_DF2)
  {
    hiAddrOffset = getBits (DF__HI_ADDR_OFFSET_BITPOS_LO_DF2, DF__HI_ADDR_OFFSET_BITPOS_HI_DF2, dramOffsetReg);
    shift = DF__LSB_ADDR_BIT_IN_DRAM_OFFSET;
  } else if ((dfType->dfVersion == DF_TYPE_DF3) || (dfType->dfVersion == DF_TYPE_DF3POINT5))
  {
    hiAddrOffset = getBits (DF__HI_ADDR_OFFSET_BITPOS_LO_DF3, DF__HI_ADDR_OFFSET_BITPOS_HI_DF3, dramOffsetReg);
    shift = DF__LSB_ADDR_BIT_IN_DRAM_OFFSET;
  } else if (dfType->dfVersion == DF_TYPE_DF4)
  {
    hiAddrOffset = getBits (DF__HI_ADDR_OFFSET_BITPOS_LO_DF4, DF__HI_ADDR_OFFSET_BITPOS_HI_DF4, dramOffsetReg);
    shift = DF__LSB_ADDR_BIT_IN_DRAM_OFFSET;
  } else if ((dfType->dfVersion == DF_TYPE_DF4POINT5) && !dfType->isHeterogeneous)
  {
    hiAddrOffset = getBits (DF__HI_ADDR_OFFSET_BITPOS_LO_DF4, DF__HI_ADDR_OFFSET_BITPOS_HI_DF4, dramOffsetReg);
    shift = DF__LSB_ADDR_BIT_IN_DRAM_OFFSET;
  } else if ((dfType->dfVersion == DF_TYPE_DF4POINT5) && (dfType->isHeterogeneous))
  {
    hiAddrOffset = getBits (DF__HI_ADDR_OFFSET_BITPOS_LO_DF4, DF__HI_ADDR_OFFSET_BITPOS_HI_DF4, dramOffsetReg);
    shift = DF__MI300_LSB_ADDR_BIT_IN_DRAM_OFFSET;
  } else
  {
    ASSERT (FALSE);
  }
  hiAddrOffset = hiAddrOffset << shift;
  return (hiAddrOffset);
}

/*------------------------------------------------------------------
 Function: getCxlAddressMap
 Purpose: Get the DF address map array
 Inputs: The DF type, an instanceID (CS), a nodeID (for the PCIe bus/device)
   and a map instance number (0 through 3)
 Outputs: This function will populate the "CXL address map array"
          with the requested map. The array entries are the same
          as the DRAM map (even though there isn't exactly the same
          number of maps) for simplicity.
 *------------------------------------------------------------------*/
VOID getCxlAddressMap (
  DfType *dfType,
  INT32 instanceId,
  UINT32 nodeId,
  UINT32 mapNumber,
  UINT32 *cxlAddressMapRegs
  )
{
  UINT32 addr;
  UINT32 i;

  ASSERT (dfType->dfVersion >= DF_TYPE_DF4);
  ASSERT (instanceId != BROADCAST_ACCESS);
  ASSERT (mapNumber < DF__NUM_CXL_MAPS_AVAILABLE);

  // initialize the values
  for (i = 0; i < ADDR_MAP_ARRAYSIZE; i++)
  {
    cxlAddressMapRegs[i] = 0;
  }

  addr = DF__CXLBASE0_REGADDR_DF4;
  addr += (mapNumber * 16);
  cxlAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET] = getDfReg (dfType, instanceId, nodeId, addr);
  cxlAddressMapRegs[ADDR_MAP_ARRAY_LIMIT_OFFSET] = getDfReg (dfType, instanceId, nodeId, addr + 4);
  cxlAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET] = getDfReg (dfType, instanceId, nodeId, addr + 8);

  return;
}

/*------------------------------------------------------------------
 Function: getDramAddressMap
 Purpose: Get the DF address map array to be handed around in these
          functions so that it is not continually refetched.
 Inputs: The DF type, an instanceID (CS), a nodeID (for the PCIe bus/device)
   and a map instance number (0 through 15)
 Outputs: This function will populate the "DRAM address map array"
          with the requested map. In DF3, the global interleave control
          bits will be fetched as well, allowing the code to treat the
          two similarly. In addition, the "CS target remap" registers
          will be brought in only when they are valid. When they are
          not valid or remapping is not enabled, the map registers are
          setup as "unity". This is mainly to simplify the logic and
          keep it common across all variants.
 *------------------------------------------------------------------*/
VOID
getDramAddressMap (
  DfType *dfType,
  INT32  instanceId,
  UINT32 nodeId,
  UINT32 mapNumber,
  UINT32 *dramAddressMapRegs
  )
{
  UINT32 addr;
  UINT32 remapSel;
  UINT32 dstFabricId;
  UINT32 i;

  ASSERT (instanceId != BROADCAST_ACCESS);
  ASSERT (mapNumber < getNumAddressMaps (dfType));

  // initialize the values
  for (i = 0; i < (ADDR_MAP_ARRAYSIZE - 2); i++)
  {
    dramAddressMapRegs[i] = 0;
  }
  dramAddressMapRegs[ADDR_MAP_ARRAY_NP2_OFFSET] = 0xFFFFFFFF; // not used
  dramAddressMapRegs[ADDR_MAP_ARRAY_REMAP0_OFFSET] = 0x76543210; // unity
  dramAddressMapRegs[ADDR_MAP_ARRAY_REMAP1_OFFSET] = 0xFEDCBA98; // unity
  if ((dfType->dfVersion == DF_TYPE_DF3POINT5) && (mapNumber == DF__NUM_DRAM_MAPS_AVAILABLE_PRE_DF4))
  {
    // This is a request to fetch the "mega" address map. In DF3.5, this is only used for MI200 dGPU.
    // (it is used in the MI200 for heterogeneous systems too, but we only read Trento registers for heterogeneous)
    dramAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET] = getDfReg (dfType, instanceId, nodeId, DF__DRAMMEGABASE_REGADDR_PRE_DF4);
    dramAddressMapRegs[ADDR_MAP_ARRAY_LIMIT_OFFSET] = getDfReg (dfType, instanceId, nodeId, DF__DRAMMEGALIMIT_REGADDR_PRE_DF4);
    // Place the DF3.5 "Log2DieAddr64KSpzce" in the same place as it would be in DF4.
    dramAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET] = getDfReg (dfType, instanceId, nodeId, DF__DRAMMEGACONTROL_REGADDR_PRE_DF4) << 24;
    // Pretend to set the Mega bit in the (otherwise empty) DramAddressCtl register
    dramAddressMapRegs[ADDR_MAP_ARRAY_CTL_OFFSET] = 0x8;
  }
  if (dfType->dfVersion < DF_TYPE_DF4)
  {
    addr = DF__DRAMBASE0_REGADDR_PRE_DF4;
    addr += (mapNumber * 8);
    dramAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET] = getDfReg (dfType, instanceId, nodeId, addr);
    dramAddressMapRegs[ADDR_MAP_ARRAY_LIMIT_OFFSET] = getDfReg (dfType, instanceId, nodeId, addr + 4);
    // We treat the NP2_CHANNEL_CONFIG and REMAP in DF3 as an extension to the DF maps
    // to keep the code the same between DF3 and DF4
    // Warning: These registers do not exist prior to GN, so you cannot read them.
    //          To avoid this, we only read the registers if the interleave mode is 6-channel
    //          (which can not be enabled prior to GN either), and force the registers
    //          to look like unity.
    if (decodeDramIntLvNumChan (dfType, dramAddressMapRegs) == INTERLEAVE_MODE_DF3_6CHAN)
    {
      dstFabricId = extractDstFabricId (dfType, dramAddressMapRegs) & getNodeIdMask (dfType);
      // Read the correct CS target remap register based on DstFabricId
      if ((dstFabricId & getSocketIdMask (dfType)) == 0)
      {
        dramAddressMapRegs[ADDR_MAP_ARRAY_REMAP0_OFFSET] = getDfReg (dfType, BROADCAST_ACCESS, nodeId, DF__SKT0CSTARGETREMAP0_REGADDR);
      } else
      {
        dramAddressMapRegs[ADDR_MAP_ARRAY_REMAP0_OFFSET] = getDfReg (dfType, BROADCAST_ACCESS, nodeId, DF__SKT1CSTARGETREMAP0_REGADDR);
      }
      // Must read NP2_CHANNEL_CONFIG on the nodeID from the DstFabricID
      // (you may be reading the DRAM base registers from a CCM on node 0
      //  but then find the DstFabricID to be on node 1. Since the log2Addr64K
      //  is only available in CS, there is no guarantee that it is programmed on
      //  node 0 (or programmed to the same value)
      nodeId = dstFabricId & getNodeIdMask (dfType) >> getNodeIdShift (dfType);
      dramAddressMapRegs[ADDR_MAP_ARRAY_NP2_OFFSET] = getDfReg (dfType, BROADCAST_ACCESS, nodeId, DF__NP2CHANNELCONFIG_REGADDR);
    }
  } else if (dfType->dfVersion == DF_TYPE_DF4)
  {
    addr = DF__DRAMBASE0_REGADDR_DF4;
    addr += (mapNumber * 16);
    dramAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET] = getDfReg (dfType, instanceId, nodeId, addr);
    dramAddressMapRegs[ADDR_MAP_ARRAY_LIMIT_OFFSET] = getDfReg (dfType, instanceId, nodeId, addr + 4);
    dramAddressMapRegs[ADDR_MAP_ARRAY_CTL_OFFSET] = getDfReg (dfType, instanceId, nodeId, addr + 8);
    dramAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET] = getDfReg (dfType, instanceId, nodeId, addr + 12);
    if (getBit (DF__REMAP_EN_BITPOS_DF4, dramAddressMapRegs[ADDR_MAP_ARRAY_CTL_OFFSET]))
    {
      remapSel = getBits (DF__REMAP_SEL_BITPOS_LO_DF4, DF__REMAP_SEL_BITPOS_HI_DF4, dramAddressMapRegs[ADDR_MAP_ARRAY_CTL_OFFSET]);
      addr = DF__CSTARGETREMAP0A_REGADDR_DF4 + (remapSel * 8);
      dramAddressMapRegs[ADDR_MAP_ARRAY_REMAP0_OFFSET] = getDfReg (dfType, instanceId, nodeId, addr);
      dramAddressMapRegs[ADDR_MAP_ARRAY_REMAP1_OFFSET] = getDfReg (dfType, instanceId, nodeId, addr + 4);
    }
  } else if (dfType->dfVersion == DF_TYPE_DF4POINT5)
  {
    addr = DF__DRAMBASE0_REGADDR_DF4POINT5;
    addr += (mapNumber * 16);
    dramAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET] = getDfReg (dfType, instanceId, nodeId, addr);
    dramAddressMapRegs[ADDR_MAP_ARRAY_LIMIT_OFFSET] = getDfReg (dfType, instanceId, nodeId, addr + 4);
    dramAddressMapRegs[ADDR_MAP_ARRAY_CTL_OFFSET] = getDfReg (dfType, instanceId, nodeId, addr + 8);
    dramAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET] = getDfReg (dfType, instanceId, nodeId, addr + 12);
    if (getBit (DF__REMAP_EN_BITPOS_DF4, dramAddressMapRegs[ADDR_MAP_ARRAY_CTL_OFFSET]))
    {
      remapSel = getBits (DF__REMAP_SEL_BITPOS_LO_DF4POINT5, DF__REMAP_SEL_BITPOS_HI_DF4POINT5, dramAddressMapRegs[ADDR_MAP_ARRAY_CTL_OFFSET]);
      addr = DF__CSTARGETREMAP0A_REGADDR_DF4 + (remapSel * 24);
      dramAddressMapRegs[ADDR_MAP_ARRAY_REMAP0_OFFSET] = getDfReg (dfType, instanceId, nodeId, addr);
      dramAddressMapRegs[ADDR_MAP_ARRAY_REMAP1_OFFSET] = getDfReg (dfType, instanceId, nodeId, addr + 4);
      dramAddressMapRegs[ADDR_MAP_ARRAY_REMAP2_OFFSET] = getDfReg (dfType, instanceId, nodeId, addr + 8);
    }
  } else
  {
    ASSERT (FALSE);
  }
  return;
}

/*------------------------------------------------------------------
 Function: getDfRegDramHoleCtrl
 Purpose: Get the DF::DramHoleControl register (always on node 0 via BROADCAST_ACCESS)
 Inputs: The DF type
 Outputs: The requested register
 *------------------------------------------------------------------*/
UINT32
getDfRegDramHoleCtrl (
  DfType *dfType
  )
{
  UINT32 addr;

  if (dfType->dfVersion < DF_TYPE_DF4)
  {
    addr = DF__DRAMHOLECTL_REGADDR_PRE_DF4;
  }
  else
  {
    addr = DF__DRAMHOLECTL_REGADDR_DF4;
  }
  return (getDfReg (dfType, BROADCAST_ACCESS, 0, addr));
}

/*------------------------------------------------------------------
 Function: getDramHoleBase
 Purpose: Get the address of the DRAM hole
 Inputs: The DF type
 Outputs: The base address of the DRAM/MMIO hole (TOM)
 *------------------------------------------------------------------*/
UINT64
getDramHoleBase (
  DfType *dfType
  )
{
  UINT64 dramHoleBase;

  dramHoleBase = getBits (DF__DRAM_HOLE_BASE_ADDR_BITPOS_LO, DF__DRAM_HOLE_BASE_ADDR_BITPOS_HI, getDfRegDramHoleCtrl (dfType));
  dramHoleBase = dramHoleBase << 24;

  return (dramHoleBase);
}

/*------------------------------------------------------------------
 Function: decodeDramIntLvAddrBit
 Purpose: Decode the least-significant interleave bit
 Inputs: DF2/DF3 mode, and the address map
 Outputs: The starting interleave bit (8, 9, 10, 11, or 12)
 *------------------------------------------------------------------*/
UINT32
decodeDramIntLvAddrBit (
  DfType *dfType,
  UINT32 *dramAddressMapRegs
  )
{
  UINT32 intLvAddrBit = 0;
  UINT32 intLvAddrSel = 0;
  if (dfType->dfVersion == DF_TYPE_DF2)
  {
    intLvAddrSel = getBits (DF__INTLV_ADDR_SEL_BITPOS_LO_DF2, DF__INTLV_ADDR_SEL_BITPOS_HI_DF2, dramAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET]);
  } else if ((dfType->dfVersion == DF_TYPE_DF3) || (dfType->dfVersion == DF_TYPE_DF3POINT5))
  {
    intLvAddrSel = getBits (DF__INTLV_ADDR_SEL_BITPOS_LO_DF3, DF__INTLV_ADDR_SEL_BITPOS_HI_DF3, dramAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET]);
  } else if ((dfType->dfVersion == DF_TYPE_DF4) || (dfType->dfVersion == DF_TYPE_DF4POINT5))
  {
    intLvAddrSel = getBits (DF__INTLV_ADDR_SEL_BITPOS_LO_DF4, DF__INTLV_ADDR_SEL_BITPOS_HI_DF4, dramAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET]);
  } else
  {
    ASSERT (FALSE);
  }
  switch (intLvAddrSel)
  {
    case 0:
      intLvAddrBit = 8;
      break;
    case 1:
      intLvAddrBit = 9;
      break;
    case 2:
      intLvAddrBit = 10;
      break;
    case 3:
      intLvAddrBit = 11;
      break;
    case 4:
      intLvAddrBit = 12; // Milan only, but assume that if this is selected it is valid.
      break;
    default:
      ASSERT (FALSE); // Unsupported intLvAddrSel
  }

  return (intLvAddrBit);
}

/*------------------------------------------------------------------
 Function: getNumChannelFromDramIntLvMode
 Purpose: convert the IntLvMode (from decodeDramIntLvNumChan(intLvNumChan)
          into the nujmber of channels.
 Inputs: interleaving mode
 Outputs: The number of channels
 *------------------------------------------------------------------*/
INT32
getNumChannelFromDramIntLvMode (
  INT32 intLvMode
  )
{
  switch (intLvMode)
  {
    case INTERLEAVE_MODE_NONE:
      return (1);
    case INTERLEAVE_MODE_2CHAN_NOHASH:
      return (2);
    case INTERLEAVE_MODE_ZP_2CHAN_HASH:
      return (2);
    case INTERLEAVE_MODE_DF3_COD4_2CHAN_HASH:
      return (2);
    case INTERLEAVE_MODE_DF4_NPS4_2CHAN_HASH:
      return (2);
    case INTERLEAVE_MODE_DF4_NPS4_3CHAN_HASH:
      return (3);
    case INTERLEAVE_MODE_4CHAN_NOHASH:
      return (4);
    case INTERLEAVE_MODE_DF3_COD2_4CHAN_HASH:
      return (4);
    case INTERLEAVE_MODE_DF4_NPS2_4CHAN_HASH:
      return (4);
    case INTERLEAVE_MODE_DF4_NPS2_5CHAN_HASH:
      return (5);
    case INTERLEAVE_MODE_DF3_6CHAN:
      return (6);
    case INTERLEAVE_MODE_DF4_NPS2_6CHAN_HASH:
      return (6);
    case INTERLEAVE_MODE_8CHAN_NOHASH:
      return (8);
    case INTERLEAVE_MODE_DF3_COD1_8CHAN_HASH:
      return (8);
    case INTERLEAVE_MODE_MI2_HASH_8CHAN:
      return (8);
    case INTERLEAVE_MODE_MI3_HASH_8CHAN:
      return (8);
    case INTERLEAVE_MODE_DF4_NPS1_8CHAN_HASH:
      return (8);
    case INTERLEAVE_MODE_DF4_NPS1_10CHAN_HASH:
      return (10);
    case INTERLEAVE_MODE_DF4_NPS1_12CHAN_HASH:
      return (12);
    case INTERLEAVE_MODE_16CHAN_NOHASH:
      return (16);
    case INTERLEAVE_MODE_MI2_HASH_16CHAN:
      return (16);
    case INTERLEAVE_MODE_MI3_HASH_16CHAN:
      return (16);
    case INTERLEAVE_MODE_32CHAN_NOHASH:
      return (32);
    case INTERLEAVE_MODE_MI2_HASH_32CHAN:
      return (32);
    case INTERLEAVE_MODE_MI3_HASH_32CHAN:
      return (32);
    case INTERLEAVE_MODE_DF45_NPS1_16CHAN_1K_HASH:
      return (16);
    case INTERLEAVE_MODE_DF45_NPS0_24CHAN_1K_HASH:
      return (12);
    case INTERLEAVE_MODE_DF45_NPS4_2CHAN_1K_HASH:
      return (2);
    case INTERLEAVE_MODE_DF45_NPS2_4CHAN_1K_HASH:
      return (4);
    case INTERLEAVE_MODE_DF45_NPS1_8CHAN_1K_HASH:
      return (8);
    case INTERLEAVE_MODE_DF45_NPS4_3CHAN_1K_HASH:
      return (3);
    case INTERLEAVE_MODE_DF45_NPS2_6CHAN_1K_HASH:
      return (6);
    case INTERLEAVE_MODE_DF45_NPS1_12CHAN_1K_HASH:
      return (12);
    case INTERLEAVE_MODE_DF45_NPS2_5CHAN_1K_HASH:
      return (5);
    case INTERLEAVE_MODE_DF45_NPS1_10CHAN_1K_HASH:
      return (10);
    case INTERLEAVE_MODE_DF45_NPS4_2CHAN_2K_HASH:
      return (2);
    case INTERLEAVE_MODE_DF45_NPS2_4CHAN_2K_HASH:
      return (4);
    case INTERLEAVE_MODE_DF45_NPS1_8CHAN_2K_HASH:
      return (8);
    case INTERLEAVE_MODE_DF45_NPS1_16CHAN_2K_HASH:
      return (16);
    case INTERLEAVE_MODE_DF45_NPS4_3CHAN_2K_HASH:
      return (3);
    case INTERLEAVE_MODE_DF45_NPS2_6CHAN_2K_HASH:
      return (6);
    case INTERLEAVE_MODE_DF45_NPS1_12CHAN_2K_HASH:
      return (12);
    case INTERLEAVE_MODE_DF45_NPS0_24CHAN_2K_HASH:
      return (12);
    case INTERLEAVE_MODE_DF45_NPS2_5CHAN_2K_HASH:
      return (5);
    case INTERLEAVE_MODE_DF45_NPS1_10CHAN_2K_HASH:
      return (10);
  }
  ASSERT (FALSE);
  return 0;
}

/*------------------------------------------------------------------
 Function: decodeDramIntLvNumChan
 Purpose: Extract and decode IntLvNumChan from the address map
 Inputs: DF type, and the address map
 Outputs: The interleave mode (decoded)
 *------------------------------------------------------------------*/
UINT32
decodeDramIntLvNumChan (
  DfType *dfType,
  UINT32 *dramAddressMapRegs
  )
{
  UINT32 intLvNumChanField = 0;
  UINT32 intLvMode = 0;
  if (dfType->dfVersion == DF_TYPE_DF2)
  {
    intLvNumChanField = getBits (DF__INTLV_NUM_CHAN_BITPOS_LO_DF2, DF__INTLV_NUM_CHAN_BITPOS_HI_DF2, dramAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET]);
    // There was one reuse of an encoding. To hide this from the code, we convert to a different encoding.
    if (intLvNumChanField == 8)
    {
      intLvMode = INTERLEAVE_MODE_ZP_2CHAN_HASH;
    } else
    {
      intLvMode = intLvNumChanField;
    }
    // limited support in DF2...
    ASSERT ((intLvMode == INTERLEAVE_MODE_NONE) ||
        (intLvMode == INTERLEAVE_MODE_2CHAN_NOHASH) ||
        (intLvMode == INTERLEAVE_MODE_ZP_2CHAN_HASH));
  } else if (dfType->dfVersion == DF_TYPE_DF3)
  {
    intLvNumChanField = getBits (DF__INTLV_NUM_CHAN_BITPOS_LO_DF3, DF__INTLV_NUM_CHAN_BITPOS_HI_DF3, dramAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET]);
    intLvMode = intLvNumChanField;
  } else if (dfType->dfVersion == DF_TYPE_DF3POINT5)
  {
    intLvNumChanField = getBits (DF__INTLV_NUM_CHAN_BITPOS_LO_DF3, DF__INTLV_NUM_CHAN_BITPOS_HI_DF3POINT5, dramAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET]);
    intLvMode = intLvNumChanField;
    ASSERT (intLvMode != INTERLEAVE_MODE_DF3_6CHAN);
    // All the DF4 encodings are above the number of valid bits, so no need to check that they are excluded
  } else if (dfType->dfVersion == DF_TYPE_DF4)
  {
    intLvNumChanField = getBits (DF__INTLV_NUM_CHAN_BITPOS_LO_DF4, DF__INTLV_NUM_CHAN_BITPOS_HI_DF4, dramAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET]);
    intLvMode = intLvNumChanField;
    // All of the DF3 specific encodings are now reserved.
    ASSERT (intLvMode != INTERLEAVE_MODE_DF3_COD4_2CHAN_HASH);
    ASSERT (intLvMode != INTERLEAVE_MODE_DF3_COD2_4CHAN_HASH);
    ASSERT (intLvMode != INTERLEAVE_MODE_DF3_COD1_8CHAN_HASH);
    ASSERT (intLvMode != INTERLEAVE_MODE_DF3_6CHAN);
  } else if (dfType->dfVersion == DF_TYPE_DF4POINT5)
  {
    intLvNumChanField = getBits (DF__INTLV_NUM_CHAN_BITPOS_LO_DF4, DF__INTLV_NUM_CHAN_BITPOS_HI_DF4POINT5, dramAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET]);
    // 0-8 are common
    // 0x18-0x1A are MI300 hashes
    // To make the remaining unique, we add 0x20 to the Turin values
    if (intLvNumChanField <= INTERLEAVE_MODE_32CHAN_NOHASH)
    {
      intLvMode = intLvNumChanField;
    } else if ((intLvNumChanField >= INTERLEAVE_MODE_MI3_HASH_8CHAN) && (intLvNumChanField <= INTERLEAVE_MODE_MI3_HASH_32CHAN))
    {
      intLvMode = intLvNumChanField;
    } else
    {
      intLvMode = intLvNumChanField + 0x20;
    }
  } else
  {
    ASSERT (FALSE);
  }
  return (intLvMode);
}

/*------------------------------------------------------------------
 Function: extractDramIntLvNumDies
 Purpose: Extract extractDramIntLvNumDies from the address map
 Inputs: DF type, and the address map
 Outputs: The requested field (raw and not decoded)
 *------------------------------------------------------------------*/
UINT32
extractDramIntLvNumDies (
  DfType *dfType,
  UINT32 *dramAddressMapRegs
  )
{
  if (dfType->dfVersion == DF_TYPE_DF2)
  {
    return (getBits (DF__INTLV_NUM_DIES_BITPOS_LO_DF2, DF__INTLV_NUM_DIES_BITPOS_HI_DF2, dramAddressMapRegs[ADDR_MAP_ARRAY_LIMIT_OFFSET]));
  }
  if (dfType->dfVersion == DF_TYPE_DF3)
  {
    return (getBits (DF__INTLV_NUM_DIES_BITPOS_LO_DF3, DF__INTLV_NUM_DIES_BITPOS_HI_DF3, dramAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET]));
  }
  if (dfType->dfVersion == DF_TYPE_DF3POINT5)
  {
    return (getBit (DF__INTLV_NUM_DIES_BITPOS_DF3POINT5, dramAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET]));
  }
  if ((dfType->dfVersion == DF_TYPE_DF4) || (dfType->dfVersion == DF_TYPE_DF4POINT5))
  {
    return (getBits (DF__INTLV_NUM_DIES_BITPOS_LO_DF4, DF__INTLV_NUM_DIES_BITPOS_HI_DF4, dramAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET]));
  }
  ASSERT (FALSE);
  return 0;
}

/*------------------------------------------------------------------
 Function: decodeDramIntLvNumDies
 Purpose: Decode IntLvNumDies from the address map
 Inputs: DF type, and the address map
 Outputs: The requested field, decoded (e.g. 1, 2, or 4 dies interleaved)
 *------------------------------------------------------------------*/
UINT32
decodeDramIntLvNumDies (
  DfType *dfType,
  UINT32 *dramAddressMapRegs
  )
{
  UINT32 intLvNumDies = 0;
  UINT32 numDieInterleaved = 0;
  intLvNumDies = extractDramIntLvNumDies (dfType, dramAddressMapRegs);
  switch (intLvNumDies)
  {
    case 0:
      numDieInterleaved = 1;
      break;
    case 1:
      numDieInterleaved = 2;
      break;
    case 2:
      numDieInterleaved = 4;
      break;
    default:
      ASSERT (FALSE);
  }
  return (numDieInterleaved);
}

/*------------------------------------------------------------------
 Function: extractDramIntLvNumSkts
 Purpose: Extract IntLvNumSkts from the address map
 Inputs: DF type, and the address map
 Outputs: The requested field (raw and not decoded)
 *------------------------------------------------------------------*/
UINT32
extractDramIntLvNumSkts (
  DfType *dfType,
  UINT32 *dramAddressMapRegs
  )
{
  if (dfType->dfVersion == DF_TYPE_DF2)
  {
    return (getBit (DF__INTLV_NUM_SOCKETS_BITPOS, dramAddressMapRegs[ADDR_MAP_ARRAY_LIMIT_OFFSET]));
  } else if ((dfType->dfVersion == DF_TYPE_DF3) || (dfType->dfVersion == DF_TYPE_DF3POINT5))
  {
    return (getBit (DF__INTLV_NUM_SOCKETS_BITPOS, dramAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET]));
  } else if ((dfType->dfVersion == DF_TYPE_DF4) || (dfType->dfVersion == DF_TYPE_DF4POINT5))
  {
    return (getBit (DF__INTLV_NUM_SOCKETS_BITPOS_DF4, dramAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET]));
  }
  ASSERT (FALSE);
  return 0;
}

/*------------------------------------------------------------------
 Function: extractDstFabricId
 Purpose: Extract DstFabricId from from the address map
 Inputs: DF type, and the address map
 Outputs: The requested field
 *------------------------------------------------------------------*/
UINT32
extractDstFabricId (
  DfType *dfType,
  UINT32 *dramAddressMapRegs
  )
{
  if (dfType->dfVersion == DF_TYPE_DF2)
  {
    return (getBits (DF__DSTFABRICID_BITPOS_LO_DF2, DF__DSTFABRICID_BITPOS_HI_DF2, dramAddressMapRegs[ADDR_MAP_ARRAY_LIMIT_OFFSET]));
  } else if (dfType->dfVersion == DF_TYPE_DF3)
  {
    return (getBits (DF__DSTFABRICID_BITPOS_LO_DF3, DF__DSTFABRICID_BITPOS_HI_DF3, dramAddressMapRegs[ADDR_MAP_ARRAY_LIMIT_OFFSET]));
  } else if (dfType->dfVersion == DF_TYPE_DF3POINT5)
  {
    return (getBits (DF__DSTFABRICID_BITPOS_LO_DF3, DF__DSTFABRICID_BITPOS_HI_DF3POINT5, dramAddressMapRegs[ADDR_MAP_ARRAY_LIMIT_OFFSET]));
  } else if ((dfType->dfVersion == DF_TYPE_DF4) || (dfType->dfVersion == DF_TYPE_DF4POINT5))
  {
    return (getBits (DF__DSTFABRICID_BITPOS_LO_DF4, DF__DSTFABRICID_BITPOS_HI_DF4, dramAddressMapRegs[ADDR_MAP_ARRAY_CTL_OFFSET]));
  }
  ASSERT (FALSE);
  return 0;
}

/*------------------------------------------------------------------
 Function: extractDramBaseAddr
 Purpose: Extract DramBaseAddr (64-bits) from from the address map
 Inputs: DF type, and the address map
 Outputs: The requested field
 *------------------------------------------------------------------*/
UINT64
extractDramBaseAddr (
  DfType *dfType,
  UINT32 *dramAddressMapRegs
  )
{
  UINT64 dramBaseAddr;

  if (dfType->dfVersion < DF_TYPE_DF4)
  {
    dramBaseAddr = getBits (DF__DRAM_BASE_ADDR_BITPOS_LO, DF__DRAM_BASE_ADDR_BITPOS_HI, dramAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET]);
  } else
  {
    dramBaseAddr = getBits (DF__DRAM_BASE_ADDR_BITPOS_LO_DF4, DF__DRAM_BASE_ADDR_BITPOS_HI_DF4, dramAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET]);
  }
  dramBaseAddr = dramBaseAddr << DF__LSB_ADDR_BIT_IN_DRAM_MAPS;
  return (dramBaseAddr);
}

/*------------------------------------------------------------------
 Function: extractDramLimitAddr
 Purpose: Extract DramLimitAddr (64-bits) from from the address map
 Inputs: DF type, and the address map
 Outputs: The requested field
 *------------------------------------------------------------------*/
UINT64
extractDramLimitAddr (
  DfType *dfType,
  UINT32 *dramAddressMapRegs
  )
{
  UINT64 dramLimitAddr;
  UINT64 lowBits;

  dramLimitAddr = 0;
  if (dfType->dfVersion < DF_TYPE_DF4)
  {
    dramLimitAddr = getBits (DF__DRAM_LIMIT_ADDR_BITPOS_LO, DF__DRAM_LIMIT_ADDR_BITPOS_HI, dramAddressMapRegs[ADDR_MAP_ARRAY_LIMIT_OFFSET]);
  } else
  {
    dramLimitAddr = getBits (DF__DRAM_LIMIT_ADDR_BITPOS_LO_DF4, DF__DRAM_LIMIT_ADDR_BITPOS_HI_DF4, dramAddressMapRegs[ADDR_MAP_ARRAY_LIMIT_OFFSET]);
  }
  dramLimitAddr = dramLimitAddr << DF__LSB_ADDR_BIT_IN_DRAM_MAPS;
  // Must extend the dramLimitAddr to the right (from LSB-1 : 0) with ones.
  lowBits = (1 << DF__LSB_ADDR_BIT_IN_DRAM_MAPS);
  lowBits = lowBits - 1;
  dramLimitAddr = dramLimitAddr | lowBits;
  return (dramLimitAddr);
}

/*------------------------------------------------------------------
 Function: extractDramAddrRangeValid
 Purpose: Extract AddrRngValid from from the address map
 Inputs: DF type, and the address map
 Outputs: The requested field
 *------------------------------------------------------------------*/
UINT32
extractDramAddrRangeValid (
  DfType *dfType,
  UINT32 *dramAddressMapRegs
  )
{
  if (dfType->dfVersion < DF_TYPE_DF4)
  {
    return (getBit (DF__ADDR_RANGE_VALID_BITPOS, dramAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET]));
  }
  return (getBit (DF__ADDR_RANGE_VALID_BITPOS, dramAddressMapRegs[ADDR_MAP_ARRAY_CTL_OFFSET]));
}

/*------------------------------------------------------------------
 Function: extractLgcyMmioHoleEn
 Purpose: Extract LgcyMmioHoleEn from from the address map
 Inputs: DF type, and the address map
 Outputs: The requested field
 *------------------------------------------------------------------*/
UINT32
extractLgcyMmioHoleEn (
  DfType *dfType,
  UINT32 *dramAddressMapRegs
  )
{
  if (dfType->dfVersion < DF_TYPE_DF4)
  {
    return (getBit (DF__LEGACY_MMIO_HOLE_EN_BITPOS, dramAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET]));
  }
  return (getBit (DF__LEGACY_MMIO_HOLE_EN_BITPOS, dramAddressMapRegs[ADDR_MAP_ARRAY_CTL_OFFSET]));
}

/*------------------------------------------------------------------
 Function: extractLog2Addr64KSpace
 Purpose: Extract Log2Addr64KSpace from from the address map
 Inputs: DF type, and the address map
 Outputs: The requested field
 *------------------------------------------------------------------*/
UINT32
extractLog2Addr64KSpace (
  DfType *dfType,
  UINT32 *dramAddressMapRegs
  )
{
  UINT32 log2Addr64KSpace = 0;

  ASSERT (dfType->dfVersion != DF_TYPE_DF2);
  ASSERT (dfType->dfVersion != DF_TYPE_DF3POINT5); // 6-channel is not supported

  if (dfType->dfVersion == DF_TYPE_DF3)
  {
    ASSERT (dramAddressMapRegs[ADDR_MAP_ARRAY_NP2_OFFSET] != 0xFFFFFFFF); // detect unsupported cases
    // 6-channel config is only supported on address space 0,
    // so only need to look at log2Addr64KSpace0
    log2Addr64KSpace = getBits (DF__LOG2_ADDR_SPACE_BITPOS_LO, DF__LOG2_ADDR_SPACE_BITPOS_HI, dramAddressMapRegs[ADDR_MAP_ARRAY_NP2_OFFSET]);
  } else if ((dfType->dfVersion == DF_TYPE_DF4) || (dfType->dfVersion == DF_TYPE_DF4POINT5))
  {
    log2Addr64KSpace = getBits (DF__LOG2_ADDR_SPACE_BITPOS_LO_DF4, DF__LOG2_ADDR_SPACE_BITPOS_HI_DF4, dramAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET]);
  } else
  {
    ASSERT (FALSE);
  }
  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "log2Addr64KSpace=%d\n", log2Addr64KSpace);
  return (log2Addr64KSpace);
}

/*------------------------------------------------------------------
 Function: extractCxlBaseAddr
 Purpose: Extract CxlBaseAddr (64-bits) from from the CXL address map
 Inputs: DF type, and the address map
 Outputs: The requested field
 *------------------------------------------------------------------*/
UINT64 extractCxlBaseAddr (
  DfType *dfType,
  UINT32 *cxlAddressMapRegs
  )
{
  UINT64 cxlBaseAddr;
  if (getBits (DF__CXL_ADDR_FORMAT_BITPOS_LO, DF__CXL_ADDR_FORMAT_BITPOS_HI, cxlAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET]) == 0)
  {
    cxlBaseAddr = getBits (DF__CXL_BASE_ADDR_BITPOS_LO_FMT0, DF__CXL_BASE_ADDR_BITPOS_HI_FMT0, cxlAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET]);
  } else
  {
    cxlBaseAddr = getBits (DF__CXL_BASE_ADDR_BITPOS_LO_FMT1, DF__CXL_BASE_ADDR_BITPOS_HI_FMT1, cxlAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET]);
  }
  cxlBaseAddr = cxlBaseAddr << DF__LSB_ADDR_BIT_IN_CXL_MAPS;
  return (cxlBaseAddr);
}

/*------------------------------------------------------------------
 Function: extractCxlLimitAddr
 Purpose: Extract CxlLimitAddr (64-bits) from from the CXL address map
 Inputs: DF type, and the address map
 Outputs: The requested field
 *------------------------------------------------------------------*/
UINT64 extractCxlLimitAddr (
  DfType *dfType,
  UINT32 *cxlAddressMapRegs
  )
{
  UINT64 cxlLimitAddr;
  UINT64 lowBits;

  cxlLimitAddr = getBits (DF__CXL_LIMIT_ADDR_BITPOS_LO, DF__CXL_LIMIT_ADDR_BITPOS_HI, cxlAddressMapRegs[ADDR_MAP_ARRAY_LIMIT_OFFSET]);

  cxlLimitAddr = cxlLimitAddr << DF__LSB_ADDR_BIT_IN_CXL_MAPS;

  // Must extend the cxlLimitAddr to the right (from LSB-1 : 0) with ones.
  lowBits = (1 << DF__LSB_ADDR_BIT_IN_CXL_MAPS);
  lowBits = lowBits - 1;
  cxlLimitAddr = cxlLimitAddr | lowBits;

  return (cxlLimitAddr);

}

/*------------------------------------------------------------------
 Function: extractCxlAddrRangeValid
 Purpose: Extract AddrRngValid from from the CXL address map
 Inputs: DF type, and the address map
 Outputs: The requested field
 *------------------------------------------------------------------*/
UINT32 extractCxlAddrRangeValid (
  DfType *dfType,
  UINT32 *cxlAddressMapRegs
  )
{
  if (getBits (DF__CXL_ADDR_FORMAT_BITPOS_LO, DF__CXL_ADDR_FORMAT_BITPOS_HI, cxlAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET]) == 0)
  {
    return (getBit (DF__CXL_ADDR_RANGE_VALID_BITPOS_FMT0, cxlAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET]));
  }
  return (getBit (DF__CXL_ADDR_RANGE_VALID_BITPOS_FMT1, cxlAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET]));
}

/*------------------------------------------------------------------
 Function: extractCxlNpaEn
 Purpose: Extract NpaEn from from the CXL address map
 Inputs: DF type, and the address map
 Outputs: The requested field
 *------------------------------------------------------------------*/
UINT32 extractCxlNpaEn (
  DfType *dfType,
  UINT32 *cxlAddressMapRegs
  )
{
  if (getBits (DF__CXL_ADDR_FORMAT_BITPOS_LO, DF__CXL_ADDR_FORMAT_BITPOS_HI, cxlAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET]) == 0)
  {
    return (getBit (DF__CXL_NPA_EN_BITPOS_FMT0, cxlAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET]));
  }
  return (getBit (DF__CXL_NPA_EN_BITPOS_FMT1, cxlAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET]));
}

/*------------------------------------------------------------------
 Function: extractCxlNpaBaseEn
 Purpose: Extract NpaBaseEn from from the CXL address map
 Inputs: DF type, and the address map
 Outputs: The requested field
 *------------------------------------------------------------------*/
UINT32 extractCxlNpaBaseEn (
  DfType *dfType,
  UINT32 *cxlAddressMapRegs
  )
{
  if (getBits (DF__CXL_ADDR_FORMAT_BITPOS_LO, DF__CXL_ADDR_FORMAT_BITPOS_HI, cxlAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET]) == 0)
  {
    // Does not exist, treated as if it is always one (the base is taken out)
    return (1);
  }
  return (getBit (DF__CXL_NPA_BASE_EN_BITPOS, cxlAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET]));
}

/*------------------------------------------------------------------
 Function: extractCxlIntLvLinkEn
 Purpose: Extract IntLvLinkEn from from the CXL address map
 Inputs: DF type, and the address map, must be enabled
 Outputs: The requested field
 *------------------------------------------------------------------*/
UINT32 extractCxlIntLvLinkEn (
  DfType *dfType,
  UINT32 *cxlAddressMapRegs
  )
{
  return (getBits (DF__CXL_INT_LV_LINK_EN_BITPOS_LO, DF__CXL_INT_LV_LINK_EN_BITPOS_HI,
          cxlAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET]));
}

/*------------------------------------------------------------------
 Function: decodeCxlNumSubChannelInterleaveBits
 Purpose: Extract IntLvLinkEn from from the CXL address map
          and determine the number of bits in the interleave
 Inputs: DF type, and the address map, must be enabled
 Outputs: Log2 of the number of channels interleaved (0, 1, or 2)
 *------------------------------------------------------------------*/
UINT32 decodeCxlNumSubChannelInterleaveBits (
  DfType *dfType,
  UINT32 *cxlAddressMapRegs
  )
{
  UINT32 intLvLinkEn, count;

  intLvLinkEn = extractCxlIntLvLinkEn (dfType, cxlAddressMapRegs);
  ASSERT (intLvLinkEn != 0); // Must have at least one bit set if the address map is enabled

  count = 0;
  while (intLvLinkEn != 0)
  {
    if ((intLvLinkEn & 0x1) != 0)
    {
      count++;
    }
    intLvLinkEn = intLvLinkEn >> 1;
  }
  ASSERT (count != 3);
  ASSERT (count <= 4);
  if (count == 1)
  {
    return (0);
  }
  if (count == 2)
  {
    return (1);
  }
  if (count == 4)
  {
    return (2);
  }
  ASSERT (0);
  return (0);
}

/*------------------------------------------------------------------
 Function: decodeCxlIntLvAddrSel
 Purpose: Extract and decode IntLvAddrSel from from the CXL address map
 Inputs: DF type, and the address map
 Outputs: The interleave address bit
 *------------------------------------------------------------------*/
UINT32 decodeCxlIntLvAddrSel (
  DfType *dfType,
  UINT32 *cxlAddressMapRegs
  )
{
  if (getBit (DF__CXL_INT_LV_ADDR_SEL_BITPOS, cxlAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET]) == 0)
  {
    return (6);
  }
  return (8);
}

/*------------------------------------------------------------------
 Function: convertPhysicalCsFabricIdToLogicalCsFabricId
 Purpose: Remap a "actual FabricID" to a logical FabricID
 Inputs: The DF type, csFabricId (actual), and address map
 Outputs: The logical CS FabricID
 *------------------------------------------------------------------*/
UINT32
convertPhysicalCsFabricIdToLogicalCsFabricId (
  DfType *dfType,
  UINT32 csFabricId,
  UINT32 *dramAddressMapRegs
  )
{
  INT32 i;
  UINT32 csComponentId;
  UINT32 physicalId;

  //IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "convertPhysicalCsFabricIdToLogicalCsFabricId: csFabricId = 0x%x\n", csFabricId);

  if ((dfType->dfVersion == DF_TYPE_DF3POINT5) || (dfType->dfVersion == DF_TYPE_DF2) ||
      ((dfType->dfVersion == DF_TYPE_DF3) &&
       (decodeDramIntLvNumChan (dfType, dramAddressMapRegs) != INTERLEAVE_MODE_DF3_6CHAN)) ||
      ((dfType->dfVersion >= DF_TYPE_DF4) &&
       (getBit (DF__REMAP_EN_BITPOS_DF4, dramAddressMapRegs[ADDR_MAP_ARRAY_CTL_OFFSET]) == 0)))
  {

    return (csFabricId); // no remap supported on heterogeneous or MI200, avoid needing to read dGPU block fabric IDs
  }

  csComponentId = csFabricId & getComponentIdMask (dfType);
  if (dfType->dfVersion < DF_TYPE_DF4POINT5)
  {
    // readDfAddressMap registers have already made this "unity map" if it is not enabled, so just use it
    // The index is logical->physical. We want physical->logical, so just search for the first match.
    for (i = 0; i < 8; i++)
    {
      physicalId = (dramAddressMapRegs[ADDR_MAP_ARRAY_REMAP0_OFFSET] >> (i * 4)) & 0x0F;
      if (physicalId == csComponentId)
      {
        csFabricId = (csFabricId & getNodeIdMask (dfType)) | i;
        return (csFabricId);
      }
    }
    ASSERT (dfType->dfVersion >= DF_TYPE_DF4); // 4-bit CS IDs were not supported prior to DF4
    for (i = 0; i < 8; i++)
    {
      physicalId = (dramAddressMapRegs[ADDR_MAP_ARRAY_REMAP1_OFFSET] >> (i * 4)) & 0x0F;
      if (physicalId == csComponentId)
      {
        csFabricId = (csFabricId & getNodeIdMask (dfType)) | (i + 8);
        return (csFabricId);
      }
    }
  } else
  {
    // readDfAddressMap registers have already made this "unity map" if it is not enabled, so just use it
    // The index is logical->physical. We want physical->logical, so just search for the first match.
    for (i = 0; i < 6; i++)
    {
      physicalId = (dramAddressMapRegs[ADDR_MAP_ARRAY_REMAP0_OFFSET] >> (i * 5)) & 0x1F;
      if (physicalId == csComponentId)
      {
        csFabricId = (csFabricId & getNodeIdMask (dfType)) | i;
        return (csFabricId);
      }
    }
    for (i = 0; i < 6; i++)
    {
      physicalId = (dramAddressMapRegs[ADDR_MAP_ARRAY_REMAP1_OFFSET] >> (i * 5)) & 0x1F;
      if (physicalId == csComponentId)
      {
        csFabricId = (csFabricId & getNodeIdMask (dfType)) | (i + 6);
        return (csFabricId);
      }
    }
    for (i = 0; i < 6; i++)
    {
      physicalId = (dramAddressMapRegs[ADDR_MAP_ARRAY_REMAP2_OFFSET] >> (i * 5)) & 0x1F;
      if (physicalId == csComponentId)
      {
        csFabricId = (csFabricId & getNodeIdMask (dfType)) | (i + 12);
        return (csFabricId);
      }
    }
  }
  ASSERT (0); // could not find it in the index register.
  return (0);
}
/*------------------------------------------------------------------
 Function: convertLogicalFabricIdToPhysicalFabricId
 Purpose: checkDramHit has found that a given address would map to
          a "logical" CS component on a given nodeID. However, this
          logical number may not map to a physical CS component.
          This determines the physical CS component.
          It is "packaged" in a FabricID format.
 Inputs: The DF type, logical CS component ID, its socket number and address map
 Outputs: The physical CS nodeID and instance ID packaged in a FabricID format
 Algorithm: There are two ways that a DstFabricID can be converted.
            1) The first is through a remap register in DF. This is
               indexed by logical CS number and gives a "physical CS"
            2) The second is by BIOS renumbering the IDs. So now what
               looks like DstFabricID=2 ends up going to physical CS 3
               for example. But we would find a CS instnace 3 which
               has a BlockFabricID=2.
            It is unlikely that both will be in play at the same time
            but this is not ruled out.
 *------------------------------------------------------------------*/
UINT32
convertLogicalFabricIdToPhysicalFabricId (
  DfType *dfType,
  UINT32 logicalDstFabricId,
  UINT32 *dramAddressMapRegs
  )
{
  UINT32 remapReg;
  UINT32 fabricBlockInstanceInformation0Reg;
  UINT32 nodeId;
  UINT32 physicalCsComponentId;
  UINT32 physicalDstFabricId;
  UINT32 numDFInstances;
  UINT32 logicalCsId;
  UINT32 biosRemappedPhysicalDstFabricId;
  UINT32 i;

  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "convertLogicalFabricIdToPhysicalFabricId: logicalDstFabricId=0x%x\n", logicalDstFabricId);

  if (dfType->dfVersion == DF_TYPE_DF3POINT5)
  {
    return (logicalDstFabricId); // no remap supported on heterogeneous or MI200, avoid needing to read dGPU block fabric IDs
  }

  // First go through the CS remapper logic to convert the logical CS fabricID (nth offset in the map)
  // to an "actual" CS fabricID (the one that will go in the packet).
  // readDramAddressMapRegs have already made it "unity" if the remap is not enabled, so just read it.
  if ((dfType->dfVersion == DF_TYPE_DF3POINT5) ||
      (dfType->dfVersion == DF_TYPE_DF2) ||
      ((dfType->dfVersion == DF_TYPE_DF3) &&
       (decodeDramIntLvNumChan (dfType, dramAddressMapRegs) != INTERLEAVE_MODE_DF3_6CHAN)) ||
      ((dfType->dfVersion >= DF_TYPE_DF4) &&
       (getBit (DF__REMAP_EN_BITPOS_DF4, dramAddressMapRegs[ADDR_MAP_ARRAY_CTL_OFFSET]) == 0)))
  {
    physicalCsComponentId = logicalDstFabricId & getComponentIdMask (dfType);
  } else
  {
    if (dfType->dfVersion < DF_TYPE_DF4POINT5)
    {
      logicalCsId = logicalDstFabricId & getComponentIdMask (dfType);
      if (logicalCsId < 8)
      {
        remapReg = dramAddressMapRegs[ADDR_MAP_ARRAY_REMAP0_OFFSET];
        physicalCsComponentId = (remapReg >> (logicalCsId * 4));
        physicalCsComponentId &= 0x0F;
      } else
      {
        ASSERT (dfType->dfVersion >= DF_TYPE_DF4);
        remapReg = dramAddressMapRegs[ADDR_MAP_ARRAY_REMAP1_OFFSET];
        physicalCsComponentId = (remapReg >> ((logicalCsId - 8) * 4));
        physicalCsComponentId &= 0x0F;
      }
    } else
    {
      logicalCsId = logicalDstFabricId & getComponentIdMask (dfType);
      if (logicalCsId < 6)
      {
        remapReg = dramAddressMapRegs[ADDR_MAP_ARRAY_REMAP0_OFFSET];
        physicalCsComponentId = (remapReg >> (logicalCsId * 5));
        physicalCsComponentId &= 0x1F;
      } else if (logicalCsId < 12)
      {
        remapReg = dramAddressMapRegs[ADDR_MAP_ARRAY_REMAP1_OFFSET];
        physicalCsComponentId = (remapReg >> ((logicalCsId - 6) * 5));
        physicalCsComponentId &= 0x1F;
      } else
      {
        remapReg = dramAddressMapRegs[ADDR_MAP_ARRAY_REMAP2_OFFSET];
        physicalCsComponentId = (remapReg >> ((logicalCsId - 12) * 5));
        physicalCsComponentId &= 0x1F;
      }
    }
  }
  physicalDstFabricId = (logicalDstFabricId & getNodeIdMask (dfType)) | physicalCsComponentId;

  // Now find which CS block will get this FabricID destination.
  // To allow for BIOS renumbering of fabricIDs, search for this instance ID.
  nodeId = (logicalDstFabricId & getNodeIdMask (dfType)) >> getNodeIdShift (dfType);
  numDFInstances = getBits (DF__BLOCK_INSTANCE_COUNT_BITPOS_LO, DF__BLOCK_INSTANCE_COUNT_BITPOS_HI, getDfRegFabricBlkInstanceCnt (dfType, nodeId));
  for (i = 0; i < numDFInstances; i++)
  {
    fabricBlockInstanceInformation0Reg = getDfRegFabricBlkInstInfo0 (dfType, i, nodeId);
    // Skip gated blocks (detected because at least one bit must be non-zero in non-gated blocks)
    if (fabricBlockInstanceInformation0Reg == 0)
    {
      continue;
    }
    if (getBits (DF__INSTANCE_TYPE_BITPOS_LO, DF__INSTANCE_TYPE_BITPOS_HI, fabricBlockInstanceInformation0Reg) != DF__CS_INSTANCE_TYPE_VALUE)
    {
      continue;
    }
    if (getBits (DF__BLOCK_FABRICID_BITPOS_LO, DF__BLOCK_FABRICID_BITPOS_HI, getDfRegFabricBlkInstInfo3 (dfType, i, nodeId)) == physicalDstFabricId)
    {
      // Package the "CS instanceID" and the "CS nodeID" in a FabricID format.
      // Note that this is no longer the DstFabricID in any way. It is really now {nodeId, instanceID}
      biosRemappedPhysicalDstFabricId = (physicalDstFabricId & getNodeIdMask (dfType)) | i;
      if (biosRemappedPhysicalDstFabricId != physicalDstFabricId)
      {
        IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "BIOS shifted csId 0x%x to 0x%x\n", physicalDstFabricId, biosRemappedPhysicalDstFabricId);
      }

      IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "convertLogicalFabricIdToPhysicalFabricId complete: physicalFabricId=0x%x\n", biosRemappedPhysicalDstFabricId);
      return (biosRemappedPhysicalDstFabricId);
    }
  }

  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_ERROR, "Could not find logicalDstFabricId=0x%x (physicalDstFabricId=0x%x) in FabricBlockInstanceInformation3!\n",
      logicalDstFabricId, physicalDstFabricId);
  // CS not found!!!
  ASSERT (FALSE);
  return 0;
}

/*------------------------------------------------------------------
 Function: getCsLogicalComponentIdFromAddr
 Purpose: Determines the offset from DstFabricID based on the interleave modes
          and the given address.
 Inputs: DF type, address map, and address
 Outputs: The fabricID to add to DstFabricID.
 *------------------------------------------------------------------*/
UINT32
getCsLogicalComponentIdFromAddr(
  DfType *dfType,
  UINT32 *dramAddressMapRegs,
  UINT64 addr
  )
{
  UINT32 csId = 0;
  UINT32 dieId = 0;
  UINT32 socketId = 0;
  UINT32 nodeId;
  UINT32 intLvMode;
  UINT32 numChanInterleaved;
  UINT32 intLvAddrBit;
  UINT32 numDieInterleaved;
  UINT32 numSocketsInterleaved;
  UINT32 hashPA, hashPA8, hashPA9, hashPA12, hashPA13, modValue, region1KBit, mask;
  UINT32 addressMod;
  UINT32 intlvHashCtl, hashIntlvCtl4K, hashIntlvCtl64K, hashIntlvCtl2M, hashIntlvCtl1G, hashIntlvCtl1T;
  UINT64 tempAddr;
  UINT64 sizePerNode = 0;

  //IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "getCsLogicalComponentIdFromAddr(addr=0x%016lX, dfType=%d\n", addr, dfType->dfVersion);
  // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "dramBaseReg=0x%08X, dramLimitReg=0x%08X dramIntlvReg=0x%08X, dramCtlReg=0x%08X\n",
  //         dramAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET], dramAddressMapRegs[ADDR_MAP_ARRAY_LIMIT_OFFSET],
  //         dramAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET], dramAddressMapRegs[ADDR_MAP_ARRAY_CTL_OFFSET]);

  intLvMode = decodeDramIntLvNumChan (dfType, dramAddressMapRegs);
  intLvAddrBit = decodeDramIntLvAddrBit (dfType, dramAddressMapRegs);
  numChanInterleaved = getNumChannelFromDramIntLvMode (intLvMode);
  numDieInterleaved = decodeDramIntLvNumDies (dfType, dramAddressMapRegs);
  numSocketsInterleaved = extractDramIntLvNumSkts (dfType, dramAddressMapRegs) + 1;

  // non-interleaved should still have "1 socket/die interleaved"...
  ASSERT (numSocketsInterleaved != 0);
  ASSERT (numDieInterleaved != 0);
  ASSERT (numDieInterleaved <= 4);
  ASSERT (numSocketsInterleaved <= 2);

  intlvHashCtl = getIntlvHashCtlBits (dfType, dramAddressMapRegs);
  hashIntlvCtl64K = ((intlvHashCtl & 0x1) != 0) ? 1 : 0;
  hashIntlvCtl2M = ((intlvHashCtl & 0x2) != 0) ? 1 : 0;
  hashIntlvCtl1G = ((intlvHashCtl & 0x4) != 0) ? 1 : 0;
  hashIntlvCtl4K = ((intlvHashCtl & 0x8) != 0) ? 1 : 0;
  hashIntlvCtl1T = ((intlvHashCtl & 0x10) != 0) ? 1 : 0; // Can rely on this being zero when reserved
  switch (intLvMode)
  {
    case INTERLEAVE_MODE_NONE:
    case INTERLEAVE_MODE_2CHAN_NOHASH:
    case INTERLEAVE_MODE_4CHAN_NOHASH:
    case INTERLEAVE_MODE_8CHAN_NOHASH:
    case INTERLEAVE_MODE_16CHAN_NOHASH:
    case INTERLEAVE_MODE_32CHAN_NOHASH:
      if (intLvMode != INTERLEAVE_MODE_NONE)
      {
        csId = (UINT32)getBits64 (intLvAddrBit,
               intLvAddrBit + ((INT32)log2 (numChanInterleaved) - 1),
               addr);
      }
      if (numDieInterleaved > 1)
      {
        dieId = (UINT32)getBits64 (intLvAddrBit + ((INT32)log2 (numChanInterleaved)),
                intLvAddrBit + ((INT32)log2 (numChanInterleaved)) + ((INT32)log2 (numDieInterleaved) - 1),
                addr);
      }
      if (numSocketsInterleaved > 1)
      {
        socketId = (UINT32)getBits64 (intLvAddrBit + ((INT32)log2 (numChanInterleaved)) + ((INT32)log2 (numDieInterleaved)),
                   intLvAddrBit + ((INT32)log2 (numChanInterleaved)) + ((INT32)log2 (numDieInterleaved)) + ((INT32)log2 (numSocketsInterleaved) - 1),
                   addr);
      }
      break;
    case INTERLEAVE_MODE_DF3_COD4_2CHAN_HASH:
    case INTERLEAVE_MODE_DF3_COD2_4CHAN_HASH:
    case INTERLEAVE_MODE_DF3_COD1_8CHAN_HASH:
      // If we are using address hashing, the interleave address bit must be 8 or 9.
      ASSERT ((intLvAddrBit == 8) || (intLvAddrBit == 9));
      // Socket/die interleaving must be disabled.
      ASSERT (numDieInterleaved == 1);
      ASSERT (numSocketsInterleaved == 1);
      // CSSelect[0] = XOR of addr{intLvAddrBit, 14, 18, 23, 32}
      // CSSelect[1] = XOR of addr{12, 16, 21, 30}
      // CSSelect[2] = XOR of addr{13, 17, 22, 31}
      csId = 0;
      csId += (UINT32)((getBit64 (intLvAddrBit, addr) ^
                        getBit64 (14, addr) ^
                        (getBit64 (18, addr) & hashIntlvCtl64K) ^
                        (getBit64 (23, addr) & hashIntlvCtl2M) ^
                        (getBit64 (32, addr) & hashIntlvCtl1G)
                       ) << 0);
      csId += (UINT32)((getBit64 (12, addr) ^
                        (getBit64 (16, addr) & hashIntlvCtl64K) ^
                        (getBit64 (21, addr) & hashIntlvCtl2M) ^
                        (getBit64 (30, addr) & hashIntlvCtl1G)
                       ) << 1);
      csId += (UINT32)((getBit64 (13, addr) ^
                        (getBit64 (17, addr) & hashIntlvCtl64K) ^
                        (getBit64 (22, addr) & hashIntlvCtl2M) ^
                        (getBit64 (31, addr) & hashIntlvCtl1G)
                       ) << 2);
      csId &= (numChanInterleaved - 1);
      break;
    case INTERLEAVE_MODE_DF3_6CHAN:
      // Interleave address bit must be 11 or 12.
      ASSERT ((intLvAddrBit == 11) || (intLvAddrBit == 12));
      // Socket/die interleaving must be disabled.
      ASSERT (numDieInterleaved == 1);
      ASSERT (numSocketsInterleaved == 1);
      // For intLvAddrBit==12:
      // HashPA[0] = {12 ^ 15 ^ 23 ^ 32}
      // HashPA[1] = {13 ^ 21 ^ 30}
      // HashPA[2] = {14 ^ 22 ^ 31}
      // ModId[1:0] = (HashPA[2:1] == 3) ? PA[n:15] % 3 : HashPA[2:1]
      // CsSelect[2:0] = {ModId[1:0], HashPA[0])
      // For intLvAddrBit==11:
      // HashPA[0] = {11 ^ 14 ^ 23 ^ 32}
      // HashPA[1] = {12 ^ 21 ^ 30}
      // HashPA[2] = {13 ^ 22 ^ 31}
      // ModId[1:0] = (HashPA[2:1] == 3) ? PA[n:14] % 3 : HashPA[2:1]
      // CsSelect[2:0] = {ModId[1:0], HashPA[0])
      hashPA = 0;
      hashPA += (UINT32)((getBit64 (intLvAddrBit, addr) ^
                          getBit64 (intLvAddrBit + 3, addr) ^
                          (getBit64 (23, addr) & hashIntlvCtl2M) ^
                          (getBit64 (32, addr) & hashIntlvCtl1G)
                         ) << 0);
      hashPA += (UINT32)((getBit64 (intLvAddrBit + 1, addr) ^
                          (getBit64 (21, addr) & hashIntlvCtl2M) ^
                          (getBit64 (30, addr) & hashIntlvCtl1G)
                         ) << 1);
      hashPA += (UINT32)((getBit64 (intLvAddrBit + 2, addr) ^
                          (getBit64 (22, addr) & hashIntlvCtl2M) ^
                          (getBit64 (31, addr) & hashIntlvCtl1G)
                         ) << 2);
      // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "hashPA=%d\n", hashPA);
      if (getBits (1, 2, hashPA) == 3)
      {
        // keep bit 0 of hashPA
        hashPA = getBit (0, hashPA);
        // Find modulo 3 of bits n:15 or n:14 of system address
        tempAddr = getBits64 (intLvAddrBit + 3, 63, addr);
        addressMod = (UINT32)(tempAddr % 3);
        // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "tempAddr=0x%016lX, addressMod=%d\n",
        //         tempAddr, addressMod);
        // The modulo 3 is used as CsSelect[2:1]
        hashPA |= (addressMod << 1);
      }
      csId = hashPA;
      break;
    case INTERLEAVE_MODE_MI2_HASH_8CHAN:
    case INTERLEAVE_MODE_MI2_HASH_16CHAN:
    case INTERLEAVE_MODE_MI2_HASH_32CHAN:
      // Socket interleaving must be disabled.
      ASSERT (numSocketsInterleaved == 1);
      // Die interleaving must be disabled.
      ASSERT (numDieInterleaved == 1);
      // intLvAddrBit is actually a don't care. Assert that it must indicate bit 8.
      ASSERT (intLvAddrBit == 8);
      //                              64K  2M  1G
      // CSSelect[0] = XOR of addr{8,  16, 21, 30};
      // CSSelect[1] = XOR of addr{9,  17, 22, 31};
      // CSSelect[2] = XOR of addr{10, 18, 23, 32};
      // CSSelect[3] = XOR of addr{11, 19, 24, 33};
      // CSSelect[4] = XOR of addr{12, 20, 25, 34};
      csId = 0;
      csId += (UINT32)((getBit64 (8, addr) ^
                        (getBit64 (16, addr) & hashIntlvCtl64K) ^
                        (getBit64 (21, addr) & hashIntlvCtl2M) ^
                        (getBit64 (30, addr) & hashIntlvCtl1G)
                       ) << 0);
      csId += (UINT32)((getBit64 (9, addr) ^
                        (getBit64 (17, addr) & hashIntlvCtl64K) ^
                        (getBit64 (22, addr) & hashIntlvCtl2M) ^
                        (getBit64 (31, addr) & hashIntlvCtl1G)
                       ) << 1);
      csId += (UINT32)((getBit64 (10, addr) ^
                        (getBit64 (18, addr) & hashIntlvCtl64K) ^
                        (getBit64 (23, addr) & hashIntlvCtl2M) ^
                        (getBit64 (32, addr) & hashIntlvCtl1G)
                       ) << 2);
      csId += (UINT32)((getBit64 (11, addr) ^
                        (getBit64 (19, addr) & hashIntlvCtl64K) ^
                        (getBit64 (24, addr) & hashIntlvCtl2M) ^
                        (getBit64 (33, addr) & hashIntlvCtl1G)
                       ) << 3);
      csId += (UINT32)((getBit64 (12, addr) ^
                        (getBit64 (20, addr) & hashIntlvCtl64K) ^
                        (getBit64 (25, addr) & hashIntlvCtl2M) ^
                        (getBit64 (34, addr) & hashIntlvCtl1G)
                       ) << 4);
      csId &= (numChanInterleaved - 1);
      break;
    case INTERLEAVE_MODE_MI3_HASH_8CHAN:
    case INTERLEAVE_MODE_MI3_HASH_16CHAN:
    case INTERLEAVE_MODE_MI3_HASH_32CHAN:
      // Socket interleaving must be disabled.
      ASSERT (numSocketsInterleaved == 1);
      // intLvAddrBit is actually a don't care. Assert that it must indicate bit 8.
      ASSERT (intLvAddrBit == 8);
      //                               4K 64K  2M  1G  1T  1T
      // CSSelect[0] = XOR of addr{8,  12, 15, 22, 29, 36, 43};
      // CSSelect[1] = XOR of addr{9,  13, 16, 23, 30, 37, 44};
      // CSSelect[2] = XOR of addr{10, 14, 17, 24, 31, 38, 45};
      // CSSelect[3] = XOR of addr{11,     18, 25, 32, 39, 46};
      // CSSelect[4] = XOR of addr{14,     19, 26, 33, 40, 47}; // aka stack
      // DieID[0]    = XOR of addr{12,     20, 27, 34, 41    };
      // DieID[1]    = XOR of addr{13,     21, 28, 35, 42    };
      csId = 0;
      csId += (UINT32)((getBit64 (8, addr) ^
                        (getBit64 (12, addr) & hashIntlvCtl4K) ^
                        (getBit64 (15, addr) & hashIntlvCtl64K) ^
                        (getBit64 (22, addr) & hashIntlvCtl2M) ^
                        (getBit64 (29, addr) & hashIntlvCtl1G) ^
                        (getBit64 (36, addr) & hashIntlvCtl1T) ^
                        (getBit64 (43, addr) & hashIntlvCtl1T)
                       ) << 0);
      csId += (UINT32)((getBit64 (9, addr) ^
                        (getBit64 (13, addr) & hashIntlvCtl4K) ^
                        (getBit64 (16, addr) & hashIntlvCtl64K) ^
                        (getBit64 (23, addr) & hashIntlvCtl2M) ^
                        (getBit64 (30, addr) & hashIntlvCtl1G) ^
                        (getBit64 (37, addr) & hashIntlvCtl1T) ^
                        (getBit64 (44, addr) & hashIntlvCtl1T)
                       ) << 1);
      csId += (UINT32)((getBit64 (10, addr) ^
                        (getBit64 (14, addr) & hashIntlvCtl4K) ^
                        (getBit64 (17, addr) & hashIntlvCtl64K) ^
                        (getBit64 (24, addr) & hashIntlvCtl2M) ^
                        (getBit64 (31, addr) & hashIntlvCtl1G) ^
                        (getBit64 (38, addr) & hashIntlvCtl1T) ^
                        (getBit64 (45, addr) & hashIntlvCtl1T)
                       ) << 2);
      csId += (UINT32)((getBit64 (11, addr) ^
                        (getBit64 (18, addr) & hashIntlvCtl64K) ^
                        (getBit64 (25, addr) & hashIntlvCtl2M) ^
                        (getBit64 (32, addr) & hashIntlvCtl1G) ^
                        (getBit64 (39, addr) & hashIntlvCtl1T) ^
                        (getBit64 (46, addr) & hashIntlvCtl1T)
                       ) << 3);
      csId += (UINT32)((getBit64 (14, addr) ^
                        (getBit64 (19, addr) & hashIntlvCtl64K) ^
                        (getBit64 (26, addr) & hashIntlvCtl2M) ^
                        (getBit64 (33, addr) & hashIntlvCtl1G) ^
                        (getBit64 (40, addr) & hashIntlvCtl1T) ^
                        (getBit64 (47, addr) & hashIntlvCtl1T)
                       ) << 4);
      csId &= (numChanInterleaved - 1);
      dieId = 0;
      dieId += (UINT32)((getBit64 (12, addr) ^
                         (getBit64 (20, addr) & hashIntlvCtl64K) ^
                         (getBit64 (27, addr) & hashIntlvCtl2M) ^
                         (getBit64 (34, addr) & hashIntlvCtl1G) ^
                         (getBit64 (41, addr) & hashIntlvCtl1T)
                        ) << 0);
      dieId += (UINT32)((getBit64 (13, addr) ^
                         (getBit64 (21, addr) & hashIntlvCtl64K) ^
                         (getBit64 (28, addr) & hashIntlvCtl2M) ^
                         (getBit64 (35, addr) & hashIntlvCtl1G) ^
                         (getBit64 (42, addr) & hashIntlvCtl1T)
                        ) << 1);
      dieId &= (numDieInterleaved - 1);
      break;
    case INTERLEAVE_MODE_ZP_2CHAN_HASH:
      // Iinterleave address bit must be 8 or 9.
      ASSERT ((intLvAddrBit == 8) || (intLvAddrBit == 9));
      // Socket/die interleaving must be disabled.
      ASSERT (numDieInterleaved == 1);
      ASSERT (numSocketsInterleaved == 1);
      // CSSelect[0] = XOR of addr{intLvAddrBit, 12, 18, 21, 30}
      csId = (UINT32)(getBit64 (intLvAddrBit, addr) ^
                      getBit64 (12, addr) ^
                      getBit64 (18, addr) ^
                      getBit64 (21, addr) ^
                      getBit64 (30, addr));
      break;
    case INTERLEAVE_MODE_DF4_NPS4_2CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS2_4CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS1_8CHAN_HASH:
      // The interleave address bit must be 8.
      ASSERT (intLvAddrBit == 8);
      // Die interleaving must be disabled.
      ASSERT (numDieInterleaved == 1);
      ASSERT (numSocketsInterleaved <= 2);
      // CSSelect[0] = XOR of addr{8, 16, 21, 30}
      //               and add in an XOR of 14 if socket interleaving disabled
      // CSSelect[1] = XOR of addr{12, 17, 22, 31}
      // CSSelect[2] = XOR of addr{13, 18, 23, 32}
      // CSSelect[3] = XOR of addr{14, 19, 24, 33} // socket interleaving enabled only
      hashPA = (UINT32)(getBit64 (8, addr) ^
                        (getBit64 (16, addr) & hashIntlvCtl64K) ^
                        (getBit64 (21, addr) & hashIntlvCtl2M) ^
                        (getBit64 (30, addr) & hashIntlvCtl1G));
      if (numSocketsInterleaved == 1)
      {
        hashPA ^= getBit64 (14, addr);
      }
      hashPA += (UINT32)((getBit64 (12, addr) ^
                          (getBit64 (17, addr) & hashIntlvCtl64K) ^
                          (getBit64 (22, addr) & hashIntlvCtl2M) ^
                          (getBit64 (31, addr) & hashIntlvCtl1G)
                         ) << 1);
      hashPA += (UINT32)((getBit64 (13, addr) ^
                          (getBit64 (18, addr) & hashIntlvCtl64K) ^
                          (getBit64 (23, addr) & hashIntlvCtl2M) ^
                          (getBit64 (32, addr) & hashIntlvCtl1G)
                         ) << 2);
      hashPA += (UINT32)((getBit64 (14, addr) ^
                          (getBit64 (19, addr) & hashIntlvCtl64K) ^
                          (getBit64 (24, addr) & hashIntlvCtl2M) ^
                          (getBit64 (33, addr) & hashIntlvCtl1G)
                         ) << 3);
      hashPA &= ((numChanInterleaved * numSocketsInterleaved) - 1);
      // When socket interleaving is enabled, CSSelect[0] is the socket bit
      if (numSocketsInterleaved == 1)
      {
        csId = hashPA;
      } else
      {
        socketId = hashPA & 1;
        csId = (hashPA >> 1);
      }
      break;
    case INTERLEAVE_MODE_DF45_NPS1_16CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS4_2CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS2_4CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_8CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS4_2CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS2_4CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_8CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_16CHAN_2K_HASH:
      // The interleave address bit must be 8.
      ASSERT (intLvAddrBit == 8);
      // Die interleaving must be disabled.
      ASSERT (numDieInterleaved == 1);
      ASSERT (numSocketsInterleaved <= 2);
      // CSSelect[0] = XOR of addr{8, 16, 21, 30, 40}
      // CSSelect[1] = XOR of addr{9, 17, 22, 31, 41} // 1K mode only
      // CSSelect[2] = XOR of addr{12, 18, 23, 32, 42}
      // CSSelect[3] = XOR of addr{13, 19, 24, 33, 43}
      // CSSelect[4] = XOR of addr{14, 19, 24, 33, 43}
      // In 2K, CSSelect[4:2] becomes CSSelect[3:1] (32-channel not supported)
      hashPA = (UINT32)(getBit64 (8, addr) ^
                        (getBit64 (16, addr) & hashIntlvCtl64K) ^
                        (getBit64 (21, addr) & hashIntlvCtl2M) ^
                        (getBit64 (30, addr) & hashIntlvCtl1G) ^
                        (getBit64 (40, addr) & hashIntlvCtl1T));
      region1KBit = 0;
      if ((intLvMode == INTERLEAVE_MODE_DF45_NPS1_16CHAN_1K_HASH) ||
          (intLvMode == INTERLEAVE_MODE_DF45_NPS4_2CHAN_1K_HASH) ||
          (intLvMode == INTERLEAVE_MODE_DF45_NPS2_4CHAN_1K_HASH) ||
          (intLvMode == INTERLEAVE_MODE_DF45_NPS1_8CHAN_1K_HASH))
      {
        hashPA += (UINT32)((getBit64 (9, addr) ^
                            (getBit64 (17, addr) & hashIntlvCtl64K) ^
                            (getBit64 (22, addr) & hashIntlvCtl2M) ^
                            (getBit64 (31, addr) & hashIntlvCtl1G) ^
                            (getBit64 (41, addr) & hashIntlvCtl1T)
                           ) << 1);
        region1KBit = 1;
      }
      hashPA += (UINT32)((getBit64 (12, addr) ^
                          (getBit64 (18, addr) & hashIntlvCtl64K) ^
                          (getBit64 (23, addr) & hashIntlvCtl2M) ^
                          (getBit64 (32, addr) & hashIntlvCtl1G) ^
                          (getBit64 (42, addr) & hashIntlvCtl1T)
                         ) << (1 + region1KBit));
      hashPA += (UINT32)((getBit64 (13, addr) ^
                          (getBit64 (19, addr) & hashIntlvCtl64K) ^
                          (getBit64 (24, addr) & hashIntlvCtl2M) ^
                          (getBit64 (33, addr) & hashIntlvCtl1G) ^
                          (getBit64 (43, addr) & hashIntlvCtl1T)
                         ) << (2 + region1KBit));
      hashPA += (UINT32)((getBit64 (14, addr) ^
                          (getBit64 (20, addr) & hashIntlvCtl64K) ^
                          (getBit64 (25, addr) & hashIntlvCtl2M) ^
                          (getBit64 (34, addr) & hashIntlvCtl1G) ^
                          (getBit64 (44, addr) & hashIntlvCtl1T)
                         ) << (3 + region1KBit));
      // In Turin, the number of channels encoded in IntLvNumChan already includes socket interleaving.
      if (dfType->dfVersion == DF_TYPE_DF4POINT5)
      {
        mask = (numChanInterleaved - 1);
      } else
      {
        mask = ((numChanInterleaved * numSocketsInterleaved) - 1);
      }
      hashPA &= mask;

      // When socket interleaving is enabled, CSSelect[0] is the socket bit
      if (numSocketsInterleaved == 1)
      {
        csId = hashPA;
      } else
      {
        socketId = hashPA & 1;
        csId = (hashPA >> 1);
      }
      break;
    case INTERLEAVE_MODE_DF4_NPS4_3CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS2_6CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS1_12CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS2_5CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS1_10CHAN_HASH:
      // The interleave address bit must be 8.
      ASSERT (intLvAddrBit == 8);
      // Die interleaving must be disabled.
      ASSERT (numDieInterleaved == 1);
      ASSERT (numSocketsInterleaved <= 2);
      switch (intLvMode)
      {
        case INTERLEAVE_MODE_DF4_NPS4_3CHAN_HASH:
        case INTERLEAVE_MODE_DF4_NPS2_6CHAN_HASH:
        case INTERLEAVE_MODE_DF4_NPS1_12CHAN_HASH:
          modValue = 3;
          break;
        case INTERLEAVE_MODE_DF4_NPS2_5CHAN_HASH:
        case INTERLEAVE_MODE_DF4_NPS1_10CHAN_HASH:
          modValue = 5;
          break;
      }
      // hashPA8
      hashPA8 = (UINT32)(getBit64 (8, addr) ^
                         getBit64 (14, addr) ^
                         (getBit64 (16, addr) & hashIntlvCtl64K) ^
                         (getBit64 (21, addr) & hashIntlvCtl2M) ^
                         (getBit64 (30, addr) & hashIntlvCtl1G));
      // hashPA12
      hashPA12 = (UINT32)(getBit64 (12, addr) ^
                          (getBit64 (17, addr) & hashIntlvCtl64K) ^
                          (getBit64 (22, addr) & hashIntlvCtl2M) ^
                          (getBit64 (31, addr) & hashIntlvCtl1G));
      // hashPA13
      hashPA13 = (UINT32)(getBit64 (13, addr) ^
                          (getBit64 (18, addr) & hashIntlvCtl64K) ^
                          (getBit64 (23, addr) & hashIntlvCtl2M) ^
                          (getBit64 (32, addr) & hashIntlvCtl1G));
      tempAddr = getBits64 (14, 63, addr);
      addressMod = tempAddr % modValue;

      // get csID from function{hashPA8/12/13, address % modValue}
      if (hashPA8)
      {
        csId = addressMod + 1;
        csId = csId % modValue;
      } else
      {
        csId = addressMod;
      }
      switch (intLvMode)
      {
        case INTERLEAVE_MODE_DF4_NPS2_6CHAN_HASH:
        case INTERLEAVE_MODE_DF4_NPS1_10CHAN_HASH:
          csId += hashPA13 * modValue;
          break;
        case INTERLEAVE_MODE_DF4_NPS1_12CHAN_HASH:
          csId += ((hashPA13 << 1) | hashPA12) * modValue;
          break;
      }
      if (numSocketsInterleaved > 1)
      {
        socketId = hashPA8;
      }
      break;
    case INTERLEAVE_MODE_DF45_NPS0_24CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS0_24CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_12CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_12CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS2_6CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS2_6CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS4_3CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS4_3CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_10CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_10CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS2_5CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS2_5CHAN_1K_HASH:
      // The interleave address bit must be 8.
      ASSERT (intLvAddrBit == 8);
      // Die interleaving must be disabled.
      ASSERT (numDieInterleaved == 1);
      ASSERT (numSocketsInterleaved <= 2);
      // hashPA8
      hashPA8 = (UINT32)(getBit64 (8, addr) ^
                         getBit64 (14, addr) ^
                         (getBit64 (16, addr) & hashIntlvCtl64K) ^
                         (getBit64 (21, addr) & hashIntlvCtl2M) ^
                         (getBit64 (30, addr) & hashIntlvCtl1G) ^
                         (getBit64 (40, addr) & hashIntlvCtl1T));
      // hashPA9
      hashPA9 = (UINT32)(getBit64 (9, addr) ^
                         (getBit64 (17, addr) & hashIntlvCtl64K) ^
                         (getBit64 (22, addr) & hashIntlvCtl2M) ^
                         (getBit64 (31, addr) & hashIntlvCtl1G) ^
                         (getBit64 (41, addr) & hashIntlvCtl1T));
      // hashPA12
      hashPA12 = (UINT32)(getBit64 (12, addr) ^
                          (getBit64 (18, addr) & hashIntlvCtl64K) ^
                          (getBit64 (23, addr) & hashIntlvCtl2M) ^
                          (getBit64 (32, addr) & hashIntlvCtl1G) ^
                          (getBit64 (42, addr) & hashIntlvCtl1T));
      // hashPA13
      hashPA13 = (UINT32)(getBit64 (13, addr) ^
                          (getBit64 (19, addr) & hashIntlvCtl64K) ^
                          (getBit64 (24, addr) & hashIntlvCtl2M) ^
                          (getBit64 (33, addr) & hashIntlvCtl1G) ^
                          (getBit64 (43, addr) & hashIntlvCtl1T));
      switch (intLvMode)
      {
        case INTERLEAVE_MODE_DF45_NPS0_24CHAN_1K_HASH:
          // SocketSel = HashPA[8]
          // Mod3[1:0] = {PA[MSB:13], 3'b0} % 3
          // ChSel = {Mod3[1:0], HashPA[12], HashPA[9]}
          // NormAddr = {PA[MSB:13] / 3, PA[11:10],PA[7:0]}
          socketId = hashPA8;
          csId = hashPA9;
          csId |= (hashPA12 << 1);
          csId |= ((getBits64 (13, 63, addr) << 3) % 3) << 2;
          break;
        case INTERLEAVE_MODE_DF45_NPS0_24CHAN_2K_HASH:
          // SocketSel = HashPA[8]
          // Mod3[1:0] = {PA[MSB:14], 4'b0} % 3
          // ChSel = {Mod3[1:0], HashPA[13:12]}
          // NormAddr = {PA[MSB:14] / 3, PA[11:9], PA[7:0]}
          socketId = hashPA8;
          csId = hashPA12;
          csId |= (hashPA13 << 1);
          csId |= ((getBits64 (14, 63, addr) << 4) % 3) << 2;
          break;
        case INTERLEAVE_MODE_DF45_NPS1_12CHAN_1K_HASH:
          // Mod3[1:0] = {PA[MSB:12], 2'b0} % 3
          // ChSel = {Mod3[1:0], HashPA[9:8]}
          // NormAddr = {PA[MSB:12] / 3, PA[11:10], PA[7:0]}
          ASSERT (numSocketsInterleaved == 1);
          socketId = 0;
          csId = hashPA8;
          csId |= (hashPA9 << 1);
          csId |= ((getBits64 (12, 63, addr) << 2) % 3) << 2;
          break;
        case INTERLEAVE_MODE_DF45_NPS1_12CHAN_2K_HASH:
          // Mod3[1:0] = {PA[MSB:13], 3'b0} % 3
          // ChSel = {Mod3[1:0], HashPA[12], HashPA[8]}
          // NormAddr = {{PA[MSB:13]} / 3, PA[11:9], PA[7:0]}
          ASSERT (numSocketsInterleaved == 1);
          socketId = 0;
          csId = hashPA8;
          csId |= (hashPA12 << 1);
          csId |= ((getBits64 (13, 63, addr) << 3) % 3) << 2;
          break;
        case INTERLEAVE_MODE_DF45_NPS2_6CHAN_1K_HASH:
          // Mod3[1:0] = {PA[MSB:12], PA[9], 1'b0} % 3
          // ChSel = {Mod3[1:0], HashPA[8]}
          // NormAddr = {{PA[MSB:12], PA[9]} / 3, PA[11:10], PA[7:0]}
          ASSERT (numSocketsInterleaved == 1);
          socketId = 0;
          csId = hashPA8;
          csId |= (((getBits64 (12, 63, addr) << 2) | (getBit64 (9, addr) << 1)) % 3) << 1;
          break;
        case INTERLEAVE_MODE_DF45_NPS2_6CHAN_2K_HASH:
          // Mod3[1:0] = {PA[MSB:12], 2'd0} % 3
          // ChSel = {Mod3[1:0], HashPA[8]}
          // NormAddr = {PA[MSB:12] / 3, PA[11:9], PA[7:0]}
          ASSERT (numSocketsInterleaved == 1);
          socketId = 0;
          csId = hashPA8;
          csId |= ((getBits64 (12, 63, addr) << 2) % 3) << 1;
          break;
        case INTERLEAVE_MODE_DF45_NPS4_3CHAN_1K_HASH:
          // Mod3[1:0] = {PA[MSB:12], PA[9:8]} % 3
          // ChSel = Mod3[1:0]
          // NormAddr = {{PA[MSB:12], PA[9:8]} / 3, PA[11:10], PA[7:0]}
          ASSERT (numSocketsInterleaved == 1);
          socketId = 0;
          csId = ((getBits64 (12, 63, addr) << 2) | (getBits64 (8, 9, addr))) % 3;
          break;
        case INTERLEAVE_MODE_DF45_NPS4_3CHAN_2K_HASH:
          // Mod3[1:0] = {PA[MSB:12], PA[8], 1'b0]} % 3
          // ChSel = Mod3[1:0]
          // NormAddr = {{PA[MSB:12], PA[8]} / 3, PA[11:9], PA[7:0]}
          ASSERT (numSocketsInterleaved == 1);
          socketId = 0;
          csId = ((getBits64 (12, 63, addr) << 2) | (getBit64 (8, addr) << 1)) % 3;
          break;
        case INTERLEAVE_MODE_DF45_NPS1_10CHAN_1K_HASH:
          // Mod5[2:0] = {PA[MSB:12], PA[9], 1'b0} % 5
          // ChSel = {Mod5[2:0], HashPA[8]}
          // NormAddr = {{PA[MSB:12], PA[9]} / 5, PA[11:10], PA[7:0]}
          ASSERT (numSocketsInterleaved == 1);
          socketId = 0;
          csId = hashPA8;
          csId |= (((getBits64 (12, 63, addr) << 2) | (getBit64 (9, addr) << 1)) % 5) << 1;
          break;
        case INTERLEAVE_MODE_DF45_NPS1_10CHAN_2K_HASH:
          // Mod5[2:0] = {PA[MSB:12], 2'd0} % 5
          // ChSel = {Mod5[2:0], HashPA[8]}
          // Norm Addr = {PA[MSB:12] / 5, PA[11:9], PA[7:0]}
          ASSERT (numSocketsInterleaved == 1);
          socketId = 0;
          csId = hashPA8;
          csId |= ((getBits64 (12, 63, addr) << 2) % 5) << 1;
          break;
        case INTERLEAVE_MODE_DF45_NPS2_5CHAN_1K_HASH:
          // Mod5[2:0] = {PA[MSB:12], PA[9:8]} % 5
          // ChSel = Mod5[2:0]
          // NormAddr = {{PA[MSB:12], PA[9:8]} / 5, PA[11:10], PA[7:0]}
          ASSERT (numSocketsInterleaved == 1);
          socketId = 0;
          csId = ((getBits64 (12, 63, addr) << 2) | (getBits64 (8, 9, addr))) % 5;
          break;
        case INTERLEAVE_MODE_DF45_NPS2_5CHAN_2K_HASH:
          // Mod5[2:0] = {PA[MSB:12], PA[8], 1'b0} % 5
          // ChSel = Mod5[2:0]
          // NormAddr = {{PA[MSB:12], PA[8]} / 5, PA[11:9], PA[7:0]}
          ASSERT (numSocketsInterleaved == 1);
          socketId = 0;
          csId = ((getBits64 (12, 63, addr) << 2) | (getBit64 (8, addr) << 1)) % 5;
          break;
      }
      break;
    default:
      ASSERT (FALSE);
  }


  // check for mega address map
  if ((dfType->dfVersion >= DF_TYPE_DF3POINT5) && (getBit (3, dramAddressMapRegs[ADDR_MAP_ARRAY_CTL_OFFSET]) != 0))
  {
    sizePerNode = (UINT64) getBits (24, 29, dramAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET]);
    sizePerNode = (UINT64) 1 << sizePerNode; // 2 to the power of log2DieAddr64KSpace
    sizePerNode = sizePerNode << 16; // multiply by 64KB
    // Find the integer division which will give you the nodeID
    nodeId = (UINT32)(addr / sizePerNode);
    // and place the nodeID in the cs ID
    csId |= (nodeId << getNodeIdShift (dfType));
  }

  // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "intLvMode=%d, numChanInterleaved=%d, intLvAddrBit=%d, numDieInterleaved=%d, numSocketsInterleaved=%d\n",
  //         intLvMode, numChanInterleaved, intLvAddrBit, numDieInterleaved, numSocketsInterleaved);
  //IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "getCsLogicalComponentIdFromAddr complete: csId=%d, dieId=%d, socketId=%d\n", csId, dieId, socketId);

  if (dieId != 0)
  {
    csId |= (dieId << getDieIdShift (dfType));
  }
  if (socketId != 0)
  {
    csId |= (socketId << getSocketIdShift (dfType));
  }
  return (csId);
}

/*------------------------------------------------------------------
 Function: normalizeMod3
 Purpose: Helper function to just do the mod3 algorithm
 Inputs: dfType, address map, and address
 Outputs: Normalized address
 *------------------------------------------------------------------*/
UINT64
normalizeMod3 (
  DfType *dfType,
  UINT32 *dramAddressMapRegs,
  UINT64 addr
  )
{
  UINT64 normAddr;
  UINT32 hashPA;
  UINT32 intlvHashCtl, hashIntlvCtl2M, hashIntlvCtl1G;
  UINT32 intLvMode;
  UINT32 intLvAddrBit;
  UINT32 numInterleaveBits;
  UINT32 log2Addr64KSpace;
  // Calculate hashPA
  //   For intLvAddrBit==12:
  //     HashPA[2] = {14 ^ 22 ^ 31}
  //     HashPA[1] = {13 ^ 21 ^ 30}
  //     HashPA[0] = {12 ^ 15 ^ 23 ^ 32}
  //     ModId[1:0] = (HashPA[2:1] == 3) ? PA[n:15] % 3 : HashPA[2:1]
  //     CsSelect[2:0] = {ModId[1:0], HashPA[0])
  //   For intLvAddrBit==11:
  //     HashPA[2] = {13 ^ 22 ^ 31}
  //     HashPA[1] = {12 ^ 21 ^ 30}
  //     HashPA[0] = {11 ^ 14 ^ 23 ^ 32}
  //     ModId[1:0] = (HashPA[2:1] == 3) ? PA[n:14] % 3 : HashPA[2:1]
  //     CsSelect[2:0] = {ModId[1:0], HashPA[0])
  intLvAddrBit = decodeDramIntLvAddrBit (dfType, dramAddressMapRegs);
  intlvHashCtl = getIntlvHashCtlBits (dfType, dramAddressMapRegs);
  hashIntlvCtl2M = ((intlvHashCtl & 0x2) != 0) ? 1 : 0;
  hashIntlvCtl1G = ((intlvHashCtl & 0x4) != 0) ? 1 : 0;
  hashPA = (UINT32)((getBit64 (intLvAddrBit + 2, addr) ^
                     (getBit64 (22, addr) & hashIntlvCtl2M) ^
                     (getBit64 (31, addr) & hashIntlvCtl1G))
                    << 2);
  hashPA += (UINT32)((getBit64 (intLvAddrBit + 1, addr) ^
                      (getBit64 (21, addr) & hashIntlvCtl2M) ^
                      (getBit64 (30, addr) & hashIntlvCtl1G))
                     << 1);
  hashPA += (UINT32)(getBit64 (intLvAddrBit, addr) ^
                     getBit64 (intLvAddrBit + 3, addr) ^
                     (getBit64 (23, addr) & hashIntlvCtl2M) ^
                     (getBit64 (32, addr) & hashIntlvCtl1G));
  intLvMode = decodeDramIntLvNumChan (dfType, dramAddressMapRegs);
  // The number of interleave bits is as if we rounded up to the next power of 2
  // For example, 6-channel interleaving takes out 3 bits (6->8, log2(8)=3)
  // To do this, we simply truncate the log2 and add 1.
  numInterleaveBits = ((INT32)log2 (getNumChannelFromDramIntLvMode (intLvMode))) +  1;
  // Pull out the three bits that we have as part of the interleave
  normAddr = removeBits64 (intLvAddrBit, (intLvAddrBit + numInterleaveBits - 1), addr);
  // if HashPA is 11x, then force on the two MSBs of the normalized address
  if (getBits (1, 2, hashPA) == 3)
  {
    log2Addr64KSpace = extractLog2Addr64KSpace (dfType, dramAddressMapRegs);
    // Force the two MSBs (as indicated by log2Addr64KSpace) to one.
    // Since log2Addr64KSpace is in units of 64KB (2^16) and in units of the entire
    // DRAM map, we first must "normalize" it (16-numInterleaveBits)
    // Then the shift is 2 less than this (to align to MSB-2)
    normAddr |= (((UINT64)3) << (14 - numInterleaveBits + log2Addr64KSpace));
  }
  return (normAddr);
}

/*------------------------------------------------------------------
 Function: normalizeDf4NP2
 Purpose: Helper function to just do the DF4 & DF4.5 non-power-of-two algorithm
 Inputs: dfType, address map, and address
 Outputs: Normalized address
 *------------------------------------------------------------------*/
UINT64 normalizeDf4NP2 (
  DfType *dfType,
  UINT32 *dramAddressMapRegs,
  UINT64 addr
  )
{
  UINT32 hashPA8;
  UINT64 normalizedAddr;
  UINT64 tempAddrA;
  UINT64 tempAddrB;
  UINT32 intLvMode;
  UINT32 intlvHashCtl, hashIntlvCtl64K, hashIntlvCtl2M, hashIntlvCtl1G;
  UINT32 shiftValue = 0;
  UINT32 modValue = 0;
  intLvMode = decodeDramIntLvNumChan (dfType, dramAddressMapRegs);
  intlvHashCtl = getIntlvHashCtlBits (dfType, dramAddressMapRegs);
  hashIntlvCtl64K = ((intlvHashCtl & 0x1) != 0) ? 1 : 0;
  hashIntlvCtl2M = ((intlvHashCtl & 0x2) != 0) ? 1 : 0;
  hashIntlvCtl1G = ((intlvHashCtl & 0x4) != 0) ? 1 : 0;
  switch (intLvMode)
  {
    case INTERLEAVE_MODE_DF4_NPS4_3CHAN_HASH:
      modValue = 3;
      shiftValue = 13;
      break;
    case INTERLEAVE_MODE_DF4_NPS2_6CHAN_HASH:
      modValue = 3;
      shiftValue = 12;
      break;
    case INTERLEAVE_MODE_DF4_NPS1_12CHAN_HASH:
      modValue = 3;
      shiftValue = 11;
      break;
    case INTERLEAVE_MODE_DF4_NPS2_5CHAN_HASH:
      modValue = 5;
      shiftValue = 13;
      break;
    case INTERLEAVE_MODE_DF4_NPS1_10CHAN_HASH:
      modValue = 5;
      shiftValue = 12;
      break;
    default:  //Should not be here. Since normalizeDf4NP2 will only be invoked if intLvMode is one of the above cases.
      return 0;
  }
  // get PA[n:14] / 3 or 5
  tempAddrA = getBits64 (14, 63, addr);
  tempAddrA = tempAddrA / modValue;
  // When socket interleaving is disabled, hashPA8 is stored as part of the NA
  // It will be just below the divided PA[n:14]
  // When socket interleaving is enabled, hashPA8 is "stored" in the CSID
  if (extractDramIntLvNumSkts (dfType, dramAddressMapRegs) == 0)
  {
    // Calculate hashPA8, as this is part of the normalized address
    // (unless it is socketID of the CSID)
    hashPA8 = (UINT32)(getBit64 (8, addr) ^
                       getBit64 (14, addr) ^
                       (getBit64 (16, addr) & hashIntlvCtl64K) ^
                       (getBit64 (21, addr) & hashIntlvCtl2M) ^
                       (getBit64 (30, addr) & hashIntlvCtl1G));
    tempAddrA = tempAddrA << 1;
    tempAddrA |= ((UINT64)hashPA8);
  }
  // Align PA[n:14] / modValue and the hashPA[8] into bit[shiftValue]
  tempAddrA = tempAddrA << shiftValue;
  // Now remove the bits that are part of the CSID
  // bit8 (always), bit 12 and bit 13 (based on the interleaving mode)
  tempAddrB = removeBits64 (8, 8, addr);
  // Keep around only the bits below the shiftValue
  //   This will remove bit 12 and bit 13 based on the interleaving mode.
  tempAddrB = getBits64 (0, (shiftValue - 1), tempAddrB);
  // Now combine:
  //   tempAddrA = PA[n:14] / modValue || hashPA8 (when necessary)
  //   tempAddrB = PA[13:9] (as appropriate) and [7:0]
  normalizedAddr = tempAddrA | tempAddrB;
  return (normalizedAddr);
}
/*------------------------------------------------------------------
 Function: normalizeDf45NP2
 Purpose: Helper function to just do the DF4.5 non-power-of-two algorithm
 Inputs: dfType, address map, and address
 Outputs: Normalized address
 *------------------------------------------------------------------*/
UINT64 normalizeDf45NP2 (
  DfType *dfType,
  UINT32 *dramAddressMapRegs,
  UINT64 addr
  )
{
  UINT64 normalizedAddr;
  UINT64 tempAddrA, tempAddrB, tempAddrC;
  UINT32 intLvMode;
  intLvMode = decodeDramIntLvNumChan (dfType, dramAddressMapRegs);
  tempAddrA = getBits64 (0, 7, addr);
  tempAddrB = 0;
  tempAddrC = 0;
  switch (intLvMode)
  {
    case INTERLEAVE_MODE_DF45_NPS0_24CHAN_1K_HASH:
      // SocketSel = HashPA[8]
      // Mod3[1:0] = {PA[MSB:13], 3'b0} % 3
      // ChSel = {Mod3[1:0], HashPA[12], HashPA[9]}
      // NormAddr = {PA[MSB:13] / 3, PA[11:10],PA[7:0]}
      tempAddrB = getBits64 (10, 11, addr) << 8;
      tempAddrC = (getBits64 (13, 63, addr)) / 3;
      tempAddrC = tempAddrC << 10;
      break;
    case INTERLEAVE_MODE_DF45_NPS0_24CHAN_2K_HASH:
      // SocketSel = HashPA[8]
      // Mod3[1:0] = {PA[MSB:14], 4'b0} % 3
      // ChSel = {Mod3[1:0], HashPA[13:12]}
      // NormAddr = {PA[MSB:14] / 3, PA[11:9], PA[7:0]}
      tempAddrB = getBits64 (9, 11, addr) << 8;
      tempAddrC = (getBits64 (14, 63, addr)) / 3;
      tempAddrC = tempAddrC << 11;
      break;
    case INTERLEAVE_MODE_DF45_NPS1_12CHAN_1K_HASH:
      // Mod3[1:0] = {PA[MSB:12], 2'b0} % 3
      // ChSel = {Mod3[1:0], HashPA[9:8]}
      // NormAddr = {PA[MSB:12] / 3, PA[11:10], PA[7:0]}
      tempAddrB = getBits64 (10, 11, addr) << 8;
      tempAddrC = (getBits64 (12, 63, addr)) / 3;
      tempAddrC = tempAddrC << 10;
      break;
    case INTERLEAVE_MODE_DF45_NPS1_12CHAN_2K_HASH:
      // Mod3[1:0] = {PA[MSB:13], 3'b0} % 3
      // ChSel = {Mod3[1:0], HashPA[12], HashPA[8]}
      // NormAddr = {{PA[MSB:13]} / 3, PA[11:9], PA[7:0]}
      tempAddrB = getBits64 (9, 11, addr) << 8;
      tempAddrC = (getBits64 (13, 63, addr)) / 3;
      tempAddrC = tempAddrC << 11;
      break;
    case INTERLEAVE_MODE_DF45_NPS2_6CHAN_1K_HASH:
      // Mod3[1:0] = {PA[MSB:12], PA[9], 1'b0} % 3
      // ChSel = {Mod3[1:0], HashPA[8]}
      // NormAddr = {{PA[MSB:12], PA[9]} / 3, PA[11:10], PA[7:0]}
      tempAddrB = getBits64 (10, 11, addr) << 8;
      tempAddrC = ((getBits64 (12, 63, addr) << 1) |
                   getBit64 (9, addr)) / 3;
      tempAddrC = tempAddrC << 10;
      break;
    case INTERLEAVE_MODE_DF45_NPS2_6CHAN_2K_HASH:
      // Mod3[1:0] = {PA[MSB:12], 2'd0} % 3
      // ChSel = {Mod3[1:0], HashPA[8]}
      // NormAddr = {PA[MSB:12] / 3, PA[11:9], PA[7:0]}
      tempAddrB = getBits64 (9, 11, addr) << 8;
      tempAddrC = (getBits64 (12, 63, addr)) / 3;
      tempAddrC = tempAddrC << 11;
      break;
    case INTERLEAVE_MODE_DF45_NPS4_3CHAN_1K_HASH:
      // Mod3[1:0] = {PA[MSB:12], PA[9:8]} % 3
      // ChSel = Mod3[1:0]
      // NormAddr = {{PA[MSB:12], PA[9:8]} / 3, PA[11:10], PA[7:0]}
      tempAddrB = getBits64 (10, 11, addr) << 8;
      tempAddrC = ((getBits64 (12, 63, addr) << 2) |
                   getBits64 (8, 9, addr)) / 3;
      tempAddrC = tempAddrC << 10;
      break;
    case INTERLEAVE_MODE_DF45_NPS4_3CHAN_2K_HASH:
      // Mod3[1:0] = {PA[MSB:12], PA[8], 1'b0]} % 3
      // ChSel = Mod3[1:0]
      // NormAddr = {{PA[MSB:12], PA[8]} / 3, PA[11:9], PA[7:0]}
      tempAddrB = getBits64 (9, 11, addr) << 8;
      tempAddrC = ((getBits64 (12, 63, addr) << 1) |
                   getBit64 (8, addr)) / 3;
      tempAddrC = tempAddrC << 11;
      break;
    case INTERLEAVE_MODE_DF45_NPS1_10CHAN_1K_HASH:
      // Mod5[2:0] = {PA[MSB:12], PA[9], 1'b0} % 5
      // ChSel = {Mod5[2:0], HashPA[8]}
      // NormAddr = {{PA[MSB:12], PA[9]} / 5, PA[11:10], PA[7:0]}
      tempAddrB = getBits64 (10, 11, addr) << 8;
      tempAddrC = ((getBits64 (12, 63, addr) << 1) |
                   getBit64 (9, addr)) / 5;
      tempAddrC = tempAddrC << 10;
      break;
    case INTERLEAVE_MODE_DF45_NPS1_10CHAN_2K_HASH:
      // Mod5[2:0] = {PA[MSB:12], 2'd0} % 5
      // ChSel = {Mod5[2:0], HashPA[8]}
      // Norm Addr = {PA[MSB:12] / 5, PA[11:9], PA[7:0]}
      tempAddrB = getBits64 (9, 11, addr) << 8;
      tempAddrC = (getBits64 (12, 63, addr)) / 5;
      tempAddrC = tempAddrC << 11;
      break;
    case INTERLEAVE_MODE_DF45_NPS2_5CHAN_1K_HASH:
      // Mod5[2:0] = {PA[MSB:12], PA[9:8]} % 5
      // ChSel = Mod5[2:0]
      // NormAddr = {{PA[MSB:12], PA[9:8]} / 5, PA[11:10], PA[7:0]}
      tempAddrB = getBits64 (10, 11, addr) << 8;
      tempAddrC = ((getBits64 (12, 63, addr) << 2) |
                   getBits64 (8, 9, addr)) / 5;
      tempAddrC = tempAddrC << 10;
      break;
    case INTERLEAVE_MODE_DF45_NPS2_5CHAN_2K_HASH:
      // Mod5[2:0] = {PA[MSB:12], PA[8], 1'b0} % 5
      // ChSel = Mod5[2:0]
      // NormAddr = {{PA[MSB:12], PA[8]} / 5, PA[11:9], PA[7:0]}
      tempAddrB = getBits64 (9, 11, addr) << 8;
      tempAddrC = ((getBits64 (12, 63, addr) << 1) |
                   getBit64 (8, addr)) / 5;
      tempAddrC = tempAddrC << 11;
      break;
  }
  normalizedAddr = tempAddrA | tempAddrB | tempAddrC;
  return (normalizedAddr);
}
/*------------------------------------------------------------------
 Function: normalizeAddr
 Purpose: Helper function to do the address normalization
 Inputs: dfType, address map, and address
 Outputs: Normalized address
 *------------------------------------------------------------------*/
UINT64
normalizeAddr (
  DfType *dfType,
  UINT32 *dramAddressMapRegs,
  UINT64 addr
  )
{
  UINT64 normalizedAddr = 0;;
  UINT32 intLvAddrBit = 0;;
  UINT32 intLvMode = 0;;
  UINT32 numInterleaveBits = 0;;
  UINT32 numBitsToRemove = 0;
//  BOOLEAN debugDataValid = FALSE;
  // Note that most sanity checks were already done in checkDramHit. There is no need to repeat them again.
  intLvMode = decodeDramIntLvNumChan (dfType, dramAddressMapRegs);

  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "normalizeAddr, dfType=%d, addr=0x%016lX\n", dfType->dfVersion, addr);
  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "dramBaseReg=0x%08X, dramLimitReg=0x%08X dramIntlvReg=0x%08X, dramCtlReg=0x%08X\n",
      dramAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET], dramAddressMapRegs[ADDR_MAP_ARRAY_LIMIT_OFFSET],
      dramAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET], dramAddressMapRegs[ADDR_MAP_ARRAY_CTL_OFFSET]);
  switch (intLvMode)
  {
    case INTERLEAVE_MODE_NONE:
      normalizedAddr = addr;
      break;
    case INTERLEAVE_MODE_2CHAN_NOHASH:
    case INTERLEAVE_MODE_4CHAN_NOHASH:
    case INTERLEAVE_MODE_8CHAN_NOHASH:
    case INTERLEAVE_MODE_16CHAN_NOHASH:
    case INTERLEAVE_MODE_32CHAN_NOHASH:
    case INTERLEAVE_MODE_MI2_HASH_8CHAN:
    case INTERLEAVE_MODE_MI2_HASH_16CHAN:
    case INTERLEAVE_MODE_MI2_HASH_32CHAN:
    case INTERLEAVE_MODE_MI3_HASH_8CHAN:
    case INTERLEAVE_MODE_MI3_HASH_16CHAN:
    case INTERLEAVE_MODE_MI3_HASH_32CHAN:
    case INTERLEAVE_MODE_ZP_2CHAN_HASH:
      // The system address bit consists of:
      //      XXXXXXXXXIIIYYY
      // where III is the ID for this CS, and XXXXXX and YYYYY are the address bits used
      // in the normalized address.
      // Simply remove the number of channel bits (III)
      numInterleaveBits = log2 (getNumChannelFromDramIntLvMode (intLvMode));
      numInterleaveBits += extractDramIntLvNumDies (dfType, dramAddressMapRegs);
      numInterleaveBits += extractDramIntLvNumSkts (dfType, dramAddressMapRegs);
      intLvAddrBit = decodeDramIntLvAddrBit (dfType, dramAddressMapRegs);
      // Pull out the III (from intLvAddrBit to intLvAddrBit + numInterleavedBits-1)
      normalizedAddr = removeBits64 (intLvAddrBit, (intLvAddrBit + numInterleaveBits - 1), addr);
//      debugDataValid = FALSE;
      break;
    case INTERLEAVE_MODE_DF3_COD4_2CHAN_HASH:
    case INTERLEAVE_MODE_DF3_COD2_4CHAN_HASH:
    case INTERLEAVE_MODE_DF3_COD1_8CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS4_2CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS2_4CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS1_8CHAN_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_16CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS4_2CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS2_4CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_8CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS4_2CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS2_4CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_8CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_16CHAN_2K_HASH:
      // The system address bit consists of:
      //      XXXXXXIIZZZIYYY
      //   where III is the ID for this CS, and XXXXXXZZZYYYYY are the address bits used
      //   in the normalized address.
      //   log2(number of channels) tells us how many "I" bits there are.
      //   intLvAddrBit tells us how many "Y" bits there are (where the first "I" starts)
      //   The remaining III bits are higher up starting at bit 12.
      // Pull out the low-order I bit (intLvAddrBit)
      // In the 1K region hash, it is actually bits bits 9:8.
      intLvAddrBit = decodeDramIntLvAddrBit (dfType, dramAddressMapRegs);
      numBitsToRemove = 1;
      if ((intLvMode == INTERLEAVE_MODE_DF45_NPS1_16CHAN_1K_HASH) ||
          (intLvMode == INTERLEAVE_MODE_DF45_NPS2_4CHAN_1K_HASH) ||
          (intLvMode == INTERLEAVE_MODE_DF45_NPS1_8CHAN_1K_HASH))
      {
        numBitsToRemove = 2;
      }
      // Pull out the low-order I bits
      normalizedAddr = removeBits64 (intLvAddrBit, intLvAddrBit + numBitsToRemove - 1, addr);
      // Pull out the high-order I bits (intLvAddrBit)
      numInterleaveBits = log2 (getNumChannelFromDramIntLvMode (intLvMode));
      // In Turin, the number of channels encoded in IntLvNumChan already includes socket interleaving.
      if (dfType->dfVersion != DF_TYPE_DF4POINT5)
      {
        numInterleaveBits += extractDramIntLvNumSkts (dfType, dramAddressMapRegs); // DF4 only
      }

      if (numInterleaveBits > numBitsToRemove)
      {
        // Since we pulled out one or two bits already, the remaining III bits are also shifted over
        // If we removed 1 bit, we now remove bits 11 to 11+numInterLeaveBits-1-1)
        //    first -1 because we have already removed one bit
        //    second -1 to get the "numInterLeaveBits" to get the top-most bit
        // If we removed 2 bits, we now remove bits 10 to 10+numInterLeaveBits-2-1)
        //    first -2 because we have already removed two bits
        //    second -1 to get the "numInterLeaveBits" to get the top-most bit
        // We can make that equation (12 - numBitsToRemove) + numInterleaveBIts - numBitsToRemove - 1)
        // So the upper bit is 11-(2*numBitsToRemove)+numInterleaveBits
        normalizedAddr = removeBits64 (12 - numBitsToRemove, (11 - numBitsToRemove - numBitsToRemove + numInterleaveBits), normalizedAddr);
      }
//      debugDataValid = FALSE;
      break;
    case INTERLEAVE_MODE_DF3_6CHAN:
      normalizedAddr = normalizeMod3 (dfType, dramAddressMapRegs, addr);
      break;
    case INTERLEAVE_MODE_DF4_NPS4_3CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS2_6CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS1_12CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS2_5CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS1_10CHAN_HASH:
      normalizedAddr = normalizeDf4NP2 (dfType, dramAddressMapRegs, addr);
      break;
    case INTERLEAVE_MODE_DF45_NPS0_24CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS4_3CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS2_6CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_12CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS2_5CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_10CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS4_3CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS2_6CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_12CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS0_24CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS2_5CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_10CHAN_2K_HASH:
      normalizedAddr = normalizeDf45NP2 (dfType, dramAddressMapRegs, addr);
      break;
    default:
      ASSERT (FALSE);
  }
//  if (debugDataValid) IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "intLvAddrBit=%d, numInterleaveBits=%d\n", intLvAddrBit, numInterleaveBits);
  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "addr 0x%016lX normalized to 0x%016lX\n", addr, normalizedAddr);
  return (normalizedAddr);
}

/*------------------------------------------------------------------
 Function: deNormalizeAddrMod3
 Purpose: Helper function to do the address denormalization
 Inputs: dfType, csFabricId (already converted to logical), address map, and address
 Outputs: Normalized address
 *------------------------------------------------------------------*/
UINT64
deNormalizeAddrMod3 (
  DfType *dfType,
  UINT32 logicalCsFabricId,
  UINT32 *dramAddressMapRegs,
  UINT64 normAddr
  )
{
  UINT64 deNormAddr = 0;
  UINT64 tempAddrA, tempAddrB;
  UINT32 intLvMode;
  UINT32 intLvAddrBit;
  UINT32 numInterleaveBits;
  UINT32 csId;
  UINT32 msbNormAddrBits;
  UINT32 physicalAddressMSB;
  UINT32 addressMod;
  UINT32 log2Addr64KSpace;
  intLvMode = decodeDramIntLvNumChan (dfType, dramAddressMapRegs);
  intLvAddrBit = decodeDramIntLvAddrBit (dfType, dramAddressMapRegs);
  // Calculate the CS ID and adjust the MSBs appropriately
  // Also make room in the address for where the CS ID goes at the same time.
  csId = logicalCsFabricId & getComponentIdMask (dfType);
  switch (intLvMode)
  {
    case INTERLEAVE_MODE_DF3_6CHAN:
      // Check if two MSBs (as indicated by log2Addr64KSpace) are one.
      // 2^(log2Addr64KSpace+16) is the size of the DRAM address space rounded up to the nearest power of 2.
      // In other words, if you have a 6*1GB address space, the total size is 6GB. Log2(8GB) is 33, so
      // the value of log2Addr64KSpace is 17 (33-16=17).
      // numInterleaveBits is log2(number of channels rounded up to the nearest power of 2)
      // Therefore, 2^(log2Addr64KSpace+16-numInterleaveBits) is the size of the normalized address space.
      // and the 2 MSB bits are (log2Addr64KSpace+15-numInterleaveBits and log2Addr64KSpace+14+numInterleaveBits)
      numInterleaveBits = ((INT32) log2 (getNumChannelFromDramIntLvMode (intLvMode))) +  1;
      log2Addr64KSpace = extractLog2Addr64KSpace (dfType, dramAddressMapRegs);
      msbNormAddrBits = (UINT32)getBits64 ((log2Addr64KSpace + 14 - numInterleaveBits),
                        (log2Addr64KSpace + 15 - numInterleaveBits),
                        normAddr);
      // If the two MSB address bits are 11b, then the logical CSID is now 6 or 7,
      // and the normalized address is adjusted based on the mod3
      if (msbNormAddrBits == 3)
      {
        // Remove the 2 MSBs of 11b.
        tempAddrB = getBits64 (intLvAddrBit, (log2Addr64KSpace + 13 - numInterleaveBits), normAddr);
        // Calculate the mod3
        addressMod = tempAddrB % 3;
        // Calculate the physical address MSB based on the mod 3, the logical CS number and shift amount
        physicalAddressMSB = ((1 + ((intLvAddrBit + log2Addr64KSpace + 1) & 1)) *
                              (3 - addressMod + getBits (1, 2, csId))) % 3;
        // Remember that tempAddrB is already just bits n:(intLvAddrBit+numInterleaveBits)
        tempAddrB |= (((UINT64)physicalAddressMSB) << (log2Addr64KSpace + 14 - numInterleaveBits - intLvAddrBit));
        // csId = [1,1,csId[0]]
        csId = (3 << 1) + (csId & 1);
      } else
      {
        tempAddrB = getBits64 (intLvAddrBit, 63, normAddr);
      }
      // tempAddrA is all the normalized bits below the interleave bit.
      tempAddrA = getBits64 (0, (intLvAddrBit - 1), normAddr);
      // align tempAddrB
      tempAddrB = tempAddrB << (intLvAddrBit + numInterleaveBits);
      deNormAddr = tempAddrA | tempAddrB;
      break;
  }
  // Insert the unhashed CS ID into the appropriate bits of the address
  switch (intLvMode)
  {
    case INTERLEAVE_MODE_DF3_6CHAN:
      // csID is inserted at bits (intLvAddrBit+numIntLvBit-1) : intLvAddrBit
      deNormAddr |= (((UINT64)csId) << intLvAddrBit);
      break;
  }

  return (deNormAddr);
}

/*------------------------------------------------------------------
 Function: deNormalizeAddrDf4Np2
 Purpose: Helper function to do the address denormalization
 Inputs: dfType, csFabricId (already converted to logical), address map, and address
 Outputs: Normalized address
 *------------------------------------------------------------------*/
UINT64 deNormalizeAddrDf4Np2 (
  DfType *dfType,
  UINT32 logicalCsFabricId,
  UINT32 *dramAddressMapRegs,
  UINT64 normAddr
  )
{
  UINT64 deNormAddr;
  UINT64 tempAddrA, tempAddrB;
  UINT32 intLvMode;
  UINT32 modValue = 0;
  UINT32 shiftValue = 0;
  UINT32 hashPA8;
  UINT32 hashedBit;
  UINT32 group;
  UINT32 groupOffset;
  UINT32 numSocketsInterleaved;
  UINT32 intlvHashCtl, hashIntlvCtl64K, hashIntlvCtl2M, hashIntlvCtl1G;
  UINT32 logicalCsOffset, mask;
  intLvMode = decodeDramIntLvNumChan (dfType, dramAddressMapRegs);
  intlvHashCtl = getIntlvHashCtlBits (dfType, dramAddressMapRegs);
  hashIntlvCtl64K = ((intlvHashCtl & 0x1) != 0) ? 1 : 0;
  hashIntlvCtl2M = ((intlvHashCtl & 0x2) != 0) ? 1 : 0;
  hashIntlvCtl1G = ((intlvHashCtl & 0x4) != 0) ? 1 : 0;
  switch (intLvMode)
  {
    case INTERLEAVE_MODE_DF4_NPS4_3CHAN_HASH:
      modValue = 3;
      shiftValue = 13;
      break;
    case INTERLEAVE_MODE_DF4_NPS2_6CHAN_HASH:
      modValue = 3;
      shiftValue = 12;
      break;
    case INTERLEAVE_MODE_DF4_NPS1_12CHAN_HASH:
      modValue = 3;
      shiftValue = 11;
      break;
    case INTERLEAVE_MODE_DF4_NPS2_5CHAN_HASH:
      modValue = 5;
      shiftValue = 13;
      break;
    case INTERLEAVE_MODE_DF4_NPS1_10CHAN_HASH:
      modValue = 5;
      shiftValue = 12;
      break;
    default:  //Should not be here. Since deNormalizeAddrDf4Np2 will only be invoked if intLvMode is one of the above cases.
      return 0;
  }
  numSocketsInterleaved = extractDramIntLvNumSkts (dfType, dramAddressMapRegs) + 1;
  // Remember the hashPA bit, which was stored in NA[shiftValue] or CS[SocketId]
  // If it was stored in NA, shift it out.
  if (numSocketsInterleaved == 1)
  {
    hashPA8 = (UINT32)getBit64 (shiftValue, normAddr);
    tempAddrA = removeBits64 (shiftValue, shiftValue, normAddr);
  } else
  {
    hashPA8 = (logicalCsFabricId & getSocketIdMask (dfType)) >> getSocketIdShift (dfType);
    tempAddrA = normAddr;
  }
  // Now make room for the real PA[8] to be inserted
  tempAddrA = expandBits64 (8, 1, tempAddrA);
  // Now make room in the address for other bits from the CS
  if ((intLvMode == INTERLEAVE_MODE_DF4_NPS2_6CHAN_HASH) ||
      (intLvMode == INTERLEAVE_MODE_DF4_NPS1_10CHAN_HASH))
  {
    tempAddrA = expandBits64 (13, 1, tempAddrA);
  }
  if (intLvMode == INTERLEAVE_MODE_DF4_NPS1_12CHAN_HASH)
  {
    tempAddrA = expandBits64 (12, 2, tempAddrA);
  }
  // Keep around these low order bits
  tempAddrA = getBits64 (0, 13, tempAddrA);
  // Get NA[n:14] * 3 or NA[n:14] * 5 (NA[14] actually depends on the interleave mode)
  tempAddrB = getBits64 ((shiftValue + 1 - log2 (numSocketsInterleaved)), 63, normAddr);
  tempAddrB *= modValue;
  // Calculate your group (quadrant in mod3 and side in mod5) and your offset within that group
  mask = getComponentIdMask (dfType);
  logicalCsOffset = (logicalCsFabricId & mask) - (extractDstFabricId (dfType, dramAddressMapRegs) & mask);
  group = logicalCsOffset / modValue;
  groupOffset = logicalCsOffset % modValue;
  // Add in the remainder from the division (which was stored in CS ID groupOffset)
  if (hashPA8)
  {
    if (groupOffset == 0)
    {
      groupOffset = modValue - 1;
    } else
    {
      groupOffset--;
    }
  }
  tempAddrB += (UINT64)groupOffset;
  // Realign this to PA[n:14]
  tempAddrB = tempAddrB << 14;
  // Merge together the PA[n:14] (tempAddrB) and PA[13:0] (tempAddrB)
  deNormAddr = tempAddrA | tempAddrB;
  // Now calculate the hash value for PA[8, 12, and 13]
  // and place them in there. The bits will be zero, so you can just OR it in.
  // bit 8 = hashPA8 ^ 14 ^ 16 ^ 21 ^ 30
  hashedBit = (UINT32)(hashPA8 ^
                       getBit64 (14, deNormAddr) ^
                       (getBit64 (16, deNormAddr) & hashIntlvCtl64K) ^
                       (getBit64 (21, deNormAddr) & hashIntlvCtl2M) ^
                       (getBit64 (30, deNormAddr) & hashIntlvCtl1G)
                      );
  // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "hashedBit=%d\n", hashedBit);
  deNormAddr = deNormAddr | (((UINT64)hashedBit) << 8);
  if (intLvMode == INTERLEAVE_MODE_DF4_NPS1_12CHAN_HASH)
  {
    // bit 12 = group[0] ^ 17 ^ 22 ^ 31
    // bit 13 = group[1] ^ 18 ^ 23 ^ 32
    hashedBit = (UINT32)(getBit (0, group) ^
                         (getBit64 (17, deNormAddr) & hashIntlvCtl64K) ^
                         (getBit64 (22, deNormAddr) & hashIntlvCtl2M) ^
                         (getBit64 (31, deNormAddr) & hashIntlvCtl1G)
                        );
    hashedBit += (UINT32)((getBit (1, group) ^
                           (getBit64 (18, deNormAddr) & hashIntlvCtl64K) ^
                           (getBit64 (23, deNormAddr) & hashIntlvCtl2M) ^
                           (getBit64 (32, deNormAddr) & hashIntlvCtl1G)
                          ) << 1);
    // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "hashedBit=%d\n", hashedBit);
    deNormAddr = deNormAddr | (((UINT64)hashedBit) << 12);
  } else if ((intLvMode == INTERLEAVE_MODE_DF4_NPS2_6CHAN_HASH) ||
             (intLvMode == INTERLEAVE_MODE_DF4_NPS1_10CHAN_HASH))
  {
    // bit 13 = group[0] ^ 18 ^ 23 ^ 32
    hashedBit = (UINT32)(getBit (0, group) ^
                         (getBit64 (18, deNormAddr) & hashIntlvCtl64K) ^
                         (getBit64 (23, deNormAddr) & hashIntlvCtl2M) ^
                         (getBit64 (32, deNormAddr) & hashIntlvCtl1G)
                        );
    // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "hashedBit=%d\n", hashedBit);
    deNormAddr = deNormAddr | (((UINT64)hashedBit) << 13);
  }
  return (deNormAddr);
}
/*------------------------------------------------------------------
 Function: deNormalizeAddrDf45Np2
 Purpose: Helper function to do the address denormalization for DF4.5
 Inputs: dfType, csFabricId (already converted to logical), address map, and address
 Outputs: Normalized address
 *------------------------------------------------------------------*/
UINT64 deNormalizeAddrDf45Np2 (
  DfType *dfType,
  UINT32 logicalCsFabricId,
  UINT32 *dramAddressMapRegs,
  UINT64 normAddr
  )
{
  UINT64 baseDeNormAddr, divAddr = 0, thisAddrNormalizedAddress;
  UINT64 deNormAddrWithRemainderWithoutBase, currentSpaWithoutBase, currentSpaWithBase;
  UINT64 dramBaseAddr, dramHoleBase, dramHoleSize;
  UINT32 hashedBit, rehashVector = 0, thisAddrLogicalCsFabricId;
  UINT32 droppedRemainder, modValue = 0;
  UINT32 testPermutation, numPermutations = 0;
  UINT32 intlvHashCtl, hashIntlvCtl64K, hashIntlvCtl2M, hashIntlvCtl1G, hashIntlvCtl1T;
  UINT32 intLvMode, lgcyMmioHoleEn, dstFabricId;

  //IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "DEBUG>> deNormalizeAddrDf45Np2 (0x%x 0x%016lX)\n", logicalCsFabricId, normAddr);

  intLvMode = decodeDramIntLvNumChan (dfType, dramAddressMapRegs);

  intlvHashCtl = getIntlvHashCtlBits (dfType, dramAddressMapRegs);
  hashIntlvCtl64K = ((intlvHashCtl & 0x1) != 0) ? 1 : 0;
  hashIntlvCtl2M = ((intlvHashCtl & 0x2) != 0) ? 1 : 0;
  hashIntlvCtl1G = ((intlvHashCtl & 0x4) != 0) ? 1 : 0;
  hashIntlvCtl1T = ((intlvHashCtl & 0x10) != 0) ? 1 : 0;

  dramBaseAddr = extractDramBaseAddr (dfType, dramAddressMapRegs);
  lgcyMmioHoleEn = extractLgcyMmioHoleEn (dfType, dramAddressMapRegs);
  if (lgcyMmioHoleEn)
  {
    dramHoleBase = getDramHoleBase (dfType);
    dramHoleSize = (((UINT64)1) << 32) - dramHoleBase;
  } else
  {
    dramHoleBase = 0;
    dramHoleSize = 0;
  }
  dstFabricId = extractDstFabricId (dfType, dramAddressMapRegs);

  /*
   * Explanation for the method to denormalize.
   *
   * We will take the 3-channel 1K algorithm as an example.
   *
   * Terms:
   *   PA = Raw SPA
   *   PACS = SPA - base - hole
   *          Note that PACS is the same as PA for all bits below bit 28 because the base and the hole are always on 256MB boundaries.
   *   PALR = the result of taking "Div3(PA[54:12])", dropping the remainder. This value is "stored" in the NA. We can restore PALR by
   *          taking those bits of the NA, multiplying by 3 and adding in the base + MMIOhole, but it is "less the remainder" so we refer to it as "LR".
   *
   * PA is used to do all hashPA calculations and MOD calculations for selecting the channel.
   * PACS is used to create the normalized address.
   * PALR is "stored" in the NA.
   *
   * In the code, we only know the NA and the channel number, and we are asked to recover PA.
   *
   * NA = {Div3(PACS[54:12]), PACS[11:10], PACS[7:0]}
   *   From this, we "recover" PALR[54:12] by taking NA[MSB:10] (the Div3(PACS[54:12])) and multiplying by 3 and adding in the base + MMIOhole
   *   But in this, PALR[54:12] differs from the "actual" PA[54:12] because we have lost the remainder.
   *
   * Now as well, the channel selection is taken from:
   *   Chan[1:0] = mod3 (PA[54:12], PA[9:8])
   *
   * From the normalized address, we don't know what PA[9:8] is at all. It has been "lost". So we have to resurrect that.
   * We know the result of the mod3 operation to create Chan[1:0] so we part of this information.
   * But we don't actually know the exact PA[54:12] that was used to create the channel. We only know the PALR[54:12]
   *
   * Put simply:
   *   We know the NA and the channel. But how can we recover" PA[54:12] and PA[9:8] if we only know PALR[54:12] from the NA?
   *
   * Fundamentally, we have two equations and two unknowns (PA[9:8] and the remainder). We know the results of these two equations (NA and channel).
   * But in the DF3.5 and DF4 mod algorithms, because the PACS was used for all equations, we could easily solve those two equations.
   * The "mod" is part of the channel and that is the remainder. So we can recapture PA from PALR + mod.
   * But here, in one equation we use PACS and in the other equation it uses PA.
   * This makes the equations more difficult and it is not an easy solution.
   *
   * At a single channel/NA, it is hard to predict (or rather calculate) which of the combinations of PA[9:8] and PA[54:12]
   * created your channel/NA because the relationship isn't simple - it would vary based on what your base is for example.
   * But you "know" that there is only one combinations that solves both equations.
   *
   * So the resolution is to "search" for the answer. You have a limited number of remainders that you have to add to the Div3.
   * You have a limited number of PA[9:8] values that you have to use to create the result of Chan[1:0] = mod3 (PA[54:12], PA[9:8]).
   *   There may be more than one solution to Chan[1:0]
   *     Given a single channel), there may be two PA[9:8] bits that have the same mod3 results to create your channel.
   *     But note that for these, the PA[54:12] would have to be different and thus the NA would be ifferent.
   *   There obviously are three unique "dropped remainder" values that could have been used to create the NA.
   *     These dropped remainders are related to the mod3 value (in a complicated way sincde one uses PA and one usees PACS).
   *     But for each one of these, we know that they would have mapped to two different channels.
   *
   * So what we do is to go through all permutations of the dropped remainder and all permutations of the dropped PA[9:8]
   * and find the one that gives your channel and your NA. That is the solution to the problem.
   *
   * The other complexity is when the Div algorithm takes a non-consecutive group of address bits. For example, the 6-channel
   * 1K hash has "{PA[MSB:12], PA[9]} / 3". When we add in the "dropped remainder", this has to be done on that value and
   * then we split it.
   *
   */

  // Everyone has normAddr[7:0] passed through.
  baseDeNormAddr = getBits64 (0, 7, normAddr);

  switch (intLvMode)
  {
    /***************************/
    case INTERLEAVE_MODE_DF45_NPS0_24CHAN_1K_HASH:
      // SocketSel = HashPA[8]
      // Mod3[1:0] = {PA[MSB:13], 3'b0} % 3
      // ChSel = {Mod3[1:0], HashPA[12], HashPA[9]}
      // NormAddr = {PA[MSB:13] / 3, PA[11:10], PA[7:0]}
      modValue = 3;
      numPermutations = 8;
      rehashVector = (3 << 8) | (1 << 12);
      baseDeNormAddr |= (getBits64 (8, 9, normAddr) << 10);
      divAddr = getBits64 (10, 63, normAddr);
      break;

      /***************************/
    case INTERLEAVE_MODE_DF45_NPS0_24CHAN_2K_HASH:
      // SocketSel = HashPA[8]
      // Mod3[1:0] = {PA[MSB:14], 4'b0} % 3
      // ChSel = {Mod3[1:0], HashPA[13:12]}
      // NormAddr = {PA[MSB:14] / 3, PA[11:9], PA[7:0]}
      modValue = 3;
      numPermutations = 8;
      rehashVector = (1 << 8) | (3 << 12);
      baseDeNormAddr |= (getBits64 (8, 10, normAddr) << 9);
      divAddr = getBits64 (11, 63, normAddr);
      break;

      /***************************/
    case INTERLEAVE_MODE_DF45_NPS1_12CHAN_1K_HASH:
      // Mod3[1:0] = {PA[MSB:12], 2'b0} % 3
      // ChSel = {Mod3[1:0], HashPA[9:8]}
      // NormAddr = {PA[MSB:12] / 3, PA[11:10], PA[7:0]}
      modValue = 3;
      numPermutations = 4;
      rehashVector = (3 << 8);
      baseDeNormAddr |= (getBits64 (8, 9, normAddr) << 10);
      divAddr = getBits64 (10, 63, normAddr);
      break;

      /***************************/
    case INTERLEAVE_MODE_DF45_NPS1_12CHAN_2K_HASH:
      // Mod3[1:0] = {PA[MSB:13], 3'b0} % 3
      // ChSel = {Mod3[1:0], HashPA[12], HashPA[8]}
      // NormAddr = {{PA[MSB:13]} / 3, PA[11:9], PA[7:0]}
      modValue = 3;
      numPermutations = 4;
      rehashVector = (1 << 8) | (1 << 12);
      baseDeNormAddr |= (getBits64 (8, 10, normAddr) << 9);
      divAddr = getBits64 (11, 63, normAddr);
      break;

      /***************************/
    case INTERLEAVE_MODE_DF45_NPS2_6CHAN_1K_HASH:
      // Mod3[1:0] = {PA[MSB:12], PA[9], 1'b0} % 3
      // ChSel = {Mod3[1:0], HashPA[8]}
      // NormAddr = {{PA[MSB:12], PA[9]} / 3, PA[11:10], PA[7:0]}
      modValue = 3;
      numPermutations = 2;
      rehashVector = (1 << 8);
      baseDeNormAddr |= (getBits64 (8, 9, normAddr) << 10);
      divAddr = getBits64 (10, 63, normAddr);
      break;

      /***************************/
    case INTERLEAVE_MODE_DF45_NPS2_6CHAN_2K_HASH:
      // Mod3[1:0] = {PA[MSB:12], 2'd0} % 3
      // ChSel = {Mod3[1:0], HashPA[8]}
      // NormAddr = {PA[MSB:12] / 3, PA[11:9], PA[7:0]}
      modValue = 3;
      numPermutations = 2;
      rehashVector = (1 << 8);
      baseDeNormAddr |= (getBits64 (8, 10, normAddr) << 9);
      divAddr = getBits64 (11, 63, normAddr);
      break;

      /***************************/
    case INTERLEAVE_MODE_DF45_NPS4_3CHAN_1K_HASH:
      // Mod3[1:0] = {PA[MSB:12], PA[9:8]} % 3
      // ChSel = Mod3[1:0]
      // NormAddr = {{PA[MSB:12], PA[9:8]} / 3, PA[11:10], PA[7:0]}
      modValue = 3;
      numPermutations = 4;
      rehashVector = 0;
      baseDeNormAddr |= (getBits64 (8, 9, normAddr) << 10);
      divAddr = getBits64 (10, 63, normAddr);
      break;

      /***************************/
    case INTERLEAVE_MODE_DF45_NPS4_3CHAN_2K_HASH:
      // Mod3[1:0] = {PA[MSB:12], PA[8], 1'b0]} % 3
      // ChSel = Mod3[1:0]
      // NormAddr = {{PA[MSB:12], PA[8]} / 3, PA[11:9], PA[7:0]}
      modValue = 3;
      numPermutations = 2;
      rehashVector = 0;
      baseDeNormAddr |= (getBits64 (8, 10, normAddr) << 9);
      divAddr = getBits64 (11, 63, normAddr);
      break;

      /***************************/
    case INTERLEAVE_MODE_DF45_NPS1_10CHAN_1K_HASH:
      // Mod5[2:0] = {PA[MSB:12], PA[9], 1'b0} % 5
      // ChSel = {Mod5[2:0], HashPA[8]}
      // NormAddr = {{PA[MSB:12], PA[9]} / 5, PA[11:10], PA[7:0]}
      modValue = 5;
      numPermutations = 2;
      rehashVector = (1 << 8);
      baseDeNormAddr |= (getBits64 (8, 9, normAddr) << 10);
      divAddr = getBits64 (10, 63, normAddr);
      break;

      /***************************/
    case INTERLEAVE_MODE_DF45_NPS1_10CHAN_2K_HASH:
      // Mod5[2:0] = {PA[MSB:12], 2'd0} % 5
      // ChSel = {Mod5[2:0], HashPA[8]}
      // Norm Addr = {PA[MSB:12] / 5, PA[11:9], PA[7:0]}
      modValue = 5;
      numPermutations = 2;
      rehashVector = (1 << 8);
      baseDeNormAddr |= (getBits64 (8, 10, normAddr) << 9);
      divAddr = getBits64 (11, 63, normAddr);
      break;

      /***************************/
    case INTERLEAVE_MODE_DF45_NPS2_5CHAN_1K_HASH:
      // Mod5[2:0] = {PA[MSB:12], PA[9:8]} % 5
      // ChSel = Mod5[2:0]
      // NormAddr = {{PA[MSB:12], PA[9:8]} / 5, PA[11:10], PA[7:0]}
      modValue = 5;
      numPermutations = 4;
      rehashVector = 0;
      baseDeNormAddr |= (getBits64 (8, 9, normAddr) << 10);
      divAddr = getBits64 (10, 63, normAddr);
      break;

      /***************************/
    case INTERLEAVE_MODE_DF45_NPS2_5CHAN_2K_HASH:
      // Mod5[2:0] = {PA[MSB:12], PA[8], 1'b0} % 5
      // ChSel = Mod5[2:0]
      // NormAddr = {{PA[MSB:12], PA[8]} / 5, PA[11:9], PA[7:0]}
      modValue = 5;
      numPermutations = 2;
      rehashVector = 0;
      baseDeNormAddr |= (getBits64 (8, 10, normAddr) << 9);
      divAddr = getBits64 (11, 63, normAddr);
      break;

    default:
      ASSERT (FALSE);
  }

  // Now multiply divAddr by 3/5
  divAddr *= modValue;

  // First loop is to go through all permutations of "dropped remainder"
  for (droppedRemainder = 0; droppedRemainder < modValue; droppedRemainder++)
  {

    deNormAddrWithRemainderWithoutBase = baseDeNormAddr;

    switch (intLvMode)
    {
      /***************************/
      case INTERLEAVE_MODE_DF45_NPS0_24CHAN_1K_HASH:
        // SocketSel = HashPA[8]
        // Mod3[1:0] = {PA[MSB:13], 3'b0} % 3
        // ChSel = {Mod3[1:0], HashPA[12], HashPA[9]}
        // NormAddr = {PA[MSB:13] / 3, PA[11:10], PA[7:0]}
        deNormAddrWithRemainderWithoutBase |= ((divAddr + droppedRemainder) << 13);
        break;

        /***************************/
      case INTERLEAVE_MODE_DF45_NPS0_24CHAN_2K_HASH:
        // SocketSel = HashPA[8]
        // Mod3[1:0] = {PA[MSB:14], 4'b0} % 3
        // ChSel = {Mod3[1:0], HashPA[13:12]}
        // NormAddr = {PA[MSB:14] / 3, PA[11:9], PA[7:0]}
        deNormAddrWithRemainderWithoutBase |= ((divAddr + droppedRemainder) << 14);
        break;

        /***************************/
      case INTERLEAVE_MODE_DF45_NPS1_12CHAN_1K_HASH:
        // Mod3[1:0] = {PA[MSB:12], 2'b0} % 3
        // ChSel = {Mod3[1:0], HashPA[9:8]}
        // NormAddr = {PA[MSB:12] / 3, PA[11:10], PA[7:0]}
        deNormAddrWithRemainderWithoutBase |= ((divAddr + droppedRemainder) << 12);
        break;

        /***************************/
      case INTERLEAVE_MODE_DF45_NPS1_12CHAN_2K_HASH:
        // Mod3[1:0] = {PA[MSB:13], 3'b0} % 3
        // ChSel = {Mod3[1:0], HashPA[12], HashPA[8]}
        // NormAddr = {{PA[MSB:13]} / 3, PA[11:9], PA[7:0]}
        deNormAddrWithRemainderWithoutBase |= ((divAddr + droppedRemainder) << 13);
        break;

        /***************************/
      case INTERLEAVE_MODE_DF45_NPS2_6CHAN_1K_HASH:
        // Mod3[1:0] = {PA[MSB:12], PA[9], 1'b0} % 3
        // ChSel = {Mod3[1:0], HashPA[8]}
        // NormAddr = {{PA[MSB:12], PA[9]} / 3, PA[11:10], PA[7:0]}
        deNormAddrWithRemainderWithoutBase |= (getBit64 (0, (divAddr + droppedRemainder))) << 9;
        deNormAddrWithRemainderWithoutBase |= (getBits64 (1, 63, (divAddr + droppedRemainder))) << 12;
        break;

        /***************************/
      case INTERLEAVE_MODE_DF45_NPS2_6CHAN_2K_HASH:
        // Mod3[1:0] = {PA[MSB:12], 2'd0} % 3
        // ChSel = {Mod3[1:0], HashPA[8]}
        // NormAddr = {PA[MSB:12] / 3, PA[11:9], PA[7:0]}
        deNormAddrWithRemainderWithoutBase |= ((divAddr + droppedRemainder) << 12);
        break;

        /***************************/
      case INTERLEAVE_MODE_DF45_NPS4_3CHAN_1K_HASH:
        // Mod3[1:0] = {PA[MSB:12], PA[9:8]} % 3
        // ChSel = Mod3[1:0]
        // NormAddr = {{PA[MSB:12], PA[9:8]} / 3, PA[11:10], PA[7:0]}
        deNormAddrWithRemainderWithoutBase |= (getBits64 (0, 1, (divAddr + droppedRemainder))) << 8;
        deNormAddrWithRemainderWithoutBase |= (getBits64 (2, 63, (divAddr + droppedRemainder))) << 12;
        break;

        /***************************/
      case INTERLEAVE_MODE_DF45_NPS4_3CHAN_2K_HASH:
        // Mod3[1:0] = {PA[MSB:12], PA[8], 1'b0]} % 3
        // ChSel = Mod3[1:0]
        // NormAddr = {{PA[MSB:12], PA[8]} / 3, PA[11:9], PA[7:0]}
        deNormAddrWithRemainderWithoutBase |= (getBit64 (0, (divAddr + droppedRemainder))) << 8;
        deNormAddrWithRemainderWithoutBase |= (getBits64 (1, 63, (divAddr + droppedRemainder))) << 12;
        break;

        /***************************/
      case INTERLEAVE_MODE_DF45_NPS1_10CHAN_1K_HASH:
        // Mod5[2:0] = {PA[MSB:12], PA[9], 1'b0} % 5
        // ChSel = {Mod5[2:0], HashPA[8]}
        // NormAddr = {{PA[MSB:12], PA[9]} / 5, PA[11:10], PA[7:0]}
        deNormAddrWithRemainderWithoutBase |= (getBit64 (0, (divAddr + droppedRemainder))) << 9;
        deNormAddrWithRemainderWithoutBase |= (getBits64 (1, 63, (divAddr + droppedRemainder))) << 12;
        break;

        /***************************/
      case INTERLEAVE_MODE_DF45_NPS1_10CHAN_2K_HASH:
        // Mod5[2:0] = {PA[MSB:12], 2'd0} % 5
        // ChSel = {Mod5[2:0], HashPA[8]}
        // Norm Addr = {PA[MSB:12] / 5, PA[11:9], PA[7:0]}
        deNormAddrWithRemainderWithoutBase |= ((divAddr + droppedRemainder) << 12);
        break;

        /***************************/
      case INTERLEAVE_MODE_DF45_NPS2_5CHAN_1K_HASH:
        // Mod5[2:0] = {PA[MSB:12], PA[9:8]} % 5
        // ChSel = Mod5[2:0]
        // NormAddr = {{PA[MSB:12], PA[9:8]} / 5, PA[11:10], PA[7:0]}
        deNormAddrWithRemainderWithoutBase |= (getBits64 (0, 1, (divAddr + droppedRemainder))) << 8;
        deNormAddrWithRemainderWithoutBase |= (getBits64 (2, 63, (divAddr + droppedRemainder))) << 12;
        break;

        /***************************/
      case INTERLEAVE_MODE_DF45_NPS2_5CHAN_2K_HASH:
        // Mod5[2:0] = {PA[MSB:12], PA[8], 1'b0} % 5
        // ChSel = Mod5[2:0]
        // NormAddr = {{PA[MSB:12], PA[8]} / 5, PA[11:9], PA[7:0]}
        deNormAddrWithRemainderWithoutBase |= (getBit64 (0, (divAddr + droppedRemainder))) << 8;
        deNormAddrWithRemainderWithoutBase |= (getBits64 (1, 63, (divAddr + droppedRemainder))) << 12;
        break;

        /***************************/
      default:
        ASSERT (FALSE);
    }

    // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "DEBUG>> deNormAddrWithRemainderWithoutBase = %016lX\n", deNormAddrWithRemainderWithoutBase);

    // Go through all permutations of "missing" address bits.
    for (testPermutation = 0; testPermutation < numPermutations; testPermutation++)
    {
      // Now put the test permutation into the vector
      currentSpaWithoutBase = deNormAddrWithRemainderWithoutBase;

      switch (intLvMode)
      {
        /***************************/
        case INTERLEAVE_MODE_DF45_NPS0_24CHAN_1K_HASH:
          // SocketSel = HashPA[8]
          // Mod3[1:0] = {PA[MSB:13], 3'b0} % 3
          // ChSel = {Mod3[1:0], HashPA[12], HashPA[9]}
          // NormAddr = {PA[MSB:13] / 3, PA[11:10], PA[7:0]}
          currentSpaWithoutBase |= ((UINT64)getBit (0, testPermutation)) << 8;
          currentSpaWithoutBase |= ((UINT64)getBit (1, testPermutation)) << 9;
          currentSpaWithoutBase |= ((UINT64)getBit (2, testPermutation)) << 12;
          break;

          /***************************/
        case INTERLEAVE_MODE_DF45_NPS0_24CHAN_2K_HASH:
          // SocketSel = HashPA[8]
          // Mod3[1:0] = {PA[MSB:14], 4'b0} % 3
          // ChSel = {Mod3[1:0], HashPA[13:12]}
          // NormAddr = {PA[MSB:14] / 3, PA[11:9], PA[7:0]}
          currentSpaWithoutBase |= ((UINT64)getBit (0, testPermutation)) << 8;
          currentSpaWithoutBase |= ((UINT64)getBit (1, testPermutation)) << 12;
          currentSpaWithoutBase |= ((UINT64)getBit (2, testPermutation)) << 13;
          break;

          /***************************/
        case INTERLEAVE_MODE_DF45_NPS1_12CHAN_1K_HASH:
          // Mod3[1:0] = {PA[MSB:12], 2'b0} % 3
          // ChSel = {Mod3[1:0], HashPA[9:8]}
          // NormAddr = {PA[MSB:12] / 3, PA[11:10], PA[7:0]}
          currentSpaWithoutBase |= ((UINT64)getBit (0, testPermutation)) << 8;
          currentSpaWithoutBase |= ((UINT64)getBit (1, testPermutation)) << 9;
          break;

          /***************************/
        case INTERLEAVE_MODE_DF45_NPS1_12CHAN_2K_HASH:
          // Mod3[1:0] = {PA[MSB:13], 3'b0} % 3
          // ChSel = {Mod3[1:0], HashPA[12], HashPA[8]}
          // NormAddr = {{PA[MSB:13]} / 3, PA[11:9], PA[7:0]}
          currentSpaWithoutBase |= ((UINT64)getBit (0, testPermutation)) << 8;
          currentSpaWithoutBase |= ((UINT64)getBit (1, testPermutation)) << 12;
          break;

          /***************************/
        case INTERLEAVE_MODE_DF45_NPS2_6CHAN_1K_HASH:
          // Mod3[1:0] = {PA[MSB:12], PA[9], 1'b0} % 3
          // ChSel = {Mod3[1:0], HashPA[8]}
          // NormAddr = {{PA[MSB:12], PA[9]} / 3, PA[11:10], PA[7:0]}
          currentSpaWithoutBase |= ((UINT64)getBit (0, testPermutation)) << 8;
          break;

          /***************************/
        case INTERLEAVE_MODE_DF45_NPS2_6CHAN_2K_HASH:
          // Mod3[1:0] = {PA[MSB:12], 2'd0} % 3
          // ChSel = {Mod3[1:0], HashPA[8]}
          // NormAddr = {PA[MSB:12] / 3, PA[11:9], PA[7:0]}
          currentSpaWithoutBase |= ((UINT64)getBit (0, testPermutation)) << 8;
          break;

          /***************************/
        case INTERLEAVE_MODE_DF45_NPS4_3CHAN_1K_HASH:
          // Mod3[1:0] = {PA[MSB:12], PA[9:8]} % 3
          // ChSel = Mod3[1:0]
          // NormAddr = {{PA[MSB:12], PA[9:8]} / 3, PA[11:10], PA[7:0]}
          currentSpaWithoutBase |= ((UINT64)getBit (0, testPermutation)) << 8;
          currentSpaWithoutBase |= ((UINT64)getBit (1, testPermutation)) << 9;
          break;

          /***************************/
        case INTERLEAVE_MODE_DF45_NPS4_3CHAN_2K_HASH:
          // Mod3[1:0] = {PA[MSB:12], PA[8], 1'b0]} % 3
          // ChSel = Mod3[1:0]
          // NormAddr = {{PA[MSB:12], PA[8]} / 3, PA[11:9], PA[7:0]}
          currentSpaWithoutBase |= ((UINT64)getBit (0, testPermutation)) << 8;
          break;

          /***************************/
        case INTERLEAVE_MODE_DF45_NPS1_10CHAN_1K_HASH:
          // Mod5[2:0] = {PA[MSB:12], PA[9], 1'b0} % 5
          // ChSel = {Mod5[2:0], HashPA[8]}
          // NormAddr = {{PA[MSB:12], PA[9]} / 5, PA[11:10], PA[7:0]}
          currentSpaWithoutBase |= ((UINT64)getBit (0, testPermutation)) << 8;
          break;

          /***************************/
        case INTERLEAVE_MODE_DF45_NPS1_10CHAN_2K_HASH:
          // Mod5[2:0] = {PA[MSB:12], 2'd0} % 5
          // ChSel = {Mod5[2:0], HashPA[8]}
          // Norm Addr = {PA[MSB:12] / 5, PA[11:9], PA[7:0]}
          currentSpaWithoutBase |= ((UINT64)getBit (0, testPermutation)) << 8;
          break;

          /***************************/
        case INTERLEAVE_MODE_DF45_NPS2_5CHAN_1K_HASH:
          // Mod5[2:0] = {PA[MSB:12], PA[9:8]} % 5
          // ChSel = Mod5[2:0]
          // NormAddr = {{PA[MSB:12], PA[9:8]} / 5, PA[11:10], PA[7:0]}
          currentSpaWithoutBase |= ((UINT64)getBit (0, testPermutation)) << 8;
          currentSpaWithoutBase |= ((UINT64)getBit (1, testPermutation)) << 9;
          break;

          /***************************/
        case INTERLEAVE_MODE_DF45_NPS2_5CHAN_2K_HASH:
          // Mod5[2:0] = {PA[MSB:12], PA[8], 1'b0} % 5
          // ChSel = Mod5[2:0]
          // NormAddr = {{PA[MSB:12], PA[8]} / 5, PA[11:9], PA[7:0]}
          currentSpaWithoutBase |= ((UINT64)getBit (0, testPermutation)) << 8;
          break;

          /***************************/
        default:
          ASSERT (FALSE);
      }

      // Now add in the offset, which we need to get the correct hashing bits
      // since the hash and mod algorithms to pick the CS is based off of the raw SPA
      currentSpaWithBase = currentSpaWithoutBase + dramBaseAddr;
      if (lgcyMmioHoleEn && (currentSpaWithBase > dramHoleBase))
      {
        currentSpaWithBase += dramHoleSize;
      }

      // Now recalculate the hash bits
      if ((rehashVector & (1 << 8)) != 0)
      {
        hashedBit = (UINT32)(getBit64 (8, currentSpaWithBase) ^
                             getBit64 (14, currentSpaWithBase) ^
                             (getBit64 (16, currentSpaWithBase) & hashIntlvCtl64K) ^
                             (getBit64 (21, currentSpaWithBase) & hashIntlvCtl2M) ^
                             (getBit64 (30, currentSpaWithBase) & hashIntlvCtl1G) ^
                             (getBit64 (40, currentSpaWithBase) & hashIntlvCtl1T));
        // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "DEBUG>> hashedBit = %d\n", hashedBit);
        if (getBit64 (8, currentSpaWithBase) != hashedBit)
        {
          currentSpaWithBase ^= (((UINT64)1) << 8);
        }
      }
      if ((rehashVector & (1 << 9)) != 0)
      {
        hashedBit = (UINT32)(getBit64 (9, currentSpaWithBase) ^
                             (getBit64 (17, currentSpaWithBase) & hashIntlvCtl64K) ^
                             (getBit64 (22, currentSpaWithBase) & hashIntlvCtl2M) ^
                             (getBit64 (31, currentSpaWithBase) & hashIntlvCtl1G) ^
                             (getBit64 (41, currentSpaWithBase) & hashIntlvCtl1T));
        // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "DEBUG>> hashedBit = %d\n", hashedBit);
        if (getBit64 (9, currentSpaWithBase) != hashedBit)
        {
          currentSpaWithBase ^= (((UINT64)1) << 9);
        }
      }
      if ((rehashVector & (1 << 12)) != 0)
      {
        hashedBit = (UINT32)(getBit64 (12, currentSpaWithBase) ^
                             (getBit64 (18, currentSpaWithBase) & hashIntlvCtl64K) ^
                             (getBit64 (23, currentSpaWithBase) & hashIntlvCtl2M) ^
                             (getBit64 (32, currentSpaWithBase) & hashIntlvCtl1G) ^
                             (getBit64 (42, currentSpaWithBase) & hashIntlvCtl1T));
        // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "DEBUG>> hashedBit = %d\n", hashedBit);
        if (getBit64 (12, currentSpaWithBase) != hashedBit)
        {
          currentSpaWithBase ^= (((UINT64)1) << 12);
        }
      }
      if ((rehashVector & (1 << 13)) != 0)
      {
        hashedBit = (UINT32)(getBit64 (13, currentSpaWithBase) ^
                             (getBit64 (19, currentSpaWithBase) & hashIntlvCtl64K) ^
                             (getBit64 (24, currentSpaWithBase) & hashIntlvCtl2M) ^
                             (getBit64 (33, currentSpaWithBase) & hashIntlvCtl1G) ^
                             (getBit64 (43, currentSpaWithBase) & hashIntlvCtl1T));
        // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "DEBUG>> hashedBit = %d\n", hashedBit);
        if (getBit64 (13, currentSpaWithBase) != hashedBit)
        {
          currentSpaWithBase ^= (((UINT64)1) << 13);
        }
      }
      // Note that we never have to rehash bit 14
      ASSERT ((rehashVector & (1 << 14)) == 0);

      // Now that we have rehashed, regenerate the SPA address without the base
      currentSpaWithoutBase = currentSpaWithBase;
      if (lgcyMmioHoleEn && (currentSpaWithoutBase > dramHoleBase))
      {
        currentSpaWithoutBase -= dramHoleSize;
      }
      currentSpaWithoutBase -= dramBaseAddr;

      //IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "DEBUG>> Going to try permutation (%d/%d) (test SPA of 0x%016lX)\n", droppedRemainder, testPermutation, currentSpaWithBase);
      // CS is picked using the raw SPA (no subtraction).
      thisAddrLogicalCsFabricId = getCsLogicalComponentIdFromAddr (dfType, dramAddressMapRegs, currentSpaWithBase);
      if ((logicalCsFabricId - dstFabricId) == thisAddrLogicalCsFabricId)
      {
        //IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "DEBUG>> Found that permutation (%d/%d) gave the proper CS fabricID\n", droppedRemainder, testPermutation);
        thisAddrNormalizedAddress = normalizeDf45NP2 (dfType, dramAddressMapRegs, currentSpaWithoutBase);
        if (thisAddrNormalizedAddress == normAddr)
        {
          // THIS IS THE SOLUTION! We return currentSpaWithoutBase (without the base) because it will be added again later.
          // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "DEBUG>> Found that permutation (%d/%d) also gave the proper normalized address\n", droppedRemainder, testPermutation);
          //IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "DEBUG>> deNormalizeAddrDf45Np2 complete (0x%x 0x%016lX) -> 0x%016lX\n",
          //    logicalCsFabricId, normAddr, currentSpaWithoutBase);
          return (currentSpaWithoutBase);
        } else
        {
          // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "DEBUG>> Permutation (%d/%d) is not correct because it gave a different normalized address 0x%016lx\n",
          //                    droppedRemainder, testPermutation, thisAddrNormalizedAddress);
        }
      } else
      {
        // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "DEBUG>> Permutation (%d/%d) is not correct because it gave a different FabricID 0x%x\n",
        //                    droppedRemainder, testPermutation, thisAddrLogicalCsFabricId);
      }
    }
  }

  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "ERROR: Could not find a permutation of hash bits that matched the normAddress\n");
  ASSERT (0);
  return (0);
}
/*------------------------------------------------------------------
 Function: deNormalizeAddr
 Purpose: Helper function to do the address denormalization
 Inputs: dfType, csFabricId, address map, and address
 Outputs: Normalized address
 *------------------------------------------------------------------*/
UINT64
deNormalizeAddr (
  DfType *dfType,
  UINT32 csFabricId,
  UINT32 *dramAddressMapRegs,
  UINT64 normAddr
  )
{
  UINT64 deNormAddr = 0;
  UINT32 intLvMode;
  UINT32 intLvAddrBit;
  UINT32 numInterleaveBits;
  UINT32 mask;
  UINT32 csId;
  UINT32 dieId;
  UINT32 numDies;
  UINT32 numSockets;
  UINT32 numChannelsPerNode;

  intLvMode = decodeDramIntLvNumChan (dfType, dramAddressMapRegs);
  intLvAddrBit = decodeDramIntLvAddrBit (dfType, dramAddressMapRegs);

  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "deNormalizeAddr, csFabricId=0x%x, normAddr=0x%016lX\n",
      csFabricId, normAddr);
  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "dramBaseReg=0x%08X, dramLimitReg=0x%08X dramIntlvReg=0x%08X, dramCtlReg=0x%08X\n",
      dramAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET], dramAddressMapRegs[ADDR_MAP_ARRAY_LIMIT_OFFSET],
      dramAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET], dramAddressMapRegs[ADDR_MAP_ARRAY_CTL_OFFSET]);
  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "intLvMode=%d, intLvAddrBit=%d\n",
      intLvMode, intLvAddrBit);
  // If there is no interleaving, just return here to avoid special casing it everywhere.
  if (intLvMode == INTERLEAVE_MODE_NONE)
  {
    return (normAddr);
  }
  // Go through the reverse CS remap to convert this fabricID to a "logical" fabricID.
  csFabricId = (csFabricId & getNodeIdMask (dfType)) |
               convertPhysicalCsFabricIdToLogicalCsFabricId (dfType, csFabricId, dramAddressMapRegs);
  // All modulo algorithms are done elsewhere
  if (intLvMode == INTERLEAVE_MODE_DF3_6CHAN)
  {
    deNormAddr = deNormalizeAddrMod3 (dfType, csFabricId, dramAddressMapRegs, normAddr);
    return (deNormAddr);
  }
  // All modulo algorithms are done elsewhere
  if ((intLvMode == INTERLEAVE_MODE_DF4_NPS4_3CHAN_HASH) ||
      (intLvMode == INTERLEAVE_MODE_DF4_NPS2_6CHAN_HASH) ||
      (intLvMode == INTERLEAVE_MODE_DF4_NPS1_12CHAN_HASH) ||
      (intLvMode == INTERLEAVE_MODE_DF4_NPS2_5CHAN_HASH) ||
      (intLvMode == INTERLEAVE_MODE_DF4_NPS1_10CHAN_HASH))
  {
    deNormAddr = deNormalizeAddrDf4Np2 (dfType, csFabricId, dramAddressMapRegs, normAddr);
    return (deNormAddr);
  }
  if ((intLvMode == INTERLEAVE_MODE_DF45_NPS0_24CHAN_1K_HASH) ||
      (intLvMode == INTERLEAVE_MODE_DF45_NPS4_3CHAN_1K_HASH) ||
      (intLvMode == INTERLEAVE_MODE_DF45_NPS2_6CHAN_1K_HASH) ||
      (intLvMode == INTERLEAVE_MODE_DF45_NPS1_12CHAN_1K_HASH) ||
      (intLvMode == INTERLEAVE_MODE_DF45_NPS2_5CHAN_1K_HASH) ||
      (intLvMode == INTERLEAVE_MODE_DF45_NPS1_10CHAN_1K_HASH) ||
      (intLvMode == INTERLEAVE_MODE_DF45_NPS4_3CHAN_2K_HASH) ||
      (intLvMode == INTERLEAVE_MODE_DF45_NPS2_6CHAN_2K_HASH) ||
      (intLvMode == INTERLEAVE_MODE_DF45_NPS1_12CHAN_2K_HASH) ||
      (intLvMode == INTERLEAVE_MODE_DF45_NPS0_24CHAN_2K_HASH) ||
      (intLvMode == INTERLEAVE_MODE_DF45_NPS2_5CHAN_2K_HASH) ||
      (intLvMode == INTERLEAVE_MODE_DF45_NPS1_10CHAN_2K_HASH))
  {
    deNormAddr = deNormalizeAddrDf45Np2 (dfType, csFabricId, dramAddressMapRegs, normAddr);
    return (deNormAddr);
  }
  // Make room in the address for where the channel identifier goes
  switch (intLvMode)
  {
    case INTERLEAVE_MODE_2CHAN_NOHASH:
    case INTERLEAVE_MODE_4CHAN_NOHASH:
    case INTERLEAVE_MODE_8CHAN_NOHASH:
    case INTERLEAVE_MODE_16CHAN_NOHASH:
    case INTERLEAVE_MODE_32CHAN_NOHASH:
    case INTERLEAVE_MODE_MI2_HASH_8CHAN:
    case INTERLEAVE_MODE_MI2_HASH_16CHAN:
    case INTERLEAVE_MODE_MI2_HASH_32CHAN:
    case INTERLEAVE_MODE_MI3_HASH_8CHAN:
    case INTERLEAVE_MODE_MI3_HASH_16CHAN:
    case INTERLEAVE_MODE_MI3_HASH_32CHAN:
    case INTERLEAVE_MODE_ZP_2CHAN_HASH:
      numInterleaveBits = log2 (getNumChannelFromDramIntLvMode (intLvMode));
      numInterleaveBits += extractDramIntLvNumDies (dfType, dramAddressMapRegs);
      numInterleaveBits += extractDramIntLvNumSkts (dfType, dramAddressMapRegs);
      // Make room for the CS ID at bit "intLvAddrBit"
      deNormAddr = expandBits64 (intLvAddrBit, numInterleaveBits, normAddr);
      break;
    case INTERLEAVE_MODE_DF3_COD4_2CHAN_HASH:
    case INTERLEAVE_MODE_DF3_COD2_4CHAN_HASH:
    case INTERLEAVE_MODE_DF3_COD1_8CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS4_2CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS2_4CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS1_8CHAN_HASH:
    case INTERLEAVE_MODE_DF45_NPS4_2CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS2_4CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_8CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_16CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS4_2CHAN_1K_HASH:
      // Make room for the CS ID at bit "intLvAddrBit" and then <n> bits starting at bit 12
      // <n> is the number of interleave bits - 1
      numInterleaveBits = log2 (getNumChannelFromDramIntLvMode (intLvMode));
      // In Turin, the number of channels encoded in IntLvNumChan already includes socket interleaving.
      if (dfType->dfVersion != DF_TYPE_DF4POINT5)
      {
        numInterleaveBits += extractDramIntLvNumSkts (dfType, dramAddressMapRegs); // DF4 only
      }
      deNormAddr = expandBits64 (intLvAddrBit, 1, normAddr);
      if (numInterleaveBits > 1)
      {
        deNormAddr = expandBits64 (12, (numInterleaveBits - 1), deNormAddr);
      }
      break;
    case INTERLEAVE_MODE_DF45_NPS1_16CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS2_4CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_8CHAN_1K_HASH:
      // Make room for the CS ID at bits "IntLvAddrBit+1 : intLvAddrBit" and then <n> bits starting at bit 12
      // <n> is the number of interleave bits - 2
      numInterleaveBits = log2 (getNumChannelFromDramIntLvMode (intLvMode));
      // In Turin, the number of channels encoded in IntLvNumChan already includes socket interleaving.
      if (dfType->dfVersion != DF_TYPE_DF4POINT5)
      {
        numInterleaveBits += extractDramIntLvNumSkts (dfType, dramAddressMapRegs); // DF4 only
      }
      deNormAddr = expandBits64 (intLvAddrBit, 2, normAddr);
      if (numInterleaveBits > 2)
      {
        deNormAddr = expandBits64 (12, (numInterleaveBits - 2), deNormAddr);
      }
      break;
    default:
      ASSERT (FALSE);
  }
  // Calculate the CS ID
  switch (intLvMode)
  {
    case INTERLEAVE_MODE_2CHAN_NOHASH:
    case INTERLEAVE_MODE_4CHAN_NOHASH:
    case INTERLEAVE_MODE_8CHAN_NOHASH:
    case INTERLEAVE_MODE_16CHAN_NOHASH:
    case INTERLEAVE_MODE_32CHAN_NOHASH:
    case INTERLEAVE_MODE_ZP_2CHAN_HASH:
    case INTERLEAVE_MODE_DF3_COD4_2CHAN_HASH:
    case INTERLEAVE_MODE_DF3_COD2_4CHAN_HASH:
    case INTERLEAVE_MODE_DF3_COD1_8CHAN_HASH:
    case INTERLEAVE_MODE_MI2_HASH_8CHAN:
    case INTERLEAVE_MODE_MI2_HASH_16CHAN:
    case INTERLEAVE_MODE_MI2_HASH_32CHAN:
    case INTERLEAVE_MODE_MI3_HASH_8CHAN:
    case INTERLEAVE_MODE_MI3_HASH_16CHAN:
    case INTERLEAVE_MODE_MI3_HASH_32CHAN:
      numInterleaveBits = log2 (getNumChannelFromDramIntLvMode (intLvMode));
      // You subtract off the base dstFabricId here, but only as many bits in the
      // channel mask. We dont have any way for the dstFabricID to "spill" over
      // to the nodeID, so this offset can only be within the number of channels.
      // The base CS ComponentID in all sockets/dies must be the same and
      // there can be no overflow from this addition into the socket/die bits
      // (CS FabricIDs start at 0 so this can't happen anyways).
      mask = getComponentIdMask (dfType);
      csId = (csFabricId & mask) - (extractDstFabricId (dfType, dramAddressMapRegs) & mask);
      mask = (1 << numInterleaveBits) - 1;
      csId = csId & mask;
      numDies = decodeDramIntLvNumDies (dfType, dramAddressMapRegs);
      if (numDies > 1)
      {
        mask = (1 << ((INT32)(log2 (numDies)))) - 1;
        csId |= (((csFabricId & getDieIdMask (dfType)) >> getDieIdShift (dfType)) & mask) << numInterleaveBits;
        numInterleaveBits += (INT32)(log2 (numDies));
      }
      numSockets = extractDramIntLvNumSkts (dfType, dramAddressMapRegs) + 1;
      if (numSockets > 1)
      {
        mask = (1 << ((INT32)(log2 (numSockets)))) - 1;
        csId |= (((csFabricId & getSocketIdMask (dfType)) >> getSocketIdShift (dfType)) & mask) << numInterleaveBits;
      }
      break;
    case INTERLEAVE_MODE_DF4_NPS4_2CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS2_4CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS1_8CHAN_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_16CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS4_2CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS2_4CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_8CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS4_2CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS2_4CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_8CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_16CHAN_2K_HASH:
      numChannelsPerNode = getNumChannelFromDramIntLvMode (intLvMode);
      // In Turin, the number of channels encoded in IntLvNumChan already includes socket interleaving.
      if (dfType->dfVersion == DF_TYPE_DF4POINT5)
      {
        numChannelsPerNode = numChannelsPerNode / (extractDramIntLvNumSkts (dfType, dramAddressMapRegs) + 1);
      }
      numInterleaveBits = log2 (numChannelsPerNode);
      // You subtract off the base dstFabricId here, but only as many bits in the
      // channel mask. We dont have any way for the dstFabricID to "spill" over
      // to the nodeID, so this offset can only be within the number of channels.
      // All this means is that socket and die interleaving must not have an offset
      mask = getComponentIdMask (dfType);
      csId = (csFabricId & mask) - (extractDstFabricId (dfType, dramAddressMapRegs) & mask);
      mask = (1 << numInterleaveBits) - 1;
      csId = csId & mask;
      ASSERT (decodeDramIntLvNumDies (dfType, dramAddressMapRegs) == 1);
      numSockets = extractDramIntLvNumSkts (dfType, dramAddressMapRegs) + 1;
      if (numSockets > 1)
      {
        // Here, the socket ID bit is really csID[0].
        numInterleaveBits = log2 (numSockets);
        csId = csId << numInterleaveBits;
        mask = (1 << numInterleaveBits) - 1;
        csId |= ((csFabricId & getSocketIdMask (dfType)) >> getSocketIdShift (dfType)) & mask;
      }
      break;
    default:
      ASSERT (FALSE);
  }
  // Insert the unhashed CS ID into the appropriate bits of the address
  switch (intLvMode)
  {
    case INTERLEAVE_MODE_2CHAN_NOHASH:
    case INTERLEAVE_MODE_4CHAN_NOHASH:
    case INTERLEAVE_MODE_8CHAN_NOHASH:
    case INTERLEAVE_MODE_16CHAN_NOHASH:
    case INTERLEAVE_MODE_32CHAN_NOHASH:
    case INTERLEAVE_MODE_MI2_HASH_8CHAN:
    case INTERLEAVE_MODE_MI2_HASH_16CHAN:
    case INTERLEAVE_MODE_MI2_HASH_32CHAN:
    case INTERLEAVE_MODE_ZP_2CHAN_HASH:
      deNormAddr |= (((UINT64)csId) << intLvAddrBit);
      break;
    case INTERLEAVE_MODE_MI3_HASH_8CHAN:
    case INTERLEAVE_MODE_MI3_HASH_16CHAN:
    case INTERLEAVE_MODE_MI3_HASH_32CHAN:
      // The MI300 hash has:
      //   Channel[3:0] = bits 11:8
      //   Stack[0]     = bit 14, aka CSID[4]
      //   Die[1:0]     = bits 13:12
      // So we swizzle csId such that it is bits 4,6,5,3,2,1,0
      dieId = (csFabricId & getDieIdMask (dfType)) >> getDieIdShift (dfType);
      csId = (csFabricId & 0xF) | ((csFabricId & 0x10) << 2) | (dieId << 4);
      deNormAddr |= (((UINT64)csId) << intLvAddrBit);
      break;
    case INTERLEAVE_MODE_DF3_COD4_2CHAN_HASH:
    case INTERLEAVE_MODE_DF3_COD2_4CHAN_HASH:
    case INTERLEAVE_MODE_DF3_COD1_8CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS4_2CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS2_4CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS1_8CHAN_HASH:
    case INTERLEAVE_MODE_DF45_NPS4_2CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS2_4CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_8CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_16CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS4_2CHAN_1K_HASH:
      // csID[0] is inserted at intLvAddrBit
      deNormAddr |= (((UINT64)(csId & 0x01)) << intLvAddrBit);
      // csID[2:1] is inserted at bits (12+numIntLvBit-2) : 12
      deNormAddr |= (((UINT64)(csId & 0xFE)) << 11);
      break;
    case INTERLEAVE_MODE_DF45_NPS1_16CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS2_4CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_8CHAN_1K_HASH:
      // csID[1:0] is inserted at intLvAddrBit
      deNormAddr |= (((UINT64)(csId & 0x3)) << intLvAddrBit);
      // csID[n:2] is inserted at bits (12+numIntLvBit-3) : 12
      deNormAddr |= (((UINT64)(csId & 0xFC)) << 10);
      break;
  }
  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "deNormalizeAddr=0x%016lX\n", deNormAddr);
  return (deNormAddr);
}

/*------------------------------------------------------------------
 Function: deNormHashAddr
 Purpose: Helper function to do the address denormalization
 Inputs: dfType, address map, and address
 Outputs: Normalized address after correcting any hashing
 *------------------------------------------------------------------*/
UINT64
deNormHashAddr (
  DfType *dfType,
  UINT32 *dramAddressMapRegs,
  UINT64 deNormAddr
  )
{
  UINT32 intLvMode;
  UINT32 intLvAddrBit;
  UINT32 numInterleaveBits;
  UINT32 i;
  UINT32 intlvHashCtl, hashIntlvCtl4K, hashIntlvCtl64K, hashIntlvCtl2M, hashIntlvCtl1G, hashIntlvCtl1T;
  UINT32 hashedBit, baseBitNum;
  UINT32 numSocketsInterleaved;
  UINT32 totalChannelsInterleaved;
  UINT64 rehashVector;
  intLvMode = decodeDramIntLvNumChan (dfType, dramAddressMapRegs);
  switch (intLvMode)
  {
    case INTERLEAVE_MODE_NONE:
    case INTERLEAVE_MODE_2CHAN_NOHASH:
    case INTERLEAVE_MODE_4CHAN_NOHASH:
    case INTERLEAVE_MODE_8CHAN_NOHASH:
    case INTERLEAVE_MODE_16CHAN_NOHASH:
    case INTERLEAVE_MODE_32CHAN_NOHASH:
      return (deNormAddr);
  }
  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "deNormHashAddr 0x%016lX\n", deNormAddr);
  intLvAddrBit = decodeDramIntLvAddrBit (dfType, dramAddressMapRegs);
  // Perform any hashing
  intlvHashCtl = getIntlvHashCtlBits (dfType, dramAddressMapRegs);
  hashIntlvCtl64K = ((intlvHashCtl & 0x1) != 0) ? 1 : 0;
  hashIntlvCtl2M = ((intlvHashCtl & 0x2) != 0) ? 1 : 0;
  hashIntlvCtl1G = ((intlvHashCtl & 0x4) != 0) ? 1 : 0;
  hashIntlvCtl4K = ((intlvHashCtl & 0x8) != 0) ? 1 : 0;
  hashIntlvCtl1T = ((intlvHashCtl & 0x10) != 0) ? 1 : 0;
  switch (intLvMode)
  {
    case INTERLEAVE_MODE_ZP_2CHAN_HASH:
      // Hashing must use interleave address bit 8 or 9
      ASSERT ((intLvAddrBit == 8) || (intLvAddrBit == 9));
      // Does not support socket and die interleaving
      ASSERT (decodeDramIntLvNumDies (dfType, dramAddressMapRegs) == 1);
      ASSERT (extractDramIntLvNumSkts (dfType, dramAddressMapRegs) == 0);
      // CSSelect[0] = XOR of deNormAddr{intLvAddrBit, 12, 18, 21, 30}
      // DF2 didnt have any hash controls so these are unnecessary here.
      hashedBit = (UINT32)(getBit64 (intLvAddrBit, deNormAddr) ^
                           getBit64 (12, deNormAddr) ^
                           getBit64 (18, deNormAddr) ^
                           getBit64 (21, deNormAddr) ^
                           getBit64 (30, deNormAddr));
      // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "hashedBit=%d\n", hashedBit);
      if (getBit64 (intLvAddrBit, deNormAddr) != hashedBit)
      {
        deNormAddr ^= (((UINT64)1) << intLvAddrBit);
      }
      break;
    case INTERLEAVE_MODE_MI2_HASH_8CHAN:
    case INTERLEAVE_MODE_MI2_HASH_16CHAN:
    case INTERLEAVE_MODE_MI2_HASH_32CHAN:
      // Does not support socket interleaving
      ASSERT (extractDramIntLvNumSkts (dfType, dramAddressMapRegs) == 0);
      // Does not support die interleaving
      ASSERT (decodeDramIntLvNumDies (dfType, dramAddressMapRegs) == 1);
      // CSSelect[0] = XOR of addr{8,  16, 21, 30};
      // CSSelect[1] = XOR of addr{9,  17, 22, 31};
      // CSSelect[2] = XOR of addr{10, 18, 23, 32};
      // CSSelect[3] = XOR of addr{11, 19, 24, 33}; - 16 and 32 channel only
      // CSSelect[4] = XOR of addr{12, 20, 25, 34}; - 32 channel only
      numInterleaveBits = log2 (getNumChannelFromDramIntLvMode (intLvMode));
      for (i = 0; i < numInterleaveBits; i++)
      {
        hashedBit = (UINT32)(getBit64 (8 + i, deNormAddr) ^
                             (getBit64 (16 + i, deNormAddr) & hashIntlvCtl64K) ^
                             (getBit64 (21 + i, deNormAddr) & hashIntlvCtl2M) ^
                             (getBit64 (30 + i, deNormAddr) & hashIntlvCtl1G));
        // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "hashedBit=%d\n", hashedBit);
        if (getBit64 (8 + i, deNormAddr) != hashedBit)
        {
          deNormAddr ^= (((UINT64)1) << (8 + i));
        }
      }
      break;
    case INTERLEAVE_MODE_MI3_HASH_8CHAN:
    case INTERLEAVE_MODE_MI3_HASH_16CHAN:
    case INTERLEAVE_MODE_MI3_HASH_32CHAN:
      // Does not support socket interleaving
      ASSERT (extractDramIntLvNumSkts (dfType, dramAddressMapRegs) == 0);
      //                               4K 64K  2M  1G  1T  1T
      // CSSelect[0] = XOR of addr{8,  12, 15, 22, 29, 36, 43};
      // CSSelect[1] = XOR of addr{9,  13, 16, 23, 30, 37, 44};
      // CSSelect[2] = XOR of addr{10, 14, 17, 24, 31, 38, 45};
      // CSSelect[3] = XOR of addr{11,     18, 25, 32, 39, 46};
      // CSSelect[4] = XOR of addr{14,     19, 26, 33, 40, 47}; // aka stack
      // DieID[0]    = XOR of addr{12,     20, 27, 34, 41    };
      // DieID[1]    = XOR of addr{13,     21, 28, 35, 42    };
      numInterleaveBits = log2 (getNumChannelFromDramIntLvMode (intLvMode));
      for (i = 0; i < numInterleaveBits; i++)
      {
        baseBitNum = 8 + i;
        if (i == 4)
        {
          baseBitNum = 14;
        }
        hashedBit = (UINT32)(getBit64 (baseBitNum, deNormAddr) ^
                             ((i <= 2) ? getBit64 (12 + i, deNormAddr) & hashIntlvCtl4K : 0) ^
                             (getBit64 (15 + i, deNormAddr) & hashIntlvCtl64K) ^
                             (getBit64 (22 + i, deNormAddr) & hashIntlvCtl2M) ^
                             (getBit64 (29 + i, deNormAddr) & hashIntlvCtl1G) ^
                             (getBit64 (36 + i, deNormAddr) & hashIntlvCtl1T) ^
                             (getBit64 (43 + i, deNormAddr) & hashIntlvCtl1T));
        // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "hashedBit=%d\n", hashedBit);
        if (getBit64 (baseBitNum, deNormAddr) != hashedBit)
        {
          deNormAddr ^= (((UINT64)1) << baseBitNum);
        }
      }
      numInterleaveBits = extractDramIntLvNumDies (dfType, dramAddressMapRegs);
      for (i = 0; i < numInterleaveBits; i++)
      {
        baseBitNum = 12 + i;
        hashedBit = (UINT32)(getBit64 (baseBitNum, deNormAddr) ^
                             (getBit64 (20 + i, deNormAddr) & hashIntlvCtl64K) ^
                             (getBit64 (27 + i, deNormAddr) & hashIntlvCtl2M) ^
                             (getBit64 (34 + i, deNormAddr) & hashIntlvCtl1G) ^
                             (getBit64 (41 + i, deNormAddr) & hashIntlvCtl1T));
        // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "hashedBit=%d\n", hashedBit);
        if (getBit64 (baseBitNum, deNormAddr) != hashedBit)
        {
          deNormAddr ^= (((UINT64)1) << baseBitNum);
        }
      }
      break;
    case INTERLEAVE_MODE_DF3_COD4_2CHAN_HASH:
    case INTERLEAVE_MODE_DF3_COD2_4CHAN_HASH:
    case INTERLEAVE_MODE_DF3_COD1_8CHAN_HASH:
      // Hashing must use interleave address bit 8 or 9
      ASSERT ((intLvAddrBit == 8) || (intLvAddrBit == 9));
      // Does not support socket and die interleaving
      ASSERT (decodeDramIntLvNumDies (dfType, dramAddressMapRegs) == 1);
      ASSERT (extractDramIntLvNumSkts (dfType, dramAddressMapRegs) == 0);
      // CSSelect[0] = XOR of deNormAddr{intLvAddrBit, 14, 18, 23, 32}
      hashedBit = (UINT32)(getBit64 (intLvAddrBit, deNormAddr) ^
                           getBit64 (14, deNormAddr) ^
                           (getBit64 (18, deNormAddr) & hashIntlvCtl64K) ^
                           (getBit64 (23, deNormAddr) & hashIntlvCtl2M) ^
                           (getBit64 (32, deNormAddr) & hashIntlvCtl1G));
      // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "hashedBit=%d\n", hashedBit);
      if (getBit64 (intLvAddrBit, deNormAddr) != hashedBit)
      {
        deNormAddr ^= (((UINT64)1) << intLvAddrBit);
      }
      if ((intLvMode == INTERLEAVE_MODE_DF3_COD2_4CHAN_HASH) ||
          (intLvMode == INTERLEAVE_MODE_DF3_COD1_8CHAN_HASH))
      {
        // CSSelect[1] = XOR of deNormAddr{12, 16, 21, 30}
        hashedBit = (UINT32)(getBit64 (12, deNormAddr) ^
                             (getBit64 (16, deNormAddr) & hashIntlvCtl64K) ^
                             (getBit64 (21, deNormAddr) & hashIntlvCtl2M) ^
                             (getBit64 (30, deNormAddr) & hashIntlvCtl1G));
        // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "hashedBit=%d\n", hashedBit);
        if (getBit64 (12, deNormAddr) != hashedBit)
        {
          deNormAddr ^= (((UINT64)1) << 12);
        }
      }
      if (intLvMode == INTERLEAVE_MODE_DF3_COD1_8CHAN_HASH)
      {
        // CSSelect[2] = XOR of deNormAddr{13, 17, 22, 31}
        hashedBit = (UINT32)(getBit64 (13, deNormAddr) ^
                             (getBit64 (17, deNormAddr) & hashIntlvCtl64K) ^
                             (getBit64 (22, deNormAddr) & hashIntlvCtl2M) ^
                             (getBit64 (31, deNormAddr) & hashIntlvCtl1G));
        // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "hashedBit=%d\n", hashedBit);
        if (getBit64 (13, deNormAddr) != hashedBit)
        {
          deNormAddr ^= (((UINT64)1) << 13);
        }
      }
      break;
    case INTERLEAVE_MODE_DF3_6CHAN:
      // The number of interleave bits is as if we rounded up to the next power of 2
      // For example, 6-channel interleaving takes out 3 bits (6->8, log2(8)=3)
      // To do this, we simply truncate the log2 and add 1.
      numInterleaveBits = ((INT32)log2 (getNumChannelFromDramIntLvMode (intLvMode))) +  1;
      // The hashPA was inserted into Addr[14:12] or Addr[13:11] (depending on intLvAddrBit)
      // Either Addr[15] or Addr[14] is used.
      // interleaveBits[0] = HashPA[0] ^ PA[intlvBit+numIntLvBits] ^ PA[23] ^ PA[32];
      hashedBit = (UINT32)(getBit64 ((intLvAddrBit + 0), deNormAddr) ^
                           getBit64 ((intLvAddrBit + numInterleaveBits), deNormAddr) ^
                           (getBit64 (23, deNormAddr) & hashIntlvCtl2M) ^
                           (getBit64 (32, deNormAddr) & hashIntlvCtl1G));
      // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "hashedBit=%d\n", hashedBit);
      if (getBit64 (intLvAddrBit, deNormAddr) != hashedBit)
      {
        deNormAddr ^= (((UINT64)1) << intLvAddrBit);
      }
      // interleaveBits[1] = HashPA[1] ^ PA[21] ^ PA[30];
      hashedBit = (UINT32)(getBit64 ((intLvAddrBit + 1), deNormAddr) ^
                           (getBit64 (21, deNormAddr) & hashIntlvCtl2M) ^
                           (getBit64 (30, deNormAddr) & hashIntlvCtl1G));
      // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "hashedBit=%d\n", hashedBit);
      if (getBit64 (intLvAddrBit + 1, deNormAddr) != hashedBit)
      {
        deNormAddr ^= (((UINT64)1) << (intLvAddrBit + 1));
      }
      // interleaveBits[2] = HashPA[2] ^ PA[22] ^ PA[31];
      hashedBit = (UINT32)(getBit64 ((intLvAddrBit + 2), deNormAddr) ^
                           (getBit64 (22, deNormAddr) & hashIntlvCtl2M) ^
                           (getBit64 (31, deNormAddr) & hashIntlvCtl1G));
      // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "hashedBit=%d\n", hashedBit);
      if (getBit64 (intLvAddrBit + 2, deNormAddr) != hashedBit)
      {
        deNormAddr ^= (((UINT64)1) << (intLvAddrBit + 2));
      }
      break;
    case INTERLEAVE_MODE_DF4_NPS4_2CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS2_4CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS1_8CHAN_HASH:
      // Hashing must use interleave address bit 8
      ASSERT (intLvAddrBit == 8);
      // Does not support die interleaving
      ASSERT (decodeDramIntLvNumDies (dfType, dramAddressMapRegs) == 1);
      numSocketsInterleaved = extractDramIntLvNumSkts (dfType, dramAddressMapRegs) + 1;
      ASSERT (numSocketsInterleaved <= 2);
      totalChannelsInterleaved = getNumChannelFromDramIntLvMode (intLvMode) * numSocketsInterleaved;
      // CSSelect[0] = XOR of addr{8, 16, 21, 30}
      //               and add in an XOR of 14 if socket interleaving disabled
      // CSSelect[1] = XOR of addr{12, 17, 22, 31}
      // CSSelect[2] = XOR of addr{13, 18, 23, 32}
      // CSSelect[3] = XOR of addr{14, 19, 24, 33} // socket interleaving enabled only
      hashedBit = (UINT32)(getBit64 (8, deNormAddr) ^
                           (getBit64 (16, deNormAddr) & hashIntlvCtl64K) ^
                           (getBit64 (21, deNormAddr) & hashIntlvCtl2M) ^
                           (getBit64 (30, deNormAddr) & hashIntlvCtl1G));
      if (numSocketsInterleaved == 1)
      {
        hashedBit ^= getBit64 (14, deNormAddr);
      }
      // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "hashedBit=%d\n", hashedBit);
      if (getBit64 (8, deNormAddr) != hashedBit)
      {
        deNormAddr ^= (((UINT64)1) << 8);
      }
      if (totalChannelsInterleaved > 2)
      {
        hashedBit = (UINT32)(getBit64 (12, deNormAddr) ^
                             (getBit64 (17, deNormAddr) & hashIntlvCtl64K) ^
                             (getBit64 (22, deNormAddr) & hashIntlvCtl2M) ^
                             (getBit64 (31, deNormAddr) & hashIntlvCtl1G));
        // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "hashedBit=%d\n", hashedBit);
        if (getBit64 (12, deNormAddr) != hashedBit)
        {
          deNormAddr ^= (((UINT64)1) << 12);
        }
      }
      if (totalChannelsInterleaved > 4)
      {
        hashedBit = (UINT32)(getBit64 (13, deNormAddr) ^
                             (getBit64 (18, deNormAddr) & hashIntlvCtl64K) ^
                             (getBit64 (23, deNormAddr) & hashIntlvCtl2M) ^
                             (getBit64 (32, deNormAddr) & hashIntlvCtl1G));
        // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "hashedBit=%d\n", hashedBit);
        if (getBit64 (13, deNormAddr) != hashedBit)
        {
          deNormAddr ^= (((UINT64)1) << 13);
        }
      }
      if (totalChannelsInterleaved > 8)
      {
        hashedBit = (UINT32)(getBit64 (14, deNormAddr) ^
                             (getBit64 (19, deNormAddr) & hashIntlvCtl64K) ^
                             (getBit64 (24, deNormAddr) & hashIntlvCtl2M) ^
                             (getBit64 (33, deNormAddr) & hashIntlvCtl1G));
        // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "hashedBit=%d\n", hashedBit);
        if (getBit64 (14, deNormAddr) != hashedBit)
        {
          deNormAddr ^= (((UINT64)1) << 14);
        }
      }
      break;
    case INTERLEAVE_MODE_DF45_NPS4_2CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS4_2CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS2_4CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS2_4CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_8CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_8CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_16CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_16CHAN_2K_HASH:
      rehashVector = 0;
      numSocketsInterleaved = extractDramIntLvNumSkts (dfType, dramAddressMapRegs) + 1;
      // Hashing must use interleave address bit 8
      ASSERT (intLvAddrBit == 8);
      // Does not support die interleaving
      ASSERT (decodeDramIntLvNumDies (dfType, dramAddressMapRegs) == 1);
      ASSERT (numSocketsInterleaved <= 2);
      totalChannelsInterleaved = getNumChannelFromDramIntLvMode (intLvMode);
      // In Turin, the number of channels encoded in IntLvNumChan already includes socket interleaving.
      if (dfType->dfVersion != DF_TYPE_DF4POINT5)
      {
        totalChannelsInterleaved *= numSocketsInterleaved;
      }
      rehashVector = totalChannelsInterleaved - 1;
      rehashVector = rehashVector << 8;
      if ((intLvMode == INTERLEAVE_MODE_DF45_NPS2_4CHAN_1K_HASH) ||
          (intLvMode == INTERLEAVE_MODE_DF45_NPS1_8CHAN_1K_HASH) ||
          (intLvMode == INTERLEAVE_MODE_DF45_NPS1_16CHAN_1K_HASH))
      {
        rehashVector = expandBits64 (10, 2, rehashVector);
      } else
      {
        rehashVector = expandBits64 (9, 3, rehashVector);
      }
      if ((rehashVector & (1 << 8)) != 0)
      {
        hashedBit = (UINT32)(getBit64 (8, deNormAddr) ^
                             (getBit64 (16, deNormAddr) & hashIntlvCtl64K) ^
                             (getBit64 (21, deNormAddr) & hashIntlvCtl2M) ^
                             (getBit64 (30, deNormAddr) & hashIntlvCtl1G) ^
                             (getBit64 (40, deNormAddr) & hashIntlvCtl1T));
        // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "hashedBit=%d\n", hashedBit);
        if (getBit64 (8, deNormAddr) != hashedBit)
        {
          deNormAddr ^= (((UINT64)1) << 8);
        }
      }
      if ((rehashVector & (1 << 9)) != 0)
      {
        hashedBit = (UINT32)(getBit64 (9, deNormAddr) ^
                             (getBit64 (17, deNormAddr) & hashIntlvCtl64K) ^
                             (getBit64 (22, deNormAddr) & hashIntlvCtl2M) ^
                             (getBit64 (31, deNormAddr) & hashIntlvCtl1G) ^
                             (getBit64 (41, deNormAddr) & hashIntlvCtl1T));
        // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "hashedBit=%d\n", hashedBit);
        if (getBit64 (9, deNormAddr) != hashedBit)
        {
          deNormAddr ^= (((UINT64)1) << 9);
        }
      }
      if ((rehashVector & (1 << 12)) != 0)
      {
        hashedBit = (UINT32)(getBit64 (12, deNormAddr) ^
                             (getBit64 (18, deNormAddr) & hashIntlvCtl64K) ^
                             (getBit64 (23, deNormAddr) & hashIntlvCtl2M) ^
                             (getBit64 (32, deNormAddr) & hashIntlvCtl1G) ^
                             (getBit64 (42, deNormAddr) & hashIntlvCtl1T));
        // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "hashedBit=%d\n", hashedBit);
        if (getBit64 (12, deNormAddr) != hashedBit)
        {
          deNormAddr ^= (((UINT64)1) << 12);
        }
      }
      if ((rehashVector & (1 << 13)) != 0)
      {
        hashedBit = (UINT32)(getBit64 (13, deNormAddr) ^
                             (getBit64 (19, deNormAddr) & hashIntlvCtl64K) ^
                             (getBit64 (24, deNormAddr) & hashIntlvCtl2M) ^
                             (getBit64 (33, deNormAddr) & hashIntlvCtl1G) ^
                             (getBit64 (43, deNormAddr) & hashIntlvCtl1T));
        // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "hashedBit=%d\n", hashedBit);
        if (getBit64 (13, deNormAddr) != hashedBit)
        {
          deNormAddr ^= (((UINT64)1) << 13);
        }
      }
      if ((rehashVector & (1 << 14)) != 0)
      {
        hashedBit = (UINT32)(getBit64 (14, deNormAddr) ^
                             (getBit64 (20, deNormAddr) & hashIntlvCtl64K) ^
                             (getBit64 (25, deNormAddr) & hashIntlvCtl2M) ^
                             (getBit64 (34, deNormAddr) & hashIntlvCtl1G) ^
                             (getBit64 (44, deNormAddr) & hashIntlvCtl1T));
        // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "hashedBit=%d\n", hashedBit);
        if (getBit64 (14, deNormAddr) != hashedBit)
        {
          deNormAddr ^= (((UINT64)1) << 14);
        }
      }
      break;
      /************************/
    case INTERLEAVE_MODE_DF45_NPS0_24CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS0_24CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_12CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_12CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_10CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS1_10CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS2_6CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS2_6CHAN_2K_HASH:
      // For these cases, the hashing was done as the CS ID was inserted
      // Since it depended on functions that were already calculated.
      break;
    case INTERLEAVE_MODE_DF4_NPS4_3CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS2_6CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS1_12CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS2_5CHAN_HASH:
    case INTERLEAVE_MODE_DF4_NPS1_10CHAN_HASH:
      // For these cases, the hashing was done as the CS ID was inserted
      // Since it depended on functions that were already calculated.
      break;
    case INTERLEAVE_MODE_DF45_NPS2_5CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS2_5CHAN_2K_HASH:
    case INTERLEAVE_MODE_DF45_NPS4_3CHAN_1K_HASH:
    case INTERLEAVE_MODE_DF45_NPS4_3CHAN_2K_HASH:
      // For these cases, there is no hashPA bits part of the calculation,
      // so nothing to do.
      break;
    default:
      ASSERT (FALSE);
  }

  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "deNormHashAddr completed - 0x%016lX\n", deNormAddr);

  return (deNormAddr);
}

/*------------------------------------------------------------------
 Function: findModeratorInstanceId
 Purpose: Helper function to find a CCM or GCM instance ID on the given node
 Inputs:  DF type and nodeID
 Outputs: The first CCM or GCM instance ID. One must be found!
          A CCM is returned if found.
 *------------------------------------------------------------------*/
UINT32
findModeratorInstanceId (
  DfType *dfType,
  UINT32 nodeId
  )
{
  UINT32 i;
  UINT32 numDFInstances;
  UINT32 fabricBlockInstanceInformation0Reg;
  INT32 firstGcmFabricId = -1;

  // Find a CCM instance ID
  numDFInstances = getBits (DF__BLOCK_INSTANCE_COUNT_BITPOS_LO, DF__BLOCK_INSTANCE_COUNT_BITPOS_HI, getDfRegFabricBlkInstanceCnt (dfType, nodeId));
  for (i = 0; i < numDFInstances; i++)
  {
    fabricBlockInstanceInformation0Reg = getDfRegFabricBlkInstInfo0 (dfType, i, nodeId);
    // Skip gated blocks (detected because at least one bit must be non-zero in non-gated blocks)
    if (fabricBlockInstanceInformation0Reg == 0)
    {
      continue;
    }
    if (getBits (DF__INSTANCE_TYPE_BITPOS_LO, DF__INSTANCE_TYPE_BITPOS_HI, fabricBlockInstanceInformation0Reg) == DF__CCM_INSTANCE_TYPE_VALUE)
    {
      // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "ccmInstanceId=%d\n", i);
      return (i);
    }
    if ((getBits (DF__INSTANCE_TYPE_BITPOS_LO, DF__INSTANCE_TYPE_BITPOS_HI, fabricBlockInstanceInformation0Reg) == DF__GCM_INSTANCE_TYPE_VALUE)
        && firstGcmFabricId < 0)
    {
      firstGcmFabricId = i;
    }
  }
  // If a CCM was not found, then this must be a dGPU. Return the GCM found.
  // All parts have at least one CCM or one GCM.
  if (firstGcmFabricId >= 0)
  {
    // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "gcmInstanceId=%d\n", firstGcmFabricId);
    return (firstGcmFabricId);
  }
  // No moderator found!!!
  ASSERT (FALSE);
  return 0;
}

/*------------------------------------------------------------------
 Function: findMapRegBySysAddr
 Purpose: Helper function to find a map register for a given system address
          This should only be used for DF3.5 heterogeneous systems,
          it is a shortcut to trying to read the dGPU node.
 Inputs:  dfType, nodeID and address
 Outputs: The adddress map number to which this address assigned
          This address map will be on the first CCM (found by findModeratorInstanceId)
 *------------------------------------------------------------------*/
UINT32
findMapRegBySysAddr (
  DfType *dfType,
  UINT32 nodeId,
  UINT64 sysAddr
  )
{
  UINT32 ccmInstanceId;
  UINT32 mapRegNumber;
  UINT32 dramAddressMapRegs[ADDR_MAP_ARRAYSIZE];
  UINT64 dramBaseAddr, dramLimitAddr;

  ASSERT (dfType->dfVersion == DF_TYPE_DF3POINT5); // only use for heterogeneous systems!
  // Find a CCM instance ID
  ccmInstanceId = findModeratorInstanceId (dfType, nodeId);
  for (mapRegNumber = 0; mapRegNumber < getNumAddressMaps (dfType); mapRegNumber++)
  {
    getDramAddressMap (dfType, ccmInstanceId, nodeId, mapRegNumber, dramAddressMapRegs);
    if (extractDramAddrRangeValid (dfType, dramAddressMapRegs) == 0)
    {
      continue;
    }
    dramBaseAddr = extractDramBaseAddr (dfType, dramAddressMapRegs);
    dramLimitAddr = extractDramLimitAddr (dfType, dramAddressMapRegs);
    // Check if we hit in this address map (address within base and limit)
    if ((sysAddr >= dramBaseAddr) && (sysAddr <= dramLimitAddr))
    {
      return (mapRegNumber);
    }
  }

  // missed in address maps.
  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_ERROR, "findMapRegBySysAddr: SysAddr 0x%016lX missed DRAM maps\n",
      sysAddr);
  ASSERT (FALSE);
  return 0;
}

/*------------------------------------------------------------------
 Function: findMapRegByDstFabricId
 Purpose: Helper function to find a map register for a given DstFabricId
          This should only be used for DF3.5 heterogeneous systems,
          it is a shortcut to trying to read the dGPU node.
 Inputs:  dfType, nodeID and address
 Outputs: The adddress map number to which this address assigned
          This address map will be on the first CCM (found by findModeratorInstanceId)
 *------------------------------------------------------------------*/
UINT32
findMapRegByDstFabricId (
  DfType *dfType,
  UINT32 nodeId,
  UINT32 dstFabricId
  )
{
  UINT32 ccmInstanceId;
  UINT32 mapRegNumber;
  UINT32 nodeIdMask;
  UINT32 dramAddressMapRegs[ADDR_MAP_ARRAYSIZE];
  ASSERT (dfType->dfVersion == DF_TYPE_DF3POINT5); // only use for heterogeneous systems!
  // Find a CCM instance ID
  ccmInstanceId = findModeratorInstanceId (dfType, nodeId);
  nodeIdMask = getNodeIdMask (dfType);
  for (mapRegNumber = 0; mapRegNumber < getNumAddressMaps (dfType); mapRegNumber++)
  {
    getDramAddressMap (dfType, ccmInstanceId, nodeId, mapRegNumber, dramAddressMapRegs);
    if (extractDramAddrRangeValid (dfType, dramAddressMapRegs) == 0)
    {
      continue;
    }
    //Compare just the nodeIDs of the dstFabricId
    if ((extractDstFabricId (dfType, dramAddressMapRegs) & nodeIdMask) == (dstFabricId & nodeIdMask))
    {
      return (mapRegNumber);
    }
  }
  // missed in address maps.
  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_ERROR, "findMapRegByDstFabricId: DstFabricId 0x%x missed DRAM maps\n",
      dstFabricId);

  ASSERT (FALSE);
  return 0;
}

/*------------------------------------------------------------------
 Function: calcCxlNormAddrFromCsNormAddr
 Purpose: Further normalize a address into a CXL device address
 Inputs:  dfType, CS FabricID (same one from checkDramHit) and a CS-normalized address
 Outputs: The adddress map number to which this address assigned
          This address map will be on the first CCM (found by findModeratorInstanceId)
 *------------------------------------------------------------------*/
UINT64 calcCxlNormAddrFromCsNormAddr (
  DfType *dfType,
  UINT32 csFabricId,
  UINT64 csNormAddr
  )
{
  UINT32 nodeId, cnliInstanceId, numDFInstances, instanceType, instanceSubType;
  UINT32 fabricBlockInstanceInformation0Reg;
  INT32 i, j, count, cxlSubLink, firstCsInstance, firstCnliInstance;
  UINT32 cxlAddressMapRegs[ADDR_MAP_ARRAYSIZE];
  UINT64 cxlBaseAddr, cxlLimitAddr;
  UINT32 npaEn, npaBaseEn, intLvLinkEn, numInterleaveBits, intLvAddrBit, portIndex;
  // While we have the CS_CMP block, we need to find the associated CNLI block.
  // We will find the delta between the first CS_CMP and the first CNLI, and then add this.
  nodeId = (csFabricId & getNodeIdMask (dfType)) >> getNodeIdShift (dfType);
  numDFInstances = getBits (DF__BLOCK_INSTANCE_COUNT_BITPOS_LO, DF__BLOCK_INSTANCE_COUNT_BITPOS_HI, getDfRegFabricBlkInstanceCnt (dfType, nodeId));
  firstCsInstance = -1;
  firstCnliInstance = -1;
  for (i = 0; (UINT32)i < numDFInstances; i++)
  {
    fabricBlockInstanceInformation0Reg = getDfRegFabricBlkInstInfo0 (dfType, i, nodeId);
    // Skip gated blocks (detected because at least one bit must be non-zero in non-gated blocks)
    if (fabricBlockInstanceInformation0Reg == 0)
    {
      continue;
    }
    instanceType = getBits (DF__INSTANCE_TYPE_BITPOS_LO, DF__INSTANCE_TYPE_BITPOS_HI, fabricBlockInstanceInformation0Reg);
    instanceSubType = getBits (DF__INSTANCE_SUBTYPE_BITPOS_LO, DF__INSTANCE_SUBTYPE_BITPOS_HI, fabricBlockInstanceInformation0Reg);
    if ((firstCsInstance < 0) && (instanceType == DF__CS_INSTANCE_TYPE_VALUE) && (instanceSubType == DF__CSCMP_INSTANCE_SUBTYPE_VALUE))
    {
      firstCsInstance = i;
    }
    if ((firstCnliInstance < 0) && (instanceType == DF__CNLI_INSTANCE_TYPE_VALUE))
    {
      firstCnliInstance = i;
    }
    if ((firstCsInstance > 0) && (firstCnliInstance > 0))
    {
      break;
    }
  }
  ASSERT (firstCsInstance >= 0);
  ASSERT (firstCnliInstance >= 0);
  cnliInstanceId = (csFabricId & getComponentIdMask (dfType)) + (firstCnliInstance - firstCsInstance);
  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "Determined CNLI instance as %d (from CS instance %d)\n",
      cnliInstanceId, (csFabricId & getComponentIdMask (dfType)));
  for (i = 0; i < DF__NUM_CXL_MAPS_AVAILABLE; i++)
  {
    getCxlAddressMap (dfType, cnliInstanceId, nodeId, i, cxlAddressMapRegs);
    // check AddrRngVal
    if (extractCxlAddrRangeValid (dfType, cxlAddressMapRegs) != 0)
    {
      cxlBaseAddr = extractCxlBaseAddr (dfType, cxlAddressMapRegs);
      cxlLimitAddr = extractCxlLimitAddr (dfType, cxlAddressMapRegs);
      // Check if we hit in this address map (address within base and limit)
      if ((csNormAddr >= cxlBaseAddr) && (csNormAddr <= cxlLimitAddr))
      {
        IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "Hit in CXL address map %d\n", i);
        npaEn = extractCxlNpaEn (dfType, cxlAddressMapRegs);
        npaBaseEn = extractCxlNpaBaseEn (dfType, cxlAddressMapRegs);
        intLvLinkEn = extractCxlIntLvLinkEn (dfType, cxlAddressMapRegs);
        numInterleaveBits = decodeCxlNumSubChannelInterleaveBits (dfType, cxlAddressMapRegs);
        intLvAddrBit = decodeCxlIntLvAddrSel (dfType, cxlAddressMapRegs);
        // Get the address bits that specify what port to interleave to
        if (numInterleaveBits == 0)
        {
          portIndex = 0;
        } else
        {
          portIndex = (UINT32)getBits64 (intLvAddrBit, (intLvAddrBit + numInterleaveBits - 1), csNormAddr);
        }
        IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "NPAEn=%d, NPABaseEn=%d, intLvLinkEn=%d\n",
            npaEn, npaBaseEn, intLvLinkEn);
        IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "portIndex=%d, numInterleaveBits=%d, intLvAddrBit=%d\n",
            portIndex, numInterleaveBits, intLvAddrBit);
        // Search through intLvLinkEn until you find the nth bit
        // For example, let's assume intLvLinkEn=0xC and intLvAddrSel=0 (bit 6).
        // This is 2-way subLink interleaving
        // If addr[6] is 1, then you are looking for the second bit set in intLvLinkEn
        // so the address goes out port 3 of this CXL link.
        count = 0;
        cxlSubLink = -1;
        for (j = 0; j < DF__NUM_CXL_SUBPORTS_PER_LINK; j++)
        {
          if ((intLvLinkEn & (1 << j)) != 0)
          {
            if ((UINT32)count == portIndex)
            {
              cxlSubLink = j;
            }
            count++;
          }
        }
        ASSERT (cxlSubLink >= 0);
        // BOZO: Find a way to pass this back to the caller
        IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "CXL sublink %d is targeted in this address\n", cxlSubLink);
        // First subtract off the CXL base (if enabled)
        if (npaBaseEn == 1)
        {
          csNormAddr = csNormAddr - cxlBaseAddr;
        }
        // Then include the interleave bits (if enabled)
        // Note that if npaEn=0, then the device sees unnormalized addressses
        if ((npaEn == 1) && (numInterleaveBits > 0))
        {
          csNormAddr = removeBits64 (intLvAddrBit, (intLvAddrBit + numInterleaveBits - 1), csNormAddr);
          IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "CXL normalized address after removing bits %d:%d = 0x%016lX\n",
              (intLvAddrBit + numInterleaveBits - 1), intLvAddrBit, csNormAddr);
        }
        IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "CXL normalized address = 0x%016lX\n",
            csNormAddr);
        return (csNormAddr);
      }
    }
  }
  // missed in address maps.
  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "calcCxlNormAddrFromCsNormAddr: csNormAddr 0x%016lX missed CXL maps\n",
      csNormAddr);
  ASSERT (0); // Cannot miss in CXL address map
  return (0);
}

/*----------------------------------------------------------------------------------------*/
/**
 * Convert socket/die/UMC number to the Nth UMC in the system (the same as Nth channel in the system,
 * since one channel per UMC on ZP)
 * The input parameter umc_chan_num is fixed to 0 on ZP
 *
 * @param[in] pkg_no            Socket ID (0..1)
 * @param[in] mpu_no            Die ID (0..3)
 * @param[in] umc_inst_num      UMC ID (0..1)
 * @param[in] umc_chan_num      always = 0 in ZP
 * @retval                      Nth channel in the system
 *----------------------------------------------------------------------------------------*/
UINTN
convert_to_addr_trans_index (
  UINTN  pkg_no,
  UINTN  mpu_no,
  UINTN  umc_inst_num,
  UINTN  umc_chan_num
  )
{
  UINTN    U_CH;
  UINTN    M_U_CH;

  U_CH = ((CHANNEL_PER_UMC * umc_inst_num) + umc_chan_num);
  M_U_CH = ((CHANNEL_PER_UMC * UMC_PER_DIE) * mpu_no) + U_CH;

  return (((CHANNEL_PER_UMC * UMC_PER_DIE * DIE_PER_SOCKET) * pkg_no) + M_U_CH);
}

BOOLEAN
internal_bit_wise_xor (
  UINT32  inp
  )
{
  BOOLEAN   t;
  UINT32    i;

  t = 0;
  for (i = 0; i < 32; i++) {
    t = t ^ ((inp >> i) & 0x1);
  }

  return t;
}

UINT32
smnRegRead (
  UINTN   socket,
  UINTN   die,
  UINTN   umc,
  UINTN   ch,
  UINTN   offset,
  UINTN   BusNumberBase
  )
{
  UINTN   pciAddress;
  UINT32  smnIndex;
  UINT32  value;
  UINT32  umcSmnBase;

  pciAddress = PCI_SEGMENT_LIB_ADDRESS ((BusNumberBase / MAX_PCI_BUS_NUMBER_PER_SEGMENT), (BusNumberBase % MAX_PCI_BUS_NUMBER_PER_SEGMENT), 0, 0, IOHC_NB_SMN_INDEX_2_BIOS);
  umcSmnBase = ((UINT32) umc) << 20;
  smnIndex = umcSmnBase + 0x50000 + (UINT32)offset;
  PciSegmentWrite32 (pciAddress, smnIndex);
  pciAddress = PCI_SEGMENT_LIB_ADDRESS ((BusNumberBase / MAX_PCI_BUS_NUMBER_PER_SEGMENT), (BusNumberBase % MAX_PCI_BUS_NUMBER_PER_SEGMENT), 0, 0, IOHC_NB_SMN_DATA_2_BIOS);
  value = PciSegmentRead32 (pciAddress);
  return value;
}

/*----------------------------------------------------------------------------------------*/
/**
 * Read related register and save to global variable
 *
 * @param[in] pkgnoe            Die ID
 * @param[in] mpuno             Channel ID
 * @param[in] umcno             UMC ID
 * @param[in] umcchno           always = 0 in ZP
 * @param[in] BusNumberBase     PCI bus number
 * @retval    VOID
 *----------------------------------------------------------------------------------------*/
VOID
retrieve_regs (
  UINTN   pkgno,
  UINTN   mpuno,
  UINTN   umcno,
  UINTN   umcchno,
  UINTN   BusNumberBase
  )
{

  LOC = convert_to_addr_trans_index (pkgno, mpuno, umcno, umcchno);

  // UMC0CHx00000000 [DRAM CS Base Address] (BaseAddr), ch0_cs[3:0]_aliasSMN; UMC0CHx0000_000[[C,8,4,0]];
  // [31:1]BaseAddr: Base Address [39:9]
  gAddrData->CSBASE[LOC][0] = ((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x0, BusNumberBase) >> 1) << 1);
  gAddrData->CSBASE[LOC][1] = ((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x4, BusNumberBase) >> 1) << 1);
  gAddrData->CSBASE[LOC][2] = ((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x8, BusNumberBase) >> 1) << 1);
  gAddrData->CSBASE[LOC][3] = ((smnRegRead (pkgno, mpuno, umcno, umcchno, 0xC, BusNumberBase) >> 1) << 1);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "\t\tBaseAddr-x00\t\t Channel %x cs0 = %08x cs1 = %08x cs2 = %08x cs3 = %08x\n",
         LOC, gAddrData->CSBASE[LOC][0], gAddrData->CSBASE[LOC][1], gAddrData->CSBASE[LOC][2], gAddrData->CSBASE[LOC][3]);

  gAddrData->EXT_CSBASE[LOC][0] = (smnRegRead (pkgno, mpuno, umcno, umcchno, 0xB00, BusNumberBase));
  gAddrData->EXT_CSBASE[LOC][1] = (smnRegRead (pkgno, mpuno, umcno, umcchno, 0xB04, BusNumberBase));
  gAddrData->EXT_CSBASE[LOC][2] = (smnRegRead (pkgno, mpuno, umcno, umcchno, 0xB08, BusNumberBase));
  gAddrData->EXT_CSBASE[LOC][3] = (smnRegRead (pkgno, mpuno, umcno, umcchno, 0xB0C, BusNumberBase));

  // UMC0CHx00000020 [DRAM CS Mask Address] (AddrMask), ch0_dimm[1:0]_aliasSMN; UMC0CHx0000_002[4,0];
  // [31:1]AddrMask: Address Mask [39:9]
  gAddrData->CSMASK[LOC][0] = ((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x20, BusNumberBase)>>1)<<1) | 0x1;
  gAddrData->CSMASK[LOC][1] = ((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x24, BusNumberBase)>>1)<<1) | 0x1;
  gAddrData->CSMASK[LOC][2] = ((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x28, BusNumberBase)>>1)<<1) | 0x1;
  gAddrData->CSMASK[LOC][3] = ((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x2c, BusNumberBase)>>1)<<1) | 0x1;
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "\t\tAddrMask-x20\t\tChannel %x Dimm0 = %08x Dimm1 = %08x Dimm2 = %08x Dimm3 = %08x\n",
                   LOC, gAddrData->CSMASK[LOC][0], gAddrData->CSMASK[LOC][1], gAddrData->CSMASK[LOC][2], gAddrData->CSMASK[LOC][3]);

  gAddrData->EXT_CSMASK[LOC][0] = (smnRegRead (pkgno, mpuno, umcno, umcchno, 0xB20, BusNumberBase));
  gAddrData->EXT_CSMASK[LOC][1] = (smnRegRead (pkgno, mpuno, umcno, umcchno, 0xB24, BusNumberBase));
  gAddrData->EXT_CSMASK[LOC][2] = (smnRegRead (pkgno, mpuno, umcno, umcchno, 0xB28, BusNumberBase));
  gAddrData->EXT_CSMASK[LOC][3] = (smnRegRead (pkgno, mpuno, umcno, umcchno, 0xB2c, BusNumberBase));

  // UMC0CHx00000010 [DRAM CS Base Secondary Address] (BaseAddrSec), ch0_cs[3:0]_aliasSMN; UMC0CHx0000_001[[C,8,4,0]];
  // [31:1]BaseAddr: Base Address [39:9]
  gAddrData->CSBASESEC[LOC][0] = ((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x10, BusNumberBase) >> 1) << 1);
  gAddrData->CSBASESEC[LOC][1] = ((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x14, BusNumberBase) >> 1) << 1);
  gAddrData->CSBASESEC[LOC][2] = ((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x18, BusNumberBase) >> 1) << 1);
  gAddrData->CSBASESEC[LOC][3] = ((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x1C, BusNumberBase) >> 1) << 1);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "\t\tBaseAddrSec-x10\t\tChannel %x cs0 = %08x cs1 = %08x cs2 = %08x cs3 = %08x\n",
                   LOC, gAddrData->CSBASESEC[LOC][0], gAddrData->CSBASESEC[LOC][1], gAddrData->CSBASESEC[LOC][2], gAddrData->CSBASESEC[LOC][3]);

  gAddrData->EXT_CSBASESEC[LOC][0] = (smnRegRead (pkgno, mpuno, umcno, umcchno, 0xB10, BusNumberBase));
  gAddrData->EXT_CSBASESEC[LOC][1] = (smnRegRead (pkgno, mpuno, umcno, umcchno, 0xB14, BusNumberBase));
  gAddrData->EXT_CSBASESEC[LOC][2] = (smnRegRead (pkgno, mpuno, umcno, umcchno, 0xB18, BusNumberBase));
  gAddrData->EXT_CSBASESEC[LOC][3] = (smnRegRead (pkgno, mpuno, umcno, umcchno, 0xB1C, BusNumberBase));

  //UMC00CHx00000030...UMC11CHx0000003C [DRAM CS Mask Secondary Address] (UMC::AddrMaskSec)
  gAddrData->CSMASKSEC[LOC][0] = ((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x30, BusNumberBase)>>1)<<1)| 0x1;
  gAddrData->CSMASKSEC[LOC][1] = ((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x34, BusNumberBase)>>1)<<1)| 0x1;
  gAddrData->CSMASKSEC[LOC][2] = ((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x38, BusNumberBase)>>1)<<1)| 0x1;
  gAddrData->CSMASKSEC[LOC][3] = ((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x3c, BusNumberBase)>>1)<<1)| 0x1;
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "\t\tAddrMaskSec-x30\t\tChannel %x Dimm0 = %08x Dimm1 = %08x Dimm2 = %08x Dimm3 = %08x\n",
                       LOC, gAddrData->CSMASKSEC[LOC][0], gAddrData->CSMASKSEC[LOC][1], gAddrData->CSMASKSEC[LOC][2], gAddrData->CSMASKSEC[LOC][3]);

  gAddrData->EXT_CSMASKSEC[LOC][0] = (smnRegRead (pkgno, mpuno, umcno, umcchno, 0xB30, BusNumberBase));
  gAddrData->EXT_CSMASKSEC[LOC][1] = (smnRegRead (pkgno, mpuno, umcno, umcchno, 0xB34, BusNumberBase));
  gAddrData->EXT_CSMASKSEC[LOC][2] = (smnRegRead (pkgno, mpuno, umcno, umcchno, 0xB38, BusNumberBase));
  gAddrData->EXT_CSMASKSEC[LOC][3] = (smnRegRead (pkgno, mpuno, umcno, umcchno, 0xB3c, BusNumberBase));

  //ctrlreg[0]=is_3wayintlv
  //ctrlreg[2]= 0->1+2 or 1->2+1
  //they need to be organically derived
  //if(addrmaskcs0=1=2=secmask0=1=2 || addrmaskcs0=2=3=sec0=2=3)ctrlreg[0]=1 if(csenable3==0 then [1]=1 else 0) else 0
  if(((gAddrData->CSMASK[LOC][0]==gAddrData->CSMASK[LOC][1]) && (gAddrData->CSMASK[LOC][1]==gAddrData->CSMASK[LOC][2]) &&
      (gAddrData->CSMASK[LOC][1]==gAddrData->CSMASKSEC[LOC][0]) && (gAddrData->CSMASKSEC[LOC][0]==gAddrData->CSMASKSEC[LOC][1]) &&
      (gAddrData->CSMASKSEC[LOC][1]==gAddrData->CSMASKSEC[LOC][2]) && (((smnRegRead(pkgno, mpuno, umcno, umcchno, 0x0, BusNumberBase)) & 0x1)) &&
      ((smnRegRead(pkgno, mpuno, umcno, umcchno, 0x4, BusNumberBase)) & 0x1) && ((smnRegRead(pkgno, mpuno, umcno, umcchno, 0x8, BusNumberBase)) & 0x1) &&
      ((smnRegRead(pkgno, mpuno, umcno, umcchno, 0x10, BusNumberBase)) & 0x1) && ((smnRegRead(pkgno, mpuno, umcno, umcchno, 0x14, BusNumberBase)) & 0x1) &&
      ((smnRegRead(pkgno, mpuno, umcno, umcchno, 0x18, BusNumberBase)) & 0x1)) ||
     ((gAddrData->CSMASK[LOC][0]==gAddrData->CSMASK[LOC][3]) && (gAddrData->CSMASK[LOC][3]==gAddrData->CSMASK[LOC][2]) &&
     (gAddrData->CSMASK[LOC][3]==gAddrData->CSMASKSEC[LOC][0]) && (gAddrData->CSMASKSEC[LOC][0]==gAddrData->CSMASKSEC[LOC][3]) &&
     (gAddrData->CSMASKSEC[LOC][3]==gAddrData->CSMASKSEC[LOC][2]) && (((smnRegRead(pkgno, mpuno, umcno, umcchno, 0x0, BusNumberBase)) & 0x1)) &&
     ((smnRegRead(pkgno, mpuno, umcno, umcchno, 0xc, BusNumberBase)) & 0x1) && ((smnRegRead(pkgno, mpuno, umcno, umcchno, 0x8, BusNumberBase)) & 0x1) &&
     ((smnRegRead(pkgno, mpuno, umcno, umcchno, 0x10, BusNumberBase)) & 0x1) && ((smnRegRead(pkgno, mpuno, umcno, umcchno, 0x1c, BusNumberBase)) & 0x1) &&
     ((smnRegRead(pkgno, mpuno, umcno, umcchno, 0x18, BusNumberBase)) & 0x1) )
    ){
      three_way_cs=1;

      if(((smnRegRead(pkgno, mpuno, umcno, umcchno, 0xc, BusNumberBase)) & 0x1)==0)
        two_p_one=1;
      else
        two_p_one=0;
  }
  gAddrData->CTRLREG[LOC][0] = (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x11C, BusNumberBase)>>3) & 0x1)<<1) | ( three_way_cs & 0x1) | (((two_p_one) & 0x1)<<2);
  gAddrData->CTRLREG[LOC][1] = (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x11C, BusNumberBase)>>3) & 0x1)<<1) | ( three_way_cs & 0x1) | (((two_p_one) & 0x1)<<2);
  gAddrData->CTRLREG[LOC][2] = (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x11C, BusNumberBase)>>3) & 0x1)<<1) | ( three_way_cs & 0x1) | (((two_p_one) & 0x1)<<2);
  gAddrData->CTRLREG[LOC][3] = (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x11C, BusNumberBase)>>3) & 0x1)<<1) | ( three_way_cs & 0x1) | (((two_p_one) & 0x1)<<2);

  //UMC00CHx00000010...UMC11CHx0000001C [DRAM CS Base Secondary Address] (UMC::BaseAddrSec)
  //UMC00CHx00000040...UMC11CHx0000004C [DRAM Address Configuration] (UMC::AddrCfg)
  //UMC00CHx00000000...UMC11CHx0000000C [DRAM CS Base Address] (UMC::BaseAddr)
  gAddrData->CONFIGDIMM[LOC][0] =
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x10, BusNumberBase))>>0) & 0x1)<<24) |
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x40, BusNumberBase))>>2) & 0x3)<<20) |
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x40, BusNumberBase))>>16) & 0xf)<<16) |
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x40, BusNumberBase))>>6) & 0x1)<<12) |
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x40, BusNumberBase))>>8) & 0xf)<<8) |
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x40, BusNumberBase))>>4) & 0x3)<<6) |
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x40, BusNumberBase))>>20) & 0x3)<<4) |
    (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x40, BusNumberBase)>>30) & 0x3)<<1) |
    ((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x0, BusNumberBase)) & 0x1);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "\t\tAddrCfg-x40\t\t Channel %x Dimm0 = %08x ([25:24]BaseAddrSec CSEnable, [1:0]BaseAddr CSEnable)\n",
                   LOC, gAddrData->CONFIGDIMM[LOC][0]);

  gAddrData->CONFIGDIMM[LOC][1] =
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x14, BusNumberBase))>>0) & 0x1)<<24) |
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x44, BusNumberBase))>>2) & 0x3)<<20) |
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x44, BusNumberBase))>>16) & 0xf)<<16) |
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x44, BusNumberBase))>>6) & 0x1)<<12) |
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x44, BusNumberBase))>>8) & 0xf)<<8) |
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x44, BusNumberBase))>>4) & 0x3)<<6) |
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x44, BusNumberBase))>>20) & 0x3)<<4) |
    (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x44, BusNumberBase)>>30) & 0x3)<<1) |
    ((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x4, BusNumberBase)) & 0x1);
  gAddrData->CONFIGDIMM[LOC][2] =
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x18, BusNumberBase))>>0) & 0x1)<<24) |
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x48, BusNumberBase))>>2) & 0x3)<<20) |
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x48, BusNumberBase))>>16) & 0xf)<<16) |
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x48, BusNumberBase))>>6) & 0x1)<<12) |
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x48, BusNumberBase))>>8) & 0xf)<<8) |
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x48, BusNumberBase))>>4) & 0x3)<<6) |
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x48, BusNumberBase))>>20) & 0x3)<<4) |
    (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x48, BusNumberBase)>>30) & 0x3)<<1) |
    ((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x8, BusNumberBase)) & 0x1);
  gAddrData->CONFIGDIMM[LOC][3] =
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x1c, BusNumberBase))>>0) & 0x1)<<24) |
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x4c, BusNumberBase))>>2) & 0x3)<<20) |
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x4c, BusNumberBase))>>16) & 0xf)<<16) |
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x4c, BusNumberBase))>>6) & 0x1)<<12) |
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x4c, BusNumberBase))>>8) & 0xf)<<8) |
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x4c, BusNumberBase))>>4) & 0x3)<<6) |
    ((((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x4c, BusNumberBase))>>20) & 0x3)<<4) |
    (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x4c, BusNumberBase)>>30) & 0x3)<<1) |
    ((smnRegRead (pkgno, mpuno, umcno, umcchno, 0xc, BusNumberBase)) & 0x1);

  //UMC00CHx00000050...UMC11CHx0000005C [DRAM Bank Address Select] (UMC::AddrSel)
  // [19:16]BankBit4
  // [15:12]BankBit3
  // [11:8]BankBit2
  // [7:4]BankBit1
  // [3:0]BankBit0
  gAddrData->BANKSELDIMM[LOC][0] = (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x50, BusNumberBase)>>16) & 0xf ) <<16) |
    (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x50, BusNumberBase)>>12) & 0xf ) <<12) |
    (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x50, BusNumberBase)>>8) & 0xf ) <<8) |
    (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x50, BusNumberBase)>>4) & 0xf ) <<4) |
    (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x50, BusNumberBase)) & 0xf ) >>0) ;
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "\t\tAddrSel-x50\t\tChannel %x BankBit Dimm0 = %08x Dimm1 = %08x Dimm2 = %08x Dimm3 = %08x\n",
                   LOC, gAddrData->BANKSELDIMM[LOC][0], gAddrData->BANKSELDIMM[LOC][1], gAddrData->BANKSELDIMM[LOC][2], gAddrData->BANKSELDIMM[LOC][3]);

  gAddrData->BANKSELDIMM[LOC][1] = (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x54, BusNumberBase)>>16) & 0xf ) <<16) |
    (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x54, BusNumberBase)>>12) & 0xf ) <<12) |
    (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x54, BusNumberBase)>>8) & 0xf ) <<8) |
    (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x54, BusNumberBase)>>4) & 0xf ) <<4) |
    (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x54, BusNumberBase)) & 0xf ) >>0) ;
  gAddrData->BANKSELDIMM[LOC][2] = (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x58, BusNumberBase)>>16) & 0xf ) <<16) |
    (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x58, BusNumberBase)>>12) & 0xf ) <<12) |
    (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x58, BusNumberBase)>>8) & 0xf ) <<8) |
    (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x58, BusNumberBase)>>4) & 0xf ) <<4) |
    (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x58, BusNumberBase)) & 0xf ) >>0) ;
  gAddrData->BANKSELDIMM[LOC][3] = (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x5c, BusNumberBase)>>16) & 0xf ) <<16) |
    (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x5c, BusNumberBase)>>12) & 0xf ) <<12) |
    (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x5c, BusNumberBase)>>8) & 0xf ) <<8) |
    (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x5c, BusNumberBase)>>4) & 0xf ) <<4) |
    (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x5c, BusNumberBase)) & 0xf ) >>0) ;

  //UMC00CHx00000050...UMC11CHx0000005C [DRAM Bank Address Select] (UMC::AddrSel)
  // [27:24]Row
  gAddrData->ROWSELDIMM[LOC][0] = (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x50, BusNumberBase)>>24) & 0xff ));
  gAddrData->ROWSELDIMM[LOC][1] = (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x54, BusNumberBase)>>24) & 0xff ));
  gAddrData->ROWSELDIMM[LOC][2] = (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x58, BusNumberBase)>>24) & 0xff ));
  gAddrData->ROWSELDIMM[LOC][3] = (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x5c, BusNumberBase)>>24) & 0xff ));
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "\t\tAddrSel-x50\t\t Channel %x RowHiLo Dimm0 = %08x Dimm1 = %08x Dimm2 = %08x Dimm3 = %08x\n",
                  LOC, gAddrData->ROWSELDIMM[LOC][0], gAddrData->ROWSELDIMM[LOC][1], gAddrData->ROWSELDIMM[LOC][2], gAddrData->ROWSELDIMM[LOC][3]);

  //UMC00CHx00000060...UMC11CHx00000078 [DRAM Column Address Select Low] (UMC::ColSelLo)
  gAddrData->COL0SELDIMM[LOC][0] = (smnRegRead (pkgno, mpuno, umcno, umcchno, 0x60, BusNumberBase));
  gAddrData->COL0SELDIMM[LOC][1] = (smnRegRead (pkgno, mpuno, umcno, umcchno, 0x68, BusNumberBase));
  gAddrData->COL0SELDIMM[LOC][2] = (smnRegRead (pkgno, mpuno, umcno, umcchno, 0x70, BusNumberBase));
  gAddrData->COL0SELDIMM[LOC][3] = (smnRegRead (pkgno, mpuno, umcno, umcchno, 0x78, BusNumberBase));
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "\t\tColSelLo-x60\t\t Channel %x Dimm0 = %08x Dimm1 = %08x Dimm2 = %08x Dimm3 = %08x\n",
                   LOC, gAddrData->COL0SELDIMM[LOC][0], gAddrData->COL0SELDIMM[LOC][1], gAddrData->COL0SELDIMM[LOC][2], gAddrData->COL0SELDIMM[LOC][3]);

  //UMC00CHx00000064...UMC11CHx0000007C [DRAM Column Address Select High] (UMC::ColSelHi)
  gAddrData->COL1SELDIMM[LOC][0] =  (smnRegRead (pkgno, mpuno, umcno, umcchno, 0x64, BusNumberBase));
  gAddrData->COL1SELDIMM[LOC][1] =  (smnRegRead (pkgno, mpuno, umcno, umcchno, 0x6C, BusNumberBase));
  gAddrData->COL1SELDIMM[LOC][2] =  (smnRegRead (pkgno, mpuno, umcno, umcchno, 0x74, BusNumberBase));
  gAddrData->COL1SELDIMM[LOC][3] =  (smnRegRead (pkgno, mpuno, umcno, umcchno, 0x7c, BusNumberBase));
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "\t\tColSelHi-x64\t\t Channel %x Dimm0 = %08x Dimm1 = %08x Dimm2 = %08x Dimm3 = %08x\n",
                   LOC, gAddrData->COL1SELDIMM[LOC][0], gAddrData->COL1SELDIMM[LOC][1], gAddrData->COL1SELDIMM[LOC][2], gAddrData->COL1SELDIMM[LOC][3]);

  //UMC00CHx00000080...UMC11CHx0000008C [DRAM Rank Multiply Address Select] (UMC::RmSel)
  gAddrData->RMSELDIMM[LOC][0] = (smnRegRead (pkgno, mpuno, umcno, umcchno, 0x80, BusNumberBase));
  gAddrData->RMSELDIMM[LOC][1] = (smnRegRead (pkgno, mpuno, umcno, umcchno, 0x84, BusNumberBase));
  gAddrData->RMSELDIMM[LOC][2] = (smnRegRead (pkgno, mpuno, umcno, umcchno, 0x88, BusNumberBase));
  gAddrData->RMSELDIMM[LOC][3] = (smnRegRead (pkgno, mpuno, umcno, umcchno, 0x8c, BusNumberBase));
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "\t\tRmSel-x80\t\tChannel %x Dimm0 = %08x Dimm1 = %08x Dimm2 = %08x Dimm3 = %08x\n",
                   LOC, gAddrData->RMSELDIMM[LOC][0], gAddrData->RMSELDIMM[LOC][1], gAddrData->RMSELDIMM[LOC][2], gAddrData->RMSELDIMM[LOC][3]);

  gAddrData->ADDRHASHBANK0[LOC] = smnRegRead (pkgno, mpuno, umcno, umcchno, 0x98, BusNumberBase);
  gAddrData->ADDRHASHBANK1[LOC] = smnRegRead (pkgno, mpuno, umcno, umcchno, 0x9C, BusNumberBase);
  gAddrData->ADDRHASHBANK2[LOC] = smnRegRead (pkgno, mpuno, umcno, umcchno, 0xA0, BusNumberBase);
  gAddrData->ADDRHASHBANK3[LOC] = smnRegRead (pkgno, mpuno, umcno, umcchno, 0xA4, BusNumberBase);
  gAddrData->ADDRHASHBANK4[LOC] = smnRegRead (pkgno, mpuno, umcno, umcchno, 0xA8, BusNumberBase);

  gAddrData->ADDRHASHNORMADDR[LOC][0] = smnRegRead (pkgno, mpuno, umcno, umcchno, 0xC8, BusNumberBase);
  gAddrData->ADDRHASHNORMADDR[LOC][1] = smnRegRead (pkgno, mpuno, umcno, umcchno, 0xCC, BusNumberBase);
  gAddrData->ADDRHASHNORMADDR[LOC][2] = 0;

  gAddrData->EXT_ADDRHASHNORMADDR[LOC][0] = (UINT8) smnRegRead (pkgno, mpuno, umcno, umcchno, 0xB98, BusNumberBase);
  gAddrData->EXT_ADDRHASHNORMADDR[LOC][1] = (UINT8) smnRegRead (pkgno, mpuno, umcno, umcchno, 0xBCC, BusNumberBase);
  gAddrData->EXT_ADDRHASHNORMADDR[LOC][2] = 0;

  gAddrData->ADDRHASHRMADDR[LOC][0]= smnRegRead (pkgno, mpuno, umcno, umcchno, 0xB0, BusNumberBase);
  gAddrData->ADDRHASHRMADDR[LOC][1]= smnRegRead (pkgno, mpuno, umcno, umcchno, 0xB4, BusNumberBase);
  gAddrData->ADDRHASHRMADDR[LOC][2]= smnRegRead (pkgno, mpuno, umcno, umcchno, 0xB8, BusNumberBase);

  gAddrData->EXT_ADDRHASHRMADDR[LOC][0]= (UINT8) smnRegRead (pkgno, mpuno, umcno, umcchno, 0xBB4, BusNumberBase);
  gAddrData->EXT_ADDRHASHRMADDR[LOC][1]= (UINT8) smnRegRead (pkgno, mpuno, umcno, umcchno, 0xBB8, BusNumberBase);
  gAddrData->EXT_ADDRHASHRMADDR[LOC][2]= (UINT8) smnRegRead (pkgno, mpuno, umcno, umcchno, 0xBBC, BusNumberBase);

  if(gAddrData->CTRLREG[LOC][0]>>1 & 0x1){
    gAddrData->ADDRHASHPC[LOC]= smnRegRead (pkgno, mpuno, umcno, umcchno, 0xc0, BusNumberBase);
    gAddrData->ADDRHASHPC2[LOC]= smnRegRead (pkgno, mpuno, umcno, umcchno, 0xc4, BusNumberBase);
  }

  gAddrData->TOTAL_NUM_RANKS_PER_UMCCH_ADDR_TRANS[LOC] = (((smnRegRead(pkgno, mpuno, umcno, umcchno, 0x4, BusNumberBase)) & 0x1)) +
                                                   ((smnRegRead(pkgno, mpuno, umcno, umcchno, 0x0, BusNumberBase)) & 0x1) +
                                                   ((smnRegRead(pkgno, mpuno, umcno, umcchno, 0x8, BusNumberBase)) & 0x1) +
                                                   ((smnRegRead(pkgno, mpuno, umcno, umcchno, 0xC, BusNumberBase)) & 0x1) ;

  gAddrData->RANK_ENABLE_PER_UMCCH_ADDR_TRANS[LOC] =  ((((smnRegRead(pkgno, mpuno, umcno, umcchno, 0xC, BusNumberBase)) & 0x1))<<3)|
                                               ((((smnRegRead(pkgno, mpuno, umcno, umcchno, 0x8, BusNumberBase)) & 0x1)) <<2)|
                                               ((((smnRegRead(pkgno, mpuno, umcno, umcchno, 0x4, BusNumberBase)) & 0x1))<<1)|
                                                 ((smnRegRead(pkgno, mpuno, umcno, umcchno, 0x0, BusNumberBase)) & 0x1);


  //UMC00CHx00000090...UMC11CHx00000094 [DIMM Configuration] (UMC::DimmCfg)
  //[4] - This bit specifies if a RDIMM is populated on a given UMC channel
  gAddrData->DimmPresent[pkgno][umcno] = (((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x90, BusNumberBase) & BIT4) != 0) ||
                                          ((smnRegRead (pkgno, mpuno, umcno, umcchno, 0x94, BusNumberBase) & BIT4) != 0)) ? TRUE : FALSE;

}

/*----------------------------------------------------------------------------------------*/
/**
 * this function returns the (number of "1"s in inp)+8. mainly used to count it in CSMask
 *----------------------------------------------------------------------------------------*/
UINT32
popcnt (
  UINT32  inp
  )
{
  UINT32    ans=0, i=0;

  for (i = 0; i < 32; i++) {
    if ((inp >> i) & 0x1) {
      ans ++;
    }
  }
  return (ans + 8);
}

UINT32
popcnt64 (
  UINT64  inp
  )
{
  UINT32    ans=0, i=0;

  for (i = 0; i < 64; i++) {
    if ((inp >> i) & 0x1) {
      ans ++;
    }
  }
  return (ans + 8);
}

/*----------------------------------------------------------------------------------------*/
/**
 *  Function returns a Bank, Row, and Col as seen on the DRAM Command/Addr bus.
 *----------------------------------------------------------------------------------------*/
VOID
NormalizedToBankAddrMap(
  UINT64 ChannelAddr,
  UINT8 *Bank,
  UINT32 *Row,
  UINT16 *Col,
  UINT8 *Rankmul,
  UINT8 *Subchan,
  UINT8 numbankbits,
  UINT8 bank4,
  UINT8 bank3,
  UINT8 bank2,
  UINT8 bank1,
  UINT8 bank0,
  UINT8 numrowlobits,
  UINT8 numcolbits,
  UINT8 row_lo0,
  UINT32 COL0REG,
  UINT32 COL1REG,
  UINT8 numcsbits,
  UINT8 rm0,
  UINT8 rm1,
  UINT8 rm2,
  UINT8 chan,
  UINT8 invertmsbse,
  UINT8 invertmsbso,
  UINT8 SEC,
  UINT8 cs,
  UINT32 addrhashbank0,
  UINT32 addrhashbank1,
  UINT32 addrhashbank2,
  UINT32 addrhashbank3,
  UINT32 addrhashbank4,
  UINT32 addrhashpc,
  UINT32 addrhashpc2,
  UINT8 vcmen
  )
{

  if(SEC==3) {
    //IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "ERROR: SEC value cannot be 3\n");
    ASSERT (FALSE);
  }

  //if addrhash is enabled, we can get bank after gettting row and col
  if(((addrhashbank0 & 0x1)==0) && ((addrhashbank1 & 0x1)==0) && ((addrhashbank2 & 0x1)==0) &&
      ((addrhashbank3 & 0x1)==0) && ((addrhashbank4 & 0x1)==0)) {
    *Bank = (numbankbits ==3) ? (((ChannelAddr >> (bank0 + 5)) &0x1) |
                                (((ChannelAddr >> (bank1 + 5)) &0x1)<<1) |
                                (((ChannelAddr >> (bank2 + 5)) &0x1)<<2)) :
            (numbankbits ==4) ? (((ChannelAddr >> (bank0 + 5)) &0x1) | (((ChannelAddr >> (bank1 + 5)) &0x1)<<1) |
                                (((ChannelAddr >> (bank2 + 5)) &0x1)<<2) | (((ChannelAddr >> (bank3 + 5)) &0x1)<<3) ) :
                                (((ChannelAddr >> (bank0 + 5)) &0x1) | (((ChannelAddr >> (bank1 + 5)) &0x1)<<1) |
                                (((ChannelAddr >> (bank2 + 5)) &0x1)<<2) | (((ChannelAddr >> (bank3 + 5)) &0x1)<<3) |
                                (((ChannelAddr >> (bank4 + 5)) &0x1)<<4)) ;

  }//IF addr hash is disabled

  if((numcolbits >= 5) && (numcolbits <= 8)) {
    *Col = ( (ChannelAddr >> (((COL0REG >> 0) & 0xF) + 2)) & 0x1 ) | (( (ChannelAddr >> (((COL0REG >> 4) & 0xF) + 2)) & 0x1 ) << 1) |
               (( (ChannelAddr >> (((COL0REG >> 8) & 0xF) + 2)) & 0x1 ) << 2) | (( (ChannelAddr >> (((COL0REG >> 12) & 0xF) + 2)) & 0x1 ) << 3) |
               (( (ChannelAddr >> (((COL0REG >> 16) & 0xF) + 2)) & 0x1 ) << 4) | (( (ChannelAddr >> (((COL0REG >> 20) & 0xF) + 2)) & 0x1 ) << 5) |
               (( (ChannelAddr >> (((COL0REG >> 24) & 0xF) + 2)) & 0x1 ) << 6) | (( (ChannelAddr >> (((COL0REG >> 28) & 0xF) + 2)) & 0x1 ) << 7);
   } else {
             *Col = ( (ChannelAddr >> (((COL0REG >> 0) & 0xF) + 2)) & 0x1 ) | (( (ChannelAddr >> (((COL0REG >> 4) & 0xF) + 2)) & 0x1 ) << 1) |
               (( (ChannelAddr >> (((COL0REG >> 8) & 0xF) + 2)) & 0x1 ) << 2) | (( (ChannelAddr >> (((COL0REG >> 12) & 0xF) + 2)) & 0x1 ) << 3) |
               (( (ChannelAddr >> (((COL0REG >> 16) & 0xF) + 2)) & 0x1 ) << 4) | (( (ChannelAddr >> (((COL0REG >> 20) & 0xF) + 2)) & 0x1 ) << 5) |
               (( (ChannelAddr >> (((COL0REG >> 24) & 0xF) + 2)) & 0x1 ) << 6) | (( (ChannelAddr >> (((COL0REG >> 28) & 0xF) + 2)) & 0x1 ) << 7) |

               ( ((ChannelAddr >> (((COL1REG >> 0) & 0xF) + 8)) & 0x1) << 8 ) | (( (ChannelAddr >> (((COL1REG >> 4) & 0xF) + 8)) & 0x1 ) << 9) |
               (( (ChannelAddr >> (((COL1REG >> 8) & 0xF) + 8)) & 0x1 ) << 10) | (( (ChannelAddr >> (((COL1REG >> 12) & 0xF) + 8)) & 0x1 ) << 11) |
               (( (ChannelAddr >> (((COL1REG >> 16) & 0xF) + 8)) & 0x1 ) << 12) | (( (ChannelAddr >> (((COL1REG >> 20) & 0xF) + 8)) & 0x1 ) << 13) |
               (( (ChannelAddr >> (((COL1REG >> 24) & 0xF) + 8)) & 0x1 ) << 14) | (( (ChannelAddr >> (((COL1REG >> 28) & 0xF) + 8)) & 0x1 ) << 15);

  }
  *Col = *Col & (((UINT16)pow_ras(2,(UINTN)numcolbits))-1);

  //will work for regular dimm, non power of 2 dimms are handled later
  *Row = (UINT32)(((ChannelAddr >> (row_lo0+12)) & ((UINT64)pow_ras(2,(UINTN)numrowlobits)-1)));

  if(SEC!=2) {
    *Row = ((((*Row>>(numrowlobits-1)) & 0x1 ) ^ (SEC ? (((invertmsbso>>1) & 0x1)) : (((invertmsbse>>1) & 0x1))))<<(numrowlobits-1)) |
             ((((*Row>>(numrowlobits-2)) & 0x1 ) ^ (SEC ? (((invertmsbso>>0) & 0x1)) : ( ((invertmsbse>>0) & 0x1))))<<(numrowlobits-2)) |
             (*Row & (((UINT64)pow_ras(2,(UINTN)numrowlobits-2))-1));
  }


  if (((addrhashbank0) & 0x1) || ((addrhashbank1) & 0x1) || ((addrhashbank2) & 0x1) || ((addrhashbank3) & 0x1) || ((addrhashbank4) & 0x1)) {
    *Bank = (numbankbits ==3) ? ((ChannelAddr >> (bank0 + 5) &0x1) |
                                ((ChannelAddr >> (bank1 + 5) &0x1)<<1) |
                                ((ChannelAddr >> (bank2 + 5) &0x1)<<2)) :
            (numbankbits ==4) ? ((ChannelAddr >> (bank0 + 5) &0x1) |
                                ((ChannelAddr >> (bank1 + 5) &0x1)<<1) |
                                ((ChannelAddr >> (bank2 + 5) &0x1)<<2) |
                                ((ChannelAddr >> (bank3 + 5) &0x1)<<3) ) :
                                ((ChannelAddr >> (bank0 + 5) &0x1) |
                                ((ChannelAddr >> (bank1 + 5) &0x1)<<1) |
                                ((ChannelAddr >> (bank2 + 5) &0x1)<<2) |
                                ((ChannelAddr >> (bank3 + 5) &0x1)<<3) |
                                ((ChannelAddr >> (bank4 + 5) &0x1)<<4)) ;


    gAddrData->addrhash[0] = ((internal_bit_wise_xor(*Col & ((addrhashbank0>>1)&0x1fff)))
                  ^ ( internal_bit_wise_xor(*Row & ((addrhashbank0>>14)&0x3ffff)))) & (addrhashbank0 & 1);
    gAddrData->addrhash[1] = ((internal_bit_wise_xor(*Col & ((addrhashbank1>>1)&0x1fff)))
                  ^ ( internal_bit_wise_xor(*Row & ((addrhashbank1>>14)&0x3ffff)))) & (addrhashbank1 & 1);
    gAddrData->addrhash[2] = ((internal_bit_wise_xor(*Col & ((addrhashbank2>>1)&0x1fff)))
                  ^ ( internal_bit_wise_xor(*Row & ((addrhashbank2>>14)&0x3ffff)))) & (addrhashbank2 & 1);
    gAddrData->addrhash[3] = ((internal_bit_wise_xor(*Col & ((addrhashbank3>>1)&0x1fff)))
                  ^ ( internal_bit_wise_xor(*Row & ((addrhashbank3>>14)&0x3ffff)))) & (addrhashbank3 & 1);
    gAddrData->addrhash[4] = ((internal_bit_wise_xor(*Col & ((addrhashbank4>>1)&0x1fff)))
                  ^ ( internal_bit_wise_xor(*Row & ((addrhashbank4>>14)&0x3ffff)))) & (addrhashbank4 & 1);


    *Bank = (numbankbits ==3) ? ((((addrhashbank0 & 0x1)==1) ? (gAddrData->addrhash[0] ^ (*Bank & 0x1)) : (*Bank & 0x1)) |
                                ((((addrhashbank1 & 0x1)==1) ? (gAddrData->addrhash[1] ^ ((*Bank>>1) & 0x1)) : ((*Bank>>1) & 0x1))<<1) |
                                ((((addrhashbank2 & 0x1)==1) ? (gAddrData->addrhash[2] ^ ((*Bank>>2) & 0x1)) : ((*Bank>>2) & 0x1))<<2)) :
            (numbankbits ==4) ? ((((addrhashbank0 & 0x1)==1) ? (gAddrData->addrhash[0] ^ (*Bank & 0x1)) : (*Bank & 0x1)) |
                                ((((addrhashbank1 & 0x1)==1) ? (gAddrData->addrhash[1] ^ ((*Bank>>1) & 0x1)) : ((*Bank>>1) & 0x1))<<1) |
                                ((((addrhashbank2 & 0x1)==1) ? (gAddrData->addrhash[2] ^ ((*Bank>>2) & 0x1)) : ((*Bank>>2) & 0x1))<<2) |
                                ((((addrhashbank3 & 0x1)==1) ? (gAddrData->addrhash[3] ^ ((*Bank>>3) & 0x1)) : ((*Bank>>3) & 0x1))<<3)) :
                                ((((addrhashbank0 & 0x1)==1) ? (gAddrData->addrhash[0] ^ (*Bank & 0x1)) : (*Bank & 0x1)) |
                                ((((addrhashbank1 & 0x1)==1) ? (gAddrData->addrhash[1] ^ ((*Bank>>1) & 0x1)) : ((*Bank>>1) & 0x1))<<1) |
                                ((((addrhashbank2 & 0x1)==1) ? (gAddrData->addrhash[2] ^ ((*Bank>>2) & 0x1)) : ((*Bank>>2) & 0x1))<<2) |
                                ((((addrhashbank3 & 0x1)==1) ? (gAddrData->addrhash[3] ^ ((*Bank>>3) & 0x1)) : ((*Bank>>3) & 0x1))<<3) |
                                ((((addrhashbank4 & 0x1)==1) ? (gAddrData->addrhash[4] ^ ((*Bank>>4) & 0x1)) : ((*Bank>>4) & 0x1))<<4));

  }

   *Rankmul = numcsbits == 0? 0 : (numcsbits == 1? ((ChannelAddr>>(rm0+12)) &0x1) :
                numcsbits ==2? (((ChannelAddr>>(rm0+12))&0x1) | (((ChannelAddr>>(rm1+12))&0x1)<<1)):(((ChannelAddr>>(rm0+12))&0x1) |
                               (((ChannelAddr>>(rm1+12))&0x1)<<1) | (((ChannelAddr>>(rm2+12))&0x1)<<2)));

  if(vcmen){
    if (((addrhashpc & 0x1)==0))
    {
      *Subchan = (((ChannelAddr >> (chan+5)) & 0x1 ));
    }
    else
    {
       gAddrData->addrhash[5] = ((internal_bit_wise_xor(*Col & ((addrhashpc>>1)&0x1fff))) ^ ( internal_bit_wise_xor(*Row & ((addrhashpc>>14)&0x3ffff))))  & (addrhashpc & 1);
       gAddrData->addrhash[5] = gAddrData->addrhash[5] ^ (internal_bit_wise_xor(((numbankbits ==3) ? ((ChannelAddr >> (bank0 + 5) &0x1) | ((ChannelAddr >> (bank1 + 5) &0x1)<<1) |
         ((ChannelAddr >> (bank2 + 5) &0x1)<<2)) : (numbankbits ==4) ? ((ChannelAddr >> (bank0 + 5) &0x1) | ((ChannelAddr >> (bank1 + 5) &0x1)<<1) | ((ChannelAddr >> (bank2 + 5) &0x1)<<2) |
         ((ChannelAddr >> (bank3 + 5) &0x1)<<3) ) : ((ChannelAddr >> (bank0 + 5) &0x1) | ((ChannelAddr >> (bank1 + 5) &0x1)<<1) | ((ChannelAddr >> (bank2 + 5) &0x1)<<2) |
         ((ChannelAddr >> (bank3 + 5) &0x1)<<3) | ((ChannelAddr >> (bank4 + 5) &0x1)<<4)))  & (addrhashpc2 & 0x1f))) ;
       *Subchan =  (addrhashpc & 0x1 ) ? ((((ChannelAddr >> (chan+5)) & 0x1 )) ^ (gAddrData->addrhash[5])) : (((ChannelAddr >> (chan+5)) & 0x1 ));
    }
  }
  else {
    *Subchan = 0;
  }
}

// Function returns a rebuilt normalized address
UINT64 BankAddrToNormalizedMap (UINT32 CSBase, UINT64 CSBaseExt, UINT32 CSMask, UINT64 CSMaskExt, UINT8 Rank, UINT8 Bank, UINT32 Row, UINT16 Col, UINT8 Rankmul, UINT8 Subchan,
  UINT8 numbankbits, UINT8 bank4, UINT8 bank3, UINT8 bank2, UINT8 bank1, UINT8 bank0, UINT8 numrowlobits,
  UINT8 numcolbits, UINT8 row_lo0, UINT32 COL0REG, UINT32 COL1REG, UINT8 numcsbits,
  UINT8 rm0, UINT8 rm1, UINT8 rm2, UINT8 chan, UINT8 invertmsbse,
  UINT8 invertmsbso,
  UINT64 CSMasksec, UINT64 CSMasksecExt, UINT64 CSBasesec, UINT64 CSBasesecExt, UINT8 SEC, UINT32 addrhashbank0,UINT32 addrhashbank1,UINT32 addrhashbank2,
  UINT32 addrhashbank3,UINT32 addrhashbank4, UINT32 addrhashpc, UINT32 addrhashpc2, UINT8 vcmen ) {

  //Initialize the NA=0
  UINT64 ChannelAddr_G = 0 ;

  ChannelAddr_G = ChannelAddr_G | ((((Col >>0 ) & 0x1 ) << (((COL0REG>>0) &0xf)+2)) |
                                  (((Col >>1 ) & 0x1 ) << (((COL0REG>>4) &0xf)+2)) |
                                  (((Col >>2 ) & 0x1 ) << (((COL0REG>>8) &0xf)+2)) |
                                  (((Col >>3 ) & 0x1 ) << (((COL0REG>>12) &0xf)+2)) |
                                  (((Col >>4 ) & 0x1 ) << (((COL0REG>>16) &0xf)+2)));

  if(numcolbits >= 0x6)
  {
    ChannelAddr_G |= (((Col >>5 ) & 0x1 ) << (((COL0REG>>20) &0xf)+2));
  }
  if(numcolbits >= 0x7)
  {
    ChannelAddr_G |= (((Col >>6 ) & 0x1 ) << (((COL0REG>>24) &0xf)+2));
  }
  if(numcolbits >= 0x8)
  {
    ChannelAddr_G |= (((Col >>7 ) & 0x1 ) << (((COL0REG>>28) &0xf)+2));
  }
  if(numcolbits >= 0x9)
  {
    ChannelAddr_G |= (((Col >>8 ) & 0x1 ) << (((COL1REG>>0) &0xf)+8));
  }
  if(numcolbits >= 0xa)
  {
    ChannelAddr_G |= (((Col >>9 ) & 0x1 ) << (((COL1REG>>4) &0xf)+8));
  }
  if(numcolbits >= 0xb)
  {
    ChannelAddr_G |= (((Col >>10 ) & 0x1 ) << (((COL1REG>>8) &0xf)+8));
  }
  if(numcolbits >= 0xc)
  {
    ChannelAddr_G |= (((Col >>11 ) & 0x1 ) << (((COL1REG>>12) &0xf)+8));
  }
  if(numcolbits >= 0xd)
  {
    ChannelAddr_G |= (((Col >>12 ) & 0x1 ) << (((COL1REG>>16) &0xf)+8));
  }
  if(numcolbits >= 0xe)
  {
    ChannelAddr_G |= (((Col >>13 ) & 0x1 ) << (((COL1REG>>20) &0xf)+8));
  }
  if(numcolbits >= 0xf)
  {
    ChannelAddr_G |= (((Col >>14 ) & 0x1 ) << (((COL1REG>>24) &0xf)+8));
  }
  if(numcolbits >= 0x10)
  {
    ChannelAddr_G |= (((Col >>15 ) & 0x1 ) << (((COL1REG>>28) &0xf)+8));
  }


  //Place the row
  //manipulation
  if(SEC !=2)
  {
    Row = ((((Row>>(numrowlobits-1)) & 0x1 ) ^ (SEC ? (((invertmsbso>>1) & 0x1)) : (((invertmsbse>>1) & 0x1))))<<(numrowlobits-1)) |
            ((((Row>>(numrowlobits-2)) & 0x1 ) ^ (SEC ? (((invertmsbso>>0) & 0x1)) : (((invertmsbse>>0) & 0x1))))<<(numrowlobits-2)) |
            (Row & (((UINT64)pow_ras(2,numrowlobits-2))-1));

    ChannelAddr_G = ChannelAddr_G |  (((UINT64)(Row & ((UINT64)pow_ras(2,numrowlobits)-1)))<<(row_lo0+12)) ;

   //Row has been placed
  }


  //For HBM case, let us use Rankmul as the PC bit. its a one bit value. Let us consider Rankmul[0]
  //For HBM, if chan and bank0 are 1 or 2 apart, then we need to do fill bank first and then PC/VC bit else dont care

  //Place the rm


    ChannelAddr_G = ChannelAddr_G |  (numcsbits==0 ? 0 :
                   (numcsbits==1? (((UINT64)(Rankmul & 0x1))<<(rm0+12)):
                    numcsbits ==2? ((((UINT64)(Rankmul&0x1))<<(rm0+12)) |
                                   (((UINT64)((Rankmul>>1)&0x1))<<(rm1+12))) :
                                   ((((UINT64)(Rankmul&0x1))<<(rm0+12)) |
                                   (((UINT64)((Rankmul>>1)&0x1))<<(rm1+12)) |
                                   (((UINT64)((Rankmul>>2)&0x1))<<(rm2+12))))) ;

   //Let us compute Bank at the very end
   //Reason being, if we are in swizzle case, we want the right bits at other bit positions besides original bank bits
   if(((addrhashbank0 & 0x1)==0) && ((addrhashbank1 & 0x1)==0) &&
       ((addrhashbank2 & 0x1)==0) && ((addrhashbank3 & 0x1)==0) && ((addrhashbank4 & 0x1)==0))
   {

     ChannelAddr_G = ChannelAddr_G | ((numbankbits==3)? ((((UINT64)((Bank>>0) & 0x1))<<(bank0+5)) |
                                     (((UINT64)((Bank>>1) & 0x1))<<(bank1+5)) |
                                     (((UINT64)((Bank>>2) & 0x1))<<(bank2+5))) :
                    (numbankbits==4)? ((((UINT64)((Bank>>0) & 0x1))<<(bank0+5)) |
                                      (((UINT64)((Bank>>1) & 0x1))<<(bank1+5)) |
                                      (((UINT64)((Bank>>2) & 0x1))<<(bank2+5)) |
                                      (((UINT64)((Bank>>3) & 0x1))<<(bank3+5))) :
                                      ((((UINT64)((Bank>>0) & 0x1))<<(bank0+5)) |
                                      (((UINT64)((Bank>>1) & 0x1))<<(bank1+5)) |
                                      (((UINT64)((Bank>>2) & 0x1))<<(bank2+5)) |
                                      (((UINT64)((Bank>>3) & 0x1))<<(bank3+5)) |
                                      ((((UINT64)((Bank>>4) & 0x1))<<(bank4+5)))));
   }//addrhash was not on
   else
   {
     gAddrData->addrhash[0] = ((internal_bit_wise_xor(Col & ((addrhashbank0>>1)&0x1fff)))
                   ^ ( internal_bit_wise_xor(Row & ((addrhashbank0>>14)&0x3ffff)))) & (addrhashbank0 & 1);
     gAddrData->addrhash[1] = ((internal_bit_wise_xor(Col & ((addrhashbank1>>1)&0x1fff)))
                   ^ ( internal_bit_wise_xor(Row & ((addrhashbank1>>14)&0x3ffff)))) & (addrhashbank1 & 1);
     gAddrData->addrhash[2] = ((internal_bit_wise_xor(Col & ((addrhashbank2>>1)&0x1fff)))
                   ^ ( internal_bit_wise_xor(Row & ((addrhashbank2>>14)&0x3ffff)))) & (addrhashbank2 & 1);
     gAddrData->addrhash[3] = ((internal_bit_wise_xor(Col & ((addrhashbank3>>1)&0x1fff)))
                   ^ ( internal_bit_wise_xor(Row & ((addrhashbank3>>14)&0x3ffff)))) & (addrhashbank3 & 1);
     gAddrData->addrhash[4] = ((internal_bit_wise_xor(Col & ((addrhashbank4>>1)&0x1fff)))
                   ^ ( internal_bit_wise_xor(Row & ((addrhashbank4>>14)&0x3ffff)))) & (addrhashbank4 & 1);

     if(addrhashbank0 & 0x1)
       ChannelAddr_G = ChannelAddr_G | (((UINT64)((Bank & 0x1) ^ gAddrData->addrhash[0]))<<(bank0+5));
     else
       ChannelAddr_G = ChannelAddr_G | (((UINT64)(Bank & 0x1) )<<(bank0+5));

     if(addrhashbank1 & 0x1)
       ChannelAddr_G = ChannelAddr_G | (((UINT64)(((Bank>>1) & 0x1) ^ gAddrData->addrhash[1]))<<(bank1+5));
     else
       ChannelAddr_G = ChannelAddr_G | (((UINT64)((Bank>>1) & 0x1) )<<(bank1+5));

     if(addrhashbank2 & 0x1)
       ChannelAddr_G = ChannelAddr_G | (((UINT64)(((Bank>>2) & 0x1) ^ gAddrData->addrhash[2]))<<(bank2+5));
     else
       ChannelAddr_G = ChannelAddr_G | (((UINT64)((Bank>>2) & 0x1) )<<(bank2+5));

     if(numbankbits >= 4)
     {
       if(addrhashbank3 & 0x1)
         ChannelAddr_G = ChannelAddr_G | (((UINT64)(((Bank>>3) & 0x1) ^ gAddrData->addrhash[3]))<<(bank3+5));
       else
         ChannelAddr_G = ChannelAddr_G | (((UINT64)((Bank>>3) & 0x1) )<<(bank3+5));

       if(numbankbits == 5)
       {
         if(addrhashbank4 & 0x1)
           ChannelAddr_G = ChannelAddr_G | (((UINT64)(((Bank>>4) & 0x1) ^ gAddrData->addrhash[4]))<<(bank4+5));
         else
           ChannelAddr_G = ChannelAddr_G | (((UINT64)((Bank>>4) & 0x1) )<<(bank4+5));
       }
     }

   }


   if(vcmen){
     if((addrhashpc & 0x1)==0)
     {
         ChannelAddr_G = ChannelAddr_G | ((UINT64)((Subchan) & 0x1) << (chan+5));
     }
     else
     {
          gAddrData->addrhash[5] = ((internal_bit_wise_xor(Col & ((addrhashpc>>1)&0x1fff))) ^ ( internal_bit_wise_xor(Row & ((addrhashpc>>14)&0x3ffff))))  & (addrhashpc & 1);
          gAddrData->addrhash[5] = gAddrData->addrhash[5] ^ (internal_bit_wise_xor(Bank & (addrhashpc2 & 0x1f))) ;
          ChannelAddr_G  = ChannelAddr_G | ((UINT64)(Subchan ^ gAddrData->addrhash[5] ) << (chan+5));
     }
   }

   //Place the cs
   if((SEC==0) || ((SEC==2) && (Rank%2==0)))
   {
     ChannelAddr_G |= (UINT64)(((UINT64)(((((UINT64)(CSBaseExt))<<32)| (CSBase)) & ((((UINT64)(~CSMaskExt))<<32)| (~CSMask))))<<8);
   }
   else
   {
     ChannelAddr_G |= (UINT64)(((UINT64)(((((UINT64)(CSBasesecExt))<<32)| (CSBasesec)) & ((((UINT64)(~CSMasksecExt))<<32)| (~CSMasksec))))<<8);
   }

   return ChannelAddr_G;
}


VOID get_fake_bank_row_col_rm (UINT64 addrhashnormaddr0, UINT8 numbankbits, UINT8 bank0, UINT8 bank1,
    UINT8 bank2, UINT8 bank3, UINT8 bank4, UINT8 row_lo0, UINT8 numrowlobits,
    UINT8 numcsbits, UINT8 rm0, UINT8 rm1,  UINT8 rm2, UINT8 numcolbits,
    UINT32 COL0REG, UINT32 COL1REG, UINT8* fk_bank, UINT16 *fk_col, UINT32  *fk_row, UINT8 *fk_rm
    )
{
    *fk_bank = numbankbits == 3 ? ( ((( (UINT64)(addrhashnormaddr0>>1)<<9)>>(bank0+5)) & 0x1) |
                                    (((((UINT64)(addrhashnormaddr0>>1)<<9)>>(bank1+5)) & 0x1)<<1) |
                                    (((((UINT64)(addrhashnormaddr0>>1)<<9)>>(bank2+5)) & 0x1)<<2) ) :
             numbankbits == 4 ? (((((UINT64)(addrhashnormaddr0>>1)<<9)>>(bank0+5)) & 0x1) |
                                (((((UINT64)(addrhashnormaddr0>>1)<<9)>>(bank1+5)) & 0x1)<<1) |
                                (((((UINT64)(addrhashnormaddr0>>1)<<9)>>(bank2+5)) & 0x1)<<2) |
                                (((((UINT64)(addrhashnormaddr0>>1)<<9)>>(bank3+5)) & 0x1)<<3)) :
                                (((((UINT64)(addrhashnormaddr0>>1)<<9)>>(bank0+5)) & 0x1) |
                                (((((UINT64)(addrhashnormaddr0>>1)<<9)>>(bank1+5)) & 0x1)<<1) |
                                (((((UINT64)(addrhashnormaddr0>>1)<<9)>>(bank2+5)) & 0x1)<<2) |
                                (((((UINT64)(addrhashnormaddr0>>1)<<9)>>(bank3+5)) & 0x1)<<3) |
                                (((((UINT64)(addrhashnormaddr0>>1)<<9)>>(bank4+5)) & 0x1)<<4) ) ;

  *fk_row = (UINT32)((((UINT64)(addrhashnormaddr0>>1)<<9) >> (row_lo0+12)) & ((UINT64)pow_ras(2,numrowlobits)-1)) ;

  *fk_rm =  numcsbits == 0 ? 0 : numcsbits == 1 ? ((((UINT64)(addrhashnormaddr0>>1)<<9) >> (rm0+12)) & 0x1) :
            numcsbits == 2 ? (((((UINT64)(addrhashnormaddr0>>1)<<9) >> (rm0+12)) & 0x1) |
                             (((((UINT64)(addrhashnormaddr0>>1)<<9) >> (rm1+12)) & 0x1)<<1)) :
                             (((((UINT64)(addrhashnormaddr0>>1)<<9) >> (rm0+12)) & 0x1) |
                             (((((UINT64)(addrhashnormaddr0>>1)<<9) >> (rm1+12)) & 0x1)<<1) |
                             (((((UINT64)(addrhashnormaddr0>>1)<<9) >> (rm2+12)) & 0x1)<<2) ) ;

  //*fk_col

  *fk_col = ((((UINT64)(addrhashnormaddr0>>1)<<9) >> (((COL0REG >> 0) & 0xF) + 2)) & 0x1 ) |
            (( (((UINT64)(addrhashnormaddr0>>1)<<9) >> (((COL0REG >> 4) & 0xF) + 2)) & 0x1 ) << 1) |
            (( (((UINT64)(addrhashnormaddr0>>1)<<9) >> (((COL0REG >> 8) & 0xF) + 2)) & 0x1 ) << 2) |
            (( (((UINT64)(addrhashnormaddr0>>1)<<9) >> (((COL0REG >> 12) & 0xF) + 2)) & 0x1 ) << 3) |
            (( (((UINT64)(addrhashnormaddr0>>1)<<9) >> (((COL0REG >> 16) & 0xF) + 2)) & 0x1 ) << 4);

  if(numcolbits >= 0x6)
  {
    *fk_col |= (( (((UINT64)(addrhashnormaddr0>>1)<<9) >> (((COL0REG >> 20) & 0xF) + 2)) & 0x1 ) << 5);
  }
  if(numcolbits >= 0x7)
  {
    *fk_col |= (( (((UINT64)(addrhashnormaddr0>>1)<<9) >> (((COL0REG >> 24) & 0xF) + 2)) & 0x1 ) << 6);
  }
  if(numcolbits >= 0x8)
  {
    *fk_col |= (( (((UINT64)(addrhashnormaddr0>>1)<<9) >> (((COL0REG >> 28) & 0xF) + 2)) & 0x1 ) << 7);
  }
  if(numcolbits >= 0x9)
  {
    *fk_col |= ( ((((UINT64)(addrhashnormaddr0>>1)<<9) >> (((COL1REG >> 0) & 0xF) + 8)) & 0x1) << 8 );
  }
  if(numcolbits >= 0xa)
  {
    *fk_col |= (( (((UINT64)(addrhashnormaddr0>>1)<<9) >> (((COL1REG >> 4) & 0xF) + 8)) & 0x1 ) << 9);
  }
  if(numcolbits >= 0xb)
  {
    *fk_col |= (( (((UINT64)(addrhashnormaddr0>>1)<<9) >> (((COL1REG >> 8) & 0xF) + 8)) & 0x1 ) << 10);
  }
  if(numcolbits >= 0xc)
  {
    *fk_col |= (( (((UINT64)(addrhashnormaddr0>>1)<<9) >> (((COL1REG >> 12) & 0xF) + 8)) & 0x1 ) << 11);
  }
  if(numcolbits >= 0xd)
  {
    *fk_col |= (( (((UINT64)(addrhashnormaddr0>>1)<<9) >> (((COL1REG >> 16) & 0xF) + 8)) & 0x1 ) << 12);
  }
  if(numcolbits >= 0xe)
  {
    *fk_col |= (( (((UINT64)(addrhashnormaddr0>>1)<<9) >> (((COL1REG >> 20) & 0xF) + 8)) & 0x1 ) << 13);
  }
  if(numcolbits >= 0xf)
  {
    *fk_col |= (( (((UINT64)(addrhashnormaddr0>>1)<<9) >> (((COL1REG >> 24) & 0xF) + 8)) & 0x1 ) << 14);
  }
  if(numcolbits >= 0x10)
  {
    *fk_col |= (( (((UINT64)(addrhashnormaddr0>>1)<<9) >> (((COL1REG >> 28) & 0xF) + 8)) & 0x1 ) << 15);
  }

}

VOID unhash_bank (UINT8 bank, UINT32 addrhashbank0, UINT32 addrhashbank1, UINT32 addrhashbank2,
    UINT32 addrhashbank3, UINT32 addrhashbank4, UINT8* outputbnk, UINT16 col, UINT32 row, UINT8 numbankbits)
{
  if (((addrhashbank0 & 1) ==0) && ((addrhashbank1 & 1) ==0) && ((addrhashbank2 & 1) ==0) &&
      ((addrhashbank3 & 1) ==0) && ((addrhashbank4 & 1) ==0))
  {
    *outputbnk = bank;
  }
  else
  {

    gAddrData->addrhash[0] = ((internal_bit_wise_xor(col & ((addrhashbank0>>1)&0x1fff)))
        ^ ( internal_bit_wise_xor(row & ((addrhashbank0>>14)&0x3ffff)))) & (addrhashbank0 & 1);
    gAddrData->addrhash[1] = ((internal_bit_wise_xor(col & ((addrhashbank1>>1)&0x1fff)))
        ^ ( internal_bit_wise_xor(row & ((addrhashbank1>>14)&0x3ffff)))) & (addrhashbank1 & 1);
    gAddrData->addrhash[2] = ((internal_bit_wise_xor(col & ((addrhashbank2>>1)&0x1fff)))
        ^ ( internal_bit_wise_xor(row & ((addrhashbank2>>14)&0x3ffff)))) & (addrhashbank2 & 1);
    gAddrData->addrhash[3] = ((internal_bit_wise_xor(col & ((addrhashbank3>>1)&0x1fff)))
        ^ ( internal_bit_wise_xor(row & ((addrhashbank3>>14)&0x3ffff)))) & (addrhashbank3 & 1);
    gAddrData->addrhash[4] = ((internal_bit_wise_xor(col & ((addrhashbank4>>1)&0x1fff)))
        ^ ( internal_bit_wise_xor(row & ((addrhashbank4>>14)&0x3ffff)))) & (addrhashbank4 & 1);


    *outputbnk = (numbankbits ==3) ? ((((addrhashbank0 & 0x1)==1) ? (gAddrData->addrhash[0] ^ (bank & 0x1)) : (bank & 0x1)) |
                                     ((((addrhashbank1 & 0x1)==1) ? (gAddrData->addrhash[1] ^ ((bank>>1) & 0x1)) : ((bank>>1) & 0x1))<<1) |
                                     ((((addrhashbank2 & 0x1)==1) ? (gAddrData->addrhash[2] ^ ((bank>>2) & 0x1)) : ((bank>>2) & 0x1))<<2)) :
                 (numbankbits ==4) ?  ((((addrhashbank0 & 0x1)==1) ? (gAddrData->addrhash[0] ^ (bank & 0x1)) : (bank & 0x1)) |
                                      ((((addrhashbank1 & 0x1)==1) ? (gAddrData->addrhash[1] ^ ((bank>>1) & 0x1)) : ((bank>>1) & 0x1))<<1) |
                                      ((((addrhashbank2 & 0x1)==1) ? (gAddrData->addrhash[2] ^ ((bank>>2) & 0x1)) : ((bank>>2) & 0x1))<<2) |
                                      ((((addrhashbank3 & 0x1)==1) ? (gAddrData->addrhash[3] ^ ((bank>>3) & 0x1)) : ((bank>>3) & 0x1))<<3)) :
                                      ((((addrhashbank0 & 0x1)==1) ? (gAddrData->addrhash[0] ^ (bank & 0x1)) : (bank & 0x1)) |
                                      ((((addrhashbank1 & 0x1)==1) ? (gAddrData->addrhash[1] ^ ((bank>>1) & 0x1)) : ((bank>>1) & 0x1))<<1) |
                                      ((((addrhashbank2 & 0x1)==1) ? (gAddrData->addrhash[2] ^ ((bank>>2) & 0x1)) : ((bank>>2) & 0x1))<<2) |
                                      ((((addrhashbank3 & 0x1)==1) ? (gAddrData->addrhash[3] ^ ((bank>>3) & 0x1)) : ((bank>>3) & 0x1))<<3) |
                                      ((((addrhashbank4 & 0x1)==1) ? (gAddrData->addrhash[4] ^ ((bank>>4) & 0x1)) : ((bank>>4) & 0x1))<<4));

  }
}
VOID uninvertmsbs_row (UINT32 row, UINT8 numrowlobits, UINT8 invertmsbse,
    UINT8 invertmsbso, UINT8 SEC, UINT32* output_row, UINT8 cs )
{
     *output_row = ((((row>>(numrowlobits-1)) & 0x1 ) ^ (cs%2 ? (((invertmsbso>>1) & 0x1)) : (((invertmsbse>>1) & 0x1))))<<(numrowlobits-1)) |
                      ((((row>>(numrowlobits-2)) & 0x1 ) ^ (cs%2 ? (((invertmsbso>>0) & 0x1)) : (((invertmsbse>>0) & 0x1))))<<(numrowlobits-2)) |
                      (row & (((UINT64)pow_ras(2,numrowlobits-2))-1));
}

VOID unhash_rm (UINT8 rankmul, UINT64 addrhashrm0, UINT64 addrhashrm1, UINT64 addrhashrm2, UINT8* outputrm, UINT8 noofrm, UINT8 numbankbits,
  UINT8 bank0, UINT8 bank1, UINT8 bank2, UINT8 bank3, UINT8 bank4, UINT8 row_lo0, UINT8 numrowlobits, UINT8 numcsbits, UINT8 rm0, UINT8 rm1,
  UINT8 rm2, UINT8 numcolbits, UINT32 COL0REG, UINT32 COL1REG, UINT32 row, UINT8 invertmsbse, UINT8 invertmsbso, UINT8 cs_num, UINT8 SEC, UINT16 col,
  UINT32 addrhashbank0, UINT32 addrhashbank1, UINT32 addrhashbank2, UINT32 addrhashbank3, UINT32 addrhashbank4, UINT8 bank  )
{
  UINT32 temp_row, fk_row;
  UINT8 temp_bank;
  UINT8 fk_rm, fk_bank;
  UINT16 fk_col;
  *outputrm=0;

  if(((addrhashrm0 & 0x1)==0) && ((addrhashrm1 & 0x1)==0) && ((addrhashrm2 & 0x1)==0))
  {
    *outputrm = rankmul;
  }
  else
  {
   uninvertmsbs_row(row, numrowlobits, invertmsbse, invertmsbso, SEC, &temp_row, cs_num  );
   unhash_bank (bank, addrhashbank0, addrhashbank1, addrhashbank2, addrhashbank3, addrhashbank4, &temp_bank, col, temp_row, numbankbits );
   if((addrhashrm0 & 1) && (noofrm >=1)){
     get_fake_bank_row_col_rm (addrhashrm0, numbankbits, bank0, bank1, bank2, bank3, bank4, row_lo0, numrowlobits, numcsbits, rm0, rm1, rm2,
                               numcolbits, COL0REG, COL1REG, &fk_bank, &fk_col, &fk_row, &fk_rm );
     *outputrm = (rankmul &0x1) ^ (internal_bit_wise_xor(fk_bank & temp_bank)) ^ (internal_bit_wise_xor(fk_row & temp_row)) ^ (internal_bit_wise_xor(fk_col & col)) ^ \
                   (internal_bit_wise_xor(fk_rm & rankmul));
   }
   else
   {
     *outputrm = rankmul & 1;
   }


   if((addrhashrm1 & 1) && (noofrm >=2) ){
     get_fake_bank_row_col_rm (addrhashrm1, numbankbits, bank0, bank1, bank2, bank3, bank4, row_lo0, numrowlobits, numcsbits, rm0, rm1, rm2,
                               numcolbits, COL0REG, COL1REG, &fk_bank, &fk_col, &fk_row, &fk_rm );
     *outputrm = (((rankmul>>1)&0x1) ^ (internal_bit_wise_xor(fk_bank & temp_bank)) ^ (internal_bit_wise_xor(fk_row & temp_row)) ^ (internal_bit_wise_xor(fk_col & col)) ^ \
                   (internal_bit_wise_xor(fk_rm & rankmul)))<<1 | *outputrm;
   }
   else
   {
     *outputrm = (((rankmul>>1)&0x1)<<1) | *outputrm;
   }

   if((addrhashrm2 & 1) && (noofrm >=3)){
     get_fake_bank_row_col_rm (addrhashrm2, numbankbits, bank0, bank1, bank2, bank3, bank4, row_lo0, numrowlobits, numcsbits, rm0, rm1, rm2,
                               numcolbits, COL0REG, COL1REG, &fk_bank, &fk_col, &fk_row, &fk_rm );
     *outputrm = (((rankmul>>2)&0x1) ^ (internal_bit_wise_xor(fk_bank & temp_bank)) ^ (internal_bit_wise_xor(fk_row & temp_row)) ^ (internal_bit_wise_xor(fk_col & col)) ^ \
                   (internal_bit_wise_xor(fk_rm & rankmul)))<<2 | *outputrm;
   }
   else
   {
     *outputrm = (((rankmul>>2)&0x1)<<2) | *outputrm;
   }
  }
}

//this function finds the cs position in a 40 bit NormAddr..depends on which rank we are looking at
VOID bit_pos_40b (UINT64 cs_pos, UINT8 *fpos, UINT8 *spos)
{
  UINT8 lp=0;
  UINT64 cs_pos_tmp=0;

  *fpos= 0; *spos=0;
  cs_pos_tmp = cs_pos;

  for(lp=0;lp<40;lp++)
  {
    if((cs_pos_tmp>>lp) & 0x1)
    {
       if(*fpos==0) *fpos = lp; else *spos = lp;
    }
  }
}

UINT32 get_row_bits (UINT8 pkg_no, UINT8 mpu_no, UINT8 umc_inst_num, UINT8 umc_chan_num, UINT8 cs_num)
{

  return (((((gAddrData->CONFIGDIMM[convert_to_addr_trans_index(pkg_no, mpu_no, umc_inst_num, umc_chan_num)][cs_num])>>8) & 0xf) + 10) +
         ((((gAddrData->CONFIGDIMM[convert_to_addr_trans_index(pkg_no, mpu_no, umc_inst_num, umc_chan_num)][cs_num])>>12) & 0xf) + 0));

}

/*----------------------------------------------------------------------------------------*/
/**
 * Convert normalized address to chip select, row, column, bank, rankmul
 *
 * ChannelAddr expected to be passed from the caller should be till lsb=0 and only msb=39 is considered
 * no fancy 39:4 version
 * pkg_no: socket number
 * mpu_no: die number
 *
 *----------------------------------------------------------------------------------------*/
VOID
translate_norm_to_dram_addr (
  UINT64  ChannelAddr,
  UINT8   pkg_no,
  UINT8   mpu_no,
  UINT8   umc_inst_num,
  UINT8   umc_chan_num,
  UINT8   *cs_num,
  UINT8   *bank,
  UINT32  *row,
  UINT16  *col,
  UINT8   *rankmul,
  UINT8   *subchan
  )
{
  //umc_inst_num is like nodeid.

  //Need to check the validity of the NA vs dct number vs node number
  UINT32 CSBase=0, CSMask=0,CSBasesec=0, CSMasksec=0;
  UINT64 CSBaseExt=0, CSMaskExt=0, CSBaseExtsec=0, CSMaskExtsec=0;
  //UINT64 NormAddr=0;
  UINT8 Bank=0,Rankmul=0, Subchan=0;
  UINT32 Row=0;
  UINT16 Col=0;
  UINT8 SEC = 0;  //this will be set to 1 if we are in the secondary rank of a non-power of 2 sized rank, or secondary of 3cs case

  UINT64 temp=0;
  UINT8 cs=0 ;

  BOOLEAN CSEn=0,CSEnsec=0;
  UINT8 Chipselect=0;
  UINT8 noofbank=0, noofrm=0, noofrowlo=0, noofcol=0,  bank0=0, bank1=0, bank2=0,bank3=0,bank4=0;
  UINT8 numrowlobits=0, numcolbits=0, numcsbits=0,  numbankbits=0;
  UINT8 row_lo0=0, rm0=0, rm1=0,rm2=0, chan=0,  invertmsbse=0, invertmsbso=0;
  UINT32 COL0REG=0, COL1REG=0;
  UINT32 addrhashbank0=0,addrhashbank1=0,addrhashbank2=0,addrhashbank3=0,addrhashbank4=0, addrhashpc=0, addrhashpc2=0;
  UINT64 InputAddr=0;
  UINT8 rankmul_temp=0,vcmen=0;

  LOC_1 = convert_to_addr_trans_index(pkg_no, mpu_no, umc_inst_num, umc_chan_num);

  //get_highest_possible_addr_bit(pkg_no, mpu_no, umc_inst_num, umc_chan_num);

  //read out the addrhash* registers here
  addrhashbank0  = gAddrData->ADDRHASHBANK0 [LOC_1];
  addrhashbank1  = gAddrData->ADDRHASHBANK1 [LOC_1];
  addrhashbank2  = gAddrData->ADDRHASHBANK2 [LOC_1];
  addrhashbank3  = gAddrData->ADDRHASHBANK3 [LOC_1];
  addrhashbank4  = gAddrData->ADDRHASHBANK4 [LOC_1];
  addrhashpc     = gAddrData->ADDRHASHPC    [LOC_1];
  addrhashpc2    = gAddrData->ADDRHASHPC2   [LOC_1];

  for (cs = 0; cs < 8; ++cs)
  {
    temp = gAddrData->CSBASE[LOC_1][cs];

    CSBase = temp & 0xffffffff;

    //Secondary decoder stuff for each primary
    temp = gAddrData->CSBASESEC[LOC_1][cs];
    CSBasesec = temp & 0xffffffff;

    temp = gAddrData->CSMASK[LOC_1][(cs)];

    CSMask = temp & 0xffffffff;

    //Secondary decoder stuff
    temp = gAddrData->CSMASKSEC[LOC_1][(cs)];
    CSMasksec = temp &  0xffffffff;

    temp = gAddrData->EXT_CSBASE[LOC_1][(cs)] & 0xff;
    CSBaseExt = temp;

    temp = gAddrData->EXT_CSBASESEC[LOC_1][(cs)] & 0xff;
    CSBaseExtsec = temp;

    temp = gAddrData->EXT_CSMASK[LOC_1][(cs)] & 0xff;
    CSMaskExt = temp;

    temp = gAddrData->EXT_CSMASKSEC[LOC_1][(cs)] & 0xff;
    CSMaskExtsec = temp;

    InputAddr = (ChannelAddr>>8) & 0x3ffffffff;

    //Read out *RAMCFG*
    temp = gAddrData->CONFIGDIMM[LOC_1][(cs)];
    CSEn = temp & 0x1;
    CSEnsec = (temp >> 24) & 0x1;

    if((CSEn && (InputAddr & ((((UINT64)(~CSMaskExt))<<32)| (~CSMask))) == ( ((((UINT64)(CSBaseExt))<<32)| (CSBase)) & ((((UINT64)(~CSMaskExt))<<32)| (~CSMask)))) || (CSEnsec &&
        (InputAddr & ((((UINT64)(~CSMaskExtsec))<<32)| (~CSMasksec))) == (((((UINT64)(CSBaseExtsec))<<32)| (CSBasesec)) & ((((UINT64)(~CSMaskExtsec))<<32)| (~CSMasksec))))) {

      ////hashing
      //Dealing with cshash..
      Chipselect = 0 ;
      if(gAddrData->ADDRHASHNORMADDR[LOC_1][0] & 0x1)  {
        Chipselect = ((internal_bit_wise_xor((((((UINT64)(gAddrData->EXT_ADDRHASHNORMADDR[LOC_1][0]))<<32) |gAddrData->ADDRHASHNORMADDR[LOC_1][0])>>1) & (ChannelAddr>>9))) ^ (cs & 0x1));
      } else {
        Chipselect  = (cs & 0x1) ;
      }

      if(gAddrData->ADDRHASHNORMADDR[LOC_1][1] & 0x1) {
        Chipselect =  Chipselect | (((internal_bit_wise_xor((((((UINT64)(gAddrData->EXT_ADDRHASHNORMADDR[LOC_1][1]))<<32)|
          gAddrData->ADDRHASHNORMADDR[LOC_1][1])>>1) & (ChannelAddr>>9))) ^ ((cs>>1) & 0x1))<<1);
      } else {
        Chipselect = Chipselect |(((cs>>1)& 0x1)<<1);
      }

      if(gAddrData->ADDRHASHNORMADDR[LOC_1][2] & 0x1) {
        Chipselect =  Chipselect | (((internal_bit_wise_xor((((((UINT64)(gAddrData->EXT_ADDRHASHNORMADDR[LOC_1][2]))<<32)|
          gAddrData->ADDRHASHNORMADDR[LOC_1][2])>>1) & (ChannelAddr>>9))) ^ ((cs>>2) & 0x1))<<2);
      } else {
        Chipselect = Chipselect |(((cs>>2)& 0x1)<<2);
      }

      //hashing end

      SEC = (CSEn && (InputAddr & ((((UINT64)(~CSMaskExt))<<32)| (~CSMask))) == (((((UINT64)(CSBaseExt))<<32)| (CSBase)) & ((((UINT64)(~CSMaskExt))<<32)| (~CSMask)))) ? 0 : (CSEnsec &&
        ((InputAddr & ((((UINT64)(~CSMaskExtsec))<<32) | (~CSMasksec))) == (((((UINT64)(CSBaseExtsec))<<32)| (CSBasesec)) & ((((UINT64)(~CSMaskExtsec))<<32)| (~CSMasksec)))) && CSEn) ? 1 : 3;
      InputAddr =0 ;

      noofbank  = (temp >> 4) & 0x3;
      noofrm    = (temp >> 6) & 0x3;
      noofrowlo = (temp >> 8) & 0xF;
      noofcol   = (temp >> 16) & 0xF;


      //Read out *BANK_SEL*

      temp = gAddrData->BANKSELDIMM[LOC_1][(cs)];
      bank0 = temp & 0xf;
      bank1 = (temp >> 4) & 0xf;
      bank2 = (temp >> 8) & 0xf;
      bank3 = (temp >> 12) & 0xf;
      bank4 = (temp >> 16) & 0xf;

      break;
    }//csen inputaddr loop
  }//cs loop

  if(((gAddrData->RANK_ENABLE_PER_UMCCH_ADDR_TRANS[LOC_1]>>((cs==2) && (((gAddrData->CONFIGDIMM[LOC_1][2]>>1) & 0x3)!=0) ? cs ^ ((gAddrData->CONFIGDIMM[LOC_1][2]>>1) & 0x3):cs)) & 0x1) == 0)
  {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "ERROR: Rank is out of bounds. What NormAddr did you pass into the library?? \n");
  }

  //Read out *ROW_SEL*
  temp = gAddrData->ROWSELDIMM[LOC_1][(cs)];
  row_lo0 = (temp >> 0) & 0xF ;

  //Read out *COL0_SEL*
  temp = gAddrData->COL0SELDIMM[LOC_1][(cs)];
  COL0REG = (UINT32)temp;

  //Read out *COL1_SEL*
  temp = gAddrData->COL1SELDIMM[LOC_1][(cs)];
  COL1REG = (UINT32)temp;

  //Read out *RM_SEL*
  temp = gAddrData->RMSELDIMM[LOC_1][(cs)];
  rm0 = (temp >> 0) & 0xF;
  rm1 = (temp >> 4) & 0xF;
  rm2 = (temp >> 8) & 0xF;
  chan = (temp >>16) & 0xF;
  invertmsbse = (temp >> 28) & 0x3;
  invertmsbso = (temp >> 30) & 0x3;

  numrowlobits = noofrowlo + 10;
  numcolbits = noofcol + 5;
  numcsbits = noofrm;
  numbankbits = noofbank == 2? 5: noofbank == 1? 4 : 3;

  //Let us see if we need to swizzle or not
  temp = gAddrData->CTRLREG[LOC_1][(cs>>1)];
  vcmen = (temp >>1) & 0x1;

  NormalizedToBankAddrMap(ChannelAddr, &Bank, &Row, &Col, &Rankmul, &Subchan, numbankbits, bank4, bank3, bank2, bank1, bank0,
         numrowlobits, numcolbits, row_lo0, COL0REG, COL1REG, numcsbits, rm0, rm1, rm2, chan, invertmsbse, invertmsbso,
         SEC, cs, addrhashbank0,addrhashbank1,addrhashbank2,addrhashbank3,addrhashbank4,addrhashpc,addrhashpc2, vcmen);

  rankmul_temp=0;

  if((gAddrData->ADDRHASHRMADDR[LOC_1][0] & 1) && (noofrm>=1))
  {
    rankmul_temp = ((internal_bit_wise_xor((((((UINT64)(gAddrData->EXT_ADDRHASHRMADDR[LOC_1][0]))<<32)|gAddrData->ADDRHASHRMADDR[LOC_1][0])>>1) & (ChannelAddr>>9))) ^ (Rankmul & 0x1)) ;
  } else {
    rankmul_temp = Rankmul & 0x1;
  }

  if((gAddrData->ADDRHASHRMADDR[LOC_1][1] & 0x1) && (noofrm>=2)) {
    rankmul_temp =  rankmul_temp | (((internal_bit_wise_xor((((((UINT64)(gAddrData->EXT_ADDRHASHRMADDR[LOC_1][1]))<<32)|gAddrData->ADDRHASHRMADDR[LOC_1][1])>>1) &
        (ChannelAddr>>9))) ^ ((Rankmul>>1) & 0x1))<<1);
  } else {
    rankmul_temp = rankmul_temp |(((Rankmul>>1)& 0x1)<<1);
  }

  if((gAddrData->ADDRHASHRMADDR[LOC_1][2] & 0x1)  && (noofrm>=3)) {
    rankmul_temp =  rankmul_temp | (((internal_bit_wise_xor((((((UINT64)(gAddrData->EXT_ADDRHASHRMADDR[LOC_1][2]))<<32)|gAddrData->ADDRHASHRMADDR[LOC_1][2])>>1) &
        (ChannelAddr>>9))) ^ ((Rankmul>>2) & 0x1))<<2);
  } else {
    rankmul_temp = rankmul_temp |(((Rankmul>>2)& 0x1)<<2);
  }

  //self checking
  //NormAddr = BankAddrToNormalizedMap(CSBase, CSBaseExt, CSMask, CSMaskExt, cs, Bank, Row, Col, Rankmul, Subchan, numbankbits, bank4, bank3, bank2, bank1, bank0,
  //       numrowlobits,  numcolbits, row_lo0, COL0REG, COL1REG, numcsbits, rm0, rm1, rm2, chan, invertmsbse, invertmsbso,
  //       CSMasksec, CSMaskExtsec, CSBasesec, CSBaseExtsec, SEC, addrhashbank0,addrhashbank1,addrhashbank2,addrhashbank3,addrhashbank4,addrhashpc,addrhashpc2);

  //if ((NormAddr & ((UINT64)pow_ras(2,gAddrData->VALIDHI[LOC_1])-1))>>3 != (ChannelAddr & ((UINT64)pow_ras(2,gAddrData->VALIDHI[LOC_1])-1))>>3) {
  //  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ERROR: Given NormAddr_39_3=%jx, Backward NormAddr_39_3=%jx\n\n",
  //    (ChannelAddr & ((UINT64)pow_ras(2,gAddrData->VALIDHI[LOC_1])-1) )>>3 ,(NormAddr & ((UINT64)pow_ras(2,gAddrData->VALIDHI[LOC_1])-1))>>3);
  //}

  *cs_num = Chipselect;
  //we need to change it to 0 if its 2 and we are in csxor case
  {
    if((Chipselect==2) && (((gAddrData->CONFIGDIMM[LOC_1][2]>>1) & 0x3)!=0)){
      *cs_num = *cs_num ^ ((gAddrData->CONFIGDIMM[LOC_1][2]>>1) & 0x3);
    }
  }

  *bank = Bank;
  *row = Row;
  *col = Col;
  *rankmul = rankmul_temp;
  *subchan = Subchan;

  //Need to check if the outputs respect the config
  if(Bank >= (noofbank==2? 32 : (noofbank==1? 16 : 8))) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "translate_norm_to_dram_addr: ERROR: Bank of the Given NormAddr is out of bounds\n");
  }

  if(Row >= (UINT32)(pow_ras(2,(noofrowlo+10)))) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "translate_norm_to_dram_addr: ERROR: Row of the Given NormAddr is out of bounds\n");
  }

  if(Col >= (UINT16)(pow_ras(2,(noofcol+5)))) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "translate_norm_to_dram_addr: ERROR: Col of the Given NormAddr is out of bounds\n");
  }
}

UINT64 translate_dram_to_norm_addr (
  UINT8 pkg_no,
  UINT8 mpu_no,
  UINT8 umc_inst_num,
  UINT8 umc_chan_num,
  UINT8 cs_num,
  UINT8 bank,
  UINT32 row,
  UINT16 col,
  UINT8 rankmul,
  UINT8 subchan)
{

  UINT64 ChannelAddr_G=0;
  UINT64 InputAddr=0;

  //if(!OOR)
  {
      //Let us check the validity of nnode

      UINT8 TransCS=0;/*TransDCT=0,DctSelIntLvAddr=0,BankSwapAddr8En=0,DctSelBankSwap=0;*/

      UINT32 temp=0;
      UINT32 CSBase=0, CSMask=0,CSBasesec=0, CSMasksec=0,CSBase1sec=0,CSMask1sec=0;
      UINT64 CSBaseExt=0, CSMaskExt=0, CSBaseExtsec=0, CSMaskExtsec=0;
      UINT16 TransCol=0,fk_col=0;
      UINT32 TransRow=0,fk_row=0, temp_row=0;
      UINT8 TransBank=0, fk_bank=0, temp_bank=0;
      UINT8 TransRankmul=0,fk_rm=0, temp_rm;
      UINT8 TransSubchan;
      UINT8 SEC =0, Chipselect=0, Chipselect1=0;

      UINT8 noofbank=0, noofrm=0, noofrowlo=0, noofcol=0,  bank0=0, bank1=0, bank2=0,bank3=0,bank4=0;
      UINT8 numrowlobits=0, numcolbits=0, numcsbits=0, numbankbits=0;
      UINT8 row_lo0=0, rm0=0, rm1=0,rm2=0, chan=0, invertmsbse=0, invertmsbso=0;
      UINT32 addrhashbank0=0,addrhashbank1=0,addrhashbank2=0,addrhashbank3=0,addrhashbank4=0, addrhashpc=0, addrhashpc2=0;
      UINT64 addrhashnormaddr0=0, addrhashnormaddr1=0, addrhashnormaddr2=0, addrhashrmaddr0, addrhashrmaddr1, addrhashrmaddr2 ;
      UINT8 rankmul_temp,rankmul_temp1, vcmen;
      UINT8 cs1=0;
      BOOLEAN CSEn1=0, CSEn=0,CSEn1sec=0,CSEnsec=0;

      UINT32 COL0REG=0, COL1REG=0;

      UINT64 ADDR=0;
      UINT64 cs_pos=0;
      UINT8  fpos=0, spos=0;
      //UINT32 inputaddr_lhs=0, cs_rhs=0 ,inputaddr_lhs_sec=0, cs_rhssec=0;
      UINT32 CSMask1=0, CSBase1=0;
      UINT32 CSMask1Ext=0, CSBase1Ext=0, CSMask1Extsec=0, CSBase1Extsec=0;
      UINT64 CSMask1_64b=0,CSBase1_64b=0,CSMask1sec_64b=0,CSBase1sec_64b=0;

      DRAMTYPE = get_dramtype();

      LOC_2 = convert_to_addr_trans_index(pkg_no, mpu_no, umc_inst_num, umc_chan_num);

      //printf ("translate_dram_to_norm_addr %d %d %d %d rank%d %d 0x%x col%x %x %x\n",pkg_no,mpu_no,umc_inst_num,umc_chan_num, cs_num, bank, row, col, rankmul, subchan);
      //now cs hash has two requirements: no xoring in cs postion and the dimms have to ve identical on a channel.
      //so the incoming cs_num needs to unhashed. so that we can put the right csbasemask in.
      //to unhash, we need row bank col as wedont have normaddr yet to do the calculation.

      //read out the addrhash* registers here
      addrhashbank0 = gAddrData->ADDRHASHBANK0  [LOC_2];
      addrhashbank1 = gAddrData->ADDRHASHBANK1  [LOC_2];
      addrhashbank2 = gAddrData->ADDRHASHBANK2  [LOC_2];
      addrhashbank3 = gAddrData->ADDRHASHBANK3  [LOC_2];
      addrhashbank4 = gAddrData->ADDRHASHBANK4  [LOC_2];
      addrhashpc     = gAddrData->ADDRHASHPC    [LOC_2];
      addrhashpc2    = gAddrData->ADDRHASHPC2   [LOC_2];
      addrhashnormaddr0 = ((UINT64)(gAddrData->EXT_ADDRHASHNORMADDR[LOC_2][0])<<32) | gAddrData->ADDRHASHNORMADDR[LOC_2][0];
      addrhashnormaddr1 = ((UINT64)(gAddrData->EXT_ADDRHASHNORMADDR[LOC_2][1])<<32) | gAddrData->ADDRHASHNORMADDR[LOC_2][1];
      addrhashnormaddr2 = ((UINT64)(gAddrData->EXT_ADDRHASHNORMADDR[LOC_2][2])<<32) | gAddrData->ADDRHASHNORMADDR[LOC_2][2];
      addrhashrmaddr0= (((UINT64)(gAddrData->EXT_ADDRHASHRMADDR[LOC_2][0]))<<32)|gAddrData->ADDRHASHRMADDR[LOC_2][0];
      //addrhashrmaddr0= (((UINT64)(gAddrData->EXT_ADDRHASHRMADDR[LOC_2][0]))<<32)|gAddrData->ADDRHASHRMADDR[LOC_2][0];
      addrhashrmaddr1= (((UINT64)(gAddrData->EXT_ADDRHASHRMADDR[LOC_2][1]))<<32)|gAddrData->ADDRHASHRMADDR[LOC_2][1];
      addrhashrmaddr2= (((UINT64)(gAddrData->EXT_ADDRHASHRMADDR[LOC_2][2]))<<32)|gAddrData->ADDRHASHRMADDR[LOC_2][2];

      if (umc_chan_num >= TOTAL_NUM_UMCCH_PER_UMC_ADDR_TRANS)
      {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ERROR: Invalid UMCCH was passed. it has to be between 0x0 and %d\n", TOTAL_NUM_UMCCH_PER_UMC_ADDR_TRANS-1);
      }

      if ((((gAddrData->RANK_ENABLE_PER_UMCCH_ADDR_TRANS[LOC_2])>>cs_num)& 0x1)==0)
      {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ERROR: Invalid CS %d. Are you passing some rank greater than the number of ranks supported per channel\n",cs_num);
      }

      //now if csxor case is in play here, then we need to look at the #rows and csxor
      if((cs_num==0) && (row>(((UINT32)(pow_ras(2,(((gAddrData->CONFIGDIMM[LOC_2][0]>>8) & 0xf)+10)))-1)/2)) && (((gAddrData->CONFIGDIMM[LOC_2][2]>>1) & 0x3)!=0)){
        cs_num=cs_num ^ ((gAddrData->CONFIGDIMM[LOC_2][2]>>1) & 0x3);
      }
      temp = gAddrData->CONFIGDIMM[LOC_2][(cs_num)];

      CSEn = temp & 0x1;
      CSEnsec = (temp >>24) & 0x1;
      if(CSEn==0 && CSEnsec==0)
      {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ERROR: The rank you passed is not enabled!! Look at the inputs into routine\n");
      }
      else if ((CSEn==0) && (CSEnsec!=0) && (cs_num%2==0))
      {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Error: UMC_ADDR_TRANS The rank you passed is even and why does it have secondary enabled with primary off?? \
            this is allowed for only odd ranks in asymm case of D4 for umc=%d, ch=%d\n",umc_inst_num, umc_chan_num);
      }

      noofbank = (temp >>4) & 0x3;
      noofrm = (temp>>6) & 0x3 ;
      noofrowlo = (temp >> 8) & 0xF;
      noofcol = (temp >> 16) & 0xF;

      //Asserts for Invalid bank
      if ((bank >= 0x8) && noofbank == 0)
      {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Invalid bank. LOC_2:%0d,temp: %jx, cs_num:%0d. Bank has to be between 0x0 and 0x7.\
            Your bank=%0d, noofbank:%0d,\n",LOC_2, temp,cs_num,bank,noofbank);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ERROR: Invalid bank. It has to be 0x0-0x7\n");
      }else if (bank>=0x10 && noofbank == 1)
      {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Invalid bank. LOC_2:%0d,temp: %jx, cs_num:%0d. Bank has to be between 0x0 and 0xf.\
            Your bank=%0d, noofbank:%0d,\n",LOC_2, temp,cs_num,bank,noofbank);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ERROR: Invalid bank. It has to be 0x0-0xf\n");
      }else if (bank>=0x20 && noofbank == 2)
      {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Invalid bank. LOC_2:%0d,temp: %jx, cs_num:%0d. Bank has to be between 0x0 and 0x1f.\
            Your bank=%0d, noofbank:%0d,\n",LOC_2, temp,cs_num,bank,noofbank);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ERROR: Invalid bank. It has to be 0x0-0x1f\n");
      }

      //Assert for Invalid row
      if ((row >= (UINT32)(pow_ras(2,(noofrowlo+10)))))
      {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Invalid row. LOC_2:%0d,temp: 0x%lx, cs_num:%0d. Row has to be between 0x0 and 0x%x.\
            Your row=0x%x, noofrowlo:%0d\n", LOC_2, temp, cs_num, (UINT32)(pow_ras(2,(noofrowlo+10))-1), row, noofrowlo);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ERROR: Invalid row. It has to be 0x0-0x1f\n");
      }

      //Assert for Invalid col
      if (col >= (UINT16)(pow_ras(2,(noofcol+5))))
      {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Invalid col.LOC_2:%0d,temp: 0x%lx, cs_num:%0d.  Col has to be between 0x0 and 0x%x.\
            Your col=0x%x, noofcol:%0d\n",LOC_2, temp, cs_num, (UINT32)(pow_ras(2,(noofcol+5))-1), col, noofcol);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ERROR: Invalid col. It has to be 0x0-0x1f\n");
      }


      //Lets read swizzle
      temp = gAddrData->CTRLREG[LOC_2][(cs_num>>1)];
      vcmen = (temp >>1) & 0x1;


      //if( (DRAMTYPE==1) )
      //{
      //  //Normal case
      //  switch (noofrm)
      //  {
      //    case 0 : if ((rankmul>>0) & 0x1) {if(ASSERT_UMC_ADDR_TRANS){ printf
      //             ("File of coredump is %s and line of coredump is %d\n",__FILE__,__LINE__); exit(1);}
      //             else printf ("ERROR: rankmul should have been 0\n Check the inputs to the routine\n\n");} break;
      //    case 1 : if (((rankmul>>1) & 0x7f) != 0) {if(ASSERT_UMC_ADDR_TRANS){ printf
      //             ("File of coredump is %s and line of coredump is %d\n",__FILE__,__LINE__); exit(1);}
      //             else printf ("ERROR: Wrong rankmul is passed for noofrm=1\n");} break;//rankmul[7:1] should be 0
      //    case 2 : if (((rankmul>>2) & 0x3f) != 0) {if(ASSERT_UMC_ADDR_TRANS){ printf
      //             ("File of coredump is %s and line of coredump is %d\n",__FILE__,__LINE__); exit(1);}
      //             else printf ("ERROR: Wrong rankmul is passed for noofrm=2\n");} break;//rankmul[7:2] should be 0
      //    case 3 : if (((rankmul>>3) & 0x1f) != 0) {if(ASSERT_UMC_ADDR_TRANS){ printf
      //             ("File of coredump is %s and line of coredump is %d\n",__FILE__,__LINE__); exit(1);}
      //             else printf ("ERROR: Wrong rankmul is passed for noofrm=3\n");} break;//rankmul[7:3] should be 0
      //  }
      //}

      // Obtain the CS Base from D18F2x[4C:40]
      temp = gAddrData->CSBASE[LOC_2][cs_num];
      CSBase = temp & 0xffffffff;
      temp = gAddrData->CSBASESEC[LOC_2][cs_num];
      CSBasesec = temp & 0xffffffff;

      // Extract variables from D18F2x[64:60]
      temp = gAddrData->CSMASK[LOC_2][(cs_num)];
      CSMask = temp & 0xffffffff;
      temp = gAddrData->CSMASKSEC[LOC_2][(cs_num)];
      CSMasksec = temp & 0xffffffff;

      temp = gAddrData->EXT_CSBASE[LOC_2][(cs_num)] & 0xff;
      CSBaseExt = temp;

      temp = gAddrData->EXT_CSBASESEC[LOC_2][(cs_num)] & 0xff;
      CSBaseExtsec = temp;

      temp = gAddrData->EXT_CSMASK[LOC_2][(cs_num)] & 0xff;
      CSMaskExt = temp;

      temp = gAddrData->EXT_CSMASKSEC[LOC_2][(cs_num)] & 0xff;
      CSMaskExtsec = temp;

       if (((row > 0xbfff) && (noofrowlo+10 == 16) && (CSMasksec !=0) && (CSEnsec !=0) && (CSEn !=0)  ) ||
           ((row > 0x17fff) && (noofrowlo+10 == 17) && (CSMasksec !=0) && (CSEnsec !=0) && (CSEn !=0) ))
      {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ERROR: In an odd cs scenario..we ignore the last quarter of the rows in a setup...\
            your row falls in the ignored region...illegal row\n");
      }


      //Read out *BANK_SEL*

      temp = gAddrData->BANKSELDIMM[LOC_2][(cs_num)];
      bank0 = temp & 0xf;
      bank1 = (temp >>4)& 0xf;
      bank2 = (temp >>8)& 0xf;
      bank3 = (temp >>12)& 0xf;
      bank4 = (temp >>16)& 0xf;

      //REad out *ROW_SEL*
      temp = gAddrData->ROWSELDIMM[LOC_2][(cs_num)];
      row_lo0 = (temp >>0) & 0xF ;

      //Read out *COL0_SEL*
      temp = gAddrData->COL0SELDIMM[LOC_2][(cs_num)];
      COL0REG = temp;

      //Read out *COL1_SEL*

      temp = gAddrData->COL1SELDIMM[LOC_2][(cs_num)];
      COL1REG = temp;

      //Read out *RM_SEL*

      temp = gAddrData->RMSELDIMM[LOC_2][(cs_num)];
      rm0 = (temp >>0 ) & 0xF;
      rm1 = (temp >>4 ) & 0xF;
      rm2 = (temp >>8 ) & 0xF;
      chan = (temp >>16) & 0xF;
      invertmsbse = (temp>>28) & 0x3;
      invertmsbso = (temp>>30) & 0x3;


      numrowlobits = noofrowlo + 10;
      numcolbits = noofcol + 5;
      numcsbits = noofrm;
      numbankbits = noofbank==2 ? 5 : noofbank==1 ? 4 :3;

      //When NumRM=0, we ar dealing with 6GB/12GB/24GB..both 6 adn 12 have 16 row bits..and 24 has 17 row bits...
      //so half of 2^numrow/2 is the start of row in the sec..cos half is on primary and quarter is on sec...last quarter is left out.
      //6GB, rm=0 and 12GB, rm=0 and 24GB, rm=0

      //SEC is 1 for sec rank of non power of 2 when we have no3cs or 3cs intlv case (in 1+2 case, odd row on r0/3 and even row on r2...2+1 case, odd row on 0/2 and even row on 1)
      SEC = (((row >= ((UINT32)(pow_ras(2,((get_row_bits(pkg_no, mpu_no, umc_inst_num, umc_chan_num, cs_num))-1))))) && (((((UINT64)(CSBaseExtsec))<<32)|(CSBasesec)) !=0) && \
              (CSEnsec !=0) && (CSEn!=0) && !(gAddrData->CTRLREG[LOC_2][cs_num>>1] & 0x1)) \
              || ((gAddrData->CTRLREG[LOC_2][(cs_num>>1)] & 0x1) && (row & 0x1) && ((cs_num==0) || (cs_num==(((gAddrData->CTRLREG[LOC_2][(cs_num>>1)]>>2) & 0x1)? 2: 3)))) \
              || ((gAddrData->CTRLREG[LOC_2][(cs_num>>1)] & 0x1) && (!(row & 0x1)) && ((cs_num==(((gAddrData->CTRLREG[LOC_2][(cs_num>>1)]>>2) & 0x1)? 1: 2)))) \
              ) ? 1 : 0;

      Chipselect =cs_num;
      uninvertmsbs_row(row, numrowlobits, invertmsbse, invertmsbso, SEC, &temp_row, cs_num  );
      unhash_bank (bank, addrhashbank0, addrhashbank1, addrhashbank2, addrhashbank3, addrhashbank4,
          &temp_bank, col, temp_row, numbankbits );
      unhash_rm(rankmul, addrhashrmaddr0, addrhashrmaddr1, addrhashrmaddr2, &temp_rm, noofrm, numbankbits, bank0, bank1, bank2, bank3, bank4, row_lo0, numrowlobits, numcsbits, rm0, rm1, rm2,
                  numcolbits, COL0REG, COL1REG, row, invertmsbse, invertmsbso, cs_num, SEC, col, addrhashbank0, addrhashbank1, addrhashbank2, addrhashbank3, addrhashbank4, bank  );
      if (addrhashnormaddr0 &  1 ){
        get_fake_bank_row_col_rm (addrhashnormaddr0, numbankbits, bank0, bank1, bank2, bank3, bank4, row_lo0, numrowlobits, numcsbits, rm0, rm1, rm2,
                                  numcolbits, COL0REG, COL1REG, &fk_bank, &fk_col, &fk_row, &fk_rm );
        Chipselect = (cs_num & 0x1) ^ (internal_bit_wise_xor(fk_bank & temp_bank))
          ^ (internal_bit_wise_xor(fk_row & temp_row)) ^ (internal_bit_wise_xor(fk_col & col))
          ^  (internal_bit_wise_xor(fk_rm & rankmul));
      }
      else
      {
        Chipselect =  (cs_num & 0x1 );
      }

      if (addrhashnormaddr1 & 1){
        get_fake_bank_row_col_rm (addrhashnormaddr1, numbankbits, bank0, bank1, bank2, bank3, bank4, row_lo0, numrowlobits, numcsbits, rm0, rm1, rm2,
                                  numcolbits, COL0REG, COL1REG, &fk_bank, &fk_col, &fk_row, &fk_rm );
        Chipselect = (((cs_num >>1 ) & 0x1) ^ (internal_bit_wise_xor(fk_bank & temp_bank))
            ^ (internal_bit_wise_xor(fk_row & temp_row)) ^ (internal_bit_wise_xor(fk_col & col))
            ^  (internal_bit_wise_xor(fk_rm & rankmul))) <<1 | Chipselect;
      }
      else
      {
        Chipselect = (((cs_num>>1)&0x1)<<1) | Chipselect;
      }

      if (addrhashnormaddr2 & 1){
        get_fake_bank_row_col_rm (addrhashnormaddr2, numbankbits, bank0, bank1, bank2, bank3, bank4, row_lo0, numrowlobits, numcsbits, rm0, rm1, rm2,
                                  numcolbits, COL0REG, COL1REG, &fk_bank, &fk_col, &fk_row, &fk_rm );
        Chipselect = (((cs_num >>2 ) & 0x1) ^ (internal_bit_wise_xor(fk_bank & temp_bank)) \
            ^ (internal_bit_wise_xor(fk_row & temp_row)) ^ (internal_bit_wise_xor(fk_col & col)) \
            ^  (internal_bit_wise_xor(fk_rm & rankmul))) <<2 | Chipselect;
      }
      else
      {
        Chipselect = (((cs_num>>2)&0x1)<<2) | Chipselect;
      }

      CSBase = gAddrData->CSBASE[LOC_2][Chipselect];
      CSMask = gAddrData->CSMASK[LOC_2][Chipselect];
      CSBasesec = gAddrData->CSBASESEC[LOC_2][Chipselect];
      CSMasksec = gAddrData->CSMASKSEC[LOC_2][Chipselect];
      CSBaseExt = gAddrData->EXT_CSBASE[LOC_2][Chipselect];
      CSMaskExt = gAddrData->EXT_CSMASK[LOC_2][Chipselect];
      CSBaseExtsec = gAddrData->EXT_CSBASESEC[LOC_2][Chipselect];
      CSMaskExtsec = gAddrData->EXT_CSMASKSEC[LOC_2][Chipselect];

      cs_pos = (SEC==0) ? ((((UINT64)(CSBase!=0 ? (((((UINT64)(CSBaseExt))<<32)| (CSBase)) & ((((UINT64)(~CSMaskExt))<<32)| (~CSMask))): (((((UINT64)(~CSMaskExt))<<32)| (~CSMask)))))<<8)) : \
                                                  (((((UINT64)(CSBasesec!=0 ? (((((UINT64)(CSBaseExtsec))<<32)| (CSBasesec)) & ((((UINT64)(~CSMaskExtsec))<<32)| (~CSMasksec))) : \
                                                                              (((((UINT64)(~CSMaskExtsec))<<32)| (~CSMasksec))))))<<8)) ;//40b version where "1" is in cs position
      bit_pos_40b(cs_pos, &fpos, &spos);

      //before we send the components, we need to rmhash the rm parts. and rmhash considers cs
      rankmul_temp = 0;
      uninvertmsbs_row(row, numrowlobits, invertmsbse, invertmsbso, SEC, &temp_row, cs_num  );
      unhash_bank (bank, addrhashbank0, addrhashbank1, addrhashbank2, addrhashbank3, addrhashbank4, &temp_bank, col, temp_row, numbankbits );
      if((addrhashrmaddr0 & 1) && (noofrm >=1)){
        get_fake_bank_row_col_rm (addrhashrmaddr0, numbankbits, bank0, bank1, bank2, bank3, bank4, row_lo0, numrowlobits, numcsbits, rm0, rm1, rm2,
                                  numcolbits, COL0REG, COL1REG, &fk_bank, &fk_col, &fk_row, &fk_rm );

       if(spos==0)
         rankmul_temp = (rankmul &0x1) ^ (internal_bit_wise_xor(fk_bank & temp_bank)) ^ (internal_bit_wise_xor(fk_row & temp_row)) ^ (internal_bit_wise_xor(fk_col & col)) ^ \
                          (internal_bit_wise_xor(fk_rm & rankmul)) ^ (((((addrhashrmaddr0>>1)<<9)>>fpos) & 0x1) & (Chipselect & 0x1)) ;
       else
         rankmul_temp = (rankmul &0x1) ^ (internal_bit_wise_xor(fk_bank & temp_bank)) ^ (internal_bit_wise_xor(fk_row & temp_row)) ^ (internal_bit_wise_xor(fk_col & col)) ^ \
                          (internal_bit_wise_xor(fk_rm & rankmul)) ^ (((((addrhashrmaddr0>>1)<<9)>>fpos) & 0x1) & (Chipselect & 0x1)) ^ \
                          (((((addrhashrmaddr0>>1)<<9)>>spos) & 0x1) & (Chipselect>>1 & 0x1));
      }
      else
      {
        rankmul_temp = rankmul & 1;
      }


      if((addrhashrmaddr1 & 1) && (noofrm >=2) ){
        get_fake_bank_row_col_rm (addrhashrmaddr1, numbankbits, bank0, bank1, bank2, bank3, bank4, row_lo0, numrowlobits, numcsbits, rm0, rm1, rm2,
                                  numcolbits, COL0REG, COL1REG, &fk_bank, &fk_col, &fk_row, &fk_rm );
       if(spos==0)
         rankmul_temp = (((rankmul>>1)&0x1) ^ (internal_bit_wise_xor(fk_bank & temp_bank)) ^ (internal_bit_wise_xor(fk_row & temp_row)) ^ (internal_bit_wise_xor(fk_col & col)) ^ \
                          (internal_bit_wise_xor(fk_rm & rankmul)) ^ (((((addrhashrmaddr1>>1)<<9)>>fpos) & 0x1) & (Chipselect & 0x1))  )<<1 | rankmul_temp;
       else
         rankmul_temp = (((rankmul>>1)&0x1) ^ (internal_bit_wise_xor(fk_bank & temp_bank)) ^ (internal_bit_wise_xor(fk_row & temp_row)) ^ (internal_bit_wise_xor(fk_col & col)) ^ \
                          (internal_bit_wise_xor(fk_rm & rankmul)) ^ (((((addrhashrmaddr1>>1)<<9)>>fpos) & 0x1) & (Chipselect & 0x1)) ^ \
                          (((((addrhashrmaddr1>>1)<<9)>>spos) & 0x1) & (Chipselect>>1 & 0x1))  )<<1 | rankmul_temp;
      }
      else
      {
        rankmul_temp = (((rankmul>>1)&0x1)<<1) | rankmul_temp;
      }

      if((addrhashrmaddr2 & 1) && (noofrm >=3)){
        get_fake_bank_row_col_rm (addrhashrmaddr2, numbankbits, bank0, bank1, bank2, bank3, bank4, row_lo0, numrowlobits, numcsbits, rm0, rm1, rm2,
                                  numcolbits, COL0REG, COL1REG, &fk_bank, &fk_col, &fk_row, &fk_rm );
       if(spos==0)
         rankmul_temp = (((rankmul>>2)&0x1) ^ (internal_bit_wise_xor(fk_bank & temp_bank)) ^ (internal_bit_wise_xor(fk_row & temp_row)) ^ (internal_bit_wise_xor(fk_col & col)) ^ \
                          (internal_bit_wise_xor(fk_rm & rankmul)) ^ (((((addrhashrmaddr2>>1)<<9)>>fpos) & 0x1) & (Chipselect & 0x1)) )<<2 | rankmul_temp;
       else
         rankmul_temp = (((rankmul>>2)&0x1) ^ (internal_bit_wise_xor(fk_bank & temp_bank)) ^ (internal_bit_wise_xor(fk_row & temp_row)) ^ (internal_bit_wise_xor(fk_col & col)) ^ \
                          (internal_bit_wise_xor(fk_rm & rankmul)) ^ (((((addrhashrmaddr2>>1)<<9)>>fpos) & 0x1) & (Chipselect & 0x1)) ^ \
                          (((((addrhashrmaddr2>>1)<<9)>>spos) & 0x1) & (Chipselect>>1 & 0x1)) )<<2 | rankmul_temp;

      }
      else
      {
        rankmul_temp = (((rankmul>>2)&0x1)<<2) | rankmul_temp;
      }



      ChannelAddr_G = BankAddrToNormalizedMap(CSBase, CSBaseExt, CSMask, CSMaskExt, Chipselect, bank, row, col, rankmul_temp, subchan, numbankbits, bank4, bank3, bank2, bank1, bank0,
           numrowlobits, numcolbits, row_lo0, COL0REG, COL1REG, numcsbits, rm0, rm1, rm2, chan, invertmsbse, invertmsbso,
           CSMasksec, CSMaskExtsec, CSBasesec, CSBaseExtsec, SEC, addrhashbank0,addrhashbank1,addrhashbank2,addrhashbank3,addrhashbank4,addrhashpc,addrhashpc2, vcmen);

      //printf ("translate_dram_to_norm_addr %d %d %d %d rank%d %d %d col%x %x %x: addr=0x%jx\n",pkg_no,mpu_no,umc_inst_num,umc_chan_num, cs_num, bank, row, col, rankmul, subchan, ChannelAddr_G);

      //self checking
      NormalizedToBankAddrMap(ChannelAddr_G, &TransBank, &TransRow, &TransCol, &TransRankmul, &TransSubchan, numbankbits, bank4, bank3, bank2, bank1, bank0,
      numrowlobits,  numcolbits, row_lo0, COL0REG, COL1REG, numcsbits, rm0, rm1, rm2, chan, invertmsbse, invertmsbso,
      SEC, cs_num, addrhashbank0,addrhashbank1,addrhashbank2,addrhashbank3,addrhashbank4,addrhashpc,addrhashpc2,vcmen);
      //printf ("translate_dram_to_norm_addr self: %d %d %d %d rank is not displayed %d %d col%x %x %x: \n",
      //  pkg_no,mpu_no,umc_inst_num,umc_chan_num, TransBank, TransRow, TransCol, TransRankmul, TransSubchan);


      if ((row != TransRow) || (col != TransCol)||(bank != TransBank) )
      {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Expected B/R/C/RM/SUBCH = 0x%x/0x%x/0x%x/0x%x/0x%x and \
            actual B/R/C/RM/SUBCH = 0x%x/0x%x/0x%x/0x%x/0x%x for cs=%d  for umc=%d\n", \
            bank, row, col, rankmul, subchan, TransBank, TransRow, TransCol, TransRankmul, TransSubchan, cs_num, umc_inst_num);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ERROR: Expected bank/row/col is differnt from actual bank/row/col\n");
      }else
      {
      //Lets check for node dct and cs_num
      //Changed for soc15
      for (cs1 = 0; cs1 < 8; ++cs1) {
      // Obtain the CS Base from D18F2x[1,0][4C:40]
        temp = gAddrData->CSBASE[LOC_2][cs1];
        CSBase1 = temp & 0xffffffff;
        temp = gAddrData->CSBASESEC[LOC_2][cs1];
        CSBase1sec = temp & 0xffffffff;
        temp = gAddrData->EXT_CSBASE[LOC_2][cs1];
        CSBase1Ext = temp & 0xffffffff;
        temp = gAddrData->EXT_CSBASESEC[LOC_2][cs1];
        CSBase1Extsec = temp & 0xffffffff;

        // Obtain the CS Mask from D18F2x[64:60]
        temp = gAddrData->CSMASK[LOC_2][(cs1)];
        CSMask1 = temp & 0xffffffff;
        temp = gAddrData->CSMASKSEC[LOC_2][(cs1)];
        CSMask1sec = temp & 0xffffffff;
        temp =  gAddrData->EXT_CSMASK[LOC_2][(cs1)];
        CSMask1Ext = temp & 0xffffffff;
        temp = gAddrData->EXT_CSMASKSEC[LOC_2][(cs1)];
        CSMask1Extsec = temp & 0xffffffff;

        // Adjust the Channel Addr for easy comparison
        InputAddr = (ChannelAddr_G >> 8) & 0x3ffffffff;
        //RAMCFG now has CSEn1
        temp = gAddrData->CONFIGDIMM[LOC_2][(cs1)];
        CSEn1 = temp & 0x1;
        CSEn1sec = (temp >>24) & 0x1;

        if((CSEn1 && (InputAddr & ((((UINT64)(~CSMask1Ext))<<32)| (~CSMask1))) == (((((UINT64)(CSBase1Ext))<<32)| (CSBase1)) & ((((UINT64)(~CSMask1Ext))<<32)| (~CSMask1)))) || \
           (CSEn1sec && (InputAddr & ((((UINT64)(~CSMask1Extsec))<<32)| (~CSMask1sec))) == (((((UINT64)(CSBase1Extsec))<<32)| (CSBase1sec)) & ((((UINT64)(~CSMask1Extsec))<<32)| (~CSMask1sec))))) {

           Chipselect1=0;
           ADDR=0;
           SEC = (CSEn1sec && ((InputAddr & ((((UINT64)(~CSMask1Extsec))<<32)| (~CSMask1sec))) == \
                   (((((UINT64)(CSBase1Extsec))<<32)| (CSBase1sec)) & ((((UINT64)(~CSMask1Extsec))<<32)| (~CSMask1sec)))) && CSEn1);
           CSMask1_64b = (((UINT64)(CSMask1))<<8)|0xff;
           CSBase1_64b = (((((UINT64)(CSBase1))>>1)<<1)<<8)|0x00;
           CSMask1sec_64b = (((UINT64)(CSMask1sec))<<8)|0xff;
           CSBase1sec_64b = (((((UINT64)(CSBase1sec))>>1)<<1)<<8)|0x00;
           CSMask1_64b = ((UINT64)(((UINT64)CSMask1Ext <<32)|(((UINT64)(CSMask1))))<<8)|0xff;
           CSBase1_64b = ((UINT64)(((UINT64)CSBase1Ext <<32)|((((UINT64)(CSBase1))>>1)<<1))<<8)|0x00;
           CSMask1sec_64b = ((UINT64)(((UINT64)CSMask1Extsec <<32)|(((UINT64)(CSMask1sec))))<<8)|0xff;
           CSBase1sec_64b = ((UINT64)(((UINT64)CSBase1Extsec <<32)|((((UINT64)(CSBase1sec))>>1)<<1))<<8)|0x00;
           ADDR = (SEC==0) ? (ChannelAddr_G & (~(CSBase1_64b & ~CSMask1_64b))) :
                             (ChannelAddr_G & (~(CSBase1sec_64b & ~CSMask1sec_64b)));

           if(gAddrData->ADDRHASHNORMADDR[LOC_2][0] & 0x1)
           {
             Chipselect1 = ((internal_bit_wise_xor((((((UINT64)(gAddrData->EXT_ADDRHASHNORMADDR[LOC_2][0]))<<32)|gAddrData->ADDRHASHNORMADDR[LOC_2][0])>>1) & (ADDR>>9))) ^ (cs1 & 0x1));
           }
           else
           {
             Chipselect1  = (cs1 & 0x1) ;
           }

           if(gAddrData->ADDRHASHNORMADDR[LOC_2][1] & 0x1)
           {
             Chipselect1 =  Chipselect1 | (((internal_bit_wise_xor((((((UINT64)(gAddrData->EXT_ADDRHASHNORMADDR[LOC_2][1]))<<32)|gAddrData->ADDRHASHNORMADDR[LOC_2][1])>>1) & (ADDR>>9))) ^ \
                                            ((cs1>>1) & 0x1))<<1);
           }
           else
           {
             Chipselect1 = Chipselect1 |(((cs1>>1)& 0x1)<<1);
           }
           if(gAddrData->ADDRHASHNORMADDR[LOC_2][2] & 0x1)
           {
             Chipselect1 =  Chipselect1 | ((((internal_bit_wise_xor(((((UINT64)(gAddrData->EXT_ADDRHASHNORMADDR[LOC_2][2]))<<32)|gAddrData->ADDRHASHNORMADDR[LOC_2][2])>>1) & (ADDR>>9))) ^ \
                                            ((cs1>>2) & 0x1))<<2);
           }
           else
           {
             Chipselect1 = Chipselect1 |(((cs1>>2)& 0x1)<<2);
           }

           TransCS = Chipselect1;
           InputAddr =0 ;
           CSMask1 =0;
           CSBase1 =0;
           //cs_rhs=0;
           //inputaddr_lhs=0;
           break;
        }//main eqn
      }//cs1 loop

      if (TransCS != cs_num)
      {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Expected CS=0x%x Actual CS =0x%x. Failed!!!...bank=0x%x row=0x%x col=0x%x rankmul=0x%x\n", \
            cs_num, TransCS, bank, row, col, rankmul);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ERROR: Expected rank is differnt from actual rank \n");
      }

      if (!(addrhashrmaddr0 & 1) && !(addrhashrmaddr1 & 1) && !(addrhashrmaddr2 & 1))
      {
        if (TransRankmul != rankmul)
        {
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "nohash Expected rankmul=0x%x Actual rankmul =0x%x. Failed!!!\n", rankmul, TransRankmul);
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ERROR: Expected rankmul is differnt from actual rank \n");
        }
      }
      else
      {
        //need to  rmhash or rather un rmhash stuf
        //but before that, we dont want cs to come into play. so we have to 0 out the cs in the address we got since we never used cs in the inital rmhash cal

        rankmul_temp1=0;
        if((gAddrData->ADDRHASHRMADDR[LOC_2][0] & 0x1) && (noofrm >=1))
        {
          rankmul_temp1 = ((internal_bit_wise_xor((gAddrData->ADDRHASHRMADDR[LOC_2][0]>>1) & (ChannelAddr_G>>9))) ^ (TransRankmul & 0x1)) ;
        }
        else
        {
          rankmul_temp1 = TransRankmul & 0x1;
        }

        if((gAddrData->ADDRHASHRMADDR[LOC_2][1] & 0x1) &&  (noofrm >=2))
        {
          rankmul_temp1 =  rankmul_temp1 | (((internal_bit_wise_xor((gAddrData->ADDRHASHRMADDR[LOC_2][1]>>1) & (ChannelAddr_G>>9))) ^ ((TransRankmul>>1) & 0x1))<<1)   ;
        }
        else
        {
          rankmul_temp1 = rankmul_temp1 |(((TransRankmul>>1)& 0x1)<<1);
        }

        if((gAddrData->ADDRHASHRMADDR[LOC_2][2] & 0x1) && (noofrm >=3))
         {
           rankmul_temp1 =  rankmul_temp1 | (((internal_bit_wise_xor((gAddrData->ADDRHASHRMADDR[LOC_2][2]>>1) & (ChannelAddr_G>>9))) ^ ((TransRankmul>>2) & 0x1))<<2)   ;
         }
         else
         {
           rankmul_temp1 = rankmul_temp1 |(((TransRankmul>>2)& 0x1)<<2);
         }

         if(rankmul_temp1 != rankmul)
         {
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Expected rankmul=0x%x Actual rankmul =0x%x. Failed!!!\n", rankmul, rankmul_temp1);
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "The umc=%x, ch=%x, rank=%x, bank=%x, row=%x col=%x and rankmul=%x \n", \
              umc_inst_num, umc_chan_num, cs_num, bank, row, col, rankmul);
         }
         else
         {
           TransRankmul = rankmul_temp1;
         }
      }

    }//else part after b/r/c checking

    return (ChannelAddr_G);
  }//NOT OOR
}

//-----------------------------------------------------------------------------------------------------------
//-----------------------------------------------------------------------------------------------------------
//-----------------------------------------------------------------------------------------------------------
// END OF HELPER FUNCTIONS
//-----------------------------------------------------------------------------------------------------------
//-----------------------------------------------------------------------------------------------------------
//-----------------------------------------------------------------------------------------------------------
/*------------------------------------------------------------------
 Function: checkDramHit
 Purpose: Lookup a system address in the DRAM address map.
 Parameters (all are input only)
   sysAddr (ulong)
     The address to be converted.
     The user must remove all VMGuard key indices from the system address.
 Returns:
   The physical CS location for the address in a FabricID format:
      (FabricID & NodeIDMask) >> NodeIDShift: the DF NodeID
      (FabricID & ComponentIDMask): The physical location of the channel
   Note that this FabricID may not match the "dstFabricID" that
   the CS is known as in address maps - for example unused CS fabricIDs can
   be downshifted. The idea is that this function gives back a physical
   CS channel number in a "FabricID" format to make it possible to
   determine the nodeID as well.
   NodeIDMask, NodeIDShift, and ComponentIDMask can be read from DF
   SystemInformation registers. However, note that on Stones, the
   socket1 NodeID is "2", although from an addressing standpoint,
   it is node1 (bus 0, device 0x19).
 Side Effects:
   None:
 *------------------------------------------------------------------*/
UINT32
checkDramHit (
  UINT64  sysAddr
  )
{
  UINT64 dramBaseAddr;
  UINT64 dramLimitAddr;
  UINT64 adjSysAddr;
  UINT64 dramHoleBase;
  UINT32 dramAddressMapRegs[ADDR_MAP_ARRAYSIZE];
  UINT32 moderatorInstanceId;
  UINT32 mapRegNumber;
  DfType dfType;
  UINT32 holeEn;
  UINT32 nodeId;
  UINT32 logicalDstFabricId, physicalDstFabricId;
  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "checkDramHit (0x%016lX)\n", sysAddr);
  // Detect version information
  determineDfType (&dfType);
  //IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "dfType = %d, isDgpu=%d, isHeterogeneous=%d\n",
  //                  dfType.dfVersion, dfType.isDGpu ? 1 : 0, dfType.isHeterogeneous ? 1 : 0);
  nodeId = 0; // We use nodeID zero to find the map hit.
  // Find a CCM instance ID (if there is one, otherwise a GCM instance ID)
  moderatorInstanceId = findModeratorInstanceId (&dfType, nodeId);
  for (mapRegNumber = 0; mapRegNumber < getNumAddressMaps (&dfType); mapRegNumber++)
  {
    getDramAddressMap (&dfType, moderatorInstanceId, nodeId, mapRegNumber, dramAddressMapRegs);
    // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "dramBaseReg=0x%08X, dramLimitReg=0x%08X dramIntlvReg=0x%08X, dramCtlReg=0x%08X\n",
    //         dramAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET], dramAddressMapRegs[ADDR_MAP_ARRAY_LIMIT_OFFSET],
    //         dramAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET], dramAddressMapRegs[ADDR_MAP_ARRAY_CTL_OFFSET]);
    if (extractDramAddrRangeValid (&dfType, dramAddressMapRegs) == 0)
    {
      continue;
    }
    dramBaseAddr = extractDramBaseAddr (&dfType, dramAddressMapRegs);
    dramLimitAddr = extractDramLimitAddr (&dfType, dramAddressMapRegs);
    // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "dramBaseAddr=0x%016lX, dramLimitAddr=0x%016lX\n",
    //         dramBaseAddr, dramLimitAddr);
    // Check if we hit in this address map (address within base and limit)
    if ((sysAddr >= dramBaseAddr) && (sysAddr <= dramLimitAddr))
    {
      // hit, now figure out the DstFabricId.
      IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "Hit in map %d\n", mapRegNumber);
      // Check that the address is not within the DRAM hole
      holeEn = extractLgcyMmioHoleEn (&dfType, dramAddressMapRegs);
      dramHoleBase = getDramHoleBase (&dfType);
      // If the address is between DramHoleBase and 4GB (1<<32), then it is an MIMO address
      if (holeEn && (sysAddr < (((UINT64)1) << 32)) && (sysAddr >= dramHoleBase))
      {
        IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_ERROR, "CheckDramHit was given a system address (0x%016lX) that was an MMIO address\n",
            sysAddr);
        ASSERT (FALSE);
      }

      // The rules for which address was used in the hash algorithm changed.
      // In order to more efficiently handle various mod and divide algorithms,
      // whether or not the hardware does a subtract of the base before doing
      // the FabricID calculation has changed.
      //   DF2:                               The raw system address bits are used
      //   DF3 except 6-channel interleaving: The raw system address bits are used
      //   DF3 6-channel interleaving:        The bases are subtracted first
      //   DF3.5 && DF4:                      The bases are subtracted first
      //   DF4.5:                             The raw system address bits are used
      adjSysAddr = sysAddr;
      if ((dfType.dfVersion == DF_TYPE_DF3POINT5) || (dfType.dfVersion == DF_TYPE_DF4) ||
          (decodeDramIntLvNumChan (&dfType, dramAddressMapRegs) == INTERLEAVE_MODE_DF3_6CHAN))
      {
        // Account for the DRAM hole - look for the address to be above 4GB
        if (holeEn && (adjSysAddr >= (((UINT64)1) << 32)))
        {
          // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "dramHoleBase=0x%016lX, adjust=0x%016lX\n",
          //         dramHoleBase, ((((UINT64) 1) << 32) - dramHoleBase));
          adjSysAddr -= ((((UINT64)1) << 32) - dramHoleBase);
          // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "adjSysAddr=0x%016lX\n", adjSysAddr);
        }
        adjSysAddr -= dramBaseAddr;
        // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "adjSysAddr=0x%016lX\n", adjSysAddr);
      }
      // Get the base FabricID from the address map
      logicalDstFabricId = extractDstFabricId (&dfType, dramAddressMapRegs);
      // Translate the address to a (logical) FabricID offset from dstFabricID.
      logicalDstFabricId += getCsLogicalComponentIdFromAddr (&dfType, dramAddressMapRegs, adjSysAddr);
      // Convert the component ID part into a physical component ({nodeId, instanceID})
      physicalDstFabricId = convertLogicalFabricIdToPhysicalFabricId (&dfType, logicalDstFabricId, dramAddressMapRegs);
      IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "CheckDramHit completed: System address 0x%016lX hits at physical CS FabricID=0x%x (logical 0x%x)\n",
          sysAddr, physicalDstFabricId, logicalDstFabricId);
      return (physicalDstFabricId);
    }
  }
  // missed in address maps.
  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_ERROR, "CheckDramHit completed: SysAddr 0x%016lX missed DRAM maps\n",
      sysAddr);
  ASSERT (0);
  return (0);
}

/*------------------------------------------------------------------
 Function: calcNormAddr
 Purpose: Normalize a system address into a memory controller
   (normalized) system address.
 Parameters (all are input only)
   sysAddr (ulong)
     The address to be converted.
     The user must remove all VMGuard key indices from the system address.
 Returns:
   A normalized address (ulong)
 Side Effects:
   None:
 *------------------------------------------------------------------*/
NORMALIZED_ADDRESS
calcNormAddr(
  UINT64 sysAddr
  )
{
  UINT64  hiAddrOffset;
  UINT64  dramBaseAddr;
  UINT64  dramLimitAddr;
  UINT64  dramHoleBase;
  UINT64  normAddr = 0;
  UINT32  dramAddressMapRegs[ADDR_MAP_ARRAYSIZE];
  UINT32  dramOffsetReg;
  BOOLEAN performCsNormalization;
  UINT32  i;
  UINT32  nodeId;
  UINT32  csInstanceId;
  UINT32  moderatorInstanceId;
  UINT32  csFabricId;
  INT32   mapRegNumber;
  UINT32  holeEn;
  UINT32  hiAddrOffsetEn;
  DfType  dfType;
  UINT32 fabricBlockInstanceInformation0Reg;
  UINT32 instanceSubType;
  BOOLEAN isCmp;
  NORMALIZED_ADDRESS normalizedAddress;

  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "calcNormAddr (0x%016lX)\n", sysAddr);

  dramLimitAddr = 0;

  // Detect version information
  determineDfType (&dfType);
  //IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "dfType = %d, isDgpu=%d, isHeterogeneous=%d\n",
  //                  dfType.dfVersion, dfType.isDGpu ? 1 : 0, dfType.isHeterogeneous ? 1 : 0);
  // Find where this address maps
  csFabricId = checkDramHit (sysAddr);

  // Note that checkDramHit really returns {nodeID, channelNumber} in a FabricID format.
  // So the low order bits are really the physical channel number, and the physical channel number maps one to one to the instanceID
  // So grab the CS instance ID from the checkDramHit results
  csInstanceId = csFabricId & getComponentIdMask (&dfType);
  // Grab the node ID from the checkDramHit results
  nodeId = (csFabricId & getNodeIdMask (&dfType)) >> getNodeIdShift (&dfType);
  // Check if it hits on DRAM mapping register 0 or 1 in the CS.
  // Special case heterogeneous systems where there is only one map used on the dGPU side (avoids having to find the off-chip CS)
  // BOZO: Do similar for MI300 - switch to isHeterogeneous. But MI300 requires to know what the offset is based on the limit.
  if ((dfType.dfVersion == DF_TYPE_DF3POINT5) && !dfType.isDGpu && (nodeId > 0))
  {
    // Here we just use the moderator maps on node 0 to determine it and know that the CS has a offset of zero.
    // All the other information is usable in the moderator maps for finding the normalized address.
    moderatorInstanceId = findModeratorInstanceId (&dfType, 0);
    mapRegNumber = findMapRegBySysAddr (&dfType, 0, sysAddr);
    IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "Using moderatorInstanceId=%d, mapRegNumber=%d for heterogeneous access\n", moderatorInstanceId, mapRegNumber);
    getDramAddressMap (&dfType, moderatorInstanceId, 0, mapRegNumber, dramAddressMapRegs);
    mapRegNumber = -1;
    isCmp = FALSE; // not supported on heterogeneous systems
  } else
  {
    mapRegNumber = -1;
    for (i = 0; i < getNumCsMaps (&dfType); i++)
    {
      // first fetch it
      getDramAddressMap (&dfType, csInstanceId, nodeId, i, dramAddressMapRegs);
      dramBaseAddr = extractDramBaseAddr (&dfType, dramAddressMapRegs);
      dramLimitAddr = extractDramLimitAddr (&dfType, dramAddressMapRegs);
      if (extractDramAddrRangeValid (&dfType, dramAddressMapRegs) &&
          (sysAddr >= dramBaseAddr) && (sysAddr <= dramLimitAddr))
      {
        mapRegNumber = i;
        break;
      }
    }
    ASSERT (mapRegNumber >= 0);
    fabricBlockInstanceInformation0Reg = getDfRegFabricBlkInstInfo0 (&dfType, csInstanceId, nodeId);
    instanceSubType = getBits (DF__INSTANCE_SUBTYPE_BITPOS_LO, DF__INSTANCE_SUBTYPE_BITPOS_HI, fabricBlockInstanceInformation0Reg);
    if (instanceSubType == DF__CSCMP_INSTANCE_SUBTYPE_VALUE)
    {
      isCmp = TRUE;
    } else
    {
      isCmp = FALSE;
    }
  }
  ASSERT (extractDramAddrRangeValid (&dfType, dramAddressMapRegs) == 1);
  performCsNormalization = TRUE;
  if (isCmp)
  {
    // Check if the normalization is not enabled on ths CS_CMP (CNLI would see SPA)
    if (getBits(20, 20, getDfReg(&dfType, csInstanceId, nodeId, DF__COHERENTSLAVEMODECTRLA1_REGADDR)) > 0)
    {
      //IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "CS normalization is disabled.\n");
      performCsNormalization = FALSE;
    }
  }

  if (performCsNormalization)
  {
    dramBaseAddr = extractDramBaseAddr (&dfType, dramAddressMapRegs);
    IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "dramBaseAddr=0x%016lX\n", dramBaseAddr);

    holeEn = extractLgcyMmioHoleEn (&dfType, dramAddressMapRegs);
    dramHoleBase = getDramHoleBase (&dfType);
    //IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "holeEn = %d, dramHoleBase = 0x%016lX\n", holeEn, dramHoleBase);
    // Account for the DRAM hole
    if (holeEn && (sysAddr >= dramHoleBase))
    {
      // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "dramHoleBase = 0x%016lX, adjust = 0x%016lX\n",
      //         dramHoleBase, ((((UINT64) 1) << 32) - dramHoleBase));
      normAddr = sysAddr - ((((UINT64)1) << 32) - dramHoleBase);
      // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "post hole address=0x%016lX\n", normAddr);
    } else
    {
      normAddr = sysAddr;
    }
    // Subtract the base.
    normAddr = normAddr - dramBaseAddr;
    // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "post base address=0x%016lX\n", normAddr);
    // Now remove the bits and normalize...
    normAddr = normalizeAddr (&dfType, dramAddressMapRegs, normAddr);
    // Add the offset, skip if it is known to be zero in heterogeneous systems
    if (mapRegNumber > 0)
    {
      dramOffsetReg = getDfRegDramOffset (&dfType, csInstanceId, nodeId, mapRegNumber);
      hiAddrOffsetEn = getBit (DF__HI_ADDR_OFFSET_EN_BITPOS, dramOffsetReg);
      ASSERT (hiAddrOffsetEn == 1);
      hiAddrOffset = extractDramOffset (&dfType, dramOffsetReg);
      // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "hiAddrOffset=0x%016lX\n", hiAddrOffset);
      normAddr = normAddr + hiAddrOffset;
    }
  }
  if (isCmp)
  {
    // Now go to the CNLI and determine the interleaving within the CXL device
    normAddr = calcCxlNormAddrFromCsNormAddr (&dfType, csFabricId, normAddr);
  }
  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "CalcNormAddr completed: System address 0x%016lX maps to normalized address=0x%016lX at CS FabricID=0x%x\n",
      sysAddr, normAddr, csFabricId);
  // Add the offset
  normalizedAddress.normalizedAddr = normAddr;
  normalizedAddress.normalizedSocketId = (UINT8)((csFabricId & getSocketIdMask (&dfType)) >> getSocketIdShift (&dfType));
  normalizedAddress.normalizedDieId = (UINT8)((csFabricId & getDieIdMask (&dfType)) >> getDieIdShift (&dfType));
  normalizedAddress.normalizedChannelId = (UINT8)(csFabricId & ~(getSocketIdMask (&dfType) | getDieIdMask (&dfType)));
  normalizedAddress.reserved = 0;

  return (normalizedAddress);
}

/*------------------------------------------------------------------
 Function: calcSysAddr
 Purpose: Denormalize an address from a memory controller into a
   system address.
   A normalized address comes from a memory controller that is on
   a particular socket and die, as well as a physical channel number
   (the UMC instance).
   This routine takes the location of the memory controller
   (socket+die+physical number) and converts the normalized
   address to a system adress.
 Parameters (all are input only)
   normAddr (ulong)
     The address to be converted
   addrSocketNum (uint):
     The socket number within the system where the memory channel is
     (i.e. where the normalized address was observed).
     Valid range:
      ZP/SSP/GN/RS: 0-1
      BA: 0, 4-7
   addrDieNum (uint):
     The die number within the socket where the memory channel is
     (i.e. where the normalized address was observed).
     Note: this is not related to any CCD die numbers.
     Valid range:
      ZP: 0-3
      SSP/GN/RS: 0
      BA: 0-1 (1 only when socketNum is 4-7).
   umcPhysChannelNum (uint):
     This channel number within the die where this normalized
     address was found.
     Valid range:
      ZP: 0-1
      SSP/GN/BA: 0-7
      RS: 0-15 (12-15 are CS_CMP related to CXL and are allowed)
     Note, that this is memory channel number from 0-n.
     It is both the physical UMC ID as well as the
     coherent station instanceID (UMCn is always attached to CSn).
     However, it may not be the CS fabricID as DF supports the
     ability to remap.
     Note that to convert an external pin name (e.g. channel A-H)
     to a UMC number, the table in the PPR must be consulted.
     It is not a consecutive (e.g. A=0, B=1, H=7) map!!!
 Returns:
   A system address (ulong)
 Side Effects:
   None:
 Limitations:
   - The system address returned will not have any VMGuard key information
   - When the UMC address is being used for system functions
     (e.g. PSP private area, CC6 save address), the algorithm will
     give you the system address - which is actually a UEFI reserved
     region in memory.
 *------------------------------------------------------------------*/
UINT64
calcSysAddr(
  UINT64  normAddr,
  UINT32  addrSocketNum,
  UINT32  addrDieNum,
  UINT32  umcPhysChannelNum
  )
{
  UINT64  hiAddrOffset;
  UINT64  lastDramOffset;
  UINT64  dramBaseAddr;
  UINT64  dramLimitAddr;
  UINT64  dramHoleBase;
  UINT64  sysAddr;
  UINT32  dramAddressMapRegs[ADDR_MAP_ARRAYSIZE];
  UINT32  dramOffsetReg;
  UINT32  i;
  UINT32  nodeIdShift;
  UINT32  socketIdShift;
  UINT32  socketIdMask;
  UINT32  dieIdMask;
  UINT32  dieIdShift;
  UINT32  nodeId;
  UINT32  csInstanceId;
  UINT32  csFabricId;
  UINT32  ccmInstanceId;
  INT32   mapRegNumber;
  UINT32  intLvMode;
  UINT32  holeEn;
  UINT32  hiAddrOffsetEn;
  DfType  dfType;
  // Detect version information
  determineDfType (&dfType);
  //IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "dfType = %d, isDgpu=%d, isHeterogeneous=%d\n",
   //   dfType.dfVersion, dfType.isDGpu ? 1 : 0, dfType.isHeterogeneous ? 1 : 0);
  socketIdShift = getSocketIdShift (&dfType);
  socketIdMask = getSocketIdMask (&dfType);
  dieIdShift = getDieIdShift (&dfType);
  dieIdMask = getDieIdMask (&dfType);
  nodeIdShift = getNodeIdShift (&dfType);
  nodeId = (((addrSocketNum << socketIdShift) & socketIdMask) | ((addrDieNum << dieIdShift) & dieIdMask)) >> nodeIdShift;
  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "calcSysAddr (0x%016lX, nodeId=%d, channel=%d)\n",
      normAddr, nodeId, umcPhysChannelNum);
  //IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "socketIdShift=%d, socketIdMask=0x%x, dieIdShift=%d, dieIdMask=0x%x, nodeIdShift=%d\n",
   //   socketIdShift, socketIdMask, dieIdShift, dieIdMask, nodeIdShift);
  // Find the matching CS
  // If one was to do this programmatically, it would still require us to have some SOC-specific
  // defines to determine how the CS->UMC is attached). In addition, one would have to be careful
  // of coherent stations that are gated because they are unused (no memory, not valid on the package, etc).
  // However, on all CPU products, the relationship is one-to-one (CS0 maps to UMC0) and furthermore, CS0 is
  // always starting at instance ID 0 in the fabric.
  // For heterogeneous systems, the calling function must account for any swizzle in the non-CPU nodes.
  csInstanceId = umcPhysChannelNum;
  // Read the CS offset registers and determine whether this
  // address was part of base/limit register 0, or 1
  // Special case heterogeneous systems where there is only one map used on the dGPU side (avoids having to find the off-chip CS)
  // BOZO: Do similar for MI300 - switch to isHeterogeneous. But MI300 requires to know what the offset is based on the limit.
  if ((dfType.dfVersion == DF_TYPE_DF3POINT5) && !dfType.isDGpu && (nodeId > 0))
  {
    // No FabricID remapping in heterogeneous systems, CS fabricID is just instanceID and nodeID
    csFabricId = csInstanceId | (nodeId << nodeIdShift);
    // Here we just use the CCM maps on node 0 to determine it and know that the CS has a offset of zero.
    // All the other information is usable in the CCM for finding the normalized address.
    ccmInstanceId = findModeratorInstanceId (&dfType, 0);
    mapRegNumber = findMapRegByDstFabricId (&dfType, 0, csFabricId);
    IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "Using ccmInstanceId=%d, mapRegNumber=%d for heterogeneous access\n", ccmInstanceId, mapRegNumber);
    getDramAddressMap (&dfType, ccmInstanceId, 0, mapRegNumber, dramAddressMapRegs);
    hiAddrOffset = 0;
    mapRegNumber = -1;
  } else
  {
    // Once you have the CS instance ID, now get the actual FabricID from the information block
    csFabricId = getBits (DF__BLOCK_FABRICID_BITPOS_LO, DF__BLOCK_FABRICID_BITPOS_HI,
                 getDfRegFabricBlkInstInfo3 (&dfType, csInstanceId, nodeId));
    IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "csInstanceId=%d, csFabricId=0x%x\n", csInstanceId, csFabricId);
    mapRegNumber = 0;
    lastDramOffset = 0;
    for (i = 1; i < getNumCsMaps (&dfType); i++)
    {
      dramOffsetReg = getDfRegDramOffset (&dfType, csInstanceId, nodeId, i);
      hiAddrOffsetEn = getBit (DF__HI_ADDR_OFFSET_EN_BITPOS, dramOffsetReg);
      hiAddrOffset = extractDramOffset (&dfType, dramOffsetReg);
      //IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "hiAddrOffsetEn[%d]=%d, hiAddrOffset[%d]=0x%016lX\n", i, hiAddrOffsetEn, i, hiAddrOffset);
      if (hiAddrOffsetEn)
      {
        // Map 0 would have the normalized offset of 0. Maps 1-n must have a non-zero offset.
        ASSERT (hiAddrOffset != 0);
        // We require the maps to be in order for this search to work.
        ASSERT (hiAddrOffset > lastDramOffset);
        if (normAddr >= hiAddrOffset)
        {
          mapRegNumber = i;
        }
        lastDramOffset = hiAddrOffset;
      }
    }
    // Refetch it after the search because this could have overshot
    dramOffsetReg = getDfRegDramOffset (&dfType, csInstanceId, nodeId, mapRegNumber);
    hiAddrOffsetEn = getBit (DF__HI_ADDR_OFFSET_EN_BITPOS, dramOffsetReg);
    hiAddrOffset = extractDramOffset (&dfType, dramOffsetReg);
    IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "mapRegNumber=%d, hiAddrOffset=0x%016lX\n", mapRegNumber, hiAddrOffset);
    getDramAddressMap (&dfType, csInstanceId, nodeId, mapRegNumber, dramAddressMapRegs);
  }
  //IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "dramBaseReg=0x%08X, dramLimitReg=0x%08X dramIntlvReg=0x%08X, dramCtlReg=0x%08X\n",
   //   dramAddressMapRegs[ADDR_MAP_ARRAY_BASE_OFFSET], dramAddressMapRegs[ADDR_MAP_ARRAY_LIMIT_OFFSET],
   //   dramAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET], dramAddressMapRegs[ADDR_MAP_ARRAY_CTL_OFFSET]);
  ASSERT (extractDramAddrRangeValid (&dfType, dramAddressMapRegs) == 1);
  intLvMode = decodeDramIntLvNumChan (&dfType, dramAddressMapRegs);
  dramBaseAddr = extractDramBaseAddr (&dfType, dramAddressMapRegs);
  dramLimitAddr = extractDramLimitAddr (&dfType, dramAddressMapRegs);
  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "dramBaseAddr=0x%016lX, dramLimitAddr=0x%016lX\n", dramBaseAddr, dramLimitAddr);
  holeEn = extractLgcyMmioHoleEn (&dfType, dramAddressMapRegs);
  dramHoleBase = getDramHoleBase (&dfType);
  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "holeEn=%d, dramHoleBase=0x%016lX\n", holeEn, dramHoleBase);
  // Subtract the normalized offset
  sysAddr = normAddr - hiAddrOffset;
  //IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "after subtracting offset, sysAddr=0x%016lX\n", sysAddr);
  // Denormalize the address
  sysAddr = deNormalizeAddr (&dfType, csFabricId, dramAddressMapRegs, sysAddr);
  //IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "Denormalized address=0x%016lX\n", sysAddr);
  // IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "post normalization, sysAddr=0x%016lX\n", sysAddr);
  // The rules for which address was used in the hash algorithm changed.
  // In order to more efficiently handle various mod and divide algorithms,
  // whether or not the hardware does a subtract of the base before doing
  // the FabricID calculation has changed.
  //   DF2:                               The raw system address bits are used (case 1)
  //   DF3 except 6-channel interleaving: The raw system address bits are used (case 1)
  //   DF3 6-channel interleaving:        The bases are subtracted first (case 2)
  //   DF3.5 && DF4:                      The bases are subtracted first (case 2)
  //   DF4.5:                             The raw system address bits are used (case 1)
  // For denormalization, case 1 means the bases are added and then the hash is recalculated
  // And case 2 means that the hash is recalculated first and then the bases are added.
  // This test is for case 1
  if (((dfType.dfVersion < DF_TYPE_DF3POINT5) && (intLvMode != INTERLEAVE_MODE_DF3_6CHAN)) ||
      (dfType.dfVersion >= DF_TYPE_DF4POINT5))
  {
    // Add in the base.
    sysAddr = sysAddr + dramBaseAddr;
    // Account for the DRAM hole
    if (holeEn && (sysAddr >= dramHoleBase))
    {
      IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "dramHoleBase=0x%016lX, adjust=0x%016lX\n",
          dramHoleBase, ((((UINT64)1) << 32) - dramHoleBase));
      sysAddr = sysAddr + ((((UINT64)1) << 32) - dramHoleBase);
    }
    //IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "System address after hole/base = 0x%016lX\n", sysAddr);
  }
  //IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "prior to hashing, sysAddr=0x%016lX\n", sysAddr);
  // Now account for any hashing (corrects the CS ID that was placed in the address if the hash doesnt match)
  sysAddr = deNormHashAddr (&dfType, dramAddressMapRegs, sysAddr);
  //IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "System address after hash correction = 0x%016lX\n", sysAddr);
  //IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "post hashing, sysAddr=0x%016lX\n", sysAddr);

  // This test is for case 2
  if ((dfType.dfVersion == DF_TYPE_DF3POINT5) || (dfType.dfVersion == DF_TYPE_DF4) || (intLvMode == INTERLEAVE_MODE_DF3_6CHAN))
  {
    // Add in the base.
    sysAddr = sysAddr + dramBaseAddr;
    // Account for the DRAM hole
    if (holeEn && (sysAddr >= dramHoleBase))
    {
      IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "dramHoleBase=0x%016lX, adjust=0x%016lX\n",
          dramHoleBase, ((((UINT64)1) << 32) - dramHoleBase));
      sysAddr = sysAddr + ((((UINT64)1) << 32) - dramHoleBase);
    }
    //IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "System address after hole/base = 0x%016lX\n", sysAddr);
  }
  //IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "calculated sysAddr=0x%016lX\n", sysAddr);
  // Check that you didnt go over the limit
  ASSERT (sysAddr <= dramLimitAddr);
  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "CalcSysAddr completed: CS fabricID 0x%x normalized address 0x%016lX mapped to SysAddr 0x%016lX\n",
      csFabricId, normAddr, sysAddr);
  return (sysAddr);
}

/*------------------------------------------------------------------
 Function: calcSysAddrForCxl
 Purpose: Denormalize an address from a CXL device into a
   system address.
   A normalized address comes from a CXL port (e.g. "P1") and
   a sub-link (e.g. the second x4 device on "P1") that is on
   a particular socket and die.
   This routine takes the device location and converts the
   normalized address to a system adress.
 Parameters (all are input only)
   normAddr (ulong)
     The address to be converted
   dstFabricId (uint):
     The destination fabric ID for the IOS that hosts the
     CXL. This would match the DstFabricID in the data fabric
     PCIe configuration bus mapping registers.
   cxlPortNum (uint):
     When a link is bifurcated, this is an index specifying
     which sub-port the device is on (i.e. where the normalized address
     was observed). For unbifurcated links, this parameter should be 0.
     Note: Two x8 devices are enumerated as 0, 1. This parameter is not
           a function of which physical lanes the device is on, but
           of the CXL port bifurcation index.
 Terminology:
   1) Each DF node (socket/IOD) has 1-n PCIe/xGMI "links". These are the
      physical x16 groupings that are named P0, G0, P1, G1, ....
   2) A given link is hosted by an IOHC (an IOHC may have more than one
      link but at this time, an IOHC may only have one CXL-capable link).
   3) Each IOHC has an SDP port to the fabric for downstream transactions.
   4) This DstFabricID of that port is in the PCIe configuration map registers
      so that if the CXL device is on bus <n>, then there will be a map
      to send bus <n> transactions to a DstFabriciD
   5) Therefore, a DstFabricID identifies the CXL link to this program.
   6) A given CXL link can be "bifurcated" into more than one "port".
      This port number is also given in the parameters and identifies
      the port within the link.
 Returns:
   A system address (ulong)
 Side Effects:
   None:
 Limitations:
   - Refer to calcSysAddr limitations
 *------------------------------------------------------------------*/
UINT64 calcSysAddrForCxl(
  UINT64 normAddr,
  UINT32 dstFabricId,
  UINT32 cxlPortNum
  )
{
  UINT32 nodeIdShift;
  UINT32 socketIdShift;
  UINT32 socketIdMask;
  UINT32 dieIdMask;
  UINT32 dieIdShift;
  UINT32 nodeId;
  DfType dfType;
  UINT32 deviceAndVendorId;
  UINT32 i, j, numDFInstances, instanceType, instanceSubType, cnliInstanceId = 0, csInstanceId = 0;
  UINT32 addrSocketNum, addrDieNum;
  INT32 iosInstanceId;
  UINT64 sysAddr, tempAddrA, tempAddrB, cxlBaseAddr, cxlLimitAddr;
  UINT32 cxlAddressMapRegs[ADDR_MAP_ARRAYSIZE], fabricBlockInstanceInformation0Reg;
  UINT32 npaEn, npaBaseEn, intLvLinkEn, numInterleaveBits, intLvAddrBit, portIndex;
  BOOLEAN found, performCsCmpDenormalization;

  // Detect version information
  determineDfType (&dfType);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "dfType = %d, isDgpu=%d, isHeterogeneous=%d\n",
                    dfType.dfVersion, dfType.isDGpu ? 1 : 0, dfType.isHeterogeneous ? 1 : 0);
  socketIdShift = getSocketIdShift (&dfType);
  socketIdMask = getSocketIdMask (&dfType);
  dieIdShift = getDieIdShift (&dfType);
  dieIdMask = getDieIdMask (&dfType);
  nodeIdShift = getNodeIdShift (&dfType);
  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "calcSysAddrForCxl (0x%016lX, dstFabricId=0x%x, cxlPortNum=%d)\n",
      normAddr, dstFabricId, cxlPortNum);
  ASSERT (cxlPortNum < DF__NUM_CXL_SUBPORTS_PER_LINK);
  //IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "socketIdShift=%d, socketIdMask=0x%x, dieIdShift=%d, dieIdMask=0x%x, nodeIdShift=%d\n",
  //                  socketIdShift, socketIdMask, dieIdShift, dieIdMask, nodeIdShift);
  // We search to find the IOS instance ID for this destination fabricID
  iosInstanceId = -1;
  nodeId = (dstFabricId & getNodeIdMask (&dfType)) >> nodeIdShift;
  numDFInstances = getBits (DF__BLOCK_INSTANCE_COUNT_BITPOS_LO, DF__BLOCK_INSTANCE_COUNT_BITPOS_HI, getDfRegFabricBlkInstanceCnt (&dfType, nodeId));
  for (i = 0; i < numDFInstances; i++)
  {
    fabricBlockInstanceInformation0Reg = getDfRegFabricBlkInstInfo0 (&dfType, i, nodeId);
    // Skip gated blocks (detected because at least one bit must be non-zero in non-gated blocks)
    if (fabricBlockInstanceInformation0Reg == 0)
    {
      continue;
    }
    //remove for Turin, instanceType = getBits (DF__INSTANCE_TYPE_BITPOS_LO, DF__INSTANCE_TYPE_BITPOS_HI, fabricBlockInstanceInformation0Reg);
    //remove for Turin, instanceSubType = getBits (DF__INSTANCE_SUBTYPE_BITPOS_LO, DF__INSTANCE_SUBTYPE_BITPOS_HI, fabricBlockInstanceInformation0Reg);
    // IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "instanceType=0x%x instanceSubType=0x%x\n",
    //   instanceType,
    //   instanceSubType);
    //remove for Turin, if ((instanceType == DF__NCS_INSTANCE_TYPE_VALUE) && (instanceSubType == DF__IOS_INSTANCE_SUBTYPE_VALUE))
    {
      if (getBits (DF__BLOCK_FABRICID_BITPOS_LO, DF__BLOCK_FABRICID_BITPOS_HI, getDfRegFabricBlkInstInfo3 (&dfType, i, nodeId)) == dstFabricId)
      {
        iosInstanceId = i;
        break;
      }
    }
  }
  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "Final iosInstanceId=0x%x \n", iosInstanceId);
  ASSERT (iosInstanceId >= 0);

  // Need to convert the IOS DstFabricID number to a DF CNLI and CSCMP ID.
  // There is no good way to do this, so it is a hardcoded table.
  deviceAndVendorId = getDfReg (&dfType, BROADCAST_ACCESS, 0, DF__DEVICEID_REGADDR);
  if (deviceAndVendorId == STONES_DF_DEVICE_AND_VENDOR_ID)
  {
    cnliInstanceId = iosInstanceId - STONES_IOS0_INSTANCE_ID + STONES_CNLI0_INSTANCE_ID;
    csInstanceId = iosInstanceId - STONES_IOS0_INSTANCE_ID + STONES_CSCMP0_INSTANCE_ID;
  } else if (deviceAndVendorId == TURIN_DF_DEVICE_AND_VENDOR_ID)
  {
    // IOS0, 1, 4, and 5 are attached to CNLI0-3.
    cnliInstanceId = iosInstanceId - TURIN_IOS0_INSTANCE_ID;
    if (cnliInstanceId >= 4) {
      cnliInstanceId -= 2;
    }
    csInstanceId = cnliInstanceId + TURIN_CSCMP0_INSTANCE_ID;
    cnliInstanceId = cnliInstanceId + TURIN_CNLI0_INSTANCE_ID;
  } else
  {
    ASSERT (FALSE);
  }
  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "Determined IOS instance as %d, CNLI instance as %d, CSCMP instance %d\n",
      iosInstanceId, cnliInstanceId, csInstanceId);

  fabricBlockInstanceInformation0Reg = getDfRegFabricBlkInstInfo0 (&dfType, cnliInstanceId, nodeId);
  instanceType = getBits (DF__INSTANCE_TYPE_BITPOS_LO, DF__INSTANCE_TYPE_BITPOS_HI, fabricBlockInstanceInformation0Reg);
  ASSERT (instanceType == DF__CNLI_INSTANCE_TYPE_VALUE);
  fabricBlockInstanceInformation0Reg = getDfRegFabricBlkInstInfo0 (&dfType, csInstanceId, nodeId);
  instanceType = getBits (DF__INSTANCE_TYPE_BITPOS_LO, DF__INSTANCE_TYPE_BITPOS_HI, fabricBlockInstanceInformation0Reg);
  instanceSubType = getBits (DF__INSTANCE_SUBTYPE_BITPOS_LO, DF__INSTANCE_SUBTYPE_BITPOS_HI, fabricBlockInstanceInformation0Reg);
  ASSERT (instanceType == DF__CS_INSTANCE_TYPE_VALUE);
  ASSERT (instanceSubType == DF__CSCMP_INSTANCE_SUBTYPE_VALUE);

  for (i = 0; i < DF__NUM_CXL_MAPS_AVAILABLE; i++)
  {
    getCxlAddressMap (&dfType, cnliInstanceId, nodeId, i, cxlAddressMapRegs);
    // check AddrRngVal
    if (extractCxlAddrRangeValid (&dfType, cxlAddressMapRegs) != 0)
    {
      intLvLinkEn = extractCxlIntLvLinkEn (&dfType, cxlAddressMapRegs);
      // Check if we hit in this address map
      // (intLvLinkEn is a one-hot vector of the CXL ports)
      if ((intLvLinkEn & (1 << cxlPortNum)) != 0)
      {
        IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "Hit in CXL address map %d\n", i);
        npaEn = extractCxlNpaEn (&dfType, cxlAddressMapRegs);
        npaBaseEn = extractCxlNpaBaseEn (&dfType, cxlAddressMapRegs);
        numInterleaveBits = decodeCxlNumSubChannelInterleaveBits (&dfType, cxlAddressMapRegs);
        intLvAddrBit = decodeCxlIntLvAddrSel (&dfType, cxlAddressMapRegs);
        IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "NPAEn=%d, NPABaseEn=%d, intLvLinkEn=%d\n",
            npaEn, npaBaseEn, intLvLinkEn);
        IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "numInterleaveBits=%d, intLvAddrBit=%d\n",
            numInterleaveBits, intLvAddrBit);
        // Use the port to determine the CXL interleave address bits
        // If NPAEn=0 (normally not 0 for type-3) or if there is no interleaving,
        // then the csNormAddress is equal to the device normalized address
        if ((npaEn == 1) && (numInterleaveBits > 0))
        {
          // Search through intLvLinkEn until you find the nth bit
          // For example, let's assume intLvLinkEn=0xC and intLvAddrSel=0 (bit 6).
          // This is 2-way port interleaving (two x4 devices off the second half of the link)
          // If port was 2, then addr[6]=0 because bit2 was the first bit set
          // If port was 3, then addr[6]=1 because bit3 was the second bit set
          portIndex = 0;
          found = FALSE;
          for (j = 0; j < DF__NUM_CXL_SUBPORTS_PER_LINK; j++)
          {
            if ((intLvLinkEn & (1 << j)) != 0)
            {
              if (j == cxlPortNum)
              {
                found = TRUE;
                break;
              }
              portIndex++;
            }
          }
          ASSERT (found);
          IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "The value of the interleave address bits is %d\n", portIndex);
          tempAddrA = expandBits64 (intLvAddrBit, numInterleaveBits, normAddr);
          tempAddrB = (UINT64)portIndex;
          tempAddrB = tempAddrB << intLvAddrBit;
          normAddr = tempAddrA | tempAddrB;
          IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "CXL normalized address after inserting port (%d) bits %d:%d = 0x%016lX\n",
              portIndex, (intLvAddrBit + numInterleaveBits - 1), intLvAddrBit, normAddr);
        }
        cxlBaseAddr = extractCxlBaseAddr (&dfType, cxlAddressMapRegs);
        cxlLimitAddr = extractCxlLimitAddr (&dfType, cxlAddressMapRegs);
        // If NpaBaseEn is 0 (normally not 0 for type-3), the device has seen the address without
        // having the cxlBaseAddress subtracted, so we do not add it here. Normally we add the
        // cxlBaseAddress to get the "CS normalized address" that was provided.
        if (npaBaseEn == 1)
        {
          IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "Adding cxlBaseAddr = 0x%016lX\n",
              cxlBaseAddr);
          normAddr = normAddr + cxlBaseAddr;
        }
        // The normalized address should fall in this range
        ASSERT (normAddr >= cxlBaseAddr);
        ASSERT (normAddr <= cxlLimitAddr);
        IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "CS_CMP normalized address = 0x%016lX\n",
            normAddr);
        performCsCmpDenormalization = TRUE;
        // For format 1 SOCs, detect if CS_CMP did not normalize the address. If it did not, then system address is now known.
        // But if it did normalize (and format 0 SOCs always normalize), then go through the CSCMP denormalization.
        if (getBits (DF__CXL_ADDR_FORMAT_BITPOS_LO, DF__CXL_ADDR_FORMAT_BITPOS_HI, cxlAddressMapRegs[ADDR_MAP_ARRAY_INTLV_OFFSET]) > 0)
        {
          // Any non-zero values in F2x130[7:0] indicate that there is no CS->CNLI normalization.
          // Note that we would normally expect these to align with the npaBaseEn, but it is a separate configuration,
          // Hypothetically speakjing, CS_CMP could denormalize and CNLI could then not "further" normalize, or vice versa.
          // But the expected use case would be to either have both normalize, or neither normalize...
          if (getBits(20, 20, getDfReg(&dfType, csInstanceId, nodeId, DF__COHERENTSLAVEMODECTRLA1_REGADDR)) > 0)
          {
            performCsCmpDenormalization = FALSE;
          }
        }
        IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "performCsCmpDenormalization = %d\n", performCsCmpDenormalization);
        if (performCsCmpDenormalization)
        {
          // At this point, the calculation of the system address goes through the normal denormalization.
          addrSocketNum = (dstFabricId & socketIdMask) >> socketIdShift;
          addrDieNum = (dstFabricId & dieIdMask) >> dieIdShift;
          sysAddr = calcSysAddr (normAddr, addrSocketNum, addrDieNum, csInstanceId);
        } else
        {
          sysAddr = normAddr;
        }
        return (sysAddr);
      }
    }
  }
  // missed in address maps.
  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "calcSysAddrForCxl completed: normalized 0x%016lX missed CXL maps\n",
      normAddr);
  ASSERT (0);
  return (0);
}

/*----------------------------------------------------------------------------------------*/
/**
 * Convert normalized address to chip select, row, column, bank, rankmul
 *
 * ChannelAddr expected to be passed from the caller should be till lsb=0 and only msb=39 is considered
 * no fancy 39:4 version
 * pkg_no: socket number
 * mpu_no: die number
 *
 *----------------------------------------------------------------------------------------*/
VOID
translate_norm_to_dpa (
  UINT64  ChannelAddr,
  UINT8   pkg_no,
  UINT8   mpu_no,
  UINT8   umc_inst_num,
  UINT8   umc_chan_num,
  UINT8   cs_num,
  UINT8   bank,
  UINT32  row,
  UINT16  col,
  UINT8   rankmul,
  UINT64  *Dpa
)
{
  UINTN   channelId;
  UINTN   configdimm;
  UINT8   dimm_num;
  UINTN   bankbits;
  UINTN   rowbits;
  UINTN   rankmulbits;

  // channelId: channel ID of system
  umc_chan_num = 0;   // umc_chan_num = 0 in SSP
  channelId = convert_to_addr_trans_index(pkg_no, mpu_no, umc_inst_num, umc_chan_num);

  // read out the addrhash* registers here
  dimm_num = cs_num >> 1;
  configdimm = gAddrData->CONFIGDIMM [channelId][dimm_num];

  // bank + bankgroup bits = gAddrData->CONFIGDIMM[channel][0] bit [5:4], 0: 3 bits, 1: 4 bits, 2: 5 bits
  bankbits = ((configdimm >> 4) & 0x3) + 3;
  // row bits = gAddrData->CONFIGDIMM[channel][0] bit [11:8], then plus 10;
  rowbits = ((configdimm >> 8) & 0xF) + 10;
  // rankmul bits = gAddrData->CONFIGDIMM[channel][0] bit [7:6];
  rankmulbits = (configdimm >> 6) & 0x3;

  //*Dpa = (cs_num << (rowbits + rankmulbits + bankbits + 13)) | (row << (rankmulbits + bankbits + 13)) | (rankmul << (bankbits + 13)) | (bank << 13) | (col << 3) | (ChannelAddr & 0x7);
  *Dpa = (((((((((cs_num << rowbits) | row ) << (rankmulbits)) | rankmul) << bankbits) | bank) << 10) | col) << 3) | (ChannelAddr & 0x7);
}

EFI_STATUS
AcquireGpuSecBusNum (
  IN       UINT8 SocketId,
  IN       UINT8 DieId,
  OUT      UINT8 *SecBusNum
)
{
  return EFI_UNSUPPORTED;
}

