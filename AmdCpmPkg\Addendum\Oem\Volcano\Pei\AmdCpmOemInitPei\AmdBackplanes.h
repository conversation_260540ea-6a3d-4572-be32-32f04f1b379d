/*****************************************************************************
 * Copyright (C) 2015-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * AMD CPM OEM API, and related functions.
 *
 * Contains the definitions for AMD backplanes.
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      CPM
 * @e sub-project:  OEM
 * @e \$Revision: 270275 $   @e \$Date: 2013-08-09 03:54:44 +0800 (Fri, 09 Aug 2013) $
 *
 */

#ifndef _AMD_BACKPLANES_H_
#define _AMD_BACKPLANES_H_

typedef enum {
  STANDARD_BACKPLANE,
  FULLNVME_BACKPLANE
} BACKPLANE_TYPE;

#define AGPIO5_DEVSLP1_SATA_ZP1_L 0x14
#define AGPIO6_DEVSLP1_SATA_ZP1_L 0x18
#define AGPIO106_LAD2             0x1A8

////////////////// Volcano 4G Standard Definitions //////////////////

DXIO_PORT_DESCRIPTOR Volcano4gStandardS0[] = {
    { // UBM backplane HFC 0 - P3 <11-8>
       0,
       DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 24, 27, 1),
       DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
       PORT_PARAMS_START
         PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
         PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
         PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
         PORT_PARAM (PP_NPEM_ENABLE, 0x1),
         PORT_PARAM (PP_START_LANE, 24),
         PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
         PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
         PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
         PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
         PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
         PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
         PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
         PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
         PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
         PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
         PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
         PORT_PARAM (PP_SLOT_NUM, 0x20),
       PORT_PARAMS_END
    },
    { // UBM backplane HFC 1 - P3 <15-12>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 28, 31, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 28),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 2 - P3 <3-0>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 16, 19, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 16),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 3 - P3 <7-4>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 20, 23, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 20),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 4 - P1 <11-8>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 40, 43, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 40),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 5 - P1 <15-12>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 44, 47, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 44),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 6 - P1 <3-0>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 32, 35, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 32),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 7 - P1 <7-4>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 36, 39, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 36),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    }
};   // End of DXIO_PORT_DESCRIPTOR

ADDIN_CARD_PORTS Volcano4gStandardS0Entry = {
    0xFF,
    8,
    &Volcano4gStandardS0[0]
};

DXIO_PORT_DESCRIPTOR Volcano4gStandardS1[] = {
    { // UBM backplane HFC 16 - P3 <3-0>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 16, 19, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 16),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x28),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 17 - P3 <7-4> B
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 20, 23, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 20),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x28),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 18 - P3 <11-8> C
       0,
       DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 24, 27, 1),
       DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
       PORT_PARAMS_START
         PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
         PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
         PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
         PORT_PARAM (PP_NPEM_ENABLE, 0x1),
         PORT_PARAM (PP_START_LANE, 24),
         PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
         PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
         PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
         PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
         PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
         PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
         PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
         PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
         PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
         PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
         PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
         PORT_PARAM (PP_SLOT_NUM, 0x28),
       PORT_PARAMS_END
    },
    { // UBM backplane HFC 19 - P3 <15-12> D
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 28, 31, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 28),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x28),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 20 - P1 <11-8> C
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 40, 43, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 40),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x28),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 21 - P1 <15-12> D
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 44, 47, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 44),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x28),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 22 - P1 <3-0> A
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 32, 35, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 32),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x28),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 23 - P1 <7-4> B
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 36, 39, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 36),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x28),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 24 - P0 <11-8>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 8, 11, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_START_LANE, 8),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x23),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 4),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 5),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x72),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x30),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 25 - P0 <15-12>
     0,
     DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 12, 15, 1),
     DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
     PORT_PARAMS_START
       PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
       PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
       PORT_PARAM (PP_START_LANE, 12),
       PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
       PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x23),
       PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
       PORT_PARAM (PP_GPIOx_BP_TYPE, 7),
       PORT_PARAM (PP_GPIOx_I2C_RESET, 8),
       PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
       PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
       PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
       PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x72),
       PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
       PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
       PORT_PARAM (PP_SLOT_NUM, 0x30),
       PORT_PARAMS_END
    }
};   // End of DXIO_PORT_DESCRIPTOR

ADDIN_CARD_PORTS Volcano4gStandardS1Entry = {
    0xFF,
    10,
    &Volcano4gStandardS1[0]
};

////////////////// Volcano 4G Full NVME Definitions //////////////////

DXIO_PORT_DESCRIPTOR Volcano4gFullNvmeS0[] = {
    { // UBM backplane HFC 0 - P3 <11-8>
       0,
       DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 24, 27, 1),
       DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
       PORT_PARAMS_START
         PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
         PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
         PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
         PORT_PARAM (PP_NPEM_ENABLE, 0x1),
         PORT_PARAM (PP_START_LANE, 24),
         PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
         PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
         PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
         PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
         PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
         PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
         PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
         PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
         PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
         PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
         PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
         PORT_PARAM (PP_SLOT_NUM, 0x20),
       PORT_PARAMS_END
    },
    { // UBM backplane HFC 1 - P3 <15-12>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 28, 31, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 28),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 2 - P3 <3-0>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 16, 19, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 16),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 3 - P3 <7-4>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 20, 23, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 20),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 4 - P1 <11-8>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 40, 43, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 40),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 5 - P1 <15-12>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 44, 47, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 44),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 6 - P1 <3-0>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 32, 35, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 32),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 7 - P1 <7-4>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 36, 39, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 36),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),    // BP1
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 8 - G3
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 88, 91, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 88),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x28),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 9 - G3
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 92, 95, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 92),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x28),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 10 - G3
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 80, 83, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 80),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x28),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 11 - G3
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 84, 87, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 84),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x28),
      PORT_PARAMS_END
    }
};   // End of DXIO_PORT_DESCRIPTOR

ADDIN_CARD_PORTS Volcano4gFullNvmeS0Entry = {
    0xFF,
    12,
    &Volcano4gFullNvmeS0[0]
};

DXIO_PORT_DESCRIPTOR Volcano4gFullNvmeS1[] = {
    { // UBM backplane HFC 12 - G1 
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 72, 75, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 72),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x2C),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 13 - G1 
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 76, 79, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 76),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x2D),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 14 - G1 
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 64, 67, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 64),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x2E),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 15 - G1 
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 68, 71, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 68),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 2),    // BP2
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x2F),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 16 - P3 <3-0>
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 16, 19, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 16),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x30),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 17 - P3 <7-4> B
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 20, 23, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 20),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x30),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 18 - P3 <11-8> C
       0,
       DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 24, 27, 1),
       DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
       PORT_PARAMS_START
         PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
         PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
         PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
         PORT_PARAM (PP_NPEM_ENABLE, 0x1),
         PORT_PARAM (PP_START_LANE, 24),
         PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
         PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
         PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
         PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
         PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
         PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
         PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
         PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
         PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
         PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
         PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
         PORT_PARAM (PP_SLOT_NUM, 0x30),
       PORT_PARAMS_END
    },
    { // UBM backplane HFC 19 - P3 <15-12> D
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 28, 31, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 28),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x30),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 20 - P1 <11-8> C
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 40, 43, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 40),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 8),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 9),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x30),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 21 - P1 <15-12> D
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 44, 47, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 44),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 11),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 12),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x30),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 22 - P1 <3-0> A
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 32, 35, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 32),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x30),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 23 - P1 <7-4> B
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 36, 39, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 36),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x22),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 3),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 4),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 3),    // BP3
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x30),
      PORT_PARAMS_END
    }
};   // End of DXIO_PORT_DESCRIPTOR

ADDIN_CARD_PORTS Volcano4gFullNvmeS1Entry = {
    0xFF,
    12,
    &Volcano4gFullNvmeS1[0]
};

#endif
