#pragma message( "Compal Server Override Compiling-" __FILE__ )
/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */
/**
 *  @file DxioCfgPoints.c
 *  @brief Callouts from MPIO initialization
 */

#include <GnbRegistersBRH.h>
#include "AmdNbioPei.h"
#include <Filecode.h>
#include <MpioLib.h>
#include "PcieComplexData.h"
#include <Deli.h>

#define FILECODE NBIO_BRH_PEI_DXIOCFGPOINTS_FILECODE

/*----------------------------------------------------------------------------------------
 *                         E X T E R N   D E F I N I T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */

// Comment this line to enable extended debug output if tracing is enabled
//#undef GNB_TRACE_ENABLE

typedef enum  {
  SRIS_CMN = BIT0,
  SRIS_DBG = BIT1,
  SRIS_DBG_PBS = BIT2,
  SRIS_AUTODETECT = BIT3
} SRIS_CFG_TYPE;

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */

typedef struct {
  PCIE_CAP_LIST_STRUCT  CapList;
  PCIE_CAP_STRUCT       Caps;
} PCIE_CAP_REG;                                                             ///<

typedef struct {
  BOOLEAN  IsCxl;
  BOOLEAN  IsPortAllocated;
} PORT_STATE_IN_WRAPPER;

typedef struct {
  PCIe_DPC_STATUS_DATA   *DpcStatusData;
  BOOLEAN                IsPortActive;
} AFTER_RECONFIG_BUFFER;

typedef struct {
    UINT8    PRESET;
    UINT8    LC_FORCE_PRE_CURSOR;
    UINT8    LC_FORCE_CURSOR;
    UINT8    LC_FORCE_POST_CURSOR;
} FORCE_PRESET_STRUCT;

FORCE_PRESET_STRUCT ROMDATA ForcePresetTable [] = {
  {0,   0x0, 0x24, 0xC},
  {1,   0x0, 0x28, 0x8},
  {2,   0x0, 0x26, 0xA},
  {3,   0x0, 0x2A, 0x6},
  {4,   0x0, 0x30, 0x0},
  {5,   0x4, 0x2C, 0x0},
  {6,   0x6, 0x2A, 0x0},
  {7,   0x5, 0x21, 0xA},
  {8,   0x6, 0x23, 0x7},
  {9,   0x8, 0x28, 0x0}
};

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                    P P I   N O T I F Y   D E S C R I P T O R S
 *----------------------------------------------------------------------------------------
 */



/**----------------------------------------------------------------------------------------*/
/**
 * Interface to configure DXIO/PCIe ports after ports are mapped and before reconfig
 *  - This function is called once for each socket
 *
 *
 *
 * @param[in]  GnbHandle      Pointer to the Silicon Descriptor for this node
 * @param[in]  PortPointer    Pointer to the first topology entry
 *
 * @retval    UINT32          Size of ancillary data required to store the strap subheap
 */
 /*----------------------------------------------------------------------------------------*/

UINT32
DxioCfgGetStrapListSize (
  IN       GNB_HANDLE            *GnbHandle,
  IN       DXIO_PORT_DESCRIPTOR  *PortPointer
  )
{
  return 0;
}


/**----------------------------------------------------------------------------------------*/
/**
 * Interface to configure DXIO/PCIe ports after ports are mapped and before reconfig
 *  - This function is called once for each socket
 *
 *
 *
 * @param[in]  GnbHandle      Pointer to the Silicon Descriptor for this node
 * @param[in]  AncillaryData  Pointer to the next available entry in Ancillary Data for this node
 * @param[in]  PortList       Pointer to the topology structures for this node
 *
 * @retval    AGESA_STATUS
 */
 /*----------------------------------------------------------------------------------------*/

UINT32
DxioCfgAddStrapList (
  IN       GNB_HANDLE             *GnbHandle,
  IN       VOID                   *AncillaryData,
  IN       DXIO_PORT_DESCRIPTOR   *PortList
  )
{
  return 0;
}

//=========================================================================================
/**
 *  @brief Callback from MpioLib to update GLOBAL CONFIG
 *
 *  @param [in] GnbHandle GNB_HANDLE pointer
 *  @param [in] ArgList   Pointer to array of arguments  cast as MPIO_GLOBAL_CONFIG
 *  @return               Returns nothing
 *
 *  @details This function provides platform-specific initialization of the MPIO_GLOBAL_CONFIG
 */
VOID
MpioCfgGlobalConfig (
  IN       GNB_HANDLE                     *GnbHandle,
  IN OUT   UINT32                         *ArgList
  )
{
  MPIO_GLOBAL_CONFIG    *GlobalConfig;

  GlobalConfig = (MPIO_GLOBAL_CONFIG *) ArgList;

  // Clock Gating
  GlobalConfig->PWRMNGMT_PRFRM_CLK_GATING = PcdGetBool (PcdCfgDxioClockGating) ? 1 : 0;

  // Training Timers
  if (PcdGetBool (PcdPcieDxioTimingControlEnable)) {
    GlobalConfig->LinkReceiverDetectionPolling = PcdGet32 (PcdPCIELinkReceiverDetectionPolling);
    GlobalConfig->LinkResetToTrainingTime = PcdGet32 (PcdPCIELinkResetToTrainingTime);
    GlobalConfig->LinkL0Polling = PcdGet32 (PcdPCIELinkL0Polling);
  }

  // Exact Match
  if (PcdGetBool(PcdPCIeExactMatchEnable)) {
    GlobalConfig->matchPortSizeExactly = 1;
  }

  // Valid PHY firmware
  if (PcdGet8 (PcdDxioPhyValid) == 0) {
    GlobalConfig->ValidPhyFWFlag = 0;
  }

  // Use PHY SRAM
  if (PcdGet8 (PcdDxioPhyProgramming) == 0) {
    GlobalConfig->usePhySram = 0;
  }

  // Skip Vetting
  if (PcdGet8 (PcdCfgSkipPspMessage) == 1) {
    GlobalConfig->skipVetting = 1;
  }

  if (PcdGet8 (PcdDxioSaveRestoreModes) == 1) {
    GlobalConfig->saverestoremode = 1;
  }

  GlobalConfig->disableSbrTrap = (PcdGetBool(PcdSbrBrokenLaneAvoidanceSup)? 0 : 1);
  GlobalConfig->disableLaneMarginingTrap = (PcdGetBool (PcdAutoFullMarginSup)? 0 : 1);

  // Link Disable at Power Off Delay
  IDS_HDT_CONSOLE (GNB_TRACE, "LinkDisableAtPowerOffDelay: %x\n", PcdGet8 (PcdLinkDisableAtPowerOffDelay));
  GlobalConfig->LinkDisableAtPowerOffDelay = PcdGet8 (PcdLinkDisableAtPowerOffDelay);

  // PCIe SPC Gen4
  GlobalConfig->Enable2SpcGen4 = PcdGetBool (PcdEnable2SpcGen4) ? 1 : 0;

  // PCIe SPC Gen5
  GlobalConfig->Enable2SpcGen5 = PcdGetBool (PcdEnable2SpcGen5) ? 1 : 0;

  // DFE TAP Enable
  GlobalConfig->DfeTapEnable = PcdGetBool (PcdDfeTapEnable) ? 1 : 0;

  // DFE TAP Count
  GlobalConfig->DfeTapCount = PcdGet8 (PcdDfeTapCount);

  // Non-PCIe Compliant Support
  GlobalConfig->EnablePcieNonCompliantWa = PcdGetBool (PcdPcieNonPcieCompliantTrainingFailureSupport) ? 1 : 0;

  // PCIe loopback Mode
  GlobalConfig->enableLoopbackSupport = PcdGetBool (PcdCfgPcieLoopbackMode) ? 1 : 0;

  // SLT Mode (xGMI Loopback) Support
  IDS_HDT_CONSOLE (GNB_TRACE, "Slt Mode: %x\n", PcdGetBool (PcdActiveSltMode));
  GlobalConfig->ActiveSltMode = PcdGetBool (PcdActiveSltMode) ? 1 : 0;

  // TX FIFO Read Pointer Offset
  GlobalConfig->TxFifoRdPtrOffset = PcdGet8 (PcdCfgDxioTxFIFORdPtrOffset);

  // Safe recovery on BERExceedErr
  IDS_HDT_CONSOLE (GNB_TRACE, "Safe recovery on BERExceedErr value: %x\n", PcdGetBool (PcdsafeRecoveryBER) ? 1 : 0);
  GlobalConfig->RunXgmiSafeRecoveryOdt = PcdGetBool (PcdsafeRecoveryBER) ? 1 : 0;

  // Periodic Zcal
  IDS_HDT_CONSOLE (GNB_TRACE, "Periodic Zcal: %x\n", PcdGetBool (PcdPeriodicCal) ? 1 : 0);
  GlobalConfig->RunZcal = PcdGetBool (PcdPeriodicCal) ? 1 : 0;

  GlobalConfig->XgmiAsyncFifoModeEnable = 0;
#ifdef INTERNAL_IDS  // Enable xGMI Async Fifo Mode on internal build only
  GlobalConfig->XgmiAsyncFifoModeEnable = 1;
#endif

  // Enforce Gen5 max speed reporting
  IDS_HDT_CONSOLE (GNB_TRACE, "Limit hotplug devices to PCIe boot speed: %x\n", PcdGetBool (PcdLimitHpDevicesToPcieBootSpeed) ? 1 : 0);
  GlobalConfig->EnforceGen5MaxSpeedReporting = PcdGetBool (PcdLimitHpDevicesToPcieBootSpeed) ? 0 : 1;

  return;
}

//=========================================================================================
// Timepoint after port mapping and before reconfig
//=========================================================================================

/*----------------------------------------------------------------------------------------*/
/**
 * Per-Wrapper Callback for Early Trained Ports confinguration before bifurcation.
 * Use function for capability settings set dynamically.  Static capabilities for early training
 * should be set in ABL.
 *
 *
 * @param[in]     Wrapper   Wrapper configuration info
 * @param[in]     GnbHandle GnbHandle Pointer
 */
VOID
STATIC
EarlyTrainingMpioCfgBeforeReconfigWrapper (
  IN      PCIe_WRAPPER_CONFIG               *Wrapper,
  IN      GNB_HANDLE                        *GnbHandle
  )
{
  UINTN Index;

  // ACS Enablement
  SmnPrivateRegRMW (
    GnbHandle,
    WRAP_SPACE(GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_STRAP_F0_ADDRESS),
    (UINT32) ~(PCIE_STRAP_F0_STRAP_F0_ACS_EN_MASK),
    Wrapper->AcsSupport << PCIE_STRAP_F0_STRAP_F0_ACS_EN_OFFSET,
    0
    );
  // Advanced error reporting (AER)
  SmnPrivateRegRMW (
    GnbHandle,
    WRAP_SPACE(GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_STRAP_F0_ADDRESS),
    (UINT32) ~(PCIE_STRAP_F0_STRAP_F0_AER_EN_MASK),
    Wrapper->AdvancedErrorReporting << PCIE_STRAP_F0_STRAP_F0_AER_EN_OFFSET,
    0
    );
  // ECRC Capability
  SmnPrivateRegRMW (
    GnbHandle,
    WRAP_SPACE(GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_STRAP_F0_ADDRESS),
    (UINT32) ~(PCIE_STRAP_F0_STRAP_F0_ECRC_GEN_EN_MASK |
               PCIE_STRAP_F0_STRAP_F0_ECRC_CHECK_EN_MASK),
    (((((Wrapper->ECRCSupport & 0x1) == 0x1) ? 1 : 0) << PCIE_STRAP_F0_STRAP_F0_ECRC_GEN_EN_OFFSET) |
    ((((Wrapper->ECRCSupport & 0x2) == 0x2) ? 1 : 0) << PCIE_STRAP_F0_STRAP_F0_ECRC_CHECK_EN_OFFSET)),
    0
    );

  // IDE Capability
  for (Index = 0; Index < 9; Index += 2)  {
    // PRIV_PCIE_IDE_CAP 16Bits data. Used 32 bits access for both odd even num.
    SmnPrivateRegRMW (
      GnbHandle,
      WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIE0_N0NBIO0_PRIV_PCIE_IDE_CAP_ADDRESS) + (Index * 2),
      (UINT32) ~(PRIV_PCIE_IDE_CAP_STRAP_IDE_SUPPORTED_MASK |\
                 ((Index < 8 ? PRIV_PCIE_IDE_CAP_STRAP_IDE_SUPPORTED_MASK : 0) << 16)),
      (((PcdGetBool (PcdCfgSevSnpSupport) == TRUE) || (PcdGetBool (PcdCfgSevTioSupport) == TRUE) || (PcdGetBool (PcdPcieIdeCapSup) == TRUE)) ? \
       (Index < 8 ? 0x10001 : 1) : 0) << PRIV_PCIE_IDE_CAP_STRAP_IDE_SUPPORTED_OFFSET,
      0
      );
  }
  SmnPrivateRegRMW (
    GnbHandle,
    WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIECORE_PCIE_RX_IDE_MISC_CTL_ADDRESS),
    (UINT32) ~(PCIE_RX_IDE_MISC_CTL_RX_IDE_BYPASS_EN_MASK),
    (((PcdGetBool (PcdCfgSevSnpSupport) == TRUE) || (PcdGetBool (PcdCfgSevTioSupport) == TRUE) || (PcdGetBool (PcdPcieIdeCapSup) == TRUE)) ? \
     0 : 1) << PCIE_RX_IDE_MISC_CTL_RX_IDE_BYPASS_EN_OFFSET,
    0
    );
  // Receiver Error report
  SmnPrivateRegRMW (
    GnbHandle,
    WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_HW_DEBUG_ADDRESS),
    (UINT32) ~(PCIE_HW_DEBUG_HW_13_DEBUG_MASK),
    (PcdGetBool(PcdCfgRcvErrEnable)? 1 : 0) << PCIE_HW_DEBUG_HW_13_DEBUG_OFFSET,
    0
  );
}
//=========================================================================================
// Timepoint after port mapping and before reconfig
//=========================================================================================

/*----------------------------------------------------------------------------------------*/
/**
 * Per-Engine Callback for All ports before bifurcation
 *
 *
 *
 * @param[in]     Engine  Engine configuration info
 * @param[in,out] Buffer  Buffer pointer
 * @param[in]     Pcie    PCIe configuration info
 */
VOID
STATIC
MpioCfgBeforeReconfigCallbackAllPorts (
  IN      PCIe_ENGINE_CONFIG                *Engine,
  IN OUT  VOID                              *Buffer,
  IN      PCIe_WRAPPER_CONFIG               *Wrapper
  )
{
  GNB_HANDLE          *GnbHandle;
  GnbHandle = (GNB_HANDLE *) PcieConfigGetParentSilicon (Wrapper);

  IDS_HDT_CONSOLE (GNB_TRACE, "%a Enter\n", __FUNCTION__);
  if (PcdGet8 (PcdCfgDxioCplTimeout) != 0xFF) {
    SmnPrivateRegRMW (
      GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, Engine->Type.Port.PortId, SMN_FUNC0_PCIE0NBIO0_PCIE_TX_PORT_CTRL_1_ADDRESS),
      (UINT32) ~(PCIE_TX_PORT_CTRL_1_TX_CPL_PASS_P_MASK),
      ((PcdGetBool (PcdCfgDxioAllowCompPass) ? 1 : 0) << PCIE_TX_PORT_CTRL_1_TX_CPL_PASS_P_OFFSET),
      0
      );
  }
}

/*----------------------------------------------------------------------------------------*/
/**
 * Per-Engine Callback for port configuration before bifurcation
 *
 *
 *
 * @param[in]     Engine  Engine configuration info
 * @param[in,out] Buffer  Buffer pointer
 * @param[in]     Pcie    PCIe configuration info
 */
VOID
STATIC
MpioCfgBeforeReconfigCallback (
  IN      PCIe_ENGINE_CONFIG                *Engine,
  IN OUT  VOID                              *Buffer,
  IN      PCIe_WRAPPER_CONFIG               *Wrapper
  )
{
  GNB_HANDLE          *GnbHandle;
  PORT_STATE_IN_WRAPPER *PortStateInWrapper;
  UINT16                Index;

  GnbHandle = (GNB_HANDLE *) PcieConfigGetParentSilicon (Wrapper);
  PortStateInWrapper = (PORT_STATE_IN_WRAPPER *) Buffer;

  IDS_HDT_CONSOLE (GNB_TRACE, "%a Enter\n", __FUNCTION__);

  if (PcieLibIsEngineAllocated(Engine)) {
    PortStateInWrapper->IsPortAllocated = TRUE;

    // Enable ten bit tag CAP support
    if (Engine->Type.Port.PortCapabilities.TenBitTagSupport == 1) {
      Index = STRAP_BIF_TEN_BIT_TAG_COMPLETER_SUPPORTED_A_INDEX + (Engine->Type.Port.PortId * STRAP_BIF_PORT_DIFF);
      WritePcieStrap (
        GnbHandle,
        Index,
        1,
        Wrapper->WrapId
        );
    }
    else if(Engine->Type.Port.PortCapabilities.TenBitTagSupport == 2 ) {
       Index = STRAP_BIF_TEN_BIT_TAG_REQUESTER_SUPPORTED_A_INDEX + (Engine->Type.Port.PortId * STRAP_BIF_PORT_DIFF);
       WritePcieStrap (
        GnbHandle,
        Index,
        1,
        Wrapper->WrapId
        );
    }
    else if(Engine->Type.Port.PortCapabilities.TenBitTagSupport == 3) {
        Index = STRAP_BIF_TEN_BIT_TAG_COMPLETER_SUPPORTED_A_INDEX + (Engine->Type.Port.PortId * STRAP_BIF_PORT_DIFF);
        WritePcieStrap (
        GnbHandle,
        Index,
        1,
        Wrapper->WrapId
        );

        Index = STRAP_BIF_TEN_BIT_TAG_REQUESTER_SUPPORTED_A_INDEX + (Engine->Type.Port.PortId * STRAP_BIF_PORT_DIFF);
        WritePcieStrap (
        GnbHandle,
        Index,
        1,
        Wrapper->WrapId
        );
    }

    // Lane power state during dynamic link width change
    if (Engine->Type.Port.PortFeatures.DynLanesPwrState != 0xf) {
        SmnPrivateRegRMW (GnbHandle,
              PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_LINK_WIDTH_CNTL_ADDRESS),
              (UINT32) ~(PCIE_LC_LINK_WIDTH_CNTL_LC_DYN_LANES_PWR_STATE_MASK | PCIE_LC_LINK_WIDTH_CNTL_LC_TURN_OFF_UNUSED_LANES_MASK),
              ((Engine->Type.Port.PortFeatures.DynLanesPwrState & 0x3) << PCIE_LC_LINK_WIDTH_CNTL_LC_DYN_LANES_PWR_STATE_OFFSET) |
              ((Engine->Type.Port.PortFeatures.TurnOffUnusedLanes & 0x1) << PCIE_LC_LINK_WIDTH_CNTL_LC_TURN_OFF_UNUSED_LANES_OFFSET),
               0
               );
    }

  //Data Link feature (DLF)
  if(Engine->Type.Port.PortCapabilities.DataLinkFeature == 0)
  {
     Index = STRAP_BIF_LOCAL_DLF_SUPPORTED_A_INDEX + (Engine->Type.Port.PortId * STRAP_BIF_PORT_DIFF);
     WritePcieStrap (GnbHandle, STRAP_BIF_DLF_EN_INDEX, 1, Wrapper->WrapId);
     WritePcieStrap (GnbHandle, Index, 1, Wrapper->WrapId);
  }
  else if(Engine->Type.Port.PortCapabilities.DataLinkFeature == 1)
  {
     Index = STRAP_BIF_LOCAL_DLF_SUPPORTED_A_INDEX + (Engine->Type.Port.PortId * STRAP_BIF_PORT_DIFF);
     WritePcieStrap (GnbHandle, STRAP_BIF_DLF_EN_INDEX, 1, Wrapper->WrapId);
     WritePcieStrap (GnbHandle, Index, 0, Wrapper->WrapId);
   }
   else if(Engine->Type.Port.PortCapabilities.DataLinkFeature == 2)
   {
     WritePcieStrap (GnbHandle, STRAP_BIF_DLF_EN_INDEX, 0, Wrapper->WrapId);
   }

  //Data Link feature Exchange Control(DLF)
  Index = STRAP_BIF_DLF_EXCHANGE_EN_A_INDEX + (Engine->Type.Port.PortId * STRAP_BIF_PORT_DIFF);
  WritePcieStrap (GnbHandle, Index, Engine->Type.Port.PortFeatures.DataLinkFeatureExchangeControl, Wrapper->WrapId);

  //For configurable SSID/SSVID
  WritePcieStrap (
    GnbHandle,
    WRP_MISC_STRAP_RESERVED_INDEX,
    BIT9,
    Wrapper->WrapId
    );

  //Allow Compliance
  Index = STRAP_BIF_COMPLIANCE_DIS_A_INDEX + (Engine->Type.Port.PortId * STRAP_BIF_PORT_DIFF);
  if (PcdGet16(PcdAmdAllowCompliance) == 0x0F) {
    WritePcieStrap (GnbHandle, Index, 0, Wrapper->WrapId);
  } else {
    WritePcieStrap (GnbHandle, Index, PcdGet16(PcdAmdAllowCompliance), Wrapper->WrapId);
  }

  //Max Payload support
  Index = STRAP_BIF_MAX_PAYLOAD_SUPPORT_A_INDEX + (Engine->Type.Port.PortId * STRAP_BIF_PORT_DIFF);
  if(Engine->Type.Port.PortCapabilities.MaxPayloadSupport != 0xf)
  {
    WritePcieStrap (GnbHandle, Index, Engine->Type.Port.PortCapabilities.MaxPayloadSupport, Wrapper->WrapId);
  }

    // Program equalization parameters into their corresponding strap. These are written per wrapper
    // but programmed in the per engine call. Different lane CBS values within a wrapper should be
    // avoided to prevent overwriting the desired value for the whole wrapper.

    // Gen3 US/DS Preset
    WritePcieStrap (
      GnbHandle,
      STRAP_BIF_PCIE_LANE_EQUALIZATION_CNTL_DOWNSTREAM_PORT_RX_PRESET_HINT_INDEX,
      Engine->Type.Port.LaneEqualizationCntl.DsRxPresetHint,
      Wrapper->WrapId
      );
    WritePcieStrap (
      GnbHandle,
      STRAP_BIF_PCIE_LANE_EQUALIZATION_CNTL_UPSTREAM_PORT_RX_PRESET_HINT_INDEX,
      Engine->Type.Port.LaneEqualizationCntl.UsRxPresetHint,
      Wrapper->WrapId
      );
    WritePcieStrap (
      GnbHandle,
      STRAP_BIF_PCIE_LANE_EQUALIZATION_CNTL_DOWNSTREAM_PORT_TX_PRESET_INDEX,
      Engine->Type.Port.LaneEqualizationCntl.DsTxPreset,
      Wrapper->WrapId
      );
    WritePcieStrap (
      GnbHandle,
      STRAP_BIF_PCIE_LANE_EQUALIZATION_CNTL_UPSTREAM_PORT_TX_PRESET_INDEX,
      Engine->Type.Port.LaneEqualizationCntl.UsTxPreset,
      Wrapper->WrapId
      );

    // Gen4 US/DS Preset
    WritePcieStrap (
      GnbHandle,
      STRAP_BIF_LANE_EQUALIZATION_CNTL_DSP_16GT_TX_PRESET_INDEX,
      Engine->Type.Port.Gen4LaneEqualizationCntl.DsTxPreset,
      Wrapper->WrapId
      );
    WritePcieStrap (
      GnbHandle,
      STRAP_BIF_LANE_EQUALIZATION_CNTL_USP_16GT_TX_PRESET_INDEX,
      Engine->Type.Port.Gen4LaneEqualizationCntl.UsTxPreset,
      Wrapper->WrapId
      );

     // Gen5 US/DS Preset
    WritePcieStrap (
      GnbHandle,
      STRAP_BIF_LANE_EQUALIZATION_CNTL_DSP_32GT_TX_PRESET_INDEX,
      Engine->Type.Port.Gen5LaneEqualizationCntl.DsTxPreset,
      Wrapper->WrapId
      );
    WritePcieStrap (
      GnbHandle,
      STRAP_BIF_LANE_EQUALIZATION_CNTL_USP_32GT_TX_PRESET_INDEX,
      Engine->Type.Port.Gen5LaneEqualizationCntl.UsTxPreset,
      Wrapper->WrapId
      );

    // Gen5 Precoding Request
    if (Engine->Type.Port.PortData.LinkSpeedCapability == DxioGen5 ||
        Engine->Type.Port.PortData.LinkSpeedCapability == DxioGenMaxSupported) {
      WritePcieStrap (
          GnbHandle,
          (STRAP_BIF_LC_SET_TRANSMITTER_PRECODE_REQUEST_A_INDEX + (STRAP_BIF_PORT_DIFF * Engine->Type.Port.PortId)),
          Engine->Type.Port.Gen5LaneEqualizationCntl.PrecodeRequest,
          Wrapper->WrapId
          );
    }

    // Gen5 Advertise EQ To High Rate Support = 0 on P4 and P5
    if (((GnbHandle->RBIndex & 0x1) == 0) && (Wrapper->WrapId == 2)) {
      WritePcieStrap (
        GnbHandle,
        (STRAP_BIF_LC_ADVERTISE_EQ_TO_HIGH_RATE_SUPPORT_A_INDEX + (STRAP_BIF_PORT_DIFF * Engine->Type.Port.PortId)),
        0,
        Wrapper->WrapId
        );
    }

    // These are all programmed per engine


    // Gen3 Disable Phase2/3 EQ
    SmnPrivateRegRMW (GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_EQ_CNTL_8GT_ADDRESS),
      (UINT32) ~(PCIE_LC_EQ_CNTL_8GT_LC_USC_EQ_NOT_REQD_8GT_MASK),
      Engine->Type.Port.DisGen3EQPhase << PCIE_LC_EQ_CNTL_8GT_LC_USC_EQ_NOT_REQD_8GT_OFFSET,
      0
      );
    // Gen4 Disable Phase2/3 EQ
    SmnPrivateRegRMW (GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_EQ_CNTL_16GT_ADDRESS),
      (UINT32) ~(PCIE_LC_EQ_CNTL_16GT_LC_USC_EQ_NOT_REQD_16GT_MASK),
      Engine->Type.Port.DisGen4EQPhase << PCIE_LC_EQ_CNTL_16GT_LC_USC_EQ_NOT_REQD_16GT_OFFSET,
      0
      );

    // Gen5 Disable Phase2/3 EQ
    SmnPrivateRegRMW (GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_EQ_CNTL_32GT_ADDRESS),
      (UINT32) ~(PCIE_LC_EQ_CNTL_32GT_LC_USC_EQ_NOT_REQD_32GT_MASK),
      Engine->Type.Port.DisGen5EQPhase << PCIE_LC_EQ_CNTL_32GT_LC_USC_EQ_NOT_REQD_32GT_OFFSET,
      0
      );

    // ASPM
//COMPAL_CHANGE    if (Engine->InitStatus == INIT_STATUS_PCIE_TRAINING_SUCCESS){
      if ((GnbHandle->SocketId == PcdGet8 (PcdEarlyBmcLinkSocket)) &&
        (PcdGet8 (PcdEarlyBmcLinkLaneNum) >= Wrapper->StartPhyLane) &&
        (PcdGet8 (PcdEarlyBmcLinkLaneNum) <= Wrapper->EndPhyLane)){
        Engine->Type.Port.PortCapabilities.AspmCapability=0;
      }
//COMPAL_CHANGE    }
    Index = STRAP_BIF_PM_SUPPORT_A_INDEX + ((Engine->Type.Port.PortId) * STRAP_BIF_PORT_DIFF);
    WritePcieStrap (GnbHandle, Index, Engine->Type.Port.PortCapabilities.AspmCapability & 0x2, Wrapper->WrapId);

    // Link PM SubState
    WritePcieStrap (
      GnbHandle,
      PORT_STRAP_INDEX(STRAP_BIF_PCI_PM_L1_1_SUPPORTED_A_INDEX, Engine->Type.Port.PortId),
      0,
      Wrapper->WrapId
      );

    WritePcieStrap (
      GnbHandle,
      PORT_STRAP_INDEX(STRAP_BIF_L1_PM_SUBSTATES_SUPPORTED_A_INDEX, Engine->Type.Port.PortId),
      0,
      Wrapper->WrapId
      );

    if ((Engine->Type.Port.PortCapabilities.AspmL1_1 == 1) || (Engine->Type.Port.PortCapabilities.AspmL1_2 == 1))  {

      if (Engine->Type.Port.PortCapabilities.AspmL1_2 == 1) {
        Index = STRAP_BIF_ASPM_L1_1_SUPPORTED_A_INDEX + ((Engine->Type.Port.PortId) * STRAP_BIF_PORT_DIFF);
        WritePcieStrap (GnbHandle, Index, 1, Wrapper->WrapId);

        Index = STRAP_BIF_PCI_PM_L1_2_SUPPORTED_A_INDEX + ((Engine->Type.Port.PortId) * STRAP_BIF_PORT_DIFF);
        WritePcieStrap (GnbHandle, Index, 1, Wrapper->WrapId);
      }

      if (Engine->Type.Port.PortCapabilities.AspmL1_1 == 1)  {
        Index = STRAP_BIF_ASPM_L1_1_SUPPORTED_A_INDEX + ((Engine->Type.Port.PortId) * STRAP_BIF_PORT_DIFF);
        WritePcieStrap (GnbHandle, Index, 1, Wrapper->WrapId);
      }
    }

    // DPC capability
    if (Engine->Type.Port.PortCapabilities.DownstreamPortContainment != 0)
    {
         Index = STRAP_BIF_DPC_EN_A_INDEX + ((Engine->Type.Port.PortId) * STRAP_BIF_PORT_DIFF);
         WritePcieStrap (GnbHandle, Index, Engine->Type.Port.PortCapabilities.DownstreamPortContainment, Wrapper->WrapId);
    }

    // Set STRAP_BIF_SFI_EN_INDEX/STRAP_BIF_SFI_EN_A_INDEX
    if (((PcdGet8 (PcdAmdHotPlugHandlingMode) == 5) || (PcdGetBool (PcdAmdHotPlugForceSFIStrap))) &&
       ((Engine->Type.Port.PortData.LinkHotplug == DxioHotplugServerEntSSD) ||
       (Engine->Type.Port.PortData.LinkHotplug == DxioHotplugUBM) ||
       (Engine->Type.Port.PortData.LinkHotplug == DxioHotplugOCP))){
      IDS_HDT_CONSOLE (GNB_TRACE, "  SFI Straps Engine->Type.Port.PortId = 0x%x\n", Engine->Type.Port.PortId);
          WritePcieStrap (
            GnbHandle,
            PORT_STRAP_INDEX(STRAP_BIF_SFI_EN_INDEX, Engine->Type.Port.PortId),
            1,
            Wrapper->WrapId
        );
          WritePcieStrap (
            GnbHandle,
            PORT_STRAP_INDEX(STRAP_BIF_SFI_EN_A_INDEX, Engine->Type.Port.PortId),
            1,
            Wrapper->WrapId
        );
          WritePcieStrap (
            GnbHandle,
            PORT_STRAP_INDEX(STRAP_BIF_ERR_COR_SUBCLASS_CAPABLE_A_INDEX, Engine->Type.Port.PortId),
            1,
            Wrapper->WrapId
        );
        if (PcdGetBool (PcdAmdExposeSFIDRSSupport)) {
          WritePcieStrap (
            GnbHandle,
            PORT_STRAP_INDEX(STRAP_BIF_DRS_SUPPORTED_INDEX, Engine->Type.Port.PortId),
            1,
            Wrapper->WrapId
          );
        }
        if (PcdGetBool (PcdAmdExposeSFIOOBSupport)) {
            WritePcieStrap (
              GnbHandle,
              PORT_STRAP_INDEX(STRAP_BIF_OOB_PD_SUPPORTED_A_INDEX, Engine->Type.Port.PortId),
              1,
              Wrapper->WrapId
            );
          }
    }

    // RTM Margining Support
    WritePcieStrap (
      GnbHandle,
      STRAP_BIF_MARGIN_IGNORE_C_SKP_INDEX,
      (Engine->Type.Port.RxMarginPersistence? 0 : 1),
      Wrapper->WrapId
      );

    if (Engine->Type.Port.SrisEnableMode == 1) {
      IDS_HDT_CONSOLE (GNB_TRACE, " SRIS Config:\n");
      IDS_HDT_CONSOLE (GNB_TRACE, "  - PcdSrisCfgType       = 0x%x\n", PcdGet8 (PcdSrisCfgType));
      IDS_HDT_CONSOLE (GNB_TRACE, "  - SrisEnableMode       = 0x%x\n", Engine->Type.Port.SrisEnableMode);
      IDS_HDT_CONSOLE (GNB_TRACE, "  - SrisSkipInterval     = 0x%x\n", Engine->Type.Port.SrisSkipInterval);
      IDS_HDT_CONSOLE (GNB_TRACE, "  - LowerSkpOsGenSup     = 0x%x\n", Engine->Type.Port.LowerSkpOsGenSup);
      IDS_HDT_CONSOLE (GNB_TRACE, "  - LowerSkpOsRcvSup     = 0x%x\n", Engine->Type.Port.LowerSkpOsRcvSup);
      WritePcieStrap (
          GnbHandle,
          (STRAP_BIF_LC_SRIS_EN_A_INDEX + (STRAP_BIF_PORT_DIFF * Engine->Type.Port.PortId)),
          ((Engine->Type.Port.SrisAutoDetectMode == 1)? 0: 1),
          Wrapper->WrapId
          );
    }
    if (Engine->Type.Port.SrisAutoDetectMode == 1) {
      IDS_HDT_CONSOLE (GNB_TRACE, " SRIS AutoDetect:\n");
      IDS_HDT_CONSOLE (GNB_TRACE, "  - SrisAutoDetectMode   = 0x%x\n", Engine->Type.Port.SrisAutoDetectMode);
      IDS_HDT_CONSOLE (GNB_TRACE, "  - SrisSkpIntervalSel   = 0x%x\n", Engine->Type.Port.SrisSkpIntervalSel);
      IDS_HDT_CONSOLE (GNB_TRACE, "  - SrisAutodetectFactor = 0x%x\n", Engine->Type.Port.SrisAutodetectFactor);
      WritePcieStrap (
          GnbHandle,
          (STRAP_BIF_LC_SRIS_AUTODETECT_EN_A_INDEX + (STRAP_BIF_PORT_DIFF * Engine->Type.Port.PortId)),
          0x1,
          Wrapper->WrapId
          );
    }

    // Symbols per Clock strap programming (All Ports)
    //SPC Gen 1
    Index = STRAP_BIF_LC_SPC_MODE_2P5GT_A_INDEX + (Engine->Type.Port.PortId * STRAP_BIF_PORT_DIFF);
    WritePcieStrap (GnbHandle, Index, Engine->Type.Port.SpcMode.SpcGen1, Wrapper->WrapId);

    //SPC Gen 2
    Index = STRAP_BIF_LC_SPC_MODE_5GT_A_INDEX + (Engine->Type.Port.PortId * STRAP_BIF_PORT_DIFF);
    WritePcieStrap (GnbHandle, Index, Engine->Type.Port.SpcMode.SpcGen2, Wrapper->WrapId);

    //SPC Gen 3
    Index = STRAP_BIF_LC_SPC_MODE_8GT_A_INDEX + (Engine->Type.Port.PortId * STRAP_BIF_PORT_DIFF);
    WritePcieStrap (GnbHandle, Index, Engine->Type.Port.SpcMode.SpcGen3, Wrapper->WrapId);

    //SPC Gen 4
    if(PcdGetBool (PcdEnable2SpcGen4))  {
      IDS_HDT_CONSOLE (GNB_TRACE, "Overwritting SPC_Gen4 to 2 SPC\n");
      Engine->Type.Port.SpcMode.SpcGen4 = 1;
    }
    Index = STRAP_BIF_LC_SPC_MODE_16GT_A_INDEX + (Engine->Type.Port.PortId * STRAP_BIF_PORT_DIFF);
    WritePcieStrap (GnbHandle, Index, Engine->Type.Port.SpcMode.SpcGen4, Wrapper->WrapId);

    //SPC Gen 5
    if(PcdGetBool (PcdEnable2SpcGen5))  {
      IDS_HDT_CONSOLE (GNB_TRACE, "Overwritting SPC_Gen5 to 2 SPC\n");
      Engine->Type.Port.SpcMode.SpcGen5 = 1;
    }
    Index = STRAP_BIF_LC_SPC_MODE_32GT_A_INDEX + (Engine->Type.Port.PortId * STRAP_BIF_PORT_DIFF);
    WritePcieStrap (GnbHandle, Index, Engine->Type.Port.SpcMode.SpcGen5, Wrapper->WrapId);

    //PCIe Compliance
    Index = STRAP_BIF_E2E_PREFIX_EN_A_INDEX + (Engine->Type.Port.PortId * STRAP_BIF_PORT_DIFF);
    WritePcieStrap (GnbHandle, Index, 1, Wrapper->WrapId);

    //Rcv_L0s Entry
    WritePcieStrap (
      GnbHandle,
      PORT_STRAP_INDEX (STRAP_BIF_LC_RCV_L0_TO_RCV_L0S_DIS_A_INDEX, Engine->Type.Port.PortId),
      1,
      Wrapper->WrapId
      );
  }

  // SELECTABLE_DEEMPHASIS
  if(Engine->Type.Port.TXDeEmphasis != 0xf) {
      WritePcieStrap (
          GnbHandle,
          STRAP_BIF_DE_EMPHASIS_SEL_A_INDEX + (Engine->Type.Port.PortId * STRAP_BIF_PORT_DIFF),
          Engine->Type.Port.TXDeEmphasis,
          Wrapper->WrapId
      );
  }

  // Data Object Exchange (DOE)
  if (PcdGetBool(PcdDataObjectExchange)){
    Index = STRAP_BIF_DOE_EN_INDEX + (Engine->Type.Port.PortId * STRAP_BIF_PORT_DIFF);
    WritePcieStrap (GnbHandle, Index, 1, Wrapper->WrapId);
  }

  // All P Links (WrapId 0, 2, 5, and 7) support CXL
  if (CXL_CAPABLE_RB(GnbHandle)) {
    if(PcdGetBool (PcdAmdCxlOnAllPorts)) {
      Engine->Type.Port.CxlControl = 1;
    }
    // CXL Alternate Protocol Support
    if (Engine->Type.Port.CxlControl != 0) {
      PortStateInWrapper->IsCxl = TRUE;

      WritePcieStrap (
        GnbHandle,
        PORT_STRAP_INDEX(STRAP_BIF_LC_RTM1_PRESENCE_DET_SUPP_A_INDEX, Engine->Type.Port.PortId),
        1,
        Wrapper->WrapId
        );

      WritePcieStrap (
        GnbHandle,
        PORT_STRAP_INDEX(STRAP_BIF_LC_ALTERNATE_PROTOCOL_CHECK_RTM_CXL_AWARE_A_INDEX, Engine->Type.Port.PortId),
        1,
        Wrapper->WrapId
        );

      WritePcieStrap (GnbHandle, STRAP_BIF_CXL2_0_EN_INDEX, 1, Wrapper->WrapId);

      // STRAP_BIF_LC_ALTERNATE_PROTOCOL_DETAILS_2 = 0x1F (or 0x7 to disable CXL.cache)
      WritePcieStrap (
        GnbHandle,
        PORT_STRAP_INDEX(STRAP_BIF_LC_ALTERNATE_PROTOCOL_DETAILS_2_A_INDEX, Engine->Type.Port.PortId),
        0x41F,
        Wrapper->WrapId
        );

      // STRAP_BIF_LC_ADVERTISE_MODIFIED_TS_OS_SUPPORT_A_INDEX = 0x1
      WritePcieStrap (
        GnbHandle,
        PORT_STRAP_INDEX(STRAP_BIF_LC_ADVERTISE_MODIFIED_TS_OS_SUPPORT_A_INDEX, Engine->Type.Port.PortId),
        1,
        Wrapper->WrapId
        );

      // STRAP_BIF_LC_CXL_COMMON_CLOCK_IN_MODTS2_A_INDEX = 0x1
      WritePcieStrap (
        GnbHandle,
        PORT_STRAP_INDEX(STRAP_BIF_LC_CXL_COMMON_CLOCK_IN_MODTS2_A_INDEX, Engine->Type.Port.PortId),
        (PcdGetBool (PcdCxlSyncHeaderByPassCompMode)? 1 : 0),
        Wrapper->WrapId
        );

      // Temp Gen5 Advertisement for Alternate Protocol
      SmnPrivateRegRMW (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_LTSSM_CXL_CNTL_EXTRA_ADDRESS),
        (UINT32) ~(PCIE_LC_LTSSM_CXL_CNTL_EXTRA_LC_TEMP_GEN5_ADVERTISEMENT_FOR_ALTPTCL_MASK),
        ((PcdGetBool (PcdCxlTempGen5AdvertAltPtcl)? 1 : 0 ) << PCIE_LC_LTSSM_CXL_CNTL_EXTRA_LC_TEMP_GEN5_ADVERTISEMENT_FOR_ALTPTCL_OFFSET),
        0
        );

      // ARBMUX Skid Buffer
      SmnPrivateRegRMW (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_LTSSM_CXL_CNTL_EXTRA_ADDRESS),
        (UINT32) ~(PCIE_LC_LTSSM_CXL_CNTL_EXTRA_LC_ATTEMPT_FLUSH_CAMEM_FOR_ALL_RECOVERY_MASK),
        (1 << PCIE_LC_LTSSM_CXL_CNTL_EXTRA_LC_ATTEMPT_FLUSH_CAMEM_FOR_ALL_RECOVERY_OFFSET),
        0
        );
    }
  }

  //FAPE Enable Gen3
  SmnPrivateRegRMW (GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_FAPE_CNTL_8GT_ADDRESS),
      (UINT32)  ~(PCIE_LC_FAPE_CNTL_8GT_LC_FAPE_COEFF_MASK_8GT_MASK),
          (Engine->Type.Port.LaneEqualizationCntl.LcFapeEnable8GT << PCIE_LC_FAPE_CNTL_8GT_LC_FAPE_COEFF_MASK_8GT_OFFSET),
          0
  );

  //FAPE Enable Gen4
  SmnPrivateRegRMW (GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_FAPE_CNTL_16GT_ADDRESS),
      (UINT32)  ~(PCIE_LC_FAPE_CNTL_16GT_LC_FAPE_COEFF_MASK_16GT_MASK),
          (Engine->Type.Port.Gen4LaneEqualizationCntl.LcFapeEnable16GT << PCIE_LC_FAPE_CNTL_16GT_LC_FAPE_COEFF_MASK_16GT_OFFSET),
          0
  );

  //FAPE Enable Gen5
  SmnPrivateRegRMW (GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_FAPE_CNTL_32GT_ADDRESS),
      (UINT32)  ~(PCIE_LC_FAPE_CNTL_32GT_LC_FAPE_COEFF_MASK_32GT_MASK),
      (Engine->Type.Port.Gen5LaneEqualizationCntl.LcFapeEnable32GT << PCIE_LC_FAPE_CNTL_32GT_LC_FAPE_COEFF_MASK_32GT_OFFSET),
      0
  );
  SmnPrivateRegRMW (
      GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_TX_PORT_CTRL_1_ADDRESS),
      (UINT32) ~(PCIE_TX_PORT_CTRL_1_TX_FLUSH_TLP_DIS_MASK),
      0 << PCIE_TX_PORT_CTRL_1_TX_FLUSH_TLP_DIS_OFFSET,
      0
  );

  // Set STRAP_BIF_TPH_SUPPORTED to indicate TPH and Extended TPH are supported in the downstream port
  if (PcdGetBool (PcdAmdFabricCdma) == TRUE){
    WritePcieStrap (GnbHandle, STRAP_BIF_TPH_SUPPORTED_INDEX, 1, Wrapper->WrapId);
  }

  if(Engine->Type.Port.SrisEnableMode == 1) {
    WritePcieStrap (GnbHandle, STRAP_BIF_DESKEW_EMPTYMODE_INDEX, 0, Wrapper->WrapId);
  }

  return;
}

/*----------------------------------------------------------------------------------------*/
/**
 * Per-Wrapper Callback for wrapper configuration before bifurcation
 *
 *
 *
 * @param[in]     Wrapper   Wrapper configuration info
 * @param[in,out] Buffer    Buffer pointer
 * @param[in]     GnbHandle GnbHandle Pointer
 */
VOID
STATIC
MpioCfgBeforeReconfigWrapperCallback (
  IN      PCIe_WRAPPER_CONFIG               *Wrapper,
  IN OUT  VOID                              *Buffer,
  IN      GNB_HANDLE                        *GnbHandle
  )
{
  UINT16                 Index;
  PORT_STATE_IN_WRAPPER  PortStateInWrapper;

  PortStateInWrapper.IsCxl           = FALSE;
  PortStateInWrapper.IsPortAllocated = FALSE;

  PcieConfigRunProcForAllEnginesInWrapper (
    DESCRIPTOR_PCIE_ENGINE,
    MpioCfgBeforeReconfigCallbackAllPorts,
    (VOID *) &PortStateInWrapper,
    Wrapper
    );

  if(Wrapper->IsEarlyConfigured) {
    if(IsEarlyTrainedBmcInWrapper(Wrapper, GnbHandle)){
      EarlyTrainingMpioCfgBeforeReconfigWrapper (Wrapper, GnbHandle);
    }
    return;
  }

  PcieConfigRunProcForAllEnginesInWrapper (
    DESCRIPTOR_ALLOCATED | DESCRIPTOR_PCIE_ENGINE,
    MpioCfgBeforeReconfigCallback,
    (VOID *) &PortStateInWrapper,
    Wrapper
    );

  // All P Links (WrapId 0, 2, 5, and 7) support CXL
  if (CXL_CAPABLE_RB(GnbHandle)) {
    WritePcieStrap (
      GnbHandle,
      STRAP_BIF_CXL_APERTURE_INDEX,
      0x800 + GnbHandle->RBIndex,
      Wrapper->WrapId
      );
    if (PortStateInWrapper.IsCxl) {
      // STRAP_BIF_CXL_EN = 0b1
      WritePcieStrap (
        GnbHandle,
        STRAP_BIF_CXL_EN_INDEX,
        1,
        Wrapper->WrapId
        );

      //CXL correctable error logging
      SmnPrivateRegRMW (
            GnbHandle,
            WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_CXL_ERR_AER_CTRL_ADDRESS),
            (UINT32) ~(PCIE_CXL_ERR_AER_CTRL_RX_CXL_ERR0_LOG_AS_CIE_MASK),
            ((PcdGetBool (PcdCxlCorrectableErrorLogging)? 1 : 0) << PCIE_CXL_ERR_AER_CTRL_RX_CXL_ERR0_LOG_AS_CIE_OFFSET),
            0
           );

      //CXL uncorrectable error logging
      SmnPrivateRegRMW (
            GnbHandle,
            WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_CXL_ERR_AER_CTRL_ADDRESS),
            (UINT32) ~(PCIE_CXL_ERR_AER_CTRL_RX_CXL_ERR0_LOG_AS_UIE_MASK),
            ((PcdGetBool (PcdCxlUnCorrectableErrorLogging)? 2 : 0) << PCIE_CXL_ERR_AER_CTRL_RX_CXL_ERR0_LOG_AS_UIE_OFFSET),
            0
           );
    }
  }

  if ((PcdGetBool (PcdCfgSevSnpSupport) == TRUE) ||\
      (PcdGetBool (PcdCfgSevTioSupport) == TRUE) ||\
      (PcdGetBool (PcdPcieIdeCapSup) == TRUE)) {
    WritePcieStrap (
      GnbHandle,
      STRAP_BIF_IDE_SUPPORTED_INDEX,
      1,
      Wrapper->WrapId
    );
    SmnPrivateRegRMW (
      GnbHandle,
      WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIECORE_PCIE_RX_IDE_MISC_CTL_ADDRESS),
      (UINT32) ~(PCIE_RX_IDE_MISC_CTL_RX_IDE_BYPASS_EN_MASK),
      0 << PCIE_RX_IDE_MISC_CTL_RX_IDE_BYPASS_EN_OFFSET,
      0
      );
  } else {
    WritePcieStrap (
      GnbHandle,
      STRAP_BIF_IDE_SUPPORTED_INDEX,
      0,
      Wrapper->WrapId
    );
    WritePcieStrap (
      GnbHandle,
      STRAP_BIF_IDE_PCRC_SUPPORTED_DIS_INDEX,
      1,
      Wrapper->WrapId
    );
    SmnPrivateRegRMW (
      GnbHandle,
      WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIECORE_PCIE_RX_IDE_MISC_CTL_ADDRESS),
      (UINT32) ~(PCIE_RX_IDE_MISC_CTL_RX_IDE_BYPASS_EN_MASK),
      1 << PCIE_RX_IDE_MISC_CTL_RX_IDE_BYPASS_EN_OFFSET,
      0
      );
  }

  if((PcdGetBool (PcdCfgSevTioSupport) == TRUE)) {
    WritePcieStrap (
      GnbHandle,
      STRAP_BIF_IDE_SELECTIVE_FOR_CFG_SUPPORTED_INDEX,
      1,
      Wrapper->WrapId
    );
  }

  if(PortStateInWrapper.IsPortAllocated) {
      // CPL Abort Error
      WritePcieStrap (
        GnbHandle,
        STRAP_BIF_CPL_ABORT_ERR_EN_INDEX,
        PcdGetBool (PcdCfgAEREnable)? 1 : 0,
        Wrapper->WrapId
        );

      WritePcieStrap (
        GnbHandle,
        STRAP_PLL_CMP_FREQ_MODE_INDEX,
        3,
        Wrapper->WrapId
      );

      // Receiver Error report
      SmnPrivateRegRMW (
        GnbHandle,
        WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_HW_DEBUG_ADDRESS),
        (UINT32) ~(PCIE_HW_DEBUG_HW_13_DEBUG_MASK),
        (PcdGetBool(PcdCfgRcvErrEnable)? 1 : 0) << PCIE_HW_DEBUG_HW_13_DEBUG_OFFSET,
        0
        );

        // ACS Enablement
        for (Index = STRAP_BIF_ACS_EN_INDEX; Index <= STRAP_BIF_ACS_UPSTREAM_FORWARDING_INDEX; Index++) {
           WritePcieStrap (
                GnbHandle,
                (UINT16) Index,
                Wrapper->AcsSupport,
                Wrapper->WrapId
           );
        }

      // ARI Forwarding
      WritePcieStrap (GnbHandle, STRAP_BIF_SWUS_ARI_EN_INDEX, Wrapper->AriForwarding, Wrapper->WrapId);

      // Lane Margining
      WritePcieStrap (GnbHandle, STRAP_BIF_MARGINING_EN_INDEX, Wrapper->LaneMargining, Wrapper->WrapId);

      //Native PCIe Enclosure Management (NPEM)
      WritePcieStrap (GnbHandle, STRAP_BIF_NPEM_EN_INDEX, Wrapper->NativePCIeEnclosureManagement, Wrapper->WrapId);

      // Advanced error reporting (AER)
      WritePcieStrap (GnbHandle, STRAP_BIF_AER_EN_INDEX, Wrapper->AdvancedErrorReporting, Wrapper->WrapId);
      WritePcieStrap (GnbHandle, STRAP_BIF_SWUS_AER_EN_INDEX, Wrapper->AdvancedErrorReporting, Wrapper->WrapId);

      // ECRC Capability
      WritePcieStrap (GnbHandle, STRAP_BIF_ECRC_GEN_EN_INDEX, (((Wrapper->ECRCSupport & 0x1) == 0x1) ? 1 : 0), Wrapper->WrapId);
      WritePcieStrap (GnbHandle, STRAP_BIF_SWUS_ECRC_GEN_EN_INDEX, (((Wrapper->ECRCSupport & 0x1) == 0x1) ? 1 : 0), Wrapper->WrapId);
      WritePcieStrap (GnbHandle, STRAP_BIF_ECRC_CHECK_EN_INDEX, (((Wrapper->ECRCSupport & 0x2) == 0x2) ? 1 : 0), Wrapper->WrapId);
      WritePcieStrap (GnbHandle, STRAP_BIF_SWUS_ECRC_CHECK_EN_INDEX, (((Wrapper->ECRCSupport & 0x2) == 0x2) ? 1 : 0), Wrapper->WrapId);

      //LTR Support
      WritePcieStrap (GnbHandle, STRAP_BIF_LTR_SUPPORTED_INDEX, Wrapper->LtrSupport, Wrapper->WrapId);

      //Surprise Down Feature
      WritePcieStrap (GnbHandle, STRAP_BIF_SURPRISE_DOWN_ERR_REPORTING_CAPABLE_INDEX, PcdGetBool (PcdSurpriseDownFeature)? 1 : 0, Wrapper->WrapId);
  }
  return;
}

/**----------------------------------------------------------------------------------------*/
/**
 * Interface to configure DXIO/PCIe ports after ports are mapped and before reconfig
 *  - This function is called once for each socket
 *
 *
 *
 * @param[in]  GnbHandle      Pointer to the Silicon Descriptor for this node
 *
 * @retval    AGESA_STATUS
 */
 /*----------------------------------------------------------------------------------------*/

VOID
MpioCfgBeforeReconfig (
  IN       GNB_HANDLE       *GnbHandle
  )
{
  GNB_HANDLE                *LocalHandle;
  UINT32                    SocketId;

  IDS_HDT_CONSOLE (GNB_TRACE, "%a for Socket %d\n", __FUNCTION__, GnbHandle->SocketId);

  LocalHandle = GnbHandle;
  SocketId = GnbHandle->SocketId;
  while (LocalHandle != NULL) {
    if (LocalHandle->SocketId == SocketId) {
      IDS_HOOK(IDS_HOOK_NBIO_PCIE_TUNING, (VOID *)LocalHandle, (VOID *)NULL);
      PcieConfigRunProcForAllWrappersInNbio (
        DESCRIPTOR_ALL_WRAPPERS,
        MpioCfgBeforeReconfigWrapperCallback,
        NULL,
        LocalHandle
        );

    }
    LocalHandle = GnbGetNextHandle (LocalHandle);
  }
  IDS_HDT_CONSOLE (GNB_TRACE, "%a Exit\n", __FUNCTION__);
  return;
}


//=========================================================================================
// Timepoint after reconfig and before port training
//=========================================================================================

/*----------------------------------------------------------------------------------------*/
/**
 * Per-Engine Callback for configuration after bifurcation
 *
 *
 *
 * @param[in]     Engine  Engine configuration info
 * @param[in,out] Buffer  Buffer pointer
 * @param[in]     Pcie    PCIe configuration info
 */
VOID
STATIC
MpioCfgAfterReconfigCallback (
  IN      PCIe_ENGINE_CONFIG                *Engine,
  IN OUT  VOID                              *Buffer,
  IN      PCIe_WRAPPER_CONFIG               *Wrapper
  )
{
  GNB_HANDLE                                    *GnbHandle;
  UINT32                                        Value32;
  PCIe_DPC_STATUS_DATA                          *DpcStatusData;
  LINK_CAP_PCIERCCFG_STRUCT                     LinkCap;
  DVSEC_FLEX_BUS_PORT_CONTROL_PCIERCCFG_STRUCT  DvsecFlexBusPort;
  PCI_ADDR                                      PciDevice;
  UINT32                                        Value;
  UINT32                                        Index;
  PCIE_SDP_CTRL_STRUCT                          PcieSdpCtrl;
  UINT8                                         ForcePreset;

  GnbHandle = (GNB_HANDLE *) PcieConfigGetParentSilicon (Wrapper);


  SmnPrivateRegRead (
    GnbHandle,
    WRAP_SPACE(GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_SDP_CTRL_ADDRESS),
    &PcieSdpCtrl.Value
    );

  switch (Wrapper->WrapId) {
  case GPP_WRAP_ID:
    PcieSdpCtrl.Field.SDP_UNIT_ID_LOWER = GPP_UNIT_ID & 0x7;
    PcieSdpCtrl.Field.SDP_UNIT_ID = GPP_UNIT_ID >> 3;
    break;
  case BONUS_WRAP_ID:
    PcieSdpCtrl.Field.SDP_UNIT_ID = BONUS_UNIT_ID;
    // For 4x4, use per-port UNIT_ID
    for (Index = 0; Index < NUMBER_OF_BONUS_PORTS; Index++) {
      Value = BONUS_PORT_UNIT_ID + Index;
      SmnPrivateRegWrite (
        GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, Index, SMN_FUNC0_PCIE0NBIO0_PCIEP_SDP_CTRL_ADDRESS),
        &Value,
        GNB_REG_ACC_FLAG_S3SAVE
        );
    }
    break;
  case TUNNEL_WRAP_ID:
    PcieSdpCtrl.Field.SDP_UNIT_ID = TUNNEL_UNIT_ID;
    // For 3x3, use per-port UNIT_ID
    for (Index = 0; Index < NUMBER_OF_TUNNEL_PORTS; Index++) {
      Value = TUNNEL_PORT_UNIT_ID + Index;
      SmnPrivateRegWrite (
        GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, Index, SMN_FUNC0_PCIE0NBIO0_PCIEP_SDP_CTRL_ADDRESS),
        &Value,
        GNB_REG_ACC_FLAG_S3SAVE
        );
    }
    break;
  default:
    ASSERT (FALSE);
  }

  SmnPrivateRegWrite (
    GnbHandle,
    WRAP_SPACE(GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_SDP_CTRL_ADDRESS),
    &PcieSdpCtrl.Value,
    GNB_REG_ACC_FLAG_S3SAVE
    );

  if (((AFTER_RECONFIG_BUFFER*) Buffer)->DpcStatusData != NULL) {
    DpcStatusData = (((AFTER_RECONFIG_BUFFER*) Buffer)->DpcStatusData);
    SmnRegisterReadS (
      GnbHandle->Address.Address.Segment,
      GnbHandle->Address.Address.Bus,
      PORT_SPACE(GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_DPC_STATUS_ADDRESS),
      &Value32
      );
    GNB_DEBUG_CODE (
      IDS_HDT_CONSOLE (
        GNB_TRACE,
        "SocketId=%d, DieNumber=%d, BRIndex=%d Bus=0x%x CoreID=%d PortID=%d PCIE_DPC_STATUS(0x388) = 0x%x\n",
        GnbHandle->SocketId,
        GnbHandle->DieNumber,
        GnbHandle->RBIndex,
        GnbHandle->Address.Address.Bus,
        Wrapper->WrapId,
        (Engine->Type.Port.PortId),
        Value32
        )
      );
    if (Value32 & BIT0) {
      if (DpcStatusData->size < MAX_NUMBER_DPCSTATUS) {
        DpcStatusData->DpcStatusArray[DpcStatusData->size].SocketId = (UINT8) GnbHandle->SocketId;
        DpcStatusData->DpcStatusArray[DpcStatusData->size].DieID = (UINT8) GnbHandle->DieNumber;
        DpcStatusData->DpcStatusArray[DpcStatusData->size].RBIndex = (UINT8) GnbHandle->RBIndex;
        DpcStatusData->DpcStatusArray[DpcStatusData->size].BusNumber = (UINT8) GnbHandle->Address.Address.Bus;
        DpcStatusData->DpcStatusArray[DpcStatusData->size].PCIeCoreID = (UINT8) Wrapper->WrapId;
        DpcStatusData->DpcStatusArray[DpcStatusData->size].PCIePortID = (UINT8) (Engine->Type.Port.PortId);
        DpcStatusData->DpcStatusArray[DpcStatusData->size].DpcStatus = (UINT16) Value32;

        SmnRegisterWriteS (
          GnbHandle->Address.Address.Segment, GnbHandle->Address.Address.Bus,
          PORT_SPACE(GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_DPC_STATUS_ADDRESS),
          &Value32,
          0
          );
        DpcStatusData->size++;
      }
    }
  }

    // Gen3 EQ Search Mode
    SmnPrivateRegRMW (GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_EQ_CNTL_8GT_ADDRESS),
      (UINT32) ~(PCIE_LC_EQ_CNTL_8GT_LC_EQ_SEARCH_MODE_8GT_MASK),
      Engine->Type.Port.EqSearchMode << PCIE_LC_EQ_CNTL_8GT_LC_EQ_SEARCH_MODE_8GT_OFFSET,
      0
      );

    // Gen4 EQ Search Mode
    SmnPrivateRegRMW (GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_EQ_CNTL_16GT_ADDRESS),
      (UINT32) ~(PCIE_LC_EQ_CNTL_16GT_LC_EQ_SEARCH_MODE_16GT_MASK),
      Engine->Type.Port.EqSearchModeGen4 << PCIE_LC_EQ_CNTL_16GT_LC_EQ_SEARCH_MODE_16GT_OFFSET,
      0
      );

    // Gen5 EQ Search Mode
    SmnPrivateRegRMW (GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_EQ_CNTL_32GT_ADDRESS),
      (UINT32) ~(PCIE_LC_EQ_CNTL_32GT_LC_EQ_SEARCH_MODE_32GT_MASK),
      Engine->Type.Port.EqSearchModeGen5 << PCIE_LC_EQ_CNTL_32GT_LC_EQ_SEARCH_MODE_32GT_OFFSET,
      0
      );

    // Gen3 Bypass Phase3 EQ
    SmnPrivateRegRMW (GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_EQ_CNTL_8GT_ADDRESS),
      (UINT32) ~(PCIE_LC_EQ_CNTL_8GT_LC_BYPASS_EQ_8GT_MASK),
      Engine->Type.Port.BypassGen3EQ << PCIE_LC_EQ_CNTL_8GT_LC_BYPASS_EQ_8GT_OFFSET,
      0
      );

    // Gen4 Bypass Phase3 EQ
    SmnPrivateRegRMW (GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_EQ_CNTL_16GT_ADDRESS),
      (UINT32) ~(PCIE_LC_EQ_CNTL_16GT_LC_BYPASS_EQ_16GT_MASK),
      Engine->Type.Port.BypassGen4EQ << PCIE_LC_EQ_CNTL_16GT_LC_BYPASS_EQ_16GT_OFFSET,
      0
      );

    // Gen5 Bypass Phase3 EQ
    SmnPrivateRegRMW (GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_EQ_CNTL_32GT_ADDRESS),
      (UINT32) ~(PCIE_LC_EQ_CNTL_32GT_LC_BYPASS_EQ_32GT_MASK),
      Engine->Type.Port.BypassGen5EQ << PCIE_LC_EQ_CNTL_32GT_LC_BYPASS_EQ_32GT_OFFSET,
      0
      );
    //Preset Search Mask Gen3
    SmnPrivateRegRMW (GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_PRESET_MASK_CNTL_ADDRESS),
      (UINT32)  ~(PCIE_LC_PRESET_MASK_CNTL_LC_PRESET_MASK_8GT_MASK),
      (Engine->Type.Port.LaneEqualizationCntl.LcPresetMask8Gt << PCIE_LC_PRESET_MASK_CNTL_LC_PRESET_MASK_8GT_OFFSET),
      0
      );

    //Preset Search Mask Gen4
    SmnPrivateRegRMW (GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_PRESET_MASK_CNTL_ADDRESS),
      (UINT32)  ~(PCIE_LC_PRESET_MASK_CNTL_LC_PRESET_MASK_16GT_MASK),
      (Engine->Type.Port.Gen4LaneEqualizationCntl.LcPresetMask16Gt << PCIE_LC_PRESET_MASK_CNTL_LC_PRESET_MASK_16GT_OFFSET),
      0
    );

    //Preset Search Mask Gen5
    SmnPrivateRegRMW (GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_PRESET_MASK_CNTL_ADDRESS),
      (UINT32)  ~(PCIE_LC_PRESET_MASK_CNTL_LC_PRESET_MASK_32GT_MASK),
      (Engine->Type.Port.Gen5LaneEqualizationCntl.LcPresetMask32Gt << PCIE_LC_PRESET_MASK_CNTL_LC_PRESET_MASK_32GT_OFFSET),
      0
    );
    // Gen3 Fixed Preset
    if (Engine->Type.Port.SetGen3FixedPreset) {

      SmnPrivateRegRMW (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_EQ_CNTL_8GT_ADDRESS),
        (UINT32)  ~(PCIE_LC_EQ_CNTL_8GT_LC_FORCE_PRESET_IN_EQ_REQ_PHASE_8GT_MASK) ,
        (1 << PCIE_LC_EQ_CNTL_8GT_LC_FORCE_PRESET_IN_EQ_REQ_PHASE_8GT_OFFSET) ,
        0
        );

      SmnPrivateRegRMW (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_EQ_CNTL_8GT_ADDRESS),
        (UINT32)  ~(PCIE_LC_EQ_CNTL_8GT_LC_FORCE_PRESET_VALUE_8GT_MASK),
        (Engine->Type.Port.Gen3FixedPreset << PCIE_LC_EQ_CNTL_8GT_LC_FORCE_PRESET_VALUE_8GT_OFFSET),
        0
        );
    }

    // Gen3 Force Preset
    if(Engine->Type.Port.SetGen3ForcePreset) {

      ForcePreset = Engine->Type.Port.Gen3ForcePreset > 9 ? 0 : Engine->Type.Port.Gen3ForcePreset ; //Valid values 0-9

      SmnPrivateRegRMW (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_PCIEPORT_PCIE_LC_FORCE_COEFF_ADDRESS),
        (UINT32) ~(PCIE_LC_FORCE_COEFF_LC_FORCE_PRE_CURSOR_8GT_MASK),
        ForcePresetTable[ForcePreset].LC_FORCE_PRE_CURSOR << PCIE_LC_FORCE_COEFF_LC_FORCE_PRE_CURSOR_8GT_OFFSET,
        0
      );

      SmnPrivateRegRMW (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_PCIEPORT_PCIE_LC_FORCE_COEFF_ADDRESS),
        (UINT32) ~(PCIE_LC_FORCE_COEFF_LC_FORCE_CURSOR_8GT_MASK),
        ForcePresetTable[ForcePreset].LC_FORCE_CURSOR << PCIE_LC_FORCE_COEFF_LC_FORCE_CURSOR_8GT_OFFSET,
        0
      );

      SmnPrivateRegRMW (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_PCIEPORT_PCIE_LC_FORCE_COEFF_ADDRESS),
        (UINT32) ~(PCIE_LC_FORCE_COEFF_LC_FORCE_POST_CURSOR_8GT_MASK),
        ForcePresetTable[ForcePreset].LC_FORCE_POST_CURSOR << PCIE_LC_FORCE_COEFF_LC_FORCE_POST_CURSOR_8GT_OFFSET,
        0
      );

      SmnPrivateRegRMW (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_PCIEPORT_PCIE_LC_FORCE_COEFF_ADDRESS),
        (UINT32) ~(PCIE_LC_FORCE_COEFF_LC_FORCE_COEFF_8GT_MASK),
        1 << PCIE_LC_FORCE_COEFF_LC_FORCE_COEFF_8GT_OFFSET,
        0
      );
    }

    // Gen4 Fixed Preset
    if (Engine->Type.Port.SetGen4FixedPreset) {

      SmnPrivateRegRMW (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_EQ_CNTL_16GT_ADDRESS),
        (UINT32)  ~(PCIE_LC_EQ_CNTL_16GT_LC_FORCE_PRESET_IN_EQ_REQ_PHASE_16GT_MASK) ,
        (1 << PCIE_LC_EQ_CNTL_16GT_LC_FORCE_PRESET_IN_EQ_REQ_PHASE_16GT_OFFSET) ,
        0
        );

      SmnPrivateRegRMW (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_EQ_CNTL_16GT_ADDRESS),
        (UINT32)  ~(PCIE_LC_EQ_CNTL_16GT_LC_FORCE_PRESET_VALUE_16GT_MASK),
        (Engine->Type.Port.Gen4FixedPreset << PCIE_LC_EQ_CNTL_16GT_LC_FORCE_PRESET_VALUE_16GT_OFFSET),
        0
        );
    }

    // Gen4 Force Preset
    if(Engine->Type.Port.SetGen4ForcePreset) {

      ForcePreset = Engine->Type.Port.Gen4ForcePreset > 9 ? 0 : Engine->Type.Port.Gen4ForcePreset ; //Valid values 0-9

      SmnPrivateRegRMW (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_PCIEPORT_PCIE_LC_FORCE_COEFF2_ADDRESS),
        (UINT32) ~(PCIE_LC_FORCE_COEFF2_LC_FORCE_PRE_CURSOR_16GT_MASK),
        ForcePresetTable[ForcePreset].LC_FORCE_PRE_CURSOR << PCIE_LC_FORCE_COEFF2_LC_FORCE_PRE_CURSOR_16GT_OFFSET,
        0
      );

      SmnPrivateRegRMW (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_PCIEPORT_PCIE_LC_FORCE_COEFF2_ADDRESS),
        (UINT32) ~(PCIE_LC_FORCE_COEFF2_LC_FORCE_CURSOR_16GT_MASK),
        ForcePresetTable[ForcePreset].LC_FORCE_CURSOR << PCIE_LC_FORCE_COEFF2_LC_FORCE_CURSOR_16GT_OFFSET,
        0
      );

      SmnPrivateRegRMW (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_PCIEPORT_PCIE_LC_FORCE_COEFF2_ADDRESS),
        (UINT32) ~(PCIE_LC_FORCE_COEFF2_LC_FORCE_POST_CURSOR_16GT_MASK),
        ForcePresetTable[ForcePreset].LC_FORCE_POST_CURSOR << PCIE_LC_FORCE_COEFF2_LC_FORCE_POST_CURSOR_16GT_OFFSET,
        0
      );

      SmnPrivateRegRMW (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_PCIEPORT_PCIE_LC_FORCE_COEFF2_ADDRESS),
        (UINT32) ~(PCIE_LC_FORCE_COEFF2_LC_FORCE_COEFF_16GT_MASK),
        1 << PCIE_LC_FORCE_COEFF2_LC_FORCE_COEFF_16GT_OFFSET,
        0
      );
    }

    // Gen5 Fixed Preset
    if (Engine->Type.Port.SetGen5FixedPreset) {

      SmnPrivateRegRMW (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_EQ_CNTL_32GT_ADDRESS),
        (UINT32)  ~(PCIE_LC_EQ_CNTL_32GT_LC_FORCE_PRESET_IN_EQ_REQ_PHASE_32GT_MASK) ,
        (1 << PCIE_LC_EQ_CNTL_32GT_LC_FORCE_PRESET_IN_EQ_REQ_PHASE_32GT_OFFSET) ,
        0
        );

      SmnPrivateRegRMW (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_EQ_CNTL_32GT_ADDRESS),
        (UINT32)  ~(PCIE_LC_EQ_CNTL_32GT_LC_FORCE_PRESET_VALUE_32GT_MASK),
        (Engine->Type.Port.Gen5FixedPreset << PCIE_LC_EQ_CNTL_32GT_LC_FORCE_PRESET_VALUE_32GT_OFFSET),
        0
        );
    }

    // Gen5 Force Preset
    if(Engine->Type.Port.SetGen5ForcePreset) {

      ForcePreset = Engine->Type.Port.Gen5ForcePreset > 9 ? 0 : Engine->Type.Port.Gen5ForcePreset ; //Valid values 0-9

      SmnPrivateRegRMW (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_PCIEPORT_PCIE_LC_FORCE_COEFF3_ADDRESS),
        (UINT32) ~(PCIE_LC_FORCE_COEFF3_LC_FORCE_PRE_CURSOR_32GT_MASK),
        ForcePresetTable[ForcePreset].LC_FORCE_PRE_CURSOR << PCIE_LC_FORCE_COEFF3_LC_FORCE_PRE_CURSOR_32GT_OFFSET,
        0
      );

      SmnPrivateRegRMW (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_PCIEPORT_PCIE_LC_FORCE_COEFF3_ADDRESS),
        (UINT32) ~(PCIE_LC_FORCE_COEFF3_LC_FORCE_CURSOR_32GT_MASK),
        ForcePresetTable[ForcePreset].LC_FORCE_CURSOR << PCIE_LC_FORCE_COEFF3_LC_FORCE_CURSOR_32GT_OFFSET,
        0
      );

      SmnPrivateRegRMW (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_PCIEPORT_PCIE_LC_FORCE_COEFF3_ADDRESS),
        (UINT32) ~(PCIE_LC_FORCE_COEFF3_LC_FORCE_POST_CURSOR_32GT_MASK),
        ForcePresetTable[ForcePreset].LC_FORCE_POST_CURSOR << PCIE_LC_FORCE_COEFF3_LC_FORCE_POST_CURSOR_32GT_OFFSET,
        0
      );

      SmnPrivateRegRMW (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_PCIEPORT_PCIE_LC_FORCE_COEFF3_ADDRESS),
        (UINT32) ~(PCIE_LC_FORCE_COEFF3_LC_FORCE_COEFF_32GT_MASK),
        1 << PCIE_LC_FORCE_COEFF3_LC_FORCE_COEFF_32GT_OFFSET,
        0
      );
    }

    // Multi Upstream Auto Speed Change
    SmnPrivateRegRMW (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_SPEED_CNTL2_ADDRESS),
        (UINT32) ~(PCIE_LC_SPEED_CNTL2_LC_MULT_UPSTREAM_AUTO_SPD_CHNG_EN_MASK),
        Engine->Type.Port.PortFeatures.AutoSpdChngEn << PCIE_LC_SPEED_CNTL2_LC_MULT_UPSTREAM_AUTO_SPD_CHNG_EN_OFFSET,
        0
        );

    // Multi Auto Speed Change On Last Rate
    SmnPrivateRegRMW (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_CNTL3_ADDRESS),
        (UINT32) ~(PCIE_LC_CNTL3_LC_MULT_AUTO_SPD_CHG_ON_LAST_RATE_MASK),
        ((PcdGetBool (PcdLcMultAutoSpdChgOnLastRateEnable)? 1 : 0) << PCIE_LC_CNTL3_LC_MULT_AUTO_SPD_CHG_ON_LAST_RATE_OFFSET),
        0
        );

    // BIFC.LINK_CNTL_32GT.EQ_BYPASS_TO_HIGHEST_RATE_DIS = !(AdvertiseEqToHiRate)
    SmnRegisterRMWS (
      GnbHandle->Address.Address.Segment,
      GnbHandle->Address.Address.Bus,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_LINK_CNTL_32GT_ADDRESS),
      (UINT32) ~(LINK_CNTL_32GT_EQ_BYPASS_TO_HIGHEST_RATE_DIS_MASK),
      !(Engine->Type.Port.Gen5LaneEqualizationCntl.AdvertiseEqToHiRate) << LINK_CNTL_32GT_EQ_BYPASS_TO_HIGHEST_RATE_DIS_OFFSET,
      0
      );

  // CXL Alternate Protocol Support
  if (Engine->Type.Port.CxlControl != 0) {
    // BIFC.AP_CNTL.AP_NEGOTIATION_GLOBAL_EN = 0b1
    SmnRegisterRMWS (
      GnbHandle->Address.Address.Segment,
      GnbHandle->Address.Address.Bus,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_AP_CNTL_ADDRESS),
      (UINT32) ~(AP_CNTL_AP_NEGOTIATION_GLOBAL_EN_MASK),
      1 << AP_CNTL_AP_NEGOTIATION_GLOBAL_EN_OFFSET,
      0
      );

    // BIFC.LINK_CNTL_32GT.MODIFIED_TS_USAGE_MODE_SELECTED = 0x2
    SmnRegisterRMWS (
      GnbHandle->Address.Address.Segment,
      GnbHandle->Address.Address.Bus,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_LINK_CNTL_32GT_ADDRESS),
      (UINT32) ~(LINK_CNTL_32GT_MODIFIED_TS_USAGE_MODE_SEL_MASK),
      2 << LINK_CNTL_32GT_MODIFIED_TS_USAGE_MODE_SEL_OFFSET,
      0
      );

    // BIFC.DVSEC_FLEX_BUS_PORT_CONTROL.MEM_EN = 0x1
    // SmnRegisterRMWS (
      // GnbHandle->Address.Address.Segment,
      // GnbHandle->Address.Address.Bus,
      // PORT_SPACE (GnbHandle,
                  // Wrapper,
                  // (Engine->Type.Port.PortId),
                  // SMN_FUNC0_PCIE0NBIO0_DVSEC_FLEX_BUS_PORT_CONTROL_ADDRESS),
      // (UINT32) ~(DVSEC_FLEX_BUS_PORT_CONTROL_MEM_EN_MASK | DVSEC_FLEX_BUS_PORT_CONTROL_CXL2p0_ENABLE_MASK),
      // (1 << DVSEC_FLEX_BUS_PORT_CONTROL_MEM_EN_OFFSET) | (1 << DVSEC_FLEX_BUS_PORT_CONTROL_CXL2p0_ENABLE_OFFSET),
      // 0
      // );
      IDS_HDT_CONSOLE ( GNB_TRACE, "Writing to DvsecFlexBusPort via MMIO, so MPIO will track the traps\n");
      PciDevice = Engine->Type.Port.Address;
      GnbLibPciRead (PciDevice.AddressValue | (SMN_FUNC0_PCIE0NBIO0_DVSEC_FLEX_BUS_PORT_CONTROL_ADDRESS & 0xFFF),
        AccessWidth16,
        &(DvsecFlexBusPort.Value),
        NULL
      );

      DvsecFlexBusPort.Field.MEM_EN = 1;
      DvsecFlexBusPort.Field.CXL2p0_ENABLE = 1;

      GnbLibPciWrite (PciDevice.AddressValue | (SMN_FUNC0_PCIE0NBIO0_DVSEC_FLEX_BUS_PORT_CONTROL_ADDRESS & 0xFFF),
        AccessWidth16,
        &(DvsecFlexBusPort.Value),
        NULL
      );


    // BIFC.DVSEC_FLEX_BUS_PORT_CONTROL.CACHE_EN = 0x1 to enable CXL.cache
      GnbLibPciRMW (PciDevice.AddressValue | PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_DVSEC_FLEX_BUS_PORT_CONTROL_ADDRESS),
        AccessWidth16,
        (UINT32) ~(DVSEC_FLEX_BUS_PORT_CONTROL_CACHE_EN_MASK),
        1 << DVSEC_FLEX_BUS_PORT_CONTROL_CACHE_EN_OFFSET,
        NULL
      );
    SmnRegisterReadS (
      GnbHandle->Address.Address.Segment,
      GnbHandle->Address.Address.Bus,
      PORT_SPACE(GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_LINK_CAP_ADDRESS),
      &LinkCap.Value
      );

    GnbLibPciRMW (PciDevice.AddressValue | PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_DVSEC_FLEX_BUS_PORT_CONTROL_ADDRESS),
        AccessWidth16,
        (UINT32) ~(DVSEC_FLEX_BUS_PORT_CONTROL_CXL_SYNC_HDR_BYPASS_EN_MASK),
        ((PcdGetBool (PcdSyncHeaderByPass)? 1 : 0) << DVSEC_FLEX_BUS_PORT_CONTROL_CXL_SYNC_HDR_BYPASS_EN_OFFSET),
        NULL
      );
  }

  //
  // Following registers are labeled as CXL in PPR but should be applied to PCIE and CXL devices
  //

  // QosNormalLimit
  SmnPrivateRegRMW (
    GnbHandle,
    PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId),
    SMN_FUNC0_PCIE0NBIO0_PCIE_CXL_QOS_CTRL1_ADDRESS),
    (UINT32) ~(PCIE_CXL_QOS_CTRL1_CXL_QOS_NORM_LIMIT_MASK),
    (PcdGet16 (PcdCxlQosNormalLimit) << PCIE_CXL_QOS_CTRL1_CXL_QOS_NORM_LIMIT_OFFSET),
    0
    );

  // QosHighLimit
  SmnPrivateRegRMW (
    GnbHandle,
    PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId),
    SMN_FUNC0_PCIE0NBIO0_PCIE_CXL_QOS_CTRL1_ADDRESS),
    (UINT32) ~(PCIE_CXL_QOS_CTRL1_CXL_QOS_HIGH_LIMIT_MASK),
    (PcdGet16 (PcdCxlQosHighLimit) << PCIE_CXL_QOS_CTRL1_CXL_QOS_HIGH_LIMIT_OFFSET),
    0
    );

  // QosTimerLimit
  SmnPrivateRegRMW (
    GnbHandle,
    PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId),
    SMN_FUNC0_PCIE0NBIO0_PCIE_CXL_QOS_CTRL2_ADDRESS),
    (UINT32) ~(PCIE_CXL_QOS_CTRL2_CXL_QOS_TIMER_LIMIT_MASK),
    (PcdGet16 (PcdCxlQosTimerLimit) << PCIE_CXL_QOS_CTRL2_CXL_QOS_TIMER_LIMIT_OFFSET),
    0
    );

  // QosSchedGap
  SmnPrivateRegRMW (
    GnbHandle,
    PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId),
    SMN_FUNC0_PCIE0NBIO0_PCIE_CXL_QOS_CTRL2_ADDRESS),
    (UINT32) ~(PCIE_CXL_QOS_CTRL2_CXL_QOS_SCHEDULING_GAP_MASK),
    (PcdGet16 (PcdCxlQosSchedGap) << PCIE_CXL_QOS_CTRL2_CXL_QOS_SCHEDULING_GAP_OFFSET),
    0
    );

  // QosVariableGap
  SmnPrivateRegRMW (
    GnbHandle,
    PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId),
    SMN_FUNC0_PCIE0NBIO0_PCIE_CXL_QOS_CTRL3_ADDRESS),
    (UINT32) ~(PCIE_CXL_QOS_CTRL3_CXL_QOS_USE_VARIABLE_GAP_MASK),
    ((PcdGetBool (PcdCxlQosVariableGap)? 1 : 0) << PCIE_CXL_QOS_CTRL3_CXL_QOS_USE_VARIABLE_GAP_OFFSET),
    0
    );

  // QosRdspIncMode
  SmnPrivateRegRMW (
    GnbHandle,
    PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId),
    SMN_FUNC0_PCIE0NBIO0_PCIE_CXL_QOS_CTRL3_ADDRESS),
    (UINT32) ~(PCIE_CXL_QOS_CTRL3_CXL_QOS_RDRSP_INC_MODE_MASK),
    ((PcdGetBool (PcdCxlQosRdspIncMode)? 1 : 0) <<  PCIE_CXL_QOS_CTRL3_CXL_QOS_RDRSP_INC_MODE_OFFSET),
    0
    );

  // QosTimerDecNum
  SmnPrivateRegRMW (
    GnbHandle,
    PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId),
    SMN_FUNC0_PCIE0NBIO0_PCIE_CXL_QOS_CTRL3_ADDRESS),
    (UINT32) ~(PCIE_CXL_QOS_CTRL3_CXL_QOS_TIMER_DEC_NUM_MASK),
    (PcdGet8 (PcdCxlQosTimerDecNum) << PCIE_CXL_QOS_CTRL3_CXL_QOS_TIMER_DEC_NUM_OFFSET),
    0
    );

  // QosRdspIncNum
  SmnPrivateRegRMW (
    GnbHandle,
    PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId),
    SMN_FUNC0_PCIE0NBIO0_PCIE_CXL_QOS_CTRL3_ADDRESS),
    (UINT32) ~(PCIE_CXL_QOS_CTRL3_CXL_QOS_RDRSP_INC_NUM_MASK),
    (PcdGet8 (PcdCxlQosRdspIncNum) << PCIE_CXL_QOS_CTRL3_CXL_QOS_RDRSP_INC_NUM_OFFSET),
    0
    );

  // QosWrrspIncNum
  SmnPrivateRegRMW (
    GnbHandle,
    PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId),
    SMN_FUNC0_PCIE0NBIO0_PCIE_CXL_QOS_CTRL3_ADDRESS),
    (UINT32) ~(PCIE_CXL_QOS_CTRL3_CXL_QOS_WRRSP_INC_NUM_MASK),
    (PcdGet8 (PcdCxlQosWrrspIncNum) << PCIE_CXL_QOS_CTRL3_CXL_QOS_WRRSP_INC_NUM_OFFSET),
    0
    );

  return;
}

/*----------------------------------------------------------------------------------------*/
/**
 * Per-Wrapper Callback for configuration after bifurcation
 *
 *
 *
 * @param[in]     Wrapper   Wrapper configuration info
 * @param[in,out] Buffer    Buffer pointer
 * @param[in]     GnbHandle GnbHandle Pointer
 */
VOID
STATIC
MpioCfgAfterReconfigWrapperCallback (
  IN      PCIe_WRAPPER_CONFIG               *Wrapper,
  IN OUT  VOID                              *Buffer,
  IN      GNB_HANDLE                        *GnbHandle
  )
{
  UINT16    Index;
  SOC_LOGICAL_ID            LogicalId;
  EFI_STATUS                Status;

  if(Wrapper->IsEarlyConfigured && (IsEarlyTrainedBmcInWrapper(Wrapper, GnbHandle) == FALSE)) {
    return;
  }

  ((AFTER_RECONFIG_BUFFER*) Buffer)->IsPortActive    = FALSE;

  PcieConfigRunProcForAllEnginesInWrapper (DESCRIPTOR_ALLOCATED | DESCRIPTOR_PCIE_ENGINE, MpioCfgAfterReconfigCallback, Buffer, Wrapper);

  if (PcdGetBool(PcdAmdRxMarginEnabled)) {
    SmnPrivateRegRMW (
      GnbHandle,
      WRAP_SPACE(GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_RXMARGIN_CONTROL_CAPABILITIES_ADDRESS),
      (UINT32) ~(PCIE_RXMARGIN_CONTROL_CAPABILITIES_M_INDLEFTRIGHTTIMING_MASK),
      (0x1 << PCIE_RXMARGIN_CONTROL_CAPABILITIES_M_INDLEFTRIGHTTIMING_OFFSET),
      0
      );

    SmnPrivateRegRMW (
      GnbHandle,
      WRAP_SPACE(GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_RXMARGIN_1_SETTINGS_ADDRESS),
      (UINT32) ~(PCIE_RXMARGIN_1_SETTINGS_M_NUMTIMINGSTEPS_MASK | PCIE_RXMARGIN_1_SETTINGS_M_MAXTIMINGOFFSET_MASK),
      (0x10 << PCIE_RXMARGIN_1_SETTINGS_M_NUMTIMINGSTEPS_OFFSET) |
      (0x19 << PCIE_RXMARGIN_1_SETTINGS_M_MAXTIMINGOFFSET_OFFSET),
      0
      );

    SmnPrivateRegRMW (
      GnbHandle,
      WRAP_SPACE(GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_RXMARGIN_2_SETTINGS_ADDRESS),
      (UINT32) ~(PCIE_RXMARGIN_2_SETTINGS_M_MAXLANES_MASK),
      (0xF << PCIE_RXMARGIN_2_SETTINGS_M_MAXLANES_OFFSET),
      0
      );

  }

  if (PcdGetBool(PcdMaster7bitSteeringTag)) {
    SmnPrivateRegRMW (
      GnbHandle,
      WRAP_SPACE(GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_MST_CTRL_2_ADDRESS),
      (UINT32) ~(PCIE_MST_CTRL_2_CI_MST_7BIT_ST_TAG_EN_MASK),
      (0x1 << PCIE_MST_CTRL_2_CI_MST_7BIT_ST_TAG_EN_OFFSET),
      0
      );
    SmnPrivateRegRMW (
      GnbHandle,
      WRAP_SPACE(GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_RX_CNTL4_ADDRESS),
      (UINT32) ~(PCIE_RX_CNTL4_RX_7BIT_ST_TAG_EN_MASK),
      (0x1 << PCIE_RX_CNTL4_RX_7BIT_ST_TAG_EN_OFFSET),
      0
      );
  }

 //There is no strap for STRAP_IDE_TEE_LIMITED_STREAM_SUPPORTED
  //Need to set all Nx individually in a loop
  if ((PcdGetBool (PcdCfgSevTioSupport) == TRUE) &&\
      (IsEarlyTrainedBmcInWrapper(Wrapper, GnbHandle) == FALSE)) {
    for (Index = 0; Index < 9; Index +=2)  {
    // PRIV_PCIE_IDE_CAP 16Bits data. Used 32 bits access for both odd even num.
      SmnPrivateRegRMW (
        GnbHandle,
        WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIE0_N0NBIO0_PRIV_PCIE_IDE_CAP_ADDRESS) + (Index * 2),
        (UINT32) ~(PRIV_PCIE_IDE_CAP_STRAP_IDE_TEE_LIMITED_STREAM_SUPPORTED_MASK |
                  ((Index < 8 ? PRIV_PCIE_IDE_CAP_STRAP_IDE_TEE_LIMITED_STREAM_SUPPORTED_MASK : 0) << 16)),
        (Index < 8 ? 0x10001 : 1) << PRIV_PCIE_IDE_CAP_STRAP_IDE_TEE_LIMITED_STREAM_SUPPORTED_OFFSET,
        0
        );
    }
  }
  /*if (PcdGetBool(PcdCfgNbioCTOtoSC)) {
    SmnPrivateRegRMW (
      GnbHandle,
      WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_CI_CNTL_ADDRESS),
      (UINT32) ~(PCIE_CI_CNTL_RX_RCB_RC_CTO_TO_SC_IN_LINK_DOWN_EN_MASK),
      1 << PCIE_CI_CNTL_RX_RCB_RC_CTO_TO_SC_IN_LINK_DOWN_EN_OFFSET,
      0
      );
  }

  if (PcdGetBool(PcdCfgNbioCTOIgnoreError)) {
    SmnPrivateRegRMW (
      GnbHandle,
      WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_CI_CNTL_ADDRESS),
      (UINT32) ~(PCIE_CI_CNTL_RX_RCB_RC_CTO_IGNORE_ERR_IN_LINK_DOWN_EN_MASK),
      (UINT32)  (1 << PCIE_CI_CNTL_RX_RCB_RC_CTO_IGNORE_ERR_IN_LINK_DOWN_EN_OFFSET),
      0
      );
}*/

  Status = PcieGetLogicalId(&LogicalId);
  if(!EFI_ERROR (Status)){
    if((LogicalId.Revision & (AMD_REV_F1A_BRH_Cx)) && (Wrapper->WrapId == 0)){
      SmnPrivateRegRMW (
        GnbHandle,
        WRAP_SPACE(GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_P_CNTL_ADDRESS),
        (UINT32) ~(PCIE_P_CNTL_P_ALWAYS_USE_FAST_TXCLK_MASK),
        (0x1 << PCIE_P_CNTL_P_ALWAYS_USE_FAST_TXCLK_OFFSET),
        0
      );
      SmnPrivateRegRMW (
        GnbHandle,
        WRAP_SPACE(GnbHandle, Wrapper, SMN_FUNC0_PCIE0NBIO0_PCIEP_HW_DEBUG_LC_ADDRESS),
        (UINT32) ~(PCIEP_HW_DEBUG_LC_HW_09_DEBUG_LC_MASK),
        (0x1 << PCIEP_HW_DEBUG_LC_HW_09_DEBUG_LC_OFFSET),
        0
      );
      SmnPrivateRegRMW (
        GnbHandle,
        WRAP_SPACE(GnbHandle, Wrapper, SMN_FUNC1_PCIE0NBIO0_PCIEP_HW_DEBUG_LC_ADDRESS),
        (UINT32) ~(PCIEP_HW_DEBUG_LC_HW_10_DEBUG_LC_MASK),
        (0x1 << PCIEP_HW_DEBUG_LC_HW_10_DEBUG_LC_OFFSET),
        0
      );
      SmnPrivateRegRMW (
        GnbHandle,
        WRAP_SPACE(GnbHandle, Wrapper, SMN_FUNC2_PCIE0NBIO0_PCIEP_HW_DEBUG_LC_ADDRESS),
        (UINT32) ~(PCIEP_HW_DEBUG_LC_HW_10_DEBUG_LC_MASK),
        (0x1 << PCIEP_HW_DEBUG_LC_HW_10_DEBUG_LC_OFFSET),
        0
      );
      SmnPrivateRegRMW (
        GnbHandle,
        WRAP_SPACE(GnbHandle, Wrapper, SMN_FUNC3_PCIE0NBIO0_PCIEP_HW_DEBUG_LC_ADDRESS),
        (UINT32) ~(PCIEP_HW_DEBUG_LC_HW_11_DEBUG_LC_MASK),
        (0x1 << PCIEP_HW_DEBUG_LC_HW_11_DEBUG_LC_OFFSET),
        0
      );
      SmnPrivateRegRMW (
        GnbHandle,
        WRAP_SPACE(GnbHandle, Wrapper, SMN_FUNC0_PCIE0NBIO0_PCIEP_HW_DEBUG_LC_ADDRESS),
        (UINT32) ~(PCIEP_HW_DEBUG_LC_HW_05_DEBUG_LC_MASK),
        (0x1 << PCIEP_HW_DEBUG_LC_HW_05_DEBUG_LC_OFFSET),
        0
      );
      SmnPrivateRegRMW (
        GnbHandle,
        WRAP_SPACE(GnbHandle, Wrapper, SMN_FUNC1_PCIE0NBIO0_PCIEP_HW_DEBUG_LC_ADDRESS),
        (UINT32) ~(PCIEP_HW_DEBUG_LC_HW_05_DEBUG_LC_MASK),
        (0x1 << PCIEP_HW_DEBUG_LC_HW_05_DEBUG_LC_OFFSET),
        0
      );
    }
  }


  return;
}

/**----------------------------------------------------------------------------------------*/
/**
 * Interface to configure DXIO/PCIe ports after reconfig and before training
 *  - This function is called once for each socket
 *
 *
 *
 * @param[in]  GnbHandle      Pointer to the Silicon Descriptor for this node
 *
 * @retval    AGESA_STATUS
 */
 /*----------------------------------------------------------------------------------------*/

VOID
MpioCfgAfterReconfig (
  IN       GNB_HANDLE       *GnbHandle
  )
{
  EFI_STATUS                      Status;
  PEI_AMD_NBIO_PCIE_DPCSTATUS_PPI *DpcStatusPpi;
  CONST EFI_PEI_SERVICES          **PeiServices;
  GNB_HANDLE                      *LocalHandle;
  UINT32                          SocketId;
  AFTER_RECONFIG_BUFFER           AfterReconfigBuffer;

  IDS_HDT_CONSOLE (GNB_TRACE, "%a for Socket %d\n", __FUNCTION__, GnbHandle->SocketId);
  AfterReconfigBuffer.DpcStatusData = NULL;
  DpcStatusPpi = NULL;
  PeiServices = GetPeiServicesTablePointer();

  Status = (*PeiServices)->LocatePpi (PeiServices, &gAmdNbioPcieDpcStatusPpiGuid, 0, NULL, (VOID **)&DpcStatusPpi);
  if (!EFI_ERROR (Status)) {
    DpcStatusPpi->GetDpcStatus (DpcStatusPpi, (PCIe_DPC_STATUS_DATA **)&(AfterReconfigBuffer.DpcStatusData));
  }
  LocalHandle = GnbHandle;
  SocketId = GnbHandle->SocketId;
  while (LocalHandle != NULL) {
    if (LocalHandle->SocketId == SocketId) {
      PcieConfigRunProcForAllWrappersInNbio (
        DESCRIPTOR_ALL_WRAPPERS,
        MpioCfgAfterReconfigWrapperCallback,
        &AfterReconfigBuffer,
        LocalHandle
        );
    }
    LocalHandle = GnbGetNextHandle (LocalHandle);
  }
  IDS_HDT_CONSOLE (GNB_TRACE, "%a Exit\n", __FUNCTION__);
  return;
}

//=========================================================================================
// Timepoint before MPIO firmware begins training (after reset deasserts)
//=========================================================================================

/**----------------------------------------------------------------------------------------*/
/**
 * Interface to configure DXIO/PCIe ports after ports are reconfig and resets deasserted
 *  - This function is called once for each socket
 *
 *
 *
 * @param[in]  GnbHandle      Pointer to the Silicon Descriptor for this node
 *
 * @retval    AGESA_STATUS
 */
 /*----------------------------------------------------------------------------------------*/

VOID
MpioCfgBeforeTraining (
  IN       GNB_HANDLE       *GnbHandle
  )
{
  return;
}

/**----------------------------------------------------------------------------------------*/
/**
 * Interface to perform Early Link Authentication
 *  - This function is called once for each socket
 *
 *
 *
 * @param[in]  GnbHandle      Pointer to the Silicon Descriptor for this node
 *
 * @retval    AGESA_STATUS
 */
 /*----------------------------------------------------------------------------------------*/

VOID
MpioCfgEarlyLinkAuthentication (
  IN       GNB_HANDLE       *GnbHandle,
  IN      PCI_ADDR         EarlyLinkAddress
  )
{
  return;
}

//=========================================================================================
// Timepoint before DXIO firmware intialization begins
//=========================================================================================

/**----------------------------------------------------------------------------------------*/
/**
 * Configuration Timepoint before DXIO firmware initialization starts
 *
 *
 *
 * @param[in]  Pcie                 Pointer silicon complex descriptor
 * @param[in]  ComplexDescriptor    Pointer to platform complex descriptor
 *
 *-----------------------------------------------------------------------------------------*/


VOID
DxioCfgBeforeDxioInit (
  IN      PCIe_PLATFORM_CONFIG      *Pcie,
  IN      DXIO_COMPLEX_DESCRIPTOR   *ComplexDescriptor
  )
{
  GNB_HANDLE                *GnbHandle;
  DXIO_COMPLEX_DESCRIPTOR   *LocalDescriptor;

  LocalDescriptor = ComplexDescriptor;
  while (LocalDescriptor != NULL) {
    IDS_HDT_CONSOLE (GNB_TRACE, "%a for Socket %d\n", __FUNCTION__, LocalDescriptor->SocketId);
    DxioManageTopology (LocalDescriptor);
    DxioTopologyWorkarounds (LocalDescriptor);

    GnbHandle = NbioGetHandle (Pcie);
    while (GnbHandle != NULL) {
      if (GnbHandle->SocketId == LocalDescriptor->SocketId) {
        IDS_HOOK(IDS_HOOK_NBIO_PCIE_USER_CONFIG, (VOID *)GnbHandle, (VOID *)LocalDescriptor);
      }
      GnbHandle = GnbGetNextHandle (GnbHandle);
    }
    LocalDescriptor = PcieConfigGetNextDataDescriptor (LocalDescriptor);
  }
  return;
}

//=========================================================================================
// Timepoint after DXIO firmware initialization completes
//=========================================================================================


/**----------------------------------------------------------------------------------------*/
/**
 * PCIE interface to configure register setting after Dxio init done
 *
 *
 *
 * @param[in]  GnbHandle      Pointer to the Silicon Descriptor for this node
 *
 * @retval    AGESA_STATUS
 */
 /*----------------------------------------------------------------------------------------*/

VOID
SubsystemIdSetting (
  IN       GNB_HANDLE       *GnbHandle
  )
{
  UINT32                        Value;
  UINT32                        SubsystemDeviceId;
  UINT32                        SubsystemVendorId;
  UINT32                        Address;
  PCIe_WRAPPER_CONFIG           *Wrapper;
  PCIe_ENGINE_CONFIG            *Engine;

  IDS_HDT_CONSOLE (GNB_TRACE,
                   "%a Enter for Socket %d RB %d\n",
                   __FUNCTION__,
                   GnbHandle->SocketId,
                   GnbHandle->RBIndex
                   );

  // NB ADAPTER
  Value = PcdGet32 (PcdCfgNbioSsid);
  if (Value != 0) {
    IDS_HDT_CONSOLE (GNB_TRACE, "PcdCfgNbioSsid = %x\n", Value);
    Address = BIG_IOHC (GnbHandle) ? SMN_IOHUB0NBIO0_NB_ADAPTER_ID_W_ADDRESS : SMN_IOHUB1NBIO0_NB_ADAPTER_ID_W_ADDRESS;
    SmnRegisterWriteS (
      GnbHandle->Address.Address.Segment,
      GnbHandle->Address.Address.Bus,
      NBIO_SPACE (GnbHandle, Address),
      &Value,
      0
      );
  }

  // PCIERCCFG Adapter
  SubsystemDeviceId = (UINT32) PcdGet16 (PcdAmdPcieSubsystemDeviceID);
  SubsystemVendorId = (UINT32) PcdGet16 (PcdAmdPcieSubsystemVendorID);
  Value = (SubsystemDeviceId << 16) | SubsystemVendorId;
  if (Value != 0) {
    IDS_HDT_CONSOLE (GNB_TRACE, "Root Port SSID = %x\n", Value);
    Wrapper = PcieConfigGetChildWrapper (GnbHandle);
    while (Wrapper != NULL) {
      Engine = PcieConfigGetChildEngine (Wrapper);
      while (Engine != NULL) {
        SmnPrivateRegRMW (
          GnbHandle,
          PORT_SPACE (GnbHandle, Wrapper, Engine->Type.Port.PortId, SMN_FUNC0_PCIE0NBIO0_ADAPTER_ID_W_ADDRESS),
          (UINT32) ~(ADAPTER_ID_W_SUBSYSTEM_VENDOR_ID_MASK | ADAPTER_ID_W_SUBSYSTEM_ID_MASK),
          (SubsystemVendorId << ADAPTER_ID_W_SUBSYSTEM_VENDOR_ID_OFFSET) | (SubsystemDeviceId << ADAPTER_ID_W_SUBSYSTEM_ID_OFFSET),
          0
        );
        Engine = PcieLibGetNextDescriptor (Engine);
      }
      Wrapper = PcieLibGetNextDescriptor (Wrapper);
    }
  }

  if (BIG_IOHC (GnbHandle)) {
    // IOMMU
    Value = PcdGet32 (PcdCfgIommuSsid);
    if (Value != 0) {
      IDS_HDT_CONSOLE (GNB_TRACE, "PcdCfgIommuSsid = %x\n", Value);
      SmnRegisterWriteS (
        GnbHandle->Address.Address.Segment,
        GnbHandle->Address.Address.Bus,
        NBIO_SPACE (GnbHandle, SMN_IOMMU_ADAPTER_ID_W_ADDRESS),
        &Value,
        0
        );
    }

  // NBIF Dummy Functions
    Value = PcdGet32 (PcdCfgNbifF0Ssid);
    if (Value != 0) {
      IDS_HDT_CONSOLE (GNB_TRACE, "PcdCfgNbifF0Ssid = %x\n", Value);
      SmnRegisterWriteS (
        GnbHandle->Address.Address.Segment,
        GnbHandle->Address.Address.Bus,
        NBIO_SPACE (GnbHandle, SMN_DEV0_FUNC0_NBIF0NBIO0_ADAPTER_ID_W_ADDRESS),
        &Value,
        0
        );
    }
  }

  if (GnbHandle->RBIndex == 0) {
    // PSPCCP
    Value = PcdGet32 (PcdCfgPspccpSsid);
    if (Value != 0) {
      IDS_HDT_CONSOLE (GNB_TRACE, "PcdCfgPspccpSsid = %x\n", Value);
      SmnRegisterWriteS (
        GnbHandle->Address.Address.Segment,
        GnbHandle->Address.Address.Bus,
        NBIO_SPACE (GnbHandle, SMN_DEV0_FUNC5_NBIF1NBIO0_ADAPTER_ID_W_ADDRESS), &Value, 0);
    }

  // NBIF2 Dummy Functions
    Value = PcdGet32 (PcdCfgNbifF0Ssid);
    if (Value != 0) {
      SmnRegisterWriteS (
        GnbHandle->Address.Address.Segment,
        GnbHandle->Address.Address.Bus,
        NBIO_SPACE (GnbHandle, SMN_DEV0_FUNC0_NBIF2NBIO0_ADAPTER_ID_W_ADDRESS),
        &Value,
        0
        );
      SmnRegisterWriteS (
        GnbHandle->Address.Address.Segment,
        GnbHandle->Address.Address.Bus,
        NBIO_SPACE (GnbHandle, SMN_DEV0_FUNC0_NBIF2NBIO1_ADAPTER_ID_W_ADDRESS),
        &Value,
        0
        );
    }
  }

  IDS_HDT_CONSOLE (GNB_TRACE, "%a Exit\n", __FUNCTION__);
  return;
}

/**----------------------------------------------------------------------------------------*/
/**
 * PCIE interface to configure  setting after Dxio init done
 *
 *
 * @param[in]  GnbHandle      Pointer to the Silicon Descriptor for this node
 *
 */
 /*----------------------------------------------------------------------------------------*/

#if 0
STATIC
VOID
GetCcdInfo (
    IN       UINT32  *CcdBitfield
  )
{
  UINT32 ApobInstanceId;
  UINT32 Socket;
  UINT32 Index;
  APOB_CCD_LOGICAL_TO_PHYSICAL_MAP_TYPE_STRUCT  ApobCcdLogToPhysMap;
  APOB_TYPE_HEADER                              *ApobEntry;

  Socket = 0;
  ApobInstanceId = Socket;
  AmdPspGetApobEntryInstance (APOB_CCX, APOB_CCD_LOGICAL_TO_PHYSICAL_MAP_TYPE, ApobInstanceId, FALSE, &ApobEntry);
  CopyMem (&ApobCcdLogToPhysMap, ApobEntry, sizeof (APOB_CCD_LOGICAL_TO_PHYSICAL_MAP_TYPE_STRUCT));

  for (Index = 0;Index < MAX_CCDS_PER_IOD; Index++) {
    if (ApobCcdLogToPhysMap.CcdMap[Index].PhysCcdNumber != CCX_NOT_PRESENT) {
      IDS_HDT_CONSOLE (GNB_TRACE, "Ccd: %d\n",
          ApobCcdLogToPhysMap.CcdMap[Index].PhysCcdNumber);
      *CcdBitfield |= (1 << ApobCcdLogToPhysMap.CcdMap[Index].PhysCcdNumber);
    } else {
      break;
    }
  }
}

STATIC
VOID
DxioClearBer (
  IN       GNB_HANDLE       *GnbHandle
  )
{
  UINT32      Value;
  UINT32      SmuArg[6];
  UINT32      CcdBitfield;
  EFI_STATUS                        Status;
  CONST EFI_PEI_SERVICES            **PeiServices;
  PEI_AMD_NBIO_SMU_SERVICES_PPI     *SmuServicesPpi;

  SmuServicesPpi = NULL;
  PeiServices = GetPeiServicesTablePointer();

  Status = (*PeiServices)->LocatePpi (PeiServices, &gAmdNbioSmuServicesPpiGuid, 0, NULL, (VOID **)&SmuServicesPpi);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (GNB_TRACE, "Unable to locate SMU services\n");
  }

  CcdBitfield = 0;
  GetCcdInfo (&CcdBitfield);

  GnbLibPciRead (MAKE_SBDFO (0, 0, 0x18, 1, 0x330), AccessWidth32, &Value, NULL);

  LibAmdMemFill (SmuArg, 0x00, 24, (AMD_CONFIG_PARAMS *) NULL);
  SmuArg[1] = 0x22;
  SmuArg[2] = 0;
  SmuArg[3] = ((Value & 0x1F) << 16) | CcdBitfield | (CcdBitfield << 8);
  SmuArg[4] = 1;
  SmuServicesPpi->SmuDxioServiceRequest (SmuServicesPpi,
                                         GnbHandle->SocketId,
                                         58,
                                         SmuArg,
                                         SmuArg);

}
#endif

/*----------------------------------------------------------------------------------------*/
/**
 * After Pcie Training Enumerate all Pcie connectors for register setting.
 *
 *
 *
 * @param[in]     Engine  Engine configuration info
 * @param[in,out] Buffer  Buffer pointer
 * @param[in]     Pcie    PCIe configuration info
 */
VOID
STATIC
InitBusRanges (
  IN     GNB_HANDLE                           *GnbHandle,
  IN     PCIe_PLATFORM_CONFIG                 *Pcie,
  IN     AMD_PEI_FABRIC_TOPOLOGY_SERVICES2_PPI *FabricTopologyServicesPpi

  )
{
  GNB_HANDLE                      *InnerHandle;
  PCIE_VDM_NODE_CTRL4_STRUCT      VdmNode;
  UINTN                           SystemFabricID;
  UINTN                           BusNumberBase;
  UINTN                           BusNumberLimit;
  UINT32                          VdmNodeAddress;

  GNB_DEBUG_CODE (IDS_HDT_CONSOLE (GNB_TRACE,
                   "%a Enter for Socket %d RB %d\n",
                   __FUNCTION__,
                   GnbHandle->SocketId,
                   GnbHandle->RBIndex
                   ));

  InnerHandle = NbioGetHandle (Pcie);
  while (InnerHandle != NULL) {
    if (InnerHandle->SocketId == GnbHandle->SocketId) {
      FabricTopologyServicesPpi->GetRootBridgeInfo (
                                   InnerHandle->SocketId,
                                   InnerHandle->DieNumber,
                                   InnerHandle->LogicalRBIndex,
                                   &SystemFabricID,
                                   &BusNumberBase,
                                   &BusNumberLimit,
                                   NULL,
                                   NULL,
                                   NULL
                                   );
      VdmNode.Field.BUS_RANGE_BASE = BusNumberBase;
      VdmNode.Field.BUS_RANGE_LIMIT = BusNumberLimit;
      if (InnerHandle->LogicalRBIndex == GnbHandle->LogicalRBIndex) {
        VdmNode.Field.NODE_PRESENT = 0;
      } else {
        VdmNode.Field.NODE_PRESENT = 1;
      }

      if (BIG_IOHC (GnbHandle)) {
        VdmNodeAddress = SMN_IOHUB0_N0NBIO0_PCIE_VDM_NODE_CTRL4_ADDRESS;
      } else {
        VdmNodeAddress = SMN_IOHUB1_N0NBIO0_PCIE_VDM_NODE_CTRL4_ADDRESS;
      }

      VdmNodeAddress += InnerHandle->LogicalRBIndex * 0x10;
      SmnRegisterWriteS (GnbHandle->Address.Address.Segment,
                         GnbHandle->Address.Address.Bus,
                         NBIO_SPACE(GnbHandle, VdmNodeAddress),
                         &VdmNode.Value,
                         0
                        );
    }
    InnerHandle = GnbGetNextHandle (InnerHandle);
  }
}

/*----------------------------------------------------------------------------------------*/
/*
 *  Routine to fixup Pcie Platform Configuration for package specific values
 *
 *
 *
 * @param[in]     Pcie    PCIe configuration info
 */
AGESA_STATUS
STATIC
PcieCommonCoreConfigurationCallback (
  IN      PCIe_WRAPPER_CONFIG   *Wrapper,
  IN      VOID                  *Buffer,
  IN      PCIe_PLATFORM_CONFIG  *Pcie
  )
{
  GNB_HANDLE                *GnbHandle;
  UINT32                    Value;

  GnbHandle = (GNB_HANDLE *) PcieConfigGetParentSilicon (Wrapper);
  GNB_DEBUG_CODE (IDS_HDT_CONSOLE (GNB_TRACE,
                   "%a Enter for Socket %d Nbio %d Wrapper %d\n",
                   __FUNCTION__,
                   GnbHandle->SocketId,
                   GnbHandle->RBIndex,
                   Wrapper->WrapId
                   ));



  // @TODO - how to apply this for server?
  Value = (Wrapper->WrapId == 0) ? (UINT32) PcdGet8 (PcdGppAtomicOps) : (UINT32) PcdGet8(PcdGfxAtomicOps);
  SmnPrivateRegRMW (
    GnbHandle,
    WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_STRAP_F0_ADDRESS),
    (UINT32) ~(PCIE_STRAP_F0_STRAP_F0_ATOMIC_EN_MASK | PCIE_STRAP_F0_STRAP_F0_ATOMIC_ROUTING_EN_MASK),
    (Value << PCIE_STRAP_F0_STRAP_F0_ATOMIC_EN_OFFSET) | (Value << PCIE_STRAP_F0_STRAP_F0_ATOMIC_ROUTING_EN_OFFSET),
    0
    );

  /*SmnPrivateRegRMW (
    GnbHandle,
    WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_CNTL_ADDRESS),
    (UINT32) ~(PCIE_CNTL_RX_RCB_INVALID_SIZE_DIS_MASK | PCIE_CNTL_RX_RCB_WRONG_ATTR_DIS_MASK),
    (0x0 << PCIE_CNTL_RX_RCB_INVALID_SIZE_DIS_OFFSET | 0x1 << PCIE_CNTL_RX_RCB_WRONG_ATTR_DIS_OFFSET),
    0
    );
*/
  SmnPrivateRegRMW (
    GnbHandle,
    WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_TX_CTRL_1_ADDRESS),
    (UINT32) ~(PCIE_TX_CTRL_1_TX_ATOMIC_OPS_DISABLE_MASK | PCIE_TX_CTRL_1_TX_ATOMIC_ORDERING_DIS_MASK),
    (0x0 << PCIE_TX_CTRL_1_TX_ATOMIC_OPS_DISABLE_OFFSET | 0x1 << PCIE_TX_CTRL_1_TX_ATOMIC_ORDERING_DIS_OFFSET),
    0
    );

  SmnPrivateRegRMW (
    GnbHandle,
    WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIECORE_PCIE_TX_CTRL_3_ADDRESS),
    (UINT32) ~(PCIE_TX_CTRL_3_TX_ENCMSG_DESTID_FROM_SDP_REQ_EN_MASK |
               PCIE_TX_CTRL_3_TX_ENCMSG_HDR_FROM_SDP_REQ_EN_MASK),
    (0x0 << PCIE_TX_CTRL_3_TX_ENCMSG_DESTID_FROM_SDP_REQ_EN_OFFSET) |
    (0x0 << PCIE_TX_CTRL_3_TX_ENCMSG_HDR_FROM_SDP_REQ_EN_OFFSET),
    0
    );

  if ( PcdGetBool (PcdAmdNbioReportEdbErrors) ) {
    Value = 0;
  } else {
    Value = 1;
  }
  SmnPrivateRegRMW (
    GnbHandle,
    WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_P_CNTL_ADDRESS),
    (UINT32) ~(PCIE_P_CNTL_P_IGNORE_EDB_ERR_MASK | PCIE_P_CNTL_P_ELEC_IDLE_MODE_MASK),
    Value << PCIE_P_CNTL_P_IGNORE_EDB_ERR_OFFSET | 0x1 << PCIE_P_CNTL_P_ELEC_IDLE_MODE_OFFSET,
    0
    );

  // Change SLV_PHDR_CREDITS_RSVD and SLV_CTRL_1_SLV_PHDR from default(4) to 9
  SmnPrivateRegRMW (
    GnbHandle,
    WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_SLV_CTRL_1_ADDRESS),
    (UINT32) ~(PCIE_SLV_CTRL_1_SLV_PHDR_CREDITS_RSVD_MASK | PCIE_SLV_CTRL_1_SLV_PDAT_CREDITS_RSVD_MASK),
    9 << PCIE_SLV_CTRL_1_SLV_PHDR_CREDITS_RSVD_OFFSET | 9 << PCIE_SLV_CTRL_1_SLV_PDAT_CREDITS_RSVD_OFFSET,
    0
    );

  return AGESA_SUCCESS;
}

/*----------------------------------------------------------------------------------------*/
/*
 *  Routine to fixup Pcie Platform Configuration for package specific values
 *
 *
 *
 * @param[in]     Pcie    PCIe configuration info
 */
VOID
STATIC
PcieCommonEngineConfigurationCallback (
  IN      PCIe_ENGINE_CONFIG    *Engine,
  IN      VOID                  *Buffer,
  IN      PCIe_PLATFORM_CONFIG  *Pcie
  )
{
  GNB_HANDLE                  *GnbHandle;
  PCIe_WRAPPER_CONFIG         *Wrapper;
  PCIE_TX_REQUESTER_ID_STRUCT TxRequesterId;
  UINT32                      Value32;

  Value32 = 0;
  Wrapper = PcieConfigGetParentWrapper(Engine);
  GnbHandle = (GNB_HANDLE *) PcieConfigGetParentSilicon (Engine);

  if ((Engine->InitStatus == INIT_STATUS_PCIE_TRAINING_SUCCESS) ||
       ((Engine->Type.Port.PortData.LinkHotplug != HotplugDisabled) &&
        (Engine->Type.Port.PortData.LinkHotplug != HotplugInboard))) {

    GNB_DEBUG_CODE (IDS_HDT_CONSOLE (GNB_TRACE,
                     "%a Enter for Socket %d Nbio %d Wrapper %d Engine\n",
                     __FUNCTION__,
                     GnbHandle->SocketId,
                     GnbHandle->RBIndex,
                     Wrapper->WrapId,
                     Engine->Type.Port.PortId
                     ));

    SmnPrivateRegRead (GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_TX_REQUESTER_ID_ADDRESS),
      &TxRequesterId.Value
      );

    TxRequesterId.Field.TX_REQUESTER_ID_FUNCTION = Engine->Type.Port.PortData.FunctionNumber;
    TxRequesterId.Field.TX_REQUESTER_ID_DEVICE = Engine->Type.Port.PortData.DeviceNumber;
    TxRequesterId.Field.TX_REQUESTER_ID_BUS = GnbHandle->Address.Address.Bus;

    SmnPrivateRegWrite(GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_TX_REQUESTER_ID_ADDRESS),
      &TxRequesterId.Value,
      GNB_REG_ACC_FLAG_S3SAVE
      );

    //Set Slot Power Limit
    GnbLibPciRmw (
      (GnbHandle->Address.AddressValue |
       MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber, PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_SLOT_CAP_ADDRESS))),
      AccessWidth32,
      (UINT32) ~(SLOT_CAP_SLOT_PWR_LIMIT_VALUE_MASK |
                 SLOT_CAP_SLOT_PWR_LIMIT_SCALE_MASK),
      (UINT32) ((Engine->Type.Port.SlotPowerLimit << SLOT_CAP_SLOT_PWR_LIMIT_VALUE_OFFSET) |
                (Engine->Type.Port.SlotPowerLimitScale << SLOT_CAP_SLOT_PWR_LIMIT_SCALE_OFFSET)),
      NULL
      );

    // Set slot_implemented
    GnbLibPciRMW (
      (GnbHandle->Address.AddressValue |
      MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber, PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_PCIE_CAP_ADDRESS))),
      AccessWidth16,
      (UINT32) ~(PCIE_CAP_SLOT_IMPLEMENTED_MASK),
      (1 << (PCIE_CAP_SLOT_IMPLEMENTED_OFFSET)),
      NULL
    );

    // Set Physical Slot Number
    GnbLibPciRmw (
      (GnbHandle->Address.AddressValue |
       MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber, PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_SLOT_CAP_ADDRESS))),
      AccessWidth32,
      (UINT32) ~SLOT_CAP_PHYSICAL_SLOT_NUM_MASK,
      (UINT32) (Engine->Type.Port.PortData.SlotNum << SLOT_CAP_PHYSICAL_SLOT_NUM_OFFSET),
      NULL
      );

    // Set Completion Timeout
    GnbLibPciRMW (
      (GnbHandle->Address.AddressValue |
      MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber, PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_DEVICE_CNTL2_ADDRESS))),
      AccessWidth16,
      (UINT32) ~(DEVICE_CNTL2_CPL_TIMEOUT_VALUE_MASK),
      (0x6 << DEVICE_CNTL2_CPL_TIMEOUT_VALUE_OFFSET),
      NULL
    );

    SmnPrivateRegRMW (GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_CNTL_ADDRESS),
      (UINT32) ~(PCIE_LC_CNTL_LC_L1_IMMEDIATE_ACK_MASK),
      (0x1 << PCIE_LC_CNTL_LC_L1_IMMEDIATE_ACK_OFFSET),
      0
      );
    SmnPrivateRegRMW (GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_LINK_WIDTH_CNTL_ADDRESS),
      (UINT32) ~(PCIE_LC_LINK_WIDTH_CNTL_LC_DUAL_END_RECONFIG_EN_MASK |
        PCIE_LC_LINK_WIDTH_CNTL_LC_RENEGOTIATE_EN_MASK),
      (0x1 << PCIE_LC_LINK_WIDTH_CNTL_LC_DUAL_END_RECONFIG_EN_OFFSET) |
        (0x1 << PCIE_LC_LINK_WIDTH_CNTL_LC_RENEGOTIATE_EN_OFFSET),
      0
      );
    SmnPrivateRegRMW (GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_LINK_WIDTH_CNTL_ADDRESS),
      (UINT32) ~(PCIE_LC_LINK_WIDTH_CNTL_LC_L1_RECONFIG_EN_MASK),
      (0x1 << PCIE_LC_LINK_WIDTH_CNTL_LC_L1_RECONFIG_EN_OFFSET),
      0
      );
    SmnPrivateRegRMW (GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_CNTL2_ADDRESS),
      (UINT32) ~(PCIE_LC_CNTL2_LC_WAIT_FOR_OTHER_LANES_MODE_MASK),
      (0x1 << PCIE_LC_CNTL2_LC_WAIT_FOR_OTHER_LANES_MODE_OFFSET),
      0
      );
    SmnPrivateRegRMW (GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_CNTL2_ADDRESS),
      (UINT32) ~(PCIE_LC_CNTL2_LC_ELEC_IDLE_MODE_MASK),
      (0x1 << PCIE_LC_CNTL2_LC_ELEC_IDLE_MODE_OFFSET),
      0
      );
    SmnPrivateRegRMW (GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_CNTL3_ADDRESS),
      (UINT32) ~(PCIE_LC_CNTL3_LC_LINK_DOWN_SPD_CHG_EN_MASK),
      (0x1 << PCIE_LC_CNTL3_LC_LINK_DOWN_SPD_CHG_EN_OFFSET),
      0
      );
    SmnPrivateRegRMW (GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_TRAINING_CNTL_ADDRESS),
      (UINT32) ~(PCIE_LC_TRAINING_CNTL_LC_L0S_L1_TRAINING_CNTL_EN_MASK),
      1 << PCIE_LC_TRAINING_CNTL_LC_L0S_L1_TRAINING_CNTL_EN_OFFSET,
      0
      );
    SmnPrivateRegRMW (GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIEP_HW_DEBUG_LC_ADDRESS ),
      (UINT32) ~(PCIEP_HW_DEBUG_LC_HW_15_DEBUG_LC_MASK),
      (0x1 << PCIEP_HW_DEBUG_LC_HW_15_DEBUG_LC_OFFSET),
      0
      );
    // Set Tx Margin
    GnbLibPciRMW (
      (GnbHandle->Address.AddressValue |
      MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber, PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_LINK_CNTL2_ADDRESS))),
      AccessWidth16,
      (UINT32) ~(LINK_CNTL2_XMIT_MARGIN_MASK),
      (Engine->Type.Port.TXMargin << LINK_CNTL2_XMIT_MARGIN_OFFSET),
      NULL
    );

    // Set Powerdown state in L1/L1.1/L1.2
    SmnPrivateRegRMW (GnbHandle,
                  PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_CNTL4_ADDRESS),
                  (UINT32) ~(PCIE_LC_CNTL4_LC_L1_POWERDOWN_MASK),
                  ((Engine->Type.Port.PortFeatures.L1PowerDown & 1) << PCIE_LC_CNTL4_LC_L1_POWERDOWN_OFFSET),
                  0
                  );

    SmnPrivateRegRMW (GnbHandle,
                  PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_L1_PM_SUBSTATE_ADDRESS),
                  (UINT32) ~(PCIE_LC_L1_PM_SUBSTATE_LC_L1_1_POWERDOWN_MASK),
                  ((Engine->Type.Port.PortFeatures.L11PowerDown & 7) << PCIE_LC_L1_PM_SUBSTATE_LC_L1_1_POWERDOWN_OFFSET),
                  0
                  );

    SmnPrivateRegRMW (GnbHandle,
                  PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_L1_PM_SUBSTATE_ADDRESS),
                  (UINT32) ~(PCIE_LC_L1_PM_SUBSTATE_LC_L1_2_POWERDOWN_MASK),
                  ((Engine->Type.Port.PortFeatures.L12PowerDown & 7) << PCIE_LC_L1_PM_SUBSTATE_LC_L1_2_POWERDOWN_OFFSET),
                  0
                  );

    //If at least one port is trained, enabling PCIE_RCB_CNTL_RX_RCB_RC_CTO_IGNORE_ERR_IN_LINK_DOWN_EN
    SmnPrivateRegRMW (GnbHandle,
                     WRAP_SPACE (GnbHandle, Wrapper, SMN_PCIE0NBIO0_PCIE_RCB_CNTL_ADDRESS),
                     (UINT32) ~(PCIE_RCB_CNTL_RX_RCB_RC_CTO_TO_SC_IN_LINK_DOWN_EN_MASK |
                                PCIE_RCB_CNTL_RX_RCB_RC_CTO_IGNORE_ERR_IN_LINK_DOWN_EN_MASK),
                     (UINT32) (1 << PCIE_RCB_CNTL_RX_RCB_RC_CTO_TO_SC_IN_LINK_DOWN_EN_OFFSET) |
                             (1 << PCIE_RCB_CNTL_RX_RCB_RC_CTO_IGNORE_ERR_IN_LINK_DOWN_EN_OFFSET),
                     0
                    );

    // SRNS Enable Mode
    if (Engine->Type.Port.SrisEnableMode == 1 || Engine->Type.Port.SrisAutoDetectMode == 1) {
      SmnPrivateRegRead (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_SPEED_CNTL_ADDRESS),
        &Value32
        );
      IDS_HDT_CONSOLE (GNB_TRACE, "PCIE_LC_SPEED_CNTL = 0x%x\n", Value32);
      Value32 = ((Value32 & PCIE_LC_SPEED_CNTL_LC_GEN5_EN_STRAP_MASK)? 0x1F :\
                ((Value32 & PCIE_LC_SPEED_CNTL_LC_GEN4_EN_STRAP_MASK)? 0xF :\
                ((Value32 & PCIE_LC_SPEED_CNTL_LC_GEN3_EN_STRAP_MASK)? 0x7 :\
                ((Value32 & PCIE_LC_SPEED_CNTL_LC_GEN2_EN_STRAP_MASK)? 0x3 : 0x1))));
      IDS_HDT_CONSOLE (GNB_TRACE, "set STRAP_LOWER_SKP_OS_GEN_SUPPORT = 0x%x\n", Value32);
      WritePcieStrap (
          GnbHandle,
          (STRAP_LOWER_SKP_OS_GEN_SUPPORT_A_INDEX + (STRAP_BIF_PORT_DIFF * Engine->Type.Port.PortId)),
          ((Engine->Type.Port.SrisAutoDetectMode != 1 && (PcdGet8 (PcdSrisCfgType) & (SRIS_DBG | SRIS_DBG_PBS)))?
           ((Engine->Type.Port.LowerSkpOsGenSup <= Value32)? Engine->Type.Port.LowerSkpOsGenSup : Value32) : 0),
          Wrapper->WrapId
          );
      WritePcieStrap (
          GnbHandle,
          (STRAP_LOWER_SKP_OS_RCV_SUPPORT_A_INDEX + (STRAP_BIF_PORT_DIFF * Engine->Type.Port.PortId)),
          ((Engine->Type.Port.SrisAutoDetectMode != 1 && (PcdGet8 (PcdSrisCfgType) & (SRIS_DBG | SRIS_DBG_PBS)))?
           ((Engine->Type.Port.LowerSkpOsRcvSup <= Value32)? Engine->Type.Port.LowerSkpOsRcvSup : Value32) : 0),
          Wrapper->WrapId
          );
      SmnPrivateRegRead (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_CNTL6_ADDRESS),
        &Value32
        );
      if (Engine->Type.Port.SrisAutoDetectMode == 1) {
        // Set AutoDetect Mode & Autodetection factor
        Value32 &= ~(PCIE_LC_CNTL6_LC_SRIS_AUTODETECT_MODE_MASK | PCIE_LC_CNTL6_LC_SRIS_AUTODETECT_FACTOR_MASK);
        Value32 |= (Engine->Type.Port.SrisSkpIntervalSel << PCIE_LC_CNTL6_LC_SRIS_AUTODETECT_MODE_OFFSET) |\
                   (Engine->Type.Port.SrisAutodetectFactor << PCIE_LC_CNTL6_LC_SRIS_AUTODETECT_FACTOR_OFFSET) |\
                   (1 << PCIE_LC_CNTL6_LC_SRIS_AUTODETECT_EN_OFFSET);
      } else {
        // SRIS
        if (Engine->Type.Port.SrisEnableMode == 1) {
          Value32 |= (1 << PCIE_LC_CNTL6_LC_SRIS_EN_OFFSET);
        }
      }
      SmnPrivateRegWrite (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_CNTL6_ADDRESS),
        &Value32,
        0
        );
    }
    /*IDS_HDT_CONSOLE (GNB_TRACE, "spc: %d\n", Engine->Type.Port.SpcMode.SpcGen3);
    SmnPrivateRegRMW (GnbHandle,
      PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_CNTL6_ADDRESS),
      (UINT32) ~(PCIE_LC_CNTL6_LC_SPC_MODE_2P5GT_MASK |
                 PCIE_LC_CNTL6_LC_SPC_MODE_5GT_MASK |
                 PCIE_LC_CNTL6_LC_SPC_MODE_8GT_MASK),
      (Engine->Type.Port.SpcMode.SpcGen1 << PCIE_LC_CNTL6_LC_SPC_MODE_2P5GT_OFFSET) |
      (Engine->Type.Port.SpcMode.SpcGen2 << PCIE_LC_CNTL6_LC_SPC_MODE_5GT_OFFSET) |
      (Engine->Type.Port.SpcMode.SpcGen3 << PCIE_LC_CNTL6_LC_SPC_MODE_8GT_OFFSET),
      0
      );*/

    // PCI-E specification states L0 Exit latency should be 7
    // to discourage older software from enabling Rx-L0s (ie. enabling TX-L0s in the EP)
    WritePcieStrap(
      GnbHandle,
      STRAP_BIF_L0S_EXIT_LATENCY_A_INDEX + ((Engine->Type.Port.PortId) * STRAP_BIF_PORT_DIFF),
      7,
      Wrapper->WrapId
      );

  } else {
    // Expose Unused PCIE port
    if (PcieConfigCheckPortStatus (Engine, INIT_STATUS_PCIE_PORT_ALWAYS_EXPOSE)) {
      // Set slot_implemented
      SmnRegisterRMWS (GnbHandle->Address.Address.Segment, GnbHandle->Address.Address.Bus,
                      PORT_SPACE(GnbHandle, Wrapper, (Engine->Type.Port.PortId % 8), SMN_FUNC0_PCIE0NBIO0_PCIE_CAP_LIST_ADDRESS),
                      (UINT32) ~(PCIE_CAP_SLOT_IMPLEMENTED_MASK << 16),
                      (1 << (PCIE_CAP_SLOT_IMPLEMENTED_OFFSET + 16)),
                      0
                      );
      // Assign slot number
      GnbLibPciRmw (
        (GnbHandle->Address.AddressValue |
         MAKE_SBDFO (0, 0, Engine->Type.Port.PortData.DeviceNumber, Engine->Type.Port.PortData.FunctionNumber, PCICFG_OFFSET (SMN_FUNC0_PCIE0NBIO0_SLOT_CAP_ADDRESS))),
        AccessWidth32,
        (UINT32) ~SLOT_CAP_PHYSICAL_SLOT_NUM_MASK,
        (UINT32) (Engine->Type.Port.PortData.SlotNum << SLOT_CAP_PHYSICAL_SLOT_NUM_OFFSET),
        NULL
        );
    }
  }

  if (PcdGet8 (PcdCfgPcieCVTestWA) == 1) {
    SmnRegisterReadS (
      GnbHandle->Address.Address.Segment,
       GnbHandle->Address.Address.Bus,
       PORT_SPACE(GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_LINK_CAP_ADDRESS),
       &Value32
       );
    if ((Value32 & 0xF) >= 0x03) {
      SmnPrivateRegRMW (GnbHandle,
        PORT_SPACE (GnbHandle, Wrapper, (Engine->Type.Port.PortId), SMN_FUNC0_PCIE0NBIO0_PCIE_LC_CNTL2_ADDRESS),
        (UINT32) ~(PCIE_LC_CNTL2_LC_MORE_TS2_EN_MASK),
        (1 << PCIE_LC_CNTL2_LC_MORE_TS2_EN_OFFSET),
        0
        );
    }
  }
}

VOID
PrintDeliInfo(DXIO_FW_MP1_DELI_INFO *pDeliBuf)
{
  UINT8 LGSCount;

  if(pDeliBuf->Hdr.engType == 0){  // Print the DELI log only for PCIe engine
    IDS_HDT_CONSOLE (GNB_TRACE, "\n //////// Header Data ////////\n");
    IDS_HDT_CONSOLE (GNB_TRACE, "Number of Dwords = %d\n", pDeliBuf->Hdr.numDWords);
    IDS_HDT_CONSOLE (GNB_TRACE, "Version = %d\n", pDeliBuf->Hdr.version);
    IDS_HDT_CONSOLE (GNB_TRACE, "////////// Lane Group Data //////////\n");
    IDS_HDT_CONSOLE (GNB_TRACE, "Number of Lane Groups = %d\n", pDeliBuf->Dxio.numLGroups);
    IDS_HDT_CONSOLE (GNB_TRACE, "Version = %d\n", pDeliBuf->Dxio.version);
    for (LGSCount = 0; LGSCount < DXIO_FW_MP1_DELI_MAX_NUM_DXIO_LGS; LGSCount++){
      IDS_HDT_CONSOLE (GNB_TRACE, "Phy Start Lane = %d\n",pDeliBuf->Dxio.LGS[LGSCount].KPNP_startPhyLane);
      IDS_HDT_CONSOLE (GNB_TRACE, "Phy End Lane = %d\n",pDeliBuf->Dxio.LGS[LGSCount].KPNP_endPhyLane);
      IDS_HDT_CONSOLE (GNB_TRACE, "Lane Req Status = %x\n",pDeliBuf->Dxio.LGS[LGSCount].KPNP_LANE_REQ_STATUS);
      IDS_HDT_CONSOLE (GNB_TRACE, "Hardware Debug = %x\n",pDeliBuf->Dxio.LGS[LGSCount].KPMX_HWDEBUG);
    }
    IDS_HDT_CONSOLE (GNB_TRACE, "//////// PCIe Training Data /////////\n");
    IDS_HDT_CONSOLE (GNB_TRACE, "Version = %d\n", pDeliBuf->Proto.Pcie.version);
    IDS_HDT_CONSOLE (GNB_TRACE, "Link State = %x\n", pDeliBuf->Proto.Pcie.PCIE_link_state);
    IDS_HDT_CONSOLE (GNB_TRACE, "Link Speed = %d\n", pDeliBuf->Proto.Pcie.PCIE_link_speed);
    IDS_HDT_CONSOLE (GNB_TRACE, "Link Width = %d\n", pDeliBuf->Proto.Pcie.PCIE_link_width);
    IDS_HDT_CONSOLE (GNB_TRACE, "Link Active = %x\n", pDeliBuf->Proto.Pcie.PCIE_link_active);
    IDS_HDT_CONSOLE (GNB_TRACE, "Port Number = %x\n", pDeliBuf->Proto.Pcie.PCIE_port_num);
    IDS_HDT_CONSOLE (GNB_TRACE, "SWRST_CONTROL = %x\n", pDeliBuf->Proto.Pcie.SWRST_CONTROL_6);
  }
}

VOID
GetDeliInfo(
  IN GNB_HANDLE                *GnbHandle,
  IN PCIe_ENGINE_CONFIG        *Engine
  )
{
  UINT32                          Response;
  UINT32                          MpioArg[6];
  UINT8                           DELIBuf[sizeof(DXIO_FW_MP1_DELI_INFO)];
  DXIO_FW_MP1_DELI_INFO           *pDeliBuf;

  LibAmdMemFill ((VOID *)(DELIBuf), 0x00, sizeof(DELIBuf), (AMD_CONFIG_PARAMS *) NULL);

  NbioMpioServiceCommonInitArguments (MpioArg);
  MpioArg[1] = 0;                                     // Address High
  MpioArg[2] = (UINT32) DELIBuf;                      // Address Low
  MpioArg[4] = (UINT32) Engine->EngineData.StartLane; // StartLane

  IDS_HDT_CONSOLE (GNB_TRACE, "DELI INFO for InstanceId = %d, StartLane = %d \n",
                                GnbHandle->InstanceId,Engine->EngineData.StartLane);

  Response = MpioServiceRequest (NbioGetHostPciAddress (GnbHandle), BIOS_MPIO_MSG_GET_DELI_INFO, MpioArg, 0);
  IDS_HDT_CONSOLE (GNB_TRACE, "  MPIO Response = 0x%x\n", Response);
  GnbLibDebugDumpBuffer ((VOID*)MpioArg[2], sizeof(DELIBuf), 1, 16);
  pDeliBuf = (DXIO_FW_MP1_DELI_INFO*) MpioArg[2];
  PrintDeliInfo(pDeliBuf);
}

VOID
CheckDeliIfEngineIsBmc (
  IN GNB_HANDLE                *GnbHandle,
  IN PCIe_ENGINE_CONFIG        *Engine
  )
{
  UINT32                          Response;
  UINT32                          MpioArg[6];
  UINT8                           DELIBuf[sizeof(DXIO_FW_MP1_DELI_INFO)];
  DXIO_FW_MP1_DELI_INFO           *pDeliBuf;

  LibAmdMemFill ((VOID *)(DELIBuf), 0x00, sizeof(DELIBuf), (AMD_CONFIG_PARAMS *) NULL);

  NbioMpioServiceCommonInitArguments (MpioArg);
  MpioArg[1] = 0;                                          // Address High
  MpioArg[2] = (UINT32) DELIBuf;                           // Address Low
  MpioArg[4] = (UINT32) PcdGet8 (PcdEarlyBmcLinkLaneNum);  // StartLane

  Response = MpioServiceRequest (NbioGetHostPciAddress (GnbHandle), BIOS_MPIO_MSG_GET_DELI_INFO, MpioArg, 0);
  IDS_HDT_CONSOLE (GNB_TRACE, "  MPIO Response = 0x%x\n", Response);
  pDeliBuf = (DXIO_FW_MP1_DELI_INFO*) MpioArg[2];

  IDS_HDT_CONSOLE (GNB_TRACE, "Port Number DELI = %d, Port ID Engine = %d\n",
                              pDeliBuf->Proto.Pcie.PCIE_port_num, Engine->Type.Port.PortId);

  if (pDeliBuf->Proto.Pcie.PCIE_port_num == Engine->Type.Port.PortId) {
    IDS_HDT_CONSOLE (GNB_TRACE, "DELI INFO for InstanceId = %d, StartLane = %d \n",
                            GnbHandle->InstanceId, PcdGet8 (PcdEarlyBmcLinkLaneNum));
    GnbLibDebugDumpBuffer ((VOID *)MpioArg[2], sizeof (DELIBuf), 1, 16);
    PrintDeliInfo (pDeliBuf);
    Engine->Type.Port.IsBmcLocation = 1;
  }
}


VOID
STATIC
PcieCommonEngineGetDeliInfoCallback (
  IN      PCIe_ENGINE_CONFIG    *Engine,
  IN      VOID                  *Buffer,
  IN      PCIe_PLATFORM_CONFIG  *Pcie
) {
  GNB_HANDLE                  *GnbHandle;
  PCIe_WRAPPER_CONFIG         *Wrapper;

  GnbHandle = (GNB_HANDLE *) PcieConfigGetParentSilicon (Engine);
  Wrapper = PcieConfigGetParentWrapper (Engine);

  // Use   Engine->EngineData.StartLane;
  GetDeliInfo (GnbHandle, Engine);

  if (IsEarlyTrainedBmcInWrapper (Wrapper, GnbHandle)) {
    CheckDeliIfEngineIsBmc (GnbHandle, Engine);
  }

  return;
}


/**----------------------------------------------------------------------------------------*/
/**
 * Configuration Timepoint after DXIO firmware initialization completes
 *
 *
 *
 * @param[in]  Pcie                 Pointer silicon complex descriptor
 *
 *-----------------------------------------------------------------------------------------*/


VOID
DxioCfgAfterDxioInit (
  IN     PCIe_PLATFORM_CONFIG                 *Pcie,
  IN     AMD_PEI_FABRIC_TOPOLOGY_SERVICES2_PPI *FabricTopologyServicesPpi

  )
{
  GNB_HANDLE     *GnbHandle;
  // UINT32         PackageType;

  IDS_HDT_CONSOLE (GNB_TRACE, "%a Enter\n", __FUNCTION__);

  // PackageType = LibAmdGetPackageType ((AMD_CONFIG_PARAMS *) NULL);

  PcieConfigDebugDump(Pcie);
  PcieConfigRunProcForAllWrappers (DESCRIPTOR_ALL_WRAPPERS, PcieCommonCoreConfigurationCallback, NULL, Pcie);
  PcieConfigRunProcForAllEngines (DESCRIPTOR_ALLOCATED | DESCRIPTOR_PCIE_ENGINE, PcieCommonEngineConfigurationCallback, NULL, Pcie);
  PcieConfigRunProcForAllEngines (DESCRIPTOR_ALLOCATED | DESCRIPTOR_PCIE_ENGINE, PcieCommonEngineGetDeliInfoCallback, NULL, Pcie);

  GnbHandle = NbioGetHandle (Pcie);
  while (GnbHandle != NULL) {
    NbioPerfAnalysis((VOID *)GnbHandle, NULL);
    InitBusRanges (GnbHandle, Pcie, FabricTopologyServicesPpi);
    SubsystemIdSetting (GnbHandle);
    if (GnbHandle->RBIndex < 4) {
      // Initialize ARI
      if (TRUE == PcdGetBool (PcdCfgPcieAriSupport)) {
        SmnRegisterRMWS (
          GnbHandle->Address.Address.Segment,
          GnbHandle->Address.Address.Bus,
          NBIO_SPACE (GnbHandle, SMN_IOHUB0NBIO0_IOHC_FEATURE_CNTL_ADDRESS),
          (UINT32)~(IOHC_FEATURE_CNTL_IOHC_ARI_SUPPORTED_MASK),
          1 << IOHC_FEATURE_CNTL_IOHC_ARI_SUPPORTED_OFFSET,
          0
          );
        PcieAriInit (GnbHandle);
      }
    } else {
      if (TRUE == PcdGetBool (PcdCfgPcieAriSupport)) {
        SmnRegisterRMWS (
          GnbHandle->Address.Address.Segment,
          GnbHandle->Address.Address.Bus,
          NBIO_SPACE (GnbHandle, SMN_IOHUB1NBIO0_IOHC_FEATURE_CNTL_ADDRESS),
          (UINT32)~(IOHC_FEATURE_CNTL_IOHC_ARI_SUPPORTED_MASK),
          1 << IOHC_FEATURE_CNTL_IOHC_ARI_SUPPORTED_OFFSET,
          0
          );
        PcieAriInit (GnbHandle);
      }
    }
    GnbHandle = GnbGetNextHandle (GnbHandle);
  }


  return;
}

/**----------------------------------------------------------------------------------------*/
/**
 * Configuration Usb4 Lane number before DXIO firmware initialization
 *
 *
 *
 * @param[in]  Counter                 Counter of Controller number
 * @param[in]  AskEntry                Pointer to ASK entry
 * @param[in]  TopologyEntry           Pointer to engine topology
 *
 */
 /*----------------------------------------------------------------------------------------*/
AGESA_STATUS
Usb4LaneNumberCfgCallback(
  IN       UINT8                        Counter,
  IN       FW_ASK_STRUCT                *AskEntry,
  IN       DXIO_PORT_DESCRIPTOR         *TopologyEntry
)
{
  return EFI_UNSUPPORTED;
}

/*----------------------------------------------------------------------------------------*/
/**
 * Check each Engine that ever tried training
 *
 * @param[in]     Engine  Engine configuration info
 * @return        TRUE  - This engine is ever tried training
 *                FALSE - This engine is not tried training
 *
 */
BOOLEAN
MpioIsEverTriedTraining (
  PCIe_ENGINE_CONFIG        *Engine
) {
  GNB_HANDLE                  *GnbHandle;
  PCIe_WRAPPER_CONFIG         *Wrapper;
  UINT32                      Value32;
  UINT32                      SmnAddress;
  UINT32                      i, j;

  Value32 = 0;
  SmnAddress = SMN_FUNC0_PCIE0NBIO0_PCIE_LC_STATE0_ADDRESS;
  Wrapper = PcieConfigGetParentWrapper(Engine);
  GnbHandle = (GNB_HANDLE *) PcieConfigGetParentSilicon (Engine);

  // Check LC_STATE0 ~ LC_STATE5
  for (i = 0; i < 6; i++) {
    SmnPrivateRegRead (
      GnbHandle,
      PORT_SPACE(GnbHandle, Wrapper, Engine->Type.Port.PortId, SmnAddress + i * 4),
      &Value32
      );
    // Check LC_CURRENT_STATE and all LC_PREV_STATE
    for (j = 0; j < 4; j++) {
      if (((Value32 >> (j * 8)) & 0xFF) > 0x4) {
        return TRUE;
      }
    }
  }
  return FALSE;
}


