#;*****************************************************************************
#;
#; Copyright (C) 2018-2025 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

## @file
#  PRM Driver
#
#  A PRM Module implementation. This PRM Module provides PRM handlers that perform various types
#  of address tansltion.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = PrmAddressTranslateModule
  FILE_GUID                      = 8dceeebb-5741-4092-884d-144ec472682d
  MODULE_TYPE                    = DXE_RUNTIME_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = PrmAddressTranslateModuleInit

[Sources]
  PrmAddressTranslateModule.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec
  AgesaModulePkg/AgesaModuleRasPkg.dec
  AgesaPkg/AgesaPkg.dec
# AMI PORTING
#  edk2/PrmPkg/PrmPkg.dec
  PrmPkg/PrmPkg.dec
  AmdCpmPkg/AmdCpmPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  MemoryAllocationLib
  UefiDriverEntryPoint
  UefiLib
  IoLib

[Depex]
  TRUE

[BuildOptions.common]
  MSFT:*_*_*_DLINK_FLAGS  = /DLL /SUBSYSTEM:CONSOLE /VERSION:1.0
  MSFT:*_*_*_GENFW_FLAGS = --keepoptionalheader
  GCC:*_*_X64_GENFW_FLAGS = --keepoptionalheader --prm
  GCC:*_*_X64_DLINK_FLAGS = -Wl,--no-gc-sections -Wl,--require-defined=PrmModuleExportDescriptor -Wl,--require-defined=AmdNormalizedToDramAddrHandler -Wl,--require-defined=AmdDramToNormalizedAddrHandler -Wl,--require-defined=AmdNormalizedToSystemPhysicalAddrHandler -Wl,--require-defined=AmdSystemPhysicalToNormalizedAddrHandler -Wl,--require-defined=AmdSystemPhysicalToDramAddrHandler -Wl,--require-defined=AmdDramToSystemPhysicalAddrHandler -Wl,--require-defined=AmdCxlDpaToSystemPhysicalAddrHandler
  GCC:*_*_X64_OBJCOPY_STRIPFLAG = --keep-symbol=PrmModuleExportDescriptor --keep-symbol=AmdNormalizedToDramAddrHandler --keep-symbol=AmdDramToNormalizedAddrHandler --keep-symbol=AmdNormalizedToSystemPhysicalAddrHandler --keep-symbol=AmdSystemPhysicalToNormalizedAddrHandler --keep-symbol=AmdSystemPhysicalToDramAddrHandler --keep-symbol=AmdDramToSystemPhysicalAddrHandler --keep-symbol=AmdCxlDpaToSystemPhysicalAddrHandler
