#***********************************************************************
#*                                                                     *
#*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
#*                                                                     *
#*      All rights reserved. Subject to AMI licensing agreement.       *
#*                                                                     *
#***********************************************************************

## @file
#  Cryptographic Library Instance for SEC.
#
#  Caution: This module requires additional review when modified.
#  This library will have external input - signature.
#  This external input must be validated carefully to avoid security issues such as
#  buffer overflow or integer overflow.
#
#  Copyright (c) 2023, Intel Corporation. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = SecCryptLib
  MODULE_UNI_FILE                = SecCryptLib.uni
  FILE_GUID                      = 894C367F-254A-4563-8624-798D46EAD796
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = BaseCryptLib|SEC

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  InternalCryptLib.h
  Hash/CryptSha512.c
  Hash/CryptMd5Null.c
  Hash/CryptSha1Null.c
  Hash/CryptSha256Null.c
  Hash/CryptSm3Null.c
  Hash/CryptParallelHashNull.c
  Hmac/CryptHmacNull.c
  Kdf/CryptHkdfNull.c
  Cipher/CryptAesNull.c
  Cipher/CryptAeadAesGcmNull.c
  Pk/CryptRsaBasicNull.c
  Pk/CryptRsaExtNull.c
  Bn/CryptBnNull.c
  Pem/CryptPemNull.c
  Pk/CryptDhNull.c
  Pk/CryptEcNull.c
  Pk/CryptPkcs1OaepNull.c
  Pk/CryptPkcs5Pbkdf2Null.c
  Pk/CryptPkcs7SignNull.c
  Pk/CryptPkcs7VerifyNull.c
  Pk/CryptPkcs7VerifyEkuNull.c
  Pk/CryptX509Null.c
  Pk/CryptAuthenticodeNull.c
  Pk/CryptTsNull.c
  Rand/CryptRandNull.c
  SysCall/CrtWrapper.c
  SysCall/ConstantTimeClock.c
  #APTIOV OVERRIDE - To include malloc, realloc, free functions
  SysCall/BaseMemAllocation.c
  #APTIOV OVERRIDE

[Packages]
  MdePkg/MdePkg.dec
  CryptoPkg/CryptoPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  MemoryAllocationLib
  DebugLib
  MbedTlsLib
  IntrinsicLib
  PrintLib

#
# Remove these [BuildOptions] after this library is cleaned up
#
[BuildOptions]
  GCC:*_CLANGDWARF_*_CC_FLAGS = -std=gnu99
  GCC:*_CLANGPDB_*_CC_FLAGS = -std=c99 -Wno-error=incompatible-pointer-types

  XCODE:*_*_*_CC_FLAGS = -std=c99
