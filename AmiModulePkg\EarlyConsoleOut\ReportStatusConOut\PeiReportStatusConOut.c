//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************

/** @file  PeiReportStatusConOut.c
  Defines Status code function to display Progress and Error in ConOut Devices.
**/

#include <PeiReportStatusConOut.h>

static EFI_PEI_NOTIFY_DESCRIPTOR gNotifyList[] = {
  {
    EFI_PEI_PPI_DESCRIPTOR_NOTIFY_CALLBACK | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST,
    &gEfiPeiRscHandlerPpiGuid,
    ConsplitterTextOutPpiCallback
  }
};

/**
    Notify function to register the PeiReportStatusCode handler 
    to display checkpoint.

    @param PeiServices  Pointer to PeiServices Table
    @param NotifyDesc   Pointer to Notify Desc
    @param InvokePpi    Pointer to Invoked Ppi

    @retval EFI_STATUS

**/
EFI_STATUS
EFIAPI
ConsplitterTextOutPpiCallback (
    IN  EFI_PEI_SERVICES          **PeiServices,
    IN  EFI_PEI_NOTIFY_DESCRIPTOR *NotifyDesc,
    IN  VOID                      *InvokePpi
)
{
    EFI_STATUS                      Status;
    EFI_PEI_RSC_HANDLER_PPI         *RscHandlerPpi;
    
    // Locate Handler for Registering the ConOut Status Code 
    Status = PeiServicesLocatePpi(
                                &gEfiPeiRscHandlerPpiGuid,
                                0,
                                NULL,
                                (VOID**)&RscHandlerPpi);
    
    if (EFI_ERROR (Status)) {
        DEBUG((DEBUG_ERROR, "%a():Register : %r", __FUNCTION__, Status));
        return Status;
    }
    
    Status = RscHandlerPpi->Register (PeiReportStatusConOutWorker);
    
    return Status;
}

/**
    Print PEI phase status code check point and description string.

    @param PeiServices - Pointer to the PEI Core data Structure
    @param Value - EFI status code Value
    @param CodeType - EFI status code type
    @param Instance - The enumeration of a hardware or software entity within
                    the system. A system may contain multiple entities that
                    match a class/subclass pairing. The instance differentiates
                    between them. An instance of 0 indicates that instance
                    information is unavailable, not meaningful, or not relevant.
                    Valid instance numbers start with 1.
    @param CallerId - This optional parameter may be used to identify the caller.
                           This parameter allows the status code driver to apply different
                           rules to different callers.
    @param Data - This optional parameter may be used to pass additional data.

    @return EFI_STATUS

**/
EFI_STATUS
EFIAPI
PeiReportStatusConOutWorker (
    IN CONST  EFI_PEI_SERVICES        **PeiServices,
    IN EFI_STATUS_CODE_TYPE           CodeType,
    IN EFI_STATUS_CODE_VALUE          Value,
    IN UINT32                         Instance,
    IN CONST EFI_GUID                 *CallerId,
    IN CONST EFI_STATUS_CODE_DATA     *ErrorData OPTIONAL 
)
{
    EFI_STATUS                  Status;
    UINTN                       CheckPointCode = 0;
    CHAR8                       *ProgressCodeString = NULL;
    CHAR8                       *ErrorCodeString = NULL;
    CHAR8                       *PossibleCauseString = NULL;
    CHAR8                       *PossibleSolutionString = NULL;
    CHAR16                      CheckPointString[5];
    CHAR16                      ErrorString[13];
    CHAR16                      TextString[160];
    AMI_EARLY_CONSOLE_OUT_PPI   *AmiEarlyConsoleOutPpi;
    
    //DEBUG((DEBUG_INFO,"%a() : ENTRY", __FUNCTION__));
    Status = PeiServicesLocatePpi (
                        &gAmiEarlyConsoleOutPpiGuid,
                        0,
                        NULL,
                        (VOID**)&AmiEarlyConsoleOutPpi);
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR,"%a : Locate AmiEarlyConsoleOutPpi : %r\n",__FUNCTION__, Status));
        return Status;
    }
    
    // Handle only the Progress Code and Error Code type Status 
    if (((CodeType & EFI_STATUS_CODE_TYPE_MASK) != EFI_PROGRESS_CODE) &&  
        ((CodeType & EFI_STATUS_CODE_TYPE_MASK) != EFI_ERROR_CODE)) {
        return EFI_SUCCESS;
    }
    
    // Check if the type is Progress Code
    if ((CodeType & EFI_STATUS_CODE_TYPE_MASK) == EFI_PROGRESS_CODE) {

        // Get the CheckPoint value for the EFI Progress Code
        CheckPointCode = GetAmiProgressCodeCheckPoint (Value);
        if (CheckPointCode == 0) {
            // If no CheckPoint found return it
            return EFI_SUCCESS;
        }
        
        // Get the Progress code String for the 
        ProgressCodeString = GetAmiProgressCodeString (Value);
        if (ProgressCodeString == NULL) {
            return EFI_SUCCESS;
        }
        
        AmiEarlyConsoleOutPpi->SetAttribute (EFI_TEXT_ATTR(PROGRESS_CODE_FOREGROUND, PROGRESS_CODE_BACKGROUND));
        
        UnicodeSPrintAsciiFormat (CheckPointString, sizeof(CheckPointString), "0x%X", CheckPointCode);
        
        // Add the Progress Code string along with CheckPoint
        UnicodeSPrintAsciiFormat (TextString, sizeof(TextString), " %s : %a \n\r", CheckPointString, ProgressCodeString);

        // Write String to ConOut Device 
        AmiEarlyConsoleOutPpi->OutputString (
                            EarlyConsoleDisplayFrameDebug,
                            TextString);
        
        // Set the Original Attribute 
        AmiEarlyConsoleOutPpi->SetAttribute (EFI_TEXT_ATTR(POST_MSG_FOREGROUND, POST_MSG_BACKGROUND));
    } else if ((CodeType & EFI_STATUS_CODE_TYPE_MASK) == EFI_ERROR_CODE) {

        // Get the Check Point value for the EFI Error Code
        CheckPointCode= GetAmiErrorCodeCheckPoint (Value);
        if (CheckPointCode == 0) {
            // If no CheckPoint found return it
            return EFI_SUCCESS;
        }
        
        // Get the Error Code String 
        Status = GetAmiErrorCodeString (
                    Value, 
                    &ErrorCodeString,
                    &PossibleCauseString,
                    &PossibleSolutionString );
        if (EFI_ERROR(Status) || (ErrorCodeString == NULL)) {
            return EFI_SUCCESS;
        }

        UnicodeSPrintAsciiFormat (CheckPointString, sizeof(CheckPointString), "0x%X", CheckPointCode);
        

        // Set the Attribute based on the Error Type
        if ((CodeType & EFI_STATUS_CODE_SEVERITY_MASK) == EFI_ERROR_MINOR) {
            AmiEarlyConsoleOutPpi->SetAttribute (EFI_TEXT_ATTR(MINOR_ERROR_FOREGROUND, MINOR_ERROR_BACKGROUND));
        } else { 
            AmiEarlyConsoleOutPpi->SetAttribute (EFI_TEXT_ATTR(MAJOR_ERROR_FOREGROUND, MAJOR_ERROR_BACKGROUND));
        }

        // Add the Error String along with CheckPoint Value
        UnicodeSPrintAsciiFormat (TextString, sizeof(TextString), " %s : %a \n \r", CheckPointString, ErrorCodeString);

        //Write the String to Conout device
        AmiEarlyConsoleOutPpi->OutputString (
                            EarlyConsoleDisplayFrameDebug,
                            TextString);
        
        if (ErrorData != NULL) { 
            // Display the Error Code Data
            UnicodeSPrintAsciiFormat (ErrorString, sizeof(ErrorString),  "%x %x %x %x ", 
                    ((*(UINT32*)(ErrorData + 1)) >> 24) & 0xFF, ((*(UINT32*)(ErrorData + 1)) >> 16) & 0xFF, 
                    ((*(UINT32*)(ErrorData + 1)) >> 8) & 0xFF, (*(UINT32*)(ErrorData + 1)) & 0xFF);
        
            AmiEarlyConsoleOutPpi->OutputString (
                                EarlyConsoleDisplayFrameDebug,
                                ErrorString);
        }
        
        //Display the Possible Cause String if available
        if (!EFI_ERROR(Status) && (PossibleCauseString != NULL)) {
            UnicodeSPrintAsciiFormat (TextString, sizeof(TextString), "%a \n \r", PossibleCauseString);
            AmiEarlyConsoleOutPpi->OutputString (
                                EarlyConsoleDisplayFrameDebug,
                                TextString);
        }
        
        //Display the Possible Solution String if available
        if (!EFI_ERROR(Status) && (PossibleSolutionString != NULL)) {
            UnicodeSPrintAsciiFormat (TextString, sizeof(TextString), "%a \n \r", PossibleSolutionString);

            AmiEarlyConsoleOutPpi->OutputString (
                                EarlyConsoleDisplayFrameDebug,
                                TextString);
        }
        
        // Set the Original Attribute 
        AmiEarlyConsoleOutPpi->SetAttribute (EFI_TEXT_ATTR(POST_MSG_FOREGROUND, POST_MSG_BACKGROUND));
    }

    return EFI_SUCCESS;
}

/**
    This function is the entry point for this PEI.
    This installs the CallBack function for the ReportStatusCode 

    @param FileHandle Pointer to image file handle.
    @param PeiServices Pointer to the PEI Core data Structure

    @retval EFI_SUCCESS Successful driver initialization

**/
EFI_STATUS
EFIAPI
InitializePeiReportStatusCode (
  IN EFI_PEI_FILE_HANDLE     FileHandle,
  IN CONST EFI_PEI_SERVICES  **PeiServices 
)
{
    EFI_STATUS              Status;
    
    Status =  (*PeiServices)->NotifyPpi(
                                    PeiServices, 
                                    gNotifyList);
    
    return Status;
    
}
