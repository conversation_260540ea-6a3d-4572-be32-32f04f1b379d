//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************


/** @file PeiVideoInit.c
    LIB driver for initializing the Video in PEI

**/
#include <PiPei.h>
#include <Library/DebugLib.h>
#include <Library/PciLib.h>
#include <Library/MemoryAllocationLib.h>
#include <Library/EarlyConsoleInterfaceCommonLib.h>
#include <Library/PeiServicesLib.h>
#include <Library/PciSegmentLib.h>
#include <IndustryStandard/Pci.h>
#include <EarlyConsoleInclude.h>
#include <EarlyConsoleElink.h>

extern PLATFORM_VGA_INIT_INTERFACE PLATFORM_VGA_INIT \
                                   EndOfPlatformVgaInitInterfaceList;
PLATFORM_VGA_INIT_INTERFACE   *gPlatformVgaInitInterfaceHook [] = 
  {  
          PLATFORM_VGA_INIT NULL 
  };

CONST EFI_PEI_PPI_DESCRIPTOR gVideoInitDoneSignalPpi = {
  (EFI_PEI_PPI_DESCRIPTOR_PPI | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST),
  &gVideoInitDonePpiGuid,
  NULL
};

UINT8 mVideoFontData[] = {
//0x20, ' '
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
//0x21, '!'
0x00, 0x00, 0x18, 0x3c, 0x3c, 0x3c, 0x18, 0x18,
0x18, 0x00, 0x18, 0x18, 0x00, 0x00, 0x00, 0x00,
//0x22, '"'
0x00, 0x66, 0x66, 0x66, 0x24, 0x00, 0x00, 0x00,
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
//0x23, '#'
0x00, 0x00, 0x00, 0x6c, 0x6c, 0xfe, 0x6c, 0x6c,
0x6c, 0xfe, 0x6c, 0x6c, 0x00, 0x00, 0x00, 0x00,
//0x24, '$'
0x18, 0x18, 0x7c, 0xc6, 0xc2, 0xc0, 0x7c, 0x06,
0x06, 0x86, 0xc6, 0x7c, 0x18, 0x18, 0x00, 0x00,
//0x25, '%'
0x00, 0x00, 0x00, 0x00, 0xc2, 0xc6, 0x0c, 0x18,
0x30, 0x60, 0xc6, 0x86, 0x00, 0x00, 0x00, 0x00,
//0x26, '&'
0x00, 0x00, 0x38, 0x6c, 0x6c, 0x38, 0x76, 0xdc,
0xcc, 0xcc, 0xcc, 0x76, 0x00, 0x00, 0x00, 0x00,
//0x27, '''
0x00, 0x30, 0x30, 0x30, 0x60, 0x00, 0x00, 0x00,
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
//0x28, '('
0x00, 0x00, 0x0c, 0x18, 0x30, 0x30, 0x30, 0x30,
0x30, 0x30, 0x18, 0x0c, 0x00, 0x00, 0x00, 0x00,
//0x29, ')'
0x00, 0x00, 0x30, 0x18, 0x0c, 0x0c, 0x0c, 0x0c,
0x0c, 0x0c, 0x18, 0x30, 0x00, 0x00, 0x00, 0x00,
//0x2a, '*'
0x00, 0x00, 0x00, 0x00, 0x00, 0x66, 0x3c, 0xff,
0x3c, 0x66, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
//0x2b, '+'
0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 0x7e,
0x18, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
//0x2c, ','
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
0x00, 0x18, 0x18, 0x18, 0x30, 0x00, 0x00, 0x00,
//0x2d, '-'
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe,
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
//0x2e, '.'
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
0x00, 0x00, 0x18, 0x18, 0x00, 0x00, 0x00, 0x00,
//0x2f, '/'
0x00, 0x00, 0x00, 0x00, 0x02, 0x06, 0x0c, 0x18,
0x30, 0x60, 0xc0, 0x80, 0x00, 0x00, 0x00, 0x00,
//0x30, '0'
0x00, 0x00, 0x3c, 0x66, 0xc3, 0xc3, 0xdb, 0xdb,
0xc3, 0xc3, 0x66, 0x3c, 0x00, 0x00, 0x00, 0x00,
//0x31, '1'
0x00, 0x00, 0x18, 0x38, 0x78, 0x18, 0x18, 0x18,
0x18, 0x18, 0x18, 0x7e, 0x00, 0x00, 0x00, 0x00,
//0x32, '2'
0x00, 0x00, 0x7c, 0xc6, 0x06, 0x0c, 0x18, 0x30,
0x60, 0xc0, 0xc6, 0xfe, 0x00, 0x00, 0x00, 0x00,
//0x33, '3'
0x00, 0x00, 0x7c, 0xc6, 0x06, 0x06, 0x3c, 0x06,
0x06, 0x06, 0xc6, 0x7c, 0x00, 0x00, 0x00, 0x00,
//0x34, '4'
0x00, 0x00, 0x0c, 0x1c, 0x3c, 0x6c, 0xcc, 0xfe,
0x0c, 0x0c, 0x0c, 0x1e, 0x00, 0x00, 0x00, 0x00,
//0x35, '5'
0x00, 0x00, 0xfe, 0xc0, 0xc0, 0xc0, 0xfc, 0x06,
0x06, 0x06, 0xc6, 0x7c, 0x00, 0x00, 0x00, 0x00,
//0x36, '6'
0x00, 0x00, 0x38, 0x60, 0xc0, 0xc0, 0xfc, 0xc6,
0xc6, 0xc6, 0xc6, 0x7c, 0x00, 0x00, 0x00, 0x00,
//0x37, '7'
0x00, 0x00, 0xfe, 0xc6, 0x06, 0x06, 0x0c, 0x18,
0x30, 0x30, 0x30, 0x30, 0x00, 0x00, 0x00, 0x00,
//0x38, '8'
0x00, 0x00, 0x7c, 0xc6, 0xc6, 0xc6, 0x7c, 0xc6,
0xc6, 0xc6, 0xc6, 0x7c, 0x00, 0x00, 0x00, 0x00,
//0x39, '9'
0x00, 0x00, 0x7c, 0xc6, 0xc6, 0xc6, 0x7e, 0x06,
0x06, 0x06, 0x0c, 0x78, 0x00, 0x00, 0x00, 0x00,
//0x3a, ':'
0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 0x00, 0x00,
0x00, 0x18, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00,
//0x3b, ';'
0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 0x00, 0x00,
0x00, 0x18, 0x18, 0x30, 0x00, 0x00, 0x00, 0x00,
//0x3c, '<'
0x00, 0x00, 0x00, 0x06, 0x0c, 0x18, 0x30, 0x60,
0x30, 0x18, 0x0c, 0x06, 0x00, 0x00, 0x00, 0x00,
//0x3d, '='
0x00, 0x00, 0x00, 0x00, 0x00, 0x7e, 0x00, 0x00,
0x7e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
//0x3e, '>'
0x00, 0x00, 0x00, 0x60, 0x30, 0x18, 0x0c, 0x06,
0x0c, 0x18, 0x30, 0x60, 0x00, 0x00, 0x00, 0x00,
//0x3f, '?'
0x00, 0x00, 0x7c, 0xc6, 0xc6, 0x0c, 0x18, 0x18,
0x18, 0x00, 0x18, 0x18, 0x00, 0x00, 0x00, 0x00,
//0x40, '@'
0x00, 0x00, 0x00, 0x7c, 0xc6, 0xc6, 0xde, 0xde,
0xde, 0xdc, 0xc0, 0x7c, 0x00, 0x00, 0x00, 0x00,
//0x41, 'A'
0x00, 0x00, 0x10, 0x38, 0x6c, 0xc6, 0xc6, 0xfe,
0xc6, 0xc6, 0xc6, 0xc6, 0x00, 0x00, 0x00, 0x00,
//0x42, 'B'
0x00, 0x00, 0xfc, 0x66, 0x66, 0x66, 0x7c, 0x66,
0x66, 0x66, 0x66, 0xfc, 0x00, 0x00, 0x00, 0x00,
//0x43, 'C'
0x00, 0x00, 0x3c, 0x66, 0xc2, 0xc0, 0xc0, 0xc0,
0xc0, 0xc2, 0x66, 0x3c, 0x00, 0x00, 0x00, 0x00,
//0x44, 'D'
0x00, 0x00, 0xf8, 0x6c, 0x66, 0x66, 0x66, 0x66,
0x66, 0x66, 0x6c, 0xf8, 0x00, 0x00, 0x00, 0x00,
//0x45, 'E'
0x00, 0x00, 0xfe, 0x66, 0x62, 0x68, 0x78, 0x68,
0x60, 0x62, 0x66, 0xfe, 0x00, 0x00, 0x00, 0x00,
//0x46, 'F'
0x00, 0x00, 0xfe, 0x66, 0x62, 0x68, 0x78, 0x68,
0x60, 0x60, 0x60, 0xf0, 0x00, 0x00, 0x00, 0x00,
//0x47, 'G'
0x00, 0x00, 0x3c, 0x66, 0xc2, 0xc0, 0xc0, 0xde,
0xc6, 0xc6, 0x66, 0x3a, 0x00, 0x00, 0x00, 0x00,
//0x48, 'H'
0x00, 0x00, 0xc6, 0xc6, 0xc6, 0xc6, 0xfe, 0xc6,
0xc6, 0xc6, 0xc6, 0xc6, 0x00, 0x00, 0x00, 0x00,
//0x49, 'I'
0x00, 0x00, 0x3c, 0x18, 0x18, 0x18, 0x18, 0x18,
0x18, 0x18, 0x18, 0x3c, 0x00, 0x00, 0x00, 0x00,
//0x4a, 'J'
0x00, 0x00, 0x1e, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c,
0xcc, 0xcc, 0xcc, 0x78, 0x00, 0x00, 0x00, 0x00,
//0x4b, 'K'
0x00, 0x00, 0xe6, 0x66, 0x66, 0x6c, 0x78, 0x78,
0x6c, 0x66, 0x66, 0xe6, 0x00, 0x00, 0x00, 0x00,
//0x4c, 'L'
0x00, 0x00, 0xf0, 0x60, 0x60, 0x60, 0x60, 0x60,
0x60, 0x62, 0x66, 0xfe, 0x00, 0x00, 0x00, 0x00,
//0x4d, 'M'
0x00, 0x00, 0xc3, 0xe7, 0xff, 0xff, 0xdb, 0xc3,
0xc3, 0xc3, 0xc3, 0xc3, 0x00, 0x00, 0x00, 0x00,
//0x4e, 'N'
0x00, 0x00, 0xc6, 0xe6, 0xf6, 0xfe, 0xde, 0xce,
0xc6, 0xc6, 0xc6, 0xc6, 0x00, 0x00, 0x00, 0x00,
//0x4f, 'O'
0x00, 0x00, 0x7c, 0xc6, 0xc6, 0xc6, 0xc6, 0xc6,
0xc6, 0xc6, 0xc6, 0x7c, 0x00, 0x00, 0x00, 0x00,
//0x50, 'P'
0x00, 0x00, 0xfc, 0x66, 0x66, 0x66, 0x7c, 0x60,
0x60, 0x60, 0x60, 0xf0, 0x00, 0x00, 0x00, 0x00,
//0x51, 'Q'
0x00, 0x00, 0x7c, 0xc6, 0xc6, 0xc6, 0xc6, 0xc6,
0xc6, 0xd6, 0xde, 0x7c, 0x0c, 0x0e, 0x00, 0x00,
//0x52, 'R'
0x00, 0x00, 0xfc, 0x66, 0x66, 0x66, 0x7c, 0x6c,
0x66, 0x66, 0x66, 0xe6, 0x00, 0x00, 0x00, 0x00,
//0x53, 'S'
0x00, 0x00, 0x7c, 0xc6, 0xc6, 0x60, 0x38, 0x0c,
0x06, 0xc6, 0xc6, 0x7c, 0x00, 0x00, 0x00, 0x00,
//0x54, 'T'
0x00, 0x00, 0xff, 0xdb, 0x99, 0x18, 0x18, 0x18,
0x18, 0x18, 0x18, 0x3c, 0x00, 0x00, 0x00, 0x00,
//0x55, 'U'
0x00, 0x00, 0xc6, 0xc6, 0xc6, 0xc6, 0xc6, 0xc6,
0xc6, 0xc6, 0xc6, 0x7c, 0x00, 0x00, 0x00, 0x00,
//0x56, 'V'
0x00, 0x00, 0xc3, 0xc3, 0xc3, 0xc3, 0xc3, 0xc3,
0xc3, 0x66, 0x3c, 0x18, 0x00, 0x00, 0x00, 0x00,
//0x57, 'W'
0x00, 0x00, 0xc3, 0xc3, 0xc3, 0xc3, 0xc3, 0xdb,
0xdb, 0xff, 0x66, 0x66, 0x00, 0x00, 0x00, 0x00,
//0x58, 'X'
0x00, 0x00, 0xc3, 0xc3, 0x66, 0x3c, 0x18, 0x18,
0x3c, 0x66, 0xc3, 0xc3, 0x00, 0x00, 0x00, 0x00,
//0x59, 'Y'
0x00, 0x00, 0xc3, 0xc3, 0xc3, 0x66, 0x3c, 0x18,
0x18, 0x18, 0x18, 0x3c, 0x00, 0x00, 0x00, 0x00,
//0x5a, 'Z'
0x00, 0x00, 0xff, 0xc3, 0x86, 0x0c, 0x18, 0x30,
0x60, 0xc1, 0xc3, 0xff, 0x00, 0x00, 0x00, 0x00,
//0x5b, '['
0x00, 0x00, 0x3c, 0x30, 0x30, 0x30, 0x30, 0x30,
0x30, 0x30, 0x30, 0x3c, 0x00, 0x00, 0x00, 0x00,
//0x5c, '\'
0x00, 0x00, 0x00, 0x80, 0xc0, 0xe0, 0x70, 0x38,
0x1c, 0x0e, 0x06, 0x02, 0x00, 0x00, 0x00, 0x00,
//0x5d, ']'
0x00, 0x00, 0x3c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c,
0x0c, 0x0c, 0x0c, 0x3c, 0x00, 0x00, 0x00, 0x00,
//0x5e, '^'
0x10, 0x38, 0x6c, 0xc6, 0x00, 0x00, 0x00, 0x00,
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
//0x5f, '_'
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00,
//0x60, '`'
0x30, 0x30, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00,
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
//0x61, 'a'
0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 0x0c, 0x7c,
0xcc, 0xcc, 0xcc, 0x76, 0x00, 0x00, 0x00, 0x00,
//0x62, 'b'
0x00, 0x00, 0xe0, 0x60, 0x60, 0x78, 0x6c, 0x66,
0x66, 0x66, 0x66, 0x7c, 0x00, 0x00, 0x00, 0x00,
//0x63, 'c'
0x00, 0x00, 0x00, 0x00, 0x00, 0x7c, 0xc6, 0xc0,
0xc0, 0xc0, 0xc6, 0x7c, 0x00, 0x00, 0x00, 0x00,
//0x64, 'd'
0x00, 0x00, 0x1c, 0x0c, 0x0c, 0x3c, 0x6c, 0xcc,
0xcc, 0xcc, 0xcc, 0x76, 0x00, 0x00, 0x00, 0x00,
//0x65, 'e'
0x00, 0x00, 0x00, 0x00, 0x00, 0x7c, 0xc6, 0xfe,
0xc0, 0xc0, 0xc6, 0x7c, 0x00, 0x00, 0x00, 0x00,
//0x66, 'f'
0x00, 0x00, 0x38, 0x6c, 0x64, 0x60, 0xf0, 0x60,
0x60, 0x60, 0x60, 0xf0, 0x00, 0x00, 0x00, 0x00,
//0x67, 'g'
0x00, 0x00, 0x00, 0x00, 0x00, 0x76, 0xcc, 0xcc,
0xcc, 0xcc, 0xcc, 0x7c, 0x0c, 0xcc, 0x78, 0x00,
//0x68, 'h'
0x00, 0x00, 0xe0, 0x60, 0x60, 0x6c, 0x76, 0x66,
0x66, 0x66, 0x66, 0xe6, 0x00, 0x00, 0x00, 0x00,
//0x69, 'i'
0x00, 0x00, 0x18, 0x18, 0x00, 0x38, 0x18, 0x18,
0x18, 0x18, 0x18, 0x3c, 0x00, 0x00, 0x00, 0x00,
//0x6a, 'j'
0x00, 0x00, 0x06, 0x06, 0x00, 0x0e, 0x06, 0x06,
0x06, 0x06, 0x06, 0x06, 0x66, 0x66, 0x3c, 0x00,
//0x6b, 'k'
0x00, 0x00, 0xe0, 0x60, 0x60, 0x66, 0x6c, 0x78,
0x78, 0x6c, 0x66, 0xe6, 0x00, 0x00, 0x00, 0x00,
//0x6c, 'l'
0x00, 0x00, 0x38, 0x18, 0x18, 0x18, 0x18, 0x18,
0x18, 0x18, 0x18, 0x3c, 0x00, 0x00, 0x00, 0x00,
//0x6d, 'm'
0x00, 0x00, 0x00, 0x00, 0x00, 0xe6, 0xff, 0xdb,
0xdb, 0xdb, 0xdb, 0xdb, 0x00, 0x00, 0x00, 0x00,
//0x6e, 'n'
0x00, 0x00, 0x00, 0x00, 0x00, 0xdc, 0x66, 0x66,
0x66, 0x66, 0x66, 0x66, 0x00, 0x00, 0x00, 0x00,
//0x6f, 'o'
0x00, 0x00, 0x00, 0x00, 0x00, 0x7c, 0xc6, 0xc6,
0xc6, 0xc6, 0xc6, 0x7c, 0x00, 0x00, 0x00, 0x00,
//0x70, 'p'
0x00, 0x00, 0x00, 0x00, 0x00, 0xdc, 0x66, 0x66,
0x66, 0x66, 0x66, 0x7c, 0x60, 0x60, 0xf0, 0x00,
//0x71, 'q'
0x00, 0x00, 0x00, 0x00, 0x00, 0x76, 0xcc, 0xcc,
0xcc, 0xcc, 0xcc, 0x7c, 0x0c, 0x0c, 0x1e, 0x00,
//0x72, 'r'
0x00, 0x00, 0x00, 0x00, 0x00, 0xdc, 0x76, 0x66,
0x60, 0x60, 0x60, 0xf0, 0x00, 0x00, 0x00, 0x00,
//0x73, 's'
0x00, 0x00, 0x00, 0x00, 0x00, 0x7c, 0xc6, 0x60,
0x38, 0x0c, 0xc6, 0x7c, 0x00, 0x00, 0x00, 0x00,
//0x74, 't'
0x00, 0x00, 0x10, 0x30, 0x30, 0xfc, 0x30, 0x30,
0x30, 0x30, 0x36, 0x1c, 0x00, 0x00, 0x00, 0x00,
//0x75, 'u'
0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xcc, 0xcc,
0xcc, 0xcc, 0xcc, 0x76, 0x00, 0x00, 0x00, 0x00,
//0x76, 'v'
0x00, 0x00, 0x00, 0x00, 0x00, 0xc3, 0xc3, 0xc3,
0xc3, 0x66, 0x3c, 0x18, 0x00, 0x00, 0x00, 0x00,
//0x77, 'w'
0x00, 0x00, 0x00, 0x00, 0x00, 0xc3, 0xc3, 0xc3,
0xdb, 0xdb, 0xff, 0x66, 0x00, 0x00, 0x00, 0x00,
//0x78, 'x'
0x00, 0x00, 0x00, 0x00, 0x00, 0xc3, 0x66, 0x3c,
0x18, 0x3c, 0x66, 0xc3, 0x00, 0x00, 0x00, 0x00,
//0x79, 'y'
0x00, 0x00, 0x00, 0x00, 0x00, 0xc6, 0xc6, 0xc6,
0xc6, 0xc6, 0xc6, 0x7e, 0x06, 0x0c, 0xf8, 0x00,
//0x7a, 'z'
0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xcc, 0x18,
0x30, 0x60, 0xc6, 0xfe, 0x00, 0x00, 0x00, 0x00,
};

FONT_MAP_INFO mVideoFontMapInfo = {
    mVideoFontData,
    0x20, //start character ' ' (space)
    0x5b, //character count -- includes all characters from ' ' to 'z' (see below)
    0x10, //16-bits per character.
    0x00, //use the first font table.
};

/** @internal
    Initialize platform Vga controller.

    @param[in]  parameters  VGA parameter table.
    @param[in]  vBus        VGA device bus number

    @retval EFI_SUCCESS VGA device are successfully initialized.
    @retval Others      Return status

*/
EFI_STATUS
VgaInit (
  IN VIDEO_PARAMETERS    *parameters,
  IN UINT8               vBus
  )
{
    EFI_STATUS          Status;
    UINT8               Index;
    
    for (Index = 0; gPlatformVgaInitInterfaceHook[Index]; Index++) {
        Status = gPlatformVgaInitInterfaceHook[Index] (parameters, vBus);
        DEBUG ((DEBUG_INFO, "gPlatformVgaInitInterfaceHook[%d] Status = %r\n", Index, Status));
        if (Status == EFI_SUCCESS) {
            return EFI_SUCCESS;;
        }
    }

    return EFI_INVALID_PARAMETER;
}

/**
    Program BARs in the video controller and apertures in the upstream bridges

    @param VIDEO_PARAMETERS structure specifying parameters for video initialization
    @param vBus - bus number assigned to the video controller

    @retval VIDEO_SUCCESS if everything worked, one of the video error codes if something did not work or bad input was provided.
 **/

EFI_STATUS 
AssignResources (
    IN  VIDEO_PARAMETERS  *parameters,
    IN  UINT8             vBus )
{
    UINT32   NextMemAddr;
    UINT32   NextIoAddr;
    UINT32   MemLimit;
    UINT32   IoLimit;
    UINT32   CurrentReqSize;
    UINT32   data32;
    UINT16   barOffset;
    UINT8    vDev;
    UINT8    vFunc;
    UINT8    bus;
    UINT8    devPathIdx;
    UINT64   Segment;
    DEV_PATH *DevicePath = (DEV_PATH*)(UINTN)parameters->DevPath;

    //
    // Sanity check on input.
    //
    if ((parameters->MemBase & 0xFFFFF) != 0) return EFI_INVALID_PARAMETER;
    if ((parameters->MemSizeMax & 0xFFFFF) != 0) return EFI_INVALID_PARAMETER;
    if ((parameters->IoBase & 0xFFF) != 0) return EFI_INVALID_PARAMETER;
    if ((parameters->IoSizeMax & 0xFFF) != 0) return EFI_INVALID_PARAMETER;

    NextMemAddr = parameters->MemBase;
    NextIoAddr = parameters->IoBase;
	MemLimit = parameters->MemBase + parameters->MemSizeMax;
	IoLimit = (UINT32)parameters->IoBase + (UINT32)parameters->IoSizeMax;
	
    // Actual MemBase & IoBase will be updated below after granularity alignment.
    parameters->MemBase = 0;
    parameters->IoBase = 0;
    
    //
    // Determine Resource requirements and assign resources to the video device (last device in the path).
    //
    vDev = DevicePath[parameters->DevPathEntries - 1].Dev;
    vFunc = DevicePath[parameters->DevPathEntries - 1].Func;
    Segment = DevicePath[parameters->DevPathEntries - 1].Segment;
    
    data32 = PciSegmentRead32 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, vBus, vDev, vFunc, 0));

    for (barOffset = PCI_BASE_ADDRESSREG_OFFSET; barOffset < PCI_CARDBUS_CIS_OFFSET; barOffset += 4) {
        //
        //Write all FFs to the bar to determine bar size
        //
        PciSegmentWrite32 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, vBus, vDev, vFunc, barOffset), 0xFFFFFFFF);
        data32 = PciSegmentRead32 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, vBus, vDev, vFunc, barOffset));

        //
        //Skip if BAR is not used.
        //
        if (data32 == 0x0) continue;

        CurrentReqSize = ~(data32 & 0xFFFFFFF0);
        //
        // Determine placement for this bar.
        //
        if ((data32 & 0x01) == 0) { // memory bar
            if ((NextMemAddr & CurrentReqSize) != 0 ) {
                //Align the BAR address to meet granularity requirement
                NextMemAddr = (NextMemAddr + CurrentReqSize + 1) & ~CurrentReqSize;
            }

            // Update the first aligned MemBase address to use for early video.
            if (parameters->MemBase == 0) {
                parameters->MemBase = NextMemAddr;
            }

            data32 &= 0xF;
            data32 |= NextMemAddr;

            NextMemAddr+=CurrentReqSize+1;    
            if (NextMemAddr > MemLimit) {
                return EFI_OUT_OF_RESOURCES;
            }
        } else {    //I/O bar
            if ((NextIoAddr & CurrentReqSize) != 0 ) {
                //Align the BAR address to meet granularity requirement
                NextIoAddr = (NextIoAddr + CurrentReqSize + 1) & ~CurrentReqSize;
            }

            // Update the first aligned IoBase address to use for early video.
            if (parameters->IoBase == 0) {
                parameters->IoBase = (UINT16) NextIoAddr;
            }

            data32 &= 0xF;
            data32 |= NextIoAddr;
            NextIoAddr+=CurrentReqSize+1;
            if (NextIoAddr > IoLimit) {
                return EFI_OUT_OF_RESOURCES;
            }
	    }
        //
        // Program the BAR.
        //
        PciSegmentWrite32 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, vBus, vDev, vFunc, barOffset), data32);

        //
        // Deal with 64-bit memory BARs -- need to zero the upper half.
        //
        if ((data32 & 0x01) == 0 && ((data32 >> 1) & 0x03) == 0x2) {
            barOffset+=4;
            PciSegmentWrite32((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, vBus, vDev, vFunc, barOffset), 0);
        }

    }

    //
    // Now program the apertures for all upstream bridges.
    //
    for (devPathIdx = 0; devPathIdx < parameters->DevPathEntries - 1; devPathIdx++) {

        bus = (UINT8)DevicePath[devPathIdx].Bus;
        vDev = DevicePath[devPathIdx].Dev;
        vFunc = DevicePath[devPathIdx].Func;
        Segment = DevicePath[devPathIdx].Segment;

        //
        // Program memory aperture (or close the aperture if no memory required).
        //
        if (parameters->MemBase != 0) {
            PciSegmentWrite16 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, vDev, vFunc, PCI_MEMBASE), (UINT16)((parameters->MemBase>> 16) & 0xFFF0));
            PciSegmentWrite16 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, vDev, vFunc, PCI_MEMLIMIT), (UINT16)((NextMemAddr >> 16) & 0xFFF0));
        } else {
            PciSegmentWrite16 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, vDev, vFunc, PCI_MEMBASE), 0xFFF0);
            PciSegmentWrite16 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, vDev, vFunc, PCI_MEMLIMIT), 0);
        }

        //
        // Program I/O aperture (or close if not required).
        //
        if (parameters->IoBase != 0) {
            PciSegmentWrite8 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, vDev, vFunc, PCI_IOBASE), (UINT8)((parameters->IoBase >> 8) & 0xF0));
            PciSegmentWrite8 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, vDev, vFunc, PCI_IOLIMIT), (UINT8)((NextIoAddr >> 8) & 0xF0));
            PciSegmentWrite16 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, vDev, vFunc, PCI_IOBASE_U), 0);
            PciSegmentWrite16 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, vDev, vFunc, PCI_IOLIMIT_U), 0);
        } else {
            PciSegmentWrite8 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, vDev, vFunc, PCI_IOBASE), 0xF0);
            PciSegmentWrite8 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, vDev, vFunc, PCI_IOLIMIT), 0x00);
            PciSegmentWrite16 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, vDev, vFunc, PCI_IOBASE_U), 0xFF);
            PciSegmentWrite16 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, vDev, vFunc, PCI_IOLIMIT_U), 0);
        }

        //
        // Close the prefetchable aperture (all memory assigned into non-prefetchable)
        //
        PciSegmentWrite16 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, vDev, vFunc, PCI_PRE_MEMBASE), 0xFFF0);
        PciSegmentWrite16 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, vDev, vFunc, PCI_PRE_MEMLIMIT), 0);
        PciSegmentWrite32 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, vDev, vFunc, PCI_PRE_MEMBASE_U), 0xFFFFFFFF);
        PciSegmentWrite32 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, vDev, vFunc, PCI_PRE_MEMLIMIT_U), 0);

    }

    return EFI_SUCCESS;
}
/**
    Enables legacy VGA decode down to the video controller
 
    @param parameters - VIDEO_PARAMETERS structure specifying parameters for video initialization
 
    @retval VIDEO_SUCCESS if everything worked, one of the video error codes if something did not work or bad input was provided.
 **/

EFI_STATUS 
EnableVgaDecode (
    IN  VIDEO_PARAMETERS  *parameters )
{
    EFI_STATUS        Status;
    UINT8             bus;
    UINT8             dev;
    UINT8             func;
    UINT8             devPathIdx;
    UINT16            data16;
    UINT64            Segment;
    DEV_PATH          *DevicePath = (DEV_PATH*)(UINTN)parameters->DevPath;

    //
    // Iterate through all the bridges in the dev path and enable Mem/IO decode and VGA decode.
    //
    for (devPathIdx = 0; devPathIdx < parameters->DevPathEntries - 1; devPathIdx++) {
        bus = (UINT8)DevicePath[devPathIdx].Bus;
        dev = DevicePath[devPathIdx].Dev;
        func = DevicePath[devPathIdx].Func;
        Segment = DevicePath[devPathIdx].Segment;

        //
        //Enable VGA decode
        //
        data16 = PciSegmentRead16 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, dev, func, PCI_BRIDGE_CONTROL_REGISTER_OFFSET));
        data16 |= EFI_PCI_BRIDGE_CONTROL_VGA;
        PciSegmentWrite16 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, dev, func, PCI_BRIDGE_CONTROL_REGISTER_OFFSET), data16);

        //
        //Enable Mem & IO decode
        //
        data16 = PciSegmentRead16 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment,bus, dev, func, PCI_COMMAND_OFFSET));
        data16 |= (EFI_PCI_COMMAND_MEMORY_SPACE | EFI_PCI_COMMAND_IO_SPACE);
        PciSegmentWrite16 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, dev, func, PCI_COMMAND_OFFSET), data16);
    }

    //
    //Enable Mem & IO decode for endpoint VGA device
    //
    bus = (UINT8)DevicePath[devPathIdx].Bus;
    dev = DevicePath[devPathIdx].Dev;
    func = DevicePath[devPathIdx].Func;
    Segment = DevicePath[devPathIdx].Segment;

    data16 = PciSegmentRead16 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, dev, func, PCI_COMMAND_OFFSET));
    data16 |= (EFI_PCI_COMMAND_MEMORY_SPACE | EFI_PCI_COMMAND_IO_SPACE);
    PciSegmentWrite16 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, dev, func, PCI_COMMAND_OFFSET), data16);

    Status = PlatformVgaEnable ((EFI_PEI_SERVICES**)(UINTN)parameters->PeiServices, bus + ((UINT8)(Segment&0xF)<< 8));

    return Status;
}

/**
    Assigns bus numbers along the device path from the root bus to the video controller.
 
    @param parameters - VIDEO_PARAMETERS structure specifying parameters for video initialization
    @param VideoBus - pointer to a UINT8 which will be updated with the bus number assigned to the video controller.
 
    @retval VIDEO_SUCCESS if everything worked, one of the video error codes if something did not work or bad input was provided.
 **/

EFI_STATUS 
AssignBusNumbers (
    IN  VIDEO_PARAMETERS  *parameters,
    IN  UINT8             *VideoBus )
{
    UINT8   bus;
    UINT8   dev;
    UINT8   func;
    UINT8   devPathIdx;
    UINT8   data8;
    UINT8   Sbus;
    UINT8   SubBus;
    UINT16  data16;
    UINT8   PrimaryBus;
    UINT64  Segment;
    DEV_PATH  *DevicePath = (DEV_PATH*)(UINTN)parameters->DevPath;

    //
    // Iterate through the bridges in the device path and assign bus numbers
    //
    for (devPathIdx = 0; devPathIdx < parameters->DevPathEntries-1; devPathIdx++) {
        bus = (UINT8)DevicePath[devPathIdx].Bus;
        dev = DevicePath[devPathIdx].Dev;
        func = DevicePath[devPathIdx].Func;
        Segment = DevicePath[devPathIdx].Segment;
        DEBUG((DEBUG_INFO,"[Early Video] Assign Bus Number 1:Seg:%lx Bus: %x | Dev: %x | Fun %x, Dp.Bus:%x \n",Segment, bus, dev, func,DevicePath[devPathIdx].Bus));
        //
        // Read header type and validate that this is a bridge
        //

        //Find primary bus number
        PrimaryBus = PciSegmentRead8 ((UINT64) PCI_SEGMENT_LIB_ADDRESS(Segment, bus, dev, func, PCI_BRIDGE_PRIMARY_BUS_REGISTER_OFFSET));
        
        //Assign bus number to enable IDs
        PciSegmentWrite8 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, dev, func, PCI_BRIDGE_PRIMARY_BUS_REGISTER_OFFSET), bus);
        //Read Vendor Id
        data16 = PciSegmentRead16 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, dev, func, PCI_VENDOR_ID_OFFSET));
        if (data16 == 0xFFFF) {
            DEBUG((DEBUG_ERROR, "Error: Vendor ID is not valid %x \n", data16));
            //Reassign old primary bus number
            PciSegmentWrite8 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, dev, func, PCI_BRIDGE_PRIMARY_BUS_REGISTER_OFFSET), PrimaryBus);
            return EFI_INVALID_PARAMETER;
        }
        //Read Device Id
        data16 = PciSegmentRead16 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, dev, func, PCI_DEVICE_ID_OFFSET));
        if (data16 == 0xFFFF) {
            DEBUG((DEBUG_ERROR, "Error: Device ID is not valid %x \n", data16));
            PciSegmentWrite8 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, dev, func, PCI_BRIDGE_PRIMARY_BUS_REGISTER_OFFSET), PrimaryBus);
            return EFI_INVALID_PARAMETER;
        }

        //check if the device is a bridge or not
        data8 = PciSegmentRead8 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, dev, func, PCI_HEADER_TYPE_OFFSET));
        if ( (data8 & 0x7f) != 0x01) {
            DEBUG((DEBUG_ERROR, "Error: Device is not bridge %x \n", data8));
            PciSegmentWrite8 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, dev, func, PCI_BRIDGE_PRIMARY_BUS_REGISTER_OFFSET), PrimaryBus);
            return EFI_INVALID_PARAMETER;
        }

        if (bus == (UINT8)DevicePath[0].Bus) {
            Sbus = (UINT8)DevicePath[1].Bus;
            SubBus = (UINT8)DevicePath[2].Bus; 
        } else if (bus == (UINT8)DevicePath[1].Bus) {
            Sbus = (UINT8)DevicePath[2].Bus;
            SubBus = (UINT8)DevicePath[2].Bus;
        } else {
            continue;
        }

        //Assign primary bus number
        PciSegmentWrite8 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, dev, func, PCI_BRIDGE_PRIMARY_BUS_REGISTER_OFFSET), bus);

        //Assign secondary and subordinate bus numbers
        PciSegmentWrite8 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, dev, func, PCI_BRIDGE_SECONDARY_BUS_REGISTER_OFFSET), Sbus);
        PciSegmentWrite8 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, dev, func, PCI_BRIDGE_SUBORDINATE_BUS_REGISTER_OFFSET), SubBus);

    }
    DEBUG((DEBUG_INFO, "[Early Video] Assign Bus Number 2: Bus: %x | Dev: %x | Fun %x, DP.Bus:%x \n", bus, dev, func,DevicePath[devPathIdx].Bus));
    //
    // Read header type and validate that this is an endpoint device
    //
    bus = (UINT8)DevicePath[devPathIdx].Bus;
    dev = DevicePath[devPathIdx].Dev;
    func = DevicePath[devPathIdx].Func;
    Segment = DevicePath[devPathIdx].Segment;

    data8 = PciSegmentRead8 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, dev, func, PCI_HEADER_TYPE_OFFSET));
    data16 = PciSegmentRead16 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, dev, func, PCI_VENDOR_ID_OFFSET));
    data16 = PciSegmentRead16 ((UINT64)PCI_SEGMENT_LIB_ADDRESS(Segment, bus, dev, func, PCI_DEVICE_ID_OFFSET));

    if ((data8 & 0x7f) != 0x00) {
        return EFI_INVALID_PARAMETER;
    }

    *VideoBus = bus;

    return EFI_SUCCESS;
}

/**
    Initializes video controller with VGA standard init. 

    @param   parameters : Pointer to Video Parameter 

    @retval  EFI_NOT_FOUND
**/
EFI_STATUS
VideoInit (
    IN  VIDEO_PARAMETERS    *parameters )
{
    UINT8           vBus;
    EFI_STATUS      Status;

    Status = AssignBusNumbers (parameters, &vBus);
    if (EFI_ERROR(Status)) {
        return Status;
    }
    
    Status = EnableVgaDecode (parameters);
    if (EFI_ERROR(Status)) {
        return Status;
    }

    Status = AssignResources (parameters, vBus);
    if (EFI_ERROR(Status)) {
        return Status;
    }

    Status = VgaInit (parameters, vBus);
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR,"%a(): VgaInit Status:%r", __FUNCTION__, Status));
        return Status;
    }

    PlatformClearBusNumbers (parameters);

    return EFI_SUCCESS;
}

/**
    Initializes video controller with VGA standard init. 

    @param   PeiServices                    : Pointer to PeiServices 
    @param   EarlyConsoleVideoInitMode      : Video mode to initialize

    @retval  EFI_NOT_FOUND
**/
EFI_STATUS 
AmiVideoInit(
    IN  EFI_PEI_SERVICES    **PeiServices,
    IN  UINT32              EarlyConsoleVideoInitMode
)
{

    EFI_STATUS                     Status;
    VIDEO_PARAMETERS               *DevInfo;

    if (EarlyConsoleVideoInitMode != FixedPcdGet32 (AmiPcdEarlyConsoleVideoMode)) {
        DEBUG ((DEBUG_ERROR, "%a : Default Video mode is [%d]. Configure AmiPcdEarlyConsoleVideoMode PCD for the intended Video mode!!!\n",
                             __FUNCTION__, EarlyConsoleVideoInitMode));
        return EFI_UNSUPPORTED;
    }

    DevInfo = AllocateZeroPool (sizeof(VIDEO_PARAMETERS));

    if (DevInfo == NULL) {
        DEBUG ((DEBUG_ERROR, "%a : Failed to allocate Memory for VIDEO_PARAMETERS!!!\n", __FUNCTION__));
        return EFI_OUT_OF_RESOURCES;    
    }

    DevInfo->PeiServices        = (EFI_PHYSICAL_ADDRESS)(UINTN)PeiServices;
    DevInfo->FontMap            = (EFI_PHYSICAL_ADDRESS)(UINTN)&mVideoFontMapInfo;  //Pointer to a buffer of font
                                                        //map information structures that define the font map(s) to load.
    DevInfo->FontMapCount       = 1;                   //Count of entries in the preceding buffer.
    DevInfo->Mode               = FixedPcdGet32 (AmiPcdEarlyConsoleVideoMode);
    DevInfo->DisplayResolution  = FixedPcdGet32 (AmiPcdEarlyConsoleVideoDisplayResolution);

    //Initialize Video Parameters
    Status = PlatformVideoParameterInit (DevInfo);
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR,"%a(): PlatformVideoParameterInit Status:%r", __FUNCTION__, Status));
        return Status;
    }

    // Initialize VGA Hardware
    Status = VideoInit (DevInfo);

    if (!EFI_ERROR(Status)) {
        Status = PeiServicesInstallPpi( &gVideoInitDoneSignalPpi);
    }

    return Status;
}


