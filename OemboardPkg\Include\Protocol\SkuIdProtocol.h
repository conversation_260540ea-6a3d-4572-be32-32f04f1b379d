/** @file
  SKU ID Protocol Definition

  This protocol provides access to the platform SKU ID information.

  Copyright (c) 2024, Compal Electronics Inc. All rights reserved.
  
**/

#ifndef _SKU_ID_PROTOCOL_H_
#define _SKU_ID_PROTOCOL_H_

#include <Uefi.h>

//
// SKU ID Protocol GUID
// {9940F1EA-4A16-4955-ABCB-60D68C2311F3}
//
#define OEMBOARD_PKG_SKU_ID_GUID \
  { \
    0x9940f1ea, 0x4a16, 0x4955, { 0xab, 0xcb, 0x60, 0xd6, 0x8c, 0x23, 0x11, 0xf3 } \
  }

extern EFI_GUID gOemboardPkgSkuIdGuid;

//
// SKU ID Protocol Structure
//
typedef struct {
  UINT8   SkuId;    ///< Platform SKU ID value
} SKU_ID_PROTOCOL;

#endif // _SKU_ID_PROTOCOL_H_
