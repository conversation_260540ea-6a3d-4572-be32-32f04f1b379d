/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */
//This file is auto generated, do not edit it manually


#include <Uefi.h>
#include <Base.h>
#include <Library/DebugLib.h>
#include <AmdCbsVariable.h>
#include <Porting.h>
#include <Protocol/AmdApcbProtocol.h>
#include <Addendum/Apcb/Inc/BRH/APCB.h>

EFI_STATUS
UpdateCbsApcbTokens (
  VOID *CbsVariable,
  AMD_APCB_SERVICE_PROTOCOL *ApcbProtocol
  )
{
  EFI_STATUS                            Status;
  CBS_CONFIG                            *Setup_Config;
  FP_SET_TOKEN_BOOL                     ApcbSetTokenBool;                 ///< Set an APCB BOOL token
  FP_SET_TOKEN_8                        ApcbSetToken8;                    ///< Set an APCB UINT8 token
  FP_SET_TOKEN_16                       ApcbSetToken16;                   ///< Set an APCB UINT16 token
  FP_SET_TOKEN_32                       ApcbSetToken32;                   ///< Set an APCB UINT32 token

  if (CbsVariable == NULL) {
    ASSERT (FALSE);
    return EFI_INVALID_PARAMETER;
  }

  if (ApcbProtocol == NULL) {
    ASSERT (FALSE);
    return EFI_INVALID_PARAMETER;
  }

  Status = ApcbProtocol->ApcbAcquireMutex (ApcbProtocol);
  if (EFI_ERROR (Status)) {
    return Status;
  }
  ApcbSetTokenBool = ApcbProtocol->ApcbSetTokenBool;                 ///< Set an APCB BOOL token
  ApcbSetToken8 = ApcbProtocol->ApcbSetToken8;                    ///< Set an APCB UINT8 token
  ApcbSetToken16 = ApcbProtocol->ApcbSetToken16;                   ///< Set an APCB UINT16 token
  ApcbSetToken32 = ApcbProtocol->ApcbSetToken32;                   ///< Set an APCB UINT32 token
  //Clear all token setting
  ApcbProtocol->ApcbPurgeAllTokens (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG);

  Setup_Config = (CBS_CONFIG *) CbsVariable;

  //APCB_TOKEN_UID_CBS_SYNC_SIGNATURE - token for CBS and APCB in sync
  ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CBS_SYNC_SIGNATURE, Setup_Config->Header.ApcbVariableHash);

  //Check if select Auto
  if (Setup_Config->CbsCpuSmtCtrl != 0xF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCX_SMT_CTRL, Setup_Config->CbsCpuSmtCtrl);
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsCmnCpuCpb != 1) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCX_CPB, Setup_Config->CbsCmnCpuCpb);
    }
  }

  //Check if select Auto
  if (Setup_Config->CbsDbgCpuLApicMode != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_APIC_MODE, Setup_Config->CbsDbgCpuLApicMode);
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsCmnCpuSmuPspDebugMode != 3) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_PSP_ENABLE_DEBUG_MODE, Setup_Config->CbsCmnCpuSmuPspDebugMode);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsCmnCpuPpinCtrl != 0xFF) {
      ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCX_PPIN_OPT_IN, Setup_Config->CbsCmnCpuPpinCtrl);
    }
  }

  //Check Display Condition CbsCmnCpuSmee=Enable
  if ((Setup_Config->CbsCmnCpuSmee == 0x1)) {
    ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_PSP_SEV_DISABLE, Setup_Config->CbsPspSevCtrl);  //User Input
  }

  //Check Display Condition CbsComboFlag=255,CbsCmnCpuSmee=Enable,CbsPspSevCtrl=Enable
  if ((Setup_Config->CbsComboFlag == 0xff) && (Setup_Config->CbsCmnCpuSmee == 0x1) && (Setup_Config->CbsPspSevCtrl == 0x0)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCX_MIN_SEV_ASID, Setup_Config->CbsCmnCpuSevAsidSpaceLimit);  //User Input
  }

  //Check Display Condition CbsComboFlag=255,CbsCmnCpuSmee=Enable,CbsPspSevCtrl=Enable,CbsDbgCpuSnpMemCover=Enabled|CbsDbgCpuSnpMemCover=Custom
  if (((Setup_Config->CbsComboFlag == 0xff) && (Setup_Config->CbsCmnCpuSmee == 0x1) && (Setup_Config->CbsPspSevCtrl == 0x0) && (Setup_Config->CbsDbgCpuSnpMemCover == 0x1)) ||((Setup_Config->CbsDbgCpuSnpMemCover == 0x2))) {
    //Check if select Auto
    if (Setup_Config->CbsCmnCpu64BitMMIOCoverage != 0xFF) {
      ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_RMP_COVER_64B_MMIO, Setup_Config->CbsCmnCpu64BitMMIOCoverage);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsCmnActionOnBistFailure != 0xFF) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_ACTION_ON_BIST_FAILURE, Setup_Config->CbsCmnActionOnBistFailure);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsCmnCpuPfReqThrEn != 0xFF) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCX_L3_XI_PREFETCH_REQ_THROTTLE_EN, Setup_Config->CbsCmnCpuPfReqThrEn);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_SCAN_DUMP_DEBUG_ENABLE, Setup_Config->CbsCmnCpuScanDumpDbgEn);  //User Input
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsCmnCpuMcax64BankSupport != 0xFF) {
      ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MCAX_64_BANK_EN, Setup_Config->CbsCmnCpuMcax64BankSupport);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsCpuLatencyUnderLoad != 0xFF) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCX_LUL, Setup_Config->CbsCpuLatencyUnderLoad);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsCmnCpuFP512 != 0xFF) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCX_FP512, Setup_Config->CbsCmnCpuFP512);
    }
  }

  //Check if select Auto
  if (Setup_Config->CbsCpuPstCustomP0 != 2) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCX_P0_SETTING, Setup_Config->CbsCpuPstCustomP0);
  }

  //Check Display Condition CbsComboFlag=255
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CPU_CCX_P0_FID, Setup_Config->CbsCpuPst0Fid);  //User Input
  }

  //Check Display Condition CbsComboFlag=255
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CPU_CCX_P0_VID, Setup_Config->CbsCpuPst0Vid);  //User Input
  }

  //Check Display Condition CbsCmnCpuDowncoreMode=Bitmap
  if ((Setup_Config->CbsCmnCpuDowncoreMode == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCD0_BITMAP_DOWN_CORE_CTRL, Setup_Config->CbsCmnCpuCcd0DowncoreBitMap);  //User Input
  }

  //Check Display Condition CbsCmnCpuDowncoreMode=Bitmap
  if ((Setup_Config->CbsCmnCpuDowncoreMode == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCD1_BITMAP_DOWN_CORE_CTRL, Setup_Config->CbsCmnCpuCcd1DowncoreBitMap);  //User Input
  }

  //Check Display Condition CbsCmnCpuDowncoreMode=Bitmap
  if ((Setup_Config->CbsCmnCpuDowncoreMode == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCD2_BITMAP_DOWN_CORE_CTRL, Setup_Config->CbsCmnCpuCcd2DowncoreBitMap);  //User Input
  }

  //Check Display Condition CbsCmnCpuDowncoreMode=Bitmap
  if ((Setup_Config->CbsCmnCpuDowncoreMode == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCD3_BITMAP_DOWN_CORE_CTRL, Setup_Config->CbsCmnCpuCcd3DowncoreBitMap);  //User Input
  }

  //Check Display Condition CbsCmnCpuDowncoreMode=Bitmap
  if ((Setup_Config->CbsCmnCpuDowncoreMode == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCD4_BITMAP_DOWN_CORE_CTRL, Setup_Config->CbsCmnCpuCcd4DowncoreBitMap);  //User Input
  }

  //Check Display Condition CbsCmnCpuDowncoreMode=Bitmap
  if ((Setup_Config->CbsCmnCpuDowncoreMode == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCD5_BITMAP_DOWN_CORE_CTRL, Setup_Config->CbsCmnCpuCcd5DowncoreBitMap);  //User Input
  }

  //Check Display Condition CbsCmnCpuDowncoreMode=Bitmap
  if ((Setup_Config->CbsCmnCpuDowncoreMode == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCD6_BITMAP_DOWN_CORE_CTRL, Setup_Config->CbsCmnCpuCcd6DowncoreBitMap);  //User Input
  }

  //Check Display Condition CbsCmnCpuDowncoreMode=Bitmap
  if ((Setup_Config->CbsCmnCpuDowncoreMode == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCD7_BITMAP_DOWN_CORE_CTRL, Setup_Config->CbsCmnCpuCcd7DowncoreBitMap);  //User Input
  }

  //Check Display Condition CbsCmnCpuDowncoreMode=Bitmap
  if ((Setup_Config->CbsCmnCpuDowncoreMode == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCD8_BITMAP_DOWN_CORE_CTRL, Setup_Config->CbsCmnCpuCcd8DowncoreBitMap);  //User Input
  }

  //Check Display Condition CbsCmnCpuDowncoreMode=Bitmap
  if ((Setup_Config->CbsCmnCpuDowncoreMode == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCD9_BITMAP_DOWN_CORE_CTRL, Setup_Config->CbsCmnCpuCcd9DowncoreBitMap);  //User Input
  }

  //Check Display Condition CbsCmnCpuDowncoreMode=Bitmap
  if ((Setup_Config->CbsCmnCpuDowncoreMode == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCD10_BITMAP_DOWN_CORE_CTRL, Setup_Config->CbsCmnCpuCcd10DowncoreBitMap);  //User Input
  }

  //Check Display Condition CbsCmnCpuDowncoreMode=Bitmap
  if ((Setup_Config->CbsCmnCpuDowncoreMode == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCD11_BITMAP_DOWN_CORE_CTRL, Setup_Config->CbsCmnCpuCcd11DowncoreBitMap);  //User Input
  }

  //Check Display Condition CbsCmnCpuDowncoreMode=Bitmap
  if ((Setup_Config->CbsCmnCpuDowncoreMode == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCD12_BITMAP_DOWN_CORE_CTRL, Setup_Config->CbsCmnCpuCcd12DowncoreBitMap);  //User Input
  }

  //Check Display Condition CbsCmnCpuDowncoreMode=Bitmap
  if ((Setup_Config->CbsCmnCpuDowncoreMode == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCD13_BITMAP_DOWN_CORE_CTRL, Setup_Config->CbsCmnCpuCcd13DowncoreBitMap);  //User Input
  }

  //Check Display Condition CbsCmnCpuDowncoreMode=Bitmap
  if ((Setup_Config->CbsCmnCpuDowncoreMode == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCD14_BITMAP_DOWN_CORE_CTRL, Setup_Config->CbsCmnCpuCcd14DowncoreBitMap);  //User Input
  }

  //Check Display Condition CbsCmnCpuDowncoreMode=Bitmap
  if ((Setup_Config->CbsCmnCpuDowncoreMode == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCD15_BITMAP_DOWN_CORE_CTRL, Setup_Config->CbsCmnCpuCcd15DowncoreBitMap);  //User Input
  }

  //Check Display Condition CbsCmnCpuDowncoreMode=Enablement Option
  if ((Setup_Config->CbsCmnCpuDowncoreMode == 0x0)) {
    //Check if select Auto
    if (Setup_Config->CbsCpuCcdCtrl != 0) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCX_CCD_CTRL, Setup_Config->CbsCpuCcdCtrl);
    }
  }

  //Check Display Condition CbsCmnCpuDowncoreMode=Enablement Option
  if ((Setup_Config->CbsCmnCpuDowncoreMode == 0x0)) {
    //Check if select Auto
    if (Setup_Config->CbsCpuCoreCtrl != 0) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CCX_CORE_CTRL, Setup_Config->CbsCpuCoreCtrl);
    }
  }

  //Check if select Auto
  if (Setup_Config->CbsDbgCpuGenCpuWdt != 3) {
    ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CPU_WDT_EN, Setup_Config->CbsDbgCpuGenCpuWdt);
  }

  //Check Display Condition CbsDbgCpuGenCpuWdt=Enabled
  if ((Setup_Config->CbsDbgCpuGenCpuWdt == 0x1)) {
    //Check if select Auto
    if (Setup_Config->CbsDbgCpuGenCpuWdtTimeout != 0xFFFF) {
      ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CPU_WDT_TIMEOUT, Setup_Config->CbsDbgCpuGenCpuWdtTimeout);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsDfCmnExtIpSyncFloodProp != 0xFF) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_EXT_IP_SYNC_FLOOD_PROP, Setup_Config->CbsDfCmnExtIpSyncFloodProp);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsDfCmnDisSyncFloodProp != 0xFF) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_SYNC_FLOOD_PROP, Setup_Config->CbsDfCmnDisSyncFloodProp);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsDfCmnFreezeQueueError != 3) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_FREEZE_MODULE_ON_ERROR, Setup_Config->CbsDfCmnFreezeQueueError);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsDfCmnCc6MemEncryption != 3) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_SAVE_RESTORE_MEM_ENCRYPT, Setup_Config->CbsDfCmnCc6MemEncryption);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsDfCmnCcdBwThrottleLv != 0xFF) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_CCD_BW_THROTTLE_LV, Setup_Config->CbsDfCmnCcdBwThrottleLv);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsDfDbgNumPciSegments != 0xFFFFFFFF) {
      ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_PCI_MMIO_SIZE, Setup_Config->CbsDfDbgNumPciSegments);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsDfCmnCcmThrot != 0xFF) {
      ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_DBG_GLB_TH_EN, Setup_Config->CbsDfCmnCcmThrot);
    }
  }

  //Check Display Condition CbsDfCmnCcmThrot = Enabled
  if ((Setup_Config->CbsDfCmnCcmThrot == 0x1)) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_FINE_THROT_HEAVY, Setup_Config->CbsDfCmnFineThrotHeavy);  //User Input
  }

  //Check Display Condition CbsDfCmnCcmThrot = Enabled
  if ((Setup_Config->CbsDfCmnCcmThrot == 0x1)) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_FINE_THROT_LIGHT, Setup_Config->CbsDfCmnFineThrotLight);  //User Input
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsDfCmnCleanVicFtiCmdBal != 0xFF) {
      ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_CLEAN_VIC_FTI_CMD_BAL, Setup_Config->CbsDfCmnCleanVicFtiCmdBal);
    }
  }

  //Check Display Condition CbsDfCmnCleanVicFtiCmdBal = Enabled
  if ((Setup_Config->CbsDfCmnCleanVicFtiCmdBal == 0x1)) {
    //Check if select Auto
    if (Setup_Config->CbsDfCmnReqvReqNDImbThr != 0xFF) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_REQV_REQ_ND_IMB_THR, Setup_Config->CbsDfCmnReqvReqNDImbThr);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_CXL_STRGLY_ORD_WR, Setup_Config->CbsDfCmnCxlStronglyOrderedWrites);  //User Input
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsDfCmnEnhancedPartWr != 0xFF) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_ENHANCED_PART_WR, Setup_Config->CbsDfCmnEnhancedPartWr);
    }
  }

  //Check if select Auto
  if (Setup_Config->CbsDfCmnDramNps != 7) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_DRAM_NPS, Setup_Config->CbsDfCmnDramNps);
  }

  //Check if select Auto
  if (Setup_Config->CbsDfCmnMemIntlv != 7) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_MEM_INTERLEAVING, Setup_Config->CbsDfCmnMemIntlv);
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsDfCmnMixedInterleavedMode != 0xFF) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_UMC_CXL_MIXED_INTERLEAVED_MODE, Setup_Config->CbsDfCmnMixedInterleavedMode);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsDfCmnCxlMemIntlv != 0xFF) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_CXL_MEM_INTERLEAVING, Setup_Config->CbsDfCmnCxlMemIntlv);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsDfCnliSublinkInterleaving != 0xFF) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_CXL_SUBLINK_INTERLEAVING, Setup_Config->CbsDfCnliSublinkInterleaving);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsDfCmnDramMapInversion != 3) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_INVERT_DRAM_MAP, Setup_Config->CbsDfCmnDramMapInversion);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsDfCmnCc6AllocationScheme != 0xFF) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_SYS_STORAGE_AT_TOP_OF_MEM, Setup_Config->CbsDfCmnCc6AllocationScheme);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsDfCmnGmiEncryption != 3) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_GMI_ENCRYPT, Setup_Config->CbsDfCmnGmiEncryption);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsDfCmnXGmiEncryption != 3) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_ENCRYPT, Setup_Config->CbsDfCmnXGmiEncryption);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsDfDbgXgmiLinkCfg != 3) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF3_XGMI2_LINK_CFG, Setup_Config->CbsDfDbgXgmiLinkCfg);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsDfCmn4LinkMaxXgmiSpeed != 0xFF) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_4LINK_MAX_XGMI_SPEED, Setup_Config->CbsDfCmn4LinkMaxXgmiSpeed);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsDfCmn3LinkMaxXgmiSpeed != 0xFF) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_3LINK_MAX_XGMI_SPEED, Setup_Config->CbsDfCmn3LinkMaxXgmiSpeed);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CONFIG_XGMI_CRC_SCALE, Setup_Config->CbsDfXgmiCrcScale);  //User Input
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CONFIG_XGMI_CRC_THRESHOLD, Setup_Config->CbsDfXgmiCrcThreshold);  //User Input
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsDfXgmiPresetControl != 0xFF) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_PRESET_CONTROL, Setup_Config->CbsDfXgmiPresetControl);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsDfXgmiTrainingErrMask != 0xff) {
      ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_TRAINING_ERR_MASK, Setup_Config->CbsDfXgmiTrainingErrMask);
    }
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_PRESET_P11, Setup_Config->CbsDfXgmiPresetP11);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_PRESET_P12, Setup_Config->CbsDfXgmiPresetP12);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_PRESET_P13, Setup_Config->CbsDfXgmiPresetP13);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_PRESET_P14, Setup_Config->CbsDfXgmiPresetP14);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_PRESET_P15, Setup_Config->CbsDfXgmiPresetP15);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S0_L0, Setup_Config->CbsDfXgmiInitPresetS0L0);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S0_L1, Setup_Config->CbsDfXgmiInitPresetS0L1);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S0_L2, Setup_Config->CbsDfXgmiInitPresetS0L2);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S0_L3, Setup_Config->CbsDfXgmiInitPresetS0L3);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S1_L0, Setup_Config->CbsDfXgmiInitPresetS1L0);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S1_L1, Setup_Config->CbsDfXgmiInitPresetS1L1);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S1_L2, Setup_Config->CbsDfXgmiInitPresetS1L2);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S1_L3, Setup_Config->CbsDfXgmiInitPresetS1L3);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L0_P01, Setup_Config->CbsDfXgmiTxeqS0L0P01);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L0_P23, Setup_Config->CbsDfXgmiTxeqS0L0P23);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L1_P01, Setup_Config->CbsDfXgmiTxeqS0L1P01);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L1_P23, Setup_Config->CbsDfXgmiTxeqS0L1P23);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L2_P01, Setup_Config->CbsDfXgmiTxeqS0L2P01);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L2_P23, Setup_Config->CbsDfXgmiTxeqS0L2P23);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L3_P01, Setup_Config->CbsDfXgmiTxeqS0L3P01);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L3_P23, Setup_Config->CbsDfXgmiTxeqS0L3P23);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L0_P01, Setup_Config->CbsDfXgmiTxeqS1L0P01);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L0_P23, Setup_Config->CbsDfXgmiTxeqS1L0P23);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L1_P01, Setup_Config->CbsDfXgmiTxeqS1L1P01);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L1_P23, Setup_Config->CbsDfXgmiTxeqS1L1P23);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L2_P01, Setup_Config->CbsDfXgmiTxeqS1L2P01);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L2_P23, Setup_Config->CbsDfXgmiTxeqS1L2P23);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L3_P01, Setup_Config->CbsDfXgmiTxeqS1L3P01);  //User Input
  }

  //Check Display Condition CbsDfXgmiPresetControl=Enabled
  if ((Setup_Config->CbsDfXgmiPresetControl == 0x1)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L3_P23, Setup_Config->CbsDfXgmiTxeqS1L3P23);  //User Input
  }

  //Check Display Condition CbsDfXgmiAcDcCoupledLinkControl=Manual
  if ((Setup_Config->CbsDfXgmiAcDcCoupledLinkControl == 0x0)) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_XGMI_AC_DC_COUPLED_LINK, Setup_Config->CbsDfXgmiAcDcCoupledLink);  //User Input
  }

  //Check Display Condition CbsDfXgmiChannelTypeControl=Manual
  if ((Setup_Config->CbsDfXgmiChannelTypeControl == 0x0)) {
    ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_XGMI_CHANNEL_TYPE, Setup_Config->CbsDfXgmiChannelType);  //User Input
  }

  //Check if select Auto
  if (Setup_Config->CbsDfCdma != 0xFF) {
    ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_CDMA, Setup_Config->CbsDfCdma);
  }

  //Check if select Auto
  if (Setup_Config->CbsDfDbgDisRmtSteer != 0xFF) {
    ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_CDMA_CS_CFGA4_B7, Setup_Config->CbsDfDbgDisRmtSteer);
  }

  //Check if select Auto
  if (Setup_Config->CbsDfCmnPfOrganization != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_PF_ORGANIZATION, Setup_Config->CbsDfCmnPfOrganization);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnDfPdrTuning != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_PDR_TUNING, Setup_Config->CbsCmnDfPdrTuning);
  }

  //Check if select Auto
  if (Setup_Config->CbsDfCmnMemIntlvPageSize != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_MEM_INTLV_PAGE_SIZE, Setup_Config->CbsDfCmnMemIntlvPageSize);
  }

  //Check if select Auto
  if (Setup_Config->CbsDfCmnPfPdrMode != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_PF_PDR_MODE, Setup_Config->CbsDfCmnPfPdrMode);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemCsInterleaveDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_CS_INTERLEAVE_DDR, Setup_Config->CbsCmnMemCsInterleaveDdr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemAddressHashBankDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_ADDRESS_HASH_BANK_DDR, Setup_Config->CbsCmnMemAddressHashBankDdr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemAddressHashCsDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_ADDRESS_HASH_CS_DDR, Setup_Config->CbsCmnMemAddressHashCsDdr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemAddressHashRmDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_ADDRESS_HASH_RM_DDR, Setup_Config->CbsCmnMemAddressHashRmDdr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemAddressHashSubchannelDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_ADDRESS_HASH_SUBCHANNEL_DDR, Setup_Config->CbsCmnMemAddressHashSubchannelDdr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemCtrllerBankSwapModeDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_BANK_SWAP_MODE_DDR, Setup_Config->CbsCmnMemCtrllerBankSwapModeDdr);
  }

  //Check Display Condition CbsComboFlag=10|CbsComboFlag=16
  if (((Setup_Config->CbsComboFlag == 0xa)) ||((Setup_Config->CbsComboFlag == 0x10))) {
    //Check if select Auto
    if (Setup_Config->CbsCmnMemContextRestoreDdr != 0xFF) {
      ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEMRESTORECTL, Setup_Config->CbsCmnMemContextRestoreDdr);
    }
  }

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_DRAM_SURVIVES_WARM_RESET_DDR, Setup_Config->CbsDramSurvivesWarmReset);  //User Input

  //Check if select Auto
  if (Setup_Config->CbsCmnMemCtrllerPwrDnEnDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_CTRLLER_POWER_DOWN_EN_DDR, Setup_Config->CbsCmnMemCtrllerPwrDnEnDdr);
  }

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_SUBURGREFLOWERBOUND, Setup_Config->CbsCmnMemSubUrgRefLowerBound);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_URGREFLIMIT, Setup_Config->CbsCmnMemUrgRefLimit);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DRAMDOUBLEREFRESHRATE, Setup_Config->CbsCmnMemDramRefreshRate);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_SELF_REFRESH_EXIT_STAGGERING, Setup_Config->CbsCmnMemSelfRefreshExitStaggering);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_2X_REFRESH_THRESHOLD_DDR, Setup_Config->CbsCmnMemt2xRefreshTemperatureThreshold);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_CHANNEL_DISABLE_FLOAT_POWER_GOOD_DDR, Setup_Config->CbsCmnMemChannelDisableFloatPowerGoodDdr);  //User Input

  ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_CHANNEL_DISABLE_BITMASK_DDR, Setup_Config->CbsCmnMemChannelDisableBitmaskDdr);  //User Input

  //Check if select Auto
  if (Setup_Config->CbsCmnMemRefManagementDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_REFRESH_MANAGEMENT_DDR, Setup_Config->CbsCmnMemRefManagementDdr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemArfmDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_ADAPTIVE_REFRESH_MANAGEMENT_DDR, Setup_Config->CbsCmnMemArfmDdr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemRAAIMTDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_RAAIMT_DDR, Setup_Config->CbsCmnMemRAAIMTDdr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemRAAMMTDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_RAAMMT_DDR, Setup_Config->CbsCmnMemRAAMMTDdr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemRAARefDecMultiplierDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_RAA_DEC_REF_MULT_DDR, Setup_Config->CbsCmnMemRAARefDecMultiplierDdr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemDrfmDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_DRFM_DDR, Setup_Config->CbsCmnMemDrfmDdr);
  }

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_DRFM_BRC_DDR, Setup_Config->CbsCmnMemDrfmBrcDdr);  //User Input

  //Check if select Auto
  if (Setup_Config->CbsCmnMemDrfmHashDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_DRFM_HASH_DDR, Setup_Config->CbsCmnMemDrfmHashDdr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemMbistEnDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MBIST_EN_DDR, Setup_Config->CbsCmnMemMbistEnDdr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemMbistTestmodeDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MBIST_TESTMODE_DDR, Setup_Config->CbsCmnMemMbistTestmodeDdr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemMbistAggressorsDdr != 0xff) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MBIST_AGGRESSORS_DDR, Setup_Config->CbsCmnMemMbistAggressorsDdr);
  }

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_HEALING_BIST_ENABLE_BITMASK_DDR, Setup_Config->CbsCmnMemHealingBistEnableBitMaskDdr);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_HEALING_BIST_REPAIR_TYPE_DDR, Setup_Config->CbsCmnMemHealingBistRepairTypeDdr);  //User Input

  //Check if select Auto
  if (Setup_Config->CbsCmnMemPmuBistAlgorithmSelect != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_PMU_BIST_ALGORITHM_SELECT_DDR, Setup_Config->CbsCmnMemPmuBistAlgorithmSelect);
  }

  //Check Display Condition CbsCmnMemPmuBistAlgorithmSelect=By User
  if ((Setup_Config->CbsCmnMemPmuBistAlgorithmSelect == 0x0)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_PMU_BIST_ALGORITHM_BITMASK_DDR, Setup_Config->CbsCmnMemPmuBistAlgorithmBitMaskDdr);  //User Input
  }

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MBIST_PATTERN_SELECT_DDR, Setup_Config->CbsCmnMemMbistPatternSelect);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MBIST_PATTERN_LENGTH_DDR, Setup_Config->CbsCmnMemMbistPatternLength);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MBIST_AGGRESSORS_CHNL_DDR, Setup_Config->CbsCmnMemMbistAggressorsChnl);  //User Input

  ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MBIST_AGGR_STATIC_LANE_CTRL, Setup_Config->CbsCmnMemMbistAggrStaticLaneCtrl);  //User Input

  ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MBIST_AGGR_STATIC_LANE_SEL_U32, Setup_Config->CbsCmnMemMbistAggrStaticLaneSelU32);  //User Input

  ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MBIST_AGGR_STATIC_LANE_SEL_L32, Setup_Config->CbsCmnMemMbistAggrStaticLaneSelL32);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MBIST_AGGR_STATIC_LANE_SEL_ECC, Setup_Config->CbsCmnMemMbistAggrStaticLaneSelEcc);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MBIST_AGGR_STATIC_LANE_VAL, Setup_Config->CbsCmnMemMbistAggrStaticLaneVal);  //User Input

  ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MBIST_TGT_STATIC_LANE_CTRL, Setup_Config->CbsCmnMemMbistTgtStaticLaneCtrl);  //User Input

  ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MBIST_TGT_STATIC_LANE_SEL_U32, Setup_Config->CbsCmnMemMbistTgtStaticLaneSelU32);  //User Input

  ApcbSetToken32 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MBIST_TGT_STATIC_LANE_SEL_L32, Setup_Config->CbsCmnMemMbistTgtStaticLaneSelL32);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MBIST_TGT_STATIC_LANE_SEL_ECC, Setup_Config->CbsCmnMemMbistTgtStaticLaneSelEcc);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MBIST_TGT_STATIC_LANE_VAL, Setup_Config->CbsCmnMemMbistTgtStaticLaneVal);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MBIST_READ_DATA_EYE_VOLTAGE_STEP, Setup_Config->CbsCmnMemMbistReadDataEyeVoltageStep);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MBIST_READ_DATA_EYE_TIMING_STEP, Setup_Config->CbsCmnMemMbistReadDataEyeTimingStep);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MBIST_WRITE_DATA_EYE_VOLTAGE_STEP, Setup_Config->CbsCmnMemMbistWriteDataEyeVoltageStep);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MBIST_WRITE_DATA_EYE_TIMING_STEP, Setup_Config->CbsCmnMemMbistWriteDataEyeTimingStep);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MBIST_DATAEYE_SILENT_EXECUTION_DDR, Setup_Config->CbsCmnMemMbistDataeyeSilentExecution);  //User Input

  //Check if select Auto
  if (Setup_Config->CbsCmnMemDataPoisoningDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_DATA_POISONING_DDR, Setup_Config->CbsCmnMemDataPoisoningDdr);
  }

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_BOOT_TIME_POST_PACKAGE_REPAIR_ENABLE, Setup_Config->CbsCmnMemBootTimePostPackageRepair);  //User Input

  //Check if select Auto
  if (Setup_Config->CbsCmnMemRcdParityDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_RCD_PARITY, Setup_Config->CbsCmnMemRcdParityDdr);
  }

  //Check Display Condition CbsCmnMemRcdParityDdr=Enabled
  if ((Setup_Config->CbsCmnMemRcdParityDdr == 0x1)) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MAX_RCD_PARITY_ERROR_REPLAY, Setup_Config->CbsCmnMemMaxRcdParityErrorReplayDdr);  //User Input
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemWriteCrcDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_WR_CRC_EN_DDR, Setup_Config->CbsCmnMemWriteCrcDdr);
  }

  //Check Display Condition CbsCmnMemWriteCrcDdr=Enabled
  if ((Setup_Config->CbsCmnMemWriteCrcDdr == 0x1)) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MAX_WRITE_CRC_ERROR_REPLAY, Setup_Config->CbsCmnMemMaxWriteCrcErrorReplayDdr);  //User Input
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemReadCrcDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_RD_CRC_EN_DDR, Setup_Config->CbsCmnMemReadCrcDdr);
  }

  //Check Display Condition CbsCmnMemReadCrcDdr=Enabled
  if ((Setup_Config->CbsCmnMemReadCrcDdr == 0x1)) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MAX_READ_CRC_ERROR_REPLAY, Setup_Config->CbsCmnMemMaxReadCrcErrorReplayDdr);  //User Input
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemDisMemErrInj != 0xff) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_ECC_DIS_ERR_INJECTION_DDR, Setup_Config->CbsCmnMemDisMemErrInj);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemDramEccSymbolSizeDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_DRAM_ECC_SYMBOL_SIZE_DDR, Setup_Config->CbsCmnMemDramEccSymbolSizeDdr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemDramEccEnDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_ECC_EN_DDR, Setup_Config->CbsCmnMemDramEccEnDdr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemDramUeccRetryDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_UECC_RETRY_EN_DDR, Setup_Config->CbsCmnMemDramUeccRetryDdr);
  }

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MAX_UECC_ERROR_REPLAY, Setup_Config->CbsCmnMemMaxDramUeccErrorReplayDdr);  //User Input

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsCmnMemDramMemClrDdr != 0xFF) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MEMORY_CLEAR_DDR, Setup_Config->CbsCmnMemDramMemClrDdr);
    }
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemAddrXorAfterEcc != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_ADDR_XOR_AFTER_ECC_DDR, Setup_Config->CbsCmnMemAddrXorAfterEcc);
  }

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_CIPHERTEXT_HIDE_EN_DDR, Setup_Config->CbsDbgMemCipherTextHiding);  //User Input

  //Check if select Auto
  if (Setup_Config->CbsCmnMemDramEcsModeDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_ECSFEATURE_DDR, Setup_Config->CbsCmnMemDramEcsModeDdr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemDramRedirectScrubEnDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_REDIRECT_SCRUB_DDR, Setup_Config->CbsCmnMemDramRedirectScrubEnDdr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemDramRedirectScrubLimitDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_REDIRECT_SCRUB_LIMIT_DDR, Setup_Config->CbsCmnMemDramRedirectScrubLimitDdr);
  }

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_PATROL_SCRUB_DDR, Setup_Config->CbsCmnMemDramScrubTime);  //User Input

  //Check Display Condition CbsCmnMemDramEcsModeDdr=ManualECS
  if ((Setup_Config->CbsCmnMemDramEcsModeDdr == 0x1)) {
    //Check if select Auto
    if (Setup_Config->CbsCmnMemtECSintCtrlDdr != 0xFF) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_TECSINTCTRL_DDR, Setup_Config->CbsCmnMemtECSintCtrlDdr);
    }
  }

  //Check Display Condition CbsCmnMemDramEcsModeDdr=ManualECS, CbsCmnMemtECSintCtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemDramEcsModeDdr == 0x1) && (Setup_Config->CbsCmnMemtECSintCtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_TECSINT_DDR, Setup_Config->CbsCmnMemtECSintDdr);  //User Input
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemDramEtcDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_ETC_DDR, Setup_Config->CbsCmnMemDramEtcDdr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemDramEcsCountModeDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_ECS_COUNT_MODE_DDR, Setup_Config->CbsCmnMemDramEcsCountModeDdr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemDramAutoEcsSelfRefreshDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_AUTOECS_IN_SELF_REFRESH_DDR, Setup_Config->CbsCmnMemDramAutoEcsSelfRefreshDdr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemDramEcsWritebackSuppressionDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_ECS_WRITEBACK_SUPPRESSION_DDR, Setup_Config->CbsCmnMemDramEcsWritebackSuppressionDdr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemDramX4WritebackSuppressionDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_X4_WRITEBACK_SUPPRESSION_DDR, Setup_Config->CbsCmnMemDramX4WritebackSuppressionDdr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemOdtImpedProcDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_PROC_ODT_DDR, Setup_Config->CbsCmnMemOdtImpedProcDdr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemOdtPullDownImpedProcDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_PROC_ODT_PULL_DOWN_IMP_DDR, Setup_Config->CbsCmnMemOdtPullDownImpedProcDdr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemDramDrvStrenDqDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_DRV_IMP_DDR, Setup_Config->CbsCmnMemDramDrvStrenDqDdr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemRttNomWrP0Ddr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_RTT_NOM_WR_P0_DDR, Setup_Config->CbsCmnMemRttNomWrP0Ddr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemRttNomRdP0Ddr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_RTT_NOM_RD_P0_DDR, Setup_Config->CbsCmnMemRttNomRdP0Ddr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemRttWrP0Ddr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_RTT_WR_P0_DDR, Setup_Config->CbsCmnMemRttWrP0Ddr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemRttParkP0Ddr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_RTT_PARK_P0_DDR, Setup_Config->CbsCmnMemRttParkP0Ddr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemRttParkDqsP0Ddr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_RTT_PARK_DQS_P0_DDR, Setup_Config->CbsCmnMemRttParkDqsP0Ddr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemRttNomWrP1Ddr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_RTT_NOM_WR_P1_DDR, Setup_Config->CbsCmnMemRttNomWrP1Ddr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemRttNomRdP1Ddr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_RTT_NOM_RD_P1_DDR, Setup_Config->CbsCmnMemRttNomRdP1Ddr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemRttWrP1Ddr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_RTT_WR_P1_DDR, Setup_Config->CbsCmnMemRttWrP1Ddr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemRttParkP1Ddr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_RTT_PARK_P1_DDR, Setup_Config->CbsCmnMemRttParkP1Ddr);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemRttParkDqsP1Ddr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_RTT_PARK_DQS_P1_DDR, Setup_Config->CbsCmnMemRttParkDqsP1Ddr);
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1)) {
    //Check if select Auto
    if (Setup_Config->CbsCmnMemTargetSpeedDdr != 0xFFFF) {
      ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TARGET_SPEED_DDR, Setup_Config->CbsCmnMemTargetSpeedDdr);
    }
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTclCtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTclCtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TCL_DDR, Setup_Config->CbsCmnMemTimingTclDdr);  //User Input
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTrcdCtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTrcdCtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TRCD_DDR, Setup_Config->CbsCmnMemTimingTrcdDdr);  //User Input
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTrpCtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTrpCtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TRP_DDR, Setup_Config->CbsCmnMemTimingTrpDdr);  //User Input
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTrasCtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTrasCtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TRAS_DDR, Setup_Config->CbsCmnMemTimingTrasDdr);  //User Input
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTrcCtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTrcCtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TRC_DDR, Setup_Config->CbsCmnMemTimingTrcDdr);  //User Input
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTwrCtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTwrCtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TWR_DDR, Setup_Config->CbsCmnMemTimingTwrDdr);  //User Input
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTrfc1CtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTrfc1CtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TRFC1_DDR, Setup_Config->CbsCmnMemTimingTrfc1Ddr);  //User Input
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTrfc2CtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTrfc2CtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TRFC2_DDR, Setup_Config->CbsCmnMemTimingTrfc2Ddr);  //User Input
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTrfcSbCtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTrfcSbCtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TRFCSB_DDR, Setup_Config->CbsCmnMemTimingTrfcSbDdr);  //User Input
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTcwlCtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTcwlCtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TCWL_DDR, Setup_Config->CbsCmnMemTimingTcwlDdr);  //User Input
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTrtpCtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTrtpCtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TRTP_DDR, Setup_Config->CbsCmnMemTimingTrtpDdr);  //User Input
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTrrdLCtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTrrdLCtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TRRDL_DDR, Setup_Config->CbsCmnMemTimingTrrdLDdr);  //User Input
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTrrdSCtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTrrdSCtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TRRDS_DDR, Setup_Config->CbsCmnMemTimingTrrdSDdr);  //User Input
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTfawCtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTfawCtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TFAW_DDR, Setup_Config->CbsCmnMemTimingTfawDdr);  //User Input
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTwtrLCtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTwtrLCtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TWTRL_DDR, Setup_Config->CbsCmnMemTimingTwtrLDdr);  //User Input
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTwtrSCtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTwtrSCtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TWTRS_DDR, Setup_Config->CbsCmnMemTimingTwtrSDdr);  //User Input
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTrdrdScLCtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTrdrdScLCtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TRDRDSCL_DDR, Setup_Config->CbsCmnMemTimingTrdrdScLDdr);  //User Input
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTrdrdScCtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTrdrdScCtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TRDRDSC_DDR, Setup_Config->CbsCmnMemTimingTrdrdScDdr);  //User Input
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTrdrdSdCtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTrdrdSdCtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TRDRDSD_DDR, Setup_Config->CbsCmnMemTimingTrdrdSdDdr);  //User Input
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTrdrdDdCtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTrdrdDdCtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TRDRDDD_DDR, Setup_Config->CbsCmnMemTimingTrdrdDdDdr);  //User Input
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTwrwrScLCtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTwrwrScLCtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TWRWRSCL_DDR, Setup_Config->CbsCmnMemTimingTwrwrScLDdr);  //User Input
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTwrwrScCtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTwrwrScCtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TWRWRSC_DDR, Setup_Config->CbsCmnMemTimingTwrwrScDdr);  //User Input
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTwrwrSdCtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTwrwrSdCtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TWRWRSD_DDR, Setup_Config->CbsCmnMemTimingTwrwrSdDdr);  //User Input
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTwrwrDdCtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTwrwrDdCtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TWRWRDD_DDR, Setup_Config->CbsCmnMemTimingTwrwrDdDdr);  //User Input
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTwrrdCtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTwrrdCtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TWRRD_DDR, Setup_Config->CbsCmnMemTimingTwrrdDdr);  //User Input
  }

  //Check Display Condition CbsCmnMemTimingSettingDdr=Enabled,CbsCmnMemTimingTrdwrCtrlDdr=Manual
  if ((Setup_Config->CbsCmnMemTimingSettingDdr == 0x1) && (Setup_Config->CbsCmnMemTimingTrdwrCtrlDdr == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TIMING_TRDWR_DDR, Setup_Config->CbsCmnMemTimingTrdwrDdr);  //User Input
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemDramPdaEnumIdProgModeDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_DRAM_PDA_ENUM_ID_PROG_MODE_DDR, Setup_Config->CbsCmnMemDramPdaEnumIdProgModeDdr);
  }

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_WRITE_TRAINING_BURST_LENGTH, Setup_Config->CbsCmnMemWriteTrainingBurstLength);  //User Input

  //Check if select Auto
  if (Setup_Config->CbsCmnTrainingRetryCount != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_PMU_TRAINING_RETRY_COUNT, Setup_Config->CbsCmnTrainingRetryCount);
  }

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_PPT_CTRL_DDR, Setup_Config->CbsCmnMemPeriodicTrainingModeDdr);  //User Input

  //Check Display Condition CbsCmnMemPeriodicIntervalMode=Manual
  if ((Setup_Config->CbsCmnMemPeriodicIntervalMode == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_PR_INTERVAL_DDR, Setup_Config->CbsCmnMemPeriodicInterval);  //User Input
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnMemTsmeEnableDdr != 0xFF) {
    ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TSME_ENABLE_DDR, Setup_Config->CbsCmnMemTsmeEnableDdr);
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_AES, Setup_Config->CbsCmnMemAes);  //User Input
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_DATA_CTRL_DATA_SCRAMBLE_EN_DDR, Setup_Config->CbsCmnMemDataScramble);  //User Input
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_SME_MK_ENABLE, Setup_Config->CbsCmnMemSmeMkEnable);  //User Input
  }

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_PMIC_OPERATION_MODE_DDR, Setup_Config->CbsCmnMemCtrllerPmicOpMode);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_PMIC_PERSISTENT_ERROR_DDR, Setup_Config->CbsCmnMemCtrllerPmicFaultRecovery);  //User Input

  ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_PMIC_SWA_SWB_VDDCORE_DDR, Setup_Config->CbsCmnMemCtrllerPmicSwaSwbVddCore);  //User Input

  ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_PMIC_SWC_VDDIO_DDR, Setup_Config->CbsCmnMemCtrllerPmicSwcVddio);  //User Input

  ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_PMIC_SWD_VPP_DDR, Setup_Config->CbsCmnMemCtrllerPmicSwdVpp);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_PMIC_STAGGER_DDR, Setup_Config->CbsCmnMemCtrllerPmicStaggerDelay);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_MAX_DIMM_STAGGER_DDR, Setup_Config->CbsCmnMemCtrllerMaxPmicPowerOn);  //User Input

  //Check if select Auto
  if (Setup_Config->CbsCmnMemOdtsCmdThrottleThresholdDdr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_ODTS_CMD_THROTTLE_THRESHOLD_DDR, Setup_Config->CbsCmnMemOdtsCmdThrottleThresholdDdr);
  }

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_THERMAL_THROTTLE_DDR, Setup_Config->CbsCmnTsodThermalThrottleControlDdr);  //User Input

  //Check Display Condition CbsCmnTsodThermalThrottleControlDdr=Enabled
  if ((Setup_Config->CbsCmnTsodThermalThrottleControlDdr == 0x1)) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TSOD_TT_START_TEMP_DDR, Setup_Config->CbsCmnTsodThermalThrottleStartTempDdr);  //User Input
  }

  //Check Display Condition CbsCmnTsodThermalThrottleControlDdr=Enabled
  if ((Setup_Config->CbsCmnTsodThermalThrottleControlDdr == 0x1)) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TSOD_TT_HYSTERESIS_DDR, Setup_Config->CbsCmnTsodThermalThrottleHysteresisDdr);  //User Input
  }

  //Check Display Condition CbsCmnTsodThermalThrottleControlDdr=Enabled
  if ((Setup_Config->CbsCmnTsodThermalThrottleControlDdr == 0x1)) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TSOD_CMD_THR_0_DDR, Setup_Config->CbsCmnTsodCmdThrottlePercentage0Ddr);  //User Input
  }

  //Check Display Condition CbsCmnTsodThermalThrottleControlDdr=Enabled
  if ((Setup_Config->CbsCmnTsodThermalThrottleControlDdr == 0x1)) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TSOD_CMD_THR_5_DDR, Setup_Config->CbsCmnTsodCmdThrottlePercentage5Ddr);  //User Input
  }

  //Check Display Condition CbsCmnTsodThermalThrottleControlDdr=Enabled
  if ((Setup_Config->CbsCmnTsodThermalThrottleControlDdr == 0x1)) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TSOD_CMD_THR_10_DDR, Setup_Config->CbsCmnTsodCmdThrottlePercentage10Ddr);  //User Input
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsCmnX3dStackOverride != 0xF) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_X3DSTACKOVERRIDE, Setup_Config->CbsCmnX3dStackOverride);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsCmnL3Bist != 0xF) {
      ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_L3_BIST, Setup_Config->CbsCmnL3Bist);
    }
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnEarlyLinkSpeed != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_BMC_LINK_SPEED, Setup_Config->CbsCmnEarlyLinkSpeed);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnPcieTargetLinkSpeed != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CXL_GEN_SPEED, Setup_Config->CbsCmnPcieTargetLinkSpeed);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnDrtmMemoryReservation != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_RESERVED_DRAM_MODULE_DRTM, Setup_Config->CbsCmnDrtmMemoryReservation);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnGnbNbIOMMU != 0xf) {
    ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_IOMMU, Setup_Config->CbsCmnGnbNbIOMMU);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnFchI3C0Config != 0xf) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_I2C_I3C_SMBUS_0, Setup_Config->CbsCmnFchI3C0Config);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnFchI3C1Config != 0xf) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_I2C_I3C_SMBUS_1, Setup_Config->CbsCmnFchI3C1Config);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnFchI3C2Config != 0xf) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_I2C_I3C_SMBUS_2, Setup_Config->CbsCmnFchI3C2Config);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnFchI3C3Config != 0xf) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_I2C_I3C_SMBUS_3, Setup_Config->CbsCmnFchI3C3Config);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnFchI2C4Config != 0xf) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_I2C_I3C_SMBUS_4, Setup_Config->CbsCmnFchI2C4Config);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnFchI2C5Config != 0xf) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_I2C_I3C_SMBUS_5, Setup_Config->CbsCmnFchI2C5Config);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnFchI2cSdaHoldOverride != 0xf) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_IC_SDA_HOLD_OVERRIDE, Setup_Config->CbsCmnFchI2cSdaHoldOverride);
  }

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_APML_SBTSI_SLAVE_MODE, Setup_Config->CbsCmnFchApmlSbtsiSlvMode);  //User Input

  //Check Display Condition CbsComboFlag=255
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    //Check if select Auto
    if (Setup_Config->CbsCmnFchI3cModeSpeed != 0xf) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_I3C_TRANSFER_SPEED, Setup_Config->CbsCmnFchI3cModeSpeed);
    }
  }

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_I3C_PP_HCNT, Setup_Config->CbsCmnFchI3cPpHcntValue);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_I3C_SDA_HOLD_SWITCH_DLY_TIMING, Setup_Config->CbsCmnFchI3cSdaHoldValue);  //User Input

  //Check if select Auto
  if (Setup_Config->CbsCmnFchI3cSdaHoldOverride != 0xf) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_I3C_SDA_HOLD_OVERRIDE, Setup_Config->CbsCmnFchI3cSdaHoldOverride);
  }

  ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_I2C0_SDA_TX_HOLD, Setup_Config->CbsCmnFchI2c0SdaTxHoldValue);  //User Input

  ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_I2C1_SDA_TX_HOLD, Setup_Config->CbsCmnFchI2c1SdaTxHoldValue);  //User Input

  ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_I2C2_SDA_TX_HOLD, Setup_Config->CbsCmnFchI2c2SdaTxHoldValue);  //User Input

  ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_I2C3_SDA_TX_HOLD, Setup_Config->CbsCmnFchI2c3SdaTxHoldValue);  //User Input

  ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_I2C4_SDA_TX_HOLD, Setup_Config->CbsCmnFchI2c4SdaTxHoldValue);  //User Input

  ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_I2C5_SDA_TX_HOLD, Setup_Config->CbsCmnFchI2c5SdaTxHoldValue);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_I2C0_SDA_RX_HOLD, Setup_Config->CbsCmnFchI2c0SdaRxHoldValue);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_I2C1_SDA_RX_HOLD, Setup_Config->CbsCmnFchI2c1SdaRxHoldValue);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_I2C2_SDA_RX_HOLD, Setup_Config->CbsCmnFchI2c2SdaRxHoldValue);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_I2C3_SDA_RX_HOLD, Setup_Config->CbsCmnFchI2c3SdaRxHoldValue);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_I2C4_SDA_RX_HOLD, Setup_Config->CbsCmnFchI2c4SdaRxHoldValue);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_I2C5_SDA_RX_HOLD, Setup_Config->CbsCmnFchI2c5SdaRxHoldValue);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_I3C0_SDA_HOLD, Setup_Config->CbsCmnFchI3c0SdaHoldValue);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_I3C1_SDA_HOLD, Setup_Config->CbsCmnFchI3c1SdaHoldValue);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_I3C2_SDA_HOLD, Setup_Config->CbsCmnFchI3c2SdaHoldValue);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_I3C3_SDA_HOLD, Setup_Config->CbsCmnFchI3c3SdaHoldValue);  //User Input

  //Check if select Auto
  if (Setup_Config->CbsCmnFchSataEnable != 0xf) {
    ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_SATA_ENABLE, Setup_Config->CbsCmnFchSataEnable);
  }

  //Check Display Condition CbsCmnFchSataEnable=Enabled
  if ((Setup_Config->CbsCmnFchSataEnable == 0x1)) {
    //Check if select Auto
    if (Setup_Config->CbsCmnFchSataClass != 0xf) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_NBIO_SATA_MODE, Setup_Config->CbsCmnFchSataClass);
    }
  }

  //Check if select Auto
  if (Setup_Config->CbsDbgFchSata0Enable != 0xf) {
    ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_SATA_0_ENABLE, Setup_Config->CbsDbgFchSata0Enable);
  }

  //Check if select Auto
  if (Setup_Config->CbsDbgFchSata1Enable != 0xf) {
    ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_SATA_1_ENABLE, Setup_Config->CbsDbgFchSata1Enable);
  }

  //Check if select Auto
  if (Setup_Config->CbsDbgFchSata2Enable != 0xf) {
    ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_SATA_2_ENABLE, Setup_Config->CbsDbgFchSata2Enable);
  }

  //Check if select Auto
  if (Setup_Config->CbsDbgFchSata3Enable != 0xf) {
    ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_SATA_3_ENABLE, Setup_Config->CbsDbgFchSata3Enable);
  }

  //Check if select Auto
  if (Setup_Config->CbsDbgFchSata4Enable != 0xf) {
    ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_SATA_4_ENABLE, Setup_Config->CbsDbgFchSata4Enable);
  }

  //Check if select Auto
  if (Setup_Config->CbsDbgFchSata5Enable != 0xf) {
    ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_SATA_5_ENABLE, Setup_Config->CbsDbgFchSata5Enable);
  }

  //Check if select Auto
  if (Setup_Config->CbsDbgFchSata6Enable != 0xf) {
    ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_SATA_6_ENABLE, Setup_Config->CbsDbgFchSata6Enable);
  }

  //Check if select Auto
  if (Setup_Config->CbsDbgFchSata7Enable != 0xf) {
    ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_SATA_7_ENABLE, Setup_Config->CbsDbgFchSata7Enable);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnFchUsbXHCI0Enable != 0xf) {
    ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_USB_0_ENABLE, Setup_Config->CbsCmnFchUsbXHCI0Enable);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnFchUsbXHCI1Enable != 0xf) {
    ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_USB_1_ENABLE, Setup_Config->CbsCmnFchUsbXHCI1Enable);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnFchUsbXHCI2Enable != 0xf) {
    ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_USB_2_ENABLE, Setup_Config->CbsCmnFchUsbXHCI2Enable);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnFchUsbXHCI3Enable != 0xf) {
    ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_USB_3_ENABLE, Setup_Config->CbsCmnFchUsbXHCI3Enable);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnFchSystemPwrFailShadow != 0xf) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_PWRFAIL_OPTION, Setup_Config->CbsCmnFchSystemPwrFailShadow);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnFchPwrFailShadowABLEnabled != 0xF) {
    ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_PWRFAIL_EARLY_SHADOW, Setup_Config->CbsCmnFchPwrFailShadowABLEnabled);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnSocAblConOut != 2) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_CONSOLE_OUT_ENABLE, Setup_Config->CbsCmnSocAblConOut);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnSocAblConOutSerialPort != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_CONSOLE_OUT_SERIAL_PORT, Setup_Config->CbsCmnSocAblConOutSerialPort);
  }

  //Check Display Condition CbsCmnSocAblSerialPortIOCustomEnabled=Disabled
  if ((Setup_Config->CbsCmnSocAblSerialPortIOCustomEnabled == 0x0)) {
    //Check if select Auto
    if (Setup_Config->CbsCmnSocAblConOutSerialPortIO != 0xFF) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_CONSOLE_OUT_SERIAL_PORT_IO, Setup_Config->CbsCmnSocAblConOutSerialPortIO);
    }
  }

  //Check Display Condition CbsCmnSocAblSerialPortIOCustomEnabled=Enabled
  if ((Setup_Config->CbsCmnSocAblSerialPortIOCustomEnabled == 0x1)) {
    ApcbSetToken16 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_CONSOLE_OUT_SERIAL_PORT_IO_CUSTOMIZED, Setup_Config->CbsCmnSocAblConOutSerialPortIOCustom);  //User Input
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnSocAblConOutBasic != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_FCH_CONSOLE_OUT_BASIC_ENABLE, Setup_Config->CbsCmnSocAblConOutBasic);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnSocAblPmuMsgCtrl != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_TRAINING_HDTCTRL, Setup_Config->CbsCmnSocAblPmuMsgCtrl);
  }

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_POPULATION_MSG_CTRL, Setup_Config->CbsCmnSocAblMemPopMsgCtrl);  //User Input

  //Check Display Condition CbsNumberOfSockets=2
  if ((Setup_Config->CbsNumberOfSockets == 0x2)) {
    //Check if select Auto
    if (Setup_Config->CbsCmnPrintSocket1PmuMsgBlock != 0xFF) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_PRINT_SOCKET1_PMU_MSGBLOCK, Setup_Config->CbsCmnPrintSocket1PmuMsgBlock);
    }
  }

  //Check Display Condition CbsNumberOfSockets=2
  if ((Setup_Config->CbsNumberOfSockets == 0x2)) {
    //Check if select Auto
    if (Setup_Config->CbsCmnPrintSocket1TrainingLog != 0xFF) {
      ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_MEM_PRINT_SOCKET1_TRAINING_LOG, Setup_Config->CbsCmnPrintSocket1TrainingLog);
    }
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnSecI2cVoltMode != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_SEC_I2C_VOLT_MODE, Setup_Config->CbsCmnSecI2cVoltMode);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnCxlSdpReqSysAddr != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_DF_SDP_REQUEST_SYSTEM_ADDRESS, Setup_Config->CbsCmnCxlSdpReqSysAddr);
  }

  ApcbSetTokenBool (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CXL_ENCRYPTION_ENABLE, Setup_Config->CbsCmnCxlEncryption);  //User Input

  ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CXL_MEMORY_ONLINE_OFFLINE, Setup_Config->CbsCmnCxlMemOnlineOffline);  //User Input

  //Check if select Auto
  if (Setup_Config->CbsDbgCxlOverideCxlMemorySize != 0xFF) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_TRUNCATE_CXl_MEMORY, Setup_Config->CbsDbgCxlOverideCxlMemorySize);
  }

  //Check if select Auto
  if (Setup_Config->CbsCmnCxlMemIsolationEnable != 0xf) {
    ApcbSetToken8 (ApcbProtocol, APCB_TYPE_PURPOSE_DEBUG, APCB_TOKEN_UID_CXL_MEM_ISOLATION_EN, Setup_Config->CbsCmnCxlMemIsolationEnable);
  }


  //Write back updated data to SPI
  ApcbProtocol->ApcbFlushData (ApcbProtocol);
  ApcbProtocol->ApcbReleaseMutex (ApcbProtocol);
  return EFI_SUCCESS;
}



UINT32
Murmur_32_scramble(UINT32 h, UINT32 k) {
  UINT32 MURMURHASH_C1 = 0xCC9E2D51;
  UINT32 MURMURHASH_C2 = 0x1B873593;
  UINT32 MURMURHASH_M = 5;
  UINT32 MURMURHASH_N = 0xE6546B64;

  k *= MURMURHASH_C1;
  k = (k << 15) | (k >> 17);
  k *= MURMURHASH_C2;
  
  h ^= k;
  h = (h << 13) | (h >> 19);
  h = h * MURMURHASH_M + MURMURHASH_N;
  
  return h;
}

UINT32
GetApcbHash (
  VOID *CbsVariable,
  AMD_APCB_SERVICE_PROTOCOL *ApcbProtocol
  )
{
  CBS_CONFIG                            *Setup_Config;
  UINT32 MURMURHASH_SEED = 0xACB55EED;
  UINT32 hash = MURMURHASH_SEED;
  UINT32 key;

  if (CbsVariable == NULL) {
    return 0xFFFFFFFF;
  }

  Setup_Config = (CBS_CONFIG *) CbsVariable;
  
  key = Setup_Config->CbsCpuSmtCtrl;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCpuCpb;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDbgCpuLApicMode;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCpuSmuPspDebugMode;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCpuPpinCtrl;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsPspSevCtrl;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCpuSevAsidSpaceLimit;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCpu64BitMMIOCoverage;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnActionOnBistFailure;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCpuPfReqThrEn;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCpuScanDumpDbgEn;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCpuMcax64BankSupport;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCpuLatencyUnderLoad;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCpuFP512;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCpuPstCustomP0;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCpuPst0Fid;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCpuPst0Vid;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCpuCcd0DowncoreBitMap;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCpuCcd1DowncoreBitMap;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCpuCcd2DowncoreBitMap;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCpuCcd3DowncoreBitMap;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCpuCcd4DowncoreBitMap;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCpuCcd5DowncoreBitMap;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCpuCcd6DowncoreBitMap;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCpuCcd7DowncoreBitMap;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCpuCcd8DowncoreBitMap;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCpuCcd9DowncoreBitMap;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCpuCcd10DowncoreBitMap;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCpuCcd11DowncoreBitMap;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCpuCcd12DowncoreBitMap;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCpuCcd13DowncoreBitMap;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCpuCcd14DowncoreBitMap;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCpuCcd15DowncoreBitMap;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCpuCcdCtrl;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCpuCoreCtrl;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDbgCpuGenCpuWdt;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDbgCpuGenCpuWdtTimeout;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCmnExtIpSyncFloodProp;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCmnDisSyncFloodProp;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCmnFreezeQueueError;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCmnCc6MemEncryption;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCmnCcdBwThrottleLv;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfDbgNumPciSegments;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCmnCcmThrot;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCmnFineThrotHeavy;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCmnFineThrotLight;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCmnCleanVicFtiCmdBal;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCmnReqvReqNDImbThr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCmnCxlStronglyOrderedWrites;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCmnEnhancedPartWr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCmnDramNps;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCmnMemIntlv;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCmnMixedInterleavedMode;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCmnCxlMemIntlv;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCnliSublinkInterleaving;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCmnDramMapInversion;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCmnCc6AllocationScheme;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCmnGmiEncryption;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCmnXGmiEncryption;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfDbgXgmiLinkCfg;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCmn4LinkMaxXgmiSpeed;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCmn3LinkMaxXgmiSpeed;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiCrcScale;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiCrcThreshold;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiPresetControl;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiTrainingErrMask;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiPresetP11;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiPresetP12;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiPresetP13;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiPresetP14;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiPresetP15;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiInitPresetS0L0;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiInitPresetS0L1;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiInitPresetS0L2;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiInitPresetS0L3;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiInitPresetS1L0;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiInitPresetS1L1;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiInitPresetS1L2;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiInitPresetS1L3;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiTxeqS0L0P01;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiTxeqS0L0P23;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiTxeqS0L1P01;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiTxeqS0L1P23;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiTxeqS0L2P01;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiTxeqS0L2P23;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiTxeqS0L3P01;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiTxeqS0L3P23;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiTxeqS1L0P01;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiTxeqS1L0P23;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiTxeqS1L1P01;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiTxeqS1L1P23;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiTxeqS1L2P01;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiTxeqS1L2P23;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiTxeqS1L3P01;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiTxeqS1L3P23;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiAcDcCoupledLink;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfXgmiChannelType;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCdma;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfDbgDisRmtSteer;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCmnPfOrganization;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnDfPdrTuning;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCmnMemIntlvPageSize;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDfCmnPfPdrMode;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemCsInterleaveDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemAddressHashBankDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemAddressHashCsDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemAddressHashRmDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemAddressHashSubchannelDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemCtrllerBankSwapModeDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemContextRestoreDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDramSurvivesWarmReset;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemCtrllerPwrDnEnDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemSubUrgRefLowerBound;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemUrgRefLimit;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemDramRefreshRate;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemSelfRefreshExitStaggering;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemt2xRefreshTemperatureThreshold;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemChannelDisableFloatPowerGoodDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemChannelDisableBitmaskDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemRefManagementDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemArfmDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemRAAIMTDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemRAAMMTDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemRAARefDecMultiplierDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemDrfmDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemDrfmBrcDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemDrfmHashDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemMbistEnDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemMbistTestmodeDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemMbistAggressorsDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemHealingBistEnableBitMaskDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemHealingBistRepairTypeDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemPmuBistAlgorithmSelect;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemPmuBistAlgorithmBitMaskDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemMbistPatternSelect;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemMbistPatternLength;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemMbistAggressorsChnl;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemMbistAggrStaticLaneCtrl;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemMbistAggrStaticLaneSelU32;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemMbistAggrStaticLaneSelL32;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemMbistAggrStaticLaneSelEcc;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemMbistAggrStaticLaneVal;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemMbistTgtStaticLaneCtrl;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemMbistTgtStaticLaneSelU32;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemMbistTgtStaticLaneSelL32;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemMbistTgtStaticLaneSelEcc;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemMbistTgtStaticLaneVal;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemMbistReadDataEyeVoltageStep;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemMbistReadDataEyeTimingStep;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemMbistWriteDataEyeVoltageStep;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemMbistWriteDataEyeTimingStep;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemMbistDataeyeSilentExecution;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemDataPoisoningDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemBootTimePostPackageRepair;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemRcdParityDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemMaxRcdParityErrorReplayDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemWriteCrcDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemMaxWriteCrcErrorReplayDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemReadCrcDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemMaxReadCrcErrorReplayDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemDisMemErrInj;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemDramEccSymbolSizeDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemDramEccEnDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemDramUeccRetryDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemMaxDramUeccErrorReplayDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemDramMemClrDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemAddrXorAfterEcc;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDbgMemCipherTextHiding;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemDramEcsModeDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemDramRedirectScrubEnDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemDramRedirectScrubLimitDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemDramScrubTime;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemtECSintCtrlDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemtECSintDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemDramEtcDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemDramEcsCountModeDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemDramAutoEcsSelfRefreshDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemDramEcsWritebackSuppressionDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemDramX4WritebackSuppressionDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemOdtImpedProcDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemOdtPullDownImpedProcDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemDramDrvStrenDqDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemRttNomWrP0Ddr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemRttNomRdP0Ddr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemRttWrP0Ddr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemRttParkP0Ddr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemRttParkDqsP0Ddr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemRttNomWrP1Ddr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemRttNomRdP1Ddr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemRttWrP1Ddr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemRttParkP1Ddr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemRttParkDqsP1Ddr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTargetSpeedDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTclDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTrcdDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTrpDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTrasDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTrcDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTwrDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTrfc1Ddr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTrfc2Ddr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTrfcSbDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTcwlDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTrtpDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTrrdLDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTrrdSDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTfawDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTwtrLDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTwtrSDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTrdrdScLDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTrdrdScDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTrdrdSdDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTrdrdDdDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTwrwrScLDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTwrwrScDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTwrwrSdDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTwrwrDdDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTwrrdDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTimingTrdwrDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemDramPdaEnumIdProgModeDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemWriteTrainingBurstLength;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnTrainingRetryCount;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemPeriodicTrainingModeDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemPeriodicInterval;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemTsmeEnableDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemAes;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemDataScramble;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemSmeMkEnable;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemCtrllerPmicOpMode;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemCtrllerPmicFaultRecovery;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemCtrllerPmicSwaSwbVddCore;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemCtrllerPmicSwcVddio;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemCtrllerPmicSwdVpp;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemCtrllerPmicStaggerDelay;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemCtrllerMaxPmicPowerOn;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnMemOdtsCmdThrottleThresholdDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnTsodThermalThrottleControlDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnTsodThermalThrottleStartTempDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnTsodThermalThrottleHysteresisDdr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnTsodCmdThrottlePercentage0Ddr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnTsodCmdThrottlePercentage5Ddr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnTsodCmdThrottlePercentage10Ddr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnX3dStackOverride;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnL3Bist;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnEarlyLinkSpeed;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnPcieTargetLinkSpeed;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnDrtmMemoryReservation;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnGnbNbIOMMU;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI3C0Config;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI3C1Config;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI3C2Config;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI3C3Config;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI2C4Config;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI2C5Config;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI2cSdaHoldOverride;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchApmlSbtsiSlvMode;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI3cModeSpeed;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI3cPpHcntValue;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI3cSdaHoldValue;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI3cSdaHoldOverride;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI2c0SdaTxHoldValue;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI2c1SdaTxHoldValue;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI2c2SdaTxHoldValue;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI2c3SdaTxHoldValue;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI2c4SdaTxHoldValue;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI2c5SdaTxHoldValue;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI2c0SdaRxHoldValue;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI2c1SdaRxHoldValue;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI2c2SdaRxHoldValue;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI2c3SdaRxHoldValue;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI2c4SdaRxHoldValue;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI2c5SdaRxHoldValue;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI3c0SdaHoldValue;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI3c1SdaHoldValue;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI3c2SdaHoldValue;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchI3c3SdaHoldValue;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchSataEnable;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchSataClass;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDbgFchSata0Enable;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDbgFchSata1Enable;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDbgFchSata2Enable;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDbgFchSata3Enable;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDbgFchSata4Enable;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDbgFchSata5Enable;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDbgFchSata6Enable;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDbgFchSata7Enable;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchUsbXHCI0Enable;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchUsbXHCI1Enable;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchUsbXHCI2Enable;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchUsbXHCI3Enable;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchSystemPwrFailShadow;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnFchPwrFailShadowABLEnabled;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnSocAblConOut;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnSocAblConOutSerialPort;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnSocAblConOutSerialPortIO;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnSocAblConOutSerialPortIOCustom;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnSocAblConOutBasic;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnSocAblPmuMsgCtrl;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnSocAblMemPopMsgCtrl;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnPrintSocket1PmuMsgBlock;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnPrintSocket1TrainingLog;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnSecI2cVoltMode;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCxlSdpReqSysAddr;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCxlEncryption;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCxlMemOnlineOffline;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsDbgCxlOverideCxlMemorySize;
  hash = Murmur_32_scramble(hash, key);

  key = Setup_Config->CbsCmnCxlMemIsolationEnable;
  hash = Murmur_32_scramble(hash, key);


  hash = Murmur_32_scramble(hash, 0x42A2239A);

  hash ^= 308;
  hash ^= hash >> 16;
  hash *= 0x85EBCA6B;
  hash ^= hash >> 13;
  hash *= 0xC2B2AE35;
  hash ^= hash >> 16;
  
  if(hash == 0x56434552){
    hash++;
  }
  
  return hash;
}
