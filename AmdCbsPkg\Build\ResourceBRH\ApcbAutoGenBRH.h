/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */
//This file is auto generated, do not edit it manually

#ifndef _APCB_AUTOGEN_BRH_H_
#define _APCB_AUTOGEN_BRH_H_


#ifdef NON_CBS
#define APCB_TOKEN_BOOL_AUTOGEN_EMPTY 1
#define APCB_TOKEN_U8_AUTOGEN_EMPTY 1
#define APCB_TOKEN_U16_AUTOGEN_EMPTY 1
#define APCB_TOKEN_U32_AUTOGEN_EMPTY 1
#else
#define APCB_TOKEN_BOOL_AUTOGEN_EMPTY 0
#define APCB_TOKEN_U8_AUTOGEN_EMPTY 0
#define APCB_TOKEN_U16_AUTOGEN_EMPTY 0
#define APCB_TOKEN_U32_AUTOGEN_EMPTY 0

//
// APCB Token Value Definition
//
#define APCB_TOKEN_UID_DF_XGMI_PRESET_P11_VALUE 0x3000  // 0x3000
#define APCB_TOKEN_UID_DF_XGMI_PRESET_P12_VALUE 0x3000  // 0x3000
#define APCB_TOKEN_UID_DF_XGMI_PRESET_P13_VALUE 0x3000  // 0x3000
#define APCB_TOKEN_UID_DF_XGMI_PRESET_P14_VALUE 0x3000  // 0x3000
#define APCB_TOKEN_UID_DF_XGMI_PRESET_P15_VALUE 0x3000  // 0x3000
#define APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S0_L0_VALUE 0x4444  // 0x4444
#define APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S0_L1_VALUE 0x4444  // 0x4444
#define APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S0_L2_VALUE 0x4444  // 0x4444
#define APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S0_L3_VALUE 0x4444  // 0x4444
#define APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S1_L0_VALUE 0x4444  // 0x4444
#define APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S1_L1_VALUE 0x4444  // 0x4444
#define APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S1_L2_VALUE 0x4444  // 0x4444
#define APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S1_L3_VALUE 0x4444  // 0x4444
#define APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L0_P01_VALUE 0x007A007A  // 0x007A007A
#define APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L0_P23_VALUE 0x007A007A  // 0x007A007A
#define APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L1_P01_VALUE 0x007A007A  // 0x007A007A
#define APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L1_P23_VALUE 0x007A007A  // 0x007A007A
#define APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L2_P01_VALUE 0x007A007A  // 0x007A007A
#define APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L2_P23_VALUE 0x007A007A  // 0x007A007A
#define APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L3_P01_VALUE 0x007A007A  // 0x007A007A
#define APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L3_P23_VALUE 0x007A007A  // 0x007A007A
#define APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L0_P01_VALUE 0x007A007A  // 0x007A007A
#define APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L0_P23_VALUE 0x007A007A  // 0x007A007A
#define APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L1_P01_VALUE 0x007A007A  // 0x007A007A
#define APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L1_P23_VALUE 0x007A007A  // 0x007A007A
#define APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L2_P01_VALUE 0x007A007A  // 0x007A007A
#define APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L2_P23_VALUE 0x007A007A  // 0x007A007A
#define APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L3_P01_VALUE 0x007A007A  // 0x007A007A
#define APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L3_P23_VALUE 0x007A007A  // 0x007A007A
#define APCB_TOKEN_UID_MEM_DRAM_SURVIVES_WARM_RESET_DDR_VALUE 0  // Disabled
#define APCB_TOKEN_UID_MEM_SUBURGREFLOWERBOUND_VALUE 1  // 1
#define APCB_TOKEN_UID_MEM_URGREFLIMIT_VALUE 4  // 4
#define APCB_TOKEN_UID_DRAMDOUBLEREFRESHRATE_VALUE 0  // 3.9 usec
#define APCB_TOKEN_UID_MEM_SELF_REFRESH_EXIT_STAGGERING_VALUE 9  // n = 9
#define APCB_TOKEN_UID_MEM_2X_REFRESH_THRESHOLD_DDR_VALUE 2  // 85' - 90'
#define APCB_TOKEN_UID_MEM_CHANNEL_DISABLE_FLOAT_POWER_GOOD_DDR_VALUE 0  // Disabled
#define APCB_TOKEN_UID_MEM_CHANNEL_DISABLE_BITMASK_DDR_VALUE 0  // 0
#define APCB_TOKEN_UID_MEM_DRFM_BRC_DDR_VALUE 4  // BRC4
#define APCB_TOKEN_UID_MEM_HEALING_BIST_ENABLE_BITMASK_DDR_VALUE 0  // Disabled
#define APCB_TOKEN_UID_MEM_HEALING_BIST_REPAIR_TYPE_DDR_VALUE 0  // Soft Repair
#define APCB_TOKEN_UID_MEM_MBIST_PATTERN_SELECT_DDR_VALUE 0  // PRBS
#define APCB_TOKEN_UID_MEM_MBIST_PATTERN_LENGTH_DDR_VALUE 3  // 3
#define APCB_TOKEN_UID_MEM_MBIST_AGGRESSORS_CHNL_DDR_VALUE 2  // All Channels
#define APCB_TOKEN_UID_MEM_MBIST_AGGR_STATIC_LANE_CTRL_VALUE 0  // Disabled
#define APCB_TOKEN_UID_MEM_MBIST_AGGR_STATIC_LANE_SEL_U32_VALUE 0  // 0
#define APCB_TOKEN_UID_MEM_MBIST_AGGR_STATIC_LANE_SEL_L32_VALUE 0  // 0
#define APCB_TOKEN_UID_MEM_MBIST_AGGR_STATIC_LANE_SEL_ECC_VALUE 0  // 0
#define APCB_TOKEN_UID_MEM_MBIST_AGGR_STATIC_LANE_VAL_VALUE 0  // 0
#define APCB_TOKEN_UID_MEM_MBIST_TGT_STATIC_LANE_CTRL_VALUE 0  // Disabled
#define APCB_TOKEN_UID_MEM_MBIST_TGT_STATIC_LANE_SEL_U32_VALUE 0  // 0
#define APCB_TOKEN_UID_MEM_MBIST_TGT_STATIC_LANE_SEL_L32_VALUE 0  // 0
#define APCB_TOKEN_UID_MEM_MBIST_TGT_STATIC_LANE_SEL_ECC_VALUE 0  // 0
#define APCB_TOKEN_UID_MEM_MBIST_TGT_STATIC_LANE_VAL_VALUE 0  // 0
#define APCB_TOKEN_UID_MEM_MBIST_READ_DATA_EYE_VOLTAGE_STEP_VALUE 1  // 1
#define APCB_TOKEN_UID_MEM_MBIST_READ_DATA_EYE_TIMING_STEP_VALUE 1  // 1
#define APCB_TOKEN_UID_MEM_MBIST_WRITE_DATA_EYE_VOLTAGE_STEP_VALUE 1  // 1
#define APCB_TOKEN_UID_MEM_MBIST_WRITE_DATA_EYE_TIMING_STEP_VALUE 1  // 1
#define APCB_TOKEN_UID_MEM_MBIST_DATAEYE_SILENT_EXECUTION_DDR_VALUE 0  // Disabled
#define APCB_TOKEN_UID_BOOT_TIME_POST_PACKAGE_REPAIR_ENABLE_VALUE 0  // Disable
#define APCB_TOKEN_UID_MEM_WR_CRC_EN_DDR_VALUE 0  // Disabled
#define APCB_TOKEN_UID_MEM_RD_CRC_EN_DDR_VALUE 0  // Disabled
#define APCB_TOKEN_UID_MEM_MAX_UECC_ERROR_REPLAY_VALUE 8  // 8
#define APCB_TOKEN_UID_MEM_CIPHERTEXT_HIDE_EN_DDR_VALUE 0  // Disable
#define APCB_TOKEN_UID_MEM_PATROL_SCRUB_DDR_VALUE 24  // 24 hours
#define APCB_TOKEN_UID_WRITE_TRAINING_BURST_LENGTH_VALUE 2  // 4x
#define APCB_TOKEN_UID_MEM_PPT_CTRL_DDR_VALUE 1  // Legacy
#define APCB_TOKEN_UID_MEM_PMIC_OPERATION_MODE_DDR_VALUE 0  // Secure Mode
#define APCB_TOKEN_UID_MEM_PMIC_PERSISTENT_ERROR_DDR_VALUE 0  // Always
#define APCB_TOKEN_UID_MEM_PMIC_SWA_SWB_VDDCORE_DDR_VALUE 1100  // 1100
#define APCB_TOKEN_UID_MEM_PMIC_SWC_VDDIO_DDR_VALUE 1100  // 1100
#define APCB_TOKEN_UID_MEM_PMIC_SWD_VPP_DDR_VALUE 1800  // 1800
#define APCB_TOKEN_UID_MEM_PMIC_STAGGER_DDR_VALUE 5  // 5
#define APCB_TOKEN_UID_MEM_MAX_DIMM_STAGGER_DDR_VALUE 0xFF  // 0xFF
#define APCB_TOKEN_UID_MEM_THERMAL_THROTTLE_DDR_VALUE 0  // Disabled
#define APCB_TOKEN_UID_BMC_LINK_SPEED_VALUE 2  // Gen2
#define APCB_TOKEN_UID_I2C_I3C_SMBUS_0_VALUE 0  // I3C Enabled
#define APCB_TOKEN_UID_I2C_I3C_SMBUS_1_VALUE 0  // I3C Enabled
#define APCB_TOKEN_UID_I2C_I3C_SMBUS_2_VALUE 0  // I3C Enabled
#define APCB_TOKEN_UID_I2C_I3C_SMBUS_3_VALUE 0  // I3C Enabled
#define APCB_TOKEN_UID_APML_SBTSI_SLAVE_MODE_VALUE 0  // I3C
#define APCB_TOKEN_UID_FCH_I3C_PP_HCNT_VALUE 0x08  // 0x08
#define APCB_TOKEN_UID_FCH_I3C_SDA_HOLD_SWITCH_DLY_TIMING_VALUE 0x2  // 0x2
#define APCB_TOKEN_UID_FCH_I2C0_SDA_TX_HOLD_VALUE 0x35  // 0x35
#define APCB_TOKEN_UID_FCH_I2C1_SDA_TX_HOLD_VALUE 0x35  // 0x35
#define APCB_TOKEN_UID_FCH_I2C2_SDA_TX_HOLD_VALUE 0x35  // 0x35
#define APCB_TOKEN_UID_FCH_I2C3_SDA_TX_HOLD_VALUE 0x35  // 0x35
#define APCB_TOKEN_UID_FCH_I2C4_SDA_TX_HOLD_VALUE 0x35  // 0x35
#define APCB_TOKEN_UID_FCH_I2C5_SDA_TX_HOLD_VALUE 0x35  // 0x35
#define APCB_TOKEN_UID_FCH_I2C0_SDA_RX_HOLD_VALUE 0x00  // 0x00
#define APCB_TOKEN_UID_FCH_I2C1_SDA_RX_HOLD_VALUE 0x00  // 0x00
#define APCB_TOKEN_UID_FCH_I2C2_SDA_RX_HOLD_VALUE 0x00  // 0x00
#define APCB_TOKEN_UID_FCH_I2C3_SDA_RX_HOLD_VALUE 0x00  // 0x00
#define APCB_TOKEN_UID_FCH_I2C4_SDA_RX_HOLD_VALUE 0x00  // 0x00
#define APCB_TOKEN_UID_FCH_I2C5_SDA_RX_HOLD_VALUE 0x00  // 0x00
#define APCB_TOKEN_UID_FCH_I3C0_SDA_HOLD_VALUE 0x2  // 0x2
#define APCB_TOKEN_UID_FCH_I3C1_SDA_HOLD_VALUE 0x2  // 0x2
#define APCB_TOKEN_UID_FCH_I3C2_SDA_HOLD_VALUE 0x2  // 0x2
#define APCB_TOKEN_UID_FCH_I3C3_SDA_HOLD_VALUE 0x2  // 0x2
#define APCB_TOKEN_UID_FCH_USB_2_ENABLE_VALUE 1  // Enabled
#define APCB_TOKEN_UID_FCH_USB_3_ENABLE_VALUE 1  // Enabled
#define APCB_TOKEN_UID_FCH_PWRFAIL_OPTION_VALUE 0  // Always Off
#define APCB_TOKEN_UID_MEM_POPULATION_MSG_CTRL_VALUE 0  // Warning message
#define APCB_TOKEN_UID_CXL_ENCRYPTION_ENABLE_VALUE 0  // Disabled
#define APCB_TOKEN_UID_CXL_MEMORY_ONLINE_OFFLINE_VALUE 0  // Disabled

//
// APCB TOKEN COMMON BOOL DATA
//
#define APCB_CMN_TOKEN_BOOL_AUTOGEN \
APCB_TOKEN_BOOL (APCB_TOKEN_UID_MEM_MBIST_AGGR_STATIC_LANE_CTRL, APCB_TOKEN_UID_MEM_MBIST_AGGR_STATIC_LANE_CTRL_VALUE), \
APCB_TOKEN_BOOL (APCB_TOKEN_UID_MEM_MBIST_TGT_STATIC_LANE_CTRL, APCB_TOKEN_UID_MEM_MBIST_TGT_STATIC_LANE_CTRL_VALUE), \
APCB_TOKEN_BOOL (APCB_TOKEN_UID_FCH_USB_2_ENABLE, APCB_TOKEN_UID_FCH_USB_2_ENABLE_VALUE), \
APCB_TOKEN_BOOL (APCB_TOKEN_UID_FCH_USB_3_ENABLE, APCB_TOKEN_UID_FCH_USB_3_ENABLE_VALUE), \
APCB_TOKEN_BOOL (APCB_TOKEN_UID_CXL_ENCRYPTION_ENABLE, APCB_TOKEN_UID_CXL_ENCRYPTION_ENABLE_VALUE), \

//
// APCB TOKEN COMMON UINT8 DATA
//
#define APCB_CMN_TOKEN_U8_AUTOGEN \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_DRAM_SURVIVES_WARM_RESET_DDR, APCB_TOKEN_UID_MEM_DRAM_SURVIVES_WARM_RESET_DDR_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_SUBURGREFLOWERBOUND, APCB_TOKEN_UID_MEM_SUBURGREFLOWERBOUND_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_URGREFLIMIT, APCB_TOKEN_UID_MEM_URGREFLIMIT_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_DRAMDOUBLEREFRESHRATE, APCB_TOKEN_UID_DRAMDOUBLEREFRESHRATE_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_SELF_REFRESH_EXIT_STAGGERING, APCB_TOKEN_UID_MEM_SELF_REFRESH_EXIT_STAGGERING_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_2X_REFRESH_THRESHOLD_DDR, APCB_TOKEN_UID_MEM_2X_REFRESH_THRESHOLD_DDR_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_CHANNEL_DISABLE_FLOAT_POWER_GOOD_DDR, APCB_TOKEN_UID_MEM_CHANNEL_DISABLE_FLOAT_POWER_GOOD_DDR_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_DRFM_BRC_DDR, APCB_TOKEN_UID_MEM_DRFM_BRC_DDR_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_HEALING_BIST_ENABLE_BITMASK_DDR, APCB_TOKEN_UID_MEM_HEALING_BIST_ENABLE_BITMASK_DDR_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_HEALING_BIST_REPAIR_TYPE_DDR, APCB_TOKEN_UID_MEM_HEALING_BIST_REPAIR_TYPE_DDR_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_MBIST_PATTERN_SELECT_DDR, APCB_TOKEN_UID_MEM_MBIST_PATTERN_SELECT_DDR_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_MBIST_PATTERN_LENGTH_DDR, APCB_TOKEN_UID_MEM_MBIST_PATTERN_LENGTH_DDR_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_MBIST_AGGRESSORS_CHNL_DDR, APCB_TOKEN_UID_MEM_MBIST_AGGRESSORS_CHNL_DDR_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_MBIST_AGGR_STATIC_LANE_SEL_ECC, APCB_TOKEN_UID_MEM_MBIST_AGGR_STATIC_LANE_SEL_ECC_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_MBIST_AGGR_STATIC_LANE_VAL, APCB_TOKEN_UID_MEM_MBIST_AGGR_STATIC_LANE_VAL_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_MBIST_TGT_STATIC_LANE_SEL_ECC, APCB_TOKEN_UID_MEM_MBIST_TGT_STATIC_LANE_SEL_ECC_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_MBIST_TGT_STATIC_LANE_VAL, APCB_TOKEN_UID_MEM_MBIST_TGT_STATIC_LANE_VAL_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_MBIST_READ_DATA_EYE_VOLTAGE_STEP, APCB_TOKEN_UID_MEM_MBIST_READ_DATA_EYE_VOLTAGE_STEP_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_MBIST_READ_DATA_EYE_TIMING_STEP, APCB_TOKEN_UID_MEM_MBIST_READ_DATA_EYE_TIMING_STEP_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_MBIST_WRITE_DATA_EYE_VOLTAGE_STEP, APCB_TOKEN_UID_MEM_MBIST_WRITE_DATA_EYE_VOLTAGE_STEP_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_MBIST_WRITE_DATA_EYE_TIMING_STEP, APCB_TOKEN_UID_MEM_MBIST_WRITE_DATA_EYE_TIMING_STEP_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_MBIST_DATAEYE_SILENT_EXECUTION_DDR, APCB_TOKEN_UID_MEM_MBIST_DATAEYE_SILENT_EXECUTION_DDR_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_BOOT_TIME_POST_PACKAGE_REPAIR_ENABLE, APCB_TOKEN_UID_BOOT_TIME_POST_PACKAGE_REPAIR_ENABLE_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_WR_CRC_EN_DDR, APCB_TOKEN_UID_MEM_WR_CRC_EN_DDR_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_RD_CRC_EN_DDR, APCB_TOKEN_UID_MEM_RD_CRC_EN_DDR_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_MAX_UECC_ERROR_REPLAY, APCB_TOKEN_UID_MEM_MAX_UECC_ERROR_REPLAY_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_CIPHERTEXT_HIDE_EN_DDR, APCB_TOKEN_UID_MEM_CIPHERTEXT_HIDE_EN_DDR_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_PATROL_SCRUB_DDR, APCB_TOKEN_UID_MEM_PATROL_SCRUB_DDR_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_WRITE_TRAINING_BURST_LENGTH, APCB_TOKEN_UID_WRITE_TRAINING_BURST_LENGTH_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_PPT_CTRL_DDR, APCB_TOKEN_UID_MEM_PPT_CTRL_DDR_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_PMIC_OPERATION_MODE_DDR, APCB_TOKEN_UID_MEM_PMIC_OPERATION_MODE_DDR_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_PMIC_PERSISTENT_ERROR_DDR, APCB_TOKEN_UID_MEM_PMIC_PERSISTENT_ERROR_DDR_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_PMIC_STAGGER_DDR, APCB_TOKEN_UID_MEM_PMIC_STAGGER_DDR_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_MAX_DIMM_STAGGER_DDR, APCB_TOKEN_UID_MEM_MAX_DIMM_STAGGER_DDR_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_THERMAL_THROTTLE_DDR, APCB_TOKEN_UID_MEM_THERMAL_THROTTLE_DDR_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_BMC_LINK_SPEED, APCB_TOKEN_UID_BMC_LINK_SPEED_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_I2C_I3C_SMBUS_0, APCB_TOKEN_UID_I2C_I3C_SMBUS_0_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_I2C_I3C_SMBUS_1, APCB_TOKEN_UID_I2C_I3C_SMBUS_1_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_I2C_I3C_SMBUS_2, APCB_TOKEN_UID_I2C_I3C_SMBUS_2_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_I2C_I3C_SMBUS_3, APCB_TOKEN_UID_I2C_I3C_SMBUS_3_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_APML_SBTSI_SLAVE_MODE, APCB_TOKEN_UID_APML_SBTSI_SLAVE_MODE_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_FCH_I3C_PP_HCNT, APCB_TOKEN_UID_FCH_I3C_PP_HCNT_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_FCH_I3C_SDA_HOLD_SWITCH_DLY_TIMING, APCB_TOKEN_UID_FCH_I3C_SDA_HOLD_SWITCH_DLY_TIMING_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_FCH_I2C0_SDA_RX_HOLD, APCB_TOKEN_UID_FCH_I2C0_SDA_RX_HOLD_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_FCH_I2C1_SDA_RX_HOLD, APCB_TOKEN_UID_FCH_I2C1_SDA_RX_HOLD_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_FCH_I2C2_SDA_RX_HOLD, APCB_TOKEN_UID_FCH_I2C2_SDA_RX_HOLD_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_FCH_I2C3_SDA_RX_HOLD, APCB_TOKEN_UID_FCH_I2C3_SDA_RX_HOLD_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_FCH_I2C4_SDA_RX_HOLD, APCB_TOKEN_UID_FCH_I2C4_SDA_RX_HOLD_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_FCH_I2C5_SDA_RX_HOLD, APCB_TOKEN_UID_FCH_I2C5_SDA_RX_HOLD_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_FCH_I3C0_SDA_HOLD, APCB_TOKEN_UID_FCH_I3C0_SDA_HOLD_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_FCH_I3C1_SDA_HOLD, APCB_TOKEN_UID_FCH_I3C1_SDA_HOLD_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_FCH_I3C2_SDA_HOLD, APCB_TOKEN_UID_FCH_I3C2_SDA_HOLD_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_FCH_I3C3_SDA_HOLD, APCB_TOKEN_UID_FCH_I3C3_SDA_HOLD_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_FCH_PWRFAIL_OPTION, APCB_TOKEN_UID_FCH_PWRFAIL_OPTION_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_MEM_POPULATION_MSG_CTRL, APCB_TOKEN_UID_MEM_POPULATION_MSG_CTRL_VALUE), \
APCB_TOKEN_U8 (APCB_TOKEN_UID_CXL_MEMORY_ONLINE_OFFLINE, APCB_TOKEN_UID_CXL_MEMORY_ONLINE_OFFLINE_VALUE), \

//
// APCB TOKEN COMMON UINT16 DATA
//
#define APCB_CMN_TOKEN_U16_AUTOGEN \
APCB_TOKEN_U16 (APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S0_L0, APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S0_L0_VALUE), \
APCB_TOKEN_U16 (APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S0_L1, APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S0_L1_VALUE), \
APCB_TOKEN_U16 (APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S0_L2, APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S0_L2_VALUE), \
APCB_TOKEN_U16 (APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S0_L3, APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S0_L3_VALUE), \
APCB_TOKEN_U16 (APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S1_L0, APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S1_L0_VALUE), \
APCB_TOKEN_U16 (APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S1_L1, APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S1_L1_VALUE), \
APCB_TOKEN_U16 (APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S1_L2, APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S1_L2_VALUE), \
APCB_TOKEN_U16 (APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S1_L3, APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_S1_L3_VALUE), \
APCB_TOKEN_U16 (APCB_TOKEN_UID_MEM_PMIC_SWA_SWB_VDDCORE_DDR, APCB_TOKEN_UID_MEM_PMIC_SWA_SWB_VDDCORE_DDR_VALUE), \
APCB_TOKEN_U16 (APCB_TOKEN_UID_MEM_PMIC_SWC_VDDIO_DDR, APCB_TOKEN_UID_MEM_PMIC_SWC_VDDIO_DDR_VALUE), \
APCB_TOKEN_U16 (APCB_TOKEN_UID_MEM_PMIC_SWD_VPP_DDR, APCB_TOKEN_UID_MEM_PMIC_SWD_VPP_DDR_VALUE), \
APCB_TOKEN_U16 (APCB_TOKEN_UID_FCH_I2C0_SDA_TX_HOLD, APCB_TOKEN_UID_FCH_I2C0_SDA_TX_HOLD_VALUE), \
APCB_TOKEN_U16 (APCB_TOKEN_UID_FCH_I2C1_SDA_TX_HOLD, APCB_TOKEN_UID_FCH_I2C1_SDA_TX_HOLD_VALUE), \
APCB_TOKEN_U16 (APCB_TOKEN_UID_FCH_I2C2_SDA_TX_HOLD, APCB_TOKEN_UID_FCH_I2C2_SDA_TX_HOLD_VALUE), \
APCB_TOKEN_U16 (APCB_TOKEN_UID_FCH_I2C3_SDA_TX_HOLD, APCB_TOKEN_UID_FCH_I2C3_SDA_TX_HOLD_VALUE), \
APCB_TOKEN_U16 (APCB_TOKEN_UID_FCH_I2C4_SDA_TX_HOLD, APCB_TOKEN_UID_FCH_I2C4_SDA_TX_HOLD_VALUE), \
APCB_TOKEN_U16 (APCB_TOKEN_UID_FCH_I2C5_SDA_TX_HOLD, APCB_TOKEN_UID_FCH_I2C5_SDA_TX_HOLD_VALUE), \

//
// APCB TOKEN COMMON UINT32 DATA
//
#define APCB_CMN_TOKEN_U32_AUTOGEN \
APCB_TOKEN_U32 (APCB_TOKEN_UID_DF_XGMI_PRESET_P11, APCB_TOKEN_UID_DF_XGMI_PRESET_P11_VALUE), \
APCB_TOKEN_U32 (APCB_TOKEN_UID_DF_XGMI_PRESET_P12, APCB_TOKEN_UID_DF_XGMI_PRESET_P12_VALUE), \
APCB_TOKEN_U32 (APCB_TOKEN_UID_DF_XGMI_PRESET_P13, APCB_TOKEN_UID_DF_XGMI_PRESET_P13_VALUE), \
APCB_TOKEN_U32 (APCB_TOKEN_UID_DF_XGMI_PRESET_P14, APCB_TOKEN_UID_DF_XGMI_PRESET_P14_VALUE), \
APCB_TOKEN_U32 (APCB_TOKEN_UID_DF_XGMI_PRESET_P15, APCB_TOKEN_UID_DF_XGMI_PRESET_P15_VALUE), \
APCB_TOKEN_U32 (APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L0_P01, APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L0_P01_VALUE), \
APCB_TOKEN_U32 (APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L0_P23, APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L0_P23_VALUE), \
APCB_TOKEN_U32 (APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L1_P01, APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L1_P01_VALUE), \
APCB_TOKEN_U32 (APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L1_P23, APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L1_P23_VALUE), \
APCB_TOKEN_U32 (APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L2_P01, APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L2_P01_VALUE), \
APCB_TOKEN_U32 (APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L2_P23, APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L2_P23_VALUE), \
APCB_TOKEN_U32 (APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L3_P01, APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L3_P01_VALUE), \
APCB_TOKEN_U32 (APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L3_P23, APCB_TOKEN_UID_DF_XGMI_TXEQ_S0_L3_P23_VALUE), \
APCB_TOKEN_U32 (APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L0_P01, APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L0_P01_VALUE), \
APCB_TOKEN_U32 (APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L0_P23, APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L0_P23_VALUE), \
APCB_TOKEN_U32 (APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L1_P01, APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L1_P01_VALUE), \
APCB_TOKEN_U32 (APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L1_P23, APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L1_P23_VALUE), \
APCB_TOKEN_U32 (APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L2_P01, APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L2_P01_VALUE), \
APCB_TOKEN_U32 (APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L2_P23, APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L2_P23_VALUE), \
APCB_TOKEN_U32 (APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L3_P01, APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L3_P01_VALUE), \
APCB_TOKEN_U32 (APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L3_P23, APCB_TOKEN_UID_DF_XGMI_TXEQ_S1_L3_P23_VALUE), \
APCB_TOKEN_U32 (APCB_TOKEN_UID_MEM_CHANNEL_DISABLE_BITMASK_DDR, APCB_TOKEN_UID_MEM_CHANNEL_DISABLE_BITMASK_DDR_VALUE), \
APCB_TOKEN_U32 (APCB_TOKEN_UID_MEM_MBIST_AGGR_STATIC_LANE_SEL_U32, APCB_TOKEN_UID_MEM_MBIST_AGGR_STATIC_LANE_SEL_U32_VALUE), \
APCB_TOKEN_U32 (APCB_TOKEN_UID_MEM_MBIST_AGGR_STATIC_LANE_SEL_L32, APCB_TOKEN_UID_MEM_MBIST_AGGR_STATIC_LANE_SEL_L32_VALUE), \
APCB_TOKEN_U32 (APCB_TOKEN_UID_MEM_MBIST_TGT_STATIC_LANE_SEL_U32, APCB_TOKEN_UID_MEM_MBIST_TGT_STATIC_LANE_SEL_U32_VALUE), \
APCB_TOKEN_U32 (APCB_TOKEN_UID_MEM_MBIST_TGT_STATIC_LANE_SEL_L32, APCB_TOKEN_UID_MEM_MBIST_TGT_STATIC_LANE_SEL_L32_VALUE), \

//
// APCB TOKEN DBG BOOL DATA
//
#define APCB_DBG_TOKEN_BOOL_AUTOGEN

//
// APCB TOKEN DBG UINT8 DATA
//
#define APCB_DBG_TOKEN_U8_AUTOGEN

//
// APCB TOKEN DBG UINT16 DATA
//
#define APCB_DBG_TOKEN_U16_AUTOGEN

//
// APCB TOKEN DBG UINT32 DATA
//
#define APCB_DBG_TOKEN_U32_AUTOGEN \
APCB_TOKEN_U32 (APCB_TOKEN_UID_CBS_SYNC_SIGNATURE, 0x3CB618CC), \

#endif  // NON_CBS

#endif //_APCB_AUTOGEN_BRH_H_
