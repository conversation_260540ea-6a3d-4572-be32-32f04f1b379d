#pragma message( "Compal Server Override Compiling-" __FILE__ )
//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************
 
/** @file RasIpmiLibBrh.c
    Translate Error Log entry from AMD MCA and NBIO format to Ipmi

**/

#include <AmiDxeLib.h>
#include <Library/RasCommonLib.h>
#include <Library/RasLibBrh.h>
#include <Library/RasIpmiLibBrh.h>
#include <AmdRas.h>
#include <Library/CpmRasLib.h>
#include <Include/AmiIpmiNetFnStorageDefinitions.h>
#include <Token.h>
#include <AmdRasRegistersBrh.h>
//COMPAL_CHANGE >>>
#include <Library/PciSegmentLib.h>
#include <Library/CpmRasPciLib.h>
#include <Library/PciLib.h>
#include <Pci.h>
#include <OemBoardInfoLib.h>

extern PLATFORM_APEI_PRIVATE_BUFFER_V3         *mPlatformApeiData;

#define ARRAYSIZE(x) (sizeof(x) / sizeof((x)[0]))

typedef struct {
    UINT8 PhysicalSlot;
    UINT8 SensorNumber;
} CUSTOM_SLOT_MAP;

STATIC CONST CUSTOM_SLOT_MAP mCustomSlotMap[] = {
    {  44, 0x2B },  // PCIE_SLOT_11
    {  56, 0x2A },  // PCIE_SLOT_10    
    {  40, 0x40 },  {  41, 0x41 },  {  42, 0x42 },  {  43, 0x43 },
    {  52, 0x44 },  {  53, 0x45 },  {  54, 0x46 },  {  55, 0x47 },
    { 106, 0x24 },  { 107, 0x25 },  { 109, 0x21 },  { 104, 0x22 },
    { 105, 0x23 },  { 108, 0x2C },  { 110, 0x49 },  { 111, 0x48 },
    { 112, 0x4B },  { 113, 0x4A },  { 114, 0x26 },  { 115, 0x27 },
    { 116, 0x28 },  { 117, 0x29 },  { 118, 0x2E },  { 119, 0x2D },
    { 120, 0x4E },  { 121, 0x4F },  { 122, 0x4C },  { 123, 0x4D },
};
STATIC CONST UINTN mCustomSlotMapCount = ARRAYSIZE(mCustomSlotMap);

//COMPAL_CHANGE <<<

/**
    This function will update generic IPMI info fields 
    @param IPMI_SEL_EVENT_RECORD_DATA   *SelRecord - pointer to SEL event log structure
    
    @retval EFI_SUCCESS - Ipmi error log entry created
    @retval EFI_INVALID_PARAMETER - input param contains null pointer
**/

EFI_STATUS
STATIC
InitGenericSelInfo (
    IN OUT  IPMI_SEL_EVENT_RECORD_DATA  *SelRecord )
{
  if (SelRecord == NULL) {
      return EFI_INVALID_PARAMETER;
  }

  SelRecord->TimeStamp = 0;
  SelRecord->RecordType = IPMI_SEL_SYSTEM_RECORD;
  SelRecord->GeneratorId = (UINT16)EFI_GENERATOR_ID(SMI_HANDLER_SOFTWARE_ID);
  SelRecord->EvMRevision = IPMI_EVM_REVISION;
  SelRecord->EventDirType = IPMI_SENSOR_TYPE_EVENT_CODE_DISCRETE;  
  //All our SEL record has OEM data in Byte 2 and 3.
  SelRecord->OEMEvData1 = SEL_OEM_CODE_IN_BYTE2AND3;
  return EFI_SUCCESS;
}

/**
    Translate/convert, if possible, error entry from Processor to IPMI format

    @param McaErrorRecord - Pointer to MCA error structure
    @param ErrorBuffer - Buffer with IPMI error entry, only data
    @param BankIndex - MCA bank index
    @param ProcessorNumber - logical CPU number
    @param GenericProcErrEntryBuffer - pointer to GenericErrorEntryBuffer

    @retval EFI_SUCCESS - IPMI error log entry created
    @retval EFI_INVALID_PARAMETER - Input param contains null pointer
    @retval EFI_NOT_FOUND - Translation cannot be done
**/

EFI_STATUS
ProcessorToIpmiBrh (
    IN  RAS_MCA_ERROR_INFO_V2       *McaErrorRecord,
    IN  OUT UINT8                   *ErrorBuffer,
    IN  UINT8                       BankIndex,
    IN  UINTN                       ProcessorNumber,
    IN  GENERIC_PROC_ERR_ENTRY_V3   *GenericProcErrEntryBuffer
    )
{
    IPMI_SEL_EVENT_RECORD_DATA  *SelRecord;

    if (EFI_ERROR (InitErrorLogBuffer((VOID *)McaErrorRecord, (VOID *)ErrorBuffer))) {
        return  EFI_INVALID_PARAMETER; 
    }
    
    // check for the branch from UMC_WRITEDATAPOISONERR
    if(McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_UMC_ID){
        SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
        InitGenericSelInfo (SelRecord);
        
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        // ---------------------------- start --------------------------------------------------  
        SelRecord->SensorType = SEL_SENSOR_TYPE_OEM;
        SelRecord->SensorNumber = OEM_CPU_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
        SelRecord->OEMEvData2 = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
        // Correctable / Uncorrectable error
        if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0x01) {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
        } else {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
        }
        SelRecord->OEMEvData3 = OEM_TYPE_CPU_GEN;
        // ---------------------------- end ----------------------------------------------------
        
        return EFI_SUCCESS;
    }else{
        return McaToIpmiBrh(McaErrorRecord,ErrorBuffer, NULL, NULL, BankIndex);
    }
}
    
/**
    Translate/convert, if possible, error entry from MCA to IPMI format

    @param McaErrorRecord - Pointer to MCA error structure
    @param ErrorBuffer - Buffer with IPMI error entry, only data
    @param DimmInfo - Pointer to DIMM info structure, can be NULL for 
                                            for non DRAM ECC errors
    @param NORMALIZED_ADDRESS   *Address - Pointer to NORMALIZED_ADDRESS structure which has information about the
                                           Channel, socket of DRAM. Can be null for non DRAM ECC errors
    @param BankIndex - MCA bank index

    @retval EFI_SUCCESS - IPMI error log entry created
    @retval EFI_INVALID_PARAMETER - Input param contains null pointer
    @retval EFI_NOT_FOUND - Translation cannot be done
**/

EFI_STATUS
McaToIpmiBrh (
    IN  RAS_MCA_ERROR_INFO_V2  *McaErrorRecord,
    IN  OUT UINT8              *ErrorBuffer,
    IN  DIMM_INFO              *DimmInfo,
    IN  NORMALIZED_ADDRESS     *Address,
    IN  UINT8                  BankIndex )
{
    EFI_STATUS                  Status = EFI_NOT_FOUND;
    UINT8                       ExErrCode;
    IPMI_SEL_EVENT_RECORD_DATA  *SelRecord;
    UINT8                       UMCID;

    DEBUG((DEBUG_LOADFILE, "[AmiRAS] SMM McaToIpmiBrh entry\n"));

    if (EFI_ERROR (InitErrorLogBuffer((VOID *)McaErrorRecord, (VOID *)ErrorBuffer))) {
        return  EFI_INVALID_PARAMETER; 
    }
    

    if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_UMC_ID){
        if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == UMC_MCA_TYPE) && \
                (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val) && \
                (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.ErrorCodeExt == UMC_DCQSRAMECCERR)\
                )
        {

            //Use IPID Instance ID check the UMC channel number.
             UMCID = McaInstanceIdSearch(McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr);
             
             SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
             InitGenericSelInfo (SelRecord);
                     
             // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
             // ---------------------------- start -----------------------------------------------------
             SelRecord->SensorType = SEL_SENSOR_TYPE_OEM;
             SelRecord->SensorNumber = DUMMY_SENSOR_NUMBER;
             SelRecord->OEMEvData2 = (McaErrorRecord->CpuInfo.SocketId << 4) | UMCID;
             SelRecord->OEMEvData3 = OEM_TYPE_UMC;
             
             // Correctable / Uncorrectable error
             if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0x01) {
                 SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
             } else {
                 SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
             }

             // ---------------------------- end ----------------------------------------------------
             
             return EFI_SUCCESS;
        }
        else  
        {
            return MemToIpmiBrh (
                                      McaErrorRecord,
                                      ErrorBuffer,
                                      BankIndex,
                                      DimmInfo,
                                      Address,
                                      NULL
                                      );
            
        }
    }

    if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_DATA_FABRIC_ID) && \
               (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == PIE_MCA_TYPE) && \
               (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
        SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
        InitGenericSelInfo (SelRecord);
        
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        // ---------------------------- start --------------------------------------------------
        SelRecord->SensorType = SEL_SENSOR_TYPE_OEM;
        SelRecord->SensorNumber = OEM_CPU_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
        SelRecord->OEMEvData2 = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
        ExErrCode = ((UINT8)McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.ErrorCodeExt & 0xF);
        // ExErrCode field indicates which bit position MCA_CTL_PIE enables error reporting for the logger error.
        SelRecord->OEMEvData3 = (OEM_TYPE_PIE << 4) | (ExErrCode);
        if(McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.SyndV == 1){
            // Correctable / Uncorrectable error
            if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaSyndMsr.Field.ErrorPriority < 0x03){
                SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
            } else {
                SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
            }
        } else { // Syndrome is not valid
            if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0x01) {
                SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
            } else {
                SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
            }
        }
        // ---------------------------- end ----------------------------------------------------
        Status = EFI_SUCCESS;
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_DATA_FABRIC_ID) && \
               (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == CS_MCA_TYPE) && \
               (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
        
        SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
        InitGenericSelInfo (SelRecord);
        
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        // ---------------------------- start --------------------------------------------------
        SelRecord->SensorType = SEL_SENSOR_TYPE_OEM;
        SelRecord->SensorNumber = OEM_CPU_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
        SelRecord->OEMEvData2 = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
        SelRecord->OEMEvData3 = OEM_TYPE_CS;
        // Correctable / Uncorrectable error
        if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0x01) {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
        } else {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
        }
        // ---------------------------- end ----------------------------------------------------
        Status = EFI_SUCCESS;
   } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_NBIO_ID) && \
               (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == NBIO_MCA_TYPE) && \
               (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)&& \
             (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.ErrorCodeExt != NBIO_PCIE_SIDEBAND)) {
        
        SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
        InitGenericSelInfo (SelRecord);
        
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        // ---------------------------- start -----------------------------------------------------
        SelRecord->SensorType = SEL_SENSOR_TYPE_OEM;
        SelRecord->SensorNumber = OEM_CPU_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
        SelRecord->OEMEvData2 = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
        SelRecord->OEMEvData3 = (OEM_TYPE_NBIO << 4);
        // Correctable / Uncorrectable error
        if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0x01) {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
        } else {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
        }
        // ---------------------------- end ----------------------------------------------------
        
        Status = EFI_SUCCESS;
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_SMU_ID) && \
            (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == SMU_MCA_TYPE) && \
            (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
        
        SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
        InitGenericSelInfo (SelRecord);
        
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        // ---------------------------- start --------------------------------------------------  
        SelRecord->SensorType = SEL_SENSOR_TYPE_OEM;
        SelRecord->SensorNumber = OEM_CPU_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
        SelRecord->OEMEvData2 = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
        // Correctable / Uncorrectable error
        if(McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.SyndV == 1){
            if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaSyndMsr.Field.ErrorPriority == 0x02) {
                SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
            } else {
                SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
            }
        } else { // Syndrome is not valid
            if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0x01) {
                SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
            } else {
                SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
            }
        }
        SelRecord->OEMEvData3 = OEM_TYPE_SMU;
        // ---------------------------- end ----------------------------------------------------
        
        Status = EFI_SUCCESS;
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_SMU_ID) && \
            (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == MP5_MCA_TYPE) && \
            (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
        
        SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
        InitGenericSelInfo (SelRecord);
        
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        // ---------------------------- start --------------------------------------------------  
        SelRecord->SensorType = SEL_SENSOR_TYPE_OEM;
        SelRecord->SensorNumber = OEM_CPU_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
        SelRecord->OEMEvData2 = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
        // Correctable / Uncorrectable error
        if(McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.SyndV == 1){
            if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaSyndMsr.Field.ErrorPriority == 0x02) {
                SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
            } else {
                SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
            }
        } else { // Syndrome is not valid
            if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0x01) {
                SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
            } else {
                SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
            }
        }
        SelRecord->OEMEvData3 = OEM_TYPE_MP5;
        // ---------------------------- end ----------------------------------------------------
        
        Status = EFI_SUCCESS;
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_SMU_ID) && \
            (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == MPDMA_MCA_TYPE) && \
            (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
        
        SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
        InitGenericSelInfo (SelRecord);
        
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        // ---------------------------- start --------------------------------------------------  
        SelRecord->SensorType = SEL_SENSOR_TYPE_OEM;
        SelRecord->SensorNumber = OEM_CPU_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
        SelRecord->OEMEvData2 = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
        // Correctable / Uncorrectable error
        if(McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.SyndV == 1){
            if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaSyndMsr.Field.ErrorPriority == 0x02) {
                SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
            } else {
                SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
            }
        } else { // Syndrome is not valid
            if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0x01) {
                SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
            } else {
                SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
            }
        }
        SelRecord->OEMEvData3 = OEM_TYPE_MPDMA;
        // ---------------------------- end ----------------------------------------------------
        
        Status = EFI_SUCCESS;
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_PSP_ID) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == PSP_MCA_TYPE) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.ErrorCodeExt <= 0x17)&& \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
        
        SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
        InitGenericSelInfo (SelRecord);
        
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        // ---------------------------- start --------------------------------------------------  
        SelRecord->SensorType = SEL_SENSOR_TYPE_OEM;
        SelRecord->SensorNumber = OEM_CPU_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
        SelRecord->OEMEvData2 = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
        // Correctable / Uncorrectable error
        if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0x01) {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
        } else {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
        }
        SelRecord->OEMEvData3 = OEM_TYPE_PSP;
        // ---------------------------- end ----------------------------------------------------
        
        Status = EFI_SUCCESS;
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_PARAMETER_BLOCK_ID) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == PB_MCA_TYPE) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
        
        SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
        InitGenericSelInfo (SelRecord);
        
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        // ---------------------------- start --------------------------------------------------  
        SelRecord->SensorType = SEL_SENSOR_TYPE_OEM;
        SelRecord->SensorNumber = OEM_CPU_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
        SelRecord->OEMEvData2 = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
        // Correctable / Uncorrectable error
        if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0x01) {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
        } else {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
        }
        SelRecord->OEMEvData3 = OEM_TYPE_PB;
        // ---------------------------- end ----------------------------------------------------
        
        Status = EFI_SUCCESS;
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_PCIE_ID) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == PCIE_MCA_TYPE) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
        
        SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
        InitGenericSelInfo (SelRecord);
        
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        // ---------------------------- start --------------------------------------------------  
        SelRecord->SensorType = SEL_SENSOR_TYPE_OEM;
        SelRecord->SensorNumber = OEM_CPU_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
        SelRecord->OEMEvData2 = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
        // Correctable / Uncorrectable error
        if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0x01) {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
        } else {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
        }
        SelRecord->OEMEvData3 = OEM_TYPE_PCIE;
        // ---------------------------- end ----------------------------------------------------
        
        Status = EFI_SUCCESS;
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_CPU_CORE_ID) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == LS_MCA_TYPE) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
        
        SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
        InitGenericSelInfo (SelRecord);
        
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        // ---------------------------- start --------------------------------------------------  
        SelRecord->SensorType = SEL_SENSOR_TYPE_OEM;
        SelRecord->SensorNumber = OEM_CPU_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
        SelRecord->OEMEvData2 = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
        // Correctable / Uncorrectable error
        if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0x01) {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
        } else {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
        }
        SelRecord->OEMEvData3 = OEM_TYPE_LS;
        // ---------------------------- end ----------------------------------------------------
        
        Status = EFI_SUCCESS;
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_CPU_CORE_ID) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == IF_MCA_TYPE) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
        
        SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
        InitGenericSelInfo (SelRecord);
        
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        // ---------------------------- start --------------------------------------------------  
        SelRecord->SensorType = SEL_SENSOR_TYPE_OEM;
        SelRecord->SensorNumber = OEM_CPU_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
        SelRecord->OEMEvData2 = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
        // Correctable / Uncorrectable error
        if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0x01) {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
        } else {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
        }
        SelRecord->OEMEvData3 = OEM_TYPE_IF;
        // ---------------------------- end ----------------------------------------------------
        
        Status = EFI_SUCCESS;
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_CPU_CORE_ID) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == L2_MCA_TYPE) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
        
        SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
        InitGenericSelInfo (SelRecord);
        
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        // ---------------------------- start --------------------------------------------------  
        SelRecord->SensorType = SEL_SENSOR_TYPE_OEM;
        SelRecord->SensorNumber = OEM_CPU_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
        SelRecord->OEMEvData2 = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
        // Correctable / Uncorrectable error
        if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0x01) {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
        } else {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
        }
        SelRecord->OEMEvData3 = OEM_TYPE_L2;
        // ---------------------------- end ----------------------------------------------------
        
        Status = EFI_SUCCESS;
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_CPU_CORE_ID) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == DE_MCA_TYPE) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
        
        SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
        InitGenericSelInfo (SelRecord);
        
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        // ---------------------------- start --------------------------------------------------  
        SelRecord->SensorType = SEL_SENSOR_TYPE_OEM;
        SelRecord->SensorNumber = OEM_CPU_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
        SelRecord->OEMEvData2 = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
        // Correctable / Uncorrectable error
        if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0x01) {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
        } else {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
        }
        SelRecord->OEMEvData3 = OEM_TYPE_DE;
        // ---------------------------- end ----------------------------------------------------
        
        Status = EFI_SUCCESS;
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_CPU_CORE_ID) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == EX_MCA_TYPE) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
        
        SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
        InitGenericSelInfo (SelRecord);
        
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        // ---------------------------- start --------------------------------------------------  
        SelRecord->SensorType = SEL_SENSOR_TYPE_OEM;
        SelRecord->SensorNumber = OEM_CPU_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
        SelRecord->OEMEvData2 = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
        // Correctable / Uncorrectable error
        if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0x01) {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
        } else {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
        }
        SelRecord->OEMEvData3 = OEM_TYPE_EX;
        // ---------------------------- end ----------------------------------------------------
        
        Status = EFI_SUCCESS;
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_CPU_CORE_ID) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == FP_MCA_TYPE) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
        
        SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
        InitGenericSelInfo (SelRecord);
        
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        // ---------------------------- start --------------------------------------------------  
        SelRecord->SensorType = SEL_SENSOR_TYPE_OEM;
        SelRecord->SensorNumber = OEM_CPU_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
        SelRecord->OEMEvData2 = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
        // Correctable / Uncorrectable error
        if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0x01) {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
        } else {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
        }
        SelRecord->OEMEvData3 = OEM_TYPE_FP;
        // ---------------------------- end ----------------------------------------------------
        
        Status = EFI_SUCCESS;
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_CPU_CORE_ID) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == L3_MCA_TYPE) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
        
        SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
        InitGenericSelInfo (SelRecord);
        
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        // ---------------------------- start --------------------------------------------------  
        SelRecord->SensorType = SEL_SENSOR_TYPE_OEM;
        SelRecord->SensorNumber = OEM_CPU_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
        SelRecord->OEMEvData2 = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
        // Correctable / Uncorrectable error
        if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0x01) {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
        } else {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
        }
        SelRecord->OEMEvData3 = OEM_TYPE_L3;
        // ---------------------------- end ----------------------------------------------------
        
        Status = EFI_SUCCESS;
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_NBIF_ID) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == NBIF_MCA_TYPE) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
        
        SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
        InitGenericSelInfo (SelRecord);
        
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        // ---------------------------- start --------------------------------------------------  
        SelRecord->SensorType = SEL_SENSOR_TYPE_OEM;
        SelRecord->SensorNumber = OEM_CPU_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
        SelRecord->OEMEvData2 = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
        // Correctable / Uncorrectable error
        if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0x01) {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
        } else {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
        }
		
		SelRecord->OEMEvData3 = OEM_TYPE_NBIF << 0; //COMPAL_CHANGE OEM_TYPE_NBIF << 4;
       
        // ---------------------------- end ----------------------------------------------------
        
        Status = EFI_SUCCESS;
    } else if (((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_PCS_GMI_ID) || \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_KPX_GMI_ID)) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
        
        SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
        InitGenericSelInfo (SelRecord);
        
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        // ---------------------------- start --------------------------------------------------  
        SelRecord->SensorType = SEL_SENSOR_TYPE_OEM;
        SelRecord->SensorNumber = OEM_XGMI_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
        SelRecord->OEMEvData2 = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
        // Correctable / Uncorrectable error
        if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0x01) {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
        } else {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
        }
        SelRecord->OEMEvData3 = OEM_TYPE_GMI;
        // ---------------------------- end ----------------------------------------------------
        
        Status = EFI_SUCCESS;
    } else if (((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_PCS_XGMI_ID) || \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_KPX_SERDES_ID))   && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
        
        SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
        InitGenericSelInfo (SelRecord);
        
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        // ---------------------------- start --------------------------------------------------  
        SelRecord->SensorType = SEL_SENSOR_TYPE_OEM;
        SelRecord->SensorNumber = OEM_XGMI_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
        SelRecord->OEMEvData2 = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
        // Correctable / Uncorrectable error
        if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0x01) {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
        } else {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
        }
        SelRecord->OEMEvData3 = OEM_TYPE_XGMI;
        // ---------------------------- end ----------------------------------------------------
        
        Status = EFI_SUCCESS;
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_SATA_ID) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == SATA_MCA_TYPE) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
        
        SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
        InitGenericSelInfo (SelRecord);
        
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        // ---------------------------- start --------------------------------------------------  
        SelRecord->SensorType = SEL_SENSOR_TYPE_OEM;
        SelRecord->SensorNumber = OEM_CPU_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
        SelRecord->OEMEvData2 = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
        // Correctable / Uncorrectable error
        if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0x01) {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
        } else {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
        }
        
        SelRecord->OEMEvData3 = OEM_TYPE_SATA << 0; //COMPAL_CHANGE OEM_TYPE_SATA << 4;
        
        
        // ---------------------------- end ----------------------------------------------------
        
        Status = EFI_SUCCESS;
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_USB_ID) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == USB_MCA_TYPE) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
        
        SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
        InitGenericSelInfo (SelRecord);
        
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        // ---------------------------- start --------------------------------------------------  
        SelRecord->SensorType = SEL_SENSOR_TYPE_OEM;
        SelRecord->SensorNumber = OEM_CPU_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
        SelRecord->OEMEvData2 = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
        // Correctable / Uncorrectable error
        if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0x01) {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
        } else {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
        }
        SelRecord->OEMEvData3 = OEM_TYPE_USB;
        // ---------------------------- end ----------------------------------------------------
        
        Status = EFI_SUCCESS;
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_SHUB_ID) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == SHUB_MCA_TYPE) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
        
        SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
        InitGenericSelInfo (SelRecord);
        
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        // ---------------------------- start --------------------------------------------------  
        SelRecord->SensorType = SEL_SENSOR_TYPE_OEM;
        SelRecord->SensorNumber = OEM_CPU_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
        SelRecord->OEMEvData2 = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
        // Correctable / Uncorrectable error
        if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0x01) {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
        } else {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
        }
        SelRecord->OEMEvData3 = OEM_TYPE_SHUB;
        // ---------------------------- end ----------------------------------------------------
        
        Status = EFI_SUCCESS;
    } else if ((McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_PSP_ID) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == PSP_MCA_TYPE) && \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.ErrorCodeExt == 0x3F)&& \
         (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)) {
        
        SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
        InitGenericSelInfo (SelRecord);
        
        // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
        // ---------------------------- start --------------------------------------------------  
        SelRecord->SensorType = SEL_SENSOR_TYPE_OEM;
        SelRecord->SensorNumber = OEM_CPU_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
        SelRecord->OEMEvData2 = (McaErrorRecord->CpuInfo.SocketId << 4) | McaErrorRecord->CpuInfo.DieId;
        // Correctable / Uncorrectable error
        if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0x01) {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
        } else {
            SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
        }
        SelRecord->OEMEvData3 = OEM_TYPE_WAFL;
        // ---------------------------- end ----------------------------------------------------
        
        Status = EFI_SUCCESS;
    } else { 
        Status = EFI_NOT_FOUND;
    }
    return Status;
}

/**
    Translate/convert, if possible, error entry from memory MCA to IPMI format

    @param McaErrorRecord - Pointer to MCA error structure
    @param ErrorBuffer - Buffer with IPMI error entry, only data
    @param BankIndex - MCA bank index
    @param ProcessorNumber - logical CPU number
    @param GenericMemErrEntryBuffer - pointer to generic Memory Error entry

    @retval EFI_SUCCESS - IPMI error log entry created
    @retval EFI_INVALID_PARAMETER - Input param contains null pointer
    @retval EFI_NOT_FOUND - Translation cannot be done
**/

EFI_STATUS
MemToIpmiBrh (
    IN     RAS_MCA_ERROR_INFO_V2    *McaErrorRecord,
    IN OUT UINT8                    *ErrorBuffer,
    IN     UINT8                    BankIndex,
    IN     DIMM_INFO                *DimmInfo,
    IN     NORMALIZED_ADDRESS       *Address,
    IN     GENERIC_MEM_ERR_ENTRY_V3 *GenericMemErrEntryBuffer
    )
{
    EFI_STATUS                  Status = EFI_NOT_FOUND;
    UINTN                       EccType = 0;
    UINT8                       DramErrorType;
    UINT8                       Symbol;
    UINT16                      Syndrome;
    IPMI_SEL_EVENT_RECORD_DATA  *SelRecord;   
    UINT8                       ChannelId;
    UINT32                      ChipSelectBit;
    UINT16                      ChipSelect;
    
    DEBUG((DEBUG_LOADFILE, "[AmiRAS] SMM MemToIpmiBrh entry\n"));

    if (EFI_ERROR (InitErrorLogBuffer((VOID *)McaErrorRecord, (VOID *)ErrorBuffer))) {
        return  EFI_INVALID_PARAMETER; 
    }
    
    if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_UMC_ID) {
        
        SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
        InitGenericSelInfo (SelRecord);
        
        SelRecord->SensorType = SEL_SENSOR_TYPE_MEMORY;
        SelRecord->SensorNumber = OEM_CPU0_DIMM_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
        
        DramErrorType = (UINT8)McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.ErrorCodeExt; 

        //Use IPID Instance ID check the UMC channel number.
            ChannelId = McaInstanceIdSearch(McaErrorRecord->McaBankErrorInfo[BankIndex].McaIpidMsr);
        
        //Module = (ChipSelect & 0x03) >> 1, ChipSelect 0,1 = Module 0, ChipSelect 2,3 = Module 1
        ChipSelectBit = (UINT32)(McaErrorRecord->McaBankErrorInfo[BankIndex].McaAddrMsr.Field.ErrorAddr >> 32) & 0x0F;

        if (ChipSelectBit == 0) {
                ChipSelect = 0x00;
        }else {
                ChipSelect = (UINT16)RasBitPositionToInt(ChipSelectBit);
        }

        DEBUG ((DEBUG_LOADFILE,"MCA UMC Error:Type %x Socket %x Channel %x DIMM %x\n",\
                DramErrorType, McaErrorRecord->CpuInfo.SocketId, ChannelId, ChipSelect));
        if (DramErrorType == DramEccErr || DramErrorType == WriteDataPoisonErr || DramErrorType == EcsRowErr) {
            //
            // OEMEvData2(8 bits) will have UMC instance id (bits 4-7) and DIMM number( bits 0-3).
            //
            if (Address != NULL && DimmInfo != NULL) {
                SelRecord->OEMEvData2 = (Address->normalizedChannelId << 4) |((DimmInfo->ChipSelect > 1)?1:0);
            } else {
                if (GenericMemErrEntryBuffer != NULL) {
                    SelRecord->OEMEvData2 = ((UINT8)GenericMemErrEntryBuffer->MemErrorSection.Card << 4) |(UINT8)GenericMemErrEntryBuffer->MemErrorSection.Module;
                } else {
                    return EFI_INVALID_PARAMETER;
                }
            }
            if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 1) {
                SelRecord->OEMEvData1 |= SEL_MEMORY_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
            } else if( McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Deferred == 1) {
                SelRecord->OEMEvData1 |= SEL_MEMORY_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
            }
            if (DramErrorType == DramEccErr){
                SelRecord->OEMEvData3 = (MEM_TYPE_ECC << 4) | (UINT8)McaErrorRecord->CpuInfo.SocketId;
            } else if (DramErrorType == WriteDataPoisonErr) {
                SelRecord->OEMEvData3 = (MEM_TYPE_DATA_POISON << 4) | (Address->normalizedSocketId);
            } else if (DramErrorType == EcsRowErr){
                SelRecord->OEMEvData3 = (MEM_TYPE_ECSROW << 4) | (Address->normalizedSocketId);
            }
            //
            // Check if this is a Corrected Error
            //
            if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaSyndMsr.Field.ErrorPriority == MCA_ERROR_CORRECTED) {
                //
                // Check if syndrome valid
                //
                if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaSyndMsr.Field.Length != 0) {
                    Symbol = (UINT8)((McaErrorRecord->McaBankErrorInfo[BankIndex].McaSyndMsr.Field.ErrorInformation >> 8)& 0x3F - 1);
                    Syndrome = (UINT16)(McaErrorRecord->McaBankErrorInfo[BankIndex].McaSyndMsr.Field.Syndrome);
                        if (IsMultiBitErrorBrh(Symbol,Syndrome) == TRUE) {
                       EccType = 2;
                    } else {
                       EccType = 1;
                    }
                }
            }
            //
            // OEM PORTING NOTE - this block of code can be used to add details about Corr DRAM ECC error - 
            // Single or Multibit 
            if (EccType == 1) { //single bit

            } else if (EccType == 2) { // Multi Bit

            } else { // Error is not corrected or not enough information

            }
            Status = EFI_SUCCESS;

        } else if (DramErrorType == AddressCommandParityErr || DramErrorType == WriteDataCrcErr) {
            //
            // OEM PORTING NOTE - below values are just dummy, need to map with existing OEM error list
            // OEMEvData2(8 bits) will have UMC instance id (bits 4-7) and DIMM number( bits 0-3).

            SelRecord->OEMEvData2 = (ChannelId << 4) |((ChipSelect & 0x03) >> 1);

            if (McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 1) {
                SelRecord->OEMEvData1 |= SEL_MEMORY_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
            } else if( McaErrorRecord->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Deferred == 1) {
                SelRecord->OEMEvData1 |= SEL_MEMORY_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
            }

            if(DramErrorType == AddressCommandParityErr) {
                SelRecord->OEMEvData3 = (MEM_TYPE_PARITY << 4) | (McaErrorRecord->CpuInfo.SocketId);
            } else {
                SelRecord->OEMEvData3 = (MEM_TYPE_CRC << 4) | (McaErrorRecord->CpuInfo.SocketId);
            }

            Status = EFI_SUCCESS;

        } else {
           //
           // Other error types
           // 
           Status = EFI_NOT_FOUND;
        }
//COMPAL_CHANGE >>>
        if(Status==EFI_SUCCESS)
        {
            if(SelRecord->OEMEvData3 & 0x0F) {  // Socket 1?
                SelRecord->SensorNumber += 0x20;   //OEM_CPU1_DIMM_SENSOR
            }
            
            switch(SelRecord->OEMEvData2 >> 4)  //Channel/UMC ID
            {
                case 0:  //channel C0
                    SelRecord->SensorNumber += 4;
                    break;              
                case 1:  //channel E0
                    SelRecord->SensorNumber += 8;
                    break;
                case 2:  //channel F0
                    SelRecord->SensorNumber += 0x0A;
                    break;
                case 3:  //channel A0
                default:
                    
                    break;
                case 4:  //channel B0
                    SelRecord->SensorNumber += 2;
                    break;
                case 5:  //channel D0
                    SelRecord->SensorNumber += 6;
                    break;
                case 6:  //channel I0
                    SelRecord->SensorNumber += 0x10;
                    break;
                case 7:  //channel K0
                    SelRecord->SensorNumber += 0x14;
                    break;
                case 8:  //channel L0
                    SelRecord->SensorNumber += 0x16;
                    break;
                case 9:  //channel G0
                    SelRecord->SensorNumber += 0xC;
                    break;
                case 0xA:  //channel H0
                    SelRecord->SensorNumber += 0xE;
                    break;
                case 0xB:  //channel J0
                    SelRecord->SensorNumber += 0x12;
                    break;
            }
            if (SelRecord->OEMEvData2 & 0xF) {  // If DIMM 1 ?
                SelRecord->SensorNumber++;
            }
        }
//COMPAL_CHANGE <<<
    }
    return Status;
}

/**
    Translate/convert, if possible, error entry from NBIO to Ipmi format

    @param NbioErrorRecord - Pointer to NBIO error structure
    @param ErrorBuffer - Buffer with IPMI error entry, only data

    @retval EFI_SUCCESS - IPMI error log entry created
    @retval EFI_INVALID_PARAMETER - Input param contains null pointer
    @retval EFI_NOT_FOUND - Translation cannot be done
**/

EFI_STATUS
NbioToIpmiBrh (
    IN  RAS_NBIO_ERROR_INFO  *NbioErrorRecord,
    IN OUT UINT8             *ErrorBuffer )
{
    IPMI_SEL_EVENT_RECORD_DATA  *SelRecord;

    if (EFI_ERROR (InitErrorLogBuffer((VOID *)NbioErrorRecord, (VOID *)ErrorBuffer))) {
        return  EFI_INVALID_PARAMETER; 
    }

    SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
    InitGenericSelInfo (SelRecord);
    
// PORTING PORTING    
    SelRecord->SensorType = SEL_SENSOR_TYPE_OEM; //COMPAL_CHANGE SEL_SENSOR_TYPE_CRITICAL_INTERRUPT;
    SelRecord->SensorNumber = OEM_CPU_SENSOR;    //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
    SelRecord->OEMEvData2 = 0x00;
    SelRecord->OEMEvData3 = (OEM_TYPE_NBIO << 4);

// --------------------------------------------------------
    return EFI_SUCCESS;
}

BOOLEAN
IsSwitchDownstreamPort(
    IN UINT8 Bus,
    IN UINT8 Dev,
    IN UINT8 Fun
    )
{
    UINT16 VendorId = PciSegmentRead16(PCI_LIB_ADDRESS(Bus, Dev, Fun, PCI_VID));
    UINT16 DeviceId = PciSegmentRead16(PCI_LIB_ADDRESS(Bus, Dev, Fun, PCI_DID));
    UINT32 ClassCode = PciSegmentRead32(PCI_LIB_ADDRESS(Bus, Dev, Fun, PCI_RID)) & ~0xFF;

    if ((VendorId == 0x1000) && (DeviceId == 0xC030) && (ClassCode == 0x06040000)) {
        return TRUE;
    }
    return FALSE;
}

BOOLEAN
FindUpstreamPortOfEndpoint (
    IN  UINT8 EndBus,
    IN  UINT8 EndDev,
    IN  UINT8 EndFun,
    OUT UINT8 *UpBus,
    OUT UINT8 *UpDev,
    OUT UINT8 *UpFun
    )
{
    for (UINT8 bus = 0; bus < 256; bus++) {
        for (UINT8 dev = 0; dev < 32; dev++) {
            for (UINT8 fun = 0; fun < 8; fun++) {
                UINT16 VendorId = PciSegmentRead16(PCI_LIB_ADDRESS(bus, dev, fun, PCI_VID));
                if (VendorId == 0xFFFF) continue; 
                UINT32 ClassCode = PciSegmentRead32(PCI_LIB_ADDRESS(bus, dev, fun, PCI_RID)) & ~0xFF;
                if (ClassCode != 0x06040000) continue; 
                UINT8 SecBus = PciSegmentRead8(PCI_LIB_ADDRESS(bus, dev, fun, PCI_SBUS));
                if (SecBus == EndBus) {
                    *UpBus = bus;
                    *UpDev = dev;
                    *UpFun = fun;
                    return TRUE; 
                }
            }
        }
    }
    return FALSE;
}

//COMPAL_CHANGE >>>
VOID
OEM_PcieSensorNumber (
    IN OUT IPMI_SEL_EVENT_RECORD_DATA  *SelRecord, 
    IN     GENERIC_PCIE_AER_ERR_ENTRY_V3  *GenPcie )
{
    //EFI_STATUS        Status = EFI_SUCCESS;
    UINT16            PciePortIndex=0;
    PCIE_PORT_PROFILE    *PciePortProfileInstance;
    UINT8             ErrorBus = (UINT8)GenPcie->PcieAerErrorSection.DeviceId.PrimaryBus;
    UINT8             ErrorDevice = (UINT8)GenPcie->PcieAerErrorSection.DeviceId.Device;
    UINT8             ErrorFunction = (UINT8)GenPcie->PcieAerErrorSection.DeviceId.Function;
    UINT16            VendorID=0, DeviceID=0;
    UINT32            ClassID=0;
    PCI_ADDR          PciAddr;
    UINT8             Offset=0;
    PCI_REG_PCIE_CAPABILITY         PCIeCap;
    PCI_REG_PCIE_SLOT_CAPABILITY    SlotCap;
    //UINTN             NumberOfSockets=0;
    UINT16            SlotIndex=0;
	UINT8	MaxMcaBankCount = 0;


    DEBUG ((DEBUG_LOADFILE,"[COMPAL] OEM_PcieSensorNumber start.\n"));
    DEBUG ((DEBUG_LOADFILE,"Inject Err Bus:0x%X Dev:0x%X Fun:0x%X\n", ErrorBus, ErrorDevice, ErrorFunction));
    
    if (mPlatformApeiData == NULL) {
        DEBUG ((DEBUG_LOADFILE,"mPlatformApeiData is NULL !!!\n"));
        return;
    }
	
	MaxMcaBankCount = mPlatformApeiData->MaxMcaBankCount;
	DEBUG ((DEBUG_LOADFILE,"MaxMcaBankCount:0x%X \n", MaxMcaBankCount));

//    NumberOfSockets = (UINTN) FabricTopologyGetNumberOfProcessorsPresent ();

    VendorID = PciSegmentRead16(PCI_LIB_ADDRESS(ErrorBus, ErrorDevice, ErrorFunction, PCI_VID));
    DeviceID = PciSegmentRead16(PCI_LIB_ADDRESS(ErrorBus, ErrorDevice, ErrorFunction, PCI_DID));
    ClassID = PciSegmentRead32(PCI_LIB_ADDRESS(ErrorBus, ErrorDevice, ErrorFunction, PCI_RID)) & ~0xFF;
    DEBUG ((DEBUG_LOADFILE,"Inject Vendor, Device and Class ID: 0x%X, 0x%X, 0x%X \n", VendorID, DeviceID, ClassID));
    if ( (VendorID == 0x1022) && (ClassID == 0x06040000) ) {  //Is AMD PCIe Bridge
        PciAddr.AddressValue = (UINT32) PCI_LIB_ADDRESS(ErrorBus, ErrorDevice, ErrorFunction, 0);
	} else {
	    DEBUG ((DEBUG_LOADFILE,"Not AMD PCIe bridge\n"));
	    PciePortProfileInstance = mPlatformApeiData->AmdPciePortMap->PciPortNumber;
	    DEBUG ((DEBUG_LOADFILE,"PortCount = %d.\n", mPlatformApeiData->AmdPciePortMap->PortCount));

	    if (IsSwitchDownstreamPort(ErrorBus, ErrorDevice, ErrorFunction)) {
	        PciAddr.AddressValue = (UINT32)PCI_LIB_ADDRESS(ErrorBus, ErrorDevice, ErrorFunction, 0);
	        DEBUG ((DEBUG_LOADFILE, "[Switch] Downstream port detected at B%02x D%02x F%x, use its own BDF for slot agent.\n",
	            ErrorBus, ErrorDevice, ErrorFunction));
	    } else {
	        UINT8 UpBus = 0, UpDev = 0, UpFun = 0;
	        if (FindUpstreamPortOfEndpoint(ErrorBus, ErrorDevice, ErrorFunction, &UpBus, &UpDev, &UpFun)) {
	            PciAddr.AddressValue = PCI_LIB_ADDRESS(UpBus, UpDev, UpFun, 0);
	            DEBUG ((DEBUG_LOADFILE, "[Switch] Endpoint detected at B%02x D%02x F%x, upstream port B%02x D%02x F%x will be used.\n",
	                ErrorBus, ErrorDevice, ErrorFunction, UpBus, UpDev, UpFun));
	        } else {
	            for (PciePortIndex = 0; (PciePortIndex < mPlatformApeiData->AmdPciePortMap->PortCount); PciePortIndex++) {
            		DEBUG ((DEBUG_LOADFILE,"Find PortIndex = %x, RpPciAddr = %x.\n", PciePortIndex, PciePortProfileInstance[PciePortIndex].RpPciAddr));
	                if ((PciSegmentRead32 (PciePortProfileInstance[PciePortIndex].RpPciAddr + PCI_RID) & ~0xFF) != 0x06040000) {
	                    continue;
	                }
	                if (PciSegmentRead8 (PciePortProfileInstance[PciePortIndex].RpPciAddr + PCI_SBUS) == ErrorBus) {
	                    PciAddr.AddressValue = PciePortProfileInstance[PciePortIndex].RpPciAddr;
	                    break;
	                }
	            }
	        }
	    }
	}
    if (PciePortIndex >= mPlatformApeiData->AmdPciePortMap->PortCount) {
        //No detect
        DEBUG ((DEBUG_LOADFILE,"No detect with root port.\n"));
    } else {
        Offset = RasFindPciCapability (PciAddr.AddressValue, PCIE_CAP_ID);
        
        PCIeCap.Uint16 = PciSegmentRead16(PciAddr.AddressValue + Offset + 2);
        SlotCap.Uint32 = PciSegmentRead32(PciAddr.AddressValue + Offset + PCIE_SLOT_CAP_REGISTER);
        
        DEBUG ((DEBUG_LOADFILE,"Match PortIndex:%X, RpPciAddr.AddressValue:0x%X, CapPtr=0x%X, SlotImplemented=%X, Slot=0x%X\n", PciePortIndex, PciAddr.AddressValue, Offset, PCIeCap.Bits.SlotImplemented, SlotCap.Bits.PhysicalSlotNumber));

        for (SlotIndex = 0; SlotIndex < mCustomSlotMapCount; SlotIndex++) {
            if (SlotCap.Bits.PhysicalSlotNumber == mCustomSlotMap[SlotIndex].PhysicalSlot) {
                SelRecord->SensorNumber = mCustomSlotMap[SlotIndex].SensorNumber;
                DEBUG ((DEBUG_LOADFILE,
                    "[COMPAL] custom slot %u -> sensor 0x%X\n",
                    SlotCap.Bits.PhysicalSlotNumber,
                    SelRecord->SensorNumber
                ));
                DEBUG ((DEBUG_LOADFILE, "[COMPAL] OEM_PcieSensorNumber exit.\n"));
                return;
            }
        }

        if (PCIeCap.Bits.SlotImplemented) {
            if (SlotCap.Bits.PhysicalSlotNumber >= NVME_SLOT_NUMBER) { // NVME slot number define
                SelRecord->SensorNumber = (UINT8)(OEM_NVME_SENSOR + (SlotCap.Bits.PhysicalSlotNumber - NVME_SLOT_NUMBER));
            } else if (SlotCap.Bits.PhysicalSlotNumber >= OCP_SLOT_NUMBER) { // OCP slot number define
                SelRecord->SensorNumber = (UINT8)(OEM_OCP_SENSOR + (SlotCap.Bits.PhysicalSlotNumber - OCP_SLOT_NUMBER));
            } else if (SlotCap.Bits.PhysicalSlotNumber >= M2_SLOT_NUMBER) { // M.2 slot number define
                SelRecord->SensorNumber = (UINT8)(OEM_M2_SENSOR + (SlotCap.Bits.PhysicalSlotNumber - M2_SLOT_NUMBER));
            } else {    // PCIe slot number start from 1
                SelRecord->SensorNumber = (UINT8)(OEM_PCIE_SENSOR + SlotCap.Bits.PhysicalSlotNumber);
            }
        } else {
            SelRecord->SensorNumber = OEM_PCIE_SENSOR;
        }
    }   //if (PciePortIndex >= mPlatformApeiData->AmdPciePortMap->PortCount)
    //--------------------------------------------------------------------------------------
    
    DEBUG ((DEBUG_LOADFILE,"[COMPAL] OEM_PcieSensorNumber exit: SensorNumber = %x.\n", SelRecord->SensorNumber));
}
//COMPAL_CHANGE <<<

/**
    Translate/convert, if possible, error entry from PCIe to IPMI format

    @param GenPcieAerErrEntry - Pointer to GENERIC_PCIE_AER_ERR_ENTRY structure
    @param ErrorBuffer - Buffer with IPMI error entry, only data

    @retval EFI_SUCCESS - IPMI error log entry created
    @retval EFI_INVALID_PARAMETER - input param contains null pointer
    @retval EFI_NOT_FOUND - translation cannot be done
**/

EFI_STATUS
PcieToIpmiBrh (
  	IN     VOID     *PcieErrorEntry,
    IN OUT UINT8    *ErrorBuffer )
{
    IPMI_SEL_EVENT_RECORD_DATA  *SelRecord;
    GENERIC_PCIE_AER_ERR_ENTRY_V3   *GenPcie;

    if (EFI_ERROR (InitErrorLogBuffer((VOID *)PcieErrorEntry, (VOID *)ErrorBuffer))) {
        return  EFI_INVALID_PARAMETER; 
    }

    SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
    InitGenericSelInfo (SelRecord);
    
    GenPcie = (GENERIC_PCIE_AER_ERR_ENTRY_V3*)PcieErrorEntry;
    
// PORTING PORTING    
    SelRecord->SensorType = SEL_SENSOR_TYPE_CRITICAL_INTERRUPT;
    SelRecord->SensorNumber = OEM_PCIE_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
    if (GenPcie->GenErrorDataEntry.ErrorSeverity == ERROR_RECOVERABLE || GenPcie->GenErrorDataEntry.ErrorSeverity == ERROR_SEVERITY_FATAL) {
        SelRecord->OEMEvData1 |= SEL_CRITICAL_INTERRUPT_SENSOR_OFFSET_PCI_SERR;
    } else if (GenPcie->GenErrorDataEntry.ErrorSeverity == ERROR_SEVERITY_CORRECTED) {
       SelRecord->OEMEvData1 |= SEL_CRITICAL_INTERRUPT_SENSOR_OFFSET_PCI_PERR;
    } else {
       SelRecord->OEMEvData1 |= SEL_CRITICAL_INTERRUPT_SENSOR_OFFSET_PCI_PERR ;
    }
    SelRecord->OEMEvData2 = (UINT8)GenPcie->PcieAerErrorSection.DeviceId.PrimaryBus ;
    SelRecord->OEMEvData3 = (UINT8)( GenPcie->PcieAerErrorSection.DeviceId.Device << 3 | 
            GenPcie->PcieAerErrorSection.DeviceId.Function);
// -------------------------------
    OEM_PcieSensorNumber(SelRecord, GenPcie);  //COMPAL_CHANGE
    
    return EFI_SUCCESS;
}

/**
    Translate/convert, if possible, error entry from Memory test error log to IPMI format

    @param GenMemErroEntry - Pointer to GENERIC_MEM_ERR_ENTRY_V3 structure
    @param ErrorBuffer - Buffer with IPMI error entry, only data

    @retval EFI_SUCCESS - IPMI error log entry created
    @retval EFI_INVALID_PARAMETER - Input param contains null pointer
    @retval EFI_NOT_FOUND - Translation cannot be done
**/

EFI_STATUS
MemTestToIpmiBrh (
  IN     GENERIC_MEM_ERR_ENTRY_V3 *GenMemErroEntry,
  IN OUT UINT8                    *ErrorBuffer,
  IN     UINT16                   Node,
  IN     UINT16                   Card,
  IN     UINT16                   Module
  )
{
    IPMI_SEL_EVENT_RECORD_DATA  *SelRecord;
    
    if (EFI_ERROR (InitErrorLogBuffer((VOID *)GenMemErroEntry, (VOID *)ErrorBuffer))) {
        return  EFI_INVALID_PARAMETER; 
    }
    
    SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
    InitGenericSelInfo (SelRecord);
    
    SelRecord->SensorType = SEL_SENSOR_TYPE_MEMORY;
    // PORTING PORTING        
    SelRecord->SensorNumber = OEM_CPU0_DIMM_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
    DEBUG ((DEBUG_LOADFILE,"Mem Test Error: Socket %x Channel %x DIMM %x\n",\
            GenMemErroEntry->MemErrorSection.Node,GenMemErroEntry->MemErrorSection.Card,GenMemErroEntry->MemErrorSection.Module));
    //
    // OEMEvData2(8 bits) will have UMC instance id (bits 4-7) and DIMM number( bits 0-3).
    // For UMC instance id to Channel mapping please refer the PPR Volume1 table 134 (Instance to package channel mapping)
    //
    SelRecord->OEMEvData2 = (UINT8)((GenMemErroEntry->MemErrorSection.Card << 4) |((GenMemErroEntry->MemErrorSection.Module)));
    //Memory test or Training is failed for this DIMM. So marking it as disabled
    //If needed use GenMemErroEntry->GenErrorDataEntry.ErrorSeverity to determine the type
    SelRecord->OEMEvData1 |= SEL_MEMORY_SENSOR_OFFSET_DEVICE_DISABLED;
    SelRecord->OEMEvData3 = (UINT8)GenMemErroEntry->MemErrorSection.Node;
    // --------------------------------------------------------
//COMPAL_CHANGE >>>
    if(SelRecord->OEMEvData3 & 0x0F) {     //Socket 1?
        SelRecord->SensorNumber += 0x20;   //OEM_CPU1_DIMM_SENSOR
    }
        
    switch(SelRecord->OEMEvData2 >> 4)  //Channel/UMC ID
    {
        case 0:  //channel C0
            SelRecord->SensorNumber += 4;
            break;              
        case 1:  //channel E0
            SelRecord->SensorNumber += 8;
            break;
        case 2:  //channel F0
            SelRecord->SensorNumber += 0x0A;
            break;
        case 3:  //channel A0
        default:
            
            break;
        case 4:  //channel B0
            SelRecord->SensorNumber += 2;
            break;
        case 5:  //channel D0
            SelRecord->SensorNumber += 6;
            break;
        case 6:  //channel I0
            SelRecord->SensorNumber += 0x10;
            break;
        case 7:  //channel K0
            SelRecord->SensorNumber += 0x14;
            break;
        case 8:  //channel L0
            SelRecord->SensorNumber += 0x16;
            break;
        case 9:  //channel G0
            SelRecord->SensorNumber += 0xC;
            break;
        case 0xA:  //channel H0
            SelRecord->SensorNumber += 0xE;
            break;
        case 0xB:  //channel J0
            SelRecord->SensorNumber += 0x12;
            break;
    }
    if (SelRecord->OEMEvData2 & 0xF) {  // If DIMM 1 ?
        SelRecord->SensorNumber++;
    }
//COMPAL_CHANGE <<<
    
    return EFI_SUCCESS;
}

EFI_STATUS
SMNToIpmiBrh (
    IN     GENERIC_SMN_ERR_ENTRY_V3  *GenSmnErrEntry,
    IN OUT UINT8                     *ErrorBuffer )
{
    EFI_STATUS                  Status;
    IPMI_SEL_EVENT_RECORD_DATA  *SelRecord;

    if (EFI_ERROR (InitErrorLogBuffer((VOID *)GenSmnErrEntry, (VOID *)ErrorBuffer))) {
        return  EFI_INVALID_PARAMETER; 
    }

    SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
    InitGenericSelInfo (SelRecord);
    
// PORTING PORTING    
    SelRecord->SensorType = SEL_SENSOR_TYPE_OEM; 
    SelRecord->SensorNumber = OEM_CPU_SENSOR;   //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
    
    if (GenSmnErrEntry->GenErrorDataEntry.ErrorSeverity == ERROR_SEVERITY_FATAL) {
        SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
    } else if (GenSmnErrEntry->GenErrorDataEntry.ErrorSeverity == ERROR_SEVERITY_CORRECTED) {
        SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
    } else {
        SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR ;
    }
    SelRecord->OEMEvData2 = GenSmnErrEntry->SmnErrorSection.BusId; 
//COMPAL_CHANGE    SelRecord->OEMEvData3 = (UINT8)((OEM_TYPE_SMN << 4) | (GenSmnErrEntry->SmnErrorSection.ValidationBits.Field.ErrorSource));
    SelRecord->OEMEvData3 = (UINT8)((OEM_TYPE_SMN << 1) | (GenSmnErrEntry->SmnErrorSection.ValidationBits.Field.ErrorSource));  //COMPAL_CHANGE

    Status = EFI_SUCCESS;
// -------------------------------
    return Status;
}

/**
    Translate/convert, if possible, error entry from  CXL  error log to IPMI format
    
    @param GenSmnECxlErrorLogData - Pointer to CXL_ERROR_LOG_DATA structure 
    @param ErrorBuffer - Buffer with IPMI error entry, only data
    @param GenCxlErrEntry - Pointer to GENERIC_CXL_ERR_ENTRY_V3 structure

    @retval EFI_SUCCESS - IPMI error log entry created
    @retval EFI_INVALID_PARAMETER - Input param contains null pointer
**/

EFI_STATUS
CxlProtocolToIpmiBrh (
    IN     CXL_ERROR_LOG_DATA       *GenSmnECxlErrorLogData,
    IN OUT UINT8                    *ErrorBuffer,
    IN     GENERIC_CXL_ERR_ENTRY_V3 *GenCxlErrEntry
    )
{
    IPMI_SEL_EVENT_RECORD_DATA  *SelRecord;
    
    if (EFI_ERROR (InitErrorLogBuffer((VOID *)GenSmnECxlErrorLogData, (VOID *)ErrorBuffer))) {
        return  EFI_INVALID_PARAMETER;
    }
    
    SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
    InitGenericSelInfo (SelRecord);
    
    if(GenCxlErrEntry->CxlErrorSection.CxlAgentType == 5){
        SelRecord->SensorType = SEL_SENSOR_TYPE_CXL_2_0;
    } else {
        SelRecord->SensorType = SEL_SENSOR_TYPE_CXL;
    }
    SelRecord->SensorNumber = OEM_PCIE_SENSOR;   //COMPAL_CHANGE SEL_CXL_SENSOR_PROTOCOL_ERROR;
    
    //Store Error Severity
    if (GenCxlErrEntry->GenErrorDataEntry.ErrorSeverity == ERROR_RECOVERABLE || GenCxlErrEntry->GenErrorDataEntry.ErrorSeverity == ERROR_SEVERITY_FATAL) {
        SelRecord->OEMEvData1 |= SEL_CXL_SENSOR_UNCORRECTABLE_ERROR;
    } else if (GenCxlErrEntry->GenErrorDataEntry.ErrorSeverity == ERROR_SEVERITY_CORRECTED) {
        SelRecord->OEMEvData1 |= SEL_CXL_SENSOR_CORRECTABLE_ERROR;
    } else {
        SelRecord->OEMEvData1 |= SEL_CXL_SENSOR_CORRECTABLE_ERROR ;
    }
    
    //Device Type
    if(GenCxlErrEntry->CxlErrorSection.CxlAgentType == 5){
        SelRecord->OEMEvData1 |= (UINT8)(GenCxlErrEntry->CxlErrorSection.CxlAgentType >> 1);
    } else {
        SelRecord->OEMEvData1 |= (UINT8)(GenCxlErrEntry->CxlErrorSection.CxlAgentType << 1);
    }
    
    //Store Cache/Mem error
    SelRecord->OEMEvData1 |= (UINT8)(CXL_ELOG_CACHE_MEM << 2);
    
    SelRecord->OEMEvData2 = (UINT8)GenCxlErrEntry->CxlErrorSection.CxlAgentAddress.Device.BusNum ;
    SelRecord->OEMEvData3 = (UINT8)(GenCxlErrEntry->CxlErrorSection.CxlAgentAddress.Device.DeviceNum << 3 |
            GenCxlErrEntry->CxlErrorSection.CxlAgentAddress.Device.FunctionNum);
    
    return EFI_SUCCESS;
}

/**
    Translate/convert, if possible, error entry from  CXL  error log to IPMI format
    
    @param GenSmnECxlErrorLogData - Pointer to CXL_ERROR_LOG_DATA structure 
    @param ErrorBuffer - Buffer with IPMI error entry, only data
    @param GenCxlErrEntry - Pointer to GENERIC_CXL_ERR_ENTRY_V3 structure

    @retval EFI_SUCCESS - IPMI error log entry created
    @retval EFI_INVALID_PARAMETER - Input param contains null pointer
**/

EFI_STATUS
CxlComponentToIpmiBrh (
    IN     CXL_ERROR_LOG_DATA       *GenSmnECxlErrorLogData,
    IN OUT UINT8                    *ErrorBuffer,
    IN     GENERIC_CXL_ERR_ENTRY_V3 *GenCxlErrEntry
    )
{
    IPMI_SEL_EVENT_RECORD_DATA  *SelRecord;
    
    if (EFI_ERROR (InitErrorLogBuffer((VOID *)GenSmnECxlErrorLogData, (VOID *)ErrorBuffer))) {
        return  EFI_INVALID_PARAMETER;
    }
    
    SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
    InitGenericSelInfo (SelRecord);
    if(GenCxlErrEntry->CxlErrorSection.CxlAgentType == 5){
        SelRecord->SensorType = SEL_SENSOR_TYPE_CXL_2_0;
    } else {
        SelRecord->SensorType = SEL_SENSOR_TYPE_CXL;
    }
    SelRecord->SensorNumber = OEM_PCIE_SENSOR;   //COMPAL_CHANGE SEL_CXL_SENSOR_COMPONENT_ERROR;
    
    //Store Error Severity
    if (GenCxlErrEntry->GenErrorDataEntry.ErrorSeverity == ERROR_RECOVERABLE || GenCxlErrEntry->GenErrorDataEntry.ErrorSeverity == ERROR_SEVERITY_FATAL) {
        SelRecord->OEMEvData1 |= SEL_CXL_SENSOR_UNCORRECTABLE_ERROR;
    } else if (GenCxlErrEntry->GenErrorDataEntry.ErrorSeverity == ERROR_SEVERITY_CORRECTED) {
        SelRecord->OEMEvData1 |= SEL_CXL_SENSOR_CORRECTABLE_ERROR;
    } else {
        SelRecord->OEMEvData1 |= SEL_CXL_SENSOR_CORRECTABLE_ERROR ;
    }
    
    //Device Type
    if(GenCxlErrEntry->CxlErrorSection.CxlAgentType == 5){
        SelRecord->OEMEvData1 |= (UINT8)(GenCxlErrEntry->CxlErrorSection.CxlAgentType >> 1);
    } else {
        SelRecord->OEMEvData1 |= (UINT8)(GenCxlErrEntry->CxlErrorSection.CxlAgentType << 1);
    }
    
    //Store Component error
    SelRecord->OEMEvData1 |= (UINT8)(CXL_ELOG_COMPONENT_EVENT << 2);
    
    SelRecord->OEMEvData2 = (UINT8)GenCxlErrEntry->CxlErrorSection.CxlAgentAddress.Device.BusNum ;
    SelRecord->OEMEvData3 = (UINT8)(GenCxlErrEntry->CxlErrorSection.CxlAgentAddress.Device.DeviceNum << 3 | 
            GenCxlErrEntry->CxlErrorSection.CxlAgentAddress.Device.FunctionNum);
    
    return EFI_SUCCESS;
}

EFI_STATUS
PmicToIpmiBrh (
    IN  UINT8                       Socket,
    IN  UINT8                       Channel,
    IN  UINT8                       Dimm,
    IN OUT UINT8                    *ErrorBuffer,
    IN  GENERIC_PMIC_ERR_ENTRY_V3   *PlatformPmicErrEntry)
{
    EFI_STATUS                  Status;
    IPMI_SEL_EVENT_RECORD_DATA  *SelRecord;

    if (EFI_ERROR (InitErrorLogBuffer((VOID *)PlatformPmicErrEntry, (VOID *)ErrorBuffer))) {
        return  EFI_INVALID_PARAMETER; 
    }

    SelRecord = (IPMI_SEL_EVENT_RECORD_DATA*)ErrorBuffer;
    InitGenericSelInfo (SelRecord);
    
// PORTING PORTING    
    SelRecord->SensorType = SEL_SENSOR_TYPE_MEMORY; //COMPAL_CHANGE SEL_SENSOR_TYPE_OEM; 
    SelRecord->SensorNumber = OEM_CPU0_DIMM_SENSOR; //COMPAL_CHANGE DUMMY_SENSOR_NUMBER;
    
    if (PlatformPmicErrEntry->GenErrorDataEntry.ErrorSeverity == ERROR_SEVERITY_FATAL) {
        SelRecord->OEMEvData1 |= SEL_MEMORY_SENSOR_OFFSET_UNCORRECTABLE_ERROR;  //COMPAL_CHANGE SEL_OEM_SENSOR_OFFSET_UNCORRECTABLE_ERROR;
    } else if (PlatformPmicErrEntry->GenErrorDataEntry.ErrorSeverity == ERROR_SEVERITY_CORRECTED) {
        SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR;
    } else {
        SelRecord->OEMEvData1 |= SEL_OEM_SENSOR_OFFSET_CORRECTABLE_ERROR ;
    }
    // OEMEvData2: BIT5 - Socket, BIT4 - DIMM, BIT[3..0] - Channel
    if(Socket == 1){
        SelRecord->OEMEvData2 |= BIT5; 
    }
    if(Dimm == 1){
        SelRecord->OEMEvData2 |= BIT4; 
    }
    SelRecord->OEMEvData2 |= (UINT8)(Channel & 0xF); 
    SelRecord->OEMEvData3 = (UINT8)OEM_TYPE_PMIC;

//COMPAL_CHANGE >>>
    if(Socket == 1) {   //Socket 1?
        SelRecord->SensorNumber += 0x20;    //OEM_CPU1_DIMM_SENSOR
    }
        
    switch(Channel)                         //Channel/UMC ID
    {
        case 0:  //channel C0
            SelRecord->SensorNumber += 4;
            break;              
        case 1:  //channel E0
            SelRecord->SensorNumber += 8;
            break;
        case 2:  //channel F0
            SelRecord->SensorNumber += 0x0A;
            break;
        case 3:  //channel A0
        default:
            
            break;
        case 4:  //channel B0
            SelRecord->SensorNumber += 2;
            break;
        case 5:  //channel D0
            SelRecord->SensorNumber += 6;
            break;
        case 6:  //channel I0
            SelRecord->SensorNumber += 0x10;
            break;
        case 7:  //channel K0
            SelRecord->SensorNumber += 0x14;
            break;
        case 8:  //channel L0
            SelRecord->SensorNumber += 0x16;
            break;
        case 9:  //channel G0
            SelRecord->SensorNumber += 0xC;
            break;
        case 0xA:  //channel H0
            SelRecord->SensorNumber += 0xE;
            break;
        case 0xB:  //channel J0
            SelRecord->SensorNumber += 0x12;
            break;
    }
    if (Dimm == 1) {  // If DIMM 1 ?
        SelRecord->SensorNumber++;
    }
//COMPAL_CHANGE <<<
    
    Status = EFI_SUCCESS;
// -------------------------------
    return Status;
}
