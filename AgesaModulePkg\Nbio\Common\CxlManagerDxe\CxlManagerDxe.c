/** @file

Cxl Memory Manager Driver.

**/
/******************************************************************************
 * Copyright (C) 2021-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 ***************************************************************************/

#include <AMD.h>
#include <Porting.h>
#include <Library/IdsLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/MemoryAllocationLib.h>
#include <Library/GnbPciAccLib.h>
#include <Library/GnbPciLib.h>
#include <Library/NbioCxlMboxLib.h>
#include <Library/PcdLib.h>
#include <Protocol/AmdApcbProtocol.h>
#include <Protocol/MpService.h>
#include <cpuRegisters.h>
#include "AGESA.h"
#include <GnbDxio.h>
#include <Library/PcieConfigLib.h>
#include <Library/NbioHandleLib.h>
#include <Library/NbioCommonLibDxe.h>
#include "CxlManagerDxe.h"
#include <Library/TimerLib.h>
#include <Library/BaseLib.h>
#include <AmdErrorLog.h>
#include <Protocol/AmdCxlErrorLogProtocol.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define FILECODE NBIO_COMMON_CXLMANAGERDXE_CXLMANAGERDXE_FILECODE

#define CXLMEM_TEST_PATTERN               0xDEADBEEFDEADBEEF
#define TEST_PROGRESS_UNIT                0xFFFFFFFF           //Per 4G
#define MAX_MISMATCH_COUNT                0x100
#define CDAT_DSMAS_NV_MEMORY_FLAG         0x04
#define DVSEC_VID                         0x1E98
#define DVSEC_FOR_CXL                     0
#define DVSEC_VID_OFFSET                  0x04
#define DVSEC_ID_OFFSET                   0x08
#define DVSEC_CAP                         0x23
#define GET_DYNAMIC_CAPACITY_CONFIG       0x4800
#define GET_DYNAMIC_CAPACITY_EXTENT_LIST  0x4801
#define ADD_DYNAMIC_CAPACITY_RESPONSE     0x4802
#define RELEASE_DYNAMIC_CAPACITY          0x4803
#define THIRTY_2_GB                       0x800000000
#define SIXTY_4_GB                        0x1000000000
#define ONE_28_GB                         0x2000000000
#define INVALID_UID                       0xFF
#define PSP_BOOT_MODE_S5_COLD             4

STATIC AMD_CXL_PORT_INFO_STRUCT   mCxlRootBridgeList[MAX_CXL_ROOT_BRIDGES];
STATIC UINTN                      mCxlRootBridgeCount = 0;
STATIC UINT8                      mApobType3EntriesCount = 0;
STATIC UINT8                      mApcbType3EntriesCount = 0;
STATIC UINT8                      mApcbTotalEntries = 0;
STATIC UINT8                      mApobTotalEntries = 0;
STATIC CXL_REGION_DESCRIPTOR      mCxlApcbList[MAX_ABL_MEMORY_RANGES] = { 0 };
STATIC CXL_ADDR_MAP_INFO          mCxlApobList[MAX_ABL_MEMORY_RANGES] = { 0 };
STATIC BOOLEAN                    mTom2UpdateRequired = FALSE;
STATIC UINT64                     mTom2Org = 0;
STATIC UINT64                     mTom2Mod = (UINT64)0xFFFFFFFFFFFFFFFF;
STATIC AMD_CXL_SWITCH_INFO_STRUCT mCxlSwitchList[MAX_CXL_ROOT_BRIDGES];
STATIC UINTN                      mCxlSwitchCount = 0;
AMD_CXL_ERROR_LOG_PROTOCOL        *mAmdCxlError = NULL;

typedef struct {
  UINT8   NumOfAvailableRegions;
  UINT64  Reserved:56;
  VOID    *RegionConfigStructure;
} GET_DCD_CONFIG_OUTPUT;


/*----------------------------------------------------------------------------------------
 *           P R O T O C O L   I N S T A N T I A T I O N
 *----------------------------------------------------------------------------------------
 */
// Consumed Protocols
STATIC AMD_NBIO_CXL_SERVICES_PROTOCOL   *mCxlNbioProtocol = NULL;
STATIC AMD_APCB_SERVICE_PROTOCOL        *mCxlApcbProtocol = NULL;

// Produced Protocols
STATIC AMD_CXL_MANAGER_PROTOCOL  mCxlManagerProtocol = {
  AMD_CXL_MANAGER_PROTOCOL_REVISION,
  CxlMgrIsCxlDevice,
  CxlMgrIsCxlSwitch,
  CxlMgrEnableMemoryPool,
  CxlMgrGetDcRegions,
  CxlMgrGetApobInfo
};


/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */

// CXL memory types
typedef enum {
  CxlConventionalMemory,
  CxlPersistentMemory,
  CxlCache,
  CxlDualMode
} CXL_MEM_TYPE;

// APCB Entry Status
typedef enum {
  NoApcbEntry,
  ValidType3ApcbEntry,
  InvalidType3ApcbEntry
} APCB_ENTRY_STATUS;

GLOBAL_REMOVE_IF_UNREFERENCED CONST CHAR8 *mEfiMemoryTypeName[] = {
  "EfiReservedMemoryType",
  "EfiLoaderCode",
  "EfiLoaderData",
  "EfiBootServicesCode",
  "EfiBootServicesData",
  "EfiRuntimeServicesCode",
  "EfiRuntimeServicesData",
  "EfiConventionalMemory",
  "EfiUnusableMemory",
  "EfiACPIReclaimMemory",
  "EfiACPIMemoryNVS",
  "EfiMemoryMappedIO",
  "EfiMemoryMappedIOPortSpace",
  "EfiPalCode",
  "EfiPersistentMemory",
  "EfiUnacceptedMemoryType",
  "EfiMaxMemoryType"
};

GLOBAL_REMOVE_IF_UNREFERENCED CONST CHAR8 *mGcdMemoryTypeName[] = {
  "EfiGcdMemoryTypeNonExistent",
  "EfiGcdMemoryTypeReserved",
  "EfiGcdMemoryTypeSystemMemory",
  "EfiGcdMemoryTypeMemoryMappedIo",
  "EfiGcdMemoryTypePersistent",
  "EfiGcdMemoryTypePersistent",
  "EfiGcdMemoryTypeMoreReliable",
  "EfiGcdMemoryTypeUndefined",
  "EfiGcdMemoryTypeMaximum"
};

/*----------------------------------------------------------------------------------------*/
/**
 *  @brief CXL endpoint memory active status
 *
 *  @param [in]     Address          Endpoint device address
 *  @param [in out] CxlRangeSizeLo   CXL Range Size Low value
 *
 */
EFI_STATUS
CxlMgrMemRangeActiveStatus (
  IN UINT32                Address,
  IN OUT UINT32            *CxlRangeSizeLo
)
{
  UINT16     MemActiveTimeout = 1;
  UINT8      HwInit;
  UINTN      Index;
  UINT32     FlexBusRangeSizeLo;

  IDS_HDT_CONSOLE (MAIN_FLOW, "%a Entry.....\n", __FUNCTION__);
  //
  // DVSEC Capability Bit 3 Mem_HWInit_Mode bit is set
  // Get Range Size Low bit[13:15] HwInit Memory_Active_Timeout for maximum time that the device
  // is permitted to take to set Memory_Active bit after hot/warm/code reset.
  //
  HwInit = (*CxlRangeSizeLo & 0x0000E000) >> 13;
  if (HwInit <= 4) {
    for(Index = 0; Index < HwInit; Index ++) {
      MemActiveTimeout *= 4;
    }
  }
  IDS_HDT_CONSOLE (GNB_TRACE, "Dvsec Address 0x%x HwInit = 0x%x MemActiveTimeout = %d\n",
          Address, HwInit, MemActiveTimeout);
  do {
    MicroSecondDelay (1000000); // 1s delay
    IDS_HDT_CONSOLE (GNB_TRACE, "*");
    if ((MemActiveTimeout % 60) == 0) {
      IDS_HDT_CONSOLE (GNB_TRACE, "\n");
    }
    // Read FlexBusRangeSizeLo
    GnbLibPciRead (
      Address,
      AccessWidth32,
      &FlexBusRangeSizeLo,
      NULL
      );
    if ((FlexBusRangeSizeLo & 0x00000002) == 0x2) {
      *CxlRangeSizeLo = FlexBusRangeSizeLo;
      IDS_HDT_CONSOLE (GNB_TRACE, "Success! Address = 0x%x FlexBusRangeSizeLo = 0x%x\n", Address, FlexBusRangeSizeLo);
      return EFI_SUCCESS;
    }
  } while (MemActiveTimeout--);
  return EFI_NOT_READY;
}

STATIC
CONST CHAR8 *
CxlMgrGcdMemoryTypeString (
  EFI_GCD_MEMORY_TYPE  GcdMemoryType
  )
{
  if (GcdMemoryType > EfiGcdMemoryTypeMaximum) {
    GcdMemoryType = EfiGcdMemoryTypeMaximum;
  }
  return mGcdMemoryTypeName[(UINTN)GcdMemoryType];
}

STATIC
CONST CHAR8 *
CxlMgrEfiMemoryTypeString (
  EFI_MEMORY_TYPE  EfiMemoryType
  )
{
  if (EfiMemoryType > EfiMaxMemoryType) {
    EfiMemoryType = EfiMaxMemoryType;
  }
  return mEfiMemoryTypeName[(UINTN)EfiMemoryType];
}

STATIC
UINT64
CxlMgrDecodeAlignmentFromPowerOfTwo (
  UINT8  Exponent
  )
{
  // Exponent is expected to be 0..63.
  // E.g., (Exponent = 32) returns SIZE_4GB.
  return 1ULL << (Exponent & 0x3F);
}

STATIC
UINT8
CxlMgrNbioIdToBitmap (
  IN  UINT8  NbioId
  )
{
  return BIT0 << (NbioId & 0x3);
}

/*----------------------------------------------------------------------------------------*/
/**
 *  @brief Get CXL DVSEC.
 *
 *  @param [in] Address        PCI address
 *  @param [in] DvsecId        The DVSEC ID to find
 *  @return                    Register address of desired DVSEC (extended capability pointer)
 *
 */
STATIC
UINT16
CxlGetDvsec (
  IN  UINT32  Address,
  IN  UINT16  DvsecId
  )
{
  UINT32 DvsecValue;
  UINT16 DvsecPtr = GnbLibFindPcieExtendedCapability (Address, DVSEC_CAP, NULL);

  while (DvsecPtr != 0) {
    GnbLibPciRead (
        Address | (DvsecPtr + DVSEC_VID_OFFSET),
        AccessWidth32,
        &DvsecValue,
        NULL
        );

    if ((UINT16) DvsecValue == DVSEC_VID) {
      GnbLibPciRead (
        Address | (DvsecPtr + DVSEC_ID_OFFSET),
        AccessWidth16,
        &DvsecValue,
        NULL
        );

      if ((UINT16) DvsecValue == DvsecId) {
        return DvsecPtr;
      }
    }
    DvsecPtr = GnbLibFindNextPcieExtendedCapability(Address, DvsecPtr, DVSEC_CAP, NULL);
  }

  return 0;
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Gets the PCIe Link Capabilities Port Number
 *
 * @param Address
 * @return The PCIe Link Capabilities Port Number
 */
STATIC
UINT8
CxlGetPortNumber (
  IN  UINT32  Address
)
{
  UINT8   PcieCapPtr;
  UINT32  Value;

  PcieCapPtr = GnbLibFindPciCapability (Address, PCIE_CAP_ID, NULL);
  if (PcieCapPtr == 0) {
    return 0;
  }
  GnbLibPciRead (
    Address | (PcieCapPtr + PCIE_LINK_CAP_REGISTER),
    AccessWidth32,
    &Value,
    NULL
    );

  return ((UINT8) ((Value & 0xFF000000) >> 24));
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Dumps a CXL region descriptor entry in the APCB.
 *
 * @param[in] ApcbRange   A pointer to the CXL region descriptor structure.
 * @return VOID
 */
STATIC
VOID
CxlMgrDumpApcbMemoryRange (
  IN CXL_REGION_DESCRIPTOR  *ApcbRange
  )
{
  UINTN Index;
  IDS_HDT_CONSOLE (GNB_TRACE, "  Size      = 0x%lX\n", ApcbRange->Size);
  IDS_HDT_CONSOLE (GNB_TRACE, "  Alignment = %d (0x%lX)\n",
    ApcbRange->Alignment, CxlMgrDecodeAlignmentFromPowerOfTwo (ApcbRange->Alignment));
  IDS_HDT_CONSOLE (GNB_TRACE, "  Socket    = %d\n", ApcbRange->Socket);
  IDS_HDT_CONSOLE (GNB_TRACE, "  NbioIdMap = 0x%X\n", ApcbRange->PhysNbioMap);
  IDS_HDT_CONSOLE (GNB_TRACE, "  IntlvSize = %d\n", ApcbRange->IntlvSize);
  IDS_HDT_CONSOLE (GNB_TRACE, "  SubIlvMap = 0x%X %X %X %X\n",
    ApcbRange->SubIntlvMap[3], ApcbRange->SubIntlvMap[2], ApcbRange->SubIntlvMap[1], ApcbRange->SubIntlvMap[0]);
  IDS_HDT_CONSOLE (GNB_TRACE, "  SubIlvSize= 0x%X\n", ApcbRange->SubIntlvSize);
  IDS_HDT_CONSOLE (GNB_TRACE, "  Type      = %d\n", ApcbRange->Type);
  IDS_HDT_CONSOLE (GNB_TRACE, "  Segment   = 0x%X\n", ApcbRange->Sbdf.Seg);
  IDS_HDT_CONSOLE (GNB_TRACE, "  Bus       = 0x%X\n", ApcbRange->Sbdf.Bus);

  for(Index = 0; Index < ApcbRange->DsmasCount; Index++) {
    IDS_HDT_CONSOLE (GNB_TRACE, "  DSMAS[%d] Handle = 0x%X\n", Index, ApcbRange->Dsmas[Index].Handle);
    IDS_HDT_CONSOLE (GNB_TRACE, "  DSMAS[%d] Flags = 0x%X\n", Index, ApcbRange->Dsmas[Index].Flags);
    IDS_HDT_CONSOLE (GNB_TRACE, "  DSMAS[%d] DPA Base = 0x%lX\n", Index, ApcbRange->Dsmas[Index].DpaBase);
    IDS_HDT_CONSOLE (GNB_TRACE, "  DSMAS[%d] DPA Length = 0x%lX\n", Index, ApcbRange->Dsmas[Index].DpaLength);
  }

  for(Index = 0; Index < ApcbRange->DsisCount; Index++) {
    IDS_HDT_CONSOLE (GNB_TRACE, "  DSIS[%d] Handle = 0x%X\n", Index, ApcbRange->Dsis[Index].Handle);
    IDS_HDT_CONSOLE (GNB_TRACE, "  DSIS[%d] Flags = 0x%X\n", Index, ApcbRange->Dsis[Index].Flags);
  }
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Dumps a CXL range info entry in the APOB.
 *
 * @param[in] ApobRange   A pointer to the APOB entry structure.
 * @return VOID
 */
STATIC
VOID
CxlMgrDumpApobMemoryRange (
  IN CXL_ADDR_MAP_INFO  *ApobRange
  )
{
  IDS_HDT_CONSOLE (GNB_TRACE, "  Base      = 0x%lX\n", ApobRange->Base);
  IDS_HDT_CONSOLE (GNB_TRACE, "  Size      = 0x%lX\n", ApobRange->Size);
  IDS_HDT_CONSOLE (GNB_TRACE, "  Status    = %d\n", ApobRange->Status);
  IDS_HDT_CONSOLE (GNB_TRACE, "  Socket    = %d\n", ApobRange->Socket);
  IDS_HDT_CONSOLE (GNB_TRACE, "  NbioIdMap = 0x%X\n", ApobRange->PhysNbioMap);
  IDS_HDT_CONSOLE (GNB_TRACE, "  Alignment = %d (0x%lX)\n",
    ApobRange->Alignment, CxlMgrDecodeAlignmentFromPowerOfTwo (ApobRange->Alignment));
  IDS_HDT_CONSOLE (GNB_TRACE, "  IntlvSize = %d\n", ApobRange->IntlvSize);
  IDS_HDT_CONSOLE (GNB_TRACE, "  SubIlvMap = 0x%X %X %X %X\n",
    ApobRange->SubIntlvMap[3], ApobRange->SubIntlvMap[2], ApobRange->SubIntlvMap[1], ApobRange->SubIntlvMap[0]);
  IDS_HDT_CONSOLE (GNB_TRACE, "  SubIlvSize= 0x%X\n", ApobRange->SubIntlvSize);
   IDS_HDT_CONSOLE (GNB_TRACE, "  Type      = %d\n",ApobRange->Type);
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Dumps a CXL memory pool structure.
 *
 * @param[in] ApobRange   A pointer to the APOB entry structure.
 * @return VOID
 */
STATIC
VOID
CxlMgrDumpCxlMemoryPool (
  IN AMD_CXL_MEMORY_POOL  *MemPool
  )
{
  IDS_HDT_CONSOLE (GNB_TRACE, "  Base      = 0x%lX\n", MemPool->Base);
  IDS_HDT_CONSOLE (GNB_TRACE, "  Size      = 0x%lX\n", MemPool->Size);
  IDS_HDT_CONSOLE (GNB_TRACE, "  Alignment = 0x%lX\n", MemPool->Alignment);
  IDS_HDT_CONSOLE (GNB_TRACE, "  EfiType   = 0x%X (%a)\n",
    MemPool->EfiType, CxlMgrEfiMemoryTypeString (MemPool->EfiType));
  IDS_HDT_CONSOLE (GNB_TRACE, "  Flags     = 0x%X\n", MemPool->Flags);
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Compares the APCB to the APOB.
 *
 * @retval TRUE   The APCB matches the APOB.
 * @retval FALSE  The APCB does not match the APOB.
 */
STATIC
BOOLEAN
CxlMgrCompareApobToApcb (
  IN AMD_PCI_LOCATION                 EndpointLocation,
  IN AMD_CXL_MEMORY_POOL              *MemPoolToEnable,
  OUT AMD_CXL_MEMORY_POOL             *ApobMemPool,
  OUT UINTN                           *ApobMemPoolCount
)
{
  UINTN                   ApobIndex;
  UINTN                   ApcbIndex;
  UINTN                   SwitchIndex;
  UINTN                   EndpointIndex;
  BOOLEAN                 ApcbMatchInApob[MAX_ABL_MEMORY_RANGES];
  CXL_ADDR_MAP_INFO       *ApobRange;
  CXL_REGION_DESCRIPTOR   *ApcbRange;
  UINTN                   ApobMemPoolIndex = 0;

  IDS_HDT_CONSOLE (MAIN_FLOW, "%a Entry\n", __FUNCTION__);

  gBS->SetMem(&ApcbMatchInApob, MAX_ABL_MEMORY_RANGES*sizeof(BOOLEAN), FALSE);

  // If the endpoint belongs to a switch, get the switch segment and bus to compare to the APCB entries
  for (SwitchIndex = 0; SwitchIndex < mCxlSwitchCount; SwitchIndex++) {
    for (EndpointIndex = 0; EndpointIndex < mCxlSwitchList[SwitchIndex].EndpointCount; EndpointIndex++) {
      if (EndpointLocation.AsBits.Segment == mCxlSwitchList[SwitchIndex].Endpoints[EndpointIndex].PciAddress.Address.Segment &&
        EndpointLocation.AsBits.Bus == mCxlSwitchList[SwitchIndex].Endpoints[EndpointIndex].PciAddress.Address.Bus) {
        EndpointLocation.AsBits.Bus = mCxlSwitchList[SwitchIndex].SwitchPortInfo.EndPointBDF.Address.Bus;
        EndpointLocation.AsBits.Segment = mCxlSwitchList[SwitchIndex].SwitchPortInfo.EndPointBDF.Address.Segment;
      }
    }
  }

  for (ApobIndex = 0; ApobIndex < mApobTotalEntries; ApobIndex++) {
    ApobRange = &mCxlApobList[ApobIndex];

    for (ApcbIndex = 0; ApcbIndex < mApcbTotalEntries; ApcbIndex++) {
      ApcbRange = &mCxlApcbList[ApcbIndex];
      if ((ApobRange->Status == CXL_ADDR_SUCCESS) && (ApobRange->Base != ABL_INVALID_ADDRESS)) {
        if ((ApobRange->Socket == ApcbRange->Socket) &&
            (ApobRange->Alignment <= ApcbRange->Alignment) &&
            (ApobRange->Size != 0) &&
            (ApobRange->PhysNbioMap & ApcbRange->PhysNbioMap) &&
            (ApobRange->Type == ApcbRange->Type)) {
          if ((PcdGet8(PcdAmdCxlSpaEnable)) &&
                (ApcbRange->Sbdf.Seg == (UINT8) EndpointLocation.AsBits.Segment) &&
                (ApcbRange->Sbdf.Bus == (UINT8) EndpointLocation.AsBits.Bus)) {
            MemPoolToEnable->Base = ApobRange->Base;
            //Info is needed to program the endpoint decoders. It handles the case where a single apcb has multiple apobs (split)
            ApobMemPool[ApobMemPoolIndex].Base = ApobRange->Base;
            ApobMemPool[ApobMemPoolIndex].Size = ApobRange->Size;
            IDS_HDT_CONSOLE (MAIN_FLOW, "ApobMemPool[%d] Size 0x%lX ApobMemPool[%d].Base = 0x%lX\n",
                              ApobIndex, ApobMemPool[ApobIndex].Size, ApobIndex, ApobMemPool[ApobIndex].Base);
            ApobMemPoolIndex++;
            *ApobMemPoolCount = ApobMemPoolIndex;
            IDS_HDT_CONSOLE (MAIN_FLOW, "Use SPA, assign Bus 0x%x MemPoolToEnable->Base = 0x%lX\n",
                              ApcbRange->Sbdf.Bus, MemPoolToEnable->Base);
          }
          if (MemPoolToEnable->EfiType == CxlDualMode) {
            ApobRange->Type = CxlDualMode;
          }
          if (ApobRange->Type == CxlPersistentMemory) {
            MemPoolToEnable->EfiType = EfiPersistentMemory;
          }
          ApcbMatchInApob[ApcbIndex] = TRUE;
          IDS_HDT_CONSOLE (GNB_TRACE, "Found APCB entry %d in APOB entry %d\n", ApcbIndex, ApobIndex);
        }
      } else if ((ApcbRange->Type == CxlCache) &&
                (ApobRange->Type == CxlCache) &&
                (ApobRange->Size == 0) &&
                (ApobRange->PhysNbioMap == ApcbRange->PhysNbioMap)) {
          ApcbMatchInApob[ApcbIndex] = TRUE;
          IDS_HDT_CONSOLE (GNB_TRACE, "Found APCB entry %d in APOB entry %d\n", ApcbIndex, ApobIndex);
      }
    }
  }

  for (ApcbIndex = 0; ApcbIndex < mApcbTotalEntries; ApcbIndex++) {
    if (ApcbMatchInApob[ApcbIndex] == FALSE) {
      IDS_HDT_CONSOLE (GNB_TRACE, "Error: Unable to match APCB to APOB!\n");
      return FALSE;
    }
  }

  IDS_HDT_CONSOLE (MAIN_FLOW, "%a Exit\n", __FUNCTION__);
  return TRUE;
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Finds the Endpoint's Root Bridge
 *
 * @param[in] EndpointLocation    AMD_PCI_LOCATION (SBDF)
 * @retval    TRUE                The endpoint location has a CXL root bridge.
 * @retval    FALSE               The endpoint location does not have a CXL root bridge.
 */
STATIC
BOOLEAN
CxlMgrFindRootBridge (
  IN  AMD_PCI_LOCATION  EndpointLocation
  )
{
  AMD_CXL_PORT_INFO_STRUCT  *RootBridge;
  UINTN                     RootBridgeIndex;
  UINTN                     SwitchIndex;
  UINTN                     EndpointIndex;

  for (RootBridgeIndex = 0; RootBridgeIndex < mCxlRootBridgeCount; ++RootBridgeIndex) {
    RootBridge = &mCxlRootBridgeList[RootBridgeIndex];
    if (EndpointLocation.AsBits.Segment == RootBridge->EndPointBDF.Address.Segment &&
      EndpointLocation.AsBits.Bus == RootBridge->EndPointBDF.Address.Bus) {
      return TRUE;
    }

    if (RootBridge->IsSwitch) {
      for (SwitchIndex = 0; SwitchIndex < mCxlSwitchCount; SwitchIndex++) {
        if (mCxlSwitchList[SwitchIndex].SwitchPortInfo.EndPointBDF.AddressValue == RootBridge->EndPointBDF.AddressValue) {
          for (EndpointIndex = 0; EndpointIndex < mCxlSwitchList[SwitchIndex].EndpointCount; EndpointIndex++) {
            if (EndpointLocation.AsBits.Segment == mCxlSwitchList[SwitchIndex].Endpoints[EndpointIndex].PciAddress.Address.Segment &&
              EndpointLocation.AsBits.Bus == mCxlSwitchList[SwitchIndex].Endpoints[EndpointIndex].PciAddress.Address.Bus) {
              return TRUE;
            } else if (EndpointLocation.AsBits.Bus == mCxlSwitchList[SwitchIndex].Endpoints[EndpointIndex].RootPortAddress.Address.Bus &&
                       EndpointLocation.AsBits.Segment== mCxlSwitchList[SwitchIndex].Endpoints[EndpointIndex].RootPortAddress.Address.Segment){
              return TRUE;
            }
          }
        }
      }
    }
  }
  return FALSE;
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Checks if the PCI endpoint location is a CXL device.
 *
 * @param[in]  This               Pointer to the AMD_CXL_MANAGER_PROTOCOL instance.
 * @param[in]  EndpointLocation   AMD PCI endpoint location structure.
 *
 * @retval EFI_SUCCESS             The PCI endpoint location is a CXL device.
 * @retval Other                   The PCI endpoint location is not a CXL device.
 */
EFI_STATUS
EFIAPI
CxlMgrIsCxlDevice (
  IN  CONST AMD_CXL_MANAGER_PROTOCOL  *This,
  IN  AMD_PCI_LOCATION                EndpointLocation
  )
{
  if (CxlMgrFindRootBridge (EndpointLocation)) {
    IDS_HDT_CONSOLE (GNB_TRACE, "Found CXL device:\n");
    IDS_HDT_CONSOLE (GNB_TRACE, " Segment = %d, Bus = 0x%02X, Device = 0x%02X, Function = 0x%X\n",
      EndpointLocation.AsBits.Segment, EndpointLocation.AsBits.Bus,
      EndpointLocation.AsBits.Device, EndpointLocation.AsBits.Function);
    return EFI_SUCCESS;
  }

  return EFI_NOT_FOUND;
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Checks if the CXL endpoint location is a CXL switch or attached to a CXL switch.
 *
 * @param[in]   This                Pointer to the AMD_CXL_MANAGER_PROTOCOL instance.
 * @param[in]   EndpointLocation    AMD PCI endpoint location structure.
 * @param[out]  IsSwitch            Boolean flag that indicates the endpoint is a switch.
 * @param[out]  EndpointCount       The number of CXL endpoints attached to the switch.
 * @param[out]  EndpointsBDF        The PCI addresses of the CXL endpoints attached to the switch.
 *
 * @retval EFI_SUCCESS              The PCI endpoint location is a CXL switch or attached to a CXL switch.
 * @retval Other                    The PCI endpoint location is neither a CXL switch or attached to a CXL switch.
 */
EFI_STATUS
EFIAPI
CxlMgrIsCxlSwitch (
  IN  CONST AMD_CXL_MANAGER_PROTOCOL  *This,
  IN  AMD_PCI_LOCATION                EndpointLocation,
  OUT BOOLEAN                         *IsSwitch,
  OUT UINTN                           *EndpointCount,
  OUT AMD_CXL_SWITCH_EP_INFO_STRUCT   *Endpoints,
  OUT BOOLEAN                         *IsSwitchInterleaved
  )
{
  AMD_CXL_PORT_INFO_STRUCT  *RootBridge;
  UINTN                     RootBridgeIndex;
  UINTN                     SwitchIndex;
  UINTN                     EndpointIndex;

  *IsSwitch = FALSE;

  for (RootBridgeIndex = 0; RootBridgeIndex < mCxlRootBridgeCount; ++RootBridgeIndex) {
    RootBridge = &mCxlRootBridgeList[RootBridgeIndex];

    if (RootBridge->IsSwitch) {
      if (EndpointLocation.AsBits.Segment == RootBridge->EndPointBDF.Address.Segment &&
          EndpointLocation.AsBits.Bus == RootBridge->EndPointBDF.Address.Bus) {
        *IsSwitch = TRUE;
        *IsSwitchInterleaved = PcdGetBool (PcdAmdCxlSwitchInterleave);
        for (SwitchIndex = 0; SwitchIndex < mCxlSwitchCount; SwitchIndex++) {
          if (mCxlSwitchList[SwitchIndex].SwitchPortInfo.EndPointBDF.AddressValue == RootBridge->EndPointBDF.AddressValue) {
            *EndpointCount = mCxlSwitchList[SwitchIndex].EndpointCount;
            CopyMem (&Endpoints[0], &mCxlSwitchList[SwitchIndex].Endpoints, sizeof (AMD_CXL_SWITCH_EP_INFO_STRUCT)*mCxlSwitchList[SwitchIndex].EndpointCount);
            return EFI_SUCCESS;
          }
        }
      }

      for (SwitchIndex = 0; SwitchIndex < mCxlSwitchCount; SwitchIndex++) {
        if (mCxlSwitchList[SwitchIndex].SwitchPortInfo.EndPointBDF.AddressValue == RootBridge->EndPointBDF.AddressValue) {
          for (EndpointIndex = 0; EndpointIndex < mCxlSwitchList[SwitchIndex].EndpointCount; EndpointIndex++) {
            if (EndpointLocation.AsBits.Segment == mCxlSwitchList[SwitchIndex].Endpoints[EndpointIndex].PciAddress.Address.Segment &&
                EndpointLocation.AsBits.Bus == mCxlSwitchList[SwitchIndex].Endpoints[EndpointIndex].PciAddress.Address.Bus) {
              *EndpointCount = mCxlSwitchList[SwitchIndex].EndpointCount;
              return EFI_SUCCESS;
            }
          }
        }
      }
    }
  }

  return EFI_NOT_FOUND;
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Restores TOM2 across all APs.
 *
 * @param[in] Buffer
 * @return VOID
 */
VOID
EFIAPI
CxlManagerRestoreTom2 (
    IN  VOID *Buffer
  )
{
  AsmWriteMsr64 (MSR_TOM2, mTom2Org);
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Enable a CXL memory pool.
 *
 * @param[in]  This              Pointer to the AMD_CXL_MANAGER_PROTOCOL instance.
 * @param[in]  EndpointLocation  AMD PCI endpoint location structure.
 * @param[in]  MemPoolToEnable   Pointer to CXL memory pool structure to enable.
 * @param[out] ApobMemPool       Pointer to Apob memory pool.
 * @param[out] ApobMemPoolCount  Apob mem pool count.
 *
 * @retval EFI_SUCCESS           The function completed successfully.
 * @retval Other                 The requested operation could not be completed.
**/
EFI_STATUS
EFIAPI
CxlMgrEnableMemoryPool (
  IN CONST AMD_CXL_MANAGER_PROTOCOL   *This,
  IN AMD_PCI_LOCATION                 EndpointLocation,
  IN AMD_CXL_MEMORY_POOL              *MemPoolToEnable,
  OUT AMD_CXL_MEMORY_POOL             *ApobMemPool,
  OUT UINTN                           *ApobMemPoolCount
  )
{
  EFI_STATUS                Status;
  PCI_ADDR                  EndpointAddress;
  EFI_MP_SERVICES_PROTOCOL  *MpServices;
  UINT8                     Index;

  IDS_HDT_CONSOLE (GNB_TRACE, "%a - ENTRY\n", __FUNCTION__);
  Status = EFI_UNSUPPORTED;

  // This check could be done in the CxlMgrUpdateApcb() (if the APCB is current) instead?
  if (!CxlMgrCompareApobToApcb (EndpointLocation, MemPoolToEnable, ApobMemPool, ApobMemPoolCount)) {
    goto ON_EXIT;
  }

  IDS_HDT_CONSOLE (GNB_TRACE, "MemPoolToEnable:\n");
  if(*ApobMemPoolCount > 1) {
    for(Index = 0; Index < *ApobMemPoolCount; Index++)
      CxlMgrDumpCxlMemoryPool (&ApobMemPool[Index]);
  } else {
    CxlMgrDumpCxlMemoryPool (MemPoolToEnable);
  }

  if (MemPoolToEnable->EfiType == EfiPersistentMemory || MemPoolToEnable->EfiType == CxlDualMode) {
    EndpointAddress.AddressValue = MAKE_SBDFO (EndpointLocation.AsBits.Segment,
                                               EndpointLocation.AsBits.Bus,
                                               EndpointLocation.AsBits.Device,
                                               EndpointLocation.AsBits.Function,
                                               0
                                              );
    Status = mCxlNbioProtocol->CxlEnableScmForPersistentMemory (mCxlNbioProtocol, EndpointAddress);
    if (EFI_ERROR (Status)) {
      IDS_HDT_CONSOLE (GNB_TRACE, "Error: Enable SCM for PMEM failed! (Status = %r)\n", Status);
    }
  }

  Status = gBS->LocateProtocol (&gEfiMpServiceProtocolGuid, NULL, (VOID **)&MpServices);
  if (!EFI_ERROR(Status) && mTom2UpdateRequired) {
    IDS_HDT_CONSOLE (GNB_TRACE, "Restore TOM2 from %lx to %lx\n", mTom2Mod, mTom2Org);
    CxlManagerRestoreTom2 (NULL);
    Status = MpServices->StartupAllAPs (
        MpServices,
        CxlManagerRestoreTom2,
        FALSE,
        NULL,
        0,
        NULL,
        NULL
    );
  }

  if (PcdGetBool (PcdAmdCxlDvsecLock)) {
    IDS_HDT_CONSOLE (GNB_TRACE, "CXL DVSEC Lock requested \n");
    MemPoolToEnable->Flags |= AMD_CXL_DVSEC_LOCK;
  }

  if (PcdGetBool (PcdAmdCxlHdmDecoderLockOnCommit)) {
    IDS_HDT_CONSOLE (GNB_TRACE, "CXL HDM Decoder Lock On Commit requested \n");
    MemPoolToEnable->Flags |= AMD_CXL_LOCK_ON_COMMIT;
  }

  ON_EXIT:
  IDS_HDT_CONSOLE (GNB_TRACE, "%a - EXIT (Status = %r)\n", __FUNCTION__, Status);
  return Status;
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Convert a CXL memory pool.
 *
 * @param[in]  Base           Base address of CXL memory pool.
 * @param[in]  Size           Size of CXL memory pool.
 * @param[in]  GcdMemoryType  GCD Memory Type for CXL memory pool.
 * @param[in]  Attributes     Attribbutes for CXL memory pool.
 *
 * @retval EFI_SUCCESS        The function completed successfully.
 * @retval Other              The requested operation could not be completed.
**/
EFI_STATUS
EFIAPI
CxlMgrConvertMemoryPool (
  IN UINT64               Base,
  IN UINT64               Size,
  IN EFI_GCD_MEMORY_TYPE  GcdMemoryType,
  IN UINT64               Attributes
  )
{
  EFI_GCD_MEMORY_SPACE_DESCRIPTOR  GcdDescriptor;
  EFI_STATUS                       Status;

  IDS_HDT_CONSOLE (GNB_TRACE, "%a - ENTRY\n", __FUNCTION__);
  IDS_HDT_CONSOLE (GNB_TRACE, "Memory Pool: 0x%lX - 0x%lX\n", Base, Base + Size - 1);
  IDS_HDT_CONSOLE (GNB_TRACE, "Requested: GcdMemoryType = %d (%a), Attributes = 0x%lX\n",
    GcdMemoryType,
    CxlMgrGcdMemoryTypeString (GcdMemoryType),
    Attributes);

  // Get current GCD parameters
  Status = gDS->GetMemorySpaceDescriptor (Base, &GcdDescriptor);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (GNB_TRACE, "Error: gDS->GetMemorySpaceDescriptor () failed! \n");
    goto ON_EXIT;
  }
  IDS_HDT_CONSOLE (GNB_TRACE, "Current: GcdMemoryType = %d (%a), Attributes = 0x%lX\n",
    GcdDescriptor.GcdMemoryType,
    CxlMgrGcdMemoryTypeString (GcdDescriptor.GcdMemoryType),
    GcdDescriptor.Attributes);

  // Need to convert Type?
  if (GcdMemoryType != GcdDescriptor.GcdMemoryType) {
    // GCD Type convertion is a two-step process:
    //  Step1: Change to EfiGcdMemoryTypeNonExistent via gDS->RemoveMemorySpace()
    //  Step2: Change to desired GcdMemoryType and Attributes via gDS->AddMemorySpace()
    if (GcdDescriptor.GcdMemoryType != EfiGcdMemoryTypeNonExistent) {
      Status = gDS->RemoveMemorySpace (Base, Size);
      if (EFI_ERROR (Status)) {
        IDS_HDT_CONSOLE (GNB_TRACE, "Error: gDS->RemoveMemorySpace () failed! \n");
        goto ON_EXIT;
      }
    }
    if (GcdMemoryType != EfiGcdMemoryTypeNonExistent) {
      Status = gDS->AddMemorySpace (GcdMemoryType, Base, Size, Attributes);
      if (EFI_ERROR (Status)) {
        IDS_HDT_CONSOLE (GNB_TRACE, "Error: gDS->AddMemorySpace () failed! \n");
        goto ON_EXIT;
      }
    }

    // Get converted GCD parameters
    Status = gDS->GetMemorySpaceDescriptor (Base, &GcdDescriptor);
    if (EFI_ERROR (Status)) {
      IDS_HDT_CONSOLE (GNB_TRACE, "Error: gDS->GetMemorySpaceDescriptor () failed! \n");
      goto ON_EXIT;
    }
    IDS_HDT_CONSOLE (GNB_TRACE, "After GcdMemoryType convertion: GcdMemoryType = %d (%a), Attributes = 0x%lX\n",
      GcdDescriptor.GcdMemoryType,
      CxlMgrGcdMemoryTypeString (GcdDescriptor.GcdMemoryType),
      GcdDescriptor.Attributes);
  }

  // Need to change Attributes?
  if (Attributes != GcdDescriptor.Attributes && GcdMemoryType != EfiGcdMemoryTypeNonExistent) {
    Status = gDS->SetMemorySpaceAttributes (Base, Size, Attributes);
    if (EFI_ERROR (Status)) {
      IDS_HDT_CONSOLE (GNB_TRACE, "Error: gDS->SetMemorySpaceAttributes () failed! \n");
      goto ON_EXIT;
    }

    // Get converted GCD parameters
    Status = gDS->GetMemorySpaceDescriptor (Base, &GcdDescriptor);
    if (EFI_ERROR (Status)) {
      IDS_HDT_CONSOLE (GNB_TRACE, "Error: gDS->GetMemorySpaceDescriptor () failed! \n");
      goto ON_EXIT;
    }
    IDS_HDT_CONSOLE (GNB_TRACE, "After Attributes convertion: GcdMemoryType = %d (%a), Attributes = 0x%lX\n",
      GcdDescriptor.GcdMemoryType,
      CxlMgrGcdMemoryTypeString (GcdDescriptor.GcdMemoryType),
      GcdDescriptor.Attributes);
  }

ON_EXIT:
  IDS_HDT_CONSOLE (GNB_TRACE, "%a - EXIT (Status = %r)\n", __FUNCTION__, Status);
  return Status;
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Fill CFMWS Info from the DCD configuration and return the number of entries filled.
 *
 * @param[in] CfmwsInfoArray    A pointer to the CFMWS information array.
 * @return    UINT8             The number of CFMWS entries created.
 */
UINT8
CxlMgrFillCfmWsInfoFromDcd (
  IN  CFMWS_INFO  *CfmwsInfoArray
  )
{
  UINT8             CfmwsEntriesCount;
  UINT8             IntlvTargetListIndex;
  UINT8             Index;
  UINT8             SocketId;
  UINT8             RBIdx;
  EFI_STATUS        Status;
  DC_REGION_CONFIG  DcRegions[MAX_CXL_DC_REGIONS];
  UINT8             DcRegionsCount;
  UINT8             DcRegionsIndex;

  IDS_HDT_CONSOLE (GNB_TRACE, "%a - ENTRY\n", __FUNCTION__);

  CfmwsEntriesCount = 0;
  IntlvTargetListIndex = 0;
  SocketId = 0;
  RBIdx = 0;
  gBS->SetMem(&DcRegions, MAX_CXL_DC_REGIONS * sizeof(DC_REGION_CONFIG), 0);

  for (Index = 0; Index < mCxlRootBridgeCount; Index++) {
    Status = mCxlNbioProtocol->GetCxlPortRBLocation (
                      mCxlNbioProtocol,
                      (UINT8) (mCxlRootBridgeList[Index].CxlPortAddress.Address.Segment),
                      (UINT8) (mCxlRootBridgeList[Index].CxlPortAddress.Address.Bus),
                      &SocketId,
                      &RBIdx);
    if (EFI_ERROR (Status)) {
      IDS_HDT_CONSOLE (GNB_TRACE, "Error: CXL services protocol status %r\n", Status);
    } else {
      Status = CxlMgrGetDcRegions (NULL, mCxlRootBridgeList[Index].EndPointBDF.AddressValue, &DcRegionsCount, DcRegions);
      if (!EFI_ERROR (Status)) {
        for (DcRegionsIndex = 0; DcRegionsIndex < DcRegionsCount; DcRegionsIndex++) {
          CfmwsInfoArray[CfmwsEntriesCount].BaseHpa = DcRegions[DcRegionsIndex].RegionBase;
          CfmwsInfoArray[CfmwsEntriesCount].WindowSize = DcRegions[DcRegionsIndex].RegionLen;
          CfmwsInfoArray[CfmwsEntriesCount].Niw = 1;                                  // TODO: How is this determined? This should match what is in the HDM Decoder.
          CfmwsInfoArray[CfmwsEntriesCount].Hbig = 0;                                 // TODO: This must match what is in the HDM Decoder.
          CfmwsInfoArray[CfmwsEntriesCount].Itl[IntlvTargetListIndex] = RBIdx;        // TODO: Need to determine what is supported? (Multiple DCDs, Interleaved DC regions vs Uninterleaved DC regions)
          CfmwsInfoArray[CfmwsEntriesCount].RecordLength = CEDT_CFMWS_ENTRY_LENGTH + (4*CfmwsInfoArray[CfmwsEntriesCount].Niw);
        }
      }
    }
  }

  IDS_HDT_CONSOLE (GNB_TRACE, "%a - EXIT\n", __FUNCTION__);
  return CfmwsEntriesCount;
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief This function calculates index or the uid of the bridge by using its bus number.
 *
 * @param[in] RootBus    Root Bus
 * @return    UINT8      Index or the UID.
**/
UINT8
GetAcpiUidOfRootPort (
  IN UINT8 RootSegment,
  IN UINT8 RootBus
)
{
    EFI_STATUS            Status;
    GNB_HANDLE            *CountHandle;
    PCIe_PLATFORM_CONFIG  *Pcie;
    UINT8                  RBIdx = 0;
    UINT16                 RootSegBus = 0;
    UINT16                 CurrentSegBus = 0;

    RootSegBus = RootSegment;
    RootSegBus = (RootSegBus << 8) | RootBus;

    Status = PcieGetPcieDxe (&Pcie);
    if(EFI_ERROR (Status)) {
      IDS_HDT_CONSOLE (GNB_TRACE, "Error: PcieGetPcieDxe status %r\n", Status);
      return INVALID_UID;
    }
    CountHandle = NbioGetHandle (Pcie);

    while (CountHandle != NULL) {
        CurrentSegBus = (UINT16) (CountHandle->Address.Address.Segment << 8);
        CurrentSegBus |= CountHandle->Address.Address.Bus;
        if (CurrentSegBus < RootSegBus) {
            RBIdx++;
        }
        CountHandle = GnbGetNextHandle (CountHandle);
    }
    IDS_HDT_CONSOLE (GNB_TRACE, "GetAcpiUidOfRootPort Root Segment %d Bus %x UID is %x\n",  RootSegment, RootBus, RBIdx);
    return RBIdx;
}
/*----------------------------------------------------------------------------------------*/
/**
 * @brief This function gets uid of the bridge by NBIO map.
 *
 * @param[in] Socket       Socket ID
 * @param[in] PhysNbioMap  NBIOS Identifier
 * @return    UINT8        return UID of this host bridge
**/

UINT8
GetInterleaveTargetId (
  IN UINT8 Socket,
  IN UINT8 PhysNbioMap
  )
{
  UINT8        TargetId = 0xFF;
  UINT8        SocketId = 0;
  UINT8        Index;
  EFI_STATUS   Status;

  for (Index = 0; Index < mCxlRootBridgeCount; Index++) {
    if ((PhysNbioMap == CxlMgrNbioIdToBitmap (mCxlRootBridgeList[Index].PhysicalNbioInstance)) &&
        (Socket == mCxlRootBridgeList[Index].SocketID)) {
        // Determine CXL version and update entry accordingly - if UsRCRB is zero then CXL version is 2.0
      if ((mCxlRootBridgeList[Index].UsRcrb == 0) && (mCxlRootBridgeList[Index].CxlVersion > 1)) {
        TargetId = GetAcpiUidOfRootPort((UINT8)(mCxlRootBridgeList[Index].CxlPortAddress.Address.Segment),
                                        (UINT8)(mCxlRootBridgeList[Index].CxlPortAddress.Address.Bus));
        return TargetId;
      } else {
        Status = mCxlNbioProtocol->GetCxlPortRBLocation ( 
                                     mCxlNbioProtocol,
                                     (UINT8) mCxlRootBridgeList[Index].EndPointBDF.Address.Segment,
                                     (UINT8) mCxlRootBridgeList[Index].EndPointBDF.Address.Bus,
                                     &SocketId,
                                     &TargetId);
        if (EFI_ERROR (Status)) {
          IDS_HDT_CONSOLE (GNB_TRACE, "Error: CXL services protocol status %r\n", Status);
        }
        return TargetId;
      }
    }
  }
  return TargetId; 
}
/**
 * @brief This function gets interleave target count for a HPA entry.
 *
 * @param[in] BitsMap      Cross-link or Sublink bits map.
 * @return    UINT8        return interelave target count
**/

UINT8
GetIntlvTargetCount (
  IN UINT8 BitsMap
   )
{
  UINT8 IntlvTargetCnt = 0;
  UINT8 Index;

  for (Index = 0; Index < 4; Index++) {
    if (((BitsMap >> Index) & BIT0) == 1) {
      IntlvTargetCnt++;
    }
  }
  return IntlvTargetCnt;
}
/**
 * @brief This function gets sublink count for a cross-link.
 *
 * @param[in] NbioIndex    Bit index of APOB PhyNbioId field.
 * @return    UINT8        return PhysNbioId bit map value
**/

UINT8
GetPhysNbioId (
   IN UINT8 NbioIndex
   )
{
  switch (NbioIndex) {
    case 0: return 1;
    case 1: return 2;
    case 2: return 4;
    case 3: return 8;
    default: return 0;
  }
}
/*----------------------------------------------------------------------------------------*/
/**
 * @brief Fill CFMWS Info from the APOB list and return the number of entries filled.
 *
 * @param[in] CfmwsInfoArray    A pointer to the CFMWS information array.
 * @return    UINT8             The number of CFMWS entries created.
**/
UINT8
CxlMgrFillCfmWsInfoFromApob (
  IN  CFMWS_INFO  *CfmwsInfoArray
  )
{
  UINT8      CfmwsEntriesCount;
  UINT8      IntlvTargetListIndex;
  UINT8      Index;
  UINT8      Index2;
  UINT32     InterleaveTargetId;
  UINT8      NbioIndex;
  UINT8      SublnkIndex;
  UINT8      SublnkTargetCnt;
  UINT8      IntlvTargetCnt;
  UINT8      PhysNbioId;
  BOOLEAN    Over1TB = FALSE;

  IDS_HDT_CONSOLE (GNB_TRACE, "%a - ENTRY\n", __FUNCTION__);

  CfmwsEntriesCount = 0;
  IntlvTargetListIndex = 0;

  for (Index = 0; Index < mApobTotalEntries; Index++) {
    if ((mCxlApobList[Index].Type == CxlConventionalMemory) && (mCxlApobList[Index].Base == 0x10000000000)) {
      Over1TB = TRUE;
      IDS_HDT_CONSOLE (GNB_TRACE, "Over 1TB configuration\n");
      break;
    }
  }

  // Fill CFMWS
  // Interleave Target list order is based on FSDL interleave order, cross-link first then sublink on 256B Granularity
  // NIW is total number of the interleave target list.
  //
  if (mApobType3EntriesCount == 1) {
    IDS_HDT_CONSOLE (GNB_TRACE, "CXL on Single Socket, Interleave and Sublink Interleave Enabled, all CXL are interleaved to the same HPA.\n");

    CfmwsInfoArray[CfmwsEntriesCount].Niw = mApcbType3EntriesCount;
    CfmwsInfoArray[CfmwsEntriesCount].RecordLength = CEDT_CFMWS_ENTRY_LENGTH + (4*CfmwsInfoArray[CfmwsEntriesCount].Niw);
    CfmwsInfoArray[CfmwsEntriesCount].BaseHpa = mCxlApobList[CfmwsEntriesCount].Base;
    CfmwsInfoArray[CfmwsEntriesCount].WindowSize = mCxlApobList[CfmwsEntriesCount].Size;
    CfmwsInfoArray[CfmwsEntriesCount].Hbig = 0; // CxlApobList[CfmwsEntriesCount].IntlvSize;

    IntlvTargetCnt = GetIntlvTargetCount (mCxlApobList[0].PhysNbioMap);

    for (NbioIndex = 0; NbioIndex < 4; NbioIndex++) {
      if (((mCxlApobList[0].PhysNbioMap >> NbioIndex) & BIT0) != 0) {
        PhysNbioId = GetPhysNbioId (NbioIndex);
        SublnkTargetCnt = GetIntlvTargetCount (mCxlApobList[0].SubIntlvMap[NbioIndex]);
        SublnkIndex = 0;
        for (Index = 0; Index < mApcbTotalEntries; Index++) {
          if ((mCxlApcbList[Index].Type == CxlConventionalMemory) && (mCxlApcbList[Index].Size > 0)) {
            if ((mCxlApcbList[Index].PhysNbioMap == PhysNbioId) &&
                (mCxlApcbList[Index].Socket == mCxlApobList[0].Socket)) {
              InterleaveTargetId = GetInterleaveTargetId (mCxlApcbList[Index].Socket, mCxlApcbList[Index].PhysNbioMap);
              CfmwsInfoArray[CfmwsEntriesCount].Itl[IntlvTargetListIndex+(IntlvTargetCnt*SublnkIndex)] = InterleaveTargetId;
              SublnkIndex++;
            }
            if (SublnkIndex == SublnkTargetCnt) {
              break;
            }
          }
        } // for apcb entries
        IntlvTargetListIndex++;
      } // if
    } // for NbioIndex
    CfmwsEntriesCount++;
  } else if (((mApobType3EntriesCount == mApcbType3EntriesCount) && (!Over1TB)) 
              || (mApobType3EntriesCount == (mApcbType3EntriesCount + 1))) {
    IDS_HDT_CONSOLE (GNB_TRACE, "Interleave and Sublink Interleave Disabled\n");

    for (Index = 0; Index < mApobTotalEntries; Index++) {
      if ((mCxlApobList[Index].Type == CxlConventionalMemory) && (mCxlApobList[Index].Size > 0)) {
        CfmwsInfoArray[CfmwsEntriesCount].Niw = 1;
        CfmwsInfoArray[CfmwsEntriesCount].RecordLength = CEDT_CFMWS_ENTRY_LENGTH + 4;
        CfmwsInfoArray[CfmwsEntriesCount].BaseHpa = mCxlApobList[Index].Base;
        CfmwsInfoArray[CfmwsEntriesCount].WindowSize = mCxlApobList[Index].Size;
        CfmwsInfoArray[CfmwsEntriesCount].Hbig = 0; //ApobCxlInfo->CxlInfo[Index].IntlvSize;
        InterleaveTargetId = GetInterleaveTargetId (mCxlApobList[Index].Socket, mCxlApobList[Index].PhysNbioMap);
        CfmwsInfoArray[CfmwsEntriesCount].Itl[0] = InterleaveTargetId;
        CfmwsEntriesCount++;
      }
    }
  } else {
    // Determine how many APOB entries are interleaved
    IDS_HDT_CONSOLE (GNB_TRACE, "Interleave or Sublink Interleave Enabled, CXL on Multiple Sockets.\n");
    IntlvTargetListIndex = 0;
    CfmwsEntriesCount = 0;
    IntlvTargetListIndex = 0;
    for (Index2 = 0; Index2 < mApobTotalEntries; Index2++) {
      if ((mCxlApobList[Index2].Type == CxlConventionalMemory) && (mCxlApobList[Index2].Size > 0)) {
        IntlvTargetListIndex = 0;
        CfmwsInfoArray[CfmwsEntriesCount].Niw = IntlvTargetListIndex;
        CfmwsInfoArray[CfmwsEntriesCount].RecordLength = CEDT_CFMWS_ENTRY_LENGTH;
        CfmwsInfoArray[CfmwsEntriesCount].BaseHpa = mCxlApobList[Index2].Base;
        CfmwsInfoArray[CfmwsEntriesCount].WindowSize = mCxlApobList[Index2].Size;
        CfmwsInfoArray[CfmwsEntriesCount].Hbig = 0; //ApobCxlInfo->CxlInfo[Index2].IntlvSize;

        IntlvTargetCnt = GetIntlvTargetCount (mCxlApobList[Index2].PhysNbioMap);

        for (NbioIndex = 0; NbioIndex < 4; NbioIndex++) {
          if (((mCxlApobList[Index2].PhysNbioMap >> NbioIndex) & BIT0) != 0) {
            PhysNbioId = GetPhysNbioId (NbioIndex);
            SublnkTargetCnt = GetIntlvTargetCount (mCxlApobList[Index2].SubIntlvMap[NbioIndex]);
            SublnkIndex = 0;
            for (Index = 0; Index < mApcbTotalEntries; Index++) {
              if ((mCxlApcbList[Index].PhysNbioMap == PhysNbioId) &&
                  (mCxlApcbList[Index].Socket == mCxlApobList[Index2].Socket)) {
                InterleaveTargetId = GetInterleaveTargetId (mCxlApcbList[Index].Socket, mCxlApcbList[Index].PhysNbioMap);
                CfmwsInfoArray[CfmwsEntriesCount].Itl[IntlvTargetListIndex+(IntlvTargetCnt*SublnkIndex)] = InterleaveTargetId;
                CfmwsInfoArray[CfmwsEntriesCount].Niw += 1;
                CfmwsInfoArray[CfmwsEntriesCount].RecordLength += sizeof (UINT32);
                SublnkIndex++;
              }
             if (SublnkIndex == SublnkTargetCnt) {
               break;
             }
           } // for Apcb entry
           IntlvTargetListIndex++;
          } //if nbioid match
        } // for NbioIndex
        CfmwsEntriesCount++;
      } // if type 3match
    } // for apob list
  }
  IDS_HDT_CONSOLE (GNB_TRACE, "%a - EXIT\n", __FUNCTION__);
  return CfmwsEntriesCount;
}
/**
  CxlMgrGetApobInfo

  @param[in]  This           Pointer to the AMD_CXL_MANAGER_PROTOCOL instance.
  @param[in]  EntryIndex     Identifies which relevant entry should be returned
  @param[in]  RBIndex        Index of the physical NBIO instance (0-3) to return info for
  @param[in]  SocketId       Socket number of the index of interest
  @param[out] ApobInfo       Pointer to APOB Information structure.

  @retval EFI_SUCCESS     The function completed successfully.
  @retval Other           The requested operation could not be completed.

**/
EFI_STATUS
EFIAPI
CxlMgrGetApobInfo (
  IN CONST AMD_CXL_MANAGER_PROTOCOL  *This,
  IN       UINT8                     EntryIndex,
  IN       UINT8                     RBIndex,
  IN       UINT8                     SocketId,
     OUT   APOB_ENTRY_INFO           *ApobInfo
)
{
  UINT8               Index;
  UINT8               CxlRootBridgeIndex;
  UINT8               MatchCount = 0;
  UINT8               WhichNbio;
  CXL_ADDR_MAP_INFO   *ApobRange;

  IDS_HDT_CONSOLE (GNB_TRACE, "%a - ENTRY\n", __FUNCTION__);

  WhichNbio = 1 << RBIndex;

  for (Index = 0; Index < MAX_ABL_MEMORY_RANGES; Index++) {
    ApobRange = &mCxlApobList[Index];
    if (ApobRange->Size != 0) {
      if (((ApobRange->PhysNbioMap & WhichNbio) != 0) && ( ApobRange->Socket == SocketId)) {
        if (MatchCount == EntryIndex) {
          ApobInfo->Base = ApobRange->Base;
          ApobInfo->Size = ApobRange->Size;
          for (CxlRootBridgeIndex = 0; CxlRootBridgeIndex < mCxlRootBridgeCount; CxlRootBridgeIndex++) {
            if ((mCxlRootBridgeList[CxlRootBridgeIndex].SocketID == ApobRange->Socket) &&
               ((CxlMgrNbioIdToBitmap (mCxlRootBridgeList[CxlRootBridgeIndex].PhysicalNbioInstance) & WhichNbio) != 0)) {
                ApobInfo->TargetPortNum = CxlGetPortNumber (mCxlRootBridgeList[CxlRootBridgeIndex].CxlPortAddress.AddressValue);
                break;
              }
          }
          IDS_HDT_CONSOLE (GNB_TRACE, "%a - Exit success\n", __FUNCTION__);
          return EFI_SUCCESS;
        }
        else {
          MatchCount++;
        }
      }
    }
  }
  IDS_HDT_CONSOLE (GNB_TRACE, "%a - Exit NOT FOUND\n", __FUNCTION__);
  return EFI_NOT_FOUND;
}
/*----------------------------------------------------------------------------------------*/
/**
 * @brief Private function to get CXL entries from the APOB.
 *
 * @return VOID
**/
STATIC
VOID
GetCxlEntriesFromApob (
  VOID
  )
{
  UINT8                             Index;
  APOB_SYSTEM_CXL_INFO_TYPE_STRUCT  *ApobCxlInfo;
  UINTN                             Entries;
  EFI_STATUS                        Status;

  // Get Cxl entries from APOB
  IDS_HDT_CONSOLE (GNB_TRACE, "Reading CXL_ADDR_MAP_INFO entries from APOB: AmdPspGetApobEntryInstance()");
  Status = AmdPspGetApobEntryInstance (
    APOB_FABRIC,
    APOB_SYS_CXL_INFO_TYPE,
    0, FALSE,
    (APOB_TYPE_HEADER **)&ApobCxlInfo
    );
  IDS_HDT_CONSOLE (GNB_TRACE, " - Status = %r\n", Status);
  if (!EFI_ERROR (Status)) {
    Entries = sizeof (ApobCxlInfo->CxlInfo) / sizeof (CXL_ADDR_MAP_INFO);
    mApobTotalEntries = (UINT8) Entries;

    if (Entries > MAX_ABL_MEMORY_RANGES) {
      IDS_HDT_CONSOLE (GNB_TRACE, "Warning: Too many CXL_ADDR_MAP_INFO entries (%d) in APOB! \n", Entries);
    }
    else {
      for (Index = 0; Index < Entries; ++Index) {
        IDS_HDT_CONSOLE (GNB_TRACE, "APOB Range[%d]:\n", Index);
        CopyMem (&mCxlApobList[Index], &ApobCxlInfo->CxlInfo[Index], sizeof (CXL_ADDR_MAP_INFO));
        if (mCxlApobList[Index].Size > 0 &&
           (mCxlApobList[Index].Type == CxlConventionalMemory ||
            mCxlApobList[Index].Type == CxlPersistentMemory)) {
          mApobType3EntriesCount++;
        }
        CxlMgrDumpApobMemoryRange (&mCxlApobList[Index]);
      }
    }
  }
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Private function to get CXL entries from APCB.
 *
 * @return VOID
**/
STATIC
VOID
GetCxlEntriesFromApcb (
  VOID
  )
{
  UINT8       ApcbPurpose;
  UINT8       *ApcbDataBuffer;
  UINT32      ApcbDataSize;
  UINTN       Entries = 0;
  UINT8       Index;
  EFI_STATUS  Status;
  UINT8       CacheEntries = 0;

  IDS_HDT_CONSOLE (GNB_TRACE, "Reading CXL_REGION_DESCRIPTOR entries from APCB: ApcbGetType()");
  ApcbPurpose = APCB_TYPE_PURPOSE_NORMAL;
  ApcbDataBuffer = NULL;
  ApcbDataSize = 0;
  Status = mCxlApcbProtocol->ApcbGetType (
    mCxlApcbProtocol,
    &ApcbPurpose,
    APCB_GROUP_DF,
    APCB_DF_TYPE_CXL_CONFIG,
    0,
    &ApcbDataBuffer,
    &ApcbDataSize
    );
  IDS_HDT_CONSOLE (GNB_TRACE, " - Status = %r\n", Status);
  if (!EFI_ERROR (Status)) {
    Entries = ApcbDataSize / sizeof (CXL_REGION_DESCRIPTOR);
    mApcbTotalEntries = (UINT8) Entries;

    if (Entries == 0 || ApcbDataBuffer == NULL) {
      IDS_HDT_CONSOLE (GNB_TRACE, "Warning: No CXL_REGION_DESCRIPTOR entries found in APCB!\n");
    }
    else if (Entries > MAX_ABL_MEMORY_RANGES) {
      IDS_HDT_CONSOLE (GNB_TRACE, "Warning: Too many CXL_REGION_DESCRIPTOR entries (%d) found in APCB!\n", Entries);
    }
    else {
      for (Index = 0; Index < Entries; ++Index) {
        IDS_HDT_CONSOLE (GNB_TRACE, "APCB Range[%d]:\n", Index);
        CopyMem (&mCxlApcbList[Index], ApcbDataBuffer, sizeof (CXL_REGION_DESCRIPTOR));
        // Find Type 3 entries count
        if (mCxlApcbList[Index].Size != 0 &&
           (mCxlApcbList[Index].Type == CxlConventionalMemory ||
            mCxlApcbList[Index].Type == CxlPersistentMemory)) {
          mApcbType3EntriesCount++;
        } else if (mCxlApcbList[Index].Type == CxlCache) {
          CacheEntries++;
        }
        CxlMgrDumpApcbMemoryRange (&mCxlApcbList[Index]);
        ApcbDataBuffer += sizeof (CXL_REGION_DESCRIPTOR);
      }
      mApcbTotalEntries = mApcbType3EntriesCount + CacheEntries;
    }
  }
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Private function to scan CXL switches.
 *
 * @return VOID
 */
STATIC
VOID
ScanCxlSwitches (
  VOID
)
{
  UINTN             Index;
  UINT32            ScanBus;
  UINT32            DsPortBus;
  PCI_ADDR          DsPort;
  PCI_ADDR          PciDevice;
  UINTN             Device;
  UINTN             Segment;
  PCIE_DEVICE_TYPE  DeviceType;

  for (Index = 0; Index < mCxlRootBridgeCount; Index++) {
    if (mCxlRootBridgeList[Index].IsSwitch) {
      IDS_HDT_CONSOLE (GNB_TRACE, "Found CXL Switch at 0x%08x\n", mCxlRootBridgeList[Index].EndPointBDF.AddressValue);
      mCxlSwitchList[mCxlSwitchCount].EndpointCount = 0;
      mCxlSwitchList[mCxlSwitchCount].SwitchPortInfo = mCxlRootBridgeList[Index];
      GnbLibPciRead (mCxlRootBridgeList[Index].EndPointBDF.AddressValue + PCICFG_SPACE_PRIMARY_BUS_OFFSET, AccessWidth32, &ScanBus, NULL);
      ScanBus = (ScanBus & 0x0000ff00) >> 8;
      Segment = mCxlRootBridgeList[Index].EndPointBDF.Address.Segment;
      for (Device = 0; Device < PCIE_MAX_DEVICES; Device++) {
        DsPort.AddressValue = MAKE_SBDFO (Segment, ScanBus, Device, 0, 0);
        if (GnbLibPciIsDevicePresent (DsPort.AddressValue, NULL)) {
          DeviceType = GnbLibGetPcieDeviceType (DsPort, NULL);
          if (DeviceType == PcieDeviceDownstreamPort) {
            IDS_HDT_CONSOLE (GNB_TRACE, "\tFound switch downstream port at 0x%08x\n", DsPort.AddressValue);
            GnbLibPciRead (DsPort.AddressValue + PCICFG_SPACE_PRIMARY_BUS_OFFSET, AccessWidth32, &DsPortBus, NULL);
            DsPortBus = (DsPortBus & 0x0000ff00) >> 8;
            PciDevice.AddressValue = MAKE_SBDFO (Segment, DsPortBus, 0, 0, 0);
            if (GnbLibPciIsDevicePresent (PciDevice.AddressValue, NULL)) {
              DeviceType = GnbLibGetPcieDeviceType (PciDevice, NULL);
              if ((DeviceType == PcieDeviceEndPoint) || (DeviceType == PCieDeviceRCiEP)) { //TODO: Should check for PCieDeviceRCiEP?
                if (CxlGetDvsec (PciDevice.AddressValue, DVSEC_FOR_CXL)) {
                  IDS_HDT_CONSOLE (GNB_TRACE, "\tFound CXL endpoint at 0x%08x \n", PciDevice.AddressValue);
                  mCxlSwitchList[mCxlSwitchCount].Endpoints[mCxlSwitchList[mCxlSwitchCount].EndpointCount].PciAddress = PciDevice;
                  mCxlSwitchList[mCxlSwitchCount].Endpoints[mCxlSwitchList[mCxlSwitchCount].EndpointCount].PortNumber = CxlGetPortNumber (PciDevice.AddressValue);
                  mCxlSwitchList[mCxlSwitchCount].Endpoints[mCxlSwitchList[mCxlSwitchCount].EndpointCount].RootPortAddress = DsPort;
                  mCxlSwitchList[mCxlSwitchCount].EndpointCount++;
                }
              }
            }
          }
        }
      }
      IDS_HDT_CONSOLE (GNB_TRACE, "\tNumber of attached CXL endpoints = %d\n", mCxlSwitchList[mCxlSwitchCount].EndpointCount);
      mCxlSwitchCount++;
    }
  }
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Private function to scan CXL devices.
 *
 * @return VOID
 */
STATIC
VOID
ScanCxlDevices (
  VOID
  )
{
  UINT8       Index;
  EFI_STATUS  Status;

  Status = EFI_SUCCESS;

  for (Index = 0; Index < MAX_CXL_ROOT_BRIDGES; Index++) {
    mCxlNbioProtocol->CxlFind2p0Devices (
      mCxlNbioProtocol,
      Index
      );
  }

  IDS_HDT_CONSOLE (GNB_TRACE, "Reading CXL device list from AGESA: mCxlNbioProtocol() \n");
  for (Index = 0; Index < MAX_CXL_ROOT_BRIDGES; Index++) {
    Status = mCxlNbioProtocol->CxlGetRootPortInformation (
      mCxlNbioProtocol,
      Index,
      &mCxlRootBridgeList[Index]
      );

    if (Status == EFI_SUCCESS) {
      mCxlRootBridgeCount++;
      IDS_HDT_CONSOLE (GNB_TRACE, "Found CXL device at 0x%x \n", mCxlRootBridgeList[Index].EndPointBDF.AddressValue);
    }
  }
}
/*----------------------------------------------------------------------------------------*/
/**
 * @brief Dumps a DC Region Configuration
 *
 * @param[in] DcRegionConfig    A pointer to the DC region configurations structure.
 * @param[in] NumOfRegions      The number of DC regions to dump.
 * @return VOID
 */
STATIC
VOID
CxlDcRegionsDump (
  IN DC_REGION_CONFIG  *DcRegions,
  IN UINT8             NumOfRegions
)
{
  UINT8 Index;

  for (Index = 0; Index < NumOfRegions; Index++) {
    IDS_HDT_CONSOLE (GNB_TRACE, "<---------- DC Region Configuration [%d]---------->\n", Index);
    IDS_HDT_CONSOLE (GNB_TRACE, "\tRegion Base          = 0x%lx\n", DcRegions[Index].RegionBase);
    IDS_HDT_CONSOLE (GNB_TRACE, "\tRegion Decode Length = 0x%lx\n", DcRegions[Index].RegionDecodeLen);
    IDS_HDT_CONSOLE (GNB_TRACE, "\tRegion Length        = 0x%lx\n", DcRegions[Index].RegionLen);
    IDS_HDT_CONSOLE (GNB_TRACE, "\tRegion Block Size    = 0x%lx\n", DcRegions[Index].RegionBlockSize);
    IDS_HDT_CONSOLE (GNB_TRACE, "\tDSMAD Handle         = 0x%lx\n", DcRegions[Index].DsmadHandle);
    IDS_HDT_CONSOLE (GNB_TRACE, "\tFlags                = 0x%02x\n", DcRegions[Index].Flags);
    IDS_HDT_CONSOLE (GNB_TRACE, "<-------- DC Region Configuration [%d] End -------->\n", Index);
  }
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Check is the CXL device is a Dynamic Capacity Device (DCD)
 *
 * @param[in] CxlPciAddr
 * @return BOOLEAN
 */
STATIC
BOOLEAN
CxlMgrIsDcd (
  UINT32  CxlPciAddr
)
{
  EFI_STATUS              Status;
  GET_LOG_OUTPUT_PAYLOAD  CelLog;
  CEL_ENTRY               CelEntry;
  UINTN                   Index;

  Status = CxlGetLog ((UINT64) CxlPciAddr, COMMAND_EFFECTS_LOG_UUID, &CelLog);

  if (!EFI_ERROR (Status)) {
    if (CelLog.LogData != NULL) {
      if (CelLog.LogSize != 0) {
        for (Index = 0; Index < (CelLog.LogSize/4); Index++) {
          CelEntry.Value = *((UINT32 *) CelLog.LogData + Index);
          if (CelEntry.Field.Opcode == GET_DYNAMIC_CAPACITY_CONFIG) {
            return TRUE;
          }
        }
      }
    }
  }

  return FALSE;
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief
 *
 * @param CxlPciAddr
 * @param DcRegionCount
 * @param DcRegions
 * @return EFI_STATUS
 */
EFI_STATUS
EFIAPI
CxlMgrGetDcRegions (
  IN CONST  AMD_CXL_MANAGER_PROTOCOL  *This,
  IN        UINT32                    CxlPciAddr,
  OUT       UINT8                     *DcRegionCount,
  OUT       DC_REGION_CONFIG          *DcRegions
)
{
  EFI_STATUS             Status;
  MB_CMD_STATUS          MbCmdStatus;
  UINT8                  InputPayload[2];
  UINT32                 OutputPayloadLen;
  GET_DCD_CONFIG_OUTPUT  GetDcdConfigOutput;
  UINT8                  DcRegionConfigOffset;
  UINTN                  Index;

  IDS_HDT_CONSOLE (GNB_TRACE, "%a - ENTRY\n", __FUNCTION__);
  Status = EFI_UNSUPPORTED;
  GetDcdConfigOutput.NumOfAvailableRegions = 0;
  GetDcdConfigOutput.Reserved = 0;
  GetDcdConfigOutput.RegionConfigStructure = AllocatePages (1);

  if (!CxlMgrIsDcd (CxlPciAddr)) {
    goto ON_EXIT;
  }

  if (GetDcdConfigOutput.RegionConfigStructure == NULL) {
    IDS_HDT_CONSOLE (GNB_TRACE, "Output buffer allocation failed!\n");
    goto ON_EXIT;
  }

  InputPayload[0] = 0x08; // Max DC regions (8)
  InputPayload[1] = 0x00; // Starting index 0
  MbCmdStatus = SendCxlPrimaryMbCmd ( (UINT64) CxlPciAddr,
                                      GET_DYNAMIC_CAPACITY_CONFIG,
                                      2,
                                      InputPayload,
                                      &OutputPayloadLen,
                                      (VOID**)&GetDcdConfigOutput
                                    );

  if (MbCmdStatus == Mb_Cmd_Success) {
    if (OutputPayloadLen != 0) {
      *DcRegionCount = GetDcdConfigOutput.NumOfAvailableRegions;
      IDS_HDT_CONSOLE (GNB_TRACE, "DCD Config NumOfAvailableRegions = %d\n", GetDcdConfigOutput.NumOfAvailableRegions);
      DcRegionConfigOffset = 0;
      for (Index = 0; Index < GetDcdConfigOutput.NumOfAvailableRegions; Index++) {
        DcRegions[Index].RegionBase = *((UINT64 *) GetDcdConfigOutput.RegionConfigStructure + DcRegionConfigOffset);
        DcRegions[Index].RegionDecodeLen = *((UINT64 *) GetDcdConfigOutput.RegionConfigStructure + DcRegionConfigOffset + 8);
        DcRegions[Index].RegionLen = *((UINT64 *) GetDcdConfigOutput.RegionConfigStructure + DcRegionConfigOffset + 16);
        DcRegions[Index].RegionBlockSize = *((UINT64 *) GetDcdConfigOutput.RegionConfigStructure + DcRegionConfigOffset + 24);
        DcRegions[Index].DsmadHandle = *((UINT16 *) GetDcdConfigOutput.RegionConfigStructure + DcRegionConfigOffset + 32);
        DcRegions[Index].Flags = *((UINT8 *) GetDcdConfigOutput.RegionConfigStructure + DcRegionConfigOffset + 36);
        DcRegionConfigOffset += 40;
      }
      CxlDcRegionsDump (DcRegions, GetDcdConfigOutput.NumOfAvailableRegions);
      Status = EFI_SUCCESS;
    }
  } else {
    Status = EFI_INVALID_PARAMETER;
    IDS_HDT_CONSOLE (GNB_TRACE, "Get DC Config command failed: 0x%08x\n", MbCmdStatus);
  }

ON_EXIT:
  if (GetDcdConfigOutput.RegionConfigStructure != NULL) {
    FreePages (GetDcdConfigOutput.RegionConfigStructure, 1);
  }
  IDS_HDT_CONSOLE (GNB_TRACE, "%a - EXIT (Status = %r)\n", __FUNCTION__, Status);
  return Status;
}
/*----------------------------------------------------------------------------------------*/
/**
 * @brief
 *
 * @param[in] EndpointAddress
 * @return    BOOLEAN
 */

VOID
SearchApcbEntry (
  IN UINT32 EndpointAddress,
  OUT UINT8 *ApcbEntryStatus
)
{
  UINT8 Index;

  IDS_HDT_CONSOLE (GNB_TRACE, "%a: EndpointAddress = 0x%x\n", __FUNCTION__, EndpointAddress);

  *ApcbEntryStatus = 0xFF;

  for (Index = 0; Index < MAX_ABL_MEMORY_RANGES; ++Index) {
    if ((mCxlApcbList[Index].Sbdf.Seg == (UINT8) (EndpointAddress >> 28)) &&
        (mCxlApcbList[Index].Sbdf.Bus == (UINT8) (EndpointAddress >> 20))) {
      if ((mCxlApcbList[Index].Type == 0) && (mCxlApcbList[Index].Size != 0) &&
          (mCxlApcbList[Index].PhysNbioMap != 0) && (mCxlApcbList[Index].SubIntlvSize != 0)) {
            *ApcbEntryStatus = ValidType3ApcbEntry;  //valid entry
        return;
      } else if (((mCxlApcbList[Index].Type == 0) && (mCxlApcbList[Index].Size == 0)) &&
                 (mCxlApcbList[Index].PhysNbioMap != 0) && (mCxlApcbList[Index].SubIntlvSize != 0)) {
        *ApcbEntryStatus = InvalidType3ApcbEntry;   // invalid entry
        return;
      }
    }
  }
  if (Index == MAX_ABL_MEMORY_RANGES) {
    // first time discovered CXL
    *ApcbEntryStatus = NoApcbEntry;
  }
  return;
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Reads the CXL DVSEC to get the memory ranges and memory type.
 *
 * @param[in]  EndpointAddress    The CXL device PCI address.
 * @param[out] MemPools           The memory pool structure to populate.
 * @param[out] MemPoolCount       The number of memory pools found and populated.
 * @param[out] CxlType            The CXL device type.
 * @return VOID
 */
STATIC
VOID
CxlMgrGetMemoryPools (
  IN  UINT32              EndpointAddress,
  OUT AMD_CXL_MEMORY_POOL *MemPools,
  OUT UINT8               *MemPoolsCount,
  OUT UINT8               *CxlType
)
{
  UINT16                  PcieCapPtr = 0;
  UINT32                  CapValue;
  UINT32                  Range1SizeLo = 0;
  UINT32                  Range1SizeHi = 0;
  UINT32                  Range2SizeLo = 0;
  UINT32                  Range2SizeHi = 0;
  UINT16                  CacheCapableValue;
  UINT16                  CxlCapability;
  UINT8                   HdmCount;
  UINT8                   DeviceType = 0;
  UINT8                   MemPoolCount;
  UINT8                   MemHwInitMode;
  EFI_STATUS              Status;
  UINT32                  ClassCode;
  ERROR_LOG_PARAMS        CxlErrorLog;
  UINT8                   ApcbEntryStatus;


  IDS_HDT_CONSOLE (MAIN_FLOW, "%a Entry\n", __FUNCTION__);
  MemPoolCount = 0;
  DeviceType = 0;
  PcieCapPtr = CxlGetDvsec (EndpointAddress, DVSEC_FOR_CXL);

  if (PcieCapPtr != 0) {
    CacheCapableValue = 0;
    CxlCapability = 0;

    GnbLibPciRead (
      EndpointAddress | (PcieCapPtr + 0x0A),
      AccessWidth16,
      &CacheCapableValue,
      NULL
      );

    switch (CacheCapableValue & 0x0005) {
      case 0x0001: DeviceType = 1; IDS_HDT_CONSOLE (GNB_TRACE, "Found CXL Type 1 device\n"); break; // Cache only
      case 0x0005: DeviceType = 2; IDS_HDT_CONSOLE (GNB_TRACE, "Found CXL Type 2 device\n"); break; // Cache + Mem
      case 0x0004: DeviceType = 3; IDS_HDT_CONSOLE (GNB_TRACE, "Found CXL Type 3 device\n"); break; // Mem only
    }

    if(DeviceType == 1) {
      MemPoolCount++;
      MemPools[0].Size = 0;
      MemPools[0].EfiType = CxlCache;
    }
    else {
      // Find DVSEC range
      GnbLibPciRead (
        EndpointAddress | (PcieCapPtr + 0x0A),
        AccessWidth16,
        &CxlCapability,
        NULL
        );

      HdmCount = (UINT8) ((CxlCapability & 0x30) >> 4);
      MemHwInitMode =  (CxlCapability & 0x8) >> 3;
      IDS_HDT_CONSOLE (GNB_TRACE, "CXL Capability 0x%x HdmCount 0x%x MemHwInitMode = 0x%x\n", CxlCapability, HdmCount, MemHwInitMode);

      ApcbEntryStatus = 0xFF;
      SearchApcbEntry (EndpointAddress, &ApcbEntryStatus);
      IDS_HDT_CONSOLE (MAIN_FLOW, "EndpointAddress 0x%x ApcbEntryStatus = 0x%x\n", EndpointAddress, ApcbEntryStatus);

      if (HdmCount != 0) {
        GnbLibPciRead (
          EndpointAddress | (PcieCapPtr + 0x18),
          AccessWidth32,
          &CapValue,
          NULL
          );

        Range1SizeHi = CapValue;

        GnbLibPciRead (
          EndpointAddress | (PcieCapPtr + 0x1C),
          AccessWidth32,
          &CapValue,
          NULL
          );

        Range1SizeLo = CapValue;
        IDS_HDT_CONSOLE (GNB_TRACE, "CXL Memory Pool 1 Registers: Range1SizeHi 0x%x Range1SizeLo 0x%x\n", Range1SizeHi, Range1SizeLo);

        if ((MemHwInitMode == 1) && ((Range1SizeLo & 0x00000002) == 0)) {
          IDS_HDT_CONSOLE (MAIN_FLOW, "Memory Active is NOT set.\n");
          if (ApcbEntryStatus ==  ValidType3ApcbEntry) {
            // if apcb entry valid and memory not active, wait time for memory active set
            Status = CxlMgrMemRangeActiveStatus (
                EndpointAddress | (PcieCapPtr + 0x1C),
                &CapValue
                );
            if (EFI_ERROR (Status)) {
              IDS_HDT_CONSOLE (GNB_TRACE, " CXL Memory Pool 1 Memory NOT Active! CXL Memory Pool 1 is not added.\n");
              if (mAmdCxlError != NULL) {
                ZeroMem (&CxlErrorLog, sizeof (ERROR_LOG_PARAMS));
                CxlErrorLog.ErrorClass = AMD_ALERT;
                CxlErrorLog.ErrorInfo = CXL_ERROR_LOG_ID | CXL_ENDPOINT_INIT_CONFIG | CXL_MEMORY_NOT_ACTIVE;
                CxlErrorLog.DataParam1 = EndpointAddress >> 28;
                CxlErrorLog.DataParam2 = EndpointAddress >> 20 & 0xFF;
                mAmdCxlError->CxlAddErrorLog (mAmdCxlError, &CxlErrorLog);
              }
            }
            Range1SizeLo = CapValue;
          }
        } // MemHwInitMode == 1
      } // HdmCount != 0
      if (HdmCount == 2) {

        GnbLibPciRead (
          EndpointAddress | (PcieCapPtr + 0x28),
          AccessWidth32,
          &CapValue,
          NULL
          );

        Range2SizeHi = CapValue;

        GnbLibPciRead (
          EndpointAddress | (PcieCapPtr + 0x2C),
          AccessWidth32,
          &CapValue,
          NULL
          );

        Range2SizeLo = CapValue;
        if ((MemHwInitMode == 1) && ((Range2SizeLo & 0x00000002) == 0)) {
          if (ApcbEntryStatus == ValidType3ApcbEntry) {
            // if memory not active wait, skip checking memory active at the initial boot when apcb is not store with data
            Status = CxlMgrMemRangeActiveStatus (
                EndpointAddress | (PcieCapPtr + 0x2C),
                &CapValue
                );
            if (EFI_ERROR (Status)) {
              IDS_HDT_CONSOLE (GNB_TRACE, " CXL Memory Pool 2 Memory NOT Active! CXL Memory Pool 1 is not added.\n");
              if (mAmdCxlError != NULL) {
                ZeroMem (&CxlErrorLog, sizeof (ERROR_LOG_PARAMS));
                CxlErrorLog.ErrorClass = AMD_ALERT;
                CxlErrorLog.ErrorInfo = CXL_ERROR_LOG_ID | CXL_ENDPOINT_INIT_CONFIG | CXL_MEMORY_NOT_ACTIVE;
                CxlErrorLog.DataParam1 = EndpointAddress >> 28;
                CxlErrorLog.DataParam2 = EndpointAddress >> 20 & 0xFF;
                mAmdCxlError->CxlAddErrorLog (mAmdCxlError, &CxlErrorLog);
              }
            }
            Range2SizeLo = CapValue;
          } // MemHwInitMode == 1
        }
        IDS_HDT_CONSOLE (GNB_TRACE, "CXL Memory Pool 2 Registers: Range2SizeHi 0x%x Range2SizeLo 0x%x\n", Range2SizeHi, Range2SizeLo);
      } // HdmCount == 2
      if (((Range1SizeLo & 0x00000001) != 0) && (HdmCount != 0)) {
        if ((Range1SizeHi != 0) || ((Range1SizeLo & 0xF0000000) != 0)) {
          MemPoolCount++;
          MemPools[0].Size = (((UINT64) Range1SizeHi) << 32) + (Range1SizeLo & 0xF0000000);
          /*
            NOTE: The Range Size Low Media Type setting is deprecated for CXL 2.0.
                  The memory characteristics are communicated via CDAT.
                  The APCB Type descriptor will be updated if the media type is NV,
                  when the CDAT is read later in this function.
          */
          if ((Range1SizeLo & 0x00000004) == 0x00000004) {
            MemPools[0].EfiType = CxlPersistentMemory;
          } else if (DeviceType == 3) {
            MemPools[0].EfiType = CxlConventionalMemory;
          } else if (DeviceType == 2) {
            MemPools[0].EfiType = CxlCache;
          }
          if (((Range1SizeLo & 0x00000002) == 0) && (ApcbEntryStatus != NoApcbEntry)) {
            // Handle special case when APOB has memory range defined and memory active bit is not set
            // Clear memory pool
            IDS_HDT_CONSOLE (GNB_TRACE, "CXL Memory Pool 1 NOT Active! Clear Memory Pool Size.\n");
            MemPoolCount--;
            MemPools[0].Size = 0;
          }
          IDS_HDT_CONSOLE (GNB_TRACE, "CXL Memory Pool 1 size 0x%lX\n", MemPools[0].Size);
        }
      }

      if (((Range2SizeLo & 0x00000001) != 0) && (HdmCount == 2)) {
        if ((Range2SizeHi != 0) || ((Range2SizeLo & 0xF0000000) != 0)) {
          MemPoolCount++;
          MemPools[1].Size = (((UINT64) Range2SizeHi) << 32) + (Range2SizeLo & 0xF0000000);
          if ((Range2SizeLo & 0x00000004) == 0x00000004) {
            MemPools[1].EfiType = CxlPersistentMemory;
          } else if (DeviceType == 3) {
            MemPools[1].EfiType = CxlConventionalMemory;
          } else if (DeviceType == 2) {
            MemPools[1].EfiType = CxlCache;
          }
          if (((Range2SizeLo & 0x00000002) == 0) && (ApcbEntryStatus != NoApcbEntry)) {
            // Handle special case when APOB has memory range defined and memory active bit is not set
            // Do not add CXL memory. Clear memory pool
            IDS_HDT_CONSOLE (GNB_TRACE, "CXL Memory Pool 2 NOT Active! Clear Memory Pool Size.\n");
            MemPoolCount--;
            MemPools[1].Size = 0;
          }
          IDS_HDT_CONSOLE (GNB_TRACE, "CXL Memory Pool 2 size 0x%lX\n", MemPools[1].Size);
        }
      }

      // Check if this is a dual mode card by reading the Base Class code
      GnbLibPciRead (
        EndpointAddress + PCICFG_SPACE_REV_ID_OFFSET,
        AccessWidth32,
        &ClassCode,
        NULL
        );
      // If the base class is not a memory controller (5) and there is memory, set the memory type to CXL dual mode
      // NOTE: PCI_CLASS_SCC is also checked because a CXL Type 3 device on a frequently used development system advertises base class 7.
      if ((((ClassCode & 0xFF000000) >> 24) != PCI_CLASS_MEMORY_CONTROLLER) &&
          (((ClassCode & 0xFF000000) >> 24) != PCI_CLASS_SCC)) {
        if (MemPools[0].Size != 0) {
          MemPools[0].EfiType = CxlPersistentMemory;
          IDS_HDT_CONSOLE (GNB_TRACE, "Found CXL Dual Mode device\n");
        }
        if (MemPools[1].Size != 0) {
          MemPools[1].EfiType = CxlPersistentMemory;
        }
      }
    }
  }

  *MemPoolsCount = MemPoolCount;
  *CxlType = DeviceType;
  IDS_HDT_CONSOLE (MAIN_FLOW, "%a Exit\n", __FUNCTION__);
}

/*----------------------------------------------------------------------------------------*/
STATIC
VOID
CxlMgrReadCdatforMediaType (
  IN UINT32  EndpointAddress,
  IN UINT8   *MediaType
)
{
  EFI_STATUS  Status;
  UINTN       CdatIndex;
  CDAT_TABLE  CdatTable;
  DSMAS_CDAT  CdatDsmas[MAX_DSMAS_RECORDS_PER_CXL_DEVICE];

  IDS_HDT_CONSOLE (MAIN_FLOW, "%a Entry\n", __FUNCTION__);

  gBS->SetMem (&CdatTable.Header, sizeof (CDAT_HEADER), 0);
  CdatTable.Entries = NULL;

  Status = CxlGetCdat (EndpointAddress, &CdatTable);

  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (GNB_TRACE, "\tFailure retrieving CDAT (Status = %r)\n", Status);
    goto ON_EXIT;
  }
  gBS->SetMem (&CdatDsmas, MAX_DSMAS_RECORDS_PER_CXL_DEVICE * sizeof (DSMAS_CDAT), 0);
  Status = CxlParseCdat (EndpointAddress, CdatTable, CdatTypeDsmas, (VOID*) &CdatDsmas);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (GNB_TRACE, "\tFailure parsing CDAT (Status = %r)\n", Status);
    goto ON_EXIT;
  }

  for (CdatIndex = 0; (CdatIndex < MAX_DSMAS_RECORDS_PER_CXL_DEVICE); CdatIndex++) {
    if (CdatDsmas[CdatIndex].Length) {
      if ((CdatDsmas[CdatIndex].Flags & CDAT_DSMAS_NV_MEMORY_FLAG) != 0) {
        IDS_HDT_CONSOLE (GNB_TRACE, "\tMemory region is Non-Volatile\n");
        *MediaType = (UINT8) CxlPersistentMemory;
      }
    }
  }

ON_EXIT:
  if (CdatTable.Entries != NULL) {
    FreePool (CdatTable.Entries);
  }

  IDS_HDT_CONSOLE (MAIN_FLOW, "%a EXIT (Status = %r)\n", __FUNCTION__, Status);
}

VOID
CheckBootModeToTriggerWarmReset ()
{
  APOB_BOOT_MODE_INFO_TYPE_STRUCT *ApobBootModeInfo;

  IDS_HDT_CONSOLE (MAIN_FLOW, "%a Entry\n", __FUNCTION__);

  if (AmdPspGetApobEntryInstance (APOB_FABRIC, APOB_BOOT_MODE_INFO_TYPE, 0, FALSE, (APOB_TYPE_HEADER **)&ApobBootModeInfo) == EFI_SUCCESS) {
    IDS_HDT_CONSOLE ( GNB_TRACE, "Read Boot mode from APOB 0x%x:\n", ApobBootModeInfo->BootMode);
    if (ApobBootModeInfo->BootMode == PSP_BOOT_MODE_S5_COLD) {
      if(gRT != NULL) {
          IDS_HDT_CONSOLE (GNB_TRACE, "\tTrigerring Warm Reset since previous boot mode was S5 Cold\n");
          gRT->ResetSystem (EfiResetWarm, EFI_SUCCESS, 0, NULL);
        }
    }
  }
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Private function to update APCB with the CXL memory ranges.
 *
 * @return VOID
*/
STATIC
VOID
CxlMgrUpdateApcb (
)
{
  EFI_STATUS              Status;
  UINT8                   Index;
  UINT8                   Index2;
  CXL_REGION_DESCRIPTOR   CxlToApcbList[MAX_ABL_MEMORY_RANGES];
  UINT8                   MemPoolCount;
  AMD_CXL_MEMORY_POOL     MemPools[MAX_CXL_MEM_POOLS_PER_ENDPOINT];
  AMD_CXL_MEMORY_POOL     HostMemPoolList[MAX_CXL_ROOT_BRIDGES];
  CXL_REGION_DESCRIPTOR   *ApcbRange;
  UINTN                   SubIntlvIndex;
  UINT8                   DeviceType;
  BOOLEAN                 DynamicCapacityDevice = FALSE;
  DC_REGION_CONFIG        DcRegions[MAX_CXL_DC_REGIONS];
  UINT8                   DcRegionsCount;
  UINT8                   DcRegionsIndex;
  UINTN                   EndpointIndex;
  UINTN                   EndpointCount;
  UINT32                  EndpointAddress;
  UINTN                   SwitchIndex;
  UINT8                   PcdValue;
  BOOLEAN                 X8Flag[MAX_SOCKETS_SUPPORTED][MAX_RBS_PER_SOCKET];

  IDS_HDT_CONSOLE (MAIN_FLOW, "%a Entry\n", __FUNCTION__);
  Status = EFI_SUCCESS;
  DeviceType = 0;
  gBS->SetMem(&HostMemPoolList, MAX_CXL_ROOT_BRIDGES*sizeof(AMD_CXL_MEMORY_POOL), 0);
  gBS->SetMem(&CxlToApcbList, MAX_ABL_MEMORY_RANGES * sizeof(CXL_REGION_DESCRIPTOR), 0);
  gBS->SetMem(&DcRegions, MAX_CXL_DC_REGIONS * sizeof(DC_REGION_CONFIG), 0);

  for (Index = 0; Index < mCxlRootBridgeCount; Index++) {
    EndpointCount = 1;
    // If the root bridge is a switch, loop on the endpoint count to get the ranges for every endpoint
    if (mCxlRootBridgeList[Index].IsSwitch) {
      // Get the switch endpoint info
      for (SwitchIndex = 0; SwitchIndex < mCxlSwitchCount; ) {
        if (mCxlRootBridgeList[Index].EndPointBDF.AddressValue == mCxlSwitchList[SwitchIndex].SwitchPortInfo.EndPointBDF.AddressValue) {
          EndpointCount = mCxlSwitchList[SwitchIndex].EndpointCount;
          break;
        }
        SwitchIndex++;
      }
    }

    for (EndpointIndex = 0; EndpointIndex < EndpointCount; EndpointIndex++) {
      gBS->SetMem(&MemPools, MAX_CXL_MEM_POOLS_PER_ENDPOINT * sizeof(AMD_CXL_MEMORY_POOL), 0);
      MemPoolCount = 0;

      if (mCxlRootBridgeList[Index].IsSwitch) {
        EndpointAddress = mCxlSwitchList[SwitchIndex].Endpoints[EndpointIndex].PciAddress.AddressValue;
      } else {
        EndpointAddress = mCxlRootBridgeList[Index].EndPointBDF.AddressValue;
      }

      DcRegionsCount = 0;
      Status = CxlMgrGetDcRegions (NULL, EndpointAddress, &DcRegionsCount, DcRegions);
      if (!EFI_ERROR (Status)) {
        IDS_HDT_CONSOLE (MAIN_FLOW, "Found CXL Dynamic Capacity Device (DCD)\n");
        DynamicCapacityDevice = TRUE;
        // Accumulate DC regions for the MemPool
        for (DcRegionsIndex = 0; DcRegionsIndex < DcRegionsCount; DcRegionsIndex++) {
          if (DcRegions[DcRegionsIndex].RegionLen != 0) {
            MemPools[MemPoolCount].Size = DcRegions[DcRegionsIndex].RegionLen;
            MemPoolCount++;
          }
        }
      } else {
        CxlMgrGetMemoryPools (EndpointAddress,
                              &MemPools[0],
                              &MemPoolCount,
                              &DeviceType
                             );
      }

      HostMemPoolList[Index].Base = 0ULL;
      HostMemPoolList[Index].Flags = 0ULL;

      for (Index2 = 0; Index2 < MemPoolCount; Index2++) {
        if ((DeviceType == 2) || (DeviceType == 3) || (MemPools[Index2].EfiType == CxlPersistentMemory)) {
          HostMemPoolList[Index].Size += MemPools[Index2].Size;
          //Override CXL Memory Size if option is set in CBS
          if(PcdGet8(PcdTruncateCxlMemory) != 0xFF) {
            PcdValue = PcdGet8(PcdTruncateCxlMemory);
            switch(PcdValue) {
              case 0:  //32GB
                  HostMemPoolList[Index].Size = THIRTY_2_GB;
                  break;
              case 1:  //64GB
                  HostMemPoolList[Index].Size = SIXTY_4_GB;
                  break;
              case 2:  //128GB
                  HostMemPoolList[Index].Size = ONE_28_GB;
            }
          }
          HostMemPoolList[Index].Alignment = MemPools[Index2].Alignment;
          HostMemPoolList[Index].EfiType = MemPools[Index2].EfiType;
        } else if ((MemPools[Index2].EfiType == CxlCache) && (DeviceType == 1)) {
          HostMemPoolList[Index].Size = 0;
          HostMemPoolList[Index].Alignment = 0;
          HostMemPoolList[Index].EfiType = CxlCache;
        } else if (DynamicCapacityDevice) {
          HostMemPoolList[Index].Size += MemPools[Index2].Size;
          HostMemPoolList[Index].Alignment = 0x10000000;
          HostMemPoolList[Index].EfiType = CxlConventionalMemory;
        }
      }

      // Make sure Alignment is compliant with ABL requirements
      if (HostMemPoolList[Index].Alignment < 0x10000000) {
        HostMemPoolList[Index].Alignment = 0x10000000;
      }

      IDS_HDT_CONSOLE ( GNB_TRACE, "Host Memory Pool for device at 0x%x:\n", mCxlRootBridgeList[Index].EndPointBDF.AddressValue);
      CxlMgrDumpCxlMemoryPool (&HostMemPoolList[Index]);
    }
  }
  ZeroMem (&X8Flag, sizeof (X8Flag));
  for (Index = 0; Index < mCxlRootBridgeCount; Index++) {
    if (mCxlRootBridgeList[Index].PortWidth == 8) {
      X8Flag[mCxlRootBridgeList[Index].SocketID][mCxlRootBridgeList[Index].PhysicalNbioInstance] = TRUE;
    }
  }
  for (Index = 0; Index < mCxlRootBridgeCount; Index++) {
    // Configure APCB descriptor
    ApcbRange = &CxlToApcbList[Index];
    ApcbRange->Size = HostMemPoolList[Index].Size;
    ApcbRange->Socket = mCxlRootBridgeList[Index].SocketID;
    ApcbRange->PhysNbioMap = CxlMgrNbioIdToBitmap(mCxlRootBridgeList[Index].PhysicalNbioInstance);
    ApcbRange->Type = (UINT8) HostMemPoolList[Index].EfiType;
      for (SubIntlvIndex  = 0; SubIntlvIndex < 4; SubIntlvIndex++) {
        if (mCxlRootBridgeList[Index].PortWidth == 16) {
          ApcbRange->SubIntlvMap[SubIntlvIndex] = 1;
        } else if (mCxlRootBridgeList[Index].PortWidth == 8) {
          if (mCxlRootBridgeList[Index].PortId == 0) {
            if (SubIntlvIndex < 2) {
              ApcbRange->SubIntlvMap[SubIntlvIndex] = 1;
            }
          } else {
            if (SubIntlvIndex > 1) {
              ApcbRange->SubIntlvMap[SubIntlvIndex] = 1;
            }
          }
        } else if (mCxlRootBridgeList[Index].PortId == SubIntlvIndex) {
          if(X8Flag[mCxlRootBridgeList[Index].SocketID][mCxlRootBridgeList[Index].PhysicalNbioInstance]) {
              ApcbRange->SubIntlvMap[SubIntlvIndex + 1] = 1;
          } else {
              ApcbRange->SubIntlvMap[SubIntlvIndex] = 1;
          }
        }
      }
    if(ApcbRange->Type != CxlCache) { // not type 1
      ApcbRange->Alignment = CXL_MIN_POWER_OF_TWO_ALIGNMENT;
      ApcbRange->IntlvSize = (UINT8) DF_MEM_INTLV_SIZE_AUTO;
      ApcbRange->SubIntlvSize = (UINT8) DF_MEM_INTLV_SIZE_AUTO;
    }
    ApcbRange->Sbdf.Seg = (UINT8) mCxlRootBridgeList[Index].EndPointBDF.Address.Segment;
    ApcbRange->Sbdf.Bus = (UINT8) mCxlRootBridgeList[Index].EndPointBDF.Address.Bus;
    ApcbRange->Sbdf.Dev = (UINT8) mCxlRootBridgeList[Index].EndPointBDF.Address.Device;
    ApcbRange->Sbdf.Func = (UINT8) mCxlRootBridgeList[Index].EndPointBDF.Address.Function;
    CxlMgrReadCdatforMediaType (mCxlRootBridgeList[Index].EndPointBDF.AddressValue, &ApcbRange->Type);
    IDS_HDT_CONSOLE (GNB_TRACE, "APCB Entry[%d]:\n", Index);
    CxlMgrDumpApcbMemoryRange (ApcbRange);
  }

  // Compare Current to Previous APCB entries
  if (CompareMem (CxlToApcbList, mCxlApcbList, sizeof (CxlToApcbList)) != 0) {
    IDS_HDT_CONSOLE (GNB_TRACE, "Updating current APCB descriptors: ApcbSetType() ");
    Status = mCxlApcbProtocol->ApcbSetType (
      mCxlApcbProtocol,
      APCB_TYPE_PURPOSE_NORMAL,
      APCB_GROUP_DF,
      APCB_DF_TYPE_CXL_CONFIG,
      0,
      (UINT8 *)&CxlToApcbList[0],
      sizeof (CxlToApcbList)
      );
    IDS_HDT_CONSOLE (GNB_TRACE, "(Status = %r)\n", Status);

    if (!EFI_ERROR (Status)) {
      // Flush APCB data to SPI ROM
      IDS_HDT_CONSOLE (GNB_TRACE, "Flushing APCB descriptors to SPI ROM: ApcbFlushData() ");
      Status = mCxlApcbProtocol->ApcbFlushData (mCxlApcbProtocol);
      IDS_HDT_CONSOLE (GNB_TRACE, "(Status = %r)\n", Status);

      if (!EFI_ERROR (Status)) {
        // Trigger Warm-reset
        IDS_HDT_CONSOLE (GNB_TRACE, "Triggering Warm-Reset to map APCB descriptors!\n");
        if(gRT != NULL) {
          gRT->ResetSystem (EfiResetWarm, EFI_SUCCESS, 0, NULL);
        }
      }
    }
  } else {
    Status = EFI_SUCCESS;
    IDS_HDT_CONSOLE (GNB_TRACE, "APCB descriptors are up-to-date\n");
    if(PcdGet8(PcdAmdCXlEarlyLinkTraining)) {
      CheckBootModeToTriggerWarmReset();
    }
  }

  IDS_HDT_CONSOLE (GNB_TRACE, "%a - EXIT (Status = %r)\n", __FUNCTION__, Status);
  return;
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Fills the CXL memory with data for memory validation test
 *
 * @param CxlMemStart
 * @param CxlMemSize
 * @param AddressSpecific
 * @return VOID
 */
VOID
FillCxlMem (
  IN      UINT64    *CxlMemStart,
  IN      UINT64    CxlMemSize,
  IN      BOOLEAN   AddressSpecific
  )
{
  UINT64    *MemPointer;
  UINT64    MemDataValue;
  UINT64    EndPointer;

  IDS_HDT_CONSOLE (GNB_TRACE, "%a - Enter\n", __FUNCTION__);

  MemPointer = CxlMemStart;
  MemDataValue = CXLMEM_TEST_PATTERN;
  EndPointer = CxlMemSize + (UINT64) CxlMemStart;

  *MemPointer = MemDataValue;

  do {
    if (AddressSpecific) {
      MemDataValue = CXLMEM_TEST_PATTERN | (UINT64) MemPointer;
    }
    *MemPointer = MemDataValue;
    MemPointer++;
    if (0 == ((UINT64) MemPointer & TEST_PROGRESS_UNIT)) {
      IDS_HDT_CONSOLE (GNB_TRACE, "* ");
    }
  } while ((UINT64) MemPointer < EndPointer);
  IDS_HDT_CONSOLE (GNB_TRACE, "\n%a - EXIT\n", __FUNCTION__);
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Checks if the memory is valid from the test writes
 *
 * @param CxlMemStart
 * @param CxlMemSize
 * @param AddressSpecific
 * @return VOID
 */
VOID
CheckCxlMem (
  IN      UINT64    *CxlMemStart,
  IN      UINT64    CxlMemSize,
  IN      BOOLEAN   AddressSpecific
  )
{
  UINT64    *MemPointer;
  UINT64    MemDataValue;
  UINT64    EndPointer;
  UINT64    MismatchCount;

  IDS_HDT_CONSOLE (GNB_TRACE, "%a - Enter\n", __FUNCTION__);

  MemPointer = CxlMemStart;
  MemDataValue = CXLMEM_TEST_PATTERN;
  EndPointer = CxlMemSize + (UINT64) CxlMemStart;
  MismatchCount = 0;

  do {
    if (AddressSpecific) {
      MemDataValue = CXLMEM_TEST_PATTERN | (UINT64) MemPointer;
    }
    if (*MemPointer != MemDataValue) {
      IDS_HDT_CONSOLE (GNB_TRACE, "Memory Miscompare at 0x%lX\n", (UINT64) MemPointer);
      IDS_HDT_CONSOLE (GNB_TRACE, "  Expected 0x%lX  Found 0x%lX\n", MemDataValue, *MemPointer);
      if (MismatchCount >=  MAX_MISMATCH_COUNT) {
        IDS_HDT_CONSOLE (GNB_TRACE, "Stop diagnostic. The data error has reached the upper limit!!\n");
        break;
      }
      MismatchCount++;
    }
    MemPointer++;
    if (0 == ((UINT64) MemPointer & TEST_PROGRESS_UNIT)) {
      IDS_HDT_CONSOLE (GNB_TRACE, "* ");
    }
  } while ((UINT64) MemPointer < EndPointer);

  IDS_HDT_CONSOLE (GNB_TRACE, "\n%a - EXIT\n", __FUNCTION__);
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Performs CXL memory validation test.
 *
 * @return VOID
 */
VOID
CxlMemoryTest (
  VOID
)
{
  UINTN Index;

  IDS_HDT_CONSOLE (GNB_TRACE, "%a - Start\n", __FUNCTION__);

  for (Index = 0; Index < MAX_ABL_MEMORY_RANGES; Index++) {
    if (mCxlApobList[Index].Size == 0) {
      continue;
    }
    IDS_HDT_CONSOLE (GNB_TRACE, "\nTesting APOB Range[%d]:\n", Index);
    CxlMgrDumpApobMemoryRange (&mCxlApobList[Index]);
    FillCxlMem ((UINT64 *) mCxlApobList[Index].Base, mCxlApobList[Index].Size, FALSE);
    CheckCxlMem ((UINT64 *) mCxlApobList[Index].Base, mCxlApobList[Index].Size, FALSE);
  }

  IDS_HDT_CONSOLE (GNB_TRACE, "%a - Exit\n", __FUNCTION__);
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Notification function of EVT_GROUP_READY_TO_BOOT event group.
 *
 * @param[in] Event        Event whose notification function is being invoked.
 * @param[in] Context      Pointer to the notification function's context.
 * @return    VOID
**/
VOID
EFIAPI
CxlManagerOnReadyToBoot (
  IN  EFI_EVENT  Event,
  IN  VOID       *Context
  )
{
  EFI_STATUS            Status;
  UINTN                 Index;
  CXL_ADDR_MAP_INFO     *ApobRange;
  EFI_GCD_MEMORY_TYPE   GcdMemoryType;
  UINT64                Attributes;
  ERROR_LOG_PARAMS      CxlErrorLog;

  // Ensure we do not get more notifications
  gBS->CloseEvent (Event);

  IDS_HDT_CONSOLE (GNB_TRACE, "%a - ENTRY\n", __FUNCTION__);

  // Convert the Memory Pool(s) from reserved to usable (or SPM)
  for (Index = 0; Index < MAX_ABL_MEMORY_RANGES; Index++) {
    ApobRange = &mCxlApobList[Index];
    if (ApobRange->Size != 0) {
      if (ApobRange->Type == (UINT8) CxlPersistentMemory) {
        GcdMemoryType = EfiGcdMemoryTypePersistent;
        Attributes = (EFI_MEMORY_NV | EFI_MEMORY_SP);
      } else if (ApobRange->Type == (UINT8) CxlDualMode) {
        GcdMemoryType = EfiGcdMemoryTypePersistent;
        Attributes = EFI_MEMORY_SP;
      } else if((ApobRange->Type == (UINT8) CxlConventionalMemory) || (ApobRange->Type == (UINT8) CxlCache)){
        GcdMemoryType = EfiGcdMemoryTypeSystemMemory;
        Attributes = (EFI_MEMORY_WB | EFI_MEMORY_WT | EFI_MEMORY_WC | EFI_MEMORY_UC | (PcdGetBool (PcdAmdCxlSpm)?EFI_MEMORY_SP:0));
      } else {
        GcdMemoryType = EfiGcdMemoryTypeReserved;
        Attributes = 0ULL;
      }
      // Convert memory pool via Cxl Manager
      Status = CxlMgrConvertMemoryPool (ApobRange->Base, ApobRange->Size, GcdMemoryType, Attributes);
      if (EFI_ERROR (Status)) {
        IDS_HDT_CONSOLE (GNB_TRACE, "Failed to convert memory pool at 0x%lX!\n", ApobRange->Base);
        if (mAmdCxlError != NULL) {
          ZeroMem (&CxlErrorLog, sizeof (ERROR_LOG_PARAMS));
          CxlErrorLog.ErrorClass = AMD_ALERT;
          CxlErrorLog.ErrorInfo = CXL_ERROR_LOG_ID | CXL_ENDPOINT_INIT_CONFIG | CXL_CONVERT_MEMORY_TYPE_ERROR;
          mAmdCxlError->CxlAddErrorLog (mAmdCxlError, &CxlErrorLog);
        }
      }
    }
  }

  // CXL memory validation test
  if (PcdGetBool (PcdCxlMemValidation)) {
    CxlMemoryTest ();
  }

  IDS_HDT_CONSOLE (GNB_TRACE, "%a - EXIT\n", __FUNCTION__);
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Updates TOM2 across all APs.
 *
 * @param Buffer
 * @return VOID
 */
VOID
EFIAPI
CxlManagerUpdateTom2 (
    IN  VOID *Buffer
  )
{
  AsmWriteMsr64 (MSR_TOM2, mTom2Mod);
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief Runs necessary routines across all APs
 *
 * @param[in] Event    Event whose notification function is being invoked.
 * @param[in] Context  Unused pointer to the notification function's context.
 * @return VOID
 */
VOID
EFIAPI
CxlManagerOnMpServices (
  IN  EFI_EVENT Event,
  IN  VOID      *Context
  )
{
  EFI_STATUS                Status;
  EFI_MP_SERVICES_PROTOCOL  *MpServices;
  CXL_ADDR_MAP_INFO         *ApobRange;
  UINTN                     Index;

  IDS_HDT_CONSOLE (GNB_TRACE, "%a - ENTRY\n", __FUNCTION__);
  Status = gBS->LocateProtocol (&gEfiMpServiceProtocolGuid, NULL, (VOID **)&MpServices);
  if (!EFI_ERROR(Status)) {
    mTom2Org = AsmReadMsr64 (MSR_TOM2);
    for (Index = 0; Index < MAX_ABL_MEMORY_RANGES; Index++) {
      ApobRange = &mCxlApobList[Index];
      if (ApobRange->Size != 0) {
        mTom2UpdateRequired = TRUE;
        if (mTom2Mod > ApobRange->Base) {
          mTom2Mod = ApobRange->Base;
        }
      }
    }
    if (mTom2UpdateRequired) {
      IDS_HDT_CONSOLE (GNB_TRACE, "Update TOM2 from %lx to %lx\n", mTom2Org, mTom2Mod);
      CxlManagerUpdateTom2 (NULL);
      Status = MpServices->StartupAllAPs (
          MpServices,
          CxlManagerUpdateTom2,
          FALSE,
          NULL,
          0,
          NULL,
          NULL
      );
    }
  }

  IDS_HDT_CONSOLE (GNB_TRACE, "%a - EXIT\n", __FUNCTION__);
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief The Cxl Manager entry point.
 *
 * @param[in]  ImageHandle    The firmware allocated handle for the EFI image.
 * @param[in]  SystemTable    A pointer to the EFI System Table.
 *
 * @retval EFI_SUCCESS        The entry point is executed successfully.
 * @retval other              Some error occurs when executing this entry point.
**/
EFI_STATUS
EFIAPI
CxlManagerEntryPoint (
  IN EFI_HANDLE         ImageHandle,
  IN EFI_SYSTEM_TABLE  *SystemTable
  )
{
  EFI_HANDLE                Handle;
  EFI_STATUS                Status;
  VOID                      *Registration;
  EFI_EVENT                 ReadyToBootEvent;
  EFI_EVENT                 CxlManagerOnMpServicesEvent;
  VOID                      *RegistrationForCxlManagerOnMpServicesEvent;
  EFI_MP_SERVICES_PROTOCOL  *MpServices;

  IDS_HDT_CONSOLE (GNB_TRACE, "%a - ENTRY\n", __FUNCTION__);

  // Locate CxlNbio protocol
  Status = gBS->LocateProtocol (
    &gAmdNbioCxlServicesProtocolGuid,
    NULL,
    (VOID **)&mCxlNbioProtocol
    );
  ASSERT_EFI_ERROR (Status);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (GNB_TRACE, "Error: gBS->LocateProtocol (gAmdNbioCxlServicesProtocolGuid) failed! \n");
    goto ON_EXIT;
  }

  // Locate CxlApcb protocol
  Status = gBS->LocateProtocol (
    &gAmdApcbDxeServiceProtocolGuid,
    NULL,
    (VOID **)&mCxlApcbProtocol
    );
  ASSERT_EFI_ERROR (Status);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (GNB_TRACE, "Error: gBS->LocateProtocol(gAmdApcbDxeServiceProtocolGuid) failed! \n");
    goto ON_EXIT;
  }

  // Get Cxl entries from APCB
  GetCxlEntriesFromApcb();

  // Get Cxl entries from APOB
  GetCxlEntriesFromApob();

  // Install CxlManager Protocol
  Handle = NULL;
  Status = gBS->InstallProtocolInterface (
                  &Handle,
                  &gAmdCxlManagerProtocolGuid,
                  EFI_NATIVE_INTERFACE,
                  &mCxlManagerProtocol
                  );
  ASSERT_EFI_ERROR (Status);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (GNB_TRACE, "Error: gBS->InstallProtocolInterface (gAmdCxlManagerProtocolGuid) failed! \n");
  } else {
    EfiCreateProtocolNotifyEvent (&gEfiPciIoProtocolGuid, TPL_NOTIFY, CxlMgrCallbackAfterPciIo, NULL, &Registration);
    // Configure Event for ReadyToBoot notification
    Status = gBS->CreateEventEx (
      EVT_NOTIFY_SIGNAL,
      TPL_CALLBACK,
      CxlManagerOnReadyToBoot,
      NULL,
      &gEfiEventReadyToBootGuid,
      &ReadyToBootEvent
      );
    if (EFI_ERROR (Status)) {
      IDS_HDT_CONSOLE (GNB_TRACE, "Error: gBS->CreateEventEx (gEfiEventReadyToBootGuid) failed! \n");
    }
    EfiCreateProtocolNotifyEvent (&gEfiPciEnumerationCompleteProtocolGuid, TPL_CALLBACK, CxlMgrCallbackAcpiTableProtocol, NULL, &Registration);
    Status = gBS->LocateProtocol (&gAmdCxlErrorLogProtocolGuid,
                                  NULL,
                                  (VOID **)&mAmdCxlError
                                  );
    if (EFI_ERROR (Status)) {
      IDS_HDT_CONSOLE (GNB_TRACE, "Locate AMD CXL Error Log Protocol failed! \n");
    }

    Status = gBS->LocateProtocol (&gEfiMpServiceProtocolGuid, NULL, (VOID **)&MpServices);
    if (!EFI_ERROR (Status)) {
      CxlManagerOnMpServices (NULL, NULL);
    } else {
      Status = gBS->CreateEventEx (
                               EVT_NOTIFY_SIGNAL,
                               TPL_NOTIFY,
                               CxlManagerOnMpServices,
                               NULL,
                               NULL,
                               &CxlManagerOnMpServicesEvent
                               );

      Status = gBS->RegisterProtocolNotify (
                              &gEfiMpServiceProtocolGuid,
                              CxlManagerOnMpServicesEvent,
                              &RegistrationForCxlManagerOnMpServicesEvent
                              );
    }
    gBS->SetMem(&mCxlRootBridgeList, MAX_CXL_ROOT_BRIDGES*sizeof(AMD_CXL_PORT_INFO_STRUCT), 0);
    gBS->SetMem(&mCxlSwitchList, MAX_CXL_ROOT_BRIDGES*sizeof(AMD_CXL_SWITCH_INFO_STRUCT), 0);
  }

ON_EXIT:
  IDS_HDT_CONSOLE (GNB_TRACE, "%a - EXIT (Status = %r)\n", __FUNCTION__, Status);
  return Status;
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief PCI IO protocol notification event handler.
 *
 * @param[in] Event     Event whose notification function is being invoked.
 * @param[in] Context   Pointer to the notification function's context, which is
 *                      always zero in current implementation.
 * @return VOID
 */
VOID
EFIAPI
CxlMgrCallbackAfterPciIo (
  IN  EFI_EVENT  Event,
  IN  VOID       *Context
  )
{
  EFI_STATUS            Status;
  EFI_PCI_IO_PROTOCOL   *PciIoProtocol;

  //
  // Add more check to locate protocol after got event, because
  // the library will signal this event immediately once it is register
  // just in case it is already installed.
  //
  Status = gBS->LocateProtocol (&gEfiPciIoProtocolGuid, NULL, (VOID **)&PciIoProtocol);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "%a First notify\n", __FUNCTION__);
    return;
  }
  IDS_HDT_CONSOLE (MAIN_FLOW, "%a Entry\n", __FUNCTION__);

  // Scan for CXL devices
  ScanCxlDevices ();
  ScanCxlSwitches ();

  // Send the CXL info to MPIO
  Status = mCxlNbioProtocol->CxlReportToMpio (mCxlNbioProtocol, &mCxlSwitchList[0]);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (GNB_TRACE, "Error: CxlReportToMpio failed: Status = %r\n", Status);
  }

  // Update APCB entries
  CxlMgrUpdateApcb ();

  gBS->CloseEvent (Event);
  IDS_HDT_CONSOLE (MAIN_FLOW, "%a Exit\n", __FUNCTION__);
  return;
}

/*----------------------------------------------------------------------------------------*/
/**
 * @brief ACPI Table protocol notification event handler.
 *
 * @param[in] Event     Event whose notification function is being invoked.
 * @param[in] Context   Pointer to the notification function's context, which is
 *                      always zero in current implementation.
 *
 * @return VOID
 */
VOID
EFIAPI
CxlMgrCallbackAcpiTableProtocol (
  IN  EFI_EVENT  Event,
  IN  VOID       *Context
  )
{
  EFI_STATUS                Status;
  EFI_ACPI_TABLE_PROTOCOL   *AcpiTableProtocol;

  //
  // Add more check to locate protocol after got event, because
  // the library will signal this event immediately once it is register
  // just in case it is already installed.
  //
  Status = gBS->LocateProtocol (&gEfiPciEnumerationCompleteProtocolGuid, NULL, (VOID **)&AcpiTableProtocol);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "%a First notify\n", __FUNCTION__);
    return;
  }
  IDS_HDT_CONSOLE (MAIN_FLOW, "%a Entry\n", __FUNCTION__);

  CxlSetCedt ();

  gBS->CloseEvent (Event);
  IDS_HDT_CONSOLE (MAIN_FLOW, "%a Exit\n", __FUNCTION__);
  return;
}

