/*****************************************************************************
 *
 * Copyright (C) 2016-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */
#include "AmdNbioDxe.h"
#include <Filecode.h>

#define FILECODE NBIO_BRH_DXE_PMMTABLEINIT_FILECODE


/**
  Populate PPTable with values from PCDs


  @param PPTable Pointer to a PPTable which must be pre allocated.
**/
VOID
PopulatePPTable (
  IN    UINT32                     PackageType,
  IN    PPTable_t                  *PPTable
  )
{
  ZeroMem (PPTable, sizeof (PPTable_t));

  // Populate Pcd value to Pptable.

  //DEFAULT INFRASTRUCTURE LIMITS
  PPTable->TDP = PcdGet32 (PcdAmdcTDP);
  PPTable->PPT = PcdGet32(PcdCfgPPT);
  PPTable->TDC = PcdGet32(PcdCfgTDC);

  //PLATFORM INFRASTRUCTURE LIMITS
  PPTable->TDP_PlatformLimit = PcdGet32 (PcdCfgPlatformTDP);
  PPTable->PPT_PlatformLimit = PcdGet32 (PcdCfgPlatformPPT);
  PPTable->TDC_PlatformLimit = PcdGet32 (PcdCfgPlatformTDC);
  PPTable->EDC_PlatformLimit = PcdGet32 (PcdCfgPlatformEDC);

  //Determinism Control
  if (PcdGet8 (PcdAmdDeterminismMode) == 0) {
    PPTable->DeterminismControl = 0;
  } else {
    // Manual Mode-Power=0, Performance=1
    if (PcdGetBool (PcdAmdDeterminismControl) == 0) {
      PPTable->DeterminismControl = 1;
    } else {
      PPTable->DeterminismControl = 2;
    }
  }

  //xGMI Pstate Control-if manual set pstate support enable-1,else 0
  if (PcdGet8 (PcdXgmiPstateControl) == 1) {
    PPTable->XgmiPstateRangeSupportEn = 1;
    PPTable->XgmiPstateRangeMin = PcdGet8 (PcdXgmiPstateSelection);
    PPTable->XgmiPstateRangeMax = PcdGet8 (PcdXgmiPstateSelection);
  }
  else{
    PPTable->XgmiPstateRangeSupportEn = 0;
  }

  //XGMI CONFIG
  PPTable->xGMIForceLinkWidthEn = PcdGet8 (PcdxGMIForceLinkWidthEn);
  PPTable->xGMIForceLinkWidth = PcdGet8 (PcdxGMIForceLinkWidth);
  PPTable->xGMIMaxLinkWidthEn = PcdGet8 (PcdxGMIMaxLinkWidthEn);
  PPTable->xGMIMaxLinkWidth = PcdGet8 (PcdxGMIMaxLinkWidth);
  PPTable->xGMIMinLinkWidth = PcdGet8 (PcdxGMIMinLinkWidth);

  //APBDIS
  PPTable->APBDIS = PcdGet8(PcdCfgApbDis);
  PPTable->APBDIS_DfPstate = PcdGet8(PcdCfgFixedSocPstate);

  //Power Profile Selection
  PPTable->Policy = PcdGet8 (PcdPowerProfileSelect);

  //DF PState Frequency Optimizer
  PPTable->DFFO_Disable = PcdGet8 (PcdDFFODisable);

  //SVI3 SVC Speed
  PPTable->Svi3SvcSpeed = PcdGet8 (PcdAmdSvi3SvcSpeed);

  //I3C Parameters
  PPTable->I3cSdaHold[0] = PcdGet8 (PcdAmdFchI3c0SdaHold);
  PPTable->I3cSdaHold[1] = PcdGet8 (PcdAmdFchI3c1SdaHold);
  PPTable->I3cSdaHold[2] = PcdGet8 (PcdAmdFchI3c2SdaHold);
  PPTable->I3cSdaHold[3] = PcdGet8 (PcdAmdFchI3c3SdaHold);
  PPTable->I3cPpHcnt     = PcdGet8 (PcdFchI3cPPHcnt);
  PPTable->I3cSpeed      = PcdGet8 (PcdFchI3cSpeed);

  //DRAM PPR setting
  PPTable->PprConfigInitiator = PcdGet8 (PcdAmdMemPostPackageRepairConfigInitiator);

  //DfPstate Range Support
  PPTable->DfPstateRangeSupportEn = PcdGet8 (PcdDfPstateRangeSupportEn);
  PPTable->DfPstateRangeMax = PcdGet8 (PcdDfPstateRangeMax);
  PPTable->DfPstateRangeMin = PcdGet8 (PcdDfPstateRangeMin);

  //Throttler Mode
  PPTable->ThrottlerMode = PcdGet8 (PcdThrottlerMode) == 0xF? 0: PcdGet8 (PcdThrottlerMode);
}

