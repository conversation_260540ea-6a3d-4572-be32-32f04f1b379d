TOKEN
	Name  = "PSP_BLOCK_START_OFFSET"
	Value  = "$(NVRAM_OFFSET)+$(NVRAM_SIZE)"
	Help  = "The offset of PSP data."
	TokenType = Integer
	TargetEQU = Yes
	TargetMAK = Yes
	TargetH = Yes
	Token = "FAULT_TOLERANT_NVRAM_UPDATE" "=" "0"
End

TOKEN
	Name  = "PSP_BLOCK_START_OFFSET"
	Value  = "$(NVRAM_OFFSET)+$(NVRAM_SIZE)+$(NVRAM_SIZE)"
	Help  = "The offset of PSP data."
	TokenType = Integer
	TargetEQU = Yes
	TargetMAK = Yes
	TargetH = Yes
	Token = "FAULT_TOLERANT_NVRAM_UPDATE" "=" "1"
End

TOKEN
	Name  = "PSP_BLOCK_START_BASE"
	Value  = "$(PSP_BLOCK_START_OFFSET)+$(FLASH_BASE)"
	Help  = "The base of PSP data."
	TokenType = Integer
	TargetEQU = Yes
	TargetMAK = Yes
	TargetH = Yes
End

TOKEN
	Name  = "TOTAL_PSP_DATA_SIZE"
	Value  = "$(PSP_DATA_SIZE_BRH)"
	Help  = "The total size of PSP firmware."
	TokenType = Integer
	TargetEQU = Yes
	TargetMAK = Yes
	TargetH = Yes
End

ELINK
	Name  = "PSP_MAK"
	InvokeOrder = ReplaceParent
End

ELINK
    Name  = "0, PSPDIR_HEADER_OFFSET_BRH, 0, 0, 0, SPI_BRH_EFS, BIOSDIR_L1_OFFSET_BRH, 0, 0, 0, 0, 0, 0, SPI_MODE, SPI_SPEED, 0, ESPI_BRH_EFS"
#    Name  = "0, PSPDIR_HEADER_OFFSET_BRH, 0, 0, 0, SPI_BRH_EFS, BIOSDIR_L1_OFFSET_BRH"
    Help = "Start with offset 0x10. 0ffset 0x14:PSP DIR for GN, 0x20:BIOS DIR for GN, 0x24:EFS 2nd Gen"
	Parent  = "AmdFirmwareDirectoryList"
    InvokeOrder = AfterParent
End

TOKEN
    Name  = "SPI_2ND_GEN_EFS"
    Value  = "0xFFFFFFFE"
    Help  = "Flag to indicate ROMSIG for MTS onward, offset 0x24[0]. 1 - 1st gen, 0 - 2nd gen"
    TokenType = Integer
    TargetEQU = Yes
    TargetMAK = Yes
    TargetH = Yes
End

TOKEN
    Name  = "SPI_BRH_EFS"
    Value  = "0xFFFFFFE3"
    Help  = "Flag to indicate ROMSIG for BRH, offset 0x24[4:0]=00011b"
    TokenType = Integer
    TargetEQU = Yes
    TargetMAK = Yes
    TargetH = Yes
End

TOKEN
    Name  = "SPI_MODE"
    Value  = "0xFFFFFFFF"
    Help  = "Byte3 (EFS offset 0x47) for SPI Mode"
    TokenType = Integer
    TargetEQU = Yes
    TargetMAK = Yes
    TargetH = Yes
End

TOKEN
    Name  = "SPI_SPEED"
    Value  = "0xFFFFFFFF"
    Help  = "Byte0 (EFS offset 0x48) for SPI Speed, Byte1 (EFS offset 0x49) for MicroDetectFlag, Byte2 (EFS offset 0x4A) for MGX"
    TokenType = Integer
    TargetEQU = Yes
    TargetMAK = Yes
    TargetH = Yes
End

TOKEN
    Name  = "ESPI_BRH_EFS"
    Value  = "0xFFFFFF0E"
    Help  = "Byte0 (EFS offset 0x50) for eSPI0 configuration, Byte1 (EFS offset 0x51) for eSPI1 configuration"
    TokenType = Integer
    TargetEQU = Yes
    TargetMAK = Yes
    TargetH = Yes
End
