/*****************************************************************************
 * Copyright (C) 2008-2025 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/

#ifndef _AMD_PSP_DXE_SMM_BUF_LIB_H_
#define _AMD_PSP_DXE_SMM_BUF_LIB_H_

#include "Porting.h"
// To ensure the security, an communication buffer should be allocated in the SMRAM.
// #SMM.3.5: Boot firmware SMM module MUST copy the communication buffer to SMRAM before the check,
// to resist TOC/TOU or DMA attacks.
// Below are the size of SMARM buffer communication buffer copy to
#define AMD_PSP_SMM_TMP_BUFFER_SIZE    (8 * 1024)

/**
 * @brief Get Amd Psp Smm/Run Time Buffer Address
 *
 * @param   VOID
 *
 * @return  UINT8* Pointer
 */
UINT8 *
GetAmdPspSmmRunTimeBufferAddress (
  VOID
  );

#endif //_AMD_PSP_DXE_SMM_BUF_LIB_H_

