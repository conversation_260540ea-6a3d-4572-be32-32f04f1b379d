#***********************************************************************
#*                                                                     *
#*   Copyright (c) 1985-2025, American Megatrends International LLC.   *
#*                                                                     *
#*      All rights reserved. Subject to AMI licensing agreement.       *
#*                                                                     *
#***********************************************************************

## @file
#   PEI instance of the NbPciDxeInitLib library class
##

[Defines]
  INF_VERSION           = 0x00010005
  VERSION_STRING        = 1.0
  BASE_NAME             = NbPciDxeInitLib
  FILE_GUID             = B3513A69-497E-4833-BDE6-A9763D0EB92E
  MODULE_TYPE           = DXE_DRIVER
  LIBRARY_CLASS         = NULL
  CONSTRUCTOR           = RegisterPciPlatformProtocol

[Sources]
  NbPciDxeInitLib.c

[LibraryClasses]
  UefiDriverEntryPoint
  AmiSdlLib
  AmiPciBusLib
  PciAccessCspLib
  PciLib
  PciSegmentLib
  AmiPciBusSetupOverrideLib
  AmdSocBaseLib
  BaseMemoryLib
  MemoryAllocationLib
  AmiPciExpressLib

[Protocols]
  gEfiDevicePathProtocolGuid
  gEfiSimpleTextOutProtocolGuid
  gEfiGraphicsOutputProtocolGuid
  gEfiUgaDrawProtocolGuid
  gEfiHiiFontProtocolGuid
  gEfiHiiDatabaseProtocolGuid
  gEfiDriverBindingProtocolGuid
  gAmiBoardPciInitProtocolGuid
  gAmdFabricResourceManagerServicesProtocolGuid
  gPciNumaNodeMappingProtocolGuid
  gEfiPciPlatformProtocolGuid             #PRODUCED
  gAmdNbioCxlServicesProtocolGuid

[Guids]
  gDisableResourcesVgaListGuid
  gLastBootFailedGuid
  gFabricRootBridgeOrderInfoHobGuid

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  AmiCompatibilityPkg/AmiCompatibilityPkg.dec
  AmiModulePkg/AmiModulePkg.dec
  AmiChipsetModulePkg/AmiChipsetModulePkg.dec
  AgesaPkg/AgesaPkg.dec
  TurinSoCPkg/TurinSoCPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec

[Pcd]
  gAmiChipsetModulePkgTokenSpaceGuid.PcdLegacyVgaBDF

