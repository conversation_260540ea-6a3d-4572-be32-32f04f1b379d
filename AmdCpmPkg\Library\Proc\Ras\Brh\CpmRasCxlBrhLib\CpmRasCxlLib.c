/*****************************************************************************
 *
 * Copyright (C) 2021-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Library/BaseMemoryLib.h>
#include <Library/IoLib.h>
#include <Library/BaseLib.h>
#include "AmdRas.h"
#include "Library/CpmCxlMboxLib.h"
#include "Library/CpmRasPciLib.h"
#include "Library/CpmRasCxlLib.h"
#include <Library/PciSegmentLib.h>
#include <Library/IdsLib.h>
#include <Library/FabricRegisterAccLib.h>
#include <FabricRegistersBrh.h>
#include "AmdRasRegistersBrh.h"
#include "Library/CpmRasLib.h"
#include <AmdSoc.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define DF_CFGBASEADDRESS                       ((0<<12)|(0xC80))
#define DF_CFGLIMITADDRESS                      ((0<<12)|(0xC84))
#define DF_FABRICBLOCKINSTANCEINFORMATION3_IOS  ((FABRICBLOCKINSTANCEINFORMATION3_IOS_FUNC << 12) | (FABRICBLOCKINSTANCEINFORMATION3_IOS_REG))
#define BRH_IOS0_INSTANCE_ID                    (0x28)
// FWDEV-61901: Clear the WA for Turin C0 to set CfgBase.Field.SegmentNum as 0x80 for local segment(s)
#define BRH_GET_SEGMENT_NUMBER(CfgBase)         ((CfgBase.Field.SegmentNum) & (~BIT7))

//Banaras IOD has 8 RCEC
//Numbered RCEC             SMN ApertureID   | Unnumbered RCEC.         SMN ApertureID
//  RCEC0 @ NBIO0 IOHUB0    19Fh             |   RCEC @ NBIO0 IOHUB1    1E4h
//  RCEC1 @ NBIO0 IOHUB2    1A0h             |   RCEC @ NBIO0 IOHUB3    1E5h
//  RCEC2 @ NBIO1 IOHUB0    1A1h             |   RCEC @ NBIO1 IOHUB1    1E6h
//  RCEC3 @ NBIO1 IOHUB2    1A2h             |   RCEC @ NBIO1 IOHUB3    1E7h
#define NBIO0_IOHUB0_RCEC_PCIE_RCECEPA_ASSOCI_NEXTBUS  0x19F00161  //IOAPIC::RCEC::PCIE_RCECEPA_ASSOCI_NEXTBUS
#define NBIO0_IOHUB0_RCEC_PCIE_RCECEPA_ASSOCI_LASTBUS  0x19F00162  //IOAPIC::RCEC::PCIE_RCECEPA_ASSOCI_LASTBUS
#define NBIO0_IOHUB1_RCEC_PCIE_RCECEPA_ASSOCI_NEXTBUS  0x1E400161  //IOAPIC::RCEC::PCIE_RCECEPA_ASSOCI_NEXTBUS
#define NBIO0_IOHUB1_RCEC_PCIE_RCECEPA_ASSOCI_LASTBUS  0x1E400162  //IOAPIC::RCEC::PCIE_RCECEPA_ASSOCI_LASTBUS
#define UPRCRB_OFFSET                      (4 * 1024)

//DF::CnliCxlUncorrErrStat0
//A0 Stepping
//#define SMN_CNLI0_UNCORRECTABLE_ERROR_STATUS                 0x1F100054

//C0 stepping
#define SMN_CNLI0_UNCORRECTABLE_ERROR_STATUS                 0x1F100060

// Convert DF IOS Index to NBIO RbIndex
//    IOS 0 (FabricId: 0x20) => NBIO RbIndex 0
//    IOS 1 (FabricId: 0x21) => NBIO RbIndex 5
//    IOS 2 (FabricId: 0x22) => NBIO RbIndex 1
//    IOS 3 (FabricId: 0x23) => NBIO RbIndex 4
//    IOS 4 (FabricId: 0x24) => NBIO RbIndex 2
//    IOS 5 (FabricId: 0x25) => NBIO RbIndex 7
//    IOS 6 (FabricId: 0x26) => NBIO RbIndex 3
//    IOS 7 (FabricId: 0x27) => NBIO RbIndex 6
#define RASCXLLIB_IOS_TO_RBINDEX(ios)  (UINT8)(((ios & BIT0) << 2) | ((ios & BIT2) >> 1) | ((ios & BIT0) ? (~((ios & BIT1) >> 1) & BIT0) : ((ios & BIT1) >> 1)))

// Convert NBIO RbIndex to DF IOS Index
//    NBIO RbIndex 0 => IOS 0 (FabricId: 0x20)
//    NBIO RbIndex 1 => IOS 2 (FabricId: 0x22)
//    NBIO RbIndex 2 => IOS 4 (FabricId: 0x24)
//    NBIO RbIndex 3 => IOS 6 (FabricId: 0x26)
//    NBIO RbIndex 4 => IOS 3 (FabricId: 0x23)
//    NBIO RbIndex 5 => IOS 1 (FabricId: 0x21)
//    NBIO RbIndex 6 => IOS 7 (FabricId: 0x27)
//    NBIO RbIndex 7 => IOS 5 (FabricId: 0x25)
#define RASCXLLIB_NBIO_RB_INDEX_TO_IOS(RbIndex)  (UINT8)(((RbIndex & 0x4) >> 2) | ((RbIndex & 0x4) ? ((~(RbIndex & 0x1) & 0x1) << 1) : ((RbIndex & 0x1) << 1)) | ((RbIndex & 0x2) << 1))

// Convert DF IOS Index to NBIO number
#define RASCXLLIB_IOS_TO_NBIO(ios)  (ios >> 2) & BIT0

// Convert DF IOS Index to IOHC number in an NBIO
#define RASCXLLIB_IOS_TO_IOHC(ios)  (ios & BIT0) ? ((ios & (BIT1|BIT0)) ^ BIT1) : (ios & (BIT1|BIT0))

// Convert DF IOS Index to PCIe core number in an NBIO
#define RASCXLLIB_IOS_TO_PCIE(ios)  (((RASCXLLIB_IOS_TO_IOHC(ios) & BIT0) << 1) | (((RASCXLLIB_IOS_TO_IOHC(ios) & BIT1) >> 1)))

// Convert DF IOS Index to CNLI number in a Processor
#define RASCXLLIB_IOS_TO_CNLI(ios)  (((ios & BIT2) >> 1) | (ios & BIT0))

// All DXIO P Links support CXL:
//   CNLI0 P0 @IOHUBS0 (FabricId: 0x20) => NBIO0 IOHC0 PCIE0
//   CNLI1 P1 @IOHUBS1 (FabricId: 0x21) => NBIO0 IOHC3 PCIE3
//   CNLI2 P2 @IOHUBS4 (FabricId: 0x24) => NBIO1 IOHC0 PCIE0
//   CNLI3 P3 @IOHUBS5 (FabricId: 0x25) => NBIO1 IOHC3 PCIE3
#define RASCXLLIB_CXL_CAPABLE_RB(IOHUBS) ((IOHUBS & 0x2) == 0)

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
VOID
CxlGetDeviceId (
  IN       UINT32         CxlDevice,
     OUT   CXL_DEVICE_ID  *CxlDeviceId
 );

VOID
CxlGetCompDeviceId (
  IN       UINT32               CxlDevice,
     OUT   CXL_COMP_DEVICE_ID   *CxlCompDeviceId
);

BOOLEAN
RcrbAerUnCorrErrCheck(
  IN       UINT64        RcrbAddress,
  IN       UINT16        AerCapPtr
  );

BOOLEAN
RcrbAerCorrErrCheck(
  IN       UINT64        RcrbAddress,
  IN       UINT16        AerCapPtr
  );

BOOLEAN
RcrbAerErrCheck(
  IN       UINT64        RcrbAddress,
  IN       UINT16        AerCapPtr
  );

BOOLEAN
CxlRasUnCorrErrCheck(
  IN       UINT64        CxlCacheMemRegisterAddr,
  IN       UINT16        CxlRasCapPtr
  );


BOOLEAN
CxlRasCorrErrCheck(
  IN       UINT64        CxlCacheMemRegisterAddr,
  IN       UINT16        CxlRasCapPtr
  );


BOOLEAN
CxlRasCapErrCheck(
  IN       UINT64        CxlCacheMemRegisterAddr,
  IN       UINT16        CxlRasCapPtr
  );


UINT64
RasGetFullRegisterBlockAddress (
  IN       UINT32       RciepAddress,
  IN       UINT16       RegisterBlockPtr
  );


UINT16
RasGetRegisterBlockNumber (
  IN       UINT32       RciepAddress,
  IN       UINT16       RegisterLocatorDvsecPtr
  );


VOID
CxlDevInfoFill(
  IN       UINT32             CxlDeviceAddr,
  IN       CXL_ERROR_LOG_DATA *CxlErrorLogData
  );

VOID
GenerateErrorSectionGuid (
     OUT   EFI_GUID  *DestinationGuid,
  IN       EFI_GUID  *SourceGuid
  );

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                          T A B L E    D E F I N I T I O N S
 *----------------------------------------------------------------------------------------
 */
extern UINT32 BrhIomsInstanceIds[];



/*--------------------------------------------------------------------------------------*/

PCI_ADDR
CxlCxlAgentAddrToPciAddr (
  IN       CXL_AGENT_ADDRESS         *CxlAgentAddress
)
{
  PCI_ADDR            CxlDevice;

  CxlDevice.Address.Segment = (UINT32)CxlAgentAddress->Device.SegmentNum;
  CxlDevice.Address.Bus = (UINT32)CxlAgentAddress->Device.BusNum;
  CxlDevice.Address.Device = (UINT32)CxlAgentAddress->Device.DeviceNum;
  CxlDevice.Address.Function = (UINT32)CxlAgentAddress->Device.FunctionNum;
  CxlDevice.Address.Register = 0;

  return CxlDevice;
}


VOID
CxlGetDeviceId (
  IN       UINT32         CxlDevice,
     OUT   CXL_DEVICE_ID  *CxlDeviceId
)
{
  UINT8     PcieCapPtr;
  PCI_ADDR  PciAddr;
  UINT64    PciSegAddr;

  PciAddr.AddressValue = CxlDevice;
  PciSegAddr = PCI_SEGMENT_LIB_ADDRESS (PciAddr.Address.Segment,
                                        PciAddr.Address.Bus,
                                        PciAddr.Address.Device,
                                        PciAddr.Address.Function,
                                        PciAddr.Address.Register);

  CxlDeviceId->VendorId = PciSegmentRead16 (PciSegAddr + PCICFG_SPACE_VID_OFFSET);
  CxlDeviceId->DeviceId = PciSegmentRead16 (PciSegAddr + PCICFG_SPACE_DID_OFFSET);
  CxlDeviceId->SubVendorId = PciSegmentRead16 (PciSegAddr + PCICFG_SPACE_SUBSYSTEM_VID_OFFSET);
  CxlDeviceId->SubDeviceId = PciSegmentRead16 (PciSegAddr + PCICFG_SPACE_SUBSYSTEM_ID_OFFSET);
  CxlDeviceId->ClassCode = PciSegmentRead16 (PciSegAddr + PCI_CLASS_CODE_1_REG);

  PcieCapPtr = RasFindPciCapability (CxlDevice, PCIE_CAP_ID);
  if (PcieCapPtr != 0) {
    CxlDeviceId->Slot = (UINT16)(PciSegmentRead32 (PciSegAddr + PcieCapPtr + PCIE_SLOT_CAP_REGISTER) >> PCIE_SLOT_NUMBER_SHIFT);
  }

}


VOID
CxlGetCompDeviceId (
  IN       UINT32               CxlDevice,
     OUT   CXL_COMP_DEVICE_ID   *CxlCompDeviceId
)
{
  UINT8      PcieCapPtr;
  PCI_ADDR   PciDevice;
  UINT64     PciSegAddr;

  PciDevice.AddressValue = CxlDevice;
  PciSegAddr = PCI_SEGMENT_LIB_ADDRESS (PciDevice.Address.Segment,
                                        PciDevice.Address.Bus,
                                        PciDevice.Address.Device,
                                        PciDevice.Address.Function,
                                        PciDevice.Address.Register);

  CxlCompDeviceId->VendorId = PciSegmentRead16 (PciSegAddr + PCICFG_SPACE_VID_OFFSET);
  CxlCompDeviceId->DeviceId = PciSegmentRead16 (PciSegAddr + PCICFG_SPACE_DID_OFFSET);
  CxlCompDeviceId->FunctionNum = (UINT8)PciDevice.Address.Function;
  CxlCompDeviceId->DeviceNum = (UINT8)PciDevice.Address.Device;
  CxlCompDeviceId->BusNum = (UINT8)PciDevice.Address.Bus;
  CxlCompDeviceId->SegmentNum = (UINT16)PciDevice.Address.Segment;
  PcieCapPtr = RasFindPciCapability (CxlDevice, PCIE_CAP_ID);
  if (PcieCapPtr != 0) {
    CxlCompDeviceId->Slot = (UINT16)(PciSegmentRead32 (PciSegAddr + PcieCapPtr + PCIE_SLOT_CAP_REGISTER) >> PCIE_SLOT_NUMBER_SHIFT);
  }
}


EFI_STATUS
LogCxlProtocolErrorHelper (
  IN       CXL_ERROR_LOG_DATA   *CxlErrorLogData,
  IN OUT   VOID                 *GenCxlErrEntryBuffer,
     OUT   UINT32               *RecordLength
)
{
  EFI_STATUS                        Status = EFI_SUCCESS;
  EFI_GUID                          CxlErrorSectGuid = CXL_PROTOCOL_ERROR_SECT_GUID;
  GENERIC_CXL_ERR_ENTRY_V3          *GenCxlErrEntry;
  CXL_1_1_RAS_CAPABILITY_STRUCTURE  *RasCapStruc;
  PCIE_CXL_DEVICE_DVSEC             *PcieCxlDeviceDvsec;
  FLEX_BUS_PORT_DVSEC               *FlexBusPortDvsec;
  CXL_AGENT_ADDRESS                 CxlAgentAddress;
  UINT64                            CxlCacheMemRegisterAddr;
  PCI_ADDR                          CxlDevice;
  UINT32                            *CxlDvsec;
  UINT32                            *CxlRasCap;
  UINT32                            Index;
  UINT32                            MaxIndex;
  UINT16                            DevSnCapPtr = 0;
  UINT16                            CxlDvsecPtr;
  UINT16                            CxlRasCapPtr;
  UINT8                             PcieCapPtr = 0;
  UINT64                            PciSegAddr;
  UINT8                             Seg;
  UINT8                             Bus;
  UINT32                            RasCapStrucSmnAddr; //point to CXL_1_1_RAS_CAPABILITY_STRUCTURE

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a - Start\n", __FUNCTION__);

  GenCxlErrEntry = (GENERIC_CXL_ERR_ENTRY_V3*)GenCxlErrEntryBuffer;
  CxlAgentAddress.AgentAddress = CxlErrorLogData->CxlAgentAddress;

  switch (CxlErrorLogData->CxlAgentType) {
  case CXL_AGENT_TYPE_DEVICE:

    CxlDevice = CxlCxlAgentAddrToPciAddr(&CxlAgentAddress);
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL Device at Seg: 0x%x, Bus: 0x%X, Dev: 0x%X, Func: 0x%x, Reg: 0x%x\n",
           CxlDevice.Address.Segment,
           CxlDevice.Address.Bus,
           CxlDevice.Address.Device,
           CxlDevice.Address.Function,
           CxlDevice.Address.Register);
    PciSegAddr = PCI_SEGMENT_LIB_ADDRESS (CxlDevice.Address.Segment,
                                          CxlDevice.Address.Bus,
                                          CxlDevice.Address.Device,
                                          CxlDevice.Address.Function,
                                          CxlDevice.Address.Register);

    *RecordLength = sizeof (GENERIC_CXL_ERR_ENTRY_V3) + sizeof (PCIE_CXL_DEVICE_DVSEC) + sizeof (CXL_1_1_RAS_CAPABILITY_STRUCTURE);
    if (*RecordLength > PRE_GENERIC_CXL_ERR_ENTRY_SIZE) {
      return EFI_INVALID_PARAMETER;
    }

    PcieCxlDeviceDvsec = (PCIE_CXL_DEVICE_DVSEC*)((UINTN)GenCxlErrEntry + sizeof(GENERIC_CXL_ERR_ENTRY_V3));
    RasCapStruc = (CXL_1_1_RAS_CAPABILITY_STRUCTURE*)((UINTN)PcieCxlDeviceDvsec + sizeof (PCIE_CXL_DEVICE_DVSEC));
    FlexBusPortDvsec = NULL;

    GenCxlErrEntry->CxlErrorSection.CxlDvsecLength = sizeof (PCIE_CXL_DEVICE_DVSEC);

    PcieCapPtr = RasFindPciCapability (CxlDevice.AddressValue, PCIE_CAP_ID);

    if (PcieCapPtr != 0) {
      CxlGetDeviceId(CxlDevice.AddressValue, &GenCxlErrEntry->CxlErrorSection.DeviceId);
      GetPcieCapStructure(CxlDevice.AddressValue, &GenCxlErrEntry->CxlErrorSection.CapabilityStructure);

      GenCxlErrEntry->CxlErrorSection.ValidationBits.Field.DeviceIdValid = 1;
      GenCxlErrEntry->CxlErrorSection.ValidationBits.Field.CapabilityStructureValid = 1;
    }

    DevSnCapPtr = RasFindPcieExtendedCapability (CxlDevice.AddressValue, PCIE_EXT_DEV_SN_ID, 0xFFFF);
    if (DevSnCapPtr != 0) {
      GetDeviceSn(CxlDevice.AddressValue, GenCxlErrEntry->CxlErrorSection.DeviceSerialNum);

      GenCxlErrEntry->CxlErrorSection.ValidationBits.Field.DeviceSerialNumberValid = 1;
    }

    // Update DVSEC Structure
    CxlDvsecPtr = RasFindCxlDvsecCapability (CxlDevice.AddressValue, PcieDvsecforCxlDevice);
    if (CxlDvsecPtr != 0) {
      MaxIndex = (sizeof (PCIE_CXL_DEVICE_DVSEC)) / sizeof (UINT32);
      CxlDvsec = (UINT32*)PcieCxlDeviceDvsec;

      for (Index = 0; Index < MaxIndex; Index++) {
      *CxlDvsec = PciSegmentRead32 (PciSegAddr + (CxlDvsecPtr + ((sizeof (UINT32)) * Index)));
        CxlDvsec++;
      }
      GenCxlErrEntry->CxlErrorSection.ValidationBits.Field.CxlDvsecValid = 1;
    }

    break;
  case CXL_AGENT_TYPE_DOWNSTREAM:
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL DownStream port (RCRB) Address: 0x%016lX\n", CxlAgentAddress.AgentAddress);

    *RecordLength = sizeof (GENERIC_CXL_ERR_ENTRY_V3) + sizeof (FLEX_BUS_PORT_DVSEC) + sizeof (CXL_1_1_RAS_CAPABILITY_STRUCTURE);
    if (*RecordLength > PRE_GENERIC_CXL_ERR_ENTRY_SIZE) {
      return EFI_INVALID_PARAMETER;
    }

    PcieCxlDeviceDvsec = NULL;
    FlexBusPortDvsec = (FLEX_BUS_PORT_DVSEC*)((UINTN)GenCxlErrEntry + sizeof(GENERIC_CXL_ERR_ENTRY_V3));
    RasCapStruc = (CXL_1_1_RAS_CAPABILITY_STRUCTURE*)((UINTN)FlexBusPortDvsec + sizeof (FLEX_BUS_PORT_DVSEC));
    GenCxlErrEntry->CxlErrorSection.CxlDvsecLength = sizeof (FLEX_BUS_PORT_DVSEC);

    // Update DVSEC Structure
    CxlDvsecPtr = RasFindCxlRcrbDvsecCapability (CxlAgentAddress.AgentAddress, PcieDvsecforFlexBusPort);
    if (CxlDvsecPtr != 0) {
      MaxIndex = (sizeof (FLEX_BUS_PORT_DVSEC)) / sizeof (UINT32);
      CxlDvsec = (UINT32*)FlexBusPortDvsec;

      for (Index = 0; Index < MaxIndex; Index++) {
        *CxlDvsec = MmioRead32 (CxlAgentAddress.AgentAddress + (CxlDvsecPtr + ((sizeof (UINT32)) * Index)));
        CxlDvsec++;
      }
      GenCxlErrEntry->CxlErrorSection.ValidationBits.Field.CxlDvsecValid = 1;
    }

    break;
  case CXL_AGENT_TYPE_ROOT_PORT:
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL Root Port at Seg: 0x%X, Bus: 0x%X, Dev: 0x%X, Func: 0x%X\n",
           CxlAgentAddress.Device.SegmentNum,
           CxlAgentAddress.Device.BusNum,
           CxlAgentAddress.Device.DeviceNum,
           CxlAgentAddress.Device.FunctionNum);

    *RecordLength = sizeof (GENERIC_CXL_ERR_ENTRY_V3) + sizeof (FLEX_BUS_PORT_DVSEC) + sizeof (CXL_1_1_RAS_CAPABILITY_STRUCTURE);
    if (*RecordLength > PRE_GENERIC_CXL_ERR_ENTRY_SIZE) {
      return EFI_INVALID_PARAMETER;
    }

    PcieCxlDeviceDvsec = NULL;
    FlexBusPortDvsec = (FLEX_BUS_PORT_DVSEC*)((UINTN)GenCxlErrEntry + sizeof(GENERIC_CXL_ERR_ENTRY_V3));
    RasCapStruc = (CXL_1_1_RAS_CAPABILITY_STRUCTURE*)((UINTN)FlexBusPortDvsec + sizeof (FLEX_BUS_PORT_DVSEC));
    GenCxlErrEntry->CxlErrorSection.CxlDvsecLength = sizeof (FLEX_BUS_PORT_DVSEC);

    CxlDevice = CxlCxlAgentAddrToPciAddr(&CxlAgentAddress);
    PciSegAddr = PCI_SEGMENT_LIB_ADDRESS (CxlDevice.Address.Segment,
                                          CxlDevice.Address.Bus,
                                          CxlDevice.Address.Device,
                                          CxlDevice.Address.Function,
                                          CxlDevice.Address.Register);

    // Update DVSEC Structure
    CxlDvsecPtr = RasFindCxlDvsecCapability (CxlDevice.AddressValue, PcieDvsecforFlexBusPort);
    if (CxlDvsecPtr != 0) {
      CxlDvsec = (UINT32*)FlexBusPortDvsec;
      MaxIndex = (sizeof (FLEX_BUS_PORT_DVSEC)) / sizeof (UINT32);

      for (Index = 0; Index < MaxIndex; Index++) {
        *CxlDvsec = PciSegmentRead32 (PciSegAddr + (CxlDvsecPtr + ((sizeof (UINT32)) * Index)));
        CxlDvsec++;
      }
      GenCxlErrEntry->CxlErrorSection.ValidationBits.Field.CxlDvsecValid = 1;
    }

    // Update RAS Capbability Structure
    //   Search RAS Capability structure Pointer
    CxlCacheMemRegisterAddr = CxlErrorLogData->CxlComponentRegAddr + CXL_CACHE_MEM_REGISTERS_OFFSET;
    CxlRasCapPtr = RasGetCxlCapabilityPointer (CxlCacheMemRegisterAddr, CxlRasCapabilityId);

    if (0 != CxlRasCapPtr) {
      CxlRasCap = (UINT32*)RasCapStruc;
      MaxIndex = (sizeof (CXL_1_1_RAS_CAPABILITY_STRUCTURE)) / sizeof (UINT32);

      for (Index = 0; Index < MaxIndex; Index++) {
        *CxlRasCap = MmioRead32 (CxlCacheMemRegisterAddr + (CxlRasCapPtr + ((sizeof (UINT32)) * Index)));
        CxlRasCap++;
      }
      GenCxlErrEntry->CxlErrorSection.ValidationBits.Field.CxlErrorLogValid = 1;
      GenCxlErrEntry->CxlErrorSection.CxlErrorLogLength = sizeof (CXL_1_1_RAS_CAPABILITY_STRUCTURE);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL RAS Cap - UnCorr Error Status: 0x%08X\n",
        RasCapStruc->UncorrectableErrorStatus.Uint32);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL RAS Cap - UnCorr Error Mask: 0x%08X\n",
        RasCapStruc->UncorrectableErrorMask.Uint32);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL RAS Cap - UnCorr Error Severity: 0x%08X\n",
        RasCapStruc->UncorrectableErrorSeverity.Uint32);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL RAS Cap - Corr Error Status: 0x%08X\n",
        RasCapStruc->CorrectableErrorStatus.Uint32);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL RAS Cap - Corr Error Mask: 0x%08X\n",
        RasCapStruc->CorrectableErrorMask.Uint32);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL RAS Cap - ErrorCapAndCtrl: 0x%08X\n",
        RasCapStruc->ErrorCapabilitiesAndControl.Uint32);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL RAS Cap - HeaderLog[0]: 0x%08X\n",
        RasCapStruc->HeaderLog[0]);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL RAS Cap - HeaderLog[1]: 0x%08X\n",
        RasCapStruc->HeaderLog[1]);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL RAS Cap - HeaderLog[2]: 0x%08X\n",
        RasCapStruc->HeaderLog[2]);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL RAS Cap - HeaderLog[3]: 0x%08X\n",
        RasCapStruc->HeaderLog[3]);
    }
    break;
  default:
    //Invalid Parameter.
    return EFI_INVALID_PARAMETER;
    break;
  }


  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]GenCxlErrEntry Address: 0x%08x\n", GenCxlErrEntry);
  if (PcieCxlDeviceDvsec != NULL) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]PcieCxlDeviceDvsec Address: 0x%08x\n", PcieCxlDeviceDvsec);
  }
  if (FlexBusPortDvsec != NULL) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]FlexBusPortDvsec Address: 0x%08x\n", FlexBusPortDvsec);
  }
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]RasCapStruc Address: 0x%08x\n", RasCapStruc);


  //Update Error section GUID
  CopyGuid ((EFI_GUID*)&GenCxlErrEntry->GenErrorDataEntry.SectionType, &CxlErrorSectGuid);

  //Update generic error section
  GenCxlErrEntry->GenErrorDataEntry.Revision = GENERIC_ERROR_REVISION;
  GenCxlErrEntry->GenErrorDataEntry.ValidationBits = FRU_STRING_VALID;
  GenCxlErrEntry->GenErrorDataEntry.Flags = CPER_SECTION_FLAG_PRIMARY;
  AsciiStrCpyS ((CHAR8 *)GenCxlErrEntry->GenErrorDataEntry.FruText, FRU_TEXT_MAX_LENGTH, "CXL Protocol Error");  // Error Fru Text String

  GenCxlErrEntry->CxlErrorSection.CxlAgentAddress.AgentAddress = CxlErrorLogData->CxlAgentAddress;
  GenCxlErrEntry->CxlErrorSection.CxlAgentType = CxlErrorLogData->CxlAgentType;

  GenCxlErrEntry->CxlErrorSection.ValidationBits.Field.CxlAgentTypeValid = 1;
  GenCxlErrEntry->CxlErrorSection.ValidationBits.Field.CXLAgentAddressValid = 1;

  // CXL 1.1 Update RAS Capbability Structure
  //Search RAS Capability structure Pointer
  if ((CxlErrorLogData->CxlAgentType == CXL_AGENT_TYPE_DOWNSTREAM) &&
      (CxlErrorLogData->CxlRasCapStrucSmnAddr != 0)) {
    CxlDevice.AddressValue = CxlErrorLogData->RcecAddress;
    Seg = (UINT8)CxlDevice.Address.Segment;
    Bus = (UINT8)CxlDevice.Address.Bus;

    RasCapStrucSmnAddr = CxlErrorLogData->CxlRasCapStrucSmnAddr;
    CxlRasCap = (UINT32*)RasCapStruc;
    MaxIndex = (sizeof (CXL_1_1_RAS_CAPABILITY_STRUCTURE)) / sizeof (UINT32);

    for (Index = 0; Index < MaxIndex; Index++) {
      RasSmnRead (SMN_SEG_BUS (Seg, Bus), (RasCapStrucSmnAddr + ((sizeof (UINT32)) * Index)), CxlRasCap);
      CxlRasCap++;
    }
    GenCxlErrEntry->CxlErrorSection.ValidationBits.Field.CxlErrorLogValid = 1;
    GenCxlErrEntry->CxlErrorSection.CxlErrorLogLength = sizeof (CXL_1_1_RAS_CAPABILITY_STRUCTURE);
  } else if (CxlErrorLogData->CxlAgentType != CXL_AGENT_TYPE_ROOT_PORT) {
    //CXL RCRB with CxlRasCapStrucSmnAddr == 0  or CXL Device
    CxlCacheMemRegisterAddr = CxlErrorLogData->CxlComponentRegAddr + CXL_CACHE_MEM_REGISTERS_OFFSET;
    CxlRasCapPtr = RasGetCxlCapabilityPointer (CxlCacheMemRegisterAddr, CxlRasCapabilityId);

    if (0 != CxlRasCapPtr) {
      CxlRasCap = (UINT32*)RasCapStruc;
      MaxIndex = (sizeof (CXL_1_1_RAS_CAPABILITY_STRUCTURE)) / sizeof (UINT32);

      for (Index = 0; Index < MaxIndex; Index++) {
        *CxlRasCap = MmioRead32 (CxlCacheMemRegisterAddr + (CxlRasCapPtr + ((sizeof (UINT32)) * Index)));
        CxlRasCap++;
      }
      GenCxlErrEntry->CxlErrorSection.ValidationBits.Field.CxlErrorLogValid = 1;
      GenCxlErrEntry->CxlErrorSection.CxlErrorLogLength = sizeof (CXL_1_1_RAS_CAPABILITY_STRUCTURE);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL RAS Cap - UnCorr Error Status: 0x%08X\n",
        RasCapStruc->UncorrectableErrorStatus.Uint32);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL RAS Cap - UnCorr Error Mask: 0x%08X\n",
        RasCapStruc->UncorrectableErrorMask.Uint32);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL RAS Cap - UnCorr Error Severity: 0x%08X\n",
        RasCapStruc->UncorrectableErrorSeverity.Uint32);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL RAS Cap - Corr Error Status: 0x%08X\n",
        RasCapStruc->CorrectableErrorStatus.Uint32);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL RAS Cap - Corr Error Mask: 0x%08X\n",
        RasCapStruc->CorrectableErrorMask.Uint32);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL RAS Cap - ErrorCapAndCtrl: 0x%08X\n",
        RasCapStruc->ErrorCapabilitiesAndControl.Uint32);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL RAS Cap - HeaderLog[0]: 0x%08X\n",
        RasCapStruc->HeaderLog[0]);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL RAS Cap - HeaderLog[1]: 0x%08X\n",
        RasCapStruc->HeaderLog[1]);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL RAS Cap - HeaderLog[2]: 0x%08X\n",
        RasCapStruc->HeaderLog[2]);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL RAS Cap - HeaderLog[3]: 0x%08X\n",
        RasCapStruc->HeaderLog[3]);
    }
  }

  GenCxlErrEntry->GenErrorDataEntry.ErrorDataLength = *RecordLength - sizeof (EFI_ACPI_6_3_GENERIC_ERROR_DATA_ENTRY_STRUCTURE);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a - End\n", __FUNCTION__);

  return Status;
}


VOID
CxlDeviceEventLogSeverityConvertion (
  IN       UINT32 EventRecordSeverity,
     OUT   UINT32 *ErrorSeverity
)
{

  /*
  Convert CXL event record severity to ACPI severity
  Bits[1:0]: Event Record Severity: The severity of the event. This
  shall match the event log where the event was placed by the device.
  (0) = Informational Event
  (1) = Warning Event
  (2) = Failure Event
  (3) = Fatal Event

  ACPI 6.1 Table 18-343 Generic Error Data Entry
  (0) = ERROR_RECOVERABLE
  (1) = ERROR_SEVERITY_FATAL
  (2) = ERROR_SEVERITY_CORRECTED
  (3) = ERROR_NONE
  */
  switch (EventRecordSeverity) {
  case EVENT_RECORD_SEVERITY_INFORMATIONAL:
    *ErrorSeverity = ERROR_NONE;
    break;
  case EVENT_RECORD_SEVERITY_WARNING:
    *ErrorSeverity = ERROR_SEVERITY_CORRECTED;
    break;
  case EVENT_RECORD_SEVERITY_FAILURE:
    *ErrorSeverity = ERROR_RECOVERABLE;
    break;
  case EVENT_RECORD_SEVERITY_FATAL:
    *ErrorSeverity = ERROR_SEVERITY_FATAL;
    break;
  default:
    *ErrorSeverity = ERROR_NONE;
  }
}


EFI_STATUS
LogCxlEventRecordHelper (
  IN       CXL_ERROR_LOG_DATA                 *CxlErrorLogData,
  IN       EVENT_RECORD_HEADER                *EventRecordHdr,
  IN OUT   VOID                               *GenCxlEventEntryBuffer,
  IN       UINT32                             *CxlEventLen
)
{
  EFI_STATUS                  Status = EFI_SUCCESS;
  GENERIC_CXL_EVENT_ENTRY_V3  *GenCxlEventEntry;
  EVENT_RECORD_HEADER         *EventRecordHeader;
  CXL_AGENT_ADDRESS           CxlAgentAddress;
  UINT32                      *EventRecord;
  UINT32                      Index;
  UINT32                      MaxIndex;
  PCI_ADDR                    CxlDevice;
  UINT16                      DevSnCapPtr = 0;
  UINT8                       PcieCapPtr = 0;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a - Start\n", __FUNCTION__);

  if (*CxlEventLen < sizeof(GENERIC_CXL_EVENT_ENTRY_V3)) {
    Status = EFI_INVALID_PARAMETER;
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Invalid CxlEventLen: 0x%x\n", *CxlEventLen);
    return Status;
  }

  GenCxlEventEntry = (GENERIC_CXL_EVENT_ENTRY_V3*)GenCxlEventEntryBuffer;
  CxlAgentAddress.AgentAddress = CxlErrorLogData->CxlAgentAddress;
  EventRecordHeader = EventRecordHdr;

  //Update Error section GUID
  GenerateErrorSectionGuid (
    (EFI_GUID*)&GenCxlEventEntry->GenErrorDataEntry.SectionType,
    &EventRecordHeader->EventRecordIdentifier);

  //Update generic error section
  GenCxlEventEntry->GenErrorDataEntry.Revision = GENERIC_ERROR_REVISION;
  GenCxlEventEntry->GenErrorDataEntry.ValidationBits = FRU_STRING_VALID;
  GenCxlEventEntry->GenErrorDataEntry.Flags = CPER_SECTION_FLAG_PRIMARY;
  AsciiStrCpyS ((CHAR8 *)GenCxlEventEntry->GenErrorDataEntry.FruText, FRU_TEXT_MAX_LENGTH, "CXL Component Event");  // Error Fru Text String

  CxlDevice = CxlCxlAgentAddrToPciAddr(&CxlAgentAddress);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "CXL Device at Seg: 0x%x, Bus: 0x%X, Dev: 0x%X, Func: 0x%x, Reg: 0x%x\n",
         CxlDevice.Address.Segment,
         CxlDevice.Address.Bus,
         CxlDevice.Address.Device,
         CxlDevice.Address.Function,
         CxlDevice.Address.Register);

  DevSnCapPtr = RasFindPcieExtendedCapability (CxlDevice.AddressValue, PCIE_EXT_DEV_SN_ID, 0xFFFF);
  PcieCapPtr = RasFindPciCapability (CxlDevice.AddressValue, PCIE_CAP_ID);

  if (PcieCapPtr != 0) {
    CxlGetCompDeviceId(CxlDevice.AddressValue, &GenCxlEventEntry->CxlEventSection.DeviceId);
    GenCxlEventEntry->CxlEventSection.ValidationBits.Field.DeviceIdValid = 1;
  }

  if (DevSnCapPtr != 0) {
    GetDeviceSn(CxlDevice.AddressValue, GenCxlEventEntry->CxlEventSection.DeviceSerialNum);
    GenCxlEventEntry->CxlEventSection.ValidationBits.Field.DeviceSerialNumberValid = 1;
  }

  //Starting with the Common Event Record field. (e.g. CXL 2.0 - Table 155. DRAM Event Record, Starting with offset 0x10)
  EventRecord = (UINT32*)&(EventRecordHeader->EventRecordLength);

  MaxIndex = ((*CxlEventLen) - sizeof (GENERIC_CXL_EVENT_ENTRY_V3)) / sizeof (UINT32);

  for (Index = 0; Index < MaxIndex; Index++) {
    GenCxlEventEntry->CxlEventSection.CxlErrorLog[Index] = MmioRead32((UINTN)EventRecord);
    EventRecord++;
  }

  GenCxlEventEntry->CxlEventSection.ValidationBits.Field.CxlComponentEventLogValid = 1;

  GenCxlEventEntry->GenErrorDataEntry.ErrorDataLength = (*CxlEventLen) - sizeof (EFI_ACPI_6_3_GENERIC_ERROR_DATA_ENTRY_STRUCTURE);

  //In CXL 2.0, "GenCxlEventEntry->CxlEventSection.Length" will be the same as "GenCxlEventEntry->GenErrorDataEntry.ErrorDataLength"
  GenCxlEventEntry->CxlEventSection.Length = sizeof (CXL_EVENT_RECORD) + ((*CxlEventLen) - sizeof (GENERIC_CXL_EVENT_ENTRY_V3));

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a - End\n", __FUNCTION__);

  return Status;
}

BOOLEAN
RcrbAerUnCorrErrCheck(
  IN       UINT64        RcrbAddress,
  IN       UINT16        AerCapPtr
)
{
  UINT32            PcieErrorStatus;
  UINT32            PcieErrorMask;
  UINT32            PcieError;

  PcieErrorStatus = MmioRead32(RcrbAddress + AerCapPtr + PCIE_UNCORR_STATUS_PTR);
  PcieErrorMask = MmioRead32(RcrbAddress + AerCapPtr + PCIE_UNCORR_MASK_PTR);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  RCRB AER UnCorr Error Status: 0x%08x\n", PcieErrorStatus);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  RCRB AER UnCorr Error Mask: 0x%08x\n", PcieErrorMask);

  PcieError = PcieErrorStatus & ~PcieErrorMask;
  if (0 != PcieError) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  RCRB AER UnCorr Error Found\n");
    return TRUE;
  }
  return FALSE;
}


BOOLEAN
RcrbAerCorrErrCheck(
  IN       UINT64        RcrbAddress,
  IN       UINT16        AerCapPtr
)
{
  UINT32            PcieErrorStatus;
  UINT32            PcieErrorMask;
  UINT32            PcieError;

  PcieErrorStatus = MmioRead32(RcrbAddress + AerCapPtr + PCIE_CORR_STATUS_PTR);
  PcieErrorMask = MmioRead32(RcrbAddress + AerCapPtr + PCIE_CORR_MASK_PTR);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  RCRB AER Corr Error Status: 0x%08x\n", PcieErrorStatus);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  RCRB AER Corr Error Mask: 0x%08x\n", PcieErrorMask);

  PcieError = PcieErrorStatus & ~PcieErrorMask;
  if (0 != PcieError) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  PCIE AER Corr Error Found!!!\n");
    return TRUE;
  }
  return FALSE;
}


BOOLEAN
RcrbAerErrCheck(
  IN       UINT64        RcrbAddress,
  IN       UINT16        AerCapPtr
)
{
  BOOLEAN           ValidError;

  ValidError = RcrbAerUnCorrErrCheck (RcrbAddress, AerCapPtr);
  if (ValidError) {
    return TRUE;
  }

  ValidError = RcrbAerCorrErrCheck (RcrbAddress, AerCapPtr);
  if (ValidError) {
    return TRUE;
  }
  return FALSE;
}


BOOLEAN
CxlRasUnCorrErrCheck(
  IN       UINT64        CxlCacheMemRegisterAddr,
  IN       UINT16        CxlRasCapPtr
)
{
  UINT32            CxlRasErrorStatus;
  UINT32            CxlRasErrorMask;
  UINT32            CxlRasError;

  CxlRasErrorStatus = MmioRead32(CxlCacheMemRegisterAddr + CxlRasCapPtr + CXL_UNCORRECTABLE_ERROR_STATUS_OFFSET);
  CxlRasErrorMask = MmioRead32(CxlCacheMemRegisterAddr + CxlRasCapPtr + CXL_UNCORRECTABLE_ERROR_MASK_OFFSET);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  CXL RAS UnCorr Error Status: 0x%08x\n", CxlRasErrorStatus);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  CXL RAS UnCorr Error Mask: 0x%08x\n", CxlRasErrorMask);

  CxlRasError = CxlRasErrorStatus & ~CxlRasErrorMask;
  if (0 != CxlRasError) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  CXL RAS UnCorr Error Found\n");
    return TRUE;
  }
  return FALSE;
}


BOOLEAN
CxlRasCorrErrCheck(
  IN       UINT64        CxlCacheMemRegisterAddr,
  IN       UINT16        CxlRasCapPtr
)
{
  UINT32            CxlRasErrorStatus;
  UINT32            CxlRasErrorMask;
  UINT32            CxlRasError;

  CxlRasErrorStatus = MmioRead32(CxlCacheMemRegisterAddr + CxlRasCapPtr + CXL_CORRECTABLE_ERROR_STATUS_OFFSET);
  CxlRasErrorMask = MmioRead32(CxlCacheMemRegisterAddr + CxlRasCapPtr + CXL_CORRECTABLE_ERROR_MASK_OFFSET);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  CXL RAS Corr Error Status: 0x%08x\n", CxlRasErrorStatus);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  CXL RAS Corr Error Mask: 0x%08x\n", CxlRasErrorMask);

  CxlRasError = CxlRasErrorStatus & ~CxlRasErrorMask;
  if (0 != CxlRasError) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  CXL RAS Corr Error Found!!!\n");
    return TRUE;
  }
  return FALSE;
}


BOOLEAN
CxlRasCapErrCheck(
  IN       UINT64        CxlCacheMemRegisterAddr,
  IN       UINT16        CxlRasCapPtr
)
{
  BOOLEAN           ValidError;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  %a - CxlCacheMemRegisterAddr: 0x%016lX, CxlRasCapPtr: 0x%04x\n",
          __FUNCTION__,
          CxlCacheMemRegisterAddr,
          CxlRasCapPtr);

  ValidError = CxlRasUnCorrErrCheck (CxlCacheMemRegisterAddr, CxlRasCapPtr);
  if (ValidError) {
    return TRUE;
  }

  ValidError = CxlRasCorrErrCheck (CxlCacheMemRegisterAddr, CxlRasCapPtr);
  if (ValidError) {
    return TRUE;
  }

  return FALSE;
}

EFI_STATUS
CxlRasCapStsClear(
  IN       UINT64        ComponentRegisterAddr,
  IN       UINT32        RetryCnt
)
{
  UINT64    CxlCacheMemRegisterAddr;
  UINT32    CxlRasCorrErrStatus;
  UINT32    CxlRasUnCorrErrStatus;
  UINT32    RasRetryCounter;
  UINT16    CxlRasCapPtr;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS]  CxlRasCapStsClear Entry\n");

  RasRetryCounter = RetryCnt;
  CxlCacheMemRegisterAddr = ComponentRegisterAddr + CXL_CACHE_MEM_REGISTERS_OFFSET;
  CxlRasCapPtr = RasGetCxlCapabilityPointer (CxlCacheMemRegisterAddr, CxlRasCapabilityId);

  if (0 != CxlRasCapPtr) {
    CxlRasCorrErrStatus = MmioRead32(CxlCacheMemRegisterAddr + CxlRasCapPtr + CXL_CORRECTABLE_ERROR_STATUS_OFFSET);
    CxlRasUnCorrErrStatus = MmioRead32(CxlCacheMemRegisterAddr + CxlRasCapPtr + CXL_UNCORRECTABLE_ERROR_STATUS_OFFSET);

    while ((CxlRasCorrErrStatus != 0) || (CxlRasUnCorrErrStatus != 0)) {
      if (RasRetry (&RasRetryCounter)) {
        return EFI_ABORTED;
      }
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS]  CXL RAS Corr Error Status : 0x%08x\n", CxlRasCorrErrStatus);
      MmioWrite32 (CxlCacheMemRegisterAddr + CxlRasCapPtr + CXL_CORRECTABLE_ERROR_STATUS_OFFSET, CxlRasCorrErrStatus);
      CxlRasCorrErrStatus = MmioRead32(ComponentRegisterAddr + CxlRasCapPtr + CXL_CORRECTABLE_ERROR_STATUS_OFFSET);

      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS]  CXL RAS UnCorr Error Status : 0x%08x\n", CxlRasUnCorrErrStatus);
      MmioWrite32 (CxlCacheMemRegisterAddr + CxlRasCapPtr + CXL_UNCORRECTABLE_ERROR_STATUS_OFFSET, CxlRasUnCorrErrStatus);
      CxlRasUnCorrErrStatus = MmioRead32(ComponentRegisterAddr + CxlRasCapPtr + CXL_UNCORRECTABLE_ERROR_STATUS_OFFSET);
    }
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS]  CxlRasCapStsClear Exit\n");

  return EFI_SUCCESS;
}


/*----------------------------------------------------------------------------------------*/
/*
 * Find CXL Capability Pointer
 *
 *
 *
 * @param[in] Address               ComponentRegisterAddr
 * @retval                          CXL Capability pointer
 *
 */
UINT16
RasGetCxlCapabilityPointer (
  IN      UINT64              Address,
  IN      UINT16              CxlCapId
  )
{
  UINT8   ArrayIndex;
  CXL_CAPABILITY_HEADER           CxlCapabilityHeader;
  CXL_CAPABILITY_ELEMENT_HEADER   CxlCapabilityElementHeader;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  %a - Address: 0x%016lX, CxlCapId: 0x%04X, ",
          __FUNCTION__,
          Address,
          CxlCapId);

  CxlCapabilityHeader.Uint32 = MmioRead32 (Address | CXL_CAPABILITY_HEADER_OFFSET);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Value: 0x%08X\n", CxlCapabilityHeader.Uint32);

  if ((1 == CxlCapabilityHeader.Bits.CxlCapabilityId) && (1 == CxlCapabilityHeader.Bits.CxlCapabilityVersion)) {
    //This is a valid Capability header, find the Cap pointer match the Cap Id
    if (CxlCapabilityHeader.Bits.ArraySize != 0) {
      for (ArrayIndex = 0; ArrayIndex < CxlCapabilityHeader.Bits.ArraySize; ArrayIndex++) {
        CxlCapabilityElementHeader.Value = MmioRead32((Address | CXL_CAPABILITY_ELEMENT_HEADER_OFFSET) + (ArrayIndex * sizeof(UINT32)));
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "   - Capability at 0x%x with type 0x%x\n",
               CxlCapabilityElementHeader.Field.CxlCapabilityPointer,
               CxlCapabilityElementHeader.Field.CxlCapabilityId
               );
        if (CxlCapabilityElementHeader.Field.CxlCapabilityId == CxlCapId) {
          return  (UINT16)CxlCapabilityElementHeader.Field.CxlCapabilityPointer;
        }
      }
    }
  } else {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  CXL Capability header not found\n");
  }
  return 0;
}


/*----------------------------------------------------------------------------------------*/
/*
 * Find CXL Device capability pointer
 *
 *
 *
 * @param[in] Address               CXL Memory Device regstier address
 * @param[in] CxlDevCapId           CXL Device Capability ID
 * @param[out] CxlDevCapLength      CXL Device Capability Structure Length
 * @retval                          CXL Device Capability pointer
 *
 */
UINT32
RasGetCxlDeviceCapabilityPointer (
  IN      UINT64    Address,
  IN      UINT16    CxlDevCapId,
     OUT  UINT32    *CxlDevCapLength
)
{
  CXL_DEVICE_CAPABILITY_ARRAY     CxlDeviceCapabilityArray;
  CXL_DEVICE_CAPABILITY_HEADER1   CxlDeviceCapabilityHeader1;
  CXL_DEVICE_CAPABILITY_HEADER2   CxlDeviceCapabilityHeader2;
  UINT64                          CapHeaderAddr;
  UINT16                          CapIndex;
  UINT32                          CapLength;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  %a - Address: 0x%016lX, DvsecId: 0x%04x\n",
          __FUNCTION__,
          Address,
          CxlDevCapId);

  CxlDeviceCapabilityArray.Value = MmioRead64(Address | CXL_DEVICE_CAPABILITY_ARRAY_OFFSET);
  if ((0 == CxlDeviceCapabilityArray.Field.CapabilityId) && (1 == CxlDeviceCapabilityArray.Field.Version)) {
    //Valid CXL Device Capabilities Array Register.
    if (CxlDeviceCapabilityArray.Field.CapabilitiesCount != 0) {
      CapHeaderAddr = Address | CXL_DEVICE_CAPABILITY_HEADER1_OFFSET;
      for (CapIndex = 0; CapIndex < CxlDeviceCapabilityArray.Field.CapabilitiesCount; CapIndex++) {
        CxlDeviceCapabilityHeader1.Value = MmioRead64(CapHeaderAddr);
        if (CxlDeviceCapabilityHeader1.Field.CapabilityId == CxlDevCapId) {
          CxlDeviceCapabilityHeader2.Value = MmioRead64(CapHeaderAddr + sizeof (UINT64));
          CapLength = (UINT32)CxlDeviceCapabilityHeader2.Field.Length;
          if (CxlDevCapLength != NULL) {
            *CxlDevCapLength = CapLength;
          }
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "   - Capability at 0x%x with type 0x%x, Version: %d, DvsecLength: 0x%x\n",
                 CxlDeviceCapabilityHeader1.Field.Offset,
                 CxlDeviceCapabilityHeader1.Field.CapabilityId,
                 CxlDeviceCapabilityHeader1.Field.Version,
                 CapLength);

          return  (UINT32)CxlDeviceCapabilityHeader1.Field.Offset;
        }
        CapHeaderAddr += CXL_DEVICE_CAPABILITY_HEADER_SIZE;
      }
    }
  } else {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  CXL Device Capability Array not found\n");
  }
  return 0;
}



/*----------------------------------------------------------------------------------------*/
/*
 * Find CXL DVSEC capability pointer
 *
 *
 *
 * @param[in] Address               PCI address (as described in PCI_ADDR)
 * @param[in] DvsecId               CXL DvsecId
 * @retval                          Register address of extended capability pointer
 *
 */
UINT16
RasFindCxlDvsecCapability (
  IN      UINT32              Address,
  IN      UINT16              DvsecId
  )
{
  UINT16                              CapabilityPtr;
  UINT16                              CurrentDvsecId;
  UINT32                              ExtendedCapabilityIdBlock;
  DESIGNATED_VENDOR_SPECIFIC_HEADER1  DesignatedVendorSpecificHeader1;
  PCI_ADDR                            PciAddr;
  UINT64                              PciSegAddr;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  %a - Address: 0x%08X, DvsecId: 0x%04x\n",
          __FUNCTION__,
          Address,
          DvsecId);

  PciAddr.AddressValue = Address;
  PciSegAddr = PCI_SEGMENT_LIB_ADDRESS (PciAddr.Address.Segment,
                                        PciAddr.Address.Bus,
                                        PciAddr.Address.Device,
                                        PciAddr.Address.Function,
                                        PciAddr.Address.Register);

  //Get DVSEC capability pointer
  CapabilityPtr = RasFindPcieExtendedCapability (Address, PCIE_EXT_ESM_CAP_ID, 0xFFFF);
  if (CapabilityPtr != 0) {
    do {
    //Check DVSEC Id
      DesignatedVendorSpecificHeader1.Value = PciSegmentRead32(PciSegAddr | (CapabilityPtr + DVSEC_HEADER1_OFFSET));
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Designated Vendor Specific Header 1: 0x%08X from CapPtr: 0x%04X\n",
              DesignatedVendorSpecificHeader1.Value,
              CapabilityPtr);

      if (DesignatedVendorSpecificHeader1.Field.DvsecVendorId == CXL_DVSEC_VENDOR_ID) {
          //This is CXL DVSEC capability, check ID
        CurrentDvsecId = PciSegmentRead16(PciSegAddr | (CapabilityPtr + DVSEC_HEADER2_OFFSET));
        if (CurrentDvsecId == DvsecId) {
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Structure Pointer found\n");
          return  CapabilityPtr;
        }
      }

      ExtendedCapabilityIdBlock = PciSegmentRead32(PciSegAddr | CapabilityPtr);
      CapabilityPtr = (UINT16) ((ExtendedCapabilityIdBlock >> 20) & 0xfff);
    } while ((CapabilityPtr != 0) && (CapabilityPtr != 0xfff));
  }

  return 0;
}


/*----------------------------------------------------------------------------------------*/
/*
 * Find CXL RCRB DVSEC capability pointer
 *
 *
 *
 * @param[in] Address               RCRB address
 * @param[in] DvsecId               CXL DvsecId
 * @retval                          Register address of extended capability pointer
 *
 */
UINT16
RasFindCxlRcrbDvsecCapability (
  IN      UINT64              RcrbAddress,
  IN      UINT16              DvsecId
  )
{
  UINT16  CapabilityPtr;
  UINT16  CurrentDvsecId;
  UINT32  ExtendedCapabilityIdBlock;
  DESIGNATED_VENDOR_SPECIFIC_HEADER1  DesignatedVendorSpecificHeader1;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  %a - RcrbAddress: 0x%016lX\n", __FUNCTION__, RcrbAddress);

  //Get DVSEC capability pointer
  CapabilityPtr = RasFindRcrbExtendedCapability (RcrbAddress, PCIE_EXT_ESM_CAP_ID);
  if (CapabilityPtr != 0) {
    do {
    //Check DVSEC Id
      DesignatedVendorSpecificHeader1.Value = MmioRead32(RcrbAddress | (CapabilityPtr + DVSEC_HEADER1_OFFSET));
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Designated Vendor Specific Header 1: 0x%08X from CapPtr: 0x%04X\n",
              DesignatedVendorSpecificHeader1.Value,
              CapabilityPtr);

      if (DesignatedVendorSpecificHeader1.Field.DvsecVendorId == CXL_DVSEC_VENDOR_ID) {
          //This is CXL DVSEC capability, check ID

        CurrentDvsecId = MmioRead16(RcrbAddress | (CapabilityPtr + DVSEC_HEADER2_OFFSET));
        if (CurrentDvsecId == DvsecId) {
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Structure Pointer found\n");
          return  CapabilityPtr;
        }
      }

      ExtendedCapabilityIdBlock = MmioRead32(RcrbAddress | CapabilityPtr);
      CapabilityPtr = (UINT16) ((ExtendedCapabilityIdBlock >> 20) & 0xfff);
    } while ((CapabilityPtr != 0) && (CapabilityPtr != 0xfff));
  }
  return 0;
}


/**
 *  Get Number of Register Locator DVSEC Register Block Entries
 *
 *
 *
 * @param[in] RciepAddress              RCiEP Address
 * @param[in] RegisterLocatorDvsecPtr   Register Locator DVSEC pointer
 * @retval                              Number of Register Locator DVSEC Register Block Entries
 *
 **/
UINT16
RasGetRegisterBlockNumber (
  IN      UINT32              RciepAddress,
  IN      UINT16              RegisterLocatorDvsecPtr
)
{
  DESIGNATED_VENDOR_SPECIFIC_HEADER1  DesignatedVendorSpecificHeader1;
  PCI_ADDR                            PciAddr;
  UINT64                              PciSegAddr;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  %a - RciepAddress: 0x%8X\n", __FUNCTION__, RciepAddress);

  PciAddr.AddressValue = RciepAddress;

  PciSegAddr = PCI_SEGMENT_LIB_ADDRESS (PciAddr.Address.Segment,
                                        PciAddr.Address.Bus,
                                        PciAddr.Address.Device,
                                        PciAddr.Address.Function,
                                        PciAddr.Address.Register);

  DesignatedVendorSpecificHeader1.Value = PciSegmentRead32(PciSegAddr | (RegisterLocatorDvsecPtr + DVSEC_HEADER1_OFFSET));

  return (UINT16)((DesignatedVendorSpecificHeader1.Field.DvsecLength - REGISTER_BLOCK1_OFFSET_LOW) / sizeof (UINT64));
}


/**
 *  Get Component Register address from Down Stream port RCRB
 *
 *
 *
 * @param[in] RCRB Address          RCRB address
 * @retval                          Component Register address
 *
 **/
UINT64
RasGetDpComponentRegisterAddress (
  IN      UINT64    RcrbAddress
)
{
  UINT64  ComponentRegisterAddress = 0;
  UINT32  ComponentRegisterAddressLo;

  if (RcrbAddress == 0) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  [Error] %a: RcrbAddress is 0!!!\n", __FUNCTION__);
    return 0;
  }

  ComponentRegisterAddressLo = (UINT64)MmioRead32(RcrbAddress | PCICFG_SPACE_BAR0_OFFSET);

  if ((ComponentRegisterAddressLo & MEMORY_SPACE_INDICATOR) == 0) {
    if ((ComponentRegisterAddressLo & MEMORY_TYPE_WIDE) == 0) {
      // it is 32 address bit Memory Space
      ComponentRegisterAddress = (UINT64)(ComponentRegisterAddressLo & BASE_MEMORY_ADDRESS_MASK);
    } else {
      // it is 64 address bit Memory Space
      ComponentRegisterAddress = (UINT64)MmioRead32(RcrbAddress | PCICFG_SPACE_BAR1_OFFSET);
      ComponentRegisterAddress = (LShiftU64 (ComponentRegisterAddress, 32)) | (ComponentRegisterAddressLo & BASE_MEMORY_ADDRESS_MASK);
    }
  } else {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  [Error] This is IO space!!!\n");
  }

  return ComponentRegisterAddress;
}


UINT64
RasGetFullRegisterBlockAddress (
  IN       UINT32      RciepAddress,
  IN       UINT16      RegisterBlockPtr
)
{
  UINT64                            RegisterBlockAddress;
  UINT64                            RegisterBlockBase;
  UINT32                            RegisterBlockBaseLo;
  REGISTER_BLOCK_OFFSET_LOW         RegisterBlockOffsetLow;
  UINT32                            MemoryBaseOffset;
  PCI_ADDR                          PciAddr;
  UINT64                            PciSegAddr;

  PciAddr.AddressValue = RciepAddress;
  PciSegAddr = PCI_SEGMENT_LIB_ADDRESS (PciAddr.Address.Segment,
                                        PciAddr.Address.Bus,
                                        PciAddr.Address.Device,
                                        PciAddr.Address.Function,
                                        PciAddr.Address.Register);

  RegisterBlockBase = 0;
  RegisterBlockAddress = 0;
  RegisterBlockOffsetLow.Value = PciSegmentRead32(PciSegAddr | RegisterBlockPtr);

  //  Register BIR Defined encodings are:
  //  0 Base Address Register 10h
  //  1 Base Address Register 14h
  //  2 Base Address Register 18h
  //  3 Base Address Register 1Ch
  //  4 Base Address Register 20h
  //  5 Base Address Register 24h
  MemoryBaseOffset = RegisterBlockOffsetLow.Field.RegisterBIR * sizeof (UINT32);
  RegisterBlockBaseLo = PciSegmentRead32(PciSegAddr | (PCI_BASE_ADDRESS_REG + MemoryBaseOffset));
  if ((RegisterBlockBaseLo & MEMORY_SPACE_INDICATOR) == 0) {
    if ((RegisterBlockBaseLo & MEMORY_TYPE_WIDE) == 0) {
      // it is 32 address bit Memory Space
      RegisterBlockBase = RegisterBlockBaseLo & BASE_MEMORY_ADDRESS_MASK;
    } else {
      // it is 64 address bit Memory Space
      RegisterBlockBase = PciSegmentRead32(PciSegAddr | (PCI_BASE_ADDRESS_REG + MemoryBaseOffset + sizeof (UINT32)));
      RegisterBlockBase = (RegisterBlockBase << 32) | (RegisterBlockBaseLo & BASE_MEMORY_ADDRESS_MASK);
    }
  } else {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  [Error] This is IO space!!!\n");
  }

  // Get Regsiter Offset High
  RegisterBlockAddress = PciSegmentRead32((PciSegAddr | RegisterBlockPtr) + sizeof (UINT32));

  //Get full regsiter offset
  RegisterBlockAddress = (RegisterBlockAddress << 32) | (RegisterBlockOffsetLow.Value & REGISTER_BLOCK_OFFSET_LOW_MASK);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Register Block Base Addr: 0x%016lX, Offset: 0x%X\n", RegisterBlockBase, RegisterBlockAddress);


  return  (UINT64)(RegisterBlockAddress + RegisterBlockBase);
}


/**
 *  Get Register address from Register Locator from RCiEP
 *
 *
 * @param[in] RciepAddress          RCiEP PCI address
 * @param[in] RegisterBlockId       Register Block Identifier
 *                                    0 = Indicates the register block entry is empty and the Register BIR.
 *                                    1 = Component Registers.
 *                                    2 = BAR Virtualization ACL Registers.
 *                                    3 = CXL Memory Device Registers.
 * @retval                          Register address
 *
 *
 **/
UINT64
RasGetRciepRegisterBlockAddress (
  IN      UINT32      RciepAddress,
  IN      UINT8       RegisterBlockId
)
{
  UINT64                      RegisterBlockAddress;
  UINT16                      RegisterLocatorDvsecPtr;
  UINT16                      RegisterBlockPtr;
  UINT32                      RegisterBlockNum;
  UINT32                      RegBlkIndex;
  REGISTER_BLOCK_OFFSET_LOW   RegisterBlockOffsetLow;
  PCI_ADDR                    PciAddr;
  UINT64                      PciSegAddr;
  UINT64                      RegisterBlockAddressLo;
  UINT64                      UsRcrbAddr;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  %a for RCiEP: 0x%08X\n",
         __FUNCTION__,
         RciepAddress);

  PciAddr.AddressValue = RciepAddress;

  PciSegAddr = PCI_SEGMENT_LIB_ADDRESS (PciAddr.Address.Segment,
                                        PciAddr.Address.Bus,
                                        PciAddr.Address.Device,
                                        PciAddr.Address.Function,
                                        PciAddr.Address.Register);

  RegisterBlockAddress = 0;
  RegisterBlockAddressLo = 0;
  UsRcrbAddr = 0;
  RegisterLocatorDvsecPtr = RasFindCxlDvsecCapability (RciepAddress, RegisterLocatorDvsec);

  if (RegisterLocatorDvsecPtr != 0) {
    //CXL device 2.0
    RegisterBlockNum = RasGetRegisterBlockNumber(RciepAddress, RegisterLocatorDvsecPtr);
    RegisterBlockPtr = RegisterLocatorDvsecPtr + REGISTER_BLOCK1_OFFSET_LOW;
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Register Block Numbers: %d\n",RegisterBlockNum);

    for (RegBlkIndex = 0; RegBlkIndex < RegisterBlockNum; RegBlkIndex++) {
      RegisterBlockOffsetLow.Value = PciSegmentRead32(PciSegAddr + RegisterBlockPtr);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Register Block %d Register Offset Low: 0x%08X, Register Block Ptr: 0x%04X\n",
             RegBlkIndex + 1,
             RegisterBlockOffsetLow.Value,
             RegisterBlockPtr);

      if (RegisterBlockOffsetLow.Field.RegisterBlockIdentifier == RegisterBlockId) {
        RegisterBlockAddress = RasGetFullRegisterBlockAddress(RciepAddress, RegisterBlockPtr);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Register Block Identifier: %d, Register Address 0x%016lX\n",
                RegisterBlockId,
                RegisterBlockAddress);
        return RegisterBlockAddress;
      }
      RegisterBlockPtr = RegisterBlockPtr + sizeof (UINT64);
    }
  }

  if ((RegisterBlockAddress == 0) &&
      (RasGetPcieDeviceType (PciAddr) != PcieDeviceRootComplex /*PCIe root port, CXL 2.0 root port*/)) {
    //CXL device 1.1 or CXL device 2.0 but no component register in register locator DVSEC
    if (ComponenetRegistersBlkId == RegisterBlockId) {
      //Attempt 1 - try to find UsRcrb for this RCiEP
      if (RasGetUpRcrbAddrOfRciep (RciepAddress, &UsRcrbAddr, &RegisterBlockAddress)) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
          "  CXL Device 1.0 Component Register Address 0x%016lX (64 bit Upstream RCRB MEM BAR0)\n",
          RegisterBlockAddress);
        if (RegisterBlockAddress != 0) {
          return RegisterBlockAddress;
        }
      }
      //If code flow reaches this line, attempt 1 has failed.

      //Attempt 2 - try to find RCiEP's MEM BAR0
      if (RegisterBlockAddress == 0) {
        RegisterBlockAddressLo = PciSegmentRead32(PciSegAddr | PCICFG_SPACE_BAR0_OFFSET);
        if (((RegisterBlockAddressLo & BASE_MEMORY_ADDRESS_MASK) != 0) && (RegisterBlockAddressLo != 0xFFFFFFFF)) {
          if ((RegisterBlockAddressLo & MEMORY_SPACE_INDICATOR) != 0) {
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR,
              "  [Error] %a This is IO space!!! Addr: 0x%016lX\n", __FUNCTION__, RegisterBlockAddressLo);
            return 0;
          }
          if ((RegisterBlockAddressLo & MEMORY_TYPE_WIDE) == 0) {
            // 32 bit MMIO address
            RegisterBlockAddress = (UINT64)(RegisterBlockAddressLo & BASE_MEMORY_ADDRESS_MASK);
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
              "  CXL Device 1.0 Component Register Address 0x%016lX (32 bit)\n", RegisterBlockAddress);
          } else {
            // 64 bit MMIO address
            RegisterBlockAddress = PciSegmentRead32(PciSegAddr | PCICFG_SPACE_BAR1_OFFSET);
            RegisterBlockAddress = \
              (LShiftU64 (RegisterBlockAddress, 32)) | (RegisterBlockAddressLo & BASE_MEMORY_ADDRESS_MASK);
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
              "  CXL Device 1.0 Component Register Address 0x%016lX (64 bit)\n", RegisterBlockAddress);
          }
          return RegisterBlockAddress;
        }
      }
      //If code flow reaches this line, attempt 2 has failed.
    }
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Register Address NOT Found!!! Register Block Identifier: %d\n",
    RegisterBlockId);

  return 0;
}


/**
 *
 * Get RCRB AER ROOT_ERR_CMD register value
 *
 *
 *
 * @param[in] RCRB Address    RCRB address
 * @retval                    RCRB AER ROOT_ERR_CMD register value
 *
 **/
UINT32
RasRcrbRootErrorStatus (
  IN      UINT32      RcrbAddress
)
{
  UINT64    AerCapPtr;
  // Get Root Error Status Register
  AerCapPtr  = RasFindRcrbExtendedCapability(RcrbAddress, PCIE_EXT_AER_CAP_ID);

  if (AerCapPtr != 0) {
    return MmioRead32(RcrbAddress + AerCapPtr + PCIE_ROOT_STATUS_PTR);
  } else {
    return 0;
  }
}


/**
 *
 * Find RCRB PCI capability pointer
 *
 *
 *
 * @param[in] RCRB Address    RCRB address
 * @param[in] CapabilityId    PCI capability ID
 * @retval                    Register address of capability pointer
 *
 **/
UINT8
RasFindRcrbPciCapability (
  IN      UINT64              Address,
  IN      UINT8               CapabilityId
  )
{
  UINT8     CapabilityPtr;
  UINT8     CurrentCapabilityId;
  CapabilityPtr = PCI_CAPABILITY_REG;
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  %a - 0x%016lX\n", __FUNCTION__, Address);

  while ((CapabilityPtr != 0) && (CapabilityPtr != 0xff)) {
    CapabilityPtr = MmioRead8 (Address | CapabilityPtr);
    if (CapabilityPtr != 0) {
      CurrentCapabilityId = MmioRead8 (Address | CapabilityPtr);
      if (CurrentCapabilityId == CapabilityId) {
        return CapabilityPtr;
      }
      CapabilityPtr++;
    }
  }
  return  0;
}


/**
 * Find RCRB extended capability pointer
 *
 *
 *
 * @param[in] RCRB Address          RCRB address
 * @param[in] ExtendedCapabilityId  Extended PCIe capability ID
 * @retval                          Register address of extended capability pointer
 *
 **/
UINT16
RasFindRcrbExtendedCapability (
  IN      UINT64              RcrbAddress,
  IN      UINT16              ExtendedCapabilityId
  )
{
  UINT16  CapabilityPtr;
  PCIE_EXTENDED_CAPABILITY_HEADER   ExtendedCapabilityIdBlock;

  CapabilityPtr = 0;
  ExtendedCapabilityIdBlock.Value = MmioRead32 (RcrbAddress | CapabilityPtr);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[%a]ExtendedCapabilityIdBlock.Value: 0x%08X\n",
    __FUNCTION__, ExtendedCapabilityIdBlock.Value
  );
  if (ExtendedCapabilityIdBlock.Value == -1) {
    return 0;
  }
  //Check first DP RCRB PCIe extended capability header
  if ((ExtendedCapabilityIdBlock.Field.PcieExtCapNextCapOffet != 0) && ((UINT16)ExtendedCapabilityIdBlock.Field.PcieExtCapId == 0)) {
    do {
      CapabilityPtr = (UINT16)ExtendedCapabilityIdBlock.Field.PcieExtCapNextCapOffet;
      ExtendedCapabilityIdBlock.Value = MmioRead32 (RcrbAddress | CapabilityPtr);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "   - Capability at 0x%x with type 0x%x\n",
             CapabilityPtr,
             ExtendedCapabilityIdBlock.Field.PcieExtCapId
             );
      if (ExtendedCapabilityIdBlock.Field.PcieExtCapId == ExtendedCapabilityId) {
        return  CapabilityPtr;
      }
    } while ((CapabilityPtr != 0) && (CapabilityPtr != 0xfff));
  }
  return 0;
}


EFI_STATUS
GetRcrbPcieCapStructure(
  IN       UINT64         RcrbAddress,
     OUT   CAP_STRUCTURE  *CapabilityStructure
)
{
  EFI_STATUS  Status = EFI_UNSUPPORTED;
  UINT32      Index;
  UINT32      MaxIndex = 0;
  UINT16      PcieCapPtr;

  PcieCapPtr = RasFindRcrbPciCapability (RcrbAddress, PCIE_CAP_ID);
  if (PcieCapPtr != 0) {
    // Check PCIE CAP version
    // This structure is used to report the 36-byte PCIe 1.1 Capability Structure
    // (See Figure 7-9 of the PCI Express Base Specification, Rev 1.1) with the last 24 bytes padded.
    //
    // This structure is also used to report the 60-byte PCIe 2.0 Capability Structure
    // (See Figure 7-9 of the PCI Express 2.0 Base Specification.)
    MaxIndex = ((MmioRead16 (RcrbAddress + PcieCapPtr + PCIE_CAP_REGISTER) & PCIE_CAP_VESION_MASK) < 2)? 9 : 15;

    for (Index = 0; Index < MaxIndex; Index++) {
      CapabilityStructure->CapabilityData[Index] = MmioRead32 (RcrbAddress + (PcieCapPtr + ((sizeof (UINT32)) * Index)));
    }
    Status = EFI_SUCCESS;
  }
  return Status;
}


EFI_STATUS
FillRcrbPcieErrorSection (
  IN       UINT64               RcrbAddress,
  IN       PCI_ADDR             RcrbBdf,
  IN       UINT32               SeverityType,
     OUT   PCIE_ERROR_SECTION   *PcieErrorSection
)
{
  UINT8                         PortType;
  UINT32                        MaxIndex = 0;
  UINT32                        Index;
  UINT16                        AerCapPtr;
  UINT8                         PcieCapPtr;


  AerCapPtr  = RasFindRcrbExtendedCapability(RcrbAddress, PCIE_EXT_AER_CAP_ID);
  PcieCapPtr = RasFindRcrbPciCapability (RcrbAddress, PCIE_CAP_ID);

  if ((AerCapPtr == 0) && (PcieCapPtr == 0)) {
    return EFI_UNSUPPORTED;
  }

  PortType = MmioRead8 (RcrbAddress | (PcieCapPtr + PCIE_CAP_REGISTER));

  PcieErrorSection->Validation.Value = 0xEF;
  PcieErrorSection->PortType = (UINT32)(PortType >> 4);
  PcieErrorSection->Revision = 0x02;
  PcieErrorSection->CommandStatus = MmioRead32 (RcrbAddress + PCI_COMMAND_REG);

  PcieErrorSection->DeviceId.VendorId = MmioRead16 (RcrbAddress + PCI_VENDORID_REG);
  PcieErrorSection->DeviceId.DeviceId = MmioRead16 (RcrbAddress + PCI_DEVICEID_REG);
  PcieErrorSection->DeviceId.ClassCode[0] = MmioRead8 (RcrbAddress + PCI_CLASS_CODE_0_REG);
  PcieErrorSection->DeviceId.ClassCode[1] = MmioRead8 (RcrbAddress + PCI_CLASS_CODE_1_REG);
  PcieErrorSection->DeviceId.ClassCode[2] = MmioRead8 (RcrbAddress + PCI_CLASS_CODE_2_REG);

  // Segment information need update for multi segment support.
  PcieErrorSection->DeviceId.Function = (UINT8)RcrbBdf.Address.Function;
  PcieErrorSection->DeviceId.Device = (UINT8)RcrbBdf.Address.Device;
  PcieErrorSection->DeviceId.Segment = (UINT8)RcrbBdf.Address.Segment;

  // RCRB Bus number information
  PcieErrorSection->DeviceId.PrimaryBus = (UINT8)RcrbBdf.Address.Bus; //MmioRead8 (RcrbAddress + PCI_PRIMARY_BUS_REG);
  PcieErrorSection->DeviceId.SecondaryBus = MmioRead8 (RcrbAddress + PCI_SECONDARY_BUS_REG);

  PcieErrorSection->BridgeCtrlStatus = (UINT32) (MmioRead16 (RcrbAddress + PCI_BRIDGE_CONTROL_REG)) << 16 | (UINT32) (MmioRead16 (RcrbAddress + PCI_SEC_STATUS_REG));

  PcieErrorSection->DeviceId.Slot = (UINT16)((MmioRead32 (RcrbAddress + PcieCapPtr + PCIE_SLOT_CAP_REGISTER) >> PCIE_SLOT_NUMBER_SHIFT) << 3);
  GetRcrbPcieCapStructure(RcrbAddress, &PcieErrorSection->CapabilityStructure);

  // Check AER CAP version
  MaxIndex = ((MmioRead16 (RcrbAddress + AerCapPtr + 2) & 0xF) < 2)? 14 : 18;  //14 = (0x38/4), 18 = (0x48/4)
  for (Index = 0; Index < MaxIndex; Index++) {
    PcieErrorSection->AerInfo.AerInfoData[Index] = MmioRead32 (RcrbAddress + (AerCapPtr + (4 * Index)));
  }

  return EFI_SUCCESS;
}


EFI_STATUS
RasRcrbPcieStsClr (
  IN       UINT64    RcrbAddress,
  IN       UINT32    RetryCnt
  )
{
  UINT8         PcieCapPtr;
  UINT16        AerCapPtr;
  UINT32        PcieUncorrStatus;
  UINT32        PcieCorrStatus;
  UINT32        PcieRootStatus;
  UINT16        PcieDevStatus;
  UINT32        RasRetryCounter;

  if (RcrbAddress == 0) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS][Error] RasRcrbPcieStsClr - RcrbAddress is 0!!!\n", RcrbAddress);
    return EFI_NOT_FOUND;
  }

  AerCapPtr = RasFindRcrbExtendedCapability (RcrbAddress, PCIE_EXT_AER_CAP_ID);
  PcieCapPtr = RasFindRcrbPciCapability (RcrbAddress, PCIE_CAP_ID);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]RCRB ClrSts @ Address: 0x%016lX\n", RcrbAddress);
  //Clear Status register
  RasRetryCounter = RetryCnt;
  if (AerCapPtr != 0) {
    PcieUncorrStatus = MmioRead32 (RcrbAddress + AerCapPtr + PCIE_UNCORR_STATUS_PTR);
    PcieCorrStatus = MmioRead32 (RcrbAddress + AerCapPtr + PCIE_CORR_STATUS_PTR);
    PcieRootStatus = MmioRead32 (RcrbAddress + AerCapPtr + PCIE_ROOT_STATUS_PTR);
    while ((PcieUncorrStatus != 0) || (PcieCorrStatus != 0) || (PcieRootStatus != 0)) {
      if (RasRetry (&RasRetryCounter)) {
        break;
      }
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]  RCRB PCIE UnCorr Error Status : 0x%08x\n", PcieUncorrStatus);
      MmioWrite32(RcrbAddress + AerCapPtr + PCIE_UNCORR_STATUS_PTR, PcieUncorrStatus);

      PcieUncorrStatus = MmioRead32 (RcrbAddress + AerCapPtr + PCIE_UNCORR_STATUS_PTR);

      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]  RCRB PCIE Corr Error Status : 0x%08x\n", PcieCorrStatus);
      MmioWrite32 (RcrbAddress + AerCapPtr + PCIE_CORR_STATUS_PTR, PcieCorrStatus);
      PcieCorrStatus = MmioRead32 (RcrbAddress + AerCapPtr + PCIE_CORR_STATUS_PTR);

      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]  RCRB PCIE Root Error Status : 0x%08x\n", PcieRootStatus);
      MmioWrite32 (RcrbAddress + AerCapPtr + PCIE_ROOT_STATUS_PTR, PcieRootStatus);
      PcieRootStatus = MmioRead32 (RcrbAddress + AerCapPtr + PCIE_ROOT_STATUS_PTR);
    }
  }

  RasRetryCounter = RetryCnt;
  if (PcieCapPtr != 0) {
    PcieDevStatus = MmioRead16 (RcrbAddress + PcieCapPtr + PCIE_DEVICE_STATUS_PTR);
    while ((PcieDevStatus & 0xF) != 0) {
      if (RasRetry (&RasRetryCounter)) {
        break;
      }
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]  RCRB PCIE Device Status : 0x%08x\n", PcieDevStatus);
      MmioWrite16(RcrbAddress + PcieCapPtr + PCIE_DEVICE_STATUS_PTR, PcieDevStatus);
      PcieDevStatus = MmioRead16 (RcrbAddress + PcieCapPtr + PCIE_DEVICE_STATUS_PTR);
    }
  }

  return EFI_SUCCESS;
}


/**
 * Scan all RCEC subordinate buses
 *
 *
 * @param[in]     Rcec            RCEC address
 * @param[in,out] ScanData        Scan configuration data
 *
 **/
VOID
RasRcecScanSecondaryBus (
  IN       PCI_ADDR             Rcec,
  IN OUT   RAS_PCI_SCAN_DATA    *ScanData
  )
{
  PCI_ADDR  StartRange;
  PCI_ADDR  EndRange;
  UINT16    RcecCapPtr;
  RCEC_ASSOCIATED_BUS_NUMBER_REG  RcecAssBusNum;
  UINT8     SecondaryBus;
  UINT8     SubordinateBus;
  UINT64    PciSegAddr;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  %a at RCEC Seg: 0x%X, Bus: 0x%X, Devcie: 0x%X, Function 0x%X\n",
         __FUNCTION__,
         Rcec.Address.Segment,
         Rcec.Address.Bus,
         Rcec.Address.Device,
         Rcec.Address.Function);

  PciSegAddr = PCI_SEGMENT_LIB_ADDRESS (Rcec.Address.Segment,
                                        Rcec.Address.Bus,
                                        Rcec.Address.Device,
                                        Rcec.Address.Function,
                                        Rcec.Address.Register);

  RcecCapPtr = RasFindPcieExtendedCapability (Rcec.AddressValue, PCIE_EXT_RCEC_CAP_ID, 0xFFFF);

  if (RcecCapPtr == 0) {
    //Not Root Complex Event Collector
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Not Root Complex Event Collector\n");
    return;
  }
  RcecAssBusNum.Value = PciSegmentRead32((PciSegAddr + RcecCapPtr) + RCEC_ASSOCIATED_BUS_NUMBER_PTR);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  RCEC Cap Ptr: 0x%x, RCEC Associate Bus Number Register reading: 0x%08x\n",
          RcecCapPtr,
          RcecAssBusNum.Value);

  SecondaryBus = RcecAssBusNum.Field.RcecNextBus;
  SubordinateBus = RcecAssBusNum.Field.RcecLastBus;
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Secondry Bus Number: 0x%x, Subordinate Bus Number: 0x%X\n", SecondaryBus, SubordinateBus);

  if (SecondaryBus != 0) {
    StartRange.AddressValue = MAKE_SBDFO (Rcec.Address.Segment, SecondaryBus, 0, 0, 0);
    EndRange.AddressValue = MAKE_SBDFO (Rcec.Address.Segment, SubordinateBus, 0x1f, 0x7, 0);
    RasPciScan (StartRange, EndRange, ScanData);
  }
}


EFI_STATUS
CxlGetMemoryDeviceStatus(
  IN       UINT64                   CxlMemDeviceRegAddr,
     OUT   CXL_DEVICE_EVENT_STATUS  *CxlDeviceEventStatus
  )
{
  EFI_STATUS          Status = EFI_SUCCESS;
  UINT32              CxlDevCapLength;
  UINT32              CxlDevCapOffset;

  // Check CXL component event
  CxlDevCapOffset = RasGetCxlDeviceCapabilityPointer (CxlMemDeviceRegAddr, DeviceStatusRegisterCapId, &CxlDevCapLength);

  if ((CxlDevCapOffset != 0) && (CxlDevCapLength != 0)) {
    CxlDeviceEventStatus->Value = MmioRead32(CxlMemDeviceRegAddr + CxlDevCapOffset);
    Status = EFI_SUCCESS;
  } else {
    CxlDeviceEventStatus->Value = 0;
    Status = EFI_UNSUPPORTED;
  }

  return Status;
}


EFI_STATUS
CxlDpErrCheck(
  IN       CXL_DP_ENTRY       *CxlDpEntry,
  IN       RAS_ERR_LOG_DATA   *ErrLogData
  )
{
  EFI_STATUS          Status = EFI_SUCCESS;
  PCI_ADDR            DummyPci;
  UINT16              AerCapPtr;
  UINT16              CxlRasCapPtr;
  UINT64              RcrbComponenetRegsiterAddress;
  UINT64              CxlCacheMemRegisterAddr;

  CXL_ERROR_LOG_DATA  *CxlErrorLogData;


  CxlErrorLogData =  (CXL_ERROR_LOG_DATA*)ErrLogData->Buffer;

  CxlErrorLogData->CxlAgentType = CXL_AGENT_TYPE_DOWNSTREAM;
  CxlErrorLogData->CxlAgentAddress = CxlDpEntry->RcrbAddr;
  CxlErrorLogData->RcecAddress = CxlDpEntry->RcecPciAddr;
  DummyPci.AddressValue = 0;

  //Get Components register address
  RcrbComponenetRegsiterAddress = RasGetDpComponentRegisterAddress (CxlDpEntry->RcrbAddr);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  CXL RCRB Component Register Address 0x%016lX\n",
    RcrbComponenetRegsiterAddress);
  CxlErrorLogData->CxlComponentRegAddr = RcrbComponenetRegsiterAddress;
  CxlErrorLogData->ErrorLogType = 0;

  //Check IO error
  AerCapPtr = RasFindRcrbExtendedCapability(CxlDpEntry->RcrbAddr, PCIE_EXT_AER_CAP_ID);
  if (0 != AerCapPtr) {
    if (RcrbAerErrCheck (CxlDpEntry->RcrbAddr, AerCapPtr)) {
      CxlErrorLogData->ErrorLogType = CXL_IO_ERROR;
    }
  }

  //Search RAS Capability structure Pointer
  CxlCacheMemRegisterAddr = RcrbComponenetRegsiterAddress + CXL_CACHE_MEM_REGISTERS_OFFSET;
  CxlRasCapPtr = RasGetCxlCapabilityPointer (CxlCacheMemRegisterAddr, CxlRasCapabilityId);

  //Check CXL protocol error
  if (0 != CxlRasCapPtr) {
    if (CxlRasCapErrCheck(CxlCacheMemRegisterAddr, CxlRasCapPtr)) {
      CxlErrorLogData->ErrorLogType |= CXL_CACHE_MEM_ERROR;
    }
  }

  //Log Errors
  if (0 != CxlErrorLogData->ErrorLogType) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Log CXL RCRB Error. Error Log Type: 0x%x\n", CxlErrorLogData->ErrorLogType);
    ErrLogData->RasLogCallback(DummyPci, ErrLogData);
  }

  // Clear status registers before enable error report.
  RasRcrbPcieStsClr(CxlDpEntry->RcrbAddr, 3);

  // Clear RCRB CXL RAS capability registers
  CxlRasCapStsClear (CxlErrorLogData->CxlComponentRegAddr, 3);

  return Status;
}


VOID
CxlDevInfoFill(
  IN       UINT32             CxlDeviceAddr,
  IN       CXL_ERROR_LOG_DATA *CxlErrorLogData
)
{
  PCI_ADDR                  PciAddr;
  CXL_AGENT_ADDRESS_DEVICE  CxlAgentAddrDev;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  %a for CXL Device: 0x%08X\n",
         __FUNCTION__,
         CxlDeviceAddr);

  PciAddr.AddressValue = CxlDeviceAddr;

  // Prepare CXL Error Log Data structure
  CxlErrorLogData->CxlAgentType = CXL_AGENT_TYPE_DEVICE;
  CxlAgentAddrDev.Field.FunctionNumber = PciAddr.Address.Function;
  CxlAgentAddrDev.Field.DeviceNumber = PciAddr.Address.Device;
  CxlAgentAddrDev.Field.BusNumber = PciAddr.Address.Bus;
  CxlAgentAddrDev.Field.Segment = PciAddr.Address.Segment;
  CxlErrorLogData->CxlAgentAddress = CxlAgentAddrDev.Value;

  CxlErrorLogData->CxlComponentRegAddr = RasGetRciepRegisterBlockAddress (PciAddr.AddressValue, ComponenetRegistersBlkId);
  CxlErrorLogData->CxlMemDeviceRegAddr = RasGetRciepRegisterBlockAddress (PciAddr.AddressValue, CxlMemoryDeviceRegistersBlkId);
}


EFI_STATUS
CxlDevIoCacheMemErrCheck(
  IN       CXL_RCIEP_ENTRY    *CxlRciepEntry,
  IN       RAS_ERR_LOG_DATA   *ErrLogData
  )
{
  EFI_STATUS          Status = EFI_SUCCESS;
  UINT16              AerCapPtr;
  UINT16              CxlRasCapPtr;
  PCI_ADDR            Rciep;
  ROOT_ERR_STS_REG    RootErrSts;
  UINT64              CxlCacheMemRegisterAddr;
  UINT64              RciepComponenetRegsiterAddress;
  CXL_ERROR_LOG_DATA  *CxlErrorLogData;
  EFI_STATUS          PcieErrorLogStatus;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a - Start\n", __FUNCTION__);

  Rciep.AddressValue = CxlRciepEntry->DevAddr;

  // Prepare CXL Error Log Data structure
  CxlErrorLogData = (CXL_ERROR_LOG_DATA*)ErrLogData->Buffer;
  CxlErrorLogData->RcecAddress = CxlRciepEntry->RcecPciAddr;
  CxlErrorLogData->ErrorLogType = 0;

  CxlDevInfoFill (Rciep.AddressValue, CxlErrorLogData);

  RciepComponenetRegsiterAddress = CxlErrorLogData->CxlComponentRegAddr;

  //Check CXL io error
  PcieErrorLogStatus = EFI_NOT_FOUND;
  if ((CxlRciepEntry->DevType != PCieDeviceRCiEP) &&
      (CxlRciepEntry->DevType != PcieDeviceEndPoint)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  Not RCiEP!! Address: 0x%08x, Type: 0x%x\n", CxlRciepEntry->DevAddr, CxlRciepEntry->DevType);
    return Status;
  }

  AerCapPtr = RasFindPcieExtendedCapability (Rciep.AddressValue, PCIE_EXT_AER_CAP_ID, 0xFFFF);

  if (AerCapPtr != 0) {
    if (CxlErrorLogData->RcecAddress != 0) { //RcecAddress is 0 in CXL 2.0
      //CXL 1.1
      RootErrSts.Value = RasPcieRootErrorStatus (CxlErrorLogData->RcecAddress);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  RCEC Address: 0x%08x, Root Error Status: 0x%x\n",
              CxlErrorLogData->RcecAddress,
              RootErrSts.Value);
    } else {
      //CXL 2.0
      RootErrSts.Value = CxlErrorLogData->Cxl2p0RPPcieRootStatus;
    }

    if ((RootErrSts.Value & ROOT_ERROR_STATUS_MASK) != 0) {
      // Check io error
      if (AerErrCheck (Rciep, AerCapPtr)){
        // Log IO error
        CxlErrorLogData->ErrorLogType = CXL_IO_ERROR;
      }
    }
  }

  // Search RAS Capability structure Pointer
  if (RciepComponenetRegsiterAddress != 0) {
    CxlCacheMemRegisterAddr = RciepComponenetRegsiterAddress + CXL_CACHE_MEM_REGISTERS_OFFSET;
    CxlRasCapPtr = RasGetCxlCapabilityPointer(CxlCacheMemRegisterAddr, CxlRasCapabilityId);

    // Check CXL protocol error
    if (0 != CxlRasCapPtr) {
      if (CxlRasCapErrCheck(CxlCacheMemRegisterAddr, CxlRasCapPtr)) {
        CxlErrorLogData->ErrorLogType |= CXL_CACHE_MEM_ERROR;
      }
    }

    //Log Error
    if (0 != CxlErrorLogData->ErrorLogType) {
      PcieErrorLogStatus = ErrLogData->RasLogCallback(Rciep, ErrLogData);
    }
  } else {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "  No Component Address Found !!!\n");
  }

  // Clear AER status registers
  RasPcieStsClr (Rciep, 3);

  //Clear Device CXL RAS capability registers
  if (RciepComponenetRegsiterAddress != 0) {
    CxlRasCapStsClear (RciepComponenetRegsiterAddress, 3);
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a - End, PcieErrorLog Status: %r\n", __FUNCTION__, PcieErrorLogStatus);

  return PcieErrorLogStatus;
}


EFI_STATUS
CxlDevComponentErrCheck(
  IN       CXL_RCIEP_ENTRY    *CxlRciepEntry,
  IN       RAS_ERR_LOG_DATA   *ErrLogData
  )
{
  EFI_STATUS                Status = EFI_SUCCESS;
  PCI_ADDR                  Rciep;
  UINT64                    RciepCxlMemDeviceRegAddr;
  CXL_ERROR_LOG_DATA        *CxlErrorLogData;
  CXL_DEVICE_EVENT_STATUS   CxlDeviceEventStatus;
  EFI_STATUS                PcieErrorLogStatus;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a - Start\n", __FUNCTION__);

  PcieErrorLogStatus = EFI_NOT_FOUND;
  Rciep.AddressValue = CxlRciepEntry->DevAddr;

  // Prepare CXL Error Log Data structure
  CxlErrorLogData =  (CXL_ERROR_LOG_DATA*)ErrLogData->Buffer;
  CxlErrorLogData->RcecAddress = CxlRciepEntry->RcecPciAddr;
  CxlErrorLogData->ErrorLogType = 0;

  CxlDevInfoFill (Rciep.AddressValue, CxlErrorLogData);

  RciepCxlMemDeviceRegAddr = CxlErrorLogData->CxlMemDeviceRegAddr;

  if (RciepCxlMemDeviceRegAddr != 0) {  //1dfa:01e0 returns 0.
    // Check CXL component event
    Status = CxlGetMemoryDeviceStatus (RciepCxlMemDeviceRegAddr, &CxlDeviceEventStatus);
    if (!EFI_ERROR (Status) && ((CxlDeviceEventStatus.Value & EVENT_STATUS_MASK) != 0)) {
      CxlErrorLogData->ErrorLogType = CXL_COMPONENT_EVENT;
    }
  }

  //Log Error
  if (0 != CxlErrorLogData->ErrorLogType) {
    PcieErrorLogStatus = ErrLogData->RasLogCallback(Rciep, ErrLogData);
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a - End, PcieErrorLog Status: %r\n", __FUNCTION__, PcieErrorLogStatus);

  return PcieErrorLogStatus;
}


EFI_STATUS
CxlDpErrStsCheck(
  IN OUT   RCEC_PROFILE         *RcecProfile,
  IN       RAS_ERR_LOG_DATA     *ErrLogData
)
{
  EFI_STATUS          Status = EFI_SUCCESS;
  CXL_ERROR_LOG_DATA  CxlErrorLogData;
  UINT8               Index;
  LIST_ENTRY          *Node;
  CXL_DP_ENTRY        *CxlDpEntry;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a - Start\n", __FUNCTION__);

  Node = NULL;
  // MPIO Interrupt Playload is invalid for down stram port.
  CxlErrorLogData.MpioIntrPayload = CXL_MPIO_INTR_PL_INVALID;

  ErrLogData->Buffer = &CxlErrorLogData;
  //Check PCI devices under root port

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]Cxl Down Stream port count : %d\n", RcecProfile->DpCnt);
  if (RcecProfile->DpCnt != 0) {
    //Search Down stream port error
    for (Index = 0; Index < RcecProfile->DpCnt; Index++) {
      if (Index == 0) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Get First Node, Head Forward Link Addr: 0x%08x\n",RcecProfile->RcrbLinkList.ForwardLink);
        Node = GetFirstNode(&RcecProfile->RcrbLinkList);
      } else {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Get Next Node, Current Node Address: 0x%08x\n",(UINTN)Node);
        Node = GetNextNode(&RcecProfile->RcrbLinkList, Node);
      }
      CxlDpEntry = (CXL_DP_ENTRY*)Node;
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  RCRB Address 0x%016lX\n", CxlDpEntry->RcrbAddr);

      //Check and Log Down stream port io and protocol error
      Status = CxlDpErrCheck(CxlDpEntry, ErrLogData);
    }
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a - End\n", __FUNCTION__);

  return Status;
}


EFI_STATUS
CxlDevErrStsCheck(
  IN OUT   RCEC_PROFILE         *RcecProfile,
  IN       RAS_ERR_LOG_DATA     *ErrLogData,
  IN       UINT8                MpioIntrPayload,
  IN       UINT8                ErrTypeCheck
)
{
  EFI_STATUS          Status = EFI_SUCCESS;
  CXL_ERROR_LOG_DATA  CxlErrorLogData;
  UINT8               Index;
  LIST_ENTRY          *Node;
  CXL_RCIEP_ENTRY     *CxlRciepEntry;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a - Start\n", __FUNCTION__);

  Node = NULL;
  CxlErrorLogData.MpioIntrPayload = MpioIntrPayload;
  ErrLogData->Buffer = &CxlErrorLogData;
  //Check PCI devices under root port

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]Cxl RCiEP count : %d\n", RcecProfile->RciepCnt);
  if (RcecProfile->RciepCnt != 0) {
    //Search End Point device error
    for (Index = 0; Index < RcecProfile->RciepCnt; Index++) {
      if (Index == 0) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Get First Node, Head Forward Link Addr: 0x%08x\n",RcecProfile->RciepLinkList.ForwardLink);
        Node = GetFirstNode(&RcecProfile->RciepLinkList);
      } else {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Get Next Node, Current Node Address: 0x%08x\n",(UINTN)Node);
        Node = GetNextNode(&RcecProfile->RciepLinkList, Node);
      }
      CxlRciepEntry = (CXL_RCIEP_ENTRY*)Node;
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  RCiEP Address: 0x%08x, DevType: 0x%x\n",
              CxlRciepEntry->DevAddr,
              CxlRciepEntry->DevType);

      if ((ErrTypeCheck == CXL_DEV_CHK_ALL_ERROR_TYPES) || (ErrTypeCheck == CXL_DEV_CHK_IO_CACHE_MEM_ERROR_ONLY)) {
        //Check and Log device io and protocol error
        Status = CxlDevIoCacheMemErrCheck(CxlRciepEntry, ErrLogData);
      }
      if ((ErrTypeCheck == CXL_DEV_CHK_ALL_ERROR_TYPES) || (ErrTypeCheck == CXL_DEV_CHK_COMPONENT_ERROR_ONLY)) {
        //Check and Log device Component error
        Status = CxlDevComponentErrCheck(CxlRciepEntry, ErrLogData);
      }
    }
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a - End\n", __FUNCTION__);

  return Status;
}

STATIC
UINT8
GetIosInstanceIdFromFabricId (
  IN     UINT32 FabricId
  )
{
  UINT8                                                     Index;
  FABRIC_BLOCK_INSTANCE_INFORMATION3__IOS_REGISTER          InstanceInfo3;

  //IOS Fabric ID: 0x20 => IOS Instance ID: 0x28
  //IOS Fabric ID: 0x21 => IOS Instance ID: 0x29
  //IOS Fabric ID: 0x22 => IOS Instance ID: 0x2A
  //IOS Fabric ID: 0x23 => IOS Instance ID: 0x2B
  //IOS Fabric ID: 0x24 => IOS Instance ID: 0x2C
  //IOS Fabric ID: 0x25 => IOS Instance ID: 0x2D
  //IOS Fabric ID: 0x26 => IOS Instance ID: 0x2E
  //IOS Fabric ID: 0x27 => IOS Instance ID: 0x2F
  for (Index = 0; Index < BRH_MAX_IOMS_PER_DIE; Index++) {
    InstanceInfo3.Value = FabricRegisterAccRead (0, 0, (((DF_FABRICBLOCKINSTANCEINFORMATION3_IOS) >> 12) & 0x7), (DF_FABRICBLOCKINSTANCEINFORMATION3_IOS & 0xFFF), BRH_IOS0_INSTANCE_ID + Index);
    if (InstanceInfo3.Field.BlockFabricID == FabricId) {
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS - CXL] %a Given Fabric ID: 0x%x => IOS Instance ID: 0x%x\n",
        __FUNCTION__, FabricId,  (BRH_IOS0_INSTANCE_ID + Index));
      return (UINT8) (BRH_IOS0_INSTANCE_ID + Index);
    }
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS - CXL] %a Failed to get IOS instance id from given FabricId 0x%x\n", __FUNCTION__, FabricId);

  return 0xFF;
}

BOOLEAN
RasGetUpRcrbAddrOfRciep (
  IN      UINT32  RciepAddr,
  IN OUT  UINT64  *RciepUpRcrbAddr,
  IN OUT  UINT64  *RciepUpRcrbMemBar0
  )
{
  PCI_ADDR                      RciepPciAddr;
  BOOLEAN                       FoundCxlRootOfRciep;
  UINT8                         Index;
  CFG_BASE_ADDRESS_REGISTER     CfgBaseAddrValue;
  CFG_LIMIT_ADDRESS_REGISTER    CfgLimitAddrValue;
  UINT8                         IosNum;
  UINT32                        TargetCxlSeg;
  UINT32                        TargetCxlBus;
  UINT8                         TargetCxlNbio;
  UINT8                         TargetCxlIohub;
  UINT32                        SmnBase;
  UINT32                        SmnAddr;
  UINT32                        RcecAssociNextbus;
  UINT32                        RcecAssociLastbus;
  UINT8                         Port;
  CXL_RCRB_BASE_ADDR_LO_REG     CxlRcrbBaseAddrLo;
  UINT32                        CxlRcrbBaseAddrHi;
  UINT64                        RcrbAddress;
  UINT8                         SecondaryBusNum;
  UINT8                         SubordinateBusNum;
  UINT32                        RegOffset;
  UINTN                         DfCfgNbioBaseAddress;
  UINTN                         DfCfgNbioLimitAddress;
  UINTN                         DfCfgAddrCtrlFunction;
  UINTN                         DfCfgAddrCtrlOffset;
  UINT8                         TmpIohub;
  UINT32                        RegEax;
  UINT64                        SocFamilyID;
  UINT8                         CpuModStep;
  UINT8                         MaxInstCnt;

  RegEax = 0;
  AsmCpuid (0x80000001, &RegEax, NULL, NULL, NULL);
  SocFamilyID = (RegEax & RAW_FAMILY_ID_MASK);
  CpuModStep = 0;
  if ((SocFamilyID == F1A_BRH_RAW_ID) || (SocFamilyID == F1A_BRHD_RAW_ID)) {
    CpuModStep = (RegEax & (CPUID_BASE_MODEL_MASK | CPUID_STEPPING_MASK));
  }

  //Determine total number of instances of DF::CfgBaseAddress Register
  if (CpuModStep == 0x00 /*A0*/) {
    MaxInstCnt = 8;
  } else {
    MaxInstCnt = 16;
  }

  RciepPciAddr.AddressValue = RciepAddr;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] %a - RCiEP@ Seg:0x%02X Bus:0x%02X Dev:0x%02X Fnc:0x%02X\n",
    __FUNCTION__, RciepPciAddr.Address.Segment, RciepPciAddr.Address.Bus, RciepPciAddr.Address.Device, RciepPciAddr.Address.Function);

  //Try to get the segment/bus and nbio/iohub numbers of RCEC from the given RCiEP by the IOM0 instance of the DF::CfgBaseAddress register
  FoundCxlRootOfRciep = FALSE;
  for (Index = 0; (Index < MaxInstCnt) && (!FoundCxlRootOfRciep); Index++) {
    DfCfgNbioBaseAddress = DF_CFGBASEADDRESS + sizeof (UINT64) * Index;
    DfCfgAddrCtrlFunction = (DfCfgNbioBaseAddress >> 12) & 0x7;
    DfCfgAddrCtrlOffset = DfCfgNbioBaseAddress & 0xFFF;
    CfgBaseAddrValue.Value = FabricRegisterAccRead (0, 0, DfCfgAddrCtrlFunction, DfCfgAddrCtrlOffset, BrhIomsInstanceIds[0]);
    // FWDEV-61901: Clear the WA for Turin C0 to set CfgBase.Field.SegmentNum as 0x80 for local segment(s)
    CfgBaseAddrValue.Field.SegmentNum = BRH_GET_SEGMENT_NUMBER (CfgBaseAddrValue);
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] CfgBaseAddrValue = 0x%08X.\n", CfgBaseAddrValue.Value);
    if ((CfgBaseAddrValue.Value & 3) != 3) { //3 = Bit1: WE, Bit0: RE
      continue; //skip if this CfgBaseAddrValueess register is not enabled.
    }
    if (CfgBaseAddrValue.Field.SegmentNum != RciepPciAddr.Address.Segment) {
      continue;
    }
    DfCfgNbioLimitAddress = DF_CFGLIMITADDRESS + sizeof (UINT64) * Index;
    DfCfgAddrCtrlFunction = (DfCfgNbioLimitAddress >> 12) & 0x7;
    DfCfgAddrCtrlOffset = DfCfgNbioLimitAddress & 0xFFF;
    CfgLimitAddrValue.Value = FabricRegisterAccRead (0, 0, DfCfgAddrCtrlFunction, DfCfgAddrCtrlOffset, BrhIomsInstanceIds[0]);;
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] CfgLimitAddrValue = 0x%08X.\n", CfgLimitAddrValue.Value);

    IosNum = GetIosInstanceIdFromFabricId (CfgLimitAddrValue.Field.DstFabricID & ~BIT7 /*mask remote die indicator*/) & 0x7;

    TargetCxlNbio = (UINT8)((IosNum >> 2) & BIT0);  //Nbio = IosNum[BIT2];
    TmpIohub = (IosNum & (BIT1|BIT0));
    TargetCxlIohub = (IosNum & BIT0) ? (TmpIohub ^ BIT1) : TmpIohub;
    TargetCxlSeg = CfgBaseAddrValue.Field.SegmentNum;
    TargetCxlBus = CfgBaseAddrValue.Field.BusNumBase;

    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] Scan [Socket:%d, Seg:0x%02X, Bus:0x%02X. Nbio:%d, Iohub:%d] for RCEC.\n",
      ((CfgLimitAddrValue.Field.DstFabricID & BIT7) >> 7), TargetCxlSeg, TargetCxlBus, TargetCxlNbio, TargetCxlIohub);

    SmnBase = ((TargetCxlIohub & BIT0) == BIT0) ? NBIO0_IOHUB1_RCEC_PCIE_RCECEPA_ASSOCI_NEXTBUS : NBIO0_IOHUB0_RCEC_PCIE_RCECEPA_ASSOCI_NEXTBUS;
    SmnAddr = (SmnBase & ~(BIT1 | BIT0) /*for alignment*/) + (IOHC_SMN_ADDR_OFFSET * (TargetCxlIohub >> 1)) + NBIO_SMN_ADDR_OFFSET * TargetCxlNbio;
    RasSmnRead (SMN_SEG_BUS (TargetCxlSeg, TargetCxlBus), SmnAddr, &RcecAssociNextbus);
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] SMN Addr/Data: 0x%08X/0x%08X\n", SmnAddr, RcecAssociNextbus);

    RcecAssociLastbus = (RcecAssociNextbus >> 16) & 0xFF;
    RcecAssociNextbus = (RcecAssociNextbus >> 8)  & 0xFF;
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]   => RcecAssociNextbus:0x%02X, RcecAssociLastbus:0x%02X.\n", RcecAssociNextbus, RcecAssociLastbus);

    if ((RciepPciAddr.Address.Bus >= RcecAssociNextbus) && (RciepPciAddr.Address.Bus <= RcecAssociLastbus)) {
      //The Seg/bus number of the RCEC for the given RCiEP was found
      FoundCxlRootOfRciep = TRUE;
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] A corresponding RCEC has been found for the given RCiEP.\n");
    }
  }

  if (!FoundCxlRootOfRciep) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] The RCEC for the given RCiEP: 0x%08X could not be found.\n", RciepAddr);
    return FALSE;
  }

  SmnBase = ((TargetCxlIohub & BIT0) == BIT0) ? \
    (NBIO0_IOHUB1_BASE_ADDR + NB_PCIE2_PORTA_CXL_RCRB_BASE_ADDR_LO) : (NBIO0_IOHUB0_BASE_ADDR + NB_PCIE0_PORTA_CXL_RCRB_BASE_ADDR_LO);  //Todo GetSmnCxlRcrbBase
  SmnAddr = SmnBase + (IOHC_SMN_ADDR_OFFSET * (TargetCxlIohub >> 1)) + (NBIO_SMN_ADDR_OFFSET * TargetCxlNbio);
  for (Port = 0; Port < MAX_PCIE_CORE0_PORT_SUPPORT; Port++) {
    //Get Lower 32 bits RCRB Address
    RegOffset = (SmnAddr + (Port * sizeof(UINT64)));  //Todo (RcrbBaseAddrReg | (Port << 12));
    RasSmnRead (SMN_SEG_BUS (TargetCxlSeg, TargetCxlBus), RegOffset, &CxlRcrbBaseAddrLo.Value);
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] PCIE0_PORT_%c_CXL_RCRB_BASE_ADDR_LO@0x%08X = 0x%08X.\n",
      ('A'+ Port), RegOffset, CxlRcrbBaseAddrLo.Value);
    if (CxlRcrbBaseAddrLo.Field.Cxl_Rcrb_Enable) {
      //Get Higher 32 bitd RCRB Address
      RasSmnRead (SMN_SEG_BUS (TargetCxlSeg, TargetCxlBus), (RegOffset + sizeof(UINT32)), &CxlRcrbBaseAddrHi);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] PCIE0_PORT_%c_CXL_RCRB_BASE_ADDR_HI@0x%08X = 0x%08X.\n",
        ('A'+ Port), (RegOffset + sizeof(UINT32)), CxlRcrbBaseAddrHi);
      RcrbAddress = (UINT64)CxlRcrbBaseAddrHi;
      RcrbAddress = (UINT64)((RcrbAddress << 32) | (CxlRcrbBaseAddrLo.Value & CXL_RCRB_BASE_ADDR_LO_MASK));
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] DpRcrb Address - 0x%016lX.\n", RcrbAddress);

      SecondaryBusNum = MmioRead8(RcrbAddress + PCICFG_SPACE_SECONDARY_BUS_OFFSET);
      SubordinateBusNum = MmioRead8(RcrbAddress + PCICFG_SPACE_SUBORDINATE_BUS_OFFSET);

      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] DpRcrb SecondaryBusNum = 0x%02X, SubordinateBusNum = 0x%02X\n", SecondaryBusNum, SubordinateBusNum);

      if ((RciepPciAddr.Address.Bus >= SecondaryBusNum) && (RciepPciAddr.Address.Bus <= SubordinateBusNum)) {
        *RciepUpRcrbAddr = (RcrbAddress + UPRCRB_OFFSET);
        *RciepUpRcrbMemBar0 = RasGetDpComponentRegisterAddress(*RciepUpRcrbAddr);

        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] Rciep UsRcrbAddr@0x%08X, Rciep UpRcrbMemBar0@ 0x%016lX\n", *RciepUpRcrbAddr, *RciepUpRcrbMemBar0);
        return TRUE;
      }
    }
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] %a Cannot find UP RCRB from RCiEP: 0x%08X\n", __FUNCTION__, RciepAddr);
  return FALSE;
}

EFI_STATUS
Cxl2p0RPErrStsCheck (  //CxlDpErrStsCheck, CxlDpErrCheck
  IN       PCIE_PORT_PROFILE     *PciePortProfileInstance,
  IN       RAS_ERR_LOG_DATA     *ErrLogData
  )
{
  CXL_ERROR_LOG_DATA        CxlErrorLogData;
  PCI_ADDR                  PciPortAddr;
  UINT64                    PciSegAddr;
  UINT16                    AerCapPtr;
  UINT32                    PcieUceErrorStatus;
  UINT32                    PcieCeErrorStatus;
  CXL_AGENT_ADDRESS         CxlAgentAddr;
  UINT32                    PcieRootStatus;
  EFI_STATUS                PcieErrorLogStatus;
  UINT64                    CxlComponentRegAddr;
  UINT64                    CxlCacheMemRegisterAddr;
  UINT16                    CxlRasCapPtr;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a - Start\n", __FUNCTION__);

  PcieErrorLogStatus = EFI_NOT_FOUND;

  if (PciePortProfileInstance->CxlPresent == FALSE || (PciePortProfileInstance->CxlVersion != 2)) {
    return EFI_UNSUPPORTED;
  }

  ErrLogData->Buffer = &CxlErrorLogData;

  PciPortAddr.AddressValue = PciePortProfileInstance->RpPciAddr;  //CXL 2.0 Root port PCI address
  AerCapPtr = RasFindPcieExtendedCapability (PciPortAddr.AddressValue, PCIE_EXT_AER_CAP_ID, 0xFFFF);
  if (AerCapPtr == 0) {
    return EFI_UNSUPPORTED;
  }

  PciSegAddr = PCI_SEGMENT_LIB_ADDRESS (PciPortAddr.Address.Segment,
                                        PciPortAddr.Address.Bus,
                                        PciPortAddr.Address.Device,
                                        PciPortAddr.Address.Function,
                                        PciPortAddr.Address.Register);
  PcieUceErrorStatus = PciSegmentRead32 (PciSegAddr + AerCapPtr + PCIE_UNCORR_STATUS_PTR);
  PcieCeErrorStatus  = PciSegmentRead32 (PciSegAddr + AerCapPtr + PCIE_CORR_STATUS_PTR);
  PcieRootStatus = PciSegmentRead32 (PciSegAddr + AerCapPtr + PCIE_ROOT_STATUS_PTR);

  //Check CXL error
  CxlErrorLogData.ErrorLogType = 0;
  // MPIO Interrupt Playload is invalid for down stram port.
  CxlErrorLogData.MpioIntrPayload = CXL_MPIO_INTR_PL_INVALID;

  //CXL IO error
  if ((PcieUceErrorStatus != 0) || (PcieCeErrorStatus != 0)){
    CxlErrorLogData.ErrorLogType = CXL_IO_ERROR;
  }

  //CXL Protocol error
  //  Search RAS Capability structure Pointer
  CxlComponentRegAddr =  RasGetCxl2p0RpRegisterBlockAddress (PciPortAddr.AddressValue, ComponenetRegistersBlkId);
  CxlCacheMemRegisterAddr = CxlComponentRegAddr + CXL_CACHE_MEM_REGISTERS_OFFSET;
  CxlRasCapPtr = RasGetCxlCapabilityPointer (CxlCacheMemRegisterAddr, CxlRasCapabilityId);
  if (0 != CxlRasCapPtr) {
    if (CxlRasCapErrCheck(CxlCacheMemRegisterAddr, CxlRasCapPtr)) {
      CxlErrorLogData.ErrorLogType |= CXL_CACHE_MEM_ERROR;
    }
  }

  //Log Errors
  if (CxlErrorLogData.ErrorLogType != 0) {
    IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_ERROR, "  Log CXL root port Error. Error Log Type: 0x%x\n", CxlErrorLogData.ErrorLogType);

    CxlAgentAddr.AgentAddress = 0;
    CxlAgentAddr.Device.FunctionNum = PciPortAddr.Address.Function;
    CxlAgentAddr.Device.DeviceNum = PciPortAddr.Address.Device;
    CxlAgentAddr.Device.BusNum = PciPortAddr.Address.Bus;
    CxlAgentAddr.Device.SegmentNum = PciPortAddr.Address.Segment;
    CxlErrorLogData.CxlAgentAddress = CxlAgentAddr.AgentAddress;
    CxlErrorLogData.CxlAgentType = CXL_AGENT_TYPE_ROOT_PORT;
    CxlErrorLogData.CxlRasCapStrucSmnAddr = 0;
    CxlErrorLogData.Cxl2p0RPPcieRootStatus = PcieRootStatus;
    CxlErrorLogData.RcecAddress = 0;
    CxlErrorLogData.CxlComponentRegAddr = CxlComponentRegAddr;
    PcieErrorLogStatus = ErrLogData->RasLogCallback (PciPortAddr, ErrLogData);  //CxlErrorLog
  } else {
     //No errors found on CXL RAS CAP
    return EFI_NOT_FOUND;
  }

  //CXL 2.0 Root Port IO error - Clear AER status registers
  if (!EFI_ERROR (PcieErrorLogStatus)) {
    RasPcieStsClr (PciPortAddr, 3);
  }

  //CXL Protocol error - Clear CXL RAS capability registers
  if ((CxlErrorLogData.ErrorLogType & CXL_CACHE_MEM_ERROR) == CXL_CACHE_MEM_ERROR) {
    CxlRasCapStsClear (CxlErrorLogData.CxlComponentRegAddr, 3);
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a - End, PcieErrorLog Status: %r\n", __FUNCTION__, PcieErrorLogStatus);

  return PcieErrorLogStatus;
}

EFI_STATUS
Cxl2p0DevErrStsCheck ( //CxlDevErrStsCheck
  IN       PCIE_PORT_PROFILE    *PciePortProfileInstance,
  IN       PCIE_DEV_ENTRY       *PcieDevEntry,
  IN       RAS_ERR_LOG_DATA     *ErrLogData,
  IN       UINT8                MpioIntrPayload,
  IN       UINT8                ErrTypeCheck
 )
{
  EFI_STATUS          Status = EFI_SUCCESS;
  CXL_ERROR_LOG_DATA  *CxlErrorLogData;
  CXL_RCIEP_ENTRY     CxlRciepEntry;
  EFI_STATUS          PcieErrorLogStatus;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a - Start\n", __FUNCTION__);

  PcieErrorLogStatus = EFI_NOT_FOUND;

  if (PciePortProfileInstance->CxlPresent == FALSE || (PciePortProfileInstance->CxlVersion != 2)) {
    return EFI_UNSUPPORTED;
  }

  CxlErrorLogData = (CXL_ERROR_LOG_DATA*)(ErrLogData->Buffer);
  CxlErrorLogData->MpioIntrPayload = MpioIntrPayload;

  CxlRciepEntry.DevAddr = PcieDevEntry->DevAddr;
  CxlRciepEntry.DevType = PcieDevEntry->DevType;
  CxlRciepEntry.RcecPciAddr = 0;
  InitializeListHead(&CxlRciepEntry.ListEntry);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  RCiEP Address: 0x%08x, DevType: 0x%x\n",
          CxlRciepEntry.DevAddr,
          CxlRciepEntry.DevType);

  if ((ErrTypeCheck == CXL_DEV_CHK_ALL_ERROR_TYPES) || (ErrTypeCheck == CXL_DEV_CHK_IO_CACHE_MEM_ERROR_ONLY)) {
    //Check and Log device io and protocol error
    PcieErrorLogStatus = CxlDevIoCacheMemErrCheck(&CxlRciepEntry, ErrLogData);
  }
  if ((ErrTypeCheck == CXL_DEV_CHK_ALL_ERROR_TYPES) || (ErrTypeCheck == CXL_DEV_CHK_COMPONENT_ERROR_ONLY)) {
    //Check and Log device Component error
    Status = CxlDevComponentErrCheck(&CxlRciepEntry, ErrLogData);
  }

  if (EFI_ERROR (PcieErrorLogStatus)) {
     PcieErrorLogStatus = Status;
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a - End, PcieErrorLog Status: %r\n", __FUNCTION__, PcieErrorLogStatus);

  return PcieErrorLogStatus;
}

VOID
GenerateErrorSectionGuid (
     OUT   EFI_GUID  *DestinationGuid,
  IN       EFI_GUID  *SourceGuid
  )
{
  UINT8     Index;
  EFI_GUID  CxlEventRecordIdList [] = {
    CXL_GENERAL_MEDIA_EVENT_RECORD_GUID,
    CXL_DRAM_EVENT_RECORD_GUID,
    CXL_MEMORY_MODULE_EVENT_RECORD_GUID,
    CXL_PHYSICAL_SWITCH_EVENT_RECORD_GUID,
    CXL_VIRTUAL_SWITCH_EVENT_RECORD_GUID,
    CXL_MLD_PORT_EVENT_RECORD_GUID
  };

  //Copy SourceGuid to DestinationGuid to generate the default value for the DestinationGuid.
  CopyGuid (DestinationGuid, SourceGuid);

  //Convert the format of DestinationGuid. (Some CXL devices use a different GUID format than the CXL Spec.)
  for (Index = 0; Index < (sizeof (CxlEventRecordIdList) / sizeof (EFI_GUID)); Index++) {
    if (CompareMem(CxlEventRecordIdList[Index].Data4, SourceGuid->Data4, sizeof(SourceGuid->Data4)) == 0) {
      CopyGuid (DestinationGuid, &CxlEventRecordIdList[Index]);
    }
  }

  return;
}

/**
 *  Get Register address from Register Locator from CXL Root Port
 *
 *
 * @param[in] Cxl2p0RpPciAddress    CXL 2.0 Root Port PCI address
 * @param[in] RegisterBlockId       Register Block Identifier
 *                                    0 = Indicates the register block entry is empty and the Register BIR.
 *                                    1 = Component Registers.
 *                                    2 = BAR Virtualization ACL Registers.
 *                                    3 = CXL Memory Device Registers.
 * @retval                          Register address
 *
 *
 **/
UINT64
RasGetCxl2p0RpRegisterBlockAddress (
  IN      UINT32      Cxl2p0RpPciAddress,
  IN      UINT8       RegisterBlockId
  )
{
  return RasGetRciepRegisterBlockAddress (Cxl2p0RpPciAddress, RegisterBlockId);
}