//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2020, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************


/** @file PeiPciEnumerationResouceLib.c
    This file contains the porting function which return RootBridge resources.

**/
#include <PeiPciEnumeration/PeiPciEnumeration.h>
#include <Ppi/FabricResourceManagerServicesPpi.h>
#include <Ppi/FabricTopologyServices2Ppi.h>
#include <FabricResourceManagerCmn.h>
#include <Library/BaseFabricTopologyLib.h>
#include <Library/FabricRegisterAccLib.h>

typedef struct _RB_BUS_INFO {
    UINTN RbNo[MAX_SOCKETS_SUPPORTED][MAX_RBS_PER_SOCKET];
    UINTN BusBase[MAX_SOCKETS_SUPPORTED][MAX_RBS_PER_SOCKET];
    UINTN BusLimit[MAX_SOCKETS_SUPPORTED][MAX_RBS_PER_SOCKET];
    UINTN DieCount[MAX_SOCKETS_SUPPORTED];
    UINTN DieRbCount[MAX_SOCKETS_SUPPORTED][MAX_DIES_PER_SOCKET];
    UINTN RbCount[MAX_SOCKETS_SUPPORTED];
} RB_BUS_INFO;

/**
    Routine returns Root Bridge Available Resource from Fabric Resource Manager
    
    @param  TotalSockets
    @param  FabricResource
    @param  RbBusInfo
    
    @return EFI_STATUS
**/
EFI_STATUS
GetAvailableResource (
    OUT  UINTN                          *TotalSockets,
    OUT  FABRIC_RESOURCE_FOR_EACH_RB    *FabricResource,
	OUT  RB_BUS_INFO                    *RbBusInfo
)
{
	EFI_STATUS                               Status; 
    UINTN                                    FabricId;
    UINTN                                    Socket;
    UINTN                                    Die;
    UINTN                                    Rb;
    FABRIC_RESOURCE_MANAGER_PPI              *FabricResourceManagerServicesPpi;
    AMD_PEI_FABRIC_TOPOLOGY_SERVICES2_PPI    *AmdFabricTopologyServices;
    UINT8                                    Index;
    UINT8                                    JIndex;
    UINTN                                    DieInSocket;
    UINTN                                    RbIndex;
    UINTN                                    RbInDie;
    UINTN                                    TempRbNo;
    UINTN                                    TempBusBase;
    UINTN                                    TempBusLimit;
    
    Status = PeiServicesLocatePpi(
                                   &gAmdFabricTopologyServices2PpiGuid,
                                   0,
                                   NULL,
                                   (VOID **)&AmdFabricTopologyServices
                                   );
    if (EFI_ERROR (Status)) {
        DEBUG ((DEBUG_ERROR, "GetAvailableResource : Locate Fabric Topology PPI %r \n",Status));
        return Status;
    }
    
    // Get Total Sockets  
    Status = AmdFabricTopologyServices->GetSystemInfo (
                                                   TotalSockets,
                                                   NULL,
                                                   NULL,
                                                   NULL,
                                                   NULL
                                                   );
    if (EFI_ERROR (Status)) {
        DEBUG ((DEBUG_ERROR, "GetAvailableResource : GetSystemInfo failed %r \n",Status));
        return Status;
    }
    
    if (*TotalSockets > MAX_SOCKETS_SUPPORTED) {
        DEBUG ((DEBUG_ERROR, "The socket number of the system is large than MAX_SOCKETS_SUPPORTED, please update the definition accordingly.\n"));
        return EFI_BUFFER_TOO_SMALL;  
    }
       
    for (Socket = 0; Socket < (*TotalSockets); Socket++) {
        
        Status = AmdFabricTopologyServices->GetProcessorInfo (
                                                         Socket,
                                                         &DieInSocket,
                                                         NULL
                                                         );
        if (EFI_ERROR (Status)) {
            DEBUG ((DEBUG_ERROR, "GetAvailableResource : GetProcessorInfo failed %r \n",Status));
            return Status;
        }
        
        // Store Die count per Socket
        RbBusInfo->DieCount[Socket] = DieInSocket;
        // Initialize Root Bridge count per Socket
        RbBusInfo->RbCount[Socket] = 0;
        
        for (Die = 0;Die < DieInSocket; Die++) {
            
            Status = AmdFabricTopologyServices->GetDieInfo (
                                                          Socket,
                                                          Die,
                                                          &RbInDie,
                                                          NULL,
                                                          NULL
                                                          );
            if (EFI_ERROR (Status)) {
                DEBUG ((DEBUG_ERROR, "GetAvailableResource : GetDieInfo failed %r \n",Status));
                return Status;
            }
            
            if (DieInSocket > MAX_DIES_PER_SOCKET) {
                DEBUG ((DEBUG_ERROR, "The Die number of the system is large than MAX_DIES_PER_SOCKET, please update the definition accordingly.\n"));
                return EFI_BUFFER_TOO_SMALL; 
            }
                
            // Store Root Bridge count per Die
            RbBusInfo->DieRbCount[Socket][Die] = RbInDie;
            // Get the last Root Bridge count per Socket
            RbIndex = RbBusInfo->RbCount[Socket];
            // Updated Root Bridge count per Socket
            RbBusInfo->RbCount[Socket] += RbInDie;
            
            if (RbBusInfo->RbCount[Socket] > MAX_RBS_PER_SOCKET) {
                DEBUG ((DEBUG_ERROR, "The root bridge number of the system is large than MAX_RBS_PER_SOCKET, please update the definition accordingly.\n"));
                return EFI_BUFFER_TOO_SMALL; 
            }
            
            for (Rb = 0; Rb < RbInDie; Rb++) {
                // Get root bridge number, bus base and bus limit
                Status = AmdFabricTopologyServices->GetRootBridgeInfo (
                                                           Socket,
                                                           Die,
                                                           Rb,
                                                           &FabricId,
                                                           &RbBusInfo->BusBase[Socket][RbIndex + Rb],
                                                           &RbBusInfo->BusLimit[Socket][RbIndex + Rb],
                                                           NULL,
                                                           NULL,
                                                           NULL
                                                           );
                if (EFI_ERROR (Status)) {
                    DEBUG ((DEBUG_ERROR, "GetAvailableResource : GetRootBridgeInfo failed %r \n",Status));
                    return Status;
                }
                RbBusInfo->RbNo[Socket][RbIndex + Rb]= Rb;
            }
        }
    }
    
    Status = PeiServicesLocatePpi(
                                  &gAmdFabricResourceManagerServicesPpiGuid,
                                  0,
                                  NULL,
                                  (VOID **)&FabricResourceManagerServicesPpi
                                  );
    if (EFI_ERROR (Status)) {
        DEBUG ((DEBUG_ERROR, "GetAvailableResource : Locate Fabric Resource Manager PPI %r \n",Status));
        return Status;
    }
    
    // Get RB available resource from Fabric resource manager       
    Status = FabricResourceManagerServicesPpi->FabricGetAvailableResource (FabricResource);
    
    if (EFI_ERROR (Status)) {
        DEBUG ((DEBUG_ERROR, "GetAvailableResource : FabricGetAvailableResource failed %r \n",Status));
        return Status;
    }

    //Rearrange RB_BUS_INFO structure based on bus base
    for (Socket = 0; Socket < (*TotalSockets); Socket++) {
        
        for (Index = 0; Index < RbBusInfo->RbCount[Socket]; Index++) {
            
            for (JIndex = Index + 1; JIndex < RbBusInfo->RbCount[Socket]; JIndex++){
                
                if (RbBusInfo->BusBase[Socket][Index] > RbBusInfo->BusBase[Socket][JIndex]) {
                    
                    TempBusBase = RbBusInfo->BusBase[Socket][Index];
                    TempRbNo = RbBusInfo->RbNo[Socket][Index];
                    TempBusLimit = RbBusInfo->BusLimit[Socket][Index];
                    
                    RbBusInfo->BusBase[Socket][Index] = RbBusInfo->BusBase[Socket][JIndex];
                    RbBusInfo->RbNo[Socket][Index] = RbBusInfo->RbNo[Socket][JIndex];
                    RbBusInfo->BusLimit[Socket][Index] = RbBusInfo->BusLimit[Socket][JIndex];
                    
                    RbBusInfo->BusBase[Socket][JIndex] = TempBusBase;
                    RbBusInfo->RbNo[Socket][JIndex] = TempRbNo;
                    RbBusInfo->BusLimit[Socket][JIndex] = TempBusLimit; 
                }
            }
        }
    }

    return EFI_SUCCESS;
}
/**
    Routine returns Root Bridge count
    
    @param  None
    
    @return UINT8
**/
UINT8
GetRootBridgeCount (
)
{
    EFI_STATUS                               Status; 
    UINTN                                    TotalRBs = 0;
    AMD_PEI_FABRIC_TOPOLOGY_SERVICES2_PPI    *AmdFabricTopologyServices;

    // Locate AMD Fabric Topology Services PPI
    Status = PeiServicesLocatePpi(
                                   &gAmdFabricTopologyServices2PpiGuid,
                                   0,
                                   NULL,
                                   (VOID **) &AmdFabricTopologyServices
                                   );
    if (EFI_ERROR (Status)) {
        DEBUG ((DEBUG_ERROR, "GetRootBridgeCount : Fabric Topology PPI %r \n",Status));
        ASSERT_EFI_ERROR(Status);
        return 0; // returns root bridge as 0
    }
    // Get Total Root Bridge's   
    Status = AmdFabricTopologyServices->GetSystemInfo (
                                                   NULL,
                                                   NULL,
                                                   &TotalRBs,
                                                   NULL,
                                                   NULL
                                                   );
    if (EFI_ERROR (Status)) {
        DEBUG ((DEBUG_ERROR, "GetRootBridgeCount : GetSystemInfo failed %r \n",Status));
        ASSERT_EFI_ERROR(Status);
        return 0; // returns root bridge as 0
    }
	
    DEBUG((DEBUG_INFO, "Total RB Count - 0x%x\n", TotalRBs));
    return (UINT8)TotalRBs; //return available root bridge in platform.
}

/**
    Routine returns Root Bridge's resources based on Root Bridge Index
    
    @param  RbIndex
    @param  RbResource
    
    @return EFI_STATUS
**/
EFI_STATUS
GetResourceForRootBridge (
    IN   UINT8                       RbIndex,
    OUT  PCI_ROOT_BRIDGE_RESOURCE    *RbResource
)
{
    EFI_STATUS                            Status;
    UINT8                                 RbInSocket;
    UINTN                                 Socket;
    UINTN                                 RbNumber;
    UINT8                                 RbCount    = 0;
    static UINTN                          TotalSockets;
    static FABRIC_RESOURCE_FOR_EACH_RB    FabricResource;
    static RB_BUS_INFO                    RbBusInfo;
    static BOOLEAN                        FirstCall  = TRUE;

    // Get Root Bridge Resource in first call
    if (FirstCall) {
        Status = GetAvailableResource(&TotalSockets, &FabricResource, &RbBusInfo);
        if (EFI_ERROR (Status)) {
            DEBUG ((DEBUG_ERROR, "GetResourceForRootBridge : GetAvailableResource failed %r \n",Status));
            return Status;
        }
        FirstCall = FALSE;
    }

    ZeroMem((VOID *)RbResource, sizeof(PCI_ROOT_BRIDGE_RESOURCE));
    
    for (Socket = 0; Socket < TotalSockets; Socket++) {
        
        for (RbInSocket = 0; RbInSocket < RbBusInfo.RbCount[Socket]; RbInSocket++) {
                        
             if(RbCount == RbIndex) {

                RbNumber = RbBusInfo.RbNo[Socket][RbInSocket];
                
		        RbResource->Segment = (UINT8)(RbBusInfo.BusBase[Socket][RbInSocket] / MAX_PCI_BUS_NUMBER_PER_SEGMENT);
                RbResource->BusBase = (UINT8)(RbBusInfo.BusBase[Socket][RbInSocket] % MAX_PCI_BUS_NUMBER_PER_SEGMENT);
                RbResource->BusLimit = (UINT8)(RbBusInfo.BusLimit[Socket][RbInSocket] - RbBusInfo.BusBase[Socket][RbInSocket] + RbResource->BusBase);
                RbResource->PciResourceMemBase = (UINT32)FabricResource.PrefetchableMmioSizeBelow4G[Socket][RbNumber].Base;
                RbResource->PciResourceMemLimit = RbResource->PciResourceMemBase + (UINT32)FabricResource.PrefetchableMmioSizeBelow4G[Socket][RbNumber].Size - 1;
                RbResource->PciResourceIoBase = (UINT16)FabricResource.IO[Socket][RbNumber].Base;
                RbResource->PciResourceIoLimit = RbResource->PciResourceIoBase + (UINT16)FabricResource.IO[Socket][RbNumber].Size - 1;
                
                DEBUG((DEBUG_INFO, "RbIndex - 0x%x\n", RbIndex));
                DEBUG((DEBUG_INFO, "Segment - 0x%x\n", RbResource->Segment));
                DEBUG((DEBUG_INFO, "BusBase - 0x%x\n", RbResource->BusBase));
                DEBUG((DEBUG_INFO, "BusLimit - 0x%x\n", RbResource->BusLimit));
                DEBUG((DEBUG_INFO, "PciResourceMemBase - 0x%x\n", RbResource->PciResourceMemBase));
                DEBUG((DEBUG_INFO, "PciResourceMemLimit - 0x%x\n", RbResource->PciResourceMemLimit));
                DEBUG((DEBUG_INFO, "PciResourceIoBase - 0x%x\n", RbResource->PciResourceIoBase));
                DEBUG((DEBUG_INFO, "PciResourceIoLimit - 0x%x\n", RbResource->PciResourceIoLimit));
                    
                return EFI_SUCCESS;
             }
             RbCount++;     
        }
    }
    
    return EFI_NOT_FOUND;
}

/**
   Certain PCI controllers requires additional Initialization before
   Enumerating the device. This routine will help to do initialization
   
   @param Segment
   @param Bus
   @param Device
   @param Function
   @param VendorId
   @param DeviceId
   
   @return None
 */
VOID
ProcessPreEnumerationInit (
    IN UINT8                   Segment,
    IN UINT8                   Bus,
    IN UINT8                   Device,
    IN UINT8                   Function,
    IN UINT16                  VendorId, 
    IN UINT16                  DeviceId
)
{
    return;
}

