/*****************************************************************************
 *
 * Copyright (C) 2018-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */


/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Library/BaseMemoryLib.h>
#include "AmdPlatformRasBrhSmm.h"
#include <AmdCpmSmm.h>
#include <Protocol/AmdCpmRasOemProtocol.h>
#include <Library/MemoryAllocationLib.h>
#include <Library/IdsLib.h>
#include <Library/SmmMemLib.h>

extern EFI_STATUS
CxlErrorLog (
  IN       PCI_ADDR             Device,
  IN OUT   RAS_ERR_LOG_DATA    *ErrLogData
  );

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
EFI_STATUS
ParsingPcieError (
  IN       UINT16               ErrorEntryCnt,
  IN       PCIE_ERR_ENTRY       *PcieErrorEntry,
  OUT      BOOLEAN              *TrigNMI,
  OUT      BOOLEAN              *TrigSCI
  );

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                          T A B L E    D E F I N I T I O N S
 *----------------------------------------------------------------------------------------
 */

EFI_STATUS
ParsingPcieError (
  IN       UINT16               ErrorEntryCnt,
  IN       PCIE_ERR_ENTRY       *PcieErrorEntry,
  OUT      BOOLEAN              *TrigNMI,
  OUT      BOOLEAN              *TrigSCI
  )
{
  EFI_STATUS        Status = EFI_SUCCESS;
  PCI_ADDR          PciAddr;
  UINT16            ErrEntryIndx;
  PCIE_ERR_ENTRY    *LocalErrEntry;
  BOOLEAN           IsEdrDpcError;
  UINT16            AerCapPtr;
  UINT32            PcieRootStatus;
  UINT64            PciSegAddr;

  LocalErrEntry = PcieErrorEntry;

  for (ErrEntryIndx = 0; ErrEntryIndx < ErrorEntryCnt; ErrEntryIndx++) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Local Error Entry Address: 0x%08x\n", (UINTN)LocalErrEntry);
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  PCI Error @ Address : 0x%08x Type: %x Valid: %x\n", LocalErrEntry->DevAddr, LocalErrEntry->DevType, LocalErrEntry->EntryValid);
    if (LocalErrEntry->EntryValid) {
      PciAddr.AddressValue = LocalErrEntry->DevAddr;

      PciSegAddr = PCI_SEGMENT_LIB_ADDRESS (PciAddr.Address.Segment,
                                            PciAddr.Address.Bus,
                                            PciAddr.Address.Device,
                                            PciAddr.Address.Function,
                                            PciAddr.Address.Register);
      //Log Error
      IsEdrDpcError = FALSE;
      PcieErrorLog(LocalErrEntry, TrigNMI, TrigSCI, &IsEdrDpcError);
      if (!IsEdrDpcError) {
        //Clear Status
        RasPcieStsClr(PciAddr, mPlatformApeiData->PlatRasPolicy.RasRetryCnt);
        if ((LocalErrEntry->DevType == PcieDeviceDownstreamPort) || (LocalErrEntry->DevType == PcieDeviceRootComplex)) {
          RasDpcErrHandle(PciAddr, mPlatformApeiData->PlatRasPolicy.RasRetryCnt);
          //DPC action abort would effect AER error log, continue process without handle the error.
        }
      } else {
        //EDR Flow: Clear Root Error Status [Bit0 ERR_COR Received] only
        //          (Bit 0 will be set during EDR on a non-hot-plug PCIe slot)
        AerCapPtr = RasFindPcieExtendedCapability (PciAddr.AddressValue, PCIE_EXT_AER_CAP_ID, 0xFFFF);
        PcieRootStatus = PciSegmentRead32 (PciSegAddr + AerCapPtr + PCIE_ROOT_STATUS_PTR);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] EDR - PCIE Root Error Status : 0x%08x => ", PcieRootStatus);
        PciSegmentWrite32 (PciSegAddr + AerCapPtr + PCIE_ROOT_STATUS_PTR, (PcieRootStatus & BIT0));
        PcieRootStatus = PciSegmentRead32 (PciSegAddr + AerCapPtr + PCIE_ROOT_STATUS_PTR);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, " Clear ERR_COR Received bit: 0x%08x\n", PcieRootStatus);
      }
    }
    LocalErrEntry++;
  }
  return Status;
}

EFI_STATUS
PcieErrorScanHelper (
  IN     UINT8    IndexOfRbBusMap,
  IN     UINT32   RpPciAddr,
  IN OUT BOOLEAN  *TrigNMI,
  IN OUT BOOLEAN  *TrigSCI,
  IN     BOOLEAN  CheckEdrEnFlg
  )
{
  EFI_STATUS            Status = EFI_SUCCESS;
  UINT16                PciePortIndex;
  PCIE_PORT_PROFILE     *PciePortProfileInstance;
  PCIE_ERR_ENTRY        *PcieErrorEntry;
  UINT16                ScanDeviceCnt;
  UINT16                ErrorEntryCnt;
  BOOLEAN               OsEdrEnabledFlag;
  PCI_ADDR              RpAddress;
  RAS_ERR_LOG_DATA      CxlErrLogData;
  UINT8                 Seg;
  UINT8                 Bus;
  EDR_DSM_ACPI_SMM_DATA *EdrDsmAcpiSmmData;
  BOOLEAN               EdrJustDetectedOnThisPort;

  EdrDsmAcpiSmmData = mPlatformApeiData->EdrDsmAcpiSmmData;

  //RpPciAddr == 0xFFFFFFFF => Scan all RootPorts and their devices under the Seg and Bus derived by IndexOfRbBusMap.
  //RpPciAddr != 0xFFFFFFFF => Scan only the specified RootPort and all devices under this RootPort,
  //                           IndexOfRbBusMap will be ignored.
  if (RpPciAddr != 0xFFFFFFFF) {
    RpAddress.AddressValue = RpPciAddr;
    Seg = (UINT8)RpAddress.Address.Segment;
    Bus = (UINT8)RpAddress.Address.Bus;
  } else {
    Seg = mRbBusMap->RbBusEntry[IndexOfRbBusMap].RbSeg;
    Bus = mRbBusMap->RbBusEntry[IndexOfRbBusMap].RbBusBase;
  }

  //Search active PCI-E port for error only
  PciePortProfileInstance = mPlatformApeiData->AmdPciePortMap->PciPortNumber;
  for (PciePortIndex = 0; PciePortIndex < mPlatformApeiData->AmdPciePortMap->PortCount; PciePortIndex++, PciePortProfileInstance++) {
    if (RpPciAddr != 0xFFFFFFFF) {
      if (PciePortProfileInstance->RpPciAddr != RpPciAddr) {
        continue;
      }
    } else { //RpPciAddr == 0xFFFFFFFF
      RpAddress.AddressValue = PciePortProfileInstance->RpPciAddr;
      if ((RpAddress.Address.Segment != Seg) || (RpAddress.Address.Bus != Bus)) {
        //find next
        continue;
      }
    }

    RpAddress.AddressValue = PciePortProfileInstance->RpPciAddr;
    EdrJustDetectedOnThisPort = FALSE;
    if (EdrDsmAcpiSmmData != 0 && EdrDsmAcpiSmmData != NULL) {
      if (EdrDsmAcpiSmmData->RpAslDevName != 0) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
          "EdrDsmAcpiSmmData->RpSeg 0x%02X, ->RpBus: 0x%02X, ->RpDevFnc: 0x%08X, ->RpAslDevName: 0x%08X. Current RpPciAddr: 0x%08X\n",
          EdrDsmAcpiSmmData->RpSeg,
          EdrDsmAcpiSmmData->RpBus,
          EdrDsmAcpiSmmData->RpDevFnc,
          EdrDsmAcpiSmmData->RpAslDevName,
          RpAddress.AddressValue
        );
        EdrJustDetectedOnThisPort = \
          ((RpAddress.Address.Segment  == EdrDsmAcpiSmmData->RpSeg) &&
           (RpAddress.Address.Bus      == EdrDsmAcpiSmmData->RpBus) &&
           (RpAddress.Address.Device   == (UINT8)(EdrDsmAcpiSmmData->RpDevFnc >> 16)) &&
           (RpAddress.Address.Function == (UINT8)(EdrDsmAcpiSmmData->RpDevFnc & 0xFF))
          );
      }
    }

    OsEdrEnabledFlag = FALSE;
    if (CheckEdrEnFlg || EdrJustDetectedOnThisPort) {
      OsEdrEnabledFlag = OsEdrEnabled(RpAddress);
    }
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
      "OsEdrEnabledFlag: 0x%02X, CheckEdrEnFlg: 0x%02X, EdrJustDetectedOnThisPort: 0x%02X\n",
      OsEdrEnabledFlag, CheckEdrEnFlg, EdrJustDetectedOnThisPort
    );

    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "PCI-E Error search at Seg: 0x%x, Bus: 0x%x, Port: %0d, Device count: %d\n",
      Seg, Bus, PciePortProfileInstance->PciPortNumber, PciePortProfileInstance->EndPointDevCnt
    );

    //PCI error detection
    ScanDeviceCnt = PciePortProfileInstance->EndPointDevCnt + 1;

    Status = gSmst->SmmAllocatePool (
                      EfiRuntimeServicesData,
                      sizeof (PCIE_ERR_ENTRY) * ScanDeviceCnt,
                      (VOID **)&PcieErrorEntry
                      );
    if (EFI_ERROR (Status)) {
      return Status;
    }
    ZeroMem (PcieErrorEntry, sizeof (PCIE_ERR_ENTRY) * ScanDeviceCnt);

    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]PCIE Error Entry Address: 0x%08x\n", (UINTN)PcieErrorEntry);
    if (mPlatformApeiData->PlatRasPolicy.CpmCxlErrorReport) {
      //PCI error detection and CXL 2.0 root port/ device error detection
      CxlErrLogData.Buffer = NULL;
      CxlErrLogData.RasLogCallback = CxlErrorLog;
      RasPcieCxl2p0ErrStsCheck (PciePortProfileInstance, PcieErrorEntry, &ErrorEntryCnt, OsEdrEnabledFlag, &CxlErrLogData);
    } else {
      RasPcieErrStsCheckV2(PciePortProfileInstance, PcieErrorEntry, &ErrorEntryCnt, OsEdrEnabledFlag);
    }
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]Error Entry Counter: %d\n", ErrorEntryCnt);

    if (ErrorEntryCnt != 0) {
      //Have at least one error entry found, log the error
      ParsingPcieError(ErrorEntryCnt, PcieErrorEntry, TrigNMI, TrigSCI);
    }
    Status = gSmst->SmmFreePool (PcieErrorEntry);
    if (EFI_ERROR(Status)) {
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] SMMFreePool failed at PCIE Error Entry:0x%08x\n",(UINTN)PcieErrorEntry);
    }
  }

  return EFI_SUCCESS;
}

EFI_STATUS
PcieErrorScan (
  UINT8  IndexOfRbBusMap  //NbioBusNum
  )
{
  EFI_STATUS            Status = EFI_SUCCESS;
  BOOLEAN               TrigNMI;
  BOOLEAN               TrigSCI;

  TrigNMI = FALSE;
  TrigSCI = FALSE;

  Status = PcieErrorScanHelper (IndexOfRbBusMap, 0xFFFFFFFF, &TrigNMI, &TrigSCI, FALSE);

  if (TrigNMI) {
    //Trigger NMI
    RasTriggerNMI();
  }
  if (TrigSCI) {
    //Trigger SCI
    RasTriggerSci();
  }
  return Status;
}

EFI_STATUS
PcieErrorLog (
  PCIE_ERR_ENTRY    *PcieErrEntry,
  BOOLEAN           *TrigNMI,
  BOOLEAN           *TrigSCI,
  BOOLEAN           *IsEdrDpcError
  )
{
  EFI_STATUS                                   Status = EFI_SUCCESS;
  UINT16                                       AerCapPtr;
  UINT8                                        PcieCapPtr;
  UINT32                                       Index;
  UINT32                                       ErrorSeverity;
  PCIE_DEVICE_TYPE                             PortType;
  PCI_ADDR                                     PciAddr;
  UINT8                                        DevType;
  ROOT_ERR_STS_REG                             RootErrSts;
  UINT32                                       MaxIndex = 0;
  UINTN                                        HandleBufferSize = 0;
  UINT8                                        FreeFlag = 0;
  EFI_HANDLE                                   *HandleBuffer = NULL;
  AMD_CPM_RAS_OEM_PROTOCOL                     *AmdRasOemProtocol = NULL;
  EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE  *PcieErrStatusBlk;
  EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE  *TempPcieErrStatusBlk;
  GENERIC_PCIE_AER_ERR_ENTRY_V3                *GenPcieAerErrEntry;
  PCIE_ERROR_SECTION                           *PcieErrorSection;
  EFI_GUID                                     PcieErrSectionType = PCIE_SECT_GUID;
  UINT64                                       PciSegAddr;
  UINTN                                        TempCommBufferSize;
  UINT8                                        TempCommBuffer[MAX_ERROR_BLOCK_SIZE];

  PciAddr.AddressValue = PcieErrEntry->DevAddr;
  DevType = PcieErrEntry->DevType;
  RootErrSts.Value = PcieErrEntry->RootErrSts;
  PciSegAddr = PCI_SEGMENT_LIB_ADDRESS (PciAddr.Address.Segment,
                                        PciAddr.Address.Bus,
                                        PciAddr.Address.Device,
                                        PciAddr.Address.Function,
                                        PciAddr.Address.Register);

  AerCapPtr = RasFindPcieExtendedCapability (PciAddr.AddressValue, PCIE_EXT_AER_CAP_ID, 0xFFFF);
  PcieCapPtr = RasFindPciCapability (PciAddr.AddressValue, PCIE_CAP_ID);

  //end-point device severity will aligned to the root port.
  ErrorSeverity = ERROR_NONE;
  if (RootErrSts.Field.ErrCorReceived) {
    ErrorSeverity = ERROR_SEVERITY_CORRECTED;
  }
  if (RootErrSts.Field.NonFatalErrMesgReceived) {
    ErrorSeverity = ERROR_RECOVERABLE;
  }
  if (RootErrSts.Field.FatalErrMesgReceived) {
    ErrorSeverity = ERROR_SEVERITY_FATAL;
  }

  *IsEdrDpcError = FALSE;
  if ((RootErrSts.Value >> 16) == 0xFFFF) {
    *IsEdrDpcError = TRUE;
    *TrigSCI = TRUE;
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] %a: It's an EDR DPC Error => SCI will be triggered and this error will not be logged to GHES\n", __FUNCTION__);
    return EFI_SUCCESS;
  }

  switch (ErrorSeverity) {
  case ERROR_SEVERITY_FATAL:
    //Find uncorrectable error block
    if (DevType == PcieDeviceRootComplex) {
      PcieErrStatusBlk = mPlatformApeiData->AmdPcieAerUnErrBlk;
    } else if ((DevType == PcieDeviceEndPoint) ||
               (DevType == PcieDeviceLegacyEndPoint ||
                DevType == PCieDeviceRCiEP)) {
      PcieErrStatusBlk = mPlatformApeiData->AmdPcieDevAerUnErrBlk;
    } else {
      PcieErrStatusBlk = mPlatformApeiData->AmdPcieBridgeAerUnErrBlk;
    }

    TempCommBufferSize = sizeof (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE) + PcieErrStatusBlk->DataLength + sizeof (GENERIC_PCIE_AER_ERR_ENTRY_V3);
    if (TempCommBufferSize > MAX_ERROR_BLOCK_SIZE) {
      RasPcieStsClr(PciAddr, mPlatformApeiData->PlatRasPolicy.RasRetryCnt);
      return EFI_OUT_OF_RESOURCES;
    }

    if (!SmmIsBufferOutsideSmmValid ((UINTN) PcieErrStatusBlk, TempCommBufferSize)) {
      DEBUG ((EFI_D_ERROR, "PcieErrStatusBlk is in SMRAM or overlapped with SMRAM!\n"));
      return EFI_INVALID_PARAMETER;
    }

    ZeroMem ((VOID *) TempCommBuffer, MAX_ERROR_BLOCK_SIZE);
    CopyMem ((VOID *) TempCommBuffer, (VOID *) PcieErrStatusBlk, TempCommBufferSize);
    TempPcieErrStatusBlk = (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE *) TempCommBuffer;

    RasReinitErrBlkSts(TempPcieErrStatusBlk);

    //Set BlockStatus
    if (TempPcieErrStatusBlk->BlockStatus.UncorrectableErrorValid) {
      TempPcieErrStatusBlk->BlockStatus.MultipleUncorrectableErrors = 1;
    } else {
      TempPcieErrStatusBlk->BlockStatus.UncorrectableErrorValid = 1;
    }
    break;
  case ERROR_RECOVERABLE:
  case ERROR_SEVERITY_CORRECTED:
    //Find correctable error block
    if (DevType == PcieDeviceRootComplex) {
      PcieErrStatusBlk = mPlatformApeiData->AmdPcieAerErrBlk;
    } else if ((DevType == PcieDeviceEndPoint)||
               (DevType == PcieDeviceLegacyEndPoint)) {
      PcieErrStatusBlk = mPlatformApeiData->AmdPcieDevAerErrBlk;
    } else {
      PcieErrStatusBlk = mPlatformApeiData->AmdPcieBridgeAerErrBlk;
    }

    TempCommBufferSize = sizeof (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE) + PcieErrStatusBlk->DataLength + sizeof (GENERIC_PCIE_AER_ERR_ENTRY_V3);
    if (TempCommBufferSize > MAX_ERROR_BLOCK_SIZE) {
      RasPcieStsClr(PciAddr, mPlatformApeiData->PlatRasPolicy.RasRetryCnt);
      return EFI_OUT_OF_RESOURCES;
    }

    if (!SmmIsBufferOutsideSmmValid ((UINTN) PcieErrStatusBlk, TempCommBufferSize)) {
      DEBUG ((EFI_D_ERROR, "PcieErrStatusBlk is in SMRAM or overlapped with SMRAM!\n"));
      return EFI_INVALID_PARAMETER;
    }

    ZeroMem ((VOID *) TempCommBuffer, MAX_ERROR_BLOCK_SIZE);
    CopyMem ((VOID *) TempCommBuffer, (VOID *) PcieErrStatusBlk, TempCommBufferSize);
    TempPcieErrStatusBlk = (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE *) TempCommBuffer;

    RasReinitErrBlkSts(TempPcieErrStatusBlk);

    //Set BlockStatus
    if (TempPcieErrStatusBlk->BlockStatus.CorrectableErrorValid) {
      TempPcieErrStatusBlk->BlockStatus.MultipleCorrectableErrors = 1;
    } else {
      TempPcieErrStatusBlk->BlockStatus.CorrectableErrorValid = 1;
    }
    break;
  default:
    return EFI_UNSUPPORTED;
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]Log Device Type : 0x%x @ Block Address: 0x%08x\n", DevType, (UINTN)PcieErrStatusBlk);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Seg: 0x%x Bus: 0x%x Dev: 0x%x Func: 0x%x PCIE Cap Ptr : 0x%x\n", PciAddr.Address.Segment, PciAddr.Address.Bus, PciAddr.Address.Device, PciAddr.Address.Function, PcieCapPtr);

  GenPcieAerErrEntry = (GENERIC_PCIE_AER_ERR_ENTRY_V3 *) ((UINTN ) TempPcieErrStatusBlk + sizeof (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE) + TempPcieErrStatusBlk->DataLength);
  CopyGuid ((EFI_GUID*)&GenPcieAerErrEntry->GenErrorDataEntry.SectionType, &PcieErrSectionType);
  GenPcieAerErrEntry->GenErrorDataEntry.Revision = GENERIC_ERROR_REVISION;
  GenPcieAerErrEntry->GenErrorDataEntry.Flags = 0x01;
  GenPcieAerErrEntry->GenErrorDataEntry.ErrorDataLength = sizeof (PCIE_ERROR_SECTION);

  GenPcieAerErrEntry->GenErrorDataEntry.ValidationBits = FRU_STRING_VALID;
  AsciiStrCpyS ((CHAR8 *)GenPcieAerErrEntry->GenErrorDataEntry.FruText, FRU_TEXT_MAX_LENGTH, "PcieError");

  GenPcieAerErrEntry->GenErrorDataEntry.ErrorSeverity = ErrorSeverity;
  TempPcieErrStatusBlk->ErrorSeverity = ErrorSeverity;

  PcieErrorSection = &GenPcieAerErrEntry->PcieAerErrorSection;

  PortType = RasGetPcieDeviceType (PciAddr);
  PcieErrorSection->Validation.Value = 0xEF;
  PcieErrorSection->PortType = (UINT32)PortType;
  PcieErrorSection->Revision = 0x02;
  PcieErrorSection->CommandStatus = PciSegmentRead32 (PciSegAddr + PCI_COMMAND_REG);

  PcieErrorSection->DeviceId.VendorId = PciSegmentRead16 (PciSegAddr + PCI_VENDORID_REG);
  PcieErrorSection->DeviceId.DeviceId = PciSegmentRead16 (PciSegAddr + PCI_DEVICEID_REG);
  PcieErrorSection->DeviceId.ClassCode[0] = PciSegmentRead8 (PciSegAddr + PCI_CLASS_CODE_0_REG);
  PcieErrorSection->DeviceId.ClassCode[1] = PciSegmentRead8 (PciSegAddr + PCI_CLASS_CODE_1_REG);
  PcieErrorSection->DeviceId.ClassCode[2] = PciSegmentRead8 (PciSegAddr + PCI_CLASS_CODE_2_REG);
  PcieErrorSection->DeviceId.Function = (UINT8)PciAddr.Address.Function;
  PcieErrorSection->DeviceId.Device = (UINT8)PciAddr.Address.Device;
  PcieErrorSection->DeviceId.Segment = (UINT8)PciAddr.Address.Segment;

  // Bus number information to uniquely identify the PCI configuration type. Default values for both the bus numbers is zero.
  switch (PortType) {
  case  PcieDeviceEndPoint:
  case  PcieDeviceLegacyEndPoint:
  case  PCieDeviceRCiEP:
  case  PcieDeviceRcec:
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Type 0 PCI Configuration Space\n");
    PcieErrorSection->DeviceId.PrimaryBus = (UINT8)PciAddr.Address.Bus;
    PcieErrorSection->DeviceId.SecondaryBus = 0;
    break;
  case  PcieDeviceRootComplex:
  case  PcieDeviceUpstreamPort:
  case  PcieDeviceDownstreamPort:
  case  PcieDevicePcieToPcix:
  case  PcieDevicePcixToPcie:
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Type 1 PCI Configuration Space\n");
    PcieErrorSection->DeviceId.PrimaryBus = PciSegmentRead8 (PciSegAddr + PCI_PRIMARY_BUS_REG);
    PcieErrorSection->DeviceId.SecondaryBus = PciSegmentRead8 (PciSegAddr + PCI_SECONDARY_BUS_REG);
    break;
  default:
    break;
  }

  if (PcieCapPtr != 0) {
    PcieErrorSection->DeviceId.Slot = (UINT16) ((PciSegmentRead32 (PciSegAddr + PcieCapPtr + PCIE_SLOT_CAP_REGISTER) >> PCIE_SLOT_NUMBER_SHIFT) << 3);
  }
  PcieErrorSection->BridgeCtrlStatus = (UINT32) (PciSegmentRead16 (PciSegAddr + PCI_BRIDGE_CONTROL_REG)) << 16 | (UINT32) (PciSegmentRead16 (PciSegAddr + PCI_SEC_STATUS_REG));

  if (PcieCapPtr != 0) {
    // Check PCIE CAP version
    MaxIndex = ((PciSegmentRead16 (PciSegAddr + PcieCapPtr + 2) & 0xF) < 2)? 9 : 15;
    for (Index = 0; Index < MaxIndex; Index++) {
      PcieErrorSection->CapabilityStructure.CapabilityData[Index] = \
                                            PciSegmentRead32 (PciSegAddr + (PcieCapPtr + (4 * Index)));
    }
  }
  if (AerCapPtr != 0) {
    // Check AER CAP version
    MaxIndex = ((PciSegmentRead16 (PciSegAddr + AerCapPtr + 2) & 0xF) < 2)? 14 : 18;  //14 = (0x38/4), 18 = (0x48/4)
    for (Index = 0; Index < MaxIndex; Index++) {
      PcieErrorSection->AerInfo.AerInfoData[Index] = PciSegmentRead32 (PciSegAddr + (AerCapPtr + (4 * Index)));
    }
  }

  TempPcieErrStatusBlk->BlockStatus.ErrorDataEntryCount++;
  TempPcieErrStatusBlk->DataLength += sizeof (GENERIC_PCIE_AER_ERR_ENTRY_V3);

  UpdateGenErrStsBlkSeverity(TempPcieErrStatusBlk, ErrorSeverity);

  switch (ErrorSeverity) {
  case ERROR_SEVERITY_FATAL:
    if (mPlatformApeiData->PlatRasPolicy.PcieUnCorrGhesNotifyType) {
      *TrigNMI = TRUE;
    }
    break;
  case ERROR_SEVERITY_CORRECTED:
    if (mPlatformApeiData->PlatRasPolicy.PcieGhesNotifyType) {
      *TrigSCI = TRUE;
    }
    break;
  }

  //Locate Ras Oem Protocol
  Status = gSmst->SmmLocateHandle (
                              ByProtocol,
                              &gAmdCpmRasOemSmmProtocolGuid,
                              NULL,
                              &HandleBufferSize,
                              HandleBuffer);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "PcieErrorLog SmmLocateHandle gAmdCpmRasOemSmmProtocolGuid Status =%r\n", Status);

  if (EFI_ERROR(Status)) {
      if (Status == EFI_BUFFER_TOO_SMALL) {
          HandleBuffer = AllocateRuntimePool (HandleBufferSize);
          if (HandleBuffer != NULL) {
            Status = gSmst->SmmLocateHandle (
                        ByProtocol,
                        &gAmdCpmRasOemSmmProtocolGuid,
                        NULL,
                        &HandleBufferSize,
                        HandleBuffer);
            if (!EFI_ERROR(Status))
              FreeFlag = 1;
          }
      }
  }

  if (!EFI_ERROR(Status)) {
    if (HandleBuffer != NULL) {
      for (Index = 0; Index < (HandleBufferSize / sizeof(EFI_HANDLE)); Index++) {
          Status = gSmst->SmmHandleProtocol (
                                    HandleBuffer[Index],
                                    &gAmdCpmRasOemSmmProtocolGuid,
                                    (VOID **)&AmdRasOemProtocol);
        if(!EFI_ERROR(Status)){
          AmdRasOemProtocol->OemErrorLogEventPcie (GenPcieAerErrEntry);
        }
      }
    }
  }

  if ((FreeFlag == 1) && (HandleBuffer != NULL)){
    Status = gSmst->SmmFreePool (HandleBuffer);
    ASSERT_EFI_ERROR (Status);
  }

  CopyMem ((VOID *) PcieErrStatusBlk, (VOID *) TempCommBuffer, TempCommBufferSize);

  return Status;
}
