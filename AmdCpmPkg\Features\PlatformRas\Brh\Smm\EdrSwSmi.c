/*****************************************************************************
 *
 * Copyright (C) 2018-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */


/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Library/BaseMemoryLib.h>
#include <Library/BaseLib.h>
#include <Library/IoLib.h>
#include "AmdPlatformRasBrhSmm.h"
#include <AmdCpmSmm.h>
#include <Library/NbioHandleLib.h>
#include <Library/NbioSmuBrhLib.h>
#include <Library/MpioInitLib.h>
#include <MpioLib.h>
#include <Library/IdsLib.h>
#include <FabricRegistersBrh.h>
#include <Library/FabricRegisterAccLib.h>
#include <AmdSoc.h>
#include <Library/SmmMemLib.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
#define RSMU_SW_STRAPRX_ADDR_PCIE0_INDEX    0x90467AC
#define RSMU_SW_STRAPRX_ADDR_PCIE0_DATA     0x90467B0

#define FUSE_INSTANCE_MULTIPLIER            0x1000
#define FUSE_ACCESS_LOCATION                0xFFFE0000

#define WRP_MISC_STRAP_RESERVED_INDEX       0xa9  ///< WRP_MISC_STRAP_RESERVED
#define WRP_MISC_STRAP_RESERVED_SIZE        31    ///< WriteDis = N, DefaultVal = 0x0

#define SMN_NBIO0N0_NB_PROG_DEVICE_REMAP_ADDRESS  0x13b100b8UL
#define SMN_NBIO0PCIE0_PCIE_CNTL_ADDRESS          0x1A380040UL

#define PCIE_ERR_CNTL_PRIV_SURP_DIS_VEC_OFFSET        24

/// Bitfield Description : Controls if HwInit fields are read-only.
#define PCIE_CNTL_HWINIT_WR_LOCK_OFFSET                        0
#define PCIE_CNTL_HWINIT_WR_LOCK_WIDTH                         1
#define PCIE_CNTL_HWINIT_WR_LOCK_MASK                          0x1

#define SMN_PCIE0PortASerr_ACTION_CONTROL_ADDRESS                       0x13b20264UL
#define SMN_PCIE0PortAExtFatal_ACTION_CONTROL_ADDRESS                   0x13b20274UL
#define SMN_PCIE0PortAExtNonFatal_ACTION_CONTROL_ADDRESS                0x13b20278UL
#define SMN_PCIE0PortAExtCorr_ACTION_CONTROL_ADDRESS                    0x13b2027cUL

#define SMN_PCIE2PortASerr_ACTION_CONTROL_ADDRESS                       0x1d42010cUL
#define SMN_PCIE2PortAExtFatal_ACTION_CONTROL_ADDRESS                   0x1d42011cUL
#define SMN_PCIE2PortAExtNonFatal_ACTION_CONTROL_ADDRESS                0x1d420120UL
#define SMN_PCIE2PortAExtCorr_ACTION_CONTROL_ADDRESS                    0x1d420124UL

#define SMN_NBIF1PortASerr_ACTION_CONTROL_ADDRESS                       0x13b204e4UL
#define SMN_NBIF1PortAExtFatal_ACTION_CONTROL_ADDRESS                   0x13b204f4UL
#define SMN_NBIF1PortAExtNonFatal_ACTION_CONTROL_ADDRESS                0x13b204f8UL
#define SMN_NBIF1PortAExtCorr_ACTION_CONTROL_ADDRESS                    0x13b204fcUL

#define PCIE_CONTROLLER_ACTION_CONTROL_OFFSET  0x120
#define ACTION_CONTROL_IntrGenSel_MASK         0x6

#define DF_CFGBASEADDRESS                                      ((0<<12)|(0xC80))
#define DF_CFGLIMITADDRESS                                     ((0<<12)|(0xC84))
#define DF_FABRICBLOCKINSTANCEINFORMATION3_IOS                 ((FABRICBLOCKINSTANCEINFORMATION3_IOS_FUNC << 12) | (FABRICBLOCKINSTANCEINFORMATION3_IOS_REG))

#define DEVFUNC(d, f) ((((UINT8) d) << 3) | ((UINT8) f))
#ifndef WRAP_SPACE
  #define  WRAP_SPACE(HANDLE, WRAPPER, ADDRESS)   (ADDRESS + (HANDLE << 20) + (WRAPPER << 22))
#endif

#define MAX_PCIE_CORE_PORT_SUPPORT           (9)
#define NUM_OF_PCIE_GEN5_9X16_CNTLR_PER_NBIO (4)
#define BRH_IOS0_INSTANCE_ID                 (0x28)

// Correspondence table between IOM and NBIO RbIndex
#define IOM_NUM_TO_NBIO_RB_INDEX(iom)  (UINT8)(((iom & BIT0) << 2) | ((iom & BIT2) >> 1) | ((iom & BIT0) ? (~((iom & BIT1) >> 1) & BIT0) : ((iom & BIT1) >> 1)))
/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */
extern ROOTPORT_VS_NVMESLOT_DESC RootPortVsNvmeSlotMappingTable[TOTAL_NVME_SLOT];
extern UINT32 BrhIomsInstanceIds[];
/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
BOOLEAN
ReConfigRErCmdReg (
  VOID
  );

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */

typedef union {
  struct {
    UINT32                                                      RE:1 ; ///<
    UINT32                                                      WE:1 ; ///<
    UINT32                                                        :6 ; ///<
    UINT32                                              SegmentNum:8 ; ///<
    UINT32                                              BusNumBase:8 ; ///<
    UINT32                                                        :8 ; ///<
  } Field;                                                             ///<
  UINT32 Value;                                                        ///<
} CfgBaseAddress_STRUCT;

typedef union {
  struct {
    UINT32                                             DstFabricID:12; ///<
    UINT32                                                        :4 ; ///<
    UINT32                                             BusNumLimit:8 ; ///<
    UINT32                                                        :8 ; ///<
  } Field;                                                             ///<
  UINT32 Value;                                                        ///<
} CfgLimitAddress_STRUCT;

/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                          T A B L E    D E F I N I T I O N S
 *----------------------------------------------------------------------------------------
 */
/*********************************************************************************
 * Name: WritePcieStrap
 *
 * Description
 *   Routine to write pcie soft straps
 *
 * Arguments:
 *   BusNumber        : Nbio bus number
 *   NbioInstance     : Nbio instance number
 *   StrapIndex       : Strap index
 *   Value            : Pointer to byte
 *   Wrapper          : Pcie wrapper number
 *
 * Returns:
 *   N/A
 *
 *********************************************************************************/
VOID
WritePcieStrap (
  IN   UINT32       BusNumber,
  IN   UINT8        NbioInstance,
  IN   UINT16       StrapIndex,
  IN   UINT32       Value,
  IN   UINT8        Wrapper
  )
{
  UINT32 Index;
  UINT8  InstanceNumber;

  Index = FUSE_ACCESS_LOCATION;
  Index += StrapIndex;
  InstanceNumber = NbioInstance + Wrapper * 4;

  RasSmnWrite (
    BusNumber,
    RSMU_SW_STRAPRX_ADDR_PCIE0_INDEX + FUSE_INSTANCE_MULTIPLIER * InstanceNumber,
    &Index
    );

  RasSmnWrite (
    BusNumber,
    RSMU_SW_STRAPRX_ADDR_PCIE0_DATA + FUSE_INSTANCE_MULTIPLIER * InstanceNumber,
    &Value
    );
}

/*********************************************************************************
 * Name: ReadPcieStrap
 *
 * Description
 *   Routine to read pcie soft straps
 *
 * Arguments:
 *   BusNumber        : Nbio bus number
 *   NbioInstance     : Nbio instance number
 *   StrapIndex       : Strap index
 *   Value            : Pointer to store read byte(s)
 *   Wrapper          : Pcie wrapper number
 *
 * Returns:
 *   N/A
 *
 *********************************************************************************/
VOID
ReadPcieStrap (
  IN   UINT32       BusNumber,
  IN   UINT8        NbioInstance,
  IN   UINT16       StrapIndex,
  OUT  UINT32       *Value,
  IN   UINT8        Wrapper
  )
{
  UINT32 Index;
  UINT8  InstanceNumber;

  Index = FUSE_ACCESS_LOCATION;
  Index += StrapIndex;
  InstanceNumber = NbioInstance + Wrapper * 4;

  RasSmnWrite (
    BusNumber,
    RSMU_SW_STRAPRX_ADDR_PCIE0_INDEX + FUSE_INSTANCE_MULTIPLIER * InstanceNumber,
    &Index
    );

  RasSmnRead (
    BusNumber,
    RSMU_SW_STRAPRX_ADDR_PCIE0_DATA + FUSE_INSTANCE_MULTIPLIER * InstanceNumber,
    Value
    );
}

/*********************************************************************************
 * Name: EnableSurpDnStrap
 *
 * Description
 *   Routine to enable the Surprise Down PCIe soft strap
 *
 * Arguments:
 *   PciPortAddr      : Address of root port
 *   PcieSmnAddr      : Address of SMN
 *   Port             : Port number
 *
 * Returns:
 *   EFI_SUCCESS
 *
 *********************************************************************************/
EFI_STATUS
EnableSurpDnStrap (
    IN PCI_ADDR  PciPortAddr,
    IN UINT32    PcieSmnAddr,
    IN UINT8     Port
    )
{
  UINT32    Value;

  RasSmnRead (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus), (PcieSmnAddr + (Port << 12)), &Value);
  Value &= 0x00FFFFFF;
  Value |= 0x26 << PCIE_ERR_CNTL_PRIV_SURP_DIS_VEC_OFFSET;
  RasSmnWrite (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus), (PcieSmnAddr + (Port << 12)), &Value);

  return EFI_SUCCESS;
}

/*********************************************************************************
 * Name: GetIosInstanceIdFromFabricId
 *
 * Description
 *   Retrieves the IOS Instance ID from the specified IOS Fabric ID
 *
 * Arguments:
 *   FabricId   : IOS Fabric ID (Valid values: 0x20 ~ 0x27)
 *
 * Returns:
 *   0x28~0x2F. IOS Instance ID.
 *   0xFF.      Failed to get IOS Instance ID.
 *
 *********************************************************************************/
STATIC
UINT8
GetIosInstanceIdFromFabricId (
  IN     UINT32 FabricId
  )
{
  UINT8                                                     Index;
  FABRIC_BLOCK_INSTANCE_INFORMATION3__IOS_REGISTER          InstanceInfo3;

  //IOS Fabric ID: 0x20 => IOS Instance ID: 0x28
  //IOS Fabric ID: 0x21 => IOS Instance ID: 0x29
  //IOS Fabric ID: 0x22 => IOS Instance ID: 0x2A
  //IOS Fabric ID: 0x23 => IOS Instance ID: 0x2B
  //IOS Fabric ID: 0x24 => IOS Instance ID: 0x2C
  //IOS Fabric ID: 0x25 => IOS Instance ID: 0x2D
  //IOS Fabric ID: 0x26 => IOS Instance ID: 0x2E
  //IOS Fabric ID: 0x27 => IOS Instance ID: 0x2F
  for (Index = 0; Index < BRH_MAX_IOMS_PER_DIE; Index++) {
    InstanceInfo3.Value = FabricRegisterAccRead (0, 0, (((DF_FABRICBLOCKINSTANCEINFORMATION3_IOS) >> 12) & 0x7), (DF_FABRICBLOCKINSTANCEINFORMATION3_IOS & 0xFFF), BRH_IOS0_INSTANCE_ID + Index);
    if (InstanceInfo3.Field.BlockFabricID == FabricId) {
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS - EDR] %a Given Fabric ID: 0x%x => IOS Instance ID: 0x%x\n",
        __FUNCTION__, FabricId,  (BRH_IOS0_INSTANCE_ID + Index));
      return (UINT8) (BRH_IOS0_INSTANCE_ID + Index);
    }
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS - EDR] %a Failed to get IOS instance id from given FabricId 0x%x\n", __FUNCTION__, FabricId);

  return 0xFF;
}

/*********************************************************************************
 * Name: ConvertRpPciAddrToNbioWrapperPort
 *
 * Description
 *   Routine to convert root port PCI address into corresponding NBIO, Wrapper,
 *    and Port numbers.
 *
 * Arguments:
 *   PciPortAddr      : Address of root port
 *   Iom              : pointer to resulting IOHUBS instance.
 *                      Valid values: 0:7.
 *   Wrapper          : pointer to resulting Wrapper instance (PCIe Core number).
 *                      Valid values: 0, 1, 2, 3, 5.
 *   Port             : pointer to resulting Port instance (PCIe Port number in a PCIe Core).
 *                      Valid values: PCIeCore(0, 1, 2, 3) => 0:8, PCIeCore(5) => 0:7.
 *
 * Returns:
 *   EFI_SUCCESS
 *
 *********************************************************************************/
EFI_STATUS
ConvertRpPciAddrToNbioWrapperPort (
    IN  PCI_ADDR  PciPortAddr,
    OUT UINT8     *Iom,
    OUT UINT8     *Wrapper,
    OUT UINT8     *Port
    )
{
  UINT32                  Value;
  UINT8                   Pcie;
  UINT8                   PortCount;
  UINT8                   InstCnt;
  UINT32                  SmnAddress;
  BOOLEAN                 Found;
  CfgBaseAddress_STRUCT   CfgBaseAddrValue;
  CfgLimitAddress_STRUCT  CfgLimitAddrValue;
  UINTN                   DfCfgNbioBaseAddress;
  UINTN                   DfCfgNbioLimitAddress;
  UINTN                   DfCfgAddrCtrlFunction;
  UINTN                   DfCfgAddrCtrlOffset;
  UINT8                   TmpIom;
  UINT8                   Nbio;
  UINT8                   TmpIohc;
  UINT8                   Iohc;
  UINT32                  RegOffset;
  UINT8                   MaxPortCount;
  UINT32                  RegEax;
  UINT64                  SocFamilyID;
  UINT8                   CpuModStep;
  UINT8                   MaxInstCnt;

  RegEax = 0;
  AsmCpuid (0x80000001, &RegEax, NULL, NULL, NULL);
  SocFamilyID = (RegEax & RAW_FAMILY_ID_MASK);
  CpuModStep = 0;
  if ((SocFamilyID == F1A_BRH_RAW_ID) || (SocFamilyID == F1A_BRHD_RAW_ID)) {
    CpuModStep = (RegEax & (CPUID_BASE_MODEL_MASK | CPUID_STEPPING_MASK));
  }

  //Determine total number of instances of DF::CfgBaseAddress Register
  if (CpuModStep == 0x00 /*A0*/) {
    MaxInstCnt = 8;
  } else {
    MaxInstCnt = 16;
  }

  // Find IOM instance for given bus number
  Found = FALSE;
  InstCnt = 0;
  TmpIom = 0;

  for (InstCnt = 0; InstCnt < MaxInstCnt; InstCnt++) {
    DfCfgNbioBaseAddress = DF_CFGBASEADDRESS + sizeof (UINT64) * InstCnt;
    DfCfgAddrCtrlFunction = (DfCfgNbioBaseAddress >> 12) & 0x7;
    DfCfgAddrCtrlOffset = DfCfgNbioBaseAddress & 0xFFF;
    CfgBaseAddrValue.Value =  FabricRegisterAccRead (0, 0, DfCfgAddrCtrlFunction, DfCfgAddrCtrlOffset, BrhIomsInstanceIds[0]);
    if ((CfgBaseAddrValue.Value & 3) != 3) continue; // skip if this CfgBaseAddress register is not enabled
    if (PciPortAddr.Address.Segment != (CfgBaseAddrValue.Field.SegmentNum & ~BIT7)) continue;
    DfCfgNbioLimitAddress = DF_CFGLIMITADDRESS + sizeof (UINT64) * InstCnt;
    DfCfgAddrCtrlFunction = (DfCfgNbioLimitAddress >> 12) & 0x7;
    DfCfgAddrCtrlOffset = DfCfgNbioLimitAddress & 0xFFF;
    CfgLimitAddrValue.Value =  FabricRegisterAccRead (0, 0, DfCfgAddrCtrlFunction, DfCfgAddrCtrlOffset, BrhIomsInstanceIds[0]);
    if ((PciPortAddr.Address.Bus >= CfgBaseAddrValue.Field.BusNumBase) && (PciPortAddr.Address.Bus <= CfgLimitAddrValue.Field.BusNumLimit)) {
      TmpIom = GetIosInstanceIdFromFabricId (CfgLimitAddrValue.Field.DstFabricID & ~BIT7 /*mask remote die indicator*/) & 0x7;
      Found = TRUE;
      break;
    }
  }

  if (Found == FALSE) {
    return EFI_NOT_FOUND;
  }

  //Each IOHC has only one PCIe Gen5 9x16 controller.
  //Except NBIO0 IOHC2, it has two PCIe controller: one PCIe Gen5 9x16 controller and one PCIe Gen4 bonus lanes controller
  //IOM 0 => NBIO 0, IOHC 0, PCIE 0
  //IOM 1 => NBIO 0, IOHC 3, PCIE 3
  //IOM 2 => NBIO 0, IOHC 2, PCIE 1, PCIE 5
  //IOM 3 => MBIO 0, IOHC 1, PCIE 2
  //IOM 4 => NBIO 1, IOHC 0, PCIE 0
  //IOM 5 => NBIO 1, IOHC 3, PCIE 3
  //IOM 6 => NBIO 1, IOHC 2, PCIE 1
  //IOM 7 => MBIO 1, IOHC 1, PCIE 2
  Nbio =    (TmpIom >> 2) & BIT0;  //Nbio = TmpIom[BIT2]
  TmpIohc = (TmpIom & (BIT1|BIT0));
  Iohc =    (TmpIom & BIT0) ? (TmpIohc ^ BIT1) : TmpIohc;
  Pcie =    (((Iohc & BIT0) << 1) | (((Iohc & BIT1) >> 1)));

  // Find wrapper and port number for given PCI address of root port
  Found = FALSE;
  MaxPortCount = ((TmpIom == 2) ? MAX_PCIE_PORT_SUPPORT : MAX_PCIE_CORE_PORT_SUPPORT);
  for (PortCount = 0; PortCount < MaxPortCount; PortCount++) {
    SmnAddress = ((Iohc & BIT0) == 0) ? NBIO0_IOHUB0_PCIE_P00_NB_PROG_DEVICE_REMAP : NBIO0_IOHUB1_PCIE_P00_NB_PROG_DEVICE_REMAP;
    SmnAddress = SmnAddress + (IOHC_SMN_ADDR_OFFSET * (Iohc >> 1)) + (NBIO_SMN_ADDR_OFFSET * Nbio);
    RegOffset = SmnAddress + (PortCount * sizeof(UINT32));
    RasSmnRead (SMN_SEG_BUS(PciPortAddr.Address.Segment, PciPortAddr.Address.Bus), RegOffset, &Value);
    if (((Value & 0x7) == PciPortAddr.Address.Function) && (((Value >> 3) & 0x1F) == PciPortAddr.Address.Device)) {
      Found = TRUE;
      *Iom = TmpIom;
      *Wrapper = (PortCount < MAX_PCIE_CORE_PORT_SUPPORT) ? Pcie : 5/*PCIe 5*/;
      *Port    = (PortCount < MAX_PCIE_CORE_PORT_SUPPORT) ? PortCount : (PortCount - MAX_PCIE_CORE_PORT_SUPPORT) /*This is the port on PCIe 5*/;
      break;
    }
  }

  if (Found) {
    return EFI_SUCCESS;
  } else {
    return EFI_NOT_FOUND;
  }
}

/*********************************************************************************
 * Name: GetPcieCtrlSmnAddr
 *
 * Description
 *   Routine to get the SMN address of PCIECORE::PCIE_CNTL per IOS Number and PCIe core number
 *
 * Arguments:
 *   Iom   : IOM number in a socket (valid value: 0:7)
 *   PcieCore : PCIe Gen5 9x16 controller number in a NBIO (valid value: 0:3)
 *
 * Returns:
 *   0: Fail to get the address
 *   Other values: the SMN address of PCIECORE::PCIE_CNTL
 *
 *********************************************************************************/
UINT32
GetPcieCtrlSmnAddr (
  IN  UINT8     Iom,
  IN  UINT8     PcieCoreNumInNbio
  )
{
  UINT8           Nbio;
  UINT32          PcieCtrlSmnAddr;

  if ((Iom > 7) || (PcieCoreNumInNbio > 5) || (PcieCoreNumInNbio == 4)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS - EDR] %a failed. Iom = %d, PcieCoreNumInNbio = %d\n", __FUNCTION__, Iom, PcieCoreNumInNbio);
    return 0;
  }

  Nbio = (Iom >> 2) & BIT0;  //Nbio = Iom[BIT2]

  switch (PcieCoreNumInNbio) {
    case 0:
      PcieCtrlSmnAddr = 0x1A380040UL;
      break;
    case 1:
      PcieCtrlSmnAddr = 0x1A480040UL;
      break;
    case 2:
      PcieCtrlSmnAddr = 0x1A780040UL;
      break;
    case 3:
      PcieCtrlSmnAddr = 0x1A880040UL;
      break;
    case 5: /*PCIe 5. Bonus Lanes*/
      PcieCtrlSmnAddr = 0x1AB80040UL;
      break;
    default:
      //Should not be here
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS - EDR] %a failed. PcieCoreNumInNbio = %d\n", __FUNCTION__, PcieCoreNumInNbio);
      return 0;
      break;
  }

  PcieCtrlSmnAddr += (Nbio * NBIO_SMN_ADDR_OFFSET);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS - EDR] %a PcieCtrlSmnAddr = 0x%0lx @ Iom: %d, PcieCoreNumInNbio: %d\n", __FUNCTION__, PcieCtrlSmnAddr, Iom, PcieCoreNumInNbio);

  return PcieCtrlSmnAddr;
}

/*********************************************************************************
 * Name: GetPcieErrCtrlSmnAddr
 *
 * Description
 *   Routine to get the SMN address of PCIEPORT::PCIE_ERR_CNTL per IOS Number and PCIe core number
 *
 * Arguments:
 *   IosNum   : IOM number in a socket (valid value: 0:7)
 *   PcieCore : PCIe Gen5 9x16 controller number in a NBIO (valid value: 0:3)
 *
 * Returns:
 *   0: Fail to get the address
 *   Other values: the SMN address of PCIEPORT::PCIE_ERR_CNTL
 *
 *********************************************************************************/
UINT32
GetPcieErrCtrlSmnAddr (
  IN  UINT8     Iom,
  IN  UINT8     PcieCoreNumInNbio
  )
{
  UINT8           Nbio;
  UINT32          PcieErrCtrlSmnAddr;

  if ((Iom > 7) || (PcieCoreNumInNbio > 5) || (PcieCoreNumInNbio == 4)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS - EDR] %a failed. Iom = %d, PcieCoreNumInNbio = %d\n", __FUNCTION__, Iom, PcieCoreNumInNbio);
    return 0;
  }

  Nbio = (Iom >> 2) & BIT0;  //Nbio = Iom[BIT2]

  switch (PcieCoreNumInNbio) {
    case 0:
      PcieErrCtrlSmnAddr = 0x1A3401A8UL;
      break;
    case 1:
      PcieErrCtrlSmnAddr = 0x1A4401A8UL;
      break;
    case 2:
      PcieErrCtrlSmnAddr = 0x1A7401A8UL;
      break;
    case 3:
      PcieErrCtrlSmnAddr = 0x1A8401A8UL;
      break;
    case 5: /*PCIe 5. Bonus Lanes*/
      PcieErrCtrlSmnAddr = 0x1AB401A8UL;
      break;
    default:
      //Should not be here
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS - EDR] %a failed. PcieCoreNumInNbio = %d\n", __FUNCTION__, PcieCoreNumInNbio);
      return 0;
      break;
  }

  PcieErrCtrlSmnAddr += (Nbio * NBIO_SMN_ADDR_OFFSET);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS - EDR] %a PcieErrCtrlSmnAddr = 0x%0lx @ Iom: %d, PcieCoreNumInNbio: %d\n", __FUNCTION__, PcieErrCtrlSmnAddr, Iom, PcieCoreNumInNbio);

  return PcieErrCtrlSmnAddr;
}

/*********************************************************************************
 * Name: EdrDsmDpcEnReqFromOs
 *
 * Description
 *   The OS may evaluate this _DSM more than once, so it is imperative that subsequent calls simply return to the operating
 *   system without changing any state.
 *
 *   If the operating system does not support DPC or Error Disconnect Recover, does not evaluate the "Downsteam Port Containment Enable Request from OS" _DSM
 *   Or the firmware detects an unsupported processor (e.g. Rome), then the platform / root port remains in Hotplug Surprise mode.
 *   The operating system must evaluate the "Downsteam Port Containment Enable Request from OS" _DSM for each hotplug-capable root port or switch in the system.
 *
 * Arguments:
 *   ToDo
 *
 * Returns:
 *   ToDo
 *
 *********************************************************************************/
EFI_STATUS
EdrDsmDpcEnReqFromOs (
  IN PCI_ADDR  PciPortAddr,
  IN BOOLEAN   DpcEn
  )
{
  UINT8         PcieCapPtr;
  UINT16        DpcCapPtr;
  UINT16        AerCapPtr;
  UINT32        Data32;
  UINT16        Data16;
  UINT8         Iom;
  UINT8         Pci;
  UINT8         Port;
  UINT32        Value;
  DPC_CNTL_REG  DpcCntlReg;
  EFI_STATUS    Status;
  UINT16        SfiCapPtr;
  BOOLEAN       SfiEnabled;
  UINT64        PciSegAddr;
  UINT32        PcieCtrlSmnAddr;
  UINT32        PcieErrCtrlSmnAddr;

  PciSegAddr = PCI_SEGMENT_LIB_ADDRESS (PciPortAddr.Address.Segment,
                                        PciPortAddr.Address.Bus,
                                        PciPortAddr.Address.Device,
                                        PciPortAddr.Address.Function,
                                        PciPortAddr.Address.Register);

  PcieCapPtr = RasFindPciCapability (PciPortAddr.AddressValue, PCIE_CAP_ID);
  DpcCapPtr = RasFindPcieExtendedCapability (PciPortAddr.AddressValue, DPC_EXT_CAP_ID, 0xFFFF);
  AerCapPtr = RasFindPcieExtendedCapability (PciPortAddr.AddressValue, PCIE_EXT_AER_CAP_ID, 0xFFFF);

  if ((PcieCapPtr == 0) || (DpcCapPtr == 0) || (AerCapPtr == 0)) {
    return EFI_NOT_FOUND;
  }

  SfiEnabled = FALSE;
  SfiCapPtr = RasFindPcieExtendedCapability (PciPortAddr.AddressValue, SFI_EXT_CAP_ID, 0xFFFF);
  if (SfiCapPtr != 0) {
    Data16 = PciSegmentRead16 (PciSegAddr + SfiCapPtr + SFI_CONTROL_REG);
    if ((Data16 & SFI_HPS_SUPPRESS) != 0) {
      SfiEnabled = TRUE;
    }
  }

// Disable DPC
  if (!DpcEn) {
    // Based on PCI FW 3.3 Spec, clear SFI_HPS_SUPPRESS bit
    if (SfiEnabled) {
      Data16 = Data16 & (~SFI_HPS_SUPPRESS);
      PciSegmentWrite16(PciSegAddr + SfiCapPtr + SFI_CONTROL_REG, Data16);
    }
    DpcCntlReg.Value = 0; //(DpcCntlReg.Field.DpcTriggerEnable = 0;)
    PciSegmentWrite16(PciSegAddr + DpcCapPtr + DPC_CONTROL_REG, DpcCntlReg.Value);
    return EFI_SUCCESS;
  }

// Enable DPC
  //  1.2. Clear DPC_CONTROL[DL_active ERR_COR Signaling]. This causes the processor not to signal ERR_COR when DL_active asserts.
  DpcCntlReg.Value = PciSegmentRead16 (PciSegAddr + DpcCapPtr + DPC_CONTROL_REG);
  DpcCntlReg.Field.DlActiveErrCorEnable = 0;
  //  1.3. Set DPC_CONTROL[DPC Trigger Enable] to 0x1. This enables eDPC triggering on fatal errors.
  DpcCntlReg.Field.DpcTriggerEnable = 1;

  Data32 = PciSegmentRead32 (PciSegAddr + PcieCapPtr + PCIE_SLOT_CAP_REGISTER);
  if ((Data32 & BIT6) && SfiEnabled) {
    //SFI
    DpcCntlReg.Field.DpcErrCorEnable = 0;
  } else if ((Data32 & BIT6) && (Data32 & BIT5) && !(Data32 & BIT0)) { //BIT6: HOTPLUG_CAPABLE and BIT5: HOTPLUG_SURPRISE and BIT0: ATTN_BUTTON_PRESENT=0
    //EDR
    //  1.4. Clear DPC_CONTROL[DPC ERR_COR Enable]. The causes the processor not to signal an ERR_COR on an error that triggers DPC.
    DpcCntlReg.Field.DpcErrCorEnable = 0;
  } else {
    DpcCntlReg.Field.DpcErrCorEnable = 1;
  }
  PciSegmentWrite16(PciSegAddr + DpcCapPtr + DPC_CONTROL_REG, DpcCntlReg.Value);

  //1. If NBIO0PCIECORE0P0CFGx0000006C[HOTPLUG_CAPABLE] = 1 (i.e. this is a hotplug-capable slot) and
  //NBIO0PCIECORE0P0CFGx0000006C[HOTPLUG_SURPRISE] = 1 (this port is still configured for Hotplug Surprise), then configure the slot as follows:
  Data32 = PciSegmentRead32 (PciSegAddr + PcieCapPtr + PCIE_SLOT_CAP_REGISTER);
  if ((Data32 & BIT6) && (Data32 & BIT5)) { //BIT6: HOTPLUG_CAPABLE, BIT5: HOTPLUG_SURPRISE
    Status = ConvertRpPciAddrToNbioWrapperPort (PciPortAddr, &Iom, &Pci, &Port);
    if (!EFI_ERROR (Status)) {
      PcieCtrlSmnAddr = GetPcieCtrlSmnAddr(Iom, Pci);
      if (PcieCtrlSmnAddr != 0) {
        // Unlock HWINIT
        RasSmnRead (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus), PcieCtrlSmnAddr, &Value);
        Value &=  (UINT32) ~(PCIE_CNTL_HWINIT_WR_LOCK_MASK);
        RasSmnWrite (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus), PcieCtrlSmnAddr, &Value);
        //  1.1. Clear NBIO0PCIECORE0P0CFGx0000006C[HOTPLUG_SURPRISE].
        Data32 &= (~BIT5);
        PciSegmentWrite32 (PciSegAddr + PcieCapPtr + PCIE_SLOT_CAP_REGISTER, Data32);
        // Lock HWINIT
        Value |= (1 << PCIE_CNTL_HWINIT_WR_LOCK_OFFSET);
        RasSmnWrite (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus), PcieCtrlSmnAddr, &Value);

        PcieErrCtrlSmnAddr = GetPcieErrCtrlSmnAddr(Iom, Pci);
        // Enable Surprise Down Strap
        EnableSurpDnStrap (PciPortAddr, PcieErrCtrlSmnAddr, Port);
      }
    }
  }

//  1.5. Set up RP PIO so that errors on config cycles and all CTOs will be ignored and return all FFs.
//    1.5.1. Set PCIERCCFG::PCIE_RP_PIO_MASK=5'b{11111}, clear all other bits in this register. CTO errors will be reported via AER.
//    Data32 = PciSegmentRead32 (PciSegAddr + DpcCapPtr + RP_PIO_MASK_REG);
  Data32 = 0x70707;
  PciSegmentWrite32 (PciSegAddr + DpcCapPtr + RP_PIO_MASK_REG, Data32);

//  1.6. Set up RP PIO so that unmasked errors will trigger eDPC.
//    1.6.1 Set PCIERCCFG::PCIE_RP_PIO_SEVERITY to all FF.
//    Data32 = PciSegmentRead32 (PciSegAddr + DpcCapPtr + RP_PIO_SEVERITY_REG);
  Data32 = 0xFFFFFFFF;
  PciSegmentWrite32 (PciSegAddr + DpcCapPtr + RP_PIO_SEVERITY_REG, Data32);

//  Unmask Surprise Down errors by clearing PCIE_UNCORR_ERR_MASK[SURPDN_ERR_MASK]
  Data32 = PciSegmentRead32 (PciSegAddr + AerCapPtr + PCIE_UNCORR_MASK_PTR);
  Data32 &= 0xFFFFFFDF;
  PciSegmentWrite32 (PciSegAddr + AerCapPtr + PCIE_UNCORR_MASK_PTR, Data32);

  //  2. Return success status to the OS.
  return EFI_SUCCESS;
}

/*********************************************************************************
 * Name: ReConfigAerReportingPath
 *
 * Description
 *     Reconfiguring the AER reporting path for FW First but allow OS First to all
 *     extrernal PCIE ports
 *
 * Arguments:
 *   PciPortAddr      : Address of root port
 *
 * Returns:
 *   EFI_SUCCESS
 *
 *********************************************************************************/
EFI_STATUS
ReConfigAerReportingPath (
  IN PCI_ADDR  PciPortAddr
  )
{
  UINT8                       Port;
  UINT8                       WrapId;
  UINT16                      PcieCntlOffset;
  UINT16                      PciePortOffset;
  UINT32                      IoHubSmnAddrOffset;
  UINT32                      ActionCtrlValue;
  CfgBaseAddress_STRUCT       CfgBaseAddrValue;
  CfgLimitAddress_STRUCT      CfgLimitAddrValue;
  UINTN                       DfCfgNbioBaseAddress;
  UINTN                       DfCfgNbioLimitAddress;
  UINTN                       DfCfgAddrCtrlFunction;
  UINTN                       DfCfgAddrCtrlOffset;
  BOOLEAN                     Found;
  UINT8                       Pcie;
  UINT8                       NbioRbIndex;
  UINT8                       InstCnt;
  UINT8                       TmpIom;
  UINT8                       TmpIohc;
  UINT8                       Iohc;
  UINT32                      RegEax;
  UINT64                      SocFamilyID;
  UINT8                       CpuModStep;
  UINT8                       MaxInstCnt;

  RegEax = 0;
  AsmCpuid (0x80000001, &RegEax, NULL, NULL, NULL);
  SocFamilyID = (RegEax & RAW_FAMILY_ID_MASK);
  CpuModStep = 0;
  if ((SocFamilyID == F1A_BRH_RAW_ID) || (SocFamilyID == F1A_BRHD_RAW_ID)) {
    CpuModStep = (RegEax & (CPUID_BASE_MODEL_MASK | CPUID_STEPPING_MASK));
  }

  //Determine total number of instances of DF::CfgBaseAddress Register
  if (CpuModStep == 0x00 /*A0*/) {
    MaxInstCnt = 8;
  } else {
    MaxInstCnt = 16;
  }

  // Find IOM instance for given bus number
  Found = FALSE;
  InstCnt = 0;
  TmpIom = 0;

  for (InstCnt = 0; InstCnt < MaxInstCnt; InstCnt++) {
    DfCfgNbioBaseAddress = DF_CFGBASEADDRESS + sizeof (UINT64) * InstCnt;
    DfCfgAddrCtrlFunction = (DfCfgNbioBaseAddress >> 12) & 0x7;
    DfCfgAddrCtrlOffset = DfCfgNbioBaseAddress & 0xFFF;
    CfgBaseAddrValue.Value =  FabricRegisterAccRead (0, 0, DfCfgAddrCtrlFunction, DfCfgAddrCtrlOffset, BrhIomsInstanceIds[0]);
    if ((CfgBaseAddrValue.Value & 3) != 3) continue; // skip if this CfgBaseAddress register is not enabled
    if (PciPortAddr.Address.Segment != (CfgBaseAddrValue.Field.SegmentNum & ~BIT7)) continue;
    DfCfgNbioLimitAddress = DF_CFGLIMITADDRESS + sizeof (UINT64) * InstCnt;
    DfCfgAddrCtrlFunction = (DfCfgNbioLimitAddress >> 12) & 0x7;
    DfCfgAddrCtrlOffset = DfCfgNbioLimitAddress & 0xFFF;
    CfgLimitAddrValue.Value =  FabricRegisterAccRead (0, 0, DfCfgAddrCtrlFunction, DfCfgAddrCtrlOffset, BrhIomsInstanceIds[0]);
    if ((PciPortAddr.Address.Bus >= CfgBaseAddrValue.Field.BusNumBase) && (PciPortAddr.Address.Bus <= CfgLimitAddrValue.Field.BusNumLimit)) {
      TmpIom = GetIosInstanceIdFromFabricId (CfgLimitAddrValue.Field.DstFabricID & ~BIT7 /*mask remote die indicator*/) & 0x7;
      Found = TRUE;
      break;
    }
  }

  if (Found == FALSE) {
    return EFI_NOT_FOUND;
  }

  //Find IOHUBS instance for given bus number
  //  Each IOHC has only one PCIe Gen5 9x16 controller.
  //  Except NBIO0 IOHC2, it has two PCIe controller: one PCIe Gen5 9x16 controller and one PCIe Gen4 bonus lanes controller
  //  IOHUBS 0 => NBIO 0, IOHC 0, PCIE 0
  //  IOHUBS 1 => NBIO 0, IOHC 3, PCIE 3
  //  IOHUBS 2 => NBIO 0, IOHC 2, PCIE 1, PCIE 5
  //  IOHUBS 3 => MBIO 0, IOHC 1, PCIE 2
  //  IOHUBS 4 => NBIO 1, IOHC 0, PCIE 0
  //  IOHUBS 5 => NBIO 1, IOHC 3, PCIE 3
  //  IOHUBS 6 => NBIO 1, IOHC 2, PCIE 1
  //  IOHUBS 7 => NBIO 1, IOHC 1, PCIE 2
  TmpIohc = (TmpIom & (BIT1|BIT0));
  Iohc =    (TmpIom & BIT0) ? (TmpIohc ^ BIT1) : TmpIohc;
  Pcie =    (((Iohc & BIT0) << 1) | (((Iohc & BIT1) >> 1)));

  WrapId =  (Pcie == 5 /*PCIe5*/) ? 1 : 0;
  NbioRbIndex = IOM_NUM_TO_NBIO_RB_INDEX(TmpIom); // Convert to NBIO RbIndex
  IoHubSmnAddrOffset = IOHC_SMN_ADDR_OFFSET * (NbioRbIndex & 0x3);
  PcieCntlOffset = PCIE_CONTROLLER_ACTION_CONTROL_OFFSET * WrapId;

  for (Port = 0; Port < ((TmpIom == 2) ? MAX_PCIE_PORT_SUPPORT : MAX_PCIE_CORE_PORT_SUPPORT) ; Port++) {
    PciePortOffset = PCIE_ACTION_CONTROL_OFFSET * Port;
    if (NbioRbIndex < 4) {
      // Serr_ACTION_CONTROL
      RasSmnRead (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus),
                  SMN_PCIE0PortASerr_ACTION_CONTROL_ADDRESS + IoHubSmnAddrOffset + PcieCntlOffset + PciePortOffset,
                  &ActionCtrlValue);
      ActionCtrlValue &= (~ACTION_CONTROL_IntrGenSel_MASK);
      RasSmnWrite (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus),
                   SMN_PCIE0PortASerr_ACTION_CONTROL_ADDRESS + IoHubSmnAddrOffset + PcieCntlOffset + PciePortOffset,
                   &ActionCtrlValue);

      // ExtFatal_ACTION_CONTROL
      RasSmnRead (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus),
                  SMN_PCIE0PortAExtFatal_ACTION_CONTROL_ADDRESS + IoHubSmnAddrOffset + PcieCntlOffset + PciePortOffset,
                  &ActionCtrlValue);
      ActionCtrlValue &= (~ACTION_CONTROL_IntrGenSel_MASK);
      RasSmnWrite (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus),
                   SMN_PCIE0PortAExtFatal_ACTION_CONTROL_ADDRESS + IoHubSmnAddrOffset + PcieCntlOffset + PciePortOffset,
                  &ActionCtrlValue);

      // ExtNonFatal_ACTION_CONTROL
      RasSmnRead (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus),
                  SMN_PCIE0PortAExtNonFatal_ACTION_CONTROL_ADDRESS + IoHubSmnAddrOffset + PcieCntlOffset + PciePortOffset,
                  &ActionCtrlValue);
      ActionCtrlValue &= (~ACTION_CONTROL_IntrGenSel_MASK);
      RasSmnWrite (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus),
                   SMN_PCIE0PortAExtNonFatal_ACTION_CONTROL_ADDRESS + IoHubSmnAddrOffset + PcieCntlOffset + PciePortOffset,
                   &ActionCtrlValue);

      // ExtCorr_ACTION_CONTROL
      RasSmnRead (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus),
                  SMN_PCIE0PortAExtCorr_ACTION_CONTROL_ADDRESS + IoHubSmnAddrOffset + PcieCntlOffset + PciePortOffset,
                  &ActionCtrlValue);
      ActionCtrlValue &= (~ACTION_CONTROL_IntrGenSel_MASK);
      RasSmnWrite (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus),
                   SMN_PCIE0PortAExtCorr_ACTION_CONTROL_ADDRESS + IoHubSmnAddrOffset + PcieCntlOffset + PciePortOffset,
                   &ActionCtrlValue);
    } else {
      // Serr_ACTION_CONTROL
      RasSmnRead (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus),
                  SMN_PCIE2PortASerr_ACTION_CONTROL_ADDRESS + IoHubSmnAddrOffset + PcieCntlOffset + PciePortOffset,
                  &ActionCtrlValue);
      ActionCtrlValue &= (~ACTION_CONTROL_IntrGenSel_MASK);
      RasSmnWrite (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus),
                   SMN_PCIE2PortASerr_ACTION_CONTROL_ADDRESS + IoHubSmnAddrOffset + PcieCntlOffset + PciePortOffset,
                   &ActionCtrlValue);

      // ExtFatal_ACTION_CONTROL
      RasSmnRead (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus),
                  SMN_PCIE2PortAExtFatal_ACTION_CONTROL_ADDRESS + IoHubSmnAddrOffset + PcieCntlOffset + PciePortOffset,
                  &ActionCtrlValue);
      ActionCtrlValue &= (~ACTION_CONTROL_IntrGenSel_MASK);
      RasSmnWrite (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus),
                   SMN_PCIE2PortAExtFatal_ACTION_CONTROL_ADDRESS + IoHubSmnAddrOffset + PcieCntlOffset + PciePortOffset,
                  &ActionCtrlValue);

      // ExtNonFatal_ACTION_CONTROL
      RasSmnRead (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus),
                  SMN_PCIE2PortAExtNonFatal_ACTION_CONTROL_ADDRESS + IoHubSmnAddrOffset + PcieCntlOffset + PciePortOffset,
                  &ActionCtrlValue);
      ActionCtrlValue &= (~ACTION_CONTROL_IntrGenSel_MASK);
      RasSmnWrite (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus),
                   SMN_PCIE2PortAExtNonFatal_ACTION_CONTROL_ADDRESS + IoHubSmnAddrOffset + PcieCntlOffset + PciePortOffset,
                   &ActionCtrlValue);

      // ExtCorr_ACTION_CONTROL
      RasSmnRead (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus),
                  SMN_PCIE2PortAExtCorr_ACTION_CONTROL_ADDRESS + IoHubSmnAddrOffset + PcieCntlOffset + PciePortOffset,
                  &ActionCtrlValue);
      ActionCtrlValue &= (~ACTION_CONTROL_IntrGenSel_MASK);
      RasSmnWrite (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus),
                   SMN_PCIE2PortAExtCorr_ACTION_CONTROL_ADDRESS + IoHubSmnAddrOffset + PcieCntlOffset + PciePortOffset,
                   &ActionCtrlValue);
    }
  }

  if (NbioRbIndex < 4) {
    // NBIF1 Serr_ACTION_CONTROL
    RasSmnRead (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus),
                SMN_NBIF1PortASerr_ACTION_CONTROL_ADDRESS + IoHubSmnAddrOffset,
                &ActionCtrlValue);
    ActionCtrlValue &= (~ACTION_CONTROL_IntrGenSel_MASK);
    RasSmnWrite (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus),
                SMN_NBIF1PortASerr_ACTION_CONTROL_ADDRESS + IoHubSmnAddrOffset,
                &ActionCtrlValue);

    // NBIF1 ExtFatal_ACTION_CONTROL
    RasSmnRead (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus),
                SMN_NBIF1PortAExtFatal_ACTION_CONTROL_ADDRESS + IoHubSmnAddrOffset,
                &ActionCtrlValue);
    ActionCtrlValue &= (~ACTION_CONTROL_IntrGenSel_MASK);
    RasSmnWrite (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus),
                SMN_NBIF1PortAExtFatal_ACTION_CONTROL_ADDRESS + IoHubSmnAddrOffset,
                &ActionCtrlValue);

    // NBIF1 ExtNonFatal_ACTION_CONTROL
    RasSmnRead (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus),
                SMN_NBIF1PortAExtNonFatal_ACTION_CONTROL_ADDRESS + IoHubSmnAddrOffset,
                &ActionCtrlValue);
    ActionCtrlValue &= (~ACTION_CONTROL_IntrGenSel_MASK);
    RasSmnWrite (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus),
                SMN_NBIF1PortAExtNonFatal_ACTION_CONTROL_ADDRESS + IoHubSmnAddrOffset,
                &ActionCtrlValue);

    // NBIF1 ExtCorr_ACTION_CONTROL
    RasSmnRead (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus),
                SMN_NBIF1PortAExtCorr_ACTION_CONTROL_ADDRESS + IoHubSmnAddrOffset,
                &ActionCtrlValue);
    ActionCtrlValue &= (~ACTION_CONTROL_IntrGenSel_MASK);
    RasSmnWrite (SMN_SEG_BUS (PciPortAddr.Address.Segment, PciPortAddr.Address.Bus),
                SMN_NBIF1PortAExtCorr_ACTION_CONTROL_ADDRESS + IoHubSmnAddrOffset,
                &ActionCtrlValue);
  }

  return EFI_SUCCESS;
}

/*********************************************************************************
 * Name: AmdEdrDsmCallback
 *
 * Description
 *   To-Do
 *
 *
 * Arguments:
 *   DispatchHandle  : The handle of this callback, obtained when registering
 *   SwContext       : Pointer to the FCH_SMM_SW_REGISTER_CONTEXT
 *
 * Returns:
 *   None
 *
 *********************************************************************************/
EFI_STATUS
EFIAPI
AmdEdrDsmCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SW_REGISTER_CONTEXT *DispatchContext,
  IN OUT   FCH_SMM_SW_CONTEXT                *SwContext,
  IN OUT   UINTN                             *SizeOfSwContext
  )
{
  EFI_STATUS                 Status;
  EDR_DSM_ACPI_SMM_DATA      *EdrDsmAcpiSmmData;
  RAS_ACPI_SMM_DATA          *RasAcpiSmmData;
  PCI_ADDR                   PciPortAddr;
  UINT8                      Index;
  UINT32                     RpAddr;
  UINT32                     MpioStatus;
  UINT32                     MpioArg[6];

  EdrDsmAcpiSmmData = mPlatformApeiData->EdrDsmAcpiSmmData;
  if (!SmmIsBufferOutsideSmmValid ((UINTN) EdrDsmAcpiSmmData, sizeof(EDR_DSM_ACPI_SMM_DATA))) {
    DEBUG ((EFI_D_ERROR, "EdrDsmAcpiSmmData is in SMRAM or overlapped with SMRAM!\n"));
    return EFI_INVALID_PARAMETER;
  }
  RasAcpiSmmData = mPlatformApeiData->RasAcpiSmmData;
  if (!SmmIsBufferOutsideSmmValid ((UINTN) RasAcpiSmmData, sizeof(RAS_ACPI_SMM_DATA))) {
    DEBUG ((EFI_D_ERROR, "RasAcpiSmmData is in SMRAM or overlapped with SMRAM!\n"));
    return EFI_INVALID_PARAMETER;
  }

  PciPortAddr.AddressValue = 0;
  PciPortAddr.Address.Segment = EdrDsmAcpiSmmData->RpSeg;
  PciPortAddr.Address.Bus = EdrDsmAcpiSmmData->RpBus;
  PciPortAddr.Address.Device = (EdrDsmAcpiSmmData->RpDevFnc >> 16);
  PciPortAddr.Address.Function = (EdrDsmAcpiSmmData->RpDevFnc & 0xFFFF);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS - EDR _DSM] On PCIe Seg: 0x%02x, Bus: 0x%02x, Dev: 0x%02x, Func: 0x%02x.\n",
    PciPortAddr.Address.Segment, PciPortAddr.Address.Bus, PciPortAddr.Address.Device, PciPortAddr.Address.Function);

  EdrDsmAcpiSmmData->OutputData = 0;
  switch (EdrDsmAcpiSmmData->FunctionIndex) {
  case 0x00:
    EdrDsmAcpiSmmData->OutputData = 0x3001; // Support Function 0x00, 0x0C, 0x0D
    break;
  case 0x0C:  //Function 0x0C: Downstream Port Containment Enable request from OS
    if (!EdrDsmAcpiSmmData->InputData) {
      //Disable DPC
      Status = EdrDsmDpcEnReqFromOs (PciPortAddr, FALSE);
      if (!EFI_ERROR (Status)) {
        EdrDsmAcpiSmmData->OutputData = 0;
      }
      else {
        EdrDsmAcpiSmmData->OutputData = 2; // As per PCI FW 3.3
      }
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS - EDR _DSM] Function 0x0C with input data(Arg3): 0x00 - Disable DPC: %r, OpRegion EDSM.DOUT = 0x%04x\n", Status, EdrDsmAcpiSmmData->OutputData);
    } else {
      //Enable DPC

      RpAddr = (PciPortAddr.AddressValue & 0xFFFFF000);
      Index = 0;
      while ((Index < TOTAL_NVME_SLOT) && (RootPortVsNvmeSlotMappingTable[Index].RpAddr != RpAddr)) {
        Index++;
      }
      if (Index == TOTAL_NVME_SLOT) {
        Index = 0;
        while ((Index < TOTAL_NVME_SLOT) && (RootPortVsNvmeSlotMappingTable[Index].RpAddr != 0)) {
          Index++;
        }
        if (Index == TOTAL_NVME_SLOT) {
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS - EDR _DSM] Function 0x0C, Enable DPC - Add Root Port: 0x%04x into RootPortVsNvmeSlotMappingTable failed.\n", RpAddr);
        } else {
          RootPortVsNvmeSlotMappingTable[Index].RpAddr = RpAddr;
          RootPortVsNvmeSlotMappingTable[Index].NvmeSlot = EdrDsmAcpiSmmData->RpAslDevName;
          RootPortVsNvmeSlotMappingTable[Index].RasRetryCnt = mPlatformApeiData->PlatRasPolicy.RasRetryCnt;
        }
      } else {
        RootPortVsNvmeSlotMappingTable[Index].NvmeSlot = EdrDsmAcpiSmmData->RpAslDevName;
        RootPortVsNvmeSlotMappingTable[Index].RasRetryCnt = mPlatformApeiData->PlatRasPolicy.RasRetryCnt;
      }

      if(Index < TOTAL_NVME_SLOT) {
        EdrDsmAcpiSmmData->RpAslDevName = 0;
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS - EDR _DSM] Function 0x0C, Enable DPC - [%d] RpAddr: 0x%08x, NvmeSlot: 0x%08x\n",
                Index, RootPortVsNvmeSlotMappingTable[Index].RpAddr, RootPortVsNvmeSlotMappingTable[Index].NvmeSlot);

        Status = EdrDsmDpcEnReqFromOs (PciPortAddr, TRUE);
        if (!EFI_ERROR (Status)) {
          AddOsEdrEnabledPortToTable(PciPortAddr);
          EdrDsmAcpiSmmData->OutputData = 1;

          if (RasAcpiSmmData->PcieOscCtrl.Field.PcieAerCtrl == 1 && RasAcpiSmmData->PcieOscCtrl.Field.PcieDpcCtrl == 1) {
            NbioMpioServiceCommonInitArguments (MpioArg);
            // Disable SMI generation
            MpioArg[0] = BIOSSMC_MSG_Param_HotplugDisableSMI;
            MpioStatus = MpioServiceRequest (PciPortAddr, BIOS_MPIO_MSG_HOTPLUG_FLAGS_SET, MpioArg, 0);
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Response from MPIO: %x\n", MpioStatus);
          }
        }
        else {
          EdrDsmAcpiSmmData->OutputData = 2; // As per PCI FW 3.3
        }
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS - EDR _DSM] Function 0x0C with input data(Arg3): 0x01 - Enable DPC: %r, OpRegion EDSM.DOUT = 0x%04x\n", Status, EdrDsmAcpiSmmData->OutputData);
      }
    }
    break;
  case 0x0D:  //Function 0x0D: Locate Error Port
    // PCIe FW Spec
    // return BDF of port that experienced containment
    //               ^^^^
    // Type: Integer
    // Description:
    //   Port Bus, Device, and Function number encoded as a 16 bit quality.
    //   (Bits 2:0 = Function, Bits 7:3 = Device, Bits 15:8 = Bus)
    //
    // AMD Spec
    //  Return the bus, device, and function number of the device where an error was logged
    //                                                     ^^^^^^

    // PciPortAddr.AddressValue format: Bits 11:0 = Register, Bits 14:12 = Function, Bits 19:15 = Device, Bits 27:20 = Bus, Bit 31:28 = Segment
    PciPortAddr.AddressValue >>= 12;  //Convert PciPortAddr format to _DSM return value format Bits 2:0 = Function, Bits 7:3 = Device, Bits 15:8 = Bus

    //return BDF of port
    EdrDsmAcpiSmmData->OutputData = (UINT16)PciPortAddr.AddressValue;

    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS - EDR _DSM] Function 0x0D, return BDF of port: 0x%04x\n", PciPortAddr.AddressValue);
    break;
  }

  return EFI_SUCCESS;
}

/*********************************************************************************
 * Name: AmdEdrOstCallback
 *
 * Description
 *   To-Do
 *
 *
 * Arguments:
 *   DispatchHandle  : The handle of this callback, obtained when registering
 *   DispatchContext : Pointer to the FCH_SMM_USB_REGISTER_CONTEXT
 *
 * Returns:
 *   None
 *
 *********************************************************************************/
EFI_STATUS
EFIAPI
AmdEdrOstCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SW_REGISTER_CONTEXT *DispatchContext,
  IN OUT   FCH_SMM_SW_CONTEXT                *SwContext,
  IN OUT   UINTN                             *SizeOfSwContext
  )
{
  EDR_OST_ACPI_SMM_DATA      *EdrOstAcpiSmmData;
  UINT32                     OstArg1;
  PCI_ADDR                   PciPortAddr;
  UINT16                     DpcCapPtr;
  DPC_CNTL_REG               DpcCntlReg;
  UINT64                     PciSegAddr;
  UINT8                      PcieCapPtr;
  UINT32                     PcieSlotCap;
  UINT16                     PcieSlotStatus;
  UINT32                     *RasRetryCnt;
  UINT8                      Index;

  EdrOstAcpiSmmData = mPlatformApeiData->EdrOstAcpiSmmData;
  if (!SmmIsBufferOutsideSmmValid ((UINTN) EdrOstAcpiSmmData, sizeof(EDR_OST_ACPI_SMM_DATA))) {
    DEBUG ((EFI_D_ERROR, "EdrOstAcpiSmmData is in SMRAM or overlapped with SMRAM!\n"));
    return EFI_INVALID_PARAMETER;
  }
  OstArg1 = EdrOstAcpiSmmData->StatusCode;

// The layout of Arg1 is as follows
// Bit 15:0  Status of the operation. See ACPI specification.
// Bit 18:16 Function number of the port that experienced the containment event
// Bit 23:19 Device number of the port that experienced the containment event
// Bit 31:24 Bus number of the port that experienced the containment event
//
// The layout of PciPortAddr.AddressValue is as follows
// Bit 11:0 = Register
// Bit 14:12 = Function
// Bit 19:15 = Device
// Bit 27:20 = Bus
// Bit 31:28 = Segment
  PciPortAddr.AddressValue = (UINT32)((OstArg1 >> 16) << 12);
  PciSegAddr = PCI_SEGMENT_LIB_ADDRESS (PciPortAddr.Address.Segment,
                                        PciPortAddr.Address.Bus,
                                        PciPortAddr.Address.Device,
                                        PciPortAddr.Address.Function,
                                        PciPortAddr.Address.Register);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS - EDR _OST] On PCIe Seg: 0x%02x, Bus: 0x%02x, Dev: 0x%02x, Func: 0x%02x.\n",
    PciPortAddr.Address.Segment, PciPortAddr.Address.Bus, PciPortAddr.Address.Device, PciPortAddr.Address.Function);

  DpcCapPtr = RasFindPcieExtendedCapability (PciPortAddr.AddressValue, DPC_EXT_CAP_ID, 0xFFFF);
  if (DpcCapPtr == 0) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS - EDR _OST] Locating DpcCapPtr failed.\n");
    return EFI_SUCCESS;
  }

  switch (OstArg1 & 0xFF) {
  case 0x80:  //_OST Notification with status 0x80 (success):
//  this indicates device recovery was successful. The SMI handler:
//    1. No action is needed. Return control to the OS.
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS - EDR _OST] Notification status: 0x80 (success), Return control to the OS.\n");

    //Restore the retry count
    for (Index = 0; Index < TOTAL_NVME_SLOT; Index++) {
      if (RootPortVsNvmeSlotMappingTable[Index].RpAddr == PciPortAddr.AddressValue) {
        RootPortVsNvmeSlotMappingTable[Index].RasRetryCnt = mPlatformApeiData->PlatRasPolicy.RasRetryCnt;
      }
    }

    //Clear DLSC for non-hotplug port
    PcieCapPtr = RasFindPciCapability (PciPortAddr.AddressValue, PCIE_CAP_ID);
    if (PcieCapPtr != 0) {
      PcieSlotCap = PciSegmentRead32 (PciSegAddr + PcieCapPtr + PCIE_SLOT_CAP_REGISTER);
      if ((PcieSlotCap & BIT6 /*Hot-Plug Capable*/) == 0) {
        PcieSlotStatus = PciSegmentRead16 (PciSegAddr + PcieCapPtr + 0x1A /*PCIE_SLOT_STATUS_REGISTER*/);
        PcieSlotStatus &= BIT8 /*Data Link Layer State Changed*/;
        if (PcieSlotStatus != 0) {
          PciSegmentWrite16 (PciSegAddr + PcieCapPtr + 0x1A /*PCIE_SLOT_STATUS_REGISTER*/, PcieSlotStatus);
        }
      }
    }
    break;

  case 0x81:  //_OST Notification with status 0x81 (failure):
  default:
//  this indicates device recovery was unsuccessful. The SMI handler:
//    1. Issue a software trigger of DPC by setting PCIERCCFG::PCIE_DPC_CNTL[DPC_SOFTWARE_TRIGGER].
//    2. Return control to the OS.

    //Initial the retry count
    RasRetryCnt = NULL;
    for (Index = 0; Index < TOTAL_NVME_SLOT; Index++) {
      if (RootPortVsNvmeSlotMappingTable[Index].RpAddr == PciPortAddr.AddressValue) {
        RasRetryCnt = &RootPortVsNvmeSlotMappingTable[Index].RasRetryCnt;
      }
    }

    if ((RasRetryCnt != NULL) && (*RasRetryCnt > 0)) {
      (*RasRetryCnt)--;
      DpcCntlReg.Value = PciSegmentRead16 (PciSegAddr + DpcCapPtr + DPC_CONTROL_REG);
      DpcCntlReg.Field.DpcSoftwareTrigger = 1;
      PciSegmentWrite16 (PciSegAddr + DpcCapPtr + DPC_CONTROL_REG, DpcCntlReg.Value);
      IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_ERROR, "[RAS - EDR _OST] Notification status: 0x81 (failure), recovery was unsuccessful.\
        Issue a software trigger of DPC. DpcCntlRegValue: 0x%08x\n", DpcCntlReg.Value);
    } else {
      IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_ERROR, "RAS Retry limit exceeded.\n");
    }

    break;
  }

  return EFI_SUCCESS;
}

/*********************************************************************************
 * Name: AmdOscCallback
 *
 * Description
 *   To-Do
 *
 *
 * Arguments:
 *   DispatchHandle  : The handle of this callback, obtained when registering
 *   DispatchContext : Pointer to the FCH_SMM_SW_CONTEXT
 *
 * Returns:
 *   None
 *
 *********************************************************************************/
EFI_STATUS
EFIAPI
AmdOscCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SW_REGISTER_CONTEXT *DispatchContext,
  IN OUT   FCH_SMM_SW_CONTEXT                *SwContext,
  IN OUT   UINTN                             *SizeOfSwContext
  )
{
  OS_HOT_PLUG_MECHANISM_STRUCTURE   *OsHotPlugStrData;
  PCI_ADDR                          PciPortAddr;
  UINT32                            SmuArg[6];

  OsHotPlugStrData = mPlatformApeiData->OsHotPlugMechanism;

  PciPortAddr.AddressValue = 0;
  PciPortAddr.Address.Bus = OsHotPlugStrData->RpBus;
  PciPortAddr.Address.Device = (OsHotPlugStrData->RpDevFnc >> 16);
  PciPortAddr.Address.Function = (OsHotPlugStrData->RpDevFnc & 0xFFFF);

  IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "[RAS - _OSC] On PCIe Bus: 0x%02x, Dev: 0x%02x, Func: 0x%02x.\n",
    PciPortAddr.Address.Bus, PciPortAddr.Address.Device, PciPortAddr.Address.Function);

  if (((OsHotPlugStrData->RpDevFnc >> 16)    == 0xDEAD) &&
      ((OsHotPlugStrData->RpDevFnc & 0xFFFF) == 0xBABE)) {
    ReConfigRErCmdReg ();
    return EFI_SUCCESS;
  }

  ReConfigAerReportingPath (PciPortAddr);

  //
  // Send SMU AER Enable and AER Reporting settings
  //
  NbioSmuServiceCommonInitArguments (SmuArg);
  SmuArg[0] = 1;//PcdCfgAEREnable != 0;
  SmuArg[1] = 1;//PCIe Aer Reporting Mechanism - OS First;

  if (NbioSmuServiceRequest (PciPortAddr, BIOSSMC_MSG_EnableAer, SmuArg, 0)) {
    IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "Send SMU AER Enable and AER Reporting OS First successful\n");
  } else {
    IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_INFO, "Send SMU AER Enable and AER Reporting OS First failed\n");
  }

  return EFI_SUCCESS;
}

EFI_STATUS
RasSmmRegisterEdrSwSmi (
  VOID
  )
{
  EFI_STATUS                               Status;
  FCH_SMM_SW_DISPATCH2_PROTOCOL            *AmdSwDispatch;
  FCH_SMM_SW_REGISTER_CONTEXT              SwRegisterContext;
  EFI_HANDLE                               SwHandle;
  UINT8                                    EdrDsmSwSmiCmd;
  UINT8                                    EdrOstSwSmiCmd;
  UINT8                                    OscSwSmiCmd;

  //
  //  Locate SMM SW dispatch protocol
  //
  Status = gSmst->SmmLocateProtocol (
                  &gFchSmmSwDispatch2ProtocolGuid,
                  NULL,
                  (VOID **)&AmdSwDispatch
                  );
  ASSERT_EFI_ERROR (Status);

  ZeroMem (RootPortVsNvmeSlotMappingTable, sizeof (ROOTPORT_VS_NVMESLOT_DESC) * TOTAL_NVME_SLOT);

  //EdrDsmSwSmiCmd = 0x87;
  EdrDsmSwSmiCmd = mPlatformApeiData->EdrDsmAcpiSmmData->EdrDsmSwSmiCmd;

  SwRegisterContext.AmdSwValue  = EdrDsmSwSmiCmd;
  SwRegisterContext.Order  = 0x80;
  Status = AmdSwDispatch->Register (
                            AmdSwDispatch,
                            AmdEdrDsmCallback,
                            &SwRegisterContext,
                            &SwHandle
                            );
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] Install EDR DSM Software SMI callback handler: %r\n", Status);
  if (EFI_ERROR (Status)) {
    return Status;
  }

  //EdrOstSwSmiCmd = 0x88;
  EdrOstSwSmiCmd = mPlatformApeiData->EdrOstAcpiSmmData->EdrOstSwSmiCmd;

  SwRegisterContext.AmdSwValue  = EdrOstSwSmiCmd;
  SwRegisterContext.Order  = 0x80;
  Status = AmdSwDispatch->Register (
                            AmdSwDispatch,
                            AmdEdrOstCallback,
                            &SwRegisterContext,
                            &SwHandle
                            );
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] Install EDR OST Software SMI callback handler: %r\n", Status);
  if (EFI_ERROR (Status)) {
    return Status;
  }

  //OscSwSmiCmd = 0x89;
  mPlatformApeiData->OsHotPlugMechanism->OscSwSmiCmd = PcdGet8 (PcdCpmOscSwSmiId);
  OscSwSmiCmd = mPlatformApeiData->OsHotPlugMechanism->OscSwSmiCmd;

  SwRegisterContext.AmdSwValue  = OscSwSmiCmd;
  SwRegisterContext.Order  = 0x80;
  Status = AmdSwDispatch->Register (
                            AmdSwDispatch,
                            AmdOscCallback,
                            &SwRegisterContext,
                            &SwHandle
                            );
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] Install OSC Software SMI callback handler: %r\n", Status);
  if (EFI_ERROR (Status)) {
    return Status;
  }

  return EFI_SUCCESS;
}

BOOLEAN
ReConfigRErCmdReg (
  VOID
  )
{
  PCIE_PORT_PROFILE           *PciePortProfileInstance;
  UINT16                      PciePortIndex;
  PCI_ADDR                    ActPciPortAddr;
  UINT16                      AerCapPtr;
  UINT32                      PcieRootErrCmd;
  static BOOLEAN              RootErrCmdProgrammed = FALSE;

  //Search active PCI-E port to program root error command register only
  if (!RootErrCmdProgrammed) {
    PciePortProfileInstance = mPlatformApeiData->AmdPciePortMap->PciPortNumber;
    for (PciePortIndex = 0; PciePortIndex < mPlatformApeiData->AmdPciePortMap->PortCount; PciePortIndex++, PciePortProfileInstance++) {
      ActPciPortAddr.AddressValue = PciePortProfileInstance->RpPciAddr;
      AerCapPtr = RasFindPcieExtendedCapability (ActPciPortAddr.AddressValue, PCIE_EXT_AER_CAP_ID, 0xFFFF);
      if (AerCapPtr != 0) {
        PcieRootErrCmd = PciSegmentRead32 (ActPciPortAddr.AddressValue + AerCapPtr + PCIE_ROOT_ERR_CMD_PTR);
        PcieRootErrCmd &= ~(PCIE_ROOT_CORR_ERR | PCIE_ROOT_NON_FATAL_ERR | PCIE_ROOT_FATAL_ERR);
        PciSegmentWrite32 (ActPciPortAddr.AddressValue + AerCapPtr + PCIE_ROOT_ERR_CMD_PTR, PcieRootErrCmd);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS - _OSC] Set Root Error Command register to 0x%02X on PCIe Seg: 0x%02X, Bus: 0x%02X, Dev: 0x%02X, Func: 0x%02X\n",
          PcieRootErrCmd, ActPciPortAddr.Address.Segment, ActPciPortAddr.Address.Bus, ActPciPortAddr.Address.Device, ActPciPortAddr.Address.Function);
      }
    }
    RootErrCmdProgrammed = TRUE;
  }

  return RootErrCmdProgrammed;
}
