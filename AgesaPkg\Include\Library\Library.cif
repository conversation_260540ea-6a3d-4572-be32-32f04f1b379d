<component>
    name = "Library"
    category = ModulePart
    LocalRoot = "AgesaPkg\Include\Library\"
    RefName = "AgesaPkg.Include.Library"
[files]
"ApcbTokenWhiteListLib.h"
"AcpiTableHelperLib.h"
"AmdPspApobLib.h"
"AmdPspCommonLib.h"
"AmdPspFlashAccLib.h"
"AmdPspPsbFusingLib.h"
"AmdPspRegMuxLibV2.h"
"AmdPspRomArmorLib.h"
"AmdStbLib.h"
"AmlGenerationLib.h"
"ApobCommonServiceLib.h"
"FabricResourceManagerLib.h"
"GnbPciLib.h"
"IvrsDeviceInfoLib.h"
"LegacyInterruptLib.h"
"MpdmaIvrsLib.h"
"OemClkReqControlLib.h"
"OemGpioResetControlLib.h"
"PlatformPspRomArmorWhitelistLib.h"
<endComponent>