/*****************************************************************************
 *
 * Copyright (C) 2019-2025 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 **/

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */

#include <Uefi.h>
#include <BRH/ApcbV3TokenUid.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                            L O C A L   F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                                 F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/**
 * @brief Function to calculate APCB token pair CRC32 checksum
 *
 * @param[in]     ApcbTokenUid      APCB TOKEN UID to check
 *
 * @return TRUE   This APCB TOKEN is in white list
 * @return FALSE  This APCB TOKEN is not in white list
 */
BOOLEAN
IsApcbTokenInWhiteList (
  UINT32  ApcbTokenUid
  )
{
  UINT32    Index;
  UINT32    ApcbTokenWhiteList[] = {
      APCB_TOKEN_UID_CBS_SYNC_SIGNATURE,
      APCB_TOKEN_UID_CCD0_BITMAP_DOWN_CORE_CTRL,
      APCB_TOKEN_UID_CCD1_BITMAP_DOWN_CORE_CTRL,
      APCB_TOKEN_UID_CCD2_BITMAP_DOWN_CORE_CTRL,
      APCB_TOKEN_UID_CCD3_BITMAP_DOWN_CORE_CTRL,
      APCB_TOKEN_UID_CCD4_BITMAP_DOWN_CORE_CTRL,
      APCB_TOKEN_UID_CCD5_BITMAP_DOWN_CORE_CTRL,
      APCB_TOKEN_UID_CCD6_BITMAP_DOWN_CORE_CTRL,
      APCB_TOKEN_UID_CCD7_BITMAP_DOWN_CORE_CTRL,
      APCB_TOKEN_UID_CCD8_BITMAP_DOWN_CORE_CTRL,
      APCB_TOKEN_UID_CCD9_BITMAP_DOWN_CORE_CTRL,
      APCB_TOKEN_UID_CCD10_BITMAP_DOWN_CORE_CTRL,
      APCB_TOKEN_UID_CCD11_BITMAP_DOWN_CORE_CTRL,
      APCB_TOKEN_UID_CCD12_BITMAP_DOWN_CORE_CTRL,
      APCB_TOKEN_UID_CCD13_BITMAP_DOWN_CORE_CTRL,
      APCB_TOKEN_UID_CCD14_BITMAP_DOWN_CORE_CTRL,
      APCB_TOKEN_UID_CCD15_BITMAP_DOWN_CORE_CTRL,
      APCB_TOKEN_UID_CCX_CCD_CTRL,
      APCB_TOKEN_UID_CCX_CORE_CTRL,
      APCB_TOKEN_UID_CCX_SMT_CTRL,
      APCB_TOKEN_UID_FCH_CONSOLE_OUT_ENABLE
  };

  for (Index = 0; Index < (sizeof(ApcbTokenWhiteList)/sizeof(UINT32)); Index++) {
    if (ApcbTokenUid == ApcbTokenWhiteList[Index]) {
      return TRUE;
    }
  }
  return FALSE;
}

