{"ApcbDataDefaultRecovery": {"binary": "APCB_BRH_D4_DefaultRecovery.bin", "sources": {"Compliance": {"common": ["ApcbDataDefaultRecovery/Compliance/ApcbData_BRH_GID_0x1704_Type_DimmInfoSpd.c", "ApcbDataDefaultRecovery/Compliance/ApcbData_BRH_GID_0x1705_Type_PcieConfig.c", "ApcbDataDefaultRecovery/Compliance/ApcbData_BRH_GID_0x3000_Type_TokenBoolean.c"]}, "BreithornCommon": {"common": ["ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_ConsoleOutControl.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_ErrorOutControl.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_ExtVoltageControl.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_DimmInfoSpd.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_PsoOverride.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_PsBusCfgRDIMMDdr5.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_PsBusCfgRDIMMDdr5_Hynix.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_PsBusCfgRDIMMDdr5_Hynix_24Gb.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_PsBusCfgRDIMMDdr5_Hynix_DIMM_6400.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_PsBusCfgRDIMMDdr5_Micron.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_PsBusCfgRDIMMDdr5_Micron_24Gb.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_PsBusCfgRDIMMDdr5_Micron_DIMM_6400.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_PsBusCfgRDIMMDdr5_Samsung.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_PsBusCfgRDIMMDdr5_Samsung_24Gb.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_PsBusCfgRDIMMDdr5_Samsung_DIMM_6400.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_PsDramDqPinMapping.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_PsDramCaPinMapping.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_PsMaxFreq3DSRDIMMDdr5.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_PsMaxFreqRDIMMDdr5.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_PsMaxFreqRDIMMDdr5_C1.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_SpdInfo.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_MemDfeSearchScheme.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_PmuBistVendorAlgorithm.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_RawCardCfgRDIMMDdr5_Hynix.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_RawCardCfgRDIMMDdr5_Micron.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_RawCardCfgRDIMMDdr5_Samsung.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_DdrTrainingOverride.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1705_Type_PcieConfig.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1706_Type_EspiInitConfiguration.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1706_Type_EspiSioInitConfiguration.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x3000_Type_TokenBoolean.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x3000_Type_Token2Bytes.c", "ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x3000_Type_Token4Bytes.c"], "multiboard": ["ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_BoardIdGettingMethod.c"]}, "Onyx": {"common": ["ApcbDataDefaultRecovery/Onyx/ApcbData_BRH_GID_0x1704_Type_DimmInfoSpd.c", "ApcbDataDefaultRecovery/Onyx/ApcbData_BRH_GID_0x1705_Type_PcieConfig.c", "ApcbDataDefaultRecovery/Onyx/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c"]}, "QuartzFR4": {"common": ["ApcbDataDefaultRecovery/QuartzFR4/ApcbData_BRH_GID_0x1705_Type_PcieConfig.c", "ApcbDataDefaultRecovery/QuartzFR4/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c", "ApcbDataDefaultRecovery/QuartzFR4/ApcbData_BRH_GID_0x3000_Type_TokenBoolean.c"]}, "QuartzRevA": {"common": ["ApcbDataDefaultRecovery/QuartzRevA/ApcbData_BRH_GID_0x1705_Type_PcieConfig.c", "ApcbDataDefaultRecovery/QuartzRevA/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c", "ApcbDataDefaultRecovery/QuartzRevA/ApcbData_BRH_GID_0x3000_Type_TokenBoolean.c"]}, "Quartz": {"common": ["ApcbDataDefaultRecovery/Quartz/ApcbData_BRH_GID_0x1705_Type_PcieConfig.c", "ApcbDataDefaultRecovery/Quartz/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c", "ApcbDataDefaultRecovery/Quartz/ApcbData_BRH_GID_0x3000_Type_TokenBoolean.c"]}, "Titanite": {"common": ["ApcbDataDefaultRecovery/Titanite/ApcbData_BRH_GID_0x1701_Type_BoardSpecificValueGettingMethod.c", "ApcbDataDefaultRecovery/Titanite/ApcbData_BRH_GID_0x1704_Type_DimmInfoSpd.c", "ApcbDataDefaultRecovery/Titanite/ApcbData_BRH_GID_0x1704_Type_PsoOverride.c", "ApcbDataDefaultRecovery/Titanite/ApcbData_BRH_GID_0x1705_Type_PcieConfig.c", "ApcbDataDefaultRecovery/Titanite/ApcbData_BRH_GID_0x1706_Type_EspiInitConfiguration.c", "ApcbDataDefaultRecovery/Titanite/ApcbData_BRH_GID_0x1706_Type_EspiInitConfiguration_Espi1.c", "ApcbDataDefaultRecovery/Titanite/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c", "ApcbDataDefaultRecovery/Titanite/ApcbData_BRH_GID_0x3000_Type_TokenBoolean.c"]}, "Titanite2P2G": {"common": ["ApcbDataDefaultRecovery/Titanite2P2G/ApcbData_BRH_GID_0x1704_Type_DimmInfoSpd.c", "ApcbDataDefaultRecovery/Titanite2P2G/ApcbData_BRH_GID_0x1704_Type_PsoOverride.c", "ApcbDataDefaultRecovery/Titanite2P2G/ApcbData_BRH_GID_0x1705_Type_PcieConfig.c", "ApcbDataDefaultRecovery/Titanite2P2G/ApcbData_BRH_GID_0x1706_Type_EspiInitConfiguration.c", "ApcbDataDefaultRecovery/Titanite2P2G/ApcbData_BRH_GID_0x1706_Type_EspiInitConfiguration_Espi1.c", "ApcbDataDefaultRecovery/Titanite2P2G/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c", "ApcbDataDefaultRecovery/Titanite2P2G/ApcbData_BRH_GID_0x3000_Type_TokenBoolean.c"]}, "TitaniteRevC": {"common": ["ApcbDataDefaultRecovery/TitaniteRevC/ApcbData_BRH_GID_0x1701_Type_BoardSpecificValueGettingMethod.c", "ApcbDataDefaultRecovery/TitaniteRevC/ApcbData_BRH_GID_0x1704_Type_DimmInfoSpd.c", "ApcbDataDefaultRecovery/TitaniteRevC/ApcbData_BRH_GID_0x1704_Type_PsoOverride.c", "ApcbDataDefaultRecovery/TitaniteRevC/ApcbData_BRH_GID_0x1705_Type_PcieConfig.c", "ApcbDataDefaultRecovery/TitaniteRevC/ApcbData_BRH_GID_0x1706_Type_EspiInitConfiguration.c", "ApcbDataDefaultRecovery/TitaniteRevC/ApcbData_BRH_GID_0x1706_Type_EspiInitConfiguration_Espi1.c", "ApcbDataDefaultRecovery/TitaniteRevC/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c", "ApcbDataDefaultRecovery/TitaniteRevC/ApcbData_BRH_GID_0x3000_Type_TokenBoolean.c"]}, "CharzRX": {"common": ["ApcbDataDefaultRecovery/CharzRX/ApcbData_BRH_GID_0x1704_Type_DimmInfoSpd.c", "ApcbDataDefaultRecovery/CharzRX/ApcbData_BRH_GID_0x1705_Type_PcieConfig.c", "ApcbDataDefaultRecovery/CharzRX/ApcbData_BRH_GID_0x3000_Type_TokenBoolean.c"]}, "CharzTX": {"common": ["ApcbDataDefaultRecovery/CharzTX/ApcbData_BRH_GID_0x1704_Type_DimmInfoSpd.c", "ApcbDataDefaultRecovery/CharzTX/ApcbData_BRH_GID_0x1705_Type_PcieConfig.c", "ApcbDataDefaultRecovery/CharzTX/ApcbData_BRH_GID_0x3000_Type_TokenBoolean.c"]}, "Galena": {"common": ["ApcbDataDefaultRecovery/Galena/ApcbData_BRH_GID_0x1705_Type_PcieConfig.c", "ApcbDataDefaultRecovery/Galena/ApcbData_BRH_GID_0x1704_Type_DimmInfoSpd.c", "ApcbDataDefaultRecovery/Galena/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c"]}, "Chalupa": {"common": ["ApcbDataDefaultRecovery/Chalupa/ApcbData_BRH_GID_0x1705_Type_PcieConfig.c", "ApcbDataDefaultRecovery/Chalupa/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c", "ApcbDataDefaultRecovery/Chalupa/ApcbData_BRH_GID_0x3000_Type_TokenBoolean.c"]}, "Huambo": {"common": ["ApcbDataDefaultRecovery/Huambo/ApcbData_BRH_GID_0x1705_Type_PcieConfig.c", "ApcbDataDefaultRecovery/Huambo/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c"]}, "Volcano": {"common": ["ApcbDataDefaultRecovery/Volcano/ApcbData_BRH_GID_0x1701_Type_BoardSpecificValueGettingMethod.c", "ApcbDataDefaultRecovery/Volcano/ApcbData_BRH_GID_0x1705_Type_PcieConfig.c", "ApcbDataDefaultRecovery/Volcano/ApcbData_BRH_GID_0x1706_Type_EspiInitConfiguration.c", "ApcbDataDefaultRecovery/Volcano/ApcbData_BRH_GID_0x1706_Type_EspiInitConfiguration_Espi1.c", "ApcbDataDefaultRecovery/Volcano/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c", "ApcbDataDefaultRecovery/Volcano/ApcbData_BRH_GID_0x3000_Type_TokenBoolean.c"]}, "Purico": {"common": ["ApcbDataDefaultRecovery/Purico/ApcbData_BRH_GID_0x1704_Type_DimmInfoSpd.c", "ApcbDataDefaultRecovery/Purico/ApcbData_BRH_GID_0x1704_Type_PsoOverride.c", "ApcbDataDefaultRecovery/Purico/ApcbData_BRH_GID_0x1705_Type_PcieConfig.c", "ApcbDataDefaultRecovery/Purico/ApcbData_BRH_GID_0x1704_Type_MemDfeSearchScheme.c", "ApcbDataDefaultRecovery/Purico/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c"]}, "Ruby": {"common": ["ApcbDataDefaultRecovery/Ruby/ApcbData_BRH_GID_0x1704_Type_DimmInfoSpd.c", "ApcbDataDefaultRecovery/Ruby/ApcbData_BRH_GID_0x1704_Type_PsoOverride.c", "ApcbDataDefaultRecovery/Ruby/ApcbData_BRH_GID_0x1705_Type_PcieConfig.c", "ApcbDataDefaultRecovery/Ruby/ApcbData_BRH_GID_0x1704_Type_MemDfeSearchScheme.c", "ApcbDataDefaultRecovery/Ruby/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c"]}}}, "ApcbDataEventLog": {"binary": "APCB_BRH_D4_EventLog.bin", "sources": ["ApcbDataEventLog/ApcbData_BRH_GID_0x1704_Type_DdrPostPackageRepair.c"]}, "ApcbDataUpdatable": {"binary": "APCB_BRH_D4_Updatable.bin", "sources": ["ApcbDataUpdatable/ApcbData_BRH_GID_0x3000_Type_TokenBoolean.c", "ApcbDataUpdatable/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c", "ApcbDataUpdatable/ApcbData_BRH_GID_0x3000_Type_Token2Bytes.c", "ApcbDataUpdatable/ApcbData_BRH_GID_0x3000_Type_Token4Bytes.c", "ApcbDataUpdatable/ApcbData_BRH_GID_0x3000_Type_TokenBooleanDebug.c", "ApcbDataUpdatable/ApcbData_BRH_GID_0x3000_Type_Token1ByteDebug.c", "ApcbDataUpdatable/ApcbData_BRH_GID_0x3000_Type_Token2BytesDebug.c", "ApcbDataUpdatable/ApcbData_BRH_GID_0x3000_Type_Token4BytesDebug.c", "ApcbDataUpdatable/ApcbData_BRH_GID_0x1703_Type_CxlConfig.c"]}}