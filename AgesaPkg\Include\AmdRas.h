/*****************************************************************************
 *
 * Copyright (C) 2016-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 **/
/**
 * @file AmdRas.h
 *
 * @brief  Common RAS definition
 *
 * @details Provide generic MSR and error section definition across programs
 *
 */

#ifndef _AMD_RAS_H_
#define _AMD_RAS_H_

/// ACPI define
#define BERT_SIG SIGNATURE_32('B', 'E', 'R', 'T')

///
/// The following are the definitions related to the FW implementation of Boot Error Region and Generic Error Status Block.
///     Boot Error Region:
///         BERT contains the pointer of Boot Error Region.
///     Generic Error Status Block (Defined in Table ACPI 6.3, 18-391):
///         HEST contains the pointer of Generic Error Status Block.
///     The format of the Boot Error Region follow that of a Generic Error Status Block:
///         i.e. Boot Error Region = Generic Error Status Block
///
/**
 * @brief The Raw Error Data record type of the Boot Error Region/Generic Error Status Block.
 *
 * @details The error records of the Boot Error Region/Generic Error Status Block are divided into two types:
 *          Raw Error Data and Generic Error Data.
 *          This definition is used to specify the record as Raw Error Data
 *          when adding an error record to the Boot Error Region/Generic Error Status Block.
 */
#define ERROR_TYPE_RAW          (1)
/**
 * @brief The Generic Error Data record type of the Boot Error Region/Generic Error Status Block.
 *
 * @details The error records of the Boot Error Region/Generic Error Status Block are divided into two types:
 *          Raw Error Data and Generic Error Data.
 *          This definition is used to specify the record as Generic Error Data
 *          when adding an error record to the Boot Error Region/Generic Error Status Block.
 */
#define ERROR_TYPE_GENERIC      (2)

/// Block Status field in Boot Error Region/Generic Error Status Block.
/**
 * @brief The Uncorrectable Error Valid bit in the Block Status field of the Boot Error Region/Generic Error Status Block.
 *
 * @details This definition is used to indicate that an uncorrectable error condition (record) exists in
 *          the Boot Error Region/Generic Error Status Block.
 */
#define ERROR_UNCORR_VALID      (1 << 0)
/**
 * @brief The Correctable Error Valid bit in the Block Status field of the Boot Error Region/Generic Error Status Block.
 *
 * @details This definition is used to indicate that a correctable error condition (record) exists in
 *          the Boot Error Region/Generic Error Status Block.
 */
#define ERROR_CORR_VALID        (1 << 1)
/**
 * @brief The Multiple Uncorrectable Errors bit in the Block Status field of the Boot Error Region/Generic Error Status Block.
 *
 * @details This definition is used to indicate that there are multiple detected uncorrectable errors (records) in
 *          the Boot Error Region/Generic Error Status Block.
 */
#define MULT_UNCORR_ERROR_VALID (1 << 2)
/**
 * @brief The Multiple Correctable Errors bit in the Block Status field of the Boot Error Region/Generic Error Status Block.
 *
 * @details This definition is used to indicate that there are multiple detected correctable errors (records) in
 *          the Boot Error Region/Generic Error Status Block.
 */
#define MULT_CORR_ERROR_VALID   (1 << 3)

/// Error Severity in Boot Error Region/Generic Error Status Block and ACPI 6.3 Table 18-392 Generic Error Data Entry
/**
 * @brief The value of the Error Severity field is the Recoverable error severity.
 *
 * @details This definition is used to identify the Recoverable severity of the reported error in
 *          the Error Severity field of Boot Error Region/Generic Error Status Block/Generic Error Data Entry.
 */
#define ERROR_RECOVERABLE           (0)
/**
 * @brief The value of the Error Severity field is the Fatal error severity.
 *
 * @details This definition is used to identify the Fatal severity of the reported error in
 *          the Error Severity field of Boot Error Region/Generic Error Status Block/Generic Error Data Entry.
 */
#define ERROR_SEVERITY_FATAL        (1)
/**
 * @brief The value of the Error Severity field is the Corrected error severity.
 *
 * @details This definition is used to identify the Corrected severity of the reported error in
 *          the Error Severity field of Boot Error Region/Generic Error Status Block/Generic Error Data Entry.
 */
#define ERROR_SEVERITY_CORRECTED    (2)
/**
 * @brief The value of the Error Severity field is None.
 *
 * @details This definition is used to identify the severity (None) of the reported error in
 *          the Error Severity field of Boot Error Region/Generic Error Status Block/Generic Error Data Entry.
 */
#define ERROR_NONE                  (3)

/// Validation bits field in ACPI 6.3 Table 18-392 Generic Error Data Entry, UEFI spec2.6
/**
 * @brief The FRU Id field of the Generic Error Data Entry contains valid information.
 *
 * @details In the Generic Error Data Entry, set Bit 0 of Validation Bits field to
 *          indicate that the FRU Id field contains valid information.
 */
#define FRU_ID_VALID                        (1 << 0)
/**
 * @brief The FRU Text field of the Generic Error Data Entry contains valid information.
 *
 * @details In the Generic Error Data Entry, set Bit 1 of Validation Bits field to
 *          indicate that the FRU Text field contains valid information.
 */
#define FRU_STRING_VALID                    (1 << 1)

//Memory Tester Event Log ID
/**
 * @brief PMU training errors.
 *
 * @details The format of the data section of the PMU error record:
 *          DATA A in APOB_ERROR_LOG structure or DataParam1 in ERROR_LOG_PARAMS structure
 *             Bit 7:0 - Socket
 *             Bit 15:8 - Channel
 *             Bits 19:16 - Bitmask of failed CS's, corresponding to CS3..0
 *             Bit 20 - DIMM 0 PMU training failure
 *             Bit 21 - DIMM 1 PMU training failure
 *             Bit 31:22 - reserved
 *          DATA B in APOB_ERROR_LOG structure or DataParam2 in ERROR_LOG_PARAMS structure
 *             Bit 0 - PMU FW Load Error
 *             Bit 1 - PMU Traiing Error
 *             Bit 31:2 - Reserved
 */
#define ABL_MEM_PMU_TRAIN_ERROR                                       (0x4001)

/**
 * @brief Agesa memory test error.
 *
 * @details The format of the data section of the Agesa memory test record:
 *          DATA A in APOB_ERROR_LOG structure or DataParam1 in ERROR_LOG_PARAMS structure
 *             Bit 7:0 - Socket
 *             Bit 15:8 - Channel
 *             Bit 16 - DIMM 0, Agesa memory test error on this DIMM
 *             Bit 17 - DIMM 1, Agesa memory test error on this DIMM
 *             Bit 31:18 - reserved
 *          DATA B in APOB_ERROR_LOG structure or DataParam2 in ERROR_LOG_PARAMS structure
 *             Bit 31:0 - Reserved
 */
#define ABL_MEM_AGESA_MEMORY_TEST_ERROR                               (0x4003)

/**
 * @brief PMU training errors ever happened.
 *
 * @details The format of the data section of the PMU error record:
 *          DATA A in APOB_ERROR_LOG structure or DataParam1 in ERROR_LOG_PARAMS structure
 *             Bit 7:0 - Socket
 *             Bit 15:8 - Retry iteration count
 *             Bit 27:16 - Channel retry mask, bit 16=channel 0, bit 17=channel 1, etc, 1:
 *             channel retried
 *             Bit 31:28 - Reserved
 *          DATA B in APOB_ERROR_LOG structure or DataParam2 in ERROR_LOG_PARAMS structure
 *             Bit 31:0 - Reserved
 */
#define ABL_MEM_PMU_TRAIN_EVER_FAILED                                 (0x401B)

/**
 * @brief Mixed ECC and Non-ECC in System Error
 */
#define ABL_MEM_ERROR_MIXED_ECC_AND_NON_ECC_DIMM_IN_SYSTEM            (0x4020)

/**
 * @brief Agesa memory error mixed Ecc and Non-Ecc DIMM in a channel.
 *
 * @details The format of the data section of the Agesa memory error mixed record:
 *          DATA A in APOB_ERROR_LOG structure or DataParam1 in ERROR_LOG_PARAMS structure
 *             Bit 7:0 - Socket
 *             Bit 15:8 - Channel
 *             Bit 31:16 - reserved
 *          DATA B in APOB_ERROR_LOG structure or DataParam2 in ERROR_LOG_PARAMS structure
 *             Bit 31:0 - Reserved
 */
#define ABL_MEM_ERROR_MIXED_3DS_AND_NON_3DS_DIMM_IN_CHANNEL           (0x4021)

/**
 * @brief Agesa memory error mixed x4 and x8 DIMM in a channel.
 *
 * @details The format of the data section of the Agesa memory error mixed record:
 *          DATA A in APOB_ERROR_LOG structure or DataParam1 in ERROR_LOG_PARAMS structure
 *             Bit 7:0 - Socket
 *             Bit 15:8 - Channel
 *             Bit 31:16 - reserved
 *          DATA B in APOB_ERROR_LOG structure or DataParam2 in ERROR_LOG_PARAMS structure
 *             Bit 31:0 - Reserved
 */
#define ABL_MEM_ERROR_MIXED_X4_AND_X8_DIMM_IN_CHANNEL                 (0x4022)

/**
 * @brief Agesa memory error mixed RDIMM and LRDIMM.
 *
 * @details The format of the data section of the Agesa memory error mixed record:
 *          DATA A in APOB_ERROR_LOG structure or DataParam1 in ERROR_LOG_PARAMS structure
 *             Bit 7:0 - Socket
 *             Bit 15:8 - Channel
 *             Bit 31:16 - reserved
 *          DATA B in APOB_ERROR_LOG structure or DataParam2 in ERROR_LOG_PARAMS structure
 *             Bit 31:0 - Reserved
 */
#define ABL_MEM_ERROR_MIXED_DIFFERENT_ECC_SIZE_DIMM_IN_CHANNEL        (0x4028)

/**
 * @brief Agesa memory error installed on a channel where the UMC is not present.
 *
 * @details The format of the data section of the Agesa memory error installed record:
 *          DATA A in APOB_ERROR_LOG structure or DataParam1 in ERROR_LOG_PARAMS structure
 *             Bit 7:0 - Socket
 *             Bit 15:8 - Channel
 *             Bit 31:16 - reserved
 *          DATA B in APOB_ERROR_LOG structure or DataParam2 in ERROR_LOG_PARAMS structure
 *             Bit 31:0 - Reserved
 */
#define ABL_MEM_WARNING_MEM_INSTALLED_ON_DISCONNECTED_CHANNEL         (0x4029)

/**
 * @brief RRW Error
 */
#define ABL_MEM_RRW_ERROR                                             (0x402A)
/**
 * @brief Agesa memory mbist results error.
 *
 * @details The format of the data section of the Agesa memory mbist results error record:
 *          DATA A in APOB_ERROR_LOG structure or DataParam1 in ERROR_LOG_PARAMS structure
 *             Bit 31:0 - Reserved
 *          DATA B in APOB_ERROR_LOG structure or DataParam2 in ERROR_LOG_PARAMS structure
 *             Bit 31:0 - Reserved
 */
#define ABL_MEM_ERROR_MBIST_RESULTS_ERROR                             (0x4030)

/**
 * @brief LRDIMM Mixed Manufacturer Error
 * @details: Mix certain vendor LRDIMM with other vendor LRDIMM in the same channel
 */
#define ABL_MEM_ERROR_LRDIMM_MIXMFG                                   (0x4033)

/**
 * @brief RRW Error
 */
#define ABL_CCD_BIST_FAILURE                                          (0x4065)

/**
 * @brief Memory Healing BIST Error
 */
#define ABL_MEM_MEMORY_HEALING_BIST_ERROR                             (0x4067)

/**
 * @brief Agesa memory module population order error.
 *
 * @details Memory Module Population Order. In a 2DPCH system,
 *          the DIMMs within a channel should be populated starting with
 *          slot farthes from the memory controller, unless the platform
 *          uses balanced-tee routing.
 *          DATA A in APOB_ERROR_LOG structure or DataParam1 in ERROR_LOG_PARAMS structure
 *             Bit 7:0 - Socket
 *             Bit 15:8 - Channel
 *          DATA B in APOB_ERROR_LOG structure or DataParam2 in ERROR_LOG_PARAMS structure
 *             Bit 31:0 - Reserved
 */
#define ABL_MEM_ERROR_MODULE_POPULATION_ORDER                         (0x406A)

/**
 * @brief Agesa memory pmic error.
 *
 * @details The format of the data section of the Agesa memory PMIC error record:
 *          DATA A in APOB_ERROR_LOG structure or DataParam1 in ERROR_LOG_PARAMS structure
 *             Bits 7:0    - Socket
 *             Bits 15:8   - Channel
 *             Bit  16     - DIMM
 *             Bit  17     - Channel Status (0 = Disabled, 1 = Enabled)
 *             Bits 31:18  - Reserved (MBZ)
 *          DATA B in APOB_ERROR_LOG structure or DataParam2 in ERROR_LOG_PARAMS structure
 *             Bits 7:0   - PMIC Register 0x04
 *             Bits 15:8  - PMIC Register 0x05
 *             Bits 23:16 - PMIC Register 0x06
 *             Bits 31:24 - Reserved (MBZ)
 */
#define ABL_MEM_ERROR_PMIC_ERROR                                      (0x406B)

/**
 * @brief Agesa memory channel population order warning.
 *
 * @details Memory channels are recommended to be populated in the order shown in Memory Population Guidelines.
 *          Other configurations may be functional but may not be validated by AMD.
 *          DATA A in APOB_ERROR_LOG structure or DataParam1 in ERROR_LOG_PARAMS structure
 *             Bit 7:0 - Socket
 *             Bit 15:8 - reserved
 *             Bit 16 - System Halted
 *             Bit 31:17 - reserved
 *          DATA B in APOB_ERROR_LOG structure or DataParam2 in ERROR_LOG_PARAMS structure
 *             Bit 31:0 - Reserved
 */
#define ABL_MEM_CHANNEL_POPULATION_ORDER                              (0x406C)

/**
 * @brief DIMM SPD CRC verify failure
 *
 * @details DIMM SPD CRC verify feature controlled by APCB_TOKEN_UID_MEM_SPD_VERIFY_CRC
 *          Declare DIMM location with CRC verify failure at DATA A
 *          DATA A in APOB_ERROR_LOG structure or DataParam1 in ERROR_LOG_PARAMS structure
 *             Bit 7:0   - Socket
 *             Bit 15:8  - Channel
 *             Bit 23:16 - DIMM
 *             Bit 31:24 - Reserved
 *          DATA B in APOB_ERROR_LOG structure or DataParam2 in ERROR_LOG_PARAMS structure
 *             Bit 31:0  - Reserved
 */
#define ABL_MEM_SPD_VERIFY_CRC_ERROR                                  (0x406D)

/**
 * @brief - The PMIC is reporting that there is Real-time error
 *
 * @details DIMM PMIC reporting real time error, report PMIC register 0x8, 0x9, 0xA, 0xB & 0x33 contents
 *          DATA A APOB_ERROR_LOG structure or DataParam1 in ERROR_LOG_PARAMS structure
 *             Bits 7:0   - Socket
 *             Bits 15:8  - Channel
 *             Bits 16    - DIMM
 *             Bit 17     - Channel Status (0 = Disabled, 1 = Enabled)
 *             Bits 23:18 - Reserved (MBZ)
 *             Bits 31:24 - PMIC Register 0x33[000b, 4:0]
 *          DATA B in APOB_ERROR_LOG structure or DataParam2 in ERROR_LOG_PARAMS structure
 *             Bits 7:0   - PMIC Register 0x08[7:0]
 *             Bits 15:8  - PMIC Register 0x09[7:0]
 *             Bits 23:16 - PMIC Register 0x0A[7:0]
 *             Bits 31:24 - PMIC Register 0x0B[7:0]
 */
#define ABL_MEM_ERROR_PMIC_REAL_TIME_ERROR                            (0x406E)

/**
 * @brief HBM training errors
 *
 * @details The format os the data section of the HBM training errors record.
 *          DATA A APOB_ERROR_LOG structure or DataParam1 in ERROR_LOG_PARAMS structure
 *             Bit 7:0   - Socket Id
 *             Bit 15:8  - AID Id
 *             Bit 16    - HBM0/DIMM0 error detected under ps0
 *             Bit 17    - HBM1/DIMM1 error detected under ps0
 *             Bit 18    - HBM0/DIMM0 error detected under ps1
 *             Bit 19    - HBM1/DIMM1 error detected under ps1
 *             Bit 20    - HBM0/DIMM0 error detected under ps2
 *             Bit 21    - HBM1/DIMM1 error detected under ps2
 *             Bit 22    - HBM0/DIMM0 error detected under ps3
 *             Bit 23    - HBM1/DIMM1 error detected under ps3
 *             Bit 31:24 - reserved
 */
#define ABL_MEM_HBM_TRAIN_ERROR                                       (0x406F)

/**
 * @brief Self-Healing Mem BIST error
 *
 * @details Unrepairable fails remain after running Self-Healing Mem BIST.
 *          Declare package location with unrepairable fails at DATA A.
 *          EventClass   - AGESA_ERROR
 *          EventInfo    - 0x4072
 *          DATA A in APOB_ERROR_LOG structure or DataParam1 in ERROR_LOG_PARAMS structure
 *             Bit 2:0   - Socket
 *             Bit 6:3   - Channel
 *             Bit 7     - SubChannel
 *             Bit 9:8   - ChipSelect
 *             Bit 12:10 - RankMultiplier
 *             Bit 17:13 - TargetDevice
 *             Bit 31:18 - Reserved
 *          DATA B in APOB_ERROR_LOG structure or DataParam2 in ERROR_LOG_PARAMS structure
 *             Bit 31:0  - Reserved
 */
#define ABL_MEM_SELF_HEALING_BIST_ERROR                               (0x4072)

/**
 * @brief Self-Healing BIST Error Log Structure
 * @details
 */
typedef union _SELF_HEALING_BIST_ERROR_LOG_STRUCT {
  struct {
    UINT32 Socket         : 3;            ///< [2:0] Socket Number
    UINT32 Channel        : 4;            ///< [6:3] DDR Channel
    UINT32 SubChannel     : 1;            ///< [7:7] Sub Channel
    UINT32 ChipSelect     : 2;            ///< [9:8] ChipSelect
    UINT32 RankMultiplier : 3;            ///< [12:10] Rank Multiplier
    UINT32 TargetDevice   : 5;            ///< [17:13] Target Device
    UINT32 Reserved       : 14;           ///< [31:18] Reserved
  } Field;                                ///< Field
  UINT32 Value;                           ///< Value
} SELF_HEALING_BIST_ERROR_LOG_STRUCT;

/**
 * @brief GPU memory training errors
 *
 * @details The format of the data section of the (A+A) GPU memory training failure error record in previous boot:
 *          DATA A in APOB_ERROR_LOG structure or DataParam1 in ERROR_LOG_PARAMS structure
 *             GPU socketId = DataParam1[2:0], AID = DataParam1[4:3]
 *          DATA B in APOB_ERROR_LOG structure or DataParam2 in ERROR_LOG_PARAMS structure
 *             UMC ChannleId = DataParam2[7:0] , HBM stack = [15:8]
 */
#define MEM_ALERT_GPU_MEM_TRAINING_ERROR          (0x4010300)

/**
 * @brief GPU FW_load failure
 *
 * @details The format of the data section of the (A+A) GPU Slave Node WAFL Link Training error record:
 *          DATA A in APOB_ERROR_LOG structure or DataParam1 in ERROR_LOG_PARAMS structure
 *             GPU socketId = DataParam1[2:0], AID = DataParam1[4:3]
 *          DATA B in APOB_ERROR_LOG structure or DataParam2 in ERROR_LOG_PARAMS structure
 *             FWId = DataParam2
 */
#define GNB_EVENT_FW_LOAD_FAILURE                 (0x20040000)

/**
 * @brief GPU xGMI Error
 *
 * @details The format of the data section of the (A+A) GPU Slave Node XGMI Link Training error record:
 *          DATA A in APOB_ERROR_LOG structure or DataParam1 in ERROR_LOG_PARAMS structure
 *             GPU socketId = DataParam1[2:0], AID = DataParam1[4:3]
 *          DATA B in APOB_ERROR_LOG structure or DataParam2 in ERROR_LOG_PARAMS structure
 *             XGMI LinkId = DataParam2
 */
#define GNB_EVENT_GPU_ERR_XGMI_LINK_TRAINING      (0x20050000)

/**
 * @brief GPU WAFL Error
 *
 * @details The format of the data section of the (A+A) GPU Slave Node WAFL Link Training error record:
 *          DATA A in APOB_ERROR_LOG structure or DataParam1 in ERROR_LOG_PARAMS structure
 *             GPU socketId = DataParam1[2:0], AID = DataParam1[4:3]
 *          DATA B in APOB_ERROR_LOG structure or DataParam2 in ERROR_LOG_PARAMS structure
 *             WAFL LinkId = DataParam2
 */
#define GNB_EVENT_GPU_ERR_WAFL_LINK_TRAINING      (0x20060000)

/**
 * @brief GPU USR-CP link Error
 *
 * @details The format of the data section of the (A+A) GPU Slave Node USR CP Link Training error record:
 *          DATA A in APOB_ERROR_LOG structure or DataParam1 in ERROR_LOG_PARAMS structure
 *             GPU socketId = DataParam1[2:0], AID = DataParam1[4:3]
 *          DATA B in APOB_ERROR_LOG structure or DataParam2 in ERROR_LOG_PARAMS structure
 *             Link ID = DataParam2
 */
#define GNB_EVENT_USR_CP_LINK_TRAINING            (0x20070000)

/**
 * @brief GPU USR-DP link Error
 *
 * @details The format of the data section of the (A+A) GPU Slave Node USR DP Link Training error record:
 *          DATA A in APOB_ERROR_LOG structure or DataParam1 in ERROR_LOG_PARAMS structure
 *             GPU socketId = DataParam1[2:0], AID = DataParam1[4:3]
 *          DATA B in APOB_ERROR_LOG structure or DataParam2 in ERROR_LOG_PARAMS structure
 *             Link ID = DataParam2
 */
#define GNB_EVENT_USR_DP_LINK_TRAINING            (0x20080000)


/**
 * @brief GPU memory tester failure
 *
 * @details The format of the data section of the (A+A) GPU memory training failure error record in previous boot:
 *          DATA A in APOB_ERROR_LOG structure or DataParam1 in ERROR_LOG_PARAMS structure
 *             GPU socketId = DataParam1[2:0], AID = DataParam1[4:3]
 *          DATA B in APOB_ERROR_LOG structure or DataParam2 in ERROR_LOG_PARAMS structure
 *             UMC ChannleId = DataParam2[7:0] , HBM stack = [15:8]
 */
#define GNB_EVENT_MEM_TEST_FAILURE                (0x20090000)

/**
 * @brief GPU memory BIST failure
 *
 * @details The format of the data section of the (A+A) GPU memory BIST failure error record in previous boot:
 *          DATA A in APOB_ERROR_LOG structure or DataParam1 in ERROR_LOG_PARAMS structure
 *             GPU socketId = DataParam1[2:0], AID = DataParam1[4:3]
 *          DATA B in APOB_ERROR_LOG structure or DataParam2 in ERROR_LOG_PARAMS structure
 *             UMC ChannleId = DataParam2
 */
#define GNB_EVENT_MEM_BIST_TRAINING               (0x200A0000)

//
//AMD vendor specific error section GUID
//

/// NBIO Error Section GUID
#define NBIO_ERROR_SECT_GUID \
  { 0x911CAC2E, 0xA256, 0x4F84, {0xA3, 0xD3, 0xDD, 0xAE, 0x89, 0x9B, 0x27, 0x28} }
  //911CAC2E-A256-4F84-A3D3DDAE899B2728

/// SMU-SMN Error Section GUID
#define SMN_ERROR_SECT_GUID \
  { 0xA2860CC1, 0x8987, 0x4B7C, {0xB8, 0x6A, 0xD5, 0x08, 0xB1, 0x76, 0xBA, 0x70} }
  // A2860CC1-8987-4B7C-B86A-D508B176BA70

/// FCH SDP Parity Error Section GUID
#define FCH_SDP_PARITY_ERROR_SECT_GUID \
  { 0x259A0CF8, 0x293E, 0x4336, {0x90, 0x29, 0xB0, 0x5F, 0x1C, 0xFF, 0x58, 0x39} }
  // 259A0CF8-293E-4336-9029-B05F1CFF5839

/// Serial-ATA (SATA) Parity and ECC Error Section GUID
#define SATA_ERROR_SECT_GUID \
  { 0x7C27AAD8, 0x06D4, 0x4ABA, {0xB2, 0x0D, 0x42, 0xA8, 0x79, 0x50, 0xD0, 0xE4} }
  //7C27AAD8-06D4-4ABA-B20D-42A87950D0E4

/// Fusion Controller Hub (FCH) A-Link Parity Error Section GUID
#define FCH_ALINK_ERROR_SECT_GUID \
  { 0x896798ED, 0x2514, 0x4E2B, {0xB4, 0x5A, 0x15, 0x27, 0x3F, 0x30, 0x2B, 0x94} }
  //896798ED-2514-4E2B-B45A-15273F302B94

/// Universal Serial Bus (USB) Parity and ECC Error Section GUID
#define USB_ERROR_SECT_GUID \
  { 0xCC366EE9, 0x1EC5, 0x4140, {0x89, 0xBF, 0xE1, 0x28, 0xAC, 0xED, 0x27, 0xAB} }
  //CC366EE9-1EC5-4140-89BF-E128ACED27AB

/// PMIC Error Section GUID
#define PLATFORM_PMIC_SECT_GUID \
  { 0xFD2EBF1F, 0x5AC0, 0x42F1, {0x85, 0x36, 0x1E, 0x0F, 0x3B, 0x0E, 0x34, 0x66} }
  //FD2EBF1F-5AC0-42F1-8536-1E0F3B0E3466

/// Genreric Error Section GUID
#define GENERIC_ERROR_SECT_GUID \
  { 0xA9C00C2F, 0xBBC2, 0x4F25, {0x9D, 0xA4, 0x75, 0x21, 0x52, 0xA7, 0xF8, 0x7D} }
  //A9C00C2F-BBC2-4F25-9DA4-752152A7F87D

///SMM Save State Structure
/**
 * @brief SMRAM State-Save Area
 *
 * @details When an SMI occurs, the processor saves its state in the 512-byte SMRAM state-save area during the
 *          control transfer into SMM.
 */
typedef struct _SMM_SAVE_STATE {
   UINT64   ES[2];                           ///< FE00
   UINT64   CS[2];                           ///< FE10
   UINT64   SS[2];                           ///< FE20
   UINT64   DS[2];                           ///< FE30
   UINT64   FS[2];                           ///< FE40
   UINT64   GS[2];                           ///< FE50
   UINT64   GDTR[2];                         ///< FE60
   UINT64   LDTR[2];                         ///< FE70
   UINT64   IDTR[2];                         ///< FE80
   UINT64   TR[2];                           ///< FE90
   UINT64   IO_RESTAERT_RIP;                 ///< FEA0
   UINT64   IO_RESTAERT_RCX;                 ///< FEA8
   UINT64   IO_RESTAERT_RSI;                 ///< FEB0
   UINT64   IO_RESTAERT_RDI;                 ///< FEB8
   UINT32   TrapoFFSET;                      ///< FEC0
   UINT32   LocalSmiStatus;                  ///< FEC4
   UINT8    IoRestart;                       ///< FEC8
   UINT8    AutoHalt;                        ///< FEC9
   UINT8    NmiMask;                         ///< FECA
   UINT8    Reserved1[5];                    ///< FECB
   UINT64   EFER;                            ///< FED0
   UINT64   SvmState;                        ///< FED8
   UINT64   GuestVMCBPyysicalAddress;        ///< FEE0
   UINT64   SVMVirtualInterruptControl;      ///< FEE8
   UINT8    Reserved2[12];                   ///< FEF0
   UINT32   SMMRevId;                        ///< FEFC
   UINT32   SMBASE;                          ///< FF00
   UINT8    Reserved3[28];                   ///< FF04
   UINT64   GuestPAT;                        ///< FF20
   UINT64   HostEFER;                        ///< FF28
   UINT64   HostCR4;                         ///< FF30
   UINT64   NestedCR3;                       ///< FF38
   UINT64   HostCR0;                         ///< FF40
   UINT64   CR4;                             ///< FF48
   UINT64   CR3;                             ///< FF50
   UINT64   CR0;                             ///< FF58
   UINT64   DR7;                             ///< FF60
   UINT64   DR6;                             ///< FF68
   UINT64   RFLAGS;                          ///< FF70
   UINT64   RIP;                             ///< FF78
   UINT64   R15;                             ///< FF80
   UINT64   R14;                             ///< FF88
   UINT64   R13;                             ///< FF90
   UINT64   R12;                             ///< FF98
   UINT64   R11;                             ///< FFA0
   UINT64   R10;                             ///< FFA8
   UINT64   R9;                              ///< FFB0
   UINT64   R8;                              ///< FFB8
   UINT64   RDI;                             ///< FFC0
   UINT64   RSI;                             ///< FFC8
   UINT64   RBP;                             ///< FFD0
   UINT64   RSP;                             ///< FFD8
   UINT64   RBX;                             ///< FFE0
   UINT64   RDX;                             ///< FFE8
   UINT64   RCX;                             ///< FFF0
   UINT64   RAX;                             ///< FFF8
} SMM_SAVE_STATE;

/// RAS NBIO Group Type enumerate
typedef enum _NBIO_GRP_TYPE_NUM {
  NBIO_GRP_UNCORR = 0,      ///< / 0  = Uncorrectable Parity Group
  NBIO_GRP_CORR = 1,        ///< / 1  = Correctable Parity Group
  NBIO_GRP_UCP = 3,         ///< / 3  = Uncorrectable converts to poison data parity group
} NBIO_GRP_TYPE_NUM;

/// RAS MCA Bank enumerate
typedef enum _MCA_INT_TYPE_NUM {
//CORE MCA Banks, access through each thread.
  MCA_NO_INTERRUPT = 0,      ///< / 0  = No Interrupt
  MCA_APIC,                  ///< / 1  = APIC based interrupt (LVT)
  MCA_SMI,                   ///< / 2  = SMI trigger event
  MCA_RESERVED,              ///< / 3  = Reserved
} MCA_INT_TYPE_NUM;

/// RAS SMN Category enumerate
typedef enum _SMN_CATEGORY_NUM {
  SMN_CATEGORY_UNCORR = 0,   ///< / 0  = Uncorrectable Category
  SMN_CATEGORY_CORR = 1,     ///< / 1  = Correctable Category
  SMN_CATEGORY_FATAL = 2,    ///< / 2  = Fatal Category
  SMN_CATEGORY_UCP = 3,      ///< / 3  = Uncorrectable converts to poison data Category
} SMN_CATEGORY_NUM;


/**
 * @brief Local SMI Status
 * @details
 */
typedef union {
  struct {
     UINT64       IoTrapSts:4;              ///< [3:0] IO Trap Status
     UINT64       :4;                       ///< [7:4] Reserved
     UINT64       MceRedirSts:1;            ///< [8] Machine check exception redirection status
     UINT64       :2;                       ///< [10:9] Reserved
     UINT64       WrMsr:1;                  ///< [11] SMM due to a WRMSR of an MCE_STATUS
     UINT64       :4;                       ///< [15:12] Reserved
     UINT64       SmiSrcLvtLgcy:1;          ///< [16] SMI source is legacy LVT APIC entry
     UINT64       SmiSrcLvtExt:1;           ///< [17] SMI source is APIC[530:500] LVT
     UINT64       SmiSrcMca:1;              ///< [18] SMI source is MCA
     UINT64       :54;                      ///< [63:19] Reserved
  } Field;                                  ///< Field
  UINT64  Value;                            ///< Value
} LOCAL_SMI_STATUS;


/**
 * @brief UMC Miscellaneous Configuration register
 * @details
 */
typedef union {
  struct {
     UINT32       RegchClkGateEn:1;         ///< Enable configuration register clock gating
     UINT32       DisDebugBusSel:1;         ///< Specific UMC debug bus configuration writes.
     UINT32       DisErrInj:1;              ///< Specific UMC error injection configuration writes.
     UINT32       MCAGateDis:1;             ///< Disables medium grain clock gating for MCA logic.
     UINT32       :2;                       ///< Reserved.
     UINT32       BlockAbHalfRefreshed:1;   ///< Specifies if SPAZ blocks REFab after half of the banks are refreshed by REFpb.
     UINT32       SpecPchgClkGateDis:1;     ///< Disables medium grain clock gating in SPECPCHG block of ARB.
     UINT32       :24;                      ///< Not used.
  } Field;                                  ///< Field
  UINT32  Value;                            ///< Value
} UMC_MISCCFG_REG;


/**
 * @brief UMC Ecc Control Register
 * @details
 */
typedef union {
  struct {
     UINT32       WrEccEn:1;                ///< Enables ECC generation for DRAM data.
     UINT32       :3;                       ///< Reserved
     UINT32       EccBadDramSymEn:1;        ///< Enables the software managed ECC history mechanism for x8 symbol size.
     UINT32       EccHardwareHistoryEn:1;   ///< Enables the hardware managed ECC history mechanism for x8 symbol size.
     UINT32       EccBitInterleaving:1;     ///< Enables data burst bit interleaving for ECC.
     UINT32       EccSymbolSize:1;          ///< 0=x4 symbol. 1=x8 symbol.
     UINT32       UCFatalEn:1;              ///< Promote uncorrectable errors from deferred to fatal.
     UINT32       EccSymbolSize16:1;        ///< 1=x16 symbol. 0=use EccSymbolSize
     UINT32       RdEccEn:1;                ///< Enable DRAM data ECC checking and correction.
     UINT32       :21;                      ///< Reserved
  } Field;                                  ///< Field
  UINT32  Value;                            ///< Value
} UMC_ECCCTRL_REG;


/**
 * @brief UMC ECC Error Inject Control Register
 * @details
 */
typedef union {
  struct {
     UINT32       EccErrPersistentEn:1;     ///< Enable continuous error injection.
     UINT32       EccErrOneShotEn:1;        ///< Enable a single error injection.
     UINT32       EccErrAddrEn:1;           ///< Allow address-based injection from the DF CS.
     UINT32       :29;                      ///< Reserved
  } Field;                                  ///< Field
  UINT32  Value;                            ///< Value
} UMC_ECCERRINJCTRL_REG;


/**
 * @brief RAS UMC EXT_ERROR_CODE enumerate
 * @details
 */
typedef enum _UMC_EXT_ERROR_CODE_NUM {
  UMC_DRAMECCERR = 0,                       ///< DRAM ECC error.
  UMC_WRITEDATAPOISONERR,                   ///< Data poison error.
  UMC_SDPPARITYERR,                         ///< SDP parity error.
  UMC_APBERR,                               ///< Advanced peripheral bus error.
  UMC_ADDRCMDPARITYERR,                     ///< Address/command parity error.
  UMC_WRITEDATACRCERR,                      ///< Write data CRC error.
  UMC_DCQSRAMECCERR,                        ///< An ECC error occurred on a DCQ SRAM.
  UMC_AESSRAMECCERR,                        ///< An ECC error occurred on a AES SRAM.
  UMC_ECSROWERR,                            ///< A single device row exceeded four code word errors.
  UMC_ECSERR,                               ///< A device exceeded the ECS Error Threshold Count.
  UMC_THRTTLERR,                            ///< UMC is throttling.
  UMC_RDCRCERR                              ///< Read CRC error. CRC error occurred on a DRAM read from any subchannel.
} UMC_EXT_ERROR_CODE_NUM;


/**
 * @brief UMC Error Inject Register
 * @details
 */
typedef union {
  struct {
     UINT32       EccInjVector:16;          ///< Specifies the pattern XORed into each double byte of data specified by the EccInjEn field.
     UINT32       EccInjEn:9;               ///< Specifies the double byte of data that the pattern in EccInjVector should be XORed to.
     UINT32       :7;                       ///< Reserved
  } Field;                                  ///< Field
  UINT32  Value;                            ///< Value
} UMC_ECCERRINJ_REG;

///DF
/// @cond !(RS || BRH)
/**
 * @brief DRAM Scrubber Base Address Register
 * @details
 */
typedef union {
  /// Bitfields of DRAM Scrubber Base Address Register
  struct {
    UINT32 DramScrubEn:1;              ///< DRAM Scrub Enable
    UINT32 :3;                         ///< Reserved
    UINT32 DramScrubBaseAddr:28;       ///< DRAM Scrub Base Address
  } Field;                             ///< Field
  UINT32  Value;                       ///< Value
} DRAM_SCRUB_BASEADDR_REG;
/// @endcond


/**
 * @brief DRAM Scrubber Error Address Lo Register
 * @details
 */
typedef union {
  /// Bitfields of DRAM Scrubber Error Address Lo Register
  struct {
    UINT32 ErrInjEn:1;                      ///< A new command to inject error is triggered by writing a 1
    UINT32 ErrInjDone:1;                    ///< Set by hardware once the write is sent to CS
    UINT32 LockAndDisErrInj:1;              ///< Lock error injection widget
    UINT32 :3;                              ///< Reserved
    UINT32 ErrInjAddrLo:26;                 ///< Error injection address low
  } Field;                                  ///< Field
  UINT32  Value;                            ///< Value
} DRAM_SCRUB_ERRORADDR_LO_REG;


/**
 * @brief DRAM Scrubber Error Address Hi Register
 * @details
 */
typedef union {
  /// Bitfields of DRAM Scrubber Error Address Hi Register
  struct {
    UINT32 ErrInjAddrHi:24;                 ///< Error injection address high
    UINT32 :8;                              ///< Reserved
  } Field;                                  ///< Field
  UINT32  Value;                            ///< Value
} DRAM_SCRUB_ERRORADDR_HI_REG;

/// @cond !(RS || BRH)
/**
 * @brief DRAM Scrubber Address Lo Register
 * @details
 */
typedef union {
  /// Bitfields of DRAM Scrubber Address Lo Register
  struct {
    UINT32 MemInitEnable :1;                ///< Enables memory initialization
    UINT32 MemInitDone   :1;                ///< This bit is set by hardware once it has cleared the memory
    UINT32 DisMemInit    :1;                ///< BIOS or PSP should set the bit when it wants to protect any other malicious software from clearing memory
    UINT32               :3;                ///< Reserved
    UINT32 SeqScrubAddrLo:26;               ///< Scrubber Address Low
  } Field;                                  ///< Field
  UINT32  Value;                            ///< Value
} DRAM_SCRUB_ADDR_LO_REG;


/**
 * @brief DRAM Scrubber Address Hi Register
 * @details
 */
typedef union {
  /// Bitfields of DRAM Scrubber Address Hi Register
  struct {
    UINT32               :4;                ///< Reserved
    UINT32 SeqScrubAddrHi:16;               ///< Scrubber Address High
    UINT32               :12;               ///< Reserved
  } Field;                                  ///< Field
  UINT32  Value;                            ///< Value
} DRAM_SCRUB_ADDR_HI_REG;
/// @endcond

/**
 * @brief MCA Status MSR
 * @details
 */
typedef union {
  struct {
     UINT32       ErrorCode:16;             ///< Error code for this error
     UINT32       ErrorCodeExt:6;           ///< This field is used to identify the error type for root cause analysis.
     UINT32       :2;                       ///< Reserved
     UINT32       AddrLsb:6;                ///< Specifies the least significant valid bit of the address contained in MCA_ADDR[ErrorAddr].
     UINT32       :2;                       ///< Reserved
     UINT32       ErrCoreId:6;              ///< This field indicates which core within the processor is associated with the error
     UINT32       :2;                       ///< Reserved
     UINT32       Scrub:1;                  ///< 1 = The error was the result of a scrub operation.
     UINT32       :2;                       ///< Reserved
     UINT32       Poison:1;                 ///< 1 = The error was the result of attempting to consume poisoned data.
     UINT32       Deferred:1;               ///< 1 = A deferred error was created.
     UINT32       UECC:1;                   ///< 1 = The error was an uncorrectable ECC error
     UINT32       CECC:1;                   ///< 1 = The error was a correctable ECC error
     UINT32       :5;                       ///< Reserved
     UINT32       Transparent:1;            ///< 1 = This error is a transparent error.
     UINT32       SyndV:1;                  ///< 1 = This error logged information in MCA_SYND
     UINT32       :1;                       ///< Reserved
     UINT32       TCC:1;                    ///< 1 = The thread which consumed the error is not restartable and must be terminated.
     UINT32       ErrCoreIdVal:1;           ///< 1 = The ErrCoreId field is valid
     UINT32       PCC:1;                    ///< 1 = Hardware context held by the processor may have been corrupted
     UINT32       AddrV:1;                  ///< 1 = MCA_ADDR contains address information
     UINT32       MiscV:1;                  ///< 1 = Valid thresholding in MCA_MISC0
     UINT32       En:1;                     ///< 1 = MCA error reporting is enabled for this error.
     UINT32       UC:1;                     ///< 1 = The error was not corrected by hardware
     UINT32       Overflow:1;               ///< 1 = An error was detected while the valid bit was set
     UINT32       Val:1;                    ///< 1 = A valid error has been detected
  } Field;                                  ///< Field
  UINT64  Value;                            ///< Value
} MCA_STATUS_MSR;

/**
 * @brief MCA DESTAT MSR
 * @details
 */
typedef union {
  struct {
    UINT64       ErrorCode:16;             ///< Error code for this error.
    UINT64       ErrorCodeExt:6;           ///< Logs an extended error code when an error is detected.
    UINT64       :2;                       ///< Reserved
    UINT64       AddrLsb:6;                ///< Specifies the least significant valid bit of the address contained in MCA_ADDR[ErrorAddr].
    UINT64       :14;                      ///< Reserved
    UINT64       Deferred:1;               ///< 1 = A deferred error was created.
    UINT64       :8;                       ///< Reserved
    UINT64       SyndV:1;                  ///< 1 = This error logged information in MCA_SYND
    UINT64       :4;                       ///< Reserved
    UINT64       AddrV:1;                  ///< 1 = MCA_ADDR contains address information
    UINT64       :3;                       ///< Reserved
    UINT64       Overflow:1;               ///< 1 = An error was detected while the valid bit was set
    UINT64       Val:1;                    ///< 1 = A valid error has been detected
  } Field;                                 ///< Field
  UINT64  Value;                           ///< Value
} MCA_DESTAT_MSR;

/**
 * @brief MCA ADDR MSR
 * @details
 */
typedef union {
  struct {
     UINT64       ErrorAddr:64;             ///< Error Address
  } Field;                                  ///< Field
  UINT64  Value;                            ///< Value
} MCA_ADDR_MSR;

/**
 * @brief MCA MISC0 MSR
 * @details
 */
typedef union {
  struct {
     UINT64       :24;                       ///< Reserved
     UINT64       BlkPtr:8;                  ///< 01h=Extended MSR block is valid.
     UINT64       ErrCnt:12;                 ///< Error Counter
     UINT64       :4;                        ///< Reserved
     UINT64       Ovrflw:1;                  ///< Set by hardware when ErrCnt transitions from FFEh to FFFh.
     UINT64       ThresholdIntType:2;        ///< Type of interrupt signal
     UINT64       CntEn:1;                   ///< 1=Count thresholding errors.
     UINT64       LvtOffset:4;               ///< Error thresholding interrupt LVT address
     UINT64       :4;                        ///< Reserved
     UINT64       IntP:1;                    ///< 1=ThresholdIntType can be used to generate interrupts.
     UINT64       Locked:1;                  ///< 1=Writes to this register are ignored
     UINT64       CntP:1;                    ///< 1=A valid threshold counter is present
     UINT64       Valid:1;                   ///< 1=A valid CntP field is present in this register.
  } Field;                                   ///< Field
  UINT64  Value;                             ///< Value
} MCA_MISC0_MSR;

/**
 * @brief MCA MISC1 MSR
 * @details
 */
typedef union {
  struct {
     UINT64       :24;                       ///< Reserved
     UINT64       BlkPtr:8;                  ///< 01h=Extended MSR block is valid.
     UINT64       ErrCnt:12;                 ///< Error Counter
     UINT64       :4;                        ///< Reserved
     UINT64       Ovrflw:1;                  ///< Set by hardware when ErrCnt transitions from FFEh to FFFh.
     UINT64       ThresholdIntType:2;        ///< Type of interrupt signal
     UINT64       CntEn:1;                   ///< 1=Count thresholding errors.
     UINT64       :8;                        ///< Reserved
     UINT64       IntP:1;                    ///< 1=ThresholdIntType can be used to generate interrupts.
     UINT64       Locked:1;                  ///< 1=Writes to this register are ignored
     UINT64       CntP:1;                    ///< 1=A valid threshold counter is present
     UINT64       Valid:1;                   ///< 1=A valid CntP field is present in this register.
  } Field;                                   ///< Field
  UINT64  Value;                             ///< Value
} MCA_MISC1_MSR;

/**
 * @brief MCA CONFIG MSR
 * @details
 */
typedef union {
  struct {
     UINT64       McaX:1;                               ///< Reserved
     UINT64       TransparentErrorLoggingSupported:1;   ///< 01h=Extended MSR block is valid.
     UINT64       DeferredErrorLoggingSupported:1;      ///< Error Counter
     UINT64       :2;                                   ///< Reserved
     UINT64       DeferredIntTypeSupported:1;           ///< Set by hardware when ErrCnt transitions from FFEh to FFFh.
     UINT64       :2;                                   ///< Reserved
     UINT64       McaLsbInStatusSupported:1;            ///< 1=indactes that AddrLbc is located in McaStatus registers.
     UINT64       McaFruTextInMca:1;                    ///< 1=FruText is reported McaSynd1/McaSynd2 registers
     UINT64       IntPresent:1;                         ///< 1=indicates that this bank can be configured to trigger a corrected machine check interrupt.
     UINT64       :21;                                  ///< Reserved
     UINT64       McaXEnable:1;                         ///< Type of interrupt signal
     UINT64       TransparentErrorLoggingEnable:1;      ///< 1=Count thresholding errors.
     UINT64       LogDeferredInMcaStat:1;               ///< Log Deferred error in MCA_STATUS
     UINT64       :2;                                   ///< Reserved
     UINT64       DeferredIntType:2;                    ///< Type of Deferred error interrupt.
     UINT64       McaFatalMask:1;                       ///< 1=Fatal errors in this bank will not generate a System Fatal Error Event.
     UINT64       IntEn:1;                              ///< 1=When set, this bank will generate CMCIs on corrected errors (if a threshold counter is present in MCA_MISC)
     UINT64       :23;                                  ///< Reserved
  } Field;                                              ///< Field
  UINT64  Value;                                        ///< Value
} MCA_CONFIG_MSR;

/// Syndrome Error Priority
/**
 * @brief ErrorPriority field of MCA_SYND register
 *
 * @details The ErrorPriority (Bit 26:24) encodes the priority of the error logged in MCA_SYND register
 *          3'b000 = 0x00 = No error
 *          3'b001 = 0x01 = Reserved
 *          3'b010 = 0x02 = Corrected Error
 *          3'b011 = 0x03 = Deferred Error
 *          3'b100 = 0x04 = Uncorrected Error
 *          3'b101 = 0x05 = Fatal Error
 *          all others reserved.
 */
#define MCA_SYND_ERROR_PRIORITY_NO_ERROR    0x00  ///< No error
#define MCA_SYND_ERROR_PRIORITY_RESERVED    0x01  ///< Reserved
#define MCA_SYND_ERROR_PRIORITY_CORRECTED   0x02  ///< Corrected Error
#define MCA_SYND_ERROR_PRIORITY_DEFERRED    0x03  ///< Deferred Error
#define MCA_SYND_ERROR_PRIORITY_UNCORRECTED 0x04  ///< Uncorrected Error
#define MCA_SYND_ERROR_PRIORITY_FATAL       0x05  ///< Fatal Error

/// Syndrome Types
/**
 * @brief Type field of MCA_SYND register
 *
 * @details The Type (Bit 31:28) specifies the type of error contained in MCA_SYND register
 *          4'b0000         = 0x00 = DRAM error
 *          4'b0001         = 0x01 = Cache error
 *          4'b0010-4'b1111 = 0x02 = Other error
 */
#define MCA_SYND_TYPE_DRAM_ERR     0x00  ///< DRAM error
#define MCA_SYND_TYPE_CACHE_ERR    0x01  ///< Cache error
#define MCA_SYND_TYPE_INTERNAL_ERR 0x02  ///< Other error

/**
 * @brief MCA SYND MSR
 * @details
 */
typedef union {
  struct {
     UINT32       ErrorInformation:18;      ///< Contains error-specific information about the location of the error.
     UINT32       Length:6;                 ///< Specifies the length in bits of the syndrome.
     UINT32       ErrorPriority:3;          ///< Encodes the priority of the error logged.
     UINT32       :1;                       ///< Reserved
     UINT32       Type:4;                   ///< Type
     UINT32       Syndrome:32;              ///< Contains the syndrome, if any, associated with the error.
  } Field;                                  ///< Field
  UINT64  Value;                            ///< Value
} MCA_SYND_MSR;

/**
 * @brief MCA IPID MSR
 * @details
 */
typedef union {
  struct {
     UINT32       InstanceId:32;            ///< The instance ID of this IP.
     UINT32       HardwareID:12;            ///< The Hardware ID of the IP associated with this MCA bank.
     UINT32       InstanceIdHi:4;           ///< The Hi value instance ID of this IP.
     UINT32       McaType:16;               ///< The McaType of the MCA bank within this IP.
  } Field;                                  ///< Field
  UINT64  Value;                            ///< Value
} MCA_IPID_MSR;

/**
 * @brief MCA SYNDX MSR
 * @details
 */
typedef union {
  struct {
     UINT64       Syndrome:64;              ///< Contains the syndrome, if any, associated with the error logged in MCA::UMC::MCA_STATUS_UMC
  } Field;                                  ///< Field
  UINT64  Value;                            ///< Value
} MCA_SYNDX_MSR;

/**
 * @brief A structure for storing CPU related information.
 *
 * @details RAS feature code stores all enabled CPU related information in the CPU_INFO array,
 *          this array can be queried through AMD_RAS_POLICY.
 *
 */
typedef struct _CPU_INFO {
  UINTN        ProcessorNumber;         ///< The ProcessorNumber of this Processor.
  UINT8        SocketId;                ///< The Socket Id of this Processor.
  UINT8        CcxId;                   ///< The Ccx Id of this Processor.
  UINT8        DieId;                   ///< The Die Id of this Processor.
  UINT8        CoreId;                  ///< The Core Id of this Processor.
  UINT8        ThreadID;                ///< The Thread ID of this Processor.
} CPU_INFO;

/**
 * @brief A structure for storing CPU related information from RasCpuMap.
 * @details
 */
typedef union {
  struct {
     UINT32       ThreadID:4;           ///< The Thread ID of this Processor.
     UINT32       CoreId:8;             ///< The Core ID of this Processor.
     UINT32       DieId:8;              ///< The Die ID of this Processor.
     UINT32       CcxId:8;              ///< The CCX ID of this Processor.
     UINT32       SocketId:4;           ///< The Socket ID of this Processor.
  } Field;                              ///< Field
  UINT32  Value;                        ///< Value
} CORE_MCA_INSTANCEID;

/**
 * @brief The number of MCA register in the Register Array of IA32/X64 Processor Context Information Structure
 *
 * @details A total of 9 MCA registers are stored in IA32/X64 Processor Context Information Structure:
 *          MCA_STATUS, MCA_ADDR, MCA_MISC0, MCA_CONFIG, MCA_IPID, MCA_SYND, MCA_DESTATUS, MCA_DEADDR, MCA_MISC1, MCA_SYND1, MCA_SYND2.
 */
#define MCA_BANK_ERROR_INFO_REG_NUM     (15)

/**
 * @brief The maximum number of CPU/PIE WDT error related info in the Register Array of
 *        IA32/X64 Processor Context Information Structure
 *
 * @details When WDT timeout occurs, CPM RAS stores the "last I/O address sent that was sent" or "HW_ASSERT status"
 *          in the second "IA32/X64 processor context information structure" in an error record of the BERT.
 *          Related registers:
 *          DF::HardwareAssertStatusLow and DF::HardwareAssertStatusHigh
 *          DF::RSPQWDTIoTransLogLow and DF::RSPQWDTIoTransLogHi.
 *          DF::OrigWdtAddrLogLo and DF::OrigWdtAddrLogHi
 */
#define MAX_SEC_PROC_CONTEXT_INFO_ARRY_NUM  (128)

/**
 * @brief This structure stores the MCA registers of the block where the error occurred.
 *
 * @details A processor contains multiple blocks. When an error occurs in the block,
 *          RAS feature code stores the MCA registers in the block into MCA_BANK_ERROR_INFO structure
 *          for reference by the error handler.
 *          the register order in the structure need match to the actual register MSR address offset
 *          the counting start from MCA_STATUS at offset 0x1
 */
typedef struct _MCA_BANK_ERROR_INFO {
  UINTN                 McaBankNumber;  ///< Mca Bank Number
  MCA_STATUS_MSR        McaStatusMsr;   ///< Mca Statu sMsr
  MCA_ADDR_MSR          McaAddrMsr;     ///< Mca Addr Msr
  MCA_MISC0_MSR         McaMisc0Msr;    ///< Mca Misc0 Msr
  MCA_CONFIG_MSR        McaConfigMsr;   ///< Mca ConfigMsr
  MCA_IPID_MSR          McaIpidMsr;     ///< Mca Ipid Msr
  MCA_SYND_MSR          McaSyndMsr;     ///< Mca Synd Msr
  MCA_DESTAT_MSR        McaDeStatMsr;   ///< Mca DeStat Msr
  MCA_ADDR_MSR          McaDeAddrMsr;   ///< Mca DeAddr Msr
  MCA_MISC1_MSR         McaMisc1Msr;    ///< Mca Misc1 Msr
  UINT64                Reserved1;      ///< Reserved1
  UINT64                Reserved2;      ///< Reserved2
  UINT64                Reserved3;      ///< Reserved3
  UINT64                Reserved4;      ///< Reserved4
  MCA_SYNDX_MSR         McaSynd1Msr;    ///< Mca Synd1 Msr
  MCA_SYNDX_MSR         McaSynd2Msr;    ///< Mca Synd2 Msr
} MCA_BANK_ERROR_INFO;

/**
 * @brief The maximum number of machine check banks visible to the thread on ZP (Naples).
 *
 * @details The number of error reporting banks visible to each core is fixed to 23
 */
#define ZP_MCA_MAX_BANK_COUNT (23)

/**
 * @brief The maximum number of machine check banks visible to a logical core on SSP (Rome) and GN (Milan).
 *
 * @details The number of visible error reporting banks may vary from core to core,
 *          but the maximum value of this number is 28.
 */
#define SSP_MCA_MAX_BANK_COUNT (28)

/**
 * @brief The maximum number of machine check banks visible to a logical core on RS (Genoa) or later programs.
 *
 * @details The number of visible error reporting banks may vary from core to core,
 *          but the maximum value of this number is 64.
 */
#define XMCA_MAX_BANK_COUNT (64)

/**
 * @brief The number of Core MCA Bank (on SSP, GN , RS and BRH).
 *
 * @details The Core MCA banks (7 banks in total) are present in the address space of every logical core.
 */
#define MAX_CORE_MCA_BANK_COUNT (7)

/**
 * @brief This structure stores the MCA register of the block where the error occurred and the
 *        processor information associated with it.
 *
 * @details A processor contains multiple blocks. When an error occurs in the block,
 *          RAS feature code stores the MCA registers in the block into MCA_BANK_ERROR_INFO structure
 *          and stores processor information in CPU_INFO structure for reference by the error handler.
 */
typedef struct _RAS_MCA_ERROR_INFO {
  CPU_INFO              CpuInfo;                                  ///< Logical processor to Physical processor map.
  MCA_BANK_ERROR_INFO   McaBankErrorInfo[ZP_MCA_MAX_BANK_COUNT];  ///< MCA bank regster dump.
} RAS_MCA_ERROR_INFO;

/**
 * @brief This structure stores the MCA register of the block where the error occurred and the bank number
 *        and processor information associated with it.
 *
 * @details A processor contains multiple blocks. When an error occurs in the block,
 *          RAS feature code stores the MCA registers in the block into MCA_BANK_ERROR_INFO structure,
 *          stores the bank number in McaBankCount, and stores the processor information in the CPU_INFO
 *          structure for reference by the error handler.
 */
typedef struct _RAS_MCA_ERROR_INFO_V2 {
  CPU_INFO              CpuInfo;                                ///< Logical processor to Physical processor map.
  UINTN                 McaBankCount;                           ///< For SSP/GN/BA This is the max bank number support for the processor.
                                                                ///< For RS or later, this is the Error bank count from the processor
  MCA_BANK_ERROR_INFO   McaBankErrorInfo[XMCA_MAX_BANK_COUNT];  ///< MCA bank regster dump.
} RAS_MCA_ERROR_INFO_V2;

/**
 * @brief This structure stores information related to the error in NBIO When an error occurs in NBIO.
 *
 * @details A processor contains multiple NBIO. When an error occurs in the NBIO,
 *          RAS feature code stores the error status, NBIO compnent info and
 *          NBIO info in the RAS_NBIO_ERROR_INFO structure.
 *
 *          The RAS_NBIO_ERROR_INFO structure is no longer used in RS (Genoa) or later programs
 *          because these errors have been reported through MCA instead.
 */
typedef struct _RAS_NBIO_ERROR_INFO {
  UINT8                 Die;                    ///< Die Number of NBIO
  UINT8                 DieBusNumber;           ///< The Bus Number of NBIO
  NBIO_GRP_TYPE_NUM     TypeId;                 ///< RAS NBIO Group Type enumerate
  UINT8                 GroupId;                ///< Group Id
  UINT32                NbioGlobalStatusLo;     ///< NBIO Global Status Low
  UINT32                NbioGlobalStatusHi;     ///< NBIO Global Status Hihg
  UINT32                NbioParityErrorStsAddr; ///< The address of NBIO Parity Error Status (depend on NBIO_GRP_TYPE_NUM)
  UINT32                NbioParityErrorSts;     ///< Store the value of NBIO Parity Error Status
} RAS_NBIO_ERROR_INFO;

/**
 * @brief Used to note that the Socket ID is an invalid.
 *
 * @details The maximum socket ID is not greater than or equal to 0xFF,
 *          so use 0xFF to indicate that this is an invalid socket ID.
 */
#define INVALID_SOCKET_ID (0xFF)

/// Request the identity of dimm from system address
typedef struct {
  UINT64  normalizedAddr;               ///< Error Address that needs to be translated to dimm identification.
  UINT8   normalizedSocketId;           ///< The socket on which the targeted address locates.
  UINT8   normalizedDieId;              ///< The die on which the targeted address locates.
  UINT8   normalizedChannelId;          ///< The ChannelId on which the targeted address locates.
  UINT8   reserved;                     ///< Reserved
} NORMALIZED_ADDRESS;

/**
 * @brief This structure stores DIMM information.
 *
 * @details When an ECC error occurs in a DIMM, the RAS feature code converts the Normalized Address
 *          in MCA into a DRAM address and stores it in DIMM_INFO structure.
 */
typedef struct _DIMM_INFO {
  UINT8     ChipSelect;                 ///< The chip select on which the targeted address locates.
  UINT8     Bank;                       ///< The Bank for which the error address resides
  UINT32    Row;                        ///< The Row for which the error address resides
  UINT16    Column;                     ///< The Column for which the error address resides
  UINT8     rankmul;                    ///< The Rank Multiplex for which the error address resides
  UINT8     DimmId;                     ///< Reserved
  UINT8     subchan;                    ///< The subchannel for which the error address resides
  UINT8     StackId;                    ///< The StackId for which the error address resides
} DIMM_INFO;

/**
 * @brief This structure stores the error threshold related fields (CntEn, ErrCnt and ThresholdIntType) of the MCA MISC register.
 *
 * @details RAS feature code refer the content of RAS_THRESHOLD_CONFIG structure to set all MCA MISC registers
  *         (so that all MCA_MISC registers will have the same error threshold configuration.)
 */
typedef struct _RAS_THRESHOLD_CONFIG {
  BOOLEAN  ThresholdControl;             ///<  Control to Count thresholding errors.
  UINT16   ThresholdCount;               ///<  This is written by software to set the starting value of the error counter.
  UINT8    ThresholdIntType;             ///<  Specifies the type of interrupt signaled when Ovrflw is set.
} RAS_THRESHOLD_CONFIG;

//
//AMD vendor specific error section structure
//
/**
 * @brief The DXIO ID of the DXIO error source (Generic Error Status Block - NBIO and NBIF Errors).
 *
 * @details When a DXIO error occurs, it is used to indicate the PHY type.
 *          The NBIO and NBIF Errors structure has been deprecated from RS (Genoa)
 *          because these errors have been reported through MCA instead.
 */
#define DXIO_PHYTYPE_PCIE     (0)       ///< PHY type for PCIE
#define DXIO_PHYTYPE_SATA     (1)       ///< PHY type for SATA
#define DXIO_PHYTYPE_XGBE     (2)       ///< PHY type for XGBE
#define DXIO_PHYTYPE_XGMI     (3)       ///< PHY type for XGMI
#define DXIO_PHYTYPE_WAFL     (4)       ///< PHY type for WAFL
#define DXIO_PHYTYPE_WAFLPCIE (5)       ///< PHY type for WAFLPCIE

/**
 * @brief A structure for DXIO error Id.
 * @details
 */
typedef union {
  struct {
    UINT8        Pcs:2;                 ///< [1:0] Pcs Intance
    UINT8        Id:3;                  ///< [4:2] The ID of the Type
    UINT8        PhyType:3;             ///< [7:5] Error Source Phy Type
  } Field;                              ///< Field
  UINT8  Value;                         ///< Value
} DXIO_ERR_ID;

/**
 * @brief A structure for storing DXIO error Id.
 * @details
 */
typedef union {
  struct {
    UINT32       BusId:1;               ///< [0] Bus ID
    UINT32       ErrorSource:1;         ///< [1] Error Source
    UINT32       ErrorType:1;           ///< [2] Error Type
    UINT32       GroupType:1;           ///< [3] Group Type
    UINT32       GroupId:1;             ///< [4] Group ID
    UINT32       NbifId:1;              ///< [5] Leaf ID
    UINT32       LeafId:1;              ///< [6] Leaf ID
    UINT32       ParityErrSts:1;        ///< [7] Parity Error Status
    UINT32       :24;                   ///< [31:8] Reserved
  } Field;                              ///< Field
  UINT32  Value;                        ///< Value
} NBIO_ERR_VALID_BIT;

/**
 * @brief A structure for NBIO Error Source
 * @details
 */
typedef union {
  struct {
    UINT8       Nbio:1;                ///< [0] NBIO
    UINT8       NbifMm:1;              ///< [1] NBIFMM
    UINT8       SyshubMm:1;            ///< [2] SYSHUBMM
    UINT8       Dxio:1;                ///< [3] DXIO
    UINT8       :4;                    ///< [7:4] Reserved
  } Field;                             ///< Field
  UINT8  Value;                        ///< Value
} NBIO_ERR_SRC;

/**
 * @brief A structure for NBIO Error Type
 * @details
 */
typedef union {
  struct {
    UINT8       ParityErrCorr:1;       ///< [0] ParityErrCorr (NBIO) or PHY Controller ECC
    UINT8       ParityErrNonFatal:1;   ///< [1] ParityErrNonFatal (NBIO)
    UINT8       ParityErrFatal:1;      ///< [2] ParityErrFatal (NBIO or NBIF)
    UINT8       ParityErrSerr:1;       ///< [3] ParityErrSerr (NBIO)
    UINT8       PoisonError:1;         ///< [4] Poisoned Error (NBIF)
    UINT8       WatchdogTimeOut:1;     ///< [5] Watchdog timeout (NBIF)
    UINT8       SyncfloodFromPin:1;    ///< [6] SyncfloodFromPin
    UINT8       :1;                    ///< [7] Reserved
  } Field;                             ///< Field
  UINT8  Value;                        ///< Value
} NBIO_ERR_TYPE;

/**
 * @brief A structure for NBIO Error Type
 * @details
 */
typedef union {
  struct {
    UINT32       BusId:1;               ///< [0] Bus ID
    UINT32       ErrorType:1;           ///< [1] Error Type
    UINT32       ErrorSource:1;         ///< [2] Error Source
    UINT32       SocketId:1;            ///< [3] Error Source
    UINT32       :28;                   ///< [31:4] Reserved
  } Field;                              ///< Field
  UINT32  Value;                        ///< Value
} SMN_ERROR_VALID_BIT;

/**
 * @brief A structure for Fch SDP Parity Error Valid bit
 * @details
 */
typedef union {
  struct {
    UINT32       FchId:1;               ///< [0] Fch ID
    UINT32       ErrorSource:1;         ///< [1] Error Source
    UINT32       :30;                   ///< [31:2] Reserved
  } Field;                              ///< Field
  UINT32  Value;                        ///< Value
} FCH_SDP_PARITY_ERROR_VALID_BIT;

/**
 * @brief A structure for Fch SDP Parity Error Source
 * @details
 */
typedef union {
  struct {
    UINT8      SdpParityError:1;        ///< [0] SDP Parity Error
    UINT8      InternalThermalTrip:1;   ///< [1] Internal Thermal Trip
    UINT8      ThermalTrip:1;           ///< [2] Thermal Trip (BP_THERMTRIP assertion)
    UINT8      :5;                      ///< [7:3] Reserved
  } Field;                              ///< Field
  UINT8  Value;                         ///< Value
} FCH_SDP_PARITY_ERROR_SOURCE;

/**
 * @brief A structure for SMN Error Source
 * @details
 */
typedef union {
  struct {
    UINT8      SmnParitySmnPspTimeoutSmuParityEcc:1;    ///< [0] SMN Parity
                                                        ///      SMN Timeouts:PSP
                                                        ///      SMU Parity and ECC
    UINT8      :1;                                      ///< [1] Reserved
    UINT8      PspParityEcc:1;                          ///< [2] Psp Parity and ECC
    UINT8      SmnTimeoutSmu:1;                         ///< [3] SMN Timeouts: SMU
    UINT8      SmnLinkPacketCrcWithRetry:1;             ///< [4] SMN Link Packet CRC with Retry
    UINT8      :3;                                      ///< [7:5] Reserved
  } Field;                                              ///< Field
  UINT8  Value;                                         ///< Value
} SMN_ERROR_SOURCE;

/**
 * @brief A structure for SATA ERROR VALID BIT
 * @details
 */
typedef union {
  struct {
    UINT32       BusId:1;               ///< [0] Bus ID
    UINT32       Port:1;                ///< [1] Port
    UINT32       ErrorSource:1;         ///< [2] Error Source
    UINT32       ErrorType:1;           ///< [3] Error Type
    UINT32       :28;                   ///< [31:4] Reserved
  } Field;                              ///< Field
  UINT32  Value;                        ///< Value
} SATA_ERROR_VALID_BIT;

/**
 * @brief A structure for SATA ERROR Source
 * @details
 */
typedef union {
  struct {
    UINT8      Sata:1;                  ///< [0] SATA
    UINT8      :7;                      ///< [7:1] Reserved
  } Field;                              ///< Field
  UINT8  Value;                         ///< Value
} SATA_ERROR_SOURCE;

/**
 * @brief A structure for ALINK error Valid Bit
 * @details
 */
typedef union {
  struct {
    UINT32       ErrorType:1;           ///< [0] Error Type
    UINT32       ErrorSource:1;         ///< [1] Error Source
    UINT32       ErrorStatus:1;         ///< [2] Error Status
    UINT32       :29;                   ///< [31:3] Reserved
  } Field;                              ///< Field
  UINT32  Value;                        ///< Value
} ALINK_ERROR_VALID_BIT;

/**
 * @brief A structure for ALINK error status
 * @details
 */
typedef union {
  struct {
    UINT16       DetectedParityError:1;     ///< [0] DetectedParityError (SMBus or LPC Bridge)
    UINT16       DataParityErrorDetected:1; ///< [1] DataParityErrorDetected (SMBus)
    UINT16       MasterDataParityError:1;   ///< [2] MasterDataParityError (LPC Bridge)
    UINT16       :13;                       ///< [15:3] Reserved
  } Field;                                  ///< Field
  UINT16  Value;                            ///< Value
} ALINK_ERROR_STATUS;

//USB Error Section
/**
 * @brief A structure for USB Error Valid Bit
 * @details
 */
typedef union {
  struct {
    UINT32       ErrorType:1;           ///< [0] Error Type
    UINT32       ErrorSource:1;         ///< [1] Error Source
    UINT32       ErrorStatus:1;         ///< [2] Error Status
    UINT32       :29;                   ///< [31:3] Reserved
  } Field;                              ///< Field
  UINT32  Value;                        ///< Value
} USB_ERROR_VALID_BIT;

/**
 * @brief A structure for USB Error Status
 * @details
 */
typedef union {
  struct {
    UINT16       SEC_Ram0:1;        ///< [0] SEC_Ram0
    UINT16       DED_Ram0:1;        ///< [1] DED_Ram0
    UINT16       FED_Ram0:1;        ///< [2] FED_Ram0
    UINT16       SEC_Ram1:1;        ///< [3] SEC_Ram1
    UINT16       DED_Ram1:1;        ///< [4] DED_Ram1
    UINT16       FED_Ram1:1;        ///< [5] FED_Ram1
    UINT16       SEC_Ram2:1;        ///< [6] SEC_Ram2
    UINT16       DED_Ram2:1;        ///< [7] DED_Ram2
    UINT16       FED_Ram2:1;        ///< [8] FED_Ram2
    UINT16               :7;        ///< [15:9] Reserved
  } Field;                          ///< Field
  UINT16 Value;                     ///< Value
} USB_ERROR_STATUS;

/**
 * @brief A structure for USB Error Type
 * @details
 */
typedef union {
  struct {
    UINT8      UsbParityAndEcc:1;   ///< [0] Universal Serial Bus Parity and ECC
    UINT8      :7;                  ///< [7:1] Reserved
  } Field;                          ///< Field
  UINT8  Value;                     ///< Value
} USB_ERROR_TYPE;

///Slink Error Section
/**
 * @brief A structure for SLINK Error Valid Bit
 * @details
 */
typedef union {
  struct {
    UINT64       SourceIdValid:1;       ///< [0] Error Type
    UINT64       CcixPortIdValid:1;     ///< [1] Error Source
    UINT64       PerLogValid:1;         ///< [2] Error Status
    UINT64       :61;                   ///< [63:3] Reserved
  } Field;                              ///< Field
  UINT64  Value;                        ///< Value
} SLINK_ERROR_VALID_BIT;

#pragma pack (push, 1)
///
/// BSC to AP MSR sync up
typedef struct {
  UINT32 RegisterAddress;   ///< MSR Address
  UINT64 RegisterValue;     ///< BSC's MSR Value
  UINT64 RegisterMask;      ///< MSR mask
  BOOLEAN ForceSetting;     ///< if TRUE, then force to set MSR to value of RegisterValue
} RAS_BSP_AP_MSR_SYNC;

/// BSP/AP MSR Access
typedef struct {
  UINT32 RegisterAddress;   ///< MSR Address
  UINT64 RegisterValue;     ///< BSC's MSR Value
  UINT64 RegisterMask;      ///< MSR mask
} RAS_MSR_ACCESS;

/// PMIC Error Section Valid bit fields
typedef union {
  struct {
    UINT32       ErrorLogValid:1;              ///< [0] Error Log Valid
    UINT32       RealtimeStatusValid:1;        ///< [1] Real-time Status Valid
    UINT32       PeriodicStatusValid:1;        ///< [2] Periodic Status Valid
    UINT32       DimmAddressValid:1;           ///< [3] DIMM Address Valid
    UINT32       DimmStatusValid:1;            ///< [4] DIMM Status Valid
    UINT32       :27;                          ///< [31:5] Reserved
  } Field;                                     ///< Field
  UINT32  Value;                               ///< Value
} PMIC_ERR_VALID_BIT;

///
/// PMIC Error Log
///
typedef union {
  struct {
    UINT32       PMIC_REG_04:8;                ///< [7:0] PMIC Register 0x04[7:0]
    UINT32       PMIC_REG_05:8;                ///< [15:8] PMIC Register 0x05[7:0]
    UINT32       PMIC_REG_06:8;                ///< [23:16] PMIC Register 0x06[7:0]
    UINT32       :8;                           ///< [31:24] Reserved (RAZ)
  } Field;                                     ///< Field
  UINT32  Value;                               ///< Value
} PMIC_ERR_LOG;

///
/// PMIC Real-time Status
///
typedef union {
  struct {
    UINT64       PMIC_REG_08:8;                ///< [7:0] PMIC Register 0x08[7:0]
    UINT64       PMIC_REG_09:8;                ///< [15:8] PMIC Register 0x09[7:0]
    UINT64       PMIC_REG_0A:8;                ///< [23:16] PMIC Register 0x0A[7:0]
    UINT64       PMIC_REG_0B:8;                ///< [31:24] PMIC Register 0x0B[7:0]
    UINT64       PMIC_REG_33:3;                ///< [34:32] PMIC Register 0x33[4:2]
    UINT64                  :29;               ///< [63:35] Reserved (RAZ)
  } Field;                                     ///< Field
  UINT64  Value;                               ///< Value
} PMIC_REAL_TIME_STATUS;

///
/// PMIC Periodic Status
///
typedef union {
  struct {
    UINT64       PMIC_REG_0C:8;                ///< [7:0] PMIC Register 0x0C[7:0]
    UINT64       PMIC_REG_0D:8;                ///< [15:8] PMIC Register 0x0D[7:0]
    UINT64       PMIC_REG_0E:8;                ///< [23:16] PMIC Register 0x0E[7:0]
    UINT64       PMIC_REG_0F:8;                ///< [31:24] PMIC Register 0x0F[7:0]
    UINT64       PMIC_REG_33:3;                ///< [34:32] PMIC Register 0x33[7:5]
    UINT64                  :29;               ///< [63:35] Reserved (RAZ)
  } Field;                                     ///< Field
  UINT64  Value;                               ///< Value
} PMIC_PERIODIC_STATUS;

///
/// PMIC DIMM Address
///
typedef union {
  struct {
    UINT32       SocketId:8;                   ///< [7:0]   Socket ID (0..1)
    UINT32      ChannelId:8;                   ///< [15:8]  Channel ID (0..11)
    UINT32         DimmId:8;                   ///< [23:16] DIMM ID (0..1)
    UINT32               :8;                   ///< [31:24] Reserved (RAZ)
  } Field;                                     ///< Field
  UINT32  Value;                               ///< Value
} PMIC_DIMM_ADDRESS;

typedef struct {
  PMIC_ERR_VALID_BIT      ValidBits;           ///< Validation Bits
  PMIC_ERR_LOG            ErrLog;              ///< Error Log
  PMIC_REAL_TIME_STATUS   RealtimeStatus;      ///< Real-time Status
  PMIC_PERIODIC_STATUS    PeriodicStatus;      ///< Periodic Status
  PMIC_DIMM_ADDRESS       DimmAddress;         ///< DIMM Address
  UINT8                   DimmStatus;          ///< DIMM Status (0:Channel Disabled/ 1:Channel Enabled)
  UINT8                   Reserved0;           ///< Reserved (MBZ)
  UINT8                   Reserved1;           ///< Reserved (MBZ)
  UINT8                   Reserved2;           ///< Reserved (MBZ)
} AMD_PMIC_ERROR_RECORD;

///
/// Generic Error Entry Structure
///
/**
 * @brief A structure for Generic ERROR VALID BIT
 * @details
 */
typedef union {
  struct {
    UINT32       ErrSrcValid:1;         ///< [0] Error Source Valid
    UINT32       SocketId:1;            ///< [1] Socket ID Valid
    UINT32       CcdId:1;               ///< [2] CCD ID Valid
    UINT32       AidId:1;               ///< [3] AID ID Valid
    UINT32       LinkId:1;              ///< [4] Link ID Valid
    UINT32       UmcChannelNo:1;        ///< [5] UMC Channel Number
    UINT32       HbmStackNo:1;          ///< [6] HBM Stack Number
    UINT32       FWId:1;                ///< [7] Firmware ID
    UINT32       :24;                   ///< [31:8] Reserved
  } Field;                              ///< Field
  UINT32  Value;                        ///< Value
} GENERIC_ERR_VALID_BIT;

/**
 * @brief A structure for Generic Error Data Error Source
 * @details
 */
typedef union {
  struct {
    UINT8       HBMTrainErr:1;           ///< [0] HBM training Error
    UINT8       XGMILinkErr:1;           ///< [1] xGMI link Error
    UINT8       WAFLLinkErr:1;           ///< [2] WAFL link Error
    UINT8       USRDPLinkErr:1;          ///< [3] USR-DP link Error
    UINT8       USRCPLinkErr:1;          ///< [4] USR-CP link Error
    UINT8       FWLoadFail:1;            ///< [5] Firmware load Failed
    UINT8       MemTestFail:1;           ///< [6] Memory test Failed
    UINT8       MemBistFail:1;           ///< [7] Memory BIST Training Error
  } Field;                               ///< Field
  UINT8  Value;                          ///< Value
} GENERIC_ERR_SRC;

typedef struct {
  GENERIC_ERR_VALID_BIT   ValidationBits;   ///< Generic Error Valid bit
  GENERIC_ERR_SRC         ErrorSource;      ///< Error Source
  UINT8                   SocketId;         ///< Socket ID
  UINT8                   CCDId;            ///< CCD ID
  UINT8                   AIDId;            ///< AID ID
  UINT8                   LinkId;           ///< Link ID
  UINT8                   Channel;          ///< UMC channel number
  UINT8                   HBMStack;         ///< HBM stack number
  UINT8                   FWId;             ///< Firmware ID
} AMD_GENERIC_ERROR_RECORD;

///
/// NBIO Error Record structure
///
typedef struct {
    NBIO_ERR_VALID_BIT      ValidationBits;     ///< NBIO Error Valid bit
    UINT8                   BusId;              ///< Bus Id
    NBIO_ERR_SRC            ErrorSource;        ///< Error Source
    NBIO_ERR_TYPE           ErrorType;          ///< Error Type
    UINT8                   GroupType;          ///< Group Type
    UINT16                  GroupId;            ///< Group Id
    UINT8                   NbifId;             ///< Nbif Id
    UINT8                   LeafId;             ///< Leaf Id
    UINT32                  ParityErrSts;       ///< Parity Error status
} AMD_NBIO_ERROR_RECORD;

///
/// SMN Error Record structure
///
typedef struct {
    SMN_ERROR_VALID_BIT     ValidationBits;      ///< SMN Error Valid bit
    UINT8                   BusId;               ///< Bus Id
    UINT8                   Category;            ///< Category
    SMN_ERROR_SOURCE        ErrorSource;         ///< Error Source
    UINT8                   SocketId;            ///< Socket Id
} AMD_SMN_ERROR_RECORD;
///
/// FCH SDP Parity Error Record structure
///
typedef struct {
    FCH_SDP_PARITY_ERROR_VALID_BIT     ValidationBits;      ///< FCH SDP Parity Error Valid bit
    UINT8                              FchId;               ///< FCH Id
    FCH_SDP_PARITY_ERROR_SOURCE        ErrorSource;         ///< Error Source
} AMD_FCH_SDP_PARITY_ERROR_RECORD;

///
/// SATA Error Record structure (AMD vendor specific error section structure)
///
/**
 * @brief The Error Type of the SATA error source (Generic Error Status Block - Serial ATA (SATA) Parity and ECC).
 *
 * @details When a SATA error occurs, it is used to indicate the SATA error type.
 *          The SATA Error Record structure has been deprecated from RS (Genoa)
 *          because these errors have been reported through MCA instead.
 */
///SATA Error Type definition
#define SATA_D2H_FIFO_PARITY_ERROR          (0)  ///< SATA error type for D2H_FIFO_PARITY_ERROR
#define SATA_H2D_FIFO_PARITY_ERROR          (1)  ///< SATA error type for H2D_FIFO_PARITY_ERROR
#define SATA_CONTEXT_MEMORY_PARITY_ERROR    (2)  ///< SATA error type for CONTEXT_MEMORY_PARITY_ERROR

///
/// SATA Error Record structure
///
typedef struct {
    SATA_ERROR_VALID_BIT    ValidationBits;       ///< SATA Error Valid bit
    UINT8                   BusId;                ///< Bus Id
    UINT8                   Port;                 ///< SATA Port number
    SATA_ERROR_SOURCE       ErrorSource;          ///< Error Source
    UINT8                   ErrorType;            ///< Error Type
} AMD_SATA_ERROR_RECORD;

///
/// FCH A-Link Error Record structure (AMD vendor specific error section structure)
///
/**
 * @brief The Error Type of the FCH A-Link Error Record (Generic Error Status Block - Fusion Controller Hub (FCH)).
 *
 * @details There is only one instance of the Error Type - 0: Fusion Controller Hub A-Link Parity,
 *          all other values are reserved.
 *          The FCH A-Link Error Record structure has been deprecated from RS (Genoa)
 *          because these errors have been reported through MCA instead.
 */
///A-Link Error Type definition
#define FCH_ALINK_PARITY_ERROR              (0)
/**
 * @brief The Error source of the FCH A-Link Error Record (Generic Error Status Block - Fusion Controller Hub (FCH)).
 *
 * @details When a FCH A-Link Error occurs, it is used to indicate the source of the FCH A-Link error.
 *          The FCH A-Link Error Record structure has been deprecated from RS (Genoa)
 *          because these errors have been reported through MCA instead.
 */
///A-Link Error Source definition
#define ALINK_SMBUS_PARITY_ERROR            (0)  ///< FCH A-Link Error for SMBUS_PARITY_ERROR
#define ALINK_LPCBRIDGE_PARITY_ERROR        (1)  ///< FCH A-Link Error for LPCBRIDGE_PARITY_ERROR

///
/// ALINK Error Record structure
///
typedef struct {
    ALINK_ERROR_VALID_BIT   ValidationBits;       ///< ALINK Error Balid Bit
    UINT8                   ErrorType;            ///< Error Type
    UINT8                   ErrorSource;          ///< Error Source
    ALINK_ERROR_STATUS      ErrorStatus;          ///< Error Status
} AMD_ALINK_ERROR_RECORD;

///
/// USB Error Record structure (AMD vendor specific error section structure)
///
/**
 * @brief The Error source of the USB Error Record structure (Generic Error Status Block - Universal Serial Bus (USB)).
 *
 * @details When a USB error occurs, it is used to indicate the source of the USB error.
 *          The USB Error Record structure has been deprecated from RS (Genoa)
 *          because these errors have been reported through MCA instead.
 */
///USB Error Source definition
#define USB_USBCONTAINER0_ERROR             (0)  ///< USB error for CONTAINER0_ERROR
#define USB_USBCONTAINER1_ERROR             (1)  ///< USB error for CONTAINER1_ERROR

///
/// USB Error Record structure
///
typedef struct {
    USB_ERROR_VALID_BIT     ValidationBits;      ///< USB Error Valid bit
    USB_ERROR_TYPE          ErrorType;           ///< Error Type
    UINT8                   ErrorSource;         ///< Error Source
    USB_ERROR_STATUS        ErrorStatus;         ///< Error Status
} AMD_USB_ERROR_RECORD;

///
/// SLink Error Record structure
///
typedef struct {
    UINT32                  Length;               ///< Total length of SLink Error Record structure
    SLINK_ERROR_VALID_BIT   ValidationBits;       ///< SLINK Error Valid bit
    UINT8                   CcixSourceId;         ///< Ccix Source Id
    UINT8                   CcixPortId;           ///< Ccix Port Id
    UINT8                   Reserved[2];          ///< Reserved
    UINT32                  CcixPerLog[];         ///< Place holder for the Ccix Per Log structure
} AMD_SLINK_ERROR_RECORD;

///
/// Platform DIMM FRU text structure
///
typedef struct {
  UINT32                McaFruTextDW0;            ///< Value for UMC::McaFruTextDW0
  UINT32                McaFruTextDW1;            ///< Value for UMC::McaFruTextDW1
  UINT32                McaFruTextDW2;            ///< Value for UMC::McaFruTextDW2
  UINT32                McaFruTextDW3;            ///< Value for UMC::McaFruTextDW3
} AMD_FRUTEXT_STR;

/**
 * @brief The FRUTEXT entry used to specify the DIMM McaFruText values
 * @details
 */
typedef struct {
  UINT16                Socket;                    ///< Socket ID
  UINT16                Die;                       ///< Die ID
  UINT16                Channel;                   ///< Channel ID
  UINT16                Module;                    ///< Module 0 (CS=0,1):Module 1 (CS=2,3)
  AMD_FRUTEXT_STR       AmdFrutextStr;             ///< DIMM Fru text string
} AMD_FRUTEXT_ENTRY;

/**
 * @brief The FRUTEXT table point to the Oem Dimm Frutext entries for update.
 * @details
 */
typedef struct {
  UINT32                     TableSize;            ///< Table Entries total size, not include header size
  UINT32                     TableEntryNum;        ///< Table Entries number
  AMD_FRUTEXT_ENTRY          *FrutextEntry;        ///< Oem Dimm Frutext entry.
} AMD_DIMM_FRUTEXT_TABLE;

/**
 * @brief OEM MCA THRESHOLD entry used to specify the Oem Threshold values
 * @details
 */
typedef struct {
  UINT16                HardwareID;                                   ///< MCA HW ID
  UINT16                McaType;                                      ///< MCA TYPE - 0xFFFF means will check only HW ID for comparison. If not, will check both HW ID and MCA Type
  UINT16                Misc0ThresholdCount;                     ///< (MCA::XXX::MCA_MISC0_XXX)
  UINT16                Misc1ThresholdCount;                    ///< (MCA::XXX::MCA_MISC1_XXX)
} AMD_MCA_THRESHOLD_ENTRY;

/**
 * @brief The OEM MCA THRESHOLD table point to the Oem MCA THRESHOLD (threshold values, banks) entries for update.
 * @details
 */
typedef struct {
  UINT32                        TableSize;            ///< Table Entries total size, not include header size
  UINT32                        TableEntryNum;        ///< Table Entries number
  AMD_MCA_THRESHOLD_ENTRY       *McaThresholdTableEntry; ///< Oem Mca Threshold entry.
} AMD_MCA_THRESHOLD_TABLE;

#pragma pack (pop)

#endif  // _AMD_RAS_H_



