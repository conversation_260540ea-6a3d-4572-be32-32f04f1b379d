//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************

/**
  @file  PeiSerialTextOut.c
  This file contains the PPI functions to use serial post.
*/

#include <Uefi.h>
#include <Library/DebugLib.h>
#include <Library/PeiServicesLib.h>
#include <Library/MemoryAllocationLib.h>
#include <Library/SerialPortLib.h>
#include <Library/PrintLib.h>
#include <Library/PcdLib.h>
#include <Library/AmiSerialTextOutLib.h>
#include <Ppi/AmiSimpleTextOutPpi.h>


typedef struct {
    AMI_SIMPLE_TEXT_OUTPUT_PPI          AmiPeiSimpleTextOut;
    EFI_SIMPLE_TEXT_OUTPUT_MODE         Mode;
    UINT32                              MaxRows;    ///< Max number of rows in current mode
    UINT32                              MaxColumns; ///< Max number of columns in current mode
} AMI_SERIAL_TEXT_OUT_PRIVATE_DATA;


EFI_STATUS
EFIAPI
ReinstallSerialTextOutPpiAfterMemoryDiscovered (
        IN EFI_PEI_SERVICES           **PeiServices,
        IN EFI_PEI_NOTIFY_DESCRIPTOR  *NotifyDescriptor,
        IN VOID                       *Ppi
);

EFI_PEI_NOTIFY_DESCRIPTOR gSerialTextOutMemoryInstalledNotifyList[] = {
                                    {
                                     (EFI_PEI_PPI_DESCRIPTOR_NOTIFY_CALLBACK | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST),
                                     &gEfiPeiMemoryDiscoveredPpiGuid,
                                     ReinstallSerialTextOutPpiAfterMemoryDiscovered
                                    }
                                     };


/**
  Reset the text output device hardware and optionally run diagnostics

  @param  This                 The PPI instance pointer.
  @param  ExtendedVerification Driver may perform more exhaustive verification
                               operation of the device during reset.

  @retval EFI_SUCCESS          The text output device was reset.
  @retval EFI_DEVICE_ERROR     The text output device is not functioning correctly and
                               could not be reset.

**/
EFI_STATUS  
PeiSimpleTextOutReset (
    IN AMI_SIMPLE_TEXT_OUTPUT_PPI   *This,
    IN BOOLEAN                      ExtendedVerification
)
{
    // Reset should:
    // - Clear the screen 
    // - set the cursor back to (0,0)
    
    This->SetAttribute(This, EFI_BACKGROUND_BLACK | EFI_WHITE);
    
    This->ClearScreen(This);
    
    This->SetCursorPosition(This, 0, 0);
    
    This->EnableCursor(This, PcdGetBool(PcdDefaultCursorState));
    
    return  EFI_SUCCESS;
}

/**
    Clears the output device(s) display to the currently selected background 
    color.
      
    @param   This   The PPI instance pointer.

    @retval  EFI_SUCCESS      The operation completed successfully.
    @retval  EFI_DEVICE_ERROR The device had an error and could not complete the request.
    @retval  EFI_UNSUPPORTED  The output device is not in a valid text mode.

**/
EFI_STATUS
EFIAPI
PeiSimpleTextOutClearScreen(
    IN AMI_SIMPLE_TEXT_OUTPUT_PPI       *This
)
{
    EFI_STATUS                  Status; 

    Status = TerminalClearScreen();
    if (EFI_ERROR(Status)) {
        return Status;
    }
    
    //Set cursor position X=0, Y=0
    Status = This->SetCursorPosition (This, 0, 0);
    
    if (EFI_ERROR(Status)) { //on first invocation this failed because MaxRows = MaxCols = 0
        This->Mode->CursorColumn = 0;
        This->Mode->CursorRow = 0;
    }

    return Status; 
}

/**
    Returns information for an available text mode that the output device(s)
    supports.

    @param  This       The PPI instance pointer.
    @param  ModeNumber The mode number to return information on.
    @param  Columns    Returns the geometry of the text output device for the
                       requested ModeNumber.
    @param  Rows       Returns the geometry of the text output device for the
                       requested ModeNumber.
                                          
    @retval EFI_SUCCESS      The requested mode information was returned.
    @retval EFI_DEVICE_ERROR The device had an error and could not complete the request.
    @retval EFI_UNSUPPORTED  The mode number was not valid.

**/
EFI_STATUS
EFIAPI
PeiSimpleTextOutQueryMode (
    IN AMI_SIMPLE_TEXT_OUTPUT_PPI   *This,
    IN UINTN                        ModeNumber,
    IN OUT UINTN                    *Columns,
    IN OUT UINTN                    *Rows
)
{
    
    if (ModeNumber >= (UINTN)(This->Mode->MaxMode)) {
        return EFI_UNSUPPORTED;
    }
    
    switch (ModeNumber) {
        case 0:
            //Mode 0 is the only valid mode
            *Columns = 80;
            *Rows    = 25;
            break;
        default:
            *Columns = 0;
            *Rows    = 0;
            return EFI_UNSUPPORTED;
    }

    return EFI_SUCCESS;
}

/**
  Sets the output device(s) to a specified mode.

  @param  This       The PPI instance pointer.
  @param  ModeNumber The mode number to set.

  @retval EFI_SUCCESS      The requested text mode was set.
  @retval EFI_DEVICE_ERROR The device had an error and could not complete the request.
  @retval EFI_UNSUPPORTED  The mode number was not valid.

**/
EFI_STATUS
EFIAPI
PeiSimpleTextOutSetMode (
    IN AMI_SIMPLE_TEXT_OUTPUT_PPI   *This,
    IN UINTN                        ModeNumber
)
{
    
    if (ModeNumber >= (UINTN)(This->Mode->MaxMode)) {
        return EFI_UNSUPPORTED;
    }

    // if the mode is a valid mode, return EFI_SUCCESS as we are
    // supporting only Mode 0.
    
    return EFI_SUCCESS;
}

/**
    Sets the background and foreground colors for the OutputString () and
    ClearScreen () functions.

    @param  This        The PPI instance pointer.
    @param  Attribute   Attribute to set.
  

    @retval EFI_SUCCESS       The attribute was set.
    @retval EFI_DEVICE_ERROR  The device had an error and could not complete the request.
    @retval EFI_UNSUPPORTED   The attribute requested is not defined.

**/
EFI_STATUS
EFIAPI
PeiSimpleTextOutSetAttribute (
    IN AMI_SIMPLE_TEXT_OUTPUT_PPI       *This, 
    IN UINTN                            Attribute
)
{
    //    Bits 0..3 are the foreground color, and
    //    bits 4..6 are the background color. All other bits are undefined
    //    and must be zero.
    This->Mode->Attribute = (INT32)Attribute;
    
    // Call the SerialTextOut LIB to set the attribute
    return TerminalSetAttribute((UINT8)Attribute);
}


/**
  Verifies that all characters in a string can be output to the 
  target device.

  @param  This   The PPI instance pointer.
  @param  String The NULL-terminated string to be examined for the output
                 device(s).

  @retval EFI_SUCCESS      The device(s) are capable of rendering the output string.
  @retval EFI_UNSUPPORTED  Some of the characters in the string cannot be
                           rendered by one or more of the output devices mapped
                           by the EFI handle.

**/
EFI_STATUS  
PeiSimpleTextOutTestString (
    IN AMI_SIMPLE_TEXT_OUTPUT_PPI       *This,
    IN CHAR16                           *String
)
{
    return EFI_SUCCESS;
}

/**
    Write a string to the output device.

    @param  This   The PPI instance pointer.
    @param  String The NULL-terminated string to be displayed on the output
                   device(s). All output devices must also support the Unicode
                   drawing character codes defined in this file.

    @retval EFI_SUCCESS             The string was output to the device.
    @retval EFI_DEVICE_ERROR        The device reported an error while attempting to output
                                    the text.
    @retval EFI_UNSUPPORTED         The output device's mode is not currently in a
                                    defined text mode.
**/
EFI_STATUS
EFIAPI
PeiSimpleTextOutPutString (
    IN AMI_SIMPLE_TEXT_OUTPUT_PPI   *This,
    IN CHAR16                       *String
)    
{
    CHAR8   Text[0x400];   //1KB

    UnicodeStrToAsciiStrS (String, Text, sizeof(Text));

    return TerminalOutputStringHelper((CONST CHAR8 *)Text);
}

/**
    Sets the current coordinates of the cursor position

    @param  This        The PPI instance pointer.
    @param  Column      The position to set the cursor to. Must be greater than or
                        equal to zero and less than the number of columns and rows
                        by QueryMode ().
    @param  Row         The position to set the cursor to. Must be greater than or
                        equal to zero and less than the number of columns and rows
                        by QueryMode ().

    @retval EFI_SUCCESS      The operation completed successfully.
    @retval EFI_DEVICE_ERROR The device had an error and could not complete the request.
    @retval EFI_UNSUPPORTED  The output device is not in a valid text mode, or the
                             cursor position is invalid for the current mode.

**/
EFI_STATUS
EFIAPI
PeiSimpleTextOutSetCursorPosition (
    IN AMI_SIMPLE_TEXT_OUTPUT_PPI   *This, 
    IN UINTN                        Column,
    IN UINTN                        Row
)
{
    EFI_STATUS              Status; 

    if (Column >= MAX_COLUMNS || Row >= MAX_ROWS) {
        return EFI_UNSUPPORTED;
    }

    // Call the SerialTextOut LIB to set the cursor position 
    Status = TerminalSetCursorPosition ((UINT8)Column, (UINT8)Row);
    
    This->Mode->CursorColumn = (INT32)Column;
    This->Mode->CursorRow    = (INT32)Row;

    return Status; 
}

/**
    Makes the cursor visible or invisible

    @param  This    The PPI instance pointer.
    @param  Enable  If TRUE, the cursor is set to be visible. If FALSE, the cursor is
                    set to be invisible.

    @retval EFI_SUCCESS      The operation completed successfully.
    @retval EFI_DEVICE_ERROR The device had an error and could not complete the
                             request, or the device does not support changing
                             the cursor mode.
    @retval EFI_UNSUPPORTED  The output device is not in a valid text mode.

**/
EFI_STATUS 
EFIAPI
PeiSimpleTextOutEnableCursor(
   IN AMI_SIMPLE_TEXT_OUTPUT_PPI   *This,
   IN BOOLEAN                      Enable
)
{
    This->Mode->CursorVisible = Enable;
    return EFI_SUCCESS;
}

/**
    After memory discovered, Re-install the PEI SerialTestOut Ppi with
    updated SerialtextOut.

    @param[in] PeiServices          Describes the list of possible PEI
                                    Services.

    @return EFI_STATUS  Status

**/
EFI_STATUS
EFIAPI
ReinstallSerialTextOutPpiAfterMemoryDiscovered (
        IN EFI_PEI_SERVICES           **PeiServices,
        IN EFI_PEI_NOTIFY_DESCRIPTOR  *NotifyDescriptor,
        IN VOID                       *Ppi
)
{
    EFI_STATUS                              Status;
    EFI_PEI_PPI_DESCRIPTOR                  *SerialSimpleTextOutDescriptor;
    AMI_SIMPLE_TEXT_OUTPUT_PPI              *SerialSimpleTextOutPpi;
    AMI_SERIAL_TEXT_OUT_PRIVATE_DATA        *AmiSerialTextOutPrivate;

    // Locate PEI Serial Ppi.
    DEBUG ((DEBUG_INFO, "Serial TextOut Ppi Instance : %x \n", PcdGet16 (AmiPcdSerialSimpleTextOutPpiInstance)));
    
    Status = (*PeiServices)->LocatePpi (
                                (CONST EFI_PEI_SERVICES **)PeiServices,
                                &gAmiSimpleTextOutPpiGuid,
                                PcdGet16 (AmiPcdSerialSimpleTextOutPpiInstance),
                                &SerialSimpleTextOutDescriptor,
                                (VOID **)&SerialSimpleTextOutPpi);

    if (EFI_ERROR (Status)) {
        DEBUG ((DEBUG_ERROR, "LocatePpi for gAmiSimpleTextOutPpiGuid Status: %r \n", Status));
        return Status;
    }

    AmiSerialTextOutPrivate = (AMI_SERIAL_TEXT_OUT_PRIVATE_DATA *)SerialSimpleTextOutPpi;
    
    SerialSimpleTextOutPpi->Reset             = PeiSimpleTextOutReset;
    SerialSimpleTextOutPpi->OutputString      = PeiSimpleTextOutPutString;
    SerialSimpleTextOutPpi->TestString        = PeiSimpleTextOutTestString;
    SerialSimpleTextOutPpi->QueryMode         = PeiSimpleTextOutQueryMode;
    SerialSimpleTextOutPpi->SetMode           = PeiSimpleTextOutSetMode;
    SerialSimpleTextOutPpi->SetAttribute      = PeiSimpleTextOutSetAttribute;
    SerialSimpleTextOutPpi->ClearScreen       = PeiSimpleTextOutClearScreen;
    SerialSimpleTextOutPpi->SetCursorPosition = PeiSimpleTextOutSetCursorPosition;
    SerialSimpleTextOutPpi->EnableCursor      = PeiSimpleTextOutEnableCursor;
    SerialSimpleTextOutPpi->Mode              = &AmiSerialTextOutPrivate->Mode;
    SerialSimpleTextOutPpi->ConsoleType       = PeiSimpleTextOutConsoleTypeSerial;
    
    // Re-install the VIDEO TEXTOUT PPI 
    Status = (*PeiServices)->ReInstallPpi (
                                (CONST EFI_PEI_SERVICES **)PeiServices,
                                SerialSimpleTextOutDescriptor,
                                SerialSimpleTextOutDescriptor );
    
    DEBUG ((DEBUG_INFO, "ReInstallPpi for SerialTextOutDescriptor Status: %r \n", Status));
    return Status;
}

/**
    This function is the entry point for this PEI.
    This installs the Serial TextOut PPI

    @param FileHandle Pointer to image file handle.
    @param PeiServices Pointer to the PEI Core data Structure

    @return EFI_STATUS
    @retval EFI_SUCCESS Successful driver initialization

**/
EFI_STATUS
EFIAPI
PeiSerialTextOutEntry (
  IN       EFI_PEI_FILE_HANDLE     FileHandle,
  IN CONST EFI_PEI_SERVICES        **PeiServices
)
{
    EFI_STATUS                          Status;
    EFI_PEI_PPI_DESCRIPTOR              *SerialSimpleTextOutDescriptor;
    AMI_SERIAL_TEXT_OUT_PRIVATE_DATA    *AmiSerialTextOutPrivate = NULL;
    AMI_SIMPLE_TEXT_OUTPUT_PPI          *SimpleTextOutPpi = NULL;
    UINTN                               Instance = 0;
    
    Status = (*PeiServices)->NotifyPpi(
                                   PeiServices,
                                   &gSerialTextOutMemoryInstalledNotifyList[0] );

    if (EFI_ERROR(Status)) { 
        DEBUG((DEBUG_ERROR,"%a(): Notify PPI for MemoryDiscovered PPI Status:%r", __FUNCTION__, Status));
        return Status;
    }
    
    Status = (*PeiServices)->AllocatePool(
                                     PeiServices,
                                     sizeof(EFI_PEI_PPI_DESCRIPTOR),
                                     (VOID**)&SerialSimpleTextOutDescriptor);
    if (EFI_ERROR(Status)) { 
        DEBUG((DEBUG_ERROR,"%a(): AllocatePool SerialSimpleTextOutDescriptor Status:%r", __FUNCTION__, Status));
        return Status;
    }

    Status = (*PeiServices)->AllocatePool(
                                     PeiServices,
                                     sizeof(AMI_SERIAL_TEXT_OUT_PRIVATE_DATA),
                                     (VOID**)&AmiSerialTextOutPrivate);
    if (EFI_ERROR(Status)){ 
        FreePool(SerialSimpleTextOutDescriptor);
        DEBUG((DEBUG_ERROR,"%a(): AllocatePool SerialSimpleTextOutPpi Status:%r", __FUNCTION__, Status));
        return Status;
    }
    
    // Initialize serial port.
    SerialPortInitialize();
    
    SerialSimpleTextOutDescriptor->Flags = EFI_PEI_PPI_DESCRIPTOR_PPI | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST;
    SerialSimpleTextOutDescriptor->Guid  = &gAmiSimpleTextOutPpiGuid;
    SerialSimpleTextOutDescriptor->Ppi   = &AmiSerialTextOutPrivate->AmiPeiSimpleTextOut;

    AmiSerialTextOutPrivate->AmiPeiSimpleTextOut.Reset             = PeiSimpleTextOutReset;
    AmiSerialTextOutPrivate->AmiPeiSimpleTextOut.OutputString      = PeiSimpleTextOutPutString;
    AmiSerialTextOutPrivate->AmiPeiSimpleTextOut.TestString        = PeiSimpleTextOutTestString;
    AmiSerialTextOutPrivate->AmiPeiSimpleTextOut.QueryMode         = PeiSimpleTextOutQueryMode;
    AmiSerialTextOutPrivate->AmiPeiSimpleTextOut.SetMode           = PeiSimpleTextOutSetMode;
    AmiSerialTextOutPrivate->AmiPeiSimpleTextOut.SetAttribute      = PeiSimpleTextOutSetAttribute;
    AmiSerialTextOutPrivate->AmiPeiSimpleTextOut.ClearScreen       = PeiSimpleTextOutClearScreen;
    AmiSerialTextOutPrivate->AmiPeiSimpleTextOut.SetCursorPosition = PeiSimpleTextOutSetCursorPosition;
    AmiSerialTextOutPrivate->AmiPeiSimpleTextOut.EnableCursor      = PeiSimpleTextOutEnableCursor;
    AmiSerialTextOutPrivate->AmiPeiSimpleTextOut.ConsoleType       = PeiSimpleTextOutConsoleTypeSerial;
    AmiSerialTextOutPrivate->AmiPeiSimpleTextOut.Mode              = &AmiSerialTextOutPrivate->Mode;
    AmiSerialTextOutPrivate->AmiPeiSimpleTextOut.Mode->MaxMode     = 1;
    AmiSerialTextOutPrivate->AmiPeiSimpleTextOut.Mode->Mode        = 0;
    AmiSerialTextOutPrivate->AmiPeiSimpleTextOut.Mode->CursorVisible = FALSE;
    AmiSerialTextOutPrivate->MaxColumns = MAX_COLUMNS;
    AmiSerialTextOutPrivate->MaxRows    = MAX_ROWS;
    
    // Clear Screen and Install PPI.
    PeiSimpleTextOutSetAttribute (
                            &AmiSerialTextOutPrivate->AmiPeiSimpleTextOut, 
                            EFI_BLACK);
    
    PeiSimpleTextOutClearScreen  (&AmiSerialTextOutPrivate->AmiPeiSimpleTextOut);
    
    PeiSimpleTextOutSetAttribute (
                            &AmiSerialTextOutPrivate->AmiPeiSimpleTextOut, 
                            EFI_BACKGROUND_BLACK | EFI_WHITE);
    
    PeiSimpleTextOutEnableCursor(
                            &AmiSerialTextOutPrivate->AmiPeiSimpleTextOut, 
                            PcdGetBool(PcdDefaultCursorState));
    
    Status = (*PeiServices)->InstallPpi(
                                    PeiServices,
                                    SerialSimpleTextOutDescriptor);
    if (EFI_ERROR(Status)){ 
        FreePool(SerialSimpleTextOutDescriptor);
        FreePool(AmiSerialTextOutPrivate);
        DEBUG((DEBUG_ERROR,"%a(): Failed to Install SerialSimpleTextOutDescriptor!!! Status:%r", __FUNCTION__, Status));
        return Status;
    }
    
    do {  
        // Locate the Serial TextOut PPI Index.
        Status = (*PeiServices)->LocatePpi(
                                    (const EFI_PEI_SERVICES**)PeiServices,
                                    &gAmiSimpleTextOutPpiGuid,
                                    Instance,
                                    NULL,
                                    (VOID**)&SimpleTextOutPpi); 

        if (!EFI_ERROR(Status) && (&AmiSerialTextOutPrivate->AmiPeiSimpleTextOut == SimpleTextOutPpi)) {
            PcdSet16S (AmiPcdSerialSimpleTextOutPpiInstance, (UINT16)Instance);
            break;
        }
        
        Instance++;
    } while(!EFI_ERROR(Status));
    
    return Status;
}
