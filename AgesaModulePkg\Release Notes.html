<!DOCTYPE html>
<!-- saved from url=(0185)file:///C:/Users/<USER>/AppData/Local/Microsoft/Windows/INetCache/Content.Outlook/T5RVY5JM/Updated_PI_Release_Notes%20-%20Internal%20Draft%20only%20not%20for%20external%20sharing.html -->
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title> AMD Turin PI Release Notes</title>
    <style>
body{font-family:arial; min-width: 860px;}
h2{font-size:1.4em;}
h3{text-indent:60px; font-weight:normal; font-size: 1.1em;
    background: linear-gradient(90deg, rgba(2,0,36,0) 0%, rgba(170,230,254,0.5346872025177258) 47%, rgba(34,70,86,1) 100%);
}

#header{
    background-repeat: repeat-y;
    width:100%;
    height:200px;
    background: linear-gradient(90deg, rgba(2,0,36,0) 0%, rgba(170,230,254,0.5346872025177258) 47%, rgba(34,70,86,1) 100%);
    margin-bottom:40px;
}
#header #header-title{ font-size:2em;}

.subheader{
    width:100%;
    height:20px;
    background: linear-gradient(90deg, rgba(2,0,36,0) 0%, rgba(170,230,254,0.5346872025177258) 47%, rgba(34,70,86,1) 100%);
}

#toc-box{padding-bottom: 20px;}
#toc-title{
    font-size:1.4em;
    font-weight: bold;
}
#toc-box ul li{}
.toc-link{text-decoration:none;color:inherit;}

#spec-box, #package-contents-box, #tools-box, #known-issues-box, #Changes-box{}
#abl-box, #cbs-box, #cpm-box, #psp-box, #smu-box, #dxio-box, #agesa-box{}

.table{display: table;  width: 100%;}

.row{
    display: table-row;
    border-left: 1px solid #d0d0d0;
    border-bottom: 1px solid #d0d0d0;
    border-right: 1px solid #d0d0d0;
    overflow:hidden;
    text-overflow: ellipsis;
    display:flex;
}
.row:after {content: ""; display: table; clear: both;}
.row.title{ border: 1px solid #d0d0d0; font-weight:bold;}

.column{
    float:left; /* EF'n explorer*/
    display: table-cell;
    overflow:hidden;
    text-overflow: ellipsis;
    border-right: 1px solid #d0d0d0;
    border-left: 1px solid #d0d0d0;
    padding-left: 10px;
    height:100%;

}

.column.desc{ border-right: none; }
.column.ticket{min-width:110px;}
.column.cont_title{width:200px;}
.column.version{width:60px; min-width:60px;}
.column.pid{width:80px; min-width:80px;}
.column.hash{min-width:80px;}
.column.files{width:600px; min-width:600px;overflow-wrap:break-word}
.column.tname,.column.pkg{
    width:160px;
    height:100%;
}


#footer{
    font-size:.6em; margin-top:40px;
    color:#aaaaaa; background-color:#eeeeee;
}
#footer .copyright{font-size:1.5em;}
#footer p{ }
</style>
  </head>
  <body>
    <div id="header"> <img
src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMEAAACYCAYAAABdw42jAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAJeBSURBVHja7L13nCVXcf79PaH7xrmTZ/NKWkmrnLOEAiBA5GzjAAYbMMnYBBNs/MNkZMBggwEDJohsMNggwIAEklACSaAcVlptDrOTZ27q7hPeP07fOzMbtCtp%0A5fCa1ud8ZjXx3u5Tp6qeeuop4b3nt9dvr//Ll/ztLfjt9X/90gCnnnzKPr/BGEMURVx44YXUajWSJME5RxRFjI2NsWnLZjye6alpoijCe0+zWcc5hxAC5xzee8444wyWL1vG1NQUg3393H///Wx8cAPEkvGJSaQI9jjeMGQOpFhgqSpClzROQGQ0SpVoCYc1bQaGh2g25qBYY0WtxJYdO6ktW8Xsrp3UCpqdO+po4RgYrqGLMUXpGRufgcGVHLly%0AmLvvupnMRKiowMjq5UyM7kT2DzEYezauu48jDj+BoShiNGly9623QjutnHnWmX80Uu0dueuWWz7WarWmoyjCOYc1lnOf/WTu27qRRrPJKWecTtqQ/OTf/o0TTjmRNWuPBAFta1CRBiWJlEI4MNbgnENKyczMDHffcw8777uP5Yev4ojjT+K+u++hpGOWlstsuOtOipUKAmg326w8dNkzT7rg3FN/ddPNP6yr0t1bN2xrDEtJbWQ5Uz7DNydYffxp%0ATM80YGwnkzNzVCtV1FAfw9kME02FKFVQ7Wmy+gyN2ZS41EOlp0Bm2xR7B5hJPDNT02jT4pjjjmbbZAPTqrN06RLW37+OSMcMVnshUsSliGRqhl0zk5hmE+ccTojHfDO7fEOPxOF03z3GSdOUkZERjjjiCL75zW8uNoLfXrtfAmczWhMTjPWMUurrXz2ybPj31/7pK59y4qlnnnr8kUfXVpR6eNtrXlf5xU9++Jfl3kGS1DK8bIBqbxX7YPbYPGRn%0AWbLqMO65dRP1HU2EUtTrc7zyz//y/U971UuOP+6O2/52+6ats/dcf9N3dt5z7/Xbxqe+Ptaeq5dph3clBL8NfvfhCX57hQ0igGajQTM1xH3l5cedcfqLzzjtgiedcva5FwyuPTzqP3wFCSDTcMrUjj7qXK6+lmb/EFHWZnBpDSEfuwjTGkscK8r9FXZMzgAtRHXwpNrak45plCusOvtcjjiH2sVP/52XTm7Y/tL77rrrw7+88Sc/WnfHr74xMTH+%0Ak9nZVrMnS/HeI/4LTubfGsH/nt2PNYbZuQlsJGT/wOArLzr//GecdOaZFx9/8kmF5cPLaDuYNI4tkympNZSFIuotsPqUE44un37SIVUtN6Wb7iGOBnDOLzIs5xxZu43JUqzJ8ITQx+PBSYRzi8IhpSQmyzAmg7lZ2s0GUqru78syS39/gfSoo7FRH/1DtYurqw5XO+dg3ADW0eclvSuXc+4Ry2tnPOPxv7txw32/e/evb5+++1c3fWP9Tb/6/NTE%0AxE0SQe9g72+N4f+yEQgh8M4xNbYLR7l4/Akn/Ok5lzz1L04//6JDVx+1ltRBO4HNDWi0E4QQaKUpGk+kPCZzrDrysIGVrbHlza2bN80ZQzsdwZmMmckJ5lpNpifGwZZPllobIaQG+XSlBEqA1hq0Quc5AQKccyAEQoidaZJsjlcesrJ3aOQbO7dtao3tGqWiY2yW0Z6dY/iYNTR2TLJ6+eDZ1cFe0sRh221iL7BeMVZvIwoCGRUYWHEcF686tu+i%0Aiy551frbb33Vbb+89oqbr77qH3fs2vn9SmQQshf+DxuD/j948IeTf3yckWpUPO2ss197zjOe/+azzn3c0v4VK6mnsGNXE2slCk0ztlghiVKPlg7tBa7dpm7bDNSq9CxfsWrdlm03aG9KUsenWiEvPOu881WpVl09vGT4ohUrTjjiSU//HeJiRLmniipGWMAFX4AUghCIBUOwxmKt5cKJcWSxjGyO//2Dd/3m3qOOOv6GmfHJcZSe9VJ/dzppTOzc%0Aes/g6aedcGK5EjM6NYGyjpIuQJbhhCWRBt+qkHnBrG8RS8HRZ53DCWeddvH5T7vk4mt+ef1dW6/84Ws2PrDrmuoQ9Ebi/6Rn0P+XTn6AZr1Bq9WqHH/6KW+/6DnPf9UR55w32D8yyOxMg/Ed04BACon3HonAtB1KSrwQzKUWrRTGGnSmiapLOPSCiz/Ud/yJ7zj5xJNXDows6y/11OgfXkKxp4ckMSgnUFKSGYP34aT33gfExHmkDBuv8/oCQqQY%0AWhuQNmdt35ozn3y2lpzdaswyumM7Jz3/dz+8c+f2xj3HHceKM84dmEkSkiRFa0UjbeUmJbAtkKJNmxQhoJ1aZsYbSCmprD6Kpx5+3HETZz/l6kOuuureu6778aWbHrjniyXhEMT/pzyD/r9w9AsBzUadrG45YtXKZz31xX/yyRMvePwKMTDEbCJobBsjTRKUUmglcd7Nw7NCdOE95zzGSZxzJM2MckXxlKc/Y3WtUoC4SGYEjXbCbNsw1ZzBOouw%0AGSpPlp1z87idAO98F8jrfNq7EBtJGUA+Z0MYprRAa0HfihUMq1WFo088vnDuhRdQz2B01y4kkKQpCFBKh+QXgRMG8Hgf/oa1FoQgzQxCSorLlvDkP3zh0eecd8oXfvmTH77ohh/+x99uWL/txqGlET21ItjfGsH/7v0vJVm7TaM+w1HHHn7+CRde8MHTn/iEc2uHHM7OtqE5NkFsPLGO8Hi8E5hsdxuS+UkdNrFAEMURzjoas9NIaZlptfDeg4jI%0AsnCS4yVSKpxLMMIhhMprJq5rBUKA92GD5naB88E7dLyFchFITSY8DoMXDiE91mbEhQLeC7x1oBRZlqGUwppkMeqVG3EnD/Leg3TgPGPZHFOxpHfJMp7x8tc/5bRzL37Kj7765W/dftONbx/ftWt9tbec/7z/rRH8rwv8gcbsLDh/3JlPeNI7nvD8Z76of+3hTDvYuXMcHZWRVmKxtNIMKSUen0OlYjdjCv/v8+O04zWsMaTCIqMIrAdriFQBawzO%0AOrRQWJ9gXNY93a2zCBaHQAuvDnzpvcc6i3YWKyOQYAV44XAYrPc0bAvlHLHSZFn4fWmadn9+YRgoRPj7nc93jC8SDp/AlLPMNRKGDj+Zl/71US+875dXPfcn3/7qe+574O53Uygho+pvjeB/U+zvrGV2ZorVw4MXXfj6P778yNPPqtRJWT/TwFhJUcaoeosYRVa0GGGROYNEKRU2zoKwhfmzO3gM77t1hUwpTGZQUqGMJ7UZwgsUIYyyIsNiwJOf%0A+L77N6y13cq692CtQSmFUgESdd7hrMNgADB4vPThNSjwShA5j08TrA/V5oWbvfP/nftirc3DrNwrSEE1k2A8TSVpS8mWmTnKkefQ8y/Uf3zKce+65t++/sRbrrvhbQ+Ojt6gpPr/ZeKs//9mAO1WCzE3wylnn/f5E88+92XFNWvYODOJlzqgPF6B9DjjyKyjbcFqgcxPZynEos0vhISFJEPRCejD56yKcIDNT35nDThPJBVSSgwZ1rsQ6rgQCnU2%0AZ4dSsjApTtP5zeu8QzmNEgpkHtN7h1QKYSXOe1LcIr+1cON3/r1wLfycdwJvBbHXaDxWpBgNqRZsmp2mt7ePp/3xay847tTHXf+1L1/2xdtvuvn/2YgtUsrfGsH/VANoNRsU4+j4k04/80uHnn7Oqa2oyNT2UbSUKFVGeYXUirZMSUSGFBaRxpDJBSGDWHT8y/2dfCLLQwuPz3MHvKdlHc47vBL43GgWhijdsCdPWsVun89jFgQJSslwgnsIAJNC%0ASIlwHiuDRxDIbo7R+fnOv6WU3c3f8QZSylAhV4pESIRMiaQhFgJhFV5EtKYtWwuS4dPO4w1HHf/Sf//i5876z29e9qKZ6anbpYx/awT/o8If52jMzLDqkGXnnnjGOVf0HnFcadtsm4Y2DJgEnUf5UhUwwuILikRnGJ3Rk5bRXuFF2IidUGRRnL7QLHYzCuUkPkf9bXfzOTy5EQgFhM3dyTm8zzfobhtfKjVvLLnH8d4gBUQivAslJJkD4UFLhY0V%0AFokSrktC9ARkyHvfzWcW5gnd1y4FLaWwSlLyCQUSCl4gjQZRQPiYOavYXk8Ziau89M/ecMzJxx15yw++++1LHnhw25X91dL/L8Ij/b/cAjDG4Kxl9epDP7LyjPPfmPXU2DJdB+Eo+gyrSzSFRso2LgukM+kUQkIkBak0OXrTCSfMoujHS9GltHY27v6uDugphETbBKzDSIEXAqQCH3IO5wQxKUp4vJBkeIy1oBQqZ6Z6RKBS4NABV0UJgXRgpcdK%0Ah/MhvhdCIL1CqmCrTgiUjxBWdHMEpdSi5LhgQz0EX8CLAplUqEihVIRH0qMN1juaWYEdssiJT/99Pbz25Cu+8LG/++d77/jNq0ZGhv/XG4L+3+wBTJqSJQlHH3PcZ5avPeEVcwhaM3XAEQlDJBypEkip8RKkFEinwYbYXwiJVbZbS+jAlgsvKXweIpHDln4P5EjsjkzlyIuUAuM0WI/Lv9e4BA9oGaGkwgtL26QBv5cCFUVoBCZpIgCvImxuQE5I%0AJB6kCmCUc91cxgsR3kcM0svw/iQgPE561IJX2Q2XAOk9EQIvNAiJ1DqkPCL8rMIRaYURGutg13STFUccwzs/cOmffu1f/nnpVT/98R80m41GX+/Ab43gv9QAEKRpGyXEylVrjvzMkjVHPXWqmZEqgRESpcAKgxQe4TKc9HgBSkqMDJtNyM5GkfnmD/HQ7vCoEibkBWI+sd39teyOILn8pJUC8GW8k+Bt+D3eIfBYn2ABG4FxFm8cWkgiL4kiSezA%0AW4d1BvK6gfUOoTXEwSAtAm0iVHihCATSO3ACpERJhVHghEUhF9UNFi5kqGqHXGFBUi0VQgTvp3PDkFIx10zpqwzwR69/y7OHhodv/fTHPvSs2ZmZewarvbjfGsF/jQdI0gQhxXFrTj39a7WVa04cb2ZkXmJsoA4457HCIoXDG4EQCpTAChFif9V5wHmSKWQ38Nd5TuDzxNTsdnruju877/aoI4WCmADhsSQ4B8I5NB6ZOwvnQ1XOpCHkiqREeEhm%0A55ianaUURRSjCFkooUoltJYUtEZIGfhFziCUwsnc03nwOKRQSC+xPkC0Ho/Ic5C9vY+AiIWkWSm1B7waqujghMLl3sJKzXRqKMUFfue1rzmiPNB3yyff/4HHTU1O/rp/yfBvjeCx9QCQthNK5dqJhx1/4vWF5csqM+0MawElkUhMFja/xOFkJ1kNGx4hEFoijMDLgKYooVFSY60JGyQ3gg6K4lWMQ+KdQyqZx/rzr2n38CgkFLl38IJMtRACdF5o%0AEwTatIgUBoPyGm0V1mQIrUmadR64+26yZpNyoUCp1osoxJSqFcr5qvRUKVcreGcwApySWG+RKnCcAKKoSKYkkXdoPw+fLvyIEOFnoEvtEEKgte52uWkt0Rq80Fgnw/0RAk9EC8H2pufpL/390kDP4A2f/eAHLpkYH//5slXLfmsEj6UHKEVFDjv2mKvkwFBlfLqOEB4tFSLHvBESLzwWj5cSLzK8MCH5kwJJ8BReQBRHwbBMEsIAIbAmwIhKKYRS%0AZEkWPIkAkxtGh/Lc3fRCLEoJnHU479FS46JWCIWsBBd4PU4AMibxhoIHlVeHjQOTpGRpG+ctjXaDetrCeYHUCqkkURxRqVSpVCsMDg5SXjJIsb9GsVDGS4HxHkfHU9ChZu/1nsoFXmChl1BKobXu5jVSSLyQIDxCeKQWyLye2GxbdkwoLnjOU+KeSuVnH3nPOx8/OTF5VUWq3xrBQS+CtdtEpeKKQ445/ou+t6d/anYOpWI8GV4akB5vZXDXnSRS%0AghAGEDgBXgi8MUSRBiGoz80hraRSLpEmplvJ9YDwgjTJiLI6SWMOHRdC+OJcgB39QsR0t6TTOXAOoSOUbIN1SKdQLuDvTmpET4m4EOOMwYpQALAy0CLKPWVsluCcxTmBFDqEXc6RNVvU2wnTO3cxtmkrUa1Iqa/KyNLlDK9YQd/ACDIukBiDEjKgTN6x+5YM4U5YnbrBQohW5p4SqUEqlCB4WGmRIvCyEAKBop1adtY1Jz/lcbzWvePn//x373/W%0AzPj49/+3oEb6QDagc44sy7qrkyCmaRrcppK7RQS+G050qqLGmEW/wxhzQC9QSkmz1UJGcNiJx/9rWozO3TU+ipZVyrFExx5n2rTTJEQgCDwKZIwUkvyARyiJy/k7WaRx3tNutXGpw/f2USgUSI0F79Fak7TaRFHMs59wHmtXL2Vyph7yjZxi3cH8O2jQvCH4ReiLcBbpQbkIRYSXmhnj+OEvrmfjrl04XcBGGi08iQiUzd6hAZxJ0QokEd5JrDGY%0AzODTNHgLkWEyQzI1SXNqjPHNWylWa4wsW8EhRxzJ0hWrKccF2hKaNvQudF7v/Mkf7q/s5AeA0robLmmt0ToK6b4MSJH3FuE9wkmc8EQolNa0LWyZdZz59MejnPmPj/zV28+fmZy8rloo7BVQ+B9nBNbumy/b5bfAnyopo85pqZVCSembzeaniqWiE2LxabiQx5LfhOcrpZaFOFOTJMndc3NzP+sd7l/095wPXDRPQGPa9TpRXOx7ysWXfH3JyqXn%0A1lsJdolgtp2xZftONJJKMWZ4aAmx1Oh81xshcUKipEep8LuM89SbTXZNTDBTb9FoNnHG49MGg6sOJcnx9ogMb2ZI0zbT04fwpBc+k4N5plngV3fdx23rthD3QmqbFKMCmSUgR1IRVXqQQBQrpBJooVCo0OOQGLI0ozE7x3gyi8va6KYlmWqwbvoe7t/8IEuXjnDiUUez/PAjGewfIrOCzCtwGiFiIu/RUhAJiSJCIpDOo7VEaYUTHlTIf/L0JsCo%0AKBwO8Figx1iKAqbRtKRkSwNOfdaTxGuz9Ifv+cs3nD0zPnFPtb9/MfXkf6IRVCqVfXqB0V27OP2009701re97cNxoYDJPUGxVCJJEr79b99++WWXXfZ4rfW01pp2u82aNWtYsmQJzWYTay3e+5e+4hWv+MJxxx5Hq9WkVCyxacMG/vb/vfMld9x315d1NF+CL+pACpNCIqUmazf5oxf/wS3v+9u/WSOFxyGJSkW2j8/w4Y/9I1dddy1HnHEG737b%0AWxnuKZElJnBsJFghEC5UXL0H4y2NZosdu8a5e90DXPWLX3DnXfeGdslmHacLeKnJbIbE44zhO9/7Ea3JOn/9xldTjhTUW6AlRBEWgbBmsXjTbrTjvHYLmPAiogKzTrFsuI+1q5dji6G63G4FKZskaQc6RGoDMiVAoQMEqhUohY6LVOKYoWUrGCGlMTeD2TVDc67FRGuW6WadLfc/yMzGLaxYs57DTzyZQ9YcRbl3kJQQykmtcYDxDh0BCgSym9+E%0AiM8vqD/nnk3I3OnlBiLz5iA82gqyhmfaCZ7yvKfXmo36je97w1+cWp+YXF8a6F9IuXpMLg+5eT4CI1iz5rB94jFxFPH85z//r1asWrnX73jpS1968s233PIPP7vyyj8aHBwkTVPiOGb58uX09PTQbrcBhtasWcPA4FD35waGhvnQhz502e+/7A+bDzzw4L+NDI8AUNEC7wLMmKUZv/uC533hve/86zVDtcWGWhju56TDDuea624k0jHLhnupCKC0%0AH05LXx9HL1/G408+gZe/4Dn87Lpf8s1//z53P7gVFVdxUYQTHqVitJaIUsyPrroOpSL+6i9eQbFawrda4NJAp/YOvyAJDB5xAYQaKgc5S9UCnthZllY1hy8p0xaWerNJ0wkaLUOr1URJhcMTRTFaaKQLuU5mAjHPa03DerQWFAolVlaqlEcOpdVKGZ2eYMvOzTSnJim02ozet476+ASTm7dz6LEnsuKII5HFIhbIrMcLhxKBBYtUCOWRMoR82suu%0ACbPIrPNQ1UOqwKgAExcc2MyROdiF5JI/+t3arrHRqz976fsPNc6ZeEES/lhcFigKj3okRrBt05a9MRKYmZ5haGT4jMdfdNEAgM159wHms+g4plar8bSnPvUlV15xxdt27NixI45j6vU6q1atoFqtdoxgtNVqAdBuNUKc6T1rjz+ej37kI99+6R+//Kmzs7P/2dfXhzEWj2BsbJyzzz73w+955zteOtzbg83aSOsC+hiX8Y0WcdbGGxegyGYG5Qha%0AjcC4lAoRFxcRGeicZ3k+UtCKp55/NicfezQf+ad/4epbbkOJHtoOjFZIpZFCoquaf7/iZ1iT8tdvfg3lUgmfNvHWIlUcTtDFmfJi5MjLcEbJcIoWybjhyss//oOf/vyfir212AshdVQws43W0WuPPfHbfcPDTM82Uc4gjUQpiVAB+TLGoiONk5IUh/MWaywiKqH7KiwfGmDFUWtoje3k/htuvO22m371zMGh/iPSmZl3TuzYfuGOTRs5+rTTWH7o%0A4dQTgyXBuBQhCgRbU2ghET4kvYKOR1gIC7uuMaQq5EDaerQRFJQi846JpmM2Vrzgta9bMTO585tf/4dPPT8q14iLBR4r6U/hwxZQwmP8wzSCrdu378W1eLZv285bnvWs9w8tGQHr8c4hpAI8WmkwFiLFmWeeydlnn/32O+644/V9fX0opahWqxSLxc4b7gb+cRwj/PwmOf+CJ/LhD33oh+94x9+MxHFhXMqU6elZTj755Cf93d/93ZsOXbUKXIbC%0AE0w8bDJtLdIZBBJnHFEneMWCA288MzNjaO3zZDkGFVGrlUFH+LnZQCsoFlk22Mdb/+wVND70cW647W5EXME6j1ES5QVeRMhSD9/5yZUkJuFtf/4qhnqqQAZED53Y2923kACbksyNXSVM877p2XBPih5GN2+8K7LZnw4uWXFm5oQulIpHF0s9ZxVKVQqVEjKKQSu88kit0Domk4KWEChv8E6Ak9TKMYceeQjrbrrh2+1WY0vaLm9Zd9uvf17bvOEN%0As5O7fm9s55Yzjjz5NE467UxESZN5g3cGj8Jrj3EW6VVI/4XHCRDWI7rRku+iYq5LOfG5iAF4LXEKJhpQrEn+6HV//rx7b7v373/z0yveGBWXgPgfGA71jwzt8YU0TTmsUl71zKc//eKO9cvOb9cKsvlkevny5Zx77rmvWb9+/TuUUrPGWMbHJ1iz5lCSJAFQC61fdE5Ma/BK8tznvkDs2LHzBx/72MfOHx+fTGu1vmPf9e53X37qaSfmcKOdP8Xz%0AipPNV6cXN1Rp868XSvjUccU11/Obu28n8+BEgWKxwtLhYR53+imcfPyRQZWh3QJtGO6v8YqX/gEP/u0H2DnXwosi1iu0imgm7QCdVqrceMfdfPlb3+V5T3kCS3vLOC9xbrHon/ehcQYhKBYKOQ1adt+9RRDHhcEVgzUassB44pFRRLlaYevmzZ/ZOTb1mdrAMFpakpm5Q0qV3hdFpeLhvYODstzTc3Khp3xaqdqDKldwsowvlPAiwzqJlwKvHZs2%0ArJ/dsGX9JwcPW05JlpicGGdibOyjqc0+OjO2/UOzk6Nvnt2xhTMuvIAlqw+hniRkOLIsRUVFrHcoL/HOI7REK4X1Jme8zvdFSxeACAdkwiF0qMwLD0UP07OG5UNLeOt7PviGt+x82Y83b3zwxz1Ll3af28G6nJBIY4nbszltRTw8I1i5YsUeCfHY2BgnnnjiK08944xwoiFARdh2AkKg4rj7wAuFAuedd5763ve+9/z777//C1prBgb7UVp3PIHf%0AV85hTEIUlXnNa1535oYNG7/yT//0yb//4Af/6kdPfOJFcaAbeIRXuY3PJ2VtDYmSGOnwIiSX5EkfQkIxZvP4HJf94MeIQollKw4niov47B5+fuPNvPqlv8fFZ5+CwOTUZzh17aFccsnFfOGb38Yah1cFWu02Sil6+vs56pgzGekrM9Fs8+8//E8zsfH++3Eydd6rBbGuEkKkjUajvXbt2pNe/id/XFRREW88Ho1E4tBYL61xsLK/gqxnTFuH8xAX%0AixTKZeJCCena1NvtTZnxlzZ2NKjOzqDSNqWiPrI6MPB7w4NDT5RDQxdUBpfiywOU+4uoKKYM/Oyaq98/PrptsjI4gEtD33NcrZAkCZvX3/eXSWP6+2Zq9IMzE+PnnHLeRZx4xqnUcTRNQAOdp9sn0S2FuA79Yr4pWlmVN/Q7jMy9BQLtBEUjSKRiogmrT1zLH7/xzV//8Hv/5rQ0zTYI5w+qoIUFKhIiGRDGh+0JyuXSnl+INBecf/6filjjUxvi%0A30JMc6qBc47epSP4JMMAURRxzDHHcN655/1lmiRf6Ovro7e3l1aztVce+6JwQWuMSdC6wJvf/OYXnnXWOS98znOeGwpTmUWL+R5fEHgRsAojJVnOSob5jwgFQgVPoQv4Wj+DS1eiq4OgCkjv2Tk3xRf+9busPvQQ1i7twyVtrDVESnPGaSfxk1/8gq3jUzTThFqpyuFHruXQo9ayectmbvrNbfztG17FtnvvHfvipz5x4UClOiaU7j7OKIokYG+5%0A+RZOO/20v/7T17zqvYuJH+CExgolMySJlyzpKSDajp1uIVU75BMqipBRTOwDIudsQmN29v6pRv3d7dmZd/uNm06r9C9/5dDIquf0LG+NDB22gq0btpjNGzd9RhfLZJnHeBvuj3foQgHvMibGx65xSePcuTnzlfGpxh/MzE1y+uMfT7lYwpjAlu0Ecs5aUmfolEW67BA80guUlyTaYlQ4jJTzaCspGEEiJImIGJ31PPF5z+3fumndNz73wb8/Cy9R%0AscL7g2MJznlqFY0ua9JHYgRbt25Z5AVmZ2fp7e0983EXnDMcPmfxwoE3ZBh2jO7Aa0HvwAA6N7veWo0Lzj//mHvvueeFSTv5ls0szvqQNHqP6IYDEqQkyzLAEzmHs4p25liyZCkveMHzAUdiLGms0JkPIUvk8gpwnhM4ico03gZ2pcsLPF4Fdx0jqGrLoSMriGsDWKVxUuOdpVirsX1igqtu+BVrn3sJqlDAtRNQmsOW9nPY6pWMth2lYo3jDzmM%0AOE340Rc/ddnsxNaj/unvLz3rCUet4d9uv00N9y1tDfT3YGza6YIhjmObZilDy5bQNzQ4kWWGKI5zQp4DFNI5pPVeWg8mKDYvL2g2acFsBoWHSv6kQscxMo4pFIuM75q8pZFEf1qfGH2LvP/mPz9s69qzNm/e8DmR2KlauTcEkc4x60NvgsgbeErVHhqtJun27X9Y9lx1w9zoZyanJ8W5T3825dog7UaK85KStAhrwQeioZABphXaYb0hw2GExNu8%0AIu5DBpZ6QSYFWhpcO6EuYwoxPP33X3vmz3963YfXX3XFm8Xw8EFLD5wDpHxEAZYG2Llz56J4dvv2bfzFX7zhHcuXrQZncd4hI41JU6JCzHe++91PH374muN+/yUvPR/v8NYhlOTUk09m6bJl77jyiiu+VeutUSwV6e/vJ03TRSFQh3Q2unMnK0eGkIUiPoEsgygykLWIZBEnFNaFwlCsJFLl1TSVtyO6jnbPfAgopACXYYkpRppSuQcjNULqEKsK%0AEXB+rdi6c5TMeSIpiWQ42QarNZYPDzO4a46GrnDHL29o3fbjf/uYKsc9P/ju1458yuOfFJAlpTzOlZ2T9UBt8ERRhLWWyZlplNaUy+U+Z/1egkCPyM9AwXyzziGD/dy1bdfDQk90HBNXKqRzozNzuybePbVrFGMyiqUKpt0O5Dgl8jheLHrOOoool8qMbt7wOTe57YF6Zi5vtLPKBZc8k6ElK2m2DanN0NKGXgMf7r8QAqzDYTu8WDrvJdBOAqzq%0ARZCDETiMg7EJz8rhKq99w1vf9M51d32x3W7eWa72HJSEWHioaot1D98MJECpVMxXCaUUw8Mjtadc8pRndv+IC645SVNmpme4+uqrP/KjH/3n/wtPQeLyivPw0qU87nHnnSgjdfiDGzcyNT1JaR4h6vpRkZfkt2/fzpU//Sk4R6kUUHVMAsIgRYrOEmbnWkzVm3nTiAKh98CstdZ0iew5Tu+sQ2kFUQEjFSkSKyVOK5xQRIUixjmsdfN30nsKKqai%0AI0reUrYpt13+9deXYwo/+cH3XveUxz9pAB8MOnVWOimEUJJiuYrUMY1Wi/GpySBsFcAEf6Bxb2Isywb76K9WaCbZw4qXvfcoHVEoVQI7VgfyHzkJkIciswmBLhSYnJ65qjW647x1P//Zhiu++XUmdmwhLigyYWnZhMRltLFkWKy3OOsQOUfKO4dzNpAArcHmyxmD8ZYUi8Vg8IyOOU4+6zye9wcv+26WQl1VacW9tOLaI16NqB8dxxSlfUT9DBKg%0At7cvX0Gl+AlPeOKrzzv3cQF1cRYVx7hc2OmnP/3p9c1m84F169Zddd1VP9/QYSN6G4hl5z/ufE495dQ31utz7Nixk3qjidZ6XlHBB7RBSEmlWuETn/vsdV/65rdGQyiTK7CFjBjlMu5e/yDX3XQTKiosMgDpA4wrlUKrnGBGzu2XUS694nBKYaXGKY2MYmQUo+OIzFqkVkQyz6RcYHniHL2FmJntW7nnhms+c+hRRxzxo8u/98YnnHcOZK1ujcE6%0AJ6SSUmqNF4J6u0G91cQ4N9+TsKC5fr/u3IcIfO2yQUqxJjH2EYUKgdbycNUgBKVymTRNb2tt33LW9l/fdPvl3/gKYzs2I5Unc4bEJrR9StultLMEkxlMakkX8MH2ttIsI81SsiwhM4a5JGE29TzlhS8+YvVJZ3zYjs+SGkVq9CNbmcYaSezNI65Ga4DJyamcJ+QYG5vgggvOf7MQEu/TrpxfmmXU63Xq9frkc57znPMa9brdsHHj3ed5DhNK460B%0AKVm2bBnnn3/+q3/1q1+9r9Fsbm82G/T09HQ3Q0c1AaBYKNJwfuLz3/jXVUefeTZnHXkImRdEXoBx7Bif5XNf/waHrFzK76gIfDbv0a3H4SiWinkjSECHvLOIHCmS+YYQSqNlaKaPhECmFi09S4YHUEqEjS1DriIyy/iO7WO3XX/NJalQ0fe//M83XnDRhZC25+tfQBRp46yzaZowOzeJ8zYQznLPZK2lXC73ugVPxjmHZO/aPQJIM0tvucRJq4ZY%0AP0dIRv8ryDP5ayxWqzA1M1bfue0ZLcVtV2jV/6TnPZO+kSGarRTvwAoZSmgetLfYvI7QccWL5VgEXlg8Fu9zWfrUsiNJWLVsNc95yZ+86R/f+qaPqrmxbbpYekSIqfeCgrJUVYpBPXIjWLZseTchPuSQQy9+0sVPGgJPmmXEKsZbg44ienp7efnLX/6MNE2fAQKtJGQGlOxSiJVWnHvuueKqq6/+i/vXrXuLjvQigp5YcOOzNOW4U8981i3rHuQj%0An/4Cf/OWN3DCkl7AMTo+zie/9E2u/tWNvPK4P5pnQbqc56IFcRw8gzHZfOeUCnCqtblsorHEUfBWxjliBSJtMVytcNYJx3VfixMgnSdNE+74zS0/mBnd9OvHX/KM33/iE56woOisFhSfhWslrQlmg+uXMsggdoiD9XqdVqs1tjtX/6EZu9DKDLVixNEFxbqxhIbfXznuIFRalcImhtnJSZQxpO1ki2g3zo8F37xau+MufNrTGRxZwUwrJQW01HiX%0Aa2wIH4QKmO9c675XIRA+Ax8U8xxBTKxhM3bMZJxwwYWc9aQL/vqW7371NT2FZY8oG7AIenToF39UnmB6ehrvPTtHd/KMZzzj7cuWr1hEo1ZChQ4jIaBUZiGg6pOsWwF2PoQna9as4eSTTnr9z372s3f29/W1TjrxpL2ePkpJ4p5eotoAD+6c4P3/8M+85FlP5shDVvCdH1zB966+merAEBR0nvzIQDGNcoxaKer1OtMzM0RRzILEoEt1jmVobcQY%0AHJ600aRiE5500UWcfuRhOG8JVQKJVILN27awadOGH0oB2jZtq96iVKvgZQFhHSLKUShnSxOz42eUWoXRSrlcdC713nsvpRRaa9VoNGakkrViqRiMzHuU1gcQmEDbOHoKmmWxZ8zkBLfH8JJKkSUJ03N1ULmnnjN3jRn3RB2X7rqxWBm88IlPRlWqpAaM6MjpeZxwAXBYgC52qeRCoK1BWBeEvcjynM0wUW9Q7e/jqc99/svvvOrK907Vk+1Rby2H%0AeQ4QEUKAs/QK/6h6m3WObZOmKX29fSMXX3zxEzqna0ABAqUWtfcnIQr55zMTQusso1qtctFFFxW+9vWvPX98fOIrYlGQugChEJAKaBko9tS4f+NWPvjxTzNUq7JzsoGsjTA3twWEzBNN29XWCYzLhHa7TalYmv/dHfw6bwrRDkyWISNNHGuGB5fz5HNO4/kXnY4MLTmBPp2/v19c+wtz1523/SzSMpxg+ct1NhidsyCs48wzz+j/9Kc/ebXLrJXI%0AKKeMe6WUKFcqcsvmzfIXv/gFO3fsZOmypaFZpUs72b8hNNOMwZ4Khy5xbBmfe2zYl2Ke6ySEwEdREAOQ4J1mbOvWUSsLf+TKlcsjVeCcp1yCQJOlWQiBnMdJhxGiy6RdmAN5ILIudM4JsHis9Dif4bwhmc5YccTh0QVPe+YnrvzJT55XLlQCR+oAIzghFSVbR5kmzqtHZwTVcoVd9QbnnHX2K04/7fRwOjiQKsYKx47RXezatYvZmdmcDkCurenB%0AZRx77DH0D40gncUbA1HE8UcdyZMvuvCDX/zKN74yOzMzV4rzHlbfAq/JvCITMcKWEGlgM8pqlfFWgx1jM8QIFA7jBKnJ403VAYQDaa6ZGmRvhZ6VI7Sko0bQE7JpghcxJ558AkuWHoJ3oIsx1WqFQ1etYGlvjPVgjMU5i5ISoeCBdffzwx/953uL5fKEigtoVYpdLlMtlQs33oWC9IqVK1mxcuU+Kav33HkXP/j+5c+WQv7eBz74gRfpSJMlKVKr%0ATgHRPzTuHZqVjl41SGoyNo3OBM98MDa/koErbfaNOQoBcbnC5OYNPyj39Lxjg9LvjQf7WHv6WbStIcoJc6m02IJCuHkto07I6HN2FbnwmM+Tf+ds+L7EMtqrWHvJE59748+vOmpm46b7ij09B0SncB4iJRjokzipHhUDQwPcd/86GvU6f/VXf/UGqTrFrRyxtJa4UOTb//ad+y+77LLf6evtbUmpohzgNtu3bvKvetWf/tO73vfBJ0o8Timwlmpf%0AP0972tNWfP+HV6ys1xubuyGxt+BlN8RyLtQNhHcBeJERcamCyhJIEmIpUbknwGZh6RhPCC+WDi+lp9iDsjKEKtYHeRHnOHntEbB2L+/aZAjvEN6hdIxQitmpab582WW3rX9w/buWLV/FuvvXkaYm1Up3CYVCgIhybxM6YMK9l6HzS8fBm3zuM5/d+f73v//Zzrlf/eiHP/xesVAY/Nt3vetJUSHON5hA5hnkvnIFIQQ2v/9rlvYx0UjJMosWkrYx%0AZN0ZBg//9BeJxbYyROqYm96G8J5CvjEXNUchKMVFdqy7732iFClXjN9VrvWx/JA1pGmbhhNk3iLQXa7C7rbd6UjwgYcR/j+fyyC8ZIdtMrJiFaeffd47fvqvX31x5GsHNCDEeEEpDn3j3j46N6kBtmzdyrnnnPOMpz7taYPdt5/34fo0YWZ6mnvvuee93rlbG41GwOVzJMBay79+81tvet3rXn/r8LLlSKHyo0Ry2ulncOppp//ZPffdc3UU5yGL%0AikFqpPdIl2G9xeJRncTWGnA2QKVCgMlQnVZMFYPxeTO9oFQsM1DuJzKa2ObxtosIys8ajANp53OFDrQjQcqom+jedefd/Os3v3Hl177+9T8wJqhOZGmKMdmeOKVfHErYkIEvNIDmO9/5zsdprdcPDw9TrVb5zne+8+Qsy374gQ984KlCyQ5QoDsSJw+F/zsUxaJiVU1Sb02ihKe3p4Ar10gzg3+4+JEEN9tE+yBI5nxeUOuGaX4Rk1gqiW012bl5%0A47t7BwfPWn/d9U/r0QWKA/3UvQmHZSvthrl7Oji/YAaDXywN7zypETRFxNGnnv4H9976m3elzj8Q54fFvslyCmFaFNIJQjFSPHojOOXkk4ee97znvadSrewRKxYrZW697Vbuvffe7y5ZsiQfMzSvprx27dGM7tp127e/8927Xv3a1x7HgqaS3sEhnv3sZ79l+rLJP0/ykAZZ6P6NqFQALcmcRXiXE0bDCd3dBGnaxd1BQyFsdlUsoYoliOIAjRXz%0AG1EoLGgdCIMzOs0s3TtrLHNT4zy4/kGuvfHmjVf+7OefGx8fe18cxzQaDeI4phDYn8rmiVrnpwV7ZjcdA/jsP3+m/t73vvcipdT6Wq2GtZYoihgeHubf//3fn6a1/uF73/++pxZKRYQQtnMfHwrzd96yefM2knaLslI0Wi1Wr1lDuVTipt/cii+P4IR+SOHgPbyNknTEV+d7BvYeFnnvUcUiWaPJ1rvvfLVN7PX3VHtWHHXuObRjhUwtkV3A4fJ+%0Ar/Crx+fSk677b7wjE5qJpMHSVYeKZUetfeWvbvrlW3pU70NubCskQ74e+gZ49ArZGmB4eDi76667vvbpT35qTEk54r1XwjuPF3Z42dLlP/zhD79Sr9fnCnnTdGeifavVotVqkaSGy778ld8zxv5tuVw+NMsyJYRwSmsxOTEtqpWerR//1KeaJ59y6nPqaRL19PaitKSdtJlqzuEIU1mwHul93mgcdG+q1Sq3/OZWvvidfnqUwJgMKxSZLjJWb+Kr%0AJbbUZ7jsR1fTH0lIG8Ra4LEYZ7FUgiq1bUPSJG20WLdu3X/e98ADu7btGP2xtOm/R0o0e3t7aTQai2YHxHFc6OmthX0T7R3Z0bnIwOc/+y/ND3zgA49P0/SW4eHhLizsfaBTLF26lK9+9atPU1J9/13vffczSqWSbLfbu1FKFhtAlmXsGt9Fs9kKlIzACyEzlnaSIkyKbk8SVXrJ/N43ckD4PEiJaTdQRiCKEQ9dgfBkDob6+6iUYiaNx5uM6Q33%0Abu4ZWfGTocHBl6WtFi2n0cYijO+OtNojHPJusbZrR8HbewQO62MMlmZZccixx7zk1zff+Fdz01MmivYOxBgUFWWJVXJQDKBrBO12e+bGG2/80PXXXveh4eFh8C6MbXHe9w0ODG7ZunWi1WoxPj6OEILe3t5uA/3s7CzWWtqt1h1f/OIXn18oFIjjuDt8wlpPuVTgqqu28PEvfaN/5aGH33X8KScvm5ubRUaCyUxSKBcxSWiGkTaPHZ0H4fGFArfd%0A9wD3rdtAtRAzPjGJLBQ47OjjKdZ6kT1VxrM2//rTKyhJQVGB9waEw0uPc/0UVYxsTTG+5QGz6b6732bq9Y9Mz86xfNVqjj58NZg0Z/N4OkIA5VKZVqs1deuttzI8PEySJFhrF52qUkpiHfGTH/9k9oOXfvBi59zN/f39e6grdAxhZGSEb3zzG8/03n9/fHx8xlpLs9nsfs9CeNFay2x9DpN7k70VFZxQlKRHz22ilUlkoYbNkj08QLvdDhKK3uFN%0A8ARuP4GUA1QholApkUzOIb3lmAue/NfnPv15z+1ddQhTrTYmzcB72tjub9vTCPyiOkhnDFVHMQ8HqYEdacrA4YcvOWLtUS/Zsn7958s91b0mNNYLqun0QS0i6s7NqlQqVEpl+vv7A3chJ0v1DvRPTE5NLdK478h0dDZMp6egv7+fOI5zBbNQB1DAzulZNk80afmIZWuOLjRlhbp2JFkGwuHSNpIoyAWaELoEeUNL24Mu14iLRXaOjTPZNhyz9nh8%0A3yDTOUvVW0exMhD6kslHGgmLEx6th2inhtnJjWzfuu3SycnJjxy5aiVRHFMpl7pzwjrvqxOn9/b1sn379h+95S1veebw8PCwtdbbXKZOzBuBdNZy1513XQNs6O3tJUmS7r1yNoxj7dyjOG9H/dF//uiZURSpQqGwqJAohSSzGc1Wk8yGkU9xvJ+eaakx1lIUBkmTKdfZ3gLv3WLjXTT3YD/5sw9aqK16g7LWR596wYWfOPa8C5/YjspsnZ1DOCgI%0AhfMZqbTzOYHbPTHe0yg63+NwiMTivGDOGeJqgdVHHPmq6Q1bPl/yerGoGWBlhLSzKEJ1WBxsI3g4V5KmOVkqzxv3clOjSCGFYNPWXdy6YQftqWlWn3TS66pDSwemkoxUV2jbJrFMyExGRFCRU1JhTBLcqPB4qZHFEm0Ek62UgZWrqYwsoy01LlLE1qC8IFIxHoF1Pv+5PAkUitnZccZ2bb9y84b178Bk7JycohrlBTgpKcQBMp3X4clV7Kw1MzMz%0Aly88nXf3BCYLEGq5XF60odtJm2q1SrlcppmHWR1P09fXR5Iktl6vz1PDlaLebNDKmZ+hl/sAheBFeM3CtOnJWbLKN0itp2H9/geN7AVB8h7arSaDy5Y8/vyLn/bjwbVHRFtabeqtFOE0ZSvQqcVpg1EG4ffvCXYPhxyOOFV4J8iUZ7qZMbJk6Rm9g0NnNHE3RQuqhF4qVLsO9RmcEgffE3QMIYoiCoVC4L2H2UCUSqXuSSZyTZ9mu9mF+nQckWah%0AEqi1plAoBPftHXfdv5Xb7ttMKdaUyyVWDS55gU4yXGOWTJjASc8ESpXIjO0Ot/DCE+RrHWWbUjRtJmfqCOkZWb4EXQoy4ZIMgyNWEp+18FpitMJJiXaSqo9pzE0wtWWd2bHhwVda60FqxpopuqrokUFjdGp2mumZOWbmZhH5AL+FHrJarXYFxXY3AmsMcRx3NzgCMmuYnauzYuVKqrUeZmdnieJ4n5tRKUUrSZir19Fad3VB/cMAv0PiLolUSDgl%0AAUjLpw88LBjdO48UnoElK94wuPak99WpRH6qHeY3i9AfpzOPNJBqSFSQfp+fsDPvTpyJcVaDsqG6jA0csJxZii2irEY2LG0cPbURCiuX/e66W391U7lc7YqbOR0xZOoUhcMeZOHEoDaxbdu8OzYhzutQi2dbdebm5gIC0tWkkSyok1Msl5iYnMQYQ29vDe/hni3jjM80qPWWsSalp2fgxIGly05sJm1MluG1wSUGnYUyvXAWrMPbkLIpL/DW4lxG%0A23sa9ToDQ0MMDg0FsmfWQmiFlwrnBFoKjLOY3FAFHpKExug2Jres/2BjdOeDlCshBvUebzOaMzt5YHaMuUYD7wPxLXCMbHfDCvatSD2v6ixopxnGmfnRq0p2Dada66FcKjM7O7sHFq+kot1uM9ds7HOi5cNqLvHzuJWUILwjMf6AUESRTxf0+RDAJUuWXuJkoXT/gxtY6SzFvloYD+sFwoazMjEeoyXG++58N7+g/VJYA1bgZe7ZhcUJi3AGsKQm%0ARRqNkwLrLc2SZniw71mlpP1m4YKKoUVS8BmFKIREB3ucrIbQVO+9Z/uO7XvIIzrn0LmH2CuclzeVZyZjdHyMnWOjCO9IfQFdrGKxpE5QHRr53WaxwFQ7I0EhbdCpIWsGSXEf+omtDX9f5MUVo4sk3lPo6QOhGBvdRbVWo1IqYgUk5CNXvcILlUfDgkqxxNz4KJNb118+ufXBv0F6sCnVSDFc1pS1I0kzjIFoL+Qcmyf2O3bsYHTXrn3Cjd77rqyk%0AzyUcd/8eKcPh4ryjXC53DUFJRTtpM9eoBybsQWy6dT702w4WBXPp/utPAkFiXdBPkEGVev19d73isFP67reoeMfO7Swt6aBX5DypA+3C6Cmc6aKhi/eIQNBA+WAInfmfUnSs1eOtxziBESGHaRlHb62yZtWaNSPtJNmlCgVIU+TMWD7o/ODzR3QXj84f+r6KNw+FZ3dCgfnxoFDCY5oJLaPQQlEdGHrOjLHMuaBnWTaO2HpaNqD4gqBQYL3Mw7HA%0AGM2MC+xPHdFut9m0YSPVapXhkWFqfb0UKz2QiaD8rMJoIy0V7bkZpnZuY3b7xtdiUkrFIkPFAtViYJmmLgyc0FrsE1oEaLXbXbRn95O6Sw9Xqqvnua/7k2UZtZ4a5XKZRqOBlAEibqfJQfEAe8XTPcRKMFTZf/hgjWHF6sNYsXIFN//mdnbONBkbH9tcHd326drKNa9vzE1Tn6oQVXswxhLJCONBWYvsxPguhMxdz4nAkJKSIWxoXgmiCQInPN6L%0AfHaCxSBDTmc95WJVlXt6X7B5272fjCs99Js6yppAj3gMrsdEldr7sLGHK5Y5E+Fl5fhitbI2sQbvDdo7dJYhM4PVCkOYxBgIZkGYy9tQVBHOopzFG0tRRWQuozk7y4bpaXpqvdSWDDHUO0S52EMbF/jlEsZHt7Nz47rPTU3ObF5S66EnjgPE5mwuLHiANJsFc4X35QkOKNcUYYKNUoqt27bhvKNTnX4sVdmc54DkF1JjqZaLLB0aYLCskU6zZVax%0A8cGNl54wMPCHMW6gsWuUQa0RUuX1DYX2PkCvC+9PTo9QQtBWngQouJCndekVQmI9OCwCnw+ilVgDzVSiKwMvaxn5SVVvoKP0MTOArhFYa5mbm8ub3w+SIQBaCerNJv1LD3987I2endiFdwLnMtI0wZqMpo5yTpwLo4yEROQwmnNh9pizBiE1qlgK01ocCB3TnK3TaEwzV9rJQN8SeodHqAzVaM5NMbrhPrbdd+dfxqZN6gtM4AMCFBpl88Ybh3Pi%0AYVEP9mcEnUkvs7OzLF26lKGhoUV5gJSSVquFsYZSqbRbUSvUXToDMkJusfjhN5tNarUaIyMjB5dQKgTGOmbn6mzbNY5Lm9S8ZHrnru077ou/sXTVytc0ZqbRzhL31IJocLGEtZ75xqEwAtfnnsUCWRTaPL1xuNTgU4PzYfRs4lwof0mX010ihHE0IkEclU6XWfY0N7vrhxM6n4/wKK8kSYjygYh7GEGhUBhZtmzZeT09PVGWZf5gyuSlPuWwNUdf%0A0D9yCDOtDOc1HgO+DaSkTnYpPZ1cQHoCx997pDB4ATvHpti+axJnNLEuYZxAywghU9K5KXbOtpiYHKN/dgRh2yztrbD2caf/KcY8aIWIfd6OmRuBX2AE/mAagZBhMlmWZfT29rqpqanr6/X6llqtFpCnrjDXwrGqIVwqFosjxx133JMAZ4zBWuuVWswtttYyMDBgH1i//sdZms6Uy+UD8Eh+P+lwOHBmZmZYtXLFuWedeeYhrWaDSIHLhCGuHrfy%0AsMNwcQEXlyj29jKTGjbv2IaQxUUUB2vnC2IOUN4ijUFllqU9vYyMjKBjjZUeo0C4FOksEIOLkB40lnK0hKWy+cqx7RsqOtIcDNm6/N7hvb8TuGeRERx//AmXvOlNb/rSaaedGqqLD8MIHjJXwOO1wzuJMxHSxyA8RngSZUFkFL0JSXE+OdLnje8dlNxICTrmhlt+zRVXXc9Pr7yWZtIGFSazRM5QiBSZgaTeZOP6dRS15R/f/haefv65H2w1U5D5%0A2NK8+ydowGX54+9MEvOP6P3uniN0whulFHNzc9x9990zv/zlL396+eWXf2R2ZvbGvlqtOwRQa02xWMQ5R7PZpKen5+j3ve99Xzn11FNpNBo4a8PQ7r0gUh/8wAdv+PDff+Rc69xDbg+RD9J4qGekpWa2PcfIyJK/eOtb3/bRY445mqQZIGmXgHUaFSsyKTESVKnIhz/zZR64705kqQ+p426TTdewhSSzDtlqcMKhqzn/tDM447i1rFqyBKkETvkw%0AWdQnRM7jKCCcQhH6NZTPUCJ5dir1s20+EeJRM8jzCTyf/vSnx4CRxf0EPUW7dNmQ1VqrarX6WGQJLBxiMf/v0jzD86Foj8AxgysxJ53OzbfcxuTYLkqFCG09CE3bazIhkZGilBlKrRYlPDrS9PT+9w3jKZVKjIyM9F500UUveOJFj3/Bxz72sbdu3Ljx7zrV2yRJ8sGBoHXM9u07r/va177x7VNPPfUF+5LL71x//Y53nHPnvfe/7bv/9q8fHBrs%0AR+toLzTmgMTEeh+wYj5aqllvc+jqNc/+67e/46MnnHB82Bi1vvA9lbyBYsH141/dwdU/uwale8AmeN8m9RHOBWmbIoLINCnYlCedexove9HzWbFkaB/vJH6Iz1cek9bSkZGR6h7hkMmMaTaaDlDOpvOaox1Bl4e2r4fe+GKBOxa7sby82IuXE4tcuFUC0Xb4ZoppNIO/xWOxKATO66BPJILaTUFJZkcn7jJJ+1CggktZ2OzuvV/0dySPlT5sGNfq%0A0QitOf2sM/nYP/7jpX/2uteV7rjrrnfV+moYY2g06mgdI6UgiiL7la985cVHHXXUea94xZ8s8x5Elt8KaUBajM1Cz0Vc4L3vec8H7rr91l9s377lup6eeO8N/ELmShx+r48ozVIQjLz6Va/+1wsvOg/vQHgP3gQWqXN4Z0ErZFRg3bZR/v5jH6PRyij09+HsLM4meWFOIE1Qyiad45lPejxveOUfobQIKd8CIWYR5gYhwiDbPR6/78QDC2UfH8Hh%0A64TCEcSFO/lVs9mc3fOYRSyMyndbfj/L7mO58NGqMKzXRmBisAuWi+a/1l06X+H/lU+RtBAkCG8CzTqXSHE+D3O8C1NWBJg0ae7aNfpZKcTMfNV1fnUoNCIfQidEHiYd9OWCLoxzuMzijGVoeIh3v+c9f3vYYYc9rlFv5DylwIw1eeU5TdP2Rz/60T+86aZbyAdvdh+FR+ShXRiMccxRh/PBD37gS1EU9SZJko9XWrwC/G3y+WeLV5alzM7O8Nzn%0APvuyl77sxXGnWhwOirwgKiVOamRUoJ05PvHJT/Pg5q3IQpF2ajBeYFzoLlLeUCBFmzqnH7+WV77kd4IB2FwBxCSE0pchDM/q7JUFzygUG4JSnu/sI/8o1l5DRLmHEQgRxrLlJI3803LeRh4Z3LCnPT2SRQZkSDKEN5DzH72XQYo8P1mkd0Q46lNj34vx34mUruXVvsXDtncbZL2/W+j2s/aZhOY9GQ7f5RQ56zhy7ZG88pWv+GBvby/Lly/P11K0%0ADrBjb28vW7Zs+dm73vWuD01OTnUdbXdmTP66rQkn9XOe8+zD/+RPXv7ZNE27M+RsTtyz1mJMRqVaZHCoj77+Hvr6e+gfqNHbV8XYhFNOPekVf/mWNz+lXClgs3zTdfpJfMDuVd4v8dGPf/L2H/3op7vK1T6a7XD6t1OPsWGqj8/aaNNipBLzgmc8iVq1DGkL6Uz4KD0CM1+4e1invF/U5/LoIHxv9jACa613uTZiN1x4pH+ssy87dFmR4mXSXU48%0AvBVOIxUw5c483jz5EiJQJpyxSO/Q3mDT9udrldKMc7acozV7ZdnMb6sDOUcO3B27XFnC2bwnWQi0DvPkO0n/JU996nlLly49Y9euXTQajW7FvqOGV6vVuOaaa97y8Y9/4rrARgwV7E59Q0kZUsV8EsXb3va2F55yyikvHRsb22siHzj9OS+LXC3OZgwPD576lre86TNHHrkG52w39PXe42xeT8mny/zs59fywfe+56wkTS8tFAt4D2mW4YTEGB/a%0AXrM2kcs4cvUyTj7u6PwFGPAmFNGkCMbig+q1y2f4+Hy+3N6WX0AS7KBOe3uO+1wLCHudyxgzvZeKsaZcrkrYrXlEPVTMf0C2sDje6/BT9ptEL/wtGmIPcQkfFXBekWUWWQjMS5tLAEZSYJP2aGt25hqTtW2s9RzQi4wf0p+Jh/FODuTqPrYsDUQ0HUEH8cobcFatWsWqVauO2bFjx00dmkVfXy/j44GyXi6XUUrxuc997veOPurYu373Rc/vUVGE%0Ad2k4m1we31qBzQLs9/73v/9fXvSiF/10dnZ2W19ff/deKq1I0zZJ2u422zWbLZRUxdf/+Z//y7Of/ZyQYbkUvEBJHTrABGipQAkmx8f5m3e8/fdnpybbfStW3yhcGAhikjbFchSM3TtiCS5LOGz1airVGpgsJNVCdhkAQhRykUD9MO6yy/HCh5+/7S2eqVarK/fwBK1WwszMjAJIW+nu+/bRYbOExKSzgt3te4WgJsIT4dG0UWRoVKUXGVdCk71Q%0ACOdwWRIUI7yjoBRJq7UhFj4pKTnQrDd6957jPOx47BGYvwgJMXEwALFbyu8cp5xyyhPPP/98zj77bM466ywuvPBCjjrqSLLMIIWgWCzSarW2fPJTn/yzdesezPsBggEIRHCLC86L8847T77xjW/8D6217uQ6UgZelbMKbyXOSqTQGJNx7nnn/sOrX/Wqk0PEmMzvVW+xzqHlPGHy/e9593d+c9Mvv15buox6o77ZZclYQUlwBmcznAdjDFoIKsXi%0Agtl0cj6pyQeCe6mw6G42kFpIXPi4+0osGBvykyTJeARau1jnQvdjknR7K2ZmZkb38AT33Xfvj9/z7vf9zoknnPCHlXKZRr3O9OSkEEJ6Gyt8PnlkH7YmFtKlXB7nmcz4vr6+I3v6lh1jrFvMqXgoy81Py/C7HK1iCu0U3YSZuSbNVptiqYTIJ4VpJQnhsaVZn/tJu14nUkz8wz987L3X/vxn57SRRRuYwXtvo93/Ps8eKh6SCyAF771PkiQZGhxa%0A8+a/fPPanoFqaPbv6Px3+FlKsXbt2sOq1Wq3atzXW6Pa08M969Yj88Hhw8uWc+/6B770gUsvfdanP/kPzysUinjfXkSD8N6TGUOkNa997etOu+feey/98mVffNPw0GCukg1ZEjAw7yFJmxxx1JHPffvb3/7K/oF+nE/ysGH+nkuhu4IE3/7Slx789D99/PcH+gaI+/pJpd6atZs3x7XKU2PvcVmGEA6sQcoCxXIZlYsqWKEQwiHzgeqgkVqwact2%0Avvb179BOE5QSOG/33jshJMKmtJv1u3p6ausnp6dxzlGv1/eopO/tMsYw2N/HUH+NdpKGbr3Z2fXr1q37+Ktf/erFRpBl2cyWLVu/FWn9rdWrV3PjDTfyi+uvoqQqZPsr1i0QPQpUCUWkFF44hg5Z/bJ4YOXn0yTso04X16L36R86MFLSYU2YTSZUhIgjlNY4DykOfAElHNgEbPumuFohwtk777jjb3768yuJo3KQZtyH8e3vcNlfM/xCI3DOkfqE%0As88458mvf+Of/Riqi9HiBXlWZkw7SZLuw5ycmmbJyBKWjAyxfcco1Vz0oBBFXH7591742ZNP2PS6P3vdSqEKQf7QBa2m4CAczhniOOKtb3nzG39zy02/WL9+/b/39tVCGJEn041GAx3pla/90z//8hlnnIFzWWhp9XEXTvXWIMMUce645VYu/dCH/qi/tz9xxRLj9Tls2qZULt5a61/y1CQ1YbChcwjrIXNIB7p7V03uDWLwFiEMEDExPct/XHkN%0Ao7Mt4kKYGSH20qPsBZSjiG33r/uHWlF9thQF8bJ77rgVVSjv3wskTU4/8xwuOv8cxicC1X9ycpJGo7EnbUJKRU9PD0NDQwwNDjE8NMRAdYBqtYdUHYgRzE9IV1KQOEFcq1LoH7gw1RLr1YIimd9jEz1UaCEcqKgQZoPlU2tCe57F4MHHFIXCpFmrPjd1b5JlaKVZUq3isgxd6UEovUfb376NwD9sI+gk6mO7xrn4oifxwt97gWu06gwyuPcKrhAY%0Aa+nAmh1eS39/P6tXLGfdvfeivO3QMEhaTfdPn/zUH512+hlXnnPOWSDDiU2O6QXOWmDnHn74kVx66aX/8od/8Ic/n5urz1TKVaxNSLOEJEnVS178ym+/9GV/WAnvLUMRIYTuJpFKaYSWNOfmeM973/WXD2zcfO2hy1ewM8tI0lBlbyXtb5RN9nYho9C9l2VBIME69IK8SInc03q1ADqPkEoTlcv4Frg4Qi1007tTsQsx1dqA2rThQU4+dhV9tQpb%0A+4fYXzERYGZ2lv7+PiqVSk6bN1hr9+DIyWAEAmNTpmem2Lp9CzOzM912wANdnSx81grGE8vo+KxIMp7rhcYJjUPjvAofH2J5ES1YGiMiHAWkKqBVjPCqG3UrBJESYaSpMbc7xwNSalxcxDiPD4PEw+gh/9gtZx2NeoMnX3IxL3vlS3HOxVlm9p85LIBqOzT0tUcdRalUIopjorxfe3BwiNnZ2Z9deuml79y+fQcy3/yd8Mp3Gmlyvv0TnvikgVe/%0A+lWX4SFNUqT0tFoNzj7rzHe85a1vOgtJPmS92xnchSBFFDzThy790BU3/vJXHx4ZGiKxGUQFyj19lGsDZMZql2Zo8mEd3oX+i9wi/Tyms9vBshDS9mgRoG1cQPekd6gwQ7O7nM0oFKNTlyxZImaSmE1bRylEioM58CzXIpUkSZPNWzZy3/334J3nkMMPCQ94f39LytAwLR2Rgw1THoWlGsuaiMousyKIV3Rm+u4nuxF7KPvk7YFuYQFaIvJQwJoM%0AkGRZmqioRBTFeGco9UUsXXEMjXYWYLUDvGlyN66OzQ2pC5hJtbjukB9erWaTM845g3KpzM4dOw8oZt39ajTqLF++lMMOO5SdO0cXnXZRFHHttde++xOf+KfHv//9771IKYl1AY5dWGN3JkPpmL94wxufdf0N17/uyit+9olyucbq1avPevtfveWdKw9ZijPh3uGDPqgMDWFdQ/zPH/5o9FOf/tTznbUU44iGcYw30lCBNYZSsbRLImaVkDVjM7x3%0ACGdz/pfYr26qz3WlOoRCtyAcWsTFyoPoQqX8IinFq72KrY0kZZOStRt4oQ6eEczj2ZpIRwg5fzLtnzUhMMLipaNi4ThvuXcuIpG6gvcFZQyqczbsZWiFfAgjCH3NURgjKgRKhaYL0YkD8sFAUkGrUb9nbmwMoiLDNU2tsoSegQHc9Cy5SMQBkqz0gkGBYM1iI9B67/MFiqUCl19+OQCHHXaY73bo7SHI5rstmXsmcpaBgX6WLl3Kvffe1+3t7myO%0AQqHAZZdd9vsnnXTSg7/7uy8sxlrhvMG74M07OLr0jp5aL+9617s+fv+6Dd/dtHHLtj95+8s+9+SnPkmE2kgQNlYqwmQWa02Y6iMFDz6wno9+9KMv6+3rm9VKEWuFzTyxd0gBPnJIIbZi7NVxrJ6Z2pzVIWRXb3QPgqHf86izuX6VUnK3+ed+EYbiPEglGnP1udC4FcWYuEbSaFLQbkHw9SiNIIeNaDab3arjATd6SIEVDi8d0wZKbcMSGbEli7Y3%0ACswpqUt+AX9795uk/L6pys57pNR4L8L8K6BYriAiHdTqfGjcwFkc7rqoWKJaLTDSI5iYmqOVwtxco9u8cqBMw4XddZ22yX19feHrbjabfPnLX+bJT37yzIUXXpgj3C73ZQvANDc/sGTh7/Lek6YZZ5xxBnfccSetVmuR5pDWmnq9vuMTn/jES0466aR/PfrotUFyxs/riCql8C4MxjjzrHP5s9e9/qvXX3/jtle+8uXHB55YO+9/dggRwgrnHLEO%0AYgEfvPTSv9u0adOPVqwMUHqsBI2JNpHNKOSy9Fma0Ko3m+Vif3cuwEIh3gMs23Y/PGTKKSGzVoxt3xKmrnWGB0qNE46ydI+610ADFIvFobVr1z6uv7+/2mq1EiHEgfcUSCGtcMJLR2wRqmmJhJw9qzZwsR8aGFAsPgmc362hwe3bCHxHUxTJ+OQUOyem2DE2QdsanNQ4HwaFOJMQF4tu8JBV9Lo5TGuucMQxR71h1epDT5qdrW/x3qcHOsNISRkv%0A5JZYaxNPZ0jynl/f7bX76enpSEp5Qq1W2yspTOSJNHixt3FOjUaDlSuXs3r1Kn79698QCl/z31Or1bj99tu/9Z73vOfLn/3sZ15cLpdQWmFtoEtIKfP+aJAq4lWvfvWFz3/+C+kfHCTLGt3QB8Kgbp17foCPf/zjv778B5e/dWhoiKnpqZxXpai3FArXrVBjHVIIGeXD/FxXUMsfcNjZqaoLvW/5FAEY7/AKUe3tla5jBLkkjBWC1KVEWTMYwiPM%0AEzTAmjVrnvr2t7/9shNPPHGfsoD7hKGExEiDEI6SAZX50EVULJEWNM56lFhQJ/YPXY/d433k4fzt9zzArffex2e/cBnTO8aJq73dHEEBLrPWmZRCDImQR7/qVa/6wDOe+dzgBeaf/H4Lwt7vhlAIsae79vuCdHPoMv+/zBiiXIFhIT/Rhg6u0tzc3B5dTp2/sWr1Ku64465u/NwNI3JFumuuueZPvvjFL5/3mte8cg1CI9V8XK2kxHlPlrYoFEus%0APGQZzpow16WTDOQcsU5ifvPNN/OZz3zm+TIXDDbGoKSnngjq7Yhc8DsPETNskpqOUBr4vG314bEJnHdI/9Cb1zkPAlXp6Yms9+kijy7A0wMtDY0ZUPuDMh/aE8iO0tl+Fc/2+vB1+NNxWJ2NWergZI/mys/cspIUnUdbR+Qtyju8UKTSo5wjbTSn0iyh3R9jlEqKxWoGRFr/9/QT+CwEy9a7bvN5506YzHD3nXdefdddd9Pb27vH3YyiiHY7oX+g%0AtodEC0Ch0EuWZdknP/UPf3j8scdfe8FF58oQG5swdF0plFB4p3GZCQrg0hOLYtBOkoEYJ0TA3cfHx/mbv/mbV9Tr9Y39XcoFFCLFjpk6c7MzRFp1N7lN2pT7akmPsnjjkV6j0CiCyMG8XE2uhtd97/OUekmGFi5Aqw/FmbOgXdTtWBOLT6TA1i304qzHJ7P5EAvx8I3AOWdtYG9JZ+x8suoJf+ShqnJyYTXZzfPzc9iUR5vBK3DG4bI2IkuQLn99%0A3oMkn4AC1pjUGktiDMaYlrU+BaIw9MDumxFrD9AKHyZpqtNGobTC4RY9wJmZGW64/oYr1q1bR19f357P3gXOT1wo7l0JQ4JTsHP7rhs+9HcfeefaI9e+Z+mKoe7X6IqHqcC8dTbnRARJepMPJpF58/qll/7d92666abPDQ4OdsMzJQWtxDDbbIE3LKz6e5vicM6LML41KIXks4z9Imhj76IGuSK1yNvr2Y8hKC+9t9773RjB3YKckIhCOEx8Mhvo%0AOSJQJiA0N/nJqS6zdg9JnH0lrAd8UHuLwiIJp3Pg+y/ooPCPcrkM6TO0MEgV+gA6E0/84iHSsoO6SCGk914cED1LHuS1kHYk86ksObolckrI5d///oZNmzbdNDIyQqlU2mOVK2UKhRibgUkkNl28TCIRLqZaHuC66657/8f+4R+u89ajVAEp9YI40nUZmPMGZgNOr4LW53/8x79v++rXvvriarUaZCWNDXwsa5mut8hSu5cRUwJAOOcDhSkX3HIL%0AhbceMnLwOfLjD2gMle/SqPe2OuGdRcQ1RKGXWAlirSjGYZbM1q1bUUpRLBYpl8t7hKCPLK1eQLeW3oYX4BcMw2DBC+yQ8h+xIYSTXOd4dvecE4t5Pz5cB8aGWChO+1iHRd4vqj1s2rCRyy677C8GBgbqg4OD9Pb27nX19/ejY4UnC4NGdl/CoiNBuVJ0X/vaV1/yzW/8a7gtMlqwQ+xeboXsVqnvu/du/v7v//5PlJSzcRwF+nTecOO9pZEGeU2Z%0AQ+ZdNEt25sbZbs5yQDObF8wwmM+tDo6ogwSa7QSEpFYpUC1G9FfLSOG5/oYbu4LRw8PDewjM6b09tAVSkoE8F6bmBTgt/7oklMuVduEbXU5DRODFvDqx6MjzHRhndk/XmA+ScyLHlvMbaJzDCIsmUH+11kXyFjrnZCREPvl7IVMjhxVsLiQsZWg9XOwexUNn7vt57aFRR+CsCQUzT9cDTIxP8IY3vOGdN9100/eWLl3KrgXKdns7/6wNPRMLhX53%0AR9KiSDE9Pf3ghz/ykd87/sTjv3H8Ccfl097yUKNzGHXDtFB7MSblfe9737tu+uWNP162fDnOZt1brwQ0Mk9iQReiPU5KpzRSSiGFzD2z7W7sRfDx7jUCvxgBPLBh5x7vndRaK5zcK9QtpWByao4j1hzCkqrjrjvvoFgqd3lqHa3YzmyNveYED2W53jusnZ/S7rxH5yxHpaP501/J3cjEB+sKTeI2KhOVe/BKYbwjEj4njoU4MYqiopAi59IItVcY%0AMzei7vPQ0UEa87Cn3Sgd5ylNuG9XX3WVu+xLl73jhhtu+IDWeg8S197OAikFSu2/1bxcKXH77bd9893ves/5X/ji519bqZYRNgXpu2rR3deWz2D7yEc+fNO3vvWtv80VsndLwxx1F2NFRLQXcpcLgmRiUf9418GKh4D6Hsm9FDjnXJqmxu4FJEAKJsdmWDLUzx8+74nc9uububmdUiyVD/hv6P2/hHwQdk4fsMYwOz0dZhD4PH3PTz8hFdaqoIQs%0A8yBZqwOhK+/776uQ4Iw32kzX2yTWd4eHKwEmx6dLpWLFWocQTbzbR9/eAn1+awytZpN2llHPq5EeqFaraK3mCXdePgxH4LvwYoDTHXfeccfYTTfddMVPf/rTDwgh7li5ciUPPvjgAdAqfA5h7v/vSyHo7x/kJz/+6es/9cnPnP+mv/yLE0MC3EQu1GsI2vP84PL/mPzQ333oWcaYPSBx7z1KClyxQjGKu6OzFj8VTxRF0lnXHb8kcgz/IMeSgTDp%0AvZ2dnTHO+0X5iReCxuQMg0sG+aMXPIW+nipTMw2kfATo0D4P7hzfllHE2I4d1OfmKBaLvPvd7/7BT3/y048fcujqiohkxVqXeJRyKOm8lMYLUe3pPeGwQw99cysfli3yuMTtjzu0SMcnyDIKKZmZnmFqcpbZepNSqYzB5omfDtr8UsokaRF3EbJ9vCXnUIUCY2NjfOIfP36zl7zj7HPPZXJiAq013/jGN9i8efM8b+cAjcA5R7FYZOXKlZTLZQ4/%0A/HB27drFfffdd/vMzMyOnp4eSqUSc3NzBwQ6CwSFuIwQ6oBAi7hapN0ccz+/8uf3vfKVLz+x1ldFyNDkL3NvSm54V1xxxXfTNN151FFHdSUT51kwnsQrptKIaC+cGYEgk0Hz1fl5z4rPtaIWEOj2/RTEHhSZh/QE3vt2O+jVdcIt58E16zzutGN58tOeQqUYMzk997ANYJ47hM+EEG5hotwJpS0eJRzOpGStJu1GnVKxcP3WbVt/vGHrVvr6KmRZ%0AwOwdCiElrdkGRL1P3Hhy882ZbYVCTV5NdPuZWL74xnisDHlG2OiaQrGEzKdf4ggPWEMrS44an9jBoSMDFJHK+1TsLS4NU+UFJsvYum3rhquuuurHK1et4qijjmJ6epp7772XB9ZvII7UwzaCarWKMYa+vj6WLVtGvV5nZGSky8h1Bzyt3YOMyIIa2YH9iAOvJaoQF1OT07AzHV5/p1aTHzC9vQNTZ519DsNDw3u4y0KkmW22uP7O9V0vs7unKJVL%0ARHGplDmJ9mFacaosqQoyZsW8x8R7QSoEygu0n5cmUD7FC0eiU4pERFbvNVoQ3uORGJzIUiMAnJRBccN4SBsce9RqlowMsXXrtjBl6ZFWjAErhNhH51Wu+Cw8kZK0Wk0OWb2q9vo/ey3f+/4PmG3N0ttbwiLxaKZnEyAh7uk5IS73oEURY9IuTv5woVjT6XEWIJG5IQU2jrJgcikSFRfOLZZrxEphMif3JzAqpWRoaKicpRmf+dSnefVrX8vRxx5D%0Ab28v5VJhvoj1MIygUqnQ09NDpVLpTsB8uBX4zpt1QmGsOaDRSp0rMRkiUr47R8LrBfDoQhU7WbDWhRB3L0bwwMYtjG7dRFQo7BHWG2Mol8ulvpHVh7c7gxZF0BYN2k+g83sWgtKc4LZgl/vO90uBFwvhwz0DQoTDONsTV8qBpyQ1Lm1w6Op+iq6XnbumaLbaj2ym8wGFQ7ulup0xQjMzMwwPDfF7L3ohX/ra10iTjEKphEfRaDuELiG8+75PGx92%0ApaJKWcAUfJjeyneadvyCyS3CLaAPO7xXxMVStd1usYuMgXJk9teu09m4td4aSZLwmU9/mle99jXUajUOphbrIwiEQYTNK/EPK7mUezA29zZ4lodEZpxzJBZKtQEqpcJevm7ROjrKOneKIzTnCyUWKVctDodseBLCdwMNSxHvI3AFbOTJRLt7ZokFSJb3EAmJSLO7e/r6HFEB5S1udoaRgRJFH5Ok5lHf8UfEKVA6CEBt2rIDmwmkiIhVRKlcwYqY%0ArWNzZNZMu6TpXKVHWeFzJbGHX5RTohPkO+a1KnIxJwEREdZalFTHZMYe2crE/U4UjReY+fcnHtIQBgYGqM/N8cV/+TwT01MUF6hF/7eYgdQ87BlLB7GuUS6XKVdSCoVoLzmVJY4LQiqVP1Oxn+DW5ENU5je3EwrvBZIwmCRbgCcGLlMe9wsoSnCtxpfq9VlXjSMqrsWczWhnHuEyCuXywTEC7/GdcGihvvxCjaSFXVC9tZr9xXW/5IZf3RbCEhXo%0AtNY5RmoFTJawa2xqwiTNy2PJcxPpwuSZ3choe09cFzeRSySqUyPIx/cIlU+czBMxZz1IVe4bWnKiMu378SIKPGH22cSTF7BEJ0TrqdWCGLF1XfnuR+NiH60X4BF6o0V1wH0NDXko6xJg0pR2q450e3oCk2Wo3uhYqVRgLAiwzi4CUhZXhl0Y7xXw2Zx2YYkkxD4DU0CK4m4AXocIqJE2A5+qik0ZsgYrHPYgo1B6PvQXe72jSimczbD5BiwWC1x7%0A3Q18+9+/R3/fcJQmrczajGKxQFwsK6WUxvlMusS1ZybuKw0OEaVZPntg/zmB382daxGRpRlSSVQUBZ1LJwMvP9e9xztUXACpzzBp9m9Rpaq883sWy/ZEocRCjxBFEcNDQ0RRxOjYrjzEkP/7vIB4JJW+cO+VDEVPb8PM4N29BFJTKFWea1zoeFFa431Q9Q60lQUTizpojiPXHwpGIK3FNxvYmTGkquJlcb7eJOb3gFIFEjXHzNTOuhubYjKOaQlH%0APWtS66lSchLrg6J3vV4niiKmpqYYHR0lSRKazSbLli1jamqKubk5jDHU63VardbDCIdyd6eUpBDFzCRTtJM2p5162kuXLD/03GpPbQSXjQspjI7iclwsVKXUOs1MJvH1SqW8VlT6Mfl8KinlPqufCzfjwlNBe0GSGbwUzDTb/PSaXzA+O4cXMc46tBeBI4+iXK4+cXTnVqZi1Vad4p0QB3yqeu8xxtCfJ8WjY7tA/Fd5hEfvBR7tFceaqbkGm7bt%0AJG23MWZxUm+NIS4Wi8Vy9bCWDeGpyynUAoHKhcQ63jyMwg0wqvC5uK6DFcMDvOBpF9NsNfCyBOhuoCs6WkneoYQmlS2a9bPfGM2a5xR1LIzGz5FSLZUoWlBxxODgAL21GlprnvzkJ3PGGWeIMEvb0dPT46219PX356qInrGxsS8C/7abEexbj8SYDK0E/cPDlKs9YDNOPvPs5Ui9/L/yAW3eOc4vf3M71/7yRozNkFGMQyCUQnuJd5ZioXBcakV/%0AO80mVSQdoLAWHqqfYI9h0440y+jrrQGenbvGu+HT/7pcYN8hu997Yg3t1CILFfp7ehe31gpwxlKI46O9c6c6myOH5LwiFFma4J3B2jQPLyTWguoAI17gUsvSwT7e8qbX58jV7vwmOY/54jEoNPJY4Nh9vc12mqJEMJxCXNjr93QOX6UU5XL59D2MQEld8HnXsvd+XipROiICJiukoljtyxUNRGfaHosk2P2C9+D3crs73y/oMFsCAW9vVd3uDwf+%0ATatRx2RZUCWwjgiIhKCAIZOeaSmoRH2lcmn4ea3xse/jijIkVxK5UC3Ai7wLRyCFEkpo5F7o3s54BnsHkUKzY3TnY2wIHiF0kBNzj9wCpCfME+7Ur3RQgxBdRq4HKZDeF2Su+LDo552noASmbTGJWVwj8OCspbii/3fmygXStE3VOITMsBK0k7TbgqYtMt7K6xTeoJxBqACkeCnwOrBNQ03LLX4ue4RyotOpslgcdrc9Vty9B8bvGRourNAXCgW7%0ARzgkhVIsYpT6rjUKrwI+63wY1RnYdAtR55AC+RyetouxUNutDs7LbDs5P3xau930JXaf/KLy0Z8+SKgEqY5Q/3HeI73FK0mCoCAVfb19T5ka33UNVqRAwXdgw90GTef9qkJItY9yvyezloGc779952NsCEI/ar6V6DzEzntUgPV7iCVIfCTxe7SoayXYunWc1sQ0hUKEW9jvbQ2RjvrjUvXP54TA6dBa6clwTqCIaTRSZtqGLRPT1KdbVPvi0NzT%0A0Z1yITlWUoQmGUB6sbgH2++ez+R1hIU3xy48MP2eM2D2dXPyMVnWmNm9VYz3ASiL3EAXZv370JQRu0vtdho7snm77ign2E5fAAhX3EdS11G6SEF4lLco/IK9LHFKhukoQhA7Dz6j0ls+I+2vPN/apgsGvhvWvsBG/YL/9nVCp1lGf8cQRkcfA0PIvYA48JFR+4uA9nkidiFKMifYQ07HSWjU2xSkpBLrRd7ZtDPKvT3nlys95aY3eKfJhEaS4l1Q%0ArfPWMT4xxqatm7j3wQc5/dTjwgFmJftM1YVbLGkiDtDaF+099v3ze7+laq/o0AHd1AN5fbttEOnz+BDV7W4SeLBiwZCGhRtg93Aon2rpbWgH7EBtQoWQXwQpwKIUaC8p9fYc6pcO/ZX3RoXfJhdt8i4NPMDA3gvxkEO0vfckxoTECtj5WBiCeAxbQMWeRuD3MhnA5164VCshKxGiFC3+mi7St3TkZf3DIxTimG1btjKXtSlrgRQqPMUoYro1xz0P%0APMDVN/ySo445gp5SAZIFLmqPHoIDaMM5UHBjH9+3P7vSeQhSYh8NNg/XCBbi/CJXzQrKZGG4npRR0A7tsBNVthuOuXsPKSA1qAjvg7qx6GoP5iM7PGivKJYKRLGk7IZ6XKe3eFFfvOg2cwgpkUrJ3WVP9nUZaxkcGkJIyY4dOw6SIXiUioJ8uz84g+mU0nHndXnnO/JMizJlD5lzfpEubKQUs/UWm8Z3kamURgfFE5C0WvQPLxnsO/TQ0/uXLmdJ%0ApUa1XGN8apz21Bjp3Bw6EhgsUsHmXTu5+qabWXn4YbzwkscjCyG0tjbLJ9SEUVY4g6cAIp4X5FoIo3fUK/ZblFtU0FrkKBbFJ/nXvV/MhelMtJeVSqi8qd104x+9xldfd2tH8YLf+jAPv1LvEKrUg0XhpUIq1Z1BrD2YzCIrMbJWoVIrUhxZlv8puVfr7u3vQ0e61my1KJYPrEKcZimVaoWh4WEmJyb22gT/cC/rBDbLDooRBJ6Sp1ar5kax95sc%0AF+JYaBkEt/KrWCqw6cFxpndOo4oFDCZsJuuw1jByyKF/VzvssJWqVMOmhqHBAaojvTC3hOb4JJu2bw0AipYI6bl34yY+9fmvsHXLBl74nEtYPbwcpXZHbjJETvVeOGHzse75q1Qq/XsYwfj4+M4777wLgSdN0tCYHfqjMPKRumqBd5YIQ7k2RBqVMTLCuMASibxDYXFi3+pL3oMmQkn45W338pOfX8tUo4XUcZh3nKfXoaIMxgp0bz89PSV2tj0b%0At09RdE3mWvW8SytIlpRKJe69917uvOuuL0VxtN/axe4eodxToVGv0263H5HcYsdLWidI0/SgPXRrLbt2jX7xlltuvWTpskHSLEF4T5amHc+H0ppt27fPNBoNpnNUxQNpGrF+4w5IHHahuEK7Sd+a1WcfdtZZL7PlXlIjg2Cas******************************+yme//G1+cvV1XHTB+Zx96qksW7GMWqmAcyYwu5OUmYlpVKT2DAYe7bkg%0ABMK6blckUlIsldi0afOORd/mvef4408qHnfccWccdsiqkaTdDhPkc8lv8wjjVe89Wiu0TbnngW1DpSWH/GOqK3HiBIEbmqJchnCF3XKC3QBS4bHOMDo+xezcHE4HvglS46XA+hbaKLQrMSsUQyceyeFHr2GpkNz/nz+/1je2vP+0M04oCwdzjRZKSYrFIuvXr9+wcePGXx84vXlh3iOY2DW+yAg6LNK1a9dSq9U48cQTGR0dZW5ujsnJye4Q782b%0AN7Nx40ZKpSLOQWYO3gmXZRmrV6/m9NNPf8LwSH//5s0bhHeO5cuX+yRNkUIQFwv84tprr9q6detEZzaClOEA2T7hsTYUH/GBLGfrdZ704j+4Z+mF5xy9YWIG3XL0GY+WlrmoSdWVmNwwyj0P3oNXDiEJ0o2tBC0Eke7BtCz4jL5alaVDAxS0JNIeqSPfGt3wscNXDl5nRRw4YAsOlc49O1Bv2znkuooSAoT1COe6CobVag833XLzLVdcecXGxRCp%0AlO1CIf5FHMd453BWdo1APoqkrVYt88tbfsNPr/0Vhx3X+qit9JN5gbEGRUYkDM4WeMgKkbdY79A6Qhfyk2uB9xDEOAlWGVzWpL1rG30nHIMslVHHHLPqxx//7o/G5qZ50uPOY8XyJbRaCdZ7isUicRzTarUedkijlUR598hVMJ1DOkcmFGmrgfKgKpVHHRJ1Nk0cxz8rl8skSYLJsm6DkBCCOC7QGRElO70aMugkDfYolJpnoqb1Br1nPe4Nhzz+%0AyUdPzEwSpZJYx2Q+I7UO7atkaYsdu7agZQAsAowNUsZgLe2khSoW0arMrEmZ2rIFDRSExGd2bvOGB9/oRcy5pxxJO2/sT1JDmmZB6v+gGIEPfe9AFEeLpC0XJsaL1QS87PZmPxLujPeeSqnIbKPFz26+CxN44/9RLJde5IwFp1BeBIVnt78pIFHAo70PJYh8okp3gjoalMeQUSx4mmM7mN62nZHTz+D4Jz3xkMntd3/6pq/8y6vuv2cjZ51xEqed%0Acjwrly9l2xb1sBitWgiED+hUIzG0ZExGRpQmSK2CvKJ3FKSgGGt6KmWm8nCjEEVkWYoS4XtQinahzIx1LD3xdJjYRWNsNG9RFY86z+ioW8RxjMr/LXf7uHBZY8E5SsouCkeydJa1xx35/KWHrKBx6wTlzOJwEEmEjIlkxOTEduYa00ilUYCSUXhGIrRGIsFrg5MOJKgoQhpPqVhmdnxqpjFT5yfX387m0VEqxYgjDlnBsuEBRob6mZmrY50/YACi%0Ak1R3czWRCz0wbwS7q44DB7/P3AOR1hTiiO//7DqSJKVQLNKcnf1mUYYXpfJBzt4rNDJfYq9L2EDDlszreC5+iJ0iUZjHlTnPffffj2m1GYiLXPT0Z7+idvgJF7XqE1z18+v55699j7se3EKxGFMuRERK7GVJpAgbXwuB8o5GmtEQms0u4p4tk4zGPcyWavQcfixJdQkJEUZFTKSW0ekGD2zawtzMDAWtmJiZJfUw1U6pR0WiJctJ4iJZq8mS089l%0A5VOegY49hVK8WLPpv+ASAtI0o91u00qyRctFBTbdcccbrv7CJz+W7dyYDFUEtaLAZ20kBmNbjI7u6FZzne+wAfIJnmFAObGMQkOUDxM8w8BFS73ZVuVyRrmUcfu6Tdx85/1872c38vV//zE3/voOlFQM9feFfnb32N2Tgw5QC6BcKnLjrXdz/4ObqJZLeO9oNWb/w7Tm7i+o0pGJy4trUmOtZ18TgQOfJvzWzqbfPWyaV1MIMV8cx4zu3MF9d93D%0AWQMj9K1aIy/8nRd99fv/9OlDRamY1TPL577+A9YeuoTBkmSubfdy8gYFuNl2gtOaUt8gO6cbpInHlKq4bBvaO7xSFEaWYxsOkzlmyzV+Nd7ATW7j+rvWUy0WOOSQpdx69z2sPe54Ih3RlhoVx4i0HgRnkzZaCVQkqfX3kPghZnfszKsbgv/OSymNteamm6695qZqpD597Jlnf2jk8GOeWYwKIDzbd+5genqaio5wiOB1AicA5z2RCh6yM39Z5vMI%0AfJh2w+CSoaX9vSf8oNmY+ZHSjStN0rqnGMdMTE1y+RW/4Dd33sdpJx7DScceRbWvxPTsHAcIlj48z3lQvYD3lItFpubq/OS6X6EijYw0Uke0Wi3fmJ74ek+s8ykr+Q7PG7P3OUdYduoCnSrvvmeWCw8FHaGl4o5bb2Xn5q0YFXPGxZcsP+miJ/yjHx1D5dNc1j2wjRvu3cF9o03W7Wxwf/7x3i2z3Lt1kgkjmLGC6czQiEuoYhlhDXFwRx1UAZe0%0AEc4EPR/vkUKgJERxkdnMs2V6DmSAaZUUSB+kCxdCYN67vL7ikdU+GiI+yLI1+9kE+bgHtdsS3lEoFukbXkK9PnffNd/51rN++K2vvc6ZNloKRrdvQ1iXe+FcFp6O4JgIMjxC5EJ4OVsAgcnFG5Be6mrf0/qWrf74ikPX3L1s5SFfKhYLa8vFAgO9PYxNTPEfP76Kz33tO9x57wP0VCuPSaPRQTUCpcM4ju//+Ock9TpFJbFJgstSXJYxMzZ2DyYh%0AVpooKmKFygfa7RmndtfC2rHfh7GI+TcjHFTiInPT09zyq1+RNlOiai+X/O7vvmrg6KOfYZ2hN1ZUCoqK9FSFA5diTRvvMypLagwsGww0bSmIhEA5C84eUKzeGcMkBcRKUor0Xgdy7CuW9CajjWaOKBjWY7j5PaClwPowMrW920q9pD5Xpz03R5pYaLWoFIsPnHDc8UgP9akZSlEcUH6pMEEHtsvTcbkld/luHWlKAZl3GG9JHDSMxMdlBpaueMmS%0AZcs/L7WWXkp6aj0MDw0yPj3LV7/3YzZu3cHIYB+PBNH7LzEC7z2FKGKu2WbX5AzVapVCXKAQhVUulfHOfSNL2z+IuqNtO//Y/9qnkcj5ryMDfGkzQyEqsP7++7n3pl/jMkPfmkN4xstf+s1CqXgCzQZF5yjhKXkXpB5dBliKtRKV/p7QrfbfFYbgSdDUiR8zQ/BAQUmaiWXnrGOirRhvi0VrwsQ8cOc6prdsxSUZ0aFHjbzo5a/4dq1a447f/AZt%0AfMiZpEAtGNYhpESoeeysq8otFj6rsAwCg8ZLhXGOyanpXzaSxBkkmfOkzlPuqSJ0xE+u+SVTM3OUS8WDmjbJg2MAIRmO44grbvw1JipSHhxEV3vQPWHFvX34OGZ6auqrSshQj0MFlyn9vE/eY4W6gM+RhkXCt7IjEzmfIAvCRJdCoYhAcfNV17J1wyYaGo694Kzys5737O8nWUYiIVMKE+mgzpwLnDpjsZn5r5IqfUhDaKMeE0PoGEDLWDZNtjDe%0ABaG/vQkM6zzOTFMufsqTP3/siadUr/nFdYzt3EVPoYJ2QQTNO5srT3RQmPnRmnJBTufzz3f+LWTevYjE4UdHx0c/12gn1Ntt5vI122yhI82me+/niht/TU+lfFCfz0ExAikFcRzxq1/fyT13rkMlbdLpWdKZxcvMNZjdufObNBtbikWNEaEJW6AQSKSfX8pLFJJIWGJhiQg69dJESBuhVREpovwnRc7EjMEJIinQLqU39rQbY1x35X+SjE1js5iz%0An/+iQ45/xjP+pS4ts0VNWqugCvnk+f9h12NhCB6IpaeROTZONXDKoEoeFTtUYc8lIgMkHHPeea97+nNe+PT77tvITTf+hkJUAJ+CVqRIEuswhFqB1hFKanROmjR5o73wAunCEl6Al2Q4MplRjQWtXTu+3K7X7ykWSkRSLVpKSKKBfu64dz3bR8cplwoHLT2QB+Om6kiD91x33S34pI3KDL6d7LFEamhPT7v6xK53FWPphbD5mIHcCBb8J5AoL/CY%0AMJTOCLwVKCIiXQhkOgdKyFywS3XZpd4aNBblU2rViK0P3s/1P7qSrGkw5X6e9yev/ONjTz31Y35i4uHNZ/sfYQj+YXrpgLNXigVqlRID1SIZEbumU0RUQsdFUPE+VhE/l1IcGDziRS992UdTH/GT//w51kEcFxDCYUUIWTLnyKxDCIlWEQIVYFFE3pcQNr7yAuk7KIdAahUq1GmL1sT4FwaLJQZLJQYKxT3Wklovvplw5z0PUCwUDpp3fNQQqQBK%0AccSv73mQltJUh4dDTLiv0pcrMzU3+8Vio/6pUlyI5ikae+UnkMqYNLMMDgwwNDzM+Og4M5PjFAoKhAvGsHvFudvbIIk8DNf6uOfuu6gMDHDBhRdQ6x3kxa/+8z//ZDNtb7n91rfpuADG4H0M/wMNomMIEFMlkOQOxC8476mWSxQizYZdY6zbsJU2ETtbBVARhWKRVquBF2Ggu0Askk93SQalvt4X/cnrrjx07XH6i1/7N7aP7qC3WkG4lEIc00gy%0ArPFdkbBOMcrlChQdWHTvAAIIa6iUC8yNjn1vpt2+W0ZxMJp9PIeoWuE3923guGOOpLdappkkj9oYHpUn8B7KxQKTM3P88OfX0zIGIwUt7/a5EiGYnJmxs2M7vtsTBTkVmQdFC5NdIQSpNbS9ZumaI1h25BH0Ll3KwPKlECuMTRDCdId1dET+FqNGIrQcGkePjrj5hl9y069+zVw7pTi8khe/8a1vPfbsc95vpqeR5TJSqRy7/Z/uEdinR/A5TFut%0AlBnqr7FjbIJv/OBnfPHHv+Dqm+/itge2084caIm1hmKpRKlUDkXIHOYNrZQO0jZP+J0XfOGCS567+oqrb+Due9dRKldwxqAleBfQIJsjZ1EUKAm7q4rsC9TwCIpaUfAZs9OTb3Ja46MIp9U+V1wuMd5occf9GyiX4oPiDR6VEQgBlVKRX915H2ljjkJBYWyGc+YhVoaXMDm26w00GuPFuJD3wOY4spL5hvboSHHIEUdw2JHHEJerzCUp1YF+hpYt%0AIRMOx/y8Ahbc2IWrw9WJBcTOceO113HLr39NMzX0LVvNH/zZX7z9tKde8s+FwaEA6z0MRul/uyG4QHfoLmspxTGlYoG77lvPtb+5lxtuf4Db716HlpJCpUR/Tzmo6HuPyVJWHraGNYcfTiEuoqRAS4fPgKbllCc84VPPfdGLn3vbXev42bXXgxDEUlCKNVpKWq1WUKXOJ/EsNIC90RM6RtqlNihFUUpaY+MbszR9oFguEkWKKNL7XDpS9PdWufPB%0AzeycmKFUiB91bvCojKBYiNm8fZTb716HLJbCqev9fldBRzRn57ZP79j21qIKrYUqF+u11gYxJ63o6e0lkrn8RiRRsabtLENLl1Op9mOsDAQrKQN9V6kgb7NgeRGKNlIIegoamTS48eqruPWmm0kbLeLBpbzgNX/xytPPPuerbnqadqu1R3fc/zhD8JKGLOLLPbhypbtkrY/RRpMf/eJ6vnH5T7j7wW3ouEhPtYyU+9eADmGMx41PsOaE4//fi/8/%0A9s48yK6rvvOfs9zt7b1qsxZLsi0jb9iEsA3EIQngBAZCtokDCRlXApnghAxQU0lqqlKZqZkklUkIExigEiYhUFk8ZIFMIDjxVIwBG4wNlmytre5W78vrfvt9995zzvxxX7ckyzLIWI4XnapTqi6V+l29e37nt31/3+9/eM87V9Y7fOZv/oZ+t03oCXxpweUwiw1q9g0DOBvsdqbDf74RbIL3hMR3js7qyhuzLMOmKebb2FrA8kqdR45OUIjCby04%0Af6mMwFrLUK3C1Nw87aUlKr4ksBmBNd9yhy4jko7G0sIfp3G85GsP4cSmEEgez+ZAs1Z9lebyPFHgExVy9REdFdmxex9SR5iBgJ9zLv83j+svJFpipEAIh+cMNV/jJX2+ePfnePgr99JJHNS28YM//Y6fvO2OO+6XWl3TXFvbHDd8Ni6JIxMKE0Q4P9zcolBkqdnh+PRpPK2oFCO+FVP52ZykzjmS1VV23HD9z7zrff/pNzpG8em7/oJuY5mRcoRH%0AgkeGzRL6cQ8zkKv1PA/f958QnPf4veEtALTv06wvf6y3tnzIMwmi30N+G1vEXSrS8sgjh1haWSX6DitFTzkxDn2fmfllDp1chPI4fRVe5Jt0NJOUhYWl37li3/7fsdZg0z7az+v2iTM02i2qYUh7eZFybZhCdZjMOOJWm9rQGDt2xsxNHENKhxO5dq86R2ROkCqQzhFmDt9ClvWpeZqetdx/z93M2QIveeUrGasN8dqfvv2l2/ZufeDzn/jET67M%0Arvy9VyqhA/2sNASBQ1hzXtLpa4WvL45LNQ9hoL+4wM7rrvmhO3/9Vz+uShX+8pP/h8WZGWpFD0xMICwmTTBxjDEpBonyPcIwOKcSdfYzDeQ7BkqVbhOJq3wftDbTs3PvNb0EZRUXhYkQgvrcIvc/cpQ3vfbf0Iv7z7wRDNVK3Hv3A0x+8xBUt9BrPgUK8iRleeL4/9hWC98X1EbGYxmQWId0Ai9z0I3JtIfJDAuzs+ytVKhVStSThLifMr51J+1W%0Ai7XVFUKtcykKa3HCYXEgTU4TbvMyXSIcKJ2DvYTGl3D0Xz6Lbc7xstd8P2ZsjP2vektlfPzAZ+/7u7v+4L67//6XZCGiuIVndRn1O8vr8jC0u7rM1utuede7fu3Xf98fGedP/+xTTE+dZEulhJfmjTCRCdJ+RpbkdIxWaaJChO/7g2qQOAfG4hD4NkE6S6J8EuFjcXjOUlOShRNHf7VdbzdFUOOi5YwciGiEL3/zFDcd2M9wrUI/SZ+5cEgIQZJm%0AJGmCkD5KZEhpLnorX6JdatdnJm41/VbDabCeh0WjnaakQ3rdGGMcvU6T5YV5PK0YqlWQWtM3ht1XH6BYGyFJLcpJROZyasbBhL02Au3yJDlTikxrMqGwSqH9gB3SsPi1r/CFuz7NxJEpuonP0P7reMvPv/POt7373V+r1apvaq3Xc7Je556VJdTvpLJhrWVleYmbvvulv/v+3/ytD6nyVv/jf/oXTE9OMVT288EqNAqPOM7odfoIPJzwCAoFgigc%0ASKyKTRqbzXM6UDf1XH44rZQYFL7n49qtxfrE8d8OspRCmlBI4ovf0pHOz3Li1CzVcukpU+o/JU8glaTV7jG7sEBhvEQYeE/5PTg8lhvNR8Xi/PvLV+7/CGmGQZIpTYpAupik1SKqVlien6dcqTE6Mk6WZDSbbYSCnVfuYSKJabXalAIf50yuZ+zEJoHUZjfhLHkhISXaRAwpyerUKf75Lz/OS179Cq578Q0Uy0UOvvFHbtl64Pq/ffC+L/3dvf/4%0Aud/pdzpfFH54hoXvOW4ApttldXGBV77me/7sTW/5sdtXY8unPvmnLC3OMVTMewEbczadXpduv4vSkiRLCKIixTDKEbEXOHvCWYzywXkgNNoZQqUoSMvkySNviE2MFwYI8dQrcjL0mJg6Tbt73UC51F56T+CcoxxFzC2vsNxcR4caq3jK2ykQgWZ1ZeGjtt34fFErlLMgJSkSTwhkZkk7PWzcZ/rkSVrtFpXRIaJKgW6aEg1X2Ll/HyIKiW2WH24U%0AWqjzhnAen9w7FaFVQM0XRGmDr/7zZ/n8Z/6aYxOnqSeakatfyq0/8o43vf19v3bvza//wY/pNH0Z7QZZu/WE6ujPhfDHOQf1FWQY7Ln1jW++5y0//fO3H5tf5Y8/8SmWl5eplMqILCW0IC20Oh06cQ+hFFYJCuUShUJhk1XzyW5gIzSp8nBCIp2jEiga86c/sLK88JDyNcLLm/1PdeuSz/TsHAvLq/iefmY8gdaaXr/PFx98iCTtEYV6szv4ndSo%0A4l7M4qljP7f74C2PZkoV+2mKUz7WGLSAXqdL0QtIuz0mTx7nqoMHGRkdxQpJu92mMjzMnmuuYurYcTpxj6LnI6zFSpeD7C7Q7W76EmkzPJdRkA5tLLOHDrN0usWBm17GDTe+mNrYELtvfhU7b3zxHTe/8hV3PHrfF//h0Fcf+N3m1OQ/tVstkjQd6DWLZ70BJElKp9Vg68Hrf+In7/zFP7z6Ja8Yvvver/Cle++j2+pSiQrILMUXCmVzcuJ+0kdI%0AiVMSKSSlagWpFGma5aVo9yTyfFJiEGiRg/ayZn1x5tSxX3ZaDULf78ypKj+gW1/jxPQs+3btoN3tXXojEEKQZBmNZpNQa5x5eugSfF+zXl+drs5Pv29o+54PWQtWuU0+UmkccbtJKKo0V+vMTZ9m3zVXUasOYRJLp9ehNjqGc5aJI4/RSWICqXINA/fE4YsAOjr/u4LNKcU1mvGoQNKNOXL3PSwcP8H+F1/HvoPXEA0XueLm72LXjTe84aUT3/+G%0Aow8//PWVQ4/9UWN+6XPr9fpEiiOtVJFZhgjFvxrF+oWqP721NYQRO15x6/d+4Id+4sffKooRn/703/DQw48gHVTCAO0ytHBIFN1ej26vh6cViTF4WlEqlzE4nLU5L5bNodIX9gR5xUilGUVPdCdPn3p9Zgxa66dJ5xhQik63R5blJML2UhtBFPgcPj5Bt91Fao172lAGAqU0c6dOfDgMCz9UG7nittVOl0waBAqlFVlq6LfaBNUqc5NTCGDP3msY%0AHx1jcSmjk8QMbRnnSmeZPn6cJMvwnMux7hf4AiNrMdKB8DEbZFWZINCGMHKsLBznvrtPMXHkG+y55gB7D1xFuVZmbO8Btlx14Gbz8vmblyYmsmNHj3x2emryxOTk1F3dJFmqLy+vCu01S9UgbyxdAqCeHfDtf6v8LUn6xL0eO1508B23/du3/vZ3/ZvvHZ2YnOaf/uozTJ2eoBD5OSrUJHgDfYRmu0uaJIN5YJuLsJSKyMAD68jSDEU+S2Cf9K3m%0A/FLVYsDq9MT7l5YWHvaC8GnNqHSpxImZuZxSR+Y6XZfMCDbHJ9fX6TVaVEdHcE/j8IkSgrTfZ2F2+ud2FcoPF8NgtOUEqXV4UhEGHklm6DVaRLUqc6em8WWR3fv3MjI6ynJ9iXavx+i2rSgpmDpxDNProzz/XMLgs8iMh5IMJyBWPn2p6IsMP7B4WRffTyn4CmUy1qZPUp+YZeprh9l74Bp2XLWb0a2jxNu3Urtyp37197zmzb2lRVozp99bn55l%0A5sTx+WZ97c976/N3dUJ/znRaC+1OK27XV+j1enQ6XWxjjTiOEQray0tIk6G2lkE+OYZpox4f+P5gkutJbuIsI4qKW1/7hts+dOuP/7u3JDrk7794Hw99+auYZsxwqYyv0pxwTeYcPe1uTGoN+CHCGIpK4ZWLGC8ffJFC5EReZmO4flPOe/PLFS7fShlKxZDG4uKfTJ449ocCsFn6tF4GWsDa4iKTs3Ncu3cPcb9xUU7mooxAKUmr02VyeQ0KZZLB%0AeOTT6rpDj7X15qyZmPjlK6+96c98LcniFDUodQorMcrS664RlEJmpiawwrHjyh2Mjo6wvLpCu99neHwc3/eYPHKcXreHrxS+yNVSrMvpAq1zZCJv5Qjn0M7kN5u1JFJjB6KBnhDo0AMjaKzM8OCXZnjkoZBdu3dRvno/ozuvYLRcpVQaY+iGbey+Aa5P+9vajeZ7OvWF98TdDvX62mS73T68s9vrdprtuNPppP1eLxvbut0hDNfffocaHxpRJ77x%0A6N2nT0x/SoTBk35PWZrhBwGVanWDu+MJ30U/7lOplHd93+tue8v0bIt/vPcfmZ2eYtjLGCtnJM6QOYlWjiwz9OOEvk0RWuHIEJ7CL0VY4bCmPwh9cpCjFQ7jcsoc7VIkA8lZpUnwiNGUfEXaaq4dO/zoz8RxjyCIMFn29J4ZIYibbY5OTHNw/96nrGj/ba3A81hcXePU1DzogNReGoyNNYKV+cVPRtHUd2/Ze8W7E6GQQpImOQW4VI406eJ6CYWo%0AyNTJ4ziVsWP3LoaHh2nU1+h0Y0q1EfZdF3Lq6Al6a3U8KfC0JHMGY3LxiFSrgTCJRTuHM/nMsxUh1oGyAg04YUFbvFI+T9vrNDl66CHE8QkK1WFGxofZtnMXI9u2UByuERYLiFKZysgYw57HDuv24NijnMNlOb+nEJJ0fZVUZBS3jDNeKPLXv/e7Lz907xc/pYqFC4ZPAuj1YpbiLlvLRbJOJ9cqxCHUue9EeD4rjcYDH/ufH3yPP37N79XXu4yE%0ABSLXRtPDSJ9USOIspd/vgwTpCazN8vHYQpiHiwOyMTEoCeUSxQ6cQli12cUe8P8jPE0QRWhjmHj40Pf11pqoqEh6qUC6hQqHT87wqvoaoe+RXAS130UZQbEQ8uj93yRbWodqhSxNuWQrSTg9cezOai3yqsOj72x0U0yoSXIdXXwRYXsJsWlSqNSYPnGcLEvYfeWV6OER6it12r0Ev1DkquuvY+bESVbnZ/EzCMMB055LkJwRnttAneZwJIuCXF3l%0AbPUUJ8kyS1QsYa2hn0GvXWdifYmTx4/ghQEjoyMUKxWqwzVGaqNEQURUKhIGOfub7/koT2MBX2sCrUkaa7Q6bXylVxnga84H8rl8OktIKuM1ekrSz1Kk7dBKJZ1OH9vsDMZP5UCUQoAxLExO//41t7g37dq269Zm3CU1jp6qgnKQJmSJydUihUTofORRq/yWF26AxZIDfNZZpNHaZeAMmfRICHLhFOnwyKjKvps8dOjn68tLXxeBf8kLABfSZn5a%0AjUDAgATpGah6KI12MP/o8Xfpq+wri9u2Xb+WGVIrsThCNIGQ9JOYfnMVr1hkbvIUJs3YuXsvQyPj1FfWaCcJoe+x8+prCMpF5idPEnfaFIs5U5pN7eZhkxvKU2dpP5gncL2QK7tLKYk8Cc5S0DoPr9KY+uxpFqczUBJf+AReSFCI8HwP4Wn8IMALfKTWKONwEnq2T1lpFk+cSIQaCBw+DtYtEGRZQlArUhyqEDfaWGMphgG2oHFhSOCHJFlGr9PC%0AJH0wJp/rzTKaM8dvv2KsctwKXez7BRIZQdYC69Da2+yl5BTvuYe01uYzwRuiBo9TxJDkhMJ9EZCICOugqC01P82mH33wB+anZu7xowKZTS75kZFPkb3v4hLjwQc9I91SKfJLqt3l+OFHXrrbFw8WrrjiRUm3j4mznCoyZwQkTXpIICxXWZyZI40Ne/ZdTW14CNdq0ut1sUKwbddOSuUCp0+doNtYw/MUvqdyXpzBAA7ufHrgczWdzwWJCZsnlVLI%0AwZC/JBQShIdxFlKBy1J66z3a1pIN5KrMIDNXVuGUIFWWsufj1huqUCyh9PldeGkdiRZYPRh64Sz484C7qFKrkQqfoZ2KzuIknThFO0fa69Koz8+vL039t6E9L/ovK6lHP5F4TuLJfCTybKp5a91g0mzAIvEE+hFuUB1yxmKtw2mJkpJyAMsTj713burUPUFUxZlnAJq+gQDQ6qK9wbMXOD9o7ftRRJKm8akjh2/JVhb/X01rfCGwxuQvRgqk9Eji%0AhF6jRegEjZUVJo49Rr/TohR5VKIQoaDT71GqDXHg4PVs37UX4zx6aYoRFivA6Vwf2QyYLJ5Y2vncqSk3IPNyzuIGs225VKlFOdAyP2SB7xGFIaUopByEVIOIchRRiiIKQUgpKhCFAUEQaA+JFuduH0FQLhPtuGIg7ucukDCneb4hJUEgCF90HTuuOUgQ+MRewGOn5/7rcqNx0tiMwBNEUYDnbTCDn7vdIPy70BYoUgOJsSjnCEXKSOBYPfXYR+ZO%0AT31A+9GgKmefkUvTZhndXv+ixVOe3UYwuHmDMCRN03jm8OHbw2ZzYsT3kcJCoIhtXo3Q2sOkKf1mC98auq01po49Sm91mXKg8mkoreglCYkT7Nl/LVcfuJHy0DAdk9InZ0tIsdiB/rQhBzc+md+zSmKUxiiVlzaR5Clkvq10WGkHIDObs9Q5h3YO7XJDkUiEA+UEnlLS93wCzyfwPALPI/IDjLP0C0VUFJ4XJj3h92ZtTrSLAifppRYrQsTItpdX%0AduzyCpWQyEsQLsM4QZZlg1D32/fywjoy6WO8gMiHUS+lN3PkPTMTR9/ZbLVJMkGSJWSkuQCjdJdsq1DTWVnhkWMnKRWi55cRbBiCF4YkaTZ39MEHD7p261h5uEhPp1gZ4PBxTuBLjTAZcauB6XVIex0WTp1kaXoSXzhCP8g1DYSm006p1bZy1XUvYufeXejAo5P0SGyWQ7EFmyqbT7aMUqRKYZTCSEUqJeasnSlHpixGbngKmyMznUVbO2BekJtn%0AT0uRM5TkYHAQgtRkrMedvHZv7LfVad3wWN1uh059hZKnbnrZ62/7zC/+6n/+0ut+9Ed3+QWfrDUPWY7SzfOA83UizvY5wp3ZeSHIkgqNKFbxNTRnT35gbWHm96X2/3WO1iCHudhgXfNcWc7hBQG99U58+KGv37zvxhu/Vh3ZemDNtYmzPpHyyHoJgc4Zj5POOsp46EKVtdU6zW6Hka07KBbLZEaSOkPL5IwJO3bvZ2hkG0uLS6wsLNJPDaGUYCxK%0A5/XwCw0nyoEE7YWkBzd+Eo/TBz1z1FKUSxFZ3nNxQXFkPe4Uk263g/byJpazoDTq2zz8WWZIWi3ixirBtn2vvep7f+Dttxw88PZ9N76ElbV1Hvn6N2nVO2QEeV6iZd7wsm5QIxObMb/GIBwYITDK5R5ucAFbYyh4ZYrSY+30yQ+vL87/shcUkbH51wHZbpD+Pm+NYMMQwpA06XeOP/jwTbsP3HB/aU/1xk43xWYC6XlkLr9BpRDYNKbdkYSFKlm3%0Ay/LsDJWhcYqVKtr3yKwlzRxp5pBBkV37rmZ8fBuzJ0/SXlkj0h5WWMSTaIgod37yfM57OQ9cJs8aPBEIGSOsQZowdwhRdK0XRa9Jkvj/oiRBqYBNUtJ+/KQH3xhDrxvT6DbxSuWhsT27f+bG73/1q/e85JVvHt7/IrrNJvfeex9HHnmE1cVlQs9H6RKGFCXMQBSR84xdDnpwRuRU66kzOTGd0kRRgSKO2UPf/GCzuXRnoRQhnoMQc/2ce2Ln8PyQ%0AXq/Xnzhx+JZ9wb4Pbtmy9V2tviXRYJwEK5FWoZ0jSRJayQrFUhklJauLs7Qa6wyPjuAHBYywA5ZsSz8xKCVQvgYvz/+stQNB8QtXzC66jHFuvQ2XQzFBWKTJ8LWUPQXOxoyMbqNRb5F2O+eFOkmS0ut2SXrLGL8gR8e2vH7fngNvve6lL3/r3uturga1LSzPneKBz3+OyckpVpeWwUIUhLk6DiCUxtj0ArGyJRU5D5ATBqzDE47Q0/gOhEmYOHb4%0AD+pzS79UHa0ihH1GqueXjWCQIyit0X5gVmZmfyFbXz80vu/qD64rZC+1SDy0VTgDWqZYkZJ0W8S9LsVSBdtzLM91qVSrRNUhjNI4LGHoUW/UWa6voIUjw6CVyhtEl2hZp3IZWvIhFIlDSmt14LNjx3bSzJAmCSQJSZLgXEZvaWFQQrZbt27Z+Yo9u266defB675v64GDB4rbriTJFCcnFznyL/ewOvkwSWsBayXFASOIGxBlOWPyzONJwqxMSIxQ%0ASCcQLsVHUpACeq3Zk6dO/fDy7MID1cLQc3r8VPMcXlJKtO+xODf3oU6SfHV47+4/r9VG93baKcJJpFFYkZNRYRxSWbqNBlGxiBd4NFeWaLQ6VIZHKJUiTJpSX6sPhAslmTUXjUO52MPghNosrVrrENIh/KCze9d+tm4Z5oF//gJJ1xaRlD1JZev27a+46qp9N2zbs+fGq66++jXbt+xT5eEx1l3K1PICD375ARZOTbM+NY/sGrwgIwoDrCGHhTiH%0AtSbvPMu88eie4PnPLgNbB9pZIq0oSUdj/vQnlxdn352i1/wges7PXz+njWDDEJTnMzd3+qsdm1y744r+/xqqjb0jdZI0zvIKtZUDmsH8JXfbLWRXEvgBngxpLC0RtwOQ0KrX0VKihMRX8pJTtG9ITQnnsFhi69i+98Dbx7Zs/a6iNtte8QNvDIa2XPHW0S1bxmrDY6o6voXi8Bipg2anxYmZNjMPPczppSnW1+dIWmv4WY8xz6MaONalpu98lLCY%0AAdODHCjI2AvELhvhlrUOoQy+hJJUBKZPY3HugyePP3an52uC4XG6zd5z/Qg9941gM0H1A/qdTjJz5NGf3b5954NDO/b8nip5XjvWA5a6M9mrFApsjrBMkzUKxRIphk67Bf0UT0kCpRCZGUw+XWqpjLy2ZIFe6njVq77nZ2+68XpMmhEOjZOSP2uz2ebU6QWWvv4Iq2vrNJoN1uod0jjDV5ZAGyqexvMDlMjomhQzQM46cUa8joGQ3YYRnp28n8ND%0AhCBQhsgD0e1MTh09+lNxr3ufH4QoLZ7yYPtlI7hk1bHcvSulWJo9/YdL9ZUvb7/22r/0ylv2Jf0Mk6WIQWVjU/7JGVyc0svaOJ1X5gPtg80QxiBtzrLsJJcs4RMuR6jmqi4Kozxm6h3E9Ar19Q7d5jdIuk0ajXWyfp9+t4vtJzhjCXyfsp8iipYgU+hc34q+EjSkplf0KaeSoJ9hhcg/43H1f+3OUpIh13lgMITkFyICurQXpj6+NDP/C/1mKw6K%0AFZxNeU5mwM93Izg7PBKex3qz8fV4cuLA+FD6kZHayM+K0KOdGpyQZMaSGoMWiiB0ODKstUgJEoHDgJW4gc7v2YOw5zmF79hL2IEfyH9P5HtMHn+ME0cOkyERJkYP4Mu+0nhKIAOFln7eHBI5ZsZJh0HmFFdO5VCLDKTJDZ+BlKkQeYxvN9mnNZmVg36dQ5LPbVSjAr1mO11cmXhdc33hHmElQVR4XvIvPe+MYFB9x/N9PO1nrZnpf5/Oz3x0aMfW%0A/1jesuVH+07SdQZPaHCSRPQBi1ISJRTWOKSQGCE2p88ej5x82pMCBlh8Z1G4nBQXm1OReB4b6p0brSzyPH9AIBDhbI7td2Kjo+uIEkvR5f1pBkQDuZJkTkkpcHlp2AmE8JDKYm1GIfAJjGFtauIvFk7N/vduv/twYUcBgcFmCc9HCrLnpRGc8QoCFYa0Guv3rxx97Me2NBs/Vxvb8v7RodF9cZKRJhmZ1APdXUECCNQZKYyzxKCfKfPNP1eCkud1%0AFB7fbh7gPc+K7c/yLwKscLhNfhLyvIBNzAPIXLPZJX2KUZSzbyydPja7uHDH4uzivaFXQns+guf30rwAltZ5d3h1fu6j7Wbrj8bG2u+tDg3/SjEqjHdRxCYX0M4T6FxIbiDLjnbp4KA9945CPg+hMEINdAccApOD+KRACUXBl2gt6K6vTCwvzt2xsjx/j0wzlOejtfe8iv0veFnyAllCCLwwRHmemT09/VuPPfzg/tXJ4x+W/ZhqFFDQEo9cJ01I%0Am8/QGrfZWNqonDwdz/Fk++n4/Wc/64aSj5ACBWgkoZKUfE1BSvpry6fmTh551+SJo1evry7fg9J4Qfi85V59wXqCxyfOXhDQrtdbM1OTv+CtNT8+OlK7szw0fFu1NDRshKSTpRir0ErgjMvVLUUON3bPcpKtjecTQuSzDjZF49AaAuUItMKlMZ3l+WONRusPlubnPhb3OkltbBueH2Cz5FnDl3TZCC71f9zz8AOPNE2+OjNx7G1BoRKNDg+/vVit%0A/kqhNnK1DDWZMfSFIjPmnMP1bPd4G3ycSinKvszJtATYXqfdXFz+21a78+H1laX7pPZwUuEHIVJKnsUiPZeN4FIupTUuCHFC9Jr11Y8sLS18pDwy9rKwWHhDrTbydn9o6x7hJDZLcVk2gBycK5vnNkk5z88czjSonriK9fjUd3CSN8MZuVnVOTct2ajz5yhPsanXZp1Dag/PDxBaI+Im/fX6yWZj/YNry8ufoB/Xw0oN6floz0f2E8wL/Ay84I1g%0A42xJKdFKI4F+knxlaXHhK/OFpd+o1hZ/uFwovrk0VPtBFRVrTnv0rSXFYaxDZoMB/bwVfYaxYsDC6DuLdoOfrTsz+ufyOs3GrS3EQOPLObBnnqmLJVMCPcA/bRjghvpzqh2ZNIRSEyqFcg6ZpaSNpRNrq/X/HSf9z7Qb6990SZfMOKqFAlrrF1TMf9kInkI4obVGez7WYZOV+bvq/fiulSiKonLlbWF1aLtfG3qbioKtKggLnufjjMMYO6gh5eXI%0AzNrNLq20eTdaKJnLSw1udiHAitwoNqe2EGf+tALrKRJnsTh8rQcEZA6tFIHw6PspcdZBJp1Gv9Nb6q2t/kncbX/BdroPyDQjK5aRXo4dIkkvv+DLRnCRVRwp0TrCOMiQvfWV1Y+GrS792fnf8Eul4VoY/Hg/8EeCYumnAj/c5sD3wjCQyssZ2xBYLUmdGwzHnwmbnMsZGqzIS7GSwVSUtbkMtsqH/svWUHE5x5wUBhvHCGszF9t2uxcvJ93GnyTC%0ALp9eWftkJETHxl2spwi0j1IeRmvEs1SW9rIRPMcMQkqJ9H28MKSXpC7FrTYX5j+kI5+m5/1mv5dUtu/YUW7GnRtsZm4plssiDILbE+1vxfOV73naOZsKIcpn5wKGvDchAWHAWtuTDlzqbJoZ46XdLwiTfqMb91Wn3VoqVoa/kiR2bWri5GK5XOn6aYyrVjADFU8vDMk2xkFfYFWey0bwDC4pZQ7WCwJ0ENAH+lnWzBzN9eXl2bTX+Qc3Ng6l8mfn%0A11sjRnletVKOhBAdpdT1WuvX5QxvCqdzDqM0y3CZXe4nyV+ZLHNJmmSdVjcZrwb3R5pGc61BY72ODgoY/IE2gMaPIlKtkVl6Oc5/qpecu3xbXF4v9Evt8ldweV02gsvr8nqBr/8/ACmNXXRMNZkpAAAAAElFTkSuQmCC"
style="padding-top:20px;padding-right:10px;float:bottom;float:right"> <span
        id="header-title">Platform Initialization Package Release Notes</span><br>
      <br>
      <br>
      <span style="font-size:20pt">Turin</span> (AMD EPYC™ CPU Family
      1Ah Model 10h-1Fh)<br>
      Package Name: TurinPI-SP5_*******.zip<br>
      Version: *******<br>
      Release Date: June 30th, 2025<br>
      The AMD Secure Processor Technology is referred to in this
      document as Platform Security Processor (PSP)<br>
    </div>
    <!-- TABLE of CONTENTS    --> <span id="toc-title">Table of
      Contents:</span>
    <ul>
      <li><a class="toc-link"
href="file:///C:/Users/<USER>/AppData/Local/Microsoft/Windows/INetCache/Content.Outlook/T5RVY5JM/Updated_PI_Release_Notes%20-%20Internal%20Draft%20only%20not%20for%20external%20sharing.html#specs">Documentation





          Specifications</a></li>
      <li><a class="toc-link"
href="file:///C:/Users/<USER>/AppData/Local/Microsoft/Windows/INetCache/Content.Outlook/T5RVY5JM/Updated_PI_Release_Notes%20-%20Internal%20Draft%20only%20not%20for%20external%20sharing.html#package">Package





          Contents</a></li>
      <li><a class="toc-link"
href="file:///C:/Users/<USER>/AppData/Local/Microsoft/Windows/INetCache/Content.Outlook/T5RVY5JM/Updated_PI_Release_Notes%20-%20Internal%20Draft%20only%20not%20for%20external%20sharing.html#tools">Tools</a></li>
      <li><a class="toc-link"
href="file:///C:/Users/<USER>/AppData/Local/Microsoft/Windows/INetCache/Content.Outlook/T5RVY5JM/Updated_PI_Release_Notes%20-%20Internal%20Draft%20only%20not%20for%20external%20sharing.html#issues">Known





          Issues</a></li>
      <!-- LINKS TO PI CHANGES -->
      <li><a class="toc-link"
href="file:///C:/Users/<USER>/AppData/Local/Microsoft/Windows/INetCache/Content.Outlook/T5RVY5JM/Updated_PI_Release_Notes%20-%20Internal%20Draft%20only%20not%20for%20external%20sharing.html#changes">Changes





          for this release</a></li>
      <ul>
        <li><a class="toc-link"
href="file:///C:/Users/<USER>/AppData/Local/Microsoft/Windows/INetCache/Content.Outlook/T5RVY5JM/Updated_PI_Release_Notes%20-%20Internal%20Draft%20only%20not%20for%20external%20sharing.html#abl">AGESA





            Boot Loader (ABL)</a></li>
        <li><a class="toc-link"
href="file:///C:/Users/<USER>/AppData/Local/Microsoft/Windows/INetCache/Content.Outlook/T5RVY5JM/Updated_PI_Release_Notes%20-%20Internal%20Draft%20only%20not%20for%20external%20sharing.html#cbs">Common





            BIOS Setup Options (CBS)</a></li>
        <li><a class="toc-link"
href="file:///C:/Users/<USER>/AppData/Local/Microsoft/Windows/INetCache/Content.Outlook/T5RVY5JM/Updated_PI_Release_Notes%20-%20Internal%20Draft%20only%20not%20for%20external%20sharing.html#cpm">Common





            Platform Module (CPM)</a></li>
        <li><a class="toc-link"
href="file:///C:/Users/<USER>/AppData/Local/Microsoft/Windows/INetCache/Content.Outlook/T5RVY5JM/Updated_PI_Release_Notes%20-%20Internal%20Draft%20only%20not%20for%20external%20sharing.html#psp">Platform





            Security Processor (PSP)</a></li>
        <li><a class="toc-link"
href="file:///C:/Users/<USER>/AppData/Local/Microsoft/Windows/INetCache/Content.Outlook/T5RVY5JM/Updated_PI_Release_Notes%20-%20Internal%20Draft%20only%20not%20for%20external%20sharing.html#smu">System





            Management Unit (SMU)</a></li>
        <li><a class="toc-link"
href="file:///C:/Users/<USER>/AppData/Local/Microsoft/Windows/INetCache/Content.Outlook/T5RVY5JM/Updated_PI_Release_Notes%20-%20Internal%20Draft%20only%20not%20for%20external%20sharing.html#dxio">Multiprocessor





            Input Output (MPIO)</a></li>
        <li><a class="toc-link"
href="file:///C:/Users/<USER>/AppData/Local/Microsoft/Windows/INetCache/Content.Outlook/T5RVY5JM/Updated_PI_Release_Notes%20-%20Internal%20Draft%20only%20not%20for%20external%20sharing.html#agesa">AMD





            Generic Encapsulated Software Architecture (AGESA)</a></li>
      </ul>
      <!-- END OF LINKS TO PI CHANGES -->
    </ul>
    <!-- DOCUMENTATION SPECIFICATIONS --> <a name="specs">
      <h2 class="subheader">Documentation Specifications</h2>
    </a>
    <div class="table" id="Specifications">
      <div class="row title">
        <div class="column pid">PID </div>
        <div class="column version">Version</div>
        <div class="column desc">Document Name</div>
      </div>
      <div class="row">
        <div class="column pid">57918 </div>
        <div class="column version">1.10</div>
        <div class="column desc">AMD Generic Encapsulated Software
          Architecture (AGESA™) Interface Specification Overview (NDA)</div>
      </div>
      <div class="row">
        <div class="column pid">58145 </div>
        <div class="column version">*******</div>
        <div class="column desc">AMD Generic Encapsulated Software
          Architecture (AGESA™) Platform Interface Specification for
          Family 1Ah Models 00h-0Fh And 10h-1Fh</div>
      </div>
      <div class="row">
        <div class="column pid">N/A </div>
        <div class="column version">N/A</div>
        <div class="column desc">Revision Guide for AMD Family 1Ah
          Models 10h-1Fh Processors<br>
          Unavailable at this time.</div>
      </div>
      <div class="row">
        <div class="column pid">55730 </div>
        <div class="column version">1.23</div>
        <div class="column desc">Common Platform Module Implementation
          Guide</div>
      </div>
      <div class="row">
        <div class="column pid">57299 </div>
        <div class="column version">2.00</div>
        <div class="column desc">AMD Platform Security Processor BIOS
          Implementation Guide for Server EPYC Processors</div>
      </div>
      <div class="row">
        <div class="column pid">57238<br>
          57238<br>
          57238<br>
          57238<br>
          57883<br>
          57883<br>
        </div>
        <div class="column version">0.76<br>
          0.53<br>
          0.59<br>
          0.33<br>
          0.53<br>
          0.60</div>
        <div class="column desc">Processor Programming Reference (PPR)
          for AMD Family 1Ah Model 00h, Revision A0 (Turin) Processors.<br>
          Processor Programming Reference (PPR) for AMD Family 1Ah Model
          01h, Revision B0 (Turin) Processors.<br>
          Processor Programming Reference (PPR) for AMD Family 1Ah Model
          02h, Revision C0 (Turin) Processors.<br>
          Processor Programming Reference (PPR) for AMD Family 1Ah Model
          02h, Revision C1 (Turin) Processors.<br>
          Processor Programming Reference (PPR) for AMD Family 1Ah Model
          10h, Revision A0 (Turin Dense) Processors.<br>
          Processor Programming Reference (PPR) for AMD Family 1Ah Model
          11h, Revision B0 (Turin Dense) Processors.<br>
          Please contact your AMD representative for access.</div>
      </div>
    </div>
    <!-- PI PACKAGE CONTENTS --> <a name="package">
      <h2 class="subheader">Package Contents</h2>
    </a>
    <div class="table" id="contents">
      <div class="row title">
        <div class="column pkg">Package </div>
        <div class="column desc">Document Name</div>
      </div>
      <div class="row">
        <div class="column cont_title"> ABL </div>
        <div class="column desc"> AGESA Boot Loader Version
          RABLBRH1006F010 </div>
      </div>
      <div class="row">
        <div class="column cont_title"> ABL RT Driver </div>
        <div class="column desc"> Version: 0x10.0x06.0xF0.0x10 </div>
      </div>
      <div class="row">
        <div class="column cont_title"> CPU S3 Image </div>
        <div class="column desc"> Turin A0:0x00.0x00.0x00.0x38<br>
          Turin B0:0x00.0x00.0x00.0x39<br>
          Turin C0:0x00.0x00.0x00.0x2C<br>
          Turin C1:0x00.0x00.0x00.0x0A<br>
          Turin Dense A0:0x00.0x00.0x00.0x0E<br>
          Turin Dense B0:0x00.0x00.0x00.0x16 </div>
      </div>
      <div class="row">
        <div class="column cont_title"> AgesaModulePkg &amp; AgesaPkg </div>
        <div class="column desc"> AGESA v9 ******* for Family 1Ah Model
          10h-1Fh platform solution </div>
      </div>
      <div class="row">
        <div class="column cont_title"> AmdCpmPkg </div>
        <div class="column desc"> Common Platform Module </div>
      </div>
      <div class="row">
        <div class="column cont_title"> AmdCbsPkg </div>
        <div class="column desc"> Common BIOS Setup Options </div>
      </div>
      <div class="row">
        <div class="column cont_title"> Platform BIOS </div>
        <div class="column desc"> Onyx: ROXT1006C.tar.gz<br>
          Galena: RGAT1006C.tar.gz<br>
          Chalupa: RCHT1006C.tar.gz<br>
          Quartz: RQZT1006C.tar.gz<br>
          Ruby: RRRT1006C.tar.gz<br>
          Titanite: RTIT1006C.tar.gz<br>
          Purico: RPUT1006C.tar.gz<br>
          Volcano: RVOT1006C.tar.gz<br>
          SimNow Onyx: SROXT1006C.fd<br>
          SimNow Quartz: SRQZT1006C.fd </div>
      </div>
      <div class="row">
        <div class="column cont_title"> SMU FW </div>
        <div class="column desc"> Version: 5E.7B.00 </div>
      </div>
      <div class="row">
        <div class="column cont_title"> SMU Dense FW </div>
        <div class="column desc"> Version: 63.7B.00 </div>
      </div>
      <div class="row">
        <div class="column cont_title"> MPIO FW </div>
        <div class="column desc"> Version: 0x01.0x00.0x1B.0x00 </div>
      </div>
      <div class="row">
        <div class="column cont_title"> PSP Secure OS Loader </div>
        <div class="column desc"> Version: 0x00.0x3D.0x00.0x77 </div>
      </div>
      <div class="row">
        <div class="column cont_title"> PSP Secure OS Loader (Stage 2) </div>
        <div class="column desc"> Version: 0x00.0x3D.0x00.0x77 </div>
      </div>
      <div class="row">
        <div class="column cont_title"> PSP Secure OS </div>
        <div class="column desc"> Version: 0x00.0x3D.0x00.0x77 </div>
      </div>
      <div class="row">
        <div class="column cont_title"> PSP Drivers </div>
        <div class="column desc">
          <ul>
            <li>System Driver</li>
            <li>Boot Driver</li>
            <li>SOC Driver</li>
            <li>Debug Driver</li>
            <li>Interface Driver</li>
            <li>RAS Driver</li>
            <li>FHP Driver</li>
            <li>SPDM Driver</li>
          </ul>
        </div>
      </div>
      <div class="row">
        <div class="column cont_title"> SEV Driver </div>
        <div class="column desc"> Version: 0x01.0x01.0x37.0x41<br>
          <sub>(Note: SEV is architecturally a PSP Driver, but due to
            backwards compatibility, it does NOT inherit it's version
            number from the TOS framework like other drivers.)</sub> </div>
      </div>
      <div class="row">
        <div class="column cont_title"> Security Patch Level </div>
        <div class="column desc">4<br>
        </div>
      </div>
      <div class="row">
        <div class="column cont_title"> FIPS Overlay Binary (SRAM
          Firmware Ext) </div>
        <div class="column desc"> Version: 0x00.0x3D.0x03.0x06 </div>
      </div>
      <div class="row">
        <div class="column cont_title"> RAS Trusted Application </div>
        <div class="column desc"> Version: 0x00.0x3D.0x00.0x41 </div>
      </div>
      <div class="row">
        <div class="column cont_title"> Security Policy </div>
        <div class="column desc"> Version: 0x0E.0x11.0x40.0x59 </div>
      </div>
      <div class="row">
        <div class="column cont_title"> Security Policy Dense </div>
        <div class="column desc"> Version: 0x0E.0x11.0xE0.0x59 </div>
      </div>
      <div class="row">
        <div class="column cont_title"> GMI PHY </div>
        <div class="column desc"> Version: 0xBB.0x05.0x53.0x00 </div>
      </div>
      <div class="row">
        <div class="column cont_title"> FCH PHY </div>
        <div class="column desc"> Version: 0x00.0x01.0x49.0x00 </div>
      </div>
      <div class="row">
        <div class="column cont_title"> DF-RIB </div>
        <div class="column desc"> Version: 0x0B.0x00.0x05.0x1F </div>
      </div>
      <div class="row">
        <div class="column cont_title"> PM-MPDMA </div>
        <div class="column desc"> Version: 0x00.0x5E.0x1D.0x00 </div>
      </div>
      <div class="row">
        <div class="column cont_title"> TF-MPDMA </div>
        <div class="column desc"> Version: 0x00.0x5E.0x24.0x00 </div>
      </div>
      <div class="row">
        <div class="column cont_title"> PMU FW A0/B0 </div>
        <div class="column desc"> Version: 0x00.0x00.0x90.0x50 </div>
      </div>
      <div class="row">
        <div class="column cont_title"> PMU FW C0/Dense B0 </div>
        <div class="column desc"> Version: 0x00.0x00.0x9C.0x13 </div>
      </div>
      <div class="row">
        <div class="column cont_title"> eMCR FW C0 </div>
        <div class="column desc"> Version: 0x00.0x00.0xEC.0x13 </div>
      </div>
      <div class="row">
        <div class="column cont_title"> uCode </div>
        <div class="column desc"> A0: 0x0B00004D<br>
          B0: 0x0B001016<br>
          C0: 0x0B002032<br>
          C1: 0x0B002151<br>
          Dense A0: 0x0B10000F<br>
          Dense B0: 0x0B10104E </div>
      </div>
    </div>
    <div id="cve_data_section">
      <h2>CVEs Mitigated</h2>
      <table class="dataframe" border="1">
        <thead> <tr style="text-align: center;">
            <th>FWDEV #</th>
            <th>CVE #</th>
            <th>Module fixed</th>
          </tr>
        </thead> <tbody>
          <tr>
            <td>FWDEV-123560</td>
            <td>CVE-2025-29950</td>
            <td>CPM</td>
          </tr>
          <tr>
            <td>FWDEV-122497</td>
            <td>CVE-2025-48517</td>
            <td>SEV</td>
          </tr>
          <tr>
            <td>FWDEV-125991</td>
            <td>CVE-2025-29946</td>
            <td>SEV</td>
          </tr>
          <tr>
            <td>FWDEV-123315</td>
            <td>CVE-2025-29948</td>
            <td>SEV</td>
          </tr>
          <tr>
            <td>FWDEV-125263</td>
            <td>CVE-2025-0027</td>
            <td>SEV</td>
          </tr>
          <tr>
            <td>FWDEV-126573</td>
            <td>CVE-2025-29952</td>
            <td>SEV</td>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- TOOLS CATALOG --> <a name="tools">
      <h2 class="subheader">Tools</h2>
    </a>
    <div class="table" id="tools">
      <div class="row title">
        <div class="column tname">Tool </div>
        <div class="column desc">Version</div>
      </div>
      <div class="row">
        <div class="column ticket"> BIOS Test Suite NT </div>
        <div class="column desc"> *********** </div>
      </div>
      <div class="row">
        <div class="column ticket"> BIOS Test Suite POSIX </div>
        <div class="column desc"> ********** </div>
      </div>
    </div>
    <!-- KNOWN ISSUES -->
    <div id="known-issues-box"> <a name="issues">
        <h2 class="subheader">Known Issues</h2>
      </a>
      <div class="table" id="known_issues">
        <div class="row title">
          <div class="column ticket">Ticket Number </div>
          <div class="column desc">Description</div>
        </div>
        <div class="row">
          <div class="column ticket"> &nbsp; None</div>
          <div class="column desc"> &nbsp; <br>
          </div>
        </div>
        <br>
      </div>
    </div>
    <div id="Changes-box"><a name="changes">
        <h2 class="subheader">Changes for this release</h2>
      </a>
      <!-- ABL CHANGES -->
      <div id="abl-box"> <a name="abl">
          <h3>AGESA Boot Loader (ABL) Changes</h3>
        </a>
        <div class="table" id="ABL-changes">
          <div class="row title">
            <div class="column ticket">Ticket Number </div>
            <div class="column desc">Description</div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-127930 </div>
            <div class="column desc"> Turin WA DRAM CA/CS
              backside(QCA/QCS) margin issue for specific vendor</div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-127926 </div>
            <div class="column desc"> [Turin] DDR workaround 1b E-die CE
              errors at 6400 for specific vendor</div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-129299 </div>
            <div class="column desc"> Add APCB token to modify
              twrwrscdlr +2 clks @4800 MBPS for Specific Vendor 3DS
              256GB DIMM</div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-127209 </div>
            <div class="column desc"> [Turin] The root bridges in the
              same NBIO should have the same segment number </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-121651 </div>
            <div class="column desc"> Specific Vendor TMRS request for
              DDR5 D1b M-die 32Gb 3DS </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-132570 </div>
            <div class="column desc"> Release - RABLBRH1006F010 </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-124850 </div>
            <div class="column desc"> Release - RABLRST1005 </div>
          </div>
        </div>
      </div>
      <!-- CBS CHANGES -->
      <div id="cbs-box"> <a name="cbs">
          <h3>Common BIOS Setup Options (CBS) Changes</h3>
        </a>
        <div class="table" id="CBS-changes">
          <div class="row title">
            <div class="column ticket">Ticket Number </div>
            <div class="column desc">Description</div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-127432 </div>
            <div class="column desc"> [Turin] Change default value to
              Auto for CBS option Print Socket 1 PMU MsgBlock and Print
              Socket 1 PMU Training Log </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-127365 </div>
            <div class="column desc"> Move SEV-TIO CBS option to
              external option </div>
          </div>
        </div>
      </div>
      <!-- CPM CHANGES -->
      <div id="cpm-box"> <a name="cpm">
          <h3>Common Platform Module (CPM) Changes</h3>
        </a>
        <div class="table" id="CPM-changes">
          <div class="row title">
            <div class="column ticket">Ticket Number </div>
            <div class="column desc">Description</div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-128986 </div>
            <div class="column desc"> FW WA to detect OS retry count</div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-125296 </div>
            <div class="column desc"> [Turin] Dump CXL Component State
              Dump Log for maximum of 4K log size </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-117359 </div>
            <div class="column desc"> Chalupa system OverCurrent
              register programming is incorrect </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-120540 </div>
            <div class="column desc"> FW WA to detect OS retry count </div>
          </div>
        </div>
      </div>
      <!-- PSP CHANGES -->
      <div id="psp-box"> <a name="psp">
          <h3>Platform Security Processor (PSP) Changes</h3>
        </a>
        <div class="table" id="PSP-changes">
          <div class="row title">
            <div class="column ticket">Ticket Number </div>
            <div class="column desc">Description</div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-136934 </div>
            <div class="column desc"> PSP FW 00.3D.00.77 release</div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-135472 </div>
            <div class="column desc"> [SEV-TIO] Disable blockwise
              measurement report</div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-135169 </div>
            <div class="column desc"> Disable TIO Guest Message MMIO_CFG
            </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-130542 </div>
            <div class="column desc"> [SEV-TIO] Update spdm-lib </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-131222 </div>
            <div class="column desc"> Refresh Peer Cert Chain Address in
              spdm_ctX </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-134514 </div>
            <div class="column desc"> Add a check for tio around device
              table reclaim in shutdown </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-134824 </div>
            <div class="column desc"> [SEV/TIO] store digest of L1/L2
              frame as info.meas_digest </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-134199 </div>
            <div class="column desc"> Incorporate guest unique value
              into measurement req </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-79426 </div>
            <div class="column desc"> Validate the input parameters
              reamins same in susequent calls. </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-131319 </div>
            <div class="column desc"> [SEV-TIO] Re-init SPDM session on
              FW reload </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-134558 </div>
            <div class="column desc"> [SEV] Persistent mitigation vector
            </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-134300 </div>
            <div class="column desc"> [SEV] Prevent IOMMU RMP Writes
              until SPEs init </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-131873 </div>
            <div class="column desc"> [SEV-TIO] Secure clear of SPDM
              crypto contexts from memory </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-133601 </div>
            <div class="column desc"> [SEV-TIO] Compose proper L1/L2
              vector </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-128778 </div>
            <div class="column desc"> Add page state check after
              rmp_get_addr_entry_state() </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-132903 </div>
            <div class="column desc"> Change minimum size for out and
              scratch buffers </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-132613 </div>
            <div class="column desc"> Remove the call to save and check
              the DevCTX SLA's in both tdi_bind and connect command </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-133809 </div>
            <div class="column desc"> Add device specific information in
              scratch buffer header </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-134334 </div>
            <div class="column desc"> Initialize limit when disabling
              base&nbsp; </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-132616 </div>
            <div class="column desc"> Validate tdi_ctx first before any
              further process </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-132582 </div>
            <div class="column desc"> Validate dev_ctx first before any
              further process </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-132921 </div>
            <div class="column desc"> [SEV]GuestRequest Commands should
              support previous versions of messages (part 2) </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-128120 </div>
            <div class="column desc"> [SEV-TIO] Generate 256-bits
              entropy as IDE key KDF unique value </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-131724 </div>
            <div class="column desc"> [Turin] Customer co-signing code
              needs update. </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-130798 </div>
            <div class="column desc"> [SEV-TIO] Advertised minimum of 4K
              for scratch buffer does not work</div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-128121 </div>
            <div class="column desc"> [Turin]remove data_buffer_validate
            </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-132893 </div>
            <div class="column desc"> [SEV] Revert incompatible struct
              members. </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-131097 </div>
            <div class="column desc"> Unmap memory regardless of the
              fill_zeros() status </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-128252 </div>
            <div class="column desc"> [SEV-TIO] Inform guest when cert
              chain chainged via DEV_CERTS </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-132062 </div>
            <div class="column desc"> Remove copy back response data
              buffer when save data back to system memory in command
              process </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-126858 </div>
            <div class="column desc"> Generate unique IVs instead of
              incrementing </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-127199 </div>
            <div class="column desc"> Ineffective Secure Memory Clearing
              Implementation </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-131824 </div>
            <div class="column desc"> [TIO] SDTE Write should use guest
              device ID </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-131491 </div>
            <div class="column desc"> Reset all spdm managed buffers
              during disconnect </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-128095 </div>
            <div class="column desc"> Do SPDM Registration for all
              devices</div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-128507 </div>
            <div class="column desc"> [SDPM] Report ALIAS_CERT_CAP </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-126863 </div>
            <div class="column desc"> retire
              validate_and_copy_x86_to_asp </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-68346 </div>
            <div class="column desc"> [Turin] Post POC Handle
              interleaved TIO mailbox commands </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-130483 </div>
            <div class="column desc"> Do not validate Response data
              buffer for the first call to a command handler.</div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-73254 </div>
            <div class="column desc"> OS Crashes and becomes
              unrecoverable when SNP enables TIO in IOMMU with SME
              enabled in kernel </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-72311 </div>
            <div class="column desc"> clear the SLA pages when claimed </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-128678 </div>
            <div class="column desc"> [Turin]TDI report is signed with
              PEK </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-128533 </div>
            <div class="column desc"> remove RMP_ENTRY_VAL_MMIO
              Workaround </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-130091 </div>
            <div class="column desc"> Report SEV-TIO status </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-130079 </div>
            <div class="column desc"> Check SPL before enabling SEV-TIO
            </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-128258 </div>
            <div class="column desc"> [Turin]integer overflow in
              sev_tio_msg_mmio_validate_req</div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-130286 </div>
            <div class="column desc"> [TIO] Validate SDTE Write's Device
              ID </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-130566 </div>
            <div class="column desc"> [SEV-TIO] Remove SharedAES-GCM
              open source code </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-128592 </div>
            <div class="column desc"> ASID_FENCE__STATUS/CLEAR struct
              definitions changed in latest TIO spec (v0.90) </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-128835 </div>
            <div class="column desc"> [TIO] Update FW per the latest ABI
              Spec (v0.91) </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-128673 </div>
            <div class="column desc"> Always check TDI count before
              decomissioning </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-128103 </div>
            <div class="column desc"> jump to clear not end </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-128109 </div>
            <div class="column desc"> always check IV </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-128125 </div>
            <div class="column desc"> fix update_dev_no_fw_update </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-128246 </div>
            <div class="column desc"> goto exit_sev_init_invalidnot end
            </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-128097 </div>
            <div class="column desc"> Check alignment of guest context
              page </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-128814 </div>
            <div class="column desc"> Raise minimum dev cxt size </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-130089 </div>
            <div class="column desc"> [SEV] Remove VM Check Subcommand </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-129841 </div>
            <div class="column desc"> [TIO]Do not allow TIO on 2-P
              systems </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-129053 </div>
            <div class="column desc"> SFS.C does not provide the correct
              keysize to the internal hashing function </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-129828 </div>
            <div class="column desc"> [SEV-TIO] Fix SPDM max cert chain
              block size </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-127191 </div>
            <div class="column desc"> Insecure Management of Trusted
              Memory Regions </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-127268 </div>
            <div class="column desc"> [SEV-TIO][SPDM] Remove spdm-lib
              untracked directory </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-128428 </div>
            <div class="column desc"> [Turin]FW shall program the
              correct value in Payload Length and data obj length </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-129197 </div>
            <div class="column desc"> Install/clear data buffer seed </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-126857 </div>
            <div class="column desc"> avoid spdm buffer copy when
              validation steps fail</div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-126864 </div>
            <div class="column desc"> return error if copy_page_to_sla's
              checks fail </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-128575 </div>
            <div class="column desc"> Implement Conctenated Measurement
              Transcript </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-127194 </div>
            <div class="column desc"> Missing Return Value Validation
              from `pci_ide_km_receive_key_prog` </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-126855 </div>
            <div class="column desc"> Insufficient checks for
              tdi_info-&gt;tdi_info_paddr in snp_tio_mcmd_tdi_info </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-127249 </div>
            <div class="column desc"> Set the initial return status of
              command handler to be SEV_STATUS_UNSUPPORTED </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-128339 </div>
            <div class="column desc"> Check if the MMIO pages assigned
              to guest are 4K </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-126862 </div>
            <div class="column desc"> reset_spdm_ctrl_scratch_buffer
              ignores result of sev_tio_check_spdm_scrat_page_state </div>
          </div>
          <div class="row">
            <div class="column ticket"> PLAT-156817 </div>
            <div class="column desc"> Set TDISP violation behavior in
              PCIECORE </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-120817 </div>
            <div class="column desc"> Request buffer expansion if output
              buffer is too small </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-127195 </div>
            <div class="column desc"> Missing Key Material Clearing From
              Stack Memory </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-127196 </div>
            <div class="column desc"> Undefined Behavior in
              `get_next_iv` </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-127190 </div>
            <div class="column desc"> Missing Return Value Validation in
              `pci_doe_spdm_vendor_receive_data_ex` </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-118661 </div>
            <div class="column desc"> Simplify spdm_dev_ctx
              Get/Set/Reset/Increment the value of structure </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-119969 </div>
            <div class="column desc"> Blockwise measurements updates </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-126542 </div>
            <div class="column desc"> [SEV-TIO] MMIO validate reports
              incorrect status </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-120165 </div>
            <div class="column desc"> TIO - MMIO_REPORTING_OFFSET
              behavior not complaint with TDISP </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-122433 </div>
            <div class="column desc"> Check the device algorithms </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-104051 </div>
            <div class="column desc"> TDI_Bind returns false success if
              Device Context Buffer is all 0 </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-124792 </div>
            <div class="column desc"> buffer expansion support for
              output and scratch buffer </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-120483 </div>
            <div class="column desc"> Session policy updates </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-105971 </div>
            <div class="column desc"> PCI IDE KM DEV_CONNECT returns
              SPDM_REQUEST despite failure </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-121246 </div>
            <div class="column desc"> TIO_STATUSAdd SHA information </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-104783 </div>
            <div class="column desc"> TIO Secure Device Table Pages fail
              to increment subpage_count </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-124883 </div>
            <div class="column desc"> DownloadFirmwareEX17 fails </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-118689 </div>
            <div class="column desc"> TDI_REPORTUpdate error code upon
              length mismtach </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-122996 </div>
            <div class="column desc"> Fix DIG_REPORT API </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-119967 </div>
            <div class="column desc"> Large Interface Report </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-79343 </div>
            <div class="column desc"> Check the device capabilities from
              device </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-120863 </div>
            <div class="column desc"> TIO_DEV_CONNECT resets too much
              state on failure</div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-76331 </div>
            <div class="column desc"> Fix the use of data_obj_id_t in
              data_obj_spdm_req/resp </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-119395 </div>
            <div class="column desc"> [SEV-TIO] Incorrect ALGOS reported
              in TDI_INFO and TIO_MSG_TDI_INFO_RSP </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-120919 </div>
            <div class="column desc"> Misc Dev Status fixes </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-79635 </div>
            <div class="column desc"> [SEV-TIO] MMIO validate reports
              incorrect status </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-117813 </div>
            <div class="column desc"> All reserved fields should be zero
            </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-107017 </div>
            <div class="column desc"> Remove map memory call in
              DevStatus command handler</div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-118922 </div>
            <div class="column desc"> Refactorstp_page_count() and
              related code </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-120202 </div>
            <div class="column desc"> Erase IDE keys from SRAM after use
            </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-117934 </div>
            <div class="column desc"> Make TIO_INIT idempotent</div>
          </div>
          <div class="row">
            <div class="column ticket"> PLAT-171190 </div>
            <div class="column desc"> Support slot 2 based TIO devices </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-117693 </div>
            <div class="column desc"> Add TDI Reports counter field in
              TDI_INFO and TDI_DIGEST_REPORT data structure </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-118789 </div>
            <div class="column desc"> FW failed on small scratch and
              output buffer if the MAX BUFFER SIZE is different from
              MIN_BUFFER SIZE </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-113391 </div>
            <div class="column desc"> Update measurement report with one
              time value </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-103474 </div>
            <div class="column desc"> Restore libspdm context after live
              fw update </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-116762 </div>
            <div class="column desc"> Chunk Mode Support Define
              Enablement</div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-107131 </div>
            <div class="column desc"> TIO_STATUSAdd Status buffer state
              check </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-107096 </div>
            <div class="column desc"> TDI_STATUSValidate input
              parameters </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-106880 </div>
            <div class="column desc"> ASID_FENCE_STATUSValidate input
              physical address </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-107267 </div>
            <div class="column desc"> Set request attribute flag in
              measurement </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-109625 </div>
            <div class="column desc"> Guest request message processing
              should use host device ID</div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-109194 </div>
            <div class="column desc"> Guest RequestRestore libspdm
              callback registration after live fw update </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-107867 </div>
            <div class="column desc"> Fix EFR2 check </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-108895 </div>
            <div class="column desc"> Restore tio_en flag upon error </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-106850 </div>
            <div class="column desc"> [SEV-TIO] Support Topology
              Discovery </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-104991 </div>
            <div class="column desc"> Improve Guest Request Message
              Error Handling </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-105999 </div>
            <div class="column desc"> Add TIO_TDI_DIGEST_REPORT command
            </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-105226 </div>
            <div class="column desc"> [SEV]SegmentedRMP struct was not
              getting initialized after DLFW_EX </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-104785 </div>
            <div class="column desc"> TDI_BIND Error Handling can change
              any page to FIRMWARE state </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-104315 </div>
            <div class="column desc"> RedoInvestigate OS based test
              failure when request data buffer obj length is set </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-80587 </div>
            <div class="column desc"> Implement MMIO_CONFIG_REQ in TIO
              Guest Message. </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-72049 </div>
            <div class="column desc"> DEV_CONNECT never succeed </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-97722 </div>
            <div class="column desc"> Update SNP Platform Status Command
              to reflect TIO status </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-99054 </div>
            <div class="column desc"> [Turin] Added TIO bit in
              FEATURE_INFO mailbox command </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-98657 </div>
            <div class="column desc"> [SEV-TIO] TDI_INFO fails with
              SEV_RET_INVALID_PAGE_STATE </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-101227 </div>
            <div class="column desc"> Prevent index_base from advancing
              too far </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-70450 </div>
            <div class="column desc"> [Turin] RMP initialization in TIO
              Support in Segmented RMP </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-98894 </div>
            <div class="column desc"> Turin should be functional when
              HANDSHAKE_IN_CLEAR_FLAG is cleared </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-93420 </div>
            <div class="column desc"> Fix Dev Status response structure
            </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-83445 </div>
            <div class="column desc"> TIO_TDI_INFO </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-92380 </div>
            <div class="column desc"> [SEV TIO] TDI_STATUS fails after
              TDI_UNBIND and before TDI_RECLAIM </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1451 </div>
            <div class="column desc"> TIO_DEV_STATUS </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-92036 </div>
            <div class="column desc"> [SEV]Renaming drv_sev_rebase to
              drv_sev </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-92871 </div>
            <div class="column desc"> [Turin] [SEV-TIO] Dev Disconnect
              Cmd Error handling. </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-90871 </div>
            <div class="column desc"> [SEV-TIO] [TURIN] FixSlot Mask and
              Slot ID usage </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-90326 </div>
            <div class="column desc"> Update the
              LIBSPDM_MAX_CERT_CHAIN_BLOCK_LEN to 4080. </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-87886 </div>
            <div class="column desc"> [SEV-TIO]Move Memory Pool Dev
              context </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-84141 </div>
            <div class="column desc"> Need to check iommu secure device
              table setup status </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-84824 </div>
            <div class="column desc"> Set and use TDI Context object
              header params </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-84250 </div>
            <div class="column desc"> Revert setting data buffer request
              object length </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-84260 </div>
            <div class="column desc"> Check TDI bind status only on
              running guests </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-79207 </div>
            <div class="column desc"> Enable TIO_Init check for TIO
              commands</div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-83974 </div>
            <div class="column desc"> Do not set reponse data obj length
            </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-67777 </div>
            <div class="column desc"> Check all TDI unbound in
              SNP_Decommission </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-83534 </div>
            <div class="column desc"> FW skip sDTE zeroize if TDI SLA is
              date page type </div>
          </div>
          <div class="row">
            <div class="column ticket"> PLAT-159014 </div>
            <div class="column desc"> Turin C0SEV-TIO Dev connect fail
              with DEV measurement failed </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-73415 </div>
            <div class="column desc"> Set the object length </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-82928 </div>
            <div class="column desc"> Release memory resources in TIO
              command error handling </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-2224 </div>
            <div class="column desc"> [SEV_TIO] Fence Clear for Blocked
              by Default </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-82240 </div>
            <div class="column desc"> [Turin] Allow SLA to be data page
              + move them to FW state </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-82308 </div>
            <div class="column desc"> reset TIO Init and enable flags in
              snp shutdown </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-72130 </div>
            <div class="column desc"> DEV_CONNECT does not return any
              certificates data object </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-82573 </div>
            <div class="column desc"> In second DEV_CONNECT commandthe
              fw returns SEV_IN_USE error </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-80936 </div>
            <div class="column desc"> [SEV-TIO]Incorrect Interface ID in
              Guest Context. </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-80008 </div>
            <div class="column desc"> Corect Data Object Header of
              CertificateMeasurementInterface Data Object </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-80903 </div>
            <div class="column desc"> [SEV-TIO]Reading incorrect value
              in ASID CRTL registers in IOMMU L1 </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-2192 </div>
            <div class="column desc"> TIO_TDI_BIND fails when ran
              repeatedly </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-2218 </div>
            <div class="column desc"> Provide GPA Page Frame Number for
              MMIO validate </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-79209 </div>
            <div class="column desc"> Enable Encryption in
              TIO_DEV_CREATE </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-79210 </div>
            <div class="column desc"> TIO_DEV_CONNECT compliant with
              SEV-TIO 0.72 ABI </div>
          </div>
          <div class="row">
            <div class="column ticket"> PLAT-156034 </div>
            <div class="column desc"> [Turin B0/C0]Multiple TDI VFs
              Support. </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-68348 </div>
            <div class="column desc"> Allow SLA to be a data page -
              Refactoring </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1450 </div>
            <div class="column desc"> TIO_DEV_MEASUREMENTS</div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1449 </div>
            <div class="column desc"> TIO_DEV_CERTIFICATE </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-156386 </div>
            <div class="column desc"> Enable Check For TEE_MEM </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1460 </div>
            <div class="column desc"> Guest Request TIO_MSG_TDI_INFO_REQ
            </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1459 </div>
            <div class="column desc"> Restore Bind stage iteration </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1456 </div>
            <div class="column desc"> TIO_TDI_REPORT</div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1933 </div>
            <div class="column desc"> TIO_ASID_FENCE_CLEAR </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1934 </div>
            <div class="column desc"> TIO_ASID_FENCE_STATUS</div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-76026 </div>
            <div class="column desc"> [Turin] [SEV-TIO] Fixed Device Id
              for the Guest SDTE Write </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-73470 </div>
            <div class="column desc"> SPDM_TYPE is not set for SEV-TIO </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-2207 </div>
            <div class="column desc"> Store/Erase ASP SDTE Info in Dev
              Ctx </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-75800 </div>
            <div class="column desc"> [Turin] [SEV-TIO] Secure DMA
              Configuration. </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-75545 </div>
            <div class="column desc"> [Turin] [SEV-TIO] Removed 64M
              check in SecDev Table Init. </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1457 </div>
            <div class="column desc"> TIO_TDI_STATUS </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1468 </div>
            <div class="column desc"> API for sDTE Read Write </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-2189 </div>
            <div class="column desc"> TIO Guest Request </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-2198 </div>
            <div class="column desc"> Incorrect offset value used in
              TDISP requester library </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-2193 </div>
            <div class="column desc"> Validate SPDM Ctrl page states as
              per TIO spec </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-70591 </div>
            <div class="column desc"> [BKC][Turin] SPI ROM is Corrupted
              when setting SMEE in BIOS setup menu </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-2187 </div>
            <div class="column desc"> Update Physical Function ID &amp;
              Virtual Function ID </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-17880 </div>
            <div class="column desc"> DEV_CONNECT fails at
              SPDM_FINISH_RSP </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1447 </div>
            <div class="column desc"> TIO_STATUS </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-2163 </div>
            <div class="column desc"> Databuffer Refactor Changes </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1455 </div>
            <div class="column desc"> TIO_TDI_UNBIND </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1454 </div>
            <div class="column desc"> TIO_TDI_BIND </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-70528 </div>
            <div class="column desc"> Function ID off spec </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1712 </div>
            <div class="column desc"> SEV-TIO initialization </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-69095 </div>
            <div class="column desc"> [Turin] DF Address offset
              calculation error </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1931 </div>
            <div class="column desc"> TIO_DEVICE_DISCONNECT </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1452 </div>
            <div class="column desc"> TIO_TDI_CREATE </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1930 </div>
            <div class="column desc"> TIO_DEV_CONNECT </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-2146 </div>
            <div class="column desc"> [SEV-TIO] IDE Key generation </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-2145 </div>
            <div class="column desc"> [SEV-TIO] Add IDE subkey
              encryption of 128 bit of 0's </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-2147 </div>
            <div class="column desc"> [SEV-TIO] Fixed IDE substream
              direction for device </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1448 </div>
            <div class="column desc"> TIO_DEV_RECLAIM </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-2142 </div>
            <div class="column desc"> TIO_DEV_CREATE </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-67063 </div>
            <div class="column desc"> [Turin] SEV-TIOIncorrect PKDB SMN
              Key Address.</div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1453 </div>
            <div class="column desc"> TIO_TDI_RECLAIM </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-2123 </div>
            <div class="column desc"> Dev Ctx page does not have address
              page bit set </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-2086 </div>
            <div class="column desc"> Fixed the AMD Tee Crypto AES-GCM
              enc/dec (2) </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-66581 </div>
            <div class="column desc"> [Turin] SEV-TIOBuffer number
              parameter in memory allocation </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-2117 </div>
            <div class="column desc"> [SEV-TIO] Commit fix for Key for
              interim development </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-2111 </div>
            <div class="column desc"> [SEV-TIO] IDE KM message parameter
              error </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-2110 </div>
            <div class="column desc"> [SEV-TIO] Incorrect Substream ID
              for IDE PROG over PKDB. </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-2113 </div>
            <div class="column desc"> [SEV-TIO] Use fixed Key for
              interim development </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-2109 </div>
            <div class="column desc"> [SEV-TIO] Substream in IDE PROG
              Header is incorrect. </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-2108 </div>
            <div class="column desc"> [SEV-TIO] IDE Key Programming
              error </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-2101 </div>
            <div class="column desc"> Incorrect buffer length passed to
              copy databuffer API </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-65986 </div>
            <div class="column desc"> Build failure due to rebase
              truin-tio-dev to turin-mainline </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-2081 </div>
            <div class="column desc"> Extend TIO_DEV_CONNECT for TDISP
              version and capabilities </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-62114 </div>
            <div class="column desc"> Lib SPDM stack check for valid
              data transfer size </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-2085 </div>
            <div class="column desc"> Enable AMD TEE Crypto </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1985 </div>
            <div class="column desc"> AMD Tee Crypto and SPDMLIB Build
              integration </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-42949 </div>
            <div class="column desc"> [Turin] requester_SNP_drv - HW
              HMAC API's with Context save/restore </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-62113 </div>
            <div class="column desc"> Configure correct
              SNPIO_SPDM_CT_EXPONENT value </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-62357 </div>
            <div class="column desc"> SPDM CTRL structure should not
              have buffer len info </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-61384 </div>
            <div class="column desc"> Secure Message fails after 13th
              iteration (K_SET_STOP) </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-61349 </div>
            <div class="column desc"> SPDM GET Capabilities fails with
              latest libspdm stack </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1984 </div>
            <div class="column desc"> Add test case for SPDMLIB AEAD
              Encr and Decr. </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1945 </div>
            <div class="column desc"> Move SPDM mplementation flow into
              TIO_DEV_CONNECT </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1929 </div>
            <div class="column desc"> CSF-1932 - Implement TIO Device
              Create and Device Reclaim </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1823 </div>
            <div class="column desc"> Create sev-tio command handler
              stubs </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1714 </div>
            <div class="column desc"> Refactor data buffer interface </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1928 </div>
            <div class="column desc"> Implement Data payload encryption
              and decryption in Data Buffer </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1910 </div>
            <div class="column desc"> Change the Vendor ID in SPDM
              Vendor Defined message </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1977 </div>
            <div class="column desc"> Get certificate has incorrect
              length for a self-signed certificate </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1978 </div>
            <div class="column desc"> Add test case to retrieve EC
              Public Key from self signed certificate </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1909 </div>
            <div class="column desc"> Removed Sequence Numberand Random
              Bytes in Secure Message </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1975 </div>
            <div class="column desc"> HMAC API incorrectly check for
              Hmac Context size. </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1961 </div>
            <div class="column desc"> Hmac Key setup failed due missing
              algo </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1908 </div>
            <div class="column desc"> Set Stream Id = 0(Default). HV
              needs to configure this. </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1891 </div>
            <div class="column desc"> Out of Resources after 3 runsHMAC
              context buffers. </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1785 </div>
            <div class="column desc"> ADD TDISP Message and State
              Machine support </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1953 </div>
            <div class="column desc"> Memory allocation scheme for
              Crypto Context</div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1952 </div>
            <div class="column desc"> Local IDE Configuration in IDE KM
              module </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1951 </div>
            <div class="column desc"> IDE Programming on SOC using PKDB
            </div>
          </div>
          <div class="row">
            <div class="column ticket"> CSF-1896 </div>
            <div class="column desc"> Enable SEV DlFw with LibSPDM code
              integrated </div>
          </div>
        </div>
      </div>
      <!-- SMU CHANGES -->
      <div id="smu-box"> <a name="smu">
          <h3>System Management Unit (SMU) Changes</h3>
        </a>
        <div class="table" id="SMU-changes">
          <div class="row title">
            <div class="column ticket">Ticket Number </div>
            <div class="column desc">Description</div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-129889 </div>
            <div class="column desc"> Turin PMFW Release Version
              5E.7B.00 / 63.7B.00 </div>
          </div>
          <div class="row">
            <div class="column ticket"> FWDEV-129204 </div>
            <div class="column desc"> Fix corner cases involving L2
              refclkcycle and Core refclkcycle accumulation counter
              overflows </div>
          </div>
        </div>
      </div>
      <!-- MPIO CHANGES -->
      <!-- AGESA CHANGES -->
      <div id="agesa-box"> <a name="agesa">
          <h3>AMD Generic Encapsulated Software Architecture (AGESA)
            Changes</h3>
        </a>
        <div class="table" id="AGESA-changes">
          <div class="row title">
            <div class="column ticket">Ticket Number </div>
            <div class="column hash">Hash</div>
            <div class="column files">Affected Files</div>
            <div class="column desc">Description</div>
          </div>
          <div class="row">
            <div class="column ticket">FWDEV-134397</div>
            <div class="column hash">8c5daa3a949</div>
            <div class="column files">N/A<br>
            </div>
            <div class="column desc">[ C1]Integrate C1 ucode 0x0B002151
              to BIOS</div>
          </div>
          <div class="row">
            <div class="column ticket">FWDEV-134398</div>
            <div class="column hash">2085dea4411</div>
            <div class="column files">N/A<br>
            </div>
            <div class="column desc">[<span data-teams="true">Turin
                Dense</span>-B0]Integrate <span data-teams="true">Turin
                Dense</span> B0 ucode 0x0B10104E to BIOS</div>
          </div>
          <div class="row">
            <div class="column ticket">FWDEV-134607</div>
            <div class="column hash">29bc743b14c</div>
            <div class="column files">N/A<br>
            </div>
            <div class="column desc"> PSP FW 00.3D.00.76</div>
          </div>
          <div class="row">
            <div class="column ticket">FWDEV-132887</div>
            <div class="column hash">5922e5a9bec</div>
            <div class="column files">AgesaModulePkg/Nbio/BRH/PEI/DxioCfgPoints.c<br>
              AgesaModulePkg/Nbio/BRH/PEI/NbioPeiBrh.inf<br>
              AgesaPkg/AgesaPkg.dec<br>
            </div>
            <div class="column desc">[Turin] Set DRS supported bit in
              the Link Capabilities 2 register</div>
          </div>
          <div class="row">
            <div class="column ticket">FWDEV-128131</div>
            <div class="column hash">e109a6d5452</div>
            <div class="column files">AgesaModulePkg/AgesaSp5BrhModulePkg.inc.dsc<br>
              AgesaModulePkg/Library/ApcbLibV3/ApcbLibV3.c<br>
              AgesaModulePkg/Library/ApcbLibV3/ApcbLibV3.inf<br>
AgesaModulePkg/Library/ApcbTokenWhiteListBrhLib/ApcbTokenWhiteListBrhLib.c<br>
AgesaModulePkg/Library/ApcbTokenWhiteListBrhLib/ApcbTokenWhiteListBrhLib.inf<br>
AgesaModulePkg/Library/ApcbTokenWhiteListNullLib/ApcbTokenWhiteListNullLib.c<br>
AgesaModulePkg/Library/ApcbTokenWhiteListNullLib/ApcbTokenWhiteListNullLib.inf<br>
AgesaModulePkg/Library/ApcbTokenWhiteListRsLib/ApcbTokenWhiteListRsLib.c<br>
AgesaModulePkg/Library/ApcbTokenWhiteListRsLib/ApcbTokenWhiteListRsLib.inf<br>
              AgesaPkg/Include/Library/ApcbTokenWhiteListLib.h<br>
            </div>
            <div class="column desc">[Turin] Add a white list for APCB
              tokens which don't need to trigger full PMU training</div>
          </div>
          <div class="row">
            <div class="column ticket">FWDEV-132102</div>
            <div class="column hash">5f1a024d0a4</div>
            <div class="column files">N/A<br>
            </div>
            <div class="column desc">[ C1]Integrate C1 ucode 0x0B002150
              to BIOS</div>
          </div>
          <div class="row">
            <div class="column ticket">FWDEV-132103</div>
            <div class="column hash">247c6f7753d</div>
            <div class="column files">N/A<br>
            </div>
            <div class="column desc">[<span data-teams="true">Turin
                Dense</span>-B0]Integrate <span data-teams="true">Turin
                Dense</span> B0 ucode 0x0B10104D to BIOS</div>
          </div>
          <div class="row">
            <div class="column ticket">FWDEV-130378</div>
            <div class="column hash">f533df3c6c0</div>
            <div class="column files">AgesaModulePkg/Nbio/BRH/DXE/PcieAer.c<br>
            </div>
            <div class="column desc">RP ECRC GEN and CHECK EN config for
              mix non-ECRC and ECRC endpoint devices</div>
          </div>
          <div class="row">
            <div class="column ticket">FWDEV-127930</div>
            <div class="column hash">f19b35aa4f0</div>
            <div class="column files">AgesaPkg/Addendum/Apcb/Inc/BRH/ApcbV3TokenUid.h<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/Chalupa/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/CharzRX/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/CharzTX/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/Compliance/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/Galena/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/Huambo/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/Onyx/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/Purico/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/Quartz/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/QuartzFR4/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/QuartzRevA/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/Ruby/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/Titanite/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/Titanite2P2G/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/TitaniteRevC/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/Volcano/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/Include/ApcbCustomizedDefinitions.h<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/Include/ApcbDefaults.h<br>
            </div>
            <div class="column desc">Turin WA DRAM CA/CS
              backside(QCA/QCS) margin issue for specific vendor (WA #4)</div>
          </div>
          <div class="row">
            <div class="column ticket">FWDEV-127926</div>
            <div class="column hash">01489daff4e</div>
            <div class="column files">AgesaPkg/Addendum/Apcb/Inc/BRH/ApcbV3TokenUid.h<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/Chalupa/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/CharzRX/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/CharzTX/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/Compliance/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/Galena/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/Huambo/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/Onyx/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/Purico/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/Quartz/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/QuartzFR4/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/QuartzRevA/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/Ruby/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/Titanite/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/Titanite2P2G/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/TitaniteRevC/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/Volcano/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/Include/ApcbCustomizedDefinitions.h<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/Include/ApcbDefaults.h<br>
              <br>
              <br>
              <br>
              <br>
              <br>
              <br>
            </div>
            <div class="column desc">[Turin] DDR workaround 1b E-die CE
              errors at 6400 for specific vendor (WA #4)</div>
          </div>
          <div class="row">
            <div class="column ticket">FWDEV-122553</div>
            <div class="column hash">a304b487b25</div>
            <div class="column files">AgesaModulePkg/Nbio/BRH/PEI/DxioCfgPoints.c<br>
            </div>
            <div class="column desc"> Update Force Preset Table for P8</div>
          </div>
          <div class="row">
            <div class="column ticket">FWDEV-123194</div>
            <div class="column hash">59f286ad9d5</div>
            <div class="column files">N/A<br>
            </div>
            <div class="column desc">Relic.py should accept multiple API
              keys wrt artifactory instances</div>
          </div>
          <div class="row">
            <div class="column ticket">FWDEV-131101</div>
            <div class="column hash">3da8f605208</div>
            <div class="column files">N/A<br>
            </div>
            <div class="column desc"> PSP FW 00.3D.00.75 release</div>
          </div>
          <div class="row">
            <div class="column ticket">FWDEV-129913</div>
            <div class="column hash">377eb5d20e9</div>
            <div class="column files">AgesaPkg/Addendum/Apcb/Inc/BRH/ApcbV3TokenUid.h<br>
            </div>
            <div class="column desc">[Turin AGESA Spec]
              APCB_TOKEN_UID_DF_SYS_STORAGE_AT_TOP_OF_MEM default value
              should be DF_SYS_STORAGE_AT_TOP_OF_MEM_CONSOLIDATED in
              AGESA spec</div>
          </div>
          <div class="row">
            <div class="column ticket">FWDEV-129030</div>
            <div class="column hash">a2be3fd6be0</div>
            <div class="column files">AgesaModulePkg/Library/CcxZen5SegRmpBrhLib/CcxZen5SegRmpBrhDxeLib.c<br>
            </div>
            <div class="column desc">Cover Pre-Fetch and NON-PreFetch
              64Bit MMIO Regions under RMP</div>
          </div>
          <div class="row">
            <div class="column ticket">FWDEV-129589</div>
            <div class="column hash">a9d849ddac0</div>
            <div class="column files">N/A<br>
            </div>
            <div class="column desc"> PSP FW 00.3D.00.74 release</div>
          </div>
          <div class="row">
            <div class="column ticket">FWDEV-129299</div>
            <div class="column hash">311a8e5c303</div>
            <div class="column files">AgesaPkg/Addendum/Apcb/Inc/BRH/ApcbV3TokenUid.h<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c<br>
AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/Include/ApcbDefaults.h<br>
              <br>
              <br>
            </div>
            <div class="column desc">Add APCB token to modify twrwrscdlr
              +2 clks @4800 MBPS for Specific Vendor 3DS 256GB DIMM</div>
          </div>
          <div class="row">
            <div class="column ticket">FWDEV-129889</div>
            <div class="column hash">07e17c6b0d3</div>
            <div class="column files">N/A<br>
            </div>
            <div class="column desc">Turin PMFW Release Version 5E.7B.00
              / 63.7B.00</div>
          </div>
          <div class="row">
            <div class="column ticket">FWDEV-128903</div>
            <div class="column hash">245c7177491</div>
            <div class="column files">AgesaModulePkg/Nbio/BRH/DXE/NbioDxeBrh.inf<br>
              AgesaModulePkg/Nbio/BRH/DXE/PmmTableInit.c<br>
              AgesaPkg/AgesaPkg.dec<br>
              <br>
              <br>
            </div>
            <div class="column desc">TDC and EDC configuration through
              the PPTable has to be revised</div>
          </div>
          <div class="row">
            <div class="column ticket">FWDEV-125153</div>
            <div class="column hash">8c799568725</div>
            <div class="column files">AgesaModulePkg/Nbio/BRH/PEI/DxioCfgPoints.c<br>
            </div>
            <div class="column desc">[Turin] Incorrectly programmed
              PCIE_RCB_CNTL.RX_RCB_RC_CTO_IGNORE_ERR_IN_LINK_DOWN_EN</div>
          </div>
          <div class="row">
            <div class="column ticket">FWDEV-127365</div>
            <div class="column hash">6f3c0ac9030</div>
            <div class="column files">AgesaModulePkg/AgesaModuleNbioPkg.dec<br>
              AgesaModulePkg/Nbio/BRH/DXE/NbioDxeBrh.inf<br>
              AgesaModulePkg/Nbio/BRH/PEI/NbioPeiBrh.inf<br>
              AgesaPkg/AgesaPkg.dec<br>
              <br>
              <br>
              <br>
            </div>
            <div class="column desc">Move SEV-TIO CBS option to external
              option</div>
          </div>
          <div class="row">
            <div class="column ticket">FWDEV-125827</div>
            <div class="column hash">0b74d2754ca</div>
            <div class="column files">N/A<br>
            </div>
            <div class="column desc">Integrate <span data-teams="true">Turin





                Dense</span> B0 ucode 0x0B10104A to BIOS</div>
          </div>
          <div class="row">
            <div class="column ticket">FWDEV-122448</div>
            <div class="column hash">4b8bfe4e007</div>
            <div class="column files">AgesaModulePkg/Nbio/Common/CxlManagerDxe/CxlManagerDxe.c<br>
            </div>
            <div class="column desc">[CXL][Turin]Check CXL memory_active
              bit only once after warm-reset to handle CXL DRAM error</div>
          </div>
        </div>
      </div>
    </div>
    <!-- END OF CHANGES -->
    <div id="footer">
      <p class="copyright">Copyright 2024 ADVANCED MICRO DEVICES, INC.
        All Rights Reserved.</p>
      <p>AMD is granting you permission to use this software and
        documentation (if any) (collectively, the "Materials") pursuant
        to the terms and conditions of the Software License Agreement
        included with the Materials. If you do not have a copy of the
        Software License Agreement, contact your AMD representative for
        a copy.</p>
      <p>You agree that you will not reverse engineer or decompile the
        Materials, in whole or in part, except as allowed by applicable
        law.</p>
      <p>WARRANTY DISCLAIMER: THE MATERIALS ARE PROVIDED "AS IS" WITHOUT
        WARRANTY OF ANY KIND. AMD DISCLAIMS ALL WARRANTIES, EXPRESS,
        IMPLIED, OR STATUTORY, INCLUDING BUT NOT LIMITED TO THE IMPLIED
        WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE,
        TITLE, NON-INFRINGEMENT, THAT THE MATERIALS WILL RUN
        UNINTERRUPTED OR ERROR-FREE OR WARRANTIES ARISING FROM CUSTOM OF
        TRADE OR COURSE OF USAGE. THE ENTIRE RISK ASSOCIATED WITH THE
        USE OF THE MATERIAL IS ASSUMED BY YOU. Some jurisdictions do not
        allow the exclusion of implied warranties, so the above
        exclusion may not apply to You.</p>
      <p>LIMITATION OF LIABILITY AND INDEMNIFICATION: AMD AND ITS
        LICENSORS WILL NOT, UNDER ANY CIRCUMSTANCES BE LIABLE TO YOU FOR
        ANY PUNITIVE, DIRECT, INCIDENTAL, INDIRECT, SPECIAL OR
        CONSEQUENTIAL DAMAGES ARISING FROM USE OF THE MATERIALS OR THIS
        AGREEMENT EVEN IF AMD AND ITS LICENSORS HAVE BEEN ADVISED OF THE
        POSSIBILITY OF SUCH DAMAGES. In no event shall AMD's total
        liability to You for all damages, losses, and causes of action
        (whether in contract, tort (including negligence) or otherwise)
        exceed the amount of $100 USD. You agree to defend, indemnify
        and hold harmless AMD and its licensors, and any of their
        directors, officers, employees, affiliates or agents from and
        against any and all loss, damage, liability and other expenses
        (including reasonable attorneys' fees), resulting from Your use
        of the Materials or violation of the terms and conditions of
        this Agreement.</p>
      <p>U.S. GOVERNMENT RESTRICTED RIGHTS: The Materials are provided
        with "RESTRICTED RIGHTS." Use, duplication, or disclosure by the
        Government is subject to the restrictions as set forth in FAR
        52.227-14 and DFAR252.227-7013, et seq., or its successor. Use
        of the Materials by the Government constitutes acknowledgment of
        AMD's proprietary rights in them.</p>
      <p>EXPORT RESTRICTIONS: The Materials may be subject to export
        restrictions as stated in the Software License Agreement.</p>
    </div>
  </body>
</html>
