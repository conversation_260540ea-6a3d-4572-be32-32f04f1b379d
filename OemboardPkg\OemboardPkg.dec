## @file
#
# @copyright
# INTEL CONFIDENTIAL
# Copyright 2015 - 2021 Intel Corporation. <BR>
#
# The source code contained or described herein and all documents related to the
# source code ("Material") are owned by Intel Corporation or its suppliers or
# licensors. Title to the Material remains with Intel Corporation or its suppliers
# and licensors. The Material may contain trade secrets and proprietary    and
# confidential information of Intel Corporation and its suppliers and licensors,
# and is protected by worldwide copyright and trade secret laws and treaty
# provisions. No part of the Material may be used, copied, reproduced, modified,
# published, uploaded, posted, transmitted, distributed, or disclosed in any way
# without Intel's prior express written permission.
#
# No license under any patent, copyright, trade secret or other intellectual
# property right is granted to or conferred upon you by disclosure or delivery
# of the Materials, either expressly, by implication, inducement, estoppel or
# otherwise. Any license under such intellectual property rights must be
# express and approved by Intel in writing.
#
# Unless otherwise agreed by Intel in writing, you may not remove or alter
# this notice or any other notice embedded in Materials by Intel or
# Intel's suppliers or licensors in any way.
##

[Defines]
  DEC_SPECIFICATION              = 0x00010005
  PACKAGE_NAME                   = OemboardPkg
  PACKAGE_GUID                   = bc51da93-67a3-43ab-95ed-0d6023345226
  PACKAGE_VERSION                = 0.1

[Includes]
  Include
  ../Build
  Library

[LibraryClasses]

[Guids]
  gOemboardPkgTokenSpaceGuid                       = { 0xd5515eab, 0x150c, 0x474b, {0xae, 0xe9, 0x9d, 0x42, 0x2a, 0x37, 0xb3, 0xc7} }
  gOemCMOS_GUID                                    = { 0xAA87D643, 0xEBA4, 0x4BB5, {0xA1, 0xE5, 0x3F, 0x3E, 0x36, 0xB2, 0x0D, 0xBB} }

[Protocols]
  gOemboardPkgSkuIdGuid                            = { 0x9940f1ea, 0x4a16, 0x4955, { 0xab, 0xcb, 0x60, 0xd6, 0x8c, 0x23, 0x11, 0xf3} };

[PPIs]
  gI2CcontrolPpiGuid = { 0xC8613891, 0x4906, 0x6B13, {0x66, 0x77, 0xAB, 0x85, 0x10, 0xAE, 0x7E, 0x9E }}

[PcdsFeatureFlag]

[PcdsFixedAtBuild]

[PcdsDynamicEx]
  gOemboardPkgTokenSpaceGuid.PcdRtcPwrSts|FALSE|BOOLEAN|0x00000202
  
[PcdsFixedAtBuild, PcdsPatchableInModule, PcdsDynamic, PcdsDynamicEx]
  gOemboardPkgTokenSpaceGuid.PcdMacListPtr|{0x00}|VOID*|0x00010002
  gOemboardPkgTokenSpaceGuid.PcdSmbiosUpdateData|{0x00}|VOID*|0x00010003
  
[PcdsDynamic, PcdsDynamicEx]
## SKU number
  gOemboardPkgTokenSpaceGuid.CompalPcdSkuId|L"To be filled by O.E.M.                                            "|VOID*|0x00001000
  
## System Product Part Number
  gOemboardPkgTokenSpaceGuid.CompalPcdSystemProductPartNumber|L"To be filled by O.E.M.                                            "|VOID*|0x00010008

