//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2025, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************

/** @file VideoTextOutLib.c
    AMI Video TextOut Library functions

**/

#include <Uefi.h>
#include <Library/DebugLib.h>
#include <Library/AmiVideoTextOutLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/MemoryAllocationLib.h>
#include <Pi/PiBootMode.h>
#include <Pi/PiHob.h>
#include <Library/HobLib.h>

/**
    Get Hob data for the Guid passed as input parameter.

    @param HobData
	@param Guid
	@param Size

    @retval VOID
**/
VOID
GetVideoHobData (
    VOID                      **HobData,
    EFI_GUID                  *Guid,
    UINT16                    Size
)
{
    EFI_HOB_GUID_TYPE         *GuidHob;
    
    GuidHob = GetFirstGuidHob (Guid);
    if (GuidHob == NULL) {
        *HobData = BuildGuidHob (Guid, Size);
        if (*HobData == NULL) {
            DEBUG((DEBUG_ERROR, "HOB creation failed\n"));
            return;
        }
        
        ZeroMem (*HobData, Size);
    } else {
        *HobData = GET_GUID_HOB_DATA (GuidHob);
    }
    
    return;
}
/**
    Clears the screen (writes spaces) to the text mode frame buffer.

    @param VOID

    @retval EFI_SUCCESS
**/

EFI_STATUS
AmiVideoClear(
    VOID
) 
{
    TEXT_MODE_CHAR      *VideoMemory = (TEXT_MODE_CHAR *)(UINTN)VIDEO_MEMORY;
    UINT32              Index;
    VIDEO_PRIVATE_DATA  *ScreenInfo = NULL;

    // Clear the Video Memory Area
    for (Index = 0; Index < (MAX_ROWS * MAX_COLS); Index += 1) {
        // Fill with Spaces
        VideoMemory[Index].Ascii = ' ';
        VideoMemory[Index].Attribute = EFI_BACKGROUND_BLACK;
    }

    GetVideoHobData ((VOID **)&ScreenInfo, 
                    &gAmiVideoPrivateDataHobGuid,
                    sizeof(VIDEO_PRIVATE_DATA));
	if (ScreenInfo == NULL) {
	    return EFI_OUT_OF_RESOURCES;
	}
    // Reset current screen cursor position and color values
    ScreenInfo->Cursor = 0;
    ScreenInfo->Color = EFI_BACKGROUND_BLACK;
   
    return EFI_SUCCESS;
}

/**
    Scroll the screen up by one line

    @param None    StartRow
    @param None    LastRow
    @param None    MaxColumns

    @retval EFI_SUCCESS
**/

EFI_STATUS 
AmiVideoScrollUp(
  IN UINT32                                 StartRow, 
  IN UINT32                                 LastRow,
  IN UINT32                                 MaxColumns
)
{
    TEXT_MODE_CHAR      *VideoMemory = (TEXT_MODE_CHAR *)(UINTN)VIDEO_MEMORY;
    UINT32              Index;
    TEXT_MODE_CHAR      *FrameBuffer = NULL; 
    VIDEO_PRIVATE_DATA  *ScreenInfo = NULL; 

    GetVideoHobData ((VOID **)&ScreenInfo, 
                    &gAmiVideoPrivateDataHobGuid,
                    sizeof(VIDEO_PRIVATE_DATA));

    GetVideoHobData ((VOID **)&FrameBuffer, 
                    &gAmiTextModeFrameBufferHobGuid,
                    TEXT_MODE_FRAME_BUFFER_SIZE);
    
	if ((ScreenInfo == NULL) || (FrameBuffer == NULL)) {
	    return EFI_OUT_OF_RESOURCES;
	}
    // Move the next Row value to current row and keep doing until it reaches the end for VideoMemory and FrameBuffer   
    for (Index = (StartRow * MaxColumns); Index < ((LastRow - 1) * MaxColumns); Index++) {
        VideoMemory[Index].Ascii = FrameBuffer[Index + MaxColumns].Ascii;
        VideoMemory[Index].Attribute =  FrameBuffer[Index + MaxColumns].Attribute;
        
        FrameBuffer[Index].Ascii = FrameBuffer[Index + MaxColumns].Ascii;
        FrameBuffer[Index].Attribute =  FrameBuffer[Index + MaxColumns].Attribute;
    }
        
    //Clear the Last Row for VideoMemory and FrameBuffer
    for (Index = ((LastRow - 1) *  MaxColumns); Index < (LastRow * MaxColumns); Index++) {
        VideoMemory[Index].Ascii = ' '; // Fill with Spaces
        VideoMemory[Index].Attribute = ScreenInfo->Color; 
        
        FrameBuffer[Index].Ascii = ' ';
        FrameBuffer[Index].Attribute = ScreenInfo->Color; 
    }
        
    return EFI_SUCCESS;
}


/**
    Function to print the input string and scroll the line if needed

    @param String - String to print

    @retval EFI_SUCCESS
**/
EFI_STATUS 
AmiVideoPrintWorker(
    IN CONST CHAR8          *String
) 
{
    TEXT_MODE_CHAR          *VideoMemory = (TEXT_MODE_CHAR *)(UINTN)VIDEO_MEMORY;
    CHAR8                   Char;
    TEXT_MODE_CHAR          *FrameBuffer = NULL;
    VIDEO_PRIVATE_DATA      *ScreenInfo = NULL; 

    GetVideoHobData ((VOID **)&ScreenInfo, 
                    &gAmiVideoPrivateDataHobGuid,
                    sizeof(VIDEO_PRIVATE_DATA));
    
    if (ScreenInfo == NULL) {
        return EFI_OUT_OF_RESOURCES;
    }
    while (*String != '\0') {
        Char = *String++;
        
        switch (Char) {
        
            case CHAR_LINEFEED: 
                if (ScreenInfo->Cursor >= (MAX_ROWS * MAX_COLS)) {
                    AmiVideoScrollUp(0, MAX_ROWS, MAX_COLS);
                    ScreenInfo->Cursor = (MAX_ROWS - 1) * MAX_COLS;
                } else {
                    // Go to the next line
                    ScreenInfo->Cursor = ScreenInfo->Cursor + MAX_COLS;
                }
                break;
                
            case CHAR_CARRIAGE_RETURN: 
                // Bring the cursor to the beginning of the line
                ScreenInfo->Cursor = ScreenInfo->Cursor / (MAX_COLS);
                ScreenInfo->Cursor = ScreenInfo->Cursor * (MAX_COLS);
                break;
                
            default:    
                //Write date to Video Memory
                VideoMemory[ScreenInfo->Cursor].Ascii = Char;
                VideoMemory[ScreenInfo->Cursor].Attribute = ScreenInfo->Color;

                GetVideoHobData ((VOID **)&FrameBuffer, 
                                &gAmiTextModeFrameBufferHobGuid ,
                                TEXT_MODE_FRAME_BUFFER_SIZE);
                if (FrameBuffer == NULL) {
                    return EFI_OUT_OF_RESOURCES;
                }
                FrameBuffer[ScreenInfo->Cursor].Ascii = Char;
                FrameBuffer[ScreenInfo->Cursor].Attribute = ScreenInfo->Color;
                ScreenInfo->Cursor += 1;
        }
        
    }
    
    return EFI_SUCCESS;
}

/**
    Prints input string

    @param Side - Top/Bottom screen to print on
    @param String - String to print

    @retval EFI_SUCCESS
**/
EFI_STATUS
AmiVideoPrint(
    IN CONST CHAR8      *String
)
{
    
    AmiVideoPrintWorker(String);
    return EFI_SUCCESS;
}

/**
    Get the current cursor position (X,Y).

    @param Column - Pointer to Column number
    @param Row - Pointer to Row number    

    @retval EFI_SUCCESS
**/

EFI_STATUS
AmiVideoGetCursorPosition(
    IN UINT8    *Row, 
    IN UINT8    *Column 
)
{
    VIDEO_PRIVATE_DATA      *ScreenInfo = NULL; 

    GetVideoHobData ((VOID **)&ScreenInfo, 
                    &gAmiVideoPrivateDataHobGuid,
                    sizeof(VIDEO_PRIVATE_DATA));

    if (ScreenInfo == NULL) {
        return EFI_OUT_OF_RESOURCES;
    }
    // Bring the cursor to the beginning of the line
    *Row = (UINT8)(ScreenInfo->Cursor / (MAX_COLS));
    *Column = (UINT8)(ScreenInfo->Cursor - (ScreenInfo->Cursor / MAX_COLS ) * MAX_COLS);

    return EFI_SUCCESS;
}

/**
    Sets the cursor to the row and column specified by x and y.

    @param ColX - Column number
    @param RowY - Row number    

    @retval EFI_SUCCESS - Cursor position set as input X, Y
**/

EFI_STATUS
AmiVideoSetCursorPosition(
    IN UINT8        Column, 
    IN UINT8        Row
)
{
    VIDEO_PRIVATE_DATA      *ScreenInfo = NULL; 

    GetVideoHobData ((VOID **)&ScreenInfo, 
                    &gAmiVideoPrivateDataHobGuid,
                    sizeof(VIDEO_PRIVATE_DATA));
    if (ScreenInfo == NULL) {
        return EFI_OUT_OF_RESOURCES;
    }
    ScreenInfo->Cursor = (Row * MAX_COLS) + Column;

    return EFI_SUCCESS;
}

/**
    Sets the current screen foreground/background color to the specified value

    @param Color - Color attributes to be set
           Foreground color is specified by bits 2:0,
           Background color is specified by bits 6:4
           Bit 3 increases the intensity of the color selected.
           Bit 7 causes the text to blink.
 
       For example, intense white text on a blue background would be specified 
       as ((EFI_BACKGROUND_BLUE << 4) | EFI_WHITE).

    @retval EFI_SUCCESS
**/

EFI_STATUS
AmiVideoSetColor(
    IN UINT8            Color
)
{
    VIDEO_PRIVATE_DATA      *ScreenInfo = NULL; 

    GetVideoHobData ((VOID **)&ScreenInfo, 
                    &gAmiVideoPrivateDataHobGuid,
                    sizeof(VIDEO_PRIVATE_DATA));
    
    if (ScreenInfo == NULL) {
        return EFI_OUT_OF_RESOURCES;
    }
    ScreenInfo->Color = Color;

    return EFI_SUCCESS;
}
