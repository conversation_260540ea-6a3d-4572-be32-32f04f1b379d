/*****************************************************************************
 *
 * Copyright (C) 2018-2023 Advanced Micro Devices, Inc. All rights reserved.
 *
 * ***************************************************************************
 */

  External (DRPS, FieldUnitObj)
  External (DRPB, FieldUnitObj)
  External (DRPA, FieldUnitObj)
  External (DRPN, FieldUnitObj)
  External (CRPN, FieldUnitObj)
  External (\_SB.AERR, DeviceObj)

  // EDR Sample code - Start
  // To support AMD EDR Hot-plug, please modify the NVMEe drive's root port path and name here to match platform design.

#if !FixedPcdGetBool(UsePciXAslName)
  //Segment
  External (\_SB.S0D0._SEG, MethodObj)
  External (\_SB.S0D1._SEG, MethodObj)
  External (\_SB.PCI0._SEG, MethodObj)
  External (\_SB.S0D3._SEG, MethodObj)
  External (\_SB.S0D4._SEG, MethodObj)
  External (\_SB.S0D5._SEG, MethodObj)
  External (\_SB.S0D6._SEG, MethodObj)
  External (\_SB.S0D7._SEG, MethodObj)
  External (\_SB.S1D0._SEG, MethodObj)
  External (\_SB.S1D1._SEG, MethodObj)
  External (\_SB.S1D2._SEG, MethodObj)
  External (\_SB.S1D3._SEG, MethodObj)
  External (\_SB.S1D4._SEG, MethodObj)
  External (\_SB.S1D5._SEG, MethodObj)
  External (\_SB.S1D6._SEG, MethodObj)
  External (\_SB.S1D7._SEG, MethodObj)

  //Bus
  External (\_SB.S0D0._BBN, MethodObj)
  External (\_SB.S0D1._BBN, MethodObj)
  External (\_SB.PCI0._BBN, MethodObj)
  External (\_SB.S0D3._BBN, MethodObj)
  External (\_SB.S0D4._BBN, MethodObj)
  External (\_SB.S0D5._BBN, MethodObj)
  External (\_SB.S0D6._BBN, MethodObj)
  External (\_SB.S0D7._BBN, MethodObj)
  External (\_SB.S1D0._BBN, MethodObj)
  External (\_SB.S1D1._BBN, MethodObj)
  External (\_SB.S1D2._BBN, MethodObj)
  External (\_SB.S1D3._BBN, MethodObj)
  External (\_SB.S1D4._BBN, MethodObj)
  External (\_SB.S1D5._BBN, MethodObj)
  External (\_SB.S1D6._BBN, MethodObj)
  External (\_SB.S1D7._BBN, MethodObj)

  //S0D0
  External (\_SB.S0D0.D0B0, DeviceObj)
  External (\_SB.S0D0.D0B1, DeviceObj)
  External (\_SB.S0D0.D0B2, DeviceObj)
  External (\_SB.S0D0.D0B3, DeviceObj)
  External (\_SB.S0D0.D0B4, DeviceObj)
  External (\_SB.S0D0.D0B5, DeviceObj)
  External (\_SB.S0D0.D0B6, DeviceObj)
  External (\_SB.S0D0.D0B7, DeviceObj)
  External (\_SB.S0D0.D0B8, DeviceObj)

  //S0D1
  External (\_SB.S0D1.D1A0, DeviceObj)
  External (\_SB.S0D1.D1A1, DeviceObj)
  External (\_SB.S0D1.D1A2, DeviceObj)
  External (\_SB.S0D1.D1A3, DeviceObj)
  External (\_SB.S0D1.D1A4, DeviceObj)
  External (\_SB.S0D1.D1A5, DeviceObj)
  External (\_SB.S0D1.D1A6, DeviceObj)
  External (\_SB.S0D1.D1A7, DeviceObj)
  External (\_SB.S0D1.D1A8, DeviceObj)
  External (\_SB.S0D1.P4B0, DeviceObj) //AMI PORTING
  External (\_SB.S0D1.WAF0, DeviceObj)
  External (\_SB.S0D1.WAF1, DeviceObj)
  External (\_SB.S0D1.WAF2, DeviceObj)
  External (\_SB.S0D1.WAF3, DeviceObj)
//  External (\_SB.S0D1.WAF4, DeviceObj) //AMI PORTING start
  External (\_SB.S0D1.D1B5, DeviceObj)
  External (\_SB.S0D1.D1B6, DeviceObj)
  External (\_SB.S0D1.D1B7, DeviceObj) //AMI PORTING end

  //PCI0
  External (\_SB.PCI0.D2A0, DeviceObj)
  External (\_SB.PCI0.D2A1, DeviceObj)
  External (\_SB.PCI0.D2A2, DeviceObj)
//  External (\_SB.PCI0.D0B3, DeviceObj) //AMI PORTING
  External (\_SB.PCI0.D2A3, DeviceObj) //AMI PORTING
  External (\_SB.PCI0.D2A4, DeviceObj)
  External (\_SB.PCI0.D2A5, DeviceObj)
  External (\_SB.PCI0.D2A6, DeviceObj)
  External (\_SB.PCI0.D2A7, DeviceObj)
  External (\_SB.PCI0.D2A8, DeviceObj)

  //S0D3
  External (\_SB.S0D3.D3B0, DeviceObj)
  External (\_SB.S0D3.D3B1, DeviceObj)
  External (\_SB.S0D3.D3B2, DeviceObj)
  External (\_SB.S0D3.D3B3, DeviceObj)
  External (\_SB.S0D3.D3B4, DeviceObj)
  External (\_SB.S0D3.D3B5, DeviceObj)
  External (\_SB.S0D3.D3B6, DeviceObj)
  External (\_SB.S0D3.D3B7, DeviceObj)
  External (\_SB.S0D3.D3B8, DeviceObj)

  //S0D4
  External (\_SB.S0D4.D4B0, DeviceObj)
  External (\_SB.S0D4.D4B1, DeviceObj)
  External (\_SB.S0D4.D4B2, DeviceObj)
  External (\_SB.S0D4.D4B3, DeviceObj)
  External (\_SB.S0D4.D4B4, DeviceObj)
  External (\_SB.S0D4.D4B5, DeviceObj)
  External (\_SB.S0D4.D4B6, DeviceObj)
  External (\_SB.S0D4.D4B7, DeviceObj)
  External (\_SB.S0D4.D4B8, DeviceObj)

  //S0D5
  External (\_SB.S0D5.D5B0, DeviceObj)
  External (\_SB.S0D5.D5B1, DeviceObj)
  External (\_SB.S0D5.D5B2, DeviceObj)
  External (\_SB.S0D5.D5B3, DeviceObj)
  External (\_SB.S0D5.D5B4, DeviceObj)
  External (\_SB.S0D5.D5B5, DeviceObj)
  External (\_SB.S0D5.D5B6, DeviceObj)
  External (\_SB.S0D5.D5B7, DeviceObj)
  External (\_SB.S0D5.D5B8, DeviceObj)

  //S0D6
  External (\_SB.S0D6.D6B0, DeviceObj)
  External (\_SB.S0D6.D6B1, DeviceObj)
  External (\_SB.S0D6.D6B2, DeviceObj)
  External (\_SB.S0D6.D6B3, DeviceObj)
  External (\_SB.S0D6.D6B4, DeviceObj)
  External (\_SB.S0D6.D6B5, DeviceObj)
  External (\_SB.S0D6.D6B6, DeviceObj)
  External (\_SB.S0D6.D6B7, DeviceObj)
  External (\_SB.S0D6.D6B8, DeviceObj)

  //S0D7
  External (\_SB.S0D7.D7B0, DeviceObj)
  External (\_SB.S0D7.D7B1, DeviceObj)
  External (\_SB.S0D7.D7B2, DeviceObj)
  External (\_SB.S0D7.D7B3, DeviceObj)
  External (\_SB.S0D7.D7B4, DeviceObj)
  External (\_SB.S0D7.D7B5, DeviceObj)
  External (\_SB.S0D7.D7B6, DeviceObj)
  External (\_SB.S0D7.D7B7, DeviceObj)
  External (\_SB.S0D7.D7B8, DeviceObj)

  //S1D0
  External (\_SB.S1D0.D8B0, DeviceObj)
  External (\_SB.S1D0.D8B1, DeviceObj)
  External (\_SB.S1D0.D8B2, DeviceObj)
  External (\_SB.S1D0.D8B3, DeviceObj)
  External (\_SB.S1D0.D8B4, DeviceObj)
  External (\_SB.S1D0.D8B5, DeviceObj)
  External (\_SB.S1D0.D8B6, DeviceObj)
  External (\_SB.S1D0.D8B7, DeviceObj)
  External (\_SB.S1D0.D8B8, DeviceObj)

  //S1D1
  External (\_SB.S1D1.D9A0, DeviceObj)
  External (\_SB.S1D1.D9A1, DeviceObj)
  External (\_SB.S1D1.D9A2, DeviceObj)
  External (\_SB.S1D1.D9A3, DeviceObj)
  External (\_SB.S1D1.D9A4, DeviceObj)
  External (\_SB.S1D1.D9A5, DeviceObj)
  External (\_SB.S1D1.D9A6, DeviceObj)
  External (\_SB.S1D1.D9A7, DeviceObj)
  External (\_SB.S1D1.D9A8, DeviceObj)
  External (\_SB.S1D1.D9B0, DeviceObj)
  External (\_SB.S1D1.D9B1, DeviceObj)
  External (\_SB.S1D1.D9B2, DeviceObj)
  External (\_SB.S1D1.D9B3, DeviceObj)
  External (\_SB.S1D1.D9B4, DeviceObj)
  External (\_SB.S1D1.D9B5, DeviceObj)
  External (\_SB.S1D1.D9B6, DeviceObj)
  External (\_SB.S1D1.D9B7, DeviceObj)

  //S1D2
  External (\_SB.S1D2.DAA0, DeviceObj)
  External (\_SB.S1D2.DAA1, DeviceObj)
  External (\_SB.S1D2.DAA2, DeviceObj)
  External (\_SB.S1D2.DAA3, DeviceObj)
  External (\_SB.S1D2.DAA4, DeviceObj)
  External (\_SB.S1D2.DAA5, DeviceObj)
  External (\_SB.S1D2.DAA6, DeviceObj)
  External (\_SB.S1D2.DAA7, DeviceObj)
  External (\_SB.S1D2.DAA8, DeviceObj)

  //S1D3
  External (\_SB.S1D3.DBB0, DeviceObj)
  External (\_SB.S1D3.DBB1, DeviceObj)
  External (\_SB.S1D3.DBB2, DeviceObj)
  External (\_SB.S1D3.DBB3, DeviceObj)
  External (\_SB.S1D3.DBB4, DeviceObj)
  External (\_SB.S1D3.DBB5, DeviceObj)
  External (\_SB.S1D3.DBB6, DeviceObj)
  External (\_SB.S1D3.DBB7, DeviceObj)
  External (\_SB.S1D3.DBB8, DeviceObj)

  //S1D4
  External (\_SB.S1D4.DCB0, DeviceObj)
  External (\_SB.S1D4.DCB1, DeviceObj)
  External (\_SB.S1D4.DCB2, DeviceObj)
  External (\_SB.S1D4.DCB3, DeviceObj)
  External (\_SB.S1D4.DCB4, DeviceObj)
  External (\_SB.S1D4.DCB5, DeviceObj)
  External (\_SB.S1D4.DCB6, DeviceObj)
  External (\_SB.S1D4.DCB7, DeviceObj)
  External (\_SB.S1D4.DCB8, DeviceObj)

  //S1D5
  External (\_SB.S1D5.DDB0, DeviceObj)
  External (\_SB.S1D5.DDB1, DeviceObj)
  External (\_SB.S1D5.DDB2, DeviceObj)
  External (\_SB.S1D5.DDB3, DeviceObj)
  External (\_SB.S1D5.DDB4, DeviceObj)
  External (\_SB.S1D5.DDB5, DeviceObj)
  External (\_SB.S1D5.DDB6, DeviceObj)
  External (\_SB.S1D5.DDB7, DeviceObj)
  External (\_SB.S1D5.DDB8, DeviceObj)

  //S1D6
  External (\_SB.S1D6.DEB0, DeviceObj)
  External (\_SB.S1D6.DEB1, DeviceObj)
  External (\_SB.S1D6.DEB2, DeviceObj)
  External (\_SB.S1D6.DEB3, DeviceObj)
  External (\_SB.S1D6.DEB4, DeviceObj)
  External (\_SB.S1D6.DEB5, DeviceObj)
  External (\_SB.S1D6.DEB6, DeviceObj)
  External (\_SB.S1D6.DEB7, DeviceObj)
  External (\_SB.S1D6.DEB8, DeviceObj)

  //S1D7
  External (\_SB.S1D7.DFB0, DeviceObj)
  External (\_SB.S1D7.DFB1, DeviceObj)
  External (\_SB.S1D7.DFB2, DeviceObj)
  External (\_SB.S1D7.DFB3, DeviceObj)
  External (\_SB.S1D7.DFB4, DeviceObj)
  External (\_SB.S1D7.DFB5, DeviceObj)
  External (\_SB.S1D7.DFB6, DeviceObj)
  External (\_SB.S1D7.DFB7, DeviceObj)
  External (\_SB.S1D7.DFB8, DeviceObj)
  // EDR Sample code - End
#else
  //Segment
  External (\_SB.PCI7._SEG, MethodObj)
  External (\_SB.PCI5._SEG, MethodObj)
  External (\_SB.PCI0._SEG, MethodObj)
  External (\_SB.PCI2._SEG, MethodObj)
  External (\_SB.PCI4._SEG, MethodObj)
  External (\_SB.PCI6._SEG, MethodObj)
  External (\_SB.PCI3._SEG, MethodObj)
  External (\_SB.PCI1._SEG, MethodObj)
  External (\_SB.PCIF._SEG, MethodObj)
  External (\_SB.PCID._SEG, MethodObj)
  External (\_SB.PCI8._SEG, MethodObj)
  External (\_SB.PCIA._SEG, MethodObj)
  External (\_SB.PCIC._SEG, MethodObj)
  External (\_SB.PCIE._SEG, MethodObj)
  External (\_SB.PCIB._SEG, MethodObj)
  External (\_SB.PCI9._SEG, MethodObj)

  //Bus
  External (\_SB.PCI7._BBN, MethodObj)
  External (\_SB.PCI5._BBN, MethodObj)
  External (\_SB.PCI0._BBN, MethodObj)
  External (\_SB.PCI2._BBN, MethodObj)
  External (\_SB.PCI4._BBN, MethodObj)
  External (\_SB.PCI6._BBN, MethodObj)
  External (\_SB.PCI3._BBN, MethodObj)
  External (\_SB.PCI1._BBN, MethodObj)
  External (\_SB.PCIF._BBN, MethodObj)
  External (\_SB.PCID._BBN, MethodObj)
  External (\_SB.PCI8._BBN, MethodObj)
  External (\_SB.PCIA._BBN, MethodObj)
  External (\_SB.PCIC._BBN, MethodObj)
  External (\_SB.PCIE._BBN, MethodObj)
  External (\_SB.PCIB._BBN, MethodObj)
  External (\_SB.PCI9._BBN, MethodObj)

  //PCI7
  External (\_SB.PCI7.RP11, DeviceObj)
  External (\_SB.PCI7.RP12, DeviceObj)
  External (\_SB.PCI7.RP13, DeviceObj)
  External (\_SB.PCI7.RP14, DeviceObj)
  External (\_SB.PCI7.RP15, DeviceObj)
  External (\_SB.PCI7.RP16, DeviceObj)
  External (\_SB.PCI7.RP17, DeviceObj)
  External (\_SB.PCI7.RP21, DeviceObj)
  External (\_SB.PCI7.RP22, DeviceObj)

  //PCI5
  External (\_SB.PCI5.RP11, DeviceObj)
  External (\_SB.PCI5.RP12, DeviceObj)
  External (\_SB.PCI5.RP13, DeviceObj)
  External (\_SB.PCI5.RP14, DeviceObj)
  External (\_SB.PCI5.RP15, DeviceObj)
  External (\_SB.PCI5.RP16, DeviceObj)
  External (\_SB.PCI5.RP17, DeviceObj)
  External (\_SB.PCI5.RP21, DeviceObj)
  External (\_SB.PCI5.RP22, DeviceObj)
  External (\_SB.PCI5.RP31, DeviceObj)
  External (\_SB.PCI5.RP32, DeviceObj)
  External (\_SB.PCI5.RP33, DeviceObj)
  External (\_SB.PCI5.RP34, DeviceObj)
  External (\_SB.PCI5.RP35, DeviceObj)

  //PCI0
  External (\_SB.PCI0.RP11, DeviceObj)
  External (\_SB.PCI0.RP12, DeviceObj)
  External (\_SB.PCI0.RP13, DeviceObj)
  External (\_SB.PCI0.RP14, DeviceObj)
  External (\_SB.PCI0.RP15, DeviceObj)
  External (\_SB.PCI0.RP16, DeviceObj)
  External (\_SB.PCI0.RP17, DeviceObj)
  External (\_SB.PCI0.RP21, DeviceObj)
  External (\_SB.PCI0.RP22, DeviceObj)

  //PCI2
  External (\_SB.PCI2.RP11, DeviceObj)
  External (\_SB.PCI2.RP12, DeviceObj)
  External (\_SB.PCI2.RP13, DeviceObj)
  External (\_SB.PCI2.RP14, DeviceObj)
  External (\_SB.PCI2.RP15, DeviceObj)
  External (\_SB.PCI2.RP16, DeviceObj)
  External (\_SB.PCI2.RP17, DeviceObj)
  External (\_SB.PCI2.RP21, DeviceObj)
  External (\_SB.PCI2.RP22, DeviceObj)

  //PCI4
  External (\_SB.PCI4.RP11, DeviceObj)
  External (\_SB.PCI4.RP12, DeviceObj)
  External (\_SB.PCI4.RP13, DeviceObj)
  External (\_SB.PCI4.RP14, DeviceObj)
  External (\_SB.PCI4.RP15, DeviceObj)
  External (\_SB.PCI4.RP16, DeviceObj)
  External (\_SB.PCI4.RP17, DeviceObj)
  External (\_SB.PCI4.RP21, DeviceObj)
  External (\_SB.PCI4.RP22, DeviceObj)

  //PCI6
  External (\_SB.PCI6.RP11, DeviceObj)
  External (\_SB.PCI6.RP12, DeviceObj)
  External (\_SB.PCI6.RP13, DeviceObj)
  External (\_SB.PCI6.RP14, DeviceObj)
  External (\_SB.PCI6.RP15, DeviceObj)
  External (\_SB.PCI6.RP16, DeviceObj)
  External (\_SB.PCI6.RP17, DeviceObj)
  External (\_SB.PCI6.RP21, DeviceObj)
  External (\_SB.PCI6.RP22, DeviceObj)

  //PCI3
  External (\_SB.PCI3.RP11, DeviceObj)
  External (\_SB.PCI3.RP12, DeviceObj)
  External (\_SB.PCI3.RP13, DeviceObj)
  External (\_SB.PCI3.RP14, DeviceObj)
  External (\_SB.PCI3.RP15, DeviceObj)
  External (\_SB.PCI3.RP16, DeviceObj)
  External (\_SB.PCI3.RP17, DeviceObj)
  External (\_SB.PCI3.RP21, DeviceObj)
  External (\_SB.PCI3.RP22, DeviceObj)

  //PCI1
  External (\_SB.PCI1.RP11, DeviceObj)
  External (\_SB.PCI1.RP12, DeviceObj)
  External (\_SB.PCI1.RP13, DeviceObj)
  External (\_SB.PCI1.RP14, DeviceObj)
  External (\_SB.PCI1.RP15, DeviceObj)
  External (\_SB.PCI1.RP16, DeviceObj)
  External (\_SB.PCI1.RP17, DeviceObj)
  External (\_SB.PCI1.RP21, DeviceObj)
  External (\_SB.PCI1.RP22, DeviceObj)

  //PCIF
  External (\_SB.PCIF.RP11, DeviceObj)
  External (\_SB.PCIF.RP12, DeviceObj)
  External (\_SB.PCIF.RP13, DeviceObj)
  External (\_SB.PCIF.RP14, DeviceObj)
  External (\_SB.PCIF.RP15, DeviceObj)
  External (\_SB.PCIF.RP16, DeviceObj)
  External (\_SB.PCIF.RP17, DeviceObj)
  External (\_SB.PCIF.RP21, DeviceObj)
  External (\_SB.PCIF.RP22, DeviceObj)

  //PCID
  External (\_SB.PCID.RP11, DeviceObj)
  External (\_SB.PCID.RP12, DeviceObj)
  External (\_SB.PCID.RP13, DeviceObj)
  External (\_SB.PCID.RP14, DeviceObj)
  External (\_SB.PCID.RP15, DeviceObj)
  External (\_SB.PCID.RP16, DeviceObj)
  External (\_SB.PCID.RP17, DeviceObj)
  External (\_SB.PCID.RP21, DeviceObj)
  External (\_SB.PCID.RP22, DeviceObj)
  External (\_SB.PCID.RP31, DeviceObj)
  External (\_SB.PCID.RP32, DeviceObj)
  External (\_SB.PCID.RP33, DeviceObj)
  External (\_SB.PCID.RP34, DeviceObj)
  External (\_SB.PCID.RP35, DeviceObj)
  External (\_SB.PCID.RP36, DeviceObj)
  External (\_SB.PCID.RP37, DeviceObj)
  External (\_SB.PCID.RP41, DeviceObj)

  //PCI8
  External (\_SB.PCI8.RP11, DeviceObj)
  External (\_SB.PCI8.RP12, DeviceObj)
  External (\_SB.PCI8.RP13, DeviceObj)
  External (\_SB.PCI8.RP14, DeviceObj)
  External (\_SB.PCI8.RP15, DeviceObj)
  External (\_SB.PCI8.RP16, DeviceObj)
  External (\_SB.PCI8.RP17, DeviceObj)
  External (\_SB.PCI8.RP21, DeviceObj)
  External (\_SB.PCI8.RP22, DeviceObj)

  //PCIA
  External (\_SB.PCIA.RP11, DeviceObj)
  External (\_SB.PCIA.RP12, DeviceObj)
  External (\_SB.PCIA.RP13, DeviceObj)
  External (\_SB.PCIA.RP14, DeviceObj)
  External (\_SB.PCIA.RP15, DeviceObj)
  External (\_SB.PCIA.RP16, DeviceObj)
  External (\_SB.PCIA.RP17, DeviceObj)
  External (\_SB.PCIA.RP21, DeviceObj)
  External (\_SB.PCIA.RP22, DeviceObj)

  //PCIC
  External (\_SB.PCIC.RP11, DeviceObj)
  External (\_SB.PCIC.RP12, DeviceObj)
  External (\_SB.PCIC.RP13, DeviceObj)
  External (\_SB.PCIC.RP14, DeviceObj)
  External (\_SB.PCIC.RP15, DeviceObj)
  External (\_SB.PCIC.RP16, DeviceObj)
  External (\_SB.PCIC.RP17, DeviceObj)
  External (\_SB.PCIC.RP21, DeviceObj)
  External (\_SB.PCIC.RP22, DeviceObj)

  //PCIE
  External (\_SB.PCIE.RP11, DeviceObj)
  External (\_SB.PCIE.RP12, DeviceObj)
  External (\_SB.PCIE.RP13, DeviceObj)
  External (\_SB.PCIE.RP14, DeviceObj)
  External (\_SB.PCIE.RP15, DeviceObj)
  External (\_SB.PCIE.RP16, DeviceObj)
  External (\_SB.PCIE.RP17, DeviceObj)
  External (\_SB.PCIE.RP21, DeviceObj)
  External (\_SB.PCIE.RP22, DeviceObj)

  //PCIB
  External (\_SB.PCIB.RP11, DeviceObj)
  External (\_SB.PCIB.RP12, DeviceObj)
  External (\_SB.PCIB.RP13, DeviceObj)
  External (\_SB.PCIB.RP14, DeviceObj)
  External (\_SB.PCIB.RP15, DeviceObj)
  External (\_SB.PCIB.RP16, DeviceObj)
  External (\_SB.PCIB.RP17, DeviceObj)
  External (\_SB.PCIB.RP21, DeviceObj)
  External (\_SB.PCIB.RP22, DeviceObj)

  //PCI9
  External (\_SB.PCI9.RP11, DeviceObj)
  External (\_SB.PCI9.RP12, DeviceObj)
  External (\_SB.PCI9.RP13, DeviceObj)
  External (\_SB.PCI9.RP14, DeviceObj)
  External (\_SB.PCI9.RP15, DeviceObj)
  External (\_SB.PCI9.RP16, DeviceObj)
  External (\_SB.PCI9.RP17, DeviceObj)
  External (\_SB.PCI9.RP21, DeviceObj)
  External (\_SB.PCI9.RP22, DeviceObj)
  // EDR Sample code - End
#endif

  // CXL Timeout and Isolation Notification Sample code - Start
  External (\_SB.S0D0, DeviceObj)
  External (\_SB.PCI0, DeviceObj)
  External (\_SB.S0D5, DeviceObj)
  External (\_SB.S0D7, DeviceObj)
  External (\_SB.S1D0, DeviceObj)
  External (\_SB.S1D2, DeviceObj)
  External (\_SB.S1D5, DeviceObj)
  External (\_SB.S1D7, DeviceObj)
  // CXL Timeout and Isolation Notification Sample code - End

  Scope (\_GPE)
  {
    Method(_L14, 0) {
      If (LAnd(LEqual(DRPN, 0), LEqual(CRPN, 0))) {
        Notify (\_SB.AERR, 0x80)
      } ElseIf (LNotEqual(DRPN, 0))  {
        ToBuffer (DRPN, Local0)
        Store (ToString (Local0), Local1)
  // EDR Sample code - Start
#if FixedPcdGetBool(UsePciXAslName)
        If (LAnd(LEqual(\_SB.PCI7._SEG, DRPS), LEqual(\_SB.PCI7._BBN, DRPB))) {
          If (LEqual(Local1, "P711")) {
            Notify (\_SB.PCI7.RP11, 0xF)
          }
          If (LEqual(Local1, "P712")) {
            Notify (\_SB.PCI7.RP12, 0xF)
          }
          If (LEqual(Local1, "P713")) {
            Notify (\_SB.PCI7.RP13, 0xF)
          }
          If (LEqual(Local1, "P714")) {
            Notify (\_SB.PCI7.RP14, 0xF)
          }
          If (LEqual(Local1, "P715")) {
            Notify (\_SB.PCI7.RP15, 0xF)
          }
          If (LEqual(Local1, "P716")) {
            Notify (\_SB.PCI7.RP16, 0xF)
          }
          If (LEqual(Local1, "P717")) {
            Notify (\_SB.PCI7.RP17, 0xF)
          }
          If (LEqual(Local1, "P721")) {
            Notify (\_SB.PCI7.RP21, 0xF)
          }
          If (LEqual(Local1, "P722")) {
            Notify (\_SB.PCI7.RP22, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.PCI5._SEG, DRPS), LEqual (\_SB.PCI5._BBN, DRPB))) {
          If (LEqual(Local1, "P511")) {
            Notify (\_SB.PCI5.RP11, 0xF)
          }
          If (LEqual(Local1, "P512")) {
            Notify (\_SB.PCI5.RP12, 0xF)
          }
          If (LEqual(Local1, "P513")) {
            Notify (\_SB.PCI5.RP13, 0xF)
          }
          If (LEqual(Local1, "P514")) {
            Notify (\_SB.PCI5.RP14, 0xF)
          }
          If (LEqual(Local1, "P515")) {
            Notify (\_SB.PCI5.RP15, 0xF)
          }
          If (LEqual(Local1, "P516")) {
            Notify (\_SB.PCI5.RP16, 0xF)
          }
          If (LEqual(Local1, "P517")) {
            Notify (\_SB.PCI5.RP17, 0xF)
          }
          If (LEqual(Local1, "P521")) {
            Notify (\_SB.PCI5.RP21, 0xF)
          }
          If (LEqual(Local1, "P522")) {
            Notify (\_SB.PCI5.RP22, 0xF)
          }
          If (LEqual(Local1, "P531")) {
            Notify (\_SB.PCI5.RP31, 0xF)
          }
          If (LEqual(Local1, "P532")) {
            Notify (\_SB.PCI5.RP32, 0xF)
          }
          If (LEqual(Local1, "P533")) {
            Notify (\_SB.PCI5.RP33, 0xF)
          }
          If (LEqual(Local1, "P534")) {
            Notify (\_SB.PCI5.RP34, 0xF)
          }
          If (LEqual(Local1, "P535")) {
            Notify (\_SB.PCI5.RP35, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.PCI0._SEG, DRPS), LEqual (\_SB.PCI0._BBN, DRPB))) {
          If (LEqual(Local1, "P011")) {
            Notify (\_SB.PCI0.RP11, 0xF)
          }
          If (LEqual(Local1, "P012")) {
            Notify (\_SB.PCI0.RP12, 0xF)
          }
          If (LEqual(Local1, "P013")) {
            Notify (\_SB.PCI0.RP13, 0xF)
          }
          If (LEqual(Local1, "P014")) {
            Notify (\_SB.PCI0.RP14, 0xF)
          }
          If (LEqual(Local1, "P015")) {
            Notify (\_SB.PCI0.RP15, 0xF)
          }
          If (LEqual(Local1, "P016")) {
            Notify (\_SB.PCI0.RP16, 0xF)
          }
          If (LEqual(Local1, "P017")) {
            Notify (\_SB.PCI0.RP17, 0xF)
          }
          If (LEqual(Local1, "P021")) {
            Notify (\_SB.PCI0.RP21, 0xF)
          }
          If (LEqual(Local1, "P022")) {
            Notify (\_SB.PCI0.RP22, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.PCI2._SEG, DRPS), LEqual (\_SB.PCI2._BBN, DRPB))) {
          If (LEqual(Local1, "P211")) {
            Notify (\_SB.PCI2.RP11, 0xF)
          }
          If (LEqual(Local1, "P212")) {
            Notify (\_SB.PCI2.RP12, 0xF)
          }
          If (LEqual(Local1, "P213")) {
            Notify (\_SB.PCI2.RP13, 0xF)
          }
          If (LEqual(Local1, "P214")) {
            Notify (\_SB.PCI2.RP14, 0xF)
          }
          If (LEqual(Local1, "P215")) {
            Notify (\_SB.PCI2.RP15, 0xF)
          }
          If (LEqual(Local1, "P216")) {
            Notify (\_SB.PCI2.RP16, 0xF)
          }
          If (LEqual(Local1, "P217")) {
            Notify (\_SB.PCI2.RP17, 0xF)
          }
          If (LEqual(Local1, "P221")) {
            Notify (\_SB.PCI2.RP21, 0xF)
          }
          If (LEqual(Local1, "P222")) {
            Notify (\_SB.PCI2.RP22, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.PCI4._SEG, DRPS), LEqual (\_SB.PCI4._BBN, DRPB))) {
          If (LEqual(Local1, "P411")) {
            Notify (\_SB.PCI4.RP11, 0xF)
          }
          If (LEqual(Local1, "P412")) {
            Notify (\_SB.PCI4.RP12, 0xF)
          }
          If (LEqual(Local1, "P413")) {
            Notify (\_SB.PCI4.RP13, 0xF)
          }
          If (LEqual(Local1, "P414")) {
            Notify (\_SB.PCI4.RP14, 0xF)
          }
          If (LEqual(Local1, "P415")) {
            Notify (\_SB.PCI4.RP15, 0xF)
          }
          If (LEqual(Local1, "P416")) {
            Notify (\_SB.PCI4.RP16, 0xF)
          }
          If (LEqual(Local1, "P417")) {
            Notify (\_SB.PCI4.RP17, 0xF)
          }
          If (LEqual(Local1, "P421")) {
            Notify (\_SB.PCI4.RP21, 0xF)
          }
          If (LEqual(Local1, "P422")) {
            Notify (\_SB.PCI4.RP22, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.PCI6._SEG, DRPS), LEqual (\_SB.PCI6._BBN, DRPB))) {
          If (LEqual(Local1, "P611")) {
            Notify (\_SB.PCI6.RP11, 0xF)
          }
          If (LEqual(Local1, "P612")) {
            Notify (\_SB.PCI6.RP12, 0xF)
          }
          If (LEqual(Local1, "P613")) {
            Notify (\_SB.PCI6.RP13, 0xF)
          }
          If (LEqual(Local1, "P614")) {
            Notify (\_SB.PCI6.RP14, 0xF)
          }
          If (LEqual(Local1, "P615")) {
            Notify (\_SB.PCI6.RP15, 0xF)
          }
          If (LEqual(Local1, "P616")) {
            Notify (\_SB.PCI6.RP16, 0xF)
          }
          If (LEqual(Local1, "P617")) {
            Notify (\_SB.PCI6.RP17, 0xF)
          }
          If (LEqual(Local1, "P621")) {
            Notify (\_SB.PCI6.RP21, 0xF)
          }
          If (LEqual(Local1, "P622")) {
            Notify (\_SB.PCI6.RP22, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.PCI3._SEG, DRPS), LEqual (\_SB.PCI3._BBN, DRPB))) {
          If (LEqual(Local1, "P311")) {
            Notify (\_SB.PCI3.RP11, 0xF)
          }
          If (LEqual(Local1, "P312")) {
            Notify (\_SB.PCI3.RP12, 0xF)
          }
          If (LEqual(Local1, "P313")) {
            Notify (\_SB.PCI3.RP13, 0xF)
          }
          If (LEqual(Local1, "P314")) {
            Notify (\_SB.PCI3.RP14, 0xF)
          }
          If (LEqual(Local1, "P315")) {
            Notify (\_SB.PCI3.RP15, 0xF)
          }
          If (LEqual(Local1, "P316")) {
            Notify (\_SB.PCI3.RP16, 0xF)
          }
          If (LEqual(Local1, "P317")) {
            Notify (\_SB.PCI3.RP17, 0xF)
          }
          If (LEqual(Local1, "P321")) {
            Notify (\_SB.PCI3.RP21, 0xF)
          }
          If (LEqual(Local1, "P322")) {
            Notify (\_SB.PCI3.RP22, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.PCI1._SEG, DRPS), LEqual (\_SB.PCI1._BBN, DRPB))) {
          If (LEqual(Local1, "P111")) {
            Notify (\_SB.PCI1.RP11, 0xF)
          }
          If (LEqual(Local1, "P112")) {
            Notify (\_SB.PCI1.RP12, 0xF)
          }
          If (LEqual(Local1, "P113")) {
            Notify (\_SB.PCI1.RP13, 0xF)
          }
          If (LEqual(Local1, "P114")) {
            Notify (\_SB.PCI1.RP14, 0xF)
          }
          If (LEqual(Local1, "P115")) {
            Notify (\_SB.PCI1.RP15, 0xF)
          }
          If (LEqual(Local1, "P116")) {
            Notify (\_SB.PCI1.RP16, 0xF)
          }
          If (LEqual(Local1, "P117")) {
            Notify (\_SB.PCI1.RP17, 0xF)
          }
          If (LEqual(Local1, "P121")) {
            Notify (\_SB.PCI1.RP21, 0xF)
          }
          If (LEqual(Local1, "P122")) {
            Notify (\_SB.PCI1.RP22, 0xF)
          }
        }

#if (FixedPcdGet8 (PcdAmdNumberOfPhysicalSocket) == 2)
        If (LAnd(LEqual(\_SB.PCIF._SEG, DRPS), LEqual (\_SB.PCIF._BBN, DRPB))) {
          If (LEqual(Local1, "PF11")) {
            Notify (\_SB.PCIF.RP11, 0xF)
          }
          If (LEqual(Local1, "PF12")) {
            Notify (\_SB.PCIF.RP12, 0xF)
          }
          If (LEqual(Local1, "PF13")) {
            Notify (\_SB.PCIF.RP13, 0xF)
          }
          If (LEqual(Local1, "PF14")) {
            Notify (\_SB.PCIF.RP14, 0xF)
          }
          If (LEqual(Local1, "PF15")) {
            Notify (\_SB.PCIF.RP15, 0xF)
          }
          If (LEqual(Local1, "PF16")) {
            Notify (\_SB.PCIF.RP16, 0xF)
          }
          If (LEqual(Local1, "PF17")) {
            Notify (\_SB.PCIF.RP17, 0xF)
          }
          If (LEqual(Local1, "PF21")) {
            Notify (\_SB.PCIF.RP21, 0xF)
          }
          If (LEqual(Local1, "PF22")) {
            Notify (\_SB.PCIF.RP22, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.PCID._SEG, DRPS), LEqual (\_SB.PCID._BBN, DRPB))) {
          If (LEqual(Local1, "PD11")) {
            Notify (\_SB.PCID.RP11, 0xF)
          }
          If (LEqual(Local1, "PD12")) {
            Notify (\_SB.PCID.RP12, 0xF)
          }
          If (LEqual(Local1, "PD13")) {
            Notify (\_SB.PCID.RP13, 0xF)
          }
          If (LEqual(Local1, "PD14")) {
            Notify (\_SB.PCID.RP14, 0xF)
          }
          If (LEqual(Local1, "PD15")) {
            Notify (\_SB.PCID.RP15, 0xF)
          }
          If (LEqual(Local1, "PD16")) {
            Notify (\_SB.PCID.RP16, 0xF)
          }
          If (LEqual(Local1, "PD17")) {
            Notify (\_SB.PCID.RP17, 0xF)
          }
          If (LEqual(Local1, "PD21")) {
            Notify (\_SB.PCID.RP21, 0xF)
          }
          If (LEqual(Local1, "PD22")) {
            Notify (\_SB.PCID.RP22, 0xF)
          }
          If (LEqual(Local1, "PD31")) {
            Notify (\_SB.PCID.RP31, 0xF)
          }
          If (LEqual(Local1, "PD32")) {
            Notify (\_SB.PCID.RP32, 0xF)
          }
          If (LEqual(Local1, "PD33")) {
            Notify (\_SB.PCID.RP33, 0xF)
          }
          If (LEqual(Local1, "PD34")) {
            Notify (\_SB.PCID.RP34, 0xF)
          }
          If (LEqual(Local1, "PD35")) {
            Notify (\_SB.PCID.RP35, 0xF)
          }
          If (LEqual(Local1, "PD36")) {
            Notify (\_SB.PCID.RP36, 0xF)
          }
          If (LEqual(Local1, "PD37")) {
            Notify (\_SB.PCID.RP37, 0xF)
          }
          If (LEqual(Local1, "PD41")) {
            Notify (\_SB.PCID.RP41, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.PCI8._SEG, DRPS), LEqual (\_SB.PCI8._BBN, DRPB))) {
          If (LEqual(Local1, "P811")) {
            Notify (\_SB.PCI8.RP11, 0xF)
          }
          If (LEqual(Local1, "P812")) {
            Notify (\_SB.PCI8.RP12, 0xF)
          }
          If (LEqual(Local1, "P813")) {
            Notify (\_SB.PCI8.RP13, 0xF)
          }
          If (LEqual(Local1, "P814")) {
            Notify (\_SB.PCI8.RP14, 0xF)
          }
          If (LEqual(Local1, "P815")) {
            Notify (\_SB.PCI8.RP15, 0xF)
          }
          If (LEqual(Local1, "P816")) {
            Notify (\_SB.PCI8.RP16, 0xF)
          }
          If (LEqual(Local1, "P817")) {
            Notify (\_SB.PCI8.RP17, 0xF)
          }
          If (LEqual(Local1, "P821")) {
            Notify (\_SB.PCI8.RP21, 0xF)
          }
          If (LEqual(Local1, "P822")) {
            Notify (\_SB.PCI8.RP22, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.PCIA._SEG, DRPS), LEqual (\_SB.PCIA._BBN, DRPB))) {
          If (LEqual(Local1, "PA11")) {
            Notify (\_SB.PCIA.RP11, 0xF)
          }
          If (LEqual(Local1, "PA12")) {
            Notify (\_SB.PCIA.RP12, 0xF)
          }
          If (LEqual(Local1, "PA13")) {
            Notify (\_SB.PCIA.RP13, 0xF)
          }
          If (LEqual(Local1, "PA14")) {
            Notify (\_SB.PCIA.RP14, 0xF)
          }
          If (LEqual(Local1, "PA15")) {
            Notify (\_SB.PCIA.RP15, 0xF)
          }
          If (LEqual(Local1, "PA16")) {
            Notify (\_SB.PCIA.RP16, 0xF)
          }
          If (LEqual(Local1, "PA17")) {
            Notify (\_SB.PCIA.RP17, 0xF)
          }
          If (LEqual(Local1, "PA21")) {
            Notify (\_SB.PCIA.RP21, 0xF)
          }
          If (LEqual(Local1, "PA22")) {
            Notify (\_SB.PCIA.RP22, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.PCIC._SEG, DRPS), LEqual (\_SB.PCIC._BBN, DRPB))) {
          If (LEqual(Local1, "PC11")) {
            Notify (\_SB.PCIC.RP11, 0xF)
          }
          If (LEqual(Local1, "PC12")) {
            Notify (\_SB.PCIC.RP12, 0xF)
          }
          If (LEqual(Local1, "PC13")) {
            Notify (\_SB.PCIC.RP13, 0xF)
          }
          If (LEqual(Local1, "PC14")) {
            Notify (\_SB.PCIC.RP14, 0xF)
          }
          If (LEqual(Local1, "PC15")) {
            Notify (\_SB.PCIC.RP15, 0xF)
          }
          If (LEqual(Local1, "PC16")) {
            Notify (\_SB.PCIC.RP16, 0xF)
          }
          If (LEqual(Local1, "PC17")) {
            Notify (\_SB.PCIC.RP17, 0xF)
          }
          If (LEqual(Local1, "PC21")) {
            Notify (\_SB.PCIC.RP21, 0xF)
          }
          If (LEqual(Local1, "PC22")) {
            Notify (\_SB.PCIC.RP22, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.PCIE._SEG, DRPS), LEqual (\_SB.PCIE._BBN, DRPB))) {
          If (LEqual(Local1, "PE11")) {
            Notify (\_SB.PCIE.RP11, 0xF)
          }
          If (LEqual(Local1, "PE12")) {
            Notify (\_SB.PCIE.RP12, 0xF)
          }
          If (LEqual(Local1, "PE13")) {
            Notify (\_SB.PCIE.RP13, 0xF)
          }
          If (LEqual(Local1, "PE14")) {
            Notify (\_SB.PCIE.RP14, 0xF)
          }
          If (LEqual(Local1, "PE15")) {
            Notify (\_SB.PCIE.RP15, 0xF)
          }
          If (LEqual(Local1, "PE16")) {
            Notify (\_SB.PCIE.RP16, 0xF)
          }
          If (LEqual(Local1, "PE17")) {
            Notify (\_SB.PCIE.RP17, 0xF)
          }
          If (LEqual(Local1, "PE21")) {
            Notify (\_SB.PCIE.RP21, 0xF)
          }
          If (LEqual(Local1, "PE22")) {
            Notify (\_SB.PCIE.RP22, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.PCIB._SEG, DRPS), LEqual (\_SB.PCIB._BBN, DRPB))) {
          If (LEqual(Local1, "PB11")) {
            Notify (\_SB.PCIB.RP11, 0xF)
          }
          If (LEqual(Local1, "PB12")) {
            Notify (\_SB.PCIB.RP12, 0xF)
          }
          If (LEqual(Local1, "PB13")) {
            Notify (\_SB.PCIB.RP13, 0xF)
          }
          If (LEqual(Local1, "PB14")) {
            Notify (\_SB.PCIB.RP14, 0xF)
          }
          If (LEqual(Local1, "PB15")) {
            Notify (\_SB.PCIB.RP15, 0xF)
          }
          If (LEqual(Local1, "PB16")) {
            Notify (\_SB.PCIB.RP16, 0xF)
          }
          If (LEqual(Local1, "PB17")) {
            Notify (\_SB.PCIB.RP17, 0xF)
          }
          If (LEqual(Local1, "PB21")) {
            Notify (\_SB.PCIB.RP21, 0xF)
          }
          If (LEqual(Local1, "PB22")) {
            Notify (\_SB.PCIB.RP22, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.PCI9._SEG, DRPS), LEqual (\_SB.PCI9._BBN, DRPB))) {
          If (LEqual(Local1, "P911")) {
            Notify (\_SB.PCI9.RP11, 0xF)
          }
          If (LEqual(Local1, "P912")) {
            Notify (\_SB.PCI9.RP12, 0xF)
          }
          If (LEqual(Local1, "P913")) {
            Notify (\_SB.PCI9.RP13, 0xF)
          }
          If (LEqual(Local1, "P914")) {
            Notify (\_SB.PCI9.RP14, 0xF)
          }
          If (LEqual(Local1, "P915")) {
            Notify (\_SB.PCI9.RP15, 0xF)
          }
          If (LEqual(Local1, "P916")) {
            Notify (\_SB.PCI9.RP16, 0xF)
          }
          If (LEqual(Local1, "P917")) {
            Notify (\_SB.PCI9.RP17, 0xF)
          }
          If (LEqual(Local1, "P921")) {
            Notify (\_SB.PCI9.RP21, 0xF)
          }
          If (LEqual(Local1, "P922")) {
            Notify (\_SB.PCI9.RP22, 0xF)
          }
        }
#endif // FixedPcdGet8 (PcdAmdNumberOfPhysicalSocket)
#else // FixedPcdGet8 (UsePciXAslName)
        If (LAnd(LEqual(\_SB.S0D0._SEG, DRPS), LEqual(\_SB.S0D0._BBN, DRPB))) {
          If (LEqual(Local1, "D0B0")) {
            Notify (\_SB.S0D0.D0B0, 0xF)
          }
          If (LEqual(Local1, "D0B1")) {
            Notify (\_SB.S0D0.D0B1, 0xF)
          }
          If (LEqual(Local1, "D0B2")) {
            Notify (\_SB.S0D0.D0B2, 0xF)
          }
          If (LEqual(Local1, "D0B3")) {
            Notify (\_SB.S0D0.D0B3, 0xF)
          }
          If (LEqual(Local1, "D0B4")) {
            Notify (\_SB.S0D0.D0B4, 0xF)
          }
          If (LEqual(Local1, "D0B5")) {
            Notify (\_SB.S0D0.D0B5, 0xF)
          }
          If (LEqual(Local1, "D0B6")) {
            Notify (\_SB.S0D0.D0B6, 0xF)
          }
          If (LEqual(Local1, "D0B7")) {
            Notify (\_SB.S0D0.D0B7, 0xF)
          }
          If (LEqual(Local1, "D0B8")) {
            Notify (\_SB.S0D0.D0B8, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.S0D1._SEG, DRPS), LEqual (\_SB.S0D1._BBN, DRPB))) {
          If (LEqual(Local1, "D1A0")) {
            Notify (\_SB.S0D1.D1A0, 0xF)
          }
          If (LEqual(Local1, "D1A1")) {
            Notify (\_SB.S0D1.D1A1, 0xF)
          }
          If (LEqual(Local1, "D1A2")) {
            Notify (\_SB.S0D1.D1A2, 0xF)
          }
          If (LEqual(Local1, "D1A3")) {
            Notify (\_SB.S0D1.D1A3, 0xF)
          }
          If (LEqual(Local1, "D1A4")) {
            Notify (\_SB.S0D1.D1A4, 0xF)
          }
          If (LEqual(Local1, "D1A5")) {
            Notify (\_SB.S0D1.D1A5, 0xF)
          }
          If (LEqual(Local1, "D1A6")) {
            Notify (\_SB.S0D1.D1A6, 0xF)
          }
          If (LEqual(Local1, "D1A7")) {
            Notify (\_SB.S0D1.D1A7, 0xF)
          }
          If (LEqual(Local1, "D1A8")) {
            Notify (\_SB.S0D1.D1A8, 0xF)
          }
//AMI PORTING start
          If (LEqual(Local1, "P4B0")) {
            Notify (\_SB.S0D1.P4B0, 0xF)
          }
//AMI PORTING end
          If (LEqual(Local1, "WAF0")) {
            Notify (\_SB.S0D1.WAF0, 0xF)
          }
          If (LEqual(Local1, "WAF1")) {
            Notify (\_SB.S0D1.WAF1, 0xF)
          }
          If (LEqual(Local1, "WAF2")) {
            Notify (\_SB.S0D1.WAF2, 0xF)
          }
          If (LEqual(Local1, "WAF3")) {
            Notify (\_SB.S0D1.WAF3, 0xF)
          }
//AMI PORTING start
//          If (LEqual(Local1, "WAF4")) {
//            Notify (\_SB.S0D1.WAF4, 0xF)
//          }
          If (LEqual(Local1, "D1B5")) {
            Notify (\_SB.S0D1.D1B5, 0xF)
          }
          If (LEqual(Local1, "D1B6")) {
            Notify (\_SB.S0D1.D1B6, 0xF)
          }
          If (LEqual(Local1, "D1B7")) {
            Notify (\_SB.S0D1.D1B7, 0xF)
          }
//AMI PORTING end
        }

        If (LAnd(LEqual(\_SB.PCI0._SEG, DRPS), LEqual (\_SB.PCI0._BBN, DRPB))) {
          If (LEqual(Local1, "D2A0")) {
            Notify (\_SB.PCI0.D2A0, 0xF)
          }
          If (LEqual(Local1, "D2A1")) {
            Notify (\_SB.PCI0.D2A1, 0xF)
          }
          If (LEqual(Local1, "D2A2")) {
            Notify (\_SB.PCI0.D2A2, 0xF)
          }
//AMI PORTING start
//          If (LEqual(Local1, "D0B3")) {
//            Notify (\_SB.PCI0.D0B3, 0xF)
//          }
          If (LEqual(Local1, "D2A3")) {
            Notify (\_SB.PCI0.D2A3, 0xF)
          }
//AMI PORTING end
          If (LEqual(Local1, "D2A4")) {
            Notify (\_SB.PCI0.D2A4, 0xF)
          }
          If (LEqual(Local1, "D2A5")) {
            Notify (\_SB.PCI0.D2A5, 0xF)
          }
          If (LEqual(Local1, "D2A6")) {
            Notify (\_SB.PCI0.D2A6, 0xF)
          }
          If (LEqual(Local1, "D2A7")) {
            Notify (\_SB.PCI0.D2A7, 0xF)
          }
          If (LEqual(Local1, "D2A8")) {
            Notify (\_SB.PCI0.D2A8, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.S0D3._SEG, DRPS), LEqual (\_SB.S0D3._BBN, DRPB))) {
          If (LEqual(Local1, "D3B0")) {
            Notify (\_SB.S0D3.D3B0, 0xF)
          }
          If (LEqual(Local1, "D3B1")) {
            Notify (\_SB.S0D3.D3B1, 0xF)
          }
          If (LEqual(Local1, "D3B2")) {
            Notify (\_SB.S0D3.D3B2, 0xF)
          }
          If (LEqual(Local1, "D3B3")) {
            Notify (\_SB.S0D3.D3B3, 0xF)
          }
          If (LEqual(Local1, "D3B4")) {
            Notify (\_SB.S0D3.D3B4, 0xF)
          }
          If (LEqual(Local1, "D3B5")) {
            Notify (\_SB.S0D3.D3B5, 0xF)
          }
          If (LEqual(Local1, "D3B6")) {
            Notify (\_SB.S0D3.D3B6, 0xF)
          }
          If (LEqual(Local1, "D3B7")) {
            Notify (\_SB.S0D3.D3B7, 0xF)
          }
          If (LEqual(Local1, "D3B8")) {
            Notify (\_SB.S0D3.D3B8, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.S0D4._SEG, DRPS), LEqual (\_SB.S0D4._BBN, DRPB))) {
          If (LEqual(Local1, "D4B0")) {
            Notify (\_SB.S0D4.D4B0, 0xF)
          }
          If (LEqual(Local1, "D4B1")) {
            Notify (\_SB.S0D4.D4B1, 0xF)
          }
          If (LEqual(Local1, "D4B2")) {
            Notify (\_SB.S0D4.D4B2, 0xF)
          }
          If (LEqual(Local1, "D4B3")) {
            Notify (\_SB.S0D4.D4B3, 0xF)
          }
          If (LEqual(Local1, "D4B4")) {
            Notify (\_SB.S0D4.D4B4, 0xF)
          }
          If (LEqual(Local1, "D4B5")) {
            Notify (\_SB.S0D4.D4B5, 0xF)
          }
          If (LEqual(Local1, "D4B6")) {
            Notify (\_SB.S0D4.D4B6, 0xF)
          }
          If (LEqual(Local1, "D4B7")) {
            Notify (\_SB.S0D4.D4B7, 0xF)
          }
          If (LEqual(Local1, "D4B8")) {
            Notify (\_SB.S0D4.D4B8, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.S0D5._SEG, DRPS), LEqual (\_SB.S0D5._BBN, DRPB))) {
          If (LEqual(Local1, "D5B0")) {
            Notify (\_SB.S0D5.D5B0, 0xF)
          }
          If (LEqual(Local1, "D5B1")) {
            Notify (\_SB.S0D5.D5B1, 0xF)
          }
          If (LEqual(Local1, "D5B2")) {
            Notify (\_SB.S0D5.D5B2, 0xF)
          }
          If (LEqual(Local1, "D5B3")) {
            Notify (\_SB.S0D5.D5B3, 0xF)
          }
          If (LEqual(Local1, "D5B4")) {
            Notify (\_SB.S0D5.D5B4, 0xF)
          }
          If (LEqual(Local1, "D5B5")) {
            Notify (\_SB.S0D5.D5B5, 0xF)
          }
          If (LEqual(Local1, "D5B6")) {
            Notify (\_SB.S0D5.D5B6, 0xF)
          }
          If (LEqual(Local1, "D5B7")) {
            Notify (\_SB.S0D5.D5B7, 0xF)
          }
          If (LEqual(Local1, "D5B8")) {
            Notify (\_SB.S0D5.D5B8, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.S0D6._SEG, DRPS), LEqual (\_SB.S0D6._BBN, DRPB))) {
          If (LEqual(Local1, "D6B0")) {
            Notify (\_SB.S0D6.D6B0, 0xF)
          }
          If (LEqual(Local1, "D6B1")) {
            Notify (\_SB.S0D6.D6B1, 0xF)
          }
          If (LEqual(Local1, "D6B2")) {
            Notify (\_SB.S0D6.D6B2, 0xF)
          }
          If (LEqual(Local1, "D6B3")) {
            Notify (\_SB.S0D6.D6B3, 0xF)
          }
          If (LEqual(Local1, "D6B4")) {
            Notify (\_SB.S0D6.D6B4, 0xF)
          }
          If (LEqual(Local1, "D6B5")) {
            Notify (\_SB.S0D6.D6B5, 0xF)
          }
          If (LEqual(Local1, "D6B6")) {
            Notify (\_SB.S0D6.D6B6, 0xF)
          }
          If (LEqual(Local1, "D6B7")) {
            Notify (\_SB.S0D6.D6B7, 0xF)
          }
          If (LEqual(Local1, "D6B8")) {
            Notify (\_SB.S0D6.D6B8, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.S0D7._SEG, DRPS), LEqual (\_SB.S0D7._BBN, DRPB))) {
          If (LEqual(Local1, "D7B0")) {
            Notify (\_SB.S0D7.D7B0, 0xF)
          }
          If (LEqual(Local1, "D7B1")) {
            Notify (\_SB.S0D7.D7B1, 0xF)
          }
          If (LEqual(Local1, "D7B2")) {
            Notify (\_SB.S0D7.D7B2, 0xF)
          }
          If (LEqual(Local1, "D7B3")) {
            Notify (\_SB.S0D7.D7B3, 0xF)
          }
          If (LEqual(Local1, "D7B4")) {
            Notify (\_SB.S0D7.D7B4, 0xF)
          }
          If (LEqual(Local1, "D7B5")) {
            Notify (\_SB.S0D7.D7B5, 0xF)
          }
          If (LEqual(Local1, "D7B6")) {
            Notify (\_SB.S0D7.D7B6, 0xF)
          }
          If (LEqual(Local1, "D7B7")) {
            Notify (\_SB.S0D7.D7B7, 0xF)
          }
          If (LEqual(Local1, "D7B8")) {
            Notify (\_SB.S0D7.D7B8, 0xF)
          }
        }

#if (FixedPcdGet8 (PcdAmdNumberOfPhysicalSocket) == 2)
        If (LAnd(LEqual(\_SB.S1D0._SEG, DRPS), LEqual (\_SB.S1D0._BBN, DRPB))) {
          If (LEqual(Local1, "D8B0")) {
            Notify (\_SB.S1D0.D8B0, 0xF)
          }
          If (LEqual(Local1, "D8B1")) {
            Notify (\_SB.S1D0.D8B1, 0xF)
          }
          If (LEqual(Local1, "D8B2")) {
            Notify (\_SB.S1D0.D8B2, 0xF)
          }
          If (LEqual(Local1, "D8B3")) {
            Notify (\_SB.S1D0.D8B3, 0xF)
          }
          If (LEqual(Local1, "D8B4")) {
            Notify (\_SB.S1D0.D8B4, 0xF)
          }
          If (LEqual(Local1, "D8B5")) {
            Notify (\_SB.S1D0.D8B5, 0xF)
          }
          If (LEqual(Local1, "D8B6")) {
            Notify (\_SB.S1D0.D8B6, 0xF)
          }
          If (LEqual(Local1, "D8B7")) {
            Notify (\_SB.S1D0.D8B7, 0xF)
          }
          If (LEqual(Local1, "D8B8")) {
            Notify (\_SB.S1D0.D8B8, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.S1D1._SEG, DRPS), LEqual (\_SB.S1D1._BBN, DRPB))) {
          If (LEqual(Local1, "D9A0")) {
            Notify (\_SB.S1D1.D9A0, 0xF)
          }
          If (LEqual(Local1, "D9A1")) {
            Notify (\_SB.S1D1.D9A1, 0xF)
          }
          If (LEqual(Local1, "D9A2")) {
            Notify (\_SB.S1D1.D9A2, 0xF)
          }
          If (LEqual(Local1, "D9A3")) {
            Notify (\_SB.S1D1.D9A3, 0xF)
          }
          If (LEqual(Local1, "D9A4")) {
            Notify (\_SB.S1D1.D9A4, 0xF)
          }
          If (LEqual(Local1, "D9A5")) {
            Notify (\_SB.S1D1.D9A5, 0xF)
          }
          If (LEqual(Local1, "D9A6")) {
            Notify (\_SB.S1D1.D9A6, 0xF)
          }
          If (LEqual(Local1, "D9A7")) {
            Notify (\_SB.S1D1.D9A7, 0xF)
          }
          If (LEqual(Local1, "D9A8")) {
            Notify (\_SB.S1D1.D9A8, 0xF)
          }
          If (LEqual(Local1, "D9B0")) {
            Notify (\_SB.S1D1.D9B0, 0xF)
          }
          If (LEqual(Local1, "D9B1")) {
            Notify (\_SB.S1D1.D9B1, 0xF)
          }
          If (LEqual(Local1, "D9B2")) {
            Notify (\_SB.S1D1.D9B2, 0xF)
          }
          If (LEqual(Local1, "D9B3")) {
            Notify (\_SB.S1D1.D9B3, 0xF)
          }
          If (LEqual(Local1, "D9B4")) {
            Notify (\_SB.S1D1.D9B4, 0xF)
          }
          If (LEqual(Local1, "D9B5")) {
            Notify (\_SB.S1D1.D9B5, 0xF)
          }
          If (LEqual(Local1, "D9B6")) {
            Notify (\_SB.S1D1.D9B6, 0xF)
          }
          If (LEqual(Local1, "D9B7")) {
            Notify (\_SB.S1D1.D9B7, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.S1D2._SEG, DRPS), LEqual (\_SB.S1D2._BBN, DRPB))) {
          If (LEqual(Local1, "DAA0")) {
            //Notify (\_SB.S1D1.DAA0, 0xF) //AMI PORTING
            Notify (\_SB.S1D2.DAA0, 0xF)
          }
          If (LEqual(Local1, "DAA1")) {
            //Notify (\_SB.S1D1.DAA1, 0xF) //AMI PORTING
            Notify (\_SB.S1D2.DAA1, 0xF)
          }
          If (LEqual(Local1, "DAA2")) {
            //Notify (\_SB.S1D1.DAA2, 0xF) //AMI PORTING
            Notify (\_SB.S1D2.DAA2, 0xF)
          }
          If (LEqual(Local1, "DAA3")) {
            //Notify (\_SB.S1D1.DAA3, 0xF) //AMI PORTING
            Notify (\_SB.S1D2.DAA3, 0xF)
          }
          If (LEqual(Local1, "DAA4")) {
            //Notify (\_SB.S1D1.DAA4, 0xF) //AMI PORTING
            Notify (\_SB.S1D2.DAA4, 0xF)
          }
          If (LEqual(Local1, "DAA5")) {
            //Notify (\_SB.S1D1.DAA5, 0xF) //AMI PORTING
            Notify (\_SB.S1D2.DAA5, 0xF)
          }
          If (LEqual(Local1, "DAA6")) {
            //Notify (\_SB.S1D1.DAA6, 0xF) //AMI PORTING
            Notify (\_SB.S1D2.DAA6, 0xF)
          }
          If (LEqual(Local1, "DAA7")) {
            //Notify (\_SB.S1D1.DAA7, 0xF) //AMI PORTING
            Notify (\_SB.S1D2.DAA7, 0xF)
          }
          If (LEqual(Local1, "DAA8")) {
            //Notify (\_SB.S1D1.DAA8, 0xF) //AMI PORTING
            Notify (\_SB.S1D2.DAA8, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.S1D3._SEG, DRPS), LEqual (\_SB.S1D3._BBN, DRPB))) {
          If (LEqual(Local1, "DBB0")) {
            Notify (\_SB.S1D3.DBB0, 0xF)
          }
          If (LEqual(Local1, "DBB1")) {
            Notify (\_SB.S1D3.DBB1, 0xF)
          }
          If (LEqual(Local1, "DBB2")) {
            Notify (\_SB.S1D3.DBB2, 0xF)
          }
          If (LEqual(Local1, "DBB3")) {
            Notify (\_SB.S1D3.DBB3, 0xF)
          }
          If (LEqual(Local1, "DBB4")) {
            Notify (\_SB.S1D3.DBB4, 0xF)
          }
          If (LEqual(Local1, "DBB5")) {
            Notify (\_SB.S1D3.DBB5, 0xF)
          }
          If (LEqual(Local1, "DBB6")) {
            Notify (\_SB.S1D3.DBB6, 0xF)
          }
          If (LEqual(Local1, "DBB7")) {
            Notify (\_SB.S1D3.DBB7, 0xF)
          }
          If (LEqual(Local1, "DBB8")) {
            Notify (\_SB.S1D3.DBB8, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.S1D4._SEG, DRPS), LEqual (\_SB.S1D4._BBN, DRPB))) {
          If (LEqual(Local1, "DCB0")) {
            Notify (\_SB.S1D4.DCB0, 0xF)
          }
          If (LEqual(Local1, "DCB1")) {
            Notify (\_SB.S1D4.DCB1, 0xF)
          }
          If (LEqual(Local1, "DCB2")) {
            Notify (\_SB.S1D4.DCB2, 0xF)
          }
          If (LEqual(Local1, "DCB3")) {
            Notify (\_SB.S1D4.DCB3, 0xF)
          }
          If (LEqual(Local1, "DCB4")) {
            Notify (\_SB.S1D4.DCB4, 0xF)
          }
          If (LEqual(Local1, "DCB5")) {
            Notify (\_SB.S1D4.DCB5, 0xF)
          }
          If (LEqual(Local1, "DCB6")) {
            Notify (\_SB.S1D4.DCB6, 0xF)
          }
          If (LEqual(Local1, "DCB7")) {
            Notify (\_SB.S1D4.DCB7, 0xF)
          }
          If (LEqual(Local1, "DCB8")) {
            Notify (\_SB.S1D4.DCB8, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.S1D5._SEG, DRPS), LEqual (\_SB.S1D5._BBN, DRPB))) {
          If (LEqual(Local1, "DDB0")) {
            Notify (\_SB.S1D5.DDB0, 0xF)
          }
          If (LEqual(Local1, "DDB1")) {
            Notify (\_SB.S1D5.DDB1, 0xF)
          }
          If (LEqual(Local1, "DDB2")) {
            Notify (\_SB.S1D5.DDB2, 0xF)
          }
          If (LEqual(Local1, "DDB3")) {
            Notify (\_SB.S1D5.DDB3, 0xF)
          }
          If (LEqual(Local1, "DDB4")) {
            Notify (\_SB.S1D5.DDB4, 0xF)
          }
          If (LEqual(Local1, "DDB5")) {
            Notify (\_SB.S1D5.DDB5, 0xF)
          }
          If (LEqual(Local1, "DDB6")) {
            Notify (\_SB.S1D5.DDB6, 0xF)
          }
          If (LEqual(Local1, "DDB7")) {
            Notify (\_SB.S1D5.DDB7, 0xF)
          }
          If (LEqual(Local1, "DDB8")) {
            Notify (\_SB.S1D5.DDB8, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.S1D6._SEG, DRPS), LEqual (\_SB.S1D6._BBN, DRPB))) {
          If (LEqual(Local1, "DEB0")) {
            Notify (\_SB.S1D6.DEB0, 0xF)
          }
          If (LEqual(Local1, "DEB1")) {
            Notify (\_SB.S1D6.DEB1, 0xF)
          }
          If (LEqual(Local1, "DEB2")) {
            Notify (\_SB.S1D6.DEB2, 0xF)
          }
          If (LEqual(Local1, "DEB3")) {
            Notify (\_SB.S1D6.DEB3, 0xF)
          }
          If (LEqual(Local1, "DEB4")) {
            Notify (\_SB.S1D6.DEB4, 0xF)
          }
          If (LEqual(Local1, "DEB5")) {
            Notify (\_SB.S1D6.DEB5, 0xF)
          }
          If (LEqual(Local1, "DEB6")) {
            Notify (\_SB.S1D6.DEB6, 0xF)
          }
          If (LEqual(Local1, "DEB7")) {
            Notify (\_SB.S1D6.DEB7, 0xF)
          }
          If (LEqual(Local1, "DEB8")) {
            Notify (\_SB.S1D6.DEB8, 0xF)
          }
        }

        If (LAnd(LEqual(\_SB.S1D7._SEG, DRPS), LEqual (\_SB.S1D7._BBN, DRPB))) {
          If (LEqual(Local1, "DFB0")) {
            Notify (\_SB.S1D7.DFB0, 0xF)
          }
          If (LEqual(Local1, "DFB1")) {
            Notify (\_SB.S1D7.DFB1, 0xF)
          }
          If (LEqual(Local1, "DFB2")) {
            Notify (\_SB.S1D7.DFB2, 0xF)
          }
          If (LEqual(Local1, "DFB3")) {
            Notify (\_SB.S1D7.DFB3, 0xF)
          }
          If (LEqual(Local1, "DFB4")) {
            Notify (\_SB.S1D7.DFB4, 0xF)
          }
          If (LEqual(Local1, "DFB5")) {
            Notify (\_SB.S1D7.DFB5, 0xF)
          }
          If (LEqual(Local1, "DFB6")) {
            Notify (\_SB.S1D7.DFB6, 0xF)
          }
          If (LEqual(Local1, "DFB7")) {
            Notify (\_SB.S1D7.DFB7, 0xF)
          }
          If (LEqual(Local1, "DFB8")) {
            Notify (\_SB.S1D7.DFB8, 0xF)
          }
        }
#endif // FixedPcdGet8 (PcdAmdNumberOfPhysicalSocket)
#endif // FixedPcdGetBool(UsePciXAslName)
        Store (0xFF, DRPB)
        Store (0xFFFFFFFF, DRPA)
        Store (0, DRPN)
  // EDR Sample code - End
      } ElseIf (LNotEqual(CRPN, 0))  {
  // CXL Timeout and Isolation Notification Sample code - Start
        ToBuffer (CRPN, Local0)
        Store (ToString (Local0), Local1)

        If (LEqual(Local1, "S0D0")) {
          Notify (\_SB.S0D0, 0x80)
        }
        If (LEqual(Local1, "PCI0")) {
          Notify (\_SB.PCI0, 0x80)
        }
        If (LEqual(Local1, "S0D5")) {
          Notify (\_SB.S0D5, 0x80)
        }
        If (LEqual(Local1, "S0D7")) {
          Notify (\_SB.S0D7, 0x80)
        }
#if (FixedPcdGet8 (PcdAmdNumberOfPhysicalSocket) == 2)
        If (LEqual(Local1, "S1D0")) {
          Notify (\_SB.S1D0, 0x80)
        }
        If (LEqual(Local1, "S1D2")) {
          Notify (\_SB.S1D2, 0x80)
        }
        If (LEqual(Local1, "S1D5")) {
          Notify (\_SB.S1D5, 0x80)
        }
        If (LEqual(Local1, "S1D7")) {
          Notify (\_SB.S1D7, 0x80)
        }
#endif // FixedPcdGet8 (PcdAmdNumberOfPhysicalSocket
        Store (0, CRPN)
  // CXL Timeout and Isolation Notification Sample code - End
      }
    }
  }
