#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = ApobApcbUpdatesBrhLib
  FILE_GUID                      = A7E39BDD-D548-4D3A-98B2-1B2F120403E0
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = ApobApcbUpdatesLib

[Sources]
  ApobApcbUpdatesBrhLib.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModulePspPkg.dec

[LibraryClasses]
  DebugLib
  PcdLib
  AmdPspApobLib
  IdsLib

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdApicMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgIommuSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdResetCpuOnSyncFlood
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabric1TbRemap
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdRmpCover64BitMMIORanges

