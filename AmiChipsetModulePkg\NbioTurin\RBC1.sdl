PCIDEVICE
	Title  = "Root Complex Socket0 RBC1"
	Parent  = "PciHost (Virtual)"
#	Attribute  = "0x7307f"   - this sets VGA bits. Try without
	Attribute  = "0x303f"
	Dev_type  = "RootBridge"
	Bus  = 00h
	Dev  = 00h
	Fun  = 00h
	BridgeBusNum  = 05h
	SleepNum  = 01h
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB2.asl','AmiChipsetModulePkg/AslTurin/OSCM.ASL','AmdCpmPkg/Features/AmdCdmaDataInit/Asl/AmdCdmaDsm.asl'"
	ASLdeviceName  = "S0D1"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	FixedBus = Yes
	PCIExpress = Yes
#	ASL_PTS  = "Method;\_SB.PCI0.NPTS(Arg0)"
#	ASL_WAK  = "Method;ShiftLeft(Arg0, 4, DBG8)  \_SB.PCI0.NWAK(Arg0)"
	InitRoutine  = "RootBrgInit"
#	Token = "NSOCKETS" "=" "1"
End

#PCIDEVICE
#    Title  = "Root Complex Socket0 RBC1"
#    Parent  = "PciHost (Virtual)"
##   Attribute  = "0x7307f"   - this sets VGA bits. Try without
#    Attribute  = "0x303f"
#    Dev_type  = "RootBridge"
#    Bus  = 00h
#    Dev  = 00h
#    Fun  = 00h
#    BridgeBusNum  = 02h
#    SleepNum  = 01h
#    ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB2.asl','AmiChipsetModulePkg/AslTurin/OSCM.ASL','AmdCpmPkg/Features/AmdCdmaDataInit/Asl/AmdCdmaDsm.asl'"
#    ASLdeviceName  = "S0D1"
#    DeviceType = OnBoard
#    PCIBusSize = 32bit
#    ROMMain = No
#    FixedBus = Yes
#    PCIExpress = Yes
##   ASL_PTS  = "Method;\_SB.PCI0.NPTS(Arg0)"
##   ASL_WAK  = "Method;ShiftLeft(Arg0, 4, DBG8)  \_SB.PCI0.NWAK(Arg0)"
#    InitRoutine  = "RootBrgInit"
#    Token = "NSOCKETS" "=" "2"
#End

PCIDEVICE
	Title  = "Socket 0 RBC1 - IOMMU"
	Parent  = "Root Complex Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 00h
	Fun  = 02h
	ASLdeviceName  = "MU01"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
End

PCIDEVICE
	Title  = "Socket 0 RBC1 - RCEC"
	Parent  = "Root Complex Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 00h
	Fun  = 03h
	IntA =  LNKB; 57
	ASLdeviceName  = "EC01"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 0 Socket0 RBC1 TypeB"
	Parent  = "Root Complex Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 01h
	Fun  = 01h
	IntA =  LNKA; 56
	ASLdeviceName  = "D1A0"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl','AmdCpmPkg/Features/HotPlug/Brh/Asl/AmdHpRegsReconfig.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 1 Socket0 RBC1 TypeB"
	Parent  = "Root Complex Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 01h
	Fun  = 02h
	IntB =  LNKA; 56
	ASLdeviceName  = "D1A1"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl','AmdCpmPkg/Features/HotPlug/Brh/Asl/AmdHpRegsReconfig.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 2 Socket0 RBC1 TypeB"
	Parent  = "Root Complex Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 01h
	Fun  = 03h
	IntC =  LNKA; 56
	ASLdeviceName  = "D1A2"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl','AmdCpmPkg/Features/HotPlug/Brh/Asl/AmdHpRegsReconfig.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 3 Socket0 RBC1 TypeB"
	Parent  = "Root Complex Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 01h
	Fun  = 04h
	IntD =  LNKA; 56
	ASLdeviceName  = "D1A3"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl','AmdCpmPkg/Features/HotPlug/Brh/Asl/AmdHpRegsReconfig.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 4 Socket0 RBC1 TypeB"
	Parent  = "Root Complex Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 01h
	Fun  = 05h
	IntA =  LNKA; 56
	ASLdeviceName  = "D1A4"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl','AmdCpmPkg/Features/HotPlug/Brh/Asl/AmdHpRegsReconfig.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 5 Socket0 RBC1 TypeB"
	Parent  = "Root Complex Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 01h
	Fun  = 06h
	IntB =  LNKA; 56
	ASLdeviceName  = "D1A5"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl','AmdCpmPkg/Features/HotPlug/Brh/Asl/AmdHpRegsReconfig.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 6 Socket0 RBC1 TypeB"
	Parent  = "Root Complex Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 01h
	Fun  = 07h
	IntC =  LNKA; 56
	ASLdeviceName  = "D1A6"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl','AmdCpmPkg/Features/HotPlug/Brh/Asl/AmdHpRegsReconfig.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 7 Socket0 RBC1 TypeB"
	Parent  = "Root Complex Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 02h
	Fun  = 01h
	IntA =  LNKA; 56
	ASLdeviceName  = "D1A7"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl','AmdCpmPkg/Features/HotPlug/Brh/Asl/AmdHpRegsReconfig.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 8 Socket0 RBC1 TypeB"
	Parent  = "Root Complex Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 02h
	Fun  = 02h
	IntB =  LNKA; 56
	ASLdeviceName  = "D1A8"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl','AmdCpmPkg/Features/HotPlug/Brh/Asl/AmdHpRegsReconfig.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 0"
	Parent  = "Root Complex Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 03h
	Fun  = 01h
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl'"
	ASLdeviceName  = "P4B0"
	IntA =     LNKB; 57
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
End

PCIDEVICE
	Title  = "WAFL x1 PCIe GPP Bridge 0"
	Parent  = "Root Complex Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 03h
	Fun  = 02h
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl'"
	ASLdeviceName  = "WAF0"
	IntB =     LNKB; 57
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
End

PCIDEVICE
	Title  = "WAFL x1 PCIe GPP Bridge 1"
	Parent  = "Root Complex Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 03h
	Fun  = 03h
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl'"
	ASLdeviceName  = "WAF1"
	IntC =     LNKB; 57
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
End

PCIDEVICE
	Title  = "WAFL x1 PCIe GPP Bridge 2"
	Parent  = "Root Complex Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 03h
	Fun  = 04h
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl'"
	ASLdeviceName  = "WAF2"
	IntD =     LNKB; 57
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
End

PCIDEVICE
	Title  = "WAFL x1 PCIe GPP Bridge 3"
	Parent  = "Root Complex Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 03h
	Fun  = 05h
	IntA =  LNKB; 57
	ASLdeviceName  = "WAF3"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 5 Socket0 RBC1 TypeD"
	Parent  = "Root Complex Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 03h
	Fun  = 06h
	IntB =  LNKB; 57
	ASLdeviceName  = "D1B5"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 6 Socket0 RBC1 TypeD"
	Parent  = "Root Complex Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 03h
	Fun  = 07h
	IntC =  LNKB; 57
	ASLdeviceName  = "D1B6"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 7 Socket0 RBC1 TypeD"
	Parent  = "Root Complex Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 04h
	Fun  = 01h
	IntA =  LNKB; 57
	ASLdeviceName  = "D1B7"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl'"
End

PCIDEVICE
	Title  = "Int Bridge to Bus B Socket0 RBC1"
	Parent  = "Root Complex Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 07h
	Fun  = 01h
	IntA = LNKF; 69
#REF for fixed devices which must match below    
#	IntA =  LNKB; 65
#	IntB =  LNKC; 66
#	IntC =  LNKD; 67
#	IntD =  LNKA; 64
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	GPEbit  = 00Bh
	SleepNum  = 04h
	WakeEnabled = Yes
	ASLdeviceName  = "B010"
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL'"
	Help  = "Exposes NTB and PTDMA"    
End

PCIDEVICE
	Title  = "Primary PCIe Dummy Function Socket0 RBC1"
	Parent  = "Int Bridge to Bus B Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 00h
	Fun  = 00h
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
End

PCIDEVICE
	Title  = "MPDMA Socket0 RBC1"
	Parent  = "Int Bridge to Bus B Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 0h
	Fun  = 01h
	SleepNum  = 01h
	IntA = 	LNKF; 69
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
End

PCIDEVICE
	Title  = "PCIe Non-Transparent Bridge Socket0 RBC1"
	Parent  = "Int Bridge to Bus B Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 0h
	Fun  = 02h
	SleepNum  = 01h
	IntB = 	LNKG; 70
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
End

PCIDEVICE
	Title  = "PCIe See Vntb Socket0 RBC1"
	Parent  = "Int Bridge to Bus B Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 0h
	Fun  = 03h
	SleepNum  = 01h
	IntC = 	LNKH; 71
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
End

PCIDEVICE
	Title  = "PCIe ASP Socket0 RBC1"
	Parent  = "Int Bridge to Bus B Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 00h
	Fun  = 05h
	ASLdeviceName  = "ASP0"
	IntD =     LNKE; 68
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
End

PCIDEVICE
	Title  = "PCIe ACP Socket0 RBC1"
	Parent  = "Int Bridge to Bus B Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 0h
	Fun  = 06h
	SleepNum  = 01h
	IntA =     LNKF; 69
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
End

PCIDEVICE
	Title  = "HD Audio Controller Socket0 RBC1"
	Parent  = "Int Bridge to Bus B Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 0h
	Fun  = 07h
	SleepNum  = 01h
	IntB =     LNKG; 70
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
End

PCIDEVICE
	Title  = "Int Bridge to Bus C Port1 Socket0 RBC1"
	Parent  = "Root Complex Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 07h
	Fun  = 02h
	IntB =  LNKC; 74
#REF for fixed devices which must match below
#	IntA =  LNKB; 73
#	IntB =  LNKC; 74
#	IntC =  LNKD; 75
#	IntD =  LNKA; 72
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	ASLdeviceName  = "C011"
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL'"
	Help  = "Exposes SATA"    
End

PCIDEVICE
	Title  = "SATA AHCI Mode Socket0 RBC1"
	Parent  = "Int Bridge to Bus C Port1 Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	ASLfile  = "'AmiChipsetModulePkg\Asl\Sata.asl'"
	ASLdeviceName  = "SA11"
	Dev  = 1h
	Fun  = 0h
	SleepNum  = 01h
	IntA =     LNKB; 73
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	InitRoutine  = "SATA_Init"
End

PCIDEVICE
	Title  = "SATA2 AHCI Mode Socket0 RBC1"
	Parent  = "Int Bridge to Bus C Port1 Socket0 RBC1"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	ASLfile  = "'AmiChipsetModulePkg\Asl\Sata.asl'"
	ASLdeviceName  = "SA12"
	Dev  = 1h
	Fun  = 1h
	SleepNum  = 01h
	IntB =     LNKC; 74
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	InitRoutine  = "SATA_Init"
End

IOAPIC
	Title  = "RBC1_IOAPIC"
	APICID  = 0F1h
	VectorBase  = 038h
	VectorRange  = 020h
	AddressBase  = 0CF000000h
End
