//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2025, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************

/** @file ProgramMmioRange.c
        This file contains helper function to program MMIO range registers!
**/
#include <PciBus.h>
#include <PiDxe.h>
#include <Library/UefiBootServicesTableLib.h>
#include <Library/UefiRuntimeServicesTableLib.h>
#include <Protocol/SocLogicalIdProtocol.h>
#include <Protocol/AmdApcbProtocol.h>
#include <Library/ProgramMmioRange.h>
#include <Library/UefiLib.h>
#include <Library/PciLib.h>
#include <Library/BaseFabricTopologyLib.h>
#include "Nb.h"

#include <Protocol/FabricResourceManagerServicesProtocol.h>

// static EFI_GUID  gAmiGlobalVariableGuid = AMI_GLOBAL_VARIABLE_GUID;

extern
UINTN
AlignFromGra (
    UINTN  g
    );

extern  FABRIC_RESOURCE_FOR_EACH_RB *gFabricResource;
extern  BOOLEAN                     mIoReallocationReq;

#if BoardPciRes_SUPPORT
typedef struct _VGA_DEVICE {
    UINT16 VenId;
    UINT16 DevId;
    UINT8  Bus;
    UINT8  Device;
    UINT8  Function;
    BOOLEAN VgaTriedFlag;
} VGA_DEVICE;

typedef struct _VGA_DEVICES_TRIED {
    BOOLEAN     VideoFound;
    UINT8       VideoIndex; 
    VGA_DEVICE  DeviceList[5];
} VGA_DEVICES_TRIED;

VGA_DEVICES_TRIED gVgaDevices;
#endif //end OOR support

EFI_STATUS
GetRootbridgeLocation (
    IN      PCI_ROOT_BRG_DATA  *RootBrgData,
    IN      UINTN              RootBrgIndex,
    IN OUT  UINTN              *SocketNo,
    IN OUT  UINTN              *RbNo,
    IN OUT  UINTN              *PhyRbNo
    );

VOID
DoColdReset (
    UINT32  RawNewBottomIo )
{
    EFI_STATUS  Status;
    UINT64      AlignedNewBottomIO;
    UINT32      TokenSize;
    UINT64      ApcbBottomIo;

    AMD_APCB_SERVICE_PROTOCOL   *ApcbDxeServiceProtocol;

    Status = gBS->LocateProtocol(
                    &gAmdApcbDxeServiceProtocolGuid,
                    NULL,
                    (VOID **) &ApcbDxeServiceProtocol
                  );
    if (EFI_ERROR (Status)) {
        CpuDeadLoop();
    }

    TokenSize = sizeof(UINT8);
    Status = ApcbDxeServiceProtocol->ApcbGetConfigParameter (
                                      ApcbDxeServiceProtocol,
                                      APCB_ID_CONFIG_DF_BOTTOMIO,
                                      &TokenSize,
                                      &ApcbBottomIo
                                    );

    ApcbBottomIo &= (UINT64)0xFF;
    ApcbBottomIo = ApcbBottomIo << 24;

    AlignedNewBottomIO = ((UINT64)RawNewBottomIo & 0xF0000000);

    DEBUG ((DEBUG_INFO, "\nAPCB:BottomIo = %lx, RawNewBottomIo = %lx, AlignedNewBottomIO = %x\n",
            ApcbBottomIo,
            RawNewBottomIo,
            AlignedNewBottomIO));

    if(AlignedNewBottomIO < ApcbBottomIo) {
        AlignedNewBottomIO = AlignedNewBottomIO >> 24;

        TokenSize = sizeof(UINT8);
        Status = ApcbDxeServiceProtocol->ApcbSetConfigParameter (
                                          ApcbDxeServiceProtocol,
                                          APCB_ID_CONFIG_DF_BOTTOMIO,
                                          &TokenSize,
                                          &AlignedNewBottomIO
                                        );
        if(Status == EFI_UNSUPPORTED) {
          DEBUG ((DEBUG_INFO, "\nRecovery flag set, skipping bottomIO adjust!\n"));
          return;
        }

        Status = ApcbDxeServiceProtocol->ApcbFlushData (ApcbDxeServiceProtocol);

        DEBUG ((DEBUG_INFO, "\nDoing Warm reset to adjust bottomIO!\n"));
        gRT->ResetSystem(EfiResetWarm, EFI_SUCCESS, 0, NULL);
    }
}

#if BoardPciRes_SUPPORT
EFI_STATUS
FirstBootSetVgaList (
    VOID )
{
    EFI_STATUS Status;
    UINT32     LegacyVgaBDF;
    UINTN      Bus, Device, Function;
    UINT16     VendorId, DeviceId;
    UINTN      VariableSize;
    
    LegacyVgaBDF = PcdGet32 (PcdLegacyVgaBDF);
    
    DEBUG((DEBUG_ERROR, "FirstBootSetVgaList: LegacyVgaBDF-%x\n", LegacyVgaBDF));
    
    if(LegacyVgaBDF == 0) return EFI_UNSUPPORTED;
    
    gVgaDevices.VideoIndex = 0;
    
    LegacyVgaBDF &= 0xFFFFFFFE;
    VendorId = PciRead16(LegacyVgaBDF);
    DeviceId = PciRead16(LegacyVgaBDF + PCI_DID);
            
    Bus = (0xFF00000 & LegacyVgaBDF) >> 20;
    Device = (0xF8000 & LegacyVgaBDF) >> 15;
    Function = (0x7000 & LegacyVgaBDF) >> 12;
    
    gVgaDevices.VideoIndex = 0;
    gVgaDevices.DeviceList[0].Bus = (UINT8) Bus;
    gVgaDevices.DeviceList[0].Device = (UINT8) Device;
    gVgaDevices.DeviceList[0].Function = (UINT8) Function;
    gVgaDevices.DeviceList[0].VenId = (UINT16) VendorId;
    gVgaDevices.DeviceList[0].DevId = (UINT16) DeviceId;
    gVgaDevices.DeviceList[0].VgaTriedFlag = FALSE;
    
    
    VariableSize = sizeof(VGA_DEVICES_TRIED);
    Status = gRT->SetVariable (L"VgaList",
            &gDisableResourcesVgaListGuid,
             EFI_VARIABLE_NON_VOLATILE |
             EFI_VARIABLE_BOOTSERVICE_ACCESS,
             VariableSize,
             &gVgaDevices );
    
    return Status;
}

EFI_STATUS
GetVgaList()
{
    EFI_STATUS Status;
    UINTN      VariableSize;

    VariableSize = sizeof(VGA_DEVICES_TRIED); 
    Status = gRT->GetVariable(L"VgaList",
            &gDisableResourcesVgaListGuid,
            NULL,
            &VariableSize,
            &gVgaDevices
            );
    
    
    return Status;
}

VOID
ClearVgaList (
    VOID )
{
    EFI_STATUS Status;
    UINTN      VariableSize;
    
    VariableSize = 0;
    Status = gRT->SetVariable (L"VgaList",
            &gDisableResourcesVgaListGuid,
             EFI_VARIABLE_NON_VOLATILE |
             EFI_VARIABLE_BOOTSERVICE_ACCESS,
             VariableSize,
             &gVgaDevices );

}

/*
 * Input:
 *      NewVga
 *          -TRUE if need to use new VGA device
 *          -FALSE if need to use same VGA device
 */

EFI_STATUS
IncrementVgaList (
    BOOLEAN  NewVga )
{
    EFI_STATUS  Status;
    UINTN       Index;
    UINTN       VariableSize = sizeof(VGA_DEVICES_TRIED);
    
    Status = gRT->GetVariable(L"VgaList",
            &gDisableResourcesVgaListGuid,
            NULL,
            &VariableSize,
            &gVgaDevices
            );
    if(EFI_ERROR(Status)) return Status;
    
    Index = gVgaDevices.VideoIndex;
    
    //if NewVga is TRUE then we want to mark this device as tried
    //if NewVga is FALSE then we want to mark this device as not tried
    gVgaDevices.DeviceList[Index].VgaTriedFlag = NewVga;

    Status = gRT->SetVariable (L"VgaList",
            &gDisableResourcesVgaListGuid,
             EFI_VARIABLE_NON_VOLATILE |
             EFI_VARIABLE_BOOTSERVICE_ACCESS,
             VariableSize,
             &gVgaDevices );
    
    return EFI_SUCCESS;
}

EFI_STATUS
GetLastBootFailed (
    VOID )
{
    EFI_STATUS      Status;
    UINTN           VariableSize;
    
    VariableSize = sizeof(LAST_BOOT_FAILED_VAR);
    Status = gRT->GetVariable (L"LastBootFailed",
                 &gLastBootFailedGuid,
                 NULL,
                 &VariableSize,
                 &gLastBootFailedVar ); 
    
    return Status;
}

EFI_STATUS
SetLastBootFailed (
    UINTN    NumRootBridges,
    BOOLEAN  RbsFailed,
    BOOLEAN         DisableNextBoot,
    UINT8           Type
)
{
    UINTN       Index;
    EFI_STATUS  Status;    
    
    Status = GetLastBootFailed();
    if(NumRootBridges > MAX_ROOT_BRIDGE_COUNT){
        NumRootBridges = MAX_ROOT_BRIDGE_COUNT;
    }
    for(Index = 0; Index < NumRootBridges; Index++){
        gLastBootFailedVar.FailedRb[Index] = RbsFailed;
    }
    
    gLastBootFailedVar.DisableNextBoot = DisableNextBoot;
    
    gLastBootFailedVar.FailType = Type;
    Status = gRT->SetVariable (L"LastBootFailed",
                 &gLastBootFailedGuid,
                 EFI_VARIABLE_NON_VOLATILE |
                 EFI_VARIABLE_BOOTSERVICE_ACCESS,
                 sizeof(LAST_BOOT_FAILED_VAR),
                 &gLastBootFailedVar );
    
    return Status;
    
}
#endif //end OOR support

EFI_STATUS
HandleMMIO32 (
    IN PCI_HOST_BRG_DATA  *HostBrgData,
    IN PCI_ROOT_BRG_DATA  *RootBrgData,
    IN UINTN              RootBrgIndex )
{
    EFI_STATUS       Status;
    UINT8            Index;
    UINT8            Index2;
    UINT32           RegVal;
    ASLR_QWORD_ASD   *res;
    UINT32           RbMmioEstimatedBase;
    UINT32           RbMmioEstimatedBaseDelta;
    UINT64           RbMmioEstimatedPaddedBase;
    UINT64           RbMmioBaseForThisDevice;
    UINT64           RbMmioBaseForThisDie;
    UINT64           RbMmioActualPaddedBase;
    DXE_SERVICES    *dxe;
    UINT64           AddressLimit;
    BOOLEAN          NeedReDistribute;
    UINT64           AmiPciMmioResourceLengthForThisDie[16] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    UINT64           TOM;
    //UINT64           Above4GShrinkedSize;
    UINTN            Socket;
    UINTN            Rb;
    UINTN            PhyRb = 0;
    UINT64           NewTop;
    UINT64           AlignedBase;
    UINT8            LastPresentRB;
    UINT64           RbMmioEstimatedBaseTmp;
    UINT64           RbMmioEstimatedBaseCur;
    UINT64           TotalMmio32Size = 0;
    
    EFI_PCI_ROOT_BRIDGE_IO_PROTOCOL    *RtBrdg;
    PCI_ROOT_BRG_DATA                  *rbdata;
    FABRIC_RESOURCE_MANAGER_PROTOCOL   *AmdFabricResourceManager;
    FABRIC_ADDR_SPACE_SIZE             SpaceStatus;
    EFI_GCD_MEMORY_SPACE_DESCRIPTOR    GcdMemorySpaceDescriptor;
#if BoardPciRes_SUPPORT   
    BOOLEAN                            BootFailed = FALSE;
    BOOLEAN                            DisableDevicesNextBoot = FALSE;
#endif //end OOR support

    RtBrdg = NULL;
    rbdata = NULL;
    RegVal = 0;
    RbMmioEstimatedBase = 0xffffffff;
    RbMmioEstimatedPaddedBase = 0xffffffff;
    RbMmioBaseForThisDevice = 0xffffffff;
    RbMmioBaseForThisDie = 0xffffffff;
    RbMmioActualPaddedBase = 0xffffffff;
    AddressLimit = 0;
    LastPresentRB = 0;
    NeedReDistribute = FALSE;
    
    Status=LibGetDxeSvcTbl(&dxe);
    if(EFI_ERROR(Status)) {
      return Status;
    }

    RtBrdg = &RootBrgData->RbIoProtocol;

    NB_TRACE ((TRACE_NB, "==========HandleMMIO32 start==============\n"));

    for (Index = 0; Index < HostBrgData->RootBridgeCount; Index++) {
      rbdata = HostBrgData->RootBridges[Index];
      if(rbdata->NotPresent==0) {
        RtBrdg->Pci.Read(
                      RtBrdg,
                      EfiPciWidthUint32,
                      EFI_PCI_ADDRESS (
                        rbdata->RbSdlData->Bus,
                        rbdata->RbSdlData->Device,
                        rbdata->RbSdlData->Function,
                        0
                      ),
                      1,
                      &RegVal
                    );
        if(RegVal != 0xffffffff) {
          LastPresentRB = Index;
        }
      }
    }

    DEBUG ((DEBUG_INFO, "\nLastPresentRB = %x\n", LastPresentRB));

    for (Index = 0; Index < HostBrgData->RootBridgeCount; Index++) {
      //print this RB res data
      rbdata = HostBrgData->RootBridges[Index];
      if (rbdata->RbHandle == RootBrgData->RbHandle) {
        DEBUG ((DEBUG_INFO, "RBDATA #%x resource\n",Index));

        for (Index2 = 0; Index2 < RootBrgData->ResCount; Index2++){
          res=RootBrgData->RbRes[Index2];
          if(res->Type==ASLRV_SPC_TYPE_MEM) {
            if(!res->_LEN)
              continue;
            if(res->_GRA==32){
              DEBUG ((DEBUG_INFO,"MIN=0x%lX MAX=0x%lX LEN=0x%lX\n",res->_MIN, res->_MAX, res->_LEN));
            }
          }
        }
      }
    }

    //4.  When the system reaches last known RootBrgIndex, then cold reset if need to lower bottomIO,
    if(RootBrgIndex == LastPresentRB) {
      //Set PCIe BAR to MMIO in GCD
      AddressLimit = PCIEX_BASE_ADDRESS;
      Status=dxe->AllocateMemorySpace(
                    EfiGcdAllocateAddress,
                    EfiGcdMemoryTypeMemoryMappedIo,
                    0,
                    MultU64x32 (FabricTopologyGetNumberOfPciSegments(), SIZE_256MB),
                    &AddressLimit,
                    RootBrgData->ImageHandle,
                    RootBrgData->RbHandle
                  );
      if(EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR,"PciRootBrg: Memory Allocation Failed for PCIE\n"));
        return Status;
      }
      Status = dxe->GetMemorySpaceDescriptor (AddressLimit, &GcdMemorySpaceDescriptor);
      if (Status == EFI_SUCCESS) {
        dxe->SetMemorySpaceAttributes (GcdMemorySpaceDescriptor.BaseAddress,
                                                GcdMemorySpaceDescriptor.Length,
                                                GcdMemorySpaceDescriptor.Attributes | EFI_MEMORY_RUNTIME);
      }

      Status = gBS->LocateProtocol (
                      &gAmdFabricResourceManagerServicesProtocolGuid,
                      NULL,
                      &AmdFabricResourceManager
                    );
      if (EFI_ERROR (Status)) {
        return Status;
      }

      //estimate bottomio
      AddressLimit = PCIEX_BASE_ADDRESS -1; //  Die0 limit Default= 0xEFFF_FFFF
      RbMmioEstimatedBase  = (UINT32)AddressLimit + 1;

      DEBUG ((DEBUG_INFO, "\n==========Accumulate MMIO32 request==============\n"));

      for (Index = 0; Index < HostBrgData->RootBridgeCount; Index++) {
        //print all RB res data
        rbdata = HostBrgData->RootBridges[Index];
        if (rbdata->NotPresent==0) {
          DEBUG ((DEBUG_INFO, "====RBDATA %x resource====\n", Index));

          GetRootbridgeLocation (rbdata, Index, &Socket, &Rb, &PhyRb);

          RbMmioEstimatedBaseTmp = gFabricResource->PrefetchableMmioSizeBelow4G[Socket][Rb].Base;
          RbMmioEstimatedBaseCur = gFabricResource->PrefetchableMmioSizeBelow4G[Socket][Rb].Base;

		  DEBUG ((DEBUG_INFO, " RbMmioEstimatedBaseTmp = %X, RbMmioEstimatedBaseCur= %X \n",
                   RbMmioEstimatedBaseTmp,
                   RbMmioEstimatedBaseCur
                   ));
				   		  
          for (Index2 = 0; Index2 < rbdata->ResCount; Index2++) {
            res=rbdata->RbRes[Index2];
            if (res->Type==ASLRV_SPC_TYPE_MEM) {
              if(!res->_LEN) {
                continue;
              }
              if (res->_GRA==32){
                DEBUG ((DEBUG_INFO, "MIN=0x%lX MAX=0x%lX LEN=0x%lX\n",
                        res->_MIN,
                        res->_MAX,
                        res->_LEN
                      ));

                RbMmioEstimatedBase -= (UINT32)res->_LEN;
                //RbMmioEstimatedBaseDelta = RbMmioEstimatedBase;
                RbMmioEstimatedBase &= ~res->_MAX;
                if(RbMmioEstimatedBaseTmp != (RbMmioEstimatedBaseTmp&(~res->_MAX))) {
                    RbMmioEstimatedBaseTmp += res->_MAX+1;
                    RbMmioEstimatedBaseTmp &= ~res->_MAX;
                }
                
                RbMmioEstimatedBaseDelta = (UINT32)( RbMmioEstimatedBaseTmp - RbMmioEstimatedBaseCur);
               
			    AmiPciMmioResourceLengthForThisDie[Index] += (UINT32)res->_LEN;
                AmiPciMmioResourceLengthForThisDie[Index] += RbMmioEstimatedBaseDelta;  // Alignment

                RbMmioEstimatedBaseTmp += ((UINT32)res->_LEN + RbMmioEstimatedBaseDelta);
				RbMmioEstimatedBaseCur = RbMmioEstimatedBaseTmp;
				
		        DEBUG ((DEBUG_INFO, " RbMmioEstimatedBaseTmp = %X, RbMmioEstimatedBaseCur= %X \n",
                   RbMmioEstimatedBaseTmp,
                   RbMmioEstimatedBaseCur
                   ));
		  
                // Update alignment of DF resource with the maximum one
                if (res->_MAX >= gFabricResource->PrefetchableMmioSizeBelow4G[Socket][Rb].Alignment) {
                  gFabricResource->PrefetchableMmioSizeBelow4G[Socket][Rb].Alignment = res->_MAX;
                }
              }
            }
          }

          //once per RB print estimated bottom
          //DEBUG ((DEBUG_INFO,"  Estimated base= 0x%lX\n", RbMmioEstimatedBase));
          //RbMmioEstimatedPaddedBase = (UINT64)RbMmioEstimatedBase - ((UINT64)RbPaddingMmioLength[Index] << 25);
          //AmiPciMmioResourceLengthForThisDie[Index] += ((UINT64)RbPaddingMmioLength[Index] << 25);
          //DEBUG ((DEBUG_INFO,"  Estimated padded base= 0x%lX\n", RbMmioEstimatedPaddedBase));
          //RbMmioEstimatedBase = (UINT32)RbMmioEstimatedPaddedBase;

          DEBUG ((DEBUG_INFO, " Estimated size = %X, DF allocated size = 0x%X at 0x%X\n",
            AmiPciMmioResourceLengthForThisDie[Index],
            gFabricResource->PrefetchableMmioSizeBelow4G[Socket][Rb].Size,
            gFabricResource->PrefetchableMmioSizeBelow4G[Socket][Rb].Base
          ));
          
          // Check if current MMIO distribution satisfy current hardware configuration
          if ((AmiPciMmioResourceLengthForThisDie[Index]) > gFabricResource->PrefetchableMmioSizeBelow4G[Socket][Rb].Size) {
              NeedReDistribute = TRUE;
              DEBUG ((DEBUG_INFO, "Socket %x RB %x requires redistribution \n", Socket,Rb));
          }

          // Check#2 - max alignment and current DF base alignment
          if(NeedReDistribute == FALSE){
              AlignedBase = gFabricResource->PrefetchableMmioSizeBelow4G[Socket][Rb].Base &~gFabricResource->PrefetchableMmioSizeBelow4G[Socket][Rb].Alignment;
              if(AlignedBase < gFabricResource->PrefetchableMmioSizeBelow4G[Socket][Rb].Base ){
                  NeedReDistribute = TRUE;
                  DEBUG ((DEBUG_INFO, "Socket %x RB %x requires redistribution due to alignment\n", Socket,Rb));
              }
          }
          DEBUG ((DEBUG_INFO, "====RBDATA %x resource Done====\n\n", Index));
        }
      }

      // The pre-distribution can't satisfy hardware request, need to re-distribute
      if (NeedReDistribute) {
        DEBUG ((DEBUG_INFO, "==========Adjust MMIO32 allocation==========\n"));
        for (Index = 0; Index < HostBrgData->RootBridgeCount; Index++) {
          // Clear all below 4G size to 0 before re-distribute
          rbdata = HostBrgData->RootBridges[Index];
          if(rbdata->NotPresent==0) {
            GetRootbridgeLocation (rbdata, Index, &Socket, &Rb, &PhyRb);
            DEBUG ((DEBUG_INFO, "RB %x requires 0x%lX, we have 0x%lX\n",
                    Index,
                    AmiPciMmioResourceLengthForThisDie[Index],
                    gFabricResource->PrefetchableMmioSizeBelow4G[Socket][Rb].Size));
            gFabricResource->PrefetchableMmioSizeBelow4G[Socket][Rb].Size = 0;
          }
        }
        for (Index = 0; Index < HostBrgData->RootBridgeCount; Index++) {
          // Adjust below 4G size for each Die
          rbdata = HostBrgData->RootBridges[Index];
          if(rbdata->NotPresent == 0) {
            GetRootbridgeLocation (rbdata, Index, &Socket, &Rb, &PhyRb);
            gFabricResource->PrefetchableMmioSizeBelow4G[Socket][Rb].Size += AmiPciMmioResourceLengthForThisDie[Index];
            gFabricResource->NonPrefetchableMmioSizeBelow4G[Socket][Rb].Size = 0;
          }
        }

        DEBUG ((DEBUG_INFO, "==========Adjust MMIO32 allocation Done==========\n"));

        // Call AGESA function to reallocate MMIO size
        DEBUG ((DEBUG_INFO, "Call AGESA function to reallocate MMIO size\n"));
        Status = AmdFabricResourceManager->FabricReallocateResourceForEachRb (
                                            AmdFabricResourceManager,
                                            gFabricResource,
                                            &SpaceStatus
                                           );

        if (!EFI_ERROR(Status)) {
          DEBUG ((DEBUG_INFO, "  Reallocate MMIO32 success\n"));
#if BoardPciRes_SUPPORT
          Status = GetLastBootFailed();
          if (EFI_ERROR(Status)){
              ASSERT(FALSE);
          }
          //Check if we disabled bridges this boot
          //if we did then set flag to disable them next boot too
          if(gLastBootFailedVar.DisableNextBoot == TRUE){
              DEBUG((DEBUG_INFO, "PciOOR: Reallocation succeeded. Disabling same devices next boot\n"));
              SetLastBootFailed(HostBrgData->RootBridgeCount, TRUE, TRUE, ASLRV_SPC_TYPE_MEM);
              
              //Try same VGA
              IncrementVgaList(FALSE);
          }    
#endif //end OOR support
          
          gRT->ResetSystem(EfiResetWarm, EFI_SUCCESS, 0, NULL);
        } else {
          DEBUG ((DEBUG_INFO, "  Adjust Bottom IO\n"));
          TOM = AsmReadMsr64 (0xC001001A);
          TotalMmio32Size = (UINT64)SpaceStatus.MmioSizeBelow4GReqInc + (UINT64)SpaceStatus.MmioSizeBelow4G;
          AmdFabricResourceManager->FabricReallocateResourceForEachRb (
                                              AmdFabricResourceManager,
                                              gFabricResource,
                                              &SpaceStatus
                                             );
          //SpaceStatus.MmioSizeBelow4GReqInc gives us the required MMIO adjustment needed below 4GB.
          RbMmioEstimatedBase = (UINT32)TOM - SpaceStatus.MmioSizeBelow4GReqInc;
          if ((RbMmioEstimatedBase < BOTTOM_MMIO_LIMIT)||(TotalMmio32Size > (0x100000000 - BOTTOM_MMIO_LIMIT))) {
              RbMmioEstimatedBase = BOTTOM_MMIO_LIMIT;

              DEBUG((DEBUG_INFO, "ERROR: PciOOR EstimatedBottomIO is below BOTTOM_MMIO_LIMIT\n"));
#if BoardPciRes_SUPPORT
              
              BootFailed = TRUE;
              DisableDevicesNextBoot = TRUE;
              
              //Check to see if this is our first boot or if we have already attempted to disable devices
              GetLastBootFailed();
              if(gLastBootFailedVar.DisableNextBoot == TRUE){
                  //If we already disabled bridges try new VGA device to see if it passed allocation
                  IncrementVgaList(TRUE);
                  DEBUG((DEBUG_INFO, "PciOOR: Allocation failed. Attempting to reboot with new VGA\n"));
              } else {
                  //If this is our first boot then try first VGA device
                  IncrementVgaList(FALSE);
                  Status = FirstBootSetVgaList();
                  if (EFI_ERROR(Status)){
                      DEBUG((DEBUG_ERROR, "Status of FirstBootSetVgaList: %r\nPciOOR: Allocation failed. Rebooting and enabling first VGA device\n", Status));
                      //ASSERT(FALSE);
                  }
                  
              }
#endif //end OOR support
          }
#if BoardPciRes_SUPPORT
          SetLastBootFailed(HostBrgData->RootBridgeCount, BootFailed, DisableDevicesNextBoot, ASLRV_SPC_TYPE_MEM);
#endif //end OOR support
          
          DoColdReset(RbMmioEstimatedBase);
#if BoardPciRes_SUPPORT          
          if(BootFailed) gRT->ResetSystem(EfiResetWarm, EFI_SUCCESS, 0, NULL);
#endif //end OOR support
        }
      } //end if(NeedToRedestribute)
      
      // Check if IO redistribution required
      if(mIoReallocationReq) {
          DEBUG((DEBUG_INFO, "IO re-allocation required, reset system..\n"));
          gRT->ResetSystem(EfiResetWarm, EFI_SUCCESS, 0, NULL);
      }
      
      //if not continue and program DF regs and fill ACPI RB and RES data
      //GCD allocation
      
#if BoardPciRes_SUPPORT      
      //If allocation succeeds make sure we do not disable again
      GetLastBootFailed();
      if(gLastBootFailedVar.DisableNextBoot == TRUE){
          
          GetVgaList();
          if(gVgaDevices.VideoFound){
              SetLastBootFailed(HostBrgData->RootBridgeCount, FALSE, FALSE, ASLRV_SPC_TYPE_MEM);
              ClearVgaList();
          } else {
              DEBUG((DEBUG_ERROR, "PciOOR: System cannot allocate resources with any video devices enabled!!\n"));
              ASSERT(FALSE);
              EFI_DEADLOOP();
          }
      }
#endif //end OOR support

      for (Index = 0; Index < HostBrgData->RootBridgeCount; Index++) {
        //print all RB res data
        rbdata = HostBrgData->RootBridges[Index];
        if(rbdata->NotPresent==0) {
          DEBUG ((DEBUG_VERBOSE, "\n==========RBDATA %x resource============\n", Index));

          GetRootbridgeLocation (rbdata, Index, &Socket, &Rb, &PhyRb);

          RbMmioBaseForThisDie = gFabricResource->PrefetchableMmioSizeBelow4G[Socket][Rb].Base;
          //RbMmioBaseForThisDevice = RbMmioBaseForThisDie + AmiPciMmioResourceLengthForThisDie[Index];
          NewTop = RbMmioBaseForThisDie + AmiPciMmioResourceLengthForThisDie[Index];;
          RbMmioBaseForThisDevice = gFabricResource->PrefetchableMmioSizeBelow4G[Socket][Rb].Base;
          //Set limit before starting RB res allocations

          for (Index2 = 0; Index2 < rbdata->ResCount; Index2++){
            res=rbdata->RbRes[Index2];
            if(res->Type==ASLRV_SPC_TYPE_MEM) {
              if(!res->_LEN)
                  continue;
              if(res->_GRA==32){
                DEBUG ((DEBUG_VERBOSE, "MIN=0x%lX MAX=0x%lX LEN=0x%lX\n",res->_MIN, res->_MAX, res->_LEN));
                
                //RbMmioBaseForThisDevice = RbMmioEstimatedBase1 + (UINT32)res->_LEN;
                
                if(RbMmioBaseForThisDevice != (RbMmioBaseForThisDevice&(~res->_MAX))) {
                    RbMmioBaseForThisDevice += res->_MAX+1;
                    RbMmioBaseForThisDevice &= ~res->_MAX;
                }
             
                if( NewTop < (RbMmioBaseForThisDevice + res->_LEN) ) {
                    DEBUG ((DEBUG_VERBOSE, "Error: Calculated MMIO lengnth came bigger during GCD memory allocation \n"));
                }
                
                //RbMmioBaseForThisDevice -= res->_LEN;
                //RbMmioBaseForThisDevice &= ~res->_MAX;
                Status=dxe->AllocateMemorySpace(
                              EfiGcdAllocateAddress,
                              EfiGcdMemoryTypeMemoryMappedIo,
                              AlignFromGra((UINTN)res->_MAX),
                              res->_LEN,
                              &RbMmioBaseForThisDevice,
                              rbdata->ImageHandle,
                              rbdata->RbHandle
                            );
                if(EFI_ERROR(Status)) {
                  DEBUG((DEBUG_VERBOSE,"PciRootBrg: Memory Allocation Failed: Base: %lX Length: %lX\n", RbMmioBaseForThisDevice, res->_LEN));
                  //Test to see if how GCD will fail if try allocating into mem space. May need to do bottomio reset here.
                  Status = EFI_OUT_OF_RESOURCES;
                  return Status;
                }

                DEBUG ((DEBUG_VERBOSE, "GCD AllocateMemorySpace - BaseAddress = 0x%x", RbMmioBaseForThisDevice));
                rbdata->AcpiRbRes[raMmio32].AddrUsed=RbMmioBaseForThisDevice;

                Status=dxe->SetMemorySpaceAttributes(RbMmioBaseForThisDevice,res->_LEN,EFI_MEMORY_UC);
                ASSERT_EFI_ERROR(Status);
                if(EFI_ERROR(Status)) return Status;

                res->_MIN = RbMmioBaseForThisDevice;
                DEBUG ((DEBUG_VERBOSE, "\nMMIO32: _MIN=0x%lX; _MAX=0x%lX; _LEN=0x%lX; _GRA=0x%lX\n",
                res->_MIN,res->_MAX,res->_LEN, res->_GRA ));
                
                RbMmioBaseForThisDevice = RbMmioBaseForThisDevice + res->_LEN;
                        
                // Check for case when due to granularity resource allocated from bottom,
                // next resource for same RB will be attempted to locate below this and fail
                // if that RB bottom equal to global MMIO bottom
                //if(RbMmioBaseForThisDevice != RbMmioBaseForThisDie){
                //    NewTop = RbMmioBaseForThisDevice;
                //} else {
                //    RbMmioBaseForThisDevice = NewTop;
                //}
                
              } //end if(res->_GRA==32){
            } // end if MMIO32
          } // for each resource in 1 RB

          if(rbdata->AcpiRbRes[raMmio32].Min == 0) {
            rbdata->AcpiRbRes[raMmio32].Min=RbMmioBaseForThisDie;
            rbdata->AcpiRbRes[raMmio32].Max= RbMmioBaseForThisDie + AmiPciMmioResourceLengthForThisDie[Index] - 1;
            rbdata->AcpiRbRes[raMmio32].Len= AmiPciMmioResourceLengthForThisDie[Index];
            rbdata->AcpiRbRes[raMmio32].AllocType=EfiGcdAllocateAddress;
          }
        } // end if present RB
      } //for each RB

      DEBUG ((DEBUG_VERBOSE, "==========HandleMMIO32 end==============\n"));
      return EFI_SUCCESS;
    } // end last RB

    DEBUG ((DEBUG_VERBOSE, "==========HandleMMIO32 end EFI_NOT_READY ==============\n"));
    return EFI_NOT_READY;
}

