TOKEN
	Name  = "<PERSON><PERSON><PERSON>_SUPPORT"
	Value  = "1"
	Help  = "Main switch to enable Chalupa support in Project"
	TokenType = Boolean
	TargetEQU = Yes
	TargetMAK = Yes
	TargetH = Yes
	Master = Yes
	Token = "PLATFORM_SELECT" "=" "1"
End

TOKEN
	Name  = "PLATFORM_NAME"
	Value  = "Chalupa"
	TokenType = Expression
	TargetMAK = Yes
	TargetDSC = Yes
End

TOKEN
	Name  = "PROJECT_TAG"
	Value  = "0ACSU"
	TokenType = Expression
	TargetMAK = Yes
	TargetH = Yes
End

TOKEN
	Name  = "PLATFORM_GUID"
	Value = "9ddc3d5a-1247-437f-b871-b868f491fade"
	Help  = "The unique platform GUID that does into the description file.\MUST BE CLONED IN EVERY PROJECT!"
	TokenType = Expression
	TargetDSC = Yes
End

PCIDEVICE
	Title  = "AST2600 P2P"
	Parent = "WAFL x1 PCIe GPP Bridge 1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 00h
	SleepNum  = 01h
	IntA =  LNKC; 66
	IntB =  LNKD; 67
	IntC =  LNKA; 64
	IntD =  LNKB; 65
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	Help  = ""
#	InitRoutine  = "BMC_Init"
End

PCIDEVICE
	Title  = "AST2600 VGA"
	Parent  = "AST2600 P2P"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 00h
	SleepNum  = 01h
	ASLdeviceName  = "VGFX"
	ROMFile  = "TurinPkg/uefi_2600.ROM"
	IntA =  LNKC; 66
	IntB =  LNKD; 67
	IntC =  LNKA; 64
	IntD =  LNKB; 65
	DeviceType = OnBoard
	PCIBusSize = 32bit
	OptionROM = Yes
	ROMMain = No
	PCIExpress = Yes
	SmbiosStr = Yes
	LegacyRom = Yes
#	UefiRom = Yes
	FFSFileName  = "ROM Storage"
	ROMSectionGuid  = "577dc59e-5260-4783-bf52-7301ec9d6be5"
	SMBIOSString  = "Onboard VGA"
	Help  = ""
End


PCIDEVICE
	Title  = "BCM5720 GigE"
	Parent  = "WAFL x1 PCIe GPP Bridge 2"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	ROMFile  = "TurinPkg/b57pxee.rom"
	Dev  = 00h
	IntA =  LNKG; 62
	IntB =  LNKH; 63
	IntC =  LNKE; 60
	IntD =  LNKF; 61
	DeviceType = OnBoard
	OptionROM = Yes
	LegacyRom = Yes
	FFSFileName  = "ROM Storage"
	ROMSectionGuid  = "577dc59e-5260-4783-bf52-7301ec9d6bea"
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
End

PCIDEVICE
	Title  = "BCM5720 GigE UEFI"
	Parent  = "WAFL x1 PCIe GPP Bridge 2"
	Attribute  = "0x0"
	Dev_type  = "Container"
	Dev  = 00h
	Fun  = 00h
	ROMFile  = "TurinPkg/b57undix64.efi"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	OptionROM = Yes
	LegacyRom = No
	UefiRom = Yes
	ROMMain = No
	Virtual = Yes
	PCIExpress = Yes    
	FFSFileName  = "ROM Storage"
	ROMSectionGuid  = "4633054d-d2ca-4af1-a133-************"
End

PCIDEVICE
	Title  = "Slot1 x16"
	Parent = "PCIe GPP Bridge 0 Socket0 RBC0 TypeC"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 0h
	Slot  = 01h
	ASLdeviceName  = "SLT1"
	ASLfile = "'TurinPkg/Asl/CrbPcieSlot.asl'"
	IntA =  LNKA; 24
	IntB =  LNKB; 25
	IntC =  LNKC; 26
	IntD =  LNKD; 27
	DeviceType = Slot
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	HasSetup = Yes
	Help  = "Chalupa P0 RBC0 - Slot1"
End

PCIDEVICE
	Title  = "Slot2 x16"
	Parent  = "PCIe GPP Bridge 0 Socket0 RBC5 TypeB"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 0h
	Slot  = 02h
	ASLdeviceName  = "SLT2"
	ASLfile = "'TurinPkg/Asl/CrbPcieSlot.asl'"
	IntA =  LNKA; 184
	IntB =  LNKB; 185
	IntC =  LNKC; 186
	IntD =  LNKD; 187
	DeviceType = Slot
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	HasSetup = Yes
	Help  = "Chalupa P1 RBC5 - Slot2"
End

PCIDEVICE
	Title  = "Slot3 x16"
	Parent  = "PCIe GPP Bridge 0 Socket0 RBC2 TypeD"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 0h
	Slot  = 03h
	ASLdeviceName  = "SLT3"
	ASLfile = "'TurinPkg/Asl/CrbPcieSlot.asl'"
	IntA =  LNKA; 88
	IntB =  LNKB; 89
	IntC =  LNKC; 90
	IntD =  LNKD; 91
	DeviceType = Slot
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	HasSetup = Yes
	Help  = "Chalupa P2 RBC2 - Slot3"
End

PCIDEVICE
	Title  = "Slot4 x16"
	Parent = "PCIe GPP Bridge 0 Socket0 RBC7 TypeB"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 0h
	Slot = 04h
	ASLdeviceName  = "SLT4"
	ASLfile = "'TurinPkg/Asl/CrbPcieSlot.asl'"
	IntA =  LNKA; 248
	IntB =  LNKB; 249
	IntC =  LNKC; 250
	IntD =  LNKD; 251
	DeviceType = Slot
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	HasSetup = Yes
	Help  = "Chalupa P3 RBC7 - Slot4"
End

PCIDEVICE
	Title  = "NVMe M.2 MSLOT1"
	Parent  = "PCIe GPP Bridge 0"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 0h
	Slot  = 0Ah
	ASLfile = "'TurinPkg/Asl/CrbPcieSlot.asl'"
	IntA =  LNKC; 74
	IntB =  LNKD; 75
	IntC =  LNKA; 72
	IntD =  LNKB; 73
	DeviceType = Slot
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	HasSetup = Yes
	Help  = "Chalupa Socket0 - P4 NVMe M.2"
End

PCIDEVICE
	Title  = "NVMe M.2 MSLOT2"
	Parent  = "PCIe GPP Bridge 2 Socket1 RBC1 TypeD"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 0h
	Slot  = 0Bh
	ASLfile = "'TurinPkg/Asl/CrbPcieSlot.asl'"
	IntA =  LNKC; 322
	IntB =  LNKD; 323
	IntC =  LNKA; 320
	IntD =  LNKB; 321
	DeviceType = Slot
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	HasSetup = Yes
	Help  = "Chalupa Socket1 P5 NVMe M.2"
End

PCIDEVICE
	Title  = "NVMe M.2 MSLOT3"
	Parent  = "PCIe GPP Bridge 0 Socket1 RBC1 TypeD"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 0h
	Slot  = 0Ch
	ASLfile = "'TurinPkg/Asl/CrbPcieSlot.asl'"
	IntA =  LNKG; 330
	IntB =  LNKH; 331
	IntC =  LNKE; 328
	IntD =  LNKF; 329
	DeviceType = Slot
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	HasSetup = Yes
	Help  = "Chalupa Socket1 - P4 NVMe M.2"
End

TOKEN
	Name  = "OnBoardSerial_SUPPORT"
	Value  = "1"
	Help  = "For OnBoardS otherwise 0x3F8."
	TokenType = Boolean
	TargetEQU = Yes
	TargetH = Yes
End

TOKEN
	Name  = "ExternalSerial_SUPPORT"
	Value  = "0"
	Help  = "For External AMD 0x3F8."
	TokenType = Boolean
	TargetEQU = Yes
	TargetH = Yes
End

TOKEN
	Name  = "SioSerial_SUPPORT"
	Value  = "0"
	Help  = "For SIO 0x3F8."
	TokenType = Boolean
	TargetEQU = Yes
	TargetH = Yes
End

TOKEN
	Name  = "AST2600_SUPPORT"
	Value  = "0"
	Help  = "Main switch to enable AST2600 support in Project"
	TokenType = Boolean
	TargetEQU = Yes
	TargetH = Yes
	Token = "SioSerial_SUPPORT" "=" "0"
End

TOKEN
	Name  = "===================== SMBios.sdl ====================="
	Value = "======================================================"
	TokenType = Expression
End

TOKEN
	Name  = "BASE_BOARD_PRODUCT_NAME"
	Value  = "Chalupa"
	Help  = "Specifies the Product Name"
	TokenType = Expression
	TargetEQU = Yes
	Token = "BASE_BOARD_INFO" "=" "1"
End

TOKEN
	Name  = "SYSTEM_PRODUCT_NAME"
	Value  = "Chalupa"
	Help  = "Specifies the System Product Name"
	TokenType = Expression
	TargetEQU = Yes
End

TOKEN
	Name  = "ONBOARD_DEVICE_EXT_COUNT"
	Value  = "2"
	Help  = "Number of Onboard Devices"
	TokenType = Integer
	TargetEQU = Yes
	TargetH = Yes
	Range  = "0 - 16"
End

TOKEN
	Name   = "================== ACPI Configurations =================="
	Value  = "========================================================="
	TokenType = Expression
End

TOKEN
	Name  = "T_ACPI_OEM_TBL_ID"
	Value  = "Chalupa"
	Help  = "ACPI OEM Id -\a string value to be filled into ACPI table headers"
	TokenType = Expression
	TargetMAK = Yes
	TargetH = Yes
	Range  = "1-8 characters"
End

#PcdMapping
#	Name  = "PcdAcpiDefaultOemTableId"
#	GuidSpace  = "gEfiMdeModulePkgTokenSpaceGuid"
#	PcdType  = "PcdsDynamicDefault"
#	Value  = "0x656c626154646D41"   #"AmdTable" was 'A M I ' = 0x2049204D2041
#	Arch  = "common"
#	Offset  = 00h
#	Length  = 00h
#	TargetDSC = Yes
#End

TOKEN
	Name   = "================== AMD CPM =================="
	Value  = "========================================================="
	TokenType = Expression
End

PcdMapping
	Name  = "PcdAmdAcpiHestTableHeaderOemTableId"
	GuidSpace  = "gAmdCpmPkgTokenSpaceGuid"
	PcdType  = "PcdsFixedAtBuild"
	Value  = "AmdTable"
	Arch  = "common"
	Offset  = 00h
	Length  = 00h
	TargetDSC = Yes
End

PcdMapping
	Name  = "PcdAmdAcpiBertTableHeaderOemTableId"
	GuidSpace  = "gAmdCpmPkgTokenSpaceGuid"
	PcdType  = "PcdsFixedAtBuild"
	Value  = "AmdTable"
	Arch  = "common"
	Offset  = 00h
	Length  = 00h
	TargetDSC = Yes
End

PcdMapping
	Name  = "PcdAmdAcpiEinjTableHeaderOemTableId"
	GuidSpace  = "gAmdCpmPkgTokenSpaceGuid"
	PcdType  = "PcdsFixedAtBuild"
	Value  = "AmdTable"
	Arch  = "common"
	Offset  = 00h
	Length  = 00h
	TargetDSC = Yes
End

PATH
	Name = "APCB_SCRIPT_DIR"
	path = "AgesaPkg/Addendum/Apcb/TurinSp5Rdimm"
End

PcdMapping
	Name  = "PcdEarlyBmcLinkTraining"
	GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
	PcdType  = "PcdsDynamicDefault"
	DataType  = "Boolean"
	Value  = "TRUE"
	Offset  = 00h
	Length  = 01h
	TargetDSC = Yes
End

PcdMapping
	Name  = "PcdEarlyBmcLinkSocket"
	GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
	PcdType  = "PcdsDynamicDefault"
	DataType  = "Uint8"
	Value  = "0x0"
	Offset  = 00h
	Length  = 01h
	TargetDSC = Yes
End

PcdMapping
	Name  = "PcdEarlyBmcLinkDie"
	GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
	PcdType  = "PcdsDynamicDefault"
	DataType  = "Uint8"
	Value  = "0x0"
	Help  = "It is MilanCPU Config setting"
	Offset  = 00h
	Length  = 01h
	TargetDSC = Yes
End

PcdMapping
	Name  = "PcdEarlyBmcLinkLaneNum"
	GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
	PcdType  = "PcdsDynamicDefault"
	DataType  = "Uint8"
	Value  = "134"
	Help  = "It is MilanCPU Config setting"
	Offset  = 00h
	Length  = 01h
	TargetDSC = Yes
End

TOKEN
	Name  = "NUMBER_OF_EXT_PORT_CONNECTORS"
	Value  = "4"
	Help  = "Number of External Port Connectors in the System."
	TokenType = Integer
	TargetEQU = Yes
	Range  = "0 - 32"
End

TOKEN
	Name  = "NUMBER_OF_INT_PORT_CONNECTORS"
	Value  = "0"
	Help  = "Number of Internal Port Connectors in the System."
	TokenType = Integer
	TargetEQU = Yes
	Range  = "0 - 32"
End

TOKEN
	Name  = "NUMBER_OF_SYSTEM_SLOTS"
	Value  = "4"
	Help  = "Identifies the number of system slots available on platform including the AGP slot."
	TokenType = Integer
	TargetEQU = Yes
	TargetH = Yes
End

TOKEN
	Name  = "NUMBER_OF_DIMMS_PER_CHANNEL"
	Value  = "1"
	Help  = "Number of DIMMs supported per channel"
	TokenType = Integer
	TargetH = Yes
End

### SMBIOS Out of resources change
TOKEN
	Name  = "EXTRA_RESERVED_BYTES"
	Value  = "0x4000"
	Help  = "Number of extra bytes to reserve in addition to the static table."
	TokenType = Integer
	TargetEQU = Yes
	TargetH = Yes
End

TOKEN
	Name  = "SMBIOS_TABLE_LOCATION"
	Value  = "0"
	Help  = "SMBIOS Table (version 2.X) location: \0 = Above 1MB only \1 = Auto (E000 Segment, if unable to allocate to E000, then put table above 1MB)"
	TokenType = Integer
	TargetH = Yes
End
###

PcdMapping
	Name  = "PcdSataEnable2"
	GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
	PcdType  = "PcdsDynamicDefault"
	DataType = "UINT8"
	Value  = 0xFF
	Offset  = 00h
	Length  = 00h
	TargetDSC = Yes
End

PcdMapping
	Name  = "PcdSataMultiDiePortRxPolarity"
	GuidSpace  = "gEfiAmdAgesaModulePkgTokenSpaceGuid"
	PcdType  = "PcdsDynamicDefault"
	DataType = "UINT64"
	Value  = 0xFFFFFFFFFFFFFFFF
	Offset  = 00h
	Length  = 00h
	TargetDSC = Yes
End

PcdMapping
	Name  = "PcdSataMultiDiePortESP"
	GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
	PcdType  = "PcdsDynamicDefault"
	DataType = "UINT64"
	Value  = 0x0000FFFF00000000
	Offset  = 00h
	Length  = 00h
	TargetDSC = Yes
End

PcdMapping
	Name  = "PcdAmdFchI2c0SdaHold"
	GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
	PcdType  = "PcdsDynamicDefault"
	DataType  = "Uint32"
	Value  = "0x00000035"
	Offset  = 00h
	Length  = 04h
	TargetDSC = Yes
End

PcdMapping
	Name  = "PcdAmdFchI2c1SdaHold"
	GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
	PcdType  = "PcdsDynamicDefault"
	DataType  = "Uint32"
	Value  = "0x00000035"
	Offset  = 00h
	Length  = 04h
	TargetDSC = Yes
End

PcdMapping
	Name  = "PcdAmdFchI2c2SdaHold"
	GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
	PcdType  = "PcdsDynamicDefault"
	DataType  = "Uint32"
	Value  = "0x00000035"
	Offset  = 00h
	Length  = 04h
	TargetDSC = Yes
End

PcdMapping
	Name  = "PcdAmdFchI2c3SdaHold"
	GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
	PcdType  = "PcdsDynamicDefault"
	DataType  = "Uint32"
	Value  = "0x00000035"
	Offset  = 00h
	Length  = 04h
	TargetDSC = Yes
End

PcdMapping
	Name  = "PcdAmdFchI2c4SdaHold"
	GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
	PcdType  = "PcdsDynamicDefault"
	DataType  = "Uint32"
	Value  = "0x00000035"
	Offset  = 00h
	Length  = 04h
	TargetDSC = Yes
End

PcdMapping
	Name  = "PcdAmdFchI2c5SdaHold"
	GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
	PcdType  = "PcdsDynamicDefault"
	DataType  = "Uint32"
	Value  = "0x00000035"
	Offset  = 00h
	Length  = 04h
	TargetDSC = Yes
End

# Add Bit5  for enabling I2C0  (EGPIO0146 pin -> I2C0 pin)
# Add Bit6  for enabling I2C1  (EGPIO0148 pin -> I2C1 pin)
# Add Bit7  for enabling I2C2  (EGPIO0114 pin -> I2C2 pin)
# Add Bit8  for enabling I2C3  (AGPIO020  pin -> I2C3 pin)
# Add Bit9  for enabling I2C4  (EGPIO0150 pin -> I2C4 pin)
# Add Bit10 for enabling I2C5  (EGPIO0152 pin -> I2C5 pin)

TOKEN
	Name  = "FchRTDeviceEnableMapToken"
	Value  = "0x1F60"             # Or Bit10,Bit9,Bit8,Bit6,Bit5
	TokenType = Integer
	TargetH = Yes
	Range  = "0...0FFFFh"
End

PcdMapping
	Name  = "PcdAmdMemMaxDimmPerChannelV2"
	GuidSpace  = "gEfiAmdAgesaModulePkgTokenSpaceGuid"
	PcdType  = "PcdsFixedAtBuild"
	DataType = "UINT8"
	Value  = "1"
	Offset  = 00h
	Length  = 01h
	TargetDSC = Yes
End

PcdMapping
	Name  = "PcdCfgPlatformTDP"
	GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
	PcdType  = "PcdsDynamicDefault"
	DataType  = "Uint32"
	Value  = "500"                   ## Support 500W
	Offset  = 00h
	Length  = 04h
	TargetDSC = Yes
End

PcdMapping
	Name  = "PcdCfgPlatformPPT"
	GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
	PcdType  = "PcdsDynamicDefault"
	DataType  = "Uint32"
	Value  = "500"                   ## Support 500W
	Offset  = 00h
	Length  = 04h
	TargetDSC = Yes
End

PcdMapping
	Name  = "PcdCfgPlatformTDC"
	GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
	PcdType  = "PcdsDynamicDefault"
	DataType  = "Uint32"
	Value  = "235"                   ## Support 235W
	Offset  = 00h
	Length  = 04h
	TargetDSC = Yes
End

PcdMapping
	Name  = "PcdCfgPlatformEDC"
	GuidSpace  = "gEfiAmdAgesaPkgTokenSpaceGuid"
	PcdType  = "PcdsDynamicDefault"
	DataType  = "Uint32"
	Value  = "330"                   ## Support 330W
	Offset  = 00h
	Length  = 04h
	TargetDSC = Yes
End

TOKEN
	Name  = "NSOCKETS"
	Value  = "2" #To Do :The value should be "2" in 2P configuration.
	Help  = "Number of CPU sockets in the system."
	TokenType = Integer
	TargetEQU = Yes
	TargetH = Yes
	TargetMAK = Yes
	Range  = "1-8"
End

TOKEN
	Name  = "NCPU"
	Value  = "768" # 16CCDs 192 Cores 384 threads and 2P.
	Help  = "NOTE: This should be equal to Number of Cores in the System\"
	TokenType = Integer
	TargetEQU = Yes
	TargetH = Yes
	Range  = "1- number of cpu in the system"
End

PcdMapping
	Name  = "PcdCpuMaxLogicalProcessorNumber"
	GuidSpace  = "gUefiCpuPkgTokenSpaceGuid"
	PcdType  = "PcdsDynamicDefault"
	Value  = "$(NCPU)"
	Offset  = 00h
	Length  = 00h
	TargetDSC = Yes
End

TOKEN
	Name  = "SERIAL_PORT_ESPI_CONTROLLER_VALUE"
	Value  = "0x18"
	TokenType = Integer
	TargetH = Yes
End

TOKEN
	Name  = "SECSB_SIO_ESPI0_2E2F"
	Value  = "1"
	Help  = "Enable 0x2E/2F IO decoding in eSPI controller 0 in SEC"
	TokenType = Boolean
	TargetEQU = Yes
	TargetH = Yes
	Token = "FCH_UART_DEBUG_SELECT" "=" "3"
End

TOKEN
	Name  = "VGA_ROOT_PORT_FUN"
	Value  = "0x3"
	Help  = "Function number for Root Port where Aspeed VGA connected"
	TokenType = Integer
	TargetH = Yes
End

PcdMapping
    Name  = "PcdXhciUsb20OcPinSelect"
    GuidSpace  = "gEfiAmdAgesaModulePkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType = "UINT64"
    Value  = "0xFFFFFFFFFFFF1010"
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdXhciUsb31OcPinSelect"
    GuidSpace  = "gEfiAmdAgesaModulePkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType = "UINT32"
    Value  = "0xFFFF1010"
    TargetDSC = Yes
End

PcdMapping
    Name  = "PcdXhciOcPolarityCfgLow"
    GuidSpace  = "gEfiAmdAgesaModulePkgTokenSpaceGuid"
    PcdType  = "PcdsDynamicDefault"
    DataType = "BOOLEAN"
    Value  = "TRUE"
    TargetDSC = Yes
End