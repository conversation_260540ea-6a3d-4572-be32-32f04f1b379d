/*****************************************************************************
 *
 * Copyright (C) 2018-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#ifndef _AMD_PLATFORM_RAS_BRH_SMM_DRIVER_H_
#define _AMD_PLATFORM_RAS_BRH_SMM_DRIVER_H_

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Protocol/SmmBase2.h>
#include <Protocol/SmmCpu.h>
#include <Library/UefiBootServicesTableLib.h>
#include <Library/SmmServicesTableLib.h>
#include <Library/DebugLib.h>

#include <Protocol/FchSmmSwDispatch2.h>
#include <Protocol/FchSmmApuRasDispatch.h>
#include <Protocol/FchSmmMiscDispatch.h>
#include <Protocol/AmdRasServiceSmmProtocol.h>
#include <Protocol/AmdPspArsServiceProtocol.h>
#include <Protocol/RasMaintPcieActivePortMapProtocol.h>
#include <Protocol/AmdNbioPcieAerProtocol.h>
#include <Protocol/AmdAsyncHpEdrProtocol.h>

#include "AmdRasRegistersBrh.h"
#include "AmdMpioSmiDef.h"
#include <Library/CpmRasLib.h>
#include <Library/CpmRasPciLib.h>
#include <Library/CpmRasCxlLib.h>
#include "AmdRas.h"
#include "AmdCpmRas.h"
#include <Library/RasSmmLib.h>

#include <Library/AgesaConfigLib.h>
#include <ActOptions.h>
#include <Library/PciSegmentLib.h>
#include <Library/IdsLib.h>
#include <Library/AmdPspMboxLibV2.h>
#include <Protocol/PspMboxSmmBufferAddressProtocol.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */

typedef enum _SMI_MODE {
  INTERRUPT_MODE = 1,
  POLLING_MODE
} SMI_MODE;

#define TOTAL_NVME_SLOT  (48)
typedef struct _ROOTPORT_VS_NVMESLOT_DESC {
  UINT32 RpAddr;
  UINT32 NvmeSlot;
  UINT32 RasRetryCnt;
} ROOTPORT_VS_NVMESLOT_DESC;

// Check whether the IOS supports CXL
#define BRH_CXL_CAPABLE_RB(IOHUBS) ((IOHUBS & 0x2) == 0)

// Convert DF IOS Index to CNLI number in a Processor
//   All DXIO P Links support CXL:
//     CNLI0 P0 @IOHUBS0 (FabricId: 0x20) => NBIO0 IOHC0 PCIE0
//     CNLI1 P1 @IOHUBS1 (FabricId: 0x21) => NBIO0 IOHC3 PCIE3
//     CNLI2 P2 @IOHUBS4 (FabricId: 0x24) => NBIO1 IOHC0 PCIE0
//     CNLI3 P3 @IOHUBS5 (FabricId: 0x25) => NBIO1 IOHC3 PCIE3
#define BRH_IOS_TO_CNLI(ios)  (((ios & BIT2) >> 1) | (ios & BIT0))

// Convert IOS Index to NBIO RbIndex
//    IOS 0 (FabricId: 0x20) => NBIO RbIndex 0
//    IOS 1 (FabricId: 0x21) => NBIO RbIndex 5
//    IOS 2 (FabricId: 0x22) => NBIO RbIndex 1
//    IOS 3 (FabricId: 0x23) => NBIO RbIndex 4
//    IOS 4 (FabricId: 0x24) => NBIO RbIndex 2
//    IOS 5 (FabricId: 0x25) => NBIO RbIndex 7
//    IOS 6 (FabricId: 0x26) => NBIO RbIndex 3
//    IOS 7 (FabricId: 0x27) => NBIO RbIndex 6
#define BRH_IOS_TO_RBINDEX(ios)  (UINT8)(((ios & BIT0) << 2) | ((ios & BIT2) >> 1) | ((ios & BIT0) ? (~((ios & BIT1) >> 1) & BIT0) : ((ios & BIT1) >> 1)))

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */

extern AMD_RAS_SERVICE_SMM_PROTOCOL            *mAmdRasServiceSmmProtocol;
extern PLATFORM_APEI_PRIVATE_BUFFER_V3         *mPlatformApeiData;
extern RB_BUS_MAP                              *mRbBusMap;
extern AMD_PSP_ARS_SERVICE_PROTOCOL            *mAmdPspArsServiceProtocol;

extern EFI_GUID gAmdPlatformApeiDataProtocolGuid;

/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

//EFI_STATUS
//EFIAPI
//AmdMcetoSmiCallback (
//  IN       EFI_HANDLE                        DispatchHandle,
//  IN       CONST FCH_SMM_SW_REGISTER_CONTEXT *DispatchContext,
//  IN OUT   FCH_SMM_SW_CONTEXT                *SwContext,
//  IN OUT   UINTN                             *SizeOfSwContext
//  );

EFI_STATUS
RasSmmRegisterMceSwSmi (
  VOID
  );

EFI_STATUS
RasSmmRegisterMcePeriodicSmi (
  VOID
  );

EFI_STATUS
RasEnablePspEinj (
  VOID
  );

EFI_STATUS
RasSmmRegisterNbioSmi (
  VOID
  );

VOID
EFIAPI
RasSmmRegisterMbatTableUpdate (
  VOID
);

EFI_STATUS
NbioErrorScan (
  RAS_NBIO_ERROR_INFO   *RasNbioErrorInfo
  );

EFI_STATUS
PcieErrorScan (
  UINT8     IndexOfRbBusMap
  );

EFI_STATUS
SlinkErrorScan (
  UINT8     NbioBusNum
  );

EFI_STATUS
RasSmmRegisterFchALinkRasSmi (
  VOID
  );

EFI_STATUS
RasSmmRegisterUsbRasSmi (
  VOID
  );

EFI_STATUS
RasSmmRegisterSataRasSmi (
  VOID
  );

EFI_STATUS
RasSmmRegisterPcieLegacyRasSmi (
  VOID
  );

EFI_STATUS
RasSmmRegisterCxlRasSmi (
  VOID
  );

VOID
RasTriggerNMI (
  IN OUT   VOID
  );

VOID
RasSciInit(
  IN OUT   VOID
  );

VOID
RasTriggerSci (
  IN OUT   VOID
  );

VOID
RasResetErrBlk (
  IN OUT   EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE *ErrStatusBlk
  );

VOID
RasReinitErrBlkSts (
  IN OUT   EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE *ErrStatusBlk
  );

VOID
MpRegisterAccess (
  IN       UINTN                ProcessorNumber,
  IN OUT   PLAT_RAS_MSR_ACCESS  *RasMsrAccess
  );

VOID
UpdateGenErrStsBlkSeverity(
  EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE *ErrStatusBlk,
  UINT32        ErrorSeverity
  );

EFI_STATUS
RasPcieMisc (
  IN OUT   VOID
  );

EFI_STATUS
EFIAPI
RasRebuildByRootPort (
  IN  UINT32    RpPciAddr
  );



EFI_STATUS
EFIAPI
BuildSmmPcieActivePortMapCallback (
  IN CONST EFI_GUID     *Protocol,
  IN VOID               *Interface,
  IN EFI_HANDLE         Handle
  );

EFI_STATUS
EFIAPI
PerformEdr (
  IN  UINT32    RpPciAddr
  );

BOOLEAN
EFIAPI
OsEdrEnabled (
  IN  PCI_ADDR  RpAddress
  );

EFI_STATUS
AddOsEdrEnabledPortToTable (
  IN  PCI_ADDR  RpAddress
  );

EFI_STATUS
RasSmmRegisterEdrSwSmi (
  VOID
  );

EFI_STATUS
PcieErrorLog (
  PCIE_ERR_ENTRY    *LocalErrEntry,
  BOOLEAN           *TrigNMI,
  BOOLEAN           *TrigSCI,
  BOOLEAN           *IsEdrDpcError
  );

EFI_STATUS
PcieErrorScanHelper (
  IN     UINT8    IndexOfRbBusMap,
  IN     UINT32   RpPciAddr,
  IN OUT BOOLEAN  *TrigNMI,
  IN OUT BOOLEAN  *TrigSCI,
  IN     BOOLEAN  CheckEdrEnFlg
  );

BOOLEAN
LeakyBucketService (
  IN OUT  UINT32          *pSmiCount,
  IN OUT  UINT64          *pTscLast,
  IN OUT  SMI_MODE        *pSmiMode
  );

EFI_STATUS
RuntimePprInit (
    VOID
  );

#endif  //_AMD_PLATFORM_RAS_BRH_SMM_DRIVER_H_
