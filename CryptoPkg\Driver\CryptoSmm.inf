#***********************************************************************
#*                                                                     *
#*   Copyright (c) 1985-2025, American Megatrends International LLC.   *
#*                                                                     *
#*      All rights reserved. Subject to AMI licensing agreement.       *
#*                                                                     *
#***********************************************************************

## @file
#  Produces the EDK II SMM Crypto Protocol using the library services from
#  BaseCryptLib and TlsLib.  PcdCryptoServiceFamilyEnable is used to enable the
#  subset of available services.
#
#  Copyright (C) Microsoft Corporation. All rights reserved.
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x0001001B
  PI_SPECIFICATION_VERSION       = 0x00010014
  BASE_NAME                      = CryptoSmm
  MODULE_UNI_FILE                = Crypto.uni
  FILE_GUID                      = 391B853F-F488-479B-A3D6-870766C7A38F
  MODULE_TYPE                    = DXE_SMM_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = CryptoSmmEntry

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 ARM AARCH64
#

[Sources]
  Crypto.c
  CryptoSmm.c

[Packages]
  MdePkg/MdePkg.dec
  CryptoPkg/CryptoPkg.dec

[LibraryClasses]
  UefiDriverEntryPoint
  SmmServicesTableLib
  DebugLib
  BaseCryptLib
  TlsLib

[BuildOptions]
  #MSFT:*_*_IA32_DLINK_FLAGS = /ALIGN:4096  // APTIOV OVERRIDE - 4K Alignment for DXE_SMM_DRIVER
  #MSFT:*_*_X64_DLINK_FLAGS  = /ALIGN:4096  // has already defined in build_rule.txt file.
  #GCC:*_*_AARCH64_DLINK_XIPFLAGS = -z common-page-size=0x1000  // TODO

[Protocols]
  gEdkiiSmmCryptoProtocolGuid  ## PRODUCES

[Pcd]
  gEfiCryptoPkgTokenSpaceGuid.PcdCryptoServiceFamilyEnable  ## CONSUMES

[Depex]
  TRUE
