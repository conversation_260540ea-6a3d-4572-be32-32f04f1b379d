#pragma message( "Compal Server Override Compiling-" __FILE__ )
//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2025, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************

/** @file NbPciDxeInitLib.c
    Chipset NB PCI libraries

**/

#include <Token.h>
#include <PciBus.h>
#include <PciHostBridge.h>
#include <Setup.h>
#include <Protocol/PciRootBridgeIo.h>
#include <Protocol/PciIo.h>
#include <Protocol/AmiBoardInitPolicy.h>
#include <Library/PciAccessCspLib.h>
#include <Library/AmiPciBusLib.h>
#include <Library/PciLib.h>
#include <Library/PciSegmentLib.h>
#include <AmiPciBusSetupOverrideLib.h>
#include <Protocol/FabricResourceManagerServicesProtocol.h>
#include <Library/AmdSocBaseLib.h>
#include <PciNumaMapping.h>
#include <Library/BaseMemoryLib.h>
#include <Library/MemoryAllocationLib.h>
#include <AMD.h>
#include <Protocol/AmdCxlServicesProtocol.h>
#include <Library/HobLib.h>
#include <Guid/FabricRootBridgeOrderInfoHob.h>

#include <Library/AmiPciExpressLib.h>
//-------------------------------------------------------------------------
//!!!!!!!!!!! PORTING REQUIRED !!!!!!!!!!! PORTING REQUIRED !!!!!!!!!!!*
//!!!!!!!!!!!!!!!! must be maintained for PCI devices!!!!!!!!!!!!!!!!!!*
//-------------------------------------------------------------------------

typedef struct _VGA_DEVICE {
    UINT16 VenId;
    UINT16 DevId;
    UINT8  Bus;
    UINT8  Device;
    UINT8  Function;
    BOOLEAN VgaTriedFlag;
} VGA_DEVICE;

typedef struct _VGA_DEVICES_TRIED {
    BOOLEAN     VideoFound;
    UINT8       VideoIndex; 
    VGA_DEVICE  DeviceList[5];
} VGA_DEVICES_TRIED;

typedef struct _LAST_BOOT_FAILED_VAR {
    BOOLEAN     FailedRb[MAX_ROOT_BRIDGE_COUNT];
    BOOLEAN     DisableNextBoot;
    UINT8       FailType;
}LAST_BOOT_FAILED_VAR;

typedef struct _LEGACY_VGA_INFO {
    LIST_ENTRY Link;
    PCI_DEV_INFO   *Dev;
} LEGACY_VGA_INFO;

UINT32 NbioFabricId_RS [] = { 0x22, 0x23, 0x21, 0x20, 0x122, 0x123, 0x121, 0x120};
UINT32 NbioFabricId_BRH [16] = {0};

#define NB_SMN_INDEX_EXT_3_BIOS  0x00C0
#define NB_SMN_INDEX_3_BIOS  0x00C4
#define NB_SMN_DATA_3_BIOS   0x00C8
BOOLEAN gAtleastOneVideoFound = FALSE;

extern EFI_GUID gLastBootFailedGuid;
extern EFI_GUID gDisableResourcesVgaListGuid;


VGA_DEVICES_TRIED          gVgaDevices;
PCI_DEV_INFO               gMultiFuncDevices[4];
UINTN                      gNumMultiFuncDevices = 0;
LAST_BOOT_FAILED_VAR       gLastBootFailedVar;
EFI_PCI_PLATFORM_PROTOCOL  gPciPlatformProtocol;
LIST_ENTRY                 gLegacyVgaList;
BOOLEAN                    IsCsmOn = FALSE;
AMD_NBIO_CXL_SERVICES_PROTOCOL *gAmdNbioCxlServicesProtocol = NULL;
PCI_SETUP_DATA             *gPciDefaultSetup=NULL;
PCI_COMMON_SETUP_DATA      *gPciCommonSetup=NULL;
#define NBIO_MAX_CXLPORT       4
#define SOCKET_MAX_CXLPORT     (NBIO_MAX_CXLPORT * 4)
#define MAX_SOCKETS            2

typedef
VOID
(*DEVICE_PARSER) (
    IN PCI_DEV_INFO  *Device,
    IN BOOLEAN       *PciOutOfResHit
    );

EFI_STATUS
MatchVgaDevice (
    IN PCI_DEV_INFO  *Device
    );

EFI_STATUS
GetLastBootFailed (
    VOID
    );

VOID
FindSlot (
    PCI_DEV_INFO  *dev,
    UINT32        *Slot
    );

VOID
VgaRoute(
    IN PCI_DEV_INFO  *dev
    );

BOOLEAN
IsSocBrh(
 VOID
 );

BOOLEAN
IsSocBrhd(
 VOID
 );

EFI_STATUS
EFIAPI
PhaseNotify (
  IN EFI_PCI_PLATFORM_PROTOCOL                      *This,
  IN EFI_HANDLE                                     HostBridge,
  IN EFI_PCI_HOST_BRIDGE_RESOURCE_ALLOCATION_PHASE  Phase,
  IN EFI_PCI_CHIPSET_EXECUTION_PHASE                ChipsetPhase )
{
    EFI_STATUS       Status;
    PCI_DEV_INFO     *dev;
    PCI_DEV_INFO     *SelectedVGA = NULL;
    LEGACY_VGA_INFO  *LegacyVgaEntry;
    LIST_ENTRY       *NodeEntry;
    UINT32           Slot;
    SETUP_DATA       SetupData;
    UINTN            BufferSize;
    EFI_GUID         EfiSetupGuid = SETUP_GUID;
    
    
    DEBUG((DEBUG_ERROR, "%a InitStep: %d\n",__FUNCTION__, Phase));
    
    if(ChipsetPhase == ChipsetExit) return EFI_SUCCESS;
    
    switch (Phase) {

      case EfiPciHostBridgeEndBusAllocation:
          NodeEntry = GetFirstNode (&gLegacyVgaList);
          
          BufferSize = sizeof(SETUP_DATA);
          Status = pRS->GetVariable(L"Setup", &EfiSetupGuid, NULL, &BufferSize, &SetupData);
          
          if(EFI_ERROR(Status)) return Status;
          
          while(!IsNull (&gLegacyVgaList, NodeEntry)) {
              LegacyVgaEntry = (LEGACY_VGA_INFO *) NodeEntry;
              dev = LegacyVgaEntry->Dev;
              DEBUG((DEBUG_ERROR, "B:%x|D:%d|F:%d Attribute %lx\n", dev->Address.Addr.Bus, dev->Address.Addr.Device, dev->Address.Addr.Function, dev->Attrib));
              
              FindSlot(dev, &Slot);
              if(SelectedVGA == NULL){
                  SelectedVGA = dev;
              } else {
                  //If current device is onboard
                  if(dev->Attrib == EFI_PCI_IO_ATTRIBUTE_EMBEDDED_DEVICE){
                      //Only situation where onboard gets priority over existing device if SETUP->Onboard and existing is EXT
                      if((SetupData.OnExtVgaSelect == 1) && !(SelectedVGA->Attrib & EFI_PCI_IO_ATTRIBUTE_EMBEDDED_DEVICE)){
                          SelectedVGA = dev;
                      }
                  } else {
                      //If current device is external.
                      //if AUTO and existing device is INT then replace
                      if((SetupData.OnExtVgaSelect == 0) && (SelectedVGA->Attrib == EFI_PCI_IO_ATTRIBUTE_EMBEDDED_DEVICE)){
                          SelectedVGA = dev;
                      }
                      if(SetupData.OnExtVgaSelect == 2){
                          if(Slot == SetupData.VgaSlotNum){
                              SelectedVGA = dev;
                          }
                      }
                  }
              } // else existing device
              NodeEntry = GetNextNode (&gLegacyVgaList, NodeEntry);
          }
          
          if (SelectedVGA != NULL ) VgaRoute(SelectedVGA);
          break;
      default:
          return EFI_UNSUPPORTED;
    }
    
    return EFI_SUCCESS;
}

EFI_STATUS
EFIAPI
PlatformPrepController (
    IN  EFI_PCI_PLATFORM_PROTOCOL                      *This,
    IN  EFI_HANDLE                                     HostBridge,
    IN  EFI_HANDLE                                     RootBridge,
    IN  EFI_PCI_ROOT_BRIDGE_IO_PROTOCOL_PCI_ADDRESS    PciAddress,
    IN  EFI_PCI_CONTROLLER_RESOURCE_ALLOCATION_PHASE   Phase,
    IN  EFI_PCI_CHIPSET_EXECUTION_PHASE                ChipsetPhase )
{   
    return EFI_SUCCESS;
}

EFI_STATUS
EFIAPI
GetPlatformPolicy (
    IN  CONST EFI_PCI_PLATFORM_PROTOCOL  *This,
    OUT EFI_PCI_PLATFORM_POLICY          *PciPolicy )
{    
    return EFI_UNSUPPORTED;
}

EFI_STATUS
EFIAPI
GetPciRom (
  IN  CONST EFI_PCI_PLATFORM_PROTOCOL   *This,
  IN  EFI_HANDLE                        PciHandle,
  OUT VOID                              **RomImage,
  OUT UINTN                             *RomSize
  )
{    
    return EFI_UNSUPPORTED;
}

EFI_STATUS
EFIAPI
RegisterPciPlatformProtocol (
    IN EFI_HANDLE        ImageHandle,
    IN EFI_SYSTEM_TABLE  *SystemTable )
{
    EFI_STATUS Status;
    VOID                              *GuidHob;
    FABRIC_ROOT_BRIDGE_ORDER_INFO_HOB *RBOrderHob;
    UINT32                            Socket;
    UINT32                            Index;
    
    DEBUG((DEBUG_ERROR, "Installing PciPlatformProtocol\n"));
    
    ZeroMem (&gLegacyVgaList, sizeof(LIST_ENTRY) );
    InitializeListHead (&gLegacyVgaList);
    
    gPciPlatformProtocol.GetPciRom = GetPciRom;
    gPciPlatformProtocol.GetPlatformPolicy = GetPlatformPolicy;
    gPciPlatformProtocol.PlatformNotify = PhaseNotify;
    gPciPlatformProtocol.PlatformPrepController = PlatformPrepController;
    
    Status = gBS->InstallProtocolInterface(
            &ImageHandle,
            &gEfiPciPlatformProtocolGuid,
            EFI_NATIVE_INTERFACE,
            &gPciPlatformProtocol
            );

    if (IsSocBrh () || IsSocBrhd ()) {
        GuidHob = GetFirstGuidHob (&gFabricRootBridgeOrderInfoHobGuid);
        if (GuidHob != NULL) {
            RBOrderHob = GET_GUID_HOB_DATA (GuidHob);
            if (ARRAY_SIZE (NbioFabricId_BRH) == RBOrderHob->NumberOfRootBridges * MAX_SOCKETS) {
                for (Socket = 0; Socket < MAX_SOCKETS; Socket++) {
                    for (Index = 0; Index < RBOrderHob->NumberOfRootBridges; Index++) {
                        NbioFabricId_BRH[Socket * RBOrderHob->NumberOfRootBridges + Index] = (Socket << 7) + 0x20 + RBOrderHob->RootBridgeOrder[Index];
                    }
                }
            }
        }
    }

    return Status;
}

VOID
NbSmnRead (
    IN  UINT32  DieNum,
    IN  UINT32  Address,
    IN  UINT32  *Value )
{
    UINT32 read_value=0;

    PciWrite32(NB_SMN_INDEX_EXT_3_BIOS, DieNum);
    PciWrite32(NB_SMN_INDEX_3_BIOS, Address);
    read_value=PciRead32(NB_SMN_DATA_3_BIOS);
    *Value=read_value;

    //clear in case other functions don't pay attention to this reg
    PciWrite32(NB_SMN_INDEX_EXT_3_BIOS, 0);

}

VOID
NbSmnWrite (
    IN UINT32  DieNum,
    IN UINT32  Address,
    IN UINT32  *Value )
{
    UINT32 write_value=*Value;

    PciWrite32(NB_SMN_INDEX_EXT_3_BIOS, DieNum);
    PciWrite32(NB_SMN_INDEX_3_BIOS, Address);
    PciWrite32(NB_SMN_DATA_3_BIOS, write_value);

    //clear in case other functions don't pay attention to this reg
    PciWrite32(NB_SMN_INDEX_EXT_3_BIOS, 0);

}

VOID
NbSmnRW (
    IN UINT32  DieNum,
    IN UINT32  Address,
    IN UINT32  AndMask,
    IN UINT32  OrMask )
{
    UINT32  RegValue;

    NbSmnRead (DieNum, Address, &RegValue);
    RegValue &= AndMask;
    RegValue |= OrMask;
    NbSmnWrite (DieNum, Address, &RegValue);
}

BOOLEAN
IsSocBrh(
 VOID
 )
{
    SOC_ID_STRUCT   SocId;

    SocId.SocFamilyID = F1A_BRH_RAW_ID;
    SocId.PackageType = ZEN5_PKG_SP5;
    if (SocHardwareIdentificationCheck(&SocId)){
        return TRUE;
    }
    return FALSE;       // Not BRH
}

BOOLEAN
IsSocBrhd(
 VOID
 )
{
    SOC_ID_STRUCT   SocId;

    SocId.SocFamilyID = F1A_BRHD_RAW_ID;
    SocId.PackageType = ZEN5_PKG_SP5;
    if (SocHardwareIdentificationCheck(&SocId))
    {
        return TRUE;    // BRHD
    }
    return FALSE;       // Not BRHD
}

EFI_STATUS
RootBrgInit (
    AMI_BOARD_INIT_PROTOCOL  *This,
    IN     UINTN             *Function,
    IN OUT VOID              *ParameterBlock )
{
//Update Standard DEBUGck
    AMI_BOARD_INIT_PARAMETER_BLOCK  *Args=(AMI_BOARD_INIT_PARAMETER_BLOCK*)ParameterBlock;
    PCI_INIT_STEP                   InitStep=(PCI_INIT_STEP)Args->InitStep;
    PCI_ROOT_BRG_DATA               *dev=(PCI_ROOT_BRG_DATA*)Args->Param1;
    PCI_ROOT_BRG_DATA               *node0=dev->Owner->RootBridges[0];
    ASLR_QWORD_ASD                  *res = (ASLR_QWORD_ASD*)Args->Param2;
    EFI_STATUS                      Status=EFI_UNSUPPORTED;
    UINT32                          temp;
    UINT32                          temp2;
    EFI_PCI_ROOT_BRIDGE_IO_PROTOCOL *RtBrdg;
    UINT32                          nbiocount = 0;
    EFI_PHYSICAL_ADDRESS            a;
    ACPI_HDR                        *dsdt;
    UINT32                          dstFabricId;
    UINT32                          busNumLimit = 0;
    UINT32                          busNumBase = 0;
    
    RtBrdg = &node0->RbIoProtocol;
    //ASLR_QWORD_ASD    *res=(ASLR_QWORD_ASD*)Args->Param2;
    //ASLR_QWORD_ASD    *busrd;

//---------------------------------

    //Skip RBs that aren't there
    switch (InitStep)
        {
            //-------------------------------------------------------------------------
            case isRbCheckPresence:
                DEBUG((DEBUG_VERBOSE,"\n (isRbCheckPresence\n); " ));

                //For now this function only supports 2 combinations (2 socket, 4 die SP3) & (1 socket, 4 die SP3)
                //Update later to support all POR combinations.

                DEBUG((DEBUG_VERBOSE, "Bus = %x, Device = %x, Function = %x\n", dev->RbSdlData->Bus,
                                dev->RbSdlData->Device,
                                dev->RbSdlData->Function));
                //assume Die0= Bus0 exists
                //change to look ASL name for better match
                if(dev->RbSdlData->Bus != 0){
          if ( IsSocBrh() || IsSocBrhd() ){

				dev->NotPresent = TRUE; //Set NotPresent as TRUE at the beginning

				for(nbiocount=0; nbiocount<16; nbiocount++){
				  NbSmnRead (0, 0x49000C84 + nbiocount * 8, &temp2);	//DF Reg CfgLimitAddress
				  dstFabricId = temp2 & 0x00000FFF;

				  if (dstFabricId == NbioFabricId_BRH [dev->RbSdlData->Bus]){
					NbSmnRead (0, 0x49000C80 + nbiocount * 8, &temp);	 //DF Reg CfgBaseAddress
					if ((temp & 3) == 3) {
					  temp2 = ((temp >> 8) & 0xFF) & (0x7F); 	  //SegmentNum, FWDEV-61901: clear bit 7 as a WA for Turin C0 set it to be 1 for local segment(s)
					  temp = ((temp & 0x00FF0000) >> 16); //BusNumBase
					  temp = PciSegmentRead32 (PCI_SEGMENT_LIB_ADDRESS (temp2, temp, 0, 0, 0));
					  if(temp != 0xFFFFFFFF){
						dev->NotPresent = FALSE; //Set NotPresent as FALSE for RB detected
						break;
					  }
					}
				  }
				}
				
				if (dev->NotPresent){
				  DEBUG((DEBUG_INFO, "Root not present.\n"));
				  dev->RbSdlData->PciSegment = 0xFF;
				} else {
				  dev->RbSdlData->PciSegment = temp2;
            }
          } else {
            //GN, do a test read to see if this bus is present (assumes dev 0, func 0 exists on the bus)
                    if (dev->RbSdlData->Bus < 8) {
            NbSmnRead (0, 0x1C0A0 + dev->RbSdlData->Bus * 4, &temp);    //Reg CfgAddressMap
                    } else {
                        NbSmnRead (0, 0x1C3D0 + ((dev->RbSdlData->Bus - 8) * 4), &temp);
                    }
            if ((temp & 3) != 3){
              dev->NotPresent = TRUE; //indicates not present.
              DEBUG((DEBUG_INFO, "Root not present.\n"));
            }
            temp = PciRead32 (((temp & 0x00FF0000) >> 16) << 20);
            if(temp == 0xFFFFFFFF){
              dev->NotPresent = TRUE; //indicates not present.
              DEBUG((DEBUG_INFO, "Root not present.\n"));
            }
          }

                    //calculate bus size based on # of DIE
                    /*
                    RtBrdg->Pci.Read(RtBrdg, EfiPciWidthUint32,
                                                         EFI_PCI_ADDRESS (dev->RbSdlData->Bus, dev->RbSdlData->Device,
                                                             dev->RbSdlData->Function, 0),
                                                 1, &temp);
                    if(temp != 0x14501022) {
                                  DEBUG((DEBUG_INFO, "Root not present, DevVenId = %x\n",temp));
                                    dev->NotPresent = TRUE; //indicates not present.
                                    //Test also changing non-existent bus # to 255
                                    //not sure if necessary
                                dev->RbSdlData->Bus=0xFF;
                              }*/
                }
                Status= EFI_SUCCESS;
                break;
            case isRbBusUpdate:
                //Check system die count to determine bus mapping

                DEBUG((DEBUG_VERBOSE,"\n (isRbBusUpdate)\n" ));
                // NbSmnRead (0, 0x1C604, &temp);
			
                //adjust ACPI_RES_DATA?		
                //read DF programmed bus # to determine bus ranges.
                {

                if ( IsSocBrh() || IsSocBrhd() ) {
                 for(nbiocount=0; nbiocount<16; nbiocount++){
                   NbSmnRead (0, 0x49000C80 + nbiocount * 8, &temp);
                   if ((temp & 3) != 3) continue; // skip if this CfgAddressMap register is not enabled
                   NbSmnRead (0, 0x49000C84 + nbiocount * 8, &temp2);
                   dstFabricId = temp2 & 0x00000FFF;
                   if (dstFabricId == NbioFabricId_BRH [dev->RbSdlData->Bus]){
                     busNumLimit = (temp2 & 0x00FF0000) >> 16;
                     busNumBase  = (temp & 0x00FF0000) >> 16;
                     break;
                   }
                 }
                } else {
                        DEBUG((EFI_D_ERROR, "Not supported \n"));
                        return EFI_DEVICE_ERROR;
                    }
                    //if (dev->RbSdlData->Bus == 12) return EFI_NOT_FOUND;
                    //adjust ASLR_QWORD_ASD
                    res->_MIN = busNumBase;
                    res->_MAX = busNumLimit;
                    res->_LEN = (res->_MAX - res->_MIN) + 1;
                }

                //adjust AMI_SDL_PCI_DEV_INFO here
                DEBUG ((DEBUG_INFO, "BEFORE Bus = %x, Device = %x, Function = %x\n", dev->RbSdlData->Bus,
                                dev->RbSdlData->Device,
                                dev->RbSdlData->Function));

                dev->RbSdlData->Bus = (UINT8) res->_MIN;

                DEBUG ((DEBUG_INFO, "AFTER Bus = %x, Device = %x, Function = %x\n", dev->RbSdlData->Bus,
                                dev->RbSdlData->Device,
                                dev->RbSdlData->Function));            
                Status = LibGetDsdt (&a, EFI_ACPI_TABLE_VERSION_ALL);
                if (!EFI_ERROR (Status)) {
                    dsdt = (ACPI_HDR *) a;
                    UpdateAslNameOfDevice (dsdt, dev->RbSdlData->AslName, "PSEG", dev->RbSdlData->PciSegment);
                    dsdt->Checksum = 0;
                    dsdt->Checksum = ChsumTbl((UINT8*)dsdt, dsdt->Length);
                }
                Status= EFI_SUCCESS;
                break;
        }//switch
    //---------------------------------

    return Status;
}


EFI_STATUS
USB3_Init (
    AMI_BOARD_INIT_PROTOCOL  *This,
    IN     UINTN             *Function,
    IN OUT VOID              *ParameterBlock )
{
    //Update Standard parameter block
    AMI_BOARD_INIT_PARAMETER_BLOCK  *Args = (AMI_BOARD_INIT_PARAMETER_BLOCK*)ParameterBlock;
    PCI_INIT_STEP                   InitStep = (PCI_INIT_STEP)Args->InitStep;
    EFI_STATUS                      Status = EFI_UNSUPPORTED;
    //Temp just try DIE1
   // UINT32  DieNum;
   // UINT32          temp;
//---------------------------------
    //Check if parameters passed are VALID and
    if(Args->Signature != AMI_PCI_PARAM_SIG) return EFI_INVALID_PARAMETER;
    if(InitStep>=isPciMaxStep) return EFI_INVALID_PARAMETER;
    
    switch (InitStep)
    {
    //-------------------------------------------------------------------------
        case isPciSkipDevice:
            DEBUG((DEBUG_VERBOSE,"\ncalling USB3_Init\n" ));

            break;
    //-------------------------------------------------------------------------

    }//switch

    return Status;
}

EFI_STATUS 
SATA_Init (
    AMI_BOARD_INIT_PROTOCOL   *This,
    IN UINTN                  *Function,
    IN OUT VOID               *ParameterBlock )
{
    //Update Standard parameter block
    AMI_BOARD_INIT_PARAMETER_BLOCK  *Args = (AMI_BOARD_INIT_PARAMETER_BLOCK*)ParameterBlock;
    PCI_INIT_STEP                   InitStep = (PCI_INIT_STEP)Args->InitStep;
    PCI_DEV_INFO                    *dev = (PCI_DEV_INFO*)Args->Param1;
    EFI_STATUS                      Status = EFI_UNSUPPORTED;

    UINT32                          DieNum;
    UINT32                          temp;
//---------------------------------
    //Check if parameters passed are VALID and
    if(Args->Signature != AMI_PCI_PARAM_SIG) return EFI_INVALID_PARAMETER;
    if(InitStep>=isPciMaxStep) return EFI_INVALID_PARAMETER;
    
    switch (InitStep)
    {
    //-------------------------------------------------------------------------
        case isPciSkipDevice:
            DEBUG((DEBUG_VERBOSE,"\ncalling SATA_Init\n" ));
            //We're going to use this as an init step.

#if 0
            //calculate Die Number based on BUS Number
            NbSmnRead (0, 0x1C604, &temp);
            temp = temp & 0xF;             
            //calculate bus size based on # of DIE            
            temp = 256/temp;
#endif

      if ( IsSocBrh() || IsSocBrhd() ){
        NbSmnRead (0, 0x49000C84, &temp);  //Reg CfgBaseAddress
        temp = temp & 0x00FF0000;       //BusNumLimit
        temp = temp >> 16;
      } else {    //GN, BA
        NbSmnRead (0, 0x1C0A0, &temp);  //Reg CfgAddressMap
        temp = temp & 0xFF000000;       //BusNumLimit
        temp = temp >> 24;
      }
            temp = temp+1;

            //calculate die # based on bus num
            if(dev->AmiSdlPciDevData->Bus != 0){
                temp = dev->AmiSdlPciDevData->Bus/temp;
                DEBUG((DEBUG_VERBOSE, "Calculated die # = %x\n",temp));  
                DieNum = temp;
            } else {
                DieNum=0;
            }
            //This is Diesel specific, need to make more generic later. Assume Die0 SATA= FieldCard
            if(DieNum == 0){
                //DEBUG((DEBUG_VERBOSE, "Limit SATA to Gen2\n"));
                //NbSmnRW (DieNum, 0x3100000, 0xFFFFFFFB, 0x4);               
            }
            break;
    //-------------------------------------------------------------------------

    }//switch

    return Status;
}

EFI_STATUS 
BMC_Init (
    AMI_BOARD_INIT_PROTOCOL  *This,
    IN     UINTN             *Function,
    IN OUT VOID              *ParameterBlock )
{
    //Update Standard parameter block
    AMI_BOARD_INIT_PARAMETER_BLOCK  *Args = (AMI_BOARD_INIT_PARAMETER_BLOCK*)ParameterBlock;
    PCI_INIT_STEP                   InitStep = (PCI_INIT_STEP)Args->InitStep;
    PCI_DEV_INFO                    *dev = (PCI_DEV_INFO*)Args->Param1;
    EFI_STATUS                      Status = EFI_UNSUPPORTED;
//---------------------------------
    //Check if parameters passed are VALID and
    if(Args->Signature != AMI_PCI_PARAM_SIG) return EFI_INVALID_PARAMETER;
    if(InitStep>=isPciMaxStep) return EFI_INVALID_PARAMETER;

    switch (InitStep)
    {
    //-------------------------------------------------------------------------
        case isPciGetSetupConfig:
            DEBUG((DEBUG_VERBOSE," (isPciGetSetupConfig); " ));

            DEBUG((DEBUG_VERBOSE,"\nPciInit: BMC - Disabling PCIe Init...\n" ));
//              if(PcieCheckPcieCompatible(dev)){
            dev->DevSetup.Pcie1Disable = TRUE;
            dev->DevSetup.Pcie2Disable = TRUE; //if you think that issues is in GEN2 int use this line only.
            DEBUG((DEBUG_VERBOSE,"\nPciInit: BMC - Disabling Gen1&2...\n" ));
//           }
            Status = EFI_SUCCESS;

            break;
    //-------------------------------------------------------------------------
        case isPciSkipDevice:
            DEBUG((DEBUG_VERBOSE," (isPciSkipDevice); " ));
            //DEBUG((DEBUG_VERBOSE,"\ncalling BMC_Init\n" ));
            //DEBUG((DEBUG_VERBOSE,"skip BMC VGA\n" ));
            //Status= EFI_SUCCESS;
            break;
    //-------------------------------------------------------------------------
        case isPciSetAttributes:
            DEBUG((DEBUG_VERBOSE," (isPciSetAttributes); " ));

            break;
    //-------------------------------------------------------------------------
        case isPciProgramDevice:
            DEBUG((DEBUG_VERBOSE," (isPciProgramDevice); " ));

            break;
    //-------------------------------------------------------------------------
        case isPcieSetAspm:
            DEBUG((DEBUG_VERBOSE," (isPcieSetAspm); " ));

            break;
    //-------------------------------------------------------------------------
        case isPcieSetLinkSpeed:
            DEBUG((DEBUG_VERBOSE," (isPcieSetLinkSpeed); " ));

            break;
    //-------------------------------------------------------------------------
        case isPciGetOptionRom:
            DEBUG((DEBUG_VERBOSE," (isPciGetOptionRom); " ));

            break;
    //-------------------------------------------------------------------------
        case isPciOutOfResourcesCheck:
            DEBUG((DEBUG_VERBOSE," (isPciOutOfResourcesCheck); " ));

            break;
    //-------------------------------------------------------------------------
        case isPciReadyToBoot:
            DEBUG((DEBUG_VERBOSE," (isPciReadyToBoot); " ));

            break;
    //-------------------------------------------------------------------------
    }//switch

    return Status;
}

EFI_STATUS 
NTB_Init (
    AMI_BOARD_INIT_PROTOCOL   *This,
    IN     UINTN              *Function,
    IN OUT VOID               *ParameterBlock )
{
    //Update Standard parameter block
    AMI_BOARD_INIT_PARAMETER_BLOCK  *Args = (AMI_BOARD_INIT_PARAMETER_BLOCK*)ParameterBlock;
    PCI_INIT_STEP                   InitStep = (PCI_INIT_STEP)Args->InitStep;
//    PCI_DEV_INFO                    *dev = (PCI_DEV_INFO*)Args->Param1;
    EFI_STATUS                      Status = EFI_UNSUPPORTED;
//---------------------------------
    //Check if parameters passed are VALID and
    if(Args->Signature != AMI_PCI_PARAM_SIG) return EFI_INVALID_PARAMETER;
    if(InitStep>=isPciMaxStep) return EFI_INVALID_PARAMETER;

    switch (InitStep)
    {
    //-------------------------------------------------------------------------
        case isPciGetSetupConfig:
            DEBUG((DEBUG_VERBOSE," (isPciGetSetupConfig); " ));

            DEBUG((DEBUG_VERBOSE,"\nPciInit: NTB - Disabling PCIe Init...\n" ));
                    //For Tiger NTB
                    // dev->DevSetup.Pcie1Disable=TRUE;
                    // dev->DevSetup.Pcie2Disable=TRUE; //if you think that issues is in GEN2 int use this line only.
            DEBUG((DEBUG_VERBOSE,"\nPciInit: NTB - Disabling Gen1&2...\n" ));

            //Status=EFI_SUCCESS;

            break;
    //-------------------------------------------------------------------------
        case isPciSkipDevice:
            DEBUG((DEBUG_VERBOSE," (isPciSkipDevice); " ));
            DEBUG((DEBUG_VERBOSE,"\ncalling NTB_Init\n" ));

            //{ volatile unsigned long __iii; __iii = 1; while (__iii); }
            //    Status= EFI_SUCCESS;
            break;
    //-------------------------------------------------------------------------
        case isPciSetAttributes:
            DEBUG((DEBUG_VERBOSE," (isPciSetAttributes); " ));

        break;
    //-------------------------------------------------------------------------
        case isPciProgramDevice:
            DEBUG((DEBUG_VERBOSE," (isPciProgramDevice); " ));

        break;
    //-------------------------------------------------------------------------
        case isPcieSetAspm:
            DEBUG((DEBUG_VERBOSE," (isPcieSetAspm); " ));

        break;
    //-------------------------------------------------------------------------
        case isPcieSetLinkSpeed:
            DEBUG((DEBUG_VERBOSE," (isPcieSetLinkSpeed); " ));

        break;
    //-------------------------------------------------------------------------
        case isPciGetOptionRom:
            DEBUG((DEBUG_VERBOSE," (isPciGetOptionRom); " ));

        break;
    //-------------------------------------------------------------------------
        case isPciOutOfResourcesCheck:
            DEBUG((DEBUG_VERBOSE," (isPciOutOfResourcesCheck); " ));

        break;
    //-------------------------------------------------------------------------
        case isPciReadyToBoot:
            DEBUG((DEBUG_VERBOSE," (isPciReadyToBoot); " ));

        break;
    //-------------------------------------------------------------------------
    }//switch

    return Status;
}

VOID
LimitAndCheckVgaRes(
    IN PCI_DEV_INFO  *dev)
{
    PCI_DEV_INFO    *ParentBrg = dev->ParentBrg;
    PCI_CFG_ADDR    PciDev;
    UINT64          TotalMMIO32;
    UINTN           i;
    EFI_STATUS      Status;
    UINT32          id;
    
    DEBUG((DEBUG_ERROR, "Limiting %x:%x:%x Gfx device to 32bit MMIO.\n",
         dev->Address.Addr.Bus,
         dev->Address.Addr.Device,
         dev->Address.Addr.Function));
    
    dev->DevSetup.Decode4gDisable=TRUE;
    
    // We are past step where Decode4gDisable is checked by PciBus
    // Apply 4g disable manually
    for(i = 0; i < PCI_MAX_BAR_NO; i++){
        if(dev->Bar[i].Type==tBarMmio64pf) dev->Bar[i].Type=tBarMmio32pf;
        if(dev->Bar[i].Type==tBarMmio64) dev->Bar[i].Type=tBarMmio32;
    }
    
    // Check that new bars are not over 32 bit limit
    TotalMMIO32 = 0;
    for(i = 0; i < PCI_MAX_BAR_NO + 1; i++){
        if((dev->Bar[i].Type!=tBarUnused) && (dev->Bar[i].Length!=0) ){
            if( dev->Bar[i].Type == tBarMmio32 || dev->Bar[i].Type == tBarMmio32pf) {
                TotalMMIO32 += dev->Bar[i].Length;
            }
        }//if not empty
    } //for
    
    if(TotalMMIO32 >= PCI_DEVICE_32BIT_RESOURCE_THRESHOLD){
        DEBUG((DEBUG_VERBOSE,"\nPciBus: !!!RES TOO BIG WARNING!!! Device @[B%X|D%X|F%X], VID=%X, DID=%X\n",
                dev->Address.Addr.Bus, dev->Address.Addr.Device, dev->Address.Addr.Function,
                dev->DevVenId.VenId, dev->DevVenId.DevId));
        DEBUG((DEBUG_VERBOSE,"        !!!Adjust xx_RESOURCE_THRESHOLD tokens or remove the card!!!\n"));
        
        //Zero out all BARs and ROM BAR.
        for(i = PCI_BAR0, id = 0; i < PCI_CAPP; i += 4){
            PciDev.Addr.Register = (UINT8) i;
            Status = PciCfg32(ParentBrg->RbIo, PciDev,TRUE,&id);
            if(EFI_ERROR(Status)) return;
        }
        
        //Clear CMD_REG
        PciDev.Addr.Register = (UINT8) PCI_CMD;
        Status = PciCfg16(ParentBrg->RbIo, PciDev, TRUE, (UINT16*) (&id));
        if(EFI_ERROR(Status)) return;

        //Clear All collected resource information for that device
        //to avoid them to be added to the system's resource request.
        for(i = 0; i < PCI_MAX_BAR_NO + 1; i++){
            dev->Bar[i].Type = tBarUnused;
            dev->Bar[i].DiscoveredType = tBarUnused;    
            dev->Bar[i].Length = 0;
            dev->Bar[i].Gran = 0;
        }
        //Setting Flags telling not to install PciIo protocol instance
        //on this device and don't touch device at all! 
        dev->Started = TRUE;
        dev->Assigned = TRUE;
        dev->Discovered = TRUE; 
        dev->Enumerated = TRUE;
        dev->RomBarError = TRUE;
        dev->Incompatible = TRUE;
    } //if .. Device requests too much resources.
}

VOID 
VgaRoute (
    IN PCI_DEV_INFO  *dev )
{
    UINT8                             OnBoard;
    EFI_STATUS                        Status;
    FABRIC_RESOURCE_MANAGER_PROTOCOL  *AmdFabricResourceManager;
    FABRIC_TARGET                     Target = {0};
    
    Status = pBS->LocateProtocol (
                  &gAmdFabricResourceManagerServicesProtocolGuid,
                  NULL,
                  &AmdFabricResourceManager
                );
    
    if (EFI_ERROR (Status)) {
        return;
    }
    
    Target.PciBusNum  = dev->Address.Addr.Bus;
    Target.TgtType    = 0;   // TARGET_PCI_BUS
    Status = AmdFabricResourceManager->FabricEnableVgaMmio (
                                      AmdFabricResourceManager,
                                      Target
                                     );
    if(dev->Attrib == EFI_PCI_IO_ATTRIBUTE_EMBEDDED_DEVICE){
        OnBoard = 1;
    } else {
        OnBoard = 0;
    }
    if(IsCsmOn){
        LimitAndCheckVgaRes(dev);
        PcdSet32S (PcdLegacyVgaBDF, PCI_LIB_ADDRESS(dev->Address.Addr.Bus,
                dev->Address.Addr.Device,dev->Address.Addr.Function,OnBoard));
    }
    return;
}

VOID 
FindSlot (
    PCI_DEV_INFO  *dev,
    UINT32        *Slot )
{
    if(dev != NULL){
        if ((dev->SdlMatchFound != 1) && (dev->ParentBrg != dev)){
            FindSlot(dev->ParentBrg, Slot);
        } else {
            *Slot = dev->AmiSdlPciDevData->Slot;
        }
    }
}

// <AMI_PHDR_START>
//-------------------------------------------------------------------------
//
// Procedure: DeviceXXXX_Init
//
// Description:
//  This function provide each initial routine in genericsio.c
//
// Input:
//  IN  AMI_SIO_PROTOCOL    *AmiSio - Logical Device's information
//  IN  EFI_PCI_IO_PROTOCOL *PciIo - Read/Write PCI config space
//  IN  SIO_INIT_STEP       InitStep - Initial routine step
//
// Output:
//  EFI_SUCCESS - Initial step sucessfully
//  EFI_INVALID_PARAMETER - not find the initial step
//
// Modified:    Nothing
//
// Referrals:   None
//
// Notes:
//-------------------------------------------------------------------------
// <AMI_PHDR_END>

EFI_STATUS 
PciDevXXX_Init(
  AMI_BOARD_INIT_PROTOCOL  *This,
  IN UINTN                 *Function,
  IN OUT VOID              *ParameterBlock )
{
    //Update Standard parameter block
    AMI_BOARD_INIT_PARAMETER_BLOCK  *Args = (AMI_BOARD_INIT_PARAMETER_BLOCK*)ParameterBlock;
    PCI_INIT_STEP                   InitStep = (PCI_INIT_STEP)Args->InitStep;
    PCI_DEV_INFO                    *dev = (PCI_DEV_INFO*)Args->Param1;
    EFI_STATUS                      Status = EFI_UNSUPPORTED;
    PCI_CFG_ADDR                    addr;
#if ( defined(CSM_SUPPORT) && (CSM_SUPPORT != 0) )
    SETUP_DATA  SetupData;
    UINTN                           BufferSize;
    EFI_GUID                        EfiSetupGuid = SETUP_GUID;
#endif
    LEGACY_VGA_INFO                 *LegacyVgaEntry;                 
#if BoardPciRes_SUPPORT
    UINT32                          LegacyVgaBDF;
    EFI_STATUS MatchVgaStatus;
#endif

    if ((dev->ParentBrg->Type == tPci2PciBrg || dev->ParentBrg->Type == tPci2CrdBrg)&&(dev->AmiSdlPciDevData != NULL)) {
        if (dev->ParentBrg->PciExpress->PcieCap.SlotImpl && (dev->ParentBrg->PciExpress->SlotCap.PhisSlotNum != dev->AmiSdlPciDevData->Slot)) {
            addr.ADDR = dev->ParentBrg->Address.ADDR;
            addr.Addr.Register = (UINT8)dev->ParentBrg->PciExpress->PcieOffs+PCIE_SLT_CAP_OFFSET;
            dev->ParentBrg->PciExpress->SlotCap.PhisSlotNum = dev->AmiSdlPciDevData->Slot;
//COMPAL_CHANGE            PciCfg32(dev->RbIo,addr,TRUE,&dev->ParentBrg->PciExpress->SlotCap.SLT_CAP);
        }
    }

    //Check if parameters passed are VALID and
    if(Args->Signature != AMI_PCI_PARAM_SIG) return EFI_INVALID_PARAMETER;
    if(InitStep >= isPciMaxStep) return EFI_INVALID_PARAMETER;
    switch (InitStep)
    {
        case isPciGetSetupConfig:
            DEBUG((DEBUG_VERBOSE," (isPciGetSetupConfig); " ));
      // If CSM is enabled && CSM video is set to legacy then limit to 32bit MMIO.
      // Ideally only 1st gfx device found with legacy opt rom would have this done.

#if BoardPciRes_SUPPORT
        GetLastBootFailed();
        if(gLastBootFailedVar.DisableNextBoot){
            if ((dev->Class.BaseClassCode == PCI_CLASS_DISPLAY && dev->Class.SubClassCode == PCI_CLASS_DISPLAY_VGA) ||
                (dev->Class.BaseClassCode == PCI_CLASS_DISPLAY && dev->Class.SubClassCode == PCI_CLASS_DISPLAY_OTHER) ||
                (dev->Class.BaseClassCode == PCI_CLASS_MEDIA && dev->Class.SubClassCode == PCI_CLASS_DISPLAY_VGA)){
                LegacyVgaBDF = PcdGet32(PcdLegacyVgaBDF);
                if(!LegacyVgaBDF){
                    MatchVgaStatus = MatchVgaDevice(dev);
                    if(MatchVgaStatus != EFI_SUCCESS) VgaRoute(dev);
                }
            }
        } else {
#endif
#if ( defined(CSM_SUPPORT) && (CSM_SUPPORT != 0) )
            BufferSize = sizeof(SETUP_DATA);
            pRS->GetVariable(L"Setup", &EfiSetupGuid, NULL, &BufferSize, &SetupData);
            if (SetupData.CsmSupport == 1) {
                IsCsmOn = TRUE;
            }
#endif
            if ((dev->Class.BaseClassCode == 0x03 && dev->Class.SubClassCode == 0x00) ||
                    (dev->Class.BaseClassCode == 0x03 && dev->Class.SubClassCode == 0x80) ||
                    (dev->Class.BaseClassCode == 0x04 && dev->Class.SubClassCode == 0x00))
                {
                    LegacyVgaEntry = AllocateZeroPool( sizeof(LEGACY_VGA_INFO));
                    LegacyVgaEntry->Dev = dev;
                    
                    InsertTailList(&gLegacyVgaList, &LegacyVgaEntry->Link);
                } //end if VGA device
#if BoardPciRes_SUPPORT
        }
#endif

      // APTIOV_SERVER_OVERRIDE_START : AmiPciBus Setup Override changes.
#if PciBusSetupOverride_SUPPORT
            if (dev->PciExpress != NULL) {
               if (dev->PciExpress->Pcie2 != NULL) {
                   AmiPciGetPcie2SetupDataOverrideHook (&dev->PciExpress->Pcie2->Pcie2Setup, dev->AmiSdlPciDevData, dev->SdlDevIndex, FALSE);
               }
               AmiPciGetPcie1SetupDataOverrideHook (&dev->PciExpress->Pcie1Setup, dev->AmiSdlPciDevData, dev->SdlDevIndex, FALSE);
            }
            Status = AmiPciGetPciDevSetupDataOverrideHook (&dev->DevSetup, dev->AmiSdlPciDevData, dev->SdlDevIndex, FALSE);
#endif
       // APTIOV_SERVER_OVERRIDE_END : AmiPciBus Setup Override changes.
            break;
  //-------------------------------------------------------------------------
    case isPciSkipDevice:
        DEBUG((DEBUG_VERBOSE," (isPciSkipDevice); " ));

        break;
  //-------------------------------------------------------------------------
    case isPciSetAttributes:
        DEBUG((DEBUG_VERBOSE," (isPciSetAttributes); " ));

        break;
  //-------------------------------------------------------------------------
    case isPciProgramDevice:
        DEBUG((DEBUG_VERBOSE," (isPciProgramDevice); " ));

        break;
  //-------------------------------------------------------------------------
    case isPcieSetAspm:
        DEBUG((DEBUG_VERBOSE," (isPcieSetAspm); " ));

        break;
  //-------------------------------------------------------------------------
    case isPcieSetLinkSpeed:
        DEBUG((DEBUG_VERBOSE," (isPcieSetLinkSpeed); " ));

        break;
  //-------------------------------------------------------------------------
    case isPciGetOptionRom:
        DEBUG((DEBUG_VERBOSE," (isPciGetOptionRom); " ));

        break;
  //-------------------------------------------------------------------------
    case isPciOutOfResourcesCheck:
        DEBUG((DEBUG_VERBOSE," (isPciOutOfResourcesCheck); " ));

        break;
  //-------------------------------------------------------------------------
    case isPciReadyToBoot:
        DEBUG((DEBUG_VERBOSE," (isPciReadyToBoot); " ));

        break;
  //-------------------------------------------------------------------------
    }//switch

    return Status;
}

#if BoardPciRes_SUPPORT
EFI_STATUS
GetLastBootFailed (
    VOID )
{
    EFI_STATUS      Status;
    UINTN           VariableSize;
    
    VariableSize = sizeof(LAST_BOOT_FAILED_VAR);
    Status = pRS->GetVariable (L"LastBootFailed",
                 &gLastBootFailedGuid,
                 NULL,
                 &VariableSize,
                 &gLastBootFailedVar ); 
    
    return Status;
}

//Checks Device to see if it is found in gVgaDevices
// Return
//      EFI_NOT_READY   = Variable has not been installed. First boot. Insert this video device
//      EFI_SUCCESS     = Device has been tried. Disable.
//      EFI_UNSUPPORTED = Enable device.
//      EFI_NOT_FOUND   = Device not in list

EFI_STATUS
MatchVgaDevice (
    IN PCI_DEV_INFO  *Device )
{
    EFI_STATUS  Status;
    UINTN       VariableSize;
    UINTN       Index;
    
    VariableSize = sizeof(VGA_DEVICES_TRIED);
    Status = pRS->GetVariable(L"VgaList",
                              &gDisableResourcesVgaListGuid,
                              NULL,
                              &VariableSize,
                              &gVgaDevices);

    if(EFI_ERROR(Status) || gVgaDevices.VideoIndex == (UINT8)-1) return EFI_NOT_READY;


    Status = EFI_NOT_FOUND;
    for (Index = 0; Index <= gVgaDevices.VideoIndex; Index++)
    {

        if (Device->DevVenId.DevId == gVgaDevices.DeviceList[Index].DevId &&
            Device->DevVenId.VenId == gVgaDevices.DeviceList[Index].VenId &&
            Device->Address.Addr.Bus == gVgaDevices.DeviceList[Index].Bus &&
            Device->Address.Addr.Device == gVgaDevices.DeviceList[Index].Device &&
            Device->Address.Addr.Function == gVgaDevices.DeviceList[Index].Function)
        {
            if (gVgaDevices.DeviceList[Index].VgaTriedFlag == TRUE)
            {
                //If device has been tried flag for disabling
                Status = EFI_SUCCESS;
                break;
            }
            else
            {
                //Do not skip this device
                Status = EFI_UNSUPPORTED;
                break;
            }
        }else {
            //Check if any video devices are untried
            if (gVgaDevices.DeviceList[Index].VgaTriedFlag == FALSE &&
                    gVgaDevices.DeviceList[Index].DevId != 0x00 &&
                    gVgaDevices.DeviceList[Index].VenId != 0x00){
                Status = EFI_SUCCESS;
                break;
            }
        }
    } //end for
    
    return Status;
}

/**
    Add device to VGA list
    
    @param *Device              Pointer to Device Data to check
    @param Type                 PCI BAR Type

    @retval EFI_STATUS
 */

EFI_STATUS
AddVgaDevice (
    IN PCI_DEV_INFO  *Device )
{
    EFI_STATUS Status = EFI_UNSUPPORTED;
    EFI_STATUS SetVarStatus;
    UINTN Index;
    
    Status = MatchVgaDevice(Device);
    
    if (EFI_ERROR (Status)) {
      if ((Status == EFI_NOT_READY) || (Status == EFI_UNSUPPORTED)) {
          if (Status == EFI_NOT_READY) {
              // This is first boot initialize variable
              pBS->SetMem(&gVgaDevices, sizeof(VGA_DEVICES_TRIED), 0xFF);

              gVgaDevices.VideoIndex = 0;

              gVgaDevices.DeviceList[0].DevId = Device->DevVenId.DevId;
              gVgaDevices.DeviceList[0].VenId = Device->DevVenId.VenId;
              gVgaDevices.DeviceList[0].Bus   = Device->AmiSdlPciDevData->Bus;
              gVgaDevices.DeviceList[0].Device = Device->AmiSdlPciDevData->Device;
              gVgaDevices.DeviceList[0].Function = Device->AmiSdlPciDevData->Function;
              gVgaDevices.DeviceList[0].VgaTriedFlag = TRUE;              
          }
          gAtleastOneVideoFound = TRUE;
          
          if ((Device->Func0 != NULL) && ((Device->FuncCount != 0) || (Device->FuncInitCnt != 0))) {
              // Multi-Func device
              gNumMultiFuncDevices = Device->FuncCount;
              for (Index = 0; Index < gNumMultiFuncDevices; Index++) {
                  pBS->CopyMem(&gMultiFuncDevices[Index], Device->DevFunc[Index], sizeof(PCI_DEV_INFO));
              }
          }
      } else if (Status == EFI_NOT_FOUND) {
          // Device not found in list so add to list and try
          gVgaDevices.VideoIndex++;

          if (gVgaDevices.VideoIndex == 5) {
              DEBUG ((DEBUG_ERROR, "ERROR: Video device list full.\n"));
              ASSERT(FALSE);
              EFI_DEADLOOP();
          }

          Index = gVgaDevices.VideoIndex;
          gVgaDevices.DeviceList[Index].DevId = Device->DevVenId.DevId;
          gVgaDevices.DeviceList[Index].VenId = Device->DevVenId.VenId;
          gVgaDevices.DeviceList[Index].Bus   = Device->AmiSdlPciDevData->Bus;
          gVgaDevices.DeviceList[Index].Device = Device->AmiSdlPciDevData->Device;
          gVgaDevices.DeviceList[Index].Function = Device->AmiSdlPciDevData->Function;
          gVgaDevices.DeviceList[Index].VgaTriedFlag = TRUE;

          gAtleastOneVideoFound = TRUE;
      }
      Status = EFI_UNSUPPORTED;
    }

    gVgaDevices.VideoFound = gAtleastOneVideoFound;
    SetVarStatus = pRS->SetVariable(L"VgaList",
                     &gDisableResourcesVgaListGuid,
                     EFI_VARIABLE_NON_VOLATILE |
                         EFI_VARIABLE_BOOTSERVICE_ACCESS,
                     sizeof(VGA_DEVICES_TRIED),
                     &gVgaDevices);
    if (EFI_ERROR (SetVarStatus)) {
        DEBUG ((DEBUG_ERROR, "Set VgaList Variable with Status - %r.\n", SetVarStatus));
        return SetVarStatus;
    }
    
    return Status;
}

/**
    Check to see if this device uses this resource type
    
    @param *Device              Pointer to Device Data to check
    @param Type                 PCI BAR Type

    @retval BOOLEAN       
**/

BOOLEAN
DoesDeviceUseMmio (
    IN PCI_DEV_INFO  *Device,
    IN PCI_BAR_TYPE  Type )
{
    UINT8 BarIndex;

    for (BarIndex = 0; BarIndex <= PCI_MAX_BAR_NO; BarIndex++)
    {
        if (Device->Bar[BarIndex].Type == Type)
            return TRUE;
    }

    return FALSE;
}

VOID
DisableDevResourceType (
    IN PCI_DEV_INFO  *Device,
    IN PCI_BAR_TYPE  Type )
{
    UINT8 BarIndex;

    for (BarIndex = 0; BarIndex <= PCI_MAX_BAR_NO; BarIndex++)
    {
        if (Device->Bar[BarIndex].Type == Type)
        {
            Device->Bar[BarIndex].Type = tBarUnused;
            Device->Bar[BarIndex].DiscoveredType = tBarUnused;
            Device->Bar[BarIndex].Base = 0;
            Device->Bar[BarIndex].Length = 0;
        }
    }
}
/**
    Function to disable the SRIOV BAR Resources while OOR Occurs.

    @param *Device             Current device (not necessarily problematic device)
    @param *PciOutOfResHit     Out of resource condition

**/
VOID
DisableSRIOVBars(IN PCI_DEV_INFO *Device, IN BOOLEAN *PciOutOfResHit) 
{
    BOOLEAN SRIOVDevice = FALSE;
    UINTN BarIndex = 0;
    EFI_STATUS PciReadStatus;
    EFI_STATUS Status;
    PCI_BAR                   *VfBar;
    PCI_CFG_ADDR              DevAddress;
    PCIE_EXT_CAP_HDR          ExtCap; 
    
    //Create Pci Setup Data Buffer;
    if (gPciCommonSetup == NULL) {

        gPciDefaultSetup = AllocateZeroPool (sizeof (PCI_SETUP_DATA));
        if (gPciDefaultSetup == NULL) return;

        gPciCommonSetup = AllocateZeroPool (sizeof (PCI_COMMON_SETUP_DATA));
        if (gPciCommonSetup == NULL) return;

        //Call Library function from PciBusLib to get setup data or Defaults
#if (PCI_SETUP_USE_APTIO_4_STYLE == 0)
        Status = AmiPciGetSetupData (gPciCommonSetup,
                                  &gPciDefaultSetup->PciDevSettings, 
                                  &gPciDefaultSetup->Pcie1Settings,
                                  &gPciDefaultSetup->Pcie2Settings,
                                  NULL, 0);
#else
        Status = AmiPciGetSetupData (gPciDefaultSetup,
                                  gPciCommonSetup,
                                  NULL);

#endif
        DEBUG ((EFI_D_INFO, "PciBus: Updating Default Setup Data...............Status=%r\n", Status));
        ASSERT_EFI_ERROR (Status);
        if (EFI_ERROR (Status)) {
            return;
        }

    }
    
    if (PcieCheckPcieCompatible(Device)) {
        if (gPciCommonSetup->SriovSupport) {
            DevAddress.ADDR = Device->Address.ADDR;
            //Check Pci Express Extended Capability header
            DevAddress.Addr.ExtendedRegister = 0x100;

            while (DevAddress.Addr.ExtendedRegister) {
                PciReadStatus = PciCfg32(Device->RbIo, DevAddress, FALSE,
                        &ExtCap.EXT_CAP_HDR);
                ASSERT_EFI_ERROR(PciReadStatus);

                if (ExtCap.ExtCapId == PCIE_CAP_ID_SRIOV) {
                    SRIOVDevice = TRUE;
                    break;
                }
                DevAddress.Addr.ExtendedRegister = ExtCap.NextItemPtr;
            }

            if (SRIOVDevice) {

                VfBar = &Device->PciExpress->SriovData->Bar[0];

                for (BarIndex = 0; BarIndex < PCI_MAX_BAR_NO; BarIndex++) {

                    if ((VfBar[BarIndex].Type >= tBarIo16)&& VfBar[BarIndex].Length)
                    {
                        Device->Bar[BarIndex].Type = tBarUnused;
                        Device->Bar[BarIndex].DiscoveredType = tBarUnused;
                        Device->Bar[BarIndex].Base = 0;
                        Device->Bar[BarIndex].Length = 0;

                    }
                }
                gBS->SetMem(&Device->PciExpress->SriovData->Bar[0],
                        (PCI_MAX_BAR_NO * sizeof(PCI_BAR)), 0);

                for (BarIndex = 0; BarIndex <= PCI_MAX_BAR_NO; BarIndex++) {
                }

            }
            SRIOVDevice = FALSE;
        }
    }
}

/**
    Function to disable the Resources while OOR Occurs. Only Onboard/Bridge/VGA device can be skipped.

    @param *Device             Current device (not necessarily problematic device)
    @param *PciOutOfResHit     Out of resource condition

**/
VOID
EnableDisableResources (
    IN PCI_DEV_INFO  *Device,
    IN BOOLEAN       *PciOutOfResHit )
{

    EFI_STATUS Status = EFI_SUCCESS;
    BOOLEAN IsDeviceVga = FALSE;
//    BOOLEAN SRIOVDevice = FALSE;
    UINTN Index;

//    PCI_BAR *bbar = Device->Bar;

    if ((Device->Class.BaseClassCode == PCI_CLASS_DISPLAY && Device->Class.SubClassCode == PCI_CLASS_DISPLAY_VGA) ||
            (Device->Class.BaseClassCode == PCI_CLASS_DISPLAY && Device->Class.SubClassCode == PCI_CLASS_DISPLAY_OTHER) ||
            (Device->Class.BaseClassCode == PCI_CLASS_MEDIA && Device->Class.SubClassCode == PCI_CLASS_DISPLAY_VGA)) {
            IsDeviceVga = TRUE;
    }

    //if (Device->Address.Addr.Bus == 0)
    //{
    //    DEBUG((EFI_D_INFO, "\n Bus 0 onboard Devices won't be SKIPPED\n"));
    //    DEBUG((EFI_D_INFO, "BUS 0 devices BUS = %x DEVICE = %x FUNCTION = %x", Device->Address.Addr.Bus, Device->Address.Addr.Device, Device->Address.Addr.Function));
    //   return; // Do not skip the device
    //}
    
    if (((Device->AmiSdlPciDevData != NULL) && (Device->AmiSdlPciDevData->PciDevFlags.Bits.OnBoard == 1) && (Device->AmiSdlPciDevData->PciDevFlags.Bits.PciDevice == 1)) && (!IsDeviceVga))
    {
        DEBUG((EFI_D_INFO, "\n  Onboard PciDevice won't be SKIPPED\n"));
        DEBUG((EFI_D_INFO, "Onboard PciDevices BUS = %x DEVICE = %x FUNCTION = %x", Device->Address.Addr.Bus, Device->Address.Addr.Device, Device->Address.Addr.Function));
        return; // Do not skip the device
    }
    else
    {
        if (Device->Class.BaseClassCode == PCI_CLASS_BRIDGE)
        { // this is the bridge device
            DEBUG((EFI_D_INFO, "\nBridge Device won't be SKIPPED\n"));
            DEBUG((EFI_D_INFO, "Bridge info : BUS = %x DEVICE = %x FUNCTION = %x\n", Device->Address.Addr.Bus, Device->Address.Addr.Device, Device->Address.Addr.Function));
            return; // Do not skip the device
        }
    }

    //if VGA device
    if ((Device->Class.BaseClassCode == PCI_CLASS_DISPLAY && Device->Class.SubClassCode == PCI_CLASS_DISPLAY_VGA) ||
        (Device->Class.BaseClassCode == PCI_CLASS_DISPLAY && Device->Class.SubClassCode == PCI_CLASS_DISPLAY_OTHER) ||
        (Device->Class.BaseClassCode == PCI_CLASS_MEDIA && Device->Class.SubClassCode == PCI_CLASS_DISPLAY_VGA))
    {
        if (gAtleastOneVideoFound == TRUE)
        {
            Status = EFI_SUCCESS; // FLAG VGA Device for Disabling and SKIP THE VIDEO
        }
        else
        {
            if (Device->AmiSdlPciDevData != NULL)
            {
				Status = AddVgaDevice(Device);
            }
        }
    }

    if (gNumMultiFuncDevices != 0)
    {
        for (Index = 0; Index < gNumMultiFuncDevices; Index++)
        {
            if (Device->DevVenId.DevId == gMultiFuncDevices[Index].DevVenId.DevId &&
                Device->DevVenId.VenId == gMultiFuncDevices[Index].DevVenId.VenId &&
                Device->Address.Addr.Bus == gMultiFuncDevices[Index].Address.Addr.Bus &&
                Device->Address.Addr.Device == gMultiFuncDevices[Index].Address.Addr.Device &&
                Device->Address.Addr.Function == gMultiFuncDevices[Index].Address.Addr.Function)
            {
                Status = EFI_UNSUPPORTED;
            }
        }
    }

    if (Status != EFI_SUCCESS)
    {
        DEBUG((EFI_D_INFO, "Enabling  the PCI device : BUS = %x DEVICE = %x FUNCTION = %x\n", Device->Address.Addr.Bus, Device->Address.Addr.Device, Device->Address.Addr.Function));
        DEBUG((EFI_D_INFO, "Enabling the PCI device : VENDDORID = %x DEVICEID = %x\n", Device->DevVenId.VenId, Device->DevVenId.DevId));
        return;
    }

    // Does this device use  MMIO/IO?
    if (DoesDeviceUseMmio(Device, tBarMmio32) || DoesDeviceUseMmio(Device, tBarMmio32pf) ||
        DoesDeviceUseMmio(Device, tBarIo16) || DoesDeviceUseMmio(Device, tBarIo32))
    {

        // yes, disable its  MMIO/IO
        DEBUG((EFI_D_INFO, "Disabling the PCI device : BUS = %x DEVICE = %x FUNCTION = %x\n", Device->Address.Addr.Bus, Device->Address.Addr.Device, Device->Address.Addr.Function));
        DEBUG((EFI_D_INFO, "VENDDORID = %x DEVICEID = %x\n", Device->DevVenId.VenId, Device->DevVenId.DevId));

        DisableDevResourceType(Device, tBarIo16);
        DisableDevResourceType(Device, tBarIo32);
        DisableDevResourceType(Device, tBarMmio32);
        DisableDevResourceType(Device, tBarMmio32pf);
        // Prevent UEFI driver/legacy OpROM from connecting/starting
        // SetAmiBdsConnectPolicyDisable(Device);

        *PciOutOfResHit = TRUE;
        DisableSRIOVBars(Device, PciOutOfResHit);
    }
}
/**
    Execute passed in function on all devices behind this bridge.
    
    @param *Bridge              Pointer to Bridge Data
    @param Parser               Parsing Function
    @param *Context             Any data the parsing function needs

    @retval VOID       

**/

VOID
ParseAllDevices (
    IN PCI_BRG_INFO   *Bridge,
    IN BOOLEAN        *PciOutOfResHit,
    IN DEVICE_PARSER  Parser )
{
    UINTN Index;

    for (Index = 0; Index < Bridge->Bridge.ChildCount; Index++) {
        if ((Bridge->Bridge.ChildList[Index]->Type == tPci2PciBrg) || (Bridge->Bridge.ChildList[Index]->Type == tPci2CrdBrg)) {
            // Call recursively on next-level bridges
            ParseAllDevices((PCI_BRG_INFO *)(Bridge->Bridge.ChildList[Index]),
                            PciOutOfResHit,
                            Parser);            
        }
        // Call parse function on this device
        Parser(Bridge->Bridge.ChildList[Index], PciOutOfResHit);
    }
}

/**
    Find Root Bridge of this device
    
    @param *Device              Pointer to device data

    @retval PCI_DEV_INFO        Pointer to Root bridge data  
**/

PCI_DEV_INFO *
GetRootBridge (
    IN PCI_DEV_INFO  *Device )
{
    if (Device->Type == tPciRootBrg)
        return Device;

    return GetRootBridge(Device->ParentBrg);
}
/**
    If there was a resource overflow, implement the following
    
    @param *Device             Current device (not necessarily problematic device)
    @param *PciOutOfResHit     Out of resource condition

    @retval EFI_STATUS          EFI_SUCCESS       
                                EFI_UNSUPPORTED

**/
EFI_STATUS
EnableDisableDevices(
    IN PCI_DEV_INFO *Device,
    IN BOOLEAN *PciOutOfResHit)
{
    PCI_DEV_INFO *RootBridge;
//    EFI_HANDLE *Handle = NULL;

    RootBridge = GetRootBridge(Device);
    ParseAllDevices((PCI_BRG_INFO *)RootBridge, PciOutOfResHit, EnableDisableResources);
    return EFI_UNSUPPORTED;
}

/**
    If there was a resource overflow, implement the following
    
    @param *Device             Current device (not necessarily problematic device)
    @param Count               Count of already disabled devices
    @param LowResType          MRES_TYPE - type of resource causing overflow
    @param *PciOutOfResHit     Out of resource condition

    @retval EFI_STATUS          EFI_SUCCESS       
                                EFI_UNSUPPORTED
**/

EFI_STATUS
DefaultPciOutOfResourceHandling (
    IN PCI_DEV_INFO  *Device,
    IN UINTN         Count,
    IN UINTN         LowResType,
    IN BOOLEAN       **PciOutOfResHit )
{
    EFI_STATUS Status = EFI_UNSUPPORTED;
    DEBUG((EFI_D_INFO, "In DefaultPciOutOfResourceHandling(): LowResType : %lx \n", LowResType));
    Status = EnableDisableDevices(Device, *PciOutOfResHit);
    //gAtleastOneVideoFound = FALSE;

    return Status;
}
#endif //end OOR support

/**
    This function provide each initial routine in genericsio.c
    
    @param *This                Pointer to an instance of AMI_BOARD_INIT_PROTOCOL.
    @param *Function            Pointer to the function number from which it is called.
    @param *ParameterBlock      Contains a block of parameters of routine.

    @retval EFI_STATUS          EFI_SUCCESS       
                                EFI_UNSUPPORTED
**/

EFI_STATUS
EFIAPI
PciRootPortInitCallback(
    IN AMI_BOARD_INIT_PROTOCOL  *This,
    IN UINTN                    *Function,
    IN OUT   VOID               *ParameterBlock )
{
    EFI_STATUS Status = EFI_UNSUPPORTED;
    AMI_BOARD_INIT_PARAMETER_BLOCK *Args = (AMI_BOARD_INIT_PARAMETER_BLOCK *)ParameterBlock;
    PCI_INIT_STEP InitStep = (PCI_INIT_STEP)Args->InitStep;
    PCI_DEV_INFO *dev = (PCI_DEV_INFO *)Args->Param1;
    
    PCI_NUMA_NODE_MAPPING_PROTOCOL  *PciNumaProtocol;
    UINT32                          Domain;
    EFI_PHYSICAL_ADDRESS            a;   
    ACPI_HDR                        *dsdt;

#if BoardPciRes_SUPPORT
    UINTN *Count;
    UINTN *LowResType;
    BOOLEAN *PciOutOfResHit;
#endif //end OOR support

    //Check if parameters passed are VALID and
    if (Args->Signature != AMI_PCI_PARAM_SIG)
        return EFI_INVALID_PARAMETER;

    if (InitStep >= isPciMaxStep)
        return EFI_INVALID_PARAMETER;

    if (dev == NULL)
        return Status;

    switch (InitStep)
    {
    case isPciGetSetupConfig:
        DEBUG((DEBUG_VERBOSE, "%a:isPciGetSetupConfig\n", __FUNCTION__));
        break;

    case isPciSkipDevice:
        DEBUG((DEBUG_VERBOSE, "%a:isPciSkipDevice\n", __FUNCTION__));
        break;

    case isPciSetAttributes:
        DEBUG((DEBUG_VERBOSE, "%a:isPciSetAttributes\n", __FUNCTION__));
        break;

    case isPciProgramDevice:
        DEBUG((DEBUG_VERBOSE, "%a:isPciProgramDevice\n", __FUNCTION__));
        
        //Locate PciNumaNodeMappingProtocol
        Status = pBS->LocateProtocol(
                         &gPciNumaNodeMappingProtocolGuid,
                         NULL,
                         &PciNumaProtocol
                         );
        ASSERT_EFI_ERROR(Status);
        
        Status = PciNumaProtocol->GetPciP2pNumaNodeDomain(PciNumaProtocol, dev->ParentBrg->Address.Addr.Bus + dev->ParentBrg->RbIo->SegmentNumber * MAX_PCI_BUS_NUMBER_PER_SEGMENT, &Domain);
        ASSERT_EFI_ERROR(Status);
        
        Status=LibGetDsdt(&a,EFI_ACPI_TABLE_VERSION_ALL);
        if(EFI_ERROR(Status)){
            DEBUG((DEBUG_ERROR,"PciRB: Fail to Get DSDT - returned %r\n", Status));
            ASSERT_EFI_ERROR(Status);
            return Status;
        } else dsdt=(ACPI_HDR*)a;
        
        
        //Use parent bus to locate domain
        Status = UpdateAslNameOfDevice(dsdt, dev->AmiSdlPciDevData->AslName, "PRXM", Domain);
        
        dsdt->Checksum = 0;
        dsdt->Checksum = ChsumTbl((UINT8*)dsdt, dsdt->Length);
        
        break;

    case isPcieSetAspm:
        DEBUG((DEBUG_VERBOSE, "%a:isPcieSetAspm\n", __FUNCTION__));
        break;

    case isPcieSetLinkSpeed:
        DEBUG((DEBUG_VERBOSE, "%a:isPcieSetLinkSpeed\n", __FUNCTION__));
        break;

    case isPciGetOptionRom:
        DEBUG((DEBUG_VERBOSE, "%a:isPciGetOptionRom\n", __FUNCTION__));
        break;

    case isPciOutOfResourcesCheck:
        DEBUG((DEBUG_VERBOSE, "%a:isPciOutOfResourcesCheck\n", __FUNCTION__));
#if BoardPciRes_SUPPORT
        Count = (UINTN *)Args->Param2;
        LowResType = (UINTN *)Args->Param3;
        PciOutOfResHit = (BOOLEAN *)Args->Param4;
        Status = DefaultPciOutOfResourceHandling(dev, *Count, *LowResType, &PciOutOfResHit);
#endif //end OOR support
        break;

    case isPciReadyToBoot:
        DEBUG((DEBUG_VERBOSE, "%a:isPciReadyToBoot\n", __FUNCTION__));
        
        break;

    } //switch

    return Status;
}

EFI_STATUS CxlRootBrgInit(
  AMI_BOARD_INIT_PROTOCOL *This,
  IN UINTN                *Function,
  IN OUT VOID             *ParameterBlock
)
{
//Update Standard DEBUGck
    AMI_BOARD_INIT_PARAMETER_BLOCK  *Args=(AMI_BOARD_INIT_PARAMETER_BLOCK*)ParameterBlock;
    PCI_INIT_STEP                   InitStep=(PCI_INIT_STEP)Args->InitStep;
    PCI_ROOT_BRG_DATA               *dev=(PCI_ROOT_BRG_DATA*)Args->Param1;
    PCI_ROOT_BRG_DATA               *node0=dev->Owner->RootBridges[0];
    ASLR_QWORD_ASD                  *res = (ASLR_QWORD_ASD*)Args->Param2;
    EFI_STATUS                      Status=EFI_UNSUPPORTED;
    EFI_PCI_ROOT_BRIDGE_IO_PROTOCOL *RtBrdg;
    AMD_CXL_PORT_INFO_STRUCT        PortInfo;
    RtBrdg = &node0->RbIoProtocol;
    if (gAmdNbioCxlServicesProtocol == NULL) {
        gBS->LocateProtocol (&gAmdNbioCxlServicesProtocolGuid, NULL, &gAmdNbioCxlServicesProtocol);
    }

//---------------------------------

    DEBUG((DEBUG_ERROR,"%a InitStep =%x\n", __FUNCTION__, InitStep ));
    //Skip RBs that aren't there
    switch (InitStep)
        {
            //-------------------------------------------------------------------------
            case isRbCheckPresence:
                DEBUG((DEBUG_VERBOSE,"\n (isCxlRbCheckPresence\n); " ));

                DEBUG((DEBUG_VERBOSE, "Bus = %x, Device = %x, Function = %x\n", dev->RbSdlData->Bus,
                                dev->RbSdlData->Device,
                                dev->RbSdlData->Function));
                dev->NotPresent = FALSE;
                if (gAmdNbioCxlServicesProtocol == NULL) {
                    dev->NotPresent = TRUE;
                } else {
                    Status = gAmdNbioCxlServicesProtocol->CxlGetRootPortInformation (gAmdNbioCxlServicesProtocol, dev->RbSdlData->Bus - 0xE0, &PortInfo);
                    if (EFI_ERROR(Status)) {
                        dev->NotPresent = TRUE;
                    }
                }
                Status= EFI_SUCCESS;
                break;
            case isRbBusUpdate:
                //Check system die count to determine bus mapping

                DEBUG((DEBUG_VERBOSE,"\n (isCxlRbBusUpdate)\n" ));
                {
                    UINT32 busNumLimit = 0;
                    UINT32 busNumBase = 0;

                    if( IsSocBrh() || IsSocBrhd() ){

                        if (gAmdNbioCxlServicesProtocol == NULL) {
                            DEBUG((EFI_D_ERROR, "ERROR: It should support CXL services protocol in this phase\n"));
                            return EFI_DEVICE_ERROR;
                        }

                        Status = gAmdNbioCxlServicesProtocol->CxlGetRootPortInformation (gAmdNbioCxlServicesProtocol, dev->RbSdlData->Bus - 0xE0, &PortInfo);
                        if (EFI_ERROR(Status)) {
                            DEBUG((EFI_D_ERROR, "ERROR: GetCxlPortPresenceInfo\n"));
                            return EFI_DEVICE_ERROR;
                        }

                        busNumBase = PortInfo.EndPointBDF.Address.Bus;
                        busNumLimit = busNumBase;
          } else {
                        DEBUG((EFI_D_ERROR, "Not supported \n"));
                        return EFI_DEVICE_ERROR;
          }
                    //adjust ASLR_QWORD_ASD
                    res->_MIN = busNumBase;
                    res->_MAX = busNumLimit;
                    res->_LEN = (res->_MAX - res->_MIN) + 1;
                }

                //adjust AMI_SDL_PCI_DEV_INFO here
                DEBUG ((DEBUG_INFO, "BEFORE Bus = %x, Device = %x, Function = %x\n", dev->RbSdlData->Bus,
                                dev->RbSdlData->Device,
                                dev->RbSdlData->Function));

                dev->RbSdlData->Bus = (UINT8) res->_MIN;

                DEBUG ((DEBUG_INFO, "AFTER Bus = %x, Device = %x, Function = %x\n", dev->RbSdlData->Bus,
                                dev->RbSdlData->Device,
                                dev->RbSdlData->Function));
                Status= EFI_SUCCESS;
                break;
        }//switch
    //---------------------------------

    return Status;

}

