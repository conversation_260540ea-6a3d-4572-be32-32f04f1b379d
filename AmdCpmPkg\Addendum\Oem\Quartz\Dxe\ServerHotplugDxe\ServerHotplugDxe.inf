#pragma message( "Compal Server KERP3 Override Compiling-" __FILE__ )
#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ServerHotplugDxe
  FILE_GUID                      = FAFF8CA9-E515-44ed-B5F9-E2F6E5D902E3
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0

  ENTRY_POINT                    = HotplugDescEntry

[Sources]
  ServerHotplugDxe.c

[Packages]
  AgesaPkg/AgesaPkg.dec
  MdePkg/MdePkg.dec
  AmdCpmPkg/AmdCpmPkg.dec
  OemboardPkg/OemboardPkg.dec   #COMPAL_CHANGE


[LibraryClasses]
  UefiDriverEntryPoint
  OemBoardInfoLib               #COMPAL_CHANGE


[PCD]
gAmdCpmPkgTokenSpaceGuid.PcdCpmBoardRevId
gAmdCpmPkgTokenSpaceGuid.PcdCpmAmberBackplaneId
gAmdCpmPkgTokenSpaceGuid.PcdCpmAmberRetimerBackplaneId
gAmdCpmPkgTokenSpaceGuid.PcdCpmEntSsdGBackplaneId
gAmdCpmPkgTokenSpaceGuid.PcdCpmEntSsdPBackplaneId
gAmdCpmPkgTokenSpaceGuid.PcdCpmExpModeABackplaneId
gAmdCpmPkgTokenSpaceGuid.PcdCpmExpModeBBackplaneId
gAmdCpmPkgTokenSpaceGuid.PcdCpmSimPresenceBackplaneId
gAmdCpmPkgTokenSpaceGuid.PcdCpmPBackplaneRevId

[Protocols]
  gAmdHotplugDescProtocolGuid    #Produced
  gOemboardPkgSkuIdGuid          #Produced  #COMPAL_CHANGE

[Depex]
  TRUE
