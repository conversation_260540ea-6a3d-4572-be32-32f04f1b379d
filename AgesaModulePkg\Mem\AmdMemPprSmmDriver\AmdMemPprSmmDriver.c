/*****************************************************************************
 * Copyright (C) 2018-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * AMD Memory API, and related functions.
 *
 * Contains code that initializes memory
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  Mem
 * @e \$Revision:  $   @e \$Date:  $
 *
 */

#include <PiDxe.h>
#include <Porting.h>
#include <Library/AmdPspApobLib.h>
#include <Library/AmdBaseLib.h>
#include <Library/AmdSocBaseLib.h>
#include <Library/IdsLib.h>
#include <Library/BaseMemoryLib.h>
#include <Protocol/SmmBase2.h>
#include <Library/SmmServicesTableLib.h>
#include <Protocol/AmdMemPprProtocol.h>
#include <Protocol/AmdApcbProtocol.h>
#include "AmdMemPprSmmDriver.h"

#define FILECODE MEM_AMDMEMPPRSMMDRIVER_AMDMEMPPRSMMDRIVER_FILECODE

/**
 * @brief Spd Die decode
 * @details Decodes SPD Dies Per Package from SPD_DIE_PER_PKG_TABLE
 *
 * @param SpdDies Index to SPD_DIE_PER_PKG_TABLE
 *
 * @returns Dies value
 */
UINT8
DecodeSpdDiesPerPackage (
  UINT8 SpdDies
  )
{
  UINT8 SpdDiePerPkgTable[] = {1, 0, 2, 4, 8, 16};
  UINT8 Dies;

  if (SpdDies >= sizeof(SpdDiePerPkgTable)) {
    Dies = 0;
  } else {
    Dies = SpdDiePerPkgTable[SpdDies];
  }
  return Dies;
}

/*++

Routine Description:

  Smm Driver to extrat the Post Package Repair Info

Arguments:

Returns:

  EFI_STATUS

--*/
EFI_STATUS
EFIAPI
GetPostPackageRepairStatus (
  )
{
  EFI_STATUS                      Status;
  APOB_TYPE_HEADER                *ApobEntry;
  APOB_MEM_GENERAL_CONFIGURATION_INFO_TYPE_STRUCT_PPR_V2 ApobMemGenConfigPprV2;
  APOB_MEM_GENERAL_CONFIGURATION_INFO_TYPE_STRUCT_PPR_V3 ApobMemGenConfigPprV3;
  APOB_DPPR_STRUCT_V2             *PostPackageRepairPtrV2 = NULL;
  APOB_DPPR_STRUCT_V3             *PostPackageRepairPtrV3 = NULL;
  UINT8                           Socket;
  UINT16                          Instance;
  UINT8                           Index;
  UINT32                          *PostPackageRepairDW;
  UINT16                          i;
  UINT16                          j;
  AMD_APCB_SERVICE_PROTOCOL       *AmdApcbService = NULL;
  VOID                            *EntryArray = NULL;
  UINT32                          NumOfEntries = 0;
  UINT32                          Entry;
  BOOLEAN                         FlushRequired = FALSE;
  UINT8                           EntryStructVersion;

  if (ISSOCBRH || ISSOCBRHD) {
    EntryStructVersion = DPPR_REPAIR_ENTRY_STRUCT_VERSION_V3;
  } else {
    EntryStructVersion = DPPR_REPAIR_ENTRY_STRUCT_VERSION_V2;
  }

  Status = gSmst->SmmLocateProtocol (&gAmdApcbSmmServiceProtocolGuid, NULL, (VOID**)&AmdApcbService );
  if (!EFI_ERROR(Status)) {
    Status = AmdApcbService->ApcbGetDramPostPkgRepairEntriesEx (AmdApcbService, EntryStructVersion, NULL, &NumOfEntries);
    if (!EFI_ERROR(Status)) {
      if (EntryStructVersion == DPPR_REPAIR_ENTRY_STRUCT_VERSION_V3) {
        Status = gSmst->SmmAllocatePool (EfiRuntimeServicesData, NumOfEntries * sizeof (DPPR_REPAIR_ENTRY_V3), (VOID**)&EntryArray );
      } else {
        Status = gSmst->SmmAllocatePool (EfiRuntimeServicesData, NumOfEntries * sizeof (DPPR_REPAIR_ENTRY_V2), (VOID**)&EntryArray );
      }

      if (!EFI_ERROR(Status)) {
        Status = AmdApcbService->ApcbGetDramPostPkgRepairEntriesEx (AmdApcbService, EntryStructVersion, EntryArray, &NumOfEntries);
        if (EFI_ERROR(Status)) {
          NumOfEntries = 0;
        }
      }
    }
  }

  //
  // Update SPD data
  //
  IDS_HDT_CONSOLE (MAIN_FLOW, "Check APOB PostPackageRepair for PPR Data\n");
  for (Socket = 0; Socket < AMD_MEM_MAX_SOCKETS_SUPPORTED; Socket++) {
    Instance = ((Socket & 0x000000FF) << 8);
    Status = AmdPspGetApobEntryInstance (APOB_MEM, APOB_MEM_GENERAL_CONFIGURATION_INFO_TYPE, Instance, FALSE, &ApobEntry);
    if (Status == EFI_SUCCESS) {
      if (EntryStructVersion == DPPR_REPAIR_ENTRY_STRUCT_VERSION_V3) {
        CopyMem (&ApobMemGenConfigPprV3, ApobEntry, sizeof (APOB_MEM_GENERAL_CONFIGURATION_INFO_TYPE_STRUCT_PPR_V3));
        PostPackageRepairPtrV3 = &ApobMemGenConfigPprV3.DdrPostPackageRepair;
        PostPackageRepairDW = (UINT32 *) PostPackageRepairPtrV3;
      } else {
        CopyMem (&ApobMemGenConfigPprV2, ApobEntry, sizeof (APOB_MEM_GENERAL_CONFIGURATION_INFO_TYPE_STRUCT_PPR_V2));
        PostPackageRepairPtrV2 = &ApobMemGenConfigPprV2.DdrPostPackageRepair;
        PostPackageRepairDW = (UINT32 *) PostPackageRepairPtrV2;
      }

      IDS_HDT_CONSOLE (MAIN_FLOW, "Socket %d, APOB_MEM_GENERAL_CONFIGURATION_INFO_TYPE_STRUCT location = 0x%08x\n", Socket, ApobEntry);
      IDS_HDT_CONSOLE (MAIN_FLOW, "PostPackageRepair APOB_DPPR_STRUCT_V%d:\n", EntryStructVersion);
      i = 0;
      for (j = 0; j < 16; j++) {
        IDS_HDT_CONSOLE (MAIN_FLOW, "%08x  %08x  %08x  %08x\n", PostPackageRepairDW[i], PostPackageRepairDW[i + 1], PostPackageRepairDW[i + 2], PostPackageRepairDW[i + 3]);
        i = i + 4;
      }
      IDS_HDT_CONSOLE (MAIN_FLOW, "%08x\n", PostPackageRepairDW[i]);

      if (EntryStructVersion == DPPR_REPAIR_ENTRY_STRUCT_VERSION_V3) {
        if (PostPackageRepairPtrV3->PprResultsValid == TRUE) {
          FlushRequired = FALSE;
          IDS_HDT_CONSOLE (MAIN_FLOW, "PPR valid\n");
          for (Index = 0; Index < APOB_MAX_DPPRCL_ENTRY; Index++) {
            if (PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.Valid) {
              IDS_HDT_CONSOLE (MAIN_FLOW, "\n\t\t Index Entry: %02x, Repair Status: %02x, Repair Type: %x, HardPPRDone: %x, Socket: %x, Channel: %x, SubChannel: %x, ChipSelect: %x, "
              "RankMultiplier: %x, Bank Address: %02x, Row Address: %05x, Device: %02x, TargetDevice: %02x, ErrorCause: %x",
              Index, PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.RepairResult,
              PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.RepairType,
              PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.HardPPRDone,
              PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.Socket,
              PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.Channel,
              PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.SubChannel,
              PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.ChipSelect,
              PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.RankMultiplier,
              PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.Bank,
              PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.Row,
              PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.Device,
              PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.TargetDevice,
              PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.ErrorCause);
              if (PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.RepairResult == APOB_STATUS_REPAIR_PASS) {
                for (Entry = 0; Entry < NumOfEntries; Entry++) {
                  if ((((DPPR_REPAIR_ENTRY_V3 *)EntryArray)[Entry].ddr.RepairType == PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.RepairType) &&
                      (((DPPR_REPAIR_ENTRY_V3 *)EntryArray)[Entry].ddr.HardPPRDone == 0) &&
                      (((DPPR_REPAIR_ENTRY_V3 *)EntryArray)[Entry].ddr.Socket == PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.Socket) &&
                      (((DPPR_REPAIR_ENTRY_V3 *)EntryArray)[Entry].ddr.Channel == PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.Channel) &&
                      (((DPPR_REPAIR_ENTRY_V3 *)EntryArray)[Entry].ddr.SubChannel == PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.SubChannel) &&
                      (((DPPR_REPAIR_ENTRY_V3 *)EntryArray)[Entry].ddr.ChipSelect == PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.ChipSelect) &&
                      (((DPPR_REPAIR_ENTRY_V3 *)EntryArray)[Entry].ddr.RankMultiplier == PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.RankMultiplier) &&
                      (((DPPR_REPAIR_ENTRY_V3 *)EntryArray)[Entry].ddr.Bank == PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.Bank) &&
                      (((DPPR_REPAIR_ENTRY_V3 *)EntryArray)[Entry].ddr.Row == PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.Row) &&
                      (((DPPR_REPAIR_ENTRY_V3 *)EntryArray)[Entry].ddr.Device == PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.Device) &&
                      (((DPPR_REPAIR_ENTRY_V3 *)EntryArray)[Entry].ddr.TargetDevice == PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.TargetDevice)) {
                    // Remove the entry, mark HardPPRDone, then add the entry back
                    Status = AmdApcbService->ApcbRemoveDramPostPkgRepairEntryEx (AmdApcbService, DPPR_REPAIR_ENTRY_STRUCT_VERSION_V3, &((DPPR_REPAIR_ENTRY_V3 *)EntryArray)[Entry]);
                    ((DPPR_REPAIR_ENTRY_V3 *)EntryArray)[Entry].ddr.RepairResult = PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.RepairResult;
                    if (PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.RepairType == APOB_DPPR_HARD_REPAIR) {
                      ((DPPR_REPAIR_ENTRY_V3 *)EntryArray)[Entry].ddr.HardPPRDone = 1;
                    }
                    Status = AmdApcbService->ApcbAddDramPostPkgRepairEntryEx (AmdApcbService, DPPR_REPAIR_ENTRY_STRUCT_VERSION_V3, &((DPPR_REPAIR_ENTRY_V3 *)EntryArray)[Entry]);
                    if (!EFI_ERROR (Status) && (PostPackageRepairPtrV3->Channel[0].DppRclReportEntry[Index].ddr.RepairType == APOB_DPPR_HARD_REPAIR)) {
                      IDS_HDT_CONSOLE (MAIN_FLOW, " (HardPPRDone set)");
                      FlushRequired = TRUE;
                    }
                    break;
                  }
                }
              }
              IDS_HDT_CONSOLE (MAIN_FLOW, "\n");
            }
          }
          if (FlushRequired) {
            Status = AmdApcbService->ApcbFlushData (AmdApcbService);
            if (EFI_ERROR(Status)) {
              IDS_HDT_CONSOLE (MAIN_FLOW, "ApcbFlushData failed, Status = %r\n", Status);
            }
          }
        } else {
          IDS_HDT_CONSOLE (MAIN_FLOW, "PPR invalid\n");
        }
      } else { // V2 structure
        if (PostPackageRepairPtrV2->PprResultsValid == TRUE) {
          FlushRequired = FALSE;
          IDS_HDT_CONSOLE (MAIN_FLOW, "PPR valid\n");
          for (Index = 0; Index < APOB_MAX_DPPRCL_ENTRY; Index++) {
            if (PostPackageRepairPtrV2->Channel[0].DppRclReportEntry[Index].ddr.Valid) {
              IDS_HDT_CONSOLE (MAIN_FLOW, "\n\t\t Index Entry: %02x, Repair Status: %02x, Repair Type: %x, Socket: %x, Channel: %x, SubChannel: %x, ChipSelect: %x, "
              "RankMultiplier: %x, Bank Address: %02x, Row Address: %05x, Device: %02x, TargetDevice: %02x, ErrorCause: %x",
              Index, PostPackageRepairPtrV2->Channel[0].DppRclReportEntry[Index].ddr.Status,
              PostPackageRepairPtrV2->Channel[0].DppRclReportEntry[Index].ddr.RepairType,
              PostPackageRepairPtrV2->Channel[0].DppRclReportEntry[Index].ddr.Socket,
              PostPackageRepairPtrV2->Channel[0].DppRclReportEntry[Index].ddr.Channel,
              PostPackageRepairPtrV2->Channel[0].DppRclReportEntry[Index].ddr.SubChannel,
              PostPackageRepairPtrV2->Channel[0].DppRclReportEntry[Index].ddr.ChipSelect,
              PostPackageRepairPtrV2->Channel[0].DppRclReportEntry[Index].ddr.RankMultiplier,
              PostPackageRepairPtrV2->Channel[0].DppRclReportEntry[Index].ddr.BankAddress,
              PostPackageRepairPtrV2->Channel[0].DppRclReportEntry[Index].ddr.RowAddress,
              PostPackageRepairPtrV2->Channel[0].DppRclReportEntry[Index].ddr.DeviceWidth,
              PostPackageRepairPtrV2->Channel[0].DppRclReportEntry[Index].ddr.TargetDevice,
              PostPackageRepairPtrV2->Channel[0].DppRclReportEntry[Index].ddr.ErrorCause);
              if ((PostPackageRepairPtrV2->Channel[0].DppRclReportEntry[Index].ddr.RepairType == APOB_DPPR_HARD_REPAIR) &&
                  (PostPackageRepairPtrV2->Channel[0].DppRclReportEntry[Index].ddr.Status == APOB_STATUS_REPAIR_PASS)) {
                for (Entry = 0; Entry < NumOfEntries; Entry++) {
                  if ((((DPPR_REPAIR_ENTRY_V2 *)EntryArray)[Entry].ddr.RepairType == PostPackageRepairPtrV2->Channel[0].DppRclReportEntry[Index].ddr.RepairType) &&
                      (((DPPR_REPAIR_ENTRY_V2 *)EntryArray)[Entry].ddr.Socket == PostPackageRepairPtrV2->Channel[0].DppRclReportEntry[Index].ddr.Socket) &&
                      (((DPPR_REPAIR_ENTRY_V2 *)EntryArray)[Entry].ddr.Channel == PostPackageRepairPtrV2->Channel[0].DppRclReportEntry[Index].ddr.Channel) &&
                      (((DPPR_REPAIR_ENTRY_V2 *)EntryArray)[Entry].ddr.SubChannel == PostPackageRepairPtrV2->Channel[0].DppRclReportEntry[Index].ddr.SubChannel) &&
                      (((DPPR_REPAIR_ENTRY_V2 *)EntryArray)[Entry].ddr.ChipSelect == PostPackageRepairPtrV2->Channel[0].DppRclReportEntry[Index].ddr.ChipSelect) &&
                      (((DPPR_REPAIR_ENTRY_V2 *)EntryArray)[Entry].ddr.RankMultiplier == PostPackageRepairPtrV2->Channel[0].DppRclReportEntry[Index].ddr.RankMultiplier) &&
                      (((DPPR_REPAIR_ENTRY_V2 *)EntryArray)[Entry].ddr.Bank == PostPackageRepairPtrV2->Channel[0].DppRclReportEntry[Index].ddr.BankAddress) &&
                      (((DPPR_REPAIR_ENTRY_V2 *)EntryArray)[Entry].ddr.Row == PostPackageRepairPtrV2->Channel[0].DppRclReportEntry[Index].ddr.RowAddress) &&
                      (((DPPR_REPAIR_ENTRY_V2 *)EntryArray)[Entry].ddr.Device == PostPackageRepairPtrV2->Channel[0].DppRclReportEntry[Index].ddr.DeviceWidth) &&
                      (((DPPR_REPAIR_ENTRY_V2 *)EntryArray)[Entry].ddr.TargetDevice == PostPackageRepairPtrV2->Channel[0].DppRclReportEntry[Index].ddr.TargetDevice)) {
                    Status = AmdApcbService->ApcbRemoveDramPostPkgRepairEntryEx (AmdApcbService, DPPR_REPAIR_ENTRY_STRUCT_VERSION_V2, &((DPPR_REPAIR_ENTRY_V2 *)EntryArray)[Entry]);
                    if (!EFI_ERROR(Status)) {
                      IDS_HDT_CONSOLE (MAIN_FLOW, " (Removed)");
                      FlushRequired = TRUE;
                    }
                    break;
                  }
                }
              }
              IDS_HDT_CONSOLE (MAIN_FLOW, "\n");
            }
          }
          if (FlushRequired) {
            Status = AmdApcbService->ApcbFlushData (AmdApcbService);
            if (EFI_ERROR(Status)) {
              IDS_HDT_CONSOLE (MAIN_FLOW, "ApcbFlushData failed, Status = %r\n", Status);
            }
          }
        } else {
          IDS_HDT_CONSOLE (MAIN_FLOW, "PPR invalid\n");
        }
      }
    }
    IDS_HDT_CONSOLE (MAIN_FLOW, "\n");
  }

  if (EntryArray) {
    gSmst->SmmFreePool (EntryArray);
  }

  return EFI_SUCCESS;
}

/*++

Routine Description:

  Smm Driver to extrat the Post Package Repair Status Info

Arguments:

Returns:

  EFI_STATUS

--*/
EFI_STATUS
EFIAPI
GetPprInfo (
  IN       AMD_POST_PACKAGE_REPAIR_INFO_PROTOCOL    *This,
  IN       AMD_DIMM_INFO                            *AmdDimmInfo,
     OUT   AMD_PPR_INFO                             *PprInfo
  )
{
  UINT8      Dimm;
  EFI_STATUS Status;
  UINTN      PprArrayIndex;

  Dimm = 0;

  switch (AmdDimmInfo->Chipselect) {
    case 0:
    case 1:
      Dimm = 0;
      break;
    case 2:
    case 3:
      Dimm = 1;
      break;
  }
  //
  // Ppr Array Index
  //
  PprArrayIndex = AmdDimmInfo->SocketId * AMD_MEM_MAX_DIES_PER_SOCKET * AMD_MEM_MAX_CHANNELS_PER_DIE * AMD_MEM_MAX_DIMMS_PER_CHANNEL;
  PprArrayIndex += AmdDimmInfo->DieId * AMD_MEM_MAX_CHANNELS_PER_DIE * AMD_MEM_MAX_DIMMS_PER_CHANNEL;
  PprArrayIndex += AmdDimmInfo->ChannelId * AMD_MEM_MAX_DIMMS_PER_CHANNEL;
  PprArrayIndex += Dimm;
  if (This->AmdPprArray.DimmSpdInfo [PprArrayIndex].IsValidRecord) {
    PprInfo->IsValidRecord = This->AmdPprArray.DimmSpdInfo [PprArrayIndex].IsValidRecord;
    PprInfo->DpprSupported = This->AmdPprArray.DimmSpdInfo [PprArrayIndex].DpprSupported;
    PprInfo->SpprSupported = This->AmdPprArray.DimmSpdInfo [PprArrayIndex].SpprSupported;
    if (AmdDimmInfo->Chipselect % 2) {    //Odd Rank
      PprInfo->DeviceWidth  = (This->AmdPprArray.DimmSpdInfo [PprArrayIndex].DeviceWidth >> 4) & 0x7;
      PprInfo->LogicalRanks = (This->AmdPprArray.DimmSpdInfo [PprArrayIndex].LogicalRanks >> 4) & 0x7;
    } else {                              //Even Rank
      PprInfo->DeviceWidth  = This->AmdPprArray.DimmSpdInfo [PprArrayIndex].DeviceWidth & 0x7;
      PprInfo->LogicalRanks = This->AmdPprArray.DimmSpdInfo [PprArrayIndex].LogicalRanks & 0x7;
    }
    PprInfo->SerialNumber = This->AmdPprArray.DimmSpdInfo [PprArrayIndex].SerialNumber;
    Status = EFI_SUCCESS;
  } else {
    Status = EFI_NOT_FOUND;
  }
  IDS_HDT_CONSOLE (MAIN_FLOW, "AGESA PPR: GetPprInfo \n");
  return Status;
}

/*++

Routine Description:

  Driver Entry for Post Package Reapir

Arguments:

Returns:

  EFI_STATUS

--*/
EFI_STATUS
EFIAPI
AmdMemDramPprDriverEntry (
  IN       EFI_HANDLE         ImageHandle,
  IN       EFI_SYSTEM_TABLE   *SystemTable
  )
{
  EFI_STATUS                      Status;
  APOB_TYPE_HEADER                *ApobEntry;
  APOB_MEM_DIMM_D5_SPD_DATA_STRUCT  *ApobSpdHeaderPtr;
  AMD_POST_PACKAGE_REPAIR_INFO_PROTOCOL  *AmdPprInfoPtr;
  UINT8                           Socket;
  UINT8                           Channel;
  UINT8                           Dimm;
  UINT16                          Instance;
  UINT16                          DieLoop;
  UINT16                          DimmIndex;
  EFI_HANDLE                      SmmHandle;
  UINTN                           PprArrayIndex;
  ApobEntry = NULL;
  AmdPprInfoPtr = NULL;
  SmmHandle = NULL;
  PprArrayIndex = 0;

  IDS_HDT_CONSOLE (MAIN_FLOW, "AGESA Post Package Repair Driver\n");

  //
  // Allocate the SMM Runtime Space for Post Package Repair data
  //

  Status = gSmst->SmmAllocatePool (
                         EfiRuntimeServicesData,
                         sizeof (AMD_POST_PACKAGE_REPAIR_INFO_PROTOCOL),
                         (VOID**)&AmdPprInfoPtr
                         );

  if (EFI_ERROR (Status)) {
    return Status;
  }
  ZeroMem (AmdPprInfoPtr, sizeof (AMD_POST_PACKAGE_REPAIR_INFO_PROTOCOL));

  Status = gSmst->SmmAllocatePool (
                         EfiRuntimeServicesData,
                         sizeof (APOB_MEM_DIMM_D5_SPD_DATA_STRUCT),
                         (VOID**)&ApobSpdHeaderPtr
                         );
  if (EFI_ERROR(Status)) {
    return Status;
  }
  ZeroMem (ApobSpdHeaderPtr, sizeof (APOB_MEM_DIMM_D5_SPD_DATA_STRUCT));

  //
  // Update SPD data
  //
  for (Socket = 0; Socket < AMD_MEM_MAX_SOCKETS_SUPPORTED; Socket++) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "Get Spd Data from APOB for Socket %d\n", Socket);
    for (DieLoop = 0; DieLoop < AMD_MEM_MAX_DIES_PER_SOCKET; DieLoop++) {
      Instance = DieLoop;
      Instance |= ((Socket & 0x000000FF) << 8);

      Status = AmdPspGetApobEntryInstance(APOB_MEM, APOB_MEM_DIMM_SPD_DATA_TYPE, Instance, FALSE, &ApobEntry);
      if (Status == EFI_SUCCESS) {
        CopyMem (ApobSpdHeaderPtr, ApobEntry, sizeof (APOB_MEM_DIMM_D5_SPD_DATA_STRUCT));
        //
        // ApobSpdHeader.MaxChannelsPerSocket represents AMD_MEM_MAX_CHANNELS_PER_DIE
        // instead of AMD_MEM_MAX_CHANNELS_PER_SOCKET
        //
        for (Channel = 0; Channel < AMD_MEM_MAX_CHANNELS_PER_DIE; Channel++) {
          IDS_HDT_CONSOLE (MAIN_FLOW, "Get Spd Data from APOB for Channel %d\n", Channel);
          for (Dimm = 0; Dimm < AMD_MEM_MAX_DIMMS_PER_CHANNEL; Dimm++) {
            //
            // Ppr Array Index
            //
            PprArrayIndex = Socket * AMD_MEM_MAX_DIES_PER_SOCKET * AMD_MEM_MAX_CHANNELS_PER_DIE * AMD_MEM_MAX_DIMMS_PER_CHANNEL;
            PprArrayIndex += DieLoop * AMD_MEM_MAX_CHANNELS_PER_DIE * AMD_MEM_MAX_DIMMS_PER_CHANNEL;
            PprArrayIndex += Channel * AMD_MEM_MAX_DIMMS_PER_CHANNEL;
            PprArrayIndex += Dimm;
            IDS_HDT_CONSOLE (MAIN_FLOW, "Get Spd Data from APOB for Dimm %d\n", Dimm);
            for (DimmIndex = 0; DimmIndex < (sizeof(ApobSpdHeaderPtr->DimmSmbusInfo)/sizeof(APOB_D5_SPD_STRUCT)); DimmIndex++) {
              if ((ApobSpdHeaderPtr->DimmSmbusInfo[DimmIndex].SocketNumber == Socket) &&
                  (ApobSpdHeaderPtr->DimmSmbusInfo[DimmIndex].ChannelNumber == Channel) &&
                  (ApobSpdHeaderPtr->DimmSmbusInfo[DimmIndex].DimmNumber == Dimm) &&
                  (ApobSpdHeaderPtr->DimmSmbusInfo[DimmIndex].DimmPresent == TRUE)) {
                IDS_HDT_CONSOLE (MAIN_FLOW, "Dimm Present is set in APOB Record for SpdInfo on Socket: %x, Die: %x, Channel: %x, Dimm: %x\n", Socket, DieLoop, Channel, Dimm);
                AmdPprInfoPtr->AmdPprArray.DimmSpdInfo[PprArrayIndex].IsValidRecord = TRUE;
                AmdPprInfoPtr->AmdPprArray.DimmSpdInfo[PprArrayIndex].SerialNumber =
                                         ((ApobSpdHeaderPtr->DimmSmbusInfo[DimmIndex].Data[SPD_D5_SERIAL_NUMBER_BYTE_OFFSET_3] << 24) |
                                          (ApobSpdHeaderPtr->DimmSmbusInfo[DimmIndex].Data[SPD_D5_SERIAL_NUMBER_BYTE_OFFSET_2] << 16) |
                                          (ApobSpdHeaderPtr->DimmSmbusInfo[DimmIndex].Data[SPD_D5_SERIAL_NUMBER_BYTE_OFFSET_1] << 8) |
                                          (ApobSpdHeaderPtr->DimmSmbusInfo[DimmIndex].Data[SPD_D5_SERIAL_NUMBER_BYTE_OFFSET_0]));
                AmdPprInfoPtr->AmdPprArray.DimmSpdInfo[PprArrayIndex].DeviceWidth   = ((UINT8) ApobSpdHeaderPtr->DimmSmbusInfo[DimmIndex].Data[SPD_D5_FIRST_DEVICE_WIDTH_BYTE_OFFSET] & 0xE0) >> 5;
                AmdPprInfoPtr->AmdPprArray.DimmSpdInfo[PprArrayIndex].LogicalRanks  =
                  DecodeSpdDiesPerPackage (((UINT8) ApobSpdHeaderPtr->DimmSmbusInfo[DimmIndex].Data[SPD_D5_FIRST_DIE_PER_PACKAGE_BYTE_OFFSET] & 0xE0) >> 5);
                if (ApobSpdHeaderPtr->DimmSmbusInfo[DimmIndex].Data[SPD_D5_MODULE_ORGANIZATION_BYTE_OFFSET] & BIT6) {     // Asymmetrical memory configurations
                  AmdPprInfoPtr->AmdPprArray.DimmSpdInfo[PprArrayIndex].DeviceWidth   |= ((UINT8) ApobSpdHeaderPtr->DimmSmbusInfo[DimmIndex].Data[SPD_D5_SECOND_DEVICE_WIDTH_BYTE_OFFSET] & 0xE0) >> 1;
                  AmdPprInfoPtr->AmdPprArray.DimmSpdInfo[PprArrayIndex].LogicalRanks  |=
                    DecodeSpdDiesPerPackage (((UINT8) ApobSpdHeaderPtr->DimmSmbusInfo[DimmIndex].Data[SPD_D5_SECOND_DIE_PER_PACKAGE_BYTE_OFFSET] & 0xE0) >> 5) << 4;
                } else {
                  AmdPprInfoPtr->AmdPprArray.DimmSpdInfo[PprArrayIndex].DeviceWidth   |= ((UINT8) ApobSpdHeaderPtr->DimmSmbusInfo[DimmIndex].Data[SPD_D5_FIRST_DEVICE_WIDTH_BYTE_OFFSET] & 0xE0) >> 1;
                  AmdPprInfoPtr->AmdPprArray.DimmSpdInfo[PprArrayIndex].LogicalRanks  |=
                    DecodeSpdDiesPerPackage (((UINT8) ApobSpdHeaderPtr->DimmSmbusInfo[DimmIndex].Data[SPD_D5_FIRST_DIE_PER_PACKAGE_BYTE_OFFSET] & 0xE0) >> 5) << 4;
                }
                AmdPprInfoPtr->AmdPprArray.DimmSpdInfo[PprArrayIndex].DpprSupported = TRUE;
                AmdPprInfoPtr->AmdPprArray.DimmSpdInfo[PprArrayIndex].SpprSupported = TRUE;
                break;
              }
            }
          }
        }
      }
    }
  }

  //
  // Process and Print Repair Entries in case we have any
  //
  GetPostPackageRepairStatus ();
  //
  // Install Protocol Interface under SMM
  //
  AmdPprInfoPtr->AmdGetPprInfo = GetPprInfo;
  Status = gSmst->SmmInstallProtocolInterface (
               &SmmHandle,
               &gAmdPostPackageRepairInfoProtocolGuid,
               EFI_NATIVE_INTERFACE,
               AmdPprInfoPtr
               );
  gSmst->SmmFreePool (ApobSpdHeaderPtr);
  return (Status);
}



