#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = AmdNbioDxe
  FILE_GUID                      = 242B3D0C-5FB8-4A75-9CD3-710DDFE42703
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0

  ENTRY_POINT                    = NbioDxeEntryPoint

[Sources]
  EntryPoints.c
  SmuInit.c
  PcieInit.c
  IommuInit.c
  SmuServicesProtocol.c
  CppcServicesProtocol.c
  PcieTopologyProtocol.c
  PcieAer.c
  PcieAer.h
  PcieSris.c
  PcieSris.h
  RASControl.c
  ServerHotplugFeat.c
  SmuSupportFunctions.c
  SmuDebug.c
  PkgTypeFixups.c
  pmfw.h
  SmuFeats.h
  PmmTableInit.c
  NbioHwLock.c
  CxlConfigurePortFeatures.c
  NbioIoapicInit.c
  CxlHotPlugFeatures.c
  PcieAllEndpoints.c

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleNbioPkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaFamily1AModulePkg.dec
  AgesaModulePkg/AgesaModuleCcxPkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec
  AgesaModulePkg/AgesaModuleRasPkg.dec
  AmiCompatibilityPkg/AmiCompatibilityPkg.dec # AMI PORTING

[LibraryClasses]
  NbioCommonDxeLib
  AmdBaseLib
  UefiBootServicesTableLib
  UefiDriverEntryPoint
  IdsLib
  UefiLib
  PcdLib
  HobLib
  PcieMiscCommLib
  NbioHandleLib
  SmnAccessLib
  PcieConfigLib
  AmdIdsHookLib
  NbioIdsHookBrhLibDxe
  GnbPciAccLib
  GnbPciLib
  SmnTableLib
  NbioSmuBrhLib
  OemClkReqControlLib
  NbioUtilLib
  GnbLib
  NbioIommuIvrsLib
  AmdIOMMUDmarLib
  MemoryAllocationLib
  BaseLib
  BaseMemoryLib
  GnbLib
  SynchronizationLib
  AmdPspApobLib
  AmdCapsuleLib
  CcxApicZen5Lib
  MpioLib
  AgesaConfigLib
  DebugLib
  GetPcieResourcesLib
  CxlConfigLib
  NbioServicesLibDxe
  BaseFabricTopologyLib
  AmdPspMboxLibV2

[Guids]
  gEfiEventReadyToBootGuid
  gAmdEventPspRTBDoneGuid

[Protocols]
  gAmdNbioSmuServicesProtocolGuid         # PRODUCED
  gAmdNbioPcieServicesProtocolGuid
  gEfiPciIoProtocolGuid
  gAmdNbioPcieAerProtocolGuid
  gAmdSocLogicalIdProtocolGuid
  gEfiMpServiceProtocolGuid
  gAmdFabricTopologyServices2ProtocolGuid # CONSUMED
  gAmdHotplugDescProtocolGuid
  gAmdNbioSmuInitCompleteProtocolGuid     # PRODUCED
  gAmdNbioCppcServicesProtocolGuid        # PRODUCED
  gEfiPciEnumerationCompleteProtocolGuid
  gAmdApcbDxeServiceProtocolGuid
  gAmdNbioCxlServicesProtocolGuid
  gAmdPciResourceProtocolGuid             # PRODUCED
  gAmdNbioServicesProtocolGuid
  gBdsAllDriversConnectedProtocolGuid     # AMI PORTING

[Pcd]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgAgmLogDramSize
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIohcNonPCIBarInitSmu
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNonPCIBarInitIommuVf
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNonPCIBarInitIommuVfCntl
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNonPCIBarInitIommuVfEnBit
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgPCIeLTREnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdDcBtc
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdDcBtcErrorOfsetVoltageMargin
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdDcBtcVid
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdDldoPsmMargin
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdFllBtcEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdHTPsmMargin
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdIvrsControl
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMocTjMax
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSmuAllocateDramBufferSize
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSmuFeatureControlDefines
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSmuFeatureControlDefinesExt
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSmuFeatureControlDefines64
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSocDcBtcEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdVddcrCpuVoltageMargin
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdxGMIForceLinkWidth
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdxGMIForceLinkWidthEn
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdxGMIMinLinkWidth
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdxGMIMaxLinkWidth
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdxGMIMaxLinkWidthEn
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdGmiFolding
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDeterminismControl
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDeterminismMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdEdpcEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugHandlingMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMCTPEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMCTPMasterID
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioLclkDpmLevel
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMaskNbioSyncFlood
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioEgressPoisonMaskHi
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioEgressPoisonMaskLo
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSyncFloodToApml
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioPoisonConsumption
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioRASControlV2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioRASUcpMaskHi
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioRASUcpMaskLo
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPcieAerReportMechanism
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPcieSyncFloodOnFatal
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmuCc1Dis
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdcTDP
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAEREnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgApbDis
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgFixedSocPstate
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgIommuSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPPT
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgTDC
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPcieTbtSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPlatformTDP
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPlatformPPT
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPlatformTDC
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPlatformEDC
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEfficiencyOptimizedMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFMaxFrequency
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdIvInfoDmaReMap
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdNbioCorrectedErrThreshCount
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdNbioCorrectedErrThreshEn
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOcDisable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOcFrequencyMax
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOcVoltageMax
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieEcrcEnablement
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieEcrcSeverityFatal
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTelemetry_VddcrSocOffset
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTelemetry_VddcrSocfull_Scale_Current
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTelemetry_VddcrVddOffset
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTelemetry_VddcrVddfull_Scale_Current
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDfCstateEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgHSMPSupport
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdPreSilCtrl0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdPreSilCtrl1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdVddioDcBtcEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdBoostFmax
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDFFODisable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdDFLODisable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEgressPoisonSeverityHi
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEgressPoisonSeverityLo
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEnableEgressPoisonSeverity
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioEgressPoisonMaskHi
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioEgressPoisonMaskLo
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMocPBOLimitScalar
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSvi3SvcSpeed
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPowerProfileSelect
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgHotplugMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdHotplugI2cAddress
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdHotplugSlotIndex
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlAspm
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlIoL1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlIoL2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlCaMemL1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlCaMemL2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlMemIsolationEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlMemIsolationFwNotification
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdObffControl
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSyshubWdtTimerInterval
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdRASAcsValue
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPmicErrorReporting
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAllowCompliance
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAutoSpeedChangeEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdLcMultAutoSpdChgOnLastRateEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotplugSynchronousFF
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgGnbIoapicId
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgFchIoapicId
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDfPstateRangeSupportEn
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDfPstateRangeMax
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDfPstateRangeMin
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdThrottlerMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCxlHotPlugSlotTimeOut
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMCTPEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMCTPMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMCTPDiscoveryNotify
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMCTPMasterID
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMCTPMasterSeg
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEarlyBmcLinkLaneNum
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEarlyBmcLinkSocket
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEarlyBmcLinkTraining
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugSettleTime
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugSettleTimeMultiplier
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugPDSettle
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugDLPDSyncCount
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugDisBridgeDis
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSyncHeaderByPass
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgDev0F1PasidEn
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAtomicRoutingEnStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdNbifDev0F1AtomicRequestEn
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgCPPCMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXgmiPstateSelection
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXgmiPstateControl
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugNVMESkipHPStatUpdate
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI3c0SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI3c1SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI3c2SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchI3c3SdaHold
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI3cPPHcnt
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchI3cSpeed
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotplugPortReset
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCtrlUnusedTileClkGating
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCXlEarlyLinkTraining
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMemPostPackageRepairConfigInitiator
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgDiagnosticMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPowerSupplyIdleControl
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdEnvironmentFlag
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIOMMUMultiContextSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgDxioCplTimeout
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgSevTioSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgACSEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPcieAriSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieAriForwardingEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDlfExEnV2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDlfCapEnV2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgSevSnpSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPresenceDetectSelectMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDataObjectExchange
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAdvertiseEqToHighRateSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSrisEnableMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgForcePcieGenSpeed
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTargetPcieGenSpeed
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLinkAspmAllPort
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS0P0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS0P1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS0P2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS0P3
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS1P0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS1P1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS1P2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS1P3
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqPresetMask8GtConfig
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqPresetMask16GtConfig
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqPresetMask32GtConfig
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPcieLoopbackMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEnable2SpcGen4
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlOnAllPorts
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlSpm
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCxlTempGen5AdvertAltPtcl
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlDvsecLock
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlSpaEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlProtocolErrorReporting
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlComponentErrorReporting
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgRxMarginPersistenceMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgSriovEnDev0F1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAriEnDev0F1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAerEnDev0F1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAcsEnDev0F1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAtsEnDev0F1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPasidEnDev0F1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPwrEnDev0F1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgRtrEnDev0F1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPriEnDev0F1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAtcEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsEnRccDev0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAerEnRccDev0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDlfEnStrap1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPhy16gtStrap1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMarginEnStrap1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsSourceValStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsTranslationalBlockingStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsP2pReq
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsP2pCompStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsUpstreamFwdStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsP2PEgressStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsDirectTranslatedStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsSsidEnStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPriEnPageReq
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPriResetPageReq
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPriResetPageReq
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsSourceVal
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsTranslationalBlocking
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsP2pComp
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsUpstreamFwd
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsP2PEgress
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsP2pReqStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdRccDev0E2EPrefix
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdRccDev0ExtendedFmtSupported
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDmaProtection
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverRideEnabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEnable2SpcGen5
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBP0P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBLinkSpeedP0P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBModeP0P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBP0P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBLinkSpeedP0P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBModeP0P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdSmuDsmClkCtrl
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPCIeSFIConfigviaOOBEn
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdDrbStatusCleared

[Depex]
  gAmdNbioBRHDxeDepexProtocolGuid           AND
  gAmdSocLogicalIdProtocolGuid              AND
  gAmdFabricTopologyServices2ProtocolGuid

