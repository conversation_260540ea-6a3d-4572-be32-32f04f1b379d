#pragma message( "Compal Server Override Compiling-" __FILE__ )
#***********************************************************************
#*                                                                     *
#*   Copyright (c) 1985-2023, American Megatrends International LLC.   *
#*                                                                     *
#*      All rights reserved. Subject to AMI licensing agreement.       *
#*                                                                     *
#***********************************************************************

[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = AmiOemRasSmm
  FILE_GUID                      = 8F1A65CF-1D27-4354-9F77-4DB764471B4A
  MODULE_TYPE                    = DXE_SMM_DRIVER
  PI_SPECIFICATION_VERSION       = 0x0001000A
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = AmiOemRasSmmInit

[Sources]
  AmiOemRasSmm.c
  AmiOemRasSmm.h

[Packages]
  MdePkg/MdePkg.dec
  AgesaModulePkg/AgesaModuleRasPkg.dec
  AgesaPkg/AgesaPkg.dec
  AmdCpmPkg/AmdCpmPkg.dec
  AmiModulePkg/AmiModulePkg.dec
  AmiGpnvErrorLoggingPkg/AmiGpnvErrorLoggingPkg.dec
  AmiRasWrapperPkg/AmiRasWrapperPkg.dec
  AmiIpmi2Pkg/AmiIpmi2Pkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  $(AMI_SERVER_MODULE_PKG_DEC)
  OemboardPkg/OemboardPkg.dec      #COMPAL_CHANGE
  
[LibraryClasses]
  BaseMemoryLib
  UefiDriverEntryPoint
  SmmServicesTableLib
  DebugLib
  CpmRasLib
  RasCommonLibBrh
  RasIpmiLibBrh
  $(AMIOEMRAS_LIB_CLASSES_LIST)

[Guids]

[Pcd]
  $(AMI_SYSTEM_INV_PCD)
  
[FixedPcd]
  gWheaTokenSpaceGuid.PcdErrorLogDataBufferSize    # CONSUMES

[Protocols]
  gAmdCpmRasOemSmmProtocolGuid              #CONSUMED
  gEfiRedirElogProtocolGuid                 #CONSUMED
  gAmdPlatformApeiDataProtocolGuid          #COMPAL_CHANGE
  gSmmIpmiTransport2ProtocolGuid
  gAmdRasSmm2ProtocolGuid
  $(AMI_SYSTEM_INV_INFO_PROTOCOL_GUID)
  gOemboardPkgSkuIdGuid                     #COMPAL_CHANGE

[Depex]
  AFTER gAmdPlatformRasBrhSmmFileGuid
  #gEfiRedirElogProtocolGuid