#***********************************************************************
#*                                                                     *
#*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
#*                                                                     *
#*      All rights reserved. Subject to AMI licensing agreement.       *
#*                                                                     *
#***********************************************************************
## @file
#	Includes main build module for TcgNist sub-components
# 1. Create Golden Measurement(GM) value after final .ROM image is created
# 2. Update template TCG Reference Manifest XML form with the GM record and 
#    BIOS static configuration parameters for NIST SP800-155 reporting 
##

ifneq ($(BUILD_OS), $(BUILD_OS_WINDOWS))
$(error  Tcg NISTsp800-155: Unsupported OS. Only Windows tools are provided to generate TCG Reference Manifest $(TCG_RM_XML_OUTPUT_FILE))
endif

.PHONY : TcgXmlClear TcgFvMainMeasure

#Form Date:Time string, e.g. 2014-03-27T12:51:35
TCG_RM_DATE := $(shell $(DATE) +'%Y-%m-%dT%T')
#TCG_RM_DATE := $(shell $(DATE) +'%Y-%m-%dT%H:%M:%S'))

#---------------------------------------------------------------------------

clean: TcgXmlClear
Prepare: TcgXmlClear $(TCG_RM_XML_OUTPUT_DIR)

TcgXmlClear:
ifneq ("$(wildcard  $(TCG_RM_XML_OUTPUT_DIR)$(PATH_SLASH)gm_sha1.ber)", "")
	-$(RM) $(TCG_RM_XML_OUTPUT_DIR)$(PATH_SLASH)gm_sha*.*
endif
ifneq ("$(wildcard  $(TCG_RM_XML_OUTPUT_FILE))", "")
	-$(RM) $(TCG_RM_XML_OUTPUT_FILE)
endif

$(TCG_RM_XML_OUTPUT_DIR):
	$(MKDIR) $(TCG_RM_XML_OUTPUT_DIR)

#---------------------------------------------------------------------------
# Should be the last step after creating of the ROM image. All fixups to the .ROM must be made prior to this step.
# check END target in the MAIN.MAK and all .MAK files to make sure this step is not overriden
#---------------------------------------------------------------------------
$(TCG_RM_BUILD_TARGET) : TcgFvMainMeasure

TcgFvMainMeasure: $(TCG_RM_ROM_FILE)
#   TPM 2.0 based platform support multiple SHA hash algorithms.
#   ->  Invoke CRYPTOCON command line for each SHA384, SHA256 and SHA512 algorithm
#       to generate 3 different values of same BIOS "golden" measurement
#       Output is stored in bin format for compatiblility with xml Rimm schema data storage format
	$(ECHO) ----Record measurement of BIOS ROM for NIST SP800-155 reporting
#
#	Copy un-signed BIOS Image "$(ROM_FILE_NAME)' if '$(TCG_RM_ROM_FILE)' file doesn't exist
	@if not exist $(TCG_RM_ROM_FILE) $(CP) $(ROM_FILE_NAME) $(TCG_RM_ROM_FILE)
	$(XMLCRYPTCON) -h2 $(TCG_RM_ROM_LAYOUT_FILE) -t -f$(TCG_RM_ROM_FILE) -o$(TCG_RM_XML_OUTPUT_DIR)$(PATH_SLASH)gm_sha2.bin
	$(XMLCRYPTCON) -h3 $(TCG_RM_ROM_LAYOUT_FILE) -t -f$(TCG_RM_ROM_FILE) -o$(TCG_RM_XML_OUTPUT_DIR)$(PATH_SLASH)gm_sha3.bin
	$(XMLCRYPTCON) -h5 $(TCG_RM_ROM_LAYOUT_FILE) -t -f$(TCG_RM_ROM_FILE) -o$(TCG_RM_XML_OUTPUT_DIR)$(PATH_SLASH)gm_sha5.bin
ifeq ($(TcgXmlRimm_support),1)
#   For TPM 2.0 bound systems with multiple SHA hash algorithms are supported.
#   ->  Invoke XMLTCGRIM command line for each SHA384, SHA256 and SHA512 value of BIOS "golden" measurement
	$(ECHO) ----Update Reference Manifest from Template file
#	for %%i in ($(TCG_RM_ROM_FILE)) do SET RomSize=%%~zi
#	SET Sz=%%~z$(TCG_RM_ROM_FILE)
#	echo ====%RomSize%==== OR ====%Sz%=====
	$(XMLRIMM) --hash SHA256 --gm $(TCG_RM_XML_OUTPUT_DIR)$(PATH_SLASH)gm_sha2.bin $(subst ',",$(XMLRIMM_CMDLINE)) -f $(TCG_RM_XML_TEMPLATE_FILE) -o $(TCG_RM_XML_OUTPUT_FILE)
	$(XMLRIMM) --hash SHA384 --gm $(TCG_RM_XML_OUTPUT_DIR)$(PATH_SLASH)gm_sha3.bin -f $(TCG_RM_XML_OUTPUT_FILE) -o $(TCG_RM_XML_OUTPUT_FILE)
	$(XMLRIMM) --hash SHA512 --gm $(TCG_RM_XML_OUTPUT_DIR)$(PATH_SLASH)gm_sha5.bin -f $(TCG_RM_XML_OUTPUT_FILE) -o $(TCG_RM_XML_OUTPUT_FILE)
#	$(CP) $(TCG_RM_XML_BIOS_ASSERTION_SCHEMA_FILE) $(TCG_RM_XML_OUTPUT_DIR)
ifeq ($(TcgXmlRimm_signer_support),1)
	$(ECHO) ----Sign Reference Manifest file
	$(XMLSIG) --sign --x509 $(XMLSIG_CMDLINE) -f$(TCG_RM_XML_OUTPUT_FILE) -o$(TCG_RM_XML_OUTPUT_FILE)
# Debug: verify xml signature
	$(ECHO) ----Verify Reference Manifest signature
	$(XMLSIG) --verify --x509 $(XMLSIG_CMDLINE) -f$(TCG_RM_XML_OUTPUT_FILE)
endif
endif

