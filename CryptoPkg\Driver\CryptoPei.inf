#***********************************************************************
#*                                                                     *
#*   Copyright (c) 1985-2025, American Megatrends International LLC.   *
#*                                                                     *
#*      All rights reserved. Subject to AMI licensing agreement.       *
#*                                                                     *
#***********************************************************************

## @file
#  Produces the EDK II Crypto PPI using the library services from BaseCryptLib
#  and TlsLib.  PcdCryptoServiceFamilyEnable is used to enable the subset of
#  available services.  If this PEIM is dispatched before memory is discovered,
#  the RegisterForShadow() feature is used to reload this PEIM into memory after
#  memory is discovered.
#
#  Copyright (C) Microsoft Corporation. All rights reserved.
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x0001001B
  BASE_NAME                      = CryptoPei
  MODULE_UNI_FILE                = Crypto.uni
  FILE_GUID                      = 0D1CE46B-72D9-4BA7-95DA-23511865E661
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = CryptoPeiEntry

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 ARM AARCH64
#

[Sources]
  Crypto.c
  CryptoPei.c

[Packages]
  MdePkg/MdePkg.dec
  CryptoPkg/CryptoPkg.dec

[LibraryClasses]
  PeimEntryPoint
  PeiServicesLib
  DebugLib
  BaseCryptLib
  TlsLib

[BuildOptions]
  #MSFT:*_*_IA32_DLINK_FLAGS = /ALIGN:4096  // APTIOV OVERRIDE -TODO
  #MSFT:*_*_X64_DLINK_FLAGS  = /ALIGN:4096  // APTIOV OVERRIDE -TODO
  #MSFT:*_*_X64_DLINK_XIPFLAGS  = /ALIGN:4096  // APTIOV OVERRIDE- We don't have a DLINK_XIPFLAGS
  #MSFT:*_*_IA32_DLINK_XIPFLAGS = /ALIGN:4096     defines for MSFT
  #GCC:*_*_AARCH64_DLINK_XIPFLAGS = -z common-page-size=0x1000  // APTIOV OVERRIDE - TODO
  MSFT:DEBUG_*_*_CC_FLAGS = /O1 #APTIOV_SERVER_OVERRIDE - JIRA#T2-496to reduce size in debug build
  
[Ppis]
  gEfiPeiMemoryDiscoveredPpiGuid  ## CONSUMES
  gEdkiiCryptoPpiGuid             ## PRODUCES

[Pcd]
  gEfiCryptoPkgTokenSpaceGuid.PcdCryptoServiceFamilyEnable  ## CONSUMES

[Depex]
  TRUE
