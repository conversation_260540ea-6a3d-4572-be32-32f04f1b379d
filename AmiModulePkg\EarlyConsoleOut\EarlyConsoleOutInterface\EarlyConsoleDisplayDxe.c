//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2025, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************

/** @file EarlyConsoleDisplayPeiLib.c

**/
#include <Library/UefiBootServicesTableLib.h>
#include <Library/AmiVideoTextOutLib.h>
#include <Protocol/PciIo.h>
#include <Protocol/DevicePath.h>
#include <Protocol/AmiConInStarted.h>
#include <Ppi/AmiSimpleTextOutPpi.h> // Added for AMI_PEI_SIMPLE_TEXT_CONSOLE_TYPE
#include <Guid/GraphicsInfoHob.h>
#include <IndustryStandard/Pci.h>
#include <EarlyConsoleElink.h>
#include <EarlyConsoleDisplay.h>
#include <Token.h>

EARLY_CONSOLE_DISPLAY_FRAME_INFO_HOB  *gFrameInfoHobData;
BOOLEAN gGraphicsModeEnabled  = FALSE;
BOOLEAN gConnectDriversCalled = FALSE;
BOOLEAN gPciEnumerationStarted = FALSE;
BOOLEAN gIsSerialDisplay      = FALSE;
BOOLEAN gEfiGopAvailable      = FALSE;

static VOID         *gRegistration = NULL;
static EFI_HANDLE   *gHandleBuffer = NULL;
static UINTN        gHandleCount = 0;
CHAR8 *gHotkeyList[] = {AMI_EARLY_CONSOLE_HOTKEY_LIST "\0"};

HOTKEY_LOCATION  *gHotkeyLocation;
UINT8            gHotkeyCount;

DISPLAY_FRAME_INFO*
GetDisplayFrameInfo (
    AMI_PEI_SIMPLE_TEXT_CONSOLE_TYPE   ConsoleType
);

EFI_STATUS
UpdateGopProtocolPointers(
    DISPLAY_FRAME_INFO     *DisplayFrameInfo
);

VOID
OutputStringInAllGops (
    BOOLEAN                          IsAttribute,
    UINTN                            Attribute,
    AMI_EARLY_GRAPHICS_FRAME_INFO    *FrameInfo,
    CHAR16                           *String
);

VOID
DisplayBltInAllGops (
    EFI_GRAPHICS_OUTPUT_BLT_PIXEL           *BltBuffer    OPTIONAL,
    EFI_GRAPHICS_OUTPUT_BLT_OPERATION       BltOperation,
    UINTN                                   SourceX,
    UINTN                                   SourceY,
    UINTN                                   DestinationX,
    UINTN                                   DestinationY,
    UINTN                                   Width,
    UINTN                                   Height,
    UINTN                                   Delta         OPTIONAL
);

VOID
ProcessString (
  IN EFI_SIMPLE_TEXT_OUTPUT_PROTOCOL        *SimpleTextOut,
  IN EFI_GRAPHICS_OUTPUT_PROTOCOL           *GraphicsOutput,
  IN AMI_EARLY_GRAPHICS_FRAME_INFO          *FrameInfo,
  IN CHAR16                                 *String
);

/**
    Returns the maximum length of the hotkey strings
    
    @param None
    
    @return UINTN
**/
UINTN
GetMaxLength()
{
    UINT8   Index;
    UINTN   MaxLength = 0;
    UINTN   Length;
    
    for (Index = 0; Index < gHotkeyCount; Index++) {
        Length = AsciiStrLen(gHotkeyList[Index]);
        if (Length > MaxLength) {
            MaxLength = Length;
        }
    }
    return MaxLength;
}

/**
    Returns sum of hotkey strings length
    
    @param None
    
    @return UINTN
**/
UINTN
GetTotalStringLength()
{
    UINT8   Index;
    UINTN   TotalLength = 0;
    
    for (Index = 0; Index < gHotkeyCount; Index++) {
        TotalLength += AsciiStrLen(gHotkeyList[Index]);
    }
    return TotalLength;
}

/**
    Event to display the hotkey frame
    
    @param Event
    @param Context
    
    @return VOID
**/
VOID
EFIAPI
ConInStartedProtocolCallBack (
    IN EFI_EVENT  Event,
    IN VOID       *Context         
)
{
    DISPLAY_FRAME_INFO              *DisplayFrameInfo;
    AMI_EARLY_GRAPHICS_FRAME_INFO   *FrameInfo;
    UINTN                           HotkeyStrlen;
    UINT8                           Index;
    UINT8                           Index1;
    CHAR16                          UnicodeStr[20];
    UINT32                          Length;
    UINTN                           StartX;
    UINTN                           StartY;
    EFI_GRAPHICS_OUTPUT_BLT_PIXEL   WhiteColor = {0xFF, 0xFF, 0xFF, 0};
    EFI_GRAPHICS_OUTPUT_BLT_PIXEL   BlackColor = {0, 0, 0, 0};
    UINT8                           BorderWidth = 2;
    UINTN                           MaxLength;
    UINT32                          MaxColumn;
    UINT32                          StartColumn;
    CHAR8                           *HotkeyStr;
    UINT8                           ColonPlace = 0;

    DEBUG((DEBUG_ERROR, "%a\n", __FUNCTION__));
    gBS->CloseEvent (Event);
    
    DisplayFrameInfo = GetDisplayFrameInfo (PeiSimpleTextOutConsoleTypeVideoGop);
    if (DisplayFrameInfo == NULL) {
        return;
    }
    
    FrameInfo = &DisplayFrameInfo->FrameInfo[EarlyConsoleDisplayFrameHotkey];
    if (!FrameInfo->IsFrameInfoValid) {
        DEBUG((DEBUG_ERROR, "%a Hotkey frame is not added!!!\n", __FUNCTION__));
        return;
    }
    
    if ((FrameInfo->EndRow - FrameInfo->StartRow) < 2) {
        DEBUG((DEBUG_ERROR, "%a Minimum 3 rows are needed to draw Hotkey frame!!!\n", __FUNCTION__));
        return;
    }
    
    gHotkeyCount = (sizeof(gHotkeyList) / sizeof(UINTN)) - 1;
    if (gHotkeyCount == 0) {
        DEBUG((DEBUG_ERROR, "%a Hotkey elink is not ported!!!\n", __FUNCTION__));
    }
    
    HotkeyStrlen = GetTotalStringLength() + (gHotkeyCount * 3);
    if (HotkeyStrlen > FrameInfo->EndColumn) {
        DEBUG((DEBUG_ERROR, "%a Sum of hotkey strings length (%d) exceeds the number of columns (%d)!!!\n",
                __FUNCTION__, HotkeyStrlen, FrameInfo->EndColumn));
        return;
    }

    // Get the maximum length of hotkey string
    MaxLength = GetMaxLength();
    if (MaxLength == 0) {
        DEBUG((DEBUG_ERROR, "MaxLength is 0\n"));
        return;
    }
    
    if (gHotkeyCount > MAX_HOTKEY_COUNT) {
        DEBUG((DEBUG_ERROR, "Hotkey count is greater than MAX_HOTKEY_COUNT(%d)\n", MAX_HOTKEY_COUNT));
    }
    
    MaxColumn = (FrameInfo->EndColumn + 1) / MAX_HOTKEY_COUNT;
    DEBUG((DEBUG_ERROR, "%a MaxColumn per hotkey is %d\n", __FUNCTION__, MaxColumn));
    if (MaxLength > MaxColumn) {
        DEBUG((DEBUG_ERROR, "%a MaxLength (%d) is greater than MaxColumn (%d)\n", __FUNCTION__, MaxLength, MaxColumn));
        return;
    }

    gHotkeyLocation = AllocateZeroPool (sizeof(HOTKEY_LOCATION) * gHotkeyCount);
    if (gHotkeyLocation == NULL) {
        return;
    }
    
    // Calculates Row number to display the hotkey strings at the center of Hotkey frame
    FrameInfo->CurrentRow += ((FrameInfo->EndRow - FrameInfo->StartRow) / 2);

    // Sets attribute for hotkey string
    OutputStringInAllGops (TRUE, EFI_TEXT_ATTR(EFI_WHITE, EFI_BACKGROUND_BLACK), NULL, NULL);

    for (Index = 0; Index < gHotkeyCount; Index++, ColonPlace = 0) {

        // Separate hotkey and its string
        HotkeyStr = gHotkeyList[Index];
        for (Index1 = 0; HotkeyStr[Index1] != 0; Index1++) {
            UnicodeStr[Index1] = (CHAR16)HotkeyStr[Index1];
            if (HotkeyStr[Index1] == ':') {
                ColonPlace = Index1;
                break;
            }
        }
        UnicodeStr[Index1] = 0;

        // If colon not found in hotkey string, then skip it
        if (ColonPlace == 0) {
            continue;
        }
        
        Length = (UINT32)AsciiStrLen(HotkeyStr) + 2; // +2 for spaces
        
        // Places the hotkey string at the center
        StartColumn = (MaxColumn - Length) / 2;
        if ((MaxColumn * Index) < FrameInfo->EndColumn) {
            FrameInfo->CurrentColumn = (MaxColumn * Index) + StartColumn;
        } else {
            DEBUG((DEBUG_ERROR, "%a (MaxColumn * Index) < FrameInfo->EndColumn fail\n", __FUNCTION__));
            continue;
        }
        
        StartX = ((UINTN)FrameInfo->CurrentColumn * EFI_GLYPH_WIDTH) + FrameInfo->DeltaX;
        StartY = ((UINTN)FrameInfo->CurrentRow * EFI_GLYPH_HEIGHT) + FrameInfo->DeltaY;

        // First draw white color box to place hotkey
        DisplayBltInAllGops (
                    &WhiteColor,
                    EfiBltVideoFill,
                    0,
                    0,
                    StartX - (BorderWidth * 2),
                    StartY - (BorderWidth * 2),
                    (ColonPlace * EFI_GLYPH_WIDTH) + (BorderWidth * 4),
                    EFI_GLYPH_HEIGHT + (BorderWidth * 4),
                    0 );

        // Fill black color inside the white box to show the box with white color border
        DisplayBltInAllGops (
                    &BlackColor,
                    EfiBltVideoFill,
                    0,
                    0,
                    StartX - BorderWidth,
                    StartY - BorderWidth,
                    (ColonPlace * EFI_GLYPH_WIDTH) + (BorderWidth * 2),
                    EFI_GLYPH_HEIGHT + (BorderWidth * 2),
                    0 );

        // Store the hotkey box drawn co-ordinates to highlight it later once hotkey is consumed
        gHotkeyLocation[Index].StartX = StartX - (BorderWidth * 2);
        gHotkeyLocation[Index].StartY = StartY - (BorderWidth * 2);
        gHotkeyLocation[Index].Width  = (ColonPlace * EFI_GLYPH_WIDTH) + (BorderWidth * 4);
        gHotkeyLocation[Index].Height = EFI_GLYPH_HEIGHT + (BorderWidth * 4);

        // Displays hotkey inside white box
        OutputStringInAllGops (FALSE, 0, FrameInfo, UnicodeStr);
        
        // Then display hotkey's string after two spaces
        FrameInfo->CurrentColumn += 2;
        AsciiStrToUnicodeStrS (&HotkeyStr[ColonPlace+1], UnicodeStr, sizeof(UnicodeStr)/sizeof(CHAR16));
        OutputStringInAllGops (FALSE, 0, FrameInfo, UnicodeStr);
    }
}

/**
    Callback function for gAmiBdsConnectDriversProtocolGuid.
    
    @param Event
    @param Context
    
    @return VOID
**/
VOID
EFIAPI
BdsConnectDriversCallBack (
    IN EFI_EVENT  Event,
    IN VOID       *Context 
)
{
    // During PCI enumeration, resources will be re-allocated. So stopping the 
    // display update by setting gPciEnumerationStarted to TRUE
    gPciEnumerationStarted = TRUE;

    // In Graphics mode enable gConnectDriversCalled based on AmiPcdGraphicsConsoleSupportInBds
    if (gGraphicsModeEnabled) {
        if (FixedPcdGetBool(AmiPcdGraphicsConsoleSupportInBds)) {
            gBS->CloseEvent (Event);
            return;
        }
    }
    
    // If this flag is set, then Graphics/Text console support is disabled in BDS
    gConnectDriversCalled = TRUE;
    gBS->CloseEvent (Event);
}

VOID
EFIAPI
PciEnumerationCallBack (
    IN EFI_EVENT  Event,
    IN VOID       *Context 
)
{
    gPciEnumerationStarted = FALSE;
}
/**
    Callback function for gAmiSimpleTextOutProtocolGuid. Updates the
    GOP and TextOut pointers to DisplayFrameInfo
    
    @param Event
    @param TextOutContext
    
    @return VOID
**/
VOID
EFIAPI
AmiGopProtocolCallBack (
    IN EFI_EVENT  Event,
    IN VOID       *TextOutContext 
)
{
    EFI_STATUS           Status;
    DISPLAY_FRAME_INFO   *DisplayFrameInfo;
    
    DisplayFrameInfo = GetDisplayFrameInfo (PeiSimpleTextOutConsoleTypeVideoGop);
    if (DisplayFrameInfo == NULL) {
        return;
    }
    
    Status = UpdateGopProtocolPointers (DisplayFrameInfo);
    if (!EFI_ERROR(Status)) {
        gBS->CloseEvent (Event);
    }
    return;
}

/**
    Checks for video controller which initialized in PEI phase 
    
    @param Handle
    
    @return BOOLEAN
**/
BOOLEAN
CheckVideoController (
    EFI_HANDLE   Handle
)
{
    EFI_STATUS                        Status;
    VOID                              *HobStart;
    EFI_PEI_GRAPHICS_DEVICE_INFO_HOB  *DeviceInfo;
    EFI_DEVICE_PATH_PROTOCOL          *DevicePath;
    EFI_HANDLE                        ControllerHandle;
    EFI_PCI_IO_PROTOCOL               *PciIo;
    PCI_TYPE00                        PciConfig;
    
    HobStart = GetFirstGuidHob (&gEfiGraphicsDeviceInfoHobGuid);
    if ((HobStart == NULL)  || (GET_GUID_HOB_DATA_SIZE (HobStart) == sizeof (EFI_PEI_GRAPHICS_DEVICE_INFO_HOB))) {
        DEBUG((DEBUG_ERROR, "%a : Graphics device info HOB not found!!!\n", __FUNCTION__));
        return FALSE;
    }
    
    DeviceInfo = (EFI_PEI_GRAPHICS_DEVICE_INFO_HOB*)(GET_GUID_HOB_DATA (HobStart));
    
    Status = gBS->HandleProtocol ( 
                            Handle, 
                            &gEfiDevicePathProtocolGuid, 
                            (VOID**)&DevicePath );
    if (EFI_ERROR(Status)) {
        return FALSE;
    }
    
    // Get parent handle
    Status = gBS->LocateDevicePath (
                            &gEfiPciIoProtocolGuid,
                            &DevicePath,
                            &ControllerHandle);
    if (EFI_ERROR(Status)) {
        return FALSE;
    }
    
    Status = gBS->HandleProtocol ( 
                            ControllerHandle, 
                            &gEfiPciIoProtocolGuid, 
                            (VOID**)&PciIo );
    if (EFI_ERROR(Status)) {
        return FALSE;
    }
    
    Status = PciIo->Pci.Read (
                           PciIo, 
                           EfiPciIoWidthUint8, 
                           0, 
                           sizeof (PciConfig), 
                           &PciConfig );
    if (EFI_ERROR(Status)) {
        return FALSE;
    }
    
    // Match the details with HOB data
    if (((DeviceInfo->VendorId != MAX_UINT16) && (DeviceInfo->VendorId != PciConfig.Hdr.VendorId)) ||
        ((DeviceInfo->DeviceId != MAX_UINT16) && (DeviceInfo->DeviceId != PciConfig.Hdr.DeviceId)) ||
        ((DeviceInfo->RevisionId != MAX_UINT8) && (DeviceInfo->RevisionId != PciConfig.Hdr.RevisionID)) ||
        ((DeviceInfo->SubsystemVendorId != MAX_UINT16) && (DeviceInfo->SubsystemVendorId != PciConfig.Device.SubsystemVendorID)) ||
        ((DeviceInfo->SubsystemId != MAX_UINT16) && (DeviceInfo->SubsystemId != PciConfig.Device.SubsystemID))) {
        return FALSE;
    }
    
    DEBUG((DEBUG_INFO, "%a : Matching Graphics device found!!!\n", __FUNCTION__));
    return TRUE;
}

/**
    Callback function for EfiSimpleTextOutProtocolGuid
    
    @param Event
    @param TextOutContext
    
    @return VOID
**/
VOID
EFIAPI
EfiSimpleTextOutCallBack (
    IN EFI_EVENT  Event,
    IN VOID       *TextOutContext 
)
{
    EFI_STATUS                       Status;
    EFI_HANDLE                       Handle;
    UINTN                            BufferSize = sizeof(Handle);
    EFI_GRAPHICS_OUTPUT_PROTOCOL     *Gop;
    EFI_SIMPLE_TEXT_OUTPUT_PROTOCOL  *SimpleTextOut;
    DISPLAY_FRAME_INFO               *DisplayFrameInfo;
    
    // If BDS support is disabled, then return from event
    if (gConnectDriversCalled) {
        gBS->CloseEvent (Event);
        return;
    }
    
    DisplayFrameInfo = GetDisplayFrameInfo (PeiSimpleTextOutConsoleTypeVideoGop);
    if (DisplayFrameInfo == NULL) {
        gBS->CloseEvent (Event);
        return;
    }
    
    Status = gBS->LocateHandle ( 
                        ByRegisterNotify, 
                        NULL, 
                        gRegistration, 
                        &BufferSize, 
                        &Handle );
    if (EFI_ERROR(Status)) {
        return;
    }
    
    Status = gBS->HandleProtocol ( 
                            Handle, 
                            &gEfiGraphicsOutputProtocolGuid, 
                            (VOID**)&Gop );
    if (EFI_ERROR(Status)) {
        return;
    }
    
    Status = gBS->HandleProtocol ( 
                            Handle, 
                            &gEfiSimpleTextOutProtocolGuid, 
                            (VOID**)&SimpleTextOut );
    if (EFI_ERROR(Status)) {
        return;
    }
    
    if (CheckVideoController(Handle)) {
        DisplayFrameInfo->GraphicsOutput.GraphicsOutputProtocol = Gop;
        DisplayFrameInfo->SimpleTextOut.SimpleTextOutProtocol   = SimpleTextOut;
        gEfiGopAvailable = TRUE;
        gBS->CloseEvent (Event);
    }
    return;
}

/**
  @internal
   This function scrolls up the text in the given frame
   
    @param   SimpleTextOut - Pointer to SimpleTextOut protocol instance
    @param   GraphicsOutput - Pointer to GraphicsOutput protocol instance
    @param   FrameInfo - Pointer to Frame Information
    @param   StartRow - Start position of the row to scroll up 
    @param   NoOfRows - Number of rows to be scrolled up
    @param   LastRow - Last row of the frame
    @param   MaxColumns - Max number of columns in the frame.
   
    @return None
   
  @endinternal
**/
VOID 
FrameScrollUp (
    IN EFI_SIMPLE_TEXT_OUTPUT_PROTOCOL         *SimpleTextOut,
    IN EFI_GRAPHICS_OUTPUT_PROTOCOL            *GraphicsOutput,
    IN AMI_EARLY_GRAPHICS_FRAME_INFO           *FrameInfo,
    IN UINT32                                  StartRow, 
    IN UINT32                                  NoOfRows,     
    IN UINT32                                  LastRow,
    IN UINT32                                  MaxColumns
)
{
    EFI_GRAPHICS_OUTPUT_BLT_PIXEL   Fill = EARLY_GRAPHICS_BACKGROUND_COLOR;
    
    // Scroll function not supported in Serial display

    if (gGraphicsModeEnabled) {
        // Read 2nd row to last row in frame and
        // update it in 1st row to last row - 1
        GraphicsOutput->Blt(
                GraphicsOutput,
                &Fill,
                EfiBltVideoToVideo,
                FrameInfo->DeltaX + FrameInfo->StartX,
                FrameInfo->DeltaY + (StartRow + 1) * EFI_GLYPH_HEIGHT,   // SourceY                 
                FrameInfo->DeltaX + FrameInfo->StartX,
                FrameInfo->DeltaY + StartRow * EFI_GLYPH_HEIGHT,         // Dest Y
                MaxColumns * EFI_GLYPH_WIDTH,
                NoOfRows * EFI_GLYPH_HEIGHT,
                0);
    
        // Override last row with background color
        GraphicsOutput->Blt(
                GraphicsOutput,
                &Fill,
                EfiBltVideoFill,
                0,
                0,                   
                FrameInfo->DeltaX + FrameInfo->StartX,
                FrameInfo->DeltaY + (LastRow * EFI_GLYPH_HEIGHT),
                MaxColumns * EFI_GLYPH_WIDTH,
                EFI_GLYPH_HEIGHT,
                0);
    } 
#if (VideoTextConsole_SUPPORT == 1)
    else { 
        AmiVideoScrollUp (StartRow, LastRow + 1, MaxColumns);
    }
#endif
}
  
/**
  @internal
    This function process next line characters.
    
    @param SimpleTextOutProtocol  Pointer to Simple Text out Protocol
    @param GraphicsOutPutProtocol Pointer to Graphics output Protocol
    @param FrameInfo              Pointer to Frame Info Data
    
    @return None
    
  @endinternal
**/
VOID
ProcessLineFeed (
    IN EFI_SIMPLE_TEXT_OUTPUT_PROTOCOL        *SimpleTextOut,
    IN EFI_GRAPHICS_OUTPUT_PROTOCOL           *GraphicsOutput,
    IN AMI_EARLY_GRAPHICS_FRAME_INFO          *FrameInfo
)
{
    UINTN     CurrentRow;
    UINTN     CurrentColumn;
    
    // If current row is last row, scroll up
    if (FrameInfo->CurrentRow == FrameInfo->EndRow) {
        FrameScrollUp (
                SimpleTextOut,
                GraphicsOutput,
                FrameInfo,
                FrameInfo->StartRow,                        // StartRow
                FrameInfo->EndRow - FrameInfo->StartRow,    // NoOfRows
                FrameInfo->EndRow,                          // LastRow
                FrameInfo->EndColumn - FrameInfo->StartColumn + 1);
        FrameInfo->CurrentRow--;
    }
    
    CurrentRow    = ++FrameInfo->CurrentRow;
    CurrentColumn = FrameInfo->CurrentColumn;

    SimpleTextOut->SetCursorPosition (
                                SimpleTextOut, 
                                CurrentColumn, 
                                CurrentRow);
}

/**
  @internal
    This function sets cursor to first column of current row
    
    @param SimpleTextOut     Pointer to Simple Text out Protocol
    @param FrameInfo         Pointer to Frame Info Data
    
    @return None
    
  @endinternal
**/
VOID
ProcessCarriageReturn (
  IN EFI_SIMPLE_TEXT_OUTPUT_PROTOCOL        *SimpleTextOut,
  IN AMI_EARLY_GRAPHICS_FRAME_INFO          *FrameInfo
)
{
    FrameInfo->CurrentColumn = FrameInfo->StartColumn;

    // Set cursor to first column of current row
    SimpleTextOut->SetCursorPosition (
                            SimpleTextOut, 
                            FrameInfo->CurrentColumn, 
                            FrameInfo->CurrentRow);
}

/**
  @internal
    This function displays string in the given frame
    
    @param SimpleTextOutProtocol  Pointer to Simple Text out Protocol
    @param GraphicsOutPutProtocol Pointer to Graphics output Protocol
    @param FrameInfo              Pointer to Frame Info Data
    @param String                 String to Print in console
    
    @return None
    
  @endinternal
**/
VOID 
DisplayStringInFrame (
  IN EFI_SIMPLE_TEXT_OUTPUT_PROTOCOL        *SimpleTextOut,
  IN EFI_GRAPHICS_OUTPUT_PROTOCOL           *GraphicsOutput,
  IN AMI_EARLY_GRAPHICS_FRAME_INFO          *FrameInfo,
  IN CHAR16                                 *String
)
{
    UINTN           StringLength = StrLen(String);
    UINTN           CharCount;
    CHAR16          Buffer[200];

    // If dynamic background for string is enabled, then
    // set AmiPcdCallFromEarlyConsoleOut to inform SimpleTextOut instance to update the string background
    // with actual background data
    if (FixedPcdGetBool(AmiPcdDynamicBackgroundStringsSupport)) {
        PcdSetBoolS (AmiPcdCallFromEarlyConsoleOut, TRUE);
    }

    while (TRUE) {
        CharCount = FrameInfo->EndColumn - FrameInfo->CurrentColumn;
        // If current row won't accommodate input string then truncate it
        // to available characters in current row and print remaining
        // string in next row
        if (StringLength > CharCount) {
            ZeroMem (Buffer, sizeof(Buffer));
            CopyMem (Buffer, String, CharCount * sizeof(UINT16));
            
            SimpleTextOut->OutputString (
                                    SimpleTextOut, 
                                    Buffer);
            FrameInfo->CurrentColumn = SimpleTextOut->Mode->CursorColumn;
            FrameInfo->CurrentRow    = SimpleTextOut->Mode->CursorRow;
            
            ProcessLineFeed (
                        SimpleTextOut,
                        GraphicsOutput,
                        FrameInfo);
            
            ProcessCarriageReturn (
                        SimpleTextOut,
                        FrameInfo);

            StringLength -= CharCount;
            String += CharCount;
        } else {
            SimpleTextOut->OutputString (
                                    SimpleTextOut, 
                                    String);
            FrameInfo->CurrentColumn = SimpleTextOut->Mode->CursorColumn;
            FrameInfo->CurrentRow    = SimpleTextOut->Mode->CursorRow;
            break;
        }
    }

    // Once string displayed, clear AmiPcdCallFromEarlyConsoleOut
    if (FixedPcdGetBool(AmiPcdDynamicBackgroundStringsSupport)) {
        PcdSetBoolS (AmiPcdCallFromEarlyConsoleOut, FALSE);
    }

    return;
}

/**
  @internal 
    This function process the strings(text, carriage return, line feed) characters
    and displays in screen
    
    @param SimpleTextOutProtocol  Pointer to Simple Text out Protocol
    @param GraphicsOutPutProtocol Pointer to Graphics output Protocol
    @param FrameInfo              Pointer to Frame Info Data
    @param String                 String to Print in console
    
    @return None
    
  @endinternal
**/
VOID
ProcessString (
  IN EFI_SIMPLE_TEXT_OUTPUT_PROTOCOL        *SimpleTextOut,
  IN EFI_GRAPHICS_OUTPUT_PROTOCOL           *GraphicsOutput,
  IN AMI_EARLY_GRAPHICS_FRAME_INFO          *FrameInfo,
  IN CHAR16                                 *String
)
{
    CHAR16              *Temp;
    CHAR16              *RemainStr = NULL;
    BOOLEAN             LineFeed = FALSE;
    BOOLEAN             CarriageReturn = FALSE;
    UINT16              Index = 0;
    CHAR16              Buffer[200];
    
    Temp = String;
    
    ZeroMem (Buffer, sizeof(Buffer));

    // Process to know whether string has line feed or carriage return characters
    // if yes, first print the characters till that character and process \n \r characters
    while (Temp[Index] != 0) {
        switch (Temp[Index]) {
            case CHAR_CARRIAGE_RETURN:
                CarriageReturn = TRUE;
                break;

            case CHAR_LINEFEED:
                LineFeed = TRUE;
                break;
        }

        if (CarriageReturn || LineFeed) {
            RemainStr = &Temp[Index+1];
            break;
        }

        Index++;
    }

    if (Index > (sizeof(Buffer)/sizeof(UINT16))) {
        return;
    }

    if (Index != 0) {
        CopyMem (Buffer, String, Index * sizeof(UINT16));
        DisplayStringInFrame (
                        SimpleTextOut,
                        GraphicsOutput,
                        FrameInfo, 
                        Buffer);
    }
    
    // Carriage Return \r found. Set cursor at column 0 of current row
    if (CarriageReturn) {
        ProcessCarriageReturn (
                        SimpleTextOut,
                        FrameInfo);
    }

    // Line Feed \n found. Set cursor at current column of next row
    if (LineFeed) {
        ProcessLineFeed (
                    SimpleTextOut,
                    GraphicsOutput,
                    FrameInfo);
    }
 
    // When string reaches end, break the recursive call
    if ((RemainStr == NULL) || (*RemainStr == 0)) {
        return;
    }

    // Recursive call to process remaining string
    ProcessString (
             SimpleTextOut,
             GraphicsOutput,
             FrameInfo, 
             RemainStr);
    return;
}

/**
    This function is not supported as frames cannot be created in DXE phase
 
    @param DisplayFrameInfo   Pointer to Display Frame Info
    
    @return EFI_STATUS
**/
EFI_STATUS
CreateFrames (
    IN DISPLAY_FRAME_INFO     *DisplayFrameInfo
)
{
    return EFI_UNSUPPORTED;
}

/**
    In BDS phase, display primary GOP data in all GOP instances
 
    @param HandleBuffer - Pointer to GOP handle
    @param HandleCount - No.Of GOP handles
    
    @return None
**/
VOID
DisplayPrimaryGopDataInAllGops (
    EFI_HANDLE          *HandleBuffer,
    UINTN               HandleCount
)
{
    EFI_STATUS                       Status;
    EFI_GRAPHICS_OUTPUT_PROTOCOL     *GopConOut = NULL;
    EFI_GRAPHICS_OUTPUT_PROTOCOL     *Gop;
    EFI_GRAPHICS_OUTPUT_BLT_PIXEL    *ScreenBuffer;
    UINTN                            Index;
    UINTN                            Index1;
    EFI_HANDLE                       GopHandle = NULL;
    
    // If current call's GOP handles matches with previous call's GOP handles, then return
    if ((gHandleCount == HandleCount) &&
        (CompareMem (gHandleBuffer, HandleBuffer, sizeof(EFI_HANDLE) * HandleCount) == 0)) {
        return;
    }
    
    // Check if input HandleBuffer has video controller initialized in PEI phase
    // If yes, get its GOP instance. Else return error
    for (Index = 0; Index < HandleCount; Index++) {
        if (CheckVideoController (HandleBuffer[Index]) == FALSE) {
            continue;
        }
        
        Status = gBS->HandleProtocol (
                                HandleBuffer[Index], 
                                &gEfiGraphicsOutputProtocolGuid, 
                                (VOID**)&GopConOut );
        if (!EFI_ERROR(Status)) {
            GopHandle = HandleBuffer[Index];
            break;
        }
    }
    
    if (GopConOut == NULL) {
        return;
    }
    
    // Allocate memory for BltBuffer
    ScreenBuffer = AllocatePool (
                        GopConOut->Mode->Info->HorizontalResolution *
                        GopConOut->Mode->Info->VerticalResolution *
                        sizeof (EFI_GRAPHICS_OUTPUT_BLT_PIXEL));
    if (ScreenBuffer == NULL) {
        return;
    }
    
    // Store current video data to BltBuffer
    Status = GopConOut->Blt (
                    GopConOut,
                    ScreenBuffer,
                    EfiBltVideoToBltBuffer,
                    0,
                    0,
                    0,
                    0,
                    GopConOut->Mode->Info->HorizontalResolution,
                    GopConOut->Mode->Info->VerticalResolution,
                    0);
    if (EFI_ERROR(Status)) {
        return;
    }
    
    for (Index = 0; Index < HandleCount; Index++) {
        for (Index1 = 0; Index1 < gHandleCount; Index1++) {
            // Skip previous call's GOP handles
            if (HandleBuffer[Index] == gHandleBuffer[Index1]) {
                break;
            }
        }
        
        if (Index1 != gHandleCount) {
            continue;
        }
        
        if (GopHandle == HandleBuffer[Index]) {
            continue;
        }
        
        // If new GOP handle found, then 
        Status = gBS->HandleProtocol ( 
                                HandleBuffer[Index], 
                                &gEfiGraphicsOutputProtocolGuid, 
                                (VOID**)&Gop );
        if (EFI_ERROR(Status)) {
            continue;
        }
        
        Gop->Blt (
                Gop,
                ScreenBuffer,
                EfiBltBufferToVideo,
                0,
                0,
                0,
                0,
                GopConOut->Mode->Info->HorizontalResolution,
                GopConOut->Mode->Info->VerticalResolution,
                0);
    }
    
    // As there is a change in GOP instances, update global buffer with latest handles
    if (gHandleBuffer == NULL) {
        gHandleBuffer = AllocateCopyPool ((sizeof(EFI_HANDLE) * HandleCount), HandleBuffer);
    } else {
        gHandleBuffer = ReallocatePool (
                            sizeof(EFI_HANDLE) * gHandleCount, // OldSize
                            sizeof(EFI_HANDLE) * HandleCount,  // NewSize
                            gHandleBuffer);
    }
    
    if (gHandleBuffer == NULL) {
        gHandleCount = 0;
    } else {
        gHandleCount = HandleCount;
    }
    
    FreePool (ScreenBuffer);
}

/**
    In BDS phase, sets attribute and displays string in all available EFI GOP instances
 
    @param IsAttribute  - If this flag is TRUE then consider Attribute
                        - Otherwise FrameInfo and String are valid
    @param Attribute    - Attribute value to be set on the screen.
    @param FrameInfo    - Pointer to Frame information data
    @param String       - String to be displayed on screen
    
    @return None
**/
VOID
OutputStringInAllGops (
    BOOLEAN                          IsAttribute,
    UINTN                            Attribute,
    AMI_EARLY_GRAPHICS_FRAME_INFO    *FrameInfo,
    CHAR16                           *String
)
{
    EFI_STATUS                       Status;
    UINTN                            HandleCount;
    EFI_HANDLE                       *HandleBuffer;
    UINTN                            Index;
    EFI_GRAPHICS_OUTPUT_PROTOCOL     *Gop;
    EFI_SIMPLE_TEXT_OUTPUT_PROTOCOL  *SimpleTextOut;
    
    Status = gBS->LocateHandleBuffer (
                              ByProtocol,
                              &gEfiGraphicsOutputProtocolGuid,
                              NULL,
                              &HandleCount,
                              &HandleBuffer );
    if (EFI_ERROR(Status)) {
        return;
    }
    
    DisplayPrimaryGopDataInAllGops (HandleBuffer, HandleCount);
    
    for (Index = 0; Index < HandleCount; Index++) {
        Status = gBS->HandleProtocol ( 
                                HandleBuffer[Index], 
                                &gEfiGraphicsOutputProtocolGuid, 
                                (VOID**)&Gop );
        if (EFI_ERROR(Status)) {
            continue;
        }
        
        Status = gBS->HandleProtocol ( 
                                HandleBuffer[Index], 
                                &gEfiSimpleTextOutProtocolGuid, 
                                (VOID**)&SimpleTextOut );
        if (EFI_ERROR(Status)) {
            continue;
        }
        
        if (IsAttribute) {
            SimpleTextOut->SetAttribute (
                                SimpleTextOut, 
                                Attribute );
        } else {
            // Set cursor to end of previous string in frame
            SimpleTextOut->SetCursorPosition (
                                     SimpleTextOut, 
                                     FrameInfo->CurrentColumn, 
                                     FrameInfo->CurrentRow );
            ProcessString (
                     SimpleTextOut,
                     Gop,
                     FrameInfo, 
                     String );
        }
    }
    
    FreePool (HandleBuffer);
}

/**
    Displays BLT buffer in all EFI GOP instances
    
    @param BltBuffer    - Pointer to data buffer
    @param BltOperation - Type of operation to be performed on the buffer 
    @param SourceX      - X-Position of the source/screen data
    @param SourceY      - Y-Position of the source/screen data
    @param DestinationX - X-Position of the source/screen data
    @param DestinationY - Y-Position of the source/screen data
    @param Width        - Width of the data.
    @param Height       - Height of the data
    @param Delta        - 
    
    @return VOID
**/
VOID
DisplayBltInAllGops (
    EFI_GRAPHICS_OUTPUT_BLT_PIXEL           *BltBuffer,
    EFI_GRAPHICS_OUTPUT_BLT_OPERATION       BltOperation,
    UINTN                                   SourceX,
    UINTN                                   SourceY,
    UINTN                                   DestinationX,
    UINTN                                   DestinationY,
    UINTN                                   Width,
    UINTN                                   Height,
    UINTN                                   Delta
)
{
    EFI_STATUS                              Status;
    EFI_GRAPHICS_OUTPUT_PROTOCOL            *Gop;
    UINTN                                   NoProtocols;
    VOID                                    **Buffer;
    UINTN                                   Index;
    
    Status = EfiLocateProtocolBuffer (
                        &gEfiGraphicsOutputProtocolGuid,
                        &NoProtocols,
                        &Buffer );
    if (EFI_ERROR (Status) || (Buffer == NULL)) {
        return;
    }
    
    for (Index = 0; Index < NoProtocols; Index++) {
        Gop = (EFI_GRAPHICS_OUTPUT_PROTOCOL*)Buffer[Index];
        Gop->Blt (
                Gop,
                BltBuffer,
                BltOperation,
                SourceX,
                SourceY,
                DestinationX,
                DestinationY,
                Width,
                Height,
                0 );
    }
    
    FreePool (Buffer);
}

/**
    Returns the screen frame information from HOB data based on the 
    console type

    @param   ConsoleType - Serial/Text/GOP

    @retval  DISPLAY_FRAME_INFO
**/
DISPLAY_FRAME_INFO*
GetDisplayFrameInfo (
    AMI_PEI_SIMPLE_TEXT_CONSOLE_TYPE   ConsoleType
)
{
    UINT8                                   Index;
    EARLY_CONSOLE_DISPLAY_FRAME_INFO_HOB    *FrameInfoHobDataPtr = gFrameInfoHobData;
    
    if (FrameInfoHobDataPtr == NULL) {
        return NULL;
    }
    
    for (Index = 0; Index < FixedPcdGet8 (AmiPcdSimpleTextOutMaxPpiSupported); Index++) {
        if (!FrameInfoHobDataPtr->DisplayFrameInfo[Index].IsDisplayFrameInfoValid) {
            continue;
        }
        
        if (FrameInfoHobDataPtr->DisplayFrameInfo[Index].SimpleTextOutConsoleType == ConsoleType) {
            return &FrameInfoHobDataPtr->DisplayFrameInfo[Index];
        }
    }
    
    return NULL;
}

/**
    Clears the Pei pointers present in HOB

    @param   VOID

    @retval  VOID
**/
VOID
ClearPeiPointersInHobData ()
{
    UINT8                                   Index;
    EARLY_CONSOLE_DISPLAY_FRAME_INFO_HOB    *FrameInfoHobDataPtr = gFrameInfoHobData;
    
    if (FrameInfoHobDataPtr == NULL) {
        return;
    }
    
    FrameInfoHobDataPtr->ConsSimpleTextOut.SimpleTextOutProtocol = NULL;
    
    for (Index = 0; Index < FixedPcdGet8 (AmiPcdSimpleTextOutMaxPpiSupported); Index++) {
        if (!FrameInfoHobDataPtr->DisplayFrameInfo[Index].IsDisplayFrameInfoValid) {
            continue;
        }

        FrameInfoHobDataPtr->DisplayFrameInfo[Index].GraphicsOutput.GraphicsOutputProtocol = NULL;
        FrameInfoHobDataPtr->DisplayFrameInfo[Index].SimpleTextOut.SimpleTextOutProtocol   = NULL;
    }
}

/**
    Updates the SimpletextOut pointers to DisplayFrameInfo structure.

    @param   Event
    @param   Context

    @retval  VOID
**/
VOID
UpdateTextOutProtocolPointers (
    IN EFI_EVENT  Event,
    IN VOID       *Context 
)   
{
    EFI_STATUS                              Status;
    UINTN                                   HandleCount;
    EFI_HANDLE                              *HandleBuffer;
    UINT8                                   Index;
    EFI_DEV_PATH_PTR                        DevicePath;
    VOID                                    *Interface;
    DISPLAY_FRAME_INFO                      *DisplayFrameInfo;

    Status = gBS->LocateHandleBuffer (
                              ByProtocol,
                              &gAmiSimpleTextOutProtocolGuid,
                              NULL,
                              &HandleCount,
                              &HandleBuffer );
    if (EFI_ERROR(Status)) {
        return;
    }
    
    for (Index = 0; Index < HandleCount; Index++) {
        Status = gBS->HandleProtocol (
                            HandleBuffer[Index],
                            &gAmiSimpleTextOutProtocolGuid, 
                            &Interface );
        if (EFI_ERROR(Status)) {
            continue;
        }
        
        Status = gBS->HandleProtocol (
                            HandleBuffer[Index],
                            &gEfiDevicePathProtocolGuid, 
                            (VOID**)&DevicePath.DevPath );
        if (EFI_ERROR(Status)) {
            continue;
        }
        
        DisplayFrameInfo = GetDisplayFrameInfo (PeiSimpleTextOutConsoleTypeSerial);
        if ((DisplayFrameInfo != NULL) && 
            (DisplayFrameInfo->SimpleTextOut.SimpleTextOutProtocol == NULL)) {
            if ((DevicePath.Acpi->Header.Type == ACPI_DEVICE_PATH) &&
                (DevicePath.Acpi->Header.SubType == ACPI_DP) &&
                (DevicePath.Acpi->HID == EISA_PNP_ID(0x501)) &&
                (DevicePath.Acpi->UID == 0xFF)) {
                DEBUG((DEBUG_INFO, "Serial TextOut pointer updated\n"));
                DisplayFrameInfo->SimpleTextOut.SimpleTextOutProtocol = Interface;
                continue;
            }
        }
        
        DisplayFrameInfo = GetDisplayFrameInfo (PeiSimpleTextOutConsoleTypeVideoText);
        if ((DisplayFrameInfo != NULL) && 
            (DisplayFrameInfo->SimpleTextOut.SimpleTextOutProtocol == NULL)) {
            if ((DevicePath.DevPath->Type == ACPI_DEVICE_PATH) &&
                (DevicePath.DevPath->SubType == ACPI_ADR_DP)) {
                DEBUG((DEBUG_INFO, "Video TextOut pointer updated\n"));
                DisplayFrameInfo->SimpleTextOut.SimpleTextOutProtocol = Interface;
                continue;
            }
        }
    }
    
    FreePool (HandleBuffer);
    return;
}

/**
    Updates the SimpletextOut & GOP pointers to DisplayFrameInfo structure.

    @param   DisplayFrameInfo - Pointer to frame information

    @retval  EFI_STATUS
**/
EFI_STATUS
UpdateGopProtocolPointers (
    DISPLAY_FRAME_INFO     *DisplayFrameInfo
)
{
    EFI_STATUS                        Status;
    UINTN                             HandleCount;
    EFI_HANDLE                        *HandleBuffer;
    UINTN                             Index;
    EFI_SIMPLE_TEXT_OUTPUT_PROTOCOL   *SimpleTextOut;
    EFI_GRAPHICS_OUTPUT_PROTOCOL      *Gop;
    
    Status = gBS->LocateHandleBuffer (
                              ByProtocol,
                              &gAmiSimpleTextOutProtocolGuid,
                              NULL,
                              &HandleCount,
                              &HandleBuffer );
    if (EFI_ERROR(Status)) {
        return EFI_UNSUPPORTED;
    }
    
    for (Index = 0; Index < HandleCount; Index++) {
        Status = gBS->HandleProtocol (
                            HandleBuffer[Index],
                            &gAmiSimpleTextOutProtocolGuid, 
                            (VOID**)&SimpleTextOut );
        if (EFI_ERROR(Status)) {
            continue;
        }

        Status = gBS->HandleProtocol (
                            HandleBuffer[Index],
                            &gAmiGraphicsOutputProtocolGuid, 
                            (VOID**)&Gop );
        if (EFI_ERROR(Status)) {
            continue;
        }
                
        DisplayFrameInfo->SimpleTextOut.SimpleTextOutProtocol = SimpleTextOut;
        DisplayFrameInfo->GraphicsOutput.GraphicsOutputProtocol = Gop;
        
        EfiNamedEventListen (
                      &gEfiSimpleTextOutProtocolGuid,
                      TPL_CALLBACK,
                      EfiSimpleTextOutCallBack,
                      NULL,
                      &gRegistration);
        break;
    }

    FreePool (HandleBuffer);
    return Status;
}

/**
    Updates the SimpletextOut & GOP pointers to DisplayFrameInfo structure.

    @param   ImageHandle - Handle for the image of this driver
    @param   SystemTable - Pointer to the EFI System Table

    @retval  EFI_STATUS
**/
EFI_STATUS
EarlyConsoleDisplayDxeInit ()
{
    EFI_STATUS                              Status;
    EFI_HOB_GUID_TYPE                       *GuidHob;
    DISPLAY_FRAME_INFO                      *DisplayFrameInfo;
    VOID                                    *Registration = NULL;

    DEBUG((DEBUG_INFO, "EarlyConsoleDisplayDxeInit\n"));
    
    GuidHob = GetFirstGuidHob (&gAmiEarlyConsoleDisplayFrameInfoHobGuid);
    if (GuidHob == NULL) {
        return EFI_SUCCESS;
    }
    
    gFrameInfoHobData = AllocateCopyPool (GET_GUID_HOB_DATA_SIZE (GuidHob), GET_GUID_HOB_DATA (GuidHob));
    if (gFrameInfoHobData == NULL) {
        return EFI_SUCCESS;
    }
    
    ClearPeiPointersInHobData ();

    // Check whether Graphics mode is enabled
    DisplayFrameInfo = GetDisplayFrameInfo (PeiSimpleTextOutConsoleTypeVideoGop);
    if (DisplayFrameInfo != NULL) {
        gGraphicsModeEnabled = TRUE;
        
        // If AMI Gop protocol not available, create event
        Status = UpdateGopProtocolPointers (DisplayFrameInfo);
        if (EFI_ERROR(Status)) {
            EfiNamedEventListen (
                          &gAmiSimpleTextOutProtocolGuid,
                          TPL_CALLBACK,
                          AmiGopProtocolCallBack,
                          NULL,
                          NULL);
        }
    }

    EfiNamedEventListen (
                  &gAmiBdsConnectDriversProtocolGuid,
                  TPL_CALLBACK,
                  BdsConnectDriversCallBack,
                  NULL,
                  NULL);
    
    EfiNamedEventListen (
                  &gEfiPciEnumerationCompleteProtocolGuid,
                  TPL_CALLBACK,
                  PciEnumerationCallBack,
                  NULL,
                  NULL);

    // Event to display the hotkey frame
    EfiNamedEventListen (
                  &gAmiConInStartedProtocolGuid,
                  TPL_CALLBACK,
                  ConInStartedProtocolCallBack,
                  NULL,
                  NULL);
    
    EfiCreateProtocolNotifyEvent (
                  &gAmiSimpleTextOutProtocolGuid,
                  TPL_CALLBACK,
                  UpdateTextOutProtocolPointers,
                  NULL,
                  &Registration );
    return EFI_SUCCESS;
}

