
//-------- DO NOT EDIT THIS FILE --------
//
// FILE WAS GENERATED AUTOMATICALY USING AMISDL v7.04.0351 (?? 25 2025,01:02:13)
//
//-------- DO NOT EDIT THIS FILE --------
//***********************************************************************
//*                                                                     *
//*                 Copyright (c) 1985 - 2025, AMI.                     *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************
#if !defined(_TOKEN_SDL_H)
#define _TOKEN_SDL_H

#define AMI_COMPATIBILITY_PKG_VERSION	0x37
#define FV_MAIN_BLOCKS	0x49f
#define NVRAM_BLOCKS	0x20
#define FV_BB_BLOCKS	0x340
#define CORE_BUILD_NUMBER	0x23
#define CORE_COMBINED_VERSION	0x50023
#define CORE_MAJOR_VERSION	0x5
#define AMI_PKG_VERSION	0x2f
#define CORE_VENDOR	American Megatrends
#define EXTERNAL_DMA_CAPABLE_DEVICES_SUPPORT	0
#define AMI_MODULE_PKG_VERSION	0x4a
#define MDE_PKG_VERSION	0x28
#define INTEL_FRAMEWORK_MODULE_PKG_VERSION	0xe
#define AmiStatusCode_SUPPORT	1
#define STRING_STATUS_SUPPORT	1
#define SERIAL_CHECKPOINT_ENABLE	0
#define RUNTIME_TRACE_SUPPORT	1
#define DATAHUB_STATUS_SUPPORT	1
#define CONOUT_CHECKPOINTS_SUPPORT	1
#define CON_OUT_CHECKPOINTS_IN_QUIET_MODE	1
#define CHECKPOINT_PROGRESS_CODES_MAP	ProgressCheckpointMap
#define CHECKPOINT_ERROR_CODES_MAP	ErrorCheckpointMap
#define BEEP_PROGRESS_CODES_MAP	ProgressBeepMap
#define BEEP_ERROR_CODES_MAP	ErrorBeepMap
#define DETAILED_ERROR_MESSAGES	1
#define STATUS_CODE_VERSION	0xdc
#define ROUTER_CALLBACK_BUFFER_SIZE	0x1000
#define PEI_DEBUG_SERVICE_PRINT_GATE	NullDebugServicePrintGate
#define DXE_RT_DEBUG_SERVICE_PRINT_GATE	NullDebugServicePrintGate
#define SMM_DEBUG_SERVICE_PRINT_GATE	NullDebugServicePrintGate
#define PEI_DEBUG_SERVICE_ASSERT_GATE	NullDebugServiceAssertGate
#define DXE_RT_DEBUG_SERVICE_ASSERT_GATE	NullDebugServiceAssertGate
#define SMM_DEBUG_SERVICE_ASSERT_GATE	NullDebugServiceAssertGate
#define BDS_VERSION	0x20
#define BBS_USB_DEVICE_TYPE_SUPPORT	0
#define BBS_NETWORK_DEVICE_TYPE_SUPPORT	0
#define GROUP_BOOT_OPTIONS_BY_TAG	1
#define FW_ORPHAN_BOOT_OPTIONS_POLICY	ORPHAN_BOOT_OPTIONS_POLICY_DELETE
#define ORPHAN_GROUP_HEADERS_POLICY	ORPHAN_BOOT_OPTIONS_POLICY_DELETE
#define NORMALIZE_BOOT_OPTION_NAME	1
#define NORMALIZE_BOOT_OPTION_DEVICE_PATH	1
#define BOOT_OPTION_NAME_PREFIX_FUNCTION	ConstructBootOptionNamePrefixDefault
#define MATCH_BOOT_OPTION_BY_LOCATION	1
#define MATCH_BOOT_OPTION_BY_DEVICE	1
#define BUILT_IN_SHELL_SUPPORT	1
#define BDS_UPDATE_MEMORY_USAGE_INFORMATION_FUNCTION	UpdateMemoryUsageInformationDefault
#define Platform_Driver_Override_SUPPORT	0
#define ADD_DEVICE_NODE_FOR_DEFAULT_BOOT_OPTION	0
#define REPORT_NO_CON_OUT_ERROR	1
#define REPORT_NO_CON_IN_ERROR	1
#define GraphicsSplitter_SUPPORT	0x0
#define GC_COLOR_BLACK	0,0,0
#define GC_COLOR_BLUE	0x98,0,0
#define GC_COLOR_GREEN	0,0x98,0
#define GC_COLOR_CYAN	0x98,0x98,0
#define GC_COLOR_RED	0,0,0x98
#define GC_COLOR_MAGENTA	0x98,0,0x98
#define GC_COLOR_BROWN	0x0,0x40,0x80
#define GC_COLOR_LIGHTGRAY	0x98,0x98,0x98
#define GC_COLOR_DARKGRAY	0x10,0x10,0x10
#define GC_COLOR_LIGHTBLUE	0xFF,0x10,0x10
#define GC_COLOR_LIGHTGREEN	0x10,0xFF,0x10
#define GC_COLOR_LIGHTCYAN	0xFF,0xFF,0xE0
#define GC_COLOR_LIGHTRED	0x10,0x10,0xFF 
#define GC_COLOR_LIGHTMAGENTA	0xF0,0x10,0xFF
#define GC_COLOR_YELLOW	0x10,0xFF,0xFF
#define GC_COLOR_WHITE	0xFF,0xFF,0xFF
#define GC_MODE1	{ 1, 80, 50, 1280, 1024 }
#define GC_MODE2	{ 2, 100, 31, 800, 600 }
#define GC_MODE3	{ 3, MAX_RES, MAX_RES, MAX_RES, MAX_RES }
#define GC_MODE_LIST	{ 0, 80, 25, 1024, 768 }, { 1, 80, 50, 1280, 1024 }, { 2, 100, 31, 800, 600 }, { 3, MAX_RES, MAX_RES, MAX_RES, MAX_RES }
#define GC_INITIAL_MODE	0x0
#define NVRAM_SUPPORT	1
#define NVRAM_VERSION	0x21
#define NVRAM_HEADER_SIZE	0x90
#define NVRAM_MONOTONIC_COUNTER_SUPPORT	1
#define NVRAM_RECORD_CHECKSUM_SUPPORT	0
#define DXE_NVRAM_COMPATIBILITY_CHECKER	DxeNvramDataCompatiblityChecker
#define MM_NVRAM_COMPATIBILITY_CHECKER	DxeNvramDataCompatiblityChecker
#define DISK_VERSION	0x18
#define AST2600_SUPPORT	0
#define FLASH_VERSION	0x2c
#define FLASH_ERASE_POLARITY	0x1
#define FLASH_RETRIES	0x3
#define FLASH_PART_STRING_LENGTH	0x30
#define WRITE_BLOCK_BUFFER_SIZE	0x100
#define SPIFlash_SUPPORT	1
#define SST_25VFxxx	1
#define SST_25LFxxx	1
#define STM_25PExx	1
#define ATMEL_26DFxxx	1
#define Numonyx_M25P128	0
#define NUMONYX_USE_BLOCK_ERASE	0
#define FAST_READ_SUPPORT	0x0
#define BLOCK_PROTECT_ENABLE	0
#define NATIVE_FIFO_SUPPORT	1
#define DETECT_SPI_ADDRESS_MODE	0x1
#define AMD_SYSTEM_BOARD	1
#define SW_SMI_PSTATE_CNT	0x0
#define SW_SMI_CST_CNT	0x0
#define APIC_BASE	0xfee00000
#define LOCAL_APIC_BASE	0xfee00000
#define USE_AP_HLT	1
#define AP_WAKE_INT	0x21
#define MP_JUMP_FUNCTION_ADDRESS	0x1000
#define MP_ZERO_DATA_ADDRESS	0x0
#define SMM_BASE_SAVE_STATE_OFFSET	0xff00
#define APIC_ID_REGISTER	0x20
#define APIC_VERSION_REGISTER	0x30
#define APIC_EOI_REGISTER	0xb0
#define APIC_SPURIOUS_VECTOR_REGISTER	0xf0
#define APIC_ICR_LOW_REGISTER	0x300
#define APIC_ICR_HIGH_REGISTER	0x310
#define APIC_LVT_LINT0_REGISTER	0x350
#define APIC_LVT_LINT1_REGISTER	0x360
#define CPU_MAJOR_VER	0x1
#define CPU_MINOR_VER	0x2
#define NUMBER_OF_CACHES	0x4
#define MAX_SMM_SAVE_STATE_SIZE	0x800
#define SMBIOS_PROCESSOR_FAMILY	0x87
#define SMBIOS_PROCESSOR_UPGRADE	0x11
#define CAR_TOTAL_SIZE	0x100000
#define CAR_BASE_ADDRESS	0x100000
#define AP_INIT_BY_CMOS	1
#define CPU_TIMER_LIB_SUPPORT	1
#define CACHE_INFO_MODEL	0
#define MAX_AMI_ELINK_NUM	0x5
#define ELINK_ARRAY_NUM	0x10
#define TSEG_CACHING_TYPE	0x6000
#define NB_NUMBER_OF_HOST_BRG	0x1
#define RBC0_IOAPIC_ID	0xf0
#define NBIO_IOAPIC_LENGTH	0x1000
#define NBIO_IOMMU_LENGTH	0x80000
#define PSP_BASE_LENGTH	0x100000
#define PCI_REGISTER_MAX	0x1000
#define MEMORY_ARRAY_NUM	0x1
#define NB_DYNAMIC_MEMORY_CEILING	1
#define MCT_BOTTOM_IO_VALUE	0xb0
#define MCT_MIN_RECLAIM_MEM_VAL	0x0
#define NB_DEBUG_MESSAGE	0
#define NB_SHOW_EXTENDED_SETUP_INFO	1
#define SMBIOS_TYPE16_FROM_AGESA	0x1
#define MAXIMUM_CAPACITY	0x800000
#define DIMM_SPD_MAP	{0, 0, 0xA0},{0, 1, 0xA4},{1, 0, 0xA2},{1, 1, 0xA6}
#define BY_PASS_SPD_CONTROL	1
#define MEMORY_DOWN	0x0
#define CMOS_AGESA_SPECIFIED	0x84
#define MAX_ROOT_BRIDGE_COUNT	0x10
#define MM_LEGACY_RESERVE_MEM	0x1000000
#define MMIO_64BIT_LIMIT	0x1FFFFFFFFFF
#define NMI_BUTTON_SUPPORT	1
#define NB_SETUP_SUPPORT	1
#define CXL_SUPPORT	1
#define RBC4_SUPPORT	1
#define RBC5_SUPPORT	1
#define RBC6_SUPPORT	1
#define RBC7_SUPPORT	1
#define RBC8_SUPPORT	1
#define RBC9_SUPPORT	1
#define RBCA_SUPPORT	1
#define RBCB_SUPPORT	1
#define RBCC_SUPPORT	1
#define RBCD_SUPPORT	1
#define RBCE_SUPPORT	1
#define RBCF_SUPPORT	1
#define SB_IOAPIC_ID	0x80
#define CHIPSET_VENDOR	0x1
#define SB_TEMPLATE_VER	0x5
#define SB_RESET_PPI_SUPPORT	0
#define SB_STALL_PPI_SUPPORT	1
#define SMBUS_BASE_ADDRESS	0xb00
#define SMBUS_BASE_LENGTH	0x10
#define SMBUS1_BASE_ADDRESS	0xb20
#define SMBUS1_BASE_LENGTH	0x20
#define DEBUG_COM_PORT_ADDR	0x3f8
#define SB_PIRQ_ROUTER_VID	0x1002
#define SB_PIRQ_ROUTER_DID	0x439d
#define PM_BASE_LENGTH	0xa0
#define PM2_BASE_ADDRESS	0xfe00
#define PBLK	0x810
#define PMBS	0x800
#define GPBS	0x0
#define GPLN	0x0
#define IDE_BASE_ADDRESS	0xff00
#define APCB	0xfec00000
#define APCL	0x1000
#define ACPI_SCI_INT	0x9
#define ACPI_ALARM_DAY_CMOS	0xd
#define ACPI_ALARM_MONTH_CMOS	0x0
#define ACPI_CENTURY_CMOS	0x32
#define ACPI_RESET_REG_ADDRESS	0xcf9
#define ACPI_RESET_REG_TYPE	0x1
#define ACPI_RESET_REG_BITWIDTH	0x8
#define ACPI_RESET_REG_BITOFFSET	0x0
#define ACPI_RESET_REG_VALUE	0x6
#define PM1A_EVT_BLK_ADDRESS	0x800
#define PM1A_EVT_BLK_TYPE	0x1
#define PM1A_EVT_BLK_BITWIDTH	0x20
#define PM1A_EVT_BLK_BITOFFSET	0x0
#define PM1_EVT_LENGTH	0x4
#define PM1A_CNT_BLK_ADDRESS	0x804
#define PM1A_CNT_BLK_TYPE	0x1
#define PM1A_CNT_BLK_BITWIDTH	0x10
#define PM1A_CNT_BLK_BITOFFSET	0x0
#define PM1_CNT_LENGTH	0x2
#define PM1B_EVT_BLK_ADDRESS	0x0
#define PM1B_EVT_BLK_TYPE	0x1
#define PM1B_EVT_BLK_BITWIDTH	0x0
#define PM1B_EVT_BLK_BITOFFSET	0x0
#define PM1B_CNT_BLK_ADDRESS	0x0
#define PM1B_CNT_BLK_TYPE	0x1
#define PM1B_CNT_BLK_BITWIDTH	0x0
#define PM1B_CNT_BLK_BITOFFSET	0x0
#define PM2_CNT_BLK_ADDRESS	0x0
#define PM2_CNT_BLK_TYPE	0x1
#define PM2_CNT_BLK_BITWIDTH	0x8
#define PM2_CNT_BLK_BITOFFSET	0x0
#define PM2_CNT_LENGTH	0x0
#define PM_TMR_BLK_ADDRESS	0x808
#define PM_TMR_BLK_TYPE	0x1
#define PM_TMR_BLK_BITWIDTH	0x20
#define PM_TMR_BLK_BITOFFSET	0x0
#define PM_TMR_LENGTH	0x4
#define GPE0_BLK_ADDRESS	0x820
#define GPE0_BLK_TYPE	0x1
#define GPE0_BLK_BITWIDTH	0x40
#define GPE0_BLK_BITOFFSET	0x0
#define GPE0_BLK_LENGTH	0x8
#define GPE1_BLK_ADDRESS	0x0
#define GPE1_BLK_TYPE	0x1
#define GPE1_BLK_BITWIDTH	0x0
#define GPE1_BLK_BITOFFSET	0x0
#define GPE1_BLK_LENGTH	0x0
#define GPE1_BASE_OFFSET	0x0
#define REMOTE_POWER_ON_SUPPORTED	1
#define PRIMARY_CHANNEL_ENABLE	1
#define PRIMARY_MASTER_DRIVE_ENABLE	1
#define MAX_UDMA	0x6
#define PRIMARY_SLAVE_DRIVE_ENABLE	1
#define SECONDARY_CHANNEL_ENABLE	1
#define SECONDARY_MASTER_DRIVE_ENABLE	1
#define SECONDARY_SLAVE_DRIVE_ENABLE	1
#define BUSMASTER_ENABLE	1
#define MASTER_INTERRUPT_BASE	0x58
#define SLAVE_INTERRUPT_BASE	0x70
#define PRIMARY_IDE_GPI	0x5
#define SPI_BASE_ADDRESS	0xfec10000
#define SPI_STS	0x0
#define SPI_CTL	0x2
#define SPI_ADR	0x4
#define SPI_DAT0	0x8
#define SPI_BIOS_BAR	0x50
#define SPI_PREOP	0x54
#define SPI_OPTYPE	0x56
#define SPI_OPMENU	0x58
#define SPI_OPCODE_WRITE_INDEX 	0x0
#define SPI_OPCODE_READ_INDEX	0x1
#define SPI_OPCODE_ERASE_INDEX	0x2
#define SPI_OPCODE_READ_S_INDEX	0x3
#define SPI_OPCODE_READ_ID_INDEX	0x4
#define SPI_OPCODE_WRITE_S_INDEX	0x5
#define SPI_OPCODE_WRITE_E_INDEX	0x6
#define SPI_OPCODE_WRITE_S_E_INDEX	0x7
#define SPI_OPCODE_TYPE_READ_NO_ADDRESS	0x0
#define SPI_OPCODE_TYPE_WRITE_NO_ADDRESS	0x1
#define SPI_OPCODE_TYPE_READ_WITH_ADDRESS	0x2
#define SPI_OPCODE_TYPE_WRITE_WITH_ADDRESS	0x3
#define SPI_MAX_TRANSFER	0x40
#define HPET_BASE_ADDRESS	0xfed00000
#define PCI_EHCI_BAR_OFFSET	0x10
#define EHCI_DEBUG_PORT_SELECT	0x2
#define EHCI_MMIO_SIZE	0x400
#define SB_PCI_DEVICES_SSID_TABLE	{SATA_BUS_DEV_FUN, (UINT32)-1}, {SMBUS_BUS_DEV_FUN, (UINT32)-1}, {USB1_EHCI_BUS_DEV_FUN, (UINT32)-1},{LPC_BUS_DEV_FUN, (UINT32)-1}, {USB_XHCI_BUS_DEV_FUN, (UINT32)-1}, {(UINT64)-1, (UINT32)-1}
#define SMM_SB800_TIMER_SUPPORT	1
#define SW_SMI_ACTIVATE_TIMER_SMI_S3	0xa2
#define SHOW_IDE_MODE_SETUP	0
#define SB_OEM_4080PIN_CABLE_STATUS	0
#define SB_HPET_OEM_ID	T_ACPI_OEM_ID
#define SB_HPET_OEM_TBL_ID	T_ACPI_OEM_TBL_ID
#define SB_WDRT_OEM_ID	T_ACPI_OEM_ID
#define SB_WDRT_OEM_TBL_ID	T_ACPI_OEM_TBL_ID
#define DEVICE_NAME_LENGTH	0xe
#define SB_ENHANCE_BOOT_FAIL_TIMER	0
#define SB_POWER_SAVING_SUPPORT	0
#define DEFAULT_MONTH	0x1
#define DEFAULT_DAY	0x1
#define CTRL_HOME_CHECK_LOOP	0xf
#define FCH_FIRMWARE_SIZE	0x1000
#define SB900_EC_SUPPORT	0
#define EC_LDN5_MAILBOX_BASE_ADDRESS	0x550
#define EC_LDN5_IRQ	0x5
#define EC_LDN9_MAILBOX_BASE_ADDRESS	0x3e
#define FCH_NO_XHCI_SUPPORT	1
#define FCH_FW_FAMILY	0x6
#define SB_SIO_PME_BASE_ADDRESS	0xe00
#define SB_DEBUG_OPTION_SUPPORT	1
#define SB_THRMTRIP_SUPPORT	1
#define SB_PCIB_DEBUG_OPTIONS	1
#define SB_KBC_ASSERT_CLEAR	0
#define USB_RX_MODE	0x0
#define SB_MMIO_SUPPORT	1
#define SB_MMIO_BASE	0xfed80000
#define CMOS_BANK1_INDEX_IS_TRANSLATED	0
#define CMOS_BAD_CHECK_ADDRESS	0x9f
#define CMOS_BAD_EXTRA_CHECK_ADDRESS	0x9e
#define SB_S3_SMI_SUPPORT	1
#define SB_START_TIMER_SMI	0xb5
#define SB_STOP_TIMER_SMI	0xb6
#define SB_TIMER_TICK_INTERVAL_WA	0
#define SB_USB_BATTERY_CHARGE_SUPPORT	0
#define FCH_USB1_OVER_CURRENT_CONTROL	{0xFF, 0xFF, 0xFF, 0xFF, 0xFF}
#define FCH_USB2_OVER_CURRENT_CONTROL	{0xFF, 0xFF, 0xFF, 0xFF, 0xFF}
#define FCH_USB3_OVER_CURRENT_CONTROL	{0xFF, 0xFF, 0xFF, 0xFF}
#define FCH_XHCI0_OVER_CURRENT_CONTROL	{0xFF, 0xFF}
#define FCH_CAPSULE_STOP_USB_SUPPORT	1
#define AMD_SB_CODEC_TABLE_BY_ELINK	1
#define CONTROL_ALL_USB_METHOD	0
#define OEM_USB_PER_PORT_DISABLE_SUPPORT	0
#define BLDCFG_SMBUS0_BASE_ADDRESS	0xb00
#define BLDCFG_SMBUS1_BASE_ADDRESS	0xb20
#define BLDCFG_SIO_PME_BASE_ADDRESS	0xe00
#define BLDCFG_ACPI_PM1_EVT_BLOCK_ADDRESS	0x800
#define BLDCFG_ACPI_PM1_CNT_BLOCK_ADDRESS	0x804
#define BLDCFG_ACPI_PM_TMR_BLOCK_ADDRESS	0x808
#define BLDCFG_ACPI_CPU_CNT_BLOCK_ADDRESS	0x810
#define BLDCFG_ACPI_GPE0_BLOCK_ADDRESS	0x820
#define BLDCFG_ACPI_PMA_BLK_ADDRESS	0xfe00
#define BLDCFG_SMI_CMD_PORT_ADDRESS	0xb2
#define BLDCFG_ROM_BASE_ADDRESS	0xfec10000
#define BLDCFG_GEC_SHADOW_ROM_BASE	0xfed61000
#define BLDCFG_HPET_BASE_ADDRESS	0xfed00000
#define BLDCFG_SMBUS_SSID	0x0
#define BLDCFG_IDE_SSID	0x0
#define BLDCFG_SATA_AHCI_SSID	0x0
#define BLDCFG_SATA_IDE_SSID	0x0
#define BLDCFG_SATA_RAID5_SSID	0x0
#define BLDCFG_SATA_RAID_SSID	0x0
#define BLDCFG_EHCI_SSID	0x0
#define BLDCFG_LPC_SSID	0x0
#define BLDCFG_SD_SSID	0x0
#define BLDCFG_XHCI_SSID	0x0
#define BLDCFG_FCH_PORT80_BEHIND_PCIB	!1
#define BLDCFG_FCH_IR_PIN_CONTROL	0x23
#define FCH_NO_IMC_SUPPORT	TRUE
#define XHCI0_PME_MAP_EVENTBIT	0x19
#define XHCI1_PME_MAP_EVENTBIT	0x19
#define USB_WAKUP0_MAP_EVENTBIT	0x18
#define USB_WAKUP1_MAP_EVENTBIT	0x18
#define USB_WAKUP2_MAP_EVENTBIT	0x18
#define USB_WAKUP3_MAP_EVENTBIT	0x18
#define SBGPP_PME0_MAP_EVENTBIT	0xf
#define SBGPP_PME1_MAP_EVENTBIT	0x10
#define SBGPP_PME2_MAP_EVENTBIT	0x11
#define SBGPP_PME3_MAP_EVENTBIT	0x12
#define SBGPP_HP0_MAP_EVENTBIT	0x0
#define SBGPP_HP1_MAP_EVENTBIT	0x0
#define SBGPP_HP2_MAP_EVENTBIT	0x0
#define SBGPP_HP3_MAP_EVENTBIT	0x0
#define SATA_GEVENT0_MAP_EVENTBIT	0x1e
#define SATA_GEVENT1_MAP_EVENTBIT	0x1f
#define IMC_GEVENT0_MAP_EVENTBIT	0xc
#define IMC_GEVENT1_MAP_EVENTBIT	0x0
#define CIR_PME_MAP_EVENTBIT	0x1c
#define WAKEPIN_GEVENT_MAP_EVENTBIT	0x0
#define FANTH_GEVENT_MAP_EVENTBIT	0xd
#define ASF_MASTERINTR_MAP_EVENTBIT	0x0
#define ASF_SLAVEINTR_MAP_EVENTBIT	0x0
#define SMBUS0_MAP_EVENTBIT	0x0
#define TWARN_MAP_EVENTBIT	0x1
#define TMI_MAP_EVENTBIT	0x0
#define COLD_RESET_WITH_POWER_CYCLE	0
#define NONE_SIO_KBC_SUPPORT	1
#define FCH_SERIAL_IRQ_MODE	0
#define FCH_LPC_CLK_DRVSTH	0x1
#define FCH_SATA_DEVSLP_PORT0	0
#define FCH_SATA_DEVSLP_PORT0_S5PIN	0x6
#define FCH_SATA_DEVSLP_PORT1	0
#define FCH_SATA_DEVSLP_PORT1_S5PIN	0x0
#define FCH_SATADBGTX_DRV_STR	0x6
#define FCH_SATADBGTX_DE_EMPH_STR	0x6
#define USB_BATTERY_CHARGE_ENABLE	0
#define REDUCE_USB3PORT_TO_LASTTWO	0
#define SB_DEVICE_REGS_RC_DEFINITION_SUPPORT	1
#define SB_DEVICE_REGS_RC_DEFINITION_HEADER	<Token.h>
#define SDIO_INSTALLATION_SUPPORT	0
#define SB_AMI_IO_DECODE_LIB_SUPPORT	1
#define DISABLE_DAYLIGHT_SAVINGS	0
#define SB_WAKEUP_TYPE_FN	1
#define AMD_SB_SIO_PME_BASE_WORKAROUND	0
#define LEGACY_UART_INPUT_CLOCK	0x1c2000
#define FchUart2LegacyEnableToken	0x0
#define FchUart3LegacyEnableToken	0x0
#define DXE_SMM_USBSB_SOURCE_OVERRIDE	1
#define GPIO_Sticky	0x4
#define GPIO_PullUpB	0x8
#define GPIO_PullDown	0x10
#define GPIO_GpioOutEnB	0x20
#define GPIO_GpioOut	0x40
#define AMDMUF0	0x0
#define AMDMUF1	0x100
#define AMDMUF2	0x200
#define AMDMUF3	0x300
#define GPIO_PullUpSel1	0x800
#define GPIO_DrvStrengthSel0	0x0
#define GPIO_DrvStrengthSel1	0x1000
#define GPIO_DrvStrengthSel2	0x2000
#define GPIO_DrvStrengthSel3	0x3000
#define IS_GPIO	0x8000
#define IS_NOT_GPIO	0x0
#define IS_GPO	0x8000
#define IS_GPI	0x8020
#define OUTPUT_HIGH	0x40
#define OUTPUT_LOW	0x0
#define GPO_HIGH	0x8040
#define GPO_LOW	0x8000
#define SmBus_SUPPORT	0
#define SB_SETUP_SUPPORT	1
#define SW_SMI_ACPI_ENABLE	0xa0
#define SW_SMI_ACPI_DISABLE	0xa1
#define GPIO_SCI_BITMAP	0x0
#define EXTENDED_SMI	0xf0
#define GPI_DISPATCH_BY_BITMAP	1
#define AHCI_SUPPORT	1
#define AHCI_DRIVER_VERSION	0x24
#define PORT_MULTIPLIER_SUPPORT	0
#define INDEX_DATA_PORT_ACCESS	0
#define AHCI_USE_PCIIO_FOR_MMIO_AND_IO	0
#define HDD_PASSWORD_SUPPORT_UNDER_RAIDMODE	0
#define SUPPORT_ATAPI_IN_RAID_MODE	1
#define DiPM_SUPPORT	0
#define ENABLE_DIPM	0
#define DEVICE_SLEEP_SUPPORT	0
#define ENABLE_DEVICE_SLEEP	0
#define USE_PCIIO_MAP_ADDRESS_FOR_DATA_TRANSFER	1
#define AHCI_VERBOSE_PRINT	0x0
#define AINT13_SUPPORT	1
#define AHCI_CONTROLLER_COUNT	0x8
#define AI13_BINARY_VERSION	0x165a
#define AHCI_INT13_SMM_SUPPORT	1
#define AHCI_INT13_SMM_SWSMI_VALUE	0x43
#define AVOID_MULTIPLE_BIG_REAL_MODE_SWITCH	1
#define ACCESS_MMIO_THROUGH_SWSMI	0
#define AmiAfuProtocol_SUPPORT	1
#define AFU_PROTOCOL_VERSION	0xf
#define AFU_PROTOCOL_ARCH	0
#define FLASH_INTERFACE_TYPE	0x2
#define OEM_FIRMWARE_HEADER_SIZE	0x0
#define MAX_FLASH_CHIP_NAME_LENGTH	0x30
#define FLASH_INTERFACE_BY_SMI	1
#define FLASH_INTERFACE_BY_RUNTIMENVRAMHOOK	0
#define AfuProtocolPspFlash_SUPPORT	0
#define TSE_MAJOR	0x2
#define TSE_MINOR	0x22
#define TSE_BUILD	0x1294
#define SETUP_BMP_LOGO_SUPPORT	1
#define SETUP_GIF_LOGO_SUPPORT	0
#define SETUP_PCX_LOGO_SUPPORT	0
#define SETUP_PNG_LOGO_SUPPORT	0
#define TSE_WATCHDOG_TIMER	1
#define TSE_PARTIAL_KEY_SUPPORT	0
#define SETUP_OEM_FORMAT_LOGO_SUPPORT	0
#define SETUP_GRAYOUT_READONLY_CONTROL	0
#define TSE_SETUP_GRAYOUT_SELECTABLE	0
#define MAX_POST_GC_MODE	800, 600
#define SERIAL_DEBUG	1
#define SETUP_PRINT_ENTER_SETUP_MSG	1
#define SETUP_PRINT_ENTER_BBSPOPUP_MSG	1
#define SETUP_ENTRY_UNICODE	0x0000
#define SETUP_ENTRY_SHIFT_STATE	0
#define TSE_ANSI_ESC_CODE_SUPPORT	0x0
#define SETUP_TOGGLE_KEY_UNICODE	L'\t'
#define SETUP_TOGGLE_KEY_SCANCODE	0x0000
#define POPUP_MENU_ENTER_SETUP	1
#define POPUP_MENU_ENTRY_UNICODE	0x0000
#define POPUP_MENU_ENTRY_SHIFT_STATE	0
#define SETUP_OEM_KEY1_ENABLE	0
#define SETUP_OEM_KEY2_ENABLE	0
#define SETUP_OEM_KEY3_ENABLE	0
#define SETUP_OEM_KEY4_ENABLE	0
#define TSE_HOOKBASE_VALUE	0xFFFF0000
#define MAX_MSGBOX_WIDTH	0x2d
#define WIDE_GLYPH_SUPPORT	1
#define SETUP_DEFAULT_TIMEOUT	0xa
#define SETUP_TIMEOUT_IN_TENTHOFSEC	0
#define TSE_FOR_64BIT	1
#define TSE_CSM_SUPPORT	1
#define PROGRESSBAR_BACKGROUNDCOLOR	{0,0,0,0}
#define PROGRESSBAR_BORDERCOLOR	{0xc0,0xc0,0xc0,0}
#define PROGRESSBAR_FILLCOLOR	{0xc0,0xc0,0xc0,0}
#define TSE_DEVICE_PATH_NAME	0
#define TSE_CLEAR_USERPW_WITH_ADMINPW	0
#define SETUP_USE_AMI_DEFAULTS	1
#define TSE_LOAD_PASSWORD_ON_DEFAULTS	0
#define MINISETUP_MOUSE_SUPPORT	0
#define SINGLE_CLICK_ACTIVATION	0
#define TSE_USE_AMI_EFI_KEYCODE_PROTOCOL	1
#define SETUP_STORE_KEYCODE_PASSWORD	0x0
#define TSE_PRN_SCRN_EVENT_SUPPORT	0
#define STYLE_DATE_FORMAT	0x0
#define STYLE_SHOW_DAY	1
#define SETUP_USER_PASSWORD_POLICY	1
#define SETUP_SUPPORT_ADD_BOOT_OPTION	0
#define SETUP_SUPPORT_ADD_DRIVER_OPTION	0
#define SETUP_ENTRY_LEGACY_CODE	0x53
#define POPUP_MENU_ENTRY_LEGACY_CODE	0x41
#define SETUP_SUPPORT_KEY_MONITORING	0
#define TSE_CAPITAL_BOOT_OPTION	1
#define PASSWORD_WITH_SPECIAL_CHAR_SUPPORT	1
#define SETUP_LINK_HISTORY_SUPPORT	1
#define SETUP_DISPLAY_SUBMENU_PAGETITLE	0
#define SETUP_GROUP_DYNAMIC_PAGES	1
#define GROUP_DYNAMIC_PAGES_BY_CLASSCODE	0
#define SETUP_ORPHAN_PAGES_AS_ROOT_PAGE	0
#define SETUP_ITK_COMPATIBILITY	0
#define NO_VARSTORE_SUPPORT	0
#define TSE_CONTINUE_BOOT_NOW_ON_FAIL	1
#define SETUP_UPDATE_BOOT_ORDER_CURSOR	0
#define TSE_PRESERVE_DISABLED_BBS_DEVICE_ORDER	0
#define TSE_BOOT_TIME_OUT_AS_ZERO	0
#define TSE_POSTSCREEN_SCROLL_AREA	{0,0,24,80}
#define DRIVER_HEALTH_SUPPORT	1
#define HONOR_DRVHLTH_CONFIGREQD_ON_BOOTFAIL	0x0
#define EDK_1_05_RETRIEVE_DATA	1
#define SECONDARY_BOOT_FROM_ENABLED_DEVICES	0
#define AMITSE_SUBPAGE_AS_ROOT_PAGE_LIST_SUPPORT	1
#define AMITSE_HIDE_ROOT_PAGE	1
#define OVERRIDE_CheckIsAllowedPasswordChar	0
#define OVERRIDE_CheckSystemPasswordPolicy	0
#define OVERRIDE_CheckIsAllowedPasswordCharCombination	0
#define OVERRIDE_HelperIsPasswordCharValid	0
#define OVERRIDE_PopupPasswordFormCallback	0
#define OVERRIDE_PopupPwdHandleActionOverRide	0
#define OVERRIDE_PasswordCheckInstalled	0
#define OVERRIDE_PasswordAuthenticate	0
#define OVERRIDE_PasswordUpdate	0
#define OVERRIDE_PasswordCommitChanges	0
#define OVERRIDE_PopupPasswordCheckInstalled	0
#define OVERRIDE_PopupPasswordAuthenticate	0
#define OVERRIDE_GetAMITSEVariable	0
#define OVERRIDE_GetBootTimeOut	0
#define OVERRIDE_IsOEMLogoType	0
#define OVERRIDE_ConvertOEMFormatToUgaBlt	0
#define OVERRIDE_CleanUpOEMLogo	0
#define OVERRIDE_DoOEMLogoAnimate	0
#define OVERRIDE_BBSBuildName	0
#define OVERRIDE_BBSGetNonStandardGroupType	0
#define OVERRIDE_FramePwdCallbackIdePasswordUpdate	0
#define OVERRIDE_PopupPwdAuthenticateIDEPwd	0
#define OVERRIDE_CheckForIDEPasswordInstalled	0
#define OVERRIDE_PopupPwdUpdateIDEPwd	0
#define OVERRIDE_TSEIDEPasswordGetName	0
#define OVERRIDE_TSEIDEPasswordAuthenticate	0
#define OVERRIDE_TSEIDEPasswordUpdate	0
#define OVERRIDE_TSEIDEPasswordGetDataPtr	0
#define OVERRIDE_TSEIDEPasswordGetLocked	0
#define OVERRIDE_TSEIDEPasswordCheck	0
#define OVERRIDE_TSEIDEPasswordFreezeDevices	0
#define OVERRIDE_TSEUnlockHDD	0
#define OVERRIDE_TSESetHDDPassword	0
#define OVERRIDE_TSEIDEUpdateConfig	0
#define OVERRIDE_OEMSpecialGetControlCount	0
#define OVERRIDE_OEMSpecialOneOfFixup	0
#define OVERRIDE_OEMSpecialUpdateOneOf	0
#define OVERRIDE_OEMSpecialGotoFixup	0
#define OVERRIDE_OEMSpecialGotoSelect	0
#define OVERRIDE_TSEMouseInitHook	0
#define OVERRIDE_TSEMouseStopHook	0
#define OVERRIDE_TSEMouseDestoryHook	0
#define OVERRIDE_TSEMouseRefreshHook	0
#define OVERRIDE_TSEMouseStartHook	0
#define OVERRIDE_TSEMouseIgnoreMouseActionHook	0
#define OVERRIDE_TSEStringReadLoopEntryHook	0
#define OVERRIDE_TSEStringReadLoopExitHook	0
#define OVERRIDE_GetMessageboxColorHook	0
#define OVERRIDE_GOPSetScreenResolutionHook	0
#define OVERRIDE_LaunchHotKeyBootOption	0
#define OVERRIDE_DriverHealthSystemReset	0
#define OVERRIDE_UefiBootFailHook	0
#define OVERRIDE_LegacyBootFailHook	0
#define OVERRIDE_UpdateNumericDisplayString	0
#define OVERRIDE_MainSetupLoopHook	0
#define OVERRIDE_MainSetupLoopInitHook	0
#define OVERRIDE_ProcessBrowserActionRequestHook	0
#define OVERRIDE_OEMCheckControlCondition	0
#define OVERRIDE_GetUefiSpecVersion	0
#define OVERRIDE_TSEMouseInit	0
#define OVERRIDE_TSEMouseStop	0
#define OVERRIDE_TSEIsMouseClickedonSoftkbd	0
#define OVERRIDE_TSEMouseRefresh	0
#define OVERRIDE_TSEMouseStart	0
#define OVERRIDE_TSEMouseFreeze	0
#define OVERRIDE_TSEGetCoordinates	0
#define OVERRIDE_TSEMouseReadInfo	0
#define OVERRIDE_TSEMouseDestroy	0
#define OVERRIDE_TSEGetactualScreentop	0
#define OVERRIDE_TSENumericSoftKbdInit	0
#define OVERRIDE_TSENumericSoftKbdExit	0
#define OVERRIDE_LaunchSecondaryBootPath	0
#define OVERRIDE_OsUpdateCapsuleWrap	0
#define OVERRIDE_UIUpdateCallbackHook	0
#define OVERRIDE_FixMergePagesExtraHook	0
#define OVERRIDE_PageRemoveHook	0
#define OVERRIDE_ProcessPackNotificationHook	0
#define OVERRIDE_SaveSetupGobalDataHook	0
#define OVERRIDE_RestoreSetupGobalDataHook	0
#define OVERRIDE_CsmBBSBootOptionName	0
#define OVERRIDE_GetCurrentSetupPageID	0
#define OVERRIDE_BootGetBootOptionNameUEFIAndLegacy	0
#define CONTRIB_BGRT_TABLE_TO_ACPI	0x1
#define TSE_USE_GETGYLPH_PRINTLENGH	1
#define SETUP_HIDE_DISABLE_BOOT_OPTIONS	0
#define POPUP_MENU_HIDE_DISABLE_BOOT_OPTIONS	0
#define SETUP_HIDE_BIOS_SIGNON_MESSAGE2	0
#define AMITSE_VERSION	Version %x.%02x.%04x.
#define BOOT_FLOW_NORMAL_LAUNCH_DEFAULT_BOOTIMAGE	1
#define BOOT_FLOW_NORMAL_INFINITE_LOOP	0
#define TSE_ROMHOLE_SUPPORT	1
#define TSE_OEM_POSTLOGO_SUPPORT	0
#define BOOT_TO_IMAGE_CODE_TYPE_SUPPORT	1
#define TSE_ROMHOLE_MAKFILE_GUID	05ca020b-0fc1-11dc-9011-00173153eba8
#define TSE_ROMHOLE_HEADER_GUID	{0x05ca020b, 0x0fc1, 0x11dc, {0x90, 0x11, 0x00, 0x17, 0x31, 0x53, 0xeb, 0xa8}}
#define ROMHOLE_BLOCK_SIZE	0x10000
#define ROMHOLE_NUMBER_OF_BLOCK	0x2
#define OSIndication_SUPPORT	1
#define SETUP_DELAY_LOGO_TILL_INPUT	0
#define SETUP_DELAY_POST_TILL_GOP	1
#define DRAW_POST_LOGO_AT_0POS	0
#define TSE_IGNORE_KEY_FOR_FASTBOOT	0
#define TSE_CURSOR_SUPPORT	1
#define TSE_SUPPORT_NATIVE_RESOLUTION	1
#define BOOT_OVERRIDE_BOOTNEXT_VARIABLE_FEATURE	0
#define SETUP_SAVE_PSWD_TO_NVRAM	0
#define TSE_DONOT_LOAD_PASSWORD_ON_DEFAULTS	0
#define FLUSH_KEYS_AFTER_READ	1
#define TSE_LOAD_DEFAULTS_FROM_DEFAULTS_BUFFER	1
#define AMITSE_ROOT_PAGE_ORDER_SUPPORT	1
#define SETUP_ROOT_PAGE_ORDER_GUID	  { 0x9204ecbe, 0xa665, 0x49d3,  {0x86, 0xde, 0x8, 0x12, 0x99, 0xe2, 0x23, 0xef }}
#define SETUP_GO_TO_EXIT_PAGE_ON_EXIT_KEY	0
#define TSE_SHOW_PROMPT_STRING_AS_TITLE	0
#define TSE_DONOT_LOAD_SYSTEM_ACCESS_FOR_USER_DEFAULT	0
#define SHOW_FORMSETS_WITH_CLASSGUID	1
#define TSE_DBG_DISABLE_APPDESTRUCTOR	0
#define TSE_HASH_PASSWORD	1
#define TSE_PWD_ENCRYPT_USING_SHA256	1
#define TSE_PWD_ENCRYPT_USING_SHA384	0
#define TSE_PWD_ENCRYPT_USING_SHA512	0
#define EXTENDED_TSE_PWD_ENCRYPT_SUPPORT	0
#define SUPPRESS_HANDLE_FOR_VAR_CREATION	0
#define TSE_BUILD_AS_APPLICATION	0
#define OVERRIDE_GetGraphicsBitMapFromFV	0
#define OVERRIDE_CheckAdvShiftState	0
#define OVERRIDE_LaunchSecBootPathFromEnabledDevs	0
#define OVERRIDE_FramePasswordAdvancedCallback	0
#define OVERRIDE_TseLaunchFileSystem	0
#define TSE_DEFAULT_SETUP_PASSWORD_SUPPORT	0
#define RT_ACCESS_FOR_EFIVARSTORE	0
#define DISABLE_ESC_IN_BBS	0
#define TSE_FOR_EDKII_SUPPORT	0
#define OVERRIDE_ESAInitFvHook	0
#define OVERRIDE_SetMessageBoxProgressType	0
#define OVERRIDE_SetResAndScreenBufferHook	0
#define TSE_RECALCULATE_LAYOUT_TO_MAX_RES	0
#define TSE_BEST_TEXT_GOP_MODE	0
#define TSE_SUPPORT_WRITE_CHAR_LAST_ROW_COLUMN	0
#define TSE_FULL_SCREEN_POST_SUPPORT	0
#define TSE_MOUSE_POST_TRIGGER_TIME	0x4c4b40
#define TSE_SKIP_ORPHAN_BOOT_ORDER_VARIABLE	1
#define TSE_ISOLATE_SETUP_DATA	1
#define TSE_WAIT_FOR_KEY_EVENT_BEFORE_READ	0
#define DEFAULT_CONDITIONAL_EXPRESSION_SUPPORT	1
#define TSE_DISPLAY_FORMSET_PARSING_ERROR	0
#define TSE_CACHE_PLATFORM_LANG	0x0
#define TSE_RECOVERY_SUPPORT	0
#define ESA_RECONNECT_SUPPORT	1
#define OVERRIDE_TSEBreakInteractiveBbsPopup	0
#define TSE_BREAK_INTERACTIVE_BBS_POPUP	0
#define TSE_BOOT_TO_DISABLED_BOOT_OPTIONS	0
#define TSE_FILTER_DRVHLTH_CREDENTIAL_CLASSGUID	0
#define TSE_DEVICE_PATH_TO_TEXT_PROTOCOL_SUPPORT	0
#define REPORT_MAPPING_ID_CONFLITCS_IN_FILE	0
#define TSE_MAPPING_LANGUAGE_SKIP_NONSTORAGE_CONTROLS_CHECK	0
#define TSE_UEFI_26_FEATURES_SUPPORT	1
#define TSE_EFI_BROWSER_ACTION_FORM_OPEN_ON_PAGE_NAVIGATION_SUPPORT	1
#define SETUP_FORM_BROWSER_NESTED_SEND_FORM_SUPPORT	1
#define TSE_ENFORCE_SENDFORM_PARAM_CHECK	0
#define TSE_ENFORCE_FDF_PACKAGING	0
#define BIN_SUPPORT_IN_HPKTOOL	1
#define TSE_PAUSE_PROTOCOL_SUPPORT	1
#define TSE_USE_FILE_BROWSER_PROTOCOL	1
#define SW_SCHEMA_SETUP_DATA	AmiTsePkg/Core/em/AMITSE/SetupPackageSchema.xsd
#define FIRST_BOOT_DEFAULTS_EVALUATION	1
#define TSE_CLANG_SUPPORT	1
#define JSON_CONFIG_CAPSULE_SUPPORT	1
#define TSE_ALT_DEFAULTS_CONFIGURATION	1
#define TSE_STRICT_MODE_GCC_SUPPORT	1
#define TSE_INCONSISTENT_IF_POLICY	0
#define TSE_ENABLE_MULTILINE_FOR_POPUP	0
#define TSE_CACHE_GET_OP	0
#define TSE_MINIMUM_YEAR	0x7ce
#define TSE_DEFAULT_YEAR	0x7e7
#define TSE_DEFAULT_MONTH	0x1
#define TSE_DEFAULT_DAY	0x1
#define QUIET_BOOT_LOGO_IN_NCB	0
#define SKIP_VAR_STRING_DEFAULTS	0
#define TSE_NVRAM_DEFAULTS_SUPPORT	1
#define DEFAULTS_BIN_IN_EDK2FORMAT	0
#define GET_DEFAULTS_FROM_VPD_BINARY	0
#define TSE_NVAR2_SUPPORT	0
#define TSE_BOARD_SOURCE_SUPPORT	1
#define TSE_ADVANCED_BIN_SUPPORT	1
#define APTIO_4_00	1
#define SETUP_SUPPORT_PLATFORM_LANG_VAR	1
#define STYLE_COLOR_GRAYOUT	EFI_DARKGRAY
#define SETUP_STYLE_BIN_EZPORT	1
#define TSE_PRN_SCRN_KEY_SCAN	SCAN_F12
#define HOTCLICK_FULLSCREEN_SUPPORT	1
#define TSE_ADVANCED_SUPPORT	1
#define TSE_SEPERATE_EFI_LEGACY_OPTIONS	0
#define MINIMUM_GIF_DELAY 	0x64
#define TSE_USE_EDK_LIBRARY	0
#define SETUP_OEM_SPECIAL_CONTROL_SUPPORT	0
#define TSE_MAX_DRIVE_NAME	0x33
#define TSE_BEST_HORIZONTAL_RESOLUTION	800
#define TSE_BEST_VERTICAL_RESOLUTION	600
#define TSE_LITE_SOURCES_SUPPORT	1
#define STYLE_CLEAR_SCREEN_COLOR	((UINT8)(EFI_BACKGROUND_BLUE) | EFI_WHITE)
#define UEFI_HII_2_1_SUPPORT	1
#define UEFI_SOURCES_SUPPORT	1
#define SETUP_FORM_BROWSER_SUPPORT	1
#define SETUP_STYLE_EZPORT	1
#define TSE_STYLE_SOURCES_SUPPORT	1
#define SETUP_STYLE_FULL_SCREEN	1
#define STYLE_FULL_MAX_ROWS	(UINT16)(31)
#define STYLE_FULL_MAX_COLS	(UINT16)(100)
#define STYLE_STD_MAX_ROWS	(UINT16)(24)
#define STYLE_STD_MAX_COLS	(UINT16)(80)
#define PAGEBORDER_FULL_X	(UINT16)(0)
#define PAGEBORDER_FULL_Y	(UINT16)(2)
#define PAGEBORDER_FULL_W	(UINT16)(STYLE_FULL_MAX_COLS)
#define PAGEBORDER_FULL_H	(UINT16)(STYLE_FULL_MAX_ROWS - 3)
#define FULL_VERTICAL_MAIN_DIVIDER	(UINT16)(STYLE_FULL_MAX_COLS*2/3)
#define FULL_HORIZONTAL_HELP_DIVIDER	(UINT16)(STYLE_FULL_MAX_ROWS/2)
#define TITLE_FULL_X	(UINT16)(0)
#define TITLE_FULL_Y	(UINT16)(0)
#define TITLE_FULL_W	(UINT16)(STYLE_FULL_MAX_COLS)
#define TITLE_FULL_H	(UINT16)(1)
#define MENU_FULL_X	(UINT16)(0)
#define MENU_FULL_Y	(UINT16)(1)
#define MENU_FULL_W	(UINT16)(STYLE_FULL_MAX_COLS)
#define MENU_FULL_H	0x1
#define HELPTITLE_FULL_X	0x0
#define HELPTITLE_FULL_Y	(UINT16)(PAGEBORDER_FULL_Y+PAGEBORDER_FULL_H)
#define HELPTITLE_FULL_W	(UINT16)(STYLE_FULL_MAX_COLS)
#define HELPTITLE_FULL_H	0x1
#define HELP_FULL_X	(UINT16)(FULL_VERTICAL_MAIN_DIVIDER+1)
#define HELP_FULL_Y	(UINT16)(PAGEBORDER_FULL_Y+1)
#define HELP_FULL_W	(UINT16)(PAGEBORDER_FULL_W - FULL_VERTICAL_MAIN_DIVIDER-2)
#define HELP_FULL_H	(UINT16)(FULL_HORIZONTAL_HELP_DIVIDER - PAGEBORDER_FULL_Y - 2)
#define MAIN_FULL_X	(UINT16)(PAGEBORDER_FULL_X+1)
#define MAIN_FULL_Y	(UINT16)(PAGEBORDER_FULL_Y+1)
#define MAIN_FULL_W	(UINT16)(FULL_VERTICAL_MAIN_DIVIDER - 2)
#define MAIN_FULL_H	(UINT16)(PAGEBORDER_FULL_H-2)
#define NAV_FULL_X	(UINT16)(FULL_VERTICAL_MAIN_DIVIDER+1)
#define NAV_FULL_Y	(UINT16)(FULL_HORIZONTAL_HELP_DIVIDER+1)
#define NAV_FULL_W	(UINT16)(PAGEBORDER_FULL_W - FULL_VERTICAL_MAIN_DIVIDER-2)
#define NAV_FULL_H	(UINT16)(PAGEBORDER_FULL_H - FULL_HORIZONTAL_HELP_DIVIDER)
#define PAGEBORDER_X	0x0
#define PAGEBORDER_Y	0x2
#define PAGEBORDER_W	(UINT16)(STYLE_STD_MAX_COLS)
#define PAGEBORDER_H	(UINT16)(STYLE_STD_MAX_ROWS - 3)
#define VERTICAL_MAIN_DIVIDER	(UINT16)(STYLE_STD_MAX_COLS*2/3)
#define HORIZONTAL_HELP_DIVIDER	(UINT16)(STYLE_STD_MAX_ROWS/2)
#define TITLE_X	(UINT16)(0)
#define TITLE_Y	(UINT16)(0)
#define TITLE_W	(UINT16)(STYLE_STD_MAX_COLS)
#define TITLE_H	(UINT16)(1)
#define MENU_X	(UINT16)(0)
#define MENU_Y	(UINT16)(1)
#define MENU_W	(UINT16)(STYLE_STD_MAX_COLS)
#define MENU_H	0x1
#define HELPTITLE_X	0x0
#define HELPTITLE_Y	(UINT16)(PAGEBORDER_Y+PAGEBORDER_H)
#define HELPTITLE_W	(UINT16)(STYLE_STD_MAX_COLS)
#define HELPTITLE_H	0x1
#define HELP_X	(UINT16)(VERTICAL_MAIN_DIVIDER+1)
#define HELP_Y	(UINT16)(PAGEBORDER_Y+1)
#define HELP_W	(UINT16)(PAGEBORDER_W - VERTICAL_MAIN_DIVIDER-2)
#define HELP_H	(UINT16)(HORIZONTAL_HELP_DIVIDER - PAGEBORDER_Y - 1)
#define MAIN_X	(UINT16)(PAGEBORDER_X+1)
#define MAIN_Y	(UINT16)(PAGEBORDER_Y+1)
#define MAIN_W	(UINT16)(VERTICAL_MAIN_DIVIDER - 2)
#define MAIN_H	(UINT16)(PAGEBORDER_H-1)
#define NAV_X	(UINT16)(VERTICAL_MAIN_DIVIDER+1)
#define NAV_Y	(UINT16)(HORIZONTAL_HELP_DIVIDER+1)
#define NAV_W	(UINT16)(PAGEBORDER_W - VERTICAL_MAIN_DIVIDER-2)
#define NAV_H	(UINT16)(PAGEBORDER_H - HORIZONTAL_HELP_DIVIDER)
#define PAGE_BGCOLOR	(UINT8)(EFI_BACKGROUND_LIGHTGRAY)
#define PAGE_FGCOLOR	(UINT8)(EFI_BLUE)
#define MAIN_BGCOLOR	(UINT8)(PAGE_BGCOLOR)
#define MAIN_FGCOLOR	(UINT8)(PAGE_FGCOLOR)
#define TITLE_BGCOLOR	(UINT8)(EFI_BACKGROUND_BLUE)
#define TITLE_FGCOLOR	(UINT8)(EFI_WHITE)
#define MENU_BGCOLOR	(UINT8)(EFI_BACKGROUND_BLUE)
#define MENU_FGCOLOR	(UINT8)(EFI_LIGHTGRAY)
#define SELECTED_MENU_BGCOLOR	(UINT8)(EFI_BACKGROUND_LIGHTGRAY)
#define SELECTED_MENU_FGCOLOR	(UINT8)(EFI_BLUE)
#define HELP_BGCOLOR	(UINT8)(PAGE_BGCOLOR)
#define HELP_FGCOLOR	(UINT8)(PAGE_FGCOLOR)
#define HELPTITLE_BGCOLOR	(UINT8)(EFI_BACKGROUND_BLUE)
#define HELPTITLE_FGCOLOR	(UINT8)(EFI_LIGHTGRAY)
#define NAV_BGCOLOR	(UINT8)(PAGE_BGCOLOR)
#define NAV_FGCOLOR	(UINT8)(PAGE_FGCOLOR)
#define SCROLLBAR_BGCOLOR	(UINT8)(PAGE_BGCOLOR)
#define SCROLLBAR_FGCOLOR	(UINT8)(PAGE_FGCOLOR)
#define SCROLLBAR_UPARROW_BGCOLOR	(UINT8)(PAGE_BGCOLOR)
#define SCROLLBAR_UPARROW_FGCOLOR	(UINT8)(PAGE_FGCOLOR)
#define SCROLLBAR_DOWNARROW_BGCOLOR	(UINT8)(PAGE_BGCOLOR)
#define SCROLLBAR_DOWNARROW_FGCOLOR	(UINT8)(PAGE_FGCOLOR)
#define POPUP_BGCOLOR	(UINT8)(EFI_BACKGROUND_BLUE)
#define POPUP_FGCOLOR	(UINT8)(EFI_WHITE)
#define FOCUS_COLOR	(UINT8)(EFI_WHITE)
#define LABEL_FOCUS_COLOR	(UINT8)(FOCUS_COLOR)
#define CONTROL_FOCUS_COLOR	(UINT8)(FOCUS_COLOR)
#define NON_FOCUS_COLOR	(UINT8)(PAGE_FGCOLOR)
#define LABEL_NON_FOCUS_COLOR	(UINT8)(NON_FOCUS_COLOR)
#define CONTROL_NON_FOCUS_COLOR	(UINT8)(NON_FOCUS_COLOR)
#define PAGE_LINK_COLOR	(UINT8)(PAGE_FGCOLOR)
#define EDIT_BGCOLOR	(UINT8)(EFI_BACKGROUND_BLUE)
#define EDIT_FGCOLOR	(UINT8)(EFI_WHITE)
#define TEXT_COLOR	(UINT8)(EFI_DARKGRAY)
#define MEMO_COLOR	(UINT8)(EFI_BLACK)
#define FULL_STYLE_CONTROL_LEFT_MARGIN	0x25
#define FULL_STYLE_CONTROL_LEFT_PAD	0x2
#define FULL_STYLE_CONTROL_RIGHT_AREA_WIDTH	((UINT16)FULL_VERTICAL_MAIN_DIVIDER - FULL_STYLE_CONTROL_LEFT_MARGIN - FULL_STYLE_CONTROL_LEFT_PAD)
#define STYLE_CONTROL_LEFT_MARGIN	0x18
#define STYLE_CONTROL_LEFT_PAD	0x2
#define STYLE_CONTROL_RIGHT_AREA_WIDTH	((UINT16)VERTICAL_MAIN_DIVIDER - STYLE_CONTROL_LEFT_MARGIN - STYLE_CONTROL_LEFT_PAD)
#define FULL_STYLE_LABEL_LEFT_MARGIN	2
#define STYLE_LABEL_LEFT_MARGIN	2
#define STYLE_SCROLLBAR_ROLLOVER	1
#define STYLE_PAGE_FIRSTITEM_FOCUS	0
#define STYLE_SUBPAGE_FIRSTITEM_FOCUS	0
#define STYLE_SHADOW_SUPPORT	1
#define JSON_SUPPORT	1
#define AgesaModulePkg_SUPPORT	1
#define PEIM_AgesaModulePkg_Debug_AmdIdsDebugPrintPei_AmdIdsDebugPrintPei_SUPPORT	1
#define PEIM_AgesaModulePkg_Fch_Common_I2cPei_I2cMasterPei_SUPPORT	1
#define PEIM_AgesaModulePkg_Psp_AmdPspPeiV2Brh_AmdPspPeiV2_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceManager3Lib_SUPPORT	1
#define PEIM_AgesaModulePkg_Psp_AmdPspDtpmPei_AmdPspDtpmPei_SUPPORT	1
#define PEIM_AgesaModulePkg_Psp_AmdPspPsbDisablePei_AmdPspPsbDisablePei_SUPPORT	1
#define PEIM_AgesaModulePkg_Psp_ApcbDrv_ApcbV3Pei_ApcbV3Pei_SUPPORT	1
#define PEIM_AgesaModulePkg_Psp_ApobDrv_ApobBrhPei_ApobBrhPei_SUPPORT	1
#define PEIM_AgesaModulePkg_Ccx_Zen5_Pei_AmdCcxZen5Pei_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_CcxResetTablesZen5Lib_CcxResetTablesZen5Lib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_CcxRolesZen5Lib_CcxRolesZen5Lib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_CcxPstatesZen5Lib_CcxPstatesZen5Lib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_CcxSetMcaZen5Lib_CcxSetMcaZen5Lib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_FabricWdtDf4Lib_FabricWdtDf4Lib_SUPPORT	1
#define PEIM_AgesaModulePkg_Fabric_BRH_FabricBrhPei_AmdFabricBrhPei_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_BaseFabricTopologyBrhLib_BaseFabricTopologyBrhLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Nbio_BRH_PEI_NbioPeiBrh_SUPPORT	1
#define PEIM_AgesaModulePkg_Fch_Kunlun_FchKunlunPei_FchPei_SUPPORT	1
#define PEIM_AgesaModulePkg_Fch_Kunlun_FchKunLunSmbusPei_Smbus_SUPPORT	1
#define PEIM_AgesaModulePkg_Fch_Common_I3cPei_I3cMasterPei_SUPPORT	1
#define PEIM_AgesaModulePkg_Fch_Common_FchEspiCmdPei_FchEspiCmdPei_SUPPORT	1
#define PEIM_AgesaModulePkg_Fch_Kunlun_FchKunlunMultiFchPei_FchMultiFchPei_SUPPORT	1
#define PEIM_AgesaModulePkg_Mem_AmdMemBrhSp5Pei_AmdMemBrhSp5Pei_SUPPORT	1
#define PEIM_AgesaModulePkg_Soc_AmdSocSp5BrhPei_AmdSocSp5BrhPei_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceInit3Lib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_BaseSocketLogicalIdRsDieLib_BaseSocketLogicalIdRsDieLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_BaseSocLogicalIdXlatZen5DieLib_BaseSocLogicalIdXlatZen5DieLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_PeiSocBistZen5CcdBrhLib_PeiSocBistZen5CcdBrhLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_PeiFabricSocSpecificServicesBrhLib_PeiFabricSocSpecificServicesBrhLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_PeiSocZen5ServicesBrhLib_PeiSocZen5ServicesBrhLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_PeiCcxCoreTopologyServicesV3BrhLib_PeiCcxCoreTopologyServicesV3BrhLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_ApobApcbUpdatesBrhLib_ApobApcbUpdatesBrhLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_FabricRootBridgeOrderLib_FabricRootBridgeOrderLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Mem_AmdMemChanXLatPei_MemChanXLatPei_SUPPORT	1
#define PEIM_AgesaModulePkg_Mem_AmdMemSmbiosV2BrhPei_MemSmbiosV2Pei_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_MemSmbiosV2BrhD5Lib_MemSmbiosV2Lib_SUPPORT	1
#define PEIM_AgesaModulePkg_Mem_AmdMemRestorePei_MemRestorePei_SUPPORT	1
#define PEIM_AgesaModulePkg_Mem_AmdMbistBrhPei_AmdMbistBrhPei_SUPPORT	1
#define PEIM_AgesaModulePkg_ErrorLog_AmdErrorLogPei_AmdErrorLogPei_SUPPORT	1
#define PEIM_AgesaModulePkg_Mem_AmdMemoryHobInfoPeimBrh_AmdMemoryHobInfoPeimBrh_SUPPORT	1
#define PEIM_AgesaModulePkg_Universal_Version_AmdVersionPei_AmdVersionPei_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Debug_AmdIdsDebugPrintDxe_AmdIdsDebugPrintDxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Mem_AmdMemSmbiosV2Dxe_AmdMemSmbiosV2Dxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Mem_AmdMemRestoreDxe_MemRestoreDxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Mem_AmdMemPprSmmDriver_AmdMemPprSmmDriver_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Mem_AmdMemChanXLatDxe_MemChanXLatDxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Psp_ApcbDrv_ApcbV3Dxe_ApcbV3Dxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Psp_ApcbDrv_ApcbV3Smm_ApcbV3Smm_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Psp_AmdPspDxeV2Brh_AmdPspDxeV2_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Psp_AmdPspDxeV2Brh_AmdDrtmAsl_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Psp_AmdPspP2CmboxV2Mcm_AmdPspP2CmboxV2_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Psp_AmdPspP2CmboxV2Mcm_AmdPspP2CmboxV2SmmBuffer_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Psp_AmdPspSmmV2Mcm_AmdPspSmmV2_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Psp_AmdHstiV2_AmdHstiV2_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Psp_ApobDrv_ApobBrhDxe_ApobBrhDxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Psp_AmdPspAspt_AmdPspAspt_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Ccx_Zen5_Dxe_AmdCcxZen5Dxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_CcxResetTablesZen5Lib_CcxResetTablesZen5Lib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_CcxSetMcaZen5Lib_CcxSetMcaZen5Lib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_FabricWdtDf4Lib_FabricWdtDf4Lib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_CcxSmbiosZen5Lib_CcxSmbiosZen5Lib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_CcxRolesZen5Lib_CcxRolesZen5Lib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_CcxPstatesZen5Lib_CcxPstatesZen5Lib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Ccx_Zen5_Smm_AmdCcxZen5Smm_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Fabric_BRH_FabricBrhDxe_AmdFabricBrhDxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceManager3Lib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_BaseFabricTopologyBrhLib_BaseFabricTopologyBrhLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Fabric_BRH_FabricBrhSmm_AmdFabricBrhSmm_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_AmdS3SaveLib_WOS3Save_AmdWOS3SaveLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Soc_AmdSocSp5BrhDxe_AmdSocSp5BrhDxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_BaseSocketLogicalIdRsDieLib_BaseSocketLogicalIdRsDieLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_BaseSocLogicalIdXlatZen5DieLib_BaseSocLogicalIdXlatZen5DieLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_DxeSocZen5ServicesBrhLib_DxeSocZen5ServicesBrhLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_DxeCcxCoreTopologyServicesV3BrhLib_DxeCcxCoreTopologyServicesV3BrhLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_DxeFabricSocSpecificServicesBrhLib_DxeFabricSocSpecificServicesBrhLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_AmdIdsExtLibNull_AmdIdsHookExtLibNull_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_ErrorLog_AmdErrorLogDxe_AmdErrorLogDxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_ErrorLog_AmdErrorLogDisplayBrhDxe_AmdErrorLogDisplayBrhDxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_ErrorLog_AmdCxlErrorLog_AmdCxlErrorLogDxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Nbio_Common_CxlManagerDxe_CxlManagerDxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Nbio_BRH_DXE_NbioDxeBrh_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Nbio_Library_IvrsLibV3_IvrsLibV3_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Nbio_BRH_Library_CollectNbifPortInfoLib_CollectNbifPortInfoLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunDxe_FchDxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunSmm_FchSmm_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunSmbusDxe_SmbusLight_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Fch_Common_I2cDxe_I2cMasterDxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Fch_Common_I2cSmm_I2cMasterSmm_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Fch_Common_I3cDxe_I3cMasterDxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Fch_Common_FchEspiCmdDxe_FchEspiCmdDxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Fch_Common_FchEspiCmdSmm_FchEspiCmdSmm_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunCf9ResetDxe_Cf9Reset_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunSmmControlDxe_SmmControl_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunSmmDispatcher_FchSmmDiagDispatcher_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunSmmDispatcher_FchSmmDispatcher_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunMultiFchDxe_FchMultiFchDxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Fch_Kunlun_FchKunlunMultiFchSmm_FchMultiFchSmm_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Universal_Smbios_AmdSmbiosDxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Universal_Acpi_AmdAcpiDxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Universal_Acpi_AmdAcpiHmatService_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Universal_AmdSmmCommunication_AmdSmmCommunication_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Universal_Version_AmdVersionDxe_AmdVersionDxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Ras_Brh_AmdRasBrhServiceDxe_AmdRasBrhServiceDxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Ras_Brh_AmdRasBrhDxe_AmdRasBrhDxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Ras_Brh_AmdRasBrhServiceSmm_AmdRasBrhServiceSmm_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Universal_ActDxe_ActDxe_SUPPORT	1
#define _AgesaModulePkg_Library_AmdPostCodeLib_AmdPostCodeLib_SUPPORT	1
#define _AgesaModulePkg_Library_AmdSocBaseLib_AmdSocBaseLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_AmdPspFlashUpdateLib_AmdPspFlashUpdateLib_SUPPORT	1
#define DXE_SMM_DRIVER_AgesaModulePkg_Library_DxeFabricResourceSizeForEachRbLib_DxeFabricResourceSizeForEachRbLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_AmdS3SaveLib_WOS3Save_AmdWOS3SaveLib_SUPPORT	1
#define _AgesaModulePkg_Library_NbioClkReqControlLibNull_NbioClkReqControlLibNull_SUPPORT	1
#define _AgesaModulePkg_Library_AmdErrorLogLib_AmdErrorLogLib_SUPPORT	1
#define _AgesaModulePkg_Library_CcxBaseX86Lib_CcxBaseX86Lib_SUPPORT	1
#define _AgesaModulePkg_Library_AmdTableLibV2_AmdTableLibV2_SUPPORT	1
#define _AgesaModulePkg_Library_CcxPstatesZen5Lib_CcxPstatesZen5Lib_SUPPORT	1
#define PEI_CORE_AgesaModulePkg_Library_AmdTableLibV2_Pei_AmdTableHookPeiLibV2_SUPPORT	1
#define _AgesaModulePkg_Library_GnbCpuAccLib_GnbCpuAccLib_SUPPORT	1
#define _AgesaModulePkg_Library_AmdMpmRegBaseLib_AmdMpmRegBaseLib_SUPPORT	1
#define _AgesaModulePkg_Library_SmmFabricTopologyServices2Lib_SmmFabricTopologyServices2Lib_SUPPORT	1
#define _AgesaModulePkg_Library_AmdDirectoryBaseLib_AmdDirectoryBaseLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_AmdCfgPcdBufLibDxe_AmdCfgPcdBufLibDxe_SUPPORT	1
#define _AgesaModulePkg_Library_FchI2cLib_FchI2cLib_SUPPORT	1
#define DXE_RUNTIME_DRIVER_AgesaModulePkg_Library_AmdCfgPcdBufLibDxe_AmdCfgPcdBufLibDxe_SUPPORT	1
#define DXE_RUNTIME_DRIVER_AgesaModulePkg_Library_AmdStbLibNull_AmdStbLibNull_SUPPORT	1
#define _AgesaModulePkg_Library_AmdIdsHookLibNull_AmdIdsHookLibNull_SUPPORT	1
#define DXE_SMM_DRIVER_AgesaModulePkg_Library_ApcbVariableLibV3_ApcbVariableLibV3_SUPPORT	1
#define _AgesaModulePkg_Library_AmdPspFwImageHeaderLib_AmdPspFwImageHeaderLib_SUPPORT	1
#define _AgesaModulePkg_Library_FchEspiLib_FchEspiLib_SUPPORT	1
#define _AgesaModulePkg_Library_AmdBaseLib_AmdBaseLibNoIntrinsic_SUPPORT	1
#define _AgesaModulePkg_Nbio_Library_CxlCdatLib_CxlCdatLib_SUPPORT	1
#define _AgesaModulePkg_Library_GnbPciSegmentAccLib_GnbPciSegmentAccLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_AmdPspRegMuxLibV2Null_AmdPspRegMuxLibV2_SUPPORT	1
#define _AgesaModulePkg_Nbio_Library_MpioLibV2_MpioLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_AmdCapsuleLibPei_AmdCapsuleLibPei_SUPPORT	1
#define _AgesaModulePkg_Library_FchSmmLib_FchSmmLib_SUPPORT	1
#define _AgesaModulePkg_Library_PcieConfigLib_PcieConfigLib_SUPPORT	1
#define _AgesaModulePkg_Library_BaseSocLogicalIdXlatZen5DieLib_BaseSocLogicalIdXlatZen5DieLib_SUPPORT	1
#define PEI_CORE_AgesaModulePkg_Library_AmdHeapPeiLib_AmdHeapPeiLib_SUPPORT	1
#define _AgesaModulePkg_Library_GnbLib_GnbLib_SUPPORT	1
#define _AgesaModulePkg_Library_AmdPspRegMuxLibV2Null_AmdPspRegMuxLibV2_SUPPORT	1
#define _AgesaModulePkg_Library_AmdIdsExtLibNull_AmdIdsHookExtLibNull_SUPPORT	1
#define DXE_RUNTIME_DRIVER_AgesaModulePkg_Library_AmdHeapDxeLib_AmdHeapDxeLib_SUPPORT	1
#define _AgesaModulePkg_Library_DxeFabricTopologyServices2Lib_DxeFabricTopologyServices2Lib_SUPPORT	1
#define _AgesaModulePkg_Library_AmdPspPsbFusingLib_AmdPspPsbFusingLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_PeiSocBistLogging3Lib_PeiSocBistLogging3Lib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_DxeCcxCoreTopologyServicesV3OnV2Lib_DxeCcxCoreTopologyServicesV3OnV2Lib_SUPPORT	1
#define SEC_AgesaModulePkg_Library_AmdEmulationAutoDetectPeiLib_AmdEmulationAutoDetectPeiLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_ApcbHmacChecksumLibV3_ApcbHmacChecksumLibV3_SUPPORT	1
#define DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspCommonLibDxe_AmdPspCommonLibDxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_Ras_Brh_DfAddressTranslateBrhLib_DfAddressTranslateBrhLib_SUPPORT	1
#define _AgesaModulePkg_Library_FabricRegisterAccDf4Lib_FabricRegisterAccDf4Lib_SUPPORT	1
#define _AgesaModulePkg_Library_FchDxeLib_FchDxeLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_AmdIdsHookLibPei_AmdIdsHookLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_AmdEmulationFlagLib_AmdEmulationFlagPeiLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_Ras_RasIdsPeiLib_RasIdsPeiLib_SUPPORT	1
#define _AgesaModulePkg_Library_AmdAcpiAmlLib_AmdAcpiAmlLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Nbio_Library_CommonPei_NbioCommonPeiLib_SUPPORT	1
#define DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdEmulationFlagLib_AmdEmulationFlagDxeSmmLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_DxeCcxSmmAccess2Lib_DxeCcxSmmAccess2Lib_SUPPORT	1
#define _AgesaModulePkg_Library_AmdPspHstiStateLib_AmdPspHstiStateLib_SUPPORT	1
#define _AgesaModulePkg_Library_AmdPspApobLib_AmdPspApobLib_SUPPORT	1
#define _AgesaModulePkg_Nbio_Library_IvrsDeviceDfltLib_IvrsDeviceDfltLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_AmdIdsHookLibDxe_AmdIdsHookLib_SUPPORT	1
#define _AgesaModulePkg_Library_BaseFabricTopologyBrhLib_BaseFabricTopologyBrhLib_SUPPORT	1
#define _AgesaModulePkg_Library_FchI3cLib_FchI3cLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_PeiCcxSmmAccessLib_PeiCcxSmmAccessLib_SUPPORT	1
#define _AgesaModulePkg_Library_AmdStbLib_AmdStbLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_CcxMpServicesDxeLib_CcxMpServicesDxeLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_AmdPspCommonLibDxe_AmdPspCommonLibDxe_SUPPORT	1
#define _AgesaModulePkg_Library_CcxPspLib_CcxPspLib_SUPPORT	1
#define _AgesaModulePkg_Library_AmdS3SaveLib_WOS3Save_AmdWOS3SaveLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_AmdTableLibV2_Pei_AmdTableHookPeiLibV2_SUPPORT	1
#define _AgesaModulePkg_Library_SocCmnIdsHookBrhLib_Pei_SocCmnIdsHookBrhLibPei_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_Ras_RasIdsDxeLib_RasIdsDxeLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_ApcbLibV3_ApcbLibV3_SUPPORT	1
#define DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspRomArmorLib_AmdPspRomArmorLib_SUPPORT	1
#define _AgesaModulePkg_Library_FabricResourceReportToGcdLib_FabricResourceReportToGcdLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_DxeSocLogicalIdServicesLib_DxeSocLogicalIdServicesLib_SUPPORT	1
#define _AgesaModulePkg_Library_SocCmnIdsHookBrhLib_Dxe_SocCmnIdsHookBrhLibDxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_AmdPspDxeSmmBufLib_AmdPspDxeSmmBufLib_SUPPORT	1
#define _AgesaModulePkg_Nbio_Library_CxlCedtLib_CxlCedtLib_SUPPORT	1
#define _AgesaModulePkg_Nbio_Library_MpioInitLib_MpioInitLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_IdsDxeLib_IdsDxeLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_AmdEmulationAutoDetectPeiLib_AmdEmulationAutoDetectPeiLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_AmdPspCommonLibPei_AmdPspCommonLibPei_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_AmdHeapPeiLib_AmdHeapPeiLib_SUPPORT	1
#define _AgesaModulePkg_Library_DxeFabricResourceManagerServicesLib_DxeFabricResourceManagerServicesLib_SUPPORT	1
#define _AgesaModulePkg_Library_AmdPspMmioLib_AmdPspMmioLib_SUPPORT	1
#define _AgesaModulePkg_Library_NbioRegisterAccLib_NbioRegisterAcc_SUPPORT	1
#define _AgesaModulePkg_Library_AmdPspMboxLibV2_AmdPspMboxLibV2_SUPPORT	1
#define _AgesaModulePkg_Nbio_Library_CxlConfigLib_CxlConfigLib_SUPPORT	1
#define _AgesaModulePkg_Library_AmdEmulationAutoDetectDxeLib_AmdEmulationAutoDetectDxeLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_CcxZen5BrhIdsHookLib_Dxe_CcxZen5BrhIdsHookLibDxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_AmdPspRomArmorLibNull_AmdPspRomArmorLibNull_SUPPORT	1
#define DXE_SMM_DRIVER_AgesaModulePkg_Library_Ras_Brh_DfAddressTranslateBrhLib_DfAddressTranslateBrhLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_AmdCapsuleLibDxe_AmdCapsuleLibDxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_AmdEmulationFlagLib_AmdEmulationFlagDxeSmmLib_SUPPORT	1
#define DXE_SMM_DRIVER_AgesaModulePkg_Library_ApcbHmacChecksumLibV3_ApcbHmacChecksumLibV3_SUPPORT	1
#define _AgesaModulePkg_Library_CcxMicrocodePatchLib_CcxMicrocodePatchLib_SUPPORT	1
#define _AgesaModulePkg_Library_CcxApicZen5Lib_CcxApicZen5Lib_SUPPORT	1
#define _AgesaModulePkg_Library_PeiFabricResourceManagerServicesLib_PeiFabricResourceManagerServicesLib_SUPPORT	1
#define _AgesaModulePkg_Library_NbioUtilLib_NbioUtilLib_SUPPORT	1
#define DXE_SMM_DRIVER_AgesaModulePkg_Library_PmMpDmaArsLib_Brh_PmMpDmaBrhArsLib_SUPPORT	1
#define _AgesaModulePkg_Library_GnbMemAccLib_GnbMemAccLib_SUPPORT	1
#define DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspRegMuxLibV2Dxe_AmdPspRegMuxLibV2_SUPPORT	1
#define DXE_SMM_DRIVER_AgesaModulePkg_Library_ApcbLibV3_ApcbLibV3_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_AmdPspRegMuxLibV2Dxe_AmdPspRegMuxLibV2_SUPPORT	1
#define DXE_SMM_DRIVER_AgesaModulePkg_Library_Ras_RasIdsSmmLib_Brh_RasIdsSmmLib_SUPPORT	1
#define DXE_SMM_DRIVER_AgesaModulePkg_Library_ApobCommonServiceLibDxe_ApobCommonServiceLibDxe_SUPPORT	1
#define DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdIdsHookLibDxe_AmdIdsHookLib_SUPPORT	1
#define PEI_CORE_AgesaModulePkg_Library_AmdEmulationAutoDetectPeiLib_AmdEmulationAutoDetectPeiLib_SUPPORT	1
#define _AgesaModulePkg_Library_CcxRolesX86Lib_CcxRolesX86Lib_SUPPORT	1
#define _AgesaModulePkg_Library_DxeCcxBaseX86ServicesLib_DxeCcxBaseX86ServicesLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_AmdPspBarInitLibV2_AmdPspBarInitLibV2_SUPPORT	1
#define _AgesaModulePkg_Library_AmdPspBaseLibV2_AmdPspBaseLibV2_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_DxeFabricResourceSizeForEachRbLib_DxeFabricResourceSizeForEachRbLib_SUPPORT	1
#define _AgesaModulePkg_Library_AmdCfgPcdBufLibNull_AmdCfgPcdBufLibNull_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Nbio_Library_IommuDmarLib_DXE_AmdIOMMUDmarLib_SUPPORT	1
#define _AgesaModulePkg_Library_PresiliconControlBrhLib_PresiliconControlBrhLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_AmdCfgPcdBufLibPei_AmdCfgPcdBufLibPei_SUPPORT	1
#define _AgesaModulePkg_Library_NbioServicesLib_Dxe_NbioServicesLibDxe_SUPPORT	1
#define _AgesaModulePkg_Library_CcxZen5BrhDxeLib_CcxZen5BrhDxeLib_SUPPORT	1
#define _AgesaModulePkg_Fch_Kunlun_FchKunlunCore_FchKunlunLibDxe_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_DxeCcxCppcLib_DxeCcxCppcLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_DxeCcxCcdReorderZen5Lib_DxeCcxCcdReorderZen5Lib_SUPPORT	1
#define _AgesaModulePkg_Library_AgesaConfigLib_AgesaConfigLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_IdsPeiLib_IdsPeiLib_SUPPORT	1
#define DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspFlashUpdateLib_AmdPspFlashUpdateLib_SUPPORT	1
#define _AgesaModulePkg_Library_Mem_BaseLib_AmdMemBaseLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_DxeCoreTopologyV3Lib_DxeCoreTopologyV3Lib_SUPPORT	1
#define _AgesaModulePkg_Library_IdsMiscLib_IdsMiscLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_FabricIdsHookBrhLib_Pei_FabricIdsHookBrhLibPei_SUPPORT	1
#define DXE_RUNTIME_DRIVER_AgesaModulePkg_Library_AmdPspRegMuxLibV2DxeRt_AmdPspRegMuxLibV2_SUPPORT	1
#define DXE_SMM_DRIVER_AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_SUPPORT	1
#define _AgesaModulePkg_Library_FchEspiCmdLib_FchEspiCmdLib_SUPPORT	1
#define _AgesaModulePkg_Library_PcieMiscCommLib_PcieMiscCommLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_GnbHeapDxeLib_GnbHeapDxeLib_SUPPORT	1
#define _AgesaModulePkg_Library_FchSocLib_Breithorn_FchBreithornLib_SUPPORT	1
#define _AgesaModulePkg_Nbio_Library_CommonDxe_NbioCommonDxeLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_AcpiTableHelperLib_AcpiTableHelperLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_ApcbTokenWhiteListNullLib_ApcbTokenWhiteListNullLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_ApcbTokenWhiteListLib_ApcbTokenWhiteListLib_SUPPORT	1
#define _AgesaModulePkg_Library_CcxHaltLib_CcxHaltLib_SUPPORT	1
#define _AgesaModulePkg_Library_SmnAccessLib_SmnAccessLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_FchIdsHookBrhLib_Dxe_FchIdsHookBrhLibDxe_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_Mem_BaseLib_AmdMemBaseLib_SUPPORT	1
#define DXE_RUNTIME_DRIVER_AgesaModulePkg_Library_FchDxeRuntimeResetSystemLib_KunLun_FchDxeRuntimeResetSystemLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_FchInitHookLib_FchInitHookLibPei_SUPPORT	1
#define _AgesaModulePkg_Library_AmdHeapLibNull_AmdHeapLibNull_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_ApcbLibV3Pei_ApcbLibV3Pei_SUPPORT	1
#define _AgesaModulePkg_Library_ApcbCoreLib_ApcbCoreLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Nbio_BRH_Library_NbioIdsHookBrhLib_Pei_NbioIdsHookBrhLibPei_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_Ras_RasAcpi63Lib_RasAcpi63Lib_SUPPORT	1
#define _AgesaModulePkg_Library_DxeFabricResourceSizeForEachRbLib_DxeFabricResourceSizeForEachRbLib_SUPPORT	1
#define DXE_SMM_DRIVER_AgesaModulePkg_Library_Ras_Brh_RasBrhSmmLib_RasBrhSmmLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_ApobCommonServiceLibPei_ApobCommonServiceLibPei_SUPPORT	1
#define _AgesaModulePkg_Library_FchBaseResetSystemLib_FchBaseResetSystemLib_SUPPORT	1
#define _AgesaModulePkg_Library_NbioSmuBrhLib_NbioSmuBrhLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Fch_Kunlun_FchKunlunCore_FchKunlunLibPei_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_AmdHeapDxeLib_AmdHeapDxeLib_SUPPORT	1
#define DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdTableLibV2_Dxe_AmdTableHookDxeLibV2_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_SocCoreInfo2AccessLib_SocCoreInfo2AccessLib_SUPPORT	1
#define _AgesaModulePkg_Nbio_Library_GetPcieResourcesLib_GetPcieResourcesLib_SUPPORT	1
#define PEI_CORE_AgesaModulePkg_Library_AmdCfgPcdBufLibPei_AmdCfgPcdBufLibPei_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_FabricIdsHookBrhLib_Dxe_FabricIdsHookBrhLibDxe_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_PeiSocLogicalIdServicesLib_PeiSocLogicalIdServicesLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Nbio_BRH_Library_NbioIdsHookBrhLib_Dxe_NbioIdsHookBrhLibDxe_SUPPORT	1
#define _AgesaModulePkg_Library_GnbCommonLib_GnbCommonLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_ApobCommonServiceLibDxe_ApobCommonServiceLibDxe_SUPPORT	1
#define DXE_SMM_DRIVER_AgesaModulePkg_Library_CcxZen5BrhIdsHookLib_Smm_CcxZen5BrhIdsHookLibSmm_SUPPORT	1
#define _AgesaModulePkg_Library_MemRestoreLib_MemRestoreLib_SUPPORT	1
#define _AgesaModulePkg_Library_ApobApcbLib_ApobApcbLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_ApcbVariableLibV3_ApcbVariableLibV3_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_PeiFabricResourceSizeForEachRbLib_PeiFabricResourceSizeForEachRbLib_SUPPORT	1
#define _AgesaModulePkg_Library_NbioHandleLib_NbioHandleLib_SUPPORT	1
#define _AgesaModulePkg_Nbio_Library_IvrsLibV3_IvrsLibV3_SUPPORT	1
#define _AgesaModulePkg_Library_GnbPciLib_GnbPciLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_FchIdsHookBrhLib_Pei_FchIdsHookBrhLibPei_SUPPORT	1
#define _AgesaModulePkg_Library_Ras_Brh_RasBrhSocLib_RasBrhSocLib_SUPPORT	1
#define _AgesaModulePkg_Library_FchSpiAccessLib_FchSpiAccessRom2Lib_SUPPORT	1
#define _AgesaModulePkg_Library_AmdPspFlashAccLibNull_AmdPspFlashAccLibNull_SUPPORT	1
#define _AgesaModulePkg_Library_GnbIoAccLib_GnbIoAccLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_CcxSetMmioCfgBaseLib_CcxSetMmioCfgBaseLib_SUPPORT	1
#define _AgesaModulePkg_Library_BaseCoreLogicalIdX86Lib_BaseCoreLogicalIdX86Lib_SUPPORT	1
#define _AgesaModulePkg_Library_AmdPspSfsLib_AmdPspSfsLib_SUPPORT	1
#define _AgesaModulePkg_Library_AmdPspRegBaseLib_AmdPspRegBaseLib_SUPPORT	1
#define DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdCfgPcdBufLibDxe_AmdCfgPcdBufLibDxe_SUPPORT	1
#define _AgesaModulePkg_Library_FchIdsHookLib_FchIdsHookLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Nbio_Library_PcieComplexDefaultsLib_PcieComplexDefaultsLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_FchPeiLib_FchPeiLib_SUPPORT	1
#define _AgesaModulePkg_Library_FchInitHookLib_FchInitHookLibDxe_SUPPORT	1
#define DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspMmioLibSmmIso_AmdPspMmioLib_SUPPORT	1
#define DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspMmioLib_AmdPspMmioLib_SUPPORT	1
#define _AgesaModulePkg_Library_PeiFabricTopologyServices2Lib_PeiFabricTopologyServices2Lib_SUPPORT	1
#define _AgesaModulePkg_Library_FchDxeLegacyInterruptLib_FchDxeLegacyInterruptLib_SUPPORT	1
#define _AgesaModulePkg_Library_CcxZen5SegRmpBrhLib_CcxZen5SegRmpBrhDxeLib_SUPPORT	1
#define _AgesaModulePkg_Library_IdsNonUefiLib_IdsNonUefiLib_SUPPORT	1
#define _AgesaModulePkg_Library_ApobBrhLib_ApobBrhLib_SUPPORT	1
#define _AgesaModulePkg_Library_AmdIdsDebugPrintLib_AmdIdsDebugPrintLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_SocCoreInfo2AccessLib_SocCoreInfo2AccessLib_SUPPORT	1
#define _AgesaModulePkg_Library_AmdRtclib_AmdRtcLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_AmdPspFlashAccLibDxe_AmdPspFlashAccLibDxe_SUPPORT	1
#define DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdCapsuleLibDxe_AmdCapsuleLibDxe_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_FabricResourceReportToGcdNullLib_FabricResourceReportToGcdNullLib_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_AmdTableLibV2_Dxe_AmdTableHookDxeLibV2_SUPPORT	1
#define DXE_DRIVER_AgesaModulePkg_Library_DxeAmlGenerationLib_AmlGenerationLib_SUPPORT	1
#define DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdHeapDxeLib_AmdHeapDxeLib_SUPPORT	1
#define SEC_AgesaModulePkg_Library_CcxNonSmmResumeSecLib_CcxNonSmmResumeSecLib_SUPPORT	1
#define _AgesaModulePkg_Nbio_Library_SmnTable_SmnTableLib_SUPPORT	1
#define _AgesaModulePkg_Nbio_Library_CxlMboxLib_CxlMboxLib_SUPPORT	1
#define _AgesaModulePkg_Library_FchBaseLib_FchBaseLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_CcxZen5BrhIdsHookLib_Pei_CcxZen5BrhIdsHookLibPei_SUPPORT	1
#define DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdPspDxeSmmBufLib_AmdPspDxeSmmBufLib_SUPPORT	1
#define _AgesaModulePkg_Library_BxbNbio_BxbNullLib_BxbNullLib_SUPPORT	1
#define PEIM_AgesaModulePkg_Library_PeiCoreTopologyV3Lib_PeiCoreTopologyV3Lib_SUPPORT	1
#define _AgesaModulePkg_Library_ApobDummyLib_ApobDummyLib_SUPPORT	1
#define AhciRecovery_SUPPORT	0
#define AmdCbsPkg_SUPPORT	1
#define PEIM_AmdCbsPkg_CbsBasePei_CbsBasePeiBRH_SUPPORT	1
#define PEIM_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsFuncLibPei_SUPPORT	1
#define PEIM_AmdCbsPkg_Build_ResourceBRH_CbsSetAgesaPcdLibBRH_SUPPORT	1
#define DXE_DRIVER_AmdCbsPkg_BctBaseSmm_BctBaseSmmBRH_SUPPORT	1
#define DXE_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsBctSmmLib_SUPPORT	1
#define DXE_DRIVER_AmdCbsPkg_CbsSetupDxe_CbsSetupDxeBRH_SUPPORT	1
#define DXE_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsFuncLibDxe_SUPPORT	1
#define DXE_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsSetupLib_SUPPORT	1
#define DXE_DRIVER_AmdCbsPkg_CbsBaseDxe_CbsBaseDxeBRH_SUPPORT	1
#define DXE_DRIVER_AmdCbsPkg_CbsSetupSmm_CbsSetupSmmBRH_SUPPORT	1
#define DXE_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsUpdateApcbLib_SUPPORT	1
#define DXE_SMM_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsUpdateApcbLib_SUPPORT	1
#define DXE_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsIdsLibDxe_SUPPORT	1
#define DXE_SMM_DRIVER_AmdCbsPkg_Library_CbsSmmCommLib_CbsSmmCommLib_SUPPORT	1
#define DXE_DRIVER_AmdCbsPkg_Library_CbsSmmCommLib_CbsSmmCommLib_SUPPORT	1
#define DXE_SMM_DRIVER_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsBctSmmLib_SUPPORT	1
#define PEIM_AmdCbsPkg_Library_Family_0x1A_BRH_External_CbsIdsLibPei_SUPPORT	1
#define AmdCpmPkg_SUPPORT	1
#define PEIM_AmdCpmPkg_Addendum_Oem_Quartz_Pei_AmdCpmOemInitPei_AmdCpmOemInitPeimQuartz_SUPPORT	1
#define PEIM_AmdCpmPkg_Addendum_Oem_Quartz_Pei_PlatformCustomizePei_PlatformCustomizePei_SUPPORT	1
#define PEIM_AmdCpmPkg_Devices_Ds125Br401a_Pei_Ds125Br401aPei_SUPPORT	1
#define PEIM_AmdCpmPkg_Devices_M24LC128_Pei_M24Lc128Pei_SUPPORT	1
#define PEIM_AmdCpmPkg_Devices_Pca9535a_Pei_Pca9535aPei_SUPPORT	1
#define PEIM_AmdCpmPkg_Devices_Pca9545a_Pei_Pca9545aPei_SUPPORT	1
#define PEIM_AmdCpmPkg_Devices_Sff8472_Pei_Sff8472Pei_SUPPORT	1
#define PEIM_AmdCpmPkg_Devices_Tca9548a_Pei_Tca9548aPei_SUPPORT	1
#define PEIM_AmdCpmPkg_Devices_M24LC256_Pei_M24Lc256Pei_SUPPORT	1
#define PEIM_AmdCpmPkg_Devices_Pca9536_Pei_Pca9536Pei_SUPPORT	1
#define PEIM_AmdCpmPkg_Devices_Pca6107_Pei_Pca6107Pei_SUPPORT	1
#define PEIM_AmdCpmPkg_Features_BoardId_Pei_AmdBoardIdPei_SUPPORT	1
#define PEIM_AmdCpmPkg_Features_GpioInit_Pei_AmdCpmGpioInitPeim_SUPPORT	1
#define PEIM_AmdCpmPkg_Features_LpcUart_Pei_AmdCpmLpcUartPeim_SUPPORT	1
#define PEIM_AmdCpmPkg_Features_PcieInit_Pei_AmdCpmPcieInitPeim_SUPPORT	1
#define PEIM_AmdCpmPkg_Features_PlatformRas_Brh_Pei_AmdPlatformRasBrhPei_SUPPORT	1
#define PEIM_AmdCpmPkg_Kernel_Pei_AmdCpmInitPeim_SUPPORT	1
#define PEIM_AmdCpmPkg_Features_FabricTopologyDump_FabricTopologyDump_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Quartz_Dxe_ServerHotplugDxe_ServerHotplugDxe_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Quartz_Dxe_RasOemDimmMap_RasOemDimmMap_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Kernel_Asl_AmdCpmInitAsl_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Kernel_Dxe_AmdCpmInitDxe_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Kernel_Smm_AmdCpmInitSmm_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_BoardId_Dxe_AmdBoardIdDxe_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_PcieInit_Asl_AmdCpmPcieInitAsl_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_PcieInit_Dxe_AmdCpmPcieInitDxe_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_GpioInit_Dxe_AmdCpmGpioInitDxe_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_GpioInit_Smm_AmdCpmGpioInitSmm_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_PlatformRas_Brh_Dxe_AmdPlatformRasBrhDxe_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_PlatformRas_Brh_Smm_AmdPlatformRasBrhSmm_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_PlatformRas_Brh_Asl_PlatformRasBrhAsl_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_AmdCdmaDataInit_Brh_Dxe_AmdCdmaDsmDxe_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_AmdCdmaDataInit_Asl_AmdCdmaAsl_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_MPDMA_BRH_Dxe_MPDMABrhDxe_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_MPDMA_BRH_Asl_MpDmaBrhAsl_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_HSMP_BRH_Dxe_HsmpBrhDxe_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_HSMP_BRH_Asl_HsmpBrhAsl_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_xGbEI2cMaster_xGbEI2cMasterDxe_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Devices_Pca9535a_Dxe_Pca9535aDxe_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Devices_Pca9545a_Dxe_Pca9545aDxe_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Devices_Sff8472_Dxe_Sff8472Dxe_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Devices_Tca9548a_Dxe_Tca9548aDxe_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Devices_Pca9536_Dxe_Pca9536Dxe_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Devices_Pca6107_Dxe_Pca6107Dxe_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_CF9IoTrap_BRH_CF9IoTrapBrh_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_AcpiI3cSlaveSsdt_AcpiI3cSlaveSsdt_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Quartz_Dxe_AcpiI3cSlave_ASL_AcpiI3cSlaveAsl_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_SysTopologyReport_Dxe_SysTopologyReportDxe_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_AmdBctPkg_BiosCfgToolDxe_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_AmdBctPkg_BiosCfgToolSmm_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Devices_GenericCxl_CxlEndpointDriver_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Quartz_Dxe_AmdPbsSetupDxe_AmdPbsSetupDxe_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_ApicInfoData_BRH_Dxe_ApicInfoDataDxe_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_HotPlug_Brh_Smm_AmdHotPlugBrhSmm_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_LegacyInterrupt_Dxe_LegacyInterruptDxe_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Quartz_Dxe_DeferredPsbFuse_DeferredPsbFuseDxe_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Addendum_Oem_OobPprDxe_OobPprDxe_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_AmdPrm_PrmModule_PrmAddressTranslateModule_Library_Brh_DxeAddressTranslateModuleConfigLib_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_AmdPrm_PrmConfigDxe_PrmConfigDxe_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_AmdPrm_PrmModule_PrmAddressTranslateModule_Brh_PrmAddressTranslateModule_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_AmdVariableProtection_AmdVariableProtection_SUPPORT	0
#define _AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasAcpi63BrhLib_SUPPORT	1
#define _AmdCpmPkg_Library_Proc_Cpu_AmdCpmCpu_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Quartz_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_SUPPORT	1
#define _AmdCpmPkg_Features_MPDMA_MpdmaIvrsLib_MpdmaIvrsLib_SUPPORT	1
#define _AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasCxlBrhLib_CpmRasCxlBrhLib_SUPPORT	1
#define PEIM_AmdCpmPkg_Addendum_Oem_Quartz_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Quartz_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_SUPPORT	1
#define DXE_SMM_DRIVER_AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasProcBrhLib_CpmRasProcSmmBrhLib_SUPPORT	1
#define _AmdCpmPkg_Library_AmdCxlPcieLib_AmdCxlPcieLib_SUPPORT	1
#define _AmdCpmPkg_Library_Proc_Base_AmdCpmBaseLib_SUPPORT	1
#define _AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasBrhLib_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasProcBrhLib_CpmRasProcDxeBrhLib_SUPPORT	1
#define _AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasMemBrhLib_CpmRasMemBrhLib_SUPPORT	1
#define _AmdCpmPkg_Library_Proc_Fch_AmdCpmFch_SUPPORT	1
#define _AmdCpmPkg_Addendum_Oem_Quartz_Library_OemGpioResetControlLib_OemGpioResetControlLib_SUPPORT	1
#define _AmdCpmPkg_Library_AmdCpmCxlMboxLib_AmdCpmCxlMboxLib_SUPPORT	1
#define _AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasPciBrhLib_SUPPORT	1
#define DXE_DRIVER_AmdCpmPkg_Features_Xgmi_XgmiFreqDxe_SUPPORT	0
#define Setup_SUPPORT	1
#define SETUP_DRIVER_VERSION	0x17
#define ALWAYS_PUBLISH_HII_RESOURCES	1
#define SHOW_ADVANCED_FORMSET	1
#define SHOW_CHIPSET_FORMSET	1
#define SHOW_SECURITY_FORMSET	1
#define SHOW_BOOT_FORMSET	1
#define SETUP_PASSWORD_LENGTH	0x14
#define DEFAULT_NUMLOCK_STATE	1
#define EXTERNAL_SHELL_SUPPORT	0
#define PASSWORD_MAX_SIZE	0x14
#define PASSWORD_MIN_SIZE	0x3
#define DEFAULT_LANGUAGE_CODE	en-US
#define FILTER_CUSTOM_LANGUAGE_CODES	1
#define AMI_CRYPTOPACKAGE_MODULE_REVISION	0x36
#define AmiCryptoPkg_DEBUG_LEVEL	EFI_D_INFO
#define E_CONST	0x01, 0x00, 0x01
#define PSS_SIG_SALTLEN	0x8
#define PKCS7_MUTEX_LOCK	0
#define FWKEY_FV_HEADER_GUID	{ 0x61C0F511, 0xA691, 0x4F54, { 0x97, 0x4F, 0xB9, 0xA4, 0x21, 0x72, 0xCE, 0x53 }}
#define CR_DXE_MAX_HEAP_SIZE	0x10000
#define CR_PEI_MAX_HEAP_SIZE	0xC000
#define CRYPTO_trace_level	0x1
#define SHA256_FAST	0x0
#define CONFIG_PEI_PKCS7	0x1
#define CONFIG_X509_EXT_ALT_NAME_KEY_USAGE	0x0
#define CONFIG_X509_CERTIFICATE_EXPIRATION	0x0
#define CONFIG_PKCS7_MsSpcNestedSignature	0x0
#define ASN1_BASE64_DECODE	0x0
#define AMI_HSTI_PKG_VERSION	0x9
#define HSTI_REVISION	0x0
#define AmiIpmi2Pkg_SUPPORT	1
#define AMI_IPMI2_PKG_VERSION	0x13
#define AmiKcsInterface_SUPPORT	1
#define IPMI2_SUPPORT	1
#define IPMI2_VERSION	0x13
#define USE_PLATFORM_EVENT_MESSAGE	0
#define DEFAULT_BMC_SUPPORT	0x1
#define DEFAULT_WAITFORBMC_POLICY	0x0
#define IPMI_SSIF_SLAVE_ADDRESS	0x10
#define IPMI_KCS_BASE_ADDRESS	0xca2
#define IPMI_SMM_KCS_BASE_ADDRESS	0xca2
#define IPMI_KCS_DATA_PORT	0xca2
#define IPMI_SMM_KCS_DATA_PORT	0xca2
#define IPMI_KCS_COMMAND_PORT	0xca3
#define IPMI_SMM_KCS_COMMAND_PORT	0xca3
#define IPMI_BT_BASE_ADDRESS	0xe4
#define IPMI_BT_CTRL_PORT	0xe4
#define IPMI_BT_BUFFER_PORT	0xe5
#define IPMI_BT_INTERRUPT_MASK_PORT	0xe6
#define IPMI_BT_COMMAND_PORT_READ_RETRY_COUNTER	0x4e400
#define IPMI_BT_DELAY_PER_RETRY	0xf
#define IPMI_BT_BUFFER_SIZE	0x40
#define BMC_ADDRESS_DECODE_MASK	0xc
#define OEM_SEL_SUPPORT	1
#define BMC_TIMEZONE_SUPPORT	0
#define BMC_WARM_RESET_SUPPORT	1
#define SRVV	0x200
#define BMC_USB_INTERFACE_VENDOR_ID	0x46b
#define BMC_USB_INTERFACE_PRODUCT_ID	0xff20
#define IPMI_BMC_SLAVE_ADDRESS	0x20
#define IPMI_KCS_COMMAND_PORT_READ_RETRY_COUNTER	0xc350
#define IPMI_SSIF_COMMAND_REQUEST_RETRY_COUNTER	0x5
#define COLLECT_BMC_USER_DETAILS_FOR_EVERY_BMC_USER_SETTING_CALLBACK	0
#define IPMI_BOOT_OVERRIDE	0
#define COMMAND_SPECIFIC_COUNTER_UPDATE_SUPPORT	0
#define IPMI_SSIF_WAIT_RESPONSE_DATA_DELAY	0x3d090
#define IPMI_SSIF_TIME_BETWEEN_REQUEST_RETRIES_DELAY	0x3d090
#define IPMI_DEFAULT_ACCESS_TYPE	1
#define DEFAULT_SYSTEM_INTERFACE	0x1
#define EFI_IPMI_PROTOCOL_REVISION	0x101
#define IpmiInitialize_SUPPORT	1
#define BMC_INIT_FIXED_DELAY	0x1c9c380
#define IPMI_CMOS_CLEAR_RESET_TYPE	1
#define IPMI_BMC_TIME_SYNC_TO_BIOS_TIME_SUPPORT	1
#define BMC_USB_DRIVER_BINDING_SUPPORT	0x1
#define IPMI_SMBIOS_TYPE38_SUPPORT	1
#define IPMI_SMBIOS_TYPE42_SUPPORT	1
#define IPMI_SMBIOS_TYPE45_SUPPORT	1
#define Frb_SUPPORT	1
#define DxeFrb_SUPPORT	1
#define DEFAULT_OSBOOTWDT_TIMEOUT	0xa
#define SelStatusCode_SUPPORT	1
#define DxeSelStatusCode_SUPPORT	1
#define DxeBmcElog_SUPPORT	1
#define IANA_PEN_IPMI_FORUM	0x1bf2
#define IANA_PEN_INTEL	0x157
#define FRU_DEVICE_ID	0xff
#define CUSTOM_FRU_ID_SUPPORT	0x0
#define MULTIPLE_CUSTOM_FRU_ID_SUPPORT	0x0
#define CUSTOM_FRU_ID	0x0
#define CUSTOM_FRU_DATA_SIZE	0x10
#define FRU_DEVICE_INFO_TO_SETUP	0x2
#define BMC_ACPI_SWSMI	0xc0
#define BMC_ACPI_SWSMI_MAX	0xc6
#define BSMI	0xc0
#define BMC_LAN_COUNT	0x2
#define IPV6_SUPPORT_CONTROL_BY_BIOS	0
#define ServerMgmtSetup_SUPPORT	1
#define SERVER_MGMT_SETUP_DATA_LAYOUT_OVERRIDE_SUPPORT	0
#define SEL_MAX_ERROR_MANAGER_SIZE	0xd000
#define BMC_USER_PASSWORD_MIN_SIZE	0x1
#define BMC_USER_PASSWORD_MAX_SIZE	0x14
#define BMC_USER_PASSWORD_MAX_SIZE_WITH_NULL	0x15
#define IPMI_BMC_DELETE_USER_DATA	0xff
#define IPMI_BMC_USER_SETTINGS_WITHOUT_PASSWORD	0
#define PeiIpmiCmosClear_SUPPORT	1
#define MmIpmiCmosClear_SUPPORT	0
#define ACPI_SUPPORT	1
#define INSTALL_FLOATING_POINTER	0
#define ACPI_MODULE_VER	0x154
#define ACPI_DBG8_ASL_SUPPORT	1
#define S3_MEMORY_SIZE_PER_CPU	0x1000
#define ATAD_SUPPORT	0
#define ACPI_SPEC_VERSION	0x41
#define ACPI_ERRATA	0x0
#define ACPI_BUILD_TABLES_2_0	1
#define ACPI_ARM_BOOT_ARCH	0x0
#define ACPI_APIC_TBL	1
#define UPDATE_WSMT	1
#define FACP_BUILD	1
#define DSDT_BUILD	1
#define FACS_BUILD	1
#define NMIs_QUANTITY	0x0
#define LOCAL_APIC_VERSION_PARAMETER	0x21
#define LAPIC_QUANTITY	0x2
#define LAPIC_0_INT_TYPE	0x3
#define LAPIC_0_POLARITY	0x1
#define LAPIC_0_TRIGGER_MODE	0x1
#define LAPIC_0_SOURCE_BUS_ID	0x0
#define LAPIC_0_SOURCE_BUS_IRQ	0x0
#define LAPIC_0_DEST_ID	0xff
#define LAPIC_0_DEST_LINTIN	0x0
#define LAPIC_1_INT_TYPE	0x1
#define LAPIC_1_POLARITY	0x1
#define LAPIC_1_TRIGGER_MODE	0x1
#define LAPIC_1_SOURCE_BUS_ID	0x0
#define LAPIC_1_SOURCE_BUS_IRQ	0x0
#define LAPIC_1_DEST_ID	0xff
#define LAPIC_1_DEST_LINTIN	0x1
#define IRQ_00_OVERRIDE_ENABLE	1
#define IRQ_00_APIC_INT	0x2
#define IRQ_00_POLARITY	0x0
#define IRQ_00_TRIGGER_MODE	0x0
#define IRQ_01_OVERRIDE_ENABLE	0
#define IRQ_03_OVERRIDE_ENABLE	0
#define IRQ_04_OVERRIDE_ENABLE	0
#define IRQ_05_OVERRIDE_ENABLE	0
#define IRQ_06_OVERRIDE_ENABLE	0
#define IRQ_07_OVERRIDE_ENABLE	0
#define IRQ_08_OVERRIDE_ENABLE	0
#define IRQ_09_OVERRIDE_ENABLE	1
#define IRQ_09_APIC_INT	0x9
#define IRQ_09_TRIGGER_MODE	0x3
#define IRQ_10_OVERRIDE_ENABLE	0
#define IRQ_11_OVERRIDE_ENABLE	0
#define IRQ_12_OVERRIDE_ENABLE	0
#define IRQ_13_OVERRIDE_ENABLE	0
#define IRQ_14_OVERRIDE_ENABLE	0
#define IRQ_15_OVERRIDE_ENABLE	0
#define PCI_BUS_APIC_AUTODETECT	0
#define SW_SMI_S4BIOS	0x0
#define ACPI_RSDT_TABLE_NUM	0x10
#define ACPI_APIC_FLAGS	0x1
#define ACPI_IA_BOOT_ARCH	0x3
#define P_LVL3_LAT_VAL	0x3e9
#define FLUSH_SIZE_VAL	0x400
#define FLUSH_STRIDE_VAL	0x10
#define DUTY_OFFSET_VAL	0x1
#define FACS_FLAG_S4BIOS	0x0
#define FACS_FLAG_64BIT_WAKE_SUPPORTED	0x0
#define DEFAULT_SS4	0x0
#define DEFAULT_ACPI_LOCK_LEGACY_DEV	0x0
#define DEFAULT_SS3	0x0
#define DEFAULT_SS2	0x0
#define DEFAULT_SS1	0x0
#define DEFAULT_AUTO_ACPI	0x0
#define S3_VIDEO_REPOST_SUPPORT	0
#define LOCK_LEGACY_RES_SETUP_ENABLE	1
#define FACP_FLAG_WBINVD	1
#define FACP_FLAG_WBINVD_FLUSH	0
#define FACP_FLAG_PROC_C1	1
#define FACP_FLAG_PWR_BUTTON	0
#define FACP_FLAG_SLP_BUTTON	1
#define FACP_FLAG_FIX_RTC	0
#define FACP_FLAG_DCK_CAP	0
#define FACP_FLAG_RESET_REG_SUP	1
#define FACP_FLAG_SEALED_CASE	0
#define FACP_FLAG_HEADLESS	0
#define FACP_FLAG_CPU_SW_SLP	0
#define FACP_FLAG_PCI_EXP_WAK	0
#define FACP_FLAG_REMOTE_POWER_ON_CAPABLE	0x1
#define FACP_FLAG_FORCE_APIC_CLUSTER_MODEL	0
#define FACP_FLAG_FORCE_APIC_PHYSICAL_DESTINATION_MODE	0
#define HW_REDUCED_ACPI	0
#define LOW_POWER_S0_IDLE_CAPABLE	0
#define FACP_FLAG_PERSISTENT_CPU_CACHES	0x0
#define GTDT_BUILD	0
#define ARM_MADT_BUILD	0
#define ACPI_THUNK_REAL_MODE_SEGMENT	0x1000
#define ACPI_THUNK_STACK_TOP	0x1000
#define IFDEF_ASL_SUPPORT	1
#define PM1A_EVT_BLK_ACCESS_SIZE	0x2
#define PM1A_CNT_BLK_ACCESS_SIZE	0x2
#define PM1B_EVT_BLK_ACCESS_SIZE	0x2
#define PM1B_CNT_BLK_ACCESS_SIZE	0x2
#define PM2_CNT_BLK_ACCESS_SIZE	0x1
#define PM_TMR_BLK_ACCESS_SIZE	0x3
#define GPE0_BLK_ACCESS_SIZE	0x1
#define GPE1_BLK_ACCESS_SIZE	0x1
#define AmiRomConcat_SUPPORT	0
#define DEBUG_PORT_TABLES_SUPPORT	1
#define DBGP_INTERFACE_TYPE	0x0
#define DBGP_ADDR_SPC_ID	0x1
#define DBGP_ADDR_BIT_WIDTH	0x8
#define DBGP_ADDR_BIT_OFFSET	0x0
#define DBGP_ACCESS_SIZE	0x0
#define SERIAL_DEV_INFO_SUPPORT	1
#define SERIAL_GENERIC_ADDR_SIZE	0x20
#define SERIAL_NUM_OF_GENERIC_ADDR_REG	0x1
#define SERIAL_NAME_SPACE	"."
#define SERIAL_DBG2_ADDR_SPC_ID	0x1
#define SERIAL_DBG2_ADDR_BIT_WIDTH	0x8
#define SERIAL_DBG2_ADDR_BIT_OFFSET	0x0
#define SERIAL_DBG2_ACCESS_SIZE	0x0
#define EHCI_DEV_INFO_SUPPORT	1
#define EHCI_GENERIC_ADDR_SIZE	0x20
#define EHCI_NUM_OF_GENERIC_ADDR_REG	0x1
#define EHCI_SEG_BUS_DEV_FUN	{0x00, 0x00, 0x1D, 0x00}
#define EHCI_USB_NAME_SPACE	"."
#define EHCIPORT0_DBG2_ADDR_SPC_ID	0x0
#define EHCIPORT0_DBG2_ADDR_BIT_WIDTH	0x8
#define EHCIPORT0_DBG2_ADDR_BIT_OFFSET	0x0
#define EHCIPORT0_DBG2_ACCESS_SIZE	0x0
#define XHCI_DEV_INFO_SUPPORT	1
#define XHCI_GENERIC_ADDR_SIZE	0xc
#define XHCI_NUM_OF_GENERIC_ADDR_REG	0x1
#define XHCI_SEG_BUS_DEV_FUN	{0x00, 0x00, 0x14, 0x00}
#define XHCI_USB_NAME_SPACE	"."
#define XHCIPORT0_DBG2_ADDR_SPC_ID	0x0
#define XHCIPORT0_DBG2_ADDR_BIT_WIDTH	0x8
#define XHCIPORT0_DBG2_ADDR_BIT_OFFSET	0x0
#define XHCIPORT0_DBG2_ACCESS_SIZE	0x0
#define DBG_ACPI_OEM_ID	"COMPAL"
#define DBG_ACPI_OEM_TBL_ID	"AMI"
#define EarlyTextConsole_SUPPORT	1
#define EarlyGraphicsConsole_SUPPORT	1
#define INFO_MESSAGE_BUFFER_SIZE	0x1f4
#define DEBUG_MESSAGE_BUFFER_SIZE	0x1f4
#define EARLY_GRAPHICS_FONT_FFS_FILE_GUID	{0xDAC2B117, 0xB5FB, 0x4964,{0xA3, 0x12, 0xD, 0xCC, 0x77, 0x6, 0x1B, 0x9B}}
#define PEI_GRAPHICS_VBT_FILE_FFS_GUID	{0x7E175642, 0xF3AD, 0x490A, {0x9F, 0x8A, 0x2E, 0x9F, 0xC6, 0x93, 0x3D, 0xDD}}
#define PEI_GRAPHICS_BMP_LOGO_FFS_GUID	{0x7BB28B99, 0x61BB, 0x11D5, {0x9A, 0x5D, 0x00, 0x90, 0x27, 0x3F, 0xC1,0x4D}}
#define PEI_GRAPHICS_BMP_BG_IMAGE_FFS_GUID	{0x31a89c3b, 0x3adb, 0x44dc, {0xaf, 0x6e, 0xd7, 0x40, 0x94, 0x2d, 0x66, 0x45}}
#define SYSTEM_INFO_GUID_PACKAGE_DEC	 
#define SYSTEM_INFO_CALLBACK_GUID	gEfiPeiMemoryDiscoveredPpiGuid
#define SerialTextConsole_SUPPORT	1
#define VideoTextConsole_SUPPORT	1
#define PEI_VIDEO_CALLBACK_PACKAGE_DEC	 
#define PEI_VIDEO_CALLBACK_GUID	gEfiPeiMemoryDiscoveredPpiGuid
#define VideoGraphicsConsole_SUPPORT	1
#define EDK_II_GOP_DRIVER_FFS_GUID	{0x20830080, 0xCC28, 0x4169, {0x98, 0x36, 0x7F, 0x42, 0xB8, 0xD0, 0xC8, 0xC9}}
#define EARLY_GRAPHICS_BACKGROUND_COLOR	{0, 0, 0, 0}
#define RemoveLegacyGptHddDevice	0
#define RemoveBootOptionWithoutFile	0
#define DISPLAY_FULL_OPTION_NAME_WITH_FBO	1
#define NEW_UEFI_OS_OPTION_ORDER_POLICY	0x0
#define CREATE_BOOT_OPTION_WITH_UEFI_FILE_NAME_POLICY	1
#define NAME_OF_UEFI_OS	L"UEFI OS"
#define CREATE_EFI_OS_BOOT_OPTIONS_FUNC_PTR	CreateEfiOsBootOptions
#define CREATE_TARGET_EFI_OS_BOOT_OPTION_FUNC_PTR	CreateTargetEfiOsBootOption
#define DefaultFwBootOption	1
#define KeepDuplicateNonFWBootOption	0
#define NewUefiOsOptionOrderPolicySetupControl	0
#define EOBON_SKIP_REMOVABLE_DEVICE	1
#define BUILD_UP_BOOT_OPTION_FOR_LINUX_SYSTEM	1
#define CHECK_FILE_TIME_STAMP	0
#define POST_TIME_TUNING_SUPPORT	1
#define FAST_BOOT_SUPPORT	1
#define PTT_VER	0x13
#define MAX_SATA_DEVICE_COUNT	0x8
#define SKIP_TSE_HANDSHAKE	0
#define ALLOW_FIRST_FASTBOOT_IN_S4	1
#define USB_SKIP_TABLE	{{0, 0, 0, 0, 0, 0}}
#define LAST_BOOT_FAIL_MECHANISM	1
#define OVERRIDE_FastBootLaunch	1
#define DEFAULT_FAST_BOOT	0
#define DEFAULT_VGA_SUPPORT_SETTING	1
#define DEFAULT_PS2_SUPPORT_SETTING	1
#define DEFAULT_I2C_SUPPORT_SETTING	1
#define DEFAULT_REDIRECTION_SUPPORT_SETTING	0
#define DEFAULT_NETWORK_STACK_SUPPORT_SETTING	0
#define DEFAULT_NVME_SUPPORT_SETTING	1
#define DEFAULT_UFS_SUPPORT_SETTING	1
#define CONNECT_EVERYTHING_IN_FASTBOOT	1
#define SINGAL_ALL_DRIVERS_CONNECTED_EVENT	1
#define CALL_DISPATCHER_AGAIN_IN_FASTBOOT	1
#define IS_VALID_FASTBOOT_BOOT_OPTION_FUNC	IsValidFastBootOption
#define SUPPORT_RAID_DRIVER	0
#define FASTBOOT_NEED_RESTART	0
#define SKIP_USB_STORAGE	1
#define FASTBOOT_WAIT_KEYBOARD_INSTALL_DELAY	0
#define HOTKEY_DELAY_DEFAULT	0x0
#define FAST_BOOT_SMI_SUPPORT	0
#define FastBootOption_SUPPORT	1
#define IS_CORRECT_BOOT_OPTION_FUNCTION	IsCorrectBootoption
#define FmpCapsuleUpdate_SUPPORT	0
#define GenericSio_SUPPORT	0
#define HARDWARE_SIGNATURE_MANAGEMENT_SUPPORT	1
#define HARDWARE_SIGNATURE_USB_CHANGE	0
#define HARDWARE_SIGNATURE_DEBUG_MESSAGES	0
#define THRESHOLD_OF_DIFFERENT_MEMORY_SIZE	0x80
#define LEGACYSREDIR_SUPPORT	1
#define LEGACY_SREDIR_VERSION	0x13
#define SREDIR_CODE_DATA_SELECTION	0x0
#define DISPLAY_WHOLE_SCREEN	0
#define TRAP_INT10_WORKAROUND	0
#define CHECKLOOPBACK_RETRY_COUNT	0x32
#define SERIAL_READ_WRITE_CALLBACK	0
#define LEGACY_SREDIR_SWSMI	0x41
#define CTRLI_KEY_MAPPING	1
#define CTRLH_KEY_MAPPING	1
#define CHECK_FOR_LOOPBACK_DEVICE	0
#define CLEAR_LEGACYSREDIR_KB_BUFFER_AT_READYTOBOOT	0
#define UART_POLLING_REDIRECTION	0
#define LEGACYSMMSREDIR_ACPI_PACKAGES	AmiChipsetPkg/AmiChipsetPkg.dec
#define OemActivation_eModule_SUPPORT	1
#define OEM_ACTIVATION_VERSION	0x17
#define OA1_SUPPORT	0
#define OA2_SUPPORT	0
#define OA3_SUPPORT	1
#define OA3_SMM_SUPPORT	1
#define OemActivation_SUPPORT	1
#define SW_SMI_OA3_FUNCTION_NUMBER	0xdf
#define AmiPciRootBridgeDxe_SUPPORT	1
#define PCI_BUS_MAJOR_VER	0xa5
#define PCI_BUS_MINOR_VER	0x1
#define PCI_BUS_REVISION	0x20
#define PCI_BUS_VER_COMBINED	0x409e
#define HOTPLUG_APPLY_PADDING_ANYWAY	0
#define HOTPLUG_SETUP_DEFAULT_VALUE	1
#define PCIE_GEN1_ENABLE_SETUP_DEFAULT_VALUE	0
#define PCIE_GEN2_ENABLE_SETUP_DEFAULT_VALUE	0
#define VGA_PALLETE_SNOOP_SETUP_DEFAULT_VALUE	0
#define PERR_SETUP_DEFAULT_VALUE	0
#define SERR_SETUP_DEFAULT_VALUE	0
#define RELAXED_ORDERING_SETUP_DEFAULT_VALUE	1
#define EXTENDED_TAG_FIELD_SETUP_DEFAULT_VALUE	0
#define NO_SNOOP_SETUP_DEFAULT_VALUE	1
#define EXTENDED_SYNC_SETUP_DEFAULT_VALUE	0
#define CLOCK_PM_SETUP_DEFAULT_VALUE	0
#define PCIE_LINK_ENABLE_SETUP_DEFAULT_VALUE	0
#define ARI_FORWARDING_SETUP_DEFAULT_VALUE	0
#define ATOMIC_OPERATION_SETUP_DEFAULT_VALUE	0
#define ATOMIC_OPERATION_EGRESS_SETUP_DEFAULT_VALUE	0
#define IDO_REQ_SETUP_DEFAULT_VALUE	0
#define IDO_COMPLETION_SETUP_DEFAULT_VALUE	0
#define LTR_REPORTING_SETUP_DEFAULT_VALUE	0
#define E2E_TLP_PREFIX_BLOCK_SETUP_DEFAULT_VALUE	0
#define COMPLIANCE_SOS_SETUP_DEFAULT_VALUE	0
#define HW_AUTO_WIDTH_SETUP_DEFAULT_VALUE	0
#define HW_AUTO_SPEED_SETUP_DEFAULT_VALUE	0
#define PCI_EXPRESS_SUPPORT	1
#define PCI_EXPRESS_GEN2_SUPPORT	1
#define PCI_EXPRESS_GEN3_SUPPORT	1
#define PCI_EXPRESS_GEN4_SUPPORT	1
#define PCI_EXPRESS_GEN5_SUPPORT	1
#define PCIE_LINK_TRAINING_POLLING_COUNT	0x1f4
#define PCI_FIXED_BUS_ASSIGNMENT	0
#define PCI_DEV_REVERSE_SCAN_ORDER	0
#define SRIOV_SUPPORT	1
#define PCI_4K_RESOURCE_ALIGNMENT	1
#define PCI_DEVICE_IO_RESOURCE_THRESHOLD	0xffff
#define PCI_DEVICE_32BIT_RESOURCE_THRESHOLD	0xffffffff
#define PCI_CARD_BUS_BRG_MMIO_PADDING_LEN	0x2000000
#define PCI_CARD_BUS_BRG_MMIO_PADDING_ALN	0x1ffffff
#define PCI_CARD_BUS_BRG_IO_PADDING_LEN	0x1000
#define PCI_CARD_BUS_BRG_IO_PADDING_ALN	0xfff
#define ABOVE_4G_PCI_DECODE	1
#define SETUP_SHOW_ABOVE_4G_DECODE	1
#define PCI_BME_DMA_MITIGATION_FOR_BRIDGES	1
#define PCI_BME_DMA_MITIGATION_DEFAULT_VALUE	0
#define PCI_DO_NOT_RESET_VC_MAPPING	0
#define SETUP_SHOW_PCI_BME_DMA_MITIGATION_OPTION	1
#define RESIZABLE_BAR_SUPPORT	0
#define RESIZABLE_BAR_SUPPORT_DEFAULT_VALUE	0
#define RT8111_ON_BOARD_BAD_ROM_BAR	0
#define SETUP_SHOW_SRIOV_SUPPORT	1
#define SETUP_SHOW_RESET_VC_MAPPING	0
#define SETUP_SHOW_PCIE_FORM	1
#define SETUP_SHOW_PCIE2_FORM	1
#define SETUP_SHOW_HOTPLUG_FORM	0
#define PREFETCHABLE_MMIO_CACHE_POLICY	0x4
#define PCI_MMIO_RES_TOP_ALLIGN	0
#define PCI_AMI_COMBINE_MEM_PMEM32	0
#define PCI_AMI_COMBINE_MEM64_PMEM	0
#define PCI_IO32_SUPPORT	0
#define PCI_T_RST	0x3e8
#define PCI_T_RST_RECOVERY	0x186a0
#define PCI_BUS_SKIP_BRG_RECURSIVELY	0
#define PCI_SETUP_USE_APTIO_4_STYLE	0
#define PCI_SETUP_SHOW_NOT_FOUND_ONBOARD_DEVICES	0
#define PCI_SETUP_SHOW_SLOTS_FIRST	1
#define PCI_SETUP_SORT_ONBOARD	0x2
#define PCI_BUS_IGNORE_OPTION_ROM	0
#define PCI_BUS_USE_PCI_PCIIO_FOR_OPTION_ROM	0
#define PCIE_CRS_SUPPORT	0
#define PCI_RB_COMBINE_MEM_PMEM_RESET	0
#define NO_LEGACY_IRQ_PLATFORM	0
#define IGNORE_BUS_OOR_HALT	0
#define LAUNCH_COMMON_INIT_ROUTINE_FIRST	0
#define CLEAR_BRIDGE_CONTROL_ON_ENTRY	0
#define DONT_UPDATE_RB_RES_ASL	0
#define DONT_UPDATE_PEBS_IN_AML	0
#define DONT_UPDATE_RB_UID_IN_ASL	0
#define PCI_WRITE_VID_DID	1
#define CPU_MAX_IO_SIZE	0x10000
#define AVOID_FIXED_RES_OVERLAP_IN_MIXED_MODE_CONFIG	1
#define PCI_ENUMERATION_LIGHT	0
#define SHOW_MAX_PAYLOAD_SIZE_BASED_ON_ROOT_PORT_CAP	0
#define SHOW_ASPM_OPTION_BASED_ON_ROOT_PORT_CAP	0
#define BoardPciRes_SUPPORT	1
#define PlatformToDriver_SUPPORT	1
#define RsdpPlus_SUPPORT	1
#define SmiVariable_SUPPORT	1
#define Terminal_SUPPORT	1
#define TERMINAL_VERSION	0x1d
#define DISABLE_TERMINAL_FOR_SCT_TEST	0x0
#define CREATE_ACPI_SPCR_TABLE	1
#define TOTAL_VENDOR_SERIAL_PORTS	0x0
#define TOTAL_SERIAL_PORTS	0x2
#define PCI_SERIAL_PORT_0_INDEX	0x0
#define PCI_SERIAL_PORT_1_INDEX	0x1
#define PCI_SERIAL_PORT_2_INDEX	0x2
#define PCI_SERIAL_PORT_3_INDEX	0x3
#define PCI_SERIAL_PORT_4_INDEX	0x4
#define PCI_SERIAL_PORT_5_INDEX	0x5
#define PCI_SERIAL_PORT_6_INDEX	0x6
#define PCI_SERIAL_PORT_7_INDEX	0x7
#define VENDOR_SERIAL_PORT_0_INDEX	0x2
#define VENDOR_SERIAL_PORT_1_INDEX	0x3
#define VENDOR_SERIAL_PORT_2_INDEX	0x4
#define VENDOR_SERIAL_PORT_3_INDEX	0x5
#define UART_DEFAULT_BAUD_RATE	0x1c200
#define UART_DEFAULT_BAUD_RATE_INDEX	0x7
#define UART_DEFAULT_DATA_BITS	0x8
#define UART_DEFAULT_PARITY	0x1
#define UART_DEFAULT_STOP_BITS	0x1
#define UART_DEFAULT_FLOW_CONTROL	0x0
#define DEFAULT_TERMINAL_TYPE	0x3
#define COM2_DEFAULT_CONSOLE_REDIRECTION_ENABLE	0
#define COM3_DEFAULT_CONSOLE_REDIRECTION_ENABLE	0
#define COM4_DEFAULT_CONSOLE_REDIRECTION_ENABLE	0
#define COM5_DEFAULT_CONSOLE_REDIRECTION_ENABLE	0
#define COM6_DEFAULT_CONSOLE_REDIRECTION_ENABLE	0
#define COM7_DEFAULT_CONSOLE_REDIRECTION_ENABLE	0
#define COM8_DEFAULT_CONSOLE_REDIRECTION_ENABLE	0
#define COM9_DEFAULT_CONSOLE_REDIRECTION_ENABLE	0
#define PCI2_DEFAULT_CONSOLE_REDIRECTION_ENABLE	0
#define PCI3_DEFAULT_CONSOLE_REDIRECTION_ENABLE	0
#define PCI4_DEFAULT_CONSOLE_REDIRECTION_ENABLE	0
#define PCI5_DEFAULT_CONSOLE_REDIRECTION_ENABLE	0
#define PCI6_DEFAULT_CONSOLE_REDIRECTION_ENABLE	0
#define PCI7_DEFAULT_CONSOLE_REDIRECTION_ENABLE	0
#define VENDOR0_DEFAULT_CONSOLE_REDIRECTION_ENABLE	0
#define VENDOR1_DEFAULT_CONSOLE_REDIRECTION_ENABLE	0
#define VENDOR2_DEFAULT_CONSOLE_REDIRECTION_ENABLE	0
#define VENDOR3_DEFAULT_CONSOLE_REDIRECTION_ENABLE	0
#define DEFAULT_DEBUGGER_CONSOLE_REDIRECTION_ENABLE	1
#define DEFAULT_ACPI_SPCR_COM_PORT	0x0
#define DEFAULT_ACPI_SPCR_TABLE_TERMINAL_TYPE	0x2
#define VTUTF8_ENABLE	1
#define RECORDER_MODE_ENABLE	0
#define MAX_FAILURES_ALLOWED	0x5
#define TIMEOUT_FOR_DETERMINING_LONE_ESC_CHAR	0x989680
#define NUMBER_OF_TIME_FOR_ESC_SEQUENCE_CHECKING	0x14
#define UART_FIFO_SIZE	0x40
#define REFRESH_SCREEN_KEY	0x12
#define ASCII_CONTROL_CODE_SUPPORT	1
#define spcr_interface_type	0x0
#define spcr_addr_spc_id	0x1
#define spcr_addr_bit_width	0x8
#define spcr_addr_bit_offset	0x0
#define spcr_access_size	0x0
#define spcr_interrupt_type	0x3
#define spcr_global_system_int	0xffffffff
#define spcr_parity	0x0
#define spcr_stop_bits	0x1
#define spcr_pci_device_id	0xffff
#define spcr_pci_vendor_id	0xffff
#define spcr_pci_bus_number	0x0
#define spcr_pci_device_number	0x0
#define spcr_pci_function_number	0x0
#define spcr_pci_flags	0x0
#define spcr_pci_segment	0x0
#define SERIAL_WRITE_ERROR_CHECK	1
#define MAXIMUM_SERIAL_WRITE_ERROR_COUNT	0xa
#define CLEAR_TERMINAL_KB_BUFFER_AT_READYTOBOOT	0
#define OEM_ESC_SEQUENCES	1
#define INSTALL_LEGACY_OS_THROUGH_REMOTE	0
#define DONT_SEND_ASCII_CONTROL_PRINTABLE_CHARACTERS	0
#define SERIAL_MOUSE_DETECTION	0
#define EFI_SIO_PROTOCOL_SUPPORT	0
#define TERMINAL_HOTKEY_POST_MSG	0
#define MAX_DATA_READ_RETRY_COUNT	0x0
#define SERIALIO_CHECK_FOR_LOOPBACK_DEVICE	0
#define SELECT_UART_INTERFACE	0x0
#define AMIUSB_SUPPORT	1
#define USB_DRIVER_MAJOR_VER	0x25
#define USB_HOTPLUG_HDD	0
#define USB_HOTPLUG_CDROM	0
#define HIDE_USB_SUPPORT_SETUP_QUESTION	1
#define KEY_REPEAT_DELAY	0x2
#define KEY_REPEAT_INITIAL_DELAY	0x6
#define USB_KEYREPEAT_INTERVAL	0
#define USB_EHCI_HANDOFF_SUPPORT	1
#define USB_XHCI_EXT_HW_SMI_PINS	{0x1,0xFF,0xFF,0xFF,0xFF}
#define USB_DIFFERENTIATE_IDENTICAL_DEVICE_NAME	0
#define USB_MASS_STORAGE_DEVICE_NAME	0x0
#define USB_BAD_DEVICES	{0x0}
#define HIDE_USB_HISPEED_SUPPORT_SETUP_QUESTION	1
#define DEFAULT_USB_EMUL6064_OPTION	1
#define USB_RUNTIME_DRIVER_IN_SMM	0x2
#define USB_INTERRUPT_POLLING_PERIOD	80000
#define EXTERNAL_USB_CONTROLLER_SUPPORT	1
#define USB_OHCI_HANDOFF_SUPPORT	0
#define USB_SPECIFIC_CONTROLLERS_LIST	{0x0}
#define HIDE_USB_XHCI_LEGACY_SUPPORT_SETUP_QUESTION	1
#define USB_SETUP_VARIABLE_RUNTIME_ACCESS	0
#define USB_CONTROLLERS_INITIAL_DELAY_LIST	{0x00}
#define USB_REGISTER_PERIODIC_TIMER_IN_DXE	0
#define DEFAULT_XHCI_HANDOFF_OPTION	1
#define USB_STORAGE_DEVICE_RMB_CHECK	1
#define USB_IRQ_SUPPORT	0
#define USB_RESET_PORT_POLICY	1
#define USB_HOST_SYSTEM_ERRORS_SUPPORT	1
#define USB_LEGACY_SUPPORT_SETUP_OPTION_CHECK_AMICSM	0
#define USB_PCI_IO_ALLOCATE_BUFFER_SUPPORT	0
#define USB_SKIP_CMD_MEM_CHECK	0
#define USB_PCI_IO_ATTRIBUTE_DUAL_ADDRESS_CYCLE_SUPPORT	0
#define USB_LEGACY_MASS_BUFFER_ADDRESS_CHECK	0
#define USB_ACCESS_VARIABLE_AFTER_SMM	1
#define USB_DEV_KBD	1
#define USB_DEV_MOUSE	1
#define USB_DEV_HID	1
#define USB_DEV_HUB	1
#define USB_DEV_MASS	1
#define USB_DEV_POINT	1
#define MEM_PAGE_COUNT	0x8
#define USB_SEND_COMMAND_TO_KBC	0x60
#define USB_MOUSE_UPDATE_EBDA_DATA	1
#define EHCI_ASYNC_BELL_SUPPORT	0
#define USB_DEV_HID_COUNT	0xf
#define USB_DEV_HUB_COUNT	0xc
#define USB_DEV_MASS_COUNT	0x10
#define USB_DEV_UNSUPPORTED	0x10
#define USB_START_UNIT_BEFORE_MSD_ENUMERATION	0
#define USB_MASS_EMULATION_NATIVE	1
#define USB_MASS_EMULATION_FOR_NO_MEDIA	1
#define USB_MASS_EMULATION_BY_SIZE	0
#define MAX_SIZE_FOR_USB_FLOPPY_EMULATION	0x212
#define REMOVE_USB_STORAGE_FROM_BBS_IF_NO_MEDIA	0
#define REMOVE_CHECK_FOR_USB_FLOPPY_DRIVE	0
#define HIGHSPEED_MAX_BULK_DATA_SIZE	0x4000
#define FULLSPEED_MAX_BULK_DATA_SIZE_PER_FRAME	0x380
#define EXTRA_CHECK_DEVICE_READY	0
#define USB_HID_KEYREPEAT_USE_SETIDLE	0x0
#define USB_EFIMS_DIRECT_ACCESS	0
#define BOOT_PROTOCOL_SUPPORT	0
#define CLEAR_USB_KB_BUFFER_AT_READYTOBOOT	0
#define SKIP_CARD_READER_CONNECT_BEEP_IF_NO_MEDIA	0
#define SPECIFIC_EHCI_OWNERSHIP_CHANGE_MECHANISM	0
#define LEGACY_USB_DISABLE_FOR_USB_MASS	0
#define EFI_USB_HC_INTERRUPT_OUT_SUPPORT	1
#define USB_IAD_PROTOCOL_SUPPORT	0
#define USB_ISOCTRANSFER_SUPPORT	1
#define USB_ITD_MEM_ALLOC_FOR_ALL_EHCI	1
#define USB_DCBAA_MEM_ALLOC_FOR_ALL_XHCI	1
#define USB_GET_BOS_DESCRIPTOR_SUPPORT	0
#define USB_MEM_USE_BS_IN_UEFI	0
#define USB_USE_FIXED_MEMORY_PAGE_SIZE	0x0
#define USB_SKIP_UEFI_DRIVER_SUPPORT	0
#define UINT13_SUPPORT	1
#define USB_I13_DRIVER_VERSION	0x1
#define USB_BBS_DXE	0
#define USBPEI_SUPPORT	1
#define UHCI_PEI_SUPPORT	0
#define OHCI_PEI_SUPPORT	0
#define EHCI_PEI_SUPPORT	0
#define PEI_PCI_SEGMENT_SUPPORT	0
#define PEI_UHCI_IOBASE	0x4000
#define PEI_UHCI_PCI_DEVICES	{0x00}
#define PEI_EHCI_PCI_BDFS	{0x02,  0x00, 0x1A, 0x00,  0x00, 0x1D, 0x00}
#define PEI_EHCI_MEM_BASE_ADDRESSES	{0x02,  0x00, 0xF0, 0xFF, 0xDF,  0x00, 0xFC, 0xFF, 0xDF}
#define PEI_OHCI_IOBASE	0xfc087000
#define PEI_XHCI_MMIOBASE	0xfe400000
#define PEI_XHCI_MMIOSIZE	0x10000
#define PEI_USB_RECOVERYREQUEST_RETRIES	0x2
#define PEI_USB_RECOVERYREQUEST_TIMEOUT	0x1e
#define PEI_USB_RECOVERYREQUEST_KEY_MODIIFIER_VALUES	{0x02, 0x08, 0x04}
#define PEI_USB_RECOVERYREQUEST_KEY_EFISCANCODE	0x5
#define PEI_USB_KEYBOARD_NUMLOCK_ON	1
#define USBPEI_IN_S3_SUPPORT	0
#define USBR_OHCI_CONTROLLER_PCI_ADDRESS	{0x01,  0x00, 0x12, 0x00}
#define USBR_OHCI_CONTROLLER_PCI_REGISTER_VALUE	{0x1,  0x00, 0x12, 0x00, 0x40,0x00,0x00,0x00, 0x20, 0x03,0x00,0x00,0x00, 0x00,0x00,0x00,0x00}
#define PEI_USB_DEVICE_CONNECT_TIMEOUT	0x1
#define PEI_USB_KEYBOARD_CONNECT_DELAY_MS	0x0
#define PEI_USB_MASS_STORAGE_SUPPORT_POLICY	0x0
#define PEI_USB_HOST_SYSTEM_ERRORS_SUPPORT	1
#define AMI_USB_PEI_SKIP_TABLE	{0x00,  0x00, 0x00, 0x00, 0x00}
#define AmiUsbLib_SUPPORT	1
#define USB_DEV_CCID	0
#define AmiNetworkPkg_SUPPORT	1
#define AMI_NETWORK_PKG_VERSION	0x31
#define TSE_POST_MGR_PROTOCOL_USED	0
#define UefiNetworkStack_SUPPORT	1
#define NET_PKG_AMI_PORTING_ENABLE	1
#define SET_AUTOMATIC_POLICY_AS_DEFAULT	1
#define UefiPxeBcDxe_SUPPORT	1
#define Ipv4_SUPPORT	1
#define PRESERVE_IP4_CONFIG_VARIABLE_SUPPORT	1
#define SUPPORT_CACERT_IN_BUILDTIME	0
#define Ipv6_SUPPORT	1
#define LOAD_DRIVER_ONLY_ON_NWSTACK_ENABLE	1
#define PRESERVE_TLS_CA_CERTIFICATE	1
#define CONTINUOUS_MNP_POLLING_ENABLE	0
#define NET_FAIL_SAFE_DHCP4_CLIENT_ADDR	1
#define AMISV657_SECURITYFIX	1
#define NetworkStackSetupScreen_SUPPORT	1
#define NETWORKSTACK_IPV6_HTTP_SUPPORT	0
#define NTP_SUPPORT	0
#define AmiBoardInfo_SUPPORT	1
#define AMI_BOARD_INFO_MAJOR_VERSION	0x1
#define AMI_BOARD_INFO_MINOR_VERSION	0x0
#define AMI_BOARD_VER_COMBINED	0x64
#define Recovery_SUPPORT	1
#define ENABLE_RECOVERY_TRACES	0x0
#define RECOVERY_SCAN_RETRIES	0x3
#define RECOVERY_DEVICE_ORDER	BLOCK,
#define OFFSET_TO_ADDRESS(Offset)	(0xFFFFFFFF - FLASH_SIZE + (Offset) + 1)
#define REFLASH_INTERACTIVE	1
#define REFLASH_UPDATE_NVRAM_CONTROL	1
#define REFLASH_UPDATE_NVRAM	1
#define REFLASH_UPDATE_BOOT_BLOCK_CONTROL	1
#define REFLASH_UPDATE_MAIN_BLOCK	1
#define RECOVERY_DEV_MODE	0
#define GetFirmwareVersion	GetVersionFromFid
#define SETUP_FIRMWARE_UPDATE	1
#define FMP_FIRMWARE_UPDATE	1
#define REFLASH_DEFINITIONS_SUPPORT	0
#define PRESERVE_FFS	0
#define FLASH_UPDATE_BATTERY_THRESHOLD	0x32
#define DISABLE_FLASH_UPDATE_WITH_NO_AC	0
#define REFLASH_IMAGE_EXTRA_CHECK	1
#define ReFlashAfu_SUPPORT	0
#define NTFS_NAME_CORRECTION	0
#define EXFATRecovery_SUPPORT	1
#define SEARCH_PATH	0
#define RECOVERY_PATH	Recovery/BIOS/
#define MATCH_VOLUME_NAME	0
#define FtRecovery_SUPPORT	0
#define IsRecovery_SUPPORT	1
#define SMM_SUPPORT	1
#define RestBoot_SUPPORT	1
#define CLEAR_BOOT_REQUEST_URL	"/redfish/v1/Oem/Ami/InventoryData"
#define CLEAR_BOOT_CRC_BODY_REQUEST	"{\"Systems\": {\"Bios\": {\"SD\": false, \"ChangePassword\": false, \"ResetBios\": false}, \"SecureBoot\": {\"ResetKeys\" : false}}, \"OOB_Crc\": {\"BOOT_OVERRIDE\": true, \"VLAN\": false, \"ISCSI\": false, \"SECUREBOOT\":false}}"
#define HardwareHealthManagement_SUPPORT	0
#define REMOTE_FWCONFIG_SUPPORT	1
#define Dump_SetupData_Xml_File	0
#define ALWAYS_SEND_SETUPDATA_XML	0
#define UiApp_SUPPORT	1
#define FwComboButton_SUPPORT	1
#define FwCss_SUPPORT	1
#define FwFavicon_SUPPORT	1
#define FwIndex_SUPPORT	1
#define FwJs_SUPPORT	1
#define FwLoaderImage_SUPPORT	0
#define FwRbLogo_SUPPORT	1
#define AMI_REDFISH_INVETORY_DATA_URI	"/redfish/v1/Oem/Ami/InventoryData"
#define RedfishFeatures_SUPPORT	1
#define RfSecureBoot_SUPPORT	1
#define AMI_RFSB_USE_LIST	0x1
#define RfTrustedModules_SUPPORT	1
#define RfTrustedModulesTest	0
#define TpmFirmwareUpdateMechanism	0x2
#define RfBootOptions_SUPPORT	1
#define RfBootOptions_AptioV_Project	1
#define BOOT_FLOW_CONDITION_REDFISH_BOOT	0xD
#define REDFISH_BOOTFLOW_OVERRIDE_HOTKEY	0
#define RfInventory_SUPPORT	1
#define RFINVENTORY_DUMP_SUPPORT	0
#define RFINVENTORY_POST_ALWAYS	0
#define RFINVENTORY_INTEL_NVDIMM_SUPPORT	0
#define RfIScsiBoot_SUPPORT	1
#define RfIScsi_FLAG	0
#define RfVlan_SUPPORT	1
#define RfVlan_FLAG	1
#define RfTlsCertificates_SUPPORT	1
#define INVENTORY_DEBUG_SUPPORT	1
#define FILTER_VIRTUALUSB_NETWORK	1
#define SYSTEM_INVENTORY_INFO_MAJOR_REVISION	0x1
#define SYSTEM_INVENTORY_INFO_MINOR_REVISION	0xf
#define SYSTEM_INVENTORY_INFO_PATCH_REVISION 	1
#define AMI_SYS_INV_DIE_CORE_COUNT	0x0
#define AMI_SYS_INV_ETH_SUPPORT_ON_INFINIBAND	0x0
#define HHM_NO_OF_DIMM_SLOTS	0x30
#define AMIRedFishVlanExt_SUPPORT	1
#define AMIRedFishVlan_SUPPORT	0
#define AmiRedfishIScsi_SUPPORT	0
#define AMI_REDFISH_PKG_VERSION	0x3
#define RedfishHi_SUPPORT	1
#define REDFISH_VERSION_BIOS_SUPPORT	"1.15.1"
#define RTP_VERSION_BIOS_SUPPORT	"RB_1.0.19"
#define USE_REDFISH_HI_SETUP_PAGE	0x1
#define RedfishBmcPresent_SUPPORT	1
#define AMI_DATA_PROCESSING_PKG_VERSION	0x3
#define AmiJsonLibCJson_SUPPORT	1
#define AmiJsonLibJansson_SUPPORT	0
#define MAX_USBLAN_INTERFACE	0x10
#define USBLAN_VERBOSE_PRINT	0x0
#define USBLAN_BULKIN_CMD_CONTROL	0x1
#define MAXIMUM_STOPBULKIN_CNT	0x12c
#define MINIMUM_STOPBULKIN_CNT	0x3
#define BULKIN_CMD_POLLING_CNT	0x12c
#define BmcSync_SUPPORT	1
#define BC_IPMI_SUPPORT_INCLUDED	1
#define AMI_IPMI_OVER_USB_SUPPORT	0
#define SUPPRESS_COND_EXPRESSION_CONTROLS	0
#define SUPPRESSIF_TRUE_COND_CONTROLS	0
#define SYSTEM_ACCESS_MAPPINGID	L"SYSACS"
#define BOOT_ORDER_MAPPINGID	L"SETUP006"
#define REMOTE_ENT_ESP_BASE_FOLDER	L"EFI\\Boot\\EMA"
#define REMOTE_CLEAR_PASSWORD_SUPPORT	0
#define AMIUTILSPKG_VERSION	0x1b
#define NUMERIC_HEX_REPRESENTATION_SUPPORT	0x0
#define EFIVARSTORE_COND_EXPRESSION_SUPPORT	0x0
#define REMOTE_CONFIG_TSE_DEPENDENCY	1
#define SetupDataCreationLib_SUPPORT	0
#define AttribRegistryLib_SUPPORT	1
#define ATTRIBUTE_REG_CONFIG_LIB_DIR	AmiUtilsPkg/JsonCreationLib/AttribRegistryLib
#define LIST_MAPPING_STRING_ONLY	1
#define ALLOW_DUPLICATE_MAP_ID	0
#define OPTIMISATION_NO_SPACE_ATTRIB_REGISTRY	0
#define UNSUPPORTED_CONTROL_TYPE	_CONTROL_TYPE_LABEL, _CONTROL_TYPE_MSGBOX, _CONTROL_TYPE_EMPTY, _CONTROL_TYPE_MENU,_CONTROL_TYPE_ORDERED_LIST,
#define JSONHII_LIBRARY_SUPPORT	1
#define SetupDataXmlCreation_SUPPORT	1
#define XML_TSE_LIBRARY	 
#define SUPPORT_SUBTITLE_IN_SAVE_EXIT_PAGE	0
#define SETUPDATA_XML_ALLOW_DUPLICATE_MAP_ID	1
#define SETUPDATA_XML_UNSUPPORTED_CONTROL_TYPE	_CONTROL_TYPE_LABEL,_CONTROL_TYPE_MSGBOX,_CONTROL_TYPE_EMPTY,_CONTROL_TYPE_MENU,_CONTROL_TYPE_ORDERED_LIST,
#define SUPPRESS_FORMSET_SUPPORT	0
#define CRB_FORMSET_SUPPRESS	0x00
#define MountFs_SUPPORT	0
#define RedfishBmcRootCa_SUPPORT	1
#define OEM_IPMI_INTERAFACE_OUTPUT_BUFF	0xf0
#define AmiTlsCertificate_SUPPORT	1
#define USE_AMI_REDFISH_INTERFACE	1
#define AMIBMC_DEVICE_VENDOR_ID	0x46b
#define AMIBMC_DEVICE_PRODUCT_ID	0xffb0
#define AMI_SECUREBOOTPKG_MODULE_REVISION	0x15
#define AmiSecureBootLib_SUPPORT	1
#define SecureBootLib_DEBUG_LEVEL	EFI_D_INFO
#define AmiTrustedFv_Support	0
#define AtaPassThru_SUPPORT	1
#define ATAPASSTHRU_DRIVER_VERSION	0xe
#define ATAPI_COMMANDS_SUPPORT_IN_ATAPASSTHRU	1
#define CSM_SUPPORT	1
#define CSM16_VERSION_MAJOR	0x7
#define CSM16_VERSION_MINOR	0x54
#define CSM_VERSION_BUILD	0x18
#define AGGRESSIVELY_JOINED_E820_ENTRIES	0
#define PMM_EBDA_LOMEM_SIZE	0x60000
#define PMM_LOMEM_SIZE	0x30000
#define PMM_HIMEM_SIZE	0x9600000
#define LEGACY_TO_EFI_DEFAULT	0
#define LEGACY_TO_EFI_BOOT_BUFFER_SIZE	0x300000
#define OPROM_MAX_ADDRESS	0xe7fff
#define SKIP_EARLY_BCV_DEVICES	{0x3f20105a}
#define INT15_D042_SWSMI	0x44
#define USB_SWSMI	0x31
#define CSM_SET_ZIP_EMULATION_TYPE	0
#define CSM_CREATES_ATA_ATAPI_STRINGS	1
#define CSM_DEFAULT_VMODE_SWITCHING	0x0
#define CSM_ALLOW_LARGE_OPROMS	0
#define CSM_KEEP_BOOT_HDD_ORDER	0
#define INT10_VESA_GO_SUPPORT	1
#define INT10_VGA_GO_SUPPORT	0
#define INT10_SIMPLE_TEXT_SUPPORT	1
#define INT10_TRUST_EDID_INFORMATION	1
#define INT10_GET_VGA_PORTS	1
#define CSM_VGA_64BITBAR_WORKAROUND	0
#define CSMSETUP_ENABLE_ALL_BOOT_OPTIONS	0x0
#define CSMSETUP_LEGACY_ONLY_BOOT_OPTIONS	0x1
#define CSMSETUP_UEFI_ONLY_BOOT_OPTIONS	0x2
#define DEFAULT_BOOT_OPTION_FILTERING_POLICY	0x0
#define CSMSETUP_SKIP_OPROMS	0x0
#define CSMSETUP_UEFI_ONLY_OPROMS	0x1
#define CSMSETUP_LEGACY_ONLY_OPROMS	0x2
#define DEFAULT_PXE_OPROM_POLICY	0x1
#define DEFAULT_MASS_STORAGE_OPROM_POLICY	0x1
#define DEFAULT_OLD_OPROM_POLICY	0x1
#define ACPI_TIMER_IN_LEGACY_SUPPORT	0
#define CMOS_MANAGER_SUPPORT	1
#define CMOS_MANAGER_VERSION	0x10
#define CMOS_RTC_STATUS_REGISTER	0xd
#define CMOS_DIAGNOSTIC_STATUS_REGISTER	0xe
#define CSMI	0x61
#define FULL_CMOS_MANAGER_DEBUG	0
#define FIRST_MANAGED_CMOS_ADDRESS	0x40
#define CMOS_MGR_RECOVER_ONLY_CHECKUMMED	0
#define CMOS_MGR_RECOVER_IN_PEI	1
#define CMOS_SEC_SUPPORT	0
#define CMOS_SMM_SUPPORT	0
#define CMOS_USES_STANDARD_RANGE_ACCESS	1
#define CMOS_BANK0_INDEX	0x70
#define CMOS_BANK0_DATA	0x71
#define CMOS_BANK1_INDEX	0x72
#define CMOS_BANK1_DATA	0x73
#define CMOS_BANK1_OFFSET	0x0
#define CMOS_MGR_SET_NMI_BIT	1
#define CMOS_NMI_BIT_VALUE	0x80
#define CMOS_BASED_API_SUPPORT	1
#define CMOS_BASED_API_INDEX	0x70
#define CMOS_BASED_API_DATA	0x71
#define CMOS_ACCESS_API_BYTE3	0x42
#define CMOS_SETUP_SUPPORT	0
#define CMOS_MESSAGES_SUPPORT	0
#define CMOS_USES_STANDARD_BATTERY_TEST	1
#define CMOS_USES_STANDARD_IS_FIRST_BOOT	1
#define CMOS_USES_STANDARD_IS_BSP	1
#define CMOS_USES_STANDARD_IS_CMOS_USABLE	1
#define CMOS_USES_STANDARD_IS_COLD_BOOT	1
#define ENHANCE_PEI_VARIABLE_LABEL_VERSION	0x12
#define GET_FROM_STD_DEFAULT	1
#define FIND_STD_DEFAULT_IN_SPECIFIC_FV	0
#define FIND_SPECIFIC_FV_NAME_GUID	{ 0x61C0F511, 0xA691, 0x4F54, { 0x97, 0x4F, 0xB9, 0xA4, 0x21, 0x72, 0xCE, 0x53 } }
#define SET_CACHED_VARIABLE	0
#define FlashSmiSupport	1
#define DISABLE_FLASH_SMI_CRITICAL_SECTION	0
#define FlashSmiLinksSupport	0
#define HDD_SECURITY_SUPPORT	0
#define SMMHDDSECURITY_SUPPORT	0
#define AMI_TSE_HDD_SECURITY_SUPPORT	0
#define HDD_SECURITY_DYNAMIC_SETUP_SUPPORT	0
#define HDD_SECURITY_STATIC_SETUP_SUPPORT	0
#define EMUL6064_SUPPORT	1
#define KBCEMULATION_DRIVER_VERSION	0xb
#define SB_EMUL_SUPPORT	0
#define IOTRAP_EMUL_SUPPORT	0
#define ICH10_WORKAROUND	0
#define IRQ_EMUL_SUPPORT	1
#define EMULATION_ACPI_ENABLE_DISPATCH	0
#define TRACK_PS2MOUSE_4BYTE_PACKET	1
#define Esrt_SUPPORT	1
#define RamDisk_SUPPORT	0
#define MsSysGrdTpm_Verbose	0
#define NVMe_SUPPORT	1
#define NVME_VERBOSE_PRINT	0x0
#define NVME_SETUP	0x1
#define NVMe_SMM_SUPPORT	1
#define NVME_BUS_DRIVER_VERSION	0x1
#define NVME_DRIVER_VERSION	0x25
#define NVME_COMMAND_TIMEOUT	0x1
#define DO_NOT_LAUNCH_NVME_OPROM	1
#define NVME_SWSMI	0x42
#define MAX_RETRY_COUNT_FOR_FATAL_ERROR	0x0
#define NVMEINT13_SUPPORT	1
#define NvmeDynamicSetup_SUPPORT	1
#define OFBD_SW_SMI_VALUE	0x26
#define OFBD_VERSION	0x210
#define OFBD_NEW_WSMT_SUPPORT	1
#define OpalSecurity_SUPPORT	1
#define OPAL_SECURITY_DRIVER_VERSION	0xa
#define PeiPciEnumeration_SUPPORT	0
#define SATA_DEVICE_INFO_SUPPORT	1
#define DISPLAY_HDD_FEATURE_INFO	0
#define SecureBoot_SUPPORT	1
#define SECURE_BOOT_MODULE_REVISION	0x34
#define SecureBootDXE_SUPPORT	1
#define CUSTOMIZED_SECURE_BOOT_DEPLOYMENT	1
#define DEFAULT_SECURE_BOOT_DEPLOYED_MODE	0
#define SECUREBOOT_AUDIT_MODE_CUSTOMIZATION	0
#define SECUREBOOT_DEVICE_GUARD_READY	0
#define DEFAULT_ACTIVE_UEFI_CA_SET	0x1
#define SECURE_BOOT_SETUP	1
#define SECUREBOOT_PROMPT_RESET_ON_STATE_CHANGE	1
#define SECUREBOOT_FORCE_DEFAULTS_IN_STANDARD_MODE	0
#define LOAD_FROM_FV	0x0
#define LOAD_FROM_OROM	0x4
#define LOAD_FROM_REMOVABLE_MEDIA	0x4
#define LOAD_FROM_FIXED_MEDIA	0x4
#define SecureBoot_DEBUG_LEVEL	EFI_D_INFO
#define AuthVariable_SUPPORT	1
#define SECUREBOOT_MODE_CHANGE_RESET_REQUIRED	0
#define USER_MODE_POLICY_OVERRIDE	1
#define SELF_SIGNED_CERTIFICATE_IN_PRIVATE_VARIABLES	0
#define ImageVerification_SUPPORT	1
#define AmiRedFishApi_VERSION	0x1040401
#define AmiDeviceGuardApi_SUPPORT	0
#define Certificates_SUPPORT	0
#define OemCert_SUPPORT	0
#define MsftCert_SUPPORT	0
#define SecureMod_SUPPORT	1
#define SECURE_FLASH_MODULE_REVISION	0x2e
#define FWCAPSULE_CERT_FORMAT	0x0
#define FWCAPSULE_MAX_HDR_SIZE	0x1000
#define FWCAPSULE_MAX_PAYLOAD_SIZE	0x1000000
#define FWCAPSULE_IMAGE_SIZE	0x1004000
#define SecureMod_DEBUG_LEVEL	EFI_D_INFO
#define SecFlashUpd_SUPPORT	1
#define FlashUpdatePolicy	0x7
#define BBUpdatePolicy	0x7
#define IGNORE_RUNTIME_UPDATE_IMAGE_REVISION_CHECK	0
#define IGNORE_FID_FW_VERSION_GUID_CHECK	1
#define FWCAPSULE_RECOVERY_SUPPORT	1
#define FLASH_LOCK_EVENT_NOTIFY	1
#define SecSMIFlash_SUPPORT	1
#define NEW_BIOS_MEM_ALLOC	0x1
#define FWCAPSULE_S4_SHUTDOWN_SUPPORT	0
#define INSTALL_SECURE_FLASH_SW_SMI_HNDL	0
#define SwSmi_LoadFwImage	0x1d
#define SwSmi_GetFlashPolicy	0x1e
#define SwSmi_SetFlashMethod	0x1f
#define FWKEY_FILE_SIZE	0x20
#define SMBIOS_SUPPORT	1
#define AMI_SMBIOS_MODULE_VERSION	0x8c
#define SMBIOS_2X_MAJOR_VERSION	0x2
#define SMBIOS_2X_MINOR_VERSION	0x8
#define SMBIOS_3X_MAJOR_VERSION	0x3
#define SMBIOS_DOC_VERSION	0x0
#define SMBIOS_SPEC_VERSION	0x15e
#define SMBIOS_DYNAMIC_UPDATE_TPL	0x8
#define AMI_SMBIOS_DBG_MSG_SUPPORT	0
#define SMBIOS_2X_SUPPORT	1
#define SMBIOS_3X_SUPPORT	1
#define SMBIOS_TABLE_ADDRESS_LIMIT	0
#define PRODUCER_HANDLE_ELEMENTS	0x200
#define AMI_SMBIOS_PROTOCOL_ENABLE	1
#define SMBIOS_DMIEDIT_DATA_LOC	0x2
#define SMBIOS_PRESERVE_NVRAM	1
#define ADD_STRUCTURE_LOCATION	1
#define WRITE_STRUCTURE_HANDLE_POLICY	1
#define SMBIOS_UPDATE_POLICY	0
#define UPDATE_BIOS_VERSION	0
#define UPDATE_BIOS_RELEASE_DATE	1
#define UPDATE_BASEBOARD_TYPE2	0
#define UPDATE_SYSTEM_CHASSIS_TYPE3	0
#define UPDATE_SLOT_TYPE9	1
#define UPDATE_ONBOARD_DEV_TYPE10	1
#define UPDATE_DEVICE_EXT_TYPE41	1
#define UPDATE_FIRMWARE_INVENTORY_TYPE45	1
#define DYNAMIC_UPDATE_TYPE0_EC_VERSION	0
#define SMBIOSDYNAMICDATA_HEADER_VERSION	0x0
#define NO_OF_PROCESSOR_SOCKETS	0x1
#define MAX_PEER_GROUPING_COUNT	0x10
#define NO_OF_PHYSICAL_MEMORY_ARRAY	0x1
#define SMBIOS_DYNAMIC_UPDATE_POLICY	0
#define SORT_SMBIOS_TABLE_BY_TYPE	0
#define CONVERT_TYPE4_V2X_TO_V3X	0
#define MAX_NVRAM_STRING_SIZE	1024
#define MANUFACTURER_ID_CODE	{{6, 0xf1, "InnoDisk Corporation"}, {0, 0, "Undefined"}}
#define SMBIOS_DEFAULT_LANGUAGE	"en-US"
#define SMBIOS_SUPPORTED_LANGUAGES	"en-US"
#define SMBIOS_STATIC_DATA_SUPPORT	0
#define SMBIOS_STATIC_DATA_DT_SUPPORT	1
#define AMI_SMBIOS_STATIC_DATA_DT	1
#define SMBIOS_ARM_ARCHITECTURE_SUPPORT	0
#define TYPE0_STRUCTURE	1
#define BIOS_VENDOR	"American Megatrends International, LLC."
#define BIOS_RELEASE_DATE	"12/12/2012"
#define BI_BIOS_START_ADDR	0xf000
#define BIOS_SIZE	0xff
#define BIOS_SIZE_MB	0x0
#define BIOS_SIZE_MB_ROUNDUP	0x1
#define BI_UNKNOWN_CHAR	0
#define BI_BIOS_CHAR_NOT_SUPPORTED	0
#define INC_ISA	0
#define BI_MCA	0
#define BI_EISA	0
#define INC_PCI	1
#define BI_PCMCIA	0
#define INC_PNP	0
#define INC_APM	0
#define BI_BIOS_FLASH	1
#define BI_BIOS_SHADOW	1
#define BI_VL_VESA	0
#define BI_ESCD	0
#define BI_CDROM_BOOT	1
#define BI_SELECTABLE_BOOT	1
#define BI_BIOS_ROM_SOCKET	1
#define BI_PCMCIA_BOOT	0
#define BI_EDD	1
#define BI_INT13_NEC9800	1
#define BI_INT13_TOSHIBA	1
#define BI_INT13_5_25_360	1
#define BI_INT13_5_25_1_2	1
#define BI_INT13_3_5_720	1
#define BI_INT13_3_5_2_88	1
#define BI_INT5_PRINT_SCRN	1
#define BI_INT09_KBC_SUPPORT	0
#define BI_INT14_SERIAL_SVC	1
#define BI_INT17_PRN_SVC	1
#define BI_INT10_CGA_MONO	1
#define BI_NEC_PC_98	0
#define BI_UPDATE_STRING	1
#define BI_ACPI_SUPPORT	1
#define BI_AMIUSB_SUPPORT	1
#define BI_AGP_SUPPORT	0
#define BI_I2O_BOOT_SUP	0
#define BI_LS120_BOOT_SUP	0
#define BI_ATAPI_ZIP_SUP	0
#define BI_IEEE_1394_SUP	0
#define BI_SMART_BAT_SUP	0
#define BBS_SUPPORT	1
#define NETBOOT_SUPPORT	0
#define BI_ETCD	1
#define UEFI_SUPPORT	1
#define VIRTUAL_MACHINE	0
#define MANUFACTURING_MODE_SUPPORT	0
#define ECMA	0xff
#define ECMI	0xff
#define TYPE1_STRUCTURE	1
#define SYSTEM_VERSION	"Default string"
#define SYSTEM_SERIAL_NUMBER	"Default string"
#define SMBIOS_UUID	03000200-0400-0500-0006-000700080009
#define SYSTEM_WAKEUP_TYPE	0x6
#define SYSTEM_SKU_NUMBER	"Default string"
#define BASE_BOARD_INFORMATION	1
#define TYPE2_STRUCTURE	0x1
#define NUMBER_OF_BASEBOARDS	0x1
#define BASE_BOARD_MANUFACTURER_1	"Default string"
#define BASE_BOARD_PRODUCT_NAME_1	"Default string"
#define BASE_BOARD_VERSION_1	"Default string"
#define BASE_BOARD_SERIAL_NUMBER_1	"Default string"
#define BB_ASSET_TAG_1	"Default string"
#define BASE_BOARD_FEATURE_FLAG_1	0x9
#define BB_LOC_IN_CHASSIS_1	"Default string"
#define BASE_BOARD_TYPE_1	0xa
#define NUMBER_OF_OBJECT_HANDLES_1	0x0
#define BASE_BOARD_MANUFACTURER_2	"Default string"
#define BASE_BOARD_PRODUCT_NAME_2	"Default string"
#define BASE_BOARD_VERSION_2	"Default string"
#define BASE_BOARD_SERIAL_NUMBER_2	"Default string"
#define BB_ASSET_TAG_2	"Default string"
#define BASE_BOARD_FEATURE_FLAG_2	0x9
#define BB_LOC_IN_CHASSIS_2	"Default string"
#define BASE_BOARD_TYPE_2	0xa
#define NUMBER_OF_OBJECT_HANDLES_2	0x0
#define BASE_BOARD_MANUFACTURER_3	"Default string"
#define BASE_BOARD_PRODUCT_NAME_3	"Default string"
#define BASE_BOARD_VERSION_3	"Default string"
#define BASE_BOARD_SERIAL_NUMBER_3	"Default string"
#define BB_ASSET_TAG_3	"Default string"
#define BASE_BOARD_FEATURE_FLAG_3	0x9
#define BB_LOC_IN_CHASSIS_3	"Default string"
#define BASE_BOARD_TYPE_3	0xa
#define NUMBER_OF_OBJECT_HANDLES_3	0x0
#define BB_HOSTING_BOARD	1
#define BB_REQUIRE_AUX_BOARD	0
#define BB_REPLACEABLE	1
#define BB_HOT_SWAPPABLE	0
#define SYS_CHASSIS_INFO	1
#define TYPE3_STRUCTURE	0x1
#define NUMBER_OF_SYSTEM_CHASSIS	0x1
#define SYS_CHASSIS_MANUFACTURER_1	"Default string"
#define SYS_CHASSIS_LOCK_1	0
#define SYS_CHASSIS_VERSION_1	"Default string"
#define SYS_CHASSIS_SERIAL_NUM_1	"Default string"
#define SYS_CHASSIS_ASSET_TAG_NUM_1	"Default string"
#define SYS_CHASSIS_BOOT_STATE_1	0x3
#define SYS_PWR_SUPPLY_STATE_1	0x3
#define SYS_THERMAL_STATE_1	0x3
#define SYS_SECURE_STATE_1	0x3
#define SYS_OEM_1	0x0
#define SYS_HEIGHT_1	0x0
#define NO_PWR_CORDS_1	0x1
#define ELEMENT_COUNT_1	0x0
#define CONT_ELEMENT_1	{0,0,0}
#define SYS_CHASSIS_SKU_NUMBER_1	"Default string"
#define SYS_CHASSIS_MANUFACTURER_2	"Default string"
#define SYS_CHASSIS_LOCK_2	0
#define SYS_CHASSIS_TYPE_2	0x3
#define SYS_CHASSIS_VERSION_2	"Default string"
#define SYS_CHASSIS_SERIAL_NUM_2	"Default string"
#define SYS_CHASSIS_ASSET_TAG_NUM_2	"Default string"
#define SYS_CHASSIS_BOOT_STATE_2	0x3
#define SYS_PWR_SUPPLY_STATE_2	0x3
#define SYS_THERMAL_STATE_2	0x3
#define SYS_SECURE_STATE_2	0x3
#define SYS_OEM_2	0x0
#define SYS_HEIGHT_2	0x0
#define NO_PWR_CORDS_2	0x1
#define ELEMENT_COUNT_2	0x0
#define ELEMENT_LEN_2	0x3
#define CONT_ELEMENT_2	{0,0,0,0,0,0}
#define SYS_CHASSIS_SKU_NUMBER_2	"Default string"
#define SYS_CHASSIS_MANUFACTURER_3	"Default string"
#define SYS_CHASSIS_LOCK_3	0
#define SYS_CHASSIS_TYPE_3	0x3
#define SYS_CHASSIS_VERSION_3	"Default string"
#define SYS_CHASSIS_SERIAL_NUM_3	"Default string"
#define SYS_CHASSIS_ASSET_TAG_NUM_3	"Default string"
#define SYS_CHASSIS_BOOT_STATE_3	0x3
#define SYS_PWR_SUPPLY_STATE_3	0x3
#define SYS_THERMAL_STATE_3	0x3
#define SYS_SECURE_STATE_3	0x3
#define SYS_OEM_3	0x0
#define SYS_HEIGHT_3	0x0
#define NO_PWR_CORDS_3	0x1
#define ELEMENT_COUNT_3	0x0
#define ELEMENT_LEN_3	0x3
#define CONT_ELEMENT_3	{0,0,0,0,0,0,0,0,0}
#define SYS_CHASSIS_SKU_NUMBER_3	"Default string"
#define SYS_CHASSIS_MANUFACTURER_4	"Default string"
#define SYS_CHASSIS_LOCK_4	0
#define SYS_CHASSIS_TYPE_4	0x3
#define SYS_CHASSIS_VERSION_4	"Default string"
#define SYS_CHASSIS_SERIAL_NUM_4	"Default string"
#define SYS_CHASSIS_ASSET_TAG_NUM_4	"Default string"
#define SYS_CHASSIS_BOOT_STATE_4	0x3
#define SYS_PWR_SUPPLY_STATE_4	0x3
#define SYS_THERMAL_STATE_4	0x3
#define SYS_SECURE_STATE_4	0x3
#define SYS_OEM_4	0x0
#define SYS_HEIGHT_4	0x0
#define NO_PWR_CORDS_4	0x1
#define ELEMENT_COUNT_4	0x0
#define ELEMENT_LEN_4	0x3
#define CONT_ELEMENT_4	{0,0,0,0,0,0,0,0,0,0,0,0}
#define SYS_CHASSIS_SKU_NUMBER_4	"Default string"
#define SYS_CHASSIS_MANUFACTURER_5	"Default string"
#define SYS_CHASSIS_LOCK_5	0
#define SYS_CHASSIS_TYPE_5	0x3
#define SYS_CHASSIS_VERSION_5	"Default string"
#define SYS_CHASSIS_SERIAL_NUM_5	"Default string"
#define SYS_CHASSIS_ASSET_TAG_NUM_5	"Default string"
#define SYS_CHASSIS_BOOT_STATE_5	0x3
#define SYS_PWR_SUPPLY_STATE_5	0x3
#define SYS_THERMAL_STATE_5	0x3
#define SYS_SECURE_STATE_5	0x3
#define SYS_OEM_5	0x0
#define SYS_HEIGHT_5	0x0
#define NO_PWR_CORDS_5	0x1
#define ELEMENT_COUNT_5	0x0
#define ELEMENT_LEN_5	0x3
#define CONT_ELEMENT_5	{0,0,0,0,0,0,0,0,0,0,0,0,0,0,0}
#define SYS_CHASSIS_SKU_NUMBER_5	"Default string"
#define TYPE4_STRUCTURE	0x0
#define PROC_1_SOC_DESIG	"CPU 1"
#define PROC_2_SOC_DESIG	"CPU 2"
#define PROC_3_SOC_DESIG	"CPU 4"
#define PROC_1_TYPE	0x3
#define PROC_2_TYPE	0x3
#define PROC_3_TYPE	0x3
#define PROC_4_TYPE	0x3
#define PROC_1_FAMILY	0x1
#define PROC_2_FAMILY	0x1
#define PROC_3_FAMILY	0x1
#define PROC_4_FAMILY	0x1
#define PROC_1_ID	0x0
#define PROC_2_ID	0x0
#define PROC_3_ID	0x0
#define PROC_4_ID	0x0
#define PROC_1_MANUFACTURER	"Processor 1 Manufacturer"
#define PROC_2_MANUFACTURER	"Processor 2 Manufacturer"
#define PROC_3_MANUFACTURER	"Processor 3 Manufacturer"
#define PROC_4_MANUFACTURER	"Processor 4 Manufacturer"
#define PROC_1_VERSION	"Processor 1 Version"
#define PROC_2_VERSION	"Processor 2 Version"
#define PROC_3_VERSION	"Processor 3 Version"
#define PROC_4_VERSION	"Processor 4 Version"
#define PROC_1_VOLTAGE	0x2
#define PROC_2_VOLTAGE	0x2
#define PROC_3_VOLTAGE	0x2
#define PROC_4_VOLTAGE	0x2
#define PROC_1_EXT_CLOCK	0x0
#define PROC_2_EXT_CLOCK	0x0
#define PROC_3_EXT_CLOCK	0x0
#define PROC_4_EXT_CLOCK	0x0
#define PROC_1_MAX_SPEED	0x0
#define PROC_2_MAX_SPEED	0x0
#define PROC_3_MAX_SPEED	0x0
#define PROC_4_MAX_SPEED	0x0
#define PROC_1_STATUS	0x41
#define PROC_2_STATUS	0x41
#define PROC_3_STATUS	0x41
#define PROC_4_STATUS	0x41
#define PROC_1_UPGRADE	0x1
#define PROC_2_UPGRADE	0x1
#define PROC_3_UPGRADE	0x1
#define PROC_4_UPGRADE	0x1
#define PROC_1_L1_HANDLE	0x0
#define PROC_1_L2_HANDLE	0x0
#define PROC_1_L3_HANDLE	0x0
#define PROC_2_L1_HANDLE	0x0
#define PROC_2_L2_HANDLE	0x0
#define PROC_2_L3_HANDLE	0x0
#define PROC_3_L1_HANDLE	0x0
#define PROC_3_L2_HANDLE	0x0
#define PROC_3_L3_HANDLE	0x0
#define PROC_4_L1_HANDLE	0x0
#define PROC_4_L2_HANDLE	0x0
#define PROC_4_L3_HANDLE	0x0
#define PROC_1_SERIAL_NO	"Processor 1 Serial Number"
#define PROC_2_SERIAL_NO	"Processor 2 Serial Number"
#define PROC_3_SERIAL_NO	"Processor 3 Serial Number"
#define PROC_4_SERIAL_NO	"Processor 4 Serial Number"
#define PROC_1_ASSET_TAG	"Processor 1 Asset Tag"
#define PROC_2_ASSET_TAG	"Processor 2 Asset Tag"
#define PROC_3_ASSET_TAG	"Processor 3 Asset Tag"
#define PROC_4_ASSET_TAG	"Processor 4 Asset Tag"
#define PROC_1_PART_NO	"Processor 1 Part Number"
#define PROC_2_PART_NO	"Processor 2 Part Number"
#define PROC_3_PART_NO	"Processor 3 Part Number"
#define PROC_4_PART_NO	"Processor 4 Part Number"
#define PROC_1_CORE_COUNT	0x1
#define PROC_2_CORE_COUNT	0x1
#define PROC_3_CORE_COUNT	0x1
#define PROC_4_CORE_COUNT	0x1
#define PROC_1_CORE_ENABLED	0x1
#define PROC_2_CORE_ENABLED	0x1
#define PROC_3_CORE_ENABLED	0x1
#define PROC_4_CORE_ENABLED	0x1
#define PROC_1_THREAD_COUNT	0x1
#define PROC_2_THREAD_COUNT	0x1
#define PROC_3_THREAD_COUNT	0x1
#define PROC_4_THREAD_COUNT	0x1
#define PROC_1_CHARACTERISTICS	0x4
#define PROC_2_CHARACTERISTICS	0x4
#define PROC_3_CHARACTERISTICS	0x4
#define PROC_4_CHARACTERISTICS	0x4
#define PROC_1_FAMILY_2	0x1
#define PROC_2_FAMILY_2	0x1
#define PROC_3_FAMILY_2	0x1
#define PROC_4_FAMILY_2	0x1
#define PROC_1_CORE_COUNT_2	0x1
#define PROC_2_CORE_COUNT_2	0x1
#define PROC_3_CORE_COUNT_2	0x1
#define PROC_4_CORE_COUNT_2	0x1
#define PROC_1_CORE_ENABLED_2	0x1
#define PROC_2_CORE_ENABLED_2	0x1
#define PROC_3_CORE_ENABLED_2	0x1
#define PROC_4_CORE_ENABLED_2	0x1
#define PROC_1_THREAD_COUNT_2	0x1
#define PROC_2_THREAD_COUNT_2	0x1
#define PROC_3_THREAD_COUNT_2	0x1
#define PROC_4_THREAD_COUNT_2	0x1
#define PROC_1_THREAD_ENABLED	0x1
#define PROC_2_THREAD_ENABLED	0x1
#define PROC_3_THREAD_ENABLED	0x1
#define PROC_4_THREAD_ENABLED	0x1
#define PROC_L1_CACHE_CFG_1	0x180
#define PROC_L1_CACHE_CFG_2	0x180
#define PROC_L1_CACHE_CFG_3	0x180
#define PROC_L1_CACHE_CFG_4	0x180
#define PROC_L1_MAX_CACHE_SIZE_1	0x0
#define PROC_L1_MAX_CACHE_SIZE_2	0x0
#define PROC_L1_MAX_CACHE_SIZE_3	0x0
#define PROC_L1_MAX_CACHE_SIZE_4	0x0
#define PROC_L1_INSTALLED_SIZE_1	0x0
#define PROC_L1_INSTALLED_SIZE_2	0x0
#define PROC_L1_INSTALLED_SIZE_3	0x0
#define PROC_L1_INSTALLED_SIZE_4	0x0
#define PROC_L1_SUP_SRAM_TYPE_1	0x2
#define PROC_L1_SUP_SRAM_TYPE_2	0x2
#define PROC_L1_SUP_SRAM_TYPE_3	0x2
#define PROC_L1_SUP_SRAM_TYPE_4	0x2
#define PROC_L1_CUR_SRAM_TYPE_1	0x2
#define PROC_L1_CUR_SRAM_TYPE_2	0x2
#define PROC_L1_CUR_SRAM_TYPE_3	0x2
#define PROC_L1_CUR_SRAM_TYPE_4	0x2
#define PROC_L1_CACHE_SPEED_1	0x0
#define PROC_L1_CACHE_SPEED_2	0x0
#define PROC_L1_CACHE_SPEED_3	0x0
#define PROC_L1_CACHE_SPEED_4	0x0
#define PROC_L1_ERR_CORECTION_TYPE_1	0x3
#define PROC_L1_ERR_CORECTION_TYPE_2	0x3
#define PROC_L1_ERR_CORECTION_TYPE_3	0x3
#define PROC_L1_ERR_CORECTION_TYPE_4	0x3
#define PROC_L1_SYS_CACHE_TYPE_1	0x2
#define PROC_L1_SYS_CACHE_TYPE_2	0x2
#define PROC_L1_SYS_CACHE_TYPE_3	0x2
#define PROC_L1_SYS_CACHE_TYPE_4	0x2
#define PROC_L1_ASSOCIATIVITY_1	0x2
#define PROC_L1_ASSOCIATIVITY_2	0x2
#define PROC_L1_ASSOCIATIVITY_3	0x2
#define PROC_L1_ASSOCIATIVITY_4	0x2
#define PROC_L1_MAX_CACHE_SIZE2_1	0x0
#define PROC_L1_MAX_CACHE_SIZE2_2	0x0
#define PROC_L1_MAX_CACHE_SIZE2_3	0x0
#define PROC_L1_MAX_CACHE_SIZE2_4	0x0
#define PROC_L1_INSTALLED_CACHE_SIZE2_1	0x0
#define PROC_L1_INSTALLED_CACHE_SIZE2_2	0x0
#define PROC_L1_INSTALLED_CACHE_SIZE2_3	0x0
#define PROC_L1_INSTALLED_CACHE_SIZE2_4	0x0
#define PROC_L2_CACHE_CFG_1	0x281
#define PROC_L2_CACHE_CFG_2	0x281
#define PROC_L2_CACHE_CFG_3	0x281
#define PROC_L2_CACHE_CFG_4	0x281
#define PROC_L2_MAX_CACHE_SIZE_1	0x0
#define PROC_L2_MAX_CACHE_SIZE_2	0x0
#define PROC_L2_MAX_CACHE_SIZE_3	0x0
#define PROC_L2_MAX_CACHE_SIZE_4	0x0
#define PROC_L2_INSTALLED_SIZE_1	0x0
#define PROC_L2_INSTALLED_SIZE_2	0x0
#define PROC_L2_INSTALLED_SIZE_3	0x0
#define PROC_L2_INSTALLED_SIZE_4	0x0
#define PROC_L2_SUP_SRAM_TYPE_1	0x2
#define PROC_L2_SUP_SRAM_TYPE_2	0x2
#define PROC_L2_SUP_SRAM_TYPE_3	0x2
#define PROC_L2_SUP_SRAM_TYPE_4	0x2
#define PROC_L2_CUR_SRAM_TYPE_1	0x2
#define PROC_L2_CUR_SRAM_TYPE_2	0x2
#define PROC_L2_CUR_SRAM_TYPE_3	0x2
#define PROC_L2_CUR_SRAM_TYPE_4	0x2
#define PROC_L2_CACHE_SPEED_1	0x0
#define PROC_L2_CACHE_SPEED_2	0x0
#define PROC_L2_CACHE_SPEED_3	0x0
#define PROC_L2_CACHE_SPEED_4	0x0
#define PROC_L2_ERR_CORECTION_TYPE_1	0x3
#define PROC_L2_ERR_CORECTION_TYPE_2	0x3
#define PROC_L2_ERR_CORECTION_TYPE_3	0x3
#define PROC_L2_ERR_CORECTION_TYPE_4	0x3
#define PROC_L2_SYS_CACHE_TYPE_1	0x2
#define PROC_L2_SYS_CACHE_TYPE_2	0x2
#define PROC_L2_SYS_CACHE_TYPE_3	0x2
#define PROC_L2_SYS_CACHE_TYPE_4	0x2
#define PROC_L2_ASSOCIATIVITY_1	0x2
#define PROC_L2_ASSOCIATIVITY_2	0x2
#define PROC_L2_ASSOCIATIVITY_3	0x2
#define PROC_L2_ASSOCIATIVITY_4	0x2
#define PROC_L2_MAX_CACHE_SIZE2_1	0x0
#define PROC_L2_MAX_CACHE_SIZE2_2	0x0
#define PROC_L2_MAX_CACHE_SIZE2_3	0x0
#define PROC_L2_MAX_CACHE_SIZE2_4	0x0
#define PROC_L2_INSTALLED_CACHE_SIZE2_1	0x0
#define PROC_L2_INSTALLED_CACHE_SIZE2_2	0x0
#define PROC_L2_INSTALLED_CACHE_SIZE2_3	0x0
#define PROC_L2_INSTALLED_CACHE_SIZE2_4	0x0
#define PROC_L3_CACHE_CFG_1	0x282
#define PROC_L3_CACHE_CFG_2	0x282
#define PROC_L3_CACHE_CFG_3	0x282
#define PROC_L3_CACHE_CFG_4	0x282
#define PROC_L3_MAX_CACHE_SIZE_1	0x0
#define PROC_L3_MAX_CACHE_SIZE_2	0x0
#define PROC_L3_MAX_CACHE_SIZE_3	0x0
#define PROC_L3_MAX_CACHE_SIZE_4	0x0
#define PROC_L3_INSTALLED_SIZE_1	0x0
#define PROC_L3_INSTALLED_SIZE_2	0x0
#define PROC_L3_INSTALLED_SIZE_3	0x0
#define PROC_L3_INSTALLED_SIZE_4	0x0
#define PROC_L3_SUP_SRAM_TYPE_1	0x2
#define PROC_L3_SUP_SRAM_TYPE_2	0x2
#define PROC_L3_SUP_SRAM_TYPE_3	0x2
#define PROC_L3_SUP_SRAM_TYPE_4	0x2
#define PROC_L3_CUR_SRAM_TYPE_1	0x2
#define PROC_L3_CUR_SRAM_TYPE_2	0x2
#define PROC_L3_CUR_SRAM_TYPE_3	0x2
#define PROC_L3_CUR_SRAM_TYPE_4	0x2
#define PROC_L3_CACHE_SPEED_1	0x0
#define PROC_L3_CACHE_SPEED_2	0x0
#define PROC_L3_CACHE_SPEED_3	0x0
#define PROC_L3_CACHE_SPEED_4	0x0
#define PROC_L3_ERR_CORECTION_TYPE_1	0x3
#define PROC_L3_ERR_CORECTION_TYPE_2	0x3
#define PROC_L3_ERR_CORECTION_TYPE_3	0x3
#define PROC_L3_ERR_CORECTION_TYPE_4	0x3
#define PROC_L3_SYS_CACHE_TYPE_1	0x2
#define PROC_L3_SYS_CACHE_TYPE_2	0x2
#define PROC_L3_SYS_CACHE_TYPE_3	0x2
#define PROC_L3_SYS_CACHE_TYPE_4	0x2
#define PROC_L3_ASSOCIATIVITY_1	0x2
#define PROC_L3_ASSOCIATIVITY_2	0x2
#define PROC_L3_ASSOCIATIVITY_3	0x2
#define PROC_L3_ASSOCIATIVITY_4	0x2
#define PROC_L3_MAX_CACHE_SIZE2_1	0x0
#define PROC_L3_MAX_CACHE_SIZE2_2	0x0
#define PROC_L3_MAX_CACHE_SIZE2_3	0x0
#define PROC_L3_MAX_CACHE_SIZE2_4	0x0
#define PROC_L3_INSTALLED_CACHE_SIZE2_1	0x0
#define PROC_L3_INSTALLED_CACHE_SIZE2_2	0x0
#define PROC_L3_INSTALLED_CACHE_SIZE2_3	0x0
#define PROC_L3_INSTALLED_CACHE_SIZE2_4	0x0
#define MEM_CTRL_INFO	0
#define TYPE5_STRUCTURE	0x0
#define MEM_CTRL_EDD	0x6
#define MEM_CTRL_INTRLV	0x3
#define MAX_MEM_MODULE_SIZE	0x20
#define MCE_OTHER	0
#define MCE_UNKNOWN	0
#define MCE_NONE	1
#define MCE_SINGLE_BIT_ECC	0
#define MCE_DOUBLE_BIT_ECC	0
#define MCE_ERROR_SCRUBBING	0
#define SS_OTHER 	0
#define SS_UNKNOWN 	0
#define SS_70NS	1
#define SS_60NS	1
#define SS_50NS	0
#define TYPE_OTHER	0
#define TYPE_UNKNOWN	0
#define TYPE_STANDARD	0
#define TYPE_FAST_PAGE_MODE	0
#define TYPE_EDO	0
#define TYPE_PARITY	0
#define TYPE_ECC	0
#define TYPE_SIMM	0
#define TYPE_DIMM	1
#define TYPE_BURST_EDO	0
#define TYPE_SDRAM	1
#define MM_VOLTAGE_29V	0
#define MM_VOLTAGE_33V	1
#define MM_VOLTAGE_5V	0
#define MEM_MODULE_INFO	0
#define TYPE6_STRUCTURE	0x0
#define BANK_CONNECTION_1	0xff
#define MEMORY_TYPE_1	0x100
#define BANK_CONNECTION_2	0xff
#define MEMORY_TYPE_2	0x100
#define BANK_CONNECTION_3	0xff
#define MEMORY_TYPE_3	0x100
#define BANK_CONNECTION_4	0xff
#define MEMORY_TYPE_4	0x100
#define BANK_CONNECTION_5	0xff
#define MEMORY_TYPE_5	0x100
#define BANK_CONNECTION_6	0xff
#define MEMORY_TYPE_6	0x100
#define BANK_CONNECTION_7	0xff
#define MEMORY_TYPE_7	0x100
#define BANK_CONNECTION_8	0xff
#define MEMORY_TYPE_8	0x100
#define EXTERNAL_CACHE_INFORMATION	0
#define TYPE7_STRUCTURE	0x0
#define PORT_CONNECTOR_INFO	1
#define TYPE8_STRUCTURE	0x1
#define NUMBER_OF_PORT_CONNECTORS	0x5
#define INT_CONN_1	1
#define INT_CONN_2	1
#define INT_CONN_3	1
#define INT_CONN_4	1
#define INT_CONN_5	1
#define INT_CONN_6	0
#define INT_CONN_7	0
#define INT_CONN_8	0
#define INT_CONN_9	0
#define INT_CONN_10	0
#define INT_CONN_11	0
#define INT_CONN_12	0
#define INT_CONN_13	0
#define INT_CONN_14	0
#define INT_CONN_15	0
#define INT_CONN_16	0
#define INT_CONN_17	0
#define INT_CONN_18	0
#define INT_CONN_19	0
#define INT_CONN_20	0
#define INT_CONN_DESIGNATION_1	"Internal Connector 1"
#define INT_CONN_DESIGNATION_2	"Internal Connector 2"
#define INT_CONN_DESIGNATION_3	"Internal Connector 3"
#define INT_CONN_DESIGNATION_4	"Internal Connector 4"
#define INT_CONN_DESIGNATION_5	"Internal Connector 5"
#define INT_CONN_DESIGNATION_6	"Internal Connector 6"
#define INT_CONN_DESIGNATION_7	"Internal Connector 7"
#define INT_CONN_DESIGNATION_8	"Internal Connector 8"
#define INT_CONN_DESIGNATION_9	"Internal Connector 9"
#define INT_CONN_DESIGNATION_10	"Internal Connector 10"
#define INT_CONN_DESIGNATION_11	"Internal Connector 11"
#define INT_CONN_DESIGNATION_12	"Internal Connector 12"
#define INT_CONN_DESIGNATION_13	"Internal Connector 13"
#define INT_CONN_DESIGNATION_14	"Internal Connector 14"
#define INT_CONN_DESIGNATION_15	"Internal Connector 15"
#define INT_CONN_DESIGNATION_16	"Internal Connector 16"
#define INT_CONN_DESIGNATION_17	"Internal Connector 17"
#define INT_CONN_DESIGNATION_18	"Internal Connector 18"
#define INT_CONN_DESIGNATION_19	"Internal Connector 19"
#define INT_CONN_DESIGNATION_20	"Internal Connector 20"
#define INT_CONN_TYPE_1	0x0
#define INT_CONN_TYPE_2	0x0
#define INT_CONN_TYPE_3	0x0
#define INT_CONN_TYPE_4	0x0
#define INT_CONN_TYPE_5	0x0
#define INT_CONN_TYPE_6	0x0
#define INT_CONN_TYPE_7	0x0
#define INT_CONN_TYPE_8	0x0
#define INT_CONN_TYPE_9	0x0
#define INT_CONN_TYPE_10	0x0
#define INT_CONN_TYPE_11	0x0
#define INT_CONN_TYPE_12	0x0
#define INT_CONN_TYPE_13	0x0
#define INT_CONN_TYPE_14	0x0
#define INT_CONN_TYPE_15	0x0
#define INT_CONN_TYPE_16	0x0
#define INT_CONN_TYPE_17	0x0
#define INT_CONN_TYPE_18	0x0
#define INT_CONN_TYPE_19	0x0
#define INT_CONN_TYPE_20	0x0
#define EXT_CONN_1	1
#define EXT_CONN_2	1
#define EXT_CONN_3	1
#define EXT_CONN_4	1
#define EXT_CONN_5	1
#define EXT_CONN_6	0
#define EXT_CONN_7	0
#define EXT_CONN_8	0
#define EXT_CONN_9	0
#define EXT_CONN_10	0
#define EXT_CONN_11	0
#define EXT_CONN_12	0
#define EXT_CONN_13	0
#define EXT_CONN_14	0
#define EXT_CONN_15	0
#define EXT_CONN_16	0
#define EXT_CONN_17	0
#define EXT_CONN_18	0
#define EXT_CONN_19	0
#define EXT_CONN_20	0
#define EXT_CONN_DESIGNATION_1	"External Connector 1"
#define EXT_CONN_DESIGNATION_2	"External Connector 2"
#define EXT_CONN_DESIGNATION_3	"External Connector 3"
#define EXT_CONN_DESIGNATION_4	"External Connector 4"
#define EXT_CONN_DESIGNATION_5	"External Connector 5"
#define EXT_CONN_DESIGNATION_6	"External Connector 6"
#define EXT_CONN_DESIGNATION_7	"External Connector 7"
#define EXT_CONN_DESIGNATION_8	"External Connector 8"
#define EXT_CONN_DESIGNATION_9	"External Connector 9"
#define EXT_CONN_DESIGNATION_10	"External Connector 10"
#define EXT_CONN_DESIGNATION_11	"External Connector 11"
#define EXT_CONN_DESIGNATION_12	"External Connector 12"
#define EXT_CONN_DESIGNATION_13	"External Connector 13"
#define EXT_CONN_DESIGNATION_14	"External Connector 14"
#define EXT_CONN_DESIGNATION_15	"External Connector 15"
#define EXT_CONN_DESIGNATION_16	"External Connector 16"
#define EXT_CONN_DESIGNATION_17	"External Connector 17"
#define EXT_CONN_DESIGNATION_18	"External Connector 18"
#define EXT_CONN_DESIGNATION_19	"External Connector 19"
#define EXT_CONN_DESIGNATION_20	"External Connector 20"
#define EXT_CONN_TYPE_1	0x0
#define EXT_CONN_TYPE_2	0x0
#define EXT_CONN_TYPE_3	0x0
#define EXT_CONN_TYPE_4	0x0
#define EXT_CONN_TYPE_5	0x0
#define EXT_CONN_TYPE_6	0x0
#define EXT_CONN_TYPE_7	0x0
#define EXT_CONN_TYPE_8	0x0
#define EXT_CONN_TYPE_9	0x0
#define EXT_CONN_TYPE_10	0x0
#define EXT_CONN_TYPE_11	0x0
#define EXT_CONN_TYPE_12	0x0
#define EXT_CONN_TYPE_13	0x0
#define EXT_CONN_TYPE_14	0x0
#define EXT_CONN_TYPE_15	0x0
#define EXT_CONN_TYPE_16	0x0
#define EXT_CONN_TYPE_17	0x0
#define EXT_CONN_TYPE_18	0x0
#define EXT_CONN_TYPE_19	0x0
#define EXT_CONN_TYPE_20	0x0
#define PORT_TYPE_1	0x0
#define PORT_TYPE_2	0x0
#define PORT_TYPE_3	0x0
#define PORT_TYPE_4	0x0
#define PORT_TYPE_5	0x0
#define PORT_TYPE_6	0x0
#define PORT_TYPE_7	0x0
#define PORT_TYPE_8	0x0
#define PORT_TYPE_9	0x0
#define PORT_TYPE_10	0x0
#define PORT_TYPE_11	0x0
#define PORT_TYPE_12	0x0
#define PORT_TYPE_13	0x0
#define PORT_TYPE_14	0x0
#define PORT_TYPE_15	0x0
#define PORT_TYPE_16	0x0
#define PORT_TYPE_17	0x0
#define PORT_TYPE_18	0x0
#define PORT_TYPE_19	0x0
#define PORT_TYPE_20	0x0
#define TYPE9_STRUCTURE	0x1
#define AGP_BRIDGE_BUS_DEV_FUNC_NO	0x8
#define SYSTEM_SLOT_TYPE_1	0xa6
#define SYSTEM_SLOT_TYPE_2	0xa6
#define SYSTEM_SLOT_TYPE_3	0xa6
#define SYSTEM_SLOT_TYPE_4	0xa6
#define SYSTEM_SLOT_TYPE_5	0xa6
#define SYSTEM_SLOT_TYPE_6	0xa6
#define SYSTEM_SLOT_TYPE_7	0xa6
#define SYSTEM_SLOT_TYPE_8	0xa6
#define SYSTEM_SLOT_TYPE_9	0xa6
#define SYSTEM_SLOT_TYPE_10	0xa6
#define SYSTEM_SLOT_BUS_WIDTH_1	0x5
#define SYSTEM_SLOT_BUS_WIDTH_2	0x5
#define SYSTEM_SLOT_BUS_WIDTH_3	0x5
#define SYSTEM_SLOT_BUS_WIDTH_4	0x5
#define SYSTEM_SLOT_BUS_WIDTH_5	0x5
#define SYSTEM_SLOT_BUS_WIDTH_6	0x5
#define SYSTEM_SLOT_BUS_WIDTH_7	0x5
#define SYSTEM_SLOT_BUS_WIDTH_8	0x5
#define SYSTEM_SLOT_BUS_WIDTH_9	0x5
#define SYSTEM_SLOT_BUS_WIDTH_10	0x5
#define SYSTEM_SLOT_CUR_USAGE_1	0x3
#define SYSTEM_SLOT_CUR_USAGE_2	0x3
#define SYSTEM_SLOT_CUR_USAGE_3	0x3
#define SYSTEM_SLOT_CUR_USAGE_4	0x3
#define SYSTEM_SLOT_CUR_USAGE_5	0x3
#define SYSTEM_SLOT_CUR_USAGE_6	0x3
#define SYSTEM_SLOT_CUR_USAGE_7	0x3
#define SYSTEM_SLOT_CUR_USAGE_8	0x3
#define SYSTEM_SLOT_CUR_USAGE_9	0x3
#define SYSTEM_SLOT_CUR_USAGE_10	0x3
#define SYSTEM_SLOT_LENGTH_1	0x3
#define SYSTEM_SLOT_LENGTH_2	0x3
#define SYSTEM_SLOT_LENGTH_3	0x3
#define SYSTEM_SLOT_LENGTH_4	0x3
#define SYSTEM_SLOT_LENGTH_5	0x3
#define SYSTEM_SLOT_LENGTH_6	0x3
#define SYSTEM_SLOT_LENGTH_7	0x3
#define SYSTEM_SLOT_LENGTH_8	0x3
#define SYSTEM_SLOT_LENGTH_9	0x3
#define SYSTEM_SLOT_LENGTH_10	0x3
#define SYSTEM_SLOT_ID_1	0x1
#define SYSTEM_SLOT_ID_2	0x1
#define SYSTEM_SLOT_ID_3	0x1
#define SYSTEM_SLOT_ID_4	0x1
#define SYSTEM_SLOT_ID_5	0x1
#define SYSTEM_SLOT_ID_6	0x1
#define SYSTEM_SLOT_ID_7	0x1
#define SYSTEM_SLOT_ID_8	0x1
#define SYSTEM_SLOT_ID_9	0x1
#define SYSTEM_SLOT_ID_10	0x1
#define SYSTEM_SLOT_CHAR1_1	0x4
#define SYSTEM_SLOT_CHAR1_2	0x4
#define SYSTEM_SLOT_CHAR1_3	0x4
#define SYSTEM_SLOT_CHAR1_4	0x4
#define SYSTEM_SLOT_CHAR1_5	0x4
#define SYSTEM_SLOT_CHAR1_6	0x4
#define SYSTEM_SLOT_CHAR1_7	0x4
#define SYSTEM_SLOT_CHAR1_8	0x4
#define SYSTEM_SLOT_CHAR1_9	0x4
#define SYSTEM_SLOT_CHAR1_10	0x4
#define SYSTEM_SLOT_CHAR2_1	0x1
#define SYSTEM_SLOT_CHAR2_2	0x1
#define SYSTEM_SLOT_CHAR2_3	0x1
#define SYSTEM_SLOT_CHAR2_4	0x1
#define SYSTEM_SLOT_CHAR2_5	0x1
#define SYSTEM_SLOT_CHAR2_6	0x1
#define SYSTEM_SLOT_CHAR2_7	0x1
#define SYSTEM_SLOT_CHAR2_8	0x1
#define SYSTEM_SLOT_CHAR2_9	0x1
#define SYSTEM_SLOT_CHAR2_10	0x1
#define SYSTEM_SLOT_SEG_GROUP_1	0x0
#define SYSTEM_SLOT_SEG_GROUP_2	0x0
#define SYSTEM_SLOT_SEG_GROUP_3	0x0
#define SYSTEM_SLOT_SEG_GROUP_4	0x0
#define SYSTEM_SLOT_SEG_GROUP_5	0x0
#define SYSTEM_SLOT_SEG_GROUP_6	0x0
#define SYSTEM_SLOT_SEG_GROUP_7	0x0
#define SYSTEM_SLOT_SEG_GROUP_8	0x0
#define SYSTEM_SLOT_SEG_GROUP_9	0x0
#define SYSTEM_SLOT_SEG_GROUP_10	0x0
#define SYSTEM_SLOT_BUS_NUMBER_1	0x0
#define SYSTEM_SLOT_BUS_NUMBER_2	0x0
#define SYSTEM_SLOT_BUS_NUMBER_3	0x0
#define SYSTEM_SLOT_BUS_NUMBER_4	0x0
#define SYSTEM_SLOT_BUS_NUMBER_5	0x0
#define SYSTEM_SLOT_BUS_NUMBER_6	0x0
#define SYSTEM_SLOT_BUS_NUMBER_7	0x0
#define SYSTEM_SLOT_BUS_NUMBER_8	0x0
#define SYSTEM_SLOT_BUS_NUMBER_9	0x0
#define SYSTEM_SLOT_BUS_NUMBER_10	0x0
#define SYSTEM_SLOT_DEV_FN_1	0x0
#define SYSTEM_SLOT_DEV_FN_2	0x0
#define SYSTEM_SLOT_DEV_FN_3	0x0
#define SYSTEM_SLOT_DEV_FN_4	0x0
#define SYSTEM_SLOT_DEV_FN_5	0x0
#define SYSTEM_SLOT_DEV_FN_6	0x0
#define SYSTEM_SLOT_DEV_FN_7	0x0
#define SYSTEM_SLOT_DEV_FN_8	0x0
#define SYSTEM_SLOT_DEV_FN_9	0x0
#define SYSTEM_SLOT_DEV_FN_10	0x0
#define SLOT_DESIGNATION_1	"Slot 1"
#define SLOT_DESIGNATION_2	"Slot 2"
#define SLOT_DESIGNATION_3	"Slot 3"
#define SLOT_DESIGNATION_4	"Slot 4"
#define SLOT_DESIGNATION_5	"Slot 5"
#define SLOT_DESIGNATION_6	"Slot 6"
#define SLOT_DESIGNATION_7	"Slot 7"
#define SLOT_DESIGNATION_8	"Slot 8"
#define SLOT_DESIGNATION_9	"Slot 9"
#define SLOT_DESIGNATION_10	"Slot 10"
#define ONBOARD_DEVICE_INFO	1
#define TYPE10_STRUCTURE	0x1
#define DEVICE_DESCRIPTION_1	"Device 1"
#define DEVICE_DESCRIPTION_2	"Device 2"
#define DEVICE_DESCRIPTION_3	"Device 3"
#define DEVICE_DESCRIPTION_4	"Device 4"
#define DEVICE_DESCRIPTION_5	"Device 5"
#define DEVICE_DESCRIPTION_6	"Device 6"
#define DEVICE_DESCRIPTION_7	"Device 7"
#define DEVICE_DESCRIPTION_8	"Device 8"
#define DEVICE_DESCRIPTION_9	"Device 9"
#define DEVICE_DESCRIPTION_10	"Device 10"
#define OEM_STRING_INFO	1
#define TYPE11_STRUCTURE	0x1
#define NUMBER_OF_OEM_STRINGS	0x1
#define OEM_STRING_1	"Default string"
#define OEM_STRING_2	"Default string"
#define OEM_STRING_3	"Default string"
#define OEM_STRING_4	"Default string"
#define OEM_STRING_5	"Default string"
#define OEM_STRING_6	"Default string"
#define OEM_STRING_7	"Default string"
#define OEM_STRING_8	"Default string"
#define OEM_STRING_9	"Default string"
#define OEM_STRING_10	"Default string"
#define OEM_STRING_11	"Default string"
#define OEM_STRING_12	"Default string"
#define OEM_STRING_13	"Default string"
#define OEM_STRING_14	"Default string"
#define OEM_STRING_15	"Default string"
#define OEM_STRING_16	"Default string"
#define OEM_STRING_17	"Default string"
#define OEM_STRING_18	"Default string"
#define OEM_STRING_19	"Default string"
#define OEM_STRING_20	"Default string"
#define OEM_STRING_21	"Default string"
#define OEM_STRING_22	"Default string"
#define OEM_STRING_23	"Default string"
#define OEM_STRING_24	"Default string"
#define OEM_STRING_25	"Default string"
#define OEM_STRING_26	"Default string"
#define OEM_STRING_27	"Default string"
#define OEM_STRING_28	"Default string"
#define OEM_STRING_29	"Default string"
#define OEM_STRING_30	"Default string"
#define OEM_STRING_31	"Default string"
#define OEM_STRING_32	"Default string"
#define SYSTEM_CONFIG_OPTION_INFO	1
#define TYPE12_STRUCTURE	0x1
#define SYSTEM_CONFIG_STRING_1	"Default string"
#define SYSTEM_CONFIG_STRING_2	"Default string"
#define SYSTEM_CONFIG_STRING_3	"Default string"
#define SYSTEM_CONFIG_STRING_4	"Default string"
#define SYSTEM_CONFIG_STRING_5	"Default string"
#define SYSTEM_CONFIG_STRING_6	"Default string"
#define SYSTEM_CONFIG_STRING_7	"Default string"
#define SYSTEM_CONFIG_STRING_8	"Default string"
#define SYSTEM_CONFIG_STRING_9	"Default string"
#define SYSTEM_CONFIG_STRING_10	"Default string"
#define BIOS_LANGUAGE_INFO	1
#define TYPE13_STRUCTURE	0x1
#define NUMBER_OF_LANGUAGES	0x1
#define BIOS_LANGUAGE_FORMAT	0
#define LANGUAGE_1	"Default string"
#define LANGUAGE_2	"Default string"
#define LANGUAGE_3	"Default string"
#define LANGUAGE_4	"Default string"
#define LANGUAGE_5	"Default string"
#define EVENT_LOG_INFO	0
#define TYPE15_STRUCTURE	0x0
#define NO_OF_SUPPORTED_EVENTS	0x10
#define FLASH_MEMORY_ARRAY_INFO	0
#define A1_LOC_1	0x3
#define A1_USE_1	0x3
#define A1_MEM_ERROR_CORRECTION_1	0x3
#define A1_MAX_CAPACITY_1	0x400000
#define A1_EXT_MAX_CAPACITY_1	0x0
#define A1_TOTAL_WIDTH_1	0xffff
#define A1_TOTAL_WIDTH_2	0xffff
#define A1_TOTAL_WIDTH_3	0xffff
#define A1_TOTAL_WIDTH_4	0xffff
#define A1_TOTAL_WIDTH_5	0xffff
#define A1_TOTAL_WIDTH_6	0xffff
#define A1_TOTAL_WIDTH_7	0xffff
#define A1_TOTAL_WIDTH_8	0xffff
#define A1_TOTAL_WIDTH_9	0xffff
#define A1_TOTAL_WIDTH_10	0xffff
#define A1_TOTAL_WIDTH_11	0xffff
#define A1_TOTAL_WIDTH_12	0xffff
#define A1_TOTAL_WIDTH_13	0xffff
#define A1_TOTAL_WIDTH_14	0xffff
#define A1_TOTAL_WIDTH_15	0xffff
#define A1_TOTAL_WIDTH_16	0xffff
#define A1_DATA_WIDTH_1	0xffff
#define A1_DATA_WIDTH_2	0xffff
#define A1_DATA_WIDTH_3	0xffff
#define A1_DATA_WIDTH_4	0xffff
#define A1_DATA_WIDTH_5	0xffff
#define A1_DATA_WIDTH_6	0xffff
#define A1_DATA_WIDTH_7	0xffff
#define A1_DATA_WIDTH_8	0xffff
#define A1_DATA_WIDTH_9	0xffff
#define A1_DATA_WIDTH_10	0xffff
#define A1_DATA_WIDTH_11	0xffff
#define A1_DATA_WIDTH_12	0xffff
#define A1_DATA_WIDTH_13	0xffff
#define A1_DATA_WIDTH_14	0xffff
#define A1_DATA_WIDTH_15	0xffff
#define A1_DATA_WIDTH_16	0xffff
#define A1_FORM_FACTOR_1	0x9
#define A1_FORM_FACTOR_2	0x9
#define A1_FORM_FACTOR_3	0x9
#define A1_FORM_FACTOR_4	0x9
#define A1_FORM_FACTOR_5	0x9
#define A1_FORM_FACTOR_6	0x9
#define A1_FORM_FACTOR_7	0x9
#define A1_FORM_FACTOR_8	0x9
#define A1_FORM_FACTOR_9	0x9
#define A1_FORM_FACTOR_10	0x9
#define A1_FORM_FACTOR_11	0x9
#define A1_FORM_FACTOR_12	0x9
#define A1_FORM_FACTOR_13	0x9
#define A1_FORM_FACTOR_14	0x9
#define A1_FORM_FACTOR_15	0x9
#define A1_FORM_FACTOR_16	0x9
#define A1_DEVICE_SET_1	0xff
#define A1_DEVICE_SET_2	0xff
#define A1_DEVICE_SET_3	0xff
#define A1_DEVICE_SET_4	0xff
#define A1_DEVICE_SET_5	0xff
#define A1_DEVICE_SET_6	0xff
#define A1_DEVICE_SET_7	0xff
#define A1_DEVICE_SET_8	0xff
#define A1_DEVICE_SET_9	0xff
#define A1_DEVICE_SET_10	0xff
#define A1_DEVICE_SET_11	0xff
#define A1_DEVICE_SET_12	0xff
#define A1_DEVICE_SET_13	0xff
#define A1_DEVICE_SET_14	0xff
#define A1_DEVICE_SET_15	0xff
#define A1_DEVICE_SET_16	0xff
#define A1_DEV_LOCATOR_1	"DIMM 1"
#define A1_DEV_LOCATOR_2	"DIMM 2"
#define A1_DEV_LOCATOR_3	"DIMM 3"
#define A1_DEV_LOCATOR_4	"DIMM 4"
#define A1_DEV_LOCATOR_5	"DIMM 5"
#define A1_DEV_LOCATOR_6	"DIMM 6"
#define A1_DEV_LOCATOR_7	"DIMM 7"
#define A1_DEV_LOCATOR_8	"DIMM 8"
#define A1_DEV_LOCATOR_9	"DIMM 9"
#define A1_DEV_LOCATOR_11	"DIMM 11"
#define A1_DEV_LOCATOR_12	"DIMM 12"
#define A1_DEV_LOCATOR_13	"DIMM 13"
#define A1_DEV_LOCATOR_14	"DIMM 14"
#define A1_DEV_LOCATOR_15	"DIMM 15"
#define A1_DEV_LOCATOR_16	"DIMM 16"
#define A1_BANK_LOCATOR_1	"Bank 1"
#define A1_BANK_LOCATOR_2	"Bank 2"
#define A1_BANK_LOCATOR_3	"Bank 3"
#define A1_BANK_LOCATOR_4	"Bank 4"
#define A1_BANK_LOCATOR_5	"Bank 5"
#define A1_BANK_LOCATOR_6	"Bank 6"
#define A1_BANK_LOCATOR_7	"Bank 7"
#define A1_BANK_LOCATOR_8	"Bank 8"
#define A1_BANK_LOCATOR_9	"Bank 9"
#define A1_BANK_LOCATOR_10	"Bank 10"
#define A1_BANK_LOCATOR_11	"Bank 11"
#define A1_BANK_LOCATOR_12	"Bank 12"
#define A1_BANK_LOCATOR_13	"Bank 13"
#define A1_BANK_LOCATOR_14	"Bank 14"
#define A1_BANK_LOCATOR_15	"Bank 15"
#define A1_BANK_LOCATOR_16	"Bank 16"
#define A1_MEMORY_TYPE_1	0x18
#define A1_MEMORY_TYPE_2	0x18
#define A1_MEMORY_TYPE_3	0x18
#define A1_MEMORY_TYPE_4	0x18
#define A1_MEMORY_TYPE_5	0x18
#define A1_MEMORY_TYPE_6	0x18
#define A1_MEMORY_TYPE_7	0x18
#define A1_MEMORY_TYPE_8	0x18
#define A1_MEMORY_TYPE_9	0x18
#define A1_MEMORY_TYPE_10	0x18
#define A1_MEMORY_TYPE_11	0x18
#define A1_MEMORY_TYPE_12	0x18
#define A1_MEMORY_TYPE_13	0x18
#define A1_MEMORY_TYPE_14	0x18
#define A1_MEMORY_TYPE_15	0x18
#define A1_MEMORY_TYPE_16	0x18
#define A1_TYPE_DETAIL_1	0x4
#define A1_TYPE_DETAIL_2	0x4
#define A1_TYPE_DETAIL_3	0x4
#define A1_TYPE_DETAIL_4	0x4
#define A1_TYPE_DETAIL_5	0x4
#define A1_TYPE_DETAIL_6	0x4
#define A1_TYPE_DETAIL_7	0x4
#define A1_TYPE_DETAIL_8	0x4
#define A1_TYPE_DETAIL_9	0x4
#define A1_TYPE_DETAIL_10	0x4
#define A1_TYPE_DETAIL_11	0x4
#define A1_TYPE_DETAIL_12	0x4
#define A1_TYPE_DETAIL_13	0x4
#define A1_TYPE_DETAIL_14	0x4
#define A1_TYPE_DETAIL_15	0x4
#define A1_TYPE_DETAIL_16	0x4
#define A1_SPEED_1	0x4
#define A1_SPEED_2	0x4
#define A1_SPEED_3	0x4
#define A1_SPEED_4	0x4
#define A1_SPEED_5	0x4
#define A1_SPEED_6	0x4
#define A1_SPEED_7	0x4
#define A1_SPEED_8	0x4
#define A1_SPEED_9	0x4
#define A1_SPEED_10	0x4
#define A1_SPEED_11	0x4
#define A1_SPEED_12	0x4
#define A1_SPEED_13	0x4
#define A1_SPEED_14	0x4
#define A1_SPEED_15	0x4
#define A1_SPEED_16	0x4
#define A1_MANAFACTURER_1	"Array 1 Manufacturer 1"
#define A1_MANAFACTURER_2	"Array 1 Manufacturer 2"
#define A1_MANAFACTURER_3	"Array 1 Manufacturer 3"
#define A1_MANAFACTURER_4	"Array 1 Manufacturer 4"
#define A1_MANAFACTURER_5	"Array 1 Manufacturer 5"
#define A1_MANAFACTURER_6	"Array 1 Manufacturer 6"
#define A1_MANAFACTURER_7	"Array 1 Manufacturer 7"
#define A1_MANAFACTURER_8	"Array 1 Manufacturer 8"
#define A1_MANAFACTURER_9	"Array 1 Manufacturer 9"
#define A1_MANAFACTURER_10	"Array 1 Manufacturer 10"
#define A1_MANAFACTURER_11	"Array 1 Manufacturer 11"
#define A1_MANAFACTURER_12	"Array 1 Manufacturer 12"
#define A1_MANAFACTURER_13	"Array 1 Manufacturer 13"
#define A1_MANAFACTURER_14	"Array 1 Manufacturer 14"
#define A1_MANAFACTURER_15	"Array 1 Manufacturer 15"
#define A1_MANAFACTURER_16	"Array 1 Manufacturer 16"
#define A1_SERIAL_NUMBER_1	"Array 1 Serial Number 1"
#define A1_SERIAL_NUMBER_2	"Array 1 Serial Number 2"
#define A1_SERIAL_NUMBER_3	"Array 1 Serial Number 3"
#define A1_SERIAL_NUMBER_4	"Array 1 Serial Number 4"
#define A1_SERIAL_NUMBER_5	"Array 1 Serial Number 5"
#define A1_SERIAL_NUMBER_6	"Array 1 Serial Number 6"
#define A1_SERIAL_NUMBER_7	"Array 1 Serial Number 7"
#define A1_SERIAL_NUMBER_8	"Array 1 Serial Number 8"
#define A1_SERIAL_NUMBER_9	"Array 1 Serial Number 9"
#define A1_SERIAL_NUMBER_10	"Array 1 Serial Number 10"
#define A1_SERIAL_NUMBER_11	"Array 1 Serial Number 11"
#define A1_SERIAL_NUMBER_12	"Array 1 Serial Number 12"
#define A1_SERIAL_NUMBER_13	"Array 1 Serial Number 13"
#define A1_SERIAL_NUMBER_14	"Array 1 Serial Number 14"
#define A1_SERIAL_NUMBER_15	"Array 1 Serial Number 15"
#define A1_SERIAL_NUMBER_16	"Array 1 Serial Number 16"
#define A1_ASSET_TAG_1	"Array 1 Asset Tag 1"
#define A1_ASSET_TAG_2	"Array 1 Asset Tag 2"
#define A1_ASSET_TAG_3	"Array 1 Asset Tag 3"
#define A1_ASSET_TAG_4	"Array 1 Asset Tag 4"
#define A1_ASSET_TAG_5	"Array 1 Asset Tag 5"
#define A1_ASSET_TAG_6	"Array 1 Asset Tag 6"
#define A1_ASSET_TAG_7	"Array 1 Asset Tag 7"
#define A1_ASSET_TAG_8	"Array 1 Asset Tag 8"
#define A1_ASSET_TAG_9	"Array 1 Asset Tag 9"
#define A1_ASSET_TAG_10	"Array 1 Asset Tag 10"
#define A1_ASSET_TAG_11	"Array 1 Asset Tag 11"
#define A1_ASSET_TAG_12	"Array 1 Asset Tag 12"
#define A1_ASSET_TAG_13	"Array 1 Asset Tag 13"
#define A1_ASSET_TAG_14	"Array 1 Asset Tag 14"
#define A1_ASSET_TAG_15	"Array 1 Asset Tag 15"
#define A1_ASSET_TAG_16	"Array 1 Asset Tag 16"
#define A1_PART_NUMBER_1	"Array 1 Part Number 1"
#define A1_PART_NUMBER_2	"Array 1 Part Number 2"
#define A1_PART_NUMBER_3	"Array 1 Part Number 3"
#define A1_PART_NUMBER_4	"Array 1 Part Number 4"
#define A1_PART_NUMBER_5	"Array 1 Part Number 5"
#define A1_PART_NUMBER_6	"Array 1 Part Number 6"
#define A1_PART_NUMBER_7	"Array 1 Part Number 7"
#define A1_PART_NUMBER_8	"Array 1 Part Number 8"
#define A1_PART_NUMBER_9	"Array 1 Part Number 9"
#define A1_PART_NUMBER_10	"Array 1 Part Number 10"
#define A1_PART_NUMBER_11	"Array 1 Part Number 11"
#define A1_PART_NUMBER_12	"Array 1 Part Number 12"
#define A1_PART_NUMBER_13	"Array 1 Part Number 13"
#define A1_PART_NUMBER_14	"Array 1 Part Number 14"
#define A1_PART_NUMBER_15	"Array 1 Part Number 15"
#define A1_PART_NUMBER_16	"Array 1 Part Number 16"
#define A1_ATTRIBUTES_1	0x0
#define A1_ATTRIBUTES_2	0x0
#define A1_ATTRIBUTES_3	0x0
#define A1_ATTRIBUTES_4	0x0
#define A1_ATTRIBUTES_5	0x0
#define A1_ATTRIBUTES_6	0x0
#define A1_ATTRIBUTES_7	0x0
#define A1_ATTRIBUTES_8	0x0
#define A1_ATTRIBUTES_9	0x0
#define A1_ATTRIBUTES_10	0x0
#define A1_ATTRIBUTES_11	0x0
#define A1_ATTRIBUTES_12	0x0
#define A1_ATTRIBUTES_13	0x0
#define A1_ATTRIBUTES_14	0x0
#define A1_ATTRIBUTES_15	0x0
#define A1_ATTRIBUTES_16	0x0
#define A1_CONF_MEM_CLK_SPEED_1	0x0
#define A1_CONF_MEM_CLK_SPEED_2	0x0
#define A1_CONF_MEM_CLK_SPEED_3	0x0
#define A1_CONF_MEM_CLK_SPEED_4	0x0
#define A1_CONF_MEM_CLK_SPEED_5	0x0
#define A1_CONF_MEM_CLK_SPEED_6	0x0
#define A1_CONF_MEM_CLK_SPEED_7	0x0
#define A1_CONF_MEM_CLK_SPEED_8	0x0
#define A1_CONF_MEM_CLK_SPEED_9	0x0
#define A1_CONF_MEM_CLK_SPEED_10	0x0
#define A1_CONF_MEM_CLK_SPEED_11	0x0
#define A1_CONF_MEM_CLK_SPEED_12	0x0
#define A1_CONF_MEM_CLK_SPEED_13	0x0
#define A1_CONF_MEM_CLK_SPEED_14	0x0
#define A1_CONF_MEM_CLK_SPEED_15	0x0
#define A1_CONF_MEM_CLK_SPEED_16	0x0
#define A1_MIN_VOLTAGE_1	0x0
#define A1_MIN_VOLTAGE_2	0x0
#define A1_MIN_VOLTAGE_3	0x0
#define A1_MIN_VOLTAGE_4	0x0
#define A1_MIN_VOLTAGE_5	0x0
#define A1_MIN_VOLTAGE_6	0x0
#define A1_MIN_VOLTAGE_7	0x0
#define A1_MIN_VOLTAGE_8	0x0
#define A1_MIN_VOLTAGE_9	0x0
#define A1_MIN_VOLTAGE_10	0x0
#define A1_MIN_VOLTAGE_11	0x0
#define A1_MIN_VOLTAGE_12	0x0
#define A1_MIN_VOLTAGE_13	0x0
#define A1_MIN_VOLTAGE_14	0x0
#define A1_MIN_VOLTAGE_15	0x0
#define A1_MIN_VOLTAGE_16	0x0
#define A1_MAX_VOLTAGE_1	0x0
#define A1_MAX_VOLTAGE_2	0x0
#define A1_MAX_VOLTAGE_3	0x0
#define A1_MAX_VOLTAGE_4	0x0
#define A1_MAX_VOLTAGE_5	0x0
#define A1_MAX_VOLTAGE_6	0x0
#define A1_MAX_VOLTAGE_7	0x0
#define A1_MAX_VOLTAGE_8	0x0
#define A1_MAX_VOLTAGE_9	0x0
#define A1_MAX_VOLTAGE_10	0x0
#define A1_MAX_VOLTAGE_11	0x0
#define A1_MAX_VOLTAGE_12	0x0
#define A1_MAX_VOLTAGE_13	0x0
#define A1_MAX_VOLTAGE_14	0x0
#define A1_MAX_VOLTAGE_15	0x0
#define A1_MAX_VOLTAGE_16	0x0
#define A1_CONF_VOLTAGE_1	0x0
#define A1_CONF_VOLTAGE_2	0x0
#define A1_CONF_VOLTAGE_3	0x0
#define A1_CONF_VOLTAGE_4	0x0
#define A1_CONF_VOLTAGE_5	0x0
#define A1_CONF_VOLTAGE_6	0x0
#define A1_CONF_VOLTAGE_7	0x0
#define A1_CONF_VOLTAGE_8	0x0
#define A1_CONF_VOLTAGE_9	0x0
#define A1_CONF_VOLTAGE_10	0x0
#define A1_CONF_VOLTAGE_11	0x0
#define A1_CONF_VOLTAGE_12	0x0
#define A1_CONF_VOLTAGE_13	0x0
#define A1_CONF_VOLTAGE_14	0x0
#define A1_CONF_VOLTAGE_15	0x0
#define A1_CONF_VOLTAGE_16	0x0
#define A1_MEMORY_TECHNOLOGY_1	0x2
#define A1_MEMORY_TECHNOLOGY_2	0x2
#define A1_MEMORY_TECHNOLOGY_3	0x2
#define A1_MEMORY_TECHNOLOGY_4	0x2
#define A1_MEMORY_TECHNOLOGY_5	0x2
#define A1_MEMORY_TECHNOLOGY_6	0x2
#define A1_MEMORY_TECHNOLOGY_7	0x2
#define A1_MEMORY_TECHNOLOGY_8	0x2
#define A1_MEMORY_TECHNOLOGY_9	0x2
#define A1_MEMORY_TECHNOLOGY_10	0x2
#define A1_MEMORY_TECHNOLOGY_11	0x2
#define A1_MEMORY_TECHNOLOGY_12	0x2
#define A1_MEMORY_TECHNOLOGY_13	0x2
#define A1_MEMORY_TECHNOLOGY_14	0x2
#define A1_MEMORY_TECHNOLOGY_15	0x2
#define A1_MEMORY_TECHNOLOGY_16	0x2
#define A1_MEMORY_OPMODE_CAP_1	0x4
#define A1_MEMORY_OPMODE_CAP_2	0x4
#define A1_MEMORY_OPMODE_CAP_3	0x4
#define A1_MEMORY_OPMODE_CAP_4	0x4
#define A1_MEMORY_OPMODE_CAP_5	0x4
#define A1_MEMORY_OPMODE_CAP_6	0x4
#define A1_MEMORY_OPMODE_CAP_7	0x4
#define A1_MEMORY_OPMODE_CAP_8	0x4
#define A1_MEMORY_OPMODE_CAP_9	0x4
#define A1_MEMORY_OPMODE_CAP_10	0x4
#define A1_MEMORY_OPMODE_CAP_11	0x4
#define A1_MEMORY_OPMODE_CAP_12	0x4
#define A1_MEMORY_OPMODE_CAP_13	0x4
#define A1_MEMORY_OPMODE_CAP_14	0x4
#define A1_MEMORY_OPMODE_CAP_15	0x4
#define A1_MEMORY_OPMODE_CAP_16	0x4
#define A1_EXTENDED_SPEED_1	0x0
#define A1_EXTENDED_SPEED_2	0x0
#define A1_EXTENDED_SPEED_3	0x0
#define A1_EXTENDED_SPEED_4	0x0
#define A1_EXTENDED_SPEED_5	0x0
#define A1_EXTENDED_SPEED_6	0x0
#define A1_EXTENDED_SPEED_7	0x0
#define A1_EXTENDED_SPEED_8	0x0
#define A1_EXTENDED_SPEED_9	0x0
#define A1_EXTENDED_SPEED_10	0x0
#define A1_EXTENDED_SPEED_11	0x0
#define A1_EXTENDED_SPEED_12	0x0
#define A1_EXTENDED_SPEED_13	0x0
#define A1_EXTENDED_SPEED_14	0x0
#define A1_EXTENDED_SPEED_15	0x0
#define A1_EXTENDED_SPEED_16	0x0
#define A1_EXTENDED_CONF_MEM_CLK_SPEED_1	0x0
#define A1_EXTENDED_CONF_MEM_CLK_SPEED_2	0x0
#define A1_EXTENDED_CONF_MEM_CLK_SPEED_3	0x0
#define A1_EXTENDED_CONF_MEM_CLK_SPEED_4	0x0
#define A1_EXTENDED_CONF_MEM_CLK_SPEED_5	0x0
#define A1_EXTENDED_CONF_MEM_CLK_SPEED_6	0x0
#define A1_EXTENDED_CONF_MEM_CLK_SPEED_7	0x0
#define A1_EXTENDED_CONF_MEM_CLK_SPEED_8	0x0
#define A1_EXTENDED_CONF_MEM_CLK_SPEED_9	0x0
#define A1_EXTENDED_CONF_MEM_CLK_SPEED_10	0x0
#define A1_EXTENDED_CONF_MEM_CLK_SPEED_11	0x0
#define A1_EXTENDED_CONF_MEM_CLK_SPEED_12	0x0
#define A1_EXTENDED_CONF_MEM_CLK_SPEED_13	0x0
#define A1_EXTENDED_CONF_MEM_CLK_SPEED_14	0x0
#define A1_EXTENDED_CONF_MEM_CLK_SPEED_15	0x0
#define A1_EXTENDED_CONF_MEM_CLK_SPEED_16	0x0
#define A1_PMIC0_MANUFACTURER_ID_1	0x0
#define A1_PMIC0_MANUFACTURER_ID_2	0x0
#define A1_PMIC0_MANUFACTURER_ID_3	0x0
#define A1_PMIC0_MANUFACTURER_ID_4	0x0
#define A1_PMIC0_MANUFACTURER_ID_5	0x0
#define A1_PMIC0_MANUFACTURER_ID_6	0x0
#define A1_PMIC0_MANUFACTURER_ID_7	0x0
#define A1_PMIC0_MANUFACTURER_ID_8	0x0
#define A1_PMIC0_MANUFACTURER_ID_9	0x0
#define A1_PMIC0_MANUFACTURER_ID_10	0x0
#define A1_PMIC0_MANUFACTURER_ID_11	0x0
#define A1_PMIC0_MANUFACTURER_ID_12	0x0
#define A1_PMIC0_MANUFACTURER_ID_13	0x0
#define A1_PMIC0_MANUFACTURER_ID_14	0x0
#define A1_PMIC0_MANUFACTURER_ID_15	0x0
#define A1_PMIC0_MANUFACTURER_ID_16	0x0
#define A1_PMIC0_REVISION_NUMBER_1	0xff00
#define A1_PMIC0_REVISION_NUMBER_2	0xff00
#define A1_PMIC0_REVISION_NUMBER_3	0xff00
#define A1_PMIC0_REVISION_NUMBER_4	0xff00
#define A1_PMIC0_REVISION_NUMBER_5	0xff00
#define A1_PMIC0_REVISION_NUMBER_6	0xff00
#define A1_PMIC0_REVISION_NUMBER_7	0xff00
#define A1_PMIC0_REVISION_NUMBER_8	0xff00
#define A1_PMIC0_REVISION_NUMBER_9	0xff00
#define A1_PMIC0_REVISION_NUMBER_10	0xff00
#define A1_PMIC0_REVISION_NUMBER_11	0xff00
#define A1_PMIC0_REVISION_NUMBER_12	0xff00
#define A1_PMIC0_REVISION_NUMBER_13	0xff00
#define A1_PMIC0_REVISION_NUMBER_14	0xff00
#define A1_PMIC0_REVISION_NUMBER_15	0xff00
#define A1_PMIC0_REVISION_NUMBER_16	0xff00
#define A1_RCD_MANUFACTURER_ID_1	0x0
#define A1_RCD_MANUFACTURER_ID_2	0x0
#define A1_RCD_MANUFACTURER_ID_3	0x0
#define A1_RCD_MANUFACTURER_ID_4	0x0
#define A1_RCD_MANUFACTURER_ID_5	0x0
#define A1_RCD_MANUFACTURER_ID_6	0x0
#define A1_RCD_MANUFACTURER_ID_7	0x0
#define A1_RCD_MANUFACTURER_ID_8	0x0
#define A1_RCD_MANUFACTURER_ID_9	0x0
#define A1_RCD_MANUFACTURER_ID_10	0x0
#define A1_RCD_MANUFACTURER_ID_11	0x0
#define A1_RCD_MANUFACTURER_ID_12	0x0
#define A1_RCD_MANUFACTURER_ID_13	0x0
#define A1_RCD_MANUFACTURER_ID_14	0x0
#define A1_RCD_MANUFACTURER_ID_15	0x0
#define A1_RCD_MANUFACTURER_ID_16	0x0
#define A1_RCD_REVISION_NUMBER_1	0xff00
#define A1_RCD_REVISION_NUMBER_2	0xff00
#define A1_RCD_REVISION_NUMBER_3	0xff00
#define A1_RCD_REVISION_NUMBER_4	0xff00
#define A1_RCD_REVISION_NUMBER_5	0xff00
#define A1_RCD_REVISION_NUMBER_6	0xff00
#define A1_RCD_REVISION_NUMBER_7	0xff00
#define A1_RCD_REVISION_NUMBER_8	0xff00
#define A1_RCD_REVISION_NUMBER_9	0xff00
#define A1_RCD_REVISION_NUMBER_10	0xff00
#define A1_RCD_REVISION_NUMBER_11	0xff00
#define A1_RCD_REVISION_NUMBER_12	0xff00
#define A1_RCD_REVISION_NUMBER_13	0xff00
#define A1_RCD_REVISION_NUMBER_14	0xff00
#define A1_RCD_REVISION_NUMBER_15	0xff00
#define A1_RCD_REVISION_NUMBER_16	0xff00
#define MEMORY_ERROR_INFO	0
#define TYPE18_STRUCTURE	0x0
#define MEMORY_ERR_TYPE	0x2
#define ERROR_GRANULARITY	0x2
#define ERROR_OPERATION	0x2
#define VENDOR_SYNDROME	0x0
#define MEMORY_ARRAY_ERROR_ADDRESS	0x80000000
#define DEVICE_ERROR_ADDRESS	0x80000000
#define ERROR_RESOLUTION	0x80000000
#define A1_MEMORY_ERROR_TYPE	0x2
#define A1_TYPE17_MEM_ERROR_TYPE_1	0x2
#define A1_TYPE17_MEM_ERROR_TYPE_2	0x2
#define A1_TYPE17_MEM_ERROR_TYPE_3	0x2
#define A1_TYPE17_MEM_ERROR_TYPE_4	0x2
#define A1_TYPE17_MEM_ERROR_TYPE_5	0x2
#define A1_TYPE17_MEM_ERROR_TYPE_6	0x2
#define A1_TYPE17_MEM_ERROR_TYPE_7	0x2
#define A1_TYPE17_MEM_ERROR_TYPE_8	0x2
#define A1_TYPE17_MEM_ERROR_TYPE_9	0x2
#define A1_TYPE17_MEM_ERROR_TYPE_10	0x2
#define A1_TYPE17_MEM_ERROR_TYPE_11	0x2
#define A1_TYPE17_MEM_ERROR_TYPE_12	0x2
#define A1_TYPE17_MEM_ERROR_TYPE_13	0x2
#define A1_TYPE17_MEM_ERROR_TYPE_14	0x2
#define A1_TYPE17_MEM_ERROR_TYPE_15	0x2
#define A1_TYPE17_MEM_ERROR_TYPE_16	0x2
#define A1_ERROR_GRANULARITY	0x2
#define A1_TYPE17_ERROR_GRANULARITY_1	0x2
#define A1_TYPE17_ERROR_GRANULARITY_2	0x2
#define A1_TYPE17_ERROR_GRANULARITY_3	0x2
#define A1_TYPE17_ERROR_GRANULARITY_4	0x2
#define A1_TYPE17_ERROR_GRANULARITY_5	0x2
#define A1_TYPE17_ERROR_GRANULARITY_6	0x2
#define A1_TYPE17_ERROR_GRANULARITY_7	0x2
#define A1_TYPE17_ERROR_GRANULARITY_8	0x2
#define A1_TYPE17_ERROR_GRANULARITY_9	0x2
#define A1_TYPE17_ERROR_GRANULARITY_10	0x2
#define A1_TYPE17_ERROR_GRANULARITY_11	0x2
#define A1_TYPE17_ERROR_GRANULARITY_12	0x2
#define A1_TYPE17_ERROR_GRANULARITY_13	0x2
#define A1_TYPE17_ERROR_GRANULARITY_14	0x2
#define A1_TYPE17_ERROR_GRANULARITY_15	0x2
#define A1_TYPE17_ERROR_GRANULARITY_16	0x2
#define A1_ERROR_OPERATION	0x2
#define A1_TYPE17_ERROR_OPERATION_1	0x2
#define A1_TYPE17_ERROR_OPERATION_2	0x2
#define A1_TYPE17_ERROR_OPERATION_3	0x2
#define A1_TYPE17_ERROR_OPERATION_4	0x2
#define A1_TYPE17_ERROR_OPERATION_5	0x2
#define A1_TYPE17_ERROR_OPERATION_6	0x2
#define A1_TYPE17_ERROR_OPERATION_7	0x2
#define A1_TYPE17_ERROR_OPERATION_8	0x2
#define A1_TYPE17_ERROR_OPERATION_9	0x2
#define A1_TYPE17_ERROR_OPERATION_10	0x2
#define A1_TYPE17_ERROR_OPERATION_11	0x2
#define A1_TYPE17_ERROR_OPERATION_12	0x2
#define A1_TYPE17_ERROR_OPERATION_13	0x2
#define A1_TYPE17_ERROR_OPERATION_14	0x2
#define A1_TYPE17_ERROR_OPERATION_15	0x2
#define A1_TYPE17_ERROR_OPERATION_16	0x2
#define A1_VENDOR_SYNDROME	0x0
#define A1_TYPE17_VENDOR_SYNDROME_1	0x0
#define A1_TYPE17_VENDOR_SYNDROME_2	0x0
#define A1_TYPE17_VENDOR_SYNDROME_3	0x0
#define A1_TYPE17_VENDOR_SYNDROME_4	0x0
#define A1_TYPE17_VENDOR_SYNDROME_5	0x0
#define A1_TYPE17_VENDOR_SYNDROME_6	0x0
#define A1_TYPE17_VENDOR_SYNDROME_7	0x0
#define A1_TYPE17_VENDOR_SYNDROME_8	0x0
#define A1_TYPE17_VENDOR_SYNDROME_9	0x0
#define A1_TYPE17_VENDOR_SYNDROME_10	0x0
#define A1_TYPE17_VENDOR_SYNDROME_11	0x0
#define A1_TYPE17_VENDOR_SYNDROME_12	0x0
#define A1_TYPE17_VENDOR_SYNDROME_13	0x0
#define A1_TYPE17_VENDOR_SYNDROME_14	0x0
#define A1_TYPE17_VENDOR_SYNDROME_15	0x0
#define A1_TYPE17_VENDOR_SYNDROME_16	0x0
#define A1_MEMORY_ARRAY_ERROR_ADDRESS	0x80000000
#define A1_TYPE17_MEM_ARRAY_ERROR_ADDR_1	0x80000000
#define A1_TYPE17_MEM_ARRAY_ERROR_ADDR_2	0x80000000
#define A1_TYPE17_MEM_ARRAY_ERROR_ADDR_3	0x80000000
#define A1_TYPE17_MEM_ARRAY_ERROR_ADDR_4	0x80000000
#define A1_TYPE17_MEM_ARRAY_ERROR_ADDR_5	0x80000000
#define A1_TYPE17_MEM_ARRAY_ERROR_ADDR_6	0x80000000
#define A1_TYPE17_MEM_ARRAY_ERROR_ADDR_7	0x80000000
#define A1_TYPE17_MEM_ARRAY_ERROR_ADDR_8	0x80000000
#define A1_TYPE17_MEM_ARRAY_ERROR_ADDR_9	0x80000000
#define A1_TYPE17_MEM_ARRAY_ERROR_ADDR_10	0x80000000
#define A1_TYPE17_MEM_ARRAY_ERROR_ADDR_11	0x80000000
#define A1_TYPE17_MEM_ARRAY_ERROR_ADDR_12	0x80000000
#define A1_TYPE17_MEM_ARRAY_ERROR_ADDR_13	0x80000000
#define A1_TYPE17_MEM_ARRAY_ERROR_ADDR_14	0x80000000
#define A1_TYPE17_MEM_ARRAY_ERROR_ADDR_15	0x80000000
#define A1_TYPE17_MEM_ARRAY_ERROR_ADDR_16	0x80000000
#define A1_DEVICE_ERROR_ADDRESS	0x80000000
#define A1_TYPE17_DEV_ERROR_ADDR_1	0x80000000
#define A1_TYPE17_DEV_ERROR_ADDR_2	0x80000000
#define A1_TYPE17_DEV_ERROR_ADDR_3	0x80000000
#define A1_TYPE17_DEV_ERROR_ADDR_4	0x80000000
#define A1_TYPE17_DEV_ERROR_ADDR_5	0x80000000
#define A1_TYPE17_DEV_ERROR_ADDR_6	0x80000000
#define A1_TYPE17_DEV_ERROR_ADDR_7	0x80000000
#define A1_TYPE17_DEV_ERROR_ADDR_8	0x80000000
#define A1_TYPE17_DEV_ERROR_ADDR_9	0x80000000
#define A1_TYPE17_DEV_ERROR_ADDR_10	0x80000000
#define A1_TYPE17_DEV_ERROR_ADDR_11	0x80000000
#define A1_TYPE17_DEV_ERROR_ADDR_12	0x80000000
#define A1_TYPE17_DEV_ERROR_ADDR_13	0x80000000
#define A1_TYPE17_DEV_ERROR_ADDR_14	0x80000000
#define A1_TYPE17_DEV_ERROR_ADDR_15	0x80000000
#define A1_TYPE17_DEV_ERROR_ADDR_16	0x80000000
#define A1_ERROR_RESOLUTION	0x80000000
#define A1_TYPE17_ERROR_RESOLUTION_1	0x80000000
#define A1_TYPE17_ERROR_RESOLUTION_2	0x80000000
#define A1_TYPE17_ERROR_RESOLUTION_3	0x80000000
#define A1_TYPE17_ERROR_RESOLUTION_4	0x80000000
#define A1_TYPE17_ERROR_RESOLUTION_5	0x80000000
#define A1_TYPE17_ERROR_RESOLUTION_6	0x80000000
#define A1_TYPE17_ERROR_RESOLUTION_7	0x80000000
#define A1_TYPE17_ERROR_RESOLUTION_8	0x80000000
#define A1_TYPE17_ERROR_RESOLUTION_9	0x80000000
#define A1_TYPE17_ERROR_RESOLUTION_10	0x80000000
#define A1_TYPE17_ERROR_RESOLUTION_11	0x80000000
#define A1_TYPE17_ERROR_RESOLUTION_12	0x80000000
#define A1_TYPE17_ERROR_RESOLUTION_13	0x80000000
#define A1_TYPE17_ERROR_RESOLUTION_14	0x80000000
#define A1_TYPE17_ERROR_RESOLUTION_15	0x80000000
#define A1_TYPE17_ERROR_RESOLUTION_16	0x80000000
#define ARRAY_MAPPED_ENDING_ADDRESS	0x0
#define PARTITION_WIDTH	0x0
#define A1_MAPPED_STARTING_ADDR_1	0x0
#define A1_MAPPED_STARTING_ADDR_2	0x0
#define A1_MAPPED_STARTING_ADDR_3	0x0
#define A1_MAPPED_STARTING_ADDR_4	0x0
#define A1_MAPPED_ENDING_ADDR_1	0x0
#define A1_MAPPED_ENDING_ADDR_2	0x0
#define A1_MAPPED_ENDING_ADDR_3	0x0
#define A1_MAPPED_ENDING_ADDR_4	0x0
#define A1_PARTITION_WIDTH_1	0x0
#define A1_PARTITION_WIDTH_2	0x0
#define A1_PARTITION_WIDTH_3	0x0
#define A1_PARTITION_WIDTH_4	0x0
#define TYPE20_STRUCTURE	0x0
#define MEMORY_DEVICE_STARTING_ADDRESS	0x0
#define MEMORY_DEVICE_ENDING_ADDRESS	0x0
#define NO_OF_DEVICES_IN_ROW	0x1
#define INTERLEAVE_DATA_DEPTH	0x0
#define A1_MEM_DEV_STARTING_ADDR_1	0x0
#define A1_MEM_DEV_STARTING_ADDR_2	0x0
#define A1_MEM_DEV_STARTING_ADDR_3	0x0
#define A1_MEM_DEV_STARTING_ADDR_4	0x0
#define A1_MEM_DEV_STARTING_ADDR_5	0x0
#define A1_MEM_DEV_STARTING_ADDR_6	0x0
#define A1_MEM_DEV_STARTING_ADDR_7	0x0
#define A1_MEM_DEV_STARTING_ADDR_8	0x0
#define A1_MEM_DEV_STARTING_ADDR_9	0x0
#define A1_MEM_DEV_STARTING_ADDR_10	0x0
#define A1_MEM_DEV_STARTING_ADDR_11	0x0
#define A1_MEM_DEV_STARTING_ADDR_12	0x0
#define A1_MEM_DEV_STARTING_ADDR_13	0x0
#define A1_MEM_DEV_STARTING_ADDR_14	0x0
#define A1_MEM_DEV_STARTING_ADDR_15	0x0
#define A1_MEM_DEV_STARTING_ADDR_16	0x0
#define A1_MEM_DEV_ENDING_ADDR_1	0x0
#define A1_MEM_DEV_ENDING_ADDR_2	0x0
#define A1_MEM_DEV_ENDING_ADDR_3	0x0
#define A1_MEM_DEV_ENDING_ADDR_4	0x0
#define A1_MEM_DEV_ENDING_ADDR_5	0x0
#define A1_MEM_DEV_ENDING_ADDR_6	0x0
#define A1_MEM_DEV_ENDING_ADDR_7	0x0
#define A1_MEM_DEV_ENDING_ADDR_8	0x0
#define A1_MEM_DEV_ENDING_ADDR_9	0x0
#define A1_MEM_DEV_ENDING_ADDR_10	0x0
#define A1_MEM_DEV_ENDING_ADDR_11	0x0
#define A1_MEM_DEV_ENDING_ADDR_12	0x0
#define A1_MEM_DEV_ENDING_ADDR_13	0x0
#define A1_MEM_DEV_ENDING_ADDR_14	0x0
#define A1_MEM_DEV_ENDING_ADDR_15	0x0
#define A1_MEM_DEV_ENDING_ADDR_16	0x0
#define A1_PARTITION_ROW_POS_1	0x1
#define A1_PARTITION_ROW_POS_2	0x1
#define A1_PARTITION_ROW_POS_3	0x1
#define A1_PARTITION_ROW_POS_4	0x1
#define A1_PARTITION_ROW_POS_5	0x1
#define A1_PARTITION_ROW_POS_6	0x1
#define A1_PARTITION_ROW_POS_7	0x1
#define A1_PARTITION_ROW_POS_8	0x1
#define A1_PARTITION_ROW_POS_9	0x1
#define A1_PARTITION_ROW_POS_10	0x1
#define A1_PARTITION_ROW_POS_11	0x1
#define A1_PARTITION_ROW_POS_12	0x1
#define A1_PARTITION_ROW_POS_13	0x1
#define A1_PARTITION_ROW_POS_14	0x1
#define A1_PARTITION_ROW_POS_15	0x1
#define A1_PARTITION_ROW_POS_16	0x1
#define A1_INTERLEAVE_POS_1	0x1
#define A1_INTERLEAVE_POS_2	0x1
#define A1_INTERLEAVE_POS_3	0x1
#define A1_INTERLEAVE_POS_4	0x1
#define A1_INTERLEAVE_POS_5	0x1
#define A1_INTERLEAVE_POS_6	0x1
#define A1_INTERLEAVE_POS_7	0x1
#define A1_INTERLEAVE_POS_8	0x1
#define A1_INTERLEAVE_POS_9	0x1
#define A1_INTERLEAVE_POS_10	0x1
#define A1_INTERLEAVE_POS_11	0x1
#define A1_INTERLEAVE_POS_12	0x1
#define A1_INTERLEAVE_POS_13	0x1
#define A1_INTERLEAVE_POS_15	0x1
#define A1_INTERLEAVE_POS_16	0x1
#define A1_INTERLEAVE_DATA_DEPTH_1	0x0
#define A1_INTERLEAVE_DATA_DEPTH_2	0x0
#define A1_INTERLEAVE_DATA_DEPTH_3	0x0
#define A1_INTERLEAVE_DATA_DEPTH_4	0x0
#define A1_INTERLEAVE_DATA_DEPTH_5	0x0
#define A1_INTERLEAVE_DATA_DEPTH_6	0x0
#define A1_INTERLEAVE_DATA_DEPTH_7	0x0
#define A1_INTERLEAVE_DATA_DEPTH_8	0x0
#define A1_INTERLEAVE_DATA_DEPTH_9	0x0
#define A1_INTERLEAVE_DATA_DEPTH_10	0x0
#define A1_INTERLEAVE_DATA_DEPTH_11	0x0
#define A1_INTERLEAVE_DATA_DEPTH_12	0x0
#define A1_INTERLEAVE_DATA_DEPTH_13	0x0
#define A1_INTERLEAVE_DATA_DEPTH_14	0x0
#define A1_INTERLEAVE_DATA_DEPTH_15	0x0
#define A1_INTERLEAVE_DATA_DEPTH_16	0x0
#define A1_TYPE20_EXTENDED_STARTING_ADDR_2	0x0
#define A1_TYPE20_EXTENDED_STARTING_ADDR_4	0x0
#define A1_TYPE20_EXTENDED_STARTING_ADDR_6	0x0
#define A1_TYPE20_EXTENDED_STARTING_ADDR_8	0x0
#define A1_TYPE20_EXTENDED_STARTING_ADDR_9	0x0
#define A1_TYPE20_EXTENDED_STARTING_ADDR_10	0x0
#define A1_TYPE20_EXTENDED_STARTING_ADDR_11	0x0
#define A1_TYPE20_EXTENDED_STARTING_ADDR_12	0x0
#define A1_TYPE20_EXTENDED_STARTING_ADDR_13	0x0
#define A1_TYPE20_EXTENDED_STARTING_ADDR_14	0x0
#define A1_TYPE20_EXTENDED_STARTING_ADDR_15	0x0
#define A1_TYPE20_EXTENDED_STARTING_ADDR_16	0x0
#define BUILTIN_POINTING_DEVICE_INFO	0
#define TYPE21_STRUCTURE	0x0
#define NO_OF_POINTING_DEVICE	0x1
#define POINTING_DEV_TYPE_1	0x3
#define POINTING_DEV_INT_1	0x4
#define POINTING_DEV_BUTTONS_1	0x3
#define POINTING_DEV_TYPE_2	0x3
#define POINTING_DEV_INT_2	0x4
#define POINTING_DEV_BUTTONS_2	0x3
#define POINTING_DEV_TYPE_3	0x3
#define POINTING_DEV_INT_3	0x4
#define POINTING_DEV_BUTTONS_3	0x3
#define POINTING_DEV_TYPE_4	0x3
#define POINTING_DEV_INT_4	0x4
#define POINTING_DEV_BUTTONS_4	0x3
#define POINTING_DEV_TYPE_5	0x3
#define POINTING_DEV_INT_5	0x4
#define POINTING_DEV_BUTTONS_5	0x3
#define PORTABLE_BATTERY_INFO	0
#define TYPE22_STRUCTURE	0x0
#define NO_OF_PORTABLE_BATTERY	0x5
#define SYSTEM_RESET_INFO	0
#define TYPE23_STRUCTURE	0x0
#define RESET_CAPABILITIES	0x0
#define RESET_COUNT	0xffff
#define RESET_LIMIT	0xffff
#define RESET_TIMER_INTERVAL	0xffff
#define RESET_TIMEOUT	0xffff
#define HARDWARE_SECURITY_INFO	0
#define TYPE24_STRUCTURE	0x0
#define HARDWARE_SECURITY_SETTINGS	0x0
#define SYSTEM_POWER_CONTROLS_INFO	0
#define TYPE25_STRUCTURE	0x0
#define NEXT_SCHEDULED_POWERON_MONTH	0xff
#define NEXT_SCHEDULED_POWERON_DAY_OF_MONTH	0xff
#define NEXT_SCHEDULED_POWERON_HOUR	0xff
#define NEXT_SCHEDULED_POWERON_MINUTE	0xff
#define NEXT_SCHEDULED_POWERON_SECOND	0xff
#define VOLTAGE_PROBE_INFO	0
#define TYPE26_STRUCTURE	0x0
#define NUMBER_OF_VOLTAGE_PROBE	0x2
#define VOLT_PROBE_DESC_1	"Voltage Probe #1"
#define VOLT_PROBE_LOC_1	0x67
#define VOLT_PROBE_MAX_1	0x8000
#define VOLT_PROBE_MIN_1	0x8000
#define VOLT_PROBE_RESOLUTION_1	0x8000
#define VOLT_PROBE_TOLERANCE_1	0x8000
#define VOLT_PROBE_ACCURACY_1	0x8000
#define VOLT_PROBE_OEM_1	0x0
#define VOLT_PROBE_NORM_VAL_1	0x8000
#define VOLT_PROBE_MGNT_DEV_1	0x1
#define VOLT_PROBE_SYS_PWR_SUPPLY_1	0x1
#define VOLT_PROBE_DESC_2	"Voltage Probe #2"
#define VOLT_PROBE_LOC_2	0x67
#define VOLT_PROBE_MAX_2	0x8000
#define VOLT_PROBE_MIN_2	0x8000
#define VOLT_PROBE_RESOLUTION_2	0x8000
#define VOLT_PROBE_TOLERANCE_2	0x8000
#define VOLT_PROBE_ACCURACY_2	0x8000
#define VOLT_PROBE_OEM_2	0x0
#define VOLT_PROBE_NORM_VAL_2	0x8000
#define VOLT_PROBE_MGNT_DEV_2	0x2
#define VOLT_PROBE_SYS_PWR_SUPPLY_2	0x2
#define VOLT_PROBE_DESC_3	"Voltage Probe #3"
#define VOLT_PROBE_LOC_3	0x67
#define VOLT_PROBE_MAX_3	0x8000
#define VOLT_PROBE_MIN_3	0x8000
#define VOLT_PROBE_RESOLUTION_3	0x8000
#define VOLT_PROBE_TOLERANCE_3	0x8000
#define VOLT_PROBE_ACCURACY_3	0x8000
#define VOLT_PROBE_OEM_3	0x0
#define VOLT_PROBE_NORM_VAL_3	0x8000
#define VOLT_PROBE_MGNT_DEV_3	0x0
#define VOLT_PROBE_SYS_PWR_SUPPLY_3	0x0
#define VOLT_PROBE_DESC_4	"Voltage Probe #4"
#define VOLT_PROBE_LOC_4	0x67
#define VOLT_PROBE_MAX_4	0x8000
#define VOLT_PROBE_MIN_4	0x8000
#define VOLT_PROBE_RESOLUTION_4	0x8000
#define VOLT_PROBE_TOLERANCE_4	0x8000
#define VOLT_PROBE_ACCURACY_4	0x8000
#define VOLT_PROBE_OEM_4	0x0
#define VOLT_PROBE_NORM_VAL_4	0x8000
#define VOLT_PROBE_MGNT_DEV_4	0x0
#define VOLT_PROBE_SYS_PWR_SUPPLY_4	0x0
#define VOLT_PROBE_DESC_5	"Voltage Probe #5"
#define VOLT_PROBE_LOC_5	0x67
#define VOLT_PROBE_MAX_5	0x8000
#define VOLT_PROBE_MIN_5	0x8000
#define VOLT_PROBE_RESOLUTION_5	0x8000
#define VOLT_PROBE_TOLERANCE_5	0x8000
#define VOLT_PROBE_ACCURACY_5	0x8000
#define VOLT_PROBE_OEM_5	0x0
#define VOLT_PROBE_NORM_VAL_5	0x8000
#define VOLT_PROBE_MGNT_DEV_5	0x0
#define VOLT_PROBE_SYS_PWR_SUPPLY_5	0x0
#define COOLING_DEVICE_INFO	0
#define TYPE27_STRUCTURE	0x0
#define NUMBER_OF_COOLING_DEVICE	0x3
#define COOLING_DEV_TYPE_1	0x62
#define COOLING_UINT_GROUP_1	0x0
#define COOLING_OEM_1	0x0
#define COOLING_NORM_SPEED_1	0x8000
#define COOLING_DESCRITION_1	"Cooling device #1"
#define COOLING_MGNT_DEV_1	0x1
#define COOLING_SYS_PWR_SUPPLY_1	0x1
#define COOLING_DEV_TYPE_2	0x62
#define COOLING_UINT_GROUP_2	0x0
#define COOLING_OEM_2	0x0
#define COOLING_NORM_SPEED_2	0x8000
#define COOLING_DESCRITION_2	"Cooling device #2"
#define COOLING_MGNT_DEV_2	0x2
#define COOLING_SYS_PWR_SUPPLY_2	0x2
#define COOLING_DEV_TYPE_3	0x62
#define COOLING_UINT_GROUP_3	0x0
#define COOLING_OEM_3	0x0
#define COOLING_NORM_SPEED_3	0x8000
#define COOLING_DESCRITION_3	"Cooling device #3"
#define COOLING_MGNT_DEV_3	0x2
#define COOLING_SYS_PWR_SUPPLY_3	0x3
#define COOLING_DEV_TYPE_4	0x62
#define COOLING_UINT_GROUP_4	0x0
#define COOLING_OEM_4	0x0
#define COOLING_NORM_SPEED_4	0x8000
#define COOLING_DESCRITION_4	"Cooling device #4"
#define COOLING_MGNT_DEV_4	0x0
#define COOLING_SYS_PWR_SUPPLY_4	0x0
#define COOLING_DEV_TYPE_5	0x62
#define COOLING_UINT_GROUP_5	0x0
#define COOLING_OEM_5	0x0
#define COOLING_NORM_SPEED_5	0x8000
#define COOLING_DESCRITION_5	"Cooling device #5"
#define COOLING_MGNT_DEV_5	0x0
#define COOLING_SYS_PWR_SUPPLY_5	0x0
#define TEMPERATURE_PROBE_INFO	0
#define TYPE28_STRUCTURE	0x0
#define NUMBER_OF_TEMPERATURE_PROBES	0x4
#define TEMPERATURE_PROBE_DESCRITION_1	"Temperature Probe #1"
#define TEMPERATURE_PROBE_LOC_1	0x62
#define TEMPERATURE_PROBE_MAX_1	0x8000
#define TEMPERATURE_PROBE_MIN_1	0x8000
#define TEMPERATURE_PROBE_RESOLUTION_1	0x8000
#define TEMPERATURE_PROBE_TOLERANCE_1	0x8000
#define TEMPERATURE_PROBE_ACCURACY_1	0x8000
#define TEMPERATURE_PROBE_OEM_1	0x0
#define TEMPERATURE_PROBE_NORM_VAL_1	0x8000
#define TEMPERATURE_PROBE_MGNT_DEV_1	0x1
#define TEMPERATURE_PROBE_DESCRITION_2	"Temperature Probe #2"
#define TEMPERATURE_PROBE_LOC_2	0x62
#define TEMPERATURE_PROBE_MAX_2	0x8000
#define TEMPERATURE_PROBE_MIN_2	0x8000
#define TEMPERATURE_PROBE_RESOLUTION_2	0x8000
#define TEMPERATURE_PROBE_TOLERANCE_2	0x8000
#define TEMPERATURE_PROBE_ACCURACY_2	0x8000
#define TEMPERATURE_PROBE_OEM_2	0x0
#define TEMPERATURE_PROBE_NORM_VAL_2	0x8000
#define TEMPERATURE_PROBE_MGNT_DEV_2	0x2
#define TEMPERATURE_PROBE_DESCRITION_3	"Temperature Probe #3"
#define TEMPERATURE_PROBE_LOC_3	0x62
#define TEMPERATURE_PROBE_MAX_3	0x8000
#define TEMPERATURE_PROBE_MIN_3	0x8000
#define TEMPERATURE_PROBE_RESOLUTION_3	0x8000
#define TEMPERATURE_PROBE_TOLERANCE_3	0x8000
#define TEMPERATURE_PROBE_ACCURACY_3	0x8000
#define TEMPERATURE_PROBE_OEM_3	0x0
#define TEMPERATURE_PROBE_NORM_VAL_3	0x8000
#define TEMPERATURE_PROBE_MGNT_DEV_3	0x1
#define TEMPERATURE_PROBE_DESCRITION_4	"Temperature Probe #4"
#define TEMPERATURE_PROBE_LOC_4	0x62
#define TEMPERATURE_PROBE_MAX_4	0x8000
#define TEMPERATURE_PROBE_MIN_4	0x8000
#define TEMPERATURE_PROBE_RESOLUTION_4	0x8000
#define TEMPERATURE_PROBE_TOLERANCE_4	0x8000
#define TEMPERATURE_PROBE_ACCURACY_4	0x8000
#define TEMPERATURE_PROBE_OEM_4	0x0
#define TEMPERATURE_PROBE_NORM_VAL_4	0x8000
#define TEMPERATURE_PROBE_MGNT_DEV_4	0x2
#define TEMPERATURE_PROBE_DESCRITION_5	"Temperature Probe #5"
#define TEMPERATURE_PROBE_LOC_5	0x62
#define TEMPERATURE_PROBE_MAX_5	0x8000
#define TEMPERATURE_PROBE_MIN_5	0x8000
#define TEMPERATURE_PROBE_RESOLUTION_5	0x8000
#define TEMPERATURE_PROBE_TOLERANCE_5	0x8000
#define TEMPERATURE_PROBE_ACCURACY_5	0x8000
#define TEMPERATURE_PROBE_OEM_5	0x0
#define TEMPERATURE_PROBE_NORM_VAL_5	0x8000
#define TEMPERATURE_PROBE_MGNT_DEV_5	0x0
#define ELECTRICAL_PROBE_INFO	0
#define TYPE29_STRUCTURE	0x0
#define NUMBER_OF_ELECTRICAL_PROBES	0x5
#define ELECTRICAL_PROBE_DESCRIPTION_1	"Electrical Probe #1"
#define ELECTRICAL_PROBE_LOC_1	0x62
#define ELECTRICAL_PROBE_MAX_1	0x8000
#define ELECTRICAL_PROBE_MIN_1	0x8000
#define ELECTRICAL_PROBE_RESOLUTION_1	0x8000
#define ELECTRICAL_PROBE_TOLERANCE_1	0x8000
#define ELECTRICAL_PROBE_ACCURACY_1	0x8000
#define ELECTRICAL_PROBE_OEM_1	0x0
#define ELECTRICAL_PROBE_NORM_VAL_1	0x8000
#define ELECTRICAL_PROBE_MGNT_DEV_1	0x1
#define ELECTRICAL_PROBE_SYS_PWR_SUPPLY_1	0x1
#define ELECTRICAL_PROBE_DESCRIPTION_2	"Electrical Probe #2"
#define ELECTRICAL_PROBE_LOC_2	0x62
#define ELECTRICAL_PROBE_MAX_2	0x8000
#define ELECTRICAL_PROBE_MIN_2	0x8000
#define ELECTRICAL_PROBE_RESOLUTION_2	0x8000
#define ELECTRICAL_PROBE_TOLERANCE_2	0x8000
#define ELECTRICAL_PROBE_ACCURACY_2	0x8000
#define ELECTRICAL_PROBE_OEM_2	0x0
#define ELECTRICAL_PROBE_NORM_VAL_2	0x8000
#define ELECTRICAL_PROBE_MGNT_DEV_2	0x2
#define ELECTRICAL_PROBE_SYS_PWR_SUPPLY_2	0x2
#define ELECTRICAL_PROBE_DESCRIPTION_3	"Electrical Probe #3"
#define ELECTRICAL_PROBE_LOC_3	0x62
#define ELECTRICAL_PROBE_MAX_3	0x8000
#define ELECTRICAL_PROBE_MIN_3	0x8000
#define ELECTRICAL_PROBE_RESOLUTION_3	0x8000
#define ELECTRICAL_PROBE_TOLERANCE_3	0x8000
#define ELECTRICAL_PROBE_ACCURACY_3	0x8000
#define ELECTRICAL_PROBE_OEM_3	0x0
#define ELECTRICAL_PROBE_NORM_VAL_3	0x8000
#define ELECTRICAL_PROBE_MGNT_DEV_3	0x1
#define ELECTRICAL_PROBE_SYS_PWR_SUPPLY_3	0x1
#define ELECTRICAL_PROBE_DESCRIPTION_4	"Electrical Probe #4"
#define ELECTRICAL_PROBE_LOC_4	0x62
#define ELECTRICAL_PROBE_MAX_4	0x8000
#define ELECTRICAL_PROBE_MIN_4	0x8000
#define ELECTRICAL_PROBE_RESOLUTION_4	0x8000
#define ELECTRICAL_PROBE_TOLERANCE_4	0x8000
#define ELECTRICAL_PROBE_ACCURACY_4	0x8000
#define ELECTRICAL_PROBE_OEM_4	0x0
#define ELECTRICAL_PROBE_NORM_VAL_4	0x8000
#define ELECTRICAL_PROBE_MGNT_DEV_4	0x2
#define ELECTRICAL_PROBE_SYS_PWR_SUPPLY_4	0x2
#define ELECTRICAL_PROBE_DESCRIPTION_5	"Electrical Probe #5"
#define ELECTRICAL_PROBE_LOC_5	0x62
#define ELECTRICAL_PROBE_MAX_5	0x8000
#define ELECTRICAL_PROBE_MIN_5	0x8000
#define ELECTRICAL_PROBE_RESOLUTION_5	0x8000
#define ELECTRICAL_PROBE_TOLERANCE_5	0x8000
#define ELECTRICAL_PROBE_ACCURACY_5	0x8000
#define ELECTRICAL_PROBE_OEM_5	0x0
#define ELECTRICAL_PROBE_NORM_VAL_5	0x8000
#define ELECTRICAL_PROBE_MGNT_DEV_5	0x1
#define ELECTRICAL_PROBE_SYS_PWR_SUPPLY_5	0x1
#define OUT_OF_BAND_REMOTE_ACCESS_INFO	0
#define TYPE30_STRUCTURE	0x0
#define OBRA_MANUFACTURER_NAME	"Default string"
#define OBRA_CONNECTIONS	0x0
#define BIS_INFO	0
#define TYPE31_STRUCTURE	0x0
#define CHECKSUM	0x0
#define BIS_ENTRY_POINT_REAL_MODE	0xffffffff
#define BIS_ENTRY_POINT_FLAT_MODE	0xffffffff
#define TYPE32_STRUCTURE	0x1
#define BOOT_STATUS	0x0
#define SIXTY_FOURBIT_MEMORY_ERROR_INFO	0
#define TYPE33_STRUCTURE	0x0
#define MANAGEMENT_DEVICE_INFO	0
#define TYPE34_STRUCTURE	0x0
#define NUMBER_OF_MANAGEMENT_DEVICES	0x1
#define MANAGEMENT_DEV_DESC_1	"Management Dev #1"
#define MANAGEMENT_DEV_TYPE_1	0x2
#define MANAGEMENT_DEV_ADDR_1	0x0
#define MANAGEMENT_DEV_ADDR_TYPE_1	0x2
#define MANAGEMENT_DEV_DESC_2	"Management Dev #2"
#define MANAGEMENT_DEV_TYPE_2	0x2
#define MANAGEMENT_DEV_ADDR_2	0x0
#define MANAGEMENT_DEV_ADDR_TYPE_2	0x2
#define MANAGEMENT_DEV_DESC_3	"Management Dev #3"
#define MANAGEMENT_DEV_TYPE_3	0x2
#define MANAGEMENT_DEV_ADDR_3	0x0
#define MANAGEMENT_DEV_ADDR_TYPE_3	0x2
#define MANAGEMENT_DEV_DESC_4	"Management Dev #4"
#define MANAGEMENT_DEV_TYPE_4	0x2
#define MANAGEMENT_DEV_ADDR_4	0x0
#define MANAGEMENT_DEV_ADDR_TYPE_4	0x2
#define MANAGEMENT_DEV_DESC_5	"Management Dev #5"
#define MANAGEMENT_DEV_TYPE_5	0x2
#define MANAGEMENT_DEV_ADDR_5	0x0
#define MANAGEMENT_DEV_ADDR_TYPE_5	0x2
#define MANAGEMENT_DEVICE_COMPONENT_INFO	0
#define TYPE35_STRUCTURE	0x0
#define NUMBER_OF_MGNT_DEV_COMPONENTS	0x2
#define MGMT_DEV_COMPONENT_DESC_1	"Default string"
#define MGMT_DEV_COMPONENT_MGNT_DEV_1	0x1
#define MGMT_DEV_COMPONENT_THRESHOLD_1	0x2
#define MGMT_DEV_COMPONENT_DESC_2	"Default string"
#define MGMT_DEV_COMPONENT_MGNT_DEV_2	0x1
#define MGMT_DEV_COMPONENT_THRESHOLD_2	0x3
#define MGMT_DEV_COMPONENT_DESC_3	"Default string"
#define MGMT_DEV_COMPONENT_MGNT_DEV_3	0x0
#define MGMT_DEV_COMPONENT_THRESHOLD_3	0x0
#define MGMT_DEV_COMPONENT_DESC_4	"Default string"
#define MGMT_DEV_COMPONENT_MGNT_DEV_4	0x0
#define MGMT_DEV_COMPONENT_THRESHOLD_4	0x0
#define MGMT_DEV_COMPONENT_DESC_5	"Default string"
#define MGMT_DEV_COMPONENT_MGNT_DEV_5	0x0
#define MGMT_DEV_COMPONENT_THRESHOLD_5	0x0
#define MANAGEMENT_DEVICE_THRESHOLD_INFO	0
#define TYPE36_STRUCTURE	0x0
#define NUMBER_OF_MGNT_DEV_THRESHOLDS	0x3
#define LOWER_THRESHOLD_NON_CRITICAL_1	0x8000
#define UPPER_THRESHOLD_NON_CRITICAL_1	0x8000
#define LOWER_THRESHOLD_CRITICAL_1	0x8000
#define UPPER_THRESHOLD_CRITICAL_1	0x8000
#define LOWER_THRESHOLD_NON_RECOVERABLE_1	0x8000
#define UPPER_THRESHOLD_NON_RECOVERABLE_1	0x8000
#define LOWER_THRESHOLD_NON_CRITICAL_2	0x8000
#define UPPER_THRESHOLD_NON_CRITICAL_2	0x8000
#define LOWER_THRESHOLD_CRITICAL_2	0x8000
#define UPPER_THRESHOLD_CRITICAL_2	0x8000
#define LOWER_THRESHOLD_NON_RECOVERABLE_2	0x8000
#define UPPER_THRESHOLD_NON_RECOVERABLE_2	0x8000
#define LOWER_THRESHOLD_NON_CRITICAL_3	0x8000
#define UPPER_THRESHOLD_NON_CRITICAL_3	0x8000
#define LOWER_THRESHOLD_CRITICAL_3	0x8000
#define UPPER_THRESHOLD_CRITICAL_3	0x8000
#define LOWER_THRESHOLD_NON_RECOVERABLE_3	0x8000
#define UPPER_THRESHOLD_NON_RECOVERABLE_3	0x8000
#define LOWER_THRESHOLD_NON_CRITICAL_4	0x8000
#define UPPER_THRESHOLD_NON_CRITICAL_4	0x8000
#define LOWER_THRESHOLD_CRITICAL_4	0x8000
#define UPPER_THRESHOLD_CRITICAL_4	0x8000
#define LOWER_THRESHOLD_NON_RECOVERABLE_4	0x8000
#define UPPER_THRESHOLD_NON_RECOVERABLE_4	0x8000
#define LOWER_THRESHOLD_NON_CRITICAL_5	0x8000
#define UPPER_THRESHOLD_NON_CRITICAL_5	0x8000
#define LOWER_THRESHOLD_CRITICAL_5	0x8000
#define UPPER_THRESHOLD_CRITICAL_5	0x8000
#define LOWER_THRESHOLD_NON_RECOVERABLE_5	0x8000
#define UPPER_THRESHOLD_NON_RECOVERABLE_5	0x8000
#define MEMORY_CHANNEL_INFO	0
#define TYPE37_STRUCTURE	0x0
#define NUMBER_OF_MEMORY_CHANNELS	0x1
#define MEMORY_CHANNEL_TYPE_1	0x1
#define MAX_CHANNEL_LOAD_1	0x1
#define MEMORY_DEVICE_COUNT_1	0x4
#define MEMORY_CHANNEL_TYPE_2	0x1
#define MAX_CHANNEL_LOAD_2	0x1
#define MEMORY_DEVICE_COUNT_2	0x4
#define MEMORY_CHANNEL_TYPE_3	0x1
#define MAX_CHANNEL_LOAD_3	0x1
#define MEMORY_DEVICE_COUNT_3	0x4
#define MEMORY_CHANNEL_TYPE_4	0x1
#define MAX_CHANNEL_LOAD_4	0x1
#define MEMORY_DEVICE_COUNT_4	0x4
#define IPMI_DEVICE_INFO	0
#define TYPE38_STRUCTURE	0x0
#define IPMI_INTERFACE_TYPE	0x0
#define IPMI_SPECIFICATION_REVISION	0x15
#define I2C_SLAVE_ADDRESS	0x0
#define NV_STORAGE_DEVICE_ADDRESS	0xff
#define IPMI_BASE_ADDRESS_LO	0xffffffff
#define IPMI_BASE_ADDRESS_HI	0xffffffff
#define BASE_ADDR_MOD_INTR_INFO	0x0
#define INTERRUPT_NUMBER	0x0
#define SYSTEM_POWER_SUPPLY_INFO	0
#define TYPE39_STRUCTURE	0x0
#define NUMBER_OF_POWER_SUPPLY	0x3
#define POWER_SUPPLY_UNIT_GROUP_1	0x1
#define PWR_SUPPLY_LOCATION_1	"Default string"
#define POWER_SUPPLY_DEVICE_NAME_1	"Default string"
#define POWER_SUPPLY_MANUFACTURER_1	"Default string"
#define POWER_SUPPLY_SERIAL_NUMBER_1	"Default string"
#define POWER_SUPPLY_ASSET_TAG_NUMBER_1	"Default string"
#define POWER_SUPPLY_MODEL_PART_NUMBER_1	"Default string"
#define POWER_SUPPLY_REVISION_LEVEL_1	"Default string"
#define POWER_SUPPLY_MAX_POWER_CAPACITY_1	0x8000
#define POWER_SUPPLY_CHARACTERISTICS_1	0x11a2
#define POWER_SUPPLY_UNIT_GROUP_2	0x1
#define PWR_SUPPLY_LOCATION_2	"Default string"
#define POWER_SUPPLY_DEVICE_NAME_2	"Default string"
#define POWER_SUPPLY_MANUFACTURER_2	"Default string"
#define POWER_SUPPLY_SERIAL_NUMBER_2	"Default string"
#define POWER_SUPPLY_ASSET_TAG_NUMBER_2	"Default string"
#define POWER_SUPPLY_MODEL_PART_NUMBER_2	"Default string"
#define POWER_SUPPLY_REVISION_LEVEL_2	"Default string"
#define POWER_SUPPLY_MAX_POWER_CAPACITY_2	0x8000
#define POWER_SUPPLY_CHARACTERISTICS_2	0x11a2
#define POWER_SUPPLY_UNIT_GROUP_3	0x1
#define PWR_SUPPLY_LOCATION_3	"Default string"
#define POWER_SUPPLY_DEVICE_NAME_3	"Default string"
#define POWER_SUPPLY_MANUFACTURER_3	"Default string"
#define POWER_SUPPLY_SERIAL_NUMBER_3	"Default string"
#define POWER_SUPPLY_ASSET_TAG_NUMBER_3	"Default string"
#define POWER_SUPPLY_MODEL_PART_NUMBER_3	"Default string"
#define POWER_SUPPLY_REVISION_LEVEL_3	"Default string"
#define POWER_SUPPLY_MAX_POWER_CAPACITY_3	0x8000
#define POWER_SUPPLY_CHARACTERISTICS_3	0x11a2
#define ADDITIONAL_INFO	0
#define TYPE40_STRUCTURE	0x0
#define ADDITIONAL_INFO_COUNT	0x3
#define REFERENCE_OFFSET_1	0x0
#define USE_STRING_1	0
#define REFERENCE_VALUE_1	00
#define REFERENCE_OFFSET_2	0x0
#define USE_STRING_2	0
#define REFERENCE_VALUE_2	00 11
#define REFERENCE_OFFSET_3	0x0
#define USE_STRING_3	0
#define REFERENCE_VALUE_3	00 11 22
#define REFERENCE_OFFSET_4	0x0
#define USE_STRING_4	0
#define REFERENCE_VALUE_4	00 11 22 33
#define REFERENCE_OFFSET_5	0x0
#define USE_STRING_5	0
#define REFERENCE_VALUE_5	00 11 22 33 44
#define ONBOARD_DEVICE_EXTENDED_INFO	1
#define TYPE41_STRUCTURE	0x1
#define DEVICE_DESIGNATION_STRING_1	"Device 1"
#define DEVICE_TYPE_1	0x82
#define DEVICE_TYPE_INSTANCE_1	0x1
#define SEGMENT_GROUP_NUMBER_1	0x0
#define BUS_NUMBER_1	0x0
#define DEVICE_FUNCTION_NUMBER_1	0x0
#define DEVICE_DESIGNATION_STRING_2	"Device 2"
#define DEVICE_TYPE_2	0x82
#define DEVICE_TYPE_INSTANCE_2	0x1
#define SEGMENT_GROUP_NUMBER_2	0x0
#define BUS_NUMBER_2	0x0
#define DEVICE_FUNCTION_NUMBER_2	0x0
#define DEVICE_DESIGNATION_STRING_3	"Device 3"
#define DEVICE_TYPE_3	0x82
#define DEVICE_TYPE_INSTANCE_3	0x1
#define SEGMENT_GROUP_NUMBER_3	0x0
#define BUS_NUMBER_3	0x0
#define DEVICE_FUNCTION_NUMBER_3	0x0
#define DEVICE_DESIGNATION_STRING_4	"Device 4"
#define DEVICE_TYPE_4	0x82
#define DEVICE_TYPE_INSTANCE_4	0x1
#define SEGMENT_GROUP_NUMBER_4	0x0
#define BUS_NUMBER_4	0x0
#define DEVICE_FUNCTION_NUMBER_4	0x0
#define DEVICE_DESIGNATION_STRING_5	"Device 5"
#define DEVICE_TYPE_5	0x82
#define DEVICE_TYPE_INSTANCE_5	0x1
#define SEGMENT_GROUP_NUMBER_5	0x0
#define BUS_NUMBER_5	0x0
#define DEVICE_FUNCTION_NUMBER_5	0x0
#define DEVICE_DESIGNATION_STRING_6	"Device 6"
#define DEVICE_TYPE_6	0x82
#define DEVICE_TYPE_INSTANCE_6	0x1
#define SEGMENT_GROUP_NUMBER_6	0x0
#define BUS_NUMBER_6	0x0
#define DEVICE_FUNCTION_NUMBER_6	0x0
#define DEVICE_DESIGNATION_STRING_7	"Device 7"
#define DEVICE_TYPE_7	0x82
#define DEVICE_TYPE_INSTANCE_7	0x1
#define SEGMENT_GROUP_NUMBER_7	0x0
#define BUS_NUMBER_7	0x0
#define DEVICE_FUNCTION_NUMBER_7	0x0
#define DEVICE_DESIGNATION_STRING_8	"Device 8"
#define DEVICE_TYPE_8	0x82
#define DEVICE_TYPE_INSTANCE_8	0x1
#define SEGMENT_GROUP_NUMBER_8	0x0
#define BUS_NUMBER_8	0x0
#define DEVICE_FUNCTION_NUMBER_8	0x0
#define DEVICE_DESIGNATION_STRING_9	"Device 9"
#define DEVICE_TYPE_9	0x82
#define DEVICE_TYPE_INSTANCE_9	0x1
#define SEGMENT_GROUP_NUMBER_9	0x0
#define BUS_NUMBER_9	0x0
#define DEVICE_FUNCTION_NUMBER_9	0x0
#define DEVICE_DESIGNATION_STRING_10	"Device 10"
#define DEVICE_TYPE_10	0x82
#define DEVICE_TYPE_INSTANCE_10	0x1
#define SEGMENT_GROUP_NUMBER_10	0x0
#define BUS_NUMBER_10	0x0
#define DEVICE_FUNCTION_NUMBER_10	0x0
#define INTEL_ASF	0
#define TYPE129_STRUCTURE	0x0
#define OEM_IO_GPNV_STRUC_INFO	0
#define TYPE240_STRUCTURE	0x0
#define TYPE44_STRUCTURE	1
#define NO_OF_PROCESSOR_ADDITIONAL_INFO	0x1
#define PROCESSOR_SPECIFIC_DATA_SIZE_1	0x1
#define PROCESSOR_ARCHITECTURE_TYPE_1	0x1
#define PROCESSOR_SPECIFIC_DATA_SIZE_2	0x1
#define PROCESSOR_ARCHITECTURE_TYPE_2	0x1
#define PROCESSOR_SPECIFIC_DATA_SIZE_3	0x1
#define PROCESSOR_ARCHITECTURE_TYPE_3	0x1
#define PROCESSOR_SPECIFIC_DATA_SIZE_4	0x1
#define PROCESSOR_ARCHITECTURE_TYPE_4	0x1
#define PROCESSOR_SPECIFIC_DATA_SIZE_5	0x1
#define PROCESSOR_ARCHITECTURE_TYPE_5	0x1
#define PROCESSOR_SPECIFIC_DATA_SIZE_6	0x1
#define PROCESSOR_ARCHITECTURE_TYPE_6	0x1
#define PROCESSOR_SPECIFIC_DATA_SIZE_7	0x1
#define PROCESSOR_ARCHITECTURE_TYPE_7	0x1
#define PROCESSOR_SPECIFIC_DATA_SIZE_8	0x1
#define PROCESSOR_ARCHITECTURE_TYPE_8	0x1
#define FIRMWARE_INVENTORY_INFO	1
#define TYPE45_STRUCTURE	1
#define BIOS_FW_COMPONENT_NAME	"BIOS Firmware"
#define BIOS_FW_VERSION	"03"
#define BIOS_FW_VERSION_FORMAT	0x1
#define BIOS_FW_ID	"Default string"
#define BIOS_FW_ID_FORMAT	0x1
#define BIOS_FW_MANUFACTURER	"Default string"
#define BIOS_LOWEST_SUPPORTED_FV	"03"
#define BIOS_FW_IMAGE_SIZE	0x1000000
#define BIOS_FW_CHARACTERISTICS_INFO	0x1
#define BIOS_FW_STATE_INFO	0x4
#define NO_OF_ASSOCIATED_COMPONENTS	0x1
#define OEM_MEMORY_GPNV_STRUC_INFO	0
#define TYPE241_STRUCTURE	0x0
#define PROCESSOR_DMIEDIT_SUPPORT	1
#define SmbiosDMIEdit_SUPPORT	1
#define DmiEditSmm_Support	1
#define REGISTER_SW_SMI_FN50	1
#define REGISTER_SW_SMI_FN51	1
#define REGISTER_SW_SMI_FN52	1
#define REGISTER_SW_SMI_FN53	1
#define INTEL_ARCH_SUPPORT	1
#define DMIEDIT_DEBUG_TRACE	1
#define SmbiosFlashData_SUPPORT	0
#define SMI_FLASH_LABEL_VERSION	0x2e
#define SMI_FLASH_INTERFACE_VERSION	0xe
#define AFU_BUFFER_IN_SHADOW	0
#define DISABLE_PWR_BUTTON	0
#define RECOVERY_PRESERVE_VARS_IN_SMM	1
#define NVRAM_MIGRATION	1
#define FLASH_MAX_MEM_ADDR	0xffffffff
#define FLASH_DEVICE_RANGE	0x1000000
#define CONVERT_OFFSET_TO_ROM_LAYOUT_ADDRESS	0
#define SMIFLASH_NV_BLOCK_FAULT_TOLERANT_UPDATE	0
#define SmiFlashBackupType_SUPPORT	0
#define TCG2Support	1
#define TCG2_VERSION	0x25
#define TCG2_VERBOSE_PRINT	0
#define MeasureCPUMicrocodeToken	0
#define MeasureCPUMicrocodeInPEIToken	1
#define MeasureGptFilterIntelIDER	1
#define INTEL_ARCHITECTURE_SUPPORT_TCG	1
#define AMI_TPM_PROGRESS_CODE_BASE	0xa00
#define MAX_LOG_AREA_SIZE	0x10000
#define PEI_MAX_LOG_AREA_SIZE	0x1000
#define FTpmPlatformProfile	0
#define MEASURE_EMBEDDED_BOOT_IMAGES	1
#define UNLOCK_PP_IN_MANUFACTURING_MODE	0
#define ALLOWINTERFACESELECT	0
#define TCGNIST_03_OR_NEWER	1
#define RomLayout_SUPPORT	1
#define NistPlatformManufacturerStr	AmiNistExamplePlatformManufacString
#define NistPlatformVersion	AmiNistPlatformVerString
#define NistPlatformModel	AmiNistPlatformModelString
#define NistFirmwareManufacturerStr	AmiNistFirmwareManufString
#define NistFirmwareManufacturerId	AmiNistFirmwareManufIdString
#define NistFirmwareVersion	AmiNistFirmwareVersionString
#define ALLOCATE_PCR_AFTER_SMM_INIT	0
#define WORD_ACCESS_SMI_PORT	1
#define DEBUG_MODE_PLATFORM	0
#define StartupCmd_SelfTest_State	0
#define HashSmmDrivers	0
#define TcgOemVenderID	0x1234
#define x64_TCG	1
#define SET_LIFETIME_PPLOCK	0
#define USE_ZERO_SEPARATOR	1
#define PORT_TPM_IOMEMBASE	0xfed40000
#define PPI_OFFSET	0x35
#define TCG_CONVENTIONAL_BIOS_6_1	1
#define TPM_PASSWORD_AUTHENTICATION	0
#define EXPOSE_FORCE_TPM_ENABLE	0
#define TPM2_S3_STARTUP_FAILURE_REBOOT_FLOW	0
#define CONFIRM_SETUP_CHANGE	0
#define LOG_EV_EFI_ACTION	1
#define TCG_CLEAR_REQUEST_KEY	SCAN_F12
#define TCG_CONFIGURATION_ACCEPT_KEY	SCAN_F10
#define TCG_CONFIGURATION_IGNORE_KEY	SCAN_ESC
#define gTcgMeDataHobGuid	{0x1e94f097,0x5acd,0x4089, {0xb2,0xe3,0xb9,0xa5,0xc8,0x79,0xa7,0x0c}}
#define CRTM_GUID	{ 0xa7102b05, 0xd9c7, 0x4165, { 0x81, 0x40, 0x2a, 0xdd, 0xe9, 0x4a, 0xf6, 0x3c } }
#define TRST	0x2
#define TCG_SPEC_ID_REVISION	0x69
#define TOKEN_TCG_TBL_OEM_REV	0x0
#define AUTO_ACCEPT_PPI	0
#define TCG2_REPORT_STATUS_CODE	0
#define TCMFNAME	TCMF
#define TTPFNAME	TTPF
#define DTPTNAME	DTPT
#define TTDPNAME	TTDP
#define TPMBNAME	TPMB
#define TPMBSIZE	TPBS
#define TPMCNAME	TPMC
#define TPMCSIZE	TPCS
#define TPMMNAME	TPMM
#define TPMIRQNAME	TMRQ
#define TPMIRQLEVEL	TLVL
#define FTPMNAME	FTPM
#define PPIMNAME	PPIM
#define PPILNAME	PPIL
#define AMDTNAME	AMDT
#define TPMFNAME	TPMF
#define PPIVNAME	PPIV
#define TCMESIADVALUE	ZIT0101
#define TPMPNPESAIDVALUE	PNP0C31
#define AMI_TCG_TPM_GPIO_RESOURCE_OVERRIDE	0
#define TPM20CRB_SUPPORT	1
#define PRE_BUILT_INTEL_HCILIBRARY	0
#define SMC_FUNC_ID	0x0
#define PRE_BUILT_NULL_HCILIBRARY	1
#define CONTROL_AREA_BASE	0x80300000
#define SET_TPM_IDLE	1
#define CRB_DELAY_TIMEOUT	0x32
#define CRB_COUNT_TIMEOUT	0x32
#define TPM20_CRBBASE	0xfed70000
#define TPM_REPORT_STATUSCODE_SUPPORT	1
#define TCG2PLATFORM_SUPPORT	1
#define TPM2_CAPABILITIES_LIB_SUPPORT	1
#define TISLIB_SUPPORT	1
#define TPM_DRIVER_WAIT	0x5
#define DELAY_AMOUNT	0x1e
#define TcgPeiSupport	1
#define TcgDxeSupport	1
#define SUPPORTED_TCM_DEVICE_1_VID	0x19f5
#define SUPPORTED_TCM_DEVICE_1_DID	0x1
#define SUPPORTED_TCM_DEVICE_2_VID	0x1b4e
#define SUPPORTED_TCM_DEVICE_2_DID	0x1
#define SUPPORTED_TCM_DEVICE_3_VID	0x1b4e
#define SUPPORTED_TCM_DEVICE_3_DID	0x201
#define NUMBER_OF_SUPPORTED_TCM_DEVICES	0x3
#define AMI_TCG_BINARIES	1
#define AMI_TCG_NVFLAG_SAMPLE_SUPPORT	1
#define NV_DATA_SIZE	0xa
#define NO_PPI_PROVISION_DEFAULT	0x1
#define NO_PPI_CLEAR_DEFAULT	0x1
#define NO_PPI_MAINTENANCE_DEFAULT	0x0
#define PPI_REQUIRED_FOR_CHANGE_EPS_DEFAULT	0x1
#define PPI_REQUIRED_FOR_ENABLE_BLOCK_SID_DEFAULT	0x1
#define PPI_REQUIRED_FOR_DISABLE_BLOCK_SID_DEFAULT	0x1
#define DONT_SEND_SELFTEST_TILL_READY_TO_BOOT	1
#define SELF_TEST_VID	0x15d1
#define Measure_Boot_Data	1
#define WAKE_EVENT_MEASUREMENT	0
#define PPI_DISPLAY_OFFSET	0x1
#define Measure_Smbios_Tables	0
#define TCGMeasureSecureBootVariables	1
#define UnconfiguredSecureBootVariables	0
#define MEASURE_CRTM_VERSION_PEI_FUNCTION	MeasureCRTMVersion
#define MEASURE_PLATFORM_CONFIG_FUNCTION	Tpm12PlatformHashConfig
#define MEASURE_PCI_OPTION_ROM_DXE_FUNCTION	MeasurePCIOproms
#define MEASURE_HANDOFF_TABLES_DXE_FUNCTION	MeasureHandoffTables
#define MEASURE_CPU_MICROCODE_DXE_FUNCTION	MeasureCpuMicroCode
#define MEASURES_TCG_BOOT_SEPARATORS_DXE_FUNCTION	MeasureSeparators
#define MEASURE_SECURE_BOOT_DXE_FUNCTION	MeasureSecurebootVariables
#define MEASURES_BOOT_VARIABLES_DXE_FUNCTION	MeasureAllBootVariables
#define MEASURE_WAKE_EVENT_DXE_FUNCTION	MeasureWakeEvent
#define SKIP_PHYSICAL_PRESENCE_LOCK_FUNCTION	DummySkipPhysicalPresence
#define TCGRomLayout_SUPPORT	1
#define TcgLegacy_SUPPORT	1
#define TCG_MOR_REG	0xe3
#define DISPLAY_TPM_SETUP_ERROR	0
#define CHOOSE_TPM_STACK_QUESTION	1
#define DEFAULT_SHA1_BANK_ENABLE	00
#define DEFAULT_SHA256_BANK_ENABLE	0x2
#define DEFAULT_SHA384_BANK_ENABLE	0x0
#define DEFAULT_SHA512_BANK_ENABLE	0x0
#define DEFAULT_SM3_BANK_ENABLE	0x0
#define TPMCLEARONROLLBACK_SUPPORT	1
#define Tpm20PlatformSupport	1
#define TCG_AMI_MODULE_PKG_VERSION	1
#define TPM20_MEASURE_PLATFORM_CONFIG_FUNCTION	Tpm20PlatformHashConfig
#define Measure_ACPI_Tables	0
#define TPM20_MEASURE_HANDOFF_TABLES_DXE_FUNCTION	Tpm20MeasureHandoffTables
#define MOR_RESET_S4S5	0
#define TCG_WAIT_OS_TPM_READY	0
#define TPM20_CRB_WITH_START_METHOD	0
#define INTELFTPMBASE	0xfed70000
#define TpmSmbios_SUPPORT	1
#define CHECK_TPM_SMBIOS_VERSION_SUPPORT	1
#define SmbiosMeasurementDxe_SUPPORT	1
#define TpmNvme_SUPPORT	0
#define TcmDxeSupport	1
#define TcmPeiSupport	1
#define TcgNistSp800_155_SUPPORT	1
#define TcgXmlRimm_support	1
#define TcgNistSp800_155_RM_REVISION	0x6
#define ReferenceManifestGuid_TcgLog	{0x80995D5D,0x47B6,0x485b,{0x8D,0xFC,0x89,0x01,0xB1,0xF2,0xBD,0xE3}}
#define Rim_PlatformId	FF7F21F4-B9BF-42D4-8BEA-37F93392E024
#define Rim_PlatformTag	KERP3
#define Rim_Version	6
#define Rim_BuildVersion	001003
#define Rim_ModelName	QuartzKERP3
#define Rim_FirmwareVendorName	AMI 
#define Rim_PlatformFirmwareId	20974
#define Rim_PlatformFirmwareStr	AMI Quartz
#define Rim_PlatformManufacturerStr	ODM Name 
#define Rim_PlatformModel	ODM Name Quartz
#define TcgLog_VendorName	AMI 
#define TcgLog_BiosName	KERP3
#define Nist800_155_PlatformManufacturerStr	ODM Name 
#define Nist800_155_PlatformModel	QuartzKERP3
#define Nist800_155_PlatformVersion	KERP3
#define Nist800_155_NistFirmwareManufacturerStr	AMI 
#define Nist800_155_FirmwareManufacturerId	20974
#define TcgStorageSecurity_SUPPORT	1
#define TCG_KMS_PASSWORD	0
#define TCG_STORAGE_SEC_SETUP_ON_SAME_PAGE	0
#define ENTERPRISE_SSC_DEVICE_SUPPORT	0
#define RUBY_SSC_DEVICE_SUPPORT	0
#define REVERSE_PWD_SUPPORT	1
#define TCG_STORAGE_SEC_SAVE_PCI_ROOTBRIDGE_REGS	1
#define TCG_STORAGE_SEC_VERBOSE_PRINT	0
#define FREEZE_LOCK_OPAL	1
#define ENABLE_DEVICE_RESET_THRU_PSID	0
#define FORCE_TCG_OPAL_PASSWORD_PROMPT	1
#define ATTEMPT_PADDED_PASSWORD_FOR_UNLOCK	0
#define TCG_MAX_PASSWORD_LENGTH	0x20
#define TCG_STORAGE_SECURITY_DRIVER_VERSION	0x17
#define TcgStorageDynamicSetup_SUPPORT	1
#define AMI_TSE_TCG_SECURITY_SUPPORT	1
#define TCG_STORAGE_SEC_SETUP_SAME_SYS_PW	0
#define PASSWORD_RETRY_ATTEMPTS	0x3
#define MAXIMUM_TCG_OPAL_PWD_UNLOCK_ATTEMPTS	0x6
#define TCG_STORAGE_SEC_ALL_HDD_SAME_PW	0
#define SmmTcgStorageSec_SUPPORT	1
#define TCG_SW_SMI_OPAL_UNLOCK_PASSWORD	0xd8
#define TurinSoCPkg_SUPPORT	1
#define APCB_OEM_BINARY_SUPPORT	1
#define APCB_BRH_SUPPORT	1
#define APCB_V3_BRH_ENABLE	1
#define APCB_RS_SUPPORT	0
#define AmiRasWrapperPkg_SUPPORT	1
#define SMBIOS_RAS_ELOG	0
#define DXE_DRIVER_AmdCpmPkg_Features_PlatformRas_Brh_Dxe_AmdOemRasBrhDxe_SUPPORT	0
#define DXE_DRIVER_AmdCpmPkg_Features_PlatformRas_Brh_Smm_AmdOemRasBrhSmm_SUPPORT	0
#define WHEA_ERST_SUPPORT	1
#define SERVER_WHEA_DEBUG_MESSAGE_SUPPORT	0
#define SERVER_WHEA_DEBUG(Arguments)	 
#define SERVER_WHEA_TRACE(Arguments)	 
#define FV_WHEA_BLOCKS	0x10
#define FV_WHEA_SIZE	0x10000
#define AMI_SMIPORT	0xb2
#define ELAR_SIZE	0x2000
#define ERST_BUSY_STS_VALUE	0x1
#define ERST_EXECUTE_OPERATION_CMD	0x70
#define AmiExtSerialUart0_SUPPORT	0
#define AmiSerialUart0_SUPPORT	1
#define AmiSerialUart1_SUPPORT	1
#define SMM_ISOLATION_SUPPORT	0
#define FirmwareConfigurationSrc_SUPPORT	1
#define TSE_SUPPORT_DEFAULT_FOR_STRING_CONTROL	1
#define FWBMC_DEBUG_MODE	1
#define REMOTE_COND_EXPRESSION_SUPPORT	0x1
#define FWCONFIG_REST_INSTANCE_TYPE	0x1
#define SUPPORT_STATIC_FILES_TIMEOUT_CHECK	0x2
#define ADD_WHOLEMAPID_TO_INDEXJSON	1
#define FWC_REDFISH_SERVER_IP	"************"
#define AMIUTIL_COMPLEX_EVAL_SUPPORT	1
#define REMOTE_MULTILANG_SUPPORT	1
#define REMOTE_SUPPORTED_LANGUAGES	L"en-US"
#define BOOTOPTION_SUPPORT_THROUGH_SYSTEMSSELF	0
#define NO_BOOT_OPTION_POST_SUPPORT	1
#define FIRMWARE_CONFIG_AS_APPLICATION	0
#define MODULE_TYPE	UEFI_DRIVER
#define ENTRYPOINT_LIBRARY	UefiDriverEntryPoint
#define FW_DEPEX_SECTION	[Depex]
#define FW_DEPEX_VALUE	gAmiRedfishHiProtocolGuid
#define Remote_Trigger_TseSetupEntryGuid	1
#define MULTI_LANG_PROV_GUID	gMultiLangProvProtocolGuid
#define USE_FWC_PROTOCOL	0
#define SKIP_INPUT_PASSWORD_ON_REDFISH_PWD_UPDATE	0
#define FWC_EfiOsBootOptionNames_SUPPORT	1
#define AmiPspPkg_SUPPORT	1
#define PSP_CRISIS_RECOVERY_SUPPORT	0
#define PSP_FLASH_BLOCK_DESC_TYPE	0x7E
#define HIGH_MEMORY_REGION_BB_BASE	0x75cc0000
#define HIGH_MEMORY_APOB_BASE	0x75bc0000
#define PSP_NOTIFY_SMM_SW_SMI	0x81
#define FV_IN_RAM	0
#define PRESERVE_PSP_SEV_DATA	0
#define PSP_BLOCK_CRB_SUPPORT	1
#define TOTAL_PSP_BLOCK_SUPPORT	0x4
#define BLOCK_0_SIZE	0x290000
#define BLOCK_1_SIZE	0x40000
#define BLOCK_2_SIZE	0x350000
#define BLOCK_3_SIZE	0x180000
#define BLOCK_0_OFFSET	0x61000
#define BLOCK_0_ADDRESS	0xff061000
#define BLOCK_1_OFFSET	0x2f1000
#define BLOCK_1_ADDRESS	0xff2f1000
#define BLOCK_2_OFFSET	0x331000
#define BLOCK_2_ADDRESS	0xff331000
#define BLOCK_3_OFFSET	0x681000
#define BLOCK_3_ADDRESS	0xff681000
#define PSP_BLOCK_START_OFFSET	0x61000
#define PSP_BLOCK_START_BASE	0xff061000
#define TOTAL_PSP_DATA_SIZE	0x7a0000
#define SPI_2ND_GEN_EFS	0xfffffffe
#define SPI_BRH_EFS	0xffffffe3
#define SPI_MODE	0xffffffff
#define SPI_SPEED	0xffffffff
#define PSP_BRH_SUPPORT	1
#define PSPDIR_L1_SIZE_BRH	0x290000
#define BIOSDIR_L1_SIZE_BRH	0x40000
#define PSPDIR_HEADER_OFFSET_BRH	0x61000
#define PSPDIR_L1_OFFSET_BRH	0x61000
#define BIOSDIR_L1_OFFSET_BRH	0x2f1000
#define PSPDIR_L2_OFFSET_BRH	0x331000
#define BIOSDIR_L2_OFFSET_BRH	0x681000
#define PSP_BOOT_LOADER_OFFSET_BRH	0x33a000
#define PSP_DATA_SIZE_BRH	0x7a0000
#define DUAL_BOOT	0
#define PSP_SEV_DATA_OFFSET_BRH	0x332000
#define PSP_SEV_DATA_SIZE_BRH	0x8000
#define PSP_BLOCK0_DATA_SIZE_BRH	0x2d1000
#define PSP_FV_BB_COMPRESS	0
#define FV_BB_OFFSET	0xcc0000
#define PSP_FV_BB_SIZE	0x340000
#define PSP_FV_BB_BASE	0x75cc0000
#define PSP_FV_BB_SIZE2	0x340000
#define FIXED_BOOT_ORDER_SUPPORT	1
#define BOOT_OPTION_GET_BBS_ENTRY_DEVICE_TYPE_FUNCTION	FBO_GetBbsEntryDeviceType
#define INITIAL_LEGCAY_GROUP_FUNCTION	InitLegacyGroupDefault
#define INITIAL_UEFI_GROUP_FUNCTION	InitUefiGroupDefault
#define USBKEY_RANGE_SIZE	0x7d00
#define READ_NAME_OF_LEGACY_DEVICES_FROM_BBSTABLE	0
#define SETUP_RUNTIME_IFR_PROCESSING	1
#define FIXED_BOOT_SWAP_POLICY	0
#define FIXED_BOOT_INCLUDE_DISABLED_OPTION	1
#define TSE_SAVE_DISABLED_BBS_DEVICEPATH	0
#define TSE_LOAD_OPTION_HIDDEN	1
#define FBO_USE_BOOT_OPTION_NUMBER_UNI	0
#define FBO_DUAL_MODE	0
#define FBO_WTG_PRIORITY_UPDATE	1
#define FIXED_BOOT_ORDER_GROUP_MAX_NUM	0x12
#define FIXED_BOOT_ORDER_TOTAL_DEVICES_MAX_NUM	0x21c
#define BcpBootOrder_SUPPORT	0
#define SHELL_GROUP_SUPPORT	0
#define FBO_TRACE_LEVEL	0x0
#define FBO_ETHERNET_DEFAULT_PRIORITY_POLICY	0
#define ADJUST_DEVICE_ACTIVE_BY_BOOTOPTION	0
#define ADJUST_GROUP_PRIORITY_BY_BOOTORDER	0
#define ADJUST_DEVICE_PRIORITY_BY_BOOTORDER	0
#define FBO_NEW_UEFI_OS_OPTION_ORDER_POLICY	0x0
#define FBO_RUNTIME_CALLBACK_REGISTRATION	1
#define FBO_SHOW_HIDE_BBS_PRIORITY_SUBMENU	0
#define DONOT_LOAD_DEFAULT_IN_SETUP	0
#define TSE_BOOT_NOW_IN_BOOT_ORDER	1
#define FBO_EMA_INDICATION_VALUE	0x5
#define FixedBootOrderStyle_SUPPORT	0
#define DEFAULT_FIXED_BOOT_ORDER_SUPPORT	1
#define FBO_ITEM_START_KEY	0x2000
#define D_OR_SYMBOL	||
#define OR_SYMBOL	|
#define NVME_GROUP_SUPPORT	1
#define FBO_MULTI_HARD_DISK_GROUPS	0
#define USB_GROUP	2
#define OTHER_GROUP	0
#define EFI_DEVICE_IN_ONE_GROUP	0
#define PUT_SATA_TO_USB_HDD_INTO_UEFI_USB_HDD_GROUP	0
#define FBO_LEGACY_TYPE_TO_NAME	{BoTagLegacyUSBHardDisk, L"USB HDD"},{BoTagLegacyUSBCdrom, L"USB CD"},{BoTagLegacyUSBFloppy, L"USB Floppy"},{BoTagLegacyUSBKey, L"USB KEY"},{BoTagLegacyHardDisk1, L"Hard Drive1"},{BoTagLegacyHardDisk2, L"Hard Drive2"},{BoTagLegacyHardDisk3, L"Hard Drive3"},{BoTagLegacyHardDisk4, L"Hard Drive4"},{BoTagLegacyHardDisk5, L"Hard Drive5"},{BoTagLegacyNvme, L"NVME"}
#define RESTORE_FBO_VARIABLES	1
#define FBO_IPMI_LEGACY_FORCE_BOOT_FLOPPY_TYPE	BoTagLegacyUSBFloppy
#define FBO_IPMI_UEFI_FORCE_BOOT_FLOPPY_TYPE	BoTagUefiUsbFloppy
#define FBO_NON_PERSISTENT_BOOT_ON_GROUP	1
#define FboGroupForm_SUPPORT	1
#define FBO_MAPPING_LANGUAGE_SUPPORT	1
#define FboSce_SUPPORT	1
#define FboExternalSync_SUPPORT	1
#define FBO_sync_ver	0x1
#define AmiFwUpdateCore_SUPPORT	0
#define CryptoPkg_MAJOR_VERSION	0x3a
#define CryptoPkg_MINOR_VERSION	0x0
#define BUILD_OPENSSL_WITH_SOCKET	0
#define BUILD_EDKII_PEI_CRYPT_LIB	1
#define CREATE_CRYPTO_BIN	0
#define MINIMUM_CONFIGURATION_SUPPORT	0
#define AMI_MAX_NUMBER_OF_PCI_SEGMENTS	0x4
#define SB_LPC_PORT80_SUPPORT	1
#define SB_RAID5_SUPPORT	0
#define REG80_FIFO_SUPPORT	1
#define DXE_SMM_DRIVER_AgesaModulePkg_Library_AmdS3SaveLib_S3Save_AmdS3SaveLib_SUPPORT	0
#define DXE_DRIVER_AgesaModulePkg_Library_AmdS3SaveLib_S3Save_AmdS3SaveLib_SUPPORT	0
#define CSTATE_IO_BASE_ADDRESS	0x813
#define MP_TBL_TMP_BUFFER_SIZE	0x2000
#define FACP_FLAG_USE_PLATFORM_CLOCK	0x0
#define FEC00000_APIC_AUTODETECT	1
#define IO_APIC_BASE_ADDRESS_TOP	0xfec10000
#define IO_APIC_VERSION_PARAMETER	0x22
#define IRQ_09_POLARITY	0x3
#define MPS_TABLE_SUPPORT	0
#define ACPI_PM_PROFILE	0x4
#define FACP_FLAG_TMR_VAL_EXT	1
#define ACPI_INT_MODEL	0x1
#define P_LVL2_LAT_VAL	0x64
#define DUTY_WIDTH_VAL	0x3
#define FACP_FLAG_P_LVL2_UP	1
#define UHCI_SUPPORT	0
#define OHCI_SUPPORT	0
#define EHCI_SUPPORT	1
#define XHCI_SUPPORT	1
#define EHCI_64BIT_DATA_STRUCTURE	0
#define USB_HOTPLUG_FDD	0
#define ICH_SATA_BUS_NUMBER	0x12
#define ICH_SATA_DEVICE_NUMBER	0x0
#define ICH_SATA_FUNCTION_NUMBER	0x2
#define UsbRecov_SUPPORT	1
#define PEI_UHCI_SUPPORT	0
#define PEI_OHCI_SUPPORT	0
#define PEI_EHCI_SUPPORT	0
#define PEI_XHCI_CONTROLLER_PCI_ADDRESS	{5,0x23,0x0,0x4,0x43,0x0,0x4,0x73,0x0,0x4,0xA3,0x00,0x4,0xE3,0x0,0x4}
#define XHCI_PEI_SUPPORT	1
#define PEI_XHCI_CONTROLLER_PCI_REGISTER_VALUES	{1,0,0,0,0,0,0,0}
#define PEI_AHCI_ROOT_BRIDGE_LIST_GN	{0x20, 0x08, 0x02},{0x20, 0x08, 0x03},{0x40, 0x08, 0x02},{0x40, 0x08, 0x03},{0xA0, 0x08, 0x02},{0xA0, 0x08, 0x03}, {0xC0, 0x08, 0x02},{0xC0, 0x08, 0x03}
#define MAX_AHCI_CONTROLLER_DEVICE	0x9
#define SMM_CACHE_SUPPORT	0
#define MAX_MANAGED_CMOS_ADDRESS	0x100
#define S3_BASE_MEMORY_SIZE	0x80000
#define TCGSMIPORT	0xb2
#define TCGSMIDATAPORT	0xb3
#define Disable_RsdpRuntimeShadowWrite_SUPPORT	1
#define SOLUTION_TYPE	0x1
#define NUMBER_OF_MEM_MODULE	0x4
#define A1_MEMORY_SOCKETS	0x4
#define DIMM_SLOTS_PER_NODE	0x4
#define WDT_SUPPORT	0
#define FCH_LPC_CLK_RUN_SUPPORT	0
#define HPET_SUPPORT	1
#define PM_BASE_ADDRESS	0x800
#define SW_SMI_IO_ADDRESS	0xb2
#define PCI_DEVICE_64BIT_RESOURCE_THRESHOLD	0xFFFFFFFFFFFFFFFF
#define BIG_REAL_MODE_MMIO_ACCESS	1
#define UHCI_EMUL_SUPPORT	0
#define OHCI_EMUL_SUPPORT	1
#define OHCI_EMUL_PCI_DEVICES	{FixedMemory, 0x80, 0xFED80040}
#define SPI_ROM3_BASE_ADDRESS	0xFD00000000
#define SPI_ROM3_BASE_ADDRESS_HIGH	0xFD
#define APCB_TOKEN_UID_FCH_ROM3_BASE_HIGH_VALUE	0xfd
#define TURIN_CRB_SUPPORT	1
#define SKIP_DUMMY_LAPICS	0
#define AMD_PI_NAME	Turin
#define FW_VERSION_GUID	{0x3ad942ce, 0xd4f9, 0x4b99, {0x8d, 0xac, 0x31, 0x6a, 0x7e, 0x57, 0xf9, 0x9f}}
#define Recovery_Time_Delay	0x5
#define ISA_DMA_MASK	0x10
#define CRB_SWSMI	0xbf
#define REFLASH_UPDATE_BOOT_BLOCK	1
#define AMI_SEC_ROM	KERP3.rom
#define CRB_USB_ASL_SUPPORT	1
#define CRB_LS_ASL_SUPPORT	0
#define HIGH_MEMORY_REGION_BASE	0x75000000
#define TCGPPISPEC_1_2_SUPPORT	1
#define SPI_TPM_SUPPORT	1
#define CRB_PROJECT_TAG	KERP3
#define AMI_ROM_FD	KERP3001003.FD
#define BUILD_BIOS_TAR	1
#define x64_BUILD	1
#define FAULT_TOLERANT_NVRAM_UPDATE	1
#define NVRAM_SMI_SUPPORT	1
#define NVRAM_RT_GARBAGE_COLLECTION_SUPPORT	1
#define SERIAL_STATUS_SUPPORT	1
#define NUMBER_OF_SYSTEM_CONFIG_STRINGS	0x1
#define NUMBER_OF_ONBOARD_DEVICES	0x1
#define SMBIOS_PI_1_1	1
#define SYS_CHASSIS_TYPE_1	0x11
#define SMM_THUNK_IN_CSM	0
#define SMM_THUNK_NO_AB_SEG	0
#define LOAD_SMM_DRIVERS_IN_TSEG_OUTSIDE_SMM	1
#define FORCE_USER_TO_SETUP_ON_FIRST_BOOT	0
#define FACP_FLAG_RTC_S4	0
#define FACP_FLAG_S4_RTC_STS_VALID	0x0
#define FLASH_SIZE	0x1000000
#define FLASH_BLOCK_SIZE	0x1000
#define DEFAULT_SATA_SUPPORT_SETTING	0x0
#define CMOS_ADDR_PORT	0x70
#define CMOS_DATA_PORT	0x71
#define FLASH_UPDATE_BOOT_SUPPORTED	1
#define CSLIB_WARM_RESET_SUPPORTED	1
#define AMD_PLATFROM_SUPPORT	1
#define STOP_USB_SUPPORT	1
#define NO_MMIO_FLASH_ACCESS_DURING_UPDATE	1
#define TSEG_SIZE	0x10000000
#define PlatformGOPPolicy_SUPPORT	1
#define FCH_FIRMWARE_BASE	0xff020000
#define FCH_FIRMWARE_OFFSET	0x20000
#define ROMSIG_SIZE	0x1000
#define NVRAM_SIZE	0x20000
#define NVRAM_OFFSET	0x21000
#define NVRAM_ADDRESS	0xff021000
#define NVRAM_BACKUP_ADDRESS	0xff041000
#define OEM_NCB_ADDRESS	0xff801000
#define OEM_NCB_OFFSET	0x801000
#define OEM_NCB_SIZE	0x10000
#define OEM_NCB_BLOCKS	0x10
#define OEM_ACTIVATION_TABLE_LOCATION	1
#define OEM_ACTIVATION_TABLE_SIZE	0x10000
#define OEM_ACTIVATION_TABLE_ADDRESS	0xff801000
#define GPNV_SIZE	0x10000
#define FV_ERROR_LOGGING_BLOCKS	0x10
#define FV_ERROR_LOGGING_SIZE	0xffff
#define FV_WHEA_OFFSET	0x811000
#define FV_WHEA_BASE	0xff811000
#define FV_MAIN_OFFSET	0x821000
#define FV_MAIN_BASE	0xff821000
#define FV_MAIN_SIZE	0x49f000
#define FV_BB_BASE	0xffcc0000
#define FV_BB_SIZE	0x340000
#define AMI_ROM_LAYOUT_FV_BB_ADDRESS	0xffcc0000
#define AMI_ROM_LAYOUT_FV_BB_OFFSET	0xcc0000
#define AMI_ROM_LAYOUT_FV_BB_SIZE	0x340000
#define AMI_ROM_LAYOUT_FV_MAIN_ADDRESS	0xff821000
#define AMI_ROM_LAYOUT_FV_MAIN_OFFSET	0x821000
#define AMI_ROM_LAYOUT_FV_MAIN_SIZE	0x49f000
#define AMI_ROM_LAYOUT_NVRAM_ADDRESS	0xff021000
#define AMI_ROM_LAYOUT_NVRAM_OFFSET	0x21000
#define AMI_ROM_LAYOUT_NVRAM_SIZE	0x20000
#define AMI_ROM_LAYOUT_NVRAM_BACKUP_ADDRESS	0xff041000
#define AMI_ROM_LAYOUT_NVRAM_BACKUP_OFFSET	0x41000
#define SmmOemActivation_SUPPORT	1
#define PRESERVE_SECURE_VARIABLES	1
#define FWCAPSULE_FILE_FORMAT	0
#define FWSIG_SIGNHDR	0x1
#define SECURITY_POLICY_SUPPORT	0
#define FAST_BOOT_PCI_SKIP_LIST	{{0x01,0x01},{0x01,0x04},{0x01,0x06},{0x0c,0x03},{0x06,0x01},{0x03,0xff}}
#define DEFAULT_USB_SUPPORT_SETTING	1
#define SUPPORT_ZERO_BOOT_TIMEOUT	1
#define LZMA_SUPPORT	1
#define NESTED_FV_MAIN	1
#define HOTPLUG_SUPPORT	1
#define ATIS	0xb0
#define FORCE_RECOVERY	0
#define OEM_CHIP_HOTPLUG_DEVICE	{ACPI,PCI(0x2,0x2),END},
#define RSVD_EXT_BUS	0x2
#define RSVD_PCI_MEM	0x1
#define RSVD_PCI_IO	0x1
#define PciBusSetupOverride_SUPPORT	1
#define USB_PEI_EDKII_IOMMU_PPI_SUPPORT	0
#define SerialRecovery_SUPPORT	0
#define IdeRecovery_SUPPORT	0
#define IS_RECOVERY_PPI_SUPPORT	0
#define CLEAR_NVRAM_IF_CMOS_BAD	1
#define APCB_RECOVERY_LOAD_DEFAULT_SETUP	0
#define NV_SIMULATION	0
#define SB_DEV_NUMBER	0x14
#define SB_FUN_NUMBER	0x3
#define START_IN_NATIVE_RESOLUTION	0
#define POWERON_BUSY_CLEAR_TIMEOUT	0x7530
#define S3_BUSY_CLEAR_TIMEOUT	0x7530
#define SHELL_TEXT_MODE	0x2
#define TEXT_EXCESS_SUPPORT	1
#define STYLE_HELP_AREA_SCROLLBAR	1
#define ACOUSTIC_MANAGEMENT_DRIVER_SUPPORT	0
#define XHCI_COMPLIANCE_MODE_WORKAROUND	1
#define PeiRamBootSupport	0
#define SAVE_ENTIRE_FV_IN_MEM	1
#define NvramSmiSupport	0
#define CRB_CUSTOM_PPI_SUPPORT	0
#define CRB1_SB_PCI_DEVICES_SSID_TABLE	{SATA_BUS_DEV_FUN, -1}, {SMBUS_BUS_DEV_FUN, -1}, {USB1_EHCI_BUS_DEV_FUN, -1}, {LPC_BUS_DEV_FUN, -1}, {USB_XHCI_BUS_DEV_FUN, -1}, {SD_BUS_DEV_FUN, -1}, {-1, -1}
#define CRB1_GNB_PCI_DEVICES_SSID_TABLE	{IGD_BUS_DEV_FUN, -1},{IGDHDA_BUS_DEV_FUN, -1},{GNB_BUS_DEV_FUN,-1},{GNB_PCIEBRIDGEx_BUS_DEV_FUN, -1},{GNB_PCIEBRIDGFXGEx_BUS_DEV_FUN, -1}, {-1, -1}
#define CRB2_SB_PCI_DEVICES_SSID_TABLE	{SATA_BUS_DEV_FUN, -1}, {SMBUS_BUS_DEV_FUN, -1}, {USB1_EHCI_BUS_DEV_FUN, -1}, {LPC_BUS_DEV_FUN, -1}, {USB_XHCI_BUS_DEV_FUN, -1}, {SD_BUS_DEV_FUN, -1}, {-1, -1}
#define CRB2_GNB_PCI_DEVICES_SSID_TABLE	{IGD_BUS_DEV_FUN, -1},{IGDHDA_BUS_DEV_FUN, -1},{GNB_BUS_DEV_FUN,-1},{GNB_PCIEBRIDGEx_BUS_DEV_FUN, -1},{GNB_PCIEBRIDGFXGEx_BUS_DEV_FUN, -1}, {-1, -1}
#define CAPSULE_RESET_MODE	0
#define COPY_TO_RAM_WHILE_DISPATCH	0
#define ROOT_BRIDGE_COUNT	0x1
#define SUPPORT_ESRT	1
#define CRB_XHCI_RECOVERY_WORKAROUND	0
#define USB30PORT_INIT	0x08
#define USB30PORT_INIT_SUPPORT	1
#define NVRAM_START_SMI_SERVICES_GUID	{ 0xADCf9520, 0x5791, 0x4667, { 0xad, 0xe4, 0x1c, 0xfd, 0xa8, 0x37, 0x72, 0x2d } }
#define NVRAM_SMI_FULL_PROTECTION	1
#define EDKII_FLAGS	-y report.log
#define AmdIdsDebug_SUPPORT	0
#define APCB_TOKEN_UID_FCH_CONSOLE_OUT_ENABLE_VALUE	0x0
#define DXE_DRIVER_AmdCpmPkg_Features_AmdFwConfig_Asl_AfcSsdt_SUPPORT	0
#define DXE_DRIVER_AmdCpmPkg_Features_AmdFwConfig_Dxe_AmdFwConfigDxe_SUPPORT	0
#define DXE_DRIVER_AmdCpmPkg_Features_AmdFwConfig_Smm_AmdFwConfigSmmBrh_SUPPORT	0
#define _AmdCpmPkg_Library_Proc_AmdFwConfigRuntimeLib_AmdFwConfigRuntimeLib_SUPPORT	0
#define _AmdCpmPkg_Library_Proc_AmdFwConfigCbsLib_AmdFwConfigCbsBrhLib_SUPPORT	0
#define _AmdCpmPkg_Library_Proc_AmdFwConfigApcbLibV3_AmdFwConfigApcbBrhLibV3_SUPPORT	0
#define A_S3	0
#define FORCE_COMMON_MODE_FOR_DEVICES	1
#define SERIAL_DEBUGGER_SUPPORT	0
#define AMIDEBUGGERPKG_SUPPORT	0
#define MAX_ADDITIONAL_P2P_BRIDGES	0x80
#define TSE_GOP_NOTIFICATION_SUPPORT	0
#define DBG_PEI_XHCI_MMIOBASE	0xed100000
#define USB_3_DEBUG_SUPPORT	0
#define BOTTOM_MMIO_LIMIT	0x90000000
#define IGNORE_IMAGE_ROLLBACK	0
#define ENABLE_IMAGE_EXEC_POLICY_OVERRIDE	0
#define AmiHstiAmdFwProtectSpiImplementationBit	0x8
#define PCI_OUT_OF_RESOURCE_SETUP_SUPPORT	1
#define S5ResetStatusLib_SUPPORT	0
#define USB_DXE_EDKII_IOMMU_PROTOCOL_SUPPORT	1
#define TCG_SKIP_MOR_FULL	1
#define RT_ACCESS_SUPPORT_IN_HPKTOOL	0
#define LOCK_SETVAR_AT_ENDOFDXE	0
#define AGESA_SMU_CMOS_BYTE	0xdc
#define FLASH_BASE	0xff000000
#define AMITSE_CryptoPkg_SUPPORT	1
#define CRB_SETUP_SUPPORT	1
#define FCH_UART_BASE_ADDRESS	0xfedc9000
#define SERIALIO_PCI_SERIAL_SUPPORT	1
#define PCI_SERIAL_MMIO_WIDTH	0x4
#define PCI_UART_INPUT_CLOCK	0x2db4000
#define PCI0_DEFAULT_CONSOLE_REDIRECTION_ENABLE	1
#define PCI1_DEFAULT_CONSOLE_REDIRECTION_ENABLE	1
#define COM_MMIO_WIDTH	0x4
#define FCH_UART1_BASE_ADDRESS	0xfedca000
#define ISA_IRQ_MASK	0xe31d
#define FchUart0LegacyEnableToken	0x4
#define FchUart1LegacyEnableToken	0x2
#define CIM_LEGACY_FREE	1
#define Ast2600_EarlyVideo_DP_Port_Support	1
#define VGA_ROOT_PORT_BUS	0x5
#define EARLY_CONSOLE_PLATFORM_NAME	Turin
#define AmiApcbSmmService_SUPPORT	1
#define AmiPspEarlyVga_SUPPORT	1
#define PSP_BMC_VGA_SELECT	0
#define PSPDIR_L2_SIZE_BRH	0x350000
#define BIOSDIR_L2_SIZE_BRH	0x180000
#define Chalupa_SUPPORT	0
#define Galena_SUPPORT	0
#define Onyx_SUPPORT	0
#define Purico_SUPPORT	0
#define Quartz_SUPPORT	1
#define ExternalSerial_SUPPORT	0
#define SYSTEM_PRODUCT_NAME	Quartz
#define NUMBER_OF_SYSTEM_SLOTS	0x7
#define ONBOARD_DEVICE_EXT_COUNT	0x2
#define EXTRA_RESERVED_BYTES	0x4000
#define SMBIOS_TABLE_LOCATION	0x0
#define FchRTDeviceEnableMapToken	0x1f60
#define NSOCKETS	0x2
#define NCPU	0x300
#define Ruby_SUPPORT	0
#define Titanite_SUPPORT	0
#define Volcano_SUPPORT	0
#define TURIN_BUILD	1
#define INTERRUPTS_TO_PRESERVE	{0x13, 0x40, 0x08}
#define CRB_PROJECT_MAJOR_VERSION	0x0
#define CRB_PROJECT_MINOR_VERSION	0x14
#define SMBIOS_3X_MINOR_VERSION	0x5
#define SYSTEM_MANUFACTURER	AMD Corporation
#define ELEMENT_LEN_1	0x0
#define TYPE16_STRUCTURE	0
#define MEMORY_DEVICE_INFO	0
#define IO_APIC_ID_READ_MASK	0xff000000
#define USE_BOARD_INFO_APIC_DATA	1
#define USB_PEI_KEYBOARD_SUPPORT	0
#define SimNow_Support	0
#define PCIE_BASE_ADDRESS_OVER_4GB	1
#define AMI_NUMBER_OF_PCI_SEGMENTS	0x1
#define PCIEX_BASE_ADDRESS	0x3FFB00000000
#define PCIE_BASE_ADDRESS	0x0
#define PCIEX_BASE_ADDRESS_LOW	0x0
#define PCIEX_BASE_ADDRESS_HIGH	0x3ffb
#define PCIEX_LENGTH	0x40000000
#define APCB_TOKEN_UID_DF_PCI_MMIO_BASE_VALUE	0x0
#define APCB_TOKEN_UID_DF_PCI_MMIO_HI_BASE_VALUE	0x3ffb
#define APCB_TOKEN_UID_DF_PCI_MMIO_SIZE_VALUE	0x10000000
#define EHCI_MMIO_BASE_ADDRESS	0x0
#define RA_BB_PROTECT_TEST	0
#define AmdAutoTool_Support	0
#define ESPI_UART_IO_ADDR	0x3f8
#define AmiSsifInterface_SUPPORT	0
#define AmiBtInterface_SUPPORT	0
#define AmiUsbInterface_SUPPORT	0
#define AmiIpmbInterface_SUPPORT	0
#define REBOOT_ON_FIRST_BOOT_EVALUATION	0
#define APCB_TOKEN_UID_FCH_ABL_APCB_BOARDID_ERROR_HALT_VALUE	0x0
#define SB_BEFORE_PCI_RESTORE_SWSMI	0x0
#define SB_AFTER_PCI_RESTORE_SWSMI	0x0
#define MAX_NUM_MCA_BANKS	0x20
#define NETWORKSTACK_IPV4_HTTP_SUPPORT	1
#define CompalModuleAmiPkg_SUPPORT	1
#define COMPAL_PROJECT_TAG	KERP3
#define COMPAL_MODULE_VERSION	0x0
#define COMPAL_PROJECT_MAJOR_VERSION_STR	0x1
#define COMPAL_PROJECT_MINOR_VERSION_STR	0x3
#define COMPAL_PROJECT_AUXILIARY_VERSION_STR	0x0
#define PROJECT_MAJOR_VERSION	0x1
#define PROJECT_MINOR_VERSION	0x3
#define CompalModuleAmiPkg_Universal_SUPPORT	1
#define CompalRomIdGenerator_SUPPORT	1
#define COMPAL_ROM_ID_IN_FV_BB	1
#define CplTurinPkg_SUPPORT	1
#define CompalGpioLib_SUPPORT	1
#define CompalPlatformGpioLib_SUPPORT	1
#define CompalPkg_SUPPORT	1
#define CompalModulePkg_SUPPORT	1
#define CompalModulePkg_Universal_SUPPORT	1
#define CompalPostEnd_SUPPORT	1
#define OemboardPkg_SUPPORT	1
#define Model_ID	KERP3
#define PYCMD	$(PYTHON_COMMAND)
#define DEBUG_MODE	1
#define RMT_MODE	0
#define AmiFwUpdateBmc_SUPPORT	0
#define HIDE_SETUP_ITEM	1
#define APCB_TOKEN_UID_FCH_CONSOLE_OUT_SERIAL_PORT_VALUE	0x2
#define SioSerial_SUPPORT	0
#define OnBoardSerial_SUPPORT	1
#define FCH_CHK_SERIAL_CABLE	0
#define TSE_DEBUG_MESSAGES	0x0
#define SUPPRESS_PRINT	0
#define SERVER_IPMI2_DEBUG_MESSAGE_SUPPORT	0x0
#define PCI_BUS_DEBUG_MESSAGES	0
#define NWS_DEBUG_MESSAGES	0
#define NWS_REDUNDANT_DEBUG_MESSAGE	0
#define AUX_NUMBER	0x0
#define AMI_COMMON_SETTING	1
#define HHM_MAX_SOCKET	0x2
#define HHM_MAX_CHANNEL_PER_CPU	0xc
#define HHM_MAX_DIMM_SLOTS_PER_CHANNEL	0x2
#define WRITE_ONCE_ONLY_TABLE_SUPPORT	0
#define SYSTEM_FAMILY	COMPAL
#define AST2600_SERIAL_PORT2_PRESENT	0
#define AST2600_SERIAL_PORT3_PRESENT	0
#define AST2600_SERIAL_PORT4_PRESENT	0
#define AST2600_GPIO_PORT_PRESENT	0
#define AST2600_SWC_PRESENT	0
#define AST2600_ILPC2AHB_PRESENT	0
#define AST2600_MAILBOX_PRESENT	0
#define AST2600_SCU_ACCESS_ENABLE	0
#define T_ACPI_OEM_ID	COMPAL
#define T_ACPI_OEM_TBL_ID	AMI
#define ACPI_OEM_REV	0x10300
#define PRESERVE_PASSWORDS	1
#define AMIDEBUG_RX_SUPPORT	0
#define RECOVERY_ROM	AMI.ROM
#define LOAD_DEFAULTS_IF_CMOS_BAD	1
#define SETUP_PRINT_EVAL_MSG	0
#define PCR_FOR_BOOT_VARIABLES	0x5
#define TCG_PLATFORM_CLASS	0x1
#define PPI_REQUIRED_FOR_CHANGE_PCR_DEFAULT	0x0
#define PROJECT_HSTI_SUPPORT	1
#define DEFAULT_QUIET_BOOT	0
#define DEFAULT_BOOT_TIMEOUT	0x1
#define DEFAULT_VIDEO_OPROM_POLICY	0x1
#define DEFAULT_CSM_LAUNCH_POLICY	0x0
#define OEM_DEFAULT_ONBOARD_EXTERNAL_VGA_SELECT	0x1
#define COM0_DEFAULT_CONSOLE_REDIRECTION_ENABLE	0
#define DEFAULT_ACPI_SPCR_CONSOLE_REDIRECTION_ENABLE	0
#define COM1_DEFAULT_CONSOLE_REDIRECTION_ENABLE	0
#define EXTENDED_RESOLUTION_ENABLE	1
#define NON_FW_ORPHAN_BOOT_OPTIONS_POLICY	ORPHAN_BOOT_OPTIONS_POLICY_DELETE
#define BOOT_OPTION_TAG_PRIORITIES	BoTagUefi, BoTagLegacyHardDisk, BoTagLegacyCdrom, BoTagLegacyFloppy, BoTagLegacyEmbedNetwork, BoTagEmbeddedShell
#define NWSTACK_DEFAULT_VALUE	1
#define NETWORKSTACK_IPV4_PXE_SUPPORT	1
#define NETWORKSTACK_IPV6_PXE_SUPPORT	1
#define PRESERVE_NWS_VARIABLE	0
#define BIOS_SIGNON_MESSAGE2	BIOS Date: $(TODAY) Ver: $(BIOS_NUMBER)
#define SETUP_ENTRY_SCAN	EFI_SCAN_F1
#define SETUP_ALT_ENTRY_ENABLE	0
#define SETUP_BBS_POPUP_ENABLE	1
#define POPUP_MENU_ENTRY_SCAN	EFI_SCAN_F12
#define POPUP_MENU_SHOW_ALL_BBS_DEVICES	1
#define SETUP_SHOW_ALL_BBS_DEVICES	0
#define TSE_MULTILINE_CONTROLS	1
#define AMITSE_SUPPRESS_DYNAMIC_FORMSET	1
#define TSE_SUPPRESS_MULTILINE_FOR_ONEOFLABEL	1
#define SETUP_PASSWORD_NON_CASE_SENSITIVE	0
#define SETUP_JPEG_LOGO_SUPPORT	1
#define OptionRomPolicy_SUPPORT	0
#define OEM_MANF_ID	0xc5c6
#define BmcLanConfig_Live_status	1
#define FRU_SMBIOS_BINARY_TYPE_CODE_SUPPORT	1
#define BMC_INIT_DELAY	0
#define AMI_IPMI_DEFAULT_SENSOR_NUMBER	0x1f
#define REDFISH_ENABLE_DEFAULT_VALUE	1
#define RfBootOptions_DisableIfFboPresent	1
#define SEND_FIRMWARE_LAYOUT_ON_FIRST_LAUNCH	0
#define USB_FLOPPY_GROUP_SUPPORT	0
#define USB_LAN_GROUP_SUPPORT	0
#define SD_GROUP_SUPPORT	0
#define ODD_GROUP_SUPPORT	0
#define DEFAULT_FBO_BOOTMODE	1
#define FIXED_BOOT_DISPLAY_DEVICE_NAME_IN_SETUP	0
#define FIXED_BOOT_ORDER_SUBMENU_MAX_NUM	0x1e
#define EARLIEST_YEAR	0x7e4
#define OLDEST_YEAR	0x834
#define DEFAULT_YEAR	0x7e4
#define MAXIMUM_YEAR	0x834
#define TSE_MAXIMUM_YEAR	0x834
#define OEM_DEFAULT_TPM_SUPPORT	1
#define XHCI_EVENT_SERVICE_MODE	0x1
#define SRIOV_SUPPORT_DEFAULT_VALUE	1
#define ABOVE_4G_PCI_DECODE_DEFAULT_VALUE	1
#define SETUP_SHOW_HOTPLUG_SUPPORT	0
#define COMPAL_SETUP_GRAYOUT_ABOVE_4G_DECODE	1
#define GC_MODE0	{ 0, 80, 25, 1024, 768 }
#define CUSTOMIZED_SECURE_BOOT_DEPLOYMENT_SETUP	1
#define DEFAULT_SECURE_BOOT_ENABLE	0
#define DEFAULT_PROVISION_SECURE_VARS	1
#define DEFAULT_SECURE_BOOT_MODE	0
#define ENABLE_SECURE_FLASH_INFO_PAGE	0
#define OEM_ESRT_FIRMWARE_GUID	{0x6095ac68, 0xd66c, 0x4b2d, {0x94, 0x28, 0x7a, 0x8a, 0x1b, 0xeb, 0xe7, 0xb5}}
#define TPM2_DISABLE_PLATFORM_HIERARCHY_RANDOMIZATION	1
#define GpnvErrorLogging_SUPPORT	0
#define DXE_DRIVER_AgesaModulePkg_Mem_AmdMemBrhSp5Dxe_AmdMemBrhSp5Dxe_SUPPORT	0
#define COMPAL_SLOT_DRIVER_HII_PREFIX_SUPPORT	0
#define BMC_POWER_SAVE_MODE_BY_BIOS_SUPPORT	0
#define OEM_CRB_SETUP_CONFIG_SUPPORT	0
#define OEM_FASTBOOT_SETUP_CONFIG_SUPPORT	0
#define TOKEN_MAX_DDR_FREQUENCY_1_DIMM	3000
#define AMI_SYS_INV_FILTER_LOGICAL_STORAGE_DEVICE	0x1
#define BOARD_OVERRIDE_SUPPORT	1
#define BIOS_PACKAGE_SUPPORT	1
#define PFR_SIGN_BUILD_SUPPORT	0
#define PFR_FLASH_COMMAND_SUPPORT	0
#define CPM_BUILD_SUPPORT	1
#define SIGN_SERVER_IP	*************
#define SIGN_SERVER_PW	Pass2008
#define TAR_BUILD_SUPPORT	0
#define BIOS_PACKAGE_BUILD_SUPPORT	1
#define FULL_BIOS_RELEASE_FLODER	FULL_KERP3_BIOS_010300
#define SIMPLE_BIOS_RELEASE_FLODER	KERP3_BIOS_010300
#define FLASH_PACKAGE_FOLDER	FlashPackage
#define LINUX_FOLDER	BIOSLinux64
#define ROM_FOLDER	ROM
#define UEFI_SHELL_FOLDER	BIOSUEFIShell
#define WINDOWS_FOLDER	BIOSWIN64
#define RECOVERY_FOLDER	Recovery
#define SIGN_BIOS_FOLDER	WebUpdate
#define OBMC_SIGN_BIOS_FOLDER	WebUpdate_OpenBMC
#define FLASH_FILE_NAME	FlashBIOS
#define RELEASE_NOTE_FOLDER	$(BIOS_PACKAGE_BUILD_DIR)
#define RELEASE_CHECKLIST_FILE_NAME	KERP3_BIOS_Release_Checklist.xlsx
#define AFU_VERSION	AFU_5.16.02.0111
#define RELEASE_NOTE_BUILD_SUPPORT	1
#define RELEASE_NOTE_FILE_NAME	Release_Note.txt
#define AMD_AGEGA_PI_VERSION	TurinPI-SP5 *******
#define OEM_SOP_SUPPORT	0
#define OEM_KEYS_SUPPORT	1
#define SecurityFlashKeyPairs_SUPPORT	1
#define SecureBootKeys_SUPPORT	1
#define ADDON_SUPPORT	1
#define AST2600_VIDEO_ROM_SUPPORT	0
#define I350_OPROM_SUPPORT	0
#define OEM_LOGO_DYNAMIC_SUPPORT	0
#define OEM_LIBRARY_SUPPORT	1
#define EARLY_VIDEO_BMC_IP_SUPPORT	1
#define BMC_LAN_3_STRING	CMC LAN 
#define EARLY_BMC_LAN_1_STRING_SHOW	L" Dedicated LAN IP: %d.%d.%d.%d \n\r"
#define EARLY_BMC_LAN_2_STRING_SHOW	L" Shared LAN IP: %d.%d.%d.%d \n\r"
#define EARLY_BMC_LAN_3_STRING_SHOW	L" CMC LAN IP: %d.%d.%d.%d \n\r"
#define DEFAULT_IP_ADDRESS	0,0,0,0
#define CMCBmcLanConfig_SUPPORT	0
#define COMPAL_SETUP_SUPPORT	1
#define COMPAL_SETUP_MAIN_SUPPORT	1
#define CBS_SETUPDEFAULT_SUPPORT	1
#define COMPAL_DEF_HIDE_SETUP_ITEM	1
#define COMPAL_DEF_SP_SETUP_ITEM_0	1
#define COMPAL_DEF_ACTIVE_CONDITION	0
#define COMPAL_DEF_SP_ACTIVE_CONDITION_0	0
#define COMPAL_DEF_CbsDbgFchSystemSpreadSpectrum	0xf
#define COMPAL_DEF_CbsCmnDeterminismCtl	1
#define COMPAL_DEF_CbsCmnDeterminismEnable	0
#define COMPAL_DEF_CbsCmnEfficiencyModeEn	1
#define COMPAL_DEF_CbsCmnApbdis	0xf
#define COMPAL_DEF_CbsCmnGnbSmuCppc	0xf
#define COMPAL_DEF_CbsDfCmnAcpiSratL3Numa	255
#define COMPAL_DEF_CbsCmnCpuCstC2Latency	100
#define COMPAL_DEF_CbsCmnGnbSmuDfCstates	0xf
#define COMPAL_DEF_CbsCmnCpuGlobalCstateCtrl	3
#define COMPAL_DEF_CbsCmnCpuL1StreamHwPrefetcher	3
#define COMPAL_DEF_CbsCmnCpuL1StridePrefetcher	3
#define COMPAL_DEF_CbsCmnCpuL1RegionPrefetcher	3
#define COMPAL_DEF_CbsCmnCpuL2StreamHwPrefetcher	3
#define COMPAL_DEF_CbsCmnCpuL2UpDownPrefetcher	3
#define COMPAL_DEF_CbsCmnCpuL1BurstPrefetchMode	3
#define COMPAL_DEF_CbsDbgGnbDbgAERCAPEnable	0xF
#define COMPAL_DEF_CbsDbgFchSataSgpio0	0xf
#define APCB_TOKEN_UID_I2C_I3C_SMBUS_0_VALUE	0x0
#define APCB_TOKEN_UID_I2C_I3C_SMBUS_1_VALUE	0x0
#define APCB_TOKEN_UID_I2C_I3C_SMBUS_2_VALUE	0x0
#define APCB_TOKEN_UID_I2C_I3C_SMBUS_3_VALUE	0x0
#define COMPAL_DEF_CbsCmnFchI2C4Config	0xf
#define COMPAL_DEF_CbsCmnFchReleaseSpdHostControl	0
#define COMPAL_DEF_CbsCmnGnbPowerSupplyIdleCtrl	0xf
#define COMPAL_DEF_CbsCmnCpuSmee	3
#define COMPAL_DEF_CbsPspSevCtrl	0
#define APCB_TOKEN_UID_PSP_SEV_DISABLE_VALUE	0x0
#define COMPAL_DEF_CbsDfCmnDramNps	7
#define APCB_TOKEN_UID_DF_DRAM_NPS_VALUE	0x7
#define COMPAL_DEF_CbsCmnCpuPfeh	3
#define COMPAL_DEF_CbsDfCmnMemIntlv	7
#define APCB_TOKEN_UID_DF_MEM_INTERLEAVING_VALUE	0x7
#define COMPAL_DEF_CbsDfCmnCc6AllocationScheme	0xFF
#define APCB_TOKEN_UID_DF_SYS_STORAGE_AT_TOP_OF_MEM_VALUE	0xff
#define COMPAL_DEF_CbsCmnMemPeriodicTrainingModeDdr	1
#define APCB_TOKEN_UID_MEM_PPT_CTRL_DDR_VALUE	0x1
#define COMPAL_DEF_CbsCpuSmtCtrl	0xF
#define APCB_TOKEN_UID_CCX_SMT_CTRL_VALUE	0xf
#define COMPAL_DEF_CbsDbgFchSata0Enable	0xf
#define COMPAL_DEF_CbsDbgFchSataeSATAPort0	0xf
#define COMPAL_DEF_CbsDbgFchSataeSATAPort1	0xf
#define COMPAL_DEF_CbsDbgFchSataeSATAPort2	0xf
#define COMPAL_DEF_CbsDbgFchSataeSATAPort3	0xf
#define COMPAL_DEF_CbsDbgFchSataeSATAPort4	0xf
#define COMPAL_DEF_CbsDbgFchSataeSATAPort5	0xf
#define COMPAL_DEF_CbsDbgFchSataeSATAPort6	0xf
#define COMPAL_DEF_CbsDbgFchSataeSATAPort7	0xf
#define COMPAL_DEF_CbsHotPlugHandlingMode	0xF
#define COMPAL_DEF_CbsPresenceDetectSelectmode	0xF
#define COMPAL_DEF_CbsSevSnpSupport	0xf
#define COMPAL_DEF_CbsCmnDrtmMemoryReservation	0xFF
#define COMPAL_DEF_CbsCmnDrtmSupport	0xF
#define COMPAL_DEF_CbsCmnDmaProtection	0xF
#define COMPAL_DEF_CbsCmnGnbNbIOMMU	0xf
#define COMPAL_DEF_CbsCmnDmarSupport	0xF
#define CBS_ORIGINAL_SUPPORT	0
#define OEM_CBS_OVERRIDE_SUPPORT	1
#define OEM_CBS_TOOLS_SUPPORT	1
#define COMPAL_SETUP_ADVANCED_SUPPORT	1
#define DEFAULT_PERFORMANCE_MODE	0x0
#define DEFAULT_POWER_PROFILE_SELECTION_BALANCE	0x1
#define DEFAULT_POWER_PROFILE_SELECTION_PERFORMANCE	0x0
#define DEFAULT_POWER_PROFILE_SELECTION	0x1
#define DEFAULT_DETERMINISM_ENABLE_BALANCE	0x0
#define DEFAULT_DETERMINISM_ENABLE_PERFORMANCE	0x0
#define DEFAULT_DETERMINISM_ENABLE	0x0
#define DEFAULT_ACPI_SRAT_L3_CACHE_AS_NUMA_DOMAIN	0xff
#define DEFAULT_APBDIS	0xf
#define DEFAULT_ACPI_CST_C2_LATENCY_BALANCE	0x64
#define DEFAULT_ACPI_CST_C2_LATENCY_PERFORMANCE	0x12
#define DEFAULT_ACPI_CST_C2_LATENCY	0x64
#define DEFAULT_DF_CSTATES_BALANCE	0xf
#define DEFAULT_DF_CSTATES_PERFORMANCE	0x0
#define DEFAULT_DF_CSTATES	0xf
#define DEFAULT_GLOBAL_CSTATES	0x3
#define COMPAL_DEF_CbsCfgSriovEn	1
#define DEFAULT_TPM_DEVICE_TYPE	0x2
#define COMPAL_SETUP_SERVERMGMT_SUPPORT	1
#define DEFAULT_POWER_BUTTON	0x0
#define DEFAULT_POWER_RESTORE_POLICY	0x2
#define BMC_LAN3_SUPPORT	0
#define SETUP_BMC_LAN_1_STRING_SHOW	L"Dedicated LAN "
#define SETUP_BMC_LAN_2_STRING_SHOW	L"Shared LAN "
#define SETUP_BMC_LAN_3_STRING_SHOW	L"CMC LAN "
#define COMPAL_SETUP_EXIT_SUPPORT	1
#define OEM_REDFISH_SETUP_SUPPORT	1
#define SUPPORT_COMPAL_MAPPING	1
#define COMPAL_BDS_SUPPORT	1
#define BOOT_OPTION_NAME_SUFFIX_FUNCTION	OemConstructBootOptionNameSuffixDefault
#define OEM_UPDATE_SMBIOS_SUPPORT	1
#define UPDATE_SMBIOS_TYPE4_CPU_MAX_SPEED	1
#define COMPAL_UPDATE_SMBIOS_TYPE9_M2_SLOT	0
#define COMPAL_UPDATE_SMBIOS_TYPE9_NVME_SLOT	0
#define SMBIOS_TABLES_BUILD_TYPE	1
#define TYPE9_STATIC_DATA_INFO_SUPPORT	0
#define OEM_UPDATE_FRUSMBIOS_SUPPORT	1
#define I2CcontrolPei_SUPPORT	1
#define BIOS_STATE_SUPPORT	1
#define BIOS_FW_INFO_SUPPORT	1
#define OemRecovery_SUPPORT	0
#define DRAW_HOTKEY_INFORMATON_SUPPORT	1
#define DETECT_ERRORS_SUPPORT	1
#define USBOC_ERROR_SELECT	0x1
#define TOKEN_1stUsbPortPosition	0x0
#define TOKEN_2ndUsbPortPosition	0x1
#define OEM_DEVICE_INFORMATION_SUPPORT	1
#define OEM_MAC_ADDRESS_SUPPORT	1
#define OEM_DEVICE_CONTROL_SUPPORT	1
#define FilterLanPortBoot_SUPPORT	1
#define SORT_ETHERNET_BOOT_PRIORITIES_SUPPORT	0
#define MANUFACTURE_MODE_SUPPORT	1
#define MANUFACTURE_MODE_OVERRIDE_SUPPORT	0
#define OEM_REDFISH_PORTING_SUPPORT	1
#define COMPAL_SYSTEM_INVENTORY_INFO_SUPPORT	1
#define DetectSpecificCardToBMC_SUPPORT	1
#define Detect_RAID_CARD_SUPPORT	1
#define Detect_OCP_CARD_SUPPORT	1
#define OEM_MISCFUNCTION_SUPPORT	1
#define RTCWAKEUP_POWERBUTTON_SUPPORT	1
#define OPENBMC_SMBIOS_SUPPORT	0
#define CompalDebugConfigLib_SUPPORT	0
#define CompalDebugPrintErrorLevelLib_SUPPORT	0
#define EA_TEST_SUPPORT	1
#define EA_MEM_EYE_SUPPORT	0
#define EA_SATA_RX_SUPPORT	0
#define OEM_CHANGE_LOGO_SUPPORT	0
#define KptlLogo_SUPPORT	0
#define FRU_IMGID_INFO_SUPPORT	0
#define CPENGINELOGO_SUPPORT	0
#define CUSTOMERS_SUPPORT	1
#define HIDE_NETWORKSTACK_ONEOF_IPV4_HTTP_SUPPORT_ITEM	0
#define HIDE_NETWORKSTACK_ONEOF_IPV6_PXE_SUPPORT_ITEM	0
#define HIDE_NETWORKSTACK_ONEOF_IPV6_HTTP_SUPPORT_ITEM	0
#define HIDE_NETWORKSTACK_NUMERIC_PXE_BOOT_WAIT_TIME_ITEM	0
#define HIDE_NETWORKSTACK_NUMERIC_MEDIA_DETECT_ITEM	0
#define HIDE_PCIE_OPTIONS_FORM	1
#define HIDE_TERMINAL_FORM	1
#define HIDE_PCIE_PORT_LINK_SPEED_ITEM	1
#define HIDE_SETUP_ITEM_VGA_SELECT	1
#define HIDE_SETUP_ITEM_BOOT_MODE	1
#define BMC_RESET_DEFAULT_SUPPORT	0
#define IERxx_PROJECT_SUPPORT	0
#define IERxx_BOARD_OVERRIDE_SUPPORT	0
#define IERxx_ASL_SUPPORT	0
#define BIOS_VERSION_SUPPORT	1
#define IERxx_SmbiosLib_SUPPORT	0
#define IERxx_ROUTING_TABLE_SUPPORT	0
#define IERxx_SETUP_SUPPORT	0
#define IERxx_SETUP_ADVANCED_SUPPORT	0
#define IERxx_REDFISH_SETUP_SUPPORT	0
#define KER85_PROJECT_SUPPORT	0
#define KER85_BOARD_OVERRIDE_SUPPORT	0
#define KER85_DUAL_BIOS_SUPPORT	0
#define KER85_ASL_SUPPORT	0
#define KER85_SmbiosLib_SUPPORT	0
#define KER85_ROUTING_TABLE_SUPPORT	0
#define KER85_SETUP_SUPPORT	0
#define KER85_SETUP_ADVANCED_SUPPORT	0
#define KER85_REDFISH_SETUP_SUPPORT	0
#define JERG2_PROJECT_SUPPORT	0
#define JERG2_BOARD_OVERRIDE_SUPPORT	0
#define JERG2_DUAL_BIOS_SUPPORT	0
#define JERG2_ASL_SUPPORT	0
#define JERG2_SmbiosLib_SUPPORT	0
#define JERG2_ROUTING_TABLE_SUPPORT	0
#define JERG2_SETUP_SUPPORT	0
#define JERG2_SETUP_ADVANCED_SUPPORT	0
#define JERG2_REDFISH_SETUP_SUPPORT	0
#define KER55_PROJECT_SUPPORT	0
#define KER55_BOARD_OVERRIDE_SUPPORT	0
#define KER55_DUAL_BIOS_SUPPORT	0
#define KER55_ASL_SUPPORT	0
#define KER55_SmbiosLib_SUPPORT	0
#define KER55_ROUTING_TABLE_SUPPORT	0
#define KER55_SETUP_ADVANCED_SUPPORT	0
#define KER55_REDFISH_SETUP_SUPPORT	0
#define COMPAL_SMALL_LOGO_SUPPORT	0
#define MEDIATEK_SUPPORT	0
#define MARVEL_SUPPORT	0
#define YADRO_SUPPORT	0
#define OVH_SUPPORT	0
#define ThomasKrenn_SUPPORT	0
#define PHXFLYS_SUPPORT	0
#define ExtraComputer_SUPPORT	0
#define KPTL_SUPPORT	0
#define KER85_OVH_SUPPORT	0
#define KER85_TK_SUPPORT	0
#define KER85_KPTL_SUPPORT	0
#define KER85_ExtraComputer_SUPPORT	0
#define KERP3_PROJECT_SUPPORT	1
#define PRODUCT_SUPPORT	SX420-2A
#define PLATFORM_SELECT	0x5
#define DUALIMAGE_SUPPORT	0
#define SECOND_CPM_BUILD_SUPPORT	0
#define SECOND_PROJECT_CODE	IER85
#define TOTAL_SIO_SERIAL_PORTS	0x0
#define TOTAL_PCI_SERIAL_PORTS	0x2
#define OEM_RECOVERY_GPIO_NUM	0xffff
#define OEM_CLEAR_PASSWORD_GPIO_NUM	0xffff
#define VGA_ROOT_PORT_DEVICE	0x3
#define VGA_ROOT_PORT_FUN	0x4
#define TOKEN_EarlyBmcLinkTrainingSupport	1
#define TOKEN_EarlyBmcLinkLaneNum	0x87
#define TOKEN_BONUS1_EarlyLinkLaneStartNum	0x86
#define TOKEN_BONUS1_EarlyLinkLaneEndNum	0x86
#define TOKEN_BONUS1_DeviceNum	0x3
#define TOKEN_BONUS1_FuncNum	0x3
#define TOKEN_BONUS2_EarlyLinkLaneStartNum	0x80
#define TOKEN_BONUS2_EarlyLinkLaneEndNum	0x83
#define TOKEN_BONUS2_DeviceNum	0x3
#define TOKEN_BONUS2_FuncNum	0x1
#define PFR_FV_SUPPORT	0
#define COMPAL_DEF_CbsDfDbgXgmiLinkCfg	2
#define COMPAL_DEF_CbsCmnFchUsbXHCI2Enable	1
#define COMPAL_DEF_CbsCmnFchUsbXHCI3Enable	1
#define COMPAL_DEF_CbsCmnFchI3C0Config	0
#define COMPAL_DEF_CbsCmnFchI3C1Config	0
#define COMPAL_DEF_CbsCmnFchI3C2Config	0
#define COMPAL_DEF_CbsCmnFchI3C3Config	0
#define COMPAL_DEF_CbsCmnFchI2C5Config	0xf
#define COMPAL_DEF_CbsCmnCpuPpinCtrl	1
#define COMPAL_DEF_CbsCmnCxlControl	0
#define COMPAL_DEF_CbsCmnFchSystemPwrFailShadow	0
#define COMPAL_DEF_CbsCmnEarlyLinkSpeed	2
#define APCB_TOKEN_UID_FCH_SATA_0_ENABLE_VALUE	0x1
#define APCB_TOKEN_UID_FCH_SATA_1_ENABLE_VALUE	0x0
#define APCB_TOKEN_UID_FCH_SATA_2_ENABLE_VALUE	0x0
#define APCB_TOKEN_UID_FCH_SATA_3_ENABLE_VALUE	0x0
#define APCB_TOKEN_UID_FCH_SATA_4_ENABLE_VALUE	0x0
#define APCB_TOKEN_UID_FCH_SATA_5_ENABLE_VALUE	0x0
#define APCB_TOKEN_UID_FCH_SATA_6_ENABLE_VALUE	0x0
#define APCB_TOKEN_UID_FCH_SATA_7_ENABLE_VALUE	0x0
#define IPMI_KCS_DELAY_PER_RETRY	0x190
#define MAX_BMC_CMD_FAIL_COUNT	0x14
#define IPMI_SMM_KCS_COMMAND_PORT_READ_RETRY_COUNTER	0x7530
#define IPMI_SELF_TEST_COMMAND_RETRY_COUNT	0x1
#define RISER_0_ID_CMOS_BYTE	0x50
#define RISER_1_ID_CMOS_BYTE	0x51
#define RISER_2_ID_CMOS_BYTE	0x52
#define RISER_3_ID_CMOS_BYTE	0x53
#define RISER_4_ID_CMOS_BYTE	0x54
#define HDDBP_ID_CMOS_BYTE	0x55
#define HDDBP_ID_FLAG_CMOS_BYTE	0x56
#define SW_ID_FLAG_CMOS_BYTE	0x57
#define PCB_ID_FLAG_CMOS_BYTE	0x58
#define SW2_CFG_FLAG_CMOS_BYTE	0x59
#define OEM_3_4_GMI_AUTO_DETECT	1
#define GRAID_CARD_SUPPORT	0x0
#define Win2022_SecoreBootLogo_SUPPORT	0
#define ESPI_BRH_EFS	0xffff0eff
#define SERIAL_PORT_ESPI_CONTROLLER_VALUE	0x19
#define NUMBER_OF_DIMMS_PER_CHANNEL	0x2
#define SIGN_SERVER_PRIVATE_KEY	Signed_hash_private_key_kerp3.pem
#define SUPPORT_HPM_FILE	1
#define SUPPORT_BOARD_ID_IN_CONF	1
#define BOARD_ID_IN_CONF	0x1108,0x00FF
#define FCH_UART_DEBUG_SELECT	0x2
#define USER_DEFINED_LAN_CHANNEL_SUPPORT	1
#define LAN1_CHANNEL_NUMBER	0x1
#define LAN2_CHANNEL_NUMBER	0x8
#define BMC_LAN_1_STRING	Dedicated LAN 
#define BMC_LAN_2_STRING	Shared LAN 
#define COMPAL_DEF_CbsPcdSyncFloodToApml	0x1
#define CUSTOM_SLOT_MAP_SUPPORT	1
#define DEFAULT_FRB2_TIMEOUT	0xa
#define KERP3_BOARD_OVERRIDE_SUPPORT	1
#define CUSTOMER_NAME	COMPAL
#define RELEASE_OFFICIAL_BIOS	1
#define BOARD_REVISION	0x1
#define BIOS_VERSION	0x3
#define PROJECT_TAG	KERP3
#define BIOS_TAG	KERP3
#define SIGN_SERVER_FOLDER_PATH	KERP3_sign_BIOS
#define AMI_ROM	$(BIOS_NAME).bin
#define AMI_ROM_32	$(BIOS_NAME)_32.bin
#define PRODUCT_NAME	SX420-2A
#define FIRMWARE_VERSION	01.03.00
#define CUSTOMER_ID	00
#define RELEASE_DATE	$(shell $(DATE) +'%Y%m%d')
#define BIOS_NAME	$(PRODUCT_NAME)_$(FIRMWARE_VERSION)_$(CUSTOMER_ID)_BIOS_$(RELEASE_DATE)
#define BIOS_NUMBER	01.03.00
#define COMPAL_PROJECT_MAJOR_VERSION	0x1
#define COMPAL_PROJECT_MINOR_VERSION	0x3
#define COMPAL_PROJECT_AUXILIARY_VERSION	0x0
#define KERP3_ROUTING_TABLE_SUPPORT	1
#define KERP3_SETUP_SUPPORT	1
#define KERP3_SETUP_ADVANCED_SUPPORT	1
#define OEM_PCIE_SETUP_CONFIG_SUPPORT	0
#define HIDE_CPU0_P0_CONFIGURATION	0x1
#define HIDE_CPU0_P1_CONFIGURATION	0x1
#define HIDE_CPU0_P2_CONFIGURATION	0x1
#define HIDE_CPU0_P3_CONFIGURATION	0x1
#define HIDE_CPU0_G3_CONFIGURATION	0x1
#define HIDE_CPU1_P0_CONFIGURATION	0x1
#define HIDE_CPU1_P1_CONFIGURATION	0x1
#define HIDE_CPU1_P2_CONFIGURATION	0x1
#define HIDE_CPU1_P3_CONFIGURATION	0x1
#define HIDE_CPU1_G1_CONFIGURATION	0x1
#define DEFAULT_CPU0_PCIE_P0_ENABLE	0x1
#define DEFAULT_CPU0_PCIE_P1_ENABLE	0x1
#define DEFAULT_CPU0_PCIE_P2_ENABLE	0x1
#define DEFAULT_CPU0_PCIE_P3_ENABLE	0x1
#define DEFAULT_CPU0_PCIE_G3_ENABLE	0x1
#define DEFAULT_CPU1_PCIE_P0_ENABLE	0x1
#define DEFAULT_CPU1_PCIE_P1_ENABLE	0x1
#define DEFAULT_CPU1_PCIE_P2_ENABLE	0x1
#define DEFAULT_CPU1_PCIE_P3_ENABLE	0x1
#define DEFAULT_CPU1_PCIE_G1_ENABLE	0x1
#define DEFAULT_CPU0_PCIE_P0_WIDTH	0x1
#define DEFAULT_CPU0_PCIE_P1_WIDTH	0x5
#define DEFAULT_CPU0_PCIE_P2_WIDTH	0x1
#define DEFAULT_CPU0_PCIE_P3_WIDTH	0x1
#define DEFAULT_CPU0_PCIE_G3_WIDTH	0x1
#define DEFAULT_CPU1_PCIE_P0_WIDTH	0x1
#define DEFAULT_CPU1_PCIE_P1_WIDTH	0x5
#define DEFAULT_CPU1_PCIE_P2_WIDTH	0x1
#define DEFAULT_CPU1_PCIE_P3_WIDTH	0x1
#define DEFAULT_CPU1_PCIE_G1_WIDTH	0x1
#define DEFAULT_CPU0_PCIE_P0_SPEED	0x0
#define DEFAULT_CPU0_PCIE_P1_SPEED	0x0
#define DEFAULT_CPU0_PCIE_P2_SPEED	0x0
#define DEFAULT_CPU0_PCIE_P3_SPEED	0x0
#define DEFAULT_CPU0_PCIE_G3_SPEED	0x0
#define DEFAULT_CPU1_PCIE_P0_SPEED	0x0
#define DEFAULT_CPU1_PCIE_P1_SPEED	0x0
#define DEFAULT_CPU1_PCIE_P2_SPEED	0x0
#define DEFAULT_CPU1_PCIE_P3_SPEED	0x0
#define DEFAULT_CPU1_PCIE_G1_SPEED	0x0
#define DEFAULT_CPU0_PCIE_P0_ASPM	0x0
#define DEFAULT_CPU0_PCIE_P1_ASPM	0x0
#define DEFAULT_CPU0_PCIE_P2_ASPM	0x0
#define DEFAULT_CPU0_PCIE_P3_ASPM	0x0
#define DEFAULT_CPU0_PCIE_G3_ASPM	0x0
#define DEFAULT_CPU1_PCIE_P0_ASPM	0x0
#define DEFAULT_CPU1_PCIE_P1_ASPM	0x0
#define DEFAULT_CPU1_PCIE_P2_ASPM	0x0
#define DEFAULT_CPU1_PCIE_P3_ASPM	0x0
#define DEFAULT_CPU1_PCIE_G1_ASPM	0x0
#define KERP3_REDFISH_SETUP_SUPPORT	0
#define KERP3_ASL_SUPPORT	1
#define KERP3_SmbiosLib_SUPPORT	1
#endif
