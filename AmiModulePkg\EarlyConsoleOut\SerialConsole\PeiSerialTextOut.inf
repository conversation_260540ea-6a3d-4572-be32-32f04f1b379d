#***********************************************************************
#*                                                                     *
#*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
#*                                                                     *
#*      All rights reserved. Subject to AMI licensing agreement.       *
#*                                                                     *
#***********************************************************************
## @file PeiSerialTextOut.inf
#  Produces the PPI for the Displaying data to the Serial device
##

[Defines]
  INF_VERSION           = 0x00010005
  VERSION_STRING        = 1.0
  BASE_NAME             = PeiSerialTextOut
  MODULE_TYPE           = PEIM
  FILE_GUID             = 8FD1935A-6A80-4535-B714-C3CBE45A5C5C
  ENTRY_POINT           = PeiSerialTextOutEntry

[Sources]
  PeiSerialTextOut.c

[LibraryClasses]
  PeimEntryPoint
  PrintLib
  PcdLib
  SerialTextOutLib

[Ppis]
  gAmiSimpleTextOutPpiGuid
  gEfiPeiMemoryDiscoveredPpiGuid

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  AmiModulePkg/AmiModulePkg.dec
  AmiModulePkg/EarlyConsoleOut/AmiEarlyConsoleOutPkg.dec 
  
[Pcd]
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdSerialSimpleTextOutPpiInstance
  gAmiModulePkgTokenSpaceGuid.PcdDefaultCursorState
  
[Depex]
  TRUE

  