#***********************************************************************
#*                                                                     *
#*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
#*                                                                     *
#*      All rights reserved. Subject to AMI licensing agreement.       *
#*                                                                     *
#***********************************************************************

## @file
#  Cryptographic Library Instance for SMM driver.
#
#  Caution: This module requires additional review when modified.
#  This library will have external input - signature.
#  This external input must be validated carefully to avoid security issues such as
#  buffer overflow or integer overflow.
#
#  Note: SHA-384 Digest functions, SHA-512 Digest functions,
#  RSA external functions, PKCS#7 SignedData sign functions, Diffie-<PERSON>man functions, and
#  authenticode signature verification functions are not supported in this instance.
#
#  Copyright (c) 2023, Intel Corporation. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = SmmCryptLib
  MODULE_UNI_FILE                = SmmCryptLib.uni
  FILE_GUID                      = CF104633-9901-4504-AD7A-91690926A253
  MODULE_TYPE                    = DXE_SMM_DRIVER
  VERSION_STRING                 = 1.0
  PI_SPECIFICATION_VERSION       = 0x0001000A
  LIBRARY_CLASS                  = BaseCryptLib|DXE_SMM_DRIVER SMM_CORE MM_STANDALONE

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 ARM AARCH64
#

[Sources]
  InternalCryptLib.h
  Hash/CryptMd5.c
  Hash/CryptSha1.c
  Hash/CryptSha256.c
  Hash/CryptSha512.c
  Hash/CryptParallelHashNull.c
  Hash/CryptSm3.c
  Hmac/CryptHmac.c
  Kdf/CryptHkdf.c
  Cipher/CryptAes.c
  Cipher/CryptAeadAesGcmNull.c
  Pk/CryptRsaBasic.c
  Pk/CryptRsaExtNull.c
  Pk/CryptPkcs1Oaep.c
  Pk/CryptPkcs5Pbkdf2.c
  Pk/CryptPkcs7SignNull.c
  Pk/CryptPkcs7VerifyCommon.c
  Pk/CryptPkcs7VerifyBase.c
  Pk/CryptPkcs7VerifyEku.c
  Pk/CryptDhNull.c
  Pk/CryptX509.c
  Pk/CryptAuthenticodeNull.c
  Pk/CryptTsNull.c
  Pk/CryptRsaPss.c
  Pk/CryptRsaPssSignNull.c
  Pk/CryptEcNull.c
  Pem/CryptPem.c
  Bn/CryptBnNull.c
  Rand/CryptRand.c

  SysCall/CrtWrapper.c
  #SysCall/DummyOpensslSupport.c #APTIOV OVERRIDE to avoid - abort,fclose,isspace.. already defined in CrtWrapper.c
  SysCall/BaseMemAllocation.c
  SysCall/ConstantTimeClock.c

[Packages]
  MdePkg/MdePkg.dec
  CryptoPkg/CryptoPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  MemoryAllocationLib
  MbedTlsLib
  OpensslLib
  IntrinsicLib
  PrintLib
  MmServicesTableLib
  RngLib
  SynchronizationLib

#
# Remove these [BuildOptions] after this library is cleaned up
#
[BuildOptions]
  XCODE:*_*_*_CC_FLAGS = -mmmx -msse -std=c99

  GCC:*_CLANGDWARF_*_CC_FLAGS = -std=gnu99
  GCC:*_CLANGPDB_*_CC_FLAGS = -std=c99 -Wno-error=incompatible-pointer-types
