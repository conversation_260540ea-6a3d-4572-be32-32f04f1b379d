#***********************************************************************
#*                                                                     *
#*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
#*                                                                     *
#*      All rights reserved. Subject to AMI licensing agreement.       *
#*                                                                     *
#***********************************************************************

## @file
#  Cryptographic Library Instance for DXE_DRIVER.
#
#  Caution: This module requires additional review when modified.
#  This library will have external input - signature.
#  This external input must be validated carefully to avoid security issues such as
#  buffer overflow or integer overflow.
#
#  Copyright (c) 2009 - 2022, Intel Corporation. All rights reserved.<BR>
#  Copyright (c) 2020, Hewlett Packard Enterprise Development LP. All rights reserved.<BR>
#  Copyright (c) 2022, Loongson Technology Corporation Limited. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = BaseCryptLib
  MODULE_UNI_FILE                = BaseCryptLib.uni
  FILE_GUID                      = be3bb803-91b6-4da0-bd91-a8b21c18ca5d
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = BaseCryptLib|DXE_DRIVER DXE_CORE UEFI_APPLICATION UEFI_DRIVER

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF ARM AARCH64 RISCV64 LOONGARCH64 #APTIOV OVERRIDE
#

[Sources]
  InternalCryptLib.h
  Hash/CryptMd5.c
  Hash/CryptSha1.c
  Hash/CryptSha256.c
  Hash/CryptSha512.c
  Hash/CryptSm3.c
  Hash/CryptSha3.c
  Hash/CryptXkcp.c
  Hash/CryptCShake256.c
  Hash/CryptParallelHash.c
  Hash/CryptDispatchApDxe.c
  Hmac/CryptHmac.c
  Kdf/CryptHkdf.c
  Cipher/CryptAes.c
  Cipher/CryptAeadAesGcm.c
  Pk/CryptRsaBasic.c
  Pk/CryptRsaExt.c
  Pk/CryptPkcs1Oaep.c
  Pk/CryptPkcs5Pbkdf2.c
  Pk/CryptPkcs7Sign.c
  Pk/CryptPkcs7VerifyCommon.c
  Pk/CryptPkcs7VerifyBase.c
  Pk/CryptPkcs7VerifyEku.c
  Pk/CryptDh.c
  Pk/CryptX509.c
  Pk/CryptAuthenticode.c
  Pk/CryptTs.c
  Pk/CryptRsaPss.c
  Pk/CryptRsaPssSign.c
  Pk/CryptEc.c
  Pem/CryptPem.c
  Bn/CryptBn.c

# SysCall/CrtWrapper.c       #APTIOV OVERRIDE - CrtWrapperLib is reported as library instance, so that it can be overriden by other modules. 
                             # RemoteBios module overrides this instance
  SysCall/TimerWrapper.c
  SysCall/BaseMemAllocation.c

[Sources.Ia32]
  Rand/CryptRandTsc.c

[Sources.X64]
  Rand/CryptRandTsc.c

#APTIOV OVERRIDE starts
[Sources.IPF]
  Rand/CryptRandItc.c
#APTIOV OVERRIDE ends

[Sources.ARM]
  Rand/CryptRand.c

[Sources.AARCH64]
  Rand/CryptRand.c

[Sources.RISCV64]
  Rand/CryptRand.c

[Sources.LOONGARCH64]
  Rand/CryptRand.c

[Packages]
  MdePkg/MdePkg.dec
  CryptoPkg/CryptoPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  MemoryAllocationLib
  UefiRuntimeServicesTableLib
  DebugLib
  OpensslLib
  IntrinsicLib
  PrintLib
  CrtWrapperLib  #APTIOV OVERRIDE - CrtWrapperLib is reported as library instance, so that it can be overriden by other modules. 
                 # RemoteBios module overrides this instance
  UefiBootServicesTableLib
  SynchronizationLib

[Protocols]
  gEfiMpServiceProtocolGuid

#
# Remove these [BuildOptions] after this library is cleaned up
#
[BuildOptions]
  #
  # suppress the following warnings so we do not break the build with warnings-as-errors:
  #
  GCC:*_CLANGDWARF_*_CC_FLAGS = -std=c99 -Wno-error=incompatible-pointer-types
  GCC:*_CLANGPDB_*_CC_FLAGS = -std=c99 -Wno-error=incompatible-pointer-types

  XCODE:*_*_*_CC_FLAGS = -std=c99
  # APTIOV_OVERRIDE To suppress warning C4204: nonstandard extension used
  MSFT:*_*_IA32_CC_FLAGS   =  /wd4204
  MSFT:*_*_X64_CC_FLAGS   =   /wd4204
