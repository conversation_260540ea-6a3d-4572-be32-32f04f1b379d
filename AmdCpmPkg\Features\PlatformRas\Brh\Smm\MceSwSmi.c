/*****************************************************************************
 *
 * Copyright (C) 2018-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */


/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */
#include <Library/BaseMemoryLib.h>
#include <Library/BaseLib.h>
#include <Library/BaseFabricTopologyLib.h>
#include <Library/SmnAccessLib.h>
#include <Protocol/AmdApcbProtocol.h>
#include <Protocol/AmdMemPprProtocol.h>
#include "AmdPlatformRasBrhSmm.h"
#include "Protocol/FchSmmPeriodicalDispatch2.h"
#include <AmdCpmSmm.h>
#include <Library/CpmRasProcLib.h>
#include <Library/CpmRasMemLib.h>
#include <Protocol/AmdCpmRasOemProtocol.h>
#include <Library/MemoryAllocationLib.h>
#include <Protocol/FabricTopologyServices2.h>
#include <BRH/APOB.h>
#include <Protocol/AmdPspRuntimePprServiceProtocol.h>
#include <Library/IdsLib.h>
#include <Library/TimerLib.h>
#include <AmdSoc.h>
#include <Library/MemRestore.h>
#include <Library/SmmMemLib.h>

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */
 /**
 * @brief The SMN address offset of [ECS Status Select] (UMC::ECSStatusSel), based on UMC::BaseAddr
 *
 * @details This is the SMN address offset of [ECS Status Select] (UMC::ECSStatusSel). e.g. UMC::ECSStatusSel@UMC0 = 0x00050D98
 */
#define UMC_ECS_STATUS_SEL               (0xD98)

 /**
 * @brief The SMN address offset of [ECS Status Data] (UMC::ECSStatus), based on UMC::BaseAddr
 *
 * @details This is the SMN address offset of [ECS Status Data] (UMC::ECSStatus). e.g. UMC::ECSStatus@UMC0 = 0x00050D9C
 */
#define UMC_ECS_STATUS                   (0xD9C)

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U R E S
 *----------------------------------------------------------------------------------------
 */

//PING_PONG_SMM_BUFFER
//  PING_PONG_SMM_BUFFER is a PSP mailbox that is used to pass PSP command related data to PSP FW.
//  And PSP FW requires the address of PSP mailbox to be 32-bit aligned.
//  Therefore, the size of the PING_PONG_SMM_BUFFER needs to be aligned to 32 bits.
//  In this way, when a 32-bit aligned PSP mailbox base address is added with a buffer with a capacity that is 32-bit aligned,
//  its next PSP mailbox base address can still remain 32-bit aligned.
//
#define PING_PONG_SMM_BUFFER_SIZE    (UINT32)((sizeof(UNALIGNED_MBOX_BUFFER) + 32) & ~0x1F)    //Make the size of UNALIGNED_MBOX_BUFFER aligned to 32 bits.
typedef struct _PING_PONG_SMM_BUFFER{
  UINT8    Buffer[PING_PONG_SMM_BUFFER_SIZE];
} PING_PONG_SMM_BUFFER;

#define C2PMSG_28_OFFSET      (28 * 4)

/*----------------------------------------------------------------------------------------
 *                           G L O B A L   V A R I A B L E S
 *----------------------------------------------------------------------------------------
 */
UINT32                                     mSmiCount = 0;
UINT32                                     mSmiCoolOff = 0;
UINT64                                     mTscLast = 0;
SMI_MODE                                   mSmiMode = INTERRUPT_MODE;
EFI_HANDLE                                 mPollingModeHandle = NULL;
FCH_SMM_PERIODICAL_DISPATCH2_PROTOCOL      *mAmdPeriodicalDispatch = NULL;
extern UINT8                               *mPspMboxSmmBuffer;
extern BOOLEAN                             *mPspMboxSmmFlagAddr;
BOOLEAN                                    mRtPprEn = FALSE;
BOOLEAN                                    mPspRtPprComplFlag = FALSE;
BOOLEAN                                    mMcaWrMsrComplFlag = FALSE;
CHK_RTPPR_COMPL_STRUCT                     mChkRtPprComplStruct[MAX_NUM_OF_DPPRCL];
UINT16                                     mRtPprDdrEccErrCntStartCnt = 0;
UINT8                                      mPingPongIndex = 0;
BOOLEAN                                    mReadyForNextRtPprReq = TRUE;
UINT32                                     mP2cRuntimePprStatus = 0;
UINT32                                     mNumOfRtPptEntryInServed = 0;
DPPR_REPAIR_BLOCK_V3                       *mRtPprPspData = NULL;
DPPR_REPAIR_ENTRY_V3                       *mNewDpprQueue = NULL;
UINT8                                      mQueueFront = 0;
UINT32                                     mQueueFull = 0;
UINT32                                     *mPspBiosMboxRegAddr = NULL;
DPPR_REPAIR_BLOCK_V3                       *mEcsRtPprPspData = NULL;

/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */
VOID
LogMCAError (
  IN       RAS_MCA_ERROR_INFO_V2 *RasMcaErrorInfo
  );

EFI_STATUS
LogMemoryError (
  RAS_MCA_ERROR_INFO_V2 *RasMcaErrorInfo,
  UINT8                 BankIndex,
  UINT16                HestStrucType,
  UINTN                 ProcessorNumber
);

EFI_STATUS
LogProcessorError (
  RAS_MCA_ERROR_INFO_V2 *RasMcaErrorInfo,
  UINT8                 BankIndex,
  UINTN                 ProcessorNumber,
  UINT16                HestStrucType
);

EFI_STATUS
DimmPostPackageRepair (
  IN       RAS_MCA_ERROR_INFO_V2 *RasMcaErrorInfo,
  IN       UINT8                BankIndex
  );

VOID
ProcessPfehSmiSource (
  BOOLEAN  *SmiSourceChecked
  );

VOID
ProcessPeriodicSMI (
  VOID
  );

BOOLEAN
FindThresholdOrDeferredError (
  VOID
  );

BOOLEAN
ReCloakCheck(
  IN       UINTN    ProcessorNumber,
  OUT      UINTN    *BankNumber
  );

BOOLEAN
IsBankCloaked(
  IN       UINTN    ProcessorNumber,
  IN       UINTN    BankNumber
  );

EFI_STATUS
EFIAPI
AmdMcePeriodicSmiCallback (
  IN       EFI_HANDLE                                DispatchHandle,
  IN CONST FCH_SMM_PERIODICAL_REGISTER_CONTEXT       *RegisterContext,
  IN OUT   EFI_SMM_PERIODIC_TIMER_CONTEXT            *PeriodicTimerContext,
  IN OUT   UINTN                                     *SizeOfContext
  );

EFI_STATUS
GhesUpdate(
  IN       RAS_MCA_ERROR_INFO_V2 *RasMcaErrorInfo,
  IN       UINT8                 BankIndex,
  IN       UINTN                 ProcessorNumber,
  IN       UINT16                HestStrucType
  );

EFI_STATUS
RasSmmFwMcaClr (
  IN       UINTN    *ProcessorNumber,
  IN       UINTN    *McaBankNumber
  );

BOOLEAN
SearchProcessorIndexinMadt (
  IN       UINT32    LocalApicId,
  OUT      UINT32    *ProcessorIndex
  );

EFI_STATUS
LocalApicIdToErrStatusBlkOffset(
  IN       UINT32    LocalApicId,
  IN       UINT8     BankNumber,
  OUT      UINT32    *ErrStatusBlkOffset
  );

BOOLEAN
McaErrorHandle (
  UINTN         ProcessorNumber
  );

EFI_STATUS
RasSmmUmcEccErrCntRestore (
  UINTN         ProcessorNumber
  );

BOOLEAN
ValidateMcaRuntimePostPackageRepair (
  IN       RAS_MCA_ERROR_INFO_V2    *RasMcaErrorInfo,
  IN       UINT8                    BankIndex,
  IN       AMD_PPR_INFO             *AMD_PPR_INFO
  );

EFI_STATUS
PerformRuntimePpr (
  IN       DPPR_REPAIR_BLOCK_V3     *RtpprPspDataPtr,
  IN       UINT32                   RtpprPspDataSize
  );

EFI_STATUS
RtPprAdjustErrCounter (
  IN       BOOLEAN       AdjustCntFlag,
  IN       UINTN         Index
  );

BOOLEAN
IsRtPprRepairedSuccess (
    VOID
  );

EFI_STATUS
EFIAPI
PspRuntimePprCompletion (
  IN       VOID               *P2cRuntimePprStatus
  );

VOID
AddRtPprEntryinQueue (
  UINT32                *NumOfRtPptEntryInQueue,
  DPPR_REPAIR_ENTRY_V3  *DpprQueue,
  DPPR_REPAIR_ENTRY_V3  Dppr,
  UINT8                 *QueueFront,
  UINT32                *QueueFull
  );

VOID
RestoreRtPprVarsToDefault (
  VOID
  );

BOOLEAN
GetPspBiosMboxRegAddr (
  IN       UINT8    DfNode,
  IN OUT   VOID     **PspBiosMboxReg
  );

BOOLEAN
ParsingEcsStatusArray (
  IN     AMD_POST_PACKAGE_REPAIR_INFO_PROTOCOL  *AmdPostPackageRepairInfo,
  IN     AMD_APCB_SERVICE_PROTOCOL              *AmdApcbService,
  IN     CPU_INFO                               CpuInfo,
  IN     UINT8                                  ChannelId,
  IN     UINT8                                  DeviceType,
  IN     BOOLEAN                                *AtLeastOneRepair,
  IN OUT DPPR_REPAIR_BLOCK_V3                   *EcsRtPprPspData,
     OUT NORMALIZED_ADDRESS                     *EcsNormalizedAddress,
     OUT UINT64                                 *EcsSystemMemoryAddress,
     OUT DIMM_INFO                              *EcsDimmInfo
  );

VOID
DisableRtPpr (
  VOID
  );

STATIC AMD_PSP_RUNTIME_PPR_SERVICE_PROTOCOL    mAmdPspRuntimePprServiceProtocol = {
  PspRuntimePprCompletion
};

/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                          T A B L E    D E F I N I T I O N S
 *----------------------------------------------------------------------------------------
 */

/*********************************************************************************
 * Name: AmdMcetoSmiCallback
 *
 * Description
 *   MCE software SMI call back function entry
 *   Perform MCE error check, uncloak/cloak MCE registers
 *   call out platform error handle
 *
 * Arguments:
 *   DispatchHandle  : The handle of this callback, obtained when registering
 *   DispatchContext : Pointer to the FCH_SMM_SW_DISPATCH_CONTEXT
 *
 * Returns:
 *   None
 *
 *********************************************************************************/
EFI_STATUS
EFIAPI
AmdMcetoSmiCallback (
  IN       EFI_HANDLE                        DispatchHandle,
  IN       CONST FCH_SMM_SW_REGISTER_CONTEXT *DispatchContext,
  IN OUT   FCH_SMM_SW_CONTEXT                *SwContext,
  IN OUT   UINTN                             *SizeOfSwContext
  )
{

  EFI_STATUS Status = EFI_SUCCESS;
  LOCAL_SMI_STATUS *pLocalSmiStatusList;
  RAS_THRESHOLD_CONFIG  RasThresholdConfig;
  UINTN   ProcessorNumber;
  UINTN   BankNumber;
  UINT16  McaErrThreshCount;
  BOOLEAN SmiSourceChecked = FALSE;
  BOOLEAN RasThresholdPeriodicSmiEn;
  BOOLEAN McaErrThreshEn;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "MCE to Software SMI error handler\n");

  //Variable init
  RasThresholdPeriodicSmiEn = mPlatformApeiData->PlatRasPolicy.RasThresholdPeriodicSmiEn;
  McaErrThreshEn = mPlatformApeiData->PlatRasPolicy.McaErrThreshEn;
  McaErrThreshCount = mPlatformApeiData->PlatRasPolicy.McaErrThreshCount;

  Status = gSmst->SmmAllocatePool (
                    EfiRuntimeServicesData,
                    (sizeof (LOCAL_SMI_STATUS) * (gSmst->NumberOfCpus)),
                    (VOID **)&pLocalSmiStatusList
                    );
  ASSERT_EFI_ERROR (Status);

  ZeroMem (pLocalSmiStatusList, (sizeof (LOCAL_SMI_STATUS) * (gSmst->NumberOfCpus)));

  //Get LocalSmiStatus through all CPUs.
  mAmdRasServiceSmmProtocol->GetAllLocalSmiStatus (pLocalSmiStatusList);

  //Check LocalSmiStatus
  for (ProcessorNumber = 0; ProcessorNumber < gSmst->NumberOfCpus; ProcessorNumber++) {

    //SMI from MceRedirSts or SmiSrcMca?
    if (pLocalSmiStatusList[ProcessorNumber].Field.MceRedirSts ||
        pLocalSmiStatusList[ProcessorNumber].Field.SmiSrcMca) {

      //SMI from MCE?
      if (pLocalSmiStatusList[ProcessorNumber].Field.MceRedirSts) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS]Local SMI Status: MceRedirSts\n");
      }
      //SMI from Threshold or Deferred error?
      if (pLocalSmiStatusList[ProcessorNumber].Field.SmiSrcMca) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS]Local SMI Status: SmiSrcMca\n");
      }

      //MCA Error Handle
      McaErrorHandle(ProcessorNumber);
    } //if (pLocalSmiStatusList[ProcessorNumber].Field.MceRedirSts || (pLocalSmiStatusList[ProcessorNumber].Field.SmiSrcMca))

    // Threshold or Deferred error SMI storm handle
    if (RasThresholdPeriodicSmiEn && (pLocalSmiStatusList[ProcessorNumber].Field.SmiSrcMca)) {
      ProcessPfehSmiSource (&SmiSourceChecked);
    }

    //SMI from WRMSR
    if (pLocalSmiStatusList[ProcessorNumber].Field.WrMsr) {
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS]Local SMI Status Sourced: WrMsr\n");
      //
      //About AmdRasSmmProtocol->ClrMcaStatus service:
      //  If the 3rd parameter: IsWrMsr is True, then the 2nd parameter: McaBankNumber can be ignored,
      //  because the real MCA bank number can be derived from MCA MSR address.
      //
      if (ReCloakCheck(ProcessorNumber, &BankNumber)) {
        //Cloak MCA register
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Recloak Processor : %d Bank : 0x%x\n", ProcessorNumber, BankNumber);
        mAmdRasServiceSmmProtocol->SetMcaCloakCfg (ProcessorNumber, LShiftU64 (1, BankNumber), 0);
        if (mSmiMode == INTERRUPT_MODE) {
          //re-init Error Thresholding ErrCnt
          RasThresholdConfig.ThresholdControl = McaErrThreshEn;
          RasThresholdConfig.ThresholdCount = McaErrThreshCount;
          RasThresholdConfig.ThresholdIntType = MCA_SMI; // SMI trigger event
          mAmdRasServiceSmmProtocol->SetMcaThreshold(&ProcessorNumber, &BankNumber, &RasThresholdConfig, TRUE);
        }
        RasSmmUmcEccErrCntRestore(ProcessorNumber);
      }
      Status = mAmdRasServiceSmmProtocol->ClrMcaStatus (ProcessorNumber, 0, TRUE);
      if (Status == EFI_ABORTED) {
        //Un-cloak MCA register
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "Write MCA_STATUS aborted, uncloak processor: %d Bank : 0x%x\n", ProcessorNumber, BankNumber);
        mAmdRasServiceSmmProtocol->SetMcaCloakCfg (ProcessorNumber, 0, LShiftU64 (1, BankNumber));
      }

      //Debug start
      if (mRtPprEn) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "mPspRtPprComplFlag: 0x%0X, mMcaWrMsrComplFlag: 0x%0X\n", mPspRtPprComplFlag, mMcaWrMsrComplFlag);
        if (mNumOfRtPptEntryInServed > 0) {
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
            "DpprEntry in Served: %d, in Queue: %d. mChkRtPprComplStruct[%d].CpuInfo.ProcessorNumber: %d, mChkRtPprComplStruct[%d].BankNumber: 0x%X\n",
            mNumOfRtPptEntryInServed,
            mRtPprPspData->RepairEntries,
            (mNumOfRtPptEntryInServed - 1),
            mChkRtPprComplStruct[mNumOfRtPptEntryInServed - 1].CpuInfo.ProcessorNumber,
            (mNumOfRtPptEntryInServed - 1),
            mChkRtPprComplStruct[mNumOfRtPptEntryInServed - 1].BankNumber
            );
        }
      }
      //Debug end
      if (mRtPprEn && (mNumOfRtPptEntryInServed > 0)) {
        if ((ProcessorNumber == mChkRtPprComplStruct[mNumOfRtPptEntryInServed - 1].CpuInfo.ProcessorNumber) &&
         (BankNumber == mChkRtPprComplStruct[mNumOfRtPptEntryInServed - 1].BankNumber)) {
          mMcaWrMsrComplFlag = TRUE;
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "mMcaWrMsrComplFlag: 0x%0X\n", mMcaWrMsrComplFlag);
          if (mPspRtPprComplFlag) {
            IsRtPprRepairedSuccess ();
          }
        }
      }
    } //if (pLocalSmiStatusList[ProcessorNumber].Field.WrMsr)
  }  //for (ProcessorNumber = 0; ProcessorNumber < gSmst->NumberOfCpus; ProcessorNumber++)

  Status = gSmst->SmmFreePool (pLocalSmiStatusList);
  ASSERT_EFI_ERROR (Status);
  return EFI_SUCCESS;
}

EFI_STATUS
RasSmmRegisterMceSwSmi (
  VOID
  )
{
  EFI_STATUS                               Status;
  FCH_SMM_SW_DISPATCH2_PROTOCOL            *AmdSwDispatch;
  FCH_SMM_SW_REGISTER_CONTEXT              SwRegisterContext;
  EFI_HANDLE                               SwHandle;
  UINT16 SwSmiCmdPortAddr;
  UINT64 SmiTrigIoCycleData;
  UINT8 MceSwSmiData;

  //
  //  Locate SMM SW dispatch protocol
  //
  Status = gSmst->SmmLocateProtocol (
                  &gFchSmmSwDispatch2ProtocolGuid,
                  NULL,
                  (VOID **)&AmdSwDispatch
                  );
  ASSERT_EFI_ERROR (Status);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] Setup MCE software SMI redirection\n");

  MceSwSmiData = mPlatformApeiData->PlatRasPolicy.MceSwSmiData;
  SwSmiCmdPortAddr = mPlatformApeiData->PlatRasPolicy.SwSmiCmdPortAddr;
  SmiTrigIoCycleData = MceSwSmiData;
  SmiTrigIoCycleData = ((SmiTrigIoCycleData << 16) | BIT25 | SwSmiCmdPortAddr);

  mAmdRasServiceSmmProtocol->SetSmiTrigIoCycle (SmiTrigIoCycleData);

  SwRegisterContext.AmdSwValue  = MceSwSmiData; // use of PCD in place of MCE to Software SMI    0x80
  SwRegisterContext.Order  = 0x80;
  Status = AmdSwDispatch->Register (
                            AmdSwDispatch,
                            AmdMcetoSmiCallback,
                            &SwRegisterContext,
                            &SwHandle
                            );
  if (EFI_ERROR (Status)) {
    return Status;
  }

  return EFI_SUCCESS;
}

EFI_STATUS
LogMemoryErrorForEcsArray(
  RAS_MCA_ERROR_INFO_V2                            *RasMcaErrorInfo,
  UINT8                                            BankIndex,
  EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE      *MemErrStatusBlk
) {
  EFI_STATUS                                       Status;
  EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE      *TempMemErrStatusBlk;
  GENERIC_MEM_ERROR2_ENTRY_V3                      *GenericMemError2EntryBuffer;
  UINT8                                            i = 0;
  //UINTN                                            ResourceCheckSize;
  NORMALIZED_ADDRESS                               NormalizedAddress;
  UINT32                                           DeviceStart;
  UINT32                                           DeviceEnd;
  UINT8                                            DeviceType;
  ECS_STATUS_STRUCT                                *EcsStatusRecord;
  UINTN                                            TempCommBufferSize;
  UINT8                                            TempCommBuffer[MAX_MCA_ERROR_BLOCK_SIZE];
  OEM_MEMORY_MAP_TABLE                             *OemMemoryMapTable;
  OEM_MEMORY_MAP_TABLE                             TempOemMemoryMapTable;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] %a Entry\n", __FUNCTION__);

  // Translate MCA data into normalized address
  Status = TranslateToNormalizedAddress (
      RasMcaErrorInfo,
      BankIndex,
      mAmdRasServiceSmmProtocol->TranslateDramToNormAddr,
      &NormalizedAddress
      );
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "Translate to normalized address failed. Status=%r\n", Status);
    ASSERT_EFI_ERROR (Status);
  }

  Status = mAmdRasServiceSmmProtocol->MapSymbolToDramDevice (
    mAmdRasServiceSmmProtocol,
    RasMcaErrorInfo,
    &NormalizedAddress,
    BankIndex,
    &DeviceStart,
    &DeviceEnd,
    &DeviceType
    );
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "DimmPostPackageRepair: Map symbol to dram device failed. Status=%r\n", Status);
    ASSERT_EFI_ERROR (Status);
  }

  OemMemoryMapTable = mPlatformApeiData->OemMemoryMapTable;
  if (OemMemoryMapTable != NULL) {
    if (!SmmIsBufferOutsideSmmValid ((UINTN) OemMemoryMapTable, sizeof(OEM_MEMORY_MAP_TABLE))) {
      DEBUG ((EFI_D_ERROR, "OemMemoryMapTable is in SMRAM or overlapped with SMRAM!\n"));
      return EFI_INVALID_PARAMETER;
    }
    CopyMem ((VOID *) &TempOemMemoryMapTable, (VOID *) OemMemoryMapTable, sizeof(OEM_MEMORY_MAP_TABLE));
    if (!SmmIsBufferOutsideSmmValid ((UINTN) TempOemMemoryMapTable.MemoryMapTableEntry, sizeof(OEM_MEMORY_MAP_TABLE_ENTRY) * TempOemMemoryMapTable.TableEntryNum)) {
      DEBUG ((EFI_D_ERROR, "MemoryMapTableEntry is in SMRAM or overlapped with SMRAM!\n"));
      return EFI_INVALID_PARAMETER;
    }
    OemMemoryMapTable = &TempOemMemoryMapTable;
  }

  EcsStatusRecord = AllocateZeroPool (sizeof (*EcsStatusRecord));
  if (AmdParsingEcsStatusArray ( RasMcaErrorInfo->CpuInfo,
                                 NormalizedAddress.normalizedChannelId,
                                 DeviceType,
                                 EcsStatusRecord)) {
    TempCommBufferSize = sizeof (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE) + MemErrStatusBlk->DataLength + sizeof (GENERIC_MEM_ERROR2_ENTRY_V3) * EcsStatusRecord->EcsErrCounter;
    if (TempCommBufferSize > MAX_MCA_ERROR_BLOCK_SIZE) {
      return EFI_OUT_OF_RESOURCES;
    }

    if (!SmmIsBufferOutsideSmmValid ((UINTN) MemErrStatusBlk, TempCommBufferSize)) {
      DEBUG ((EFI_D_ERROR, "MemErrStatusBlk is in SMRAM or overlapped with SMRAM!\n"));
      return EFI_INVALID_PARAMETER;
    }

    ZeroMem ((VOID *) TempCommBuffer, MAX_MCA_ERROR_BLOCK_SIZE);
    CopyMem ((VOID *) TempCommBuffer, (VOID *) MemErrStatusBlk, TempCommBufferSize);
    TempMemErrStatusBlk = (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE *) TempCommBuffer;

    for (i = 0; i < EcsStatusRecord->EcsErrCounter; i++) {
      //ResourceCheckSize = (sizeof (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE) + MemErrStatusBlk->DataLength + sizeof (GENERIC_MEM_ERROR2_ENTRY_V3));
      //IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "For ECS :EFI_OUT_OF_RESOURCES(0x%08x) > MAX_MCA_ERROR_BLOCK_SIZE(0x%x)
      //GENERIC_ERROR_STATUS_STRUCTURE size=%x MemErrStatusBlk->DataLength size=%x sizeof (GENERIC_MEM_ERROR2_ENTRY_V3) size=%x\n",
      //  ResourceCheckSize,
      //  MAX_MCA_ERROR_BLOCK_SIZE,
      //  sizeof (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE),
      //  MemErrStatusBlk->DataLength,
      //  sizeof (GENERIC_MEM_ERROR2_ENTRY_V3)
      //);
      GenericMemError2EntryBuffer = (GENERIC_MEM_ERROR2_ENTRY_V3 *) ((UINTN ) TempMemErrStatusBlk + sizeof (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE) + TempMemErrStatusBlk->DataLength);
      Status = LogMemErrorHelperV2 (
                 RasMcaErrorInfo,
                 BankIndex,
                 mAmdRasServiceSmmProtocol->McaErrorAddrTranslate,
                 mAmdRasServiceSmmProtocol->TranslateDramToNormAddr,
                 OemMemoryMapTable,
                 mPlatformApeiData->PlatRasPolicy.AmdMcaFruTextEnable,
                 GenericMemError2EntryBuffer
                 );
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] EcsStatusArray EcsErrCounter=%x DevID=%x CID=%x Row=%x BA=%x BG=%x\n",
        EcsStatusRecord->EcsErrCounter,
        EcsStatusRecord->EcsStatusData[i].DevID,
        EcsStatusRecord->EcsStatusData[i].CID,
        EcsStatusRecord->EcsStatusData[i].Row,
        EcsStatusRecord->EcsStatusData[i].BA,
        EcsStatusRecord->EcsStatusData[i].BG
        );
      GenericMemError2EntryBuffer->MemErrorSection2.Bank = (UINT16)((EcsStatusRecord->EcsStatusData[i].BG << 8) | EcsStatusRecord->EcsStatusData[i].BA);
      GenericMemError2EntryBuffer->MemErrorSection2.Device = EcsStatusRecord->EcsStatusData[i].DevID;
      GenericMemError2EntryBuffer->MemErrorSection2.Row = EcsStatusRecord->EcsStatusData[i].Row;
      GenericMemError2EntryBuffer->MemErrorSection2.ChipId = EcsStatusRecord->EcsStatusData[i].CID;

      if (EFI_ERROR(Status)) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] LogMemoryErrorForEcsArray failed: 0x%x\n", Status);
        return Status;
      }

      switch (GenericMemError2EntryBuffer->GenErrorDataEntry.ErrorSeverity) {
        case ERROR_SEVERITY_FATAL:
          if (TempMemErrStatusBlk->BlockStatus.UncorrectableErrorValid) {
            TempMemErrStatusBlk->BlockStatus.MultipleUncorrectableErrors = 1;
          } else {
            TempMemErrStatusBlk->BlockStatus.UncorrectableErrorValid = 1;
          }
          break;
        case ERROR_SEVERITY_CORRECTED:
        case ERROR_RECOVERABLE:
          if (TempMemErrStatusBlk->BlockStatus.CorrectableErrorValid) {
            TempMemErrStatusBlk->BlockStatus.MultipleCorrectableErrors = 1;
          } else {
            TempMemErrStatusBlk->BlockStatus.CorrectableErrorValid = 1;
          }
          break;
      }
      UpdateGenErrStsBlkSeverity(TempMemErrStatusBlk, GenericMemError2EntryBuffer->GenErrorDataEntry.ErrorSeverity);
      TempMemErrStatusBlk->BlockStatus.ErrorDataEntryCount++;
      TempMemErrStatusBlk->DataLength += sizeof (GENERIC_MEM_ERROR2_ENTRY_V3);
      //IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] Error Block data Count(ErrorDataEntryCount): 0x%x, Length: 0x%x\n",
      //  MemErrStatusBlk->BlockStatus.ErrorDataEntryCount,
      //  MemErrStatusBlk->DataLength);
    }
    CopyMem ((VOID *) MemErrStatusBlk, (VOID *) TempCommBuffer, TempCommBufferSize);
  }
  return EFI_SUCCESS;
}
EFI_STATUS
LogMemoryError (
  RAS_MCA_ERROR_INFO_V2 *RasMcaErrorInfo,
  UINT8                 BankIndex,
  UINT16                HestStrucType,
  UINTN                 ProcessorNumber
  )
{
  EFI_STATUS                                       Status;
  EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE      *MemErrStatusBlk;
  EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE      *TempMemErrStatusBlk;
  UINT32                                           LocalApicId;
  UINT32                                           ErrStatusBlkOffset;
  GENERIC_MEM_ERR_ENTRY_V3                         *GenericMemErrEntryBuffer;
  UINT8                                            BankNumber;
  UINTN                                            HandleBufferSize = 0;
  UINTN                                            Index = 0;
  UINT8                                            FreeFlag = 0;
  EFI_HANDLE                                       *HandleBuffer = NULL;
  AMD_CPM_RAS_OEM_PROTOCOL                         *AmdRasOemProtocol = NULL;
  UINT8                                            UmcExtErrorCode;
  UINTN                                            TempCommBufferSize;
  UINT8                                            TempCommBuffer[MAX_MCA_ERROR_BLOCK_SIZE];
  OEM_MEMORY_MAP_TABLE                             *OemMemoryMapTable;
  OEM_MEMORY_MAP_TABLE                             TempOemMemoryMapTable;

  switch (HestStrucType) {
  case EFI_ACPI_6_3_IA32_ARCHITECTURE_MACHINE_CHECK_EXCEPTION:
    MemErrStatusBlk = mPlatformApeiData->AmdMemMceErrBlk;
    break;
  case EFI_ACPI_6_3_IA32_ARCHITECTURE_CORRECTED_MACHINE_CHECK:
    MemErrStatusBlk = mPlatformApeiData->AmdMemCmcErrBlk;
    break;
  case EFI_ACPI_6_3_IA32_ARCHITECTURE_DEFERRED_MACHINE_CHECK:
      if (mPlatformApeiData->PlatRasPolicy.HestDMCStrucEn) {
        MemErrStatusBlk = mPlatformApeiData->AmdMemDmcErrBlk;
      } else {
          //Log as correctable error when Deferred Machine Check structure disabled.
          MemErrStatusBlk = mPlatformApeiData->AmdMemCmcErrBlk;
      }
    break;
  default:
    return EFI_UNSUPPORTED;
  }

  //Get LocalApicId
  LocalApicId = RasCmnGetApicId(NULL, ProcessorNumber);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] Memory Error in LocalApicId: 0x%x\n", LocalApicId);

  ErrStatusBlkOffset = 0;
  BankNumber = (UINT8)RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaBankNumber;
  Status = LocalApicIdToErrStatusBlkOffset (LocalApicId, BankNumber, &ErrStatusBlkOffset);
  if (EFI_ERROR (Status)) {
    return Status;
  }
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] Error Data Block Base Address: 0x%08x, Error Data Block Offset: 0x%08x\n", MemErrStatusBlk, ErrStatusBlkOffset);

  MemErrStatusBlk = (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE*)((UINT8 *)MemErrStatusBlk + ErrStatusBlkOffset);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] Error Data Block Target Address: 0x%08x\n", MemErrStatusBlk);
  TempCommBufferSize = sizeof (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE) + MemErrStatusBlk->DataLength + sizeof (GENERIC_MEM_ERR_ENTRY_V3);
  if (TempCommBufferSize > MAX_MCA_ERROR_BLOCK_SIZE) {
    return EFI_OUT_OF_RESOURCES;
  }

  if (!SmmIsBufferOutsideSmmValid ((UINTN) MemErrStatusBlk, TempCommBufferSize)) {
    DEBUG ((EFI_D_ERROR, "MemErrStatusBlk is in SMRAM or overlapped with SMRAM!\n"));
    return EFI_INVALID_PARAMETER;
  }

  OemMemoryMapTable = mPlatformApeiData->OemMemoryMapTable;
  if (OemMemoryMapTable != NULL) {
    if (!SmmIsBufferOutsideSmmValid ((UINTN) OemMemoryMapTable, sizeof(OEM_MEMORY_MAP_TABLE))) {
      DEBUG ((EFI_D_ERROR, "OemMemoryMapTable is in SMRAM or overlapped with SMRAM!\n"));
      return EFI_INVALID_PARAMETER;
    }
    CopyMem ((VOID *) &TempOemMemoryMapTable, (VOID *) OemMemoryMapTable, sizeof(OEM_MEMORY_MAP_TABLE));
    if (!SmmIsBufferOutsideSmmValid ((UINTN) TempOemMemoryMapTable.MemoryMapTableEntry, sizeof(OEM_MEMORY_MAP_TABLE_ENTRY) * TempOemMemoryMapTable.TableEntryNum)) {
      DEBUG ((EFI_D_ERROR, "MemoryMapTableEntry is in SMRAM or overlapped with SMRAM!\n"));
      return EFI_INVALID_PARAMETER;
    }
    OemMemoryMapTable = &TempOemMemoryMapTable;
  }

  ZeroMem ((VOID *) TempCommBuffer, MAX_MCA_ERROR_BLOCK_SIZE);
  CopyMem ((VOID *) TempCommBuffer, (VOID *) MemErrStatusBlk, TempCommBufferSize);
  TempMemErrStatusBlk = (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE *) TempCommBuffer;

  RasResetErrBlk(TempMemErrStatusBlk);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] RasResetErrBlk(MemErrStatusBlk) Address: 0x%08x\n", MemErrStatusBlk);


  GenericMemErrEntryBuffer = (GENERIC_MEM_ERR_ENTRY_V3 *) ((UINTN ) TempMemErrStatusBlk + sizeof (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE) + TempMemErrStatusBlk->DataLength);

  Status = LogMemErrorHelper (
             RasMcaErrorInfo,
             BankIndex,
             mAmdRasServiceSmmProtocol->McaErrorAddrTranslate,
             mAmdRasServiceSmmProtocol->TranslateDramToNormAddr,
             OemMemoryMapTable,
             mPlatformApeiData->PlatRasPolicy.AmdMcaFruTextEnable,
             GenericMemErrEntryBuffer
             );

  if (EFI_ERROR(Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] LogMemoryError failed: 0x%x\n", Status);
    return Status;
  }

  switch (GenericMemErrEntryBuffer->GenErrorDataEntry.ErrorSeverity) {
    case ERROR_SEVERITY_FATAL:
      if (TempMemErrStatusBlk->BlockStatus.UncorrectableErrorValid) {
        TempMemErrStatusBlk->BlockStatus.MultipleUncorrectableErrors = 1;
      } else {
        TempMemErrStatusBlk->BlockStatus.UncorrectableErrorValid = 1;
      }
      break;
    case ERROR_SEVERITY_CORRECTED:
    case ERROR_RECOVERABLE:
      if (TempMemErrStatusBlk->BlockStatus.CorrectableErrorValid) {
        TempMemErrStatusBlk->BlockStatus.MultipleCorrectableErrors = 1;
      } else {
        TempMemErrStatusBlk->BlockStatus.CorrectableErrorValid = 1;
      }
      break;
  }

  UpdateGenErrStsBlkSeverity(TempMemErrStatusBlk, GenericMemErrEntryBuffer->GenErrorDataEntry.ErrorSeverity);

  TempMemErrStatusBlk->BlockStatus.ErrorDataEntryCount++;
  TempMemErrStatusBlk->DataLength += sizeof (GENERIC_MEM_ERR_ENTRY_V3);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS - MEM] Error Block data Count: 0x%x, Length: 0x%x\n", TempMemErrStatusBlk->BlockStatus.ErrorDataEntryCount, TempMemErrStatusBlk->DataLength);
  CopyMem ((VOID *) MemErrStatusBlk, (VOID *) TempCommBuffer, TempCommBufferSize);

  //Log Memory Error Section 2 Data for ECS Status Array
  UmcExtErrorCode = (UINT8)RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.ErrorCodeExt;
  if (UMC_ECSROWERR == UmcExtErrorCode){
    LogMemoryErrorForEcsArray(RasMcaErrorInfo, BankIndex, MemErrStatusBlk);
  }

  //Locate Ras Oem Protocol
  Status = gSmst->SmmLocateHandle (
                              ByProtocol,
                              &gAmdCpmRasOemSmmProtocolGuid,
                              NULL,
                              &HandleBufferSize,
                              HandleBuffer);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "LogMemoryError SmmLocateHandle gAmdCpmRasOemSmmProtocolGuid Status =%r\n", Status);

  if (EFI_ERROR(Status)) {
      if (Status == EFI_BUFFER_TOO_SMALL) {
          HandleBuffer = AllocateRuntimePool (HandleBufferSize);
          if (HandleBuffer != NULL) {
            Status = gSmst->SmmLocateHandle (
                        ByProtocol,
                        &gAmdCpmRasOemSmmProtocolGuid,
                        NULL,
                        &HandleBufferSize,
                        HandleBuffer);
            if (!EFI_ERROR(Status))
              FreeFlag = 1;
          }
      }
  }

  if (!EFI_ERROR(Status)) {
    if (HandleBuffer != NULL) {
      for (Index = 0; Index < (HandleBufferSize / sizeof(EFI_HANDLE)); Index++) {
          Status = gSmst->SmmHandleProtocol (
                                    HandleBuffer[Index],
                                    &gAmdCpmRasOemSmmProtocolGuid,
                                    (VOID **)&AmdRasOemProtocol);
          if(!EFI_ERROR(Status)){
            AmdRasOemProtocol->OemErrorLogEventMem (RasMcaErrorInfo, BankIndex, ProcessorNumber, GenericMemErrEntryBuffer);
          }
      }
    }
  }

  if ((FreeFlag == 1) && (HandleBuffer != NULL)){
    Status = gSmst->SmmFreePool (HandleBuffer);
    ASSERT_EFI_ERROR (Status);
  }
  return EFI_SUCCESS;
}

EFI_STATUS
MemErrorDecode (
  RAS_MCA_ERROR_INFO_V2 *RasMcaErrorInfo,
  UINT8                 BankIndex,
  UINTN                 ProcessorNumber,
  UINT16                HestStrucType
)
{
  EFI_STATUS   Status = EFI_SUCCESS;
  UINT8        UmcExtErrorCode;

  UmcExtErrorCode = (UINT8)RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.ErrorCodeExt;
  //Add error record to table.
  if ((UMC_DRAMECCERR == UmcExtErrorCode) ||
      (UMC_ADDRCMDPARITYERR == UmcExtErrorCode) ||
      (UMC_ECSROWERR == UmcExtErrorCode)) {
    LogMemoryError (RasMcaErrorInfo, BankIndex, HestStrucType, ProcessorNumber);
  } else {
    //Log all other errors from UMC as processor error.
    LogProcessorError (RasMcaErrorInfo, BankIndex, ProcessorNumber, HestStrucType);
  }
  return Status;
}

EFI_STATUS
LogProcessorError (
  RAS_MCA_ERROR_INFO_V2 *RasMcaErrorInfo,
  UINT8                 BankIndex,
  UINTN                 ProcessorNumber,
  UINT16                HestStrucType
)
{
  EFI_STATUS                                       Status;
  UINT32                                           LocalApicId;
  GENERIC_PROC_ERR_ENTRY_V3                        *GenericProcErrEntryBuffer;
  EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE      *ProcErrStatusBlk;
  EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE      *TempProcErrStatusBlk;
  UINT32                                           ErrStatusBlkOffset;
  UINT8                                            BankNumber;
  UINTN                                            HandleBufferSize = 0;
  UINTN                                            Index = 0;
  UINT8                                            FreeFlag = 0;
  EFI_HANDLE                                       *HandleBuffer = NULL;
  AMD_CPM_RAS_OEM_PROTOCOL                         *AmdRasOemProtocol = NULL;
  UINTN                                            TempCommBufferSize;
  UINT8                                            TempCommBuffer[MAX_MCA_ERROR_BLOCK_SIZE];

  switch (HestStrucType) {
  case EFI_ACPI_6_3_IA32_ARCHITECTURE_MACHINE_CHECK_EXCEPTION:
    ProcErrStatusBlk = mPlatformApeiData->AmdProcMceErrBlk;
    break;
  case EFI_ACPI_6_3_IA32_ARCHITECTURE_CORRECTED_MACHINE_CHECK:
    ProcErrStatusBlk = mPlatformApeiData->AmdProcCmcErrBlk;
    break;
  case EFI_ACPI_6_3_IA32_ARCHITECTURE_DEFERRED_MACHINE_CHECK:
      if (mPlatformApeiData->PlatRasPolicy.HestDMCStrucEn) {
        ProcErrStatusBlk = mPlatformApeiData->AmdProcDmcErrBlk;
      } else {
        //Log as correctable error when Deferred Machine Check structure disabled.
        ProcErrStatusBlk = mPlatformApeiData->AmdProcCmcErrBlk;
      }
    break;
  default:
    return EFI_UNSUPPORTED;
  }

  //Get LocalApicId
  LocalApicId = RasCmnGetApicId(NULL, ProcessorNumber);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] Processor error in LocalApicId: 0x%x\n", LocalApicId);

  ErrStatusBlkOffset = 0;
  BankNumber = (UINT8)RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaBankNumber;
  Status = LocalApicIdToErrStatusBlkOffset (LocalApicId, BankNumber, &ErrStatusBlkOffset);
  if (EFI_ERROR (Status)) {
    return Status;
  }
  ProcErrStatusBlk = (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE*)((UINT8 *)ProcErrStatusBlk + ErrStatusBlkOffset);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] Error Data Block Address: 0x%08x\n", ProcErrStatusBlk);

  TempCommBufferSize = sizeof (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE) + ProcErrStatusBlk->DataLength + PRE_GENERIC_PROC_ERR_ENTRY_SIZE;
  if (TempCommBufferSize > MAX_MCA_ERROR_BLOCK_SIZE) {
    return EFI_OUT_OF_RESOURCES;
  }

  if (!SmmIsBufferOutsideSmmValid ((UINTN) ProcErrStatusBlk, TempCommBufferSize)) {
    DEBUG ((EFI_D_ERROR, "ProcErrStatusBlk is in SMRAM or overlapped with SMRAM!\n"));
    return EFI_INVALID_PARAMETER;
  }

  ZeroMem ((VOID *) TempCommBuffer, MAX_MCA_ERROR_BLOCK_SIZE);
  CopyMem ((VOID *) TempCommBuffer, (VOID *) ProcErrStatusBlk, TempCommBufferSize);
  TempProcErrStatusBlk = (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE *) TempCommBuffer;

  RasResetErrBlk(TempProcErrStatusBlk);



  GenericProcErrEntryBuffer = (GENERIC_PROC_ERR_ENTRY_V3 *) ((UINTN ) TempProcErrStatusBlk + sizeof (EFI_ACPI_6_3_GENERIC_ERROR_STATUS_STRUCTURE) + TempProcErrStatusBlk->DataLength);

  Status = LogProcessorErrorHelper (NULL, RasMcaErrorInfo, BankIndex, ProcessorNumber, FALSE, LocalApicId, (VOID *)GenericProcErrEntryBuffer);

  if (EFI_ERROR(Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] LogProcessorErrorHelper failed: 0x%x\n", Status);
    return Status;
  }

  UpdateGenErrStsBlkSeverity(TempProcErrStatusBlk, GenericProcErrEntryBuffer->GenErrorDataEntry.ErrorSeverity);

  TempProcErrStatusBlk->BlockStatus.ErrorDataEntryCount++;
  TempProcErrStatusBlk->DataLength += (GenericProcErrEntryBuffer->GenErrorDataEntry.ErrorDataLength + sizeof (EFI_ACPI_6_3_GENERIC_ERROR_DATA_ENTRY_STRUCTURE));

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS - PROC] Error Block data Count: 0x%x, Length: 0x%x\n", TempProcErrStatusBlk->BlockStatus.ErrorDataEntryCount, TempProcErrStatusBlk->DataLength);

  //Locate Ras Oem Protocol
  Status = gSmst->SmmLocateHandle (
                              ByProtocol,
                              &gAmdCpmRasOemSmmProtocolGuid,
                              NULL,
                              &HandleBufferSize,
                              HandleBuffer);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "LogProcessorError SmmLocateHandle gAmdCpmRasOemSmmProtocolGuid Status =%r\n", Status);

  if (EFI_ERROR(Status)) {
      if (Status == EFI_BUFFER_TOO_SMALL) {
          HandleBuffer = AllocateRuntimePool (HandleBufferSize);
          if (HandleBuffer != NULL) {
            Status = gSmst->SmmLocateHandle (
                        ByProtocol,
                        &gAmdCpmRasOemSmmProtocolGuid,
                        NULL,
                        &HandleBufferSize,
                        HandleBuffer);
            if (!EFI_ERROR(Status))
              FreeFlag = 1;
          }
      }
  }

  if (!EFI_ERROR(Status)) {
    if (HandleBuffer != NULL) {
      for (Index = 0; Index < (HandleBufferSize / sizeof(EFI_HANDLE)); Index++) {
          Status = gSmst->SmmHandleProtocol (
                                    HandleBuffer[Index],
                                    &gAmdCpmRasOemSmmProtocolGuid,
                                    (VOID **)&AmdRasOemProtocol);
          if(!EFI_ERROR(Status)){
            AmdRasOemProtocol->OemErrorLogEventProcessor (RasMcaErrorInfo, BankIndex, ProcessorNumber, GenericProcErrEntryBuffer);
          }
      }
    }
  }

  if ((FreeFlag == 1) && (HandleBuffer != NULL)){
    Status = gSmst->SmmFreePool (HandleBuffer);
    ASSERT_EFI_ERROR (Status);
  }

  CopyMem ((VOID *) ProcErrStatusBlk, (VOID *) TempCommBuffer, TempCommBufferSize);

  return EFI_SUCCESS;
}


EFI_STATUS
GhesUpdate(
  IN       RAS_MCA_ERROR_INFO_V2 *RasMcaErrorInfo,
  IN       UINT8                 BankIndex,
  IN       UINTN                 ProcessorNumber,
  IN       UINT16                HestStrucType
  )
{
  EFI_STATUS            Status = EFI_SUCCESS;

  if (!mPlatformApeiData->PlatRasPolicy.CpmGhesAssistEnable) {
    return EFI_UNSUPPORTED;
  }

  if (mPlatformApeiData->RasAcpiSmmData->Sig != RAS_ACPI_SMM_DATA_BUFFER_SIGNATURE) {
    return EFI_UNSUPPORTED;
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] GHES Assist Update Entry\n");

  //Regardless of whether the OS supports GHES_ASSIST, when CpmGhesAssistEnable is enabled from CPM RAS,
  //CPM RAS will perform all GHES_ASSIST FW flow.
  if (mPlatformApeiData->RasAcpiSmmData->AcpiPlatformCap.Field.GhesAssist == 0) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] The OS does not support GHES_ASSIST\n");
  }

  if (RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_NBIO_ID) {
    if (RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.ErrorCodeExt == NBIO_PCIE_SIDEBAND) {
      //return for a PCI-E error
      return EFI_INVALID_PARAMETER;
    }
  }
  if (RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_UMC_ID) {
    //Error from UMC bank
    MemErrorDecode(RasMcaErrorInfo, BankIndex, ProcessorNumber, HestStrucType);
  } else {
    //Error from other banks
    LogProcessorError (RasMcaErrorInfo, BankIndex, ProcessorNumber, HestStrucType);
  }

  return Status;
}

VOID
LogMCAError (
  IN       RAS_MCA_ERROR_INFO_V2   *RasMcaErrorInfo
  )
{
  EFI_STATUS          Status;
  NORMALIZED_ADDRESS NormalizedAddress;
  UINT64             SystemMemoryAddress;
  DIMM_INFO          DimmInfo;
  UINT8              ErrorBankCount;
  UINT8              BankIndex;
  BOOLEAN            RasThresholdPeriodicSmiEn;
  UINTN              HandleBufferSize = 0;
  UINTN              Index = 0;
  UINT8              FreeFlag = 0;
  EFI_HANDLE         *HandleBuffer;
  AMD_CPM_RAS_OEM_PROTOCOL *AmdRasOemProtocol = NULL;

  RasThresholdPeriodicSmiEn = mPlatformApeiData->PlatRasPolicy.RasThresholdPeriodicSmiEn;
  ErrorBankCount = (UINT8)RasMcaErrorInfo->McaBankCount;

  if (!RasThresholdPeriodicSmiEn) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "Log MCA error entry\n");
  }

  for (BankIndex = 0; BankIndex < ErrorBankCount; BankIndex++) {
    if ((RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val) ||
        (RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaDeStatMsr.Field.Val)) {
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Socket# %d, Ccd# %d, Ccx# %d, Core# %d, Thread# %d\n",
              RasMcaErrorInfo->CpuInfo.SocketId,
              RasMcaErrorInfo->CpuInfo.DieId,
              RasMcaErrorInfo->CpuInfo.CcxId,
              RasMcaErrorInfo->CpuInfo.CoreId,
              RasMcaErrorInfo->CpuInfo.ThreadID);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "MCA Bank Number : %d\n", RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaBankNumber);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "MCA_STATUS : 0x%016lX\n", RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Value);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "MCA_ADDR : 0x%016lX\n", RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaAddrMsr.Value);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "MCA_SYND : 0x%016lX\n", RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaSyndMsr.Value);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "MCA_MISC0 : 0x%016lX\n", RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaMisc0Msr.Value);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "MCA_MISC1 : 0x%016lX\n", RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaMisc1Msr.Value);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "MCA_IPID : 0x%016lX\n", RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaIpidMsr.Value);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "MCA_SYND1 : 0x%016lX\n", RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaSynd1Msr.Value);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "MCA_SYND2 : 0x%016lX\n", RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaSynd2Msr.Value);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "MCA_CONFIG : 0x%016lx\n", RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaConfigMsr.Value);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "MCA_DESTAT : 0x%016lx\n", RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaDeStatMsr.Value);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "MCA_DEADDR : 0x%016lx\n", RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaDeAddrMsr.Value);

      if (RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_UMC_ID) {
        if (RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UECC &&
            RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Deferred) {
          AmdMemRestoreDiscardCurrentMemContext ();
        }
      }

      if (RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val) {
        Status = TranslateToNormalizedAddressExt (RasMcaErrorInfo, BankIndex, mAmdRasServiceSmmProtocol->TranslateDramToNormAddr, &NormalizedAddress, FALSE);
        if (!EFI_ERROR (Status)) {
          //Addition information for UMC ECC error
          Status = mAmdRasServiceSmmProtocol->McaErrorAddrTranslate (&NormalizedAddress, &SystemMemoryAddress, &DimmInfo);
          if (!EFI_ERROR (Status)) {
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "The following information is derived from MCA_STATUS_UMC and MCA_ADDR_UMC ...\n");
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ERROR ADDRESS : 0x%016lX\n", NormalizedAddress.normalizedAddr);
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ERROR ADDRESS LSB : 0x%x\n", RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.AddrLsb);
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "System Address : 0x%016lX\n", SystemMemoryAddress);
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "DIMM Info (Chip Select): 0x%x\n", DimmInfo.ChipSelect);
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "DIMM Info (Bank): 0x%x\n", DimmInfo.Bank);
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "DIMM Info (Row): 0x%x\n", DimmInfo.Row);
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "DIMM Info (Column): 0x%x\n", DimmInfo.Column);
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "DIMM Info (rankmul): 0x%x\n", DimmInfo.rankmul);
          }
        }
      }

      if ((RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaConfigMsr.Field.LogDeferredInMcaStat == 0) &&
          (RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaDeStatMsr.Field.Val)) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "\n\n");
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Independent MCA Deferred Error Found.\n");
        Status = TranslateToNormalizedAddressExt (RasMcaErrorInfo, BankIndex, mAmdRasServiceSmmProtocol->TranslateDramToNormAddr, &NormalizedAddress, TRUE);
        if (!EFI_ERROR (Status)) {
          //Addition information for UMC ECC error
          Status = mAmdRasServiceSmmProtocol->McaErrorAddrTranslate (&NormalizedAddress, &SystemMemoryAddress, &DimmInfo);
          if (!EFI_ERROR (Status)) {
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "The following information is derived from MCA_DESTAT_UMC and MCA_DEADDR_UMC ...\n");
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ERROR ADDRESS : 0x%016lX\n", NormalizedAddress.normalizedAddr);
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ERROR ADDRESS LSB : 0x%x\n", RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaDeStatMsr.Field.AddrLsb);
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "System Address : 0x%016lX\n", SystemMemoryAddress);
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "DIMM Info (Chip Select): 0x%x\n", DimmInfo.ChipSelect);
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "DIMM Info (Bank): 0x%x\n", DimmInfo.Bank);
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "DIMM Info (Row): 0x%x\n", DimmInfo.Row);
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "DIMM Info (Column): 0x%x\n", DimmInfo.Column);
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "DIMM Info (rankmul): 0x%x\n", DimmInfo.rankmul);
          }
        }
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "\n\n");
      }

      //Locate Ras Oem Protocol
      FreeFlag = 0;
      HandleBufferSize = 0;
      HandleBuffer = NULL;
      Status = gSmst->SmmLocateHandle (
                                  ByProtocol,
                                  &gAmdCpmRasOemSmmProtocolGuid,
                                  NULL,
                                  &HandleBufferSize,
                                  HandleBuffer);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "LogMCAError SmmLocateHandle gAmdCpmRasOemSmmProtocolGuid Status =%r\n", Status);

      if (EFI_ERROR(Status)) {
        if (Status == EFI_BUFFER_TOO_SMALL) {
            HandleBuffer = AllocateRuntimePool (HandleBufferSize);
            if (HandleBuffer != NULL) {
              Status = gSmst->SmmLocateHandle (
                          ByProtocol,
                          &gAmdCpmRasOemSmmProtocolGuid,
                          NULL,
                          &HandleBufferSize,
                          HandleBuffer);
              if (!EFI_ERROR(Status))
                FreeFlag = 1;
            }
        }
      }

      if (!EFI_ERROR(Status)) {
        if (HandleBuffer != NULL) {
          for (Index = 0; Index < (HandleBufferSize / sizeof(EFI_HANDLE)); Index++) {
            Status = gSmst->SmmHandleProtocol (
                                      HandleBuffer[Index],
                                      &gAmdCpmRasOemSmmProtocolGuid,
                                      (VOID **)&AmdRasOemProtocol);
            if(!EFI_ERROR(Status)){
              AmdRasOemProtocol->OemErrorLogEventMca (RasMcaErrorInfo, &DimmInfo, &NormalizedAddress, BankIndex);
            }
          }
        }
      }
      if ((FreeFlag == 1) && (HandleBuffer != NULL)){
        Status = gSmst->SmmFreePool (HandleBuffer);
        ASSERT_EFI_ERROR (Status);
      }
    }
  }
}

BOOLEAN
ReCloakCheck(
  IN       UINTN    ProcessorNumber,
  OUT      UINTN    *BankNumber
)
{
  BOOLEAN ReCloakFlag;
  BOOLEAN IsLegacyMcaAddr;
  UINT64 SmmSaveStateBase;
  SMM_SAVE_STATE *SmmSaveState;
  UINT32 ECX_Data;
  PLAT_RAS_MSR_ACCESS  RasMsrAccess;
  MCA_DESTAT_MSR McaDestatMsr;
  MCA_STATUS_MSR McaStatusMsr;
  UINT32 McaExtensionAddrBase;

  ReCloakFlag = FALSE;
  IsLegacyMcaAddr = FALSE;

  //Check RCX value if the address is in legacy MCA address range
  //if not then the address will be SMCA address.
  mAmdRasServiceSmmProtocol->GetSmmSaveStateBase (ProcessorNumber, &SmmSaveStateBase);
  SmmSaveState = (SMM_SAVE_STATE*)SmmSaveStateBase;
  ECX_Data = (UINT32) (SmmSaveState->RCX & 0xFFFFFFFF);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ECX Data : 0x%08x\n", ECX_Data);
  if ((MCA_LEGACY_BASE <= ECX_Data) && (ECX_Data < MCA_LEGACY_TOP_ADDR)) {
    //Legacy MCA address
    *BankNumber = (UINTN) ((ECX_Data - MCA_LEGACY_BASE) >> 2);
    IsLegacyMcaAddr = TRUE;
  } else if ((ECX_Data == LMCA_STATUS_REG) || (ECX_Data == LMCA_ADDR_REG)){
    *BankNumber = (UINTN)(ECX_Data >> 2);
    IsLegacyMcaAddr = TRUE;
  } else {
    //Extension MCA Address
    *BankNumber = (UINTN) ((ECX_Data & 0x00000FF0) >> 4);
  }

  McaExtensionAddrBase = (UINT32)(MCA_EXTENSION_BASE + (*BankNumber << 4));

  if (IsLegacyMcaAddr) {
    // If the OS is writing MCA_STATUS through the old address space, assume it is an SMCA-unaware OS that will never clear DESTAT
    if ((ECX_Data & MCA_REG_OFFSET_MASK) == MCA_STATUS_OFFSET) {
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "MCA_STATUS Write @ Processor: %d, Bank: 0x%x\n", ProcessorNumber, *BankNumber);
      //No-condition Clear MCA_DESTAT
      RasMsrAccess.RegisterAddress = McaExtensionAddrBase | MCA_DESTAT_OFFSET;
      RasMsrAccess.IsWrite = TRUE;
      RasMsrAccess.RegisterValue = 0;
      MpRegisterAccess (ProcessorNumber,&RasMsrAccess);

      ReCloakFlag = TRUE;
    }
  } else {
    RasMsrAccess.RegisterAddress = McaExtensionAddrBase | MCA_STATUS_OFFSET;
    RasMsrAccess.IsWrite = FALSE;
    MpRegisterAccess (ProcessorNumber,&RasMsrAccess);
    McaStatusMsr.Value = RasMsrAccess.RegisterValue;

    RasMsrAccess.RegisterAddress = McaExtensionAddrBase | MCA_DESTAT_OFFSET;
    RasMsrAccess.IsWrite = FALSE;
    MpRegisterAccess (ProcessorNumber,&RasMsrAccess);
    McaDestatMsr.Value = RasMsrAccess.RegisterValue;

    // If the OS is writing DESTAT
    if ((ECX_Data & SMCA_REG_OFFSET_MASK) == MCA_DESTAT_OFFSET) {
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "SMCA_DESTAT Write @ Processor: %d, Bank: 0x%x\n", ProcessorNumber, *BankNumber);
    // If the MCA_STATUS does not contain an error
      if (McaStatusMsr.Field.Val == 0) {
        ReCloakFlag = TRUE;
      }
    }
    // If the OS is writing MCA_STATUS through the new address space, it is an SMCA-aware OS that will also clear DESTAT
    if ((ECX_Data & SMCA_REG_OFFSET_MASK) == MCA_STATUS_OFFSET) {
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "SMCA_STATUS Write @ Processor: %d, Bank: 0x%x\n", ProcessorNumber, *BankNumber);
     // If MCA_DESTAT does not contain an error, or it contains the same error as MCA_STATUS (STATUS[Deferred]==1)
      if ((McaDestatMsr.Field.Val== 0) || (McaStatusMsr.Field.Deferred && McaStatusMsr.Field.Val)) {
        //Clear MCA_DESTAT
        RasMsrAccess.RegisterAddress = McaExtensionAddrBase | MCA_DESTAT_OFFSET;
        RasMsrAccess.IsWrite = TRUE;
        RasMsrAccess.RegisterValue = 0;
        MpRegisterAccess (ProcessorNumber,&RasMsrAccess);
        ReCloakFlag = TRUE;
      }
    }
  }
  return ReCloakFlag;
}

BOOLEAN
IsBankCloaked(
  IN       UINTN    ProcessorNumber,
  IN       UINTN    BankNumber
)
{
  PLAT_RAS_MSR_ACCESS  RasMsrAccess;

  RasMsrAccess.RegisterAddress = MSR_PFEH_CLOAK_CFG;
  RasMsrAccess.IsWrite = FALSE;
  RasMsrAccess.RegisterValue = 0;
  MpRegisterAccess (ProcessorNumber,&RasMsrAccess);

  if ((RasMsrAccess.RegisterValue & LShiftU64 (1, BankNumber)) != 0) {
    return TRUE;
  }

  return FALSE;
}

/**
 * DimmPostPackageRepair
 *
 * Determines if a DDR Post Package Repair is needed and log it in the APCB.
 * Already know the MCA would be something like a threshold exceeded and would
 * want to log it.
 *
 * IN   RasMcaErrorInfo     Contains information about the MCi
 *
 */
EFI_STATUS
DimmPostPackageRepair (
  IN       RAS_MCA_ERROR_INFO_V2 *RasMcaErrorInfo,
  IN       UINT8                BankIndex
  )
{
  AMD_APCB_SERVICE_PROTOCOL              *AmdApcbService;
  DPPR_REPAIR_ENTRY_V3                   DpprEntry;
  EFI_STATUS                             Status;
  NORMALIZED_ADDRESS                     NormalizedAddress;
  UINT64                                 SystemMemoryAddress;
  DIMM_INFO                              DimmInfo;
  UINT32                                 DeviceStart;
  UINT32                                 DeviceEnd;
  UINT8                                  DeviceType;
  UINT32                                 RepairDevice;
  BOOLEAN                                AtLeastOneRepair;
  UMC_SYND_ECC_ERR_INFO                  UmcSyndEccErrInfo;
  AMD_DIMM_INFO                          AmdDimmInfo;
  AMD_PPR_INFO                           AmdPprInfo;
  UINT8                                  SerialNumberString[4];
  AMD_POST_PACKAGE_REPAIR_INFO_PROTOCOL  *AmdPostPackageRepairInfo;
  static CHK_RTPPR_COMPL_STRUCT          TmpChkRtPprComplStruct[MAX_NUM_OF_DPPRCL] = {0};
  BOOLEAN                                IsValidRtPprReq = FALSE;
  UINT8                                  RetryCnt;
  DPPR_REPAIR_BLOCK_V3                   *EcsRtPprPspData = NULL;
  NORMALIZED_ADDRESS                     EcsNormalizedAddress[MAX_NUM_OF_DPPRCL];
  UINT64                                 EcsSystemMemoryAddress[MAX_NUM_OF_DPPRCL];
  DIMM_INFO                              EcsDimmInfo[MAX_NUM_OF_DPPRCL];
  UINT8                                  Index;

  if ((!mPlatformApeiData->PlatRasPolicy.AmdMemPostPackageRepair /* Boot time PPR disabled */) &&
      (!mRtPprEn /* Runtime PPR disabled */)) {
    return EFI_UNSUPPORTED;
  }

  ZeroMem(EcsNormalizedAddress, (MAX_NUM_OF_DPPRCL * sizeof(NORMALIZED_ADDRESS)));
  ZeroMem(EcsSystemMemoryAddress, (MAX_NUM_OF_DPPRCL * sizeof(UINT64)));
  ZeroMem(EcsDimmInfo, (MAX_NUM_OF_DPPRCL * sizeof(DIMM_INFO)));
  ZeroMem(&DpprEntry, sizeof(DPPR_REPAIR_ENTRY_V3));  //set Dppr entry to zeros

  AtLeastOneRepair = FALSE;

  // If it is not a DRAM ECC error or ECS Row Error then we do not support it.
  if (!(RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.McaType == UMC_MCA_TYPE &&
      RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaIpidMsr.Field.HardwareID == MCA_UMC_ID &&
      RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val == 1 &&
        (RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.ErrorCodeExt == UMC_DRAMECCERR ||
         RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.ErrorCodeExt == UMC_ECSROWERR))
     ) {
    return EFI_UNSUPPORTED;
  }

  //Second check for exception from PPR if the error is from a the notion of uncorrectable ECC retry.
  UmcSyndEccErrInfo.Value = (UINT32)RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaSyndMsr.Field.ErrorInformation;

  if ((RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 0) &&
      (RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Deferred == 0) &&
      (RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.ErrorCodeExt == UMC_DRAMECCERR)) {
    if ((RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UECC == 1) ||
        (UmcSyndEccErrInfo.Fields.HwHistoryErr == 1) ||
        (UmcSyndEccErrInfo.Fields.SwManagedBadSymbolIdErr == 1)) {
      return EFI_ABORTED;
    }
  }

  //Third check for exception from PPR if the error is from a deferred error and the error is due to the BadSymbolId invoked.
  if ((RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Deferred == 1) &&
      (UmcSyndEccErrInfo.Fields.SwManagedBadSymbolIdErr == 1)) {
      return EFI_ABORTED;
  }

  Status = gSmst->SmmLocateProtocol (
      &gAmdApcbSmmServiceProtocolGuid,
      NULL,
      (VOID**)&AmdApcbService
      );
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "DimmPostPackageRepair: Could not locate gAmdApcbSmmServiceProtocolGuid.\n");
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "DimmPostPackageRepair: Status=%r\n", Status);
    return Status;
  }

  Status = gSmst->SmmLocateProtocol (
      &gAmdPostPackageRepairInfoProtocolGuid,
      NULL,
      (VOID **)&AmdPostPackageRepairInfo);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "DimmPostPackageRepair: Could not locate gAmdPostPackageRepairInfoProtocolGuid.\n");
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "DimmPostPackageRepair: Status=%r\n", Status);
    return Status;
  }

  // Translate MCA data into normalized address
  Status = TranslateToNormalizedAddress (
      RasMcaErrorInfo,
      BankIndex,
      mAmdRasServiceSmmProtocol->TranslateDramToNormAddr,
      &NormalizedAddress
      );
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "DimmPostPackageRepair: Translate to normalized address failed.\n");
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "DimmPostPackageRepair: Status=%r\n", Status);
    return Status;
  }

  // Translate Normalized Address into System address and DIMM Info
  ZeroMem (&DimmInfo, sizeof (DIMM_INFO));
  Status = mAmdRasServiceSmmProtocol->McaErrorAddrTranslate (
      &NormalizedAddress,
      &SystemMemoryAddress,
      &DimmInfo  //For ECSROWERR, DimmInfo here should be equal to DimmInfo derived from TranslateDramToNormAddr service.
      );
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "DimmPostPackageRepair: Mca error address translation failed.\n");
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "DimmPostPackageRepair: Status=%r\n", Status);
    return Status;
  }

  Status = mAmdRasServiceSmmProtocol->MapSymbolToDramDevice (
    mAmdRasServiceSmmProtocol,
    RasMcaErrorInfo,
    &NormalizedAddress,
    BankIndex,
    &DeviceStart,
    &DeviceEnd,
    &DeviceType
    );
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "DimmPostPackageRepair: Map symbol to dram device failed.\n");
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "DimmPostPackageRepair: Status=%r\n", Status);
    return Status;
  }

  // Generate repair entries for all indicated repairs
  for ( RepairDevice = DeviceStart;
      RepairDevice <= DeviceEnd;
      RepairDevice++) {
    // Build Post Package Repair entry
    if (mPlatformApeiData->PlatRasPolicy.HardPprEnable) {
      DpprEntry.ddr.RepairType = DRAM_POST_PKG_HARD_REPAIR;
    } else {
      DpprEntry.ddr.RepairType = DRAM_POST_PKG_SOFT_REPAIR;
    }

    DpprEntry.ddr.ErrorCause = 0;
    if (RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UECC) {
      DpprEntry.ddr.ErrorCause = (mSmiMode == POLLING_MODE) ? PPR_UECC_STORM : PPR_UECC_CONSUME;
    } else if (RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Deferred) {
      DpprEntry.ddr.ErrorCause = PPR_UECC_DEFER;
    } else if (RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.CECC) {
      DpprEntry.ddr.ErrorCause = (mSmiMode == POLLING_MODE) ? PPR_CECC_STORM : PPR_ECC;
    }

    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ErrorCause = 0x%08x\n", DpprEntry.ddr.ErrorCause);

    DpprEntry.ddr.Bank = DimmInfo.Bank;
    DpprEntry.ddr.Row = DimmInfo.Row;
    DpprEntry.ddr.Column = DimmInfo.Column;
    DpprEntry.ddr.RankMultiplier = DimmInfo.rankmul;
    DpprEntry.ddr.ChipSelect = DimmInfo.ChipSelect;
    DpprEntry.ddr.SubChannel = DimmInfo.subchan;
    DpprEntry.ddr.Socket = NormalizedAddress.normalizedSocketId;
    DpprEntry.ddr.Channel = NormalizedAddress.normalizedChannelId;
    DpprEntry.ddr.DeviceTypeToRepair = 0;  /// This device type is DDR. Refer ApcbCommon.h for detail
    // If it is a Deferred error, we only need to indicate to repair all
    // devices in one record. MapSymbolToDramDevice will return DeviceStart = 0
    // and DeviceEnd = last device for DeviceType.
    if (RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Deferred) {
      DpprEntry.ddr.Device = DeviceType;
      DpprEntry.ddr.TargetDevice = 0;
    } else {
      DpprEntry.ddr.Device = 0x1F;
      DpprEntry.ddr.TargetDevice = RepairDevice;
    }

    AmdDimmInfo.SocketId = (UINT8)DpprEntry.ddr.Socket;
    AmdDimmInfo.DieId = 0;
    AmdDimmInfo.ChannelId = (UINT8)DpprEntry.ddr.Channel;
    AmdDimmInfo.Chipselect = (UINT8)DpprEntry.ddr.ChipSelect;
    Status = AmdPostPackageRepairInfo->AmdGetPprInfo (AmdPostPackageRepairInfo, &AmdDimmInfo, &AmdPprInfo);
    if (!EFI_ERROR(Status)) {
      DpprEntry.ddr.extfield.bt.SerialNumberLoWord = AmdPprInfo.SerialNumber;
      DpprEntry.ddr.extfield.bt.SerialNumberHiWord = 0;
    }

    if (mRtPprEn) {
      IsValidRtPprReq = ValidateMcaRuntimePostPackageRepair (RasMcaErrorInfo, BankIndex, &AmdPprInfo);
      if (IsValidRtPprReq) {
        if (mPlatformApeiData->PlatRasPolicy.RuntimePprEnableOnly) {
          DpprEntry.ddr.RepairType = DRAM_POST_PKG_HARD_REPAIR;
          DpprEntry.ddr.HardPPRDone = TRUE;
        }
      }
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Runtime Post Package Repair%ais Enabled\n",
        (mPlatformApeiData->PlatRasPolicy.RuntimePprEnableOnly) ? " Only Mode " : " ");
    }

    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Adding DIMM Post Package Repair Entry to APCB:\n");
    if (DpprEntry.ddr.RepairType == DRAM_POST_PKG_HARD_REPAIR) {
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Repair Type = Hard Repair\n");
    }
    if (DpprEntry.ddr.RepairType == DRAM_POST_PKG_SOFT_REPAIR) {
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Repair Type = Soft Repair\n");
    }

    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Bank = 0x%X\n", DpprEntry.ddr.Bank);
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Row = 0x%X\n", DpprEntry.ddr.Row);
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Column = 0x%X\n", DpprEntry.ddr.Column);
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Rank Multiplier = 0x%X\n", DpprEntry.ddr.RankMultiplier);
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Chipselect = 0x%X\n", DpprEntry.ddr.ChipSelect);
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "SubChannel = 0x%X\n", DpprEntry.ddr.SubChannel);
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Socket = 0x%X\n", DpprEntry.ddr.Socket);
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Channel = 0x%X\n", DpprEntry.ddr.Channel);
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Device = 0x%X\n", DpprEntry.ddr.Device);
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "TargetDevice = 0x%X\n", DpprEntry.ddr.TargetDevice);
    *(UINT32 *)&(SerialNumberString[0]) = DpprEntry.ddr.extfield.bt.SerialNumberLoWord;
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "SerialNumber = %02X%02X%02X%02X\n", SerialNumberString[0], SerialNumberString[1], SerialNumberString[2], SerialNumberString[3]);

    // Insert Post Package repair entry
    Status = AmdApcbService->ApcbAddDramPostPkgRepairEntryEx (
        AmdApcbService,
        DPPR_REPAIR_ENTRY_STRUCT_VERSION_V3,
        &DpprEntry
        );
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ApcbAddDramPostPkgRepairEntryEx: %r\n", Status);
    if (!EFI_ERROR (Status)) {
      AtLeastOneRepair = TRUE;
      if ( DpprEntry.ddr.Device != 0x1F ) {
        // Translated all device repair to one record, so break.
        break;
      }
    } else {
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "Failed Inserting DIMM Post Package Repair Entry:\n");
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "DimmPostPackageRepair: Status=%r\n", Status);
      break;
    }
  }

  // Handling ECS Status Array
  if (RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.CECC) {
    EcsRtPprPspData = ((IsValidRtPprReq) ? mEcsRtPprPspData : NULL);
    ParsingEcsStatusArray (
      AmdPostPackageRepairInfo,
      AmdApcbService,
      RasMcaErrorInfo->CpuInfo,
      NormalizedAddress.normalizedChannelId,
      DeviceType,
      &AtLeastOneRepair,
      EcsRtPprPspData,
      EcsNormalizedAddress,
      EcsSystemMemoryAddress,
      EcsDimmInfo
    );
  }

  // If we successfully added Post Package Repair records, then flush back
  // to SPI
  if (AtLeastOneRepair) {
    if (mRtPprEn) {
      //WA for AMD_APCB_SERVICE_PROTOCOL.ApcbFlushData to avoid system stuck in ApcbFlushData service when PSP is still processing previous AsyncCommand.
      RetryCnt = 14; //maximum retry time = 70 us (14 * 5 us)
      while (((*mPspBiosMboxRegAddr & BIT28 /*AsyncCmdInProgress*/) != 0) && (RetryCnt != 0)) {
        MicroSecondDelay(5); //5 us
        RetryCnt--;
      }
      if ((*mPspBiosMboxRegAddr & BIT28) == 0) {
        Status = AmdApcbService->ApcbFlushData (AmdApcbService);
      } else {
        Status = EFI_NOT_READY;
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "PSP is still processing AsyncCmd.\n");
      }
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ApcbFlushData: %r\n", Status);
      if (EFI_ERROR (Status)) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "Failed Flushing DIMM Post Package Repair Entries:\n");
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "DimmPostPackageRepair: Status=%r\n", Status);
      }
    } else {
      Status = AmdApcbService->ApcbFlushData (AmdApcbService);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "ApcbFlushData: %r\n", Status);
      if (EFI_ERROR (Status)) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "Failed Flushing DIMM Post Package Repair Entries:\n");
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "DimmPostPackageRepair: Status=%r\n", Status);
      }
    }
  }

  //
  // [Runtime PPR]
  //
  // Error handling of PspRuntimePprCompletion SMI not received after sending RtPPR request to PSP for a period of time:
  //    If (the queue is always full after three new SMIs) AND (OS has consumed the error in the current RtPPR request)
  //      AND (PSP mailbox interface is ready) AND (PSP is not performing async command)
  //    Then force mPspRtPprComplFlag to true, status to success, and then start checking the result of the ABL repair.
  if (mRtPprEn) {
    // Only print if runtime PPR is enabled, if runtime ppr is disabled then mPspBiosMboxRegAddr is null and can not be printed
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "mRtPprEn:%X, mQueueFull:%d, mMcaWrMsrComplFlag:%X, PspCmd: 0x%08X\n",
      mRtPprEn, mQueueFull, mMcaWrMsrComplFlag, *mPspBiosMboxRegAddr);
  }
  if (mRtPprEn && (mQueueFull > 0 && mQueueFront > 2 /* 0 based */) && mMcaWrMsrComplFlag && ((*mPspBiosMboxRegAddr & (BIT31 | BIT28)) == BIT31)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
      "[Runtime PPR] PspRuntimePprCompletion SMI was not received. Force check for result of ABL repair\n");
    mPspRtPprComplFlag = TRUE;
    mP2cRuntimePprStatus = 0;
    IsRtPprRepairedSuccess ();
  }
  if (IsValidRtPprReq) {
    //ECS
    if (EcsRtPprPspData != NULL) {
      for (Index = 0; Index < EcsRtPprPspData->RepairEntries; Index++) {
        EcsRtPprPspData->DpprClArray[Index].ddr.RepairType = DRAM_POST_PKG_SOFT_REPAIR;
        EcsRtPprPspData->DpprClArray[Index].ddr.extfield.rt.NormalizedAddressLo = (UINT32)(EcsNormalizedAddress[Index].normalizedAddr & 0xFFFFFFFF);
        EcsRtPprPspData->DpprClArray[Index].ddr.extfield.rt.NormalizedAddressHi = (UINT32)((EcsNormalizedAddress[Index].normalizedAddr >> 32) & 0xFFFFFFFF);
        AddRtPprEntryinQueue (&mRtPprPspData->RepairEntries, mNewDpprQueue, EcsRtPprPspData->DpprClArray[Index], &mQueueFront, &mQueueFull);

        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
          "[Runtime PPR - ECS] Number of DpprEntry in Queue: %d. (Queue has been full %d times.)\n",
          mRtPprPspData->RepairEntries, ((mQueueFull > 0) ? (mQueueFront) : 0));

        TmpChkRtPprComplStruct[mQueueFront].CpuInfo = RasMcaErrorInfo->CpuInfo;
        TmpChkRtPprComplStruct[mQueueFront].BankNumber = RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaBankNumber;
        TmpChkRtPprComplStruct[mQueueFront].McaIpid.Value = RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaIpidMsr.Value;
        TmpChkRtPprComplStruct[mQueueFront].SystemMemoryAddress = EcsSystemMemoryAddress[Index];
        TmpChkRtPprComplStruct[mQueueFront].NormalizedAddrInfo = EcsNormalizedAddress[Index];
        TmpChkRtPprComplStruct[mQueueFront].UmcExtErr = UMC_ECSROWERR;
        TmpChkRtPprComplStruct[mQueueFront].RtPprDimmInfo = EcsDimmInfo[Index];
      }
    }

    //MCA
    DpprEntry.ddr.extfield.rt.NormalizedAddressLo = (UINT32)(NormalizedAddress.normalizedAddr & 0xFFFFFFFF);
    DpprEntry.ddr.extfield.rt.NormalizedAddressHi = (UINT32)((NormalizedAddress.normalizedAddr >> 32) & 0xFFFFFFFF);
    DpprEntry.ddr.RepairType = DRAM_POST_PKG_SOFT_REPAIR;
    AddRtPprEntryinQueue (&mRtPprPspData->RepairEntries, mNewDpprQueue, DpprEntry, &mQueueFront, &mQueueFull);

    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
      "[Runtime PPR] Number of DpprEntry in Queue: %d. (Queue has been full %d times.)\n",
      mRtPprPspData->RepairEntries, ((mQueueFull > 0) ? (mQueueFront) : 0));

    TmpChkRtPprComplStruct[mQueueFront].CpuInfo = RasMcaErrorInfo->CpuInfo;
    TmpChkRtPprComplStruct[mQueueFront].BankNumber = RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaBankNumber;
    TmpChkRtPprComplStruct[mQueueFront].McaIpid.Value = RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaIpidMsr.Value;
    TmpChkRtPprComplStruct[mQueueFront].SystemMemoryAddress = SystemMemoryAddress;
    TmpChkRtPprComplStruct[mQueueFront].NormalizedAddrInfo = NormalizedAddress;
    TmpChkRtPprComplStruct[mQueueFront].UmcExtErr = RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.ErrorCodeExt;
    TmpChkRtPprComplStruct[mQueueFront].RtPprDimmInfo = DimmInfo;

    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[Runtime PPR] TmpChkRtPprComplStruct.CpuInfo.ProcessorNumber: 0x%x, TmpChkRtPprComplStruct.BankNumber: 0x%x\n",
      TmpChkRtPprComplStruct[mQueueFront].CpuInfo.ProcessorNumber, TmpChkRtPprComplStruct[mQueueFront].BankNumber);
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[Runtime PPR] NormalizedAddress = 0x%016lx, (NormalizedAddressHi = 0x%08x, NormalizedAddressLo = 0x%08x).\n",
      NormalizedAddress.normalizedAddr,
      mRtPprPspData->DpprClArray[mQueueFront].ddr.extfield.rt.NormalizedAddressHi,
      mRtPprPspData->DpprClArray[mQueueFront].ddr.extfield.rt.NormalizedAddressLo);

    if (!mReadyForNextRtPprReq) {
      Status = EFI_NOT_READY;
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[Runtime PPR] The previous Runtime PPR request is still in progress. ABL will not perform this Runtime PPR request.\n");
    } else {
      Status  = PerformRuntimePpr (mRtPprPspData, (sizeof (DPPR_REPAIR_BLOCK_V3) + ((mRtPprPspData->RepairEntries - 1) * sizeof (DPPR_REPAIR_ENTRY_V3))));
      if (EFI_ERROR (Status)) {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[Runtime PPR] Send Runtime PPR command to PSP FW failed. ABL will not perform this Runtime PPR request.\n");
      } else {
        mReadyForNextRtPprReq = FALSE; //This flag will be updated by IsRtPprRepairedSuccess function
        mMcaWrMsrComplFlag = FALSE;
        mPspRtPprComplFlag = FALSE;
        mNumOfRtPptEntryInServed = mRtPprPspData->RepairEntries;
        CopyMem (mChkRtPprComplStruct, TmpChkRtPprComplStruct, sizeof (mChkRtPprComplStruct));

        ZeroMem (TmpChkRtPprComplStruct, sizeof (TmpChkRtPprComplStruct));
        ZeroMem (mRtPprPspData, sizeof(DPPR_REPAIR_BLOCK_V3) + ((MAX_NUM_OF_DPPRCL - 1) * sizeof (DPPR_REPAIR_ENTRY_V3)));

        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[Runtime PPR] Send Runtime PPR command to PSP FW succeeded. After ABL finishes performing Runtime PPR, PSP will trigger SMI.\n");
      }
    }
  } else {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] This Runtime PPR request is not supported.\n");
    Status = EFI_UNSUPPORTED;
  }

  return Status;
}

BOOLEAN
FindThresholdOrDeferredError (
  VOID
  )
{
  UINTN                 ProcessorNumber;
  BOOLEAN               ErrorFound;

  ErrorFound = FALSE;
  for (ProcessorNumber = 0; ProcessorNumber < gSmst->NumberOfCpus; ProcessorNumber++) {
    //MCA Error Handle
    if (McaErrorHandle(ProcessorNumber)) {
      ErrorFound = TRUE;
    }
  }

  return ErrorFound;
}

VOID
ProcessPfehSmiSource (
  BOOLEAN  *SmiSourceChecked
  )
{
  EFI_STATUS              Status;
  RAS_THRESHOLD_CONFIG    RasThresholdConfig;
  UINT64                  MsrData;
  UINTN                   NumberOfInstalledProcessors = 0;
  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL *FabricTopology = NULL;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS - MCA] INTERRUPT_MODE - Entry: \n");
  if (*SmiSourceChecked)
    return;

  *SmiSourceChecked = TRUE;

  if (!LeakyBucketService (&mSmiCount, &mTscLast, &mSmiMode)) {
    //The Leaky Bucket does not overflow or the Leaky Bucket service is disabled
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS - MCA] INTERRUPT_MODE - Exit: Continue to stay in INTERRUPT_MODE\n");
    return;
  }

  // set UMC::CH::EccErrCntCtrl[EccErrInt]=0h when the SMI storm handler switch from interrupt mode to polling mode.
  if ((mPlatformApeiData->PlatRasPolicy.DdrEccErrorCounterEnable != 0) && (mPlatformApeiData->PlatRasPolicy.PFEHEnable == 1)) {
    if (mPlatformApeiData->PlatRasPolicy.DdrEccErrorCounterIntEnable) {
      Status = gSmst->SmmLocateProtocol(&gAmdFabricTopologyServices2SmmProtocolGuid, NULL, (VOID **) &FabricTopology);
      if (!EFI_ERROR (Status)) {
        FabricTopology->GetSystemInfo (FabricTopology, &NumberOfInstalledProcessors, NULL, NULL, NULL, NULL);
        RasSmmFwConfigEccErrInt(mRbBusMap, NumberOfInstalledProcessors, 0); // Disabled Interrupt in SMI POLLING_MODE.
      }
    }
  }

  //The Leaky Bucket has overflowed, and now SmiMode is set to POLLING_MODE by LeakyBucketService.
  //Try to turn on periodic SMI's at rate of SmiPeriod
  Status = RasSmmRegisterMcePeriodicSmi ();
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS - MCA] INTERRUPT_MODE - Exit: Register AmdMcePeriodicSmiCallback failed: 0x%08x, cannot switch to POLLING_MODE.\n", Status);
    mSmiMode = INTERRUPT_MODE;
    return;
  }

  mSmiCoolOff = mPlatformApeiData->PlatRasPolicy.RasSmiThreshold;

  //Disable SMI generation for Machine Check Events
  //01.
  RasThresholdConfig.ThresholdControl = mPlatformApeiData->PlatRasPolicy.McaErrThreshEn;
  RasThresholdConfig.ThresholdCount = mPlatformApeiData->PlatRasPolicy.McaErrThreshCount;
  RasThresholdConfig.ThresholdIntType = MCA_NO_INTERRUPT; // No interrupt
  mAmdRasServiceSmmProtocol->SetMcaThreshold(NULL, NULL, &RasThresholdConfig, FALSE);
  //02.
  MsrData = 0xFFFFFFFFFFFFFFFF;
  AsmWriteMsr64 (MSR_PFEH_DEF_INT_MASK, MsrData);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS - MCA] INTERRUPT_MODE - Exit: Successfully switched to POLLING_MODE\n");

  return;
}

VOID
ProcessPeriodicSMI (
  VOID
  )
{
  UINT64                  MsrData;
  RAS_THRESHOLD_CONFIG    RasThresholdConfig;
  UINT32                  SmiThreshold;
  BOOLEAN                 McaErrThreshEn;
  UINT16                  McaErrThreshCount;
  EFI_STATUS              Status = EFI_SUCCESS;
  UINTN                   NumberOfInstalledProcessors = 0;
  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL *FabricTopology = NULL;

  McaErrThreshEn = mPlatformApeiData->PlatRasPolicy.McaErrThreshEn;
  McaErrThreshCount = mPlatformApeiData->PlatRasPolicy.McaErrThreshCount;
  SmiThreshold = mPlatformApeiData->PlatRasPolicy.RasSmiThreshold;

  if (mSmiMode == POLLING_MODE) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "POLLING_MODE - Entry\n");
    if (FindThresholdOrDeferredError()) {
      // An error was detected, reset the threshold to max
      mSmiCoolOff = SmiThreshold;
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "POLLING_MODE - Exit: An error was detected, reset the mSmiCoolOff to max: 0x%08x\n", mSmiCoolOff);
    } else {
      mSmiCoolOff--;
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  POLLING_MODE: No Error, mSmiCoolOff-- = 0x%08x\n", mSmiCoolOff);
      if (mSmiCoolOff == 0) {
        // If we go one full leaky bucket time scale with no errors, return to interrupt mode
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  POLLING_MODE: mSmiCoolOff == 0x%08x => Go one full leaky bucket time scale with no errors => Start returning to INTERRUPT_MODE.\n",
                                mSmiCoolOff);

        //Try to turn off periodic SMI's for PFEH polling
        if (mPollingModeHandle == NULL) {
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "POLLING_MODE - Exit: cannot locate mPollingModeHandle, fail to return to INTERRUPT_MODE\n");
          return;
        }

        Status = mAmdPeriodicalDispatch->UnRegister (
                                           mAmdPeriodicalDispatch,
                                           mPollingModeHandle);
        if (EFI_ERROR(Status)) {
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "POLLING_MODE - Exit: cannot UnrRgister AmdMcePeriodicSmiCallback, fail to return to INTERRUPT_MODE\n");
          return;
        }

        mPollingModeHandle = NULL;
        mSmiMode = INTERRUPT_MODE;
        mSmiCount  = 0;

        //Enable SMI generation for Machine Check Events
        //01.
        RasThresholdConfig.ThresholdControl = McaErrThreshEn;
        RasThresholdConfig.ThresholdCount = McaErrThreshCount;
        RasThresholdConfig.ThresholdIntType = MCA_SMI; // SMI trigger event
        mAmdRasServiceSmmProtocol->SetMcaThreshold(NULL, NULL, &RasThresholdConfig, FALSE);
        //02.
        MsrData = 0x0000000000000000;
        AsmWriteMsr64 (MSR_PFEH_DEF_INT_MASK, MsrData);

       //Restore the UMC::CH::EccErrCntCtrl[EccErrInt]=2h when SMI storm handler switch from polling mode back to interrupt mode.
       if ((mPlatformApeiData->PlatRasPolicy.DdrEccErrorCounterEnable != 0) && (mPlatformApeiData->PlatRasPolicy.PFEHEnable == 1)) {
         if (mPlatformApeiData->PlatRasPolicy.DdrEccErrorCounterIntEnable) {
           Status = gSmst->SmmLocateProtocol(&gAmdFabricTopologyServices2SmmProtocolGuid, NULL, (VOID **) &FabricTopology);
           if (!EFI_ERROR (Status)) {
             FabricTopology->GetSystemInfo (FabricTopology, &NumberOfInstalledProcessors, NULL, NULL, NULL, NULL);
             RasSmmFwConfigEccErrInt(mRbBusMap, NumberOfInstalledProcessors, 2); // Restore the Interrupt type to SMI = 2
           }
         }
       }

        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "POLLING_MODE - Exit: Successfully return to INTERRUPT_MODE\n");
      } else {
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "POLLING_MODE - Exit: No error duration time is less than SMI time scale\n");
      }
    }
  }

  return;
}

EFI_STATUS
EFIAPI
AmdMcePeriodicSmiCallback (
  IN       EFI_HANDLE                                DispatchHandle,
  IN CONST FCH_SMM_PERIODICAL_REGISTER_CONTEXT       *RegisterContext,
  IN OUT   EFI_SMM_PERIODIC_TIMER_CONTEXT            *PeriodicTimerContext,
  IN OUT   UINTN                                     *SizeOfContext
  )
{

  ProcessPeriodicSMI ();

  return EFI_SUCCESS;
}

EFI_STATUS
RasSmmRegisterMcePeriodicSmi (
  VOID
  )
{
  EFI_STATUS                               Status;
  FCH_SMM_PERIODICAL_REGISTER_CONTEXT      PeriodicalRegisterContext;
  UINT32                                   SmiPeriod;

  SmiPeriod = mPlatformApeiData->PlatRasPolicy.RasSmiPeriod;

  //
  // Periodic Timer SMI Registration
  //
  Status = gSmst->SmmLocateProtocol (
                    &gFchSmmPeriodicalDispatch2ProtocolGuid,
                    NULL,
                    (VOID **)&mAmdPeriodicalDispatch
                    );

  ASSERT_EFI_ERROR (Status);
  if (EFI_ERROR (Status)) {
    return Status;
  }

  //SmiTrig0, In FchSmmPeriodicalDispatcher.c:
  //  if (SmiTickInterval == LONG_TIMER_SMI_INTERVAL) then {FCH_SMI_REG98 |= BIT29}
  //  FCH_SMI_REG98[29] = 1 => SmiTimer to be SmiLongTimer register and long timer runs at 1 ms unit time.
  PeriodicalRegisterContext.SmiTickInterval  = LONG_TIMER_SMI_INTERVAL;

  //SmiTimer, FCH_SMI_REG96 = (UINT16) (Period / SmiTickInterval) & 0x7FFF;
  PeriodicalRegisterContext.Period           = SmiPeriod * LONG_TIMER_SMI_INTERVAL;

  //SmiTimer, FCH_SMI_REG96 |= SMI_TIMER_ENABLE
  PeriodicalRegisterContext.StartNow         = 1;

  mPollingModeHandle = NULL;
  Status = mAmdPeriodicalDispatch->Register (
                                     mAmdPeriodicalDispatch,
                                     AmdMcePeriodicSmiCallback,
                                     &PeriodicalRegisterContext,
                                     &mPollingModeHandle
                                     );

  return Status;
}

EFI_STATUS
RasSmmFwMcaClr (
  IN       UINTN    *ProcessorNumber,
  IN       UINTN    *McaBankNumber
  )
{
  EFI_STATUS            Status;
  RAS_THRESHOLD_CONFIG  RasThresholdConfig;

  //Clear the MCA status register
  Status = mAmdRasServiceSmmProtocol->ClrMcaStatus (*ProcessorNumber, *McaBankNumber, FALSE);

  //re-init Error Thresholding ErrCnt
  RasThresholdConfig.ThresholdControl = mPlatformApeiData->PlatRasPolicy.McaErrThreshEn;
  RasThresholdConfig.ThresholdCount = mPlatformApeiData->PlatRasPolicy.McaErrThreshCount;
  RasThresholdConfig.ThresholdIntType = MCA_SMI; // SMI trigger event
  Status = mAmdRasServiceSmmProtocol->SetMcaThreshold(ProcessorNumber, McaBankNumber, &RasThresholdConfig, TRUE);

  return Status;
}

BOOLEAN
SearchProcessorIndexinMadt (
  IN       UINT32    LocalApicId,
  OUT      UINT32    *ProcessorIndex
  )
{
  UINT32  Index;

  for (Index = 0; Index < mPlatformApeiData->McaLocalApicIdMappingTable->TableEntryNum; Index++) {
    if (LocalApicId == mPlatformApeiData->McaLocalApicIdMappingTable->McaLocalApicIdMappingStructure[Index].ApicId) {
      *ProcessorIndex = Index;
      return TRUE;
    }
  }

  return FALSE;
}

EFI_STATUS
LocalApicIdToErrStatusBlkOffset(
  IN       UINT32    LocalApicId,
  IN       UINT8     BankNumber,
  OUT      UINT32    *ErrStatusBlkOffset
)
{
  UINT32    ProcessorIndex;

  ProcessorIndex = 0;
  if (!SearchProcessorIndexinMadt (LocalApicId, &ProcessorIndex)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] The LocalApicId:0x%x cannot be found in the MADT table.\n", LocalApicId);
    return EFI_NOT_FOUND;
  }

  // Index = ((CPU number) * (MCA Banks per CPU)) + (MCA Bank index)
  *ErrStatusBlkOffset = ((ProcessorIndex * mPlatformApeiData->MaxMcaBankCount) + BankNumber) * MAX_MCA_ERROR_BLOCK_SIZE;

  return EFI_SUCCESS;
}

EFI_STATUS
McaArsTerminator (
  IN       RAS_MCA_ERROR_INFO_V2 *RasMcaErrorInfo,
  IN       UINTN                 BankIndex
  )
{
  EFI_STATUS Status = EFI_SUCCESS;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] Check ARS activity.\n");

  if (mAmdPspArsServiceProtocol == NULL) {
    Status = gSmst->SmmLocateProtocol (
                      &gAmdPspArsServiceProtocolGuid,
                      NULL,
                      (VOID **)&mAmdPspArsServiceProtocol
                      );
    if (EFI_ERROR (Status)) {
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] Locate AmdPspArsServiceProtocol failed\n");
      return Status;
    }
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] Call out McaErrorArsHandler\n");
  mAmdPspArsServiceProtocol->McaErrorArsHandler(RasMcaErrorInfo, (UINT8)BankIndex);

  return Status;
}

BOOLEAN
McaErrorHandle (
  UINTN         ProcessorNumber
  )
{
  EFI_STATUS            Status = EFI_SUCCESS;
  RAS_MCA_ERROR_INFO_V2 RasMcaErrorInfo;
  UINTN                 McaBankNumber;
  UINT8                 ErrorBankCount;
  UINTN                 BankIndex;
  BOOLEAN               AlreadyPPRepaired;
  BOOLEAN               ErrorFound;
  UINT16                GhesStrucType;

  //Variable Init
  ErrorFound = FALSE;
  AlreadyPPRepaired = FALSE;
  GhesStrucType = EFI_ACPI_6_3_IA32_ARCHITECTURE_CORRECTED_MACHINE_CHECK;

  ZeroMem (&RasMcaErrorInfo, sizeof (RasMcaErrorInfo));
  RasMcaErrorInfo.CpuInfo.ProcessorNumber = ProcessorNumber;

  //Collect Threshold and Deferred error
  mAmdRasServiceSmmProtocol->SearchMcaError (&RasMcaErrorInfo);
  ErrorBankCount = (UINT8)RasMcaErrorInfo.McaBankCount;
  if (ErrorBankCount == 0) {
    //No error found, return FALSE
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  No MCA Error found\n");
    return ErrorFound;
  }

  //Print out error to BIOS debug message
  LogMCAError (&RasMcaErrorInfo);

  for (BankIndex = 0; BankIndex < ErrorBankCount; BankIndex++) {
    if ((RasMcaErrorInfo.McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val) ||
        (RasMcaErrorInfo.McaBankErrorInfo[BankIndex].McaDeStatMsr.Field.Val)) {
      McaBankNumber = RasMcaErrorInfo.McaBankErrorInfo[BankIndex].McaBankNumber;
      // Check ARS activity and terminate an in-progress ARS if we find any error not assoiciated with the ARS
      Status = McaArsTerminator(&RasMcaErrorInfo,BankIndex);
      if (IsBankCloaked(ProcessorNumber, McaBankNumber)) {
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "  Valid MCA Error\n");
          //Un-Cloak MCA register
          mAmdRasServiceSmmProtocol->SetMcaCloakCfg (ProcessorNumber, 0, LShiftU64 (1, McaBankNumber));
          ErrorFound = TRUE;

        if (RasMcaErrorInfo.McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC &&
            RasMcaErrorInfo.McaBankErrorInfo[BankIndex].McaStatusMsr.Field.En) {
            //Set GenerateMceOnExit
            mAmdRasServiceSmmProtocol->RasSmmExitType (ProcessorNumber, GENERATE_MCE_ON_EXIT);
        }

        // DEFERRED ERROR
        if (RasMcaErrorInfo.McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Deferred ||
            RasMcaErrorInfo.McaBankErrorInfo[BankIndex].McaDeStatMsr.Field.Val) {
          // Check for Dimm Post Package repair on any Deferred
          if (!AlreadyPPRepaired) {
            Status = DimmPostPackageRepair (&RasMcaErrorInfo, (UINT8)BankIndex);
            if (!EFI_ERROR (Status)) {
              AlreadyPPRepaired = TRUE;
              IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "The demand for DIMM POST PACKAGE REPAIR has been created.\n");
            }
          }
          if (RasMcaErrorInfo.McaBankErrorInfo[BankIndex].McaConfigMsr.Field.DeferredIntType) {
            //Set GenerateDeferredLvtOnExit
            mAmdRasServiceSmmProtocol->RasSmmExitType (ProcessorNumber, GENERATE_DEFERREDLVT_ON_EXIT);
          }
        }

        // MISC 0
        // Only check threshold status if there was an overflow
        if (RasMcaErrorInfo.McaBankErrorInfo[BankIndex].McaMisc0Msr.Field.Ovrflw ||
            IsEccErrCntOvrflw (mPlatformApeiData, &RasMcaErrorInfo, BankIndex)) {
          // Check for Dimm Post Package repair on any overflow
          if (!AlreadyPPRepaired) {
            Status = DimmPostPackageRepair (&RasMcaErrorInfo, (UINT8)BankIndex);
            if (!EFI_ERROR (Status)) {
              AlreadyPPRepaired = TRUE;
              IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "The demand for DIMM POST PACKAGE REPAIR has been created.\n");
            }
          }
          if (RasMcaErrorInfo.McaBankErrorInfo[BankIndex].McaMisc0Msr.Field.ThresholdIntType == MCA_APIC) {
            //Set GenerateThresholdLvtOnExit
            mAmdRasServiceSmmProtocol->RasSmmExitType (ProcessorNumber, GENERATE_THRESHOLDLVT_ON_EXIT);
          }
          if ((RasMcaErrorInfo.McaBankErrorInfo[BankIndex].McaConfigMsr.Field.IntPresent == 1) && (RasMcaErrorInfo.McaBankErrorInfo[BankIndex].McaConfigMsr.Field.IntEn == 1)) {
            //Set GenerateThresholdLvtOnExit
            mAmdRasServiceSmmProtocol->RasSmmExitType (ProcessorNumber, GENERATE_THRESHOLDLVT_ON_EXIT);
          }
        }

        // MISC 1
        // Only check threshold status if there was an overflow
        if (RasMcaErrorInfo.McaBankErrorInfo[BankIndex].McaMisc1Msr.Field.Ovrflw) {
          //Check Error Threshold interrupt type.
          if (RasMcaErrorInfo.McaBankErrorInfo[BankIndex].McaMisc1Msr.Field.ThresholdIntType == MCA_APIC) {
            //Set GenerateThresholdLvtOnExit
            mAmdRasServiceSmmProtocol->RasSmmExitType (ProcessorNumber, GENERATE_THRESHOLDLVT_ON_EXIT);
          }
          if ((RasMcaErrorInfo.McaBankErrorInfo[BankIndex].McaConfigMsr.Field.IntPresent == 1) && (RasMcaErrorInfo.McaBankErrorInfo[BankIndex].McaConfigMsr.Field.IntEn == 1)) {
            //Set GenerateThresholdLvtOnExit
            mAmdRasServiceSmmProtocol->RasSmmExitType (ProcessorNumber, GENERATE_THRESHOLDLVT_ON_EXIT);
          }
        }

        // Update GHES
        if (RasMcaErrorInfo.McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC) {
          // Valid uncorrectable error
          GhesStrucType = EFI_ACPI_6_3_IA32_ARCHITECTURE_MACHINE_CHECK_EXCEPTION;
        } else if (RasMcaErrorInfo.McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Deferred){
          // Valid deferred error
          GhesStrucType = EFI_ACPI_6_3_IA32_ARCHITECTURE_DEFERRED_MACHINE_CHECK;
        } else {
          // Valid other error, i.e. corrected error
          GhesStrucType = EFI_ACPI_6_3_IA32_ARCHITECTURE_CORRECTED_MACHINE_CHECK;
        }
        GhesUpdate(&RasMcaErrorInfo,
                   (UINT8)BankIndex,
                   ProcessorNumber,
                   GhesStrucType);
      } //if (IsBankCloaked(ProcessorNumber, BankIndex))
    } //if (RasMcaErrorInfo.McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val)

    //Reset PPR flag for next bank
    AlreadyPPRepaired = FALSE;
  } //for (BankIndex = 0; BankIndex < ErrorBankCount; BankIndex++)

  return ErrorFound;
}

EFI_STATUS
RasSmmUmcEccErrCntRestore (
  UINTN         ProcessorNumber
  )
{
  RAS_MCA_ERROR_INFO_V2 RasMcaErrorInfo;
  UINT8                 ErrorBankCount;
  UINTN                 BankIndex = 0;

  ZeroMem (&RasMcaErrorInfo, sizeof (RasMcaErrorInfo));
  RasMcaErrorInfo.CpuInfo.ProcessorNumber = ProcessorNumber;
  //Collect Error Bank Count
  mAmdRasServiceSmmProtocol->SearchMcaError (&RasMcaErrorInfo);
  ErrorBankCount = (UINT8)RasMcaErrorInfo.McaBankCount;

  // Check CECC and Ecc Error Counter and threshole.
  for (BankIndex = 0; BankIndex < ErrorBankCount; BankIndex++) {
    if (RasMcaErrorInfo.McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Val) {
      if (RasMcaErrorInfo.McaBankErrorInfo[BankIndex].McaStatusMsr.Field.CECC) {
        if (mPlatformApeiData->PlatRasPolicy.DdrEccErrorCounterEnable != 0) {
          RasSmmFwEccErrCntRestore (mPlatformApeiData, &RasMcaErrorInfo, BankIndex);
        }
      }
    }
  }
  return EFI_SUCCESS;
}

VOID
AddRtPprEntryinQueue (
  UINT32                *NumOfRtPptEntryInQueue,
  DPPR_REPAIR_ENTRY_V3  *DpprQueue,
  DPPR_REPAIR_ENTRY_V3  Dppr,
  UINT8                 *QueueFront,
  UINT32                *QueueFull
  )
{
  static UINT8   QFront = 0;
  static UINT32  QFull = 0;

  if (*NumOfRtPptEntryInQueue == 0) {
    QFront = 0;
    QFull  = 0;
  }

  //Always keep the most recent "MAX_NUM_OF_DPPRCL" records
  if (QFront == MAX_NUM_OF_DPPRCL) {
    IDS_HDT_CONSOLE_RAS_TRACE (
      RAS_TRACE_INFO,
      "%a: The queue is full and the oldest entry will be overwritten by the most recently added entry.\n",
      __FUNCTION__
    );
    QFront = 0;
    QFull++;
  }

  DpprQueue[QFront] = Dppr;

  *QueueFront = QFront;
  *QueueFull  = QFull;
  *NumOfRtPptEntryInQueue = (QFull > 0) ? MAX_NUM_OF_DPPRCL : (QFront + 1);

  QFront++;

  return;
}

EFI_STATUS
EFIAPI
PspRuntimePprCompletion (
  IN       VOID             *P2cRuntimePprStatus
  )
{
  EFI_STATUS                Status;
  UINTN                     HandleBufferSize = 0;
  UINTN                     Index = 0;
  UINT8                     FreeFlag = 0;
  EFI_HANDLE                *HandleBuffer = NULL;
  AMD_CPM_RAS_OEM_PROTOCOL  *AmdRasOemProtocol = NULL;
  BOOLEAN                   RepairResult[MAX_NUM_OF_DPPRCL] = {0};

  //This is the CPM callback service for the Runtime PPR completion SMI triggered by the PSP.
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] %a Entry\n", __FUNCTION__);

  mP2cRuntimePprStatus = *(UINT32 *)P2cRuntimePprStatus;
  if (mP2cRuntimePprStatus != 0) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] PSP RuntimePpr SMI callback returns ABL repair failed: 0x%08x.\n",
      mP2cRuntimePprStatus);

    //Locate Ras Oem Protocol
    Status = gSmst->SmmLocateHandle (
                                ByProtocol,
                                &gAmdCpmRasOemSmmProtocolGuid,
                                NULL,
                                &HandleBufferSize,
                                HandleBuffer);
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] SmmLocateHandle gAmdCpmRasOemSmmProtocolGuid Status =%r\n", Status);

    if (EFI_ERROR(Status)) {
      if (Status == EFI_BUFFER_TOO_SMALL) {
        HandleBuffer = AllocateRuntimePool (HandleBufferSize);
        if (HandleBuffer != NULL) {
          Status = gSmst->SmmLocateHandle (
                      ByProtocol,
                      &gAmdCpmRasOemSmmProtocolGuid,
                      NULL,
                      &HandleBufferSize,
                      HandleBuffer);
          if (!EFI_ERROR(Status))
            FreeFlag = 1;
        }
      }
    }

    if (!EFI_ERROR(Status)) {
      if (HandleBuffer != NULL) {
        for (Index = 0; Index < (HandleBufferSize / sizeof(EFI_HANDLE)); Index++) {
          Status = gSmst->SmmHandleProtocol (
                                    HandleBuffer[Index],
                                    &gAmdCpmRasOemSmmProtocolGuid,
                                    (VOID **)&AmdRasOemProtocol);
          if(!EFI_ERROR(Status)){
            AmdRasOemProtocol->OemErrorLogEventRtPpr (mP2cRuntimePprStatus, mChkRtPprComplStruct, RepairResult, mNumOfRtPptEntryInServed);
          }
        }
      }
    }

    if ((FreeFlag == 1) && (HandleBuffer != NULL)){
      Status = gSmst->SmmFreePool (HandleBuffer);
      ASSERT_EFI_ERROR (Status);
    }
  }

  mPspRtPprComplFlag = TRUE;
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "mPspRtPprComplFlag: 0x%0X, mMcaWrMsrComplFlag: 0x%0X\n", mPspRtPprComplFlag, mMcaWrMsrComplFlag);

  if (mMcaWrMsrComplFlag) {
    IsRtPprRepairedSuccess ();
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] %a Exit\n", __FUNCTION__);
  return EFI_SUCCESS;
}

VOID
DisableRtPpr (
  VOID
  )
{
  mRtPprEn = FALSE;
  mPlatformApeiData->PlatRasPolicy.RuntimePprEnable = FALSE;
  mPlatformApeiData->PlatRasPolicy.RuntimePprEnableOnly = FALSE;
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS Runtime PPR] Disable Runtime PPR.\n");

  return;
}

EFI_STATUS
RuntimePprInit (
    VOID
  )
{
  EFI_STATUS    Status;
  EFI_HANDLE    Handle;

  mRtPprEn = mPlatformApeiData->PlatRasPolicy.RuntimePprEnable || mPlatformApeiData->PlatRasPolicy.RuntimePprEnableOnly;

  if (!mRtPprEn) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] %a Runtime Post Package Repair is disabled\n", __FUNCTION__);
    return EFI_SUCCESS;
  }

  ZeroMem (mChkRtPprComplStruct, sizeof (mChkRtPprComplStruct));
  mRtPprDdrEccErrCntStartCnt = mPlatformApeiData->PlatRasPolicy.DdrEccErrorCounterStartCount;

  Status = gSmst->SmmAllocatePool (
                    EfiRuntimeServicesData,
                    sizeof (DPPR_REPAIR_BLOCK_V3) + ((MAX_NUM_OF_DPPRCL - 1) * sizeof (DPPR_REPAIR_ENTRY_V3)),
                    (VOID **)&mRtPprPspData
                    );
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] %a SmmAllocatePool for RTPPR REQ PSP DATA BLOCK: %r.\n", __FUNCTION__, Status);
  if (EFI_ERROR(Status)) {
    DisableRtPpr ();
    return Status;
  } else {
    ZeroMem (mRtPprPspData, sizeof(DPPR_REPAIR_BLOCK_V3) + ((MAX_NUM_OF_DPPRCL - 1) * sizeof (DPPR_REPAIR_ENTRY_V3)));
    mNewDpprQueue = mRtPprPspData->DpprClArray;  //Maximum number of DpprClArray elements is MAX_NUM_OF_DPPRCL.
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] %a RTPPR REQ PSP DATA BLOCK@ 0x%08X.\n", __FUNCTION__, (UINT32)(UINTN)mRtPprPspData);
  }

  Status = gSmst->SmmAllocatePool (
                    EfiRuntimeServicesData,
                    sizeof(DPPR_REPAIR_BLOCK_V3) + ((MAX_NUM_OF_DPPRCL - 1) * sizeof (DPPR_REPAIR_ENTRY_V3)),
                    (VOID **)&mEcsRtPprPspData
                    );
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] %a SmmAllocatePool for ESC RTPPR REQ PSP DATA BLOCK: %r.\n", __FUNCTION__, Status);
  if (EFI_ERROR(Status)) {
    DisableRtPpr ();
    return Status;
  } else {
    ZeroMem (mEcsRtPprPspData, sizeof(DPPR_REPAIR_BLOCK_V3) + ((MAX_NUM_OF_DPPRCL - 1) * sizeof (DPPR_REPAIR_ENTRY_V3)));
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] %a ECS RTPPR REQ PSP DATA BLOCK@ 0x%08X.\n", __FUNCTION__, (UINT32)(UINTN)mEcsRtPprPspData);
  }

  Handle = NULL;
  Status = gSmst->SmmInstallProtocolInterface(
               &Handle,
               &gAmdPspRuntimePprServiceProtocolGuid,
               EFI_NATIVE_INTERFACE,
               &mAmdPspRuntimePprServiceProtocol
           );

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] %a Install AmdPspRuntimePprServiceProtocol: %r\n", __FUNCTION__, Status);
  if (EFI_ERROR(Status)) {
    DisableRtPpr ();
    return Status;
  }

  //Required for WA for AMD_APCB_SERVICE_PROTOCOL.ApcbFlushData
  if (GetPspBiosMboxRegAddr (0, (VOID **)&mPspBiosMboxRegAddr)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS Runtime PPR] PspBiosMboxRegAddr: 0x%08X\n", (UINT32)(UINTN)mPspBiosMboxRegAddr);
  } else {
    DisableRtPpr ();
    return EFI_NOT_FOUND;
  }

  return EFI_SUCCESS;
}

BOOLEAN
ValidateMcaRuntimePostPackageRepair (
  IN       RAS_MCA_ERROR_INFO_V2    *RasMcaErrorInfo,
  IN       UINT8                    BankIndex,
  IN       AMD_PPR_INFO             *AmdPprInfo
  )
{
  UINT8         SegNum;
  UINT8         BusNum;
  UINT16        ChannelId;
  UINT32        UmcRegBase;
  UINT32        UmcCapHi;
  UINT32        UmcEccCtrl;
  UINT32        RegEax;
  UINT64        SocFamilyID;
  UINT8         CpuModStep;

  RegEax = 0;
  AsmCpuid (0x80000001, &RegEax, NULL, NULL, NULL);
  SocFamilyID = (RegEax & RAW_FAMILY_ID_MASK);
  CpuModStep = 0;
  if ((SocFamilyID == F1A_BRH_RAW_ID) || (SocFamilyID == F1A_BRHD_RAW_ID)) {
    CpuModStep = (RegEax & (CPUID_BASE_MODEL_MASK | CPUID_STEPPING_MASK));
  }

  //Get IOHC bus number
  AcquireIoHubSSecSegBusNum (mRbBusMap, RasMcaErrorInfo->CpuInfo.SocketId, 0, 0, &BusNum, &SegNum);
  //Get Channel number
  ChannelId = McaInstanceIdSearch(RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaIpidMsr);
  //Get UMC0CHx00000000...UMC11CHx0000000C [DRAM CS Base Address] (UMC::CH::BaseAddr)
  UmcRegBase = (UMC0_CH_REG_BASE + (UMC_SMN_ADDR_OFFSET * ChannelId));
  //Get UMC[00...11]CHx00000DF4 [UMC Capabilities High] (UMC::UmcCapHi)
  RasSmnRead (SMN_SEG_BUS (SegNum, BusNum), UmcRegBase + UMC_CAPABILITIES_HIGH, &UmcCapHi);
  RasSmnRead (SMN_SEG_BUS (SegNum, BusNum), UmcRegBase + UMC_ECC_CTRL, &UmcEccCtrl);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
    "[RAS Runtime PPR] %a [FamModStepExt: 0x%08X] EccEnabled: 0x%x, EccChipKillCap: 0x%x, PinReducedEcc: 0x%x, Deferred: 0x%x, UC: 0x%x\n",
    __FUNCTION__, RegEax, (UmcCapHi & BIT30), (UmcCapHi & BIT31), (UmcEccCtrl & BIT14),
    RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Deferred,
    RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC
    );

  // The following if statement checks these conditions:
  // 1. ECC is not enabled
  // 2. The silicon step is ((Classic A0/B0) OR (Dense A0)) AND ((the config is not chipkill capable) OR (this is a pin reduced dimm))
  // 3. The error is a deferred error
  // 4. The error is an uncorrectable error
  // 5. The silicon step is ((Classic C0+) OR (Dense B0+)) AND (the dimm is 8H+ 3DS)
  // If any of these conditions are TRUE then this repair/config is not valid for runtime PPR
  if (((UmcCapHi & BIT30 /* Bit30 EccEnabled */) == 0) || // 1.
      ((((SocFamilyID == F1A_BRH_RAW_ID) && (CpuModStep < 0x20 /* C0 */)) || ((SocFamilyID == F1A_BRHD_RAW_ID) && (CpuModStep < 0x10 /* B0 */))) && // 2.
      (((UmcCapHi & BIT31 /* Bit31 EccChipKillCap */) == 0) || ((UmcEccCtrl & BIT14 /* Bit14 PinReducedECC */) == BIT14))) || // 2. (continued)
      (RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.Deferred == 1) || // 3.
      (RasMcaErrorInfo->McaBankErrorInfo[BankIndex].McaStatusMsr.Field.UC == 1) || // 4.
      ((((SocFamilyID == F1A_BRH_RAW_ID) && (CpuModStep >= 0x20 /* C0 */)) || ((SocFamilyID == F1A_BRHD_RAW_ID) && (CpuModStep >= 0x10 /* B0 */))) && // 5.
      (AmdPprInfo->LogicalRanks >= 8))) { // 5. (continued)
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] %a returns FALSE\n", __FUNCTION__);
    return FALSE;
  } else {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] %a returns TRUE\n", __FUNCTION__);
    return TRUE;
  }
}

EFI_STATUS
PerformRuntimePpr (
  IN       DPPR_REPAIR_BLOCK_V3         *RtpprPspDataPtr,
  IN       UINT32                       RtpprPspDataSize
  )
{
  EFI_STATUS                  Status;
  RT_PPR_STRUCT               RtPprStruc;
  PING_PONG_SMM_BUFFER        *PingPongSmmBuffer;
  UINT8                       TmpPingPongIndex;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] %a - Entry \n", __FUNCTION__);

  if (!mRtPprEn) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS] %a - Runtime Post Package Repair is disabled.\n", __FUNCTION__);
    return EFI_SUCCESS;
  }

  if ((mPspMboxSmmBuffer == NULL) || (mPspMboxSmmFlagAddr == NULL)) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS] %a - Missing PSP Mbox SMM parameters.\n", __FUNCTION__);
    return EFI_NOT_FOUND;
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] Runtime PPR PSP Data@ 0x%016lx, Runtime PPR PSP Data size = 0x%08x.\n",
    (UINTN)RtpprPspDataPtr, RtpprPspDataSize);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] DpprEntry@ 0x%016lx, DpprEntrySize = 0x%08x.\n",
    (UINTN)&RtpprPspDataPtr->DpprClArray[0], sizeof(DPPR_REPAIR_ENTRY_V3));

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] NormalizedAddressHi = 0x%08x, NormalizedAddressLo = 0x%08x.\n",
    RtpprPspDataPtr->DpprClArray[0].ddr.extfield.rt.NormalizedAddressHi, RtpprPspDataPtr->DpprClArray[0].ddr.extfield.rt.NormalizedAddressLo);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] Send Runtime PPR command to PSP - Start.\n");

  RtPprStruc.Socketid         = RtpprPspDataPtr->DpprClArray[0].ddr.Socket;  //Todo: PSP FW may need to redefine this parameter.
  RtPprStruc.RtPprRawDataSize = RtpprPspDataSize;                            //Todo: PSP FW may need to increase its internal buffer to fully store RtpprPspDataPtr
  RtPprStruc.RtPprRawData     = (UINT8 *)RtpprPspDataPtr;

  //PingPongSmmBuffer:
  //  There are two PingPongSmmBuffers: PingPongSmmBuffer[0] and PingPongSmmBuffer[1].
  //  They are all located on 32-bit aligned memory base addresses.
  //    PingPongSmmBuffer[0] is located in mPspMboxSmmBuffer.
  //    PingPongSmmBuffer[1] is located in mPspMboxSmmBuffer + [sizeof (UNALIGNED_MBOX_BUFFER) + 32bit alignment padding].
  //
  //mPingPongIndex:
  //  This is used to select which PingPongSmmBuffer to use. Valid values: 0, 1.
  //    0 = PingPongSmmBuffer[0], 1 = PingPongSmmBuffer[1]
  //
  PingPongSmmBuffer = (PING_PONG_SMM_BUFFER *)mPspMboxSmmBuffer;
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] Current mPingPongIndex = %x.\n", mPingPongIndex);

  //Send Runtime PPR command to PSP. This is an Async command, with an SMI on completion.
  Status = PspMboxBiosRasExecuteRtPpr (&RtPprStruc, (UINT8 *)&PingPongSmmBuffer[mPingPongIndex], mPspMboxSmmFlagAddr);
  if (!EFI_ERROR(Status)) {
    //PspMboxBiosRasExecuteRtPpr has returned EFI_SUCCESS. Therefore, the value of mPingPongIndex needs to be changed.
    //  The PSP FW has accepted the MboxBiosCmdRasExecuteRtPpr command, so the PingPongSmmBuffer just passed will be read by PSP FW.
    //  The next time the MboxBiosCmdRasExecuteRtPpr command is issued, another PingPongSmmBuffer must be used to avoid a race condition that overwrites the buffer data being read by the PSP FW.
    TmpPingPongIndex  = ((++mPingPongIndex) % 2);
    mPingPongIndex = TmpPingPongIndex;
  }
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] Send Runtime PPR command to PSP - End. Status: %r. (Next mPingPongIndex = %x)\n", Status, mPingPongIndex);

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] %a - Exit \n", __FUNCTION__);

  return Status;
}

EFI_STATUS
RtPprAdjustErrCounter (
  IN       BOOLEAN       AdjustCntFlag,
  IN       UINTN         Index
  )
{
  EFI_STATUS                Status = EFI_SUCCESS;
  RAS_THRESHOLD_CONFIG      RasThresholdConfig;
  UINT8                     SegNum;
  UINT8                     BusNum;
  UINT16                    ChannelId;
  UMC_ECC_ERR_CNT_CTRL_REG  RasEccErrCntCtrlReg;

  if ((mPlatformApeiData->PlatRasPolicy.DdrEccErrorCounterEnable != 0) && (mPlatformApeiData->PlatRasPolicy.PFEHEnable == 1)) {
    //Program DdrEccErrorCounter
    //  AdjustCntFlag = TRUE:  Disable the Dram Ecc error counter to avoid accidentally triggering SMI when the scrubber is executed.
    //  AdjustCntFlag = FALSE: Enable the Dram error counter.
    AcquireIoHubSSecSegBusNum (mRbBusMap, mChkRtPprComplStruct[Index].CpuInfo.SocketId, 0, 0, &BusNum, &SegNum);
    ChannelId = McaInstanceIdSearch(mChkRtPprComplStruct[Index].McaIpid);

    //Program UMC[00...11]CHx00000D88 [DRAM ECC Error Count Control] (UMC::EccErrCntCtrl)
    RasEccErrCntCtrlReg.Value = 0;
    RasEccErrCntCtrlReg.Field.EccErrCntEn = AdjustCntFlag ? 0 /*Disable EccErrCnt*/ : 1 /*Enable EccErrCnt*/;
    RasSmnRW (
      SMN_SEG_BUS (SegNum, BusNum),
      SMN_UMC_EccErrCntCtrl_ADDRESS + (UMC_SMN_ADDR_OFFSET * ChannelId),
      ~(UINT32)(EccErrCntCtrl_EccErrCntEn_MASK),
      RasEccErrCntCtrlReg.Value
    );
    RasSmnRead (SMN_SEG_BUS (SegNum, BusNum), SMN_UMC_EccErrCntCtrl_ADDRESS + (UMC_SMN_ADDR_OFFSET * ChannelId), &(RasEccErrCntCtrlReg.Value));
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
      "[RAS Runtime PPR] %a AdjustCntFlag: 0x%02x, EccErrCntCtrl = (0x%08X:0x%08X)\n",
      __FUNCTION__,
      AdjustCntFlag,
      (SMN_UMC_EccErrCntCtrl_ADDRESS + (UMC_SMN_ADDR_OFFSET * ChannelId)),
      RasEccErrCntCtrlReg.Value
    );
  } else {
    //Program McaErrThreshCount
    //  AdjustCntFlag = TRUE:  Set the error counter to zero to avoid accidentally triggering SMI when the scrubber is executed.
    //  AdjustCntFlag = FALSE: Restore the error counter to the user default.
    RasThresholdConfig.ThresholdControl = mPlatformApeiData->PlatRasPolicy.McaErrThreshEn;
    RasThresholdConfig.ThresholdCount = (AdjustCntFlag) ? 0 : mPlatformApeiData->PlatRasPolicy.McaErrThreshCount;
    RasThresholdConfig.ThresholdIntType = (mSmiMode == INTERRUPT_MODE) ? MCA_SMI : MCA_NO_INTERRUPT;
    Status = mAmdRasServiceSmmProtocol->SetMcaThreshold(
                                          &mChkRtPprComplStruct[Index].CpuInfo.ProcessorNumber,
                                          &mChkRtPprComplStruct[Index].BankNumber,
                                          &RasThresholdConfig, FALSE);
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] %a AdjustCntFlag: 0x%02x, McaErrThreshCount: 0x%04x.\n",
      __FUNCTION__, AdjustCntFlag, RasThresholdConfig.ThresholdCount);
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] %a returns: %r\n", __FUNCTION__, Status);

  return Status;
}

VOID
RestoreRtPprVarsToDefault (
  VOID
  )
{
  mP2cRuntimePprStatus = 0;
  mPspRtPprComplFlag = FALSE;
  mMcaWrMsrComplFlag = FALSE;
  mReadyForNextRtPprReq = TRUE;
  mNumOfRtPptEntryInServed = 0;
  mQueueFront = 0;
  mQueueFull = 0;
  ZeroMem (mChkRtPprComplStruct, sizeof (mChkRtPprComplStruct));

  return;
}


BOOLEAN
IsRtPprRepairedSuccess (
    VOID
  )
{
  EFI_STATUS               Status;
  UINT8                    SegNum;
  UINT8                    BusNum;
  UINT16                   ChannelId;
  UINT32                   UmcRegBase;
  UINT32                   DramScrubCtrl;
  UINT64                   DramScrubAddr;
  UINT32                   DramScrubAddrLo;
  UINT32                   DramScrubAddrHi;
  UINT32                   TmpDramScrubCtrl;
  UINT32                   TmpDramScrubAddrLo;
  UINT32                   TmpDramScrubAddrHi;
  UINT64                   NormalizedAddr;
  UINT32                   McaExtensionAddrBase;
  PLAT_RAS_MSR_ACCESS      RasMsrAccess;
  BOOLEAN                  RepairResult[MAX_NUM_OF_DPPRCL] = {0};
  MCA_STATUS_MSR           McaStatusMsr;
  MCA_ADDR_MSR             McaAddrMsr;
  MCA_SYND_MSR             McaSyndMsr;
  DIMM_INFO                DimmInfo;
  UINT32                   CountdownInMicroseconds;
  UINTN                    HandleBufferSize = 0;
  UINTN                    Index = 0;
  UINT8                    FreeFlag = 0;
  EFI_HANDLE               *HandleBuffer = NULL;
  AMD_CPM_RAS_OEM_PROTOCOL *AmdRasOemProtocol = NULL;
  UINT8                    NumOfBABits;

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] %a - Entry\n", __FUNCTION__);

  if (!mRtPprEn) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS Runtime PPR] %a - EXIT. (Runtime PPR is disabled)\n");
    return FALSE;
  }

  if (!mPspRtPprComplFlag || !mMcaWrMsrComplFlag) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[RAS Runtime PPR] %a - EXIT. (mPspRtPprComplFlag: 0x%02x, mMcaWrMsrComplFlag: 0x%02x)\n",
    __FUNCTION__, mPspRtPprComplFlag, mMcaWrMsrComplFlag);
    return FALSE;
  }

  if (mP2cRuntimePprStatus != 0) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR]  %a - EXIT. (ABL returns repair has failed: 0x%08x => Skip verifying Runtim PPR repair results)\n",
      __FUNCTION__, mP2cRuntimePprStatus);

    //Restore Runtime PPR related variables to default values
    RestoreRtPprVarsToDefault ();

    return FALSE;
  }

  for (Index = 0; Index < mNumOfRtPptEntryInServed; Index++) {
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] [Entry: 0x%02X] Checking System Address: 0x%016lX. (Normalized Address: 0x%016lX)\n",
      Index, mChkRtPprComplStruct[Index].SystemMemoryAddress, mChkRtPprComplStruct[Index].NormalizedAddrInfo.normalizedAddr);

    AcquireIoHubSSecSegBusNum (mRbBusMap, mChkRtPprComplStruct[Index].NormalizedAddrInfo.normalizedSocketId, 0, 0, &BusNum, &SegNum);

    ChannelId = mChkRtPprComplStruct[Index].NormalizedAddrInfo.normalizedChannelId;
    UmcRegBase = (UMC0_CH_REG_BASE + (UMC_SMN_ADDR_OFFSET * ChannelId));

    //Save DramScrubAddr
    RasSmnRead (SMN_SEG_BUS (SegNum, BusNum), UmcRegBase + DRAM_SCRUBBER_ADDR_LO, &TmpDramScrubAddrLo);
    RasSmnRead (SMN_SEG_BUS (SegNum, BusNum), UmcRegBase + DRAM_SCRUBBER_ADDR_HI, &TmpDramScrubAddrHi);

    //Save TmpDramScrubCtrl
    RasSmnRead (SMN_SEG_BUS (SegNum, BusNum), UmcRegBase + DRAM_SCRUBBER_CONTROL_REG, &TmpDramScrubCtrl);
    if ((TmpDramScrubCtrl & BIT8) == 0) {
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] Patrol scrubber is disabled by default. (DramScrubCtrl@0x%08x: 0x%08x)\n",
        UmcRegBase + DRAM_SCRUBBER_CONTROL_REG, TmpDramScrubCtrl);
    } else {
      //Disable patrol scrubber
      RasSmnRead (SMN_SEG_BUS (SegNum, BusNum), UmcRegBase + DRAM_SCRUBBER_CONTROL_REG, &DramScrubCtrl);
      DramScrubCtrl &= ~(UINT32)BIT8;
      RasSmnWrite (SMN_SEG_BUS (SegNum, BusNum), UmcRegBase + DRAM_SCRUBBER_CONTROL_REG, &DramScrubCtrl);

      RasSmnRead (SMN_SEG_BUS (SegNum, BusNum), UmcRegBase + DRAM_SCRUBBER_CONTROL_REG, &DramScrubCtrl);
      if ((DramScrubCtrl & BIT8) != 0) {
        IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_ERROR, "[RAS Runtime PPR] Disable patrol scrubber: Failed. (DramScrubCtrl@0x%08x: 0x%08x)\n",
          UmcRegBase + DRAM_SCRUBBER_CONTROL_REG, DramScrubCtrl);
      }
    }

    //Calculate the normalized address for scrub starting address
    if (mChkRtPprComplStruct[Index].UmcExtErr == UMC_ECSROWERR) {
      //Dram RBC (Rank, Row, BankGroup, BankA=0, Column=0, SubCh=0) of the repair
      DimmInfo = mChkRtPprComplStruct[Index].RtPprDimmInfo;
      DimmInfo.Column = 0;
      DimmInfo.subchan = 0;

      //Set BankA to 0;
      NumOfBABits = GetNumOfBABits (mChkRtPprComplStruct[Index].CpuInfo.SocketId, ChannelId, DimmInfo.ChipSelect);
      DimmInfo.Bank &= (0xFF << NumOfBABits);

      mAmdRasServiceSmmProtocol->TranslateDramToNormAddr (
        mChkRtPprComplStruct[Index].CpuInfo.SocketId,
        mChkRtPprComplStruct[Index].CpuInfo.DieId,
        (UINT8)(ChannelId),
        &DimmInfo,
        &NormalizedAddr
        );
    } else {
      NormalizedAddr = mChkRtPprComplStruct[Index].NormalizedAddrInfo.normalizedAddr;
      if (NormalizedAddr > 0) {
        NormalizedAddr--;
      }
    }

    DramScrubAddrLo = (UINT32)(NormalizedAddr & 0xFFFFFFC0);
    DramScrubAddrHi = (UINT32)((NormalizedAddr >> 32) & 0xFFFFFFFF);
    RasSmnWrite (SMN_SEG_BUS (SegNum, BusNum), UmcRegBase + DRAM_SCRUBBER_ADDR_LO, &DramScrubAddrLo);
    RasSmnWrite (SMN_SEG_BUS (SegNum, BusNum), UmcRegBase + DRAM_SCRUBBER_ADDR_HI, &DramScrubAddrHi);
    IDS_HDT_CONSOLE_RAS_TRACE(
      RAS_TRACE_INFO, "[RAS Runtime PPR] Prepare to scrub from DramScrubAddrHi (SmnAddr: 0x%08X): 0x%08X, DramScrubAddrLo (SmnAddr: 0x%08X): 0x%08X\n",
      UmcRegBase + DRAM_SCRUBBER_ADDR_HI, DramScrubAddrHi, UmcRegBase + DRAM_SCRUBBER_ADDR_LO, DramScrubAddrLo
      );

    //Clear MCA Status before scrub
    mAmdRasServiceSmmProtocol->ClrMcaStatus (
                                 mChkRtPprComplStruct[Index].CpuInfo.ProcessorNumber,
                                 mChkRtPprComplStruct[Index].BankNumber,
                                 FALSE);

    //Set the error counter to zero to avoid accidentally triggering SMI when the scrubber is executed.
    RtPprAdjustErrCounter (TRUE, Index);

    //Enable patrol scrubber
    RasSmnRead (SMN_SEG_BUS (SegNum, BusNum), UmcRegBase + DRAM_SCRUBBER_CONTROL_REG, &DramScrubCtrl);
    DramScrubCtrl |= (UINT32)BIT8;
    RasSmnWrite (SMN_SEG_BUS (SegNum, BusNum), UmcRegBase + DRAM_SCRUBBER_CONTROL_REG, &DramScrubCtrl);

    RasSmnRead (SMN_SEG_BUS (SegNum, BusNum), UmcRegBase + DRAM_SCRUBBER_CONTROL_REG, &DramScrubCtrl);
    if ((DramScrubCtrl & BIT8) == 0) {
      IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_ERROR, "[RAS Runtime PPR] Enable patrol scrubber: Failed. (DramScrubCtrl@0x%08x: 0x%08x)\n",
        UmcRegBase + DRAM_SCRUBBER_CONTROL_REG, DramScrubCtrl);
    } else {
      //Check if the repaired memory has been scrubbered
      DramScrubAddrLo = 0;
      DramScrubAddrHi = 0;
      CountdownInMicroseconds = 200; //200 us
      do {
        RasSmnRead (SMN_SEG_BUS (SegNum, BusNum), UmcRegBase + DRAM_SCRUBBER_ADDR_LO, &DramScrubAddrLo);
        RasSmnRead (SMN_SEG_BUS (SegNum, BusNum), UmcRegBase + DRAM_SCRUBBER_ADDR_HI, &DramScrubAddrHi);
        DramScrubAddr  = ((UINT64)DramScrubAddrHi) << 32;
        DramScrubAddr |= DramScrubAddrLo;

        //sleep 1 microsecond
        MicroSecondDelay(1);
        if ((CountdownInMicroseconds--) == 0) {
          IDS_HDT_CONSOLE_RAS_TRACE (RAS_TRACE_ERROR, "[RAS Runtime PPR] Timeout while waiting for scrubbing done.\n");
          break;
        }
      } while (DramScrubAddr <= (mChkRtPprComplStruct[Index].NormalizedAddrInfo.normalizedAddr));
    }

    //Check MCA STATUS after scrubbered
    McaExtensionAddrBase = (UINT32)(MCA_EXTENSION_BASE + (mChkRtPprComplStruct[Index].BankNumber * SMCA_REG_PER_BANK));
    RasMsrAccess.RegisterAddress = McaExtensionAddrBase | MCA_STATUS_OFFSET;
    RasMsrAccess.IsWrite = FALSE;
    RasMsrAccess.RegisterValue = 0;
    MpRegisterAccess (mChkRtPprComplStruct[Index].CpuInfo.ProcessorNumber,&RasMsrAccess);
    McaStatusMsr.Value = RasMsrAccess.RegisterValue;

    RepairResult[Index] = TRUE;  //TRUE: Repair Passed, FALSE: Repair Failed
    if (McaStatusMsr.Value != 0) {
      RepairResult[Index] = FALSE;

      //Read MCA_ADDR
      RasMsrAccess.RegisterAddress = McaExtensionAddrBase | MCA_ADDR_OFFSET;
      RasMsrAccess.IsWrite = FALSE;
      RasMsrAccess.RegisterValue = 0;
      MpRegisterAccess (mChkRtPprComplStruct[Index].CpuInfo.ProcessorNumber,&RasMsrAccess);
      McaAddrMsr.Value = RasMsrAccess.RegisterValue;

      //Read MCA_SYND
      RasMsrAccess.RegisterAddress = McaExtensionAddrBase | MCA_SYND_OFFSET;
      RasMsrAccess.IsWrite = FALSE;
      RasMsrAccess.RegisterValue = 0;
      MpRegisterAccess (mChkRtPprComplStruct[Index].CpuInfo.ProcessorNumber,&RasMsrAccess);
      McaSyndMsr.Value = RasMsrAccess.RegisterValue;

      //Repair Failed
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR]    MCA_STATUS: 0x%016lX\n", McaStatusMsr.Value);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR]    MCA_ADDR  : 0x%016lX\n", McaAddrMsr.Value);
      IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR]    MCA_SYND  : 0x%016lX\n", McaSyndMsr.Value);
    }

    //Dump the validation result(s) of the Runtime PPR.
    IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR]    Scrubbing Done.  Runtime PPR repair result: %a.\n", (RepairResult[Index])? "Passed" : "Failed");

    //Disable patrol scrubber
    RasSmnRead (SMN_SEG_BUS (SegNum, BusNum), UmcRegBase + DRAM_SCRUBBER_CONTROL_REG, &DramScrubCtrl);
    DramScrubCtrl &= ~(UINT32)BIT8;
    RasSmnWrite (SMN_SEG_BUS (SegNum, BusNum), UmcRegBase + DRAM_SCRUBBER_CONTROL_REG, &DramScrubCtrl);

    //Restore DramScrubAddr
    RasSmnWrite (SMN_SEG_BUS (SegNum, BusNum), UmcRegBase + DRAM_SCRUBBER_ADDR_LO, &TmpDramScrubAddrLo);
    RasSmnWrite (SMN_SEG_BUS (SegNum, BusNum), UmcRegBase + DRAM_SCRUBBER_ADDR_HI, &TmpDramScrubAddrHi);

    if (!RepairResult[Index]) {
      //Clear MCA Status if error is still found,
      mAmdRasServiceSmmProtocol->ClrMcaStatus (
                                   mChkRtPprComplStruct[Index].CpuInfo.ProcessorNumber,
                                   mChkRtPprComplStruct[Index].BankNumber,
                                   FALSE);
    }

    //Restore the error counter to the user default.
    RtPprAdjustErrCounter (FALSE, Index);

    //Enable patrol scrubber
    //RasSmnRead (SMN_SEG_BUS (SegNum, BusNum), UmcRegBase + DRAM_SCRUBBER_CONTROL_REG, &DramScrubCtrl);
    //DramScrubCtrl |= (UINT32)BIT8;

    //Restore DramScrubCtrl
    RasSmnWrite (SMN_SEG_BUS (SegNum, BusNum), UmcRegBase + DRAM_SCRUBBER_CONTROL_REG, &TmpDramScrubCtrl);
  }

  //Locate Ras Oem Protocol
  Status = gSmst->SmmLocateHandle (
                              ByProtocol,
                              &gAmdCpmRasOemSmmProtocolGuid,
                              NULL,
                              &HandleBufferSize,
                              HandleBuffer);
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] SmmLocateHandle gAmdCpmRasOemSmmProtocolGuid Status =%r\n", Status);

  if (EFI_ERROR(Status)) {
    if (Status == EFI_BUFFER_TOO_SMALL) {
      HandleBuffer = AllocateRuntimePool (HandleBufferSize);
      if (HandleBuffer != NULL) {
        Status = gSmst->SmmLocateHandle (
                    ByProtocol,
                    &gAmdCpmRasOemSmmProtocolGuid,
                    NULL,
                    &HandleBufferSize,
                    HandleBuffer);
        if (!EFI_ERROR(Status))
          FreeFlag = 1;
      }
    }
  }

  if (!EFI_ERROR(Status)) {
    if (HandleBuffer != NULL) {
      for (Index = 0; Index < (HandleBufferSize / sizeof(EFI_HANDLE)); Index++) {
        Status = gSmst->SmmHandleProtocol (
                                  HandleBuffer[Index],
                                  &gAmdCpmRasOemSmmProtocolGuid,
                                  (VOID **)&AmdRasOemProtocol);
        if(!EFI_ERROR(Status)){
          AmdRasOemProtocol->OemErrorLogEventRtPpr (0, mChkRtPprComplStruct, RepairResult, mNumOfRtPptEntryInServed);
        }
      }
    }
  }

  if ((FreeFlag == 1) && (HandleBuffer != NULL)){
    Status = gSmst->SmmFreePool (HandleBuffer);
    ASSERT_EFI_ERROR (Status);
  }

  //Restore Runtime PPR related variables to default values
  RestoreRtPprVarsToDefault ();

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS Runtime PPR] %a - Exit\n", __FUNCTION__);

  return TRUE;  //TRUE: All RTPPR entries have been checked.
}

BOOLEAN
GetPspBiosMboxRegAddr (
  IN       UINT8    DfNode,
  IN OUT   VOID     **PspBiosMboxReg
  )
{
  EFI_STATUS        Status;
  UINT8             IohcSegNum;
  UINT8             IohcBusNum;
  UINT32            PspMmioBase;

  Status = AcquireIoHubSSecSegBusNum (mRbBusMap, DfNode, 0, 0, &IohcBusNum, &IohcSegNum);  //Param1: 0 = Socket0, 1= Socket1; Param2: 0 = NBIO0_IOHC0;
  if (EFI_ERROR(Status)) {
    return FALSE;
  }

  PspMmioBase = 0;
  RasSmnRead (SMN_SEG_BUS (IohcSegNum, IohcBusNum), IOHC_MSIC0_SMN_BASE + IOHC_MSICX_PSP_BASE_ADDR_LO_OFFSET, &PspMmioBase);  //PspMmioBase is currently under 4G
  //Mask out the lower bits
  PspMmioBase &= 0xFFF00000;
  if (PspMmioBase == 0) {
    return FALSE;
  }
  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[RAS PSP] %a - PSP_BASE_ADDR_LO: 0x%08x\n", __FUNCTION__, (UINT32)PspMmioBase);

  *PspBiosMboxReg = (VOID *)(UINTN)(PspMmioBase + C2PMSG_0_BASE + C2PMSG_28_OFFSET);

  return TRUE;
}

BOOLEAN
ParsingEcsStatusArray (
  IN     AMD_POST_PACKAGE_REPAIR_INFO_PROTOCOL  *AmdPostPackageRepairInfo,
  IN     AMD_APCB_SERVICE_PROTOCOL              *AmdApcbService,
  IN     CPU_INFO                               CpuInfo,
  IN     UINT8                                  ChannelId,
  IN     UINT8                                  DeviceType,
  IN     BOOLEAN                                *AtLeastOneRepair,
  IN OUT DPPR_REPAIR_BLOCK_V3                   *EcsRtPprPspData,
     OUT NORMALIZED_ADDRESS                     *EcsNormalizedAddress,
     OUT UINT64                                 *EcsSystemMemoryAddress,
     OUT DIMM_INFO                              *EcsDimmInfo
  )
{
  EFI_STATUS            Status;
  UINT32                EcsErrCnt;
  DPPR_REPAIR_ENTRY_V3  DpprEntry[MAX_NUM_OF_DPPRCL];
  UINT8                 SocketId;
  UINT8                 SegNum;
  UINT8                 BusNum;
  UINT32                ChipSel;
  UINT32                SubChn;
  UINT32                UmcRegBase;
  UINT32                ECSStatus;
  UINT8                 DevID;
  UINT8                 CID;
  UINT32                Row;
  UINT8                 BA;
  UINT8                 BG;
  UINT8                 NumOfBABits;
  UINT8                 Bank;
  AMD_DIMM_INFO         AmdDimmInfo;
  AMD_PPR_INFO          AmdPprInfo;
  DIMM_INFO             DimmInfo;
  UINT8                 SerialNumberString[4];

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a - Entry\n", __FUNCTION__);

  ZeroMem (DpprEntry, sizeof (DpprEntry));

  SocketId  = CpuInfo.SocketId;

  //Get IOHC bus number
  AcquireIoHubSSecSegBusNum (mRbBusMap, SocketId, 0, 0, &BusNum, &SegNum);

  EcsErrCnt = 0;
  //Get UMC0CHx00000000...UMC11CHx0000000C [DRAM CS Base Address] (UMC::CH::BaseAddr)
  UmcRegBase = (UMC0_CH_REG_BASE + (UMC_SMN_ADDR_OFFSET * ChannelId));
  for (ChipSel = 0; ChipSel < 4; ++ChipSel) {
    for (SubChn = 0; SubChn < 2; ++SubChn) {
      //UMC::ECSStatusSel[3:2] = ChipSel;
      //UMC::ECSStatusSel[4] = SubChn;
      //UMC::ECSStatusSel[1:0] = 0; // Register type = Error Count Status
      RasSmnRW (SMN_SEG_BUS (SegNum, BusNum),
                (UmcRegBase + UMC_ECS_STATUS_SEL),
                ~(UINT32)(0x3F),
                (((ChipSel & 0x3) << 2) | ((SubChn & 0x1) << 4)));

      // NOTE: EcsRowErr (MR19) logs include a valid Row Address (MR16-18),
      // whereas EcsErr (MR20) logs may not include a valid Row Address in
      // some DIMM design implementations.
      //
      // Is there an EcsRowErr (MR19) log?
      RasSmnRead (SMN_SEG_BUS (SegNum, BusNum), (UmcRegBase + UMC_ECS_STATUS), &ECSStatus);
      if ((ECSStatus & 0xFF) != 0) { //UMC::ECSStatus[7:0] = Errors Per Row Count (EpRC) from MR19 [5:0];
        // Capture DevID and CID
        if (DeviceType == DEVICE_WIDTH_x4) {
          DevID = ((ECSStatus >> 8) & 0xF); //UMC::ECSStatus[11:8];
        } else {
          DevID = ((ECSStatus >> 9) & 0x7);//UMC::ECSStatus[11:9];
        }
        CID = ((ECSStatus >> 12) & 0xF);// UMC::ECSStatus[15:12];

        // Capture the Row Address information from MR16-18
        //UMC::ECSStatusSel[1:0] = 1; // Register type = Error Row Address
        RasSmnRW (SMN_SEG_BUS (SegNum, BusNum),
                  (UmcRegBase + UMC_ECS_STATUS_SEL),
                  ~(UINT32)(0x3F),
                  (((ChipSel & 0x3) << 2) | ((SubChn & 0x1) << 4) | BIT0));
        RasSmnRead (SMN_SEG_BUS (SegNum, BusNum), (UmcRegBase + UMC_ECS_STATUS), &ECSStatus);
        Row = (ECSStatus & 0x3FFFF);    //UMC::ECSStatus[17:0];
        BA = ((ECSStatus >> 18) & 0x3); //UMC::ECSStatus[19:18];
        BG = ((ECSStatus >> 20) & 0x7); //UMC::ECSStatus[22:20];

        //Calculate bank number
        NumOfBABits = GetNumOfBABits (SocketId, (UINT16)ChannelId, (UINT8)ChipSel);
        if (NumOfBABits == 0xFF) {
          continue;
        }
        Bank = (UINT8)(((UINT32)BG << NumOfBABits) | BA);

        //Build Post Package repair entry
        DpprEntry[EcsErrCnt].ddr.Bank = Bank;
        DpprEntry[EcsErrCnt].ddr.Row = Row;
        DpprEntry[EcsErrCnt].ddr.Column = 0;
        DpprEntry[EcsErrCnt].ddr.RankMultiplier = CID;
        DpprEntry[EcsErrCnt].ddr.ChipSelect = ChipSel;
        DpprEntry[EcsErrCnt].ddr.SubChannel = SubChn;
        DpprEntry[EcsErrCnt].ddr.Socket = SocketId;
        DpprEntry[EcsErrCnt].ddr.Channel = ChannelId;
        DpprEntry[EcsErrCnt].ddr.Device = DeviceType;
        DpprEntry[EcsErrCnt].ddr.TargetDevice = DevID;
        DpprEntry[EcsErrCnt].ddr.DeviceTypeToRepair = 0;  /// This device type is DDR. Refer ApcbCommon.h for detail
        DpprEntry[EcsErrCnt].ddr.ErrorCause = (mSmiMode == POLLING_MODE) ? PPR_CECC_STORM : PPR_ECC;  //Todo ECS?

        if (mPlatformApeiData->PlatRasPolicy.RuntimePprEnableOnly) {
          DpprEntry[EcsErrCnt].ddr.RepairType = DRAM_POST_PKG_HARD_REPAIR;
          DpprEntry[EcsErrCnt].ddr.HardPPRDone = TRUE;
        } else if (mPlatformApeiData->PlatRasPolicy.HardPprEnable) {
          DpprEntry[EcsErrCnt].ddr.RepairType = DRAM_POST_PKG_HARD_REPAIR;
        } else {
          DpprEntry[EcsErrCnt].ddr.RepairType = DRAM_POST_PKG_SOFT_REPAIR;
        }
        //Obtain SerialNumber of this ECS error.
        AmdDimmInfo.SocketId = SocketId;
        AmdDimmInfo.DieId = CpuInfo.DieId;
        AmdDimmInfo.ChannelId = ChannelId;
        AmdDimmInfo.Chipselect = (UINT8)ChipSel;
        Status = AmdPostPackageRepairInfo->AmdGetPprInfo (AmdPostPackageRepairInfo, &AmdDimmInfo, &AmdPprInfo);
        if (!EFI_ERROR(Status)) {
          DpprEntry[EcsErrCnt].ddr.extfield.bt.SerialNumberLoWord = AmdPprInfo.SerialNumber;
          DpprEntry[EcsErrCnt].ddr.extfield.bt.SerialNumberHiWord = 0;
        }
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Bank = 0x%X\n", DpprEntry[EcsErrCnt].ddr.Bank);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Row = 0x%X\n", DpprEntry[EcsErrCnt].ddr.Row);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Column = 0x%X\n", DpprEntry[EcsErrCnt].ddr.Column);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Rank Multiplier = 0x%X\n", DpprEntry[EcsErrCnt].ddr.RankMultiplier);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Chipselect = 0x%X\n", DpprEntry[EcsErrCnt].ddr.ChipSelect);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "SubChannel = 0x%X\n", DpprEntry[EcsErrCnt].ddr.SubChannel);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Socket = 0x%X\n", DpprEntry[EcsErrCnt].ddr.Socket);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Channel = 0x%X\n", DpprEntry[EcsErrCnt].ddr.Channel);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "Device = 0x%X\n", DpprEntry[EcsErrCnt].ddr.Device);
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "TargetDevice = 0x%X\n", DpprEntry[EcsErrCnt].ddr.TargetDevice);
        *(UINT32 *)&(SerialNumberString[0]) = DpprEntry[EcsErrCnt].ddr.extfield.bt.SerialNumberLoWord;
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "SerialNumber = %02X%02X%02X%02X\n", SerialNumberString[0], SerialNumberString[1], SerialNumberString[2], SerialNumberString[3]);

        //Insert Post Package repair entry
        Status = AmdApcbService->ApcbAddDramPostPkgRepairEntryEx (
            AmdApcbService,
            DPPR_REPAIR_ENTRY_STRUCT_VERSION_V3,
            &DpprEntry[EcsErrCnt]
            );
        IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "[%a]ApcbAddDramPostPkgRepairEntryEx: %r\n", __FUNCTION__, Status);
        if (!EFI_ERROR (Status)) {
          *AtLeastOneRepair = TRUE;
        } else {
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "[%a]Failed Inserting DIMM Post Package Repair Entry:\n", __FUNCTION__);
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_ERROR, "DimmPostPackageRepair: Status=%r\n", Status);
        }

        // [Runtime PPR]
        if (EcsRtPprPspData != NULL) {
          EcsRtPprPspData->RepairEntries = (EcsErrCnt + 1);
          CopyMem (EcsRtPprPspData->DpprClArray, &DpprEntry[EcsErrCnt], sizeof (DPPR_REPAIR_ENTRY_V3));

          //Buid DIMM_INFO and obtain NormalizedAddress of this ECS error.
          ZeroMem (&DimmInfo, sizeof (DIMM_INFO));
          DimmInfo.ChipSelect = (UINT8)ChipSel;
          DimmInfo.Row = Row;
          DimmInfo.Column = 0;
          DimmInfo.rankmul = CID;
          DimmInfo.subchan = (UINT8)SubChn;
          DimmInfo.Bank = Bank;
          EcsDimmInfo[EcsErrCnt] = DimmInfo;
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO,
            "[%a - RtPPR] Bank = 0x%X, Row = 0x%X, Column = 0x%X, Rank Multiplier = 0x%X, Chipselect = 0x%X, SubChannel = 0x%X, Socket = 0x%X, Channel = 0x%X\n",
            __FUNCTION__, DimmInfo.Bank, DimmInfo.Row, DimmInfo.Column, DimmInfo.rankmul, DimmInfo.ChipSelect, DimmInfo.subchan, SocketId, ChannelId);
          Status = mAmdRasServiceSmmProtocol->TranslateDramToNormAddr (SocketId, 0, ChannelId, &DimmInfo, &EcsNormalizedAddress[EcsErrCnt].normalizedAddr);
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "=> DramAddr To NormAddr: %r\n", Status);
          if (!EFI_ERROR (Status)) {
            EcsNormalizedAddress[EcsErrCnt].normalizedSocketId = SocketId;
            EcsNormalizedAddress[EcsErrCnt].normalizedDieId = CpuInfo.DieId;
            EcsNormalizedAddress[EcsErrCnt].normalizedChannelId = (ChannelId);
            IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "=> NormalizedAddres = 0x%16lX\n", EcsNormalizedAddress[EcsErrCnt].normalizedAddr);
          }

          //Obtain SystemMemoryAddress of this ECS error.
          ZeroMem (&DimmInfo, sizeof (DIMM_INFO));
          EcsSystemMemoryAddress[EcsErrCnt] = 0;
          Status = mAmdRasServiceSmmProtocol->McaErrorAddrTranslate (
              &EcsNormalizedAddress[EcsErrCnt],
              &EcsSystemMemoryAddress[EcsErrCnt],
              &DimmInfo  //For ECSROWERR, DimmInfo here should be equal to DimmInfo derived from TranslateDramToNormAddr service.
              );
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "=> NormAddr to SystemAddr: %r\n", Status);
          IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "=> SystemMemoryAddress = 0x%16lX\n", EcsSystemMemoryAddress[EcsErrCnt]);
        }

        if (!mPlatformApeiData->PlatRasPolicy.CpmGhesAssistEnable) {
          //Clear EcsRowErr (MR19) log
          //UMC::ECSStatusSel[3:2] = ChipSel;
          //UMC::ECSStatusSel[4] = SubChn;
          //UMC::ECSStatusSel[1:0] = 0; // Register type = Error Count Status
          RasSmnRW (SMN_SEG_BUS (SegNum, BusNum),
                    (UmcRegBase + UMC_ECS_STATUS_SEL),
                    ~(UINT32)(0x3F),
                    (((ChipSel & 0x3) << 2) | ((SubChn & 0x1) << 4)));
          //UMC::ECSStatus[7:0] = Errors Per Row Count (EpRC) from MR19 [5:0];
          RasSmnRW (SMN_SEG_BUS (SegNum, BusNum), (UmcRegBase + UMC_ECS_STATUS), ~(UINT32)(0xFF), 0);
        }

        EcsErrCnt++;
      }
    }
  }

  IDS_HDT_CONSOLE_RAS_TRACE(RAS_TRACE_INFO, "%a - Exit. EcsErrCnt = %d \n", __FUNCTION__, EcsErrCnt);

  return (EcsErrCnt != 0);  //True: ECC Error Found, False: No ECC Error Found
}
