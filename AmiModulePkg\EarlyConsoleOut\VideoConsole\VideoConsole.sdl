TOKEN
    Name  = "VideoTextConsole_SUPPORT"
    Value  = "1"
    Help  = "Main switch to enable Video Text Console support in Project"
    TokenType = Boolean
    Master = Yes
    TargetH = Yes
    Token = "EarlyTextConsole_SUPPORT" "=" "1"
End

INFComponent
    Name  = "PeiVideoTextOut"
    File  = "PeiVideoTextOut.inf"
    Package  = "AmiModulePkg"
    ModuleTypes  = "PEIM"
    PreProcess = Yes
End

FFS_FILE
    Name  = "PeiVideoTextOut"
    FD_AREA  = "FV_BB"
    INFComponent  = "AmiModulePkg.PeiVideoTextOut"
End

INFComponent
    Name  = "DxeVideoTextOut"
    File  = "DxeVideoTextOut.inf"
    Package  = "AmiModulePkg"
    ModuleTypes  = "DXE_DRIVER"
End

INFComponent
    Name  = "VideoTextOutLib"
    File  = "VideoTextOutLib.inf"
    Package  = "AmiModulePkg"
    ModuleTypes  = "PEIM DXE_DRIVER"
End

LibraryMapping
    Class  = "VideoTextOutLib"
    Instance  = "AmiModulePkg.VideoTextOutLib"
    ModuleTypes  = "PEIM DXE_DRIVER"
End

TOKEN
    Name  = "PEI_VIDEO_CALLBACK_PACKAGE_DEC"
    Value  = " "
    Help  = "Token to add PeiVideoText CallBack Guid Package."
    TokenType = Expression
    TargetH = Yes
End

TOKEN
    Name  = "PEI_VIDEO_CALLBACK_GUID"
    Value  = "gEfiPeiMemoryDiscoveredPpiGuid"
    Help  = "Token to add PeiVideoText CallBack Guid."
    TokenType = Expression
    TargetH = Yes
End