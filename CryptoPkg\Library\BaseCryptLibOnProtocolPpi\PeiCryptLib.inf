#***********************************************************************
#*                                                                     *
#*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
#*                                                                     *
#*      All rights reserved. Subject to AMI licensing agreement.       *
#*                                                                     *
#***********************************************************************
## @file
# Implements the BaseCryptLib and TlsLib using the services of the EDK II Crypto
# PPI.
#
# Copyright (C) Microsoft Corporation. All rights reserved.
# Copyright (c) 2020, Hewlett Packard Enterprise Development LP. All rights reserved.<BR>
# Copyright (c) 2022, Loongson Technology Corporation Limited. All rights reserved.<BR>
# SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x0001001B
  BASE_NAME                      = PeiCryptLib
  MODULE_UNI_FILE                = CryptLib.uni
  FILE_GUID                      = 3E8B50C6-F68C-4212-B903-94A10FE02399
  VERSION_STRING                 = 1.0
  MODULE_TYPE                    = PEIM
  LIBRARY_CLASS                  = BaseCryptLib | PEIM
  LIBRARY_CLASS                  = TlsLib       | PEIM

#
# The following information is for reference only and not required by the build tools.
#
# VALID_ARCHITECTURES = IA32 X64 ARM AARCH64 RISCV64 LOONGARCH64
#

[Packages]
  MdePkg/MdePkg.dec
  CryptoPkg/CryptoPkg.dec

[LibraryClasses]
  BaseLib
  DebugLib
  PeiServicesLib
  IntrinsicLib  //APTIOV OVERRIDE - To avoid build error unresolved external symbol _memset

[Sources]
  PeiCryptLib.c
  CryptLib.c

[Ppis]
  gEdkiiCryptoPpiGuid  ## CONSUMES

[Depex]
  gEdkiiCryptoPpiGuid
