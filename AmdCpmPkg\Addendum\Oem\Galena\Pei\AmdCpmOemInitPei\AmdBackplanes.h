/*****************************************************************************
 * Copyright (C) 2015-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * AMD CPM OEM API, and related functions.
 *
 * Contains the definitions for AMD backplanes.
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      CPM
 * @e sub-project:  OEM
 * @e \$Revision: 270275 $   @e \$Date: 2013-08-09 03:54:44 +0800 (Fri, 09 Aug 2013) $
 *
 */

#ifndef _AMD_BACKPLANES_H_
#define _AMD_BACKPLANES_H_

// Ancillary Data speed override data format
//Bits[00:07] Bit0:Enable C0, Bit1:Enable Cn, Bit2:Enable Cp
//    [08:15] C0 override value
//    [16:23] Cn override value
//    [24:31] Cp override value
ANC_DATA_PARAM   CommonSataPortSpeedOvrdP0[] = {
  {SPD_OVRD, 0x00001807},       //Gen1: (cp/cn/c0 = 0/0/0x18)
  {SPD_OVRD, 0x00001807},       //Gen2: (cp/cn/c0 = 0/0/0x18)
  {SPD_OVRD, 0x08001E07}        //Gen3: (cp/cn/c0 = 0x8/0/0x1E)
};

ANC_DATA_PARAM   CommonSataPortSpeedOvrdG3[] = {
  {SPD_OVRD, 0x00001807},       //Gen1: (cp/cn/c0 = 0/0/0x18)
  {SPD_OVRD, 0x00001807},       //Gen2: (cp/cn/c0 = 0/0/0x18)
  {SPD_OVRD, 0x08001E07}        //Gen3: (cp/cn/c0 = 0x8/0/0x1E)
};

DXIO_PORT_DESCRIPTOR    GalenaAmberGLinksRevA[] = {
    { // G0 - x16 MCEIO slot
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 111, 96, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    { // G1 - x16 MCEIO slot
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 79, 64, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    { // G2 - x16 MCEIO slot
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 127, 112, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    { // G3 - x16 MCEIO slot
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 95, 80, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerExpress),
      PORT_PARAMS_END
    }
  };   // End of DXIO_PORT_DESCRIPTOR

ADDIN_CARD_PORTS GalenaAmberGLinksRevAEntry = {
    0xFF,
    4,
    &GalenaAmberGLinksRevA[0]
};

DXIO_PORT_DESCRIPTOR    GalenaAmberRetimerGLinksRevA[] = {
    { // G1 - x16 MCEIO slot 1
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 79, 64, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    { // G0 - x16 MCEIO slot 2
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 111, 96, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    { // G3 - x16 MCEIO slot 3
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 95, 80, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    { // G2 - x16 MCEIO slot 4
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 127, 112, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerExpress),
      PORT_PARAMS_END
    }
  };   // End of DXIO_PORT_DESCRIPTOR

ADDIN_CARD_PORTS GalenaAmberRetimerGLinksRevAEntry = {
    0xFF,
    4,
    &GalenaAmberRetimerGLinksRevA[0]
};

DXIO_PORT_DESCRIPTOR    GalenaAmberPLinksRevA[] = {
    { // P0 - x16 MCEIO slot
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 15, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerExpress),
      PORT_PARAMS_END
    },
    { // P1 - x16 MCEIO slot
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 47, 32, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    { // P2 - x16 MCEIO slot 2
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 63, 48, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    { // P3 - x16 MCEIO slot 1
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 31, 16, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
  };   // End of DXIO_PORT_DESCRIPTOR

ADDIN_CARD_PORTS GalenaAmberPLinksRevAEntry = {
    0xFF,
    4,
    &GalenaAmberPLinksRevA[0]
};

DXIO_PORT_DESCRIPTOR    GalenaAmberRetimerPLinksRevA[] = {
    { // P1 - x16 MCEIO slot 4
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 47, 32, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerExpress),
      PORT_PARAMS_END
    },
    { // P0 - x16 MCEIO slot 3
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 15, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    { // P3 - x16 MCEIO slot 2
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 31, 16, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    { // P2 - x16 MCEIO slot 1
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 63, 48, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
  };   // End of DXIO_PORT_DESCRIPTOR

ADDIN_CARD_PORTS GalenaAmberRetimerPLinksRevAEntry = {
    0xFF,
    4,
    &GalenaAmberRetimerPLinksRevA[0]
};

DXIO_PORT_DESCRIPTOR    GalenaAmberGLinksRevB[] = {
    { // G0 - x16 MCEIO slot
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 111, 96, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    { // G1 - x16 MCEIO slot
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 79, 64, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    { // G2 - x16 MCEIO slot
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 127, 112, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerExpress),
      PORT_PARAMS_END
    },
    { // G3 - x16 MCEIO slot
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 95, 80, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    }
  };   // End of DXIO_PORT_DESCRIPTOR

ADDIN_CARD_PORTS GalenaAmberGLinksRevBEntry = {
    0xFF,
    4,
    &GalenaAmberGLinksRevB[0]
};


DXIO_PORT_DESCRIPTOR    GalenaAmberPLinksRevB[] = {
    { // P0 - x16 MCEIO slot
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 15, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    { // P1 - x16 MCEIO slot
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 47, 32, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerExpress),
      PORT_PARAMS_END
    },
    { // P2 - x16 MCEIO slot
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 63, 48, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmDisabled),
      PORT_PARAMS_END
    },
    { // P3 - x16 MCEIO slot
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 31, 16, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
  };   // End of DXIO_PORT_DESCRIPTOR

ADDIN_CARD_PORTS GalenaAmberPLinksRevBEntry = {
    0xFF,
    4,
    &GalenaAmberPLinksRevB[0]
};


DXIO_PORT_DESCRIPTOR    GalenaU2UBMGLinks[] = {
    { // UBM backplane HFC 0 - G3
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 80, 95, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 80),
        PORT_PARAM (PP_SLOT_NUM, 0x30),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x24),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545)
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 1 - G2
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 112, 127, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 112),
        PORT_PARAM (PP_SLOT_NUM, 0x30),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x24),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545)
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 2 - G1
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 64, 79, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 64),
        PORT_PARAM (PP_SLOT_NUM, 0x30),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x24),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545)
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 3 - G0
      DESCRIPTOR_TERMINATE_LIST,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 96, 111, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 96),
        PORT_PARAM (PP_SLOT_NUM, 0x30),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x24),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545)
      PORT_PARAMS_END
    }
};   // End of DXIO_PORT_DESCRIPTOR

ADDIN_CARD_PORTS GalenaU2UBMGLinksEntry = {
    0xFF,
    4,
    &GalenaU2UBMGLinks[0]
};


DXIO_PORT_DESCRIPTOR    GalenaU2UBMPLinks[] = {
    { // UBM backplane HFC 0 - P0
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 0, 15, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 0),
        PORT_PARAM (PP_SLOT_NUM, 0x40),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x24),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545)
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 1 - P1
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 32, 47, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 32),
        PORT_PARAM (PP_SLOT_NUM, 0x40),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x24),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545)
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 2 - P2
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 48, 63, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 48),
        PORT_PARAM (PP_SLOT_NUM, 0x40),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x24),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545)
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 3 - P3
      DESCRIPTOR_TERMINATE_LIST,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 16, 31, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 16),
        PORT_PARAM (PP_SLOT_NUM, 0x40),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x24),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545)
      PORT_PARAMS_END
    }
};   // End of DXIO_PORT_DESCRIPTOR

ADDIN_CARD_PORTS GalenaU2UBMPLinksEntry = {
    0xFF,
    4,
    &GalenaU2UBMPLinks[0]
};


DXIO_PORT_DESCRIPTOR    GalenaU2UBMx8PLinks[] = {
    { // UBM backplane HFC 0 - P0
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 0, 15, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 0),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x24),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x72),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545)
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 1 - P1
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 32, 47, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 32),
        PORT_PARAM (PP_SLOT_NUM, 0x24),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x24),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x72),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545)
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 2 - P2
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 48, 63, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 48),
        PORT_PARAM (PP_SLOT_NUM, 0x28),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x24),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x72),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545)
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 3 - P3
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 16, 31, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 16),
        PORT_PARAM (PP_SLOT_NUM, 0x2C),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x24),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x72),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545)
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 0 - G3
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 80, 95, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 80),
        PORT_PARAM (PP_SLOT_NUM, 0x10),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x24),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545)
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 1 - G2
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 112, 127, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 112),
        PORT_PARAM (PP_SLOT_NUM, 0x14),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x24),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545)
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 2 - G1
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 64, 79, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 64),
        PORT_PARAM (PP_SLOT_NUM, 0x18),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x24),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545)
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 3 - G0
      DESCRIPTOR_TERMINATE_LIST,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 96, 111, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 96),
        PORT_PARAM (PP_SLOT_NUM, 0x1C),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x24),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_I2C_CLEAR_ALL_INTS, TRUE),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x73),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545)
      PORT_PARAMS_END
    }
};   // End of DXIO_PORT_DESCRIPTOR

ADDIN_CARD_PORTS GalenaU2UBMx8PLinksEntry = {
    0xFF,
    8,
    &GalenaU2UBMx8PLinks[0]
};


DXIO_PORT_DESCRIPTOR    LegacyNVMEEntSSDPortsGlinks[] = {
    { // Legacy Backplane - NVME0
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 112, 115, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME1
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 116, 119, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME2
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 120, 123, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME3
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 124, 127, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME4
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 96, 99, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME5
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 100, 103, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME6
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 104, 107, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME7
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 108, 111, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME8
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 64, 67, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME9
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 68, 71, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME10
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 72, 75, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME11
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 76, 79, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME12
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 80, 83, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME13
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 84, 87, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME14
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 88, 91, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME15
      DESCRIPTOR_TERMINATE_LIST,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 92, 95, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    }
}; ///< DXIO Port Descriptor List

ADDIN_CARD_PORTS LegacyNVMEEntSSDEntryGlinks = {
    0xFF,
    16,
    &LegacyNVMEEntSSDPortsGlinks[0]
};

DXIO_PORT_DESCRIPTOR    LegacyNVMEEntSSDPortsPlinks[] = {
    { // Legacy Backplane - NVME0
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 3, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME1
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 4, 7, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME2
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 8, 11, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME3
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 12, 15, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME4
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 32, 35, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME5
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 36, 39, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME6
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 40, 43, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME7
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 44, 47, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME8
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 48, 51, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME9
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 52, 55, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME10
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 56, 59, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME11
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 60, 63, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME12
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 16, 19, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME13
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 20, 23, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME14
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 24, 27, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME15
      DESCRIPTOR_TERMINATE_LIST,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 28, 31, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    }
}; ///< DXIO Port Descriptor List

ADDIN_CARD_PORTS LegacyNVMEEntSSDEntryPlinks = {
    0xFF,
    16,
    &LegacyNVMEEntSSDPortsPlinks[0]
};

DXIO_PORT_DESCRIPTOR    LegacyNVMEModeAPortsGlinks[] = {
    { // Legacy Backplane - NVME0
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 96, 99, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME1
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 100, 103, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME2
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 104, 107, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME3
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 108, 111, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME4
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 112, 115, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME5
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 116, 119, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME6
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 120, 123, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME7
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 124, 127, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME8
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 64, 67, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME9
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 68, 71, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME10
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 72, 75, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME11
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 76, 79, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME12
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 80, 83, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME13
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 84, 87, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME14
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 88, 91, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME15
      DESCRIPTOR_TERMINATE_LIST,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 92, 95, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    }
}; ///< DXIO Port Descriptor List

ADDIN_CARD_PORTS LegacyNVMEModeAEntryGlinks = {
    0xFF,
    16,
    &LegacyNVMEModeAPortsGlinks [0]
};

DXIO_PORT_DESCRIPTOR    LegacyNVMEModeAPortsPlinks[] = {
    { // Legacy Backplane - NVME0
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 3, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME1
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 4, 7, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME2
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 8, 11, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME3
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 12, 15, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME4
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 32, 35, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME5
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 36, 39, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME6
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 40, 43, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME7
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 44, 47, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME8
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 48, 51, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME9
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 52, 55, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME10
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 56, 59, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME11
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 60, 63, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME12
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 16, 19, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME13
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 20, 23, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME14
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 24, 27, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME15
      DESCRIPTOR_TERMINATE_LIST,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 28, 31, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    }
}; ///< DXIO Port Descriptor List

ADDIN_CARD_PORTS LegacyNVMEModeAEntryPlinks = {
    0xFF,
    16,
    &LegacyNVMEModeAPortsPlinks [0]
};

DXIO_PORT_DESCRIPTOR    LegacyNVMEModeBPortsGlinks[] = {
    { // Legacy Backplane - NVME0
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 96, 99, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME1
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 100, 103, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME2
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 104, 107, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME3
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 108, 111, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME4
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 112, 115, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME5
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 116, 119, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME6
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 120, 123, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME7
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 124, 127, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME8
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 64, 67, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME9
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 68, 71, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME10
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 72, 75, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME11
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 76, 79, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME12
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 80, 83, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME13
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 84, 87, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME14
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 88, 91, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME15
      DESCRIPTOR_TERMINATE_LIST,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 92, 95, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    }
}; ///< DXIO Port Descriptor List

ADDIN_CARD_PORTS LegacyNVMEModeBEntryGlinks = {
    0xFF,
    16,
    &LegacyNVMEModeBPortsGlinks [0]
};

DXIO_PORT_DESCRIPTOR    LegacyNVMEModeBPortsPlinks[] = {
    { // Legacy Backplane - NVME0
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 3, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME1
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 4, 7, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME2
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 8, 11, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME3
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 12, 15, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME4
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 32, 35, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME5
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 36, 39, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME6
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 40, 43, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME7
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 44, 47, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME8
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 48, 51, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME9
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 52, 55, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME10
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 56, 59, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME11
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 60, 63, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME12
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 16, 19, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME13
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 20, 23, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME14
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 24, 27, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME15
      DESCRIPTOR_TERMINATE_LIST,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 28, 31, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    }
}; ///< DXIO Port Descriptor List

ADDIN_CARD_PORTS LegacyNVMEModeBEntryPlinks = {
    0xFF,
    16,
    &LegacyNVMEModeBPortsPlinks [0]
};

DXIO_PORT_DESCRIPTOR    LegacyNVMESimplePresencePortsGlinks[] = {
    { // Legacy Backplane - NVME0
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 96, 99, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME1
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 100, 103, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME2
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 104, 107, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME3
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 108, 111, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME4
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 64, 67, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME5
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 68, 71, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME6
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 72, 75, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME7
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 76, 79, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME8
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 112, 115, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME9
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 116, 119, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME10
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 120, 123, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME11
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 124, 127, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },

    { // Legacy Backplane - NVME12
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 80, 83, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME13
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 84, 87, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME14
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 88, 91, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME15
      DESCRIPTOR_TERMINATE_LIST,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 92, 95, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 0)
      PORT_PARAMS_END
    }
}; ///< DXIO Port Descriptor List

ADDIN_CARD_PORTS LegacyNVMESimplePresencePortsEntryGlinks = {
    0xFF,
    16,
    &LegacyNVMESimplePresencePortsGlinks[0]
};

DXIO_PORT_DESCRIPTOR    LegacyNVMESimplePresencePortsPlinks[] = {
    { // Legacy Backplane - NVME0
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 3, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME1
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 4, 7, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME2
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 8, 11, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME3
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 12, 15, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME4
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 32, 35, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME5
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 36, 39, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME6
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 40, 43, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME7
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 44, 47, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME8
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 48, 51, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME9
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 52, 55, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME10
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 56, 59, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME11
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 60, 63, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME12
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 16, 19, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME13
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 20, 23, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME14
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 24, 27, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    },
    { // Legacy Backplane - NVME15
      DESCRIPTOR_TERMINATE_LIST,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 28, 31, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_HOTPLUG_TYPE, DxioHotplugServerEntSSD),
        PORT_PARAM (PP_LEGACY_SWITCH0_ADDR, 0x70),
        PORT_PARAM (PP_LEGACY_SWITCH0_SELECT, 1)
      PORT_PARAMS_END
    }
}; ///< DXIO Port Descriptor List

ADDIN_CARD_PORTS LegacyNVMESimplePresencePortsEntryPlinks = {
    0xFF,
    16,
    &LegacyNVMESimplePresencePortsPlinks [0]
};

DXIO_PORT_DESCRIPTOR    GalenaUBMSATAGLinks[] = {
    { // G3 - SATA ports 80-87
      0,
      DXIO_ENGINE_DATA_INITIALIZER (DxioSATAEngine, 80, 87, DxioHotplugDisabled, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      NO_PORT_PARAMS_DATA
      ANCDATA_OVRD_TBL(CommonSataPortSpeedOvrdG3)
    },
    { // G3 - SATA ports 88-95
      DESCRIPTOR_TERMINATE_LIST,
      DXIO_ENGINE_DATA_INITIALIZER (DxioSATAEngine, 88, 95, DxioHotplugDisabled, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      NO_PORT_PARAMS_DATA
      ANCDATA_OVRD_TBL(CommonSataPortSpeedOvrdG3)
    },
};

ADDIN_CARD_PORTS GalenaUBMSATAGLinksEntry = {
    0xFF,
    2,
    &GalenaUBMSATAGLinks[0]
};


DXIO_PORT_DESCRIPTOR    GalenaUBMSATAPLinks[] = {
    { // P0 - SATA ports 0-7
      0,
      DXIO_ENGINE_DATA_INITIALIZER (DxioSATAEngine, 0, 7, DxioHotplugDisabled, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      NO_PORT_PARAMS_DATA
      ANCDATA_OVRD_TBL(CommonSataPortSpeedOvrdP0)
    },
    { // P0 - SATA ports 8-15
      DESCRIPTOR_TERMINATE_LIST,
      DXIO_ENGINE_DATA_INITIALIZER (DxioSATAEngine, 8, 15, DxioHotplugDisabled, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      NO_PORT_PARAMS_DATA
      ANCDATA_OVRD_TBL(CommonSataPortSpeedOvrdP0)
    },
};

ADDIN_CARD_PORTS GalenaUBMSATAPLinksEntry = {
    0xFF,
    2,
    &GalenaUBMSATAPLinks[0]
};

#endif

