#***********************************************************************
#*                                                                     *
#*   Copyright (c) 1985-2025, American Megatrends International LLC.   *
#*                                                                     *
#*      All rights reserved. Subject to AMI licensing agreement.       *
#*                                                                     *
#***********************************************************************

[Defines]
  INF_VERSION     = 0x00010005
  VERSION_STRING  = 1.0
  BASE_NAME       = EarlyConsoleOutInterfaceDxe
  MODULE_TYPE     = DXE_DRIVER
  FILE_GUID       = 8790C65B-61E2-41A0-B6AD-FCE066F18414
  ENTRY_POINT     = EarlyConsoleOutInterfaceDxeEntryPoint

[Sources]
  EarlyConsoleOutInterfaceDxe.c
  EarlyConsoleDisplayDxe.c
  EarlyConsoleDisplay.h

[LibraryClasses]
  UefiDriverEntryPoint
  UefiBootServicesTableLib
  DebugLib
  BaseMemoryLib
  HobLib
  BaseLib
  MemoryAllocationLib
  UefiLib
  PrintLib
  $(VIDEO_TEXT_OUT_LIB)

[Guids]
  gAmiEarlyConsoleDisplayFrameInfoHobGuid
  gEfiGraphicsDeviceInfoHobGuid
  
[Protocols]
  gAmiEarlyConsoleOutProtocolGuid
  gAmiGraphicsOutputProtocolGuid
  gAmiSimpleTextOutProtocolGuid
  gEfiGraphicsOutputProtocolGuid
  gEfiSimpleTextOutProtocolGuid
  gAmiBdsConnectDriversProtocolGuid
  gEfiDevicePathProtocolGuid
  gEfiPciIoProtocolGuid
  gAmiConInStartedProtocolGuid
  gEfiPciEnumerationCompleteProtocolGuid

[Pcd]
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdSimpleTextOutMaxPpiSupported
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdProgressBarForegroundColor
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdProgressBarBackgroundColor
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdProgressBarBorderColor
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdGraphicsConsoleSupportInBds
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdHotKeyHighlightColor
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdDynamicBackgroundStringsSupport
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdCallFromEarlyConsoleOut
  
[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  AmiModulePkg/AmiModulePkg.dec
  AmiModulePkg/EarlyConsoleOut/AmiEarlyConsoleOutPkg.dec

[Depex]
  TRUE
