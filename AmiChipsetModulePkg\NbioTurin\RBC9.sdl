TOKEN
	Name  = "RBC9_SUPPORT"
	Value  = "1"
	Help  = "Main switch to enable RBC9 support in Project"
	TokenType = Boolean
	TargetEQU = Yes
	TargetMAK = Yes
	TargetH = Yes
	Master = Yes
	Token = "NSOCKETS" "=" "2"
End

PCIDEVICE
	Title  = "Root Complex Socket1 RBC1"
	Parent  = "PciHost (Virtual)"
#    Attribute  = "0x7307f"   - this sets VGA bits. Try without
	Attribute  = "0x303f"
	Dev_type  = "RootBridge"
	Bus  = 00h
	Dev  = 00h
	Fun  = 00h
	BridgeBusNum  = 0Dh
	SleepNum  = 01h
#    ASLfile  = "'AmiChipsetModulePkg/ASL/Nb.ASL','AmiChipsetModulePkg/ASL/Rb_Res.asl','AmiModulePkg\PCI\OSCM.ASL'"
#    ASLfile  = "'AmiChipsetModulePkg/ASL/Rb_Res.asl'"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB2.asl','AmiChipsetModulePkg/AslTurin/OSCM.ASL'"
	ASLdeviceName  = "S1D1"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	FixedBus = Yes
	PCIExpress = Yes
#    ASL_PTS  = "Method;\_SB.PCI0.NPTS(Arg0)"
#    ASL_WAK  = "Method;ShiftLeft(Arg0, 4, DBG8)  \_SB.PCI0.NWAK(Arg0)"
	InitRoutine  = "RootBrgInit"
End


PCIDEVICE
	Title  = "Socket 1 RBC1 - IOMMU"
	Parent  = "Root Complex Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 00h
	Fun  = 02h
	ASLdeviceName  = "MU11"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
End

PCIDEVICE
	Title  = "Socket 1 RBC1 - RCEC"
	Parent  = "Root Complex Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 00h
	Fun  = 03h
	IntA =  LNKB; 313
	ASLdeviceName  = "EC11"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 0 Socket1 RBC1 TypeB"
	Parent  = "Root Complex Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 01h
	Fun  = 01h
	IntA =  LNKA; 312
	ASLdeviceName  = "D9A0"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl','AmdCpmPkg/Features/HotPlug/Brh/Asl/AmdHpRegsReconfig.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 1 Socket1 RBC1 TypeB"
	Parent  = "Root Complex Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 01h
	Fun  = 02h
	IntB =  LNKA; 312
	ASLdeviceName  = "D9A1"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl','AmdCpmPkg/Features/HotPlug/Brh/Asl/AmdHpRegsReconfig.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 2 Socket1 RBC1 TypeB"
	Parent  = "Root Complex Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 01h
	Fun  = 03h
	IntC =  LNKA; 312
	ASLdeviceName  = "D9A2"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl','AmdCpmPkg/Features/HotPlug/Brh/Asl/AmdHpRegsReconfig.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 3 Socket1 RBC1 TypeB"
	Parent  = "Root Complex Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 01h
	Fun  = 04h
	IntD =  LNKA; 312
	ASLdeviceName  = "D9A3"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl','AmdCpmPkg/Features/HotPlug/Brh/Asl/AmdHpRegsReconfig.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 4 Socket1 RBC1 TypeB"
	Parent  = "Root Complex Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 01h
	Fun  = 05h
	IntA =  LNKA; 312
	ASLdeviceName  = "D9A4"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl','AmdCpmPkg/Features/HotPlug/Brh/Asl/AmdHpRegsReconfig.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 5 Socket1 RBC1 TypeB"
	Parent  = "Root Complex Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 01h
	Fun  = 06h
	IntB =  LNKA; 312
	ASLdeviceName  = "D9A5"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl','AmdCpmPkg/Features/HotPlug/Brh/Asl/AmdHpRegsReconfig.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 6 Socket1 RBC1 TypeB"
	Parent  = "Root Complex Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 01h
	Fun  = 07h
	IntC =  LNKA; 312
	ASLdeviceName  = "D9A6"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl','AmdCpmPkg/Features/HotPlug/Brh/Asl/AmdHpRegsReconfig.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 7 Socket1 RBC1 TypeB"
	Parent  = "Root Complex Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 02h
	Fun  = 01h
	IntA =  LNKA; 312
	ASLdeviceName  = "D9A7"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl','AmdCpmPkg/Features/HotPlug/Brh/Asl/AmdHpRegsReconfig.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 8 Socket1 RBC1 TypeB"
	Parent  = "Root Complex Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 02h
	Fun  = 02h
	IntB =  LNKA; 312
	ASLdeviceName  = "D9A8"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl','AmdCpmPkg/Features/HotPlug/Brh/Asl/AmdHpRegsReconfig.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 0 Socket1 RBC1 TypeD"
	Parent  = "Root Complex Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 03h
	Fun  = 01h
	IntA =  LNKB; 313
	ASLdeviceName  = "D9B0"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 1 Socket1 RBC1 TypeD"
	Parent  = "Root Complex Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 03h
	Fun  = 02h
	IntB =  LNKB; 313
	ASLdeviceName  = "D9B1"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 2 Socket1 RBC1 TypeD"
	Parent  = "Root Complex Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 03h
	Fun  = 03h
	IntC =  LNKB; 313
	ASLdeviceName  = "D9B2"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 3 Socket1 RBC1 TypeD"
	Parent  = "Root Complex Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 03h
	Fun  = 04h
	IntD =  LNKB; 313
	ASLdeviceName  = "D9B3"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 4 Socket1 RBC1 TypeD"
	Parent  = "Root Complex Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 03h
	Fun  = 05h
	IntA =  LNKB; 313
	ASLdeviceName  = "D9B4"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 5 Socket1 RBC1 TypeD"
	Parent  = "Root Complex Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 03h
	Fun  = 06h
	IntB =  LNKB; 313
	ASLdeviceName  = "D9B5"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 6 Socket1 RBC1 TypeD"
	Parent  = "Root Complex Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 03h
	Fun  = 07h
	IntC =  LNKB; 313
	ASLdeviceName  = "D9B6"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl'"
End

PCIDEVICE
	Title  = "PCIe GPP Bridge 7 Socket1 RBC1 TypeD"
	Parent  = "Root Complex Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 04h
	Fun  = 01h
	IntD =  LNKB; 313
	ASLdeviceName  = "D9B7"
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL','AmdCpmPkg/Features/PlatformRas/Brh/Asl/AmdAsyncHpEdr.asl'"
End


PCIDEVICE
	Title  = "Int Bridge to Bus B Socket1 RBC1"
	Parent  = "Root Complex Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 07h
	Fun  = 01h
	IntA = LNKF; 325
#REF for fixed devices which must match below
#    IntA =  LNKB; 321
#    IntB =  LNKC; 322
#    IntC =  LNKD; 323
#    IntD =  LNKA; 320
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	GPEbit  = 00Bh
	SleepNum  = 04h
	WakeEnabled = Yes
#	PWRBnotify = Yes
	ASLdeviceName  = "B110"
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL'"
	Help  = "Exposes NTB and PTDMA"
End

PCIDEVICE
	Title  = "Primary PCIe Dummy Function Socket1 RBC1"
	Parent  = "Int Bridge to Bus B Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 00h
	Fun  = 00h
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
End

PCIDEVICE
	Title  = "MPDMA Socket1 RBC1"
	Parent  = "Int Bridge to Bus B Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 0h
	Fun  = 01h
	SleepNum  = 01h
	IntA =     LNKB; 329
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
End

PCIDEVICE
	Title  = "PCIe Non-Transparent Bridge Socket1 RBC1"
	Parent  = "Int Bridge to Bus B Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 0h
	Fun  = 02h
	SleepNum  = 01h
	IntB =     LNKC; 330
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
End

PCIDEVICE
	Title  = "PCIe See Vntb Socket1 RBC1"
	Parent  = "Int Bridge to Bus B Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 0h
	Fun  = 03h
	SleepNum  = 01h
	IntC =     LNKD; 331
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
End

PCIDEVICE
	Title  = "PCIe ASP Socket1 RBC1"
	Parent  = "Int Bridge to Bus B Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 0h
	Fun  = 05h
	ASLdeviceName  = "ASP1"
	IntD=     LNKE; 332
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
End

PCIDEVICE
	Title  = "PCIe ACP Socket1 RBC1"
	Parent  = "Int Bridge to Bus B Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 0h
	Fun  = 06h
	SleepNum  = 01h
	IntA =     LNKF; 333
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
End

PCIDEVICE
	Title  = "HD Audio Controller Socket1 RBC1"
	Parent  = "Int Bridge to Bus B Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	Dev  = 0h
	Fun  = 07h
	SleepNum  = 01h
	IntB =     LNKG; 334
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
End

PCIDEVICE
	Title  = "Int Bridge to Bus C Port1 Socket1 RBC1"
	Parent  = "Root Complex Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "Pci2PciBridge"
	Dev  = 07h
	Fun  = 02h
	IntB =  LNKC; 330
#REF for fixed devices which must match below
#    IntA =  LNKF; 325
#    IntB =  LNKG; 326
#    IntC =  LNKH; 327
#    IntD =  LNKE; 324
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	PCIExpress = Yes
	ASLdeviceName  = "C110"
	InitRoutine  = "PciRootPortInitCallback"
	ASLfile  = "'AmiChipsetModulePkg/AslTurin/NB4.ASL'"
	Help  = "Exposes SATA"
End

PCIDEVICE
	Title  = "SATA AHCI Mode Socket1 RBC1"
	Parent  = "Int Bridge to Bus C Port1 Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	ASLfile  = "'AmiChipsetModulePkg\Asl\Sata.asl'"
	ASLdeviceName  = "SA91"
	Dev  = 1h
	Fun  = 0h
	SleepNum  = 01h
	IntA =     LNKB; 329
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	InitRoutine  = "SATA_Init"
End

PCIDEVICE
	Title  = "SATA2 AHCI Mode Socket1 RBC1"
	Parent  = "Int Bridge to Bus C Port1 Socket1 RBC1"
	Attribute  = "0x0"
	Dev_type  = "PciDevice"
	ASLfile  = "'AmiChipsetModulePkg\Asl\Sata.asl'"
	ASLdeviceName  = "SA92"
	Dev  = 1h
	Fun  = 1h
	SleepNum  = 01h
	IntB =     LNKC; 330
	DeviceType = OnBoard
	PCIBusSize = 32bit
	ROMMain = No
	InitRoutine  = "SATA_Init"
End

IOAPIC
	Title  = "RBC9_IOAPIC"
	APICID  = 0F9h
	VectorBase  = 0138h
	VectorRange  = 020h
	AddressBase  = 085000000h
End
