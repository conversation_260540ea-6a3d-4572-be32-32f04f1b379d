TOKEN
    Name  = "BaseCryptLibBin"
    Value  = "0"
    Help = "1 - Include pre-built Crypto binaries to the build. 0 - Include Crypto Source to the build"
    TokenType = Boolean
    TargetMAK = Yes
    Master = Yes
End

INFComponent
    Name  = "CryptoDxe"
    File  = "CryptoDxeBin.inf"
    Package  = "CryptoPkg"
    ModuleTypes  = "DXE_DRIVER"
    Arch  = "IA32 X64 ARM AARCH64"
    TOKEN = "BUILD_OPENSSL_WITH_SOCKET" "=" "0"
End

INFComponent
    Name  = "CryptoPei"
    File  = "CryptoPeiBin.inf"
    Package  = "CryptoPkg"
    ModuleTypes  = "PEIM"
    Arch  = "IA32 X64 ARM AARCH64"
    TOKEN = "BUILD_OPENSSL_WITH_SOCKET" "=" "0"
    Token = "BUILD_EDKII_PEI_CRYPT_LIB" "=" "1"
End

INFComponent
    Name  = "CryptoSmm"
    File  = "CryptoSmmBin.inf"
    Package  = "CryptoPkg"
    ModuleTypes  = "DXE_SMM_DRIVER"
    Arch  = "IA32 X64 ARM AARCH64"
    Token = "SMM_SUPPORT" "=" "1"
    TOKEN = "BUILD_OPENSSL_WITH_SOCKET" "=" "0"
End

FFS_FILE
    Name  = "CryptoStandaloneMm.txt"
    FD_AREA  = "FV_MM"
    FILE_Stmt  = "CryptoPkg/BaseCryptLibBin/AArch64/CryptoStandaloneMm.txt"
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
    TOKEN = "BUILD_OPENSSL_WITH_SOCKET" "=" "0"
End