TOKEN
    Name  = "VideoGraphicsConsole_SUPPORT"
    Value = "1"
    Help  = "Main switch to enable Early Graphics Console support in Project"
    TokenType = Boolean
    TargetH = Yes
    Master = Yes
    Token = "EarlyGraphicsConsole_SUPPORT" "=" "1"
End

INFComponent
    Name  = "PeiGraphicsOutput"
    File  = "PeiGraphicsOutput/PeiGraphicsOutput.inf"
    Package  = "AmiModulePkg"
    ModuleTypes  = "PEIM"
    PreProcess = Yes
End

INFComponent
    Name  = "DxeGraphicsOutput"
    File  = "DxeGraphicsOutput/DxeGraphicsOutput.inf"
    Package  = "AmiModulePkg"
    ModuleTypes  = "DXE_DRIVER"
End

INFComponent
    Name  = "GraphicsOutputUefi"
    File  = "GraphicsOutputUefi/GraphicsOutputUefi.inf"
    Package  = "AmiModulePkg"
    ModuleTypes  = "UEFI_DRIVER"
End

TOKEN
    Name  = "PeiGraphicsOutput_DEPEX"
    Value  = "gEfiPeiMemoryDiscoveredPpiGuid"
    Help  = "Port depex for PeiGraphicsOutput driver"
    TokenType = Expression
End

TOKEN
    Name  = "EDK_II_GOP_DRIVER_FFS_GUID"
    Value  = "{0x20830080, 0xCC28, 0x4169, {0x98, 0x36, 0x7F, 0x42, 0xB8, 0xD0, 0xC8, 0xC9}}"
    Help  = "EDK II GOP driver FFS GUID"
    TokenType = Expression
    TargetH = Yes
    Range  = "GUID"
End

TOKEN
    Name  = "EARLY_GRAPHICS_BACKGROUND_COLOR"
    Value  = "{0, 0, 0, 0}"
    Help  = "Port the color code of background color in format of EFI_GRAPHICS_OUTPUT_BLT_PIXEL"
    TokenType = Expression
    TargetH = Yes
End

# FrameBufferBltLib LibraryMapping is not done in MdeModulePkg
INFComponent
    Name  = "FrameBufferBltLib"
    File  = "../../../MdeModulePkg/Library/FrameBufferBltLib/FrameBufferBltLib.inf"
    Package  = "MdeModulePkg"
End

LibraryMapping
    Class  = "FrameBufferBltLib"
    Instance  = "MdeModulePkg.FrameBufferBltLib"
    ModuleTypes  = "DXE_DRIVER UEFI_DRIVER"
End

PcdMapping
    Name  = "PcdDefaultCursorState"
    GuidSpace  = "gAmiModulePkgTokenSpaceGuid"
    PcdType  = "PcdsFixedAtBuild"
    Value  = "FALSE"
    Offset  = 00h
    Length  = 00h
    Help  = "Disables cursor in BDS"
    TargetDSC = Yes
End
