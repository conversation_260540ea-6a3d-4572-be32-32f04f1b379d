/*****************************************************************************
 * Copyright (C) 2015-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * AMD CPM OEM API, and related functions.
 *
 * Contains the definitions for AMD backplanes.
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      CPM
 * @e sub-project:  OEM
 * @e \$Revision: 270275 $   @e \$Date: 2013-08-09 03:54:44 +0800 (Fri, 09 Aug 2013) $
 *
 */

#ifndef _AMD_BACKPLANES_H_
#define _AMD_BACKPLANES_H_

// Ancillary Data speed override data format
//Bits[00:07] Bit0:Enable C0, Bit1:Enable Cn, Bit2:Enable Cp
//    [08:15] C0 override value
//    [16:23] Cn override value
//    [24:31] Cp override value
ANC_DATA_PARAM   CommonSataPortSpeedOvrd[] = {
  {SPD_OVRD, 0x00001C07},       //Gen1: 60% Swing, 0dB (cn/c0/cp = 0/28/0)
  {SPD_OVRD, 0x00002107},       //Gen2: 70% Swing, 0dB (cn/c0/cp = 0/33/0)
  {SPD_OVRD, 0x09002707}        //Gen3: 100% Swing, -4dB (cn/c0/cp = 0/39/9)
};

DXIO_PORT_DESCRIPTOR    RubyUBMStandard[] = {
    { // UBM backplane HFC 4 - P0
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 0, 7, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_START_LANE, 0),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x72),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x10),
      PORT_PARAMS_END
      ANCDATA_OVRD_TBL(CommonSataPortSpeedOvrd)
    },
    { // UBM backplane HFC 0 - G0
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 96, 111, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 96),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x20),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x72),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x18),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 2 - G1
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 64, 79, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 64),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x20),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 2),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 3),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x72),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x18),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 0 - G2
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 112, 127, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 112),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x20),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 4),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 5),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x72),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 2 - G3
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 80, 95, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 80),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x20),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 6),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 7),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x72),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    },
};   // End of DXIO_PORT_DESCRIPTOR

ADDIN_CARD_PORTS RubyUBMStandardEntry = {
    0xFF,
    5,
    &RubyUBMStandard[0]
};

DXIO_PORT_DESCRIPTOR    RubyUBMStandardNvme[] = {
     { // UBM backplane HFC 0 - G0
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 96, 111, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 96),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x20),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x72),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x10),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 2 - G1
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 64, 79, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 64),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x20),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 2),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 3),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x72),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x10),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 0 - G2
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 112, 127, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 112),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x20),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 4),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 5),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x72),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x18),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 2 - G3
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 80, 95, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 80),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x20),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 6),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 7),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x72),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x18),
      PORT_PARAMS_END
    },
};   // End of DXIO_PORT_DESCRIPTOR

ADDIN_CARD_PORTS RubyUBMStandardNvmeEntry = {
    0xFF,
    4,
    &RubyUBMStandardNvme[0]
};

DXIO_PORT_DESCRIPTOR    RubyUBMFullNVME[] = {
    { // UBM backplane HFC 4 - P0
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 0, 15, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 0),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x72),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x10),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 0 - G0
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 96, 111, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 96),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x20),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x72),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x10),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 2 - G1
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 64, 79, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 64),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x20),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 2),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 3),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x72),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x18),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 0 - G2
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 112, 127, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 112),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x20),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 4),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 5),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x72),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x18),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 4 - P3
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 16, 31, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 16),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 2),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 3),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x72),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 1),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    },
    { // UBM backplane HFC 2 - G3
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 80, 95, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_NPEM_CAPABILITES, 0x0FFF),
        PORT_PARAM (PP_NPEM_ENABLE, 0x1),
        PORT_PARAM (PP_START_LANE, 80),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x20),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 6),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 7),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x72),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
        PORT_PARAM (PP_SLOT_NUM, 0x20),
      PORT_PARAMS_END
    }
};

ADDIN_CARD_PORTS RubyUBMFullNVMEEntry = {
    0xFF,
    6,
    &RubyUBMFullNVME[0]
};


DXIO_PORT_DESCRIPTOR    RubyUBMFullSATA[] = {
    { // UBM backplane HFC 4 - P0
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 0, 7, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_START_LANE, 0),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 0),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 1),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x72),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAMS_END
      ANCDATA_OVRD_TBL(CommonSataPortSpeedOvrd)
    },
    { // UBM backplane HFC 4 - G3 0-7
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 80, 87, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_START_LANE, 80),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x20),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 6),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 7),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x72),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x70),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 3),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAMS_END
      ANCDATA_OVRD_TBL(CommonSataPortSpeedOvrd)
    },
    { // UBM backplane HFC 4 - G3 8-15
      0,
      DXIO_ENGINE_INITIALIZER (DxioUBMHFCEngine, 88, 95, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen4),
        PORT_PARAM (PP_START_LANE, 88),
        PORT_PARAM (PP_I2C_EXPANDER_ADDRESS, 0x21),
        PORT_PARAM (PP_I2C_EXPANDER_TYPE, UBM_GPIO_EXPANDER_TYPE_9535),
        PORT_PARAM (PP_GPIOx_BP_TYPE, 4),
        PORT_PARAM (PP_GPIOx_I2C_RESET, 5),
        PORT_PARAM (PP_UBM_SWITCH0_ADDR, 0x72),
        PORT_PARAM (PP_UBM_SWITCH0_SELECT, 0),
        PORT_PARAM (PP_UBM_SWITCH0_TYPE, UBM_GPIO_SWITCH_TYPE_9546_48),
        PORT_PARAM (PP_UBM_SWITCH1_ADDR, 0x71),
        PORT_PARAM (PP_UBM_SWITCH1_SELECT, 2),
        PORT_PARAM (PP_UBM_SWITCH1_TYPE, UBM_GPIO_SWITCH_TYPE_9545),
      PORT_PARAMS_END
      ANCDATA_OVRD_TBL(CommonSataPortSpeedOvrd)
    }
};

ADDIN_CARD_PORTS RubyUBMFullSATAEntry = {
    0xFF,
    3,
    &RubyUBMFullSATA[0]
};
#endif
