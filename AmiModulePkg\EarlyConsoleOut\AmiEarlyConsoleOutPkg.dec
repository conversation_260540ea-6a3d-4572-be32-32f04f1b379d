#***********************************************************************
#*                                                                     *
#*   Copyright (c) 1985-2025, American Megatrends International LLC.   *
#*                                                                     *
#*      All rights reserved. Subject to AMI licensing agreement.       *
#*                                                                     *
#***********************************************************************
## @file
#  This DEC file provides definitions for AMI specific interfaces that 
#   are used in EarlyConsoleOut Driver.
##

################################################################################
#
# Defines Section - statements that will be processed to create a Makefile.
#
################################################################################
[Defines]
  DEC_SPECIFICATION              = 0x00010005
  PACKAGE_NAME                   = AmiEarlyConsoleOutPkg
  PACKAGE_GUID                   = 3A73A1AD-A203-4bbf-B231-FA5A633E5624
  PACKAGE_VERSION                = 0.1

[Protocols]
  gEarlyVideoTextOutProtocolGuid            = { 0x4d1f52ed, 0x71ae, 0x46ad, { 0x89, 0xc2, 0x60, 0x48, 0x19, 0x85, 0x1a, 0xc1 }}
  gSerialLivePostProtocolGuid               = { 0x7f2eadbd, 0x4df8, 0x4c8b, { 0xb3, 0xd1, 0x3c, 0xe1, 0x13, 0x43, 0x76, 0x22 }}
  gAmiSimpleTextOutProtocolGuid             = { 0xe847e6f5, 0x3eea, 0x45f2, {0x83, 0x8b, 0x41, 0x7c, 0xed, 0xc6, 0x76, 0xe6 }}
  gAmiGraphicsOutputProtocolGuid            = {0xf3f904f2, 0x7dac, 0x4e52, {0xa4, 0xfe, 0x4e, 0x6, 0xf9, 0x5b, 0x1a, 0x2}}
  gAmiEarlyConsoleOutProtocolGuid           = { 0x276f30c7, 0x4d0c, 0x46fe, { 0x8a, 0x31, 0x4, 0xf9, 0xdd, 0x67, 0x78, 0xae } }
  
[Ppis]
  gEarlyVgaPpiGuid                       = { 0x36E624D9, 0x418D, 0x4aed, {0x90, 0x13, 0x7D, 0xFF, 0xC6, 0x8E, 0x80, 0x2A }}
  gVideoInitDonePpiGuid                  = { 0x89c44717, 0x7ee4, 0x487b, {0x8f, 0x5c, 0x8e, 0x60, 0x95, 0x56, 0xa9, 0x0f }}
  
  ## Include/Ppi/AmiGraphicsOutputPpi.h
  gAmiGraphicsOutPutPpiGuid              = { 0xbd15478c, 0x59d5, 0x48af, { 0xa2, 0x90, 0xd0, 0x33, 0xb2, 0xb3, 0x88, 0xd0 } }
  
  ## Include/Ppi/AmiSimpleTextOutPpi.h
  gAmiSimpleTextOutPpiGuid               = { 0xb38799d7, 0x1ffe, 0x47fc, { 0xaf, 0xaa, 0x97, 0xd5, 0xb2, 0xb3, 0xae, 0x37 } }
  
  gAmiEarlyConsoleOutPpiGuid             = { 0x1ee1e587, 0xa7b3, 0x4826, { 0xac, 0xc9, 0x8b, 0xe5, 0x97, 0xce, 0xe0, 0xa4 } }

[Guids]
  gAmiEarlyConsolePkgTokenSpaceGuid        = {0x70054931, 0xE359, 0x4300, {0x84, 0x87, 0xD9, 0x60, 0x7C, 0x13, 0xE4, 0xDA}}
  gAmiSimpleTextOutHobGuid                 = {0xC50CEE43, 0x1926, 0x465B, {0x8A, 0x91, 0xB8, 0x08, 0x5C, 0xB0, 0xA1, 0xD7}}
  gAmiEarlyConsoleDisplayFrameInfoHobGuid  = {0x7C290B7E, 0x28A9, 0x4286, {0xA5, 0xB0, 0x5C, 0xE1, 0x02, 0x19, 0xBC, 0x60}}
  gAmiEarlyConsoleStringHobGuid            = {0x4eb96ec3, 0x2629, 0x4f65, {0xbf, 0x71, 0x0b, 0xf2, 0x40, 0x3d, 0x7a, 0x44}}
  gAmiTextModeFrameBufferHobGuid           = {0x17280e95, 0x3715, 0x469a, {0xa6, 0x90, 0x5c, 0xa8, 0x40, 0x3f, 0xa7, 0x95}}
  gAmiVideoPrivateDataHobGuid              = {0xecf9fa0a, 0x8db1, 0x4222, {0xb8, 0x7e, 0xf9, 0xad, 0x9f, 0xe4, 0x93, 0x2c}}
  
[PcdsDynamic,PcdsDynamicEx]
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdSerialSimpleTextOutPpiInstance|0x00|UINT16|0x90000001
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdVideoSimpleTextOutPpiInstance|0x00|UINT16|0x90000002
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdEarlyConsolePlatformName|L"DEFAULT                          "|VOID*|0x90000003
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdCallFromEarlyConsoleOut|FALSE|BOOLEAN|0x90000004

[PcdsFixedAtBuild]
  ##Default Video mode to initialize the Video controller
  ##0x00 - Text Mode
  ##0x01 - Graphics Mode
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdEarlyConsoleVideoMode|1|UINT32|0x90000100
  
  ##Default Graphics Video display resolution to initialize the Video controller
  #define EARLY_CONSOLE_GRAPHICS_RES_640x480      0x00
  #define EARLY_CONSOLE_GRAPHICS_RES_800x600      0x01
  #define EARLY_CONSOLE_GRAPHICS_RES_1024x768     0x02
  #define EARLY_CONSOLE_GRAPHICS_RES_1280x1024    0x03
  #define EARLY_CONSOLE_GRAPHICS_RES_1600x1200    0x04
  #define EARLY_CONSOLE_GRAPHICS_RES_MAX          0x05
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdEarlyConsoleVideoDisplayResolution|1|UINT32|0x90000101
  
  #Maximum instance of AmiSimpleTextOut instances supported in Early Console Consplitter
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdSimpleTextOutMaxPpiSupported|0x03|UINT8|0x90000102

  # PCD to enable/disable drawing AMI logo in PEI
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdDrawDefaultLogoInPei|TRUE|BOOLEAN|0x90000103
  # PCD to enable/disable background image support
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdDisplayBackgroundImageInPei|FALSE|BOOLEAN|0x90000109
  
  # Port below two PCDs with the color code of progress bar foreground, background and border color in format of EFI_GRAPHICS_OUTPUT_BLT_PIXEL.
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdProgressBarForegroundColor|{0x0, 0x0, 0xCC, 0}|VOID*|0x90000104
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdProgressBarBackgroundColor|{0x73, 0x73, 0x73, 0}|VOID*|0x90000105
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdProgressBarBorderColor|{0xFF, 0xFF, 0xFF, 0}|VOID*|0x90000107
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdHotKeyHighlightColor|{0xE3, 0x22, 0x19, 0}|VOID*|0x90000110
  
  # PCD to enable/disable Graphics Console support in BDS
  # Setting this PCD to TRUE requires platform side porting. Please refer porting guide
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdGraphicsConsoleSupportInBds|FALSE|BOOLEAN|0x90000106
  
  # PCD to enable updating string background color with actual background data
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdDynamicBackgroundStringsSupport|FALSE|BOOLEAN|0x90000108
  