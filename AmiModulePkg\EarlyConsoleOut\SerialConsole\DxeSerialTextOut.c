//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************

/**
  @file  DxeSerialTextOutt.c
  This file contains the Protocol functions to use Serial device.
*/

#include <Uefi.h>
#include <Library/UefiBootServicesTableLib.h>
#include <Library/UefiRuntimeServicesTableLib.h>
#include <Library/MemoryAllocationLib.h>
#include <Library/DebugLib.h>
#include <Library/DevicePathLib.h>
#include <Library/BaseLib.h>
#include <Library/PcdLib.h>
#include <Library/DevicePathLib.h>
#include <Library/AmiSerialTextOutLib.h>
#include <Protocol/DevicePath.h>


typedef struct {
    EFI_SIMPLE_TEXT_OUTPUT_PROTOCOL     AmiDxeSimpleTextOut;
    EFI_SIMPLE_TEXT_OUTPUT_MODE         Mode;
    UINT32                              MaxRows;    ///< Max number of rows in current mode
    UINT32                              MaxColumns; ///< Max number of columns in current mode
} AMI_SERIAL_TEXT_OUT_PRIVATE_DATA;

AMI_SERIAL_TEXT_OUT_PRIVATE_DATA        gSerialTextOutPrivate;
BOOLEAN                                 gSerialPortInUse = FALSE;
VOID                                    *gSimpleTextOutNotifyReg;


/**
  Reset the text output device hardware and optionally run diagnostics

  @param  This                 Protocol instance pointer.
  @param  ExtendedVerification Driver may perform more exhaustive verification
                               operation of the device during reset.

  @retval EFI_SUCCESS          The text output device was reset.
  @retval EFI_DEVICE_ERROR     The text output device is not functioning correctly and
                               could not be reset.

**/
EFI_STATUS  
DxeSimpleTextOutReset (
    IN EFI_SIMPLE_TEXT_OUTPUT_PROTOCOL      *This,
    IN BOOLEAN                              ExtendedVerification
)
{
    if (gSerialPortInUse == TRUE) {
        return EFI_DEVICE_ERROR;
    }
    
    // Reset should:
    // - Clear the screen 
    // - set the cursor back to (0,0)
    
    This->SetAttribute(This, EFI_BACKGROUND_BLACK | EFI_WHITE);
    
    This->ClearScreen(This);
    
    This->SetCursorPosition(This, 0, 0);
    
    This->EnableCursor(
                    This, 
                    PcdGetBool(PcdDefaultCursorState));
    
    return  EFI_SUCCESS;
}

/**
    Write a string to the output device.

    @param  This   The Protocol instance pointer.
    @param  String The NULL-terminated string to be displayed on the output
                   device(s). All output devices must also support the Unicode
                   drawing character codes defined in this file.

    @retval EFI_SUCCESS             The string was output to the device.
    @retval EFI_DEVICE_ERROR        The device reported an error while attempting to output
                                    the text.
    @retval EFI_UNSUPPORTED         The output device's mode is not currently in a
                                    defined text mode.
**/
EFI_STATUS
EFIAPI
DxeSimpleTextOutPutString (
    IN EFI_SIMPLE_TEXT_OUTPUT_PROTOCOL      *This,
    IN CHAR16                               *String
)    
{
    EFI_STATUS                      Status;
    CHAR8                           Text[0x400];   //1KB

    if (gSerialPortInUse == TRUE) {
        return EFI_DEVICE_ERROR;
    }
    
    UnicodeStrToAsciiStrS (String, Text, sizeof(Text));

    Status = TerminalOutputStringHelper((CONST CHAR8 *)Text);
    
    return Status;
}

/**
  Verifies that all characters in a string can be output to the 
  target device.

  @param  This   The Protocol instance pointer.
  @param  String The NULL-terminated string to be examined for the output
                 device(s).

  @retval EFI_SUCCESS      The device(s) are capable of rendering the output string.
  @retval EFI_UNSUPPORTED  Some of the characters in the string cannot be
                           rendered by one or more of the output devices mapped
                           by the EFI handle.

**/
EFI_STATUS  
DxeSimpleTextOutTestString (
    IN EFI_SIMPLE_TEXT_OUTPUT_PROTOCOL          *This,
    IN CHAR16                                   *String
)
{
    
    if (gSerialPortInUse == TRUE) {
        return EFI_DEVICE_ERROR;
    }

    return EFI_SUCCESS;
}

/**
    Returns information for an available text mode that the output device(s)
    supports.

    @param  This       The Protocol instance pointer.
    @param  ModeNumber The mode number to return information on.
    @param  Columns    Returns the geometry of the text output device for the
                       requested ModeNumber.
    @param  Rows       Returns the geometry of the text output device for the
                       requested ModeNumber.
                                          
    @retval EFI_SUCCESS      The requested mode information was returned.
    @retval EFI_DEVICE_ERROR The device had an error and could not complete the request.
    @retval EFI_UNSUPPORTED  The mode number was not valid.

**/
EFI_STATUS
EFIAPI
DxeSimpleTextOutQueryMode (
    IN EFI_SIMPLE_TEXT_OUTPUT_PROTOCOL      *This,
    IN UINTN                                ModeNumber,
    IN OUT UINTN                            *Columns,
    IN OUT UINTN                            *Rows
)
{
    
    if (ModeNumber >= (UINTN)(This->Mode->MaxMode)) {
        return EFI_UNSUPPORTED;
    }
    
    if (gSerialPortInUse == TRUE) {
        return EFI_DEVICE_ERROR;
    }
    
    switch (ModeNumber) {
        case 0:
            //Mode 0 is the only valid mode
            *Columns = 80;
            *Rows = 25;
            break;
        default:
            *Columns = 0;
            *Rows = 0;
            return EFI_UNSUPPORTED;
    }

    return EFI_SUCCESS;
}

/**
  Sets the output device(s) to a specified mode.

  @param  This       The Protocol instance pointer.
  @param  ModeNumber The mode number to set.

  @retval EFI_SUCCESS      The requested text mode was set.
  @retval EFI_DEVICE_ERROR The device had an error and could not complete the request.
  @retval EFI_UNSUPPORTED  The mode number was not valid.

**/
EFI_STATUS
EFIAPI
DxeSimpleTextOutSetMode (
    IN EFI_SIMPLE_TEXT_OUTPUT_PROTOCOL      *This,
    IN UINTN                                ModeNumber
)
{
    
    if (ModeNumber >= (UINTN)(This->Mode->MaxMode)) {
        return EFI_UNSUPPORTED;
    }

    if (gSerialPortInUse == TRUE) {
        return EFI_DEVICE_ERROR;
    }
    // if the mode is a valid mode, return EFI_SUCCESS as we are
    // supporting only Mode 0.
    
    return EFI_SUCCESS;
}

/**
    Sets the background and foreground colors for the OutputString () and
    ClearScreen () functions.

    @param  This        The Protocol instance pointer.
    @param  Attribute   Attribute to set.
  

    @retval EFI_SUCCESS       The attribute was set.
    @retval EFI_DEVICE_ERROR  The device had an error and could not complete the request.
    @retval EFI_UNSUPPORTED   The attribute requested is not defined.

**/
EFI_STATUS
EFIAPI
DxeSimpleTextOutSetAttribute (
    IN EFI_SIMPLE_TEXT_OUTPUT_PROTOCOL          *This, 
    IN UINTN                                    Attribute
)
{

    EFI_STATUS              Status;

    if (gSerialPortInUse == TRUE) {
        return EFI_DEVICE_ERROR;
    }
    
    //    Bits 0..3 are the foreground color, and
    //    bits 4..6 are the background color. All other bits are undefined
    //    and must be zero.
    This->Mode->Attribute = (INT32)Attribute;
    
    // Call the SerialTextOut LIB to set the attribute
    Status = TerminalSetAttribute((UINT8)Attribute);

    return Status;
}

/**
    Clears the output device(s) display to the currently selected background 
    color.
      
    @param   This   The Protocol instance pointer.

    @retval  EFI_SUCCESS      The operation completed successfully.
    @retval  EFI_DEVICE_ERROR The device had an error and could not complete the request.
    @retval  EFI_UNSUPPORTED  The output device is not in a valid text mode.

**/
EFI_STATUS
EFIAPI
DxeSimpleTextOutClearScreen(
    IN EFI_SIMPLE_TEXT_OUTPUT_PROTOCOL          *This
)
{
    EFI_STATUS                  Status; 

    if (gSerialPortInUse == TRUE) {
        return EFI_DEVICE_ERROR;
    }
    
    Status = TerminalClearScreen();
    if (EFI_ERROR(Status)) {
        return Status;
    }
    
    //Set cursor position X=0, Y=0
    Status = This->SetCursorPosition (This, 0, 0);
    
    if (EFI_ERROR(Status)) { //on first invocation this failed because MaxRows = MaxCols = 0
        This->Mode->CursorColumn = 0;
        This->Mode->CursorRow = 0;
    }

    return Status; 
}

/**
    Sets the current coordinates of the cursor position

    @param  This        The Protocol instance pointer.
    @param  Column      The position to set the cursor to. Must be greater than or
                        equal to zero and less than the number of columns and rows
                        by QueryMode ().
    @param  Row         The position to set the cursor to. Must be greater than or
                        equal to zero and less than the number of columns and rows
                        by QueryMode ().

    @retval EFI_SUCCESS      The operation completed successfully.
    @retval EFI_DEVICE_ERROR The device had an error and could not complete the request.
    @retval EFI_UNSUPPORTED  The output device is not in a valid text mode, or the
                             cursor position is invalid for the current mode.

**/
EFI_STATUS
EFIAPI
DxeSimpleTextOutSetCursorPosition (
    IN EFI_SIMPLE_TEXT_OUTPUT_PROTOCOL      *This, 
    IN UINTN                                Column,
    IN UINTN                                Row
)
{
    EFI_STATUS              Status; 

    if (Column >= MAX_COLUMNS || Row >= MAX_ROWS) {
        return EFI_UNSUPPORTED;
    }

    if (gSerialPortInUse == TRUE) {
        return EFI_DEVICE_ERROR;
    }
    
    // Call the SerialTextOut LIB to set the cursor position 
    Status = TerminalSetCursorPosition ((UINT8)Column, (UINT8)Row);
    
    This->Mode->CursorColumn = (INT32)Column;
    This->Mode->CursorRow = (INT32)Row;

    return Status; 
}

/**
    Makes the cursor visible or invisible

    @param  This    The Protocol instance pointer.
    @param  Enable  If TRUE, the cursor is set to be visible. If FALSE, the cursor is
                    set to be invisible.

    @retval EFI_SUCCESS      The operation completed successfully.
    @retval EFI_DEVICE_ERROR The device had an error and could not complete the
                             request, or the device does not support changing
                             the cursor mode.
    @retval EFI_UNSUPPORTED  The output device is not in a valid text mode.

**/
EFI_STATUS 
EFIAPI
DxeSimpleTextOutEnableCursor(
   IN EFI_SIMPLE_TEXT_OUTPUT_PROTOCOL       *This,
   IN BOOLEAN                               Enable
)
{
    This->Mode->CursorVisible = Enable;
    return EFI_SUCCESS;
}

/**
    Notification function for SimpleTextOut Protocol 
    This is stop using the Serial device 

    @param Event - Event which caused this handler
    @param Context - Context passed during Event Handler registration

    @return VOID

**/
VOID
EFIAPI
SimpleTextOutCallBack (
  IN EFI_EVENT        Event,
  IN VOID             *TextOutContext 
)
{

    EFI_STATUS                          Status;
    BOOLEAN                             SimpleTextOutOnSerialIo = FALSE;
    EFI_DEVICE_PATH_PROTOCOL            *TerminalDevicePath;
    EFI_HANDLE                          Handle;
    UINTN                               Size = sizeof(EFI_HANDLE);
    EFI_SIMPLE_TEXT_OUTPUT_PROTOCOL     *SimpleTextOut;

    Status = gBS->LocateHandle(
                        ByRegisterNotify,
                        NULL, 
                        gSimpleTextOutNotifyReg, 
                        &Size, 
                        &Handle);

    if (EFI_ERROR(Status)) {
        return;
    }
    
    // Check If the SimpleTextout Installed on the Handle 
    Status = gBS->HandleProtocol(
                            Handle,
                            &gEfiSimpleTextOutProtocolGuid,
                            (VOID**)&SimpleTextOut);
    if (EFI_ERROR(Status)) {
        return;
    }
    
    // Get the Device Path Protocol 
    Status = gBS->HandleProtocol( 
                            Handle,
                            &gEfiDevicePathProtocolGuid,
                            (VOID**)&TerminalDevicePath);
    
    if (EFI_ERROR(Status)) {
        return;
    }

    // Check for the UART Node
    while (TerminalDevicePath->Type != END_DEVICE_PATH_TYPE){
        if ((TerminalDevicePath->Type == MESSAGING_DEVICE_PATH) && 
            (TerminalDevicePath->SubType == MSG_UART_DP) ) {
            // UART node found. The SimpleTextout created from UART device.
            SimpleTextOutOnSerialIo = TRUE;
            break;
        }
        TerminalDevicePath = NextDevicePathNode(TerminalDevicePath);
    }

    // Return if the SimpleTextOut is not from UART device. 
    if (SimpleTextOutOnSerialIo == FALSE) {
        return;
    }

    gSerialPortInUse = TRUE;
    
    // Kill the Event
    gBS->CloseEvent(Event);
    
    return;
}

/**
    This function is the entry point for this DXE driver.
    This installs the Serial TextOut Protocol.

    @param ImageHandle Variable of EFI_HANDLE.
    @param SystemTable Pointer to EFI_SYSTEM_TABLE

    @return EFI_STATUS
    @retval EFI_SUCCESS Successful driver initialization

**/

EFI_STATUS
EFIAPI
DxeSerialTextOutEntry (
    IN EFI_HANDLE              ImageHandle,
    IN EFI_SYSTEM_TABLE        *SystemTable
)
{
    EFI_STATUS                  Status;
    EFI_HANDLE                  Handle = NULL;
    EFI_EVENT                   SimpleTextOutEvent;
    ACPI_HID_DEVICE_PATH        AcpiDp;
    EFI_DEVICE_PATH_PROTOCOL    *DevicePath;

    // Initialize the Protocol API's
    gSerialTextOutPrivate.AmiDxeSimpleTextOut.Reset = DxeSimpleTextOutReset;
    gSerialTextOutPrivate.AmiDxeSimpleTextOut.OutputString = DxeSimpleTextOutPutString;
    gSerialTextOutPrivate.AmiDxeSimpleTextOut.TestString = DxeSimpleTextOutTestString;
    gSerialTextOutPrivate.AmiDxeSimpleTextOut.QueryMode = DxeSimpleTextOutQueryMode;
    gSerialTextOutPrivate.AmiDxeSimpleTextOut.SetMode = DxeSimpleTextOutSetMode;
    gSerialTextOutPrivate.AmiDxeSimpleTextOut.SetAttribute = DxeSimpleTextOutSetAttribute;
    gSerialTextOutPrivate.AmiDxeSimpleTextOut.ClearScreen = DxeSimpleTextOutClearScreen;
    gSerialTextOutPrivate.AmiDxeSimpleTextOut.SetCursorPosition = DxeSimpleTextOutSetCursorPosition;
    gSerialTextOutPrivate.AmiDxeSimpleTextOut.EnableCursor = DxeSimpleTextOutEnableCursor;
    gSerialTextOutPrivate.AmiDxeSimpleTextOut.Mode = &gSerialTextOutPrivate.Mode;
    
    // Initialize the structure from the HOB data
    gSerialTextOutPrivate.Mode.Mode   = 0;
    gSerialTextOutPrivate.Mode.MaxMode   = 1;
    gSerialTextOutPrivate.MaxRows = MAX_ROWS;
    gSerialTextOutPrivate.MaxColumns = MAX_COLUMNS;
    
    AcpiDp.Header.Type    = ACPI_DEVICE_PATH;
    AcpiDp.Header.SubType = ACPI_DP;
    SetDevicePathNodeLength (&AcpiDp, sizeof(ACPI_HID_DEVICE_PATH));
    AcpiDp.HID = EISA_PNP_ID(0x501);
    AcpiDp.UID = 0xFF;
    
    DevicePath = AppendDevicePathNode (NULL, &AcpiDp.Header);
    
    Status = gBS->InstallMultipleProtocolInterfaces ( 
                                                &Handle,
                                                &gAmiSimpleTextOutProtocolGuid,
                                                &gSerialTextOutPrivate.AmiDxeSimpleTextOut,
                                                &gEfiDevicePathProtocolGuid,
                                                DevicePath,
                                                NULL);
    if (EFI_ERROR(Status)) {
        return Status;
    }
    
    // Create the notification and register callback function on the SimpleTextOut Protocol installation
    Status = gBS->CreateEvent (
                        EVT_NOTIFY_SIGNAL, 
                        TPL_CALLBACK,
                        SimpleTextOutCallBack, 
                        NULL, 
                        &SimpleTextOutEvent);
    if (!EFI_ERROR(Status)) {
        Status = gBS->RegisterProtocolNotify (
                                    &gEfiSimpleTextOutProtocolGuid,
                                    SimpleTextOutEvent, 
                                    &gSimpleTextOutNotifyReg
                                    );
    
    }
    
    return Status;
}
