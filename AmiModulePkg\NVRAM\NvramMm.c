//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2022, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************
#include "NvramDxeCommon.h"
#include <Protocol/SmmVariable.h>
#include <Protocol/SmmReadyToLock.h>
#include <Protocol/SmmVarCheck.h>
#include <Protocol/VariablePolicy.h>
#include <Library/VariablePolicyLib.h>
#include <Library/MmServicesTableLib.h>
#include <Guid/HobList.h>
#include "NvramSmi.h"

BOOLEAN     gMorLock = FALSE;
BOOLEAN     MorLockCheckIsActive = FALSE;
UINT64      MorKeyArray[2];
UINT64      *gMorKey = NULL;
BOOLEAN LockEnforcementIsActive = TRUE;

#if AuthVariable_SUPPORT
VOID AuthVariableServiceInitSMM (VOID);
#else
VOID AuthVariableServiceInitSMM (VOID) {};
#endif //#if AuthVariable_SUPPORT

EFI_STATUS EFIAPI NvramSmiEntry();
EFI_STATUS SmmSetVariableInt(
    IN CHAR16 *VariableName, IN EFI_GUID *VendorGuid,
    IN UINT32 Attributes, IN UINTN DataSize, IN VOID *Data
);
EFI_STATUS EFIAPI SmmSetVariableNoLock(
    IN CHAR16 *VariableName, IN EFI_GUID *VendorGuid,
    IN UINT32 Attributes, IN UINTN DataSize, IN VOID *Data
);

EFI_SMM_VARIABLE_PROTOCOL SmmVariable = {
    DxeGetVariableWrapper, DxeGetNextVariableNameWrapper, SmmSetVariableNoLock, QueryVariableInfo
};

EDKII_SMM_VAR_CHECK_PROTOCOL SmmVarCheck = { VarCheckRegisterSetVariableCheckHandler,
                                              VarCheckVariablePropertySet,
                                              VarCheckVariablePropertyGet };

EFI_STATUS
EFIAPI
ProtocolIsVariablePolicyEnabled (
  OUT BOOLEAN *State
  )
{
  *State = IsVariablePolicyEnabled ();
  return EFI_SUCCESS;
}
EDKII_VARIABLE_POLICY_PROTOCOL      SmmVariablePolicyProtocol    = { EDKII_VARIABLE_POLICY_PROTOCOL_REVISION,
                                                                    DisableVariablePolicy,
                                                                    ProtocolIsVariablePolicyEnabled,
                                                                    RegisterVariablePolicy,
                                                                    DumpVariablePolicy,
                                                                    LockVariablePolicy };

VOID* NvramAllocatePages(UINTN Size){
    EFI_PHYSICAL_ADDRESS Address = 0;
    EFI_STATUS Status;

    Status = gMmst->MmAllocatePages(AllocateAnyPages, EfiRuntimeServicesData, EFI_SIZE_TO_PAGES(Size), &Address);
    if (EFI_ERROR(Status)) return NULL;
    return (VOID*)(UINTN)Address;
}

VOID NvramFreePages(VOID *Address, UINTN Size){
    EFI_STATUS Status;

    Status =  gMmst->MmFreePages((EFI_PHYSICAL_ADDRESS)(UINTN)Address, EFI_SIZE_TO_PAGES(Size));
    ASSERT_EFI_ERROR (Status);
}

BOOLEAN IsLockEnforcementActive (){
    return IsSmmLocked() && LockEnforcementIsActive;
}

VOID InitMorVariables(){
    if (((UINTN)&MorKeyArray[0] & 0x3F) == 0) //Check 64 byte aligment to use unaligned address
        gMorKey = &MorKeyArray[1];
    else
        gMorKey = &MorKeyArray[0];
    *gMorKey = 0;
}

VOID ResetMorVariables(){
    UINT8 Data = 0;
    EFI_STATUS Status;
    UINTN DataSize = 0;

    // Firmware shall initialize the variable with 0 prior to processing the Boot#### on every boot.
    // (BIOS Enabling Guide for Windows 10, Section#9 - Virtual Security Mode)
    Status = SmmSetVariableInt(
        L"MemoryOverwriteRequestControlLock",&gMemoryOverwriteRequestControlLockGuid,
        EFI_VARIABLE_BOOTSERVICE_ACCESS | EFI_VARIABLE_RUNTIME_ACCESS | EFI_VARIABLE_NON_VOLATILE,
        sizeof(Data), &Data
    );
    ASSERT_EFI_ERROR(Status);
    // If MemoryOverwriteRequestControl variable does not exist, create it.
    // If the variable exists, we are not reseting it back to zero.
    // Code that cleans the memory should reset variable back to zero.
    Status = DxeGetVariableWrapper(
        L"MemoryOverwriteRequestControl",&gMemoryOverwriteRequestControlGuid, NULL, &DataSize, NULL
    );
    if (Status == EFI_NOT_FOUND){
        Status = SmmSetVariableInt(
            L"MemoryOverwriteRequestControl",&gMemoryOverwriteRequestControlGuid,
            EFI_VARIABLE_BOOTSERVICE_ACCESS | EFI_VARIABLE_RUNTIME_ACCESS | EFI_VARIABLE_NON_VOLATILE,
            sizeof(Data), &Data
        );
        ASSERT_EFI_ERROR(Status);
    }
    MorLockCheckIsActive = TRUE;
}

/**
  This function locks the update of MOR variables once
  MemoryOverwriteRequestControlLock variable is locked-down by setting it to one.
  This function is called by SmmSetVariable.

 @param VariableName pointer to Var Name in Unicode
 @param VendorGuid pointer to Var GUID
 @param Attributes Attributes of the Var
 @param DataSize size of Var
 @param Data Pointer to memory where Var data is stored

 @retval EFI_UNSUPPORTED - Passed Variable can be set.
 @retval Any Status other than EFI_UNSUPPORTED - the error is propagated as a result of SmmSetVariable.

**/
EFI_STATUS CheckForMorVariables(
    IN CHAR16 *VariableName, IN EFI_GUID *VendorGuid,
    IN UINT32 Attributes, IN UINTN DataSize, IN VOID *Data
){

    EFI_STATUS Status;
    UINT8 VarData;

    if (!MorLockCheckIsActive) return EFI_UNSUPPORTED;

    if (  !StrCmp (VariableName, L"MemoryOverwriteRequestControlLock")
        && CompareGuid (VendorGuid, &gMemoryOverwriteRequestControlLockGuid)
    ){
        if ( DataSize != 0 && Data == NULL )
            return EFI_INVALID_PARAMETER;
        if ( Attributes == 0 || DataSize == 0 || Data == NULL )
            return EFI_WRITE_PROTECTED;
        if ( Attributes != (EFI_VARIABLE_BOOTSERVICE_ACCESS | EFI_VARIABLE_RUNTIME_ACCESS | EFI_VARIABLE_NON_VOLATILE)
                || (DataSize != 1 && DataSize != 8))
            return EFI_INVALID_PARAMETER;

        if (gMorLock == FALSE){
            // MOR Variable Unlocked
            if (DataSize == 1){
                if (*(UINT8*)Data == 0)
                    return EFI_UNSUPPORTED;
                if (*(UINT8*)Data == 1){
                    gMorLock = TRUE;
                    return EFI_UNSUPPORTED;
                }
                return EFI_INVALID_PARAMETER;
            } //if (DataSize == 1)

            if (DataSize == 8){
                gMorLock = TRUE;
                *gMorKey = ReadUnaligned64((UINT64*)Data);
                VarData = 2;
                Status = SmmSetVariableInt(VariableName, VendorGuid, Attributes, 1, &VarData);
                return Status;
            }
        }else{//if (gMorLock == FALSE)
            // MOR Variable Locked
            if (DataSize !=8 || *gMorKey == 0)
                return EFI_ACCESS_DENIED;
            if (*gMorKey != ReadUnaligned64((UINT64*)Data)){
                //Change lock state when key mismatch
                *gMorKey = 0;
				//[AMIER-011] >>
                VarData = 1;
                Status = SmmSetVariableInt(VariableName, VendorGuid, Attributes, 1, &VarData);
				//[AMIER-011] <<
                return EFI_ACCESS_DENIED;
            }
            gMorLock = FALSE;
            *gMorKey = 0;
            VarData = 0;
            Status = SmmSetVariableInt(VariableName, VendorGuid, Attributes, 1, &VarData);
            return Status;
        }

    }
    if(  !StrCmp (VariableName, L"MemoryOverwriteRequestControl")
            && CompareGuid (VendorGuid, &gMemoryOverwriteRequestControlGuid)
    ){
        if ( Attributes != (EFI_VARIABLE_BOOTSERVICE_ACCESS | EFI_VARIABLE_RUNTIME_ACCESS | EFI_VARIABLE_NON_VOLATILE)
                || (DataSize != 1))
            return EFI_INVALID_PARAMETER;
        if ( Data != NULL && *(UINT8*)Data > 1 && *(UINT8*)Data != 0x11)
            return EFI_INVALID_PARAMETER;
        if (gMorLock == FALSE)
            return EFI_UNSUPPORTED;
        else
            return EFI_ACCESS_DENIED;
    }

    return EFI_UNSUPPORTED;
}

EFI_STATUS EFIAPI SmmSetVariable(
    IN CHAR16 *VariableName, IN EFI_GUID *VendorGuid,
    IN UINT32 Attributes, IN UINTN DataSize, IN VOID *Data
)
{
    EFI_STATUS Status;

    // Special handling of the Memory Override Request Variables
    Status = CheckForMorVariables (
        VariableName, VendorGuid,
        Attributes, DataSize, Data
    );
    if (Status != EFI_UNSUPPORTED){
        return Status;
    }

    return SmmSetVariableInt(VariableName, VendorGuid, Attributes, DataSize, Data);
}

/**
    This function sets Var with specific GUID, Name and attributes in SMM
    synchronizing Varstors before and after operation

    @param VariableName pointer to Var Name in Unicode
    @param VendorGuid pointer to Var GUID
    @param Attributes Attributes of the Var
    @param DataSize size of Var
    @param Data Pointer to memory where Var data is stored

    @retval EFI_STATUS based on result

**/
EFI_STATUS EFIAPI SmmSetVariableNoLock(
    IN CHAR16 *VariableName, IN EFI_GUID *VendorGuid,
    IN UINT32 Attributes, IN UINTN DataSize, IN VOID *Data
)
{
    EFI_STATUS Status;

    LockEnforcementIsActive = FALSE;
    Status = SmmSetVariable(VariableName, VendorGuid, Attributes, DataSize, Data);
    LockEnforcementIsActive = TRUE;

    return Status;
}

EFI_STATUS EFIAPI SmmReadyToLockCallback(
  IN CONST EFI_GUID *Protocol, IN VOID *Interface, IN EFI_HANDLE Handle
){
    EFI_STATUS Status;
    SwitchToBds();
    ResetMorVariables();
    Status = LockVariablePolicy ();
    ASSERT_EFI_ERROR (Status);
    VarCheckLibInitializeAtEndOfDxe (NULL);
    return EFI_SUCCESS;
}

/**
  Dummy implementation of the lock function used by Variable Policy code.

  Locks are not used in MM environment.

  @param  Lock         A pointer to the lock to acquire.

**/
VOID
AcquireLockOnlyAtBootTime (
  IN EFI_LOCK                             *Lock
  )
{
}


/**
  Dummy implementation of the lock function used by Variable Policy code.

  Locks are not used in MM environment.

  @param  Lock         A pointer to the lock to release.

**/
VOID
ReleaseLockOnlyAtBootTime (
  IN EFI_LOCK                             *Lock
  )
{
}

EFI_STATUS NvramMmInit(){
    EFI_STATUS Status;
    VOID *Registration;
    EFI_HANDLE  Handle = NULL;

    InitMorVariables();
    AuthVariableServiceInitSMM();
    InitWhenLockIsAvailable();
    Status = NvramSmiEntry();
    ASSERT_EFI_ERROR(Status);
    if (EFI_ERROR(Status)) return Status;
    Status = gMmst->MmRegisterProtocolNotify(&gEfiSmmReadyToLockProtocolGuid,SmmReadyToLockCallback,&Registration);
    ASSERT_EFI_ERROR (Status);
    Status = gMmst->MmInstallProtocolInterface(
        &Handle, &gEfiSmmVariableProtocolGuid, EFI_NATIVE_INTERFACE, &SmmVariable
    );
    ASSERT_EFI_ERROR (Status);
    Status = gMmst->MmInstallProtocolInterface(
        &Handle, &gAmiSmmNvramUpdateProtocolGuid, EFI_NATIVE_INTERFACE, &AmiNvramUpdate
    );
    ASSERT_EFI_ERROR (Status);
    Status = gMmst->MmInstallProtocolInterface (
                        &Handle, &gEdkiiSmmVarCheckProtocolGuid, EFI_NATIVE_INTERFACE, &SmmVarCheck
    );
    ASSERT_EFI_ERROR (Status);
    Status = gMmst->MmInstallProtocolInterface(
                        &Handle,
                        &gAmiSmmVariablePolicyProtocolGuid,
                        EFI_NATIVE_INTERFACE,
                        &SmmVariablePolicyProtocol
                        );
    ASSERT_EFI_ERROR (Status);
    return Status;
}
