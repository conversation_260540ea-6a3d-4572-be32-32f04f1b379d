#***********************************************************************
#*                                                                     *
#*   Copyright (c) 1985-2019, American Megatrends International LLC.   *
#*                                                                     *
#*      All rights reserved. Subject to AMI licensing agreement.       *
#*                                                                     *
#***********************************************************************
## @file
#   This driver installs CSM Protocols and functions
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = CsmDxe
  FILE_GUID                      = A062CF1F-8473-4aa3-8793-600BC4FFE9A8
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = CsmEntryPoint

[Sources]
  Csm.c
  Csm.h
  CsmBsp.c
  CsmSimpleIn.c
  thunk.c
  CsmOpROM.c
  CsmLib.c
  CsmHwInfo.c
  PciInterrupts.c

[Packages]
  AmiCompatibilityPkg/AmiCompatibilityPkg.dec
  MdePkg/MdePkg.dec
  IntelFrameworkPkg/IntelFrameworkPkg.dec
  IntelFrameworkModulePkg/IntelFrameworkModulePkg.dec
  AmiModulePkg/AmiModulePkg.dec
  AmiChipsetModulePkg/AmiChipsetModulePkg.dec

[Protocols]
  gEfiLegacyBiosProtocolGuid
  gEfiLegacyBiosPlatformProtocolGuid
  gEfiSimpleTextInProtocolGuid
  gEfiSimpleTextInputExProtocolGuid
  gAmiEfiKeycodeProtocolGuid
  gEfiLegacyRegion2ProtocolGuid
  gEfiPciIoProtocolGuid
  gEfiLegacyBiosExtProtocolGuid
  gBdsAllDriversConnectedProtocolGuid
  gEfiMpServiceProtocolGuid
  gEfiTimerArchProtocolGuid
  gEfiPciRootBridgeIoProtocolGuid
  gEfiSimpleTextOutProtocolGuid
  gEfiSioProtocolGuid
  gEfiBlockIoProtocolGuid
  gEfiSerialIoProtocolGuid
  gEfiSimplePointerProtocolGuid
  gEfiFirmwareVolume2ProtocolGuid
  gAint13ProtocolGuid
  gEfiDiskInfoProtocolGuid
  gEfiLegacy8259ProtocolGuid
  gEfiLegacyInterruptProtocolGuid
  gEfiLoadedImageProtocolGuid
  gAmiOemCsm16BinaryGuid
  gAmiExtPciBusProtocolGuid
  gAmiLegacyBootProtocolGuid
  gAmiPciIrqProgramGuid
  gEfiUsbProtocolGuid
  gAmiCsmStartedProtocolGuid
  gOpromStartEndProtocolGuid
  gEfiDevicePathProtocolGuid
  gAmiBoardInfo2ProtocolGuid
  gAmiCsmVideoPolicyProtocolGuid
  gAmiOpromPolicyProtocolGuid
  gAmiCsmOpromPolicyProtocolGuid
  gEfiConsoleControlProtocolGuid
  gEfiGraphicsOutputProtocolGuid
  gEfiRscHandlerProtocolGuid

[Guids]
  gWinCeGuid
  gAmiCsmThunkDriverGuid
  gAmiBeforeLegacyBootEventGuid
  gEfiEventExitBootServicesGuid
  
[LibraryClasses]
  UefiDriverEntryPoint
  PcdLib 
  PrintLib
  AmiSdlLib
  DebugLib
  DxeServicesTableLib
  UefiLib
  AmiPirqRouterLib

[Depex]
  gEfiLegacyRegion2ProtocolGuid AND
  gEfiLegacy8259ProtocolGuid AND
  gEfiLegacyInterruptProtocolGuid AND
  gEfiCpuArchProtocolGuid AND
  gAmiBoardInfo2ProtocolGuid AND
  gEfiRscHandlerProtocolGuid

 [Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdPciExpressBaseAddress
  gEfiMdePkgTokenSpaceGuid.PcdDebugPropertyMask
  gEfiIntelFrameworkModulePkgTokenSpaceGuid.PcdLegacyBiosCacheLegacyRegion

