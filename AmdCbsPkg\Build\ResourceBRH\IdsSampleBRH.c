/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */
//This file is auto generated, do not edit it manually

//SMT Control
IDS_NV_READ_SKIP (IDSNVID_CPU_SMT_CTRL, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CPU_SMT_CTRL_DISABLE:
    break;
  case IDSOPT_CPU_SMT_CTRL_ENABLE:
    break;
  case IDSOPT_CPU_SMT_CTRL_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_REQ_MIN_FREQ, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_CPU_REQ_MIN_FREQ_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_CPU_REQ_MIN_FREQ_MIN);
}

//Enable Requested CPU min frequency
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_EN_REQ_MIN_FREQ, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdCpuReqMinFreqEn)) {
  case IDSOPT_CMN_CPU_EN_REQ_MIN_FREQ_DISABLE:
    break;
  case IDSOPT_CMN_CPU_EN_REQ_MIN_FREQ_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//REP-MOV/STOS Streaming
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_RMSS, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdEnableRMSS)) {
  case IDSOPT_CMN_CPU_RMSS_DISABLED:
    break;
  case IDSOPT_CMN_CPU_RMSS_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//RedirectForReturnDis
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_GEN_W_A05, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdRedirectForReturnDis)) {
  case IDSOPT_CMN_CPU_GEN_W_A05_AUTO:
    break;
  case IDSOPT_CMN_CPU_GEN_W_A05_1:
    break;
  case IDSOPT_CMN_CPU_GEN_W_A05_0:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Platform First Error Handling
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_PFEH, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdCcxCfgPFEHEnable)) {
  case IDSOPT_CMN_CPU_PFEH_ENABLED:
    break;
  case IDSOPT_CMN_CPU_PFEH_DISABLED:
    break;
  case IDSOPT_CMN_CPU_PFEH_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Core Performance Boost
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_CPB, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdCpbMode)) {
  case IDSOPT_CMN_CPU_CPB_DISABLED:
    break;
  case IDSOPT_CMN_CPU_CPB_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Global C-state Control
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_GLOBAL_CSTATE_CTRL, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdCStateMode)) {
  case IDSOPT_CMN_CPU_GLOBAL_CSTATE_CTRL_DISABLED:
    break;
  case IDSOPT_CMN_CPU_GLOBAL_CSTATE_CTRL_ENABLED:
    break;
  case IDSOPT_CMN_CPU_GLOBAL_CSTATE_CTRL_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Power Supply Idle Control
IDS_NV_READ_SKIP (IDSNVID_CMN_GNB_POWER_SUPPLY_IDLE_CTRL, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdPowerSupplyIdleControl)) {
  case IDSOPT_CMN_GNB_POWER_SUPPLY_IDLE_CTRL_LOWCURRENTIDLE:
    break;
  case IDSOPT_CMN_GNB_POWER_SUPPLY_IDLE_CTRL_TYPICALCURRENTIDLE:
    break;
  case IDSOPT_CMN_GNB_POWER_SUPPLY_IDLE_CTRL_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Streaming Stores Control
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_STREAMING_STORES_CTRL, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdStreamingStoresCtrl)) {
  case IDSOPT_CMN_CPU_STREAMING_STORES_CTRL_DISABLED:
    break;
  case IDSOPT_CMN_CPU_STREAMING_STORES_CTRL_ENABLED:
    break;
  case IDSOPT_CMN_CPU_STREAMING_STORES_CTRL_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Local APIC Mode
IDS_NV_READ_SKIP (IDSNVID_DBG_CPU_L_APIC_MODE, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_CPU_L_APIC_MODE_XAPIC:
    break;
  case IDSOPT_DBG_CPU_L_APIC_MODE_X2APIC:
    break;
  case IDSOPT_DBG_CPU_L_APIC_MODE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//ACPI _CST C1 Declaration
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_CST_C1_CTRL, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdAcpiCstC1)) {
  case IDSOPT_CMN_CPU_CST_C1_CTRL_DISABLED:
    break;
  case IDSOPT_CMN_CPU_CST_C1_CTRL_ENABLED:
    break;
  case IDSOPT_CMN_CPU_CST_C1_CTRL_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_CST_C2_LATENCY, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_CPU_CST_C2_LATENCY_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_CPU_CST_C2_LATENCY_MIN);
}

//MCA error thresh enable
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_MCA_ERR_THRESH_EN, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdMcaErrThreshEn)) {
  case IDSOPT_CMN_CPU_MCA_ERR_THRESH_EN_FALSE:
    break;
  case IDSOPT_CMN_CPU_MCA_ERR_THRESH_EN_TRUE:
    break;
  case IDSOPT_CMN_CPU_MCA_ERR_THRESH_EN_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_MCA_ERR_THRESH_COUNT, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_CPU_MCA_ERR_THRESH_COUNT_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_CPU_MCA_ERR_THRESH_COUNT_MIN);
}

//MCA FruText
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_MCA_FRU_TEXT_EN, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdMcaFruTextEnable)) {
  case IDSOPT_CMN_CPU_MCA_FRU_TEXT_EN_FALSE:
    break;
  case IDSOPT_CMN_CPU_MCA_FRU_TEXT_EN_TRUE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//SMU and PSP Debug Mode
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_SMU_PSP_DEBUG_MODE, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_CPU_SMU_PSP_DEBUG_MODE_DISABLED:
    break;
  case IDSOPT_CMN_CPU_SMU_PSP_DEBUG_MODE_ENABLED:
    break;
  case IDSOPT_CMN_CPU_SMU_PSP_DEBUG_MODE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//PPIN Opt-in
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_PPIN_CTRL, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_CPU_PPIN_CTRL_DISABLED:
    break;
  case IDSOPT_CMN_CPU_PPIN_CTRL_ENABLED:
    break;
  case IDSOPT_CMN_CPU_PPIN_CTRL_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//SMEE
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_SMEE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdSmee)) {
  case IDSOPT_CMN_CPU_SMEE_DISABLE:
    break;
  case IDSOPT_CMN_CPU_SMEE_ENABLE:
    break;
  case IDSOPT_CMN_CPU_SMEE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//SEV Control
IDS_NV_READ_SKIP (IDSNVID_PSP_SEV_CTRL, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_PSP_SEV_CTRL_ENABLE:
    break;
  case IDSOPT_PSP_SEV_CTRL_DISABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_SEV_ASID_SPACE_LIMIT, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_CPU_SEV_ASID_SPACE_LIMIT_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_CPU_SEV_ASID_SPACE_LIMIT_MIN);
}

//SNP Memory (RMP Table) Coverage
IDS_NV_READ_SKIP (IDSNVID_DBG_CPU_SNP_MEM_COVER, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdSnpMemCover)) {
  case IDSOPT_DBG_CPU_SNP_MEM_COVER_DISABLED:
    break;
  case IDSOPT_DBG_CPU_SNP_MEM_COVER_ENABLED:
    break;
  case IDSOPT_DBG_CPU_SNP_MEM_COVER_CUSTOM:
    break;
  case IDSOPT_DBG_CPU_SNP_MEM_COVER_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_DBG_CPU_SNP_MEM_SIZE_COVER, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DBG_CPU_SNP_MEM_SIZE_COVER_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DBG_CPU_SNP_MEM_SIZE_COVER_MIN);
}

//RMP Coverage for 64Bit MMIO Ranges
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU64_BIT_MMIO_COVERAGE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdRmpCover64BitMMIORanges)) {
  case IDSOPT_CMN_CPU64_BIT_MMIO_COVERAGE_DISABLED:
    break;
  case IDSOPT_CMN_CPU64_BIT_MMIO_COVERAGE_ENABLED:
    break;
  case IDSOPT_CMN_CPU64_BIT_MMIO_COVERAGE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_CPU64_BIT_MMIO_RMP_S0_RB_MASK, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_CPU64_BIT_MMIO_RMP_S0_RB_MASK_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_CPU64_BIT_MMIO_RMP_S0_RB_MASK_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_CPU64_BIT_MMIO_RMP_S1_RB_MASK, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_CPU64_BIT_MMIO_RMP_S1_RB_MASK_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_CPU64_BIT_MMIO_RMP_S1_RB_MASK_MIN);
}

//Split RMP Table
IDS_NV_READ_SKIP (IDSNVID_DBG_CPU_SPLIT_RMP, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdSplitRmpTable)) {
  case IDSOPT_DBG_CPU_SPLIT_RMP_DISABLED:
    break;
  case IDSOPT_DBG_CPU_SPLIT_RMP_ENABLED:
    break;
  case IDSOPT_DBG_CPU_SPLIT_RMP_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Segmented RMP Table
IDS_NV_READ_SKIP (IDSNVID_DBG_CPU_SEGMENTED_RMP, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdSegmentedRmp)) {
  case IDSOPT_DBG_CPU_SEGMENTED_RMP_DISABLED:
    break;
  case IDSOPT_DBG_CPU_SEGMENTED_RMP_ENABLED:
    break;
  case IDSOPT_DBG_CPU_SEGMENTED_RMP_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//RMP Segment Size
IDS_NV_READ_SKIP (IDSNVID_DBG_CPU_RMP_SEGMENT_SIZE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdRmpSegSize)) {
  case IDSOPT_DBG_CPU_RMP_SEGMENT_SIZE_64GB:
    break;
  case IDSOPT_DBG_CPU_RMP_SEGMENT_SIZE_128GB:
    break;
  case IDSOPT_DBG_CPU_RMP_SEGMENT_SIZE_256GB:
    break;
  case IDSOPT_DBG_CPU_RMP_SEGMENT_SIZE_512GB:
    break;
  case IDSOPT_DBG_CPU_RMP_SEGMENT_SIZE_1024GB:
    break;
  case IDSOPT_DBG_CPU_RMP_SEGMENT_SIZE_2048GB:
    break;
  case IDSOPT_DBG_CPU_RMP_SEGMENT_SIZE_4096GB:
    break;
  case IDSOPT_DBG_CPU_RMP_SEGMENT_SIZE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Action on BIST Failure
IDS_NV_READ_SKIP (IDSNVID_CMN_ACTION_ON_BIST_FAILURE, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_ACTION_ON_BIST_FAILURE_DONOTHING:
    break;
  case IDSOPT_CMN_ACTION_ON_BIST_FAILURE_DOWNCCD:
    break;
  case IDSOPT_CMN_ACTION_ON_BIST_FAILURE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Enhanced REP MOVSB/STOSB (ERSM)
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_ERMS, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdEnableERMS)) {
  case IDSOPT_CMN_CPU_ERMS_DISABLED:
    break;
  case IDSOPT_CMN_CPU_ERMS_ENABLED:
    break;
  case IDSOPT_CMN_CPU_ERMS_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Log Transparent Errors
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_LOG_TRANSPARENT_ERRORS, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdTransparentErrorLoggingEnable)) {
  case IDSOPT_CMN_CPU_LOG_TRANSPARENT_ERRORS_AUTO:
    break;
  case IDSOPT_CMN_CPU_LOG_TRANSPARENT_ERRORS_DISABLED:
    break;
  case IDSOPT_CMN_CPU_LOG_TRANSPARENT_ERRORS_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//AVX512
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_AVX512, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdCcxEnableAvx512)) {
  case IDSOPT_CMN_CPU_AVX512_DISABLED:
    break;
  case IDSOPT_CMN_CPU_AVX512_ENABLED:
    break;
  case IDSOPT_CMN_CPU_AVX512_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//ERMSB Caching Behavior
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_DIS_FST_STR_ERMSB, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdCcxDisFstStrErmsb)) {
  case IDSOPT_CMN_CPU_DIS_FST_STR_ERMSB_DISABLED:
    break;
  case IDSOPT_CMN_CPU_DIS_FST_STR_ERMSB_ENABLED:
    break;
  case IDSOPT_CMN_CPU_DIS_FST_STR_ERMSB_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//MONITOR and MWAIT disable
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_MON_MWAIT_DIS, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdMonMwaitDis)) {
  case IDSOPT_CMN_CPU_MON_MWAIT_DIS_ENABLED:
    break;
  case IDSOPT_CMN_CPU_MON_MWAIT_DIS_DISABLED:
    break;
  case IDSOPT_CMN_CPU_MON_MWAIT_DIS_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//CPU Speculative Store Modes
IDS_NV_READ_SKIP (IDSNVID_CPU_SPECULATIVE_STORE_MODES, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdCpuSpeculativeStoreMode)) {
  case IDSOPT_CPU_SPECULATIVE_STORE_MODES_AUTO:
    break;
  case IDSOPT_CPU_SPECULATIVE_STORE_MODES_BALANCED:
    break;
  case IDSOPT_CPU_SPECULATIVE_STORE_MODES_MORESPECULATIVE:
    break;
  case IDSOPT_CPU_SPECULATIVE_STORE_MODES_LESSSPECULATIVE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Fast Short REP MOVSB (FSRM)
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_FSRM, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdEnableFSRM)) {
  case IDSOPT_CMN_CPU_FSRM_AUTO:
    break;
  case IDSOPT_CMN_CPU_FSRM_ENABLED:
    break;
  case IDSOPT_CMN_CPU_FSRM_DISABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//PauseCntSel_1_0
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_PAUSE_CNT_SEL_1_0, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdCpuPauseCntSel_1_0)) {
  case IDSOPT_CMN_CPU_PAUSE_CNT_SEL_1_0_AUTO:
    break;
  case IDSOPT_CMN_CPU_PAUSE_CNT_SEL_1_0_16CYCLES:
    break;
  case IDSOPT_CMN_CPU_PAUSE_CNT_SEL_1_0_32CYCLES:
    break;
  case IDSOPT_CMN_CPU_PAUSE_CNT_SEL_1_0_64CYCLES:
    break;
  case IDSOPT_CMN_CPU_PAUSE_CNT_SEL_1_0_128CYCLES:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Prefetch/Request Throttle
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_PF_REQ_THR_EN, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_CPU_PF_REQ_THR_EN_DISABLE:
    break;
  case IDSOPT_CMN_CPU_PF_REQ_THR_EN_ENABLE:
    break;
  case IDSOPT_CMN_CPU_PF_REQ_THR_EN_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//CMC H/W Error Notification type
IDS_NV_READ_SKIP (IDSNVID_CMN_CMC_NOTIFICATION_TYPE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdCmcNotificationType)) {
  case IDSOPT_CMN_CMC_NOTIFICATION_TYPE_POLLED:
    break;
  case IDSOPT_CMN_CMC_NOTIFICATION_TYPE_CMCI:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Scan Dump Debug Enable
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_SCAN_DUMP_DBG_EN, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_CPU_SCAN_DUMP_DBG_EN_DISABLE:
    break;
  case IDSOPT_CMN_CPU_SCAN_DUMP_DBG_EN_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//MCAX 64 bank support
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_MCAX64_BANK_SUPPORT, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_CPU_MCAX64_BANK_SUPPORT_DISABLED:
    break;
  case IDSOPT_CMN_CPU_MCAX64_BANK_SUPPORT_ENABLED:
    break;
  case IDSOPT_CMN_CPU_MCAX64_BANK_SUPPORT_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Adaptive Allocation (AA)
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_ADAPTIVE_ALLOC, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdCpuAdaptiveAlloc)) {
  case IDSOPT_CMN_CPU_ADAPTIVE_ALLOC_ENABLED:
    break;
  case IDSOPT_CMN_CPU_ADAPTIVE_ALLOC_DISABLED:
    break;
  case IDSOPT_CMN_CPU_ADAPTIVE_ALLOC_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Latency Under Load (LUL)
IDS_NV_READ_SKIP (IDSNVID_CPU_LATENCY_UNDER_LOAD, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CPU_LATENCY_UNDER_LOAD_AUTO:
    break;
  case IDSOPT_CPU_LATENCY_UNDER_LOAD_ENABLED:
    break;
  case IDSOPT_CPU_LATENCY_UNDER_LOAD_DISABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Core Trace Dump Enable
IDS_NV_READ_SKIP (IDSNVID_CMN_CORE_TRACE_DUMP_EN, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdCoreTraceDumpEnable)) {
  case IDSOPT_CMN_CORE_TRACE_DUMP_EN_DISABLE:
    break;
  case IDSOPT_CMN_CORE_TRACE_DUMP_EN_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//FP512
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_F_P512, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_CPU_F_P512_DISABLED:
    break;
  case IDSOPT_CMN_CPU_F_P512_ENABLED:
    break;
  case IDSOPT_CMN_CPU_F_P512_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//AMD_ERMSB Reporting
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_AMD_ERMSB_REPO, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdCcxErmsbRepo)) {
  case IDSOPT_CMN_CPU_AMD_ERMSB_REPO_AUTO:
    break;
  case IDSOPT_CMN_CPU_AMD_ERMSB_REPO_DISABLE:
    break;
  case IDSOPT_CMN_CPU_AMD_ERMSB_REPO_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//OC Mode
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_OC_MODE, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_CPU_OC_MODE_NORMALOPERATION:
    break;
  case IDSOPT_CMN_CPU_OC_MODE_CUSTOMIZED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DownCore Mode
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_DOWNCORE_MODE, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_CPU_DOWNCORE_MODE_ENABLEMENTOPTION:
    break;
  case IDSOPT_CMN_CPU_DOWNCORE_MODE_BITMAP:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Custom Pstate0
IDS_NV_READ_SKIP (IDSNVID_CPU_PST_CUSTOM_P0, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdCcxP0Setting)) {
  case IDSOPT_CPU_PST_CUSTOM_P0_CUSTOM:
    break;
  case IDSOPT_CPU_PST_CUSTOM_P0_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CPU_PST0_FREQ, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_PST0_FREQ_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_PST0_FREQ_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CPU_COF_P0, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_COF_P0_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_COF_P0_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CPU_VOLTAGE_P0, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_VOLTAGE_P0_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_VOLTAGE_P0_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CPU_PST0_FID, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_PST0_FID_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_PST0_FID_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CPU_PST0_VID, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_PST0_VID_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_PST0_VID_MIN);
}

//Custom Pstate1
IDS_NV_READ_SKIP (IDSNVID_CPU_PST_CUSTOM_P1, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CPU_PST_CUSTOM_P1_DISABLED:
    break;
  case IDSOPT_CPU_PST_CUSTOM_P1_CUSTOM:
    break;
  case IDSOPT_CPU_PST_CUSTOM_P1_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CPU_COF_P1, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_COF_P1_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_COF_P1_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CPU_VOLTAGE_P1, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_VOLTAGE_P1_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_VOLTAGE_P1_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CPU_PST1_FID, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_PST1_FID_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_PST1_FID_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CPU_PST1_VID, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_PST1_VID_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_PST1_VID_MIN);
}

//Custom Pstate2
IDS_NV_READ_SKIP (IDSNVID_CPU_PST_CUSTOM_P2, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CPU_PST_CUSTOM_P2_DISABLED:
    break;
  case IDSOPT_CPU_PST_CUSTOM_P2_CUSTOM:
    break;
  case IDSOPT_CPU_PST_CUSTOM_P2_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CPU_COF_P2, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_COF_P2_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_COF_P2_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CPU_VOLTAGE_P2, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_VOLTAGE_P2_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_VOLTAGE_P2_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CPU_PST2_FID, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_PST2_FID_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_PST2_FID_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CPU_PST2_VID, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_PST2_VID_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_PST2_VID_MIN);
}

//Custom Pstate3
IDS_NV_READ_SKIP (IDSNVID_CPU_PST_CUSTOM_P3, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CPU_PST_CUSTOM_P3_DISABLED:
    break;
  case IDSOPT_CPU_PST_CUSTOM_P3_CUSTOM:
    break;
  case IDSOPT_CPU_PST_CUSTOM_P3_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CPU_COF_P3, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_COF_P3_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_COF_P3_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CPU_VOLTAGE_P3, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_VOLTAGE_P3_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_VOLTAGE_P3_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CPU_PST3_FID, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_PST3_FID_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_PST3_FID_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CPU_PST3_VID, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_PST3_VID_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_PST3_VID_MIN);
}

//Custom Pstate4
IDS_NV_READ_SKIP (IDSNVID_CPU_PST_CUSTOM_P4, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CPU_PST_CUSTOM_P4_DISABLED:
    break;
  case IDSOPT_CPU_PST_CUSTOM_P4_CUSTOM:
    break;
  case IDSOPT_CPU_PST_CUSTOM_P4_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CPU_COF_P4, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_COF_P4_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_COF_P4_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CPU_VOLTAGE_P4, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_VOLTAGE_P4_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_VOLTAGE_P4_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CPU_PST4_FID, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_PST4_FID_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_PST4_FID_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CPU_PST4_VID, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_PST4_VID_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_PST4_VID_MIN);
}

//Custom Pstate5
IDS_NV_READ_SKIP (IDSNVID_CPU_PST_CUSTOM_P5, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CPU_PST_CUSTOM_P5_DISABLED:
    break;
  case IDSOPT_CPU_PST_CUSTOM_P5_CUSTOM:
    break;
  case IDSOPT_CPU_PST_CUSTOM_P5_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CPU_COF_P5, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_COF_P5_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_COF_P5_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CPU_VOLTAGE_P5, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_VOLTAGE_P5_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_VOLTAGE_P5_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CPU_PST5_FID, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_PST5_FID_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_PST5_FID_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CPU_PST5_VID, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_PST5_VID_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_PST5_VID_MIN);
}

//Custom Pstate6
IDS_NV_READ_SKIP (IDSNVID_CPU_PST_CUSTOM_P6, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CPU_PST_CUSTOM_P6_DISABLED:
    break;
  case IDSOPT_CPU_PST_CUSTOM_P6_CUSTOM:
    break;
  case IDSOPT_CPU_PST_CUSTOM_P6_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CPU_COF_P6, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_COF_P6_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_COF_P6_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CPU_VOLTAGE_P6, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_VOLTAGE_P6_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_VOLTAGE_P6_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CPU_PST6_FID, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_PST6_FID_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_PST6_FID_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CPU_PST6_VID, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_PST6_VID_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_PST6_VID_MIN);
}

//Custom Pstate7
IDS_NV_READ_SKIP (IDSNVID_CPU_PST_CUSTOM_P7, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CPU_PST_CUSTOM_P7_DISABLED:
    break;
  case IDSOPT_CPU_PST_CUSTOM_P7_CUSTOM:
    break;
  case IDSOPT_CPU_PST_CUSTOM_P7_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CPU_COF_P7, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_COF_P7_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_COF_P7_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CPU_VOLTAGE_P7, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_VOLTAGE_P7_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_VOLTAGE_P7_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CPU_PST7_FID, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_PST7_FID_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_PST7_FID_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CPU_PST7_VID, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CPU_PST7_VID_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CPU_PST7_VID_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_CCD0_DOWNCORE_BIT_MAP, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_CPU_CCD0_DOWNCORE_BIT_MAP_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_CPU_CCD0_DOWNCORE_BIT_MAP_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_CCD1_DOWNCORE_BIT_MAP, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_CPU_CCD1_DOWNCORE_BIT_MAP_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_CPU_CCD1_DOWNCORE_BIT_MAP_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_CCD2_DOWNCORE_BIT_MAP, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_CPU_CCD2_DOWNCORE_BIT_MAP_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_CPU_CCD2_DOWNCORE_BIT_MAP_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_CCD3_DOWNCORE_BIT_MAP, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_CPU_CCD3_DOWNCORE_BIT_MAP_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_CPU_CCD3_DOWNCORE_BIT_MAP_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_CCD4_DOWNCORE_BIT_MAP, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_CPU_CCD4_DOWNCORE_BIT_MAP_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_CPU_CCD4_DOWNCORE_BIT_MAP_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_CCD5_DOWNCORE_BIT_MAP, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_CPU_CCD5_DOWNCORE_BIT_MAP_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_CPU_CCD5_DOWNCORE_BIT_MAP_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_CCD6_DOWNCORE_BIT_MAP, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_CPU_CCD6_DOWNCORE_BIT_MAP_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_CPU_CCD6_DOWNCORE_BIT_MAP_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_CCD7_DOWNCORE_BIT_MAP, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_CPU_CCD7_DOWNCORE_BIT_MAP_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_CPU_CCD7_DOWNCORE_BIT_MAP_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_CCD8_DOWNCORE_BIT_MAP, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_CPU_CCD8_DOWNCORE_BIT_MAP_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_CPU_CCD8_DOWNCORE_BIT_MAP_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_CCD9_DOWNCORE_BIT_MAP, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_CPU_CCD9_DOWNCORE_BIT_MAP_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_CPU_CCD9_DOWNCORE_BIT_MAP_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_CCD10_DOWNCORE_BIT_MAP, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_CPU_CCD10_DOWNCORE_BIT_MAP_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_CPU_CCD10_DOWNCORE_BIT_MAP_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_CCD11_DOWNCORE_BIT_MAP, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_CPU_CCD11_DOWNCORE_BIT_MAP_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_CPU_CCD11_DOWNCORE_BIT_MAP_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_CCD12_DOWNCORE_BIT_MAP, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_CPU_CCD12_DOWNCORE_BIT_MAP_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_CPU_CCD12_DOWNCORE_BIT_MAP_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_CCD13_DOWNCORE_BIT_MAP, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_CPU_CCD13_DOWNCORE_BIT_MAP_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_CPU_CCD13_DOWNCORE_BIT_MAP_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_CCD14_DOWNCORE_BIT_MAP, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_CPU_CCD14_DOWNCORE_BIT_MAP_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_CPU_CCD14_DOWNCORE_BIT_MAP_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_CCD15_DOWNCORE_BIT_MAP, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_CPU_CCD15_DOWNCORE_BIT_MAP_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_CPU_CCD15_DOWNCORE_BIT_MAP_MIN);
}

//CCD Control
IDS_NV_READ_SKIP (IDSNVID_CPU_CCD_CTRL, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CPU_CCD_CTRL_AUTO:
    break;
  case IDSOPT_CPU_CCD_CTRL_2CCDS:
    break;
  case IDSOPT_CPU_CCD_CTRL_4CCDS:
    break;
  case IDSOPT_CPU_CCD_CTRL_6CCDS:
    break;
  case IDSOPT_CPU_CCD_CTRL_8CCDS:
    break;
  case IDSOPT_CPU_CCD_CTRL_10CCDS:
    break;
  case IDSOPT_CPU_CCD_CTRL_12CCDS:
    break;
  case IDSOPT_CPU_CCD_CTRL_14CCDS:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Core control
IDS_NV_READ_SKIP (IDSNVID_CPU_CORE_CTRL, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CPU_CORE_CTRL_AUTO:
    break;
  case IDSOPT_CPU_CORE_CTRL_ONE10:
    break;
  case IDSOPT_CPU_CORE_CTRL_TWO20:
    break;
  case IDSOPT_CPU_CORE_CTRL_THREE30:
    break;
  case IDSOPT_CPU_CORE_CTRL_FOUR40:
    break;
  case IDSOPT_CPU_CORE_CTRL_FIVE50:
    break;
  case IDSOPT_CPU_CORE_CTRL_SIX60:
    break;
  case IDSOPT_CPU_CORE_CTRL_SEVEN70:
    break;
  case IDSOPT_CPU_CORE_CTRL_EIGHT80:
    break;
  case IDSOPT_CPU_CORE_CTRL_NINE90:
    break;
  case IDSOPT_CPU_CORE_CTRL_TEN100:
    break;
  case IDSOPT_CPU_CORE_CTRL_ELEVEN110:
    break;
  case IDSOPT_CPU_CORE_CTRL_TWELVE120:
    break;
  case IDSOPT_CPU_CORE_CTRL_THIRTEEN130:
    break;
  case IDSOPT_CPU_CORE_CTRL_FOURTEEN140:
    break;
  case IDSOPT_CPU_CORE_CTRL_FIFTEEN150:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//L1 Stream HW Prefetcher
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_L1_STREAM_HW_PREFETCHER, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdL1StreamPrefetcher)) {
  case IDSOPT_CMN_CPU_L1_STREAM_HW_PREFETCHER_DISABLE:
    break;
  case IDSOPT_CMN_CPU_L1_STREAM_HW_PREFETCHER_ENABLE:
    break;
  case IDSOPT_CMN_CPU_L1_STREAM_HW_PREFETCHER_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//L1 Stride Prefetcher
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_L1_STRIDE_PREFETCHER, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdL1StridePrefetcher)) {
  case IDSOPT_CMN_CPU_L1_STRIDE_PREFETCHER_DISABLE:
    break;
  case IDSOPT_CMN_CPU_L1_STRIDE_PREFETCHER_ENABLE:
    break;
  case IDSOPT_CMN_CPU_L1_STRIDE_PREFETCHER_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//L1 Region Prefetcher
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_L1_REGION_PREFETCHER, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdL1RegionPrefetcher)) {
  case IDSOPT_CMN_CPU_L1_REGION_PREFETCHER_DISABLE:
    break;
  case IDSOPT_CMN_CPU_L1_REGION_PREFETCHER_ENABLE:
    break;
  case IDSOPT_CMN_CPU_L1_REGION_PREFETCHER_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//L2 Stream HW Prefetcher
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_L2_STREAM_HW_PREFETCHER, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdL2StreamPrefetcher)) {
  case IDSOPT_CMN_CPU_L2_STREAM_HW_PREFETCHER_DISABLE:
    break;
  case IDSOPT_CMN_CPU_L2_STREAM_HW_PREFETCHER_ENABLE:
    break;
  case IDSOPT_CMN_CPU_L2_STREAM_HW_PREFETCHER_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//L2 Up/Down Prefetcher
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_L2_UP_DOWN_PREFETCHER, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdL2UpDownPrefetcher)) {
  case IDSOPT_CMN_CPU_L2_UP_DOWN_PREFETCHER_DISABLE:
    break;
  case IDSOPT_CMN_CPU_L2_UP_DOWN_PREFETCHER_ENABLE:
    break;
  case IDSOPT_CMN_CPU_L2_UP_DOWN_PREFETCHER_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//L1 Burst Prefetch Mode
IDS_NV_READ_SKIP (IDSNVID_CMN_CPU_L1_BURST_PREFETCH_MODE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdL1BurstPrefetch)) {
  case IDSOPT_CMN_CPU_L1_BURST_PREFETCH_MODE_DISABLE:
    break;
  case IDSOPT_CMN_CPU_L1_BURST_PREFETCH_MODE_ENABLE:
    break;
  case IDSOPT_CMN_CPU_L1_BURST_PREFETCH_MODE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Core Watchdog Timer Enable
IDS_NV_READ_SKIP (IDSNVID_DBG_CPU_GEN_CPU_WDT, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdCpuWdtEn)) {
  case IDSOPT_DBG_CPU_GEN_CPU_WDT_DISABLED:
    break;
  case IDSOPT_DBG_CPU_GEN_CPU_WDT_ENABLED:
    break;
  case IDSOPT_DBG_CPU_GEN_CPU_WDT_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Core Watchdog Timer Interval
IDS_NV_READ_SKIP (IDSNVID_DBG_CPU_GEN_CPU_WDT_TIMEOUT, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet16 (PcdAmdCpuWdtTimeout)) {
  case IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_2681S:
    break;
  case IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_1340S:
    break;
  case IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_66941MS:
    break;
  case IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_33405MS:
    break;
  case IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_16637MS:
    break;
  case IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_8253MS:
    break;
  case IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_4061MS:
    break;
  case IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_20970MS:
    break;
  case IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_10484MS:
    break;
  case IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_5241MS:
    break;
  case IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_2620MS:
    break;
  case IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_1309MS:
    break;
  case IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_65408US:
    break;
  case IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_3264US:
    break;
  case IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_16256US:
    break;
  case IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_8064US:
    break;
  case IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_3968US:
    break;
  case IDSOPT_DBG_CPU_GEN_CPU_WDT_TIMEOUT_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DF Watchdog Timer Interval
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_WDT_INTERVAL, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdFabricWdtCntSel)) {
  case IDSOPT_DF_CMN_WDT_INTERVAL_AUTO:
    break;
  case IDSOPT_DF_CMN_WDT_INTERVAL_41MS:
    break;
  case IDSOPT_DF_CMN_WDT_INTERVAL_166MS:
    break;
  case IDSOPT_DF_CMN_WDT_INTERVAL_334MS:
    break;
  case IDSOPT_DF_CMN_WDT_INTERVAL_669MS:
    break;
  case IDSOPT_DF_CMN_WDT_INTERVAL_134SECONDS:
    break;
  case IDSOPT_DF_CMN_WDT_INTERVAL_268SECONDS:
    break;
  case IDSOPT_DF_CMN_WDT_INTERVAL_536SECONDS:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Disable DF to external IP SyncFloodPropagation
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_EXT_IP_SYNC_FLOOD_PROP, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_CMN_EXT_IP_SYNC_FLOOD_PROP_SYNCFLOODDISABLED:
    break;
  case IDSOPT_DF_CMN_EXT_IP_SYNC_FLOOD_PROP_SYNCFLOODENABLED:
    break;
  case IDSOPT_DF_CMN_EXT_IP_SYNC_FLOOD_PROP_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sync Flood Propagation to DF Components
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_DIS_SYNC_FLOOD_PROP, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_CMN_DIS_SYNC_FLOOD_PROP_SYNCFLOODDISABLED:
    break;
  case IDSOPT_DF_CMN_DIS_SYNC_FLOOD_PROP_SYNCFLOODENABLED:
    break;
  case IDSOPT_DF_CMN_DIS_SYNC_FLOOD_PROP_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Freeze DF module queues on error
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_FREEZE_QUEUE_ERROR, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdFabricImmSyncFloodOnFatalErrCtrl)) {
  case IDSOPT_DF_CMN_FREEZE_QUEUE_ERROR_DISABLED:
    break;
  case IDSOPT_DF_CMN_FREEZE_QUEUE_ERROR_ENABLED:
    break;
  case IDSOPT_DF_CMN_FREEZE_QUEUE_ERROR_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//CC6 memory region encryption
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_CC6_MEM_ENCRYPTION, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_CMN_CC6_MEM_ENCRYPTION_DISABLED:
    break;
  case IDSOPT_DF_CMN_CC6_MEM_ENCRYPTION_ENABLED:
    break;
  case IDSOPT_DF_CMN_CC6_MEM_ENCRYPTION_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//CCD B/W Balance Throttle Level
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_CCD_BW_THROTTLE_LV, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_CMN_CCD_BW_THROTTLE_LV_AUTO:
    break;
  case IDSOPT_DF_CMN_CCD_BW_THROTTLE_LV_LEVEL0:
    break;
  case IDSOPT_DF_CMN_CCD_BW_THROTTLE_LV_LEVEL1:
    break;
  case IDSOPT_DF_CMN_CCD_BW_THROTTLE_LV_LEVEL2:
    break;
  case IDSOPT_DF_CMN_CCD_BW_THROTTLE_LV_LEVEL3:
    break;
  case IDSOPT_DF_CMN_CCD_BW_THROTTLE_LV_LEVEL4:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Number of PCI Segments
IDS_NV_READ_SKIP (IDSNVID_DF_DBG_NUM_PCI_SEGMENTS, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_DBG_NUM_PCI_SEGMENTS_1SEGMENT:
    break;
  case IDSOPT_DF_DBG_NUM_PCI_SEGMENTS_2SEGMENTS:
    break;
  case IDSOPT_DF_DBG_NUM_PCI_SEGMENTS_4SEGMENTS:
    break;
  case IDSOPT_DF_DBG_NUM_PCI_SEGMENTS_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//CCM Throttler
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_CCM_THROT, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_CMN_CCM_THROT_AUTO:
    break;
  case IDSOPT_DF_CMN_CCM_THROT_ENABLED:
    break;
  case IDSOPT_DF_CMN_CCM_THROT_DISABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_DF_CMN_FINE_THROT_HEAVY, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_CMN_FINE_THROT_HEAVY_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_CMN_FINE_THROT_HEAVY_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_CMN_FINE_THROT_LIGHT, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_CMN_FINE_THROT_LIGHT_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_CMN_FINE_THROT_LIGHT_MIN);
}

//Clean Victim FTI Cmd Balancing
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_CLEAN_VIC_FTI_CMD_BAL, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_CMN_CLEAN_VIC_FTI_CMD_BAL_DISABLED:
    break;
  case IDSOPT_DF_CMN_CLEAN_VIC_FTI_CMD_BAL_ENABLED:
    break;
  case IDSOPT_DF_CMN_CLEAN_VIC_FTI_CMD_BAL_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//CCMConfig5[ReqvReqNDImbThr]
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_REQV_REQ_ND_IMB_THR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_CMN_REQV_REQ_ND_IMB_THR_AUTO:
    break;
  case IDSOPT_DF_CMN_REQV_REQ_ND_IMB_THR_1H:
    break;
  case IDSOPT_DF_CMN_REQV_REQ_ND_IMB_THR_2H:
    break;
  case IDSOPT_DF_CMN_REQV_REQ_ND_IMB_THR_3H:
    break;
  case IDSOPT_DF_CMN_REQV_REQ_ND_IMB_THR_4H:
    break;
  case IDSOPT_DF_CMN_REQV_REQ_ND_IMB_THR_5H:
    break;
  case IDSOPT_DF_CMN_REQV_REQ_ND_IMB_THR_6H:
    break;
  case IDSOPT_DF_CMN_REQV_REQ_ND_IMB_THR_7H:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//CXL Strongly Ordered Writes
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_CXL_STRONGLY_ORDERED_WRITES, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_CMN_CXL_STRONGLY_ORDERED_WRITES_DISABLED:
    break;
  case IDSOPT_DF_CMN_CXL_STRONGLY_ORDERED_WRITES_ONEATATIME:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Enhanced Partial Writes to Same Address
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_ENHANCED_PART_WR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_CMN_ENHANCED_PART_WR_DISABLED:
    break;
  case IDSOPT_DF_CMN_ENHANCED_PART_WR_ENABLED:
    break;
  case IDSOPT_DF_CMN_ENHANCED_PART_WR_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//NUMA nodes per socket
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_DRAM_NPS, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_CMN_DRAM_NPS_NPS0:
    break;
  case IDSOPT_DF_CMN_DRAM_NPS_NPS1:
    break;
  case IDSOPT_DF_CMN_DRAM_NPS_NPS2:
    break;
  case IDSOPT_DF_CMN_DRAM_NPS_NPS4:
    break;
  case IDSOPT_DF_CMN_DRAM_NPS_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Memory interleaving
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_MEM_INTLV, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_CMN_MEM_INTLV_DISABLED:
    break;
  case IDSOPT_DF_CMN_MEM_INTLV_ENABLED:
    break;
  case IDSOPT_DF_CMN_MEM_INTLV_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Mixed interleaved mode
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_MIXED_INTERLEAVED_MODE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdCXlEarlyLinkTraining)) {
  case IDSOPT_DF_CMN_MIXED_INTERLEAVED_MODE_DISABLED:
    break;
  case IDSOPT_DF_CMN_MIXED_INTERLEAVED_MODE_ENABLED:
    break;
  case IDSOPT_DF_CMN_MIXED_INTERLEAVED_MODE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//CXL Memory interleaving
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_CXL_MEM_INTLV, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_CMN_CXL_MEM_INTLV_DISABLED:
    break;
  case IDSOPT_DF_CMN_CXL_MEM_INTLV_ENABLED:
    break;
  case IDSOPT_DF_CMN_CXL_MEM_INTLV_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//CXL Sublink interleaving
IDS_NV_READ_SKIP (IDSNVID_DF_CNLI_SUBLINK_INTERLEAVING, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_CNLI_SUBLINK_INTERLEAVING_ENABLE:
    break;
  case IDSOPT_DF_CNLI_SUBLINK_INTERLEAVING_DISABLE:
    break;
  case IDSOPT_DF_CNLI_SUBLINK_INTERLEAVING_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DRAM map inversion
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_DRAM_MAP_INVERSION, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_CMN_DRAM_MAP_INVERSION_DISABLED:
    break;
  case IDSOPT_DF_CMN_DRAM_MAP_INVERSION_ENABLED:
    break;
  case IDSOPT_DF_CMN_DRAM_MAP_INVERSION_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Location of private memory regions
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_CC6_ALLOCATION_SCHEME, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_CMN_CC6_ALLOCATION_SCHEME_DISTRIBUTED:
    break;
  case IDSOPT_DF_CMN_CC6_ALLOCATION_SCHEME_CONSOLIDATED:
    break;
  case IDSOPT_DF_CMN_CC6_ALLOCATION_SCHEME_CONSOLIDATEDTO1STDRAMPAIR:
    break;
  case IDSOPT_DF_CMN_CC6_ALLOCATION_SCHEME_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//ACPI SRAT L3 Cache As NUMA Domain
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_ACPI_SRAT_L3_NUMA, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdFabricCcxAsNumaDomain)) {
  case IDSOPT_DF_CMN_ACPI_SRAT_L3_NUMA_DISABLED:
    break;
  case IDSOPT_DF_CMN_ACPI_SRAT_L3_NUMA_ENABLED:
    break;
  case IDSOPT_DF_CMN_ACPI_SRAT_L3_NUMA_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//ACPI SLIT Distance Control
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_ACPI_SLIT_DIST_CTRL, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdFabricSlitDistancePcdCtrl)) {
  case IDSOPT_DF_CMN_ACPI_SLIT_DIST_CTRL_MANUAL:
    break;
  case IDSOPT_DF_CMN_ACPI_SLIT_DIST_CTRL_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//ACPI SLIT remote relative distance
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_ACPI_SLIT_REMOTE_FAR, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdFabricSlitAutoRemoteFar)) {
  case IDSOPT_DF_CMN_ACPI_SLIT_REMOTE_FAR_NEAR:
    break;
  case IDSOPT_DF_CMN_ACPI_SLIT_REMOTE_FAR_FAR:
    break;
  case IDSOPT_DF_CMN_ACPI_SLIT_REMOTE_FAR_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_DF_CMN_ACPI_SLIT_VIRTUAL_DIST, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_CMN_ACPI_SLIT_VIRTUAL_DIST_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_CMN_ACPI_SLIT_VIRTUAL_DIST_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_CMN_ACPI_SLIT_LCL_DIST, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_CMN_ACPI_SLIT_LCL_DIST_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_CMN_ACPI_SLIT_LCL_DIST_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_CMN_ACPI_SLIT_RMT_DIST, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_CMN_ACPI_SLIT_RMT_DIST_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_CMN_ACPI_SLIT_RMT_DIST_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_CMN_ACPI_SLIT_CXL_LCL, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_CMN_ACPI_SLIT_CXL_LCL_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_CMN_ACPI_SLIT_CXL_LCL_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_CMN_ACPI_SLIT_CXL_RMT, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_CMN_ACPI_SLIT_CXL_RMT_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_CMN_ACPI_SLIT_CXL_RMT_MIN);
}

//GMI encryption control
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_GMI_ENCRYPTION, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_CMN_GMI_ENCRYPTION_DISABLED:
    break;
  case IDSOPT_DF_CMN_GMI_ENCRYPTION_ENABLED:
    break;
  case IDSOPT_DF_CMN_GMI_ENCRYPTION_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI encryption control
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_X_GMI_ENCRYPTION, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_CMN_X_GMI_ENCRYPTION_DISABLED:
    break;
  case IDSOPT_DF_CMN_X_GMI_ENCRYPTION_ENABLED:
    break;
  case IDSOPT_DF_CMN_X_GMI_ENCRYPTION_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI Link Configuration
IDS_NV_READ_SKIP (IDSNVID_DF_DBG_XGMI_LINK_CFG, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_DBG_XGMI_LINK_CFG_AUTO:
    break;
  case IDSOPT_DF_DBG_XGMI_LINK_CFG_3XGMILINKS:
    break;
  case IDSOPT_DF_DBG_XGMI_LINK_CFG_4XGMILINKS:
    break;
  case IDSOPT_DF_DBG_XGMI_LINK_CFG_2XGMILINKS2PCILINKS:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//4-link xGMI max speed
IDS_NV_READ_SKIP (IDSNVID_DF_CMN4_LINK_MAX_XGMI_SPEED, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_CMN4_LINK_MAX_XGMI_SPEED_20GBPS:
    break;
  case IDSOPT_DF_CMN4_LINK_MAX_XGMI_SPEED_25GBPS:
    break;
  case IDSOPT_DF_CMN4_LINK_MAX_XGMI_SPEED_32GBPS:
    break;
  case IDSOPT_DF_CMN4_LINK_MAX_XGMI_SPEED_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//3-link xGMI max speed
IDS_NV_READ_SKIP (IDSNVID_DF_CMN3_LINK_MAX_XGMI_SPEED, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_CMN3_LINK_MAX_XGMI_SPEED_20GBPS:
    break;
  case IDSOPT_DF_CMN3_LINK_MAX_XGMI_SPEED_25GBPS:
    break;
  case IDSOPT_DF_CMN3_LINK_MAX_XGMI_SPEED_32GBPS:
    break;
  case IDSOPT_DF_CMN3_LINK_MAX_XGMI_SPEED_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CRC_SCALE, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_CRC_SCALE_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_CRC_SCALE_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CRC_THRESHOLD, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_CRC_THRESHOLD_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_CRC_THRESHOLD_MIN);
}

//xGMI Preset Control
IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_PRESET_CONTROL, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_XGMI_PRESET_CONTROL_DISABLED:
    break;
  case IDSOPT_DF_XGMI_PRESET_CONTROL_ENABLED:
    break;
  case IDSOPT_DF_XGMI_PRESET_CONTROL_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI Training Err Mask
IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TRAINING_ERR_MASK, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_XGMI_TRAINING_ERR_MASK_DISABLE:
    break;
  case IDSOPT_DF_XGMI_TRAINING_ERR_MASK_ENABLE:
    break;
  case IDSOPT_DF_XGMI_TRAINING_ERR_MASK_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_PRESET_P11, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_PRESET_P11_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_PRESET_P11_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CMN1_P11, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_CMN1_P11_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_CMN1_P11_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CN_P11, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_CN_P11_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_CN_P11_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CNP1_P11, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_CNP1_P11_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_CNP1_P11_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_PRESET_P12, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_PRESET_P12_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_PRESET_P12_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CMN1_P12, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_CMN1_P12_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_CMN1_P12_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CN_P12, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_CN_P12_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_CN_P12_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CNP1_P12, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_CNP1_P12_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_CNP1_P12_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_PRESET_P13, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_PRESET_P13_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_PRESET_P13_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CMN1_P13, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_CMN1_P13_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_CMN1_P13_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CN_P13, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_CN_P13_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_CN_P13_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CNP1_P13, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_CNP1_P13_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_CNP1_P13_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_PRESET_P14, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_PRESET_P14_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_PRESET_P14_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CMN1_P14, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_CMN1_P14_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_CMN1_P14_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CN_P14, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_CN_P14_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_CN_P14_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CNP1_P14, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_CNP1_P14_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_CNP1_P14_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_PRESET_P15, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_PRESET_P15_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_PRESET_P15_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CMN1_P15, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_CMN1_P15_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_CMN1_P15_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CN_P15, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_CN_P15_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_CN_P15_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CNP1_P15, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_CNP1_P15_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_CNP1_P15_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S0_L0, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S0_L0_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S0_L0_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S0_L0_P0, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S0_L0_P0_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S0_L0_P0_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S0_L0_P1, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S0_L0_P1_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S0_L0_P1_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S0_L0_P2, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S0_L0_P2_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S0_L0_P2_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S0_L0_P3, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S0_L0_P3_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S0_L0_P3_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S0_L1, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S0_L1_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S0_L1_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S0_L1_P0, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S0_L1_P0_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S0_L1_P0_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S0_L1_P1, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S0_L1_P1_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S0_L1_P1_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S0_L1_P2, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S0_L1_P2_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S0_L1_P2_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S0_L1_P3, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S0_L1_P3_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S0_L1_P3_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S0_L2, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S0_L2_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S0_L2_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S0_L2_P0, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S0_L2_P0_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S0_L2_P0_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S0_L2_P1, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S0_L2_P1_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S0_L2_P1_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S0_L2_P2, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S0_L2_P2_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S0_L2_P2_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S0_L2_P3, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S0_L2_P3_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S0_L2_P3_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S0_L3, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S0_L3_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S0_L3_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S0_L3_P0, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S0_L3_P0_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S0_L3_P0_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S0_L3_P1, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S0_L3_P1_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S0_L3_P1_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S0_L3_P2, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S0_L3_P2_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S0_L3_P2_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S0_L3_P3, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S0_L3_P3_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S0_L3_P3_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S1_L0, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S1_L0_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S1_L0_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S1_L0_P0, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S1_L0_P0_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S1_L0_P0_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S1_L0_P1, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S1_L0_P1_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S1_L0_P1_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S1_L0_P2, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S1_L0_P2_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S1_L0_P2_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S1_L0_P3, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S1_L0_P3_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S1_L0_P3_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S1_L1, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S1_L1_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S1_L1_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S1_L1_P0, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S1_L1_P0_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S1_L1_P0_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S1_L1_P1, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S1_L1_P1_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S1_L1_P1_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S1_L1_P2, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S1_L1_P2_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S1_L1_P2_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S1_L1_P3, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S1_L1_P3_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S1_L1_P3_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S1_L2, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S1_L2_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S1_L2_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S1_L2_P0, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S1_L2_P0_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S1_L2_P0_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S1_L2_P1, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S1_L2_P1_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S1_L2_P1_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S1_L2_P2, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S1_L2_P2_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S1_L2_P2_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S1_L2_P3, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S1_L2_P3_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S1_L2_P3_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S1_L3, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S1_L3_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S1_L3_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S1_L3_P0, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S1_L3_P0_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S1_L3_P0_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S1_L3_P1, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S1_L3_P1_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S1_L3_P1_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S1_L3_P2, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S1_L3_P2_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S1_L3_P2_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_INIT_PRESET_S1_L3_P3, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_INIT_PRESET_S1_L3_P3_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_INIT_PRESET_S1_L3_P3_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S0_L0_P01, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S0_L0_P01_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S0_L0_P01_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S0_L0_P23, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S0_L0_P23_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S0_L0_P23_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S0_L0_P0, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S0_L0_P0_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S0_L0_P0_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S0_L0_P1, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S0_L0_P1_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S0_L0_P1_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S0_L0_P2, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S0_L0_P2_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S0_L0_P2_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S0_L0_P3, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S0_L0_P3_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S0_L0_P3_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S0_L1_P01, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S0_L1_P01_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S0_L1_P01_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S0_L1_P23, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S0_L1_P23_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S0_L1_P23_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S0_L1_P0, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S0_L1_P0_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S0_L1_P0_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S0_L1_P1, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S0_L1_P1_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S0_L1_P1_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S0_L1_P2, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S0_L1_P2_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S0_L1_P2_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S0_L1_P3, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S0_L1_P3_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S0_L1_P3_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S0_L2_P01, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S0_L2_P01_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S0_L2_P01_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S0_L2_P23, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S0_L2_P23_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S0_L2_P23_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S0_L2_P0, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S0_L2_P0_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S0_L2_P0_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S0_L2_P1, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S0_L2_P1_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S0_L2_P1_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S0_L2_P2, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S0_L2_P2_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S0_L2_P2_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S0_L2_P3, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S0_L2_P3_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S0_L2_P3_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S0_L3_P01, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S0_L3_P01_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S0_L3_P01_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S0_L3_P23, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S0_L3_P23_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S0_L3_P23_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S0_L3_P0, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S0_L3_P0_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S0_L3_P0_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S0_L3_P1, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S0_L3_P1_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S0_L3_P1_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S0_L3_P2, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S0_L3_P2_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S0_L3_P2_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S0_L3_P3, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S0_L3_P3_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S0_L3_P3_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S1_L0_P01, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S1_L0_P01_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S1_L0_P01_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S1_L0_P23, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S1_L0_P23_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S1_L0_P23_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S1_L0_P0, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S1_L0_P0_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S1_L0_P0_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S1_L0_P1, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S1_L0_P1_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S1_L0_P1_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S1_L0_P2, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S1_L0_P2_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S1_L0_P2_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S1_L0_P3, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S1_L0_P3_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S1_L0_P3_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S1_L1_P01, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S1_L1_P01_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S1_L1_P01_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S1_L1_P23, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S1_L1_P23_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S1_L1_P23_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S1_L1_P0, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S1_L1_P0_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S1_L1_P0_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S1_L1_P1, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S1_L1_P1_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S1_L1_P1_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S1_L1_P2, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S1_L1_P2_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S1_L1_P2_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S1_L1_P3, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S1_L1_P3_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S1_L1_P3_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S1_L2_P01, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S1_L2_P01_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S1_L2_P01_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S1_L2_P23, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S1_L2_P23_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S1_L2_P23_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S1_L2_P0, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S1_L2_P0_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S1_L2_P0_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S1_L2_P1, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S1_L2_P1_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S1_L2_P1_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S1_L2_P2, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S1_L2_P2_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S1_L2_P2_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S1_L2_P3, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S1_L2_P3_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S1_L2_P3_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S1_L3_P01, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S1_L3_P01_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S1_L3_P01_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S1_L3_P23, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S1_L3_P23_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S1_L3_P23_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S1_L3_P0, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S1_L3_P0_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S1_L3_P0_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S1_L3_P1, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S1_L3_P1_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S1_L3_P1_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S1_L3_P2, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S1_L3_P2_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S1_L3_P2_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_TXEQ_S1_L3_P3, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_TXEQ_S1_L3_P3_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_TXEQ_S1_L3_P3_MIN);
}

//xGMI AC/DC Coupled Link Control
IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_CONTROL, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_CONTROL_MANUAL:
    break;
  case IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_CONTROL_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_MIN);
}

//xGMI AC/DC Coupled Link Socket 0 Link 0
IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK0, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK0_ACCOUPLED:
    break;
  case IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK0_DCCOUPLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI AC/DC Coupled Link Socket 0 Link 1
IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK1, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK1_ACCOUPLED:
    break;
  case IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK1_DCCOUPLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI AC/DC Coupled Link Socket 0 Link 2
IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK2, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK2_ACCOUPLED:
    break;
  case IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK2_DCCOUPLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI AC/DC Coupled Link Socket 0 Link 3
IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK3, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK3_ACCOUPLED:
    break;
  case IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK3_DCCOUPLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI AC/DC Coupled Link Socket 1 Link 0
IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK0, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK0_ACCOUPLED:
    break;
  case IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK0_DCCOUPLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI AC/DC Coupled Link Socket 1 Link 1
IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK1, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK1_ACCOUPLED:
    break;
  case IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK1_DCCOUPLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI AC/DC Coupled Link Socket 1 Link 2
IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK2, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK2_ACCOUPLED:
    break;
  case IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK2_DCCOUPLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI AC/DC Coupled Link Socket 1 Link 3
IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK3, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK3_ACCOUPLED:
    break;
  case IDSOPT_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK3_DCCOUPLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI Channel Type Control
IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CHANNEL_TYPE_CONTROL, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_XGMI_CHANNEL_TYPE_CONTROL_MANUAL:
    break;
  case IDSOPT_DF_XGMI_CHANNEL_TYPE_CONTROL_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CHANNEL_TYPE, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DF_XGMI_CHANNEL_TYPE_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DF_XGMI_CHANNEL_TYPE_MIN);
}

//xGMI Channel Type Socket 0 Link 0
IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK0, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK0_DISABLED:
    break;
  case IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK0_LONGREACH:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI Channel Type Socket 0 Link 1
IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK1, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK1_DISABLED:
    break;
  case IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK1_LONGREACH:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI Channel Type Socket 0 Link 2
IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK2, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK2_DISABLED:
    break;
  case IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK2_LONGREACH:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI Channel Type Socket 0 Link 3
IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK3, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK3_DISABLED:
    break;
  case IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK3_LONGREACH:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI Channel Type Socket 1 Link 0
IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK0, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK0_DISABLED:
    break;
  case IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK0_LONGREACH:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI Channel Type Socket 1 Link 1
IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK1, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK1_DISABLED:
    break;
  case IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK1_LONGREACH:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI Channel Type Socket 1 Link 2
IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK2, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK2_DISABLED:
    break;
  case IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK2_LONGREACH:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI Channel Type Socket 1 Link 3
IDS_NV_READ_SKIP (IDSNVID_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK3, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK3_DISABLED:
    break;
  case IDSOPT_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK3_LONGREACH:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//SDCI
IDS_NV_READ_SKIP (IDSNVID_DF_CDMA, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdFabricCdma)) {
  case IDSOPT_DF_CDMA_DISABLED:
    break;
  case IDSOPT_DF_CDMA_ENABLED:
    break;
  case IDSOPT_DF_CDMA_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DisRmtSteer
IDS_NV_READ_SKIP (IDSNVID_DF_DBG_DIS_RMT_STEER, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_DBG_DIS_RMT_STEER_DISABLED:
    break;
  case IDSOPT_DF_DBG_DIS_RMT_STEER_ENABLED:
    break;
  case IDSOPT_DF_DBG_DIS_RMT_STEER_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Organization
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_PF_ORGANIZATION, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_CMN_PF_ORGANIZATION_AUTO:
    break;
  case IDSOPT_DF_CMN_PF_ORGANIZATION_DEDICATED:
    break;
  case IDSOPT_DF_CMN_PF_ORGANIZATION_SHARED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Periodic Directory Rinse (PDR) Tuning
IDS_NV_READ_SKIP (IDSNVID_CMN_DF_PDR_TUNING, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_DF_PDR_TUNING_PERIODIC:
    break;
  case IDSOPT_CMN_DF_PDR_TUNING_BLENDED:
    break;
  case IDSOPT_CMN_DF_PDR_TUNING_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Tracking Granularity
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_MEM_INTLV_PAGE_SIZE, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_CMN_MEM_INTLV_PAGE_SIZE_FINERGRAIN:
    break;
  case IDSOPT_DF_CMN_MEM_INTLV_PAGE_SIZE_COARSERGRAIN:
    break;
  case IDSOPT_DF_CMN_MEM_INTLV_PAGE_SIZE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//PDR Mode
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_PF_PDR_MODE, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DF_CMN_PF_PDR_MODE_AUTO:
    break;
  case IDSOPT_DF_CMN_PF_PDR_MODE_SELECTIVE:
    break;
  case IDSOPT_DF_CMN_PF_PDR_MODE_ALL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Chipselect Interleaving
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_CS_INTERLEAVE_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_CS_INTERLEAVE_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_CS_INTERLEAVE_DDR_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Address Hash Bank
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_ADDRESS_HASH_BANK_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_ADDRESS_HASH_BANK_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_ADDRESS_HASH_BANK_DDR_ENABLED:
    break;
  case IDSOPT_CMN_MEM_ADDRESS_HASH_BANK_DDR_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Address Hash CS
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_ADDRESS_HASH_CS_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_ADDRESS_HASH_CS_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_ADDRESS_HASH_CS_DDR_ENABLED:
    break;
  case IDSOPT_CMN_MEM_ADDRESS_HASH_CS_DDR_DISABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Address Hash Rm
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_ADDRESS_HASH_RM_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_ADDRESS_HASH_RM_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_ADDRESS_HASH_RM_DDR_ENABLED:
    break;
  case IDSOPT_CMN_MEM_ADDRESS_HASH_RM_DDR_DISABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Address Hash Subchannel
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_ADDRESS_HASH_SUBCHANNEL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_ADDRESS_HASH_SUBCHANNEL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_ADDRESS_HASH_SUBCHANNEL_DDR_ENABLED:
    break;
  case IDSOPT_CMN_MEM_ADDRESS_HASH_SUBCHANNEL_DDR_DISABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//BankSwapMode
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_CTRLLER_BANK_SWAP_MODE_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_CTRLLER_BANK_SWAP_MODE_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_CTRLLER_BANK_SWAP_MODE_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_CTRLLER_BANK_SWAP_MODE_DDR_SWAPCPU:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Memory Context Restore
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_CONTEXT_RESTORE_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_CONTEXT_RESTORE_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_CONTEXT_RESTORE_DDR_ENABLED:
    break;
  case IDSOPT_CMN_MEM_CONTEXT_RESTORE_DDR_DISABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DRAM Survives Warm Reset
IDS_NV_READ_SKIP (IDSNVID_DRAM_SURVIVES_WARM_RESET, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DRAM_SURVIVES_WARM_RESET_DISABLED:
    break;
  case IDSOPT_DRAM_SURVIVES_WARM_RESET_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Power Down Enable
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_CTRLLER_PWR_DN_EN_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_CTRLLER_PWR_DN_EN_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_CTRLLER_PWR_DN_EN_DDR_ENABLED:
    break;
  case IDSOPT_CMN_MEM_CTRLLER_PWR_DN_EN_DDR_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SUB_URG_REF_LOWER_BOUND, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_SUB_URG_REF_LOWER_BOUND_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_SUB_URG_REF_LOWER_BOUND_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_URG_REF_LIMIT, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_URG_REF_LIMIT_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_URG_REF_LIMIT_MIN);
}

//DRAM Refresh Rate
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_DRAM_REFRESH_RATE, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_DRAM_REFRESH_RATE_39USEC:
    break;
  case IDSOPT_CMN_MEM_DRAM_REFRESH_RATE_195USEC:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Self-Refresh Exit Staggering
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SELF_REFRESH_EXIT_STAGGERING, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_SELF_REFRESH_EXIT_STAGGERING_DISABLED:
    break;
  case IDSOPT_CMN_MEM_SELF_REFRESH_EXIT_STAGGERING_N1:
    break;
  case IDSOPT_CMN_MEM_SELF_REFRESH_EXIT_STAGGERING_N2:
    break;
  case IDSOPT_CMN_MEM_SELF_REFRESH_EXIT_STAGGERING_N3:
    break;
  case IDSOPT_CMN_MEM_SELF_REFRESH_EXIT_STAGGERING_N4:
    break;
  case IDSOPT_CMN_MEM_SELF_REFRESH_EXIT_STAGGERING_N5:
    break;
  case IDSOPT_CMN_MEM_SELF_REFRESH_EXIT_STAGGERING_N6:
    break;
  case IDSOPT_CMN_MEM_SELF_REFRESH_EXIT_STAGGERING_N7:
    break;
  case IDSOPT_CMN_MEM_SELF_REFRESH_EXIT_STAGGERING_N8:
    break;
  case IDSOPT_CMN_MEM_SELF_REFRESH_EXIT_STAGGERING_N9:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DRAM 2x Refresh Temperature Threshold
IDS_NV_READ_SKIP (IDSNVID_CMN_MEMT2X_REFRESH_TEMPERATURE_THRESHOLD, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEMT2X_REFRESH_TEMPERATURE_THRESHOLD_8590:
    break;
  case IDSOPT_CMN_MEMT2X_REFRESH_TEMPERATURE_THRESHOLD_9095:
    break;
  case IDSOPT_CMN_MEMT2X_REFRESH_TEMPERATURE_THRESHOLD_95100:
    break;
  case IDSOPT_CMN_MEMT2X_REFRESH_TEMPERATURE_THRESHOLD_100:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Memory Channel Disable Float Power Good
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_CHANNEL_DISABLE_FLOAT_POWER_GOOD_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_CHANNEL_DISABLE_FLOAT_POWER_GOOD_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_CHANNEL_DISABLE_FLOAT_POWER_GOOD_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_CHANNEL_DISABLE_BITMASK_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_CHANNEL_DISABLE_BITMASK_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_CHANNEL_DISABLE_BITMASK_DDR_MIN);
}

//Socket 0 Channel 0
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SOCKET0_CHANNEL0_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_SOCKET0_CHANNEL0_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_SOCKET0_CHANNEL0_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 0 Channel 1
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SOCKET0_CHANNEL1_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_SOCKET0_CHANNEL1_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_SOCKET0_CHANNEL1_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 0 Channel 2
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SOCKET0_CHANNEL2_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_SOCKET0_CHANNEL2_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_SOCKET0_CHANNEL2_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 0 Channel 3
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SOCKET0_CHANNEL3_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_SOCKET0_CHANNEL3_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_SOCKET0_CHANNEL3_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 0 Channel 4
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SOCKET0_CHANNEL4_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_SOCKET0_CHANNEL4_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_SOCKET0_CHANNEL4_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 0 Channel 5
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SOCKET0_CHANNEL5_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_SOCKET0_CHANNEL5_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_SOCKET0_CHANNEL5_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 0 Channel 6
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SOCKET0_CHANNEL6_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_SOCKET0_CHANNEL6_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_SOCKET0_CHANNEL6_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 0 Channel 7
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SOCKET0_CHANNEL7_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_SOCKET0_CHANNEL7_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_SOCKET0_CHANNEL7_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 0 Channel 8
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SOCKET0_CHANNEL8_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_SOCKET0_CHANNEL8_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_SOCKET0_CHANNEL8_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 0 Channel 9
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SOCKET0_CHANNEL9_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_SOCKET0_CHANNEL9_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_SOCKET0_CHANNEL9_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 0 Channel 10
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SOCKET0_CHANNEL10_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_SOCKET0_CHANNEL10_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_SOCKET0_CHANNEL10_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 0 Channel 11
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SOCKET0_CHANNEL11_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_SOCKET0_CHANNEL11_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_SOCKET0_CHANNEL11_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 1 Channel 0
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SOCKET1_CHANNEL0_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_SOCKET1_CHANNEL0_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_SOCKET1_CHANNEL0_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 1 Channel 1
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SOCKET1_CHANNEL1_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_SOCKET1_CHANNEL1_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_SOCKET1_CHANNEL1_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 1 Channel 2
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SOCKET1_CHANNEL2_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_SOCKET1_CHANNEL2_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_SOCKET1_CHANNEL2_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 1 Channel 3
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SOCKET1_CHANNEL3_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_SOCKET1_CHANNEL3_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_SOCKET1_CHANNEL3_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 1 Channel 4
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SOCKET1_CHANNEL4_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_SOCKET1_CHANNEL4_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_SOCKET1_CHANNEL4_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 1 Channel 5
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SOCKET1_CHANNEL5_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_SOCKET1_CHANNEL5_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_SOCKET1_CHANNEL5_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 1 Channel 6
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SOCKET1_CHANNEL6_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_SOCKET1_CHANNEL6_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_SOCKET1_CHANNEL6_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 1 Channel 7
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SOCKET1_CHANNEL7_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_SOCKET1_CHANNEL7_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_SOCKET1_CHANNEL7_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 1 Channel 8
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SOCKET1_CHANNEL8_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_SOCKET1_CHANNEL8_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_SOCKET1_CHANNEL8_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 1 Channel 9
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SOCKET1_CHANNEL9_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_SOCKET1_CHANNEL9_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_SOCKET1_CHANNEL9_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 1 Channel 10
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SOCKET1_CHANNEL10_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_SOCKET1_CHANNEL10_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_SOCKET1_CHANNEL10_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 1 Channel 11
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SOCKET1_CHANNEL11_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_SOCKET1_CHANNEL11_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_SOCKET1_CHANNEL11_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Refresh Management
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_REF_MANAGEMENT_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_REF_MANAGEMENT_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_REF_MANAGEMENT_DDR_DISABLE:
    break;
  case IDSOPT_CMN_MEM_REF_MANAGEMENT_DDR_ENABLE:
    break;
  case IDSOPT_CMN_MEM_REF_MANAGEMENT_DDR_FORCEENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Adaptive Refresh Management
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_ARFM_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_ARFM_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_ARFM_DDR_DISABLE:
    break;
  case IDSOPT_CMN_MEM_ARFM_DDR_ARFMLEVELA:
    break;
  case IDSOPT_CMN_MEM_ARFM_DDR_ARFMLEVELB:
    break;
  case IDSOPT_CMN_MEM_ARFM_DDR_ARFMLEVELC:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//RAA Initial Management Threshold
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_RAAIMT_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_RAAIMT_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_RAAIMT_DDR_32:
    break;
  case IDSOPT_CMN_MEM_RAAIMT_DDR_40:
    break;
  case IDSOPT_CMN_MEM_RAAIMT_DDR_48:
    break;
  case IDSOPT_CMN_MEM_RAAIMT_DDR_56:
    break;
  case IDSOPT_CMN_MEM_RAAIMT_DDR_64:
    break;
  case IDSOPT_CMN_MEM_RAAIMT_DDR_72:
    break;
  case IDSOPT_CMN_MEM_RAAIMT_DDR_80:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//RAA Maximum Management Threshold
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_RAAMMT_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_RAAMMT_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_RAAMMT_DDR_3X:
    break;
  case IDSOPT_CMN_MEM_RAAMMT_DDR_4X:
    break;
  case IDSOPT_CMN_MEM_RAAMMT_DDR_5X:
    break;
  case IDSOPT_CMN_MEM_RAAMMT_DDR_6X:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//RAA Refresh Decrement Multiplier
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_RAA_REF_DEC_MULTIPLIER_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_RAA_REF_DEC_MULTIPLIER_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_RAA_REF_DEC_MULTIPLIER_DDR_05:
    break;
  case IDSOPT_CMN_MEM_RAA_REF_DEC_MULTIPLIER_DDR_1:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DRFM Enable
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_DRFM_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_DRFM_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_DRFM_DDR_DISABLE:
    break;
  case IDSOPT_CMN_MEM_DRFM_DDR_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Bounded Refresh Configuration
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_DRFM_BRC_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_DRFM_BRC_DDR_BRC2:
    break;
  case IDSOPT_CMN_MEM_DRFM_BRC_DDR_BRC3:
    break;
  case IDSOPT_CMN_MEM_DRFM_BRC_DDR_BRC4:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DRFM Hash Enable
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_DRFM_HASH_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_DRFM_HASH_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_DRFM_HASH_DDR_DISABLE:
    break;
  case IDSOPT_CMN_MEM_DRFM_HASH_DDR_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//MBIST Enable
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_MBIST_EN_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_MBIST_EN_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_MBIST_EN_DDR_ENABLED:
    break;
  case IDSOPT_CMN_MEM_MBIST_EN_DDR_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//MBIST Test Mode
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_MBIST_TESTMODE_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_MBIST_TESTMODE_DDR_INTERFACEMODE:
    break;
  case IDSOPT_CMN_MEM_MBIST_TESTMODE_DDR_DATAEYEMODE:
    break;
  case IDSOPT_CMN_MEM_MBIST_TESTMODE_DDR_BOTH:
    break;
  case IDSOPT_CMN_MEM_MBIST_TESTMODE_DDR_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//MBIST Aggressors
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_MBIST_AGGRESSORS_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_MBIST_AGGRESSORS_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_MBIST_AGGRESSORS_DDR_ENABLED:
    break;
  case IDSOPT_CMN_MEM_MBIST_AGGRESSORS_DDR_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DDR Healing BIST
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_HEALING_BIST_ENABLE_BIT_MASK_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_HEALING_BIST_ENABLE_BIT_MASK_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_HEALING_BIST_ENABLE_BIT_MASK_DDR_PMUMEMBIST:
    break;
  case IDSOPT_CMN_MEM_HEALING_BIST_ENABLE_BIT_MASK_DDR_SELFHEALINGMEMBIST:
    break;
  case IDSOPT_CMN_MEM_HEALING_BIST_ENABLE_BIT_MASK_DDR_PMUANDSELFHEALINGMEMBIST:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DDR Healing BIST Execution Mode
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_HEALING_BIST_EXECUTION_MODE, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_HEALING_BIST_EXECUTION_MODE_ONETIME:
    break;
  case IDSOPT_CMN_MEM_HEALING_BIST_EXECUTION_MODE_EVERYBOOT:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DDR Healing BIST Repair Type
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_HEALING_BIST_REPAIR_TYPE_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_HEALING_BIST_REPAIR_TYPE_DDR_SOFTREPAIR:
    break;
  case IDSOPT_CMN_MEM_HEALING_BIST_REPAIR_TYPE_DDR_HARDREPAIR:
    break;
  case IDSOPT_CMN_MEM_HEALING_BIST_REPAIR_TYPE_DDR_NOREPAIRSTESTONLY:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//PMU Mem BIST Algorithm Select
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM_SELECT, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM_SELECT_BYUSER:
    break;
  case IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM_SELECT_BYVENDOR:
    break;
  case IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM_SELECT_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM_BIT_MASK_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM_BIT_MASK_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM_BIT_MASK_DDR_MIN);
}

//Algorithm #1
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM1, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM1_DISABLED:
    break;
  case IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM1_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Algorithm #2
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM2, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM2_DISABLED:
    break;
  case IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM2_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Algorithm #3
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM3, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM3_DISABLED:
    break;
  case IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM3_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Algorithm #4
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM4, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM4_DISABLED:
    break;
  case IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM4_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Algorithm #5
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM5, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM5_DISABLED:
    break;
  case IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM5_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Algorithm #6
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM6, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM6_DISABLED:
    break;
  case IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM6_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Algorithm #7
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM7, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM7_DISABLED:
    break;
  case IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM7_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Algorithm #8
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM8, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM8_DISABLED:
    break;
  case IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM8_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Algorithm #9
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_PMU_BIST_ALGORITHM9, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM9_DISABLED:
    break;
  case IDSOPT_CMN_MEM_PMU_BIST_ALGORITHM9_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Pattern Select
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_MBIST_PATTERN_SELECT, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_MBIST_PATTERN_SELECT_PRBS:
    break;
  case IDSOPT_CMN_MEM_MBIST_PATTERN_SELECT_SSO:
    break;
  case IDSOPT_CMN_MEM_MBIST_PATTERN_SELECT_BOTH:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_MBIST_PATTERN_LENGTH, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_MBIST_PATTERN_LENGTH_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_MBIST_PATTERN_LENGTH_MIN);
}

//Aggressor Channel
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_MBIST_AGGRESSORS_CHNL, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_MBIST_AGGRESSORS_CHNL_ONESUBCHANNEL:
    break;
  case IDSOPT_CMN_MEM_MBIST_AGGRESSORS_CHNL_HALFCHANNELS:
    break;
  case IDSOPT_CMN_MEM_MBIST_AGGRESSORS_CHNL_ALLCHANNELS:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Aggressor Static Lane Control
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_MBIST_AGGR_STATIC_LANE_CTRL, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_MBIST_AGGR_STATIC_LANE_CTRL_DISABLED:
    break;
  case IDSOPT_CMN_MEM_MBIST_AGGR_STATIC_LANE_CTRL_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_MBIST_AGGR_STATIC_LANE_SEL_U32, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_MBIST_AGGR_STATIC_LANE_SEL_U32_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_MBIST_AGGR_STATIC_LANE_SEL_U32_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_MBIST_AGGR_STATIC_LANE_SEL_L32, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_MBIST_AGGR_STATIC_LANE_SEL_L32_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_MBIST_AGGR_STATIC_LANE_SEL_L32_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_MBIST_AGGR_STATIC_LANE_SEL_ECC, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_MBIST_AGGR_STATIC_LANE_SEL_ECC_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_MBIST_AGGR_STATIC_LANE_SEL_ECC_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_MBIST_AGGR_STATIC_LANE_VAL, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_MBIST_AGGR_STATIC_LANE_VAL_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_MBIST_AGGR_STATIC_LANE_VAL_MIN);
}

//Target Static Lane Control
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_MBIST_TGT_STATIC_LANE_CTRL, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_MBIST_TGT_STATIC_LANE_CTRL_DISABLED:
    break;
  case IDSOPT_CMN_MEM_MBIST_TGT_STATIC_LANE_CTRL_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_MBIST_TGT_STATIC_LANE_SEL_U32, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_MBIST_TGT_STATIC_LANE_SEL_U32_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_MBIST_TGT_STATIC_LANE_SEL_U32_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_MBIST_TGT_STATIC_LANE_SEL_L32, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_MBIST_TGT_STATIC_LANE_SEL_L32_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_MBIST_TGT_STATIC_LANE_SEL_L32_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_MBIST_TGT_STATIC_LANE_SEL_ECC, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_MBIST_TGT_STATIC_LANE_SEL_ECC_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_MBIST_TGT_STATIC_LANE_SEL_ECC_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_MBIST_TGT_STATIC_LANE_VAL, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_MBIST_TGT_STATIC_LANE_VAL_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_MBIST_TGT_STATIC_LANE_VAL_MIN);
}

//Read Voltage Sweep Step Size
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_MBIST_READ_DATA_EYE_VOLTAGE_STEP, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_MBIST_READ_DATA_EYE_VOLTAGE_STEP_1:
    break;
  case IDSOPT_CMN_MEM_MBIST_READ_DATA_EYE_VOLTAGE_STEP_2:
    break;
  case IDSOPT_CMN_MEM_MBIST_READ_DATA_EYE_VOLTAGE_STEP_4:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Read Timing Sweep Step Size
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_MBIST_READ_DATA_EYE_TIMING_STEP, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_MBIST_READ_DATA_EYE_TIMING_STEP_1:
    break;
  case IDSOPT_CMN_MEM_MBIST_READ_DATA_EYE_TIMING_STEP_2:
    break;
  case IDSOPT_CMN_MEM_MBIST_READ_DATA_EYE_TIMING_STEP_4:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Write Voltage Sweep Step Size
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_MBIST_WRITE_DATA_EYE_VOLTAGE_STEP, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_MBIST_WRITE_DATA_EYE_VOLTAGE_STEP_1:
    break;
  case IDSOPT_CMN_MEM_MBIST_WRITE_DATA_EYE_VOLTAGE_STEP_2:
    break;
  case IDSOPT_CMN_MEM_MBIST_WRITE_DATA_EYE_VOLTAGE_STEP_4:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Write Timing Sweep Step Size
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_MBIST_WRITE_DATA_EYE_TIMING_STEP, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_MBIST_WRITE_DATA_EYE_TIMING_STEP_1:
    break;
  case IDSOPT_CMN_MEM_MBIST_WRITE_DATA_EYE_TIMING_STEP_2:
    break;
  case IDSOPT_CMN_MEM_MBIST_WRITE_DATA_EYE_TIMING_STEP_4:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Silent Execution
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_MBIST_DATAEYE_SILENT_EXECUTION, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_MBIST_DATAEYE_SILENT_EXECUTION_DISABLED:
    break;
  case IDSOPT_CMN_MEM_MBIST_DATAEYE_SILENT_EXECUTION_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Data Poisoning
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_DATA_POISONING_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_DATA_POISONING_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_DATA_POISONING_DDR_ENABLED:
    break;
  case IDSOPT_CMN_MEM_DATA_POISONING_DDR_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DRAM Boot Time Post Package Repair
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_BOOT_TIME_POST_PACKAGE_REPAIR, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdMemBootTimePostPackageRepair)) {
  case IDSOPT_CMN_MEM_BOOT_TIME_POST_PACKAGE_REPAIR_ENABLE:
    break;
  case IDSOPT_CMN_MEM_BOOT_TIME_POST_PACKAGE_REPAIR_DISABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DRAM Runtime Post Package Repair
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_RUNTIME_POST_PACKAGE_REPAIR, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdMemRuntimePostPackageRepair)) {
  case IDSOPT_CMN_MEM_RUNTIME_POST_PACKAGE_REPAIR_ENABLE:
    break;
  case IDSOPT_CMN_MEM_RUNTIME_POST_PACKAGE_REPAIR_DISABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DRAM Post Package Repair Config Initiator
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_POST_PACKAGE_REPAIR_CONFIG_INITIATOR, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdMemPostPackageRepairConfigInitiator)) {
  case IDSOPT_CMN_MEM_POST_PACKAGE_REPAIR_CONFIG_INITIATOR_INBAND:
    break;
  case IDSOPT_CMN_MEM_POST_PACKAGE_REPAIR_CONFIG_INITIATOR_OUTOFBAND:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//RCD Parity
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_RCD_PARITY_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_RCD_PARITY_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_RCD_PARITY_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_RCD_PARITY_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_MAX_RCD_PARITY_ERROR_REPLAY_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_MAX_RCD_PARITY_ERROR_REPLAY_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_MAX_RCD_PARITY_ERROR_REPLAY_DDR_MIN);
}

//Write CRC
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_WRITE_CRC_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_WRITE_CRC_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_WRITE_CRC_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_WRITE_CRC_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_MAX_WRITE_CRC_ERROR_REPLAY_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_MAX_WRITE_CRC_ERROR_REPLAY_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_MAX_WRITE_CRC_ERROR_REPLAY_DDR_MIN);
}

//Read CRC
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_READ_CRC_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_READ_CRC_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_READ_CRC_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_READ_CRC_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_MAX_READ_CRC_ERROR_REPLAY_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_MAX_READ_CRC_ERROR_REPLAY_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_MAX_READ_CRC_ERROR_REPLAY_DDR_MIN);
}

//Memory Error Injection
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_DIS_MEM_ERR_INJ, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_DIS_MEM_ERR_INJ_FALSE:
    break;
  case IDSOPT_CMN_MEM_DIS_MEM_ERR_INJ_TRUE:
    break;
  case IDSOPT_CMN_MEM_DIS_MEM_ERR_INJ_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//EcsStatus Interrupt
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_ECS_STATUS_INTERRUPT_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdMemEcsStatusInterrupt)) {
  case IDSOPT_CMN_MEM_ECS_STATUS_INTERRUPT_DDR_FALSE:
    break;
  case IDSOPT_CMN_MEM_ECS_STATUS_INTERRUPT_DDR_TRUE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DRAM Corrected Error Counter Enable
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_CORRECTED_ERROR_COUNTER_ENABLE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdDdrEccErrorCounterEnable)) {
  case IDSOPT_CMN_MEM_CORRECTED_ERROR_COUNTER_ENABLE_DISABLE:
    break;
  case IDSOPT_CMN_MEM_CORRECTED_ERROR_COUNTER_ENABLE_NOLEAKMODE:
    break;
  case IDSOPT_CMN_MEM_CORRECTED_ERROR_COUNTER_ENABLE_LEAKMODE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DRAM Corrected Error Counter Interrupt Enable
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_CORRECTED_ERROR_COUNTER_INTERRUPT_ENABLE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdDdrEccErrorCounterIntEnable)) {
  case IDSOPT_CMN_MEM_CORRECTED_ERROR_COUNTER_INTERRUPT_ENABLE_FALSE:
    break;
  case IDSOPT_CMN_MEM_CORRECTED_ERROR_COUNTER_INTERRUPT_ENABLE_TRUE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_CORRECTED_ERROR_COUNTER_LEAK_RATE, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_CORRECTED_ERROR_COUNTER_LEAK_RATE_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_CORRECTED_ERROR_COUNTER_LEAK_RATE_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_CORRECTED_ERROR_COUNTER_START_COUNT, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_CORRECTED_ERROR_COUNTER_START_COUNT_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_CORRECTED_ERROR_COUNTER_START_COUNT_MIN);
}

//DRAM ECC Symbol Size
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_DRAM_ECC_SYMBOL_SIZE_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_DRAM_ECC_SYMBOL_SIZE_DDR_X4:
    break;
  case IDSOPT_CMN_MEM_DRAM_ECC_SYMBOL_SIZE_DDR_X16:
    break;
  case IDSOPT_CMN_MEM_DRAM_ECC_SYMBOL_SIZE_DDR_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DRAM ECC Enable
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_DRAM_ECC_EN_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_DRAM_ECC_EN_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_DRAM_ECC_EN_DDR_ENABLED:
    break;
  case IDSOPT_CMN_MEM_DRAM_ECC_EN_DDR_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DRAM UECC Retry
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_DRAM_UECC_RETRY_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_DRAM_UECC_RETRY_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_DRAM_UECC_RETRY_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_DRAM_UECC_RETRY_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_MAX_DRAM_UECC_ERROR_REPLAY_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_MAX_DRAM_UECC_ERROR_REPLAY_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_MAX_DRAM_UECC_ERROR_REPLAY_DDR_MIN);
}

//Memory Clear
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_DRAM_MEM_CLR_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_DRAM_MEM_CLR_DDR_ENABLED:
    break;
  case IDSOPT_CMN_MEM_DRAM_MEM_CLR_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_DRAM_MEM_CLR_DDR_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Address XOR after ECC
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_ADDR_XOR_AFTER_ECC, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_ADDR_XOR_AFTER_ECC_ENABLED:
    break;
  case IDSOPT_CMN_MEM_ADDR_XOR_AFTER_ECC_DISABLED:
    break;
  case IDSOPT_CMN_MEM_ADDR_XOR_AFTER_ECC_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//CipherText Hiding Enable
IDS_NV_READ_SKIP (IDSNVID_DBG_MEM_CIPHER_TEXT_HIDING, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_MEM_CIPHER_TEXT_HIDING_DISABLE:
    break;
  case IDSOPT_DBG_MEM_CIPHER_TEXT_HIDING_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DRAM ECS Mode
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_DRAM_ECS_MODE_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_DRAM_ECS_MODE_DDR_AUTOECS:
    break;
  case IDSOPT_CMN_MEM_DRAM_ECS_MODE_DDR_MANUALECS:
    break;
  case IDSOPT_CMN_MEM_DRAM_ECS_MODE_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_DRAM_ECS_MODE_DDR_DISABLEECS:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DRAM Redirect Scrubber Enable
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_DRAM_REDIRECT_SCRUB_EN_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_DRAM_REDIRECT_SCRUB_EN_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_DRAM_REDIRECT_SCRUB_EN_DDR_ENABLED:
    break;
  case IDSOPT_CMN_MEM_DRAM_REDIRECT_SCRUB_EN_DDR_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DRAM Scrub Redirection Limit
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_DRAM_REDIRECT_SCRUB_LIMIT_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_DRAM_REDIRECT_SCRUB_LIMIT_DDR_8SCRUBS:
    break;
  case IDSOPT_CMN_MEM_DRAM_REDIRECT_SCRUB_LIMIT_DDR_4SCRUBS:
    break;
  case IDSOPT_CMN_MEM_DRAM_REDIRECT_SCRUB_LIMIT_DDR_2SCRUBS:
    break;
  case IDSOPT_CMN_MEM_DRAM_REDIRECT_SCRUB_LIMIT_DDR_1SCRUB:
    break;
  case IDSOPT_CMN_MEM_DRAM_REDIRECT_SCRUB_LIMIT_DDR_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DRAM Scrub Time
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_DRAM_SCRUB_TIME, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_DRAM_SCRUB_TIME_DISABLED:
    break;
  case IDSOPT_CMN_MEM_DRAM_SCRUB_TIME_1HOUR:
    break;
  case IDSOPT_CMN_MEM_DRAM_SCRUB_TIME_4HOURS:
    break;
  case IDSOPT_CMN_MEM_DRAM_SCRUB_TIME_6HOURS:
    break;
  case IDSOPT_CMN_MEM_DRAM_SCRUB_TIME_8HOURS:
    break;
  case IDSOPT_CMN_MEM_DRAM_SCRUB_TIME_12HOURS:
    break;
  case IDSOPT_CMN_MEM_DRAM_SCRUB_TIME_16HOURS:
    break;
  case IDSOPT_CMN_MEM_DRAM_SCRUB_TIME_24HOURS:
    break;
  case IDSOPT_CMN_MEM_DRAM_SCRUB_TIME_48HOURS:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//tECSint Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEMT_EC_SINT_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEMT_EC_SINT_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEMT_EC_SINT_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEMT_EC_SINT_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEMT_EC_SINT_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEMT_EC_SINT_DDR_MIN);
}

//DRAM Error Threshold Count
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_DRAM_ETC_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_DRAM_ETC_DDR_ETC_4:
    break;
  case IDSOPT_CMN_MEM_DRAM_ETC_DDR_ETC_16:
    break;
  case IDSOPT_CMN_MEM_DRAM_ETC_DDR_ETC_64:
    break;
  case IDSOPT_CMN_MEM_DRAM_ETC_DDR_ETC_256:
    break;
  case IDSOPT_CMN_MEM_DRAM_ETC_DDR_ETC_1024:
    break;
  case IDSOPT_CMN_MEM_DRAM_ETC_DDR_ETC_4096:
    break;
  case IDSOPT_CMN_MEM_DRAM_ETC_DDR_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DRAM ECS Count Mode
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_DRAM_ECS_COUNT_MODE_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_DRAM_ECS_COUNT_MODE_DDR_ROWCOUNTMODE:
    break;
  case IDSOPT_CMN_MEM_DRAM_ECS_COUNT_MODE_DDR_CODEWORDCOUNTMODE:
    break;
  case IDSOPT_CMN_MEM_DRAM_ECS_COUNT_MODE_DDR_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DRAM AutoEcs during Self Refresh
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_DRAM_AUTO_ECS_SELF_REFRESH_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_DRAM_AUTO_ECS_SELF_REFRESH_DDR_AUTOECSDISABLED:
    break;
  case IDSOPT_CMN_MEM_DRAM_AUTO_ECS_SELF_REFRESH_DDR_AUTOECSENABLED:
    break;
  case IDSOPT_CMN_MEM_DRAM_AUTO_ECS_SELF_REFRESH_DDR_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DRAM ECS WriteBack Suppression
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_DRAM_ECS_WRITEBACK_SUPPRESSION_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_DRAM_ECS_WRITEBACK_SUPPRESSION_DDR_DISABLE:
    break;
  case IDSOPT_CMN_MEM_DRAM_ECS_WRITEBACK_SUPPRESSION_DDR_ENABLE:
    break;
  case IDSOPT_CMN_MEM_DRAM_ECS_WRITEBACK_SUPPRESSION_DDR_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DRAM X4 WriteBack Suppression
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_DRAM_X4_WRITEBACK_SUPPRESSION_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_DRAM_X4_WRITEBACK_SUPPRESSION_DDR_DISABLE:
    break;
  case IDSOPT_CMN_MEM_DRAM_X4_WRITEBACK_SUPPRESSION_DDR_ENABLE:
    break;
  case IDSOPT_CMN_MEM_DRAM_X4_WRITEBACK_SUPPRESSION_DDR_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Processor ODT Pull Up Impedance
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_ODT_IMPED_PROC_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_HIGHIMPEDANCE:
    break;
  case IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_480OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_240OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_160OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_120OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_96OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_80OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_686OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_60OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_533OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_48OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_436OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_40OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_369OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_343OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_32OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_30OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_282OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_267OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_IMPED_PROC_DDR_253OHM:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Processor ODT Pull Down Impedance
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_HIGHIMPEDANCE:
    break;
  case IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_480OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_240OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_160OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_120OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_96OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_80OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_686OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_60OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_533OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_48OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_436OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_40OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_369OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_343OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_32OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_30OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_282OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_267OHM:
    break;
  case IDSOPT_CMN_MEM_ODT_PULL_DOWN_IMPED_PROC_DDR_253OHM:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Dram DQ drive strengths
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_DRAM_DRV_STREN_DQ_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_DRAM_DRV_STREN_DQ_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_DRAM_DRV_STREN_DQ_DDR_48OHM:
    break;
  case IDSOPT_CMN_MEM_DRAM_DRV_STREN_DQ_DDR_40OHM:
    break;
  case IDSOPT_CMN_MEM_DRAM_DRV_STREN_DQ_DDR_34OHM:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//RTT_NOM_WR P-State 0
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_RTT_NOM_WR_P0_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_RTT_NOM_WR_P0_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_WR_P0_DDR_RTT_OFF:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_WR_P0_DDR_RZQ240:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_WR_P0_DDR_RZQ2120:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_WR_P0_DDR_RZQ380:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_WR_P0_DDR_RZQ460:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_WR_P0_DDR_RZQ548:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_WR_P0_DDR_RZQ640:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_WR_P0_DDR_RZQ734:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//RTT_NOM_RD P-State 0
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_RTT_NOM_RD_P0_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_RTT_NOM_RD_P0_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_RD_P0_DDR_RTT_OFF:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_RD_P0_DDR_RZQ240:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_RD_P0_DDR_RZQ2120:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_RD_P0_DDR_RZQ380:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_RD_P0_DDR_RZQ460:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_RD_P0_DDR_RZQ548:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_RD_P0_DDR_RZQ640:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_RD_P0_DDR_RZQ734:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//RTT_WR P-State 0
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_RTT_WR_P0_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_RTT_WR_P0_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_RTT_WR_P0_DDR_RTT_OFF:
    break;
  case IDSOPT_CMN_MEM_RTT_WR_P0_DDR_RZQ240:
    break;
  case IDSOPT_CMN_MEM_RTT_WR_P0_DDR_RZQ2120:
    break;
  case IDSOPT_CMN_MEM_RTT_WR_P0_DDR_RZQ380:
    break;
  case IDSOPT_CMN_MEM_RTT_WR_P0_DDR_RZQ460:
    break;
  case IDSOPT_CMN_MEM_RTT_WR_P0_DDR_RZQ548:
    break;
  case IDSOPT_CMN_MEM_RTT_WR_P0_DDR_RZQ640:
    break;
  case IDSOPT_CMN_MEM_RTT_WR_P0_DDR_RZQ734:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//RTT_PARK P-State 0
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_RTT_PARK_P0_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_RTT_PARK_P0_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_P0_DDR_RTT_OFF:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_P0_DDR_RZQ240:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_P0_DDR_RZQ2120:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_P0_DDR_RZQ380:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_P0_DDR_RZQ460:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_P0_DDR_RZQ548:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_P0_DDR_RZQ640:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_P0_DDR_RZQ734:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DQS_RTT_PARK P-State 0
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_RTT_PARK_DQS_P0_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_RTT_PARK_DQS_P0_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_DQS_P0_DDR_RTT_OFF:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_DQS_P0_DDR_RZQ240:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_DQS_P0_DDR_RZQ2120:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_DQS_P0_DDR_RZQ380:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_DQS_P0_DDR_RZQ460:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_DQS_P0_DDR_RZQ548:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_DQS_P0_DDR_RZQ640:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_DQS_P0_DDR_RZQ734:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//RTT_NOM_WR P-State 1
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_RTT_NOM_WR_P1_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_RTT_NOM_WR_P1_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_WR_P1_DDR_RTT_OFF:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_WR_P1_DDR_RZQ240:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_WR_P1_DDR_RZQ2120:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_WR_P1_DDR_RZQ380:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_WR_P1_DDR_RZQ460:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_WR_P1_DDR_RZQ548:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_WR_P1_DDR_RZQ640:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_WR_P1_DDR_RZQ734:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//RTT_NOM_RD P-State 1
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_RTT_NOM_RD_P1_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_RTT_NOM_RD_P1_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_RD_P1_DDR_RTT_OFF:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_RD_P1_DDR_RZQ240:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_RD_P1_DDR_RZQ2120:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_RD_P1_DDR_RZQ380:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_RD_P1_DDR_RZQ460:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_RD_P1_DDR_RZQ548:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_RD_P1_DDR_RZQ640:
    break;
  case IDSOPT_CMN_MEM_RTT_NOM_RD_P1_DDR_RZQ734:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//RTT_WR P-State 1
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_RTT_WR_P1_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_RTT_WR_P1_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_RTT_WR_P1_DDR_RTT_OFF:
    break;
  case IDSOPT_CMN_MEM_RTT_WR_P1_DDR_RZQ240:
    break;
  case IDSOPT_CMN_MEM_RTT_WR_P1_DDR_RZQ2120:
    break;
  case IDSOPT_CMN_MEM_RTT_WR_P1_DDR_RZQ380:
    break;
  case IDSOPT_CMN_MEM_RTT_WR_P1_DDR_RZQ460:
    break;
  case IDSOPT_CMN_MEM_RTT_WR_P1_DDR_RZQ548:
    break;
  case IDSOPT_CMN_MEM_RTT_WR_P1_DDR_RZQ640:
    break;
  case IDSOPT_CMN_MEM_RTT_WR_P1_DDR_RZQ734:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//RTT_PARK P-State 1
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_RTT_PARK_P1_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_RTT_PARK_P1_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_P1_DDR_RTT_OFF:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_P1_DDR_RZQ240:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_P1_DDR_RZQ2120:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_P1_DDR_RZQ380:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_P1_DDR_RZQ460:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_P1_DDR_RZQ548:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_P1_DDR_RZQ640:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_P1_DDR_RZQ734:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DQS_RTT_PARK P-State 1
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_RTT_PARK_DQS_P1_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_RTT_PARK_DQS_P1_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_DQS_P1_DDR_RTT_OFF:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_DQS_P1_DDR_RZQ240:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_DQS_P1_DDR_RZQ2120:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_DQS_P1_DDR_RZQ380:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_DQS_P1_DDR_RZQ460:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_DQS_P1_DDR_RZQ548:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_DQS_P1_DDR_RZQ640:
    break;
  case IDSOPT_CMN_MEM_RTT_PARK_DQS_P1_DDR_RZQ734:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Active Memory Timing Settings
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_SETTING_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_SETTING_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_SETTING_DDR_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Memory Target Speed
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TARGET_SPEED_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TARGET_SPEED_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TARGET_SPEED_DDR_DDR3600:
    break;
  case IDSOPT_CMN_MEM_TARGET_SPEED_DDR_DDR4000:
    break;
  case IDSOPT_CMN_MEM_TARGET_SPEED_DDR_DDR4400:
    break;
  case IDSOPT_CMN_MEM_TARGET_SPEED_DDR_DDR4800:
    break;
  case IDSOPT_CMN_MEM_TARGET_SPEED_DDR_DDR5200:
    break;
  case IDSOPT_CMN_MEM_TARGET_SPEED_DDR_DDR5600:
    break;
  case IDSOPT_CMN_MEM_TARGET_SPEED_DDR_DDR6000:
    break;
  case IDSOPT_CMN_MEM_TARGET_SPEED_DDR_DDR6400:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Tcl Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TCL_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TCL_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TCL_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TCL_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TCL_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TCL_DDR_MIN);
}

//Trcd Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRCD_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TRCD_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TRCD_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRCD_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TRCD_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TRCD_DDR_MIN);
}

//Trp Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRP_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TRP_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TRP_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRP_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TRP_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TRP_DDR_MIN);
}

//Tras Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRAS_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TRAS_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TRAS_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRAS_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TRAS_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TRAS_DDR_MIN);
}

//Trc Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRC_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TRC_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TRC_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRC_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TRC_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TRC_DDR_MIN);
}

//Twr Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TWR_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TWR_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TWR_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TWR_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TWR_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TWR_DDR_MIN);
}

//Trfc1 Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRFC1_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TRFC1_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TRFC1_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRFC1_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TRFC1_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TRFC1_DDR_MIN);
}

//Trfc2 Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRFC2_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TRFC2_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TRFC2_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRFC2_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TRFC2_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TRFC2_DDR_MIN);
}

//TrfcSb Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRFC_SB_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TRFC_SB_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TRFC_SB_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRFC_SB_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TRFC_SB_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TRFC_SB_DDR_MIN);
}

//Tcwl Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TCWL_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TCWL_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TCWL_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TCWL_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TCWL_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TCWL_DDR_MIN);
}

//Trtp Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRTP_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TRTP_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TRTP_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRTP_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TRTP_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TRTP_DDR_MIN);
}

//TrrdL Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRRD_L_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TRRD_L_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TRRD_L_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRRD_L_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TRRD_L_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TRRD_L_DDR_MIN);
}

//TrrdS Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRRD_S_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TRRD_S_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TRRD_S_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRRD_S_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TRRD_S_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TRRD_S_DDR_MIN);
}

//Tfaw Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TFAW_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TFAW_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TFAW_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TFAW_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TFAW_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TFAW_DDR_MIN);
}

//TwtrL Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TWTR_L_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TWTR_L_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TWTR_L_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TWTR_L_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TWTR_L_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TWTR_L_DDR_MIN);
}

//TwtrS Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TWTR_S_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TWTR_S_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TWTR_S_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TWTR_S_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TWTR_S_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TWTR_S_DDR_MIN);
}

//TrdrdScL Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRDRD_SC_L_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TRDRD_SC_L_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TRDRD_SC_L_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRDRD_SC_L_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TRDRD_SC_L_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TRDRD_SC_L_DDR_MIN);
}

//TrdrdSc Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRDRD_SC_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TRDRD_SC_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TRDRD_SC_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRDRD_SC_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TRDRD_SC_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TRDRD_SC_DDR_MIN);
}

//TrdrdSd Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRDRD_SD_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TRDRD_SD_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TRDRD_SD_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRDRD_SD_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TRDRD_SD_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TRDRD_SD_DDR_MIN);
}

//TrdrdDd Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRDRD_DD_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TRDRD_DD_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TRDRD_DD_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRDRD_DD_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TRDRD_DD_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TRDRD_DD_DDR_MIN);
}

//TwrwrScL Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TWRWR_SC_L_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TWRWR_SC_L_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TWRWR_SC_L_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TWRWR_SC_L_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TWRWR_SC_L_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TWRWR_SC_L_DDR_MIN);
}

//TwrwrSc Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TWRWR_SC_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TWRWR_SC_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TWRWR_SC_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TWRWR_SC_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TWRWR_SC_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TWRWR_SC_DDR_MIN);
}

//TwrwrSd Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TWRWR_SD_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TWRWR_SD_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TWRWR_SD_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TWRWR_SD_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TWRWR_SD_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TWRWR_SD_DDR_MIN);
}

//TwrwrDd Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TWRWR_DD_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TWRWR_DD_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TWRWR_DD_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TWRWR_DD_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TWRWR_DD_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TWRWR_DD_DDR_MIN);
}

//Twrrd Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TWRRD_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TWRRD_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TWRRD_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TWRRD_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TWRRD_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TWRRD_DDR_MIN);
}

//Trdwr Ctrl
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRDWR_CTRL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TIMING_TRDWR_CTRL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TIMING_TRDWR_CTRL_DDR_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TIMING_TRDWR_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_TIMING_TRDWR_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_TIMING_TRDWR_DDR_MIN);
}

//DRAM PDA Enumerate ID Programming Mode
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_DRAM_PDA_ENUM_ID_PROG_MODE_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_DRAM_PDA_ENUM_ID_PROG_MODE_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_DRAM_PDA_ENUM_ID_PROG_MODE_DDR_TOGGLINGPDAENUMERATIONMODE:
    break;
  case IDSOPT_CMN_MEM_DRAM_PDA_ENUM_ID_PROG_MODE_DDR_LEGACYPDAENUMERATIONMODE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Write Training Burst Length
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_WRITE_TRAINING_BURST_LENGTH, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_WRITE_TRAINING_BURST_LENGTH_4X:
    break;
  case IDSOPT_CMN_MEM_WRITE_TRAINING_BURST_LENGTH_8X:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Training Retry Count
IDS_NV_READ_SKIP (IDSNVID_CMN_TRAINING_RETRY_COUNT, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_TRAINING_RETRY_COUNT_AUTO:
    break;
  case IDSOPT_CMN_TRAINING_RETRY_COUNT_DISABLED:
    break;
  case IDSOPT_CMN_TRAINING_RETRY_COUNT_1:
    break;
  case IDSOPT_CMN_TRAINING_RETRY_COUNT_2:
    break;
  case IDSOPT_CMN_TRAINING_RETRY_COUNT_3:
    break;
  case IDSOPT_CMN_TRAINING_RETRY_COUNT_4:
    break;
  case IDSOPT_CMN_TRAINING_RETRY_COUNT_5:
    break;
  case IDSOPT_CMN_TRAINING_RETRY_COUNT_6:
    break;
  case IDSOPT_CMN_TRAINING_RETRY_COUNT_7:
    break;
  case IDSOPT_CMN_TRAINING_RETRY_COUNT_8:
    break;
  case IDSOPT_CMN_TRAINING_RETRY_COUNT_9:
    break;
  case IDSOPT_CMN_TRAINING_RETRY_COUNT_10:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Periodic Training Mode
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_PERIODIC_TRAINING_MODE_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_PERIODIC_TRAINING_MODE_DDR_DISABLED:
    break;
  case IDSOPT_CMN_MEM_PERIODIC_TRAINING_MODE_DDR_LEGACY:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Periodic Interval Mode
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_PERIODIC_INTERVAL_MODE, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_PERIODIC_INTERVAL_MODE_AUTO:
    break;
  case IDSOPT_CMN_MEM_PERIODIC_INTERVAL_MODE_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_PERIODIC_INTERVAL, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_PERIODIC_INTERVAL_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_PERIODIC_INTERVAL_MIN);
}

//TSME
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_TSME_ENABLE_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_TSME_ENABLE_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_TSME_ENABLE_DDR_ENABLED:
    break;
  case IDSOPT_CMN_MEM_TSME_ENABLE_DDR_DISABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//AES
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_AES, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_AES_AES128:
    break;
  case IDSOPT_CMN_MEM_AES_AES256:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Data Scramble
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_DATA_SCRAMBLE, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_DATA_SCRAMBLE_ENABLED:
    break;
  case IDSOPT_CMN_MEM_DATA_SCRAMBLE_DISABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//SME-MK
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_SME_MK_ENABLE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdHmkee)) {
  case IDSOPT_CMN_MEM_SME_MK_ENABLE_ENABLED:
    break;
  case IDSOPT_CMN_MEM_SME_MK_ENABLE_DISABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//PMIC Error Reporting
IDS_NV_READ_SKIP (IDSNVID_CMN_PMIC_ERROR_REPORTING, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdPmicErrorReporting)) {
  case IDSOPT_CMN_PMIC_ERROR_REPORTING_FALSE:
    break;
  case IDSOPT_CMN_PMIC_ERROR_REPORTING_TRUE:
    break;
  case IDSOPT_CMN_PMIC_ERROR_REPORTING_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//PMIC Operation Mode
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_CTRLLER_PMIC_OP_MODE, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_CTRLLER_PMIC_OP_MODE_SECUREMODE:
    break;
  case IDSOPT_CMN_MEM_CTRLLER_PMIC_OP_MODE_PROGRAMMABLEMODE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//PMIC Fault Recovery
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_CTRLLER_PMIC_FAULT_RECOVERY, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_CTRLLER_PMIC_FAULT_RECOVERY_ALWAYS:
    break;
  case IDSOPT_CMN_MEM_CTRLLER_PMIC_FAULT_RECOVERY_NEVER:
    break;
  case IDSOPT_CMN_MEM_CTRLLER_PMIC_FAULT_RECOVERY_ONCE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_CTRLLER_PMIC_SWA_SWB_VDD_CORE, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_CTRLLER_PMIC_SWA_SWB_VDD_CORE_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_CTRLLER_PMIC_SWA_SWB_VDD_CORE_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_CTRLLER_PMIC_SWC_VDDIO, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_CTRLLER_PMIC_SWC_VDDIO_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_CTRLLER_PMIC_SWC_VDDIO_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_CTRLLER_PMIC_SWD_VPP, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_CTRLLER_PMIC_SWD_VPP_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_CTRLLER_PMIC_SWD_VPP_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_CTRLLER_PMIC_STAGGER_DELAY, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_CTRLLER_PMIC_STAGGER_DELAY_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_CTRLLER_PMIC_STAGGER_DELAY_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_CTRLLER_MAX_PMIC_POWER_ON, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_MEM_CTRLLER_MAX_PMIC_POWER_ON_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_MEM_CTRLLER_MAX_PMIC_POWER_ON_MIN);
}

//ODTS Thermal Throttle Control
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_ODTS_CMD_THROTTLE_CYCLE_CTL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_ODTS_CMD_THROTTLE_CYCLE_CTL_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_ODTS_CMD_THROTTLE_CYCLE_CTL_DDR_ENABLED:
    break;
  case IDSOPT_CMN_MEM_ODTS_CMD_THROTTLE_CYCLE_CTL_DDR_DISABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//ODTS Thermal Throttle Threshold
IDS_NV_READ_SKIP (IDSNVID_CMN_MEM_ODTS_CMD_THROTTLE_THRESHOLD_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_MEM_ODTS_CMD_THROTTLE_THRESHOLD_DDR_AUTO:
    break;
  case IDSOPT_CMN_MEM_ODTS_CMD_THROTTLE_THRESHOLD_DDR_85C:
    break;
  case IDSOPT_CMN_MEM_ODTS_CMD_THROTTLE_THRESHOLD_DDR_90C:
    break;
  case IDSOPT_CMN_MEM_ODTS_CMD_THROTTLE_THRESHOLD_DDR_95C:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//TSOD Thermal Throttle Control
IDS_NV_READ_SKIP (IDSNVID_CMN_TSOD_THERMAL_THROTTLE_CONTROL_DDR, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_TSOD_THERMAL_THROTTLE_CONTROL_DDR_ENABLED:
    break;
  case IDSOPT_CMN_TSOD_THERMAL_THROTTLE_CONTROL_DDR_DISABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_TSOD_THERMAL_THROTTLE_START_TEMP_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_TSOD_THERMAL_THROTTLE_START_TEMP_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_TSOD_THERMAL_THROTTLE_START_TEMP_DDR_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_TSOD_THERMAL_THROTTLE_HYSTERESIS_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_TSOD_THERMAL_THROTTLE_HYSTERESIS_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_TSOD_THERMAL_THROTTLE_HYSTERESIS_DDR_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_TSOD_CMD_THROTTLE_PERCENTAGE0_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_TSOD_CMD_THROTTLE_PERCENTAGE0_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_TSOD_CMD_THROTTLE_PERCENTAGE0_DDR_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_TSOD_CMD_THROTTLE_PERCENTAGE5_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_TSOD_CMD_THROTTLE_PERCENTAGE5_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_TSOD_CMD_THROTTLE_PERCENTAGE5_DDR_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_TSOD_CMD_THROTTLE_PERCENTAGE10_DDR, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_TSOD_CMD_THROTTLE_PERCENTAGE10_DDR_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_TSOD_CMD_THROTTLE_PERCENTAGE10_DDR_MIN);
}

//PCIe loopback Mode
IDS_NV_READ_SKIP (IDSNVID_CMN_GNB_PCIE_LOOP_BACK_MODE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdCfgPcieLoopbackMode)) {
  case IDSOPT_CMN_GNB_PCIE_LOOP_BACK_MODE_AUTO:
    break;
  case IDSOPT_CMN_GNB_PCIE_LOOP_BACK_MODE_DISABLED:
    break;
  case IDSOPT_CMN_GNB_PCIE_LOOP_BACK_MODE_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Enable 2 SPC (Gen 4)
IDS_NV_READ_SKIP (IDSNVID_ENABLE2_SPC_GEN4, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdEnable2SpcGen4)) {
  case IDSOPT_ENABLE2_SPC_GEN4_ENABLE:
    break;
  case IDSOPT_ENABLE2_SPC_GEN4_DISABLE:
    break;
  case IDSOPT_ENABLE2_SPC_GEN4_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Enable 2 SPC (Gen 5)
IDS_NV_READ_SKIP (IDSNVID_ENABLE2_SPC_GEN5, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdEnable2SpcGen5)) {
  case IDSOPT_ENABLE2_SPC_GEN5_ENABLE:
    break;
  case IDSOPT_ENABLE2_SPC_GEN5_DISABLE:
    break;
  case IDSOPT_ENABLE2_SPC_GEN5_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Safe recovery upon a BERExceeded Error
IDS_NV_READ_SKIP (IDSNVID_GNB_SAFE_RECOVERY_UPON_ABER_EXCEEDED_ERROR, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdsafeRecoveryBER)) {
  case IDSOPT_GNB_SAFE_RECOVERY_UPON_ABER_EXCEEDED_ERROR_AUTO:
    break;
  case IDSOPT_GNB_SAFE_RECOVERY_UPON_ABER_EXCEEDED_ERROR_ENABLE:
    break;
  case IDSOPT_GNB_SAFE_RECOVERY_UPON_ABER_EXCEEDED_ERROR_DISABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Periodic Calibration
IDS_NV_READ_SKIP (IDSNVID_GNB_PERIODIC_CALIBRATION, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdPeriodicCal)) {
  case IDSOPT_GNB_PERIODIC_CALIBRATION_AUTO:
    break;
  case IDSOPT_GNB_PERIODIC_CALIBRATION_ENABLE:
    break;
  case IDSOPT_GNB_PERIODIC_CALIBRATION_DISABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//TDP Control
IDS_NV_READ_SKIP (IDSNVID_CMN_TDP_CTL, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_TDP_CTL_MANUAL:
    break;
  case IDSOPT_CMN_TDP_CTL_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_TDP_LIMIT, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_TDP_LIMIT_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_TDP_LIMIT_MIN);
}

//PPT Control
IDS_NV_READ_SKIP (IDSNVID_CMN_PPT_CTL, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_PPT_CTL_MANUAL:
    break;
  case IDSOPT_CMN_PPT_CTL_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_PPT_LIMIT, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_PPT_LIMIT_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_PPT_LIMIT_MIN);
}

//Determinism Control
IDS_NV_READ_SKIP (IDSNVID_CMN_DETERMINISM_CTL, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdDeterminismMode)) {
  case IDSOPT_CMN_DETERMINISM_CTL_MANUAL:
    break;
  case IDSOPT_CMN_DETERMINISM_CTL_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Determinism Enable
IDS_NV_READ_SKIP (IDSNVID_CMN_DETERMINISM_ENABLE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdDeterminismControl)) {
  case IDSOPT_CMN_DETERMINISM_ENABLE_POWER:
    break;
  case IDSOPT_CMN_DETERMINISM_ENABLE_PERFORMANCE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI Link Width Control
IDS_NV_READ_SKIP (IDSNVID_CMNX_GMI_LINK_WIDTH_CTL, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMNX_GMI_LINK_WIDTH_CTL_MANUAL:
    break;
  case IDSOPT_CMNX_GMI_LINK_WIDTH_CTL_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI Force Link Width Control
IDS_NV_READ_SKIP (IDSNVID_CMNX_GMI_FORCE_LINK_WIDTH_CTL, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdxGMIForceLinkWidthEn)) {
  case IDSOPT_CMNX_GMI_FORCE_LINK_WIDTH_CTL_AUTO:
    break;
  case IDSOPT_CMNX_GMI_FORCE_LINK_WIDTH_CTL_FORCE:
    break;
  case IDSOPT_CMNX_GMI_FORCE_LINK_WIDTH_CTL_UNFORCE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI Force Link Width
IDS_NV_READ_SKIP (IDSNVID_CMNX_GMI_FORCE_LINK_WIDTH, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdxGMIForceLinkWidth)) {
  case IDSOPT_CMNX_GMI_FORCE_LINK_WIDTH_AUTO:
    break;
  case IDSOPT_CMNX_GMI_FORCE_LINK_WIDTH_2:
    break;
  case IDSOPT_CMNX_GMI_FORCE_LINK_WIDTH_1:
    break;
  case IDSOPT_CMNX_GMI_FORCE_LINK_WIDTH_0:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI Max Link Width Range Control
IDS_NV_READ_SKIP (IDSNVID_CMNX_GMI_MAX_LINK_WIDTH_CTL, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdxGMIMaxLinkWidthEn)) {
  case IDSOPT_CMNX_GMI_MAX_LINK_WIDTH_CTL_MANUAL:
    break;
  case IDSOPT_CMNX_GMI_MAX_LINK_WIDTH_CTL_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI Max Link Width
IDS_NV_READ_SKIP (IDSNVID_CMNX_GMI_MAX_LINK_WIDTH, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdxGMIMaxLinkWidth)) {
  case IDSOPT_CMNX_GMI_MAX_LINK_WIDTH_AUTO:
    break;
  case IDSOPT_CMNX_GMI_MAX_LINK_WIDTH_2:
    break;
  case IDSOPT_CMNX_GMI_MAX_LINK_WIDTH_1:
    break;
  case IDSOPT_CMNX_GMI_MAX_LINK_WIDTH_0:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI Min Link Width
IDS_NV_READ_SKIP (IDSNVID_CMNX_GMI_MIN_LINK_WIDTH, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdxGMIMinLinkWidth)) {
  case IDSOPT_CMNX_GMI_MIN_LINK_WIDTH_AUTO:
    break;
  case IDSOPT_CMNX_GMI_MIN_LINK_WIDTH_2:
    break;
  case IDSOPT_CMNX_GMI_MIN_LINK_WIDTH_1:
    break;
  case IDSOPT_CMNX_GMI_MIN_LINK_WIDTH_0:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//APBDIS
IDS_NV_READ_SKIP (IDSNVID_CMN_APBDIS, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdCfgApbDis)) {
  case IDSOPT_CMN_APBDIS_0:
    break;
  case IDSOPT_CMN_APBDIS_1:
    break;
  case IDSOPT_CMN_APBDIS_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_APBDIS_DF_PSTATE, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_APBDIS_DF_PSTATE_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_APBDIS_DF_PSTATE_MIN);
}

//Power Profile Selection
IDS_NV_READ_SKIP (IDSNVID_CMN_EFFICIENCY_MODE_EN, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdPowerProfileSelect)) {
  case IDSOPT_CMN_EFFICIENCY_MODE_EN_HIGHPERFORMANCEMODE:
    break;
  case IDSOPT_CMN_EFFICIENCY_MODE_EN_EFFICIENCYMODE:
    break;
  case IDSOPT_CMN_EFFICIENCY_MODE_EN_MAXIMUMIOPERFORMANCEMODE:
    break;
  case IDSOPT_CMN_EFFICIENCY_MODE_EN_BALANCEDMEMORYPERFORMANCEMODE:
    break;
  case IDSOPT_CMN_EFFICIENCY_MODE_EN_BALANCEDCOREPERFORMANCEMODE:
    break;
  case IDSOPT_CMN_EFFICIENCY_MODE_EN_BALANCEDCOREMEMORYPERFORMANCEMODE:
    break;
  case IDSOPT_CMN_EFFICIENCY_MODE_EN_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI Pstate Control
IDS_NV_READ_SKIP (IDSNVID_CMN_XGMI_PSTATE_CONTROL, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdXgmiPstateControl)) {
  case IDSOPT_CMN_XGMI_PSTATE_CONTROL_AUTO:
    break;
  case IDSOPT_CMN_XGMI_PSTATE_CONTROL_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//xGMI Pstate Selection
IDS_NV_READ_SKIP (IDSNVID_CMN_XGMI_PSTATE_SELECTION, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdXgmiPstateSelection)) {
  case IDSOPT_CMN_XGMI_PSTATE_SELECTION_HIGHSPEED:
    break;
  case IDSOPT_CMN_XGMI_PSTATE_SELECTION_LOWSPEED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//BoostFmaxEn
IDS_NV_READ_SKIP (IDSNVID_CMN_BOOST_FMAX_EN, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_BOOST_FMAX_EN_MANUAL:
    break;
  case IDSOPT_CMN_BOOST_FMAX_EN_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_BOOST_FMAX, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_BOOST_FMAX_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_BOOST_FMAX_MIN);
}

//DF PState Frequency Optimizer
IDS_NV_READ_SKIP (IDSNVID_CMN_GNB_SMU_DFFO, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdDFFODisable)) {
  case IDSOPT_CMN_GNB_SMU_DFFO_AUTO:
    break;
  case IDSOPT_CMN_GNB_SMU_DFFO_ENABLED:
    break;
  case IDSOPT_CMN_GNB_SMU_DFFO_DISABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DF Cstates
IDS_NV_READ_SKIP (IDSNVID_CMN_GNB_SMU_DF_CSTATES, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdDfCstateEnable)) {
  case IDSOPT_CMN_GNB_SMU_DF_CSTATES_DISABLED:
    break;
  case IDSOPT_CMN_GNB_SMU_DF_CSTATES_ENABLED:
    break;
  case IDSOPT_CMN_GNB_SMU_DF_CSTATES_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//CPPC
IDS_NV_READ_SKIP (IDSNVID_CMN_GNB_SMU_CPPC, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdCfgCPPCMode)) {
  case IDSOPT_CMN_GNB_SMU_CPPC_DISABLED:
    break;
  case IDSOPT_CMN_GNB_SMU_CPPC_ENABLED:
    break;
  case IDSOPT_CMN_GNB_SMU_CPPC_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//HSMP Support
IDS_NV_READ_SKIP (IDSNVID_CMN_GNB_SMU_HSMP_SUPPORT, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdCfgHSMPSupport)) {
  case IDSOPT_CMN_GNB_SMU_HSMP_SUPPORT_DISABLED:
    break;
  case IDSOPT_CMN_GNB_SMU_HSMP_SUPPORT_ENABLED:
    break;
  case IDSOPT_CMN_GNB_SMU_HSMP_SUPPORT_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//SVI3 SVC Speed Control
IDS_NV_READ_SKIP (IDSNVID_CMN_SVI3_SVC_SPEED_CTL, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_SVI3_SVC_SPEED_CTL_AUTO:
    break;
  case IDSOPT_CMN_SVI3_SVC_SPEED_CTL_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//SVI3 SVC Speed
IDS_NV_READ_SKIP (IDSNVID_CMN_SVI3_SVC_SPEED, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdSvi3SvcSpeed)) {
  case IDSOPT_CMN_SVI3_SVC_SPEED_2000MHZ:
    break;
  case IDSOPT_CMN_SVI3_SVC_SPEED_1333MHZ:
    break;
  case IDSOPT_CMN_SVI3_SVC_SPEED_500MHZ:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//3D V-Cache
IDS_NV_READ_SKIP (IDSNVID_CMN_X3D_STACK_OVERRIDE, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_X3D_STACK_OVERRIDE_AUTO:
    break;
  case IDSOPT_CMN_X3D_STACK_OVERRIDE_DISABLE:
    break;
  case IDSOPT_CMN_X3D_STACK_OVERRIDE_1STACK:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//L3 BIST
IDS_NV_READ_SKIP (IDSNVID_CMN_L3_BIST, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_L3_BIST_DISABLE:
    break;
  case IDSOPT_CMN_L3_BIST_ENABLE:
    break;
  case IDSOPT_CMN_L3_BIST_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Diagnostic Mode
IDS_NV_READ_SKIP (IDSNVID_CMN_GNB_DIAG_MODE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdCfgDiagnosticMode)) {
  case IDSOPT_CMN_GNB_DIAG_MODE_DISABLED:
    break;
  case IDSOPT_CMN_GNB_DIAG_MODE_ENABLED:
    break;
  case IDSOPT_CMN_GNB_DIAG_MODE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//GMI Folding
IDS_NV_READ_SKIP (IDSNVID_CMN_GNB_SMU_GMI_FOLDING, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdGmiFolding)) {
  case IDSOPT_CMN_GNB_SMU_GMI_FOLDING_DISABLED:
    break;
  case IDSOPT_CMN_GNB_SMU_GMI_FOLDING_ENABLED:
    break;
  case IDSOPT_CMN_GNB_SMU_GMI_FOLDING_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Separate CPU power plane throttling
IDS_NV_READ_SKIP (IDSNVID_CMN_THROTTLER_MODE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdThrottlerMode)) {
  case IDSOPT_CMN_THROTTLER_MODE_ENABLE:
    break;
  case IDSOPT_CMN_THROTTLER_MODE_DISABLE:
    break;
  case IDSOPT_CMN_THROTTLER_MODE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DfPstate Range Control
IDS_NV_READ_SKIP (IDSNVID_CMN_DF_PSTATE_RANGE_CTL, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdDfPstateRangeSupportEn)) {
  case IDSOPT_CMN_DF_PSTATE_RANGE_CTL_DISABLE:
    break;
  case IDSOPT_CMN_DF_PSTATE_RANGE_CTL_ENABLE:
    break;
  case IDSOPT_CMN_DF_PSTATE_RANGE_CTL_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DfPstate Max Index
IDS_NV_READ_SKIP (IDSNVID_CMN_DF_PSTATE_MAX, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdDfPstateRangeMax)) {
  case IDSOPT_CMN_DF_PSTATE_MAX_DFP0:
    break;
  case IDSOPT_CMN_DF_PSTATE_MAX_DFP1:
    break;
  case IDSOPT_CMN_DF_PSTATE_MAX_DFP2:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DfPstate Min Index
IDS_NV_READ_SKIP (IDSNVID_CMN_DF_PSTATE_MIN, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdDfPstateRangeMin)) {
  case IDSOPT_CMN_DF_PSTATE_MIN_DFP0:
    break;
  case IDSOPT_CMN_DF_PSTATE_MIN_DFP1:
    break;
  case IDSOPT_CMN_DF_PSTATE_MIN_DFP2:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//NBIO RAS Control
IDS_NV_READ_SKIP (IDSNVID_CMN_RAS_CONTROL, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdNbioRASControlV2)) {
  case IDSOPT_CMN_RAS_CONTROL_DISABLED:
    break;
  case IDSOPT_CMN_RAS_CONTROL_MCA:
    break;
  case IDSOPT_CMN_RAS_CONTROL_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//NBIO SyncFlood Generation
IDS_NV_READ_SKIP (IDSNVID_CMN_NBIO_SYNC_FLOOD_GEN, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdMaskNbioSyncFlood)) {
  case IDSOPT_CMN_NBIO_SYNC_FLOOD_GEN_ENABLED:
    break;
  case IDSOPT_CMN_NBIO_SYNC_FLOOD_GEN_DISABLED:
    break;
  case IDSOPT_CMN_NBIO_SYNC_FLOOD_GEN_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//NBIO SyncFlood Reporting
IDS_NV_READ_SKIP (IDSNVID_PCD_SYNC_FLOOD_TO_APML, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdSyncFloodToApml)) {
  case IDSOPT_PCD_SYNC_FLOOD_TO_APML_ENABLED:
    break;
  case IDSOPT_PCD_SYNC_FLOOD_TO_APML_DISABLED:
    break;
  case IDSOPT_PCD_SYNC_FLOOD_TO_APML_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//PCIe Aer Reporting Mechanism
IDS_NV_READ_SKIP (IDSNVID_CMN_GNB_AMD_PCIE_AER_REPORT_MECHANISM, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdPcieAerReportMechanism)) {
  case IDSOPT_CMN_GNB_AMD_PCIE_AER_REPORT_MECHANISM_FIRMWAREFIRST:
    break;
  case IDSOPT_CMN_GNB_AMD_PCIE_AER_REPORT_MECHANISM_FIRMWAREFIRSTBUTALLOWOSFIRST:
    break;
  case IDSOPT_CMN_GNB_AMD_PCIE_AER_REPORT_MECHANISM_OSFIRST:
    break;
  case IDSOPT_CMN_GNB_AMD_PCIE_AER_REPORT_MECHANISM_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Edpc Control
IDS_NV_READ_SKIP (IDSNVID_EDPC_CONTROL, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdEdpcEnable)) {
  case IDSOPT_EDPC_CONTROL_DISABLED:
    break;
  case IDSOPT_EDPC_CONTROL_ENABLED:
    break;
  case IDSOPT_EDPC_CONTROL_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//ACS RAS Request Value
IDS_NV_READ_SKIP (IDSNVID_ACS_RAS_VALUE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet16 (PcdRASAcsValue)) {
  case IDSOPT_ACS_RAS_VALUE_DIRECTREQUESTACCESSENABLED:
    break;
  case IDSOPT_ACS_RAS_VALUE_REQUESTBLOCKINGENABLED:
    break;
  case IDSOPT_ACS_RAS_VALUE_REQUESTREDIRECTENABLED:
    break;
  case IDSOPT_ACS_RAS_VALUE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//NBIO Poison Consumption
IDS_NV_READ_SKIP (IDSNVID_CMN_POISON_CONSUMPTION, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdNbioPoisonConsumption)) {
  case IDSOPT_CMN_POISON_CONSUMPTION_AUTO:
    break;
  case IDSOPT_CMN_POISON_CONSUMPTION_DISABLED:
    break;
  case IDSOPT_CMN_POISON_CONSUMPTION_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sync Flood on PCIe Fatal Error
IDS_NV_READ_SKIP (IDSNVID_CMN_GNB_RAS_SYNCFLOOD_PCIE_FATAL_ERROR, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdPcieSyncFloodOnFatal)) {
  case IDSOPT_CMN_GNB_RAS_SYNCFLOOD_PCIE_FATAL_ERROR_AUTO:
    break;
  case IDSOPT_CMN_GNB_RAS_SYNCFLOOD_PCIE_FATAL_ERROR_TRUE:
    break;
  case IDSOPT_CMN_GNB_RAS_SYNCFLOOD_PCIE_FATAL_ERROR_FALSE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//NBIO RAS Numerical Common Options
IDS_NV_READ_SKIP (IDSNVID_CMN_RAS_NUMERICAL_COMMON_OPTIONS, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_RAS_NUMERICAL_COMMON_OPTIONS_DISABLE:
    break;
  case IDSOPT_CMN_RAS_NUMERICAL_COMMON_OPTIONS_MANUAL:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_PCD_EGRESS_POISON_SEVERITY_HI, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_PCD_EGRESS_POISON_SEVERITY_HI_MAX);
  ASSERT (IdsNvValue >= IDSOPT_PCD_EGRESS_POISON_SEVERITY_HI_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_PCD_EGRESS_POISON_SEVERITY_LO, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_PCD_EGRESS_POISON_SEVERITY_LO_MAX);
  ASSERT (IdsNvValue >= IDSOPT_PCD_EGRESS_POISON_SEVERITY_LO_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_PCD_AMD_NBIO_EGRESS_POISON_MASK_HI, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_PCD_AMD_NBIO_EGRESS_POISON_MASK_HI_MAX);
  ASSERT (IdsNvValue >= IDSOPT_PCD_AMD_NBIO_EGRESS_POISON_MASK_HI_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_PCD_AMD_NBIO_EGRESS_POISON_MASK_LO, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_PCD_AMD_NBIO_EGRESS_POISON_MASK_LO_MAX);
  ASSERT (IdsNvValue >= IDSOPT_PCD_AMD_NBIO_EGRESS_POISON_MASK_LO_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_PCD_AMD_NBIO_RAS_UCP_MASK_HI, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_PCD_AMD_NBIO_RAS_UCP_MASK_HI_MAX);
  ASSERT (IdsNvValue >= IDSOPT_PCD_AMD_NBIO_RAS_UCP_MASK_HI_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_PCD_AMD_NBIO_RAS_UCP_MASK_LO, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_PCD_AMD_NBIO_RAS_UCP_MASK_LO_MAX);
  ASSERT (IdsNvValue >= IDSOPT_PCD_AMD_NBIO_RAS_UCP_MASK_LO_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_PCD_SYSHUB_WDT_TIMER_INTERVAL, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_PCD_SYSHUB_WDT_TIMER_INTERVAL_MAX);
  ASSERT (IdsNvValue >= IDSOPT_PCD_SYSHUB_WDT_TIMER_INTERVAL_MIN);
}

//Data Object Exchange
IDS_NV_READ_SKIP (IDSNVID_CMN_GNB_DATA_OBJECT_EXCHANGE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdDataObjectExchange)) {
  case IDSOPT_CMN_GNB_DATA_OBJECT_EXCHANGE_DISABLED:
    break;
  case IDSOPT_CMN_GNB_DATA_OBJECT_EXCHANGE_ENABLED:
    break;
  case IDSOPT_CMN_GNB_DATA_OBJECT_EXCHANGE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//RTM Margining Support
IDS_NV_READ_SKIP (IDSNVID_CMN_GNB_RTM_MARGINING_SUPPORT, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdCfgRxMarginPersistenceMode)) {
  case IDSOPT_CMN_GNB_RTM_MARGINING_SUPPORT_DISABLE:
    break;
  case IDSOPT_CMN_GNB_RTM_MARGINING_SUPPORT_ENABLE:
    break;
  case IDSOPT_CMN_GNB_RTM_MARGINING_SUPPORT_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Multi Auto Speed Change On Last Rate
IDS_NV_READ_SKIP (IDSNVID_CMN_NBIO_FORCE_SPEED_LAST_ADVERTISED, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdLcMultAutoSpdChgOnLastRateEnable)) {
  case IDSOPT_CMN_NBIO_FORCE_SPEED_LAST_ADVERTISED_DISABLE:
    break;
  case IDSOPT_CMN_NBIO_FORCE_SPEED_LAST_ADVERTISED_ENABLE:
    break;
  case IDSOPT_CMN_NBIO_FORCE_SPEED_LAST_ADVERTISED_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Multi Upstream Auto Speed Change
IDS_NV_READ_SKIP (IDSNVID_CMN_LC_MULT_UPSTREAM_AUTO, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdCfgAutoSpeedChangeEnable)) {
  case IDSOPT_CMN_LC_MULT_UPSTREAM_AUTO_DISABLED:
    break;
  case IDSOPT_CMN_LC_MULT_UPSTREAM_AUTO_ENABLED:
    break;
  case IDSOPT_CMN_LC_MULT_UPSTREAM_AUTO_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Allow Compliance
IDS_NV_READ_SKIP (IDSNVID_STRAP_COMPLIANCE_DIS, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet16 (PcdAmdAllowCompliance)) {
  case IDSOPT_STRAP_COMPLIANCE_DIS_AUTO:
    break;
  case IDSOPT_STRAP_COMPLIANCE_DIS_DISABLE:
    break;
  case IDSOPT_STRAP_COMPLIANCE_DIS_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//EQ Bypass To Highest Rate
IDS_NV_READ_SKIP (IDSNVID_CMN_NBIO_PCIE_ADVERTISE_EQ_TO_HIGH_RATE_SUPPORT, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdAdvertiseEqToHighRateSupport)) {
  case IDSOPT_CMN_NBIO_PCIE_ADVERTISE_EQ_TO_HIGH_RATE_SUPPORT_DISABLE:
    break;
  case IDSOPT_CMN_NBIO_PCIE_ADVERTISE_EQ_TO_HIGH_RATE_SUPPORT_ENABLE:
    break;
  case IDSOPT_CMN_NBIO_PCIE_ADVERTISE_EQ_TO_HIGH_RATE_SUPPORT_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Data Link Feature Cap
IDS_NV_READ_SKIP (IDSNVID_CMN_GNB_DATA_LINK_FEATURE_CAP, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdDlfCapEnV2)) {
  case IDSOPT_CMN_GNB_DATA_LINK_FEATURE_CAP_ENABLED:
    break;
  case IDSOPT_CMN_GNB_DATA_LINK_FEATURE_CAP_DISABLED:
    break;
  case IDSOPT_CMN_GNB_DATA_LINK_FEATURE_CAP_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Data Link Feature Exchange
IDS_NV_READ_SKIP (IDSNVID_CMN_GNB_DATA_LINK_FEATURE_EXCHANGE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdDlfExEnV2)) {
  case IDSOPT_CMN_GNB_DATA_LINK_FEATURE_EXCHANGE_ENABLED:
    break;
  case IDSOPT_CMN_GNB_DATA_LINK_FEATURE_EXCHANGE_DISABLED:
    break;
  case IDSOPT_CMN_GNB_DATA_LINK_FEATURE_EXCHANGE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//SRIS
IDS_NV_READ_SKIP (IDSNVID_CMN_GNB_SRIS, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdSrisEnableMode)) {
  case IDSOPT_CMN_GNB_SRIS_AUTO:
    break;
  case IDSOPT_CMN_GNB_SRIS_DISABLE:
    break;
  case IDSOPT_CMN_GNB_SRIS_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//ACS Enable
IDS_NV_READ_SKIP (IDSNVID_DBG_GNB_DBG_ACS_ENABLE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdCfgACSEnable)) {
  case IDSOPT_DBG_GNB_DBG_ACS_ENABLE_ENABLE:
    break;
  case IDSOPT_DBG_GNB_DBG_ACS_ENABLE_DISABLED:
    break;
  case IDSOPT_DBG_GNB_DBG_ACS_ENABLE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//PCIe Ten Bit Tag Support
IDS_NV_READ_SKIP (IDSNVID_GNB_CMN_PCIE_TBT_SUPPORT, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdCfgPcieTbtSupport)) {
  case IDSOPT_GNB_CMN_PCIE_TBT_SUPPORT_DISABLE:
    break;
  case IDSOPT_GNB_CMN_PCIE_TBT_SUPPORT_ENABLE:
    break;
  case IDSOPT_GNB_CMN_PCIE_TBT_SUPPORT_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//PCIe ARI Enumeration
IDS_NV_READ_SKIP (IDSNVID_GNB_CMN_PCIE_ARI_ENUMERATION, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdPcieAriForwardingEnable)) {
  case IDSOPT_GNB_CMN_PCIE_ARI_ENUMERATION_DISABLE:
    break;
  case IDSOPT_GNB_CMN_PCIE_ARI_ENUMERATION_ENABLE:
    break;
  case IDSOPT_GNB_CMN_PCIE_ARI_ENUMERATION_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//PCIe ARI Support
IDS_NV_READ_SKIP (IDSNVID_CMN_GNB_PCIE_ARI_SUPPORT, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdCfgPcieAriSupport)) {
  case IDSOPT_CMN_GNB_PCIE_ARI_SUPPORT_DISABLE:
    break;
  case IDSOPT_CMN_GNB_PCIE_ARI_SUPPORT_ENABLE:
    break;
  case IDSOPT_CMN_GNB_PCIE_ARI_SUPPORT_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Presence Detect Select mode
IDS_NV_READ_SKIP (IDSNVID_PRESENCE_DETECT_SELECTMODE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdPresenceDetectSelectMode)) {
  case IDSOPT_PRESENCE_DETECT_SELECTMODE_OR:
    break;
  case IDSOPT_PRESENCE_DETECT_SELECTMODE_AND:
    break;
  case IDSOPT_PRESENCE_DETECT_SELECTMODE_AUTO:
    break;
  case IDSOPT_PRESENCE_DETECT_SELECTMODE_INBANDONLY:
    break;
  case IDSOPT_PRESENCE_DETECT_SELECTMODE_OUTOFBANDONLY:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Hot Plug Handling mode
IDS_NV_READ_SKIP (IDSNVID_HOT_PLUG_HANDLING_MODE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdHotPlugHandlingMode)) {
  case IDSOPT_HOT_PLUG_HANDLING_MODE_OSFIRST:
    break;
  case IDSOPT_HOT_PLUG_HANDLING_MODE_FIRMWAREFIRSTEDRIFOSSUPPORTS:
    break;
  case IDSOPT_HOT_PLUG_HANDLING_MODE_FIRMWAREFIRSTBUTALLOWOSFIRST:
    break;
  case IDSOPT_HOT_PLUG_HANDLING_MODE_SYSTEMFIRMWAREINTERMEDIARY:
    break;
  case IDSOPT_HOT_PLUG_HANDLING_MODE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Presence Detect State Settle Time
IDS_NV_READ_SKIP (IDSNVID_HOT_PLUG_PD_SETTLE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdHotPlugPDSettle)) {
  case IDSOPT_HOT_PLUG_PD_SETTLE_AUTO:
    break;
  case IDSOPT_HOT_PLUG_PD_SETTLE_TRUE:
    break;
  case IDSOPT_HOT_PLUG_PD_SETTLE_FALSE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_HOT_PLUG_SETTLE_TIME, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_HOT_PLUG_SETTLE_TIME_MAX);
  ASSERT (IdsNvValue >= IDSOPT_HOT_PLUG_SETTLE_TIME_MIN);
}

//Hotplug Support
IDS_NV_READ_SKIP (IDSNVID_HOTPLUG_SUPPORT, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdHotPlugSupport)) {
  case IDSOPT_HOTPLUG_SUPPORT_AUTO:
    break;
  case IDSOPT_HOTPLUG_SUPPORT_DISABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Early Link Speed
IDS_NV_READ_SKIP (IDSNVID_CMN_EARLY_LINK_SPEED, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_EARLY_LINK_SPEED_MAX:
    break;
  case IDSOPT_CMN_EARLY_LINK_SPEED_GEN1:
    break;
  case IDSOPT_CMN_EARLY_LINK_SPEED_GEN2:
    break;
  case IDSOPT_CMN_EARLY_LINK_SPEED_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Enable AER Cap
IDS_NV_READ_SKIP (IDSNVID_DBG_GNB_DBG_AERCAP_ENABLE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdCfgAEREnable)) {
  case IDSOPT_DBG_GNB_DBG_AERCAP_ENABLE_ENABLE:
    break;
  case IDSOPT_DBG_GNB_DBG_AERCAP_ENABLE_DISABLED:
    break;
  case IDSOPT_DBG_GNB_DBG_AERCAP_ENABLE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//PCIE Link Speed Capability
IDS_NV_READ_SKIP (IDSNVID_CMN_PCIE_CAP_LINK_SPEED, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdCfgForcePcieGenSpeed)) {
  case IDSOPT_CMN_PCIE_CAP_LINK_SPEED_MAXIMUMSPEED:
    break;
  case IDSOPT_CMN_PCIE_CAP_LINK_SPEED_GEN1:
    break;
  case IDSOPT_CMN_PCIE_CAP_LINK_SPEED_GEN2:
    break;
  case IDSOPT_CMN_PCIE_CAP_LINK_SPEED_GEN3:
    break;
  case IDSOPT_CMN_PCIE_CAP_LINK_SPEED_GEN4:
    break;
  case IDSOPT_CMN_PCIE_CAP_LINK_SPEED_GEN5:
    break;
  case IDSOPT_CMN_PCIE_CAP_LINK_SPEED_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//PCIE Target Link Speed
IDS_NV_READ_SKIP (IDSNVID_CMN_PCIE_TARGET_LINK_SPEED, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdTargetPcieGenSpeed)) {
  case IDSOPT_CMN_PCIE_TARGET_LINK_SPEED_MAXIMUMSPEED:
    break;
  case IDSOPT_CMN_PCIE_TARGET_LINK_SPEED_GEN1:
    break;
  case IDSOPT_CMN_PCIE_TARGET_LINK_SPEED_GEN2:
    break;
  case IDSOPT_CMN_PCIE_TARGET_LINK_SPEED_GEN3:
    break;
  case IDSOPT_CMN_PCIE_TARGET_LINK_SPEED_GEN4:
    break;
  case IDSOPT_CMN_PCIE_TARGET_LINK_SPEED_GEN5:
    break;
  case IDSOPT_CMN_PCIE_TARGET_LINK_SPEED_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//ASPM Control
IDS_NV_READ_SKIP (IDSNVID_CMN_ALL_PORTS_ASPM, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdPcieLinkAspmAllPort)) {
  case IDSOPT_CMN_ALL_PORTS_ASPM_DISABLE:
    break;
  case IDSOPT_CMN_ALL_PORTS_ASPM_L1:
    break;
  case IDSOPT_CMN_ALL_PORTS_ASPM_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//MCTP Enable
IDS_NV_READ_SKIP (IDSNVID_CMN_NBIO_MCTP_EN, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdMCTPEnable)) {
  case IDSOPT_CMN_NBIO_MCTP_EN_DISABLE:
    break;
  case IDSOPT_CMN_NBIO_MCTP_EN_ENABLE:
    break;
  case IDSOPT_CMN_NBIO_MCTP_EN_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//MCTP Mode
IDS_NV_READ_SKIP (IDSNVID_CMN_NBIO_MCTP_MODE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdMCTPMode)) {
  case IDSOPT_CMN_NBIO_MCTP_MODE_MCTPBRIDGE:
    break;
  case IDSOPT_CMN_NBIO_MCTP_MODE_LEGACYMCTPMCTPBRIDGE:
    break;
  case IDSOPT_CMN_NBIO_MCTP_MODE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//MCTP discovery notify message
IDS_NV_READ_SKIP (IDSNVID_CMN_NBIO_MCTP_DISCOVERY_NOTIFY_MESSAGE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdMCTPDiscoveryNotify)) {
  case IDSOPT_CMN_NBIO_MCTP_DISCOVERY_NOTIFY_MESSAGE_DISABLE:
    break;
  case IDSOPT_CMN_NBIO_MCTP_DISCOVERY_NOTIFY_MESSAGE_ENABLE:
    break;
  case IDSOPT_CMN_NBIO_MCTP_DISCOVERY_NOTIFY_MESSAGE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Non-PCIe Compliant Support
IDS_NV_READ_SKIP (IDSNVID_CMN_NBIO_PCIE_NON_PCIE_COMPLIANT_SUPPORT, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdPcieNonPcieCompliantTrainingFailureSupport)) {
  case IDSOPT_CMN_NBIO_PCIE_NON_PCIE_COMPLIANT_SUPPORT_DISABLE:
    break;
  case IDSOPT_CMN_NBIO_PCIE_NON_PCIE_COMPLIANT_SUPPORT_ENABLE:
    break;
  case IDSOPT_CMN_NBIO_PCIE_NON_PCIE_COMPLIANT_SUPPORT_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Limit hotplug devices to PCIe boot speed
IDS_NV_READ_SKIP (IDSNVID_CMN_LIMIT_HP_DEVICES_TO_PCIE_BOOT_SPEED, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdLimitHpDevicesToPcieBootSpeed)) {
  case IDSOPT_CMN_LIMIT_HP_DEVICES_TO_PCIE_BOOT_SPEED_AUTO:
    break;
  case IDSOPT_CMN_LIMIT_HP_DEVICES_TO_PCIE_BOOT_SPEED_ENABLE:
    break;
  case IDSOPT_CMN_LIMIT_HP_DEVICES_TO_PCIE_BOOT_SPEED_DISABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Enable PCIe SFI Config via OOB
IDS_NV_READ_SKIP (IDSNVID_CMN_PC_IE_SFI_CONFIGVIA_OOB_EN, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdPCIeSFIConfigviaOOBEn)) {
  case IDSOPT_CMN_PC_IE_SFI_CONFIGVIA_OOB_EN_TRUE:
    break;
  case IDSOPT_CMN_PC_IE_SFI_CONFIGVIA_OOB_EN_FALSE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//PCIE Idle Power Setting
IDS_NV_READ_SKIP (IDSNVID_CMN_NBIO_PCIE_IDLE_POWER_SETTING, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdPcieIdlePowerSetting)) {
  case IDSOPT_CMN_NBIO_PCIE_IDLE_POWER_SETTING_OPTIMIZEFORLATENCY:
    break;
  case IDSOPT_CMN_NBIO_PCIE_IDLE_POWER_SETTING_OPTIMIZEFORPERFPOWER:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//ACS Rcc_Dev0
IDS_NV_READ_SKIP (IDSNVID_CFG_ACS_EN_RCC_DEV0, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAcsEnRccDev0)) {
  case IDSOPT_CFG_ACS_EN_RCC_DEV0_AUTO:
    break;
  case IDSOPT_CFG_ACS_EN_RCC_DEV0_DISABLE:
    break;
  case IDSOPT_CFG_ACS_EN_RCC_DEV0_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//AER Rcc_Dev0
IDS_NV_READ_SKIP (IDSNVID_CFG_AER_EN_RCC_DEV0, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAerEnRccDev0)) {
  case IDSOPT_CFG_AER_EN_RCC_DEV0_AUTO:
    break;
  case IDSOPT_CFG_AER_EN_RCC_DEV0_DISABLE:
    break;
  case IDSOPT_CFG_AER_EN_RCC_DEV0_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DlfEnableStrap1
IDS_NV_READ_SKIP (IDSNVID_CFG_DLF_EN_STRAP1, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdDlfEnStrap1)) {
  case IDSOPT_CFG_DLF_EN_STRAP1_AUTO:
    break;
  case IDSOPT_CFG_DLF_EN_STRAP1_DISABLE:
    break;
  case IDSOPT_CFG_DLF_EN_STRAP1_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Phy16GTStrap1
IDS_NV_READ_SKIP (IDSNVID_CFG_PHY16GT_STRAP1, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdPhy16gtStrap1)) {
  case IDSOPT_CFG_PHY16GT_STRAP1_AUTO:
    break;
  case IDSOPT_CFG_PHY16GT_STRAP1_DISABLE:
    break;
  case IDSOPT_CFG_PHY16GT_STRAP1_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//MarginEnStrap1
IDS_NV_READ_SKIP (IDSNVID_CFG_MARGIN_EN_STRAP1, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdMarginEnStrap1)) {
  case IDSOPT_CFG_MARGIN_EN_STRAP1_AUTO:
    break;
  case IDSOPT_CFG_MARGIN_EN_STRAP1_DISABLE:
    break;
  case IDSOPT_CFG_MARGIN_EN_STRAP1_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//SourceValStrap5
IDS_NV_READ_SKIP (IDSNVID_CFG_ACS_SOURCE_VAL_STRAP5, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAcsSourceValStrap5)) {
  case IDSOPT_CFG_ACS_SOURCE_VAL_STRAP5_AUTO:
    break;
  case IDSOPT_CFG_ACS_SOURCE_VAL_STRAP5_DISABLE:
    break;
  case IDSOPT_CFG_ACS_SOURCE_VAL_STRAP5_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//TranslationalBlockingStrap5
IDS_NV_READ_SKIP (IDSNVID_CFG_ACS_TRANSLATIONAL_BLOCKING_STRAP5, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAcsTranslationalBlockingStrap5)) {
  case IDSOPT_CFG_ACS_TRANSLATIONAL_BLOCKING_STRAP5_AUTO:
    break;
  case IDSOPT_CFG_ACS_TRANSLATIONAL_BLOCKING_STRAP5_DISABLE:
    break;
  case IDSOPT_CFG_ACS_TRANSLATIONAL_BLOCKING_STRAP5_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//P2pReq ACS Control
IDS_NV_READ_SKIP (IDSNVID_CFG_ACS_P2P_REQ, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAcsP2pReq)) {
  case IDSOPT_CFG_ACS_P2P_REQ_AUTO:
    break;
  case IDSOPT_CFG_ACS_P2P_REQ_DISABLE:
    break;
  case IDSOPT_CFG_ACS_P2P_REQ_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//P2pCompStrap5
IDS_NV_READ_SKIP (IDSNVID_CFG_ACS_P2P_COMP_STRAP5, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAcsP2pCompStrap5)) {
  case IDSOPT_CFG_ACS_P2P_COMP_STRAP5_AUTO:
    break;
  case IDSOPT_CFG_ACS_P2P_COMP_STRAP5_DISABLE:
    break;
  case IDSOPT_CFG_ACS_P2P_COMP_STRAP5_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//UpstreamFwdStrap5
IDS_NV_READ_SKIP (IDSNVID_CFG_ACS_UPSTREAM_FWD_STRAP5, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAcsUpstreamFwdStrap5)) {
  case IDSOPT_CFG_ACS_UPSTREAM_FWD_STRAP5_AUTO:
    break;
  case IDSOPT_CFG_ACS_UPSTREAM_FWD_STRAP5_DISABLE:
    break;
  case IDSOPT_CFG_ACS_UPSTREAM_FWD_STRAP5_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//P2PEgressStrap5
IDS_NV_READ_SKIP (IDSNVID_CFG_ACS_P2_P_EGRESS_STRAP5, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAcsP2PEgressStrap5)) {
  case IDSOPT_CFG_ACS_P2_P_EGRESS_STRAP5_AUTO:
    break;
  case IDSOPT_CFG_ACS_P2_P_EGRESS_STRAP5_DISABLE:
    break;
  case IDSOPT_CFG_ACS_P2_P_EGRESS_STRAP5_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DirectTranslatedStrap5
IDS_NV_READ_SKIP (IDSNVID_CFG_ACS_DIRECT_TRANSLATED_STRAP5, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAcsDirectTranslatedStrap5)) {
  case IDSOPT_CFG_ACS_DIRECT_TRANSLATED_STRAP5_AUTO:
    break;
  case IDSOPT_CFG_ACS_DIRECT_TRANSLATED_STRAP5_DISABLE:
    break;
  case IDSOPT_CFG_ACS_DIRECT_TRANSLATED_STRAP5_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//SsidEnStrap5
IDS_NV_READ_SKIP (IDSNVID_CFG_ACS_SSID_EN_STRAP5, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAcsSsidEnStrap5)) {
  case IDSOPT_CFG_ACS_SSID_EN_STRAP5_AUTO:
    break;
  case IDSOPT_CFG_ACS_SSID_EN_STRAP5_DISABLE:
    break;
  case IDSOPT_CFG_ACS_SSID_EN_STRAP5_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//PriEnPageReq
IDS_NV_READ_SKIP (IDSNVID_CFG_PRI_EN_PAGE_REQ, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdPriEnPageReq)) {
  case IDSOPT_CFG_PRI_EN_PAGE_REQ_AUTO:
    break;
  case IDSOPT_CFG_PRI_EN_PAGE_REQ_DISABLE:
    break;
  case IDSOPT_CFG_PRI_EN_PAGE_REQ_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//PriResetPageReq
IDS_NV_READ_SKIP (IDSNVID_CFG_PRI_RESET_PAGE_REQ, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdPriResetPageReq)) {
  case IDSOPT_CFG_PRI_RESET_PAGE_REQ_AUTO:
    break;
  case IDSOPT_CFG_PRI_RESET_PAGE_REQ_DISABLE:
    break;
  case IDSOPT_CFG_PRI_RESET_PAGE_REQ_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//SourceVal ACS cntl
IDS_NV_READ_SKIP (IDSNVID_CFG_ACS_SOURCE_VAL, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAcsSourceVal)) {
  case IDSOPT_CFG_ACS_SOURCE_VAL_AUTO:
    break;
  case IDSOPT_CFG_ACS_SOURCE_VAL_DISABLE:
    break;
  case IDSOPT_CFG_ACS_SOURCE_VAL_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//TranslationalBlocking ACS Control
IDS_NV_READ_SKIP (IDSNVID_CFG_ACS_TRANSLATIONAL_BLOCKING, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAcsTranslationalBlocking)) {
  case IDSOPT_CFG_ACS_TRANSLATIONAL_BLOCKING_AUTO:
    break;
  case IDSOPT_CFG_ACS_TRANSLATIONAL_BLOCKING_DISABLE:
    break;
  case IDSOPT_CFG_ACS_TRANSLATIONAL_BLOCKING_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//P2pComp ACS Control
IDS_NV_READ_SKIP (IDSNVID_CFG_ACS_P2P_COMP, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAcsP2pComp)) {
  case IDSOPT_CFG_ACS_P2P_COMP_AUTO:
    break;
  case IDSOPT_CFG_ACS_P2P_COMP_DISABLE:
    break;
  case IDSOPT_CFG_ACS_P2P_COMP_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//UpstreamFwd ACS Control
IDS_NV_READ_SKIP (IDSNVID_CFG_ACS_UPSTREAM_FWD, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAcsUpstreamFwd)) {
  case IDSOPT_CFG_ACS_UPSTREAM_FWD_AUTO:
    break;
  case IDSOPT_CFG_ACS_UPSTREAM_FWD_DISABLE:
    break;
  case IDSOPT_CFG_ACS_UPSTREAM_FWD_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//P2PEgress ACS Control
IDS_NV_READ_SKIP (IDSNVID_CFG_ACS_P2_P_EGRESS, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAcsP2PEgress)) {
  case IDSOPT_CFG_ACS_P2_P_EGRESS_AUTO:
    break;
  case IDSOPT_CFG_ACS_P2_P_EGRESS_DISABLE:
    break;
  case IDSOPT_CFG_ACS_P2_P_EGRESS_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//P2pReqStrap5
IDS_NV_READ_SKIP (IDSNVID_CFG_ACS_P2P_REQ_STRAP5, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAcsP2pReqStrap5)) {
  case IDSOPT_CFG_ACS_P2P_REQ_STRAP5_AUTO:
    break;
  case IDSOPT_CFG_ACS_P2P_REQ_STRAP5_DISABLE:
    break;
  case IDSOPT_CFG_ACS_P2P_REQ_STRAP5_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//E2E_PREFIX
IDS_NV_READ_SKIP (IDSNVID_CFG_E2_E_PREFIX, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdRccDev0E2EPrefix)) {
  case IDSOPT_CFG_E2_E_PREFIX_AUTO:
    break;
  case IDSOPT_CFG_E2_E_PREFIX_DISABLE:
    break;
  case IDSOPT_CFG_E2_E_PREFIX_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//EXTENDED_FMT
IDS_NV_READ_SKIP (IDSNVID_CFG_EXTENDED_FMT_SUPPORTED, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdRccDev0ExtendedFmtSupported)) {
  case IDSOPT_CFG_EXTENDED_FMT_SUPPORTED_AUTO:
    break;
  case IDSOPT_CFG_EXTENDED_FMT_SUPPORTED_DISABLE:
    break;
  case IDSOPT_CFG_EXTENDED_FMT_SUPPORTED_ENABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//AtomicRoutingStrap5
IDS_NV_READ_SKIP (IDSNVID_CMN_NBIO_ATOMIC_ROUTING_STRAP5, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAtomicRoutingEnStrap5)) {
  case IDSOPT_CMN_NBIO_ATOMIC_ROUTING_STRAP5_DISABLE:
    break;
  case IDSOPT_CMN_NBIO_ATOMIC_ROUTING_STRAP5_ENABLE:
    break;
  case IDSOPT_CMN_NBIO_ATOMIC_ROUTING_STRAP5_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//SEV-SNP Support
IDS_NV_READ_SKIP (IDSNVID_SEV_SNP_SUPPORT, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdCfgSevSnpSupport)) {
  case IDSOPT_SEV_SNP_SUPPORT_DISABLE:
    break;
  case IDSOPT_SEV_SNP_SUPPORT_ENABLE:
    break;
  case IDSOPT_SEV_SNP_SUPPORT_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//SEV-TIO Support
IDS_NV_READ_SKIP (IDSNVID_SEV_TIO_SUPPORT, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdCfgSevTioSupport)) {
  case IDSOPT_SEV_TIO_SUPPORT_DISABLED:
    break;
  case IDSOPT_SEV_TIO_SUPPORT_ENABLED:
    break;
  case IDSOPT_SEV_TIO_SUPPORT_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DRTM Memory Reservation
IDS_NV_READ_SKIP (IDSNVID_CMN_DRTM_MEMORY_RESERVATION, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_DRTM_MEMORY_RESERVATION_DISABLED:
    break;
  case IDSOPT_CMN_DRTM_MEMORY_RESERVATION_ENABLED:
    break;
  case IDSOPT_CMN_DRTM_MEMORY_RESERVATION_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DRTM Virtual Device Support
IDS_NV_READ_SKIP (IDSNVID_CMN_DRTM_SUPPORT, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdPspDrtmVirtualDevice)) {
  case IDSOPT_CMN_DRTM_SUPPORT_DISABLED:
    break;
  case IDSOPT_CMN_DRTM_SUPPORT_ENABLED:
    break;
  case IDSOPT_CMN_DRTM_SUPPORT_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DMA Protection
IDS_NV_READ_SKIP (IDSNVID_CMN_DMA_PROTECTION, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdDmaProtection)) {
  case IDSOPT_CMN_DMA_PROTECTION_AUTO:
    break;
  case IDSOPT_CMN_DMA_PROTECTION_ENABLED:
    break;
  case IDSOPT_CMN_DMA_PROTECTION_DISABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//IOMMU
IDS_NV_READ_SKIP (IDSNVID_CMN_GNB_NB_IOMMU, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdCfgIommuSupport)) {
  case IDSOPT_CMN_GNB_NB_IOMMU_DISABLED:
    break;
  case IDSOPT_CMN_GNB_NB_IOMMU_ENABLED:
    break;
  case IDSOPT_CMN_GNB_NB_IOMMU_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//DMAr Support
IDS_NV_READ_SKIP (IDSNVID_CMN_DMAR_SUPPORT, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdIvInfoDmaReMap)) {
  case IDSOPT_CMN_DMAR_SUPPORT_DISABLED:
    break;
  case IDSOPT_CMN_DMAR_SUPPORT_ENABLED:
    break;
  case IDSOPT_CMN_DMAR_SUPPORT_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Enable Port Bifurcation
IDS_NV_READ_SKIP (IDSNVID_CMN_ENABLE_PORT_BIFURCATION, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdOverRideEnabled)) {
  case IDSOPT_CMN_ENABLE_PORT_BIFURCATION_AUTO:
    break;
  case IDSOPT_CMN_ENABLE_PORT_BIFURCATION_ENABLE:
    break;
  case IDSOPT_CMN_ENABLE_PORT_BIFURCATION_DISABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 0 P0 Override
IDS_NV_READ_SKIP (IDSNVID_CMN_S0_P0_OVERRIDE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdOverrideS0P0)) {
  case IDSOPT_CMN_S0_P0_OVERRIDE_AUTO:
    break;
  case IDSOPT_CMN_S0_P0_OVERRIDE_1PORTOFX82PORTSOFX4:
    break;
  case IDSOPT_CMN_S0_P0_OVERRIDE_1PORTOFX88PORTSOFX1:
    break;
  case IDSOPT_CMN_S0_P0_OVERRIDE_2PORTSOFX8:
    break;
  case IDSOPT_CMN_S0_P0_OVERRIDE_4PORTSOFX4:
    break;
  case IDSOPT_CMN_S0_P0_OVERRIDE_8PORTSOFX2:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 0 P1 Override
IDS_NV_READ_SKIP (IDSNVID_CMN_S0_P1_OVERRIDE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdOverrideS0P1)) {
  case IDSOPT_CMN_S0_P1_OVERRIDE_AUTO:
    break;
  case IDSOPT_CMN_S0_P1_OVERRIDE_1PORTOFX82PORTSOFX4:
    break;
  case IDSOPT_CMN_S0_P1_OVERRIDE_1PORTOFX88PORTSOFX1:
    break;
  case IDSOPT_CMN_S0_P1_OVERRIDE_2PORTSOFX8:
    break;
  case IDSOPT_CMN_S0_P1_OVERRIDE_4PORTSOFX4:
    break;
  case IDSOPT_CMN_S0_P1_OVERRIDE_8PORTSOFX2:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 0 P2 Override
IDS_NV_READ_SKIP (IDSNVID_CMN_S0_P2_OVERRIDE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdOverrideS0P2)) {
  case IDSOPT_CMN_S0_P2_OVERRIDE_AUTO:
    break;
  case IDSOPT_CMN_S0_P2_OVERRIDE_1PORTOFX82PORTSOFX4:
    break;
  case IDSOPT_CMN_S0_P2_OVERRIDE_1PORTOFX88PORTSOFX1:
    break;
  case IDSOPT_CMN_S0_P2_OVERRIDE_2PORTSOFX8:
    break;
  case IDSOPT_CMN_S0_P2_OVERRIDE_4PORTSOFX4:
    break;
  case IDSOPT_CMN_S0_P2_OVERRIDE_8PORTSOFX2:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 0 P3 Override
IDS_NV_READ_SKIP (IDSNVID_CMN_S0_P3_OVERRIDE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdOverrideS0P3)) {
  case IDSOPT_CMN_S0_P3_OVERRIDE_AUTO:
    break;
  case IDSOPT_CMN_S0_P3_OVERRIDE_1PORTOFX82PORTSOFX4:
    break;
  case IDSOPT_CMN_S0_P3_OVERRIDE_1PORTOFX88PORTSOFX1:
    break;
  case IDSOPT_CMN_S0_P3_OVERRIDE_2PORTSOFX8:
    break;
  case IDSOPT_CMN_S0_P3_OVERRIDE_4PORTSOFX4:
    break;
  case IDSOPT_CMN_S0_P3_OVERRIDE_8PORTSOFX2:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 1 P0 Override
IDS_NV_READ_SKIP (IDSNVID_CMN_S1_P0_OVERRIDE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdOverrideS1P0)) {
  case IDSOPT_CMN_S1_P0_OVERRIDE_AUTO:
    break;
  case IDSOPT_CMN_S1_P0_OVERRIDE_1PORTOFX82PORTSOFX4:
    break;
  case IDSOPT_CMN_S1_P0_OVERRIDE_1PORTOFX88PORTSOFX1:
    break;
  case IDSOPT_CMN_S1_P0_OVERRIDE_2PORTSOFX8:
    break;
  case IDSOPT_CMN_S1_P0_OVERRIDE_4PORTSOFX4:
    break;
  case IDSOPT_CMN_S1_P0_OVERRIDE_8PORTSOFX2:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 1 P1 Override
IDS_NV_READ_SKIP (IDSNVID_CMN_S1_P1_OVERRIDE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdOverrideS1P1)) {
  case IDSOPT_CMN_S1_P1_OVERRIDE_AUTO:
    break;
  case IDSOPT_CMN_S1_P1_OVERRIDE_1PORTOFX82PORTSOFX4:
    break;
  case IDSOPT_CMN_S1_P1_OVERRIDE_1PORTOFX88PORTSOFX1:
    break;
  case IDSOPT_CMN_S1_P1_OVERRIDE_2PORTSOFX8:
    break;
  case IDSOPT_CMN_S1_P1_OVERRIDE_4PORTSOFX4:
    break;
  case IDSOPT_CMN_S1_P1_OVERRIDE_8PORTSOFX2:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 1 P2 Override
IDS_NV_READ_SKIP (IDSNVID_CMN_S1_P2_OVERRIDE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdOverrideS1P2)) {
  case IDSOPT_CMN_S1_P2_OVERRIDE_AUTO:
    break;
  case IDSOPT_CMN_S1_P2_OVERRIDE_1PORTOFX82PORTSOFX4:
    break;
  case IDSOPT_CMN_S1_P2_OVERRIDE_1PORTOFX88PORTSOFX1:
    break;
  case IDSOPT_CMN_S1_P2_OVERRIDE_2PORTSOFX8:
    break;
  case IDSOPT_CMN_S1_P2_OVERRIDE_4PORTSOFX4:
    break;
  case IDSOPT_CMN_S1_P2_OVERRIDE_8PORTSOFX2:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket 1 P3 Override
IDS_NV_READ_SKIP (IDSNVID_CMN_S1_P3_OVERRIDE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdOverrideS1P3)) {
  case IDSOPT_CMN_S1_P3_OVERRIDE_AUTO:
    break;
  case IDSOPT_CMN_S1_P3_OVERRIDE_1PORTOFX82PORTSOFX4:
    break;
  case IDSOPT_CMN_S1_P3_OVERRIDE_1PORTOFX88PORTSOFX1:
    break;
  case IDSOPT_CMN_S1_P3_OVERRIDE_2PORTSOFX8:
    break;
  case IDSOPT_CMN_S1_P3_OVERRIDE_4PORTSOFX4:
    break;
  case IDSOPT_CMN_S1_P3_OVERRIDE_8PORTSOFX2:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//P0 Override
IDS_NV_READ_SKIP (IDSNVID_CMN_P0_OVERRIDE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdOverrideS0P0)) {
  case IDSOPT_CMN_P0_OVERRIDE_AUTO:
    break;
  case IDSOPT_CMN_P0_OVERRIDE_1PORTOFX82PORTSOFX4:
    break;
  case IDSOPT_CMN_P0_OVERRIDE_1PORTOFX88PORTSOFX1:
    break;
  case IDSOPT_CMN_P0_OVERRIDE_2PORTSOFX8:
    break;
  case IDSOPT_CMN_P0_OVERRIDE_4PORTSOFX4:
    break;
  case IDSOPT_CMN_P0_OVERRIDE_8PORTSOFX2:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//P1 Override
IDS_NV_READ_SKIP (IDSNVID_CMN_P1_OVERRIDE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdOverrideS0P1)) {
  case IDSOPT_CMN_P1_OVERRIDE_AUTO:
    break;
  case IDSOPT_CMN_P1_OVERRIDE_1PORTOFX82PORTSOFX4:
    break;
  case IDSOPT_CMN_P1_OVERRIDE_1PORTOFX88PORTSOFX1:
    break;
  case IDSOPT_CMN_P1_OVERRIDE_2PORTSOFX8:
    break;
  case IDSOPT_CMN_P1_OVERRIDE_4PORTSOFX4:
    break;
  case IDSOPT_CMN_P1_OVERRIDE_8PORTSOFX2:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//P2 Override
IDS_NV_READ_SKIP (IDSNVID_CMN_P2_OVERRIDE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdOverrideS0P2)) {
  case IDSOPT_CMN_P2_OVERRIDE_AUTO:
    break;
  case IDSOPT_CMN_P2_OVERRIDE_1PORTOFX82PORTSOFX4:
    break;
  case IDSOPT_CMN_P2_OVERRIDE_1PORTOFX88PORTSOFX1:
    break;
  case IDSOPT_CMN_P2_OVERRIDE_2PORTSOFX8:
    break;
  case IDSOPT_CMN_P2_OVERRIDE_4PORTSOFX4:
    break;
  case IDSOPT_CMN_P2_OVERRIDE_8PORTSOFX2:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//P3 Override
IDS_NV_READ_SKIP (IDSNVID_CMN_P3_OVERRIDE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdOverrideS0P3)) {
  case IDSOPT_CMN_P3_OVERRIDE_AUTO:
    break;
  case IDSOPT_CMN_P3_OVERRIDE_1PORTOFX82PORTSOFX4:
    break;
  case IDSOPT_CMN_P3_OVERRIDE_1PORTOFX88PORTSOFX1:
    break;
  case IDSOPT_CMN_P3_OVERRIDE_2PORTSOFX8:
    break;
  case IDSOPT_CMN_P3_OVERRIDE_4PORTSOFX4:
    break;
  case IDSOPT_CMN_P3_OVERRIDE_8PORTSOFX2:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//G0 Override
IDS_NV_READ_SKIP (IDSNVID_CMN_G0_OVERRIDE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdOverrideS1P0)) {
  case IDSOPT_CMN_G0_OVERRIDE_AUTO:
    break;
  case IDSOPT_CMN_G0_OVERRIDE_1PORTOFX82PORTSOFX4:
    break;
  case IDSOPT_CMN_G0_OVERRIDE_1PORTOFX88PORTSOFX1:
    break;
  case IDSOPT_CMN_G0_OVERRIDE_2PORTSOFX8:
    break;
  case IDSOPT_CMN_G0_OVERRIDE_4PORTSOFX4:
    break;
  case IDSOPT_CMN_G0_OVERRIDE_8PORTSOFX2:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//G1 Override
IDS_NV_READ_SKIP (IDSNVID_CMN_G1_OVERRIDE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdOverrideS1P1)) {
  case IDSOPT_CMN_G1_OVERRIDE_AUTO:
    break;
  case IDSOPT_CMN_G1_OVERRIDE_1PORTOFX82PORTSOFX4:
    break;
  case IDSOPT_CMN_G1_OVERRIDE_1PORTOFX88PORTSOFX1:
    break;
  case IDSOPT_CMN_G1_OVERRIDE_2PORTSOFX8:
    break;
  case IDSOPT_CMN_G1_OVERRIDE_4PORTSOFX4:
    break;
  case IDSOPT_CMN_G1_OVERRIDE_8PORTSOFX2:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//G2 Override
IDS_NV_READ_SKIP (IDSNVID_CMN_G2_OVERRIDE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdOverrideS1P2)) {
  case IDSOPT_CMN_G2_OVERRIDE_AUTO:
    break;
  case IDSOPT_CMN_G2_OVERRIDE_1PORTOFX82PORTSOFX4:
    break;
  case IDSOPT_CMN_G2_OVERRIDE_1PORTOFX88PORTSOFX1:
    break;
  case IDSOPT_CMN_G2_OVERRIDE_2PORTSOFX8:
    break;
  case IDSOPT_CMN_G2_OVERRIDE_4PORTSOFX4:
    break;
  case IDSOPT_CMN_G2_OVERRIDE_8PORTSOFX2:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//G3 Override
IDS_NV_READ_SKIP (IDSNVID_CMN_G3_OVERRIDE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdOverrideS1P3)) {
  case IDSOPT_CMN_G3_OVERRIDE_AUTO:
    break;
  case IDSOPT_CMN_G3_OVERRIDE_1PORTOFX82PORTSOFX4:
    break;
  case IDSOPT_CMN_G3_OVERRIDE_1PORTOFX88PORTSOFX1:
    break;
  case IDSOPT_CMN_G3_OVERRIDE_2PORTSOFX8:
    break;
  case IDSOPT_CMN_G3_OVERRIDE_4PORTSOFX4:
    break;
  case IDSOPT_CMN_G3_OVERRIDE_8PORTSOFX2:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Preset Search Mask Configuration (Gen3)
IDS_NV_READ_SKIP (IDSNVID_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN3, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdPcieLaneEqPresetMask8GtConfig)) {
  case IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN3_CUSTOM:
    break;
  case IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN3_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_NBIO_PCIE_SEARCH_MASK_GEN3, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_GEN3_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_GEN3_MIN);
}

//Preset Search Mask Configuration (Gen4)
IDS_NV_READ_SKIP (IDSNVID_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN4, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdPcieLaneEqPresetMask16GtConfig)) {
  case IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN4_CUSTOM:
    break;
  case IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN4_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_NBIO_PCIE_SEARCH_MASK_GEN4, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_GEN4_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_GEN4_MIN);
}

//Preset Search Mask Configuration (Gen5)
IDS_NV_READ_SKIP (IDSNVID_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN5, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdPcieLaneEqPresetMask32GtConfig)) {
  case IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN5_CUSTOM:
    break;
  case IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_CONFIG_GEN5_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_NBIO_PCIE_SEARCH_MASK_GEN5, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_GEN5_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_NBIO_PCIE_SEARCH_MASK_GEN5_MIN);
}

//I3C/I2C 0 Enable
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I3_C0_CONFIG, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdFchI2cI3c0)) {
  case IDSOPT_CMN_FCH_I3_C0_CONFIG_BOTHDISABLED:
    break;
  case IDSOPT_CMN_FCH_I3_C0_CONFIG_I3CENABLED:
    break;
  case IDSOPT_CMN_FCH_I3_C0_CONFIG_I2CENABLED:
    break;
  case IDSOPT_CMN_FCH_I3_C0_CONFIG_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//I3C/I2C 1 Enable
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I3_C1_CONFIG, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdFchI2cI3c1)) {
  case IDSOPT_CMN_FCH_I3_C1_CONFIG_BOTHDISABLED:
    break;
  case IDSOPT_CMN_FCH_I3_C1_CONFIG_I3CENABLED:
    break;
  case IDSOPT_CMN_FCH_I3_C1_CONFIG_I2CENABLED:
    break;
  case IDSOPT_CMN_FCH_I3_C1_CONFIG_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//I3C/I2C 2 Enable
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I3_C2_CONFIG, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdFchI2cI3c2)) {
  case IDSOPT_CMN_FCH_I3_C2_CONFIG_BOTHDISABLED:
    break;
  case IDSOPT_CMN_FCH_I3_C2_CONFIG_I3CENABLED:
    break;
  case IDSOPT_CMN_FCH_I3_C2_CONFIG_I2CENABLED:
    break;
  case IDSOPT_CMN_FCH_I3_C2_CONFIG_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//I3C/I2C 3 Enable
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I3_C3_CONFIG, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdFchI2cI3c3)) {
  case IDSOPT_CMN_FCH_I3_C3_CONFIG_BOTHDISABLED:
    break;
  case IDSOPT_CMN_FCH_I3_C3_CONFIG_I3CENABLED:
    break;
  case IDSOPT_CMN_FCH_I3_C3_CONFIG_I2CENABLED:
    break;
  case IDSOPT_CMN_FCH_I3_C3_CONFIG_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//I2C 4 Enable
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I2_C4_CONFIG, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdFchI2c4)) {
  case IDSOPT_CMN_FCH_I2_C4_CONFIG_DISABLED:
    break;
  case IDSOPT_CMN_FCH_I2_C4_CONFIG_ENABLED:
    break;
  case IDSOPT_CMN_FCH_I2_C4_CONFIG_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//I2C 5 Enable
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I2_C5_CONFIG, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdFchI2c5)) {
  case IDSOPT_CMN_FCH_I2_C5_CONFIG_DISABLED:
    break;
  case IDSOPT_CMN_FCH_I2_C5_CONFIG_ENABLED:
    break;
  case IDSOPT_CMN_FCH_I2_C5_CONFIG_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Release SPD Host Control
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_RELEASE_SPD_HOST_CONTROL, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdFchSpdHostCtrlRelease)) {
  case IDSOPT_CMN_FCH_RELEASE_SPD_HOST_CONTROL_DISABLED:
    break;
  case IDSOPT_CMN_FCH_RELEASE_SPD_HOST_CONTROL_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//PMFW Poll DDR5 Telemetry
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_PMFW_DDR5_TELEMETRY, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdFchDimmTelemetry)) {
  case IDSOPT_CMN_FCH_PMFW_DDR5_TELEMETRY_DISABLED:
    break;
  case IDSOPT_CMN_FCH_PMFW_DDR5_TELEMETRY_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Ixc Telemetry Ports Fence Control
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_IXC_TELEMETRY_PORTS_FENCE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdFchIxcTelemetryPortsFenceControl)) {
  case IDSOPT_CMN_FCH_IXC_TELEMETRY_PORTS_FENCE_DISABLED:
    break;
  case IDSOPT_CMN_FCH_IXC_TELEMETRY_PORTS_FENCE_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//I2C SDA Hold Override
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I2C_SDA_HOLD_OVERRIDE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdFchI2cSdaHoldOverride)) {
  case IDSOPT_CMN_FCH_I2C_SDA_HOLD_OVERRIDE_DISABLED:
    break;
  case IDSOPT_CMN_FCH_I2C_SDA_HOLD_OVERRIDE_ENABLED:
    break;
  case IDSOPT_CMN_FCH_I2C_SDA_HOLD_OVERRIDE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//APML SB-TSI Mode
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_APML_SBTSI_SLV_MODE, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_FCH_APML_SBTSI_SLV_MODE_I3C:
    break;
  case IDSOPT_CMN_FCH_APML_SBTSI_SLV_MODE_I2C:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//I3C Mode Speed
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I3C_MODE_SPEED, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdFchI3cSpeed)) {
  case IDSOPT_CMN_FCH_I3C_MODE_SPEED_SDR26MHZ:
    break;
  case IDSOPT_CMN_FCH_I3C_MODE_SPEED_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I3C_PP_HCNT_VALUE, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_FCH_I3C_PP_HCNT_VALUE_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_FCH_I3C_PP_HCNT_VALUE_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I3C_SDA_HOLD_VALUE, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_FCH_I3C_SDA_HOLD_VALUE_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_FCH_I3C_SDA_HOLD_VALUE_MIN);
}

//I3C SDA Hold Override
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I3C_SDA_HOLD_OVERRIDE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdFchI3cSdaHoldOverride)) {
  case IDSOPT_CMN_FCH_I3C_SDA_HOLD_OVERRIDE_DISABLED:
    break;
  case IDSOPT_CMN_FCH_I3C_SDA_HOLD_OVERRIDE_ENABLED:
    break;
  case IDSOPT_CMN_FCH_I3C_SDA_HOLD_OVERRIDE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I2C0_SDA_TX_HOLD_VALUE, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_FCH_I2C0_SDA_TX_HOLD_VALUE_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_FCH_I2C0_SDA_TX_HOLD_VALUE_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I2C1_SDA_TX_HOLD_VALUE, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_FCH_I2C1_SDA_TX_HOLD_VALUE_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_FCH_I2C1_SDA_TX_HOLD_VALUE_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I2C2_SDA_TX_HOLD_VALUE, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_FCH_I2C2_SDA_TX_HOLD_VALUE_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_FCH_I2C2_SDA_TX_HOLD_VALUE_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I2C3_SDA_TX_HOLD_VALUE, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_FCH_I2C3_SDA_TX_HOLD_VALUE_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_FCH_I2C3_SDA_TX_HOLD_VALUE_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I2C4_SDA_TX_HOLD_VALUE, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_FCH_I2C4_SDA_TX_HOLD_VALUE_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_FCH_I2C4_SDA_TX_HOLD_VALUE_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I2C5_SDA_TX_HOLD_VALUE, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_FCH_I2C5_SDA_TX_HOLD_VALUE_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_FCH_I2C5_SDA_TX_HOLD_VALUE_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I2C0_SDA_RX_HOLD_VALUE, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_FCH_I2C0_SDA_RX_HOLD_VALUE_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_FCH_I2C0_SDA_RX_HOLD_VALUE_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I2C1_SDA_RX_HOLD_VALUE, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_FCH_I2C1_SDA_RX_HOLD_VALUE_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_FCH_I2C1_SDA_RX_HOLD_VALUE_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I2C2_SDA_RX_HOLD_VALUE, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_FCH_I2C2_SDA_RX_HOLD_VALUE_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_FCH_I2C2_SDA_RX_HOLD_VALUE_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I2C3_SDA_RX_HOLD_VALUE, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_FCH_I2C3_SDA_RX_HOLD_VALUE_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_FCH_I2C3_SDA_RX_HOLD_VALUE_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I2C4_SDA_RX_HOLD_VALUE, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_FCH_I2C4_SDA_RX_HOLD_VALUE_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_FCH_I2C4_SDA_RX_HOLD_VALUE_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I2C5_SDA_RX_HOLD_VALUE, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_FCH_I2C5_SDA_RX_HOLD_VALUE_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_FCH_I2C5_SDA_RX_HOLD_VALUE_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I3C0_SDA_HOLD_VALUE, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_FCH_I3C0_SDA_HOLD_VALUE_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_FCH_I3C0_SDA_HOLD_VALUE_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I3C1_SDA_HOLD_VALUE, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_FCH_I3C1_SDA_HOLD_VALUE_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_FCH_I3C1_SDA_HOLD_VALUE_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I3C2_SDA_HOLD_VALUE, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_FCH_I3C2_SDA_HOLD_VALUE_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_FCH_I3C2_SDA_HOLD_VALUE_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_I3C3_SDA_HOLD_VALUE, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_FCH_I3C3_SDA_HOLD_VALUE_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_FCH_I3C3_SDA_HOLD_VALUE_MIN);
}

//SATA Enable
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_SATA_ENABLE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdSataEnable)) {
  case IDSOPT_CMN_FCH_SATA_ENABLE_DISABLED:
    break;
  case IDSOPT_CMN_FCH_SATA_ENABLE_ENABLED:
    break;
  case IDSOPT_CMN_FCH_SATA_ENABLE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//SATA Mode
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_SATA_CLASS, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdSataClass)) {
  case IDSOPT_CMN_FCH_SATA_CLASS_AHCI:
    break;
  case IDSOPT_CMN_FCH_SATA_CLASS_AHCIASID0X7904:
    break;
  case IDSOPT_CMN_FCH_SATA_CLASS_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//SATA RAS Support
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_SATA_RAS_SUPPORT, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdSataRasSupport)) {
  case IDSOPT_CMN_FCH_SATA_RAS_SUPPORT_DISABLED:
    break;
  case IDSOPT_CMN_FCH_SATA_RAS_SUPPORT_ENABLED:
    break;
  case IDSOPT_CMN_FCH_SATA_RAS_SUPPORT_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//SATA Staggered Spin-up
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_SATA_STAGGERED_SPINUP, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdSataStaggeredSpinup)) {
  case IDSOPT_CMN_FCH_SATA_STAGGERED_SPINUP_DISABLED:
    break;
  case IDSOPT_CMN_FCH_SATA_STAGGERED_SPINUP_ENABLED:
    break;
  case IDSOPT_CMN_FCH_SATA_STAGGERED_SPINUP_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//SATA Disabled AHCI Prefetch Function
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_SATA_AHCI_DIS_PREFETCH_FUNCTION, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdSataAhciDisPrefetchFunction)) {
  case IDSOPT_CMN_FCH_SATA_AHCI_DIS_PREFETCH_FUNCTION_DISABLED:
    break;
  case IDSOPT_CMN_FCH_SATA_AHCI_DIS_PREFETCH_FUNCTION_ENABLED:
    break;
  case IDSOPT_CMN_FCH_SATA_AHCI_DIS_PREFETCH_FUNCTION_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata0 Enable
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA0_ENABLE, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA0_ENABLE_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA0_ENABLE_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA0_ENABLE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata1 Enable
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA1_ENABLE, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA1_ENABLE_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA1_ENABLE_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA1_ENABLE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata2 Enable
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA2_ENABLE, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA2_ENABLE_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA2_ENABLE_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA2_ENABLE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata3 Enable
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA3_ENABLE, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA3_ENABLE_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA3_ENABLE_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA3_ENABLE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata4 (Socket1) Enable
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA4_ENABLE, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA4_ENABLE_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA4_ENABLE_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA4_ENABLE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata5 (Socket1) Enable
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA5_ENABLE, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA5_ENABLE_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA5_ENABLE_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA5_ENABLE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata6 (Socket1) Enable
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA6_ENABLE, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA6_ENABLE_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA6_ENABLE_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA6_ENABLE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata7 (Socket1) Enable
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA7_ENABLE, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA7_ENABLE_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA7_ENABLE_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA7_ENABLE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata0 eSATA Port0
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATAE_SATA_PORT0, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATAE_SATA_PORT0_ISATA:
    break;
  case IDSOPT_DBG_FCH_SATAE_SATA_PORT0_ESATA:
    break;
  case IDSOPT_DBG_FCH_SATAE_SATA_PORT0_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata0 eSATA Port1
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATAE_SATA_PORT1, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATAE_SATA_PORT1_ISATA:
    break;
  case IDSOPT_DBG_FCH_SATAE_SATA_PORT1_ESATA:
    break;
  case IDSOPT_DBG_FCH_SATAE_SATA_PORT1_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata0 eSATA Port2
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATAE_SATA_PORT2, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATAE_SATA_PORT2_ISATA:
    break;
  case IDSOPT_DBG_FCH_SATAE_SATA_PORT2_ESATA:
    break;
  case IDSOPT_DBG_FCH_SATAE_SATA_PORT2_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata0 eSATA Port3
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATAE_SATA_PORT3, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATAE_SATA_PORT3_ISATA:
    break;
  case IDSOPT_DBG_FCH_SATAE_SATA_PORT3_ESATA:
    break;
  case IDSOPT_DBG_FCH_SATAE_SATA_PORT3_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata0 eSATA Port4
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATAE_SATA_PORT4, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATAE_SATA_PORT4_ISATA:
    break;
  case IDSOPT_DBG_FCH_SATAE_SATA_PORT4_ESATA:
    break;
  case IDSOPT_DBG_FCH_SATAE_SATA_PORT4_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata0 eSATA Port5
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATAE_SATA_PORT5, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATAE_SATA_PORT5_ISATA:
    break;
  case IDSOPT_DBG_FCH_SATAE_SATA_PORT5_ESATA:
    break;
  case IDSOPT_DBG_FCH_SATAE_SATA_PORT5_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata0 eSATA Port6
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATAE_SATA_PORT6, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATAE_SATA_PORT6_ISATA:
    break;
  case IDSOPT_DBG_FCH_SATAE_SATA_PORT6_ESATA:
    break;
  case IDSOPT_DBG_FCH_SATAE_SATA_PORT6_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata0 eSATA Port7
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATAE_SATA_PORT7, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATAE_SATA_PORT7_ISATA:
    break;
  case IDSOPT_DBG_FCH_SATAE_SATA_PORT7_ESATA:
    break;
  case IDSOPT_DBG_FCH_SATAE_SATA_PORT7_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata1 eSATA Port0
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT0, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT0_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT0_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT0_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata1 eSATA Port1
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT1, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT1_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT1_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT1_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata1 eSATA Port2
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT2, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT2_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT2_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT2_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata1 eSATA Port3
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT3, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT3_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT3_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT3_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata1 eSATA Port4
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT4, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT4_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT4_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT4_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata1 eSATA Port5
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT5, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT5_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT5_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT5_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata1 eSATA Port6
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT6, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT6_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT6_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT6_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata1 eSATA Port7
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT7, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT7_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT7_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_ESATA_PORT7_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata2 eSATA Port0
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT0, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT0_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT0_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT0_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata2 eSATA Port1
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT1, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT1_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT1_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT1_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata2 eSATA Port2
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT2, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT2_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT2_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT2_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata2 eSATA Port3
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT3, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT3_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT3_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT3_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata2 eSATA Port4
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT4, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT4_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT4_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT4_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata2 eSATA Port5
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT5, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT5_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT5_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT5_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata2 eSATA Port6
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT6, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT6_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT6_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT6_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata2 eSATA Port7
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT7, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT7_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT7_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_ESATA_PORT7_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata3 eSATA Port0
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT0, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT0_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT0_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT0_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata3 eSATA Port1
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT1, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT1_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT1_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT1_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata3 eSATA Port2
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT2, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT2_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT2_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT2_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata3 eSATA Port3
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT3, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT3_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT3_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT3_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata3 eSATA Port4
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT4, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT4_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT4_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT4_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata3 eSATA Port5
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT5, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT5_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT5_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT5_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata3 eSATA Port6
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT6, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT6_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT6_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT6_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata3 eSATA Port7
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT7, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT7_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT7_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_ESATA_PORT7_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata4 eSATA Port0
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT0, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT0_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT0_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT0_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata4 eSATA Port1
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT1, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT1_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT1_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT1_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata4 eSATA Port2
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT2, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT2_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT2_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT2_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata4 eSATA Port3
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT3, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT3_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT3_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT3_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata4 eSATA Port4
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT4, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT4_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT4_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT4_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata4 eSATA Port5
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT5, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT5_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT5_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT5_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata4 eSATA Port6
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT6, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT6_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT6_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT6_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata4 eSATA Port7
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT7, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT7_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT7_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_ESATA_PORT7_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata5 eSATA Port0
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT0, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT0_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT0_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT0_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata5 eSATA Port1
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT1, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT1_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT1_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT1_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata5 eSATA Port2
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT2, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT2_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT2_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT2_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata5 eSATA Port3
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT3, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT3_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT3_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT3_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata5 eSATA Port4
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT4, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT4_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT4_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT4_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata5 eSATA Port5
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT5, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT5_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT5_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT5_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata5 eSATA Port6
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT6, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT6_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT6_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT6_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata5 eSATA Port7
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT7, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT7_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT7_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_ESATA_PORT7_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata6 eSATA Port0
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT0, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT0_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT0_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT0_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata6 eSATA Port1
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT1, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT1_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT1_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT1_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata6 eSATA Port2
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT2, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT2_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT2_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT2_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata6 eSATA Port3
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT3, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT3_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT3_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT3_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata6 eSATA Port4
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT4, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT4_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT4_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT4_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata6 eSATA Port5
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT5, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT5_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT5_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT5_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata6 eSATA Port6
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT6, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT6_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT6_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT6_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata6 eSATA Port7
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT7, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT7_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT7_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_ESATA_PORT7_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata7 eSATA Port0
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT0, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT0_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT0_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT0_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata7 eSATA Port1
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT1, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT1_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT1_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT1_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata7 eSATA Port2
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT2, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT2_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT2_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT2_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata7 eSATA Port3
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT3, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT3_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT3_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT3_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata7 eSATA Port4
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT4, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT4_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT4_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT4_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata7 eSATA Port5
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT5, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT5_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT5_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT5_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata7 eSATA Port6
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT6, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT6_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT6_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT6_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata7 eSATA Port7
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT7, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT7_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT7_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_ESATA_PORT7_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket0 DevSlp0 Enable
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_AGGRESIVE_DEV_SLP_P0, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdSataDevSlpPort0)) {
  case IDSOPT_DBG_FCH_SATA_AGGRESIVE_DEV_SLP_P0_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_AGGRESIVE_DEV_SLP_P0_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_AGGRESIVE_DEV_SLP_P0_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_DEV_SLP_CONTROLLER0_NUM, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DBG_FCH_SATA_DEV_SLP_CONTROLLER0_NUM_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DBG_FCH_SATA_DEV_SLP_CONTROLLER0_NUM_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_DEV_SLP_PORT0_NUM, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DBG_FCH_SATA_DEV_SLP_PORT0_NUM_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DBG_FCH_SATA_DEV_SLP_PORT0_NUM_MIN);
}

//Socket0 DevSlp1 Enable
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_AGGRESIVE_DEV_SLP_P1, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdSataDevSlpPort1)) {
  case IDSOPT_DBG_FCH_SATA_AGGRESIVE_DEV_SLP_P1_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_AGGRESIVE_DEV_SLP_P1_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_AGGRESIVE_DEV_SLP_P1_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_DEV_SLP_CONTROLLER1_NUM, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DBG_FCH_SATA_DEV_SLP_CONTROLLER1_NUM_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DBG_FCH_SATA_DEV_SLP_CONTROLLER1_NUM_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_DEV_SLP_PORT1_NUM, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DBG_FCH_SATA_DEV_SLP_PORT1_NUM_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DBG_FCH_SATA_DEV_SLP_PORT1_NUM_MIN);
}

//Socket1 DevSlp0 Enable
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP0, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP0_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP0_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP0_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP_CONTROLLER0_NUM, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP_CONTROLLER0_NUM_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP_CONTROLLER0_NUM_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP0_NUM, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP0_NUM_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP0_NUM_MIN);
}

//Socket1 DevSlp1 Enable
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP1, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP1_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP1_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP1_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP_CONTROLLER1_NUM, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP_CONTROLLER1_NUM_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP_CONTROLLER1_NUM_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE4_DEV_SLP1_NUM, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP1_NUM_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DBG_FCH_SATA_MCM_DIE4_DEV_SLP1_NUM_MIN);
}

//Sata0 SGPIO
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_SGPIO0, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_SGPIO0_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_SGPIO0_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_SGPIO0_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata1 SGPIO
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE1_SGPIO0, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_SGPIO0_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_SGPIO0_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE1_SGPIO0_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata2 SGPIO
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE2_SGPIO0, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_SGPIO0_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_SGPIO0_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE2_SGPIO0_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata3 SGPIO
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE3_SGPIO0, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_SGPIO0_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_SGPIO0_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE3_SGPIO0_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata4 SGPIO
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE4_SGPIO0, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_SGPIO0_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_SGPIO0_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE4_SGPIO0_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata5 SGPIO
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE5_SGPIO0, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_SGPIO0_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_SGPIO0_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE5_SGPIO0_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata6 SGPIO
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE6_SGPIO0, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_SGPIO0_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_SGPIO0_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE6_SGPIO0_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sata7 SGPIO
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SATA_MCM_DIE7_SGPIO0, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_SGPIO0_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_SGPIO0_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SATA_MCM_DIE7_SGPIO0_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//XHCI Controller0 enable
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_USB_XHC_I0_ENABLE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdXhci0Enable)) {
  case IDSOPT_CMN_FCH_USB_XHC_I0_ENABLE_ENABLED:
    break;
  case IDSOPT_CMN_FCH_USB_XHC_I0_ENABLE_DISABLED:
    break;
  case IDSOPT_CMN_FCH_USB_XHC_I0_ENABLE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//XHCI Controller1 enable
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_USB_XHC_I1_ENABLE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdXhci1Enable)) {
  case IDSOPT_CMN_FCH_USB_XHC_I1_ENABLE_ENABLED:
    break;
  case IDSOPT_CMN_FCH_USB_XHC_I1_ENABLE_DISABLED:
    break;
  case IDSOPT_CMN_FCH_USB_XHC_I1_ENABLE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//XHCI2 enable (Socket1)
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_USB_XHC_I2_ENABLE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdXhci2Enable)) {
  case IDSOPT_CMN_FCH_USB_XHC_I2_ENABLE_ENABLED:
    break;
  case IDSOPT_CMN_FCH_USB_XHC_I2_ENABLE_DISABLED:
    break;
  case IDSOPT_CMN_FCH_USB_XHC_I2_ENABLE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//XHCI3 enable (Socket1)
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_USB_XHC_I3_ENABLE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdXhci3Enable)) {
  case IDSOPT_CMN_FCH_USB_XHC_I3_ENABLE_ENABLED:
    break;
  case IDSOPT_CMN_FCH_USB_XHC_I3_ENABLE_DISABLED:
    break;
  case IDSOPT_CMN_FCH_USB_XHC_I3_ENABLE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Ac Loss Control
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_SYSTEM_PWR_FAIL_SHADOW, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdPwrFailShadow)) {
  case IDSOPT_CMN_FCH_SYSTEM_PWR_FAIL_SHADOW_ALWAYSOFF:
    break;
  case IDSOPT_CMN_FCH_SYSTEM_PWR_FAIL_SHADOW_ALWAYSON:
    break;
  case IDSOPT_CMN_FCH_SYSTEM_PWR_FAIL_SHADOW_RESERVED:
    break;
  case IDSOPT_CMN_FCH_SYSTEM_PWR_FAIL_SHADOW_PREVIOUS:
    break;
  case IDSOPT_CMN_FCH_SYSTEM_PWR_FAIL_SHADOW_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Set Fch Power failed Shadow in ABL
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_PWR_FAIL_SHADOW_ABL_ENABLED, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_FCH_PWR_FAIL_SHADOW_ABL_ENABLED_ENABLED:
    break;
  case IDSOPT_CMN_FCH_PWR_FAIL_SHADOW_ABL_ENABLED_DISABLED:
    break;
  case IDSOPT_CMN_FCH_PWR_FAIL_SHADOW_ABL_ENABLED_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Uart 0 Enable
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_UART0_CONFIG, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_FCH_UART0_CONFIG_DISABLED:
    break;
  case IDSOPT_CMN_FCH_UART0_CONFIG_ENABLED:
    break;
  case IDSOPT_CMN_FCH_UART0_CONFIG_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Uart 0 Legacy Options
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_UART0_LEGACY_CONFIG, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (FchUart0LegacyEnable)) {
  case IDSOPT_CMN_FCH_UART0_LEGACY_CONFIG_DISABLED:
    break;
  case IDSOPT_CMN_FCH_UART0_LEGACY_CONFIG_0X2E8:
    break;
  case IDSOPT_CMN_FCH_UART0_LEGACY_CONFIG_0X2F8:
    break;
  case IDSOPT_CMN_FCH_UART0_LEGACY_CONFIG_0X3E8:
    break;
  case IDSOPT_CMN_FCH_UART0_LEGACY_CONFIG_0X3F8:
    break;
  case IDSOPT_CMN_FCH_UART0_LEGACY_CONFIG_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Uart 1 Enable
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_UART1_CONFIG, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_FCH_UART1_CONFIG_DISABLED:
    break;
  case IDSOPT_CMN_FCH_UART1_CONFIG_ENABLED:
    break;
  case IDSOPT_CMN_FCH_UART1_CONFIG_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Uart 1 Legacy Options
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_UART1_LEGACY_CONFIG, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (FchUart1LegacyEnable)) {
  case IDSOPT_CMN_FCH_UART1_LEGACY_CONFIG_DISABLED:
    break;
  case IDSOPT_CMN_FCH_UART1_LEGACY_CONFIG_0X2E8:
    break;
  case IDSOPT_CMN_FCH_UART1_LEGACY_CONFIG_0X2F8:
    break;
  case IDSOPT_CMN_FCH_UART1_LEGACY_CONFIG_0X3E8:
    break;
  case IDSOPT_CMN_FCH_UART1_LEGACY_CONFIG_0X3F8:
    break;
  case IDSOPT_CMN_FCH_UART1_LEGACY_CONFIG_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Uart 2 Enable
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_UART2_CONFIG, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_FCH_UART2_CONFIG_DISABLED:
    break;
  case IDSOPT_CMN_FCH_UART2_CONFIG_ENABLED:
    break;
  case IDSOPT_CMN_FCH_UART2_CONFIG_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Uart 2 Legacy Options
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_UART2_LEGACY_CONFIG, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (FchUart2LegacyEnable)) {
  case IDSOPT_CMN_FCH_UART2_LEGACY_CONFIG_DISABLED:
    break;
  case IDSOPT_CMN_FCH_UART2_LEGACY_CONFIG_0X2E8:
    break;
  case IDSOPT_CMN_FCH_UART2_LEGACY_CONFIG_0X2F8:
    break;
  case IDSOPT_CMN_FCH_UART2_LEGACY_CONFIG_0X3E8:
    break;
  case IDSOPT_CMN_FCH_UART2_LEGACY_CONFIG_0X3F8:
    break;
  case IDSOPT_CMN_FCH_UART2_LEGACY_CONFIG_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//ALink RAS Support
IDS_NV_READ_SKIP (IDSNVID_CMN_FCH_ALINK_RAS_SUPPORT, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdFchAlinkRasSupport)) {
  case IDSOPT_CMN_FCH_ALINK_RAS_SUPPORT_DISABLED:
    break;
  case IDSOPT_CMN_FCH_ALINK_RAS_SUPPORT_ENABLED:
    break;
  case IDSOPT_CMN_FCH_ALINK_RAS_SUPPORT_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Reset After Sync-Flood
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SYNCFLOOD_ENABLE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdResetCpuOnSyncFlood)) {
  case IDSOPT_DBG_FCH_SYNCFLOOD_ENABLE_ENABLE:
    break;
  case IDSOPT_DBG_FCH_SYNCFLOOD_ENABLE_DISABLE:
    break;
  case IDSOPT_DBG_FCH_SYNCFLOOD_ENABLE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_DELAY_SYNCFLOOD, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_DBG_FCH_DELAY_SYNCFLOOD_MAX);
  ASSERT (IdsNvValue >= IDSOPT_DBG_FCH_DELAY_SYNCFLOOD_MIN);
}

//FCH Spread Spectrum
IDS_NV_READ_SKIP (IDSNVID_DBG_FCH_SYSTEM_SPREAD_SPECTRUM, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdSpreadSpectrum)) {
  case IDSOPT_DBG_FCH_SYSTEM_SPREAD_SPECTRUM_DISABLED:
    break;
  case IDSOPT_DBG_FCH_SYSTEM_SPREAD_SPECTRUM_ENABLED:
    break;
  case IDSOPT_DBG_FCH_SYSTEM_SPREAD_SPECTRUM_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Boot Timer Enable
IDS_NV_READ_SKIP (IDSNVID_CMN_BOOT_TIMER_ENABLE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdBootTimerEnable)) {
  case IDSOPT_CMN_BOOT_TIMER_ENABLE_DISABLED:
    break;
  case IDSOPT_CMN_BOOT_TIMER_ENABLE_ENABLED:
    break;
  case IDSOPT_CMN_BOOT_TIMER_ENABLE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket-0 P0 NTB Enable
IDS_NV_READ_SKIP (IDSNVID_CMN_S_P3_NTB_P0_P0, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdCfgNTBP0P0)) {
  case IDSOPT_CMN_S_P3_NTB_P0_P0_AUTO:
    break;
  case IDSOPT_CMN_S_P3_NTB_P0_P0_ENABLE:
    break;
  case IDSOPT_CMN_S_P3_NTB_P0_P0_DISABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_S_P3_NTB_START_LANE_P0_P0, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_S_P3_NTB_START_LANE_P0_P0_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_S_P3_NTB_START_LANE_P0_P0_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_S_P3_NTB_END_LANE_P0_P0, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_S_P3_NTB_END_LANE_P0_P0_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_S_P3_NTB_END_LANE_P0_P0_MIN);
}

//Socket-0 P0 Link Speed
IDS_NV_READ_SKIP (IDSNVID_CMN_S_P3_NTB_LINK_SPEED_P0_P0, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdCfgNTBLinkSpeedP0P0)) {
  case IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P0_AUTO:
    break;
  case IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P0_GEN1:
    break;
  case IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P0_GEN2:
    break;
  case IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P0_GEN3:
    break;
  case IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P0_GEN4:
    break;
  case IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P0_GEN5:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket-0 P0 NTB Mode
IDS_NV_READ_SKIP (IDSNVID_CMN_S_P3_NTB_MODE_P0_P0, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdCfgNTBModeP0P0)) {
  case IDSOPT_CMN_S_P3_NTB_MODE_P0_P0_AUTO:
    break;
  case IDSOPT_CMN_S_P3_NTB_MODE_P0_P0_NTBDISABLED:
    break;
  case IDSOPT_CMN_S_P3_NTB_MODE_P0_P0_NTBPRIMARY:
    break;
  case IDSOPT_CMN_S_P3_NTB_MODE_P0_P0_NTBSECONDARY:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket-0 P2 NTB Enable
IDS_NV_READ_SKIP (IDSNVID_CMN_S_P3_NTB_P0_P2, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdCfgNTBP0P2)) {
  case IDSOPT_CMN_S_P3_NTB_P0_P2_AUTO:
    break;
  case IDSOPT_CMN_S_P3_NTB_P0_P2_ENABLE:
    break;
  case IDSOPT_CMN_S_P3_NTB_P0_P2_DISABLE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_S_P3_NTB_START_LANE_P0_P2, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_S_P3_NTB_START_LANE_P0_P2_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_S_P3_NTB_START_LANE_P0_P2_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_S_P3_NTB_END_LANE_P0_P2, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_S_P3_NTB_END_LANE_P0_P2_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_S_P3_NTB_END_LANE_P0_P2_MIN);
}

//Socket-0 P2 Link Speed
IDS_NV_READ_SKIP (IDSNVID_CMN_S_P3_NTB_LINK_SPEED_P0_P2, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdCfgNTBLinkSpeedP0P2)) {
  case IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P2_AUTO:
    break;
  case IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P2_GEN1:
    break;
  case IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P2_GEN2:
    break;
  case IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P2_GEN3:
    break;
  case IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P2_GEN4:
    break;
  case IDSOPT_CMN_S_P3_NTB_LINK_SPEED_P0_P2_GEN5:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Socket-0 P2 NTB Mode
IDS_NV_READ_SKIP (IDSNVID_CMN_S_P3_NTB_MODE_P0_P2, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdCfgNTBModeP0P2)) {
  case IDSOPT_CMN_S_P3_NTB_MODE_P0_P2_AUTO:
    break;
  case IDSOPT_CMN_S_P3_NTB_MODE_P0_P2_NTBDISABLED:
    break;
  case IDSOPT_CMN_S_P3_NTB_MODE_P0_P2_NTBPRIMARY:
    break;
  case IDSOPT_CMN_S_P3_NTB_MODE_P0_P2_NTBSECONDARY:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//ABL Console Out Control
IDS_NV_READ_SKIP (IDSNVID_CMN_SOC_ABL_CON_OUT, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_SOC_ABL_CON_OUT_DISABLE:
    break;
  case IDSOPT_CMN_SOC_ABL_CON_OUT_ENABLE:
    break;
  case IDSOPT_CMN_SOC_ABL_CON_OUT_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//ABL Console Out Serial Port
IDS_NV_READ_SKIP (IDSNVID_CMN_SOC_ABL_CON_OUT_SERIAL_PORT, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_ESPIUART:
    break;
  case IDSOPT_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_SOCUART0:
    break;
  case IDSOPT_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_SOCUART1:
    break;
  case IDSOPT_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//ABL Console Out Serial Port IO
IDS_NV_READ_SKIP (IDSNVID_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_IO, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_IO_0X3F8:
    break;
  case IDSOPT_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_IO_0X2F8:
    break;
  case IDSOPT_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_IO_0X3E8:
    break;
  case IDSOPT_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_IO_0X2E8:
    break;
  case IDSOPT_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_IO_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//ABL Serial port IO customized enabled
IDS_NV_READ_SKIP (IDSNVID_CMN_SOC_ABL_SERIAL_PORT_IO_CUSTOM_ENABLED, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_SOC_ABL_SERIAL_PORT_IO_CUSTOM_ENABLED_DISABLED:
    break;
  case IDSOPT_CMN_SOC_ABL_SERIAL_PORT_IO_CUSTOM_ENABLED_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_IO_CUSTOM, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_IO_CUSTOM_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_SOC_ABL_CON_OUT_SERIAL_PORT_IO_CUSTOM_MIN);
}

//ABL Basic Console Out Control
IDS_NV_READ_SKIP (IDSNVID_CMN_SOC_ABL_CON_OUT_BASIC, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_SOC_ABL_CON_OUT_BASIC_DISABLE:
    break;
  case IDSOPT_CMN_SOC_ABL_CON_OUT_BASIC_ENABLE:
    break;
  case IDSOPT_CMN_SOC_ABL_CON_OUT_BASIC_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//ABL PMU message Control
IDS_NV_READ_SKIP (IDSNVID_CMN_SOC_ABL_PMU_MSG_CTRL, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_SOC_ABL_PMU_MSG_CTRL_DETAILEDDEBUGMESSAGE:
    break;
  case IDSOPT_CMN_SOC_ABL_PMU_MSG_CTRL_COARSEDEBUGMESSAGE:
    break;
  case IDSOPT_CMN_SOC_ABL_PMU_MSG_CTRL_STAGECOMPLETION:
    break;
  case IDSOPT_CMN_SOC_ABL_PMU_MSG_CTRL_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//ABL Memory Population message Control
IDS_NV_READ_SKIP (IDSNVID_CMN_SOC_ABL_MEM_POP_MSG_CTRL, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_SOC_ABL_MEM_POP_MSG_CTRL_WARNINGMESSAGE:
    break;
  case IDSOPT_CMN_SOC_ABL_MEM_POP_MSG_CTRL_FATALERROR:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Print Socket 1 PMU MsgBlock
IDS_NV_READ_SKIP (IDSNVID_CMN_PRINT_SOCKET1_PMU_MSG_BLOCK, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_PRINT_SOCKET1_PMU_MSG_BLOCK_DISABLED:
    break;
  case IDSOPT_CMN_PRINT_SOCKET1_PMU_MSG_BLOCK_ENABLED:
    break;
  case IDSOPT_CMN_PRINT_SOCKET1_PMU_MSG_BLOCK_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Print Socket 1 PMU Training Log
IDS_NV_READ_SKIP (IDSNVID_CMN_PRINT_SOCKET1_TRAINING_LOG, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_PRINT_SOCKET1_TRAINING_LOG_DISABLED:
    break;
  case IDSOPT_CMN_PRINT_SOCKET1_TRAINING_LOG_ENABLED:
    break;
  case IDSOPT_CMN_PRINT_SOCKET1_TRAINING_LOG_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//PSP error injection support
IDS_NV_READ_SKIP (IDSNVID_DF_CMN_PSP_ERR_INJ, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdPspEinjSupport)) {
  case IDSOPT_DF_CMN_PSP_ERR_INJ_FALSE:
    break;
  case IDSOPT_DF_CMN_PSP_ERR_INJ_TRUE:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_NUMBER_OF_SOCKETS, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_NUMBER_OF_SOCKETS_MAX);
  ASSERT (IdsNvValue >= IDSOPT_NUMBER_OF_SOCKETS_MIN);
}

//SEC_I2C Voltage Mode
IDS_NV_READ_SKIP (IDSNVID_CMN_SEC_I2C_VOLT_MODE, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_SEC_I2C_VOLT_MODE_AUTO:
    break;
  case IDSOPT_CMN_SEC_I2C_VOLT_MODE_18V:
    break;
  case IDSOPT_CMN_SEC_I2C_VOLT_MODE_11V:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//FAR enforcement state
IDS_NV_READ_SKIP (IDSNVID_CMN_SOC_FAR_ENFORCED, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_SOC_FAR_ENFORCED_DISABLED:
    break;
  case IDSOPT_CMN_SOC_FAR_ENFORCED_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

IDS_NV_READ_SKIP (IDSNVID_CMN_SOC_SPL_FUSE, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_SOC_SPL_FUSE_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_SOC_SPL_FUSE_MIN);
}

IDS_NV_READ_SKIP (IDSNVID_CMN_SOC_SPL_VALUE_IN_TBL, &IdsNvValue) {
  ASSERT (IdsNvValue <= IDSOPT_CMN_SOC_SPL_VALUE_IN_TBL_MAX);
  ASSERT (IdsNvValue >= IDSOPT_CMN_SOC_SPL_VALUE_IN_TBL_MIN);
}

//FAR Switch
IDS_NV_READ_SKIP (IDSNVID_CMN_SOC_FAR_SWITCH, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdPspAntiRollbackLateSplFuse)) {
  case IDSOPT_CMN_SOC_FAR_SWITCH_DISABLED:
    break;
  case IDSOPT_CMN_SOC_FAR_SWITCH_ENABLED:
    break;
  case IDSOPT_CMN_SOC_FAR_SWITCH_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//CXL Control
IDS_NV_READ_SKIP (IDSNVID_CMN_CXL_CONTROL, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdCxlOnAllPorts)) {
  case IDSOPT_CMN_CXL_CONTROL_AUTO:
    break;
  case IDSOPT_CMN_CXL_CONTROL_ENABLED:
    break;
  case IDSOPT_CMN_CXL_CONTROL_DISABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//CXL Physical Addressing
IDS_NV_READ_SKIP (IDSNVID_CMN_CXL_SDP_REQ_SYS_ADDR, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdCxlSpaEnable)) {
  case IDSOPT_CMN_CXL_SDP_REQ_SYS_ADDR_NORMALIZEDADDRESS:
    break;
  case IDSOPT_CMN_CXL_SDP_REQ_SYS_ADDR_SYSTEMADDRESS:
    break;
  case IDSOPT_CMN_CXL_SDP_REQ_SYS_ADDR_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//CXL Memory Attribute
IDS_NV_READ_SKIP (IDSNVID_CMN_CXL_SPM, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdCxlSpm)) {
  case IDSOPT_CMN_CXL_SPM_AUTO:
    break;
  case IDSOPT_CMN_CXL_SPM_ENABLED:
    break;
  case IDSOPT_CMN_CXL_SPM_DISABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//CXL Encryption
IDS_NV_READ_SKIP (IDSNVID_CMN_CXL_ENCRYPTION, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_CXL_ENCRYPTION_ENABLED:
    break;
  case IDSOPT_CMN_CXL_ENCRYPTION_DISABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//CXL DVSEC Lock
IDS_NV_READ_SKIP (IDSNVID_CMN_CXL_DVSEC_LOCK, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdCxlDvsecLock)) {
  case IDSOPT_CMN_CXL_DVSEC_LOCK_AUTO:
    break;
  case IDSOPT_CMN_CXL_DVSEC_LOCK_ENABLED:
    break;
  case IDSOPT_CMN_CXL_DVSEC_LOCK_DISABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//CXL HDM Decoder Lock On Commit
IDS_NV_READ_SKIP (IDSNVID_CMN_CXL_HDM_DECODER_LOCK_ON_COMMIT, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdCxlHdmDecoderLockOnCommit)) {
  case IDSOPT_CMN_CXL_HDM_DECODER_LOCK_ON_COMMIT_AUTO:
    break;
  case IDSOPT_CMN_CXL_HDM_DECODER_LOCK_ON_COMMIT_ENABLED:
    break;
  case IDSOPT_CMN_CXL_HDM_DECODER_LOCK_ON_COMMIT_DISABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Temp Gen5 Advertisement
IDS_NV_READ_SKIP (IDSNVID_CMN_CXL_TEMP_GEN5_ADVERTISEMENT, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdCxlTempGen5AdvertAltPtcl)) {
  case IDSOPT_CMN_CXL_TEMP_GEN5_ADVERTISEMENT_DISABLE:
    break;
  case IDSOPT_CMN_CXL_TEMP_GEN5_ADVERTISEMENT_ENABLE:
    break;
  case IDSOPT_CMN_CXL_TEMP_GEN5_ADVERTISEMENT_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sync Header Bypass
IDS_NV_READ_SKIP (IDSNVID_CMN_SYNC_HEADER_BY_PASS, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdSyncHeaderByPass)) {
  case IDSOPT_CMN_SYNC_HEADER_BY_PASS_AUTO:
    break;
  case IDSOPT_CMN_SYNC_HEADER_BY_PASS_ENABLED:
    break;
  case IDSOPT_CMN_SYNC_HEADER_BY_PASS_DISABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Sync Header Bypass Compatibility Mode
IDS_NV_READ_SKIP (IDSNVID_CXL_SYNC_HEADER_BYPASS_COMP_MODE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdCxlSyncHeaderByPassCompMode)) {
  case IDSOPT_CXL_SYNC_HEADER_BYPASS_COMP_MODE_AUTO:
    break;
  case IDSOPT_CXL_SYNC_HEADER_BYPASS_COMP_MODE_ENABLED:
    break;
  case IDSOPT_CXL_SYNC_HEADER_BYPASS_COMP_MODE_DISABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//CXL Memory Online/Offline
IDS_NV_READ_SKIP (IDSNVID_CMN_CXL_MEM_ONLINE_OFFLINE, &IdsNvValue) {
  switch (IdsNvValue) {
  case IDSOPT_CMN_CXL_MEM_ONLINE_OFFLINE_DISABLED:
    break;
  case IDSOPT_CMN_CXL_MEM_ONLINE_OFFLINE_ENABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//Override CXL Memory Size
IDS_NV_READ_SKIP (IDSNVID_DBG_CXL_OVERIDE_CXL_MEMORY_SIZE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdTruncateCxlMemory)) {
  case IDSOPT_DBG_CXL_OVERIDE_CXL_MEMORY_SIZE_32GB:
    break;
  case IDSOPT_DBG_CXL_OVERIDE_CXL_MEMORY_SIZE_64GB:
    break;
  case IDSOPT_DBG_CXL_OVERIDE_CXL_MEMORY_SIZE_128GB:
    break;
  case IDSOPT_DBG_CXL_OVERIDE_CXL_MEMORY_SIZE_AUTO:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//CXL Protocol Error Reporting
IDS_NV_READ_SKIP (IDSNVID_CMN_CXL_PROTOCOL_ERROR_REPORTING, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdCxlProtocolErrorReporting)) {
  case IDSOPT_CMN_CXL_PROTOCOL_ERROR_REPORTING_DISABLED:
    break;
  case IDSOPT_CMN_CXL_PROTOCOL_ERROR_REPORTING_SAMEASPCIEAER:
    break;
  case IDSOPT_CMN_CXL_PROTOCOL_ERROR_REPORTING_FORCEAERFWFIRSTIFCXLPRESENT:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//CXL Component Error Reporting
IDS_NV_READ_SKIP (IDSNVID_CMN_CXL_COMPONENT_ERROR_REPORTING, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGet8 (PcdAmdCxlComponentErrorReporting)) {
  case IDSOPT_CMN_CXL_COMPONENT_ERROR_REPORTING_ALLOWOSFIRST:
    break;
  case IDSOPT_CMN_CXL_COMPONENT_ERROR_REPORTING_FORCEFWFIRST:
    break;
  case IDSOPT_CMN_CXL_COMPONENT_ERROR_REPORTING_DEBUGFWFIRST:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//CXL Root Port Isolation
IDS_NV_READ_SKIP (IDSNVID_CMN_CXL_MEM_ISOLATION_ENABLE, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdCxlMemIsolationEnable)) {
  case IDSOPT_CMN_CXL_MEM_ISOLATION_ENABLE_AUTO:
    break;
  case IDSOPT_CMN_CXL_MEM_ISOLATION_ENABLE_ENABLED:
    break;
  case IDSOPT_CMN_CXL_MEM_ISOLATION_ENABLE_DISABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

//CXL Root Port Isolation FW Notification
IDS_NV_READ_SKIP (IDSNVID_CMN_CXL_MEM_ISOLATION_FW_NOTIFICATION, &IdsNvValue) {
  switch (IdsNvValue) {
  switch (PcdGetBool (PcdAmdCxlMemIsolationFwNotification)) {
  case IDSOPT_CMN_CXL_MEM_ISOLATION_FW_NOTIFICATION_AUTO:
    break;
  case IDSOPT_CMN_CXL_MEM_ISOLATION_FW_NOTIFICATION_ENABLED:
    break;
  case IDSOPT_CMN_CXL_MEM_ISOLATION_FW_NOTIFICATION_DISABLED:
    break;
  default:
    ASSERT (FALSE);
    break;
  }
}

