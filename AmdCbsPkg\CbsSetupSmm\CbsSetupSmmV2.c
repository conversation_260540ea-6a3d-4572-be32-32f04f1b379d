/*****************************************************************************
 *
 * Copyright (C) 2008-2025 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#include "CbsSetupSmm.h"
#include "AmdSoc.h"
#include "AmdCbsVariable.h"
#include <Library/BaseMemoryLib.h>
#include <Library/AmdPspDxeSmmBufLib.h>

EFI_HANDLE mCbsSetupSmmCommHandle;

//External decalration, the code is auto generated by AmdCbsPkg/Tools/CBSgenerate.pl
//Build\Resource<Program>\ApcbSetData<Program>.c
EFI_STATUS
UpdateCbsApcbTokens (
  VOID *CbsVariable,
  AMD_APCB_SERVICE_PROTOCOL *ApcbProtocol
  );

BOOLEAN
CpuFamilyIdentify (
  )
{
  UINT32     EAX_Reg;
  UINT64     SocFamilyID;

  EAX_Reg = 0;
  AsmCpuid (AMD_CPUID_FMF, &EAX_Reg, NULL, NULL, NULL);
  SocFamilyID = EAX_Reg & RAW_FAMILY_ID_MASK;
  if (SocFamilyID == CBS_XXX_RAW_ID) {
    return TRUE;
  }

#ifdef CBS_XXX_RAW_ID_1
  if (SocFamilyID == CBS_XXX_RAW_ID_1) {
    return TRUE;
  }
#endif //CBS_XXX_RAW_ID_1

  return FALSE;
}

/**
 * @brief SMM communication handler used to handle the SMM service request from normal world
 *        Used for updating APCB.
 *
 * @param DispatchHandle
 * @param Context
 * @param CommBuffer
 * @param CommBufferSize
 * @return EFI_STATUS
 */
EFI_STATUS
EFIAPI
CbsSetupSmmCommunicateHandler (
  IN       EFI_HANDLE  DispatchHandle,
  IN       CONST VOID  *Context,
  IN OUT   VOID    *CommBuffer,
  IN OUT   UINTN   *CommBufferSize
  )
{
  EFI_STATUS                Status;
  AMD_APCB_SERVICE_PROTOCOL *ApcbSmmProtocol;
  CBS_SMM_COMMUNICATION_CMN  *SmmCommParameterCmn;
  CBS_SMM_COMM_UPDATE_APCB  *SmmCommParameterUpdateApcb;
  UINTN                     TempCommBufferSize;
  UINT8                     *TempCommBuffer;

  //
  // If input is invalid, stop processing this SMI
  //
  if (CommBuffer == NULL || CommBufferSize == NULL) {
    return EFI_INVALID_PARAMETER;
  }

  TempCommBufferSize = *CommBufferSize;
  // too small buffer size may allow TOC/TOU attack, too big buffer size may cause stack overflow or heap overwritten
  if (TempCommBufferSize < sizeof (CBS_SMM_COMMUNICATION_CMN) || TempCommBufferSize > AMD_PSP_SMM_TMP_BUFFER_SIZE) {
    DEBUG ((EFI_D_ERROR,"CbsSetupSmmCommunicateHandler Command Buffer Size invalid!\n"));
    return EFI_INVALID_PARAMETER;
  }

  if (!SmmIsBufferOutsideSmmValid ((UINTN)CommBuffer, TempCommBufferSize)) {
    DEBUG ((EFI_D_ERROR,"SMM communication data buffer in SMRAM or overflow!\n"));
    return EFI_INVALID_PARAMETER;
  }

  // To ensure the security, an communication buffer should be allocated in the SMRAM.
  // #SMM.3.5: Boot firmware SMM module MUST copy the communication buffer to SMRAM before the check,
  // to resist TOC/TOU or DMA attacks.
  TempCommBuffer = GetAmdPspSmmRunTimeBufferAddress ();
  if (TempCommBuffer == NULL) {
    DEBUG ((EFI_D_ERROR,"TempCommBuffer Address is NULL\n"));
    return EFI_INVALID_PARAMETER;
  }

  ZeroMem ((VOID *) TempCommBuffer, AMD_PSP_SMM_TMP_BUFFER_SIZE);
  CopyMem ((VOID *) TempCommBuffer, (VOID *) CommBuffer, TempCommBufferSize);

  Status = EFI_UNSUPPORTED;
  SmmCommParameterCmn = (CBS_SMM_COMMUNICATION_CMN *) TempCommBuffer;
  DEBUG ((EFI_D_INFO, "CbsSetupSmmCommunicateHandler ID %x\n", SmmCommParameterCmn->id));

  if (SmmCommParameterCmn->id == CBS_SMM_COMM_ID_UPDATE_APCB) {
    if (TempCommBufferSize < (sizeof (CBS_SMM_COMM_UPDATE_APCB) + sizeof(CBS_CONFIG))) {
      DEBUG ((EFI_D_ERROR,"CbsSetupSmmCommunicateHandler Command Buffer Size invalid!\n"));
      Status = EFI_INVALID_PARAMETER;
    } else {
      SmmCommParameterUpdateApcb = (CBS_SMM_COMM_UPDATE_APCB *) TempCommBuffer;
      Status = gSmst->SmmLocateProtocol(&gAmdApcbSmmServiceProtocolGuid, NULL, (VOID **)&ApcbSmmProtocol);
      if (EFI_ERROR (Status)) {
        DEBUG ((EFI_D_ERROR, "gAmdApcbSmmServiceProtocolGuid Locate fail, exit\n"));
      } else {
        DEBUG ((EFI_D_INFO, "SMM UpdateCbsApcbToken\n"));
        Status = UpdateCbsApcbTokens (
                        (VOID *) &SmmCommParameterUpdateApcb->CbsVariable[0],
                        ApcbSmmProtocol
                        );
        DEBUG ((EFI_D_ERROR, "SMM UpdateCbsApcbToken : %r\n", Status));
      }
    }
  }
  // Copy the content of buffer in SMRAM back into the buffer outside of SMRAM
  CopyMem ((VOID *) CommBuffer, (VOID *) TempCommBuffer, TempCommBufferSize);

  return Status;
}


/**
  Main entry for this driver.

  @details  Allocate RT DATA used for SMM communication
            Register SMM communication handler used to update APCB, as

  @param ImageHandle     Image handle this driver.
  @param SystemTable     Pointer to SystemTable.

  @retval EFI_SUCESS     This function always complete successfully.

**/
EFI_STATUS
EFIAPI
CbsSetupSmmInit (
  IN EFI_HANDLE                   ImageHandle,
  IN EFI_SYSTEM_TABLE             *SystemTable
  )
{
  EFI_STATUS                      Status;
  VOID                            *SmmCommBuffer;

  DEBUG ((EFI_D_INFO, "CbsSetupSmmInit Enter\n"));

  if (CpuFamilyIdentify() == FALSE) {
    return EFI_SUCCESS;
  }

  //Allocate RT buffer for Smm communication before EndOfDxe Event to satisfie WSMT test
  SmmCommBuffer = NULL;
  Status = gBS->AllocatePool (EfiRuntimeServicesData, PcdGet64 (PcdCbsSmmCommunicationBufferSize), &SmmCommBuffer);

  ASSERT_EFI_ERROR(Status);
  ASSERT (SmmCommBuffer != NULL);
  if (EFI_ERROR (Status)) {
    DEBUG ((EFI_D_ERROR, "AllocateRtDataPool fail, Exit\n"));
    goto Exit;
  }
  //Save to PCD database
  PcdSet64S (PcdCbsSmmCommunicationAddress, (UINT64) (UINTN) SmmCommBuffer);

  DEBUG ((EFI_D_INFO, "Register CbsSetupSmmCommunicateHandler\n"));
  mCbsSetupSmmCommHandle = NULL;
  Status = gSmst->SmiHandlerRegister (CbsSetupSmmCommunicateHandler, &gCbsSmmCommHandleGuid, &mCbsSetupSmmCommHandle);
  ASSERT_EFI_ERROR(Status);

Exit:
  DEBUG ((EFI_D_INFO, "CbsSetupSmmInit Exit %r\n", Status));

  return Status;
}

