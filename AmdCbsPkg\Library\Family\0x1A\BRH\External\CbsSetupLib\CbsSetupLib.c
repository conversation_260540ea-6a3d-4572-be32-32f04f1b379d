/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#include <Base.h>
#include <Uefi.h>

#include <PiDxe.h>
#include <Protocol/HiiConfigRouting.h>
#include <Protocol/FormBrowser2.h>
#include <Protocol/HiiConfigAccess.h>
#include <Protocol/FabricTopologyServices2.h>
#include <Protocol/HiiDatabase.h>
#include <Protocol/HiiString.h>
#include <Guid/MdeModuleHii.h>
#include <Guid/CcxIommuFeatureData.h>
#include <Library/DebugLib.h>
#include <Library/BaseLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/UefiRuntimeServicesTableLib.h>
#include <Library/UefiBootServicesTableLib.h>
#include <Library/MemoryAllocationLib.h>
#include <Library/HiiLib.h>
#include <Library/DevicePathLib.h>
#include <Library/PrintLib.h>
#include <Library/UefiLib.h>
#include <Library/PrintLib.h>
#include <Library/UefiLib.h>
#include <Library/CcxDownCoreLib.h>

#include <Library/AmdCbsSetupLib.h>
#include <Library/HobLib.h>
#include <Protocol/AmdCbsHookProtocol.h>
#include <Guid/AmdCbsConfig.h>
#include "AmdCbsVariable.h"
#include "AmdCbsFormID.h"
#include "AmdSoc.h"

#include "Porting.h"
#include "SocLogicalId.h"
#include "ApcbCommon.h"
#include "Protocol/AmdApcbProtocol.h"
#include "Library/AmdPspBaseLibV2.h"
#include "CbsCustomCorePstates.h"
#include <Protocol/AmdNbioSmuServicesProtocol.h>
#include <Protocol/SocZen5ServicesProtocol.h>
#include <Library/CbsSmmCommLib.h>
#include <IdsNvDefBRH.h>
#include <Library/AmdPspApobLib.h>
#include <Addendum/Apcb/Inc/BRH/APOB.h>
#include <Addendum/Apcb/Inc/BRH/ApcbV3TokenUid.h>
#include <Library/BaseFabricTopologyLib.h>
#include <Library/AmdPspMboxLibV2.h>
#include <Addendum/Apcb/TurinSp5Rdimm/Include/Token.h> //AMI PORTING

EFI_STATUS
UpdateCbsApcbTokens (
  VOID *CbsVariable,
  AMD_APCB_SERVICE_PROTOCOL *ApcbProtocol
  );

UINT32
GetApcbHash (
  VOID *CbsVariable,
  AMD_APCB_SERVICE_PROTOCOL *ApcbProtocol
  );

VOID
CbsDfNpsAdjustVarValue (
 IN OUT CBS_CONFIG  *Setup_Config
 );

EFI_STATUS
CbsDfNpsCallback (
 IN  CONST EFI_HII_CONFIG_ACCESS_PROTOCOL  *This,
 IN  EFI_BROWSER_ACTION                    Action,
 IN  EFI_QUESTION_ID                       QuestionId,
 IN  UINT8                                 Type,
 IN  EFI_IFR_TYPE_VALUE                    *Value,
 OUT EFI_BROWSER_ACTION_REQUEST            *ActionRequest,
 OUT CBS_CONFIG                            *pSetup_Config,
 IN  EFI_HII_HANDLE                        HiiHandle
 );

EFI_STATUS
CbsDfCc6AllocSchmCallback (
 IN  CONST EFI_HII_CONFIG_ACCESS_PROTOCOL  *This,
 IN  EFI_BROWSER_ACTION                     Action,
 IN  EFI_QUESTION_ID                        QuestionId,
 IN  UINT8                                  Type,
 IN  EFI_IFR_TYPE_VALUE                    *Value,
 OUT EFI_BROWSER_ACTION_REQUEST            *ActionRequest,
 OUT CBS_CONFIG                            *pSetup_Config,
 IN  EFI_HII_HANDLE                         HiiHandle
 );

EFI_STATUS
CbsCpuScanDumpDbgCallback (
 OUT CBS_CONFIG *pSetup_Config
 );

extern  EFI_BOOT_SERVICES *gBS;
extern  EFI_GUID gAmdApcbDxeServiceProtocolGuid;

UINTN CbsVariableSize = sizeof(CBS_CONFIG);
DXE_AMD_NBIO_SMU_SERVICES_PROTOCOL   *mNbioSmuServices = NULL;
CCX_IOMMU_FEATURE_INFO IommuFeatureData;
APOB_SYSTEM_NPS_INFO_TYPE_STRUCT *mApobSysNps = NULL;
UINT16  mRawFamily = 0;
UINT8 mRawPkgType = 0;
UINT8 mNumerOfProcessorPresent = 0;
EFI_GUID mFormSetGuidBrh = FORMSET_ID_GUID_AMD_CBS;
UINT32 XgmiInitPreset = 0x44444444;

VOID
ApicModeOptionsChanged (
  IN     EFI_BROWSER_ACTION Action,
  IN OUT CBS_CONFIG         *pSetup_Config
 )
 {
  VOID                        *Hob;
  UINT16                      HobDataSize;
  CHAR16                      *Reason;
  EFI_INPUT_KEY               Key;

  if (Action == EFI_BROWSER_ACTION_FORM_OPEN) {
    // Get IommuFeatureData
    ZeroMem (&IommuFeatureData, sizeof(IommuFeatureData));
    Hob = GetFirstGuidHob (&gAmdCcxIommuFeatureDataGuid);
    if (Hob != NULL) {
      HobDataSize = GET_GUID_HOB_DATA_SIZE (Hob);
      ASSERT (HobDataSize >= sizeof(IommuFeatureData));
      CopyMem (&IommuFeatureData, GET_GUID_HOB_DATA (Hob), sizeof(IommuFeatureData));
    }
  }

  if ((Action == EFI_BROWSER_ACTION_FORM_OPEN) ||
      (Action == EFI_BROWSER_ACTION_CHANGED)) {
    if (pSetup_Config->CbsDbgCpuLApicMode == 2) {
      //
      // X2Apic mode is selected
      //

      Reason = NULL;
      if (pSetup_Config->CbsCmnGnbNbIOMMU == 0) {
        Reason = L"because IOMMU is disabled.";
      } else if (IommuFeatureData.Iommu_Sup == 0) {
        Reason = L"because IOMMU is disabled on boot.";
      } else if (IommuFeatureData.XT_Sup == 0) {
        Reason = L"because IOMMU has no XT Support.";
      }

      if (Reason != NULL) {
        //
        // Popup a menu to notice user
        //
        CreatePopUp (
            EFI_LIGHTGRAY | EFI_BACKGROUND_BLUE,
            &Key,
            L"Cannot support x2APIC mode",
            Reason,
            L"Will change to xAPIC mode.",
            NULL
            );
        pSetup_Config->CbsDbgCpuLApicMode = 1;
      }
    }
  }
}

VOID
CbsAdjustDefaultValue (
   IN UINT8 *IfrData
 )
{
  CBS_CONFIG *Setup_Config;

  Setup_Config = (CBS_CONFIG *) IfrData;

  // Variable value adjustment for NPS
  CbsDfNpsAdjustVarValue (Setup_Config);

}

BOOLEAN
AmdHiiCreateExtendedLabelOpCode (
    IN OUT VOID **StartOpCodeHandle,
    IN OUT VOID **EndOpCodeHandle,
    IN UINT16 Label,
    IN UINT16 LabelEnd
   )
 {
    EFI_IFR_GUID_LABEL *StartLabel;
    EFI_IFR_GUID_LABEL *EndLabel;

    *StartOpCodeHandle = HiiAllocateOpCodeHandle ();
     if (*StartOpCodeHandle == NULL) goto ErrorToCreate;

     *EndOpCodeHandle = HiiAllocateOpCodeHandle ();
      if (*EndOpCodeHandle == NULL) goto ErrorToCreate;

      //
      // Create Hii Extend Label OpCode as the start opcode
      //
      StartLabel = (EFI_IFR_GUID_LABEL *) HiiCreateGuidOpCode (*StartOpCodeHandle, &gEfiIfrTianoGuid, NULL, sizeof (EFI_IFR_GUID_LABEL));
      if (StartLabel == NULL) goto ErrorToCreate;
      StartLabel->ExtendOpCode = EFI_IFR_EXTEND_OP_LABEL;
      StartLabel->Number = Label;

      //
      // Create Hii Extend Label OpCode as the end opcode
      //
      EndLabel = (EFI_IFR_GUID_LABEL *) HiiCreateGuidOpCode (*EndOpCodeHandle, &gEfiIfrTianoGuid, NULL, sizeof (EFI_IFR_GUID_LABEL));
      if (EndLabel == NULL) goto ErrorToCreate;
      EndLabel->ExtendOpCode = EFI_IFR_EXTEND_OP_LABEL;
      EndLabel->Number = LabelEnd;

      return TRUE;

 ErrorToCreate:

     if (*StartOpCodeHandle != NULL) HiiFreeOpCodeHandle (*StartOpCodeHandle);
     if (*EndOpCodeHandle != NULL) HiiFreeOpCodeHandle (*EndOpCodeHandle);

     return FALSE;
  }

EFI_STATUS
CustomFchSataClass (
  IN  CONST EFI_HII_CONFIG_ACCESS_PROTOCOL   *This,
  IN  EFI_BROWSER_ACTION                     Action,
  IN  EFI_QUESTION_ID                        QuestionId,
  IN  UINT8                                  Type,
  IN  EFI_IFR_TYPE_VALUE                     *Value,
  OUT EFI_BROWSER_ACTION_REQUEST             *ActionRequest,
  OUT CBS_CONFIG                             *pSetup_Config,
  IN  EFI_HII_HANDLE                         HiiHandle
  )
{
  if (pSetup_Config->CbsCmnFchSataEnable == 0xf) {
    pSetup_Config->CbsCmnFchSataClass = 0x2;
  }

  return EFI_SUCCESS;
}

/**
 *
 *  @brief  RetrieveDowncoreCcxCoreCount
 *
 *  @details
 *    This routine retrieves down-cored core count
 *    from GetOpnCorePresenceEx
 *
 *  @param      None
 *
 *  @retval     Number of cores on the CCX on the highest-numbered
 *              physical CCD
 */
UINT32
RetrieveDowncoreCcxCoreCount ()
{
  AMD_SOC_ZEN5_SERVICES_PROTOCOL          *SocServices;
  UINT32                                  CcdPresentFuse;
  UINT32                                  CcdDownFuse;
  UINT32                                  CoreCountByFuse;
  UINT32                                  CoreDisFuse[MAX_CCDS_PER_IOD];
  UINT32                                  CoreDisFuseSize;
  BOOLEAN                                 SmtEnabledByFuse[MAX_CCDS_PER_IOD];
  UINT32                                  SmtEnabledByFuseSize;
  UINT32                                  CoreCount;
  UINT32                                  MaxCoreCount;
  UINT32                                  i;

  if (gBS->LocateProtocol (&gAmdSocZen5ServicesProtocolGuid, NULL, (VOID **) &SocServices) != EFI_SUCCESS) {
    ASSERT (FALSE);
    return CCX_MAX_CORES_PER_COMPLEX;
  }

  CoreDisFuseSize = sizeof (CoreDisFuse);
  SmtEnabledByFuseSize = sizeof (SmtEnabledByFuse);
  if (SocServices->GetOpnCorePresenceEx (
                    SocServices,
                    0, //Get from the first die
                    &CcdPresentFuse,
                    &CcdDownFuse,
                    &CoreDisFuse[0],
                    &CoreDisFuseSize,
                    &CoreCountByFuse,
                    &SmtEnabledByFuse[0],
                    &SmtEnabledByFuseSize
                    ) != EFI_SUCCESS) {
    ASSERT (FALSE);
    return CCX_MAX_CORES_PER_COMPLEX;
  }

  CcdPresentFuse &= (~CcdDownFuse);

  MaxCoreCount = 0;
  for (i = 0; i < MAX_CCDS_PER_IOD; i++) {
    if (CcdPresentFuse & (1 << i)) {
      CoreCount = CoreCountByFuse - BitFieldCountOnes32 (CoreDisFuse[i], 0, 31);
      if (CoreCount > MaxCoreCount) {
        MaxCoreCount = CoreCount;
      }
    }
  }
  ASSERT (MaxCoreCount > 0);
  return MaxCoreCount;
}

/**
 *
 *  @brief  RetrieveSocketCount
 *
 *  @details
 *    This routine retrieves socket count present
 *
 *  @param      None
 *
 *  @retval     Number of sockets present on system
 */
UINTN
RetrieveSocketCount ()
{
  AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL *FabricTopology;
  UINTN                                  SocketCount;

  SocketCount = CCX_MAX_SOCKETS;

  if (gBS->LocateProtocol (&gAmdFabricTopologyServices2ProtocolGuid, NULL, (VOID**) &FabricTopology) != EFI_SUCCESS) {
    ASSERT (FALSE);
    return SocketCount;
  }

  if (FabricTopology->GetSystemInfo (FabricTopology, &SocketCount, NULL, NULL, NULL, NULL) != EFI_SUCCESS) {
    ASSERT (FALSE);
    return SocketCount;
  }

  return SocketCount;
}

/**
 *
 *  @brief  CbsCpuCcdCtrlUpdate
 *
 *  @details
 *    This routine dynamically updates "CCD Control" IFR oneof element
 *    based on the number of CCD on the socket
 *
 *  @param[in] HiiHandle            Handle to HII IFR package containing
 *                                  a form with "Core Control" IFR oneof
 *
 *  @retval    None
 */
VOID
CbsCpuCcdCtrlUpdate (
  IN EFI_HII_HANDLE                         HiiHandle
  )
{
  VOID                           *StartOpCodeHandle = NULL;
  VOID                           *EndOpCodeHandle = NULL;
  VOID                           *OptionOpCodeHandle = NULL;
  UINTN                          CcdCount;
  UINTN                          OptionCount;
  UINT32                         CcdMap;
  UINT32                         CoreDisMap;
  BOOLEAN                        SmtEnable;
  UINTN                          Index;
  EFI_IFR_OP_HEADER              RawOp;
  AMD_SOC_ZEN5_SERVICES_PROTOCOL *SocServices = NULL;
  STATIC struct {
      UINT16          StringId;
      UINT8           Flags;
      UINT8           Value;
  } OptionSettings[] = {
    {STRING_TOKEN (AMD_CBS_STR_AUTO),      (EFI_IFR_OPTION_DEFAULT |
                                            EFI_IFR_OPTION_DEFAULT_MFG),    CCD_MODE_AUTO},
    {STRING_TOKEN (AMD_CBS_STR_2_CCDS),     0,                              CCD_MODE_2_CCDS},
    {STRING_TOKEN (AMD_CBS_STR_4_CCDS),     0,                              CCD_MODE_4_CCDS},
    {STRING_TOKEN (AMD_CBS_STR_6_CCDS),     0,                              CCD_MODE_6_CCDS},
    {STRING_TOKEN (AMD_CBS_STR_8_CCDS),     0,                              CCD_MODE_8_CCDS},
    {STRING_TOKEN (AMD_CBS_STR_10_CCDS),    0,                              CCD_MODE_10_CCDS},
    {STRING_TOKEN (AMD_CBS_STR_12_CCDS),    0,                              CCD_MODE_12_CCDS},
    {STRING_TOKEN (AMD_CBS_STR_14_CCDS),    0,                              CCD_MODE_14_CCDS} };

  if (AmdHiiCreateExtendedLabelOpCode (&StartOpCodeHandle,
                                       &EndOpCodeHandle,
                                       LABEL_CBS_CBS_CPU_CCD_CTRL_START,
                                       LABEL_CBS_CBS_CPU_CCD_CTRL_END)) {

    OptionOpCodeHandle = HiiAllocateOpCodeHandle ();
    if (OptionOpCodeHandle != NULL) {
      CcdCount = MAX_CCDS_PER_IOD;
      if (gBS->LocateProtocol (&gAmdSocZen5ServicesProtocolGuid, NULL, (VOID **) &SocServices) == EFI_SUCCESS) {
        if (SocServices->GetOpnCorePresence (SocServices, &CcdMap, &CoreDisMap, NULL, &SmtEnable) == EFI_SUCCESS) {
          if (CcdMap != 0) {
            CcdCount = 0;
            while (CcdMap != 0) {
              if ((CcdMap & 1) != 0) {
                CcdCount++;
              }
              CcdMap >>= 1;
            }
          }
        }
      }

      if (CcdCount <= MAX_CCDS_PER_IOD) {
        if (CcdCount < 2) {
          OptionCount = 1;
        } else {
          OptionCount = CcdCount / 2;
        }
        for (Index = 0; Index < OptionCount; Index++) {
          HiiCreateOneOfOptionOpCode (OptionOpCodeHandle,
                                      OptionSettings[Index].StringId,
                                      OptionSettings[Index].Flags,
                                      EFI_IFR_NUMERIC_SIZE_1,
                                      OptionSettings[Index].Value);
        }

        // Gray out the option if only one option
        RawOp.OpCode = EFI_IFR_GRAY_OUT_IF_OP;
        RawOp.Length = sizeof (RawOp);
        RawOp.Scope = 1;
        HiiCreateRawOpCodes (StartOpCodeHandle, (UINT8 *) &RawOp, sizeof (RawOp));

        RawOp.OpCode = (OptionCount < 2) ? EFI_IFR_TRUE_OP : EFI_IFR_FALSE_OP;
        RawOp.Length = sizeof (RawOp);
        RawOp.Scope = 0;
        HiiCreateRawOpCodes (StartOpCodeHandle, (UINT8 *) &RawOp, sizeof (RawOp));

        HiiCreateOneOfOpCode (StartOpCodeHandle,
                              (EFI_QUESTION_ID) KEY_CBS_CPU_CCD_CTRL,
                              CBS_CONFIGURATION_VARSTORE_ID,
                              (UINT16) OFFSET_OF (CBS_CONFIG, CbsCpuCcdCtrl),
                              STRING_TOKEN (AMD_CBS_STR_CCD_CONTROL),
                              STRING_TOKEN (AMD_CBS_STR_CCD_CONTROL_HELP),
                              (UINT8) (EFI_IFR_FLAG_RESET_REQUIRED | EFI_IFR_FLAG_CALLBACK),
                              (UINT8) EFI_IFR_NUMERIC_SIZE_1,
                              OptionOpCodeHandle,
                              NULL);

        HiiCreateEndOpCode (StartOpCodeHandle);

        HiiUpdateForm (HiiHandle,
                       &mFormSetGuidBrh,
                       SETUP_CCD_CORE_THREAD_ENABLEMENT_LABEL,
                       StartOpCodeHandle,
                       EndOpCodeHandle);
      }

      HiiFreeOpCodeHandle (OptionOpCodeHandle);
    }

    HiiFreeOpCodeHandle (StartOpCodeHandle);
    HiiFreeOpCodeHandle (EndOpCodeHandle);
  }
}

/**
 *
 *  @brief  CbsCpuCoreCtrlUpdate
 *
 *  @details
 *    This routine dynamically updates "Core Control" IFR oneof element
 *    based on the number of cores on the CCX on the highest-numbered
 *    physical CCD with possible asymmetric downcoring
 *
 *  @param[in] HiiHandle            Handle to HII IFR package containing
 *                                  a form with "Core Control" IFR oneof
 *
 *  @retval    None
 */
VOID
CbsCpuCoreCtrlUpdate (
  IN EFI_HII_HANDLE                         HiiHandle
  )
{
  VOID*  StartOpCodeHandle, *EndOpCodeHandle, *OptionOpCodeHandle;
  UINTN  CoreCount, Index;
  STATIC struct {
      UINT16          StringId;
      UINT8           Flags;
      UINT8           Value;
  } OptionSettings[] = {
    {STRING_TOKEN (AMD_CBS_STR_AUTO),          (EFI_IFR_OPTION_DEFAULT |
                                                EFI_IFR_OPTION_DEFAULT_MFG),   CCX_DOWN_CORE_AUTO},
    {STRING_TOKEN (AMD_CBS_STR_ONE_1_0),       0,                              CCX_DOWN_CORE_1_0},
    {STRING_TOKEN (AMD_CBS_STR_TWO_2_0),       0,                              CCX_DOWN_CORE_2_0},
    {STRING_TOKEN (AMD_CBS_STR_THREE_3_0),     0,                              CCX_DOWN_CORE_3_0},
    {STRING_TOKEN (AMD_CBS_STR_FOUR_4_0),      0,                              CCX_DOWN_CORE_4_0},
    {STRING_TOKEN (AMD_CBS_STR_FIVE_5_0),      0,                              CCX_DOWN_CORE_5_0},
    {STRING_TOKEN (AMD_CBS_STR_SIX_6_0),       0,                              CCX_DOWN_CORE_6_0},
    {STRING_TOKEN (AMD_CBS_STR_SEVEN_7_0),     0,                              CCX_DOWN_CORE_7_0},
    {STRING_TOKEN (AMD_CBS_STR_EIGHT_8_0),     0,                              CCX_DOWN_CORE_8_0},
    {STRING_TOKEN (AMD_CBS_STR_NINE_9_0),      0,                              CCX_DOWN_CORE_9_0},
    {STRING_TOKEN (AMD_CBS_STR_TEN_10_0),      0,                              CCX_DOWN_CORE_10_0},
    {STRING_TOKEN (AMD_CBS_STR_ELEVEN_11_0),   0,                              CCX_DOWN_CORE_11_0},
    {STRING_TOKEN (AMD_CBS_STR_TWELVE_12_0),   0,                              CCX_DOWN_CORE_12_0},
    {STRING_TOKEN (AMD_CBS_STR_THIRTEEN_13_0), 0,                              CCX_DOWN_CORE_13_0},
    {STRING_TOKEN (AMD_CBS_STR_FOURTEEN_14_0), 0,                              CCX_DOWN_CORE_14_0},
    {STRING_TOKEN (AMD_CBS_STR_FIFTEEN_15_0),  0,                              CCX_DOWN_CORE_15_0} };

  if (AmdHiiCreateExtendedLabelOpCode (&StartOpCodeHandle,
                                       &EndOpCodeHandle,
                                       LABEL_CBS_CBS_CPU_CORE_CTRL_START,
                                       LABEL_CBS_CBS_CPU_CORE_CTRL_END)) {

    OptionOpCodeHandle = HiiAllocateOpCodeHandle ();
    if (OptionOpCodeHandle != NULL) {
      CoreCount = RetrieveDowncoreCcxCoreCount ();

      if (CoreCount <= ARRAY_SIZE (OptionSettings)) {

        for (Index = 0; Index < CoreCount; Index++) {
          HiiCreateOneOfOptionOpCode (OptionOpCodeHandle,
                                      OptionSettings[Index].StringId,
                                      OptionSettings[Index].Flags,
                                      EFI_IFR_NUMERIC_SIZE_1,
                                      OptionSettings[Index].Value);
        }

        HiiCreateOneOfOpCode (StartOpCodeHandle,
                              (EFI_QUESTION_ID) KEY_CBS_CPU_CORE_CTRL,
                              CBS_CONFIGURATION_VARSTORE_ID,
                              (UINT16) OFFSET_OF (CBS_CONFIG, CbsCpuCoreCtrl),
                              STRING_TOKEN (AMD_CBS_STR_CORE_CONTROL),
                              STRING_TOKEN (AMD_CBS_STR_CORE_CONTROL_HELP),
                              (UINT8) (EFI_IFR_FLAG_RESET_REQUIRED | EFI_IFR_FLAG_CALLBACK),
                              (UINT8) EFI_IFR_NUMERIC_SIZE_1,
                              OptionOpCodeHandle,
                              NULL);
        HiiUpdateForm (HiiHandle,
                       &mFormSetGuidBrh,
                       SETUP_CCD_CORE_THREAD_ENABLEMENT_LABEL,
                       StartOpCodeHandle,
                       EndOpCodeHandle);
      }

      HiiFreeOpCodeHandle(OptionOpCodeHandle);
    }

    HiiFreeOpCodeHandle (StartOpCodeHandle);
    HiiFreeOpCodeHandle (EndOpCodeHandle);
  }
}

/**
 *
 *  @brief  CbsCpuSmtCtrlUpdate
 *
 *  @details
 *    This routine dynamically gray out "SMT Control" IFR oneof element
 *    based on the CPU supports SMT or not
 *
 *  @param[in] HiiHandle            Handle to HII IFR package containing
 *                                  a form with "SMT Control" IFR oneof
 *
 *  @retval    None
 */
VOID
CbsCpuSmtCtrlUpdate (
  IN EFI_HII_HANDLE HiiHandle
  )
{
  AMD_SOC_ZEN5_SERVICES_PROTOCOL  *SocZen5Services;
  UINT32                          CcdPresentFuse;
  UINT32                          CcdDownFuse;
  UINT32                          CoreCountByFuse;
  UINT32                          CoreDisFuse[MAX_CCDS_PER_IOD];
  UINT32                          CoreDisFuseSize;
  BOOLEAN                         SmtEnabledByFuse[MAX_CCDS_PER_IOD];
  UINT32                          SmtEnabledByFuseSize;
  UINT32                          Index;
  BOOLEAN                         ShowSmtCtrl;
  VOID                            *StartOpCodeHandle = NULL;
  VOID                            *EndOpCodeHandle = NULL;
  VOID                            *OptionOpCodeHandle = NULL;
  EFI_IFR_OP_HEADER               RawOp;
  STATIC struct {
      UINT16          StringId;
      UINT8           Flags;
      UINT8           Value;
  } OptionSettings[] = {
    {STRING_TOKEN (AMD_CBS_STR_DISABLE),                                                     0, SMT_CONTROL_DISABLE},
    {STRING_TOKEN (AMD_CBS_STR_ENABLE),                                                      0, SMT_CONTROL_ENABLE},
    {STRING_TOKEN (AMD_CBS_STR_AUTO),    (EFI_IFR_OPTION_DEFAULT | EFI_IFR_OPTION_DEFAULT_MFG), SMT_CONTROL_AUTO}};

  // Get OPN information
  if (gBS->LocateProtocol (&gAmdSocZen5ServicesProtocolGuid, NULL, (VOID **) &SocZen5Services) != EFI_SUCCESS) {
    ASSERT (FALSE);
    return;
  }
  CoreDisFuseSize = sizeof (CoreDisFuse);
  SmtEnabledByFuseSize = sizeof (SmtEnabledByFuse);
  if (SocZen5Services->GetOpnCorePresenceEx (
                        SocZen5Services,
                        0,
                        &CcdPresentFuse,
                        &CcdDownFuse,
                        &CoreDisFuse[0],
                        &CoreDisFuseSize,
                        &CoreCountByFuse,
                        &SmtEnabledByFuse[0],
                        &SmtEnabledByFuseSize
                        ) != EFI_SUCCESS) {
    ASSERT (FALSE);
    return;
  }

  ShowSmtCtrl = FALSE;
  for (Index = 0; Index < ARRAY_SIZE (SmtEnabledByFuse); Index++) {
    if (((1 << Index) & CcdPresentFuse) && (((1 << Index) & CcdDownFuse) == 0)) {
      if (SmtEnabledByFuse[Index]) {
        ShowSmtCtrl = TRUE;
        break;
      }
    }
  }

  if (AmdHiiCreateExtendedLabelOpCode (
        &StartOpCodeHandle,
        &EndOpCodeHandle,
        LABEL_CBS_CBS_CPU_SMT_CTRL_START,
        LABEL_CBS_CBS_CPU_SMT_CTRL_END
        )
  ) {
    OptionOpCodeHandle = HiiAllocateOpCodeHandle ();
    if (OptionOpCodeHandle != NULL) {
      for (Index = 0; Index < ARRAY_SIZE (OptionSettings); Index++) {
        HiiCreateOneOfOptionOpCode (
          OptionOpCodeHandle,
          OptionSettings[Index].StringId,
          OptionSettings[Index].Flags,
          EFI_IFR_NUMERIC_SIZE_1,
          OptionSettings[Index].Value
          );
      }

      // Gray out the option if only one option
      RawOp.OpCode = EFI_IFR_GRAY_OUT_IF_OP;
      RawOp.Length = sizeof (RawOp);
      RawOp.Scope = 1;
      HiiCreateRawOpCodes (StartOpCodeHandle, (UINT8 *) &RawOp, sizeof (RawOp));

      RawOp.OpCode = ShowSmtCtrl ? EFI_IFR_FALSE_OP : EFI_IFR_TRUE_OP;
      RawOp.Length = sizeof (RawOp);
      RawOp.Scope = 0;
      HiiCreateRawOpCodes (StartOpCodeHandle, (UINT8 *) &RawOp, sizeof (RawOp));

      HiiCreateOneOfOpCode (
        StartOpCodeHandle,
        (EFI_QUESTION_ID) KEY_CBS_CPU_SMT_CTRL,
        CBS_CONFIGURATION_VARSTORE_ID,
        (UINT16) OFFSET_OF (CBS_CONFIG, CbsCpuSmtCtrl),
        STRING_TOKEN (AMD_CBS_STR_SMT_CONTROL),
        STRING_TOKEN (AMD_CBS_STR_SMT_CONTROL_HELP),
        (UINT8) (EFI_IFR_FLAG_RESET_REQUIRED | EFI_IFR_FLAG_CALLBACK),
        (UINT8) EFI_IFR_NUMERIC_SIZE_1,
        OptionOpCodeHandle,
        NULL
        );

      HiiCreateEndOpCode (StartOpCodeHandle);

      HiiUpdateForm (
        HiiHandle,
        &mFormSetGuidBrh,
        SETUP_PERFORMANCE_LABEL,
        StartOpCodeHandle,
        EndOpCodeHandle
        );

      HiiFreeOpCodeHandle(OptionOpCodeHandle);
    }

    HiiFreeOpCodeHandle (StartOpCodeHandle);
    HiiFreeOpCodeHandle (EndOpCodeHandle);
  }
}

/**
 *
 *  @brief  CbsDfNumOfSegsUpdate
 *
 *  @details
 *    This routine dynamically updates "DF Common Options / Number of PCI Segments" IFR oneof element
 *    based on the number of sockets on the system
 *
 *  @param[in] HiiHandle            Handle to HII IFR package containing
 *                                  a form with "Core Control" IFR oneof
 *
 *  @retval    None
 */
VOID
CbsDfNumOfSegsUpdate (
  IN EFI_HII_HANDLE                         HiiHandle
  )
{
  VOID*  StartOpCodeHandle, *EndOpCodeHandle, *OptionOpCodeHandle;
  UINTN  SocketCount, Index, OptionCount;
  STATIC struct {
      UINT16          StringId;
      UINT8           Flags;
      UINT32          Value;
  } OptionSettings[] = {
    {STRING_TOKEN (AMD_CBS_STR_AUTO),          (EFI_IFR_OPTION_DEFAULT |
                                                EFI_IFR_OPTION_DEFAULT_MFG),     (UINT32)IDSOPT_DF_DBG_NUM_PCI_SEGMENTS_AUTO},
    {STRING_TOKEN (AMD_CBS_STR_1_SEGMENT),       0,                              (UINT32)IDSOPT_DF_DBG_NUM_PCI_SEGMENTS_1SEGMENT},
    {STRING_TOKEN (AMD_CBS_STR_2_SEGMENTS),      0,                              (UINT32)IDSOPT_DF_DBG_NUM_PCI_SEGMENTS_2SEGMENTS},
    {STRING_TOKEN (AMD_CBS_STR_4_SEGMENTS),      0,                              (UINT32)IDSOPT_DF_DBG_NUM_PCI_SEGMENTS_4SEGMENTS},
  };

  SocketCount = RetrieveSocketCount ();
  //If SocketCount is 1 do not create 4 Segment option else do nothing
  //if (SocketCount == 1) { // AMI PORTING
  if ((SocketCount == 1)||(!PCIE_BASE_ADDRESS_OVER_4GB)) { // AMI PORTING
    OptionCount = 3;
    if(!PCIE_BASE_ADDRESS_OVER_4GB) OptionCount = 1; // AMI PORTING
    if (AmdHiiCreateExtendedLabelOpCode (&StartOpCodeHandle,
                                         &EndOpCodeHandle,
                                         LABEL_CBS_CBS_DF_DBG_NUM_PCI_SEGMENTS_START,
                                         LABEL_CBS_CBS_DF_DBG_NUM_PCI_SEGMENTS_END)) {
      OptionOpCodeHandle = HiiAllocateOpCodeHandle ();
      if (OptionOpCodeHandle != NULL) {
        for (Index = 0; Index < OptionCount; Index++) {
          HiiCreateOneOfOptionOpCode (OptionOpCodeHandle,
                                      OptionSettings[Index].StringId,
                                      OptionSettings[Index].Flags,
                                      EFI_IFR_NUMERIC_SIZE_4,
                                      OptionSettings[Index].Value);
        }

        HiiCreateOneOfOpCode (StartOpCodeHandle,
                              (EFI_QUESTION_ID) KEY_CBS_DF_DBG_NUM_PCI_SEGMENTS,
                              CBS_CONFIGURATION_VARSTORE_ID,
                              (UINT16) OFFSET_OF (CBS_CONFIG, CbsDfDbgNumPciSegments),
                              STRING_TOKEN (AMD_CBS_STR_NUMBER_OF_PCI_SEGMENTS),
                              STRING_TOKEN (AMD_CBS_STR_NUMBER_OF_PCI_SEGMENTS_HELP),
                              (UINT8) (EFI_IFR_FLAG_RESET_REQUIRED | EFI_IFR_FLAG_CALLBACK),
                              (UINT8) EFI_IFR_NUMERIC_SIZE_4,
                              OptionOpCodeHandle,
                              NULL);
        HiiUpdateForm (HiiHandle,
                       &mFormSetGuidBrh,
                       SETUP_DF_COMMON_OPTIONS_LABEL,
                       StartOpCodeHandle,
                       EndOpCodeHandle);
      }

      HiiFreeOpCodeHandle(OptionOpCodeHandle);
    }

    HiiFreeOpCodeHandle (StartOpCodeHandle);
    HiiFreeOpCodeHandle (EndOpCodeHandle);
  }
}

EFI_STATUS
ThreadNumberControlCallback (
  IN  CONST EFI_HII_CONFIG_ACCESS_PROTOCOL   *This,
  IN  EFI_BROWSER_ACTION                     Action,
  IN  EFI_QUESTION_ID                        QuestionId,
  IN  UINT8                                  Type,
  IN  EFI_IFR_TYPE_VALUE                     *Value,
  OUT EFI_BROWSER_ACTION_REQUEST             *ActionRequest,
  OUT CBS_CONFIG                             *pSetup_Config,
  IN  EFI_HII_HANDLE                         HiiHandle
  )
{
  // The minimum thread count should satisfy two conditions:
  //   8 threads minimum
  //   At least 1 thread per active CCX
  // Since we will not have active CCX without any thread,
  // this code is only to check the thread number should be >= 8

#define MINIMUM_REQUIRED_THREAD_NUMBER 8

  UINTN                           TargetThreadNum;
  UINTN                           TargetThreadNumPerCore;
  UINTN                           TargetCcdNum;
  UINTN                           TempCcdNum;
  UINT32                          TargetCoreNum;
  UINT32                          MaxCoreMask;
  EFI_INPUT_KEY                   Key;
  CHAR16                          MsgString1Base[] = L"Please ensure the thread number will not be less than %d";
  CHAR16                          MsgString1[70];
  CHAR16                          *MsgString2;
  CHAR16                          MsgString2Option[] = L"You need to check SMT Control, CCD Control, and Core Control";
  CHAR16                          MsgString2Bitmap[] = L"You need to check SMT Control and CCD * DownCore Bitmap";
  UINT8                           CbsCpuCcdCtrl;
  UINT8                           CbsCpuCoreCtrl;
  UINT8                           CbsCpuSmtCtrl;
  UINT8                           CbsCmnCpuDowncoreMode;
  UINT32                          CbsCmnCpuCcdDowncoreBitMap[MAX_CCDS_PER_IOD];
  AMD_SOC_ZEN5_SERVICES_PROTOCOL  *SocZen5Services;
  UINT32                          CcdPresentFuse;
  UINT32                          CcdDownFuse;
  UINT32                          CoreCountByFuse;
  UINT32                          CoreDisFuse[MAX_CCDS_PER_IOD];
  UINT32                          CoreDisFuseSize;
  BOOLEAN                         SmtEnabledByFuse[MAX_CCDS_PER_IOD];
  UINT32                          SmtEnabledByFuseSize;
  UINTN                           Index;
  UINT32                          CcdIndex;
  UINT32                          CoreIndex;
  UINT32                          CoreDisableMap;
  UINTN                           CoreCount;
  UINTN                           TotalCoreCount;

  // Get OPN information
  if (gBS->LocateProtocol (&gAmdSocZen5ServicesProtocolGuid, NULL, (VOID **) &SocZen5Services) != EFI_SUCCESS) {
    ASSERT (FALSE);
    return EFI_SUCCESS;
  }
  CoreDisFuseSize = sizeof (CoreDisFuse);
  SmtEnabledByFuseSize = sizeof (SmtEnabledByFuse);
  if (SocZen5Services->GetOpnCorePresenceEx (
                        SocZen5Services,
                        0,
                        &CcdPresentFuse,
                        &CcdDownFuse,
                        &CoreDisFuse[0],
                        &CoreDisFuseSize,
                        &CoreCountByFuse,
                        &SmtEnabledByFuse[0],
                        &SmtEnabledByFuseSize
                        ) != EFI_SUCCESS) {
    ASSERT (FALSE);
    return EFI_SUCCESS;
  }
  MaxCoreMask = (((UINT32) 1) << CoreCountByFuse) - 1;
  DEBUG ((DEBUG_INFO, "Max Core Count = %d\n", CoreCountByFuse));
  DEBUG ((DEBUG_INFO, "MaxCoreMask = %x\n", MaxCoreMask));

  // Get the settings from CBS buffer
  CbsCpuCcdCtrl = pSetup_Config->CbsCpuCcdCtrl;
  CbsCpuCoreCtrl = pSetup_Config->CbsCpuCoreCtrl;
  CbsCpuSmtCtrl = pSetup_Config->CbsCpuSmtCtrl;
  CbsCmnCpuDowncoreMode = pSetup_Config->CbsCmnCpuDowncoreMode;
  CbsCmnCpuCcdDowncoreBitMap[0]= pSetup_Config->CbsCmnCpuCcd0DowncoreBitMap;
  CbsCmnCpuCcdDowncoreBitMap[1]= pSetup_Config->CbsCmnCpuCcd1DowncoreBitMap;
  CbsCmnCpuCcdDowncoreBitMap[2]= pSetup_Config->CbsCmnCpuCcd2DowncoreBitMap;
  CbsCmnCpuCcdDowncoreBitMap[3]= pSetup_Config->CbsCmnCpuCcd3DowncoreBitMap;
  CbsCmnCpuCcdDowncoreBitMap[4]= pSetup_Config->CbsCmnCpuCcd4DowncoreBitMap;
  CbsCmnCpuCcdDowncoreBitMap[5]= pSetup_Config->CbsCmnCpuCcd5DowncoreBitMap;
  CbsCmnCpuCcdDowncoreBitMap[6]= pSetup_Config->CbsCmnCpuCcd6DowncoreBitMap;
  CbsCmnCpuCcdDowncoreBitMap[7]= pSetup_Config->CbsCmnCpuCcd7DowncoreBitMap;
  CbsCmnCpuCcdDowncoreBitMap[8]= pSetup_Config->CbsCmnCpuCcd8DowncoreBitMap;
  CbsCmnCpuCcdDowncoreBitMap[9]= pSetup_Config->CbsCmnCpuCcd9DowncoreBitMap;
  CbsCmnCpuCcdDowncoreBitMap[10]= pSetup_Config->CbsCmnCpuCcd10DowncoreBitMap;
  CbsCmnCpuCcdDowncoreBitMap[11]= pSetup_Config->CbsCmnCpuCcd11DowncoreBitMap;
  CbsCmnCpuCcdDowncoreBitMap[12]= pSetup_Config->CbsCmnCpuCcd12DowncoreBitMap;
  CbsCmnCpuCcdDowncoreBitMap[13]= pSetup_Config->CbsCmnCpuCcd13DowncoreBitMap;
  CbsCmnCpuCcdDowncoreBitMap[14]= pSetup_Config->CbsCmnCpuCcd14DowncoreBitMap;
  CbsCmnCpuCcdDowncoreBitMap[15]= pSetup_Config->CbsCmnCpuCcd15DowncoreBitMap;

  // Get the setting user just changed
  switch (QuestionId) {
  case KEY_CBS_CPU_CCD_CTRL:
    CbsCpuCcdCtrl = Value->u8;
    break;
  case KEY_CBS_CPU_CORE_CTRL:
    CbsCpuCoreCtrl = Value->u8;
    break;
  case KEY_CBS_CPU_SMT_CTRL:
    CbsCpuSmtCtrl = Value->u8;
    break;
  case KEY_CBS_CMN_CPU_DOWNCORE_MODE:
    CbsCmnCpuDowncoreMode = Value->u8;
    break;
  case KEY_CBS_CMN_CPU_CCD0_DOWNCORE_BIT_MAP:
    CbsCmnCpuCcdDowncoreBitMap[0]= Value->u32;
    break;
  case KEY_CBS_CMN_CPU_CCD1_DOWNCORE_BIT_MAP:
    CbsCmnCpuCcdDowncoreBitMap[1]= Value->u32;
    break;
  case KEY_CBS_CMN_CPU_CCD2_DOWNCORE_BIT_MAP:
    CbsCmnCpuCcdDowncoreBitMap[2]= Value->u32;
    break;
  case KEY_CBS_CMN_CPU_CCD3_DOWNCORE_BIT_MAP:
    CbsCmnCpuCcdDowncoreBitMap[3]= Value->u32;
    break;
  case KEY_CBS_CMN_CPU_CCD4_DOWNCORE_BIT_MAP:
    CbsCmnCpuCcdDowncoreBitMap[4]= Value->u32;
    break;
  case KEY_CBS_CMN_CPU_CCD5_DOWNCORE_BIT_MAP:
    CbsCmnCpuCcdDowncoreBitMap[5]= Value->u32;
    break;
  case KEY_CBS_CMN_CPU_CCD6_DOWNCORE_BIT_MAP:
    CbsCmnCpuCcdDowncoreBitMap[6]= Value->u32;
    break;
  case KEY_CBS_CMN_CPU_CCD7_DOWNCORE_BIT_MAP:
    CbsCmnCpuCcdDowncoreBitMap[7]= Value->u32;
    break;
  case KEY_CBS_CMN_CPU_CCD8_DOWNCORE_BIT_MAP:
    CbsCmnCpuCcdDowncoreBitMap[8]= Value->u32;
    break;
  case KEY_CBS_CMN_CPU_CCD9_DOWNCORE_BIT_MAP:
    CbsCmnCpuCcdDowncoreBitMap[9]= Value->u32;
    break;
  case KEY_CBS_CMN_CPU_CCD10_DOWNCORE_BIT_MAP:
    CbsCmnCpuCcdDowncoreBitMap[10]= Value->u32;
    break;
  case KEY_CBS_CMN_CPU_CCD11_DOWNCORE_BIT_MAP:
    CbsCmnCpuCcdDowncoreBitMap[11]= Value->u32;
    break;
  case KEY_CBS_CMN_CPU_CCD12_DOWNCORE_BIT_MAP:
    CbsCmnCpuCcdDowncoreBitMap[12]= Value->u32;
    break;
  case KEY_CBS_CMN_CPU_CCD13_DOWNCORE_BIT_MAP:
    CbsCmnCpuCcdDowncoreBitMap[13]= Value->u32;
    break;
  case KEY_CBS_CMN_CPU_CCD14_DOWNCORE_BIT_MAP:
    CbsCmnCpuCcdDowncoreBitMap[14]= Value->u32;
    break;
  case KEY_CBS_CMN_CPU_CCD15_DOWNCORE_BIT_MAP:
    CbsCmnCpuCcdDowncoreBitMap[15]= Value->u32;
    break;
  }

  if (CbsCmnCpuDowncoreMode == 0) {
    // Enablement Option mode
    MsgString2 = MsgString2Option;

    // Get the CCD number from option
    TempCcdNum = 0;
    for (Index = 0; Index < MAX_CCDS_PER_IOD; Index++) {
      if (((1 << Index) & CcdPresentFuse) && (((1 << Index) & CcdDownFuse) == 0)) {
        TempCcdNum++;
      }
    }
    switch (CbsCpuCcdCtrl) {
    case CCD_MODE_2_CCDS:
      TargetCcdNum = 2;
      break;
    case CCD_MODE_4_CCDS:
      TargetCcdNum = 4;
      break;
    case CCD_MODE_6_CCDS:
      TargetCcdNum = 6;
      break;
    case CCD_MODE_8_CCDS:
      TargetCcdNum = 8;
      break;
    case CCD_MODE_10_CCDS:
      TargetCcdNum = 10;
      break;
    case CCD_MODE_12_CCDS:
      TargetCcdNum = 12;
      break;
    case CCD_MODE_14_CCDS:
      TargetCcdNum = 14;
      break;
    default:
      TargetCcdNum = TempCcdNum;
    }
    if (TargetCcdNum >= TempCcdNum) {
      TargetCcdNum = TempCcdNum;
    }

    // Get the Core number from option
    switch (CbsCpuCoreCtrl) {
    case CCX_DOWN_CORE_1_0:
      TargetCoreNum = 1;
      break;
    case CCX_DOWN_CORE_2_0:
      TargetCoreNum = 2;
      break;
    case CCX_DOWN_CORE_3_0:
      TargetCoreNum = 3;
      break;
    case CCX_DOWN_CORE_4_0:
      TargetCoreNum = 4;
      break;
    case CCX_DOWN_CORE_5_0:
      TargetCoreNum = 5;
      break;
    case CCX_DOWN_CORE_6_0:
      TargetCoreNum = 6;
      break;
    case CCX_DOWN_CORE_7_0:
      TargetCoreNum = 7;
      break;
    case CCX_DOWN_CORE_8_0:
      TargetCoreNum = 8;
      break;
    case CCX_DOWN_CORE_9_0:
      TargetCoreNum = 9;
      break;
    case CCX_DOWN_CORE_10_0:
      TargetCoreNum = 10;
      break;
    case CCX_DOWN_CORE_11_0:
      TargetCoreNum = 11;
      break;
    case CCX_DOWN_CORE_12_0:
      TargetCoreNum = 12;
      break;
    case CCX_DOWN_CORE_13_0:
      TargetCoreNum = 13;
      break;
    case CCX_DOWN_CORE_14_0:
      TargetCoreNum = 14;
      break;
    case CCX_DOWN_CORE_15_0:
      TargetCoreNum = 15;
      break;
    default:
      TargetCoreNum = 0;
      break;
    }

    gBS->SetMem (CbsCmnCpuCcdDowncoreBitMap, sizeof (CbsCmnCpuCcdDowncoreBitMap), 0);

    if (TargetCcdNum != 0) {
      TempCcdNum = 0;
      for (Index = 0; Index < MAX_CCDS_PER_IOD; Index++) {
        if (Index < ARRAY_SIZE (CbsCmnCpuCcdDowncoreBitMap)) {
          if (TempCcdNum < TargetCcdNum) {
            if ((CcdPresentFuse & (1 << Index)) && ((CcdDownFuse & (1 << Index)) == 0)) {
              TempCcdNum++;
            } else {
              CbsCmnCpuCcdDowncoreBitMap[Index] = (1 << CoreCountByFuse) - 1;
            }
          } else {
            CbsCmnCpuCcdDowncoreBitMap[Index] = (1 << CoreCountByFuse) - 1;
          }
        }
      }
    }

    if (TargetCoreNum != 0) {
      for (CcdIndex = 0; CcdIndex < ARRAY_SIZE (CbsCmnCpuCcdDowncoreBitMap); CcdIndex ++) {
        if (CbsCmnCpuCcdDowncoreBitMap[CcdIndex] == 0) {
          CoreCount = 0;
          for (CoreIndex = 0; CoreIndex < CCX_MAX_CORES_PER_COMPLEX; CoreIndex++) {
            if (CoreCount < TargetCoreNum) {
              if ((CoreDisFuse[CcdIndex] & (1 << CoreIndex)) == 0) {
                CoreCount++;
              }
            } else {
              CbsCmnCpuCcdDowncoreBitMap[CcdIndex] |= (1 << CoreIndex);
            }
          }
        }
      }
    }
  } else {
    // Bitmap mode
    MsgString2 = MsgString2Bitmap;
  }

  // Count total core for each CCD
  TotalCoreCount = 0;
  for (Index = 0; Index < MAX_CCDS_PER_IOD; Index++) {
    DEBUG ((DEBUG_INFO, "CCD%02d Downcore Bitmap = 0x%08x  ", Index, CbsCmnCpuCcdDowncoreBitMap[Index]));
    DEBUG ((DEBUG_INFO, "Fuse DisCore Bitmap = 0x%08x  ", CoreDisFuse[Index]));
    if (((1 << Index) & CcdPresentFuse) && (((1 << Index) & CcdDownFuse) == 0)) {
      CoreDisableMap = ((CoreDisFuse[Index] | CbsCmnCpuCcdDowncoreBitMap[Index]) & MaxCoreMask);
      CoreCount = CoreCountByFuse;
      while (CoreDisableMap != 0) {
        if (CoreDisableMap & 1) {
          CoreCount--;
        }
        CoreDisableMap >>= 1;
      }
    } else {
      CoreCount = 0;
    }
    TotalCoreCount += CoreCount;
    DEBUG ((DEBUG_INFO, "CoreCount = %d\n", CoreCount));
  }
  DEBUG ((DEBUG_INFO, "TotalCoreCount = %d\n", TotalCoreCount));

  // Get thread number per core
  TargetThreadNumPerCore = (CbsCpuSmtCtrl ? 2 : 1);
  DEBUG ((DEBUG_INFO, "TargetThreadNumPerCore = %d\n", TargetThreadNumPerCore));

  // Count target total thread count
  TargetThreadNum = TotalCoreCount * TargetThreadNumPerCore;
  DEBUG ((DEBUG_INFO, "TargetThreadNum = %d\n", TargetThreadNum));

  // Popup warning message if the target thread is too small
  if (TargetThreadNum < MINIMUM_REQUIRED_THREAD_NUMBER) {
    UnicodeSPrint (
      MsgString1,
      sizeof (MsgString1),
      MsgString1Base,
      MINIMUM_REQUIRED_THREAD_NUMBER
      );
    CreatePopUp (
      EFI_LIGHTGRAY | EFI_BACKGROUND_BLUE,
      &Key,
      MsgString1,
      MsgString2,
      NULL
      );
    return EFI_ABORTED;
  }

  return EFI_SUCCESS;
}

EFI_STATUS
XgmiPresetControlCallback (
  IN  CONST EFI_HII_CONFIG_ACCESS_PROTOCOL   *This,
  IN  EFI_BROWSER_ACTION                     Action,
  IN  EFI_QUESTION_ID                        QuestionId,
  IN  UINT8                                  Type,
  IN  EFI_IFR_TYPE_VALUE                     *Value,
  OUT EFI_BROWSER_ACTION_REQUEST             *ActionRequest,
  OUT CBS_CONFIG                             *pSetup_Config,
  IN  EFI_HII_HANDLE                         HiiHandle
  )
{
  UINT16 Value16;

  if (Value->u8 != 1) {
    return EFI_SUCCESS;
  }

  Value16 = (UINT16) ((XgmiInitPreset >> 0) & 0xF);
  pSetup_Config->CbsDfXgmiInitPresetS0L0P0 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS0L0P1 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS0L0P2 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS0L0P3 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS0L0   = (Value16 << 0) + (Value16 << 4) + (Value16 << 8) + (Value16 << 12);

  Value16 = (UINT16) ((XgmiInitPreset >> 4) & 0xF);
  pSetup_Config->CbsDfXgmiInitPresetS0L1P0 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS0L1P1 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS0L1P2 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS0L1P3 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS0L1   = (Value16 << 0) + (Value16 << 4) + (Value16 << 8) + (Value16 << 12);

  Value16 = (UINT16) ((XgmiInitPreset >> 8) & 0xF);
  pSetup_Config->CbsDfXgmiInitPresetS0L2P0 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS0L2P1 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS0L2P2 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS0L2P3 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS0L2   = (Value16 << 0) + (Value16 << 4) + (Value16 << 8) + (Value16 << 12);

  Value16 = (UINT16) ((XgmiInitPreset >> 12) & 0xF);
  pSetup_Config->CbsDfXgmiInitPresetS0L3P0 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS0L3P1 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS0L3P2 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS0L3P3 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS0L3   = (Value16 << 0) + (Value16 << 4) + (Value16 << 8) + (Value16 << 12);

  Value16 = (UINT16) ((XgmiInitPreset >> 16) & 0xF);
  pSetup_Config->CbsDfXgmiInitPresetS1L0P0 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS1L0P1 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS1L0P2 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS1L0P3 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS1L0   = (Value16 << 0) + (Value16 << 4) + (Value16 << 8) + (Value16 << 12);

  Value16 = (UINT16) ((XgmiInitPreset >> 20) & 0xF);
  pSetup_Config->CbsDfXgmiInitPresetS1L1P0 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS1L1P1 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS1L1P2 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS1L1P3 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS1L1   = (Value16 << 0) + (Value16 << 4) + (Value16 << 8) + (Value16 << 12);

  Value16 = (UINT16) ((XgmiInitPreset >> 24) & 0xF);
  pSetup_Config->CbsDfXgmiInitPresetS1L2P0 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS1L2P1 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS1L2P2 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS1L2P3 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS1L2   = (Value16 << 0) + (Value16 << 4) + (Value16 << 8) + (Value16 << 12);

  Value16 = (UINT16) ((XgmiInitPreset >> 28) & 0xF);
  pSetup_Config->CbsDfXgmiInitPresetS1L3P0 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS1L3P1 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS1L3P2 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS1L3P3 = Value16;
  pSetup_Config->CbsDfXgmiInitPresetS1L3   = (Value16 << 0) + (Value16 << 4) + (Value16 << 8) + (Value16 << 12);

  return EFI_SUCCESS;
}

EFI_STATUS
XgmiGlobalPresetListCallback (
  IN  CONST EFI_HII_CONFIG_ACCESS_PROTOCOL   *This,
  IN  EFI_BROWSER_ACTION                     Action,
  IN  EFI_QUESTION_ID                        QuestionId,
  IN  UINT8                                  Type,
  IN  EFI_IFR_TYPE_VALUE                     *Value,
  OUT EFI_BROWSER_ACTION_REQUEST             *ActionRequest,
  OUT CBS_CONFIG                             *pSetup_Config,
  IN  EFI_HII_HANDLE                         HiiHandle
  )
{
  pSetup_Config->CbsDfXgmiPresetP11 = (pSetup_Config->CbsDfXgmiCmn1P11 <<  0) +
                                      (pSetup_Config->CbsDfXgmiCnP11   <<  8) +
                                      (pSetup_Config->CbsDfXgmiCnp1P11 << 16);

  pSetup_Config->CbsDfXgmiPresetP12 = (pSetup_Config->CbsDfXgmiCmn1P12 <<  0) +
                                      (pSetup_Config->CbsDfXgmiCnP12   <<  8) +
                                      (pSetup_Config->CbsDfXgmiCnp1P12 << 16);

  pSetup_Config->CbsDfXgmiPresetP13 = (pSetup_Config->CbsDfXgmiCmn1P13 <<  0) +
                                      (pSetup_Config->CbsDfXgmiCnP13   <<  8) +
                                      (pSetup_Config->CbsDfXgmiCnp1P13 << 16);

  pSetup_Config->CbsDfXgmiPresetP14 = (pSetup_Config->CbsDfXgmiCmn1P14 <<  0) +
                                      (pSetup_Config->CbsDfXgmiCnP14   <<  8) +
                                      (pSetup_Config->CbsDfXgmiCnp1P14 << 16);

  pSetup_Config->CbsDfXgmiPresetP15 = (pSetup_Config->CbsDfXgmiCmn1P15 <<  0) +
                                      (pSetup_Config->CbsDfXgmiCnP15   <<  8) +
                                      (pSetup_Config->CbsDfXgmiCnp1P15 << 16);

  return EFI_SUCCESS;
}

EFI_STATUS
XgmiInitialPresetCallback (
  IN  CONST EFI_HII_CONFIG_ACCESS_PROTOCOL   *This,
  IN  EFI_BROWSER_ACTION                     Action,
  IN  EFI_QUESTION_ID                        QuestionId,
  IN  UINT8                                  Type,
  IN  EFI_IFR_TYPE_VALUE                     *Value,
  OUT EFI_BROWSER_ACTION_REQUEST             *ActionRequest,
  OUT CBS_CONFIG                             *pSetup_Config,
  IN  EFI_HII_HANDLE                         HiiHandle
  )
{
  pSetup_Config->CbsDfXgmiInitPresetS0L0 = (pSetup_Config->CbsDfXgmiInitPresetS0L0P0 <<  0) +
                                           (pSetup_Config->CbsDfXgmiInitPresetS0L0P1 <<  4) +
                                           (pSetup_Config->CbsDfXgmiInitPresetS0L0P2 <<  8) +
                                           (pSetup_Config->CbsDfXgmiInitPresetS0L0P3 << 12);

  pSetup_Config->CbsDfXgmiInitPresetS0L1 = (pSetup_Config->CbsDfXgmiInitPresetS0L1P0 <<  0) +
                                           (pSetup_Config->CbsDfXgmiInitPresetS0L1P1 <<  4) +
                                           (pSetup_Config->CbsDfXgmiInitPresetS0L1P2 <<  8) +
                                           (pSetup_Config->CbsDfXgmiInitPresetS0L1P3 << 12);

  pSetup_Config->CbsDfXgmiInitPresetS0L2 = (pSetup_Config->CbsDfXgmiInitPresetS0L2P0 <<  0) +
                                           (pSetup_Config->CbsDfXgmiInitPresetS0L2P1 <<  4) +
                                           (pSetup_Config->CbsDfXgmiInitPresetS0L2P2 <<  8) +
                                           (pSetup_Config->CbsDfXgmiInitPresetS0L2P3 << 12);

  pSetup_Config->CbsDfXgmiInitPresetS0L3 = (pSetup_Config->CbsDfXgmiInitPresetS0L3P0 <<  0) +
                                           (pSetup_Config->CbsDfXgmiInitPresetS0L3P1 <<  4) +
                                           (pSetup_Config->CbsDfXgmiInitPresetS0L3P2 <<  8) +
                                           (pSetup_Config->CbsDfXgmiInitPresetS0L3P3 << 12);

  pSetup_Config->CbsDfXgmiInitPresetS1L0 = (pSetup_Config->CbsDfXgmiInitPresetS1L0P0 <<  0) +
                                           (pSetup_Config->CbsDfXgmiInitPresetS1L0P1 <<  4) +
                                           (pSetup_Config->CbsDfXgmiInitPresetS1L0P2 <<  8) +
                                           (pSetup_Config->CbsDfXgmiInitPresetS1L0P3 << 12);

  pSetup_Config->CbsDfXgmiInitPresetS1L1 = (pSetup_Config->CbsDfXgmiInitPresetS1L1P0 <<  0) +
                                           (pSetup_Config->CbsDfXgmiInitPresetS1L1P1 <<  4) +
                                           (pSetup_Config->CbsDfXgmiInitPresetS1L1P2 <<  8) +
                                           (pSetup_Config->CbsDfXgmiInitPresetS1L1P3 << 12);

  pSetup_Config->CbsDfXgmiInitPresetS1L2 = (pSetup_Config->CbsDfXgmiInitPresetS1L2P0 <<  0) +
                                           (pSetup_Config->CbsDfXgmiInitPresetS1L2P1 <<  4) +
                                           (pSetup_Config->CbsDfXgmiInitPresetS1L2P2 <<  8) +
                                           (pSetup_Config->CbsDfXgmiInitPresetS1L2P3 << 12);

  pSetup_Config->CbsDfXgmiInitPresetS1L3 = (pSetup_Config->CbsDfXgmiInitPresetS1L3P0 <<  0) +
                                           (pSetup_Config->CbsDfXgmiInitPresetS1L3P1 <<  4) +
                                           (pSetup_Config->CbsDfXgmiInitPresetS1L3P2 <<  8) +
                                           (pSetup_Config->CbsDfXgmiInitPresetS1L3P3 << 12);

  return EFI_SUCCESS;
}

EFI_STATUS
XgmiTxeqSearchMaskCallback (
  IN  CONST EFI_HII_CONFIG_ACCESS_PROTOCOL   *This,
  IN  EFI_BROWSER_ACTION                     Action,
  IN  EFI_QUESTION_ID                        QuestionId,
  IN  UINT8                                  Type,
  IN  EFI_IFR_TYPE_VALUE                     *Value,
  OUT EFI_BROWSER_ACTION_REQUEST             *ActionRequest,
  OUT CBS_CONFIG                             *pSetup_Config,
  IN  EFI_HII_HANDLE                         HiiHandle
  )
{
  pSetup_Config->CbsDfXgmiTxeqS0L0P01 = (pSetup_Config->CbsDfXgmiTxeqS0L0P0 <<  0) +
                                        (pSetup_Config->CbsDfXgmiTxeqS0L0P1 << 16);
  pSetup_Config->CbsDfXgmiTxeqS0L0P23 = (pSetup_Config->CbsDfXgmiTxeqS0L0P2 <<  0) +
                                        (pSetup_Config->CbsDfXgmiTxeqS0L0P3 << 16);

  pSetup_Config->CbsDfXgmiTxeqS0L1P01 = (pSetup_Config->CbsDfXgmiTxeqS0L1P0 <<  0) +
                                        (pSetup_Config->CbsDfXgmiTxeqS0L1P1 << 16);
  pSetup_Config->CbsDfXgmiTxeqS0L1P23 = (pSetup_Config->CbsDfXgmiTxeqS0L1P2 <<  0) +
                                        (pSetup_Config->CbsDfXgmiTxeqS0L1P3 << 16);

  pSetup_Config->CbsDfXgmiTxeqS0L2P01 = (pSetup_Config->CbsDfXgmiTxeqS0L2P0 <<  0) +
                                        (pSetup_Config->CbsDfXgmiTxeqS0L2P1 << 16);
  pSetup_Config->CbsDfXgmiTxeqS0L2P23 = (pSetup_Config->CbsDfXgmiTxeqS0L2P2 <<  0) +
                                        (pSetup_Config->CbsDfXgmiTxeqS0L2P3 << 16);

  pSetup_Config->CbsDfXgmiTxeqS0L3P01 = (pSetup_Config->CbsDfXgmiTxeqS0L3P0 <<  0) +
                                        (pSetup_Config->CbsDfXgmiTxeqS0L3P1 << 16);
  pSetup_Config->CbsDfXgmiTxeqS0L3P23 = (pSetup_Config->CbsDfXgmiTxeqS0L3P2 <<  0) +
                                        (pSetup_Config->CbsDfXgmiTxeqS0L3P3 << 16);

  pSetup_Config->CbsDfXgmiTxeqS1L0P01 = (pSetup_Config->CbsDfXgmiTxeqS1L0P0 <<  0) +
                                        (pSetup_Config->CbsDfXgmiTxeqS1L0P1 << 16);
  pSetup_Config->CbsDfXgmiTxeqS1L0P23 = (pSetup_Config->CbsDfXgmiTxeqS1L0P2 <<  0) +
                                        (pSetup_Config->CbsDfXgmiTxeqS1L0P3 << 16);

  pSetup_Config->CbsDfXgmiTxeqS1L1P01 = (pSetup_Config->CbsDfXgmiTxeqS1L1P0 <<  0) +
                                        (pSetup_Config->CbsDfXgmiTxeqS1L1P1 << 16);
  pSetup_Config->CbsDfXgmiTxeqS1L1P23 = (pSetup_Config->CbsDfXgmiTxeqS1L1P2 <<  0) +
                                        (pSetup_Config->CbsDfXgmiTxeqS1L1P3 << 16);

  pSetup_Config->CbsDfXgmiTxeqS1L2P01 = (pSetup_Config->CbsDfXgmiTxeqS1L2P0 <<  0) +
                                        (pSetup_Config->CbsDfXgmiTxeqS1L2P1 << 16);
  pSetup_Config->CbsDfXgmiTxeqS1L2P23 = (pSetup_Config->CbsDfXgmiTxeqS1L2P2 <<  0) +
                                        (pSetup_Config->CbsDfXgmiTxeqS1L2P3 << 16);

  pSetup_Config->CbsDfXgmiTxeqS1L3P01 = (pSetup_Config->CbsDfXgmiTxeqS1L3P0 <<  0) +
                                        (pSetup_Config->CbsDfXgmiTxeqS1L3P1 << 16);
  pSetup_Config->CbsDfXgmiTxeqS1L3P23 = (pSetup_Config->CbsDfXgmiTxeqS1L3P2 <<  0) +
                                        (pSetup_Config->CbsDfXgmiTxeqS1L3P3 << 16);

  return EFI_SUCCESS;
}

EFI_STATUS
XgmiAcDcCoupledLinkCallback (
  IN  CONST EFI_HII_CONFIG_ACCESS_PROTOCOL   *This,
  IN  EFI_BROWSER_ACTION                     Action,
  IN  EFI_QUESTION_ID                        QuestionId,
  IN  UINT8                                  Type,
  IN  EFI_IFR_TYPE_VALUE                     *Value,
  OUT EFI_BROWSER_ACTION_REQUEST             *ActionRequest,
  OUT CBS_CONFIG                             *pSetup_Config,
  IN  EFI_HII_HANDLE                         HiiHandle
  )
{
  pSetup_Config->CbsDfXgmiAcDcCoupledLink = ((pSetup_Config->CbsDfXgmiAcDcCoupledLinkSocket0Link0 & 1) << 0) +
                                            ((pSetup_Config->CbsDfXgmiAcDcCoupledLinkSocket0Link1 & 1) << 1) +
                                            ((pSetup_Config->CbsDfXgmiAcDcCoupledLinkSocket0Link2 & 1) << 2) +
                                            ((pSetup_Config->CbsDfXgmiAcDcCoupledLinkSocket0Link3 & 1) << 3) +
                                            ((pSetup_Config->CbsDfXgmiAcDcCoupledLinkSocket1Link0 & 1) << 4) +
                                            ((pSetup_Config->CbsDfXgmiAcDcCoupledLinkSocket1Link1 & 1) << 5) +
                                            ((pSetup_Config->CbsDfXgmiAcDcCoupledLinkSocket1Link2 & 1) << 6) +
                                            ((pSetup_Config->CbsDfXgmiAcDcCoupledLinkSocket1Link3 & 1) << 7);
  return EFI_SUCCESS;
}

EFI_STATUS
XgmiChannelTypeCallback (
  IN  CONST EFI_HII_CONFIG_ACCESS_PROTOCOL   *This,
  IN  EFI_BROWSER_ACTION                     Action,
  IN  EFI_QUESTION_ID                        QuestionId,
  IN  UINT8                                  Type,
  IN  EFI_IFR_TYPE_VALUE                     *Value,
  OUT EFI_BROWSER_ACTION_REQUEST             *ActionRequest,
  OUT CBS_CONFIG                             *pSetup_Config,
  IN  EFI_HII_HANDLE                         HiiHandle
  )
{
  pSetup_Config->CbsDfXgmiChannelType = ((pSetup_Config->CbsDfXgmiChannelTypeSocket0Link0 & 0xF) <<  0) +
                                        ((pSetup_Config->CbsDfXgmiChannelTypeSocket0Link1 & 0xF) <<  4) +
                                        ((pSetup_Config->CbsDfXgmiChannelTypeSocket0Link2 & 0xF) <<  8) +
                                        ((pSetup_Config->CbsDfXgmiChannelTypeSocket0Link3 & 0xF) << 12) +
                                        ((pSetup_Config->CbsDfXgmiChannelTypeSocket1Link0 & 0xF) << 16) +
                                        ((pSetup_Config->CbsDfXgmiChannelTypeSocket1Link1 & 0xF) << 20) +
                                        ((pSetup_Config->CbsDfXgmiChannelTypeSocket1Link2 & 0xF) << 24) +
                                        ((pSetup_Config->CbsDfXgmiChannelTypeSocket1Link3 & 0xF) << 28);
  return EFI_SUCCESS;
}

EFI_STATUS
MemoryChannelDisableCallback (
  IN  EFI_QUESTION_ID                        QuestionId,
  OUT CBS_CONFIG                             *pSetup_Config
  )
{
  UINT32  Value32;

  switch (QuestionId) {
  case KEY_CBS_CMN_MEM_CHANNEL_DISABLE_BITMASK_DDR:
    Value32 = pSetup_Config->CbsCmnMemChannelDisableBitmaskDdr;

    pSetup_Config->CbsCmnMemSocket0Channel0Ddr = (UINT8)!((Value32 >> 0) & 0x01);
    pSetup_Config->CbsCmnMemSocket0Channel1Ddr = (UINT8)!((Value32 >> 1) & 0x01);
    pSetup_Config->CbsCmnMemSocket0Channel2Ddr = (UINT8)!((Value32 >> 2) & 0x01);
    pSetup_Config->CbsCmnMemSocket0Channel3Ddr = (UINT8)!((Value32 >> 3) & 0x01);
    pSetup_Config->CbsCmnMemSocket0Channel4Ddr = (UINT8)!((Value32 >> 4) & 0x01);
    pSetup_Config->CbsCmnMemSocket0Channel5Ddr = (UINT8)!((Value32 >> 5) & 0x01);
    pSetup_Config->CbsCmnMemSocket0Channel6Ddr = (UINT8)!((Value32 >> 6) & 0x01);
    pSetup_Config->CbsCmnMemSocket0Channel7Ddr = (UINT8)!((Value32 >> 7) & 0x01);
    pSetup_Config->CbsCmnMemSocket0Channel8Ddr = (UINT8)!((Value32 >> 8) & 0x01);
    pSetup_Config->CbsCmnMemSocket0Channel9Ddr = (UINT8)!((Value32 >> 9) & 0x01);
    pSetup_Config->CbsCmnMemSocket0Channel10Ddr = (UINT8)!((Value32 >> 10) & 0x01);
    pSetup_Config->CbsCmnMemSocket0Channel11Ddr = (UINT8)!((Value32 >> 11) & 0x01);

    pSetup_Config->CbsCmnMemSocket1Channel0Ddr = (UINT8)!((Value32 >> (MEM_CHANNEL_DISABLE_BITMASK_SOCKET_SHIFT + 0)) & 0x01);
    pSetup_Config->CbsCmnMemSocket1Channel1Ddr = (UINT8)!((Value32 >> (MEM_CHANNEL_DISABLE_BITMASK_SOCKET_SHIFT + 1)) & 0x01);
    pSetup_Config->CbsCmnMemSocket1Channel2Ddr = (UINT8)!((Value32 >> (MEM_CHANNEL_DISABLE_BITMASK_SOCKET_SHIFT + 2)) & 0x01);
    pSetup_Config->CbsCmnMemSocket1Channel3Ddr = (UINT8)!((Value32 >> (MEM_CHANNEL_DISABLE_BITMASK_SOCKET_SHIFT + 3)) & 0x01);
    pSetup_Config->CbsCmnMemSocket1Channel4Ddr = (UINT8)!((Value32 >> (MEM_CHANNEL_DISABLE_BITMASK_SOCKET_SHIFT + 4)) & 0x01);
    pSetup_Config->CbsCmnMemSocket1Channel5Ddr = (UINT8)!((Value32 >> (MEM_CHANNEL_DISABLE_BITMASK_SOCKET_SHIFT + 5)) & 0x01);
    pSetup_Config->CbsCmnMemSocket1Channel6Ddr = (UINT8)!((Value32 >> (MEM_CHANNEL_DISABLE_BITMASK_SOCKET_SHIFT + 6)) & 0x01);
    pSetup_Config->CbsCmnMemSocket1Channel7Ddr = (UINT8)!((Value32 >> (MEM_CHANNEL_DISABLE_BITMASK_SOCKET_SHIFT + 7)) & 0x01);
    pSetup_Config->CbsCmnMemSocket1Channel8Ddr = (UINT8)!((Value32 >> (MEM_CHANNEL_DISABLE_BITMASK_SOCKET_SHIFT + 8)) & 0x01);
    pSetup_Config->CbsCmnMemSocket1Channel9Ddr = (UINT8)!((Value32 >> (MEM_CHANNEL_DISABLE_BITMASK_SOCKET_SHIFT + 9)) & 0x01);
    pSetup_Config->CbsCmnMemSocket1Channel10Ddr = (UINT8)!((Value32 >> (MEM_CHANNEL_DISABLE_BITMASK_SOCKET_SHIFT + 10)) & 0x01);
    pSetup_Config->CbsCmnMemSocket1Channel11Ddr = (UINT8)!((Value32 >> (MEM_CHANNEL_DISABLE_BITMASK_SOCKET_SHIFT + 11)) & 0x01);

    break;

  case KEY_CBS_CMN_MEM_SOCKET0_CHANNEL0_DDR:
  case KEY_CBS_CMN_MEM_SOCKET0_CHANNEL1_DDR:
  case KEY_CBS_CMN_MEM_SOCKET0_CHANNEL2_DDR:
  case KEY_CBS_CMN_MEM_SOCKET0_CHANNEL3_DDR:
  case KEY_CBS_CMN_MEM_SOCKET0_CHANNEL4_DDR:
  case KEY_CBS_CMN_MEM_SOCKET0_CHANNEL5_DDR:
  case KEY_CBS_CMN_MEM_SOCKET0_CHANNEL6_DDR:
  case KEY_CBS_CMN_MEM_SOCKET0_CHANNEL7_DDR:
  case KEY_CBS_CMN_MEM_SOCKET0_CHANNEL8_DDR:
  case KEY_CBS_CMN_MEM_SOCKET0_CHANNEL9_DDR:
  case KEY_CBS_CMN_MEM_SOCKET0_CHANNEL10_DDR:
  case KEY_CBS_CMN_MEM_SOCKET0_CHANNEL11_DDR:
  case KEY_CBS_CMN_MEM_SOCKET1_CHANNEL0_DDR:
  case KEY_CBS_CMN_MEM_SOCKET1_CHANNEL1_DDR:
  case KEY_CBS_CMN_MEM_SOCKET1_CHANNEL2_DDR:
  case KEY_CBS_CMN_MEM_SOCKET1_CHANNEL3_DDR:
  case KEY_CBS_CMN_MEM_SOCKET1_CHANNEL4_DDR:
  case KEY_CBS_CMN_MEM_SOCKET1_CHANNEL5_DDR:
  case KEY_CBS_CMN_MEM_SOCKET1_CHANNEL6_DDR:
  case KEY_CBS_CMN_MEM_SOCKET1_CHANNEL7_DDR:
  case KEY_CBS_CMN_MEM_SOCKET1_CHANNEL8_DDR:
  case KEY_CBS_CMN_MEM_SOCKET1_CHANNEL9_DDR:
  case KEY_CBS_CMN_MEM_SOCKET1_CHANNEL10_DDR:
  case KEY_CBS_CMN_MEM_SOCKET1_CHANNEL11_DDR:
    Value32 = 0;

    Value32 |= (UINT32)!pSetup_Config->CbsCmnMemSocket0Channel0Ddr << 0;
    Value32 |= (UINT32)!pSetup_Config->CbsCmnMemSocket0Channel1Ddr << 1;
    Value32 |= (UINT32)!pSetup_Config->CbsCmnMemSocket0Channel2Ddr << 2;
    Value32 |= (UINT32)!pSetup_Config->CbsCmnMemSocket0Channel3Ddr << 3;
    Value32 |= (UINT32)!pSetup_Config->CbsCmnMemSocket0Channel4Ddr << 4;
    Value32 |= (UINT32)!pSetup_Config->CbsCmnMemSocket0Channel5Ddr << 5;
    Value32 |= (UINT32)!pSetup_Config->CbsCmnMemSocket0Channel6Ddr << 6;
    Value32 |= (UINT32)!pSetup_Config->CbsCmnMemSocket0Channel7Ddr << 7;
    Value32 |= (UINT32)!pSetup_Config->CbsCmnMemSocket0Channel8Ddr << 8;
    Value32 |= (UINT32)!pSetup_Config->CbsCmnMemSocket0Channel9Ddr << 9;
    Value32 |= (UINT32)!pSetup_Config->CbsCmnMemSocket0Channel10Ddr << 10;
    Value32 |= (UINT32)!pSetup_Config->CbsCmnMemSocket0Channel11Ddr << 11;

    Value32 |= (UINT32)!pSetup_Config->CbsCmnMemSocket1Channel0Ddr << (MEM_CHANNEL_DISABLE_BITMASK_SOCKET_SHIFT + 0);
    Value32 |= (UINT32)!pSetup_Config->CbsCmnMemSocket1Channel1Ddr << (MEM_CHANNEL_DISABLE_BITMASK_SOCKET_SHIFT + 1);
    Value32 |= (UINT32)!pSetup_Config->CbsCmnMemSocket1Channel2Ddr << (MEM_CHANNEL_DISABLE_BITMASK_SOCKET_SHIFT + 2);
    Value32 |= (UINT32)!pSetup_Config->CbsCmnMemSocket1Channel3Ddr << (MEM_CHANNEL_DISABLE_BITMASK_SOCKET_SHIFT + 3);
    Value32 |= (UINT32)!pSetup_Config->CbsCmnMemSocket1Channel4Ddr << (MEM_CHANNEL_DISABLE_BITMASK_SOCKET_SHIFT + 4);
    Value32 |= (UINT32)!pSetup_Config->CbsCmnMemSocket1Channel5Ddr << (MEM_CHANNEL_DISABLE_BITMASK_SOCKET_SHIFT + 5);
    Value32 |= (UINT32)!pSetup_Config->CbsCmnMemSocket1Channel6Ddr << (MEM_CHANNEL_DISABLE_BITMASK_SOCKET_SHIFT + 6);
    Value32 |= (UINT32)!pSetup_Config->CbsCmnMemSocket1Channel7Ddr << (MEM_CHANNEL_DISABLE_BITMASK_SOCKET_SHIFT + 7);
    Value32 |= (UINT32)!pSetup_Config->CbsCmnMemSocket1Channel8Ddr << (MEM_CHANNEL_DISABLE_BITMASK_SOCKET_SHIFT + 8);
    Value32 |= (UINT32)!pSetup_Config->CbsCmnMemSocket1Channel9Ddr << (MEM_CHANNEL_DISABLE_BITMASK_SOCKET_SHIFT + 9);
    Value32 |= (UINT32)!pSetup_Config->CbsCmnMemSocket1Channel10Ddr << (MEM_CHANNEL_DISABLE_BITMASK_SOCKET_SHIFT + 10);
    Value32 |= (UINT32)!pSetup_Config->CbsCmnMemSocket1Channel11Ddr << (MEM_CHANNEL_DISABLE_BITMASK_SOCKET_SHIFT + 11);

    pSetup_Config->CbsCmnMemChannelDisableBitmaskDdr = Value32;

    break;

  default:
    break;
  }

  return EFI_SUCCESS;
}

EFI_STATUS
MemPmuBistAlgorithmCallback (
  IN  EFI_QUESTION_ID                        QuestionId,
  OUT CBS_CONFIG                             *pSetup_Config
  )
{
  UINT16  Value16;

  switch (QuestionId) {
  case KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM_BIT_MASK_DDR:
    Value16 = pSetup_Config->CbsCmnMemPmuBistAlgorithmBitMaskDdr;
    pSetup_Config->CbsCmnMemPmuBistAlgorithm1 = (UINT8)((Value16 >> 0) & 0x01);
    pSetup_Config->CbsCmnMemPmuBistAlgorithm2 = (UINT8)((Value16 >> 1) & 0x01);
    pSetup_Config->CbsCmnMemPmuBistAlgorithm3 = (UINT8)((Value16 >> 2) & 0x01);
    pSetup_Config->CbsCmnMemPmuBistAlgorithm4 = (UINT8)((Value16 >> 3) & 0x01);
    pSetup_Config->CbsCmnMemPmuBistAlgorithm5 = (UINT8)((Value16 >> 4) & 0x01);
    pSetup_Config->CbsCmnMemPmuBistAlgorithm6 = (UINT8)((Value16 >> 5) & 0x01);
    pSetup_Config->CbsCmnMemPmuBistAlgorithm7 = (UINT8)((Value16 >> 6) & 0x01);
    pSetup_Config->CbsCmnMemPmuBistAlgorithm8 = (UINT8)((Value16 >> 7) & 0x01);
    pSetup_Config->CbsCmnMemPmuBistAlgorithm9 = (UINT8)((Value16 >> 8) & 0x01);
    break;

  case KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM1:
  case KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM2:
  case KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM3:
  case KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM4:
  case KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM5:
  case KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM6:
  case KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM7:
  case KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM8:
  case KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM9:
    Value16 = 0;
    Value16 |= (UINT16)pSetup_Config->CbsCmnMemPmuBistAlgorithm1 << 0;
    Value16 |= (UINT16)pSetup_Config->CbsCmnMemPmuBistAlgorithm2 << 1;
    Value16 |= (UINT16)pSetup_Config->CbsCmnMemPmuBistAlgorithm3 << 2;
    Value16 |= (UINT16)pSetup_Config->CbsCmnMemPmuBistAlgorithm4 << 3;
    Value16 |= (UINT16)pSetup_Config->CbsCmnMemPmuBistAlgorithm5 << 4;
    Value16 |= (UINT16)pSetup_Config->CbsCmnMemPmuBistAlgorithm6 << 5;
    Value16 |= (UINT16)pSetup_Config->CbsCmnMemPmuBistAlgorithm7 << 6;
    Value16 |= (UINT16)pSetup_Config->CbsCmnMemPmuBistAlgorithm8 << 7;
    Value16 |= (UINT16)pSetup_Config->CbsCmnMemPmuBistAlgorithm9 << 8;
    pSetup_Config->CbsCmnMemPmuBistAlgorithmBitMaskDdr = Value16;
    break;

  default:
    break;
  }

  return EFI_SUCCESS;
}

// SPL Fuse = (1 << SPL) - 1
/*
For example:
+-----+----------------------------------------------------------+
| SPL |                         SPL Fuse                         |
+-----+----------------------------------------------------------+
|   0 | 0x0  = b0000 0000                                        |
|   1 | 0x1  = b0000 0001                                        |
|   2 | 0x3  = b0000 0011                                        |
|   3 | 0x7  = b0000 0111                                        |
|   4 | 0xF  = b0000 1111                                        |
|   5 | 0x1F = b0001 1111                                        |
|   6 | 0x3F = b0011 1111                                        |
| ... | ...                                                      |
|  31 | 0x7FFFFFFF                                               |
|  32 | 0xFFFFFFFF                                               |
|  33 | (SplFuse1=0x1, SplFuse0=0xFFFFFFFF)                      |
|  34 | (SplFuse1=0x3, SplFuse0=0xFFFFFFFF)                      |
| ... | ...                                                      |
|  64 | (SplFuse1=0xFFFFFFFF, SplFuse0=0xFFFFFFFF)               |
|  65 | (SplFuse2=0x1, SplFuse1=0xFFFFFFFF, SplFuse0=0xFFFFFFFF) |
+-----+----------------------------------------------------------+
*/
UINT32 GetSpl (UINT32 SplFuse)
{
  UINT32 SPL = 0;
  while (SplFuse != 0)
  {
      SplFuse = SplFuse & (SplFuse - 1);
      SPL++;
  }
  return SPL;
}

static UINT8    SocFarEnforced = 0;
static UINT32   SplFuseValue = 0;
static UINT32   SplTableValue = 0;

VOID
SocFarInitData (
    VOID
 )
{
  UINT32          HSTIState = 0;
  UINT32          SplFuse0 = 0;
  UINT32          SplFuse1 = 0;
  UINT32          SplFuse2 = 0;
  UINT32          SplFuse3 = 0;

  PspMboxBiosQueryHSTIState(&HSTIState);
  PspMboxQuerySplFuse (&SplFuse0, &SplFuse1, &SplFuse2, &SplFuse3);
  SplFuseValue = GetSpl (SplFuse0) + GetSpl (SplFuse1) + GetSpl (SplFuse2) + GetSpl (SplFuse3);
  if (PspMboxQuerySplValue (&SplTableValue) != EFI_SUCCESS) {
    SplTableValue = 0;
  }
  SocFarEnforced = ((HSTIState & PSP_ANTI_ROLLBACK_STATUS) != 0) ? 1 : 0;
}

BOOLEAN
SocFarLoadDefault (
    IN OUT CBS_CONFIG   *Setup_Config
 )
{
  BOOLEAN     DefaultChanged = FALSE;

  if (Setup_Config->CbsCmnSocFarEnforced != SocFarEnforced) {
    Setup_Config->CbsCmnSocFarEnforced = SocFarEnforced;
    DefaultChanged = TRUE;
  }

  if (Setup_Config->CbsCmnSocSplFuse != SplFuseValue) {
    Setup_Config->CbsCmnSocSplFuse = SplFuseValue;
    DefaultChanged = TRUE;
  }

  if (Setup_Config->CbsCmnSocSplValueInTbl != SplTableValue) {
    Setup_Config->CbsCmnSocSplValueInTbl = SplTableValue;
    DefaultChanged = TRUE;
  }

  return DefaultChanged;
}

EFI_STATUS
AmdCbsSetupCallbackRoutine (
  IN  CONST EFI_HII_CONFIG_ACCESS_PROTOCOL   *This,
  IN  EFI_BROWSER_ACTION                     Action,
  IN  EFI_QUESTION_ID                        QuestionId,
  IN  UINT8                                  Type,
  IN  EFI_IFR_TYPE_VALUE                     *Value,
  OUT EFI_BROWSER_ACTION_REQUEST             *ActionRequest,
  OUT UINT8                                  *IfrData,
  IN  EFI_HII_HANDLE                         HiiHandle
  )
{
  EFI_STATUS  Status = EFI_SUCCESS;
  CBS_CONFIG *pSetup_Config;

  pSetup_Config = (CBS_CONFIG *)IfrData;

  switch (Action) {
    case EFI_BROWSER_ACTION_RETRIEVE:
      Status = OcMode (This, Action, QuestionId, Type, Value, ActionRequest, pSetup_Config, HiiHandle);
      //Status = CbsDfNpsCallback (This, Action, QuestionId, Type, Value, ActionRequest, pSetup_Config, HiiHandle);
      break;
    case EFI_BROWSER_ACTION_CHANGING:
      switch (QuestionId) {
      case KEY_CBS_CPU_SMT_CTRL:
      case KEY_CBS_CPU_CCD_CTRL:
      case KEY_CBS_CPU_CORE_CTRL:
      case KEY_CBS_CMN_CPU_DOWNCORE_MODE:
      case KEY_CBS_CMN_CPU_CCD0_DOWNCORE_BIT_MAP:
      case KEY_CBS_CMN_CPU_CCD1_DOWNCORE_BIT_MAP:
      case KEY_CBS_CMN_CPU_CCD2_DOWNCORE_BIT_MAP:
      case KEY_CBS_CMN_CPU_CCD3_DOWNCORE_BIT_MAP:
      case KEY_CBS_CMN_CPU_CCD4_DOWNCORE_BIT_MAP:
      case KEY_CBS_CMN_CPU_CCD5_DOWNCORE_BIT_MAP:
      case KEY_CBS_CMN_CPU_CCD6_DOWNCORE_BIT_MAP:
      case KEY_CBS_CMN_CPU_CCD7_DOWNCORE_BIT_MAP:
      case KEY_CBS_CMN_CPU_CCD8_DOWNCORE_BIT_MAP:
      case KEY_CBS_CMN_CPU_CCD9_DOWNCORE_BIT_MAP:
      case KEY_CBS_CMN_CPU_CCD10_DOWNCORE_BIT_MAP:
      case KEY_CBS_CMN_CPU_CCD11_DOWNCORE_BIT_MAP:
      case KEY_CBS_CMN_CPU_CCD12_DOWNCORE_BIT_MAP:
      case KEY_CBS_CMN_CPU_CCD13_DOWNCORE_BIT_MAP:
      case KEY_CBS_CMN_CPU_CCD14_DOWNCORE_BIT_MAP:
      case KEY_CBS_CMN_CPU_CCD15_DOWNCORE_BIT_MAP:
        Status = ThreadNumberControlCallback (This, Action, QuestionId, Type, Value, ActionRequest, pSetup_Config, HiiHandle);
        break;
      }
      break;
    case EFI_BROWSER_ACTION_CHANGED:
      switch (QuestionId) {
      case KEY_CBS_CMN_CPU_OC_MODE:
      case KEY_CBS_CPU_PST_CUSTOM_P0:
      case KEY_CBS_CPU_PST_CUSTOM_P1:
      case KEY_CBS_CPU_PST_CUSTOM_P2:
      case KEY_CBS_CPU_PST_CUSTOM_P3:
      case KEY_CBS_CPU_PST_CUSTOM_P4:
      case KEY_CBS_CPU_PST_CUSTOM_P5:
      case KEY_CBS_CPU_PST_CUSTOM_P6:
      case KEY_CBS_CPU_PST_CUSTOM_P7:
      case KEY_CBS_CPU_PST0_FID:
      case KEY_CBS_CPU_PST0_VID:
      case KEY_CBS_CPU_PST1_FID:
      case KEY_CBS_CPU_PST1_VID:
      case KEY_CBS_CPU_PST2_FID:
      case KEY_CBS_CPU_PST2_VID:
      case KEY_CBS_CPU_PST3_FID:
      case KEY_CBS_CPU_PST3_VID:
      case KEY_CBS_CPU_PST4_FID:
      case KEY_CBS_CPU_PST4_VID:
      case KEY_CBS_CPU_PST5_FID:
      case KEY_CBS_CPU_PST5_VID:
      case KEY_CBS_CPU_PST6_FID:
      case KEY_CBS_CPU_PST6_VID:
      case KEY_CBS_CPU_PST7_FID:
      case KEY_CBS_CPU_PST7_VID:
        CustomCorePstate (This, Action, QuestionId, Type, Value, ActionRequest, pSetup_Config, HiiHandle);
        break;
      case KEY_CBS_CMN_FCH_SATA_ENABLE:
        CustomFchSataClass (This, Action, QuestionId, Type, Value, ActionRequest, pSetup_Config, HiiHandle);
        break;
      case KEY_CBS_DF_CMN_DRAM_NPS:
        Status = CbsDfCc6AllocSchmCallback (This, Action, QuestionId, Type, Value, ActionRequest, pSetup_Config, HiiHandle);
        break;
      case KEY_CBS_DF_XGMI_PRESET_CONTROL:
        XgmiPresetControlCallback (This, Action, QuestionId, Type, Value, ActionRequest, pSetup_Config, HiiHandle);
        break;
      case KEY_CBS_DF_XGMI_CMN1P11:
      case KEY_CBS_DF_XGMI_CN_P11:
      case KEY_CBS_DF_XGMI_CNP1P11:
      case KEY_CBS_DF_XGMI_CMN1P12:
      case KEY_CBS_DF_XGMI_CN_P12:
      case KEY_CBS_DF_XGMI_CNP1P12:
      case KEY_CBS_DF_XGMI_CMN1P13:
      case KEY_CBS_DF_XGMI_CN_P13:
      case KEY_CBS_DF_XGMI_CNP1P13:
      case KEY_CBS_DF_XGMI_CMN1P14:
      case KEY_CBS_DF_XGMI_CN_P14:
      case KEY_CBS_DF_XGMI_CNP1P14:
      case KEY_CBS_DF_XGMI_CMN1P15:
      case KEY_CBS_DF_XGMI_CN_P15:
      case KEY_CBS_DF_XGMI_CNP1P15:
        XgmiGlobalPresetListCallback (This, Action, QuestionId, Type, Value, ActionRequest, pSetup_Config, HiiHandle);
        break;
      case KEY_CBS_DF_XGMI_INIT_PRESET_S0L0P0:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S0L0P1:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S0L0P2:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S0L0P3:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S0L1P0:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S0L1P1:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S0L1P2:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S0L1P3:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S0L2P0:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S0L2P1:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S0L2P2:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S0L2P3:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S0L3P0:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S0L3P1:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S0L3P2:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S0L3P3:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S1L0P0:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S1L0P1:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S1L0P2:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S1L0P3:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S1L1P0:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S1L1P1:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S1L1P2:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S1L1P3:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S1L2P0:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S1L2P1:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S1L2P2:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S1L2P3:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S1L3P0:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S1L3P1:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S1L3P2:
      case KEY_CBS_DF_XGMI_INIT_PRESET_S1L3P3:
        XgmiInitialPresetCallback (This, Action, QuestionId, Type, Value, ActionRequest, pSetup_Config, HiiHandle);
        break;
      case KEY_CBS_DF_XGMI_TXEQ_S0L0P0:
      case KEY_CBS_DF_XGMI_TXEQ_S0L0P1:
      case KEY_CBS_DF_XGMI_TXEQ_S0L0P2:
      case KEY_CBS_DF_XGMI_TXEQ_S0L0P3:
      case KEY_CBS_DF_XGMI_TXEQ_S0L1P0:
      case KEY_CBS_DF_XGMI_TXEQ_S0L1P1:
      case KEY_CBS_DF_XGMI_TXEQ_S0L1P2:
      case KEY_CBS_DF_XGMI_TXEQ_S0L1P3:
      case KEY_CBS_DF_XGMI_TXEQ_S0L2P0:
      case KEY_CBS_DF_XGMI_TXEQ_S0L2P1:
      case KEY_CBS_DF_XGMI_TXEQ_S0L2P2:
      case KEY_CBS_DF_XGMI_TXEQ_S0L2P3:
      case KEY_CBS_DF_XGMI_TXEQ_S0L3P0:
      case KEY_CBS_DF_XGMI_TXEQ_S0L3P1:
      case KEY_CBS_DF_XGMI_TXEQ_S0L3P2:
      case KEY_CBS_DF_XGMI_TXEQ_S0L3P3:
      case KEY_CBS_DF_XGMI_TXEQ_S1L0P0:
      case KEY_CBS_DF_XGMI_TXEQ_S1L0P1:
      case KEY_CBS_DF_XGMI_TXEQ_S1L0P2:
      case KEY_CBS_DF_XGMI_TXEQ_S1L0P3:
      case KEY_CBS_DF_XGMI_TXEQ_S1L1P0:
      case KEY_CBS_DF_XGMI_TXEQ_S1L1P1:
      case KEY_CBS_DF_XGMI_TXEQ_S1L1P2:
      case KEY_CBS_DF_XGMI_TXEQ_S1L1P3:
      case KEY_CBS_DF_XGMI_TXEQ_S1L2P0:
      case KEY_CBS_DF_XGMI_TXEQ_S1L2P1:
      case KEY_CBS_DF_XGMI_TXEQ_S1L2P2:
      case KEY_CBS_DF_XGMI_TXEQ_S1L2P3:
      case KEY_CBS_DF_XGMI_TXEQ_S1L3P0:
      case KEY_CBS_DF_XGMI_TXEQ_S1L3P1:
      case KEY_CBS_DF_XGMI_TXEQ_S1L3P2:
      case KEY_CBS_DF_XGMI_TXEQ_S1L3P3:
        XgmiTxeqSearchMaskCallback (This, Action, QuestionId, Type, Value, ActionRequest, pSetup_Config, HiiHandle);
        break;
      case KEY_CBS_CMN_MEM_TIMING_TCL_DDR:
        if (Value->u16 & BIT0) {
          if (Value->u16 > pSetup_Config->CbsCmnMemTimingTclDdr) {
            Value->u16++;
          } else if (Value->u16 < pSetup_Config->CbsCmnMemTimingTclDdr) {
            Value->u16--;
          }
        }
        break;
      case KEY_CBS_DF_CMN_MEM_INTLV:
        Status = CbsDfCc6AllocSchmCallback (This, Action, QuestionId, Type, Value, ActionRequest, pSetup_Config, HiiHandle);
        break;
      case KEY_CBS_DBG_FCH_DELAY_SYNCFLOOD:
        if ( ( Value->u8 < 5 ) && ( Value->u8 > 0 ) ){
          //0~4 : treat it as 0 ( disabled)
          Value->u8 = 0;
        }
        break;
      case KEY_CBS_CMN_MEM_CHANNEL_DISABLE_BITMASK_DDR:
      case KEY_CBS_CMN_MEM_SOCKET0_CHANNEL0_DDR:
      case KEY_CBS_CMN_MEM_SOCKET0_CHANNEL1_DDR:
      case KEY_CBS_CMN_MEM_SOCKET0_CHANNEL2_DDR:
      case KEY_CBS_CMN_MEM_SOCKET0_CHANNEL3_DDR:
      case KEY_CBS_CMN_MEM_SOCKET0_CHANNEL4_DDR:
      case KEY_CBS_CMN_MEM_SOCKET0_CHANNEL5_DDR:
      case KEY_CBS_CMN_MEM_SOCKET0_CHANNEL6_DDR:
      case KEY_CBS_CMN_MEM_SOCKET0_CHANNEL7_DDR:
      case KEY_CBS_CMN_MEM_SOCKET0_CHANNEL8_DDR:
      case KEY_CBS_CMN_MEM_SOCKET0_CHANNEL9_DDR:
      case KEY_CBS_CMN_MEM_SOCKET0_CHANNEL10_DDR:
      case KEY_CBS_CMN_MEM_SOCKET0_CHANNEL11_DDR:
      case KEY_CBS_CMN_MEM_SOCKET1_CHANNEL0_DDR:
      case KEY_CBS_CMN_MEM_SOCKET1_CHANNEL1_DDR:
      case KEY_CBS_CMN_MEM_SOCKET1_CHANNEL2_DDR:
      case KEY_CBS_CMN_MEM_SOCKET1_CHANNEL3_DDR:
      case KEY_CBS_CMN_MEM_SOCKET1_CHANNEL4_DDR:
      case KEY_CBS_CMN_MEM_SOCKET1_CHANNEL5_DDR:
      case KEY_CBS_CMN_MEM_SOCKET1_CHANNEL6_DDR:
      case KEY_CBS_CMN_MEM_SOCKET1_CHANNEL7_DDR:
      case KEY_CBS_CMN_MEM_SOCKET1_CHANNEL8_DDR:
      case KEY_CBS_CMN_MEM_SOCKET1_CHANNEL9_DDR:
      case KEY_CBS_CMN_MEM_SOCKET1_CHANNEL10_DDR:
      case KEY_CBS_CMN_MEM_SOCKET1_CHANNEL11_DDR:
        MemoryChannelDisableCallback (QuestionId, pSetup_Config);
        break;

      case KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM_BIT_MASK_DDR:
      case KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM1:
      case KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM2:
      case KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM3:
      case KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM4:
      case KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM5:
      case KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM6:
      case KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM7:
      case KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM8:
      case KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM9:
        MemPmuBistAlgorithmCallback (QuestionId, pSetup_Config);
        break;

      case KEY_CBS_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK0:
      case KEY_CBS_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK1:
      case KEY_CBS_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK2:
      case KEY_CBS_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK3:
      case KEY_CBS_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK0:
      case KEY_CBS_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK1:
      case KEY_CBS_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK2:
      case KEY_CBS_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK3:
        XgmiAcDcCoupledLinkCallback (This, Action, QuestionId, Type, Value, ActionRequest, pSetup_Config, HiiHandle);
        break;

      case KEY_CBS_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK0:
      case KEY_CBS_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK1:
      case KEY_CBS_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK2:
      case KEY_CBS_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK3:
      case KEY_CBS_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK0:
      case KEY_CBS_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK1:
      case KEY_CBS_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK2:
      case KEY_CBS_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK3:
        XgmiChannelTypeCallback (This, Action, QuestionId, Type, Value, ActionRequest, pSetup_Config, HiiHandle);
        break;

      case KEY_CBS_CMN_CPU_SCAN_DUMP_DBG_EN:
        CbsCpuScanDumpDbgCallback (pSetup_Config);
        break;

      default:
        break;
      }

      break;
    case EFI_BROWSER_ACTION_DEFAULT_STANDARD:
      CbsWriteDefalutValue(IfrData);
      CbsAdjustDefaultValue(IfrData);
      CbsComboIdentify (IfrData);
      CbsNumberOfSockets (IfrData);
      switch (QuestionId) {
      default:
        break;
      }
      XgmiGlobalPresetListCallback (This, Action, QuestionId, Type, Value, ActionRequest, pSetup_Config, HiiHandle);
      XgmiInitialPresetCallback (This, Action, QuestionId, Type, Value, ActionRequest, pSetup_Config, HiiHandle);
      XgmiTxeqSearchMaskCallback (This, Action, QuestionId, Type, Value, ActionRequest, pSetup_Config, HiiHandle);
      SocFarLoadDefault (pSetup_Config);
      break;
    default:
      break;
  }

  return Status;
}

VOID
AmdSaveCbsConfigData (
  IN UINT8   *IfrData
  )
{
  EFI_STATUS                      Status;
  UINT32                          EAX_Reg;
  UINT32                          EBX_Reg;
  UINT32                          ECX_Reg;
  UINT32                          EDX_Reg;
  UINT64                          SocFamilyID;
  AMD_APCB_SERVICE_PROTOCOL       *mApcbDxeServiceProtocol = NULL;
  CBS_CONFIG                      *Setup_Config;

  // Update the Variable Hash with the current CBS_CONFIG settings
  Setup_Config = (CBS_CONFIG *) IfrData;
  Setup_Config->Header.ApcbVariableHash = GetApcbHash ((VOID *)Setup_Config, NULL);
  DEBUG ((EFI_D_ERROR, "ApcbTokenHash=0x%x\n", Setup_Config->Header.ApcbVariableHash));

  EAX_Reg = 0;
  AsmCpuid (
      0x80000001,
      &EAX_Reg,
      &EBX_Reg,
      &ECX_Reg,
      &EDX_Reg
      );
  SocFamilyID = EAX_Reg & RAW_FAMILY_ID_MASK;

  if (SocFamilyID != F15_BR_RAW_ID) {
    DEBUG ((EFI_D_ERROR, "Update CBS linked APCB data\n"));
    if (PcdGetBool(PcdLockApcbDxeAfterSmmLock)) {
      //Call the SMM communication handle to call UpdateCbsApcbTokens inside of SMM
      Status = CbsSmmCommUpdateApcb (sizeof(CBS_CONFIG), IfrData);
      DEBUG ((EFI_D_ERROR, "CbsSmmCommUpdateApcb %r\n", Status));
    } else {
      Status = gBS->LocateProtocol (&gAmdApcbDxeServiceProtocolGuid, NULL, (VOID **)&mApcbDxeServiceProtocol);
      if (EFI_ERROR (Status)) {
        DEBUG ((EFI_D_ERROR, "Unable to locate APCB Protocol\n"));
      }
      //Call the auto generated code
      UpdateCbsApcbTokens (IfrData, mApcbDxeServiceProtocol);
    }
  }

  return;
}

COMBO_FLAG_XLAT CbsComboFlagTable[] =
{
  // Family, ExtModel, BaseModel, Stepping, PkgType,      ComboFlag
  {  0x1A,   0x0,      2,         'x',      ZEN5_PKG_SP5, 16      },  // BRH_SP5, C0/C1
  {  0x1A,   0x0,      1,         'x',      ZEN5_PKG_SP5, 14      },  // BRH_SP5, B0/B1
  {  0x1A,   0x0,      0,         'x',      ZEN5_PKG_SP5, 12      },  // BRH_SP5, A0
  {  0x1A,   0x1,      1,         'x',      ZEN5_PKG_SP5, 10      },  // BRHD_SP5, B0
  {  0x1A,   0x1,      0,         'x',      ZEN5_PKG_SP5, 8       },  // BRHD_SP5, A0
  COMBO_FLAG_XLAT_TERMINATOR
};

VOID
CbsComboIdentify (
  IN UINT8   *IfrData
  )
{
  UINT8      RawPkgType;
  UINT8      RawStepping;
  UINT8      RawBaseModel;
  UINT8      RawExtModel;
  UINT16     RawFamily;
  UINT32     EAX_Reg;
  UINT32     EBX_Reg;
  UINT32     ECX_Reg;
  UINT32     EDX_Reg;
  CBS_CONFIG *Setup_Config;
  COMBO_FLAG_XLAT *ComboFlagXlat;

  Setup_Config = (CBS_CONFIG *)IfrData;

  EAX_Reg = 0;
  EBX_Reg = 0;
  AsmCpuid (
      0x80000001,
      &EAX_Reg,
      &EBX_Reg,
      &ECX_Reg,
      &EDX_Reg
      );
  // get Raw CPUID
  RawFamily    = (UINT16) (((EAX_Reg & CPUID_BASE_FAMILY_MASK) >> 8) +
                           ((EAX_Reg & CPUID_EXT_FAMILY_MASK)  >> 20));
  RawBaseModel = (UINT8)   ((EAX_Reg & CPUID_BASE_MODEL_MASK)  >> 4);
  RawExtModel  = (UINT8)   ((EAX_Reg & CPUID_EXT_MODEL_MASK)   >> 16);
  RawStepping  = (UINT8)    (EAX_Reg & CPUID_STEPPING_MASK);
  RawPkgType   = (EBX_Reg >> 28) & 0x0F;

  ComboFlagXlat = &CbsComboFlagTable[0];
  DEBUG ((EFI_D_ERROR, "Family %x, ExtModel %x, BaseModel %x, Stepping %x, PkgType %x, ", RawFamily, RawExtModel, RawBaseModel, RawStepping, RawPkgType));

  while (ComboFlagXlat->ComboFlag != COMBO_FLAG_UNKNOWN) {
    if (((RawFamily    == ComboFlagXlat->RawFamily)    || (ComboFlagXlat->RawFamily    == 'x')) &&
        ((RawExtModel  == ComboFlagXlat->RawExtModel)  || (ComboFlagXlat->RawExtModel  == 'x')) &&
        ((RawBaseModel == ComboFlagXlat->RawBaseModel) || (ComboFlagXlat->RawBaseModel == 'x')) &&
        ((RawStepping  == ComboFlagXlat->RawStepping)  || (ComboFlagXlat->RawStepping  == 'x')) &&
        ((RawPkgType   == ComboFlagXlat->RawPkgType)   || (ComboFlagXlat->RawPkgType   == 'x'))) {
      Setup_Config->CbsComboFlag = ComboFlagXlat->ComboFlag;
      DEBUG ((EFI_D_ERROR, "ComboFlag %x\n", Setup_Config->CbsComboFlag));
      break;
    }
    ComboFlagXlat++;
  }
  Setup_Config->Header.CbsComboChipsetFlag = PcdGet16 (PcdChipsetIdentifiedId);
  DEBUG ((EFI_D_ERROR, "CbsComboChipsetFlag %x\n", Setup_Config->Header.CbsComboChipsetFlag));

}

//This function requires CBS->CbsNumberOfSockets to be implemented
VOID
CbsNumberOfSockets (
  IN UINT8   *IfrData
  )
{
  CBS_CONFIG *Setup_Config;

  Setup_Config = (CBS_CONFIG *)IfrData;

   if (mNumerOfProcessorPresent == 0) {
     mNumerOfProcessorPresent = (UINT8) FabricTopologyGetNumberOfProcessorsPresent ();
   }
  Setup_Config->CbsNumberOfSockets = mNumerOfProcessorPresent;
}

EFI_STATUS
EFIAPI
CbsSetupLoadDefaultFunc (
  )
{
  EFI_STATUS Status;
  CBS_CONFIG *AmdCbsConfig;

  Status = EFI_SUCCESS;
  // Load CBS default value
  AmdCbsConfig = AllocateZeroPool (CbsVariableSize);
  if (AmdCbsConfig != NULL) {
    CbsWriteDefalutValue ((UINT8 *) AmdCbsConfig);
    CbsComboIdentify ((UINT8 *) AmdCbsConfig);
    CbsNumberOfSockets ((UINT8 *) AmdCbsConfig);

    Status = gRT->SetVariable (
                CBS_SYSTEM_CONFIGURATION_NAME,
                &gCbsSystemConfigurationGuid,
                FixedPcdGet32(PcdAmdCbsVariableAttribute),
                CbsVariableSize,
                (VOID *)AmdCbsConfig
                );
    gBS->FreePool (AmdCbsConfig);
  }
  return Status;
}

VOID
MemoryChannelDisableOverride (
    IN OUT CBS_CONFIG   *Setup_Config,
    IN OUT BOOLEAN      *OverrideRequired,
    IN OUT BOOLEAN      *ResetRequired

 )
{
  EFI_STATUS                      Status;
  AMD_APCB_SERVICE_PROTOCOL       *pApcbDxeServiceProtocol;
  UINT8                           ApcbPurpose;
  UINT32                          Apcb32;

  Status = gBS->LocateProtocol (&gAmdApcbDxeServiceProtocolGuid, NULL, (VOID **)&pApcbDxeServiceProtocol);
  if (!EFI_ERROR (Status)) {
    Status = pApcbDxeServiceProtocol->ApcbGetToken32 (pApcbDxeServiceProtocol, &ApcbPurpose, APCB_TOKEN_UID_MEM_CHANNEL_DISABLE_BITMASK_DDR, &Apcb32);
    if (!EFI_ERROR (Status)) {
      if (Setup_Config->CbsCmnMemChannelDisableBitmaskDdr != Apcb32) {
        Setup_Config->CbsCmnMemChannelDisableBitmaskDdr = Apcb32;
        MemoryChannelDisableCallback(KEY_CBS_CMN_MEM_CHANNEL_DISABLE_BITMASK_DDR, Setup_Config);
        *OverrideRequired = TRUE;
      }
    }
  }
}

VOID
MemPmuBistAlgorithmOverride (
    IN OUT CBS_CONFIG   *Setup_Config,
    IN OUT BOOLEAN      *OverrideRequired,
    IN OUT BOOLEAN      *ResetRequired

 )
{
  EFI_STATUS                      Status;
  AMD_APCB_SERVICE_PROTOCOL       *pApcbDxeServiceProtocol;
  UINT8                           ApcbPurpose;
  UINT16                          Apcb16;

  Status = gBS->LocateProtocol (&gAmdApcbDxeServiceProtocolGuid, NULL, (VOID **)&pApcbDxeServiceProtocol);
  if (!EFI_ERROR (Status)) {
    Status = pApcbDxeServiceProtocol->ApcbGetToken16 (pApcbDxeServiceProtocol, &ApcbPurpose, APCB_TOKEN_UID_MEM_PMU_BIST_ALGORITHM_BITMASK_DDR, &Apcb16);
    if (!EFI_ERROR (Status)) {
      if (Setup_Config->CbsCmnMemPmuBistAlgorithmBitMaskDdr != Apcb16) {
        Setup_Config->CbsCmnMemPmuBistAlgorithmBitMaskDdr = Apcb16;
        MemPmuBistAlgorithmCallback (KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM_BIT_MASK_DDR, Setup_Config);
        *OverrideRequired = TRUE;
      }
    }
  }
}

VOID
MemHealingBistExecutionModeOverride (
    IN OUT CBS_CONFIG   *Setup_Config,
    IN OUT BOOLEAN      *OverrideRequired,
    IN OUT BOOLEAN      *ResetRequired

 )
{
  if ((Setup_Config->CbsCmnMemHealingBistExecutionMode == 0) && (Setup_Config->CbsCmnMemHealingBistEnableBitMaskDdr)) {   //One Time
    DEBUG ((EFI_D_ERROR, "DDR Healing BIST is enabled. Reset system and disable it on the next boot.\n"));
    Setup_Config->CbsCmnMemHealingBistEnableBitMaskDdr = 0;
    *ResetRequired = TRUE;
    *OverrideRequired = TRUE;
  }
}

VOID
SocFarOverride (
    IN OUT CBS_CONFIG   *Setup_Config,
    IN OUT BOOLEAN      *OverrideRequired,
    IN OUT BOOLEAN      *ResetRequired

 )
{
  SocFarInitData ();
  if (SocFarLoadDefault (Setup_Config)) {
    *OverrideRequired = TRUE;
  }
}

BOOLEAN
OverrideCbsVariable (
  IN UINT8                      *IfrData,
  IN AMD_APCB_SERVICE_PROTOCOL  *pApcbDxeServiceProtocol
  )
{
  CBS_CONFIG    *Setup_Config;
  BOOLEAN       OverrideRequired = FALSE;
  BOOLEAN       ResetRequired = FALSE;

  Setup_Config = (CBS_CONFIG *)IfrData;

  MemoryChannelDisableOverride(Setup_Config, &OverrideRequired, &ResetRequired);
  MemPmuBistAlgorithmOverride (Setup_Config, &OverrideRequired, &ResetRequired);
  MemHealingBistExecutionModeOverride (Setup_Config, &OverrideRequired, &ResetRequired);
  SocFarOverride(Setup_Config, &OverrideRequired, &ResetRequired);

  if (OverrideRequired) {
    Setup_Config->Header.ApcbVariableHash = GetApcbHash ((VOID *)Setup_Config, pApcbDxeServiceProtocol);
    gRT->SetVariable (
       CBS_SYSTEM_CONFIGURATION_NAME,
       &gCbsSystemConfigurationGuid,
       FixedPcdGet32(PcdAmdCbsVariableAttribute),
       CbsVariableSize,
       (VOID *)Setup_Config
       );
  }

  return ResetRequired;
}

VOID
InitializeCbsApcbDefault (
  IN UINT8   *IfrData
  )
{
  EFI_STATUS                      Status;
  CBS_CONFIG                      *Setup_Config;
  AMD_APCB_SERVICE_PROTOCOL       *pApcbDxeServiceProtocol;
  UINT32                          ApcbSyncSignature;
  UINT32                          NewSignature;
  UINT8                           ApcbPurpose;
  UINT32                          Apcb32;
  BOOLEAN                         ResetRequired = FALSE;

  pApcbDxeServiceProtocol = NULL;
  ApcbSyncSignature = 0;
  NewSignature = 0;
  ApcbPurpose = 0;
  Setup_Config = (CBS_CONFIG *)IfrData;

  Status = gBS->LocateProtocol (&gAmdApcbDxeServiceProtocolGuid, NULL, (VOID **)&pApcbDxeServiceProtocol);
  if (!EFI_ERROR (Status)) {
    // if APCB token (APCB_TOKEN_UID_CBS_SYNC_SIGNATURE exists
    //   1. generate CBS-APCB sync hash and check If APCB_TOKEN_UID_CBS_SYNC_SIGNATURE match between APCB setting and new generated hash from CBS setting
    //      do nothing, CBS and APCB are in sync.
    //   2. If hash value does not match, then the call made to generate hash should already done the APCB token sync
    //      write the new hash value back to APCB token APCB_TOKEN_UID_CBS_SYNC_SIGNATURE
    //      call UpdateCbsApcbTokens sync CBS setting to APCB
    // else
    //   if APCB token APCB_TOKEN_UID_CBS_SYNC_SIGNATURE does not exist then
    //     1. CBS script auto generated code to make the hash, write the hash value to APCB token APCB_TOKEN_UID_CBS_SYNC_SIGNATURE
    //     2. call UpdateCbsApcbTokens sync CBS setting to APCB
    ResetRequired = OverrideCbsVariable (IfrData, pApcbDxeServiceProtocol);
    Status = pApcbDxeServiceProtocol->ApcbGetToken32 (pApcbDxeServiceProtocol, &ApcbPurpose, APCB_TOKEN_UID_CBS_SYNC_SIGNATURE, &ApcbSyncSignature);
    DEBUG ((EFI_D_ERROR, "Status = 0x%x, InitApcb: Header.ApcbVariableHash=0x%x, ApcbTokenHash=0x%x\n", Status, Setup_Config->Header.ApcbVariableHash, ApcbSyncSignature));
    NewSignature = GetApcbHash ((VOID *)Setup_Config, pApcbDxeServiceProtocol);
    DEBUG ((EFI_D_ERROR, "new ApcbTokenHash=0x%x\n", NewSignature));
    if ((EFI_ERROR (Status)) || (Setup_Config->Header.ApcbVariableHash != ApcbSyncSignature)) {
      Setup_Config->Header.ApcbVariableHash = NewSignature;
      //Call the auto generated code to update APCB token.
      UpdateCbsApcbTokens ((VOID *)Setup_Config, pApcbDxeServiceProtocol);
    }
    if (ResetRequired) {
      gRT->ResetSystem (EfiResetWarm, EFI_SUCCESS, 0, NULL);
    }
    Status = pApcbDxeServiceProtocol->ApcbGetToken32 (pApcbDxeServiceProtocol, &ApcbPurpose, APCB_TOKEN_UID_DF_XGMI_INIT_PRESET_DEFAULT, &Apcb32);
    if (Status == EFI_SUCCESS) {
      XgmiInitPreset = Apcb32;
    }
  } else {
    DEBUG ((EFI_D_ERROR, "Unable to locate APCB Protocol\n"));
  }

  return;
}

EFI_STATUS
AmdCbsFormDynamicUpdate (
  IN  CONST EFI_HII_CONFIG_ACCESS_PROTOCOL   *This,
  IN  EFI_BROWSER_ACTION                     Action,
  IN  EFI_QUESTION_ID                        QuestionId,
  IN  UINT8                                  Type,
  IN  EFI_IFR_TYPE_VALUE                     *Value,
  OUT EFI_BROWSER_ACTION_REQUEST             *ActionRequest,
  IN  UINT8                                  *IfrData,
  IN  EFI_HII_HANDLE                         HiiHandle
  )
{
  EFI_STATUS Status = EFI_SUCCESS;
  CBS_CONFIG *Setup_Config;

  Setup_Config = (CBS_CONFIG *)IfrData;

  switch (Action) {
    case EFI_BROWSER_ACTION_FORM_OPEN:
      ApicModeOptionsChanged (EFI_BROWSER_ACTION_FORM_OPEN, Setup_Config);
      CbsCpuCcdCtrlUpdate (HiiHandle);
      CbsCpuCoreCtrlUpdate (HiiHandle);
      CbsCpuSmtCtrlUpdate (HiiHandle);
      CbsDfNumOfSegsUpdate (HiiHandle);
      Status = CbsDfNpsCallback (This, Action, QuestionId, Type, Value, ActionRequest, Setup_Config, HiiHandle);
      Status = CbsDfCc6AllocSchmCallback (This, Action, QuestionId, Type, Value, ActionRequest, Setup_Config, HiiHandle);
      break;
    default:
      break;
  }

  return Status;
}

EFI_STATUS
CbsDfNpsCallback (
    IN  CONST EFI_HII_CONFIG_ACCESS_PROTOCOL  *This,
    IN  EFI_BROWSER_ACTION                                    Action,
    IN  EFI_QUESTION_ID                                            QuestionId,
    IN  UINT8                                                               Type,
    IN  EFI_IFR_TYPE_VALUE                                        *Value,
    OUT EFI_BROWSER_ACTION_REQUEST                   *ActionRequest,
    OUT CBS_CONFIG                                                  *pSetup_Config,
    IN  EFI_HII_HANDLE                                                HiiHandle
  )
{
   EFI_STATUS Status = EFI_SUCCESS;

   // Get APOB NPS Info.
   if (mApobSysNps == NULL) {
     Status = AmdPspGetApobEntryInstance (APOB_FABRIC, APOB_SYS_NPS_INFO_TYPE, 0, FALSE, (APOB_TYPE_HEADER **)&mApobSysNps);
     if (EFI_ERROR (Status)) {
       DEBUG ((EFI_D_ERROR, "%a: Fail to locate APOB_SYS_NPS_INFO_TYPE\n", __FUNCTION__));
       return Status;
     }
   }

   // Get numboer of processor present
   if (mNumerOfProcessorPresent == 0) {
     mNumerOfProcessorPresent = (UINT8) FabricTopologyGetNumberOfProcessorsPresent ();
   }

   switch (Action) {
     case EFI_BROWSER_ACTION_FORM_OPEN:
      {
         VOID *StartOpCodeHandle = NULL;
         VOID *EndOpCodeHandle = NULL;
         VOID *OptionOpCodeHandle = NULL;
         UINT8 NpsIndex;

         //DEBUG ((EFI_D_INFO, __FUNCTION__": Executed, Action: %d, QuestionId: 0x%x\n", Action, QuestionId));
         //
         // Create item and options dynamically
         //
         // Create HII extended label OP code handles
         if (AmdHiiCreateExtendedLabelOpCode (&StartOpCodeHandle, &EndOpCodeHandle, LABEL_CBS_CBS_DF_CMN_DRAM_NPS_START, LABEL_CBS_CBS_DF_CMN_DRAM_NPS_END)) {
           // Create Option OP code handle
           OptionOpCodeHandle = HiiAllocateOpCodeHandle ();
           if (OptionOpCodeHandle != NULL) {
             // NPS0 option if >= 2P & MinAllowableNps < 1
             if ((mNumerOfProcessorPresent > 1) && (mApobSysNps->MinAllowableNps < 1)) {
               HiiCreateOneOfOptionOpCode (
                 OptionOpCodeHandle,
                 STRING_TOKEN(AMD_CBS_STR_NPS0),
                 0,
                 EFI_IFR_NUMERIC_SIZE_1,
                 IDSOPT_DF_CMN_DRAM_NPS_NPS0
                 );
             }

             // NPS1, NPS2 and NPS4 options based on MaxAllowableNps
             NpsIndex = 1;
             do {
               HiiCreateOneOfOptionOpCode (
                 OptionOpCodeHandle,
                 STRING_TOKEN(AMD_CBS_STR_NPS0 + NpsIndex),
                 0,
                 EFI_IFR_NUMERIC_SIZE_1,
                 (IDSOPT_DF_CMN_DRAM_NPS_NPS0 + NpsIndex)
                 );
               NpsIndex++;
             } while (NpsIndex <= mApobSysNps->MaxAllowableNps);

             // Auto option
             HiiCreateOneOfOptionOpCode (
                OptionOpCodeHandle,
                STRING_TOKEN(AMD_CBS_STR_AUTO),
                (EFI_IFR_OPTION_DEFAULT | EFI_IFR_OPTION_DEFAULT_MFG),
                EFI_IFR_NUMERIC_SIZE_1,
                IDSOPT_DF_CMN_DRAM_NPS_AUTO
                );

             // Create OneOfOp code (item)
             HiiCreateOneOfOpCode (StartOpCodeHandle,
               (EFI_QUESTION_ID) KEY_CBS_DF_CMN_DRAM_NPS,
               CBS_CONFIGURATION_VARSTORE_ID,
               (UINT16) OFFSET_OF (CBS_CONFIG, CbsDfCmnDramNps),
               STRING_TOKEN (AMD_CBS_STR_NUMA_NODES_PER_SOCKET),
               STRING_TOKEN (AMD_CBS_STR_NUMA_NODES_PER_SOCKET_HELP),
               (UINT8) (EFI_IFR_FLAG_CALLBACK | EFI_IFR_FLAG_RESET_REQUIRED),
               (UINT8) EFI_IFR_NUMERIC_SIZE_1,
               OptionOpCodeHandle,
               NULL
               );

             Status = HiiUpdateForm (
               HiiHandle,
               &mFormSetGuidBrh,
               SETUP_MEMORY_ADDRESSING_LABEL,
               StartOpCodeHandle,
               EndOpCodeHandle
               );

             HiiFreeOpCodeHandle (StartOpCodeHandle);
             HiiFreeOpCodeHandle (EndOpCodeHandle);
             HiiFreeOpCodeHandle (OptionOpCodeHandle);
           } else {
             Status = EFI_UNSUPPORTED;
           }
         } else {
           Status = EFI_UNSUPPORTED;
         }
      }
      break;

 #if 0
     case EFI_BROWSER_ACTION_RETRIEVE:
      {

         if (QuestionId != KEY_CBS_DF_CMN_DRAM_NPS)
           return EFI_UNSUPPORTED;

         DEBUG ((EFI_D_INFO, __FUNCTION__": Executed, Action: %d, QuestionId: 0x%x\n", Action, QuestionId));

         //
         // Adjust broswer data against absent option if needed
         //
         if ((Value->u8 > mApobSysNps->MaxAllowableNps) ||
            ((mNumerOfProcessorPresent == 1) && (Value->u8 == IDSOPT_DF_CMN_DRAM_NPS_NPS0))
            ) {
           //pSetup_Config->CbsDfCmnDramNps = IDSOPT_DF_CMN_DRAM_NPS_AUTO;
          DEBUG ((EFI_D_INFO, __FUNCTION__": NPS force to Auto!\n"));
          Value->u8 = IDSOPT_DF_CMN_DRAM_NPS_AUTO;
         }
      }
      break;
#endif

     default:
      break;
   }

   //DEBUG ((EFI_D_INFO, __FUNCTION__": Exit, Status: %r\n", Status));

   return Status;
}

VOID
CbsDfNpsAdjustVarValue (
    IN OUT CBS_CONFIG    *Setup_Config
 )
{
   EFI_STATUS Status = EFI_SUCCESS;

   // Get APOB NPS Info.
   if (mApobSysNps == NULL) {
     Status = AmdPspGetApobEntryInstance (APOB_FABRIC, APOB_SYS_NPS_INFO_TYPE, 0, FALSE, (APOB_TYPE_HEADER **)&mApobSysNps);
     if (EFI_ERROR (Status)) {
       DEBUG ((EFI_D_ERROR, "%a: Fail to locate APOB_SYS_NPS_INFO_TYPE\n", __FUNCTION__));
       return;
     }
   }

   // Get numboer of processor present
   if (mNumerOfProcessorPresent == 0) {
     mNumerOfProcessorPresent = (UINT8) FabricTopologyGetNumberOfProcessorsPresent ();
   }

   //
   // Adjust variable data against unallowable option if needed
   //
   if ((Setup_Config->CbsDfCmnDramNps > mApobSysNps->MaxAllowableNps) ||
      ((mNumerOfProcessorPresent == 1) && (Setup_Config->CbsDfCmnDramNps == IDSOPT_DF_CMN_DRAM_NPS_NPS0))
      ) {
     Setup_Config->CbsDfCmnDramNps = IDSOPT_DF_CMN_DRAM_NPS_AUTO;
   }
}

EFI_STATUS
CbsDfCc6AllocSchmCallback (
 IN  CONST EFI_HII_CONFIG_ACCESS_PROTOCOL  *This,
 IN  EFI_BROWSER_ACTION                     Action,
 IN  EFI_QUESTION_ID                        QuestionId,
 IN  UINT8                                  Type,
 IN  EFI_IFR_TYPE_VALUE                    *Value,
 OUT EFI_BROWSER_ACTION_REQUEST            *ActionRequest,
 OUT CBS_CONFIG                            *pSetup_Config,
 IN  EFI_HII_HANDLE                         HiiHandle
 )
{
  EFI_STATUS Status = EFI_SUCCESS;
  VOID *OptionOpCodeHandle = NULL;
  VOID *StartOpCodeHandle = NULL;
  VOID *EndOpCodeHandle = NULL;

  if ((Action != EFI_BROWSER_ACTION_FORM_OPEN) && (Action != EFI_BROWSER_ACTION_CHANGED)) return EFI_UNSUPPORTED;

  //DEBUG ((EFI_D_INFO, __FUNCTION__": Executed, Action: %d, QuestionId: 0x%x\n", Action, QuestionId));
  // Create HII extended label OP code handles
  if (AmdHiiCreateExtendedLabelOpCode (&StartOpCodeHandle, &EndOpCodeHandle, LABEL_CBS_CBS_DF_CMN_CC6_ALLOCATION_SCHEME_START, LABEL_CBS_CBS_DF_CMN_CC6_ALLOCATION_SCHEME_END)) {
    // Create Option OP code handle
    OptionOpCodeHandle = HiiAllocateOpCodeHandle ();
    if (OptionOpCodeHandle != NULL) {
      // Distributed option
      HiiCreateOneOfOptionOpCode (
        OptionOpCodeHandle,
        STRING_TOKEN(AMD_CBS_STR_DISTRIBUTED),
        0,
        EFI_IFR_NUMERIC_SIZE_1,
        IDSOPT_DF_CMN_CC6_ALLOCATION_SCHEME_DISTRIBUTED
        );

      // Consolidated option
      HiiCreateOneOfOptionOpCode (
        OptionOpCodeHandle,
        STRING_TOKEN(AMD_CBS_STR_CONSOLIDATED),
        0,
        EFI_IFR_NUMERIC_SIZE_1,
        IDSOPT_DF_CMN_CC6_ALLOCATION_SCHEME_CONSOLIDATED
        );

      if ((pSetup_Config->CbsDfCmnMemIntlv == IDSOPT_DF_CMN_MEM_INTLV_DISABLED) ||
          (pSetup_Config->CbsDfCmnDramNps == IDSOPT_DF_CMN_DRAM_NPS_NPS4)) {
        // Consolidated at 1st DRAM pair option
        HiiCreateOneOfOptionOpCode (
          OptionOpCodeHandle,
          STRING_TOKEN(AMD_CBS_STR_CONSOLIDATED_TO_1ST_DRAM_PAIR),
          0,
          EFI_IFR_NUMERIC_SIZE_1,
          IDSOPT_DF_CMN_CC6_ALLOCATION_SCHEME_CONSOLIDATEDTO1STDRAMPAIR
          );
      }

      // Auto option
      HiiCreateOneOfOptionOpCode (
        OptionOpCodeHandle,
        STRING_TOKEN(AMD_CBS_STR_AUTO),
        (EFI_IFR_OPTION_DEFAULT | EFI_IFR_OPTION_DEFAULT_MFG),
        EFI_IFR_NUMERIC_SIZE_1,
        IDSOPT_DF_CMN_CC6_ALLOCATION_SCHEME_AUTO
        );

      // Create OneOfOp code (item)
      HiiCreateOneOfOpCode (StartOpCodeHandle,
       (EFI_QUESTION_ID) KEY_CBS_DF_CMN_CC6_ALLOCATION_SCHEME,
       CBS_CONFIGURATION_VARSTORE_ID,
       (UINT16) OFFSET_OF (CBS_CONFIG, CbsDfCmnCc6AllocationScheme),
       STRING_TOKEN (AMD_CBS_STR_LOCATION_OF_PRIVATE_MEMORY_REGIONS),
       STRING_TOKEN (AMD_CBS_STR_LOCATION_OF_PRIVATE_MEMORY_REGIONS_HELP),
       (UINT8) (EFI_IFR_FLAG_CALLBACK | EFI_IFR_FLAG_RESET_REQUIRED),
       (UINT8) EFI_IFR_NUMERIC_SIZE_1,
       OptionOpCodeHandle,
       NULL
       );
    }

    Status = HiiUpdateForm (
     HiiHandle,
     &mFormSetGuidBrh,
     SETUP_MEMORY_ADDRESSING_LABEL,
     StartOpCodeHandle,
     EndOpCodeHandle
     );

    HiiFreeOpCodeHandle (StartOpCodeHandle);
    HiiFreeOpCodeHandle (EndOpCodeHandle);
    if (OptionOpCodeHandle != NULL) {
      HiiFreeOpCodeHandle (OptionOpCodeHandle);
    }
  }

  //DEBUG ((EFI_D_INFO, __FUNCTION__": Exit, Status: %r\n", Status));

   return Status;
}

EFI_STATUS
CbsCpuScanDumpDbgCallback (
 OUT CBS_CONFIG *pSetup_Config
  )
{

  // Adjust Setup options accordingly
  if (pSetup_Config->CbsCmnCpuScanDumpDbgEn == 1) {
    pSetup_Config->CbsCmnCpuSmuPspDebugMode = 3; //Auto
    pSetup_Config->CbsDfCmnExtIpSyncFloodProp =0xFF; //Auto
    pSetup_Config->CbsDbgFchSyncfloodEnable = 0xF; //Auto
  }

  return EFI_SUCCESS;
}
