//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************

/** @file EarlyConsoleInclude.h
    Definitions used by EarlyConsole Driver.

**/

#ifndef _EARLY_CONSOLE_INCLUDE_H_
#define _EARLY_CONSOLE_INCLUDE_H_

#define AMI_VIDEO_PARAMETERS_DATA_HOB_GUID \
    {0x19d46905, 0x7eb5, 0x4247, {0x9b, 0xbb, 0xe5, 0xca, 0x52, 0x1c, 0x48, 0x7e}}

#pragma pack (push, 1)
typedef struct {
    UINT16  Bus;
    UINT8   Dev;
    UINT8   Func;
    UINT64  Segment;
} DEV_PATH;

typedef struct {
    UINT8   *FontMap;
    UINT8   StartChar;
    UINT16  CharCount;
    UINT8   CharSize;
    UINT8   TargetTable;
} FONT_MAP_INFO;

typedef struct {
    UINT16                  RootBus;            //Bus Number of the root bus containing the VGA controller.
    UINT8                   DevPathEntries;     //Count of entries in the preceeding buffer.
    UINT32                  PciExpressCfgBase;  //The base address of PCI Express Memory Mapped Configuration Space.
    UINT32                  MemBase;            //The base address of the region where MMIO BARs are assigned when configuring the VGA controller
    UINT32                  MemSizeMax;         //The maximum amount of memory to allow during BAR assignemnt.
    UINT16                  IoBase;             //The base address of the region where IO BARs are assigned when configuring the VGA controller
    UINT16                  IoSizeMax;          //The maximum amount of IO to allow during BAR assignment.
    EFI_PHYSICAL_ADDRESS    FontMap;            //Pointer to a buffer of font map information structures that define the font map(s) to load.
    UINT8                   FontMapCount;       //Count of entries in the preceeding buffer.
    EFI_PHYSICAL_ADDRESS    PeiServices;
    EFI_PHYSICAL_ADDRESS    DevPath;            //Pointer to a buffer of dev path structure that define the location of the video controller in the PCI topology
    UINT32                  Mode;
    UINT32                  DisplayResolution;
} VIDEO_PARAMETERS;

#pragma pack (pop)

#define PCI_IOBASE                      0x001C  // I/O base Register
#define PCI_IOLIMIT                     0x001D  // I/O Limit Register
#define PCI_MEMBASE                     0x0020  // Memory Base Register
#define PCI_MEMLIMIT                    0x0022  // Memory Limit Register
#define PCI_IOBASE_U                    0x0030        // I/O base Upper Register
#define PCI_IOLIMIT_U                   0x0032        // I/O Limit Upper Register
#define PCI_PRE_MEMBASE                 0x0024        // Prefetchable memory Base register
#define PCI_PRE_MEMLIMIT                0x0026        // Prefetchable memory Limit register
#define PCI_PRE_MEMBASE_U               0x0028        // Prefetchable memory base upper 32 bits
#define PCI_PRE_MEMLIMIT_U              0x002C        // Prefetchable memory limit upper 32 bits

#define EARLY_CONSOLE_TEXT_MODE                 0x00
#define EARLY_CONSOLE_GRAPHICS_MODE             0x01
#define EARLY_CONSOLE_MAX_MODE                  0x02

#define EARLY_CONSOLE_GRAPHICS_RES_640x480      0x00
#define EARLY_CONSOLE_GRAPHICS_RES_800x600      0x01
#define EARLY_CONSOLE_GRAPHICS_RES_1024x768     0x02
#define EARLY_CONSOLE_GRAPHICS_RES_1280x1024    0x03
#define EARLY_CONSOLE_GRAPHICS_RES_1600x1200    0x04
#define EARLY_CONSOLE_GRAPHICS_RES_MAX          0x05



/** @internal
    Initialize platform Vga controller.

    @param[in]  parameters  VGA parameter table.
    @param[in]       vBus        VGA device bus number

    @retval EFI_SUCCESS VGA device are successfully initialized.
    @retval Others      Return status

*/
typedef EFI_STATUS (PLATFORM_VGA_INIT_INTERFACE) (
  IN VIDEO_PARAMETERS    *parameters,
  IN UINT8               vBus
);

#endif
