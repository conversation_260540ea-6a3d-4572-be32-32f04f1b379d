## @file
#  Cryptographic Library Instance for DXE_DRIVER.
#
#  Caution: This module requires additional review when modified.
#  This library will have external input - signature.
#  This external input must be validated carefully to avoid security issues such as
#  buffer overflow or integer overflow.
#
#  Copyright (c) 2023, Intel Corporation. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = BaseCryptLib
  FILE_GUID                      = 693C5308-AF95-4CE5-ADE9-CA011C2FC642
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = BaseCryptLib|DXE_DRIVER DXE_CORE UEFI_APPLICATION UEFI_DRIVER

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 ARM AARCH64
#

[Sources]
  InternalCryptLib.h
  Hash/CryptMd5.c
  Hash/CryptSha1.c
  Hash/CryptSha256.c
  Hash/CryptSha512.c
  Hash/CryptParallelHashNull.c
  Hash/CryptSm3.c
  Hmac/CryptHmac.c
  Kdf/CryptHkdf.c
  Cipher/CryptAes.c
  Cipher/CryptAeadAesGcm.c
  Pk/CryptRsaBasic.c
  Pk/CryptRsaExt.c
  Pk/CryptPkcs1Oaep.c
  Pk/CryptPkcs5Pbkdf2.c
  Pk/CryptPkcs7Sign.c
  Pk/CryptPkcs7VerifyCommon.c
  Pk/CryptPkcs7VerifyBase.c
  Pk/CryptPkcs7VerifyEku.c
  Pk/CryptDhNull.c
  Pk/CryptX509.c
  Pk/CryptAuthenticode.c
  Pk/CryptTs.c
  Pk/CryptRsaPss.c
  Pk/CryptRsaPssSign.c
  Pk/CryptEcNull.c
  Pem/CryptPem.c
  Bn/CryptBnNull.c
  Rand/CryptRand.c

  SysCall/CrtWrapper.c
  #SysCall/DummyOpensslSupport.c #APTIOV OVERRIDE to avoid - abort,fclose,isspace.. already defined in CrtWrapper.c
  SysCall/BaseMemAllocation.c
  SysCall/TimerWrapper.c

[Packages]
  MdePkg/MdePkg.dec
  CryptoPkg/CryptoPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  MemoryAllocationLib
  UefiRuntimeServicesTableLib
  DebugLib
  MbedTlsLib
  OpensslLib
  PrintLib
  IntrinsicLib
  RngLib
  SynchronizationLib
[Protocols]
  gEfiMpServiceProtocolGuid
#
# Remove these [BuildOptions] after this library is cleaned up
#
[BuildOptions]
  MSFT:*_*_*_CC_FLAGS = /GL-
