#***********************************************************************
#*                                                                     *
#*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
#*                                                                     *
#*      All rights reserved. Subject to AMI licensing agreement.       *
#*                                                                     *
#***********************************************************************
## @file
#  This file provide the LIB module part for the Video Initialization 
##
[Defines]
  INF_VERSION       = 0x00010005
  VERSION_STRING    = 1.0
  BASE_NAME         = PeiVideoInitLib
  LIBRARY_CLASS     = PeiVideoInitLib
  MODULE_TYPE       = PEIM
  
[Sources]
  PeiVideoInit.c
  
[Packages]
  MdePkg/MdePkg.dec
  AmiModulePkg/AmiModulePkg.dec
  AmiModulePkg/EarlyConsoleOut/AmiEarlyConsoleOutPkg.dec 
 
[Ppis]
   gVideoInitDonePpiGuid
   
[LibraryClasses]
  DebugLib
  PciLib
  PlatformVideoInitLib
  PeiServicesLib
  PciSegmentLib
  $(VIDEO_INIT_LIB)

[Pcd]
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdEarlyConsoleVideoMode
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdEarlyConsoleVideoDisplayResolution