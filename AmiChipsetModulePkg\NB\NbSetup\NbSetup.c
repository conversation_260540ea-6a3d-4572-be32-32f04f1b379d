//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************

/** @file NbSetup.c
    North Bridge Setup Rountines

**/

#include <Setup.h>
#include <AmiDxeLib.h>
#include <Protocol/AmiCpuInfo.h>
#include <AmiHobs.h>
#include <Protocol/AmiSmbios.h>
#include <Protocol/Smbios.h>
#include <Library/DebugLib.h>
#include <Library/PciLib.h>
#include <Library/PrintLib.h>
#include <AGESA.h>
#include <Protocol/FabricTopologyServices2.h>
#include <Protocol/AmdApcbProtocol.h>
#include <Protocol/ApobCommonServiceProtocol.h>
#include <Library/AmdSocBaseLib.h>
#include <Library/AmdPspApobLib.h>
#include <Addendum/Apcb/Inc/BRH/APOB.h>
#include <Library/ApobCommonServiceLib.h>
#include <mtspd5.h>

// CPU
#define MSR_SYS_CFG     0x0C0010010
#define MSR_TOP_MEM     0x0C001001A
#define MSR_TOP_MEM2    0x0C001001D

#define SMUFUSE_NONSECURE_BASEADDR    0x03820000    //Non Secure SMU FUSE SMN Public Base Address
#define SMUFUSE_S2_REPLACE_START      0x000A02BC    //FUSE_NON_SECURE replace start address for S2 region --FUSE_DATA_175
                                                    //MPASPPRIVATEx000A02BC (SMU::FUSE::FUSE_DATA_175)
#define SMUFUSE_UMC_CTRL_0_HARVEST    0x000A081C    //UMC Harvest Fuse --FUSE_DATA_519
                                                    //MPASPPRIVATEx000A081C (SMU::FUSE::FUSE_DATA_519)
#define SMUFUSE_UMCHARVEST_PUBLICADDR  (SMUFUSE_NONSECURE_BASEADDR + SMUFUSE_UMC_CTRL_0_HARVEST - SMUFUSE_S2_REPLACE_START)    //UMC Harvest Fuse Public SMN Address

//
// Memory Topology
//
#define SINGLE_RANK     1
#define DUAL_RANK       2
#define QUAD_RANK       4
#define RANK_8R         8

#define X4_IOWIDTH      4
#define X8_IOWIDTH      8
#define X16_IOWIDTH     16

#define STR_SIZE        0x32

/*
//AMICSPLib Cpu
UINT64
ReadMsr (
    UINT32  Msr
    );

VOID
WriteMsr (
    UINT32  Msr,
    UINT64  Value
    );
*/
#define DIE_PER_SCKT 4
// External data definitions
EFI_GUID gEfiSetupGuid = SETUP_GUID;

//----------------------------------------------------------------------------
EFI_PHYSICAL_ADDRESS  TotalMemorySize = 0;
EFI_PHYSICAL_ADDRESS  SystemRam = 0;

#define NB_SMN_INDEX_EXT_3_BIOS  0x00C0
#define NB_SMN_INDEX_3_BIOS  0x00C4
#define NB_SMN_DATA_3_BIOS   0x00C8

VOID
NbSmnRead (
    IN  UINT32    DieNum,
    IN  UINT32    Address,
    IN  UINT32    *Value )
{
    UINT32 read_value = 0;

    PciWrite32(NB_SMN_INDEX_EXT_3_BIOS, DieNum);
    PciWrite32(NB_SMN_INDEX_3_BIOS, Address);
    read_value = PciRead32(NB_SMN_DATA_3_BIOS);
    *Value=read_value;
    PciWrite32(NB_SMN_INDEX_EXT_3_BIOS, 0);
}

/**
    Return TOM2 MSR value or zero.

    @param VOID

    @retval UINT64 TOM2 value
**/

UINT64
GetTom2 (
    IN  VOID )
{
    return (AsmReadMsr64(MSR_SYS_CFG)&BIT21) ? AsmReadMsr64(MSR_TOP_MEM2) : 0;
}

/**
    Return TOM MSR value.

    @param VOID

    @retval UINT64 TOM value
**/

UINT64
GetTom (
    IN  VOID )
{
    return AsmReadMsr64(MSR_TOP_MEM);
}

/**
    This performs word-by-word copy of a Unicode string.

    @param Destination - Pointer to a CHAR16 string buffer to receive the
                         contents
    @param Source - Pointer to a null-terminated CHAR16 string to be copied.

    @return VOID

    @note
    Assumes the destination string is large enough (error checking must
    be made by caller).
**/

VOID
StrCpy16 (
  IN CHAR16   *Destination,
  IN CHAR16   *Source )
{
    while (*Source) {
        *(Destination++) = *(Source++);
    }
    *Destination = 0;
}

/**
    This is a worker function to extract the SMBIOS string from the
    specified SMBIOS structure data.

    @param StructureHandle - SMBIOS structure data pointer.
    @param StringNumber - The string number to get.
    @param StringData - The string is copied here.

    @return EFI_STATUS
    @retval EFI_SUCCESS or valid error code.

    @note
    This function is called by GetSmbiosDeviceString.
    Assumes the string is large enough (error checking must be made by caller).
**/

EFI_STATUS
GetSmbiosStringByNumber (
    IN  UINT8    *StructureHandle,
    IN  UINT8    StringNumber,
    OUT UINT8    **StringData )
{
    *StringData = StructureHandle +
                ((SMBIOS_STRUCTURE_HEADER*)StructureHandle)->Length;

    while (--StringNumber) {
        while(**StringData != 0) {
            (*StringData)++;
        }
        (*StringData)++;
    }

    return EFI_SUCCESS;
}
/**
    Simple utility function to convert an ASCII string to Unicode.

    @param UnicodeStr - Converted string.
    @param AsciiStr - String to be converted

    @return CHAR16 * Same as UnicodeStr to allow use in an assignment.

    @note  N/A
**/

CHAR16 *
Ascii2Unicode (
  OUT CHAR16    *UnicodeStr,
  IN  CHAR8     *AsciiStr )
{
    CHAR16  *Str;

    Str = UnicodeStr;

    while (TRUE) {
        *(UnicodeStr++) = (CHAR16) *AsciiStr;
        if (*(AsciiStr++) == '\0') {
            return Str;
        }
    }
}

/**
    This function to get DIMM Rank, Type and IoWidth details

    @param[in]  CurChannel       - Translated Channel Id
    @param[in]  PhysicalDimmInfo - Pointer to the physical DIMM info
    @param[out] DimmString1      - Final string value to store DIMM Rank, Type and IoWidth details

    @retval     None
**/
VOID
GetDimmInfo(
UINT8 CurChannel, APOB_MEM_DMI_PHYSICAL_DIMM *PhysicalDimmInfo, CHAR16 *DimmString1)
{
	EFI_STATUS            ApobStatus;
	UINT8                 Socket;
	UINT8                 Channel;
	UINT8                 Dimm;
	UINT16                Instance;
	UINT16                DieLoop;
	UINT8                 DimmSpd[1024];
	SPD_BASE_CONFIG_0_S   *BaseConfig0;
	SPD_ANNEX_COMMON_S    *ModuleParms;
	UINT8                 Rank, NumRanks;
	BOOLEAN               Asymetric;
	CHAR16                DimmString2[STR_SIZE];
	UINT8                 DimmType =0, IoWidth=0;
	UINT8                 TranslatedChannel;
	UINT8                 ChannelXTable[12][2]={/*{Translated, Requested}*/
	                                           {2,0},{4,1},{5,2},{0,3},
	                                           {1,4},{3,5},{8,6},{10,7},
	                                           {11,8},{6,9},{7,10},{9,11}};
	
	// To find the requested channel Id
	for(TranslatedChannel=0;TranslatedChannel<12;TranslatedChannel++){
	    if(ChannelXTable[TranslatedChannel][0] == CurChannel) {
	        Channel = ChannelXTable[TranslatedChannel][1];
	        break;
	    }
	}
	
	Socket  = PhysicalDimmInfo->Socket;
	Dimm    = PhysicalDimmInfo->Dimm;
	
	// Get SPD Data from APOB
	for (DieLoop = 0; DieLoop < ABL_APOB_MAX_DIES_PER_SOCKET; DieLoop++) {
	    Instance = DieLoop;
	    Instance |= ((Socket & 0x000000FF) << 8);
	    ApobStatus = ApobGetDimmSpdData (Instance, Socket, Channel, Dimm, 1024, DimmSpd);
	    if (ApobStatus == EFI_SUCCESS) {
	        break;
	    }
	}
	
	BaseConfig0 = (SPD_BASE_CONFIG_0_S*) &(DimmSpd[SpdBlock_BaseConfig_0 * SPD_BLOCK_LEN]);
	DimmType = BaseConfig0->KeyByte2.Field.BaseModuleType;
	
	ModuleParms = (SPD_ANNEX_COMMON_S*) &(DimmSpd[SpdBlock_ModuleParms_0 * SPD_BLOCK_LEN]);
	Asymetric = (ModuleParms->ModuleOrg.Field.RankMix == RankMixAsymmetrical) ? TRUE : FALSE;
	NumRanks = SPD_PACKAGE_RANKS_DECODE(ModuleParms->ModuleOrg.Field.RanksPerChannel);
	
	//
	// Add number of ranks to the string
	//
	switch(NumRanks) {
	case SINGLE_RANK:
	    UnicodeSPrint (DimmString1, STR_SIZE, L"SR");
	    break;
	case DUAL_RANK:
	    UnicodeSPrint (DimmString1, STR_SIZE, L"DR");
	    break;
	case QUAD_RANK:
	    UnicodeSPrint (DimmString1, STR_SIZE, L"QR");
	    break;
	case RANK_8R:
	    UnicodeSPrint (DimmString1, STR_SIZE, L"8R");
	    break;
	default:
        UnicodeSPrint (DimmString1, STR_SIZE, L" ");
        break;
	}
	
	//
	// Add the IOWidth and Dimm Type to the string
	//
	for (Rank = 0; Rank < MIN(NumRanks, 2); Rank ++ ){
	    if (Asymetric && (Rank & 0x01) ) {
	        IoWidth = SPD_DECODE_IO_WIDTH(BaseConfig0->SecondIoWidth.Field.IoWidth);
	    } else {
	        IoWidth = SPD_DECODE_IO_WIDTH(BaseConfig0->FirstIoWidth.Field.IoWidth);
	    }
	    
	    switch(IoWidth){
	    case X4_IOWIDTH:
	        UnicodeSPrint (DimmString2, STR_SIZE, L"x4");
	        break;
	    case X8_IOWIDTH:
	        UnicodeSPrint (DimmString2, STR_SIZE, L"x8");
	        break;
	    case X16_IOWIDTH:
	        UnicodeSPrint (DimmString2, STR_SIZE, L"x16");
	        break;
	    default:
            UnicodeSPrint (DimmString2, STR_SIZE, L" ");
            break;    
	    }
	    
	    StrCatS (DimmString1, STR_SIZE / sizeof (CHAR16), DimmString2);
	    if(!Asymetric){
	        break;
	    }
	}
    
    switch (DimmType) {
    case SPD_BASEMODULE_RDIMM:
        UnicodeSPrint (DimmString2, STR_SIZE, L" RDIMM");
        break;
    case SPD_BASEMODULE_LRDIMM:
        UnicodeSPrint (DimmString2, STR_SIZE, L" LRDIMM");
        break;
    case SPD_BASEMODULE_UDIMM:
        UnicodeSPrint (DimmString2, STR_SIZE, L" UDIMM");
        break;
    case SPD_BASEMODULE_SODIMM:
        UnicodeSPrint (DimmString2, STR_SIZE, L" SODIMM");
        break;
    case SPD_BASEMODULE_SORDIMM:
        UnicodeSPrint (DimmString2, STR_SIZE, L" SORDIMM");
        break;
    case SPD_BASEMODULE_SOLDEREDDOWN:
        UnicodeSPrint (DimmString2, STR_SIZE, L" SOLDEREDDOWN");
        break;
    default:
        UnicodeSPrint (DimmString2, STR_SIZE, L" ");
        break;
    }
    StrCatS (DimmString1, STR_SIZE / sizeof (CHAR16), DimmString2);
}
    
/**
    This function initializes the NB related setup option values

    @param HiiHandle Handle to HII database
    @param Class Indicates the setup class

    @retval VOID
**/

VOID
InitNbStrings(
  IN    EFI_HII_HANDLE  HiiHandle,
  IN    UINT16          Class )
{
    static BOOLEAN              Enumerate = FALSE;
    UINT8                       CurDimm;
    UINT8                       CurSocket;
    UINT8                       CurChannel;
    EFI_STATUS                  Status = EFI_DEVICE_ERROR;
    EFI_SMBIOS_PROTOCOL         *EfiSmbios;
    EFI_SMBIOS_HANDLE           SmbiosHandle;
    EFI_SMBIOS_TYPE             SmbiosType;
    EFI_SMBIOS_TABLE_HEADER     *SmbiosTable;
    SMBIOS_MEMORY_DEVICE_INFO   *Type17;
    UINT32                      Type17Size;

    UINT32                      DieNum = 0;
    UINT32                      Address = 0;
    UINT32                      IsTrained = 0;

    static AMI_SMBIOS_PROTOCOL  *SmbiosProtocol = NULL;
    CHAR8                       *StrAsciiTmpPtr;
    CHAR8                       *DevLocStrAsciiTmpPtr;
    CHAR16                      BankLocatorString[13];
    CHAR16                      DeviceLocatorString[11];
    CHAR16                      BankChar;
    UINT8                       MaxChannelPerSocket;
    UINT8                       MaxDimmsPerChannel;
    UINT8                       DimmStringCurNum;
    UINTN                       StringNumber;
    CHAR8                       *UnknownString = "Unknown";
    BOOLEAN                     isSpeedInMhz = TRUE;

    UINT16  DimmString[] = {
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT0_SLOT0_VALUE),    // Array0 Dimm0
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT0_SLOT1_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT0_SLOT2_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT0_SLOT3_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT0_SLOT4_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT0_SLOT5_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT0_SLOT6_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT0_SLOT7_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT0_SLOT8_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT0_SLOT9_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT0_SLOT10_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT0_SLOT11_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT0_SLOT12_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT0_SLOT13_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT0_SLOT14_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT0_SLOT15_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT0_SLOT16_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT0_SLOT17_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT0_SLOT18_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT0_SLOT19_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT0_SLOT20_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT0_SLOT21_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT0_SLOT22_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT0_SLOT23_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT1_SLOT0_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT1_SLOT1_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT1_SLOT2_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT1_SLOT3_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT1_SLOT4_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT1_SLOT5_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT1_SLOT6_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT1_SLOT7_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT1_SLOT8_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT1_SLOT9_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT1_SLOT10_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT1_SLOT11_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT1_SLOT12_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT1_SLOT13_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT1_SLOT14_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT1_SLOT15_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT1_SLOT16_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT1_SLOT17_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT1_SLOT18_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT1_SLOT19_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT1_SLOT20_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT1_SLOT21_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT1_SLOT22_VALUE),
        STRING_TOKEN(STR_MEMORY_SIZE_SCKT1_SLOT23_VALUE)
    };

    UINTN                       NumberOfInstalledProcessors;
    UINTN                       NumberOfDie;
    UINTN                       IgnoredRootBridges;
    AMD_FABRIC_TOPOLOGY_SERVICES2_PROTOCOL *FabricTopology;
    APOB_COMMON_SERVICE_PROTOCOL *ApobServicePtr = NULL;
    SOC_ID_STRUCT               SocId;
    BOOLEAN                     IsBrh = FALSE;
    UINT32                      SMUFuse;
    UINT32                      UMCNum;
    EFI_STATUS                  ApobStatus;
    APOB_TYPE_HEADER            *ApobSmbiosInfo;
    APOB_MEM_DMI_HEADER         *ApobMemDmiHeader;
    APOB_MEM_DMI_PHYSICAL_DIMM  *PhysicalDimm;
    CHAR16                      DimmString1[STR_SIZE];
    BOOLEAN                     ApobFlag = TRUE;
            
    // Check SoC hardware, default is BRH
    SocId.SocFamilyID = F1A_BRH_RAW_ID;
    SocId.PackageType = ZEN5_PKG_SP5;
    if (SocHardwareIdentificationCheck (&SocId)){
        IsBrh = TRUE;
    }

    SocId.SocFamilyID = F1A_BRHD_RAW_ID;
    SocId.PackageType = ZEN5_PKG_SP5;
    if (SocHardwareIdentificationCheck (&SocId)){
        IsBrh = TRUE;
    }

    Status = pBS->LocateProtocol (&gApobCommonServiceProtocolGuid, NULL, &ApobServicePtr);
    if (EFI_ERROR (Status)) {
        DEBUG ((DEBUG_ERROR , " Locate ApobCommonServiceProtocol failed\n"));
        return;
    }
    // Locate FabricTopologyServicesProtocol
    Status = pBS->LocateProtocol (&gAmdFabricTopologyServices2ProtocolGuid, NULL, (VOID **) &FabricTopology);
    if (EFI_ERROR (Status)) {
        DEBUG ((DEBUG_ERROR , " Locate AmdFabricTopologyServices2Protocol failed\n"));
        return;
    }
    FabricTopology->GetSystemInfo (FabricTopology, &NumberOfInstalledProcessors, &NumberOfDie, &IgnoredRootBridges, NULL, NULL);

    if (!Enumerate)
    {
        Enumerate = TRUE;
    }

    // Get DIMM information from Smbios protocol
    Status = pBS->LocateProtocol (&gEfiSmbiosProtocolGuid, NULL, (VOID **) &EfiSmbios);
    if (EFI_ERROR (Status)) {
        DEBUG ((DEBUG_ERROR , "InitNBStrings: pBS->LocateProtocol(EfiSmbiosProtocol... ERROR\n"));
        return;
    }
    // Check SmBios version - before 3.1 speed was in MHz, after - MTS
    if(((EfiSmbios->MajorVersion << 8)+(EfiSmbios->MinorVersion)) >= 0x301){
        isSpeedInMhz = FALSE;
    }
    SmbiosHandle = 0xFFFE;
    SmbiosType = 17;
    ApobServicePtr->ApobGetMaxDimmsPerChannel (0, &MaxDimmsPerChannel);

    if (IsBrh) {
        MaxChannelPerSocket = 12;
    }
    
    // Get SMBIOS APOB Entry Instance
    ApobStatus = AmdPspGetApobEntryInstance (APOB_SMBIOS, APOB_MEM_SMBIOS_TYPE, 0, FALSE, &ApobSmbiosInfo);
    if (ApobStatus != EFI_SUCCESS) {
        ApobFlag = FALSE;
        DEBUG ((DEBUG_ERROR, "%a : %r , No SMBIOS data found in APOB\n", __FUNCTION__, ApobStatus));
    }
    
    ApobMemDmiHeader = (APOB_MEM_DMI_HEADER *)ApobSmbiosInfo;
    PhysicalDimm = (APOB_MEM_DMI_PHYSICAL_DIMM *)&ApobMemDmiHeader[1];
    
    for (CurSocket = 0; CurSocket < NumberOfInstalledProcessors; CurSocket++)
    {
        DimmStringCurNum = 0;
        for (CurChannel = 0; CurChannel < MaxChannelPerSocket; CurChannel++) {

            #if (Model_ID == KERP3) 
                if(CurChannel == 3 || CurChannel == 5 || CurChannel == 9 || CurChannel == 11) {
                    for (CurDimm = 0; CurDimm < MaxDimmsPerChannel; CurDimm++) {
                        DimmStringCurNum = DimmStringCurNum + 1;
                        //Status = EfiSmbios->GetNext(EfiSmbios, &SmbiosHandle, &SmbiosType, &SmbiosTable, NULL);
                    }
                    continue;
                }
            #endif

#if defined(PLATFORM_SELECT) && (PLATFORM_SELECT == 7) // Titanite
            if ((CurChannel == 0) || (CurChannel == 6)){
                MaxDimmsPerChannel = 2;
            }
            else{
                MaxDimmsPerChannel = 1;
            }
#endif //#if defined(PLATFORM_SELECT) && (PLATFORM_SELECT == 7)
            DEBUG ((DEBUG_INFO, "CurSocket= %lx CurChannel= %lx MaxDimmsPerChannel= %lx\n", CurSocket, CurChannel, MaxDimmsPerChannel));
            for (CurDimm = 0; CurDimm < MaxDimmsPerChannel; CurDimm++) {
                DEBUG ((DEBUG_INFO, "InitNBStrings: CurDimm = %lx \n", CurDimm));
                Status = EfiSmbios->GetNext(EfiSmbios, &SmbiosHandle, &SmbiosType, &SmbiosTable, NULL);
                if (Status == EFI_SUCCESS) {
                    Type17 = (SMBIOS_MEMORY_DEVICE_INFO*)SmbiosTable;
                    DEBUG ((DEBUG_INFO, "InitNBStrings: Type17 size = %lx \n", Type17->Size));
                    Type17Size = (UINT32)(Type17->Size);
                    if (Type17Size==0x7FFF) {
                        Type17Size = Type17->ExtendedSize;
                    }
                    GetSmbiosStringByNumber((UINT8 *)SmbiosTable, Type17->BankLocator,
                    (UINT8 **)&StrAsciiTmpPtr);
                    Ascii2Unicode(BankLocatorString, StrAsciiTmpPtr);
//COMPAL_CHANGE >>>
                    //switch(BankLocatorString[StrLen(BankLocatorString)-1]){   
                    switch(BankLocatorString[StrLen(BankLocatorString)-2]){
//COMPAL_CHANGE <<<
                        case 'A':
                            if (IsBrh) {
                                DieNum = CurSocket;
                                Address = 0x350104;
                            } else {
                                DieNum = CurSocket * DIE_PER_SCKT + 1;
                                Address = 0x50104;
                            }
                            BankChar = 'A';
                            break;
                        case 'B':
                            if (IsBrh) {
                                DieNum = CurSocket;
                                Address = 0x450104;
                            } else {
                                DieNum = CurSocket * DIE_PER_SCKT + 1;
                                Address = 0x150104;
                            }
                            BankChar = 'B';
                            break;
                        case 'C':
                            if (IsBrh) {
                                DieNum = CurSocket;
                                Address = 0x050104;
                            } else {
                                DieNum = CurSocket * DIE_PER_SCKT;
                                Address = 0x150104;
                            }
                            BankChar = 'C';
                            break;
                        case 'D':
                            if (IsBrh) {
                                DieNum = CurSocket;
                                Address = 0x550104;
                            } else {
                                DieNum = CurSocket * DIE_PER_SCKT;
                                Address = 0x50104;
                            }
                            BankChar = 'D';
                            break;
                        case 'E':
                            if (IsBrh) {
                                DieNum = CurSocket;
                                Address = 0x150104;
                            } else {
                                DieNum = CurSocket * DIE_PER_SCKT + 3;
                                Address = 0x50104;
                            }
                            BankChar = 'E';
                            break;
                        case 'F':
                            if (IsBrh) {
                                DieNum = CurSocket;
                                Address = 0x250104;
                            } else {
                                DieNum = CurSocket * DIE_PER_SCKT + 3;
                                Address = 0x150104;
                            }
                            BankChar = 'F';
                            break;
                        case 'G':
                            if (IsBrh) {
                                DieNum = CurSocket;
                                Address = 0x950104;
                            } else {
                                DieNum = CurSocket * DIE_PER_SCKT + 2;
                                Address = 0x150104;
                            }
                            BankChar = 'G';
                            break;
                        case 'H':
                            if (IsBrh) {
                                DieNum = CurSocket;
                                Address = 0xA50104;
                            } else {
                                DieNum = CurSocket * DIE_PER_SCKT + 2;
                                Address = 0x50104;
                            }
                            BankChar = 'H';
                            break;
                        case 'I':
                            if (IsBrh) {
                                DieNum = CurSocket;
                                Address = 0x650104;
                            }
                            BankChar = 'I';
                            break;
                        case 'J':
                            if (IsBrh) {
                                DieNum = CurSocket;
                                Address = 0xB50104;
                            }
                            BankChar = 'J';
                            break;
                        case 'K':
                            if (IsBrh) {
                                DieNum = CurSocket;
                                Address = 0x750104;
                            }
                            BankChar = 'K';
                            break;
                        case 'L':
                            if (IsBrh) {
                                DieNum = CurSocket;
                                Address = 0x850104;
                            }
                            BankChar = 'L';
                            break;
                        default:
                            DieNum = 0;
                            Address = 0;
                            BankChar = 'X';
                            break;
                    }
                    if (!Address) {
                        DEBUG ((DEBUG_ERROR, "NbSetup - Unknown DIMM DieNum and Address\n"));
                    }
                    GetSmbiosStringByNumber((UINT8 *)SmbiosTable, Type17->DeviceLocator,
                                                (UINT8 **)&DevLocStrAsciiTmpPtr);

                    Ascii2Unicode(DeviceLocatorString, DevLocStrAsciiTmpPtr);
                    UnicodeSPrint(DeviceLocatorString, sizeof(DeviceLocatorString), L"DIMM %c%d", BankChar, CurDimm );

					// SMUFUSE_UMCHARVEST_PUBLICADDR and check the UMC_CTRL_x_HARVEST bits (bits 14:3). Any UMC's that have their
					// corresponding fuse bit set to 1 are not present and should not be accessed
                    NbSmnRead(DieNum, SMUFUSE_UMCHARVEST_PUBLICADDR, &SMUFuse);
                    SMUFuse = (SMUFuse >> 3) & 0xfff;
                    DEBUG((EFI_D_ERROR, "DieNum = %x, SMUFUSE_UMCHARVEST = %x\n", DieNum, SMUFuse));
										
                    UMCNum = (Address >> 20) & 0xf;
                    if (0 == (SMUFuse & ((UINT32) 1 << UMCNum))) {
                      NbSmnRead(DieNum, Address, &IsTrained);
					  DEBUG((EFI_D_ERROR, "DieNum = %x, Address %x value= %x\n", DieNum, Address, IsTrained));

                    }

                    IsTrained = IsTrained & 0x80000000;
                    if(IsTrained){
                        GetSmbiosStringByNumber((UINT8 *)SmbiosTable, Type17->Manufacturer,
                                                (UINT8 **)&StrAsciiTmpPtr);
                        if(AsciiStrCmp(StrAsciiTmpPtr,UnknownString)){ // Check to see if any DIMM is installed, but not trained
                            // Get DIMM Information to update setup page
                            if(ApobFlag){
                                PhysicalDimm->Dimm = CurDimm;
                                GetDimmInfo(CurChannel, PhysicalDimm, DimmString1);
                            }
                            InitString(HiiHandle, DimmString[DimmStringCurNum + (CurSocket * 24)],
                                    L"%s: %a, Size %d MB, Speed %d MT/s, %s", 
                            DeviceLocatorString , StrAsciiTmpPtr, Type17Size, isSpeedInMhz?Type17->ConfMemClkSpeed * 2:Type17->ConfMemClkSpeed, DimmString1);
                            DimmStringCurNum = DimmStringCurNum + 1;
                            SystemRam += Type17Size;
                        } else {
                            InitString(HiiHandle, DimmString[DimmStringCurNum + (CurSocket * 24)],
                                                  L"%s: Not Present",
                                                  DeviceLocatorString);
                            DimmStringCurNum = DimmStringCurNum + 1;
                        }
                    } else {
                        GetSmbiosStringByNumber((UINT8 *)SmbiosTable, Type17->Manufacturer,
                                                  (UINT8 **)&StrAsciiTmpPtr);
                        if(AsciiStrCmp(StrAsciiTmpPtr,UnknownString)){ // Check to see if any DIMM is installed, but not trained
                            InitString(HiiHandle, DimmString[DimmStringCurNum + (CurSocket * 24)],
                                                  L"%s: Present but not trained",
                                                  DeviceLocatorString);
                            DimmStringCurNum = DimmStringCurNum + 1;
                            StringNumber = 3; //String for Manufacturer to set to "Unknown" if not trained
                            Status = EfiSmbios->UpdateString(EfiSmbios, &SmbiosHandle, &StringNumber, UnknownString ) ;
                            if (EFI_ERROR(Status)) {
                                DEBUG ((DEBUG_ERROR, "NbSetup - Fail to updated SMBIOS string with Status - %r\n", Status));
                            }
                        } else {
                            InitString(HiiHandle, DimmString[DimmStringCurNum + (CurSocket * 24)],
                                                  L"%s: Not Present",
                                                  DeviceLocatorString);         
                            DimmStringCurNum = DimmStringCurNum + 1;
                        }
                    }
                }
            }
        }
    }
    if (Class == MAIN_FORM_SET_CLASS) {
        InitString (HiiHandle, STRING_TOKEN(STR_MEMORY_SIZE_VALUE),
                              L" %4d MB", SystemRam);
    }

    if (Class == CHIPSET_FORM_SET_CLASS) {
        InitString (HiiHandle, STRING_TOKEN(STR_MEMORY_SIZE_VALUE),
                              L"Total Memory: %4d MB", SystemRam);
  }
}

