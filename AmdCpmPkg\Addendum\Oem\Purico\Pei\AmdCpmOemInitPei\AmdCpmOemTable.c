/*****************************************************************************
 *
 * Copyright (C) 2015-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#include <Library/BaseMemoryLib.h>
#include <AmdCpmPei.h>
#include <AmdCpmOem.h>
#include <Ppi/AmdBoardIdPpi.h>
#include <Ppi/Pca9545aPpi.h>
#include <Ppi/Pca9535aPpi.h>
#include "AmdCpmOemInitPeim.h"
#include <Library/SmnAccessLib.h>
#include <Ppi/Tca9548aPpi.h>
#include <AmdBackplanes.h>
#include <Library/AmdBaseLib.h>
#include <Library/MemoryAllocationLib.h>
#include <Library/TimerLib.h>
#include <Ppi/I2cMaster.h>

// Eric Added
#include <Library/BaseLib.h>
//#include <AmdSoc.h>
// Eric Added

#define MAX_PORT_NUM  9

typedef enum {
  PLINK,
  GLINK,
  MAXLINK
} LINK_ENUM;

typedef struct {
  UINT8    Start;
  UINT8    End;
} PGLINK;

typedef struct {
  UINT16   ConfigMask;
  PGLINK   PGLink[MAXLINK];
} SLOT_LANES_MAP;

typedef struct {
  UINT8               ConfigType;
  ADDIN_CARD_PORTS    *ConfigEntry;
  UINT8               LaneNum[MAX_PORT_NUM];
} SLOT_CONFIG;

DXIO_PORT_DESCRIPTOR    Gen_1x8_8x1_PortList[] = {
    {
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    {
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    {
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    {
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    {
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    {
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    {
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    {
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    {
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
};   // End of DXIO_PORT_DESCRIPTOR

ADDIN_CARD_PORTS Gen_1x8_8x1 = {
    0xFF,
    9,
    &Gen_1x8_8x1_PortList[0]
};

DXIO_PORT_DESCRIPTOR    Gen_8x2_PortList[] = {
    {
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    {
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    {
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    {
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    {
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    {
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    {
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    {
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
};   // End of DXIO_PORT_DESCRIPTOR

ADDIN_CARD_PORTS Gen_8x2 = {
    0xFF,
    8,
    &Gen_8x2_PortList[0]
};

DXIO_PORT_DESCRIPTOR    Gen_x8x8_PortList[] = {
    { // P0 - x8 MCEIO slot
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    { // P1 - x8 MCEIO slot
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
};   // End of DXIO_PORT_DESCRIPTOR

ADDIN_CARD_PORTS Gen_x8x8 = {
    0xFF,
    2,
    &Gen_x8x8_PortList[0]
};

DXIO_PORT_DESCRIPTOR    Gen_x4x4x4x4_PortList[] = {
    { // P0 - x4 MCEIO slot
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    { // P1 - x4 MCEIO slot
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    { // P0 - x4 MCEIO slot
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
    { // P1 - x4 MCEIO slot
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 0, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAMS_END
    },
};   // End of DXIO_PORT_DESCRIPTOR

ADDIN_CARD_PORTS Gen_x4x4x4x4 = {
    0xFF,
    4,
    &Gen_x4x4x4x4_PortList[0]
};

SLOT_CONFIG       SlotConfig[] = {
  {GENERIC_1x8_8x1, &Gen_1x8_8x1,  {8, 1, 1, 1, 1, 1, 1, 1, 1}},
  {GENERIC_2x8,     &Gen_x8x8,     {8, 8, 0, 0, 0, 0, 0, 0, 0}},
  {GENERIC_4x4,     &Gen_x4x4x4x4, {4, 4, 4, 4, 0, 0, 0, 0, 0}},
  {GENERIC_8x2,     &Gen_8x2,      {2, 2, 2, 2, 2, 2, 2, 2, 0}}
};

SLOT_LANES_MAP    SlotLanesMap[] = {
// BitMask   PLink      GLink
  {0xF000, {{ 0, 15}, { 96, 111}}},
  {0x0F00, {{32, 47}, { 64,  79}}},
  {0x00F0, {{48, 63}, {112, 127}}},
  {0x000F, {{16, 31}, { 80,  95}}}
};

//
// OEM CPM Table Definition
//

//
// Platform Id Table: Get Board Id from SMBUS
//

// Read SMBUS to detect board ID
//BOARD_ID_BIT2  BOARD_ID_BIT1  BOARD_ID_BIT0    PCBA
//    0                      0                          0             Normal  REVA
//    0                      1                          0             SLT REVA
//    1                      0                          0             DAP REVA
AMD_CPM_PLATFORM_ID_TABLE2          gCpmPlatformIdTable = {
//{  UINT32 TableSignature;   // Signature of CPM table
//   UINT16 TableSize;        // Table size
//   UINT8  FormatRevision;   // Revision of table format
//   UINT8  ContentRevision;  // Revision of table content
//   UINT32 PlatformMask;     // The mask of platform table supports
//   UINT32 Attribute;        // Table attribute},
  {CPM_SIGNATURE_GET_PLATFORM_ID2, sizeof (gCpmPlatformIdTable) / sizeof (UINT8), 0, 0, 0, 1},
  {
//   UINT8  SmbusSelect;      ///< SMBUS Number
//   UINT8  SmbusAddress;     ///< SMBUS Address
//   UINT8  SmbusOffset;      ///< SMBUS Offset
//   UINT8  SmbusBit;         ///< SMBUS Bit
    {0xFF},
  }
};

//
// Convert Table from Board Id to Platform Id
//

AMD_CPM_PLATFORM_ID_CONVERT_TABLE   gCpmPlatformIdConvertTable = {
  {CPM_SIGNATURE_GET_PLATFORM_ID_CONVERT, sizeof (gCpmPlatformIdConvertTable) / sizeof (UINT8), 0, 0, 0, 1},
  {
    {0xFF},
  }
};


//
// Pre-Init Table
//
AMD_CPM_PRE_INIT_TABLE              gCpmPreInitTable = {
//{  UINT32 TableSignature;   // Signature of CPM table
//   UINT16 TableSize;        // Table size
//   UINT8  FormatRevision;   // Revision of table format
//   UINT8  ContentRevision;  // Revision of table content
//   UINT32 PlatformMask;     // The mask of platform table supports
//   UINT32 Attribute;        // Table attribute},
  {CPM_SIGNATURE_PRE_INIT, sizeof (gCpmPreInitTable) / sizeof (UINT8), 0, 0, 0, 0x01},
  {
//  {UINT8  Type;     // Register type. 0: FCH MMIO. 1: PCI
//   UINT8  Select;   // Register sub-type
//   UINT8  Offset;   // Register offset
//   UINT8  AndMask;  // AND mask
//   UINT8  OrMask;   // OR mask
//   UINT8  Stage;    // Stage number},
    {0xFF},
  }
};

//
// GPIO Init Table for Purico
//
AMD_CPM_GPIO_INIT_TABLE             gCpmGpioInitTablePurico = {
//{  UINT32 TableSignature;   // Signature of CPM table
//   UINT16 TableSize;        // Table size
//   UINT8  FormatRevision;   // Revision of table format
//   UINT8  ContentRevision;  // Revision of table content
//   UINT32 PlatformMask;     // The mask of platform table supports
//   UINT32 Attribute;        // Table attribute},
  {CPM_SIGNATURE_GPIO_INIT, sizeof (gCpmGpioInitTablePurico) / sizeof (UINT8), 0, 0, 0, 0x0000000F},
  {//        (socket,die,gpio,function,         output,           pullup)
//    GPIO_DEF_V2 (0, 0, 89,  GPIO_FUNCTION_1,  GPIO_NA,       GPIO_PU_PD_DIS),   //  PSP_INTR_0
//    GPIO_DEF_V2 (0, 0, 90,  GPIO_FUNCTION_0,  GPIO_INPUT,       GPIO_PU_EN    ),   // TPM_INT#
//    GPIO_DEF_V2 (0, 0, 130, GPIO_FUNCTION_0,  GPIO_NA,          GPIO_PU_PD_DIS),   // SATA_LED
//    GPIO_DEF_V2 (0, 0, 131, GPIO_FUNCTION_3,  GPIO_OUTPUT_HIGH, GPIO_PU_EN    ),   // 0: SATA, 1: PCIE
  GPIO_DEF_V2(0, 0, 4,   GPIO_FUNCTION_1, GPIO_NA,  GPIO_PU_PD_DIS), // SATA_ACT_L
//    GPIO_DEF_V2(0, 0, 75,  GPIO_FUNCTION_1, GPIO_NA,         GPIO_PU_PD_DIS), // ESPI_CLK1
  GPIO_DEF_V2(0, 0, 76,  GPIO_FUNCTION_1, GPIO_NA,  GPIO_PU_PD_DIS), // SPI_TPM_CS_L
  GPIO_DEF_V2(0, 0, 129, GPIO_FUNCTION_1, GPIO_NA,  GPIO_PU_PD_DIS), // KBRST_L

    {0xFF},
  }
};

//
// GEVENT Init Table
//
AMD_CPM_GEVENT_INIT_TABLE           gCpmGeventInitTablePurico = {
//{  UINT32 TableSignature;   // Signature of CPM table
//   UINT16 TableSize;        // Table size
//   UINT8  FormatRevision;   // Revision of table format
//   UINT8  ContentRevision;  // Revision of table content
//   UINT32 PlatformMask;     // The mask of platform table supports
//   UINT32 Attribute;        // Table attribute},
  {CPM_SIGNATURE_GEVENT_INIT, sizeof (gCpmGeventInitTablePurico) / sizeof (UINT8), 0, 0, 0, 0x00000001},
  {//                  GEvent EventEnable   SciTrigE      SciLevl         SmiSciEn        SciS0En         SciMap      SmiTrig       SmiControl
//    GEVENT_DEFINITION (0x03,  EVENT_ENABLE, SCITRIG_HI,   SCILEVEL_EDGE,  SMISCI_DISABLE, SCIS0_DISABLE,  SCIMAP_03,  SMITRIG_HI,   SMICONTROL_DISABLE), // GEVENT03: PM_INT_IN
//    GEVENT_DEFINITION (0x05,  EVENT_ENABLE, SCITRIG_HI,   SCILEVEL_EDGE,  SMISCI_DISABLE, SCIS0_DISABLE,  SCIMAP_05,  SMITRIG_HI,   SMICONTROL_DISABLE), // GEVENT05: LAN_MEDIA_SENSE
//    GEVENT_DEFINITION (0x08,  EVENT_ENABLE, SCITRIG_LOW,  SCILEVEL_EDGE,  SMISCI_DISABLE, SCIS0_DISABLE,  SCIMAP_08,  SMITRIG_LOW,  SMICONTROL_DISABLE), // GEVENT08: PCIE_WAKE_UP#
//    GEVENT_DEFINITION (0x0C,  EVENT_ENABLE, SCITRIG_LOW,  SCILEVEL_EDGE,  SMISCI_DISABLE, SCIS0_DISABLE,  SCIMAP_12,  SMITRIG_LOW,  SMICONTROL_DISABLE), // GEVENT12: USB_OC#
//    GEVENT_DEFINITION (0x0D,  EVENT_ENABLE, SCITRIG_HI,   SCILEVEL_EDGE,  SMISCI_DISABLE, SCIS0_DISABLE,  SCIMAP_13,  SMITRIG_HI,   SMICONTROL_DISABLE), // GEVENT13: LAN_LOW_POWER
//    GEVENT_DEFINITION (0x0E,  EVENT_ENABLE, SCITRIG_LOW,  SCILEVEL_EDGE,  SMISCI_DISABLE, SCIS0_DISABLE,  SCIMAP_14,  SMITRIG_LOW,  SMICONTROL_DISABLE), // GEVENT14: LAN_SMART#
//    GEVENT_DEFINITION (0x0F,  EVENT_ENABLE, SCITRIG_LOW,  SCILEVEL_EDGE,  SMISCI_DISABLE, SCIS0_DISABLE,  SCIMAP_15,  SMITRIG_LOW,  SMICONTROL_DISABLE), // GEVENT15: EVALCARD_ALERT#
    {0xFF},
  }
};

//
// CPM GPIO Module
//

AMD_CPM_GPIO_DEVICE_CONFIG_TABLE    gCpmGpioDeviceConfigTablePurico = {
//{  UINT32 TableSignature;   // Signature of CPM table
//   UINT16 TableSize;        // Table size
//   UINT8  FormatRevision;   // Revision of table format
//   UINT8  ContentRevision;  // Revision of table content
//   UINT32 PlatformMask;     // The mask of platform table supports
//   UINT32 Attribute;        // Table attribute},
  {CPM_SIGNATURE_GPIO_DEVICE_CONFIG, sizeof (gCpmGpioDeviceConfigTablePurico) / sizeof (UINT8), 0, 0, 0, 0x0000000F},
  {//                       DeviceId          Enable            Assert  Deassert  Hotplugs
//    GPIO_DEVICE_DEFINITION (DEVICE_ID_GBE,    CPM_DEVICE_ON,    0,      0,        0), // GBE
//    GPIO_DEVICE_DEFINITION (DEVICE_ID_BT,     CPM_DEVICE_ON,    0,      0,        0), // BT
//    GPIO_DEVICE_DEFINITION (DEVICE_ID_WLAN,   CPM_DEVICE_ON,    0,      0,        0), // WLAN
//    GPIO_DEVICE_DEFINITION (DEVICE_ID_PCIE_X16_SWITCH,   CPM_DEVICE_ON,    0,      0,        0), // PCIe 1x16/2x8 Switch
//    GPIO_DEVICE_DEFINITION (DEVICE_ID_SATAE_M2_SWITCH,   CPM_DEVICE_ON,    0,      0,        0), // SataExpress/M.2 Switch
    {0xFF},
  }
};

//
// CPM Device Detection Table for Purico
//
AMD_CPM_GPIO_DEVICE_DETECTION_TABLE gCpmGpioDeviceDetectionTablePurico = {
//{  UINT32 TableSignature;   // Signature of CPM table
//   UINT16 TableSize;        // Table size
//   UINT8  FormatRevision;   // Revision of table format
//   UINT8  ContentRevision;  // Revision of table content
//   UINT32 PlatformMask;     // The mask of platform table supports
//   UINT32 Attribute;        // Table attribute},
  {CPM_SIGNATURE_GPIO_DEVICE_DETECTION, sizeof (gCpmGpioDeviceDetectionTablePurico) / sizeof (UINT8), 0, 0, 0, 0x0000000F},
  {
//  {UINT8  DeviceId; // Device Id
//   UINT8  Type;     // Detection type. 0: One GPIO pin. 1: Two GPIO pins. 2: Special Pin
//   UINT16 PinNum1;  // Pin number of GPIO 1
//   UINT8  Value1;   // Value of GPIO 1
//   UINT16 PinNum2;  // Pin number of GPIO 2
//   UINT8  Value2;   // Value of GPIO 2
//   UINT16 PinNum3;  // Pin number of GPIO 3
//   UINT8  Value3;   // Value of GPIO 3},
    {0xFF},
  }
};

//
// CPM Device Reset Table
//
AMD_CPM_GPIO_DEVICE_RESET_TABLE gCpmGpioDeviceResetTablePurico = {
//{  UINT32 TableSignature;   // Signature of CPM table
//   UINT16 TableSize;        // Table size
//   UINT8  FormatRevision;   // Revision of table format
//   UINT8  ContentRevision;  // Revision of table content
//   UINT32 PlatformMask;     // The mask of platform table supports
//   UINT32 Attribute;        // Table attribute},
  {CPM_SIGNATURE_GPIO_DEVICE_RESET, sizeof (gCpmGpioDeviceResetTablePurico) / sizeof (UINT8), 0, 0, 0, 0x0000000F},
  {
//  {UINT8  DeviceId;       // Device Id
//   UINT8  Mode;           // Reset mode     // 0: Reset Assert. // 1: Reset De-assert             // 2: Delay between Assert and Deassert
//   UINT8  Type;           // Register type  // 0: GPIO.         // 1: Special pin.if Mode = 0 or 1
//   UINT32 ((UINT16)Pin + ((UINT8)Value << 16));                 // GPIO pin value
//   UINT8  InitFlag;       // Init flag in post},
    {0xFF},
  }
};

//
// CPM GPIO Device Init Table (Power On/Off)
//
AMD_CPM_GPIO_DEVICE_POWER_TABLE gCpmGpioDevicePowerTablePurico = {
//{  UINT32 TableSignature;   // Signature of CPM table
//   UINT16 TableSize;        // Table size
//   UINT8  FormatRevision;   // Revision of table format
//   UINT8  ContentRevision;  // Revision of table content
//   UINT32 PlatformMask;     // The mask of platform table supports
//   UINT32 Attribute;        // Table attribute},
  {CPM_SIGNATURE_GPIO_DEVICE_POWER, sizeof (gCpmGpioDevicePowerTablePurico) / sizeof (UINT8), 0, 0, 0, 0x0000000F},
  {
//  {UINT8  DeviceId;       // Device Id
//   UINT8  Mode;           // Device Power Mode. 1: Power On. 0: Power Off
//   UINT8  Type;           // Device Power Item. 0: Set GPIO. 1: Wait GPIO. 2: Add Delay
//   UINT32 ((UINT16)Pin + ((UINT8)Value << 16));                 // GPIO pin value or delay timer
//   UINT8  InitFlag;       // Init flag in post},
//    {DEVICE_ID_GBE,    CPM_POWER_OFF, CPM_POWER_SET,   CPM_GPIO_PIN (GBE_POWER_CONTROL_REVA, 1),  0},
//    {DEVICE_ID_BT,     CPM_POWER_OFF, CPM_POWER_SET,   CPM_GPIO_PIN (BT_POWER_CONTROL_REVA, 0),   0},
//    {DEVICE_ID_WLAN,   CPM_POWER_OFF, CPM_POWER_SET,   CPM_GPIO_PIN (WLAN_POWER_CONTROL_REVA, 0), 0},
//    {DEVICE_ID_PCIE_X16_SWITCH,   CPM_POWER_OFF, CPM_POWER_SET,   CPM_GPIO_PIN (PCIE_X16_SWITCH, 0), 0},
//    {DEVICE_ID_SATAE_M2_SWITCH,   CPM_POWER_OFF, CPM_POWER_SET,   CPM_GPIO_PIN (SATAE_M2_SWITCH, 0), 0},

//    {DEVICE_ID_GBE,    CPM_POWER_ON,  CPM_POWER_SET,   CPM_GPIO_PIN (GBE_POWER_CONTROL_REVA, 0),  0},
//    {DEVICE_ID_BT,     CPM_POWER_ON,  CPM_POWER_SET,   CPM_GPIO_PIN (BT_POWER_CONTROL_REVA, 1),   0},
//    {DEVICE_ID_WLAN,   CPM_POWER_ON,  CPM_POWER_SET,   CPM_GPIO_PIN (WLAN_POWER_CONTROL_REVA, 1), 0},
//    {DEVICE_ID_PCIE_X16_SWITCH,   CPM_POWER_ON, CPM_POWER_SET,   CPM_GPIO_PIN (PCIE_X16_SWITCH, 1), 0},
//    {DEVICE_ID_SATAE_M2_SWITCH,   CPM_POWER_ON, CPM_POWER_SET,   CPM_GPIO_PIN (SATAE_M2_SWITCH, 1), 0},
    {0xFF},
  }
};

//
// PCIE Clock Table
//
AMD_CPM_PCIE_CLOCK_TABLE    gCpmPcieClockTablePurico = {
//{  UINT32 TableSignature;   // Signature of CPM table
//   UINT16 TableSize;        // Table size
//   UINT8  FormatRevision;   // Revision of table format
//   UINT8  ContentRevision;  // Revision of table content
//   UINT32 PlatformMask;     // The mask of platform table supports
//   UINT32 Attribute;        // Table attribute},
  {CPM_SIGNATURE_PCIE_CLOCK, sizeof (gCpmPcieClockTablePurico) / sizeof (UINT8), 0, 0, 0, 0x0F},
  {
//  {UINT8  ClkId;              // FCH PCIe Clock
//   UINT8  ClkReq;             // FCH PCIe ClkReq
//   UINT8  ClkIdExt;           // External Clock Source
//   UINT8  ClkReqExt;          // External ClkReq
//   UINT8  DeviceId;           // Device Id. No Device Id if 0xFF
//   UINT8  Device;             // Device Number of PCIe bridge
//   UINT8  Function;           // Function Number of PCIe bridge
//   UINT8  SlotCheck;          // Slot Check Flag: // BIT0: Check PCI Space // BIT1: Check GPIO pin // BIT2: Check Clock Power Management Enable // BIT3~6: Reserved // BIT7: Change PCIe Clock in ACPI method
//   UINT32 SpecialFunctionId;  // Id of Special Function [31:28] Miscid, [27:24] Socket, [23:20] Die, [19:16] Reserved, [15:00] SpecialFunctionId
//***
//   SSP_GPP_CLKREQ_mapping
//
//   GPP_CLK0B => CLKREQ2_0 (CLKGEN BOT) => FCH::MISC::GPPCLKCNTROL[7:6] gpp_clk2_clock_request_mapping
//   GPP_CLK0T => CLKREQ1_0 (CLKGEN BOT) => FCH::MISC::GPPCLKCNTROL[3:2] gpp_clk1_clock_request_mapping
//   GPP_CLK1B => CLKREQ2_1 (CLKGEN TOP) => FCH::MISC2::RMT_CLKCNTL_0_REG[7:6] gpp2_clkreq_mapping
//   GPP_CLK1T => CLKREQ1_1 (CLKGEN TOP) => FCH::MISC2::RMT_CLKCNTL_0_REG[3:2] gpp1_clkreq_mapping
//   GPP_CLK2B => CLKREQ4_1 (CLKGEN TOP) => FCH::MISC2::RMT_CLKCNTL_0_REG[5:4] gpp4_clkreq_mapping
//   GPP_CLK2T => CLKREQ3_1 (CLKGEN TOP) => FCH::MISC2::RMT_CLKCNTL_0_REG[9:8] gpp3_clkreq_mapping
//   GPP_CLK3B => CLKREQ4_0 (CLKGEN BOT) => FCH::MISC::GPPCLKCNTROL[5:4] gpp_clk4_clock_request_mapping
//   GPP_CLK3T => CLKREQ3_0 (CLKGEN BOT) => FCH::MISC::GPPCLKCNTROL[9:8] gpp_clk3_clock_request_mapping
//***
// Socket 0
    //{GPP_CLK0B, CLK_ENABLE, SRC_SKIP,  0,           0,                       0, 0, SLOT_CHECK,            0x10000000},
    //{GPP_CLK0T, CLK_ENABLE, SRC_SKIP,  0,           0,                       0, 0, SLOT_CHECK,            0x10000000},
    //{GPP_CLK1B, CLK_ENABLE, SRC_SKIP,  0,           0,                       0, 0, SLOT_CHECK,            0x20000000},
    //{GPP_CLK1T, CLK_ENABLE, SRC_SKIP,  0,           0,                       0, 0, SLOT_CHECK,            0x20000000},
    //{GPP_CLK2B, CLK_ENABLE, SRC_SKIP,  0,           0,                       0, 0, SLOT_CHECK,            0x20000000},
    //{GPP_CLK2T, CLK_ENABLE, SRC_SKIP,  0,           0,                       0, 0, SLOT_CHECK,            0x20000000},
    //{GPP_CLK3B, CLK_ENABLE, SRC_SKIP,  0,           0,                       0, 0, SLOT_CHECK,            0x10000000},
    //{GPP_CLK3T, CLK_ENABLE, SRC_SKIP,  0,           0,                       0, 0, SLOT_CHECK,            0x10000000},
// Socket 1
    //{GPP_CLK0B, CLK_ENABLE, SRC_SKIP,  0,           0,                       0, 0, SLOT_CHECK,            0x11000000},
    //{GPP_CLK0T, CLK_ENABLE, SRC_SKIP,  0,           0,                       0, 0, SLOT_CHECK,            0x11000000},
    //{GPP_CLK1B, CLK_ENABLE, SRC_SKIP,  0,           0,                       0, 0, SLOT_CHECK,            0x21000000},
    //{GPP_CLK1T, CLK_ENABLE, SRC_SKIP,  0,           0,                       0, 0, SLOT_CHECK,            0x21000000},
    //{GPP_CLK2B, CLK_ENABLE, SRC_SKIP,  0,           0,                       0, 0, SLOT_CHECK,            0x21000000},
    //{GPP_CLK2T, CLK_ENABLE, SRC_SKIP,  0,           0,                       0, 0, SLOT_CHECK,            0x21000000},
    //{GPP_CLK3B, CLK_ENABLE, SRC_SKIP,  0,           0,                       0, 0, SLOT_CHECK,            0x11000000},
    //{GPP_CLK3T, CLK_ENABLE, SRC_SKIP,  0,           0,                       0, 0, SLOT_CHECK,            0x11000000},
    {0xFF},
  }
};


//
// CPM DXIO Topology Table Socket 0 with field card in slot1
//
AMD_CPM_DXIO_TOPOLOGY_TABLE gCpmDxioTopologyTablePuricoS0 = {
//{  UINT32 TableSignature;   // Signature of CPM table
//   UINT16 TableSize;        // Table size
//   UINT8  FormatRevision;   // Revision of table format
//   UINT8  ContentRevision;  // Revision of table contect
//   UINT32 PlatformMask;     // The mask of platform table supports
//   UINT32 Attribute;        // Table attribute},
  {CPM_SIGNATURE_DXIO_TOPOLOGY, sizeof (gCpmDxioTopologyTablePuricoS0) / sizeof (UINT8), 0, 0, 0, 0x0000000F},
  0,  // SocketId
  {   // DXIO_PORT_DESCRIPTOR
//  { UINT32  Flags;                    // Descriptor flags
//    #define DXIO_ENGINE_INITIALIZER (mType, mStartLane, mEndLane, mResetId)
//    #define DXIO_PORT_INITIALIZER (mPortPresent)

//    GENOA MAPPING INFORMATION
//    P0[0-15],   G0[96-111]
//    P1[32-47],  G1[64-79]
//    P2[48-63],  G2[112-127]
//    P3[16-31],  G3[80-95]
//    P4[128:131]
//    P5[132-135]
//    SATA is supported on P0[0-7]
//
    { // P4 - x4 M.2
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 128, 131, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_LINK_SPEED_CAP, DxioGen4),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen3),
        PORT_PARAM (PP_SLOT_NUM, 2)
      PORT_PARAMS_END
    },
  { // P5 - x1 M.2
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 132, 132, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_LINK_SPEED_CAP, DxioGen4),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen3),
        PORT_PARAM (PP_SLOT_NUM, 3)
      PORT_PARAMS_END
    },
   { // P5 - x1 M.2
      0,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 133, 133, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
        PORT_PARAM (PP_LINK_SPEED_CAP, DxioGen4),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen3),
        PORT_PARAM (PP_SLOT_NUM, 4)
      PORT_PARAMS_END
    },
    { // P5 - x1 NIC
      DESCRIPTOR_TERMINATE_LIST,
      DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 134, 134, 1),
      DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
      PORT_PARAMS_START
        PORT_PARAM (PP_LINK_ASPM, DxioAspmDisabled),
        PORT_PARAM (PP_LINK_SPEED_CAP, DxioGen4),
        PORT_PARAM (PP_TARGET_LINK_SPEED, DxioGen3),
        PORT_PARAM (PP_SLOT_NUM, 5)
      PORT_PARAMS_END
    }
  }   // End of DXIO_PORT_DESCRIPTOR
};

DXIO_PORT_DESCRIPTOR    CommonPLinksConfig[] = {
  { // P2 - x16 OCP 3.0 Mezzanine
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 48, 63, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 1)
    PORT_PARAMS_END
  },
  { // P3 - x16 RISER 3
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 16, 31, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 9)
    PORT_PARAMS_END
  }
};   // End of DXIO_PORT_DESCRIPTOR

ADDIN_CARD_PORTS CommonPLinksConfigEntry = {
  0xFF,
  2,
  &CommonPLinksConfig[0]
};

DXIO_PORT_DESCRIPTOR    NvmeGLinksConfig[] = {
  { // G0 - x4 NVME
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 96, 99, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 10)
    PORT_PARAMS_END
  },
  { // G0 - x4 NVME
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 100, 103, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 11)
    PORT_PARAMS_END
  },
  { // G0 - x4 NVME
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 104, 107, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 12)
    PORT_PARAMS_END
  },
  { // G0 - x4 NVME
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 108, 111, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 13)
    PORT_PARAMS_END
  },
  { // G1 - x4 NVME
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 64, 67, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 14)
    PORT_PARAMS_END
  },
  { // G1 - x4 NVME
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 68, 71, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 15)
    PORT_PARAMS_END
  },
  { // G1 - x4 NVME
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 72, 75, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 16)
    PORT_PARAMS_END
  },
  { // G1 - x4 NVME
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 76, 79, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 17)
    PORT_PARAMS_END
  },
  { // G2 - x4 NVME
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 112, 115, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 18)
    PORT_PARAMS_END
  },
  { // G2 - x4 NVME
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 116, 119, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 19)
    PORT_PARAMS_END
  },
  { // G2 - x4 NVME
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 120, 123, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 20)
    PORT_PARAMS_END
  },
  { // G2 - x4 NVME
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 124, 127, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 21)
    PORT_PARAMS_END
  },
  { // G3 - x4 NVME
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 80, 83, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present1
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 22)
    PORT_PARAMS_END
  },
  { // G3 - x4 NVME
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 84, 87, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present1
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 23)
    PORT_PARAMS_END
  },
  { // G3 - x4 NVME
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 88, 91, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present1
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 24)
    PORT_PARAMS_END
  },
  { // G3 - x4 NVME
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 92, 95, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present1
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 25)
    PORT_PARAMS_END
  }
};   // End of DXIO_PORT_DESCRIPTOR

ADDIN_CARD_PORTS NvmeGLinksConfigEntry = {
    0xFF,
    16,
    &NvmeGLinksConfig[0]
};

DXIO_PORT_DESCRIPTOR    NvmePLinksConfig[] = {
  { // P0 - x4 NVME
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 3, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 26)
    PORT_PARAMS_END
  },
  { // P0 - x4 NVME
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 4, 7, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 27)
    PORT_PARAMS_END
  },
  { // P0 - x4 NVME
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 8, 11, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 28)
    PORT_PARAMS_END
  },
  { // P0 - x4 NVME
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 12, 15, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 29)
    PORT_PARAMS_END
  },
  { // P1 - x4 NVME
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 32, 35, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 30)
    PORT_PARAMS_END
  },
  { // P1 - x4 NVME
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 36, 39, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 31)
    PORT_PARAMS_END
  },
  { // P1 - x4 NVME
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 40, 43, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 32)
    PORT_PARAMS_END
  },
  { // P1 - x4 NVME
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 44, 47, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 33)
    PORT_PARAMS_END
  }
};   // End of DXIO_PORT_DESCRIPTOR

ADDIN_CARD_PORTS NvmePLinksConfigEntry = {
    0xFF,
    8,
    &NvmePLinksConfig[0]
};

DXIO_PORT_DESCRIPTOR    RiserPLinksConfig[] = {
  { // P0 - x8 Right Riser Lower PCIe slot
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 0, 7, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 6)
    PORT_PARAMS_END
  },
  { // P1 - x16 RISER 1
    0,
    DXIO_ENGINE_INITIALIZER (DxioPcieEngine, 32, 47, 1),
    DXIO_PORT_INITIALIZER (DxioPortEnabled)                 // Port Present
    PORT_PARAMS_START
      PORT_PARAM (PP_LINK_ASPM, DxioAspmL1),
      PORT_PARAM (PP_SLOT_NUM, 8)
    PORT_PARAMS_END
  }
};   // End of DXIO_PORT_DESCRIPTOR

ADDIN_CARD_PORTS RiserPLinksConfigEntry = {
    0xFF,
    2,
    &RiserPLinksConfig[0]
};

DXIO_PORT_DESCRIPTOR    CommonGLinksConfig[1] = {0};   // End of DXIO_PORT_DESCRIPTOR

ADDIN_CARD_PORTS CommonGLinksConfigEntry = {
    0xFF,
    0,
    &CommonGLinksConfig[0]
};

EFI_STATUS
GetBackplanePresence (
  IN       CONST EFI_PEI_SERVICES   **PeiServices,
  IN OUT   UINT8                    *DeviceType
  );

EFI_STATUS
GetUbmFru (
  IN       CONST EFI_PEI_SERVICES   **PeiServices,
  IN       UINT8                     Backplane,
  IN       UINT8                     PsocMux
  );

EFI_STATUS
TopologyTableInsert  (
  IN       AMD_CPM_DXIO_TOPOLOGY_TABLE   *DxioTopologyTablePtr,
  IN       ADDIN_CARD_PORTS              *DxioTopologyAddInPorts
  )
{
  EFI_STATUS Status = EFI_SUCCESS;
  UINT8 LastPortNum;

  if ((DxioTopologyTablePtr == NULL) || (DxioTopologyAddInPorts == NULL)) {
    return EFI_INVALID_PARAMETER;
  }
  if (DxioTopologyAddInPorts->EntryNumber == 0)  {
    return EFI_INVALID_PARAMETER;
  }

  DEBUG ((EFI_D_ERROR, "%a Enter\n", __FUNCTION__));
   //Find the last port defined
    LastPortNum = 0;
    while (((DxioTopologyTablePtr->Port[LastPortNum].Flags & DESCRIPTOR_TERMINATE_LIST) == 0)
          && (LastPortNum < AMD_DXIO_PORT_DESCRIPTOR_SIZE))
      LastPortNum++;

    //Make sure we are not over the end of the list and that the entries we are adding will fit..
    if ((LastPortNum + DxioTopologyAddInPorts->EntryNumber) < AMD_DXIO_PORT_DESCRIPTOR_SIZE) {
      //Clear the terminate flag from the last entry
      DxioTopologyTablePtr->Port[LastPortNum].Flags &= ~(DESCRIPTOR_TERMINATE_LIST);
      //Insert ports at the end of list
      CopyMem (&DxioTopologyTablePtr->Port[LastPortNum+1], \
              DxioTopologyAddInPorts->DxioDescriptor, \
              sizeof (DXIO_PORT_DESCRIPTOR) * (DxioTopologyAddInPorts->EntryNumber) \
              );
      LastPortNum = LastPortNum + DxioTopologyAddInPorts->EntryNumber;
      //Terminate DXIO topology table at the new endpoint.
      DxioTopologyTablePtr->Port[LastPortNum].Flags |= DESCRIPTOR_TERMINATE_LIST;
    }

  return Status;
}

/*----------------------------------------------------------------------------------------*/
/**
 * Detect Addin Card Devices Automatically
 *
 * This function patches PCIe Topology Override Table by detecting PCIe devices
 * automatically.
 *
 * @param[in]     LaneStartNum                Starting Lane number for the slot.
 * @param[in]     SlotInfo                    Slot information
 * @param[in]     DxioTopologyTablePtr        Pointer to Dxio Topology Table
 * @param[out]    LastPortNum                 Next topology port entry number
 *
 */

EFI_STATUS
TopologyTableUpdate (
  IN       EFI_PEI_SERVICES             **PeiServices,
  IN       UINT8                         LaneStartNum,
  IN       SLOT_INFO                     SlotInfo,
  IN       AMD_CPM_DXIO_TOPOLOGY_TABLE   *DxioTopologyTablePtr,
  OUT      UINT8                         *LastPortNum
  )
{
  EFI_STATUS Status = EFI_SUCCESS;
#if 0
  DXIO_PORT_DESCRIPTOR *DestDxioPortDescriptor;


  DestDxioPortDescriptor = &DxioTopologyTablePtr->Port[*LastPortNum];
   if (SlotInfo.Field.Sata == 1) {
//Add Sata
      DEBUG ((EFI_D_ERROR, "SATA x8 %04x\n"));
      CopyMem (DestDxioPortDescriptor, \
              SataEntry.DxioDescriptor, \
              sizeof (DXIO_PORT_DESCRIPTOR) * (SataEntry.EntryNumber) \
              );
      DxioTopologyTablePtr->Port[*LastPortNum].EngineData.StartLane = LaneStartNum;
      DxioTopologyTablePtr->Port[*LastPortNum].EngineData.EndLane = LaneStartNum + 7;


      *LastPortNum = *LastPortNum + SataEntry.EntryNumber;
    }
#endif
  return Status;
}

UINT8
CatIdentifier (VOID)
{
  UINT32    Value32 = 0;

  SmnRegisterRead (0, 0x5D268, &Value32);

  return (UINT8)((Value32 >> 22) & 0x3);
}

VOID
PcieEngineConfigOverride (
  AMD_CPM_DXIO_TOPOLOGY_TABLE       *DxioTopologyTablePtr,
  AMD_EEPROM_ROOT_TABLE             *AmdEepromRootTable,
  UINT8                             CatId
) {
  UINT8 i;
  UINT8 RevisionId;

  RevisionId = AmdEepromRootTable->PlatformId.RevisionId;

  for (i = 0; i < AMD_DXIO_PORT_DESCRIPTOR_SIZE; i++) {
    if (DxioTopologyTablePtr->Port[i].Flags == DESCRIPTOR_TERMINATE_LIST) {
      break;
    }
    if (!(DxioTopologyTablePtr->Port[i].EngineData.EngineType == DxioPcieEngine &&
        DxioTopologyTablePtr->Port[i].Port.PortPresent != DxioPortDisabled)) {
      continue;
    }
    if (DxioTopologyTablePtr->Port[i].Port.LinkSpeedCapability != PcieGenMaxSupported) {
      continue;
    }
    if ((RevisionId == 0) || ((RevisionId == 1) && (CatId == 1))) {
      DxioTopologyTablePtr->Port[i].Port.LinkSpeedCapability = PcieGen4;
    }
  }
}

VOID
CpmSlotInfoOverride (
  AMD_CPM_DXIO_TOPOLOGY_TABLE       *DxioTopologyTablePtr
) {
  UINT8                             i, j, k, LaneId, LinkType;
  UINT16                            ConfigType, ConfigMask;
  UINT16                            Data1, SlotCount = 0;
  UINTN                             BufSize;
  ADDIN_CARD_PORTS                  *TempLinksConfigEntry;
  DXIO_PORT_DESCRIPTOR              *TempLinksConfig;
  UINT8                             PortCnt;

  for (LinkType = 0; LinkType < MAXLINK; LinkType++) {

    Data1 = (LinkType == PLINK)? PcdGet16 (PcdCpmSlotInfoOverride) : PcdGet16 (PcdCpmSlotInfoS1Override);
    if (Data1 == 0xFFFF) {
      continue;
    }
    DEBUG ((EFI_D_INFO, "Socket0 %a SlotInfoOverride Value : %04x\n", ((LinkType == PLINK)?"PLink":"GLINK"), Data1));
    for (i = 0; i < (sizeof(SlotLanesMap) / sizeof(SLOT_LANES_MAP)); i++) {
      if ((SlotLanesMap[i].ConfigMask & Data1) == SlotLanesMap[i].ConfigMask) {
        continue;
      }

      // Check Slot config type
      ConfigMask = SlotLanesMap[i].ConfigMask;
      ConfigType = ConfigMask & Data1;
      while (ConfigMask != 0xF) {
        ConfigMask >>= 1;
        ConfigType >>= 1;
      }

      for (j = 0; j < (sizeof(SlotConfig) / sizeof(SLOT_CONFIG)); j++) {
        if (SlotConfig[j].ConfigType != (UINT8)ConfigType) {
          continue;
        }

        DEBUG ((EFI_D_INFO, " SlotConfig[%d].ConfigType:0x%x\n", j, SlotConfig[j].ConfigType));

        LaneId = SlotLanesMap[i].PGLink[LinkType].Start;
        if (SlotLanesMap[i].PGLink[LinkType].Start < SlotLanesMap[i].PGLink[LinkType].End) {
          for (k = 0; k < SlotConfig[j].ConfigEntry->EntryNumber; k++) {
            SlotConfig[j].ConfigEntry->DxioDescriptor[k].EngineData.StartLane = LaneId;
            SlotConfig[j].ConfigEntry->DxioDescriptor[k].EngineData.EndLane = LaneId + (SlotConfig[j].LaneNum[k] - 1);
            SlotConfig[j].ConfigEntry->DxioDescriptor[k].Port.SlotNum = ++SlotCount;
            LaneId += SlotConfig[j].LaneNum[k];
            DEBUG ((EFI_D_INFO, " Slot Override StartLane:%d EndLane:%d SlotNum:%d\n",
              SlotConfig[j].ConfigEntry->DxioDescriptor[k].EngineData.StartLane,
              SlotConfig[j].ConfigEntry->DxioDescriptor[k].EngineData.EndLane,
              SlotConfig[j].ConfigEntry->DxioDescriptor[k].Port.SlotNum));
          }
        } else {
          // If SlotLanesMap table define Start lane bigger than End lane. That will assign lanes number by reversal
          for (k = 0; k < SlotConfig[j].ConfigEntry->EntryNumber; k++) {
            SlotConfig[j].ConfigEntry->DxioDescriptor[k].EngineData.StartLane = LaneId;
            SlotConfig[j].ConfigEntry->DxioDescriptor[k].EngineData.EndLane = LaneId - (SlotConfig[j].LaneNum[k] - 1);
            SlotConfig[j].ConfigEntry->DxioDescriptor[k].Port.SlotNum = ++SlotCount;
            LaneId -= SlotConfig[j].LaneNum[k];
            DEBUG ((EFI_D_INFO, " Slot Override StartLane:%d EndLane:%d SlotNum:%d\n",
              SlotConfig[j].ConfigEntry->DxioDescriptor[k].EngineData.StartLane,
              SlotConfig[j].ConfigEntry->DxioDescriptor[k].EngineData.EndLane,
              SlotConfig[j].ConfigEntry->DxioDescriptor[k].Port.SlotNum));
          }
        }
        TopologyTableInsert (DxioTopologyTablePtr, SlotConfig[j].ConfigEntry);
        break;
      }
    }
  }

  // Used original slot setting if not overrided
  for (LinkType = 0; LinkType < MAXLINK; LinkType++) {
    PortCnt = 0;
    BufSize = (LinkType == PLINK)? sizeof (CommonPLinksConfig) : sizeof (CommonGLinksConfig);
    BufSize += sizeof(ADDIN_CARD_PORTS);

    TempLinksConfigEntry = AllocateZeroPool (BufSize);
    TempLinksConfig = (DXIO_PORT_DESCRIPTOR *)((UINTN)TempLinksConfigEntry + sizeof (ADDIN_CARD_PORTS));

    TempLinksConfigEntry->CardId = 0xFF;
    TempLinksConfigEntry->DxioDescriptor = TempLinksConfig;

    Data1 = (LinkType == PLINK)? PcdGet16 (PcdCpmSlotInfoOverride) : PcdGet16 (PcdCpmSlotInfoS1Override);
    for (i = 0; i < (sizeof(SlotLanesMap) / sizeof(SLOT_LANES_MAP)); i++) {
      UINT8             LaneStart, LaneEnd;
      ADDIN_CARD_PORTS  *CommonLinksConfigEntry;

      if ((SlotLanesMap[i].ConfigMask & Data1) != SlotLanesMap[i].ConfigMask) {
        continue;
      }

      // If slot did not overrided. Keep original comon PGLinks config
      if (SlotLanesMap[i].PGLink[LinkType].Start < SlotLanesMap[i].PGLink[LinkType].End) {
        LaneStart = SlotLanesMap[i].PGLink[LinkType].Start;
        LaneEnd = SlotLanesMap[i].PGLink[LinkType].End;
      } else {
        LaneStart = SlotLanesMap[i].PGLink[LinkType].End;
        LaneEnd = SlotLanesMap[i].PGLink[LinkType].Start;
      }

      CommonLinksConfigEntry = (LinkType == PLINK)? &CommonPLinksConfigEntry : &CommonGLinksConfigEntry;

      for (j = 0; j < CommonLinksConfigEntry->EntryNumber; j++) {
        if (LaneStart <= CommonLinksConfigEntry->DxioDescriptor[j].EngineData.StartLane &&
            LaneEnd >= CommonLinksConfigEntry->DxioDescriptor[j].EngineData.EndLane) {
          LibAmdMemCopy (&TempLinksConfigEntry->DxioDescriptor[PortCnt], &CommonLinksConfigEntry->DxioDescriptor[j], sizeof (DXIO_PORT_DESCRIPTOR), NULL);
          PortCnt++;
        }
      }
    }
    if (PortCnt) {
      TempLinksConfigEntry->EntryNumber = PortCnt;
      TopologyTableInsert (DxioTopologyTablePtr, TempLinksConfigEntry);
    }
  }
}

/*----------------------------------------------------------------------------------------*/
/**
 * Purico EVT1/2 uses a different I2c address for backplane GPIO expanders
 *
 * This function updates the GPIO expander port parameter PP_I2C_EXPANDER_ADDRESS
 * to use the address used by EVT1/2 Purico boards
 *
 * @param[in]     UbmTopology    Pointer to UBM backplane definition
 *
 * @retval        EFI_SUCCESS    UBM backplane definition updated successfully
 * @retval        EFI_ERROR      UBM backplane definition update failed
 *
 */
EFI_STATUS
UpdateEvtGpioExpanderAddr(
  IN ADDIN_CARD_PORTS *UbmTopology
  )
{
  PORT_PARAM *PhyParam;
  UINT8       i;

  for(i = 0; i < UbmTopology->EntryNumber; i++) {
    PhyParam = &UbmTopology->DxioDescriptor[i].PortParams.PhyParam[0];
    while(PhyParam->ParamType != 0) {
      if(PhyParam->ParamType == PP_I2C_EXPANDER_ADDRESS && PhyParam->ParamValue == 0x21 ) {
        PhyParam->ParamValue = 0x20;
      }
      PhyParam++;
    }
  }

  return EFI_SUCCESS;
}

/*----------------------------------------------------------------------------------------*/
/**
 * CPM Override Function After AMD CPM Table PPI
 *
 * This function updates CPM OEM Tables according to setup options or the value to be detected
 * on run time after AMD CPM Table PPI is installed.
 *
 * @param[in]     PeiServices    Pointer to Pei Services
 *
 * @retval        EFI_SUCCESS    Function initialized successfully
 * @retval        EFI_ERROR      Function failed (see error for more details)
 *
 */
EFI_STATUS
EFIAPI
AmdCpmTableOverride (
  IN       EFI_PEI_SERVICES       **PeiServices
  )
{
  EFI_STATUS                        Status = EFI_SUCCESS;
  AMD_CPM_TABLE_PPI                 *AmdCpmTablePpi;
  AMD_CPM_DXIO_TOPOLOGY_TABLE       *DxioTopologyTableS0Ptr;
  ADDIN_CARD_PORTS                  *DxioTopologyAddInPortsPtr = NULL;
  UINT8                             CatId = 0;
  AMD_EEPROM_ROOT_TABLE             *AmdEepromRootTable;
  EFI_PEI_AMDBOARDID_PPI            *AmdBoardIdPpi;
  EFI_PEI_PCA9535A_PPI              *Pca9535aPpi;
  UINT16                            SlotCount;
  UINT8                             SataSgpioEnable;

  UINT16 Data1;
//SLOT_INFO SlotInfo;
  UINT8 BoardId, RevisionId;
  UINT8 BackPlaneType = 0;

   Status = (*PeiServices)->LocatePpi (
                           (CONST EFI_PEI_SERVICES **)PeiServices,
                           &gAmdCpmTablePpiGuid,
                           0,
                           NULL,
                           (VOID**)&AmdCpmTablePpi
                           );
  if (EFI_ERROR (Status)) {
    return Status;
  }

  //override DXIO topology table here.
  DxioTopologyTableS0Ptr = AmdCpmTablePpi->CommonFunction.GetTablePtr2 (AmdCpmTablePpi, CPM_SIGNATURE_DXIO_TOPOLOGY);


  Status = (*PeiServices)->LocatePpi (
     (CONST EFI_PEI_SERVICES **)PeiServices,
     &gAmdBoardIdPpiGuid,
     0,
     NULL,
     (VOID **)&AmdBoardIdPpi
     );

  AmdEepromRootTable = AmdBoardIdPpi->AmdEepromRootTable;

  BoardId = AmdEepromRootTable->PlatformId.BoardId;
  RevisionId = AmdEepromRootTable->PlatformId.RevisionId;
  CatId = CatIdentifier();

  DEBUG ((EFI_D_ERROR, "Purico Board ID: %04x\n", BoardId));
  DEBUG ((EFI_D_ERROR, "Purico Board revision ID: %04x\n", RevisionId));
  DEBUG ((EFI_D_ERROR, "Cat Identifier: 0x%x\n", CatId));

  Status = (*PeiServices)->LocatePpi (
     (CONST EFI_PEI_SERVICES **)PeiServices,
     &gPca9535aPpiGuid,
     0,
     NULL,
     (VOID **)&Pca9535aPpi
     );

    Pca9535aPpi->Get ((CONST EFI_PEI_SERVICES **)PeiServices, 0, 0x20, 0, &Data1);
    DEBUG ((EFI_D_ERROR, "I2cPCA9535A @0x20 Input Rx read out : %04x\n", Data1));

  CpmSlotInfoOverride (DxioTopologyTableS0Ptr);

  // Socket 0 PCIE Engine override
  PcieEngineConfigOverride (DxioTopologyTableS0Ptr, AmdEepromRootTable, CatId);

  SataSgpioEnable = PcdGet8(PcdSataSgpioMultiDieEnable);
  DEBUG((EFI_D_INFO, "SataSgpioEnable = 0x%x\n", SataSgpioEnable));

  // Detect whether the backplane is present
  GetBackplanePresence((CONST EFI_PEI_SERVICES **)PeiServices, &BackPlaneType);
  DEBUG ((EFI_D_ERROR, "BackPlaneType = 0x%x\n", BackPlaneType));
  switch (BackPlaneType) {
    case STANDARD_NVME_SATA_UBM:
      if(SataSgpioEnable) {
        // Swap SATA lanes to SGPIO SATA
        DEBUG((EFI_D_INFO, "Installing SATA SGPIO support\n"));
        DxioTopologyAddInPortsPtr = &PuricoSgpioStandardEntry;
      } else {
        DxioTopologyAddInPortsPtr = &PuricoUBMStandardEntry;
      }

      if(RevisionId == 1) {
        UpdateEvtGpioExpanderAddr(DxioTopologyAddInPortsPtr);
      }

      DxioTopologyTableS0Ptr->Port[1].Port.SlotNum = 6; // P0 - x8 RISER 2
      DxioTopologyTableS0Ptr->Port[2].Port.SlotNum = 7; // P1 - x16 RISER 1
      DxioTopologyTableS0Ptr->Port[4].Port.SlotNum = 8; // P3 - x16 RISER 3
      SlotCount = 9;
      // Sata uses 8 slots per x8 link
      DxioTopologyAddInPortsPtr->DxioDescriptor[0].Port.SlotNum = SlotCount;
      SlotCount += 4;
      DxioTopologyAddInPortsPtr->DxioDescriptor[1].Port.SlotNum = SlotCount;
      SlotCount += 4;
      // Nvme uses 4 slots per x16 link
      for (UINT8 q = 1; q < DxioTopologyAddInPortsPtr->EntryNumber ; q++) {
        DxioTopologyAddInPortsPtr->DxioDescriptor[q].Port.SlotNum = SlotCount;
        SlotCount += 1;
      }
      TopologyTableInsert (DxioTopologyTableS0Ptr, &RiserPLinksConfigEntry);
      DEBUG ((EFI_D_ERROR, "Found Standard BackPlane\n"));
      break;

    case STANDARD_HBA:
      DxioTopologyAddInPortsPtr = &PuricoHbaStandardEntry;

      if(RevisionId == 1) {
        UpdateEvtGpioExpanderAddr(DxioTopologyAddInPortsPtr);
      }

      DxioTopologyTableS0Ptr->Port[1].Port.SlotNum = 6; // P0 - x8 RISER 2
      DxioTopologyTableS0Ptr->Port[2].Port.SlotNum = 7; // P1 - x16 RISER 1
      DxioTopologyTableS0Ptr->Port[4].Port.SlotNum = 8; // P3 - x16 RISER 3
      SlotCount = 9;

      // Nvme uses 4 slots per x16 link
      for (UINT8 q = 1; q < DxioTopologyAddInPortsPtr->EntryNumber ; q++) {
        DxioTopologyAddInPortsPtr->DxioDescriptor[q].Port.SlotNum = SlotCount;
        SlotCount += 1;
      }
      TopologyTableInsert (DxioTopologyTableS0Ptr, &RiserPLinksConfigEntry);
      DEBUG ((EFI_D_ERROR, "Found HBA Standard BackPlane\n"));
      break;

    case FULL_NVME_UBM:
      if(RevisionId == 1) {
        UpdateEvtGpioExpanderAddr(&PuricoUBMFullNVMEEntry);
      }

      DxioTopologyAddInPortsPtr = &PuricoUBMFullNVMEEntry;

      DxioTopologyTableS0Ptr->Port[2].Port.SlotNum = 6; // P1 - x16 RISER 1
      SlotCount = 6;
      ++SlotCount;
      for (UINT8 q = 0; q < DxioTopologyAddInPortsPtr->EntryNumber ; q++) {
        DxioTopologyAddInPortsPtr->DxioDescriptor[q].Port.SlotNum = SlotCount;
        SlotCount += 1;
      }
      DEBUG ((EFI_D_ERROR, "Found Full NVMe BackPlane\n"));
      break;

    case FULL_SATA_UBM:
      if(SataSgpioEnable) {
        // Swap SATA lanes to SGPIO SATA
        DEBUG((EFI_D_INFO, "Installing SATA SGPIO support\n"));
        DxioTopologyAddInPortsPtr = &PuricoSgpioFullSATAEntry;
      } else {
        DxioTopologyAddInPortsPtr = &PuricoUBMFullSATAEntry;
      }

      if(RevisionId == 1) {
        UpdateEvtGpioExpanderAddr(DxioTopologyAddInPortsPtr);
      }

      DxioTopologyTableS0Ptr->Port[1].Port.SlotNum = 6;   // P0 - x8 RISER 2
      DxioTopologyTableS0Ptr->Port[2].Port.SlotNum = 7;   // P1 - x16 RISER 1
      DxioTopologyTableS0Ptr->Port[4].Port.SlotNum = 8;   // P3 - x16 RISER 3
      SlotCount = 11;
      ++SlotCount;
      for (UINT8 q = 0; q < DxioTopologyAddInPortsPtr->EntryNumber ; q++) {
        DxioTopologyAddInPortsPtr->DxioDescriptor[q].Port.SlotNum = SlotCount;
        SlotCount += 4;
      }
      TopologyTableInsert (DxioTopologyTableS0Ptr, &RiserPLinksConfigEntry);
      DEBUG ((EFI_D_ERROR, "Found Full SATA BackPlane\n"));
      break;
    default:
      DEBUG ((EFI_D_ERROR, "Backplane does not exist\n"));
      break;
  }

  // Add and overrid the backplane's info in DXIO topology table
  TopologyTableInsert (DxioTopologyTableS0Ptr, DxioTopologyAddInPortsPtr);

  return Status;
}

EFI_STATUS
UbmFruRead (
  IN       CONST EFI_PEI_SERVICES   **PeiServices,
  IN       UINTN                   BusSelect,
  IN       UINTN                   SlaveAddress,
  IN       UINT32                  Length,
  OUT      UINT8                   *Data
  )
{
  EFI_STATUS Status = EFI_SUCCESS;
  EFI_PEI_I2C_MASTER_PPI        *I2cMaster;
  EFI_I2C_REQUEST_PACKET        *RequestPacket;
  EFI_I2C_OPERATION             *Operation;
  UINT8                         PacketBuffer[sizeof (EFI_I2C_REQUEST_PACKET) + sizeof (EFI_I2C_OPERATION)];
  UINT8                         ReadAddress = 0;

  Status = (*PeiServices)->LocatePpi (
     PeiServices,
     &gEfiPeiI2cMasterPpiGuid,
     BusSelect,
     NULL,
     (VOID **)&I2cMaster
     );

  RequestPacket = (EFI_I2C_REQUEST_PACKET*)PacketBuffer;
  Operation   = RequestPacket->Operation;
  RequestPacket->OperationCount = 2;
  Operation[0].Flags            = 0;
  Operation[0].LengthInBytes    = sizeof (UINT8);
  Operation[0].Buffer           = &ReadAddress;
  Operation[1].Flags            = I2C_FLAG_READ;
  Operation[1].LengthInBytes    = Length;
  Operation[1].Buffer           = Data;

  Status = I2cMaster->StartRequest (I2cMaster, SlaveAddress, RequestPacket);

  return Status;
}

VOID
PrintFru(
  IN       UINT8                   *Buffer,
  IN       UINT32                  Count
  )
{
  UINT32  Index;
  UINT32  DataItemCount;

  DEBUG((EFI_D_INFO, "------- UBM FRU Dump Start -------\n"));
  DataItemCount = 0;
  for (Index = 0; Index < Count; ) {
      DEBUG((EFI_D_INFO, "%02x ", *((UINT8 *) Buffer + Index)));
      Index += 1;

    if (++DataItemCount >= 16) {
      DEBUG((EFI_D_INFO, "\n"));
      DataItemCount = 0;
    }
  }
  DEBUG((EFI_D_INFO, "------- UBM FRU Dump End -------\n"));
}

// GetBackplanePresence helper function
EFI_STATUS
DetectFullNvme (
  IN CONST EFI_PEI_SERVICES     **PeiServices,
  IN       EFI_PEI_PCA9545A_PPI  *Pca9545aPpi,
  IN       UINT8                  SocI2cNum,
  IN       UINT8                  I2cMuxAddr
  )
{
  EFI_STATUS Status;
  UINT8      Pca9545Data  = 0;
  UINT8      UbmFruDetect = 0;

  Status = Pca9545aPpi->Set (PeiServices, SocI2cNum, I2cMuxAddr, BIT1); // U72 Channel 1
  if (!EFI_ERROR(Status)) {
    Status = Pca9545aPpi->Get (PeiServices, SocI2cNum, 0x71, &Pca9545Data);
    if (!EFI_ERROR(Status)) {
      Status = Pca9545aPpi->Set (PeiServices, SocI2cNum, 0x71, BIT0); // U70 Channel 0
      if (!EFI_ERROR(Status)) {
        // Get UBM FRU's response from Channel 0
        Status = Pca9545aPpi->Get (PeiServices, SocI2cNum, 0x57, &UbmFruDetect);
        DEBUG ((EFI_D_ERROR, "Get U70 Channel 0 - Status = %r\n", Status));
        if (!EFI_ERROR(Status)) {
          Status = EFI_SUCCESS;
        } else {
          Status = EFI_NOT_FOUND;
        }
      }
      // Reset U70 control register to original value
      Pca9545aPpi->Set (PeiServices, SocI2cNum, 0x71, Pca9545Data);
    }
  }

  return Status;
}

// GetBackplanePresence helper function
EFI_STATUS
DetectFullSata (
  IN CONST EFI_PEI_SERVICES     **PeiServices,
  IN       EFI_PEI_PCA9545A_PPI  *Pca9545aPpi,
  IN       UINT8                  SocI2cNum,
  IN       UINT8                  I2cMuxAddr
  )
{
  EFI_STATUS Status;
  UINT8      Pca9545Data  = 0;
  UINT8      UbmFruDetect = 0;

  Status = Pca9545aPpi->Set (PeiServices, SocI2cNum, I2cMuxAddr, BIT2); // U72 Channel 2
  if (!EFI_ERROR(Status)) {
    Status = Pca9545aPpi->Get (PeiServices, SocI2cNum, 0x70, &Pca9545Data);
    if (!EFI_ERROR(Status)) {
      Status = Pca9545aPpi->Set (PeiServices, SocI2cNum, 0x70, BIT0); // U108 Channel 0
      if (!EFI_ERROR(Status)) {
        // Get UBM FRU's response from Channel 0
        Status = Pca9545aPpi->Get (PeiServices, SocI2cNum, 0x57, &UbmFruDetect);
        DEBUG ((EFI_D_ERROR, "Get U108 Channel 0 - Status = %r\n", Status));
        // If no FRU found then backplane is Full SATA
        if (EFI_ERROR(Status)) {
          Status = EFI_SUCCESS;
        } else {
          Status = EFI_NOT_FOUND;
        }
      }
      // Reset U108 control register to original value
      Pca9545aPpi->Set (PeiServices, SocI2cNum, 0x70, Pca9545Data);
    }
  }

  return Status;
}

// GetBackplanePresence helper function
EFI_STATUS
DetectHbaStandard (
  IN CONST EFI_PEI_SERVICES     **PeiServices,
  IN       EFI_PEI_PCA9545A_PPI  *Pca9545aPpi,
  IN       UINT8                  SocI2cNum,
  IN       UINT8                  I2cMuxAddr
  )
{
  EFI_STATUS Status;
  UINT8      Pca9545Data  = 0;
  UINT8      UbmFruDetect = 0;

  Status = Pca9545aPpi->Set (PeiServices, SocI2cNum, I2cMuxAddr, BIT1); // U72 Channel 1
  if (!EFI_ERROR(Status)) {
    Status = Pca9545aPpi->Get (PeiServices, SocI2cNum, 0x70, &Pca9545Data);
    if (!EFI_ERROR(Status)) {
      Status = Pca9545aPpi->Set (PeiServices, SocI2cNum, 0x70, BIT0); // U69 Channel 0
      if (!EFI_ERROR(Status)) {
        // Get UBM FRU's response from Channel 0
        Status = Pca9545aPpi->Get (PeiServices, SocI2cNum, 0x57, &UbmFruDetect);
        DEBUG ((EFI_D_ERROR, "Get U69 Channel 0 - Status = %r\n", Status));
        if (EFI_ERROR(Status)) {
          Status = EFI_SUCCESS;
        } else {
          Status = EFI_NOT_FOUND;
        }
      }
      // Reset U70 control register to original value
      Pca9545aPpi->Set (PeiServices, SocI2cNum, 0x70, Pca9545Data);
    }
  }

  return Status;

}

EFI_STATUS
GetBackplanePresence (
  IN       CONST EFI_PEI_SERVICES   **PeiServices,
  IN OUT   UINT8                    *DeviceType
  )
{
  EFI_STATUS            Status = EFI_SUCCESS;
  EFI_PEI_PCA9545A_PPI *Pca9545aPpi;
  UINT8                 Pca9545Data;
  UINT8                 SocI2cNum;
  UINT8                 I2cMuxAddr;

  Status = (*PeiServices)->LocatePpi (
                             PeiServices,
                             &gPca9545aPpiGuid,
                             0,
                             NULL,
                             (VOID **)&Pca9545aPpi
                             );
  if (EFI_ERROR(Status)) {
    return Status;
  }

  SocI2cNum = 4; // I2C 4 Controller

  //Save the Control Register of the I2C mux
  Pca9545Data = 0;

  I2cMuxAddr = 0x73;
  Status = Pca9545aPpi->Get (PeiServices, SocI2cNum, I2cMuxAddr, &Pca9545Data);

  if (EFI_ERROR(Status)) {
    DEBUG ((EFI_D_ERROR, "Nothing responded at I2c address 0x73, so exit with failure status\n"));
    return Status;
  }

  // Get Back Plane's type => 1: STANDARD / 2:STANDARD_NVME  3: FULL NVME / 5: FULL SATA
  *DeviceType = STANDARD_NVME_SATA_UBM; // Default to standard config
  if(DetectFullNvme(PeiServices, Pca9545aPpi, SocI2cNum, I2cMuxAddr) == EFI_SUCCESS) { // Full NVMe backplane detected
    *DeviceType = FULL_NVME_UBM;
  } else if(DetectFullSata(PeiServices, Pca9545aPpi, SocI2cNum, I2cMuxAddr) == EFI_SUCCESS) { // Full SATA backplane detected
    *DeviceType = FULL_SATA_UBM;
  } else if(DetectHbaStandard(PeiServices, Pca9545aPpi, SocI2cNum, I2cMuxAddr) == EFI_SUCCESS) { // Standard backplane with HBA card detected
    *DeviceType = STANDARD_HBA;
  }

  Pca9545aPpi->Set (PeiServices, SocI2cNum, I2cMuxAddr, Pca9545Data);

  return EFI_SUCCESS;
}

EFI_STATUS
GetUbmFru (
  IN       CONST EFI_PEI_SERVICES  **PeiServices,
  IN       UINT8                     Backplane,
  IN       UINT8                     PsocMux
  )
{
  EFI_STATUS            Status = EFI_SUCCESS;
  EFI_PEI_PCA9545A_PPI *Pca9545aPpi;
  UINT8                 Pca9545Data, Pca9545Data2;
  UINT8                 SocI2cNum;
  UINT8                 I2cMuxAddr;
  UINT8                 Fru[256];

  Status = (*PeiServices)->LocatePpi (
                             PeiServices,
                             &gPca9545aPpiGuid,
                             0,
                             NULL,
                             (VOID **)&Pca9545aPpi
                             );
  if (EFI_ERROR(Status)) {
    return Status;
  }

  SocI2cNum = 4; // I2C 4 Controller

  //Save the Control Register of the I2C mux
  Pca9545Data = 0;
  Pca9545Data2 = 0;

  I2cMuxAddr = 0x73;
  Status = Pca9545aPpi->Get (PeiServices, SocI2cNum, I2cMuxAddr, &Pca9545Data2);

  if (EFI_ERROR(Status)) {
    DEBUG ((EFI_D_ERROR, "Nothing responded at I2c address 0x73, exit with failure status\n"));
    return Status;
  }

  Status = Pca9545aPpi->Set (PeiServices, SocI2cNum, I2cMuxAddr, Backplane);
  DEBUG((EFI_D_INFO, "Pca9545aPpi->Set (backplane = 0x%x) = %r\n", Backplane, Status));
  if (!EFI_ERROR(Status)) {
    Status = Pca9545aPpi->Get (PeiServices, SocI2cNum, PsocMux, &Pca9545Data);
    DEBUG((EFI_D_INFO, "Pca9545aPpi->Get = %r\n", Status));
    if (!EFI_ERROR(Status)) {
      Status = Pca9545aPpi->Set (PeiServices, SocI2cNum, PsocMux, BIT0);
      DEBUG((EFI_D_INFO, "Pca9545aPpi->Set (Channel 0) = %r\n", Status));
      if (!EFI_ERROR(Status)) {
        Status = UbmFruRead(PeiServices, SocI2cNum, 0x57, 256, Fru);
        if(!EFI_ERROR(Status))
        {
          PrintFru(Fru, 256);
        }
      }

      Status = Pca9545aPpi->Set (PeiServices, SocI2cNum, PsocMux, BIT1);
      DEBUG((EFI_D_INFO, "Pca9545aPpi->Set (Channel 1) = %r\n", Status));
      if (!EFI_ERROR(Status)) {
        Status = UbmFruRead(PeiServices, SocI2cNum, 0x57, 256, Fru);
        if(!EFI_ERROR(Status))
        {
          PrintFru(Fru, 256);
        }
      }

      Status = Pca9545aPpi->Set (PeiServices, SocI2cNum, PsocMux, BIT2);
      DEBUG((EFI_D_INFO, "Pca9545aPpi->Set (Channel 2) = %r\n", Status));
      if (!EFI_ERROR(Status)) {
        Status = UbmFruRead(PeiServices, SocI2cNum, 0x57, 256, Fru);
        if(!EFI_ERROR(Status))
        {
          PrintFru(Fru, 256);
        }
      }

      Status = Pca9545aPpi->Set (PeiServices, SocI2cNum, PsocMux, BIT3);
      DEBUG((EFI_D_INFO, "Pca9545aPpi->Set (Channel 3) = %r\n", Status));
      if (!EFI_ERROR(Status)) {
        Status = UbmFruRead(PeiServices, SocI2cNum, 0x57, 256, Fru);
        if(!EFI_ERROR(Status))
        {
          PrintFru(Fru, 256);
        }
      }
    }
  }

  //Restore the Control Register of the I2C mux
  Pca9545aPpi->Set (PeiServices, SocI2cNum, PsocMux, Pca9545Data);
  Pca9545aPpi->Set (PeiServices, SocI2cNum, I2cMuxAddr, Pca9545Data2);

  return EFI_SUCCESS;
}
