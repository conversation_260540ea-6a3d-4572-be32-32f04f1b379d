/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */
#include "AmdCbsVariable.h"


VOID
CbsWriteDefalutValue (
  IN UINT8 *IfrData
  )
{
  CBS_CONFIG *Setup_Config;

  Setup_Config = (CBS_CONFIG *)IfrData;
  Setup_Config->Header.NewRecordOffset = OFFSET_OF (CBS_CONFIG, Reserved);
  Setup_Config->CbsComboFlag = 254;                                     // Combo CBS
  Setup_Config->CbsCpuSmtCtrl = 0xF;                                    // SMT Control
  Setup_Config->CbsCmnCpuReqMinFreq = 1800;                             // Requested CPU min frequency
  Setup_Config->CbsCmnCpuEnReqMinFreq = 0;                              // Enable Requested CPU min frequency
  Setup_Config->CbsCmnCpuRMSS = 1;                                      // REP-MOV/STOS Streaming
  Setup_Config->CbsCmnCpuGenWA05 = 0xFF;                                // RedirectForReturnDis
  Setup_Config->CbsCmnCpuPfeh = 3;                                      // Platform First Error Handling
  Setup_Config->CbsCmnCpuCpb = 1;                                       // Core Performance Boost
  Setup_Config->CbsCmnCpuGlobalCstateCtrl = 3;                          // Global C-state Control
  Setup_Config->CbsCmnGnbPowerSupplyIdleCtrl = 0xf;                     // Power Supply Idle Control
  Setup_Config->CbsCmnCpuStreamingStoresCtrl = 0xFF;                    // Streaming Stores Control
  Setup_Config->CbsDbgCpuLApicMode = 0xFF;                              // Local APIC Mode
  Setup_Config->CbsCmnCpuCstC1Ctrl = 3;                                 // ACPI _CST C1 Declaration
  Setup_Config->CbsCmnCpuCstC2Latency = 100;                            // ACPI CST C2 Latency
  Setup_Config->CbsCmnCpuMcaErrThreshEn = 0xFF;                         // MCA error thresh enable
  Setup_Config->CbsCmnCpuMcaErrThreshCount = 0xFF5;                     // MCA error thresh count
  Setup_Config->CbsCmnCpuMcaFruTextEn = 1;                              // MCA FruText
  Setup_Config->CbsCmnCpuSmuPspDebugMode = 3;                           // SMU and PSP Debug Mode
  Setup_Config->CbsCmnCpuPpinCtrl = 1;                                  // PPIN Opt-in
  Setup_Config->CbsCmnCpuSmee = 3;                                      // SMEE
  Setup_Config->CbsPspSevCtrl = 0;                                      // SEV Control
  Setup_Config->CbsCmnCpuSevAsidSpaceLimit = 1;                         // SEV-ES ASID Space Limit
  Setup_Config->CbsDbgCpuSnpMemCover = 0xFF;                            // SNP Memory (RMP Table) Coverage
  Setup_Config->CbsDbgCpuSnpMemSizeCover = 0x10;                        // Amount of Memory to Cover
  Setup_Config->CbsCmnCpu64BitMMIOCoverage = 0xFF;                      // RMP Coverage for 64Bit MMIO Ranges
  Setup_Config->CbsCmnCpu64BitMMIORmpS0RBMask = 0x01;                   // Socket0 RootBridge Mask for 64Bit MMIO RMP Coverage
  Setup_Config->CbsCmnCpu64BitMMIORmpS1RBMask = 0x00;                   // Socket1 RootBridge Mask for 64Bit MMIO RMP Coverage
  Setup_Config->CbsDbgCpuSplitRMP = 0xFF;                               // Split RMP Table
  Setup_Config->CbsDbgCpuSegmentedRMP = 0xFF;                           // Segmented RMP Table
  Setup_Config->CbsDbgCpuRmpSegmentSize = 0xFF;                         // RMP Segment Size
  Setup_Config->CbsCmnActionOnBistFailure = 0xFF;                       // Action on BIST Failure
  Setup_Config->CbsCmnCpuERMS = 0xFF;                                   // Enhanced REP MOVSB/STOSB (ERSM)
  Setup_Config->CbsCmnCpuLogTransparentErrors = 3;                      // Log Transparent Errors
  Setup_Config->CbsCmnCpuAvx512 = 0xFF;                                 // AVX512
  Setup_Config->CbsCmnCpuDisFstStrErmsb = 0xFF;                         // ERMSB Caching Behavior
  Setup_Config->CbsCmnCpuMonMwaitDis = 0xFF;                            // MONITOR and MWAIT disable
  Setup_Config->CbsCpuSpeculativeStoreModes = 0xFF;                     // CPU Speculative Store Modes
  Setup_Config->CbsCmnCpuFSRM = 0xFF;                                   // Fast Short REP MOVSB (FSRM)
  Setup_Config->CbsCmnCpuPauseCntSel_1_0 = 0xFF;                        // PauseCntSel_1_0
  Setup_Config->CbsCmnCpuPfReqThrEn = 0xFF;                             // Prefetch/Request Throttle
  Setup_Config->CbsCmnCmcNotificationType = 5;                          // CMC H/W Error Notification type
  Setup_Config->CbsCmnCpuScanDumpDbgEn = 0;                             // Scan Dump Debug Enable
  Setup_Config->CbsCmnCpuMcax64BankSupport = 0xFF;                      // MCAX 64 bank support
  Setup_Config->CbsCmnCpuAdaptiveAlloc = 0xFF;                          // Adaptive Allocation (AA)
  Setup_Config->CbsCpuLatencyUnderLoad = 0xFF;                          // Latency Under Load (LUL)
  Setup_Config->CbsCmnCoreTraceDumpEn = 0;                              // Core Trace Dump Enable
  Setup_Config->CbsCmnCpuFP512 = 0xFF;                                  // FP512
  Setup_Config->CbsCmnCpuAmdErmsbRepo = 0xFF;                           // AMD_ERMSB Reporting
  Setup_Config->CbsCmnCpuOcMode = 0;                                    // OC Mode
  Setup_Config->CbsCmnCpuDowncoreMode = 0;                              // DownCore Mode
  Setup_Config->CbsCpuLegalDisclaimer = 0;                              // Pstates Disclaimer
  Setup_Config->CbsCpuLegalDisclaimer1 = 0;                             // Pstates Disclaimer 1
  Setup_Config->CbsCpuPstCustomP0 = 2;                                  // Custom Pstate0
  Setup_Config->CbsCpuPst0Freq = 0;                                     // Pstate0 Freq (MHz)
  Setup_Config->CbsCpuCofP0 = 0;                                        // Frequency (MHz)
  Setup_Config->CbsCpuVoltageP0 = 0;                                    // Voltage (uV)
  Setup_Config->CbsCpuPst0Fid = 16;                                     // Pstate0 FID
  Setup_Config->CbsCpuPst0Vid = 0;                                      // Pstate0 VID
  Setup_Config->CbsCpuPstCustomP1 = 2;                                  // Custom Pstate1
  Setup_Config->CbsCpuCofP1 = 0;                                        // Frequency (MHz)
  Setup_Config->CbsCpuVoltageP1 = 0;                                    // Voltage (uV)
  Setup_Config->CbsCpuPst1Fid = 16;                                     // Pstate1 FID
  Setup_Config->CbsCpuPst1Vid = 255;                                    // Pstate1 VID
  Setup_Config->CbsCpuPstCustomP2 = 2;                                  // Custom Pstate2
  Setup_Config->CbsCpuCofP2 = 0;                                        // Frequency (MHz)
  Setup_Config->CbsCpuVoltageP2 = 0;                                    // Voltage (uV)
  Setup_Config->CbsCpuPst2Fid = 16;                                     // Pstate2 FID
  Setup_Config->CbsCpuPst2Vid = 255;                                    // Pstate2 VID
  Setup_Config->CbsCpuPstCustomP3 = 2;                                  // Custom Pstate3
  Setup_Config->CbsCpuCofP3 = 0;                                        // Frequency (MHz)
  Setup_Config->CbsCpuVoltageP3 = 0;                                    // Voltage (uV)
  Setup_Config->CbsCpuPst3Fid = 16;                                     // Pstate3 FID
  Setup_Config->CbsCpuPst3Vid = 255;                                    // Pstate3 VID
  Setup_Config->CbsCpuPstCustomP4 = 2;                                  // Custom Pstate4
  Setup_Config->CbsCpuCofP4 = 0;                                        // Frequency (MHz)
  Setup_Config->CbsCpuVoltageP4 = 0;                                    // Voltage (uV)
  Setup_Config->CbsCpuPst4Fid = 16;                                     // Pstate4 FID
  Setup_Config->CbsCpuPst4Vid = 255;                                    // Pstate4 VID
  Setup_Config->CbsCpuPstCustomP5 = 2;                                  // Custom Pstate5
  Setup_Config->CbsCpuCofP5 = 0;                                        // Frequency (MHz)
  Setup_Config->CbsCpuVoltageP5 = 0;                                    // Voltage (uV)
  Setup_Config->CbsCpuPst5Fid = 16;                                     // Pstate5 FID
  Setup_Config->CbsCpuPst5Vid = 255;                                    // Pstate5 VID
  Setup_Config->CbsCpuPstCustomP6 = 2;                                  // Custom Pstate6
  Setup_Config->CbsCpuCofP6 = 0;                                        // Frequency (MHz)
  Setup_Config->CbsCpuVoltageP6 = 0;                                    // Voltage (uV)
  Setup_Config->CbsCpuPst6Fid = 16;                                     // Pstate6 FID
  Setup_Config->CbsCpuPst6Vid = 255;                                    // Pstate6 VID
  Setup_Config->CbsCpuPstCustomP7 = 2;                                  // Custom Pstate7
  Setup_Config->CbsCpuCofP7 = 0;                                        // Frequency (MHz)
  Setup_Config->CbsCpuVoltageP7 = 0;                                    // Voltage (uV)
  Setup_Config->CbsCpuPst7Fid = 16;                                     // Pstate7 FID
  Setup_Config->CbsCpuPst7Vid = 255;                                    // Pstate7 VID
  Setup_Config->CbsCmnCpuCcd0DowncoreBitMap = 0;                        // CCD 0 DownCore Bitmap
  Setup_Config->CbsCmnCpuCcd1DowncoreBitMap = 0;                        // CCD 1 DownCore Bitmap
  Setup_Config->CbsCmnCpuCcd2DowncoreBitMap = 0;                        // CCD 2 DownCore Bitmap
  Setup_Config->CbsCmnCpuCcd3DowncoreBitMap = 0;                        // CCD 3 DownCore Bitmap
  Setup_Config->CbsCmnCpuCcd4DowncoreBitMap = 0;                        // CCD 4 DownCore Bitmap
  Setup_Config->CbsCmnCpuCcd5DowncoreBitMap = 0;                        // CCD 5 DownCore Bitmap
  Setup_Config->CbsCmnCpuCcd6DowncoreBitMap = 0;                        // CCD 6 DownCore Bitmap
  Setup_Config->CbsCmnCpuCcd7DowncoreBitMap = 0;                        // CCD 7 DownCore Bitmap
  Setup_Config->CbsCmnCpuCcd8DowncoreBitMap = 0;                        // CCD 8 DownCore Bitmap
  Setup_Config->CbsCmnCpuCcd9DowncoreBitMap = 0;                        // CCD 9 DownCore Bitmap
  Setup_Config->CbsCmnCpuCcd10DowncoreBitMap = 0;                       // CCD 10 DownCore Bitmap
  Setup_Config->CbsCmnCpuCcd11DowncoreBitMap = 0;                       // CCD 11 DownCore Bitmap
  Setup_Config->CbsCmnCpuCcd12DowncoreBitMap = 0;                       // CCD 12 DownCore Bitmap
  Setup_Config->CbsCmnCpuCcd13DowncoreBitMap = 0;                       // CCD 13 DownCore Bitmap
  Setup_Config->CbsCmnCpuCcd14DowncoreBitMap = 0;                       // CCD 14 DownCore Bitmap
  Setup_Config->CbsCmnCpuCcd15DowncoreBitMap = 0;                       // CCD 15 DownCore Bitmap
  Setup_Config->CbsCpuCcdCtrl = 0;                                      // CCD Control
  Setup_Config->CbsCpuCoreCtrl = 0;                                     // Core control
  Setup_Config->CbsCmnCpuL1StreamHwPrefetcher = 3;                      // L1 Stream HW Prefetcher
  Setup_Config->CbsCmnCpuL1StridePrefetcher = 3;                        // L1 Stride Prefetcher
  Setup_Config->CbsCmnCpuL1RegionPrefetcher = 3;                        // L1 Region Prefetcher
  Setup_Config->CbsCmnCpuL2StreamHwPrefetcher = 3;                      // L2 Stream HW Prefetcher
  Setup_Config->CbsCmnCpuL2UpDownPrefetcher = 3;                        // L2 Up/Down Prefetcher
  Setup_Config->CbsCmnCpuL1BurstPrefetchMode = 3;                       // L1 Burst Prefetch Mode
  Setup_Config->CbsDbgCpuGenCpuWdt = 3;                                 // Core Watchdog Timer Enable
  Setup_Config->CbsDbgCpuGenCpuWdtTimeout = 0xFFFF;                     // Core Watchdog Timer Interval
  Setup_Config->CbsDfCmnWdtInterval = 0xFF;                             // DF Watchdog Timer Interval
  Setup_Config->CbsDfCmnExtIpSyncFloodProp = 0xFF;                      // Disable DF to external IP SyncFloodPropagation
  Setup_Config->CbsDfCmnDisSyncFloodProp = 0xFF;                        // Sync Flood Propagation to DF Components
  Setup_Config->CbsDfCmnFreezeQueueError = 3;                           // Freeze DF module queues on error
  Setup_Config->CbsDfCmnCc6MemEncryption = 3;                           // CC6 memory region encryption
  Setup_Config->CbsDfCmnCcdBwThrottleLv = 0xFF;                         // CCD B/W Balance Throttle Level
  Setup_Config->CbsDfDbgNumPciSegments = 0xFFFFFFFF;                    // Number of PCI Segments
  Setup_Config->CbsDfCmnCcmThrot = 0xFF;                                // CCM Throttler
  Setup_Config->CbsDfCmnFineThrotHeavy = 0;                             // MemReqBandwidthControl[FineThrotHeavy]
  Setup_Config->CbsDfCmnFineThrotLight = 0;                             // MemReqBandwidthControl[FineThrotLight]
  Setup_Config->CbsDfCmnCleanVicFtiCmdBal = 0xFF;                       // Clean Victim FTI Cmd Balancing
  Setup_Config->CbsDfCmnReqvReqNDImbThr = 0xFF;                         // CCMConfig5[ReqvReqNDImbThr]
  Setup_Config->CbsDfCmnCxlStronglyOrderedWrites = 0;                   // CXL Strongly Ordered Writes
  Setup_Config->CbsDfCmnEnhancedPartWr = 0xFF;                          // Enhanced Partial Writes to Same Address
  Setup_Config->CbsDfCmnDramNps = 7;                                    // NUMA nodes per socket
  Setup_Config->CbsDfCmnMemIntlv = 7;                                   // Memory interleaving
  Setup_Config->CbsDfCmnMixedInterleavedMode = 0xFF;                    // Mixed interleaved mode
  Setup_Config->CbsDfCmnCxlMemIntlv = 0xFF;                             // CXL Memory interleaving
  Setup_Config->CbsDfCnliSublinkInterleaving = 0xFF;                    // CXL Sublink interleaving
  Setup_Config->CbsDfCmnDramMapInversion = 3;                           // DRAM map inversion
  Setup_Config->CbsDfCmnCc6AllocationScheme = 0xFF;                     // Location of private memory regions
  Setup_Config->CbsDfCmnAcpiSratL3Numa = 255;                           // ACPI SRAT L3 Cache As NUMA Domain
  Setup_Config->CbsDfCmnAcpiSlitDistCtrl = 0xFF;                        // ACPI SLIT Distance Control
  Setup_Config->CbsDfCmnAcpiSlitRemoteFar = 255;                        // ACPI SLIT remote relative distance
  Setup_Config->CbsDfCmnAcpiSlitVirtualDist = 11;                       // ACPI SLIT virtual distance
  Setup_Config->CbsDfCmnAcpiSlitLclDist = 12;                           // ACPI SLIT same socket distance
  Setup_Config->CbsDfCmnAcpiSlitRmtDist = 32;                           // ACPI SLIT remote socket distance
  Setup_Config->CbsDfCmnAcpiSlitCxlLcl = 50;                            // ACPI SLIT local CXL distance
  Setup_Config->CbsDfCmnAcpiSlitCxlRmt = 60;                            // ACPI SLIT remote CXL distance
  Setup_Config->CbsDfCmnGmiEncryption = 3;                              // GMI encryption control
  Setup_Config->CbsDfCmnXGmiEncryption = 3;                             // xGMI encryption control
  Setup_Config->CbsDfDbgXgmiLinkCfg = 2;                                // xGMI Link Configuration
  Setup_Config->CbsDfCmn4LinkMaxXgmiSpeed = 0xFF;                       // 4-link xGMI max speed
  Setup_Config->CbsDfCmn3LinkMaxXgmiSpeed = 0xFF;                       // 3-link xGMI max speed
  Setup_Config->CbsDfXgmiCrcScale = 7;                                  // xGMI CRC Scale
  Setup_Config->CbsDfXgmiCrcThreshold = 25;                             // xGMI CRC Threshold
  Setup_Config->CbsDfXgmiPresetControl = 1;                             // xGMI Preset Control
  Setup_Config->CbsDfXgmiTrainingErrMask = 0xff;                        // xGMI Training Err Mask
  Setup_Config->CbsDfXgmiPresetP11 = 0x3000;                            // Preset P11 (APCB)
  Setup_Config->CbsDfXgmiCmn1P11 = 0;                                   // Preset P11 Cmn1
  Setup_Config->CbsDfXgmiCnP11 = 0x30;                                  // Preset P11 Cn
  Setup_Config->CbsDfXgmiCnp1P11 = 0;                                   // Preset P11 Cnp1
  Setup_Config->CbsDfXgmiPresetP12 = 0x3000;                            // Preset P12 (APCB)
  Setup_Config->CbsDfXgmiCmn1P12 = 0;                                   // Preset P12 Cmn1
  Setup_Config->CbsDfXgmiCnP12 = 0x30;                                  // Preset P12 Cn
  Setup_Config->CbsDfXgmiCnp1P12 = 0;                                   // Preset P12 Cnp1
  Setup_Config->CbsDfXgmiPresetP13 = 0x3000;                            // Preset P13 (APCB)
  Setup_Config->CbsDfXgmiCmn1P13 = 0;                                   // Preset P13 Cmn1
  Setup_Config->CbsDfXgmiCnP13 = 0x30;                                  // Preset P13 Cn
  Setup_Config->CbsDfXgmiCnp1P13 = 0;                                   // Preset P13 Cnp1
  Setup_Config->CbsDfXgmiPresetP14 = 0x3000;                            // Preset P14 (APCB)
  Setup_Config->CbsDfXgmiCmn1P14 = 0;                                   // Preset P14 Cmn1
  Setup_Config->CbsDfXgmiCnP14 = 0x30;                                  // Preset P14 Cn
  Setup_Config->CbsDfXgmiCnp1P14 = 0;                                   // Preset P14 Cnp1
  Setup_Config->CbsDfXgmiPresetP15 = 0x3000;                            // Preset P15 (APCB)
  Setup_Config->CbsDfXgmiCmn1P15 = 0;                                   // Preset P15 Cmn1
  Setup_Config->CbsDfXgmiCnP15 = 0x30;                                  // Preset P15 Cn
  Setup_Config->CbsDfXgmiCnp1P15 = 0;                                   // Preset P15 Cnp1
  Setup_Config->CbsDfXgmiInitPresetS0L0 = 0x4444;                       // Initial Preset Socket 0 Link 0 Pstate0/1/2/3 (APCB)
  Setup_Config->CbsDfXgmiInitPresetS0L0P0 = 0x4;                        // Initial Preset Socket 0 Link 0 Pstate0
  Setup_Config->CbsDfXgmiInitPresetS0L0P1 = 0x4;                        // Initial Preset Socket 0 Link 0 Pstate1
  Setup_Config->CbsDfXgmiInitPresetS0L0P2 = 0x4;                        // Initial Preset Socket 0 Link 0 Pstate2
  Setup_Config->CbsDfXgmiInitPresetS0L0P3 = 0x4;                        // Initial Preset Socket 0 Link 0 Pstate3
  Setup_Config->CbsDfXgmiInitPresetS0L1 = 0x4444;                       // Initial Preset Socket 0 Link 1 Pstate0/1/2/3 (APCB)
  Setup_Config->CbsDfXgmiInitPresetS0L1P0 = 0x4;                        // Initial Preset Socket 0 Link 1 Pstate0
  Setup_Config->CbsDfXgmiInitPresetS0L1P1 = 0x4;                        // Initial Preset Socket 0 Link 1 Pstate1
  Setup_Config->CbsDfXgmiInitPresetS0L1P2 = 0x4;                        // Initial Preset Socket 0 Link 1 Pstate2
  Setup_Config->CbsDfXgmiInitPresetS0L1P3 = 0x4;                        // Initial Preset Socket 0 Link 1 Pstate3
  Setup_Config->CbsDfXgmiInitPresetS0L2 = 0x4444;                       // Initial Preset Socket 0 Link 2 Pstate0/1/2/3 (APCB)
  Setup_Config->CbsDfXgmiInitPresetS0L2P0 = 0x4;                        // Initial Preset Socket 0 Link 2 Pstate0
  Setup_Config->CbsDfXgmiInitPresetS0L2P1 = 0x4;                        // Initial Preset Socket 0 Link 2 Pstate1
  Setup_Config->CbsDfXgmiInitPresetS0L2P2 = 0x4;                        // Initial Preset Socket 0 Link 2 Pstate2
  Setup_Config->CbsDfXgmiInitPresetS0L2P3 = 0x4;                        // Initial Preset Socket 0 Link 2 Pstate3
  Setup_Config->CbsDfXgmiInitPresetS0L3 = 0x4444;                       // Initial Preset Socket 0 Link 3 Pstate0/1/2/3 (APCB)
  Setup_Config->CbsDfXgmiInitPresetS0L3P0 = 0x4;                        // Initial Preset Socket 0 Link 3 Pstate0
  Setup_Config->CbsDfXgmiInitPresetS0L3P1 = 0x4;                        // Initial Preset Socket 0 Link 3 Pstate1
  Setup_Config->CbsDfXgmiInitPresetS0L3P2 = 0x4;                        // Initial Preset Socket 0 Link 3 Pstate2
  Setup_Config->CbsDfXgmiInitPresetS0L3P3 = 0x4;                        // Initial Preset Socket 0 Link 3 Pstate3
  Setup_Config->CbsDfXgmiInitPresetS1L0 = 0x4444;                       // Initial Preset Socket 1 Link 0 Pstate0/1/2/3 (APCB)
  Setup_Config->CbsDfXgmiInitPresetS1L0P0 = 0x4;                        // Initial Preset Socket 1 Link 0 Pstate0
  Setup_Config->CbsDfXgmiInitPresetS1L0P1 = 0x4;                        // Initial Preset Socket 1 Link 0 Pstate1
  Setup_Config->CbsDfXgmiInitPresetS1L0P2 = 0x4;                        // Initial Preset Socket 1 Link 0 Pstate2
  Setup_Config->CbsDfXgmiInitPresetS1L0P3 = 0x4;                        // Initial Preset Socket 1 Link 0 Pstate3
  Setup_Config->CbsDfXgmiInitPresetS1L1 = 0x4444;                       // Initial Preset Socket 1 Link 1 Pstate0/1/2/3 (APCB)
  Setup_Config->CbsDfXgmiInitPresetS1L1P0 = 0x4;                        // Initial Preset Socket 1 Link 1 Pstate0
  Setup_Config->CbsDfXgmiInitPresetS1L1P1 = 0x4;                        // Initial Preset Socket 1 Link 1 Pstate1
  Setup_Config->CbsDfXgmiInitPresetS1L1P2 = 0x4;                        // Initial Preset Socket 1 Link 1 Pstate2
  Setup_Config->CbsDfXgmiInitPresetS1L1P3 = 0x4;                        // Initial Preset Socket 1 Link 1 Pstate3
  Setup_Config->CbsDfXgmiInitPresetS1L2 = 0x4444;                       // Initial Preset Socket 1 Link 2 Pstate0/1/2/3 (APCB)
  Setup_Config->CbsDfXgmiInitPresetS1L2P0 = 0x4;                        // Initial Preset Socket 1 Link 2 Pstate0
  Setup_Config->CbsDfXgmiInitPresetS1L2P1 = 0x4;                        // Initial Preset Socket 1 Link 2 Pstate1
  Setup_Config->CbsDfXgmiInitPresetS1L2P2 = 0x4;                        // Initial Preset Socket 1 Link 2 Pstate2
  Setup_Config->CbsDfXgmiInitPresetS1L2P3 = 0x4;                        // Initial Preset Socket 1 Link 2 Pstate3
  Setup_Config->CbsDfXgmiInitPresetS1L3 = 0x4444;                       // Initial Preset Socket 1 Link 3 Pstate0/1/2/3 (APCB)
  Setup_Config->CbsDfXgmiInitPresetS1L3P0 = 0x4;                        // Initial Preset Socket 1 Link 3 Pstate0
  Setup_Config->CbsDfXgmiInitPresetS1L3P1 = 0x4;                        // Initial Preset Socket 1 Link 3 Pstate1
  Setup_Config->CbsDfXgmiInitPresetS1L3P2 = 0x4;                        // Initial Preset Socket 1 Link 3 Pstate2
  Setup_Config->CbsDfXgmiInitPresetS1L3P3 = 0x4;                        // Initial Preset Socket 1 Link 3 Pstate3
  Setup_Config->CbsDfXgmiTxeqS0L0P01 = 0x007A007A;                      // TXEQ Search Mask Socket 0 Link 0 Pstate0/1 (APCB)
  Setup_Config->CbsDfXgmiTxeqS0L0P23 = 0x007A007A;                      // TXEQ Search Mask Socket 0 Link 0 Pstate2/3 (APCB)
  Setup_Config->CbsDfXgmiTxeqS0L0P0 = 0x7A;                             // TXEQ Search Mask Socket 0 Link 0 Pstate0
  Setup_Config->CbsDfXgmiTxeqS0L0P1 = 0x7A;                             // TXEQ Search Mask Socket 0 Link 0 Pstate1
  Setup_Config->CbsDfXgmiTxeqS0L0P2 = 0x7A;                             // TXEQ Search Mask Socket 0 Link 0 Pstate2
  Setup_Config->CbsDfXgmiTxeqS0L0P3 = 0x7A;                             // TXEQ Search Mask Socket 0 Link 0 Pstate3
  Setup_Config->CbsDfXgmiTxeqS0L1P01 = 0x007A007A;                      // TXEQ Search Mask Socket 0 Link 1 Pstate0/1 (APCB)
  Setup_Config->CbsDfXgmiTxeqS0L1P23 = 0x007A007A;                      // TXEQ Search Mask Socket 0 Link 1 Pstate2/3 (APCB)
  Setup_Config->CbsDfXgmiTxeqS0L1P0 = 0x7A;                             // TXEQ Search Mask Socket 0 Link 1 Pstate0
  Setup_Config->CbsDfXgmiTxeqS0L1P1 = 0x7A;                             // TXEQ Search Mask Socket 0 Link 1 Pstate1
  Setup_Config->CbsDfXgmiTxeqS0L1P2 = 0x7A;                             // TXEQ Search Mask Socket 0 Link 1 Pstate2
  Setup_Config->CbsDfXgmiTxeqS0L1P3 = 0x7A;                             // TXEQ Search Mask Socket 0 Link 1 Pstate3
  Setup_Config->CbsDfXgmiTxeqS0L2P01 = 0x007A007A;                      // TXEQ Search Mask Socket 0 Link 2 Pstate0/1 (APCB)
  Setup_Config->CbsDfXgmiTxeqS0L2P23 = 0x007A007A;                      // TXEQ Search Mask Socket 0 Link 2 Pstate2/3 (APCB)
  Setup_Config->CbsDfXgmiTxeqS0L2P0 = 0x7A;                             // TXEQ Search Mask Socket 0 Link 2 Pstate0
  Setup_Config->CbsDfXgmiTxeqS0L2P1 = 0x7A;                             // TXEQ Search Mask Socket 0 Link 2 Pstate1
  Setup_Config->CbsDfXgmiTxeqS0L2P2 = 0x7A;                             // TXEQ Search Mask Socket 0 Link 2 Pstate2
  Setup_Config->CbsDfXgmiTxeqS0L2P3 = 0x7A;                             // TXEQ Search Mask Socket 0 Link 2 Pstate3
  Setup_Config->CbsDfXgmiTxeqS0L3P01 = 0x007A007A;                      // TXEQ Search Mask Socket 0 Link 3 Pstate0/1 (APCB)
  Setup_Config->CbsDfXgmiTxeqS0L3P23 = 0x007A007A;                      // TXEQ Search Mask Socket 0 Link 3 Pstate2/3 (APCB)
  Setup_Config->CbsDfXgmiTxeqS0L3P0 = 0x7A;                             // TXEQ Search Mask Socket 0 Link 3 Pstate0
  Setup_Config->CbsDfXgmiTxeqS0L3P1 = 0x7A;                             // TXEQ Search Mask Socket 0 Link 3 Pstate1
  Setup_Config->CbsDfXgmiTxeqS0L3P2 = 0x7A;                             // TXEQ Search Mask Socket 0 Link 3 Pstate2
  Setup_Config->CbsDfXgmiTxeqS0L3P3 = 0x7A;                             // TXEQ Search Mask Socket 0 Link 3 Pstate3
  Setup_Config->CbsDfXgmiTxeqS1L0P01 = 0x007A007A;                      // TXEQ Search Mask Socket 1 Link 0 Pstate0/1 (APCB)
  Setup_Config->CbsDfXgmiTxeqS1L0P23 = 0x007A007A;                      // TXEQ Search Mask Socket 1 Link 0 Pstate2/3 (APCB)
  Setup_Config->CbsDfXgmiTxeqS1L0P0 = 0x7A;                             // TXEQ Search Mask Socket 1 Link 0 Pstate0
  Setup_Config->CbsDfXgmiTxeqS1L0P1 = 0x7A;                             // TXEQ Search Mask Socket 1 Link 0 Pstate1
  Setup_Config->CbsDfXgmiTxeqS1L0P2 = 0x7A;                             // TXEQ Search Mask Socket 1 Link 0 Pstate2
  Setup_Config->CbsDfXgmiTxeqS1L0P3 = 0x7A;                             // TXEQ Search Mask Socket 1 Link 0 Pstate3
  Setup_Config->CbsDfXgmiTxeqS1L1P01 = 0x007A007A;                      // TXEQ Search Mask Socket 1 Link 1 Pstate0/1 (APCB)
  Setup_Config->CbsDfXgmiTxeqS1L1P23 = 0x007A007A;                      // TXEQ Search Mask Socket 1 Link 1 Pstate2/3 (APCB)
  Setup_Config->CbsDfXgmiTxeqS1L1P0 = 0x7A;                             // TXEQ Search Mask Socket 1 Link 1 Pstate0
  Setup_Config->CbsDfXgmiTxeqS1L1P1 = 0x7A;                             // TXEQ Search Mask Socket 1 Link 1 Pstate1
  Setup_Config->CbsDfXgmiTxeqS1L1P2 = 0x7A;                             // TXEQ Search Mask Socket 1 Link 1 Pstate2
  Setup_Config->CbsDfXgmiTxeqS1L1P3 = 0x7A;                             // TXEQ Search Mask Socket 1 Link 1 Pstate3
  Setup_Config->CbsDfXgmiTxeqS1L2P01 = 0x007A007A;                      // TXEQ Search Mask Socket 1 Link 2 Pstate0/1 (APCB)
  Setup_Config->CbsDfXgmiTxeqS1L2P23 = 0x007A007A;                      // TXEQ Search Mask Socket 1 Link 2 Pstate2/3 (APCB)
  Setup_Config->CbsDfXgmiTxeqS1L2P0 = 0x7A;                             // TXEQ Search Mask Socket 1 Link 2 Pstate0
  Setup_Config->CbsDfXgmiTxeqS1L2P1 = 0x7A;                             // TXEQ Search Mask Socket 1 Link 2 Pstate1
  Setup_Config->CbsDfXgmiTxeqS1L2P2 = 0x7A;                             // TXEQ Search Mask Socket 1 Link 2 Pstate2
  Setup_Config->CbsDfXgmiTxeqS1L2P3 = 0x7A;                             // TXEQ Search Mask Socket 1 Link 2 Pstate3
  Setup_Config->CbsDfXgmiTxeqS1L3P01 = 0x007A007A;                      // TXEQ Search Mask Socket 1 Link 3 Pstate0/1 (APCB)
  Setup_Config->CbsDfXgmiTxeqS1L3P23 = 0x007A007A;                      // TXEQ Search Mask Socket 1 Link 3 Pstate2/3 (APCB)
  Setup_Config->CbsDfXgmiTxeqS1L3P0 = 0x7A;                             // TXEQ Search Mask Socket 1 Link 3 Pstate0
  Setup_Config->CbsDfXgmiTxeqS1L3P1 = 0x7A;                             // TXEQ Search Mask Socket 1 Link 3 Pstate1
  Setup_Config->CbsDfXgmiTxeqS1L3P2 = 0x7A;                             // TXEQ Search Mask Socket 1 Link 3 Pstate2
  Setup_Config->CbsDfXgmiTxeqS1L3P3 = 0x7A;                             // TXEQ Search Mask Socket 1 Link 3 Pstate3
  Setup_Config->CbsDfXgmiAcDcCoupledLinkControl = 0xFF;                 // xGMI AC/DC Coupled Link Control
  Setup_Config->CbsDfXgmiAcDcCoupledLink = 0xFF;                        // xGMI AC/DC Coupled Link (APCB)
  Setup_Config->CbsDfXgmiAcDcCoupledLinkSocket0Link0 = 1;               // xGMI AC/DC Coupled Link Socket 0 Link 0
  Setup_Config->CbsDfXgmiAcDcCoupledLinkSocket0Link1 = 1;               // xGMI AC/DC Coupled Link Socket 0 Link 1
  Setup_Config->CbsDfXgmiAcDcCoupledLinkSocket0Link2 = 1;               // xGMI AC/DC Coupled Link Socket 0 Link 2
  Setup_Config->CbsDfXgmiAcDcCoupledLinkSocket0Link3 = 1;               // xGMI AC/DC Coupled Link Socket 0 Link 3
  Setup_Config->CbsDfXgmiAcDcCoupledLinkSocket1Link0 = 1;               // xGMI AC/DC Coupled Link Socket 1 Link 0
  Setup_Config->CbsDfXgmiAcDcCoupledLinkSocket1Link1 = 1;               // xGMI AC/DC Coupled Link Socket 1 Link 1
  Setup_Config->CbsDfXgmiAcDcCoupledLinkSocket1Link2 = 1;               // xGMI AC/DC Coupled Link Socket 1 Link 2
  Setup_Config->CbsDfXgmiAcDcCoupledLinkSocket1Link3 = 1;               // xGMI AC/DC Coupled Link Socket 1 Link 3
  Setup_Config->CbsDfXgmiChannelTypeControl = 0xFF;                     // xGMI Channel Type Control
  Setup_Config->CbsDfXgmiChannelType = 0;                               // xGMI Channel Type (APCB)
  Setup_Config->CbsDfXgmiChannelTypeSocket0Link0 = 0;                   // xGMI Channel Type Socket 0 Link 0
  Setup_Config->CbsDfXgmiChannelTypeSocket0Link1 = 0;                   // xGMI Channel Type Socket 0 Link 1
  Setup_Config->CbsDfXgmiChannelTypeSocket0Link2 = 0;                   // xGMI Channel Type Socket 0 Link 2
  Setup_Config->CbsDfXgmiChannelTypeSocket0Link3 = 0;                   // xGMI Channel Type Socket 0 Link 3
  Setup_Config->CbsDfXgmiChannelTypeSocket1Link0 = 0;                   // xGMI Channel Type Socket 1 Link 0
  Setup_Config->CbsDfXgmiChannelTypeSocket1Link1 = 0;                   // xGMI Channel Type Socket 1 Link 1
  Setup_Config->CbsDfXgmiChannelTypeSocket1Link2 = 0;                   // xGMI Channel Type Socket 1 Link 2
  Setup_Config->CbsDfXgmiChannelTypeSocket1Link3 = 0;                   // xGMI Channel Type Socket 1 Link 3
  Setup_Config->CbsDfCdma = 0xFF;                                       // SDCI
  Setup_Config->CbsDfDbgDisRmtSteer = 0xFF;                             // DisRmtSteer
  Setup_Config->CbsDfCmnPfOrganization = 0xFF;                          // Organization
  Setup_Config->CbsCmnDfPdrTuning = 0xFF;                               // Periodic Directory Rinse (PDR) Tuning
  Setup_Config->CbsDfCmnMemIntlvPageSize = 0xFF;                        // Tracking Granularity
  Setup_Config->CbsDfCmnPfPdrMode = 0xFF;                               // PDR Mode
  Setup_Config->CbsCmnMemCsInterleaveDdr = 0xFF;                        // Chipselect Interleaving
  Setup_Config->CbsCmnMemAddressHashBankDdr = 0xFF;                     // Address Hash Bank
  Setup_Config->CbsCmnMemAddressHashCsDdr = 0xFF;                       // Address Hash CS
  Setup_Config->CbsCmnMemAddressHashRmDdr = 0xFF;                       // Address Hash Rm
  Setup_Config->CbsCmnMemAddressHashSubchannelDdr = 0xFF;               // Address Hash Subchannel
  Setup_Config->CbsCmnMemCtrllerBankSwapModeDdr = 0xFF;                 // BankSwapMode
  Setup_Config->CbsCmnMemContextRestoreDdr = 0xFF;                      // Memory Context Restore
  Setup_Config->CbsDramSurvivesWarmReset = 0;                           // DRAM Survives Warm Reset
  Setup_Config->CbsCmnMemCtrllerPwrDnEnDdr = 0xFF;                      // Power Down Enable
  Setup_Config->CbsCmnMemSubUrgRefLowerBound = 1;                       // Sub Urgent Refresh Lower Bound
  Setup_Config->CbsCmnMemUrgRefLimit = 4;                               // Urgent Refresh Limit
  Setup_Config->CbsCmnMemDramRefreshRate = 0;                           // DRAM Refresh Rate
  Setup_Config->CbsCmnMemSelfRefreshExitStaggering = 9;                 // Self-Refresh Exit Staggering
  Setup_Config->CbsCmnMemt2xRefreshTemperatureThreshold = 2;            // DRAM 2x Refresh Temperature Threshold
  Setup_Config->CbsCmnMemChannelDisableFloatPowerGoodDdr = 0;           // Memory Channel Disable Float Power Good
  Setup_Config->CbsCmnMemChannelDisableBitmaskDdr = 0;                  // Memory Channel Disable Bitmask
  Setup_Config->CbsCmnMemSocket0Channel0Ddr = 1;                        // Socket 0 Channel 0
  Setup_Config->CbsCmnMemSocket0Channel1Ddr = 1;                        // Socket 0 Channel 1
  Setup_Config->CbsCmnMemSocket0Channel2Ddr = 1;                        // Socket 0 Channel 2
  Setup_Config->CbsCmnMemSocket0Channel3Ddr = 1;                        // Socket 0 Channel 3
  Setup_Config->CbsCmnMemSocket0Channel4Ddr = 1;                        // Socket 0 Channel 4
  Setup_Config->CbsCmnMemSocket0Channel5Ddr = 1;                        // Socket 0 Channel 5
  Setup_Config->CbsCmnMemSocket0Channel6Ddr = 1;                        // Socket 0 Channel 6
  Setup_Config->CbsCmnMemSocket0Channel7Ddr = 1;                        // Socket 0 Channel 7
  Setup_Config->CbsCmnMemSocket0Channel8Ddr = 1;                        // Socket 0 Channel 8
  Setup_Config->CbsCmnMemSocket0Channel9Ddr = 1;                        // Socket 0 Channel 9
  Setup_Config->CbsCmnMemSocket0Channel10Ddr = 1;                       // Socket 0 Channel 10
  Setup_Config->CbsCmnMemSocket0Channel11Ddr = 1;                       // Socket 0 Channel 11
  Setup_Config->CbsCmnMemSocket1Channel0Ddr = 1;                        // Socket 1 Channel 0
  Setup_Config->CbsCmnMemSocket1Channel1Ddr = 1;                        // Socket 1 Channel 1
  Setup_Config->CbsCmnMemSocket1Channel2Ddr = 1;                        // Socket 1 Channel 2
  Setup_Config->CbsCmnMemSocket1Channel3Ddr = 1;                        // Socket 1 Channel 3
  Setup_Config->CbsCmnMemSocket1Channel4Ddr = 1;                        // Socket 1 Channel 4
  Setup_Config->CbsCmnMemSocket1Channel5Ddr = 1;                        // Socket 1 Channel 5
  Setup_Config->CbsCmnMemSocket1Channel6Ddr = 1;                        // Socket 1 Channel 6
  Setup_Config->CbsCmnMemSocket1Channel7Ddr = 1;                        // Socket 1 Channel 7
  Setup_Config->CbsCmnMemSocket1Channel8Ddr = 1;                        // Socket 1 Channel 8
  Setup_Config->CbsCmnMemSocket1Channel9Ddr = 1;                        // Socket 1 Channel 9
  Setup_Config->CbsCmnMemSocket1Channel10Ddr = 1;                       // Socket 1 Channel 10
  Setup_Config->CbsCmnMemSocket1Channel11Ddr = 1;                       // Socket 1 Channel 11
  Setup_Config->CbsCmnMemRefManagementDdr = 0xFF;                       // Refresh Management
  Setup_Config->CbsCmnMemArfmDdr = 0xFF;                                // Adaptive Refresh Management
  Setup_Config->CbsCmnMemRAAIMTDdr = 0xFF;                              // RAA Initial Management Threshold
  Setup_Config->CbsCmnMemRAAMMTDdr = 0xFF;                              // RAA Maximum Management Threshold
  Setup_Config->CbsCmnMemRAARefDecMultiplierDdr = 0xFF;                 // RAA Refresh Decrement Multiplier
  Setup_Config->CbsCmnMemDrfmDdr = 0xFF;                                // DRFM Enable
  Setup_Config->CbsCmnMemDrfmBrcDdr = 4;                                // Bounded Refresh Configuration
  Setup_Config->CbsCmnMemDrfmHashDdr = 0xFF;                            // DRFM Hash Enable
  Setup_Config->CbsCmnMemMbistEnDdr = 0xFF;                             // MBIST Enable
  Setup_Config->CbsCmnMemMbistTestmodeDdr = 0xFF;                       // MBIST Test Mode
  Setup_Config->CbsCmnMemMbistAggressorsDdr = 0xff;                     // MBIST Aggressors
  Setup_Config->CbsCmnMemHealingBistEnableBitMaskDdr = 0;               // DDR Healing BIST
  Setup_Config->CbsCmnMemHealingBistExecutionMode = 0;                  // DDR Healing BIST Execution Mode
  Setup_Config->CbsCmnMemHealingBistRepairTypeDdr = 0;                  // DDR Healing BIST Repair Type
  Setup_Config->CbsCmnMemPmuBistAlgorithmSelect = 0xFF;                 // PMU Mem BIST Algorithm Select
  Setup_Config->CbsCmnMemPmuBistAlgorithmBitMaskDdr = 0x01FF;           // PMU Mem BIST Algorithm Bitmask
  Setup_Config->CbsCmnMemPmuBistAlgorithm1 = 1;                         // Algorithm #1
  Setup_Config->CbsCmnMemPmuBistAlgorithm2 = 1;                         // Algorithm #2
  Setup_Config->CbsCmnMemPmuBistAlgorithm3 = 1;                         // Algorithm #3
  Setup_Config->CbsCmnMemPmuBistAlgorithm4 = 1;                         // Algorithm #4
  Setup_Config->CbsCmnMemPmuBistAlgorithm5 = 1;                         // Algorithm #5
  Setup_Config->CbsCmnMemPmuBistAlgorithm6 = 1;                         // Algorithm #6
  Setup_Config->CbsCmnMemPmuBistAlgorithm7 = 1;                         // Algorithm #7
  Setup_Config->CbsCmnMemPmuBistAlgorithm8 = 1;                         // Algorithm #8
  Setup_Config->CbsCmnMemPmuBistAlgorithm9 = 1;                         // Algorithm #9
  Setup_Config->CbsCmnMemMbistPatternSelect = 0;                        // Pattern Select
  Setup_Config->CbsCmnMemMbistPatternLength = 3;                        // Pattern Length
  Setup_Config->CbsCmnMemMbistAggressorsChnl = 2;                       // Aggressor Channel
  Setup_Config->CbsCmnMemMbistAggrStaticLaneCtrl = 0;                   // Aggressor Static Lane Control
  Setup_Config->CbsCmnMemMbistAggrStaticLaneSelU32 = 0;                 // Aggressor Static Lane Select Upper 32 bits
  Setup_Config->CbsCmnMemMbistAggrStaticLaneSelL32 = 0;                 // Aggressor Static Lane Select Lower 32 Bits
  Setup_Config->CbsCmnMemMbistAggrStaticLaneSelEcc = 0;                 // Aggressor Static Lane Select ECC
  Setup_Config->CbsCmnMemMbistAggrStaticLaneVal = 0;                    // Aggressor Static Lane Value
  Setup_Config->CbsCmnMemMbistTgtStaticLaneCtrl = 0;                    // Target Static Lane Control
  Setup_Config->CbsCmnMemMbistTgtStaticLaneSelU32 = 0;                  // Target Static Lane Select Upper 32 bit
  Setup_Config->CbsCmnMemMbistTgtStaticLaneSelL32 = 0;                  // Target Static Lane Select Lower 32 Bits
  Setup_Config->CbsCmnMemMbistTgtStaticLaneSelEcc = 0;                  // Target Static Lane Select ECC
  Setup_Config->CbsCmnMemMbistTgtStaticLaneVal = 0;                     // Target Static Lane Value
  Setup_Config->CbsCmnMemMbistReadDataEyeVoltageStep = 1;               // Read Voltage Sweep Step Size
  Setup_Config->CbsCmnMemMbistReadDataEyeTimingStep = 1;                // Read Timing Sweep Step Size
  Setup_Config->CbsCmnMemMbistWriteDataEyeVoltageStep = 1;              // Write Voltage Sweep Step Size
  Setup_Config->CbsCmnMemMbistWriteDataEyeTimingStep = 1;               // Write Timing Sweep Step Size
  Setup_Config->CbsCmnMemMbistDataeyeSilentExecution = 0;               // Silent Execution
  Setup_Config->CbsCmnMemDataPoisoningDdr = 0xFF;                       // Data Poisoning
  Setup_Config->CbsCmnMemBootTimePostPackageRepair = 0;                 // DRAM Boot Time Post Package Repair
  Setup_Config->CbsCmnMemRuntimePostPackageRepair = 0;                  // DRAM Runtime Post Package Repair
  Setup_Config->CbsCmnMemPostPackageRepairConfigInitiator = 0;          // DRAM Post Package Repair Config Initiator
  Setup_Config->CbsCmnMemRcdParityDdr = 0xFF;                           // RCD Parity
  Setup_Config->CbsCmnMemMaxRcdParityErrorReplayDdr = 8;                // Max RCD Parity Error Replay
  Setup_Config->CbsCmnMemWriteCrcDdr = 0;                               // Write CRC
  Setup_Config->CbsCmnMemMaxWriteCrcErrorReplayDdr = 0x8;               // Max Write CRC Error Replay
  Setup_Config->CbsCmnMemReadCrcDdr = 0;                                // Read CRC
  Setup_Config->CbsCmnMemMaxReadCrcErrorReplayDdr = 8;                  // Max Read CRC Error Replay
  Setup_Config->CbsCmnMemDisMemErrInj = 0xff;                           // Memory Error Injection
  Setup_Config->CbsCmnMemEcsStatusInterruptDdr = 0;                     // EcsStatus Interrupt
  Setup_Config->CbsCmnMemCorrectedErrorCounterEnable = 2;               // DRAM Corrected Error Counter Enable
  Setup_Config->CbsCmnMemCorrectedErrorCounterInterruptEnable = 1;      // DRAM Corrected Error Counter Interrupt Enable
  Setup_Config->CbsCmnMemCorrectedErrorCounterLeakRate = 0x07;          // DRAM Corrected Error Counter Leak Rate
  Setup_Config->CbsCmnMemCorrectedErrorCounterStartCount = 0xFFF5;      // DRAM Corrected Error Counter Start Count
  Setup_Config->CbsCmnMemDramEccSymbolSizeDdr = 0xFF;                   // DRAM ECC Symbol Size
  Setup_Config->CbsCmnMemDramEccEnDdr = 0xFF;                           // DRAM ECC Enable
  Setup_Config->CbsCmnMemDramUeccRetryDdr = 0xFF;                       // DRAM UECC Retry
  Setup_Config->CbsCmnMemMaxDramUeccErrorReplayDdr = 8;                 // Max DRAM UECC Error Replay
  Setup_Config->CbsCmnMemDramMemClrDdr = 0xFF;                          // Memory Clear
  Setup_Config->CbsCmnMemAddrXorAfterEcc = 0xFF;                        // Address XOR after ECC
  Setup_Config->CbsDbgMemCipherTextHiding = 0;                          // CipherText Hiding Enable
  Setup_Config->CbsCmnMemDramEcsModeDdr = 0xFF;                         // DRAM ECS Mode
  Setup_Config->CbsCmnMemDramRedirectScrubEnDdr = 0xFF;                 // DRAM Redirect Scrubber Enable
  Setup_Config->CbsCmnMemDramRedirectScrubLimitDdr = 0xFF;              // DRAM Scrub Redirection Limit
  Setup_Config->CbsCmnMemDramScrubTime = 24;                            // DRAM Scrub Time
  Setup_Config->CbsCmnMemtECSintCtrlDdr = 0xFF;                         // tECSint Ctrl
  Setup_Config->CbsCmnMemtECSintDdr = 1440;                             // tECSint
  Setup_Config->CbsCmnMemDramEtcDdr = 0xFF;                             // DRAM Error Threshold Count
  Setup_Config->CbsCmnMemDramEcsCountModeDdr = 0xFF;                    // DRAM ECS Count Mode
  Setup_Config->CbsCmnMemDramAutoEcsSelfRefreshDdr = 0xFF;              // DRAM AutoEcs during Self Refresh
  Setup_Config->CbsCmnMemDramEcsWritebackSuppressionDdr = 0xFF;         // DRAM ECS WriteBack Suppression
  Setup_Config->CbsCmnMemDramX4WritebackSuppressionDdr = 0xFF;          // DRAM X4 WriteBack Suppression
  Setup_Config->CbsCmnMemOdtImpedProcDdr = 0xFF;                        // Processor ODT Pull Up Impedance
  Setup_Config->CbsCmnMemOdtPullDownImpedProcDdr = 0xFF;                // Processor ODT Pull Down Impedance
  Setup_Config->CbsCmnMemDramDrvStrenDqDdr = 0xFF;                      // Dram DQ drive strengths
  Setup_Config->CbsCmnMemRttNomWrP0Ddr = 0xFF;                          // RTT_NOM_WR P-State 0
  Setup_Config->CbsCmnMemRttNomRdP0Ddr = 0xFF;                          // RTT_NOM_RD P-State 0
  Setup_Config->CbsCmnMemRttWrP0Ddr = 0xFF;                             // RTT_WR P-State 0
  Setup_Config->CbsCmnMemRttParkP0Ddr = 0xFF;                           // RTT_PARK P-State 0
  Setup_Config->CbsCmnMemRttParkDqsP0Ddr = 0xFF;                        // DQS_RTT_PARK P-State 0
  Setup_Config->CbsCmnMemRttNomWrP1Ddr = 0xFF;                          // RTT_NOM_WR P-State 1
  Setup_Config->CbsCmnMemRttNomRdP1Ddr = 0xFF;                          // RTT_NOM_RD P-State 1
  Setup_Config->CbsCmnMemRttWrP1Ddr = 0xFF;                             // RTT_WR P-State 1
  Setup_Config->CbsCmnMemRttParkP1Ddr = 0xFF;                           // RTT_PARK P-State 1
  Setup_Config->CbsCmnMemRttParkDqsP1Ddr = 0xFF;                        // DQS_RTT_PARK P-State 1
  Setup_Config->CbsCmnMemTimingLegalDisclaimer = 0;                     // DRAM Timing Configuration Legal Disclaimer
  Setup_Config->CbsCmnMemTimingLegalDisclaimer1 = 0;                    // DRAM Timing Configuration Legal Disclaimer 1
  Setup_Config->CbsCmnMemTimingSettingDdr = 1;                          // Active Memory Timing Settings
  Setup_Config->CbsCmnMemTargetSpeedDdr = 0xFFFF;                       // Memory Target Speed
  Setup_Config->CbsCmnMemTimingTclCtrlDdr = 0;                          // Tcl Ctrl
  Setup_Config->CbsCmnMemTimingTclDdr = 0x16;                           // Tcl
  Setup_Config->CbsCmnMemTimingTrcdCtrlDdr = 0;                         // Trcd Ctrl
  Setup_Config->CbsCmnMemTimingTrcdDdr = 8;                             // Trcd
  Setup_Config->CbsCmnMemTimingTrpCtrlDdr = 0;                          // Trp Ctrl
  Setup_Config->CbsCmnMemTimingTrpDdr = 8;                              // Trp
  Setup_Config->CbsCmnMemTimingTrasCtrlDdr = 0;                         // Tras Ctrl
  Setup_Config->CbsCmnMemTimingTrasDdr = 0x27;                          // Tras
  Setup_Config->CbsCmnMemTimingTrcCtrlDdr = 0;                          // Trc Ctrl
  Setup_Config->CbsCmnMemTimingTrcDdr = 0x39;                           // Trc
  Setup_Config->CbsCmnMemTimingTwrCtrlDdr = 0;                          // Twr Ctrl
  Setup_Config->CbsCmnMemTimingTwrDdr = 0x12;                           // Twr
  Setup_Config->CbsCmnMemTimingTrfc1CtrlDdr = 0;                        // Trfc1 Ctrl
  Setup_Config->CbsCmnMemTimingTrfc1Ddr = 0x138;                        // Trfc1
  Setup_Config->CbsCmnMemTimingTrfc2CtrlDdr = 0;                        // Trfc2 Ctrl
  Setup_Config->CbsCmnMemTimingTrfc2Ddr = 0xC0;                         // Trfc2
  Setup_Config->CbsCmnMemTimingTrfcSbCtrlDdr = 0;                       // TrfcSb Ctrl
  Setup_Config->CbsCmnMemTimingTrfcSbDdr = 50;                          // TrfcSb
  Setup_Config->CbsCmnMemTimingTcwlCtrlDdr = 0;                         // Tcwl Ctrl
  Setup_Config->CbsCmnMemTimingTcwlDdr = 0x0C;                          // Tcwl
  Setup_Config->CbsCmnMemTimingTrtpCtrlDdr = 0;                         // Trtp Ctrl
  Setup_Config->CbsCmnMemTimingTrtpDdr = 0x09;                          // Trtp
  Setup_Config->CbsCmnMemTimingTrrdLCtrlDdr = 0;                        // TrrdL Ctrl
  Setup_Config->CbsCmnMemTimingTrrdLDdr = 0x04;                         // TrrdL
  Setup_Config->CbsCmnMemTimingTrrdSCtrlDdr = 0;                        // TrrdS Ctrl
  Setup_Config->CbsCmnMemTimingTrrdSDdr = 0x04;                         // TrrdS
  Setup_Config->CbsCmnMemTimingTfawCtrlDdr = 0;                         // Tfaw Ctrl
  Setup_Config->CbsCmnMemTimingTfawDdr = 0x1A;                          // Tfaw
  Setup_Config->CbsCmnMemTimingTwtrLCtrlDdr = 0;                        // TwtrL Ctrl
  Setup_Config->CbsCmnMemTimingTwtrLDdr = 0x03;                         // TwtrL
  Setup_Config->CbsCmnMemTimingTwtrSCtrlDdr = 0;                        // TwtrS Ctrl
  Setup_Config->CbsCmnMemTimingTwtrSDdr = 0x03;                         // TwtrS
  Setup_Config->CbsCmnMemTimingTrdrdScLCtrlDdr = 0;                     // TrdrdScL Ctrl
  Setup_Config->CbsCmnMemTimingTrdrdScLDdr = 0x1;                       // TrdrdScL
  Setup_Config->CbsCmnMemTimingTrdrdScCtrlDdr = 0;                      // TrdrdSc Ctrl
  Setup_Config->CbsCmnMemTimingTrdrdScDdr = 0x1;                        // TrdrdSc
  Setup_Config->CbsCmnMemTimingTrdrdSdCtrlDdr = 0;                      // TrdrdSd Ctrl
  Setup_Config->CbsCmnMemTimingTrdrdSdDdr = 0x3;                        // TrdrdSd
  Setup_Config->CbsCmnMemTimingTrdrdDdCtrlDdr = 0;                      // TrdrdDd Ctrl
  Setup_Config->CbsCmnMemTimingTrdrdDdDdr = 0x3;                        // TrdrdDd
  Setup_Config->CbsCmnMemTimingTwrwrScLCtrlDdr = 0;                     // TwrwrScL Ctrl
  Setup_Config->CbsCmnMemTimingTwrwrScLDdr = 0x1;                       // TwrwrScL
  Setup_Config->CbsCmnMemTimingTwrwrScCtrlDdr = 0;                      // TwrwrSc Ctrl
  Setup_Config->CbsCmnMemTimingTwrwrScDdr = 0x1;                        // TwrwrSc
  Setup_Config->CbsCmnMemTimingTwrwrSdCtrlDdr = 0;                      // TwrwrSd Ctrl
  Setup_Config->CbsCmnMemTimingTwrwrSdDdr = 0x3;                        // TwrwrSd
  Setup_Config->CbsCmnMemTimingTwrwrDdCtrlDdr = 0;                      // TwrwrDd Ctrl
  Setup_Config->CbsCmnMemTimingTwrwrDdDdr = 0x3;                        // TwrwrDd
  Setup_Config->CbsCmnMemTimingTwrrdCtrlDdr = 0;                        // Twrrd Ctrl
  Setup_Config->CbsCmnMemTimingTwrrdDdr = 0x1;                          // Twrrd
  Setup_Config->CbsCmnMemTimingTrdwrCtrlDdr = 0;                        // Trdwr Ctrl
  Setup_Config->CbsCmnMemTimingTrdwrDdr = 0x5;                          // Trdwr
  Setup_Config->CbsCmnMemDramPdaEnumIdProgModeDdr = 0xFF;               // DRAM PDA Enumerate ID Programming Mode
  Setup_Config->CbsCmnMemWriteTrainingBurstLength = 2;                  // Write Training Burst Length
  Setup_Config->CbsCmnTrainingRetryCount = 0xFF;                        // Training Retry Count
  Setup_Config->CbsCmnMemPeriodicTrainingModeDdr = 1;                   // Periodic Training Mode
  Setup_Config->CbsCmnMemPeriodicIntervalMode = 0;                      // Periodic Interval Mode
  Setup_Config->CbsCmnMemPeriodicInterval = 1000;                       // Periodic Interval
  Setup_Config->CbsCmnMemTsmeEnableDdr = 0xFF;                          // TSME
  Setup_Config->CbsCmnMemAes = 1;                                       // AES
  Setup_Config->CbsCmnMemDataScramble = 1;                              // Data Scramble
  Setup_Config->CbsCmnMemSmeMkEnable = 0;                               // SME-MK
  Setup_Config->CbsCmnPmicErrorReporting = 0xFF;                        // PMIC Error Reporting
  Setup_Config->CbsCmnMemCtrllerPmicOpMode = 0;                         // PMIC Operation Mode
  Setup_Config->CbsCmnMemCtrllerPmicFaultRecovery = 0;                  // PMIC Fault Recovery
  Setup_Config->CbsCmnMemCtrllerPmicSwaSwbVddCore = 1100;               // PMIC SWA/SWB VDD Core
  Setup_Config->CbsCmnMemCtrllerPmicSwcVddio = 1100;                    // PMIC SWC VDDIO
  Setup_Config->CbsCmnMemCtrllerPmicSwdVpp = 1800;                      // PMIC SWD VPP
  Setup_Config->CbsCmnMemCtrllerPmicStaggerDelay = 5;                   // PMIC Stagger Delay
  Setup_Config->CbsCmnMemCtrllerMaxPmicPowerOn = 0xFF;                  // Max PMIC Power On
  Setup_Config->CbsCmnMemOdtsCmdThrottleCycleCtlDdr = 1;                // ODTS Thermal Throttle Control
  Setup_Config->CbsCmnMemOdtsCmdThrottleThresholdDdr = 0xFF;            // ODTS Thermal Throttle Threshold
  Setup_Config->CbsCmnTsodThermalThrottleControlDdr = 0;                // TSOD Thermal Throttle Control
  Setup_Config->CbsCmnTsodThermalThrottleStartTempDdr = 85;             // TSOD Thermal Throttle Start Temperature
  Setup_Config->CbsCmnTsodThermalThrottleHysteresisDdr = 5;             // TSOD Thermal Throttle Hysteresis
  Setup_Config->CbsCmnTsodCmdThrottlePercentage0Ddr = 10;               // TSOD Command Throttle Percentage (Threshold)
  Setup_Config->CbsCmnTsodCmdThrottlePercentage5Ddr = 20;               // TSOD Command Throttle Percentage (Threshold+5C)
  Setup_Config->CbsCmnTsodCmdThrottlePercentage10Ddr = 40;              // TSOD Command Throttle Percentage (Threshold+10C)
  Setup_Config->CbsCmnGnbPcieLoopBackMode = 0xF;                        // PCIe loopback Mode
  Setup_Config->CbsEnable2SpcGen4 = 0xf;                                // Enable 2 SPC (Gen 4)
  Setup_Config->CbsEnable2SpcGen5 = 0xf;                                // Enable 2 SPC (Gen 5)
  Setup_Config->CbsGnbSafeRecoveryUponABERExceededError = 0xf;          // Safe recovery upon a BERExceeded Error
  Setup_Config->CbsGnbPeriodicCalibration = 0xf;                        // Periodic Calibration
  Setup_Config->CbsCmnTDPCtl = 0;                                       // TDP Control
  Setup_Config->CbsCmnTDPLimit = 0;                                     // TDP
  Setup_Config->CbsCmnPPTCtl = 0;                                       // PPT Control
  Setup_Config->CbsCmnPPTLimit = 0;                                     // PPT
  Setup_Config->CbsCmnDeterminismCtl = 1;                               // Determinism Control
  Setup_Config->CbsCmnDeterminismEnable = 0;                            // Determinism Enable
  Setup_Config->CbsCmnxGmiLinkWidthCtl = 0;                             // xGMI Link Width Control
  Setup_Config->CbsCmnxGmiForceLinkWidthCtl = 0xF;                      // xGMI Force Link Width Control
  Setup_Config->CbsCmnxGmiForceLinkWidth = 0xF;                         // xGMI Force Link Width
  Setup_Config->CbsCmnxGmiMaxLinkWidthCtl = 0;                          // xGMI Max Link Width Range Control
  Setup_Config->CbsCmnxGmiMaxLinkWidth = 0xF;                           // xGMI Max Link Width
  Setup_Config->CbsCmnxGmiMinLinkWidth = 0xF;                           // xGMI Min Link Width
  Setup_Config->CbsCmnApbdis = 0xf;                                     // APBDIS
  Setup_Config->CbsCmnApbdisDfPstate = 0;                               // DfPstate
  Setup_Config->CbsCmnEfficiencyModeEn = 1;                             // Power Profile Selection
  Setup_Config->CbsCmnXgmiPstateControl = 0xf;                          // xGMI Pstate Control
  Setup_Config->CbsCmnXgmiPstateSelection = 0;                          // xGMI Pstate Selection
  Setup_Config->CbsCmnBoostFmaxEn = 0;                                  // BoostFmaxEn
  Setup_Config->CbsCmnBoostFmax = 0;                                    // BoostFmax
  Setup_Config->CbsCmnGnbSMUDffo = 0xF;                                 // DF PState Frequency Optimizer
  Setup_Config->CbsCmnGnbSmuDfCstates = 0xf;                            // DF Cstates
  Setup_Config->CbsCmnGnbSmuCppc = 0xf;                                 // CPPC
  Setup_Config->CbsCmnGnbSMUHsmpSupport = 0xF;                          // HSMP Support
  Setup_Config->CbsCmnSvi3SvcSpeedCtl = 0;                              // SVI3 SVC Speed Control
  Setup_Config->CbsCmnSvi3SvcSpeed = 3;                                 // SVI3 SVC Speed
  Setup_Config->CbsCmnX3dStackOverride = 0xF;                           // 3D V-Cache
  Setup_Config->CbsCmnL3Bist = 0xF;                                     // L3 BIST
  Setup_Config->CbsCmnGnbDiagMode = 0xF;                                // Diagnostic Mode
  Setup_Config->CbsCmnGnbSmuGmiFolding = 0xf;                           // GMI Folding
  Setup_Config->CbsCmnThrottlerMode = 0xf;                              // Separate CPU power plane throttling
  Setup_Config->CbsCmnDFPstateRangeCtl = 0xFF;                          // DfPstate Range Control
  Setup_Config->CbsCmnDfPstateMax = 0;                                  // DfPstate Max Index
  Setup_Config->CbsCmnDfPstateMin = 2;                                  // DfPstate Min Index
  Setup_Config->CbsCmnRASControl = 0xF;                                 // NBIO RAS Control
  Setup_Config->CbsCmnNBIOSyncFloodGen = 0xf;                           // NBIO SyncFlood Generation
  Setup_Config->PcdSyncFloodToApml = 0x1;                               // NBIO SyncFlood Reporting
  Setup_Config->CmnGnbAmdPcieAerReportMechanism = 0x0F;                 // PCIe Aer Reporting Mechanism
  Setup_Config->EdpcControl = 3;                                        // Edpc Control
  Setup_Config->AcsRasValue = 0xF;                                      // ACS RAS Request Value
  Setup_Config->CbsCmnPoisonConsumption = 0xF;                          // NBIO Poison Consumption
  Setup_Config->CbsCmnGnbRasSyncfloodPcieFatalError = 0xF;              // Sync Flood on PCIe Fatal Error
  Setup_Config->CbsCmnRASNumericalCommonOptions = 0;                    // NBIO RAS Numerical Common Options
  Setup_Config->PcdEgressPoisonSeverityHi = 0x00030011;                 // Egress Poison Severity High
  Setup_Config->PcdEgressPoisonSeverityLo = 0x00000004;                 // Egress Poison Severity Low
  Setup_Config->PcdAmdNbioEgressPoisonMaskHi = 0xFFFCFFFF;              // Egress Poison Mask High
  Setup_Config->PcdAmdNbioEgressPoisonMaskLo = 0xFFFFFFFB;              // Egress Poison Mask Low
  Setup_Config->PcdAmdNbioRASUcpMaskHi = 0x00030000;                    // Uncorrected Converted to Poison Enable Mask High
  Setup_Config->PcdAmdNbioRASUcpMaskLo = 0x00000004;                    // Uncorrected Converted to Poison Enable Mask Low
  Setup_Config->PcdSyshubWdtTimerInterval = 2600;                       // System Hub Watchdog Timer
  Setup_Config->CbsCmnGnbDataObjectExchange = 0xf;                      // Data Object Exchange
  Setup_Config->CbsCmnGnbRtmMarginingSupport = 0xF;                     // RTM Margining Support
  Setup_Config->CbsCmnNbioForceSpeedLastAdvertised = 0xFF;              // Multi Auto Speed Change On Last Rate
  Setup_Config->CbsCmnLcMultUpstreamAuto = 0xF;                         // Multi Upstream Auto Speed Change
  Setup_Config->STRAP_COMPLIANCE_DIS = 0xf;                             // Allow Compliance
  Setup_Config->CbsCmnNbioPcieAdvertiseEqToHighRateSupport = 0xF;       // EQ Bypass To Highest Rate
  Setup_Config->CbsCmnGnbDataLinkFeatureCap = 0xF;                      // Data Link Feature Cap
  Setup_Config->CbsCmnGnbDataLinkFeatureExchange = 0xF;                 // Data Link Feature Exchange
  Setup_Config->CbsCmnGnbSris = 0xF;                                    // SRIS
  Setup_Config->CbsDbgGnbDbgACSEnable = 0xF;                            // ACS Enable
  Setup_Config->CbsGnbCmnPcieTbtSupport = 0xf;                          // PCIe Ten Bit Tag Support
  Setup_Config->CbsGnbCmnPcieAriEnumeration = 0xf;                      // PCIe ARI Enumeration
  Setup_Config->CmnGnbPcieAriSupport = 0xf;                             // PCIe ARI Support
  Setup_Config->CbsPresenceDetectSelectmode = 0xF;                      // Presence Detect Select mode
  Setup_Config->CbsHotPlugHandlingMode = 0xF;                           // Hot Plug Handling mode
  Setup_Config->CbsHotPlugPDSettle = 0;                                 // Presence Detect State Settle Time
  Setup_Config->CbsHotPlugSettleTime = 255;                             // Hot Plug Port Settle Time
  Setup_Config->CbsHotplugSupport = 0xF;                                // Hotplug Support
  Setup_Config->CbsCmnEarlyLinkSpeed = 2;                               // Early Link Speed
  Setup_Config->CbsDbgGnbDbgAERCAPEnable = 0xF;                         // Enable AER Cap
  Setup_Config->CbsCmnPcieCAPLinkSpeed = 0xf;                           // PCIE Link Speed Capability
  Setup_Config->CbsCmnPcieTargetLinkSpeed = 0xFF;                       // PCIE Target Link Speed
  Setup_Config->CbsCmnAllPortsASPM = 0xFF;                              // ASPM Control
  Setup_Config->CbsCmnNbioMctpEn = 0xFF;                                // MCTP Enable
  Setup_Config->CbsCmnNbioMctpMode = 0xFF;                              // MCTP Mode
  Setup_Config->CbsCmnNbioMctpDiscoveryNotifyMessage = 0xFF;            // MCTP discovery notify message
  Setup_Config->CbsCmnNbioPcieNonPcieCompliantSupport = 0xFF;           // Non-PCIe Compliant Support
  Setup_Config->CbsCmnLimitHpDevicesToPcieBootSpeed = 0xf;              // Limit hotplug devices to PCIe boot speed
  Setup_Config->CbsCmnPCIeSFIConfigviaOOBEn = 0;                        // Enable PCIe SFI Config via OOB
  Setup_Config->CbsCmnNbioPcieIdlePowerSetting = 0xF;                   // PCIE Idle Power Setting
  Setup_Config->CbsCfgAcsEnRccDev0 = 0xF;                               // ACS Rcc_Dev0
  Setup_Config->CbsCfgAerEnRccDev0 = 0xF;                               // AER Rcc_Dev0
  Setup_Config->CbsCfgDlfEnStrap1 = 0xF;                                // DlfEnableStrap1
  Setup_Config->CbsCfgPhy16gtStrap1 = 0xF;                              // Phy16GTStrap1
  Setup_Config->CbsCfgMarginEnStrap1 = 0xF;                             // MarginEnStrap1
  Setup_Config->CbsCfgAcsSourceValStrap5 = 0xF;                         // SourceValStrap5
  Setup_Config->CbsCfgAcsTranslationalBlockingStrap5 = 0xF;             // TranslationalBlockingStrap5
  Setup_Config->CbsCfgAcsP2pReq = 0xF;                                  // P2pReq ACS Control
  Setup_Config->CbsCfgAcsP2pCompStrap5 = 0xF;                           // P2pCompStrap5
  Setup_Config->CbsCfgAcsUpstreamFwdStrap5 = 0xF;                       // UpstreamFwdStrap5
  Setup_Config->CbsCfgAcsP2PEgressStrap5 = 0xF;                         // P2PEgressStrap5
  Setup_Config->CbsCfgAcsDirectTranslatedStrap5 = 0xF;                  // DirectTranslatedStrap5
  Setup_Config->CbsCfgAcsSsidEnStrap5 = 0xF;                            // SsidEnStrap5
  Setup_Config->CbsCfgPriEnPageReq = 0xF;                               // PriEnPageReq
  Setup_Config->CbsCfgPriResetPageReq = 0xF;                            // PriResetPageReq
  Setup_Config->CbsCfgAcsSourceVal = 0xF;                               // SourceVal ACS cntl
  Setup_Config->CbsCfgAcsTranslationalBlocking = 0xF;                   // TranslationalBlocking ACS Control
  Setup_Config->CbsCfgAcsP2pComp = 0xF;                                 // P2pComp ACS Control
  Setup_Config->CbsCfgAcsUpstreamFwd = 0xF;                             // UpstreamFwd ACS Control
  Setup_Config->CbsCfgAcsP2PEgress = 0xF;                               // P2PEgress ACS Control
  Setup_Config->CbsCfgAcsP2pReqStrap5 = 0xF;                            // P2pReqStrap5
  Setup_Config->CbsCfgE2EPrefix = 0xF;                                  // E2E_PREFIX
  Setup_Config->CbsCfgExtendedFmtSupported = 0xF;                       // EXTENDED_FMT
  Setup_Config->CbsCmnNbioAtomicRoutingStrap5 = 0xF;                    // AtomicRoutingStrap5
  Setup_Config->CbsSevSnpSupport = 0xf;                                 // SEV-SNP Support
  Setup_Config->CbsSevTioSupport = 0xFF;                                // SEV-TIO Support
  Setup_Config->CbsCmnDrtmMemoryReservation = 0xFF;                     // DRTM Memory Reservation
  Setup_Config->CbsCmnDrtmSupport = 0xF;                                // DRTM Virtual Device Support
  Setup_Config->CbsCmnDmaProtection = 0xF;                              // DMA Protection
  Setup_Config->CbsCmnGnbNbIOMMU = 0xf;                                 // IOMMU
  Setup_Config->CbsCmnDmarSupport = 0xF;                                // DMAr Support
  Setup_Config->CbsCmnEnablePortBifurcation = 0xf;                      // Enable Port Bifurcation
  Setup_Config->CbsCmnS0P0Override = 0xf;                               // Socket 0 P0 Override
  Setup_Config->CbsCmnS0P1Override = 0xf;                               // Socket 0 P1 Override
  Setup_Config->CbsCmnS0P2Override = 0xf;                               // Socket 0 P2 Override
  Setup_Config->CbsCmnS0P3Override = 0xf;                               // Socket 0 P3 Override
  Setup_Config->CbsCmnS1P0Override = 0xf;                               // Socket 1 P0 Override
  Setup_Config->CbsCmnS1P1Override = 0xf;                               // Socket 1 P1 Override
  Setup_Config->CbsCmnS1P2Override = 0xf;                               // Socket 1 P2 Override
  Setup_Config->CbsCmnS1P3Override = 0xf;                               // Socket 1 P3 Override
  Setup_Config->CbsCmnP0Override = 0xf;                                 // P0 Override
  Setup_Config->CbsCmnP1Override = 0xf;                                 // P1 Override
  Setup_Config->CbsCmnP2Override = 0xf;                                 // P2 Override
  Setup_Config->CbsCmnP3Override = 0xf;                                 // P3 Override
  Setup_Config->CbsCmnG0Override = 0xf;                                 // G0 Override
  Setup_Config->CbsCmnG1Override = 0xf;                                 // G1 Override
  Setup_Config->CbsCmnG2Override = 0xf;                                 // G2 Override
  Setup_Config->CbsCmnG3Override = 0xf;                                 // G3 Override
  Setup_Config->CbsCmnNbioPcieSearchMaskConfigGen3 = 0xff;              // Preset Search Mask Configuration (Gen3)
  Setup_Config->CbsCmnNbioPcieSearchMaskGen3 = 0;                       // Preset Search Mask (Gen3)
  Setup_Config->CbsCmnNbioPcieSearchMaskConfigGen4 = 0xff;              // Preset Search Mask Configuration (Gen4)
  Setup_Config->CbsCmnNbioPcieSearchMaskGen4 = 0;                       // Preset Search Mask (Gen4)
  Setup_Config->CbsCmnNbioPcieSearchMaskConfigGen5 = 0xff;              // Preset Search Mask Configuration (Gen5)
  Setup_Config->CbsCmnNbioPcieSearchMaskGen5 = 0;                       // Preset Search Mask (Gen5)
  Setup_Config->CbsCmnFchI3C0Config = 0;                                // I3C/I2C 0 Enable
  Setup_Config->CbsCmnFchI3C1Config = 0;                                // I3C/I2C 1 Enable
  Setup_Config->CbsCmnFchI3C2Config = 0;                                // I3C/I2C 2 Enable
  Setup_Config->CbsCmnFchI3C3Config = 0;                                // I3C/I2C 3 Enable
  Setup_Config->CbsCmnFchI2C4Config = 0xf;                              // I2C 4 Enable
  Setup_Config->CbsCmnFchI2C5Config = 0xf;                              // I2C 5 Enable
  Setup_Config->CbsCmnFchReleaseSpdHostControl = 0;                     // Release SPD Host Control
  Setup_Config->CbsCmnFchPMFWDdr5Telemetry = 1;                         // PMFW Poll DDR5 Telemetry
  Setup_Config->CbsCmnFchIxcTelemetryPortsFence = 1;                    // Ixc Telemetry Ports Fence Control
  Setup_Config->CbsCmnFchI2cSdaHoldOverride = 0xf;                      // I2C SDA Hold Override
  Setup_Config->CbsCmnFchApmlSbtsiSlvMode = 0;                          // APML SB-TSI Mode
  Setup_Config->CbsCmnFchI3cModeSpeed = 0x2;                            // I3C Mode Speed
  Setup_Config->CbsCmnFchI3cPpHcntValue = 0x08;                         // I3C Push Pull HCNT Value
  Setup_Config->CbsCmnFchI3cSdaHoldValue = 0x2;                         // I3C SDA Hold Value
  Setup_Config->CbsCmnFchI3cSdaHoldOverride = 0xf;                      // I3C SDA Hold Override
  Setup_Config->CbsCmnFchI2c0SdaTxHoldValue = 0x35;                     // I2C 0 SDA TX HOLD VALUE
  Setup_Config->CbsCmnFchI2c1SdaTxHoldValue = 0x35;                     // I2C 1 SDA TX HOLD VALUE
  Setup_Config->CbsCmnFchI2c2SdaTxHoldValue = 0x35;                     // I2C 2 SDA TX HOLD VALUE
  Setup_Config->CbsCmnFchI2c3SdaTxHoldValue = 0x35;                     // I2C 3 SDA TX HOLD VALUE
  Setup_Config->CbsCmnFchI2c4SdaTxHoldValue = 0x35;                     // I2C 4 SDA TX HOLD VALUE
  Setup_Config->CbsCmnFchI2c5SdaTxHoldValue = 0x35;                     // I2C 5 SDA TX HOLD VALUE
  Setup_Config->CbsCmnFchI2c0SdaRxHoldValue = 0x00;                     // I2C 0 SDA RX HOLD VALUE
  Setup_Config->CbsCmnFchI2c1SdaRxHoldValue = 0x00;                     // I2C 1 SDA RX HOLD VALUE
  Setup_Config->CbsCmnFchI2c2SdaRxHoldValue = 0x00;                     // I2C 2 SDA RX HOLD VALUE
  Setup_Config->CbsCmnFchI2c3SdaRxHoldValue = 0x00;                     // I2C 3 SDA RX HOLD VALUE
  Setup_Config->CbsCmnFchI2c4SdaRxHoldValue = 0x00;                     // I2C 4 SDA RX HOLD VALUE
  Setup_Config->CbsCmnFchI2c5SdaRxHoldValue = 0x00;                     // I2C 5 SDA RX HOLD VALUE
  Setup_Config->CbsCmnFchI3c0SdaHoldValue = 0x2;                        // I3C 0 SDA HOLD VALUE
  Setup_Config->CbsCmnFchI3c1SdaHoldValue = 0x2;                        // I3C 1 SDA HOLD VALUE
  Setup_Config->CbsCmnFchI3c2SdaHoldValue = 0x2;                        // I3C 2 SDA HOLD VALUE
  Setup_Config->CbsCmnFchI3c3SdaHoldValue = 0x2;                        // I3C 3 SDA HOLD VALUE
  Setup_Config->CbsCmnFchSataEnable = 0xf;                              // SATA Enable
  Setup_Config->CbsCmnFchSataClass = 2;                                 // SATA Mode
  Setup_Config->CbsCmnFchSataRasSupport = 0xf;                          // SATA RAS Support
  Setup_Config->CbsCmnFchSataStaggeredSpinup = 0xFF;                    // SATA Staggered Spin-up
  Setup_Config->CbsCmnFchSataAhciDisPrefetchFunction = 0xf;             // SATA Disabled AHCI Prefetch Function
  Setup_Config->CbsDbgFchSata0Enable = 0xf;                             // Sata0 Enable
  Setup_Config->CbsDbgFchSata1Enable = 0xf;                             // Sata1 Enable
  Setup_Config->CbsDbgFchSata2Enable = 0xf;                             // Sata2 Enable
  Setup_Config->CbsDbgFchSata3Enable = 0xf;                             // Sata3 Enable
  Setup_Config->CbsDbgFchSata4Enable = 0xf;                             // Sata4 (Socket1) Enable
  Setup_Config->CbsDbgFchSata5Enable = 0xf;                             // Sata5 (Socket1) Enable
  Setup_Config->CbsDbgFchSata6Enable = 0xf;                             // Sata6 (Socket1) Enable
  Setup_Config->CbsDbgFchSata7Enable = 0xf;                             // Sata7 (Socket1) Enable
  Setup_Config->CbsDbgFchSataeSATAPort0 = 0xf;                          // Sata0 eSATA Port0
  Setup_Config->CbsDbgFchSataeSATAPort1 = 0xf;                          // Sata0 eSATA Port1
  Setup_Config->CbsDbgFchSataeSATAPort2 = 0xf;                          // Sata0 eSATA Port2
  Setup_Config->CbsDbgFchSataeSATAPort3 = 0xf;                          // Sata0 eSATA Port3
  Setup_Config->CbsDbgFchSataeSATAPort4 = 0xf;                          // Sata0 eSATA Port4
  Setup_Config->CbsDbgFchSataeSATAPort5 = 0xf;                          // Sata0 eSATA Port5
  Setup_Config->CbsDbgFchSataeSATAPort6 = 0xf;                          // Sata0 eSATA Port6
  Setup_Config->CbsDbgFchSataeSATAPort7 = 0xf;                          // Sata0 eSATA Port7
  Setup_Config->CbsDbgFchSataMcmDie1EsataPort0 = 0xf;                   // Sata1 eSATA Port0
  Setup_Config->CbsDbgFchSataMcmDie1EsataPort1 = 0xf;                   // Sata1 eSATA Port1
  Setup_Config->CbsDbgFchSataMcmDie1EsataPort2 = 0xf;                   // Sata1 eSATA Port2
  Setup_Config->CbsDbgFchSataMcmDie1EsataPort3 = 0xf;                   // Sata1 eSATA Port3
  Setup_Config->CbsDbgFchSataMcmDie1EsataPort4 = 0xf;                   // Sata1 eSATA Port4
  Setup_Config->CbsDbgFchSataMcmDie1EsataPort5 = 0xf;                   // Sata1 eSATA Port5
  Setup_Config->CbsDbgFchSataMcmDie1EsataPort6 = 0xf;                   // Sata1 eSATA Port6
  Setup_Config->CbsDbgFchSataMcmDie1EsataPort7 = 0xf;                   // Sata1 eSATA Port7
  Setup_Config->CbsDbgFchSataMcmDie2EsataPort0 = 0xf;                   // Sata2 eSATA Port0
  Setup_Config->CbsDbgFchSataMcmDie2EsataPort1 = 0xf;                   // Sata2 eSATA Port1
  Setup_Config->CbsDbgFchSataMcmDie2EsataPort2 = 0xf;                   // Sata2 eSATA Port2
  Setup_Config->CbsDbgFchSataMcmDie2EsataPort3 = 0xf;                   // Sata2 eSATA Port3
  Setup_Config->CbsDbgFchSataMcmDie2EsataPort4 = 0xf;                   // Sata2 eSATA Port4
  Setup_Config->CbsDbgFchSataMcmDie2EsataPort5 = 0xf;                   // Sata2 eSATA Port5
  Setup_Config->CbsDbgFchSataMcmDie2EsataPort6 = 0xf;                   // Sata2 eSATA Port6
  Setup_Config->CbsDbgFchSataMcmDie2EsataPort7 = 0xf;                   // Sata2 eSATA Port7
  Setup_Config->CbsDbgFchSataMcmDie3EsataPort0 = 0xf;                   // Sata3 eSATA Port0
  Setup_Config->CbsDbgFchSataMcmDie3EsataPort1 = 0xf;                   // Sata3 eSATA Port1
  Setup_Config->CbsDbgFchSataMcmDie3EsataPort2 = 0xf;                   // Sata3 eSATA Port2
  Setup_Config->CbsDbgFchSataMcmDie3EsataPort3 = 0xf;                   // Sata3 eSATA Port3
  Setup_Config->CbsDbgFchSataMcmDie3EsataPort4 = 0xf;                   // Sata3 eSATA Port4
  Setup_Config->CbsDbgFchSataMcmDie3EsataPort5 = 0xf;                   // Sata3 eSATA Port5
  Setup_Config->CbsDbgFchSataMcmDie3EsataPort6 = 0xf;                   // Sata3 eSATA Port6
  Setup_Config->CbsDbgFchSataMcmDie3EsataPort7 = 0xf;                   // Sata3 eSATA Port7
  Setup_Config->CbsDbgFchSataMcmDie4EsataPort0 = 0xf;                   // Sata4 eSATA Port0
  Setup_Config->CbsDbgFchSataMcmDie4EsataPort1 = 0xf;                   // Sata4 eSATA Port1
  Setup_Config->CbsDbgFchSataMcmDie4EsataPort2 = 0xf;                   // Sata4 eSATA Port2
  Setup_Config->CbsDbgFchSataMcmDie4EsataPort3 = 0xf;                   // Sata4 eSATA Port3
  Setup_Config->CbsDbgFchSataMcmDie4EsataPort4 = 0xf;                   // Sata4 eSATA Port4
  Setup_Config->CbsDbgFchSataMcmDie4EsataPort5 = 0xf;                   // Sata4 eSATA Port5
  Setup_Config->CbsDbgFchSataMcmDie4EsataPort6 = 0xf;                   // Sata4 eSATA Port6
  Setup_Config->CbsDbgFchSataMcmDie4EsataPort7 = 0xf;                   // Sata4 eSATA Port7
  Setup_Config->CbsDbgFchSataMcmDie5EsataPort0 = 0xf;                   // Sata5 eSATA Port0
  Setup_Config->CbsDbgFchSataMcmDie5EsataPort1 = 0xf;                   // Sata5 eSATA Port1
  Setup_Config->CbsDbgFchSataMcmDie5EsataPort2 = 0xf;                   // Sata5 eSATA Port2
  Setup_Config->CbsDbgFchSataMcmDie5EsataPort3 = 0xf;                   // Sata5 eSATA Port3
  Setup_Config->CbsDbgFchSataMcmDie5EsataPort4 = 0xf;                   // Sata5 eSATA Port4
  Setup_Config->CbsDbgFchSataMcmDie5EsataPort5 = 0xf;                   // Sata5 eSATA Port5
  Setup_Config->CbsDbgFchSataMcmDie5EsataPort6 = 0xf;                   // Sata5 eSATA Port6
  Setup_Config->CbsDbgFchSataMcmDie5EsataPort7 = 0xf;                   // Sata5 eSATA Port7
  Setup_Config->CbsDbgFchSataMcmDie6EsataPort0 = 0xf;                   // Sata6 eSATA Port0
  Setup_Config->CbsDbgFchSataMcmDie6EsataPort1 = 0xf;                   // Sata6 eSATA Port1
  Setup_Config->CbsDbgFchSataMcmDie6EsataPort2 = 0xf;                   // Sata6 eSATA Port2
  Setup_Config->CbsDbgFchSataMcmDie6EsataPort3 = 0xf;                   // Sata6 eSATA Port3
  Setup_Config->CbsDbgFchSataMcmDie6EsataPort4 = 0xf;                   // Sata6 eSATA Port4
  Setup_Config->CbsDbgFchSataMcmDie6EsataPort5 = 0xf;                   // Sata6 eSATA Port5
  Setup_Config->CbsDbgFchSataMcmDie6EsataPort6 = 0xf;                   // Sata6 eSATA Port6
  Setup_Config->CbsDbgFchSataMcmDie6EsataPort7 = 0xf;                   // Sata6 eSATA Port7
  Setup_Config->CbsDbgFchSataMcmDie7EsataPort0 = 0xf;                   // Sata7 eSATA Port0
  Setup_Config->CbsDbgFchSataMcmDie7EsataPort1 = 0xf;                   // Sata7 eSATA Port1
  Setup_Config->CbsDbgFchSataMcmDie7EsataPort2 = 0xf;                   // Sata7 eSATA Port2
  Setup_Config->CbsDbgFchSataMcmDie7EsataPort3 = 0xf;                   // Sata7 eSATA Port3
  Setup_Config->CbsDbgFchSataMcmDie7EsataPort4 = 0xf;                   // Sata7 eSATA Port4
  Setup_Config->CbsDbgFchSataMcmDie7EsataPort5 = 0xf;                   // Sata7 eSATA Port5
  Setup_Config->CbsDbgFchSataMcmDie7EsataPort6 = 0xf;                   // Sata7 eSATA Port6
  Setup_Config->CbsDbgFchSataMcmDie7EsataPort7 = 0xf;                   // Sata7 eSATA Port7
  Setup_Config->CbsDbgFchSataAggresiveDevSlpP0 = 0xF;                   // Socket0 DevSlp0 Enable
  Setup_Config->CbsDbgFchSataDevSlpController0Num = 0;                  // Socket0 DevSlp0 Controller Number
  Setup_Config->CbsDbgFchSataDevSlpPort0Num = 0;                        // Socket0 DevSlp0 Port Number
  Setup_Config->CbsDbgFchSataAggresiveDevSlpP1 = 0xF;                   // Socket0 DevSlp1 Enable
  Setup_Config->CbsDbgFchSataDevSlpController1Num = 1;                  // Socket0 DevSlp1 Controller Number
  Setup_Config->CbsDbgFchSataDevSlpPort1Num = 0;                        // Socket0 DevSlp1 Port Number
  Setup_Config->CbsDbgFchSataMcmDie4DevSlp0 = 0xf;                      // Socket1 DevSlp0 Enable
  Setup_Config->CbsDbgFchSataMcmDie4DevSlpController0Num = 4;           // Socket1 DevSlp0 Controller Number
  Setup_Config->CbsDbgFchSataMcmDie4DevSlp0Num = 0;                     // Socket1 DevSlp0 Port Number
  Setup_Config->CbsDbgFchSataMcmDie4DevSlp1 = 0xf;                      // Socket1 DevSlp1 Enable
  Setup_Config->CbsDbgFchSataMcmDie4DevSlpController1Num = 5;           // Socket1 DevSlp1 Controller Number
  Setup_Config->CbsDbgFchSataMcmDie4DevSlp1Num = 1;                     // Socket1 DevSlp1 Port Number
  Setup_Config->CbsDbgFchSataSgpio0 = 0xf;                              // Sata0 SGPIO
  Setup_Config->CbsDbgFchSataMcmDie1Sgpio0 = 0xf;                       // Sata1 SGPIO
  Setup_Config->CbsDbgFchSataMcmDie2Sgpio0 = 0xf;                       // Sata2 SGPIO
  Setup_Config->CbsDbgFchSataMcmDie3Sgpio0 = 0xf;                       // Sata3 SGPIO
  Setup_Config->CbsDbgFchSataMcmDie4Sgpio0 = 0xf;                       // Sata4 SGPIO
  Setup_Config->CbsDbgFchSataMcmDie5Sgpio0 = 0xf;                       // Sata5 SGPIO
  Setup_Config->CbsDbgFchSataMcmDie6Sgpio0 = 0xf;                       // Sata6 SGPIO
  Setup_Config->CbsDbgFchSataMcmDie7Sgpio0 = 0xf;                       // Sata7 SGPIO
  Setup_Config->CbsCmnFchUsbXHCI0Enable = 0xf;                          // XHCI Controller0 enable
  Setup_Config->CbsCmnFchUsbXHCI1Enable = 0xf;                          // XHCI Controller1 enable
  Setup_Config->CbsCmnFchUsbXHCI2Enable = 1;                            // XHCI2 enable (Socket1)
  Setup_Config->CbsCmnFchUsbXHCI3Enable = 1;                            // XHCI3 enable (Socket1)
  Setup_Config->CbsCmnFchSystemPwrFailShadow = 0;                       // Ac Loss Control
  Setup_Config->CbsCmnFchPwrFailShadowABLEnabled = 0xF;                 // Set Fch Power failed Shadow in ABL
  Setup_Config->CbsCmnFchUart0Config = 0xf;                             // Uart 0 Enable
  Setup_Config->CbsCmnFchUart0LegacyConfig = 0xf;                       // Uart 0 Legacy Options
  Setup_Config->CbsCmnFchUart1Config = 0xf;                             // Uart 1 Enable
  Setup_Config->CbsCmnFchUart1LegacyConfig = 0xf;                       // Uart 1 Legacy Options
  Setup_Config->CbsCmnFchUart2Config = 0xf;                             // Uart 2 Enable
  Setup_Config->CbsCmnFchUart2LegacyConfig = 0xf;                       // Uart 2 Legacy Options
  Setup_Config->CbsCmnFchAlinkRasSupport = 0xf;                         // ALink RAS Support
  Setup_Config->CbsDbgFchSyncfloodEnable = 0xf;                         // Reset After Sync-Flood
  Setup_Config->CbsDbgFchDelaySyncflood = 0;                            // Delay Reset After Sync-Flood
  Setup_Config->CbsDbgFchSystemSpreadSpectrum = 0xf;                    // FCH Spread Spectrum
  Setup_Config->CbsCmnBootTimerEnable = 2;                              // Boot Timer Enable
  Setup_Config->CbsCmnSP3NtbP0P0 = 0xF;                                 // Socket-0 P0 NTB Enable
  Setup_Config->CbsCmnSP3NtbStartLaneP0P0 = 0;                          // Socket-0 P0 Start Lane
  Setup_Config->CbsCmnSP3NtbEndLaneP0P0 = 15;                           // Socket-0 P0 End Lane
  Setup_Config->CbsCmnSP3NtbLinkSpeedP0P0 = 0xf;                        // Socket-0 P0 Link Speed
  Setup_Config->CbsCmnSP3NtbModeP0P0 = 0xf;                             // Socket-0 P0 NTB Mode
  Setup_Config->CbsCmnSP3NtbP0P2 = 0xF;                                 // Socket-0 P2 NTB Enable
  Setup_Config->CbsCmnSP3NtbStartLaneP0P2 = 48;                         // Socket-0 P2 Start Lane
  Setup_Config->CbsCmnSP3NtbEndLaneP0P2 = 63;                           // Socket-0 P2 End Lane
  Setup_Config->CbsCmnSP3NtbLinkSpeedP0P2 = 0xf;                        // Socket-0 P2 Link Speed
  Setup_Config->CbsCmnSP3NtbModeP0P2 = 0xf;                             // Socket-0 P2 NTB Mode
  Setup_Config->CbsCmnSocAblConOut = 2;                                 // ABL Console Out Control
  Setup_Config->CbsCmnSocAblConOutSerialPort = 0xFF;                    // ABL Console Out Serial Port
  Setup_Config->CbsCmnSocAblConOutSerialPortIO = 0xFF;                  // ABL Console Out Serial Port IO
  Setup_Config->CbsCmnSocAblSerialPortIOCustomEnabled = 0;              // ABL Serial port IO customized enabled
  Setup_Config->CbsCmnSocAblConOutSerialPortIOCustom = 0;               // ABL Console out Serial Port IO Customized
  Setup_Config->CbsCmnSocAblConOutBasic = 0xFF;                         // ABL Basic Console Out Control
  Setup_Config->CbsCmnSocAblPmuMsgCtrl = 0xFF;                          // ABL PMU message Control
  Setup_Config->CbsCmnSocAblMemPopMsgCtrl = 0;                          // ABL Memory Population message Control
  Setup_Config->CbsCmnPrintSocket1PmuMsgBlock = 0xFF;                   // Print Socket 1 PMU MsgBlock
  Setup_Config->CbsCmnPrintSocket1TrainingLog = 0xFF;                   // Print Socket 1 PMU Training Log
  Setup_Config->CbsDfCmnPspErrInj = 0;                                  // PSP error injection support
  Setup_Config->CbsNumberOfSockets = 0;                                 // Number of Sockets
  Setup_Config->CbsCmnSecI2cVoltMode = 0xFF;                            // SEC_I2C Voltage Mode
  Setup_Config->CbsCmnSocFarEnforced = 0;                               // FAR enforcement state
  Setup_Config->CbsCmnSocSplFuse = 0;                                   // SPL value in the CPU fuse
  Setup_Config->CbsCmnSocSplValueInTbl = 0;                             // SPL value in the SPL table
  Setup_Config->CbsCmnSocFarSwitch = 0xFF;                              // FAR Switch
  Setup_Config->CbsCmnCxlControl = 0;                                   // CXL Control
  Setup_Config->CbsCmnCxlSdpReqSysAddr = 0xFF;                          // CXL Physical Addressing
  Setup_Config->CbsCmnCxlSpm = 0xf;                                     // CXL Memory Attribute
  Setup_Config->CbsCmnCxlEncryption = 0;                                // CXL Encryption
  Setup_Config->CbsCmnCxlDvsecLock = 0xf;                               // CXL DVSEC Lock
  Setup_Config->CbsCmnCxlHdmDecoderLockOnCommit = 0xf;                  // CXL HDM Decoder Lock On Commit
  Setup_Config->CbsCmnCxlTempGen5Advertisement = 0xF;                   // Temp Gen5 Advertisement
  Setup_Config->CbsCmnSyncHeaderByPass = 0xf;                           // Sync Header Bypass
  Setup_Config->CbsCxlSyncHeaderBypassCompMode = 0xF;                   // Sync Header Bypass Compatibility Mode
  Setup_Config->CbsCmnCxlMemOnlineOffline = 0;                          // CXL Memory Online/Offline
  Setup_Config->CbsDbgCxlOverideCxlMemorySize = 0xFF;                   // Override CXL Memory Size
  Setup_Config->CbsCmnCxlProtocolErrorReporting = 1;                    // CXL Protocol Error Reporting
  Setup_Config->CbsCmnCxlComponentErrorReporting = 2;                   // CXL Component Error Reporting
  Setup_Config->CbsCmnCxlMemIsolationEnable = 0xf;                      // CXL Root Port Isolation
  Setup_Config->CbsCmnCxlMemIsolationFwNotification = 0xF;              // CXL Root Port Isolation FW Notification


  Setup_Config->Header.CbsVariableStructUniqueValue = 0x42a2239a;
  Setup_Config->Header.ApcbVariableHash = 0x3cb618cc;
  Setup_Config->Header.CbsRevisionNumber = 0x0;

  
}
