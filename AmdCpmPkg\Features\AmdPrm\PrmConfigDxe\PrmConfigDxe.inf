#;*****************************************************************************
#;
#; Copyright (C) 2018-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

## @file
#  PRM Configuration Driver
#
#  This driver configures PRM Module settings during the boot services environment.
#
#  Copyright (c) Microsoft Corporation
#  Copyright (c) 2020, Intel Corporation. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION           = 0x00010005
  BASE_NAME             = PrmConfigDxe
  FILE_GUID             = D8E4C555-0BB6-4C6D-94CD-36072D59DCD2
  MODULE_TYPE           = DXE_RUNTIME_DRIVER
  VERSION_STRING        = 1.0
  ENTRY_POINT           = PrmConfigEntryPoint

[Sources]
  PrmConfigDxe.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  AgesaModulePkg/AgesaModuleRasPkg.dec
  AgesaPkg/AgesaPkg.dec
# AMI PORTING  
#  edk2/PrmPkg/PrmPkg.dec  #Please add the PrmPkg package(edk2/PrmPkg) from the EDKII source so that supports the PRM feature properly.
  PrmPkg/PrmPkg.dec
  AmdCpmPkg/AmdCpmPkg.dec

[Guids]
  gEfiEndOfDxeEventGroupGuid
  gEfiEventVirtualAddressChangeGuid

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  DebugLib
  DxeServicesTableLib
  MemoryAllocationLib
  UefiBootServicesTableLib
  UefiDriverEntryPoint
  UefiRuntimeServicesTableLib
  UefiLib
  DxeAddressTranslateModuleConfigLib

[Protocols]
  gPrmConfigProtocolGuid
  gAmdPciResourceProtocolGuid

[Depex]
  TRUE
