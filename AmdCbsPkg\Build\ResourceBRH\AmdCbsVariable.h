/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */
#ifndef _AMD_CBS_VARIABLE_H_
#define _AMD_CBS_VARIABLE_H_

#pragma pack(push,1)

typedef struct _CBS_VARIABLE_HEADER
{
  UINT32 CbsVariableStructUniqueValue;                             ///< Will be different if CBS varialbe structure changes
  UINT32 NewRecordOffset;                                          ///< Record the offset of reserved region start, which also the offset of the new record
  UINT32 ApcbVariableHash;                                         ///< Record the APCB Hash Value
  UINT16 CbsComboChipsetFlag;                                      ///< Combo Chipset Flag
  UINT8  CbsChipsetVisibleFlag0;                                   ///< Chipset Visible Flag0
  UINT8  CbsChipsetVisibleFlag1;                                   ///< Chipset Visible Flag1
  UINT32 CbsRevisionNumber;                                        ///< CBS Revision Number
  UINT8  CbsMemTech;                                               ///< Memory Technology Flag
  UINT8  Reserved[11];                                             ///< Reserved for future use
} CBS_VARIABLE_HEADER;


typedef struct _CBS_CONFIG {
  CBS_VARIABLE_HEADER  Header;                                    ///< Variable header
  UINT8         CbsComboFlag;                                     ///< Combo CBS
  UINT8         CbsCpuSmtCtrl;                                    ///< SMT Control
  UINT16        CbsCmnCpuReqMinFreq;                              ///< Requested CPU min frequency
  UINT8         CbsCmnCpuEnReqMinFreq;                            ///< Enable Requested CPU min frequency
  UINT8         CbsCmnCpuRMSS;                                    ///< REP-MOV/STOS Streaming
  UINT8         CbsCmnCpuGenWA05;                                 ///< RedirectForReturnDis
  UINT8         CbsCmnCpuPfeh;                                    ///< Platform First Error Handling
  UINT8         CbsCmnCpuCpb;                                     ///< Core Performance Boost
  UINT8         CbsCmnCpuGlobalCstateCtrl;                        ///< Global C-state Control
  UINT8         CbsCmnGnbPowerSupplyIdleCtrl;                     ///< Power Supply Idle Control
  UINT8         CbsCmnCpuStreamingStoresCtrl;                     ///< Streaming Stores Control
  UINT8         CbsDbgCpuLApicMode;                               ///< Local APIC Mode
  UINT8         CbsCmnCpuCstC1Ctrl;                               ///< ACPI _CST C1 Declaration
  UINT16        CbsCmnCpuCstC2Latency;                            ///< ACPI CST C2 Latency
  UINT8         CbsCmnCpuMcaErrThreshEn;                          ///< MCA error thresh enable
  UINT16        CbsCmnCpuMcaErrThreshCount;                       ///< MCA error thresh count
  UINT8         CbsCmnCpuMcaFruTextEn;                            ///< MCA FruText
  UINT8         CbsCmnCpuSmuPspDebugMode;                         ///< SMU and PSP Debug Mode
  UINT8         CbsCmnCpuPpinCtrl;                                ///< PPIN Opt-in
  UINT8         CbsCmnCpuSmee;                                    ///< SMEE
  UINT8         CbsPspSevCtrl;                                    ///< SEV Control
  UINT32        CbsCmnCpuSevAsidSpaceLimit;                       ///< SEV-ES ASID Space Limit
  UINT8         CbsDbgCpuSnpMemCover;                             ///< SNP Memory (RMP Table) Coverage
  UINT32        CbsDbgCpuSnpMemSizeCover;                         ///< Amount of Memory to Cover
  UINT8         CbsCmnCpu64BitMMIOCoverage;                       ///< RMP Coverage for 64Bit MMIO Ranges
  UINT16        CbsCmnCpu64BitMMIORmpS0RBMask;                    ///< Socket0 RootBridge Mask for 64Bit MMIO RMP Coverage
  UINT16        CbsCmnCpu64BitMMIORmpS1RBMask;                    ///< Socket1 RootBridge Mask for 64Bit MMIO RMP Coverage
  UINT8         CbsDbgCpuSplitRMP;                                ///< Split RMP Table
  UINT8         CbsDbgCpuSegmentedRMP;                            ///< Segmented RMP Table
  UINT8         CbsDbgCpuRmpSegmentSize;                          ///< RMP Segment Size
  UINT8         CbsCmnActionOnBistFailure;                        ///< Action on BIST Failure
  UINT8         CbsCmnCpuERMS;                                    ///< Enhanced REP MOVSB/STOSB (ERSM)
  UINT8         CbsCmnCpuLogTransparentErrors;                    ///< Log Transparent Errors
  UINT8         CbsCmnCpuAvx512;                                  ///< AVX512
  UINT8         CbsCmnCpuDisFstStrErmsb;                          ///< ERMSB Caching Behavior
  UINT8         CbsCmnCpuMonMwaitDis;                             ///< MONITOR and MWAIT disable
  UINT8         CbsCpuSpeculativeStoreModes;                      ///< CPU Speculative Store Modes
  UINT8         CbsCmnCpuFSRM;                                    ///< Fast Short REP MOVSB (FSRM)
  UINT8         CbsCmnCpuPauseCntSel_1_0;                         ///< PauseCntSel_1_0
  UINT8         CbsCmnCpuPfReqThrEn;                              ///< Prefetch/Request Throttle
  UINT8         CbsCmnCmcNotificationType;                        ///< CMC H/W Error Notification type
  UINT8         CbsCmnCpuScanDumpDbgEn;                           ///< Scan Dump Debug Enable
  UINT8         CbsCmnCpuMcax64BankSupport;                       ///< MCAX 64 bank support
  UINT8         CbsCmnCpuAdaptiveAlloc;                           ///< Adaptive Allocation (AA)
  UINT8         CbsCpuLatencyUnderLoad;                           ///< Latency Under Load (LUL)
  UINT8         CbsCmnCoreTraceDumpEn;                            ///< Core Trace Dump Enable
  UINT8         CbsCmnCpuFP512;                                   ///< FP512
  UINT8         CbsCmnCpuAmdErmsbRepo;                            ///< AMD_ERMSB Reporting
  UINT8         CbsCmnCpuOcMode;                                  ///< OC Mode
  UINT8         CbsCmnCpuDowncoreMode;                            ///< DownCore Mode
  UINT8         CbsCpuLegalDisclaimer;                            ///< Pstates Disclaimer
  UINT8         CbsCpuLegalDisclaimer1;                           ///< Pstates Disclaimer 1
  UINT8         CbsCpuPstCustomP0;                                ///< Custom Pstate0
  UINT32        CbsCpuPst0Freq;                                   ///< Pstate0 Freq (MHz)
  UINT32        CbsCpuCofP0;                                      ///< Frequency (MHz)
  UINT32        CbsCpuVoltageP0;                                  ///< Voltage (uV)
  UINT32        CbsCpuPst0Fid;                                    ///< Pstate0 FID
  UINT32        CbsCpuPst0Vid;                                    ///< Pstate0 VID
  UINT8         CbsCpuPstCustomP1;                                ///< Custom Pstate1
  UINT32        CbsCpuCofP1;                                      ///< Frequency (MHz)
  UINT32        CbsCpuVoltageP1;                                  ///< Voltage (uV)
  UINT32        CbsCpuPst1Fid;                                    ///< Pstate1 FID
  UINT32        CbsCpuPst1Vid;                                    ///< Pstate1 VID
  UINT8         CbsCpuPstCustomP2;                                ///< Custom Pstate2
  UINT32        CbsCpuCofP2;                                      ///< Frequency (MHz)
  UINT32        CbsCpuVoltageP2;                                  ///< Voltage (uV)
  UINT32        CbsCpuPst2Fid;                                    ///< Pstate2 FID
  UINT32        CbsCpuPst2Vid;                                    ///< Pstate2 VID
  UINT8         CbsCpuPstCustomP3;                                ///< Custom Pstate3
  UINT32        CbsCpuCofP3;                                      ///< Frequency (MHz)
  UINT32        CbsCpuVoltageP3;                                  ///< Voltage (uV)
  UINT32        CbsCpuPst3Fid;                                    ///< Pstate3 FID
  UINT32        CbsCpuPst3Vid;                                    ///< Pstate3 VID
  UINT8         CbsCpuPstCustomP4;                                ///< Custom Pstate4
  UINT32        CbsCpuCofP4;                                      ///< Frequency (MHz)
  UINT32        CbsCpuVoltageP4;                                  ///< Voltage (uV)
  UINT32        CbsCpuPst4Fid;                                    ///< Pstate4 FID
  UINT32        CbsCpuPst4Vid;                                    ///< Pstate4 VID
  UINT8         CbsCpuPstCustomP5;                                ///< Custom Pstate5
  UINT32        CbsCpuCofP5;                                      ///< Frequency (MHz)
  UINT32        CbsCpuVoltageP5;                                  ///< Voltage (uV)
  UINT32        CbsCpuPst5Fid;                                    ///< Pstate5 FID
  UINT32        CbsCpuPst5Vid;                                    ///< Pstate5 VID
  UINT8         CbsCpuPstCustomP6;                                ///< Custom Pstate6
  UINT32        CbsCpuCofP6;                                      ///< Frequency (MHz)
  UINT32        CbsCpuVoltageP6;                                  ///< Voltage (uV)
  UINT32        CbsCpuPst6Fid;                                    ///< Pstate6 FID
  UINT32        CbsCpuPst6Vid;                                    ///< Pstate6 VID
  UINT8         CbsCpuPstCustomP7;                                ///< Custom Pstate7
  UINT32        CbsCpuCofP7;                                      ///< Frequency (MHz)
  UINT32        CbsCpuVoltageP7;                                  ///< Voltage (uV)
  UINT32        CbsCpuPst7Fid;                                    ///< Pstate7 FID
  UINT32        CbsCpuPst7Vid;                                    ///< Pstate7 VID
  UINT32        CbsCmnCpuCcd0DowncoreBitMap;                      ///< CCD 0 DownCore Bitmap
  UINT32        CbsCmnCpuCcd1DowncoreBitMap;                      ///< CCD 1 DownCore Bitmap
  UINT32        CbsCmnCpuCcd2DowncoreBitMap;                      ///< CCD 2 DownCore Bitmap
  UINT32        CbsCmnCpuCcd3DowncoreBitMap;                      ///< CCD 3 DownCore Bitmap
  UINT32        CbsCmnCpuCcd4DowncoreBitMap;                      ///< CCD 4 DownCore Bitmap
  UINT32        CbsCmnCpuCcd5DowncoreBitMap;                      ///< CCD 5 DownCore Bitmap
  UINT32        CbsCmnCpuCcd6DowncoreBitMap;                      ///< CCD 6 DownCore Bitmap
  UINT32        CbsCmnCpuCcd7DowncoreBitMap;                      ///< CCD 7 DownCore Bitmap
  UINT32        CbsCmnCpuCcd8DowncoreBitMap;                      ///< CCD 8 DownCore Bitmap
  UINT32        CbsCmnCpuCcd9DowncoreBitMap;                      ///< CCD 9 DownCore Bitmap
  UINT32        CbsCmnCpuCcd10DowncoreBitMap;                     ///< CCD 10 DownCore Bitmap
  UINT32        CbsCmnCpuCcd11DowncoreBitMap;                     ///< CCD 11 DownCore Bitmap
  UINT32        CbsCmnCpuCcd12DowncoreBitMap;                     ///< CCD 12 DownCore Bitmap
  UINT32        CbsCmnCpuCcd13DowncoreBitMap;                     ///< CCD 13 DownCore Bitmap
  UINT32        CbsCmnCpuCcd14DowncoreBitMap;                     ///< CCD 14 DownCore Bitmap
  UINT32        CbsCmnCpuCcd15DowncoreBitMap;                     ///< CCD 15 DownCore Bitmap
  UINT8         CbsCpuCcdCtrl;                                    ///< CCD Control
  UINT8         CbsCpuCoreCtrl;                                   ///< Core control
  UINT8         CbsCmnCpuL1StreamHwPrefetcher;                    ///< L1 Stream HW Prefetcher
  UINT8         CbsCmnCpuL1StridePrefetcher;                      ///< L1 Stride Prefetcher
  UINT8         CbsCmnCpuL1RegionPrefetcher;                      ///< L1 Region Prefetcher
  UINT8         CbsCmnCpuL2StreamHwPrefetcher;                    ///< L2 Stream HW Prefetcher
  UINT8         CbsCmnCpuL2UpDownPrefetcher;                      ///< L2 Up/Down Prefetcher
  UINT8         CbsCmnCpuL1BurstPrefetchMode;                     ///< L1 Burst Prefetch Mode
  UINT8         CbsDbgCpuGenCpuWdt;                               ///< Core Watchdog Timer Enable
  UINT16        CbsDbgCpuGenCpuWdtTimeout;                        ///< Core Watchdog Timer Interval
  UINT8         CbsDfCmnWdtInterval;                              ///< DF Watchdog Timer Interval
  UINT8         CbsDfCmnExtIpSyncFloodProp;                       ///< Disable DF to external IP SyncFloodPropagation
  UINT8         CbsDfCmnDisSyncFloodProp;                         ///< Sync Flood Propagation to DF Components
  UINT8         CbsDfCmnFreezeQueueError;                         ///< Freeze DF module queues on error
  UINT8         CbsDfCmnCc6MemEncryption;                         ///< CC6 memory region encryption
  UINT8         CbsDfCmnCcdBwThrottleLv;                          ///< CCD B/W Balance Throttle Level
  UINT32        CbsDfDbgNumPciSegments;                           ///< Number of PCI Segments
  UINT8         CbsDfCmnCcmThrot;                                 ///< CCM Throttler
  UINT8         CbsDfCmnFineThrotHeavy;                           ///< MemReqBandwidthControl[FineThrotHeavy]
  UINT8         CbsDfCmnFineThrotLight;                           ///< MemReqBandwidthControl[FineThrotLight]
  UINT8         CbsDfCmnCleanVicFtiCmdBal;                        ///< Clean Victim FTI Cmd Balancing
  UINT8         CbsDfCmnReqvReqNDImbThr;                          ///< CCMConfig5[ReqvReqNDImbThr]
  UINT8         CbsDfCmnCxlStronglyOrderedWrites;                 ///< CXL Strongly Ordered Writes
  UINT8         CbsDfCmnEnhancedPartWr;                           ///< Enhanced Partial Writes to Same Address
  UINT8         CbsDfCmnDramNps;                                  ///< NUMA nodes per socket
  UINT8         CbsDfCmnMemIntlv;                                 ///< Memory interleaving
  UINT8         CbsDfCmnMixedInterleavedMode;                     ///< Mixed interleaved mode
  UINT8         CbsDfCmnCxlMemIntlv;                              ///< CXL Memory interleaving
  UINT8         CbsDfCnliSublinkInterleaving;                     ///< CXL Sublink interleaving
  UINT8         CbsDfCmnDramMapInversion;                         ///< DRAM map inversion
  UINT8         CbsDfCmnCc6AllocationScheme;                      ///< Location of private memory regions
  UINT8         CbsDfCmnAcpiSratL3Numa;                           ///< ACPI SRAT L3 Cache As NUMA Domain
  UINT8         CbsDfCmnAcpiSlitDistCtrl;                         ///< ACPI SLIT Distance Control
  UINT8         CbsDfCmnAcpiSlitRemoteFar;                        ///< ACPI SLIT remote relative distance
  UINT8         CbsDfCmnAcpiSlitVirtualDist;                      ///< ACPI SLIT virtual distance
  UINT8         CbsDfCmnAcpiSlitLclDist;                          ///< ACPI SLIT same socket distance
  UINT8         CbsDfCmnAcpiSlitRmtDist;                          ///< ACPI SLIT remote socket distance
  UINT8         CbsDfCmnAcpiSlitCxlLcl;                           ///< ACPI SLIT local CXL distance
  UINT8         CbsDfCmnAcpiSlitCxlRmt;                           ///< ACPI SLIT remote CXL distance
  UINT8         CbsDfCmnGmiEncryption;                            ///< GMI encryption control
  UINT8         CbsDfCmnXGmiEncryption;                           ///< xGMI encryption control
  UINT8         CbsDfDbgXgmiLinkCfg;                              ///< xGMI Link Configuration
  UINT8         CbsDfCmn4LinkMaxXgmiSpeed;                        ///< 4-link xGMI max speed
  UINT8         CbsDfCmn3LinkMaxXgmiSpeed;                        ///< 3-link xGMI max speed
  UINT8         CbsDfXgmiCrcScale;                                ///< xGMI CRC Scale
  UINT8         CbsDfXgmiCrcThreshold;                            ///< xGMI CRC Threshold
  UINT8         CbsDfXgmiPresetControl;                           ///< xGMI Preset Control
  UINT8         CbsDfXgmiTrainingErrMask;                         ///< xGMI Training Err Mask
  UINT32        CbsDfXgmiPresetP11;                               ///< Preset P11 (APCB)
  UINT32        CbsDfXgmiCmn1P11;                                 ///< Preset P11 Cmn1
  UINT32        CbsDfXgmiCnP11;                                   ///< Preset P11 Cn
  UINT32        CbsDfXgmiCnp1P11;                                 ///< Preset P11 Cnp1
  UINT32        CbsDfXgmiPresetP12;                               ///< Preset P12 (APCB)
  UINT32        CbsDfXgmiCmn1P12;                                 ///< Preset P12 Cmn1
  UINT32        CbsDfXgmiCnP12;                                   ///< Preset P12 Cn
  UINT32        CbsDfXgmiCnp1P12;                                 ///< Preset P12 Cnp1
  UINT32        CbsDfXgmiPresetP13;                               ///< Preset P13 (APCB)
  UINT32        CbsDfXgmiCmn1P13;                                 ///< Preset P13 Cmn1
  UINT32        CbsDfXgmiCnP13;                                   ///< Preset P13 Cn
  UINT32        CbsDfXgmiCnp1P13;                                 ///< Preset P13 Cnp1
  UINT32        CbsDfXgmiPresetP14;                               ///< Preset P14 (APCB)
  UINT32        CbsDfXgmiCmn1P14;                                 ///< Preset P14 Cmn1
  UINT32        CbsDfXgmiCnP14;                                   ///< Preset P14 Cn
  UINT32        CbsDfXgmiCnp1P14;                                 ///< Preset P14 Cnp1
  UINT32        CbsDfXgmiPresetP15;                               ///< Preset P15 (APCB)
  UINT32        CbsDfXgmiCmn1P15;                                 ///< Preset P15 Cmn1
  UINT32        CbsDfXgmiCnP15;                                   ///< Preset P15 Cn
  UINT32        CbsDfXgmiCnp1P15;                                 ///< Preset P15 Cnp1
  UINT16        CbsDfXgmiInitPresetS0L0;                          ///< Initial Preset Socket 0 Link 0 Pstate0/1/2/3 (APCB)
  UINT16        CbsDfXgmiInitPresetS0L0P0;                        ///< Initial Preset Socket 0 Link 0 Pstate0
  UINT16        CbsDfXgmiInitPresetS0L0P1;                        ///< Initial Preset Socket 0 Link 0 Pstate1
  UINT16        CbsDfXgmiInitPresetS0L0P2;                        ///< Initial Preset Socket 0 Link 0 Pstate2
  UINT16        CbsDfXgmiInitPresetS0L0P3;                        ///< Initial Preset Socket 0 Link 0 Pstate3
  UINT16        CbsDfXgmiInitPresetS0L1;                          ///< Initial Preset Socket 0 Link 1 Pstate0/1/2/3 (APCB)
  UINT16        CbsDfXgmiInitPresetS0L1P0;                        ///< Initial Preset Socket 0 Link 1 Pstate0
  UINT16        CbsDfXgmiInitPresetS0L1P1;                        ///< Initial Preset Socket 0 Link 1 Pstate1
  UINT16        CbsDfXgmiInitPresetS0L1P2;                        ///< Initial Preset Socket 0 Link 1 Pstate2
  UINT16        CbsDfXgmiInitPresetS0L1P3;                        ///< Initial Preset Socket 0 Link 1 Pstate3
  UINT16        CbsDfXgmiInitPresetS0L2;                          ///< Initial Preset Socket 0 Link 2 Pstate0/1/2/3 (APCB)
  UINT16        CbsDfXgmiInitPresetS0L2P0;                        ///< Initial Preset Socket 0 Link 2 Pstate0
  UINT16        CbsDfXgmiInitPresetS0L2P1;                        ///< Initial Preset Socket 0 Link 2 Pstate1
  UINT16        CbsDfXgmiInitPresetS0L2P2;                        ///< Initial Preset Socket 0 Link 2 Pstate2
  UINT16        CbsDfXgmiInitPresetS0L2P3;                        ///< Initial Preset Socket 0 Link 2 Pstate3
  UINT16        CbsDfXgmiInitPresetS0L3;                          ///< Initial Preset Socket 0 Link 3 Pstate0/1/2/3 (APCB)
  UINT16        CbsDfXgmiInitPresetS0L3P0;                        ///< Initial Preset Socket 0 Link 3 Pstate0
  UINT16        CbsDfXgmiInitPresetS0L3P1;                        ///< Initial Preset Socket 0 Link 3 Pstate1
  UINT16        CbsDfXgmiInitPresetS0L3P2;                        ///< Initial Preset Socket 0 Link 3 Pstate2
  UINT16        CbsDfXgmiInitPresetS0L3P3;                        ///< Initial Preset Socket 0 Link 3 Pstate3
  UINT16        CbsDfXgmiInitPresetS1L0;                          ///< Initial Preset Socket 1 Link 0 Pstate0/1/2/3 (APCB)
  UINT16        CbsDfXgmiInitPresetS1L0P0;                        ///< Initial Preset Socket 1 Link 0 Pstate0
  UINT16        CbsDfXgmiInitPresetS1L0P1;                        ///< Initial Preset Socket 1 Link 0 Pstate1
  UINT16        CbsDfXgmiInitPresetS1L0P2;                        ///< Initial Preset Socket 1 Link 0 Pstate2
  UINT16        CbsDfXgmiInitPresetS1L0P3;                        ///< Initial Preset Socket 1 Link 0 Pstate3
  UINT16        CbsDfXgmiInitPresetS1L1;                          ///< Initial Preset Socket 1 Link 1 Pstate0/1/2/3 (APCB)
  UINT16        CbsDfXgmiInitPresetS1L1P0;                        ///< Initial Preset Socket 1 Link 1 Pstate0
  UINT16        CbsDfXgmiInitPresetS1L1P1;                        ///< Initial Preset Socket 1 Link 1 Pstate1
  UINT16        CbsDfXgmiInitPresetS1L1P2;                        ///< Initial Preset Socket 1 Link 1 Pstate2
  UINT16        CbsDfXgmiInitPresetS1L1P3;                        ///< Initial Preset Socket 1 Link 1 Pstate3
  UINT16        CbsDfXgmiInitPresetS1L2;                          ///< Initial Preset Socket 1 Link 2 Pstate0/1/2/3 (APCB)
  UINT16        CbsDfXgmiInitPresetS1L2P0;                        ///< Initial Preset Socket 1 Link 2 Pstate0
  UINT16        CbsDfXgmiInitPresetS1L2P1;                        ///< Initial Preset Socket 1 Link 2 Pstate1
  UINT16        CbsDfXgmiInitPresetS1L2P2;                        ///< Initial Preset Socket 1 Link 2 Pstate2
  UINT16        CbsDfXgmiInitPresetS1L2P3;                        ///< Initial Preset Socket 1 Link 2 Pstate3
  UINT16        CbsDfXgmiInitPresetS1L3;                          ///< Initial Preset Socket 1 Link 3 Pstate0/1/2/3 (APCB)
  UINT16        CbsDfXgmiInitPresetS1L3P0;                        ///< Initial Preset Socket 1 Link 3 Pstate0
  UINT16        CbsDfXgmiInitPresetS1L3P1;                        ///< Initial Preset Socket 1 Link 3 Pstate1
  UINT16        CbsDfXgmiInitPresetS1L3P2;                        ///< Initial Preset Socket 1 Link 3 Pstate2
  UINT16        CbsDfXgmiInitPresetS1L3P3;                        ///< Initial Preset Socket 1 Link 3 Pstate3
  UINT32        CbsDfXgmiTxeqS0L0P01;                             ///< TXEQ Search Mask Socket 0 Link 0 Pstate0/1 (APCB)
  UINT32        CbsDfXgmiTxeqS0L0P23;                             ///< TXEQ Search Mask Socket 0 Link 0 Pstate2/3 (APCB)
  UINT32        CbsDfXgmiTxeqS0L0P0;                              ///< TXEQ Search Mask Socket 0 Link 0 Pstate0
  UINT32        CbsDfXgmiTxeqS0L0P1;                              ///< TXEQ Search Mask Socket 0 Link 0 Pstate1
  UINT32        CbsDfXgmiTxeqS0L0P2;                              ///< TXEQ Search Mask Socket 0 Link 0 Pstate2
  UINT32        CbsDfXgmiTxeqS0L0P3;                              ///< TXEQ Search Mask Socket 0 Link 0 Pstate3
  UINT32        CbsDfXgmiTxeqS0L1P01;                             ///< TXEQ Search Mask Socket 0 Link 1 Pstate0/1 (APCB)
  UINT32        CbsDfXgmiTxeqS0L1P23;                             ///< TXEQ Search Mask Socket 0 Link 1 Pstate2/3 (APCB)
  UINT32        CbsDfXgmiTxeqS0L1P0;                              ///< TXEQ Search Mask Socket 0 Link 1 Pstate0
  UINT32        CbsDfXgmiTxeqS0L1P1;                              ///< TXEQ Search Mask Socket 0 Link 1 Pstate1
  UINT32        CbsDfXgmiTxeqS0L1P2;                              ///< TXEQ Search Mask Socket 0 Link 1 Pstate2
  UINT32        CbsDfXgmiTxeqS0L1P3;                              ///< TXEQ Search Mask Socket 0 Link 1 Pstate3
  UINT32        CbsDfXgmiTxeqS0L2P01;                             ///< TXEQ Search Mask Socket 0 Link 2 Pstate0/1 (APCB)
  UINT32        CbsDfXgmiTxeqS0L2P23;                             ///< TXEQ Search Mask Socket 0 Link 2 Pstate2/3 (APCB)
  UINT32        CbsDfXgmiTxeqS0L2P0;                              ///< TXEQ Search Mask Socket 0 Link 2 Pstate0
  UINT32        CbsDfXgmiTxeqS0L2P1;                              ///< TXEQ Search Mask Socket 0 Link 2 Pstate1
  UINT32        CbsDfXgmiTxeqS0L2P2;                              ///< TXEQ Search Mask Socket 0 Link 2 Pstate2
  UINT32        CbsDfXgmiTxeqS0L2P3;                              ///< TXEQ Search Mask Socket 0 Link 2 Pstate3
  UINT32        CbsDfXgmiTxeqS0L3P01;                             ///< TXEQ Search Mask Socket 0 Link 3 Pstate0/1 (APCB)
  UINT32        CbsDfXgmiTxeqS0L3P23;                             ///< TXEQ Search Mask Socket 0 Link 3 Pstate2/3 (APCB)
  UINT32        CbsDfXgmiTxeqS0L3P0;                              ///< TXEQ Search Mask Socket 0 Link 3 Pstate0
  UINT32        CbsDfXgmiTxeqS0L3P1;                              ///< TXEQ Search Mask Socket 0 Link 3 Pstate1
  UINT32        CbsDfXgmiTxeqS0L3P2;                              ///< TXEQ Search Mask Socket 0 Link 3 Pstate2
  UINT32        CbsDfXgmiTxeqS0L3P3;                              ///< TXEQ Search Mask Socket 0 Link 3 Pstate3
  UINT32        CbsDfXgmiTxeqS1L0P01;                             ///< TXEQ Search Mask Socket 1 Link 0 Pstate0/1 (APCB)
  UINT32        CbsDfXgmiTxeqS1L0P23;                             ///< TXEQ Search Mask Socket 1 Link 0 Pstate2/3 (APCB)
  UINT32        CbsDfXgmiTxeqS1L0P0;                              ///< TXEQ Search Mask Socket 1 Link 0 Pstate0
  UINT32        CbsDfXgmiTxeqS1L0P1;                              ///< TXEQ Search Mask Socket 1 Link 0 Pstate1
  UINT32        CbsDfXgmiTxeqS1L0P2;                              ///< TXEQ Search Mask Socket 1 Link 0 Pstate2
  UINT32        CbsDfXgmiTxeqS1L0P3;                              ///< TXEQ Search Mask Socket 1 Link 0 Pstate3
  UINT32        CbsDfXgmiTxeqS1L1P01;                             ///< TXEQ Search Mask Socket 1 Link 1 Pstate0/1 (APCB)
  UINT32        CbsDfXgmiTxeqS1L1P23;                             ///< TXEQ Search Mask Socket 1 Link 1 Pstate2/3 (APCB)
  UINT32        CbsDfXgmiTxeqS1L1P0;                              ///< TXEQ Search Mask Socket 1 Link 1 Pstate0
  UINT32        CbsDfXgmiTxeqS1L1P1;                              ///< TXEQ Search Mask Socket 1 Link 1 Pstate1
  UINT32        CbsDfXgmiTxeqS1L1P2;                              ///< TXEQ Search Mask Socket 1 Link 1 Pstate2
  UINT32        CbsDfXgmiTxeqS1L1P3;                              ///< TXEQ Search Mask Socket 1 Link 1 Pstate3
  UINT32        CbsDfXgmiTxeqS1L2P01;                             ///< TXEQ Search Mask Socket 1 Link 2 Pstate0/1 (APCB)
  UINT32        CbsDfXgmiTxeqS1L2P23;                             ///< TXEQ Search Mask Socket 1 Link 2 Pstate2/3 (APCB)
  UINT32        CbsDfXgmiTxeqS1L2P0;                              ///< TXEQ Search Mask Socket 1 Link 2 Pstate0
  UINT32        CbsDfXgmiTxeqS1L2P1;                              ///< TXEQ Search Mask Socket 1 Link 2 Pstate1
  UINT32        CbsDfXgmiTxeqS1L2P2;                              ///< TXEQ Search Mask Socket 1 Link 2 Pstate2
  UINT32        CbsDfXgmiTxeqS1L2P3;                              ///< TXEQ Search Mask Socket 1 Link 2 Pstate3
  UINT32        CbsDfXgmiTxeqS1L3P01;                             ///< TXEQ Search Mask Socket 1 Link 3 Pstate0/1 (APCB)
  UINT32        CbsDfXgmiTxeqS1L3P23;                             ///< TXEQ Search Mask Socket 1 Link 3 Pstate2/3 (APCB)
  UINT32        CbsDfXgmiTxeqS1L3P0;                              ///< TXEQ Search Mask Socket 1 Link 3 Pstate0
  UINT32        CbsDfXgmiTxeqS1L3P1;                              ///< TXEQ Search Mask Socket 1 Link 3 Pstate1
  UINT32        CbsDfXgmiTxeqS1L3P2;                              ///< TXEQ Search Mask Socket 1 Link 3 Pstate2
  UINT32        CbsDfXgmiTxeqS1L3P3;                              ///< TXEQ Search Mask Socket 1 Link 3 Pstate3
  UINT8         CbsDfXgmiAcDcCoupledLinkControl;                  ///< xGMI AC/DC Coupled Link Control
  UINT8         CbsDfXgmiAcDcCoupledLink;                         ///< xGMI AC/DC Coupled Link (APCB)
  UINT8         CbsDfXgmiAcDcCoupledLinkSocket0Link0;             ///< xGMI AC/DC Coupled Link Socket 0 Link 0
  UINT8         CbsDfXgmiAcDcCoupledLinkSocket0Link1;             ///< xGMI AC/DC Coupled Link Socket 0 Link 1
  UINT8         CbsDfXgmiAcDcCoupledLinkSocket0Link2;             ///< xGMI AC/DC Coupled Link Socket 0 Link 2
  UINT8         CbsDfXgmiAcDcCoupledLinkSocket0Link3;             ///< xGMI AC/DC Coupled Link Socket 0 Link 3
  UINT8         CbsDfXgmiAcDcCoupledLinkSocket1Link0;             ///< xGMI AC/DC Coupled Link Socket 1 Link 0
  UINT8         CbsDfXgmiAcDcCoupledLinkSocket1Link1;             ///< xGMI AC/DC Coupled Link Socket 1 Link 1
  UINT8         CbsDfXgmiAcDcCoupledLinkSocket1Link2;             ///< xGMI AC/DC Coupled Link Socket 1 Link 2
  UINT8         CbsDfXgmiAcDcCoupledLinkSocket1Link3;             ///< xGMI AC/DC Coupled Link Socket 1 Link 3
  UINT8         CbsDfXgmiChannelTypeControl;                      ///< xGMI Channel Type Control
  UINT32        CbsDfXgmiChannelType;                             ///< xGMI Channel Type (APCB)
  UINT32        CbsDfXgmiChannelTypeSocket0Link0;                 ///< xGMI Channel Type Socket 0 Link 0
  UINT32        CbsDfXgmiChannelTypeSocket0Link1;                 ///< xGMI Channel Type Socket 0 Link 1
  UINT32        CbsDfXgmiChannelTypeSocket0Link2;                 ///< xGMI Channel Type Socket 0 Link 2
  UINT32        CbsDfXgmiChannelTypeSocket0Link3;                 ///< xGMI Channel Type Socket 0 Link 3
  UINT32        CbsDfXgmiChannelTypeSocket1Link0;                 ///< xGMI Channel Type Socket 1 Link 0
  UINT32        CbsDfXgmiChannelTypeSocket1Link1;                 ///< xGMI Channel Type Socket 1 Link 1
  UINT32        CbsDfXgmiChannelTypeSocket1Link2;                 ///< xGMI Channel Type Socket 1 Link 2
  UINT32        CbsDfXgmiChannelTypeSocket1Link3;                 ///< xGMI Channel Type Socket 1 Link 3
  UINT8         CbsDfCdma;                                        ///< SDCI
  UINT8         CbsDfDbgDisRmtSteer;                              ///< DisRmtSteer
  UINT8         CbsDfCmnPfOrganization;                           ///< Organization
  UINT8         CbsCmnDfPdrTuning;                                ///< Periodic Directory Rinse (PDR) Tuning
  UINT8         CbsDfCmnMemIntlvPageSize;                         ///< Tracking Granularity
  UINT8         CbsDfCmnPfPdrMode;                                ///< PDR Mode
  UINT8         CbsCmnMemCsInterleaveDdr;                         ///< Chipselect Interleaving
  UINT8         CbsCmnMemAddressHashBankDdr;                      ///< Address Hash Bank
  UINT8         CbsCmnMemAddressHashCsDdr;                        ///< Address Hash CS
  UINT8         CbsCmnMemAddressHashRmDdr;                        ///< Address Hash Rm
  UINT8         CbsCmnMemAddressHashSubchannelDdr;                ///< Address Hash Subchannel
  UINT8         CbsCmnMemCtrllerBankSwapModeDdr;                  ///< BankSwapMode
  UINT8         CbsCmnMemContextRestoreDdr;                       ///< Memory Context Restore
  UINT8         CbsDramSurvivesWarmReset;                         ///< DRAM Survives Warm Reset
  UINT8         CbsCmnMemCtrllerPwrDnEnDdr;                       ///< Power Down Enable
  UINT8         CbsCmnMemSubUrgRefLowerBound;                     ///< Sub Urgent Refresh Lower Bound
  UINT8         CbsCmnMemUrgRefLimit;                             ///< Urgent Refresh Limit
  UINT8         CbsCmnMemDramRefreshRate;                         ///< DRAM Refresh Rate
  UINT8         CbsCmnMemSelfRefreshExitStaggering;               ///< Self-Refresh Exit Staggering
  UINT8         CbsCmnMemt2xRefreshTemperatureThreshold;          ///< DRAM 2x Refresh Temperature Threshold
  UINT8         CbsCmnMemChannelDisableFloatPowerGoodDdr;         ///< Memory Channel Disable Float Power Good
  UINT32        CbsCmnMemChannelDisableBitmaskDdr;                ///< Memory Channel Disable Bitmask
  UINT8         CbsCmnMemSocket0Channel0Ddr;                      ///< Socket 0 Channel 0
  UINT8         CbsCmnMemSocket0Channel1Ddr;                      ///< Socket 0 Channel 1
  UINT8         CbsCmnMemSocket0Channel2Ddr;                      ///< Socket 0 Channel 2
  UINT8         CbsCmnMemSocket0Channel3Ddr;                      ///< Socket 0 Channel 3
  UINT8         CbsCmnMemSocket0Channel4Ddr;                      ///< Socket 0 Channel 4
  UINT8         CbsCmnMemSocket0Channel5Ddr;                      ///< Socket 0 Channel 5
  UINT8         CbsCmnMemSocket0Channel6Ddr;                      ///< Socket 0 Channel 6
  UINT8         CbsCmnMemSocket0Channel7Ddr;                      ///< Socket 0 Channel 7
  UINT8         CbsCmnMemSocket0Channel8Ddr;                      ///< Socket 0 Channel 8
  UINT8         CbsCmnMemSocket0Channel9Ddr;                      ///< Socket 0 Channel 9
  UINT8         CbsCmnMemSocket0Channel10Ddr;                     ///< Socket 0 Channel 10
  UINT8         CbsCmnMemSocket0Channel11Ddr;                     ///< Socket 0 Channel 11
  UINT8         CbsCmnMemSocket1Channel0Ddr;                      ///< Socket 1 Channel 0
  UINT8         CbsCmnMemSocket1Channel1Ddr;                      ///< Socket 1 Channel 1
  UINT8         CbsCmnMemSocket1Channel2Ddr;                      ///< Socket 1 Channel 2
  UINT8         CbsCmnMemSocket1Channel3Ddr;                      ///< Socket 1 Channel 3
  UINT8         CbsCmnMemSocket1Channel4Ddr;                      ///< Socket 1 Channel 4
  UINT8         CbsCmnMemSocket1Channel5Ddr;                      ///< Socket 1 Channel 5
  UINT8         CbsCmnMemSocket1Channel6Ddr;                      ///< Socket 1 Channel 6
  UINT8         CbsCmnMemSocket1Channel7Ddr;                      ///< Socket 1 Channel 7
  UINT8         CbsCmnMemSocket1Channel8Ddr;                      ///< Socket 1 Channel 8
  UINT8         CbsCmnMemSocket1Channel9Ddr;                      ///< Socket 1 Channel 9
  UINT8         CbsCmnMemSocket1Channel10Ddr;                     ///< Socket 1 Channel 10
  UINT8         CbsCmnMemSocket1Channel11Ddr;                     ///< Socket 1 Channel 11
  UINT8         CbsCmnMemRefManagementDdr;                        ///< Refresh Management
  UINT8         CbsCmnMemArfmDdr;                                 ///< Adaptive Refresh Management
  UINT8         CbsCmnMemRAAIMTDdr;                               ///< RAA Initial Management Threshold
  UINT8         CbsCmnMemRAAMMTDdr;                               ///< RAA Maximum Management Threshold
  UINT8         CbsCmnMemRAARefDecMultiplierDdr;                  ///< RAA Refresh Decrement Multiplier
  UINT8         CbsCmnMemDrfmDdr;                                 ///< DRFM Enable
  UINT8         CbsCmnMemDrfmBrcDdr;                              ///< Bounded Refresh Configuration
  UINT8         CbsCmnMemDrfmHashDdr;                             ///< DRFM Hash Enable
  UINT8         CbsCmnMemMbistEnDdr;                              ///< MBIST Enable
  UINT8         CbsCmnMemMbistTestmodeDdr;                        ///< MBIST Test Mode
  UINT8         CbsCmnMemMbistAggressorsDdr;                      ///< MBIST Aggressors
  UINT8         CbsCmnMemHealingBistEnableBitMaskDdr;             ///< DDR Healing BIST
  UINT8         CbsCmnMemHealingBistExecutionMode;                ///< DDR Healing BIST Execution Mode
  UINT8         CbsCmnMemHealingBistRepairTypeDdr;                ///< DDR Healing BIST Repair Type
  UINT8         CbsCmnMemPmuBistAlgorithmSelect;                  ///< PMU Mem BIST Algorithm Select
  UINT16        CbsCmnMemPmuBistAlgorithmBitMaskDdr;              ///< PMU Mem BIST Algorithm Bitmask
  UINT8         CbsCmnMemPmuBistAlgorithm1;                       ///< Algorithm #1
  UINT8         CbsCmnMemPmuBistAlgorithm2;                       ///< Algorithm #2
  UINT8         CbsCmnMemPmuBistAlgorithm3;                       ///< Algorithm #3
  UINT8         CbsCmnMemPmuBistAlgorithm4;                       ///< Algorithm #4
  UINT8         CbsCmnMemPmuBistAlgorithm5;                       ///< Algorithm #5
  UINT8         CbsCmnMemPmuBistAlgorithm6;                       ///< Algorithm #6
  UINT8         CbsCmnMemPmuBistAlgorithm7;                       ///< Algorithm #7
  UINT8         CbsCmnMemPmuBistAlgorithm8;                       ///< Algorithm #8
  UINT8         CbsCmnMemPmuBistAlgorithm9;                       ///< Algorithm #9
  UINT8         CbsCmnMemMbistPatternSelect;                      ///< Pattern Select
  UINT8         CbsCmnMemMbistPatternLength;                      ///< Pattern Length
  UINT8         CbsCmnMemMbistAggressorsChnl;                     ///< Aggressor Channel
  UINT8         CbsCmnMemMbistAggrStaticLaneCtrl;                 ///< Aggressor Static Lane Control
  UINT32        CbsCmnMemMbistAggrStaticLaneSelU32;               ///< Aggressor Static Lane Select Upper 32 bits
  UINT32        CbsCmnMemMbistAggrStaticLaneSelL32;               ///< Aggressor Static Lane Select Lower 32 Bits
  UINT8         CbsCmnMemMbistAggrStaticLaneSelEcc;               ///< Aggressor Static Lane Select ECC
  UINT8         CbsCmnMemMbistAggrStaticLaneVal;                  ///< Aggressor Static Lane Value
  UINT8         CbsCmnMemMbistTgtStaticLaneCtrl;                  ///< Target Static Lane Control
  UINT32        CbsCmnMemMbistTgtStaticLaneSelU32;                ///< Target Static Lane Select Upper 32 bit
  UINT32        CbsCmnMemMbistTgtStaticLaneSelL32;                ///< Target Static Lane Select Lower 32 Bits
  UINT8         CbsCmnMemMbistTgtStaticLaneSelEcc;                ///< Target Static Lane Select ECC
  UINT8         CbsCmnMemMbistTgtStaticLaneVal;                   ///< Target Static Lane Value
  UINT8         CbsCmnMemMbistReadDataEyeVoltageStep;             ///< Read Voltage Sweep Step Size
  UINT8         CbsCmnMemMbistReadDataEyeTimingStep;              ///< Read Timing Sweep Step Size
  UINT8         CbsCmnMemMbistWriteDataEyeVoltageStep;            ///< Write Voltage Sweep Step Size
  UINT8         CbsCmnMemMbistWriteDataEyeTimingStep;             ///< Write Timing Sweep Step Size
  UINT8         CbsCmnMemMbistDataeyeSilentExecution;             ///< Silent Execution
  UINT8         CbsCmnMemDataPoisoningDdr;                        ///< Data Poisoning
  UINT8         CbsCmnMemBootTimePostPackageRepair;               ///< DRAM Boot Time Post Package Repair
  UINT8         CbsCmnMemRuntimePostPackageRepair;                ///< DRAM Runtime Post Package Repair
  UINT8         CbsCmnMemPostPackageRepairConfigInitiator;        ///< DRAM Post Package Repair Config Initiator
  UINT8         CbsCmnMemRcdParityDdr;                            ///< RCD Parity
  UINT8         CbsCmnMemMaxRcdParityErrorReplayDdr;              ///< Max RCD Parity Error Replay
  UINT8         CbsCmnMemWriteCrcDdr;                             ///< Write CRC
  UINT8         CbsCmnMemMaxWriteCrcErrorReplayDdr;               ///< Max Write CRC Error Replay
  UINT8         CbsCmnMemReadCrcDdr;                              ///< Read CRC
  UINT8         CbsCmnMemMaxReadCrcErrorReplayDdr;                ///< Max Read CRC Error Replay
  UINT8         CbsCmnMemDisMemErrInj;                            ///< Memory Error Injection
  UINT8         CbsCmnMemEcsStatusInterruptDdr;                   ///< EcsStatus Interrupt
  UINT8         CbsCmnMemCorrectedErrorCounterEnable;             ///< DRAM Corrected Error Counter Enable
  UINT8         CbsCmnMemCorrectedErrorCounterInterruptEnable;    ///< DRAM Corrected Error Counter Interrupt Enable
  UINT8         CbsCmnMemCorrectedErrorCounterLeakRate;           ///< DRAM Corrected Error Counter Leak Rate
  UINT16        CbsCmnMemCorrectedErrorCounterStartCount;         ///< DRAM Corrected Error Counter Start Count
  UINT8         CbsCmnMemDramEccSymbolSizeDdr;                    ///< DRAM ECC Symbol Size
  UINT8         CbsCmnMemDramEccEnDdr;                            ///< DRAM ECC Enable
  UINT8         CbsCmnMemDramUeccRetryDdr;                        ///< DRAM UECC Retry
  UINT8         CbsCmnMemMaxDramUeccErrorReplayDdr;               ///< Max DRAM UECC Error Replay
  UINT8         CbsCmnMemDramMemClrDdr;                           ///< Memory Clear
  UINT8         CbsCmnMemAddrXorAfterEcc;                         ///< Address XOR after ECC
  UINT8         CbsDbgMemCipherTextHiding;                        ///< CipherText Hiding Enable
  UINT8         CbsCmnMemDramEcsModeDdr;                          ///< DRAM ECS Mode
  UINT8         CbsCmnMemDramRedirectScrubEnDdr;                  ///< DRAM Redirect Scrubber Enable
  UINT8         CbsCmnMemDramRedirectScrubLimitDdr;               ///< DRAM Scrub Redirection Limit
  UINT8         CbsCmnMemDramScrubTime;                           ///< DRAM Scrub Time
  UINT8         CbsCmnMemtECSintCtrlDdr;                          ///< tECSint Ctrl
  UINT16        CbsCmnMemtECSintDdr;                              ///< tECSint
  UINT8         CbsCmnMemDramEtcDdr;                              ///< DRAM Error Threshold Count
  UINT8         CbsCmnMemDramEcsCountModeDdr;                     ///< DRAM ECS Count Mode
  UINT8         CbsCmnMemDramAutoEcsSelfRefreshDdr;               ///< DRAM AutoEcs during Self Refresh
  UINT8         CbsCmnMemDramEcsWritebackSuppressionDdr;          ///< DRAM ECS WriteBack Suppression
  UINT8         CbsCmnMemDramX4WritebackSuppressionDdr;           ///< DRAM X4 WriteBack Suppression
  UINT8         CbsCmnMemOdtImpedProcDdr;                         ///< Processor ODT Pull Up Impedance
  UINT8         CbsCmnMemOdtPullDownImpedProcDdr;                 ///< Processor ODT Pull Down Impedance
  UINT8         CbsCmnMemDramDrvStrenDqDdr;                       ///< Dram DQ drive strengths
  UINT8         CbsCmnMemRttNomWrP0Ddr;                           ///< RTT_NOM_WR P-State 0
  UINT8         CbsCmnMemRttNomRdP0Ddr;                           ///< RTT_NOM_RD P-State 0
  UINT8         CbsCmnMemRttWrP0Ddr;                              ///< RTT_WR P-State 0
  UINT8         CbsCmnMemRttParkP0Ddr;                            ///< RTT_PARK P-State 0
  UINT8         CbsCmnMemRttParkDqsP0Ddr;                         ///< DQS_RTT_PARK P-State 0
  UINT8         CbsCmnMemRttNomWrP1Ddr;                           ///< RTT_NOM_WR P-State 1
  UINT8         CbsCmnMemRttNomRdP1Ddr;                           ///< RTT_NOM_RD P-State 1
  UINT8         CbsCmnMemRttWrP1Ddr;                              ///< RTT_WR P-State 1
  UINT8         CbsCmnMemRttParkP1Ddr;                            ///< RTT_PARK P-State 1
  UINT8         CbsCmnMemRttParkDqsP1Ddr;                         ///< DQS_RTT_PARK P-State 1
  UINT8         CbsCmnMemTimingLegalDisclaimer;                   ///< DRAM Timing Configuration Legal Disclaimer
  UINT8         CbsCmnMemTimingLegalDisclaimer1;                  ///< DRAM Timing Configuration Legal Disclaimer 1
  UINT8         CbsCmnMemTimingSettingDdr;                        ///< Active Memory Timing Settings
  UINT16        CbsCmnMemTargetSpeedDdr;                          ///< Memory Target Speed
  UINT8         CbsCmnMemTimingTclCtrlDdr;                        ///< Tcl Ctrl
  UINT16        CbsCmnMemTimingTclDdr;                            ///< Tcl
  UINT8         CbsCmnMemTimingTrcdCtrlDdr;                       ///< Trcd Ctrl
  UINT16        CbsCmnMemTimingTrcdDdr;                           ///< Trcd
  UINT8         CbsCmnMemTimingTrpCtrlDdr;                        ///< Trp Ctrl
  UINT16        CbsCmnMemTimingTrpDdr;                            ///< Trp
  UINT8         CbsCmnMemTimingTrasCtrlDdr;                       ///< Tras Ctrl
  UINT16        CbsCmnMemTimingTrasDdr;                           ///< Tras
  UINT8         CbsCmnMemTimingTrcCtrlDdr;                        ///< Trc Ctrl
  UINT16        CbsCmnMemTimingTrcDdr;                            ///< Trc
  UINT8         CbsCmnMemTimingTwrCtrlDdr;                        ///< Twr Ctrl
  UINT16        CbsCmnMemTimingTwrDdr;                            ///< Twr
  UINT8         CbsCmnMemTimingTrfc1CtrlDdr;                      ///< Trfc1 Ctrl
  UINT16        CbsCmnMemTimingTrfc1Ddr;                          ///< Trfc1
  UINT8         CbsCmnMemTimingTrfc2CtrlDdr;                      ///< Trfc2 Ctrl
  UINT16        CbsCmnMemTimingTrfc2Ddr;                          ///< Trfc2
  UINT8         CbsCmnMemTimingTrfcSbCtrlDdr;                     ///< TrfcSb Ctrl
  UINT16        CbsCmnMemTimingTrfcSbDdr;                         ///< TrfcSb
  UINT8         CbsCmnMemTimingTcwlCtrlDdr;                       ///< Tcwl Ctrl
  UINT16        CbsCmnMemTimingTcwlDdr;                           ///< Tcwl
  UINT8         CbsCmnMemTimingTrtpCtrlDdr;                       ///< Trtp Ctrl
  UINT16        CbsCmnMemTimingTrtpDdr;                           ///< Trtp
  UINT8         CbsCmnMemTimingTrrdLCtrlDdr;                      ///< TrrdL Ctrl
  UINT16        CbsCmnMemTimingTrrdLDdr;                          ///< TrrdL
  UINT8         CbsCmnMemTimingTrrdSCtrlDdr;                      ///< TrrdS Ctrl
  UINT16        CbsCmnMemTimingTrrdSDdr;                          ///< TrrdS
  UINT8         CbsCmnMemTimingTfawCtrlDdr;                       ///< Tfaw Ctrl
  UINT16        CbsCmnMemTimingTfawDdr;                           ///< Tfaw
  UINT8         CbsCmnMemTimingTwtrLCtrlDdr;                      ///< TwtrL Ctrl
  UINT16        CbsCmnMemTimingTwtrLDdr;                          ///< TwtrL
  UINT8         CbsCmnMemTimingTwtrSCtrlDdr;                      ///< TwtrS Ctrl
  UINT16        CbsCmnMemTimingTwtrSDdr;                          ///< TwtrS
  UINT8         CbsCmnMemTimingTrdrdScLCtrlDdr;                   ///< TrdrdScL Ctrl
  UINT16        CbsCmnMemTimingTrdrdScLDdr;                       ///< TrdrdScL
  UINT8         CbsCmnMemTimingTrdrdScCtrlDdr;                    ///< TrdrdSc Ctrl
  UINT16        CbsCmnMemTimingTrdrdScDdr;                        ///< TrdrdSc
  UINT8         CbsCmnMemTimingTrdrdSdCtrlDdr;                    ///< TrdrdSd Ctrl
  UINT16        CbsCmnMemTimingTrdrdSdDdr;                        ///< TrdrdSd
  UINT8         CbsCmnMemTimingTrdrdDdCtrlDdr;                    ///< TrdrdDd Ctrl
  UINT16        CbsCmnMemTimingTrdrdDdDdr;                        ///< TrdrdDd
  UINT8         CbsCmnMemTimingTwrwrScLCtrlDdr;                   ///< TwrwrScL Ctrl
  UINT16        CbsCmnMemTimingTwrwrScLDdr;                       ///< TwrwrScL
  UINT8         CbsCmnMemTimingTwrwrScCtrlDdr;                    ///< TwrwrSc Ctrl
  UINT16        CbsCmnMemTimingTwrwrScDdr;                        ///< TwrwrSc
  UINT8         CbsCmnMemTimingTwrwrSdCtrlDdr;                    ///< TwrwrSd Ctrl
  UINT16        CbsCmnMemTimingTwrwrSdDdr;                        ///< TwrwrSd
  UINT8         CbsCmnMemTimingTwrwrDdCtrlDdr;                    ///< TwrwrDd Ctrl
  UINT16        CbsCmnMemTimingTwrwrDdDdr;                        ///< TwrwrDd
  UINT8         CbsCmnMemTimingTwrrdCtrlDdr;                      ///< Twrrd Ctrl
  UINT16        CbsCmnMemTimingTwrrdDdr;                          ///< Twrrd
  UINT8         CbsCmnMemTimingTrdwrCtrlDdr;                      ///< Trdwr Ctrl
  UINT16        CbsCmnMemTimingTrdwrDdr;                          ///< Trdwr
  UINT8         CbsCmnMemDramPdaEnumIdProgModeDdr;                ///< DRAM PDA Enumerate ID Programming Mode
  UINT8         CbsCmnMemWriteTrainingBurstLength;                ///< Write Training Burst Length
  UINT8         CbsCmnTrainingRetryCount;                         ///< Training Retry Count
  UINT8         CbsCmnMemPeriodicTrainingModeDdr;                 ///< Periodic Training Mode
  UINT8         CbsCmnMemPeriodicIntervalMode;                    ///< Periodic Interval Mode
  UINT16        CbsCmnMemPeriodicInterval;                        ///< Periodic Interval
  UINT8         CbsCmnMemTsmeEnableDdr;                           ///< TSME
  UINT8         CbsCmnMemAes;                                     ///< AES
  UINT8         CbsCmnMemDataScramble;                            ///< Data Scramble
  UINT8         CbsCmnMemSmeMkEnable;                             ///< SME-MK
  UINT8         CbsCmnPmicErrorReporting;                         ///< PMIC Error Reporting
  UINT8         CbsCmnMemCtrllerPmicOpMode;                       ///< PMIC Operation Mode
  UINT8         CbsCmnMemCtrllerPmicFaultRecovery;                ///< PMIC Fault Recovery
  UINT16        CbsCmnMemCtrllerPmicSwaSwbVddCore;                ///< PMIC SWA/SWB VDD Core
  UINT16        CbsCmnMemCtrllerPmicSwcVddio;                     ///< PMIC SWC VDDIO
  UINT16        CbsCmnMemCtrllerPmicSwdVpp;                       ///< PMIC SWD VPP
  UINT8         CbsCmnMemCtrllerPmicStaggerDelay;                 ///< PMIC Stagger Delay
  UINT8         CbsCmnMemCtrllerMaxPmicPowerOn;                   ///< Max PMIC Power On
  UINT8         CbsCmnMemOdtsCmdThrottleCycleCtlDdr;              ///< ODTS Thermal Throttle Control
  UINT8         CbsCmnMemOdtsCmdThrottleThresholdDdr;             ///< ODTS Thermal Throttle Threshold
  UINT8         CbsCmnTsodThermalThrottleControlDdr;              ///< TSOD Thermal Throttle Control
  UINT8         CbsCmnTsodThermalThrottleStartTempDdr;            ///< TSOD Thermal Throttle Start Temperature
  UINT8         CbsCmnTsodThermalThrottleHysteresisDdr;           ///< TSOD Thermal Throttle Hysteresis
  UINT8         CbsCmnTsodCmdThrottlePercentage0Ddr;              ///< TSOD Command Throttle Percentage (Threshold)
  UINT8         CbsCmnTsodCmdThrottlePercentage5Ddr;              ///< TSOD Command Throttle Percentage (Threshold+5C)
  UINT8         CbsCmnTsodCmdThrottlePercentage10Ddr;             ///< TSOD Command Throttle Percentage (Threshold+10C)
  UINT8         CbsCmnGnbPcieLoopBackMode;                        ///< PCIe loopback Mode
  UINT8         CbsEnable2SpcGen4;                                ///< Enable 2 SPC (Gen 4)
  UINT8         CbsEnable2SpcGen5;                                ///< Enable 2 SPC (Gen 5)
  UINT8         CbsGnbSafeRecoveryUponABERExceededError;          ///< Safe recovery upon a BERExceeded Error
  UINT8         CbsGnbPeriodicCalibration;                        ///< Periodic Calibration
  UINT8         CbsCmnTDPCtl;                                     ///< TDP Control
  UINT32        CbsCmnTDPLimit;                                   ///< TDP
  UINT8         CbsCmnPPTCtl;                                     ///< PPT Control
  UINT32        CbsCmnPPTLimit;                                   ///< PPT
  UINT8         CbsCmnDeterminismCtl;                             ///< Determinism Control
  UINT8         CbsCmnDeterminismEnable;                          ///< Determinism Enable
  UINT8         CbsCmnxGmiLinkWidthCtl;                           ///< xGMI Link Width Control
  UINT8         CbsCmnxGmiForceLinkWidthCtl;                      ///< xGMI Force Link Width Control
  UINT8         CbsCmnxGmiForceLinkWidth;                         ///< xGMI Force Link Width
  UINT8         CbsCmnxGmiMaxLinkWidthCtl;                        ///< xGMI Max Link Width Range Control
  UINT8         CbsCmnxGmiMaxLinkWidth;                           ///< xGMI Max Link Width
  UINT8         CbsCmnxGmiMinLinkWidth;                           ///< xGMI Min Link Width
  UINT8         CbsCmnApbdis;                                     ///< APBDIS
  UINT8         CbsCmnApbdisDfPstate;                             ///< DfPstate
  UINT8         CbsCmnEfficiencyModeEn;                           ///< Power Profile Selection
  UINT8         CbsCmnXgmiPstateControl;                          ///< xGMI Pstate Control
  UINT8         CbsCmnXgmiPstateSelection;                        ///< xGMI Pstate Selection
  UINT8         CbsCmnBoostFmaxEn;                                ///< BoostFmaxEn
  UINT32        CbsCmnBoostFmax;                                  ///< BoostFmax
  UINT8         CbsCmnGnbSMUDffo;                                 ///< DF PState Frequency Optimizer
  UINT8         CbsCmnGnbSmuDfCstates;                            ///< DF Cstates
  UINT8         CbsCmnGnbSmuCppc;                                 ///< CPPC
  UINT8         CbsCmnGnbSMUHsmpSupport;                          ///< HSMP Support
  UINT8         CbsCmnSvi3SvcSpeedCtl;                            ///< SVI3 SVC Speed Control
  UINT8         CbsCmnSvi3SvcSpeed;                               ///< SVI3 SVC Speed
  UINT8         CbsCmnX3dStackOverride;                           ///< 3D V-Cache
  UINT8         CbsCmnL3Bist;                                     ///< L3 BIST
  UINT8         CbsCmnGnbDiagMode;                                ///< Diagnostic Mode
  UINT8         CbsCmnGnbSmuGmiFolding;                           ///< GMI Folding
  UINT8         CbsCmnThrottlerMode;                              ///< Separate CPU power plane throttling
  UINT8         CbsCmnDFPstateRangeCtl;                           ///< DfPstate Range Control
  UINT8         CbsCmnDfPstateMax;                                ///< DfPstate Max Index
  UINT8         CbsCmnDfPstateMin;                                ///< DfPstate Min Index
  UINT8         CbsCmnRASControl;                                 ///< NBIO RAS Control
  UINT8         CbsCmnNBIOSyncFloodGen;                           ///< NBIO SyncFlood Generation
  UINT8         PcdSyncFloodToApml;                               ///< NBIO SyncFlood Reporting
  UINT8         CmnGnbAmdPcieAerReportMechanism;                  ///< PCIe Aer Reporting Mechanism
  UINT8         EdpcControl;                                      ///< Edpc Control
  UINT16        AcsRasValue;                                      ///< ACS RAS Request Value
  UINT8         CbsCmnPoisonConsumption;                          ///< NBIO Poison Consumption
  UINT8         CbsCmnGnbRasSyncfloodPcieFatalError;              ///< Sync Flood on PCIe Fatal Error
  UINT8         CbsCmnRASNumericalCommonOptions;                  ///< NBIO RAS Numerical Common Options
  UINT32        PcdEgressPoisonSeverityHi;                        ///< Egress Poison Severity High
  UINT32        PcdEgressPoisonSeverityLo;                        ///< Egress Poison Severity Low
  UINT32        PcdAmdNbioEgressPoisonMaskHi;                     ///< Egress Poison Mask High
  UINT32        PcdAmdNbioEgressPoisonMaskLo;                     ///< Egress Poison Mask Low
  UINT32        PcdAmdNbioRASUcpMaskHi;                           ///< Uncorrected Converted to Poison Enable Mask High
  UINT32        PcdAmdNbioRASUcpMaskLo;                           ///< Uncorrected Converted to Poison Enable Mask Low
  UINT32        PcdSyshubWdtTimerInterval;                        ///< System Hub Watchdog Timer
  UINT8         CbsCmnGnbDataObjectExchange;                      ///< Data Object Exchange
  UINT8         CbsCmnGnbRtmMarginingSupport;                     ///< RTM Margining Support
  UINT8         CbsCmnNbioForceSpeedLastAdvertised;               ///< Multi Auto Speed Change On Last Rate
  UINT8         CbsCmnLcMultUpstreamAuto;                         ///< Multi Upstream Auto Speed Change
  UINT16        STRAP_COMPLIANCE_DIS;                             ///< Allow Compliance
  UINT8         CbsCmnNbioPcieAdvertiseEqToHighRateSupport;       ///< EQ Bypass To Highest Rate
  UINT8         CbsCmnGnbDataLinkFeatureCap;                      ///< Data Link Feature Cap
  UINT8         CbsCmnGnbDataLinkFeatureExchange;                 ///< Data Link Feature Exchange
  UINT8         CbsCmnGnbSris;                                    ///< SRIS
  UINT8         CbsDbgGnbDbgACSEnable;                            ///< ACS Enable
  UINT8         CbsGnbCmnPcieTbtSupport;                          ///< PCIe Ten Bit Tag Support
  UINT8         CbsGnbCmnPcieAriEnumeration;                      ///< PCIe ARI Enumeration
  UINT8         CmnGnbPcieAriSupport;                             ///< PCIe ARI Support
  UINT8         CbsPresenceDetectSelectmode;                      ///< Presence Detect Select mode
  UINT8         CbsHotPlugHandlingMode;                           ///< Hot Plug Handling mode
  UINT8         CbsHotPlugPDSettle;                               ///< Presence Detect State Settle Time
  UINT8         CbsHotPlugSettleTime;                             ///< Hot Plug Port Settle Time
  UINT8         CbsHotplugSupport;                                ///< Hotplug Support
  UINT8         CbsCmnEarlyLinkSpeed;                             ///< Early Link Speed
  UINT8         CbsDbgGnbDbgAERCAPEnable;                         ///< Enable AER Cap
  UINT8         CbsCmnPcieCAPLinkSpeed;                           ///< PCIE Link Speed Capability
  UINT8         CbsCmnPcieTargetLinkSpeed;                        ///< PCIE Target Link Speed
  UINT8         CbsCmnAllPortsASPM;                               ///< ASPM Control
  UINT8         CbsCmnNbioMctpEn;                                 ///< MCTP Enable
  UINT8         CbsCmnNbioMctpMode;                               ///< MCTP Mode
  UINT8         CbsCmnNbioMctpDiscoveryNotifyMessage;             ///< MCTP discovery notify message
  UINT8         CbsCmnNbioPcieNonPcieCompliantSupport;            ///< Non-PCIe Compliant Support
  UINT8         CbsCmnLimitHpDevicesToPcieBootSpeed;              ///< Limit hotplug devices to PCIe boot speed
  UINT8         CbsCmnPCIeSFIConfigviaOOBEn;                      ///< Enable PCIe SFI Config via OOB
  UINT8         CbsCmnNbioPcieIdlePowerSetting;                   ///< PCIE Idle Power Setting
  UINT8         CbsCfgAcsEnRccDev0;                               ///< ACS Rcc_Dev0
  UINT8         CbsCfgAerEnRccDev0;                               ///< AER Rcc_Dev0
  UINT8         CbsCfgDlfEnStrap1;                                ///< DlfEnableStrap1
  UINT8         CbsCfgPhy16gtStrap1;                              ///< Phy16GTStrap1
  UINT8         CbsCfgMarginEnStrap1;                             ///< MarginEnStrap1
  UINT8         CbsCfgAcsSourceValStrap5;                         ///< SourceValStrap5
  UINT8         CbsCfgAcsTranslationalBlockingStrap5;             ///< TranslationalBlockingStrap5
  UINT8         CbsCfgAcsP2pReq;                                  ///< P2pReq ACS Control
  UINT8         CbsCfgAcsP2pCompStrap5;                           ///< P2pCompStrap5
  UINT8         CbsCfgAcsUpstreamFwdStrap5;                       ///< UpstreamFwdStrap5
  UINT8         CbsCfgAcsP2PEgressStrap5;                         ///< P2PEgressStrap5
  UINT8         CbsCfgAcsDirectTranslatedStrap5;                  ///< DirectTranslatedStrap5
  UINT8         CbsCfgAcsSsidEnStrap5;                            ///< SsidEnStrap5
  UINT8         CbsCfgPriEnPageReq;                               ///< PriEnPageReq
  UINT8         CbsCfgPriResetPageReq;                            ///< PriResetPageReq
  UINT8         CbsCfgAcsSourceVal;                               ///< SourceVal ACS cntl
  UINT8         CbsCfgAcsTranslationalBlocking;                   ///< TranslationalBlocking ACS Control
  UINT8         CbsCfgAcsP2pComp;                                 ///< P2pComp ACS Control
  UINT8         CbsCfgAcsUpstreamFwd;                             ///< UpstreamFwd ACS Control
  UINT8         CbsCfgAcsP2PEgress;                               ///< P2PEgress ACS Control
  UINT8         CbsCfgAcsP2pReqStrap5;                            ///< P2pReqStrap5
  UINT8         CbsCfgE2EPrefix;                                  ///< E2E_PREFIX
  UINT8         CbsCfgExtendedFmtSupported;                       ///< EXTENDED_FMT
  UINT8         CbsCmnNbioAtomicRoutingStrap5;                    ///< AtomicRoutingStrap5
  UINT8         CbsSevSnpSupport;                                 ///< SEV-SNP Support
  UINT8         CbsSevTioSupport;                                 ///< SEV-TIO Support
  UINT8         CbsCmnDrtmMemoryReservation;                      ///< DRTM Memory Reservation
  UINT8         CbsCmnDrtmSupport;                                ///< DRTM Virtual Device Support
  UINT8         CbsCmnDmaProtection;                              ///< DMA Protection
  UINT8         CbsCmnGnbNbIOMMU;                                 ///< IOMMU
  UINT8         CbsCmnDmarSupport;                                ///< DMAr Support
  UINT8         CbsCmnEnablePortBifurcation;                      ///< Enable Port Bifurcation
  UINT8         CbsCmnS0P0Override;                               ///< Socket 0 P0 Override
  UINT8         CbsCmnS0P1Override;                               ///< Socket 0 P1 Override
  UINT8         CbsCmnS0P2Override;                               ///< Socket 0 P2 Override
  UINT8         CbsCmnS0P3Override;                               ///< Socket 0 P3 Override
  UINT8         CbsCmnS1P0Override;                               ///< Socket 1 P0 Override
  UINT8         CbsCmnS1P1Override;                               ///< Socket 1 P1 Override
  UINT8         CbsCmnS1P2Override;                               ///< Socket 1 P2 Override
  UINT8         CbsCmnS1P3Override;                               ///< Socket 1 P3 Override
  UINT8         CbsCmnP0Override;                                 ///< P0 Override
  UINT8         CbsCmnP1Override;                                 ///< P1 Override
  UINT8         CbsCmnP2Override;                                 ///< P2 Override
  UINT8         CbsCmnP3Override;                                 ///< P3 Override
  UINT8         CbsCmnG0Override;                                 ///< G0 Override
  UINT8         CbsCmnG1Override;                                 ///< G1 Override
  UINT8         CbsCmnG2Override;                                 ///< G2 Override
  UINT8         CbsCmnG3Override;                                 ///< G3 Override
  UINT8         CbsCmnNbioPcieSearchMaskConfigGen3;               ///< Preset Search Mask Configuration (Gen3)
  UINT16        CbsCmnNbioPcieSearchMaskGen3;                     ///< Preset Search Mask (Gen3)
  UINT8         CbsCmnNbioPcieSearchMaskConfigGen4;               ///< Preset Search Mask Configuration (Gen4)
  UINT16        CbsCmnNbioPcieSearchMaskGen4;                     ///< Preset Search Mask (Gen4)
  UINT8         CbsCmnNbioPcieSearchMaskConfigGen5;               ///< Preset Search Mask Configuration (Gen5)
  UINT16        CbsCmnNbioPcieSearchMaskGen5;                     ///< Preset Search Mask (Gen5)
  UINT8         CbsCmnFchI3C0Config;                              ///< I3C/I2C 0 Enable
  UINT8         CbsCmnFchI3C1Config;                              ///< I3C/I2C 1 Enable
  UINT8         CbsCmnFchI3C2Config;                              ///< I3C/I2C 2 Enable
  UINT8         CbsCmnFchI3C3Config;                              ///< I3C/I2C 3 Enable
  UINT8         CbsCmnFchI2C4Config;                              ///< I2C 4 Enable
  UINT8         CbsCmnFchI2C5Config;                              ///< I2C 5 Enable
  UINT8         CbsCmnFchReleaseSpdHostControl;                   ///< Release SPD Host Control
  UINT8         CbsCmnFchPMFWDdr5Telemetry;                       ///< PMFW Poll DDR5 Telemetry
  UINT8         CbsCmnFchIxcTelemetryPortsFence;                  ///< Ixc Telemetry Ports Fence Control
  UINT8         CbsCmnFchI2cSdaHoldOverride;                      ///< I2C SDA Hold Override
  UINT8         CbsCmnFchApmlSbtsiSlvMode;                        ///< APML SB-TSI Mode
  UINT8         CbsCmnFchI3cModeSpeed;                            ///< I3C Mode Speed
  UINT8         CbsCmnFchI3cPpHcntValue;                          ///< I3C Push Pull HCNT Value
  UINT8         CbsCmnFchI3cSdaHoldValue;                         ///< I3C SDA Hold Value
  UINT8         CbsCmnFchI3cSdaHoldOverride;                      ///< I3C SDA Hold Override
  UINT16        CbsCmnFchI2c0SdaTxHoldValue;                      ///< I2C 0 SDA TX HOLD VALUE
  UINT16        CbsCmnFchI2c1SdaTxHoldValue;                      ///< I2C 1 SDA TX HOLD VALUE
  UINT16        CbsCmnFchI2c2SdaTxHoldValue;                      ///< I2C 2 SDA TX HOLD VALUE
  UINT16        CbsCmnFchI2c3SdaTxHoldValue;                      ///< I2C 3 SDA TX HOLD VALUE
  UINT16        CbsCmnFchI2c4SdaTxHoldValue;                      ///< I2C 4 SDA TX HOLD VALUE
  UINT16        CbsCmnFchI2c5SdaTxHoldValue;                      ///< I2C 5 SDA TX HOLD VALUE
  UINT8         CbsCmnFchI2c0SdaRxHoldValue;                      ///< I2C 0 SDA RX HOLD VALUE
  UINT8         CbsCmnFchI2c1SdaRxHoldValue;                      ///< I2C 1 SDA RX HOLD VALUE
  UINT8         CbsCmnFchI2c2SdaRxHoldValue;                      ///< I2C 2 SDA RX HOLD VALUE
  UINT8         CbsCmnFchI2c3SdaRxHoldValue;                      ///< I2C 3 SDA RX HOLD VALUE
  UINT8         CbsCmnFchI2c4SdaRxHoldValue;                      ///< I2C 4 SDA RX HOLD VALUE
  UINT8         CbsCmnFchI2c5SdaRxHoldValue;                      ///< I2C 5 SDA RX HOLD VALUE
  UINT8         CbsCmnFchI3c0SdaHoldValue;                        ///< I3C 0 SDA HOLD VALUE
  UINT8         CbsCmnFchI3c1SdaHoldValue;                        ///< I3C 1 SDA HOLD VALUE
  UINT8         CbsCmnFchI3c2SdaHoldValue;                        ///< I3C 2 SDA HOLD VALUE
  UINT8         CbsCmnFchI3c3SdaHoldValue;                        ///< I3C 3 SDA HOLD VALUE
  UINT8         CbsCmnFchSataEnable;                              ///< SATA Enable
  UINT8         CbsCmnFchSataClass;                               ///< SATA Mode
  UINT8         CbsCmnFchSataRasSupport;                          ///< SATA RAS Support
  UINT8         CbsCmnFchSataStaggeredSpinup;                     ///< SATA Staggered Spin-up
  UINT8         CbsCmnFchSataAhciDisPrefetchFunction;             ///< SATA Disabled AHCI Prefetch Function
  UINT8         CbsDbgFchSata0Enable;                             ///< Sata0 Enable
  UINT8         CbsDbgFchSata1Enable;                             ///< Sata1 Enable
  UINT8         CbsDbgFchSata2Enable;                             ///< Sata2 Enable
  UINT8         CbsDbgFchSata3Enable;                             ///< Sata3 Enable
  UINT8         CbsDbgFchSata4Enable;                             ///< Sata4 (Socket1) Enable
  UINT8         CbsDbgFchSata5Enable;                             ///< Sata5 (Socket1) Enable
  UINT8         CbsDbgFchSata6Enable;                             ///< Sata6 (Socket1) Enable
  UINT8         CbsDbgFchSata7Enable;                             ///< Sata7 (Socket1) Enable
  UINT8         CbsDbgFchSataeSATAPort0;                          ///< Sata0 eSATA Port0
  UINT8         CbsDbgFchSataeSATAPort1;                          ///< Sata0 eSATA Port1
  UINT8         CbsDbgFchSataeSATAPort2;                          ///< Sata0 eSATA Port2
  UINT8         CbsDbgFchSataeSATAPort3;                          ///< Sata0 eSATA Port3
  UINT8         CbsDbgFchSataeSATAPort4;                          ///< Sata0 eSATA Port4
  UINT8         CbsDbgFchSataeSATAPort5;                          ///< Sata0 eSATA Port5
  UINT8         CbsDbgFchSataeSATAPort6;                          ///< Sata0 eSATA Port6
  UINT8         CbsDbgFchSataeSATAPort7;                          ///< Sata0 eSATA Port7
  UINT8         CbsDbgFchSataMcmDie1EsataPort0;                   ///< Sata1 eSATA Port0
  UINT8         CbsDbgFchSataMcmDie1EsataPort1;                   ///< Sata1 eSATA Port1
  UINT8         CbsDbgFchSataMcmDie1EsataPort2;                   ///< Sata1 eSATA Port2
  UINT8         CbsDbgFchSataMcmDie1EsataPort3;                   ///< Sata1 eSATA Port3
  UINT8         CbsDbgFchSataMcmDie1EsataPort4;                   ///< Sata1 eSATA Port4
  UINT8         CbsDbgFchSataMcmDie1EsataPort5;                   ///< Sata1 eSATA Port5
  UINT8         CbsDbgFchSataMcmDie1EsataPort6;                   ///< Sata1 eSATA Port6
  UINT8         CbsDbgFchSataMcmDie1EsataPort7;                   ///< Sata1 eSATA Port7
  UINT8         CbsDbgFchSataMcmDie2EsataPort0;                   ///< Sata2 eSATA Port0
  UINT8         CbsDbgFchSataMcmDie2EsataPort1;                   ///< Sata2 eSATA Port1
  UINT8         CbsDbgFchSataMcmDie2EsataPort2;                   ///< Sata2 eSATA Port2
  UINT8         CbsDbgFchSataMcmDie2EsataPort3;                   ///< Sata2 eSATA Port3
  UINT8         CbsDbgFchSataMcmDie2EsataPort4;                   ///< Sata2 eSATA Port4
  UINT8         CbsDbgFchSataMcmDie2EsataPort5;                   ///< Sata2 eSATA Port5
  UINT8         CbsDbgFchSataMcmDie2EsataPort6;                   ///< Sata2 eSATA Port6
  UINT8         CbsDbgFchSataMcmDie2EsataPort7;                   ///< Sata2 eSATA Port7
  UINT8         CbsDbgFchSataMcmDie3EsataPort0;                   ///< Sata3 eSATA Port0
  UINT8         CbsDbgFchSataMcmDie3EsataPort1;                   ///< Sata3 eSATA Port1
  UINT8         CbsDbgFchSataMcmDie3EsataPort2;                   ///< Sata3 eSATA Port2
  UINT8         CbsDbgFchSataMcmDie3EsataPort3;                   ///< Sata3 eSATA Port3
  UINT8         CbsDbgFchSataMcmDie3EsataPort4;                   ///< Sata3 eSATA Port4
  UINT8         CbsDbgFchSataMcmDie3EsataPort5;                   ///< Sata3 eSATA Port5
  UINT8         CbsDbgFchSataMcmDie3EsataPort6;                   ///< Sata3 eSATA Port6
  UINT8         CbsDbgFchSataMcmDie3EsataPort7;                   ///< Sata3 eSATA Port7
  UINT8         CbsDbgFchSataMcmDie4EsataPort0;                   ///< Sata4 eSATA Port0
  UINT8         CbsDbgFchSataMcmDie4EsataPort1;                   ///< Sata4 eSATA Port1
  UINT8         CbsDbgFchSataMcmDie4EsataPort2;                   ///< Sata4 eSATA Port2
  UINT8         CbsDbgFchSataMcmDie4EsataPort3;                   ///< Sata4 eSATA Port3
  UINT8         CbsDbgFchSataMcmDie4EsataPort4;                   ///< Sata4 eSATA Port4
  UINT8         CbsDbgFchSataMcmDie4EsataPort5;                   ///< Sata4 eSATA Port5
  UINT8         CbsDbgFchSataMcmDie4EsataPort6;                   ///< Sata4 eSATA Port6
  UINT8         CbsDbgFchSataMcmDie4EsataPort7;                   ///< Sata4 eSATA Port7
  UINT8         CbsDbgFchSataMcmDie5EsataPort0;                   ///< Sata5 eSATA Port0
  UINT8         CbsDbgFchSataMcmDie5EsataPort1;                   ///< Sata5 eSATA Port1
  UINT8         CbsDbgFchSataMcmDie5EsataPort2;                   ///< Sata5 eSATA Port2
  UINT8         CbsDbgFchSataMcmDie5EsataPort3;                   ///< Sata5 eSATA Port3
  UINT8         CbsDbgFchSataMcmDie5EsataPort4;                   ///< Sata5 eSATA Port4
  UINT8         CbsDbgFchSataMcmDie5EsataPort5;                   ///< Sata5 eSATA Port5
  UINT8         CbsDbgFchSataMcmDie5EsataPort6;                   ///< Sata5 eSATA Port6
  UINT8         CbsDbgFchSataMcmDie5EsataPort7;                   ///< Sata5 eSATA Port7
  UINT8         CbsDbgFchSataMcmDie6EsataPort0;                   ///< Sata6 eSATA Port0
  UINT8         CbsDbgFchSataMcmDie6EsataPort1;                   ///< Sata6 eSATA Port1
  UINT8         CbsDbgFchSataMcmDie6EsataPort2;                   ///< Sata6 eSATA Port2
  UINT8         CbsDbgFchSataMcmDie6EsataPort3;                   ///< Sata6 eSATA Port3
  UINT8         CbsDbgFchSataMcmDie6EsataPort4;                   ///< Sata6 eSATA Port4
  UINT8         CbsDbgFchSataMcmDie6EsataPort5;                   ///< Sata6 eSATA Port5
  UINT8         CbsDbgFchSataMcmDie6EsataPort6;                   ///< Sata6 eSATA Port6
  UINT8         CbsDbgFchSataMcmDie6EsataPort7;                   ///< Sata6 eSATA Port7
  UINT8         CbsDbgFchSataMcmDie7EsataPort0;                   ///< Sata7 eSATA Port0
  UINT8         CbsDbgFchSataMcmDie7EsataPort1;                   ///< Sata7 eSATA Port1
  UINT8         CbsDbgFchSataMcmDie7EsataPort2;                   ///< Sata7 eSATA Port2
  UINT8         CbsDbgFchSataMcmDie7EsataPort3;                   ///< Sata7 eSATA Port3
  UINT8         CbsDbgFchSataMcmDie7EsataPort4;                   ///< Sata7 eSATA Port4
  UINT8         CbsDbgFchSataMcmDie7EsataPort5;                   ///< Sata7 eSATA Port5
  UINT8         CbsDbgFchSataMcmDie7EsataPort6;                   ///< Sata7 eSATA Port6
  UINT8         CbsDbgFchSataMcmDie7EsataPort7;                   ///< Sata7 eSATA Port7
  UINT8         CbsDbgFchSataAggresiveDevSlpP0;                   ///< Socket0 DevSlp0 Enable
  UINT8         CbsDbgFchSataDevSlpController0Num;                ///< Socket0 DevSlp0 Controller Number
  UINT8         CbsDbgFchSataDevSlpPort0Num;                      ///< Socket0 DevSlp0 Port Number
  UINT8         CbsDbgFchSataAggresiveDevSlpP1;                   ///< Socket0 DevSlp1 Enable
  UINT8         CbsDbgFchSataDevSlpController1Num;                ///< Socket0 DevSlp1 Controller Number
  UINT8         CbsDbgFchSataDevSlpPort1Num;                      ///< Socket0 DevSlp1 Port Number
  UINT8         CbsDbgFchSataMcmDie4DevSlp0;                      ///< Socket1 DevSlp0 Enable
  UINT8         CbsDbgFchSataMcmDie4DevSlpController0Num;         ///< Socket1 DevSlp0 Controller Number
  UINT8         CbsDbgFchSataMcmDie4DevSlp0Num;                   ///< Socket1 DevSlp0 Port Number
  UINT8         CbsDbgFchSataMcmDie4DevSlp1;                      ///< Socket1 DevSlp1 Enable
  UINT8         CbsDbgFchSataMcmDie4DevSlpController1Num;         ///< Socket1 DevSlp1 Controller Number
  UINT8         CbsDbgFchSataMcmDie4DevSlp1Num;                   ///< Socket1 DevSlp1 Port Number
  UINT8         CbsDbgFchSataSgpio0;                              ///< Sata0 SGPIO
  UINT8         CbsDbgFchSataMcmDie1Sgpio0;                       ///< Sata1 SGPIO
  UINT8         CbsDbgFchSataMcmDie2Sgpio0;                       ///< Sata2 SGPIO
  UINT8         CbsDbgFchSataMcmDie3Sgpio0;                       ///< Sata3 SGPIO
  UINT8         CbsDbgFchSataMcmDie4Sgpio0;                       ///< Sata4 SGPIO
  UINT8         CbsDbgFchSataMcmDie5Sgpio0;                       ///< Sata5 SGPIO
  UINT8         CbsDbgFchSataMcmDie6Sgpio0;                       ///< Sata6 SGPIO
  UINT8         CbsDbgFchSataMcmDie7Sgpio0;                       ///< Sata7 SGPIO
  UINT8         CbsCmnFchUsbXHCI0Enable;                          ///< XHCI Controller0 enable
  UINT8         CbsCmnFchUsbXHCI1Enable;                          ///< XHCI Controller1 enable
  UINT8         CbsCmnFchUsbXHCI2Enable;                          ///< XHCI2 enable (Socket1)
  UINT8         CbsCmnFchUsbXHCI3Enable;                          ///< XHCI3 enable (Socket1)
  UINT8         CbsCmnFchSystemPwrFailShadow;                     ///< Ac Loss Control
  UINT8         CbsCmnFchPwrFailShadowABLEnabled;                 ///< Set Fch Power failed Shadow in ABL
  UINT8         CbsCmnFchUart0Config;                             ///< Uart 0 Enable
  UINT8         CbsCmnFchUart0LegacyConfig;                       ///< Uart 0 Legacy Options
  UINT8         CbsCmnFchUart1Config;                             ///< Uart 1 Enable
  UINT8         CbsCmnFchUart1LegacyConfig;                       ///< Uart 1 Legacy Options
  UINT8         CbsCmnFchUart2Config;                             ///< Uart 2 Enable
  UINT8         CbsCmnFchUart2LegacyConfig;                       ///< Uart 2 Legacy Options
  UINT8         CbsCmnFchAlinkRasSupport;                         ///< ALink RAS Support
  UINT8         CbsDbgFchSyncfloodEnable;                         ///< Reset After Sync-Flood
  UINT8         CbsDbgFchDelaySyncflood;                          ///< Delay Reset After Sync-Flood
  UINT8         CbsDbgFchSystemSpreadSpectrum;                    ///< FCH Spread Spectrum
  UINT8         CbsCmnBootTimerEnable;                            ///< Boot Timer Enable
  UINT8         CbsCmnSP3NtbP0P0;                                 ///< Socket-0 P0 NTB Enable
  UINT8         CbsCmnSP3NtbStartLaneP0P0;                        ///< Socket-0 P0 Start Lane
  UINT8         CbsCmnSP3NtbEndLaneP0P0;                          ///< Socket-0 P0 End Lane
  UINT8         CbsCmnSP3NtbLinkSpeedP0P0;                        ///< Socket-0 P0 Link Speed
  UINT8         CbsCmnSP3NtbModeP0P0;                             ///< Socket-0 P0 NTB Mode
  UINT8         CbsCmnSP3NtbP0P2;                                 ///< Socket-0 P2 NTB Enable
  UINT8         CbsCmnSP3NtbStartLaneP0P2;                        ///< Socket-0 P2 Start Lane
  UINT8         CbsCmnSP3NtbEndLaneP0P2;                          ///< Socket-0 P2 End Lane
  UINT8         CbsCmnSP3NtbLinkSpeedP0P2;                        ///< Socket-0 P2 Link Speed
  UINT8         CbsCmnSP3NtbModeP0P2;                             ///< Socket-0 P2 NTB Mode
  UINT8         CbsCmnSocAblConOut;                               ///< ABL Console Out Control
  UINT8         CbsCmnSocAblConOutSerialPort;                     ///< ABL Console Out Serial Port
  UINT8         CbsCmnSocAblConOutSerialPortIO;                   ///< ABL Console Out Serial Port IO
  UINT8         CbsCmnSocAblSerialPortIOCustomEnabled;            ///< ABL Serial port IO customized enabled
  UINT16        CbsCmnSocAblConOutSerialPortIOCustom;             ///< ABL Console out Serial Port IO Customized
  UINT8         CbsCmnSocAblConOutBasic;                          ///< ABL Basic Console Out Control
  UINT8         CbsCmnSocAblPmuMsgCtrl;                           ///< ABL PMU message Control
  UINT8         CbsCmnSocAblMemPopMsgCtrl;                        ///< ABL Memory Population message Control
  UINT8         CbsCmnPrintSocket1PmuMsgBlock;                    ///< Print Socket 1 PMU MsgBlock
  UINT8         CbsCmnPrintSocket1TrainingLog;                    ///< Print Socket 1 PMU Training Log
  UINT8         CbsDfCmnPspErrInj;                                ///< PSP error injection support
  UINT8         CbsNumberOfSockets;                               ///< Number of Sockets
  UINT8         CbsCmnSecI2cVoltMode;                             ///< SEC_I2C Voltage Mode
  UINT8         CbsCmnSocFarEnforced;                             ///< FAR enforcement state
  UINT32        CbsCmnSocSplFuse;                                 ///< SPL value in the CPU fuse
  UINT32        CbsCmnSocSplValueInTbl;                           ///< SPL value in the SPL table
  UINT8         CbsCmnSocFarSwitch;                               ///< FAR Switch
  UINT8         CbsCmnCxlControl;                                 ///< CXL Control
  UINT8         CbsCmnCxlSdpReqSysAddr;                           ///< CXL Physical Addressing
  UINT8         CbsCmnCxlSpm;                                     ///< CXL Memory Attribute
  UINT8         CbsCmnCxlEncryption;                              ///< CXL Encryption
  UINT8         CbsCmnCxlDvsecLock;                               ///< CXL DVSEC Lock
  UINT8         CbsCmnCxlHdmDecoderLockOnCommit;                  ///< CXL HDM Decoder Lock On Commit
  UINT8         CbsCmnCxlTempGen5Advertisement;                   ///< Temp Gen5 Advertisement
  UINT8         CbsCmnSyncHeaderByPass;                           ///< Sync Header Bypass
  UINT8         CbsCxlSyncHeaderBypassCompMode;                   ///< Sync Header Bypass Compatibility Mode
  UINT8         CbsCmnCxlMemOnlineOffline;                        ///< CXL Memory Online/Offline
  UINT8         CbsDbgCxlOverideCxlMemorySize;                    ///< Override CXL Memory Size
  UINT8         CbsCmnCxlProtocolErrorReporting;                  ///< CXL Protocol Error Reporting
  UINT8         CbsCmnCxlComponentErrorReporting;                 ///< CXL Component Error Reporting
  UINT8         CbsCmnCxlMemIsolationEnable;                      ///< CXL Root Port Isolation
  UINT8         CbsCmnCxlMemIsolationFwNotification;              ///< CXL Root Port Isolation FW Notification

  UINT8         Reserved[1024];                                   ///< Reserved for option growth
} CBS_CONFIG;


///CbsVariableStructUniqueValue 0x42a2239a
///ApcbVariableHash 0x3cb618cc 

#pragma pack(pop)


#endif // _AMD_CBS_VARIABLE_H_
