TOKEN
    Name  = "PSP_BLOCK_CRB_SUPPORT"
    Value  = "1"
    Help  = "CRB PSP block layout. This token should be disabled if <PERSON><PERSON> has his own PSP layout"
    TokenType = Boolean
    TargetEQU = Yes
    TargetMAK = Yes
    TargetH = Yes
End

TOKEN
    Name  = "TOTAL_PSP_BLOCK_SUPPORT"
    Value  = "4"
    Help  = "The total block support."
    TokenType = Integer
    TargetEQU = Yes
    TargetMAK = Yes
    TargetH = Yes
    Token = "PSP_BLOCK_CRB_SUPPORT" "=" "1"
End

#*******************************************************************#
#                            Blocks Size                            #
#*******************************************************************#
TOKEN
    Name  = "BLOCK_0_SIZE"
    Value  = "$(PSPDIR_L1_SIZE_BRH)"
    Help  = "Size of block."
    TokenType = Integer
    TargetEQU = Yes
    TargetMAK = Yes
    TargetH = Yes
    Token = "TOTAL_PSP_BLOCK_SUPPORT" ">" "0"
    Token = "PSP_BLOCK_CRB_SUPPORT" "=" "1"
End

TOKEN
    Name  = "BLOCK_1_SIZE"
    Value  = "$(BIOSDIR_L1_SIZE_BRH)"
    Help  = "Size of block."
    TokenType = Integer
    TargetEQU = Yes
    TargetMAK = Yes
    TargetH = Yes
    Token = "TOTAL_PSP_BLOCK_SUPPORT" ">" "1"
    Token = "PSP_BLOCK_CRB_SUPPORT" "=" "1"
End

TOKEN
    Name  = "BLOCK_2_SIZE"
    Value  = "$(PSPDIR_L2_SIZE_BRH)"
    Help  = "Size of block."
    TokenType = Integer
    TargetEQU = Yes
    TargetMAK = Yes
    TargetH = Yes
    Token = "TOTAL_PSP_BLOCK_SUPPORT" ">" "2"
    Token = "PSP_BLOCK_CRB_SUPPORT" "=" "1"
End

TOKEN
    Name  = "BLOCK_3_SIZE"
    Value  = "$(BIOSDIR_L2_SIZE_BRH)"
    Help  = "Size of block."
    TokenType = Integer
    TargetEQU = Yes
    TargetMAK = Yes
    TargetH = Yes
    Token = "TOTAL_PSP_BLOCK_SUPPORT" ">" "3"
    Token = "PSP_BLOCK_CRB_SUPPORT" "=" "1"
End

#*******************************************************************#
#                          Blocks Layout                            #
#*******************************************************************#
TOKEN
    Name   = "BLOCK_0_OFFSET"
    Value  = "$(PSP_BLOCK_START_OFFSET)"
    Help  = "Psp block 0 offset"
    TokenType = Integer
    TargetEQU = Yes
    TargetMAK = Yes
    TargetH = Yes
    Token = "TOTAL_PSP_BLOCK_SUPPORT" ">" "0"
    Token = "PSP_BLOCK_CRB_SUPPORT" "=" "1"
End

TOKEN
    Name   = "BLOCK_0_ADDRESS"
    Value  = "$(BLOCK_0_OFFSET) + $(FLASH_BASE)"
    Help  = "Psp block 0 address"
    TokenType = Integer
    TargetEQU = Yes
    TargetMAK = Yes
    TargetH = Yes
    Token = "TOTAL_PSP_BLOCK_SUPPORT" ">" "0"
    Token = "PSP_BLOCK_CRB_SUPPORT" "=" "1"
End

TOKEN
    Name   = "BLOCK_1_OFFSET"
    Value  = "$(BLOCK_0_OFFSET) + $(BLOCK_0_SIZE)"
    Help  = "Psp block 1 offset"
    TokenType = Integer
    TargetEQU = Yes
    TargetMAK = Yes
    TargetH = Yes
    Token = "TOTAL_PSP_BLOCK_SUPPORT" ">" "1"
    Token = "PSP_BLOCK_CRB_SUPPORT" "=" "1"
End

TOKEN
    Name   = "BLOCK_1_ADDRESS"
    Value  = "$(BLOCK_1_OFFSET) + $(FLASH_BASE)"
    Help  = "Psp block 1 physical address"
    TokenType = Integer
    TargetEQU = Yes
    TargetMAK = Yes
    TargetH = Yes
    Token = "TOTAL_PSP_BLOCK_SUPPORT" ">" "1"
    Token = "PSP_BLOCK_CRB_SUPPORT" "=" "1"
End

TOKEN
    Name   = "BLOCK_2_OFFSET"
    Value  = "$(BLOCK_1_OFFSET) + $(BLOCK_1_SIZE)"
    Help  = "Psp block 2 offset"
    TokenType = Integer
    TargetEQU = Yes
    TargetMAK = Yes
    TargetH = Yes
    Token = "TOTAL_PSP_BLOCK_SUPPORT" ">" "2"
    Token = "PSP_BLOCK_CRB_SUPPORT" "=" "1"
End

TOKEN
    Name   = "BLOCK_2_ADDRESS"
    Value  = "$(BLOCK_2_OFFSET) + $(FLASH_BASE)"
    Help  = "Psp block 2 physical address"
    TokenType = Integer
    TargetEQU = Yes
    TargetMAK = Yes
    TargetH = Yes
    Token = "TOTAL_PSP_BLOCK_SUPPORT" ">" "2"
    Token = "PSP_BLOCK_CRB_SUPPORT" "=" "1"
End

TOKEN
    Name   = "BLOCK_3_OFFSET"
    Value  = "$(BLOCK_2_OFFSET) + $(BLOCK_2_SIZE)"
    Help  = "Psp block 3 offset"
    TokenType = Integer
    TargetEQU = Yes
    TargetMAK = Yes
    TargetH = Yes
    Token = "TOTAL_PSP_BLOCK_SUPPORT" ">" "3"
    Token = "PSP_BLOCK_CRB_SUPPORT" "=" "1"
    Token = "AmiPspEarlyVga_SUPPORT" "=" "0"
End

TOKEN
    Name   = "BLOCK_3_OFFSET"
    Value  = "$(BLOCK_2_OFFSET) + $(BLOCK_2_SIZE)"
    Help  = "Psp block 3 offset"
    TokenType = Integer
    TargetEQU = Yes
    TargetMAK = Yes
    TargetH = Yes
    Token = "TOTAL_PSP_BLOCK_SUPPORT" ">" "3"
    Token = "PSP_BLOCK_CRB_SUPPORT" "=" "1"
    Token = "AmiPspEarlyVga_SUPPORT" "=" "1"
End

TOKEN
    Name   = "BLOCK_3_ADDRESS"
    Value  = "$(BLOCK_3_OFFSET) + $(FLASH_BASE)"
    Help  = "Psp block 3 physical address"
    TokenType = Integer
    TargetEQU = Yes
    TargetMAK = Yes
    TargetH = Yes
    Token = "TOTAL_PSP_BLOCK_SUPPORT" ">" "3"
    Token = "PSP_BLOCK_CRB_SUPPORT" "=" "1"
End

