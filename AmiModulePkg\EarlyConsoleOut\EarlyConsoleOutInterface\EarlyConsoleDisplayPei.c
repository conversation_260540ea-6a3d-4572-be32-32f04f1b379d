//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2025, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************

/** @file EarlyConsoleDisplayPei.c

**/

#include <Token.h>
#include <Uefi.h>
#include <Library/PeiServicesLib.h>
#include <Library/BaseLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/PrintLib.h>
#include <EarlyConsoleDisplay.h>
#include <Library/AmiVideoTextOutLib.h>
#include <Library/PeiLogoFontLib.h>
#include <Ppi/AmiSimpleTextOutPpi.h>
#include <Ppi/AmiGraphicsOutputPpi.h>
#include <Protocol/GraphicsOutput.h>
#include <EarlyConsoleElink.h>
#include "SmLogo.h"
#include <Library/AmiProgressErrorCodeLib.h>
#include <Library/BaseLib.h>

EFI_STATUS
EFIAPI
UpdateFrameInfoHobOnMemoryDiscoveredNotify (
    IN EFI_PEI_SERVICES     **PeiServices,
    IN EFI_PEI_NOTIFY_DESCRIPTOR  *NotifyDescriptor,
    IN VOID                       *Ppi 
);

EFI_STATUS
EFIAPI
UpdateFrameInfoHobOnSimpleTextOutNotify (
    IN EFI_PEI_SERVICES     **PeiServices,
    IN EFI_PEI_NOTIFY_DESCRIPTOR  *NotifyDescriptor,
    IN VOID                       *Ppi 
);

VOID
ProcessString (
  IN AMI_SIMPLE_TEXT_OUTPUT_PPI             *SimpleTextOutPpi,
  IN AMI_GRAPHICS_OUTPUT_PPI                *GraphicsOutPutPpi,
  IN AMI_EARLY_GRAPHICS_FRAME_INFO          *FrameInfo,
  IN CHAR16                                 *String
);

EARLY_GRAPHICS_FRAME_DEFINE gGraphicsFrameDefinitions[] = {
  AMI_EARLY_CONSOLE_GRAPHICS_FRAME_DEFINE_LIST 
  {EarlyConsoleDisplayFrameMax, 0xFF, 0xFF, 0xFF}
};

EARLY_GRAPHICS_FRAME_DEFINE gTextFrameDefinitions[] = {
  AMI_EARLY_CONSOLE_TEXT_FRAME_DEFINE_LIST 
  {EarlyConsoleDisplayFrameMax, 0xFF, 0xFF, 0xFF}
};

static EFI_PEI_NOTIFY_DESCRIPTOR  mPeiEarlyConsoleDisplayNotifyList[] = {
  {
    (EFI_PEI_PPI_DESCRIPTOR_NOTIFY_DISPATCH),
    &gEfiPeiMemoryDiscoveredPpiGuid,
    UpdateFrameInfoHobOnMemoryDiscoveredNotify
  },
  {
    (EFI_PEI_PPI_DESCRIPTOR_NOTIFY_CALLBACK | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST),
    &gAmiSimpleTextOutPpiGuid,
    UpdateFrameInfoHobOnSimpleTextOutNotify
  }
};

EFI_STATUS
EFIAPI
UpdateAmiEarlyConsoleOutPpiAfterMemoryDiscovered()
{
        EFI_STATUS                              Status;
        EFI_HOB_GUID_TYPE                       *GuidHob;
        AMI_SIMPLE_TEXT_OUTPUT_PPI              *SimpleTextOutPpi;
        EARLY_CONSOLE_DISPLAY_FRAME_INFO_HOB    *FrameInfoHobDataPtr = NULL;
        UINT8                                   Instance;
        
        GuidHob = GetFirstGuidHob (&gAmiEarlyConsoleDisplayFrameInfoHobGuid);
        if (GuidHob == NULL) {
            return EFI_SUCCESS;
        }
        
        FrameInfoHobDataPtr = GET_GUID_HOB_DATA (GuidHob);
        
        for (Instance = 0; Instance < FixedPcdGet8 (AmiPcdSimpleTextOutMaxPpiSupported); Instance++) {
            
            // Locate the SimpleTextout PPI
            Status = PeiServicesLocatePpi(
                                    &gAmiSimpleTextOutPpiGuid,
                                    Instance,
                                    NULL,
                                    (VOID**)&SimpleTextOutPpi);
            if (EFI_ERROR (Status)) {
                DEBUG ((DEBUG_ERROR, "Status of LocatePpi for SimpleTextOutPpi Instance %d - %r\n", Instance, Status));
                continue;
            }
            if (FrameInfoHobDataPtr->DisplayFrameInfo[Instance].SimpleTextOutConsoleType == SimpleTextOutPpi->ConsoleType) {
                FrameInfoHobDataPtr->DisplayFrameInfo[Instance].SimpleTextOut.SimpleTextOutPpi = SimpleTextOutPpi;
            }
        }
        return EFI_SUCCESS;
}

/**
    Returns the screen frame information based on the 
    console type from HOB

    @param   ConsoleType - Serial/Text/GOP

    @retval  DISPLAY_FRAME_INFO
**/
DISPLAY_FRAME_INFO*
GetDisplayFrameInfo (
    AMI_PEI_SIMPLE_TEXT_CONSOLE_TYPE       ConsoleType
)
{
    UINT8                                   Index;
    EARLY_CONSOLE_DISPLAY_FRAME_INFO_HOB    *FrameInfoHobDataPtr;
    EFI_HOB_GUID_TYPE                       *GuidHob;
    
    GuidHob = GetFirstGuidHob (&gAmiEarlyConsoleDisplayFrameInfoHobGuid);
    if (GuidHob == NULL) {
        return NULL;
    }
    
    FrameInfoHobDataPtr = GET_GUID_HOB_DATA (GuidHob);
    
    for (Index = 0; Index < FixedPcdGet8 (AmiPcdSimpleTextOutMaxPpiSupported); Index++) {
        if (!FrameInfoHobDataPtr->DisplayFrameInfo[Index].IsDisplayFrameInfoValid) {
            continue;
        }
        
        if (FrameInfoHobDataPtr->DisplayFrameInfo[Index].SimpleTextOutConsoleType == ConsoleType) {
            return &FrameInfoHobDataPtr->DisplayFrameInfo[Index];
        }
    }
    
    return NULL;
}

/**
    Displays the frame information by getting the text strings from HOB

    @param   DISPLAY_FRAME_INFO - Pointer to displayFrameInfo

    @retval  VOID
**/
VOID
DisplayStringFromHob (
    DISPLAY_FRAME_INFO   *DisplayFrameInfo
)
{
    AMI_SIMPLE_TEXT_OUTPUT_PPI        *SimpleTextOutPpi;
    AMI_EARLY_GRAPHICS_FRAME_INFO     *FrameInfo;
    VOID                              *GuidHob;
    AMI_EARLY_CONSOLE_STRING_HOB      *StringHob;    
    CHAR16                             TempString[100];
    CHAR16                             *TempPointer = NULL;
    CHAR16                             PostCodeString[3];
    UINTN                              PostCodeValue;
    BOOLEAN                            ProgressOrErrorCode;
    CHAR16                             SearchString[2] = L"\n";
    UINTN                              Size=0;
    EFI_STATUS                         Status;
    CHAR16                             *DebugStr;
        
    if (!DisplayFrameInfo->IsDisplayFrameInfoValid) {
        return;
    }
    
    GuidHob = GetFirstGuidHob (&gAmiEarlyConsoleStringHobGuid);
    if (GuidHob == NULL) {
        return;
    }
    
    StringHob = GET_GUID_HOB_DATA (GuidHob);

    SimpleTextOutPpi = DisplayFrameInfo->SimpleTextOut.SimpleTextOutPpi;
    
    FrameInfo = &DisplayFrameInfo->FrameInfo[EarlyConsoleDisplayFrameInfo];
    if (FrameInfo->IsFrameInfoValid) {
        
        SimpleTextOutPpi->SetAttribute (SimpleTextOutPpi, EFI_TEXT_ATTR(EFI_WHITE,EFI_BLACK));
        
        if (SimpleTextOutPpi->ConsoleType == PeiSimpleTextOutConsoleTypeSerial) {
            SimpleTextOutPpi->OutputString (SimpleTextOutPpi, 
                                            StringHob->InfoMsgs);
        } else {
            // Set cursor to end of previous string in frame
            SimpleTextOutPpi->SetCursorPosition (
                                        SimpleTextOutPpi, 
                                        FrameInfo->CurrentColumn, 
                                        FrameInfo->CurrentRow);
            ProcessString (
                    SimpleTextOutPpi,
                    DisplayFrameInfo->GraphicsOutput.GraphicsOutputPpi,
                    FrameInfo, 
                    StringHob->InfoMsgs);
        }
    }
    
    FrameInfo = &DisplayFrameInfo->FrameInfo[EarlyConsoleDisplayFrameDebug];
    if (FrameInfo->IsFrameInfoValid) {
        
        DebugStr = StringHob->DebugMsgs;
       
        while (TRUE) {
           
            ZeroMem (TempString, sizeof(TempString));
            ZeroMem (PostCodeString, sizeof(PostCodeString));
       
            TempPointer = StrStr (DebugStr, SearchString);
            if (TempPointer == NULL) {
                break;
            }
            // Adding sizeof(CHAR16) to include '\n' character
            Size = ((UINTN)TempPointer - (UINTN)DebugStr + sizeof(CHAR16)); 
            if (Size > sizeof(TempString)) {
                DEBUG ((DEBUG_ERROR, "Buffer size is not enough to hold the debug process string. Skip it and go to next string\n"));
                DebugStr += (Size / sizeof(CHAR16));
                continue;
            }
           
            CopyMem (TempString, DebugStr, Size);
           
            DebugStr += (Size / sizeof(CHAR16));
       
            // The post code will be the 3rd and 4th string
            CopyMem (PostCodeString, TempString + 3, 4);
       
            PostCodeValue = StrHexToUintn(PostCodeString);
       
            Status = CheckProgressOrErrorCode (
                               (UINT8)PostCodeValue,
                               &ProgressOrErrorCode);
            if (EFI_ERROR (Status)) {
                DEBUG ((DEBUG_ERROR, "PostCode '%x' is not an progress or error code\n",PostCodeValue ));
                continue;
            }
       
            if (ProgressOrErrorCode) {
                SimpleTextOutPpi->SetAttribute (SimpleTextOutPpi, EFI_TEXT_ATTR(EFI_GREEN, EFI_BLACK));
            } else {
                SimpleTextOutPpi->SetAttribute (SimpleTextOutPpi, EFI_TEXT_ATTR(EFI_RED, EFI_BLACK));
            }
       
            SimpleTextOutPpi->SetCursorPosition (
                                     SimpleTextOutPpi, 
                                     FrameInfo->CurrentColumn, 
                                     FrameInfo->CurrentRow);
                            
            ProcessString (
                  SimpleTextOutPpi,
                  DisplayFrameInfo->GraphicsOutput.GraphicsOutputPpi,
                  FrameInfo, 
                  TempString);
        }      
    }
}

/**
    Appends the input string to the HOB data

    @param   DisplayFrameType - Frame type where the string to be appended
    @param   String  - String to be appended to HOB data                  

    @retval  VOID
**/
VOID
UpdateStringInHob (
    EARLY_CONSOLE_DISPLAY_FRAME_TYPE    DisplayFrameType,
    CHAR16                              *String
)
{
    VOID                            *GuidHob;
    AMI_EARLY_CONSOLE_STRING_HOB    *StringHob;
    UINTN                           HobStrLength;
    UINTN                           BufferLength;
    
    if ((DisplayFrameType != EarlyConsoleDisplayFrameInfo) &&
        (DisplayFrameType != EarlyConsoleDisplayFrameDebug)) {
        return;
    }

    GuidHob = GetFirstGuidHob (&gAmiEarlyConsoleStringHobGuid);
    if (GuidHob == NULL) {
        StringHob = BuildGuidHob (&gAmiEarlyConsoleStringHobGuid, sizeof (AMI_EARLY_CONSOLE_STRING_HOB));
        if (StringHob == NULL) {
            DEBUG((DEBUG_ERROR, "String HOB creation failed\n"));
            return;
        }
        
        ZeroMem (StringHob, sizeof (AMI_EARLY_CONSOLE_STRING_HOB));
    } else {
        StringHob = GET_GUID_HOB_DATA (GuidHob);
    }
    
    if (DisplayFrameType == EarlyConsoleDisplayFrameInfo) {
        HobStrLength = sizeof(StringHob->InfoMsgs) / sizeof(CHAR16);
        BufferLength = StrLen (StringHob->InfoMsgs) + StrLen (String);
        if (BufferLength > HobStrLength - 1 ) {
            DEBUG ((DEBUG_ERROR, "System Information limit has been exceeded. Increase the limit using the INFO_MESSAGE_BUFFER_SIZE token.\n"));
        }
        StrCatS (StringHob->InfoMsgs, HobStrLength, String);
    } else {
        HobStrLength = sizeof(StringHob->DebugMsgs) / sizeof(CHAR16);
        BufferLength = StrLen (StringHob->DebugMsgs) + StrLen (String);
        if (BufferLength > HobStrLength - 1 ) {
            DEBUG ((DEBUG_ERROR, "Postcode message limit has been exceeded. Increase the limit using the DEBUG_MESSAGE_BUFFER_SIZE token.\n"));
        }
        StrCatS (StringHob->DebugMsgs, HobStrLength, String);
    }
}

/**
    Validate the Frame define porting for the mentioned console type.
    
    @param ConsoleType          Console type (Text or Graphics)
    @param FrameDefinitions     Points to Frame definitions
    
    @return BOOLEAN
**/
BOOLEAN
IsFrameDefinitionValid (
  IN      AMI_PEI_SIMPLE_TEXT_CONSOLE_TYPE       ConsoleType,
  IN OUT  EARLY_GRAPHICS_FRAME_DEFINE            **FrameDefinitions
  )
{
    
    UINT8                               FrameCount;
    UINT8                               Index;
    UINT8                               Index1;
    UINT16                              TotalColumnPercentage = 0;
    UINT16                              TotalRowPercentage = 0;
    EARLY_CONSOLE_DISPLAY_FRAME_TYPE    FrameType;
    
    if ((ConsoleType <= PeiSimpleTextOutConsoleTypeUnKnown) || 
        (ConsoleType >= PeiSimpleTextOutConsoleTypeMax)) {
        return FALSE;
    }
    
    switch (ConsoleType) {
        case PeiSimpleTextOutConsoleTypeSerial:
        case PeiSimpleTextOutConsoleTypeVideoText:

            FrameCount = ARRAY_SIZE(gTextFrameDefinitions) - 1;
            
            for (Index = 0; Index < FrameCount; Index++) {
                
                TotalRowPercentage = 0;
                
                // Multiple Frames of same type is not allowed.
                FrameType = gTextFrameDefinitions[Index].FrameType;
                for (Index1 = Index + 1; Index1 < FrameCount; Index1++) {
                    if (gTextFrameDefinitions[Index1].FrameType == FrameType) {
                        DEBUG ((DEBUG_ERROR, "%a() Multiple Frames of same type is not valid "
                                "(EarlyConsoleTextFrameDefineList) Porting - FrameType : %d!!!\n",
                                __FUNCTION__, FrameType));
                        return FALSE;
                    }
                }
                   
                // Frame number should be in increasing order 0,1, 2..
                if (Index != gTextFrameDefinitions[Index].FrameNo) {
                    DEBUG ((DEBUG_ERROR, "%a() Invalid Frame No (EarlyConsoleTextFrameDefineList) Porting - FrameNo : %x!!!\n", 
                            __FUNCTION__, gTextFrameDefinitions[Index].FrameNo));
                    return FALSE;
                }
                
                // For Serial and Video Text console type, only debug and Info frames are supported.
                if ((gTextFrameDefinitions[Index].FrameType != EarlyConsoleDisplayFrameInfo) &&
                    (gTextFrameDefinitions[Index].FrameType != EarlyConsoleDisplayFrameDebug)) {
                    DEBUG ((DEBUG_ERROR, "%a() Invalid Text Frame (EarlyConsoleTextFrameDefineList) Porting - FrameType : %x!!!\n", 
                            __FUNCTION__, gTextFrameDefinitions[Index].FrameType));
                    return FALSE;
                }
                
                for (Index1 = 0; Index1 < FrameCount; Index1++) {
                    if (gTextFrameDefinitions[Index1].FrameNo == Index) {
                        TotalRowPercentage += gTextFrameDefinitions[Index1].RowPercentage;
                    }
                }
                
                
                DEBUG ((DEBUG_INFO, "%a() TotalRowPercentage : %d!!!\n", __FUNCTION__, TotalRowPercentage));
                if (TotalRowPercentage != 100) {
                    DEBUG ((DEBUG_ERROR, "%a() Invalid Text Frame (EarlyConsoleTextFrameDefineList) Porting - TotalRowPercentage : %d!!!\n", 
                            __FUNCTION__, TotalRowPercentage));
                    return FALSE;
                }
                
                TotalColumnPercentage += gTextFrameDefinitions[Index].ColumnPercentage;
            }
            
            if (TotalColumnPercentage != 100) {
                DEBUG ((DEBUG_ERROR, "%a() Invalid Text Frame (EarlyConsoleTextFrameDefineList) Porting - TotalColumnPercentage : %d!!!\n", 
                        __FUNCTION__, TotalColumnPercentage));
                return FALSE;
            }
            DEBUG ((DEBUG_INFO, "%a() TotalColumnPercentage : %d!!!\n", __FUNCTION__, TotalColumnPercentage));
            
            *FrameDefinitions = gTextFrameDefinitions;
            break;
            
        case PeiSimpleTextOutConsoleTypeVideoGop:
            
            FrameCount = ARRAY_SIZE(gGraphicsFrameDefinitions) - 1;
            for (Index = 0; Index < FrameCount; Index++) {
                
                TotalRowPercentage = 0;
                
                // Multiple Frames of same type is not allowed.
                FrameType = gGraphicsFrameDefinitions[Index].FrameType;
                for (Index1 = Index + 1; Index1 < FrameCount; Index1++) {
                    if (gGraphicsFrameDefinitions[Index1].FrameType == FrameType) {
                        DEBUG ((DEBUG_ERROR, "%a() Multiple Frames of same type is not valid "
                                "(EarlyConsoleGraphicsFrameDefineList) Porting - FrameType : %d!!!\n", 
                                __FUNCTION__, FrameType));
                        return FALSE;
                    }
                }
                
                // Frame number should be in increasing order 0,1,2..
                // In case of vertical frames, compare previous FrameNo with current FrameNo
                /*if ((Index != gGraphicsFrameDefinitions[Index].FrameNo)) {                    
                    if ((Index == 0) || (gGraphicsFrameDefinitions[Index].FrameNo != gGraphicsFrameDefinitions[Index - 1].FrameNo)) {
                        DEBUG ((DEBUG_ERROR, "%a() Invalid Frame No (EarlyConsoleGraphicsFrameDefineList) Porting - FrameNo : %x!!!\n", 
                                __FUNCTION__, gGraphicsFrameDefinitions[Index].FrameNo));
                        return FALSE;
                    }
                }*/
                
                if (gGraphicsFrameDefinitions[Index].FrameType >= EarlyConsoleDisplayFrameMax) {
                    DEBUG ((DEBUG_ERROR, "%a() Invalid Graphics Frame (EarlyConsoleGraphicsFrameDefineList) Porting - FrameType : %x!!!\n", 
                            __FUNCTION__, gGraphicsFrameDefinitions[Index].FrameType));
                    return FALSE;
                }
                
                // Check whether Debug and PostCode frames has same ColumnPercentage. If not, return error
                if (gGraphicsFrameDefinitions[Index].FrameType == EarlyConsoleDisplayFramePostCode) {
                    for (Index1 = 0; Index1 < FrameCount; Index1++) {
                        if (gGraphicsFrameDefinitions[Index1].FrameType != EarlyConsoleDisplayFrameDebug) {
                            continue;
                        }

                        if (gGraphicsFrameDefinitions[Index].ColumnPercentage != gGraphicsFrameDefinitions[Index1].ColumnPercentage) {
                            DEBUG ((DEBUG_ERROR, "%a() Invalid Graphics Frame - ColumnPercentage is not equal in Debug (%d) "
                                    "and PostCode (%d) frame!!!\n",__FUNCTION__, gGraphicsFrameDefinitions[Index1].ColumnPercentage, 
                                    gGraphicsFrameDefinitions[Index].ColumnPercentage));
                            return FALSE;
                        }
                    }
                }
                
                // Height
                // Current frame number and previous frame number are same, then skip current frame from addition
                if (Index == 0) {
                    TotalColumnPercentage =  gGraphicsFrameDefinitions[Index].ColumnPercentage;
                } else if (gGraphicsFrameDefinitions[Index].FrameNo != gGraphicsFrameDefinitions[Index-1].FrameNo) {
                    TotalColumnPercentage += gGraphicsFrameDefinitions[Index].ColumnPercentage;
                }
                DEBUG((DEBUG_INFO, "TotalColumnPercentage - %d\n", TotalColumnPercentage));
                
                // Width
                TotalRowPercentage = gGraphicsFrameDefinitions[Index].RowPercentage;
                for (Index1 = 0; Index1 < FrameCount; Index1++) {
                    if ((Index != Index1) && (gGraphicsFrameDefinitions[Index1].FrameNo == gGraphicsFrameDefinitions[Index].FrameNo)) {
                        TotalRowPercentage += gGraphicsFrameDefinitions[Index1].RowPercentage;
                    }
                }
                DEBUG((DEBUG_INFO, "TotalRowPercentage - %d\n", TotalRowPercentage));
                
                if (TotalRowPercentage != 100) {
                    DEBUG ((DEBUG_ERROR, "%a() Invalid Graphics Frame (EarlyConsoleGraphicsFrameDefineList) Porting - TotalRowPercentage : %d!!!\n", 
                            __FUNCTION__, TotalRowPercentage));
                    return FALSE;
                }
            }
            
            if (TotalColumnPercentage != 100) {
                DEBUG ((DEBUG_ERROR, "%a() Invalid Graphics Frame (EarlyConsoleGraphicsFrameDefineList) Porting - TotalColumnPercentage : %d!!!\n", 
                        __FUNCTION__, TotalColumnPercentage));
                return FALSE;
            }
            
            *FrameDefinitions = gGraphicsFrameDefinitions;
            break;
            
        default:
            return FALSE;
    }
    
    return TRUE;
}

/**
    Updated Display Frames info HOB with updated PPI pointer 

    @param  **PeiServices - pointer to the PEI services.
    @param  NotifyDescriptor - pointer to descriptor
    @param  Ppi - void pointer 
    
    @retval EFI_STATUS
*/
EFI_STATUS
EFIAPI
UpdateFrameInfoHobOnMemoryDiscoveredNotify (
    IN EFI_PEI_SERVICES     **PeiServices,
    IN EFI_PEI_NOTIFY_DESCRIPTOR  *NotifyDescriptor,
    IN VOID                       *Ppi 
)
{
    EFI_STATUS                              Status = EFI_SUCCESS;
    EARLY_CONSOLE_DISPLAY_FRAME_INFO_HOB    *FrameInfoHobDataPtr;
    AMI_SIMPLE_TEXT_OUTPUT_PPI              *SimpleTextOutPpi;
    AMI_GRAPHICS_OUTPUT_PPI                 *GraphicsOutputPpi;
    EFI_HOB_GUID_TYPE                       *GuidHob;
    UINT8                                   Instance;
    UINT8                                   GopInstance;
    
    DEBUG((DEBUG_INFO, "UpdateFrameInfoHobOnMemoryDiscoveredNotify\n"));
    
    GuidHob = GetFirstGuidHob (&gAmiEarlyConsoleDisplayFrameInfoHobGuid);
    if (GuidHob == NULL) {
        return EFI_NOT_READY;
    }
    
    FrameInfoHobDataPtr = GET_GUID_HOB_DATA (GuidHob);
    
    //Locate all simple text out PPI and respective Graphics output PPI.
    for (Instance = 0; Instance < FixedPcdGet8 (AmiPcdSimpleTextOutMaxPpiSupported); Instance++) {
            
        if (!FrameInfoHobDataPtr->DisplayFrameInfo[Instance].IsDisplayFrameInfoValid) {
            continue;
        }
        
        // Locate the SimpleTextout PPI
        Status = PeiServicesLocatePpi(
                                &gAmiSimpleTextOutPpiGuid,
                                Instance,
                                NULL,
                                (VOID**)&SimpleTextOutPpi);
        DEBUG((DEBUG_INFO, "gAmiSimpleTextOutPpiGuid [%d] - %r\n", Instance, Status));
        if (EFI_ERROR(Status)) { 
            continue;
        }
            
        if (SimpleTextOutPpi->ConsoleType == PeiSimpleTextOutConsoleTypeVideoGop) {
                
            //If console type is Graphics and should have respective GOP PPI installed,
            //if not return EFI_NOT_FOUND;
            GopInstance = 0;
            do {
                    
                // Locate the Graphics Output PPI
                Status = PeiServicesLocatePpi(
                                        &gAmiGraphicsOutPutPpiGuid,
                                        GopInstance,
                                        NULL,
                                        (VOID**)&GraphicsOutputPpi);
                DEBUG((DEBUG_INFO, "gAmiGraphicsOutPutPpiGuid [%d] - %r\n", GopInstance, Status));
                if (EFI_ERROR(Status)) { 
                    DEBUG ((DEBUG_ERROR, "%a() GraphicsOutPutPpi Not found : %r!!!\n", __FUNCTION__, Status));
                    return Status;
                }
                    
                if (GraphicsOutputPpi->SimpleTextOutPpi == FrameInfoHobDataPtr->DisplayFrameInfo[Instance].SimpleTextOut.SimpleTextOutPpi) {
                    FrameInfoHobDataPtr->DisplayFrameInfo[Instance].GraphicsOutput.GraphicsOutputPpi = GraphicsOutputPpi;
                    FrameInfoHobDataPtr->DisplayFrameInfo[Instance].SimpleTextOut.SimpleTextOutPpi   = SimpleTextOutPpi;
                    GraphicsOutputPpi->SimpleTextOutPpi = SimpleTextOutPpi;
                    break;
                }
                GopInstance++;
                    
            } while (TRUE); 
        } else {
            FrameInfoHobDataPtr->DisplayFrameInfo[Instance].SimpleTextOut.SimpleTextOutPpi   = SimpleTextOutPpi;
        }
    }
    
    DEBUG((DEBUG_INFO, "UpdateFrameInfoHobOnMemoryDiscoveredNotify End\n"));
    return Status;
}
/**
    Gets the BMP logo from FV and converts to BLT buffer and draws the logo on screen

    @param   VOID               

    @retval  VOID
**/
VOID
DrawAmiLogo ()
{
    EFI_STATUS                  Status;
    UINTN                       BltBuffer = 0;
    UINTN                       Size = 0;
    UINTN                       Height;
    UINTN                       Width;
    EFI_GUID                    LogoGuid = PEI_GRAPHICS_BMP_LOGO_FFS_GUID;
    AMI_EARLY_CONSOLE_OUT_PPI   *AmiEarlyConsoleOutPpi;
    
    Status = PeiServicesLocatePpi (
                        &gAmiEarlyConsoleOutPpiGuid,
                        0,
                        NULL,
                        (VOID**)&AmiEarlyConsoleOutPpi);
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR,"%a : Locate AmiEarlyConsoleOutPpi : %r\n",__FUNCTION__, Status));
        return;
    }
    
    Status = GetBmpLogoBlt (&BltBuffer, &Size, &Height, &Width, LogoGuid);
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR, "Logo read failed!!! %r\n",Status));
        if (FixedPcdGetBool(AmiPcdDrawDefaultLogoInPei)) {
            // Logo read from FV failed. Display default image
            Status = ConvertBmpToUgaBlt (
                                    AmiLogo, 
                                    sizeof(AmiLogo), 
                                    &BltBuffer, 
                                    &Size, 
                                    &Height, 
                                    &Width );
        }
        if (EFI_ERROR(Status)) {
            DEBUG((DEBUG_ERROR, "BLT conversion failed!!!\n"));
            return;
        }
    }
        
    Status = AmiEarlyConsoleOutPpi->Blt (
                    EarlyConsoleDisplayFrameLogo,
                    (EFI_GRAPHICS_OUTPUT_BLT_PIXEL*)BltBuffer,
                    EfiBltBufferToVideo,
                    0,
                    0,
                    Width,
                    Height,
                    TRUE );
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR, "Logo display failed!!!\n"));
    }
}

/**
    Gets the Background BMP logo from FV and converts to BLT buffer and draws the image on screen

    @param GraphicsOutputPpi - Pointer to GraphicsOutputPpi   
    @param BgImageGuid - Background image Guid        

    @retval  VOID
**/
VOID
DrawBgImage (
    AMI_GRAPHICS_OUTPUT_PPI    *GraphicsOutputPpi,
    EFI_GUID                   BgImageGuid
)
{
    EFI_STATUS      Status;
    UINTN           BltBuffer = 0;
    UINTN           Size = 0;
    UINTN           Height;
    UINTN           Width;
    
    Status = GetBmpLogoBlt (&BltBuffer, &Size, &Height, &Width, BgImageGuid);
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR, "%a GetBmpLogoBlt - %r\n", __FUNCTION__, Status));
        return;
    }
    
    Status = GraphicsOutputPpi->Blt (
                                GraphicsOutputPpi,
                                (EFI_GRAPHICS_OUTPUT_BLT_PIXEL *)BltBuffer,
                                EfiBltBufferToVideo,
                                0,
                                0,
                                0,
                                0,
                                Width,
                                Height,
                                0 );
    DEBUG((DEBUG_ERROR, "%a Blt - %r\n", __FUNCTION__, Status));
}
/**
    Updated Display Frames info HOB with New Simple text Out PPI 

    @param  **PeiServices - pointer to the PEI services.
    @param  NotifyDescriptor - pointer to descriptor
    @param  Ppi - void pointer 
    
    @retval EFI_STATUS
**/
EFI_STATUS
EFIAPI
UpdateFrameInfoHobOnSimpleTextOutNotify (
    IN EFI_PEI_SERVICES     **PeiServices,
    IN EFI_PEI_NOTIFY_DESCRIPTOR  *NotifyDescriptor,
    IN VOID                       *Ppi 
)
{
    EFI_STATUS                              Status = EFI_NOT_FOUND;
    EARLY_CONSOLE_DISPLAY_FRAME_INFO_HOB    *FrameInfoHobDataPtr;
    AMI_SIMPLE_TEXT_OUTPUT_PPI              *SimpleTextOutPpi;
    AMI_GRAPHICS_OUTPUT_PPI                 *GraphicsOutputPpi;
    EFI_HOB_GUID_TYPE                       *GuidHob;
    UINT8                                   Instance;
    UINT8                                   GopInstance;
    EFI_GUID                                BgImageGuid = PEI_GRAPHICS_BMP_BG_IMAGE_FFS_GUID;
    
    DEBUG((DEBUG_INFO, "UpdateFrameInfoHobOnSimpleTextOutNotify\n"));
    
    Status = (*PeiServices)->LocatePpi(
                                 (const EFI_PEI_SERVICES**)PeiServices,
                                 &gEfiPeiMemoryDiscoveredPpiGuid,
                                 0,
                                 NULL,
                                 NULL);
    if (Status == EFI_SUCCESS) {
        UpdateAmiEarlyConsoleOutPpiAfterMemoryDiscovered();
    }
    
    GuidHob = GetFirstGuidHob (&gAmiEarlyConsoleDisplayFrameInfoHobGuid);
    if (GuidHob == NULL) {
        return EFI_NOT_READY;
    }
    
    FrameInfoHobDataPtr = GET_GUID_HOB_DATA (GuidHob);
    
    //Find the free available Instance to update simple text out PPI and respective Graphics output PPI.
    for (Instance = 0; Instance < FixedPcdGet8 (AmiPcdSimpleTextOutMaxPpiSupported); Instance++) {

        SimpleTextOutPpi = (AMI_SIMPLE_TEXT_OUTPUT_PPI *)Ppi;
        if (FrameInfoHobDataPtr->DisplayFrameInfo[Instance].SimpleTextOut.SimpleTextOutPpi == SimpleTextOutPpi) {
            DEBUG((DEBUG_INFO, "%a: Found SimpleTextOut same instance\n", __FUNCTION__));
            break;
        }
        
        if (FrameInfoHobDataPtr->DisplayFrameInfo[Instance].IsDisplayFrameInfoValid) {
            continue;
        }
        
        DEBUG((DEBUG_INFO, "%a: Free Instance found- %d\n", __FUNCTION__, Instance));
        


        FrameInfoHobDataPtr->DisplayFrameInfo[Instance].SimpleTextOutConsoleType = SimpleTextOutPpi->ConsoleType;
        FrameInfoHobDataPtr->DisplayFrameInfo[Instance].SimpleTextOut.SimpleTextOutPpi = SimpleTextOutPpi;
        
        if (SimpleTextOutPpi->ConsoleType == PeiSimpleTextOutConsoleTypeVideoGop) {
            
            DEBUG((DEBUG_INFO, "GOP SimpleTextOut found\n"));
            
            //If console type is Graphics and should have respective GOP PPI installed,
            //if not return EFI_NOT_FOUND;
            GopInstance = 0;
            do {
                
                // Locate the Graphics Output PPI
                Status = PeiServicesLocatePpi(
                                        &gAmiGraphicsOutPutPpiGuid,
                                        GopInstance,
                                        NULL,
                                        (VOID**)&GraphicsOutputPpi);
                DEBUG ((DEBUG_INFO, "%a() GraphicsOutPutPpi found : %r!!!\n", __FUNCTION__, Status));
                if (EFI_ERROR(Status)) { 
                    DEBUG ((DEBUG_ERROR, "%a() GraphicsOutPutPpi Not found : %r!!!\n", __FUNCTION__, Status));
                    return Status;
                }
                
                if (GraphicsOutputPpi->SimpleTextOutPpi == SimpleTextOutPpi) {
                    FrameInfoHobDataPtr->DisplayFrameInfo[Instance].GraphicsOutput.GraphicsOutputPpi = GraphicsOutputPpi;
                    break;
                }
                GopInstance++;
                
            } while (TRUE);
        }
        
        Status = CreateFrames (&FrameInfoHobDataPtr->DisplayFrameInfo[Instance]);
        
        if (!EFI_ERROR(Status)) {
            DEBUG((DEBUG_INFO, "%a: New display found\n", __FUNCTION__));
            if ((SimpleTextOutPpi->ConsoleType == PeiSimpleTextOutConsoleTypeVideoGop) &&
                (GraphicsOutputPpi != NULL)) {
                if (FixedPcdGetBool(AmiPcdDisplayBackgroundImageInPei)) {
                    DrawBgImage(GraphicsOutputPpi, BgImageGuid);
                }
                DrawAmiLogo ();
            }
            DisplayStringFromHob (&FrameInfoHobDataPtr->DisplayFrameInfo[Instance]);
        }
        break;
    }
    
    DEBUG((DEBUG_INFO, "UpdateFrameInfoHobOnSimpleTextOutNotify End\n"));
    return Status;
}

/**
    Defines each frame dimensions based on input
 
    @param FrameDefine - Pointer to Frame define
    @param DisplayFrameInfo - Pointer to Frame Information
    
    @return EFI_STATUS
**/
EFI_STATUS
DefineFrameDimensions (
    IN EARLY_GRAPHICS_FRAME_DEFINE          *FrameDefine,
    IN DISPLAY_FRAME_INFO                   *DisplayFrameInfo
)
{
    EFI_STATUS                       Status;
    UINT8                            Index;
    UINT8                            FrameNo;
    AMI_EARLY_GRAPHICS_FRAME_INFO    *FrameInfo;
    AMI_EARLY_GRAPHICS_FRAME_INFO    MatchingRowFrameInfo;
    AMI_EARLY_GRAPHICS_FRAME_INFO    PrevColumnFrameInfo;
    UINTN                            MaxRows;
    UINTN                            MaxColumns;
    
    if ((DisplayFrameInfo == NULL) || (FrameDefine == NULL)) {
        return EFI_INVALID_PARAMETER;
    }
    
    DEBUG((DEBUG_ERROR, "%a\n", __FUNCTION__));
    
    FrameInfo = &DisplayFrameInfo->FrameInfo[FrameDefine->FrameType];
    FrameNo   = FrameDefine->FrameNo;
    
    ZeroMem (&MatchingRowFrameInfo, sizeof(MatchingRowFrameInfo));
    ZeroMem (&PrevColumnFrameInfo, sizeof(PrevColumnFrameInfo));
    
    // Find the matching Row FrameNo info and Previous Row FrameNo Info
    for (Index = EarlyConsoleDisplayFrameInfo; Index <EarlyConsoleDisplayFrameMax; Index++) {
        if ((DisplayFrameInfo->FrameInfo[Index].IsFrameInfoValid) && 
            (DisplayFrameInfo->FrameInfo[Index].FrameNo == FrameNo)) {
            CopyMem (&MatchingRowFrameInfo, &DisplayFrameInfo->FrameInfo[Index], sizeof(MatchingRowFrameInfo));
            break;
        }
    }
    
    for (Index = EarlyConsoleDisplayFrameInfo; Index <EarlyConsoleDisplayFrameMax; Index++) {
        if ((DisplayFrameInfo->FrameInfo[Index].IsFrameInfoValid) && 
            (DisplayFrameInfo->FrameInfo[Index].FrameNo == (FrameNo - 1))) {
            CopyMem (&PrevColumnFrameInfo, &DisplayFrameInfo->FrameInfo[Index], sizeof(PrevColumnFrameInfo));
            break;
        }
    }

    if (DisplayFrameInfo->SimpleTextOutConsoleType == PeiSimpleTextOutConsoleTypeVideoGop) {

        FrameInfo->HorizontalResolution = DisplayFrameInfo->GraphicsOutput.GraphicsOutputPpi->Mode->Info->HorizontalResolution;
        FrameInfo->VerticalResolution   = DisplayFrameInfo->GraphicsOutput.GraphicsOutputPpi->Mode->Info->VerticalResolution;

        FrameInfo->StartX = MatchingRowFrameInfo.IsFrameInfoValid ? MatchingRowFrameInfo.EndX + 1 : 0;
        FrameInfo->EndX   = FrameInfo->StartX + ((FrameInfo->HorizontalResolution * FrameDefine->RowPercentage) / 100) - 1;
        
        FrameInfo->StartY = PrevColumnFrameInfo.IsFrameInfoValid ? PrevColumnFrameInfo.EndY + 1 : 0;
        FrameInfo->EndY   = FrameInfo->StartY + ((FrameInfo->VerticalResolution * FrameDefine->ColumnPercentage) / 100) - 1;
    
        FrameInfo->FrameWidth  = FrameInfo->EndX - FrameInfo->StartX + 1;
        FrameInfo->FrameHeight = FrameInfo->EndY - FrameInfo->StartY + 1;
    
        FrameInfo->StartRow = FrameInfo->StartY / EFI_GLYPH_HEIGHT;
        if (FrameInfo->StartY % EFI_GLYPH_HEIGHT != 0) {
            FrameInfo->StartRow++;
        }
        
        FrameInfo->EndRow = (FrameInfo->EndY / EFI_GLYPH_HEIGHT) - 1; // 0 based
    
        FrameInfo->StartColumn = FrameInfo->StartX / EFI_GLYPH_WIDTH;
        if (FrameInfo->StartX % EFI_GLYPH_WIDTH != 0) {
            FrameInfo->StartColumn++;
        }
        FrameInfo->EndColumn = FrameInfo->EndX / EFI_GLYPH_WIDTH;
    
        FrameInfo->CurrentRow    = FrameInfo->StartRow;
        FrameInfo->CurrentColumn = FrameInfo->StartColumn;
    
        FrameInfo->FrameType = FrameDefine->FrameType;
        FrameInfo->FrameNo   = FrameDefine->FrameNo;
        
        FrameInfo->IsFrameInfoValid = TRUE;
        
        if (FrameNo == 0) {
            Status = DisplayFrameInfo->SimpleTextOut.SimpleTextOutPpi->QueryMode (
                                                                DisplayFrameInfo->SimpleTextOut.SimpleTextOutPpi,
                                                                DisplayFrameInfo->SimpleTextOut.SimpleTextOutPpi->Mode->Mode,
                                                                &MaxColumns,
                                                                &MaxRows );
            if (!EFI_ERROR(Status)) {
                FrameInfo->DeltaX = (UINT32)(FrameInfo->HorizontalResolution - (MaxColumns * EFI_GLYPH_WIDTH)) / 2;
                FrameInfo->DeltaY = (UINT32)(FrameInfo->VerticalResolution - (MaxRows * EFI_GLYPH_HEIGHT)) / 2;
            }
        } else {
            FrameInfo->DeltaX = PrevColumnFrameInfo.DeltaX;
            FrameInfo->DeltaY = PrevColumnFrameInfo.DeltaY;
        }

        if (FrameDefine->FrameType == EarlyConsoleDisplayFrameProgressBar) { 
            FrameInfo->CurrentPercentage = 100;
        }
        
        // Select current row and column at the center of frame
        if (FrameDefine->FrameType == EarlyConsoleDisplayFramePostCode) { 
            FrameInfo->CurrentRow    = FrameInfo->StartRow + (FrameInfo->EndRow - FrameInfo->StartRow) / 2;
            FrameInfo->CurrentColumn = FrameInfo->StartColumn + (FrameInfo->EndColumn - FrameInfo->StartColumn) / 2;
        }
        
        return EFI_SUCCESS;
    } else if ((DisplayFrameInfo->SimpleTextOutConsoleType == PeiSimpleTextOutConsoleTypeSerial) ||
               (DisplayFrameInfo->SimpleTextOutConsoleType == PeiSimpleTextOutConsoleTypeVideoText)) {
        
        Status = DisplayFrameInfo->SimpleTextOut.SimpleTextOutPpi->QueryMode (
                                                        DisplayFrameInfo->SimpleTextOut.SimpleTextOutPpi,
                                                        DisplayFrameInfo->SimpleTextOut.SimpleTextOutPpi->Mode->Mode,
                                                        &MaxColumns,
                                                        &MaxRows);
        if (EFI_ERROR(Status)) {
            return Status;
        }
        
        FrameInfo->StartRow = PrevColumnFrameInfo.IsFrameInfoValid? PrevColumnFrameInfo.EndRow + 1 : 0;
        FrameInfo->EndRow   = FrameInfo->StartRow + (((UINT32)MaxRows * FrameDefine->ColumnPercentage) / 100) - 1; // Height
    
        FrameInfo->StartColumn = MatchingRowFrameInfo.IsFrameInfoValid? MatchingRowFrameInfo.EndColumn + 1 : 0;
        FrameInfo->EndColumn   = FrameInfo->StartColumn + (((UINT32)MaxColumns * FrameDefine->RowPercentage) / 100) - 1; // Width
    
        FrameInfo->CurrentRow    = FrameInfo->StartRow;
        FrameInfo->CurrentColumn = FrameInfo->StartColumn;
    
        FrameInfo->FrameType   = FrameDefine->FrameType;
        FrameInfo->FrameNo   = FrameDefine->FrameNo;
        
        FrameInfo->IsFrameInfoValid = TRUE;
        
        return EFI_SUCCESS;
    }
    
    return EFI_UNSUPPORTED;
}

/**
    Creates number of frames based on Porting
 
    @param DisplayFrameInfo   Pointer to Display Frame Info
    
    @return EFI_STATUS
**/
EFI_STATUS
CreateFrames (
  IN DISPLAY_FRAME_INFO     *DisplayFrameInfo
)
{
    EFI_STATUS                    Status = EFI_SUCCESS;
    EARLY_GRAPHICS_FRAME_DEFINE   *FrameDefinitions;
    UINT8                         Index;
    
    //Validate the Frame definition.
    if (!IsFrameDefinitionValid (DisplayFrameInfo->SimpleTextOutConsoleType, &FrameDefinitions)) {
        return EFI_UNSUPPORTED;
    }
    
    DisplayFrameInfo->IsDisplayFrameInfoValid = TRUE;
    
    for (Index = 0;  FrameDefinitions[Index].FrameType != EarlyConsoleDisplayFrameMax; Index++) {
        Status = DefineFrameDimensions (&FrameDefinitions[Index], DisplayFrameInfo);
        if (EFI_ERROR(Status)) {
            DisplayFrameInfo->IsDisplayFrameInfoValid = FALSE;
            break;
        }
    }

    DEBUG((DEBUG_INFO," %a() SimpleTextOutConsoleType       : %d \n" ,__FUNCTION__, DisplayFrameInfo->SimpleTextOutConsoleType));
    DEBUG((DEBUG_INFO," %a() IsDisplayFrameInfoValid        : %d \n" ,__FUNCTION__, DisplayFrameInfo->IsDisplayFrameInfoValid));
    DEBUG((DEBUG_INFO," %a() SimpleTextOutPpi               : %x \n" ,__FUNCTION__, DisplayFrameInfo->SimpleTextOut.SimpleTextOutPpi));
    DEBUG((DEBUG_INFO," %a() GraphicsOutPutPpi              : %x \n" ,__FUNCTION__, DisplayFrameInfo->GraphicsOutput.GraphicsOutputPpi));
    
    for (Index = 0; FrameDefinitions[Index].FrameType != EarlyConsoleDisplayFrameMax; Index++) {
        DEBUG((DEBUG_INFO,"\n"));
        DEBUG((DEBUG_INFO," %a() FrameType                  : %d \n" ,__FUNCTION__, DisplayFrameInfo->FrameInfo[Index].FrameType));
        DEBUG((DEBUG_INFO," %a() FrameNo                    : %d \n" ,__FUNCTION__, DisplayFrameInfo->FrameInfo[Index].FrameNo));
        DEBUG((DEBUG_INFO," %a() IsFrameInfoValid           : %d \n" ,__FUNCTION__, DisplayFrameInfo->FrameInfo[Index].IsFrameInfoValid));
        DEBUG((DEBUG_INFO," %a() StartX - EndX              : %d - %d \n", __FUNCTION__, DisplayFrameInfo->FrameInfo[Index].StartX, 
                DisplayFrameInfo->FrameInfo[Index].EndX));
        DEBUG((DEBUG_INFO," %a() StartY - EndY              : %d - %d \n", __FUNCTION__, DisplayFrameInfo->FrameInfo[Index].StartY, 
                DisplayFrameInfo->FrameInfo[Index].EndY));
        DEBUG((DEBUG_INFO," %a() StartRow - EndRow          : %d - %d \n", __FUNCTION__, DisplayFrameInfo->FrameInfo[Index].StartRow, 
                DisplayFrameInfo->FrameInfo[Index].EndRow));
        DEBUG((DEBUG_INFO," %a() StartColumn - EndColumn    : %d - %d \n", __FUNCTION__, DisplayFrameInfo->FrameInfo[Index].StartColumn, 
                DisplayFrameInfo->FrameInfo[Index].EndColumn));
        DEBUG((DEBUG_INFO," %a() CurrentRow - CurrentColumn : %d - %d \n", __FUNCTION__, DisplayFrameInfo->FrameInfo[Index].CurrentRow, 
                DisplayFrameInfo->FrameInfo[Index].CurrentColumn));
        DEBUG((DEBUG_INFO," %a() CurrentPercentage          : %d\n", __FUNCTION__,DisplayFrameInfo->FrameInfo[Index].CurrentPercentage));
    }
    
    return Status;
}

/**
    Initialize display frames as per display device type.
    
    @param None
    
    @return AMI_EARLY_GRAPHICS_FRAME_INFO_HOB  Pointer to Frame info HOB
**/
EARLY_CONSOLE_DISPLAY_FRAME_INFO_HOB *
EarlyConsoleInitFrames ()
{
    EFI_STATUS                                  Status;
    EFI_HOB_GUID_TYPE                           *GuidHob;
    EARLY_CONSOLE_DISPLAY_FRAME_INFO_HOB        FrameInfoHobData;
    EARLY_CONSOLE_DISPLAY_FRAME_INFO_HOB        *FrameInfoHobDataPtr;
    AMI_SIMPLE_TEXT_OUTPUT_PPI                  *SimpleTextOutPpi;
    AMI_GRAPHICS_OUTPUT_PPI                     *GraphicsOutputPpi;
    UINT8                                       Instance;
    UINT8                                       GopInstance;
    EFI_GUID                                    BgImageGuid = PEI_GRAPHICS_BMP_BG_IMAGE_FFS_GUID;
    
    //Locate Frame Info HOB to make sure Init frames called once in a boot time
    GuidHob = GetFirstGuidHob (&gAmiEarlyConsoleDisplayFrameInfoHobGuid);
    if (GuidHob != NULL){
        return GET_GUID_HOB_DATA (GuidHob);
    }
    
    DEBUG ((DEBUG_ERROR, "%a() Initializing Frame Info structure\n", __FUNCTION__));
    
    PcdSetBoolS(AmiPcdCallFromEarlyConsoleOut, FALSE);
    
    ZeroMem (&FrameInfoHobData, sizeof(FrameInfoHobData));

    FrameInfoHobDataPtr = BuildGuidDataHob (
                                   &gAmiEarlyConsoleDisplayFrameInfoHobGuid,
                                   &FrameInfoHobData,
                                   sizeof (FrameInfoHobData));
    
    //Locate all simple text out PPI and respective Graphics output PPI.
    for (Instance = 0; Instance < FixedPcdGet8 (AmiPcdSimpleTextOutMaxPpiSupported); Instance++) {
        
        // Locate the SimpleTextout PPI
        Status = PeiServicesLocatePpi(
                                &gAmiSimpleTextOutPpiGuid,
                                Instance,
                                NULL,
                                (VOID**)&SimpleTextOutPpi);
        if (EFI_ERROR(Status)) { 
            DEBUG ((DEBUG_ERROR, "%a() Locate SimpleTextOutPpi - %r\n", __FUNCTION__, Status));
            break;
        }
        
        FrameInfoHobDataPtr->DisplayFrameInfo[Instance].SimpleTextOutConsoleType       = SimpleTextOutPpi->ConsoleType;
        FrameInfoHobDataPtr->DisplayFrameInfo[Instance].SimpleTextOut.SimpleTextOutPpi = SimpleTextOutPpi;
        
        if (SimpleTextOutPpi->ConsoleType == PeiSimpleTextOutConsoleTypeVideoGop) {
            
            DEBUG ((DEBUG_ERROR, "%a() Gop Console Type\n", __FUNCTION__));
            
            //If console type is Graphics and should have respective GOP PPI installed,
            //if not return EFI_NOT_FOUND;
            GopInstance = 0;
            do {
                // Locate the Graphics Output PPI
                Status = PeiServicesLocatePpi(
                                        &gAmiGraphicsOutPutPpiGuid,
                                        GopInstance,
                                        NULL,
                                        (VOID**)&GraphicsOutputPpi);
                if (EFI_ERROR(Status)) { 
                    DEBUG ((DEBUG_ERROR, "%a() GraphicsOutPutPpi Not found : %r!!!\n", __FUNCTION__, Status));
                    return NULL;
                }
                
                if (GraphicsOutputPpi->SimpleTextOutPpi == SimpleTextOutPpi) {
                    FrameInfoHobDataPtr->DisplayFrameInfo[Instance].GraphicsOutput.GraphicsOutputPpi = GraphicsOutputPpi;
                    break;
                }
                GopInstance++;
                
            } while (TRUE);
        }
        
        Status = CreateFrames (&FrameInfoHobDataPtr->DisplayFrameInfo[Instance]);
        if (EFI_ERROR(Status)) {
            continue;
        }
        
        if ((SimpleTextOutPpi->ConsoleType == PeiSimpleTextOutConsoleTypeVideoGop) &&
            (GraphicsOutputPpi != NULL)) {
            if (FixedPcdGetBool(AmiPcdDisplayBackgroundImageInPei)) {
                DrawBgImage(GraphicsOutputPpi, BgImageGuid);
            }
            DrawAmiLogo ();
        }
    }
    
    //Register SimpleTextOut Notification Ppi to initialize frame Info structure 
    //for respective Console device.
    Status = PeiServicesNotifyPpi(mPeiEarlyConsoleDisplayNotifyList);
    
    return FrameInfoHobDataPtr;
}

/**
  @internal
    This function scrolls up the text in the given frame
    
    @param   SimpleTextOutPpi,
    @param   GraphicsOutPutPpi;
    @param   FrameInfo,
    @param   StartRow, 
    @param   NoOfRows,     
    @param   LastRow,
    @param   MaxColumns
   
    @return None
   
  @endinternal
**/
VOID 
FrameScrollUp (
    IN AMI_SIMPLE_TEXT_OUTPUT_PPI             *SimpleTextOutPpi,
    IN AMI_GRAPHICS_OUTPUT_PPI                *GraphicsOutPutPpi,
    IN AMI_EARLY_GRAPHICS_FRAME_INFO          *FrameInfo,
    IN UINT32                                  StartRow, 
    IN UINT32                                  NoOfRows,     
    IN UINT32                                  LastRow,
    IN UINT32                                  MaxColumns
)
{
    EFI_GRAPHICS_OUTPUT_BLT_PIXEL   Fill = EARLY_GRAPHICS_BACKGROUND_COLOR;
    
    if (SimpleTextOutPpi->ConsoleType == PeiSimpleTextOutConsoleTypeVideoGop) {
        // Read 2nd row to last row in frame and
        // update it in 1st row to last row - 1
        GraphicsOutPutPpi->Blt(
                GraphicsOutPutPpi,
                &Fill,
                EfiBltVideoToVideo,
                FrameInfo->DeltaX + FrameInfo->StartX,
                FrameInfo->DeltaY + (StartRow + 1) * EFI_GLYPH_HEIGHT,   // SourceY                 
                FrameInfo->DeltaX + FrameInfo->StartX,
                FrameInfo->DeltaY + StartRow * EFI_GLYPH_HEIGHT,         // Dest Y
                MaxColumns * EFI_GLYPH_WIDTH,
                NoOfRows * EFI_GLYPH_HEIGHT,
                0);
    
        // Override last row with background color
        GraphicsOutPutPpi->Blt(
                GraphicsOutPutPpi,
                &Fill,
                EfiBltVideoFill,
                0,
                0,                   
                FrameInfo->DeltaX,
                FrameInfo->DeltaY + (LastRow * EFI_GLYPH_HEIGHT),
                MaxColumns * EFI_GLYPH_WIDTH,
                EFI_GLYPH_HEIGHT,
                0);
    } 
#if (VideoTextConsole_SUPPORT == 1)
    else if (SimpleTextOutPpi->ConsoleType == PeiSimpleTextOutConsoleTypeVideoText) {
        AmiVideoScrollUp (StartRow, LastRow + 1, MaxColumns);
    }
#endif
}
  
/**
  @internal
    This function process next line characters.
    
    @param SimpleTextOutPpi  Pointer to Simple Text out PPI
    @param GraphicsOutPutPpi Pointer to Graphics output PPI
    @param FrameInfo         Pointer to Frame Info Data
    
    @return None
    
  @endinternal
**/
VOID
ProcessLineFeed (
  IN AMI_SIMPLE_TEXT_OUTPUT_PPI             *SimpleTextOutPpi,
  IN AMI_GRAPHICS_OUTPUT_PPI                *GraphicsOutPutPpi,
  IN AMI_EARLY_GRAPHICS_FRAME_INFO          *FrameInfo
)
{
    UINTN                               CurrentRow;
    UINTN                               CurrentColumn;
    
    // If current row is last row, scroll up
    if (FrameInfo->CurrentRow == FrameInfo->EndRow) {
        FrameScrollUp (
                SimpleTextOutPpi,
                GraphicsOutPutPpi,
                FrameInfo,
                FrameInfo->StartRow,                        // StartRow
                FrameInfo->EndRow - FrameInfo->StartRow,    // NoOfRows
                FrameInfo->EndRow,                          // LastRow
                FrameInfo->EndColumn - FrameInfo->StartColumn + 1);
        FrameInfo->CurrentRow--;
    }
    
    CurrentRow    = ++FrameInfo->CurrentRow;
    CurrentColumn = FrameInfo->CurrentColumn;

    SimpleTextOutPpi->SetCursorPosition(
                                SimpleTextOutPpi, 
                                CurrentColumn, 
                                CurrentRow);
}

/**
  @internal
    This function sets cursor to first column of current row
    
    @param SimpleTextOutPpi  Pointer to Simple Text out PPI
    @param GraphicsOutPutPpi Pointer to Graphics output PPI
    @param FrameInfo         Pointer to Frame Info Data
    
    @return None
    
  @endinternal
**/
VOID
ProcessCarriageReturn (
  IN AMI_SIMPLE_TEXT_OUTPUT_PPI             *SimpleTextOutPpi,
  IN AMI_GRAPHICS_OUTPUT_PPI                *GraphicsOutPutPpi,
  IN AMI_EARLY_GRAPHICS_FRAME_INFO          *FrameInfo
)
{
    FrameInfo->CurrentColumn = FrameInfo->StartColumn;

    // Set cursor to first column of current row
    SimpleTextOutPpi->SetCursorPosition (
                                SimpleTextOutPpi, 
                                FrameInfo->CurrentColumn, 
                                FrameInfo->CurrentRow);
}

/**
  @internal
    This function displays string in the given frame
    
    @param SimpleTextOutPpi  Pointer to Simple Text out PPI
    @param GraphicsOutPutPpi Pointer to Graphics output PPI
    @param FrameInfo         Pointer to Frame Info Data
    @param String            String to Print in console
    
    @return None
    
  @endinternal
**/
VOID 
DisplayStringInFrame (
  IN AMI_SIMPLE_TEXT_OUTPUT_PPI             *SimpleTextOutPpi,
  IN AMI_GRAPHICS_OUTPUT_PPI                *GraphicsOutPutPpi,
  IN AMI_EARLY_GRAPHICS_FRAME_INFO          *FrameInfo,
  IN CHAR16                                 *String
)
{
    UINTN                               StringLength = StrLen(String);
    UINTN                               CharCount;
    CHAR16                              *Buffer;

    // If dynamic background for string is enabled, then
    // set AmiPcdCallFromEarlyConsoleOut to inform SimpleTextOut instance to update the string background
    // with actual background data    
    if (FixedPcdGetBool(AmiPcdDynamicBackgroundStringsSupport)) {
        PcdSetBoolS (AmiPcdCallFromEarlyConsoleOut, TRUE);
    }

    while (TRUE) {
        CharCount = FrameInfo->EndColumn - FrameInfo->CurrentColumn;
        // If current row won't accommodate input string then truncate it
        // to available characters in current row and print remaining
        // string in next row
        if (StringLength > CharCount) {
            Buffer = (CHAR16*)AllocateZeroPool ((CharCount * sizeof(CHAR16)) + 2); 
            CopyMem (Buffer, String, CharCount * sizeof(UINT16));
            
            SimpleTextOutPpi->OutputString (
                                    SimpleTextOutPpi, 
                                    Buffer);
            FreePool (Buffer);
            FrameInfo->CurrentColumn = SimpleTextOutPpi->Mode->CursorColumn;
            FrameInfo->CurrentRow    = SimpleTextOutPpi->Mode->CursorRow;
            
            ProcessLineFeed (
                        SimpleTextOutPpi,
                        GraphicsOutPutPpi,
                        FrameInfo);
            
            ProcessCarriageReturn (
                        SimpleTextOutPpi,
                        GraphicsOutPutPpi,
                        FrameInfo);

            StringLength -= CharCount;
            String += CharCount;
        } else {
            SimpleTextOutPpi->OutputString (
                                    SimpleTextOutPpi, 
                                    String);
            FrameInfo->CurrentColumn = SimpleTextOutPpi->Mode->CursorColumn;
            FrameInfo->CurrentRow    = SimpleTextOutPpi->Mode->CursorRow;
            break;
        }
    }

    // Once string displayed, clear AmiPcdCallFromEarlyConsoleOut
    if (FixedPcdGetBool(AmiPcdDynamicBackgroundStringsSupport)) {
        PcdSetBoolS (AmiPcdCallFromEarlyConsoleOut, FALSE);
    }
    
    return;
}

/**
  @internal 
    This function process the strings(text, carriage return, line feed) characters
    and displays in screen
    
    @param SimpleTextOutPpi  Pointer to Simple Text out PPI
    @param GraphicsOutPutPpi Pointer to Graphics output PPI
    @param FrameInfo         Pointer to Frame Info Data
    @param String            String to Print in console
    
    @return None
    
  @endinternal
**/
VOID
ProcessString (
  IN AMI_SIMPLE_TEXT_OUTPUT_PPI             *SimpleTextOutPpi,
  IN AMI_GRAPHICS_OUTPUT_PPI                *GraphicsOutPutPpi,
  IN AMI_EARLY_GRAPHICS_FRAME_INFO          *FrameInfo,
  IN CHAR16                                 *String
)
{
    CHAR16              *Temp;
    CHAR16              *RemainStr = NULL;
    BOOLEAN             LineFeed = FALSE;
    BOOLEAN             CarriageReturn = FALSE;
    UINT16              Index = 0;
    CHAR16              Buffer[200];
    
    Temp = String;
    
    ZeroMem (Buffer, sizeof(Buffer));

    // Process to know whether string has line feed or carriage return characters
    // if yes, first print the characters till that character and process \n \r characters
    while (Temp[Index] != 0) {
        switch (Temp[Index]) {
            case CHAR_CARRIAGE_RETURN:
                CarriageReturn = TRUE;
                break;

            case CHAR_LINEFEED:
                LineFeed = TRUE;
                break;
        }

        if (CarriageReturn || LineFeed) {
            RemainStr = &Temp[Index+1];
            break;
        }

        Index++;
    }

    if (Index > (sizeof(Buffer)/sizeof(UINT16))) {
        return;
    }

    if (Index != 0) {
        CopyMem (Buffer, String, Index * sizeof(UINT16));
        DisplayStringInFrame (
                        SimpleTextOutPpi,
                        GraphicsOutPutPpi,
                        FrameInfo, 
                        Buffer);
    }
    
    // Carriage Return \r found. Set cursor at column 0 of current row
    if (CarriageReturn) {
        ProcessCarriageReturn (
                        SimpleTextOutPpi,
                        GraphicsOutPutPpi,
                        FrameInfo);
    }

    // Line Feed \n found. Set cursor at current column of next row
    if (LineFeed) {
        ProcessLineFeed (
                    SimpleTextOutPpi,
                    GraphicsOutPutPpi,
                    FrameInfo);
    }
 
    // When string reaches end, break the recursive call
    if ((RemainStr == NULL) || (*RemainStr == 0)) {
        return;
    }
    
    // Recursive call to process remaining string
    ProcessString (
             SimpleTextOutPpi,
             GraphicsOutPutPpi,
             FrameInfo, 
             RemainStr);
    return;
}
