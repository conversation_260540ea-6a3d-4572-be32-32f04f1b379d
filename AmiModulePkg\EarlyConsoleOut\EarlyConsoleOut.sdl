TOKEN
    Name  = "EarlyConsoleOut_SUPPORT"
    Value  = "1"
    Help  = "Main switch to enable EarlyConsoleOut support in Project"
    TokenType = Boolean
    Master = Yes
End

TOKEN
    Name  = "EarlyTextConsole_SUPPORT"
    Value  = "1"
    Help  = "Main switch to enable Early Text Console support in Project"
    TokenType = Boolean
    TargetH = Yes
End

TOKEN
    Name  = "EarlyGraphicsConsole_SUPPORT"
    Value  = "1"
    Help  = "Main switch to enable Early Graphics Console support in Project"
    TokenType = Boolean
    TargetH = Yes
End

TOKEN
    Name  = "INFO_MESSAGE_BUFFER_SIZE"
    Value  = "500"
    Help  = "Size of the buffer to store system information messages to be displayed before gEfiPeiMemoryDiscoveredPpiGuid installed"
    TokenType = Integer
    TargetH = Yes
End

TOKEN
    Name  = "DEBUG_MESSAGE_BUFFER_SIZE"
    Value  = "500"
    Help  = "Size of the buffer to store post code messages to be displayed before gEfiPeiMemoryDiscoveredPpiGuid installed"
    TokenType = Integer
    TargetH = Yes
End

ELINK
    Name  = "OemProgressCodeList"
    InvokeOrder = ReplaceParent
    Help  = "OEM Progress Code list in the format of(EFI_STATUS_CODE_VALUE, CheckPointValue),"
End

ELINK
    Name  = "OemProgressCodeToString"
    InvokeOrder = ReplaceParent
    Help  = "OEM Progress Code to String in the format of(EFI_STATUS_CODE_VALUE, String),"
End

ELINK
    Name  = "OemErrorCodeList"
    InvokeOrder = ReplaceParent
    Help  = "OEM Error Code list in the format of(EFI_STATUS_CODE_VALUE, CheckPointValue),"
End

ELINK
    Name  = "OemErrorCodeToString"
    InvokeOrder = ReplaceParent
    Help  = "OEM Error Code to String in the format of(EFI_STATUS_CODE_VALUE, ErrorString, Possible Root cause String, Possible Solution String),"
End

ELINK
    Name  = "PLATFORM_VGA_INIT_INTERFACE_HOOK"
    InvokeOrder = ReplaceParent
    Help  = "Hook list to initialize Platform VGA,"
End

OUTPUTREGISTER
    Name = "EarlyConsoleElinkFile"
    File = "EarlyConsoleElink.h"
    Path = "Build"
    Template = "EarlyConsoleElink.txt"
End

INFComponent
    Name  = "PlatformVideoInitNullLib"
    File  = "../Library/PlatformVideoInitNullLib/PlatformVideoInitNullLib.inf"
    Package  = "AmiModulePkg"
    ModuleTypes  = "BASE"
End

LibraryMapping
    Class  = "PlatformVideoInitLib"
    Instance  = "AmiModulePkg.PlatformVideoInitNullLib"
    ModuleTypes  = "PEIM DXE_DRIVER"
End

INFComponent
    Name  = "PlatformSystemInformationNullLib"
    File  = "../Library/PlatformSystemInformationNullLib/PlatformSystemInformationNullLib.inf"
    Package  = "AmiModulePkg"
    ModuleTypes  = "BASE"
End

LibraryMapping
    Class  = "PlatformPeiSystemInformation"
    Instance  = "AmiModulePkg.PlatformSystemInformationNullLib"
    ModuleTypes  = "PEIM"
End

TOKEN
    Name  = "AspeedVgaSupport"
    Value  = "2"
    Help  = "Main switch to enable Aspeed VGA for Early Console support in Project 0 = Disable, 1 = Aspeed2500, 2 = Aspeed2600"
    TokenType = Integer
End

TOKEN
    Name  = "SYSTEM_INFORMATION_SUPPORT"
    Value  = "1"
    Help  = "Main switch to enable system information support in Project"
    TokenType = Boolean
    TargetMAK = Yes
End

ELINK
    Name  = "AspeedVgaInit,"
    Parent  = "PLATFORM_VGA_INIT_INTERFACE_HOOK"
    InvokeOrder = AfterParent
    Help  = "Elink to provide hook which initializes Platform VGA controller."
    Token = "AspeedVgaSupport" "!=" "0"
End

INFComponent
    Name  = "Aspeed2600Lib"
    File  = "../Library/Aspeed2600/Aspeed2600Lib.inf"
    Package  = "AmiModulePkg"
    ModuleTypes  = "BASE"
    Token = "AspeedVgaSupport" "=" "2"
End

LibraryMapping
    Class  = "AspeedLib"
    Instance  = "AmiModulePkg.Aspeed2600Lib"
    ModuleTypes  = "PEIM"
    Token = "AspeedVgaSupport" "=" "2"
End

INFComponent
    Name  = "PeiLogoFontLib"
    File  = "../Library/PeiLogoFontLib/PeiLogoFontLib.inf"
    Package  = "AmiModulePkg"
End

LibraryMapping
    Class  = "PeiLogoFontLib"
    Instance  = "AmiModulePkg.PeiLogoFontLib"
End

INFComponent
    Name  = "PeiVideoInitLib"
    File  = "../Library/VideoInit/PeiVideoInit.inf"
    Package  = "AmiModulePkg"
    ModuleTypes  = "PEIM"
    PreProcess = Yes
End

LibraryMapping
    Class  = "PeiVideoInitLib"
    Instance  = "AmiModulePkg.PeiVideoInitLib"
    ModuleTypes  = "PEIM"
End

ELINK
    Name = "VIDEO_INIT_LIB"
    InvokeOrder = ReplaceParent
    Help = "Elink to link libraries into PeiVideoInitLib.inf at build time."
END

ELINK
    Name = "AspeedLib"
    Parent = "VIDEO_INIT_LIB"
    InvokeOrder = AfterParent
    Help = "Includes AspeedLib into PeiVideoInitLib.inf at build time."
    Token = "AspeedVgaSupport" "!=" "0"
End

INFComponent
    Name  = "EarlyConsoleOutInterfacePei"
    File  = "EarlyConsoleOutInterface/EarlyConsoleOutInterfacePei.inf"
    Package  = "AmiModulePkg"
    ModuleTypes  = "PEIM"
    PreProcess = Yes
End

INFComponent
    Name  = "SystemInformation"
    File  = "SystemInfo/SystemInformation.inf"
    Package  = "AmiModulePkg"
    ModuleTypes  = "PEIM"
    PreProcess = Yes
    Token = "SYSTEM_INFORMATION_SUPPORT" "=" "1"
End

INFComponent
    Name  = "EarlyConsoleOutInterfaceDxe"
    File  = "EarlyConsoleOutInterface/EarlyConsoleOutInterfaceDxe.inf"
    Package  = "AmiModulePkg"
    ModuleTypes  = "DXE_DRIVER"
    PreProcess = Yes
End

TOKEN
    Name  = "EARLY_CONSOLE_PLATFORM_NAME"
    Value  = "Porting Required"
    Help  = "Platform Name !Porting Required!"
    TokenType = Expression
    TargetH = Yes
End

INFComponent
    Name  = "ProgressErrorCodeLib"
    File  = "../Library/ProgressErrorCodeLib/ProgressErrorCodeLib.inf"
    Package  = "AmiModulePkg"
    ModuleTypes  = "PEIM DXE_DRIVER"
End

LibraryMapping
    Class  = "ProgressErrorCodeLib"
    Instance  = "AmiModulePkg.ProgressErrorCodeLib"
    ModuleTypes  = "PEIM DXE_DRIVER"
End

INFComponent
    Name  = "EarlyConsoleStringToImageLib"
    File  = "../Library/EarlyConsoleStringToImageLib/EarlyConsoleStringToImageLib.inf"
    Package  = "AmiModulePkg"
    ModuleTypes  = "BASE"
End

LibraryMapping
    Class  = "StringToImageLib"
    Instance  = "AmiModulePkg.EarlyConsoleStringToImageLib"
    ModuleTypes  = "PEIM DXE_DRIVER"
End

ELINK
    Name  = "EarlyConsoleHotkeyList"
    InvokeOrder = ReplaceParent
    Help  = "Port hotkeys in following method. : is must. It should separate key and text. {Key1:Message1, Key2:Message2}. Ex {Esc: Setup}. Hotkey consuming driver has to report EarlyConsoleOut driver using its respective hotkey porting string"
End

ELINK
    Name  = "EarlyConsoleGraphicsFrameDefineList"
    InvokeOrder = ReplaceParent
    Help  = "Define frames in the format of struct EARLY_GRAPHICS_FRAME_DEFINE. Sum of percentage should be 100. Porting should end with comma"
End

ELINK
    Name = "{EarlyConsoleDisplayFrameInfo, 0, 30, 100},"
    Parent = "EarlyConsoleGraphicsFrameDefineList"
    InvokeOrder = AfterParent
End

ELINK
    Name = "{EarlyConsoleDisplayFrameLogo, 1, 40, 100},"
    Parent = "EarlyConsoleGraphicsFrameDefineList"
    InvokeOrder = AfterParent
End

ELINK
    Name = "{EarlyConsoleDisplayFrameDebug, 2, 30, 100},"
    Parent = "EarlyConsoleGraphicsFrameDefineList"
    InvokeOrder = AfterParent
End

ELINK
    Name  = "EarlyConsoleTextFrameDefineList"
    InvokeOrder = ReplaceParent
    Help  = "Define frames in the format of struct EARLY_GRAPHICS_FRAME_DEFINE. Sum of percentage should be 100. Porting should end with comma"
End

ELINK
    Name = "{EarlyConsoleDisplayFrameInfo, 0, 50, 100},"
    Parent = "EarlyConsoleTextFrameDefineList"
    InvokeOrder = AfterParent
End

ELINK
    Name = "{EarlyConsoleDisplayFrameDebug, 1, 50, 100},"
    Parent = "EarlyConsoleTextFrameDefineList"
    InvokeOrder = AfterParent
End

TOKEN
    Name  = "EARLY_GRAPHICS_FONT_FFS_FILE_GUID"
    Value = "{0xDAC2B117, 0xB5FB, 0x4964,{0xA3, 0x12, 0xD, 0xCC, 0x77, 0x6, 0x1B, 0x9B}}"
    Help  = "Font FFS section GUID"
    TokenType = Expression
    TargetH = Yes
    Range  = "GUID"
End

TOKEN
    Name  = "PEI_GRAPHICS_VBT_FILE_FFS_GUID"
    Value = "{0x7E175642, 0xF3AD, 0x490A, {0x9F, 0x8A, 0x2E, 0x9F, 0xC6, 0x93, 0x3D, 0xDD}}"
    Help  = "VBT file FFS GUID"
    TokenType = Expression
    TargetH = Yes
    Range  = "GUID"
End

TOKEN
    Name  = "PEI_GRAPHICS_BMP_LOGO_FFS_GUID"
    Value = "{0x7BB28B99, 0x61BB, 0x11D5, {0x9A, 0x5D, 0x00, 0x90, 0x27, 0x3F, 0xC1,0x4D}}"
    Help  = "Logo FFS GUID"
    TokenType = Expression
    TargetH = Yes
    Range  = "GUID"
End

TOKEN
    Name  = "PEI_GRAPHICS_BMP_BG_IMAGE_FFS_GUID"
    Value = "{0x31a89c3b, 0x3adb, 0x44dc, {0xaf, 0x6e, 0xd7, 0x40, 0x94, 0x2d, 0x66, 0x45}}"
    Help  = "Logo FFS GUID"
    TokenType = Expression
    TargetH = Yes
    Range  = "GUID"
End

TOKEN
    Name  = "SYSTEM_INFO_GUID_PACKAGE_DEC"
    Value  = " "
    Help  = "Token to add the System Info CallBack Guid package."
    TokenType = Expression
    TargetH = Yes
End

TOKEN
    Name  = "SYSTEM_INFO_CALLBACK_GUID"
    Value  = "gEfiPeiMemoryDiscoveredPpiGuid"
    Help  = "Token to add the System Info callback PPI."
    TokenType = Expression
    TargetH = Yes
End

ELINK
    Name = "VIDEO_TEXT_OUT_LIB"
    InvokeOrder = ReplaceParent
    Help = "Elink to link VideoTextOutLib at build time."
END

ELINK
    Name = "VideoTextOutLib"
    Parent = "VIDEO_TEXT_OUT_LIB"
    InvokeOrder = AfterParent
    Help = "Include VideoTextOutLib."
    Token = "VideoTextConsole_SUPPORT" "=" "1"
End