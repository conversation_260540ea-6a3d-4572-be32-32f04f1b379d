/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */
#ifndef _AMD_CBS_FORM_ID_H_
#define _AMD_CBS_FORM_ID_H_

  #define CBS_CONFIGURATION_VARSTORE_ID                                           0x5000
// Section ID
  #define SETUP_CBS_ROOT_LABEL                                                    0x7000
  #define SETUP_CPU_COMMON_OPTIONS_LABEL                                          0x7001
  #define SETUP_DF_COMMON_OPTIONS_LABEL                                           0x7002
  #define SETUP_UMC_COMMON_OPTIONS_LABEL                                          0x7003
  #define SETUP_NBIO_COMMON_OPTIONS_LABEL                                         0x7004
  #define SETUP_FCH_COMMON_OPTIONS_LABEL                                          0x7005
  #define SETUP_NTB_COMMON_OPTIONS_LABEL                                          0x7006
  #define SETUP_SOC_MISCELLANEOUS_CONTROL_LABEL                                   0x7007
  #define SETUP_CXL_COMMON_OPTIONS_LABEL                                          0x7008
  #define SETUP_PERFORMANCE_LABEL                                                 0x7009
  #define SETUP_CCD_CORE_THREAD_ENABLEMENT_LABEL                                  0x700A
  #define SETUP_PREFETCHER_SETTINGS_LABEL                                         0x700E
  #define SETUP_CORE_WATCHDOG_LABEL                                               0x700F
  #define SETUP_CUSTOM_CORE_PSTATES_LABEL                                         0x7013
  #define SETUP_DOWNCORE_BITMAP_LABEL                                             0x7017
  #define SETUP_PSTATE_LEGAL_DISCLAIMER_DECLINE_LABEL                             0x7018
  #define SETUP_PSTATE_LEGAL_DISCLAIMER_ACCEPT_LABEL                              0x7019
  #define SETUP_MEMORY_ADDRESSING_LABEL                                           0x7049
  #define SETUP_ACPI_LABEL                                                        0x704A
  #define SETUP_LINK_LABEL                                                        0x704B
  #define SETUP_SDCI_LABEL                                                        0x704C
  #define SETUP_PROBE_FILTER_LABEL                                                0x704D
  #define SETUP_XGMI_GLOBAL_PRESET_LIST_LABEL                                     0x7060
  #define SETUP_XGMI_INITIAL_PRESET_LABEL                                         0x7061
  #define SETUP_XGMI_TXEQ_SEARCH_MASK_LABEL                                       0x7062
  #define SETUP_XGMI_AC_DC_COUPLED_LINK_LABEL                                     0x7063
  #define SETUP_XGMI_CHANNEL_TYPE_LABEL                                           0x7064
  #define SETUP_PRESET_P11_LABEL                                                  0x7065
  #define SETUP_PRESET_P12_LABEL                                                  0x7066
  #define SETUP_PRESET_P13_LABEL                                                  0x7067
  #define SETUP_PRESET_P14_LABEL                                                  0x7068
  #define SETUP_PRESET_P15_LABEL                                                  0x7069
  #define SETUP_INITIAL_PRESET_SOCKET_0_LINK_0_LABEL                              0x7079
  #define SETUP_INITIAL_PRESET_SOCKET_0_LINK_1_LABEL                              0x707A
  #define SETUP_INITIAL_PRESET_SOCKET_0_LINK_2_LABEL                              0x707B
  #define SETUP_INITIAL_PRESET_SOCKET_0_LINK_3_LABEL                              0x707C
  #define SETUP_INITIAL_PRESET_SOCKET_1_LINK_0_LABEL                              0x707D
  #define SETUP_INITIAL_PRESET_SOCKET_1_LINK_1_LABEL                              0x707E
  #define SETUP_INITIAL_PRESET_SOCKET_1_LINK_2_LABEL                              0x707F
  #define SETUP_INITIAL_PRESET_SOCKET_1_LINK_3_LABEL                              0x7080
  #define SETUP_TXEQ_SEARCH_MASK_SOCKET_0_LINK_0_LABEL                            0x70A1
  #define SETUP_TXEQ_SEARCH_MASK_SOCKET_0_LINK_1_LABEL                            0x70A2
  #define SETUP_TXEQ_SEARCH_MASK_SOCKET_0_LINK_2_LABEL                            0x70A3
  #define SETUP_TXEQ_SEARCH_MASK_SOCKET_0_LINK_3_LABEL                            0x70A4
  #define SETUP_TXEQ_SEARCH_MASK_SOCKET_1_LINK_0_LABEL                            0x70A5
  #define SETUP_TXEQ_SEARCH_MASK_SOCKET_1_LINK_1_LABEL                            0x70A6
  #define SETUP_TXEQ_SEARCH_MASK_SOCKET_1_LINK_2_LABEL                            0x70A7
  #define SETUP_TXEQ_SEARCH_MASK_SOCKET_1_LINK_3_LABEL                            0x70A8
  #define SETUP_DDR_ADDRESSING_OPTIONS_LABEL                                      0x70DD
  #define SETUP_DDR_CONTROLLER_CONFIGURATION_LABEL                                0x70DE
  #define SETUP_DDR_MBIST_OPTIONS_LABEL                                           0x70DF
  #define SETUP_DDR_RAS_LABEL                                                     0x70E0
  #define SETUP_DDR_BUS_CONFIGURATION_LABEL                                       0x70E1
  #define SETUP_DDR_TIMING_CONFIGURATION_LABEL                                    0x70E2
  #define SETUP_DDR_TRAINING_OPTIONS_LABEL                                        0x70E3
  #define SETUP_DDR_SECURITY_LABEL                                                0x70E4
  #define SETUP_DDR_PMIC_CONFIGURATION_LABEL                                      0x70E5
  #define SETUP_DDR_THERMAL_THROTTLING_LABEL                                      0x70E6
  #define SETUP_DDR_MISCELLANEOUS_LABEL                                           0x70E7
  #define SETUP_DDR_POWER_OPTIONS_LABEL                                           0x70E8
  #define SETUP_MEMORY_CHANNEL_DISABLE_LABEL                                      0x70E9
  #define SETUP_REFRESH_MANAGEMENT_RFM_LABEL                                      0x70EA
  #define SETUP_PMU_MEM_BIST_ALGORITHM_LABEL                                      0x7104
  #define SETUP_DATA_EYE_LABEL                                                    0x7105
  #define SETUP_ECC_CONFIGURATION_LABEL                                           0x7110
  #define SETUP_DRAM_SCRUBBERS_LABEL                                              0x7111
  #define SETUP_ECS_CONFIG_LABEL                                                  0x7112
  #define SETUP_P_STATE_0_DRAM_ODT_IMPEDANCE_LABEL                                0x7113
  #define SETUP_P_STATE_1_DRAM_ODT_IMPEDANCE_LABEL                                0x7114
  #define SETUP_DTC_LEGAL_DISCLAIMER_DECLINE_LABEL                                0x7115
  #define SETUP_DTC_LEGAL_DISCLAIMER_ACCEPT_LABEL                                 0x7116
  #define SETUP_SPD_TIMING_LABEL                                                  0x7117
  #define SETUP_NON_SPD_TIMING_LABEL                                              0x7118
  #define SETUP_PERIODIC_PHASE_TRAINING_LABEL                                     0x711A
  #define SETUP_SMU_COMMON_OPTIONS_LABEL                                          0x711D
  #define SETUP_NBIO_RAS_COMMON_OPTIONS_LABEL                                     0x711E
  #define SETUP_PCIE_LABEL                                                        0x711F
  #define SETUP_NBIF_COMMON_OPTIONS_LABEL                                         0x7120
  #define SETUP_IOMMU_SECURITY_LABEL                                              0x7121
  #define SETUP_ENABLE_PORT_BIFURCATION_LABEL                                     0x7122
  #define SETUP_LINK_EQ_PRESET_OPTIONS_LABEL                                      0x7123
  #define SETUP_RCC_DEV0_LABEL                                                    0x712C
  #define SETUP_SOCKET0_SLOT_INFO_OVERRIDE_LABEL                                  0x712D
  #define SETUP_SOCKET1_SLOT_INFO_OVERRIDE_LABEL                                  0x712E
  #define SETUP_P_LINKS_OVERRIDE_LABEL                                            0x712F
  #define SETUP_G_LINKS_OVERRIDE_LABEL                                            0x7130
  #define SETUP_GEN3_LABEL                                                        0x7131
  #define SETUP_GEN4_LABEL                                                        0x7132
  #define SETUP_GEN5_LABEL                                                        0x7133
  #define SETUP_I3C_I2C_CONFIGURATION_OPTIONS_LABEL                               0x7134
  #define SETUP_SATA_CONFIGURATION_OPTIONS_LABEL                                  0x7135
  #define SETUP_USB_CONFIGURATION_OPTIONS_LABEL                                   0x7136
  #define SETUP_AC_POWER_LOSS_OPTIONS_LABEL                                       0x7137
  #define SETUP_UART_CONFIGURATION_OPTIONS_LABEL                                  0x7138
  #define SETUP_FCH_RAS_OPTIONS_LABEL                                             0x7139
  #define SETUP_MISCELLANEOUS_OPTIONS_LABEL                                       0x713A
  #define SETUP_I2C_SDA_TX_HOLD_VALUE_LABEL                                       0x713B
  #define SETUP_I2C_SDA_RX_HOLD_VALUE_LABEL                                       0x713C
  #define SETUP_I3C_SDA_HOLD_VALUE_LABEL                                          0x713D
  #define SETUP_SATA_CONTROLLER_OPTIONS_LABEL                                     0x713F
  #define SETUP_SATA_CONTROLLER_ENABLE_LABEL                                      0x7140
  #define SETUP_SATA_CONTROLLER_ESATA_LABEL                                       0x7141
  #define SETUP_SATA_CONTROLLER_DEVSLP_LABEL                                      0x7142
  #define SETUP_SATA_CONTROLLER_SGPIO_LABEL                                       0x7143
  #define SETUP_SATA0_ESATA_LABEL                                                 0x7144
  #define SETUP_SATA1_ESATA_LABEL                                                 0x7145
  #define SETUP_SATA2_ESATA_LABEL                                                 0x7146
  #define SETUP_SATA3_ESATA_LABEL                                                 0x7147
  #define SETUP_SATA4_ESATA_LABEL                                                 0x7148
  #define SETUP_SATA5_ESATA_LABEL                                                 0x7149
  #define SETUP_SATA6_ESATA_LABEL                                                 0x714A
  #define SETUP_SATA7_ESATA_LABEL                                                 0x714B
  #define SETUP_SOCKET0_DEVSLP_LABEL                                              0x714C
  #define SETUP_SOCKET1_DEVSLP_LABEL                                              0x714D
  #define SETUP_MCM_USB_ENABLE_LABEL                                              0x714E
  #define SETUP_FIRMWARE_ANTI_ROLLBACK_FAR_LABEL                                  0x7150
  #define SETUP_CXL_RAS_LABEL                                                     0x7152

// KEY function ID
  #define KEY_CBS_CPU_SMT_CTRL                                                    0x700B  //CbsCpuSmtCtrl
  #define KEY_CBS_CMN_CPU_SEV_ASID_SPACE_LIMIT                                    0x7010  //CbsCmnCpuSevAsidSpaceLimit
  #define KEY_CBS_CMN_CPU_SCAN_DUMP_DBG_EN                                        0x7011  //CbsCmnCpuScanDumpDbgEn
  #define KEY_CBS_CMN_CPU_OC_MODE                                                 0x7012  //CbsCmnCpuOcMode
  #define KEY_CBS_CMN_CPU_DOWNCORE_MODE                                           0x7014  //CbsCmnCpuDowncoreMode
  #define KEY_CBS_CPU_PST_CUSTOM_P0                                               0x701A  //CbsCpuPstCustomP0
  #define KEY_CBS_CPU_PST0_FID                                                    0x701B  //CbsCpuPst0Fid
  #define KEY_CBS_CPU_PST0_VID                                                    0x701C  //CbsCpuPst0Vid
  #define KEY_CBS_CPU_PST_CUSTOM_P1                                               0x701D  //CbsCpuPstCustomP1
  #define KEY_CBS_CPU_PST1_FID                                                    0x701E  //CbsCpuPst1Fid
  #define KEY_CBS_CPU_PST1_VID                                                    0x701F  //CbsCpuPst1Vid
  #define KEY_CBS_CPU_PST_CUSTOM_P2                                               0x7020  //CbsCpuPstCustomP2
  #define KEY_CBS_CPU_PST2_FID                                                    0x7021  //CbsCpuPst2Fid
  #define KEY_CBS_CPU_PST2_VID                                                    0x7022  //CbsCpuPst2Vid
  #define KEY_CBS_CPU_PST_CUSTOM_P3                                               0x7023  //CbsCpuPstCustomP3
  #define KEY_CBS_CPU_PST3_FID                                                    0x7024  //CbsCpuPst3Fid
  #define KEY_CBS_CPU_PST3_VID                                                    0x7025  //CbsCpuPst3Vid
  #define KEY_CBS_CPU_PST_CUSTOM_P4                                               0x7026  //CbsCpuPstCustomP4
  #define KEY_CBS_CPU_PST4_FID                                                    0x7027  //CbsCpuPst4Fid
  #define KEY_CBS_CPU_PST4_VID                                                    0x7028  //CbsCpuPst4Vid
  #define KEY_CBS_CPU_PST_CUSTOM_P5                                               0x7029  //CbsCpuPstCustomP5
  #define KEY_CBS_CPU_PST5_FID                                                    0x702A  //CbsCpuPst5Fid
  #define KEY_CBS_CPU_PST5_VID                                                    0x702B  //CbsCpuPst5Vid
  #define KEY_CBS_CPU_PST_CUSTOM_P6                                               0x702C  //CbsCpuPstCustomP6
  #define KEY_CBS_CPU_PST6_FID                                                    0x702D  //CbsCpuPst6Fid
  #define KEY_CBS_CPU_PST6_VID                                                    0x702E  //CbsCpuPst6Vid
  #define KEY_CBS_CPU_PST_CUSTOM_P7                                               0x702F  //CbsCpuPstCustomP7
  #define KEY_CBS_CPU_PST7_FID                                                    0x7030  //CbsCpuPst7Fid
  #define KEY_CBS_CPU_PST7_VID                                                    0x7031  //CbsCpuPst7Vid
  #define KEY_CBS_CMN_CPU_CCD0_DOWNCORE_BIT_MAP                                   0x7032  //CbsCmnCpuCcd0DowncoreBitMap
  #define KEY_CBS_CMN_CPU_CCD1_DOWNCORE_BIT_MAP                                   0x7033  //CbsCmnCpuCcd1DowncoreBitMap
  #define KEY_CBS_CMN_CPU_CCD2_DOWNCORE_BIT_MAP                                   0x7034  //CbsCmnCpuCcd2DowncoreBitMap
  #define KEY_CBS_CMN_CPU_CCD3_DOWNCORE_BIT_MAP                                   0x7035  //CbsCmnCpuCcd3DowncoreBitMap
  #define KEY_CBS_CMN_CPU_CCD4_DOWNCORE_BIT_MAP                                   0x7036  //CbsCmnCpuCcd4DowncoreBitMap
  #define KEY_CBS_CMN_CPU_CCD5_DOWNCORE_BIT_MAP                                   0x7037  //CbsCmnCpuCcd5DowncoreBitMap
  #define KEY_CBS_CMN_CPU_CCD6_DOWNCORE_BIT_MAP                                   0x7038  //CbsCmnCpuCcd6DowncoreBitMap
  #define KEY_CBS_CMN_CPU_CCD7_DOWNCORE_BIT_MAP                                   0x7039  //CbsCmnCpuCcd7DowncoreBitMap
  #define KEY_CBS_CMN_CPU_CCD8_DOWNCORE_BIT_MAP                                   0x703A  //CbsCmnCpuCcd8DowncoreBitMap
  #define KEY_CBS_CMN_CPU_CCD9_DOWNCORE_BIT_MAP                                   0x703B  //CbsCmnCpuCcd9DowncoreBitMap
  #define KEY_CBS_CMN_CPU_CCD10_DOWNCORE_BIT_MAP                                  0x703C  //CbsCmnCpuCcd10DowncoreBitMap
  #define KEY_CBS_CMN_CPU_CCD11_DOWNCORE_BIT_MAP                                  0x703D  //CbsCmnCpuCcd11DowncoreBitMap
  #define KEY_CBS_CMN_CPU_CCD12_DOWNCORE_BIT_MAP                                  0x703E  //CbsCmnCpuCcd12DowncoreBitMap
  #define KEY_CBS_CMN_CPU_CCD13_DOWNCORE_BIT_MAP                                  0x703F  //CbsCmnCpuCcd13DowncoreBitMap
  #define KEY_CBS_CMN_CPU_CCD14_DOWNCORE_BIT_MAP                                  0x7040  //CbsCmnCpuCcd14DowncoreBitMap
  #define KEY_CBS_CMN_CPU_CCD15_DOWNCORE_BIT_MAP                                  0x7041  //CbsCmnCpuCcd15DowncoreBitMap
  #define KEY_CBS_CPU_CCD_CTRL                                                    0x7042  //CbsCpuCcdCtrl
  #define KEY_CBS_CPU_CORE_CTRL                                                   0x7045  //CbsCpuCoreCtrl
  #define KEY_CBS_DBG_CPU_GEN_CPU_WDT                                             0x7048  //CbsDbgCpuGenCpuWdt
  #define KEY_CBS_DF_DBG_NUM_PCI_SEGMENTS                                         0x704E  //CbsDfDbgNumPciSegments
  #define KEY_CBS_DF_CMN_DRAM_NPS                                                 0x7051  //CbsDfCmnDramNps
  #define KEY_CBS_DF_CMN_MEM_INTLV                                                0x7054  //CbsDfCmnMemIntlv
  #define KEY_CBS_DF_CMN_CXL_MEM_INTLV                                            0x7055  //CbsDfCmnCxlMemIntlv
  #define KEY_CBS_DF_CMN_CC6_ALLOCATION_SCHEME                                    0x7056  //CbsDfCmnCc6AllocationScheme
  #define KEY_CBS_DF_CMN4_LINK_MAX_XGMI_SPEED                                     0x7059  //CbsDfCmn4LinkMaxXgmiSpeed
  #define KEY_CBS_DF_CMN3_LINK_MAX_XGMI_SPEED                                     0x705C  //CbsDfCmn3LinkMaxXgmiSpeed
  #define KEY_CBS_DF_XGMI_PRESET_CONTROL                                          0x705F  //CbsDfXgmiPresetControl
  #define KEY_CBS_DF_XGMI_CMN1P11                                                 0x706A  //CbsDfXgmiCmn1P11
  #define KEY_CBS_DF_XGMI_CN_P11                                                  0x706B  //CbsDfXgmiCnP11
  #define KEY_CBS_DF_XGMI_CNP1P11                                                 0x706C  //CbsDfXgmiCnp1P11
  #define KEY_CBS_DF_XGMI_CMN1P12                                                 0x706D  //CbsDfXgmiCmn1P12
  #define KEY_CBS_DF_XGMI_CN_P12                                                  0x706E  //CbsDfXgmiCnP12
  #define KEY_CBS_DF_XGMI_CNP1P12                                                 0x706F  //CbsDfXgmiCnp1P12
  #define KEY_CBS_DF_XGMI_CMN1P13                                                 0x7070  //CbsDfXgmiCmn1P13
  #define KEY_CBS_DF_XGMI_CN_P13                                                  0x7071  //CbsDfXgmiCnP13
  #define KEY_CBS_DF_XGMI_CNP1P13                                                 0x7072  //CbsDfXgmiCnp1P13
  #define KEY_CBS_DF_XGMI_CMN1P14                                                 0x7073  //CbsDfXgmiCmn1P14
  #define KEY_CBS_DF_XGMI_CN_P14                                                  0x7074  //CbsDfXgmiCnP14
  #define KEY_CBS_DF_XGMI_CNP1P14                                                 0x7075  //CbsDfXgmiCnp1P14
  #define KEY_CBS_DF_XGMI_CMN1P15                                                 0x7076  //CbsDfXgmiCmn1P15
  #define KEY_CBS_DF_XGMI_CN_P15                                                  0x7077  //CbsDfXgmiCnP15
  #define KEY_CBS_DF_XGMI_CNP1P15                                                 0x7078  //CbsDfXgmiCnp1P15
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S0L0P0                                      0x7081  //CbsDfXgmiInitPresetS0L0P0
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S0L0P1                                      0x7082  //CbsDfXgmiInitPresetS0L0P1
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S0L0P2                                      0x7083  //CbsDfXgmiInitPresetS0L0P2
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S0L0P3                                      0x7084  //CbsDfXgmiInitPresetS0L0P3
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S0L1P0                                      0x7085  //CbsDfXgmiInitPresetS0L1P0
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S0L1P1                                      0x7086  //CbsDfXgmiInitPresetS0L1P1
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S0L1P2                                      0x7087  //CbsDfXgmiInitPresetS0L1P2
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S0L1P3                                      0x7088  //CbsDfXgmiInitPresetS0L1P3
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S0L2P0                                      0x7089  //CbsDfXgmiInitPresetS0L2P0
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S0L2P1                                      0x708A  //CbsDfXgmiInitPresetS0L2P1
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S0L2P2                                      0x708B  //CbsDfXgmiInitPresetS0L2P2
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S0L2P3                                      0x708C  //CbsDfXgmiInitPresetS0L2P3
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S0L3P0                                      0x708D  //CbsDfXgmiInitPresetS0L3P0
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S0L3P1                                      0x708E  //CbsDfXgmiInitPresetS0L3P1
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S0L3P2                                      0x708F  //CbsDfXgmiInitPresetS0L3P2
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S0L3P3                                      0x7090  //CbsDfXgmiInitPresetS0L3P3
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S1L0P0                                      0x7091  //CbsDfXgmiInitPresetS1L0P0
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S1L0P1                                      0x7092  //CbsDfXgmiInitPresetS1L0P1
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S1L0P2                                      0x7093  //CbsDfXgmiInitPresetS1L0P2
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S1L0P3                                      0x7094  //CbsDfXgmiInitPresetS1L0P3
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S1L1P0                                      0x7095  //CbsDfXgmiInitPresetS1L1P0
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S1L1P1                                      0x7096  //CbsDfXgmiInitPresetS1L1P1
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S1L1P2                                      0x7097  //CbsDfXgmiInitPresetS1L1P2
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S1L1P3                                      0x7098  //CbsDfXgmiInitPresetS1L1P3
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S1L2P0                                      0x7099  //CbsDfXgmiInitPresetS1L2P0
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S1L2P1                                      0x709A  //CbsDfXgmiInitPresetS1L2P1
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S1L2P2                                      0x709B  //CbsDfXgmiInitPresetS1L2P2
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S1L2P3                                      0x709C  //CbsDfXgmiInitPresetS1L2P3
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S1L3P0                                      0x709D  //CbsDfXgmiInitPresetS1L3P0
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S1L3P1                                      0x709E  //CbsDfXgmiInitPresetS1L3P1
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S1L3P2                                      0x709F  //CbsDfXgmiInitPresetS1L3P2
  #define KEY_CBS_DF_XGMI_INIT_PRESET_S1L3P3                                      0x70A0  //CbsDfXgmiInitPresetS1L3P3
  #define KEY_CBS_DF_XGMI_TXEQ_S0L0P0                                             0x70A9  //CbsDfXgmiTxeqS0L0P0
  #define KEY_CBS_DF_XGMI_TXEQ_S0L0P1                                             0x70AA  //CbsDfXgmiTxeqS0L0P1
  #define KEY_CBS_DF_XGMI_TXEQ_S0L0P2                                             0x70AB  //CbsDfXgmiTxeqS0L0P2
  #define KEY_CBS_DF_XGMI_TXEQ_S0L0P3                                             0x70AC  //CbsDfXgmiTxeqS0L0P3
  #define KEY_CBS_DF_XGMI_TXEQ_S0L1P0                                             0x70AD  //CbsDfXgmiTxeqS0L1P0
  #define KEY_CBS_DF_XGMI_TXEQ_S0L1P1                                             0x70AE  //CbsDfXgmiTxeqS0L1P1
  #define KEY_CBS_DF_XGMI_TXEQ_S0L1P2                                             0x70AF  //CbsDfXgmiTxeqS0L1P2
  #define KEY_CBS_DF_XGMI_TXEQ_S0L1P3                                             0x70B0  //CbsDfXgmiTxeqS0L1P3
  #define KEY_CBS_DF_XGMI_TXEQ_S0L2P0                                             0x70B1  //CbsDfXgmiTxeqS0L2P0
  #define KEY_CBS_DF_XGMI_TXEQ_S0L2P1                                             0x70B2  //CbsDfXgmiTxeqS0L2P1
  #define KEY_CBS_DF_XGMI_TXEQ_S0L2P2                                             0x70B3  //CbsDfXgmiTxeqS0L2P2
  #define KEY_CBS_DF_XGMI_TXEQ_S0L2P3                                             0x70B4  //CbsDfXgmiTxeqS0L2P3
  #define KEY_CBS_DF_XGMI_TXEQ_S0L3P0                                             0x70B5  //CbsDfXgmiTxeqS0L3P0
  #define KEY_CBS_DF_XGMI_TXEQ_S0L3P1                                             0x70B6  //CbsDfXgmiTxeqS0L3P1
  #define KEY_CBS_DF_XGMI_TXEQ_S0L3P2                                             0x70B7  //CbsDfXgmiTxeqS0L3P2
  #define KEY_CBS_DF_XGMI_TXEQ_S0L3P3                                             0x70B8  //CbsDfXgmiTxeqS0L3P3
  #define KEY_CBS_DF_XGMI_TXEQ_S1L0P0                                             0x70B9  //CbsDfXgmiTxeqS1L0P0
  #define KEY_CBS_DF_XGMI_TXEQ_S1L0P1                                             0x70BA  //CbsDfXgmiTxeqS1L0P1
  #define KEY_CBS_DF_XGMI_TXEQ_S1L0P2                                             0x70BB  //CbsDfXgmiTxeqS1L0P2
  #define KEY_CBS_DF_XGMI_TXEQ_S1L0P3                                             0x70BC  //CbsDfXgmiTxeqS1L0P3
  #define KEY_CBS_DF_XGMI_TXEQ_S1L1P0                                             0x70BD  //CbsDfXgmiTxeqS1L1P0
  #define KEY_CBS_DF_XGMI_TXEQ_S1L1P1                                             0x70BE  //CbsDfXgmiTxeqS1L1P1
  #define KEY_CBS_DF_XGMI_TXEQ_S1L1P2                                             0x70BF  //CbsDfXgmiTxeqS1L1P2
  #define KEY_CBS_DF_XGMI_TXEQ_S1L1P3                                             0x70C0  //CbsDfXgmiTxeqS1L1P3
  #define KEY_CBS_DF_XGMI_TXEQ_S1L2P0                                             0x70C1  //CbsDfXgmiTxeqS1L2P0
  #define KEY_CBS_DF_XGMI_TXEQ_S1L2P1                                             0x70C2  //CbsDfXgmiTxeqS1L2P1
  #define KEY_CBS_DF_XGMI_TXEQ_S1L2P2                                             0x70C3  //CbsDfXgmiTxeqS1L2P2
  #define KEY_CBS_DF_XGMI_TXEQ_S1L2P3                                             0x70C4  //CbsDfXgmiTxeqS1L2P3
  #define KEY_CBS_DF_XGMI_TXEQ_S1L3P0                                             0x70C5  //CbsDfXgmiTxeqS1L3P0
  #define KEY_CBS_DF_XGMI_TXEQ_S1L3P1                                             0x70C6  //CbsDfXgmiTxeqS1L3P1
  #define KEY_CBS_DF_XGMI_TXEQ_S1L3P2                                             0x70C7  //CbsDfXgmiTxeqS1L3P2
  #define KEY_CBS_DF_XGMI_TXEQ_S1L3P3                                             0x70C8  //CbsDfXgmiTxeqS1L3P3
  #define KEY_CBS_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK0                        0x70C9  //CbsDfXgmiAcDcCoupledLinkSocket0Link0
  #define KEY_CBS_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK1                        0x70CA  //CbsDfXgmiAcDcCoupledLinkSocket0Link1
  #define KEY_CBS_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK2                        0x70CB  //CbsDfXgmiAcDcCoupledLinkSocket0Link2
  #define KEY_CBS_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET0_LINK3                        0x70CC  //CbsDfXgmiAcDcCoupledLinkSocket0Link3
  #define KEY_CBS_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK0                        0x70CD  //CbsDfXgmiAcDcCoupledLinkSocket1Link0
  #define KEY_CBS_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK1                        0x70CE  //CbsDfXgmiAcDcCoupledLinkSocket1Link1
  #define KEY_CBS_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK2                        0x70CF  //CbsDfXgmiAcDcCoupledLinkSocket1Link2
  #define KEY_CBS_DF_XGMI_AC_DC_COUPLED_LINK_SOCKET1_LINK3                        0x70D0  //CbsDfXgmiAcDcCoupledLinkSocket1Link3
  #define KEY_CBS_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK0                              0x70D1  //CbsDfXgmiChannelTypeSocket0Link0
  #define KEY_CBS_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK1                              0x70D2  //CbsDfXgmiChannelTypeSocket0Link1
  #define KEY_CBS_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK2                              0x70D3  //CbsDfXgmiChannelTypeSocket0Link2
  #define KEY_CBS_DF_XGMI_CHANNEL_TYPE_SOCKET0_LINK3                              0x70D4  //CbsDfXgmiChannelTypeSocket0Link3
  #define KEY_CBS_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK0                              0x70D5  //CbsDfXgmiChannelTypeSocket1Link0
  #define KEY_CBS_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK1                              0x70D6  //CbsDfXgmiChannelTypeSocket1Link1
  #define KEY_CBS_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK2                              0x70D7  //CbsDfXgmiChannelTypeSocket1Link2
  #define KEY_CBS_DF_XGMI_CHANNEL_TYPE_SOCKET1_LINK3                              0x70D8  //CbsDfXgmiChannelTypeSocket1Link3
  #define KEY_CBS_DF_CMN_PF_ORGANIZATION                                          0x70D9  //CbsDfCmnPfOrganization
  #define KEY_CBS_DF_CMN_MEM_INTLV_PAGE_SIZE                                      0x70DC  //CbsDfCmnMemIntlvPageSize
  #define KEY_CBS_CMN_MEM_CHANNEL_DISABLE_BITMASK_DDR                             0x70EB  //CbsCmnMemChannelDisableBitmaskDdr
  #define KEY_CBS_CMN_MEM_SOCKET0_CHANNEL0_DDR                                    0x70EC  //CbsCmnMemSocket0Channel0Ddr
  #define KEY_CBS_CMN_MEM_SOCKET0_CHANNEL1_DDR                                    0x70ED  //CbsCmnMemSocket0Channel1Ddr
  #define KEY_CBS_CMN_MEM_SOCKET0_CHANNEL2_DDR                                    0x70EE  //CbsCmnMemSocket0Channel2Ddr
  #define KEY_CBS_CMN_MEM_SOCKET0_CHANNEL3_DDR                                    0x70EF  //CbsCmnMemSocket0Channel3Ddr
  #define KEY_CBS_CMN_MEM_SOCKET0_CHANNEL4_DDR                                    0x70F0  //CbsCmnMemSocket0Channel4Ddr
  #define KEY_CBS_CMN_MEM_SOCKET0_CHANNEL5_DDR                                    0x70F1  //CbsCmnMemSocket0Channel5Ddr
  #define KEY_CBS_CMN_MEM_SOCKET0_CHANNEL6_DDR                                    0x70F2  //CbsCmnMemSocket0Channel6Ddr
  #define KEY_CBS_CMN_MEM_SOCKET0_CHANNEL7_DDR                                    0x70F3  //CbsCmnMemSocket0Channel7Ddr
  #define KEY_CBS_CMN_MEM_SOCKET0_CHANNEL8_DDR                                    0x70F4  //CbsCmnMemSocket0Channel8Ddr
  #define KEY_CBS_CMN_MEM_SOCKET0_CHANNEL9_DDR                                    0x70F5  //CbsCmnMemSocket0Channel9Ddr
  #define KEY_CBS_CMN_MEM_SOCKET0_CHANNEL10_DDR                                   0x70F6  //CbsCmnMemSocket0Channel10Ddr
  #define KEY_CBS_CMN_MEM_SOCKET0_CHANNEL11_DDR                                   0x70F7  //CbsCmnMemSocket0Channel11Ddr
  #define KEY_CBS_CMN_MEM_SOCKET1_CHANNEL0_DDR                                    0x70F8  //CbsCmnMemSocket1Channel0Ddr
  #define KEY_CBS_CMN_MEM_SOCKET1_CHANNEL1_DDR                                    0x70F9  //CbsCmnMemSocket1Channel1Ddr
  #define KEY_CBS_CMN_MEM_SOCKET1_CHANNEL2_DDR                                    0x70FA  //CbsCmnMemSocket1Channel2Ddr
  #define KEY_CBS_CMN_MEM_SOCKET1_CHANNEL3_DDR                                    0x70FB  //CbsCmnMemSocket1Channel3Ddr
  #define KEY_CBS_CMN_MEM_SOCKET1_CHANNEL4_DDR                                    0x70FC  //CbsCmnMemSocket1Channel4Ddr
  #define KEY_CBS_CMN_MEM_SOCKET1_CHANNEL5_DDR                                    0x70FD  //CbsCmnMemSocket1Channel5Ddr
  #define KEY_CBS_CMN_MEM_SOCKET1_CHANNEL6_DDR                                    0x70FE  //CbsCmnMemSocket1Channel6Ddr
  #define KEY_CBS_CMN_MEM_SOCKET1_CHANNEL7_DDR                                    0x70FF  //CbsCmnMemSocket1Channel7Ddr
  #define KEY_CBS_CMN_MEM_SOCKET1_CHANNEL8_DDR                                    0x7100  //CbsCmnMemSocket1Channel8Ddr
  #define KEY_CBS_CMN_MEM_SOCKET1_CHANNEL9_DDR                                    0x7101  //CbsCmnMemSocket1Channel9Ddr
  #define KEY_CBS_CMN_MEM_SOCKET1_CHANNEL10_DDR                                   0x7102  //CbsCmnMemSocket1Channel10Ddr
  #define KEY_CBS_CMN_MEM_SOCKET1_CHANNEL11_DDR                                   0x7103  //CbsCmnMemSocket1Channel11Ddr
  #define KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM_BIT_MASK_DDR                         0x7106  //CbsCmnMemPmuBistAlgorithmBitMaskDdr
  #define KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM1                                     0x7107  //CbsCmnMemPmuBistAlgorithm1
  #define KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM2                                     0x7108  //CbsCmnMemPmuBistAlgorithm2
  #define KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM3                                     0x7109  //CbsCmnMemPmuBistAlgorithm3
  #define KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM4                                     0x710A  //CbsCmnMemPmuBistAlgorithm4
  #define KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM5                                     0x710B  //CbsCmnMemPmuBistAlgorithm5
  #define KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM6                                     0x710C  //CbsCmnMemPmuBistAlgorithm6
  #define KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM7                                     0x710D  //CbsCmnMemPmuBistAlgorithm7
  #define KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM8                                     0x710E  //CbsCmnMemPmuBistAlgorithm8
  #define KEY_CBS_CMN_MEM_PMU_BIST_ALGORITHM9                                     0x710F  //CbsCmnMemPmuBistAlgorithm9
  #define KEY_CBS_CMN_MEM_TIMING_TCL_DDR                                          0x7119  //CbsCmnMemTimingTclDdr
  #define KEY_CBS_CMN_MEM_TSME_ENABLE_DDR                                         0x711B  //CbsCmnMemTsmeEnableDdr
  #define KEY_CBS_CMN_MEM_DATA_SCRAMBLE                                           0x711C  //CbsCmnMemDataScramble
  #define KEY_CBS_CMN_GNB_SMU_DF_CSTATES                                          0x7124  //CbsCmnGnbSmuDfCstates
  #define KEY_CBS_CMN_GNB_SMU_CPPC                                                0x7125  //CbsCmnGnbSmuCppc
  #define KEY_CBS_CMN_GNB_SMU_HSMP_SUPPORT                                        0x7126  //CbsCmnGnbSMUHsmpSupport
  #define KEY_CBS_CMN_X3D_STACK_OVERRIDE                                          0x7127  //CbsCmnX3dStackOverride
  #define KEY_CBS_CMN_GNB_DIAG_MODE                                               0x712A  //CbsCmnGnbDiagMode
  #define KEY_CBS_CMN_GNB_SMU_GMI_FOLDING                                         0x712B  //CbsCmnGnbSmuGmiFolding
  #define KEY_CBS_CMN_FCH_SATA_ENABLE                                             0x713E  //CbsCmnFchSataEnable
  #define KEY_CBS_DBG_FCH_DELAY_SYNCFLOOD                                         0x714F  //CbsDbgFchDelaySyncflood
  #define KEY_CBS_CMN_CXL_ENCRYPTION                                              0x7151  //CbsCmnCxlEncryption

// Label CBS ID
  #define LABEL_CBS_CBS_CPU_SMT_CTRL_START                                        0x700C  //CbsCpuSmtCtrl Start Label
  #define LABEL_CBS_CBS_CPU_SMT_CTRL_END                                          0x700D  //CbsCpuSmtCtrl End Label
  #define LABEL_CBS_CBS_CMN_CPU_DOWNCORE_MODE_START                               0x7015  //CbsCmnCpuDowncoreMode Start Label
  #define LABEL_CBS_CBS_CMN_CPU_DOWNCORE_MODE_END                                 0x7016  //CbsCmnCpuDowncoreMode End Label
  #define LABEL_CBS_CBS_CPU_CCD_CTRL_START                                        0x7043  //CbsCpuCcdCtrl Start Label
  #define LABEL_CBS_CBS_CPU_CCD_CTRL_END                                          0x7044  //CbsCpuCcdCtrl End Label
  #define LABEL_CBS_CBS_CPU_CORE_CTRL_START                                       0x7046  //CbsCpuCoreCtrl Start Label
  #define LABEL_CBS_CBS_CPU_CORE_CTRL_END                                         0x7047  //CbsCpuCoreCtrl End Label
  #define LABEL_CBS_CBS_DF_DBG_NUM_PCI_SEGMENTS_START                             0x704F  //CbsDfDbgNumPciSegments Start Label
  #define LABEL_CBS_CBS_DF_DBG_NUM_PCI_SEGMENTS_END                               0x7050  //CbsDfDbgNumPciSegments End Label
  #define LABEL_CBS_CBS_DF_CMN_DRAM_NPS_START                                     0x7052  //CbsDfCmnDramNps Start Label
  #define LABEL_CBS_CBS_DF_CMN_DRAM_NPS_END                                       0x7053  //CbsDfCmnDramNps End Label
  #define LABEL_CBS_CBS_DF_CMN_CC6_ALLOCATION_SCHEME_START                        0x7057  //CbsDfCmnCc6AllocationScheme Start Label
  #define LABEL_CBS_CBS_DF_CMN_CC6_ALLOCATION_SCHEME_END                          0x7058  //CbsDfCmnCc6AllocationScheme End Label
  #define LABEL_CBS_CBS_DF_CMN4_LINK_MAX_XGMI_SPEED_START                         0x705A  //CbsDfCmn4LinkMaxXgmiSpeed Start Label
  #define LABEL_CBS_CBS_DF_CMN4_LINK_MAX_XGMI_SPEED_END                           0x705B  //CbsDfCmn4LinkMaxXgmiSpeed End Label
  #define LABEL_CBS_CBS_DF_CMN3_LINK_MAX_XGMI_SPEED_START                         0x705D  //CbsDfCmn3LinkMaxXgmiSpeed Start Label
  #define LABEL_CBS_CBS_DF_CMN3_LINK_MAX_XGMI_SPEED_END                           0x705E  //CbsDfCmn3LinkMaxXgmiSpeed End Label
  #define LABEL_CBS_CBS_DF_CMN_PF_ORGANIZATION_START                              0x70DA  //CbsDfCmnPfOrganization Start Label
  #define LABEL_CBS_CBS_DF_CMN_PF_ORGANIZATION_END                                0x70DB  //CbsDfCmnPfOrganization End Label
  #define LABEL_CBS_CBS_CMN_X3D_STACK_OVERRIDE_START                              0x7128  //CbsCmnX3dStackOverride Start Label
  #define LABEL_CBS_CBS_CMN_X3D_STACK_OVERRIDE_END                                0x7129  //CbsCmnX3dStackOverride End Label

#endif // _AMD_CBS_FORM_ID_H_
