#***********************************************************************
#*                                                                     *
#*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
#*                                                                     *
#*      All rights reserved. Subject to AMI licensing agreement.       *
#*                                                                     *
#***********************************************************************
## @file
# Implements the BaseCryptLib and TlsLib using the services of the EDK II Crypto
# Protocol.
#
# Copyright (C) Microsoft Corporation. All rights reserved.
# Copyright (c) 2020, Hewlett Packard Enterprise Development LP. All rights reserved.<BR>
# Copyright (c) 2022, Loongson Technology Corporation Limited. All rights reserved.<BR>
# SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x0001001B
  BASE_NAME                      = DxeCryptLib
  MODULE_UNI_FILE                = CryptLib.uni
  FILE_GUID                      = B38CBDA6-**************-9E8328DE82F6
  VERSION_STRING                 = 1.0
  MODULE_TYPE                    = DXE_DRIVER
  LIBRARY_CLASS                  = BaseCryptLib | DXE_DRIVER UEFI_DRIVER UEFI_APPLICATION DXE_CORE
  LIBRARY_CLASS                  = TlsLib       | DXE_DRIVER UEFI_DRIVER UEFI_APPLICATION
  CONSTRUCTOR                    = DxeCryptLibConstructor

#
# The following information is for reference only and not required by the build tools.
#
# VALID_ARCHITECTURES = IA32 X64 ARM AARCH64 RISCV64 LOONGARCH64
#

[Packages]
  MdePkg/MdePkg.dec
  CryptoPkg/CryptoPkg.dec

[LibraryClasses]
  BaseLib
  DebugLib
  UefiBootServicesTableLib
  IntrinsicLib  //APTIOV OVERRIDE - To avoid build error unresolved external symbol memset
  UefiRuntimeServicesTableLib  //APTIOV OVERRIDE - To avoid build error unresolved external symbol time

[Sources]
  DxeCryptLib.c
  CryptLib.c

[Protocols]
  gEdkiiCryptoProtocolGuid  ## CONSUMES

[Depex]
  gEdkiiCryptoProtocolGuid
