TOKEN
    Name  = "BaseCryptLibOnProtocolPpi_SUPPORT"
    Value  = "1"
    Help  = "Switch for Enabling BaseCryptLibOnProtocolPpi support in the project"
    TokenType = Boolean
    Master = Yes
    TargetMAK = Yes
    Token = "BaseCryptLibBin" "=" "1"
End

INFComponent
    Name  = "DxeCryptLib"
    File  = "DxeCryptLib.inf"
    Package  = "CryptoPkg"
    Arch  = "IA32 X64 IPF ARM"
    ModuleTypes  = "DXE_DRIVER"
End

INFComponent
    Name  = "PeiCryptLib"
    File  = "PeiCryptLib.inf"
    Package  = "CryptoPkg"
    Arch  = "IA32 X64 IPF ARM"
    ModuleTypes  = "PEIM"
    Token = "BUILD_EDKII_PEI_CRYPT_LIB" "=" "1"
End

INFComponent
    Name  = "SmmCryptLib"
    File  = "SmmCryptLib.inf"
    Package  = "CryptoPkg"
    Arch  = "IA32 X64 IPF ARM"
    ModuleTypes  = "DXE_SMM_DRIVER"
End

INFComponent
    Name  = "StandaloneMmCryptLib"
    File  = "StandaloneMmCryptLib.inf"
    Package  = "CryptoPkg"
    Arch  = "IA32 X64 IPF ARM"
    ModuleTypes  = "MM_STANDALONE"
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
End

LibraryMapping
    Class  = "BaseCryptLib"
    Instance  = "CryptoPkg.DxeCryptLib"
    ModuleTypes = "DXE_DRIVER DXE_CORE UEFI_APPLICATION UEFI_DRIVER"
End

LibraryMapping
    Class  = "BaseCryptLib"
    Instance  = "CryptoPkg.PeiCryptLib"
    ModuleTypes = "PEIM"
    Token = "BUILD_EDKII_PEI_CRYPT_LIB" "=" "1"
End

LibraryMapping
    Class  = "BaseCryptLib"
    Instance  = "CryptoPkg.SmmCryptLib"
    ModuleTypes = "DXE_SMM_DRIVER"
End

LibraryMapping
    Class  = "SmmCryptLib"
    Instance  = "CryptoPkg.StandaloneMmCryptLib"
    ModuleTypes  = "MM_STANDALONE"
    Token = "MM_STANDALONE_SUPPORT" "=" "1"
End

LibraryMapping
    Class  = "TlsLib"
    Instance  = "CryptoPkg.DxeCryptLib"
    ModuleTypes = "DXE_DRIVER UEFI_DRIVER UEFI_APPLICATION"
End

LibraryMapping
    Class  = "TlsLib"
    Instance  = "CryptoPkg.PeiCryptLib"
    ModuleTypes = "PEIM"
End

LibraryMapping
    Class  = "TlsLib"
    Instance  = "CryptoPkg.SmmCryptLib"
    ModuleTypes = "DXE_SMM_DRIVER"
End

LibraryMapping
    Class  = "IntrinsicLib"
    Instance  = "AmiModulePkg.AmiMsftIntrinsicsLib"
    Arch  = "IA32 X64 IPF AARCH64"
End
