//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************


/** @file PlatformVideoInitNullLib.c
    Null Library driver for Platform early video init.

**/
#include <Uefi.h>
#include <PiPei.h>
#include <Library/DebugLib.h>
#include <EarlyConsoleInclude.h>

/**
    Initializes video Parameter with Platform details. 

    @param   DevInfo    Pointer to Video Parameter 

    @retval  EFI_NOT_FOUND
**/
EFI_STATUS 
PlatformVideoParameterInit(
    IN OUT VIDEO_PARAMETERS               *DevInfo
)
{
    DEBUG((DEBUG_ERROR,"%a(): Porting required for Platform Video Init Library!!!", __FUNCTION__));
    return EFI_NOT_FOUND;    
}

/**
    Initializes chipset specific video controller initialization.

    @param   PeiServices    Pointer to PeiServices table 

    @retval  EFI_STATUS
**/
EFI_STATUS 
PlatformVgaEnable (
    IN EFI_PEI_SERVICES         **PeiServices,
    IN UINT16                   bus
)
{
    DEBUG((DEBUG_ERROR,"%a(): Porting required for Platform Video Init Library!!!", __FUNCTION__));
    return EFI_NOT_FOUND;  
}

/**
    Disables legacy VGA decode down to the video controller
 
 **/

VOID
PlatformDisableVgaDecode (
    VOID 
) 
{
    DEBUG((DEBUG_ERROR,"%a(): Porting required for Platform Video Init Library!!!", __FUNCTION__));
    return;  
}

/**
    Clear Secondary and subordinate bus numbers in bridges

    @param   parameters    Pointer to VIDEO_PARAMETERS 

    @retval  Void
**/
VOID 
PlatformClearBusNumbers (
    IN VIDEO_PARAMETERS               *parameters
)
{
    DEBUG((DEBUG_ERROR,"%a(): Porting required for Platform Video Init Library!!!", __FUNCTION__));
    return;  
}

/**
    Find the Vbt file for specified Video controller

    @param   GraphicsPolicyPtr    Pointer to Graphics Policy Pointer
                                  Callee responsibility to allocate and copy policy Data

    @retval  Void
**/
EFI_STATUS 
PlatformGetVbtFile (
    IN VOID               **GraphicsPolicyPtr
)
{
    *GraphicsPolicyPtr = NULL;
    
    DEBUG((DEBUG_ERROR,"%a(): Porting required for Platform Video binary Table!!!", __FUNCTION__));
    
    return EFI_NOT_FOUND;  
}
