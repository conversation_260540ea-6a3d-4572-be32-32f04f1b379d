//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2025, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************

/** @file AmiVideoTextOutLib.h
    Video TextOut library defines and equates

**/

#ifndef __AMI_VIDEO_TEXT_OUT_LIB_H__
#define __AMI_VIDEO_TEXT_OUT_LIB_H__

//Dimension Definitions
#define MAX_COLS                80
#define MAX_ROWS                25
#define BYTES_PER_CHAR          2   // 1 - ASCII value, 1 - Attribute value

#define VIDEO_MEMORY                    0xB8000            // Video Memory
#define TEXT_MODE_FRAME_BUFFER_SIZE     (MAX_ROWS * MAX_COLS * BYTES_PER_CHAR)  // Use memory outside the screen area to hold current 
                                                                                             // cursor location and color.

typedef struct {
    UINT8       Ascii;
    UINT8       Attribute;
} TEXT_MODE_CHAR;

typedef struct {
    UINT32      Cursor;
    UINT8       Color;
} VIDEO_PRIVATE_DATA;

/**
    Clears the screen (writes spaces) to the text mode frame buffer.

    @param VOID

    @retval EFI_SUCCESS
**/

EFI_STATUS
AmiVideoClear (VOID);

/**
    Scroll the screen up by one line

    @param None

    @retval EFI_SUCCESS
**/

EFI_STATUS 
AmiVideoScrollUp();

/**
    Prints input string

    @param Side - Top/Bottom screen to print on
    @param String - String to print

    @retval EFI_SUCCESS
**/
EFI_STATUS
AmiVideoPrint(
    IN CONST CHAR8      *String
);

/**
    Get the current cursor position (X,Y).

    @param Column - Pointer to Column number
    @param Row - Pointer to Row number    

    @retval EFI_SUCCESS
**/

EFI_STATUS
AmiVideoGetCursorPosition(
    IN UINT8    *Row, 
    IN UINT8    *Column 
);

/**
    Sets the cursor to the row and column specified by x and y.

    @param ColX - Column number
    @param RowY - Row number    

    @retval EFI_SUCCESS - Cursor position set as input X, Y
**/

EFI_STATUS
AmiVideoSetCursorPosition(
    IN UINT8        Column, 
    IN UINT8        Row
);

/**
    Function to print the input string and scroll the line if needed

    @param String - String to print

    @retval EFI_SUCCESS
**/
EFI_STATUS 
AmiVideoPrintWorker(
    IN CONST CHAR8          *String
);

/**
    Get the current cursor position (X,Y).

    @param Column - Pointer to Column number
    @param Row - Pointer to Row number    

    @retval EFI_SUCCESS
**/
EFI_STATUS
AmiVideoGetCursorPosition(
    IN UINT8    *Col, 
    IN UINT8    *Row 
);

/**
    Sets the current screen foreground/background color to the specified value

    @param Color - Color attributes to be set
           Foreground color is specified by bits 2:0,
           Background color is specified by bits 6:4
           Bit 3 increases the intensity of the color selected.
           Bit 7 causes the text to blink.
 
       For example, intense white text on a blue background would be specified 
       as ((EFI_BACKGROUND_BLUE << 4) | EFI_WHITE).

    @retval EFI_SUCCESS
**/

EFI_STATUS
AmiVideoSetColor(
    IN UINT8            Color
);
#endif
