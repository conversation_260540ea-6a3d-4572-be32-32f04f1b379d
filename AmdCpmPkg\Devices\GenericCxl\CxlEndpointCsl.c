/** @file

Cxl Component State (Dump) Log.

**/
/******************************************************************************
 * Copyright (C) 2022-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 ***************************************************************************/

#include "CxlEndpointCsl.h"
#include <Library/MemoryAllocationLib.h>
#include <Library/BaseLib.h>
#include <Uefi/UefiBaseType.h>

#define COMPONENT_STATE_DUMP_LOG_IDENTIFIER  "b3fab4cf01b64332943e5e9962f23567\0" // b3fab4cf-01b6-4332-943e-5e9962f23567

#define GET_SUPPORTED_LOGS_OPCODE            0x0400
#define GET_LOG_OPCODE                       0x0401

/**
 * @brief Dumps the Component State Dump Log to serial output
 *
 * @param[in] Csdl AMD_CXL_CSL Component State Dump Log
 * @return STATIC
 */
STATIC
VOID
CxlCslDump (
  IN AMD_CXL_CSL  *Csdl
)
{
  UINT32  Index;
  UINT32  Count;
  UINT32  DumpDataLength = 0;

  DEBUG ((DEBUG_INFO, "<---------- Component State Dump Log ---------->\n"));
  DEBUG ((DEBUG_INFO, "  Component State Dump Data Length = 0x%06x (bytes)\n", Csdl->DumpDataLength));
  DEBUG ((DEBUG_INFO, "  Auto Populate Trigger Count      = 0x%04x\n", Csdl->AutoPopTriggerCount));
  DEBUG ((DEBUG_INFO, "  Event Log                        = 0x%04x\n", Csdl->EventLog));
  DEBUG ((DEBUG_INFO, "  Associated Event Record Handle   = 0x%06x\n", Csdl->EventRecordHandle));
  DEBUG ((DEBUG_INFO, "  Timestamp                        = 0x%06x\n", Csdl->Timestamp));
  DEBUG ((DEBUG_INFO, "  Dump Data Format UUID            = 0x%06x\n", Csdl->FormatUuid));
  DEBUG ((DEBUG_INFO, "  Auto Populate Data Bit           = 0x%x\n", Csdl->Flags));


  if (Csdl->DumpDataLength != 0) {
    if (Csdl->DumpDataLength <= EFI_PAGES_TO_SIZE (1)) {
      DumpDataLength = Csdl->DumpDataLength;
    } else {
       DumpDataLength = EFI_PAGES_TO_SIZE (1);
    }
    Count = 0;
    DEBUG ((DEBUG_INFO, "<-------  Component State Dump Data -------->\n"));
    for (Index = 0; Index < DumpDataLength; ) {
      DEBUG ((DEBUG_INFO, "%02x ", *((UINT8 *) Csdl->StateDumpData + Index)));
      Index += 1;
      if (++Count >= 16) {
        DEBUG ((DEBUG_INFO, "\n"));
        Count = 0;
      }
    }
    if (Count < 16) {
      DEBUG ((DEBUG_INFO, "\n"));
    }
    DEBUG ((DEBUG_INFO, "<------ Component State Dump Data End  ------>\n"));
  }
  DEBUG ((DEBUG_INFO, "<-------- Component State Dump Log End -------->\n"));
}

/**
 * @brief Retrieve the Component State Dump Log
 *
 * @param[in] Endpoint               CXL_ENDPOINT instance
 * @param[in] ComponentStateDumpLog  AMD_CXL_CSL instance
 */
EFI_STATUS
GetComponentStateDumpLog (
  IN  CXL_ENDPOINT *Endpoint,
  OUT AMD_CXL_CSL  *ComponentStateDumpLog
)
{
  EFI_STATUS             Status;
  PCI_ADDR               EndpointPciAddr;
  AMD_CXL_CCI_CMD        CxlCciGetSupportedLogs;
  AMD_CXL_CCI_CMD        CxlCciGetLog;
  GET_LOG_INPUT_PAYLOAD  Payload;
  SUPPORTED_LOG_ENTRY    *SupportedLogEntries;
  UINT32                 Index;
  UINT16                 NumSupportedLogEntries;
  UINT8                  ComponentStateDumpLogId[16];
  CONST CHAR8            *String;
  ERROR_LOG_PARAMS       CxlErrorLog;
  AMD_CXL_ERROR_LOG_PROTOCOL *AmdCxlErrorLog = NULL;
  UINT32                 MboxPayload;
  UINT32                 Offset;
  BOOLEAN                CslDump = TRUE;
  BOOLEAN                PrintLog = TRUE;
  UINT8                  Count = 0;

  DEBUG((DEBUG_INFO, "%a - ENTRY\n", __FUNCTION__));

  Status = gBS->LocateProtocol (&gAmdCxlErrorLogProtocolGuid, 
                                NULL, 
                                (VOID **)&AmdCxlErrorLog
                                );
  if (EFI_ERROR (Status)) {
    DEBUG((DEBUG_ERROR, "%a: Failed to locate AmdCxlErrorLogProtocol! Status = %r\n", __FUNCTION__, Status));
  }

  EndpointPciAddr.Address.Bus = Endpoint->PciLocation.AsBits.Bus;
  EndpointPciAddr.Address.Device = Endpoint->PciLocation.AsBits.Device;
  EndpointPciAddr.Address.Function = Endpoint->PciLocation.AsBits.Function;
  EndpointPciAddr.Address.Segment = Endpoint->PciLocation.AsBits.Segment;

  // Get list of supported logs
  CxlCciGetSupportedLogs.Opcode = GET_SUPPORTED_LOGS_OPCODE;
  CxlCciGetSupportedLogs.InputPayloadLen = 0;
  CxlCciGetSupportedLogs.InputPayload = NULL;
  Status = CxlCciSendCommand (EndpointPciAddr, &CxlCciGetSupportedLogs, TRUE);
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Error: CxlCciGetSupportedLogs failed! (Status = %r).\n", Status));
    if (AmdCxlErrorLog != NULL) {
      CxlErrorLog.ErrorClass = AMD_ERROR;
      CxlErrorLog.ErrorInfo = CXL_ERROR_LOG_ID | CXL_MAILBOX_ERROR | CXL_GET_SUPPORT_LOGS_FAILED;
      CxlErrorLog.DataParam1 = Endpoint->PciLocation.AsBits.Segment;
      CxlErrorLog.DataParam2 = Endpoint->PciLocation.AsBits.Bus;
      CxlErrorLog.DataParam3 = GET_SUPPORTED_LOGS_OPCODE;
      CxlErrorLog.DataParam4 = 0;
      AmdCxlErrorLog->CxlAddErrorLog (AmdCxlErrorLog, &CxlErrorLog);
    }
    goto ON_EXIT;
  }

  NumSupportedLogEntries = *((UINT16 *) CxlCciGetSupportedLogs.OutputPayload);
  String = COMPONENT_STATE_DUMP_LOG_IDENTIFIER;
  Status = AsciiStrHexToBytes (String, 32, ComponentStateDumpLogId, 16);
  // Component state dump log supported?
  SupportedLogEntries = (SUPPORTED_LOG_ENTRY *) (((UINT16 *) CxlCciGetSupportedLogs.OutputPayload) + 4);
  for (Index = 0; Index < NumSupportedLogEntries; Index++){
    if (CompareMem (ComponentStateDumpLogId, SupportedLogEntries->LogId, 16) == 0) {
      goto SUPPORTED;
    }
    SupportedLogEntries++;
  }

  // Component state dump log unsupported
  Status = EFI_UNSUPPORTED;
  goto ON_EXIT;

SUPPORTED:
  if (SupportedLogEntries->LogSize == 0) {
    DEBUG ((DEBUG_INFO, "CXL CSL No log data\n"));
    if (AmdCxlErrorLog != NULL) {
      CxlErrorLog.ErrorClass = AMD_ERROR;
      CxlErrorLog.ErrorInfo = CXL_ERROR_LOG_ID | CXL_MAILBOX_ERROR | CXL_GET_LOG_DATA_FAILED;
      CxlErrorLog.DataParam1 = Endpoint->PciLocation.AsBits.Segment;
      CxlErrorLog.DataParam2 = Endpoint->PciLocation.AsBits.Bus;
      CxlErrorLog.DataParam3 = GET_LOG_OPCODE;
      CxlErrorLog.DataParam4 = 0;
      AmdCxlErrorLog->CxlAddErrorLog (AmdCxlErrorLog, &CxlErrorLog);
    }
    goto ON_EXIT;
  }
  MboxPayload =  Endpoint->CxlCci.PrimaryPayloadSize;

  // Get Component State Dump Log
  CxlCciGetLog.Opcode = GET_LOG_OPCODE;
  CxlCciGetLog.InputPayloadLen = 24;
  CxlCciGetLog.InputPayload = AllocatePages (1);
  if (CxlCciGetLog.InputPayload == NULL) {
    Status = EFI_BAD_BUFFER_SIZE;
    DEBUG ((DEBUG_INFO, "CXL CSL unable to allocate input payload buffer!\n"));
    goto ON_EXIT;
  } 
  ZeroMem (CxlCciGetLog.InputPayload, EFI_PAGES_TO_SIZE (1));
  CopyMem (Payload.LogId, ComponentStateDumpLogId, 16);

  Offset = 0;
  if (SupportedLogEntries->LogSize > MboxPayload) {
    Payload.Length =  MboxPayload;
  } else {
    Payload.Length =  SupportedLogEntries->LogSize;
  }
  do {
    DEBUG ((DEBUG_INFO, "Loop %d Get Log from Offset 0x%x Paylog length %d\n", Count+1, Offset, Payload.Length));
          
    Payload.Offset = Offset;
    CopyMem ((VOID *) CxlCciGetLog.InputPayload, (VOID *) &Payload, (UINTN) CxlCciGetLog.InputPayloadLen);
    Status = CxlCciSendCommand (EndpointPciAddr, &CxlCciGetLog, PrintLog);
    if (EFI_ERROR (Status)) {
      DEBUG ((DEBUG_ERROR, "Error: CxlCciGetLog failed! (Status = %r).\n", Status));
      if (AmdCxlErrorLog != NULL) {
        CxlErrorLog.ErrorClass = AMD_ERROR;
        CxlErrorLog.ErrorInfo = CXL_ERROR_LOG_ID | CXL_MAILBOX_ERROR | CXL_GET_LOG_FAILED;
        CxlErrorLog.DataParam1 = Endpoint->PciLocation.AsBits.Segment;
        CxlErrorLog.DataParam2 = Endpoint->PciLocation.AsBits.Bus;
        CxlErrorLog.DataParam3 = GET_LOG_OPCODE;
        CxlErrorLog.DataParam4 = 0;
        AmdCxlErrorLog->CxlAddErrorLog (AmdCxlErrorLog, &CxlErrorLog);
      }
      goto ON_EXIT;
    }
    if (Offset == 0) {
      // Only dump the first iteration 
      CopyMem ((VOID *) ComponentStateDumpLog, (VOID *) CxlCciGetLog.OutputPayload, 0x40);
      ComponentStateDumpLog->StateDumpData = AllocatePages (1);
      if (ComponentStateDumpLog->StateDumpData == NULL) {
        Status = EFI_BAD_BUFFER_SIZE;
        DEBUG ((DEBUG_INFO, "CXL CSL unable to allocate buffer\n"));
        CslDump = FALSE;
      } else {
        ZeroMem (ComponentStateDumpLog->StateDumpData, EFI_PAGES_TO_SIZE (1));
        if (ComponentStateDumpLog->DumpDataLength <= EFI_PAGES_TO_SIZE (1)) {
          CopyMem ((VOID *) ComponentStateDumpLog->StateDumpData, (VOID *) (((CHAR8 *) CxlCciGetLog.OutputPayload) + 0x40),
                   (UINTN) ComponentStateDumpLog->DumpDataLength);
        } else {
          CopyMem ((VOID *) ComponentStateDumpLog->StateDumpData, (VOID *) (((CHAR8 *) CxlCciGetLog.OutputPayload) + 0x40),
                  (UINTN) EFI_PAGES_TO_SIZE (1));
        }
      }
    }
    Offset += Payload.Length;
    PrintLog = FALSE;
    Count++;
  } while ( Offset < SupportedLogEntries->LogSize); 

  // Dump Component State Dump Log
  if (CslDump) {
    DEBUG ((DEBUG_INFO, "Get Log Completed!\n\n"));
    CxlCslDump (ComponentStateDumpLog);
    FreePages (ComponentStateDumpLog->StateDumpData, 1);
  }
  FreePages (CxlCciGetLog.InputPayload, 1);

ON_EXIT:
  DEBUG((DEBUG_INFO, "%a - EXIT (Status = %r)\n", __FUNCTION__, Status));
  return Status;
}
