## @file
#  Produces the EDK II SMM Crypto Protocol using the library services from
#  BaseCryptLib and TlsLib for Standalone MM.
#
#  Copyright (C) Microsoft Corporation. All rights reserved.
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x0001001B
  PI_SPECIFICATION_VERSION       = 0x00010032
  BASE_NAME                      = CryptoStandaloneMm
  MODULE_UNI_FILE                = Crypto.uni
  FILE_GUID                      = B7946FEA-15EF-43F4-9FF6-0F71E032ECF5
  MODULE_TYPE                    = MM_STANDALONE
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = CryptoStandaloneMmEntry

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  Crypto.c
  CryptoStandaloneMm.c

[Packages]
  MdePkg/MdePkg.dec
  CryptoPkg/CryptoPkg.dec

[LibraryClasses]
  BaseCryptLib
  DebugLib
  MmServicesTableLib
  StandaloneMmDriverEntryPoint
  TlsLib

[Protocols]
  gEdkiiSmmCryptoProtocolGuid  ## PRODUCES

[Pcd]
  gEfiCryptoPkgTokenSpaceGuid.PcdCryptoServiceFamilyEnable  ## CONSUMES

[Depex]
  TRUE
