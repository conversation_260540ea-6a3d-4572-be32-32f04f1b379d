<component>
    name = "KERP3 Override"
    category = ModulePart
    LocalRoot = "OemboardPkg/KERP3/KERP3_Override/"
    RefName = "OemboardPkg.KERP3.BoardOverride"
[files]
"KERP3_Override.sdl"		
"OVERRIDE/AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_DimmInfoSpd.c";"AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_DimmInfoSpd.c"		
"OVERRIDE/AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/ApcbCustomizedBoardDefinitions.h";"AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/ApcbCustomizedBoardDefinitions.h"
"OVERRIDE/AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_PsMaxFreq3DSRDIMMDdr5.c";"AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_PsMaxFreq3DSRDIMMDdr5.c"	
"OVERRIDE/AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_PsMaxFreqRDIMMDdr5.c";"AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_PsMaxFreqRDIMMDdr5.c"
"OVERRIDE/AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1705_Type_PcieConfig.c";"AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1705_Type_PcieConfig.c"
"OVERRIDE/AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1706_Type_EspiInitConfiguration.c";"AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1706_Type_EspiInitConfiguration.c"
"OVERRIDE/AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1706_Type_EspiSioInitConfiguration.c";"AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1706_Type_EspiSioInitConfiguration.c"
"OVERRIDE/AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/SetApcbDataFileList.bat";"AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/SetApcbDataFileList.bat"
"OVERRIDE/AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c";"AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x3000_Type_Token1Byte.c"
"OVERRIDE/AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x3000_Type_TokenBoolean.c";"AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x3000_Type_TokenBoolean.c"
"OVERRIDE/AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_MemDfeSearchScheme.c";"AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_MemDfeSearchScheme.c"
"OVERRIDE/AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_PsoOverride.c";"AgesaPkg/Addendum/Apcb/TurinSp5Rdimm/ApcbDataDefaultRecovery/BreithornCommon/ApcbData_BRH_GID_0x1704_Type_PsoOverride.c"
"OVERRIDE/AmdCpmPkg/Addendum/Oem/Quartz/Dxe/ServerHotplugDxe/ServerHotplugDxe.inf";"AmdCpmPkg/Addendum/Oem/Quartz/Dxe/ServerHotplugDxe/ServerHotplugDxe.inf"	
"OVERRIDE/AmdCpmPkg/Addendum/Oem/Quartz/Dxe/ServerHotplugDxe/ServerHotplugDxe.c";"AmdCpmPkg/Addendum/Oem/Quartz/Dxe/ServerHotplugDxe/ServerHotplugDxe.c"
"OVERRIDE/AmdCpmPkg/Addendum/Oem/Quartz/Dxe/AmdPbsSetupDxe/AmdPbsSetupDxe.c";"AmdCpmPkg/Addendum/Oem/Quartz/Dxe/AmdPbsSetupDxe/AmdPbsSetupDxe.c"
"OVERRIDE/AmdCpmPkg/Addendum/Oem/Quartz/Library/AmdPbsConfigLib/AmdPbsDefault.c";"AmdCpmPkg/Addendum/Oem/Quartz/Library/AmdPbsConfigLib/AmdPbsDefault.c"
"OVERRIDE/AmdCpmPkg/Addendum/Oem/Quartz/Pei/AmdCpmOemInitPei/AmdCpmOemInitPeim.c";"AmdCpmPkg/Addendum/Oem/Quartz/Pei/AmdCpmOemInitPei/AmdCpmOemInitPeim.c"	
"OVERRIDE/AgesaModulePkg/Nbio/BRH/DXE/NbioDxeBrh.inf";"AgesaModulePkg/Nbio/BRH/DXE/NbioDxeBrh.inf"	
"OVERRIDE/AgesaModulePkg/Nbio/BRH/DXE/ServerHotplugFeat.c";"AgesaModulePkg/Nbio/BRH/DXE/ServerHotplugFeat.c"	
"OVERRIDE/TurinPkg/Pei/CrbPei.c";"TurinPkg/Pei/CrbPei.c"
"OVERRIDE/AmiChipsetModulePkg/NB/NbSetup/NbSetup.c";"AmiChipsetModulePkg/NB/NbSetup/NbSetup.c"	
"OVERRIDE/AmiChipsetModulePkg/NB/NbSetup/Nb.sd";"AmiChipsetModulePkg/NB/NbSetup/Nb.sd"
"OVERRIDE/OemboardPkg/OemMiscFunction/OemMiscFunction.c";"OemboardPkg/OemMiscFunction/OemMiscFunction.c"
"OVERRIDE/AmiRasWrapperPkg/Library/PlatformRasLib/BRH/RasIpmiLibBrh.c";"AmiRasWrapperPkg/Library/PlatformRasLib/BRH/RasIpmiLibBrh.c"
"OVERRIDE/AmiRasWrapperPkg/Library/PlatformRasLib/BRH/RasIpmiLibBrh.inf";"AmiRasWrapperPkg/Library/PlatformRasLib/BRH/RasIpmiLibBrh.inf"
"OVERRIDE/OemboardPkg/UpdateSmbios/UpdateSmbios.c";"OemboardPkg/UpdateSmbios/UpdateSmbios.c"
"OVERRIDE/AmiModulePkg/FixedBootOrder/FixedBootOrder.c";"AmiModulePkg/FixedBootOrder/FixedBootOrder.c"
"OVERRIDE/AgesaModulePkg/Universal/Smbios/AmdSmbiosDxe.c";"AgesaModulePkg/Universal/Smbios/AmdSmbiosDxe.c"
"OVERRIDE/AmiServerModulePkg/SystemInventoryInfo/SystemInventoryInfoDimm.c";"AmiServerModulePkg/SystemInventoryInfo/SystemInventoryInfoDimm.c"
"OVERRIDE/AgesaModulePkg/Universal/Smbios/AmdSmbiosType16.c";"AgesaModulePkg/Universal/Smbios/AmdSmbiosType16.c"
"OVERRIDE/OemboardPkg/Setup/RedfishSetup/XCompal.uni";"OemboardPkg/Setup/RedfishSetup/XCompal.uni"
<endComponent>
