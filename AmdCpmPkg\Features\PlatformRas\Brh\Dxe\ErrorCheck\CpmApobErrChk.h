/*****************************************************************************
 *
 * Copyright (C) 2018-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#ifndef _CPM_APOB_ERR_CHK_H_
#define _CPM_APOB_ERR_CHK_H_

/*----------------------------------------------------------------------------------------
 *                             M O D U L E S    U S E D
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */

#define TOTAL_ERROR_LOG_BUFFERS  128

typedef union {
  struct {                                     ///< MEMORY_PMU_ERROR_STRUC
    UINT32       Node:8;                       ///< [7:0] Node
    UINT32       Channel:8;                    ///< [15:8] Channel
    UINT32       CSBitmask:4;                  ///< [19:16] Bitmask of failed CS's, corresponding to CS3..0
    UINT32       Dimm0:1;                      ///< [20] DIMM 0 PMU training failure
    UINT32       Dimm1:1;                      ///< [21] DIMM 0 PMU training failure
    UINT32       :10;                          ///< [31:22] Reserved
  } Field;
  UINT32  Value;
} MEMORY_PMU_ERROR_STRUC;

typedef union {
  struct {                                     ///< MEMORY_PMU_TRAIN_EVER_FAIL_ERROR_STRUC
    UINT32       Node:8;                       ///< [7:0] Node
    UINT32       RetryCount:8;                 ///< [15:8] Retry Count
    UINT32       ChannelRetryMask:12;          ///< [27:16] Channel retry mask,bit 16=CH0,bit 17=CH1
                                               ///< and so on 1: channel retried
    UINT32       :4;                           ///< [31:28] Reserved
  } Field;
  UINT32  Value;
} MEMORY_PMU_TRAIN_EVER_FAIL_ERROR_STRUC;

typedef union {
  struct {                                     ///< MEMORY_TEST_ERROR_STRUC
    UINT32       Node:8;                       ///< [7:0] Node
    UINT32       Channel:8;                    ///< [15:8] Channel
    UINT32       Dimm0:1;                      ///< [16] DIMM0 error detected
    UINT32       Dimm1:1;                      ///< [17] DIMM1 error detected
    UINT32       :14;                          ///< [31:18] Reserved
  } Field;
  UINT32  Value;
} MEMORY_TEST_ERROR_STRUC;

///
/// DATA_A Of PMIC Error Log (ABL_MEM_ERROR_PMIC_ERROR 0x406B)
///
typedef union {
  struct {                                     ///< DATA A
    UINT32            Socket:8;                ///< [7:0] Socket
    UINT32           Channel:8;                ///< [15:8] Channel
    UINT32              Dimm:1;                ///< [16] DIMM
    UINT32          ChStatus:1;                ///< [17] Channel Status (0 = Disabled, 1 = Enabled)
    UINT32                  :14;               ///< [31:18] Reserved (MBZ)
  } Field;
  UINT32  Value;
} PMIC_ERROR_DATA_A_STRUC;

///
/// DATA_B Of PMIC Error Log (ABL_MEM_ERROR_PMIC_ERROR 0x406B)
///
typedef union {
  struct {                                     ///< DATA B
    UINT32       PMIC_REG_04:8;                ///< [7:0] PMIC Register 0x04
    UINT32       PMIC_REG_05:8;                ///< [15:8] PMIC Register 0x05
    UINT32       PMIC_REG_06:8;                ///< [23:16] PMIC Register 0x06
    UINT32       :8;                           ///< [31:24] Reserved (MBZ)
  } Field;
  UINT32  Value;
} PMIC_ERROR_DATA_B_STRUC;

///
/// DATA_A Of PMIC Real Time Error (ABL_MEM_ERROR_PMIC_REAL_TIME_ERROR  0x406E)
///
typedef union {
  struct {                                     ///< DATA A
    UINT32            Socket:8;                ///< [7:0] Socket
    UINT32           Channel:8;                ///< [15:8] Channel
    UINT32              Dimm:1;                ///< [16] DIMM
    UINT32          ChStatus:1;                ///< [17] Channel Status (0 = Disabled, 1 = Enabled)
    UINT32                  :6;                ///< [23:18] Reserved (MBZ)
    UINT32       PMIC_REG_33:8;                ///< [31:24] PMIC Register 0x33
  } Field;
  UINT32  Value;
} PMIC_REAL_TIME_ERROR_DATA_A_STRUC;

///
/// DATA_B Of PMIC Real Time Error (ABL_MEM_ERROR_PMIC_REAL_TIME_ERROR  0x406E)
///
typedef union {
  struct {                                     ///< DATA B
    UINT32       PMIC_REG_08:8;                ///< [7:0] PMIC Register 0x08
    UINT32       PMIC_REG_09:8;                ///< [15:8] PMIC Register 0x09
    UINT32       PMIC_REG_0A:8;                ///< [23:16] PMIC Register 0x0A
    UINT32       PMIC_REG_0B:8;                ///< [31:24] PMIC Register 0x0B
  } Field;
  UINT32  Value;
} PMIC_REAL_TIME_ERROR_DATA_B_STRUC;

///
/// The Data structure for PMIC BERT Update
///
typedef struct {
     OUT   UINT8 PmicErrorCount;                                              ///< Pmic Error log count.
     OUT   PMIC_ERROR_DATA_A_STRUC PmicErrorData_A[TOTAL_ERROR_LOG_BUFFERS];  ///< DATA_A Array Of PMIC Error log.
     OUT   PMIC_ERROR_DATA_B_STRUC PmicErrorData_B[TOTAL_ERROR_LOG_BUFFERS];  ///< DATA_B Array Of PMIC Error log.
} PMIC_ERROR_LOG_STRUCT;

typedef struct {
     OUT   UINT8 PmicRealTimeErrorCount;                                                  ///< Pmic Real Time Error count.
     OUT   PMIC_REAL_TIME_ERROR_DATA_A_STRUC PmicRTErrorData_A[TOTAL_ERROR_LOG_BUFFERS];  ///< DATA_A Array Of PMIC Real Time Error.
     OUT   PMIC_REAL_TIME_ERROR_DATA_B_STRUC PmicRTErrorData_B[TOTAL_ERROR_LOG_BUFFERS];  ///< DATA_B Array Of PMIC Real Time Error.
} PMIC_REAL_TIME_ERROR_LOG_STRUCT;

///
/// An PMIC Error Structure of 0x406B.
///
typedef struct {
     OUT   UINT8    Count;                                                     ///< Actual Error Log entries count.
     OUT   UINT32   Severity[TOTAL_ERROR_LOG_BUFFERS];                         ///< Error severity
     OUT   PMIC_ERROR_DATA_A_STRUC  PmicErrorDataA[TOTAL_ERROR_LOG_BUFFERS];   ///< Data specific to the Error.
     OUT   PMIC_ERROR_DATA_B_STRUC  PmicErrorDataB[TOTAL_ERROR_LOG_BUFFERS];   ///< Data specific to the Error.
} PMIC_ERROR_0x406B_STRUCT;

///
/// An PMIC Error Structure of 0x406E.
///
typedef struct {
     OUT   UINT8    Count;                                                                       ///< Actual Error Log entries count.
     OUT   UINT32   Severity[TOTAL_ERROR_LOG_BUFFERS];                                           ///< Error severity
     OUT   PMIC_REAL_TIME_ERROR_DATA_A_STRUC  PmicRealTimeErrorDataA[TOTAL_ERROR_LOG_BUFFERS];   ///< Data specific to the Error.
     OUT   PMIC_REAL_TIME_ERROR_DATA_B_STRUC  PmicRealTimeErrorDataB[TOTAL_ERROR_LOG_BUFFERS];   ///< Data specific to the Error.
} PMIC_ERROR_0x406E_STRUCT;

///
/// Parsed PMIC Error Structure.
///
typedef struct {
     OUT   UINT8   Count;
     OUT   UINT32  Severity[TOTAL_ERROR_LOG_BUFFERS];                          ///< Error log severity
     OUT   UINT8   Socket[TOTAL_ERROR_LOG_BUFFERS];                            ///< Socket
     OUT   UINT8   Channel[TOTAL_ERROR_LOG_BUFFERS];                           ///< Channel
     OUT   UINT8   Dimm[TOTAL_ERROR_LOG_BUFFERS];                              ///< Dimm
     OUT   UINT8   ChStatus[TOTAL_ERROR_LOG_BUFFERS];                          ///< Channel Status
     OUT   PMIC_ERROR_DATA_A_STRUC  PmicErrorDataA[TOTAL_ERROR_LOG_BUFFERS];   ///< Data specific to the Error.
     OUT   PMIC_ERROR_DATA_B_STRUC  PmicErrorDataB[TOTAL_ERROR_LOG_BUFFERS];   ///< Data specific to the Error.
     OUT   PMIC_REAL_TIME_ERROR_DATA_A_STRUC  PmicRealTimeErrorDataA[TOTAL_ERROR_LOG_BUFFERS];   ///< Data specific to the Error.
     OUT   PMIC_REAL_TIME_ERROR_DATA_B_STRUC  PmicRealTimeErrorDataB[TOTAL_ERROR_LOG_BUFFERS];   ///< Data specific to the Error.
} PARSED_PMIC_ERROR_DATA_STRUCT;

/*----------------------------------------------------------------------------------------
 *                          E X P O R T E D    F U N C T I O N S
 *----------------------------------------------------------------------------------------
 */

EFI_STATUS
CheckMemTestErr ( VOID );

EFI_STATUS
AmdErrorLogDetection ( VOID );

EFI_STATUS
CheckPmicErr ( VOID );

#endif // _CPM_APOB_ERR_CHK_H_

