//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************

/** @file ProgressErrorCode.c
    Library functions to be used for getting the progress checkpoint , Error CheckPoint
    Progress String and Error String 

**/

#include <ProgressErrorCode.h>

/**

    Return the PostCode value for the Progress Code 
  
    @param  EFI ProgressCode

    @return  PostCode.

**/
UINT8
GetAmiProgressCodeCheckPoint (
    IN EFI_STATUS_CODE_VALUE    ProgressCode 
)
{
    UINTN       CheckPointIndex;

    for (CheckPointIndex = 0; ProgressCheckpointMap[CheckPointIndex].Value != 0; CheckPointIndex++) {
        if (ProgressCheckpointMap[CheckPointIndex].Value == ProgressCode) {
            return ProgressCheckpointMap[CheckPointIndex].PostCode;  
        }
    }
       
    return 0;
}

/**

    Return the PostCode value for the EFI Error Code 
  
    @param  EFI Error Code

    @return  PostCode.

**/
UINT8
GetAmiErrorCodeCheckPoint (
    IN EFI_STATUS_CODE_VALUE    ErrorCode 
)
{
    UINTN       CheckPointIndex;

    for (CheckPointIndex = 0; ErrorCodeCheckpointMap[CheckPointIndex].Value != 0; CheckPointIndex++) {
        if (ErrorCodeCheckpointMap[CheckPointIndex].Value == ErrorCode) {
            return ErrorCodeCheckpointMap[CheckPointIndex].PostCode;  
        }
    }
       
    return 0;
}

/**

    Return the String for the Progress Code 
  
    @param  EFI ProgressCode

    @return  String.

**/
CHAR8*
GetAmiProgressCodeString (
    IN EFI_STATUS_CODE_VALUE    ProgressCode 
)
{
    UINTN       CheckPointIndex;

    for (CheckPointIndex = 0; ProgressCodeVideoStrings[CheckPointIndex].Value != 0; CheckPointIndex++) {
        if (ProgressCodeVideoStrings[CheckPointIndex].Value == ProgressCode) {
            return ProgressCodeVideoStrings[CheckPointIndex].PostString;  
        }
    }
       
    return NULL;
}


/**

    Return the String for the Progress Code 
  
    @param  EFI ProgressCode

    @return  String.

**/
EFI_STATUS
GetAmiErrorCodeString (
    IN EFI_STATUS_CODE_VALUE    ErroCode,
    OUT CHAR8                   **ErrorCodeString,
    OUT CHAR8                   **RootCauseCodeString,
    OUT CHAR8                   **PosibileSolutionString
)
{
    UINTN       CheckPointIndex;

    for (CheckPointIndex = 0; ErrorCodeVideoStrings[CheckPointIndex].Value != 0; CheckPointIndex++) {
        if ( ErrorCodeVideoStrings[CheckPointIndex].Value == ErroCode) {
            *ErrorCodeString = ErrorCodeVideoStrings[CheckPointIndex].ErrorString;  
            *RootCauseCodeString = ErrorCodeVideoStrings[CheckPointIndex].PosibileCauseString;  
            *PosibileSolutionString = ErrorCodeVideoStrings[CheckPointIndex].PosibibleSoutionString;  
            return EFI_SUCCESS;
        }
    }
       
    return EFI_NOT_FOUND;
}


/**

    Checks whether the PostCode is progress code or error code

    @param PostCode
    
    @param ProgressOrErrorCode

**/
EFI_STATUS
CheckProgressOrErrorCode (
    IN  UINT8      PostCode,
    OUT BOOLEAN    *ProgressOrErrorCode
) {
    UINTN       Index;
    for (Index = 0; ProgressCheckpointMap[Index].PostCode != 0; Index++) {
        if (ProgressCheckpointMap[Index].PostCode == PostCode) {
            *ProgressOrErrorCode = TRUE;
            return EFI_SUCCESS;
        }
    }
    
    for (Index = 0; ErrorCodeCheckpointMap[Index].PostCode != 0; Index++) {
            if (ErrorCodeCheckpointMap[Index].PostCode == PostCode) {
                *ProgressOrErrorCode = FALSE;
                return EFI_SUCCESS;
            }
        }
    return EFI_NOT_FOUND;
}
