#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************
[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = AmdNbioPei
  FILE_GUID                      = C1300EE7-4BF6-4164-9FDC-DC3E8EFB7FBD
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0

  ENTRY_POINT                    = AmdNbioPeiEntry

[Sources]
  EntryPoints.c
  BaseInit.c
  NbioTables.c
  NbioTablesMP.c
  IommuInit.c
  IommuTables.c
  DxioInit.c
  MpioTunnel.c
  PcieRemap.c
  HidePorts.c
  PcieHotplug.c
  PcieEarlyTrain.c
  CxlInit.c
  CdmaInit.c
  NtbFeature.c
  DxioCfgPoints.c
  DpcStatusReport.c
  PcieStraps.c
  PkgTypeFixups.c
  PcieComplexData.c
  PcieComplexData.h
  TopologyWA.c
  AmdNbioPei.h
  NbioIohcBrhTbl.c
  NbioNbifInit.c
  SmuInit.c
  SmuServicesPpi.c
  SmuV14Pei.h
  BiosDefaults.h
  IoApicInit.c
  CheckBmcPcie.c
  HarvestFile.c
  IommuFeaturePpi.c
  NbioPerfAnalysis.c
  NbioPerfAnalysis.h

[Packages]
  MdePkg/MdePkg.dec
  AgesaPkg/AgesaPkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleNbioPkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec
  AgesaModulePkg/AgesaModuleCcxPkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec

[LibraryClasses]
  NbioCommonPeiLib
  AmdBaseLib
  IdsLib
  PeimEntryPoint
  PeiServicesLib
  PcieConfigLib
  PcdLib
  HobLib
  NbioHandleLib
  SmnAccessLib
  AmdIdsHookLib
  NbioIdsHookBrhLibPei
  PeiServicesTablePointerLib
  FabricResourceManagerLib
  PcieMiscCommLib
  GnbPciAccLib
  MemoryAllocationLib
  AmdPspApobLib
  PcieComplexDefaultsLib
  GnbLib
  GnbPciLib
  NbioUtilLib
  NbioSmuBrhLib
  BaseMemoryLib
  MpioLib
  MpioInitLib
  SmnTableLib
  AgesaConfigLib

[Guids]
  gGnbPcieHobInfoGuid

[Ppis]
  gAmdNbioBaseServicesBaPpiGuid  #Consumed
  gAmdNbioPcieServicesPpiGuid
  gAmdNbioPcieTrainingStartPpiGuid
  gAmdNbioPcieTrainingDonePpiGuid
  gAmdFabricTopologyServices2PpiGuid
  gAmdFabricTopologyServices2PpiGuid
  gAmdNbioSmuServicesPpiGuid  #CONSUMED
  gAmdNbioPcieComplexPpiGuid
  gAmdMemoryInfoHobPpiGuid
  gAmdSocLogicalIdPpiGuid            #CONSUMED
  gAmdNbioPcieDpcStatusPpiGuid
  gAmdNbioPcieTopologyPpiGuid        #PRODUCED
  gAmdCoreTopologyServicesV2PpiGuid
  gAmdNbioPcieCxlInitStartPpiGuid
  gAmdNbioIommuFeaturePpiGuid        #PRODUCED
  gAmdErrorLogPpiGuid                #CONSUMED

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdApicMode
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdBixbyLinkFound
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAutoSpeedChangeEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioClockGating
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDynamicLanesPowerState
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgGnbIoapicAddress
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIohcNonPCIBarInitDbg
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIohcNonPCIBarInitFastReg
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIohcNonPCIBarInitFastRegCtl
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIohcNonPCIBarInitSmu
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIommuMMIOAddressReservedEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBP0P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBStartLaneP0P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBEndLaneP0P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBLinkSpeedP0P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBModeP0P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBP0P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBStartLaneP0P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBEndLaneP0P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBLinkSpeedP0P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBModeP0P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBP0G0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBStartLaneP0G0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBEndLaneP0G0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBLinkSpeedP0G0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBModeP0G0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBP0G2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBStartLaneP0G2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBEndLaneP0G2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBLinkSpeedP0G2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBModeP0G2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBP1P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBStartLaneP1P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBEndLaneP1P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBLinkSpeedP1P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBModeP1P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBP1P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBStartLaneP1P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBEndLaneP1P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBLinkSpeedP1P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBModeP1P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBP1G0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBStartLaneP1G0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBEndLaneP1G0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBLinkSpeedP1G0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBModeP1G0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBP1G2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBStartLaneP1G2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBEndLaneP1G2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBLinkSpeedP1G2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBModeP1G2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar23SizeP0P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar45SizeP0P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar23SizeP0P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar45SizeP0P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar23SizeP0G0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar45SizeP0G0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar23SizeP0G2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar45SizeP0G2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar23SizeP1P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar45SizeP1P0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar23SizeP1P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar45SizeP1P2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar23SizeP1G0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar45SizeP1G0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar23SizeP1G2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNTBBar45SizeP1G2
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNbifMgcgClkGating
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNbioCTOIgnoreError
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgNbioCTOtoSC
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgPCIeLTREnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgPcieCVTestWA
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgSataPhyTuning
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgSkipPspMessage
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgSstunlClkGating
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgSyshubMgcgClkGating
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGfxAtomicOps
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGppAtomicOps
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdIOHCClkGatingSupport
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdIommuL1ClockGatingEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdIommuL2ClockGatingEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdNbifMgcgHysteresis
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdOpnSpare
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPCIELinkL0Polling
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPCIELinkReceiverDetectionPolling
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPCIELinkResetToTrainingTime
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPTTargetSpeed
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPcieDxioTimingControlEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSyshubMgcgHysteresis
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdTPHCompleterEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieAriForwardingEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdMaster7bitSteeringTag
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlQosNormalLimit
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlQosHighLimit
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlQosTimerLimit
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlQosSchedGap
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlQosVariableGap
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlQosRdspIncMode
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlQosTimerDecNum
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlQosRdspIncNum
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlQosWrrspIncNum

  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDlfCapEnV2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDlfExEnV2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdEdpcEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugHandlingMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugNVMEDefaultMaxPayload
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdLcLoopbackWaitForAllActiveLanes
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioReportEdbErrors
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPcieSubsystemDeviceID
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPcieSubsystemVendorID
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPresenceDetectSelectMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdRxMarginEnabled
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgACSEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAEREnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgIommuSsid
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgIommuSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgNbifF0Ssid
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgNbioSsid
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgNtbSsid
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgNtbccpSsid
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPcieAriSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPcieTbtSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPspccpSsid
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgRxMarginPersistenceMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLinkAspmAllPort
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEarlyBmcLinkDie
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEarlyBmcLinkLaneNum
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEarlyBmcLinkSocket
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEarlyBmcLinkTraining
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEsmEnableAllRootPorts
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEsmTargetSpeed
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieEcrcEnablement
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLinkComplianceModeAllPorts
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdIOHCPgEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgSevSnpSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMaskDpcCapability
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAllowCompliance
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIommuAvicSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabric1TbRemap
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSrisEnableMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSrisSkipInterval
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSrisCfgType
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSrisLowerSkpOsGenSup
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSrisLowerSkpOsRcvSup
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSrisSkpIntervalSel
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSrisAutoDetectMode
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSrisAutodetectFactor
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdPreSilCtrl0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdPreSilCtrl1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdNbioGlobalCgOverride
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPcieSpeedControl
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugSupport

  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPCIeExactMatchEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdDxioPhyValid
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdDxioPhyProgramming
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdDxioSaveRestoreModes
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgMcCapEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgRcvErrEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIoApicMMIOAddressReservedEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgGnbIoapicId
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCfgExposeUnusedPciePorts
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlProtocolErrorReporting
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPcieAerReportMechanism
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSurpriseDownFeature
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdReportErrorsToRcec
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSyncHeaderByPass
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlIoArbWeights
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlCaMemArbWeights
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCnliTokenAdvertisement
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlCorrectableErrorLogging
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlUnCorrectableErrorLogging
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCxlTempGen5AdvertAltPtcl
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlCamemRxOptimization
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCxlTxOptimizeDirectOutEn
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlOnAllPorts
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFabricCdma
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdLcMultAutoSpdChgOnLastRateEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdSteeringTag
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdForceSteering
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdSteeringValue
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdForceWrPh
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdForceWrSteering
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdWrStTagMode
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdForceWrStEntry
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdTphReqrCntl
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgSriovEnDev0F1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAriEnDev0F1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAerEnDev0F1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAcsEnDev0F1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgAtsEnDev0F1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPasidEnDev0F1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPwrEnDev0F1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgRtrEnDev0F1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPriEnDev0F1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIommuL2AtsCntlEn
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgDev0F1PasidEn
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAtomicRoutingEnStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdNbifDev0F1AtomicRequestEn
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdGnbNbifDdrInitEn
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAdvertiseEqToHighRateSupport
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdIohcClkGatingControl

  gEfiAmdAgesaPkgTokenSpaceGuid.PcdRccDev0ExtendedFmtSupported
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdRccDev0E2EPrefix

  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieGen3LaneEqDsTxPreset
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieGen3LaneEqUsTxPreset
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqPresetMask8GtConfig
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqPresetMask8Gt
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqForcePreset8Gt
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieGen4LaneEqDsTxPreset
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieGen4LaneEqUsTxPreset
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqPresetMask16GtConfig
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqPresetMask16Gt
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqForcePreset16Gt
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieGen5LaneEqDsTxPreset
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieGen5LaneEqUsTxPreset
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqPresetMask32GtConfig
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqPresetMask32Gt
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieLaneEqForcePreset32Gt
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdGen5CfgPrecodeRequestEnable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdActiveSltMode
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioAllowCompPass
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugForceSFIStrap
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEnable2SpcGen4
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEnable2SpcGen5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieNonPcieCompliantTrainingFailureSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPcieLoopbackMode
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdLinkDisableAtPowerOffDelay
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSbrBrokenLaneAvoidanceSup
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAutoFullMarginSup
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgDxioTxFIFORdPtrOffset
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugSettleTime
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugSettleTimeMultiplier
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugPDSettle
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugDLPDSyncCount
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotPlugDisBridgeDis
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPresenceDetectSelectMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDataObjectExchange
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHotplugPortReset
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdFchAcpiDeviceInvisibeMapEx
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCXlEarlyLinkTraining
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsSourceVal
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsTranslationalBlocking
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsP2pReq
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsP2pComp
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsUpstreamFwd
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsP2PEgress
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsEnRccDev0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAerEnRccDev0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDlfEnStrap1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPhy16gtStrap1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdMarginEnStrap1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsSourceValStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsTranslationalBlockingStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsUpstreamFwdStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsP2PEgressStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsDirectTranslatedStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsSsidEnStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsP2pReqStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAcsP2pCompStrap5
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAtcEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPriEnPageReq
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPriResetPageReq
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgForcePcieGenSpeed
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTargetPcieGenSpeed
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAllowComplianceForHpPort

  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIommuSocket0Nbio0Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIommuSocket0Nbio1Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIommuSocket0Nbio2Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIommuSocket0Nbio3Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIommuSocket1Nbio0Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIommuSocket1Nbio1Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIommuSocket1Nbio2Enable
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgIommuSocket1Nbio3Enable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdsafeRecoveryBER
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPeriodicCal
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdLimitHpDevicesToPcieBootSpeed
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdPcieIdeCapSup
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgSevTioSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDfCstateEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgHSMPSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdPcieIdlePowerSetting
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgCPPCMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgDiagnosticMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdThrottlerMode

  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS0P0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS0P1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS0P2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS0P3
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS1P0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS1P1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS1P2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdOverrideS1P3
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCxlSyncHeaderByPassCompMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgDxioCplTimeout
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdExposeSFIOOBSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdExposeSFIDRSSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDisableInbandPDSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDfeTapEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdDfeTapCount

[Depex]
  gAmdNbioBRHPeiDepexPpiGuid AND
  gAmdFabricTopologyServices2PpiGuid AND
  gAmdErrorLogPpiGuid AND
  gAmdFchInitPpiGuid

[BuildOptions]
MSFT:*_*_*_CC_FLAGS = /D DISABLE_NEW_DEPRECATED_INTERFACES /W4

