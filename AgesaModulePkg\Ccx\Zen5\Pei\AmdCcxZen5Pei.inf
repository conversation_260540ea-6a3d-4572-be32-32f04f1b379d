#;*****************************************************************************
#;
#; Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = AmdCcxZen5Pei
  FILE_GUID                      = A2AA0A8F-43D5-4B21-A26B-5D02476AD457
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = AmdCcxZen5PeiInit

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  AgesaModulePkg/AgesaCommonModulePkg.dec
  AgesaModulePkg/AgesaModuleCcxPkg.dec
  AgesaModulePkg/AgesaModuleDfPkg.dec
  AgesaModulePkg/AgesaModuleNbioPkg.dec
  AgesaModulePkg/AgesaModulePspPkg.dec
  AgesaModulePkg/AgesaModuleFchPkg.dec
  AgesaModulePkg/AgesaFamily1AModulePkg.dec
  AgesaPkg/AgesaPkg.dec

[LibraryClasses]
  BaseLib
  PcdLib
  PeimEntryPoint
  AmdBaseLib
  IdsLib
  CcxBaseX86Lib
  CcxRolesLib
  CcxResetTablesLib
  CcxApicZen5Lib
  CcxPeiSmmAccessLib
  AmdIdsHookLib
  CcxZen5IdsHookLibPei
  FchBaseLib
  AmdCapsuleLib
  BaseMemoryLib
  AmdErrorLogLib
  MemRestoreLib
  CcxSetMcaLib
  CcxHaltLib
  MemoryAllocationLib
  CcxMicrocodePatchLib
  FabricWdtLib
  HobLib
  IoLib
  CoreTopologyV3Lib
  AmdSocBaseLib

[sources]
  AmdCcxZen5Pei.c
  AmdCcxZen5Pei.h
  CcxZen5BrandString.c
  CcxZen5BrandString.h
  CcxZen5CacheInit.c
  CcxZen5CacheInit.h

[Sources.Ia32]
  Ia32/ApAsm.nasm
  Ia32/ApStartup.nasm
  Ia32/ApStartupFixups.c

[Sources.X64]
  X64/ApAsm.nasm
  X64/ApStartup.nasm
  X64/ApStartupFixups.c

[Guids]
  gApSyncFlagNvVariableGuid
  gEfiAmdAgesaSpecificWarmResetGuid
  gAmdCcxIommuFeatureDataGuid

[Protocols]

[Ppis]
  gAmdFabricTopologyServices2PpiGuid #CONSUMED
  gAmdCcxPeiInitCompletePpiGuid      #PRODUCED
  gAmdCoreTopologyServicesV3PpiGuid  #CONSUMED
  gAmdNbioSmuServicesPpiGuid         #CONSUMED
  gAmdSocZen5ServicesPpiGuid         #CONSUMED
  gEfiPeiReset2PpiGuid               #CONSUMED
  gEfiPeiReadOnlyVariable2PpiGuid    #CONSUMED
  gEfiPeiMpServicesPpiGuid           #CONSUMED
  gPeiSmmAccessPpiGuid               #CONSUMED
  gAmdNbioIommuFeaturePpiGuid        #CONSUMED

[Pcd]
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmee
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdHmkee
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdApicMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuWdtEn
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuWdtTimeout
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCpuWdtSeverity
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspS3WakeFromSmm
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdRedirectForReturnDis
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdOpcacheCtrl
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdStreamingStoresCtrl
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdEnableFSRM
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdEnableERMS
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdEnableRMSS
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdIbsHardwareEn
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdEnableSvmAVIC
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdEnableSvmX2AVIC
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxEnableAvx512
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxDisFstStrErmsb
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCStateMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCStateIoBaseAddress
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCc6Ctrl
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpbMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSoftwarePrefetchMode
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdTransparentErrorLoggingEnable
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdMonMwaitDis
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCcxEnabledFeatures
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdIdsDebugPort
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL1BurstPrefetch
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL1StreamPrefetcher
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL1StridePrefetcher
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL1RegionPrefetcher
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL2StreamPrefetcher
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdL2UpDownPrefetcher
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgIommuSupport
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuSpeculativeStoreMode
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCpuAggrStoreSpec
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuPauseCntSel_1_0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCpuAdaptiveAlloc
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCcxLoadUcodePatch
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdSmallHammerConfiguration
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdCcxErmsbIntermThld
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxErmsbRepo

[Depex]
  gAmdCcxZen5DepexPpiGuid            AND
  gAmdFabricTopologyServices2PpiGuid AND
  gAmdCoreTopologyServicesV3PpiGuid  AND
  gAmdNbioSmuServicesPpiGuid         AND
  gEfiPeiReadOnlyVariable2PpiGuid    AND
  gAmdSocZen5ServicesPpiGuid         AND
  gApobCommonServicePpiGuid
