/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */
//This file is auto generated, do not edit it manually

#include <Base.h>
#include <Library/DebugLib.h>
#include "AmdCbsVariable.h"
#include <Library/PcdLib.h>

VOID
CbsSetAgesaPcds (
  IN       VOID *CbsVariable
  )
{
  CBS_CONFIG *Setup_Config;

  if (CbsVariable == NULL) {
    ASSERT (FALSE);
    return;
  }
  Setup_Config = (CBS_CONFIG *) CbsVariable;
  //Check Display Condition CbsComboFlag=255,CbsCmnCpuEnReqMinFreq=Enable
  if ((Setup_Config->CbsComboFlag == 0xff) && (Setup_Config->CbsCmnCpuEnReqMinFreq == 0x1)) {
    PcdSet16S (PcdAmdCpuReqMinFreq, Setup_Config->CbsCmnCpuReqMinFreq);
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    PcdSetBoolS (PcdAmdCpuReqMinFreqEn, Setup_Config->CbsCmnCpuEnReqMinFreq);
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    PcdSetBoolS (PcdAmdEnableRMSS, Setup_Config->CbsCmnCpuRMSS);
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnCpuGenWA05 != 0xFF) {
      PcdSet8S (PcdAmdRedirectForReturnDis, Setup_Config->CbsCmnCpuGenWA05);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnCpuPfeh != 3) {
      PcdSetBoolS (PcdAmdCcxCfgPFEHEnable, Setup_Config->CbsCmnCpuPfeh);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnCpuCpb != 1) {
      PcdSet8S (PcdAmdCpbMode, Setup_Config->CbsCmnCpuCpb);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnCpuGlobalCstateCtrl != 3) {
      PcdSet8S (PcdAmdCStateMode, Setup_Config->CbsCmnCpuGlobalCstateCtrl);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnGnbPowerSupplyIdleCtrl != 0xf) {
      PcdSet8S (PcdAmdPowerSupplyIdleControl, Setup_Config->CbsCmnGnbPowerSupplyIdleCtrl);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnCpuStreamingStoresCtrl != 0xFF) {
      PcdSet8S (PcdAmdStreamingStoresCtrl, Setup_Config->CbsCmnCpuStreamingStoresCtrl);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnCpuCstC1Ctrl != 3) {
      PcdSetBoolS (PcdAmdAcpiCstC1, Setup_Config->CbsCmnCpuCstC1Ctrl);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    PcdSet16S (PcdAmdAcpiCpuCstC2Latency, Setup_Config->CbsCmnCpuCstC2Latency);
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnCpuMcaErrThreshEn != 0xFF) {
      PcdSetBoolS (PcdMcaErrThreshEn, Setup_Config->CbsCmnCpuMcaErrThreshEn);
    }
  }

  //Check Display Condition CbsComboFlag=255,CbsCmnCpuMcaErrThreshEn=True
  if ((Setup_Config->CbsComboFlag == 0xff) && (Setup_Config->CbsCmnCpuMcaErrThreshEn == 0x1)) {
    PcdSet16S (PcdMcaErrThreshCount, Setup_Config->CbsCmnCpuMcaErrThreshCount);
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    PcdSetBoolS (PcdAmdMcaFruTextEnable, Setup_Config->CbsCmnCpuMcaFruTextEn);
  }

  if (Setup_Config->CbsCmnCpuSmee != 3) {
    PcdSetBoolS (PcdAmdSmee, Setup_Config->CbsCmnCpuSmee);
  }

  //Check Display Condition CbsComboFlag=255,CbsCmnCpuSmee=Enable,CbsPspSevCtrl=Enable
  if ((Setup_Config->CbsComboFlag == 0xff) && (Setup_Config->CbsCmnCpuSmee == 0x1) && (Setup_Config->CbsPspSevCtrl == 0x0)) {
    if (Setup_Config->CbsDbgCpuSnpMemCover != 0xFF) {
      PcdSet8S (PcdAmdSnpMemCover, Setup_Config->CbsDbgCpuSnpMemCover);
    }
  }

  //Check Display Condition CbsDbgCpuSnpMemCover=Custom
  if ((Setup_Config->CbsDbgCpuSnpMemCover == 0x2)) {
    PcdSet32S (PcdAmdSnpMemSize, Setup_Config->CbsDbgCpuSnpMemSizeCover);
  }

  //Check Display Condition CbsComboFlag=255,CbsCmnCpuSmee=Enable,CbsPspSevCtrl=Enable,CbsDbgCpuSnpMemCover=Enabled|CbsDbgCpuSnpMemCover=Custom
  if (((Setup_Config->CbsComboFlag == 0xff) && (Setup_Config->CbsCmnCpuSmee == 0x1) && (Setup_Config->CbsPspSevCtrl == 0x0) && (Setup_Config->CbsDbgCpuSnpMemCover == 0x1)) ||((Setup_Config->CbsDbgCpuSnpMemCover == 0x2))) {
    if (Setup_Config->CbsCmnCpu64BitMMIOCoverage != 0xFF) {
      PcdSetBoolS (PcdAmdRmpCover64BitMMIORanges, Setup_Config->CbsCmnCpu64BitMMIOCoverage);
    }
  }

  //Check Display Condition CbsComboFlag=255,CbsCmnCpu64BitMMIOCoverage=Enabled
  if ((Setup_Config->CbsComboFlag == 0xff) && (Setup_Config->CbsCmnCpu64BitMMIOCoverage == 0x1)) {
    PcdSet16S (PcdAmdRmp64BitMmioS0RbMask, Setup_Config->CbsCmnCpu64BitMMIORmpS0RBMask);
  }

  //Check Display Condition CbsComboFlag=255,CbsCmnCpu64BitMMIOCoverage=Enabled,CbsNumberOfSockets=2
  if ((Setup_Config->CbsComboFlag == 0xff) && (Setup_Config->CbsCmnCpu64BitMMIOCoverage == 0x1) && (Setup_Config->CbsNumberOfSockets == 0x2)) {
    PcdSet16S (PcdAmdRmp64BitMmioS1RbMask, Setup_Config->CbsCmnCpu64BitMMIORmpS1RBMask);
  }

  //Check Display Condition CbsComboFlag=255,CbsCmnCpuSmee=Enable,CbsPspSevCtrl=Enable,CbsDbgCpuSnpMemCover=Enabled|CbsDbgCpuSnpMemCover=Custom
  if (((Setup_Config->CbsComboFlag == 0xff) && (Setup_Config->CbsCmnCpuSmee == 0x1) && (Setup_Config->CbsPspSevCtrl == 0x0) && (Setup_Config->CbsDbgCpuSnpMemCover == 0x1)) ||((Setup_Config->CbsDbgCpuSnpMemCover == 0x2))) {
    if (Setup_Config->CbsDbgCpuSplitRMP != 0xFF) {
      PcdSet8S (PcdAmdSplitRmpTable, Setup_Config->CbsDbgCpuSplitRMP);
    }
  }

  //Check Display Condition CbsCmnCpuSmee=Enable,CbsPspSevCtrl=Enable,CbsDbgCpuSnpMemCover=Enabled|CbsDbgCpuSnpMemCover=Custom
  if (((Setup_Config->CbsCmnCpuSmee == 0x1) && (Setup_Config->CbsPspSevCtrl == 0x0) && (Setup_Config->CbsDbgCpuSnpMemCover == 0x1)) ||((Setup_Config->CbsDbgCpuSnpMemCover == 0x2))) {
    if (Setup_Config->CbsDbgCpuSegmentedRMP != 0xFF) {
      PcdSetBoolS (PcdAmdSegmentedRmp, Setup_Config->CbsDbgCpuSegmentedRMP);
    }
  }

  //Check Display Condition CbsCmnCpuSmee=Enable,CbsPspSevCtrl=Enable,CbsDbgCpuSnpMemCover=Enabled|CbsDbgCpuSnpMemCover=Custom
  if (((Setup_Config->CbsCmnCpuSmee == 0x1) && (Setup_Config->CbsPspSevCtrl == 0x0) && (Setup_Config->CbsDbgCpuSnpMemCover == 0x1)) ||((Setup_Config->CbsDbgCpuSnpMemCover == 0x2))) {
    if (Setup_Config->CbsDbgCpuRmpSegmentSize != 0xFF) {
      PcdSet8S (PcdAmdRmpSegSize, Setup_Config->CbsDbgCpuRmpSegmentSize);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnCpuERMS != 0xFF) {
      PcdSetBoolS (PcdAmdEnableERMS, Setup_Config->CbsCmnCpuERMS);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnCpuLogTransparentErrors != 3) {
      PcdSetBoolS (PcdAmdTransparentErrorLoggingEnable, Setup_Config->CbsCmnCpuLogTransparentErrors);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnCpuAvx512 != 0xFF) {
      PcdSet8S (PcdAmdCcxEnableAvx512, Setup_Config->CbsCmnCpuAvx512);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnCpuDisFstStrErmsb != 0xFF) {
      PcdSet8S (PcdAmdCcxDisFstStrErmsb, Setup_Config->CbsCmnCpuDisFstStrErmsb);
    }
  }

  //Check Display Condition CbsComboFlag=255
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnCpuMonMwaitDis != 0xFF) {
      PcdSet8S (PcdAmdMonMwaitDis, Setup_Config->CbsCmnCpuMonMwaitDis);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCpuSpeculativeStoreModes != 0xFF) {
      PcdSet8S (PcdAmdCpuSpeculativeStoreMode, Setup_Config->CbsCpuSpeculativeStoreModes);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnCpuFSRM != 0xFF) {
      PcdSetBoolS (PcdAmdEnableFSRM, Setup_Config->CbsCmnCpuFSRM);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnCpuPauseCntSel_1_0 != 0xFF) {
      PcdSet8S (PcdAmdCpuPauseCntSel_1_0, Setup_Config->CbsCmnCpuPauseCntSel_1_0);
    }
  }

  //Check Display Condition CbsCmnCpuPfeh=Disabled
  if ((Setup_Config->CbsCmnCpuPfeh == 0x0)) {
    PcdSet8S (PcdCmcNotificationType, Setup_Config->CbsCmnCmcNotificationType);
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnCpuAdaptiveAlloc != 0xFF) {
      PcdSet8S (PcdAmdCpuAdaptiveAlloc, Setup_Config->CbsCmnCpuAdaptiveAlloc);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    PcdSetBoolS (PcdCoreTraceDumpEnable, Setup_Config->CbsCmnCoreTraceDumpEn);
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnCpuAmdErmsbRepo != 0xFF) {
      PcdSet8S (PcdAmdCcxErmsbRepo, Setup_Config->CbsCmnCpuAmdErmsbRepo);
    }
  }

  if (Setup_Config->CbsCpuPstCustomP0 != 2) {
    PcdSet8S (PcdAmdCcxP0Setting, Setup_Config->CbsCpuPstCustomP0);
  }

  //Check Display Condition CbsCpuPstCustomP0=Custom
  if ((Setup_Config->CbsCpuPstCustomP0 == 0x1)) {
    PcdSet32S (PcdAmdCcxP0Freq, Setup_Config->CbsCpuPst0Freq);
  }

  //Check Display Condition CbsComboFlag=255
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    PcdSet32S (PcdAmdCcxP0Fid32, Setup_Config->CbsCpuPst0Fid);
  }

  //Check Display Condition CbsComboFlag=255
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    PcdSet32S (PcdAmdCcxP0Vid32, Setup_Config->CbsCpuPst0Vid);
  }

  if (Setup_Config->CbsCmnCpuL1StreamHwPrefetcher != 3) {
    PcdSetBoolS (PcdAmdL1StreamPrefetcher, Setup_Config->CbsCmnCpuL1StreamHwPrefetcher);
  }

  if (Setup_Config->CbsCmnCpuL1StridePrefetcher != 3) {
    PcdSetBoolS (PcdAmdL1StridePrefetcher, Setup_Config->CbsCmnCpuL1StridePrefetcher);
  }

  if (Setup_Config->CbsCmnCpuL1RegionPrefetcher != 3) {
    PcdSetBoolS (PcdAmdL1RegionPrefetcher, Setup_Config->CbsCmnCpuL1RegionPrefetcher);
  }

  if (Setup_Config->CbsCmnCpuL2StreamHwPrefetcher != 3) {
    PcdSetBoolS (PcdAmdL2StreamPrefetcher, Setup_Config->CbsCmnCpuL2StreamHwPrefetcher);
  }

  if (Setup_Config->CbsCmnCpuL2UpDownPrefetcher != 3) {
    PcdSetBoolS (PcdAmdL2UpDownPrefetcher, Setup_Config->CbsCmnCpuL2UpDownPrefetcher);
  }

  if (Setup_Config->CbsCmnCpuL1BurstPrefetchMode != 3) {
    PcdSetBoolS (PcdAmdL1BurstPrefetch, Setup_Config->CbsCmnCpuL1BurstPrefetchMode);
  }

  if (Setup_Config->CbsDbgCpuGenCpuWdt != 3) {
    PcdSetBoolS (PcdAmdCpuWdtEn, Setup_Config->CbsDbgCpuGenCpuWdt);
  }

  //Check Display Condition CbsDbgCpuGenCpuWdt=Enabled
  if ((Setup_Config->CbsDbgCpuGenCpuWdt == 0x1)) {
    if (Setup_Config->CbsDbgCpuGenCpuWdtTimeout != 0xFFFF) {
      PcdSet16S (PcdAmdCpuWdtTimeout, Setup_Config->CbsDbgCpuGenCpuWdtTimeout);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsDfCmnWdtInterval != 0xFF) {
      PcdSet8S (PcdAmdFabricWdtCntSel, Setup_Config->CbsDfCmnWdtInterval);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsDfCmnFreezeQueueError != 3) {
      PcdSetBoolS (PcdAmdFabricImmSyncFloodOnFatalErrCtrl, Setup_Config->CbsDfCmnFreezeQueueError);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsDfCmnMixedInterleavedMode != 0xFF) {
      PcdSet8S (PcdAmdCXlEarlyLinkTraining, Setup_Config->CbsDfCmnMixedInterleavedMode);
    }
  }

  if (Setup_Config->CbsDfCmnAcpiSratL3Numa != 255) {
    PcdSetBoolS (PcdAmdFabricCcxAsNumaDomain, Setup_Config->CbsDfCmnAcpiSratL3Numa);
  }

  if (Setup_Config->CbsDfCmnAcpiSlitDistCtrl != 0xFF) {
    PcdSet8S (PcdAmdFabricSlitDistancePcdCtrl, Setup_Config->CbsDfCmnAcpiSlitDistCtrl);
  }

  //Check Display Condition CbsDfCmnAcpiSlitDistCtrl=Auto
  if ((Setup_Config->CbsDfCmnAcpiSlitDistCtrl == 0xff)) {
    if (Setup_Config->CbsDfCmnAcpiSlitRemoteFar != 255) {
      PcdSetBoolS (PcdAmdFabricSlitAutoRemoteFar, Setup_Config->CbsDfCmnAcpiSlitRemoteFar);
    }
  }

  //Check Display Condition CbsDfCmnAcpiSlitDistCtrl=Manual,CbsDfCmnAcpiSratL3Numa=Enabled
  if ((Setup_Config->CbsDfCmnAcpiSlitDistCtrl == 0x0) && (Setup_Config->CbsDfCmnAcpiSratL3Numa == 0x1)) {
    PcdSet8S (PcdAmdFabricSlitVirtualDistance, Setup_Config->CbsDfCmnAcpiSlitVirtualDist);
  }

  //Check Display Condition CbsDfCmnAcpiSlitDistCtrl=Manual
  if ((Setup_Config->CbsDfCmnAcpiSlitDistCtrl == 0x0)) {
    PcdSet8S (PcdAmdFabricSlitLocalDistance, Setup_Config->CbsDfCmnAcpiSlitLclDist);
  }

  //Check Display Condition CbsDfCmnAcpiSlitDistCtrl=Manual
  if ((Setup_Config->CbsDfCmnAcpiSlitDistCtrl == 0x0)) {
    PcdSet8S (PcdAmdFabricSlitRemoteDistance, Setup_Config->CbsDfCmnAcpiSlitRmtDist);
  }

  //Check Display Condition CbsDfCmnAcpiSlitDistCtrl=Manual
  if ((Setup_Config->CbsDfCmnAcpiSlitDistCtrl == 0x0)) {
    PcdSet8S (PcdAmdFabricSlitCxlLocalDistance, Setup_Config->CbsDfCmnAcpiSlitCxlLcl);
  }

  //Check Display Condition CbsDfCmnAcpiSlitDistCtrl=Manual
  if ((Setup_Config->CbsDfCmnAcpiSlitDistCtrl == 0x0)) {
    PcdSet8S (PcdAmdFabricSlitCxlRemoteDistance, Setup_Config->CbsDfCmnAcpiSlitCxlRmt);
  }

  if (Setup_Config->CbsDfCdma != 0xFF) {
    PcdSetBoolS (PcdAmdFabricCdma, Setup_Config->CbsDfCdma);
  }

  PcdSetBoolS (PcdAmdMemBootTimePostPackageRepair, Setup_Config->CbsCmnMemBootTimePostPackageRepair);

  PcdSetBoolS (PcdAmdMemRuntimePostPackageRepair, Setup_Config->CbsCmnMemRuntimePostPackageRepair);

  PcdSet8S (PcdAmdMemPostPackageRepairConfigInitiator, Setup_Config->CbsCmnMemPostPackageRepairConfigInitiator);

  PcdSetBoolS (PcdAmdMemEcsStatusInterrupt, Setup_Config->CbsCmnMemEcsStatusInterruptDdr);

  PcdSet8S (PcdAmdDdrEccErrorCounterEnable, Setup_Config->CbsCmnMemCorrectedErrorCounterEnable);

  PcdSetBoolS (PcdAmdDdrEccErrorCounterIntEnable, Setup_Config->CbsCmnMemCorrectedErrorCounterInterruptEnable);

  PcdSet8S (PcdAmdDdrEccErrorCounterLeakRate, Setup_Config->CbsCmnMemCorrectedErrorCounterLeakRate);

  PcdSet16S (PcdAmdDdrEccErrorCounterStartCount, Setup_Config->CbsCmnMemCorrectedErrorCounterStartCount);

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    PcdSetBoolS (PcdAmdHmkee, Setup_Config->CbsCmnMemSmeMkEnable);
  }

  if (Setup_Config->CbsCmnPmicErrorReporting != 0xFF) {
    PcdSetBoolS (PcdAmdPmicErrorReporting, Setup_Config->CbsCmnPmicErrorReporting);
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnGnbPcieLoopBackMode != 0xF) {
      PcdSetBoolS (PcdCfgPcieLoopbackMode, Setup_Config->CbsCmnGnbPcieLoopBackMode);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsEnable2SpcGen4 != 0xf) {
      PcdSetBoolS (PcdEnable2SpcGen4, Setup_Config->CbsEnable2SpcGen4);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsEnable2SpcGen5 != 0xf) {
      PcdSetBoolS (PcdEnable2SpcGen5, Setup_Config->CbsEnable2SpcGen5);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsGnbSafeRecoveryUponABERExceededError != 0xf) {
      PcdSetBoolS (PcdsafeRecoveryBER, Setup_Config->CbsGnbSafeRecoveryUponABERExceededError);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsGnbPeriodicCalibration != 0xf) {
      PcdSetBoolS (PcdPeriodicCal, Setup_Config->CbsGnbPeriodicCalibration);
    }
  }

  //Check Display Condition CbsCmnTDPCtl=Manual
  if ((Setup_Config->CbsCmnTDPCtl == 0x1)) {
    PcdSet32S (PcdAmdcTDP, Setup_Config->CbsCmnTDPLimit);
  }

  //Check Display Condition CbsCmnPPTCtl=Manual
  if ((Setup_Config->CbsCmnPPTCtl == 0x1)) {
    PcdSet32S (PcdCfgPPT, Setup_Config->CbsCmnPPTLimit);
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnDeterminismCtl != 0) {
      PcdSet8S (PcdAmdDeterminismMode, Setup_Config->CbsCmnDeterminismCtl);
    }
  }

  //Check Display Condition CbsComboFlag=255,CbsCmnDeterminismCtl=Manual
  if ((Setup_Config->CbsComboFlag == 0xff) && (Setup_Config->CbsCmnDeterminismCtl == 0x1)) {
    PcdSetBoolS (PcdAmdDeterminismControl, Setup_Config->CbsCmnDeterminismEnable);
  }

  //Check Display Condition CbsComboFlag=255,CbsCmnxGmiLinkWidthCtl=Manual
  if ((Setup_Config->CbsComboFlag == 0xff) && (Setup_Config->CbsCmnxGmiLinkWidthCtl == 0x1)) {
    if (Setup_Config->CbsCmnxGmiForceLinkWidthCtl != 0xF) {
      PcdSet8S (PcdxGMIForceLinkWidthEn, Setup_Config->CbsCmnxGmiForceLinkWidthCtl);
    }
  }

  //Check Display Condition CbsComboFlag=255,CbsCmnxGmiLinkWidthCtl=Manual,CbsCmnxGmiForceLinkWidthCtl=Force
  if ((Setup_Config->CbsComboFlag == 0xff) && (Setup_Config->CbsCmnxGmiLinkWidthCtl == 0x1) && (Setup_Config->CbsCmnxGmiForceLinkWidthCtl == 0x1)) {
    if (Setup_Config->CbsCmnxGmiForceLinkWidth != 0xF) {
      PcdSet8S (PcdxGMIForceLinkWidth, Setup_Config->CbsCmnxGmiForceLinkWidth);
    }
  }

  //Check Display Condition CbsComboFlag=255,CbsCmnxGmiLinkWidthCtl=Manual
  if ((Setup_Config->CbsComboFlag == 0xff) && (Setup_Config->CbsCmnxGmiLinkWidthCtl == 0x1)) {
    if (Setup_Config->CbsCmnxGmiMaxLinkWidthCtl != 0) {
      PcdSet8S (PcdxGMIMaxLinkWidthEn, Setup_Config->CbsCmnxGmiMaxLinkWidthCtl);
    }
  }

  //Check Display Condition CbsComboFlag=255,CbsCmnxGmiLinkWidthCtl=Manual ,CbsCmnxGmiMaxLinkWidthCtl=Manual
  if ((Setup_Config->CbsComboFlag == 0xff) && (Setup_Config->CbsCmnxGmiLinkWidthCtl == 0x1) && (Setup_Config->CbsCmnxGmiMaxLinkWidthCtl == 0x1)) {
    if (Setup_Config->CbsCmnxGmiMaxLinkWidth != 0xF) {
      PcdSet8S (PcdxGMIMaxLinkWidth, Setup_Config->CbsCmnxGmiMaxLinkWidth);
    }
  }

  //Check Display Condition CbsComboFlag=255,CbsCmnxGmiLinkWidthCtl=Manual ,CbsCmnxGmiMaxLinkWidthCtl=Manual
  if ((Setup_Config->CbsComboFlag == 0xff) && (Setup_Config->CbsCmnxGmiLinkWidthCtl == 0x1) && (Setup_Config->CbsCmnxGmiMaxLinkWidthCtl == 0x1)) {
    if (Setup_Config->CbsCmnxGmiMinLinkWidth != 0xF) {
      PcdSet8S (PcdxGMIMinLinkWidth, Setup_Config->CbsCmnxGmiMinLinkWidth);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnApbdis != 0xf) {
      PcdSet8S (PcdCfgApbDis, Setup_Config->CbsCmnApbdis);
    }
  }

  //Check Display Condition CbsComboFlag=255,CbsCmnApbdis=1
  if ((Setup_Config->CbsComboFlag == 0xff) && (Setup_Config->CbsCmnApbdis == 0x1)) {
    PcdSet8S (PcdCfgFixedSocPstate, Setup_Config->CbsCmnApbdisDfPstate);
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnEfficiencyModeEn != 0xFF) {
      PcdSet8S (PcdPowerProfileSelect, Setup_Config->CbsCmnEfficiencyModeEn);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnXgmiPstateControl != 0xf) {
      PcdSet8S (PcdXgmiPstateControl, Setup_Config->CbsCmnXgmiPstateControl);
    }
  }

  //Check Display Condition CbsComboFlag=255,CbsCmnXgmiPstateControl=Manual
  if ((Setup_Config->CbsComboFlag == 0xff) && (Setup_Config->CbsCmnXgmiPstateControl == 0x1)) {
    PcdSet8S (PcdXgmiPstateSelection, Setup_Config->CbsCmnXgmiPstateSelection);
  }

  //Check Display Condition CbsComboFlag=255,CbsCmnBoostFmaxEn=Manual
  if ((Setup_Config->CbsComboFlag == 0xff) && (Setup_Config->CbsCmnBoostFmaxEn == 0x1)) {
    PcdSet32S (PcdAmdBoostFmax, Setup_Config->CbsCmnBoostFmax);
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnGnbSMUDffo != 0xF) {
      PcdSet8S (PcdDFFODisable, Setup_Config->CbsCmnGnbSMUDffo);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnGnbSmuDfCstates != 0xf) {
      PcdSet8S (PcdDfCstateEnable, Setup_Config->CbsCmnGnbSmuDfCstates);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnGnbSmuCppc != 0xf) {
      PcdSet8S (PcdCfgCPPCMode, Setup_Config->CbsCmnGnbSmuCppc);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnGnbSMUHsmpSupport != 0xF) {
      PcdSet8S (PcdCfgHSMPSupport, Setup_Config->CbsCmnGnbSMUHsmpSupport);
    }
  }

  //Check Display Condition CbsComboFlag=255,CbsCmnSvi3SvcSpeedCtl=Manual
  if ((Setup_Config->CbsComboFlag == 0xff) && (Setup_Config->CbsCmnSvi3SvcSpeedCtl == 0x1)) {
    PcdSet8S (PcdAmdSvi3SvcSpeed, Setup_Config->CbsCmnSvi3SvcSpeed);
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnGnbDiagMode != 0xF) {
      PcdSet8S (PcdCfgDiagnosticMode, Setup_Config->CbsCmnGnbDiagMode);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnGnbSmuGmiFolding != 0xf) {
      PcdSet8S (PcdAmdGmiFolding, Setup_Config->CbsCmnGnbSmuGmiFolding);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnThrottlerMode != 0xf) {
      PcdSet8S (PcdThrottlerMode, Setup_Config->CbsCmnThrottlerMode);
    }
  }

  //Check Display Condition CbsComboFlag=255 
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnDFPstateRangeCtl != 0xFF) {
      PcdSet8S (PcdDfPstateRangeSupportEn, Setup_Config->CbsCmnDFPstateRangeCtl);
    }
  }

  //Check Display Condition CbsComboFlag=255,CbsCmnDFPstateRangeCtl=Enable
  if ((Setup_Config->CbsComboFlag == 0xff) && (Setup_Config->CbsCmnDFPstateRangeCtl == 0x1)) {
    PcdSet8S (PcdDfPstateRangeMax, Setup_Config->CbsCmnDfPstateMax);
  }

  //Check Display Condition CbsComboFlag=255,CbsCmnDFPstateRangeCtl=Enable
  if ((Setup_Config->CbsComboFlag == 0xff) && (Setup_Config->CbsCmnDFPstateRangeCtl == 0x1)) {
    PcdSet8S (PcdDfPstateRangeMin, Setup_Config->CbsCmnDfPstateMin);
  }

  if (Setup_Config->CbsCmnRASControl != 0xF) {
    PcdSet8S (PcdAmdNbioRASControlV2, Setup_Config->CbsCmnRASControl);
  }

  if (Setup_Config->CbsCmnNBIOSyncFloodGen != 0xf) {
    PcdSetBoolS (PcdAmdMaskNbioSyncFlood, Setup_Config->CbsCmnNBIOSyncFloodGen);
  }

  if (Setup_Config->PcdSyncFloodToApml != 0xF) {
    PcdSetBoolS (PcdSyncFloodToApml, Setup_Config->PcdSyncFloodToApml);
  }

  if (Setup_Config->CmnGnbAmdPcieAerReportMechanism != 0x0F) {
    PcdSet8S (PcdAmdPcieAerReportMechanism, Setup_Config->CmnGnbAmdPcieAerReportMechanism);
  }

  if (Setup_Config->EdpcControl != 3) {
    PcdSet8S (PcdAmdEdpcEnable, Setup_Config->EdpcControl);
  }

  if (Setup_Config->AcsRasValue != 0xF) {
    PcdSet16S (PcdRASAcsValue, Setup_Config->AcsRasValue);
  }

  if (Setup_Config->CbsCmnPoisonConsumption != 0xF) {
    PcdSetBoolS (PcdAmdNbioPoisonConsumption, Setup_Config->CbsCmnPoisonConsumption);
  }

  if (Setup_Config->CbsCmnGnbRasSyncfloodPcieFatalError != 0xF) {
    PcdSetBoolS (PcdAmdPcieSyncFloodOnFatal, Setup_Config->CbsCmnGnbRasSyncfloodPcieFatalError);
  }

  //Check Display Condition CbsCmnRASNumericalCommonOptions=Manual
  if ((Setup_Config->CbsCmnRASNumericalCommonOptions == 0x1)) {
    PcdSet32S (PcdEgressPoisonSeverityHi, Setup_Config->PcdEgressPoisonSeverityHi);
  }

  //Check Display Condition CbsCmnRASNumericalCommonOptions=Manual
  if ((Setup_Config->CbsCmnRASNumericalCommonOptions == 0x1)) {
    PcdSet32S (PcdEgressPoisonSeverityLo, Setup_Config->PcdEgressPoisonSeverityLo);
  }

  //Check Display Condition CbsCmnRASNumericalCommonOptions=Manual
  if ((Setup_Config->CbsCmnRASNumericalCommonOptions == 0x1)) {
    PcdSet32S (PcdAmdNbioEgressPoisonMaskHi, Setup_Config->PcdAmdNbioEgressPoisonMaskHi);
  }

  //Check Display Condition CbsCmnRASNumericalCommonOptions=Manual
  if ((Setup_Config->CbsCmnRASNumericalCommonOptions == 0x1)) {
    PcdSet32S (PcdAmdNbioEgressPoisonMaskLo, Setup_Config->PcdAmdNbioEgressPoisonMaskLo);
  }

  //Check Display Condition CbsCmnRASNumericalCommonOptions=Manual
  if ((Setup_Config->CbsCmnRASNumericalCommonOptions == 0x1)) {
    PcdSet32S (PcdAmdNbioRASUcpMaskHi, Setup_Config->PcdAmdNbioRASUcpMaskHi);
  }

  //Check Display Condition CbsCmnRASNumericalCommonOptions=Manual
  if ((Setup_Config->CbsCmnRASNumericalCommonOptions == 0x1)) {
    PcdSet32S (PcdAmdNbioRASUcpMaskLo, Setup_Config->PcdAmdNbioRASUcpMaskLo);
  }

  //Check Display Condition CbsCmnRASNumericalCommonOptions=Manual
  if ((Setup_Config->CbsCmnRASNumericalCommonOptions == 0x1)) {
    PcdSet32S (PcdSyshubWdtTimerInterval, Setup_Config->PcdSyshubWdtTimerInterval);
  }

  if (Setup_Config->CbsCmnGnbDataObjectExchange != 0xf) {
    PcdSetBoolS (PcdDataObjectExchange, Setup_Config->CbsCmnGnbDataObjectExchange);
  }

  if (Setup_Config->CbsCmnGnbRtmMarginingSupport != 0xF) {
    PcdSet8S (PcdCfgRxMarginPersistenceMode, Setup_Config->CbsCmnGnbRtmMarginingSupport);
  }

  if (Setup_Config->CbsCmnNbioForceSpeedLastAdvertised != 0xFF) {
    PcdSetBoolS (PcdLcMultAutoSpdChgOnLastRateEnable, Setup_Config->CbsCmnNbioForceSpeedLastAdvertised);
  }

  if (Setup_Config->CbsCmnLcMultUpstreamAuto != 0xF) {
    PcdSet8S (PcdCfgAutoSpeedChangeEnable, Setup_Config->CbsCmnLcMultUpstreamAuto);
  }

  if (Setup_Config->STRAP_COMPLIANCE_DIS != 0xf) {
    PcdSet16S (PcdAmdAllowCompliance, Setup_Config->STRAP_COMPLIANCE_DIS);
  }

  if (Setup_Config->CbsCmnNbioPcieAdvertiseEqToHighRateSupport != 0xF) {
    PcdSetBoolS (PcdAmdAdvertiseEqToHighRateSupport, Setup_Config->CbsCmnNbioPcieAdvertiseEqToHighRateSupport);
  }

  if (Setup_Config->CbsCmnGnbDataLinkFeatureCap != 0xF) {
    PcdSet8S (PcdAmdDlfCapEnV2, Setup_Config->CbsCmnGnbDataLinkFeatureCap);
  }

  //Check Display Condition CbsCmnGnbDataLinkFeatureCap=Enabled
  if ((Setup_Config->CbsCmnGnbDataLinkFeatureCap == 0x1)) {
    if (Setup_Config->CbsCmnGnbDataLinkFeatureExchange != 0xF) {
      PcdSet8S (PcdAmdDlfExEnV2, Setup_Config->CbsCmnGnbDataLinkFeatureExchange);
    }
  }

  if (Setup_Config->CbsCmnGnbSris != 0xF) {
    PcdSet8S (PcdSrisEnableMode, Setup_Config->CbsCmnGnbSris);
  }

  //Check Display Condition CbsDbgGnbDbgAERCAPEnable=Enable|CbsDbgGnbDbgAERCAPEnable=Auto
  if (((Setup_Config->CbsDbgGnbDbgAERCAPEnable == 0x1)) ||((Setup_Config->CbsDbgGnbDbgAERCAPEnable == 0xf))) {
    if (Setup_Config->CbsDbgGnbDbgACSEnable != 0xF) {
      PcdSetBoolS (PcdCfgACSEnable, Setup_Config->CbsDbgGnbDbgACSEnable);
    }
  }

  if (Setup_Config->CbsGnbCmnPcieTbtSupport != 0xf) {
    PcdSetBoolS (PcdCfgPcieTbtSupport, Setup_Config->CbsGnbCmnPcieTbtSupport);
  }

  if (Setup_Config->CbsGnbCmnPcieAriEnumeration != 0xf) {
    PcdSetBoolS (PcdPcieAriForwardingEnable, Setup_Config->CbsGnbCmnPcieAriEnumeration);
  }

  if (Setup_Config->CmnGnbPcieAriSupport != 0xf) {
    PcdSetBoolS (PcdCfgPcieAriSupport, Setup_Config->CmnGnbPcieAriSupport);
  }

  if (Setup_Config->CbsPresenceDetectSelectmode != 0xF) {
    PcdSet8S (PcdAmdPresenceDetectSelectMode, Setup_Config->CbsPresenceDetectSelectmode);
  }

  if (Setup_Config->CbsHotPlugHandlingMode != 0xF) {
    PcdSet8S (PcdAmdHotPlugHandlingMode, Setup_Config->CbsHotPlugHandlingMode);
  }

  if (Setup_Config->CbsHotPlugPDSettle != 0) {
    PcdSetBoolS (PcdAmdHotPlugPDSettle, Setup_Config->CbsHotPlugPDSettle);
  }

  PcdSet8S (PcdAmdHotPlugSettleTime, Setup_Config->CbsHotPlugSettleTime);

  if (Setup_Config->CbsHotplugSupport != 0xF) {
    PcdSet8S (PcdAmdHotPlugSupport, Setup_Config->CbsHotplugSupport);
  }

  if (Setup_Config->CbsDbgGnbDbgAERCAPEnable != 0xF) {
    PcdSetBoolS (PcdCfgAEREnable, Setup_Config->CbsDbgGnbDbgAERCAPEnable);
  }

  if (Setup_Config->CbsCmnPcieCAPLinkSpeed != 0xf) {
    PcdSet8S (PcdCfgForcePcieGenSpeed, Setup_Config->CbsCmnPcieCAPLinkSpeed);
  }

  if (Setup_Config->CbsCmnPcieTargetLinkSpeed != 0xFF) {
    PcdSet8S (PcdTargetPcieGenSpeed, Setup_Config->CbsCmnPcieTargetLinkSpeed);
  }

  if (Setup_Config->CbsCmnAllPortsASPM != 0xFF) {
    PcdSet8S (PcdPcieLinkAspmAllPort, Setup_Config->CbsCmnAllPortsASPM);
  }

  if (Setup_Config->CbsCmnNbioMctpEn != 0xFF) {
    PcdSetBoolS (PcdAmdMCTPEnable, Setup_Config->CbsCmnNbioMctpEn);
  }

  //Check Display Condition CbsCmnNbioMctpEn = Enable
  if ((Setup_Config->CbsCmnNbioMctpEn == 0x1)) {
    if (Setup_Config->CbsCmnNbioMctpMode != 0xFF) {
      PcdSet8S (PcdAmdMCTPMode, Setup_Config->CbsCmnNbioMctpMode);
    }
  }

  //Check Display Condition CbsCmnNbioMctpEn = Enable
  if ((Setup_Config->CbsCmnNbioMctpEn == 0x1)) {
    if (Setup_Config->CbsCmnNbioMctpDiscoveryNotifyMessage != 0xFF) {
      PcdSetBoolS (PcdAmdMCTPDiscoveryNotify, Setup_Config->CbsCmnNbioMctpDiscoveryNotifyMessage);
    }
  }

  if (Setup_Config->CbsCmnNbioPcieNonPcieCompliantSupport != 0xFF) {
    PcdSetBoolS (PcdPcieNonPcieCompliantTrainingFailureSupport, Setup_Config->CbsCmnNbioPcieNonPcieCompliantSupport);
  }

  if (Setup_Config->CbsCmnLimitHpDevicesToPcieBootSpeed != 0xf) {
    PcdSetBoolS (PcdLimitHpDevicesToPcieBootSpeed, Setup_Config->CbsCmnLimitHpDevicesToPcieBootSpeed);
  }

  PcdSetBoolS (PcdPCIeSFIConfigviaOOBEn, Setup_Config->CbsCmnPCIeSFIConfigviaOOBEn);

  PcdSet8S (PcdPcieIdlePowerSetting, Setup_Config->CbsCmnNbioPcieIdlePowerSetting);

  if (Setup_Config->CbsCfgAcsEnRccDev0 != 0xF) {
    PcdSetBoolS (PcdAcsEnRccDev0, Setup_Config->CbsCfgAcsEnRccDev0);
  }

  if (Setup_Config->CbsCfgAerEnRccDev0 != 0xF) {
    PcdSetBoolS (PcdAerEnRccDev0, Setup_Config->CbsCfgAerEnRccDev0);
  }

  if (Setup_Config->CbsCfgDlfEnStrap1 != 0xF) {
    PcdSetBoolS (PcdDlfEnStrap1, Setup_Config->CbsCfgDlfEnStrap1);
  }

  if (Setup_Config->CbsCfgPhy16gtStrap1 != 0xF) {
    PcdSetBoolS (PcdPhy16gtStrap1, Setup_Config->CbsCfgPhy16gtStrap1);
  }

  if (Setup_Config->CbsCfgMarginEnStrap1 != 0xF) {
    PcdSetBoolS (PcdMarginEnStrap1, Setup_Config->CbsCfgMarginEnStrap1);
  }

  if (Setup_Config->CbsCfgAcsSourceValStrap5 != 0xF) {
    PcdSetBoolS (PcdAcsSourceValStrap5, Setup_Config->CbsCfgAcsSourceValStrap5);
  }

  if (Setup_Config->CbsCfgAcsTranslationalBlockingStrap5 != 0xF) {
    PcdSetBoolS (PcdAcsTranslationalBlockingStrap5, Setup_Config->CbsCfgAcsTranslationalBlockingStrap5);
  }

  if (Setup_Config->CbsCfgAcsP2pReq != 0xF) {
    PcdSetBoolS (PcdAcsP2pReq, Setup_Config->CbsCfgAcsP2pReq);
  }

  if (Setup_Config->CbsCfgAcsP2pCompStrap5 != 0xF) {
    PcdSetBoolS (PcdAcsP2pCompStrap5, Setup_Config->CbsCfgAcsP2pCompStrap5);
  }

  if (Setup_Config->CbsCfgAcsUpstreamFwdStrap5 != 0xF) {
    PcdSetBoolS (PcdAcsUpstreamFwdStrap5, Setup_Config->CbsCfgAcsUpstreamFwdStrap5);
  }

  if (Setup_Config->CbsCfgAcsP2PEgressStrap5 != 0xF) {
    PcdSetBoolS (PcdAcsP2PEgressStrap5, Setup_Config->CbsCfgAcsP2PEgressStrap5);
  }

  if (Setup_Config->CbsCfgAcsDirectTranslatedStrap5 != 0xF) {
    PcdSetBoolS (PcdAcsDirectTranslatedStrap5, Setup_Config->CbsCfgAcsDirectTranslatedStrap5);
  }

  if (Setup_Config->CbsCfgAcsSsidEnStrap5 != 0xF) {
    PcdSetBoolS (PcdAcsSsidEnStrap5, Setup_Config->CbsCfgAcsSsidEnStrap5);
  }

  if (Setup_Config->CbsCfgPriEnPageReq != 0xF) {
    PcdSetBoolS (PcdPriEnPageReq, Setup_Config->CbsCfgPriEnPageReq);
  }

  if (Setup_Config->CbsCfgPriResetPageReq != 0xF) {
    PcdSetBoolS (PcdPriResetPageReq, Setup_Config->CbsCfgPriResetPageReq);
  }

  if (Setup_Config->CbsCfgAcsSourceVal != 0xF) {
    PcdSetBoolS (PcdAcsSourceVal, Setup_Config->CbsCfgAcsSourceVal);
  }

  if (Setup_Config->CbsCfgAcsTranslationalBlocking != 0xF) {
    PcdSetBoolS (PcdAcsTranslationalBlocking, Setup_Config->CbsCfgAcsTranslationalBlocking);
  }

  if (Setup_Config->CbsCfgAcsP2pComp != 0xF) {
    PcdSetBoolS (PcdAcsP2pComp, Setup_Config->CbsCfgAcsP2pComp);
  }

  if (Setup_Config->CbsCfgAcsUpstreamFwd != 0xF) {
    PcdSetBoolS (PcdAcsUpstreamFwd, Setup_Config->CbsCfgAcsUpstreamFwd);
  }

  if (Setup_Config->CbsCfgAcsP2PEgress != 0xF) {
    PcdSetBoolS (PcdAcsP2PEgress, Setup_Config->CbsCfgAcsP2PEgress);
  }

  if (Setup_Config->CbsCfgAcsP2pReqStrap5 != 0xF) {
    PcdSetBoolS (PcdAcsP2pReqStrap5, Setup_Config->CbsCfgAcsP2pReqStrap5);
  }

  if (Setup_Config->CbsCfgE2EPrefix != 0xF) {
    PcdSetBoolS (PcdRccDev0E2EPrefix, Setup_Config->CbsCfgE2EPrefix);
  }

  if (Setup_Config->CbsCfgExtendedFmtSupported != 0xF) {
    PcdSetBoolS (PcdRccDev0ExtendedFmtSupported, Setup_Config->CbsCfgExtendedFmtSupported);
  }

  if (Setup_Config->CbsCmnNbioAtomicRoutingStrap5 != 0xF) {
    PcdSetBoolS (PcdAtomicRoutingEnStrap5, Setup_Config->CbsCmnNbioAtomicRoutingStrap5);
  }

  if (Setup_Config->CbsSevSnpSupport != 0xf) {
    PcdSetBoolS (PcdCfgSevSnpSupport, Setup_Config->CbsSevSnpSupport);
  }

  if (Setup_Config->CbsSevTioSupport != 0xFF) {
    PcdSetBoolS (PcdCfgSevTioSupport, Setup_Config->CbsSevTioSupport);
  }

  if (Setup_Config->CbsCmnDrtmSupport != 0xF) {
    PcdSetBoolS (PcdAmdPspDrtmVirtualDevice, Setup_Config->CbsCmnDrtmSupport);
  }

  if (Setup_Config->CbsCmnDmaProtection != 0xF) {
    PcdSetBoolS (PcdDmaProtection, Setup_Config->CbsCmnDmaProtection);
  }

  if (Setup_Config->CbsCmnGnbNbIOMMU != 0xf) {
    PcdSetBoolS (PcdCfgIommuSupport, Setup_Config->CbsCmnGnbNbIOMMU);
  }

  if (Setup_Config->CbsCmnDmarSupport != 0xF) {
    PcdSetBoolS (PcdIvInfoDmaReMap, Setup_Config->CbsCmnDmarSupport);
  }

  if (Setup_Config->CbsCmnEnablePortBifurcation != 0xf) {
    PcdSetBoolS (PcdOverRideEnabled, Setup_Config->CbsCmnEnablePortBifurcation);
  }

  //Check Display Condition CbsCmnEnablePortBifurcation=Enable,CbsNumberOfSockets=2
  if ((Setup_Config->CbsCmnEnablePortBifurcation == 0x1) && (Setup_Config->CbsNumberOfSockets == 0x2)) {
    if (Setup_Config->CbsCmnS0P0Override != 0xf) {
      PcdSet8S (PcdOverrideS0P0, Setup_Config->CbsCmnS0P0Override);
    }
  }

  //Check Display Condition CbsCmnEnablePortBifurcation=Enable,CbsNumberOfSockets=2
  if ((Setup_Config->CbsCmnEnablePortBifurcation == 0x1) && (Setup_Config->CbsNumberOfSockets == 0x2)) {
    if (Setup_Config->CbsCmnS0P1Override != 0xf) {
      PcdSet8S (PcdOverrideS0P1, Setup_Config->CbsCmnS0P1Override);
    }
  }

  //Check Display Condition CbsCmnEnablePortBifurcation=Enable,CbsNumberOfSockets=2
  if ((Setup_Config->CbsCmnEnablePortBifurcation == 0x1) && (Setup_Config->CbsNumberOfSockets == 0x2)) {
    if (Setup_Config->CbsCmnS0P2Override != 0xf) {
      PcdSet8S (PcdOverrideS0P2, Setup_Config->CbsCmnS0P2Override);
    }
  }

  //Check Display Condition CbsCmnEnablePortBifurcation=Enable,CbsNumberOfSockets=2
  if ((Setup_Config->CbsCmnEnablePortBifurcation == 0x1) && (Setup_Config->CbsNumberOfSockets == 0x2)) {
    if (Setup_Config->CbsCmnS0P3Override != 0xf) {
      PcdSet8S (PcdOverrideS0P3, Setup_Config->CbsCmnS0P3Override);
    }
  }

  //Check Display Condition CbsCmnEnablePortBifurcation=Enable,CbsNumberOfSockets=2
  if ((Setup_Config->CbsCmnEnablePortBifurcation == 0x1) && (Setup_Config->CbsNumberOfSockets == 0x2)) {
    if (Setup_Config->CbsCmnS1P0Override != 0xf) {
      PcdSet8S (PcdOverrideS1P0, Setup_Config->CbsCmnS1P0Override);
    }
  }

  //Check Display Condition CbsCmnEnablePortBifurcation=Enable,CbsNumberOfSockets=2
  if ((Setup_Config->CbsCmnEnablePortBifurcation == 0x1) && (Setup_Config->CbsNumberOfSockets == 0x2)) {
    if (Setup_Config->CbsCmnS1P1Override != 0xf) {
      PcdSet8S (PcdOverrideS1P1, Setup_Config->CbsCmnS1P1Override);
    }
  }

  //Check Display Condition CbsCmnEnablePortBifurcation=Enable,CbsNumberOfSockets=2
  if ((Setup_Config->CbsCmnEnablePortBifurcation == 0x1) && (Setup_Config->CbsNumberOfSockets == 0x2)) {
    if (Setup_Config->CbsCmnS1P2Override != 0xf) {
      PcdSet8S (PcdOverrideS1P2, Setup_Config->CbsCmnS1P2Override);
    }
  }

  //Check Display Condition CbsCmnEnablePortBifurcation=Enable,CbsNumberOfSockets=2
  if ((Setup_Config->CbsCmnEnablePortBifurcation == 0x1) && (Setup_Config->CbsNumberOfSockets == 0x2)) {
    if (Setup_Config->CbsCmnS1P3Override != 0xf) {
      PcdSet8S (PcdOverrideS1P3, Setup_Config->CbsCmnS1P3Override);
    }
  }

  //Check Display Condition CbsCmnEnablePortBifurcation=Enable,CbsNumberOfSockets=1
  if ((Setup_Config->CbsCmnEnablePortBifurcation == 0x1) && (Setup_Config->CbsNumberOfSockets == 0x1)) {
    if (Setup_Config->CbsCmnP0Override != 0xf) {
      PcdSet8S (PcdOverrideS0P0, Setup_Config->CbsCmnP0Override);
    }
  }

  //Check Display Condition CbsCmnEnablePortBifurcation=Enable,CbsNumberOfSockets=1
  if ((Setup_Config->CbsCmnEnablePortBifurcation == 0x1) && (Setup_Config->CbsNumberOfSockets == 0x1)) {
    if (Setup_Config->CbsCmnP1Override != 0xf) {
      PcdSet8S (PcdOverrideS0P1, Setup_Config->CbsCmnP1Override);
    }
  }

  //Check Display Condition CbsCmnEnablePortBifurcation=Enable,CbsNumberOfSockets=1
  if ((Setup_Config->CbsCmnEnablePortBifurcation == 0x1) && (Setup_Config->CbsNumberOfSockets == 0x1)) {
    if (Setup_Config->CbsCmnP2Override != 0xf) {
      PcdSet8S (PcdOverrideS0P2, Setup_Config->CbsCmnP2Override);
    }
  }

  //Check Display Condition CbsCmnEnablePortBifurcation=Enable,CbsNumberOfSockets=1
  if ((Setup_Config->CbsCmnEnablePortBifurcation == 0x1) && (Setup_Config->CbsNumberOfSockets == 0x1)) {
    if (Setup_Config->CbsCmnP3Override != 0xf) {
      PcdSet8S (PcdOverrideS0P3, Setup_Config->CbsCmnP3Override);
    }
  }

  //Check Display Condition CbsCmnEnablePortBifurcation=Enable,CbsNumberOfSockets=1
  if ((Setup_Config->CbsCmnEnablePortBifurcation == 0x1) && (Setup_Config->CbsNumberOfSockets == 0x1)) {
    if (Setup_Config->CbsCmnG0Override != 0xf) {
      PcdSet8S (PcdOverrideS1P0, Setup_Config->CbsCmnG0Override);
    }
  }

  //Check Display Condition CbsCmnEnablePortBifurcation=Enable,CbsNumberOfSockets=1
  if ((Setup_Config->CbsCmnEnablePortBifurcation == 0x1) && (Setup_Config->CbsNumberOfSockets == 0x1)) {
    if (Setup_Config->CbsCmnG1Override != 0xf) {
      PcdSet8S (PcdOverrideS1P1, Setup_Config->CbsCmnG1Override);
    }
  }

  //Check Display Condition CbsCmnEnablePortBifurcation=Enable,CbsNumberOfSockets=1
  if ((Setup_Config->CbsCmnEnablePortBifurcation == 0x1) && (Setup_Config->CbsNumberOfSockets == 0x1)) {
    if (Setup_Config->CbsCmnG2Override != 0xf) {
      PcdSet8S (PcdOverrideS1P2, Setup_Config->CbsCmnG2Override);
    }
  }

  //Check Display Condition CbsCmnEnablePortBifurcation=Enable,CbsNumberOfSockets=1
  if ((Setup_Config->CbsCmnEnablePortBifurcation == 0x1) && (Setup_Config->CbsNumberOfSockets == 0x1)) {
    if (Setup_Config->CbsCmnG3Override != 0xf) {
      PcdSet8S (PcdOverrideS1P3, Setup_Config->CbsCmnG3Override);
    }
  }

  if (Setup_Config->CbsCmnNbioPcieSearchMaskConfigGen3 != 0xff) {
    PcdSet8S (PcdPcieLaneEqPresetMask8GtConfig, Setup_Config->CbsCmnNbioPcieSearchMaskConfigGen3);
  }

  //Check Display Condition CbsCmnNbioPcieSearchMaskConfigGen3=Custom
  if ((Setup_Config->CbsCmnNbioPcieSearchMaskConfigGen3 == 0x0)) {
    PcdSet32S (PcdPcieLaneEqPresetMask8Gt, Setup_Config->CbsCmnNbioPcieSearchMaskGen3);
  }

  if (Setup_Config->CbsCmnNbioPcieSearchMaskConfigGen4 != 0xff) {
    PcdSet8S (PcdPcieLaneEqPresetMask16GtConfig, Setup_Config->CbsCmnNbioPcieSearchMaskConfigGen4);
  }

  //Check Display Condition CbsCmnNbioPcieSearchMaskConfigGen4=Custom
  if ((Setup_Config->CbsCmnNbioPcieSearchMaskConfigGen4 == 0x0)) {
    PcdSet32S (PcdPcieLaneEqPresetMask16Gt, Setup_Config->CbsCmnNbioPcieSearchMaskGen4);
  }

  if (Setup_Config->CbsCmnNbioPcieSearchMaskConfigGen5 != 0xff) {
    PcdSet8S (PcdPcieLaneEqPresetMask32GtConfig, Setup_Config->CbsCmnNbioPcieSearchMaskConfigGen5);
  }

  //Check Display Condition CbsCmnNbioPcieSearchMaskConfigGen5=Custom
  if ((Setup_Config->CbsCmnNbioPcieSearchMaskConfigGen5 == 0x0)) {
    PcdSet32S (PcdPcieLaneEqPresetMask32Gt, Setup_Config->CbsCmnNbioPcieSearchMaskGen5);
  }

  if (Setup_Config->CbsCmnFchI3C0Config != 0xf) {
    PcdSet8S (PcdAmdFchI2cI3c0, Setup_Config->CbsCmnFchI3C0Config);
  }

  if (Setup_Config->CbsCmnFchI3C1Config != 0xf) {
    PcdSet8S (PcdAmdFchI2cI3c1, Setup_Config->CbsCmnFchI3C1Config);
  }

  if (Setup_Config->CbsCmnFchI3C2Config != 0xf) {
    PcdSet8S (PcdAmdFchI2cI3c2, Setup_Config->CbsCmnFchI3C2Config);
  }

  if (Setup_Config->CbsCmnFchI3C3Config != 0xf) {
    PcdSet8S (PcdAmdFchI2cI3c3, Setup_Config->CbsCmnFchI3C3Config);
  }

  if (Setup_Config->CbsCmnFchI2C4Config != 0xf) {
    PcdSet8S (PcdAmdFchI2c4, Setup_Config->CbsCmnFchI2C4Config);
  }

  if (Setup_Config->CbsCmnFchI2C5Config != 0xf) {
    PcdSet8S (PcdAmdFchI2c5, Setup_Config->CbsCmnFchI2C5Config);
  }

  PcdSetBoolS (PcdAmdFchSpdHostCtrlRelease, Setup_Config->CbsCmnFchReleaseSpdHostControl);

  //Check Display Condition CbsCmnFchReleaseSpdHostControl=Disabled
  if ((Setup_Config->CbsCmnFchReleaseSpdHostControl == 0x0)) {
    PcdSetBoolS (PcdAmdFchDimmTelemetry, Setup_Config->CbsCmnFchPMFWDdr5Telemetry);
  }

  //Check Display Condition CbsCmnFchPMFWDdr5Telemetry=Enabled
  if ((Setup_Config->CbsCmnFchPMFWDdr5Telemetry == 0x1)) {
    PcdSetBoolS (PcdAmdFchIxcTelemetryPortsFenceControl, Setup_Config->CbsCmnFchIxcTelemetryPortsFence);
  }

  if (Setup_Config->CbsCmnFchI2cSdaHoldOverride != 0xf) {
    PcdSet8S (PcdAmdFchI2cSdaHoldOverride, Setup_Config->CbsCmnFchI2cSdaHoldOverride);
  }

  //Check Display Condition CbsComboFlag=255
  if ((Setup_Config->CbsComboFlag == 0xff)) {
    if (Setup_Config->CbsCmnFchI3cModeSpeed != 0xf) {
      PcdSet8S (PcdFchI3cSpeed, Setup_Config->CbsCmnFchI3cModeSpeed);
    }
  }

  PcdSet8S (PcdFchI3cPPHcnt, Setup_Config->CbsCmnFchI3cPpHcntValue);

  if (Setup_Config->CbsCmnFchI3cSdaHoldOverride != 0xf) {
    PcdSet8S (PcdAmdFchI3cSdaHoldOverride, Setup_Config->CbsCmnFchI3cSdaHoldOverride);
  }

  PcdSet16S (PcdAmdFchI2c0SdaTxHold, Setup_Config->CbsCmnFchI2c0SdaTxHoldValue);

  PcdSet16S (PcdAmdFchI2c1SdaTxHold, Setup_Config->CbsCmnFchI2c1SdaTxHoldValue);

  PcdSet16S (PcdAmdFchI2c2SdaTxHold, Setup_Config->CbsCmnFchI2c2SdaTxHoldValue);

  PcdSet16S (PcdAmdFchI2c3SdaTxHold, Setup_Config->CbsCmnFchI2c3SdaTxHoldValue);

  PcdSet16S (PcdAmdFchI2c4SdaTxHold, Setup_Config->CbsCmnFchI2c4SdaTxHoldValue);

  PcdSet16S (PcdAmdFchI2c5SdaTxHold, Setup_Config->CbsCmnFchI2c5SdaTxHoldValue);

  PcdSet8S (PcdAmdFchI2c0SdaRxHold, Setup_Config->CbsCmnFchI2c0SdaRxHoldValue);

  PcdSet8S (PcdAmdFchI2c1SdaRxHold, Setup_Config->CbsCmnFchI2c1SdaRxHoldValue);

  PcdSet8S (PcdAmdFchI2c2SdaRxHold, Setup_Config->CbsCmnFchI2c2SdaRxHoldValue);

  PcdSet8S (PcdAmdFchI2c3SdaRxHold, Setup_Config->CbsCmnFchI2c3SdaRxHoldValue);

  PcdSet8S (PcdAmdFchI2c4SdaRxHold, Setup_Config->CbsCmnFchI2c4SdaRxHoldValue);

  PcdSet8S (PcdAmdFchI2c5SdaRxHold, Setup_Config->CbsCmnFchI2c5SdaRxHoldValue);

  PcdSet8S (PcdAmdFchI3c0SdaHold, Setup_Config->CbsCmnFchI3c0SdaHoldValue);

  PcdSet8S (PcdAmdFchI3c1SdaHold, Setup_Config->CbsCmnFchI3c1SdaHoldValue);

  PcdSet8S (PcdAmdFchI3c2SdaHold, Setup_Config->CbsCmnFchI3c2SdaHoldValue);

  PcdSet8S (PcdAmdFchI3c3SdaHold, Setup_Config->CbsCmnFchI3c3SdaHoldValue);

  if (Setup_Config->CbsCmnFchSataEnable != 0xf) {
    PcdSetBoolS (PcdSataEnable, Setup_Config->CbsCmnFchSataEnable);
  }

  //Check Display Condition CbsCmnFchSataEnable=Enabled
  if ((Setup_Config->CbsCmnFchSataEnable == 0x1)) {
    if (Setup_Config->CbsCmnFchSataClass != 0xf) {
      PcdSet8S (PcdSataClass, Setup_Config->CbsCmnFchSataClass);
    }
  }

  if (Setup_Config->CbsCmnFchSataRasSupport != 0xf) {
    PcdSetBoolS (PcdSataRasSupport, Setup_Config->CbsCmnFchSataRasSupport);
  }

  if (Setup_Config->CbsCmnFchSataStaggeredSpinup != 0xFF) {
    PcdSetBoolS (PcdSataStaggeredSpinup, Setup_Config->CbsCmnFchSataStaggeredSpinup);
  }

  if (Setup_Config->CbsCmnFchSataAhciDisPrefetchFunction != 0xf) {
    PcdSetBoolS (PcdSataAhciDisPrefetchFunction, Setup_Config->CbsCmnFchSataAhciDisPrefetchFunction);
  }

  if (Setup_Config->CbsDbgFchSataAggresiveDevSlpP0 != 0xF) {
    PcdSetBoolS (PcdSataDevSlpPort0, Setup_Config->CbsDbgFchSataAggresiveDevSlpP0);
  }

  if (Setup_Config->CbsDbgFchSataAggresiveDevSlpP1 != 0xF) {
    PcdSetBoolS (PcdSataDevSlpPort1, Setup_Config->CbsDbgFchSataAggresiveDevSlpP1);
  }

  if (Setup_Config->CbsCmnFchUsbXHCI0Enable != 0xf) {
    PcdSetBoolS (PcdXhci0Enable, Setup_Config->CbsCmnFchUsbXHCI0Enable);
  }

  if (Setup_Config->CbsCmnFchUsbXHCI1Enable != 0xf) {
    PcdSetBoolS (PcdXhci1Enable, Setup_Config->CbsCmnFchUsbXHCI1Enable);
  }

  if (Setup_Config->CbsCmnFchUsbXHCI2Enable != 0xf) {
    PcdSetBoolS (PcdXhci2Enable, Setup_Config->CbsCmnFchUsbXHCI2Enable);
  }

  if (Setup_Config->CbsCmnFchUsbXHCI3Enable != 0xf) {
    PcdSetBoolS (PcdXhci3Enable, Setup_Config->CbsCmnFchUsbXHCI3Enable);
  }

  if (Setup_Config->CbsCmnFchSystemPwrFailShadow != 0xf) {
    PcdSet8S (PcdPwrFailShadow, Setup_Config->CbsCmnFchSystemPwrFailShadow);
  }

  //Check Display Condition CbsCmnFchUart0Config = Enabled
  if ((Setup_Config->CbsCmnFchUart0Config == 0x1)) {
    if (Setup_Config->CbsCmnFchUart0LegacyConfig != 0xf) {
      PcdSet8S (FchUart0LegacyEnable, Setup_Config->CbsCmnFchUart0LegacyConfig);
    }
  }

  //Check Display Condition CbsCmnFchUart1Config = Enabled
  if ((Setup_Config->CbsCmnFchUart1Config == 0x1)) {
    if (Setup_Config->CbsCmnFchUart1LegacyConfig != 0xf) {
      PcdSet8S (FchUart1LegacyEnable, Setup_Config->CbsCmnFchUart1LegacyConfig);
    }
  }

  //Check Display Condition CbsCmnFchUart2Config = Enabled
  if ((Setup_Config->CbsCmnFchUart2Config == 0x1)) {
    if (Setup_Config->CbsCmnFchUart2LegacyConfig != 0xf) {
      PcdSet8S (FchUart2LegacyEnable, Setup_Config->CbsCmnFchUart2LegacyConfig);
    }
  }

  if (Setup_Config->CbsCmnFchAlinkRasSupport != 0xf) {
    PcdSetBoolS (PcdAmdFchAlinkRasSupport, Setup_Config->CbsCmnFchAlinkRasSupport);
  }

  if (Setup_Config->CbsDbgFchSyncfloodEnable != 0xf) {
    PcdSetBoolS (PcdResetCpuOnSyncFlood, Setup_Config->CbsDbgFchSyncfloodEnable);
  }

  //Check Display Condition CbsDbgFchSyncfloodEnable=Enable
  if ((Setup_Config->CbsDbgFchSyncfloodEnable == 0x1)) {
    PcdSet8S (PcdDelayResetCpuOnSyncFlood, Setup_Config->CbsDbgFchDelaySyncflood);
  }

  if (Setup_Config->CbsDbgFchSystemSpreadSpectrum != 0xf) {
    PcdSetBoolS (PcdSpreadSpectrum, Setup_Config->CbsDbgFchSystemSpreadSpectrum);
  }

  if (Setup_Config->CbsCmnBootTimerEnable != 2) {
    PcdSetBoolS (PcdBootTimerEnable, Setup_Config->CbsCmnBootTimerEnable);
  }

  if (Setup_Config->CbsCmnSP3NtbP0P0 != 0xF) {
    PcdSetBoolS (PcdCfgNTBP0P0, Setup_Config->CbsCmnSP3NtbP0P0);
  }

  //Check Display Condition CbsCmnSP3NtbP0P0=Enable
  if ((Setup_Config->CbsCmnSP3NtbP0P0 == 0x1)) {
    PcdSet8S (PcdCfgNTBStartLaneP0P0, Setup_Config->CbsCmnSP3NtbStartLaneP0P0);
  }

  //Check Display Condition CbsCmnSP3NtbP0P0=Enable
  if ((Setup_Config->CbsCmnSP3NtbP0P0 == 0x1)) {
    PcdSet8S (PcdCfgNTBEndLaneP0P0, Setup_Config->CbsCmnSP3NtbEndLaneP0P0);
  }

  //Check Display Condition CbsCmnSP3NtbP0P0=Enable
  if ((Setup_Config->CbsCmnSP3NtbP0P0 == 0x1)) {
    if (Setup_Config->CbsCmnSP3NtbLinkSpeedP0P0 != 0xf) {
      PcdSet8S (PcdCfgNTBLinkSpeedP0P0, Setup_Config->CbsCmnSP3NtbLinkSpeedP0P0);
    }
  }

  //Check Display Condition CbsCmnSP3NtbP0P0=Enable
  if ((Setup_Config->CbsCmnSP3NtbP0P0 == 0x1)) {
    if (Setup_Config->CbsCmnSP3NtbModeP0P0 != 0xf) {
      PcdSet8S (PcdCfgNTBModeP0P0, Setup_Config->CbsCmnSP3NtbModeP0P0);
    }
  }

  if (Setup_Config->CbsCmnSP3NtbP0P2 != 0xF) {
    PcdSetBoolS (PcdCfgNTBP0P2, Setup_Config->CbsCmnSP3NtbP0P2);
  }

  //Check Display Condition CbsCmnSP3NtbP0P2=Enable
  if ((Setup_Config->CbsCmnSP3NtbP0P2 == 0x1)) {
    PcdSet8S (PcdCfgNTBStartLaneP0P2, Setup_Config->CbsCmnSP3NtbStartLaneP0P2);
  }

  //Check Display Condition CbsCmnSP3NtbP0P2=Enable
  if ((Setup_Config->CbsCmnSP3NtbP0P2 == 0x1)) {
    PcdSet8S (PcdCfgNTBEndLaneP0P2, Setup_Config->CbsCmnSP3NtbEndLaneP0P2);
  }

  //Check Display Condition CbsCmnSP3NtbP0P2=Enable
  if ((Setup_Config->CbsCmnSP3NtbP0P2 == 0x1)) {
    if (Setup_Config->CbsCmnSP3NtbLinkSpeedP0P2 != 0xf) {
      PcdSet8S (PcdCfgNTBLinkSpeedP0P2, Setup_Config->CbsCmnSP3NtbLinkSpeedP0P2);
    }
  }

  //Check Display Condition CbsCmnSP3NtbP0P2=Enable
  if ((Setup_Config->CbsCmnSP3NtbP0P2 == 0x1)) {
    if (Setup_Config->CbsCmnSP3NtbModeP0P2 != 0xf) {
      PcdSet8S (PcdCfgNTBModeP0P2, Setup_Config->CbsCmnSP3NtbModeP0P2);
    }
  }

  PcdSetBoolS (PcdAmdPspEinjSupport, Setup_Config->CbsDfCmnPspErrInj);

  if (Setup_Config->CbsCmnSocFarSwitch != 0xFF) {
    PcdSetBoolS (PcdAmdPspAntiRollbackLateSplFuse, Setup_Config->CbsCmnSocFarSwitch);
  }

  if (Setup_Config->CbsCmnCxlControl != 0xf) {
    PcdSetBoolS (PcdAmdCxlOnAllPorts, Setup_Config->CbsCmnCxlControl);
  }

  if (Setup_Config->CbsCmnCxlSdpReqSysAddr != 0xFF) {
    PcdSet8S (PcdAmdCxlSpaEnable, Setup_Config->CbsCmnCxlSdpReqSysAddr);
  }

  if (Setup_Config->CbsCmnCxlSpm != 0xf) {
    PcdSetBoolS (PcdAmdCxlSpm, Setup_Config->CbsCmnCxlSpm);
  }

  if (Setup_Config->CbsCmnCxlDvsecLock != 0xf) {
    PcdSetBoolS (PcdAmdCxlDvsecLock, Setup_Config->CbsCmnCxlDvsecLock);
  }

  if (Setup_Config->CbsCmnCxlHdmDecoderLockOnCommit != 0xf) {
    PcdSetBoolS (PcdAmdCxlHdmDecoderLockOnCommit, Setup_Config->CbsCmnCxlHdmDecoderLockOnCommit);
  }

  if (Setup_Config->CbsCmnCxlTempGen5Advertisement != 0xF) {
    PcdSetBoolS (PcdCxlTempGen5AdvertAltPtcl, Setup_Config->CbsCmnCxlTempGen5Advertisement);
  }

  if (Setup_Config->CbsCmnSyncHeaderByPass != 0xf) {
    PcdSetBoolS (PcdSyncHeaderByPass, Setup_Config->CbsCmnSyncHeaderByPass);
  }

  if (Setup_Config->CbsCxlSyncHeaderBypassCompMode != 0xF) {
    PcdSetBoolS (PcdCxlSyncHeaderByPassCompMode, Setup_Config->CbsCxlSyncHeaderBypassCompMode);
  }

  if (Setup_Config->CbsDbgCxlOverideCxlMemorySize != 0xFF) {
    PcdSet8S (PcdTruncateCxlMemory, Setup_Config->CbsDbgCxlOverideCxlMemorySize);
  }

  PcdSet8S (PcdAmdCxlProtocolErrorReporting, Setup_Config->CbsCmnCxlProtocolErrorReporting);

  PcdSet8S (PcdAmdCxlComponentErrorReporting, Setup_Config->CbsCmnCxlComponentErrorReporting);

  if (Setup_Config->CbsCmnCxlMemIsolationEnable != 0xf) {
    PcdSetBoolS (PcdAmdCxlMemIsolationEnable, Setup_Config->CbsCmnCxlMemIsolationEnable);
  }

  if (Setup_Config->CbsCmnCxlMemIsolationFwNotification != 0xF) {
    PcdSetBoolS (PcdAmdCxlMemIsolationFwNotification, Setup_Config->CbsCmnCxlMemIsolationFwNotification);
  }


}
