#pragma message( "Compal Server Override Compiling-" __FILE__ )
//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2023, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************

/** @file SystemInformation.c

    This code retrieves the system information and prints it to the video/serial output
*/

#include <SystemInformation.h>

static EFI_PEI_NOTIFY_DESCRIPTOR  mPeiSystemInfoNotifyList[] = {
  {
    (EFI_PEI_PPI_DESCRIPTOR_NOTIFY_DISPATCH | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST),
    &SYSTEM_INFO_CALLBACK_GUID,
    OutputSystemInfo
  }
};

/**
    Outputs BIOS information to Pei Early Video

    @param  EarlyVideoPpi - pointer to the Early Video Ppi services.
    
    @retval EFI_STATUS
*/

EFI_STATUS
OutputBiosVersion (
    IN CONST EFI_PEI_SERVICES     **PeiServices
)
{   
    EFI_STATUS                  Status;
    CHAR16                      TextString[160];
    CHAR8                       *BiosBuildDate = {CONVERT_TO_STRING(TODAY)};
    AMI_EARLY_CONSOLE_OUT_PPI   *AmiEarlyConsoleOutPpi;
    
    Status = PeiServicesLocatePpi (
                        &gAmiEarlyConsoleOutPpiGuid,
                        0,
                        NULL,
                        (VOID**)&AmiEarlyConsoleOutPpi);
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR,"[PEI System Information] : %a : Locate AmiEarlyConsoleOutPpi : %r\n",__FUNCTION__, Status));
        return Status;
    }
    Status = AmiEarlyConsoleOutPpi->SetAttribute (POST_MSG_FOREGROUND | POST_MSG_BACKGROUND | DISPLAY_BLINK);
    
    DEBUG((DEBUG_INFO,"[PEI System Information] : %a \n",__FUNCTION__));

    UnicodeSPrint (TextString, sizeof(TextString), L" System Information: \n");
    Status = AmiEarlyConsoleOutPpi->OutputString (EarlyConsoleDisplayFrameInfo, TextString);
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR," %a() WriteString:%r \n", __FUNCTION__, Status));
        return Status;
    }

    //Remove the first and last chars (") from bios build date .
    BiosBuildDate++;
    *(CHAR8*)(BiosBuildDate + AsciiStrLen (BiosBuildDate) -1 ) = '\0';
    
    if (StrCmp (PcdGetPtr(AmiPcdEarlyConsolePlatformName), DEFAULT_PLATFORM_NAME) != 0) {
        UnicodeSPrint (TextString, sizeof(TextString), L" Platform Name: %s, System BIOS Version: %a\n Core Version: %d.%d, Build Date: %a \n", 
                     PcdGetPtr (AmiPcdEarlyConsolePlatformName), CONVERT_TO_STRING(BIOS_TAG),
                     CORE_MAJOR_VERSION, CORE_BUILD_NUMBER, BiosBuildDate);
    } else {
        UnicodeSPrint (TextString, sizeof(TextString), L" Platform Name: %a, System BIOS Version: %a\n Core Version: %d.%d, Build Date: %a \n", 
                     CONVERT_TO_STRING(EARLY_CONSOLE_PLATFORM_NAME), CONVERT_TO_STRING(BIOS_TAG), 
                     CORE_MAJOR_VERSION, CORE_BUILD_NUMBER, BiosBuildDate);
    }
//COMPAL_CHANGE >>>
    UnicodeSPrint (TextString, sizeof(TextString), L" System BIOS Version: %02d.%02d.%02d, Build Date: %a \n",
                 BOARD_REVISION, BIOS_VERSION, AUX_NUMBER, BiosBuildDate);
//COMPAL_CHANGE <<<

    Status = AmiEarlyConsoleOutPpi->OutputString (EarlyConsoleDisplayFrameInfo, TextString);
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR," %a() WriteString:%r \n", __FUNCTION__, Status));
        return Status;
    }
    
    Status = PlatformInitializeSystemData ();
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR," %a() PlatformInitializeSystemData:%r \n", __FUNCTION__, Status));
        return Status;
    }
    
    return Status;
}

/**
    Outputs CPU information to Pei Early Video

    @param  **PeiServices - pointer to the PEI services.
    @param  EarlyVideoPpi - pointer to the Early Video Ppi services.
    
    @retval EFI_STATUS
*/

EFI_STATUS
EFIAPI
OutputCpuInformation (
    IN EFI_PEI_SERVICES               **PeiServices
)
{
    EFI_STATUS          Status;

    DEBUG((DEBUG_INFO,"[PEI System Information] %a \n", __FUNCTION__));

    Status = PlatformInitializeCpuData (PeiServices);
    
    return Status;
}

/**
    Outputs Memory and DIMMs information to Pei Early Video

    @param  **PeiServices - pointer to the PEI services.
    @param  EarlyVideoPpi - pointer to the Early Video Ppi services.
    
    @retval EFI_STATUS
*/

EFI_STATUS
EFIAPI
OutputDimmInformation (
    IN EFI_PEI_SERVICES               **PeiServices
)
{
    EFI_STATUS          Status;

    DEBUG((DEBUG_INFO,"[PEI System Information] : %a \n", __FUNCTION__));

    Status = PlatformInitializeDimmData (PeiServices);

    return Status;
}

/**
    Outputs system information to Pei Early Video

    @param  **PeiServices - pointer to the PEI services.
    @param  NotifyDescriptor - pointer to descriptor
    @param  Ppi - void pointer 
    
    @retval EFI_STATUS
*/

EFI_STATUS
EFIAPI
OutputSystemInfo (
    IN EFI_PEI_SERVICES     **PeiServices,
    IN EFI_PEI_NOTIFY_DESCRIPTOR  *NotifyDescriptor,
    IN VOID                       *Ppi 
)
{
    EFI_STATUS                      Status;
    AMI_EARLY_CONSOLE_OUT_PPI       *AmiEarlyConsoleOutPpi;
    
    Status = PeiServicesLocatePpi (
                        &gAmiEarlyConsoleOutPpiGuid,
                        0,
                        NULL,
                        (VOID**)&AmiEarlyConsoleOutPpi);
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR,"[PEI System Information] : %a : Locate AmiEarlyConsoleOutPpi : %r\n",__FUNCTION__, Status));
        return Status;
    }
    
    
    DEBUG((DEBUG_INFO,"[PEI System Information] : %a \n",__FUNCTION__));

    Status = OutputCpuInformation (PeiServices);
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR,"[PEI System Information] : %a : OutputCpuInformation : %r\n",__FUNCTION__, Status));
    }
    
    Status = OutputDimmInformation(PeiServices);
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR,"[PEI System Information] : %a : OutputDimmInformation : %r\n",__FUNCTION__, Status));
    }
    return Status;
}

/**
    Registers notification for system information components
    
    @param  FileHandle - Pointer to the FFS file header of the image.
    @param  **PeiServices - pointer to the PEI services.
 
    @retval EFI_STATUS
*/

EFI_STATUS
EFIAPI
PeiSystemInformationEntryPoint (
    IN        EFI_PEI_FILE_HANDLE  FileHandle,
    IN  CONST EFI_PEI_SERVICES     **PeiServices 
)
{
    EFI_STATUS                      Status;
    EFI_PEI_NOTIFY_DESCRIPTOR       *pNotifyList;
    
    DEBUG((DEBUG_INFO,"[PEI System Information] : %a \n",__FUNCTION__));
    
    Status = OutputBiosVersion (PeiServices);
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR,"[PEI System Information] : %a : OutputBiosVersion : %r\n",__FUNCTION__, Status));
    }
        
    pNotifyList = &mPeiSystemInfoNotifyList[0];
    Status = (*PeiServices)->NotifyPpi(PeiServices, pNotifyList);

    return Status;
}
