//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************

/** @file EarlyConsoleOutInterfacePei.c

*/

#include <PiPei.h>
#include <Library/PeiServicesLib.h>
#include <EarlyConsoleDisplay.h>
#include <AmiEarlyConsoleOutInterface.h>

AMI_EARLY_CONSOLE_OUT_PPI gAmiEarlyConsoleOutPpi = {
    EarlyConsoleSetAttribute,
    EarlyConsoleOutputString,
    EarlyConsoleBlt,
    EarlyConsoleDisplayCheckPoint,
    EarlyConsoleDisplayProgressBar,
    EarlyConsoleReportHotkey
};

EFI_PEI_PPI_DESCRIPTOR gEarlyConsoleOutPpiList = {
    (EFI_PEI_PPI_DESCRIPTOR_PPI | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST),
    &gAmiEarlyConsoleOutPpiGuid,
    &gAmiEarlyConsoleOutPpi
};

VOID
ProcessString (
  IN AMI_SIMPLE_TEXT_OUTPUT_PPI             *SimpleTextOutPpi,
  IN AMI_GRAPHICS_OUTPUT_PPI                *GraphicsOutPutPpi,
  IN AMI_EARLY_GRAPHICS_FRAME_INFO          *FrameInfo,
  IN CHAR16                                 *String
);

/**
   Function to set the foreground and background color of the console devices

   @param Attribute         Attributes to set

   @retval EFI_SUCCESS attribute was changed successfully
   @retval EFI_DEVICE_ERROR device had an error
   @retval EFI_UNSUPPORTED attribute is not supported
**/
EFI_STATUS
EarlyConsoleSetAttribute (
    IN UINTN  Attribute
)
{
    
    EFI_STATUS                              Status = EFI_SUCCESS;
    EARLY_CONSOLE_DISPLAY_FRAME_INFO_HOB    *FrameInfoHobDataPtr = NULL;
    AMI_SIMPLE_TEXT_OUTPUT_PPI              *SimpleTextOutPpi;
    EFI_HOB_GUID_TYPE                       *GuidHob;
    UINT8                                   Instance;
    
    GuidHob = GetFirstGuidHob (&gAmiEarlyConsoleDisplayFrameInfoHobGuid);
    if (GuidHob == NULL) {
        DEBUG ((DEBUG_ERROR, "%a() Frame Info Not initialized yet..\n", __FUNCTION__));
        FrameInfoHobDataPtr = EarlyConsoleInitFrames ();
        if (FrameInfoHobDataPtr == NULL) {
            DEBUG ((DEBUG_ERROR, "%a() Frame Info initialization failed!!!\n", __FUNCTION__));
            return EFI_NOT_READY;
        }
    } else {
        FrameInfoHobDataPtr = GET_GUID_HOB_DATA (GuidHob);
    }
    
    for (Instance = 0; Instance < FixedPcdGet8 (AmiPcdSimpleTextOutMaxPpiSupported); Instance++) {
        
        if (!FrameInfoHobDataPtr->DisplayFrameInfo[Instance].IsDisplayFrameInfoValid) {
            continue;
        }
        
        SimpleTextOutPpi = FrameInfoHobDataPtr->DisplayFrameInfo[Instance].SimpleTextOut.SimpleTextOutPpi;
        if (SimpleTextOutPpi == NULL) {
            continue;
        }
        
        Status = SimpleTextOutPpi->SetAttribute (
                                        SimpleTextOutPpi, 
                                        Attribute );
    }
    
    return Status;
}

/**
    Function that writes a Unicode string to all the output devices

    @param DisplayFrameType Frame type to output the string
    @param String           Pointer to the string to output

    @retval EFI_SUCCESS - function executed successfully
    @retval EFI_DEVICE_ERROR - Error occurred during output string
    @retval EFI_WARN_UNKNOWN_GLYPH - Some of characters were skipped during output
**/
EFI_STATUS
EarlyConsoleOutputString (
    IN EARLY_CONSOLE_DISPLAY_FRAME_TYPE     DisplayFrameType,
    IN CHAR16                               *String
)
{
    EFI_HOB_GUID_TYPE                       *GuidHob;
    EARLY_CONSOLE_DISPLAY_FRAME_INFO_HOB    *FrameInfoHobDataPtr = NULL;
    AMI_SIMPLE_TEXT_OUTPUT_PPI              *SimpleTextOutPpi;
    AMI_EARLY_GRAPHICS_FRAME_INFO           *FrameInfo;
    UINT8                                   Instance;
    
    if ((String == NULL) || (DisplayFrameType >= EarlyConsoleDisplayFrameMax)) {
        return EFI_INVALID_PARAMETER;
    }
            
    GuidHob = GetFirstGuidHob (&gAmiEarlyConsoleDisplayFrameInfoHobGuid);
    if (GuidHob == NULL) {
        DEBUG ((DEBUG_INFO, "%a() Frame Info Not initialized yet...\n", __FUNCTION__));
        FrameInfoHobDataPtr = EarlyConsoleInitFrames ();
        if (FrameInfoHobDataPtr == NULL) {
            DEBUG ((DEBUG_ERROR, "%a() Frame Info initialization failed!!!\n", __FUNCTION__));
            return EFI_NOT_READY;
        }
    } else {
        FrameInfoHobDataPtr = GET_GUID_HOB_DATA (GuidHob);
    }

    //Update string to Hob to update to new identified device after memory discovered.   
    UpdateStringInHob (DisplayFrameType, String);
    
    //Set cursor position in respective Frame
    for (Instance = 0; Instance < FixedPcdGet8 (AmiPcdSimpleTextOutMaxPpiSupported); Instance++) {
        
        if (!FrameInfoHobDataPtr->DisplayFrameInfo[Instance].IsDisplayFrameInfoValid ||
            !FrameInfoHobDataPtr->DisplayFrameInfo[Instance].FrameInfo[DisplayFrameType].IsFrameInfoValid) {
            continue;
        }
        
        SimpleTextOutPpi = FrameInfoHobDataPtr->DisplayFrameInfo[Instance].SimpleTextOut.SimpleTextOutPpi;
        FrameInfo        = &FrameInfoHobDataPtr->DisplayFrameInfo[Instance].FrameInfo[DisplayFrameType];
        
        if (SimpleTextOutPpi->ConsoleType == PeiSimpleTextOutConsoleTypeSerial) {
            SimpleTextOutPpi->OutputString (SimpleTextOutPpi, 
                                            String);
        } else {
            // Set cursor to end of previous string in frame
            SimpleTextOutPpi->SetCursorPosition (
                                        SimpleTextOutPpi, 
                                        FrameInfo->CurrentColumn, 
                                        FrameInfo->CurrentRow);
        
            ProcessString (
                    SimpleTextOutPpi,
                    FrameInfoHobDataPtr->DisplayFrameInfo[Instance].GraphicsOutput.GraphicsOutputPpi,
                    FrameInfo, 
                    String);
        }
    }
    
    return EFI_SUCCESS;
}

/**
    Choose frame by its type and display BLT buffer
    
    @param DisplayFrameType
    @param BltBuffer
    @param BltOperation
    @param SourceX
    @param SourceY
    @param Width
    @param Height
    @param AlignCenter      - If TRUE, displays BLT buffer at center of the frame
                            - If FALSE, displays BLT buffer at top left corner of the frame
    
    @return EFI_STATUS
**/
EFI_STATUS
EarlyConsoleBlt (
    IN  EARLY_CONSOLE_DISPLAY_FRAME_TYPE        DisplayFrameType,
    IN  EFI_GRAPHICS_OUTPUT_BLT_PIXEL           *BltBuffer,
    IN  EFI_GRAPHICS_OUTPUT_BLT_OPERATION       BltOperation,
    IN  UINTN                                   SourceX,
    IN  UINTN                                   SourceY,
    IN  UINTN                                   Width,
    IN  UINTN                                   Height,
    IN  BOOLEAN                                 AlignCenter
)
{
    EFI_STATUS                              Status;
    EFI_HOB_GUID_TYPE                       *GuidHob;
    EARLY_CONSOLE_DISPLAY_FRAME_INFO_HOB    *FrameInfoHobDataPtr;
    AMI_GRAPHICS_OUTPUT_PPI                 *GraphicsOutPutPpi;
    AMI_EARLY_GRAPHICS_FRAME_INFO           *FrameInfo;
    UINT8                                   Instance;
    UINTN                                   StartX;
    UINTN                                   StartY;
    
    if ((BltBuffer == NULL) || (DisplayFrameType >= EarlyConsoleDisplayFrameMax)) {
        return EFI_INVALID_PARAMETER;
    }
            
    GuidHob = GetFirstGuidHob (&gAmiEarlyConsoleDisplayFrameInfoHobGuid);
    if (GuidHob == NULL) {
        DEBUG ((DEBUG_INFO, "%a() Frame Info Not initialized yet...\n", __FUNCTION__));
        FrameInfoHobDataPtr = EarlyConsoleInitFrames ();
        if (FrameInfoHobDataPtr == NULL) {
            DEBUG ((DEBUG_ERROR, "%a() Frame Info initialization failed!!!\n", __FUNCTION__));
            return EFI_NOT_READY;
        }
    } else {
        FrameInfoHobDataPtr = GET_GUID_HOB_DATA (GuidHob);
    }

    // If yes, the change in DXE
    for (Instance = 0; Instance < FixedPcdGet8 (AmiPcdSimpleTextOutMaxPpiSupported); Instance++) {
        
        if (!FrameInfoHobDataPtr->DisplayFrameInfo[Instance].IsDisplayFrameInfoValid ||
            !FrameInfoHobDataPtr->DisplayFrameInfo[Instance].FrameInfo[DisplayFrameType].IsFrameInfoValid) {
            continue;
        }
        
        GraphicsOutPutPpi = FrameInfoHobDataPtr->DisplayFrameInfo[Instance].GraphicsOutput.GraphicsOutputPpi;
        FrameInfo         = &FrameInfoHobDataPtr->DisplayFrameInfo[Instance].FrameInfo[DisplayFrameType];
        
        if ((FrameInfo->FrameWidth  < (UINT32)Width) ||
            (FrameInfo->FrameHeight < (UINT32)Height)) {
            continue;
        }
        
        if (AlignCenter) {
            StartX = FrameInfo->StartX + (FrameInfo->FrameWidth - Width) / 2;
            StartY = FrameInfo->StartY + (FrameInfo->FrameHeight - Height) / 2;
        } else {
            StartX = FrameInfo->StartX;
            StartY = FrameInfo->StartY;
        }
        
        Status = GraphicsOutPutPpi->Blt (
                                    GraphicsOutPutPpi,
                                    BltBuffer,
                                    BltOperation,
                                    SourceX,
                                    SourceY,
                                    StartX,
                                    StartY,
                                    Width,
                                    Height,
                                    0 );
        DEBUG ((DEBUG_INFO, "%a() Blt Status - %r\n", __FUNCTION__, Status));
    }
    
    return EFI_SUCCESS;
}

/**
    Displays Checkpoint in PostCodeFrame
    
    @param CheckPoint
    
    @return EFI_STATUS
**/
EFI_STATUS
EarlyConsoleDisplayCheckPoint (
    IN UINT8  CheckPoint
)
{
    EFI_STATUS                              Status;
    EFI_HOB_GUID_TYPE                       *GuidHob;
    EARLY_CONSOLE_DISPLAY_FRAME_INFO_HOB    *FrameInfoHobDataPtr;
    AMI_EARLY_GRAPHICS_FRAME_INFO           *FrameInfo = NULL;
    UINT8                                   Instance;
    UINT32                                  BackupColumn;
    CHAR16                                  CheckPointStr[3];
            
    GuidHob = GetFirstGuidHob (&gAmiEarlyConsoleDisplayFrameInfoHobGuid);
    if (GuidHob == NULL) {
        DEBUG ((DEBUG_INFO, "%a() Frame Info Not initialized yet...\n", __FUNCTION__));
        FrameInfoHobDataPtr = EarlyConsoleInitFrames ();
        if (FrameInfoHobDataPtr == NULL) {
            DEBUG ((DEBUG_ERROR, "%a() Frame Info initialization failed!!!\n", __FUNCTION__));
            return EFI_NOT_READY;
        }
    } else {
        FrameInfoHobDataPtr = GET_GUID_HOB_DATA (GuidHob);
    }
    
    for (Instance = 0; Instance < FixedPcdGet8 (AmiPcdSimpleTextOutMaxPpiSupported); Instance++) {
        if (!FrameInfoHobDataPtr->DisplayFrameInfo[Instance].IsDisplayFrameInfoValid ||
            !FrameInfoHobDataPtr->DisplayFrameInfo[Instance].FrameInfo[EarlyConsoleDisplayFramePostCode].IsFrameInfoValid) {
            continue;
        }
        
        FrameInfo = &FrameInfoHobDataPtr->DisplayFrameInfo[Instance].FrameInfo[EarlyConsoleDisplayFramePostCode];
        break;
    }
    
    // Checkpoint frame not found
    if (FrameInfo == NULL) {
        return EFI_UNSUPPORTED;
    }
    
    // Backup current column of CheckPointFrame
    BackupColumn = FrameInfo->CurrentColumn;

    UnicodeSPrint (CheckPointStr, sizeof(CheckPointStr), L"%02X", CheckPoint);
    Status = EarlyConsoleOutputString (EarlyConsoleDisplayFramePostCode, CheckPointStr);
    if (!EFI_ERROR(Status)) {
        // Restore the column position. So that, Checkpoint will be displayed in same position
        FrameInfo->CurrentColumn = BackupColumn;
    }

    return Status;
}

/**
    Displays progress bar in ProgressBarFrame
    
    @param Completion
    
    @return EFI_STATUS
**/
EFI_STATUS
EarlyConsoleDisplayProgressBar (
    UINT8   Completion
)
{
    UINT8                                   Index;
    EFI_GRAPHICS_OUTPUT_BLT_PIXEL           *FgColor = FixedPcdGetPtr (AmiPcdProgressBarForegroundColor);
    EFI_GRAPHICS_OUTPUT_BLT_PIXEL           *BgColor = FixedPcdGetPtr (AmiPcdProgressBarBackgroundColor);
    UINT32                                  BlockWidth;
    UINT32                                  BlockHeight;
    UINT32                                  Temp;
    AMI_EARLY_GRAPHICS_FRAME_INFO           *FrameInfo = NULL;
    EFI_HOB_GUID_TYPE                       *GuidHob;
    EARLY_CONSOLE_DISPLAY_FRAME_INFO_HOB    *FrameInfoHobDataPtr;
    AMI_GRAPHICS_OUTPUT_PPI                 *GraphicsOutputPpi;
    EFI_GRAPHICS_OUTPUT_BLT_PIXEL           *BorderColor = FixedPcdGetPtr (AmiPcdProgressBarBorderColor);
    UINT8                                   BorderWidth = 3;
    
    GuidHob = GetFirstGuidHob (&gAmiEarlyConsoleDisplayFrameInfoHobGuid);
    if (GuidHob == NULL) {
        DEBUG ((DEBUG_INFO, "%a() Frame Info Not initialized yet...\n", __FUNCTION__));
        FrameInfoHobDataPtr = EarlyConsoleInitFrames ();
        if (FrameInfoHobDataPtr == NULL) {
            DEBUG ((DEBUG_ERROR, "%a() Frame Info initialization failed!!!\n", __FUNCTION__));
            return EFI_NOT_READY;
        }
    } else {
        FrameInfoHobDataPtr = GET_GUID_HOB_DATA (GuidHob);
    }
    
    for (Index = 0; Index < FixedPcdGet8 (AmiPcdSimpleTextOutMaxPpiSupported); Index++) {
        if (!FrameInfoHobDataPtr->DisplayFrameInfo[Index].IsDisplayFrameInfoValid ||
            !FrameInfoHobDataPtr->DisplayFrameInfo[Index].FrameInfo[EarlyConsoleDisplayFrameProgressBar].IsFrameInfoValid) {
            continue;
        }
        
        FrameInfo         = &FrameInfoHobDataPtr->DisplayFrameInfo[Index].FrameInfo[EarlyConsoleDisplayFrameProgressBar];
        GraphicsOutputPpi = FrameInfoHobDataPtr->DisplayFrameInfo[Index].GraphicsOutput.GraphicsOutputPpi;
        break;
    }
    
    // Progressbar frame not found
    if (FrameInfo == NULL) {
        return EFI_UNSUPPORTED;
    }
    
    // Progress bar width will cover approx 70% of screen
    Temp = FrameInfo->FrameWidth * 70 / 100;
    BlockWidth = Temp / 100;
    if (Temp % 100 != 0) {
        BlockWidth++;
    }

    // Progress bar height will cover approx 80% of ProgressBarFrame
    BlockHeight = FrameInfo->FrameHeight * 80 / 100;

    FrameInfo->DeltaX = (FrameInfo->FrameWidth - (BlockWidth * 100)) / 2;
    FrameInfo->DeltaY = (FrameInfo->FrameHeight - BlockHeight) / 2;
    
    // Check range
    if (Completion > 100) {
        return EFI_INVALID_PARAMETER;
    }

    // Check to see if this Completion percentage has already been displayed
    if (Completion == FrameInfo->CurrentPercentage) {
        return EFI_SUCCESS;
    }
    
    // Do special init on first call of each progress session
    if (FrameInfo->CurrentPercentage == 100) {
        // Draw progress bar background
        GraphicsOutputPpi->Blt (
                            GraphicsOutputPpi,
                            BorderColor,
                            EfiBltVideoFill,
                            0,
                            0,
                            FrameInfo->StartX + FrameInfo->DeltaX - BorderWidth,
                            FrameInfo->StartY + FrameInfo->DeltaY - BorderWidth,
                            (BlockWidth * 100) + (BorderWidth * 2),
                            BlockHeight + (BorderWidth * 2),
                            0 );

        GraphicsOutputPpi->Blt (
                            GraphicsOutputPpi,
                            BgColor,
                            EfiBltVideoFill,
                            0,
                            0,
                            FrameInfo->StartX + FrameInfo->DeltaX,
                            FrameInfo->StartY + FrameInfo->DeltaY,
                            (BlockWidth * 100),
                            BlockHeight,
                            0 );

        // Clear previous
        FrameInfo->CurrentPercentage = 0;
    }    

    // Can not update progress bar if Completion is less than previous
    if (Completion < FrameInfo->CurrentPercentage) {
        DEBUG ((DEBUG_WARN, "WARNING: Completion (%d) should not be less than Previous (%d)!!!\n", Completion, FrameInfo->CurrentPercentage));
        return EFI_INVALID_PARAMETER;
    }
    
    GraphicsOutputPpi->Blt (
                        GraphicsOutputPpi,
                        FgColor,
                        EfiBltVideoFill,
                        0,
                        0,
                        FrameInfo->StartX + FrameInfo->DeltaX + BorderWidth,
                        FrameInfo->StartY + FrameInfo->DeltaY + BorderWidth,
                        (BlockWidth * Completion) - (BorderWidth * 2),
                        BlockHeight - (BorderWidth * 2),
                        0 );

    FrameInfo->CurrentPercentage = Completion;
    return EFI_SUCCESS;
}

/**
    Function to report that hotkey is detected

    @param String     Hotkey String

    @retval EFI_SUCCESS function executed successfully
**/
EFI_STATUS
EarlyConsoleReportHotkey (
    IN CHAR8       *String
)
{
    return EFI_UNSUPPORTED;
}

/**
    Entry point of EarlyConsoleOutInterfacePei driver
    
    @param  FileHandle - Pointer to the FFS file header of the image.
    @param  **PeiServices - pointer to the PEI services.
 
    @retval EFI_STATUS
*/
EFI_STATUS
EFIAPI
EarlyConsoleOutInterfacePeiEntryPoint (
    IN        EFI_PEI_FILE_HANDLE  FileHandle,
    IN  CONST EFI_PEI_SERVICES     **PeiServices 
)
{
    EFI_STATUS   Status;
      
    Status = PeiServicesInstallPpi (&gEarlyConsoleOutPpiList);

    if (EFI_ERROR (Status)) {
        DEBUG((DEBUG_ERROR,"%a(): InstallPpi for AmiEarlyConsoleOut PPI Status:%r", __FUNCTION__, Status));
        return Status;
    }

    return EFI_SUCCESS;
}
