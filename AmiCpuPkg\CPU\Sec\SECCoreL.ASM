.686p
.xmm
.model small,c


	TITLE	SECCore.ASM -- KERNEL TABLES / PROCEDURES 

;-------- DO NOT EDIT THIS FILE --------
;
; FILE WAS GENERATED AUTOMATICALY USING AMISDL v7.04.0351 (Jul 24 2025,19:57:04)
;
;-------- DO NOT EDIT THIS FILE --------
;***********************************************************************
;*                                                                     *
;*                 Copyright (c) 1985 - 2025, AMI.                     *
;*                                                                     *
;*      All rights reserved. Subject to AMI licensing agreement.       *
;*                                                                     *
;***********************************************************************
;---------------------------------------------------------------------------
;	INCLUDE FILES
;---------------------------------------------------------------------------

	INCLUDE mbiosequ.equ
	INCLUDE mbiosmac.mac
	INCLUDE equates.equ

;---------------------------------------------------------------------------
;	EXTERNS USED
;---------------------------------------------------------------------------

	mEXTERN_NEAR32 SECCPU_EarlyInit
	mEXTERN_NEAR32 SECSB_EarlyInit
	mEXTERN_NEAR32 SecCrb_EarlyInit
	mEXTERN_NEAR32 GetApInitVector

;---------------------------------------------------------------------------
;	SEGMENTS USED
;---------------------------------------------------------------------------

STARTUP_SEG SEGMENT PARA PUBLIC 'CODE' USE32
STARTUP_SEG ENDS


;---------------------------------------------------------------------------
;	STARTUP_SEG  S E G M E N T  STARTS
;---------------------------------------------------------------------------
STARTUP_SEG SEGMENT PARA PUBLIC 'CODE' USE32

;<AMI_THDR_START>
;---------------------------------------------------------------------------
;
; Name:		SECCoreAtPowerOn
;
; Type:		eLink Table
;
; Description:	SEC Core Init after power-on and before memory detection.
;
; Referrals:	
;
; Notes:	eLink comments display the name of immediate Parent.
;
;---------------------------------------------------------------------------
;<AMI_THDR_END>

mSTART_PROC_NEAR32 SECCoreAtPowerOn
	mBODY_JMP_PROC_NEAR32 0FFFFh, SECCPU_EarlyInit
	mBODY_JMP_PROC_NEAR32 0FFFFh, SECSB_EarlyInit
	mBODY_JMP_PROC_NEAR32 0FFFFh, SecCrb_EarlyInit
mEND_PROC_WITH_JMP_NEAR32 SECCoreAtPowerOn

;<AMI_THDR_START>
;---------------------------------------------------------------------------
;
; Name:		SECCoreAPinit
;
; Type:		eLink Table
;
; Description:	AP SEC Core Init after power-on and before memory detection.
;
; Referrals:	
;
; Notes:	eLink comments display the name of immediate Parent.
;
;---------------------------------------------------------------------------
;<AMI_THDR_END>

mSTART_PROC_NEAR32 SECCoreAPinit
	mBODY_JMP_PROC_NEAR32 0FFFFh, GetApInitVector
mEND_PROC_WITH_JMP_NEAR32 SECCoreAPinit

;<AMI_THDR_START>
;---------------------------------------------------------------------------
;
; Name:		BeforeSECEntry
;
; Type:		eLink Table
;
; Description:	Provide an opportunity for OEM to adjust the FV_BB_BASE, stack is available.
;
; Referrals:	
;
; Notes:	eLink comments display the name of immediate Parent.
;
;---------------------------------------------------------------------------
;<AMI_THDR_END>

mSTART_PROC_NEAR32 BeforeSECEntry
mEND_PROC_WITH_JMP_NEAR32 BeforeSECEntry

;<AMI_THDR_START>
;---------------------------------------------------------------------------
;
; Name:		BeforeSEC_S3Entry
;
; Type:		eLink Table
;
; Description:	Provide an opportunity for OEM to adjust the FV_BB_BASE in S3 path, stack is available.
;
; Referrals:	
;
; Notes:	eLink comments display the name of immediate Parent.
;
;---------------------------------------------------------------------------
;<AMI_THDR_END>

mSTART_PROC_NEAR32 BeforeSEC_S3Entry
mEND_PROC_WITH_JMP_NEAR32 BeforeSEC_S3Entry

;---------------------------------------------------------------------------
;	STARTUP_SEG  S E G M E N T  ENDS
;---------------------------------------------------------------------------
STARTUP_SEG ENDS

END

