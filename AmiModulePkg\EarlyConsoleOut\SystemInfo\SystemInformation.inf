#***********************************************************************
#*                                                                     *
#*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
#*                                                                     *
#*      All rights reserved. Subject to AMI licensing agreement.       *
#*                                                                     *
#***********************************************************************

[Defines]
  INF_VERSION     = 0x00010005
  VERSION_STRING  = 1.0
  BASE_NAME       = SystemInformation
  MODULE_TYPE     = PEIM
  FILE_GUID       = 442BE18B-CA6E-4a23-9A99-9AFE8A213A32
  ENTRY_POINT     = PeiSystemInformationEntryPoint

[Sources]
  SystemInformation.c
  SystemInformation.h

[LibraryClasses]
  PeimEntryPoint
  DebugLib
  PrintLib
  PeiServicesLib
  IoLib
  PciLib
  PlatformPeiSystemInformation

[Ppis]
  $(SYSTEM_INFO_CALLBACK_GUID)
  gAmiEarlyConsoleOutPpiGuid

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  AmiModulePkg/AmiModulePkg.dec
  AmiModulePkg/EarlyConsoleOut/AmiEarlyConsoleOutPkg.dec
  UefiCpuPkg/UefiCpuPkg.dec
  $(SYSTEM_INFO_GUID_PACKAGE_DEC)
  
[Depex]
  gAmiEarlyConsoleOutPpiGuid AND gAmiSimpleTextOutPpiGuid
  
[Pcd]
  gAmiEarlyConsolePkgTokenSpaceGuid.AmiPcdEarlyConsolePlatformName 
