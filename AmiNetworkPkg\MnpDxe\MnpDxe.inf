#//***********************************************************************
#//*                                                                     *
#//*   Copyright (c) 1985-2025, American Megatrends International LLC.   *
#//*                                                                     *
#//*      All rights reserved. Subject to AMI licensing agreement.       *
#//*                                                                     *
#//***********************************************************************

## @file
#  This module produces EFI MNP Protocol, EFI MNP Service Binding Protocol and EFI VLAN Protocol.
#
#  This module produces EFI Managed Network Protocol upon EFI Simple Network Protocol,
#  to provide raw asynchronous network I/O services. It also produces EFI VLAN Protocol
#  to provide manageability interface for VLAN configuration.
#
#  Copyright (c) 2006 - 2018, Intel Corporation. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = MnpDxe
  FILE_GUID                      = 025BBFC7-E6A9-4b8b-82AD-6815A1AEAF4A
  MODULE_TYPE                    = UEFI_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = MnpDriverEntryPoint
  UNLOAD_IMAGE                   = MnpDriverUnload
#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 EBC
#
#  DRIVER_BINDING                =  gMnpDriverBinding
#  COMPONENT_NAME                =  gMnpComponentName
#  COMPONENT_NAME2               =  gMnpComponentName2
#

[Sources]
  MnpMain.c
  MnpIo.c
  ComponentName.h
  MnpDriver.h
  ComponentName.c
  MnpDriver.c
  MnpConfig.c
  MnpImpl.h
  MnpVlan.h
  MnpVlan.c

[Packages]
  MdePkg/MdePkg.dec
  $(ADDITIONAL_MNP_PACKAGES)

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  MemoryAllocationLib
  UefiLib
  UefiBootServicesTableLib
  UefiDriverEntryPoint
  DebugLib
  NetLib
  DpcLib   
  $(ADDITIONAL_MNP_LIBRARY)
  
[Protocols]
  gEfiManagedNetworkServiceBindingProtocolGuid  ## PRODUCES
  gEfiSimpleNetworkProtocolGuid                 ## CONSUMES
  gEfiManagedNetworkProtocolGuid                ## PRODUCES
  gEfiVlanConfigProtocolGuid                    ## SOMETIMES_PRODUCES                
  gEfiDevicePathProtocolGuid
[Depex]
  gEfiVariableArchProtocolGuid

[Pcd]  
 gEfiNetworkPkgTokenSpaceGuid.PcdSkipMediaDetection     ## CONSUMES   #AMI PORTING  
 gEfiNetworkPkgTokenSpaceGuid.PcdMnpIoPollTimeout       ## CONSUMES   #AMI PORTING  
 gEfiNetworkPkgTokenSpaceGuid.PcdFixSnpStateLeakinGrub  ## CONSUMES   #AMI PORTING  ##APTIOV_SERVER_OVERRIDE - JIRA#T2-543