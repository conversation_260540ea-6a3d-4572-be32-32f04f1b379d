TOKEN
    Name  = "AmdCpmPkg_SUPPORT"
    Value  = "1"
    Help  = "Switch for Enabling AmdCpmPkg support in the project"
    TokenType = Boolean
    TargetH = Yes
    Master = Yes
End

TOKEN
    Name  = "CPM_PKG_PATH"
    Value  = "AmdCpmPkg/Addendum/Oem/Galena/Processor/Turin"
    TokenType = Expression
    TargetDSC = Yes
    Token = "PLATFORM_SELECT" "=" "0"
End

TOKEN
    Name  = "CPM_PKG_PATH"
    Value  = "AmdCpmPkg/Addendum/Oem/Chalupa/Processor/Turin"
    TokenType = Expression
    TargetDSC = Yes
    Token = "PLATFORM_SELECT" "=" "1"
End

TOKEN
    Name  = "CPM_PKG_PATH"
    Value  = "AmdCpmPkg/Addendum/Oem/Purico/Processor/Turin"
    TokenType = Expression
    TargetDSC = Yes
    Token = "PLATFORM_SELECT" "=" "2"
End

TOKEN
    Name  = "CPM_PKG_PATH"
    Value  = "AmdCpmPkg/Addendum/Oem/Volcano/Processor/Turin"
    TokenType = Expression
    TargetDSC = Yes
    Token = "PLATFORM_SELECT" "=" "3"
End

TOKEN
    Name  = "CPM_PKG_PATH"
    Value  = "AmdCpmPkg/Addendum/Oem/Onyx/Processor/Turin"
    TokenType = Expression
    TargetDSC = Yes
    Token = "PLATFORM_SELECT" "=" "4"
End

TOKEN
    Name  = "CPM_PKG_PATH"
    Value  = "AmdCpmPkg/Addendum/Oem/Quartz/Processor/Turin"
    TokenType = Expression
    TargetDSC = Yes
    Token = "PLATFORM_SELECT" "=" "5"
End

TOKEN
    Name  = "CPM_PKG_PATH"
    Value  = "AmdCpmPkg/Addendum/Oem/Ruby/Processor/Turin"
    TokenType = Expression
    TargetDSC = Yes
    Token = "PLATFORM_SELECT" "=" "6"
End

TOKEN
    Name  = "CPM_PKG_PATH"
    Value  = "AmdCpmPkg/Addendum/Oem/Titanite/Processor/Turin"
    TokenType = Expression
    TargetDSC = Yes
    Token = "PLATFORM_SELECT" "=" "7"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Addendum_Oem_Galena_Pei_AmdCpmOemInitPei_AmdCpmOemInitPeimGalena_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "0"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Addendum_Oem_Chalupa_Pei_AmdCpmOemInitPei_AmdCpmOemInitPeimChalupa_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "1"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Addendum_Oem_Purico_Pei_AmdCpmOemInitPei_AmdCpmOemInitPeimPurico_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "2"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Addendum_Oem_Volcano_Pei_AmdCpmOemInitPei_AmdCpmOemInitPeimVolcano_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "3"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Addendum_Oem_Onyx_Pei_AmdCpmOemInitPei_AmdCpmOemInitPeimOnyx_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "4"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Addendum_Oem_Quartz_Pei_AmdCpmOemInitPei_AmdCpmOemInitPeimQuartz_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "5"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Addendum_Oem_Ruby_Pei_AmdCpmOemInitPei_AmdCpmOemInitPeimRuby_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "6"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Addendum_Oem_Titanite_Pei_AmdCpmOemInitPei_AmdCpmOemInitPeimTitanite_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "7"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Galena_Pei_AmdCpmOemInitPei_AmdCpmOemInitPeimGalena_INF"
    File = "Addendum/Oem/Galena/Pei/AmdCpmOemInitPei/AmdCpmOemInitPeimGalena.inf"
    Package = "AmdCpmPkg"
    Arch = "IA32"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Galena_Pei_AmdCpmOemInitPei_AmdCpmOemInitPeimGalena_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Chalupa_Pei_AmdCpmOemInitPei_AmdCpmOemInitPeimChalupa_INF"
    File = "Addendum/Oem/Chalupa/Pei/AmdCpmOemInitPei/AmdCpmOemInitPeimChalupa.inf"
    Package = "AmdCpmPkg"
    Arch = "IA32"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Chalupa_Pei_AmdCpmOemInitPei_AmdCpmOemInitPeimChalupa_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Purico_Pei_AmdCpmOemInitPei_AmdCpmOemInitPeimPurico_INF"
    File = "Addendum/Oem/Purico/Pei/AmdCpmOemInitPei/AmdCpmOemInitPeimPurico.inf"
    Package = "AmdCpmPkg"
    Arch = "IA32"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Purico_Pei_AmdCpmOemInitPei_AmdCpmOemInitPeimPurico_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Volcano_Pei_AmdCpmOemInitPei_AmdCpmOemInitPeimVolcano_INF"
    File = "Addendum/Oem/Volcano/Pei/AmdCpmOemInitPei/AmdCpmOemInitPeimVolcano.inf"
    Package = "AmdCpmPkg"
    Arch = "IA32"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Volcano_Pei_AmdCpmOemInitPei_AmdCpmOemInitPeimVolcano_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Onyx_Pei_AmdCpmOemInitPei_AmdCpmOemInitPeimOnyx_INF"
    File = "Addendum/Oem/Onyx/Pei/AmdCpmOemInitPei/AmdCpmOemInitPeimOnyx.inf"
    Package = "AmdCpmPkg"
    Arch = "IA32"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Onyx_Pei_AmdCpmOemInitPei_AmdCpmOemInitPeimOnyx_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Quartz_Pei_AmdCpmOemInitPei_AmdCpmOemInitPeimQuartz_INF"
    File = "Addendum/Oem/Quartz/Pei/AmdCpmOemInitPei/AmdCpmOemInitPeimQuartz.inf"
    Package = "AmdCpmPkg"
    Arch = "IA32"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Quartz_Pei_AmdCpmOemInitPei_AmdCpmOemInitPeimQuartz_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Ruby_Pei_AmdCpmOemInitPei_AmdCpmOemInitPeimRuby_INF"
    File = "Addendum/Oem/Ruby/Pei/AmdCpmOemInitPei/AmdCpmOemInitPeimRuby.inf"
    Package = "AmdCpmPkg"
    Arch = "IA32"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Ruby_Pei_AmdCpmOemInitPei_AmdCpmOemInitPeimRuby_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Titanite_Pei_AmdCpmOemInitPei_AmdCpmOemInitPeimTitanite_INF"
    File = "Addendum/Oem/Titanite/Pei/AmdCpmOemInitPei/AmdCpmOemInitPeimTitanite.inf"
    Package = "AmdCpmPkg"
    Arch = "IA32"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Titanite_Pei_AmdCpmOemInitPei_AmdCpmOemInitPeimTitanite_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Addendum_Oem_Galena_Pei_PlatformCustomizePei_PlatformCustomizePei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "0"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Addendum_Oem_Chalupa_Pei_PlatformCustomizePei_PlatformCustomizePei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "1"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Addendum_Oem_Purico_Pei_PlatformCustomizePei_PlatformCustomizePei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "2"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Addendum_Oem_Volcano_Pei_PlatformCustomizePei_PlatformCustomizePei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "3"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Addendum_Oem_Onyx_Pei_PlatformCustomizePei_PlatformCustomizePei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "4"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Addendum_Oem_Quartz_Pei_PlatformCustomizePei_PlatformCustomizePei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "5"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Addendum_Oem_Ruby_Pei_PlatformCustomizePei_PlatformCustomizePei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "6"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Addendum_Oem_Titanite_Pei_PlatformCustomizePei_PlatformCustomizePei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "7"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Galena_Pei_PlatformCustomizePei_PlatformCustomizePei_INF"
    File = "Addendum/Oem/Galena/Pei/PlatformCustomizePei/PlatformCustomizePei.inf"
    Package = "AmdCpmPkg"
    Arch = "IA32"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Galena_Pei_PlatformCustomizePei_PlatformCustomizePei_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Chalupa_Pei_PlatformCustomizePei_PlatformCustomizePei_INF"
    File = "Addendum/Oem/Chalupa/Pei/PlatformCustomizePei/PlatformCustomizePei.inf"
    Package = "AmdCpmPkg"
    Arch = "IA32"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Chalupa_Pei_PlatformCustomizePei_PlatformCustomizePei_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Purico_Pei_PlatformCustomizePei_PlatformCustomizePei_INF"
    File = "Addendum/Oem/Purico/Pei/PlatformCustomizePei/PlatformCustomizePei.inf"
    Package = "AmdCpmPkg"
    Arch = "IA32"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Purico_Pei_PlatformCustomizePei_PlatformCustomizePei_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Volcano_Pei_PlatformCustomizePei_PlatformCustomizePei_INF"
    File = "Addendum/Oem/Volcano/Pei/PlatformCustomizePei/PlatformCustomizePei.inf"
    Package = "AmdCpmPkg"
    Arch = "IA32"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Volcano_Pei_PlatformCustomizePei_PlatformCustomizePei_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Onyx_Pei_PlatformCustomizePei_PlatformCustomizePei_INF"
    File = "Addendum/Oem/Onyx/Pei/PlatformCustomizePei/PlatformCustomizePei.inf"
    Package = "AmdCpmPkg"
    Arch = "IA32"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Onyx_Pei_PlatformCustomizePei_PlatformCustomizePei_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Quartz_Pei_PlatformCustomizePei_PlatformCustomizePei_INF"
    File = "Addendum/Oem/Quartz/Pei/PlatformCustomizePei/PlatformCustomizePei.inf"
    Package = "AmdCpmPkg"
    Arch = "IA32"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Quartz_Pei_PlatformCustomizePei_PlatformCustomizePei_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Ruby_Pei_PlatformCustomizePei_PlatformCustomizePei_INF"
    File = "Addendum/Oem/Ruby/Pei/PlatformCustomizePei/PlatformCustomizePei.inf"
    Package = "AmdCpmPkg"
    Arch = "IA32"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Ruby_Pei_PlatformCustomizePei_PlatformCustomizePei_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Titanite_Pei_PlatformCustomizePei_PlatformCustomizePei_INF"
    File = "Addendum/Oem/Titanite/Pei/PlatformCustomizePei/PlatformCustomizePei.inf"
    Package = "AmdCpmPkg"
    Arch = "IA32"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Titanite_Pei_PlatformCustomizePei_PlatformCustomizePei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Devices_Ds125Br401a_Pei_Ds125Br401aPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Devices_Ds125Br401a_Pei_Ds125Br401aPei_INF"
    File = "Devices/Ds125Br401a/Pei/Ds125Br401aPei.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Devices_Ds125Br401a_Pei_Ds125Br401aPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Devices_M24LC128_Pei_M24Lc128Pei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Devices_M24LC128_Pei_M24Lc128Pei_INF"
    File = "Devices/M24LC128/Pei/M24Lc128Pei.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Devices_M24LC128_Pei_M24Lc128Pei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Devices_Pca9535a_Pei_Pca9535aPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Devices_Pca9535a_Pei_Pca9535aPei_INF"
    File = "Devices/Pca9535a/Pei/Pca9535aPei.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Devices_Pca9535a_Pei_Pca9535aPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Devices_Pca9545a_Pei_Pca9545aPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Devices_Pca9545a_Pei_Pca9545aPei_INF"
    File = "Devices/Pca9545a/Pei/Pca9545aPei.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Devices_Pca9545a_Pei_Pca9545aPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Devices_Sff8472_Pei_Sff8472Pei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Devices_Sff8472_Pei_Sff8472Pei_INF"
    File = "Devices/Sff8472/Pei/Sff8472Pei.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Devices_Sff8472_Pei_Sff8472Pei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Devices_Tca9548a_Pei_Tca9548aPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Devices_Tca9548a_Pei_Tca9548aPei_INF"
    File = "Devices/Tca9548a/Pei/Tca9548aPei.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Devices_Tca9548a_Pei_Tca9548aPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Devices_M24LC256_Pei_M24Lc256Pei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Devices_M24LC256_Pei_M24Lc256Pei_INF"
    File = "Devices/M24LC256/Pei/M24Lc256Pei.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Devices_M24LC256_Pei_M24Lc256Pei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Devices_Pca9536_Pei_Pca9536Pei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Devices_Pca9536_Pei_Pca9536Pei_INF"
    File = "Devices/Pca9536/Pei/Pca9536Pei.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Devices_Pca9536_Pei_Pca9536Pei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Devices_Pca6107_Pei_Pca6107Pei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Devices_Pca6107_Pei_Pca6107Pei_INF"
    File = "Devices/Pca6107/Pei/Pca6107Pei.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Devices_Pca6107_Pei_Pca6107Pei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Features_BoardId_Pei_AmdBoardIdPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_BoardId_Pei_AmdBoardIdPei_INF"
    File = "Features/BoardId/Pei/AmdBoardIdPei.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Features_BoardId_Pei_AmdBoardIdPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Features_GpioInit_Pei_AmdCpmGpioInitPeim_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_GpioInit_Pei_AmdCpmGpioInitPeim_INF"
    File = "Features/GpioInit/Pei/AmdCpmGpioInitPeim.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Features_GpioInit_Pei_AmdCpmGpioInitPeim_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Features_LpcUart_Pei_AmdCpmLpcUartPeim_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_LpcUart_Pei_AmdCpmLpcUartPeim_INF"
    File = "Features/LpcUart/Pei/AmdCpmLpcUartPeim.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Features_LpcUart_Pei_AmdCpmLpcUartPeim_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Features_PcieInit_Pei_AmdCpmPcieInitPeim_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_PcieInit_Pei_AmdCpmPcieInitPeim_INF"
    File = "Features/PcieInit/Pei/AmdCpmPcieInitPeim.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Features_PcieInit_Pei_AmdCpmPcieInitPeim_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Features_PlatformRas_Brh_Pei_AmdPlatformRasBrhPei_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_PlatformRas_Brh_Pei_AmdPlatformRasBrhPei_INF"
    File = "Features/PlatformRas/Brh/Pei/AmdPlatformRasBrhPei.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Features_PlatformRas_Brh_Pei_AmdPlatformRasBrhPei_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Kernel_Pei_AmdCpmInitPeim_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Kernel_Pei_AmdCpmInitPeim_INF"
    File = "Kernel/Pei/AmdCpmInitPeim.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Kernel_Pei_AmdCpmInitPeim_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Features_FabricTopologyDump_FabricTopologyDump_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_FabricTopologyDump_FabricTopologyDump_INF"
    File = "Features/FabricTopologyDump/FabricTopologyDump.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Features_FabricTopologyDump_FabricTopologyDump_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Galena_Dxe_ServerHotplugDxe_ServerHotplugDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "0"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Chalupa_Dxe_ServerHotplugDxe_ServerHotplugDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Onyx_Dxe_ServerHotplugDxe_ServerHotplugDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "4"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Quartz_Dxe_ServerHotplugDxe_ServerHotplugDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "5"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Galena_Dxe_ServerHotplugDxe_ServerHotplugDxe_INF"
    File = "Addendum/Oem/Galena/Dxe/ServerHotplugDxe/ServerHotplugDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Galena_Dxe_ServerHotplugDxe_ServerHotplugDxe_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Chalupa_Dxe_ServerHotplugDxe_ServerHotplugDxe_INF"
    File = "Addendum/Oem/Chalupa/Dxe/ServerHotplugDxe/ServerHotplugDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Chalupa_Dxe_ServerHotplugDxe_ServerHotplugDxe_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Onyx_Dxe_ServerHotplugDxe_ServerHotplugDxe_INF"
    File = "Addendum/Oem/Onyx/Dxe/ServerHotplugDxe/ServerHotplugDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Onyx_Dxe_ServerHotplugDxe_ServerHotplugDxe_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Quartz_Dxe_ServerHotplugDxe_ServerHotplugDxe_INF"
    File = "Addendum/Oem/Quartz/Dxe/ServerHotplugDxe/ServerHotplugDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Quartz_Dxe_ServerHotplugDxe_ServerHotplugDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Galena_Dxe_RasOemDimmMap_RasOemDimmMap_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "0"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Chalupa_Dxe_RasOemDimmMap_RasOemDimmMap_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Purico_Dxe_RasOemDimmMap_RasOemDimmMap_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "2"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Volcano_Dxe_RasOemDimmMap_RasOemDimmMap_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "3"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Onyx_Dxe_RasOemDimmMap_RasOemDimmMap_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "4"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Quartz_Dxe_RasOemDimmMap_RasOemDimmMap_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "5"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Ruby_Dxe_RasOemDimmMap_RasOemDimmMap_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "6"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Titanite_Dxe_RasOemDimmMap_RasOemDimmMap_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "7"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Galena_Dxe_RasOemDimmMap_RasOemDimmMap_INF"
    File = "Addendum/Oem/Galena/Dxe/RasOemDimmMap/RasOemDimmMap.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Galena_Dxe_RasOemDimmMap_RasOemDimmMap_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Chalupa_Dxe_RasOemDimmMap_RasOemDimmMap_INF"
    File = "Addendum/Oem/Chalupa/Dxe/RasOemDimmMap/RasOemDimmMap.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Chalupa_Dxe_RasOemDimmMap_RasOemDimmMap_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Purico_Dxe_RasOemDimmMap_RasOemDimmMap_INF"
    File = "Addendum/Oem/Purico/Dxe/RasOemDimmMap/RasOemDimmMap.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Purico_Dxe_RasOemDimmMap_RasOemDimmMap_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Volcano_Dxe_RasOemDimmMap_RasOemDimmMap_INF"
    File = "Addendum/Oem/Volcano/Dxe/RasOemDimmMap/RasOemDimmMap.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Volcano_Dxe_RasOemDimmMap_RasOemDimmMap_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Onyx_Dxe_RasOemDimmMap_RasOemDimmMap_INF"
    File = "Addendum/Oem/Onyx/Dxe/RasOemDimmMap/RasOemDimmMap.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Onyx_Dxe_RasOemDimmMap_RasOemDimmMap_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Quartz_Dxe_RasOemDimmMap_RasOemDimmMap_INF"
    File = "Addendum/Oem/Quartz/Dxe/RasOemDimmMap/RasOemDimmMap.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Quartz_Dxe_RasOemDimmMap_RasOemDimmMap_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Ruby_Dxe_RasOemDimmMap_RasOemDimmMap_INF"
    File = "Addendum/Oem/Ruby/Dxe/RasOemDimmMap/RasOemDimmMap.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Ruby_Dxe_RasOemDimmMap_RasOemDimmMap_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Titanite_Dxe_RasOemDimmMap_RasOemDimmMap_INF"
    File = "Addendum/Oem/Titanite/Dxe/RasOemDimmMap/RasOemDimmMap.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Titanite_Dxe_RasOemDimmMap_RasOemDimmMap_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Volcano_Dxe_RasOemMcaThresholdMap_RasOemMcaThresholdMap_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "3"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Volcano_Dxe_RasOemMcaThresholdMap_RasOemMcaThresholdMap_INF"
    File = "Addendum/Oem/Volcano/Dxe/RasOemMcaThresholdMap/RasOemMcaThresholdMap.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Volcano_Dxe_RasOemMcaThresholdMap_RasOemMcaThresholdMap_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Kernel_Asl_AmdCpmInitAsl_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Kernel_Asl_AmdCpmInitAsl_INF"
    File = "Kernel/Asl/AmdCpmInitAsl.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "USER_DEFINED"
    Token = "DXE_DRIVER_AmdCpmPkg_Kernel_Asl_AmdCpmInitAsl_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Kernel_Dxe_AmdCpmInitDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Kernel_Dxe_AmdCpmInitDxe_INF"
    File = "Kernel/Dxe/AmdCpmInitDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Kernel_Dxe_AmdCpmInitDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Kernel_Smm_AmdCpmInitSmm_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Kernel_Smm_AmdCpmInitSmm_INF"
    File = "Kernel/Smm/AmdCpmInitSmm.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Kernel_Smm_AmdCpmInitSmm_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_BoardId_Dxe_AmdBoardIdDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_BoardId_Dxe_AmdBoardIdDxe_INF"
    File = "Features/BoardId/Dxe/AmdBoardIdDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_BoardId_Dxe_AmdBoardIdDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_PcieInit_Asl_AmdCpmPcieInitAsl_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_PcieInit_Asl_AmdCpmPcieInitAsl_INF"
    File = "Features/PcieInit/Asl/AmdCpmPcieInitAsl.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "USER_DEFINED"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_PcieInit_Asl_AmdCpmPcieInitAsl_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_PcieInit_Dxe_AmdCpmPcieInitDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_PcieInit_Dxe_AmdCpmPcieInitDxe_INF"
    File = "Features/PcieInit/Dxe/AmdCpmPcieInitDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_PcieInit_Dxe_AmdCpmPcieInitDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_GpioInit_Dxe_AmdCpmGpioInitDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_GpioInit_Dxe_AmdCpmGpioInitDxe_INF"
    File = "Features/GpioInit/Dxe/AmdCpmGpioInitDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_GpioInit_Dxe_AmdCpmGpioInitDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_GpioInit_Smm_AmdCpmGpioInitSmm_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_GpioInit_Smm_AmdCpmGpioInitSmm_INF"
    File = "Features/GpioInit/Smm/AmdCpmGpioInitSmm.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_GpioInit_Smm_AmdCpmGpioInitSmm_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_PlatformRas_Brh_Dxe_AmdPlatformRasBrhDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_PlatformRas_Brh_Dxe_AmdPlatformRasBrhDxe_INF"
    File = "Features/PlatformRas/Brh/Dxe/AmdPlatformRasBrhDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_PlatformRas_Brh_Dxe_AmdPlatformRasBrhDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_PlatformRas_Brh_Smm_AmdPlatformRasBrhSmm_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_PlatformRas_Brh_Smm_AmdPlatformRasBrhSmm_INF"
    File = "Features/PlatformRas/Brh/Smm/AmdPlatformRasBrhSmm.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_PlatformRas_Brh_Smm_AmdPlatformRasBrhSmm_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_PlatformRas_Brh_Dxe_AmdOemRasBrhDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_PlatformRas_Brh_Dxe_AmdOemRasBrhDxe_INF"
    File = "Features/PlatformRas/Brh/Dxe/AmdOemRasBrhDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_PlatformRas_Brh_Dxe_AmdOemRasBrhDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_PlatformRas_Brh_Smm_AmdOemRasBrhSmm_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_PlatformRas_Brh_Smm_AmdOemRasBrhSmm_INF"
    File = "Features/PlatformRas/Brh/Smm/AmdOemRasBrhSmm.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_PlatformRas_Brh_Smm_AmdOemRasBrhSmm_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_PlatformRas_Brh_Asl_PlatformRasBrhAsl_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_PlatformRas_Brh_Asl_PlatformRasBrhAsl_INF"
    File = "Features/PlatformRas/Brh/Asl/PlatformRasBrhAsl.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "USER_DEFINED"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_PlatformRas_Brh_Asl_PlatformRasBrhAsl_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_AmdCdmaDataInit_Brh_Dxe_AmdCdmaDsmDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_AmdCdmaDataInit_Brh_Dxe_AmdCdmaDsmDxe_INF"
    File = "Features/AmdCdmaDataInit/Brh/Dxe/AmdCdmaDsmDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_AmdCdmaDataInit_Brh_Dxe_AmdCdmaDsmDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_AmdCdmaDataInit_Asl_AmdCdmaAsl_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_AmdCdmaDataInit_Asl_AmdCdmaAsl_INF"
    File = "Features/AmdCdmaDataInit/Asl/AmdCdmaAsl.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "USER_DEFINED"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_AmdCdmaDataInit_Asl_AmdCdmaAsl_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_MPDMA_BRH_Dxe_MPDMABrhDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_MPDMA_BRH_Dxe_MPDMABrhDxe_INF"
    File = "Features/MPDMA/BRH/Dxe/MPDMABrhDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_MPDMA_BRH_Dxe_MPDMABrhDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_MPDMA_BRH_Asl_MpDmaBrhAsl_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_MPDMA_BRH_Asl_MpDmaBrhAsl_INF"
    File = "Features/MPDMA/BRH/Asl/MpDmaBrhAsl.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "USER_DEFINED"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_MPDMA_BRH_Asl_MpDmaBrhAsl_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_HSMP_BRH_Dxe_HsmpBrhDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_HSMP_BRH_Dxe_HsmpBrhDxe_INF"
    File = "Features/HSMP/BRH/Dxe/HsmpBrhDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_HSMP_BRH_Dxe_HsmpBrhDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_HSMP_BRH_Asl_HsmpBrhAsl_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_HSMP_BRH_Asl_HsmpBrhAsl_INF"
    File = "Features/HSMP/BRH/Asl/HsmpBrhAsl.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "USER_DEFINED"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_HSMP_BRH_Asl_HsmpBrhAsl_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_xGbEI2cMaster_xGbEI2cMasterDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_xGbEI2cMaster_xGbEI2cMasterDxe_INF"
    File = "Features/xGbEI2cMaster/xGbEI2cMasterDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_xGbEI2cMaster_xGbEI2cMasterDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Devices_Pca9535a_Dxe_Pca9535aDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Devices_Pca9535a_Dxe_Pca9535aDxe_INF"
    File = "Devices/Pca9535a/Dxe/Pca9535aDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Devices_Pca9535a_Dxe_Pca9535aDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Devices_Pca9545a_Dxe_Pca9545aDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Devices_Pca9545a_Dxe_Pca9545aDxe_INF"
    File = "Devices/Pca9545a/Dxe/Pca9545aDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Devices_Pca9545a_Dxe_Pca9545aDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Devices_Sff8472_Dxe_Sff8472Dxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Devices_Sff8472_Dxe_Sff8472Dxe_INF"
    File = "Devices/Sff8472/Dxe/Sff8472Dxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Devices_Sff8472_Dxe_Sff8472Dxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Devices_Tca9548a_Dxe_Tca9548aDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Devices_Tca9548a_Dxe_Tca9548aDxe_INF"
    File = "Devices/Tca9548a/Dxe/Tca9548aDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Devices_Tca9548a_Dxe_Tca9548aDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Devices_Pca9536_Dxe_Pca9536Dxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Devices_Pca9536_Dxe_Pca9536Dxe_INF"
    File = "Devices/Pca9536/Dxe/Pca9536Dxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Devices_Pca9536_Dxe_Pca9536Dxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Devices_Pca6107_Dxe_Pca6107Dxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Devices_Pca6107_Dxe_Pca6107Dxe_INF"
    File = "Devices/Pca6107/Dxe/Pca6107Dxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Devices_Pca6107_Dxe_Pca6107Dxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_CF9IoTrap_BRH_CF9IoTrapBrh_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_CF9IoTrap_BRH_CF9IoTrapBrh_INF"
    File = "Features/CF9IoTrap/BRH/CF9IoTrapBrh.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_CF9IoTrap_BRH_CF9IoTrapBrh_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_AmdFwConfig_Asl_AfcSsdt_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_AmdFwConfig_Asl_AfcSsdt_INF"
    File = "Features/AmdFwConfig/Asl/AfcSsdt.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "USER_DEFINED"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_AmdFwConfig_Asl_AfcSsdt_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_AmdFwConfig_Dxe_AmdFwConfigDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_AmdFwConfig_Dxe_AmdFwConfigDxe_INF"
    File = "Features/AmdFwConfig/Dxe/AmdFwConfigDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_AmdFwConfig_Dxe_AmdFwConfigDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_AmdFwConfig_Smm_AmdFwConfigSmmBrh_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_AmdFwConfig_Smm_AmdFwConfigSmmBrh_INF"
    File = "Features/AmdFwConfig/Smm/AmdFwConfigSmmBrh.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_AmdFwConfig_Smm_AmdFwConfigSmmBrh_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_AcpiI3cSlaveSsdt_AcpiI3cSlaveSsdt_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_AcpiI3cSlaveSsdt_AcpiI3cSlaveSsdt_INF"
    File = "Features/AcpiI3cSlaveSsdt/AcpiI3cSlaveSsdt.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_AcpiI3cSlaveSsdt_AcpiI3cSlaveSsdt_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Galena_Dxe_AcpiI3cSlave_ASL_AcpiI3cSlaveAsl_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "0"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Galena_Dxe_AcpiI3cSlave_ASL_AcpiI3cSlaveAsl_INF"
    File = "Addendum/Oem/Galena/Dxe/AcpiI3cSlave/ASL/AcpiI3cSlaveAsl.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "USER_DEFINED"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Galena_Dxe_AcpiI3cSlave_ASL_AcpiI3cSlaveAsl_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Chalupa_Dxe_AcpiI3cSlave_ASL_AcpiI3cSlaveAsl_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Chalupa_Dxe_AcpiI3cSlave_ASL_AcpiI3cSlaveAsl_INF"
    File = "Addendum/Oem/Chalupa/Dxe/AcpiI3cSlave/ASL/AcpiI3cSlaveAsl.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "USER_DEFINED"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Chalupa_Dxe_AcpiI3cSlave_ASL_AcpiI3cSlaveAsl_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Purico_Dxe_AcpiI3cSlave_ASL_AcpiI3cSlaveAsl_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "2"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Purico_Dxe_AcpiI3cSlave_ASL_AcpiI3cSlaveAsl_INF"
    File = "Addendum/Oem/Purico/Dxe/AcpiI3cSlave/ASL/AcpiI3cSlaveAsl.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "USER_DEFINED"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Purico_Dxe_AcpiI3cSlave_ASL_AcpiI3cSlaveAsl_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Volcano_Dxe_AcpiI3cSlave_ASL_AcpiI3cSlaveAsl_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "3"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Volcano_Dxe_AcpiI3cSlave_ASL_AcpiI3cSlaveAsl_INF"
    File = "Addendum/Oem/Volcano/Dxe/AcpiI3cSlave/ASL/AcpiI3cSlaveAsl.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "USER_DEFINED"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Volcano_Dxe_AcpiI3cSlave_ASL_AcpiI3cSlaveAsl_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Onyx_Dxe_AcpiI3cSlave_ASL_AcpiI3cSlaveAsl_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "4"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Onyx_Dxe_AcpiI3cSlave_ASL_AcpiI3cSlaveAsl_INF"
    File = "Addendum/Oem/Onyx/Dxe/AcpiI3cSlave/ASL/AcpiI3cSlaveAsl.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "USER_DEFINED"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Onyx_Dxe_AcpiI3cSlave_ASL_AcpiI3cSlaveAsl_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Quartz_Dxe_AcpiI3cSlave_ASL_AcpiI3cSlaveAsl_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "5"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Quartz_Dxe_AcpiI3cSlave_ASL_AcpiI3cSlaveAsl_INF"
    File = "Addendum/Oem/Quartz/Dxe/AcpiI3cSlave/ASL/AcpiI3cSlaveAsl.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "USER_DEFINED"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Quartz_Dxe_AcpiI3cSlave_ASL_AcpiI3cSlaveAsl_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Ruby_Dxe_AcpiI3cSlave_ASL_AcpiI3cSlaveAsl_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "6"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Ruby_Dxe_AcpiI3cSlave_ASL_AcpiI3cSlaveAsl_INF"
    File = "Addendum/Oem/Ruby/Dxe/AcpiI3cSlave/ASL/AcpiI3cSlaveAsl.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "USER_DEFINED"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Ruby_Dxe_AcpiI3cSlave_ASL_AcpiI3cSlaveAsl_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Titanite_Dxe_AcpiI3cSlave_ASL_AcpiI3cSlaveAsl_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "7"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Titanite_Dxe_AcpiI3cSlave_ASL_AcpiI3cSlaveAsl_INF"
    File = "Addendum/Oem/Titanite/Dxe/AcpiI3cSlave/ASL/AcpiI3cSlaveAsl.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "USER_DEFINED"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Titanite_Dxe_AcpiI3cSlave_ASL_AcpiI3cSlaveAsl_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_SysTopologyReport_Dxe_SysTopologyReportDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_SysTopologyReport_Dxe_SysTopologyReportDxe_INF"
    File = "Features/SysTopologyReport/Dxe/SysTopologyReportDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_SysTopologyReport_Dxe_SysTopologyReportDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_AmdBctPkg_BiosCfgToolDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_AmdBctPkg_BiosCfgToolDxe_INF"
    File = "Features/AmdBctPkg/BiosCfgToolDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_AmdBctPkg_BiosCfgToolDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_AmdBctPkg_BiosCfgToolSmm_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_AmdBctPkg_BiosCfgToolSmm_INF"
    File = "Features/AmdBctPkg/BiosCfgToolSmm.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_AmdBctPkg_BiosCfgToolSmm_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Devices_GenericCxl_CxlEndpointDriver_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Devices_GenericCxl_CxlEndpointDriver_INF"
    File = "Devices/GenericCxl/CxlEndpointDriver.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "UEFI_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Devices_GenericCxl_CxlEndpointDriver_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Galena_Dxe_AmdPbsSetupDxe_AmdPbsSetupDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "0"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Chalupa_Dxe_AmdPbsSetupDxe_AmdPbsSetupDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Purico_Dxe_AmdPbsSetupDxe_AmdPbsSetupDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "2"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Volcano_Dxe_AmdPbsSetupDxe_AmdPbsSetupDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "3"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Onyx_Dxe_AmdPbsSetupDxe_AmdPbsSetupDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "4"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Quartz_Dxe_AmdPbsSetupDxe_AmdPbsSetupDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "5"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Ruby_Dxe_AmdPbsSetupDxe_AmdPbsSetupDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "6"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Titanite_Dxe_AmdPbsSetupDxe_AmdPbsSetupDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "7"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Galena_Dxe_AmdPbsSetupDxe_AmdPbsSetupDxe_INF"
    File = "Addendum/Oem/Galena/Dxe/AmdPbsSetupDxe/AmdPbsSetupDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Galena_Dxe_AmdPbsSetupDxe_AmdPbsSetupDxe_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Chalupa_Dxe_AmdPbsSetupDxe_AmdPbsSetupDxe_INF"
    File = "Addendum/Oem/Chalupa/Dxe/AmdPbsSetupDxe/AmdPbsSetupDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Chalupa_Dxe_AmdPbsSetupDxe_AmdPbsSetupDxe_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Purico_Dxe_AmdPbsSetupDxe_AmdPbsSetupDxe_INF"
    File = "Addendum/Oem/Purico/Dxe/AmdPbsSetupDxe/AmdPbsSetupDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Purico_Dxe_AmdPbsSetupDxe_AmdPbsSetupDxe_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Volcano_Dxe_AmdPbsSetupDxe_AmdPbsSetupDxe_INF"
    File = "Addendum/Oem/Volcano/Dxe/AmdPbsSetupDxe/AmdPbsSetupDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Volcano_Dxe_AmdPbsSetupDxe_AmdPbsSetupDxe_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Onyx_Dxe_AmdPbsSetupDxe_AmdPbsSetupDxe_INF"
    File = "Addendum/Oem/Onyx/Dxe/AmdPbsSetupDxe/AmdPbsSetupDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Onyx_Dxe_AmdPbsSetupDxe_AmdPbsSetupDxe_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Quartz_Dxe_AmdPbsSetupDxe_AmdPbsSetupDxe_INF"
    File = "Addendum/Oem/Quartz/Dxe/AmdPbsSetupDxe/AmdPbsSetupDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Quartz_Dxe_AmdPbsSetupDxe_AmdPbsSetupDxe_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Ruby_Dxe_AmdPbsSetupDxe_AmdPbsSetupDxe_INF"
    File = "Addendum/Oem/Ruby/Dxe/AmdPbsSetupDxe/AmdPbsSetupDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Ruby_Dxe_AmdPbsSetupDxe_AmdPbsSetupDxe_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Titanite_Dxe_AmdPbsSetupDxe_AmdPbsSetupDxe_INF"
    File = "Addendum/Oem/Titanite/Dxe/AmdPbsSetupDxe/AmdPbsSetupDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Titanite_Dxe_AmdPbsSetupDxe_AmdPbsSetupDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_ApicInfoData_BRH_Dxe_ApicInfoDataDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_ApicInfoData_BRH_Dxe_ApicInfoDataDxe_INF"
    File = "Features/ApicInfoData/BRH/Dxe/ApicInfoDataDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_ApicInfoData_BRH_Dxe_ApicInfoDataDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_HotPlug_Brh_Smm_AmdHotPlugBrhSmm_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_HotPlug_Brh_Smm_AmdHotPlugBrhSmm_INF"
    File = "Features/HotPlug/Brh/Smm/AmdHotPlugBrhSmm.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_HotPlug_Brh_Smm_AmdHotPlugBrhSmm_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_LegacyInterrupt_Dxe_LegacyInterruptDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_LegacyInterrupt_Dxe_LegacyInterruptDxe_INF"
    File = "Features/LegacyInterrupt/Dxe/LegacyInterruptDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_LegacyInterrupt_Dxe_LegacyInterruptDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Galena_Dxe_DeferredPsbFuse_DeferredPsbFuseDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "0"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Chalupa_Dxe_DeferredPsbFuse_DeferredPsbFuseDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Purico_Dxe_DeferredPsbFuse_DeferredPsbFuseDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "2"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Volcano_Dxe_DeferredPsbFuse_DeferredPsbFuseDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "3"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Onyx_Dxe_DeferredPsbFuse_DeferredPsbFuseDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "4"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Quartz_Dxe_DeferredPsbFuse_DeferredPsbFuseDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "5"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Ruby_Dxe_DeferredPsbFuse_DeferredPsbFuseDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "6"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Titanite_Dxe_DeferredPsbFuse_DeferredPsbFuseDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "7"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Galena_Dxe_DeferredPsbFuse_DeferredPsbFuseDxe_INF"
    File = "Addendum/Oem/Galena/Dxe/DeferredPsbFuse/DeferredPsbFuseDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Galena_Dxe_DeferredPsbFuse_DeferredPsbFuseDxe_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Chalupa_Dxe_DeferredPsbFuse_DeferredPsbFuseDxe_INF"
    File = "Addendum/Oem/Chalupa/Dxe/DeferredPsbFuse/DeferredPsbFuseDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Chalupa_Dxe_DeferredPsbFuse_DeferredPsbFuseDxe_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Purico_Dxe_DeferredPsbFuse_DeferredPsbFuseDxe_INF"
    File = "Addendum/Oem/Purico/Dxe/DeferredPsbFuse/DeferredPsbFuseDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Purico_Dxe_DeferredPsbFuse_DeferredPsbFuseDxe_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Volcano_Dxe_DeferredPsbFuse_DeferredPsbFuseDxe_INF"
    File = "Addendum/Oem/Volcano/Dxe/DeferredPsbFuse/DeferredPsbFuseDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Volcano_Dxe_DeferredPsbFuse_DeferredPsbFuseDxe_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Onyx_Dxe_DeferredPsbFuse_DeferredPsbFuseDxe_INF"
    File = "Addendum/Oem/Onyx/Dxe/DeferredPsbFuse/DeferredPsbFuseDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Onyx_Dxe_DeferredPsbFuse_DeferredPsbFuseDxe_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Quartz_Dxe_DeferredPsbFuse_DeferredPsbFuseDxe_INF"
    File = "Addendum/Oem/Quartz/Dxe/DeferredPsbFuse/DeferredPsbFuseDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Quartz_Dxe_DeferredPsbFuse_DeferredPsbFuseDxe_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Ruby_Dxe_DeferredPsbFuse_DeferredPsbFuseDxe_INF"
    File = "Addendum/Oem/Ruby/Dxe/DeferredPsbFuse/DeferredPsbFuseDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Ruby_Dxe_DeferredPsbFuse_DeferredPsbFuseDxe_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Titanite_Dxe_DeferredPsbFuse_DeferredPsbFuseDxe_INF"
    File = "Addendum/Oem/Titanite/Dxe/DeferredPsbFuse/DeferredPsbFuseDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Titanite_Dxe_DeferredPsbFuse_DeferredPsbFuseDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_OobPprDxe_OobPprDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_OobPprDxe_OobPprDxe_INF"
    File = "Addendum/Oem/OobPprDxe/OobPprDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_OobPprDxe_OobPprDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_AmdPrm_PrmModule_PrmAddressTranslateModule_Library_Brh_DxeAddressTranslateModuleConfigLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_AmdPrm_PrmModule_PrmAddressTranslateModule_Library_Brh_DxeAddressTranslateModuleConfigLib_INF"
    File = "Features/AmdPrm/PrmModule/PrmAddressTranslateModule/Library/Brh/DxeAddressTranslateModuleConfigLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_AmdPrm_PrmModule_PrmAddressTranslateModule_Library_Brh_DxeAddressTranslateModuleConfigLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_AmdPrm_PrmConfigDxe_PrmConfigDxe_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_AmdPrm_PrmConfigDxe_PrmConfigDxe_INF"
    File = "Features/AmdPrm/PrmConfigDxe/PrmConfigDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_RUNTIME_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_AmdPrm_PrmConfigDxe_PrmConfigDxe_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_AmdPrm_PrmModule_PrmAddressTranslateModule_Brh_PrmAddressTranslateModule_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_AmdPrm_PrmModule_PrmAddressTranslateModule_Brh_PrmAddressTranslateModule_INF"
    File = "Features/AmdPrm/PrmModule/PrmAddressTranslateModule/Brh/PrmAddressTranslateModule.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_RUNTIME_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_AmdPrm_PrmModule_PrmAddressTranslateModule_Brh_PrmAddressTranslateModule_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_AmdVariableProtection_AmdVariableProtection_SUPPORT"
    Value = "0"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_AmdVariableProtection_AmdVariableProtection_INF"
    File = "Features/AmdVariableProtection/AmdVariableProtection.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_AmdVariableProtection_AmdVariableProtection_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasAcpi63BrhLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasAcpi63BrhLib_INF"
    File = "Library/Proc/Ras/Brh/CpmRasAcpi63BrhLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "_AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasAcpi63BrhLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CpmRasAcpiLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasAcpi63BrhLib_INF"
    Token = "_AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasAcpi63BrhLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AmdCpmPkg_Library_Proc_Cpu_AmdCpmCpu_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Library_Proc_Cpu_AmdCpmCpu_INF"
    File = "Library/Proc/Cpu/AmdCpmCpu.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "_AmdCpmPkg_Library_Proc_Cpu_AmdCpmCpu_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdCpmCpuLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Library_Proc_Cpu_AmdCpmCpu_INF"
    Token = "_AmdCpmPkg_Library_Proc_Cpu_AmdCpmCpu_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AmdCpmPkg_Library_Proc_AmdFwConfigCbsLib_AmdFwConfigCbsBrhLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Library_Proc_AmdFwConfigCbsLib_AmdFwConfigCbsBrhLib_INF"
    File = "Library/Proc/AmdFwConfigCbsLib/AmdFwConfigCbsBrhLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "_AmdCpmPkg_Library_Proc_AmdFwConfigCbsLib_AmdFwConfigCbsBrhLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdFwConfigCbsBrhLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Library_Proc_AmdFwConfigCbsLib_AmdFwConfigCbsBrhLib_INF"
    Token = "_AmdCpmPkg_Library_Proc_AmdFwConfigCbsLib_AmdFwConfigCbsBrhLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Galena_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "0"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Chalupa_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Purico_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "2"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Volcano_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "3"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Onyx_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "4"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Quartz_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "5"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Ruby_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "6"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Titanite_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "7"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Galena_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_INF"
    File = "Addendum/Oem/Galena/Library/AmdPbsConfigLib/AmdPbsConfigDxeLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Galena_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Chalupa_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_INF"
    File = "Addendum/Oem/Chalupa/Library/AmdPbsConfigLib/AmdPbsConfigDxeLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Chalupa_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Purico_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_INF"
    File = "Addendum/Oem/Purico/Library/AmdPbsConfigLib/AmdPbsConfigDxeLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Purico_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Volcano_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_INF"
    File = "Addendum/Oem/Volcano/Library/AmdPbsConfigLib/AmdPbsConfigDxeLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Volcano_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Onyx_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_INF"
    File = "Addendum/Oem/Onyx/Library/AmdPbsConfigLib/AmdPbsConfigDxeLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Onyx_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Quartz_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_INF"
    File = "Addendum/Oem/Quartz/Library/AmdPbsConfigLib/AmdPbsConfigDxeLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Quartz_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Ruby_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_INF"
    File = "Addendum/Oem/Ruby/Library/AmdPbsConfigLib/AmdPbsConfigDxeLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Ruby_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Titanite_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_INF"
    File = "Addendum/Oem/Titanite/Library/AmdPbsConfigLib/AmdPbsConfigDxeLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Titanite_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPbsConfigLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Galena_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Galena_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPbsConfigLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Chalupa_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Chalupa_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPbsConfigLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Purico_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Purico_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPbsConfigLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Volcano_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Volcano_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPbsConfigLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Onyx_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Onyx_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPbsConfigLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Quartz_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Quartz_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPbsConfigLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Ruby_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Ruby_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPbsConfigLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Titanite_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Titanite_Library_AmdPbsConfigLib_AmdPbsConfigDxeLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AmdCpmPkg_Features_MPDMA_MpdmaIvrsLib_MpdmaIvrsLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_MPDMA_MpdmaIvrsLib_MpdmaIvrsLib_INF"
    File = "Features/MPDMA/MpdmaIvrsLib/MpdmaIvrsLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "_AmdCpmPkg_Features_MPDMA_MpdmaIvrsLib_MpdmaIvrsLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "MpdmaIvrsLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Features_MPDMA_MpdmaIvrsLib_MpdmaIvrsLib_INF"
    Token = "_AmdCpmPkg_Features_MPDMA_MpdmaIvrsLib_MpdmaIvrsLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasCxlBrhLib_CpmRasCxlBrhLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasCxlBrhLib_CpmRasCxlBrhLib_INF"
    File = "Library/Proc/Ras/Brh/CpmRasCxlBrhLib/CpmRasCxlBrhLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "_AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasCxlBrhLib_CpmRasCxlBrhLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CpmRasCxlLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasCxlBrhLib_CpmRasCxlBrhLib_INF"
    Token = "_AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasCxlBrhLib_CpmRasCxlBrhLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AmdCpmPkg_Library_Proc_AmdFwConfigApcbLibV3_AmdFwConfigApcbBrhLibV3_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Library_Proc_AmdFwConfigApcbLibV3_AmdFwConfigApcbBrhLibV3_INF"
    File = "Library/Proc/AmdFwConfigApcbLibV3/AmdFwConfigApcbBrhLibV3.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "_AmdCpmPkg_Library_Proc_AmdFwConfigApcbLibV3_AmdFwConfigApcbBrhLibV3_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdFwConfigApcbBrhLibV3"
    Instance = "AmdCpmPkg.AmdCpmPkg_Library_Proc_AmdFwConfigApcbLibV3_AmdFwConfigApcbBrhLibV3_INF"
    Token = "_AmdCpmPkg_Library_Proc_AmdFwConfigApcbLibV3_AmdFwConfigApcbBrhLibV3_SUPPORT" "=" "1"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Addendum_Oem_Galena_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "0"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Addendum_Oem_Chalupa_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "1"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Addendum_Oem_Purico_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "2"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Addendum_Oem_Volcano_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "3"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Addendum_Oem_Onyx_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "4"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Addendum_Oem_Quartz_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "5"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Addendum_Oem_Ruby_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "6"
End

TOKEN
    Name = "PEIM_AmdCpmPkg_Addendum_Oem_Titanite_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "7"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Galena_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_INF"
    File = "Addendum/Oem/Galena/Library/AmdPbsConfigLib/AmdPbsConfigPeiLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Galena_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Chalupa_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_INF"
    File = "Addendum/Oem/Chalupa/Library/AmdPbsConfigLib/AmdPbsConfigPeiLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Chalupa_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Purico_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_INF"
    File = "Addendum/Oem/Purico/Library/AmdPbsConfigLib/AmdPbsConfigPeiLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Purico_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Volcano_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_INF"
    File = "Addendum/Oem/Volcano/Library/AmdPbsConfigLib/AmdPbsConfigPeiLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Volcano_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Onyx_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_INF"
    File = "Addendum/Oem/Onyx/Library/AmdPbsConfigLib/AmdPbsConfigPeiLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Onyx_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Quartz_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_INF"
    File = "Addendum/Oem/Quartz/Library/AmdPbsConfigLib/AmdPbsConfigPeiLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Quartz_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Ruby_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_INF"
    File = "Addendum/Oem/Ruby/Library/AmdPbsConfigLib/AmdPbsConfigPeiLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Ruby_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Titanite_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_INF"
    File = "Addendum/Oem/Titanite/Library/AmdPbsConfigLib/AmdPbsConfigPeiLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Titanite_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPbsConfigLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Galena_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Galena_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPbsConfigLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Chalupa_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Chalupa_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPbsConfigLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Purico_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Purico_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPbsConfigLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Volcano_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Volcano_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPbsConfigLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Onyx_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Onyx_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPbsConfigLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Quartz_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Quartz_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPbsConfigLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Ruby_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Ruby_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdPbsConfigLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Titanite_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_INF"
    ModuleTypes = "PEIM"
    Token = "PEIM_AmdCpmPkg_Addendum_Oem_Titanite_Library_AmdPbsConfigLib_AmdPbsConfigPeiLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Galena_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "0"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Chalupa_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Purico_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "2"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Volcano_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "3"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Onyx_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "4"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Quartz_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "5"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Ruby_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "6"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Titanite_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "7"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Galena_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_INF"
    File = "Addendum/Oem/Galena/Library/CxlRangeEncryptionLib/CxlRangeEncryptionLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Galena_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CxlRangeEncryptionLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Galena_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Galena_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Chalupa_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_INF"
    File = "Addendum/Oem/Chalupa/Library/CxlRangeEncryptionLib/CxlRangeEncryptionLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Chalupa_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CxlRangeEncryptionLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Chalupa_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Chalupa_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Purico_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_INF"
    File = "Addendum/Oem/Purico/Library/CxlRangeEncryptionLib/CxlRangeEncryptionLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Purico_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CxlRangeEncryptionLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Purico_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Purico_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Volcano_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_INF"
    File = "Addendum/Oem/Volcano/Library/CxlRangeEncryptionLib/CxlRangeEncryptionLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Volcano_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CxlRangeEncryptionLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Volcano_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Volcano_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Onyx_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_INF"
    File = "Addendum/Oem/Onyx/Library/CxlRangeEncryptionLib/CxlRangeEncryptionLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Onyx_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CxlRangeEncryptionLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Onyx_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Onyx_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Quartz_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_INF"
    File = "Addendum/Oem/Quartz/Library/CxlRangeEncryptionLib/CxlRangeEncryptionLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Quartz_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CxlRangeEncryptionLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Quartz_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Quartz_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Ruby_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_INF"
    File = "Addendum/Oem/Ruby/Library/CxlRangeEncryptionLib/CxlRangeEncryptionLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Ruby_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CxlRangeEncryptionLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Ruby_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Ruby_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Titanite_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_INF"
    File = "Addendum/Oem/Titanite/Library/CxlRangeEncryptionLib/CxlRangeEncryptionLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Titanite_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CxlRangeEncryptionLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Titanite_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Addendum_Oem_Titanite_Library_CxlRangeEncryptionLib_CxlRangeEncryptionLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_SMM_DRIVER_AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasProcBrhLib_CpmRasProcSmmBrhLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasProcBrhLib_CpmRasProcSmmBrhLib_INF"
    File = "Library/Proc/Ras/Brh/CpmRasProcBrhLib/CpmRasProcSmmBrhLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_SMM_DRIVER_AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasProcBrhLib_CpmRasProcSmmBrhLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CpmRasProcLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasProcBrhLib_CpmRasProcSmmBrhLib_INF"
    ModuleTypes = "DXE_SMM_DRIVER"
    Token = "DXE_SMM_DRIVER_AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasProcBrhLib_CpmRasProcSmmBrhLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AmdCpmPkg_Library_Proc_AmdFwConfigRuntimeLib_AmdFwConfigRuntimeLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Library_Proc_AmdFwConfigRuntimeLib_AmdFwConfigRuntimeLib_INF"
    File = "Library/Proc/AmdFwConfigRuntimeLib/AmdFwConfigRuntimeLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "_AmdCpmPkg_Library_Proc_AmdFwConfigRuntimeLib_AmdFwConfigRuntimeLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdFwConfigRuntimeLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Library_Proc_AmdFwConfigRuntimeLib_AmdFwConfigRuntimeLib_INF"
    Token = "_AmdCpmPkg_Library_Proc_AmdFwConfigRuntimeLib_AmdFwConfigRuntimeLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AmdCpmPkg_Library_AmdCxlPcieLib_AmdCxlPcieLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Library_AmdCxlPcieLib_AmdCxlPcieLib_INF"
    File = "Library/AmdCxlPcieLib/AmdCxlPcieLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "UEFI_DRIVER"
    Token = "_AmdCpmPkg_Library_AmdCxlPcieLib_AmdCxlPcieLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdCxlPcieLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Library_AmdCxlPcieLib_AmdCxlPcieLib_INF"
    Token = "_AmdCpmPkg_Library_AmdCxlPcieLib_AmdCxlPcieLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AmdCpmPkg_Library_Proc_Base_AmdCpmBaseLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Library_Proc_Base_AmdCpmBaseLib_INF"
    File = "Library/Proc/Base/AmdCpmBaseLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "_AmdCpmPkg_Library_Proc_Base_AmdCpmBaseLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdCpmBaseLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Library_Proc_Base_AmdCpmBaseLib_INF"
    Token = "_AmdCpmPkg_Library_Proc_Base_AmdCpmBaseLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasBrhLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasBrhLib_INF"
    File = "Library/Proc/Ras/Brh/CpmRasBrhLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "_AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasBrhLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CpmRasLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasBrhLib_INF"
    Token = "_AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasBrhLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasProcBrhLib_CpmRasProcDxeBrhLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasProcBrhLib_CpmRasProcDxeBrhLib_INF"
    File = "Library/Proc/Ras/Brh/CpmRasProcBrhLib/CpmRasProcDxeBrhLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasProcBrhLib_CpmRasProcDxeBrhLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CpmRasProcLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasProcBrhLib_CpmRasProcDxeBrhLib_INF"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasProcBrhLib_CpmRasProcDxeBrhLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasMemBrhLib_CpmRasMemBrhLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasMemBrhLib_CpmRasMemBrhLib_INF"
    File = "Library/Proc/Ras/Brh/CpmRasMemBrhLib/CpmRasMemBrhLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "_AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasMemBrhLib_CpmRasMemBrhLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CpmRasMemLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasMemBrhLib_CpmRasMemBrhLib_INF"
    Token = "_AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasMemBrhLib_CpmRasMemBrhLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AmdCpmPkg_Library_Proc_Fch_AmdCpmFch_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Library_Proc_Fch_AmdCpmFch_INF"
    File = "Library/Proc/Fch/AmdCpmFch.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "_AmdCpmPkg_Library_Proc_Fch_AmdCpmFch_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdCpmFchLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Library_Proc_Fch_AmdCpmFch_INF"
    Token = "_AmdCpmPkg_Library_Proc_Fch_AmdCpmFch_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AmdCpmPkg_Addendum_Oem_Galena_Library_OemGpioResetControlLib_OemGpioResetControlLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "0"
End

TOKEN
    Name = "_AmdCpmPkg_Addendum_Oem_Chalupa_Library_OemGpioResetControlLib_OemGpioResetControlLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "1"
End

TOKEN
    Name = "_AmdCpmPkg_Addendum_Oem_Purico_Library_OemGpioResetControlLib_OemGpioResetControlLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "2"
End

TOKEN
    Name = "_AmdCpmPkg_Addendum_Oem_Volcano_Library_OemGpioResetControlLib_OemGpioResetControlLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "3"
End

TOKEN
    Name = "_AmdCpmPkg_Addendum_Oem_Onyx_Library_OemGpioResetControlLib_OemGpioResetControlLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "4"
End

TOKEN
    Name = "_AmdCpmPkg_Addendum_Oem_Quartz_Library_OemGpioResetControlLib_OemGpioResetControlLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "5"
End

TOKEN
    Name = "_AmdCpmPkg_Addendum_Oem_Ruby_Library_OemGpioResetControlLib_OemGpioResetControlLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "6"
End

TOKEN
    Name = "_AmdCpmPkg_Addendum_Oem_Titanite_Library_OemGpioResetControlLib_OemGpioResetControlLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
    Token = "PLATFORM_SELECT" "=" "7"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Galena_Library_OemGpioResetControlLib_OemGpioResetControlLib_INF"
    File = "Addendum/Oem/Galena/Library/OemGpioResetControlLib/OemGpioResetControlLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "_AmdCpmPkg_Addendum_Oem_Galena_Library_OemGpioResetControlLib_OemGpioResetControlLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Chalupa_Library_OemGpioResetControlLib_OemGpioResetControlLib_INF"
    File = "Addendum/Oem/Chalupa/Library/OemGpioResetControlLib/OemGpioResetControlLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "_AmdCpmPkg_Addendum_Oem_Chalupa_Library_OemGpioResetControlLib_OemGpioResetControlLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Purico_Library_OemGpioResetControlLib_OemGpioResetControlLib_INF"
    File = "Addendum/Oem/Purico/Library/OemGpioResetControlLib/OemGpioResetControlLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "_AmdCpmPkg_Addendum_Oem_Purico_Library_OemGpioResetControlLib_OemGpioResetControlLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Volcano_Library_OemGpioResetControlLib_OemGpioResetControlLib_INF"
    File = "Addendum/Oem/Volcano/Library/OemGpioResetControlLib/OemGpioResetControlLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "_AmdCpmPkg_Addendum_Oem_Volcano_Library_OemGpioResetControlLib_OemGpioResetControlLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Onyx_Library_OemGpioResetControlLib_OemGpioResetControlLib_INF"
    File = "Addendum/Oem/Onyx/Library/OemGpioResetControlLib/OemGpioResetControlLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "_AmdCpmPkg_Addendum_Oem_Onyx_Library_OemGpioResetControlLib_OemGpioResetControlLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Quartz_Library_OemGpioResetControlLib_OemGpioResetControlLib_INF"
    File = "Addendum/Oem/Quartz/Library/OemGpioResetControlLib/OemGpioResetControlLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "_AmdCpmPkg_Addendum_Oem_Quartz_Library_OemGpioResetControlLib_OemGpioResetControlLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Ruby_Library_OemGpioResetControlLib_OemGpioResetControlLib_INF"
    File = "Addendum/Oem/Ruby/Library/OemGpioResetControlLib/OemGpioResetControlLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "_AmdCpmPkg_Addendum_Oem_Ruby_Library_OemGpioResetControlLib_OemGpioResetControlLib_SUPPORT" "=" "1"
End

INFComponent
    Name = "AmdCpmPkg_Addendum_Oem_Titanite_Library_OemGpioResetControlLib_OemGpioResetControlLib_INF"
    File = "Addendum/Oem/Titanite/Library/OemGpioResetControlLib/OemGpioResetControlLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "_AmdCpmPkg_Addendum_Oem_Titanite_Library_OemGpioResetControlLib_OemGpioResetControlLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "OemGpioResetControlLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Galena_Library_OemGpioResetControlLib_OemGpioResetControlLib_INF"
    Token = "_AmdCpmPkg_Addendum_Oem_Galena_Library_OemGpioResetControlLib_OemGpioResetControlLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "OemGpioResetControlLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Chalupa_Library_OemGpioResetControlLib_OemGpioResetControlLib_INF"
    Token = "_AmdCpmPkg_Addendum_Oem_Chalupa_Library_OemGpioResetControlLib_OemGpioResetControlLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "OemGpioResetControlLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Purico_Library_OemGpioResetControlLib_OemGpioResetControlLib_INF"
    Token = "_AmdCpmPkg_Addendum_Oem_Purico_Library_OemGpioResetControlLib_OemGpioResetControlLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "OemGpioResetControlLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Volcano_Library_OemGpioResetControlLib_OemGpioResetControlLib_INF"
    Token = "_AmdCpmPkg_Addendum_Oem_Volcano_Library_OemGpioResetControlLib_OemGpioResetControlLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "OemGpioResetControlLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Onyx_Library_OemGpioResetControlLib_OemGpioResetControlLib_INF"
    Token = "_AmdCpmPkg_Addendum_Oem_Onyx_Library_OemGpioResetControlLib_OemGpioResetControlLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "OemGpioResetControlLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Quartz_Library_OemGpioResetControlLib_OemGpioResetControlLib_INF"
    Token = "_AmdCpmPkg_Addendum_Oem_Quartz_Library_OemGpioResetControlLib_OemGpioResetControlLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "OemGpioResetControlLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Ruby_Library_OemGpioResetControlLib_OemGpioResetControlLib_INF"
    Token = "_AmdCpmPkg_Addendum_Oem_Ruby_Library_OemGpioResetControlLib_OemGpioResetControlLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "OemGpioResetControlLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Addendum_Oem_Titanite_Library_OemGpioResetControlLib_OemGpioResetControlLib_INF"
    Token = "_AmdCpmPkg_Addendum_Oem_Titanite_Library_OemGpioResetControlLib_OemGpioResetControlLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AmdCpmPkg_Library_AmdCpmCxlMboxLib_AmdCpmCxlMboxLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Library_AmdCpmCxlMboxLib_AmdCpmCxlMboxLib_INF"
    File = "Library/AmdCpmCxlMboxLib/AmdCpmCxlMboxLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "_AmdCpmPkg_Library_AmdCpmCxlMboxLib_AmdCpmCxlMboxLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "AmdCpmCxlMboxLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Library_AmdCpmCxlMboxLib_AmdCpmCxlMboxLib_INF"
    Token = "_AmdCpmPkg_Library_AmdCpmCxlMboxLib_AmdCpmCxlMboxLib_SUPPORT" "=" "1"
End

TOKEN
    Name = "_AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasPciBrhLib_SUPPORT"
    Value = "1"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasPciBrhLib_INF"
    File = "Library/Proc/Ras/Brh/CpmRasPciBrhLib.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "BASE"
    Token = "_AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasPciBrhLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "CpmRasPciLib"
    Instance = "AmdCpmPkg.AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasPciBrhLib_INF"
    Token = "_AmdCpmPkg_Library_Proc_Ras_Brh_CpmRasPciBrhLib_SUPPORT" "=" "1"
End

LibraryMapping
    Class = "FabricResourceManagerLib"
    Instance  = "AgesaModulePkg.AgesaModulePkg_Library_FabricResourceManagerBrhLib_FabricResourceManager3Lib_INF"
    Override  = "AmdCpmPkg.AmdCpmPkg_Features_MPDMA_BRH_Dxe_MPDMABrhDxe_INF"
End

LibraryMapping
    Class = "DxeAddressTranslateModuleConfigLib"
    Instance  = "AmdCpmPkg.AmdCpmPkg_Features_AmdPrm_PrmModule_PrmAddressTranslateModule_Library_Brh_DxeAddressTranslateModuleConfigLib_INF"
    Override  = "AmdCpmPkg.AmdCpmPkg_Features_AmdPrm_PrmConfigDxe_PrmConfigDxe_INF"
End

TOKEN
    Name = "DXE_DRIVER_AmdCpmPkg_Features_Xgmi_XgmiFreqDxe_SUPPORT"
    Value = "0"
    Help = ""
    TokenType = "Boolean"
    TargetH = Yes
End

INFComponent
    Name = "AmdCpmPkg_Features_Xgmi_XgmiFreqDxe_INF"
    File = "Features/Xgmi/XgmiFreqDxe.inf"
    Package = "AmdCpmPkg"
    ModuleTypes = "DXE_DRIVER"
    Token = "DXE_DRIVER_AmdCpmPkg_Features_Xgmi_XgmiFreqDxe_SUPPORT" "=" "1"
End
