//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************

/** @file  AmiProgressErrorCodeLib.h
    Declares structures, progress code , error code, progress code string and error code string
    used for by PEI and DXE status code driver 

**/

#ifndef __AMI_PROGRESS_ERROR_CODE_LIB_H__
#define __AMI_PROGRESS_ERROR_CODE_LIB_H__

#define POST_MSG_FOREGROUND         0x07    //White
#define POST_MSG_BACKGROUND         0x00    //Black

#define MAJOR_ERROR_FOREGROUND      0x04    //Red
#define MAJOR_ERROR_BACKGROUND      0x00    //Black

#define MINOR_ERROR_FOREGROUND      0x06    //Brown
#define MINOR_ERROR_BACKGROUND      0x00    //Black

#define PROGRESS_CODE_FOREGROUND    0x02    //Green
#define PROGRESS_CODE_BACKGROUND    0x00    //Black

#define DISPLAY_BLINK               0x00    // Not Supported  

/**

    Return the PostCode value for the Progress Code 
  
    @param  EFI ProgressCode

    @return  PostCode.

**/
UINT8
GetAmiProgressCodeCheckPoint (
    IN EFI_STATUS_CODE_VALUE    ProgressCode 
);

/**

    Return the PostCode value for the EFI Error Code 
  
    @param  EFI Error Code

    @return  PostCode.

**/
UINT8
GetAmiErrorCodeCheckPoint (
    IN EFI_STATUS_CODE_VALUE    ErrorCode 
);

/**

    Return the String for the Progress Code 
  
    @param  EFI ProgressCode

    @return  String.

**/
CHAR8*
GetAmiProgressCodeString (
    IN EFI_STATUS_CODE_VALUE    ProgressCode 
);

/**

    Return the String for the Progress Code 
  
    @param  EFI ProgressCode

    @return  String.

**/
EFI_STATUS
GetAmiErrorCodeString (
    IN EFI_STATUS_CODE_VALUE    ErroCode,
    OUT CHAR8                   **ErrorCodeString,
    OUT CHAR8                   **RootCauseCodeString,
    OUT CHAR8                   **PosibileSolutionString
);

/**

    Checks whether the PostCode is progress code or error code

    @param PostCode
    
    @param ProgressOrErrorCode

**/
EFI_STATUS
CheckProgressOrErrorCode (
    IN  UINT8      PostCode,
    OUT BOOLEAN    *ProgressOrErrorCode
);

#endif
