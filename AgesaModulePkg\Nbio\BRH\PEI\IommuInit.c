/*****************************************************************************
 *
 * Copyright (C) 2008-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
 *******************************************************************************
 */

#include "AmdNbioPei.h"
#include <Filecode.h>

#define FILECODE NBIO_BRH_PEI_IOMMUINIT_FILECODE

//A0 related
#define SMN_IOAGR_L1_INTRPT_ORDER_CTRL_ADDRESS_A0                0x153001fcUL

/*----------------------------------------------------------------------------------------
 *                         E X T E R N   D E F I N I T I O N S
 *----------------------------------------------------------------------------------------
 */
extern SMN_TABLE ROMDATA      GnbIommuEnvInitTable [];
extern PEI_AMD_NBIO_IOMMU_FEATURE_PPI mNbioIommuFeaturePpi;

/*----------------------------------------------------------------------------------------
 *                   D E F I N I T I O N S    A N D    M A C R O S
 *----------------------------------------------------------------------------------------
 */

/*----------------------------------------------------------------------------------------
 *                  T Y P E D E F S     A N D     S T R U C T U  R E S
 *----------------------------------------------------------------------------------------
 */


/*----------------------------------------------------------------------------------------
 *           P R O T O T Y P E S     O F     L O C A L     F U  N C T I O N S
 *----------------------------------------------------------------------------------------
 */



/*----------------------------------------------------------------------------------------
 *                    P P I   N O T I F Y   D E S C R I P T O R S
 *----------------------------------------------------------------------------------------
 */
STATIC EFI_PEI_PPI_DESCRIPTOR mNbioIommuFeaturePpiList =
{
  (EFI_PEI_PPI_DESCRIPTOR_PPI | EFI_PEI_PPI_DESCRIPTOR_TERMINATE_LIST),
  &gAmdNbioIommuFeaturePpiGuid,
  &mNbioIommuFeaturePpi
};


/*----------------------------------------------------------------------------------------*/
/**
 * AmdNbio IOMMU PEI initialization
 *
 *
 *
 * @param[in]  PCIe_PLATFORM_CONFIG Pointer to Pcie topology
 * @retval     EFI_STATUS
 */

EFI_STATUS
AmdIommuInit (
  IN       PCIe_PLATFORM_CONFIG  *Pcie
  )
{

  FABRIC_TARGET                      MmioTarget = {0};
  FABRIC_MMIO_ATTRIBUTE              MmioAttr;
  UINT64                             IommMmioSize;
  UINT64                             IommMmioBase;
  GNB_HANDLE                         *GnbHandle;
  PCI_ADDR                           IommuPciAddress;
  UINT32                             Value;
  BOOLEAN                            ReserveIommuBar;
  UINT32                             Property;
  IOMMU_MMIO_CONTROL0_W_STRUCT       MmioControl0;
  SECURE_ENCRYPTION_EAX              SecureEncruptionEax;
  BOOLEAN                            SnpSupported;
  EFI_STATUS                         Status;

  Property = TABLE_PROPERTY_DEFAULT;

  if (PcdGetBool (PcdIommuL1ClockGatingEnable)) {
    Property |= PROPERTY_IOMMU_L1CLKGATING_ENABLED;
  } else {
    Property |= PROPERTY_IOMMU_L1CLKGATING_DISABLED;
  }
  if (PcdGetBool (PcdIommuL2ClockGatingEnable)) {
    Property |= PROPERTY_IOMMU_L2CLKGATING_ENABLED;
  } else {
    Property |= PROPERTY_IOMMU_L2CLKGATING_DISABLED;
  }
  if (xApicMode == PcdGet8 (PcdAmdApicMode)) {
    Property |= PROPERTY_XAPIC_MODE;
  }
  if (FALSE == PcdGetBool (PcdCfgIommuSupport)) {
    Property |= PROPERTY_IOMMU_DISABLED;
  }
  if (PcdGet8 (PcdAmdFabric1TbRemap) == 0) {
    Property |= PROPERTY_IOMMU_MAP_DRAM_INTO_HT_HOLE_ENABLED;
  }

  AGESA_TESTPOINT (TpNbioIommuPEIEntry, NULL);
  IDS_HDT_CONSOLE (MAIN_FLOW, "%a Entry\n", __FUNCTION__);

  if (PcdGetBool (PcdCfgIommuMMIOAddressReservedEnable) == 0x00) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "%a : We do not need reserved IOMMU MMIO space from GNB PEIM \n", __FUNCTION__);
    ReserveIommuBar = FALSE;
  }
  else {
    IDS_HDT_CONSOLE (MAIN_FLOW, "%a : Will reserve IOMMU Bars \n", __FUNCTION__);
    ReserveIommuBar = TRUE;
  }

  SnpSupported = FALSE;
  if (PcdGetBool (PcdCfgSevSnpSupport)) {
    SecureEncruptionEax.Value = 0;
    AsmCpuid (CPUID_AMD_SECURE_ENCRYPTION, &(SecureEncruptionEax.Value), NULL, NULL, NULL);
    if (SecureEncruptionEax.Field.SNP != 0) {
      SnpSupported = TRUE;
    }
  }

  GnbHandle = NbioGetHandle (Pcie);

  // Loop to program each GNB Handle appropriately
  while (GnbHandle != NULL) {
    if (GnbHandle->RBIndex < 4) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "%a : GnbHandle = 0x%x\n", __FUNCTION__ , GnbHandle);

      // Allocate BAR for IOMMU
      if (ReserveIommuBar) {
        MmioTarget.TgtType = TARGET_PCI_BUS;
        MmioTarget.SocketNum = GnbHandle->SocketId;
        MmioTarget.PciBusNum = (UINT16) GnbHandle->Address.Address.Bus;
        MmioTarget.PciSegNum = (UINT16) GnbHandle->Address.Address.Segment;
        MmioTarget.RbNum = GnbHandle->RBIndex;
        IommMmioSize = SIZE_512KB;
        MmioAttr.MmioType = NON_PCI_DEVICE_BELOW_4G;
        FabricAllocateMmio (&IommMmioBase, &IommMmioSize, ALIGN_512K, MmioTarget, &MmioAttr);
        IDS_HDT_CONSOLE (MAIN_FLOW, "%a : IOMMU MMIO at address 0x%x for Socket 0x%x Silicon 0x%x\n", __FUNCTION__ , IommMmioBase, GnbHandle->SocketId, GnbHandle->DieNumber);

        Value = (UINT32)IommMmioBase;
        IommuPciAddress = NbioGetHostPciAddress (GnbHandle);
        IommuPciAddress.Address.Function = 0x2;
        GnbLibPciWrite (IommuPciAddress.AddressValue | PCICFG_IOMMUL2_IOMMU_CAP_BASE_LO_OFFSET, AccessS3SaveWidth32, &Value, NULL);
      }

      // Program up IOMMU NBIO Tables
      NbioSmnTable (GnbHandle, GnbIommuEnvInitTable, NBIO_SPACE (GnbHandle, 0), Property, 0);

      // Disable IOMMUs via PCDs
      if (GnbHandle->SocketId == 0) {
        if (GnbHandle->RBIndex == 0 && !PcdGetBool(PcdCfgIommuSocket0Nbio0Enable)) {
          SmnRegisterRMWS (
            GnbHandle->Address.Address.Segment,
            GnbHandle->Address.Address.Bus,
            NBIO_SPACE (GnbHandle, SMN_CFG_IOHC_PCI_ADDRESS),
            (UINT32)~(CFG_IOHC_PCI_CFG_IOHC_PCI_Dev0Fn2RegEn_MASK),
            0 << CFG_IOHC_PCI_CFG_IOHC_PCI_Dev0Fn2RegEn_OFFSET,
            0
            );
        } else if (GnbHandle->RBIndex == 1 && !PcdGetBool(PcdCfgIommuSocket0Nbio1Enable)) {
          SmnRegisterRMWS (
            GnbHandle->Address.Address.Segment,
            GnbHandle->Address.Address.Bus,
            NBIO_SPACE (GnbHandle, SMN_CFG_IOHC_PCI_ADDRESS),
            (UINT32)~(CFG_IOHC_PCI_CFG_IOHC_PCI_Dev0Fn2RegEn_MASK),
            0 << CFG_IOHC_PCI_CFG_IOHC_PCI_Dev0Fn2RegEn_OFFSET,
            0
            );
        } else if (GnbHandle->RBIndex == 2 && !PcdGetBool(PcdCfgIommuSocket0Nbio2Enable)) {
          SmnRegisterRMWS (
            GnbHandle->Address.Address.Segment,
            GnbHandle->Address.Address.Bus,
            NBIO_SPACE (GnbHandle, SMN_CFG_IOHC_PCI_ADDRESS),
            (UINT32)~(CFG_IOHC_PCI_CFG_IOHC_PCI_Dev0Fn2RegEn_MASK),
            0 << CFG_IOHC_PCI_CFG_IOHC_PCI_Dev0Fn2RegEn_OFFSET,
            0
            );
        } else if (GnbHandle->RBIndex == 3 && !PcdGetBool(PcdCfgIommuSocket0Nbio3Enable)) {
          SmnRegisterRMWS (
            GnbHandle->Address.Address.Segment,
            GnbHandle->Address.Address.Bus,
            NBIO_SPACE (GnbHandle, SMN_CFG_IOHC_PCI_ADDRESS),
            (UINT32)~(CFG_IOHC_PCI_CFG_IOHC_PCI_Dev0Fn2RegEn_MASK),
            0 << CFG_IOHC_PCI_CFG_IOHC_PCI_Dev0Fn2RegEn_OFFSET,
            0
            );
        }
      } else if (GnbHandle->SocketId == 1) {
        if (GnbHandle->RBIndex == 0 && !PcdGetBool(PcdCfgIommuSocket1Nbio0Enable)) {
          SmnRegisterRMWS (
            GnbHandle->Address.Address.Segment,
            GnbHandle->Address.Address.Bus,
            NBIO_SPACE (GnbHandle, SMN_CFG_IOHC_PCI_ADDRESS),
            (UINT32)~(CFG_IOHC_PCI_CFG_IOHC_PCI_Dev0Fn2RegEn_MASK),
            0 << CFG_IOHC_PCI_CFG_IOHC_PCI_Dev0Fn2RegEn_OFFSET,
            0
            );
        } else if (GnbHandle->RBIndex == 1 && !PcdGetBool(PcdCfgIommuSocket1Nbio1Enable)) {
          SmnRegisterRMWS (
            GnbHandle->Address.Address.Segment,
            GnbHandle->Address.Address.Bus,
            NBIO_SPACE (GnbHandle, SMN_CFG_IOHC_PCI_ADDRESS),
            (UINT32)~(CFG_IOHC_PCI_CFG_IOHC_PCI_Dev0Fn2RegEn_MASK),
            0 << CFG_IOHC_PCI_CFG_IOHC_PCI_Dev0Fn2RegEn_OFFSET,
            0
            );
        } else if (GnbHandle->RBIndex == 2 && !PcdGetBool(PcdCfgIommuSocket1Nbio2Enable)) {
          SmnRegisterRMWS (
            GnbHandle->Address.Address.Segment,
            GnbHandle->Address.Address.Bus,
            NBIO_SPACE (GnbHandle, SMN_CFG_IOHC_PCI_ADDRESS),
            (UINT32)~(CFG_IOHC_PCI_CFG_IOHC_PCI_Dev0Fn2RegEn_MASK),
            0 << CFG_IOHC_PCI_CFG_IOHC_PCI_Dev0Fn2RegEn_OFFSET,
            0
            );
        } else if (GnbHandle->RBIndex == 3 && !PcdGetBool(PcdCfgIommuSocket1Nbio3Enable)) {
          SmnRegisterRMWS (
            GnbHandle->Address.Address.Segment,
            GnbHandle->Address.Address.Bus,
            NBIO_SPACE (GnbHandle, SMN_CFG_IOHC_PCI_ADDRESS),
            (UINT32)~(CFG_IOHC_PCI_CFG_IOHC_PCI_Dev0Fn2RegEn_MASK),
            0 << CFG_IOHC_PCI_CFG_IOHC_PCI_Dev0Fn2RegEn_OFFSET,
            0
            );
        }
      }

      if (PcdGet8(PcdAmdApicMode) != xApicMode) {
        IommuPciAddress = NbioGetHostPciAddress (GnbHandle);
        IommuPciAddress.Address.Function = 0x2;
        GnbLibPciRead(IommuPciAddress.AddressValue | PCICFG_IOMMUL2_IOMMU_MMIO_CONTROL0_W_OFFSET, AccessWidth32, &(MmioControl0.Value), NULL);
        MmioControl0.Field.XT_SUP_W = 1;
        MmioControl0.Field.GA_SUP_W = 1;
        GnbLibPciWrite(IommuPciAddress.AddressValue | PCICFG_IOMMUL2_IOMMU_MMIO_CONTROL0_W_OFFSET, AccessS3SaveWidth32, &(MmioControl0.Value), NULL);
      }

      // IOMMU configuration for SEV-TIO enabled
      SmnRegisterRMWS (
        GnbHandle->Address.Address.Segment,
        GnbHandle->Address.Address.Bus,
        NBIO_SPACE (GnbHandle, SMN_IOMMU_MMIO_CONTROL1_W_ADDRESS),
        (UINT32)~(IOMMU_MMIO_CONTROL1_W_SEVSNPIO_SUP_W_MASK),
        (UINT32)((PcdGetBool (PcdCfgSevTioSupport) ? 1 : 0) << IOMMU_MMIO_CONTROL1_W_SEVSNPIO_SUP_W_OFFSET),
        0
        );

      // IOMMU configuration for SEV-SNP enabled
      if (PcdGetBool (PcdCfgSevSnpSupport)) {
        SmnRegisterRMWS (GnbHandle->Address.Address.Segment, GnbHandle->Address.Address.Bus,
          NBIO_SPACE (GnbHandle, SMN_IOMMU_MMIO_CONTROL0_W_ADDRESS),
          (UINT32)~(IOMMU_MMIO_CONTROL0_W_GAM_SUP_W_MASK |
                    IOMMU_MMIO_CONTROL0_W_sATS_SUP_W_MASK |
                    IOMMU_MMIO_CONTROL0_W_GstBufferTRPModeSup_W_MASK),
                    (1 << IOMMU_MMIO_CONTROL0_W_GAM_SUP_W_OFFSET) |
                    (1 << IOMMU_MMIO_CONTROL0_W_sATS_SUP_W_OFFSET) |
                    (1 << IOMMU_MMIO_CONTROL0_W_GstBufferTRPModeSup_W_OFFSET),
          0);
        SmnRegisterRMWS (GnbHandle->Address.Address.Segment, GnbHandle->Address.Address.Bus,
          NBIO_SPACE (GnbHandle, SMN_SHDWL2A_IOMMU_MMIO_CONTROL0_W_ADDRESS),
          (UINT32)~(SHDWL2A_IOMMU_MMIO_CONTROL0_W_GAM_SUP_W_MASK),
                    (1 << SHDWL2A_IOMMU_MMIO_CONTROL0_W_GAM_SUP_W_OFFSET),
          0);
        SmnRegisterRMWS (GnbHandle->Address.Address.Segment, GnbHandle->Address.Address.Bus,
          NBIO_SPACE (GnbHandle, SMN_IOMMU0NBIO0_IOMMU_MMIO_CNTRL_0_ADDRESS),
          (UINT32)~(IOMMU_MMIO_CNTRL_0_GT_EN_MASK),
          (1 << IOMMU_MMIO_CNTRL_0_GT_EN_OFFSET),
          0);
        SmnRegisterRMWS (GnbHandle->Address.Address.Segment, GnbHandle->Address.Address.Bus,
          NBIO_SPACE (GnbHandle, SMN_IOMMU0NBIO0_IOMMU_MMIO_EFR_1_ADDRESS),
          (UINT32)~(IOMMU_MMIO_EFR_1_SNP_SUP_MASK),
          (UINT32)(1 << IOMMU_MMIO_EFR_1_SNP_SUP_OFFSET),
          0);
        SmnRegisterRMWS (GnbHandle->Address.Address.Segment, GnbHandle->Address.Address.Bus,
          NBIO_SPACE (GnbHandle, SMN_IOMMU0NBIO0_IOMMU_MMIO_CONTROL1_W_ADDRESS),
          (UINT32)~(IOMMU_MMIO_CONTROL1_W_ForcePhyDestSup_W_MASK |
                    IOMMU_MMIO_CONTROL1_W_GAPPI_SUP_W_MASK |
                    IOMMU_MMIO_CONTROL1_W_GAPPIDis_SUP_W_MASK),
          (UINT32)((0 << IOMMU_MMIO_CONTROL1_W_ForcePhyDestSup_W_OFFSET) |
                   (1 << IOMMU_MMIO_CONTROL1_W_GAPPI_SUP_W_OFFSET) |
                   (1 << IOMMU_MMIO_CONTROL1_W_GAPPIDis_SUP_W_OFFSET)),
          0);
        SmnRegisterRMWS (GnbHandle->Address.Address.Segment, GnbHandle->Address.Address.Bus,
          NBIO_SPACE (GnbHandle, SMN_IOMMU0NBIO0_IOMMU_MMIO_CNTRL_1_ADDRESS),
          (UINT32)~(IOMMU_MMIO_CNTRL_1_EPH_EN_MASK |
                    IOMMU_MMIO_CNTRL_1_GCR3TRPMode_MASK |
                    IOMMU_MMIO_CNTRL_1_GstBufferTRPMode_MASK |
                    IOMMU_MMIO_CNTRL_1_SNPAVICEn_MASK |
                    IOMMU_MMIO_CNTRL_1_GAPPI_EN_MASK |
                    IOMMU_MMIO_CNTRL_1_PageMigration_EN_MASK |
                    IOMMU_MMIO_CNTRL_1_CXLIOEn_MASK),
          (UINT32)((1 << IOMMU_MMIO_CNTRL_1_EPH_EN_OFFSET) |
          (1 << IOMMU_MMIO_CNTRL_1_GCR3TRPMode_OFFSET) |
          (1 << IOMMU_MMIO_CNTRL_1_GstBufferTRPMode_OFFSET) |
          (1 << IOMMU_MMIO_CNTRL_1_SNPAVICEn_OFFSET) |
          (1 << IOMMU_MMIO_CNTRL_1_GAPPI_EN_OFFSET) |
          (1 << IOMMU_MMIO_CNTRL_1_PageMigration_EN_OFFSET) |
          ((PcdGetBool (PcdAmdCxlOnAllPorts)? 1 : 0) << IOMMU_MMIO_CNTRL_1_CXLIOEn_OFFSET)),
          0);
      } else {
        // IOMMU General AVIC modes support
        SmnRegisterRMWS (GnbHandle->Address.Address.Segment, GnbHandle->Address.Address.Bus,
          NBIO_SPACE (GnbHandle, SMN_IOMMU_MMIO_CONTROL0_W_ADDRESS),
          (UINT32)~(IOMMU_MMIO_CONTROL0_W_GAM_SUP_W_MASK),
                ((PcdGetBool(PcdCfgIommuAvicSupport)? 1 : 0) << IOMMU_MMIO_CONTROL0_W_GAM_SUP_W_OFFSET),
          0);
        SmnRegisterRMWS (GnbHandle->Address.Address.Segment, GnbHandle->Address.Address.Bus,
          NBIO_SPACE (GnbHandle, SMN_SHDWL2A_IOMMU_MMIO_CONTROL0_W_ADDRESS),
          (UINT32)~(SHDWL2A_IOMMU_MMIO_CONTROL0_W_GAM_SUP_W_MASK),
                ((PcdGetBool(PcdCfgIommuAvicSupport)? 1 : 0) << SHDWL2A_IOMMU_MMIO_CONTROL0_W_GAM_SUP_W_OFFSET),
          0);
      }

      // IOMMU configuration for SEV-SNP disabled
      SmnRegisterRMWS (GnbHandle->Address.Address.Segment, GnbHandle->Address.Address.Bus,
        NBIO_SPACE (GnbHandle, SMN_IOMMU0NBIO0_IOMMU_MMIO_CONTROL1_W_ADDRESS),
        (UINT32)~(IOMMU_MMIO_CONTROL1_W_SNP_SUP_W_MASK),
            ((SnpSupported ? 1 : 0) << IOMMU_MMIO_CONTROL1_W_SNP_SUP_W_OFFSET),
        0);

      SmnRegisterReadS (GnbHandle->Address.Address.Segment, GnbHandle->Address.Address.Bus,
        NBIO_SPACE (GnbHandle, SMN_IOMMU0NBIO0_IOMMU_MMIO_CONTROL1_W_ADDRESS),
        &Value
        );
      IDS_HDT_CONSOLE (MAIN_FLOW, "Read back from IOMM_MMIO_CONTRO1_W 0x%x\n", Value);

      //Enable relaxed interrupt ordering for A0. Its enabled by default in B0
      /*Status = PcieGetLogicalId(&LogicalId);
      if (!EFI_ERROR (Status))
      {
          if(LogicalId.Revision & AMD_REV_F1A_BRH_Ax)
          {
              Value = 0xFFFF;
              SmnRegisterWriteS (GnbHandle->Address.Address.Segment,GnbHandle->Address.Address.Bus,
                                   NBIO_SPACE(GnbHandle, SMN_IOAGR_L1_INTRPT_ORDER_CTRL_ADDRESS_A0), &Value, 0);
          }
      }*/
      //Fix for Returning ordering for IO read responses and IO write  response.L1_INTRPT_ORDER_CTRL[18:17] = 0x0
      //this is added to eliminate the issue arising from SMI interrupt that is critical to SMM process arriving late.
      //the SMI was being re-ordered behind the IO Write
      Value = 0x1FFFF;
      SmnRegisterWriteS (GnbHandle->Address.Address.Segment,GnbHandle->Address.Address.Bus,
                     NBIO_SPACE(GnbHandle, SMN_IOMMU0IOAGRNBIO0_L1_INTRPT_ORDER_CTRL_ADDRESS), &Value, 0);
      SmnRegisterWriteS (GnbHandle->Address.Address.Segment,GnbHandle->Address.Address.Bus,
                     NBIO_SPACE(GnbHandle, SMN_IOMMU0PCIE0NBIO0_L1_INTRPT_ORDER_CTRL_ADDRESS), &Value, 0);
      SmnRegisterWriteS (GnbHandle->Address.Address.Segment,GnbHandle->Address.Address.Bus,
                     NBIO_SPACE(GnbHandle, SMN_IOMMU0PCIE1NBIO0_L1_INTRPT_ORDER_CTRL_ADDRESS), &Value, 0);

      // Sets the GAM_SUP value in IOMMU Extended feature register.
      SmnRegisterRMWS (GnbHandle->Address.Address.Segment, GnbHandle->Address.Address.Bus,
          NBIO_SPACE (GnbHandle, SMN_IOMMU_MMIO_CONTROL0_W_ADDRESS),
          (UINT32)~(IOMMU_MMIO_CONTROL0_W_GAM_SUP_W_MASK |
                    IOMMU_MMIO_CONTROL0_W_GstBufferTRPModeSup_W_MASK),
              (1 << IOMMU_MMIO_CONTROL0_W_GAM_SUP_W_OFFSET) |
              (1 << IOMMU_MMIO_CONTROL0_W_GstBufferTRPModeSup_W_OFFSET),
          0);
      // IOMMU configuration for ATS enabled
      SmnRegisterRMWS (GnbHandle->Address.Address.Segment, GnbHandle->Address.Address.Bus,
        NBIO_SPACE (GnbHandle, SMN_SHDWL2A_PCIE_ATS_CNTL0_ADDRESS),
        (UINT32)~(SHDWL2A_PCIE_ATS_CNTL0_ATC_ENABLE_MASK),
            ((PcdGetBool(PcdCfgIommuL2AtsCntlEn)? 1 : 0) << SHDWL2A_PCIE_ATS_CNTL0_ATC_ENABLE_OFFSET),
        0);
      SmnRegisterRMWS (GnbHandle->Address.Address.Segment, GnbHandle->Address.Address.Bus,
        NBIO_SPACE (GnbHandle, SMN_SHDWL2B_PCIE_ATS_CNTL0_ADDRESS),
        (UINT32)~(SHDWL2B_PCIE_ATS_CNTL0_ATC_ENABLE_MASK),
            ((PcdGetBool(PcdCfgIommuL2AtsCntlEn)? 1 : 0) << SHDWL2B_PCIE_ATS_CNTL0_ATC_ENABLE_OFFSET),
        0);
    }
    GnbHandle = GnbGetNextHandle (GnbHandle);
  }

  Status = PeiServicesInstallPpi (&mNbioIommuFeaturePpiList);
  if (Status != EFI_SUCCESS) {
    IDS_HDT_CONSOLE (MAIN_FLOW, "Install NbioIommuFeaturePpi Returned ERROR!!!\n");
  }

  IDS_HDT_CONSOLE (MAIN_FLOW, "%a Exit\n", __FUNCTION__);

  AGESA_TESTPOINT (TpNbioIommuPEIExit, NULL);
  return EFI_SUCCESS;
}

