/*****************************************************************************
 * Copyright (C) 2023-2024 Advanced Micro Devices, Inc. All rights reserved.
 *
*****************************************************************************
*/
/* $NoKeywords:$ */
/**
 * @file
 *
 * PSP SFS related functions
 *
 *
 * @xrefitem bom "File Content Label" "Release Content"
 * @e project:      AGESA
 * @e sub-project:  PSP
 */
#include "Uefi.h"
#include "AGESA.h"
#include <Library/UefiBootServicesTableLib.h>
#include <Library/AmdBaseLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/AmdPspMboxLibV2.h>
#include <Protocol/AmdApcbProtocol.h>
#include <BRH/ApcbV3TokenUid.h>
#include <Library/AmdPspBaseLibV2.h>
#include <Library/AmdPspRegBaseLib.h>
#include <Library/AmdPspBaseLibV2.h>
#include <Filecode.h>

#define FILECODE LIBRARY_AMDPSPSFSLIB_AMDPSPSFSLIB_FILECODE

/*=================================================================================================
*                          Definitions related to TEE extended commands
* =================================================================================================
*/

#pragma pack (push, 1)

#define TEE_EXT_CMD_MAX_SIZE        SIZE_4KB
#define INT32_MAX                   0x7FFFFFFF           /* Maximum (signed) int32 value */

#define TEE_UINT64                  UINT64
#define TEE_UINT32                  UINT32
#define TEE_UINT8                   UINT8
#define TEE_CHAR                    CHAR8

#define TEE_CMD_STATUS_MASK         0x0000FFFF
#define TEE_CMD_ID_MASK             0x000F0000
#define TEE_CMD_VERSION_MASK        0x00F00000
#define TEE_CMD_RESERVED_MASK       0x7F000000
#define TEE_CMD_RESPONSE_MASK       0x80000000

#define TEE_CMD_ID_SHIFT            16
#define TEE_CMD_VERSION_SHIFT       20

/** @def TEE_VERSION_3  AMD-TEE 3.0 */
#define TEE_VERSION_3               3


/** @enum TEE_IF_CMD_ID
* TEE I/F Command IDs for the register interface.
*  Command ID must be between 0x00010000 and 0x000F0000.
*/
typedef enum
{
    TEE_IF_CMD_ID_INIT_RING         = 0x00010000,   /**< Initialize ring buffer */
    TEE_IF_CMD_ID_DESTROY_RING      = 0x00020000,   /**< Destroy ring buffer */
    TEE_IF_CMD_ID_GET_TEE_VERSION   = 0x00030000,   /**< Get AMD-TEE version number in status field */

    TEE_IF_CMD_ID_CLEAR_MCE_LOG     = 0x00040000,   /**< Erase MCE log (Server specific)*/
    TEE_IF_CMD_ID_LOAD_PM_MPDMA     = 0x00050000,   /**< MPDMA reload (Server specific)*/
    TEE_IF_CMD_ID_READ_MCE_LOG      = 0x00060000,   /**< Read MCE log (Server specific) */

    TEE_IF_CMD_ID_CMD_EXTENDED      = 0x000E0000,  /**< Execute extended command, to support sub-commands */

    TEE_IF_CMD_ID_MAX               = 0x000F0000,   /**< Max command ID */

} TEE_IF_CMD_ID;


/** @struct TEE_CTRL
* Control registers of the TEE interface. These are located in C2P_MSG_xx
* registers.
*/
typedef struct TEE_CTRL
{
    volatile TEE_UINT32   CmdResp;          /**< +0   Command/Response register for Gfx commands */
    volatile TEE_UINT32   CmdBufAddrLo;     /**< +4   Bits [31:0] of SoC address of command buffer */
    volatile TEE_UINT32   CmdBufAddrHi;     /**< +8   Bits [63:32] of SoC address of command buffer */
    volatile TEE_UINT32   Wptr;             /**< +12  Write pointer (offset) of ring buffer */
    volatile TEE_UINT32   Rptr;             /**< +16  Read pointer (offset) of ring buffer */

} TEE_CTRL;

/** @def  TEE_FLAG_RESPONSE
* Response flag is set in the command when command is completed by PSP.
* Used in the TEE_CTRL.CmdResp.
* When PSP TEE I/F is initialized, the flag is set.
*/
#define TEE_FLAG_RESPONSE               0x80000000

/** @enum TEE_SUB_CMD_ID
* Sub Command IDs supported in TEE Extended command
*/
typedef enum
{
    TEE_SUB_CMD_SFS_GET_FW_VERSIONS       = 0x01,  /*!< Get report of loaded FW versions and their patch numbers */
    TEE_SUB_CMD_SFS_UPDATE                = 0x02,  /*!< Execute SFS update payload */
    TEE_SUB_CMD_GET_SFS_ATTESTATION_DATA  = 0x03,  /*!< Get SFS Attestation data */
    TEE_SUB_CMD_DPE_INTERFACE             = 0x04,  /*!< Execute DPE services */
    TEE_SUB_CMD_LOAD_FW                   = 0x10,  /*!< Verify and Load FW, to its respective destination. */
    TEE_SUB_CMD_MAX                       = INT32_MAX
} TEE_SUB_CMD_ID;

/** @struct TEE_EXT_CMD_HEADER
* \brief header of extended TEE I/F command
* \ingroup TEE_INTERFACE
*
* Standard header structure for extended TEE I/F command buffer
*/
typedef struct TEE_EXT_CMD_HEADER
{
    TEE_UINT32        TotalSize;      /*!< [in]  Total Size of EXT_CMD_BUFFER (including this header) in bytes */
    TEE_SUB_CMD_ID    SubCmdId;       /*!< [in]  Sub-command ID */
    TEE_UINT32        Status;         /*!< [out] Command execution status */
} TEE_EXT_CMD_HEADER;

/** @struct TEE_SUB_CMD_FW_LOAD
* Command to Load a FW
*/
typedef struct TEE_SUB_CMD_FW_LOAD
{
    TEE_UINT32    FwType;          // FwType to be loaded
    TEE_UINT32    FwSize;          // Size of the FW buffer (attached next to TEE_EXT_CMD structure)
} TEE_SUB_CMD_FW_LOAD;

/** @union TEE_EXT_SUB_COMMAND
 All TEE Extended Sub commands.
 */
typedef union TEE_EXT_SUB_COMMAND
{
    TEE_SUB_CMD_FW_LOAD    ExtCmdFwLoad;

} TEE_EXT_SUB_COMMAND;

/** @struct TEE_SUB_CMD_FW_LOAD
* \brief Structure of extended TEE I/F command
* \ingroup TEE_INTERFACE
*
* Structure for extended TEE I/F command buffer
*/
typedef struct TEE_EXT_CMD
{
    TEE_EXT_CMD_HEADER   Header;
    TEE_EXT_SUB_COMMAND  ExtSubCmd;
    TEE_UINT8            Reserved[ TEE_EXT_CMD_MAX_SIZE
                          - sizeof(TEE_EXT_CMD_HEADER)
                          - sizeof(TEE_EXT_SUB_COMMAND) ];   // For alignment and future expansion
    /** @note Total size 4096 bytes */
} TEE_EXT_CMD;

typedef struct FW_VERSION_INFO
{
  UINT32  VersionType;
  UINT32  Version;
  UINT32  SecPatchLvl;
} FW_VERSION_INFO;

typedef struct SFS_VERSION_INFO
{
  UINT32            SfSApiVersion;        // offset 0x0
  UINT32            CurrentPatchLvl;      // offset 0x4
  UINT32            SysPatchLvl;          // offset 0x8
  UINT32            NumSupportedFws;      // offset 0xC
  FW_VERSION_INFO   FwVersionInfo[300];   // offset 0x10 - 0xE1F
  UINT8             Reserved[432];        // offset 0xE20 - 0xFCF
  UINT8             CoSignTokenHash[48];  // offset 0xFD0 - 0xFFF
} SFS_VERSION_INFO;

// SFS Status codes
//
#define SFS_SUCCESS                     0x00
#define SFS_INVALID_PAYLOAD_ADDRESS     0x01
#define SFS_INVALID_TOTAL_SIZE          0x02
#define SFS_INVALID_IMAGE_ADDRESS       0x03
#define SFS_INVALID_IMAGE_SIZE          0x04
#define SFS_PATCHING_NOT_ALLOWED        0x05
#define SFS_INVALID_CUSTOMER_SIGNATURE  0x06
#define SFS_INVALID_AMD_SIGNATURE       0x07
#define SFS_CUSTOMER_SIGNATURE_MISMATCH 0x08
#define SFS_CUSTOMER_SIGNED_NOT_ALLOWED 0x09
#define SFS_INVALID_BASE_PATCH_LEVEL    0x0A
#define SFS_INVALID_CURRENT_PATCH_LEVEL 0x0B
#define SFS_INVALID_NEW_PATCH_LEVEL     0x0C
#define SFS_INVALID_SUBCOMMAND          0x0D
#define SFS_HEADER_ATTESTATION_MISMATCH 0x0E
#define SFS_TMR_PROTECTION_FAIL         0x0F
#define SFS_MODULE_NOT_LOADED           0x10
#define SFS_ERROR_AMD_DATA_ALIGNMENT    0x11
#define SFS_MODULE_UNLOAD_FAIL          0x12
#define SFS_MODULE_LOAD_FAIL            0x13
#define SFS_PREPARE_FOR_UPDATE_FAIL     0x14
#define SFS_INVALID_PAYLOAD             0x1F

#pragma pack (pop)


UINT8 TestKeyData[RSA2048_FILE_SIZE] = {
  // Offset 0x00000000 to 0x0000023F
  0x01, 0x00, 0x00, 0x00, 0xF7, 0x8F, 0xAA, 0xA0, 0x26, 0xD3, 0x2B, 0xC1,
  0x62, 0x81, 0xEC, 0xD7, 0x69, 0xFA, 0xDE, 0x78, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00,
  0x00, 0x08, 0x00, 0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x25, 0x8C, 0xE5, 0x56,
  0x3F, 0x56, 0x56, 0x6E, 0xA1, 0x56, 0x0B, 0xB6, 0x6E, 0x19, 0x07, 0x77,
  0xBE, 0xC6, 0x12, 0xE6, 0xB0, 0x7A, 0xAE, 0xC4, 0x5B, 0xAB, 0xE2, 0x56,
  0x28, 0x4F, 0xD7, 0x7F, 0x49, 0x1F, 0xE3, 0x92, 0xED, 0xD8, 0x79, 0x1F,
  0x9F, 0xD3, 0x27, 0xA3, 0x63, 0x82, 0xB3, 0x6A, 0x9B, 0xB6, 0xDF, 0x83,
  0x65, 0xC0, 0x1E, 0x45, 0x1D, 0xF5, 0x0F, 0xDC, 0x55, 0xE0, 0x2E, 0x3A,
  0x4C, 0x28, 0x46, 0x9D, 0xB3, 0x4D, 0x9D, 0xCD, 0x0E, 0xF8, 0x8E, 0xE6,
  0x76, 0x27, 0x51, 0xB7, 0xE6, 0xBD, 0x72, 0x9F, 0x3D, 0x37, 0xC4, 0x3D,
  0xEA, 0xC7, 0x76, 0x20, 0xB9, 0x2F, 0xD5, 0xB5, 0xCA, 0x3A, 0x9A, 0x6D,
  0x21, 0x2B, 0x44, 0xE2, 0xE3, 0xE2, 0x8E, 0x0A, 0x80, 0xF3, 0x24, 0x3F,
  0x38, 0x9D, 0x0D, 0xFD, 0xC5, 0x73, 0x62, 0x80, 0x5B, 0x86, 0xD4, 0x82,
  0x35, 0x9C, 0x26, 0x3C, 0xBF, 0x5F, 0x2F, 0xCA, 0x4D, 0x99, 0xF0, 0xBC,
  0xE8, 0xCE, 0x7E, 0xD2, 0xC7, 0x36, 0xD9, 0x93, 0xBA, 0x45, 0xDB, 0xB0,
  0xF0, 0xED, 0xC5, 0xFE, 0x4E, 0x48, 0x73, 0x81, 0x5E, 0x53, 0x77, 0xF9,
  0xB2, 0xFE, 0xDD, 0x6F, 0x5D, 0x50, 0x0B, 0xED, 0xA4, 0x38, 0x4F, 0x89,
  0xC0, 0x48, 0x6F, 0x92, 0xB2, 0x9E, 0xD1, 0x95, 0xB7, 0xA1, 0x6B, 0xA3,
  0x15, 0xCB, 0xA2, 0x17, 0xF5, 0x16, 0xF3, 0xA8, 0x3E, 0xD3, 0x8F, 0x02,
  0x9F, 0x0C, 0x50, 0xE0, 0xD1, 0x69, 0xAC, 0xAE, 0x7A, 0x87, 0x91, 0xE0,
  0x3C, 0x98, 0x4F, 0x9D, 0x25, 0x08, 0xDB, 0xAE, 0xBD, 0x90, 0xE7, 0x76,
  0xF2, 0xA7, 0xB2, 0xE3, 0xAA, 0xCF, 0x38, 0xAE, 0x8F, 0x6D, 0x80, 0xDF,
  0x9B, 0x39, 0x3E, 0x87, 0x02, 0x5B, 0x83, 0x6F, 0x01, 0x79, 0x32, 0xC8,
  0x1E, 0x0B, 0xA1, 0x3B, 0xF1, 0xBB, 0xA6, 0x78, 0xC9, 0x29, 0x72, 0xDD
};

/**
 * @brief Return the TeeMbox MMIO location
 *
 * @retval EFI_STATUS  0: Success, NonZero Error
 */
BOOLEAN
GetTeeMboxLocation (
  IN OUT   TEE_CTRL **TeeMbox
  )
{
  UINT32  PspMmioBase;
  UINT32  PspC2pMsgRegBaseOffset;
  UINT32  Data32;

  if (GetPspMmioBase (&PspMmioBase) == FALSE) {
    return FALSE;
  }

  PspC2pMsgRegBaseOffset = GetPspC2pMsgRegBaseOffset ();

  Data32 = *(UINT32 *) (UINTN) (PspMmioBase + PspC2pMsgRegBaseOffset + (63 * 4));
  if ((Data32 & BIT3) == 0) {   // SFS_ENABLED
    return FALSE;
  }

  *TeeMbox = (TEE_CTRL *) (UINTN) (PspMmioBase + PspC2pMsgRegBaseOffset + (17 * 4));

  return TRUE;
}

/**
 * @brief BIOS sends TEE IF command to PSP
 *
 * @param[in]  MboxBuffer   TEE mailbox buffer
 * @param[in]  Cmd          TEE IF Command
 *
 * @retval BOOLEAN          1: Success, 0 Error
 */
EFI_STATUS
SendTeeIfCommand (
  IN VOID           *MboxBuffer,
  IN TEE_IF_CMD_ID  Cmd
  )
{
  TEE_CTRL            *TeeMbox;

  if (GetTeeMboxLocation (&TeeMbox) == FALSE) {
    return EFI_UNSUPPORTED;
  }

  while ((TeeMbox->CmdResp & TEE_CMD_RESPONSE_MASK) == 0) {};

  TeeMbox->CmdBufAddrLo = (UINT32)(UINTN)MboxBuffer;
  TeeMbox->CmdBufAddrHi = (UINT32)((UINTN)MboxBuffer >> 32);
  TeeMbox->CmdResp = Cmd;

  while ((TeeMbox->CmdResp & TEE_CMD_RESPONSE_MASK) == 0) {};

  if ((TeeMbox->CmdResp & TEE_CMD_STATUS_MASK) != SFS_SUCCESS) {
    return EFI_DEVICE_ERROR;
  } else {
    return EFI_SUCCESS;
  }
}

EFI_STATUS
SfsGetFwVersions (
  VOID
  )
{
  EFI_STATUS                Status;
  EFI_PHYSICAL_ADDRESS      UnalignedBuffer;
  TEE_EXT_CMD               *TeeExtCmd;
  SFS_VERSION_INFO          *SfsVersionInfo;
  UINT32                    i;

  Status = gBS->AllocatePages (
                  AllocateAnyPages,
                  EfiBootServicesData,
                  EFI_SIZE_TO_PAGES (SIZE_8KB + SIZE_2MB),
                  &UnalignedBuffer
                );
  if (!EFI_ERROR(Status)) {
    TeeExtCmd = (TEE_EXT_CMD *)(((UINTN)UnalignedBuffer + SIZE_2MB - 1) & ~(SIZE_2MB - 1));
    TeeExtCmd->Header.TotalSize = SIZE_8KB;
    TeeExtCmd->Header.SubCmdId = TEE_SUB_CMD_SFS_GET_FW_VERSIONS;
    Status = SendTeeIfCommand ((VOID *)TeeExtCmd, TEE_IF_CMD_ID_CMD_EXTENDED);
    if (!EFI_ERROR(Status)) {
      SfsVersionInfo = (SFS_VERSION_INFO *)((UINTN)TeeExtCmd + SIZE_4KB);
      IDS_HDT_CONSOLE_PSP_TRACE ("SfSApiVersion   = 0x%08x\n", SfsVersionInfo->SfSApiVersion);
      IDS_HDT_CONSOLE_PSP_TRACE ("CurrentPatchLvl = 0x%08x\n", SfsVersionInfo->CurrentPatchLvl);
      IDS_HDT_CONSOLE_PSP_TRACE ("SysPatchLvl     = 0x%08x\n", SfsVersionInfo->SysPatchLvl);
      IDS_HDT_CONSOLE_PSP_TRACE ("NumSupportedFws = 0x%08x\n", SfsVersionInfo->NumSupportedFws);
      for (i = 0; i < SfsVersionInfo->NumSupportedFws; i++) {
        IDS_HDT_CONSOLE_PSP_TRACE ("\tFwVersionInfo[%d].VersionType = 0x%08x\n", i, SfsVersionInfo->FwVersionInfo[i].VersionType);
        IDS_HDT_CONSOLE_PSP_TRACE ("\tFwVersionInfo[%d].Version     = 0x%08x\n", i, SfsVersionInfo->FwVersionInfo[i].Version);
        IDS_HDT_CONSOLE_PSP_TRACE ("\tFwVersionInfo[%d].SecPatchLvl = 0x%08x\n", i, SfsVersionInfo->FwVersionInfo[i].SecPatchLvl);
      }
      IDS_HDT_CONSOLE_PSP_TRACE ("CoSignTokenHash = ");
      for (i = 0; i < sizeof(SfsVersionInfo->CoSignTokenHash); i++) {
        if ((i % 16) == 0) {
          IDS_HDT_CONSOLE_PSP_TRACE ("\n");
        }
        IDS_HDT_CONSOLE_PSP_TRACE ("%02x ", SfsVersionInfo->CoSignTokenHash[i]);
      }
      IDS_HDT_CONSOLE_PSP_TRACE ("\n");
    }
    Status = gBS->FreePages (UnalignedBuffer, EFI_SIZE_TO_PAGES (SIZE_8KB + SIZE_2MB));
  }
  return Status;
}

/**
 *  Handle the co-signing key if available
 *  and co-signing feature is enabled in BIOS setup
 *
 *  @param VOID
 *
 *  @retval       EFI_SUCCESS       Function succeed
 *  @retval       NON-ZERO          Error occurs
*/

EFI_STATUS
AmdPspProcessCoSignKey (
  VOID
  )
{
  EFI_STATUS                    Status;
  AMD_APCB_SERVICE_PROTOCOL     *ApcbDxeServiceProtocol;
  UINT8                         ApcbPurpose;
  BOOLEAN                       ApcbPspSfsEnableValue;
  BOOLEAN                       ApcbPspSfsCoSignRequiredValue;
  OEM_CO_SIGN_KEY               Key;
  UINT32                        PublicExponentSize;
  UINT32                        ModulusSize;
  TYPE_ATTRIB                   TypeAttrib;
  UINT64                        TestPtr;
  UINT32                        TestEntrySize;
  UINT64                        TestEntryDest;
  UINT8                         NewTestKeyData[RSA4096_FILE_SIZE];
  UINT32                        i;

  IDS_HDT_CONSOLE_PSP_TRACE ("AmdPspProcessCoSignKey - Entry\n");

  ApcbDxeServiceProtocol  = NULL;
  ApcbPurpose             = 0u;
  ZeroMem (&Key, sizeof (Key));
  ZeroMem (&NewTestKeyData, sizeof (NewTestKeyData));

  Status = gBS->LocateProtocol (&gAmdApcbDxeServiceProtocolGuid, NULL, (VOID **)&ApcbDxeServiceProtocol);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("Unable to locate APCB Protocol\n");
    return Status;
  }

  Status = ApcbDxeServiceProtocol->ApcbGetTokenBool (ApcbDxeServiceProtocol, &ApcbPurpose, APCB_TOKEN_UID_PSP_SFS_ENABLE, &ApcbPspSfsEnableValue);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("Unable to get APCB token APCB_TOKEN_UID_PSP_SFS_ENABLE, Status:%r\n", Status);
    return Status;
  }

  Status = ApcbDxeServiceProtocol->ApcbGetTokenBool (ApcbDxeServiceProtocol, &ApcbPurpose, APCB_TOKEN_UID_PSP_SFS_COSIGN_REQUIRED, &ApcbPspSfsCoSignRequiredValue);
  if (EFI_ERROR (Status)) {
    IDS_HDT_CONSOLE_PSP_TRACE ("Unable to get APCB token APCB_TOKEN_UID_PSP_SFS_COSIGN_REQUIRED, Status:%r\n", Status);
    return Status;
  }

  if (ApcbPspSfsCoSignRequiredValue && ApcbPspSfsEnableValue) {
    if (BIOSEntryInfo (PSP_SFS_COSIGNATURE, INSTANCE_IGNORED, &TypeAttrib, &TestPtr, &TestEntrySize, &TestEntryDest) == FALSE){
      IDS_HDT_CONSOLE(MAIN_FLOW, "Fail to get PSP_SFS_COSIGNATURE Entry in BIOS DIR\n");
      IDS_HDT_CONSOLE(MAIN_FLOW, "Use the original test key data instead\n");
      CopyMem (Key.OemCoSignKey, TestKeyData, sizeof (TestKeyData));

      PublicExponentSize = ((KEY_HEADER*)&TestKeyData[0])->PublicExponentSize;
      ModulusSize        = ((KEY_HEADER*)&TestKeyData[0])->ModulusSize;
    } else {
      // make sure the size for copy does not exceed the size of Key.OemCoSignKey
      if (TestEntrySize > sizeof (NewTestKeyData)) {
        TestEntrySize = sizeof (NewTestKeyData);
      }

      MapSpiDataToBuffer ((UINT32)TestPtr, &NewTestKeyData, TestEntrySize);
      CopyMem (Key.OemCoSignKey, NewTestKeyData, sizeof (NewTestKeyData));

      PublicExponentSize = ((KEY_HEADER*)&NewTestKeyData[0])->PublicExponentSize;
      ModulusSize        = ((KEY_HEADER*)&NewTestKeyData[0])->ModulusSize;
      IDS_HDT_CONSOLE(MAIN_FLOW, "PSP_SFS_COSIGNATURE = \n");
      for (i = 0; i < (RSA4096_FILE_SIZE); i++){
        if (i == 0){
          IDS_HDT_CONSOLE(MAIN_FLOW,"%x", NewTestKeyData[0]);
        } else {
          IDS_HDT_CONSOLE(MAIN_FLOW,", %x", NewTestKeyData[i]);
        }
      }
      IDS_HDT_CONSOLE(MAIN_FLOW, "\n");
    }

    IDS_HDT_CONSOLE_PSP_TRACE ("Read Input Customer Co-Sign Key , PublicExponentSize=0x%x, ModulusSize=0x%x\n", PublicExponentSize, ModulusSize);

    if (PublicExponentSize != ModulusSize) {
      IDS_HDT_CONSOLE_PSP_TRACE ("Invalid Input Customer Co-Sign Key, Key Sizes Mismatch, PublicExponentSize=0x%x, ModulusSize=0x%x\n", PublicExponentSize, ModulusSize);
      Status = EFI_UNSUPPORTED;
      return Status;
    }

    if ((PublicExponentSize != (RSA4096_SIZE * 8u)) && (PublicExponentSize != (RSA2048_SIZE * 8u))) {
      IDS_HDT_CONSOLE_PSP_TRACE ("Invalid Input Customer Co-Sign Key, Key Sizes Not 2K OR 4K, PublicExponentSize=0x%x, ModulusSize=0x%x\n", PublicExponentSize, ModulusSize);
      Status = EFI_UNSUPPORTED;
      return Status;
    }

    Key.KeySize = PublicExponentSize;
    Status = PspMboxBiosCmdSendCoSignPublicKey(&Key);
    IDS_HDT_CONSOLE_PSP_TRACE ("Sent Co-Sign Key, KeySize=0x%x, Status:%r\n", Key.KeySize, Status);

    Status = SfsGetFwVersions ();

  } else {
    IDS_HDT_CONSOLE_PSP_TRACE ("No Co-Sign Key sent, ApcbPspSfsCoSignRequiredValue: %d, ApcbPspSfsEnableValue: %d, Status:%r\n",
                                ApcbPspSfsCoSignRequiredValue, ApcbPspSfsEnableValue, Status);
    Status = EFI_UNSUPPORTED;
  }

  IDS_HDT_CONSOLE_PSP_TRACE ("AmdPspProcessCoSignKey - Exit, Status:%r\n", Status);
  return Status;
}

