#;*****************************************************************************
#;
#; Copyright (C) 2015-2024 Advanced Micro Devices, Inc. All rights reserved.
#;
#;******************************************************************************

[Defines]
 DEFINE AGESA_PKG_PATH   = AgesaModulePkg
 DEFINE AGESA_PKG_DEC    = AgesaCommonModulePkg

[LibraryClasses]
  #
  # Cpm specific common libraries
  #
  AmdCpmBaseLib|AmdCpmPkg/Library/Proc/Base/AmdCpmBaseLib.inf
  AmdCpmCpuLib|AmdCpmPkg/Library/Proc/Cpu/AmdCpmCpu.inf
  AmdCpmFchLib|AmdCpmPkg/Library/Proc/Fch/AmdCpmFch.inf
  AmdVariableProtectionLib|AmdCpmPkg/Features/AmdVariableProtection/AmdVariableProtectionLib.inf


  AmdFwConfigRuntimeLib|AmdCpmPkg/Library/Proc/AmdFwConfigRuntimeLib/AmdFwConfigRuntimeLib.inf
  AmdFwConfigCbsBrhLib|AmdCpmPkg/Library/Proc/AmdFwConfigCbsLib/AmdFwConfigCbsBrhLib.inf
  AmdFwConfigApcbBrhLibV3|AmdCpmPkg/Library/Proc/AmdFwConfigApcbLibV3/AmdFwConfigApcbBrhLibV3.inf

  ## Cxl - See comments under [Components.X64]
  AmdCxlPcieLib|AmdCpmPkg/Library/AmdCxlPcieLib/AmdCxlPcieLib.inf
  OemGpioResetControlLib|AmdCpmPkg/Addendum/Oem/Chalupa/Library/OemGpioResetControlLib/OemGpioResetControlLib.inf
  MpdmaIvrsLib|AmdCpmPkg/Features/MPDMA/MpdmaIvrsLib/MpdmaIvrsLib.inf
  AmdCpmCxlMboxLib|AmdCpmPkg/Library/AmdCpmCxlMboxLib/AmdCpmCxlMboxLib.inf

  #
  # RAS
  #
  CpmRasCxlLib|AmdCpmPkg/Library/Proc/Ras/Brh/CpmRasCxlBrhLib/CpmRasCxlBrhLib.inf
  CpmRasLib|AmdCpmPkg/Library/Proc/Ras/Brh/CpmRasBrhLib.inf
  CpmRasPciLib|AmdCpmPkg/Library/Proc/Ras/Brh/CpmRasPciBrhLib.inf
  CpmRasAcpiLib|AmdCpmPkg/Library/Proc/Ras/Brh/CpmRasAcpi63BrhLib.inf
  CpmRasMemLib|AmdCpmPkg/Library/Proc/Ras/Brh/CpmRasMemBrhLib/CpmRasMemBrhLib.inf

[LibraryClasses.common.DXE_SMM_DRIVER]
  CpmRasProcLib|AmdCpmPkg/Library/Proc/Ras/Brh/CpmRasProcBrhLib/CpmRasProcSmmBrhLib.inf

[LibraryClasses.common.DXE_DRIVER]
  AmdPbsConfigLib|AmdCpmPkg/Addendum/Oem/Chalupa/Library/AmdPbsConfigLib/AmdPbsConfigDxeLib.inf
  CpmRasProcLib|AmdCpmPkg/Library/Proc/Ras/Brh/CpmRasProcBrhLib/CpmRasProcDxeBrhLib.inf
  CxlRangeEncryptionLib|AmdCpmPkg/Addendum/Oem/Galena/Library/CxlRangeEncryptionLib/CxlRangeEncryptionLib.inf

[LibraryClasses.common.PEIM]
  AmdPbsConfigLib|AmdCpmPkg/Addendum/Oem/Chalupa/Library/AmdPbsConfigLib/AmdPbsConfigPeiLib.inf


[Components.IA32]
  AmdCpmPkg/Addendum/Oem/Chalupa/Pei/AmdCpmOemInitPei/AmdCpmOemInitPeimChalupa.inf
#  AmdCpmPkg/Addendum/Oem/Chalupa/Pei/AmdSataWorkaround/AmdSataWorkaround.inf
  AmdCpmPkg/Addendum/Oem/Chalupa/Pei/PlatformCustomizePei/PlatformCustomizePei.inf
  AmdCpmPkg/Devices/Ds125Br401a/Pei/Ds125Br401aPei.inf
  AmdCpmPkg/Devices/M24LC128/Pei/M24Lc128Pei.inf
  AmdCpmPkg/Devices/Pca9535a/Pei/Pca9535aPei.inf
  AmdCpmPkg/Devices/Pca9545a/Pei/Pca9545aPei.inf
  AmdCpmPkg/Devices/Sff8472/Pei/Sff8472Pei.inf
  AmdCpmPkg/Devices/Tca9548a/Pei/Tca9548aPei.inf
  AmdCpmPkg/Devices/M24LC256/Pei/M24Lc256Pei.inf
  AmdCpmPkg/Devices/Pca9536/Pei/Pca9536Pei.inf
  AmdCpmPkg/Devices/Pca6107/Pei/Pca6107Pei.inf
  AmdCpmPkg/Features/BoardId/Pei/AmdBoardIdPei.inf
  AmdCpmPkg/Features/GpioInit/Pei/AmdCpmGpioInitPeim.inf
  AmdCpmPkg/Features/LpcUart/Pei/AmdCpmLpcUartPeim.inf
  AmdCpmPkg/Features/PcieInit/Pei/AmdCpmPcieInitPeim.inf

  AmdCpmPkg/Features/PlatformRas/Brh/Pei/AmdPlatformRasBrhPei.inf

  AmdCpmPkg/Kernel/Pei/AmdCpmInitPeim.inf

!if $(PLATFROM_INTERNAL_BUILD) == TRUE
  AmdCpmPkg/Features/FabricTopologyDump/FabricTopologyDump.inf
!endif

[Components.X64]
#  AmdCpmPkg/Addendum/Oem/Chalupa/Dxe/AmdXgbeWorkaround/AmdXgbeWorkaround.inf
  AmdCpmPkg/Addendum/Oem/Chalupa/Dxe/ServerHotplugDxe/ServerHotplugDxe.inf
#  AmdCpmPkg/Addendum/Oem/Chalupa/Dxe/PspPlatformDriver/PspPlatform.inf
  AmdCpmPkg/Addendum/Oem/Chalupa/Dxe/RasOemDimmMap/RasOemDimmMap.inf
  AmdCpmPkg/Kernel/Asl/AmdCpmInitAsl.inf
  AmdCpmPkg/Kernel/Dxe/AmdCpmInitDxe.inf
  AmdCpmPkg/Kernel/Smm/AmdCpmInitSmm.inf
  AmdCpmPkg/Features/BoardId/Dxe/AmdBoardIdDxe.inf
  AmdCpmPkg/Features/PcieInit/Asl/AmdCpmPcieInitAsl.inf
  AmdCpmPkg/Features/PcieInit/Dxe/AmdCpmPcieInitDxe.inf
  AmdCpmPkg/Features/GpioInit/Dxe/AmdCpmGpioInitDxe.inf
  AmdCpmPkg/Features/GpioInit/Smm/AmdCpmGpioInitSmm.inf

  AmdCpmPkg/Features/PlatformRas/Brh/Dxe/AmdPlatformRasBrhDxe.inf
  AmdCpmPkg/Features/PlatformRas/Brh/Smm/AmdPlatformRasBrhSmm.inf
  AmdCpmPkg/Features/PlatformRas/Brh/Dxe/AmdOemRasBrhDxe.inf
  AmdCpmPkg/Features/PlatformRas/Brh/Smm/AmdOemRasBrhSmm.inf
  AmdCpmPkg/Features/PlatformRas/Brh/Asl/PlatformRasBrhAsl.inf

  AmdCpmPkg/Features/AmdCdmaDataInit/Brh/Dxe/AmdCdmaDsmDxe.inf
  AmdCpmPkg/Features/AmdCdmaDataInit/Asl/AmdCdmaAsl.inf
  AmdCpmPkg/Features/MPDMA/BRH/Dxe/MPDMABrhDxe.inf {
    <LibraryClasses>
    FabricResourceManagerLib|AgesaModulePkg/Library/FabricResourceManagerBrhLib/FabricResourceManager3Lib.inf
  }
  AmdCpmPkg/Features/MPDMA/BRH/Asl/MpDmaBrhAsl.inf
  AmdCpmPkg/Features/HSMP/BRH/Dxe/HsmpBrhDxe.inf
  AmdCpmPkg/Features/HSMP/BRH/Asl/HsmpBrhAsl.inf
  AmdCpmPkg/Features/xGbEI2cMaster/xGbEI2cMasterDxe.inf
  AmdCpmPkg/Devices/Pca9535a/Dxe/Pca9535aDxe.inf
  AmdCpmPkg/Devices/Pca9545a/Dxe/Pca9545aDxe.inf
  AmdCpmPkg/Devices/Sff8472/Dxe/Sff8472Dxe.inf
  AmdCpmPkg/Devices/Tca9548a/Dxe/Tca9548aDxe.inf
  AmdCpmPkg/Devices/Pca9536/Dxe/Pca9536Dxe.inf
  AmdCpmPkg/Devices/Pca6107/Dxe/Pca6107Dxe.inf
  AmdCpmPkg/Features/CF9IoTrap/BRH/CF9IoTrapBrh.inf
  AmdCpmPkg/Features/AmdFwConfig/Asl/AfcSsdt.inf
  AmdCpmPkg/Features/AmdFwConfig/Dxe/AmdFwConfigDxe.inf
  AmdCpmPkg/Features/AmdFwConfig/Smm/AmdFwConfigSmmBrh.inf
  AmdCpmPkg/Features/AcpiI3cSlaveSsdt/AcpiI3cSlaveSsdt.inf
  AmdCpmPkg/Addendum/Oem/Chalupa/Dxe/AcpiI3cSlave/ASL/AcpiI3cSlaveAsl.inf

  AmdCpmPkg/Features/SysTopologyReport/Dxe/SysTopologyReportDxe.inf

  #
  # BCT, BIOS Config Tool
  #
  AmdCpmPkg/Features/AmdBctPkg/BiosCfgToolDxe.inf
  AmdCpmPkg/Features/AmdBctPkg/BiosCfgToolSmm.inf

  ## Cxl - Per Cxl spec: Support memory expansion on Xilinx VCU128 (FPGA) endpoint
  AmdCpmPkg/Devices/GenericCxl/CxlEndpointDriver.inf

  AmdCpmPkg/Addendum/Oem/Chalupa/Dxe/AmdPbsSetupDxe/AmdPbsSetupDxe.inf
  AmdCpmPkg/Features/ApicInfoData/BRH/Dxe/ApicInfoDataDxe.inf
  #AmdCpmPkg/Features/HotPlug/Ssp/Smm/AmdHotPlugSspSmm.inf
  AmdCpmPkg/Features/HotPlug/Brh/Smm/AmdHotPlugBrhSmm.inf
  #AmdCpmPkg/Features/PlatformJedecNvdimm/Ssp/Smm/AmdPlatformJedecNvdimmSmm.inf
!ifndef $(AMD_CSM_SUPPORT_DISABLED)
  AmdCpmPkg/Features/LegacyInterrupt/Dxe/LegacyInterruptDxe.inf
!endif

AmdCpmPkg/Addendum/Oem/Chalupa/Dxe/DeferredPsbFuse/DeferredPsbFuseDxe.inf

AmdCpmPkg/Addendum/Oem/OobPprDxe/OobPprDxe.inf

#For PRM feature Support
  #
  # PRM Libraries
  #
  AmdCpmPkg/Features/AmdPrm/PrmModule/PrmAddressTranslateModule/Library/Brh/DxeAddressTranslateModuleConfigLib.inf

  #
  # PRM Configuration Driver
  #
  AmdCpmPkg/Features/AmdPrm/PrmConfigDxe/PrmConfigDxe.inf {
    <LibraryClasses>
      DxeAddressTranslateModuleConfigLib|AmdCpmPkg/Features/AmdPrm/PrmModule/PrmAddressTranslateModule/Library/Brh/DxeAddressTranslateModuleConfigLib.inf
  }

  #
  # PRM Modules X64
  #
  AmdCpmPkg/Features/AmdPrm/PrmModule/PrmAddressTranslateModule/Brh/PrmAddressTranslateModule.inf

  AmdCpmPkg/Features/AmdVariableProtection/AmdVariableProtection.inf

  !if $(PLATFROM_INTERNAL_BUILD) == TRUE
    AmdCpmPkg/Features/Xgmi/XgmiFreqDxe.inf
  !endif

[PcdsFixedAtBuild]
  gAmdCpmPkgTokenSpaceGuid.PcdAmdCpmSocBuildType|0x04
  gEfiMdePkgTokenSpaceGuid.PcdSpinLockTimeout|20000000                                                  #Increase the Spinlock timeout from 10000000 us (Default for MdePkg) to 20000000 us
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchCfgAcpiPm1EvtBlkAddr|0x800
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchCfgAcpiPm1CntBlkAddr|0x804
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchCfgAcpiPmTmrBlkAddr|0x808
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchCfgCpuControlBlkAddr|0x810
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchCfgAcpiGpe0BlkAddr|0x820
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgFchIoapicId|0x80
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdCfgGnbIoapicId|0xF0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemAfterPciRestoreSwSmi|0   #S3 unsupported
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemBeforePciRestoreSwSmi|0  #S3 unsupported
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemSpiLockSwSmi|0xB8
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdFchOemSpiUnlockSwSmi|0xB7
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosT16MaximumCapacity|0x80000000
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosSocketDesignationSocket0|"P0"
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdSmbiosSocketDesignationSocket1|"P1"
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdAcpiCpuSsdtProcessorScopeInSb|TRUE
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdMemCfgMaxPostPackageRepairEntries|0x3F
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPeiTempPageTableBaseAddress|0x60000000
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdMemMaxDimmPerChannelV2|1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNumberOfPhysicalSocket|2

[PcdsDynamicDefault]
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdLegacyFree|TRUE
  #gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdNoneSioKbcSupport|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCStateIoBaseAddress|0x0813

  # Platform RAS
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataRasSupport|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchApuRasSmiSupport|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioPoisonConsumption|FALSE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioRASControl|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCcxCfgPFEHEnable|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPcieAerReportMechanism|2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSyncFloodToApml|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdNbioRASControlV2|0x01
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEgressPoisonSeverityLo|0x00000004
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEgressPoisonSeverityHi|0x00030011
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdXhciECCDedErrRptEn|FALSE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchAlinkRasSupport|FALSE

  # Cxl (Cxl) - See comments under [Components.X64]
  #
  # PcdCxlEnable: Controls Cxl feature in the SoC.
  #
  # PcdInterleavePortsOnCxlController:
  # 1) Requires equal memory capacity per port (Endpoint)
  # 2) Requires power-of-two connected ports (2 or 4)
  #
  # PcdInterleavePairOfCxlControllers:
  # 1) Implies PcdInterleavePortsOnCxlController is TRUE
  # 2) Requires same number of connected ports per controller (1, 2 or 4)
  # 3) Requires separate pair of Coherent Slaves assigned to each controller
  # 4) Valid config NBIO configs: NBIO0+NBIO1, NBIO0+NBIO3, NBIO2+NBIO1, NBIO2+NBIO3
  #

  #gAmdCpmPkgTokenSpaceGuid.PcdInterleavePortsOnCxlController|TRUE
  #gAmdCpmPkgTokenSpaceGuid.PcdInterleavePairOfCxlControllers|TRUE
  #gAmdCpmPkgTokenSpaceGuid.PcdCxlMultipleMemoryPoolsPerSlaveAgent|FALSE

  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDdrEccErrorCounterEnable|0x02                                     # 0 - Disable, 1 - NoLeakMode, 2 - LeakMode.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDdrEccErrorCounterIntEnable|TRUE                                  # When TRUE, Specifies the SMI interrupt generated when any EccErrCnt field transitions to FFFFh.
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDdrEccErrorCounterLeakRate|0x07                                   # Error Counter Leak Rate (0x00..0x1F).
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdDdrEccErrorCounterStartCount|0x00                                 # Error Counter Start Count (0x0000..0xFFFF).

  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPmicErrorReporting|TRUE                                           # set PcdAmdPmicErrorReporting to TRUE to report PMIC errors.

  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCXlInformationalEventLogInterrupt|FALSE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCXlWarningEventLogInterrupt|TRUE

  gAmdCpmPkgTokenSpaceGuid.PcdCpmPcieRpCorrectedErrorMask|0x2000
  gAmdCpmPkgTokenSpaceGuid.PcdCpmPcieDevCorrectedErrorMask|0x2000

  gEfiAmdAgesaModulePkgTokenSpaceGuid.FchRTDeviceEnableMap|0x1F60
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdFchCfgSmiCmdPortAddr|0xB2
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlProtocolErrorReporting|1
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdCxlComponentErrorReporting|1
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdAmdS3LibTableSize|0x100000
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdAmdPspAntiRollbackLateSplFuse|TRUE
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdResetMode|0x07
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTelemetry_VddcrSocfull_Scale_Current|0x50
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdTelemetry_VddcrVddfull_Scale_Current|0xFF
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdLpcEnable|FALSE
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataClkAutoOff|0x01
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXhciOcPolarityCfgLow|TRUE
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXhciUsb20OcPinSelect|0xFFFFFFFFFFFF1010
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdXhciUsb31OcPinSelect|0xFFFF1010
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEarlyBmcLinkTraining|TRUE
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEarlyBmcLinkSocket|0
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdEarlyBmcLinkDie|0
  gEfiAmdAgesaModulePkgTokenSpaceGuid.PcdSataMultiDiePortRxPolarity|0xFFFFFFFFFFFFFFFF
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdSataMultiDiePortESP|0x0000FFFF00000000

  # Platform limits
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPlatformTDP|0x1F4                         #500W
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPlatformPPT|0x1F4                         #500W
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPlatformTDC|0xEB                          #235A
  gEfiAmdAgesaPkgTokenSpaceGuid.PcdCfgPlatformEDC|0x14A                         #300A
