#pragma message( "Compal Server Override Compiling-" __FILE__ )
//***********************************************************************
//*                                                                     *
//*   Copyright (c) 1985-2024, American Megatrends International LLC.   *
//*                                                                     *
//*      All rights reserved. Subject to AMI licensing agreement.       *
//*                                                                     *
//***********************************************************************

/**
    @file: SystemInventoryInfoStorage.c
    
    Contains Routines to collect Data of Storage Devices and populate 
    the structures in System Inventory Info Protocol.

*/

#include "SystemInventoryInfo.h"
#include <SystemInventoryInfoElinks.h>
#include <Token.h>

UINT8                                       gStorageUnitIndex = 0;
UINTN                                       gStorageVolumeIndex = 0;
UINTN                                       gStorageDeviceIndex = 0;
UINT8                                       gStorageControllerIndex = 0;
UINT8                                       gEndArrayTag = 0xFF; // To represent the end of the Array
UINTN                                       gEndArrayTagUINTN = (UINTN)0xFFFFFFFF; // To represent the end of the Array
EFI_NVM_EXPRESS_PASS_THRU_COMMAND_PACKET    *gNvmeCommandPacket;
EFI_NVM_EXPRESS_COMPLETION                  *gNvmeCompletion;
EFI_NVM_EXPRESS_COMMAND                     *gNvmeCmd;
UINT32                                      gNvmeControllerVersion;

EFI_HANDLE                                  gUsbCtrlHandle = NULL;

CHAR8                                       *StorageStringBuffer ;
UINTN                                       MaxStorageStrBuffSize = SIZE_8KB;

SYS_INV_STORAGE_DEVICE_ASSET_TAG SysInvOemStorageDeviceAssetTagList[] = { SYS_INV_STORAGE_DEVICE_ASSET_TAG_LIST {NULL, NULL}};
UINTN   SysInvOemStorageDevCount = sizeof(SysInvOemStorageDeviceAssetTagList) / sizeof(SYS_INV_STORAGE_DEVICE_ASSET_TAG);

//[COMPAL_CHANGES]+>>
extern VOID CompalUpdateNvmeStorageControllerInventoryData ( IN OUT SYSTEM_INVENTORY_INFO_PROTOCOL *SystemInventoryInfoProtocol, IN DEV_ENTRY *DeviceEntry, IN EFI_PCI_IO_PROTOCOL *PciIo);
//[COMPAL_CHANGES]+<<

/**
  @internal
  
  Checks for a matching UEFI Device path of Storage Device in Storage Asset Tags List and if found, update 
  respective Asset Tag in Storage Device Entry. 

  @param [in]       StorageDeviceEntry         - Pointer to DEV_ENTRY structure of SATA storage device.
  @param [in]       DevPathStr                 - Pointer to UEFI Device Path String.

  @retval VOID
  
  @endinternal
**/
VOID
FillStorageAssetTag(
  IN     DEV_ENTRY       *StorageDeviceEntry,
  IN     CHAR16          *DevPathStr,
//  IN OUT CHAR8           *StorageStringBuffer,
  IN OUT UINT32          *TotalStringLength,
  IN OUT UINT16          *StringIndex
)
{
    UINTN                   DevIndex;
    DEBUG ((DEBUG_INFO, "DevPathStr = %s \n", DevPathStr));
    for(DevIndex = 0 ; DevIndex < (SysInvOemStorageDevCount-1) ;DevIndex++ ){
        if(StrCmp(DevPathStr, SysInvOemStorageDeviceAssetTagList[DevIndex].DevPath) == 0){
            DEBUG ((DEBUG_INFO, "DevPath = %s; ", SysInvOemStorageDeviceAssetTagList[DevIndex].DevPath));
            DEBUG ((DEBUG_INFO, "StorageAssetTag = %a\n", SysInvOemStorageDeviceAssetTagList[DevIndex].StorageAssetTag));
            if (AsciiStrLen(SysInvOemStorageDeviceAssetTagList[DevIndex].StorageAssetTag)){
                StrBuffAvailabilityCheck (
                        &StorageStringBuffer,
                        &MaxStorageStrBuffSize,
                        AsciiStrLen(SysInvOemStorageDeviceAssetTagList[DevIndex].StorageAssetTag),
                        *TotalStringLength );
                if (StorageStringBuffer != NULL) {
                    AsciiStrCpyS(
                            &StorageStringBuffer[*TotalStringLength],
                            PcdGet32 (PcdAmiSysInvMaxStringLength),
                            SysInvOemStorageDeviceAssetTagList[DevIndex].StorageAssetTag);
                    *TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[*TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                    StorageDeviceEntry->DisplayPtr.StorageDevice.AssetTagStrIndex = (*StringIndex)++;
                    StorageDeviceEntry->DisplayPtr.StorageDevice.ValidFlags1.StorageDeViInfoVF1Param.AssetTagValid = 1;
                }
            }
            break;
        }
    }
 
    if(DevIndex >= (SysInvOemStorageDevCount-1)){
        DEBUG ((DEBUG_INFO, "No ASSET Tag Porting for Device DevPathStr = %s \n", DevPathStr));
    }
}

/**
  @internal
  
  Function converts Big endian word to Little Endian and Vice versa.
  
  @param [in] EndianWord
  
  @retval UINT16
  
  @endinternal
 */
UINT16
ToBigLittleEndianWord (
    IN UINT16 EndianWord
)
{
    return (((EndianWord >> 8) & 0xFF) + (EndianWord << 8));
}

/**
  @internal
  
  Function converts Big endian dword to Little Endian Dword and Vice versa.
  
  @param [in] EndianDword
  
  @retval UINT16
  
  @endinternal
 */
UINT32
ToBigLittleEndianDword (
    IN UINT32 EndianDword
)
{
    return (((EndianDword & 0xFF) << 24) + ((EndianDword & 0xFF00) << 8) + \
            ((EndianDword & 0xFF0000) >>8) + ((EndianDword & 0xFF000000) >>24));
}

/**
  @internal
  
  Gets the Storage Volume Information and Updates SystemInventoryInfoProtocol global data with Storage volume info

  @param [in]       StorageDeviceEntry         - Pointer to DEV_ENTRY structure of SATA storage device.
  @param [in]       Handle                     - Storage Volume Handle under the storage device.
  @param [in] [out] VolumeIndex                - Pointer to volume index.

  @retval Status                    - Returns EFI_SUCCESS if the structure is properly added
                                    - Otherwise returns any of EFI_ERROR Status.
                                    
  @endinternal
**/
EFI_STATUS
InitializeStorageVolumesData(
  IN  OUT SYSTEM_INVENTORY_INFO_PROTOCOL    *SystemInventoryInfoProtocol,
  IN      DEV_ENTRY                         *StorageDeviceEntry,
  IN      EFI_HANDLE                        DeviceHandle
  )
{
    EFI_STATUS                              Status;
    UINTN                                   Size;
    UINTN                                   OpenInfoCount;
    UINTN                                   InfoIndex;
    CHAR8                                   VolumeLabel[FixedPcdGet32 (PcdAmiSysInvMaxStringLength)];
    CHAR16                                  *DevPathStr = NULL;
    DEV_ENTRY                               StorageVolInfoEntry;
    EFI_HANDLE                              VolumeHandle;
    EFI_FILE_HANDLE                         RootFs;
    EFI_FILE_SYSTEM_INFO                    *VolumeInfo;
    STORAGE_VOLUME_INFO                     *StorageVolInfo;
    EFI_SIMPLE_FILE_SYSTEM_PROTOCOL         *SimpleFileSystemProtocol;
    EFI_OPEN_PROTOCOL_INFORMATION_ENTRY     *OpenInfo;
    EFI_DEVICE_PATH_PROTOCOL                *DevPath;
    UINT32                                  TotalStringLength = 0;
    UINT16                                  StringIndex = 1;
    CHAR8                                   TempStringBuffer[FixedPcdGet32 (PcdAmiSysInvMaxStringLength)];
    UINTN                                   StringLength = 0;
    EFI_BLOCK_IO_PROTOCOL                   *BlockIo;
    BOOLEAN                                 VirualUsb = FALSE;
    CHAR8                                   *StrDevStringBuffer = NULL;
    CHAR8                                   *StringStart;
    STORAGE_DEVICE_INFO                     *StorageDevice; 

    ZeroMem (TempStringBuffer,sizeof(TempStringBuffer));

    StorageDevice = &StorageDeviceEntry->DisplayPtr.StorageDevice;
    
    DEBUG ((DEBUG_INFO, "%a() Entry\n", __FUNCTION__));
          
    Status = gBS->OpenProtocolInformation(
                            DeviceHandle,
                            &gEfiDiskIoProtocolGuid,
                            &OpenInfo,
                            &OpenInfoCount );
    if(EFI_ERROR(Status))
        return Status;
        
    // Check if it is a storage device 
    Status = gBS->HandleProtocol (
                        DeviceHandle,
                        &gEfiBlockIoProtocolGuid,
                        (VOID **)&BlockIo );
    
    if(EFI_ERROR(Status)) // Not a storage device
        return Status;


    if (gUsbCtrlHandle && (!BlockIo->Media->ReadOnly)) { // Skipping Cdrom 
        if (StorageDevice->ValidFlags1.StorageDeViInfoVF1Param.ManufacturerValid ||
            StorageDevice->ValidFlags1.StorageDeViInfoVF1Param.ModelValid) {
            Status = SysInvGetStrings (
                        SystemInventoryInfoProtocol,
                        &StorageDevice->StringHdr,
                        &StrDevStringBuffer);
            if (!EFI_ERROR(Status)) {
                if (StorageDevice->ValidFlags1.StorageDeViInfoVF1Param.ManufacturerValid &&
                    (!EFI_ERROR(SysInvGetStringByIndex (
                        StrDevStringBuffer,
                        StorageDevice->ManufacturerStrIndex,
                        &StringStart )))){
                    if (AsciiStrCmp(StringStart,PcdGetPtr (PcdVirtualUsbManufacturer)) == 0)
                        VirualUsb = TRUE;
                }
                if (StorageDevice->ValidFlags1.StorageDeViInfoVF1Param.ModelValid &&
                    (!EFI_ERROR(SysInvGetStringByIndex (
                            StrDevStringBuffer,
                            StorageDevice->ModelStrIndex,
                            &StringStart )))) {
                    if (AsciiStrCmp(StringStart,PcdGetPtr (PcdVirtualUsbModel)) == 0)
                        VirualUsb = TRUE;
                    else 
                        VirualUsb = FALSE;
                }
                
                if (StrDevStringBuffer)
                    FreePool(StrDevStringBuffer); 
            }
        }
    }
    //DEBUG ((DEBUG_INFO, "%a() OpenInfoCount %d  BlockIo->Media->LogicalPartition %d  gUsbCtrlHandle %x\n", 
    //__FUNCTION__,OpenInfoCount,BlockIo->Media->LogicalPartition,gUsbCtrlHandle));
    if (( VirualUsb || BlockIo->Media->LogicalPartition) && gUsbCtrlHandle && (!BlockIo->Media->ReadOnly)) {
        if(OpenInfo)
        FreePool(OpenInfo);
        
        DEBUG ((DEBUG_INFO, "%a() Virtual USB found \n", __FUNCTION__));
        Status = gBS->OpenProtocolInformation(
                                    gUsbCtrlHandle,
                                    &gEfiUsb2HcProtocolGuid,
                                    &OpenInfo,
                                    &OpenInfoCount );
        if(EFI_ERROR(Status))
            return Status;

        //DEBUG ((DEBUG_INFO, "%a() OpenInfoCount %d  \n", __FUNCTION__,OpenInfoCount));
    }
        
    for (InfoIndex = 0; InfoIndex < OpenInfoCount; InfoIndex++){
        
        if (OpenInfo[InfoIndex].Attributes & EFI_OPEN_PROTOCOL_BY_CHILD_CONTROLLER){
            VolumeHandle = OpenInfo[InfoIndex].ControllerHandle;    
            Status = gBS->HandleProtocol (
                                VolumeHandle,
                                &gEfiSimpleFileSystemProtocolGuid,
                                (VOID **)&SimpleFileSystemProtocol );
            
            if(EFI_ERROR(Status)){
                continue;
            }
            Status = SimpleFileSystemProtocol->OpenVolume (
                                                SimpleFileSystemProtocol, 
                                                &RootFs);
            if (EFI_ERROR(Status)){
                continue;
            }
            
            // Get volume information of file system
            Size        = SIZE_OF_EFI_FILE_SYSTEM_INFO + 100;
            VolumeInfo  = (EFI_FILE_SYSTEM_INFO *) AllocateZeroPool (Size);
            
            if(VolumeInfo == NULL)
               continue;
            
            Status = RootFs->GetInfo (
                                RootFs,
                                &gEfiFileSystemInfoGuid,
                                &Size,
                                VolumeInfo );
            
            if (EFI_ERROR(Status)){
                continue;
            }
            
            ZeroMem (&StorageVolInfoEntry, sizeof(DEV_ENTRY));
            StorageStringBuffer = AllocateZeroPool(MaxStorageStrBuffSize);
            if (StorageStringBuffer == NULL) {
                DEBUG((DEBUG_ERROR, "%a : Memory allocation failed!!!\n", __FUNCTION__));
                return EFI_OUT_OF_RESOURCES;
            }
            TotalStringLength = 0;
            StringIndex = 1;
            
            StorageVolInfoEntry.Signature = DEV_ENTRY_SIGNATURE;
            StorageVolInfoEntry.Dp.DeviceStatus.DeviceType = SysInvDevStorageVolume;
            StorageVolInfoEntry.Dp.DeviceStatus.DeviceInstance = gStorageVolumeIndex;
            StorageVolInfoEntry.Dp.DeviceStatus.VirtualLed = 0x01;
            StorageVolInfoEntry.Dp.DeviceStatus.DeviceState = SysInvDevEnabled;
            StorageVolInfoEntry.DisplayPtr.StorageVolume.Status.State = SysInvDevEnabled;
            StorageVolInfoEntry.DisplayPtr.StorageVolume.Status.Health = SysInvHealthOK;
            StorageVolInfoEntry.InfoSize = sizeof(STORAGE_VOLUME_INFO);

            StorageVolInfoEntry.DisplayPtr.StorageVolume.Status.ValidFlags.StatusVF1Param.StateValid = 1;
            StorageVolInfoEntry.DisplayPtr.StorageVolume.Status.ValidFlags.StatusVF1Param.HealthValid = 1;
            
            Status = gBS->HandleProtocol (
                                VolumeHandle,
                                &gEfiDevicePathProtocolGuid,
                                (VOID **)&DevPath);

            if(!EFI_ERROR(Status)) {
                DevPathStr = ConvertDevicePathToText (DevPath, FALSE, TRUE);
                if(DevPathStr){
                    StorageVolInfoEntry.Dp.UefiDevPath = AllocateReservedZeroPool(StrnSizeS (DevPathStr, PcdGet32 (PcdAmiSysInvMaxStringLength)));
                    if (StorageVolInfoEntry.Dp.UefiDevPath != NULL)
                        StrCpyS (StorageVolInfoEntry.Dp.UefiDevPath, StrnSizeS (DevPathStr, PcdGet32 (PcdAmiSysInvMaxStringLength)), DevPathStr);
                    FreePool(DevPathStr);
                }
            }

            StorageVolInfo = &StorageVolInfoEntry.DisplayPtr.StorageVolume;
            
            StorageVolInfo->ParentDeviceIndex = StorageDeviceEntry->Dp.DeviceStatus.DeviceInstance;
            StorageVolInfo->ValidFlag1.StorageVolInfoVF1Param.ParentDeviceIndexValid = 1;
            
            Status = AddDevEntryToList(
                        (SYS_INV_ITEM_LIST *)&StorageDeviceEntry->DisplayPtr.StorageDevice.ChildVolumeIndex.DevInitialCnt,
                        &StorageVolInfoEntry.Dp.DeviceStatus.DeviceInstance,
                        sizeof (UINT64));

 
            if ((StrCmp (VolumeInfo->VolumeLabel, L"") != 0x00) && (CheckForNonAsciiChar(&VolumeInfo->VolumeLabel[0]))) {
                UnicodeStrToAsciiStrS(&VolumeInfo->VolumeLabel[0], &VolumeLabel[0], sizeof(VolumeLabel));
                StringLength = AsciiSPrint(
                                    TempStringBuffer,
                                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                                    "%a", VolumeLabel);
                if (StringLength) {
                    StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                    if (StorageStringBuffer != NULL) {
                        AsciiStrCpyS(
                                &StorageStringBuffer[TotalStringLength],
                                PcdGet32 (PcdAmiSysInvMaxStringLength),
                                TempStringBuffer);
                        TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                        StorageVolInfo->VolumeLabelStrIndex = StringIndex++;
                        StorageVolInfo->ValidFlag1.StorageVolInfoVF1Param.VolumeLabelValid = TRUE;
                    }
                }
            }
            else {
                StringLength = AsciiSPrint(
                                    TempStringBuffer,
                                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                                    " ");
                if (StringLength) {
                    StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                    if (StorageStringBuffer != NULL) {
                        AsciiStrCpyS(
                                &StorageStringBuffer[TotalStringLength],
                                PcdGet32 (PcdAmiSysInvMaxStringLength),
                                TempStringBuffer);
                        TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                        StorageVolInfo->VolumeLabelStrIndex = StringIndex++;
                        StorageVolInfo->ValidFlag1.StorageVolInfoVF1Param.VolumeLabelValid = TRUE;
                    }
                }
            }
           
            StringLength = AsciiSPrint(
                                TempStringBuffer,
                                PcdGet32 (PcdAmiSysInvMaxStringLength),
                                "VOL%X", gStorageVolumeIndex);
            if (StringLength) {
                StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                if (StorageStringBuffer != NULL) {
                    AsciiStrCpyS(
                            &StorageStringBuffer[TotalStringLength],
                            PcdGet32 (PcdAmiSysInvMaxStringLength),
                            TempStringBuffer);
                    TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                    StorageVolInfo->IdStrIndex = StringIndex++;
                    StorageVolInfo->ValidFlag1.StorageVolInfoVF1Param.IdValid = TRUE;
                }
            }

            StringLength = AsciiSPrint(
                                TempStringBuffer,
                                PcdGet32 (PcdAmiSysInvMaxStringLength),
                                "Volume_%X", gStorageVolumeIndex);
            if (StringLength) {
                StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                if (StorageStringBuffer != NULL) {
                    AsciiStrCpyS(
                            &StorageStringBuffer[TotalStringLength],
                            PcdGet32 (PcdAmiSysInvMaxStringLength),
                            TempStringBuffer);
                    TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                    StorageVolInfo->NameStrIndex = StringIndex++;
                    StorageVolInfo->ValidFlag1.StorageVolInfoVF1Param.NameValid = TRUE;
                }
            }
            
            StorageVolInfo->BlockSizeBytes = VolumeInfo->BlockSize;
            StorageVolInfo->ValidFlag1.StorageVolInfoVF1Param.BlockSizeBytesValid = TRUE;

            StorageVolInfo->CapacityBytes = VolumeInfo->VolumeSize;
            StorageVolInfo->ValidFlag1.StorageVolInfoVF1Param.CapacityBytesValid = TRUE;

            StorageVolInfo->FreeSpace = VolumeInfo->FreeSpace; 

            DEBUG((DEBUG_INFO,"Storage Volume Info: \n"));
            DEBUG((DEBUG_INFO,"Volume String ID: %a\n", StorageVolInfo->Id));
            DEBUG((DEBUG_INFO,"Volume Size: %lx\n", StorageVolInfo->CapacityBytes));
            DEBUG((DEBUG_INFO,"Block Size: %x\n", StorageVolInfo->BlockSizeBytes));
            DEBUG((DEBUG_INFO,"Free Space: %x\n", StorageVolInfo->FreeSpace));

            if (StorageDevice->NVMeNamespaceProperties.ValidFlag1.NVMeNamespacePropertiesVF1Data) { 
                Status = SysInvGetStrings (
                            SystemInventoryInfoProtocol,
                            &StorageDevice->StringHdr,
                            &StrDevStringBuffer);
                if (!EFI_ERROR(Status)) {
                // Nvme Namespace data filling for Volume.
                    if (StorageDevice->NVMeNamespaceProperties.ValidFlag1.NVMeNamespacePropertiesVF1Param.FormattedLBASizeValid) {
                        Status = SysInvGetStringByIndex (
                                    StrDevStringBuffer,
                                    StorageDevice->NVMeNamespaceProperties.FormattedLBASizeStrIndex,
                                    &StringStart );
                        if (!EFI_ERROR(Status)) {
                            StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,AsciiStrLen(StringStart),TotalStringLength);
                            if (StorageStringBuffer != NULL) {
                                AsciiStrCpyS(
                                        &StorageStringBuffer[TotalStringLength],
                                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                                        StringStart);
                                TotalStringLength += (UINT32)AsciiStrnSizeS (
                                                                &StorageStringBuffer[TotalStringLength],
                                                                PcdGet32 (PcdAmiSysInvMaxStringLength) ); 
                                
                                StorageVolInfo->NVMeNamespaceProperties.FormattedLBASizeStrIndex = StringIndex++;
                                StorageVolInfo->NVMeNamespaceProperties.ValidFlag1.NVMeNamespacePropertiesVF1Param.FormattedLBASizeValid= TRUE;
                            }
                        }
                    }
                 
                
                
                    if (StorageDevice->NVMeNamespaceProperties.ValidFlag1.NVMeNamespacePropertiesVF1Param.NVMeVersionValid) {
                        Status = SysInvGetStringByIndex (
                                    StrDevStringBuffer,
                                    StorageDevice->NVMeNamespaceProperties.NVMeVersionStrIndex,
                                    &StringStart );
                        if (!EFI_ERROR(Status)) {
                            StrBuffAvailabilityCheck (
                                    &StorageStringBuffer,
                                    &MaxStorageStrBuffSize,
                                    AsciiStrLen(StringStart),
                                    TotalStringLength );
                            if (StorageStringBuffer != NULL) {
                                AsciiStrCpyS(
                                        &StorageStringBuffer[TotalStringLength],
                                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                                        StringStart);
                                TotalStringLength += (UINT32)AsciiStrnSizeS (
                                                                &StorageStringBuffer[TotalStringLength], 
                                                                PcdGet32 (PcdAmiSysInvMaxStringLength) );
                                StorageVolInfo->NVMeNamespaceProperties.NVMeVersionStrIndex = StringIndex++;
                                StorageVolInfo->NVMeNamespaceProperties.ValidFlag1.NVMeNamespacePropertiesVF1Param.NVMeVersionValid= TRUE;
                            }
                        }
                    }
                    
                    
                    if (StorageDevice->NVMeNamespaceProperties.ValidFlag1.NVMeNamespacePropertiesVF1Param.NamespaceIdValid) {
                        Status = SysInvGetStringByIndex (
                                    StrDevStringBuffer,
                                    StorageDevice->NVMeNamespaceProperties.NamespaceIdStrIndex,
                                    &StringStart );
                        if (!EFI_ERROR(Status)) {
                            StrBuffAvailabilityCheck (
                                    &StorageStringBuffer,
                                    &MaxStorageStrBuffSize,
                                    AsciiStrLen(StringStart),
                                    TotalStringLength );
                            if (StorageStringBuffer != NULL) {
                                AsciiStrCpyS(
                                        &StorageStringBuffer[TotalStringLength],
                                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                                        StringStart);
                                TotalStringLength += (UINT32)AsciiStrnSizeS (
                                                                &StorageStringBuffer[TotalStringLength], 
                                                                PcdGet32 (PcdAmiSysInvMaxStringLength) );
                                StorageVolInfo->NVMeNamespaceProperties.NamespaceIdStrIndex = StringIndex++;
                                StorageVolInfo->NVMeNamespaceProperties.ValidFlag1.NVMeNamespacePropertiesVF1Param.NamespaceIdValid= TRUE;
                            }
                        }
                    }
                    
                    
                    if (StrDevStringBuffer)
                        FreePool(StrDevStringBuffer); 
                }
                
                
                if (StorageDevice->NVMeNamespaceProperties.ValidFlag1.NVMeNamespacePropertiesVF1Param.MetadataTransferredAtEndOfDataLBAValid) {
                    StorageVolInfo->NVMeNamespaceProperties.MetadataTransferredAtEndOfDataLBA = 
                            StorageDevice->NVMeNamespaceProperties.MetadataTransferredAtEndOfDataLBA;
                    StorageVolInfo->NVMeNamespaceProperties.ValidFlag1.NVMeNamespacePropertiesVF1Param.MetadataTransferredAtEndOfDataLBAValid = 1;
                }
                
                if (StorageDevice->NVMeNamespaceProperties.ValidFlag1.NVMeNamespacePropertiesVF1Param.NumberLBAFormatsValid) {
                    StorageVolInfo->NVMeNamespaceProperties.NumberLBAFormats = StorageDevice->NVMeNamespaceProperties.NumberLBAFormats;
                    StorageVolInfo->NVMeNamespaceProperties.ValidFlag1.NVMeNamespacePropertiesVF1Param.NumberLBAFormatsValid = 1;
                }
            }
                
            
            
            Status = SysInvAddStrings (
                                SystemInventoryInfoProtocol,
                                &StorageVolInfo->StringHdr,
                                StorageStringBuffer);
            
            
            if (EFI_ERROR(Status)) {
                DEBUG((DEBUG_ERROR,"%a Failed to add string for Storage Volume instance %d = %r\n", 
                        __FUNCTION__, gStorageVolumeIndex, Status));
            }  
            if (StorageStringBuffer != NULL){
                FreePool(StorageStringBuffer);
                StorageStringBuffer = NULL;
                MaxStorageStrBuffSize = SIZE_8KB;
            }
            // OEM Hook to update Storage Volume Inventory Data
            OemUpdateStorageVolumeInventory (SystemInventoryInfoProtocol, &StorageVolInfoEntry);

            Status = AddDevEntryToList(
                            (SYS_INV_ITEM_LIST *)&SystemInventoryInfoProtocol->DevInitialCnt, 
                            &StorageVolInfoEntry, 
                            sizeof (DEV_ENTRY) );
            
            if (EFI_ERROR(Status)) {
                DEBUG ((DEBUG_INFO, "StorageVolume[%d] AddDevEntryToList Status = %r, DevInfoCount = %d\n", 
                        gStorageVolumeIndex, Status, SystemInventoryInfoProtocol->DevInfoCount));
            }
            
            gStorageVolumeIndex++;
            FreePool(VolumeInfo);
        }
    }
    FreePool(OpenInfo);

    if (gUsbCtrlHandle && VirualUsb)
        gUsbCtrlHandle = NULL;


    DEBUG((DEBUG_INFO,"%a() Exit\n", __FUNCTION__));
    return EFI_SUCCESS;
}

/**
  @internal
  
  Extracts Locking Feature Level 0 feature descriptor data and fills Security Information
  
  @param [in]  Level0Data                  A pointer to TCG_LEVEL0_DISCOVERY_HEADER structure
  @param [in][out] StorageDeviceInfo       A pointer to STORAGE_DEVICE_INFO to fill Security data.
  
  @return  EFI_SUCCESS                  Requested feature data extracted successfully.
  @return  EFI_NOT_FOUND                Requested feature data not supported by the device.
  @return  EFI_INVALID_PARAMETER        One or more input parameters are invalid.
  
  @endinternal
 */
EFI_STATUS
UpdateSecurityInfoFromLevel0Data (
    IN TCG_LEVEL0_DISCOVERY_HEADER           *Level0Data,
    IN OUT STORAGE_DEVICE_INFO               *StorageDeviceInfo
)
{
    EFI_STATUS                                 Status = EFI_NOT_FOUND;
    TCG_LEVEL0_FEATURE_DESCRIPTOR_HEADER       *FeatureDescriptor;
    UINTN                                      TotalLength;
    UINT16                                     FeatureCode;
    TCG_LOCKING_FEATURE_DESCRIPTOR             *LockingFeature;
    
    if(Level0Data == NULL) {
        return EFI_INVALID_PARAMETER;
    }
    
    TotalLength =  ToBigLittleEndianDword(Level0Data->LengthBE) + 4;
    FeatureDescriptor = (TCG_LEVEL0_FEATURE_DESCRIPTOR_HEADER *)((UINT8 *)Level0Data + sizeof(TCG_LEVEL0_DISCOVERY_HEADER));
    do {
        FeatureCode = ((FeatureDescriptor->FeatureCode_BE >> 8) & 0xFF) + (FeatureDescriptor->FeatureCode_BE << 8);
        if((FeatureCode == TCG_FEATURE_LOCKING)) {
            Status = EFI_SUCCESS;
            LockingFeature = (TCG_LOCKING_FEATURE_DESCRIPTOR *) FeatureDescriptor;
            if(LockingFeature->MediaEncryption) {
                StorageDeviceInfo->EncryptionAbility = SysInvEncryptionAbilitySelfEncryptingDrive;
                StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.EncryptionAbilityValid = TRUE;
                // Filling EncryptionStatus data
                if(LockingFeature->LockingEnabled) {
                    StorageDeviceInfo->EncryptionStatus = (LockingFeature->Locked) ? SysInvEncryptionStatusLocked : SysInvEncryptionStatusUnlocked;
                    StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.EncryptionStatusValid = TRUE;
                } else {
                    StorageDeviceInfo->EncryptionStatus = SysInvEncryptionStatusUnlocked;
                    StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.EncryptionStatusValid = TRUE;
                }
            }
            break;
        }
        // Get the Next Feature Descriptor
        FeatureDescriptor = (TCG_LEVEL0_FEATURE_DESCRIPTOR_HEADER *)((UINT8 *)FeatureDescriptor + FeatureDescriptor->Length + 4);
    } while ((UINT8 *)FeatureDescriptor < (UINT8 *)((UINT8 *)Level0Data + TotalLength));
    
    return Status;
}

/**
  @internal
  
  Gets Identify Controller/Namespace Data
  
  @param [in]  NvmePassThru                  A pointer to the EFI_NVM_EXPRESS_PASS_THRU_PROTOCOL instance. 
  @param [out] TransferBuffer                A pointer to Buffer which gets Identify data.
  @param [in]  ControllerNameSpaceStructure  Denotes either Controller or Namespace identify data
  @param [in]  NamespaceId                   If RaidMode flag is set, then NamespaceId denotes NVMe device mapped Port #
                                             If RaidMode flag is not set, then NamespaceId denotes Namespace identifier
  @param [in] RaidMode                       Flag denotes whether NVMe device conneced in RAID mode
  
  @return  EFI_SUCCESS                      Identify data retrieved successfully.
  @return  EFI_INVALID_PARAMETER            One or more input parameters are invalid.
  @return  EFI_DEVICE_ERROR                 Device error occurred while attempting to send the command.
  
  @endinternal
 */
EFI_STATUS
GetNvmeIdentifyData(
    IN  EFI_NVM_EXPRESS_PASS_THRU_PROTOCOL  *NvmePassThru,
    OUT VOID                                *TransferBuffer,
    IN  UINT32                              ControllerNameSpaceStructure, 
    IN  UINT32                              NamespaceId,
    IN  BOOLEAN                             RaidMode
)
{
    EFI_STATUS                                  Status;
    UINT32                                      TransferLength = sizeof(NVME_ADMIN_CONTROLLER_DATA);
    UINT32                                      Nsid = NamespaceId;
    
    if((NvmePassThru == NULL) || (TransferBuffer == NULL)) {
        return EFI_INVALID_PARAMETER;
    }
    
    SetMem( gNvmeCommandPacket, sizeof(EFI_NVM_EXPRESS_PASS_THRU_COMMAND_PACKET), 0 );
    SetMem( gNvmeCompletion, sizeof(EFI_NVM_EXPRESS_COMPLETION), 0);
    SetMem( gNvmeCmd, sizeof(EFI_NVM_EXPRESS_COMMAND), 0);
    
    if(ControllerNameSpaceStructure ==  IdentifyNamespaceCns) {
        Nsid = RaidMode ? 1 : NamespaceId;
    }
    
    // Fill gNvmeCommandPacket to get Identify data
    gNvmeCmd->Nsid = (ControllerNameSpaceStructure == IdentifyControllerCns)? 0:Nsid;
    gNvmeCmd->Cdw0.Opcode = NVME_ADMIN_IDENTIFY_CMD;
    gNvmeCmd->Cdw0.FusedOperation = 0;
    gNvmeCmd->Cdw10 = ControllerNameSpaceStructure;  // ControllerNamespace
    gNvmeCmd->Flags |= CDW10_VALID;
    
    gNvmeCommandPacket->CommandTimeout = 10000000;   // Timeout in 100ns Unit
    gNvmeCommandPacket->NvmeCmd = gNvmeCmd;
    gNvmeCommandPacket->QueueType = 0;               // 0 - Admin Command type, 1- I/O command type 
    gNvmeCommandPacket->NvmeCompletion = gNvmeCompletion;
    gNvmeCommandPacket->TransferBuffer = TransferBuffer;
    gNvmeCommandPacket->TransferLength = TransferLength;
    
    // Send Command through Passthru API
    Status = NvmePassThru->PassThru( NvmePassThru,
                                     RaidMode ? NamespaceId : gNvmeCmd->Nsid,
                                     gNvmeCommandPacket,
                                     NULL
                                     );
    return Status;
}

/**
  @internal
  
  Updates Controller data like ModelName, SerialNo, FirmwareVersion
  
  @param [in] NvmePassThru                   A pointer to the EFI_NVM_EXPRESS_PASS_THRU_PROTOCOL instance. 
  @param [in] ControllerHandle               NVMe controller handle
  @param [in][out] StroageControllerInfo     A pointer to the STORAGE_CONTROLLER_INFO instance. 
  
  @return EFI_SUCCESS          NVMe controller related data filled successfully
  
  @endinternal
 */
EFI_STATUS
InitializeNvmeStorageControllerData(
    EFI_NVM_EXPRESS_PASS_THRU_PROTOCOL     *NvmePassThru,
    EFI_HANDLE                             ControllerHandle,
    STORAGE_CONTROLLER_INFO                *StroageControllerInfo,
 //   CHAR8                                  *StorageStringBuffer,
    UINT32                                 *TotalStringLength,
    UINT16                                 *StringIndex
    ) 
{
    EFI_STATUS                           Status;
    NVME_ADMIN_CONTROLLER_DATA           *IdentifyControllerData;
    CHAR8                                TempStringBuffer[FixedPcdGet32 (PcdAmiSysInvMaxStringLength)];
    //UINTN                                StringLength = 0;
    
    ZeroMem (TempStringBuffer,sizeof(TempStringBuffer));
    
    // Allocating aligned buffer
    IdentifyControllerData = AllocateAlignedPages (EFI_SIZE_TO_PAGES(sizeof(NVME_ADMIN_CONTROLLER_DATA)), 
                                                   NvmePassThru->Mode->IoAlign);
    if(IdentifyControllerData != NULL) {
        Status = GetNvmeIdentifyData ( 
                            NvmePassThru, 
                            (VOID*)IdentifyControllerData, 
                            IdentifyControllerCns, 
                            0,
                            FALSE );

        if(!EFI_ERROR(Status)) {

            PrepareString (TempStringBuffer,(CHAR8 *) IdentifyControllerData->Mn, sizeof(IdentifyControllerData->Mn), FALSE);
            StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize, AsciiStrLen (TempStringBuffer),*TotalStringLength);
            if (StorageStringBuffer != NULL) {
                AsciiStrCpyS(
                        &StorageStringBuffer[*TotalStringLength],
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        TempStringBuffer);
                *TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[*TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                StroageControllerInfo->ModelStrIndex = (*StringIndex)++;
                StroageControllerInfo->Flags1.StorageControllerInfoVF1Param.ModelValid = TRUE;
            }
            
            PrepareString(TempStringBuffer, (CHAR8 *)IdentifyControllerData->Sn, sizeof(IdentifyControllerData->Sn), FALSE);
            StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,AsciiStrLen (TempStringBuffer),*TotalStringLength);
            if (StorageStringBuffer != NULL) {
                AsciiStrCpyS(
                        &StorageStringBuffer[*TotalStringLength],
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        TempStringBuffer);
                *TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[*TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                StroageControllerInfo->SerialNumberStrIndex = (*StringIndex)++;
                StroageControllerInfo->Flags1.StorageControllerInfoVF1Param.SerialNumberValid = TRUE;
            }
            
            PrepareString(TempStringBuffer, (CHAR8 *)IdentifyControllerData->Fr, sizeof(IdentifyControllerData->Fr), FALSE);
            StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,AsciiStrLen (TempStringBuffer),*TotalStringLength);
            if (StorageStringBuffer != NULL) {
                AsciiStrCpyS(
                        &StorageStringBuffer[*TotalStringLength],
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        TempStringBuffer);
                *TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[*TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                StroageControllerInfo->FirmwareVersionStrIndex = (*StringIndex)++;
                StroageControllerInfo->Flags1.StorageControllerInfoVF1Param.FirmwareVersionValid = TRUE;
            }
        }
        
        FreePages(IdentifyControllerData, EFI_SIZE_TO_PAGES(sizeof(NVME_ADMIN_CONTROLLER_DATA)));
    }
    
    // Fill it with 0xFF so that FF can be used as terminator. Consumers can scan until 0xFF.
    SetMem (&StroageControllerInfo->SupportedControllerProtocols, sizeof (StroageControllerInfo->SupportedControllerProtocols), gEndArrayTag);
    
    SetMem (&StroageControllerInfo->SupportedDeviceProtocols, sizeof (StroageControllerInfo->SupportedDeviceProtocols), gEndArrayTag); 
    
    StroageControllerInfo->SupportedControllerProtocols[0] = SysInvProtocolPCIe;
    StroageControllerInfo->SupportedControllerProtocols[1] = SysInvProtocolNVMe;
    StroageControllerInfo->SupportedControllerProtocolsValidArrayElementsCount = 2;
    //StroageControllerInfo->Flags1.VF1.SupportedControllerProtocolsValid = TRUE;

    StroageControllerInfo->SupportedDeviceProtocols[0] = SysInvProtocolNVMe;
    StroageControllerInfo->SupportedDeviceProtocolsValidArrayElementsCount = 1;
    //StroageControllerInfo->Flags1.VF1.SupportedDeviceProtocolsValid = TRUE;

    return EFI_SUCCESS;
}

/**
  @internal
  
  Issues Get Log command with Log Identifier.
  
  @param [in]  NvmePassThru     A pointer to the EFI_NVM_EXPRESS_PASS_THRU_PROTOCOL instance. 
  @param [in]  NamespaceId      Namespace Identifier
  @param [out] TransferBuffer   A pointer to Buffer which gets Level 0 feature descriptor data.
  @param [in]  TransferLength   Size of the buffer
  @param [in]  LogIdentifier    Log identifier used to get specific log information
  
  @return  EFI_SUCCESS                 Specified Log data retrieved successfully.
  @return  EFI_INVALID_PARAMETER       One or more input parameters are invalid.
  @return  EFI_DEVICE_ERROR            Device error occurred while attempting to send the command.
  @return  EFI_UNSUPPORTED             Device not supports Get Log command.
  
  @endinternal
 */
EFI_STATUS
NvmeGetLogData (
    IN  EFI_NVM_EXPRESS_PASS_THRU_PROTOCOL     *NvmePassThru,
    IN  UINT32                                 NamespaceId,
    OUT VOID                                   *TransferBuffer,
    IN  UINT32                                 TransferLength,
    IN  UINT8                                  LogIdentifier
)
{
    EFI_STATUS                                  Status;
    
    if((NvmePassThru == NULL) || (TransferBuffer == NULL)) {
        return EFI_INVALID_PARAMETER;
    }
    
    SetMem( gNvmeCommandPacket, sizeof(EFI_NVM_EXPRESS_PASS_THRU_COMMAND_PACKET), 0 );
    SetMem( gNvmeCompletion, sizeof(EFI_NVM_EXPRESS_COMPLETION), 0);
    SetMem( gNvmeCmd, sizeof(EFI_NVM_EXPRESS_COMMAND), 0);
    
    // Fill gNvmeCommandPacket to get Smart/Health Log info
    gNvmeCmd->Nsid = 0xFFFFFFFF;
    gNvmeCmd->Cdw0.Opcode = NVME_ADMIN_GET_LOG_PAGE_CMD;
    gNvmeCmd->Cdw0.FusedOperation = 0;
    
    // Number of DWORD to get from Controller is stored at BIT16 to BIT27
    gNvmeCmd->Cdw10 = (TransferLength >> 2) - 1;
    gNvmeCmd->Cdw10 = gNvmeCmd->Cdw10<<16 | LogIdentifier;    // 0x02 for Smart/Health Log
    
    gNvmeControllerVersion = NvmePassThru->Mode->NvmeVersion ? NvmePassThru->Mode->NvmeVersion : gNvmeControllerVersion;
    
#if NVME_DRIVER_VERSION < 21
    gNvmeCmd->Flags |= (CDW10_VALID|CDW11_VALID|CDW12_VALID|CDW13_VALID);
#else
    if(gNvmeControllerVersion < NVME_SPEC_VS_1_2_1) {
        gNvmeCmd->Flags |= CDW10_VALID;
    } else {
        gNvmeCmd->Flags |= (CDW10_VALID|CDW11_VALID|CDW12_VALID|CDW13_VALID);
    }
#endif

    gNvmeCommandPacket->CommandTimeout = 10000000;    // 1Sec
    gNvmeCommandPacket->NvmeCmd = gNvmeCmd;   
    gNvmeCommandPacket->QueueType = 0;
    gNvmeCommandPacket->NvmeCompletion = gNvmeCompletion;
    gNvmeCommandPacket->TransferBuffer = TransferBuffer;
    gNvmeCommandPacket->TransferLength = TransferLength;

    // Send Command through Passthru API
    Status = NvmePassThru->PassThru(
                                NvmePassThru,
                                NamespaceId,
                                gNvmeCommandPacket,
                                NULL );
    return Status;
}

/**
  @internal
  
  Gets supported security protocol list of NVMe device.
  
  @param [in]  NvmePassThru     A pointer to the EFI_NVM_EXPRESS_PASS_THRU_PROTOCOL instance. 
  @param [in]  NamespaceId      Namespace Identifier
  @param [out] TransferBuffer   A pointer to Buffer which gets Level 0 feature descriptor data.
  
  @return  EFI_SUCCESS                 Supported Security Protocol List retrieved successfully.
  @return  EFI_INVALID_PARAMETER       One or more input parameters are invalid.
  @return  EFI_DEVICE_ERROR            Device error occurred while attempting to send the command.
  @return  EFI_UNSUPPORTED             Device not supports Security Receive command.
  
  @endinternal
 */
EFI_STATUS
GetNvmeSupportedProtocolList (
    IN  EFI_NVM_EXPRESS_PASS_THRU_PROTOCOL     *NvmePassThru,
    IN  UINT32                                 NamespaceId,
    OUT VOID                                   *TransferBuffer
)
{
    EFI_STATUS                                 Status;
    UINT32                                     BufferSize = sizeof(TCG_SUPPORTED_SECURITY_PROTOCOLS);
    
    if((TransferBuffer == NULL) || (NvmePassThru == NULL)) {
        return EFI_INVALID_PARAMETER;
    }
    
    SetMem( gNvmeCommandPacket, sizeof(EFI_NVM_EXPRESS_PASS_THRU_COMMAND_PACKET), 0 );
    SetMem( gNvmeCmd, sizeof(EFI_NVM_EXPRESS_COMMAND), 0);
    SetMem( gNvmeCompletion, sizeof(EFI_NVM_EXPRESS_COMPLETION), 0);
    
    gNvmeCmd->Cdw0.Opcode = NVME_ADMIN_SECURITY_RECEIVE_CMD;
    gNvmeCmd->Cdw0.FusedOperation = 0;
    gNvmeCmd->Cdw10 = TCG_SP_SPECIFIC_PROTOCOL_LIST;
    gNvmeCmd->Cdw11 = BufferSize;
    gNvmeCmd->Nsid = 0;
    gNvmeCmd->Flags = ( CDW10_VALID | CDW11_VALID );

    gNvmeCommandPacket->CommandTimeout = 10000000; // 1sec in 100 nano sec units
    gNvmeCommandPacket->QueueType = 0;
    gNvmeCommandPacket->NvmeCompletion = gNvmeCompletion;
    gNvmeCommandPacket->NvmeCmd = gNvmeCmd;
    gNvmeCommandPacket->TransferLength = BufferSize;
    gNvmeCommandPacket->TransferBuffer = TransferBuffer;
 
    // Send Command through Passthru API to get security protocol information
    Status = NvmePassThru->PassThru ( 
                                NvmePassThru,
                                NamespaceId,
                                gNvmeCommandPacket,
                                NULL );
    return Status;
}

/**
  @internal
  
  Gets Level 0 Feature Descriptor data of NVMe device.
  
  @param NvmePassThru     A pointer to the EFI_NVM_EXPRESS_PASS_THRU_PROTOCOL instance. 
  @param NamespaceId      Namespace Identifier
  @param TransferBuffer   A pointer to Buffer which gets Level 0 feature descriptor data.
  
  @return  EFI_SUCCESS                 Level 0 feature descriptor data retrieved successfully.
  @return  EFI_INVALID_PARAMETER       One or more input parameters are invalid.
  @return  EFI_DEVICE_ERROR            Device error occurred while attempting to send the command.
  @return  EFI_UNSUPPORTED             Device not supports Security Receive command.
  
  @endinternal
 */
EFI_STATUS
GetNvmeLevel0FeatureDescriptor (
    IN  EFI_NVM_EXPRESS_PASS_THRU_PROTOCOL     *NvmePassThru,
    IN  UINT32                                 NamespaceId,
    OUT VOID                                   *TransferBuffer
)
{
    EFI_STATUS                                 Status;
    UINT32                                     BufferSize = 0x200;
    
    if((TransferBuffer == NULL) || (NvmePassThru == NULL)) {
        return EFI_INVALID_PARAMETER;
    }

    SetMem(gNvmeCommandPacket, sizeof(EFI_NVM_EXPRESS_PASS_THRU_COMMAND_PACKET), 0);
    SetMem(gNvmeCmd, sizeof(EFI_NVM_EXPRESS_COMMAND), 0);
    SetMem(gNvmeCompletion, sizeof(EFI_NVM_EXPRESS_COMPLETION), 0);
    
    gNvmeCmd->Cdw0.Opcode = NVME_ADMIN_SECURITY_RECEIVE_CMD;
    gNvmeCmd->Cdw0.FusedOperation = 0;
    gNvmeCmd->Cdw10 = (TCG_OPAL_SECURITY_PROTOCOL_1 << 24) + ((TCG_SP_SPECIFIC_PROTOCOL_LEVEL0_DISCOVERY & 0xFF) << 8);
    gNvmeCmd->Cdw11 = BufferSize;
    gNvmeCmd->Nsid = 0;
    gNvmeCmd->Flags = ( CDW10_VALID | CDW11_VALID );

    gNvmeCommandPacket->CommandTimeout = 10000000; // 1sec in 100 nano sec units
    gNvmeCommandPacket->QueueType = 0;
    gNvmeCommandPacket->NvmeCompletion = gNvmeCompletion;
    gNvmeCommandPacket->NvmeCmd = gNvmeCmd;
    gNvmeCommandPacket->TransferLength = BufferSize;
    gNvmeCommandPacket->TransferBuffer = TransferBuffer;
        
    // Send Command through Passthru API
    Status = NvmePassThru->PassThru (
                                NvmePassThru,
                                NamespaceId,
                                gNvmeCommandPacket,
                                NULL );
    return Status;
}

/**
  @internal
  
  Updates Namespace data like BlockSizeBytes, CapacityBytes, Encrpytion Status
  
  @param [in] NvmePassThru               A pointer to the EFI_NVM_EXPRESS_PASS_THRU_PROTOCOL instance. 
  @param [in] NamespaceId                If RaidMode flag is set, then NamespaceId denotes NVMe device mapped Port #
                                         If RaidMode flag is not set, then NamespaceId denotes Namespace identifier
  @param [in][out] StorageDeviceInfo     A pointer to the STORAGE_DEVICE_INFO instance. 
  @param [in] RaidMode                   Flag denotes whether NVMe device conneced in RAID mode
  
  @return EFI_SUCCESS               Namespace related data filled successfully
  
  @endinternal
 */
EFI_STATUS
InitializeNvmeNamespaceData(
    IN EFI_NVM_EXPRESS_PASS_THRU_PROTOCOL    *NvmePassThru,
    IN UINT32                                NamespaceId,
    IN OUT STORAGE_DEVICE_INFO               *StorageDeviceInfo,
    IN BOOLEAN                               RaidMode,
    //IN OUT CHAR8                             *StorageStringBuffer,
    IN OUT UINT32                            *TotalStringLength,
    IN OUT UINT16                            *StringIndex
) 
{
    EFI_STATUS                              Status;
    NVME_ADMIN_CONTROLLER_DATA              *IdentifyControllerData;
    NVME_ADMIN_NAMESPACE_DATA               *IdentifyNamespaceData;
    NVME_SMART_HEALTH_INFO_LOG              *SmartData = NULL;
    UINT32                                  BlockSize;
    BOOLEAN                                 SecurityCommandsSupported = FALSE;
    TCG_SUPPORTED_SECURITY_PROTOCOLS        *SecurityProtocolList;
    UINT16                                  ListLength;
    UINT16                                  SpByte;
    VOID                                    *QueryBuffer;
    UINT8                                   PercentageUsed;
    CHAR8                                   TempStringBuffer[FixedPcdGet32 (PcdAmiSysInvMaxStringLength)];
    UINTN                                   StringLength = 0;
    
    ZeroMem (TempStringBuffer,sizeof(TempStringBuffer));
    
    DEBUG ((DEBUG_INFO, "%a() Entry\n", __FUNCTION__));    
    
    // Allocating aligned buffer
    IdentifyControllerData   = AllocateAlignedPages (EFI_SIZE_TO_PAGES(sizeof(NVME_ADMIN_CONTROLLER_DATA)), 
                                                   NvmePassThru->Mode->IoAlign);
    
    if(IdentifyControllerData != NULL) {
        // Get Controller Identify data
        Status = GetNvmeIdentifyData ( 
                            NvmePassThru, 
                            (VOID*)IdentifyControllerData, 
                            IdentifyControllerCns, 
                            NamespaceId,
                            RaidMode );
        ASSERT_EFI_ERROR(Status);
        if(!EFI_ERROR(Status)) {

            PrepareString(TempStringBuffer, (CHAR8 *)IdentifyControllerData->Mn, sizeof(IdentifyControllerData->Mn), FALSE);
            StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,AsciiStrLen (TempStringBuffer),*TotalStringLength);
            if (StorageStringBuffer != NULL) {
                AsciiStrCpyS(
                        &StorageStringBuffer[*TotalStringLength],
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        TempStringBuffer);
                *TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[*TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                StorageDeviceInfo->ModelStrIndex = (*StringIndex)++;
                StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.ModelValid = TRUE;
            } 

            PrepareString(TempStringBuffer, (CHAR8 *)IdentifyControllerData->Sn, sizeof(IdentifyControllerData->Sn), FALSE);
            StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,AsciiStrLen (TempStringBuffer),*TotalStringLength);
            if (StorageStringBuffer != NULL) {
                AsciiStrCpyS(
                        &StorageStringBuffer[*TotalStringLength],
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        TempStringBuffer);
                *TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[*TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                StorageDeviceInfo->SerialNumberStrIndex = (*StringIndex)++;
                StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.SerialNumberValid = TRUE;
            }

            PrepareString(TempStringBuffer, (CHAR8 *)IdentifyControllerData->Fr, sizeof(IdentifyControllerData->Fr), FALSE);
            StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,AsciiStrLen (TempStringBuffer),*TotalStringLength);
            if (StorageStringBuffer != NULL) {
                AsciiStrCpyS(
                        &StorageStringBuffer[*TotalStringLength],
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        TempStringBuffer);
                *TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[*TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                StorageDeviceInfo->FirmwareRevStringStrIndex = (*StringIndex)++;
                StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.FirmwareRevStringValid = TRUE;
            }
            // If Security commands supported, then set the flag
            if(IdentifyControllerData->Oacs & SECURITY_SEND_RECEIVE_SUPPORTED) {
                SecurityCommandsSupported = TRUE;
            }
        }
        FreePages(IdentifyControllerData, EFI_SIZE_TO_PAGES(sizeof(NVME_ADMIN_CONTROLLER_DATA)));
    }
    
    // Allocating aligned buffer
    IdentifyNamespaceData = AllocateAlignedPages (EFI_SIZE_TO_PAGES(sizeof(NVME_ADMIN_NAMESPACE_DATA)), 
                                                  NvmePassThru->Mode->IoAlign);
    if(IdentifyNamespaceData != NULL) {
        // Get Namespace Identify data
        Status = GetNvmeIdentifyData (
                            NvmePassThru, 
                            (VOID*)IdentifyNamespaceData, 
                            IdentifyNamespaceCns, 
                            NamespaceId,
                            RaidMode );
        ASSERT_EFI_ERROR(Status);
        if (!EFI_ERROR(Status)) {
            
            StringLength = AsciiSPrint(
                                TempStringBuffer, 
                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                "%X",
                                (IdentifyNamespaceData->Flbas & 0xF));
            if (StringLength) {
                StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,*TotalStringLength);
                if (StorageStringBuffer != NULL) {
                    AsciiStrCpyS(
                            &StorageStringBuffer[*TotalStringLength],
                            PcdGet32 (PcdAmiSysInvMaxStringLength),
                            TempStringBuffer);
                    *TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[*TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                    StorageDeviceInfo->NVMeNamespaceProperties.FormattedLBASizeStrIndex = (*StringIndex)++;
                    StorageDeviceInfo->NVMeNamespaceProperties.ValidFlag1.NVMeNamespacePropertiesVF1Param.FormattedLBASizeValid = TRUE;
                }
            }
            
            StringLength = AsciiSPrint(
                                TempStringBuffer, 
                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                "0x%X",
                                NamespaceId);
            if (StringLength) {
                StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,*TotalStringLength);
                if (StorageStringBuffer != NULL) {
                    AsciiStrCpyS(
                            &StorageStringBuffer[*TotalStringLength],
                            PcdGet32 (PcdAmiSysInvMaxStringLength),
                            TempStringBuffer);
                    *TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[*TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                    StorageDeviceInfo->NVMeNamespaceProperties.NamespaceIdStrIndex = (*StringIndex)++;
                    StorageDeviceInfo->NVMeNamespaceProperties.ValidFlag1.NVMeNamespacePropertiesVF1Param.NamespaceIdValid = TRUE;
                }
            }
            
            //Bit 4 of Formatted LBA Size indicate the MetadataTransferredAtEndOfDataLBA
            StorageDeviceInfo->NVMeNamespaceProperties.MetadataTransferredAtEndOfDataLBA = (IdentifyNamespaceData->Flbas & BIT4) ? TRUE:FALSE;
            StorageDeviceInfo->NVMeNamespaceProperties.ValidFlag1.NVMeNamespacePropertiesVF1Param.MetadataTransferredAtEndOfDataLBAValid = TRUE;
                        
            StorageDeviceInfo->NVMeNamespaceProperties.NumberLBAFormats = IdentifyNamespaceData->Nlbaf;
            StorageDeviceInfo->NVMeNamespaceProperties.ValidFlag1.NVMeNamespacePropertiesVF1Param.NumberLBAFormatsValid = TRUE;
                            
            BlockSize = IdentifyNamespaceData->LbaFormat[IdentifyNamespaceData->Flbas & 0xF].Lbads;
            StorageDeviceInfo->BlockSizeBytes = (UINT32) LShiftU64(1, BlockSize);
            StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.BlockSizeBytesValid = TRUE;
            
            StorageDeviceInfo->CapacityBytes = MultU64x32(IdentifyNamespaceData->Nsze, StorageDeviceInfo->BlockSizeBytes);
            StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.CapacityBytesValid = TRUE;
            
            StorageDeviceInfo->MediaType = SysInvMediaTypeSSD;
            StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.MediaTypeValid = TRUE;
            
            StorageDeviceInfo->Protocol = SysInvProtocolNVMe;
            StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.ProtocolValid = TRUE;
        }
        FreePages(IdentifyNamespaceData, EFI_SIZE_TO_PAGES(sizeof(NVME_ADMIN_NAMESPACE_DATA)));
    }
    
    // Allocating aligned buffer
    SmartData = AllocateAlignedPages (EFI_SIZE_TO_PAGES(sizeof(NVME_SMART_HEALTH_INFO_LOG)), 
                                      NvmePassThru->Mode->IoAlign);
    if(SmartData != NULL) {
        // Get SMART Health data
        Status = NvmeGetLogData(
                        NvmePassThru, 
                        NamespaceId, 
                        (VOID*)SmartData, 
                        sizeof(NVME_SMART_HEALTH_INFO_LOG), 
                        LID_SMART_INFO );
        if(!EFI_ERROR(Status)) {
            if(SmartData->CriticalWarningAvailableSpare || SmartData->CriticalWarningTemperature || \
               SmartData->CriticalWarningReliability || SmartData->CriticalWarningMediaReadOnly || \
               SmartData->CriticalWarningVolatileBackup ) {
                
                StorageDeviceInfo->StatusIndicator = SysInvStatusIndicatorFail;
                StorageDeviceInfo->Status.State = SysInvDevEnabled;
                StorageDeviceInfo->Status.Health = SysInvHealthCritical;
                StorageDeviceInfo->Status.ValidFlags.StatusVF1Param.StateValid = TRUE;
                StorageDeviceInfo->Status.ValidFlags.StatusVF1Param.HealthValid = TRUE;
                StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.StatusIndicatorValid = TRUE;

                StorageDeviceInfo->FailurePredicted = TRUE;
                StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.FailurePredictedValid = TRUE;
            } else {
                StorageDeviceInfo->StatusIndicator = SysInvStatusIndicatorOK;
                StorageDeviceInfo->Status.State = SysInvDevEnabled;
                StorageDeviceInfo->Status.Health = SysInvHealthOK;
                StorageDeviceInfo->Status.ValidFlags.StatusVF1Param.StateValid = TRUE;
                StorageDeviceInfo->Status.ValidFlags.StatusVF1Param.HealthValid = TRUE;
                StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.StatusIndicatorValid = TRUE;
            }
            
            // PercentageUsed field will goes upto 255 (0 based).
            // Converting value PercentageUsed to 100 if it more than 100.
            // When StorageDeviceInfo->PredictedMediaLifeLeftPercent zero doesn't mean that device is unusable. 
            PercentageUsed = (SmartData->PercentageUsed < 100) ? SmartData->PercentageUsed : 100;
            StorageDeviceInfo->PredictedMediaLifeLeftPercent= 100 - PercentageUsed;
            StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.PredictedMediaLifeLeftPercentValid = TRUE;
        }
        FreePages(SmartData, EFI_SIZE_TO_PAGES(sizeof(NVME_SMART_HEALTH_INFO_LOG)));
    }
    
    // Initialize security related data
    StorageDeviceInfo->EncryptionAbility = SysInvEncryptionAbilityNone;
    StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.EncryptionAbilityValid = TRUE;

    
    if(SecurityCommandsSupported) {
        
        // Allocating aligned buffer
        QueryBuffer = AllocateAlignedPages (EFI_SIZE_TO_PAGES(sizeof(TCG_SUPPORTED_SECURITY_PROTOCOLS)), 
                                            NvmePassThru->Mode->IoAlign);
        if(QueryBuffer != NULL) {
            // Get Supported Security Protocol List
            Status = GetNvmeSupportedProtocolList(
                                NvmePassThru,
                                NamespaceId,
                                QueryBuffer );
            if (!EFI_ERROR(Status)) {
                SecurityProtocolList = (TCG_SUPPORTED_SECURITY_PROTOCOLS*)QueryBuffer;
                ListLength = ToBigLittleEndianWord (SecurityProtocolList->ListLength_BE);
                
                for (SpByte = 0; SpByte < ListLength; SpByte++) {
                     if (SecurityProtocolList->List[SpByte] == TCG_OPAL_SECURITY_PROTOCOL_1){
                        SetMem(QueryBuffer, sizeof(TCG_SUPPORTED_SECURITY_PROTOCOLS), 0);
                        
                        Status = GetNvmeLevel0FeatureDescriptor(
                                                NvmePassThru,
                                                NamespaceId,
                                                QueryBuffer );
                        if (!EFI_ERROR(Status)) {
                            Status = UpdateSecurityInfoFromLevel0Data((TCG_LEVEL0_DISCOVERY_HEADER*)QueryBuffer, StorageDeviceInfo);
                            break;
                        }
                    }
                }
            }
            FreePages(QueryBuffer, EFI_SIZE_TO_PAGES(sizeof(TCG_SUPPORTED_SECURITY_PROTOCOLS)));
        }
    }
    
    DEBUG ((DEBUG_INFO, "%a() Exit - Status: %r\n", __FUNCTION__, EFI_SUCCESS));
    return EFI_SUCCESS;
}

/**
  @internal
  
  Fills NVMe Namespace data and append it to DevInfoList.
  
  @param [in][out]  StorageControllerEntry     A pointer to the DEV_ENTRY instance of controller
  @param [in]       NvmePassThru               Points to the EFI_NVM_EXPRESS_PASS_THRU_PROTOCOL
  @param [in]       ControllerHandle           NVMe Controller Handle
  @param [in]       DeviceHandle               NVMe Device Handle
  @param [in] [out] ChildDeviceIndex           Pointer to NVMe Device Index
  @param [in]       RaidMode                   Flag denotes whether NVMe device connected in RAID mode
  
  @return VOID
  
  @endinternal
 */
VOID
FillNvmeNamespaceData( 
    IN OUT SYSTEM_INVENTORY_INFO_PROTOCOL      *SystemInventoryInfoProtocol,
    IN OUT  DEV_ENTRY                           *StorageControllerEntry,
    IN      EFI_NVM_EXPRESS_PASS_THRU_PROTOCOL  *NvmePassThru,
    IN      EFI_HANDLE                          ControllerHandle,
    IN      EFI_HANDLE                          DeviceHandle,
    IN      BOOLEAN                             RaidMode
)
{
    EFI_STATUS                              Status;
    DEV_ENTRY                               StorageDeviceEntry;
    STORAGE_DEVICE_INFO                     *DeviceDisplayInfo;
    EFI_PCI_IO_PROTOCOL                     *PciIo = NULL;
    NVME_VER                                NvmeVersion;
    UINTN                                   Seg;
    UINTN                                   Bus;
    UINTN                                   Dev;
    UINTN                                   Fun;
    UINT32                                  PcieLinkCab = 0xFF;
    UINT16                                  PcieLinkStatus = 0xFF;
    UINT8                                   LinkSpeed;
    UINT8                                   LinkWidth;
    UINT8                                   CapIdPtr;
    EFI_DEVICE_PATH_PROTOCOL                *DevPath;
    EFI_DEVICE_PATH_PROTOCOL                *TempDevPath;
    EFI_DEVICE_PATH_PROTOCOL                MessagingDevicePathNvme = { MESSAGING_DEVICE_PATH, MSG_NVME_NAMESPACE_DP };
    EFI_DEVICE_PATH_PROTOCOL                MessagingDevicePathSata = { MESSAGING_DEVICE_PATH, MSG_SATA_DP };
    NVME_NAMESPACE_DEVICE_PATH              *NvmeDevPath;
    SATA_DEVICE_PATH                        *SataDevPath;
    UINT32                                  NameSpaceId;
    CHAR16                                  *DevPathStr = NULL;
    UINT32                                  TotalStringLength = 0;
    UINT16                                  StringIndex = 1;
    CHAR8                                   TempStringBuffer[FixedPcdGet32 (PcdAmiSysInvMaxStringLength)];
    UINTN                                   StringLength = 0;
    STORAGE_CONTROLLER_INFO                 *StorageController = &StorageControllerEntry->DisplayPtr.StorageController;
    
    ZeroMem (TempStringBuffer,sizeof(TempStringBuffer));

    
    DEBUG ((DEBUG_INFO, "%a() Entry\n", __FUNCTION__));
          
    // Check if this is a NVMe device
    NameSpaceId = 0xFFFFFFFF;
    
    Status = NvmePassThru->GetNextNamespace(NvmePassThru, &NameSpaceId);
            
    if(EFI_ERROR(Status))
        return;
    
    Status = gBS->HandleProtocol (
                        DeviceHandle,
                        &gEfiDevicePathProtocolGuid,
                        (VOID **)&DevPath );
    
    if(EFI_ERROR(Status)) {
        DEBUG ((DEBUG_ERROR, "%a() Failed to locate DevicePath protocol Status = %r\n", __FUNCTION__, Status));
        return;
    }
    
    TempDevPath = DevPath;
    
    if(!RaidMode) {
        TempDevPath = GetDevicePathTypeMatch(TempDevPath, &MessagingDevicePathNvme);
        NvmeDevPath = (NVME_NAMESPACE_DEVICE_PATH*) TempDevPath;
        NameSpaceId = NvmeDevPath->NamespaceId;
        DEBUG ((DEBUG_INFO, "NameSpaceId: %x\n", NvmeDevPath->NamespaceId));
    } else {
        TempDevPath = GetDevicePathTypeMatch(TempDevPath, &MessagingDevicePathSata);
        SataDevPath = (SATA_DEVICE_PATH*) TempDevPath;
        
        DEBUG ((DEBUG_INFO, "NameSpaceId: %x\n", NameSpaceId));
        DEBUG ((DEBUG_INFO, "Sata Port Number: %x PMPort Number: %x\n", SataDevPath->HBAPortNumber, SataDevPath->PortMultiplierPortNumber));
    }
    
    if(TempDevPath == NULL) {
        DEBUG ((DEBUG_ERROR, "%a() Messaging device path not found\n", __FUNCTION__));
        return;
    }

    SetMem(&StorageDeviceEntry, sizeof(DEV_ENTRY), 0);
    StorageStringBuffer = AllocateZeroPool(MaxStorageStrBuffSize);
    if (StorageStringBuffer == NULL) {
        DEBUG((DEBUG_ERROR, "%a : Memory allocation failed!!!\n", __FUNCTION__));
        return;
    }
    gNvmeControllerVersion = 0;
    StorageDeviceEntry.Signature = DEV_ENTRY_SIGNATURE;
    
    DevPathStr = ConvertDevicePathToText (DevPath, FALSE, TRUE);
    if(DevPathStr){
        StorageDeviceEntry.Dp.UefiDevPath = AllocateReservedZeroPool(StrnSizeS (DevPathStr, PcdGet32 (PcdAmiSysInvMaxStringLength)));
        if (StorageDeviceEntry.Dp.UefiDevPath != NULL)
            StrCpyS (StorageDeviceEntry.Dp.UefiDevPath, StrnSizeS (DevPathStr, PcdGet32 (PcdAmiSysInvMaxStringLength)), DevPathStr);
        FillStorageAssetTag(&StorageDeviceEntry, DevPathStr, /*StorageStringBuffer,*/ &TotalStringLength, &StringIndex);
        FreePool(DevPathStr);
    }
    
    StorageDeviceEntry.Dp.DeviceStatus.DeviceType = SysInvDevStorageDisk;
    StorageDeviceEntry.Dp.DeviceStatus.DeviceInstance = gStorageDeviceIndex;
    StorageDeviceEntry.Dp.DeviceStatus.VirtualLed = 0x01;
    StorageDeviceEntry.Dp.DeviceStatus.DeviceState = SysInvDevEnabled;
    StorageDeviceEntry.DisplayPtr.StorageDevice.Status.State = SysInvDevEnabled;
    StorageDeviceEntry.DisplayPtr.StorageDevice.Status.Health = SysInvHealthOK;
    
    StorageDeviceEntry.DisplayPtr.StorageDevice.Status.ValidFlags.StatusVF1Param.StateValid = 1;
    StorageDeviceEntry.DisplayPtr.StorageDevice.Status.ValidFlags.StatusVF1Param.HealthValid = 1;

    DeviceDisplayInfo = (STORAGE_DEVICE_INFO*)&StorageDeviceEntry.DisplayPtr.StorageDevice;

    StorageDeviceEntry.InfoSize = sizeof(STORAGE_DEVICE_INFO);
    DeviceDisplayInfo->ParentControllerIndex = StorageControllerEntry->Dp.DeviceStatus.DeviceInstance;
    DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.ParentControllerIndexValid = 1;
    
    
    Status = AddDevEntryToList(
                (SYS_INV_ITEM_LIST *)&StorageControllerEntry->DisplayPtr.StorageController.ChildDeviceIndex.DevInitialCnt,
                &gStorageDeviceIndex,
                sizeof (UINT64));
    DEBUG ((DEBUG_INFO, "Storage controller[%d]: AddDevEntryToList Child Status = %r, Index = %d\n", 
            StorageControllerEntry->Dp.DeviceStatus.DeviceInstance, Status, 
            StorageControllerEntry->DisplayPtr.StorageController.ChildDeviceIndex.DevInfoCount));


    // As NVMe is SSD, filling zero
    DeviceDisplayInfo->RotationSpeedRPM = 0;
    DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.RotationSpeedRPMValid = TRUE;
    
    Status = gBS->HandleProtocol ( ControllerHandle,
                                   &gEfiPciIoProtocolGuid,
                                   (VOID**)&PciIo );
    if(EFI_ERROR(Status)){
        return;
    }
    Status = PciIo->GetLocation(PciIo, &Seg, &Bus, &Dev, &Fun);
    if(!EFI_ERROR(Status)) {
        
        // If NVMe device not in RAID mode
        if(!RaidMode) {
            
            //Maximum Queue Entries Supported
            Status = PciIo->Mem.Read(
                                   PciIo,
                                   EfiPciIoWidthUint16,
                                   0,
                                   NVME_CAP_OFFSET,
                                   1, 
                                   &StorageController->NVMeControllerProperties.MaxQueueSize);
            StorageController->NVMeControllerProperties.Flags1.NVMeControllerPropertiesVF1Param.MaxQueueSizeValid = 1;
            // NVMe Revision
            Status = PciIo->Mem.Read(PciIo, EfiPciIoWidthUint32, 0, NVME_VER_OFFSET, 1, &NvmeVersion);

            if (!EFI_ERROR(Status)) {
                StringLength = AsciiSPrint(
                                    TempStringBuffer, 
                                    PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                    "NVMe Spec v%d.%d", 
                                    NvmeVersion.Mjr, 
                                    (UINT8)(NvmeVersion.Mnr >> 8));
                if (StringLength) {
                    StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                    if (StorageStringBuffer != NULL) {
                        AsciiStrCpyS(
                                &StorageStringBuffer[TotalStringLength],
                                PcdGet32 (PcdAmiSysInvMaxStringLength),
                                TempStringBuffer);
                        TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                        DeviceDisplayInfo->RevisionStrIndex = StringIndex++;
                        DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.RevisionValid = TRUE;
                    }
                    
                    StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                    if (StorageStringBuffer != NULL) {
                        AsciiStrCpyS(
                                &StorageStringBuffer[TotalStringLength],
                                PcdGet32 (PcdAmiSysInvMaxStringLength),
                                TempStringBuffer);
                        TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                        DeviceDisplayInfo->NVMeNamespaceProperties.NVMeVersionStrIndex = StringIndex++;
                        DeviceDisplayInfo->NVMeNamespaceProperties.ValidFlag1.NVMeNamespacePropertiesVF1Param.NVMeVersionValid = TRUE;
                    }
                    
                    Status= SysInvAddString (
                                SystemInventoryInfoProtocol,
                                &StorageControllerEntry->DisplayPtr.StorageController.StringHdr,
                                TempStringBuffer,
                                &StorageController->NVMeControllerProperties.NVMeVersionStrIndex);
                    if (!EFI_ERROR(Status)) {
                        StorageController->NVMeControllerProperties.Flags1.NVMeControllerPropertiesVF1Param.NVMeVersionValid = TRUE;
                    }
                }
            }
            gNvmeControllerVersion = (UINT32)((NvmeVersion.Mjr << 16) | NvmeVersion.Mnr);
            DEBUG((DEBUG_ERROR, "NVMe Version: %x\n", gNvmeControllerVersion));
            
            // NVMe Capable and Negotiated Speed
            CapIdPtr = GetPcieCapabilityIdPtr (PciIo, EFI_PCI_CAPABILITY_ID_PCIEXP);
            if(CapIdPtr) {
                // Get Maximum Supported Link Speed
                PciIo->Pci.Read (
                            PciIo,
                            EfiPciIoWidthUint32,
                            CapIdPtr + PCIE_LINK_CAPABILITIES_OFFSET,
                            1,
                            &PcieLinkCab );
                
                LinkSpeed = PcieLinkCab & 0xF;
                
                //Converted the units of CapableSpeedGbs from GT/s to Gbit/s.
                LinkWidth = (PcieLinkCab >> 4) & 0x1F;
                switch (LinkSpeed) {
                    case PCIE_LINK_SPEED_VECT_25G:
                        switch (LinkWidth) {
                            case 1:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "2");
                                break;
                            case 4:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "8");
                                break;
                            case 8:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "16");
                                break;
                            case 16:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "32");
                                break;
                            default:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "N/A");
                                break;
                        }
                        break;
                    case PCIE_LINK_SPEED_VECT_50G:
                        switch (LinkWidth) {
                            case 1:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "4");
                                break;
                            case 4:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "16");
                                break;
                            case 8:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "32");
                                break;
                            case 16:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "64");
                                break;
                            default:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "N/A");
                                break;
                        }
                        break;
                    case PCIE_LINK_SPEED_80G:
                        switch (LinkWidth) {
                            case 1:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "7.88");
                                break;
                            case 4:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "31.51");
                                break;
                            case 8:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "63.02");
                                break;
                            case 16:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "126.03");
                                break;
                            default:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "N/A");
                                break;
                        }
                        break;
                    case PCIE_LINK_SPEED_16G:
                        switch (LinkWidth) {
                            case 1:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "15.75");
                                break;
                            case 4:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "63.02");
                                break;
                            case 8:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "126.03");
                                break;
                            case 16:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "252.06");
                                break;
                            default:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "N/A");
                                break;
                        }
                        break;
//[COMPAL_CHANGES]+>>
                    case PCIE_LINK_SPEED_32G:
                        switch (LinkWidth) {
                            case 1:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "31.51");
                                break;
                            case 4:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "126.03");
                                break;
                            case 8:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "252.06");
                                break;
                            case 16:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "504.12");
                                break;
                            default:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "N/A");
                                break;
                            }
                        break;
//[COMPAL_CHANGES]+<<
                    default:
                        StringLength = AsciiSPrint(
                                            TempStringBuffer, 
                                            PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                            "N/A");
                }
                
                if (StringLength) {
                    StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                    if (StorageStringBuffer != NULL) {
                        AsciiStrCpyS(
                                &StorageStringBuffer[TotalStringLength],
                                PcdGet32 (PcdAmiSysInvMaxStringLength),
                                TempStringBuffer);
                        TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                        DeviceDisplayInfo->CapableSpeedGbsStrIndex = StringIndex++;
                        DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.CapableSpeedGbsValid = TRUE;
                    }
                }
            
                // Get Current Link Speed
                PciIo->Pci.Read (
                            PciIo,
                            EfiPciIoWidthUint16,
                            CapIdPtr + PCIE_LINK_STATUS_OFFSET,
                            1,
                            &PcieLinkStatus );

                LinkSpeed = PcieLinkStatus & 0xF;
				
                //Converted the units of NegotiatedSpeedGbs from GT/s to Gbit/s
                LinkWidth = (PcieLinkStatus >> 4) & 0x1F;
                switch (LinkSpeed) {
                    case PCIE_LINK_SPEED_VECT_25G:
                        switch (LinkWidth) {
                            case 1:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "2");
                                break;
                            case 4:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "8");
                                break;
                            case 8:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "16");
                                break;
                            case 16:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "32");
                                break;
                            default:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "N/A");
                                break;
                        }
                        break;
                    case PCIE_LINK_SPEED_VECT_50G:
                        switch (LinkWidth) {
                            case 1:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "4");
                                break;
                            case 4:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "16");
                                break;
                            case 8:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "32");
                                break;
                            case 16:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "64");
                                break;
                            default:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "N/A");
                                break;
                        }
                        break;
                    case PCIE_LINK_SPEED_80G:
                        switch (LinkWidth) {
                            case 1:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "7.88");
                                break;
                            case 4:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "31.51");
                                break;
                            case 8:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "63.02");
                                break;
                            case 16:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "126.03");
                                break;
                            default:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "N/A");
                                break;
                        }
                        break;
                    case PCIE_LINK_SPEED_16G:
                        switch (LinkWidth) {
                            case 1:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "15.75");
                                break;
                            case 4:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "63.02");
                                break;
                            case 8:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "126.03");
                                break;
                            case 16:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "252.06");
                                break;
                            default:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "N/A");
                                break;
                        }
                        break;
//[COMPAL_CHANGES]+>>
                    case PCIE_LINK_SPEED_32G:
                        switch (LinkWidth) {
                            case 1:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "31.51");
                                break;
                            case 4:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "126.03");
                                break;
                            case 8:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "252.06");
                                break;
                            case 16:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "504.12");
                                break;
                            default:
                                StringLength = AsciiSPrint(
                                                TempStringBuffer, 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "N/A");
                                break;
                            }
                        break;
//[COMPAL_CHANGES]+<<
                    default:
                        StringLength = AsciiSPrint(
                                            TempStringBuffer, 
                                            PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                            "N/A");
                }
                if (StringLength) {
                    StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                    if (StorageStringBuffer != NULL) {
                        AsciiStrCpyS(
                                &StorageStringBuffer[TotalStringLength],
                                PcdGet32 (PcdAmiSysInvMaxStringLength),
                                TempStringBuffer);
                        TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                        DeviceDisplayInfo->NegotiatedSpeedGbsStrIndex = StringIndex++;
                        DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.NegotiatedSpeedGbsValid = TRUE;
                    }
                }
            }
            
            // Update StringId. This will be unique REDFISH ID String for this NVMe Device.
            StringLength = AsciiSPrint(
                                TempStringBuffer, 
                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                "NVMe_Device%X_NSID%X",
                                StorageDeviceEntry.Dp.DeviceStatus.DeviceInstance, NameSpaceId);
            if (StringLength) {
                StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                if (StorageStringBuffer != NULL) {
                    AsciiStrCpyS(
                            &StorageStringBuffer[TotalStringLength],
                            PcdGet32 (PcdAmiSysInvMaxStringLength),
                            TempStringBuffer);
                    TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                    DeviceDisplayInfo->IdStrIndex = StringIndex++;
                    DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.IdValid = TRUE;
                }
       
                StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                if (StorageStringBuffer != NULL) {
                    AsciiStrCpyS(
                                &StorageStringBuffer[TotalStringLength],
                                PcdGet32 (PcdAmiSysInvMaxStringLength),
                                TempStringBuffer);
                    TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                    DeviceDisplayInfo->NameStrIndex = StringIndex++;
                    DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.NameValid = TRUE;
                }
            }
            
            StringLength =  AsciiSPrint(
                                TempStringBuffer, 
                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                "S%1X|B%2X|D%2X|F%2X_NVMe_NSID%X",
                                Seg,Bus,Dev,Fun,NameSpaceId);
            if (StringLength) {
                StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                if (StorageStringBuffer != NULL) {
                    AsciiStrCpyS(
                            &StorageStringBuffer[TotalStringLength],
                            PcdGet32 (PcdAmiSysInvMaxStringLength),
                            TempStringBuffer);
                    TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                    DeviceDisplayInfo->LocationStrIndex = StringIndex++;
                    DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.LocationValid = TRUE;
                }
            }
        } else {
            StringLength = AsciiSPrint(
                                TempStringBuffer, 
                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                "N/A");
            if (StringLength) {
                StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                if (StorageStringBuffer != NULL) {
                    AsciiStrCpyS(
                            &StorageStringBuffer[TotalStringLength],
                            PcdGet32 (PcdAmiSysInvMaxStringLength),
                            TempStringBuffer);
                    TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                    DeviceDisplayInfo->CapableSpeedGbsStrIndex = StringIndex++;
                    DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.CapableSpeedGbsValid = TRUE;
                }
                StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                if (StorageStringBuffer != NULL) {
                    AsciiStrCpyS(
                            &StorageStringBuffer[TotalStringLength],
                            PcdGet32 (PcdAmiSysInvMaxStringLength),
                            TempStringBuffer);
                    TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                    DeviceDisplayInfo->NegotiatedSpeedGbsStrIndex = StringIndex++;
                    DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.NegotiatedSpeedGbsValid = TRUE;
                }
    
                StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                if (StorageStringBuffer != NULL) {
                    AsciiStrCpyS(
                            &StorageStringBuffer[TotalStringLength],
                            PcdGet32 (PcdAmiSysInvMaxStringLength),
                            TempStringBuffer);
                    TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                    DeviceDisplayInfo->RevisionStrIndex = StringIndex++;
                    DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.RevisionValid = TRUE;
                }
            }
            // Update StringId. This will be unique REDFISH ID String for this ATA Device.
            
            StringLength = AsciiSPrint(
                                TempStringBuffer, 
                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                "SATA_Device%X_Port%X",
                                StorageDeviceEntry.Dp.DeviceStatus.DeviceInstance, SataDevPath->HBAPortNumber);
            if (StringLength) {
                StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                if (StorageStringBuffer != NULL) {
                    AsciiStrCpyS(
                            &StorageStringBuffer[TotalStringLength],
                            PcdGet32 (PcdAmiSysInvMaxStringLength),
                            TempStringBuffer);
                    TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                    DeviceDisplayInfo->IdStrIndex = StringIndex++;
                    DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.IdValid = TRUE;
                }
                StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                if (StorageStringBuffer != NULL) {
                    AsciiStrCpyS(
                                &StorageStringBuffer[TotalStringLength],
                                PcdGet32 (PcdAmiSysInvMaxStringLength),
                                TempStringBuffer);
                    TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                    DeviceDisplayInfo->NameStrIndex = StringIndex++;
                    DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.NameValid = TRUE;
                }
            }

            StringLength =  AsciiSPrint(
                                TempStringBuffer, 
                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                "S%1X|B%2X|D%2X|F%2X_SATA_Port_%XPMPort:%X",
                                Seg,Bus,Dev,Fun, SataDevPath->HBAPortNumber, SataDevPath->PortMultiplierPortNumber);
            if (StringLength) {
                StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                if (StorageStringBuffer != NULL) {
                    AsciiStrCpyS(
                            &StorageStringBuffer[TotalStringLength],
                            PcdGet32 (PcdAmiSysInvMaxStringLength),
                            TempStringBuffer);
                    TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                    DeviceDisplayInfo->LocationStrIndex = StringIndex++;
                    DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.LocationValid = TRUE;
                }
            }
        }
    }
    
    StringLength = AsciiSPrint(
                        TempStringBuffer, 
                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                        "N/A");
    if (StringLength) {
        StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
        if (StorageStringBuffer != NULL) {
            AsciiStrCpyS(
                    &StorageStringBuffer[TotalStringLength],
                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                    TempStringBuffer);
            TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
            DeviceDisplayInfo->ManufacturerStrIndex = StringIndex++;
            DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.ManufacturerValid = TRUE;
        }
    }
    // Update Namespace related data
    InitializeNvmeNamespaceData(NvmePassThru, NameSpaceId, DeviceDisplayInfo, RaidMode,/* StorageStringBuffer,*/ &TotalStringLength, &StringIndex);

    Status = SysInvAddStrings (
                        SystemInventoryInfoProtocol,
                        &DeviceDisplayInfo->StringHdr,
                        StorageStringBuffer);
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR,"%a Failed to add string for Storage Device instance %d = %r\n", __FUNCTION__, gStorageDeviceIndex, Status));
    }            
    if (StorageStringBuffer != NULL){
        FreePool(StorageStringBuffer);
        StorageStringBuffer = NULL;
        MaxStorageStrBuffSize = SIZE_8KB;
    }
    // OEM Hook to update NVME Storage Device Inventory Data
    OemUpdateNvmeStorageDeviceInventory (SystemInventoryInfoProtocol, &StorageDeviceEntry);
    
    Status = InitializeStorageVolumesData (SystemInventoryInfoProtocol, &StorageDeviceEntry, DeviceHandle);
    DEBUG ((DEBUG_INFO, "Nvme Device[%d]: InitializeStorageVolumesData Status = %r\n", gStorageDeviceIndex, Status));

    // All fields are filled successfully. Append the filled data 
    Status = AddDevEntryToList((SYS_INV_ITEM_LIST *)&SystemInventoryInfoProtocol->DevInitialCnt, &StorageDeviceEntry, sizeof(DEV_ENTRY));
    DEBUG ((DEBUG_INFO, "Nvme Device[%d]: AddDevEntryToList Status = %r, DevInfoCount = %d\n", 
            gStorageDeviceIndex, Status, SystemInventoryInfoProtocol->DevInfoCount));
    
    gStorageDeviceIndex++;
    if (gStorageVolumeIndex){
        SystemInventoryInfoProtocol->DataValidFlags.StorageVolumeInfoValid = TRUE;
    }
    
    DEBUG((DEBUG_INFO,"%a() Exit  - Status: %r \n", __FUNCTION__, Status));
    return; 
}

/**
  @internal
  
  Fills ATA/SATA controller speed and supported protocols
  
  @param [in]      AtaPassThru                  A pointer to the EFI_ATA_PASS_THRU_PROTOCOL instance
  @param [in]      ControllerHandle             SATA controller PCI handle
  @param [in][out] StroageControllerInfo        A pointer to the STORAGE_CONTROLLER_INFO instance
  
  @return  EFI_SUCCESS                    SATA controller data filled successfully
  
  @endinternal
 */
EFI_STATUS
InitializeAtaStorageControllerData(
    IN EFI_ATA_PASS_THRU_PROTOCOL             *AtaPassThru,
    IN EFI_HANDLE                             ControllerHandle,
    IN OUT STORAGE_CONTROLLER_INFO            *StroageControllerInfo,
    //IN OUT CHAR8                              *StorageStringBuffer,
    IN OUT UINT32                             *TotalStringLength,
    IN OUT UINT16                             *StringIndex
)
{
    EFI_STATUS                           Status;
    EFI_PCI_IO_PROTOCOL                  *PciIo;
    UINT32                               HbaCapabilityData;
    UINT8                                InterfaceSpeedSupport;
    CHAR8                                TempStringBuffer[FixedPcdGet32 (PcdAmiSysInvMaxStringLength)];
    UINTN                                StringLength = 0;
    
    ZeroMem (TempStringBuffer,sizeof(TempStringBuffer));

    
    DEBUG((DEBUG_INFO,"%a() Entry \n", __FUNCTION__));
    
    Status = gBS->HandleProtocol (
                         ControllerHandle,
                         &gEfiPciIoProtocolGuid,
                         (VOID **)&PciIo);
    
    if (EFI_ERROR(Status))
        return Status;
    
    // Make sure AHCI Base address is programmed Properly
    Status = PciIo->Mem.Read(
                        PciIo,
                        EfiPciIoWidthUint32,
                        AHCI_ABAR_INDEX,
                        0,
                        1,
                        &HbaCapabilityData );
       
    if (!EFI_ERROR(Status)) {
        // Get the Interface Speed Support( Maximum Speed supported)
        InterfaceSpeedSupport = (UINT8)((HbaCapabilityData & (BIT20+BIT21+BIT22+BIT23)) >> 20);

        switch(InterfaceSpeedSupport) {
            case 1:
                StringLength = AsciiSPrint(TempStringBuffer, PcdGet32 (PcdAmiSysInvMaxStringLength), "1.5");
                break;
            case 2:
                StringLength = AsciiSPrint(TempStringBuffer, PcdGet32 (PcdAmiSysInvMaxStringLength), "3.0");
                break;
            case 3:
                StringLength = AsciiSPrint(TempStringBuffer, PcdGet32 (PcdAmiSysInvMaxStringLength), "6.0");
                break;
            default:
                StringLength = AsciiSPrint(TempStringBuffer, PcdGet32 (PcdAmiSysInvMaxStringLength), "N/A");
        }
        if (StringLength) {
            StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,*TotalStringLength);
            if (StorageStringBuffer != NULL) {
                AsciiStrCpyS(
                        &StorageStringBuffer[*TotalStringLength],
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        TempStringBuffer);
                *TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[*TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                StroageControllerInfo->SpeedGbpsStrIndex = (*StringIndex)++;
                StroageControllerInfo->Flags1.StorageControllerInfoVF1Param.SpeedGbpsValid = TRUE;
            }
        }
    }
    
    StringLength = AsciiSPrint(TempStringBuffer, PcdGet32 (PcdAmiSysInvMaxStringLength), "Not Available");
    if (StringLength) {
        StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,*TotalStringLength);
        if (StorageStringBuffer != NULL) {
            AsciiStrCpyS(
                    &StorageStringBuffer[*TotalStringLength],
                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                    TempStringBuffer);
            *TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[*TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
            StroageControllerInfo->FirmwareVersionStrIndex = (*StringIndex)++;
            StroageControllerInfo->Flags1.StorageControllerInfoVF1Param.FirmwareVersionValid = TRUE;
        }
        StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,*TotalStringLength);
        if (StorageStringBuffer != NULL) {
            AsciiStrCpyS(
                    &StorageStringBuffer[*TotalStringLength],
                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                    TempStringBuffer);
            *TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[*TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
            StroageControllerInfo->ModelStrIndex = (*StringIndex)++;
            StroageControllerInfo->Flags1.StorageControllerInfoVF1Param.ModelValid = TRUE;
        }
        StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,*TotalStringLength);
        if (StorageStringBuffer != NULL) {
            AsciiStrCpyS(
                    &StorageStringBuffer[*TotalStringLength],
                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                    TempStringBuffer);
            *TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[*TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
            StroageControllerInfo->SerialNumberStrIndex = (*StringIndex)++;
            StroageControllerInfo->Flags1.StorageControllerInfoVF1Param.SerialNumberValid = TRUE;
        }
    }
    // Fill it with 0xFF so that FF can be used as terminator. Consumers can scan until 0xFF.
    SetMem (&StroageControllerInfo->SupportedControllerProtocols, sizeof (StroageControllerInfo->SupportedControllerProtocols), gEndArrayTag);
    SetMem (&StroageControllerInfo->SupportedDeviceProtocols, sizeof (StroageControllerInfo->SupportedDeviceProtocols), gEndArrayTag); 
    
    StroageControllerInfo->SupportedControllerProtocols[0] = SysInvProtocolPCIe;
    StroageControllerInfo->SupportedControllerProtocols[1] = SysInvProtocolAHCI;
    StroageControllerInfo->SupportedControllerProtocols[2] = SysInvProtocolSATA;
    StroageControllerInfo->SupportedControllerProtocolsValidArrayElementsCount = 3;
    
    StroageControllerInfo->SupportedDeviceProtocols[0] = SysInvProtocolAHCI;
    StroageControllerInfo->SupportedDeviceProtocols[1] = SysInvProtocolSATA;
    StroageControllerInfo->SupportedDeviceProtocolsValidArrayElementsCount = 2;

        
    DEBUG((DEBUG_INFO,"%a Exit \n", __FUNCTION__));
    return EFI_SUCCESS;
}

/**
  @internal
  
  Fills TCG security and encryption related data
  
  @param [in] AtaPassThru                  A pointer to the EFI_ATA_PASS_THRU_PROTOCOL instance
  @param [in] Port                         Port number of ATA device
  @param [in] PmPort                       Port Multiplier Port number of ATA device
  @param [in][out] StorageDeviceInfo       A pointer to the STORAGE_DEVICE_INFO instance
  
  @return  EFI_SUCCESS                 ATA device data filled successfully
  @return  EFI_OUT_OF_RESOURCES        Memory could not be allocated for the buffer
  @return  EFI_INVALID_PARAMETER       One or more input parameters are invalid.
  
  @endinternal
 */
EFI_STATUS
FillAtaTcgSecurityInfo(
    IN EFI_ATA_PASS_THRU_PROTOCOL          *AtaPassThru,
    IN UINT16                              Port,
    IN UINT16                              PmPort,
    IN OUT STORAGE_DEVICE_INFO             *StorageDeviceInfo
) 
{
    EFI_STATUS                              Status;
    EFI_ATA_COMMAND_BLOCK                   *Acb;
    EFI_ATA_STATUS_BLOCK                    *Asb;
    EFI_ATA_PASS_THRU_COMMAND_PACKET        Packet;
    UINT16                                  ListLength;
    UINT16                                  SpByte;
    UINT8                                   *QueryBuffer;
    TCG_SUPPORTED_SECURITY_PROTOCOLS        *SupportedProtocolList;
    UINT32                                  QueryBufferSize = sizeof(TCG_SUPPORTED_SECURITY_PROTOCOLS);
    UINT16                                  SpecificId = ToBigLittleEndianWord(TCG_SP_SPECIFIC_PROTOCOL_LEVEL0_DISCOVERY);
    // Check for TCG Storage Security Protocol Support
    if(AtaPassThru == NULL) {
        return EFI_INVALID_PARAMETER;
    }
    
    // Allocating aligned buffer
    QueryBuffer = AllocateAlignedPages (EFI_SIZE_TO_PAGES(QueryBufferSize),\
                                    AtaPassThru->Mode->IoAlign);
    if(QueryBuffer == NULL) {
        ASSERT (QueryBuffer != NULL);
        return EFI_OUT_OF_RESOURCES;
    }
    // Allocating aligned buffer
    Acb = AllocateAlignedPages (EFI_SIZE_TO_PAGES(sizeof(EFI_ATA_COMMAND_BLOCK)), 
                                AtaPassThru->Mode->IoAlign);
    if(Acb == NULL) {
        ASSERT (Acb != NULL);
        return EFI_OUT_OF_RESOURCES;
    }
    
    Asb = AllocateAlignedPages (EFI_SIZE_TO_PAGES(sizeof(EFI_ATA_STATUS_BLOCK)), 
                                AtaPassThru->Mode->IoAlign);
    if(Asb == NULL) {
        ASSERT (Asb != NULL);
        return EFI_OUT_OF_RESOURCES;
    }
    
    SetMem(Acb, sizeof(EFI_ATA_COMMAND_BLOCK), 0);
    SetMem(&Packet, sizeof(Packet), 0);
    
    Acb->AtaCommand          = ATA_CMD_TRUSTED_RECEIVE;
    Acb->AtaDeviceHead       = (UINT8)((UINT8)(PmPort) << 4) ;
    Acb->AtaFeatures         = TCG_SP_SPECIFIC_PROTOCOL_LIST;
    Acb->AtaSectorCount      = 1;

    Packet.Protocol          = EFI_ATA_PASS_THRU_PROTOCOL_PIO_DATA_IN ;
    Packet.Acb               = Acb;
    Packet.Asb               = Asb;
    Packet.InDataBuffer      = QueryBuffer;
    Packet.InTransferLength  = QueryBufferSize;
    Packet.Length            = EFI_ATA_PASS_THRU_LENGTH_BYTES;
    
    Status = AtaPassThru->PassThru(AtaPassThru,
                                   Port,
                                   PmPort,
                                   &Packet,
                                   0);
    if(!EFI_ERROR(Status)) {
        SupportedProtocolList = (TCG_SUPPORTED_SECURITY_PROTOCOLS *)QueryBuffer;
        ListLength = ToBigLittleEndianWord (SupportedProtocolList->ListLength_BE);
        
        for (SpByte = 0; SpByte < ListLength; SpByte++) {
            if (SupportedProtocolList->List[SpByte] == TCG_OPAL_SECURITY_PROTOCOL_1) {
                
                // Now obtain Level Zero Feature Discovery Data
                SetMem(Acb, sizeof(EFI_ATA_COMMAND_BLOCK), 0);
                SetMem(QueryBuffer, QueryBufferSize, 0);
                
                Acb->AtaCommand          = ATA_CMD_TRUSTED_RECEIVE;
                Acb->AtaDeviceHead       = (UINT8)((UINT8)(PmPort) << 4) ;
                Acb->AtaFeatures         = TCG_OPAL_SECURITY_PROTOCOL_1;
                Acb->AtaSectorCount      = 1;
                Acb->AtaCylinderLow      = (UINT8)(SpecificId >> 8);
                Acb->AtaCylinderHigh     = (UINT8)SpecificId;
                
                Status = AtaPassThru->PassThru(AtaPassThru,
                                               Port,
                                               PmPort,
                                               &Packet,
                                               0);
                if(!EFI_ERROR(Status)) {
                    // If locking feature supported, fill locking related data
                    UpdateSecurityInfoFromLevel0Data((TCG_LEVEL0_DISCOVERY_HEADER*)QueryBuffer,StorageDeviceInfo);
                }
                break;
            }
        }
    }
    // Free the allocated Aligned buffer
    FreePages(QueryBuffer, EFI_SIZE_TO_PAGES(QueryBufferSize));
    FreePages(Acb, EFI_SIZE_TO_PAGES(sizeof(EFI_ATA_COMMAND_BLOCK)));
    FreePages(Asb, EFI_SIZE_TO_PAGES(sizeof(EFI_ATA_STATUS_BLOCK)));
    
    DEBUG((DEBUG_INFO,"%a() AtaTcgSecurityInfo Status :%a\n", __FUNCTION__,Status));
    return Status;
}

/**
  @internal
  
  Fills Negotiated Speed Information for the drive connected under specified input
  Port & PmPort number.
  
  @param [in] ControllerHandle             SATA controller handle
  @param [in] Port                         Port number of ATA device
  @param [in] PmPort                       Port Multiplier Port number of ATA device
  @param [in][out] StorageDeviceInfo       A pointer to the STORAGE_DEVICE_INFO instance
  
  @return  EFI_SUCCESS                  Speed info filled successfully.
  
  @endinternal
 */
EFI_STATUS
FillAtaNegotiatedSpeedInfo(
    IN EFI_HANDLE                        ControllerHandle,
    IN UINT16                            Port,
    IN UINT16                            PmPort,
    IN OUT STORAGE_DEVICE_INFO           *StorageDeviceInfo,
   // IN OUT CHAR8                         *StorageStringBuffer,
    IN OUT UINT32                        *TotalStringLength,
    IN OUT UINT16                        *StringIndex
)
{
    EFI_STATUS                           Status;
    EFI_PCI_IO_PROTOCOL                  *PciIo;
    UINT32                               PortStatusReg;
    UINT8                                NegotiatedInterfaceSpeed;
    CHAR8                                TempStringBuffer[FixedPcdGet32 (PcdAmiSysInvMaxStringLength)];
    UINTN                                StringLength = 0;
    
    ZeroMem (TempStringBuffer,sizeof(TempStringBuffer));

        
    Status = gBS->HandleProtocol (
                        ControllerHandle,
                        &gEfiPciIoProtocolGuid,
                        (VOID **)&PciIo );
    ASSERT_EFI_ERROR(Status);
    if (!EFI_ERROR(Status)) {
        // Make sure AHCI Base address is programmed Properly
        Status = PciIo->Mem.Read(
                            PciIo,
                            EfiPciIoWidthUint32,
                            AHCI_ABAR_INDEX,
                            (Port * 0x80) + 0x100 + PSSTS_OFFSET,
                            1,
                            &PortStatusReg );
        ASSERT_EFI_ERROR(Status);
        if (!EFI_ERROR(Status)) {
            // Read Current Interface Speed from PxSSTS register.
            NegotiatedInterfaceSpeed = (PortStatusReg & (BIT4 +BIT5+BIT6+BIT7)) >> 4;
            
            switch(NegotiatedInterfaceSpeed) {
                case 1:
                    StringLength = AsciiSPrint(TempStringBuffer, PcdGet32 (PcdAmiSysInvMaxStringLength), "1.5");
                    break;
                case 2:
                    StringLength = AsciiSPrint(TempStringBuffer, PcdGet32 (PcdAmiSysInvMaxStringLength), "3.0");
                    break;
                case 3:
                    StringLength = AsciiSPrint(TempStringBuffer, PcdGet32 (PcdAmiSysInvMaxStringLength), "6.0");
                    break;
                default:
                    StringLength = AsciiSPrint(TempStringBuffer, PcdGet32 (PcdAmiSysInvMaxStringLength), "N/A");
            }
            if (StringLength) {
                StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,*TotalStringLength);
                if (StorageStringBuffer != NULL) {
                    AsciiStrCpyS(
                            &StorageStringBuffer[*TotalStringLength],
                            PcdGet32 (PcdAmiSysInvMaxStringLength),
                            TempStringBuffer);
                    *TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[*TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                    StorageDeviceInfo->NegotiatedSpeedGbsStrIndex = (*StringIndex)++;
                    StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.NegotiatedSpeedGbsValid = TRUE;
                }
            }
        }
    }
    return EFI_SUCCESS;
}
/**
  @internal
  
  Fills ATAPI Capacity Info.
  
  @param [in] ControllerHandle             SATA controller handle
  @param [in] Port                         Port number of ATA device
  @param [in] PmPort                       Port Multiplier Port number of ATA device
  @param [in][out] StorageDeviceInfo       A pointer to the STORAGE_DEVICE_INFO instance
  
  @return  EFI_SUCCESS                 ATAPI device data filled successfully
  @return  EFI_OUT_OF_RESOURCES        Memory could not be allocated for the buffer
  @return  EFI_INVALID_PARAMETER       One or more input parameters are invalid.
  
  @endinternal
 */
EFI_STATUS
FillAtapiReadCapacityInfo(
  IN EFI_HANDLE                             ControllerHandle,
  IN UINT16                                 Port,
  IN UINT16                                 PmPort,
  IN OUT STORAGE_DEVICE_INFO                *StorageDeviceInfo
) 
{
    EFI_STATUS                                  Status;
    EFI_EXT_SCSI_PASS_THRU_PROTOCOL             *ExtScsiPassThru;
    UINT8                                       Target[TARGET_MAX_BYTES];
    UINT64                                      Lun = 0;
    EFI_EXT_SCSI_PASS_THRU_SCSI_REQUEST_PACKET  *Packet;
    UINT8                                       Cdb[CDB_READCAP16_SIZE];
    UINT8                                       *SenseData;
    ATAPI_READ_CAPACITY_DATA                    AtapiReadCapacity;
    EFI_SCSI_DISK_CAPACITY_DATA16               ReadCapacity16;
    UINT8                                       *LastBlockPtr;
    UINT64                                      LastBlock = 0;
    
    // This protocol will be available if ScsiPassThruAtapi driver is present
    // in the project.
    Status = gBS->HandleProtocol(
                        ControllerHandle, 
                        &gEfiExtScsiPassThruProtocolGuid, 
                        (VOID **)&ExtScsiPassThru );
    if(EFI_ERROR(Status)) {
        return Status;
    }
    
    ZeroMem(Cdb,sizeof(Cdb));
    ZeroMem(&AtapiReadCapacity,sizeof(AtapiReadCapacity));
    ZeroMem(&ReadCapacity16,sizeof(ReadCapacity16));
    
    SetMem(Target,TARGET_MAX_BYTES, 0xFF );
    Target[0] = (UINT8)Port;
    Target[1] = (UINT8)PmPort;
    // For ATAPI device, LUN is considered as Zero.
    Lun       = 0;
    
    // Allocate Memory for Packet
    Packet = AllocateZeroPool (sizeof(EFI_EXT_SCSI_PASS_THRU_SCSI_REQUEST_PACKET));
    if ( Packet == NULL) {
        return EFI_OUT_OF_RESOURCES;
    }
    
    // Allocate Memory for Sense data and Init to 0s
    SenseData = AllocateZeroPool (SENSE_DATA_LENGTH);
    if ( SenseData == NULL) {
        return EFI_OUT_OF_RESOURCES;
    }

    // Structure of CDB packet
    Cdb[0] = ATA_CMD_READ_CAPACITY;

    Packet->Timeout             = EFI_TIMER_PERIOD_SECONDS(1);
    Packet->InDataBuffer        = &AtapiReadCapacity;
    Packet->SenseData           = SenseData;
    Packet->Cdb                 = Cdb;
    Packet->SenseDataLength     = SENSE_DATA_LENGTH;
    Packet->InTransferLength    = 8;
    Packet->CdbLength           = CDB_READ10_SIZE;
    Packet->DataDirection       = EFI_EXT_SCSI_DATA_DIRECTION_READ;
 
    Status = ExtScsiPassThru->PassThru(
                                    ExtScsiPassThru,
                                    Target,
                                    Lun,
                                    Packet,
                                    NULL );
    
    if (!EFI_ERROR (Status)) {   
        
        if ((AtapiReadCapacity.LastLba3 == 0xff) && (AtapiReadCapacity.LastLba2 == 0xff) &&
            (AtapiReadCapacity.LastLba1 == 0xff) && (AtapiReadCapacity.LastLba0 == 0xff)){
        
            // Capacity above 2TB
            Cdb[0]                      = EFI_SCSI_OP_READ_CAPACITY16;
            Cdb[1]                      = 0x10; // Service action
            Cdb[13]                     = sizeof(ReadCapacity16); //Allocated Length
            
            Packet->InDataBuffer        = &ReadCapacity16;
            Packet->CdbLength           = CDB_READCAP16_SIZE;
            
            Status = ExtScsiPassThru->PassThru(
                                            ExtScsiPassThru,
                                            Target,
                                            Lun,
                                            Packet,
                                            NULL );
            if(!EFI_ERROR (Status)) {
                
                LastBlockPtr = (UINT8*)&LastBlock;
                *LastBlockPtr++ = ReadCapacity16.LastLba0;
                *LastBlockPtr++ = ReadCapacity16.LastLba1;
                *LastBlockPtr++ = ReadCapacity16.LastLba2;
                *LastBlockPtr++ = ReadCapacity16.LastLba3;
                *LastBlockPtr++ = ReadCapacity16.LastLba4;
                *LastBlockPtr++ = ReadCapacity16.LastLba5;
                *LastBlockPtr++ = ReadCapacity16.LastLba6;
                *LastBlockPtr   = ReadCapacity16.LastLba7;
                
                StorageDeviceInfo->BlockSizeBytes = (UINT32)(ReadCapacity16.BlockSize3 << 24) |
                                                    (ReadCapacity16.BlockSize2 << 16) |
                                                    (ReadCapacity16.BlockSize1 << 8)  |
                                                    ReadCapacity16.BlockSize0;
                StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.BlockSizeBytesValid = TRUE;
                
                StorageDeviceInfo->CapacityBytes = MultU64x32 ((LastBlock + 1), StorageDeviceInfo->BlockSizeBytes);
                StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.CapacityBytesValid = TRUE;
                                 
            }
        
        } else {
            
            LastBlock = (UINT64)(UINT32)(AtapiReadCapacity.LastLba3 << 24)|(AtapiReadCapacity.LastLba2 << 16) | \
                            (AtapiReadCapacity.LastLba1 << 8) | AtapiReadCapacity.LastLba0 ;
            StorageDeviceInfo->BlockSizeBytes = (AtapiReadCapacity.BlockSize1 << 8) | AtapiReadCapacity.BlockSize0;
            StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.BlockSizeBytesValid = TRUE;
            
            StorageDeviceInfo->CapacityBytes = MultU64x32 ((LastBlock + 1), StorageDeviceInfo->BlockSizeBytes);
            StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.CapacityBytesValid = TRUE;
            
        }
    } else if( Status == EFI_NO_MEDIA) {
        // Filling Capacity and Block Size Info as 0xFFFFFFFF indicating No media present.
        StorageDeviceInfo->BlockSizeBytes = 0xFFFFFFFF;
        StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.BlockSizeBytesValid = TRUE;
        
        StorageDeviceInfo->CapacityBytes = 0xFFFFFFFF;
        StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.CapacityBytesValid = TRUE;
    } else {
        ASSERT_EFI_ERROR(Status);
    }
    FreePool(Packet);
    FreePool(SenseData);
    return Status;
}


/**
  @internal
  
  Fills  media serial number Info.
  
  @param [in] ExtScsiPassThru             ExtScsiPassThru pointer
  @param [in] TargetId                    SCSI Target ID
  @param [in] Lun                        Lun
  @param [in][out] StorageDeviceInfo       A pointer to the STORAGE_DEVICE_INFO instance
  
  @return  EFI_SUCCESS                 Media serial number successfully
  @return  EFI_OUT_OF_RESOURCES        Memory could not be allocated for the buffer
  @return  EFI_INVALID_PARAMETER       One or more input parameters are invalid.
  
  @endinternal
 */
EFI_STATUS
FillMediaSerialNoInfo(
  IN EFI_EXT_SCSI_PASS_THRU_PROTOCOL        *ExtScsiPassThru,
  IN UINT8                                  *TargetId,
  IN UINT64                                 Lun,
  IN OUT STORAGE_DEVICE_INFO                *StorageDeviceInfo,
  IN OUT UINT32                             *TotalStringLength,
  IN OUT UINT16                             *StringIndex
) 
{
    EFI_STATUS                                  Status;
    EFI_EXT_SCSI_PASS_THRU_SCSI_REQUEST_PACKET  *Packet;
    AMI_READ_MEDIA_SERIAL_NO_CDB                Cdb;
    UINT8                                       *SenseData;
    UINT32                                      *ResponseData = NULL;
    CHAR8                                       TempStringBuffer[FixedPcdGet32 (PcdAmiSysInvMaxStringLength)];
    UINT32                                      MediaSerialNoLength = 0;
    UINT8                                       *MediaSerialNumber = NULL;
    UINTN                                       StringLength =0;
    // Initiation
    ZeroMem(&Cdb,sizeof(Cdb));
    ZeroMem (TempStringBuffer,sizeof(TempStringBuffer));
    
    // Allocate Memory for Packet
    Packet = AllocateZeroPool (sizeof(EFI_EXT_SCSI_PASS_THRU_SCSI_REQUEST_PACKET));
    if ( Packet == NULL) {
        return EFI_OUT_OF_RESOURCES;
    }
    
    // Allocate Memory for Sense data and Init to 0s
    SenseData = AllocateZeroPool (SENSE_DATA_LENGTH);
    if ( SenseData == NULL) {
        return EFI_OUT_OF_RESOURCES;
    }
    
    // Response data Memory Allocation 
    ResponseData = AllocateZeroPool (sizeof(UINT32) * 10);
    if ( ResponseData == NULL) {
        return EFI_OUT_OF_RESOURCES;
    }
    
    // Structure of CDB packet READ MEDIA SERIAL NUMBER
    Cdb.OperationCode = 0XAB; //
    Cdb.ServiceAction = 0x1;   
    Cdb.AllocationLength = sizeof(UINT32) * 10; 

    Packet->Timeout             = EFI_TIMER_PERIOD_SECONDS(3);
    Packet->InDataBuffer        = (VOID*)ResponseData;
    Packet->SenseData           = SenseData;
    Packet->Cdb                 = (VOID*)&Cdb;
    Packet->SenseDataLength     = SENSE_DATA_LENGTH;
    Packet->InTransferLength    = sizeof(UINT32) * 10;
    Packet->CdbLength           = sizeof(Cdb);
    Packet->DataDirection       = EFI_EXT_SCSI_DATA_DIRECTION_READ;
 
    Status = ExtScsiPassThru->PassThru(
                                    ExtScsiPassThru,
                                    TargetId,
                                    Lun,
                                    Packet,
                                    NULL );
    DEBUG((DEBUG_INFO,"%a() ExtScsiPassThru->PassThru %r \n", __FUNCTION__,Status));
    if (!EFI_ERROR (Status) ) {
        MediaSerialNoLength = ResponseData[0];
        DEBUG((DEBUG_INFO,"%a() MediaSerialNoLength %x \n", __FUNCTION__,MediaSerialNoLength));
        if (MediaSerialNoLength) {
            MediaSerialNumber = (UINT8*)&ResponseData[1];
            for (UINT32 Index = 0; (Index < MediaSerialNoLength) && (Index < (sizeof(UINT32) * 9)) ; Index++){
                DEBUG((DEBUG_INFO,"%a() MediaSerialNoLength %x \n", __FUNCTION__,MediaSerialNumber[Index]));
            }

            StringLength = AsciiSPrint(
                            TempStringBuffer,
                            PcdGet32 (PcdAmiSysInvMaxStringLength),
                            "%a",(CHAR8*) MediaSerialNumber);
            if (StringLength) {
                StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,AsciiStrLen(TempStringBuffer),*TotalStringLength);
                if (StorageStringBuffer != NULL) {
                    AsciiStrCpyS(
                            &StorageStringBuffer[*TotalStringLength],
                            PcdGet32 (PcdAmiSysInvMaxStringLength),
                            TempStringBuffer);
                    *TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[*TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                    StorageDeviceInfo->SerialNumberStrIndex = (*StringIndex)++;
                    StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.SerialNumberValid = TRUE;
                }
            }
        }
    } 
    
    FreePool(Packet);
    FreePool(SenseData);
    FreePool(ResponseData);
    return Status;
}




/**
  @internal
  
  Fills ATA device related data
  
  @param [in] AtaPassThru                  A pointer to the EFI_ATA_PASS_THRU_PROTOCOL instance
  @param [in] ControllerHandle             SATA controller handle
  @param [in] Port                         Port number of ATA device
  @param [in] PmPort                       Port Multiplier Port number of ATA device
  @param [in][out] StorageDeviceInfo       A pointer to the STORAGE_DEVICE_INFO instance
  
  @return  EFI_SUCCESS                 ATA device data filled successfully
  @return  EFI_OUT_OF_RESOURCES        Memory could not be allocated for the buffer
  @return  EFI_INVALID_PARAMETER       One or more input parameters are invalid.
  
  @endinternal
 */
EFI_STATUS
FillAtaDeviceInfo(
  IN EFI_ATA_PASS_THRU_PROTOCOL             *AtaPassThru,
  IN EFI_HANDLE                             ControllerHandle,
  IN UINT16                                 Port,
  IN UINT16                                 PmPort,
  IN OUT STORAGE_DEVICE_INFO                *StorageDeviceInfo,
  //IN OUT CHAR8                              *StorageStringBuffer,
  IN OUT UINT32                             *TotalStringLength,
  IN OUT UINT16                             *StringIndex
) 
{
    EFI_STATUS                           Status;
    ATA_IDENTIFY_DATA                    *AtaIdentifyData = NULL;
    UINT8                                *Buffer;
    UINT32                               BufferLength = sizeof(ATA_IDENTIFY_DATA);
    EFI_ATA_COMMAND_BLOCK                *Acb;
    EFI_ATA_STATUS_BLOCK                 *Asb;
    EFI_ATA_PASS_THRU_COMMAND_PACKET     Packet;
    UINT64                               NumSectors;
    UINT16                               SmartD2HData;
    CHAR8                                TempStringBuffer[FixedPcdGet32 (PcdAmiSysInvMaxStringLength)];
    UINTN                                StringLength = 0;
    
    ZeroMem (TempStringBuffer,sizeof(TempStringBuffer));

    DEBUG((DEBUG_INFO,"%a() Entry \n", __FUNCTION__));
    
    if((AtaPassThru == NULL) || (StorageDeviceInfo == NULL)) {
        return EFI_INVALID_PARAMETER;
    }
    
    // Allocating aligned buffer
    Buffer = AllocateAlignedPages (EFI_SIZE_TO_PAGES(BufferLength), 
                                   AtaPassThru->Mode->IoAlign);
    if(Buffer == NULL) {
        DEBUG((DEBUG_ERROR,"\n FillAtaDeviceInfo  :Buffer == NULL\n"));
        return EFI_OUT_OF_RESOURCES;
    }
    
    Acb = AllocateAlignedPages (EFI_SIZE_TO_PAGES(sizeof(EFI_ATA_COMMAND_BLOCK)), 
                                AtaPassThru->Mode->IoAlign);
    if(Acb == NULL) {
        FreePages(Buffer, EFI_SIZE_TO_PAGES(BufferLength));
        DEBUG((DEBUG_ERROR,"\n FillAtaDeviceInfo  :Acb == NULL\n"));
        return EFI_OUT_OF_RESOURCES;
    }
    
    Asb = AllocateAlignedPages (EFI_SIZE_TO_PAGES(sizeof(EFI_ATA_STATUS_BLOCK)), 
                                AtaPassThru->Mode->IoAlign);
    if(Asb == NULL) {
        FreePages(Acb, EFI_SIZE_TO_PAGES(sizeof(EFI_ATA_STATUS_BLOCK)));
        FreePages(Buffer, EFI_SIZE_TO_PAGES(BufferLength));
        DEBUG((DEBUG_ERROR,"\n FillAtaDeviceInfo  :Asb == NULL\n"));
        return EFI_OUT_OF_RESOURCES;
    }
    
    SetMem(Acb, sizeof(EFI_ATA_COMMAND_BLOCK), 0);
    SetMem(&Packet, sizeof(EFI_ATA_PASS_THRU_COMMAND_PACKET), 0);
    SetMem(Buffer, BufferLength, 0);
    
    Acb->AtaCommand          = ATA_CMD_IDENTIFY_DRIVE;
    Acb->AtaDeviceHead       = (UINT8)((UINT8)(PmPort) << 4) ;

    Packet.Protocol          = EFI_ATA_PASS_THRU_PROTOCOL_PIO_DATA_IN;
    Packet.Acb               = Acb;
    Packet.Asb               = Asb;
    Packet.InDataBuffer      = Buffer;
    Packet.InTransferLength  = BufferLength;
    Packet.Length            = EFI_ATA_PASS_THRU_LENGTH_BYTES;
    
    Status = AtaPassThru->PassThru(AtaPassThru,
                                   Port,
                                   PmPort,
                                   &Packet,
                                   0);
    DEBUG((DEBUG_ERROR,"\n ATA cmd Status :%r\n", Status));
    if(EFI_ERROR(Status)) {
        // Identify Device command failed. Device may be a ATAPI device. Send Identify Packet Device command
        SetMem(Buffer, sizeof(ATA_IDENTIFY_DATA), 0);
        SetMem(Asb, sizeof(EFI_ATA_STATUS_BLOCK), 0);
        // As both ATA_IDENTIFY_DATA and ATAPI_IDENTIFY_DATA structures having same size, not updating other fields
        Acb->AtaCommand = ATA_CMD_IDENTIFY_DEVICE;
        Status = AtaPassThru->PassThru(
                                    AtaPassThru,
                                    Port,
                                    PmPort,
                                    &Packet,
                                    0 );
        DEBUG((DEBUG_ERROR,"\n ATAPI ATA cmd Status :%r\n", Status));
        if (EFI_ERROR(Status)) {
            FreePages(Acb, EFI_SIZE_TO_PAGES(sizeof(EFI_ATA_COMMAND_BLOCK)));
            FreePages(Asb, EFI_SIZE_TO_PAGES(sizeof(EFI_ATA_STATUS_BLOCK)));
            FreePages(Buffer, EFI_SIZE_TO_PAGES(BufferLength));
            return Status;
        }
    }

    if(!EFI_ERROR(Status)) {
        AtaIdentifyData = (ATA_IDENTIFY_DATA*)Buffer;
        if((AtaIdentifyData->config & (BIT15+BIT14)) == EFI_ATAPI_DEVICE_CONFIG) {
            StorageDeviceInfo->MediaType = SysInvMediaTypeOPTICAL_DVD;
            StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.MediaTypeValid = TRUE;
        }
        
        PrepareString(TempStringBuffer, AtaIdentifyData->ModelName, sizeof(AtaIdentifyData->ModelName), TRUE);
        StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,AsciiStrLen (TempStringBuffer),*TotalStringLength);
        if (StorageStringBuffer != NULL) {
            AsciiStrCpyS(
                    &StorageStringBuffer[*TotalStringLength],
                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                    TempStringBuffer);
            *TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[*TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
            StorageDeviceInfo->ModelStrIndex = (*StringIndex)++;
            StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.ModelValid = TRUE;
        }


        PrepareString(TempStringBuffer, AtaIdentifyData->SerialNo, sizeof(AtaIdentifyData->SerialNo), TRUE);
        StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,AsciiStrLen (TempStringBuffer),*TotalStringLength);
        if (StorageStringBuffer != NULL) {
            AsciiStrCpyS(
                    &StorageStringBuffer[*TotalStringLength],
                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                    TempStringBuffer);
            *TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[*TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
            StorageDeviceInfo->SerialNumberStrIndex = (*StringIndex)++;
            StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.SerialNumberValid = TRUE;
        }
        
        PrepareString(TempStringBuffer, AtaIdentifyData->FirmwareVer, sizeof(AtaIdentifyData->FirmwareVer), FALSE);
        StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,AsciiStrLen (TempStringBuffer),*TotalStringLength);
        if (StorageStringBuffer != NULL) {
            AsciiStrCpyS(
                    &StorageStringBuffer[*TotalStringLength],
                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                    TempStringBuffer);
            *TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[*TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
            StorageDeviceInfo->FirmwareRevStringStrIndex = (*StringIndex)++;
            StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.FirmwareRevStringValid = TRUE;
        }
       
        PrepareString(TempStringBuffer, AtaIdentifyData->FirmwareVer, sizeof(AtaIdentifyData->FirmwareVer), FALSE);
        StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,AsciiStrLen (TempStringBuffer),*TotalStringLength);
        if (StorageStringBuffer != NULL) {
            AsciiStrCpyS(
                    &StorageStringBuffer[*TotalStringLength],
                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                    TempStringBuffer);
            *TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[*TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
            StorageDeviceInfo->RevisionStrIndex = (*StringIndex)++;
            StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.RevisionValid = TRUE;
        }

        StringLength = 0;
        if(AtaIdentifyData->serial_ata_capabilities & BIT3) {
            StringLength = AsciiSPrint(TempStringBuffer, PcdGet32 (PcdAmiSysInvMaxStringLength), "6.0");
        } else if(AtaIdentifyData->serial_ata_capabilities & BIT2) {
            StringLength =AsciiSPrint(TempStringBuffer, PcdGet32 (PcdAmiSysInvMaxStringLength), "3.0");
        } else if(AtaIdentifyData->serial_ata_capabilities & BIT1) {
            StringLength = AsciiSPrint(TempStringBuffer, PcdGet32 (PcdAmiSysInvMaxStringLength), "1.5");
        }
        if (StringLength) {
            StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,*TotalStringLength);
            if (StorageStringBuffer != NULL) {
                AsciiStrCpyS(
                        &StorageStringBuffer[*TotalStringLength],
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        TempStringBuffer);
                *TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[*TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                StorageDeviceInfo->CapableSpeedGbsStrIndex = (*StringIndex)++;
                StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.CapableSpeedGbsValid = TRUE;
            }
        }
        
//        if (!(AtaIdentifyData->major_version_no == 0) && !(AtaIdentifyData->major_version_no == 0xFFFF)) {
//           StringLength = 0;
//           if(AtaIdentifyData->major_version_no & BIT8) {
//               StringLength = AsciiSPrint(TempStringBuffer, PcdGet32 (PcdAmiSysInvMaxStringLength), "Supports ATA8-ACS");
//           } else if(AtaIdentifyData->major_version_no & BIT7) {
//               StringLength = AsciiSPrint(TempStringBuffer, PcdGet32 (PcdAmiSysInvMaxStringLength), "Supports ATA/ATAPI-7");
//              
//           } else if(AtaIdentifyData->major_version_no & BIT6) {
//               StringLength = AsciiSPrint(TempStringBuffer, PcdGet32 (PcdAmiSysInvMaxStringLength), "Supports ATA/ATAPI-6");
//              
//           } else if(AtaIdentifyData->major_version_no & BIT5) {
//               StringLength = AsciiSPrint(TempStringBuffer, PcdGet32 (PcdAmiSysInvMaxStringLength), "Supports ATA/ATAPI-5");
//               
//           } else if(AtaIdentifyData->major_version_no & BIT4) {
//               StringLength = AsciiSPrint(TempStringBuffer, PcdGet32 (PcdAmiSysInvMaxStringLength), "Supports ATA/ATAPI-4");
//           }
//           
//           if (StringLength) {
//               StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,*TotalStringLength);
//               if (StorageStringBuffer != NULL) {
//                   AsciiStrCpyS(
//                           &StorageStringBuffer[*TotalStringLength],
//                           PcdGet32 (PcdAmiSysInvMaxStringLength),
//                           TempStringBuffer);
//                   *TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[*TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
//                   StorageDeviceInfo->RevisionStrIndex = (*StringIndex)++;
//                   StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.RevisionValid = TRUE;
//               }
//           }
//        } else {
//            StringLength = AsciiSPrint(TempStringBuffer, PcdGet32 (PcdAmiSysInvMaxStringLength), "N/A");
//            if (StringLength) {
//                StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,*TotalStringLength);
//                if (StorageStringBuffer != NULL) {
//                    AsciiStrCpyS(
//                            &StorageStringBuffer[*TotalStringLength],
//                            PcdGet32 (PcdAmiSysInvMaxStringLength),
//                            TempStringBuffer);
//                    *TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[*TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
//                    StorageDeviceInfo->RevisionStrIndex = (*StringIndex)++;
//                    StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.RevisionValid = TRUE;
//                }
//            }
//        }
        StorageDeviceInfo->Protocol = SysInvProtocolSATA;
        StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.ProtocolValid = TRUE;
        
        if(StorageDeviceInfo->MediaType == SysInvMediaTypeOPTICAL_DVD) {
            // Get and store device capacity info
            FillAtapiReadCapacityInfo(ControllerHandle, Port, PmPort, StorageDeviceInfo);
        } else {
            
            if (AtaIdentifyData->command_set_supported_83 & BIT10) {
                DEBUG((DEBUG_INFO,"\n 48 bit LBA addressing"));
                NumSectors = *(UINT64 *) &AtaIdentifyData->maximum_lba_for_48bit_addressing;
            } else {
                NumSectors = (UINT32)(AtaIdentifyData->user_addressable_sectors_lo + \
                                 (AtaIdentifyData->user_addressable_sectors_hi << 16));
            }
        
            // Obtain Sector Size
            if((AtaIdentifyData->phy_logic_sector_support & BIT14) && // WORD 106 valid? - BIT 14 - 1
                   (!(AtaIdentifyData->phy_logic_sector_support & BIT15)) && // WORD 106 valid? - BIT 15 - 0
                   (AtaIdentifyData->phy_logic_sector_support & BIT12)) { // WORD 106 bit 12 - Sectorsize > 256 words
                // The sector size is in words 117-118.
                StorageDeviceInfo->BlockSizeBytes = (UINT32)(AtaIdentifyData->logic_sector_size_lo + \
                                              (AtaIdentifyData->logic_sector_size_hi << 16)) * 2;
                StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.BlockSizeBytesValid = TRUE;
            } else {
                StorageDeviceInfo->BlockSizeBytes = 512;
                StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.BlockSizeBytesValid = TRUE;
            }
            
            StorageDeviceInfo->CapacityBytes = MultU64x32 (NumSectors, StorageDeviceInfo->BlockSizeBytes);
            StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.CapacityBytesValid = TRUE;
            
            if(AtaIdentifyData->nominal_media_rotation_rate == 0x0001 ) {// Indicates SSD media type
                StorageDeviceInfo->MediaType = SysInvMediaTypeSSD;
                StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.MediaTypeValid = TRUE;
            } else {
                StorageDeviceInfo->MediaType = SysInvMediaTypeHDD;
                StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.MediaTypeValid = TRUE;
                
                StorageDeviceInfo->RotationSpeedRPM = AtaIdentifyData->nominal_media_rotation_rate;
                StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.RotationSpeedRPMValid = TRUE;
            }
        }
    }
    
    // Fill SMART and TCG Security Information for an ATA device
    if(!(StorageDeviceInfo->MediaType == SysInvMediaTypeOPTICAL_DVD)) {
        // Now obtain and fill SMART data
        SetMem(Acb, sizeof(EFI_ATA_COMMAND_BLOCK), 0);
        SetMem(Asb, sizeof(EFI_ATA_STATUS_BLOCK), 0);
        SetMem(&Packet, sizeof(EFI_ATA_PASS_THRU_COMMAND_PACKET), 0);
    
        Acb->AtaDeviceHead       = (UINT8)((UINT8)(PmPort) << 4);
        Acb->AtaCommand          = ATA_CMD_SMART;
        Acb->AtaFeatures         = ATA_SMART_RETURN_STATUS;
        Acb->AtaCylinderLow      = 0x4F;
        Acb->AtaCylinderHigh     = 0xC2;
        Packet.Protocol          = EFI_ATA_PASS_THRU_PROTOCOL_ATA_NON_DATA;
        Packet.Acb               = Acb;
        Packet.Asb               = Asb;
            
        Status = AtaPassThru->PassThru(AtaPassThru,
                                       Port,
                                       PmPort,
                                       &Packet,
                                       0
                                       );
        if(!EFI_ERROR(Status)) {
            // Read the response data obtained from Device to Host FIS
            SmartD2HData = (Asb->AtaCylinderHigh << 8) + Asb->AtaCylinderLow;
            if ( SmartD2HData == ATA_SMART_THRESHOLD_NOT_EXCEEDED_VALUE ) {
                StorageDeviceInfo->StatusIndicator = SysInvStatusIndicatorOK;
                StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.StatusIndicatorValid = TRUE;
                StorageDeviceInfo->Status.State = SysInvDevEnabled;
                StorageDeviceInfo->Status.Health = SysInvHealthOK;
                StorageDeviceInfo->Status.ValidFlags.StatusVF1Param.StateValid = TRUE;
                StorageDeviceInfo->Status.ValidFlags.StatusVF1Param.HealthValid = TRUE;
            } else {
                StorageDeviceInfo->StatusIndicator = SysInvStatusIndicatorFail;
                StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.StatusIndicatorValid = TRUE;
                StorageDeviceInfo->Status.State = SysInvDevEnabled;
                StorageDeviceInfo->Status.Health = SysInvHealthCritical;
                StorageDeviceInfo->Status.ValidFlags.StatusVF1Param.StateValid = TRUE;
                StorageDeviceInfo->Status.ValidFlags.StatusVF1Param.HealthValid = TRUE;

                StorageDeviceInfo->FailurePredicted = TRUE;
                StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.FailurePredictedValid = TRUE;
            }
        }
        
        // Initialize security related data
        StorageDeviceInfo->EncryptionAbility = SysInvEncryptionAbilityNone;
        StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.EncryptionAbilityValid = TRUE;

        
        // Check for TCG support
        if ((AtaIdentifyData!= NULL) && (AtaIdentifyData->trusted_computing_support & BIT0)) {
             FillAtaTcgSecurityInfo(AtaPassThru, Port, PmPort, StorageDeviceInfo);
        }
    }
//    // As Media Life can't be calculated, filling 0xFF to denote field is invalid
//    StorageDeviceInfo->PredictedMediaLifeLeftPercent = 0xFF;
//    StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.PredictedMediaLifeLeftPercentValid = TRUE;
    
    Status = FillAtaNegotiatedSpeedInfo(ControllerHandle,Port, PmPort, StorageDeviceInfo, /*StorageStringBuffer,*/ TotalStringLength, StringIndex);
    DEBUG((DEBUG_INFO,"\n AtaNegotiatedSpeedInfo Status :%r\n", Status)); 
    
    FreePages(Acb, EFI_SIZE_TO_PAGES(sizeof(EFI_ATA_COMMAND_BLOCK)));
    FreePages(Asb, EFI_SIZE_TO_PAGES(sizeof(EFI_ATA_STATUS_BLOCK)));
    FreePages(Buffer, EFI_SIZE_TO_PAGES(BufferLength));
    
    DEBUG((DEBUG_INFO,"%a Exit \n", __FUNCTION__));
    return Status;
}

/**
  @internal
  
  Initialize and Fills the STORAGE_DEVICE_INFO for ATA Devices under Controller Handle
  
  @param [in][out] StorageControllerEntry       A pointer to the DEV_ENTRY instance of controller
  @param [in]      AtaPassThru                  Pointer to AtaPassThru protocol installed on controller handle
  @param [in]      ControllerHandle             ATA Controller handle
  @param [in]      DeviceHandle                 ATA Device handle
  
  @return  VOID
  
  @endinternal
 */

VOID
InitializeAtaStorageDevice(
  IN OUT SYSTEM_INVENTORY_INFO_PROTOCOL      *SystemInventoryInfoProtocol,
  IN OUT    DEV_ENTRY                     *StorageControllerEntry,
  IN        EFI_ATA_PASS_THRU_PROTOCOL    *AtaPassThru,
  IN        EFI_HANDLE                    ControllerHandle,
  IN        EFI_HANDLE                    DeviceHandle
  )
{
    EFI_STATUS                          Status;
    UINTN                               Seg;
    UINTN                               Bus;
    UINTN                               Dev;
    UINTN                               Fun;
    CHAR16                              *DevPathStr = NULL;
    EFI_DEVICE_PATH_PROTOCOL            *DevPath;
    EFI_DEVICE_PATH_PROTOCOL            *TempDevPath;
    EFI_DEVICE_PATH_PROTOCOL            MessagingDevicePath = { MESSAGING_DEVICE_PATH, MSG_SATA_DP };
    SATA_DEVICE_PATH                    *SataDevPath;
    STORAGE_DEVICE_INFO                 *DeviceInfo;
    DEV_ENTRY                           StorageDeviceEntry;
    EFI_PCI_IO_PROTOCOL                 *PciIo;
    UINT32                              TotalStringLength = 0;
    UINT16                              StringIndex = 1;
    CHAR8                               TempStringBuffer[FixedPcdGet32 (PcdAmiSysInvMaxStringLength)];
    UINTN                               StringLength = 0;
    
    ZeroMem (TempStringBuffer,sizeof(TempStringBuffer));


    DEBUG((DEBUG_INFO,"%a() Entry \n", __FUNCTION__));
          
    Status = gBS->HandleProtocol (
                        DeviceHandle,
                        &gEfiDevicePathProtocolGuid,
                        (VOID **)&DevPath );
    if(EFI_ERROR(Status)) {
        DEBUG ((DEBUG_ERROR, "%a()  Exit: Failed to locate DevicePath protocol Status = %r\n", __FUNCTION__, Status));
        return;
    }
        
    TempDevPath = DevPath;
    TempDevPath = GetDevicePathTypeMatch(TempDevPath, &MessagingDevicePath);
    
    if(TempDevPath == NULL) {
        DEBUG ((DEBUG_ERROR, "%a() Messaging device path not found\n", __FUNCTION__));
        return;
    }
        
    DEBUG ((DEBUG_INFO, "MSG_SATA_DP DevicePath Found\n"));
    SataDevPath = (SATA_DEVICE_PATH*) TempDevPath;
    DEBUG ((DEBUG_INFO, "DevPath Port: %x\n", SataDevPath->HBAPortNumber));
    DEBUG ((DEBUG_INFO, "DevPath PortPM: %x\n", SataDevPath->PortMultiplierPortNumber));
    
    ZeroMem(&StorageDeviceEntry, sizeof(DEV_ENTRY));
    StorageStringBuffer = AllocateZeroPool(MaxStorageStrBuffSize);
    if (StorageStringBuffer == NULL) {
        DEBUG((DEBUG_ERROR, "%a : Memory allocation failed!!!\n", __FUNCTION__));
        return;
    }
    StorageDeviceEntry.Signature = DEV_ENTRY_SIGNATURE;
    DevPathStr = ConvertDevicePathToText (DevPath, FALSE, TRUE);

    if(DevPathStr){
        StorageDeviceEntry.Dp.UefiDevPath = AllocateReservedZeroPool(StrnSizeS (DevPathStr, PcdGet32 (PcdAmiSysInvMaxStringLength)));
        if (StorageDeviceEntry.Dp.UefiDevPath != NULL)
            StrCpyS (StorageDeviceEntry.Dp.UefiDevPath, StrnSizeS (DevPathStr, PcdGet32 (PcdAmiSysInvMaxStringLength)), DevPathStr);
        FillStorageAssetTag(&StorageDeviceEntry, DevPathStr,/* StorageStringBuffer,*/ &TotalStringLength, &StringIndex);
        FreePool(DevPathStr);
    }
    
    StorageDeviceEntry.Dp.DeviceStatus.DeviceType = SysInvDevStorageDisk;
    StorageDeviceEntry.Dp.DeviceStatus.DeviceInstance = gStorageDeviceIndex;
    StorageDeviceEntry.Dp.DeviceStatus.VirtualLed = 0x01;
    StorageDeviceEntry.Dp.DeviceStatus.DeviceState = SysInvDevEnabled;
    StorageDeviceEntry.DisplayPtr.StorageDevice.Status.State = SysInvDevEnabled;
    StorageDeviceEntry.DisplayPtr.StorageDevice.Status.Health = SysInvHealthOK;
    StorageDeviceEntry.DisplayPtr.StorageDevice.Status.ValidFlags.StatusVF1Param.StateValid = 1;
    StorageDeviceEntry.DisplayPtr.StorageDevice.Status.ValidFlags.StatusVF1Param.HealthValid = 1;

    DeviceInfo = &StorageDeviceEntry.DisplayPtr.StorageDevice;
    StorageDeviceEntry.InfoSize = sizeof(STORAGE_DEVICE_INFO);
    DeviceInfo->ParentControllerIndex = StorageControllerEntry->Dp.DeviceStatus.DeviceInstance;

    
    
    Status = AddDevEntryToList(
                (SYS_INV_ITEM_LIST *)&StorageControllerEntry->DisplayPtr.StorageController.ChildDeviceIndex.DevInitialCnt,
                &gStorageDeviceIndex,
                sizeof (UINT64));
    DEBUG ((DEBUG_INFO, "Storage controller[%d]: AddDevEntryToList Child Status = %r, Index = %d\n", 
            StorageControllerEntry->Dp.DeviceStatus.DeviceInstance, Status, 
            StorageControllerEntry->DisplayPtr.StorageController.ChildDeviceIndex.DevInfoCount));
    
    
    Status = FillAtaDeviceInfo(
                AtaPassThru, 
                ControllerHandle, 
                SataDevPath->HBAPortNumber, 
                SataDevPath->PortMultiplierPortNumber, 
                DeviceInfo,
                &TotalStringLength, 
                &StringIndex );
    DEBUG((DEBUG_INFO,"FillAtaDeviceInfo: %r \n", Status));
    if (EFI_ERROR(Status)){
        if (StorageStringBuffer != NULL){
            FreePool(StorageStringBuffer);
            StorageStringBuffer = NULL;
            MaxStorageStrBuffSize = SIZE_8KB;
        }
        return;
    }
    
    StringLength = AsciiSPrint(
                        TempStringBuffer,
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        "N/A");
    if (StringLength) {
        StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
        if (StorageStringBuffer != NULL) {
            AsciiStrCpyS(
                    &StorageStringBuffer[TotalStringLength],
                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                    TempStringBuffer);
            TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
            DeviceInfo->ManufacturerStrIndex = StringIndex++;
            DeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.ManufacturerValid = TRUE;
        }
    }
    
    StringLength = AsciiSPrint(
                        TempStringBuffer,
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        "SATA_Device%X_Port%X",
                        StorageDeviceEntry.Dp.DeviceStatus.DeviceInstance, SataDevPath->HBAPortNumber);
    if (StringLength) {
        StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
        if (StorageStringBuffer != NULL) {
            AsciiStrCpyS(
                    &StorageStringBuffer[TotalStringLength],
                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                    TempStringBuffer);
            TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
            DeviceInfo->IdStrIndex = StringIndex++;
            DeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.IdValid = TRUE;
        }
        StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
        if (StorageStringBuffer != NULL) {
            AsciiStrCpyS(
                        &StorageStringBuffer[TotalStringLength],
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        TempStringBuffer);
            TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
            DeviceInfo->NameStrIndex = StringIndex++;
            DeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.NameValid = TRUE;
        }
    }
    Status = gBS->HandleProtocol (
                        ControllerHandle,
                        &gEfiPciIoProtocolGuid,
                        (VOID **)&PciIo );

    if(!EFI_ERROR(Status)){
        Status = PciIo->GetLocation (PciIo, &Seg, &Bus, &Dev, &Fun);
        if(!EFI_ERROR(Status)){
            StringLength = AsciiSPrint(
                                    TempStringBuffer,
                                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                                    "S%1X|B%2X|D%2X|F%2X_SATA_Port_%XPMPort:%X",
                                    Seg,Bus,Dev,Fun, SataDevPath->HBAPortNumber, SataDevPath->PortMultiplierPortNumber);
            if (StringLength) {
                StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                if (StorageStringBuffer != NULL) {
                    AsciiStrCpyS(
                            &StorageStringBuffer[TotalStringLength],
                            PcdGet32 (PcdAmiSysInvMaxStringLength),
                            TempStringBuffer);
                    TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                    DeviceInfo->LocationStrIndex = StringIndex++;
                    DeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.LocationValid = TRUE;
                }
            }
        }
    }

    Status = SysInvAddStrings (
                        SystemInventoryInfoProtocol,
                        &DeviceInfo->StringHdr,
                        StorageStringBuffer);
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR,"%a Failed to add string for Storage Device instance %d = %r\n", __FUNCTION__, gStorageDeviceIndex, Status));
    }  
    if (StorageStringBuffer != NULL){
        FreePool(StorageStringBuffer);
        StorageStringBuffer = NULL;
        MaxStorageStrBuffSize = SIZE_8KB;
    }
    // OEM Hook to update ATA Storage Device Inventory Data
    OemUpdateAtaStorageDeviceInventory (SystemInventoryInfoProtocol, &StorageDeviceEntry);
    
    InitializeStorageVolumesData (
                SystemInventoryInfoProtocol,
                &StorageDeviceEntry,
                DeviceHandle);
    DEBUG ((DEBUG_INFO, "SATA Device[%d]: InitializeStorageVolumesData Status = %r\n", gStorageDeviceIndex, Status));

    Status = AddDevEntryToList(
                    (SYS_INV_ITEM_LIST *)&SystemInventoryInfoProtocol->DevInitialCnt, 
                    &StorageDeviceEntry, 
                    sizeof(DEV_ENTRY) );
    
    DEBUG ((DEBUG_INFO, "SATA Device[%d]: AddDevEntryToList Status = %r, DevInfoCount = %d\n", 
            gStorageDeviceIndex, Status, SystemInventoryInfoProtocol->DevInfoCount));
    gStorageDeviceIndex++;

    if (gStorageVolumeIndex){
        SystemInventoryInfoProtocol->DataValidFlags.StorageVolumeInfoValid = TRUE;
    }

    DEBUG((DEBUG_INFO,"%a() EXIT  - Status: %r \n", __FUNCTION__, Status));
}

/**
  @internal
  
  Fills ATA/SATA controller data and append it to DevInfoList
  
  @param [in][out] PciDeviceEntry          Pointer to PCI device entry
  @param [in]      ControllerHandle        PCI device Handle

  @retval Status   Successfully updated ATA/SATA controller and drives info
  
  @endinternal
 */
EFI_STATUS
InitializeAtaStorageDevData (
  IN  OUT SYSTEM_INVENTORY_INFO_PROTOCOL      *SystemInventoryInfoProtocol,
  IN  DEV_ENTRY                     *PciDeviceEntry,
  IN  EFI_HANDLE                    ControllerHandle
  )
{
    EFI_STATUS                              Status;
    DEV_ENTRY                               StorageControllerEntry;
    STORAGE_CONTROLLER_INFO                 *ControllerInfo;
    SYS_INV_PCI_INFO                        *PciInfo;
    UINTN                                   OpenInfoCount;
    EFI_OPEN_PROTOCOL_INFORMATION_ENTRY     *OpenInfo;
    UINTN                                   InfoIndex;
    EFI_ATA_PASS_THRU_PROTOCOL              *AtaPassThru;
    EFI_NVM_EXPRESS_PASS_THRU_PROTOCOL      *NvmePassThru;
    EFI_HANDLE                              DeviceHandle;
    static UINT8                            ControllerIndex = 0;
    UINT32                                  TotalStringLength = 0;
    UINT16                                  StringIndex = 1;
    CHAR8                                   TempStringBuffer[FixedPcdGet32 (PcdAmiSysInvMaxStringLength)];
    UINTN                                   StringLength = 0;
    
    ZeroMem (TempStringBuffer,sizeof(TempStringBuffer));

    
    DEBUG((DEBUG_INFO,"%a() Entry \n", __FUNCTION__));
         
    Status = gBS->HandleProtocol(
                        ControllerHandle,
                        &gEfiAtaPassThruProtocolGuid, 
                        (VOID **)&AtaPassThru );
    
    if (EFI_ERROR(Status)) {
        DEBUG ((DEBUG_ERROR, "%a() Exit - AtaPassThru Protocol not Found: Status : %r \n", __FUNCTION__, Status));
    }

    Status = gBS->HandleProtocol(
                        ControllerHandle, 
                        &gEfiNvmExpressPassThruProtocolGuid, 
                        (VOID **)&NvmePassThru );
                        
    if (EFI_ERROR(Status)) {
        DEBUG ((DEBUG_ERROR, "%a() Exit - NvmePassThru Protocol not Found: Status : %r \n", __FUNCTION__, Status));
    }                   

    if ((AtaPassThru  == NULL) && (NvmePassThru == NULL)){
        DEBUG((DEBUG_INFO,"%a() Exit - No Pass Thru Protocol Found: Status : %r \n", __FUNCTION__, Status));
        return Status;
    }
        
    PciInfo = &PciDeviceEntry->DisplayPtr.Pci;
    ZeroMem(&StorageControllerEntry, sizeof(DEV_ENTRY));
    StorageStringBuffer = AllocateZeroPool(MaxStorageStrBuffSize);
    if (StorageStringBuffer == NULL) {
        DEBUG((DEBUG_ERROR, "%a : Memory allocation failed!!!\n", __FUNCTION__));
        return EFI_OUT_OF_RESOURCES;
    }
    
    StorageControllerEntry.Signature = DEV_ENTRY_SIGNATURE;
    
    FillDevicePath(&StorageControllerEntry, ControllerHandle);
    
    StorageControllerEntry.Dp.DeviceStatus.DeviceType = SysInvDevStorageController;
    StorageControllerEntry.Dp.DeviceStatus.DeviceInstance = gStorageControllerIndex;
    StorageControllerEntry.Dp.DeviceStatus.VirtualLed = 0x01;
    StorageControllerEntry.Dp.DeviceStatus.DeviceState = SysInvDevEnabled;
    StorageControllerEntry.DisplayPtr.StorageController.Status.State = SysInvDevEnabled;
    StorageControllerEntry.DisplayPtr.StorageController.Status.Health = SysInvHealthOK;
    StorageControllerEntry.DisplayPtr.StorageController.StorageIndex = gStorageUnitIndex;
    StorageControllerEntry.InfoSize = sizeof(STORAGE_CONTROLLER_INFO);
    StorageControllerEntry.DisplayPtr.StorageController.Status.ValidFlags.StatusVF1Param.StateValid = 1;
    StorageControllerEntry.DisplayPtr.StorageController.Status.ValidFlags.StatusVF1Param.HealthValid = 1;
    StorageControllerEntry.DisplayPtr.StorageController.Flags1.StorageControllerInfoVF1Param.StorageIndexValid = 1;
    
    PciInfo->StorageControllerIndex = StorageControllerEntry.Dp.DeviceStatus.DeviceInstance;
    PciInfo->NetworkControllerIndex = (UINTN)0xFFFFFFFF;
    PciInfo->Flags1.PcieDeviceFV1Param.StorageControllerIndexValid = 1;
    PciInfo->Flags1.PcieDeviceFV1Param.NetworkControllerIndexValid = 1;
    
    ControllerInfo = &StorageControllerEntry.DisplayPtr.StorageController;
    ControllerInfo->ParentPciIndex = PciDeviceEntry->Dp.DeviceStatus.DeviceInstance;
    ControllerInfo->Flags1.StorageControllerInfoVF1Param.ParentPciIndexValid = 1;
    
    StringLength =  AsciiSPrint(
                        TempStringBuffer,
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        "(SATA_Controller_%X)",
                        gStorageControllerIndex);
    if (StringLength) {
        StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
        if (StorageStringBuffer != NULL) {
            AsciiStrCpyS(
                    &StorageStringBuffer[TotalStringLength],
                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                    TempStringBuffer);
            TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], 
                                                         PcdGet32 (PcdAmiSysInvMaxStringLength));
            ControllerInfo->AssetTagStrIndex = StringIndex++;
            ControllerInfo->Flags1.StorageControllerInfoVF1Param.AssetTagValid = TRUE;
        }
    }

    
    StringLength = AsciiSPrint(
                        TempStringBuffer,
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        "%X",
                        StorageControllerEntry.Dp.DeviceStatus.DeviceInstance);
    if (StringLength) {
        StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
        if (StorageStringBuffer != NULL) {
            AsciiStrCpyS(
                    &StorageStringBuffer[TotalStringLength],
                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                    TempStringBuffer);
            TotalStringLength += (UINT32)AsciiStrnSizeS (
                                            &StorageStringBuffer[TotalStringLength], 
                                            PcdGet32 (PcdAmiSysInvMaxStringLength) );
            ControllerInfo->MemberIdStrIndex = StringIndex++;
            ControllerInfo->Flags1.StorageControllerInfoVF1Param.MemberIdValid = TRUE;
        }
    }
    
    StringLength = AsciiSPrint(
                        TempStringBuffer,
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        "SATA_Controller_%X",
                        StorageControllerEntry.Dp.DeviceStatus.DeviceInstance);
    if (StringLength) {
        StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
        if (StorageStringBuffer != NULL) {
            AsciiStrCpyS(
                    &StorageStringBuffer[TotalStringLength],
                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                    TempStringBuffer);
            TotalStringLength += (UINT32)AsciiStrnSizeS (
                                            &StorageStringBuffer[TotalStringLength], 
                                            PcdGet32 (PcdAmiSysInvMaxStringLength) );
            ControllerInfo->NameStrIndex = StringIndex++;
            ControllerInfo->Flags1.StorageControllerInfoVF1Param.NameValid = TRUE;
        }
    }
    
    if (PciDeviceEntry->DisplayPtr.Pci.PcieFunctions.Class.ClassCode == PCI_CLASS_MASS_STORAGE_RAID) {
        
        StringLength = AsciiSPrint(
                            TempStringBuffer,
                            PcdGet32 (PcdAmiSysInvMaxStringLength),
                            "RAID_%X",
                            StorageControllerEntry.Dp.DeviceStatus.DeviceInstance);
        if (StringLength) {
            StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
            if (StorageStringBuffer != NULL) {
                AsciiStrCpyS(
                        &StorageStringBuffer[TotalStringLength],
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        TempStringBuffer);
                TotalStringLength += (UINT32)AsciiStrnSizeS (
                                                &StorageStringBuffer[TotalStringLength], 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength) );
                ControllerInfo->DescriptionStrIndex = StringIndex++;
                ControllerInfo->Flags1.StorageControllerInfoVF1Param.DescriptionValid = TRUE;
            }
        }
    }

    Status = InitializeAtaStorageControllerData (
                    AtaPassThru, 
                    ControllerHandle, 
                    ControllerInfo, 
                    &TotalStringLength, 
                    &StringIndex );

    if (EFI_ERROR(Status)){
        DEBUG((DEBUG_INFO,"%a() Exit - Failed to Initializing Controller Data: Status: %r \n", 
                __FUNCTION__, Status));
        return Status;
    }

    Status = SysInvAddStrings (
                        SystemInventoryInfoProtocol,
                        &ControllerInfo->StringHdr,
                        StorageStringBuffer);
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR,"%a Failed to add string for Storage Controller instance %d = %r\n", __FUNCTION__, gStorageControllerIndex, Status));
    }
    if (StorageStringBuffer != NULL){
        FreePool(StorageStringBuffer);
        StorageStringBuffer = NULL;
        MaxStorageStrBuffSize = SIZE_8KB;
    }
    // OEM Hook to update ATA Storage Controller Inventory Data
    OemUpdateAtaStorageControllerInventory (SystemInventoryInfoProtocol, &StorageControllerEntry);

    if (AtaPassThru) {
        Status = gBS->OpenProtocolInformation(
                                    ControllerHandle,
                                    &gEfiIdeControllerInitProtocolGuid,
                                    &OpenInfo,
                                    &OpenInfoCount );
        
        if(!EFI_ERROR(Status)){ 
            for (InfoIndex = 0; InfoIndex < OpenInfoCount; InfoIndex++) {
                if (OpenInfo[InfoIndex].Attributes & EFI_OPEN_PROTOCOL_BY_CHILD_CONTROLLER){
                    DeviceHandle = OpenInfo[InfoIndex].ControllerHandle;  //disk/ssd Drive handle
                    InitializeAtaStorageDevice(
                                SystemInventoryInfoProtocol,
                                &StorageControllerEntry,
                                AtaPassThru,
                                ControllerHandle,
                                DeviceHandle );
                }
            }
            FreePool (OpenInfo);
        }    
    }
    
    if(NvmePassThru){
        Status = gBS->OpenProtocolInformation(
                                    ControllerHandle,
                                    &gEfiPciIoProtocolGuid,
                                    &OpenInfo,
                                    &OpenInfoCount );
        
        if(!EFI_ERROR(Status)) {
            for (InfoIndex = 0; InfoIndex < OpenInfoCount; InfoIndex++) {
                if (OpenInfo[InfoIndex].Attributes & EFI_OPEN_PROTOCOL_BY_CHILD_CONTROLLER){
                    DeviceHandle = OpenInfo[InfoIndex].ControllerHandle;  //disk/ssd Drive handle
                    FillNvmeNamespaceData (
                                SystemInventoryInfoProtocol,
                                &StorageControllerEntry,
                                NvmePassThru,
                                ControllerHandle,
                                DeviceHandle,
                                TRUE );
                }
            }
            FreePool (OpenInfo);
        }
    }

    Status = AddDevEntryToList((SYS_INV_ITEM_LIST *)&SystemInventoryInfoProtocol->DevInitialCnt, &StorageControllerEntry, sizeof(DEV_ENTRY));
    DEBUG ((DEBUG_INFO, "SATA Controller[%d]: AddDevEntryToList Status = %r, DevInfoCount = %d\n", 
            gStorageControllerIndex, Status, SystemInventoryInfoProtocol->DevInfoCount));
    gStorageControllerIndex++;
    ControllerIndex++;
    
    if(gStorageControllerIndex) {
        SystemInventoryInfoProtocol->DataValidFlags.StorageControllerInfoValid = TRUE;
    }
    
    if (gStorageDeviceIndex) {
        SystemInventoryInfoProtocol->DataValidFlags.StorageDeviceInfoValid = TRUE;
    }

    DEBUG((DEBUG_INFO,"%a() Exit \n", __FUNCTION__));
    return Status;
}

/**
  @internal
  
  Fills Nvme controller/device and Volume data and append it to DevInfoList
 
  @param [in][out] PciDeviceEntry          Pointer to PCI device entry
  @param [in]      ControllerHandle        PCI device Handle

  @retval Status   EFI_SUCCESS: Successfully updated Nvme Controller and drives info
  
  @endinternal
**/

EFI_STATUS
InitializeNvmeStorageDevData (
  IN  OUT SYSTEM_INVENTORY_INFO_PROTOCOL      *SystemInventoryInfoProtocol,
  IN  DEV_ENTRY                     *PciDeviceEntry,
  IN  EFI_HANDLE                    ControllerHandle
  )
{
    EFI_STATUS                              Status;
    EFI_NVM_EXPRESS_PASS_THRU_PROTOCOL      *NvmePassThru;
    EFI_PCI_IO_PROTOCOL                     *PciIo;
    DEV_ENTRY                               StorageControllerEntry;
    STORAGE_CONTROLLER_INFO                 *ControllerDisplayInfo;
    UINT32                                  PcieLinkCab = 0xFF;
    UINT8                                   LinkSpeed;
    UINT8                                   LinkWidth;
    UINT8                                   CapIdPtr;
    static UINT8                            ControllerIndex = 0;
    SYS_INV_PCI_INFO                        *PciInfo;
    UINTN                                   OpenInfoCount;
    EFI_OPEN_PROTOCOL_INFORMATION_ENTRY     *OpenInfo;
    UINTN                                   InfoIndex;
    EFI_HANDLE                              DeviceHandle;
    UINTN                                   ProtocolIndex;
    EFI_GUID                                **ProtocolGuidArray;
    UINTN                                   ArrayCount;
    UINT32                                  TotalStringLength = 0;
    UINT16                                  StringIndex = 1;
    CHAR8                                   TempStringBuffer[FixedPcdGet32 (PcdAmiSysInvMaxStringLength)];
    UINTN                                   StringLength = 0;
    
    ZeroMem (TempStringBuffer,sizeof(TempStringBuffer));

    
    DEBUG((DEBUG_INFO,"%a() Entry \n", __FUNCTION__));
    
    Status = gBS->HandleProtocol (
                        ControllerHandle,
                        &gEfiNvmExpressPassThruProtocolGuid, 
                        (VOID **)&NvmePassThru );
    
    if(EFI_ERROR(Status)) {
        return Status;
    }
    
    //
    // Allocate Aligned buffer for the PassThru Command Packet Command Buffer and Completion Buffer
    //
    gNvmeCommandPacket       = AllocateAlignedPages (EFI_SIZE_TO_PAGES(sizeof(EFI_NVM_EXPRESS_PASS_THRU_COMMAND_PACKET)), 
                                                       NvmePassThru->Mode->IoAlign);
        
    gNvmeCompletion          = AllocateAlignedPages (EFI_SIZE_TO_PAGES(sizeof(EFI_NVM_EXPRESS_COMPLETION)), 
                                                   NvmePassThru->Mode->IoAlign);
    
    gNvmeCmd                 = AllocateAlignedPages (EFI_SIZE_TO_PAGES(sizeof(EFI_NVM_EXPRESS_COMMAND)), 
                                                       NvmePassThru->Mode->IoAlign);
        
    PciInfo = &PciDeviceEntry->DisplayPtr.Pci;
    SetMem(&StorageControllerEntry, sizeof(DEV_ENTRY), 0);
    StorageStringBuffer = AllocateZeroPool(MaxStorageStrBuffSize);
    if (StorageStringBuffer == NULL) {
        DEBUG((DEBUG_ERROR, "%a : Memory allocation failed!!!\n", __FUNCTION__));
        return EFI_OUT_OF_RESOURCES;
    }
    
    StorageControllerEntry.Signature = DEV_ENTRY_SIGNATURE;
    
    FillDevicePath(&StorageControllerEntry, ControllerHandle);
    
    StorageControllerEntry.Dp.DeviceStatus.DeviceType = SysInvDevStorageController;
    StorageControllerEntry.Dp.DeviceStatus.DeviceInstance = gStorageControllerIndex;
    StorageControllerEntry.Dp.DeviceStatus.VirtualLed = 0x01;
    StorageControllerEntry.Dp.DeviceStatus.DeviceState = SysInvDevEnabled;
    StorageControllerEntry.DisplayPtr.StorageController.Status.State = SysInvDevEnabled;
    StorageControllerEntry.DisplayPtr.StorageController.Status.Health = SysInvHealthOK;
    StorageControllerEntry.DisplayPtr.StorageController.StorageIndex = gStorageUnitIndex;
    StorageControllerEntry.InfoSize = sizeof(STORAGE_CONTROLLER_INFO);
    
    StorageControllerEntry.DisplayPtr.StorageController.Status.ValidFlags.StatusVF1Param.StateValid = 1;
    StorageControllerEntry.DisplayPtr.StorageController.Status.ValidFlags.StatusVF1Param.HealthValid = 1;
    StorageControllerEntry.DisplayPtr.StorageController.Flags1.StorageControllerInfoVF1Param.StorageIndexValid = 1;

    PciInfo->StorageControllerIndex = StorageControllerEntry.Dp.DeviceStatus.DeviceInstance;
    PciInfo->NetworkControllerIndex = (UINTN)0xFFFFFFFF;
    PciInfo->Flags1.PcieDeviceFV1Param.StorageControllerIndexValid = 1;
    PciInfo->Flags1.PcieDeviceFV1Param.NetworkControllerIndexValid = 1;

    ControllerDisplayInfo = &StorageControllerEntry.DisplayPtr.StorageController;
    ControllerDisplayInfo->ParentPciIndex = PciDeviceEntry->Dp.DeviceStatus.DeviceInstance;
    ControllerDisplayInfo->Flags1.StorageControllerInfoVF1Param.ParentPciIndexValid = 1;
    
    
    StringLength = AsciiSPrint(
                        TempStringBuffer,
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        "(NVMe_Controller_%X)",
                        gStorageControllerIndex);
    if (StringLength) {
        StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
        if (StorageStringBuffer != NULL) {
            AsciiStrCpyS(
                    &StorageStringBuffer[TotalStringLength],
                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                    TempStringBuffer);
            TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
            ControllerDisplayInfo->AssetTagStrIndex = StringIndex++;
            ControllerDisplayInfo->Flags1.StorageControllerInfoVF1Param.AssetTagValid = TRUE;
        }
    }

    Status = gBS->HandleProtocol (
                        ControllerHandle,
                        &gEfiPciIoProtocolGuid,
                        (VOID **)&PciIo );
    if(EFI_ERROR(Status)) 
        return Status;
    
    // Update StringId. This will be unique REDFISH ID String for this ATA Device.
    StringLength = AsciiSPrint(
                        TempStringBuffer,
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        "%X",
                        StorageControllerEntry.Dp.DeviceStatus.DeviceInstance);
    if (StringLength) {
        StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
        if (StorageStringBuffer != NULL) {
            AsciiStrCpyS(
                    &StorageStringBuffer[TotalStringLength],
                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                    TempStringBuffer);
            TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
            ControllerDisplayInfo->MemberIdStrIndex = StringIndex++;
            ControllerDisplayInfo->Flags1.StorageControllerInfoVF1Param.MemberIdValid = TRUE;
        }
    }
 
    StringLength = AsciiSPrint(
                        TempStringBuffer,
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        "NVMe_Controller_%X",
                        StorageControllerEntry.Dp.DeviceStatus.DeviceInstance);
    if (StringLength) {
        StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
        if (StorageStringBuffer != NULL) {
            AsciiStrCpyS(
                    &StorageStringBuffer[TotalStringLength],
                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                    TempStringBuffer);
            TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
            ControllerDisplayInfo->NameStrIndex = StringIndex++;
            ControllerDisplayInfo->Flags1.StorageControllerInfoVF1Param.NameValid = TRUE;
        }
    }
    // Get Current Link Speed
    CapIdPtr = GetPcieCapabilityIdPtr (PciIo, EFI_PCI_CAPABILITY_ID_PCIEXP);
    
    if(CapIdPtr) {
        PciIo->Pci.Read (
                    PciIo,
                    EfiPciIoWidthUint32,
                    CapIdPtr + PCIE_LINK_CAPABILITIES_OFFSET,
                    1,
                    &PcieLinkCab );
        
        LinkSpeed = PcieLinkCab & 0xF;
        
        //Converted the units of SpeedGbps from GT/s to Gbit/s.
        LinkWidth = (PcieLinkCab >> 4) & 0x1F;        
        switch (LinkSpeed) {
            case PCIE_LINK_SPEED_VECT_25G:
                switch (LinkWidth) {
                    case 1:
                        StringLength = AsciiSPrint(
                                        TempStringBuffer, 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                        "2");
                        break;
                    case 4:
                        StringLength = AsciiSPrint(
                                        TempStringBuffer, 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                        "8");
                        break;
                    case 8:
                        StringLength = AsciiSPrint(
                                        TempStringBuffer, 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                        "16");
                        break;
                    case 16:
                        StringLength = AsciiSPrint(
                                        TempStringBuffer, 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                        "32");
                        break;
                    default:
                        StringLength = AsciiSPrint(
                                        TempStringBuffer, 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                        "N/A");
                        break;
                }
                break;
            case PCIE_LINK_SPEED_VECT_50G:
                switch (LinkWidth) {
                    case 1:
                        StringLength = AsciiSPrint(
                                        TempStringBuffer, 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                        "4");
                        break;
                    case 4:
                        StringLength = AsciiSPrint(
                                        TempStringBuffer, 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                        "16");
                        break;
                    case 8:
                        StringLength = AsciiSPrint(
                                        TempStringBuffer, 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                        "32");
                        break;
                    case 16:
                        StringLength = AsciiSPrint(
                                        TempStringBuffer, 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                        "64");
                        break;
                    default:
                        StringLength = AsciiSPrint(
                                        TempStringBuffer, 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                        "N/A");
                        break;
                }
                break;
            case PCIE_LINK_SPEED_80G:
                switch (LinkWidth) {
                    case 1:
                        StringLength = AsciiSPrint(
                                        TempStringBuffer, 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                        "7.88");
                        break;
                    case 4:
                        StringLength = AsciiSPrint(
                                        TempStringBuffer, 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                        "31.51");
                        break;
                    case 8:
                        StringLength = AsciiSPrint(
                                        TempStringBuffer, 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                        "63.02");
                        break;
                    case 16:
                        StringLength = AsciiSPrint(
                                        TempStringBuffer, 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                        "126.03");
                        break;
                    default:
                        StringLength = AsciiSPrint(
                                        TempStringBuffer, 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                        "N/A");
                        break;
                }
                break;
            case PCIE_LINK_SPEED_16G:
                switch (LinkWidth) {
                    case 1:
                        StringLength = AsciiSPrint(
                                        TempStringBuffer, 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                        "15.75");
                        break;
                    case 4:
                        StringLength = AsciiSPrint(
                                        TempStringBuffer, 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                        "63.02");
                        break;
                    case 8:
                        StringLength = AsciiSPrint(
                                        TempStringBuffer, 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                        "126.03");
                        break;
                    case 16:
                        StringLength = AsciiSPrint(
                                        TempStringBuffer, 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                        "252.06");
                        break;
                    default:
                        StringLength = AsciiSPrint(
                                        TempStringBuffer, 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                        "N/A");
                        break;
                }
                break;
//[COMPAL_CHANGES]+>>
            case PCIE_LINK_SPEED_32G:
                switch (LinkWidth) {
                    case 1:
                        StringLength = AsciiSPrint(
                                        TempStringBuffer, 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                        "31.51");
                        break;
                    case 4:
                        StringLength = AsciiSPrint(
                                        TempStringBuffer, 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                        "126.03");
                        break;
                    case 8:
                        StringLength = AsciiSPrint(
                                        TempStringBuffer, 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                        "252.06");
                        break;
                    case 16:
                        StringLength = AsciiSPrint(
                                        TempStringBuffer, 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                        "504.12");
                        break;
                    default:
                        StringLength = AsciiSPrint(
                                        TempStringBuffer, 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                        "N/A");
                        break;
                    }
                break;
//[COMPAL_CHANGES]+<<
            default:
                StringLength = AsciiSPrint(
                                    TempStringBuffer,
                                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                                    "N/A");
        }
        if (StringLength) {
            StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
            if (StorageStringBuffer != NULL) {
                AsciiStrCpyS(
                        &StorageStringBuffer[TotalStringLength],
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        TempStringBuffer);
                TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                ControllerDisplayInfo->SpeedGbpsStrIndex = StringIndex++;
                ControllerDisplayInfo->Flags1.StorageControllerInfoVF1Param.SpeedGbpsValid = TRUE;
            }
        }
    }
    
    if(gNvmeCommandPacket != NULL && gNvmeCompletion != NULL && gNvmeCmd != NULL) {
    
        InitializeNvmeStorageControllerData(
                                        NvmePassThru,
                                        ControllerHandle,
                                        ControllerDisplayInfo,
                                        //StorageStringBuffer,
                                        &TotalStringLength,
                                        &StringIndex);
        
        Status = SysInvAddStrings (
                            SystemInventoryInfoProtocol,
                            &ControllerDisplayInfo->StringHdr,
                            StorageStringBuffer);
        if (EFI_ERROR(Status)) {
            DEBUG((DEBUG_ERROR,"%a Failed to add string for Storage Controller instance %d = %r\n", __FUNCTION__, gStorageControllerIndex, Status));
        }            
        if (StorageStringBuffer != NULL){
            FreePool(StorageStringBuffer);
            StorageStringBuffer = NULL;
            MaxStorageStrBuffSize = SIZE_8KB;
        }
        // OEM Hook to update NVME Storage Controller Inventory Data
        OemUpdateNvmeStorageControllerInventory (SystemInventoryInfoProtocol, &StorageControllerEntry);
//[COMPAL_CHANGES]+>>
        CompalUpdateNvmeStorageControllerInventoryData (SystemInventoryInfoProtocol, &StorageControllerEntry, PciIo);
//[COMPAL_CHANGES]+<<
        
        //
        // Retrieve the list of all the protocols on ControllerHandle
        //
        Status = gBS->ProtocolsPerHandle (
                            ControllerHandle,
                            &ProtocolGuidArray,
                            &ArrayCount );
        
        if (!EFI_ERROR (Status)) {
            
            for (ProtocolIndex = 0; ProtocolIndex < ArrayCount; ProtocolIndex++) {
                
                //
                // Get Open protocol information for all the protocols installed on ControllerHandle
                //
                Status = gBS->OpenProtocolInformation (
                                            ControllerHandle,
                                            ProtocolGuidArray[ProtocolIndex],
                                            &OpenInfo,
                                            &OpenInfoCount );
              
                DEBUG((DEBUG_ERROR,"OpenProtocolInfo Status for [%g]: %r\n", ProtocolGuidArray[ProtocolIndex], Status));
              
                if(!EFI_ERROR(Status)) {
                    
                    DEBUG((DEBUG_ERROR,"OpenInfoCount: %d\n", OpenInfoCount));
                    
                    for (InfoIndex = 0; InfoIndex < OpenInfoCount; InfoIndex++) {
                        
                        if (OpenInfo[InfoIndex].Attributes & EFI_OPEN_PROTOCOL_BY_CHILD_CONTROLLER) {
                            DEBUG ((DEBUG_INFO, "Child OpenInfoIndex: [%d]\n", InfoIndex));
                            DeviceHandle = OpenInfo[InfoIndex].ControllerHandle;  //NVMe name space handle
                            FillNvmeNamespaceData (
                                        SystemInventoryInfoProtocol,
                                        &StorageControllerEntry,
                                        NvmePassThru,
                                        ControllerHandle,
                                        DeviceHandle,
                                        FALSE );
                        }
                    }
                    FreePool (OpenInfo);
                }
            }
            FreePool (ProtocolGuidArray);
        }
    }

    Status = AddDevEntryToList((SYS_INV_ITEM_LIST *)&SystemInventoryInfoProtocol->DevInitialCnt, &StorageControllerEntry, sizeof(DEV_ENTRY));
    DEBUG ((DEBUG_INFO, "Nvme Controller[%d]: AddDevEntryToList Status = %r, DevInfoCount = %d\n", 
            gStorageControllerIndex, Status, SystemInventoryInfoProtocol->DevInfoCount));
    gStorageControllerIndex++;
    ControllerIndex++;
    
    if(gStorageControllerIndex) {
        SystemInventoryInfoProtocol->DataValidFlags.StorageControllerInfoValid = TRUE;
    }
    
    if (gStorageDeviceIndex) {
        SystemInventoryInfoProtocol->DataValidFlags.StorageDeviceInfoValid = TRUE;
    }
    
    FreePages(gNvmeCommandPacket, EFI_SIZE_TO_PAGES(sizeof(EFI_NVM_EXPRESS_PASS_THRU_COMMAND_PACKET)));
    FreePages(gNvmeCompletion, EFI_SIZE_TO_PAGES(sizeof(EFI_NVM_EXPRESS_COMPLETION)));
    FreePages(gNvmeCmd, EFI_SIZE_TO_PAGES(sizeof(EFI_NVM_EXPRESS_COMMAND)));
    
    DEBUG((DEBUG_INFO,"%a() Exit \n", __FUNCTION__));
    return Status;
}

/**
  @internal
  
  Fills USB Storage Devices data
  
  @param  SystemInventoryInfoProtocol   Pointer to SystemInventoryInfoProtocol
  @param  StorageControllerEntry   Pointer to Storage Controller DEV_ENTRY
  @param  PciIo                    Pointer to PciIo protocol
  @param  Handle                   Device Handle
  @param  AgentHandle              Agent Handle
  
  @return VOID
  
  @endinternal
**/
VOID
FillUsbDeviceInfo (
  IN OUT SYSTEM_INVENTORY_INFO_PROTOCOL     *SystemInventoryInfoProtocol,
  IN  OUT  DEV_ENTRY                        *StorageControllerEntry,
  IN       EFI_PCI_IO_PROTOCOL              *PciIo,
  IN       EFI_HANDLE                       DeviceHandle,
  IN       EFI_HANDLE                       AgentHandle
  )
{
    EFI_STATUS                          Status;
    DEV_ENTRY                           StorageDeviceEntry;
    STORAGE_DEVICE_INFO                 *StorageDeviceInfo;
    EFI_USB_IO_PROTOCOL                 *UsbIo;
    EFI_USB_DEVICE_DESCRIPTOR           DevDesc;
    EFI_USB_INTERFACE_DESCRIPTOR        InterfaceDesc;
    EFI_BLOCK_IO_PROTOCOL               *BlockIo;
    EFI_DEVICE_PATH_PROTOCOL            *DevPath;
    EFI_DEVICE_PATH_PROTOCOL            *TempDevPath;
    EFI_DEVICE_PATH_PROTOCOL            MessagingDevicePath = { MESSAGING_DEVICE_PATH, MSG_USB_DP };
    USB_DEVICE_PATH                     *UsbDevPath;
    CHAR16                              *Data = NULL;
    CHAR8                               ModelNameStr[FixedPcdGet32 (PcdAmiSysInvMaxStringLength)]; 
    CHAR8                               SerialNoStr[FixedPcdGet32 (PcdAmiSysInvMaxStringLength)];
    CHAR8                               Manufacturer[FixedPcdGet32 (PcdAmiSysInvMaxStringLength)];
    UINT16                              UsbRev;
    UINT64                              DriveSizeInBytes;
    UINT64                              DeviceCapacity;
    UINTN                               Seg;
    UINTN                               Bus;
    UINTN                               Dev;
    UINTN                               Fun;
    CHAR16                              *DevPathStr = NULL;
    UINT32                              TotalStringLength = 0;
    UINT16                              StringIndex = 1;
    CHAR8                               TempStringBuffer[FixedPcdGet32 (PcdAmiSysInvMaxStringLength)];
    UINTN                               StringLength = 0;
    
    ZeroMem (TempStringBuffer,sizeof(TempStringBuffer));
    ZeroMem(ModelNameStr, sizeof(ModelNameStr));
    ZeroMem(SerialNoStr, sizeof(SerialNoStr));
    ZeroMem(Manufacturer, sizeof(Manufacturer));
    
    // Check if it is a storage device 
    Status = gBS->HandleProtocol (
                        DeviceHandle,
                        &gEfiBlockIoProtocolGuid,
                        (VOID **)&BlockIo );
    
    if(EFI_ERROR(Status)) // Not a storage device
        return;
    
    ZeroMem(&StorageDeviceEntry, sizeof(DEV_ENTRY));
    StorageStringBuffer = AllocateZeroPool(MaxStorageStrBuffSize);
    if (StorageStringBuffer == NULL) {
        DEBUG((DEBUG_ERROR, "%a : Memory allocation failed!!!\n", __FUNCTION__));
        return;
    }
    
    StorageDeviceEntry.Signature = DEV_ENTRY_SIGNATURE;
    StorageDeviceEntry.Dp.DeviceStatus.DeviceType = SysInvDevStorageDisk;
    StorageDeviceEntry.Dp.DeviceStatus.DeviceInstance = gStorageDeviceIndex;
    StorageDeviceEntry.Dp.DeviceStatus.VirtualLed = 0x01;
    StorageDeviceEntry.Dp.DeviceStatus.DeviceState = SysInvDevEnabled;
    StorageDeviceEntry.DisplayPtr.StorageDevice.Status.State = SysInvDevEnabled;
    StorageDeviceEntry.DisplayPtr.StorageDevice.Status.Health = SysInvHealthOK;
    StorageDeviceEntry.DisplayPtr.StorageDevice.Status.ValidFlags.StatusVF1Param.StateValid = 1;
    StorageDeviceEntry.DisplayPtr.StorageDevice.Status.ValidFlags.StatusVF1Param.HealthValid = 1;

    
    StorageDeviceInfo = &StorageDeviceEntry.DisplayPtr.StorageDevice;
    StorageDeviceEntry.InfoSize = sizeof(STORAGE_DEVICE_INFO);

    StorageDeviceInfo->ParentControllerIndex = StorageControllerEntry->Dp.DeviceStatus.DeviceInstance;
    StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.ParentControllerIndexValid = 1;

    
    
    Status = AddDevEntryToList(
                (SYS_INV_ITEM_LIST *)&StorageControllerEntry->DisplayPtr.StorageController.ChildDeviceIndex.DevInitialCnt,
                &gStorageDeviceIndex,
                sizeof (UINT64));
    DEBUG ((DEBUG_INFO, "Storage controller[%d]: AddDevEntryToList Child Status = %r, Index = %d\n", 
            StorageControllerEntry->Dp.DeviceStatus.DeviceInstance, Status, 
            StorageControllerEntry->DisplayPtr.StorageController.ChildDeviceIndex.DevInfoCount));
    
    
    //Get USB Serial Number from Device descriptor
    Status = gBS->HandleProtocol(
                            DeviceHandle,
                            &gEfiUsbIoProtocolGuid,
                            (VOID**)&UsbIo );
    if(!EFI_ERROR(Status)) {
        
        StorageDeviceInfo->Protocol = SysInvProtocolUSB;
        StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.ProtocolValid = TRUE;
        
        StorageDeviceInfo->EncryptionAbility = SysInvEncryptionAbilityNone;
        StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.EncryptionAbilityValid = TRUE;
        
        
        Status = UsbIo->UsbGetDeviceDescriptor(UsbIo, &DevDesc);
        DEBUG ((DEBUG_INFO, "UsbIo->UsbGetDeviceDescriptor Status = %r \n", Status));
        
        if (DevDesc.StrSerialNumber) {
            Status = UsbIo->UsbGetStringDescriptor(
                                        UsbIo,
                                        0x0409,
                                        DevDesc.StrSerialNumber,
                                        &Data);
            if(!EFI_ERROR(Status)) {
                if (CheckForNonAsciiChar(Data)) { // WA for Assert issue with non ASCII data.
                    Status = UnicodeStrToAsciiStrS (Data, SerialNoStr, sizeof(SerialNoStr));
                    if (!EFI_ERROR(Status)) {
                        StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,AsciiStrLen (SerialNoStr),TotalStringLength);
                        if (StorageStringBuffer != NULL) {
                            DEBUG ((DEBUG_INFO, " SerialNo = %a  \n", SerialNoStr));
                            AsciiStrCpyS(
                                        &StorageStringBuffer[TotalStringLength],
                                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                                        SerialNoStr);
                            TotalStringLength += (UINT32)AsciiStrnSizeS (
                                                            &StorageStringBuffer[TotalStringLength], 
                                                            PcdGet32 (PcdAmiSysInvMaxStringLength) );
                            StorageDeviceInfo->SerialNumberStrIndex = StringIndex++;
                            StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.SerialNumberValid = TRUE;
                        }
                    }
                }
                FreePool (Data);
            }
        }
        
        if (DevDesc.StrManufacturer) {
            Status = UsbIo->UsbGetStringDescriptor(
                                        UsbIo,
                                        0x0409,
                                        DevDesc.StrManufacturer,
                                        &Data );
            
            if(!EFI_ERROR(Status)) {
                Status = UnicodeStrToAsciiStrS (Data, Manufacturer, sizeof(Manufacturer));
                if (!EFI_ERROR(Status)) {
                    DEBUG ((DEBUG_INFO, " Manufacturer = %a  \n", Manufacturer));
                    StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,AsciiStrLen (Manufacturer),TotalStringLength);
                    if (StorageStringBuffer != NULL) {
                        AsciiStrCpyS(
                                    &StorageStringBuffer[TotalStringLength],
                                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                                    Manufacturer);
                        TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                        StorageDeviceInfo->ManufacturerStrIndex = StringIndex++;
                        StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.ManufacturerValid = TRUE;
                    }
                }
                FreePool (Data);
            }
        }
        
        if (DevDesc.StrProduct) {
            Status = UsbIo->UsbGetStringDescriptor(
                                        UsbIo,
                                        0x0409,
                                        DevDesc.StrProduct,
                                        &Data );
            
            if(!EFI_ERROR(Status)) {
                Status = UnicodeStrToAsciiStrS (Data, ModelNameStr, sizeof(ModelNameStr));
                if (!EFI_ERROR(Status)) {
                    DEBUG ((DEBUG_INFO, " Model Number = %a  \n", ModelNameStr));
                    StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,AsciiStrLen (ModelNameStr),TotalStringLength);
                    if (StorageStringBuffer != NULL) {
                        AsciiStrCpyS(
                                    &StorageStringBuffer[TotalStringLength],
                                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                                    ModelNameStr);
                        TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                        StorageDeviceInfo->ModelStrIndex = StringIndex++;
                        StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.ModelValid = TRUE;
                    }
                }
                FreePool (Data);
            }
        } else if (AgentHandle && gUsbCtrlHandle){
            // Fill Model name with help of ComponentName2Protocol.
            EFI_COMPONENT_NAME2_PROTOCOL        *ComponentName2Protocol;
            CHAR16                        *ControllerName = NULL;
            Status = gBS->HandleProtocol (
                            AgentHandle,
                            &gEfiComponentName2ProtocolGuid,
                            (VOID **)&ComponentName2Protocol);
            DEBUG ((DEBUG_INFO, " Status %r ComponentName2Protocol \n", Status));
            if (!EFI_ERROR(Status)) {
                Status = ComponentName2Protocol->GetControllerName (
                                ComponentName2Protocol,
                                gUsbCtrlHandle,
                                DeviceHandle,
                                ComponentName2Protocol->SupportedLanguages,
                                &ControllerName
                            );
                if (!EFI_ERROR(Status)) {
                    Status = UnicodeStrToAsciiStrS (ControllerName, ModelNameStr, sizeof(ModelNameStr));
                }
                if (!EFI_ERROR(Status) && AsciiStrLen (ModelNameStr)) {
                    DEBUG ((DEBUG_INFO, " Model Number = %a  \n", ModelNameStr));
                    StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,AsciiStrLen (ModelNameStr),TotalStringLength);
                    if (StorageStringBuffer != NULL) {
                        AsciiStrCpyS(
                                &StorageStringBuffer[TotalStringLength],
                                PcdGet32 (PcdAmiSysInvMaxStringLength),
                                ModelNameStr);
                        TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                        StorageDeviceInfo->ModelStrIndex = StringIndex++;
                        StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.ModelValid = TRUE;
                    }
                }
                DEBUG ((DEBUG_INFO, " Status %r ModelNameStr  = %a  \n", Status ,ModelNameStr));
            }
        }
        
        if (DevDesc.BcdUSB) {
            UsbRev = DevDesc.BcdUSB;
            DEBUG ((DEBUG_INFO, " Revision = %X  \n", UsbRev));
            StringLength = AsciiSPrint(
                                TempStringBuffer,
                                PcdGet32 (PcdAmiSysInvMaxStringLength),
                                "USB%x.%x%x",
                                (UINT8)(UsbRev >> 8), (UINT8) UsbRev >> 4, (UsbRev&0x0F));
            if (StringLength) {
                StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                if (StorageStringBuffer != NULL) {
                    AsciiStrCpyS(
                            &StorageStringBuffer[TotalStringLength],
                            PcdGet32 (PcdAmiSysInvMaxStringLength),
                            TempStringBuffer);
                    TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                    StorageDeviceInfo->RevisionStrIndex = StringIndex++;
                    StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.RevisionValid = TRUE;
                }
            }
        }
        
        Status = UsbIo->UsbGetInterfaceDescriptor(UsbIo, &InterfaceDesc);
        if(!EFI_ERROR(Status)){
            DEBUG ((DEBUG_INFO, "Interface Subclass = %d  \n", InterfaceDesc.InterfaceSubClass));
            if(InterfaceDesc.InterfaceSubClass == USB_MASS_STORE_RBC) {// Flash storage subclass
                StorageDeviceInfo->MediaType = SysInvMediaTypeSSD;
                StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.MediaTypeValid = TRUE;
            }    
            if (InterfaceDesc.InterfaceSubClass == USB_MASS_STORE_8020I) {
                StorageDeviceInfo->MediaType = SysInvMediaTypeOPTICAL_DVD;
                StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.MediaTypeValid = TRUE;
            }    
            else {
                StorageDeviceInfo->MediaType = SysInvMediaTypeHDD;
                StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.MediaTypeValid = TRUE;
            }
        }
    }

   // if (BlockIo->Media->ReadOnly) {
     //   StorageDeviceInfo->MediaType = SysInvMediaTypeOPTICAL_DVD;
    //    StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.MediaTypeValid = TRUE;
  //  }
    
    //  USB device capable of Readytoremove 
    
    StorageDeviceInfo->ReadyToRemove = BlockIo->Media->RemovableMedia;
    StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.ReadyToRemoveValid = TRUE;
    
    if (BlockIo->Media->MediaPresent) {
        //device capacity
        DriveSizeInBytes = ((BlockIo->Media->LastBlock + 1) * BlockIo->Media->BlockSize );
        //DriveSizeInGB is DriveSizeInBytes / 1 GB (1 Decimal GB = 10^9 bytes)
        DeviceCapacity = (UINT64) DivU64x32(DriveSizeInBytes, 1000000000);
        StorageDeviceInfo->BlockSizeBytes = BlockIo->Media->BlockSize;
        StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.BlockSizeBytesValid = TRUE;
        
        StorageDeviceInfo->CapacityBytes = (UINT64) DriveSizeInBytes;
        StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.CapacityBytesValid = TRUE;
        
        DEBUG ((DEBUG_INFO, "DeviceCapacity = %lx Bytes \n", StorageDeviceInfo->CapacityBytes));
        DEBUG ((DEBUG_INFO, "DriveSizeInBytes = %d GB \n", DeviceCapacity));
    } else {
        StorageDeviceInfo->CapacityBytes = 0;
        StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.CapacityBytesValid = TRUE;
    }
        

    
    Status = gBS->HandleProtocol (
                        DeviceHandle,
                        &gEfiDevicePathProtocolGuid,
                        (VOID **)&DevPath );
        
    if(!EFI_ERROR(Status)  && DevPath){
        DevPathStr = ConvertDevicePathToText (DevPath, FALSE, TRUE);
        if(DevPathStr){
            StorageDeviceEntry.Dp.UefiDevPath = AllocateReservedZeroPool(StrnSizeS (DevPathStr, PcdGet32 (PcdAmiSysInvMaxStringLength)));
            if (StorageDeviceEntry.Dp.UefiDevPath != NULL)
                StrCpyS (StorageDeviceEntry.Dp.UefiDevPath, StrnSizeS (DevPathStr, PcdGet32 (PcdAmiSysInvMaxStringLength)), DevPathStr);
            FillStorageAssetTag(&StorageDeviceEntry, DevPathStr, /*StorageStringBuffer,*/ &TotalStringLength, &StringIndex);
            FreePool(DevPathStr);
        }
        
        TempDevPath = GetDevicePathTypeMatch(DevPath, &MessagingDevicePath);
        if (TempDevPath != NULL) {
            UsbDevPath = (USB_DEVICE_PATH*) TempDevPath;
            
            // Update StringId. This will be unique REDFISH ID String for this USB Device.
            StringLength = AsciiSPrint(
                                TempStringBuffer,
                                PcdGet32 (PcdAmiSysInvMaxStringLength),
                                "USB_Device%X_Port%x",
                                StorageDeviceEntry.Dp.DeviceStatus.DeviceInstance, UsbDevPath->ParentPortNumber);
            if (StringLength) {
                StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                if (StorageStringBuffer != NULL) {
                    AsciiStrCpyS(
                            &StorageStringBuffer[TotalStringLength],
                            PcdGet32 (PcdAmiSysInvMaxStringLength),
                            TempStringBuffer);
                    TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                    StorageDeviceInfo->IdStrIndex = StringIndex++;
                    StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.IdValid = TRUE;
                }
                StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                if (StorageStringBuffer != NULL) {
                    AsciiStrCpyS(
                                &StorageStringBuffer[TotalStringLength],
                                PcdGet32 (PcdAmiSysInvMaxStringLength),
                                TempStringBuffer);
                    TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                    StorageDeviceInfo->NameStrIndex = StringIndex++;
                    StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.NameValid = TRUE; 
                }
            }
            Status = PciIo->GetLocation(PciIo, &Seg, &Bus, &Dev, &Fun);

            if(!EFI_ERROR(Status)) {
                StringLength = AsciiSPrint(
                                    TempStringBuffer,
                                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                                    "S%1X|B%2X|D%2X|F%2X_USB_Port%X",
                                    Seg,Bus,Dev,Fun, UsbDevPath->ParentPortNumber);
                if (StringLength) {
                    StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                    if (StorageStringBuffer != NULL) {
                        AsciiStrCpyS(
                                &StorageStringBuffer[TotalStringLength],
                                PcdGet32 (PcdAmiSysInvMaxStringLength),
                                TempStringBuffer);
                        TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                        StorageDeviceInfo->LocationStrIndex = StringIndex++;
                        StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.LocationValid = TRUE;
                    }
                }
            }
        }
    }

//    // As Media Life can't be calculated, filling 0xFF to denote field is invalid
//    StorageDeviceInfo->PredictedMediaLifeLeftPercent = 0xFF;
//    StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.PredictedMediaLifeLeftPercentValid = TRUE;

    Status = SysInvAddStrings (
                        SystemInventoryInfoProtocol,
                        &StorageDeviceInfo->StringHdr,
                        StorageStringBuffer);
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR,"%a Failed to add string for Storage Device instance %d = %r\n", __FUNCTION__, gStorageDeviceIndex, Status));
    }
    if (StorageStringBuffer != NULL){
        FreePool(StorageStringBuffer);
        StorageStringBuffer = NULL;
        MaxStorageStrBuffSize = SIZE_8KB;
    }
    // OEM Hook to update USB Storage Device Inventory Data
    OemUpdateUsbStorageDeviceInventory (SystemInventoryInfoProtocol, &StorageDeviceEntry);

    InitializeStorageVolumesData (
                    SystemInventoryInfoProtocol,
                    &StorageDeviceEntry,
                    DeviceHandle );

    DEBUG ((DEBUG_INFO, "USB Device[%d]: InitializeStorageVolumesData Status = %r\n", gStorageDeviceIndex, Status));

    Status = AddDevEntryToList(
                    (SYS_INV_ITEM_LIST *)&SystemInventoryInfoProtocol->DevInitialCnt, 
                    &StorageDeviceEntry, 
                    sizeof(DEV_ENTRY) );
    
    DEBUG ((DEBUG_INFO, "USB Device[%d]: AddDevEntryToList Status = %r, DevInfoCount = %d\n", 
            gStorageDeviceIndex, Status, SystemInventoryInfoProtocol->DevInfoCount));
    
    gStorageDeviceIndex++;
    
    if (gStorageVolumeIndex){
        SystemInventoryInfoProtocol->DataValidFlags.StorageVolumeInfoValid = TRUE;
    }
    DEBUG((DEBUG_INFO,"%a() Exit  - Status: %r \n", __FUNCTION__, Status));
    return;
}

/**
  @internal
  
  Fills USB controller/ USB Device and USB Volume data and append it to DevInfoList
  
  @param [in][out] PciDeviceEntry          Pointer to PCI device entry
  @param [in]      ControllerHandle        PCI device Handle
 
  @retval Status-EFI_SUCCESS :             Successfully updated USB controller and drives/volume info
  
  @endinternal
**/
EFI_STATUS
InitializeUsbStorageDevData (
  IN  OUT SYSTEM_INVENTORY_INFO_PROTOCOL      *SystemInventoryInfoProtocol,
  IN  DEV_ENTRY                     *PciDeviceEntry,
  IN  EFI_HANDLE                    ControllerHandle
  )
{
    EFI_STATUS                              Status;
    UINTN                                   InfoIndex;
    EFI_USB2_HC_PROTOCOL                    *Usb2Hc;
    EFI_PCI_IO_PROTOCOL                     *PciIo;
    UINTN                                   OpenInfoCount;
    EFI_OPEN_PROTOCOL_INFORMATION_ENTRY     *OpenInfo;
    DEV_ENTRY                               StorageControllerEntry;
    STORAGE_CONTROLLER_INFO                 *ControllerDisplayInfo;
    UINT16                                  xHcVersion;
    EFI_HANDLE                              DeviceHandle;
    UINT8                                   MaxSpeed;
    UINT8                                   PortNumber;
    UINT8                                   Is64BitCapable;
    static UINT8                            ControllerIndex = 0;
    SYS_INV_PCI_INFO                        *PciInfo;
    UINT32                                  TotalStringLength = 0;
    UINT16                                  StringIndex = 1;   
    CHAR8                                   TempStringBuffer[FixedPcdGet32 (PcdAmiSysInvMaxStringLength)];
    UINTN                                   StringLength = 0;
    
    ZeroMem (TempStringBuffer,sizeof(TempStringBuffer));

    DEBUG ((DEBUG_INFO, "%a() Entry...\n", __FUNCTION__));
        
    Status = gBS->HandleProtocol (
                        ControllerHandle,
                        &gEfiUsb2HcProtocolGuid,
                        (VOID **)&Usb2Hc );
    if(EFI_ERROR(Status)){
        return Status;
    }
    
    PciInfo = &PciDeviceEntry->DisplayPtr.Pci;
    ZeroMem(&StorageControllerEntry, sizeof(DEV_ENTRY));
    StorageStringBuffer = AllocateZeroPool(MaxStorageStrBuffSize);
    if (StorageStringBuffer == NULL) {
        DEBUG((DEBUG_ERROR, "%a : Memory allocation failed!!!\n", __FUNCTION__));
        return EFI_OUT_OF_RESOURCES;
    }
    
    StorageControllerEntry.Signature = DEV_ENTRY_SIGNATURE;
    
    FillDevicePath(&StorageControllerEntry, ControllerHandle);
    
    StorageControllerEntry.Dp.DeviceStatus.DeviceType = SysInvDevStorageController;
    StorageControllerEntry.Dp.DeviceStatus.DeviceInstance = gStorageControllerIndex;
    StorageControllerEntry.Dp.DeviceStatus.VirtualLed = 0x01;
    StorageControllerEntry.Dp.DeviceStatus.DeviceState = SysInvDevEnabled;
    StorageControllerEntry.DisplayPtr.StorageController.Status.State = SysInvDevEnabled;
    StorageControllerEntry.DisplayPtr.StorageController.Status.Health = SysInvHealthOK;
    StorageControllerEntry.DisplayPtr.StorageController.StorageIndex = gStorageUnitIndex;
    StorageControllerEntry.InfoSize = sizeof(STORAGE_CONTROLLER_INFO);
    StorageControllerEntry.DisplayPtr.StorageController.Status.ValidFlags.StatusVF1Param.StateValid = 1;
    StorageControllerEntry.DisplayPtr.StorageController.Status.ValidFlags.StatusVF1Param.HealthValid = 1;
    StorageControllerEntry.DisplayPtr.StorageController.Flags1.StorageControllerInfoVF1Param.StorageIndexValid = 1;
    
    PciInfo->StorageControllerIndex = StorageControllerEntry.Dp.DeviceStatus.DeviceInstance;
    PciInfo->Flags1.PcieDeviceFV1Param.StorageControllerIndexValid = 1;

    ControllerDisplayInfo = &StorageControllerEntry.DisplayPtr.StorageController;
    ControllerDisplayInfo->ParentPciIndex = PciDeviceEntry->Dp.DeviceStatus.DeviceInstance;
    ControllerDisplayInfo->Flags1.StorageControllerInfoVF1Param.ParentPciIndexValid = 1;
    
    StringLength = AsciiSPrint(
                        TempStringBuffer,
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        "(USB_Controller_%X)",
                        gStorageControllerIndex);
    if (StringLength) {
        StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
        if (StorageStringBuffer != NULL) {
            AsciiStrCpyS(
                    &StorageStringBuffer[TotalStringLength],
                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                    TempStringBuffer);
            TotalStringLength += (UINT32)AsciiStrnSizeS (
                                            &StorageStringBuffer[TotalStringLength], 
                                            PcdGet32 (PcdAmiSysInvMaxStringLength) );
            ControllerDisplayInfo->AssetTagStrIndex = StringIndex++;
            ControllerDisplayInfo->Flags1.StorageControllerInfoVF1Param.AssetTagValid = TRUE;
        }
    }
    
    Status = Usb2Hc->GetCapability(
                            Usb2Hc,
                            &MaxSpeed,
                            &PortNumber,
                            &Is64BitCapable );
    if (!EFI_ERROR(Status)) {
        DEBUG ((DEBUG_INFO, "USB2 Host Controller Port: %d\n", PortNumber));
        DEBUG ((DEBUG_INFO, "USB2 Host Controller Max speed: %d\n", MaxSpeed));
        StringLength = AsciiSPrint(
                            TempStringBuffer,
                            PcdGet32 (PcdAmiSysInvMaxStringLength),
                            "%d",
                            MaxSpeed);
        if (StringLength) {
            StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
            if (StorageStringBuffer != NULL) {
                AsciiStrCpyS(
                        &StorageStringBuffer[TotalStringLength],
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        TempStringBuffer);
                TotalStringLength += (UINT32)AsciiStrnSizeS (
                                                &StorageStringBuffer[TotalStringLength], 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength) );
                ControllerDisplayInfo->SpeedGbpsStrIndex = StringIndex++;
                ControllerDisplayInfo->Flags1.StorageControllerInfoVF1Param.SpeedGbpsValid = TRUE;
            }
        }
    }
    Status = gBS->HandleProtocol (
                        ControllerHandle,
                        &gEfiPciIoProtocolGuid,
                        (VOID **)&PciIo );
    
    if(EFI_ERROR(Status))
        return Status;
            
    Status = PciIo->Mem.Read(PciIo, EfiPciIoWidthUint16, 0, XHCI_HC_INTERFACE_VERSION_OFFSET, 1, &xHcVersion);
    
    if(!EFI_ERROR(Status)) {
        StringLength = AsciiSPrint(
                            TempStringBuffer,
                            PcdGet32 (PcdAmiSysInvMaxStringLength),
                            "%x.%x",
                            (UINT8)(xHcVersion >> 8), (UINT8) xHcVersion);
        if (StringLength) {
            StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
            if (StorageStringBuffer != NULL) {
                AsciiStrCpyS(
                        &StorageStringBuffer[TotalStringLength],
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        TempStringBuffer);
                TotalStringLength += (UINT32)AsciiStrnSizeS (
                                                &StorageStringBuffer[TotalStringLength], 
                                                PcdGet32 (PcdAmiSysInvMaxStringLength) );
                ControllerDisplayInfo->FirmwareVersionStrIndex = StringIndex++;
                ControllerDisplayInfo->Flags1.StorageControllerInfoVF1Param.FirmwareVersionValid = TRUE;
            }
        }
    }
    // Fill it with 0xFF so that FF can be used as terminator. Consumers can scan until 0xFF.
    SetMem (&ControllerDisplayInfo->SupportedControllerProtocols, sizeof (ControllerDisplayInfo->SupportedControllerProtocols), gEndArrayTag);
    SetMem (&ControllerDisplayInfo->SupportedDeviceProtocols, sizeof (ControllerDisplayInfo->SupportedDeviceProtocols), gEndArrayTag);  
    
    ControllerDisplayInfo->SupportedControllerProtocols[0] = SysInvProtocolPCIe;
    ControllerDisplayInfo->SupportedControllerProtocols[1] = SysInvProtocolUSB;
    ControllerDisplayInfo->SupportedControllerProtocolsValidArrayElementsCount = 2;

    
    ControllerDisplayInfo->SupportedDeviceProtocols[0] = SysInvProtocolUSB;
    ControllerDisplayInfo->SupportedDeviceProtocolsValidArrayElementsCount = 1;
   
    StringLength = AsciiSPrint(
                        TempStringBuffer,
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        "%X",
                        StorageControllerEntry.Dp.DeviceStatus.DeviceInstance);
    if (StringLength) {
        StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
        if (StorageStringBuffer != NULL) {
            AsciiStrCpyS(
                    &StorageStringBuffer[TotalStringLength],
                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                    TempStringBuffer);
            TotalStringLength += (UINT32)AsciiStrnSizeS (
                                            &StorageStringBuffer[TotalStringLength], 
                                            PcdGet32 (PcdAmiSysInvMaxStringLength) );
            ControllerDisplayInfo->MemberIdStrIndex = StringIndex++;
            ControllerDisplayInfo->Flags1.StorageControllerInfoVF1Param.MemberIdValid = TRUE;
        }
    }
    
    StringLength = AsciiSPrint(
                        TempStringBuffer,
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        "USB_Controller%X",
                        StorageControllerEntry.Dp.DeviceStatus.DeviceInstance);
    if (StringLength) {
        StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
        if (StorageStringBuffer != NULL) {
            AsciiStrCpyS(
                    &StorageStringBuffer[TotalStringLength],
                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                    TempStringBuffer);    
            TotalStringLength += (UINT32)AsciiStrnSizeS (
                                            &StorageStringBuffer[TotalStringLength], 
                                            PcdGet32 (PcdAmiSysInvMaxStringLength) );
            ControllerDisplayInfo->NameStrIndex = StringIndex++;
            ControllerDisplayInfo->Flags1.StorageControllerInfoVF1Param.NameValid = TRUE;
        }
    }

    StringLength = AsciiSPrint(
                        TempStringBuffer,
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        "Not Available");
    if (StringLength) {
        StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
        if (StorageStringBuffer != NULL) {
            AsciiStrCpyS(
                    &StorageStringBuffer[TotalStringLength],
                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                    TempStringBuffer);
            TotalStringLength += (UINT32)AsciiStrnSizeS (
                                            &StorageStringBuffer[TotalStringLength], 
                                            PcdGet32 (PcdAmiSysInvMaxStringLength) );
            ControllerDisplayInfo->ModelStrIndex = StringIndex++;
            ControllerDisplayInfo->Flags1.StorageControllerInfoVF1Param.ModelValid = TRUE;
        }

        StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
        if (StorageStringBuffer != NULL) {
            AsciiStrCpyS(
                    &StorageStringBuffer[TotalStringLength],
                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                    TempStringBuffer);
            TotalStringLength += (UINT32)AsciiStrnSizeS (
                                            &StorageStringBuffer[TotalStringLength], 
                                            PcdGet32 (PcdAmiSysInvMaxStringLength) );
            ControllerDisplayInfo->SerialNumberStrIndex = StringIndex++;
            ControllerDisplayInfo->Flags1.StorageControllerInfoVF1Param.SerialNumberValid = TRUE;
        } 
    }

    Status = SysInvAddStrings (
                        SystemInventoryInfoProtocol,
                        &ControllerDisplayInfo->StringHdr,
                        StorageStringBuffer);
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR,"%a Failed to add string for Storage Controller instance %d = %r\n", 
                __FUNCTION__, gStorageControllerIndex, Status));
    }
    if (StorageStringBuffer != NULL){
        FreePool(StorageStringBuffer);
        StorageStringBuffer = NULL;
        MaxStorageStrBuffSize = SIZE_8KB;
    }
    // OEM Hook to update USB Storage Controller Inventory Data
    OemUpdateUsbStorageControllerInventory (SystemInventoryInfoProtocol, &StorageControllerEntry);
    
    Status = gBS->OpenProtocolInformation(
                                ControllerHandle,
                                &gEfiUsb2HcProtocolGuid,
                                &OpenInfo,
                                &OpenInfoCount );

    gUsbCtrlHandle = ControllerHandle;

    if(!EFI_ERROR(Status)) { 
        
        for (InfoIndex = 0; InfoIndex < OpenInfoCount; InfoIndex++) {
            if (OpenInfo[InfoIndex].Attributes & EFI_OPEN_PROTOCOL_BY_CHILD_CONTROLLER){
                DeviceHandle = OpenInfo[InfoIndex].ControllerHandle;  //disk/ssd Drive handle
                FillUsbDeviceInfo(
                            SystemInventoryInfoProtocol,
                            &StorageControllerEntry,
                            PciIo,
                            DeviceHandle,
                            OpenInfo[InfoIndex].AgentHandle );
            }
        }
        FreePool (OpenInfo);
    }
    gUsbCtrlHandle = NULL;
    
    Status = AddDevEntryToList(
                    (SYS_INV_ITEM_LIST *)&SystemInventoryInfoProtocol->DevInitialCnt, 
                    &StorageControllerEntry, 
                    sizeof(DEV_ENTRY) );
    DEBUG ((DEBUG_INFO, "USB Controller[%d]: AddDevEntryToList Status = %r, DevInfoCount = %d\n", 
            gStorageControllerIndex, Status, SystemInventoryInfoProtocol->DevInfoCount));
    
    gStorageControllerIndex++;
    ControllerIndex++;

    if(gStorageControllerIndex) {
        SystemInventoryInfoProtocol->DataValidFlags.StorageControllerInfoValid = TRUE;
    }

    if (gStorageDeviceIndex) {
        SystemInventoryInfoProtocol->DataValidFlags.StorageDeviceInfoValid = TRUE;
    }
    return Status;
}

/**
    @internal
    
    Executes the SCSI INQUIRY Command using ExtScsiPassThru 
    protocol

    @param [in] ExtScsiPassThru  - ExtendedScsiPassThru protocol
                                    pointer
    @param [in] TargetId         - Scsi TargetId in which Inquiry 
                                   command to be executed
    @param [in] Lun              - Logical Unit Number of target
    @param [in][out] InDataBuffer- Buffer to Inquiry command out data
    @param [in] InTransferLength - Length of INQUIRY data
    @param [in][out]SenseData    - Sense data returned by Inquiry
    @param [in] SenseDataLength  - Length of sense data
    @param [in] PageCode         - Vital Product Data page code
    @param [in] Evpd             - Enable Vital Product Data bit
    @param [in] ReadCapFlag      - Enable ReadCapacity Commands

    @retval EFI_STATUS      - EFI_SUCCESS: Inquiry command 
                              executed properly
                              EFI_INVALID_PARAMETER: Target, 
                              Lun, or the contents of 
                              ScsiRequestPacket are invalid.
                              EFI_BAD_BUFFER_SIZE: The SCSI 
                              Request Packet was not executed. 
                              The number of bytes that could be 
                              transferred is returned in InTransferLength. 
                              
    @endinternal
**/
EFI_STATUS
ExecuteInquiry(
  IN       EFI_EXT_SCSI_PASS_THRU_PROTOCOL  *ExtScsiPassThru,
  IN       UINT8                            *TargetId,
  IN       UINT64                           Lun,
  IN  OUT  UINT8                            *InDataBuffer,
  IN       UINT32                           InTransferLength,
  IN  OUT  UINT8                            *SenseData  OPTIONAL,
  IN       UINT8                            SenseDataLength,
  IN       UINT8                            PageCode,
  IN       BOOLEAN                          Evpd,
  IN       BOOLEAN                          ReadCapFlag,
  IN       BOOLEAN                          Cmd16
  )
{
    EFI_EXT_SCSI_PASS_THRU_SCSI_REQUEST_PACKET  CommandPacket;
    EFI_STATUS                                  Status;
    UINT8                                       Cdb[CDB_INQUIRY_SIZE];
    UINT8                                       ReadCapCdb[CDB_READCAP16_SIZE];
    
    //ASSERT (InDataBuffer != NULL);
    
    ZeroMem (&CommandPacket, sizeof (EFI_EXT_SCSI_PASS_THRU_SCSI_REQUEST_PACKET));
    ZeroMem (Cdb, CDB_INQUIRY_SIZE);
    
    if (ReadCapFlag) {
        ZeroMem (ReadCapCdb, sizeof(ReadCapCdb));
    }
    
    CommandPacket.Timeout         = EFI_TIMER_PERIOD_SECONDS(1);
    CommandPacket.InDataBuffer    = InDataBuffer;
    CommandPacket.InTransferLength= InTransferLength;
    CommandPacket.SenseData       = SenseData;
    CommandPacket.SenseDataLength = SenseDataLength;
    
    if(ReadCapFlag){

        CommandPacket.Cdb               = ReadCapCdb;
        

        if(!Cmd16) {
            CommandPacket.CdbLength         = (UINT8) CDB_READ10_SIZE;  
            ReadCapCdb[0]                   = EFI_SCSI_OP_READ_CAPACITY;
        }     
        else {
            ReadCapCdb[0]                   = EFI_SCSI_OP_READ_CAPACITY16; // OpCode
            ReadCapCdb[1]                   = 0x10; // SERVICE ACTION
            ReadCapCdb[13]                  = 0x20; // Allocated Length, Response data 32 Bytes
            CommandPacket.CdbLength         = (UINT8) CDB_READCAP16_SIZE;
            
        }
        
    }
    else{
        CommandPacket.Cdb               = Cdb;
        Cdb[0]                          = EFI_SCSI_OP_INQUIRY;
        if (Evpd) {
            Cdb[1] |= 0x01;
            Cdb[2]  = PageCode;
        }
        
        if (InTransferLength > 0xff) {
            InTransferLength = 0xff;
        }
        
        Cdb[4]                          = (UINT8)InTransferLength;
        CommandPacket.CdbLength         = (UINT8) CDB_INQUIRY_SIZE;
    }
    
    
    CommandPacket.DataDirection         = EFI_EXT_SCSI_DATA_DIRECTION_READ;
    
    Status = ExtScsiPassThru->PassThru(
                                ExtScsiPassThru,
                                TargetId,
                                Lun,
                                &CommandPacket,
                                NULL );
    
    DEBUG ((DEBUG_INFO, "%a()  SCSI PassThru INQUIRY Command Status = %r \n", __FUNCTION__, Status));
    return Status;
}

/**
  @internal
  
  Fills SCSI/SAS/iSCSI Devices data
  
  @param  [in] ControllerHandle                     Pci device Handle
  @param  [in][out] DeviceEntry                     Pointer to the Storage Controller DEV_ENTRY structure if IsPhysicalDrive is TRUE
                                                    Pointer to the Network Controller DEV_ENTRY structure if IsPhysicalDrive is FALSE
  @param  [in] ExtScsiPassThruProtocol              Pointer to the ExtendedScsiPassThru protocol
  @param  [in] PassThruDevPath                      Pointer to the DevicePath structure contains the device path 
                                                    up-to the handle in which ExtendedScsiPassThru is installed
  @param  [in] IsPhysicalDrive                      BOOLEAN: Flag to indicate that the drive under the controller are attached
                                                             physically to the system or connected through network(iSCSI)
                                                    TRUE  - Drives are attached physically to the machine.
                                                    FLASE - Drives are attached to the system through network(Hence it is iSCSI storage device)
                                                       Controller data will be the network controller
  @param  [in] CtrlIndex                            The SCSI controller index in the SystemInventoryInfo list

  
  @retval EFI_STATUS                              - EFI_SUCCESS: Filling SCSI device data success
                                                    EFI_UNSUPPORTED :The device does not support 
                                                    the specified protocol. 
                                                    EFI_OUT_OF_RESOURCES: Memory allocation failed.
                                                        
  
  @endinternal
 */
EFI_STATUS
FillScsiDevInfo(
  IN OUT    SYSTEM_INVENTORY_INFO_PROTOCOL      *SystemInventoryInfoProtocol,
  IN        EFI_HANDLE                          ControllerHandle,
  IN  OUT   DEV_ENTRY                           *DeviceEntry OPTIONAL,
  IN        EFI_EXT_SCSI_PASS_THRU_PROTOCOL     *ExtScsiPassThruProtocol,
  IN        EFI_DEVICE_PATH_PROTOCOL            *PassThruDevPath,
  IN        BOOLEAN                             IsPhysicalDrive,
  IN        UINTN                               CtrlIndex OPTIONAL )
{
    EFI_STATUS                                  Status;
    EFI_HANDLE                                  DeviceHandle;
    UINTN                                       Seg = 0;
    UINTN                                       Bus = 0;
    UINTN                                       Dev = 0;
    UINTN                                       Fun = 0;
    UINT8                                       *InDataBuffer;
    UINT8                                       *SenseData;
    UINT8                                       Index;
    UINT8                                       Target[TARGET_MAX_BYTES];
    UINT8                                       *TargetId;
    UINT64                                      Lun = 0;
    CHAR8                                       ModelNumber[20];
    CHAR8                                       Manufacturer[8];
    CHAR8                                       FirmwareRev[8];
    CHAR8                                       Revision[8];
    UINT64                                      LastBlock = 0;
    UINT8                                       *LastBlockPtr;
    DEV_ENTRY                                   StorageDeviceEntry;
    STORAGE_DEVICE_INFO                         *DeviceDisplayInfo;
    EFI_SCSI_INQUIRY_DATA                       *InquiryData;
    EFI_SCSI_DISK_CAPACITY_DATA                 *ReadCapData;
    EFI_SCSI_DISK_CAPACITY_DATA16               *ReadCap16Data;
    EFI_SCSI_SUPPORTED_VPD_PAGES_VPD_PAGE       *SupportedVpd;
    EFI_DEVICE_PATH_PROTOCOL                    *TempDevPathNode;
    EFI_DEVICE_PATH_PROTOCOL                    *FullDevPath;
    EFI_PCI_IO_PROTOCOL                         *PciIo;
    EFI_BLOCK_IO_PROTOCOL                       *BlockIo;
    CHAR16                                      *DevPathStr = NULL;
    UINT32                                      TotalStringLength = 0;
    UINT16                                      StringIndex = 1;
    CHAR8                                       TempStringBuffer[FixedPcdGet32 (PcdAmiSysInvMaxStringLength)];
    UINTN                                       StringLength = 0;
    
    STORAGE_CONTROLLER_INFO                     *StorageController;
    
    ZeroMem (TempStringBuffer,sizeof(TempStringBuffer));

    
    Status = gBS->HandleProtocol (ControllerHandle,
                                  &gEfiPciIoProtocolGuid,
                                  (VOID **)&PciIo);
    if (!EFI_ERROR(Status)) {
        Status = PciIo->GetLocation(PciIo, &Seg, &Bus, &Dev, &Fun);
        if (EFI_ERROR(Status)) {
            DEBUG ((DEBUG_ERROR, "%a() PciIo->GetLocation status= %r\n", __FUNCTION__,Status));
        }
    }

    SetMem(Target, TARGET_MAX_BYTES, 0xFF);
    TargetId = Target;
                    
    while(TRUE) {
        Status = ExtScsiPassThruProtocol->GetNextTargetLun(
                                                    ExtScsiPassThruProtocol,
                                                    (UINT8 **)&TargetId, 
                                                    &Lun );
        if(EFI_ERROR(Status)){
           DEBUG ((DEBUG_ERROR, "Get Next Target device Status= %r\n", Status));
           break;
        }
        DEBUG ((DEBUG_INFO, "Target = %d\n", (UINT8)*TargetId));
        DEBUG ((DEBUG_INFO, "LUN = %ld\n", Lun));
        
        Status = ExtScsiPassThruProtocol->BuildDevicePath(
                                                    ExtScsiPassThruProtocol,
                                                    TargetId,
                                                    Lun,
                                                    &TempDevPathNode );

        DEBUG ((DEBUG_INFO, "Build Device Path Status = %r\n", Status));

        FullDevPath = AppendDevicePathNode(PassThruDevPath, TempDevPathNode);

        DevPathStr = ConvertDevicePathToText (FullDevPath, FALSE, TRUE);

        if(DevPathStr) {
            DEBUG((DEBUG_INFO, "Device Path Built: %s \n", DevPathStr)); 
            FreePool(DevPathStr);
        }
 
        DeviceHandle = NULL;
        
        Status = gBS->LocateDevicePath (
                                &gEfiDevicePathProtocolGuid,
                                &FullDevPath,
                                &DeviceHandle );

        DEBUG ((DEBUG_INFO, "LocateDevicePath for Device Handle, Status = %r\n", Status));

        if(!EFI_ERROR(Status)) {

            if(IsPhysicalDrive) {
                
                // Check if it is a logical storage device created by SCSI/SAS controller.
                Status = gBS->HandleProtocol (
                                        DeviceHandle,
                                        &gEfiBlockIoProtocolGuid,
                                        (VOID **)&BlockIo );

                if(!EFI_ERROR(Status) && BlockIo != NULL && BlockIo->Media->LogicalPartition) { // Not a physical storage device
                    DEBUG ((DEBUG_INFO, "Skipping the entry... It is a RAID Logical drive!\n"));
                    continue;
                }
            }
        } else {
            DEBUG ((DEBUG_ERROR, "Volume Information if any for the Storage Drive[%d] can not be added\n", 
                    gStorageDeviceIndex));
        }
                
        ZeroMem (&StorageDeviceEntry, sizeof(DEV_ENTRY));
        StorageStringBuffer = AllocateZeroPool(MaxStorageStrBuffSize);
        if (StorageStringBuffer == NULL) {
            DEBUG((DEBUG_ERROR, "%a : Memory allocation failed!!!\n", __FUNCTION__));
            return EFI_OUT_OF_RESOURCES;
        }
        TotalStringLength = 0;
        StringIndex = 1;
                            
        StorageDeviceEntry.Signature = DEV_ENTRY_SIGNATURE;
        StorageDeviceEntry.Dp.DeviceStatus.DeviceType = SysInvDevStorageDisk;
        StorageDeviceEntry.Dp.DeviceStatus.VirtualLed = 0x01;
        StorageDeviceEntry.Dp.DeviceStatus.DeviceState = SysInvDevEnabled;
        StorageDeviceEntry.DisplayPtr.StorageDevice.Status.State = SysInvDevEnabled;
        StorageDeviceEntry.DisplayPtr.StorageDevice.Status.Health = SysInvHealthOK;
        StorageDeviceEntry.Dp.DeviceStatus.DeviceInstance = gStorageDeviceIndex;
        StorageDeviceEntry.DisplayPtr.StorageDevice.Status.ValidFlags.StatusVF1Param.StateValid = 1;
        StorageDeviceEntry.DisplayPtr.StorageDevice.Status.ValidFlags.StatusVF1Param.HealthValid = 1;
                
        StorageDeviceEntry.InfoSize = sizeof(STORAGE_DEVICE_INFO);
        DeviceDisplayInfo = &StorageDeviceEntry.DisplayPtr.StorageDevice;
                
        // INQUIRY Command for getting device serial model numbers
            
        // Allocate memory for Data In Buffer and Sense data.
        InDataBuffer = AllocateZeroPool (sizeof(EFI_SCSI_INQUIRY_DATA) + sizeof(EFI_SCSI_SUPPORTED_VPD_PAGES_VPD_PAGE));
        if (InDataBuffer == NULL) {
            DEBUG((DEBUG_ERROR, "%a : Memory allocation failed!!!\n", __FUNCTION__));
            return EFI_OUT_OF_RESOURCES;
        }
        
        SenseData = AllocateZeroPool (SENSE_DATA_LENGTH);
        
        if (SenseData == NULL ) {
            FreePool (InDataBuffer);
            return EFI_OUT_OF_RESOURCES;
        }
        
        Status = ExecuteInquiry(
                        ExtScsiPassThruProtocol,
                        TargetId,
                        Lun,
                        InDataBuffer,
                        sizeof(EFI_SCSI_INQUIRY_DATA),
                        SenseData,
                        SENSE_DATA_LENGTH,
                        0,
                        FALSE,
                        FALSE,
                        FALSE );
        
        if(!EFI_ERROR(Status)){
            
            // Update the Child device and Parent device information only if the Target and LUN has a valid device.
            if(IsPhysicalDrive) {
//COMPAL_CHANGE >>>               
			    //Patch - Start
                StringLength = 0;
                //Debug - Start
                if (ExtScsiPassThruProtocol->Mode != NULL) {
                    DEBUG ((DEBUG_ERROR, "5084 ExtScsiPassThruProtocol->Mode->Attributes = %x\n",ExtScsiPassThruProtocol->Mode->Attributes));
                }
                //Debug - End
                if ((ExtScsiPassThruProtocol->Mode != NULL) && ((ExtScsiPassThruProtocol->Mode->Attributes & EFI_EXT_SCSI_PASS_THRU_ATTRIBUTES_PHYSICAL) == 0)){
                    DEBUG ((DEBUG_ERROR, "5087 Logical Device\n"));
                    StringLength = AsciiSPrint(
                                    TempStringBuffer,
                                    PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                    "Logical Device");
                } else {
                    DEBUG ((DEBUG_ERROR, "5095 Physical Device\n"));
                    StringLength = AsciiSPrint(
                                    TempStringBuffer,
                                    PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                    "Physical Device");
                }
                if (StringLength) {
                    StrBuffAvailabilityCheck (
                                    &StorageStringBuffer,
                                    &MaxStorageStrBuffSize,
                                    StringLength,
                                    TotalStringLength );
                    if (StorageStringBuffer != NULL) {
                        AsciiStrCpyS(
                                &StorageStringBuffer[TotalStringLength],
                                PcdGet32 (PcdAmiSysInvMaxStringLength),
                                TempStringBuffer);
                                TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength],PcdGet32 (PcdAmiSysInvMaxStringLength) );
                                DeviceDisplayInfo->DescriptionStrIndex = StringIndex++;
                                DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.DescriptionValid = TRUE;
                    }
                }
                //Patch - End
//COMPAL_CHANGE <<<
                if (CtrlIndex) {
                    DeviceDisplayInfo->ParentControllerIndex = SystemInventoryInfoProtocol->DevInfoList[CtrlIndex]->Dp.DeviceStatus.DeviceInstance;
                    
                    StorageController = &SystemInventoryInfoProtocol->DevInfoList[CtrlIndex]->DisplayPtr.StorageController;
                    Status = AddDevEntryToList(
                                (SYS_INV_ITEM_LIST *)&StorageController->ChildDeviceIndex.DevInitialCnt,
                                &gStorageDeviceIndex,
                                sizeof (UINT64));
    
                    if (EFI_ERROR (Status)) {
                        DEBUG ((DEBUG_ERROR, "%a() Storage controller[%d]: AddDevEntryToList Child Status = %r, Index = %d\n",
                                __FUNCTION__,SystemInventoryInfoProtocol->DevInfoList[CtrlIndex]->Dp.DeviceStatus.DeviceInstance, Status, 
                                StorageController->ChildDeviceIndex.DevInfoCount));
                    }
                }
                // Update StringId. This will be unique REDFISH ID String for this ATA Device.
                StringLength = AsciiSPrint(
                                    TempStringBuffer,
                                    PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                    "SCSI_Device%x_Target%X_LUN%X",
                                    StorageDeviceEntry.Dp.DeviceStatus.DeviceInstance,
                                    *TargetId, Lun);
                if (StringLength) {
                    StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                    if (StorageStringBuffer != NULL) {
                        AsciiStrCpyS(
                                &StorageStringBuffer[TotalStringLength],
                                PcdGet32 (PcdAmiSysInvMaxStringLength),
                                TempStringBuffer);
                        TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                        DeviceDisplayInfo->IdStrIndex = StringIndex++;
                        DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.IdValid = TRUE;
                    }
                    StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                    if (StorageStringBuffer != NULL) {
                        AsciiStrCpyS(
                                &StorageStringBuffer[TotalStringLength],
                                PcdGet32 (PcdAmiSysInvMaxStringLength),
                                TempStringBuffer);
                        TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                        DeviceDisplayInfo->NameStrIndex = StringIndex++;
                        DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.NameValid = TRUE;
                    }
                }
                StringLength = AsciiSPrint(
                                TempStringBuffer,
                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                "S%1X|B%2X|D%2X|F%2X SCSI TARGET%X LUN%X",
                                Seg,Bus,Dev,Fun, *TargetId, Lun);
                if (StringLength) {
                    StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                    if (StorageStringBuffer != NULL) {
                        AsciiStrCpyS(
                                &StorageStringBuffer[TotalStringLength],
                                PcdGet32 (PcdAmiSysInvMaxStringLength),
                                TempStringBuffer);
                        TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                        DeviceDisplayInfo->LocationStrIndex = StringIndex++;
                        DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.LocationValid = TRUE;
                    }
                }
            } else {
                DeviceDisplayInfo->ParentControllerIndex = DeviceEntry->Dp.DeviceStatus.DeviceInstance;
                Status = AddDevEntryToList(
                            (SYS_INV_ITEM_LIST *)&DeviceEntry->DisplayPtr.NicData.ChildDeviceIndex.DevInitialCnt,
                            &gStorageDeviceIndex,
                            sizeof (UINT64));
                if (EFI_ERROR (Status)) {
                    DEBUG ((DEBUG_ERROR, "%a() Nic controller[%d]: AddDevEntryToList Child Status = %r, Index = %d\n",
                            __FUNCTION__,DeviceEntry->Dp.DeviceStatus.DeviceInstance, Status, 
                            DeviceEntry->DisplayPtr.NicData.ChildDeviceIndex.DevInfoCount));

                }
                
                // Update StringId. This will be unique REDFISH ID String for this ATA Device.
                StringLength = AsciiSPrint(
                                    TempStringBuffer,
                                    PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                    "iSCSI_Device%x_Target%X_LUN%X",
                                    StorageDeviceEntry.Dp.DeviceStatus.DeviceInstance,
                                    *TargetId, Lun);
                if (StringLength) {
                    StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                    if (StorageStringBuffer != NULL) {
                        AsciiStrCpyS(
                                &StorageStringBuffer[TotalStringLength],
                                PcdGet32 (PcdAmiSysInvMaxStringLength),
                                TempStringBuffer);
                        TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                        DeviceDisplayInfo->IdStrIndex = StringIndex++;
                        DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.IdValid = TRUE;
                    }
    
                    StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                    if (StorageStringBuffer != NULL) {
                        AsciiStrCpyS(
                                    &StorageStringBuffer[TotalStringLength],
                                    PcdGet32 (PcdAmiSysInvMaxStringLength),
                                    TempStringBuffer);
                        TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                        DeviceDisplayInfo->NameStrIndex = StringIndex++;
                        DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.NameValid = TRUE;
                    }
                }

                StringLength = AsciiSPrint(
                                    TempStringBuffer,
                                    PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                    "S%1X|B%2X|D%2X|F%2X iSCSI TARGET%X LUN%X",
                                    Seg,Bus,Dev,Fun, *TargetId, Lun);
                if (StringLength) {
                    StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                    if (StorageStringBuffer != NULL) {
                        AsciiStrCpyS(
                                &StorageStringBuffer[TotalStringLength],
                                PcdGet32 (PcdAmiSysInvMaxStringLength),
                                TempStringBuffer);
                        TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                        DeviceDisplayInfo->LocationStrIndex = StringIndex++;
                        DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.LocationValid = TRUE;
                    }
                }
            }
            

            InquiryData = (EFI_SCSI_INQUIRY_DATA *)InDataBuffer;
            
            DEBUG ((DEBUG_INFO, "Peripheral type = %d \n", InquiryData->Peripheral_Type));
            
            DeviceDisplayInfo->EncryptionAbility = SysInvEncryptionAbilityNone;
            DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.EncryptionAbilityValid = TRUE;
            
            DeviceDisplayInfo->Protocol = SysInvProtocolSAS;
            DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.ProtocolValid = TRUE;
            

            PrepareString (ModelNumber, (CHAR8 *)&InquiryData->Reserved_5_95[11], 16, FALSE);
            StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,AsciiStrLen (ModelNumber),TotalStringLength);
            if (StorageStringBuffer != NULL) {
                AsciiStrCpyS(
                        &StorageStringBuffer[TotalStringLength],
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        ModelNumber);
                TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                DeviceDisplayInfo->ModelStrIndex = StringIndex++;
                DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.ModelValid = TRUE;
            }

            PrepareString (Manufacturer, (CHAR8 *)&InquiryData->Reserved_5_95[3], 8, FALSE);
            StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,AsciiStrLen(Manufacturer),TotalStringLength);
            if (StorageStringBuffer != NULL) {
                AsciiStrCpyS(
                        &StorageStringBuffer[TotalStringLength],
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        Manufacturer);
                TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                DeviceDisplayInfo->ManufacturerStrIndex = StringIndex++;
                DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.ManufacturerValid = TRUE;
            }
            

            
            //Serial Number read
            Status = FillMediaSerialNoInfo (
                                ExtScsiPassThruProtocol,
                                TargetId,
                                Lun,
                                DeviceDisplayInfo,
                                &TotalStringLength,
                                &StringIndex);
            if (EFI_ERROR(Status) || DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.SerialNumberValid == 0) {
                DEBUG ((DEBUG_ERROR, "%a() FillMediaSerialNoInfo %r \n", __FUNCTION__,Status));
#if AMI_SCSI_36_55_SERIAL_NO_SUPPORT
                /*SPC spec :  36~55 Bytes vendor specific (Serial Number)  
                 * 
                 */
                ZeroMem(TempStringBuffer,sizeof(TempStringBuffer));
                CopyMem(TempStringBuffer, &InquiryData->Reserved_5_95[31], SCSI_SERIAL_NO_LEN);

                
                 if (AsciiStrnLenS(TempStringBuffer,SCSI_SERIAL_NO_LEN)) {
                    StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,AsciiStrLen(TempStringBuffer),TotalStringLength);
                    if (StorageStringBuffer != NULL) {
                        AsciiStrCpyS(
                                &StorageStringBuffer[TotalStringLength],
                                PcdGet32 (PcdAmiSysInvMaxStringLength),
                                TempStringBuffer);
                        TotalStringLength += (UINT32)AsciiStrnSizeS (
                                                            &StorageStringBuffer[TotalStringLength],
                                                            PcdGet32 (PcdAmiSysInvMaxStringLength) );
                        DeviceDisplayInfo->SerialNumberStrIndex = StringIndex++;
                        DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.SerialNumberValid = TRUE;
                    }
                }
#endif
            }

            PrepareString (FirmwareRev, (CHAR8 *)&InquiryData->Reserved_5_95[27], 4, FALSE);
            StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,AsciiStrLen (FirmwareRev),TotalStringLength);
            if (StorageStringBuffer != NULL) {
                AsciiStrCpyS(
                        &StorageStringBuffer[TotalStringLength],
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        FirmwareRev);
                TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                DeviceDisplayInfo->FirmwareRevStringStrIndex = StringIndex++;
                DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.FirmwareRevStringValid = TRUE;
            }            

            PrepareString (Revision, (CHAR8 *)&InquiryData->Reserved_5_95[27], 4, FALSE);
            StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,AsciiStrLen (Revision),TotalStringLength);
            if (StorageStringBuffer != NULL) {
                AsciiStrCpyS(
                        &StorageStringBuffer[TotalStringLength],
                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                        Revision);
                TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
                DeviceDisplayInfo->RevisionStrIndex = StringIndex++;
                DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.RevisionValid = TRUE;
            }
            

//            switch(InquiryData->Version){
//            case SCSI_NO_REVISION:
//                StringLength = AsciiSPrint(
//                                    TempStringBuffer,
//                                    PcdGet32 (PcdAmiSysInvMaxStringLength), 
//                                    "No Revision");
//                break;
//            case SCSI_REVISION_SPC:
//                StringLength = AsciiSPrint(
//                                    TempStringBuffer,
//                                    PcdGet32 (PcdAmiSysInvMaxStringLength), 
//                                    "SPC");;
//                break;
//            case SCSI_REVISION_SPC_2:
//                StringLength = AsciiSPrint(
//                                    TempStringBuffer,
//                                    PcdGet32 (PcdAmiSysInvMaxStringLength), 
//                                    "SPC-2");
//                break;
//            case SCSI_REVISION_SPC_3:
//                StringLength = AsciiSPrint(
//                                    TempStringBuffer,
//                                    PcdGet32 (PcdAmiSysInvMaxStringLength), 
//                                    "SPC-3");
//                break;
//            case SCSI_REVISION_SPC_4:
//                StringLength = AsciiSPrint(
//                                    TempStringBuffer,
//                                    PcdGet32 (PcdAmiSysInvMaxStringLength), 
//                                    "SPC-4");
//                break;
//            }
//
//            if (StringLength) {
//                StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
//                if (StorageStringBuffer != NULL) {
//                    AsciiStrCpyS(
//                            &StorageStringBuffer[TotalStringLength],
//                            PcdGet32 (PcdAmiSysInvMaxStringLength),
//                            TempStringBuffer);
//                    TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], PcdGet32 (PcdAmiSysInvMaxStringLength));
//                    DeviceDisplayInfo->RevisionStrIndex = StringIndex++;
//                    DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.RevisionValid = TRUE;
//                }
//            }
            
            if(InquiryData->Peripheral_Type == DIRECT_ACCESS_BLK_DEVICE || InquiryData->Peripheral_Type == SEQUENTIAL_ACCESS_BLK_DEVICE || 
               InquiryData->Peripheral_Type == STORAGE_ARRAY_CONTROLLER_DEVICE || InquiryData->Peripheral_Type == SIMPLE_DIRECT_ACCESS_BLK_DEVICE) {
                
                DeviceDisplayInfo->MediaType = SysInvMediaTypeHDD;
                DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.MediaTypeValid = TRUE;
            
            }
            
            /******Read Capacity*****/
            ZeroMem(InDataBuffer, sizeof(EFI_SCSI_DISK_CAPACITY_DATA));
            ZeroMem(SenseData, SENSE_DATA_LENGTH);
            
            Status = ExecuteInquiry(
                                ExtScsiPassThruProtocol,
                                TargetId,
                                Lun,
                                InDataBuffer,
                                sizeof(EFI_SCSI_DISK_CAPACITY_DATA),
                                SenseData,
                                SENSE_DATA_LENGTH,
                                0,
                                FALSE,
                                TRUE ,
                                FALSE);
            
            if(!EFI_ERROR(Status)){
                ReadCapData = (EFI_SCSI_DISK_CAPACITY_DATA *)InDataBuffer;
                
                if ((ReadCapData->LastLba3 == 0xff) && (ReadCapData->LastLba2 == 0xff) &&
                    (ReadCapData->LastLba1 == 0xff) && (ReadCapData->LastLba0 == 0xff)) {
                    
                    
                    /******Read Capacity 16 -> Hard disk capacity above 2TB*****/
                    ZeroMem(InDataBuffer, sizeof(EFI_SCSI_DISK_CAPACITY_DATA16));
                    ZeroMem(SenseData, SENSE_DATA_LENGTH);
                    
                    Status = ExecuteInquiry(
                                        ExtScsiPassThruProtocol,
                                        TargetId,
                                        Lun,
                                        InDataBuffer,
                                        sizeof(EFI_SCSI_DISK_CAPACITY_DATA16),
                                        SenseData,
                                        SENSE_DATA_LENGTH,
                                        0,
                                        FALSE,
                                        TRUE ,
                                        TRUE );
                    
                    if (!EFI_ERROR(Status)){
                        
                        
                        ReadCap16Data = (EFI_SCSI_DISK_CAPACITY_DATA16 *)InDataBuffer;
 
                        LastBlockPtr = (UINT8*)&LastBlock;
                        *LastBlockPtr++ = ReadCap16Data->LastLba0;
                        *LastBlockPtr++ = ReadCap16Data->LastLba1;
                        *LastBlockPtr++ = ReadCap16Data->LastLba2;
                        *LastBlockPtr++ = ReadCap16Data->LastLba3;
                        *LastBlockPtr++ = ReadCap16Data->LastLba4;
                        *LastBlockPtr++ = ReadCap16Data->LastLba5;
                        *LastBlockPtr++ = ReadCap16Data->LastLba6;
                        *LastBlockPtr   = ReadCap16Data->LastLba7;
                        
                        
                        DeviceDisplayInfo->BlockSizeBytes = (UINT32) (ReadCap16Data->BlockSize3 << 24) |
                                                                    (ReadCap16Data->BlockSize2 << 16) |
                                                                    (ReadCap16Data->BlockSize1 << 8)  |
                                                                    ReadCap16Data->BlockSize0;
                        DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.BlockSizeBytesValid = TRUE;
                        DeviceDisplayInfo->CapacityBytes = MultU64x32 ((LastBlock + 1), DeviceDisplayInfo->BlockSizeBytes);
                        DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.CapacityBytesValid = TRUE;
                        
                    }
                } else {
                    LastBlock = (UINT64)(UINT32)(ReadCapData->LastLba3 << 24)|(ReadCapData->LastLba2 << 16) | \
                                        (ReadCapData->LastLba1 << 8) | ReadCapData->LastLba0 ;
                    DeviceDisplayInfo->BlockSizeBytes = (ReadCapData->BlockSize1 << 8) | ReadCapData->BlockSize0;
                    DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.BlockSizeBytesValid = TRUE;
                    DeviceDisplayInfo->CapacityBytes = MultU64x32 ((LastBlock + 1), DeviceDisplayInfo->BlockSizeBytes);
                    DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.CapacityBytesValid = TRUE;
                }
            }
            
            ZeroMem (InDataBuffer, sizeof(EFI_SCSI_SUPPORTED_VPD_PAGES_VPD_PAGE));
                                    
            Status = ExecuteInquiry(
                                ExtScsiPassThruProtocol,
                                TargetId,
                                Lun,
                                InDataBuffer,
                                sizeof (EFI_SCSI_SUPPORTED_VPD_PAGES_VPD_PAGE),
                                NULL,
                                0,
                                0x00,
                                TRUE,
                                FALSE,
                                FALSE );
            
            if(!EFI_ERROR(Status)){

                SupportedVpd = (EFI_SCSI_SUPPORTED_VPD_PAGES_VPD_PAGE*) InDataBuffer;

                for(Index = 0; Index < 0xFF; Index++) {

                    if(SupportedVpd->SupportedVpdPageList[Index] == BLOCK_DEVICE_CHAR_PAGE_CODE){

                        DEBUG ((DEBUG_INFO, "Found Supported Page = %x\n", SupportedVpd->SupportedVpdPageList[Index]));
    
                        // INQUIRY Command for getting device vital product data of page code 0xB1 for getting device RPM
                        ZeroMem (InDataBuffer, sizeof(EFI_SCSI_SUPPORTED_VPD_PAGES_VPD_PAGE));
                        
                        Status = ExecuteInquiry(
                                            ExtScsiPassThruProtocol,
                                            TargetId,
                                            Lun,
                                            InDataBuffer,
                                            BLOCK_DEVICE_CHAR_SIZE, // 64 bytes of Block Device Characteristics structure
                                            NULL,
                                            0,
                                            BLOCK_DEVICE_CHAR_PAGE_CODE, // Block Device Characteristics Page Code
                                            TRUE,
                                            FALSE,
                                            FALSE);
                        
                        if(!EFI_ERROR(Status)){
                            
                            if(((InDataBuffer[4] << 8) | InDataBuffer[5]) == 1) {// If Nominal Rotation Rate is 1 - Non Rotating Media (eg. SSD)
                                DeviceDisplayInfo->MediaType = SysInvMediaTypeSSD;
                                DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.MediaTypeValid = TRUE;
                            }
                            else {
                                DeviceDisplayInfo->RotationSpeedRPM = ((InDataBuffer[4] << 8) | InDataBuffer[5]);
                                DeviceDisplayInfo->ValidFlags1.StorageDeViInfoVF1Param.RotationSpeedRPMValid = TRUE;
                            }    
                        }
                        break;
                    }
                }//For loop
            }
            
            Status = SysInvAddStrings (
                                SystemInventoryInfoProtocol,
                                &DeviceDisplayInfo->StringHdr,
                                StorageStringBuffer);
            if (EFI_ERROR(Status)) {
                DEBUG((DEBUG_ERROR,"%a Failed to add string for Storage Device instance %d = %r\n", __FUNCTION__, gStorageDeviceIndex, Status));
            }
            if (StorageStringBuffer != NULL){
                FreePool(StorageStringBuffer);
                StorageStringBuffer = NULL;
                MaxStorageStrBuffSize = SIZE_8KB;
            }
            // OEM Hook to update SCSI Storage Device Inventory Data
            OemUpdateScsiStorageDeviceInventory (SystemInventoryInfoProtocol, &StorageDeviceEntry);
    
            if(DeviceHandle) {
                InitializeStorageVolumesData (
                         SystemInventoryInfoProtocol,
                         &StorageDeviceEntry,
                         DeviceHandle );
            }
            
            Status = gBS->HandleProtocol (
                                    DeviceHandle,
                                    &gEfiDevicePathProtocolGuid,
                                    (VOID **)&FullDevPath );
            
            if(!EFI_ERROR(Status)) {
                DevPathStr = ConvertDevicePathToText (FullDevPath, FALSE, TRUE);
                if(DevPathStr){
                    StorageDeviceEntry.Dp.UefiDevPath = AllocateReservedZeroPool(StrnSizeS (DevPathStr, PcdGet32 (PcdAmiSysInvMaxStringLength)));
                    if (StorageDeviceEntry.Dp.UefiDevPath != NULL)
                        StrCpyS (StorageDeviceEntry.Dp.UefiDevPath, StrnSizeS (DevPathStr, PcdGet32 (PcdAmiSysInvMaxStringLength)), DevPathStr);
                    FreePool(DevPathStr);
                }
            }

            Status = AddDevEntryToList((SYS_INV_ITEM_LIST *)&SystemInventoryInfoProtocol->DevInitialCnt, &StorageDeviceEntry, sizeof(DEV_ENTRY));
            DEBUG ((DEBUG_INFO, "AddDevEntryToList Status = %r, DevInfoCount = %d\n", Status, SystemInventoryInfoProtocol->DevInfoCount));
            
            DEBUG ((DEBUG_INFO, "ScsiDeviceIndex = %d  \n", gStorageDeviceIndex));
            gStorageDeviceIndex++;
        }
        FreePool (InDataBuffer);
        FreePool (SenseData);
    }
    
    return Status;
}

/**
  @internal
  Fills iSCSI/SCSI/SAS Controller and Devices data
  
  @param [in][out] DeviceEntry             Pointer to PCI device entry
  @param [in]      ControllerHandle        PCI device Handle
  @param [in]      IsPhysicalDrive         BOOLEAN: Flag to indicate that the drive under the controller are attached
                                                    physically to the system or connected through network(iSCSI)
                                           TRUE  - Drives are attached physically to the machine.
                                           FLASE - Drives are attached to the system through network(Hence it is iSCSI storage device)
                                                   Controller data will be the network controller
 
  @retval EFI_STATUS                     - EFI_SUCCESS: Filling ScsiSasStorage data success
                                           EFI_UNSUPPORTED :The device does not support 
                                           the specified protocol. 
                                                        
  @endinternal
 */
EFI_STATUS
InitializeScsiSasStorageDevData (
  IN  OUT SYSTEM_INVENTORY_INFO_PROTOCOL    *SystemInventoryInfoProtocol,
  IN  OUT DEV_ENTRY                         *DeviceEntry,
  IN      EFI_HANDLE                        ControllerHandle,
  IN      BOOLEAN                           IsPhysicalDrive
)
{
    EFI_STATUS                      Status;
    EFI_HANDLE                      *HandleBuffer;
    EFI_HANDLE                      TempHandle;
    BOOLEAN                         ControllerDataInitDone;
    UINTN                           HandleCount; 
    UINTN                           Seg = 0;
    UINTN                           Bus = 0;
    UINTN                           Dev = 0;
    UINTN                           Fun = 0;
    UINT8                           BaseClass;
    UINT8                           SubClass;
    UINT8                           ProgInterface;
    UINTN                           Index;
    CHAR16                          *DevPathStr = NULL; 
    UINTN                           ScsiControllerIndex;
    DEV_ENTRY                       StorageControllerEntry;
    STORAGE_CONTROLLER_INFO         *ControllerDisplayInfo;
    EFI_PCI_IO_PROTOCOL             *PciIo;
    EFI_DEVICE_PATH_PROTOCOL        *DevPath;
    EFI_DEVICE_PATH_PROTOCOL        *TempDevPath;
    EFI_EXT_SCSI_PASS_THRU_PROTOCOL *ExtScsiPassThruProtocol = NULL;
    SYS_INV_PCI_INFO                *PciInfo;
    UINT32                          TotalStringLength = 0;
    UINT16                          StringIndex = 1;
    CHAR8                           TempStringBuffer[FixedPcdGet32 (PcdAmiSysInvMaxStringLength)];
    UINTN                           StringLength = 0;
    
    ZeroMem (TempStringBuffer,sizeof(TempStringBuffer));


    DEBUG ((DEBUG_INFO, "%a() Entry\n", __FUNCTION__));

    //Locate all ExtScsiPassThru protocol handle buffer 
    Status = gBS->LocateHandleBuffer (
                            ByProtocol,
                            &gEfiExtScsiPassThruProtocolGuid,
                            NULL,
                            &HandleCount,
                            &HandleBuffer );

    DEBUG ((DEBUG_INFO, "Locate ExtScsiPassThruProtocol, Status = %r ,Handle Count=%d\n",Status, HandleCount));
    
    if(EFI_ERROR(Status) || HandleCount == 0 ) {
        return Status;
    }

    ControllerDataInitDone = FALSE;

    for (Index = 0; Index < HandleCount; Index++) {
        
        Status = gBS->HandleProtocol(
                                HandleBuffer[Index], 
                                &gEfiDevicePathProtocolGuid, 
                                (VOID **)&DevPath );

        if(EFI_ERROR(Status)){
            DEBUG ((DEBUG_ERROR, "Can't Locate DevicePathProtocol on Handle[%d], Status = %r \n", Index, Status));
            continue;
        }

        TempDevPath = DevPath;

        Status = gBS->LocateDevicePath (
                                &gEfiPciIoProtocolGuid,
                                &TempDevPath,
                                &TempHandle );
        if(EFI_ERROR(Status)){
            DEBUG ((DEBUG_ERROR, "Can't get PciIo in given device path for Handle[%d], Status = %r \n", Index, Status));
            continue;
        }

        DEBUG ((DEBUG_INFO, "Controller handle = %x \n Temp Handle  = %x \n", ControllerHandle, TempHandle));

        if(TempHandle == ControllerHandle){
            
            DEBUG ((DEBUG_INFO, "Controller Handle match Found\n"));
            
            Status = gBS->HandleProtocol(
                                    HandleBuffer[Index], 
                                    &gEfiExtScsiPassThruProtocolGuid, 
                                    (VOID **)&ExtScsiPassThruProtocol );
            if(EFI_ERROR(Status)) {
                DEBUG ((DEBUG_ERROR, "Locate ExtScsiPassThru Protocol = %r\n", Status));
                return Status;
            }
            
            if(IsPhysicalDrive) {
//COMPAL_CHANGE >>>
#if defined(AMI_SYS_INV_FILTER_LOGICAL_STORAGE_DEVICE) && (AMI_SYS_INV_FILTER_LOGICAL_STORAGE_DEVICE == 1)
                if ((ExtScsiPassThruProtocol->Mode != NULL) && ((ExtScsiPassThruProtocol->Mode->Attributes & EFI_EXT_SCSI_PASS_THRU_ATTRIBUTES_PHYSICAL) == 0)){
                    DEBUG ((DEBUG_ERROR, " EFI_UNSUPPORTED: not a Physical device/Skipping logical device Attributes %X\n",ExtScsiPassThruProtocol->Mode->Attributes));
                    continue;
                }
#endif
//COMPAL_CHANGE <<<
                if(!ControllerDataInitDone) {

                    // Locate PciIo protocol to Confirm it is Controller Handle 
                    Status = gBS->HandleProtocol (
                                            ControllerHandle,
                                            &gEfiPciIoProtocolGuid,
                                            (VOID **)&PciIo );
                    if(!EFI_ERROR(Status)) {

                        Status = PciIo->GetLocation(PciIo, &Seg, &Bus, &Dev, &Fun);
                        if (EFI_ERROR(Status))
                           return Status;

                        Status = PciIo->Pci.Read(PciIo,EfiPciIoWidthUint8, PCI_BCC,1, &BaseClass );
                        if (EFI_ERROR(Status))
                           return Status;
                        Status = PciIo->Pci.Read(PciIo,EfiPciIoWidthUint8, PCI_SCC,1, &SubClass );
                        if (EFI_ERROR(Status))
                           return Status;
                        Status = PciIo->Pci.Read(PciIo,EfiPciIoWidthUint8, PCI_IFT,1, &ProgInterface );
                        if (EFI_ERROR(Status))
                           return Status;
                        // Check if this controller is SATA running in AHCI mode. This controller data will be collected
                        // using InitializeAtaStorageDevData() function using AtaPassThru protocol.
                        if((BaseClass == PCI_CLASS_MASS_STORAGE) && ((SubClass == PCI_CLASS_MASS_STORAGE_SATADPA))) {
                            return Status;
                        }

                        PciInfo = &DeviceEntry->DisplayPtr.Pci;
                        ZeroMem (&StorageControllerEntry, sizeof(DEV_ENTRY));
                        StorageStringBuffer = AllocateZeroPool(MaxStorageStrBuffSize);
                        if (StorageStringBuffer == NULL) {
                            DEBUG((DEBUG_ERROR, "%a : Memory allocation failed!!!\n", __FUNCTION__));
                            return Status;
                        }
                        TotalStringLength = 0;
                        StringIndex = 1;

                        StorageControllerEntry.Signature = DEV_ENTRY_SIGNATURE;
                        StorageControllerEntry.Dp.DeviceStatus.DeviceType = SysInvDevStorageController;
                        StorageControllerEntry.Dp.DeviceStatus.DeviceInstance = gStorageControllerIndex;
                        StorageControllerEntry.Dp.DeviceStatus.VirtualLed = 0x01;
                        StorageControllerEntry.Dp.DeviceStatus.DeviceState = SysInvDevEnabled;
                        StorageControllerEntry.DisplayPtr.StorageController.Status.State = SysInvDevEnabled;
                        StorageControllerEntry.DisplayPtr.StorageController.Status.Health = SysInvHealthOK;
                        StorageControllerEntry.DisplayPtr.StorageController.StorageIndex = gStorageUnitIndex;
                        StorageControllerEntry.DisplayPtr.StorageController.Status.ValidFlags.StatusVF1Param.StateValid = 1;
                        StorageControllerEntry.DisplayPtr.StorageController.Status.ValidFlags.StatusVF1Param.HealthValid = 1;
                        StorageControllerEntry.DisplayPtr.StorageController.Flags1.StorageControllerInfoVF1Param.StorageIndexValid = 1;
                        StorageControllerEntry.InfoSize = sizeof(STORAGE_CONTROLLER_INFO);
                        PciInfo->StorageControllerIndex = StorageControllerEntry.Dp.DeviceStatus.DeviceInstance;
                        PciInfo->Flags1.PcieDeviceFV1Param.StorageControllerIndexValid =1;

                        ControllerDisplayInfo = &StorageControllerEntry.DisplayPtr.StorageController;
                        ControllerDisplayInfo->ParentPciIndex = DeviceEntry->Dp.DeviceStatus.DeviceInstance;
                        ControllerDisplayInfo->Flags1.StorageControllerInfoVF1Param.ParentPciIndexValid = 1;
                        
                        // Fill it with 0xFF so that FF can be used as terminator. Consumers can scan until 0xFF.
                        SetMem (
                            &ControllerDisplayInfo->SupportedControllerProtocols, 
                            sizeof (ControllerDisplayInfo->SupportedControllerProtocols), 
                            gEndArrayTag );
                        SetMem (
                            &ControllerDisplayInfo->SupportedDeviceProtocols, 
                            sizeof (ControllerDisplayInfo->SupportedDeviceProtocols), 
                            gEndArrayTag ); 
                        
                        ControllerDisplayInfo->SupportedControllerProtocols[0] = SysInvProtocolPCIe;
                        ControllerDisplayInfo->SupportedControllerProtocolsValidArrayElementsCount = 1;

                        Status = gBS->HandleProtocol (
                                            ControllerHandle,
                                            &gEfiDevicePathProtocolGuid,
                                            (VOID **)&TempDevPath );

                        if (!EFI_ERROR(Status)) {
                            DevPathStr = ConvertDevicePathToText (TempDevPath, FALSE, TRUE);
                            if(DevPathStr) {
                               StorageControllerEntry.Dp.UefiDevPath = AllocateReservedZeroPool(
                                                                           StrnSizeS (DevPathStr, 
                                                                           PcdGet32 (PcdAmiSysInvMaxStringLength)) );
                               if (StorageControllerEntry.Dp.UefiDevPath != NULL)
                                   StrCpyS (StorageControllerEntry.Dp.UefiDevPath, 
                                            StrnSizeS (DevPathStr, 
                                                       PcdGet32 (PcdAmiSysInvMaxStringLength)), 
                                            DevPathStr);
                               FreePool(DevPathStr);
                            }
                        }
                        
                        StringLength = AsciiSPrint(
                                            TempStringBuffer,
                                            PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                            "%X",
                                            gStorageControllerIndex);
                        if (StringLength) {
                            StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                            if (StorageStringBuffer != NULL) {
                                AsciiStrCpyS(
                                        &StorageStringBuffer[TotalStringLength],
                                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                                        TempStringBuffer);
                                TotalStringLength += (UINT32)AsciiStrnSizeS (
                                                                &StorageStringBuffer[TotalStringLength], 
                                                                PcdGet32 (PcdAmiSysInvMaxStringLength) );
                                ControllerDisplayInfo->MemberIdStrIndex = StringIndex++;
                                ControllerDisplayInfo->Flags1.StorageControllerInfoVF1Param.MemberIdValid = TRUE;
                            }
                        }
                        if (BaseClass == PCI_CLASS_MASS_STORAGE && SubClass == PCI_CLASS_MASS_STORAGE_SAS && ProgInterface == 0){
                            //Its a sas controller
                            ControllerDisplayInfo->SupportedControllerProtocols[1] = SysInvProtocolSAS;
                            ControllerDisplayInfo->SupportedControllerProtocolsValidArrayElementsCount = 1;


                            ControllerDisplayInfo->SupportedDeviceProtocols[0] = SysInvProtocolSAS;
                            ControllerDisplayInfo->SupportedDeviceProtocolsValidArrayElementsCount = 1;

                            
                            StringLength = AsciiSPrint(
                                                TempStringBuffer,
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "SAS_Controller%X",
                                                gStorageControllerIndex);
                            if (StringLength) {
                                StrBuffAvailabilityCheck (&StorageStringBuffer,
                                                          &MaxStorageStrBuffSize,
                                                          StringLength,
                                                          TotalStringLength);
                                if (StorageStringBuffer != NULL) {
                                    AsciiStrCpyS(
                                            &StorageStringBuffer[TotalStringLength],
                                            PcdGet32 (PcdAmiSysInvMaxStringLength),
                                            TempStringBuffer);
                                    TotalStringLength += (UINT32)AsciiStrnSizeS (
                                                                    &StorageStringBuffer[TotalStringLength], 
                                                                    PcdGet32 (PcdAmiSysInvMaxStringLength) );
                                    ControllerDisplayInfo->NameStrIndex = StringIndex++;
                                    ControllerDisplayInfo->Flags1.StorageControllerInfoVF1Param.NameValid = TRUE;
                                }
                            }
                            StringLength = AsciiSPrint(
                                                TempStringBuffer,
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "SAS_Controller%X",
                                                gStorageControllerIndex);
                            if (StringLength) {
                                StrBuffAvailabilityCheck (&StorageStringBuffer,
                                                          &MaxStorageStrBuffSize,
                                                          StringLength,
                                                          TotalStringLength);
                                if (StorageStringBuffer != NULL) {
                                    AsciiStrCpyS(
                                            &StorageStringBuffer[TotalStringLength],
                                            PcdGet32 (PcdAmiSysInvMaxStringLength),
                                            TempStringBuffer);
                                    TotalStringLength += (UINT32)AsciiStrnSizeS (
                                                                    &StorageStringBuffer[TotalStringLength], 
                                                                    PcdGet32 (PcdAmiSysInvMaxStringLength) );
                                    ControllerDisplayInfo->AssetTagStrIndex = StringIndex++;
                                    ControllerDisplayInfo->Flags1.StorageControllerInfoVF1Param.AssetTagValid = TRUE;
                                }
                            }
                        }
                        
                        if (BaseClass == PCI_CLASS_MASS_STORAGE && 
                                (SubClass == PCI_CLASS_MASS_STORAGE_RAID || SubClass == PCI_CLASS_MASS_STORAGE_SCSI)        
                           && ProgInterface == 0){
                            //Its a SCSI controller
 
                            StringLength = AsciiSPrint(
                                                TempStringBuffer,
                                                PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                                "SCSI_Controller%X",
                                                gStorageControllerIndex );
                            if (StringLength) {
                                StrBuffAvailabilityCheck (
                                            &StorageStringBuffer,
                                            &MaxStorageStrBuffSize,
                                            StringLength,
                                            TotalStringLength );
                                if (StorageStringBuffer != NULL) {
                                    AsciiStrCpyS(
                                            &StorageStringBuffer[TotalStringLength],
                                            PcdGet32 (PcdAmiSysInvMaxStringLength),
                                            TempStringBuffer);
                                    TotalStringLength += (UINT32)AsciiStrnSizeS (
                                                                        &StorageStringBuffer[TotalStringLength], 
                                                                        PcdGet32 (PcdAmiSysInvMaxStringLength) );
                                    ControllerDisplayInfo->NameStrIndex = StringIndex++;
                                    ControllerDisplayInfo->Flags1.StorageControllerInfoVF1Param.NameValid = TRUE;
                                }
                            }
                            StringLength = AsciiSPrint(
                                            TempStringBuffer,
                                            PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                            "SCSI_Controller%X",
                                            gStorageControllerIndex);
                            if (StringLength) {
                                StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                                if (StorageStringBuffer != NULL) {
                                    AsciiStrCpyS(
                                            &StorageStringBuffer[TotalStringLength],
                                            PcdGet32 (PcdAmiSysInvMaxStringLength),
                                            TempStringBuffer);
                                    TotalStringLength += (UINT32)AsciiStrnSizeS (
                                                                    &StorageStringBuffer[TotalStringLength], 
                                                                    PcdGet32 (PcdAmiSysInvMaxStringLength) );
                                    ControllerDisplayInfo->AssetTagStrIndex = StringIndex++;
                                    ControllerDisplayInfo->Flags1.StorageControllerInfoVF1Param.AssetTagValid = TRUE;
                                }
                            }
                        }
                        StringLength = 0;
                        if ((ExtScsiPassThruProtocol->Mode != NULL) && 
                                ((ExtScsiPassThruProtocol->Mode->Attributes & EFI_EXT_SCSI_PASS_THRU_ATTRIBUTES_PHYSICAL) == 0)){
                            StringLength = AsciiSPrint(
                                            TempStringBuffer,
                                            PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                            "Logical Device %X",
                                            gStorageControllerIndex);

                        } else {
                            StringLength = AsciiSPrint(
                                            TempStringBuffer,
                                            PcdGet32 (PcdAmiSysInvMaxStringLength), 
                                            "Physical Device %X",
                                            gStorageControllerIndex);
                        }
                        
                        if (StringLength) {
                            StrBuffAvailabilityCheck (
                                    &StorageStringBuffer,
                                    &MaxStorageStrBuffSize,
                                    StringLength,
                                    TotalStringLength );
                            if (StorageStringBuffer != NULL) {
                                AsciiStrCpyS(
                                        &StorageStringBuffer[TotalStringLength],
                                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                                        TempStringBuffer);
                                TotalStringLength += (UINT32)AsciiStrnSizeS (
                                                                &StorageStringBuffer[TotalStringLength], 
                                                                PcdGet32 (PcdAmiSysInvMaxStringLength) );
                                ControllerDisplayInfo->DescriptionStrIndex = StringIndex++;
                                ControllerDisplayInfo->Flags1.StorageControllerInfoVF1Param.DescriptionValid = TRUE;
                            }
                        }

                        
                        StringLength = AsciiSPrint(TempStringBuffer, PcdGet32 (PcdAmiSysInvMaxStringLength), "Not Available");
                        if (StringLength) {
                            StrBuffAvailabilityCheck (
                                    &StorageStringBuffer,
                                    &MaxStorageStrBuffSize,
                                    StringLength,
                                    TotalStringLength );
                            if (StorageStringBuffer != NULL) {
                                AsciiStrCpyS(
                                        &StorageStringBuffer[TotalStringLength],
                                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                                        TempStringBuffer);
                                TotalStringLength += (UINT32)AsciiStrnSizeS (
                                                                &StorageStringBuffer[TotalStringLength], 
                                                                PcdGet32 (PcdAmiSysInvMaxStringLength) );
                                ControllerDisplayInfo->FirmwareVersionStrIndex = (StringIndex)++;
                                ControllerDisplayInfo->Flags1.StorageControllerInfoVF1Param.FirmwareVersionValid = TRUE;
                            }
                            StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                            if (StorageStringBuffer != NULL) {
                                AsciiStrCpyS(
                                        &StorageStringBuffer[TotalStringLength],
                                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                                        TempStringBuffer);
                                TotalStringLength += (UINT32)AsciiStrnSizeS (
                                                                &StorageStringBuffer[TotalStringLength], 
                                                                PcdGet32 (PcdAmiSysInvMaxStringLength) );
                                ControllerDisplayInfo->ModelStrIndex = (StringIndex)++;
                                ControllerDisplayInfo->Flags1.StorageControllerInfoVF1Param.ModelValid = TRUE;
                            }
                            StrBuffAvailabilityCheck (&StorageStringBuffer,&MaxStorageStrBuffSize,StringLength,TotalStringLength);
                            if (StorageStringBuffer != NULL) {
                                AsciiStrCpyS(
                                        &StorageStringBuffer[TotalStringLength],
                                        PcdGet32 (PcdAmiSysInvMaxStringLength),
                                        TempStringBuffer);
                                TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], 
                                        PcdGet32 (PcdAmiSysInvMaxStringLength));
                                ControllerDisplayInfo->SerialNumberStrIndex = (StringIndex)++;
                                ControllerDisplayInfo->Flags1.StorageControllerInfoVF1Param.SerialNumberValid = TRUE;
                            }
                        }
                        
                        Status = SysInvAddStrings (
                                            SystemInventoryInfoProtocol,
                                            &ControllerDisplayInfo->StringHdr,
                                            StorageStringBuffer);
                        if (EFI_ERROR(Status)) {
                            DEBUG((DEBUG_ERROR,"%a Failed to add string for Storage Controller instance %d = %r\n", 
                                    __FUNCTION__, gStorageControllerIndex, Status));
                        }
                        if (StorageStringBuffer != NULL){
                            FreePool(StorageStringBuffer);
                            StorageStringBuffer = NULL;
                            MaxStorageStrBuffSize = SIZE_8KB;
                        }
                        // OEM Hook to update SCSI Storage Controller Inventory Data
                        OemUpdateScsiStorageControllerInventory (SystemInventoryInfoProtocol, &StorageControllerEntry);

                        Status = AddDevEntryToList(
                                    (SYS_INV_ITEM_LIST *)&SystemInventoryInfoProtocol->DevInitialCnt, 
                                    &StorageControllerEntry, 
                                    sizeof(DEV_ENTRY) );
                        DEBUG ((DEBUG_INFO, "%a() AddDevEntryToList Status = %r, DevInfoCount = %d\n", 
                                __FUNCTION__,Status, SystemInventoryInfoProtocol->DevInfoCount));
                        if (EFI_ERROR (Status)) {
                            // Deleting String Database since AddDevEntryToList failed. 
                            SysInvDeleteStrings (
                                    SystemInventoryInfoProtocol,
                                    &StorageControllerEntry.DisplayPtr.StorageController.StringHdr);
                        }
                        
                        ScsiControllerIndex = SystemInventoryInfoProtocol->DevInfoCount - 1;
                                
                        gStorageControllerIndex++;
                        ControllerDataInitDone = TRUE;
                    }
                }
                // For Updating the drive details connected with the SCSI Controller.
                Status = FillScsiDevInfo(
                            SystemInventoryInfoProtocol,
                            ControllerHandle,
                            NULL,
                            ExtScsiPassThruProtocol,
                            DevPath,
                            IsPhysicalDrive,
                            ScsiControllerIndex );
                DEBUG ((DEBUG_INFO, "%a() FillScsiDevInfo %r\n", __FUNCTION__,Status));
            } else {
                // For Updating the drive details connected with the Network Controller through iSCSI.
                Status = FillScsiDevInfo(
                            SystemInventoryInfoProtocol,
                            ControllerHandle,
                            DeviceEntry,
                            ExtScsiPassThruProtocol,
                            DevPath,
                            IsPhysicalDrive,
                            0 );
                DEBUG ((DEBUG_INFO, "%a() FillScsiDevInfo %r\n", __FUNCTION__,Status));
            }
        }
    }
    
    DEBUG((DEBUG_INFO,"%a() Exit  Status-%r \n", __FUNCTION__,Status));
    return Status;
}


/**
 
  Fills Storage Devices (ATA, NVMe, USB, SAS & SCSI) data
   
  @param [in][out] PciDeviceEntry          Pointer to PCI device entry
  @param [in]      ControllerHandle        PCI device Handle

  @retval VOID
  
 */
VOID
InitializeStorageDevData (
  IN  OUT SYSTEM_INVENTORY_INFO_PROTOCOL      *SystemInventoryInfoProtocol,
  IN  OUT DEV_ENTRY         *PciDeviceEntry,
  IN      EFI_HANDLE        ControllerHandle
  )
{
    EFI_STATUS                          Status = EFI_NOT_FOUND;
    DEBUG((DEBUG_INFO,"%a() Entry \n", __FUNCTION__));
    
    if ((PciDeviceEntry->DisplayPtr.Pci.PcieFunctions.Class.DeviceClass == PCI_CLASS_MASS_STORAGE) && 
        (PciDeviceEntry->DisplayPtr.Pci.PcieFunctions.Class.ClassCode == PCI_CLASS_MASS_STORAGE_SATADPA ||
        PciDeviceEntry->DisplayPtr.Pci.PcieFunctions.Class.ClassCode == PCI_CLASS_MASS_STORAGE_ATA ||
        PciDeviceEntry->DisplayPtr.Pci.PcieFunctions.Class.ClassCode == PCI_CLASS_MASS_STORAGE_RAID)) {
        
        DEBUG((DEBUG_INFO, "ATA Device --- \n"));
        
        Status = InitializeAtaStorageDevData (SystemInventoryInfoProtocol, PciDeviceEntry, ControllerHandle);
        
        if (EFI_ERROR(Status)) {
            DEBUG ((DEBUG_ERROR, "%a() failed to update ATA/SATA device info Status = %r\n", __FUNCTION__, Status));
            DEBUG((DEBUG_INFO, "Try SCSI Device --- \n"));
            Status = InitializeScsiSasStorageDevData (
                        SystemInventoryInfoProtocol,
                        PciDeviceEntry,
                        ControllerHandle,
                        TRUE );
            if (EFI_ERROR(Status)) {
                DEBUG((DEBUG_ERROR,"%a InitializeScsiSasStorageDevData Status= %r\n", __FUNCTION__, Status));
            }
		InitializeStorageUnit (SystemInventoryInfoProtocol, PciDeviceEntry, ControllerHandle);//COMPAL_CHANGE
        goto Exit; //COMPAL_CHANGE
        }
        
    } else if ((PciDeviceEntry->DisplayPtr.Pci.PcieFunctions.Class.DeviceClass == PCI_CLASS_MASS_STORAGE) &&
            (PciDeviceEntry->DisplayPtr.Pci.PcieFunctions.Class.ClassCode == PCI_CLASS_MASS_STORAGE_SOLID_STATE )) {
        
        DEBUG((DEBUG_INFO, "Nvme Device --- \n"));
        
        Status = InitializeNvmeStorageDevData (
                        SystemInventoryInfoProtocol,
                        PciDeviceEntry,
                        ControllerHandle );
        if (EFI_ERROR(Status)) {
            DEBUG ((DEBUG_ERROR, "%a() failed to update Nvme device info Status = %r\n", __FUNCTION__, Status));
        }
        
    } else if ((PciDeviceEntry->DisplayPtr.Pci.PcieFunctions.Class.DeviceClass == PCI_CLASS_SERIAL) &&
            (PciDeviceEntry->DisplayPtr.Pci.PcieFunctions.Class.ClassCode == PCI_CLASS_SERIAL_USB )) {

        DEBUG((DEBUG_INFO, "USB Device --- \n"));

        Status = InitializeUsbStorageDevData (
                            SystemInventoryInfoProtocol,
                            PciDeviceEntry,
                            ControllerHandle );
        if (EFI_ERROR(Status)) {
            DEBUG ((DEBUG_ERROR, "%a() failed to update USB device info Status = %r\n", __FUNCTION__, Status));
        }

    } else if ((PciDeviceEntry->DisplayPtr.Pci.PcieFunctions.Class.DeviceClass == PCI_CLASS_MASS_STORAGE) &&
            (PciDeviceEntry->DisplayPtr.Pci.PcieFunctions.Class.ClassCode == PCI_CLASS_MASS_STORAGE_SAS)) {

        DEBUG((DEBUG_INFO, "SAS/SCSI Device --- \n"));

        Status = InitializeScsiSasStorageDevData (
                        SystemInventoryInfoProtocol,
                        PciDeviceEntry,
                        ControllerHandle,
                        TRUE );
        DEBUG ((DEBUG_INFO, "%a() SCSI Data Status :%r\n", __FUNCTION__,Status)); //COMPAL_CHANGE
        InitializeStorageUnit (SystemInventoryInfoProtocol, PciDeviceEntry, ControllerHandle);//COMPAL_CHANGE
        goto Exit; //tomlschien_20250617++
    }

    
    if (!EFI_ERROR(Status)) {
        /*
        From Generic System Inventory eModule, there will be only one storage unit with StorageIndex and will be added to the DevEntryList. 
        This StorageIndex is updated in the StorageController structure.
        Hierarchy will be  
        StorageUnit 0 ->
                      StorageControllers ->
                                         Drives ->
                                                Volumes 
        All StorageControllers/Drives/Volumes will be under StorageUnit.

        OEMs can add storage units using SystemInventory HOOK and if it is added, then OEMs need to find the respective 
        StorageUnit Devices in DeviceList Database and update the StorageIndex accordingly.
        */  
        InitializeStorageUnit (SystemInventoryInfoProtocol, PciDeviceEntry, ControllerHandle);
    }
    Exit:  //COMPAL_CHANGE    
    DEBUG((DEBUG_INFO,"%a() Exit Status: %r\n", __FUNCTION__, Status));
}

/**
  @internal

 Fills Storage information

  @param    Storage      Pointer to System Inventory Storage Info

  @retval   VOID
  
  @endinternal
**/
VOID
InitializeStorageUnit(
  IN OUT SYSTEM_INVENTORY_INFO_PROTOCOL    *SystemInventoryInfoProtocol,
  IN DEV_ENTRY                             *PcieDeviceEntry,
  IN EFI_HANDLE                            ControllerHandle
) 
{
    EFI_STATUS                        Status;
    DEV_ENTRY                         StorageUnitEntry;
    STORAGE                           *Storage;
    UINT32                            TotalStringLength = 0;
    UINT16                            StringIndex = 1;
                            
    ZeroMem (&StorageUnitEntry, sizeof(DEV_ENTRY));
    StorageStringBuffer = AllocateZeroPool(MaxStorageStrBuffSize);
    if (StorageStringBuffer == NULL) {
        DEBUG((DEBUG_ERROR, "%a : Memory allocation failed!!!\n", __FUNCTION__));
        return;
    }
    TotalStringLength = 0;
    StringIndex = 1;
    
    Storage = &StorageUnitEntry.DisplayPtr.Storage;
            
    StorageUnitEntry.Signature = DEV_ENTRY_SIGNATURE;
    StorageUnitEntry.Dp.DeviceStatus.DeviceType = SysInvDevStorage;
    StorageUnitEntry.Dp.DeviceStatus.DeviceInstance = gStorageUnitIndex;
    StorageUnitEntry.Dp.DeviceStatus.VirtualLed = 0x01;
    StorageUnitEntry.Dp.DeviceStatus.DeviceState = SysInvDevEnabled;
    StorageUnitEntry.DisplayPtr.Storage.Status.State = SysInvDevEnabled;
    StorageUnitEntry.InfoSize = sizeof(STORAGE);

    StorageUnitEntry.DisplayPtr.Storage.Status.ValidFlags.StatusVF1Param.StateValid = 1;
  
    AsciiSPrint(
            &StorageStringBuffer[TotalStringLength],
            PcdGet32 (PcdAmiSysInvMaxStringLength), 
            "StorageUnit_%X",
            gStorageUnitIndex);
    TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], 
                                                 PcdGet32 (PcdAmiSysInvMaxStringLength));
    Storage->IdStrIndex = StringIndex++;
    Storage->ValidFlag1.StorageVF1Param.IdValid = TRUE;
    
    AsciiSPrint(
            &StorageStringBuffer[TotalStringLength],
            PcdGet32 (PcdAmiSysInvMaxStringLength), 
            "StorageUnit_%X", 
            gStorageUnitIndex);
    TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], 
                                                 PcdGet32 (PcdAmiSysInvMaxStringLength));
    Storage->NameStrIndex = (StringIndex)++;
    Storage->ValidFlag1.StorageVF1Param.NameValid = TRUE;    
    
    AsciiSPrint(
            &StorageStringBuffer[TotalStringLength],
            PcdGet32 (PcdAmiSysInvMaxStringLength), 
            "Storage%X", 
            gStorageUnitIndex);
    TotalStringLength += (UINT32)AsciiStrnSizeS (&StorageStringBuffer[TotalStringLength], 
                                                 PcdGet32 (PcdAmiSysInvMaxStringLength));
    Storage->DescriptionStrIndex = (StringIndex)++;
    Storage->ValidFlag1.StorageVF1Param.DescriptionValid = TRUE;
    
    Status = SysInvAddStrings (
                        SystemInventoryInfoProtocol,
                        &Storage->StringHdr,
                        StorageStringBuffer);
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR,"%a Failed to add string for Storage Instance : %d = %r\n", 
                __FUNCTION__, gStorageUnitIndex, Status));
    }
    if (StorageStringBuffer != NULL){
        FreePool(StorageStringBuffer);
        StorageStringBuffer = NULL;
        MaxStorageStrBuffSize = SIZE_8KB;
    }
    // OEM Hook to update Storage Unit Inventory Data
    OemUpdateStorageUnitInventory (SystemInventoryInfoProtocol, &StorageUnitEntry);
    
    Status = AddDevEntryToList(
                (SYS_INV_ITEM_LIST *)&SystemInventoryInfoProtocol->DevInitialCnt, 
                &StorageUnitEntry, 
                sizeof (DEV_ENTRY));
    DEBUG ((DEBUG_INFO, "%a() AddDevEntryToList Storage unit Status = %r, DevInfoCount = %d\n", 
            __FUNCTION__,Status, SystemInventoryInfoProtocol->DevInfoCount));
    
    SystemInventoryInfoProtocol->DataValidFlags.StorageValid = TRUE;
    gStorageUnitIndex++;
    return; 
}


/**

  Updates StorageController string pointer from string Data base

  @param  SystemInventoryInfoProtocol - Pointer to System Info Protocol
  @param  DevEntry                    - Pointer to a CPU Device entry
  
  @retval Status  

**/
EFI_STATUS
StorageControllerStrPtrUpdate(
  IN OUT SYSTEM_INVENTORY_INFO_PROTOCOL      *SystemInventoryInfoProtocol,
  IN OUT DEV_ENTRY                           *DevEntry
  )
{
    EFI_STATUS                      Status;
    STORAGE_CONTROLLER_INFO         *StorageContollerInfo;
    CHAR8                           *InfoStringBuffer;
    UINT8                           Index;
    
    StorageContollerInfo =  &DevEntry->DisplayPtr.StorageController;
    
    Status = SysInvGetStringsDBPtr (
                        SystemInventoryInfoProtocol,
                        &StorageContollerInfo->StringHdr,
                        &InfoStringBuffer );

    if (EFI_ERROR(Status) || InfoStringBuffer == NULL)
        return Status;

    //STORAGE_CONTROLLER_INFO String Pointer update : Start 
    if (StorageContollerInfo->Flags1.StorageControllerInfoVF1Param.SpeedGbpsValid)
        StorageContollerInfo->SpeedGbps = SysInvGetStringPtrByIndex (InfoStringBuffer,StorageContollerInfo->SpeedGbpsStrIndex);
    
    if (StorageContollerInfo->Flags1.StorageControllerInfoVF1Param.SerialNumberValid)
        StorageContollerInfo->SerialNumber = SysInvGetStringPtrByIndex (InfoStringBuffer,StorageContollerInfo->SerialNumberStrIndex);
    
    if (StorageContollerInfo->Flags1.StorageControllerInfoVF1Param.SKUValid)
        StorageContollerInfo->SKU = SysInvGetStringPtrByIndex (InfoStringBuffer,StorageContollerInfo->SKUStrIndex);
    
    if (StorageContollerInfo->Flags1.StorageControllerInfoVF1Param.AssetTagValid)
        StorageContollerInfo->AssetTag = SysInvGetStringPtrByIndex (InfoStringBuffer,StorageContollerInfo->AssetTagStrIndex);
    
    if (StorageContollerInfo->Flags1.StorageControllerInfoVF1Param.FirmwareVersionValid)
        StorageContollerInfo->FirmwareVersion = SysInvGetStringPtrByIndex (InfoStringBuffer,StorageContollerInfo->FirmwareVersionStrIndex);
    
    if (StorageContollerInfo->Flags1.StorageControllerInfoVF1Param.ManufacturerValid)
        StorageContollerInfo->Manufacturer = SysInvGetStringPtrByIndex (InfoStringBuffer,StorageContollerInfo->ManufacturerStrIndex);
    
    if (StorageContollerInfo->Flags1.StorageControllerInfoVF1Param.MemberIdValid)
        StorageContollerInfo->MemberId = SysInvGetStringPtrByIndex (InfoStringBuffer,StorageContollerInfo->MemberIdStrIndex);
    
    if (StorageContollerInfo->Flags1.StorageControllerInfoVF1Param.ModelValid)
        StorageContollerInfo->Model = SysInvGetStringPtrByIndex (InfoStringBuffer,StorageContollerInfo->ModelStrIndex);
    
    if (StorageContollerInfo->Flags1.StorageControllerInfoVF1Param.NameValid)
        StorageContollerInfo->Name = SysInvGetStringPtrByIndex (InfoStringBuffer,StorageContollerInfo->NameStrIndex);
    
    if (StorageContollerInfo->Flags1.StorageControllerInfoVF1Param.PartNumberValid)
        StorageContollerInfo->PartNumber = SysInvGetStringPtrByIndex (InfoStringBuffer,StorageContollerInfo->PartNumberStrIndex);
    
    if (StorageContollerInfo->Flags1.StorageControllerInfoVF1Param.DescriptionValid)
        StorageContollerInfo->Description = SysInvGetStringPtrByIndex (InfoStringBuffer,StorageContollerInfo->DescriptionStrIndex);
    
//STORAGE_CONTROLLER_INFO String Pointer update : End
    
    // Assembley
    AssembleyStrPtrUpdate(&StorageContollerInfo->Assembly,InfoStringBuffer);
    //Assembley 
    
//IDENTIFIERS String Pointer update : Start
    for (Index = 0; Index < StorageContollerInfo->IdentifiersValidArrayElementsCount ;Index++) {
        if (StorageContollerInfo->Identifiers[Index].ValidFlag1.IdentifierVF1Param.DurableNameValid) {
            StorageContollerInfo->Identifiers[Index].DurableName = SysInvGetStringPtrByIndex (
                                                                        InfoStringBuffer,
                                                                        StorageContollerInfo->Identifiers[Index].DurableNameStrIndex );
        }
    }
//IDENTIFIERS String Pointer update : End
    
    //NVME_CONTROLLER_PROPERTIES 
    if (StorageContollerInfo->NVMeControllerProperties.Flags1.NVMeControllerPropertiesVF1Param.NVMeVersionValid){
        StorageContollerInfo->NVMeControllerProperties.NVMeVersion = SysInvGetStringPtrByIndex (
                                                                        InfoStringBuffer,
                                                                        StorageContollerInfo->NVMeControllerProperties.NVMeVersionStrIndex );
    }
    //NVME_CONTROLLER_PROPERTIES End
    return Status;
}

/**

  Updates StorageDisk string pointer from string Data base

  @param  SystemInventoryInfoProtocol - Pointer to System Info Protocol
  @param  DevEntry                    - Pointer to a CPU Device entry
  
  @retval Status  

**/
EFI_STATUS
StorageDiskStrPtrUpdate(
  IN OUT SYSTEM_INVENTORY_INFO_PROTOCOL      *SystemInventoryInfoProtocol,
  IN OUT DEV_ENTRY                           *DevEntry
  )
{
    EFI_STATUS                      Status;
    STORAGE_DEVICE_INFO             *StorageDeviceInfo;
    CHAR8                           *InfoStringBuffer;
    UINT8                           Index;
    
    StorageDeviceInfo =  &DevEntry->DisplayPtr.StorageDevice;
    
    Status = SysInvGetStringsDBPtr (
                        SystemInventoryInfoProtocol,
                        &StorageDeviceInfo->StringHdr,
                        &InfoStringBuffer );

    if (EFI_ERROR(Status) || InfoStringBuffer == NULL)
        return Status;
    
//STORAGE_DEVICE_INFO  String Pointer update :  Start
    if (StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.CapableSpeedGbsValid) {
        StorageDeviceInfo->CapableSpeedGbs = SysInvGetStringPtrByIndex (
                                                InfoStringBuffer,
                                                StorageDeviceInfo->CapableSpeedGbsStrIndex);
    }
    if (StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.NegotiatedSpeedGbsValid){
        StorageDeviceInfo->NegotiatedSpeedGbs =  SysInvGetStringPtrByIndex (
                                                    InfoStringBuffer,
                                                    StorageDeviceInfo->NegotiatedSpeedGbsStrIndex );
    }
    if (StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.FirmwareRevStringValid){
        StorageDeviceInfo->FirmwareRevString = SysInvGetStringPtrByIndex (
                                                    InfoStringBuffer,
                                                    StorageDeviceInfo->FirmwareRevStringStrIndex );
    }
    if (StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.RevisionValid)
        StorageDeviceInfo->Revision = 
                SysInvGetStringPtrByIndex (InfoStringBuffer,StorageDeviceInfo->RevisionStrIndex );
    
    if (StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.ManufacturerValid){
        StorageDeviceInfo->Manufacturer = SysInvGetStringPtrByIndex (
                                                InfoStringBuffer,
                                                StorageDeviceInfo->ManufacturerStrIndex );
    }
    if (StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.SerialNumberValid){
        StorageDeviceInfo->SerialNumber = SysInvGetStringPtrByIndex (
                                            InfoStringBuffer,
                                            StorageDeviceInfo->SerialNumberStrIndex );
    }
    if (StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.LocationValid){
        StorageDeviceInfo->Location = SysInvGetStringPtrByIndex (
                                            InfoStringBuffer,
                                            StorageDeviceInfo->LocationStrIndex);
    }
    if (StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.SKUValid){
        StorageDeviceInfo->SKU = SysInvGetStringPtrByIndex (
                                    InfoStringBuffer,
                                    StorageDeviceInfo->SKUStrIndex );
    }
    if (StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.AssetTagValid){
        StorageDeviceInfo->AssetTag = SysInvGetStringPtrByIndex (
                                            InfoStringBuffer,
                                            StorageDeviceInfo->AssetTagStrIndex );
    }
    if (StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.ModelValid){
        StorageDeviceInfo->Model = SysInvGetStringPtrByIndex (
                                        InfoStringBuffer,
                                        StorageDeviceInfo->ModelStrIndex );
    }
    if (StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.PartNumberValid){
        StorageDeviceInfo->PartNumber = SysInvGetStringPtrByIndex (
                                            InfoStringBuffer,
                                            StorageDeviceInfo->PartNumberStrIndex );
    }
    if (StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.IdValid){
        StorageDeviceInfo->Id = SysInvGetStringPtrByIndex (
                                    InfoStringBuffer,
                                    StorageDeviceInfo->IdStrIndex);
    }
    if (StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.NameValid){
        StorageDeviceInfo->Name = SysInvGetStringPtrByIndex (
                                        InfoStringBuffer,
                                        StorageDeviceInfo->NameStrIndex );
    }
    if (StorageDeviceInfo->ValidFlags1.StorageDeViInfoVF1Param.DescriptionValid){
        StorageDeviceInfo->Description = SysInvGetStringPtrByIndex (
                                            InfoStringBuffer,
                                            StorageDeviceInfo->DescriptionStrIndex );
    }
//STORAGE_DEVICE_INFO  String Pointer update : End
    
// Assembley String Pointer update : Start
    AssembleyStrPtrUpdate(&StorageDeviceInfo->Assembly,InfoStringBuffer);
//Assembley String Pointer update : End
    
//IDENTIFIERS String Pointer update : Start
    for (Index = 0; Index < StorageDeviceInfo->IdentifiersValidArrayElementsCount ;Index++) {
        if (StorageDeviceInfo->Identifiers[Index].ValidFlag1.IdentifierVF1Param.DurableNameValid){
            StorageDeviceInfo->Identifiers[Index].DurableName = SysInvGetStringPtrByIndex (
                                                                    InfoStringBuffer,
                                                                    StorageDeviceInfo->Identifiers[Index].DurableNameStrIndex);
        }
    }
//IDENTIFIERS String Pointer update : End
    
//OPERATIONS String Pointer update : Start
    for (Index = 0; Index < StorageDeviceInfo->OperationsValidArrayElementsCount ;Index++) {
        if (StorageDeviceInfo->Operations[Index].ValidFlags1.OperationVF1Param.OperationNameValid){
            StorageDeviceInfo->Operations[Index].OperationName = SysInvGetStringPtrByIndex (
                                                                    InfoStringBuffer,
                                                                    StorageDeviceInfo->Operations[Index].OperationNameStrIndex );
        }
        //ASSOCIATED_TASK Start
        if (StorageDeviceInfo->Operations[Index].AssociatedTask.ValidFlags1.AssoTaskFV1Param.StartTimeValid){
            StorageDeviceInfo->Operations[Index].AssociatedTask.StartTime = SysInvGetStringPtrByIndex (
                    InfoStringBuffer,StorageDeviceInfo->Operations[Index].AssociatedTask.StartTimeStrIndex );
        }
        if (StorageDeviceInfo->Operations[Index].AssociatedTask.ValidFlags1.AssoTaskFV1Param.EndTimeValid){
            StorageDeviceInfo->Operations[Index].AssociatedTask.EndTime = SysInvGetStringPtrByIndex (
                    InfoStringBuffer,StorageDeviceInfo->Operations[Index].AssociatedTask.EndTimeStrIndex );
        }
        if (StorageDeviceInfo->Operations[Index].AssociatedTask.ValidFlags1.AssoTaskFV1Param.TaskMonitorValid){
            StorageDeviceInfo->Operations[Index].AssociatedTask.TaskMonitor = SysInvGetStringPtrByIndex (
                    InfoStringBuffer,StorageDeviceInfo->Operations[Index].AssociatedTask.TaskMonitorStrIndex );
        }
        if (StorageDeviceInfo->Operations[Index].AssociatedTask.ValidFlags1.AssoTaskFV1Param.IdValid){
            StorageDeviceInfo->Operations[Index].AssociatedTask.Id = SysInvGetStringPtrByIndex (
                    InfoStringBuffer,StorageDeviceInfo->Operations[Index].AssociatedTask.IdStrIndex );
        }
        if (StorageDeviceInfo->Operations[Index].AssociatedTask.ValidFlags1.AssoTaskFV1Param.NameValid){
            StorageDeviceInfo->Operations[Index].AssociatedTask.Name = SysInvGetStringPtrByIndex (
                    InfoStringBuffer,StorageDeviceInfo->Operations[Index].AssociatedTask.NameStrIndex );
        }
        if (StorageDeviceInfo->Operations[Index].AssociatedTask.ValidFlags1.AssoTaskFV1Param.DescriptionValid){
            StorageDeviceInfo->Operations[Index].AssociatedTask.Description = SysInvGetStringPtrByIndex (
                    InfoStringBuffer,StorageDeviceInfo->Operations[Index].AssociatedTask.DescriptionStrIndex );
        }
            //PAYLOAD start
        if (StorageDeviceInfo->Operations[Index].AssociatedTask.PayLoad.ValidFlags1.PayloadVF1Param.HttpHeadersValid){
            StorageDeviceInfo->Operations[Index].AssociatedTask.PayLoad.HttpHeaders = SysInvGetStringPtrByIndex (
                    InfoStringBuffer,StorageDeviceInfo->Operations[Index].AssociatedTask.PayLoad.HttpHeadersStrIndex );
        }
        if (StorageDeviceInfo->Operations[Index].AssociatedTask.PayLoad.ValidFlags1.PayloadVF1Param.HttpOperationValid){
            StorageDeviceInfo->Operations[Index].AssociatedTask.PayLoad.HttpOperation = SysInvGetStringPtrByIndex (
                    InfoStringBuffer,StorageDeviceInfo->Operations[Index].AssociatedTask.PayLoad.HttpOperationStrIndex );
        }
        if (StorageDeviceInfo->Operations[Index].AssociatedTask.PayLoad.ValidFlags1.PayloadVF1Param.JsonBodyValid){
            StorageDeviceInfo->Operations[Index].AssociatedTask.PayLoad.JsonBody = SysInvGetStringPtrByIndex (
                    InfoStringBuffer,StorageDeviceInfo->Operations[Index].AssociatedTask.PayLoad.JsonBodyStrIndex);
        }
        if (StorageDeviceInfo->Operations[Index].AssociatedTask.PayLoad.ValidFlags1.PayloadVF1Param.TargetUriValid){
            StorageDeviceInfo->Operations[Index].AssociatedTask.PayLoad.TargetUri = SysInvGetStringPtrByIndex (
                    InfoStringBuffer,StorageDeviceInfo->Operations[Index].AssociatedTask.PayLoad.TargetUriStrIndex);
        }
            // PAYLOAD end
        //ASSOCIATED_TASK end
        
    }
//OPERATIONS String Pointer update : End
    
    //NVME_NAME_SPACE_PROPERTIES Internal usage
    if (StorageDeviceInfo->NVMeNamespaceProperties.ValidFlag1.NVMeNamespacePropertiesVF1Param.FormattedLBASizeValid){
        StorageDeviceInfo->NVMeNamespaceProperties.FormattedLBASize = SysInvGetStringPtrByIndex (
                InfoStringBuffer,StorageDeviceInfo->NVMeNamespaceProperties.FormattedLBASizeStrIndex);
    }
    if (StorageDeviceInfo->NVMeNamespaceProperties.ValidFlag1.NVMeNamespacePropertiesVF1Param.NVMeVersionValid){
        StorageDeviceInfo->NVMeNamespaceProperties.NVMeVersion = SysInvGetStringPtrByIndex (
                InfoStringBuffer,StorageDeviceInfo->NVMeNamespaceProperties.NVMeVersionStrIndex);
    }
    if (StorageDeviceInfo->NVMeNamespaceProperties.ValidFlag1.NVMeNamespacePropertiesVF1Param.NamespaceIdValid){
        StorageDeviceInfo->NVMeNamespaceProperties.NamespaceId = SysInvGetStringPtrByIndex (
                InfoStringBuffer,StorageDeviceInfo->NVMeNamespaceProperties.NamespaceIdStrIndex);
    }

    return Status;
}

/**

  Updates StorageVolume string pointer from string Data base

  @param  SystemInventoryInfoProtocol - Pointer to System Info Protocol
  @param  DevEntry                    - Pointer to a CPU Device entry
  
  @retval Status  

**/
EFI_STATUS
StorageVolumeStrPtrUpdate(
  IN OUT SYSTEM_INVENTORY_INFO_PROTOCOL      *SystemInventoryInfoProtocol,
  IN OUT DEV_ENTRY                           *DevEntry
  )
{
    EFI_STATUS                      Status;
    STORAGE_VOLUME_INFO             *StorageVolumeInfo;
    CHAR8                           *InfoStringBuffer;
    UINT8                           Index;

    StorageVolumeInfo =  &DevEntry->DisplayPtr.StorageVolume;
    
    Status = SysInvGetStringsDBPtr (
                        SystemInventoryInfoProtocol,
                        &StorageVolumeInfo->StringHdr,
                        &InfoStringBuffer );

    if (EFI_ERROR(Status) || InfoStringBuffer == NULL)
        return Status;
    
//STORAGE_VOLUME_INFO Start
    if (StorageVolumeInfo->ValidFlag1.StorageVolInfoVF1Param.IdValid)
        StorageVolumeInfo->Id = SysInvGetStringPtrByIndex (InfoStringBuffer,StorageVolumeInfo->IdStrIndex );
    
    if (StorageVolumeInfo->ValidFlag1.StorageVolInfoVF1Param.NameValid)
        StorageVolumeInfo->Name = SysInvGetStringPtrByIndex (InfoStringBuffer,StorageVolumeInfo->NameStrIndex );
    
    if (StorageVolumeInfo->ValidFlag1.StorageVolInfoVF1Param.DescriptionValid)
        StorageVolumeInfo->Description = SysInvGetStringPtrByIndex (InfoStringBuffer,StorageVolumeInfo->DescriptionStrIndex );
    
    if (StorageVolumeInfo->ValidFlag1.StorageVolInfoVF1Param.VolumeLabelValid)
        StorageVolumeInfo->VolumeLabel = SysInvGetStringPtrByIndex (InfoStringBuffer,StorageVolumeInfo->VolumeLabelStrIndex );
    
    if (StorageVolumeInfo->ValidFlag1.StorageVolInfoVF1Param.DisplayNameValid)
        StorageVolumeInfo->DisplayName = SysInvGetStringPtrByIndex (InfoStringBuffer,StorageVolumeInfo->DisplayNameStrIndex );
    
    if (StorageVolumeInfo->ValidFlag1.StorageVolInfoVF1Param.ManufacturerValid)
        StorageVolumeInfo->Manufacturer = SysInvGetStringPtrByIndex (InfoStringBuffer,StorageVolumeInfo->ManufacturerStrIndex );
    
    if (StorageVolumeInfo->ValidFlag1.StorageVolInfoVF1Param.ModelValid)
        StorageVolumeInfo->Model = SysInvGetStringPtrByIndex (InfoStringBuffer,StorageVolumeInfo->ModelStrIndex );
    
//STORAGE_VOLUME_INFO End
    
//IDENTIFIERS String Pointer update : Start
    for (Index = 0; Index < StorageVolumeInfo->IdentifiersValidArrayElementsCount ;Index++) {
        if (StorageVolumeInfo->Identifiers[Index].ValidFlag1.IdentifierVF1Param.DurableNameValid){
            StorageVolumeInfo->Identifiers[Index].DurableName = SysInvGetStringPtrByIndex (
                    InfoStringBuffer,StorageVolumeInfo->Identifiers[Index].DurableNameStrIndex );
        }
    }
//IDENTIFIERS String Pointer update : End
    
//OPERATIONS String Pointer update : Start
    for (Index = 0; Index < StorageVolumeInfo->OperationsValidArrayElementsCount ;Index++) {
        if (StorageVolumeInfo->Operations[Index].ValidFlags1.OperationVF1Param.OperationNameValid){
            StorageVolumeInfo->Operations[Index].OperationName = SysInvGetStringPtrByIndex (
                    InfoStringBuffer,StorageVolumeInfo->Operations[Index].OperationNameStrIndex );
        }
        //ASSOCIATED_TASK Start
        if (StorageVolumeInfo->Operations[Index].AssociatedTask.ValidFlags1.AssoTaskFV1Param.StartTimeValid){
            StorageVolumeInfo->Operations[Index].AssociatedTask.StartTime = SysInvGetStringPtrByIndex (
                                InfoStringBuffer,StorageVolumeInfo->Operations[Index].AssociatedTask.StartTimeStrIndex);
        }
        if (StorageVolumeInfo->Operations[Index].AssociatedTask.ValidFlags1.AssoTaskFV1Param.EndTimeValid){
            StorageVolumeInfo->Operations[Index].AssociatedTask.EndTime = SysInvGetStringPtrByIndex (
                    InfoStringBuffer,StorageVolumeInfo->Operations[Index].AssociatedTask.EndTimeStrIndex);
        }
        if (StorageVolumeInfo->Operations[Index].AssociatedTask.ValidFlags1.AssoTaskFV1Param.TaskMonitorValid){
            StorageVolumeInfo->Operations[Index].AssociatedTask.TaskMonitor = SysInvGetStringPtrByIndex (
                    InfoStringBuffer,StorageVolumeInfo->Operations[Index].AssociatedTask.TaskMonitorStrIndex );
        }
        if (StorageVolumeInfo->Operations[Index].AssociatedTask.ValidFlags1.AssoTaskFV1Param.IdValid){
            StorageVolumeInfo->Operations[Index].AssociatedTask.Id = SysInvGetStringPtrByIndex (
                    InfoStringBuffer,StorageVolumeInfo->Operations[Index].AssociatedTask.IdStrIndex );
        }
        if (StorageVolumeInfo->Operations[Index].AssociatedTask.ValidFlags1.AssoTaskFV1Param.NameValid){
            StorageVolumeInfo->Operations[Index].AssociatedTask.Name = SysInvGetStringPtrByIndex (
                    InfoStringBuffer,StorageVolumeInfo->Operations[Index].AssociatedTask.NameStrIndex );
        }
        if (StorageVolumeInfo->Operations[Index].AssociatedTask.ValidFlags1.AssoTaskFV1Param.DescriptionValid){
            StorageVolumeInfo->Operations[Index].AssociatedTask.Description = SysInvGetStringPtrByIndex (
                    InfoStringBuffer,StorageVolumeInfo->Operations[Index].AssociatedTask.DescriptionStrIndex );
        }
            //PAYLOAD start
        if (StorageVolumeInfo->Operations[Index].AssociatedTask.PayLoad.ValidFlags1.PayloadVF1Param.HttpHeadersValid){
            StorageVolumeInfo->Operations[Index].AssociatedTask.PayLoad.HttpHeaders = SysInvGetStringPtrByIndex (
                    InfoStringBuffer,StorageVolumeInfo->Operations[Index].AssociatedTask.PayLoad.HttpHeadersStrIndex );
        }
        if (StorageVolumeInfo->Operations[Index].AssociatedTask.PayLoad.ValidFlags1.PayloadVF1Param.HttpOperationValid){
            StorageVolumeInfo->Operations[Index].AssociatedTask.PayLoad.HttpOperation = SysInvGetStringPtrByIndex (
                    InfoStringBuffer,StorageVolumeInfo->Operations[Index].AssociatedTask.PayLoad.HttpOperationStrIndex );
        }
        if (StorageVolumeInfo->Operations[Index].AssociatedTask.PayLoad.ValidFlags1.PayloadVF1Param.JsonBodyValid){
            StorageVolumeInfo->Operations[Index].AssociatedTask.PayLoad.JsonBody = SysInvGetStringPtrByIndex (
                    InfoStringBuffer,StorageVolumeInfo->Operations[Index].AssociatedTask.PayLoad.JsonBodyStrIndex );
        }
        if (StorageVolumeInfo->Operations[Index].AssociatedTask.PayLoad.ValidFlags1.PayloadVF1Param.TargetUriValid){
            StorageVolumeInfo->Operations[Index].AssociatedTask.PayLoad.TargetUri = SysInvGetStringPtrByIndex (
                    InfoStringBuffer,StorageVolumeInfo->Operations[Index].AssociatedTask.PayLoad.TargetUriStrIndex );
        }
            // PAYLOAD end
        //ASSOCIATED_TASK end
        
    }
//OPERATIONS String Pointer update : End

// REPLICA_INFO String Pointer update : Start

    if (StorageVolumeInfo->ReplicaInfo.ValidFlag.ReplicaInfoVF1Param.WhenActivatedValid){
        StorageVolumeInfo->ReplicaInfo.WhenActivated = SysInvGetStringPtrByIndex (
                InfoStringBuffer,StorageVolumeInfo->ReplicaInfo.WhenActivatedStrIndex );
    }
    if (StorageVolumeInfo->ReplicaInfo.ValidFlag.ReplicaInfoVF1Param.WhenDeactivatedValid){
        StorageVolumeInfo->ReplicaInfo.WhenDeactivated = SysInvGetStringPtrByIndex (
                InfoStringBuffer,StorageVolumeInfo->ReplicaInfo.WhenDeactivatedStrIndex );
    }
    if (StorageVolumeInfo->ReplicaInfo.ValidFlag.ReplicaInfoVF1Param.WhenEstablishedValid){
        StorageVolumeInfo->ReplicaInfo.WhenEstablished = SysInvGetStringPtrByIndex (
                InfoStringBuffer,StorageVolumeInfo->ReplicaInfo.WhenEstablishedStrIndex );
    }
    if (StorageVolumeInfo->ReplicaInfo.ValidFlag.ReplicaInfoVF1Param.WhenSuspendedValid){
        StorageVolumeInfo->ReplicaInfo.WhenSuspended = SysInvGetStringPtrByIndex (
                InfoStringBuffer,StorageVolumeInfo->ReplicaInfo.WhenSuspendedStrIndex );
    }
    if (StorageVolumeInfo->ReplicaInfo.ValidFlag.ReplicaInfoVF1Param.WhenSyncedValid){
        StorageVolumeInfo->ReplicaInfo.WhenSynced = SysInvGetStringPtrByIndex (
                InfoStringBuffer,StorageVolumeInfo->ReplicaInfo.WhenSyncedStrIndex );
    }
    if (StorageVolumeInfo->ReplicaInfo.ValidFlag.ReplicaInfoVF1Param.WhenSynchronizedValid){
        StorageVolumeInfo->ReplicaInfo.WhenSynchronized = SysInvGetStringPtrByIndex (
                InfoStringBuffer,StorageVolumeInfo->ReplicaInfo.WhenSynchronizedStrIndex );
    }
// REPLICA_INFO String Pointer update : End
    
    //NVME_NAME_SPACE_PROPERTIES 
    if (StorageVolumeInfo->NVMeNamespaceProperties.ValidFlag1.NVMeNamespacePropertiesVF1Param.FormattedLBASizeValid) {
        StorageVolumeInfo->NVMeNamespaceProperties.FormattedLBASize = SysInvGetStringPtrByIndex (
                InfoStringBuffer,StorageVolumeInfo->NVMeNamespaceProperties.FormattedLBASizeStrIndex);
    }
    if (StorageVolumeInfo->NVMeNamespaceProperties.ValidFlag1.NVMeNamespacePropertiesVF1Param.NVMeVersionValid){
        StorageVolumeInfo->NVMeNamespaceProperties.NVMeVersion = SysInvGetStringPtrByIndex (
                InfoStringBuffer,StorageVolumeInfo->NVMeNamespaceProperties.NVMeVersionStrIndex);
    }

    if (StorageVolumeInfo->NVMeNamespaceProperties.ValidFlag1.NVMeNamespacePropertiesVF1Param.NamespaceIdValid){
        StorageVolumeInfo->NVMeNamespaceProperties.NamespaceId = SysInvGetStringPtrByIndex (
                InfoStringBuffer,StorageVolumeInfo->NVMeNamespaceProperties.NamespaceIdStrIndex );
    }

    return Status;
}

/**
 * Function check for All the storage Sub instance Status and update the final status.
 */
VOID
StorageStausUpdate(
  IN OUT SYSTEM_INVENTORY_INFO_PROTOCOL      *SystemInventoryInfoProtocol,
  IN OUT DEV_ENTRY                           *DevEntry
  ) {
    STATUS                        Status;
    UINTN                         DevIndexStorageController = 0;
    UINTN                         DevIndexStorageDrive = 0;
    UINTN                         DevIndexStorageVol = 0;
    STORAGE_CONTROLLER_INFO       *StorageController;
    STORAGE_DEVICE_INFO           *StorageDevice;
    STORAGE_VOLUME_INFO           *StorageVolume; 
    
    Status.State = SysInvDevEnabled;
    Status.Health = SysInvHealthOK;
    Status.ValidFlags.StatusVF1Param.HealthValid = 1;
    Status.ValidFlags.StatusVF1Param.StateValid = 1;
    
    // Check for StorageController status
    for (DevIndexStorageController = 0;DevIndexStorageController < SystemInventoryInfoProtocol->DevInfoCount;DevIndexStorageController++) {
        if (SystemInventoryInfoProtocol->DevInfoList[DevIndexStorageController]->Dp.DeviceStatus.DeviceType == SysInvDevStorageController && 
             SystemInventoryInfoProtocol->DevInfoList[DevIndexStorageController]->DisplayPtr.StorageController.StorageIndex ==  
                     DevEntry->Dp.DeviceStatus.DeviceInstance ) {
            //DEBUG((DEBUG_ERROR,"%a DevIndexStorageController  %d \n", 
            //__FUNCTION__, SystemInventoryInfoProtocol->DevInfoList[DevIndexStorageController]->Dp.DeviceStatus.DeviceInstance));
            StorageController = &SystemInventoryInfoProtocol->DevInfoList[DevIndexStorageController]->DisplayPtr.StorageController;
            if (StorageController->Status.ValidFlags.StatusVF1Param.StateValid && StorageController->Status.State != SysInvDevEnabled) {
                 Status.State = StorageController->Status.State;
             }
             
             if (StorageController->Status.ValidFlags.StatusVF1Param.HealthValid == 1 &&  
                 StorageController->Status.Health != SysInvHealthOK) {
                 Status.Health = StorageController->Status.Health;
             }
             // Check for Device status
             for (DevIndexStorageDrive = 0;DevIndexStorageDrive < SystemInventoryInfoProtocol->DevInfoCount;DevIndexStorageDrive++) {
                 if (SystemInventoryInfoProtocol->DevInfoList[DevIndexStorageDrive]->Dp.DeviceStatus.DeviceType == SysInvDevStorageDisk && 
                      SystemInventoryInfoProtocol->DevInfoList[DevIndexStorageDrive]->DisplayPtr.StorageDevice.ParentControllerIndex ==  
                      SystemInventoryInfoProtocol->DevInfoList[DevIndexStorageController]->Dp.DeviceStatus.DeviceInstance ) {
                     //DEBUG((DEBUG_ERROR,"%a DevIndexStorageDrive  %d \n", 
                     //__FUNCTION__, SystemInventoryInfoProtocol->DevInfoList[DevIndexStorageDrive]->Dp.DeviceStatus.DeviceInstance));  
                     
                     StorageDevice = &SystemInventoryInfoProtocol->DevInfoList[DevIndexStorageDrive]->DisplayPtr.StorageDevice;
                     if (StorageDevice->Status.ValidFlags.StatusVF1Param.StateValid == 1 &&  
                         StorageDevice->Status.State != SysInvDevEnabled) {
                          Status.State = StorageDevice->Status.State;
                      }
                      
                      if (StorageDevice->Status.ValidFlags.StatusVF1Param.HealthValid == 1 &&  
                          StorageDevice->Status.Health != SysInvHealthOK) {
                          Status.Health = StorageDevice->Status.Health;
                      }
                      
                      // Check for Volume status
                      for (DevIndexStorageVol = 0;DevIndexStorageVol < SystemInventoryInfoProtocol->DevInfoCount;DevIndexStorageVol++) {
                          if (SystemInventoryInfoProtocol->DevInfoList[DevIndexStorageVol]->Dp.DeviceStatus.DeviceType == SysInvDevStorageVolume && 
                               SystemInventoryInfoProtocol->DevInfoList[DevIndexStorageVol]->DisplayPtr.StorageVolume.ParentDeviceIndex ==  
                                       SystemInventoryInfoProtocol->DevInfoList[DevIndexStorageDrive]->Dp.DeviceStatus.DeviceInstance ) {
                              //DEBUG((DEBUG_ERROR,"%a DevIndexStorageVol  %d \n", 
                              //__FUNCTION__, SystemInventoryInfoProtocol->DevInfoList[DevIndexStorageVol]->Dp.DeviceStatus.DeviceInstance));
                              StorageVolume = &SystemInventoryInfoProtocol->DevInfoList[DevIndexStorageVol]->DisplayPtr.StorageVolume;
                            if (StorageVolume->Status.ValidFlags.StatusVF1Param.StateValid == 1 &&  
                                StorageVolume->Status.State != SysInvDevEnabled) {
                                   Status.State = StorageVolume->Status.State;
                               }
                               
                               if (StorageVolume->Status.ValidFlags.StatusVF1Param.HealthValid == 1 &&  
                                   StorageVolume->Status.Health != SysInvHealthOK) {
                                   Status.Health = StorageVolume->Status.Health;
                               }
                          }
             
                      } // End of Volume loop
                 }
               
             } //End of Device loop
        }
    }//End of Controller loop
    
    
    // Updating the Storage status
    CopyMem(&DevEntry->DisplayPtr.Storage.Status,&Status,sizeof(STATUS));
    
    
}
/**

  Updates StorageVolume string pointer from string Data base

  @param  SystemInventoryInfoProtocol - Pointer to System Info Protocol
  @param  DevEntry                    - Pointer to a CPU Device entry
  
  @retval Status  

**/
EFI_STATUS
StorageStrPtrUpdate(
  IN OUT SYSTEM_INVENTORY_INFO_PROTOCOL      *SystemInventoryInfoProtocol,
  IN OUT DEV_ENTRY                           *DevEntry
  )
{
    EFI_STATUS                      Status;
    STORAGE                         *Storage;
    CHAR8                           *InfoStringBuffer;
    UINT8                           Index;
    
    Storage =  &DevEntry->DisplayPtr.Storage;
    
    Status = SysInvGetStringsDBPtr (
                        SystemInventoryInfoProtocol,
                        &Storage->StringHdr,
                        &InfoStringBuffer );

    if (EFI_ERROR(Status) || InfoStringBuffer == NULL)
        return Status;

    if (Storage->ValidFlag1.StorageVF1Param.IdValid)
        Storage->Id = SysInvGetStringPtrByIndex (InfoStringBuffer,Storage->IdStrIndex);
    
    if (Storage->ValidFlag1.StorageVF1Param.NameValid)
        Storage->Name = SysInvGetStringPtrByIndex (InfoStringBuffer,Storage->NameStrIndex);
    
    if (Storage->ValidFlag1.StorageVF1Param.DescriptionValid)
        Storage->Description = SysInvGetStringPtrByIndex (InfoStringBuffer,Storage->DescriptionStrIndex);
    
    //REDUNDANCY String Pointer update : Start
        for (Index = 0; Index < Storage->RedundancyValidArrayElementsCount ;Index++) {
            if (Storage->Redundancy[Index].ValidFlags1.RedundancyVF1Param.MemberIdValid)
                Storage->Redundancy[Index].MemberId = SysInvGetStringPtrByIndex (InfoStringBuffer,Storage->Redundancy[Index].MemberIdStrIndex);
            if (Storage->Redundancy[Index].ValidFlags1.RedundancyVF1Param.NameValid)
                Storage->Redundancy[Index].Name = SysInvGetStringPtrByIndex (InfoStringBuffer,Storage->Redundancy[Index].NameStrIndex);
        }
    //REDUNDANCY String Pointer update : End
        
        if (Storage->ValidFlag1.StorageVF1Param.ConsistencyGroupsValid)
            Storage->ConsistencyGroups.Id = SysInvGetStringPtrByIndex (InfoStringBuffer,Storage->ConsistencyGroups.IdStrIndex);
        
        if (Storage->ValidFlag1.StorageVF1Param.EndpointGroupsValid)
            Storage->EndpointGroups.Id = SysInvGetStringPtrByIndex (InfoStringBuffer,Storage->EndpointGroups.IdStrIndex);
        
        if (Storage->ValidFlag1.StorageVF1Param.FileSystemsValid)
            Storage->FileSystems.Id = SysInvGetStringPtrByIndex (InfoStringBuffer,Storage->FileSystems.IdStrIndex);
        
        if (Storage->ValidFlag1.StorageVF1Param.StorageGroupsValid)
            Storage->StorageGroups.Id = SysInvGetStringPtrByIndex (InfoStringBuffer,Storage->StorageGroups.IdStrIndex);
        
        if (Storage->ValidFlag1.StorageVF1Param.StoragePoolsValid)
            Storage->StoragePools.Id = SysInvGetStringPtrByIndex (InfoStringBuffer,Storage->StoragePools.IdStrIndex);
   
    return Status;
}

   
